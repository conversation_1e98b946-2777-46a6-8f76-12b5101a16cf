package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.inv.controller.InvCtrl;
import cn.sphd.miners.modules.inv.entity.*;
import cn.sphd.miners.modules.inv.service.InvService;
import cn.sphd.miners.modules.material.dao.MtSupplierMaterialDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierHistoryDao;
import cn.sphd.miners.modules.material.entity.*;

import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.sales.dao.PoDeliveryAddressDao;
import cn.sphd.miners.modules.sales.model.PoDeliveryAddress;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.InitAllot;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgInitService;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/mt")
public class MtCtrl {

    @Autowired
    MtService mtService;

    @Autowired
    InvService invService;

    @Autowired
    UserSuspendMsgService suspendMsgService;

    @Autowired
    UserPopedomService userPopedomService;

    @Autowired
    OrgService orgService;

    @Autowired
    UnitService unitService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Autowired
    MtStockService stockService;

    @Autowired
    MtSupplierMaterialDao supplierMaterialDao;

    @Autowired
    PoDeliveryAddressDao deliveryAddressDao;

    @Autowired
    SrmSupplierDao supplierDao;

    @Autowired
    SrmSupplierHistoryDao srmSupplierHistoryDao;

    @Autowired
    OrgInitService orgInitService;

    @RequestMapping("/in.do")
    public String toMaterialEntry() {
        return "/purchase/materialEntry";
    }

    @RequestMapping("/manage.do")
    public String toMaterialManage() {
        return "/purchase/materialManage";
    }

    @RequestMapping("/mtStore.do")
    public String mtStore() {
        return "/storage/rawMt";
    }

    @RequestMapping("/rawMt")
    public String rawMt() {
        return "/storageSearch/rawMt";
    }


    @RequestMapping("/ovRawMt")
    public String ovRawMt() {
        return "/companyOverview/rawMt";
    }
    //盘点管理
    @RequestMapping("/inventoryMent")
    public String inventoryMent() {
        return "/rawMt/inventoryMent";
    }
    //手动入库
    @RequestMapping("/manualWareh")
    public String manualWareh() {
        return "/rawMt/manualWareh";
    }
    //领料
    @RequestMapping("/manualResition")
    public String manualResition() {
        return "/rawMt/manualResition";
    }
    //出入库记录
    @RequestMapping("/entryExitRecords")
    public String entryExitRecords() {
        return "/rawMt/entryExitRecords";
    }
    //1.259初始化
    @RequestMapping("/init/collect")
    @ResponseBody
    public Map collect(User user, Integer oid) {

        if (oid == null)
            oid = user.getOid();
        List<InitAllot> initAllotList = orgInitService.getInitAllotListByCode(user, "purchase", oid != null ? oid : user.getOid());
        Date mtSupplierCompletDate = new Date();//材料供应商清单完成时间
        Date epSupplierCompletDate = new Date();//设备供应商清单完成时间
        Date mtCompletDate = new Date();//其他原辅材料完成时间
        Date locationDate = new Date();//确定定点信息时间

        //获取材料供应商清单完成时间
        for (InitAllot initAllot : initAllotList) {
            if (initAllot.getStage() == 1 && initAllot.getState() == 1)
                mtSupplierCompletDate = initAllot.getUpdateDate();
            if (initAllot.getStage() == 2 && initAllot.getState() == 1)
                epSupplierCompletDate = initAllot.getUpdateDate();
            if (initAllot.getStage() == 3 && initAllot.getState() == 1)
                mtCompletDate = initAllot.getUpdateDate();
            if (initAllot.getStage() == 4 && initAllot.getState() == 1)
                locationDate = initAllot.getUpdateDate();

        }

        Map result = new HashMap();
        //获取定点信息的最后编辑时间
        Map lastEditData = supplierMaterialDao.getLastPurchasedData(oid == null ? user.getOid() : oid);

        result.put("lastEditData", lastEditData.get("data"));

        //获取待确认定点信息的材料
        Map mtList1 = mtService.getList("1", oid, null, null, null, 2, null, null, null,null);
        result.put("mtList1", mtList1.get("data"));

        //获取已确认定点信息的材料
        Map mtList4 = mtService.getList("4", oid, null, null, null, 2, null, null, null,null);

        result.put("mtList4", mtList4.get("data"));
        //获取在完成时间后又编辑了的定点数据
        Set<Integer> mts = new HashSet();//已完成的不重复的材料id

        Set<Integer> supplierIds = new HashSet();//已完成的不重复的供应商id


        List<MtSupplierMaterial> supplierMaterials = supplierMaterialDao.getListByHQL(" from MtSupplierMaterial o where o.material.org=?0 and o.createDate > ?1", user.getOid(), locationDate);
        if (supplierMaterials != null && !supplierMaterials.isEmpty()) {
            for (MtSupplierMaterial supplierMaterial : supplierMaterials) {
                mts.add(supplierMaterial.getMaterial_());
                supplierIds.add(supplierMaterial.getSupplier());
            }
        }


        //获取新增完成之后新增了多少材料 在完成时间后编辑了应当减去
        Map mtBases = mtService.getMtListByDate(locationDate, user);
        List newMtBases = (List) mtBases.get("data");

        List removeMtBases = new ArrayList();
        if (mtBases != null) {
            List<HashMap> mapList = (List<HashMap>) mtBases.get("data");
            if (mapList != null && !mts.isEmpty()) {
                for (Integer mt : mts) {
                    for (HashMap m : mapList) {
                        Integer id = (Integer) m.get("id");
                        if (Objects.equals(id, mt)) {
                            removeMtBases.add(m);
                        }
                    }
                }

            }

        }
//        if (newMtBases != null)
//            newMtBases.removeAll(removeMtBases);

        result.put("newMtBases", newMtBases);
        //获取基本信息发生变动的材料
        Map changeMtBases = mtService.getMtListByCondition(locationDate, "3", oid == null ? user.getOid() : oid);
        result.put("changeMtBases", changeMtBases.get("data"));
        if (changeMtBases != null) {
            List<HashMap> mapList = (List<HashMap>) changeMtBases.get("data");
            if (mapList != null) {
                for (HashMap m : mapList) {
                    Integer id = (Integer) m.get("id");
                    List<MtBaseHistory> mtBaseHistories = mtService.getMtBaseHistoryById(id, "3");
                    if (mtBaseHistories != null && mtBaseHistories.size() > 0) {
                        m.put("history", mtBaseHistories.get(mtBaseHistories.size() - 1));
                    }
                }
            }

        }
        result.put("changeMtBasesHistory", changeMtBases.get("data"));

        //获取在完成编辑后又新增的供应商
        List<SrmSupplier> removeSuppliers = new ArrayList();

        List<SrmSupplier> suppliers = supplierDao.getSuppliersByDate(locationDate, oid == null ? user.getOid() : oid);
        if (suppliers != null) {
            for (SrmSupplier supplier : suppliers) {
                for (Integer mt : supplierIds) {
                    Integer id = supplier.getId();
                    if (Objects.equals(id, mt)) {

                        SrmSupplier s = new SrmSupplier();
                        BeanUtils.copyProperties(supplier, s);

                        removeSuppliers.add(s);
                    }
                }
            }


        }

//        if(suppliers!=null)
//            suppliers.removeAll(removeSuppliers);
        result.put("suppliers", suppliers);

        //获取信息发生变动的供应商

        List<SrmSupplier> changeSuppliers = supplierDao.getChangeSuppliers(locationDate, oid == null ? user.getOid() : oid);
        if (changeSuppliers != null) {
            if (changeSuppliers != null) {
                for (SrmSupplier changeSupplier : changeSuppliers) {
                    Integer id = changeSupplier.getId();
                    List<SrmSupplierHistory> srmSupplierHistories = srmSupplierHistoryDao.getListByHQL(" from SrmSupplierHistory o where o.supplier=?0", id);
                    if (srmSupplierHistories != null && !srmSupplierHistories.isEmpty()) {
                        changeSupplier.setSupplierHistory(srmSupplierHistories);
                    }
                }
            }
        }
        result.put("changeSuppliers", changeSuppliers);
        return result;
    }

    @RequestMapping("/init/mtBaseHandle")
    @ResponseBody
    public String changeInitial(Integer id, String initial) {
        MtBase mtBase = mtService.getMtById(id);
        if (mtBase != null) {
            mtBase.setIsInitialized("1");
            mtService.updateMaBase(mtBase);
        }
        return "1";
    }

    @RequestMapping("/init/supplierHandle")
    @ResponseBody
    public String supplierHandle(Integer id, String initial) {
        SrmSupplier supplier = supplierDao.get(id);
        if (supplier != null) {
            supplier.setOperation("D");
            supplierDao.update(supplier);
        }
        return "1";
    }


    @RequestMapping("/init/warehouse/collect")
    @ResponseBody
    public Map wCollect(User user, Integer oid, String operation) {
        Map m = new HashMap();
        Date lastEditDate = new Date();
        //获取最新的初始库存编辑时间
        List<MtStockInfo> stockInfos = stockService.getMtStockInfoByOrg(user.getOid());
        if (stockInfos != null&& !stockInfos.isEmpty()) {
            m.put("lastEditDate", stockInfos.get(0).getUpdateDate());
            m.put("lastEditName", stockInfos.get(0).getUpdateName());
        }

        //获取完成之后新增的仓库
        String category = "原辅材料库";

        InvWarehouseCategory warehouseCategory = invService.getWarehouseCategoryByName(category);
        if (warehouseCategory == null) {
            new InvCtrl().init(null, user);
        }
        warehouseCategory = invService.getWarehouseCategoryByName(category);

        Map baseList = invService.getBaseList(user.getOid(), warehouseCategory.getId());
        if (baseList != null) {
            ArrayList<HashMap> list = (ArrayList) baseList.get("data");
            if (list != null && !list.isEmpty())
                lastEditDate = (Date) list.get(0).get("update_date");

        }

        //获取有改动的时间
        if (oid == null)
            oid = user.getOid();

        Date initStockDate = new Date();
        Date warehouseDate = new Date();
        List<InitAllot> initAllotList = orgInitService.getInitAllotListByCode(user, "material", oid != null ? oid : user.getOid());

        //获取初始库存的完成时间
        for (InitAllot initAllot : initAllotList) {
            if (initAllot.getStage() == 1 && initAllot.getState() == 1)
                warehouseDate = initAllot.getUpdateDate();
            if (initAllot.getStage() == 2 && initAllot.getState() == 1)
                initStockDate = initAllot.getUpdateDate();
        }
        //获取完成之后初始库存有待编辑的材料
        Map map = mtService.getList("3", oid == null ? user.getOid() : oid, null, null, null, null, null, null, null,null);

        //获取已确定初始库存的材料
        Map emap = mtService.getList("5", oid == null ? user.getOid() : oid, null, null, null, null, null, null, null,null);
        m.put("initData", map.get("data"));
        m.put("initedData", emap.get("data"));


        List<InvWarehouseBase> warehouseBases = invService.getWarehouseBasesByCategory(warehouseCategory.getId(), Long.valueOf(user.getOid()), lastEditDate);
        m.put("initWarehouse", warehouseBases);

        List<InvWarehouseBase> changedWarehouseBases = invService.getWarehouseBasesByCategoryAndUpdateDate(warehouseCategory.getId(), Long.valueOf(user.getOid()), lastEditDate);

        if (changedWarehouseBases != null)
            for (InvWarehouseBase changedWarehouseBase : changedWarehouseBases) {


                if (changedWarehouseBase.getPreviousId() != null) {
                    //查询原来放置的材料
                    Map mtBases = mtService.getMtListByWarehouse(user.getOid(), changedWarehouseBase.getPreviousId());

                    changedWarehouseBase.setMtBases(mtBases);

                    InvWarehouseBase pre = invService.getInvWarehouseBaseById(changedWarehouseBase.getPreviousId());
                    if (pre != null) {
                        changedWarehouseBase.setPre(pre);
                    }
                }
            }
        m.put("changedWarehouse", changedWarehouseBases);
        return m;
    }


    @RequestMapping("operation")
    @ResponseBody
    public Map mtOperation(MtBase base, String operation, User user, Integer userId, @RequestParam(defaultValue = "0") Integer init) {


        //暂停供应商
        //1.259初始化时传递init后跳过审批步骤
        //if ("4".equals(operation)&&init!=1) {
        if ("4".equals(operation) && init == 0) {
            Map map = mtService.addSuspendSupplier(base, operation, userId == null ? user.getUserID() : userId, base.getEnabled());

            return map;
        }

        Map map = mtService.operation(base, operation, userId == null ? user.getUserID() : userId);
        if ("1".equals(operation) || "3".equals(operation))
            unitService.selectUnit(base.getUnitId(), 7);

        return map;
    }


    @RequestMapping("list")
    @ResponseBody
    public Map list(String state, User user, Integer oid, String keyword, Integer category, Integer supplier, Integer menu, Integer type, Integer pageNum, Integer per) {
        Map map = mtService.getList(state, oid == null ? user.getOid() : oid, keyword, category, supplier, menu, type, pageNum, per,null);

        //加入已确定定点信息条数
        Map yem = mtService.getList("4", oid == null ? user.getOid() : oid, keyword, category, supplier, menu, type, pageNum, per,null);

        Map inited = mtService.getList("5", oid == null ? user.getOid() : oid, keyword, category, supplier, menu, type, pageNum, per,null);

        map.put("yCount", yem.get("totalSize"));
        map.put("inited", inited.get("totalSize"));

        ArrayList<HashMap> arrayList = (ArrayList) map.get("data");

        if (arrayList != null && arrayList.size() > 0)
            for (HashMap m : arrayList) {
                Integer id = (Integer) m.get("supplier_material_id");

                if (id != null) {
                    MtSupplierMaterial supplierMaterial = supplierMaterialDao.getByHQL(" from MtSupplierMaterial o where o.id=?0", id);

                    if (supplierMaterial != null) {
                        if (StringUtils.isNotBlank(supplierMaterial.getDeliveryAddress())) {
                            List<PoDeliveryAddress> addresses = new ArrayList<>();

                            if (supplierMaterial.getDeliveryAddress().contains(",")) {
                                for (String deliveryId : supplierMaterial.getDeliveryAddress().split(",")) {
                                    PoDeliveryAddress deliveryAddress = deliveryAddressDao.get(Long.valueOf(deliveryId));

                                    if (deliveryAddress != null) {
                                        addresses.add(deliveryAddress);
                                    }
                                }

                                m.put("address", addresses);
                            }
                        }
                    }
                }
            }

        return map;
    }

    @RequestMapping("suppliers")
    @ResponseBody
    public Map suppliers(Integer id, String enabled) {
        Map suppliers = mtService.getSuppliersByMbId(id, enabled);
        return suppliers;
    }

    @RequestMapping("supplierState")
    @ResponseBody
    public String supplierState(Integer supplierId, Integer mtId) {
        return mtService.getSupplierState(supplierId, mtId);
    }

    @RequestMapping("mtLocation")
    @ResponseBody
    public Map mtLocation(Integer id, Integer supplierMaterialId) {
        return mtService.getmtLocation(id, supplierMaterialId);
    }

    @RequestMapping("location")
    @ResponseBody
    public void locationDetails(Integer id, Integer supplier, Integer relationId, HttpServletResponse response) throws IOException {
        Map location = mtService.getLocationDetails(id, supplier, relationId);

        ObjectToJson.objectToJson1(location, new String[]{"mtSupplierContactHashSet", "mtSupplierMaterialHashSet", "material"}, response);
    }

    @RequestMapping("stock")
    @ResponseBody
    public Map stock(Integer id) {
        Map stocks = mtService.getStocksByMtId(id);
        return stocks;
    }

    @RequestMapping("index")
    @ResponseBody
    public Map index(User user,Long org) {
        return mtService.getIndex(org==null?user.getOid().longValue():org);
    }

    /**
     * 录入初始库存数量、库位
     *
     * @param supplierMaterial 关联id
     * @param mtID             物料id
     */
    @RequestMapping("stockAndLocation")
    @ResponseBody
    public Map location(Integer supplierMaterial, Integer mtID, String params, BigDecimal initialStock, Integer operation, Integer re, User user) {
        return mtService.location(supplierMaterial, mtID, params, initialStock, operation, re, user);
    }

    @RequestMapping("history")
    @ResponseBody
    public Map stockHistory(Integer id) {
        return mtService.getHistory(id);
    }

    @RequestMapping("locations")
    @ResponseBody
    public Map locations(Integer org, Integer id) {
        Map map = invService.getLocations(org, id);

        return map;
    }

    /**
     * 获取供应商暂停申请列表
     */
    @RequestMapping("/suspendMaterielApplyList.do")
    @MessageMapping("/suspendMaterielApplyList")
    @ResponseBody
    public JsonResult suspendMaterielApplyList(User user) {


        Integer userId = user.getUserID();

        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByFromUser(userId, "1", 34, null, null, "desc");

        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/suspendMaterielApplyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }


    /**
     * 获取供应商暂停待审批列表
     */
    @RequestMapping("/suspendMaterielApprovalList.do")
    @MessageMapping("/suspendMaterielApprovalList")
    @ResponseBody
    public JsonResult suspendMaterielApprovalList(User user) {


        Integer userId = user.getUserID(); //机构id
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "1", 34, null, null, null, "desc");

        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/suspendMaterielApprovalList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }

    /**
     * 获取供应商暂停已审批列表
     */
    @RequestMapping("/suspendMaterielApprovalAlreadyList.do")
    @MessageMapping("/suspendMaterielApprovalAlreadyList")
    @ResponseBody
    public JsonResult suspendMaterielApprovalAlreadyList(User user) {


        Integer userId = user.getUserID(); //机构id
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "2", 34, null, null, null, "desc");
        List<ApprovalProcess> l = new ArrayList<>();
        for (ApprovalProcess ap : list) {
            List<ApprovalProcess> nums = approvalProcessService.getApprovalProcessByBusinessDesc(ap.getBusiness(), 34, "1");
            for (ApprovalProcess ap2 : nums) {
                if (ap.getApproveMemo().equals(ap2.getApproveMemo())) {
                    l.add(ap);
                    break;
                }
            }
        }
        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/suspendMaterielApprovalAlreadyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, l);
    }

    /**
     * 获取恢复供应商申请列表
     */
    @RequestMapping("/recoveryMaterielApplyList.do")
    @MessageMapping("/recoveryMaterielApplyList")
    @ResponseBody
    public JsonResult recoveryMaterielApplyList(User user) {


        Integer userId = user.getUserID();

        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByFromUser(userId, "1", 62, null, null, "desc");

        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/recoveryMaterielApplyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }

    /**
     * 获取供应商恢复待审批列表
     */
    @RequestMapping("/recoveryMaterielApprovalList.do")
    @MessageMapping("/recoveryMaterielApprovalList")
    @ResponseBody
    public JsonResult recoveryMaterielApprovalList(User user) {


        Integer userId = user.getUserID(); //机构id
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "1", 62, null, null, null, "desc");

        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/recoveryMaterielApprovalList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }

    /**
     * 获取供应商恢复已审批列表
     */
    @RequestMapping("/recoveryMaterielApprovalAlreadyList.do")
    @MessageMapping("/recoveryMaterielApprovalAlreadyList")
    @ResponseBody
    public JsonResult recoveryMaterielApprovalAlreadyList(User user) {


        Integer userId = user.getUserID(); //机构id
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "2", 62, null, null, null, "desc");
        List<ApprovalProcess> l = new ArrayList<>();
        for (ApprovalProcess ap : list) {
            List<ApprovalProcess> nums = approvalProcessService.getApprovalProcessByBusinessDesc(ap.getBusiness(), 62, "1");
            for (ApprovalProcess ap2 : nums) {
                if (ap.getApproveMemo().equals(ap2.getApproveMemo())) {
                    l.add(ap);
                    break;
                }
            }

        }
        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/recoveryMaterielApprovalAlreadyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, l);
    }


    /**
     * 供应商审批查看
     */
    @RequestMapping("/suspendSupplierDetails.do")
    @ResponseBody
    public Map<String, Object> suspendSupplierDetails(Integer approvalId) {

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(), approvalProcess.getBusinessType(), null);
        List<Map<String, Object>> l = new ArrayList<>();
        for (ApprovalProcess a : list) {
            if (approvalProcess.getApproveMemo().equals(a.getApproveMemo())) {
                Map<String, Object> m = new HashMap<>();
                m.put("userName", a.getToUserName());
                m.put("userId", a.getToUser());
                m.put("createDate", a.getCreateDate());
                m.put("handleTime", a.getHandleTime());
                m.put("approveStatus", a.getApproveStatus());
                l.add(m);
            }
        }

        Map data = mtService.getLocationHisDetails(approvalProcess.getBusiness());
        data.put("approval", approvalProcess);
        data.put("auditList", l);
        data.put("code", 200);
        return data;
    }


    /**
     * 获取暂停材料申请列表
     */
    @RequestMapping("/suspendPurchaseMaterielApplyList.do")
    @MessageMapping("/suspendPurchaseMaterielApplyList")
    @ResponseBody
    public JsonResult suspendPurchaseMaterielApplyList(User user) {
        Integer userId = user.getUserID(); //用户id

        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByFromUser(userId, "1", 35, null, null, "desc");
        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/suspendPurchaseMaterielApplyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }


    /**
     * 获取暂停材料待审批列表
     */
    @RequestMapping("/suspendPurchaseMaterielApprovalList.do")
    @MessageMapping("/suspendPurchaseMaterielApprovalList")
    @ResponseBody
    public JsonResult suspendPurchaseMaterielApprovalList(User user) {

        Integer userId = user.getUserID();
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "1", 35, null, null, null, "desc");

        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/suspendPurchaseMaterielApprovalList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }

    /**
     * 获取暂停材料已审批列表
     */
    @RequestMapping("/suspendPurchaseMaterielApprovalAlreadyList.do")
    @MessageMapping("/suspendPurchaseMaterielApprovalAlreadyList")
    @ResponseBody
    public JsonResult suspendPurchaseMaterielApprovalAlreadyList(User user) {

        Integer userId = user.getUserID();
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "2", 35, null, null, null, "desc");

        List<ApprovalProcess> l = new ArrayList<>();
        for (ApprovalProcess ap : list) {
            List<ApprovalProcess> nums = approvalProcessService.getApprovalProcessByBusinessDesc(ap.getBusiness(), 35, "1");
            for (ApprovalProcess ap2 : nums) {
                if (ap.getApproveMemo().equals(ap2.getApproveMemo())) {
                    l.add(ap);
                    break;
                }
            }
        }

        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/suspendPurchaseMaterielApprovalAlreadyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, l);
    }

    /**
     * 材料停用审批查看
     */
    @RequestMapping("/suspendMaterielDetails.do")
    @ResponseBody
    public Map<String, Object> suspendMaterielDetails(Integer approvalId) {

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(), approvalProcess.getBusinessType(), null);
        List<Map<String, Object>> l = new ArrayList<>();
        for (ApprovalProcess a : list) {
            if (approvalProcess.getApproveMemo().equals(a.getApproveMemo())) {
                Map<String, Object> m = new HashMap<>();
                m.put("userName", a.getToUserName());
                m.put("userId", a.getToUser());
                m.put("createDate", a.getCreateDate());
                m.put("handleTime", a.getHandleTime());
                m.put("approveStatus", a.getApproveStatus());
                l.add(m);
            }
        }


        Map data = mtService.getMaterielDetails(approvalProcess.getBusiness());

        data.put("approval", approvalProcess);
        data.put("auditList", l);
        data.put("code", 200);
        return data;
    }

    /**
     * 暂停供应商审批
     */
    @RequestMapping("/suspendSupplierApproval.do")
    @MessageMapping("/suspendSupplierApproval")
    @ResponseBody
    public JsonResult suspendSupplierApproval(String json, User user) {
        JSONObject jsonObject = JSON.parseObject(json);
        Integer approvalId = jsonObject.getInteger("approvalId"); // 审批id
        String approveStatus = jsonObject.getString("approveStatus"); //审批结果
        String reason = jsonObject.getString("reason"); //审批原因
        Integer userId = user.getUserID();
        Map<String, Object> map = new HashMap<>();

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);

        if (approvalProcess == null) {
            map.put("status", 0);
            map.put("msg", "操作失败");
            return new JsonResult(1, map);
        }
        if (!"1".equals(approvalProcess.getApproveStatus())) {
            map.put("status", 0);
            map.put("msg", "操作失败");
            return new JsonResult(1, map);
        }
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setReason(reason);
        approvalProcess.setHandleTime(new Date());

        String res = "";
        try {
            res = mtService.suspendSupplierApproval(approvalProcess);
        } catch (RuntimeException e) {
            e.getStackTrace();
            res = e.getMessage();
        }

        if ("success".equals(res)) {
            map.put("status", 1);
            map.put("msg", "操作成功");
        } else {

            map.put("status", 0);
            map.put("msg", "操作失败");
        }

        System.out.println("执行完毕");
        return new JsonResult(1, map);
    }

    /**
     * 暂停材料审批
     */
    @RequestMapping("/suspendMaterielApproval.do")
    @MessageMapping("/suspendMaterielApproval")
    @ResponseBody
    public JsonResult suspendMaterielApproval(String json, User user) {
        JSONObject jsonObject = JSON.parseObject(json);
        Integer approvalId = jsonObject.getInteger("approvalId"); // 审批id
        String approveStatus = jsonObject.getString("approveStatus"); //审批结果
        String reason = jsonObject.getString("reason"); //审批原因
        Integer userId = user.getUserID();
        Map<String, Object> map = new HashMap<>();

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);

        if (approvalProcess == null) {
            map.put("status", 0);
            map.put("msg", "操作失败");
        }
        if (!"1".equals(approvalProcess.getApproveStatus())) {
            map.put("status", 0);
            map.put("msg", "操作失败");
            return new JsonResult(1, map);
        }
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setReason(reason);
        approvalProcess.setHandleTime(new Date());

        String res = "";
        try {
            res = mtService.suspendMaterielApproval(approvalProcess);
        } catch (RuntimeException e) {
            res = e.getMessage();
        }

        if ("success".equals(res)) {
            map.put("status", 1);
            map.put("msg", "操作成功");
        } else {

            map.put("status", 0);
            map.put("msg", "操作失败");
        }

        System.out.println("执行完毕");
        return new JsonResult(1, map);
    }

    /**
     * 恢复供应商审批
     */
    @RequestMapping("/recoveryMaterielApproval.do")
    @MessageMapping("/recoveryMaterielApproval")
    @ResponseBody
    public JsonResult recoveryMaterielApproval(String json, User user) {
        JSONObject jsonObject = JSON.parseObject(json);
        Integer approvalId = jsonObject.getInteger("approvalId"); // 审批id
        String approveStatus = jsonObject.getString("approveStatus"); //审批结果
        String reason = jsonObject.getString("reason"); //审批原因

        Map<String, Object> map = new HashMap<>();

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);

        if (approvalProcess == null) {
            map.put("status", 0);
            map.put("msg", "操作失败");
            return new JsonResult(1, map);
        }
        if (!"1".equals(approvalProcess.getApproveStatus())) {
            map.put("status", 0);
            map.put("msg", "操作失败");
            return new JsonResult(1, map);
        }
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setReason(reason);
        approvalProcess.setHandleTime(new Date());

        String res = mtService.recoveryMaterielApproval(approvalProcess);
//        try {
//            res = mtService.recoveryMaterielApproval(approvalProcess);
//        } catch (Exception e) {
//            e.printStackTrace();
//            res = e.getMessage();
//        }
        System.out.println(res);

        if ("success".equals(res)) {
            map.put("status", 1);
            map.put("msg", "操作成功");
        } else {

            map.put("status", 0);
            map.put("msg", "操作失败");
        }

        System.out.println("执行完毕");
        return new JsonResult(1, map);
    }


    /**
     * 暂停供应商审批查询
     */
    @RequestMapping("/selectSuspendSupplierList.do")
    @ResponseBody
    public Map<String, Object> selectSuspendSupplierList(HttpServletRequest request, User user) throws ParseException {


        Map<String, Object> map = new HashMap<>();

        Integer userId = user.getUserID();
        String type = request.getParameter("type");
        String approveStatus = request.getParameter("approveStatus");
        String beginTime = request.getParameter("beginTime");
        String endTime = request.getParameter("endTime");
        String userType = request.getParameter("userType");
        Date st = null;
        Date ed = null;
        SimpleDateFormat sim1 = new SimpleDateFormat("yyyy-MM-dd");

        SimpleDateFormat sim2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleDateFormat sim3 = new SimpleDateFormat("yyyy-MM");
        //7天内
        if ("1".equals(type)) {
            String nowDate = sim1.format(new Date());

            st = new Date((sim1.parse(nowDate).getTime() - 3600 * 24 * 7 * 1000));

            ed = new Date(sim2.parse(nowDate + " 23:59:59").getTime());
            //当月
        } else if ("2".equals(type)) {
            //获取当前月第一天：
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
            String first = sim1.format(c.getTime());
            System.out.println("===============first:" + first);

            //获取当前月最后一天
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            String last = sim1.format(ca.getTime());
            System.out.println("===============last:" + last);

            st = new Date(sim2.parse(first + " 00:00:00").getTime());

            ed = new Date(sim2.parse(last + " 23:59:59").getTime());
        } else {

            st = new Date(sim2.parse(beginTime + " 00:00:00").getTime());

            ed = new Date(sim2.parse(endTime + " 23:59:59").getTime());
        }

        //申请人
        if ("1".equals(userType)) {

            List<ApprovalProcess> list = mtService.getApprovalProcessByFromUserId(userId, approveStatus, 34, st, ed, "desc");
            map.put("list", list);
        } else {
            List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, approveStatus, 34, st, ed, null, "desc");
            map.put("list", list);
        }

        map.put("beginTime", st);
        map.put("endTime", ed);
        map.put("status", 1);
        return map;
    }


    /**
     * 暂停材料审批查询
     */
    @RequestMapping("/selectSuspendMaterielList.do")
    @ResponseBody
    public Map<String, Object> selectSuspendMaterielList(HttpServletRequest request, User user) throws ParseException {


        Map<String, Object> map = new HashMap<>();
        Integer userId = user.getUserID();
        String type = request.getParameter("type");
        String approveStatus = request.getParameter("approveStatus");
        String beginTime = request.getParameter("beginTime");
        String endTime = request.getParameter("endTime");
        String userType = request.getParameter("userType");
        Date st = null;
        Date ed = null;
        SimpleDateFormat sim1 = new SimpleDateFormat("yyyy-MM-dd");

        SimpleDateFormat sim2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleDateFormat sim3 = new SimpleDateFormat("yyyy-MM");
        //7天内
        if ("1".equals(type)) {
            String nowDate = sim1.format(new Date());

            st = new Date((sim1.parse(nowDate).getTime() - 3600 * 24 * 7 * 1000));

            ed = new Date(sim2.parse(nowDate + " 23:59:59").getTime());
            //当月
        } else if ("2".equals(type)) {
            //获取当前月第一天：
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
            String first = sim1.format(c.getTime());
            System.out.println("===============first:" + first);

            //获取当前月最后一天
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            String last = sim1.format(ca.getTime());
            System.out.println("===============last:" + last);

            st = new Date(sim2.parse(first + " 00:00:00").getTime());

            ed = new Date(sim2.parse(last + " 23:59:59").getTime());
        } else {

            st = new Date(sim2.parse(beginTime + " 00:00:00").getTime());

            ed = new Date(sim2.parse(endTime + " 23:59:59").getTime());
        }

        //申请人
        if ("1".equals(userType)) {

            List<ApprovalProcess> list = mtService.getApprovalProcessByFromUserId(userId, approveStatus, 35, st, ed, "desc");
            map.put("list", list);
        } else {
            List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, approveStatus, 35, st, ed, null, "desc");
            map.put("list", list);
        }

        map.put("beginTime", st);
        map.put("endTime", ed);
        map.put("status", 1);
        return map;
    }


    /**
     * 恢复供应商审批查询
     */
    @RequestMapping("/selectRecoveryMaterielList.do")
    @ResponseBody
    public Map<String, Object> selectRecoveryMaterielList(HttpServletRequest request, User user) throws ParseException {


        Map<String, Object> map = new HashMap<>();
        Integer userId = user.getUserID();
        String type = request.getParameter("type");
        String approveStatus = request.getParameter("approveStatus");
        String beginTime = request.getParameter("beginTime");
        String endTime = request.getParameter("endTime");
        String userType = request.getParameter("userType");
        Date st = null;
        Date ed = null;
        SimpleDateFormat sim1 = new SimpleDateFormat("yyyy-MM-dd");

        SimpleDateFormat sim2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleDateFormat sim3 = new SimpleDateFormat("yyyy-MM");
        //7天内
        if ("1".equals(type)) {
            String nowDate = sim1.format(new Date());

            st = new Date((sim1.parse(nowDate).getTime() - 3600 * 24 * 7 * 1000));

            ed = new Date(sim2.parse(nowDate + " 23:59:59").getTime());
            //当月
        } else if ("2".equals(type)) {
            //获取当前月第一天：
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
            String first = sim1.format(c.getTime());
            System.out.println("===============first:" + first);

            //获取当前月最后一天
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            String last = sim1.format(ca.getTime());
            System.out.println("===============last:" + last);

            st = new Date(sim2.parse(first + " 00:00:00").getTime());

            ed = new Date(sim2.parse(last + " 23:59:59").getTime());
        } else {

            st = new Date(sim2.parse(beginTime + " 00:00:00").getTime());

            ed = new Date(sim2.parse(endTime + " 23:59:59").getTime());
        }

        //申请人
        if ("1".equals(userType)) {
            List<ApprovalProcess> list = mtService.getApprovalProcessByFromUserId(userId, approveStatus, 62, st, ed, "desc");
            map.put("list", list);
        } else {
            List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, approveStatus, 62, st, ed, null, "desc");
            map.put("list", list);
        }

        map.put("beginTime", st);
        map.put("endTime", ed);
        map.put("status", 1);
        return map;
    }

    /**
     * 新增材料供应商审批查询
     */
    @RequestMapping("/selectAddMaterielList.do")
    @ResponseBody
    public Map<String, Object> selectAddMaterielList(HttpServletRequest request, User user) throws ParseException {


        Map<String, Object> map = new HashMap<>();
        Integer userId = user.getUserID();
        String type = request.getParameter("type");
        String approveStatus = request.getParameter("approveStatus");
        String beginTime = request.getParameter("beginTime");
        String endTime = request.getParameter("endTime");
        String userType = request.getParameter("userType");
        Date st = null;
        Date ed = null;
        SimpleDateFormat sim1 = new SimpleDateFormat("yyyy-MM-dd");

        SimpleDateFormat sim2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleDateFormat sim3 = new SimpleDateFormat("yyyy-MM");
        //7天内
        if ("1".equals(type)) {
            String nowDate = sim1.format(new Date());

            st = new Date((sim1.parse(nowDate).getTime() - 3600 * 24 * 7 * 1000));

            ed = new Date(sim2.parse(nowDate + " 23:59:59").getTime());
            //当月
        } else if ("2".equals(type)) {
            //获取当前月第一天：
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
            String first = sim1.format(c.getTime());
            System.out.println("===============first:" + first);

            //获取当前月最后一天
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            String last = sim1.format(ca.getTime());
            System.out.println("===============last:" + last);

            st = new Date(sim2.parse(first + " 00:00:00").getTime());

            ed = new Date(sim2.parse(last + " 23:59:59").getTime());
        } else {

            st = new Date(sim2.parse(beginTime + " 00:00:00").getTime());

            ed = new Date(sim2.parse(endTime + " 23:59:59").getTime());
        }

        //申请人
        if ("1".equals(userType)) {

            List<ApprovalProcess> list = mtService.getApprovalProcessByFromUserId(userId, approveStatus, 61, st, ed, "desc");
            map.put("list", list);
        } else {
            List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, approveStatus, 61, st, ed, null, "desc");
            map.put("list", list);
        }

        map.put("beginTime", st);
        map.put("endTime", ed);
        map.put("status", 1);
        return map;
    }

    @Scheduled(cron = "0 0 10 * * ?")//每天10点运行
    public void task() {
        //不区分机构
        //查找前一日0点-24点m1数量 发送消息 （曾采购过的材料）、

        List<Organization> organizations = orgService.checkDepartmentByPId(0, 1);

        for (Organization org : organizations) {

            Integer m1 = mtService.getNum(1, org.getId());

            //查找前一日0点-24点m2 数量 发送消息 (未采购过的材料)
            Integer m2 = mtService.getNum(2, org.getId());

            //查找前一日0点-24点m3 数量 发送消息 (来自产品或商品的材料)

            Integer m3 = mtService.getNum(3, org.getId());

            Integer m4 = mtService.getNum(4, org.getId());
            Integer m5 = mtService.getNum(5, org.getId());
            Integer m6 = mtService.getNum(6, org.getId());
            Integer m7 = mtService.getNum(7, org.getId());

            //获取到所有拥有原辅材料库权限的user

            List<UserDto> users = userPopedomService.getUserByOidMid(org.getId(), "xc");

            if (m1 > 0) {
                for (UserDto userDto : users) {
                    suspendMsgService.saveUserSuspendMsg(1, "系统中录入了" + m1 + "种材料，请及时在系统中确认其是否需要定点采购", "", userDto.getUserID(), "message", userDto.getUserID());
                }
            }

            if (m2 > 0) {
                for (UserDto userDto : users) {
                    suspendMsgService.saveUserSuspendMsg(1, "系统中增加了" + (m2) + "种材料。请及时在系统中录入其初始库存。", "", userDto.getUserID(), "message", userDto.getUserID());
                }
            }
            //TODO
            if ((m1 + m3 + m4 + m6) > 0)
                for (UserDto userDto : users) {
                    suspendMsgService.saveUserSuspendMsg(1, "系统中增加了" + (m1 + m3) + "种材料。请及时在系统中录入其初始库存。", "", userDto.getUserID(), "message", userDto.getUserID());
                }

            if (m2 + m5 + m7 > 0)
                for (UserDto userDto : users) {
                    suspendMsgService.saveUserSuspendMsg(1, "系统中增加了" + m2 + "种材料，特此告知。", "", userDto.getUserID(), "message", userDto.getUserID());
                }
        }

    }

    @RequestMapping("stockChangeRecord")
    @ResponseBody
    public Map stockChangeRecord(Integer id, String type, String startTime, String endTime, String year, Integer pageNum, Integer per) {
        return stockService.getStockChangeRecord(id, type, startTime, endTime, year, pageNum, per);
    }


}
