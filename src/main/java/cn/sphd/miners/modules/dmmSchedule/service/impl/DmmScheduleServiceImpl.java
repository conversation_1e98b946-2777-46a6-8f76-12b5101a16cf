package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.dmmSchedule.dao.*;
import cn.sphd.miners.modules.dmmSchedule.entity.*;
import cn.sphd.miners.modules.dmmSchedule.service.*;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.Schedule;
import cn.sphd.miners.modules.system.service.ScheduleService;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2019/1/24.
 */
@Service("dmmScheduleService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DmmScheduleServiceImpl implements DmmScheduleService {
    @Autowired
    DmmScheduleDao dmmScheduleDao;
    @Autowired
    DmmJobDao dmmJobDao;
    @Autowired
    DmmScheduleImageDao dmmScheduleImageDao;
    @Autowired
    DmmScheduleHistoryDao dmmScheduleHistoryDao;
    @Autowired
    DmmJobMsgDao dmmJobMsgDao;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    DmmJobService dmmJobService;
    @Autowired
    DmmScheduleHistoryService dmmScheduleHistoryService;
    @Autowired
    DmmJobMsgService dmmJobMsgService;
    @Autowired
    ScheduleService scheduleService;
    @Autowired
    UploadService uploadService;
    @Autowired
    DmmScheduleSupplementService dmmScheduleSupplementService;


    @Override
    public Integer saveDmmSchedule(DmmSchedule dmmSchedule, AuthAcc acc, String source, String[] imagePaths) {
        dmmSchedule.setCreateDate(new Date());
//        dmmSchedule.setCreator(user.getUserID());
        dmmSchedule.setAccId(acc.getId());
        dmmSchedule.setCreateName(acc.getName());
        dmmSchedule.setUpdateName(acc.getName());
        dmmSchedule.setSource(source);
        dmmSchedule.setUpdateDate(new Date());

        if (dmmSchedule.isNotify()) {//需要通知
            dmmSchedule.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        }
        dmmSchedule.setNextRunTime(dmmSchedule.getActiveStartDate());//下次提醒时间是日程开始提醒时间
        dmmScheduleDao.save(dmmSchedule);

        if (imagePaths!=null) {
            this.saveDmmImages(dmmSchedule, imagePaths,acc); //保存日程附件
        }

        if (dmmSchedule.getActiveStartDate()!=null) {//首次提醒时间不等于null
            Date beginDate = dmmSchedule.getActiveStartDate();
            if (dmmSchedule.isNotify() &&
                    beginDate.getTime() > new Date().getTime() &&
                    NewDateUtils.today(beginDate).getTime() == NewDateUtils.today(new Date()).getTime()) {
                //需要提醒 且开始提醒时间为 当天，此刻以后的时间
                DmmJob dmmJob = dmmJobService.addDmmJobByUserSchedule(dmmSchedule);
                //日程 到时间 提醒
//                if (1==DmmJob.getEnabled()) {
                    DmmScheduleRemind dmmScheduleRemind = new DmmScheduleRemind(dmmJob.getId());
                    clusterMessageSendingOperations.delayCall(dmmJob.getRunTime(),dmmScheduleRemind);
//                    DmmJob.setEnabled(0);//提醒已放到延时队列中 不需要再
//                }
            }
        }
        return dmmSchedule.getId();

    }
    private void saveDmmImages(DmmSchedule dmmSchedule, String[] imagePaths, AuthAcc acc){
        Integer orders=1;
        for (String imagePath:imagePaths){
            DmmScheduleImage dmmScheduleImage=new DmmScheduleImage();
            dmmScheduleImage.setCreateDate(dmmSchedule.getCreateDate());
            dmmScheduleImage.setCreator(dmmSchedule.getCreator());
            dmmScheduleImage.setCreateName(dmmSchedule.getCreateName());
            dmmScheduleImage.setNormal(imagePath);//正常图片路径
            dmmScheduleImage.setOrders(orders);
            dmmScheduleImage.setSchedule(dmmSchedule);
            dmmScheduleImageDao.save(dmmScheduleImage);
            dmmSchedule.getDmmScheduleImageHashSet().add(dmmScheduleImage);
            DmmUsing callback = new DmmUsing(dmmScheduleImage.getId(), dmmScheduleImage.getClass());
            String name=dmmSchedule.getTitle();
            if (imagePaths.length>1){ //多个文件
                name=name+orders;
            }
            uploadService.addFileUsing(callback, dmmScheduleImage.getNormal(), name, acc, "备忘与日程");
            orders++;
        }
    }

    @Override
    public void updateDmmSchedule(DmmSchedule dmmSchedule) {
        dmmSchedule.setUpdateDate(new Date());
        dmmScheduleDao.update(dmmSchedule);
    }

    @Override
    public DmmSchedule getDmmScheduleById(Integer id) {
        return dmmScheduleDao.get(id);
    }

    @Override
    public List<DmmSchedule> getDmmScheduleListByUserId(Long accId, PageInfo pageInfo, String keyWord) {
        String hql=" from DmmSchedule where isValid=true and notify=true and (freqType>1 or (freqType=1 and activeStartDate>:lingChenDate)) and accId=:accId";
        Map<String,Object> params=new HashMap<>();
        params.put("accId", accId);
//        params.put("dangQianDate",new Date());//当前时间  and activeStartDate<:dangQianDate)
        params.put("lingChenDate",NewDateUtils.today(new Date()));//本日开始时刻
        if (!StringUtil.isNullOrEmpty(keyWord)){
            params.put("keyword","%"+keyWord+"%");
            hql+=" and (title like:keyword or description like:keyword)";
        }
        List<DmmSchedule> dmmScheduleList=dmmScheduleDao.getListByHQLWithNamedParams(hql+" order by freqType asc,activeStartDate asc",params,pageInfo);
        return dmmScheduleList;
    }

    @Override
    public void addDmmScheduleRemind(Integer id) {
        DmmJob dmmJob=dmmJobDao.get(id);
        dmmJob.getSchedule().setModifiable(false);//设置成不可修改
        if (dmmJob.isValid() && dmmJob.getEnabled().equals(1)) {//要执行的提醒
            dmmJob.setState(2);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
//            DmmJobDao.update(DmmJob);
            dmmJob.getSchedule().setNoticeNumber(dmmJob.getSchedule().getNoticeNumber()+1);
            dmmJob.setNoticeNumber(dmmJob.getNoticeNumber()+1);//提醒次数加1
            dmmJob.setEnabled(0);//本次提醒 过了 不需要再提醒
//            DmmJob.setUpdateDate(new Date());
//            String hql=" update DmmJob set enabled=0 where id=:id";
//            HashMap<String,Object> params = new HashMap<>();
//            params.put("id",id);
//            DmmJobDao.queryHQLWithNamedParams(hql,params);//为防止机群情况直接在数据库更新 状态

            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String runDate=sdf.format(dmmJob.getRunTime());
            dmmJob.setRunTimePath(dmmJob.getRunTimePath()==null?runDate:dmmJob.getRunTimePath()+","+runDate);//提醒时间路径

//            if (DmmJob.getSchedule().getFreqType()==1){
                dmmJob.getSchedule().setState(2); //日程最新状态改为提醒中
//            }
//            DmmScheduleDao.update(DmmJob.getSchedule());
            String cont = "您在" + new SimpleDateFormat("yyyy年MM月dd日 HH:mm").format(dmmJob.getRunTime()) + "有“" + dmmJob.getTitle() + "”的日程!";
            String memo = "提醒时间：" + new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss").format(dmmJob.getRunTime());
// 注释掉发送我的消息          userSuspendMsgService.saveUserSuspendMsg(1, cont,cont, Dmm, DmmJob.getUser(),"scheduleDetail",DmmJob.getScheduleId(),"DmmScheduleService",DmmJob.getId().toString());
            dmmJobMsgService.saveDmmJobMsg(1,cont,cont,memo,dmmJob.getAccId(),dmmJob.getId(),dmmJob.getScheduleId());
            //日程 提醒 开始一个小时后自动过期
            DmmJobRemind dmmJobRemind = new DmmJobRemind(dmmJob.getId(),dmmJob.getUpdateDate());
//            clusterMessageSendingOperations.delayCall(NewDateUtils.changeHour(dmmJob.getRunTime(),1),dmmJobRemind);
            clusterMessageSendingOperations.delayCall(new Date(dmmJob.getRunTime().getTime() + TimeUnit.HOURS.toMillis(1)),dmmJobRemind);

            //日程主页推送
//            clusterMessageSendingOperations.convertAndSendToUser(dmmJob.getUser().toString(),"/scheduleHomepage",null,null,user.getOid(),null,JSON.toJSONString(DmmJob.getSchedule()));
            //日程提醒记录页推送
//            clusterMessageSendingOperations.convertAndSendToUser(dmmJob.getUser().toString(),"/DmmJobPage",null,null,user.getOid(),null,JSON.toJSONString(DmmJob));


        }
    }

    @Override
    public void overdueDmmJobSchedule(Integer id,Date updateDate) {
        DmmJob dmmJob=dmmJobDao.get(id);
        if (dmmJob.getState()==2&&
                dmmJob.getUpdateDate().getTime()<=updateDate.getTime()){

            dmmJob.setState(4);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
//            if (DmmJob.getSchedule().getFreqType()==1){//一次性日程，日程也过期
                dmmJob.getSchedule().setState(4);
//            }
        }
    }

    @Override
    public List<DmmSchedule> getDmmScheduleListByDate(Long accId, Date begin, Date end) {
        String hql=" from DmmSchedule where accId=:accId and ((freqType>1 and activeStartDate<:begin) or (freqType=1 and :begin<activeStartDate and activeStartDate<:end))";

//        (freqType>1 or (freqType=1 and activeStartDate>:lingChenDate))
        if (end.getTime()>new Date().getTime()){
            hql+=" and notify=true";
        }
        Map<String,Object> params=new HashMap<>();
        params.put("begin",begin);
        params.put("end",end);
        params.put("accId",accId);
        List<DmmSchedule> dmmScheduleList=dmmScheduleDao.getListByHQLWithNamedParams(hql,params);
        return dmmScheduleList;
    }

    @Override
    public List<DmmSchedule> keywordQuerySchedule(Long accId, String keyword, String description, String supplement, String place, PageInfo pageInfo) {
        Map<String,Object> params=new HashMap<>();
        params.put("accId",accId);
        params.put("keyword","%"+keyword+"%");
        String hql=" from DmmSchedule where isValid=true and accId=:accId and (";
        if (!StringUtil.isNullOrEmpty(description)){
            hql+=" title like:keyword or";
        }
        if (!StringUtil.isNullOrEmpty(supplement)){
            hql+=" supplement like:keyword or";
        }
        if (!StringUtil.isNullOrEmpty(place)){
            hql+=" place like:keyword or";
        }
        hql= StringUtils.substringBeforeLast(hql,"or");
        hql+=")";
        List<DmmSchedule> dmmScheduleList=dmmScheduleDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return dmmScheduleList;
    }

    @Override
    public void scheduleNoRemind(Integer id) {
//        List<String> DmmJobList=new ArrayList<>();
        DmmSchedule m = dmmScheduleDao.get(id);
        for (DmmJob mj : m.getDmmJobHashSet()) {
//            if (mj.getEnabled().equals(1)) {
            if(1!=mj.getState()&&4!=mj.getState()){
                mj.setEnabled(0);
                mj.setState(3);
                mj.setUpdateDate(new Date());
                dmmJobDao.update(mj);
            }
//            DmmJobList.add(mj.getId().toString());
        }
        m.setModifiable(false);//设置成不可修改
        m.setEnabled(0);
        m.setState(3);
        m.setUpdateDate(new Date());
        this.updateDmmSchedule(m);

        m.setDmmScheduleImageHashSet(null);
//        if (DmmJobList.size()>0) {//删除消息记录
//            userSuspendMsgService.deleteUserSuspendMsg(userDao.get(m.getUser()), "DmmScheduleService", DmmJobList);
//        }
        List<DmmJobMsg> dmmJobMsgList=dmmJobMsgService.getDmmJobMsgByScheduleId(id,"1");
        for (DmmJobMsg dmmJobMsg:dmmJobMsgList){
            dmmJobMsgService.updateDmmJobMsg(dmmJobMsg,"2",dmmJobMsg.getAccId());//变成已读
        }
        //日程主页推送
//        clusterMessageSendingOperations.convertAndSendToUser(m.getUser().toString(),"/scheduleHomepage",null,null,user.getOid(),null,JSON.toJSONString(m));
        //日程提醒记录页推送
//        clusterMessageSendingOperations.convertAndSendToUser(m.getUser().toString(),"/dmmJobPage",null,null,user.getOid(),null,JSON.toJSONString(m));

    }

    @Override
    public void handleSchedule(DmmSchedule dmmSchedule, String type, Date remindDate) {

        String hql=" from DmmJob where scheduleId=:sId order by createDate desc";
        Map<String,Object> params=new HashMap<>();
        params.put("sId",dmmSchedule.getId());
        DmmJob dmmJob= (DmmJob) dmmJobDao.getByHQLWithNamedParams(hql,params);//最新一次提醒记录
        dmmJob.setUpdateDate(new Date());
        dmmJob.getSchedule().setUpdateDate(new Date());
        switch (type){
            case "1":
                dmmJob.setState(1);
                dmmJob.setEnabledTime(new Date());
                if (dmmSchedule.getFreqType()==1){
                    dmmSchedule.setState(1);
                    dmmSchedule.setActiveEndDate(new Date());
                }else {
                    dmmSchedule.setState(4);//日程详情 状态要展示成下次提醒时间
                }
                break;
            case "2":
                dmmJob.setState(3);
                if (dmmSchedule.getFreqType()==1){
                    dmmSchedule.setState(3);
                }else {
                    dmmSchedule.setState(4);//日程详情 状态要展示成下次提醒时间
                }
                dmmJob.getSchedule().setModifiable(false);//设置成不可修改

                break;
            case "3":
                dmmJob.setType(2);//类型：1-约定提醒(按约定规则进行的),2-再次提醒(人工选择了再次提醒)
                dmmJob.setRunTime(remindDate);//再次提醒时间
                dmmJob.setEnabled(1);//提醒 过了 需要再次提醒
                dmmJob.getSchedule().setLastRunTime(remindDate);//延时的再次提醒时间(纯为详情中展示用)
                //日程 到时间 提醒
                DmmScheduleRemind dmmScheduleRemind = new DmmScheduleRemind(dmmJob.getId());
                clusterMessageSendingOperations.delayCall(dmmJob.getRunTime(),dmmScheduleRemind);
                break;
        }
        //日程主页推送
//        clusterMessageSendingOperations.convertAndSendToUser(dmmJob.getUser().toString(),"/scheduleHomepage",null,null,user.getOid(),null,JSON.toJSONString(DmmJob.getSchedule()));
        //日程提醒记录页推送
//        clusterMessageSendingOperations.convertAndSendToUser(dmmJob.getUser().toString(),"/DmmJobPage",null,null,user.getOid(),null,JSON.toJSONString(DmmJob));

    }

    @Override
    public void scheduleSettleDay(String code) {
        long now = System.currentTimeMillis();
//        Date dangQianDate=new Date();
        Schedule schedule=scheduleService.getScheduleByCode(code);//记录定时器 最后 执行时间
        Date begin=schedule.getRunTime();
        Date end = new Date(now+TimeUnit.DAYS.toMillis(1));
        List<DmmSchedule> dmmScheduleList = this.getNextDmmScheduleList(begin,end);//下次提醒时间在  上次执行日程定时器时间 和 当前时间24小时后  之间
        System.out.println("符合条件的日程列表总数："+dmmScheduleList.size());
        for (DmmSchedule dmmSchedule : dmmScheduleList) {
            Long runTime=dmmSchedule.getNextRunTime().getTime();
            DmmJob dmmJob = null;
            if(runTime<end.getTime()){
                System.out.println("生成日程"+dmmSchedule.getId()+" 的单个提醒开始");
                while (runTime<end.getTime()) {
                    dmmJob = new DmmJob();
                    runTime = dmmJobService.addDmmJobByUserScheduleTask(dmmSchedule, dmmJob,runTime ,now);//生成此日程单个周期提醒记录
                }
                System.out.println("生成日程"+dmmSchedule.getId()+" 的单个提醒结束");
            }
            //日程 到时间 提醒
            if (dmmJob!=null&&dmmJob.getRunTime().getTime()>now) {
                DmmScheduleRemind dmmScheduleRemind = new DmmScheduleRemind(dmmJob.getId());
                clusterMessageSendingOperations.delayCall(dmmJob.getRunTime(),dmmScheduleRemind);
            }
        }
        schedule.setRunTime(new Date(now+TimeUnit.DAYS.toMillis(1)));
    }


    public List<DmmSchedule> getNextDmmScheduleList(Date beginTime, Date nextDate) {

        String hql=" from DmmSchedule where state not in(3) and isValid=true and notify=true and :beginTime<nextRunTime and nextRunTime<=:nextDate";
        Map<String,Object> params=new HashMap<>();
//        params.put("userId",userId);
        params.put("beginTime",beginTime);//当前时间  and activeStartDate<:dangQianDate)
        params.put("nextDate",nextDate);//24小时之后

        List<DmmSchedule> dmmScheduleList=dmmScheduleDao.getListByHQLWithNamedParams(hql,params);
        return dmmScheduleList;
    }

    @Override
    public Integer getDmmScheduleCounts(Long accId) {
        String hql="select count(id) from DmmSchedule where accId=:accId";
        Map<String,Object> params=new HashMap<>();
        params.put("accId",accId);
        Long number= (Long) dmmScheduleDao.getByHQLWithNamedParams(hql,params);
        return number.intValue();
    }

    @Override
    public List<DmmSchedule> getDmmListByUserId(Long accId, PageInfo pageInfo) {
        String hql=" from DmmSchedule where isValid=true and notify=false and id not in(select scheduleId from DmmFavoriteSchedule) and accId=:accId";
        Map<String,Object> params=new HashMap<>();
        params.put("accId", accId);
        List<DmmSchedule> dmmScheduleList=dmmScheduleDao.getListByHQLWithNamedParams(hql+" order by updateDate desc,id desc",params,pageInfo);
        return dmmScheduleList;
    }

    @Override
    public void updateDmmSchedule(DmmSchedule dmmSchedule, AuthAcc acc, String[] imagePaths) {
        dmmSchedule.setUpdateDate(new Date());
//        dmmSchedule.setUpdator(user.getUserID());
        dmmSchedule.setUpdateName(acc.getName());
        dmmSchedule.setSource("2"); //来源：1-新增,2-修改原任务而来,3-设置新的提醒时间复制而来
        if (dmmSchedule.isNotify()) {//需要通知
            dmmSchedule.setEnabled(1);
            dmmSchedule.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        }
        dmmSchedule.setNextRunTime(dmmSchedule.getActiveStartDate());//下次提醒时间是日程开始提醒时间
        dmmScheduleDao.update(dmmSchedule);

        this.saveDmmImages(dmmSchedule,imagePaths,acc); //保存日程附件

        if (dmmSchedule.getNextRunTime()!=null) {//下次提醒时间不等于null
            DmmJob lastDmmJob=dmmJobService.getLastDmmJobBySid(dmmSchedule.getId());//
            if (lastDmmJob!=null
                    &&NewDateUtils.changeDay(lastDmmJob.getRunTime(),0).getTime()==NewDateUtils.today(new Date()).getTime()
                    &&lastDmmJob.getRunTime().getTime()>new Date().getTime()) {//
                //原日程 最新一次的提醒设定为当日 当前时间之后的时间
                lastDmmJob.setValid(false);// 设成删除 状态
            }
            Date beginDate = dmmSchedule.getNextRunTime();
            if (dmmSchedule.isNotify() &&
                    beginDate.getTime() > new Date().getTime() &&
                    NewDateUtils.today(beginDate).getTime() == NewDateUtils.today(new Date()).getTime()) {
                //需要提醒 且开始提醒时间为 当天，此刻以后的时间
                DmmJob dmmJob = dmmJobService.addDmmJobByUserSchedule(dmmSchedule);
                //日程 到时间 提醒
                if (1 == dmmJob.getEnabled()) {
                    DmmScheduleRemind dmmScheduleRemind = new DmmScheduleRemind(dmmJob.getId());
                    clusterMessageSendingOperations.delayCall(dmmJob.getRunTime(), dmmScheduleRemind);

//                    DmmJob.setEnabled(0);//提醒已放到延时队列中 不需要再
                }
            }
        }
    }

    @Override
    public List<DmmSchedule> getDmmScheduleListByKeyword(Long accId, String keyword, PageInfo pageInfo) {
        Map<String,Object> params=new HashMap<>();
        params.put("accId",accId);
        params.put("keyword","%"+keyword+"%");
        String hql=" from DmmSchedule where isValid=true and notify=false and accId=:accId and (title like:keyword or description like:keyword)";
        List<DmmSchedule> dmmScheduleList=dmmScheduleDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return dmmScheduleList;
    }

    @Override
    public void deleteDmmSchedule(DmmSchedule dmmSchedule) {
        dmmScheduleDao.delete(dmmSchedule);
    }

    @Override
    public void deleteDmmSchedule(Integer id,AuthAcc acc) {
        DmmSchedule m = dmmScheduleDao.get(id);
        if (m.getFreqType()!=null) { //有提醒的日程，逻辑删除  现改成物理删除
//                for (DmmJob mj : m.getDmmJobHashSet()) {
//                    mj.setEnabled(0);
//                    mj.setValid(false);
////                DmmJobList.add(mj.getId().toString());
//                    DmmJobService.updateDmmJob(mj);
//                }
//            if (DmmJobList.size()>0) {
//                userSuspendMsgService.deleteUserSuspendMsg(user, "DmmScheduleService", DmmJobList);
//            }
            List<DmmJobMsg> dmmJobMsgList = dmmJobMsgService.getDmmJobMsgByScheduleId(id, null);
            for (DmmJobMsg dmmJobMsg : dmmJobMsgList) {
                dmmJobMsgService.updateDmmJobMsg(dmmJobMsg, "4", m.getAccId());//目前未处理的 变成删除 并推送减去角标和数据
            }
            dmmJobMsgService.deleteAllDmmJobMsg(dmmJobMsgList);//删除所有该日程的具体提醒记录

            m.setValid(false);
//                DmmScheduleService.updateDmmSchedule(m);
        }
//            else {
//                DmmScheduleService.deleteDmmSchedule(m);// 不需要提醒 的日程 物理删除
//            }


        for (DmmScheduleImage dmmScheduleImage:m.getDmmScheduleImageHashSet()){
            UploadFile uploadFile=uploadService.getUploadFile(dmmScheduleImage.getNormal());
            if (uploadFile!=null) {
                DmmUsing callback = new DmmUsing(dmmScheduleImage.getId(), dmmScheduleImage.getClass());
                try {
                    uploadService.delFileUsing(callback, dmmScheduleImage.getNormal(), acc);
                }catch (Exception e){

                }
            }
        }

        for (DmmScheduleHistory dmmScheduleHistory:m.getDmmScheduleHistoryHashSet()){
            for (DmmScheduleImageHistory dmmScheduleImageHistory:dmmScheduleHistory.getMemoScheduleImageHistoryHashSet()){
                //删除日程的 文件引用  2021/3/9 lixu 1.52空间与流量
                UploadFile uploadFile=uploadService.getUploadFile(dmmScheduleImageHistory.getNormal());
                if (uploadFile!=null) {
                    DmmUsing callback = new DmmUsing(dmmScheduleImageHistory.getId(), dmmScheduleImageHistory.getClass());
                    try {
                        uploadService.delFileUsing(callback, dmmScheduleImageHistory.getNormal(), acc);
                    }catch (Exception e){

                    }
                }
            }
        }

        List<DmmScheduleSupplement> dmmScheduleSupplementList=dmmScheduleSupplementService.getDmmScheduleSupplementListBySid(id);
        for (DmmScheduleSupplement scheduleSupplement:dmmScheduleSupplementList){
            delDmmScheduleSupplementImagFileUsing(scheduleSupplement.getDmmScheduleSupplementImageHashSet(),acc);  //删除日程 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
        }

        dmmScheduleDao.delete(m);// 需不需要提醒的日程 都改成物理删除0
    }

    //删除补充记录的附件引用
    @Override
    public void delDmmScheduleSupplementImagFileUsing(Set<DmmScheduleSupplementImage> dmmScheduleSupplementImageHashSet, AuthAcc acc) {
        for (DmmScheduleSupplementImage dmmScheduleSupplementImage:dmmScheduleSupplementImageHashSet){
            UploadFile uploadFile=uploadService.getUploadFile(dmmScheduleSupplementImage.getNormal());
            if (uploadFile!=null) {
                try{
                    uploadService.delFileUsing(new DmmUsing(dmmScheduleSupplementImage.getId(), dmmScheduleSupplementImage.getClass()), dmmScheduleSupplementImage.getNormal(), acc); // 删除补充记录的附件 引用
                }catch (Exception e) {
                }
            }
        }
    }
}
