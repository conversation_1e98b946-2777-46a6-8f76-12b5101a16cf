<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true :showBack=true @mycloseFun="handleCustomClose">
        <p v-if="Bill.index!=0" @click="statistics()">统计</p>
      </PageHeader>
    </div>
    <div class="sticky" v-if="Bill&&list.numAmount">
      <p class="padding-0-15" v-if="Bill.index==0">以下为收到且尚在手中的外部转账支票</p>
      <p class="padding-0-15" v-else>{{ new
      Date(list.beginDate).format('yyyy-MM-dd') }}至{{ new
      Date(list.endDate).format('yyyy-MM-dd') }}期间收到的外部转账支票</p>
      <p class="padding-0-15">共{{ list.numAmount.num
        }}张,{{ list.numAmount.amount == null ? 0 : this.formatCurrency(list.numAmount.amount) }}元</p>
    </div>
    <div class="re margin_top_16" v-if="list.financeReturns">
      <div class="spaceBetween" v-for="(item,index) in list.financeReturns" :key="index"
           :class="{ 'f4': index % 2 !== 0 ,'ty-fff':index%2===0}">
        <div>
          <p>
            <span>到期日</span><span class="padding-0-5">{{ new
          Date(item.expireDate).format('yyyy-MM-dd') }}</span>
            <span class="padding-0-10">票据金额<span class="padding-0-5">{{this.formatCurrency(item.amount)}}</span></span>
          </p>
          <p>支票号<span class="padding-0-5">{{ item.returnNo }}</span></p>
          <p>录入者<span class="padding-0-5">{{ item.createName }}</span>{{ new
          Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}
          </p>
        </div>
        <div class="ic" @click="rou(item.id)">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getReturnList} from '@/api/financeBill'
import {formatCurrency} from '@/utils/utils'

export default {
  name: "cftList",
  data() {
    return {
      tit: "财会",
      list: {},
      formatCurrency: formatCurrency,
      Bill: {},
      state: '',
    }
  },
  mounted() {
    let storedBill = localStorage.getItem('Bill');
    this.Bill = JSON.parse(storedBill);
    this.getList(this.Bill)
  },
  methods: {
    rou(billId) {
      this.$router.push({path: "/moneyOrderDetail", query: {billId: billId}})
    },
    getList(Bill) {
      if (Bill.index == 0) {
        this.state = 1
        this.beginTime = '';
      } else if (Bill.index == 1) {
        this.state = ''
        this.beginTime = new Date(new Date().getFullYear(), 0, 1).format('yyyy-MM-dd');
      } else if (Bill.index == 2) {
        this.beginTime = new Date(new Date().getFullYear() - 1, 0, 1).format('yyyy-MM-dd');
        console.log(this.beginTime, 'this.beginTime1111111111111111111111111')
      } else if (Bill.index == 3) {
        this.beginTime = new Date(new Date().getFullYear() - 2, 0, 1).format('yyyy-MM-dd');
      } else if (Bill.index == 4) {
        this.beginTime = new Date(this.Bill.result, 0, 1).format('yyyy-MM-dd');
      }
      const params = {type: 1, state: this.state, beginTime: this.beginTime}
      getReturnList(params).then(res1 => {
        this.list = res1.data.data
      }).catch(error => {
        console.error('Error:', error);
      });
    },
    statistics() {
      let billStatistics = {billType: 1, beginTime: this.list.beginDate, endDate: this.list.endDate}
      localStorage.setItem('billStatistics', JSON.stringify(billStatistics))
      this.$router.push({path: "/statisticsHome"})
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    }
  },
}
</script>
<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.sticky {
  background-color: #ffffff;
  line-height: 20px;
  padding: 10px 0 10px 0;
}

.re {
  position: relative;
  top: 40px;

  .spaceBetween {
    > div {
      padding: 10px 15px;
      line-height: 20px;
    }
  }
}
</style>