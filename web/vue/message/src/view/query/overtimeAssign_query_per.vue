<template>
  <div id="overtimeAssignQueryPer">
    <TY_NavTop title="被指派的加班"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.begin | formatDay('YYYY年MM月DD日')}}-{{queryData.end | formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent">
              您不同意指派加班的申请
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的数据共如下{{queryData.personnelOvertimes.length}}条
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in queryData.personnelOvertimes" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.beginTime |formatDay('YYYY年MM月DD日 dddd')}}</div>
                <div>需加班时长  {{item.duration}}h <div style="float:right">{{item.assignerName}} {{item.assigneTime|formatDay('YYYY-MM-DD HH:mm:ss')}}</div></div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.queryCondition{
  background: #fffaeb;
  font-size: 12px;
  padding: 8px;
  margin-bottom: 5px;
}
</style>

<script>
export default {
  name: 'overtimeAssignQueryPer',
  data () {
    return {
      loading: true,
      paramQuery: {},
      queryData: {
        begin: 1560408078000,
        end: 1560408078000,
        personnelOvertimes: []
      }
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    let that = this
    this.$http.post('../../../leaveAndOutTime/assignOvertimeQuery.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body
      if (data) {
        let data = response.data.data
        console.log(response.data)
        that.queryData = data
      } else {
        that.$kiko_message('加载失败')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/overtimeAssignDetail/${id}/query`
      })
    }
  }
}
</script>
