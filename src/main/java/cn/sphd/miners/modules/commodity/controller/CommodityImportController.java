package cn.sphd.miners.modules.commodity.controller;


import cn.sphd.miners.modules.commodity.dto.ReqPdObject;
import cn.sphd.miners.modules.commodity.dto.RespCommodityImport;
import cn.sphd.miners.modules.commodity.entity.PdCommodityImport;
import cn.sphd.miners.modules.commodity.entity.PdCommodityImportLog;
import cn.sphd.miners.modules.commodity.service.CommodityImportService;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ProductImportController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 9:37
 * @Version 1.0
 */
@Controller
@RequestMapping("/commodityImport")
public class CommodityImportController {
    @Autowired
    CommodityImportService commodityImportService;

    //判断是否存在未完成批量导入
    // isPurchased;//1是采购过 0是未采购过
    // produdctCatetory;//商品类型:1-通用,2-专属
    //返回值1，为没有未完成批量导入，可直接导入
    //      0，为有未完成批量导入
    @ResponseBody
    @RequestMapping("/whetherUnfinishedImport.do")
    public RespStatus whetherUnfinishedImport(User user, Integer isPurchased,String produdctCatetory) throws IOException {
        RespStatus respStatus=new RespStatus();
        Integer org = user.getOid();
        int status= commodityImportService.whetherUnfinishedImport(org,isPurchased,produdctCatetory);
        respStatus.setStatus(status);
        return respStatus;
    }
    //确认单条未导入错误信息是否修改为合法信息
    //传值 code 代号
    //返回值 status 1为可修改，2为和已有的商品相同 3为计量单位已被禁用,4为和其他正在导入的商品相同
    @ResponseBody
    @RequestMapping("/updateFalsePdEnter.do")
    public RespStatus updateFalsePdEnter(User user, PdCommodityImport pdCommodityImport) throws IOException {
        Integer org = user.getOid();
        pdCommodityImport.setOrg(org);
        RespStatus respStatus= commodityImportService.updateFalsePdEnter(pdCommodityImport);
        return respStatus;
    }
    //确认全部导入数据有多少不合法
    //传值 pdProductImportListJson  导入商品列表
    //    importSum;//导入总数
    //    produdctCatetory;//1为通用商品 2为专属商品
    //    importOption;//导入选项:1-全部,2-开增值税专用发票的,3-开普通发票的,4-不开发票的,5-通用商品,不开增值税专用发票的
    //    taxRate;//税率
    //    taxInclusive;//是否含税价:1-含税,0不含税
    //    isSaled;//是否销售过,1销售过,0未销售过
    //返回值 falseImportSum 无法保存的数量
    //     tureImportSum 可以保存的数量
    //     importSum;//导入总数
    @ResponseBody
    @RequestMapping("/allImportPdEnter.do")
    public ReqPdObject allImportPdEnter(User user, ReqPdObject reqPdObject) throws IOException {
        List<PdCommodityImport> pdCommodityImportList = JSONArray.parseArray(reqPdObject.getPdProductImportListJson(),PdCommodityImport.class);
        List<RespCommodityImport> respCommodityImportList=new ArrayList<>();
        for(PdCommodityImport commodityImport: pdCommodityImportList){
            RespCommodityImport respCommodityImport=new RespCommodityImport();
            BeanUtils.copyProperties(commodityImport,respCommodityImport);
            respCommodityImportList.add(respCommodityImport);
        }
        reqPdObject.setPdProductImportList(respCommodityImportList);
        reqPdObject.setOrg(user.getOid());
        ReqPdObject reqPd= commodityImportService.allImportPdEnter(reqPdObject);
        return reqPd;
    }
    //点击确定，提交所有导入材料，不可导入数据删除，可导入数据入数据库
    //传值  pdProductImportListJson  导入商品列表
    //     importSum;//导入总数
    //     customer 客户id
    //     importOption;//导入选项:1-全部,2-开增值税专用发票的,3-开普通发票的,4-不开发票的,5-通用商品,不开增值税专用发票的
    //     taxRate;//税率
    //     taxInclusive;//是否含税价:1-含税,0不含税
    //     isPurchased;//是否销售过,1销售过,0未销售过
    //     produdctCatetory;;//1为通用商品 2为专属商品
    //返回值
    //      tureImportSum 可以保存的数量
    //      importSum;//导入总数
    //      List<PdProductImport> pdProductImportList;
    //      buttonState 确定按钮状态1- 变亮 0- 置灰
    @ResponseBody
    @RequestMapping("/saveImportPd.do")
    public ReqPdObject saveImportPd(User user, ReqPdObject reqPdObject) throws IOException {
        List<RespCommodityImport> respCommodityImportList= JSONArray.parseArray(reqPdObject.getPdProductImportListJson(),RespCommodityImport.class);
        reqPdObject.setPdProductImportList(respCommodityImportList);
        reqPdObject.setOrg(user.getOid());
        ReqPdObject reqPd= commodityImportService.saveImportPd(reqPdObject,user);
        Integer noUnitNumber= commodityImportService.getNoUnitNumberCountByOrg(user.getOid(),reqPdObject.getIsPurchased(),reqPdObject.getProdudctCatetory());
        if (noUnitNumber==0){
            reqPd.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            reqPd.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return reqPd;
    }
    //未完成批量导入列表接口
    //传值 isPurchased;//1是采购过 0是未采购过（string）
    //     produdctCatetory;//1为通用商品 2为专属商品
    //返回值 falseImportSum 无法保存的数量
    //       tureImportSum 可以保存的数量
    //       importSum;//导入总数
    //       List<MtBase> mtBaseList  物料列表

    @ResponseBody
    @RequestMapping("/unfinishedImportPd.do")
    public ReqPdObject unfinishedImportPd(User user, String isPurchased,Integer produdctCatetory) throws IOException {
        ReqPdObject reqPd= commodityImportService.unfinishedImportPd(user,isPurchased,produdctCatetory);
        Integer noUnitNumber= commodityImportService.getNoUnitNumberCountByOrg(user.getOid(),isPurchased,produdctCatetory);
        if (noUnitNumber==0){
            reqPd.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            reqPd.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return reqPd;
    }
    //确定或放弃导入
    //传值 type 1保存，0放弃 isPurchased;//1是采购过 0是未采购过（string）//produdctCatetory 1为通用商品 2为专属商品
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/finishImportPd.do")
    public RespStatus finishImportPd(User user, String isPurchased,Integer type,Integer produdctCatetory) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status= commodityImportService.finishImportPd(user,isPurchased,type,produdctCatetory);
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改商品
    //传值 isPurchased;//1是采购过 0是未采购过（string）
    //     produdctCatetory;//1为通用商品 2为专属商品
    //     deletePdCommodityImportAttachListJson;
    //     addPdCommodityImportAttachListJson;
    //     invoice_type                  char(1)  comment '发票类型',
    //                tax_rate                      decimal(20,10)  comment '税率(增专)',
    //                unit_price                    decimal(20,10)  comment '含税单价(增专)',
    //                unit_price_notax              decimal(20,10)  comment '不含税单价(增专)',
    //                unit_price_invoice            decimal(20,10)  comment '开票单价(增通)',
    //                unit_price_noinvoice          decimal(20,10)  comment '不开票单价',
    //                unit_price_reference          decimal(20,10)  comment '参考价格',
    @ResponseBody
    @RequestMapping("/updateImportPd.do")
    public RespStatus updateImportPd(RespCommodityImport pdProductImport,User user,String isPurchased,Integer produdctCatetory) throws IOException {
        RespStatus respStatus= commodityImportService.updateImportPd(pdProductImport,user);
        Integer noUnitNumber= commodityImportService.getNoUnitNumberCountByOrg(user.getOid(),isPurchased,produdctCatetory);
        if (noUnitNumber==0){
            respStatus.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            respStatus.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return respStatus;
    }
    //删除商品
    //传值 id isPurchased;//1是采购过 0是未采购过（string）
    //      produdctCatetory;//1为通用商品 2为专属商品
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/deleteImportPd.do")
    public RespStatus deleteImportPd(int id,User user,String isPurchased,Integer produdctCatetory) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status= commodityImportService.deleteImportPd(id,user);
        respStatus.setStatus(status);
        Integer noUnitNumber= commodityImportService.getNoUnitNumberCountByOrg(user.getOid(),isPurchased,produdctCatetory);
        if (noUnitNumber==0){
            respStatus.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            respStatus.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return respStatus;
    }
    //保存时确定有多少数据不合法
    //传值 isPurchased;//1是采购过 0是未采购过（string）
    //      produdctCatetory;//1为通用商品 2为专属商品
    //返回值 count数量
    @ResponseBody
    @RequestMapping("/finishImportPdEnter.do")
    public RespStatus finishImportPdEnter(User user , String isPurchased,Integer produdctCatetory) throws IOException {
        RespStatus respStatus=new RespStatus();
        int count= commodityImportService.finishImportPdEnter(user,isPurchased,produdctCatetory);
        respStatus.setCount(count);
        return respStatus;
    }
    //处理异常数据
    @ResponseBody
    @RequestMapping("/handleImportData.do")
    public int handleImportData(User user) throws IOException {
      int number= commodityImportService.handleImportData(user);
      return number;
    }
}
