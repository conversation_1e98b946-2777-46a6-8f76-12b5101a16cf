<template>
  <div id="potentialApply" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-title">
        {{recordTtl}}修改记录
      </div>
      <div class="panel-content sect-con">
        <div v-if="list.length === 0">
          <p>当前资料尚未经修改。</p>
          <p class="createDetail">创建人：{{details.createName}} {{details.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
        </div>
        <div v-else>
          <div class="record">
            <p>当前资料为第{{list.length-1}}次修改后的结果。</p>
            <p class="createDetail">修改人：{{details.updateName}} {{details.updateDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
          <div class="record" v-for="(item, index) in list" :key="index">
            <div v-if="index === 0">
              <el-row>
                <el-col :span="8">原始信息</el-col>
                <el-col :span="16"><span class="sect-blue" @click="jumpDetails(item, 0, index)">查看</span></el-col>
              </el-row>
              <p class="createDetail">创建人：{{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
            </div>
            <div v-else>
              <el-row>
                <el-col :span="8">第{{index}}次修改后</el-col>
                <el-col :span="16"><span class="sect-blue" @click="jumpDetails(item, list[index-1].id, index)">查看</span></el-col>
              </el-row>
              <p class="createDetail">修改人：{{item.updateName}} {{item.updateDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'potentialRecord',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      list: [],
      details: {},
      recordTtl: '',
      recordType: ''
    }
  },
  created () {
    let _this = this
    let urlStr = ''
    let json = {
      customerId: _this.$route.params.detailId
    }
    let recordType = _this.$route.params.recordType
    _this.recordType = recordType
    if (recordType === '1') {
      _this.recordTtl = '基本信息'
      urlStr = '../../../sale/getPotentialRecordBaseList.do'
    } else if (recordType === '2' || recordType === '22') {
      _this.recordTtl = '联系人'
      urlStr = '../../../sale/getPotentialRecordContactList.do'
      json.contactId = _this.$route.params.recordId
    } else if (recordType === '3' || recordType === '33') {
      _this.recordTtl = '访谈记录'
      urlStr = '../../../sale/getPotentialRecordInterviewList.do'
      json.interviewId = _this.$route.params.recordId
    }
    this.axios.post(urlStr, json
    ).then(function (response) {
      _this.loading = false
      _this.details = response.data
      _this.list = response.data.list
    }).catch(function (error) {
      console.log(error)
    })
  },
  methods: {
    jumpDetails: function (item, frontId, index) {
      let recordType = this.recordType
      if (frontId === 0) {
        this.$router.push({
          path: `/recordDetail/${recordType}/${item.id}/${frontId}/${index}/${item.createName}/${item.createDate}`
        })
      } else {
        this.$router.push({
          path: `/recordDetail/${recordType}/${item.id}/${frontId}/${index}/${item.updateName}/${item.updateDate}`
        })
      }
    }
  }
}
</script>
