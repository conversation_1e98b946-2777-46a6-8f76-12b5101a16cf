package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/*
* <AUTHOR>
* @Date 2019/4/22 16:18
* @Description 该类用于存放报销与科目关联的情况
* */
@Alias("TAccountantSubjectRelevance")
public class TAccountantSubjectRelevance {
  private Integer id;
  private Integer org;
  private String module;
  private Integer subjectId;
  private String subject;
  private Integer feeCat;
  private Integer subFeeCat;
  private String code;
  private String subCode;
  private String name;
  private Integer enabled;
  private String memo;
  private Integer creator;
  private String createName;

  @DateTimeFormat(pattern="yyyy-MM-dd")
  private Date createDate;

  private Integer updator;
  private String updateName;

  @DateTimeFormat(pattern="yyyy-MM-dd")
  private Date updateDate;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public String getModule() {
    return module;
  }

  public void setModule(String module) {
    this.module = module;
  }

  public Integer getSubjectId() {
    return subjectId;
  }

  public void setSubjectId(Integer subjectId) {
    this.subjectId = subjectId;
  }

  public String getSubject() {
    return subject;
  }

  public void setSubject(String subject) {
    this.subject = subject;
  }

  public Integer getFeeCat() {
    return feeCat;
  }

  public void setFeeCat(Integer feeCat) {
    this.feeCat = feeCat;
  }

  public Integer getSubFeeCat() {
    return subFeeCat;
  }

  public void setSubFeeCat(Integer subFeeCat) {
    this.subFeeCat = subFeeCat;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getSubCode() {
    return subCode;
  }

  public void setSubCode(String subCode) {
    this.subCode = subCode;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Integer getEnabled() {
    return enabled;
  }

  public void setEnabled(Integer enabled) {
    this.enabled = enabled;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }
}
