package cn.sphd.miners.modules.personal.service;

import cn.sphd.miners.modules.personal.entity.PersonnelContract;
import cn.sphd.miners.modules.personal.entity.PersonnelContractHistory;
import cn.sphd.miners.modules.personal.entity.PersonnelContractImage;
import cn.sphd.miners.modules.personal.entity.PersonnelContractImageHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface PersonnelContractService {

    enum PerContractOperation { //操作方式
        add("增", (byte)1),
        del("删",(byte)2),
        update("改", (byte)3),
        original("不变", (byte)4);
        private String name;
        private Byte index;
        PerContractOperation(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }

        public Byte getIndex() {
            return index;
        }
    }

    enum PerConStatus { //操作方式
        efficient("有效", (byte)1),
        renewal("续约",(byte)3),
        suspend("终止", (byte)2),
        expired("过期", (byte)4);

        private String name;
        private Byte index;
        PerConStatus(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }

        public Byte getIndex() {
            return index;
        }
    }



    //新增劳动合同
    PersonnelContract insertPersonnelController(User user, PersonnelContract personnelContract, String contractSignTime, String contractStartTime,
                                                String contractEndTime,String contractBaseImages);

    //修改合同
    Integer upPerCon(User user, PersonnelContract personnelContract, String contractSignTime, String contractStartTime,
                     String contractEndTime,String contractBaseImages);

    //续约合同
    Integer renewalPerCon(User user, PersonnelContract personnelContract, String contractSignTime, String contractStartTime,
                          String contractEndTime,String contractBaseImages);

    //终止合同
    Integer terminatePerCon(Integer userID);

    List<PersonnelContract> getPerConSignRecord(Integer userID);

    //劳动合同详情
    Map perConMes(Long id);

    //劳动合同的修改记录
    List<PersonnelContractHistory> getListPerConHis(Long id);

    Map perConHisMes(Long contractHisId);

    PersonnelContract perConSingle(Long id);

    PersonnelContractHistory perConHisSingle(Long id);

    PersonnelContractImage perConImage(Long id);

    PersonnelContractImageHistory perConImageHis(Long id);

    List<PersonnelContract> getPersonnelContractsByUserIds(List<Integer> userIdList, Byte status);

    void personnelContractEveryDayTask();

    void sendPerConMes(Integer userId, String memo);

}
