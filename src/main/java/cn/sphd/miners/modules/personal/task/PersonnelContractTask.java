package cn.sphd.miners.modules.personal.task;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.personal.service.PersonnelContractService;
import cn.sphd.miners.modules.system.service.DlmService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

public class PersonnelContractTask {

    @Autowired
    DlmService dlmService;
    @Autowired
    PersonnelContractService personnelContractService;


    public void personnelContractTaskDay() {
        final Logger logger = Logger.getLogger(getClass());
        logger.warn("职工合同每日定时任务:开始定时获取分布式锁");
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            logger.warn("职工合同每日定时任务开始延时：" + NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy/MM/dd-HH:mm:ss:SSS"));
            try {
                Thread.sleep((long) (Math.random() * 3600));
            } catch (InterruptedException e) {
                Logger.getLogger(getClass()).warn("Thread sleep exception : ", e);
                Thread.currentThread().interrupt();
                return;
            }
            Long time = System.currentTimeMillis();
            logger.warn("职工合同每日定时任务开始：" + NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy/MM/dd-HH:mm:ss:SSS"));
            personnelContractService.personnelContractEveryDayTask();
            logger.warn("职工合同每日定时任务结束：" + NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy/MM/dd-HH:mm:ss:SSS"));
            dlmService.releaseLock(methodName, lockKey);

        }
    }

}
