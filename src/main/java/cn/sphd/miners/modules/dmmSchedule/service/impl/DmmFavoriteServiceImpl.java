package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.modules.dmmSchedule.dao.DmmFavoriteDao;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmFavoriteScheduleDao;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmFavorite;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmFavoriteSchedule;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;
import cn.sphd.miners.modules.dmmSchedule.service.DmmFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DmmFavoriteServiceImpl implements DmmFavoriteService {

    @Autowired
    DmmFavoriteDao dmmFavoriteDao;
    @Autowired
    DmmFavoriteScheduleDao dmmFavoriteScheduleDao;

    @Override
    public void saveDmmFavorite(DmmFavorite dmmFavorite) {
        dmmFavoriteDao.save(dmmFavorite);
    }

    @Override
    public DmmFavorite getDmmFavoriteById(Integer id) {
        return dmmFavoriteDao.get(id);
    }

    @Override
    public void updateDmmFavorite(DmmFavorite dmmFavorite) {
        dmmFavoriteDao.update(dmmFavorite);
    }

    @Override
    public void deleteDmmFavorite(DmmFavorite dmmFavorite) {
        dmmFavoriteDao.delete(dmmFavorite);
    }

    @Override
    public List<DmmFavorite> getFirstFavorites(Long accId) {
        String hql=" from DmmFavorite where accId=:accId and level=:level";
        Map<String,Object> map=new HashMap<>();
        map.put("accId",accId);
        map.put("level",1);
        List<DmmFavorite> dmmFavoriteList=dmmFavoriteDao.getListByHQLWithNamedParams(hql,map);
        return dmmFavoriteList;
    }

    @Override
    public List<DmmFavorite> getFavoritesByPid(Integer pid) {
        String hql=" from DmmFavorite where parentId=:pid";
        Map<String,Object> map=new HashMap<>();
        map.put("pid",pid);
        List<DmmFavorite> dmmFavoriteList=dmmFavoriteDao.getListByHQLWithNamedParams(hql,map);
        return dmmFavoriteList;
    }

    @Override
    public List<Integer> getFavoriteIdsByPath(String path) {
        String hql="select id from DmmFavorite where path=:path";
        Map<String,Object> map=new HashMap<>();
        map.put("path",path);
        List<Integer> ids=dmmFavoriteDao.getListByHQLWithNamedParams(hql,map);
        return ids;
    }

    @Override
    public Integer getCountsByFavoriteIds(List<Integer> favoriteIds) {
        String hql="select count(0) from DmmFavoriteSchedule where favoriteId in(:favoriteIds)";
        Map<String,Object> map=new HashMap<>();
        map.put("favoriteIds",favoriteIds);
        Long counts= (Long) dmmFavoriteScheduleDao.getByHQLWithNamedParams(hql,map);
        return counts.intValue();
    }

    @Override
    public List<DmmSchedule> getDmmSchedulesByFavoriteId(Long accId, Integer favoriteId) {
        String hql="select m.schedule from DmmFavoriteSchedule m where m.schedule.accId=:accId and m.favoriteId=:favoriteId";
        Map<String,Object> map=new HashMap<>();
        map.put("accId",accId);
        map.put("favoriteId",favoriteId);
        List<DmmSchedule> dmmScheduleList=dmmFavoriteScheduleDao.getListByHQLWithNamedParams(hql,map);
        return dmmScheduleList;
    }

    @Override
    public DmmFavoriteSchedule getDmmFavoriteScheduleByScheduleId(Long accId, Integer oldFavoriteId, Integer scheduleId) {
        String hql=" from DmmFavoriteSchedule m where m.schedule.accId=:accId and m.favoriteId=:oldFavoriteId and m.scheduleId=:scheduleId";
        Map<String,Object> map=new HashMap<>();
        map.put("accId",accId);
        map.put("oldFavoriteId",oldFavoriteId);
        map.put("scheduleId",scheduleId);
        DmmFavoriteSchedule dmmFavoriteSchedule= (DmmFavoriteSchedule) dmmFavoriteScheduleDao.getByHQLWithNamedParams(hql,map);
        return dmmFavoriteSchedule;
    }

    @Override
    public void updateDmmFavoriteSchedule(DmmFavoriteSchedule dmmFavoriteSchedule) {
        dmmFavoriteScheduleDao.update(dmmFavoriteSchedule);
    }

    @Override
    public void saveDmmFavoriteSchedule(DmmFavoriteSchedule dmmFavoriteSchedule) {
        dmmFavoriteScheduleDao.save(dmmFavoriteSchedule);
    }

    @Override
    public void deleteDmmFavoriteSchedule(DmmFavoriteSchedule dmmFavoriteSchedule) {
        dmmFavoriteScheduleDao.delete(dmmFavoriteSchedule);
    }

    @Override
    public void deleteAllDmmFavoriteSchedule(List<DmmFavoriteSchedule> dmmFavoriteScheduleList){
        dmmFavoriteScheduleDao.deleteAll(dmmFavoriteScheduleList);
    }

    @Override
    public List<DmmFavoriteSchedule> getDmmFavoriteSchedulesByScheduleId(Integer scheduleId) {
        String hql=" from DmmFavoriteSchedule where scheduleId=:scheduleId";
        Map<String,Object> map=new HashMap<>();
        map.put("scheduleId",scheduleId);
        List<DmmFavoriteSchedule> dmmFavoriteScheduleList= dmmFavoriteScheduleDao.getListByHQLWithNamedParams(hql,map);
        return dmmFavoriteScheduleList;
    }
}
