package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 装备管理_装备器具
 * 20230209 1.247装备器具新增
 * Service接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public interface ITEquEquipmentService {
    /**
     * 查询装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_装备器具
     *           20230209 1.247装备器具新增
     *           主键
     * @return 装备管理_装备器具
     * 20230209 1.247装备器具新增
     */
    public TEquEquipment selectTEquEquipmentById(Long id);

    /**
     * 查询装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 列表
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @return 装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 集合
     */
    public List<TEquEquipment> selectTEquEquipmentList(TEquEquipment tEquEquipment);

    /**
     * 新增装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquEquipment(TEquEquipment tEquEquipment);

    /**
     * 修改装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @param user
     * @return 结果
     */
    public int updateTEquEquipment(TEquEquipment tEquEquipment, User user);

    /**
     * 批量删除装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_装备器具
     *            20230209 1.247装备器具新增
     *            主键集合
     * @return 结果
     */
    public int deleteTEquEquipmentByIds(String... ids);

    /**
     * 删除装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 信息
     *
     * @param id 装备管理_装备器具
     *           20230209 1.247装备器具新增
     *           主键
     * @return 结果
     */
    public int deleteTEquEquipmentById(Long id);

}
