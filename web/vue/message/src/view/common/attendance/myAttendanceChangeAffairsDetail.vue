<template>
  <div id="myAttendanceChangeAffairsDetail" class="attendanceCon" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container">
      <div class="bg" v-if="params.name === 'leave'">
        <div class="item">
          <div class="item_title">{{detail.userName}}</div>
          <div class="item_content"></div>
        </div>
        <div class="item">
          <div class="item_title">请假记录</div>
          <div class="item_content"></div>
        </div>
        <div v-if="params.source === 1">
          <div v-if="item.approveStatus === '2'" v-for="(item, index) in personnelLeaveItemList" v-bind:key="index">
            <div class="kj-panel">
              <div class="item">
                <div class="item_title">提前结束请假</div>
                <div class="item_content"></div>
              </div>
              <div class="item">
                <div class="item_title">计划上班时间</div>
                <div class="item_content">{{item.actualEndTime | formatDay('yyyy-MM-dd hh:mm')}}</div>
              </div>
              <div class="item">
                <div class="item_title">说明</div>
                <div class="item_content">{{item.actualReason}}</div>
              </div>
            </div>
            <div class="process">
              <div>申请人 <span class="processName">{{item.createName}}</span> {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              <div v-for="(it, index) in item.processList" v-bind:key="index">
                审批人 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}
              </div>
            </div>
          </div>
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">开始时间</div>
              <div class="item_content">{{detail.beginTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">结束时间</div>
              <div class="item_content">{{detail.endTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假类型</div>
              <div class="item_content">{{detail.leaveTypeName}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假事由</div>
              <div class="item_content">{{detail.reason}}</div>
            </div>
          </div>
          <div class="process">
            <div>申请人 <span class="processName">{{detail.createName}}</span> {{detail.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div v-for="(pitem, pindex) in processList" v-bind:key="pindex" >
              审批人 <span class="processName">{{pitem.userName}}</span> {{pitem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}
            </div>
          </div>
          <div v-if="item.approveStatus === '3'" v-for="(item, index) in personnelLeaveItemList" v-bind:key="index">
            <div class="kj-panel">
              <div class="item">
                <div class="item_title">提前结束请假</div>
                <div class="item_content"></div>
              </div>
              <div class="item">
                <div class="item_title">计划上班时间</div>
                <div class="item_content">{{item.actualEndTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
              </div>
              <div class="item">
                <div class="item_title">说明</div>
                <div class="item_content">{{item.actualReason}}</div>
              </div>
            </div>
            <div v-if="is.approveStatus === '3'" v-for="(is, index) in item.processList" v-bind:key="index">
              <div class="item">提前结束请假的申请被{{is.userName}}驳回！</div>
              <div class="item">
                <div class="item_title color-red">驳回理由</div>
                <div class="item_content">{{is.reason}}</div>
              </div>
            </div>
            <div class="process">
              <div>申请人 <span class="processName">{{item.createName}}</span> {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              <div v-for="(it, index) in item.processList" v-bind:key="index">
                审批人 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">开始时间</div>
              <div class="item_content">{{detail.beginTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">结束时间</div>
              <div class="item_content">{{detail.endTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假类型</div>
              <div class="item_content">{{detail.leaveTypeName}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假事由</div>
              <div class="item_content">{{detail.reason}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg" v-if="params.name === 'overTime'">
        <div class="item">
          <div class="item_title">{{detail.userName}}</div>
          <div class="item_content"></div>
        </div>

        <div v-if="params.source === 1">
          <div class="item">
            <div class="item_title">加班所属日期</div>
            <div class="item_content">{{detail.beginTime | formatDay('YYYY-MM-DD')}}</div>
          </div>
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">批准时长</div>
              <div class="item_content">{{detail.approveDuration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">说明</div>
              <div class="item_content">{{detail.approveExplain}}</div>
            </div>
            <div class="processList">
              <div class="process">
                <div v-if="item.approveStatus === '2'" v-for="(item, index) in processList1" v-bind:key="index">
                  <div>审批人 <span class="processName">{{item.userName}}</span> {{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="kj-panel applyDetail">
            <div class="item">
              <div class="item_title">申报时长</div>
              <div class="item_content">{{detail.actualDuration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">申报起止时间</div>
              <div class="item_content">{{detail.actualBeginTime | formatDay('HH:mm')}} - {{detail.actualEndTime | formatDay('HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">加班事由</div>
              <div class="item_content">{{detail.actualReason}}</div>
            </div>
            <div class="processList">
              <div class="process">
                <div>申请人 <span class="processName">{{detail.userName}}</span> {{detail.actualApplyTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
          </div>
          <div class="kj-panel planDetail">
            <div class="item">
              <div class="item_title">计划时长</div>
              <div class="item_content">{{detail.duration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">计划起止时间</div>
              <div class="item_content">{{detail.beginTime | formatDay('HH:mm')}} - {{detail.endTime | formatDay('HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">加班事由</div>
              <div class="item_content">{{detail.reason}}h</div>
            </div>
            <div class="processList">
              <div class="process">
                <div>申请人 <span class="processName">{{detail.userName}}</span> {{detail.actualApplyTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                <div v-if="it.approveStatus === '2'" v-for="(it, index) in processList2" v-bind:key="index">
                  <div>审批人 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="item">
            <div class="item_title">加班所属日期</div>
            <div class="item_content">{{detail.attendanceDate | formatDay('YYYY-MM-DD')}}</div>
          </div>
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">加班时长</div>
              <div class="item_content">{{detail.duration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">加班事由</div>
              <div class="item_content">{{detail.reason}}</div>
            </div>
            <div class="item">
              <div class="item_title">备注</div>
              <div class="item_content">{{detail.memo}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .bg{
    background: #fff;
    padding: 4px 16px;
  }
  .item{
    display: flex;
    padding: 4px 0;
    line-height: 18px;
    .item_title{
      width: 90px;
      flex: none;
      color: #666;
      line-height: 2rem;
      padding: 0;
    }
    .color-red{ color: red!important; }
    .item_content{
      flex: auto;
      line-height: 2rem;
      text-align: left;
    }
  }
  .kj-panel{
    border-top:1px solid #ddd;
  }
  .process{
    font-size: 12px;
    color: #666;
    margin-top: 8px;
    margin-bottom: 8px;
    margin-left: 90px;
    .processName{
      display: inline-block;
      width: 52px;
      margin-left: 4px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
      text-align: left;
    }
  }
</style>
<script>
export default {
  name: 'myAttendanceChangeAffairsDetail',
  data () {
    return {
      params: {},
      title: '',
      loading: false,
      detail: {}, // 请假或加班详情
      processList: [], // 请假审批流程
      personnelLeaveItemList: [], // 提前结束请假流程
      processList1: [], // 计划加班审批流程
      processList2: [] // 申报加班和批准加班的审批流程
    }
  },
  created: function () {
    let params = this.$store.getters.getAttendanceLeaveParam
    console.log('params', params)
    this.params = params
    this.title = params.title
    if (params.name === 'leave') {
      if (params.changeType === 'before') {
        this.getLeaveDetail()
      } else {
        this.detail = params.info
      }
    } else if (params.name === 'overTime') {
      if (params.changeType === 'before') {
        this.getOverTimeDetail()
      } else {
        this.detail = params.info
      }
    }
  },
  methods: {
    getLeaveDetail: function () {
      this.loading = true
      let that = this
      this.$http.post('../../../workAttendance/getLeaveDetail.do', {
        leaveId: this.params.id, // 请假详情id
        historyType: 1,
        source: this.params.source // 来源 1-审批 2-录入 source=1时，说明此请假来源为审批进入考勤系统的，那么leaveId为business字段值。 source=2时，说明此请假来源为考勤系统的录入，那么leaveId取详情id的字段值
      }, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data
        let detail = reqData.personnelLeave // 计划请假详情
        let processList = reqData.processList1 // 计划请假的审批流程
        let personnelLeaveItemList = reqData.personnelLeaveItems // 提前结束请假详情

        if (that.params.source === 1) {
          that.detail = detail
          that.processList = processList
          that.personnelLeaveItemList = personnelLeaveItemList
        } else {
          that.detail = reqData.personnelAttendanceUserDetail
        }

        that.loading = false
      }).catch(function () {
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    getOverTimeDetail: function () {
      this.loading = true
      let that = this
      this.$http.post('../../../workAttendance/getOverTimeDetail.do', {
        overTimeId: this.params.id, // 加班详情id
        historyType: 1,
        source: this.params.source // 来源 1-审批 2-录入 source=1时，说明此请假来源为审批进入考勤系统的，那么leaveId为business字段值。 source=2时，说明此请假来源为考勤系统的录入，那么leaveId取详情id的字段值
      }, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data
        let detail = reqData.personnelOvertime // 加班详情
        let processList1 = reqData.approvalProcess1 // 计划加班审批流程
        let processList2 = reqData.approvalProcess2 // 申报加班和批准加班的审批流程

        if (that.params.source === 1) {
          that.detail = detail
          that.processList1 = processList1
          that.processList2 = processList2
        } else {
          that.detail = reqData.personnelAttendanceUserDetail
        }

        that.loading = false
      }).catch(function () {
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    }
  }
}
</script>
