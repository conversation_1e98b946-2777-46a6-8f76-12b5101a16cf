package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.personal.entity.PersonnelFolks;
import cn.sphd.miners.modules.system.entity.Offer;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * Created by Administrator on 2016/8/17.
 */
public interface PersonalService  {

    void savePersonnelOccupation(PersonnelOccupation personnelOccupation);

    void updatePersonnelOccupation(PersonnelOccupation personnelOccupation);

    PersonnelOccupation getPersonnelOccupationById(Integer id);

    List<PersonnelOccupation> getPersonnelOccupationByUserId(Integer userId);

    void  deletePersonnelOccupation(PersonnelOccupation personnelOccupation);

    PersonalEducation getPersonalEducationById(Integer id);

    List<PersonalEducation> getPersonalEducationByUserId(Integer id);

    void savePersonalEducation(PersonalEducation personalEducation);

    void updatePersonalEducation(PersonalEducation personalEducation);

    void deletePersonalEducation(PersonalEducation personalEducation);

    PersonnelSalaryLog getPersonnelSalaryLogById(Integer id);

    List<PersonnelSalaryLog> getPersonnelSalaryLogByUserId(Integer id);

    void savePersonnelSalaryLog(PersonnelSalaryLog personnelSalaryLog);

    void updatePersonnelSalaryLog(PersonnelSalaryLog personnelSalaryLog);

    void deletePersonnelSalaryLog(PersonnelSalaryLog personnelSalaryLog);

    PersonalRewardPunishment getPersonalRewardPunishmentById(Integer id);

    List<PersonalRewardPunishment> getPersonalRewardPunishmentByUserId(Integer id);

    void savePersonalRewardPunishment(PersonalRewardPunishment personalRewardPunishment);

    void updatePersonalRewardPunishment(PersonalRewardPunishment personalRewardPunishment);

    void deletePersonalRewardPunishment(PersonalRewardPunishment personalRewardPunishment);

    PersonalAssessment getPersonalAssessmentById(Integer id);

    List<PersonalAssessment> getPersonalAssessmentByUserId(Integer id);

    void savePersonalAssessment(PersonalAssessment personalAssessment);

    void updatePersonalAssessment(PersonalAssessment personalAssessment);

    void deletePersonalAssessment(PersonalAssessment personalAssessment);

    List<PersonnelFolks> getFolksByUserId(Integer userId);

    void savePersonnelFolks(PersonnelFolks personnelFolks);

    PersonnelFolks getFolkById(Integer id);

    void updatePersonnelFolks(PersonnelFolks personnelFolks);

    void deletePersonnelFolks(PersonnelFolks personnelFolks);

    void addOrUpdatePersonnelOccupation(User user,User loginUser, Integer id, String beginTime, String endTime, String corpName, String corpSize, String corpNature, String corpDepartment, String post, String jobDesc, String memo);

    void addPersonnelOccupationHistory(PersonnelOccupation personnelOccupation,boolean check);

    void addOrUpdatePersonalEducation(User user,User loginUser,Integer id,String beginTime,String endTime,String collegeName,String departmentName,String major,String degreeDesc,String majorDesc,String memo);

    void addPersonnelEducationHistory(PersonalEducation personalEducation,boolean check);


    List<PersonnelOccupationHistory> getPersonnelOccupationHistoriesById(Integer occupationId, PageInfo pageInfo);

    List<PersonnelEducationHistory> getPersonnelEducationHistoriesById(Integer educationId,PageInfo pageInfo);

    PersonnelOccupationHistory getPersonnelOccupationHistoryById(Integer id);

    PersonnelEducationHistory getPersonnelEducationHistoryById(Integer id);

    List<UserContact> getUserContactListByUserId(Integer userId);

    void  offerRejectSend(int loginNum, int operate, Offer offer, Integer toUserId, String pass, String title, String content, String tCode);
}
