package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxReportDao;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTaxReportDaoImpl extends BaseDao<AcctTaxReportEntity, Serializable> implements AcctTaxReportDao {

    @Autowired
    AcctTaxReportDao taxReportDao;

    /**
     * 根据历史报表ID获取历史税种列表
     * */
    @Override
    public List<AcctTaxHistoryEntity> getTaxByReportId(Integer reportId,int year,int month) {
//        String hql = "select tax from AcctTaxEntity tax,AcctTaxReportEntity taxReport where tax.id=taxReport.tax and taxReport.report=:id";
        String period = "" + year + "-" + String.format("%02d",month);
        String hql = "select th from " +
                "AcctTaxHistoryEntity th,AcctTaxDetailEntity td " +
                "where " +
                "th.id=td.taxHistory and " +
                "td.report=:reportId";
        String where = " and td.period=:period";
        HashMap<String, Object> param = new HashMap<>();
        param.put("reportId", reportId);
        param.put("period", period);
        List<AcctTaxHistoryEntity> list = taxReportDao.getListByHQLWithNamedParams(hql + where,param);
        if (list.size() == 0) {
            param.remove("period");
            list = taxReportDao.getListByHQLWithNamedParams(hql,param);
        }
        return list;
    }

    @Override
    public AcctTaxReportEntity getByTaxAndReport(int taxId, int reportId) {
        String hql = "from AcctTaxReportEntity where tax=:tax and report=:report";
        HashMap<String, Object> param = new HashMap<>();
        param.put("tax", taxId);
        param.put("report", reportId);
        List<AcctTaxReportEntity> list = taxReportDao.getListByHQLWithNamedParams(hql,param);
        if (list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<AcctTaxReportEntity> getTaxReportByTaxId(Integer oid, Integer id) {
        String hql = "from AcctTaxReportEntity where org=:org and tax=:tax";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", oid);
        param.put("tax", id);
        List<AcctTaxReportEntity> list = taxReportDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }


}
