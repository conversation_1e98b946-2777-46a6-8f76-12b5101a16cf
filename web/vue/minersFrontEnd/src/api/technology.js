import {request} from "@/utils/request";

/*creator:lyt 2024/5/7 08:44
* 包装管理
*已设置的商品
*  */
export function getPackList (data) {
    data = data || {}
    return request({
        url: '/packing/getPackagingList.do',
        data: data
    })
}
/*creator:lyt 2024/5/7 08:44
* 包装管理
*已设置的商品
*  */
export function getPackagingDetail (data) {
    data = data || {}
    return request({
        url: '/packing/getPackagingDetail.do',
        data: data
    })
}



/*creator:lyt 2024/5/7  9:39
* 待设置包装信息的商品
* */
export function getWszNum (data) {
    data = data || {}
    return request({
        url: '/packing/getWszNum.do',
        data: data
    })
}
/*creator:lyt 2024/5/7  9:39
* 去设置
* */
export function getUnpackList (data) {
    data = data || {}
    return request({
        url: '/packing/getWszPackagingList.do',
        data: data
    })
}
/*creator:lyt 2024/5/08  08:39
* 去设置
* */
export function setPackingFinish (data) {
    data = data || {}
    return request({
        url: '/packing/submitSettings.do',
        data: data
    })
}
/*creator:lyt 2024/5/08  10:11
* 增加包装物-确定
* */
export function addPackageDone (data) {
    data = data || {}
    return request({
        url: '/packing/addBzw.do',
        data: data
    })
}
/*creator:lyt 2024/5/08  10:30
* 获取包装物
* */
export function getPackageList (data) {
    data = data || {}
    return request({
        url: '/packing/getBzwList.do',
        data: data
    })
}

/*creator:lyt 2024/5/23  08:30
* 选择无需包装提交
* */
export function noNeedPackingOk (data) {
    data = data || {}
    return request({
        url: '/packing/addPackaging.do',
        data: data
    })
}
/*creator:lyt 2024/5/23  09:20
* 选择无需包装提交
* */
export function outerBaseSure (data, url) {
    data = data || {}
    return request({
        url: url,
        data: data
    })
}

/*creator:lyt 2024/5/23  10:02
* 选择无需包装提交
* */
export function updatePackingTipOk (data, url) {
    data = data || {}
    return request({
        url: url,
        data: data
    })
}
/*creator:lyt 2024/5/23  10:20
* 操作记录
* */
export function getRecord (data) {
    data = data || {}
    return request({
        url: '../packing/getPackagingRecordList.do',
        data: data
    })
}
/*creator:lyt 2024/5/23  10:54
* 操作记录-查看
* */
export function getRecordDetail (data) {
    data = data || {}
    return request({
        url: '../packing/getPackagingRecordDetail.do',
        data: data
    })
}

//





















/*creator:lyt 2024/5/10  08:05
* 获取计量单位列表
* */
export function getUnitList (data) {
    data = data || {}
    return request({
        url: '/unit/selectUnitListForModule.do',
        data: data
    })
}
/*creator:lyt 2024/5/27  08:05
* 新增计量单位
* */
export function addUnitOk (data) {
    data = data || {}
    return request({
        url: '../unit/addUnit.do',
        data: data
    })
}