package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/5/17 10:55
 */

@Entity
@Table(name = "t_mt_out_application")
public class MtOutApplication {
    private Integer id;
    private Integer org;
    private String instanceChain;
    private Integer applicant;
    private String applicantName;
    private Date applyTime;
    private String applyType;
    private String sn;
    private Date timePlan;
    private Long durarionPlan;
    private Date timeFact;
    private Long durarionFact;
    private String purpose;
    private Long itemPlan;
    private BigDecimal quantityPlan;
    private BigDecimal quantityFact;
    private Date deliveryFact;
    private String approvalStatus;
    private String state;
    private String memo;
    private String description;
    private String operation;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private Integer previousId;
    private Integer versionNo;
    //仓库id
    private Integer materialWarehouse;
    //仓库名称
    @Transient
    private String warehouseName;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "instance_chain")
    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    @Basic
    @Column(name = "applicant")
    public Integer getApplicant() {
        return applicant;
    }

    public void setApplicant(Integer applicant) {
        this.applicant = applicant;
    }

    @Basic
    @Column(name = "applicant_name")
    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    @Basic
    @Column(name = "apply_time")
    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    @Basic
    @Column(name = "apply_type")
    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    @Basic
    @Column(name = "sn")
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }



    @Basic
    @Column(name = "item_plan")
    public Long getItemPlan() {
        return itemPlan;
    }

    public void setItemPlan(Long itemPlan) {
        this.itemPlan = itemPlan;
    }

    @Basic
    @Column(name = "quantity_plan")
    public BigDecimal getQuantityPlan() {
        return quantityPlan;
    }

    public void setQuantityPlan(BigDecimal quantityPlan) {
        this.quantityPlan = quantityPlan;
    }

    @Basic
    @Column(name = "quantity_fact")
    public BigDecimal getQuantityFact() {
        return quantityFact;
    }

    public void setQuantityFact(BigDecimal quantityFact) {
        this.quantityFact = quantityFact;
    }

    @Basic
    @Column(name = "delivery_fact")
    public Date getDeliveryFact() {
        return deliveryFact;
    }

    public void setDeliveryFact(Date deliveryFact) {
        this.deliveryFact = deliveryFact;
    }

    @Basic
    @Column(name = "approval_status")
    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    @Basic
    @Column(name = "state")
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Basic
    @Column(name = "time_plan")
    public Date getTimePlan() {
        return timePlan;
    }

    public void setTimePlan(Date timePlan) {
        this.timePlan = timePlan;
    }
    @Basic
    @Column(name = "durarion_plan")
    public Long getDurarionPlan() {
        return durarionPlan;
    }

    public void setDurarionPlan(Long durarionPlan) {
        this.durarionPlan = durarionPlan;
    }

    @Basic
    @Column(name = "time_fact")
    public Date getTimeFact() {
        return timeFact;
    }

    public void setTimeFact(Date timeFact) {
        this.timeFact = timeFact;
    }
    @Basic
    @Column(name = "durarion_fact")
    public Long getDurarionFact() {
        return durarionFact;
    }

    public void setDurarionFact(Long durarionFact) {
        this.durarionFact = durarionFact;
    }
    @Basic
    @Column(name = "purpose")
    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    @Basic
    @Column(name = "material_warehouse")
    public Integer getMaterialWarehouse() {
        return materialWarehouse;
    }

    public void setMaterialWarehouse(Integer materialWarehouse) {
        this.materialWarehouse = materialWarehouse;
    }
    @Transient
    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
}
