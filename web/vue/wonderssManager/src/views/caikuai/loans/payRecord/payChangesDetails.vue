<template>
  <!-- 付款记录的修改记录详情</p>-->
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true @mycloseFun="handleCustomClose" :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14 " v-if="payChangesDetails.itemIndex>0">
      <p>以下为第{{ payChangesDetails.itemIndex}}次修改后数据，其中本次修改的被标为了<span style="color:#ed5565;">红色</span></p>
      <p>{{ payChangesDetails.createName }}<span class="padding-0-10">{{ new
      Date(payChangesDetails.createDate).format('yyyy-MM-dd  hh:mm:ss') }}</span>修改</p>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14" v-else>
      <p>以下为原始信息</p>
      <p>{{ payChangesDetails.createName }}<span class="padding-0-10">{{ new
      Date(payChangesDetails.createDate).format('yyyy-MM-dd  hh:mm:ss') }}</span>创建</p>
    </div>
    <div class="padding-0-15 ty-fff margin_top_16 re" v-if="info && payChangesDetails">
      <p>
        <span>付款金额</span>
        <span v-html="info.repaymentAmount"></span>
      </p>
      <p>
        <span>付款日期</span>
        <span v-html="info.repaymentTime"></span>
      </p>
      <p>
        <span>支付方式</span>
        <span v-html="info.repaymentMethod"></span>
      </p>
      <div class="end-text" v-html="info.withinOrAbroad" v-if="repaymentMethod == 3">
      </div>
      <p v-if="repaymentMethod == 5">
        <span>转账银行</span>
        <span v-html="info.receiveBank"></span>
      </p>
      <div v-if="repaymentMethod == '4' || repaymentMethod == '3'">
        <p v-if="withinOrAbroad == 1">
          <span>银行账户</span>
          <span style="width: 70%;text-align: end" v-html="info.account"></span>
        </p>
        <p>
          <span>{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票号</span>
          <span v-html="info.returnNo"></span>
        </p>
        <p v-if="withinOrAbroad == 0 || repaymentMethod == 4">
          <span>票据金额</span>
          <span v-html="info.repaymentAmount"></span>
        </p>
        <p v-if="withinOrAbroad == 0 || repaymentMethod == 4">
          <span>收到{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票的日期</span>
          <span v-html="info.receiveDate"></span>
        </p>
        <p>
          <span>{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票到期日</span>
          <span v-html="info.expireDate"></span>
        </p>
        <p v-if="withinOrAbroad == 0 || repaymentMethod == 4">
          <span>付款单位</span>
          <span v-html="info.payer"></span>
        </p>
        <p v-if="withinOrAbroad == 0 || repaymentMethod == 4">
          <span>出具{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票银行</span>
          <span style="width: 70%;text-align: end" v-html="info.bankName"></span>
        </p>
        <p v-if="withinOrAbroad == 0 || repaymentMethod == 4">
          <span>{{repaymentMethod == 4 ? '原始出具汇票' : repaymentMethod == 3 ? '出具支票' : 'err'}}单位</span>
          <span style="width: 60%;text-align: end" v-html="info.originalCorp"></span>
        </p>
      </div>
      <div v-if="withinOrAbroad == 1 && repaymentMethod == 3 ">
        <p>
          <span>接收日期</span>
          <span v-html="info.receiveDate"></span>
        </p>
        <p>
          <span>接收经手人</span>
          <span v-html="info.receiver"></span>
        </p>
        <p>
          <span>支付经手人</span>
          <span v-html="info.operator"></span>
        </p>
      </div>
    </div>

  </div>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "payChangesDetails",
  mixins: [finance],
  data() {
    return {
      tit: "财会",
      payChangesDetails: {},
      info: null,
      repaymentMethod: '',
      withinOrAbroad: '',
    }
  },
  computed: {},
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    compareWithUnit(before, after, unit = '') {
      const formattedBefore = this.formatCurrency(before) + unit
      const formattedAfter = this.formatCurrency(after) + unit
      return this.compareTxt(formattedBefore, formattedAfter)
    },
    getOrdRecordModHistoryDetail(id) {
      let params = {id: id}
      this.financeApi.ordRecordModHistoryDetail(params).then(res1 => {
        let after = res1.data.data.mapParam
        let before = res1.data.data.mapBefore
        console.log('after', after)
        console.log('before', before)
        let newInfo = {}
        if (Object.keys(before).length > 0) {
          let afterWithinOrAbroad = (after.withinOrAbroad) == 0 ? '外部转账支票' : (after.withinOrAbroad) == 1 ? '内部转账支票' : 'err';
          let beforeWithinOrAbroad = (before.withinOrAbroad) == 0 ? '外部转账支票' : (before.withinOrAbroad) == 1 ? '内部转账支票' : 'err';
          newInfo = {
            repaymentAmount: this.compareWithUnit(before.repaymentAmount, after.repaymentAmount, '元'),
            repaymentTime: this.compareTxt((new Date(before.repaymentTime).format('yyyy-MM-dd')), (new Date(after.repaymentTime).format('yyyy-MM-dd'))),
            repaymentMethod: this.chargePayMethod(after.repaymentMethod),
            withinOrAbroad: this.compareTxt(beforeWithinOrAbroad, afterWithinOrAbroad),
            receiveBank: this.compareTxt(before.receiveBank, after.receiveBank),
            returnNo: this.compareTxt(before.returnNo, after.returnNo),
            receiveDate: this.compareTxt((new Date(before.receiveDate).format('yyyy-MM-dd')), (new Date(after.receiveDate).format('yyyy-MM-dd'))),
            expireDate: this.compareTxt((new Date(before.expireDate).format('yyyy-MM-dd')), (new Date(after.expireDate).format('yyyy-MM-dd'))),
            payer: this.compareTxt(before.payer, after.payer),
            bankName: this.compareTxt(before.bankName, after.bankName),
            originalCorp: this.compareTxt(before.originalCorp, after.originalCorp),
            receiver: this.compareTxt(before.receiver, after.receiver),
            operator: this.compareTxt(before.operator, after.operator),
            account:this.compareTxt(before.account, after.account),
          }
        } else {

          let withinOrAbroadValue = (after.withinOrAbroad) == 0 ? '外部转账支票' : (after.withinOrAbroad) == 1 ? '内部转账支票' : 'err';
          newInfo = {
            repaymentAmount: `${this.formatCurrency(after.repaymentAmount)}元`,
            repaymentTime: new Date(after.repaymentTime).format('yyyy-MM-dd'),
            repaymentMethod: this.chargePayMethod(after.repaymentMethod),
            withinOrAbroad: withinOrAbroadValue,
            receiveBank: after.receiveBank,
            returnNo: after.returnNo,
            receiveDate: new Date(after.receiveDate).format('yyyy-MM-dd'),
            expireDate: new Date(after.expireDate).format('yyyy-MM-dd'),
            payer: after.payer,
            bankName: after.bankName,
            originalCorp: after.originalCorp,
            receiver: after.receiver,
            operator: after.operator,
            account: after.account,

          }
        }
        this.info = newInfo
        this.repaymentMethod = after.repaymentMethod
        this.withinOrAbroad = after.withinOrAbroad
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('getOrdRecordModHistoryDetail链接失败，请重试！')
      })
    }
  },
  mounted() {
    let payChangesDetails = localStorage.getItem('payChangesDetails')
    this.payChangesDetails = JSON.parse(payChangesDetails)
    console.log('payChangesDetails=', this.payChangesDetails)
    this.getOrdRecordModHistoryDetail(this.payChangesDetails.id)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";


.re {
  position: relative;
  top: 40px;
  line-height: 30px;

  > p {
    display: flex;
    justify-content: space-between;
    padding: 2px 0;
  }

  > div {
    > p {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
    }
  }
}

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.end-text {
  font-size: 14px;
}
</style>