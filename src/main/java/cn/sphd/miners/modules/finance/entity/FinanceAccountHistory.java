package cn.sphd.miners.modules.finance.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by 赵应 on 2016-07-12.
 */
@Entity
@Table(name = "t_finance_account_history")
public class FinanceAccountHistory  implements Serializable{
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;

    @Column(name="account_type"   , nullable=true , unique=false)
    private Integer accountType;

    @Column(name="account"  , length=100 , nullable=true , unique=false)
    private String account;

    @Column(name="sub_account"  , length=100 , nullable=true , unique=false)
    private String subAccount;

    @Column(name="previous_balance"   , nullable=true , unique=false)
    private BigDecimal previousBalance;   //上期余额

    @Column(name="carried_forward_date"   , nullable=true , unique=false)
    private Date carriedForwardDate;

    @Column(name="balance"   , nullable=true , unique=false)
    private BigDecimal balance;  //本期余额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;

    @Column(name="credit"   , nullable=true , unique=false)
    private BigDecimal credit;

    @Column(name="debit"   , nullable=true , unique=false)
    private BigDecimal debit;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;

    @Column(name="operation"  , length=100 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="account_status", nullable=true , unique=false)
    private Integer accountStatus;//账户状态 0:冻结 1：正常

    @Column(name = "bank_name", nullable=true , unique=false)
    private String bankName;//开户行

    @Column(name = "name", nullable=true , unique=false)
    private String name;//户主名

    @Column(name = "illustrate", length=255 , nullable=true , unique=false)
    private String illustrate;//操作说明

    @Column(name = "initial_amount" , nullable=true , unique=false)
    private BigDecimal initialAmount;//初始金额

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;   //版本号,每次修改+1

    @Column(name = "subject", length = 16,nullable=true , unique=false)
    private String subject; //对应会计科目

    @Column(name="is_public"  , length=10 , nullable=true , unique=false)
    private String isPublic;   //是否为对公帐号:1-是 0-否

    @Column(name = "cashable",length=1, nullable=true , unique=false)
    private Integer cashable; //是否可取现  1：可取现  2：不可取现

    @Column(name = "is_basic",nullable=true , unique=false)
    private  Integer isBasic;//是否是 基本户 true-是，false-否

    @JsonIgnore
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="account_id", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceAccount accountId;

    @Column(name = "handle_reply" , length=255 , nullable=true , unique=false)
    private String handleReply;//处理回复

    @Column(name="account_id"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer accountId_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getSubAccount() {
        return subAccount;
    }

    public void setSubAccount(String subAccount) {
        this.subAccount = subAccount;
    }

    public BigDecimal getPreviousBalance() {
        return previousBalance;
    }

    public void setPreviousBalance(BigDecimal previousBalance) {
        this.previousBalance = previousBalance;
    }

    public Date getCarriedForwardDate() {
        return carriedForwardDate;
    }

    public void setCarriedForwardDate(Date carriedForwardDate) {
        this.carriedForwardDate = carriedForwardDate;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public FinanceAccount getAccountId() {
        return accountId;
    }

    public void setAccountId(FinanceAccount accountId) {
        this.accountId = accountId;
    }

    public Integer getAccountId_() {
        return accountId_;
    }

    public void setAccountId_(Integer accountId_) {
        this.accountId_ = accountId_;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIllustrate() {
        return illustrate;
    }

    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public String getHandleReply() {
        return handleReply;
    }

    public void setHandleReply(String handleReply) {
        this.handleReply = handleReply;
    }

    public BigDecimal getInitialAmount() {
        return initialAmount;
    }

    public void setInitialAmount(BigDecimal initialAmount) {
        this.initialAmount = initialAmount;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(String isPublic) {
        this.isPublic = isPublic;
    }

    public Integer getCashable() {
        return cashable;
    }

    public void setCashable(Integer cashable) {
        this.cashable = cashable;
    }

    public Integer getIsBasic() {
        return isBasic;
    }

    public void setIsBasic(Integer isBasic) {
        this.isBasic = isBasic;
    }
}
