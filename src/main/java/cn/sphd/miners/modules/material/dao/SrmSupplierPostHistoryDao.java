package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierImage;
import cn.sphd.miners.modules.material.entity.SrmSupplierImageHistory;
import cn.sphd.miners.modules.material.entity.SrmSupplierPost;
import cn.sphd.miners.modules.material.entity.SrmSupplierPostHistory;

import java.io.Serializable;

public interface SrmSupplierPostHistoryDao extends IBaseDao<SrmSupplierPostHistory, Serializable> {
    SrmSupplierPostHistory insert(SrmSupplierPost srmSupplierPost);
}
