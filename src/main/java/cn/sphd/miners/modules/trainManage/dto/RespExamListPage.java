package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.trainManage.entity.TrainingExam;

import java.util.Date;
import java.util.List;

/**
 * @ClassName RespExamListPage
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/20 9:43
 * @Version 1.0
 */
public class RespExamListPage {
    private List<TrainingExam> trainingExamList;
    private PageInfo pageInfo;
    private Date startTime;
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<TrainingExam> getTrainingExamList() {
        return trainingExamList;
    }

    public void setTrainingExamList(List<TrainingExam> trainingExamList) {
        this.trainingExamList = trainingExamList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
}
