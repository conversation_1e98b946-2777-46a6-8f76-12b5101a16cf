package cn.sphd.miners.modules.dailyAffairs.service;

import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2018/10/30.
 */
public interface UserSuspendMsgService extends BadgeNumberCallback {

    void  saveUserSuspendMsg(UserSuspendMsg userSuspendMsg);

    void  updateUserSuspendMsg(String state,Integer toUserId ,Integer... messageIds);

    UserSuspendMsg saveUserSuspendMsg(Integer superscript, String content, String memo, Integer toUserId,String type,Integer business);//简易新增提示消息

    UserSuspendMsg saveUserSuspendMsg(Integer superscript,String title, String content, String memo, Integer toUserId,String type,Integer business);//简易新增提示消息

    void  userSuspendMsgRejectSend(Integer superscript, Integer toUserId, ArrayList<UserSuspendMsg> userSuspendMsg, ArrayList<Map<String,Integer>> mapArrayList, String pass, String title, String content, User user);

    List<UserSuspendMsg> getUserSuspendMsgListByUserId(Integer userId);

    Integer getUserSuspendMsgCounts(Integer userId);

    UserSuspendMsg getMsgById(Integer messageId);

    UserSuspendMsg saveUserSuspendMsg(Integer superscript,String title, String content, String memo, Integer toUserId,String type,Integer business,String callbackClass,String callbackParams);//简易新增提示消息

    void deleteUserSuspendMsg(User user,String callbackClass,List<String> callbackParams); //逻辑删除

    List<UserSuspendMsg> getWillDisappearMessages(Integer userId);

    void updateUserSuspendMsg(UserSuspendMsg userSuspendMsg);

    List<UserSuspendMsg>  getByContentAndToday(String content,Integer userId);
}
