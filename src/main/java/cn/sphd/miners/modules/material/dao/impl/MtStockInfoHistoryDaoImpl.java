package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtStockInfoHistoryDao;
import cn.sphd.miners.modules.material.entity.MtStockInfoHistory;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2018-01-20.
 */
@Repository
public class MtStockInfoHistoryDaoImpl extends BaseDao<MtStockInfoHistory,Serializable> implements MtStockInfoHistoryDao {

    @Override
    public List<Map> getMtSIHListByStockInfoIdForMap(String sql) {
        return  super.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }
}
