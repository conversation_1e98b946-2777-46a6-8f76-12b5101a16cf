<template>
  <div class="home" v-loading="loading" >
    <el-row :gutter="0">
      <el-col :span="6">
        <div class="directory">
          <div class="dicTtl">
            <span class="hh1"><el-icon style="position: relative; top:3px; "><Menu /></el-icon>我的节目单</span>
            <span class="fRight">
              <el-button type="text" class="bigLink" @click="goCereate">
                <el-icon><document-add /></el-icon>
                创建</el-button>
            </span>
          </div>

          <div class="docList">
            <div class="catItem" :class="editObj.id == item.id ? 'active' :'' " v-for="(item , idex) in dircList" :key="idex">
              <div class="dicTtl2" v-if="item.name == '全部'" @click="getLitSongs(item)" >
                <span>全部（{{ item.songNum || 0 }}首）</span>
              </div>
              <div @click="getLitSongs(item)" v-else>
                <span class="direName">
                  <span class="mLit"></span>
                  {{ item.name }}（{{ item.songNum || 0 }}首）
                </span>
                <span class="ctrls fRight">
                  <el-button type="text" @click.stop="editLit(item, idex)"><el-icon><Edit /></el-icon>修改</el-button>
                  <el-button type="text" class="redLink" @click.stop="delLit(item, idex)"><el-icon><Delete /></el-icon>删除</el-button>
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="files">
          <div class="hed">
            <el-row :gutter="20" >
              <el-col :span="10">
                <div class="fonL">
                  <span class="songNum"> <span class="mLit"></span> {{ catInfo }}</span>
                </div>
              </el-col>
              <el-col :span="4" ><span class="linkBtn"><el-button type="text" @click="addSongBtn">新增歌曲</el-button> </span></el-col>
              <el-col :span="10" >
                <span class="searchC">
                   <el-input placeholder="请输入歌曲名称中的关键字" v-model="input3" class="input-with-select">
                      <template #prepend>
                        <span>查找</span>
                      </template>
                      <template #append>
                        <span>确定</span>
                        <el-button icon="el-icon-search" @click="searchBtn"></el-button>
                      </template>
                   </el-input>
                </span>
              </el-col>
            </el-row>
          </div>
          <div class="tabCC">
            <el-table :data="tableData" border :header-cell-style="tableHeaderColor" :cell-style="celStyle">
              <el-table-column fixed prop="name" label="歌曲名称" width="250">
              </el-table-column>
              <el-table-column prop="songDuration" label="时长" > </el-table-column>
              <el-table-column prop="havBgmPath" label="伴奏">
              </el-table-column>
              <el-table-column prop="havOriginalPath" label="原唱" > </el-table-column>
              <el-table-column prop="havCaptionRequired" label="歌词"> </el-table-column>
              <el-table-column fixed="right" label="操作" width="250">
                <template #default="scope">

<!--                  <a :href="rootUrl + '?accID='+ '1' + accID +'&songId=' + scope.row.id" target="_blank">播放伴奏</a>-->
<!--                  <a :href="rootUrl + '?accID='+ '1' + accID +'&songId=' + scope.row.id" target="_blank">播放原唱</a>-->

                  <el-button @click="playLyc(scope.row , 1)" type="text" size="small">播放伴奏</el-button>
                  <el-button @click="playLyc(scope.row, 2)"  type="text" size="small">播放原唱</el-button>
                  <el-button v-if="scope.row.id" type="text" class="redLink" size="small" @click="delSong(scope.row)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="pageYec">
              <el-pagination class="pageYe" background layout=" pager" :currentPage="currentPage" :page-count="pageInfo.totalPage" @current-change="handleCurrentChange">
              </el-pagination>
            </div>

          </div>
        </div>
      </el-col>
    </el-row>

    <el-dialog title="!提示" v-model="dialogVisible" width="30%">
      <div>
        确定删除这个节目单吗？
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="delLitOk">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="!提示" v-model="dialogVisible2" width="30%">
      <div>
        确定移走这首歌码？
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible2 = false">取 消</el-button>
          <el-button type="primary" @click="delSongOk">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog :title="playTtl" v-model="dialogVisible3" :before-close="handleClose" width="50%">
     <div style="margin-bottom: 50px;">
       <div class="AudioCCC">
<!--         <video id='video' controls preload style='width:80%;height:500px;' crossorigin='anonymous' playsinline='' webkit-playsinline='' x5-video-player-type='h5' >-->
<!--           <source src="EasonChan-LonelyWarrior/EasonChan-LonelyWarrior.mp3">-->
<!--           <track id='track' src='EasonChan-LonelyWarrior/EasonChan-LonelyWarrior.vtt' srclang='zh-cn' label='简体中文' kind='subtitles' default >-->
<!--         </video>-->
         <video id='video' ref="video" controls preload='metadata' style='width:100%;height:10em;' crossorigin='anonymous' playsinline=''
                controlslist='nodownload nofullscreen noremoteplayback noplaybackrate' disablepictureinpicture webkit-playsinline='' x5-video-player-type='h5'
                @play='playLiveTime($event)' @playing='playLiveTime($event)' @pause='stopLiveTime($event)' @waiting='stopLiveTime($event)' @seeked='seekLiveTime($event)'
                :src="playUrl">
           <track id='track' ref="track" :src='rhythmSrc' srclang='zh-cn' label='简体中文' kind='subtitles' default >
         </video>
       </div>
     </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleClose">退 出</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog title="新增歌曲" v-model="addSongVisible"  width="30%" class="addSongFrmC">
      <div>
        <el-form label-position="top" label-width="200px" :rules="addRules" :model="addSongFrm">
          <el-form-item label="歌曲名称" prop="name">
            <el-input v-model="addSongFrm.name"></el-input>
          </el-form-item>
          <el-form-item label="直播演唱本曲目时，是否需要加字幕？">
            <el-radio-group v-model="addSongFrm.addTxt" disabled>
              <el-radio :label="1">需要</el-radio>
              <el-radio :label="0">不需要</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="原唱" class="upCC">
            <el-upload v-if="uploadBtn1_txt != '正在上传...'"
                class="avatar-uploader"
                :headers="uploadHeaders"
                :action="uploadAction"
                ref="upload"
                v-model="imgPath" :limit="fileLimit"
                :accept="uploadLyc" :data="postData"
                :multiple="false" :show-file-list="false"
                :on-remove="uploadRemove"
                :on-success="handleSuccess"
                :before-upload="beforeUpload"
            >
              <el-button type="text" size="small">{{ uploadBtn1_txt }}</el-button>
            </el-upload>

            <el-button v-else class="avatar-uploader" type="text" size="small">{{ uploadBtn1_txt }}</el-button>
            <span class="inpuk" >{{ addSongFrm.orlLyc.durationTime2 ? addSongFrm.orlLyc.filename + '（'+ addSongFrm.orlLyc.durationTime2 +'）' : '请上传' }}</span>

          </el-form-item>

          <el-form-item label="伴唱" class="upCC">
            <el-upload v-if="uploadBtn2_txt != '正在上传...'"
                       class="avatar-uploader"
                       :headers="uploadHeaders"
                       :action="uploadAction"
                       ref="upload2" :limit="fileLimit"
                       :accept="uploadLyc" :data="postData"
                       :multiple="false" :show-file-list="false"
                       :on-remove="uploadRemove"
                       :on-success="handleSuccess2"
                       :before-upload="beforeUpload2"
            >
              <el-button type="text" size="small">{{ uploadBtn2_txt }}</el-button>
            </el-upload>
            <el-button v-else class="avatar-uploader" type="text" size="small">{{ uploadBtn2_txt }}</el-button>
            <span class="inpuk" >{{ addSongFrm.banLyc.durationTime2 ? addSongFrm.banLyc.filename + '（'+ addSongFrm.banLyc.durationTime2 +'）' : '请上传' }}</span>

          </el-form-item>


        </el-form>

      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addSongVisible = false">取 消</el-button>
          <el-button type="primary" @click="addSongOk">确 定</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>
<script>
import auth from '@/sys/auth.js'
import { getRootPathFile, minePlayList, delPlayList, getPlayListMes, getMoreSongs, removePlayListSong, createLyricLive,
  playLiveTime, stopLiveTime, seekLiveTime, steamerAddSongs } from '@/api/api.js'
import { ElMessage } from 'element-plus'
// import LyricPlayer from '@/components/LyricPlayer.vue'

export default {
  data() {
    return {
      video: {},
      track: {},
      tableData: [ ],
      dircList: [],
      allSongNum: 0,
      user:{},
      org:{},
      pageInfo:{},
      addSongFrm:{
        name:'',
        addTxt:1,
        banL:'',
        banTime:{ 'min':'', 'ss':'' },
        oralTime:{ 'min':'', 'ss':'' },
        orlLyc:{ 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' },
        banLyc:{ 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' },
      },
      uploadAction:'/uploads/uploadfyByFile.do',
      uploadHeaders:{ 'Token':'' },
      uploadBtn1_txt:'上传',
      uploadBtn2_txt:'上传',
      fileLimit:'',
      postData:{
        module: '字幕精灵',
        userId: auth.getUserID()
      },
      uploadLyc:'.mp3,.cda,.wav,.aif,.aiff,.mid,.wma,.ra,.vqf,.ape',
      addRules: {
        name: [ { required: true, message: '请输入歌曲名称' } ],
      },
      catInfo:'',
      playTtl:'',
      input3:'',
      dialogVisible: false,
      dialogVisible2: false,
      dialogVisible3: true,
      addSongVisible:false,
      currentPage:1,
      loading:false,
      delObj:{},
      delSongObj:{},
      playName:'',
      playUrl:'',
      rhythmSrc:'',
      controls: true,
      // lycTxtInfo:'',
      // lycTxt:false,
      editObj:null,
      rootPath:{}
    }
  },
  mounted() {
    this.handleClose()
    localStorage.removeItem('editLit')
    this.getCats()
    this.uploadHeaders = { 'Token': auth.getToken() }
    getRootPathFile().then(res => {
      let allpath = res.data
      this.rootPath = allpath
      this.uploadAction = allpath.webRoot + this.uploadAction
      console.log('uploadAction=', this.uploadAction)
      console.log('getRootPathFile',this.rootPath)
    })
  },

  methods: {

    uploadRemove(file) {
      console.log('uploadRemove = ', file)
    },
    beforeUpload2 (file)  {
      this.uploadBtn2_txt = '正在上传...'
      console.log(' beforeUpload file=', file)
      this.addSongFrm.banLyc = { 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' }
      this.addSongFrm.banLyc.filename = file.name
    },
    beforeUpload (file)  {
      this.uploadBtn1_txt = '正在上传...'
      console.log(' beforeUpload file=', file)
      this.addSongFrm.orlLyc = { 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' }
      this.addSongFrm.orlLyc.filename = file.name
    },
    handleSuccess2 (res,file){ // 伴唱
      this.uploadBtn2_txt = '重新上传'
      console.log('handleSuccess =', res)
      this.addSongFrm.banLyc.filePath = res.filename
      this.addSongFrm.banLyc.fileUid = res.fileUid
      const audio = new Audio()
      audio.src = this.rootPath.webRoot + '/upload/' + res.filename
      this.countAudioTime(audio, 'banLyc')
    },
    handleSuccess (res,file){ // 原唱
      this.uploadBtn1_txt = '重新上传'
      console.log('handleSuccess =', res)
      this.addSongFrm.orlLyc.filePath = res.filename
      this.addSongFrm.orlLyc.fileUid = res.fileUid
      const audio = new Audio()
      audio.src = this.rootPath.webRoot + '/upload/' + res.filename
      this.countAudioTime(audio, 'orlLyc')
    },
    async countAudioTime  (audio , type){
      while (isNaN(audio.duration) || audio.duration === Infinity) {
        // 延迟一会 不然网页都卡死
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      let dur0 = audio.duration ; // 单位是 秒
      let dain = String(dur0).split('.')[1]
      let dur = String(dur0).split('.')[0]
      console.log('音频的总时长:',dur)
      this.addSongFrm[type].duration = dur0 // 秒
      let min = parseInt(dur/60)
      let ss = dur%60
      this.addSongFrm[type].durationTime = `${min}:${ss}.${dain}` // 12:12.909090
      this.addSongFrm[type].durationTime2 = `${min}:${ss}` // 12:12.909090
      console.log('min=', min)
      console.log('ss=', ss)
      if(type === 'banLyc'){
        this.addSongFrm.banTime.min = min
        this.addSongFrm.banTime.ss = ss
      }else if(type === 'orlLyc'){
        this.addSongFrm.oralTime = { 'min':min, 'ss': ss}
      }
    },
    delDian(mmTime){
      let intTime = 0
      intTime = String(mmTime).split('.')[0]
      return intTime
    },
    addSongBtn(){
      this.addSongVisible = true
      this.addSongFrm = {
        name:'',
        addTxt:1,
        banL:'',
        banTime:{ 'min':'', 'ss':'' },
        orlLyc:{ 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' },
        banLyc:{ 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' },
      }
      this.uploadBtn1_txt = '上传'
      this.uploadBtn2_txt = '上传'
    },
    addSongOk(){
      if(this.uploadBtn1_txt == '正在上传...' ){
        this.$message.error('原唱正在上传，请稍等...')
      }
      else if(this.uploadBtn2_txt == '正在上传...'){
        this.$message.error('伴唱正在上传，请稍等...')
      }
      else if(!this.addSongFrm.orlLyc.durationTime2){
        this.$message.error('请上传原唱!')
      }
      else if(!this.addSongFrm.name){
        this.$message.error('请输入歌曲名称!')
      }else {
        let duration = this.delDian(this.addSongFrm.orlLyc.duration*1000)
        let songDuration = ''
        if(this.addSongFrm.oralTime.min){
          songDuration = this.addSongFrm.oralTime.min + '分' + this.addSongFrm.oralTime.ss + '秒'
        }else if(this.addSongFrm.banTime.min){
          songDuration = this.addSongFrm.banTime.min + '分' + this.addSongFrm.banTime.ss + '秒'
        }
        let params = {
          'name': this.addSongFrm.name,
          'type': 1,
          'songDuration': songDuration ,
          'captionRequired': 1,
          'originalPath': this.addSongFrm.orlLyc.filePath,
          'trackDuration': duration,
          'module': '字幕精灵',
        }
        if(this.addSongFrm.banLyc.filePath){
          params.bgmPath = this.addSongFrm.banLyc.filePath
          params.bgmDuration = this.delDian(this.addSongFrm.banLyc.duration*1000)
        }
        steamerAddSongs(params).then(res => {
          let data = res.data.data
          console.log('data=', data)
          if(data.state == 1){
            this.$message.success({
              dangerouslyUseHTMLString: true,
              center: true,
              duration:4000,
              message: '新增成功！<br/>请到抖音小程序端为该歌曲配节奏！',
            })
          }else{
            this.$message.error({
              dangerouslyUseHTMLString: true,
              message: '新增失败！',
            })
          }

        }).catch(err => {
          console.log('err=', err)
        })
        this.addSongVisible = false

      }

    },
    handleClose(){
      this.playUrl=''
      this.rhythmSrc=''
      stopLiveTime(0)
      this.dialogVisible3 = false
    },
    playLyc(item, type){
      if(type === 1){
        if(item.havBgmPath === '没有'){
          ElMessage.error('没有可播放的伴奏！')
          return false
        }
      } else if(type === 2){
        if(item.havOriginalPath === '没有'){
          ElMessage.error('没有可播放的原唱！')
          return false
        }
      }
      // let that = this
      createLyricLive(item.songs).then(res => {
        console.log('getRootPathFile', 'playLyc', this.rootPath)
        // type :1-伴唱 2-原唱
        console.log('item=', item)
        this.playName = item.name
        this.playUrl = this.rootPath.fileUrl + (type === 1 ? item.bgmPath : item.originalPath)

        this.playTtl = (type === 1 ? '播放伴奏' : '播放原唱') + " - " + item.name

        console.log('createLyricLive then')
        console.log('res.status',res.status)
        if (res.status === 200) {

          console.log('item.captionRequired',item.captionRequired)
          if (item.captionRequired !== 0) { // 有歌词
            this.rhythmSrc = auth.webRoot + '/lyric/getCurrentRhythm.do?accId=' + auth.getAccId()
          }
          this.dialogVisible3 = true
        } else {
          return Promise.reject("后台创建播放失败!")
        }
      })
    },
    searchBtn(){
      this.editObj.searchName = this.input3
      this.currentPage = 1
      let data = {
        id: this.editObj.id,
        name: this.editObj.searchName,
        pageSize: 20,
        currentPageNo: 1,
      }
      this.moreFun(data)
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      let data = {
        id: this.editObj.id,
        name: this.editObj.searchName,
        pageSize: 20,
        currentPageNo: val,
      }
      this.moreFun(data)

    },
    moreFun(params,delType){
      this.loading = true
      getMoreSongs(params).then(res => {
        let data = res.data.data
        this.tableData = data.listSongs || []
        this.tableData.forEach(item => {
          item.havBgmPath = item.bgmPath?'有':'没有'
          item.havOriginalPath = item.originalPath?'有':'没有'
          item.havCaptionRequired = item.captionRequired == 0 ?'没有':'有'
        })
        this.pageInfo = data.pageInfo
        let totalResult = data.pageInfo.totalResult
        // this.dircList.forEach(dir => {
        //   if(dir.id === params.id){
        //     dir.songNum = totalResult
        //   }
        // })
        this.catInfo = `歌曲 > ${ this.editObj.name } ${ params.name ? ` > 搜索内容：${ params.name }`:'' }（${ totalResult}首）`
        this.loading = false
        if(!params.name ) {
          this.dircList.forEach(catI => {
            if(catI.id === this.editObj.id){
              console.log('catI=', catI)
              catI.songNum = totalResult

            }
          })
        }
        else if(delType == 1){
          this.dircList.forEach(catI => {
            if(catI.id === this.editObj.id){
              console.log('catI=', catI)
              catI.songNum--

            }
          })
        }


      }).catch(err => {
        this.loading = false
        console.log('err=', err)
      })
    },
    editLit(item,index){
      item.index = index
      localStorage.setItem('editLit', JSON.stringify(item))
      this.$router.push({
        name:'createLyc',
        params:{
          id:item.id,
        }
      })
    },
    getLitSongs(item,type){
      this.editObj = null
      this.editObj = item
      this.editObj.searchName = ''
      this.input3 = ""
      let data = {}
      this.editObj.id ? data.id = this.editObj.id : ''
      this.loading = true
      getPlayListMes(data).then(res => {
        let data = res.data.data
        console.log('getPlayListMes res=', data)
        if(type === 1){

        }else{
          this.tableData = data.listSongs || []
          this.tableData.forEach(item => {
            item.havBgmPath = item.bgmPath?'有':'没有'
            item.havOriginalPath = item.originalPath?'有':'没有'
            item.havCaptionRequired = item.captionRequired == 0 ?'没有':'有'
          })
        }

        this.catInfo = `歌曲 > ${ this.editObj.name }（${ this.editObj['songNum'] }首）`
        this.pageInfo = data.pageInfo
        this.currentPage = 1
        this.loading = false
      }).catch(err => {
        console.log('delPlayList err=', err)
      })
    },
    delLit(item,index){
      item.index = index
      this.delObj = item
      this.dialogVisible = true
    },
    delSong(songItem){
      console.log('songItem=', songItem)
      this.delSongObj = songItem
      this.dialogVisible2 = true

    },
    delSongOk(){
      let data = {}
      data.id = this.delSongObj.id
      data.playListNum = this.editObj.songNum
      this.loading = true
      removePlayListSong(data).then(res => {
        let data = res.data.data
        this.dialogVisible2 = false
        console.log('removePlayListSong=', data)
        if(data.state == 1){
          ElMessage.success({
            message: '操作成功！',
            type: 'success',
          })
          let params = {
            id: this.editObj.id,
            name: this.editObj.searchName,
            pageSize: 20,
            currentPageNo: this.currentPage
          }
          this.moreFun(params, 1)

        }else{
          ElMessage.error('操作失败！')
        }
        this.loading = false
      }).catch(err => {
        this.loading = false

      })


    },
    delLitOk(){
      this.dialogVisible = false
      if(this.delObj.id){
        this.loading = true
        delPlayList({ id: this.delObj.id }).then(res => {
          let data = res.data.data
          console.log('delPlayList res=', data)
          if(data.state == 1){
            this.dircList.splice(this.delObj.index, 1)
            ElMessage.success({
              message: '操作成功！',
              type: 'success',
            })
            if(this.delObj.id === this.editObj.id ){
              this.getCats()
            }

          }else{
            ElMessage.error('操作失败！')
          }
          this.loading = false

        }).catch(err => {
          this.loading = false
          console.log('delPlayList err=', err)
        })
      }
    },
    getCats(){
      this.loading = true
      minePlayList().then(res => {
        let data = res.data.data
        console.log('minePlayList=', data)
        this.dircList = data.playlist
        this.tableData = data.listSongs || []
        this.tableData.forEach(item => {
          item.havBgmPath = item.bgmPath?'有':'没有'
          item.havOriginalPath = item.originalPath?'有':'没有'
          item.havCaptionRequired = item.captionRequired == 0 ?'没有':'有'
        })
        this.editObj = this.dircList[0]
        this.catInfo = `歌曲 > 全部（${ this.dircList[0]['songNum']   }首）`
        this.pageInfo = data.pageInfo
        console.log('this.pageInfo =', this.pageInfo )
        this.loading = false

      }).catch(err => {
        this.loading = false
      })
    },
    goCereate(){
      this.$router.push('createLyc')
    },
    tableHeaderColor({rowIndex, columnIndex}) {
      if (rowIndex === 0 ) {
        return {background: '#eee', 'text-align':'center' }
      }
    },
    celStyle({rowIndex, columnIndex}) {
      return { 'text-align':'center'}
    },
    createLyricLive(songId) {
      console.log('createLyricLive', songId)
      createLyricLive(songId)
    },
    playLiveTime(event) {
      console.log('playLiveTime', event.target.currentTime, Math.round(event.target.currentTime*1000))
      playLiveTime(Math.round(event.target.currentTime*1000))
    },
    stopLiveTime(event) {
      console.log('stopLiveTime', event.target.currentTime, Math.round(event.target.currentTime*1000))
      stopLiveTime(Math.round(event.target.currentTime*1000))
    },
    seekLiveTime(event) {
      console.log('seekLiveTime', event.target.currentTime, Math.round(event.target.currentTime*1000))
      seekLiveTime(Math.round(event.target.currentTime*1000))
    }
  }
}
</script>
<style>
.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before{
  position: absolute;
  left: 80px;
}
</style>
<style lang="scss" scoped>
.home{
  .direName{ cursor: pointer }
  .directory{
    text-align: left;
    border-right: 1px solid #ccc;
    padding-right: 10px;

    .hh1{ font-weight: bold; font-size:20px;  }
    .dicTtl{ border-bottom: 4px solid #ded9fc; }
    .dicTtl2{ background: #eee; padding:10px;  }
    .docList{ padding:10px 0; }
    .docList>.catItem:nth-child(1){ background: #eee;border-bottom: 1px solid #ddd;  }
    .catItem{ clear: both; padding:10px;  }
    .catItem:hover, .catItem.active { clear: both; background: #eee;  }
    .ctrls{ position: relative; top:-5px;  }
  }
  .files{
    border-left: 2px solid #ccc;
    padding-left: 20px;

    .hed{ padding-bottom: 10px; }
    .fonL{ text-align: left;}
    .tabCC{ padding-top: 30px }
    .songNum{ font-size: 16px; font-weight: bold; color: #555;}
  }
  .fRight{ float: right;  }
  .bigLink{ font-weight: bold; font-size: 17px; }
  .redLink{ color: red; }
  .pageYec{ text-align: center }
  .pageYe{
    display: inline-block;
    margin:20px auto;
  }
  .mLit{
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url("../assets/img/mLit.png") ;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    position: relative;
    top:3px;
  }
  .AudioCCC{
    height: 5em;
  }
  .addSongFrmC{
    .inpuk{ display: block; padding:0 10px; width: 100%;  background: #fff; border:1px solid #ddd; border-radius:3px; height:32px; line-height:32px;   }
    .avatar-uploader{ position: absolute; right: 0; top:-33px; }
    .upCC{ position: relative;  }
  }
  ::cue {
    color: black;
    font-size: 2em;
    line-height: 2em;
    background-color: rgba(255, 255, 255, 1) !important;
  }
  //全屏按钮
  video::-webkit-media-controls-fullscreen-button {
    display: none;
  }
  //播放按钮
  video::-webkit-media-controls-play-button {
    display: block;
  }
  //进度条
  video::-webkit-media-controls-timeline {
    display: block;
  }
  //观看的当前时间
  video::-webkit-media-controls-current-time-display{
    display: block;
  }
  //剩余时间
  video::-webkit-media-controls-time-remaining-display {
    display: block;
  }
  //音量按钮
  video::-webkit-media-controls-mute-button {
    display: block;
  }
  video::-webkit-media-controls-toggle-closed-captions-button {
    display: block;
  }
  //音量的控制条
  video::-webkit-media-controls-volume-slider {
    display: block;
  }
  //所有控件
  video::-webkit-media-controls-enclosure{
    display: block;
  }
}
</style>