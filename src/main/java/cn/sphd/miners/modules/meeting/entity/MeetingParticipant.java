package cn.sphd.miners.modules.meeting.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

// 会议管理_参会者
@Entity
@Table(name = "t_meeting_participant")
public class MeetingParticipant {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name="org"  , nullable=true , unique=false)
    private Integer org;//机构ID

    @Column(name="meeting"  ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long meetingId;//会议ID

    @Column(name="user"  , nullable=true , unique=false)
    private Integer user;//参会者ID

    @Column(name="user_name"  ,length = 100, nullable=true , unique=false)
    private String userName;//参会者姓名

    @Column(name="type"  , nullable=true , unique=false)
    private Integer type;//类型:1-参与者,2-记录者

    @Column(name="orders"  , nullable=true , unique=false)
    private Integer orders;//排序

    @Column(name="is_invited"  , nullable=true , unique=false)
    private Boolean invited=true;//是否受邀

    @Column(name="is_signed"  , nullable=true , unique=false)
    private Boolean signed=false;//是否签收:true-签收'

    @Column(name="is_present"  , nullable=true , unique=false)
    private Boolean present=false;//是否出席:true-出席

    @Column(name="is_receipt"  , nullable=true , unique=false)
    private Boolean receipt;//是否签收会议记录

    @Column(name="sign_time"  ,  nullable=true , unique=false)
    private Date signTime;   //'签到时间'

    @Column(name="present_time"  ,  nullable=true , unique=false)
    private Date presentTime;   //'出席时间'

    @Column(name="receipt_time"  ,  nullable=true , unique=false)
    private Date receiptTime;   //会议记录'签收时间'

    @Column(name="memo"  ,length = 255, nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"  ,nullable=true , unique=false)
    private Integer creator;   //创建人/发起人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  ,nullable=true , unique=false)
    private Integer operation;   //操作:1-增,2-删,3-更改会议信息,4-更改参会者,5-更改问题

    @Column(name="previous_id"  ,nullable=true , unique=false)
    private Integer previousId;   //修改前记录ID

    @Column(name="version_no"  ,nullable=true , unique=false)
    private Integer versionNo;   //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="meeting", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MeetingInfo meetingInfo ;//


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }



    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Boolean getInvited() {
        return invited;
    }

    public void setInvited(Boolean invited) {
        this.invited = invited;
    }

    public Boolean getSigned() {
        return signed;
    }

    public void setSigned(Boolean signed) {
        this.signed = signed;
    }

    public Boolean getPresent() {
        return present;
    }

    public void setPresent(Boolean present) {
        this.present = present;
    }

    public Boolean getReceipt() {
        return receipt;
    }

    public void setReceipt(Boolean receipt) {
        this.receipt = receipt;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getPresentTime() {
        return presentTime;
    }

    public void setPresentTime(Date presentTime) {
        this.presentTime = presentTime;
    }

    public Date getReceiptTime() {
        return receiptTime;
    }

    public void setReceiptTime(Date receiptTime) {
        this.receiptTime = receiptTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public MeetingInfo getMeetingInfo() {
        return meetingInfo;
    }

    public void setMeetingInfo(MeetingInfo meetingInfo) {
        this.meetingInfo = meetingInfo;
    }
}
