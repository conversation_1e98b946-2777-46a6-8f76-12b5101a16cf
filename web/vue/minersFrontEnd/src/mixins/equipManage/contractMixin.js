import * as api from "@/api/supplier";
import * as epApi from "@/api/equipMent";
import auth from "@/sys/auth";
import {previewFile} from "@/utils/downloadFile";

export default {
    name:'',
    components:{},
    data(){
        return {
            addPurContractLog: false,
            form_editContract: { // 合同
                editType: 1, // 1：新增 2：修改 3：续约
                supInfo: {},
                supplier: '',
                sn: '',
                signTime: '',
                validTime: [], // 自定义的，接收开始时间和结束时间的组合字段
                validTimeMin: '',
                validStart: '',
                validEnd: '',
                contractBaseImages: [],
                removeImgList: [],
                fileList: [],
                listMt: [],
                memo: '',
                canChoosePurchaseMt: [],
                type: 1 // 1普通续约 2停用续约 3过期续约 (只有续约传）
            },
            option_suppliers:[],
            imgUpload: {
                uploadHeaders: {'Token': auth.getToken()},
                uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
                uploadLyc: '.png,.jpeg,.jpg,.gif',
                postData: {
                    module: '供应商', userId: auth.getUserID()
                }
            },
            fileUpload: {
                uploadHeaders:{ 'Token': auth.getToken() },
                uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
                uploadLyc: '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt',
                postData: {
                    module: '供应商', userId: auth.getUserID()
                },
            },
            editContractRules: {
                supplier: [
                    { required: true, message: '请选择供应商', trigger: 'change' }
                ],
                sn: [
                    { required: true, message: '请输入合同编号', trigger: 'blur' }
                ],
                validTime: [
                    { type: 'array', required: true, message: '请选择合同的有效期', trigger: 'change' }
                ]
            },

            dialog_visible_imgShow: false,
            imgShow: '',
            seeContractBySup: {
                type: 1,
                info: {},
                list1: [],
                list2: [],
                list3: [],
            },
            seeContractChangeHis: {
                info: {},
                list: []
            },
            seeContractRenewHis: {
                info: {},
                list: []
            },
            seeContractDetail: {
                info: {
                    fileList: []
                }
            },
            list_seeContractImg: {
                list: [],
                preview: []
            },
            recoveryExpire: {
                radio: '',
                validEnd: ''
            },
            supplerNameInfo: {},
            dialog_visible_manage_img: false,
            dialog_visible_manage_changeHis: false,
            dialog_visible_manage_renewHis: false,
            dialog_visible_seeContract: false,
            dialog_visible_recoveryExpire: false,
        }
    },
    created(){},
    mounted(){},
    computed(){
    },
    methods: {
        addPurContractSure(){
            this.$refs['editContractForm'].validate((valid) => {
                if (valid) {
                    const editType = this.form_editContract.editType
                    const contractInfo = this.form_editContract
                    let imgs = contractInfo.contractBaseImages.map(item => {
                        let newItem = {
                            uplaodPath: item.normalPath,
                            type: 1,
                            title: item.name,
                            operation: item.operation
                        }
                        if (item.id) newItem.id = item.id
                        if (editType === 3) newItem.operation = 1
                        return newItem
                    })
                    if (editType === 2) {
                        // 修改时需要传删除得图片 operation 1 用于表示新增 4 表示没动 2表示删除
                        let removeImgs = contractInfo.removeImgList.map(item => {
                            return {
                                uplaodPath: item.normalPath,
                                type: 1,
                                title: item.name,
                                operation: 2,
                                id: item.id
                            }
                        })
                        imgs = [...imgs, ...removeImgs]
                    }
                    let data = {
                        supplier: contractInfo.supInfo.id,
                        sn: contractInfo.sn,
                        contractSignTime: contractInfo.signTime,
                        memo: contractInfo.memo,
                        contractBaseImages: JSON.stringify(imgs),
                        contractStartTime: contractInfo.validTime?.[0],
                        contractEndTime: contractInfo.validTime?.[1],
                        mtList: JSON.stringify(contractInfo.listMt.map(item => {return {material: item.id}})),
                    }
                    if ((this.page === 'seeContractByAll' || this.page === 'main') && this.form_editContract.editType === 1) {
                        // 全部合同新增合同客户是选择的
                        data.supplier = contractInfo.supplier
                    }
                    if (contractInfo.editType === 3) {
                        data.type = contractInfo.type //1普通续约 2停用续约 3过期续约 (只有续约传）
                    }
                    if (contractInfo.fileList.length > 0) {
                        data.filePath = contractInfo.fileList[0].normalPath
                        data.fileName = contractInfo.fileList[0].name
                    }
                    if (editType > 1) {
                        data.id = contractInfo.id
                    }
                    let promise = editType === 1?api.addContract(data):(editType === 2?api.changeContract(data): api.renewContract(data))

                    promise.then(res => {
                        let state = res.data.data.state
                        if (state === 1) {
                            this.addPurContractLog = false
                            this.$message({
                                type: 'success',
                                message: '操作成功！'
                            })
                            this.initPage(1)
                        } else if (state === 2) {
                            this.$message({
                                type: 'error',
                                message: '已续约不可修改！'
                            })
                        } else if (state === 3) {
                            this.$message({
                                type: 'error',
                                message: '修改的日期不能在上一个合同结束日之前！'
                            })
                        } else {
                            this.$message({
                                type: 'error',
                                message: '操作失败！'
                            })
                        }
                    })
                } else {
                    this.$message({
                        type: 'error',
                        message: '验证失败，请检查'
                    })
                }
            })
        },
        change_supplier(value) {
            let info = this.option_suppliers.find(item => item.value === value)
            this.form_editContract.supInfo = {
                fullName: info.label,
                code: info.code,
                id: info.value
            }
            this.initGoods(value)
            this.form_editContract.listMt = []
            this.form_editContract.select = []
        },
        initGoods(supplierId) {
            api.getContractMt(supplierId)
                .then(res => {
                    let data = res.data.data
                    let mtList = data.mtList || []
                    this.form_editContract.canChoosePurchaseMt = mtList
                })
        },
        disabledDate(time) {
            return time.getTime() < new Date(this.form_editContract.validTimeMin).getTime()
        },
        editContract_imgSuccess(response, file, fileList, index) {
            // 这里的response是服务器返回的数据
            // file是上传成功的文件对象
            // fileList是上传的文件列表
            let fileItem = {
                url: auth.webRoot + '/upload/' + response.filename,
                normalPath:  response.filename,
                name: response.originalFilename,
                operation: 1
            }
            this.form_editContract.contractBaseImages = this.form_editContract.contractBaseImages.map(f => (f.uid === file.uid ? fileItem : f));
            console.log('文件上传成功', this.form_editContract.contractBaseImages);
            // 在这里执行你需要的操作
        },
        editContract_imgPreview(file) {
            this.dialog_visible_imgShow = true
            this.imgShow = file.url
        },
        editContract_imgRemove(file) {
            console.log('file', file)
            if (file.id) {
                file.operation = 2
                this.form_editContract.removeImgList.push(file)
            }
        },
        editContract_imgHandleExceed() {
            this.$message({
                type: 'error',
                message: '最多只能上传9张！'
            })
        },
        editContract_fileHandleExceed(files, fileList) {
            this.$refs.upload.clearFiles()
            this.$refs.upload.handleStart(files[0])
            this.$refs.upload.submit()
        },
        editContract_fileSuccess(response, file, fileList) {
            // 这里的response是服务器返回的数据
            // file是上传成功的文件对象
            // fileList是上传的文件列表
            let fileItem = {
                url: auth.webRoot + '/upload/' + response.filename,
                normalPath:  response.filename,
                name: response.originalFilename
            }
            fileList[fileList.length - 1] = fileItem
            console.log('文件上传成功', fileList);
            // 在这里执行你需要的操作
        },
        initContract(editType, contractInfo) {
            this.addPurContractLog = true
            this.resetEditContractForm()
            this.form_editContract.editType = editType
            if (this.form_editContract.supInfo.id) {
                let cusId = this.form_editContract.supInfo.id
                this.initGoods(cusId)
            }
            if (editType > 1) {
                // 赋默认值
                api.getContractBase(contractInfo.id)
                    .then(res => {
                        let data = res.data.data
                        let contractBase = data.contractBase
                        console.log(data)
                        const newEditContract = this.form_editContract
                        let imgs = data.listImage.map(item => {
                            return {
                                url: auth.webRoot + '/upload/' + item.uplaodPath,
                                normalPath: item.uplaodPath,
                                name: item.title,
                                operation: 4,
                                id: item.id
                            }
                        })
                        this.form_editContract = {
                            ...newEditContract,
                            id: contractBase.id,
                            editType: editType, // 1：新增 2：修改 3：续约
                            sn: contractBase.sn,
                            signTime: this.$filter.format(contractBase.signTime, 'day'),
                            validTime: [this.$filter.format(contractBase.validStart, 'day'), this.$filter.format(contractBase.validEnd, 'day')], // 自定义的，接收开始时间和结束时间的组合字段
                            validTimeMin: '',
                            contractBaseImages: imgs,
                            fileList: [],
                            listMt: data.listMt || [],
                            memo: contractBase.memo
                        }
                        if (editType === 3) {
                            this.form_editContract.validTime = []
                            this.form_editContract.validTimeMin = this.$filter.format(contractBase.validEnd, 'day')
                        }
                        console.log('validTimeMin', this.form_editContract.validTimeMin)
                        if (contractBase.filePath) {
                            this.form_editContract.fileList.push({
                                url: auth.webRoot + '/upload/' + contractBase.filePath,
                                normalPath: contractBase.filePath,
                                name: contractBase.fileName
                            })
                        }
                        console.log('this.form_editContract', this.form_editContract)
                    })
            }
        },
        resetEditContractForm(){
            let temp = { // 合同
                editType: 1, // 1：新增 2：修改 3：续约
                supplier: '',
                sn: '',
                signTime: '',
                validTime: [], // 自定义的，接收开始时间和结束时间的组合字段
                validTimeMin: '',
                validStart: '',
                validEnd: '',
                contractBaseImages: [],
                removeImgList: [],
                fileList: [],
                listMt: [],
                memo: '',
                canChoosePurchaseMt: [],
                type: 1 // 1普通续约 2停用续约 3过期续约 (只有续约传）
            }
            for (let key in temp) {
                this.form_editContract[key] = temp[key]
            }
        },
        manage_seeContractDetail(item, origin) {
            let isChange = this.dialog_visible_manage_changeHis // 修改记录还是续约记录
            let promise = isChange?api.getContractChangeHisDetail(item.id):api.getContractBase(item.id)
            promise.then(res => {
                let data = res.data.data
                let contractBase = isChange?data.contracthistory:data.contractBase
                let listImage = isChange?data.listHisImage:data.listImage
                let listMt = isChange?data.listMtHis:data.listMt
                console.log(data)
                listImage.map(item => {
                    item.url = auth.webRoot + '/upload/' + item.uplaodPath
                    item.normalPath = item.uplaodPath
                    item.name = item.title
                })
                let previewList = listImage.map(item => {
                    return auth.webRoot + '/upload/' + item.uplaodPath
                })
                this.seeContractDetail.info = {
                    origin: origin,
                    id: contractBase.id,
                    filePath: contractBase.filePath,
                    sn: contractBase.sn,
                    signTime: this.$filter.format(contractBase.signTime, 'day'),
                    validStart: this.$filter.format(contractBase.validStart, 'day'),
                    validEnd: this.$filter.format(contractBase.validEnd, 'day'),
                    imgList: listImage,
                    previewList: previewList,
                    fileList: [],
                    listMt: listMt || [],
                    memo: contractBase.memo
                }
                if (contractBase.filePath) {
                    this.seeContractDetail.info.fileList.push({
                        url: auth.webRoot + '/upload/' + contractBase.filePath,
                        normalPath: contractBase.filePath,
                        name: contractBase.fileName
                    })
                }
                console.log('manage_seeContractDetail', this.seeContractDetail.info)
                this.dialog_visible_seeContract = true
            })

        },
        manage_change(item) {
            // 修改合同信息
            this.addPurContractLog = true
            if (item.supplier) {
                this.form_editContract.supInfo = {id: item.supplier, fullName: item.supplierName}
            }
            this.initContract(2, item)
        },
        manage_renew(item, type) {
            // 续约
            this.addPurContractLog = true
            if (item.supplier) {
                this.form_editContract.supInfo = {id: item.supplier, fullName: item.supplierName}
            }
            this.form_editContract.type = type
            this.initContract(3, item)
        },
        manage_stop(item) {
            console.log(this.seeContractBySup.info.id)
            // 暂停履约/合同终止
            this.$messageBox.confirm(
                `点击“确定”后，本合同将进入“已暂停/终止的合同”。<br>确定进行本操作吗？`,
                '！！提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                }
            )
                .then(() => {
                    api.stopContract(item.id)
                        .then(res => {
                            let state = res.data.data.state
                            if (state === 1) {
                                this.$message({
                                    type: 'success',
                                    message: '操作成功！'
                                })
                                this.initPage()
                            } else if (state === 0) {
                                this.$message({
                                    type: 'error',
                                    message: '请勿重复操作！'
                                })
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: '操作失败！'
                                })
                            }
                        })
                })
        },
        manage_recovery(item) {
            if (item.supplier) {
                this.form_editContract.supInfo = {id: item.supplier, fullName: item.supplierName}
            }
            // 恢复履约/重启合作
            api.judgeRecoveryContract(item.id)
                .then(res => {
                    let data = res.data.data
                    let state = data.state // state 0-不要重复启用 1-没有过期直接恢复 2-已经过期了(跳转到确认弹窗 续约或者回到已过期）
                    let vaildEnd = data.vaildEnd // 到期日期
                    if (state === 0) {
                        this.$message({
                            type: 'warning',
                            message: '请勿重复操作！'
                        })
                    } else if (state === 2) {
                        this.dialog_visible_recoveryExpire = true
                        this.recoveryExpire = {
                            contractInfo: item,
                            radio: '',
                            validEnd: this.$filter.format(vaildEnd, 'day')
                        }
                    } else if (state === 1) {
                        this.$messageBox.confirm(
                            `点击“确定”后，本合同将回到有效合同列表。<br>确定进行本操作吗？`,
                            '！提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                                dangerouslyUseHTMLString: true
                            }
                        )
                            .then(() => {
                                this.contractRecovery_submit(item.id, 1)
                            })
                    } else {
                        this.$message({
                            type: 'error',
                            message: '操作失败！'
                        })
                    }
                })
        },
        contractRecovery_submit(id, type) {
            api.recoveryContract(id, type)
                .then(res => {
                    let state = res.data.data.state
                    if (state === 1) {
                        this.$message({
                            type: 'success',
                            message: '操作成功！'
                        })
                        this.initPage(2)
                    } else if (state === 0) {
                        this.$message({
                            type: 'error',
                            message: '请勿重复操作！'
                        })
                    } else {
                        this.$message({
                            type: 'error',
                            message: '操作失败！'
                        })
                    }
                })
        },
        manage_recoveryExpire_submit() {
            let radio = this.recoveryExpire.radio
            if (radio) {
                if (radio === 1) {
                    this.dialog_visible_recoveryExpire = false
                    this.contractRecovery_submit(this.recoveryExpire.contractInfo.id, 2)
                } else if (radio === 2) {
                    this.dialog_visible_recoveryExpire = false
                    this.form_editContract.supInfo = {id: this.recoveryExpire.contractInfo.supplier, fullName: this.recoveryExpire.contractInfo.supplierName}
                    this.initContract(3, this.recoveryExpire.contractInfo) // 客户数据在恢复合同按钮处已赋值
                    this.form_editContract.type = 2
                }
            } else {
                this.$message({
                    type: 'error',
                    message: '请勾选！'
                })
            }
        },
        manage_changeHis(item) {
            // 本版本合同的修改记录
            this.dialog_visible_seeContract = false
            this.dialog_visible_manage_changeHis = true
            api.getContractChangeHis(item.id)
                .then(res => {
                    let list = res.data.data.list
                    this.seeContractChangeHis.list = list
                })
        },
        manage_renewHis(item) {
            // 本合同的续约记录
            this.dialog_visible_manage_renewHis = true
            api.getContractRenewHis(item.primaryCont)
                .then(res => {
                    let list = res.data.data.list
                    this.seeContractRenewHis.list = list
                })
        },
        preview_File(path) {
            console.log(this.seeContractDetail.info)
            previewFile(this.rootPath, path??this.seeContractDetail.info.fileList[0].normalPath)
        },
        hideFun(str){
            switch (str) {
                case 'addPurContractLog':
                    this.addPurContractLog = false
                    break
                case 'contractMtLog':
                    this.contractMtLog = false
                    break
                case 'scanContractMtLog':
                    this.scanContractMtLog = false
                    break
                case 'dialog_visible_imgShow':
                    this.dialog_visible_imgShow = false
                    break
                case 'dialog_visible_seeContractByGood':
                    this.dialog_visible_seeContractByGood = false
                    break
                case 'dialog_visible_manage_img':
                    this.dialog_visible_manage_img = false
                    break
                case 'dialog_visible_manage_renewHis':
                    this.dialog_visible_manage_renewHis = false
                    break
                case 'dialog_visible_recoveryExpire':
                    this.dialog_visible_recoveryExpire = false
                    break
                case 'dialog_visible_manage_changeHis':
                    this.dialog_visible_manage_changeHis = false
                    break
                case 'dialog_visible_seeContract':
                    this.dialog_visible_seeContract = false
                    break
            }
        },
    }
}

