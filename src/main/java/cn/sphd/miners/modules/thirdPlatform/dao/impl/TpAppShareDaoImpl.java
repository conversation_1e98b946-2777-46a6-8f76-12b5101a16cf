package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpAppShareDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpAppShare;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpAppShareDaoImpl extends BaseDao<TpAppShare, Serializable> implements TpAppShareDao {
}
