package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquCategory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 装备管理_分类
 * 20230209 1.247装备器具新增Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Repository
public interface TEquCategoryMapper {
    /**
     * 查询装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_分类
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_分类
     * 20230209 1.247装备器具新增
     */
    public TEquCategory selectTEquCategoryById(Long id);

    /**
     * 查询装备管理_分类
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @return 装备管理_分类
     * 20230209 1.247装备器具新增集合
     */
    public List<TEquCategory> selectTEquCategoryList(TEquCategory tEquCategory);

    /**
     * 新增装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquCategory(TEquCategory tEquCategory);

    /**
     * 修改装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquCategory(TEquCategory tEquCategory);

    /**
     * 删除装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_分类
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    public int deleteTEquCategoryById(Long id);

    /**
     * 批量删除装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquCategoryByIds(String[] ids);
}
