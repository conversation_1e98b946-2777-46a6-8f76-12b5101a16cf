package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.generalAffairs.entity.PersonalEducation;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelOccupation;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.generalAffairs.service.impl.RecruitUsing;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelFolks;
import cn.sphd.miners.modules.personal.entity.PersonnelInterview;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelInterviewService;
import cn.sphd.miners.modules.recruit.entity.RdNoticePost;
import cn.sphd.miners.modules.recruit.entity.RdPost;
import cn.sphd.miners.modules.recruit.entity.RdResume;
import cn.sphd.miners.modules.recruit.service.RdNoticeService;
import cn.sphd.miners.modules.recruit.service.RdPostService;
import cn.sphd.miners.modules.recruit.service.RdResumeService;
import cn.sphd.miners.modules.recruit.service.impl.RdNoticeUsing;
import cn.sphd.miners.modules.system.entity.Offer;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.*;
import cn.sphd.miners.modules.system.service.impl.UserUsing;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2018/4/11.
 * 招聘管理
 */
@Controller
@RequestMapping("/recruit")
public class RecruitController {
    @Autowired
    UserService userService;
    @Autowired
    PersonalService personalService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    RoleService roleService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    PopedomService popedomService;
    @Autowired
    OfferService offerService;
    @Autowired
    PersonnelInterviewService personnelInterviewService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    UploadService uploadService;
    @Autowired
    AuthService authService;
    @Autowired
    RdPostService rdPostService;
    @Autowired
    RdResumeService rdResumeService;
    @Autowired
    RdNoticeService rdNoticeService;

    //跳转招聘管理页面
    @RequestMapping("/recruitManage.do")
    public String recruitManage() {
        return "";
    }

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * <AUTHOR>
     * @Date 2018/4/11 17:51
     * 添加 招聘基本信息
     *
     * hashKey 前端生成的游客标识，不清缓存一直有效，用于再次扫码 做历史数据查询   1.241招聘管理优化更改 支持二维码和链接点击 2023/6/15
     * noticeId 启示id
     * noticePostId 招聘岗位id
     */
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/addRecruitUser.do")
    public JsonResult addRecruitUser(Integer oid, Offer offer, String date, String filePath, String hashKey,Long noticeId,Long noticePostId,Integer userId,Long resumeId, HttpServletRequest request/*, @RequestParam(value = "imgFile", required = false) MultipartFile[] files*/) throws ParseException, IOException {
        System.out.println("oid:" + oid);
        System.out.println("Offer:" + offer);
//        Map<String, Object> map = new HashMap<String, Object>();
//        if (offer.getMobile().length() != 11) {
//            map.put("status", 2);//手机号不是11位的
//        } else {
//            List<User> users = userService.getUserListByOidPhone(oid, offer.getMobile());
//            List<User> userList = userService.getUserByMobeilAndIsDuty(oid, offer.getMobile(), "2");  //离职人员
//            if (users.size() > 0) {
//                map.put("status", 3);//系统中已存在手机号
//            } else if (userList.size() > 0) {
//                map.put("status", 4);//此手机号已办理离职
//            } else {
        if (resumeId!=null){
            RdResume rdResume=rdResumeService.getRdResumeById(resumeId);
            rdResumeService.deleteRdResume(rdResume);

            Offer offer1=offerService.getOfferById(rdResume.getOffer());
            offerService.deleteOffer(offer1);
        }

                Organization organization = orgService.getByOid(oid);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                //基本信息
                Offer user1 = new Offer();
                if (date != null && !"".equals(date)) {
                    Date bDate = new Date();
                    bDate = simpleDateFormat.parse(date);
                    user1.setBirthday(bDate);//出生日期
                    user1.setAge(NewDateUtils.getYear(new Date())-NewDateUtils.getYear(bDate));
                }
                user1.setUserName(offer.getUserName());//用户姓名
                user1.setRealName(offer.getUserName());//真实姓名
                user1.setGender(offer.getGender());//性别
                user1.setSuperId(organization.getSuperId());//薪资宝的
                user1.setMobile(offer.getMobile());//手机
                user1.setEmail(offer.getEmail());//电子邮箱
                user1.setQq(offer.getQq());//qq号
                user1.setPoliticalStatus(offer.getPoliticalStatus());//政治面貌
                user1.setMarry(offer.getMarry());//0已婚。1未婚
                user1.setNation(offer.getNation());//民族
                user1.setIdCard(offer.getIdCard());//身份证号
                user1.setAddress(offer.getAddress());//身份证住址
                user1.setBirthplace(offer.getAddress());//出生地
                user1.setHomeAddress(offer.getHomeAddress());//家庭地址
                user1.setActualAddress(offer.getActualAddress());//实际地址
                user1.setInteresting(offer.getInteresting());  //兴趣、特长
                user1.setSpeciality(offer.getSpeciality());  //爱好
                user1.setNativePlace(offer.getNativePlace());  //籍贯
                user1.setEdu(offer.getEdu());  //最高学历
                user1.setDegree(offer.getDegree());//学历 '1-小学以下，2-小学，3-初中，4-高中，5-中专，6-大专 7-本科，8-硕士，9-博士
                user1.setResidence(offer.getResidence());//户籍
                user1.setNativeType(offer.getNativeType());//户籍性质
                user1.setOfferSalaty(offer.getOfferSalaty());//期望薪资
                user1.setOfferPost(offer.getOfferPost());//应聘职位
                user1.setFirstLanguage(offer.getFirstLanguage());//第一外语
                user1.setFirstForeignLevel(offer.getFirstForeignLevel());//第一外语证书
                user1.setSecondLanguage(offer.getSecondLanguage());//第二外语
                user1.setSecondForeignLevel(offer.getSecondForeignLevel());//第二外语证书
                user1.setComputerLevel(offer.getComputerLevel());//计算机证书
                user1.setOtherSkills(offer.getOtherSkills());//其他技能描述

                user1.setOrdinaryEmployees(1);//是否为普通员工 1-普通员工

                user1.setCreateTime(new Date());//创建时间
                user1.setStatus("2");////状态：1-禁用 其它-启用
                user1.setIsDuty("0");//1表示在职，2表示离职 ,3可登录的代理人员,0-待入职(招聘)
                user1.setOfferStatus("1");//招聘状态:1-待入职,2-已入职,3-已拒绝
                user1.setLogonName(offer.getMobile());
                user1.setCompany(offer.getCompany());//所属公司
                user1.setTransferReason(offer.getTransferReason());//转岗原因
                user1.setWorkYear(offer.getWorkYear());//工作年限
                user1.setPay(offer.getPay());//薪资

                user1.setOfferSalaty(offer.getOfferSalaty());
                user1.setUserType(10);//代表用户的类型 目前暂定为：0：超级管理员  1：财务  2：浏览者 3:总务 4:销售
                user1.setRemark(offer.getRemark());//备注
                user1.setOid(offer.getOid());
                user1.setManager(offer.getManager());  //高管id
                user1.setManagerCode(offer.getManagerCode());  //高管权限
                user1.setRoleCode("staff");  //此员工的权限
                user1.setSuperId(organization.getSuperId());
                user1.setOid(oid);
                user1.setOfferPost(offer.getOfferPost());
                user1.setOfferTime(new Date());
                user1.setUpdateDate(new Date());

//                if (files != null) {
//                    for (MultipartFile f : files) {
//                        String path = UploadImages.uploadImages(request, f);
//                        user1.setImgPath(path);
//                    }
//                }
                offerService.saveOffer(user1);
                User staffUser=userService.getUserByID(userId);// 生成本招聘二维码的 userId

                if (offer.getImgPath()!=null&&!offer.getImgPath().isEmpty()) {
                    user1.setImgPath(offer.getImgPath());
                    uploadService.addFileUsing(new RecruitUsing(user1.getUserID()), user1.getImgPath(), user1.getUserName(), staffUser, "招聘管理");
                }
                offerService.updateOffer(user1);

//             家庭成员， 新版不填了
//                String folks = request.getParameter("folks");
//                if (!folks.isEmpty()) {
//                    JSONArray jsonArray = JSONArray.fromObject(folks);
//                    List educationList = JSONArray.toList(jsonArray);
//                    for (int i = 0; i < educationList.size(); i++) {
//                        JSONObject jo = JSONObject.fromObject(educationList.get(i));
//                        PersonnelFolks personnelFolks = new PersonnelFolks();
//                        personnelFolks.setOffer(user1.getUserID());
//                        personnelFolks.setName(jo.optString("name"));
//                        if (jo.get("age")!=null&&!jo.get("age").equals(""))
//                            personnelFolks.setAge(jo.getInt("age"));
//                        personnelFolks.setGender(jo.optString("gender"));
//                        personnelFolks.setRelation(jo.optString("relation"));
//                        personalService.savePersonnelFolks(personnelFolks);
//                    }
//                }

                String occupations = request.getParameter("occupations");
                if (!occupations.isEmpty()) {
                    JSONArray jsonArray = JSONArray.fromObject(occupations);
                    List occupationList = JSONArray.toList(jsonArray);
                    for (int i = 0; i < occupationList.size(); i++) {
                        JSONObject jo = JSONObject.fromObject(occupationList.get(i));
                        PersonnelOccupation personnelOccupation = new PersonnelOccupation();
                        personnelOccupation.setOffer(user1.getUserID());
                        personnelOccupation.setCorpName(jo.optString("corpName"));
                        if(StringUtils.isNotEmpty(jo.optString("beginTime"))&&!"null".equals(jo.optString("beginTime"))){
                            personnelOccupation.setBeginTime(sdf.parse(jo.optString("beginTime")));
                        }
                        if(StringUtils.isNotEmpty(jo.optString("endTime"))&&!"null".equals(jo.optString("endTime"))) {
                            personnelOccupation.setEndTime(sdf.parse(jo.optString("endTime")));
                        }
                        personnelOccupation.setOperatingDuty(jo.optString("operatingDuty"));
                        personnelOccupation.setPost(jo.optString("post"));
                        personnelOccupation.setSalary(jo.getDouble("salary"));
                        personnelOccupation.setMemo(jo.optString("memo"));//离职原因
                        personnelOccupation.setCorpSize(jo.getString("corpSize"));//公司规模
                        personnelOccupation.setCorpNature(jo.getString("corpNature"));//公司性质
                        personnelOccupation.setCorpDepartment(jo.getString("corpDepartment"));//所在部门
                        personnelOccupation.setJobDesc(jo.getString("jobDesc"));//工作描述
                        personalService.savePersonnelOccupation(personnelOccupation);
                    }
                }

                String educations = request.getParameter("educations");
                if (!educations.isEmpty()) {
                    JSONArray jsonArray = JSONArray.fromObject(educations);
                    List educationList = JSONArray.toList(jsonArray);
                    for (int i = 0; i < educationList.size(); i++) {
                        JSONObject jo = JSONObject.fromObject(educationList.get(i));
                        PersonalEducation personalEducation = new PersonalEducation();
                        personalEducation.setOffer(user1.getUserID());
                        personalEducation.setCollegeName(jo.optString("collegeName"));
                        if(StringUtils.isNotEmpty(jo.optString("beginTime"))&&!"null".equals(jo.optString("beginTime"))){
                            personalEducation.setBeginTime(sdf.parse(jo.optString("beginTime")));
                        }
                        if(StringUtils.isNotEmpty(jo.optString("endTime"))&&!"null".equals(jo.optString("endTime"))) {
                            personalEducation.setEndTime(sdf.parse(jo.optString("endTime")));
                        }
                        personalEducation.setMajor(jo.optString("major"));//专业
//                        personalEducation.setDegree(jo.optString("degree"));//学历学位
                        personalEducation.setDegreeDesc(jo.optString("degreeDesc"));//学历学位描述
                        personalEducation.setMajorDesc(jo.optString("majorDesc"));//专业描述
                        personalEducation.setMemo(jo.optString("memo"));//补充说明
                        personalEducation.setDepartmentName(jo.optString("departmentName"));//院系
                        personalService.savePersonalEducation(personalEducation);
                    }
                }
            User general=userService.getUserByRoleCode(oid,"general");
            RdResume rdResume=new RdResume();
            BeanUtils.copyProperties(user1,rdResume);
            if (noticePostId!=null){
                RdNoticePost rdNoticePost=rdNoticeService.getRdNoticePostById(noticePostId);
                rdResume.setPostName(rdNoticePost.getPostName());
                rdResume.setRdNoticePost(rdNoticePost);
            }
            rdResume.setOrg(user1.getOid());
            rdResume.setOrgName(organization.getName());
            rdResume.setCreateDate(user1.getCreateTime());
            rdResume.setNoticeId(noticeId);
            rdResume.setHashKey(hashKey);
            rdResume.setOffer(user1.getUserID());
            rdResume.setFilePath(filePath);
            rdResumeService.saveRdResume(rdResume);

            if (StringUtils.isNotEmpty(filePath)) {
                RdNoticeUsing callback = new RdNoticeUsing(rdResume.getId(), rdResume.getClass());
                String name = rdResume.getUserName();
                uploadService.addFileUsing(callback, rdResume.getFilePath(), name, staffUser, "招聘管理");
            }
            approvalProcessService.choiceInterviewers(user1,general.getUserID(),new Date(),general);//给总务高管加审批流程


//            map.put("status", 1);//成功
//            map.put("offerId", user1.getUserID());

//            }
            return  new JsonResult(1,1);

//        }
    }


    /**
     * <AUTHOR>
     * @Date 2018/4/12 17:17
     * 招聘人员家庭成员
     */
    @ResponseBody
    @RequestMapping("/addOfferPersonnelFolks.do")
    public void addOfferPersonnelFolks(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        String folks = request.getParameter("folks");
        String offerId=request.getParameter("offerId");
        if (!folks.isEmpty()) {
            JSONArray jsonArray = JSONArray.fromObject(folks);
            List educationList = JSONArray.toList(jsonArray);
            for (int i = 0; i < educationList.size(); i++) {
                JSONObject jo = JSONObject.fromObject(educationList.get(i));
                PersonnelFolks personnelFolks = new PersonnelFolks();
                personnelFolks.setOffer(Integer.valueOf(offerId));
                personnelFolks.setName(jo.optString("name"));
                if (jo.get("age")!=null&&!jo.get("age").equals(""))
                    personnelFolks.setAge(jo.getInt("age"));
                personnelFolks.setGender(jo.optString("gender"));
                personnelFolks.setRelation(jo.optString("relation"));
                personalService.savePersonnelFolks(personnelFolks);
            }
            map.put("status", 1);//成功
        } else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }


    /**
     * <AUTHOR>
     * @Date 2018/4/13 9:27
     * 查看招聘人员详情
     */
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/offerInfo.do")
    public JsonResult offerInfo(Integer id) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (id != null) {
            Offer offer = offerService.getOfferById(id);
            List<PersonalEducation> peList = offerService.getPersonalEducationListByOfferId(id);
            List<PersonnelOccupation> poList = offerService.getPersonnelOccupationListByOfferId(id);
            List<PersonnelFolks> pfList = offerService.getPersonnelFolksListByOfferId(id);
            List<PersonnelInterview> piList = offerService.getPersonnelInterviewByOfferId(id);
            offer.setPersonnelInterviews(piList);//面试意见
            offer.setPersonalEducations(peList);//教育经历
            offer.setPersonnelOccupations(poList);//工作经历
            offer.setPersonnelFolkses(pfList);//家庭成员
            map.put("status", 1);//成功
            map.put("offer", offer);
            map.put("filePath",rdResumeService.getRdResumeByOfferId(id).getFilePath());
        } else {
            map.put("status", 0);//失败
        }
       return new JsonResult(1,map);

    }

    /**
     * <AUTHOR>
     * @Date 2018/4/13 9:43
     * 新增面试意见
     */
    @ResponseBody
    @RequestMapping("/addPersonnelInterview.do")
    public void addPersonnelInterview(User user, PersonnelInterview p,String interviewTime1,String dutyTime1, Integer offerId, HttpServletResponse response) throws IOException, ParseException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer state=0;
        String cont="提交失败，参数传的不对";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        if (userId==null){
//            userId=user.getUserID();
//        }

        if (offerId != null) {

            ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessByBusToUser(offerId,16,user.getUserID());
            if (approvalProcess!=null) {
                if (interviewTime1 != null && !interviewTime1.equals("")) {
                    p.setInterviewTime(sdf.parse(interviewTime1));
                }
                if (dutyTime1 != null && !dutyTime1.equals("")) {
                    p.setDutyTime(sdf.parse(dutyTime1));
                }
                p.setInterviewer(user.getUserID());
                p.setInterviewerName(user.getUserName());
                p.setCreateDate(new Date());
                p.setCreator(user.getUserID());
                p.setCreateName(user.getUserName());
                p.setOffer(offerId);
                personnelInterviewService.savePersonnelInterview(p);
                map.put("personnelInterview", p);

                RdResume rdResume=rdResumeService.getRdResumeByOfferId(offerId);
                if (rdResume!=null){
                    rdResume.setState(3);
                    rdResumeService.updateRdResume(rdResume);
                }
                state=1;
                cont="提交成功";
            }else {
                state=2;// 您已不是该应聘者的面试官，不能提交
                cont="您已不是该应聘者的面试官，不能提交";
            }
        }
        map.put("status",state);//状态
        map.put("cont",cont);//提示信息
        ObjectToJson.objectToJson1(map, new String[]{"user"}, response);
    }

    /**
     * <AUTHOR>
     * @Date 2018/4/13 9:58
     * 招聘人员入职
     */
    @ResponseBody
    @RequestMapping("/offerToUser.do")
    public JsonResult offerToUser(User user, Integer offerId, Integer ordinaryEmployees, Integer manager, Integer leaderId, Integer department
            , Long postId, String onDutyDate, String phone,String manageCode) throws ParseException, IOException {

        Integer state=0;
        if (offerId != null) {
//            User user = (User) session.getAttribute("user");//操作人
//            if (user!=null){
//                userId=user.getUserID();
//            }
//            user = userService.getUserByID(userId);

            User userByLogonName = userService.getUserByOidAndPhone(user.getOid(),phone);
            if(userByLogonName!=null) {
                state=2;//手机号已存在
            }else {
//                User manage = userService.getUserByID(manager);//所属高管

                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date onDutyDate1 = new Date();
                if (onDutyDate != null && !"".equals(onDutyDate)) {
                    onDutyDate1 = simpleDateFormat.parse(onDutyDate);
                }
                Offer offer = offerService.getOfferById(offerId);
                User user1 = new User();
                user1.setUserName(offer.getUserName());//用户姓名
                user1.setRealName(offer.getUserName());//真实姓名
                user1.setGender(offer.getGender());//性别
                user1.setSuperId(offer.getSuperId());//薪资宝的
                user1.setEmail(offer.getEmail());//电子邮箱
                user1.setQq(offer.getQq());//qq号
                user1.setPoliticalStatus(offer.getPoliticalStatus());//政治面貌
                user1.setMarry(offer.getMarry());//0已婚。1未婚
                user1.setNation(offer.getNation());//民族
                user1.setIdCard(offer.getIdCard());//身份证号
                user1.setHomeAddress(offer.getHomeAddress());//家庭地址
                user1.setInteresting(offer.getInteresting());  //兴趣
                user1.setSpeciality(offer.getSpeciality());  //爱好
                user1.setNativePlace(offer.getNativePlace());  //籍贯
                user1.setEdu(offer.getEdu());  //最高学历
                user1.setCreateTime(new Date());//创建时间
                user1.setCreator(user.getUserID());//创建者
                user1.setCreateName(user.getUserName());//创建者
                user1.setStatus("2");////状态：1-禁用 其它-启用
                user1.setIsDuty("1");//1表示在职，2表示离职 ,3可登录的代理人员
                user1.setOfferStatus("2");//招聘状态:1-待入职,2-已入职,3-已拒绝
//                user1.setLogonName(phone);
                user1.setCompany(offer.getCompany());//所属公司
                user1.setTransferReason(offer.getTransferReason());//转岗原因
                user1.setWorkYear(offer.getWorkYear());//工作年限
                user1.setDegree(offer.getDegree());//学历 '1-小学以下，2-小学，3-初中，4-高中，5-中专，6-大专 7-本科，8-硕士，9-博士
                user1.setResidence(offer.getResidence());//户籍
                user1.setNativeType(offer.getNativeType());//户籍性质
                user1.setAddress(offer.getAddress());//身份证住址
                user1.setActualAddress(offer.getActualAddress());//实际住址
                user1.setPay(offer.getPay());//薪资
                user1.setOfferSalaty(offer.getOfferSalaty());//期望薪资
//            user1.setUserType(10);//代表用户的类型 目前暂定为：0：超级管理员  1：财务  2：浏览者 3:总务 4:销售
                user1.setRemark(offer.getRemark());//备注
                user1.setRoleCode("staff");  //此员工的权限
                Organization o = orgService.getByOid(user.getOid());
                user1.setOrganization(o);
                if (offer.getBirthday()!=null){
                    user1.setBirthday(offer.getBirthday());
                    user1.setAge(NewDateUtils.getYear(new Date())-NewDateUtils.getYear(offer.getBirthday()));
                }

                if (phone.length() == 11) {
//                    user1.setLogonPwd(userService.getLogonPwdByPhone(phone));//获取该有的登录密码
                    user1.setMobile(phone);//手机号
                }
                user1.setOnDutyDate(onDutyDate1);//入职时间
                user1.setOrdinaryEmployees(ordinaryEmployees);//是否为普通员工 1-普通员工
//                user1.setManager(manager);
//                user1.setManagerCode(manage.getManagerCode());
                if (manageCode!=null&&!manageCode.isEmpty()){
                    manageCode="general";
                }
                user1.setManagerCode(manageCode);
                User pUser = userService.getUserByID(leaderId);
                if (pUser != null) {
                    user1.setLeader(leaderId.toString());//从属于某位领导
                    String rankUrl = pUser.getRankUrl() + leaderId;
                    user1.setRankUrl(rankUrl);//从属关系
//                    if ("super".equals(pUser.getRoleCode())) {
//                        user1.setLeaderName("总经理");//从属于某位领导
//                    } else {
                        user1.setLeaderName(pUser.getUserName());//从属于某位领导
//                    }
                }
                if (department != null) {
                    Organization depart = orgService.getOrgByOid(department, OrgService.OrgType.department);//部门
                    user1.setDepartment(department.toString());
                    user1.setDepartName(depart.getName());
                }
                if (postId != null) {
//                    Organization post = orgService.getOrgByOid(postId, OrgService.OrgType.post);//职位
                    RdPost post=rdPostService.getRdPostById(postId);
                    user1.setPostID(postId.toString());
                    user1.setPostName(post.getName());
                }
//                AuthAcc authAcc= authService.newEnabledAcc(user1.getMobile(),user1.getUserName());// 生成登陆账号
//                user1.setAccId(authAcc.getId());
                userService.addUser(user1, o);
                if (offer.getImgPath()!=null&&!offer.getImgPath().isEmpty()) {
                    user1.setImgPath(offer.getImgPath());
                    uploadService.addFileUsing(new UserUsing(user1.getUserID()), user1.getImgPath(), user1.getUserName()+"(招聘入职)", user1, "职工头像");
                }
                userService.updateUser(user1);

                userPopedomService.saveUserPopedomByCode("staff", user1);//给普通员工附上权限
//                userService.getPopedomStringByUser(user1,true);//变更权限时用。
//                popedomTaskService.updateUserBadgeNumber(user1);//重新计算角标

                List<PersonalEducation> peList = offerService.getPersonalEducationListByOfferId(offerId);
                List<PersonnelOccupation> poList = offerService.getPersonnelOccupationListByOfferId(offerId);
                List<PersonnelFolks> pfList = offerService.getPersonnelFolksListByOfferId(offerId);
                List<PersonnelInterview> piList = offerService.getPersonnelInterviewByOfferId(offerId);
                for (PersonalEducation p : peList) {
                    p.setUser(user1);
                    personalService.updatePersonalEducation(p);
                }
                for (PersonnelOccupation p : poList) {
                    p.setUser(user1);
                    personalService.updatePersonnelOccupation(p);
                }
                for (PersonnelFolks p : pfList) {
                    p.setUser(user1);
                    personalService.updatePersonnelFolks(p);
                }
                for (PersonnelInterview p : piList) {
                    p.setUser(user1);
                    personnelInterviewService.updatePersonnelInterview(p);
                }
                offer.setHandleTime(new Date());
                offer.setOfferStatus("2");//招聘状态:1-待入职,2-已入职,3-已拒绝
                offer.setCreateName(user.getUserName());
                offer.setCreator(user.getUserID());
                offer.setDepartment(user1.getDepartment());
                offer.setDepartName(user1.getDepartName());
                offer.setOrdinaryEmployees(user1.getOrdinaryEmployees());
                offer.setLeader(user1.getLeader());
                offer.setLeaderName(user1.getLeaderName());
                offer.setPostID(user1.getPostID());
                offer.setPostName(user1.getPostName());
                offer.setOnDutyDate(user1.getOnDutyDate());
                offer.setMobile(user1.getMobile());
                offer.setManagerCode(user1.getManagerCode());
                offer.setRoleCode(user1.getRoleCode());
                offerService.updateOffer(offer);

                List<ApprovalProcess> approvalProcessList=approvalProcessService.getApprovalProcessByBusiness(offerId,16,"1");
                for (ApprovalProcess approvalProcess:approvalProcessList){
                    approvalProcess.setApproveStatus("2");
                    approvalProcessService.updateApprovalProcess(approvalProcess);//处理变成驳回
                    personalService.offerRejectSend(-1, -1, offer, approvalProcess.getToUser(), "/offerApproval", null, null, "offerApproval");//所有给面试官消减角标和数据
                }

                RdResume rdResume=rdResumeService.getRdResumeByOfferId(offerId);
                if (rdResume!=null){
                    rdResume.setState(5);
                    rdResume.setResult(1);
                    rdResumeService.updateRdResume(rdResume);
                }
                state=1;  //成功
            }
        }
        return  new JsonResult(1,state);

    }

    /**
     * <AUTHOR>
     * @Date 2018/4/13 15:58
     * 未通过面试
     */
    @ResponseBody
    @RequestMapping("/offerFalse.do")
    public JsonResult offerFalse(Integer offerId, User user) throws Exception {
        Integer state=0;
        if (offerId != null) {
            Offer offer = offerService.getOfferById(offerId);

//            User user= (User) session.getAttribute("user");
//            if (userId==null&&user==null){
//                throw new Exception("参数userId不能为空！");
//            }
//            userId=userId==null?user.getUserID():userId;
//
//            user = userService.getUserByID(userId);
            offer.setOfferStatus("3");//招聘状态:1-待入职,2-已入职,3-已拒绝
            offer.setCreateName(user.getUserName());
            offer.setHandleTime(new Date());
            offerService.updateOffer(offer);

//            List<Integer> userIds=approvalProcessService.getUserIdsByBusiness(offerId,16);
            List<ApprovalProcess> approvalProcessList=approvalProcessService.getApprovalProcessByBusiness(offerId,16,"1");
//            for (Integer userId:userIds) {
            for (ApprovalProcess approvalProcess:approvalProcessList){
                approvalProcess.setApproveStatus("3");
                approvalProcessService.updateApprovalProcess(approvalProcess);//处理变成驳回
                personalService.offerRejectSend(-1, -1, offer, approvalProcess.getToUser(), "/offerApproval", null, null, "offerApproval");//所有给面试官消减角标和数据
            }
            RdResume rdResume=rdResumeService.getRdResumeByOfferId(offerId); //面试者简历
            if (rdResume!=null){
                rdResume.setState(4);
                rdResume.setResult(0); //结果中止
                rdResume.setUpdator(user.getUserID());
                rdResume.setUpdateName(user.getUserName());
                rdResumeService.updateRdResume(rdResume);
            }
            state=1;      //成功
        }
        return  new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2018/4/17 10:59
     * 查看面试意见
     */
    @ResponseBody
    @RequestMapping("/getPersonnelInterviewList.do")
    public void getPersonnelInterviewList(Integer id, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (id != null) {
            Offer offer = offerService.getOfferById(id);
            List<PersonnelInterview> piList = offerService.getPersonnelInterviewByOfferId(id);
            offer.setPersonnelInterviews(piList);//面试意见
            List<PersonalEducation> peList = offerService.getPersonalEducationListByOfferId(id);
            List<PersonnelOccupation> poList = offerService.getPersonnelOccupationListByOfferId(id);
            List<PersonnelFolks> pfList = offerService.getPersonnelFolksListByOfferId(id);
            offer.setPersonalEducations(peList);//教育经历
            offer.setPersonnelOccupations(poList);//工作经历
            offer.setPersonnelFolkses(pfList);//家庭成员
            map.put("status", 1);//成功
            map.put("offer", offer);
        } else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map, new String[]{"user"}, response);
    }

    /**
     * Move from RecruitmentController(deleted) by wyu on 20180424.
     */
    @ResponseBody
    @RequestMapping("/getQRLink.do")
    public JsonResult getQRLink(User user, HttpServletRequest request, Long expireDate) {
        final String salt = "cRDZsFXS";
        final String enc = "UTF-8";
        if (expireDate==null || expireDate<System.currentTimeMillis()) {
            expireDate = NewDateUtils.changeDay(new Date(System.currentTimeMillis()), 8).getTime();//wyu：1+7==二维码一周有效
        }
//        User user = getUser(session,userId);
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", user.getUserID());
        params.put("oid", user.getOid());
        params.put("expire_date", expireDate);
        final JsencryptUtils jsencryptUtils = new JsencryptUtils();
        params.put("salt", DigestUtils.digest(salt + user.getUserID().toString() + user.getOid().toString() + expireDate.toString(), DigestUtils.SHA1));
        String item = jsencryptUtils.encrypt(JSON.toJSONString(params));
        if (item != null && !item.isEmpty()) {
            try {
                item = URLEncoder.encode(item, enc);
                return new JsonResult(1, GetLocalIPUtils.getRootPath(request) + "/vue/recruit/dist/index.html#/item/" + item);
            } catch (UnsupportedEncodingException e) {
                Logger.getLogger(getClass()).error("二维码链接生成错误 UnsupportedEncodingException",e);
            }
        }
        return new JsonResult(new MyException("500","二维码链接生成错误"));
    }
    private User getUser(User user, Integer userId) {
//        User user = (User) session.getAttribute("user");
//        if (user == null && userId != null) {
//            user = userService.getUserByID(userId);
//        }
        if (user==null) {
            throw new IllegalArgumentException("user must not be null(用户不能为空)!");
        }
        return user;
    }

    /**
     * <AUTHOR>
     * @Date 2018/5/4 14:46
     * 入职前选择的
     */
    @ResponseBody
    @RequestMapping("/getSelect.do")
    public void getSelect(User user,Integer userId,HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        Integer  oid = user.getOid();
        List<Organization> organizations = orgService.getAllDepartmentById(oid);//获取机构全部门
        map.put("departmentList", organizations);
        List<RdPost> posts = rdPostService.getRdPostsByOid(oid,true);//新版 职位取值 2023/5/25
        map.put("postList", posts);

        List<User> userList = new ArrayList<User>();
        List<User> manages = new ArrayList<>();
//        User superUser = userService.getUserByRoleCode(oid, "smallSuper");
//        if (superUser==null){
//            superUser = userService.getUserByRoleCode(oid, "super");
//        }
        User superUser=userService.getUserByRoleCode(oid,"super");
        if (superUser!=null) { //董事长也有可能失效取不到
            superUser.setManageName("超管");
            userList.add(superUser);
        }

        User smallSuper=userService.getUserByRoleCode(oid,"smallSuper");
        if (smallSuper!=null) { //总经理有可能不能存在或者失效取不到
            smallSuper.setManageName("小超管");
            userList.add(smallSuper);
        }

        User general = userService.getUserByRoleCode(oid, "general");
        if (general!=null){
            general.setManageName("总务");
        }else {
            general=new User();
            general.setManagerCode("general");
            general.setRoleCode("general");
        }
        manages.add(general);

//        userList.add(general);
        User finance = userService.getUserByRoleCode(oid, "finance");
        if (finance!=null){
            finance.setManageName("财务");
//            userList.add(finance);
        }else {
            finance=new User();
            finance.setManagerCode("finance");
            finance.setRoleCode("finance");
        }
        manages.add(finance);


        User sale = userService.getUserByRoleCode(oid, "sale");
        if (sale!=null) {
            sale.setManageName("销售");
//            userList.add(sale);
        }else {
            sale=new User();
            sale.setManagerCode("sale");
            sale.setRoleCode("sale");
        }
        manages.add(sale);

//        User accounting = userService.getUserByRoleCode(oid, "accounting");
        User accounting = null;
        accounting = userService.getUserByRoleCode(oid, "accounting");
        if (accounting!=null) {
            accounting.setManageName("会计");
//            userList.add(accounting);
        }else {
            accounting=new User();
            accounting.setManagerCode("accounting");
            accounting.setRoleCode("accounting");
        }
        manages.add(accounting);

        if(userId==null) {
            List<User> users = userService.getUserByOid(oid, null);
            for (User u : users) {
                if ((u.getOrdinaryEmployees() == null || u.getOrdinaryEmployees() == 0) && !"2".equals(u.getIsDuty()) && !u.getRoleCode().equals(u.getManagerCode())) {
                    userList.add(u);
                }
            }
        }else {
            userList.clear();
            List<User> users =userService.getNotDirectLowerGrade(oid,userId);
            for (User u : users) {
                if ((u.getOrdinaryEmployees() == null || u.getOrdinaryEmployees() == 0) && !"2".equals(u.getIsDuty())) {
                    userList.add(u);
                }
            }
            userList.add(superUser);
        }
        map.put("manageList", manages);
        map.put("userList", userList);//直属上级列表
        ObjectToJson.objectToJson1(map, new String[]{"user","area","financeAccounts","accountDetails","accountPeriods","mtCategoryOrg","financeReturnHashSet","financeChequeHashSet","orgPopedomHashSet","personnelOvertimeUser", "personnelLeaveUser", "parent", "roles", "educationClasses", "teacherRecommend", "organization", "educations", "leaves"
                , "userMessages", "personnelOccupationUser", "personalEducationUser", "personnelSalaryLogUser", "personalRewardPunishmentUser", "personalAssessmentUser"
                , "userFeedbackUser", "userLogs", "opMemberAssignerUser", "opMemberTracerUser", "opMemberSubmitterUser", "opTraceTracerUser", "opTraceTracerDetailUser", "inputStream", "volumeM"
                , "volumeY", "transferTime", "page", "offDutyDate", "onDutyDate", "volume", "submit", "submitM", "submitY", "password"
                , "money", "moneyY", "moneym", "lv", "lvM", "lvY", "logonState", "new", "newDiary", "contentApprovalHashSet", "contentHashSet", "personnelReimburseHashSet", "roles", "userPopedomHashSet", "personnelFolksHashSet", "personnelInterviewHashSet", "personalAssessments", "personalEducations", "personalRewardPunishments", "personnelOccupations"}, response);

    }

    /**
     * <AUTHOR>
     * @Date 2018/5/15 9:38
     * 招聘人员 上传图片
     */
    @ResponseBody
    @RequestMapping("/uploadOfferImages.do")
    public void uploadImages(HttpServletRequest request,HttpServletResponse response,@RequestParam(value = "imgFile",required = false) MultipartFile[] files) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        String path="";
        if (files!=null){
            for (MultipartFile f : files) {
                path=UploadImages.uploadImages(request,f);//存图片的
            }
            map.put("status", 1);//成功
            map.put("imagePath",path);

        }else {
            map.put("status", 0);//失败

        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

    }

    /**
    * <AUTHOR>
    * @Date 2020/4/24 17:41
    * 上传多张图片
    */
    @ResponseBody
    @RequestMapping("/uploadImages.do")
    public void uploadImagess(HttpServletRequest request,HttpServletResponse response,@RequestParam(value = "image",required = false) MultipartFile[] files) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        List<String> pathList=new ArrayList<String>();
        if (files!=null){
            for (MultipartFile f : files) {
                pathList.add(UploadImages.uploadImages(request,f));//存图片的路径
            }
            map.put("status", 1);//成功
            map.put("imagePaths",pathList);

        }else {
            map.put("status", 0);//失败

        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

    }

    /**
    * <AUTHOR>
    * @Date 2019/11/5 15:03
    * 悬浮窗招聘管理
    */
    @ResponseBody
    @RequestMapping("/getOffers.do")
    public JsonResult getOffers(User user){
//        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(json);
//        String sessionid=jsonObject.getString("session");
//        Integer oid=jsonObject.getInteger("oid");
//        List<Offer>  offerList = offerService.getOfferByOfferStatus(oid,"1",null);// offerStatus 1-待入职,2-已入职,3-已拒绝
        List<Integer> business=approvalProcessService.getBusinessByToUserBusinessType(user.getUserID(),16);
        List<Offer> offerList=new ArrayList<>();
        if (business.size()>0) {
            offerList = offerService.getOfferListByIds(business,"1");
        }
//        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/applyOutTimeHandle", null, null, null, null, JSON.toJSONString(untreated));//推送 待处理
//        Map<String, Object> map = new HashMap<>();
//        map.put("offers", offerList);
        return new JsonResult(1, offerList);
    }
    
    /**
    * <AUTHOR>
    * @Date 2019/11/5 16:08
    * 获取可选择的面试官
    */
    @ResponseBody
    @RequestMapping("/getInterviewers.do")
    public JsonResult getInterviewers(User user,Integer offerId,PageInfo pageInfo){
        List<Integer> userIds=approvalProcessService.getUserIdsByBusiness(offerId,16);//已经给此应聘者选择过的面试官id
        List<User> userList=userService.getUserNoGeneralByOid(user.getOid(),userIds,pageInfo);//获取可选面试官列表
        return new JsonResult(1,userList);
    }


    /**
    * <AUTHOR>
    * @Date 2019/11/7 14:15
    * 给应聘者选择 面试官
    */
    @ResponseBody
    @RequestMapping("/choiceInterviewers.do")
    public JsonResult choiceInterviewers(User user,Integer offerId,String json){
        Integer state=0;
        if (offerId!=null&&json!=null) {
//            List<Integer> interviewersList=new ArrayList<>(interviewers.length);
//            Collections.addAll(interviewersList,interviewers);// 转成list用于后面取差集
//            List<Integer> userIds=approvalProcessService.getUserIdsByBusiness(offerId,16);//已经给此应聘者选择过的面试官id
//            if (userIds!=null) {
//                interviewersList.removeAll(userIds);//新面试官列表 排除 已选择的面试官列表
//            }
            Offer offer = offerService.getOfferById(offerId);
            List<Integer> userIds=approvalProcessService.getUserIdsByBusiness(offer.getUserID(),16);//已经给此应聘者选择过的面试官id

            RdResume rdResume=rdResumeService.getRdResumeByOfferId(offerId); //面试者简历

            JSONArray jsonArray = JSONArray.fromObject(json);
            for (int i=0;i<jsonArray.size();i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                Integer handleId = jsonObject.getInt("interviewer"); //面试官id
                Date handleDate = NewDateUtils.dateFromString(jsonObject.getString("planTime"), "yyyy-MM-dd HH:mm:ss"); //面试时间
                approvalProcessService.choiceInterviewers(offer, handleId, handleDate, user);//给应聘者分配面试官

                if (rdResume!=null){
                    rdResume.setSendTime(handleDate);
                }
                rdResumeService.updateRdResume(rdResume);

            }
            state=1;
        }
        return new JsonResult(1,state);
    }
    
    /**
    * <AUTHOR>
    * @Date 2019/11/12 11:11
    * 查看面试官对某应聘者的面试意见
    */
    @ResponseBody
    @RequestMapping("/getPersonnelInterviewInfo.do")
    public JsonResult getPersonnelInterviewInfo(Integer id){
        PersonnelInterview personnelInterview=personnelInterviewService.getPersonnelInterviewById(id);
        return new JsonResult(1,personnelInterview);
    }

    /**
    * <AUTHOR>
    * @Date 2019/11/13 10:04
    * 获取应聘者当前面试官列表
    */
    @ResponseBody
    @RequestMapping("/getInterviewersByOfferId.do")
    public JsonResult getInterviewersByOfferId(Integer offerId){
//        List<Integer> userIds=approvalProcessService.getUserIdsByBusiness(offerId,16);// 获取应聘者最新面试官列表
//        userIds.remove(0);// 去掉总务的流程
//        List<User> userList=userService.getUsersByUserIds(userIds);
        List<User> userList=new ArrayList<>();
        List<ApprovalProcess> approvalProcessList=approvalProcessService.getApprovalProcessByBusiness(offerId,16,null);
        approvalProcessList.remove(0);// 去掉总务的流程
        for (ApprovalProcess approvalProcess:approvalProcessList){
            User user=userService.getUserByID(approvalProcess.getToUser());
            user.setPlanTime(approvalProcess.getHandleTime());
            userList.add(user);
        }
        return new JsonResult(1,userList);
    }

    /**
    * <AUTHOR>
    * @Date 2019/11/13 10:22
    * 删除应聘者某个面试官
    */
    @ResponseBody
    @RequestMapping("/deleteInterviewerById.do")
    public JsonResult deleteInterviewerById(Integer offerId,Integer interviewerUserId){
        Integer state=0;
        if (offerId!=null&&interviewerUserId!=null) {
            ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessByBusToUser(offerId,16,interviewerUserId);
            Integer toUserId=approvalProcess.getToUser();
            Offer offer=offerService.getOfferById(approvalProcess.getBusiness());
            approvalProcessService.deleteApprovalProcess(approvalProcess);

            personalService.offerRejectSend(-1, -1, offer, toUserId, "/offerApproval", null, null, "offerApproval");//给面试官 推送删除数据

            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/11/13 11:06
    * 招聘管理查询
     * * type 1-近七日，2-本月，3-自定义
    */
    @ResponseBody
    @RequestMapping("/offerManageQuery.do")
    public JsonResult offerManageQuery(User user,Integer type,String beginDate,String endDate,Integer offerStatus,PageInfo pageInfo) throws ParseException, IOException {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=new Date();
        Date end=new Date();
        HashMap<String,Object> hashMap=new HashMap<>();
        switch (type){
            case 1:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin=NewDateUtils.changeDay(end,-6);
                beginDate=sdf.format(begin);
                endDate=sdf.format(end);
                break;
            case 2:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin= NewDateUtils.changeMonth(new Date(),0);  //获取本月第一天
                beginDate=sdf.format(begin);
                endDate=sdf.format(end);
                break;
            case 3:
                begin=sdf.parse(beginDate);
                end=sdf.parse(endDate);
                end=NewDateUtils.getLastTimeOfDay(end);
                break;
        }
        List<Offer> offerList=offerService.getOfferByOfferStatus(user.getUserID(),offerStatus.toString(),begin,end,pageInfo);
        hashMap.put("beginDate",beginDate);
        hashMap.put("endDate",endDate);
        hashMap.put("offerStatus",offerStatus);
        hashMap.put("offerList",offerList);
        hashMap.put("pageInfo",pageInfo);
        return new JsonResult(1,hashMap);

    }

    @ResponseBody
    @RequestMapping("testgetUser.do")
    public void  testgetUser(){
       User user= userService.getUserByOidAndPhone(2583,"18611040009");
        System.out.println();
    }

}
