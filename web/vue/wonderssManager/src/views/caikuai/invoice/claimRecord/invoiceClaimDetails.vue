<template>
  <!--  <p>发票详情</p>-->
  <div class="box">
    <div class="ty-fff">
      <p>
        <span>{{ cname }}</span>
        <span>起止号码{{ cid1 }}-{{ cid2 }}</span>
      </p>
      <p>发票代码{{ invoiceId }}</p>
      <p>
        申领
        <span class="padding-0-10">{{ name }}</span>
        {{ date }}
      </p>
      <p>
        录入者
        <span class="padding-0-10">{{ inputer }}</span>
        {{ inputDate }}
      </p>
    </div>
    <ul class="ty-fff margin_top_16">
      <li v-for="item in list1" :key="item.id">
        <div>
          <span>
            <span class="padding-r-5">发票号码</span>
            {{ item.id }}
          </span>
          <span v-show="item.sum">
            <span class="padding-r-5">金额</span>
            {{ item.sum }}元
          </span>
          <span v-show="item.invoiceDate">
            <span class="padding-r-5">发票日期</span>
            {{ item.invoiceDate }}
          </span>
          <span v-show="item.customerName">
            <span class="padding-r-5">开给</span>
            {{ item.customerName }}
          </span>
          <p v-show="item.name">
            <span class="padding-r-5">
              开票申请
            </span>
            {{ item.name }}<span>{{ item.inputerDate }}</span>
          </p>
          <p v-show="item.not" style="width: 100%">尚未使用</p>
          <p v-show="item.void" style="width: 100%">已作废</p>
        </div>
        <el-icon style="font-size: 20px">
          <arrow-right/>
        </el-icon>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "invoiceClaimDetails",
  data() {
    return {
      cname: "增值税XXXXX发票",
      cid1: "123456",
      cid2: "789654",
      invoiceId: "(发票代码)",
      name: "刘总监",
      date: "2024-01-01",
      inputer: "王如意",
      inputDate: "2024-10-01 11:12:23 ",
      list1: [
        {
          id: 1111,
          sum: 66.66,
          invoiceDate: "2024-10-01",
          customerName: "天津XX有限责任公司XXX",
          name: "刘总监",
          inputerDate: "2024-11-01 13:27"
        }, {
          id: 3333,
          sum: 50000.00,
          invoiceDate: "2024-10-01",
          customerName: "北京XXX公司",
          name: "刘总监",
          inputerDate: "2024-11-01 13:27"
        }, {
          id: 44444,
          sum: 66.66,
          invoiceDate: "2024-10-01",
          customerName: "天津XX有限责任公司XXX",
          name: "刘总监",
          inputerDate: "2024-11-01 13:27"
        },
        {id: 2222, not: true},
        {id: 6666, void: true},
      ]
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/account.scss";
@import "src/style/common.scss";

.box {
  background-color: $tyColorBg;

  > div:nth-of-type(1) {
    padding: 5px 15px;

    > p {
      line-height: 25px;
    }

    > p:first-child {
      display: flex;
      justify-content: space-between;

      > span:first-child {
        width: 40%;
      }

      > span:nth-child(2) {
        width: 55%;
      }
    }

    > p:last-child {
      text-align: end;
    }
  }
}

ul {
  li {
    padding: 10px 15px;
    display: flex;
    align-items: center;

    > div {
      width: 95%;
      display: flex;
      flex-wrap: wrap;
      line-height: 25px;

      > span {
        width: 50%;
      }
    }
  }

  > li:nth-of-type(even) {
    background-color: $tyBounceColorGray;
  }
}
</style>