<template id="resumeRecord">
  <div class="resumeRecord">
    <van-nav-bar
      title="其他数据"
      left-arrow
      @click-left="onClickLeft"
    />
    <van-overlay :show="loading">
      <div class="wrapper">
        <van-loading />
      </div>
    </van-overlay>
    <div class="main_container">
      <van-cell-group>
        <van-cell>
          <van-row gutter="8">
            <van-col span="8"><b>招聘单位</b></van-col>
            <van-col span="12"><b>简历提交时间</b></van-col>
            <van-col span="4"><b>操作</b></van-col>
          </van-row>
        </van-cell>
        <van-cell v-for="(item, index) in list" v-bind:key="index" @click="goInfo(item.offer)">
          <van-row gutter="8">
            <van-col span="8">{{item.orgName}}</van-col>
            <van-col span="12">{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</van-col>
            <van-col span="4"><van-icon name="arrow" /></van-col>
          </van-row>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<style>
body{
  padding:0;
  margin: 0;
}
.td{
  display: inline-block;
}
.wrapper{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>

<script>
// wyu：引相关的库
export default {
  name: 'resumeRecord',
  data () {
    return {
      list: [],
      loading: true
    }
  },
  created () {
    let id = this.$route.params.id
    let owxrecord = localStorage.getItem('wxrecord')
    if (owxrecord) {
      owxrecord = JSON.parse(owxrecord)
    }
    let that = this
    this.$http.get(window.parent.$.webRoot + '/resume/getResumeOtherHistories.do?hashKey=' + owxrecord.hash + '&rdResumeId=' + id).then(res => {
      console.log(res)
      let data = res.body.data
      that.list = data
      that.loading = false
    }).catch(err => {
      console.log(err)
    })
  },
  methods: {
    onClickLeft () {
      this.$router.go(-1)
    },
    goInfo (id) {
      this.$router.push({
        path: `/resume/${id}`
      })
    }
  }
}
</script>
