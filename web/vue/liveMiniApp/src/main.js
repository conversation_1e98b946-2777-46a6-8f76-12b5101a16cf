import { createApp } from 'vue'
import App from './App.vue'
import router from '@/router'

import ElementPlus from "element-plus"
import "element-plus/dist/index.css"
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// import * as ElementIcon from '@element-plus/icons'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 引入权限相关js
import auth from './sys/auth'
import initStorage from './sys/initStorage'
import sphdSocket from './sys/sphd'
import store from "./store";



// console.log("router==", router);
// console.log('VUE_APP_WEB_ROOT',process.env.VUE_APP_WEB_ROOT)

import '@/utils/DateFormat.js'
// 引入全局组建
import Pageheader from './components/PageHeader.vue'


const app = createApp(App)
    .use(router)
    .use(store)
    .use(ElementPlus, {locale: zhCn})
    // .use(ElementPlus, { size: 'small', zIndex: 3000 })
    // .use(ElementPlus)
    .component('Pageheader',Pageheader)

// svg icon 引入
// for(const icon in ElementIcon) {
//     app.component(icon, ElementIcon[icon])
// }
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

// console.log('如何让调用呢')
// tt.miniProgram.checkJsApi({
//     success(res) {
//         console.log("getUserProfile 调用成功：", res.userInfo);
//     },
//     fail(res) {
//         console.log("getUserProfile 调用失败", res);
//     },
// })



console.log('auth.init')
auth.init({isGuest: true})
initStorage({})
sphdSocket.start()
parent.sphdSocket = sphdSocket
app.mount('#app')



