package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxReportDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxReportHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxReportHistoryEntity;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class AcctTaxReportHistoryDaoImpl extends BaseDao<AcctTaxReportHistoryEntity, Serializable> implements AcctTaxReportHistoryDao {
}
