<template>
  <div id="willCleanFile">
    <TY_NavTop title="即将消失的通知"></TY_NavTop>
    <div class="container">
      <div class="nowTime">
        下列消息即将消失！
      </div>
      <div class="container">
        <!--文件停用通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 6 || fileNotice.type === 9">
          <div class="ty-fileRow">
            <div :class="'fileType file_'+item.version"></div>
            <div class="ty-fileInfo">
              <div class="ty-fileRow">
                <div class="ty-fileName" :title="item.fileName">{{item.fileName}}</div>
              </div>
              <div class="ty-fileRow">
                <div class="ty-fileNoVersion" :title="item.fileSn">G{{item.changeNum}} {{item.fileSn}}</div>
              </div>
            </div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件夹停用通知-->
        <div class="ty-fileItem active" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 7" @click="childFolder(item)">
          <div class="ty-fileRow">
            <div class="icon_folder"></div>
            <div class="ty-fileName" :title="item.categoryName">{{item.categoryName}}</div>
            <div class="ui-cell__ft"></div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--表格关联通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 10">
          <div class="ty-fileRow">
            <div class="item_title">文件编号/文件名称</div>
            <div class="item_content">{{item.allFileName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">表格编号/表格名称</div>
            <div class="item_content">{{item.allFormName}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--表格解除关联通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 11">
          <div class="ty-fileRow">
            <div class="item_title">文件编号/文件名称</div>
            <div class="item_content">{{item.allFileName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">表格编号/表格名称</div>
            <div class="item_content">{{item.allFormName}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件位置移动的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 12">
          <div class="ty-fileRow">
            <div class="item_title">文件编号/名称</div>
            <div class="item_content">{{item.allFileName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">原位置</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">新位置</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件夹位置移动的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 13">
          <div class="ty-fileRow">
            <div class="item_title">文件夹名称</div>
            <div class="item_content">{{item.categoryName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">原位置</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">新位置</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件编号/名称修改的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 14">
          <div class="ty-fileRow">
            <div class="item_title">原编号/名称</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">改后的编号/名称</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件夹名称修改的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 15">
          <div class="ty-fileRow">
            <div class="item_title">原名称</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">改后的名称</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--表格编号/名称修改的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 16">
          <div class="ty-fileRow">
            <div class="item_title">原编号/名称</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">改后的编号/名称</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue">{{item.noticeDisappear}}</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.ty-fileItem{
  background-color: #fff;
  cursor: default;
  position: relative;
  font-size: 13px;
  padding: 8px 12px;
  .ty-fileInfo{ flex: auto }
  .ty-fileName{ width:200px; word-wrap:break-word;  overflow:hidden; text-overflow:ellipsis; white-space: nowrap; flex: none }
  .ty-fileNoVersion{  width:100px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; font-size:12px; color:#999; flex: auto}
  .ty-fileDetail{  color: #aaa;  font-size: 12px;  font-weight: 300; margin-right: 16px; text-align: right}
  .ty-fileHandle{
    margin-top: 4px;
    .link-blue{
      flex: none;
    }
    .ty-fileDetail{
      flex: auto;
    }
  }
  &.active{
    cursor: pointer;
    &:hover{
      background: #f3f3f3;
    }
  }
}
.ty-fileRow { display: flex }
.item_title{
  width: 120px;
  flex: none;
  color: #333;
}
.item_content{
  flex: auto;
}
</style>

<script>
export default {
  name: 'forumApproval',
  data () {
    return {
      dataList: [],
      loading: false,
      fileNotice: {
        title: '',
        en: '',
        type: 0,
        tips: '',
        needContactDM: false,
        hasBtn: false
      },
      listenersUid: []
    }
  },
  created () {
    let name = this.$route.params.name
    let that = this
    let fileNotice = {}
    switch (name) {
      case 'needBatchReceiptFile':
        fileNotice = {
          title: '需批量签收的文件',
          en: 'needBatchReceiptFile',
          type: 1
        }
        break
      case 'newSecuringPowerFile':
        fileNotice = {
          title: '新获得使用权限的文件',
          en: 'newSecuringPowerFile',
          type: 2
        }
        break
      case 'fileVersionChangeNotice':
        fileNotice = {
          title: '文件换版通知',
          en: 'fileVersionChangeNotice',
          type: 3
        }
        break
      case 'fileCancelNotice':
        fileNotice = {
          title: '文件废止通知',
          en: 'fileCancelNotice',
          type: 4
        }
        break
      case 'fileReturnNotice':
        fileNotice = {
          title: '废止文件的复用通知',
          en: 'fileReturnNotice',
          type: 5
        }
        break
      case 'fileDeadNotice':
        fileNotice = {
          title: '文件停用通知',
          en: 'fileDeadNotice',
          type: 6
        }
        break
      case 'folderDeadNotice':
        fileNotice = {
          title: '文件夹停用通知',
          en: 'folderDeadNotice',
          type: 7
        }
        break
      case 'deadFileReturnNotice':
        fileNotice = {
          title: '停止使用文件的复用通知',
          en: 'deadFileReturnNotice',
          type: 8
        }
        break
      case 'revokePowerNotice':
        fileNotice = {
          title: '使用权限被取消的通知',
          en: 'revokePowerNotice',
          type: 9
        }
        break
      case 'tableRelationApproval':
        fileNotice = {
          title: '表格关联通知',
          en: 'sheetRelate',
          type: 10
        }
        break
      case 'tableRelieveRelationApproval':
        fileNotice = {
          title: '表格解除关联通知',
          en: 'sheetRelieveRelate',
          type: 11
        }
        break
      case 'fileLocationMoveNotice':
        fileNotice = {
          title: '文件位置移动的通知',
          en: 'fileLocationMoveNotice',
          type: 12
        }
        break
      case 'folderLocationMoveNotice':
        fileNotice = {
          title: '文件夹位置移动的通知',
          en: 'folderLocationMoveNotice',
          type: 13
        }
        break
      case 'fileNameEditNotice':
        fileNotice = {
          title: '文件编号/名称修改的通知',
          en: 'fileNameEditNotice',
          type: 14
        }
        break
      case 'folderNameEditNotice':
        fileNotice = {
          title: '文件夹名称修改的通知',
          en: 'folderNameEditNotice',
          type: 15
        }
        break
      case 'tableNumberEditNotice':
        fileNotice = {
          title: '表格编号/名称修改的通知',
          en: 'tableNumberEditNotice',
          type: 16
        }
        break
    }
    this.fileNotice = fileNotice
    this.axios.post('../../../res/getDisappearResNotice.do', {type: fileNotice.type})
      .then(function (response) {
        console.log('listResNoticeByType', response)
        let data = response.data.data
        let listResNotice = data.listResNotice
        that.dataList = listResNotice
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('soonDisappearFile', function (data) {
        console.log('soonDisappearFile user:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.res
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toApprove.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.toApprove.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toApprove.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    console.log(that.sphdSocket.org.id)
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/willCleanFileDetail/${id}`
      })
    },
    childFolder: function (item) {
      this.$router.push({
        path: `/childFolder/${item.category}`
      })
    }
  }
}
</script>
