package cn.sphd.miners.modules.finance.entity;

import cn.sphd.miners.modules.system.entity.Organization;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_finance_return")
public class FinanceReturn implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="return_no"  , length=100 , nullable=true , unique=false)
    private String returnNo;   //支票号码

    @Column(name="type"   , nullable=true , unique=false)
    private Integer type;   //1-转帐支票，2-承兑汇票

    @Column(name="category"  , length=1 , nullable=true , unique=false)
    private String category;   //种类:1-贷款,2-借款,3-投资款,4-废品,5-其他

    @Column(name="category_desc"  , length=100 , nullable=true , unique=false)
    private String categoryDesc;  //类别说明,当类别为5-其他时,录入文字补充性说明

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;  //金额

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;  //票面金额(所开具发票或收据的金额)

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;   //摘要

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;  //用途

    @Column(name="operator"   , nullable=true , unique=false)
    private Integer operator;   //经手人

    @Column(name="operator_name"  , length=100 , nullable=true , unique=false)
    private String operatorName;   //接收经手人姓名

    @Column(name="partner_name"  , length=100 , nullable=true , unique=false)
    private String partnerName;//合作方经手人姓名

    @Column(name="receive_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date receiveDate;   //收到票据日期(收到日期)

    @Column(name="payer"  , length=100 , nullable=true , unique=false)
    private String payer;   //付款单位

    @Column(name="original_corp"  , length=100 , nullable=true , unique=false)
    private String originalCorp;   //原始出具票据单位(最初出具的单位)

    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName;  // 出具支票/汇票银行(出具支票银行/出具的银行)

    @Column(name="expire_date"   , nullable=true , unique=false)
    private Date expireDate;   //支票到期日期(到期日)

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;  //1-有效,2-存入银行,3-作废，4-已支出

    @Column(name="accout"  , length=100 , nullable=true , unique=false)
    private String accout;   //存入银行帐号

    @Column(name="deposit_date"   , nullable=true , unique=false)
    private Date depositDate;   //存入银行日期

    @Column(name="depositor"   , nullable=true , unique=false)
    private Integer depositor;   //存入经手人ID

    @Column(name="depositor_name"  , length=100 , nullable=true , unique=false)
    private String depositorName;   //存入经手人姓名

    @Column(name="receive_account_date"   , nullable=true , unique=false)
    private Date receiveAccountDate;   //到帐日期

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //说明

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;//创建人（财务经手人）

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation; //操作:1-增,2-删,3-修改(在存入银行之前的修改)

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="save_bank_name"  , length=100 , nullable=true , unique=false)
    private String saveBankName;  // 存入支票/汇票的银行名称

    @Column(name="account_id"   , nullable=true , unique=false)
    private Integer accountId;  //存入存入银行的id

    @Column(name = "modity_status")
    private String modityStatus;  //数据修改状态  null/1-未修改  2-已修改(每条数据仅能修改一次)

    @Column(name="myself_id"   , nullable=true , unique=false)
    private Integer myselfId;  //数据修改时，对应修改前支票的id(本表)

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;//修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;//版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="org", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private Organization org;

    @Column(name="org"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer org_;


    //与回款票据表历史表的一对多关系
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany (targetEntity=FinanceReturnHistory.class, fetch=FetchType.LAZY, mappedBy="financeReturn", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceReturnHistory> financeReturnHistoryHashSet = new HashSet<FinanceReturnHistory>();


    @Transient
    private String billType; //1-收入,2-支出

    @Transient
    private String billCat;  //票据种类

    @Transient
    private Integer billQuantity; //票据数量

    @Transient
    private String billPeriod;  //票据所属月份

    @Transient
    private String oppositeCorp;  //收款单位

    @Transient
    private String saveBankId;  //存入存入银行的id（就是上面的accountId）

    @Transient
    private String customerName;  //客户名称

    @Transient
    private String subType;   //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出

    @Transient
    private Date billDate;  //票据日期

    @Transient
    private Date factDate;  //实际付款/支付日期

    @Transient
    private String returnNumber;  //支票号，保留后六位的

    public String getBillCat() {
        return billCat;
    }

    public void setBillCat(String billCat) {
        this.billCat = billCat;
    }

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    public String getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(String billPeriod) {
        this.billPeriod = billPeriod;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getOppositeCorp() {
        return oppositeCorp;
    }

    public void setOppositeCorp(String oppositeCorp) {
        this.oppositeCorp = oppositeCorp;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getPayer() {
        return payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public String getOriginalCorp() {
        return originalCorp;
    }

    public void setOriginalCorp(String originalCorp) {
        this.originalCorp = originalCorp;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getAccout() {
        return accout;
    }

    public void setAccout(String accout) {
        this.accout = accout;
    }

    public Date getDepositDate() {
        return depositDate;
    }

    public void setDepositDate(Date depositDate) {
        this.depositDate = depositDate;
    }

    public Integer getDepositor() {
        return depositor;
    }

    public void setDepositor(Integer depositor) {
        this.depositor = depositor;
    }

    public String getDepositorName() {
        return depositorName;
    }

    public void setDepositorName(String depositorName) {
        this.depositorName = depositorName;
    }

    public Date getReceiveAccountDate() {
        return receiveAccountDate;
    }

    public void setReceiveAccountDate(Date receiveAccountDate) {
        this.receiveAccountDate = receiveAccountDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Organization getOrg() {
        return org;
    }

    public void setOrg(Organization org) {
        this.org = org;
    }

    public Integer getOrg_() {
        return org_;
    }

    public void setOrg_(Integer org_) {
        this.org_ = org_;
    }

//    public FinanceAccountBill getFinanceAccountBill() {
//        return financeAccountBill;
//    }
//
//    public void setFinanceAccountBill(FinanceAccountBill financeAccountBill) {
//        this.financeAccountBill = financeAccountBill;
//    }


    public String getSaveBankName() {
        return saveBankName;
    }

    public void setSaveBankName(String saveBankName) {
        this.saveBankName = saveBankName;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getSaveBankId() {
        return saveBankId;
    }

    public void setSaveBankId(String saveBankId) {
        this.saveBankId = saveBankId;
    }

    public String getModityStatus() {
        return modityStatus;
    }

    public void setModityStatus(String modityStatus) {
        this.modityStatus = modityStatus;
    }

    public Integer getMyselfId() {
        return myselfId;
    }

    public void setMyselfId(Integer myselfId) {
        this.myselfId = myselfId;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Set<FinanceReturnHistory> getFinanceReturnHistoryHashSet() {
        return financeReturnHistoryHashSet;
    }

    public void setFinanceReturnHistoryHashSet(Set<FinanceReturnHistory> financeReturnHistoryHashSet) {
        this.financeReturnHistoryHashSet = financeReturnHistoryHashSet;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public Date getFactDate() {
        return factDate;
    }

    public void setFactDate(Date factDate) {
        this.factDate = factDate;
    }

    public String getReturnNumber() {
        return returnNumber;
    }

    public void setReturnNumber(String returnNumber) {
        this.returnNumber = returnNumber;
    }
}
