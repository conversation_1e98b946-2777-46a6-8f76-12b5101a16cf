<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquCategoryHistoryMapper">
    
    <resultMap type="TEquCategoryHistory" id="TEquCategoryHistoryResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="category"    column="category"    />
        <result property="name"    column="name"    />
        <result property="content"    column="content"    />
        <result property="parent"    column="parent"    />
        <result property="isSystem"    column="is_system"    />
        <result property="revisable"    column="revisable"    />
        <result property="enabled"    column="enabled"    />
        <result property="enabledTime"    column="enabled_time"    />
        <result property="childrens"    column="childrens"    />
        <result property="descendants"    column="descendants"    />
        <result property="path"    column="path"    />
        <result property="orders"    column="orders"    />
        <result property="equipmentCount"    column="equipment_count"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTEquCategoryHistoryVo">
        select id, org, category, name, content, parent, is_system, revisable, enabled, enabled_time, childrens, descendants, path, orders, equipment_count, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_equ_category_history
    </sql>

    <select id="selectTEquCategoryHistoryList" parameterType="TEquCategoryHistory" resultMap="TEquCategoryHistoryResult">
        <include refid="selectTEquCategoryHistoryVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="category != null "> and category = #{category}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="parent != null "> and parent = #{parent}</if>
            <if test="isSystem != null "> and is_system = #{isSystem}</if>
            <if test="revisable != null "> and revisable = #{revisable}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="enabledTime != null "> and enabled_time = #{enabledTime}</if>
            <if test="childrens != null "> and childrens = #{childrens}</if>
            <if test="descendants != null "> and descendants = #{descendants}</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
            <if test="orders != null "> and orders = #{orders}</if>
            <if test="equipmentCount != null "> and equipment_count = #{equipmentCount}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>

        group by version_no order by id desc
    </select>
    
    <select id="selectTEquCategoryHistoryById" parameterType="Long" resultMap="TEquCategoryHistoryResult">
        <include refid="selectTEquCategoryHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTEquCategoryHistory" parameterType="TEquCategoryHistory" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_category_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="category != null">category,</if>
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
            <if test="parent != null">parent,</if>
            <if test="isSystem != null">is_system,</if>
            <if test="revisable != null">revisable,</if>
            <if test="enabled != null">enabled,</if>
            <if test="enabledTime != null">enabled_time,</if>
            <if test="childrens != null">childrens,</if>
            <if test="descendants != null">descendants,</if>
            <if test="path != null">path,</if>
            <if test="orders != null">orders,</if>
            <if test="equipmentCount != null">equipment_count,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="category != null">#{category},</if>
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="parent != null">#{parent},</if>
            <if test="isSystem != null">#{isSystem},</if>
            <if test="revisable != null">#{revisable},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="enabledTime != null">#{enabledTime},</if>
            <if test="childrens != null">#{childrens},</if>
            <if test="descendants != null">#{descendants},</if>
            <if test="path != null">#{path},</if>
            <if test="orders != null">#{orders},</if>
            <if test="equipmentCount != null">#{equipmentCount},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTEquCategoryHistory" parameterType="TEquCategoryHistory">
        update t_equ_category_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="category != null">category = #{category},</if>
            <if test="name != null">name = #{name},</if>
            <if test="content != null">content = #{content},</if>
            <if test="parent != null">parent = #{parent},</if>
            <if test="isSystem != null">is_system = #{isSystem},</if>
            <if test="revisable != null">revisable = #{revisable},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="enabledTime != null">enabled_time = #{enabledTime},</if>
            <if test="childrens != null">childrens = #{childrens},</if>
            <if test="descendants != null">descendants = #{descendants},</if>
            <if test="path != null">path = #{path},</if>
            <if test="orders != null">orders = #{orders},</if>
            <if test="equipmentCount != null">equipment_count = #{equipmentCount},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquCategoryHistoryById" parameterType="Long">
        delete from t_equ_category_history where id = #{id}
    </delete>

    <delete id="deleteTEquCategoryHistoryByIds" parameterType="String">
        delete from t_equ_category_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>