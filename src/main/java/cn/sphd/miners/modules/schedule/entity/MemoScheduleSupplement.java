package cn.sphd.miners.modules.schedule.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2019/8/13.
 * 日程补充记录表
 */
@Entity
@Table(name = "t_memo_schedule_supplement")
public class MemoScheduleSupplement implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="memo_schedule"  ,nullable=true , unique=false, insertable=false, updatable=false)
    private Integer scheduleId;//日程主表id

    @Column(name="memo_job"  ,nullable=true , unique=false)
    private Integer memoJobId;//日程主表id

    @Column(name="schedule_uid"  , length=36 , nullable=true , unique=false)
    private String scheduleUid;   //日程的唯一标识符。 此值用于标识分布式提醒

    @Column(name="title"  , length=255 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="description"  ,length=255 , nullable=true , unique=false)
    private String description;   //详细描述

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //'备注'

    @Column(name="orders"  ,nullable=true , unique=false)
    private Integer orders;   //排序

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateDate;

    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="memo_schedule", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MemoSchedule schedule;//日程集联

    //补充记录图片表
//    @JsonIgnore
    @OrderBy("id asc")
    @OneToMany(targetEntity=MemoScheduleSupplementImage.class, fetch= FetchType.LAZY, mappedBy="scheduleSupplement", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<MemoScheduleSupplementImage> memoScheduleSupplementImageHashSet = new HashSet<MemoScheduleSupplementImage>();


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getScheduleUid() {
        return scheduleUid;
    }

    public void setScheduleUid(String scheduleUid) {
        this.scheduleUid = scheduleUid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public MemoSchedule getSchedule() {
        return schedule;
    }

    public void setSchedule(MemoSchedule schedule) {
        this.schedule = schedule;
    }

    public Set<MemoScheduleSupplementImage> getMemoScheduleSupplementImageHashSet() {
        return memoScheduleSupplementImageHashSet;
    }

    public void setMemoScheduleSupplementImageHashSet(Set<MemoScheduleSupplementImage> memoScheduleSupplementImageHashSet) {
        this.memoScheduleSupplementImageHashSet = memoScheduleSupplementImageHashSet;
    }

    public Integer getMemoJobId() {
        return memoJobId;
    }

    public void setMemoJobId(Integer memoJobId) {
        this.memoJobId = memoJobId;
    }
}
