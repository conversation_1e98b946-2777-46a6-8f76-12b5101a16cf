package cn.sphd.miners.modules.equipment.controller;

import java.util.Date;
import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquControlImage;
import cn.sphd.miners.modules.equipment.entity.TEquControlOption;
import cn.sphd.miners.modules.equipment.entity.TEquControlPoint;
import cn.sphd.miners.modules.equipment.service.ITEquControlImageService;
import cn.sphd.miners.modules.equipment.service.ITEquControlOptionService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_控制选项 20240407 1.292机器设备之初始设置 新增Controller
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@Controller
@RequestMapping("/equ/control/option")
public class TEquControlOptionController {
  private String prefix = "system/option";

  @Autowired private ITEquControlOptionService tEquControlOptionService;

  @Autowired private ITEquControlImageService tEquControlImageService;

  @GetMapping()
  public String option() {
    return prefix + "/option";
  }

  /** 查询装备管理_控制选项 20240407 1.292机器设备之初始设置 新增列表 */
  @PostMapping("/list")
  @ResponseBody
  public List list(TEquControlOption tEquControlOption, User user) {
    tEquControlOption.setOrg(Long.valueOf(user.getOid()));
    List<TEquControlOption> list =
        tEquControlOptionService.selectTEquControlOptionList(tEquControlOption);

    if (tEquControlOption.getEnabled() != 0L)
      if (list == null || list.isEmpty()) {
        // 自动初始化出自带的选项
        tEquControlOptionService.init(user);
      }
    return list;
  }

  /** 新增装备管理_控制选项 20240407 1.292机器设备之初始设置 新增 */
  @GetMapping("/add")
  public String add() {
    return prefix + "/add";
  }

  /** 新增保存装备管理_控制选项 20240407 1.292机器设备之初始设置 新增 */
  @PostMapping("/add")
  @ResponseBody
  public AjaxResult addSave(TEquControlOption tEquControlOption, User user) {
    tEquControlOption.setOrg(Long.valueOf(user.getOid()));

    TEquControlOption op =
        tEquControlOptionService.selectTEquControlOptionByNameAndOrg(tEquControlOption);
    if (op != null) {
      if (op.getEnabled() == 1L) { // 选项已存在
        return AjaxResult.warn("选项已存在，请勿重复添加");
      } else if (op.getEnabled() == 0L) { // 选项已存在，但未启用，启用后重置为启用状态
        op.setEnabled(1L);
        return toAjax(tEquControlOptionService.updateTEquControlOption(op));
      }
    }

    tEquControlOption.setCreateDate(new Date());
    tEquControlOption.setCreateName(user.getUserName());
    Integer id = tEquControlOptionService.insertTEquControlOption(tEquControlOption);

    return toAjax(1);
  }

  @PostMapping("/enable")
  @ResponseBody
  public AjaxResult enable(String ids, Long enabled, User user) {
    if (StringUtils.isNotBlank(ids)) {
      for (String s : ids.split(",")) {
        TEquControlOption tEquControlOption =
            tEquControlOptionService.selectTEquControlOptionById(Long.valueOf(s));
        if (tEquControlOption != null) {
          tEquControlOption.setEnabled(enabled);
          tEquControlOption.setUpdateDate(new Date());
          tEquControlOption.setUpdator(Long.valueOf(user.getUserID()));
          tEquControlOption.setUpdateName(user.getUserName());
          tEquControlOptionService.updateTEquControlOption(tEquControlOption);
        }
      }
    }

    return toAjax(1);
  }

  /** 修改装备管理_控制选项 20240407 1.292机器设备之初始设置 新增 */
  @GetMapping("/edit/{id}")
  public String edit(@PathVariable("id") Long id, ModelMap mmap) {
    TEquControlOption tEquControlOption = tEquControlOptionService.selectTEquControlOptionById(id);
    mmap.put("tEquControlOption", tEquControlOption);
    return prefix + "/edit";
  }

  /** 修改保存装备管理_控制选项 20240407 1.292机器设备之初始设置 新增 */
  @PostMapping("/edit")
  @ResponseBody
  public AjaxResult editSave(TEquControlOption tEquControlOption, User user) {
    tEquControlOption.setUpdateDate(new Date());
    tEquControlOption.setUpdateName(user.getUserName());
    if (tEquControlOption.getEnabled() != null) {
      tEquControlOption.setEnabledTime(new Date());
    }
    return toAjax(tEquControlOptionService.updateTEquControlOption(tEquControlOption));
  }

  /** 删除装备管理_控制选项 20240407 1.292机器设备之初始设置 新增 */
  @PostMapping("/remove")
  @ResponseBody
  public AjaxResult remove(String ids) {
    return toAjax(tEquControlOptionService.deleteTEquControlOptionByIds(ids));
  }
}
