<!--
弹窗子组件
----desc：该子组件为全局公用组件，弹窗的高低顺序 是按照代码的前后顺序
----pages（用到的页面）：[很多，此处不做详细列举]
-->
<template>
 <div class="tyDialog">
   <div ref="tydialog" :class="klass"
        :style="{ top: position.y + 'px', left: position.x + 'px', width: width + 'px' }">
     <div class="bonceHead" @mousemove="updateMousePosition" @mousedown="startMove" @mouseup="endMove" v-if="headerShow">
       <span>{{ dialogTitle }}</span>
       <slot name="bonceHead"></slot>
       <el-icon class="bounce_close" @click.stop="hideDialog"><Close /></el-icon>
     </div>
     <div class="bonceCon">
       <slot/>
       <slot name="dialogBody"></slot>
     </div>
     <div class="bonceFoot">
       <slot name="dialogFooter"></slot>
     </div>
   </div>
 </div>
</template>
<script>

export default {
  name: "TyDialog",
  data() {
    return {
      dislogColor:'bounce-',
      moveOr:false,
      klass:false,
      position:{ x:0, y:0, mouseTop:0, mouseLeft:0 , mouseRight:0, mouseBottom:0 },
    };
  },
  props:{
    dialogTitle: { 'type':String, 'default':'提示' },
    width: { 'type':[Number, String], 'default': 460 },
    color: { 'type':String, 'default': 'blue' },
    dialogHide: { 'type':Function, 'default': null },
    dialogName: { 'type':String, 'default': '未知' },
    headerShow: { 'type':Boolean, 'default': true },
  },
  mounted() {
    console.log('dialogTitle====', this.dialogTitle)
    this.klass = 'bonceContainer bounce-' + this.color
    this.dislogColor += this.color
    const sW = window.innerWidth
    const sH = window.innerHeight
    if(this.$refs.tydialog){
      const height = this.$refs.tydialog.offsetHeight;
      let sX = (sW - this.width)/2
      let sY = (sH - height)/2 - 200
      sY = sY > 0 ? sY : (sH - height)/2
      this.position.x = sX ;
      this.position.y = sY ;
    }

  },
  methods: {
    hideDialog(){
      this.dialogHide(this.dialogName || '')
    },
    startMove(event){
      this.moveOr = true;
      const info = this.$refs.tydialog.getBoundingClientRect();
      const height = info.height;
      const width = info.width;
      console.log('info=', info)
      const left = info.left ;
      const top = info.top;
      const mouseTop = event.clientY ;
      const mouseLeft = event.clientX ;
      this.position.mouseTop = mouseTop - top ; // 鼠标距离弹窗上的距离
      this.position.mouseLeft = mouseLeft - left ; // 鼠标距离弹窗左的距离
      this.position.mouseRight = width - this.position.mouseLeft ; // 鼠标距离弹窗右的距离
      this.position.mouseBottom = height - this.position.mouseTop ; // 鼠标距离弹窗下的距离

    },
    endMove(){
      this.moveOr = false;
      this.position.mouseTop = 0 ;
      this.position.mouseLeft = 0 ;
    },
    updateMousePosition(event) {
      if(this.moveOr){
        const mouseTop = event.clientY ;
        const mouseLeft = event.clientX ;
        let x = mouseLeft -  this.position.mouseLeft
        let y = mouseTop -  this.position.mouseTop
        x = x >= 0 ? x : 0
        y = y >= 0 ? y : 0
        const info = this.$refs.tydialog.getBoundingClientRect();
        const height = info.height;
        const width = info.width;
        const screenWidth = window.innerWidth
        const screenHeight = window.innerHeight
        if(screenWidth - x < width){
          x = screenWidth - width -5
        }
        if(screenHeight - y < height){
          y = screenHeight - height - 5
        }
        this.position.x = x
        this.position.y = y
      }


    }
  },
}
</script>

<style lang="scss" scoped>
.tyDialog{
  position: fixed;left:0; top:0; z-index: 100; width: 100%; height: 100%; background: rgba(100,100,100,0.3);

  .bonceContainer{
    border-radius: 5px;
    background: #fff; width: 450px; box-shadow: 0 0 6px rgba(0,0,0,.3); position: fixed;
    .bonceHead, .bonceCon , .bonceFoot{ background-color:#fff;  }
    .bonceHead{ border-bottom:1px solid #eaeaea;height:40px; font-size:16px; line-height:40px;padding:0 15px;background-color:#fff; border-radius:5px 5px 0 0; cursor: move;   }
    .bounce_close{ width:20px; height:20px;  display:inline-block;float:right;  margin:10px 0;
      font-size: 20px;  border-radius:11px;  }
    .bounce_close:hover{ cursor:pointer; background-color:#eaeaea;   }
    .bonceCon{ padding:10px 15px; background-color: #f0f8ff;  max-height: 550px; overflow-y: auto; min-height:50px; }
    .bonceFoot{ background-color: #f0f8ff;text-align:right; padding:10px 15px 20px;border-radius:0 0 5px 5px;  }
  }
  .bounce-blue .bonceHead{ border-bottom:1px solid $tyBounce-color-blue; color:$tyBounce-color-blue;  }
  .bounce-red .bonceHead{ border-bottom:1px solid $tyBounce-color-red; color:$tyBounce-color-red;   }
  .bounce-orange .bonceHead{ border-bottom:1px solid $tyBounce-color-orange; color: $tyBounce-color-orange; }
  .bounce-green .bonceHead{ border-bottom:1px solid $tyBounce-color-green; color: $tyBounce-color-green;  }
  .bounce-cyan .bonceHead{ border-bottom:1px solid $tyBounce-color-cyan; color: $tyBounce-color-cyan;  }



}
</style>