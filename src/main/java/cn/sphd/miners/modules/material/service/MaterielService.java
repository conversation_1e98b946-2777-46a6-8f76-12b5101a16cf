package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/9/20.
 */
public interface MaterielService {

    MtBase getMtBaseById(Integer id);

    void updateMtBase(MtBase mtBase);

    void saveMtBase(MtBase mtBase);

    void deleteMtBase(MtBase mtBase);

    void saveMtStockInfo(MtStockInfo mtStockInfo);

    void updateMtStockInfo(MtStockInfo mtStockInfo);

    void updateMtQuality(MtQuality mtQuality);

    void saveMtQuality(MtQuality mtQuality);

    void updateMtCategory(MtCategory mtCategory);

    List<MtCategory> getMtCategoryByOidAndPid(Integer oid, Integer pid);
    //根据分类名称查询下级分类
    List<Map<String,Object>> getMtCategoryByOidAndName(Integer oid, String param);
    //根据分类名称查询下级分类--成品库
    List<Map<String,Object>> getMtCategoryByOidAndNameForChengPin(Integer oid, String name,Integer userId);
    List<MtBase> getMtBaseByCategoryId(Integer categoryId);

    MtCategory getMtCategoryById(Integer id);

    List<MtCategory> getMtCategoriesById(Integer id);

    void deleteCategory(MtCategory mtCategory);

    void saveMtCategory(MtCategory mtCategory);

    void saveMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial);

    void saveSrmSupplier(SrmSupplier srmSupplier);

    void updateSrmSupplier(SrmSupplier srmSupplier);

    void updateMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial);

    SrmSupplier getSrmSupplierById(Integer id);

    MtSupplierMaterial getMtSupplierMaterialById(Integer id);

    void deleteMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial);

    List<SrmSupplier> getSrmSupplierByName(String name, Integer oid,Integer enabled,Integer supplierId);

    Map getSrmSupplierByNameAndOid(String name, Integer oid,Integer state);

    void deleteSrmSupplier(SrmSupplier srmSupplier);

    MtCategory getMtCategoryByOidCName(Integer oid, String categoryName);

    List<MtBase> getMtBaseByOidAndCode(Integer oid, String code);

    List<MtBase> getMtBaseListByCode(Integer oid, String code);

    MtBase getMtBaseByProduct(Integer product);

    List<SrmSupplier> getSrmSupplierByOid(Integer oid);

    List<MtSupplierMaterial> getMtSupplierMaterialByNameAndCodeName(Integer oid, String codeName, String name);

    List<MtSupplierMaterial> getListBySupplierId(Integer supplierId);

    MtSupplierMaterial getMtSupplierMaterialByMIdANDSID(int sbId, int id);

    List<MtBase> getMtBaseByProductId(Integer id);

    Map getAllPdBaseAndMtBase(Integer oid, User user,String time, SubjectSettingService subjectSettingService, UserService userService, UserMessageService messageService);

    List<MtBase> getMtBaseListByCodeAndName(Integer oid, String code, String name);

    Map getMtBaseListByCodeAndNameMap(Integer oid, Integer supplierId,String code, String name,Integer state);
    //修改物料
    void updateBase(HttpServletRequest request, HttpServletResponse response, Integer oid, User user, SubjectSelectService subjectSelectService, UserMessageService userMessageService, UserService userService, ProductService productService) throws IOException, ParseException;

    /*
    * subjectName       经销商科目名称
    * parent            经销商父科目编号
    * endSubjectName    要转换成的商品/物料/供应商/客户/半成品的科目名称
    * relevanceType     1-商品，2-物料,3-供应商,4-客户,5-半成品
    * */
    void changeIntoSubject(Integer oid, User user, String subjectName, String parent, String endSubjectName, HashMap o, String relevanceType, Integer relevanceItem);

    MtSupplierMaterial getMtSupplierMaterialBySupplierAndMaterial(Integer id, Integer mtId);

    MtBase getMtBaseVersionAndPreVersion(int version, Integer pre);

    Map getOrdersDetailsByCondition(Object id, Integer supplierId, Integer oid);

    Integer locationSuppliers(MtSupplierMaterial mtSupplierMaterial, Integer supplierMaterialId, SrmSupplier supplier,MtBase base,User user);

    Map getSupplierMaterials(Integer supplierId,Integer state,Integer enabled,Integer invoiceType,Integer org);

    List<SrmSupplierHistory> getSrmSupplierHistory(Integer id, String orderBy, String rule);

    void saveSrmSupplierHistory(SrmSupplierHistory supplierHistory);
}
