package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.sales.service.PoService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by 19614 on 2020/8/11.
 * 1.108采购订单之审批
 * 新增采购订单的接口是sales-controller-PoCtrl-orders(接口)
 */
@Controller
@RequestMapping("/purchaseOrderApproval")
public class PurchaseOrderApprovalController {

    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    PoService poService;
    @Autowired
    ApprovalProcessService approvalProcessService;

    /**
    * <AUTHOR>
    * @Description  跳转页面
    * @Date 2020/8/22
    */
    @RequestMapping("/purchasePage.do")
    public String purchasePage(){
        return "/purchase/orderInfo";
    }

    /**
    * <AUTHOR>
    * @Description 申请人的采购订单列表
    * @Date 2020/8/12
    */
    @ResponseBody
    @RequestMapping("/getPurchaseOrderApplication.do")
    @MessageMapping("/getPurchaseOrderApplication")
    public JsonResult getPurchaseOrderApplication(String sessionid, User user){

        Map<String,Object> map = new HashMap<String,Object>();
        map = poService.getPurchaseOrderList(null,user.getUserID(),null,"1","1",null,null,null);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/purchaseOrderApplyHandle",null,null,null,null,map);  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 审批人的采购订单列表
     * @Date 2020/8/12
     */
    @ResponseBody
    @RequestMapping("/getPurchaseOrderApproval.do")
    @MessageMapping("/getPurchaseOrderApproval")
    public JsonResult getPurchaseOrderApproval(String sessionid, User user){
        Map<String,Object> map = new HashMap<String,Object>();
        Map<String,Object> purchaseOrderApprovalHandle = poService.getPurchaseOrderList(user.getUserID(),null,null,"1","1",null,null,null);
        Map<String,Object> purchaseOrderApprovalApproval = poService.getPurchaseOrderList(user.getUserID(),null,null,"1","2",null,null,null);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/purchaseOrderApprovalHandle",null,null,null,null,JSON.toJSONString(purchaseOrderApprovalHandle));  //返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/purchaseOrderApprovalApproval",null,null,null,null,JSON.toJSONString(purchaseOrderApprovalApproval));  //返回结果的订阅
        map.put("purchaseOrderApprovalHandle",purchaseOrderApprovalHandle);  //待处理
        map.put("purchaseOrderApprovalApproval",purchaseOrderApprovalApproval);  //已批准
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description 查看详情
    * @Date 2020/8/13
    */
    @ResponseBody
    @RequestMapping("/getPurchaseOrderDetail.do")
    public JsonResult getPurchaseOrderDetail(Integer ordersId){
        Map<String,Object> map = new HashMap<String,Object>();
        if (ordersId!=null){
            map = poService.getOrderDetail(ordersId);
            List<ApprovalProcess> approvalProcess = approvalProcessService.getApprovalProcessByBusiness(ordersId,23,null);
            map.put("approvalProcess",approvalProcess);
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  审批接口（有驳回）
    * @Date 2020/8/13
    */
    @ResponseBody
    @RequestMapping("/purchaseOrderApproval.do")
    @MessageMapping("/purchaseOrderApproval")
    public JsonResult purchaseOrderApproval(String json,String sessionid,User user) {
        Map<String, Object> map = new HashMap<String, Object>();
        JSONObject jsonObject = JSON.parseObject(json);
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
        String approvalStatus = jsonObject.getString("approvalStatus");  //2-批准，3-驳回,4-撤回
        String rejectReasion = jsonObject.getString("rejectReasion");  //驳回原因 1-该供应商价格贵,2- 该供应商质量不稳定,3-该供应商服务态度不好,4-该供应商无法确保准时交货,5-付款方式不合理,6-暂无需采购,7- 发票原因,8- 其他原因
        String rejectReasionDesc = jsonObject.getString("rejectReasionDesc"); // 驳回原因说明，当驳回原因是8时，录入的驳回原因
        map = poService.purchaseOrderApproval(user,approvalProcessId,approvalStatus,rejectReasion,rejectReasionDesc);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/purchaseOrderMessage",null,null,null,null,map);  //返回结果的订阅
        return new JsonResult(1, map);
    }

    /**
    * <AUTHOR>
    * @Description 申请人查询
    * @Date 2020/8/13
    * type 1-近七日 2-本月 3-自定义
    * supplier-供应商id  userId-登录人id  beginDate-开始时间  endDate-结束时间 approvalStatus-审批状态（ 2-通过审核,3-否决审核,4-撤回）
     * rejectReasion 驳回原因（1-该供应商价格贵,2- 该供应商质量不稳定,3-该供应商服务态度不好,4-该供应商无法确保准时交货,5-付款方式不合理,6-暂无需采购,7- 发票原因,8- 其他原因）
    */
    @ResponseBody
    @RequestMapping("/getPurchaseOrderApplys.do")
    public JsonResult getPurchaseOrderApplys(Integer type,User user, Integer supplier, String approvalStatus, String rejectReasion, String beginDate,String endDate) throws ParseException {
        Map<String,Object> map = new HashMap<String,Object>();
        Integer dayType = 1;
        Date beginTime = new Date();
        Date endTime = new Date();
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = new SimpleDateFormat("yyyy-MM-dd").parse(beginDate);
            endTime = new SimpleDateFormat("yyyy-MM-dd").parse(endDate);
            if (!NewDateUtils.getYearMonth(beginTime).equals(NewDateUtils.getYearMonth(endTime))){
                dayType = 2;  //需要拆分时间；
            }
        }
        map = poService.getPurchaseOrderApplys(null,user.getUserID(),supplier,approvalStatus,approvalStatus,rejectReasion,beginTime,endTime,dayType);
        map.put("beginDate",beginTime);
        map.put("endDate",endTime);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 审批人查询
     * @Date 2020/8/13
     * type 1-近七日 2-本月 3-自定义
     * supplier-供应商id  userId-登录人id  beginDate-开始时间  endDate-结束时间 approvalStatus-审批状态（ 2-通过审核,3-否决审核,4-撤回）
     * rejectReasion 驳回原因（1-该供应商价格贵,2- 该供应商质量不稳定,3-该供应商服务态度不好,4-该供应商无法确保准时交货,5-付款方式不合理,6-暂无需采购,7- 发票原因,8- 其他原因）
     */
    @ResponseBody
    @RequestMapping("/getPurchaseOrderApprovals.do")
    public JsonResult getPurchaseOrderApprovals(Integer type,User user, Integer supplier, String approvalStatus, String rejectReasion, String beginDate,String endDate) throws ParseException {
        Map<String,Object> map = new HashMap<String,Object>();
        Integer dayType = 1;
        Date beginTime = new Date();
        Date endTime = new Date();
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = new SimpleDateFormat("yyyy-MM-dd").parse(beginDate);
            endTime = new SimpleDateFormat("yyyy-MM-dd").parse(endDate);
            if (!NewDateUtils.getYearMonth(beginTime).equals(NewDateUtils.getYearMonth(endTime))){
                dayType = 2;  //需要拆分时间；
            }
        }
        map = poService.getPurchaseOrderApplys(user.getUserID(),null,supplier,approvalStatus,approvalStatus,rejectReasion,beginTime,endTime,dayType);
        map.put("beginDate",beginTime);
        map.put("endDate",endTime);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description 获取采购订单的供应商
    * @Date 2020/8/13
    */
    @ResponseBody
    @RequestMapping("/getPurchaseSuppliers.do")
    public JsonResult getPurchaseSuppliers(User user){

        Map<String,Object> map = new HashMap<String,Object>();
        List<Map<String,Object>> listMap = poService.getPurchaseSuppliers(user.getOid());
        map.put("suppliers",listMap);
        return new JsonResult(1,listMap);
    }


}
