package cn.sphd.miners.modules.equipment.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Transient;

/**
 * 对象 t_equ_model
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public class TEquModel {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 组织机构ID
     */

    private Long org;

    /**
     * 装备ID
     */

    private Long equipment;

    /**
     * 型号,为空代表未知
     */

    private String modelName;

    /**
     * 编号
     */

    private String modelCode;

    /**
     * 计量单位,指向t_mt_unit表ID
     */

    private Long unitId;

    /**
     * 所属类别,为空时代表未分类  已经移到了固定资产表内
     */

    @Transient
    private Long category;

    /**
     * 供应商ID,为空时代表自行装配
     */

    private Long supplier;

    /**
     * 状态:0-不启用,1-启用
     */

    private Long enabled;

    /**
     * 启停用时间
     */

    private Date enabledTime;

    /**
     * 参与产品数
     */

    private Long productCount;

    /**
     * 备注
     */

    private String memo;

    /**
     * 创建人id
     */

    private Long creator;

    /**
     * 创建人
     */

    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改人id
     */

    private Long updator;

    /**
     * 修改人
     */

    private String updateName;

    /**
     * 修改时间
     */

    private Date updateDate;

    /**
     * 操作:1-增,2-删,3-更改,4-启用,5-停用
     */

    private Long operation;

    /**
     * 修改前记录ID
     */

    private Long previousId;

    private Integer quantity;

    //1.292
    private Integer pointCount;

    private Integer pointState;

    /**
     * 版本号,每次修改+1
     */

    private Long versionNo;

    @Transient
    private String name;//装备器具名称

    @Transient
    private String unit;//单位

    @Transient
    private String rDate;

    @Transient
    private String equipmentName;

    @Transient
    private String ids;

    @Transient
    private Integer emCount;

    @Transient
    private Long oldEquipment;

    @Transient
    private String oldModelName;

    @Transient
    private Long oldSupplier;

    @Transient
    TEquFixedAssets fixedAssets;

    @Transient
    private Integer setNull;

    @Transient
    private Integer init;

    /**
     * 使用寿命
     */
    @Transient
    private Integer lifeSpan;
    /**
     * 原值
     */
    @Transient
    private Double originalValue;
    /**
     * 到厂日期,统一加01,返回前端时返YYYYMM
     */
    @Transient
    private Date receiveDate;
    /**
     * 新旧情况:1-新,2-旧
     */
    @Transient
    private Integer conditions;


    @Transient
    private Integer state;

    private TEquSupplier equSupplier;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOrg(Long org) {
        this.org = org;
    }

    public Long getOrg() {
        return org;
    }

    public void setEquipment(Long equipment) {
        this.equipment = equipment;
    }

    public Long getEquipment() {
        return equipment;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Integer getPointState() {
        return pointState;
    }

    public void setPointState(Integer pointState) {
        this.pointState = pointState;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setCategory(Long category) {
        this.category = category;
    }

    public Long getCategory() {
        return category;
    }

    public void setSupplier(Long supplier) {
        this.supplier = supplier;
    }

    public Long getSupplier() {
        return supplier;
    }

    public Long getOldSupplier() {
        return oldSupplier;
    }

    public void setOldSupplier(Long oldSupplier) {
        this.oldSupplier = oldSupplier;
    }

    public void setEnabled(Long enabled) {
        this.enabled = enabled;
    }

    public Long getEnabled() {
        return enabled;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setProductCount(Long productCount) {
        this.productCount = productCount;
    }

    public Integer getSetNull() {
        return setNull;
    }

    public void setSetNull(Integer setNull) {
        this.setNull = setNull;
    }

    public Long getProductCount() {
        return productCount;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }

    public Long getUpdator() {
        return updator;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setOperation(Long operation) {
        this.operation = operation;
    }

    public Long getOperation() {
        return operation;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setVersionNo(Long versionNo) {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() {
        return versionNo;
    }

    public Integer getPointCount() {
        return pointCount;
    }

    public void setPointCount(Integer pointCount) {
        this.pointCount = pointCount;
    }

    public String getName() {
        return name;
    }

    public Long getOldEquipment() {
        return oldEquipment;
    }

    public void setOldEquipment(Long oldEquipment) {
        this.oldEquipment = oldEquipment;
    }

    public String getOldModelName() {
        return oldModelName;
    }

    public void setOldModelName(String oldModelName) {
        this.oldModelName = oldModelName;
    }


    public Integer getInit() {
        return init;
    }

    public void setInit(Integer init) {
        this.init = init;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public TEquSupplier getEquSupplier() {
        return equSupplier;
    }

    public void setEquSupplier(TEquSupplier equSupplier) {
        this.equSupplier = equSupplier;
    }

    public String getEquipmentName() {
        return equipmentName;
    }

    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    public String getrDate() {
        return rDate;
    }

    public void setrDate(String rDate) {
        this.rDate = rDate;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public Integer getEmCount() {
        return emCount;
    }

    public void setEmCount(Integer emCount) {
        this.emCount = emCount;
    }

    public TEquFixedAssets getFixedAssets() {
        return fixedAssets;
    }

    public void setFixedAssets(TEquFixedAssets fixedAssets) {
        this.fixedAssets = fixedAssets;
    }

    public Integer getLifeSpan() {
        return lifeSpan;
    }

    public void setLifeSpan(Integer lifeSpan) {
        this.lifeSpan = lifeSpan;
    }

    public Double getOriginalValue() {
        return originalValue;
    }

    public void setOriginalValue(Double originalValue) {
        this.originalValue = originalValue;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public Integer getConditions() {
        return conditions;
    }

    public void setConditions(Integer conditions) {
        this.conditions = conditions;
    }
}
