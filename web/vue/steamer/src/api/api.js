import auth from '@/sys/auth'
import {request} from "@/utils/request";

/**
 * <AUTHOR>
 * @description 登录，先调用游客token清除之前token信息，然后调用登录
 * @date Create at 2023/9/15 08:04
 * @method login
 * @param logonName 帐号
 * @param logonPwd 密码
 * @returns {*}
 */
export function login(logonName, logonPwd) {
    auth.getGuestToken()
    console.log('api.js', logonName, logonPwd)
    return request({
        url: '/sys/login.do',
        data: {
            logonName: logonName,
            logonPwd: logonPwd
        }
    })
}

/*
 * creator: 2023-10-24 hxz
 * 注册页 发送短信验证码
 * params: phone 手机号
 * 返回值：data 中： 1-成功 0-失败，3-手机号格式不对，5 验证码已经发过还未过时
*/
export function sendMobileCode (data) {
    return request({
        method: "GET",
        // url: '/auth/"/verificationCodeAndLoain.do',
        url: '/auth/sendMessageVerificationCode.do',
        data: data
    })
}
/*
 * creator: 2023-10-24 hxz
 * 登陆 校验 验证码
 * params: mobile 手机号 code 验证码
*/
export function verificationCode (data) {
    return request({
        method: "GET",
        url: '/auth/verificationCodeAndLogin.do',
        data: data
    })
}

/*
 * creator: 2023-10-24 hxz
 * 登陆 验证码方式
 * params: mobile 手机号 code 验证码
*/
export function codeLogin (data) {
    return request({
        method: "GET",
        url: '/sys/codeLogin.do',
        data: data
    })
}

/*
 * creator: 2023-10-24 hxz
 * 退出登陆
*/
export function logOut (data) {
    return request({
        method: "GET",
        url: '/sys/logout.do',
        data: data
    })
}


// 
/*
 * creator: 2022-8-16  hxz
 *  领地/机构内都可用 获取文件路径
 * params:  
*/
export function getRootPathFile() {
    return request({
        url: '/uploads/getRootPath.do',
    })
}

/*
 * creator: 2022-8-16  hxz
 *   机构列表 
 * params:  
*/
export function organizationList(data) {
    return request({
        url: '/sys/getOrgListPageInfo.do',
        data: data
    })
}

/*
 * creator: 2022-8-16  hxz
 *   登录某个工作室 
 * params:   mobile， oid
 * res: user{"userName"姓名 ,"roleCode",}
*/
export function sureLogin(data) {
    data = data || {}
    return request({
        url: '/sys/sureLogin.do',
        data: data
    })
}


/*
 * creator: 2022-10-7 hxz
 *   获取全部歌曲列表
*/
export function steamerGetSongs(data) {
    data = data || {}
    return request({
        url: '/steamer/steamerGetSongs.do',
        data: data
    })
}


/*
 * creator: 2022-10-7 hxz
 *   我的节目单
 *  params: time String 最后一条的创建时间 具体看上面的第3条介绍 比较长 要是不想读直接找我解释也行

*/
export function minePlayList(data) {
    data = data || {}
    return request({
        url: '/steamer/minePlayList.do',
        data: data
    })
}


/*
 * creator: 2022-10-7 hxz
 *   点击节目单获取节目单信息和节目单下歌曲
 *  params: id long 节目单id 若是“全部”歌单就不用传

*/
export function getPlayListMes(data) {
    data = data || {}
    return request({
        url: '/steamer/getPlayListMes.do',
        data: data
    })
}

/*
 * creator: 2022-10-7 hxz
 *   确定新增歌单
 *  params:
 * 1 liveDate String 直播时间 “2023-09-20 15:30:25”
   2 songIds String 歌曲id的字符串  这样的用用，隔开就可以了 “2，3，5，4”
*/
export function addPlayList(data) {
    data = data || {}
    return request({
        url: '/steamer/addPlayList.do',
        data: data
    })
}

/*
 * creator: 2022-10-7 hxz
 *   修改歌单
 *  params:
 * 入参说明：
1 id long 要修改的歌单id
2 2 songIds String 歌曲id的字符串  这样的用用，隔开就可以了 “2，3，5，4”
*/
export function upPlayList (data) {
    data = data || {}
    return request({
        url: '/steamer/upPlayList.do',
        data: data
    })
}

/*
 * creator: 2022-10-7 hxz
 *   修改歌单
 *  params:
 * 入参说明：
1 id long 歌单id 若是在“全部”这个歌单下就不传
2 name String 搜索的名字
3 pageSize int 每页个数 固定是20
4 currentPageNo int 当前页
*/
export function getMoreSongs(data) {
    data = data || {}
    return request({
        url: '/steamer/getMoreSongs.do',
        data: data
    })
}


/*
 * creator: 2022-10-7 hxz
 *   删除歌单
 *  params: id long 要删除的歌单id
 */
export function delPlayList(data) {
    data = data || {}
    return request({
        url: '/steamer/delPlayList.do',
        data: data
    })
}
/*
 * creator: 2022-10-7 hxz
 *   移除歌曲
 *  params:
 * 入参说明：
1 id long 歌曲id
2 playListNum Integer 歌单的歌曲数量 用于对比一下后台真正的数量和前端是否一样，别出现差错
 */
export function removePlayListSong(data) {
    data = data || {}
    return request({
        url: '/steamer/removePlayListSong.do',
        data: data
    })
}

/*
 * creator: 2022-10-7 hxz
 *   缓存歌曲id
 *  params:
 * 入参说明：
1 accId Long
2 songId long 歌曲id 就是歌曲列表中listSongs中国的songs字段
 */
// export function setLyicSong(data) {
//     data = data || {}
//     return request({
//         url: '/lyric/setLyicSong.do',
//         data: data,
//         dataType:'text'
//     })
// }
//
// /*
//  * creator: 2022-10-7 hxz
//  * 获取缓存的歌曲id
//  *  params:
//  * 入参说明：
//     1 accId Long
// */
// export function getLyicSong(songId) {
//     return request({
//         url: '/lyric/getLyricSong.do',
//         data: {songId: songId},
//     })
// }




export function setLyricLive(data) {
    data = data || {}
    return request({
        url: '/lyric/setLyricLive.do',
        data: data
    })
}

export function lyricLive(accId) {
    return request({
        url: '/lyric/lyricLive.do',
        data: {accId:accId},
        // dataType:'html',
    })
}

export function createLyricLive(songId) {
    return request({
        url: '/lyric/createLyricLive.do',
        data: {songId: songId}
    })
}

export function playLiveTime(timeMillis) {
    console.log('api playLiveTime', timeMillis)
    return request({
        url: '/lyric/playLiveTime.do',
        data: {timeMillis: timeMillis}
    })
}

export function stopLiveTime(timeMillis) {
    console.log('api stopLiveTime', timeMillis)
    return request({
        url: '/lyric/stopLiveTime.do',
        data: {timeMillis: timeMillis}
    })
}

export function seekLiveTime(timeMillis) {
    return request({
        url: '/lyric/seekLiveTime.do',
        data: {timeMillis: timeMillis}
    })
}

export function getLyricLivePath() {
    return request({
        url: '/lyric/getLyricLivePath.do',
    })
}

export function steamerAddSongs(data) {
    data = data || {}
    return request({
        url: '/steamer/steamerAddSongs.do',
        data:data
    })
}


// export function createSong(data) {
//     data = data || {}
//     // let webroot = 'https://hxz-t.frp.btransmission.com'
//     let webroot = 'https://dvm05.btransmission.com'
//     let allUrl = webroot + '/lyric/createSong.do'
//     console.log('allUrl=', allUrl)
//     return request({
//         url: allUrl,
//         data: data
//     })
// }




