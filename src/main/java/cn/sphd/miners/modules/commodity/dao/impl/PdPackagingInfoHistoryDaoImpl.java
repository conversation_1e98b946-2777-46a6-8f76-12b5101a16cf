package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdPackagingInfoHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfo;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfoHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class PdPackagingInfoHistoryDaoImpl extends BaseDao<PdPackagingInfoHistory, Serializable> implements PdPackagingInfoHistoryDao {

    @Override
    public PdPackagingInfoHistory insert(PdPackagingInfo pdPackagingInfo) {
        PdPackagingInfoHistory history=new PdPackagingInfoHistory();

        BeanUtils.copyProperties(pdPackagingInfo,history);
        history.setPackaging(pdPackagingInfo.getId());
        history.setId(null);
        save(history);
        return history;
    }
}
