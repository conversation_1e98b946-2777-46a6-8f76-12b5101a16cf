package cn.sphd.miners.modules.resourceAuthority.entity;

import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @create 2017-11-01 11:33
 * @description
 * 用于在查看设置里显示所有的部门和人员
 **/
@Alias("departAndEmployee")
public class ResDepartAndEmployee implements Serializable {

    private int uid;//用户id
    private int oid;//机构id
    private int departid;//部门id
    private String departName;//部门名称
    private int pid;//父部门的id
    private boolean arrow;//是否有箭头，代表有没有子部门或人员，有的话就是true，没有就是false
    private String userName;//用户名字

    private ArrayList<ResDepartAndEmployee> resDepartAndEmployees;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public boolean isArrow() {
        return arrow;
    }

    public void setArrow(boolean arrow) {
        this.arrow = arrow;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getOid() {
        return oid;
    }

    public void setOid(int oid) {
        this.oid = oid;
    }

    public int getDepartid() {
        return departid;
    }

    public void setDepartid(int departid) {
        this.departid = departid;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public ArrayList<ResDepartAndEmployee> getResDepartAndEmployees() {
        return resDepartAndEmployees;
    }

    public void setResDepartAndEmployees(ArrayList<ResDepartAndEmployee> resDepartAndEmployees) {
        this.resDepartAndEmployees = resDepartAndEmployees;
    }
}
