package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.WorkdayType;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by Administrator on 2018/4/9.
 * 考勤设置例外表（作息时间表）
 */
@Entity
@Table(name = "t_personnel_attendance_exception")
public class PersonnelAttendanceException extends BaseEntity {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构ID

    @Column(name="name"  , length=255 , nullable=true , unique=false)
    private String name;//名称

    @Column(name="exception_date"   , nullable=true , unique=false)
    private Date exceptionDate;//考勤日期

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//类型：1-上班改休息(假)，2-休息改上班(班)

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    @CreationTimestamp
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    @UpdateTimestamp
    private Date updateDate;

    @Transient
    private String userWorkType;  //某人在某天的出勤情况

    @Transient
    private Double overTimeDuration;  //某人在某天的加班时长


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getExceptionDate() {
        return exceptionDate;
    }

    public void setExceptionDate(Date exceptionDate) {
        this.exceptionDate = exceptionDate;
    }

    public String getType() {
        return type;
    }
    public WorkdayType getWorkdayType() {return WorkdayType.getByName(type);}

    @Deprecated
    public void setType(String type) {
        this.type = type;
        throw new RuntimeException("请使用枚举类复制！");
    }
    public void setType(WorkdayType type) {
        this.type = type!=null ? type.getIndex().toString() : null;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUserWorkType() {
        return userWorkType;
    }

    public void setUserWorkType(String userWorkType) {
        this.userWorkType = userWorkType;
    }

    public Double getOverTimeDuration() {
        return overTimeDuration;
    }

    public void setOverTimeDuration(Double overTimeDuration) {
        this.overTimeDuration = overTimeDuration;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }
}