package cn.sphd.miners.modules.equipment.controller;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquCategory;
import cn.sphd.miners.modules.equipment.service.ITEquCategoryService;
import cn.sphd.miners.modules.equipment.service.ITEquModelService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_分类
 * 20230209 1.247装备器具新增Controller
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment/category")
public class EquCategoryController {
    @Autowired
    private ITEquCategoryService tEquCategoryService;
    @Autowired
    private ITEquModelService tEquModelService;

    /**
     * 查询装备管理_分类
     * 20230209 1.247装备器具新增列表
     */
    @PostMapping("/list")
    @ResponseBody
    public synchronized List list(TEquCategory tEquCategory,User user) {
        //判断是否初始化数据
        TEquCategory c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        List<TEquCategory> initList = tEquCategoryService.selectTEquCategoryList(c);
        if(initList.isEmpty()){
            //初始化类别8
            initCategories(user);
        }

        TEquCategory n = new TEquCategory();
        n.setOrg(Long.valueOf(user.getOid()));
        if(tEquCategory.getEnabled()!=null){
            n.setEnabled(tEquCategory.getEnabled());
        }
        if(tEquCategory.getParent()!=null){
            n.setParent(tEquCategory.getParent());
        }
        List<TEquCategory> list = tEquCategoryService.selectTEquCategoryList(n);

        return list;
    }

    /**
     * 新增保存装备管理_分类
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquCategory tEquCategory, User user) {
        return toAjax(tEquCategoryService.insertTEquCategory(tEquCategory,user));
    }

    /**
     * 修改保存装备管理_分类
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquCategory tEquCategory,User user) {
        return toAjax(tEquCategoryService.updateTEquCategory(tEquCategory,user));
    }

    /**
     * 删除装备管理_分类
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquCategoryService.deleteTEquCategoryByIds(ids));
    }


    private void initCategories(User user) {
        tEquCategoryService.initCategories(user);
    }
}
