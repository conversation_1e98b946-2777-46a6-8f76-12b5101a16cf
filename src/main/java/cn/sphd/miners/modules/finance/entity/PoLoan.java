package cn.sphd.miners.modules.finance.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by lyx on 2022/11/18
 * 采购_借款表  1.231差额处理(此表废弃，改用LoanBiz表，1.233差额处理2--lyx/20230704)
 */
@Deprecated
@Entity
@Table(name="t_po_loan")
public class PoLoan implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="payment_apply"   , nullable=true , unique=false)
    private Integer paymentApply;   //支付申请ID

    @Column(name="orders_prepayment"   , nullable=true , unique=false)
    private Integer ordersPrepayment;   //预付款申请ID

    @Column(name="finance_payment"   , nullable=true , unique=false)
    private Integer financePayment;   //财务支付ID

    @Column(name="type"   , nullable=true , unique=false)
    private Integer type;   //类型:1-货款(票据处理的),2-预付款

    @Column(name="supplier"  , nullable=true , unique=false)
    private Integer supplier;  //供应商ID

    @Column(name="supplier_name"  , length=100 , nullable=true , unique=false)
    private String supplierName;  //供应商名称

    @Column(name="orders"  , nullable=true , unique=false)
    private Integer orders;  //订单ID

    @Column(name="amount"  , nullable=true , unique=false)
    private BigDecimal amount;   //金额

    @Column(name="finish_time"  , nullable=true , unique=false)
    private Date finishTime;   //完结时间

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description;   //描述

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作:1-增,2-删,3-改

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getPaymentApply() {
        return paymentApply;
    }

    public void setPaymentApply(Integer paymentApply) {
        this.paymentApply = paymentApply;
    }

    public Integer getFinancePayment() {
        return financePayment;
    }

    public void setFinancePayment(Integer financePayment) {
        this.financePayment = financePayment;
    }

    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getOrdersPrepayment() {
        return ordersPrepayment;
    }

    public void setOrdersPrepayment(Integer ordersPrepayment) {
        this.ordersPrepayment = ordersPrepayment;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
