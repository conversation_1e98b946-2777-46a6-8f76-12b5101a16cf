/**
 * reDevoloper : 侯杏哲 2017-03-31 重构页面交互逻辑  
 */
$(function(){
	getfirstDoc();
});
// 获取一级文件列表
function getfirstDoc(){
	$.ajax({
		url:"res_getFirstType.action",
		type:"post",
		dataType:"json",
		beforSend:function(){ loading.open(); } ,
		success:function(data){
			loading.close();
			var status = data["status"];
			var list = data["list"];
			var typeSize = data["typeSize"];
			if(status == 0){
				$("#alertMs").html("登录失效,请重新登录！");
				bounce.show( $("#alertMess") );
			} else{
				var dataInfo = { "sonNum": typeSize , "pid": 0 , "pName":"全部类别" , "list":list  } ;
				addData(dataInfo) ;
			}

		},
		error:function(){
			loading.close();
			alert("连接错误，请稍后重试！");
		}
	});
}
// 导航跳转
function goNav(obj , pid ){
	obj.parent().nextAll().remove() ; obj.parent().prev().remove(); obj.parent().remove(); 
	var pName = obj.html();
	getSonDoc( pid , pName) ;
}
// 编辑 类别
var editTr = null ;
function uptype(obj){
    var userType = chargeRole("超管");
    if (userType) { // 0 表示超管
		bounce.show( $("#alertMess") ) ;  $("#alertMs").html("您没有此权限！"); return false ;
	}
	editTr = obj.parent().parent();
	var id = obj.siblings(".hd").html();
	var name = obj.parent().siblings(":eq(0)").html();
	$("#docName").val( name );
	$("#editMs").html( JSON.stringify({"id": id , "type":"update" }) );
	$("#editTtl").html( "修改类别" );
	$("#edittip").html( "" );
	bounce.show( $("#editDoc") ) ;
	$("#editDoc").attr("class" , "bonceContainer bounce-blue");
	$("#okEdit").attr("class" , "ty-btn ty-btn-blue ty-circle-5 ty-btn-big");
}
// 新增 类别
function addtype(pid){
    var userType = chargeRole("超管");
    if (userType) { // 0 表示超管
		bounce.show( $("#alertMess") ) ;  $("#alertMs").html("您没有此权限！"); return false ;
	}
	$("#docName").val( "" );
	$("#edittip").html( "" );
	$("#editMs").html( JSON.stringify({"id": pid , "type":"add" }) );
	$("#editTtl").html( "修改类别" );
	bounce.show( $("#editDoc") ) ;
	$("#editDoc").attr("class" , "bonceContainer bounce-green");
	$("#okEdit").attr("class" , "ty-btn ty-btn-green ty-circle-5 ty-btn-big");
}
// 确定编辑
function okEdit(){
	var info = JSON.parse($("#editMs").html());
	var id = info["id"];
	var type = info["type"];
	var name = $("#docName").val();
	if( $.trim(name) == ""){  $("#edittip").html("名称不能为空！"); return false; }
	bounce.cancel(); 
	if(type == "add"){ // 新增
		loading.open();
		$.ajax({
			url:"res_addFirstType.action",
			data:{ "id" : id  , "name" : name } ,
			type:"post" , 
			dataType:"json",
			success:function(data){
				loading.close();
				var status=data["status"];
				if(status==0){
					$("#alertMs").html("登录失效,请重新登录！");
					bounce.show( $("#alertMess") );
				}else if(status==1){
					var list = data["list"];
					var id = list[0]["id"];
					var name = list[0]["name"];
					var create_date = list[0]["create_date"];
					var update_date = list[0]["update_date"];
					if(update_date==null){ update_date=""; }
					var str ="<tr>" +
						"<td onclick='getSon($(this))'>"+name+"</td>" +
						"<td>"+create_date+"</td><td>"+update_date+"</td>" +
						"<td>" +
						"<span onclick='uptype($(this))' class='ty-color-blue' >编辑</span>" +
						"<span onclick='deltype($(this))' class='ty-color-red'>删除</span>" +
						"<span  class='hd'>"+id+"</span>" +
						"</td></tr>";
					$("#dataList").append(str); 
					var num = parseInt( $("#sonNum").html() );
					$("#sonNum").html(++num) ; 
				}else if(status==2){
					$("#alertMs").html("此文件夹下已有文件，不能新增类别！");
					bounce.show( $("#alertMess") );
				}else{
					$("#alertMs").html("系统错误，新增失败！");
					bounce.show( $("#alertMess") );
				}
			},
			error:function(){
				loading.close();
			}
		});
	}else{ // 修改
		$.ajax({
			url:"res_editType.action",
			data:{ "id" : id , "name" : name  },
			type:"post",
			dataType:"json",
			success:function(data){
				var list=data["list"];
				var status=data["status"];
				if(status==0){
					$("#alertMs").html("登录失效,请重新登录！");
					bounce.show( $("#alertMess") );
				}else if(status==1){
					var id=list[0]["id"];
					var name=list[0]["name"];
					var create_date=list[0]["create_date"];
					var update_date=list[0]["update_date"];
					if(update_date==null){ update_date=""; }
					editTr.children(":eq(0)").html(name);
					editTr.children(":eq(2)").html(update_date);
				}else{
					$("#alertMs").html("系统错误，新增失败！");
					bounce.show( $("#alertMess") );
				}
			},
			error:function(){
			}
		});
	}


}
// 获得子级类别
function getSon(obj){
	var pid = obj.siblings(":last").children(".hd").html();
	var pName = obj.html();
	getSonDoc( pid , pName) ;
}
function getSonDoc( pid , pName){
	loading.open();
	$.ajax({
		url:"res_getSecondType.action",
		data:{ "id" : pid  },
		type:"post",
		dataType:"json",
		success:function(data){
			loading.close();
			var status=data["status"];
			if(status==0){
				$("#alertMs").html("登录失效,请重新登录！");
				bounce.show( $("#alertMess") );
			}else if(status==2){
				$("#alertMs").html("此文件夹下含有文件，没有子级类别！");
				bounce.show( $("#alertMess") );
			}else if(status==3){
				var typeSize=data["typeSize"];
				var dataInfo = { "sonNum": typeSize , "pid": pid , "pName":pName , "list":null  } ;
				addData(dataInfo) ;
			}else{
				var typeSize = data["typeSize"];
				var list = data["list"];
				var dataInfo = { "sonNum": typeSize , "pid": pid , "pName":pName , "list":list  } ;
				addData(dataInfo) ;
			}
		},
		error:function(){
			loading.close();
		}
	});
}
// 刷新数据
function addData(dataInfo){
	var sonNum = dataInfo["sonNum"] ;
	var list = dataInfo["list"] ;
	var pName = dataInfo["pName"] ;
	var pid = dataInfo["pid"] ;

	if( !sonNum ){ sonNum = 0 ; }
	$("#sonNum").html( sonNum ) ;
	if(pid != 0){
		var navStr = "<span class='nav_'> / </span><span class='navTxt'><a onclick='goNav($(this) ,"+ pid +" )'>"+ pName +"</a></span>" ;
		$("#docPath").append(navStr);
	}
	$("#addDoc").attr("onclick" , "addtype("+ pid +")");
	$("#pName").html(pName);
	var str = "" ;
	if( list && list.length >0 ){
		for(var i=0;i<list.length;i++){
			var id=list[i]["id"];
			var name=list[i]["name"];
			var create_date=list[i]["create_date"];
			var update_date=list[i]["update_date"];
			if(!update_date){ update_date=""; }
			str +="<tr>" +
				"<td onclick='getSon($(this))'>"+name+"</td>" +
				"<td>"+create_date+"</td><td>"+update_date+"</td>" +
				"<td>" +
				"<span onclick='uptype($(this))' class='ty-color-blue' >编辑</span>" +
				"<span onclick='deltype($(this))' class='ty-color-red'>删除</span>" +
				"<span  class='hd'>"+id+"</span>" +
				"</td></tr>";
		}
	}
	$("#dataList").html(str);
}
// 删除
function deltype( obj ){
    var userType = chargeRole("超管");
    if (userType) { // 0 表示超管
		bounce.show( $("#alertMess") ) ;  $("#alertMs").html("您没有此权限！"); return false ;
	}
	editTr = obj.parent().parent();  
	var id = obj.siblings(".hd").html(); 
	var name = obj.parent().siblings().html();
	var str = "确定删除 " +  name; 
	$("#confirmMs").html( str);
	$("#confirmTip").html( id );
	bounce.show($("#confirmMess")); 
}
// 确定删除
function delDoc(){
	var id = $("#confirmTip").html();
	$.ajax({
		url:"res_deleteType.action",
		data:{ "id":id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			if(status==0){
				$("#alertMs").html("登录失效,请重新登录！");
				bounce.show( $("#alertMess") );
			}else if(status==1){
				editTr.remove();
				var num = parseInt( $("#sonNum").html() );
				$("#sonNum").html(--num) ;
				$("#alertMs").html("删除成功！");
				bounce.show( $("#alertMess") );
				
			}else if(status==4){
				$("#alertMs").html("该文件夹下存在文件，不能删除！");
				bounce.show( $("#alertMess") );
			}else if(status==3){
				$("#alertMs").html("该文件下存在子类，不能删除！");
				bounce.show( $("#alertMess") );
			}else {
				$("#alertMs").html("系统错误，删除失败！");
				bounce.show( $("#alertMess") );
			}
		},
		error:function(){
		}
	});
}














 