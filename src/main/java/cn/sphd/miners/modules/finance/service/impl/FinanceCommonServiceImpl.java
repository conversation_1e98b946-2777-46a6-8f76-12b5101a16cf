package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.FinanceCommonService;
import cn.sphd.miners.modules.finance.service.FinancePaymentService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * lyx/********
 * 摘出财务公共的方法
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class FinanceCommonServiceImpl implements FinanceCommonService {

    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    FinanceReturnDao financeReturnDao;
    @Autowired
    FinanceChequeDetailDao financeChequeDetailDao;
    @Autowired
    FinancePaymentDao financePaymentDao;
    @Autowired
    FinanceAccountBillHistoryDao financeAccountBillHistoryDao;
    @Autowired
    FinancePaymentHistoryDao financePaymentHistoryDao;
    @Autowired
    FinanceReceiptDao financeReceiptDao;

    @Autowired
    AccountService accountService;
    @Autowired
    FinancePaymentService financePaymentService;

    @Override
    public void cashIncome() {
    }

    /**
     * 现金支出，有账户的
     * @param user
     * @param financeAccount 账户
     * @param amount 金额
     * @param billAmount 票面金额
     * @param balance 余额
     * @param summary 摘要
     * @param purpose 用途
     * @param method 0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param business 业务号
     * @param source 数据来源 1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69) 5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8-报销时多付出去的款---需收回的款(1.231差额处理)
     *         //9-多收来的款(1.233差额处理2)
     * @param oppositeCorp 收款单位
     * @param factDate 实际(付款/支出)日期'
     * @param modityStatus 数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
     */
    @Override
    public AccountDetail cashDisbursements(User user,FinanceAccount financeAccount, BigDecimal amount,BigDecimal billAmount, BigDecimal balance,String summary,String purpose,
               String method,Integer business,String source,String oppositeCorp,Date factDate,String modityStatus) {
        //生成财务明细
        AccountDetail accountDetail = new AccountDetail();
        accountDetail.setDebit(amount);//合计报销金额 录入支出
        accountDetail.setBalance(balance);
        accountDetail.setCreator(user.getUserID());
        accountDetail.setCreateName(user.getUserName());
        accountDetail.setCreateDate(new Date());
        accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
        accountDetail.setOrg(user.getOrganization());
        accountDetail.setSummary(summary);
        accountDetail.setPurpose(purpose);  //摘要与用途一样
        accountDetail.setAuditorName(user.getUserName());
        accountDetail.setBillAmount(billAmount);
        accountDetail.setModityStatus(modityStatus);//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
        accountDetail.setMethod(method);//0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
        accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
        accountDetail.setBusiness(business);
        accountDetail.setBusinessDate(new Date());
        accountDetail.setSource(source);  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
        // 5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8-报销时多付出去的款---需收回的款(1.231差额处理)
        //9-多收来的款(1.233差额处理2)
        accountDetail.setOppositeCorp(oppositeCorp); //付款单位(收款单位)
        accountDetail.setFactDate(factDate);

        //更新账户
        financeAccount.setDebit(financeAccount.getDebit().add(amount));//总支出加上
        financeAccount.setBalance(balance);//在余额中减去
        accountService.updateFinanceAccount(financeAccount);

        //月结
        AccountPeriod yue = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
        yue.setDebit(yue.getDebit().add(amount));
        yue.setBalance(financeAccount.getBalance());
        accountService.updateAccountPeroid(yue);

        //日结
        AccountPeriod ri = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());
        ri.setDebit(ri.getDebit().add(amount));
        ri.setBalance(financeAccount.getBalance());
        accountService.updateAccountPeroid(ri);

        accountDetail.setFid(financeAccount.getId().toString());
        accountDetail.setAccountId(financeAccount);
        accountDetail.setAccount(yue.getId().toString());
        accountService.saveAccountDetail(accountDetail);

        return accountDetail;
    }

    /**
     * 转账支票、承兑汇票和内部转账支票的支出(内部银行转账的还没走账，只是在审批过程中)
     * @param user
     * @param amount 金额
     * @param billAmount 票面金额
     * @param method 0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
     * @param invoiceId 票据id
     * @param summary 摘要
     * @param purpose 用途
     * @param source 数据来源1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
     *         // 5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8-报销时多付出去的款---需收回的款(1.231差额处理)
     *         //9-多收来的款(1.233差额处理2)
     * @param business 业务号
     * @param oppositeCorp 收款单位
     * @param receiver 接收经手人
     * @param operator 支付经手人
     * @param expireDate 支票到期日
     * @param receiveDate 接收日期
     * @param modityStatus 数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
     */
    @Override
    public FinanceAccountBill invoiceDisbursements(User user,BigDecimal amount,BigDecimal billAmount,String method,Integer invoiceId,String summary,
           String purpose,String source,Integer business,String oppositeCorp,String receiver,String operator,String expireDate,String receiveDate,String modityStatus) {
        FinanceAccountBill financeAccountBill = new FinanceAccountBill();
        if ("3".equals(method)||"4".equals(method)){
            //支出的外部转账支票 或者是 承兑汇票
            FinanceReturn fr = financeReturnDao.get(invoiceId);
            fr.setState("4");//1-有效,2-存入银行,3-作废,4-已支出
            if (StringUtils.isNotEmpty(summary)) {
                fr.setSummary(summary);
                fr.setPurpose(purpose);
            }
            fr.setUpdateDate(new Date());
            fr.setUpdateName(user.getUserName());
            fr.setUpdator(user.getUserID());
            financeReturnDao.update(fr);

            //支票明细
            financeAccountBill.setCreateDate(new Date());
            financeAccountBill.setCreateName(user.getUserName());
            financeAccountBill.setCreator(user.getUserID());
            financeAccountBill.setType("2");//1-收入，2-支出
            financeAccountBill.setAmount(fr.getAmount());//金额
            financeAccountBill.setBillAmount(fr.getAmount());  //票面金额
            financeAccountBill.setBillNo(fr.getReturnNo());//发票号码
            financeAccountBill.setFinanceReturn(fr);//回款票据外键
            financeAccountBill.setOrg(fr.getOrg());
            financeAccountBill.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            financeAccountBill.setSource(source);
            financeAccountBill.setBusiness(business);
            financeAccountBill.setBusinessDate(new Date());
            financeAccountBill.setSummary(summary);
            financeAccountBill.setPurpose(summary);
//                f.setOccurrenceDate(paymentDate);
            financeAccountBill.setOppositeCorp(oppositeCorp);  //收款单位
            financeAccountBill.setModityStatus(modityStatus);
            financeAccountBillDao.save(financeAccountBill);

        }else if ("6".equals(method)){
            //支出的内部银行转账支票
            FinanceChequeDetail financeChequeDetail = financeChequeDetailDao.get(invoiceId);
            financeChequeDetail.setReceiver(receiver);//接收经手人
            financeChequeDetail.setAmount(amount);
            financeChequeDetail.setBillAmount(billAmount);  //票面金额
            financeChequeDetail.setOperator(operator);//支付经手人
            financeChequeDetail.setFinancialHandling(user.getUserName());//财务经手人
            financeChequeDetail.setExpireDate(NewDateUtils.dateFromString(expireDate,"yyyy-MM-dd"));//支票到期日
            if (StringUtils.isNotEmpty(receiveDate)) {
                financeChequeDetail.setReceiveDate(NewDateUtils.dateFromString(receiveDate, "yyyy-MM-dd"));//接收日期
            }
            financeChequeDetail.setState("2");//1-未使用,2-已使用,3-作废
            financeChequeDetail.setModityStatus("1");
            financeChequeDetail.setUpdateDate(new Date());
            financeChequeDetail.setUpdateName(user.getUserName());
            financeChequeDetail.setUpdator(user.getUserID());
            financeChequeDetail.setReceiveCorp(oppositeCorp);  //收款单位
            financeChequeDetail.setSummary(summary);
            financeChequeDetail.setPurpose(purpose);
            financeChequeDetailDao.update(financeChequeDetail);

            //支票明细
            financeAccountBill.setOperatorName(user.getUserName());//经手人
            financeAccountBill.setCreateDate(new Date());
            financeAccountBill.setCreateName(user.getUserName());
            financeAccountBill.setCreator(user.getUserID());
            financeAccountBill.setType("2");//1-收入，2-支出
            financeAccountBill.setAmount(amount);//金额
            financeAccountBill.setBillAmount(billAmount);  //票面金额
            financeAccountBill.setBillNo(financeChequeDetail.getChequeNo());//发票号码
            financeAccountBill.setCheque(financeChequeDetail);//转账支票外键
            financeAccountBill.setCheque_(financeChequeDetail.getId());
            financeAccountBill.setOrg(user.getOrganization());
            financeAccountBill.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            financeAccountBill.setBusiness(business);
            financeAccountBill.setBusinessDate(new Date());
//                financeAccountBill.setOccurrenceDate(paymentDate);  //借款中的付款时间
            financeAccountBill.setSource(source);
//                financeAccountBill.setBusinessType(businessType);//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
            financeAccountBill.setOppositeCorp(oppositeCorp); //收款单位
            financeAccountBill.setPurpose(purpose);
            financeAccountBill.setSummary(summary);
            financeAccountBillDao.save(financeAccountBill);
        }
        return financeAccountBill;
    }

    @Override
    public void invoiceDisbursementsApproval(User user,FinanceAccount financeAccount,FinancePayment financePayment,BigDecimal amount,BigDecimal billAmount,BigDecimal balance,
         String summary,String purpose,String source,String oppositeCorp,String operatorName,Date factDate,Integer business,String modityStatus) {
        if ("5".equals(financePayment.getMethod())){  //银行转账
            //生成财务明细
            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setDebit(amount);//合计报销金额 录入支出
            accountDetail.setBalance(balance);
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setCreateDate(new Date());
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(purpose);  //摘要与用途一样
            accountDetail.setAuditor(user.getUserID());
            accountDetail.setAuditorName(user.getUserName());
            accountDetail.setBillAmount(billAmount);
            accountDetail.setModityStatus(modityStatus);//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
            accountDetail.setMethod("5");//银行转账
            accountDetail.setSource(source);  //5-票款处理(1.169采购的票款处理) 7-预付款(1.229采购之预付款)
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setOppositeCorp(oppositeCorp); //付款单位(收款单位)
            accountDetail.setPartnerName(operatorName); //合作方经手人
            if (financeAccount.getAccount()!=null){
                accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            }else {
                accountDetail.setAccountBank(financeAccount.getBankName());
            }
            accountDetail.setFactDate(factDate);
            accountDetail.setBusiness(business);


            //更新账户
            financeAccount.setDebit(financeAccount.getDebit().add(amount));//总支出加上
            financeAccount.setBalance(balance);//在余额中减去
            accountService.updateFinanceAccount(financeAccount);

            //月结
            AccountPeriod yue = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
            yue.setDebit(yue.getDebit().add(amount));
            yue.setBalance(balance);
            accountService.updateAccountPeroid(yue);

            //日结
            AccountPeriod ri = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());
            ri.setDebit(ri.getDebit().add(amount));
            ri.setBalance(balance);
            accountService.updateAccountPeroid(ri);

            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);
            accountDetail.setAccount(yue.getId().toString());
            accountService.saveAccountDetail(accountDetail);

            financePayment.setStatus("9"); //付款完成
            financePayment.setAccountDetail(accountDetail.getId());
            financePayment.setUpdator(user.getUserID());
            financePayment.setUpdateName(user.getUserName());
            financePayment.setUpdateDate(new Date());
            financePaymentDao.update(financePayment);
        }else if ("3".equals(financePayment.getMethod())||"4".equals(financePayment.getMethod())){
            //支出的外部转账支票 或者是 承兑汇票
            FinanceAccountBill financeAccountBill = financeAccountBillDao.get(financePayment.getAccountBill());
            if (financeAccountBill.getCheque_()!=null){ //支出的内部转账支票
                //支出的内部银行转账支票
                FinanceChequeDetail financeChequeDetail = financeChequeDetailDao.get(financeAccountBill.getCheque_());
                financeChequeDetail.setReceiver(operatorName); //接收经手人
                if (financeChequeDetail!=null&&financeChequeDetail.getReceiveDate()==null){
                    financeChequeDetail.setReceiveDate(factDate);//接收日期
                }
                financeChequeDetail.setFinancialHandling(user.getUserName());//财务经手人
                financeChequeDetail.setUpdateDate(new Date());
                financeChequeDetail.setUpdateName(user.getUserName());
                financeChequeDetail.setUpdator(user.getUserID());
                financeChequeDetail.setSummary(summary);
                financeChequeDetail.setPurpose(purpose);
                financeChequeDetailDao.update(financeChequeDetail);

                //生成财务明细
                AccountDetail accountDetail = new AccountDetail();
                accountDetail.setDebit(amount);//合计报销金额 录入支出
                accountDetail.setBalance(balance);
                accountDetail.setCreator(user.getUserID());
                accountDetail.setCreateName(user.getUserName());
                accountDetail.setCreateDate(new Date());
                accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                accountDetail.setOrg(user.getOrganization());
                accountDetail.setSummary(summary);
                accountDetail.setPurpose(purpose);
                accountDetail.setAuditor(user.getUserID());
                accountDetail.setAuditorName(user.getUserName());
                accountDetail.setBillAmount(billAmount);
                accountDetail.setModityStatus(modityStatus);//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
                accountDetail.setMethod(financePayment.getMethod());//银行转账
                accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                accountDetail.setBillDetail(financeAccountBill);
                accountDetail.setBillDetail_(financeAccountBill.getId());
                accountDetail.setOppositeCorp(oppositeCorp); //付款单位(收款单位)
                accountDetail.setPartnerName(operatorName);
                accountDetail.setFactDate(factDate);  //实际付款日期，收款单位接收日期
                accountDetail.setBusiness(business);

                //更新账户
                financeAccount.setDebit(financeAccount.getDebit().add(amount));//总支出加上
                financeAccount.setBalance(balance);//在余额中减去
                accountService.updateFinanceAccount(financeAccount);

                //月结
                AccountPeriod yue = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
                yue.setDebit(yue.getDebit().add(amount));
                yue.setBalance(balance);
                accountService.updateAccountPeroid(yue);

                //日结
                AccountPeriod ri = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());
                ri.setDebit(ri.getDebit().add(amount));
                ri.setBalance(balance);
                accountService.updateAccountPeroid(ri);

                accountDetail.setFid(financeAccount.getId().toString());
                accountDetail.setAccountId(financeAccount);
                accountDetail.setAccount(yue.getId().toString());
                accountDetail.setSource(source);
                accountService.saveAccountDetail(accountDetail);

                financePayment.setStatus("9"); //付款完成
                financePayment.setAccountDetail(accountDetail.getId());
                financePayment.setUpdator(user.getUserID());
                financePayment.setUpdateName(user.getUserName());
                financePayment.setUpdateDate(new Date());
                financePaymentDao.update(financePayment);
            }else {
                //支出的外部转账支票或承兑汇票
                FinanceReturn fr = financeReturnDao.get(financeAccountBill.getFinanceReturn().getId());
                //                      fr.setState("4");//1-有效,2-存入银行,3-作废,4-已支出
                fr.setOperatorName(operatorName); //接收人
//                        fr.setReceiveDate(receiveDate);  //接收日期
                fr.setSummary(summary);
                fr.setPurpose(purpose);
                fr.setUpdateDate(new Date());
                fr.setUpdateName(user.getUserName());
                fr.setUpdator(user.getUserID());
                financeReturnDao.update(fr);

            }
            financeAccountBill.setFactDate(factDate); //实际付款日期，收款单位接收日期
            financeAccountBillDao.update(financeAccountBill);  //其他的在添加的时候已经录入了

            financePayment.setStatus("9"); //付款完成
            financePayment.setUpdator(user.getUserID());
            financePayment.setUpdateName(user.getUserName());
            financePayment.setUpdateDate(new Date());
            financePaymentDao.update(financePayment);
        }
    }

    @Override
    public Integer getFinancePaymentNum(Integer business,String payStatus,String businessType){
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id) from FinancePayment where businessType=:businessType";
        map.put("businessType",businessType);
        if (business!=null){
            hql+=" and business=:business";
            map.put("business",business);
        }
        if (payStatus!=null){
            if ("10".equals(payStatus)){
                hql += " and status!='9'";
            }else {
                hql += " and status=:status";
                map.put("status", payStatus);
            }
        }
        Long num = (Long) financePaymentDao.getByHQLWithNamedParams(hql,map);
        return num!=null?num.intValue():0 ;
    }

    //修改前的数据处理[没有现金这种情况]  method(1-现金,2-现金支票,3-转帐支票(外部或内部),4-承兑汇票,5-银行转帐)  financeAccountBill-老数据的
    @Override
    public void updateBeforeFinancePayment(User user,FinancePayment financePayment,FinanceAccountBill financeAccountBill,FinanceAccountBill financeAccountBillNew){
        //银行转账的改financePaymentHistory就可以
        List<FinancePaymentHistory> financePaymentHistories = financePaymentService.getPaymentHistorysByPaymentId(financePayment.getId());

        if (financePaymentHistories.size() == 1) {

            //financeAccountBill为老数据的
            if (financeAccountBill!=null&&financeAccountBill.getId()!=null){
                //第一次付款方式的financeAccountBill添加到历史表里
                FinanceAccountBillHistory financeAccountBillHistory = new FinanceAccountBillHistory();
                BeanUtils.copyProperties(financeAccountBill,financeAccountBillHistory);
                financeAccountBillHistory.setBillDetail(financeAccountBill);
                financeAccountBillHistory.setBillDetail_(financeAccountBill.getId());
                if (financeAccountBill.getCheque()!=null) {
                    financeAccountBillHistory.setCheque(financeAccountBill.getCheque_());
                }
                if (financeAccountBill.getFinanceReturn()!=null) {
                    financeAccountBillHistory.setReturnBill(financeAccountBill.getFinanceReturn().getId());
                }

                financeAccountBillHistory.setCreateDate(new Date());
                financeAccountBillHistory.setCreateName(user.getUserName());
                financeAccountBillHistory.setCreator(user.getUserID());
                financeAccountBillHistory.setUpdateDate(new Date());
                financeAccountBillHistory.setUpdateName(user.getUserName());
                financeAccountBillHistory.setUpdator(user.getUserID());
                financeAccountBillHistory.setId(null);
                financeAccountBillHistoryDao.save(financeAccountBillHistory);
                financePaymentHistories.get(0).setAccountBillHistory(financeAccountBillHistory.getId());

                if (financeAccountBill.getCheque_()!=null){  //内部的转账支票---作废
                    FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                    financeChequeDetail.setState("3");//1-未使用,2-已使用,3-作废
                    financeChequeDetail.setUpdateDate(new Date());
                    financeChequeDetail.setUpdateName(user.getUserName());
                    financeChequeDetail.setUpdator(user.getUserID());
                    financeChequeDetailDao.update(financeChequeDetail);
                }

                if (financeAccountBill.getFinanceReturn()!=null&&financeAccountBill.getFinanceReturn().getId()!=null){  //承兑汇票和外部的转账支票 ---- 恢复为有效
                    //将添加的痕迹清空
                    FinanceReturn fr = financeAccountBill.getFinanceReturn();
                    fr.setState("1");//1-有效,2-存入银行,3-作废,4-已支出
//                fr.setPartnerName(receiver);
//                fr.setOperatorName(operator);
//                    fr.setOriginalCorp(null); //收款单位(原始出具单位)
//                    fr.setSummary(null);
                    fr.setUpdateDate(new Date());
                    fr.setUpdateName(user.getUserName());
                    fr.setUpdator(user.getUserID());
//                fr.setPayer(payer);  //付款单位
                    financeReturnDao.update(fr);
                }
            }

            //新数据的
            FinancePaymentHistory financePaymentHistory = financePaymentService.addPaymentHistory(financePayment, user);  //直接从历史里新增
            if (financeAccountBillNew!=null&&financeAccountBillNew.getId()!=null){
                FinanceAccountBillHistory financeAccountBillHistory1 = new FinanceAccountBillHistory();
                BeanUtils.copyProperties(financeAccountBillNew,financeAccountBillHistory1);
                financeAccountBillHistory1.setCreateDate(new Date());
                financeAccountBillHistory1.setCreateName(user.getUserName());
                financeAccountBillHistory1.setCreator(user.getUserID());
                financeAccountBillHistory1.setUpdateDate(new Date());
                financeAccountBillHistory1.setUpdateName(user.getUserName());
                financeAccountBillHistory1.setUpdator(user.getUserID());
                if (financeAccountBillNew.getCheque()!=null) {
                    financeAccountBillHistory1.setCheque(financeAccountBillNew.getCheque_());
                }
                if (financeAccountBillNew.getFinanceReturn()!=null) {
                    financeAccountBillHistory1.setReturnBill(financeAccountBillNew.getFinanceReturn().getId());
                }
                financeAccountBillHistory1.setId(null);
                financeAccountBillHistory1.setBillDetail(financeAccountBillNew);
                financeAccountBillHistory1.setBillDetail_(financeAccountBillNew.getId());
                financeAccountBillHistoryDao.save(financeAccountBillHistory1);
                financePaymentHistory.setAccountBill(financeAccountBillNew.getId());
                financePaymentHistory.setAccountBillHistory(financeAccountBillHistory1.getId());
            }

        } else if (financePaymentHistories.size() == 2) {
            FinancePaymentHistory financePaymentHistory1 = financePaymentHistories.get(0);
            FinancePaymentHistory financePaymentHistory2 = financePaymentHistories.get(1);

            //financeAccountBill为老数据的
            if (financeAccountBill!=null&&financeAccountBill.getId()!=null){
                if (financeAccountBill.getCheque_()!=null){  //内部的转账支票
                    FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                    financeChequeDetail.setState("3");//1-未使用,2-已使用,3-作废
                    financeChequeDetail.setUpdateDate(new Date());
                    financeChequeDetail.setUpdateName(user.getUserName());
                    financeChequeDetail.setUpdator(user.getUserID());
                    financeChequeDetailDao.update(financeChequeDetail);
                }

                if (financeAccountBill.getReturnBill()!=null){  //承兑汇票和外部的转账支票
                    //将添加的痕迹清空
                    FinanceReturn fr = financeAccountBill.getFinanceReturn();
                    fr.setState("1");//1-有效,2-存入银行,3-作废,4-已支出
//                fr.setPartnerName(receiver);
//                fr.setOperatorName(operator);
//                    fr.setOriginalCorp(null); //原始出具票据单位
//                    fr.setSummary(null);
                    fr.setUpdateDate(new Date());
                    fr.setUpdateName(user.getUserName());
                    fr.setUpdator(user.getUserID());
//                fr.setPayer(payer);  //付款单位
                    financeReturnDao.update(fr);
                }
            }

            financePaymentHistory1.setAccountBill(financePaymentHistory2.getAccountBill());
            financePaymentHistory1.setAccountId(financePaymentHistory2.getAccountId());
            financePaymentHistory1.setAmount(financePaymentHistory2.getAmount());
            financePaymentHistory1.setPlanAmount(financePaymentHistory2.getPlanAmount());
            financePaymentHistory1.setFactAmount(financePaymentHistory2.getFactAmount());
            financePaymentHistory1.setMethod(financePaymentHistory2.getMethod());
            financePaymentHistory1.setFactMethod(financePaymentHistory2.getFactMethod());
            financePaymentHistory1.setPlanMethod(financePaymentHistory2.getPlanMethod());
            financePaymentHistory1.setPlanDate(financePaymentHistory2.getPlanDate());
            financePaymentHistory1.setFactDate(financePaymentHistory2.getFactDate());
            financePaymentHistory1.setSummary(financePaymentHistory2.getSummary());
            financePaymentHistory1.setPurpose(financePaymentHistory2.getPurpose());
            financePaymentHistory1.setAccountBillHistory(financePaymentHistory2.getAccountBillHistory());
            financePaymentHistory1.setCreateDate(financePaymentHistory2.getCreateDate());
            financePaymentHistory1.setCreateName(financePaymentHistory2.getCreateName());
            financePaymentHistory1.setCreator(financePaymentHistory2.getCreator());
            financePaymentHistory1.setUpdateDate(new Date());
            financePaymentHistory1.setUpdateName(user.getUserName());
            financePaymentHistory1.setUpdator(user.getUserID());
            financePaymentHistoryDao.update(financePaymentHistory1);

            financePaymentHistory2.setAccountId(financePayment.getAccountId());
            financePaymentHistory2.setAmount(financePayment.getAmount());
            financePaymentHistory2.setPlanAmount(financePayment.getPlanAmount());
            financePaymentHistory2.setFactAmount(financePayment.getFactAmount());
            financePaymentHistory2.setMethod(financePayment.getMethod());
            financePaymentHistory2.setFactMethod(financePayment.getMethod());
            financePaymentHistory2.setPlanMethod(financePayment.getMethod());
            financePaymentHistory2.setPlanDate(financePayment.getPlanDate());
            financePaymentHistory2.setFactDate(financePayment.getFactDate());
            financePaymentHistory2.setSummary(financePayment.getSummary());
            financePaymentHistory2.setAccountBill(financePayment.getAccountBill());
            financePaymentHistory2.setCreateDate(new Date());
            financePaymentHistory2.setCreateName(user.getUserName());
            financePaymentHistory2.setCreator(user.getUserID());
            financePaymentHistory2.setUpdateDate(new Date());
            financePaymentHistory2.setUpdateName(user.getUserName());
            financePaymentHistory2.setUpdator(user.getUserID());
            financePaymentHistoryDao.update(financePaymentHistory2);

            //新数据的
            if (financeAccountBillNew!=null&&financeAccountBillNew.getId()!=null){
                FinanceAccountBillHistory financeAccountBillHistory = new FinanceAccountBillHistory();
                BeanUtils.copyProperties(financeAccountBillNew,financeAccountBillHistory);
                financeAccountBillHistory.setCreateDate(new Date());
                financeAccountBillHistory.setCreateName(user.getUserName());
                financeAccountBillHistory.setCreator(user.getUserID());
                financeAccountBillHistory.setUpdateDate(new Date());
                financeAccountBillHistory.setUpdateName(user.getUserName());
                financeAccountBillHistory.setUpdator(user.getUserID());
                if (financeAccountBillNew.getCheque()!=null) {
                    financeAccountBillHistory.setCheque(financeAccountBillNew.getCheque_());
                }
                if (financeAccountBillNew.getFinanceReturn()!=null) {
                    financeAccountBillHistory.setReturnBill(financeAccountBillNew.getFinanceReturn().getId());
                }
                financeAccountBillHistory.setId(null);
                financeAccountBillHistory.setBillDetail(financeAccountBillNew);
                financeAccountBillHistory.setBillDetail_(financeAccountBillNew.getId());
                financeAccountBillHistoryDao.save(financeAccountBillHistory);
                financePaymentHistory2.setAccountBill(financeAccountBillNew.getId());
                financePaymentHistory2.setAccountBillHistory(financeAccountBillHistory.getId());
            }
        }
    }

    @Override
    public List<FinanceReceipt> getFinanceReceiptList(Integer org,String businessType,Integer business) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceReceipt where org=:org";
        map.put("org",org);
        if (StringUtils.isNotEmpty(businessType)){
            hql+=" and businessType=:businessType";
            map.put("businessType",businessType);
        }
        if (business!=null){
            hql+=" and business=:business";
            map.put("business",business);
        }
        List<FinanceReceipt> financeReceiptList = financeReceiptDao.getListByHQLWithNamedParams(hql,map);
        return financeReceiptList;
    }
}
