package cn.sphd.miners.modules.material.service.impl;


import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.SupplierImageService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class SrmSupplierUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            SupplierImageService service = ac.getBean(SupplierImageService.class, "supplierImageService");

            switch (entityClass) {
                case "PoContractBase":
                    PoContractBase entity = service.getPoContractBase(id);
                    if (entity != null&&service.getSrmSupplier(entity.getSupplier())!=null) {
                        return filename.equals(entity.getFilePath());
                    }
                    break;
                case "PoContractBaseHistory":

                    PoContractBaseHistory historyEntity = service.getPoContractBaseHistory(id);
                    if(historyEntity != null) {
                        PoContractBase sh = service.getPoContractBase(historyEntity.getContract());

                        if(sh!=null && service.getSrmSupplier(sh.getSupplier())!=null){
                            return  filename.equals(historyEntity.getFilePath());
                        }
                    }
                    break;
                case "PoContractImage":

                    PoContractImage si = service.getPoContractImage(id);
                    if(si != null) {
                        PoContractBase sh = service.getPoContractBase(si.getContract());
                        if(sh!=null && service.getSrmSupplier(sh.getSupplier())!=null){
                            return  filename.equals(si.getUplaodPath());
                        }
                    }
                    break;
                case "PoContractImageHistory":
                    PoContractImageHistory sih = service.getPoContractImageHistory(id);
                    if(sih != null) {
                        PoContractBaseHistory sh = service.getPoContractBaseHistory(sih.getContractHistory());
                        PoContractBase sb = service.getPoContractBase(sh.getContract());
                        if(sh!=null && sb!=null){
                            if(service.getSrmSupplier(sb.getSupplier())!=null){
                                return  filename.equals(sih.getUplaodPath());
                            }
                        }
                    }
                    break;
                case "SrmSupplierContact":
                    SrmSupplierContact ssl = service.getSrmSupplierContact(id);
                    if(ssl!=null){
                        return  filename.equals(ssl.getCardPath());
                    }
                    break;
                case "SrmSupplierContactHistory":
                    SrmSupplierContactHistory sslh = service.getSrmSupplierContactHistory(id);
                    if(sslh!=null){
                        SrmSupplierContact sl = service.getSrmSupplierContact(sslh.getSupplierContact());
                        if(sl!=null){
                            return  filename.equals(sslh.getCardPath());
                        }

                    }
                    break;
                case "SrmSupplierImage":

                    SrmSupplierImage ssi = service.getSrmSupplierImage(id);
                    if(ssi != null) {
                        SrmSupplier sh = service.getSrmSupplier(ssi.getSupplier());
                        if(sh!=null ){
                            return  filename.equals(ssi.getNormal());
                        }
                    }
                    break;
                case "SrmSupplierImageHistory":
                    SrmSupplierImageHistory ssih = service.getSrmSupplierImageHistory(id);
                    if(ssih != null) {
                        SrmSupplierImage ssii = service.getSrmSupplierImage(ssih.getSupplierImage());
                        if(ssii != null) {
                            SrmSupplier sh = service.getSrmSupplier(ssii.getSupplier());
                            if(sh!=null ){
                                return  filename.equals(ssih.getNormal());
                            }
                        }
                    }
                    break;

            }
        }
        return false;
    }

    @Override
    @JsonIgnore
    @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public SrmSupplierUsing(Integer id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }

    public Integer getId() {
        return id;
    }

    public String getEntityClass() {
        return entityClass;
    }


    public void setId(Integer id) {
        this.id = id;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }

    public SrmSupplierUsing() {

    }
}
