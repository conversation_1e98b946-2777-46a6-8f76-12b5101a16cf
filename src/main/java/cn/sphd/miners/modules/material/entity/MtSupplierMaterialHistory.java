package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_mt_supplier_material_history")
public class MtSupplierMaterialHistory implements Serializable {
    private Integer id;
    private Integer org;
    private Integer materialHistory;
    private Integer supplierMaterial;
    private Integer material;
    private Integer supplier;
    private String enabled;
    private Date enabledTime;
    private Integer instance;
    private String instanceChain;
    private String approveStatus;
    private String rejectReasionDesc;
    private String isPurchased;
    private String isAppointed;
    private Date beginDate;
    private Date lastDate;
    private BigDecimal lastQuantity;
    private BigDecimal lastUnitPrice;
    private Integer currentYearNum;
    private BigDecimal currentYearQuantity;
    private BigDecimal currentYearUnitPrice;
    private Integer totalNum;
    private BigDecimal totalQuantity;
    private BigDecimal totalUnitPrice;
    private String hasContact;
    private String contractSn;
    private Date validDate;
    private Date signDate;
    private String invoicable;
    private String invoiceCategory;
    private BigDecimal taxRate;
    private Integer chargeAcceptable;
    private String chargeBegin;
    private Integer chargePeriod;
    private Integer draftAcceptable;
    private String isInclude;
    private int materialInvoicable;
    private String materialInvoiceCategory;
    private BigDecimal materialTaxRate;
    private String priceStable;
    private String isParValue;
    private String atPar;
    private int inclusiveFreight;
    private String packageMethod;
    private BigDecimal unitPrice;
    private Integer isTax;
    private BigDecimal unitPriceNotax;
    private Integer perchaseCycle;
    private Integer minimumPurchase;
    private String isImprest;
    private String payMethod;
    private BigDecimal minimumStock;
    private BigDecimal initialStock;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;

    //1.239
    private String deliveryAddress;
    private Integer supplierInvoice;

    private Integer expirationDays;//保质期天数

    @Column(name = "expiration_days")
    public Integer getExpirationDays() {
        return expirationDays;
    }

    public void setExpirationDays(Integer expirationDays) {
        this.expirationDays = expirationDays;
    }

    @Transient
    private SrmSupplier srmSupplier;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "material_history")
    public Integer getMaterialHistory() {
        return materialHistory;
    }

    public void setMaterialHistory(Integer materialHistory) {
        this.materialHistory = materialHistory;
    }

    @Basic
    @Column(name = "supplier_material")
    public Integer getSupplierMaterial() {
        return supplierMaterial;
    }

    public void setSupplierMaterial(Integer supplierMaterial) {
        this.supplierMaterial = supplierMaterial;
    }

    @Basic
    @Column(name = "material")
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "supplier")
    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    @Basic
    @Column(name = "enabled")
    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time")
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "instance")
    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    @Basic
    @Column(name = "instance_chain")
    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    @Basic
    @Column(name = "approve_status")
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Basic
    @Column(name = "reject_reasion_desc")
    public String getRejectReasionDesc() {
        return rejectReasionDesc;
    }

    public void setRejectReasionDesc(String rejectReasionDesc) {
        this.rejectReasionDesc = rejectReasionDesc;
    }

    @Basic
    @Column(name = "is_purchased")
    public String getIsPurchased() {
        return isPurchased;
    }

    public void setIsPurchased(String isPurchased) {
        this.isPurchased = isPurchased;
    }

    @Basic
    @Column(name = "is_appointed")
    public String getIsAppointed() {
        return isAppointed;
    }

    public void setIsAppointed(String isAppointed) {
        this.isAppointed = isAppointed;
    }

    @Basic
    @Column(name = "begin_date")
    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    @Basic
    @Column(name = "last_date")
    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }

    @Basic
    @Column(name = "last_quantity")
    public BigDecimal getLastQuantity() {
        return lastQuantity;
    }

    public void setLastQuantity(BigDecimal lastQuantity) {
        this.lastQuantity = lastQuantity;
    }

    @Basic
    @Column(name = "last_unit_price")
    public BigDecimal getLastUnitPrice() {
        return lastUnitPrice;
    }

    public void setLastUnitPrice(BigDecimal lastUnitPrice) {
        this.lastUnitPrice = lastUnitPrice;
    }

    @Basic
    @Column(name = "current_year_num")
    public Integer getCurrentYearNum() {
        return currentYearNum;
    }

    public void setCurrentYearNum(Integer currentYearNum) {
        this.currentYearNum = currentYearNum;
    }

    @Basic
    @Column(name = "current_year_quantity")
    public BigDecimal getCurrentYearQuantity() {
        return currentYearQuantity;
    }

    public void setCurrentYearQuantity(BigDecimal currentYearQuantity) {
        this.currentYearQuantity = currentYearQuantity;
    }

    @Basic
    @Column(name = "current_year_unit_price")
    public BigDecimal getCurrentYearUnitPrice() {
        return currentYearUnitPrice;
    }

    public void setCurrentYearUnitPrice(BigDecimal currentYearUnitPrice) {
        this.currentYearUnitPrice = currentYearUnitPrice;
    }

    @Basic
    @Column(name = "total_num")
    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    @Basic
    @Column(name = "total_quantity")
    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    @Basic
    @Column(name = "total_unit_price")
    public BigDecimal getTotalUnitPrice() {
        return totalUnitPrice;
    }

    public void setTotalUnitPrice(BigDecimal totalUnitPrice) {
        this.totalUnitPrice = totalUnitPrice;
    }

    @Basic
    @Column(name = "has_contact")
    public String getHasContact() {
        return hasContact;
    }

    public void setHasContact(String hasContact) {
        this.hasContact = hasContact;
    }

    @Basic
    @Column(name = "contract_sn")
    public String getContractSn() {
        return contractSn;
    }

    public void setContractSn(String contractSn) {
        this.contractSn = contractSn;
    }

    @Basic
    @Column(name = "valid_date")
    public Date getValidDate() {
        return validDate;
    }

    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }

    @Basic
    @Column(name = "sign_date")
    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    @Basic
    @Column(name = "invoicable")
    public String getInvoicable() {
        return invoicable;
    }

    public void setInvoicable(String invoicable) {
        this.invoicable = invoicable;
    }

    @Basic
    @Column(name = "invoice_category")
    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    @Basic
    @Column(name = "tax_rate")
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }


    @Basic
    @Column(name = "charge_acceptable")
    public Integer getChargeAcceptable() {
        return chargeAcceptable;
    }

    public void setChargeAcceptable(Integer chargeAcceptable) {
        this.chargeAcceptable = chargeAcceptable;
    }

    @Basic
    @Column(name = "charge_begin")
    public String getChargeBegin() {
        return chargeBegin;
    }

    public void setChargeBegin(String chargeBegin) {
        this.chargeBegin = chargeBegin;
    }

    @Basic
    @Column(name = "charge_period")
    public Integer getChargePeriod() {
        return chargePeriod;
    }

    public void setChargePeriod(Integer chargePeriod) {
        this.chargePeriod = chargePeriod;
    }

    @Basic
    @Column(name = "draft_acceptable")
    public Integer getDraftAcceptable() {
        return draftAcceptable;
    }

    public void setDraftAcceptable(Integer draftAcceptable) {
        this.draftAcceptable = draftAcceptable;
    }

    @Basic
    @Column(name = "is_include")
    public String getIsInclude() {
        return isInclude;
    }

    public void setIsInclude(String isInclude) {
        this.isInclude = isInclude;
    }

    @Basic
    @Column(name = "material_invoicable")
    public int getMaterialInvoicable() {
        return materialInvoicable;
    }

    public void setMaterialInvoicable(int materialInvoicable) {
        this.materialInvoicable = materialInvoicable;
    }

    @Basic
    @Column(name = "material_invoice_category")
    public String getMaterialInvoiceCategory() {
        return materialInvoiceCategory;
    }

    public void setMaterialInvoiceCategory(String materialInvoiceCategory) {
        this.materialInvoiceCategory = materialInvoiceCategory;
    }

    @Basic
    @Column(name = "material_tax_rate")
    public BigDecimal getMaterialTaxRate() {
        return materialTaxRate;
    }

    public void setMaterialTaxRate(BigDecimal materialTaxRate) {
        this.materialTaxRate = materialTaxRate;
    }

    @Basic
    @Column(name = "price_stable")
    public String getPriceStable() {
        return priceStable;
    }

    public void setPriceStable(String priceStable) {
        this.priceStable = priceStable;
    }

    @Basic
    @Column(name = "is_par_value")
    public String getIsParValue() {
        return isParValue;
    }

    public void setIsParValue(String isParValue) {
        this.isParValue = isParValue;
    }

    @Basic
    @Column(name = "at_par")
    public String getAtPar() {
        return atPar;
    }

    public void setAtPar(String atPar) {
        this.atPar = atPar;
    }

    @Basic
    @Column(name = "inclusive_freight")
    public int getInclusiveFreight() {
        return inclusiveFreight;
    }

    public void setInclusiveFreight(int inclusiveFreight) {
        this.inclusiveFreight = inclusiveFreight;
    }

    @Basic
    @Column(name = "package_method")
    public String getPackageMethod() {
        return packageMethod;
    }

    public void setPackageMethod(String packageMethod) {
        this.packageMethod = packageMethod;
    }

    @Basic
    @Column(name = "unit_price")
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Basic
    @Column(name = "is_tax")
    public Integer getIsTax() {
        return isTax;
    }

    public void setIsTax(Integer isTax) {
        this.isTax = isTax;
    }

    @Basic
    @Column(name = "unit_price_notax")
    public BigDecimal getUnitPriceNotax() {
        return unitPriceNotax;
    }

    public void setUnitPriceNotax(BigDecimal unitPriceNotax) {
        this.unitPriceNotax = unitPriceNotax;
    }

    @Basic
    @Column(name = "perchase_cycle")
    public Integer getPerchaseCycle() {
        return perchaseCycle;
    }

    public void setPerchaseCycle(Integer perchaseCycle) {
        this.perchaseCycle = perchaseCycle;
    }

    @Basic
    @Column(name = "minimum_purchase")
    public Integer getMinimumPurchase() {
        return minimumPurchase;
    }

    public void setMinimumPurchase(Integer minimumPurchase) {
        this.minimumPurchase = minimumPurchase;
    }

    @Basic
    @Column(name = "is_imprest")
    public String getIsImprest() {
        return isImprest;
    }

    public void setIsImprest(String isImprest) {
        this.isImprest = isImprest;
    }

    @Basic
    @Column(name = "pay_method")
    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    @Basic
    @Column(name = "minimum_stock")
    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    @Basic
    @Column(name = "initial_stock")
    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Column(name = "delivery_address")
    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    @Column(name = "supplier_invoice")
    public Integer getSupplierInvoice() {
        return supplierInvoice;
    }

    public void setSupplierInvoice(Integer supplierInvoice) {
        this.supplierInvoice = supplierInvoice;
    }

    @Transient
    public SrmSupplier getSrmSupplier() {
        return srmSupplier;
    }

    public void setSrmSupplier(SrmSupplier srmSupplier) {
        this.srmSupplier = srmSupplier;
    }
}
