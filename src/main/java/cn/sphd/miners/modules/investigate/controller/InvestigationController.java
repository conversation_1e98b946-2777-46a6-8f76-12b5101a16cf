package cn.sphd.miners.modules.investigate.controller;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.investigate.dto.*;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubject;
import cn.sphd.miners.modules.investigate.service.InvestigationService;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.List;


/**
 * @ClassName InvestigationManageController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/13 15:57
 * @Version 1.0
 *
 */
@Controller
@RequestMapping("/investigationManage")
public class InvestigationController {
    @Autowired
    InvestigationService investigationService;

    @RequestMapping("/goInvestigation.do")
    public String assessManageIndex() {
        return "investigationManage/investigation";
    }

    @RequestMapping("/goQuestionBank.do")
    public String questionBankIndex() { return "investigationManage/questionBank"; }

    //问卷首页接口
    //status 1启用状态的，0停用状态的
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectSubjectList.do")
    public RespInvestigateListPage selectSubjectList(User user, PageInfo pageInfo, Integer status, String month) throws IOException {
        RespInvestigateListPage respInvestigateListPage =new RespInvestigateListPage();
        if(status==null)
            status=1;
        List<InvestigateSubject> list=investigationService.selectInvestigateSubjectList(user,pageInfo,status,month);
        respInvestigateListPage.setInvestigateSubjectList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        int sum=investigationService.countInvestigateSubject(user,status,month);
        respInvestigateListPage.setSum(sum);
        respInvestigateListPage.setPageInfo(pageInfo);
        return respInvestigateListPage;
    }

    //已停用问卷月列表
    @ResponseBody
    @RequestMapping("/selectSubjectStopByMonthList.do")
    public RespInvestigateStopByMonthList selectSubjectStopByMonthList(User user, String year) throws IOException {
        RespInvestigateStopByMonthList respInvestigateStopByMonthList =investigationService.selectSubjectStopByMonthList(user,year);
        return respInvestigateStopByMonthList;
    }

    //查询接口
    //keyword 内容
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectSubjectByKeywordList.do")
    public RespInvestigateListPage selectSubjectByKeywordList(User user, PageInfo pageInfo, String keyword) throws IOException {
        RespInvestigateListPage respInvestigateListPage =new RespInvestigateListPage();
        List<InvestigateSubject> list=investigationService.selectSubjectByNameList(user,pageInfo,keyword);
        int sum=investigationService.countSubjectByKeyword(user,keyword);
        respInvestigateListPage.setInvestigateSubjectList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respInvestigateListPage.setPageInfo(pageInfo);
        return respInvestigateListPage;
    }

    //新增问卷接口
    //title 标题 header页眉
    //subjectTagListJson 类别描述[name 名称 beginOrder 问题开始的题号]

    //questionListJson 问题列表 [ type类型:1-问答题,2-判断题,3-单选题,4-多选题
    //                          isRequired 是否必填 1必填，0非必填
    //                          content 内容
    //                          orders 题号
    //                          investigateQuestionKeyJson选项[
    //                              content 内容
    //                              orders  排序
    //                              ]
    //                          ]

    @ResponseBody
    @RequestMapping("/addSubject.do")
    public RespStatus addSubject(User user,ReqInvestigateSubject reqInvestigateSubject) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=investigationService.addSubject(user,reqInvestigateSubject);
        respStatus.setStatus(status);
        return respStatus;
    }
    //查看问卷
    //传值 id
    //返回值  title标题  header页眉
    //respInvestigateSubjectTagList[
    //              name 名称
    //              investigateQuestionList[
    //                              type类型:1-问答题,2-判断题,3-单选题,4-多选题
    //                              isRequired 是否必填 1必填，0非必填
    //                              content 内容
    //                              orders 题号
    //                              investigateQuestionKeyList[  选项
    //                                           content 内容
    //                                          ]
    //              ]
    //          ]
    @ResponseBody
    @RequestMapping("/selectSubject.do")
    public RespInvestigateSubject selectSubject(int id) throws IOException {
        RespInvestigateSubject respInvestigateSubject=investigationService.selectSubject(id);
        return respInvestigateSubject;
    }

    //停用、启用接口 id type 1启用，0停用
    @ResponseBody
    @RequestMapping("/updateEnabledSubject.do")
    public RespStatus updateEnabledSubject(int id,int type,User user) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=investigationService.updateEnabledSubject(id,type,user);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除判断接口
    //传值 id 返回值 status  1可以删除，-1已使用，不可以删除
    @ResponseBody
    @RequestMapping("/deleteSubjectJudge.do")
    public RespStatus deleteSubjectJudge(int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=investigationService.deleteSubjectJudge(id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除接口
    //传值 id 返回值 status  1删除成功，-1已使用，不可以删除，0删除失败
    @ResponseBody
    @RequestMapping("/deleteSubject.do")
    public RespStatus deleteSubject(int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=investigationService.deleteSubject(id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改问题以外的内容
    //id
    //title 标题 header页眉
    //tagStatus 标签是否有修改，1修改，0未修改 为1时，所有subjectTag类别都传上来整体替换
    //subjectTagListJson 类别描述[name 名称 beginOrder 问题开始的题号]
    //questionListJson [id isRequired 是否必填 1必填，0非必填]
    @ResponseBody
    @RequestMapping("/updateSubjectOrTag.do")
    public RespStatus updateSubjectOrTag(User user,ReqInvestigateSubject reqInvestigateSubject) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=investigationService.updateSubjectOrTag(user,reqInvestigateSubject);
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改问卷
    //id
    //title 标题 header页眉
    //subjectTagListJson 类别描述[name 名称 beginOrder 问题开始的题号]
    //questionListJson 问题列表 [ type类型:1-问答题,2-判断题,3-单选题,4-多选题
    //                          isRequired 是否必填 1必填，0非必填
    //                          content 内容
    //                          orders 题号
    //                          investigateQuestionKeyJson选项[
    //                              content 内容
    //                              orders  排序
    //                              ]
    //                          ]
    //返回值 status -1已经使用,无法修改,  0修改失败,1修改成功
    @ResponseBody
    @RequestMapping("/updateSubject.do")
    public RespStatus updateSubject(User user,ReqInvestigateSubject reqInvestigateSubject) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=investigationService.deleteSubjectJudge(reqInvestigateSubject.getId());
        if(status==1)
            status=investigationService.updateSubject(user,reqInvestigateSubject);
        respStatus.setStatus(status);
        return respStatus;
    }
    //id
    //修改问卷判断接口
    //status -1已经使用,无法修改,  可以修改
    @ResponseBody
    @RequestMapping("/updateSubjectJudge.do")
    public RespStatus updateSubjectJudge(Integer id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=investigationService.deleteSubjectJudge(id);
        respStatus.setStatus(status);
        return respStatus;
    }

}
