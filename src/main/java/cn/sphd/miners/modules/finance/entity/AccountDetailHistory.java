package cn.sphd.miners.modules.finance.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by 赵应 on 2016-07-12.
 */
@Entity
@Table(name = "t_finance_account_detail_history")
public class AccountDetailHistory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;

    @Column(name="account"  , length=50 , nullable=true , unique=false)
    private String account;

    @Column(name="sub_acount"  , length=50 , nullable=true , unique=false)
    private String subAcount;

    @Column(name="balance"   , nullable=true , unique=false)
    private BigDecimal balance;//余额

    @Column(name="credit"   , nullable=true , unique=false)
    private BigDecimal credit;//收入

    @Column(name="debit"   , nullable=true , unique=false)
    private BigDecimal debit;//支出

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;  //审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核',

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;//审批者

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;//审批日期

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;//申请备注

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;//审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name = "illustrate", length=255 , nullable=true , unique=false)
    private String illustrate;

    @Column(name = "summary", length=255 , nullable=true , unique=false)
    private String summary;//摘要

    @Column(name = "handle_reply" , length=255 , nullable=true , unique=false)
    private String handleReply;//处理回复

    @Column(name = "type",length = 1,nullable = true,unique = false)
    private String type;//1-初始金额冲减,2-转帐交易,3-收入,4-支出

    @Column(name = "account_name",length = 255,nullable = true,unique = false)
    private String accountName;//

    @Column(name = "payment_unit",length=255 , nullable=true , unique=false)
    private String paymentUnit;

    @Column(name = "method",length = 1,nullable = true,unique = false)
    private String method;//0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账

    @Column(name= "genre",length = 1,nullable = true,unique = false)
    private String genre;//类别 1-贷款，2-借款，3-投资款，4-废品，5-其他

    @Column(name="category_desc"  , length=100 , nullable=true , unique=false)
    private String categoryDesc; //当类别为其他时候的说明

    @Column(name = "opposite_corp",length=255 , nullable=true , unique=false)
    private String oppositeCorp;//付款单位

    @Column(name = "receive_account_date" , nullable=true , unique=false)
    private Date receiveAccountDate;//到账时间

    @Column(name = "purpose",length=255 , nullable=true , unique=false)
    private String purpose;//用途

    @Column(name = "opposite_account",length = 255,nullable = true,unique = false)
    private String oppositeAccount;//对方账户名和账号（收款时为付款账号，付款时为收款账号）

    @Column(name = "opposite_id",nullable = true,unique = false)
    private Integer oppositeId;//对方账户id（收款时为付款账号id，付款时为收款账号id）

    @Column(name = "account_bank",nullable = true,unique = false)
    private String accountBank;//账号+银行名

    @Column(name = "opposite_bankno",nullable = true,unique = false)
    private String oppositeBankno;//对方银行代码银行转帐时使用（开户行）

    @Column(name = "opposite_bankcode",nullable = true,unique = false)
    private String oppositeBankcode;//对方银行帐号(银行转帐时使用)

    @Column(name = "account_id",nullable = true,unique = false)
    private Integer accountId; //账户id

    @Column(name = "bill_history_id",nullable = true,unique = false)
    private Integer billHistoryId; //bill历史表id（当支票改变时用）

    @Column(name = "bill_cat",nullable = true,unique = false)
    private String billCat;  // 票据类型

    @Column(name = "bill_quantity",nullable = true,unique = false)
    private Integer billQuantity;  //票据数量

    @Column(name = "bill_period",nullable = true,unique = false)
    private String billPeriod;  //1-本月票据,2-非本月票据

    @Column(name = "cheque_id",nullable = true,unique = false)
    private Integer chequeId;  //支票id

    @Column(name = "cheque_no",nullable = true,unique = false)
    private String chequeNo;  //支票号

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;  //票面金额

    @Column(name = "business_date" , nullable=true , unique=false)
    private Date businessDate;//业务发生日期

    @Column(name = "business")
    private Integer business;//业务号 （1.207财务税种中，此字段代表报表id【report】）

    @Column(name = "business_key")
    private Integer businessKey;//业务关键字ID (1.207财务税种新增，在此是税种id【taxId】)

    @Column(name = "business_period_begin")
    private Integer businessPeriodBegin;//业务期起始(YYYYMMDD)(1.207财务税种新增)

    @Column(name = "business_period_end")
    private Integer businessPeriodEnd;//业务期截止(YYYYMMDD)(1.207财务税种新增)

    @Column(name="business_data"  , nullable=true , unique=false)
    private String businessData;   //业务数据,以json方式存储,存接口参数等(1.207财务税种新增)

    @Column(name= "source",length = 1,nullable = true,unique = false)
    private String source;//数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)

    @Column(name= "business_type",nullable = true,unique = false)
    private String businessType;//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款,0103-常规借款借出，0104-常规借款收款

    @Column(name = "business_history")
    private Integer businessHistory;//历史表中的业务号

    @Column(name = "previous_id")
    private Integer previousId;   //修改前记录ID

    @Column(name = "version_no")
    private Integer versionNo;   //版本号,每次修改+1

    @Column(name="partner_name"  , length=100 , nullable=true , unique=false)
    private String partnerName;//合作方经手人姓名

    @Column(name="sub_type"  , length=1 , nullable=true , unique=false)
    private String subType;   //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出

    @Column(name="stakeholder_category"  , length=1 , nullable=true , unique=false)
    private String stakeholderCategory;   //类别:1-供应商,2-员工,3-自行录入

    @Column(name = "stakeholder")
    private Integer stakeholder;//干系人ID

    @Column(name = "stakeholders_history")
    private Integer stakeholdersHistory;//干系人历史ID

    @Column(name = "fact_user")
    private Integer factUser;//实际用户(同事userid)',

    @Column(name="bill_date"   , nullable=true , unique=false)
    private Date billDate;  //票据日期

    @Column(name="fact_date"   , nullable=true , unique=false)
    private Date factDate;  //实际(付款/支出)日期'

    @Column(name = "voucher")
    private Integer voucher;//凭证ID,为空时-未入会计

    @Column(name="is_account"   , nullable=true , unique=false)
    private Boolean aIsAccount;  //是否下账,false-不予下账,true-下帐

    @Column(name="is_modify"   , nullable=true , unique=false)
    private Boolean isModify;  //是否修改,false-未修改(0),true-已修改(1)

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY ,cascade = CascadeType.REMOVE)
    @JoinColumn(name="detail_id", referencedColumnName = "id", nullable=true , unique=false , insertable=true, updatable=true)
    private AccountDetail detailId;

    @Column(name="detail_id"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer detailId_;

    @Transient
    private Date beginDate; //报销中的发生时间开始(1.207财务优化之税种-申报记录开始时间)

    @Transient
    private Date endDate;  //报销中的发生时间结束(1.207财务优化之税种-申报记录结束时间)

    @Transient
    private String taxName;  //税种名称(1.207财务优化之税种)

    @Transient
    private Integer report;  //报表id(1.207财务优化之税种)

    @Transient
    private Integer taxPeriodId;  //申报记录id(1.207财务优化之税种)

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getSubAcount() {
        return subAcount;
    }

    public void setSubAcount(String subAcount) {
        this.subAcount = subAcount;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public AccountDetail getDetailId() {
        return detailId;
    }

    public void setDetailId(AccountDetail detailId) {
        this.detailId = detailId;
    }

    public Integer getDetailId_() {
        return detailId_;
    }

    public void setDetailId_(Integer detailId_) {
        this.detailId_ = detailId_;
    }

    public String getIllustrate() {
        return illustrate;
    }

    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getHandleReply() {
        return handleReply;
    }

    public void setHandleReply(String handleReply) {
        this.handleReply = handleReply;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getGenre() {
        return genre;
    }

    public void setGenre(String genre) {
        this.genre = genre;
    }

    public String getOppositeCorp() {
        return oppositeCorp;
    }

    public void setOppositeCorp(String oppositeCorp) {
        this.oppositeCorp = oppositeCorp;
    }

    public Date getReceiveAccountDate() {
        return receiveAccountDate;
    }

    public void setReceiveAccountDate(Date receiveAccountDate) {
        this.receiveAccountDate = receiveAccountDate;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getOppositeAccount() {
        return oppositeAccount;
    }

    public void setOppositeAccount(String oppositeAccount) {
        this.oppositeAccount = oppositeAccount;
    }

    public Integer getOppositeId() {
        return oppositeId;
    }

    public void setOppositeId(Integer oppositeId) {
        this.oppositeId = oppositeId;
    }

    public String getAccountBank() {
        return accountBank;
    }

    public void setAccountBank(String accountBank) {
        this.accountBank = accountBank;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getBillHistoryId() {
        return billHistoryId;
    }

    public void setBillHistoryId(Integer billHistoryId) {
        this.billHistoryId = billHistoryId;
    }

    public Integer getChequeId() {
        return chequeId;
    }

    public void setChequeId(Integer chequeId) {
        this.chequeId = chequeId;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getBillCat() {
        return billCat;
    }

    public void setBillCat(String billCat) {
        this.billCat = billCat;
    }

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    public String getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(String billPeriod) {
        this.billPeriod = billPeriod;
    }

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPaymentUnit() {
        return paymentUnit;
    }

    public void setPaymentUnit(String paymentUnit) {
        this.paymentUnit = paymentUnit;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getBusinessHistory() {
        return businessHistory;
    }

    public void setBusinessHistory(Integer businessHistory) {
        this.businessHistory = businessHistory;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getOppositeBankno() {
        return oppositeBankno;
    }

    public void setOppositeBankno(String oppositeBankno) {
        this.oppositeBankno = oppositeBankno;
    }

    public String getOppositeBankcode() {
        return oppositeBankcode;
    }

    public void setOppositeBankcode(String oppositeBankcode) {
        this.oppositeBankcode = oppositeBankcode;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getStakeholderCategory() {
        return stakeholderCategory;
    }

    public void setStakeholderCategory(String stakeholderCategory) {
        this.stakeholderCategory = stakeholderCategory;
    }

    public Integer getStakeholder() {
        return stakeholder;
    }

    public void setStakeholder(Integer stakeholder) {
        this.stakeholder = stakeholder;
    }

    public Integer getStakeholdersHistory() {
        return stakeholdersHistory;
    }

    public void setStakeholdersHistory(Integer stakeholdersHistory) {
        this.stakeholdersHistory = stakeholdersHistory;
    }

    public Integer getFactUser() {
        return factUser;
    }

    public void setFactUser(Integer factUser) {
        this.factUser = factUser;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public Date getFactDate() {
        return factDate;
    }

    public void setFactDate(Date factDate) {
        this.factDate = factDate;
    }

    public Integer getVoucher() {
        return voucher;
    }

    public void setVoucher(Integer voucher) {
        this.voucher = voucher;
    }

    public Boolean getaIsAccount() {
        return aIsAccount;
    }

    public void setaIsAccount(Boolean aIsAccount) {
        this.aIsAccount = aIsAccount;
    }

    public Boolean getModify() {
        return isModify;
    }

    public void setModify(Boolean modify) {
        isModify = modify;
    }

    public Integer getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(Integer businessKey) {
        this.businessKey = businessKey;
    }

    public Integer getBusinessPeriodBegin() {
        return businessPeriodBegin;
    }

    public void setBusinessPeriodBegin(Integer businessPeriodBegin) {
        this.businessPeriodBegin = businessPeriodBegin;
    }

    public Integer getBusinessPeriodEnd() {
        return businessPeriodEnd;
    }

    public void setBusinessPeriodEnd(Integer businessPeriodEnd) {
        this.businessPeriodEnd = businessPeriodEnd;
    }

    public String getBusinessData() {
        return businessData;
    }

    public void setBusinessData(String businessData) {
        this.businessData = businessData;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getTaxName() {
        return taxName;
    }

    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    public Integer getReport() {
        return report;
    }

    public void setReport(Integer report) {
        this.report = report;
    }

    public Integer getTaxPeriodId() {
        return taxPeriodId;
    }

    public void setTaxPeriodId(Integer taxPeriodId) {
        this.taxPeriodId = taxPeriodId;
    }
}
