package cn.sphd.miners.modules.personal.entity;

import com.alibaba.fastjson.annotation.JSONField;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_personnel_reimburset_attachment_history")
public class PersonnelReimbursetAttachmentHistory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description;

    @Column(name="path"  , length=255 , nullable=true , unique=false)
    private String path;

    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="reimbursetH", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelReimburseHistory reimbursetH;  //报销id

    @Column(name="reimbursetH"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer reimbursetH_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public PersonnelReimburseHistory getReimbursetH() {
        return reimbursetH;
    }

    public void setReimbursetH(PersonnelReimburseHistory reimbursetH) {
        this.reimbursetH = reimbursetH;
    }

    public Integer getReimbursetH_() {
        return reimbursetH_;
    }

    public void setReimbursetH_(Integer reimbursetH_) {
        this.reimbursetH_ = reimbursetH_;
    }
}
