package cn.sphd.miners.modules.recruit.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

/**
 * 招聘面试通知表
 * <AUTHOR>
 * @date 20230506
 **/
@Entity
@Table(name = "t_rd_interview_notification")
public class RdInterviewNotification extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    @Column
    private Integer org;//机构ID

    @Column(name = "notice_post",nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticePostId;//招聘岗位ID

    @Column(name = "resume",nullable=true , unique=false, insertable=false, updatable=false)
    private Long resumeId;//简历ID

    @Column(name = "notice_time")
    private Date noticeTime;//通知时间

    @Column(name = "notice_count")
    private Integer noticeCount;//通知次数

    @Column(name = "notice_method")
    private Integer noticeMethod;//通知方式:1-电话,2-短信,3-邮件,4-以上组合,5-其它

    @Column(length = 255)
    private String memo; //备注

    @Column(name = "modify_record")
    private String modifyRecord; // 记录每次 修改时间和修改人的串

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"   , nullable=true , unique=false)
    private Integer operation; //操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1


    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="resume", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdResume rdResume ;//招聘简历表

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice_post", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNoticePost rdNoticePost ;//招聘岗位表


    public RdInterviewNotification() {
        this.createDate=new Date();
        this.updateDate=createDate;
    }

    public RdInterviewNotification(Integer org,Date planTime,Integer noticeMethod,String memo,Integer creator, String createName) {
         this();
         this.org=org;
         this.noticeTime=planTime;
         this.noticeMethod=noticeMethod;
         this.memo=memo;
         this.creator=creator;
         this.createName=createName;

    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }


    public Integer getNoticeCount() {
        return noticeCount;
    }

    public void setNoticeCount(Integer noticeCount) {
        this.noticeCount = noticeCount;
    }

    public Integer getNoticeMethod() {
        return noticeMethod;
    }

    public void setNoticeMethod(Integer noticeMethod) {
        this.noticeMethod = noticeMethod;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public RdResume getRdResume() {
        return rdResume;
    }

    public void setRdResume(RdResume rdResume) {
        this.rdResume = rdResume;
    }

    public Long getNoticePostId() {
        return noticePostId;
    }

    public void setNoticePostId(Long noticePostId) {
        this.noticePostId = noticePostId;
    }

    public Long getResumeId() {
        return resumeId;
    }

    public void setResumeId(Long resumeId) {
        this.resumeId = resumeId;
    }

    public RdNoticePost getRdNoticePost() {
        return rdNoticePost;
    }

    public void setRdNoticePost(RdNoticePost rdNoticePost) {
        this.rdNoticePost = rdNoticePost;
    }

    public Date getNoticeTime() {
        return noticeTime;
    }

    public void setNoticeTime(Date noticeTime) {
        this.noticeTime = noticeTime;
    }

    public String getModifyRecord() {
        return modifyRecord;
    }

    public void setModifyRecord(String modifyRecord) {
        this.modifyRecord = modifyRecord;
    }
}
