<!-- 模板管理/模板修改 详情页 -->
<template>
  <div id="menuDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container">
      <div class="panel">
        <h4 class="menuName">{{paramData.name}}</h4>
      </div>
      <div class="panel">
        <div class="ui-alert">
          组成本套餐的模块
          <div class="btn-group">
            创建 <span class="create">{{paramData.createName }} {{paramData.createDate | formatDay}}</span>
          </div>
        </div>
        <table class="ui-table menuTable">
          <thead>
          <tr>
            <td>模块名称</td>
            <td>下辖一级菜单数量</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in moduleList" v-bind:key="index">
            <td class="name">{{item.name}}</td>
            <td>{{item.topMenu || '--'}}个</td>
            <td>
              <span class="link-blue" @click="seeModuleDetail(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<style>
.btn-group{
  text-align: right;
  flex: auto;
}
</style>
<script>
export default {
  name: 'menuDetail',
  data () {
    return {
      loading: true,
      title: '套餐查看',
      moduleList: [],
      paramData: {
        name: '',
        createName: '',
        createDate: ''
      }
    }
  },
  created: function () {
    let params =  JSON.parse(localStorage.getItem('menuInfo'))
    console.log("params", params)
    let menuId = params.id
    this.paramData = params
    this.getMenuDetail(menuId)
  },
  methods: {
    getMenuDetail: function (menuId) {
      let that = this
      this.axios.post('../../../thali/getModulesByMpSetId.do', { id: menuId })
        .then(res => {
          that.loading = false
          let data = res.data.data
          if (data && data.length > 0) {
            that.loading = false
            that.moduleList = data
          }
        })
    },
    seeModuleDetail: function (item) {
      this.$router.push({
        path: `/moduleDetail`
      })
      localStorage.setItem('moduleInfo', JSON.stringify(item))
    }
  }
}
</script>
