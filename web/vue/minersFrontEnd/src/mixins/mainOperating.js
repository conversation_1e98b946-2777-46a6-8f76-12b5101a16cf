//

import { getRootPathFile, refreshOperatortime, logout, checkPassword } from '@/api/api'
import auth from '@/sys/auth'
import JSONBig from 'json-bigint'
import sphdSocket from '@/sys/sphd'
import {clearStorage} from "@/utils/clearStorage";


export default {
    name:"operaMixin",
    components:{},
    data(){
        return {
            operaTip:'',
            operaPropShow:false,
            opera: { 'user':'', 'passwd':'' },
            inputErrorCount: 0,
            operationed: 1, //新开页面，刷新页面也算有操作
            unlocked: 1 ,
            lockedJumpUrl: null ,
            operationedtime: new Date().getTime(),
            pastTipMsg: '屏幕已锁定，请输入密码解锁',
            rootPath:{ fileUrl :'',  ow365url :'',  uploadUrl :'',  webRoot :'' },
            intervals:[]//全局定时任务
        }
    },
    created(){},
    mounted(){
        this.opera = { 'user': auth.getAcc().mobile, 'passwd':'' }
        //绑定auth token超时注销
        auth.logoutFun = this.tokenErrorLogout
        let that = this

        document.addEventListener('mousewheel', ()=>{
            that.mainOperating()
        });
        document.addEventListener('mousedown', ()=> {
            that.mainOperating()
        });
        document.addEventListener('mousemove', ()=> {
            that.mainOperating()
        });
        document.addEventListener('mouseup', ()=> {
            that.mainOperating()
        });
        document.addEventListener('keydown', ()=> {
            that.mainOperating()
        });
        document.addEventListener('keyup', ()=> {
            that.mainOperating()
        });

        localStorage.setItem('lastAjaxtime', that.operationedtime);
        that.getRootPath()

        console.log('localStorage.getItem(\'lockScreen\')', localStorage.getItem('lockScreen'))
        switch(localStorage.getItem('lockScreen')) {//wyu：修改密码提示下刷新页面，直接弹窗。
            case 'password_changed':
                that.pastTipMsg = '用户密码已修改，请输入新密码解锁'
            //wyu:此处不能加break
            // break;
            case 'lock':
                that.operaPropShowFun(that.pastTipMsg)
                that.unlocked = 0;
                break;
            default:
                that.sendOperatortime(true);
        }

        // //wyu：刷新服务器上的无操作时间
        this.intervals.push(setInterval(that.sendOperatortime,15000))
        // //wyu：订阅锁屏事件
        console.log('订阅锁屏事件')
        sphdSocket.subscribeV2(sphdSocket.level.acc, 'lockScreenCheck',that.lockscreen)
        sphdSocket.subscribeV2(sphdSocket.level.acc, 'lockPasswordchange',that.lockscreen,null,'custom', auth.getAcc().mobile)



    },
    computed(){},
    methods:{
        operaPropShowFun(tipStr){
            this.operaPropShow = true
            this.operaTip = tipStr
            this.opera.passwd = ''
        },
        operaPropCancel(){
            this.logout()
        },
        operaPropOk(){
            let passwd = (this.opera.passwd ?? '').trim()
            if(passwd.length == 0) {
                this.$message.error('密码不能为空!')
            } else {
                let data = {
                    "password": passwd
                }
                checkPassword(data).then(res => {
                    console.log('checkPassword res=', res)
                    switch (res.data) {
                        case true:
                            this.sendOperatortime(true);
                            this.inputErrorCount = 0;
                            this.operationed = 1;//wyu：验证密码成功，将operationed设为1，中断无操作时间，避免连续弹窗。
                            this.unlocked = 1;
                            this.operaPropShow = false
                            localStorage.removeItem('lockScreen')
                            break;
                        case false:
                            this.$message.error('密码错误！')
                            this.opera.passwd = ''
                            if (++that.inputErrorCount >= 3) {
                                this.logout()
                            }
                            break;
                        default://return null
                            console.log(res.data)
                            this.$message.error('已注销！')
                            this.logout()
                    }
                }).catch(err => {
                    console.log('checkPassword err=', err)
                    this.$message.error('超时，校验失败！')
                    this.logout()
                })
            }
        },

        setLockedJumpRoot() {
            this.lockedJumpUrl = auth.webRoot;
        },
        clearLockedJumpRoot() {
            this.lockedJumpUrl = null;
        },
        getRootPath(){
            getRootPathFile().then(res => {
                console.log('RootPath=', res.data)
                this.rootPath = res.data
                localStorage.setItem('rootPath', JSON.stringify( res.data))
            }).catch(err => {
                console.log('err', err)
            })
        },
        mainOperating() {
            this.operationed = 1;
            this.operationedtime = new Date().getTime();
            // console.log("operationedtime", operationedtime, localStorage.getItem('lastOperationedtime'))
            this.closeMsgTipTimer();
        },
        lockscreen(data){
            console.log('锁屏返回：'+data);
            if (data=='logout') {//wyu：30分钟超时，已经被注销
                console.log('lockscreen logout checkpoint1')
                this.logout()
                console.log('lockscreen logout checkpoint2')
            }
            if(data=='success') { //修改密码的回调
                localStorage.setItem('lockScreen', 'password_changed')
                this.pastTipMsg = '用户密码已修改，请输入新密码解锁'
            }
            // console.log(Array('success','lock').indexOf(data)>=0, unlocked, !operationed);
            if (Array('success','lock').indexOf(data)>=0 && this.unlocked && !this.operationed) {
                //wyu:条件：需要弹窗、未弹窗、上次发送操作至当前无操作，操作：弹窗
                this.unlocked = 0;
                if (data == 'success') {
                    this.pastTipMsg = '用户密码已修改，请输入新密码解锁'
                } else if(!this.operationed) {
                    localStorage.setItem('lockScreen', 'lock')
                    this.pastTipMsg = '屏幕已锁定，请输入密码解锁'
                }
                if(this.lockedJumpUrl===null) {
                    this.operaPropShowFun(this.pastTipMsg)
                } else {
                    location.href = lockedJumpUrl;
                    this.lockedJumpUrl = null;
                }
            }
        },
        sendOperatortime(send=false){
            let that = this
            // console.log('sendOperatortime:', auth.getSessionid());
            let url = send? 'lockScreenCheck' : 'refreshOperatortime';
            // console.log("localStorage.getItem('lastOperationedtime')", new Date(Number(localStorage.getItem('lastOperationedtime'))), Number(localStorage.getItem('lastOperationedtime')), new Date().getTime()>Number(localStorage.getItem('lastOperationedtime'))+15000)
            if((send || new Date().getTime()>Number(localStorage.getItem('lastOperationedtime'))+15000 && that.operationed) && that.unlocked){
                //距上一次发送的操作时间超过15秒，两次发送间隙操作时间有更新，而且目前没锁屏
                auth.getHostTime('/favicon.ico', hosttime => {//获取服务器当前时间
                    console.log('hosttime1:', new Date(hosttime));
                    let op = that.operationed && that.unlocked;//!$("#reLogin").is(":visible");//wyu：弹窗状态 operationed=false;
                    localStorage.setItem('lastOperationedtime', that.operationedtime);
                    localStorage.setItem('lastOperationedtimeLocal', new Date(that.operationedtime));
                    that.operationed = 0;
                    let operationedtime = hosttime.getTime() + that.operationedtime - new Date().getTime()
                    localStorage.setItem('lastOperationedtimeRemote', new Date(operationedtime));
                    if(send || !sphdSocket.isConnect || hosttime.getTime()<Number(localStorage.getItem('lastAjaxtime'))+1500000) {
                        console.log('hosttime2:', new Date(operationedtime));
                        sphdSocket.send(url,  {'operatortime':operationedtime,'operationed':op});//用服务器时间矫正本地时间
                    } else {//距上一次ajax发送的操作时间超过25分钟，用ajax发送，避免session超时，否则用长连接发送。
                        console.log('hosttime3:', new Date(operationedtime));
                        localStorage.setItem("lastAjaxtime", hosttime.getTime());
                        let dataOper = {
                            data: JSONBig.stringify({
                                'operatortime': operationedtime,
                                'operationed': op,
                                'sessionid': auth.getSessionid()
                            })
                        }
                        refreshOperatortime(dataOper)
                    }
                });
            }
        },
        closeMsgTipTimer(msgNumTipTimer) {
            if (msgNumTipTimer) {
                clearInterval(msgNumTipTimer);
                window.document.title = "通用框架";
                msgNumTipTimer = 0;
            }
        },
        logout (){
            logout().then(res => {
                console.log('logOut res=', res)
                let status = res.status
                if(status === 200){
                    this._ToStart()
                }else{
                    this.$message.error('退出失败！')
                }
            }).catch(err => {
                console.log('logOut err=', err)
                this.$message.error('退出失败！')
            })
        },
        _ClearLocal() {
            clearStorage()
            //清空全局定时任务
            for(let interval of this.intervals) {
                clearInterval(interval)
            }
            this.intervals.length = 0
            //清空全局订阅
            sphdSocket.unsubscribeAll(sphdSocket.level.acc)
        },
        tokenErrorLogout(msg) {
            this.$message.error(msg)
            setTimeout(()=>{
                this._ToStart()
            },3000)
        },
        _ToStart() {
            this._ClearLocal()
            sphdSocket.reflushAuth()
            this.$router.replace('Start')
            location.reload(true)
        }
    },
}