package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.modules.personal.entity.PersonnelLeaveItem;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2021/4/29
 * 请假类型表
 */
@Entity
@Table(name = "t_personnel_leave_type")
public class PersonnelLeaveType implements Serializable{
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;

    @Column(name="code"  , length=20 , nullable=true , unique=false)
    private String code;  //代码

    @Column(name="name"   , length=100 , nullable=true , unique=false)
    private String name;  //名称

    @Column(name="enabled"   , nullable=true , unique=false)
    private Boolean enabled=true;   //是否启用 默认true

    @Column(name="enabled_time"   , nullable=true , unique=false)
    private Date enabledTime;   //启停用时间

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;//排序

    @Column(name="is_system"  , length=1 , nullable=true , unique=false)
    private Boolean isSystem=false; //是否系统自带

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作:1-增,2-删,3-改

    @Column(name="previous_id"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer previousId;   //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer versionNo;   //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
