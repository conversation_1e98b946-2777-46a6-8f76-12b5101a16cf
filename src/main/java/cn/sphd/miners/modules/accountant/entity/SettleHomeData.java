package cn.sphd.miners.modules.accountant.entity;

import java.io.Serializable;

/*
* 结账管理首页数据
* */
public class SettleHomeData implements Serializable {

    private static final long serialVersionUID =  1L;

    private String settleMonth;//结账月份

    private Integer monthVoucher;//某月所有凭证

    private Integer manualSelection;//手动选择的凭证数量

    private Integer osSelection;//系统选择的凭证数量

    private Integer accountantSelection;//会计选择的凭证数量

    public String getSettleMonth() {
        return settleMonth;
    }

    public void setSettleMonth(String settleMonth) {
        this.settleMonth = settleMonth;
    }

    public Integer getMonthVoucher() {
        return monthVoucher;
    }

    public void setMonthVoucher(Integer monthVoucher) {
        this.monthVoucher = monthVoucher;
    }

    public Integer getManualSelection() {
        return manualSelection;
    }

    public void setManualSelection(Integer manualSelection) {
        this.manualSelection = manualSelection;
    }

    public Integer getOsSelection() {
        return osSelection;
    }

    public void setOsSelection(Integer osSelection) {
        this.osSelection = osSelection;
    }

    public Integer getAccountantSelection() {
        return accountantSelection;
    }

    public void setAccountantSelection(Integer accountantSelection) {
        this.accountantSelection = accountantSelection;
    }
}
