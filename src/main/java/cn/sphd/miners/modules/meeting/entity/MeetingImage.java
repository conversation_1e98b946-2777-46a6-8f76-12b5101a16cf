package cn.sphd.miners.modules.meeting.entity;

import cn.sphd.miners.modules.recruit.entity.RdNoticePost;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

//会议随手拍
@Entity
@Table(name = "t_meeting_image")
public class MeetingImage {


    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name="org"  , nullable=true , unique=false)
    private Integer org;//机构ID

    @Column(name="meeting"  ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long meetingId;//会议ID

    @Column(name="type"  , nullable=true , unique=false)
    private Integer type;//类型:1-随手拍,2-现场

    @Column(name="original_image"  , nullable=true , unique=false)
    private Long originalImage;//源随手拍ID

    @Column(name="upload_path"  ,length = 255, nullable=true , unique=false)
    private String uploadPath;//文件上传路径

    @Column(name="orders"  , nullable=true , unique=false)
    private Integer orders=1;//'排序'

    @Column(name="memo"  ,length = 255, nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"  ,nullable=true , unique=false)
    private Integer creator;   //创建人/发起人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  ,nullable=true , unique=false)
    private Integer operation;   //操作:1-增,2-删,3-更改会议信息,4-更改参会者,5-更改问题

    @Column(name="previous_id"  ,nullable=true , unique=false)
    private Integer previousId;   //修改前记录ID

    @Column(name="version_no"  ,nullable=true , unique=false)
    private Integer versionNo;   //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="meeting", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MeetingInfo meetingInfo ;//



    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getOriginalImage() {
        return originalImage;
    }

    public void setOriginalImage(Long originalImage) {
        this.originalImage = originalImage;
    }

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public MeetingInfo getMeetingInfo() {
        return meetingInfo;
    }

    public void setMeetingInfo(MeetingInfo meetingInfo) {
        this.meetingInfo = meetingInfo;
    }
}
