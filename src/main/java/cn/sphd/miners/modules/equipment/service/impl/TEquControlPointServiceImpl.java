package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;
import java.util.Map;

import cn.sphd.miners.modules.equipment.entity.TEquControlPoint;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.mapper.TEquControlPointMapper;
import cn.sphd.miners.modules.equipment.mapper.TEquModelMapper;
import cn.sphd.miners.modules.equipment.service.ITEquControlPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_控制点
 * 20240407 1.292机器设备之初始设置 新增Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@Service
@Transactional
public class TEquControlPointServiceImpl implements ITEquControlPointService {
    @Autowired
    private TEquControlPointMapper tEquControlPointMapper;

    @Autowired
    TEquModelMapper modelMapper;
    /**
     * 查询装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param id 装备管理_控制点
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */
    @Override
    public TEquControlPoint selectTEquControlPointById(Long id) {
        return tEquControlPointMapper.selectTEquControlPointById(id);
    }

    /**
     * 查询装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增列表
     *
     * @param tEquControlPoint 装备管理_控制点
     *                         20240407 1.292机器设备之初始设置 新增
     * @return 装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */
    @Override
    public List<TEquControlPoint> selectTEquControlPointList(TEquControlPoint tEquControlPoint) {
        return tEquControlPointMapper.selectTEquControlPointList(tEquControlPoint);
    }

    /**
     * 新增装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlPoint 装备管理_控制点
     *                         20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    @Override
    public int insertTEquControlPoint(TEquControlPoint tEquControlPoint) {
        return tEquControlPointMapper.insertTEquControlPoint(tEquControlPoint);
    }

    /**
     * 修改装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlPoint 装备管理_控制点
     *                         20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    @Override
    public int updateTEquControlPoint(TEquControlPoint tEquControlPoint) {
        return tEquControlPointMapper.updateTEquControlPoint(tEquControlPoint);
    }

    /**
     * 批量删除装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param ids 需要删除的装备管理_控制点
     *            20240407 1.292机器设备之初始设置 新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquControlPointByIds(String ids) {
        //删除后减少控制点数量
        for (String id : ids.split(",")){
            TEquControlPoint tEquControlPoint = tEquControlPointMapper.selectTEquControlPointById(Long.valueOf(id));
            if (tEquControlPoint != null) {
                TEquModel model = modelMapper.selectTEquModelById(tEquControlPoint.getModel());
                if (model != null) {
                    model.setPointCount(model.getPointCount() - 1);
                    if(model.getPointState() >=1){
                        model.setPointState(model.getPointState()-1);
                    }
                    modelMapper.updateTEquModel(model);
                }

            }

        }


        return tEquControlPointMapper.deleteTEquControlPointByIds(ids.split(","));
    }

    /**
     * 删除装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增信息
     *
     * @param id 装备管理_控制点
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquControlPointById(Long id) {
        return tEquControlPointMapper.deleteTEquControlPointById(id);
    }

    @Override
    public List<Map<String, Object>> selectTEquControlPointListWithPics(TEquControlPoint tEquControlPoint) {
        return tEquControlPointMapper.selectTEquControlPointListWithPics(tEquControlPoint);
    }

    @Override
    public int enable(String ids) {
        //批量启用
        if (ids.contains(",")) {
            String[] idArr = ids.split(",");
            for (String id : idArr) {
                TEquControlPoint tEquControlPoint = tEquControlPointMapper.selectTEquControlPointById(Long.valueOf(id));
                tEquControlPoint.setEnabled(1L);
                tEquControlPointMapper.updateTEquControlPoint(tEquControlPoint);
            }
            return 1;
        }
        return 0;
    }
}
