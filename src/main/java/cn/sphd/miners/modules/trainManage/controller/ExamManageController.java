package cn.sphd.miners.modules.trainManage.controller;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.entity.ReqUserObject;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.trainManage.dto.*;
import cn.sphd.miners.modules.trainManage.entity.TrainingExam;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBank;
import cn.sphd.miners.modules.trainManage.service.ExamService;
import cn.sphd.miners.modules.trainManage.service.TrainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ExamManageController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/20 9:24
 * @Version 1.0
 */
@Controller
@RequestMapping("/examManage")
public class ExamManageController {
    @Autowired
    ExamService examService;
    @Autowired
    TrainService trainService;

    //考核首页接口   //被终止也使用此接口
    //type 排序规则 1考试截至时间降序排序，2考试截至时间升序排序，3创建时间降序排序，4创建时间升序排序，5题库数量降序排序，6题库数量升序排序，7终止时间降序排序，8终止时间升序排序
    //status 1启用状态的，0被终止
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    // endTime截至时间 createDate 创建时间 bankNum 题库数量   enabledTime终止时间  choiceNum+tfNum试题数
    @ResponseBody
    @RequestMapping("/selectExamList.do")
    public RespExamListPage selectExamList(User user, Integer type, PageInfo pageInfo, Integer status) throws IOException {
        examService.exceptionsExam();
        RespExamListPage respExamListPage =new RespExamListPage();
        if(type==null)
            type=3;
        if(status==null)
            status=1;
        List<TrainingExam> list=examService.selectExamList(user,type,pageInfo,status);
        respExamListPage.setTrainingExamList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respExamListPage.setPageInfo(pageInfo);
        return respExamListPage;
    }
    //考核对象可选择列表
    @ResponseBody
    @RequestMapping("/selectExamOptionalPersonnelList.do")
    public ReqUserObject selectExamOptionalPersonnelList(User user) throws IOException {
        ReqUserObject reqUserObject=new ReqUserObject();
        Integer org = user.getOid();
        List<User> list=examService.selectStaffByOrgList(org);
        reqUserObject.setUserList(list);
        return reqUserObject;
    }
    //可选题库列表
    @ResponseBody
    @RequestMapping("/selectOptionalQuestionBankList.do")
    public RespTrainListPage selectOptionalQuestionBankList(User user) throws IOException {
        RespTrainListPage respTrainListPage =new RespTrainListPage();
        List<TrainingQuestionBank> list=trainService.selectTrainingQuestionBankList(user,3,null,1);
        respTrainListPage.setTrainingQuestionBankList(list);
        return respTrainListPage;
    }
    //题库内可选择题数
    //题库id
    @ResponseBody
    @RequestMapping("/selectOptionalQuestionCount.do")
    public RespCountQuestion selectOptionalQuestionCount(Integer id) throws IOException {
        RespCountQuestion respCountQuestion=examService.selectOptionalQuestionCount(id);
        return respCountQuestion;
    }
    //试题生成接口
    // userList[{user:用户id}] 考核人员列表
    //examBank[{bank:题库id,choiceNum选择题数量，tfNum判断题数量}]考核题库列表
    //questionScore[{questionType:试题类型:1-选择题,2-判断题,scoreType:分值类型:1-普通,2-特殊,score:分数，questionNum:试题数}]
    //goal 考核目标
    //publicity 公示
    //endTime截至时间
    //answerDuration考试分钟数
    //passingScore及格分数
    //返回值 1成功，0失败 -1可选题数不足，请重新生成题库

    //1.196修改(publicity 需要根据设置情况修改传值)
    //新增字段publicityRanking(公示名次,int型 不公示为0,公示前几,传几)
    @ResponseBody
    @RequestMapping("/addTrainingExam.do")
    public RespStatus addTrainingExam(User user, ReqTrainingExam reqTrainingExam) throws IOException {
        TrainingExam trainingExam=new TrainingExam();
        trainingExam.setGoal(reqTrainingExam.getGoal());
        Date endTime=NewDateUtils.dateFromString( reqTrainingExam.getEndTime(),"yyyy-MM-dd HH:mm:ss");
        Integer endYear=NewDateUtils.getYear(endTime);
        Integer endMonth=NewDateUtils.getMonth(endTime);
        trainingExam.setPeroid(endYear*100+endMonth);
        trainingExam.setEndTime(endTime);
        trainingExam.setAnswerDuration(Integer.valueOf(reqTrainingExam.getAnswerDuration()));
        trainingExam.setPublicity(reqTrainingExam.getPublicity());
        trainingExam.setPassingScore(Integer.valueOf(reqTrainingExam.getPassingScore()));
        trainingExam.setPublicityRanking(reqTrainingExam.getPublicityRanking());
        reqTrainingExam.setTrainingExam(trainingExam);
        RespStatus respStatus=new RespStatus();
        int status=examService.addTrainingExam(user,reqTrainingExam);
        respStatus.setStatus(status);
        return respStatus;
    }
    //考核查看  查看考试排名也是这个接口
    //id 考核id
    @ResponseBody
    @RequestMapping("/selectExamDetail.do")
    public RespExamDetail selectExamDetail(int id) throws IOException {
        RespExamDetail respExamDetail =new RespExamDetail();
        TrainingExam trainingExam=examService.selectExamById(id);
        respExamDetail.setTrainingExam(trainingExam);
        List<RespExamBank> trainingExamBankList=examService.selectExamBankListByExam(id);
        respExamDetail.setRespExamBankList(trainingExamBankList);
        List<RespExamUser> respExamUserList=examService.selectExamUserListByExam(id);;
        respExamDetail.setRespExamUserList(respExamUserList);
        List<RespExamQuestion> respExamQuestionList=examService.selectExamQuestionListByExam(id);
        respExamDetail.setRespExamQuestionList(respExamQuestionList);
        return respExamDetail;
    }
    //终止本次考核判断
    //id 考核id
    //返回值1，可以终止，0不可以终止，-1已有员工开始答题无法终止
    @ResponseBody
    @RequestMapping("/stopExamJudge.do")
    public RespStatus stopExamJudge(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=examService.stopExamJudge(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //终止本次考核
    //id 考核id
    //返回值1，终止成功，0终止失败，-1已有员工开始答题无法终止
    @ResponseBody
    @RequestMapping("/stopExam.do")
    public RespStatus stopExam(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=examService.stopExam(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }

    //已完成考核接口
    //type 排序规则 1考试截至时间降序排序，2考试截至时间升序排序
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    // endTime截至时间 createDate 创建时间 bankNum 题库数量   enabledTime终止时间  choiceNum+tfNum试题数

    //1.196新增传值,peroid(年月组合202201)
    @ResponseBody
    @RequestMapping("/selectFinishExamList.do")
    public RespExamListPage selectFinishExamList(User user, Integer type, PageInfo pageInfo,Integer peroid) throws IOException {
        RespExamListPage respExamListPage =new RespExamListPage();
        examService.initializationPeroid();
        if(type==null)
            type=1;
        if(peroid==null)
        {
            Integer endYear=NewDateUtils.getYear(new Date());
            Integer endMonth=NewDateUtils.getMonth(new Date());
            peroid=endYear*100+endMonth;
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            respExamListPage.setStartTime(calendar.getTime());//本月第一天
            respExamListPage.setEndTime(NewDateUtils.getLastTimeOfMonth(new Date()));
        }else{
            int year=peroid/100;
            int month=peroid%100;
            String date;
            if(month<10)
                date=year+"-0"+month+"-01";
            else{
                date=year+"-"+month+"-01";
            }
            Date startTime=NewDateUtils.dateFromString(date,"yyyy-MM-dd");
            respExamListPage.setStartTime(startTime);
            respExamListPage.setEndTime(NewDateUtils.getLastTimeOfMonth(startTime));
        }
        List<TrainingExam> list=examService.selectFinishExamList(user,type,pageInfo,peroid);
        respExamListPage.setTrainingExamList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respExamListPage.setPageInfo(pageInfo);
        return respExamListPage;
    }
    //查看补考
    //id 考核id
    //返回值，status为0 本次考核尚且没有发起过补考
    @ResponseBody
    @RequestMapping("/selectExamAgain.do")
    public RespExamDetail selectExamAgain(int id) throws IOException {
        RespExamDetail respExamDetail =new RespExamDetail();
        int status= examService.whetherExamAgainByExam(id);
        respExamDetail.setStatus(status);
        if(status==1) {
            List<RespExamUser> respExamUserList = examService.selectExamAgainUserListByExam(id);
            respExamDetail.setRespExamUserList(respExamUserList);
        }
        return respExamDetail;
    }
    //补考详情
    //id 考核id
    //user userId
    @ResponseBody
    @RequestMapping("/selectExamAgainDetail.do")
    public RespExamAgain selectExamAgainDetail(Integer id,Integer user) throws IOException {
        RespExamAgain respExamAgain =examService.selectExamAgainDetail(id,user);;
        return respExamAgain;
    }
    //发起补考前显示
    //id 考核id
    //返回值 1可以发起补考，-1全部人员都通过考试，无需发起补考，-2当前正有补考正在进行。无法发起补考
    @ResponseBody
    @RequestMapping("/selectExamFailDetail.do")
    public RespExamDetail selectExamFailDetail(int id) throws IOException {
        int status=1;
        RespExamDetail respExamDetail =new RespExamDetail();
        List<RespExamUser> respExamUserList=examService.selectExamFailUserListByExam(id);
        int countExamAgain=examService.selectExamAgainCountByExam(id);
        if(respExamUserList.isEmpty())
        {
            status=-1;
        }else if(countExamAgain>0){
            status=-2;
        }
        else {
            respExamDetail.setRespExamUserList(respExamUserList);
            TrainingExam trainingExam = examService.selectExamById(id);
            respExamDetail.setTrainingExam(trainingExam);
            List<RespExamBank> trainingExamBankList = examService.selectExamBankListByExam(id);
            respExamDetail.setRespExamBankList(trainingExamBankList);
            List<RespExamQuestion> respExamQuestionList = examService.selectExamQuestionListByExam(id);
            respExamDetail.setRespExamQuestionList(respExamQuestionList);
        }
        respExamDetail.setStatus(status);
        return respExamDetail;
    }
    //发起补考
    //id 考核id
    //endTime截至时间
    //返回值 1成功，0失败
    @ResponseBody
    @RequestMapping("/addTrainingExamAgain.do")
    public RespStatus addTrainingExamAgain(User user, Integer id,String endTime) throws IOException {
        RespStatus respStatus=new RespStatus();
        Date end=NewDateUtils.dateFromString(endTime,"yyyy-MM-dd HH:mm:ss");
        int status=examService.addTrainingExamAgain(user,id,end);
        respStatus.setStatus(status);
        return respStatus;
    }
    //员工考核列表
    @ResponseBody
    @RequestMapping("/selectExamForUserList.do")
    public RespExamListForUser addTrainingExamAgain(User user) throws IOException {
        RespExamListForUser  respExamListForUser=examService.selectExamForUserList(user);
        return respExamListForUser;
    }
    //开始答题
    //exam 考核id
    @ResponseBody
    @RequestMapping("/startExam.do")
    public RespExamInfoForUser startExam(User user,Integer exam) throws IOException {
        RespExamInfoForUser  respExamInfoForUser=examService.startExam(user,exam);
        return respExamInfoForUser;
    }
    //提交单个题答案
    //传值examQuestion 问题id examKey 答案id  memo答案内容（选择题为选项，判断题1为对，0为错）
    @ResponseBody
    @RequestMapping("/addExamQuestionAnswer.do")
    public RespExamInfoForUser addExamQuestionAnswer(User user,Integer examQuestion,String memo,String examKey,Integer answerId) throws IOException {
        RespExamInfoForUser espExamInfoForUser=new RespExamInfoForUser();
        if(answerId==null||"".equals(answerId)){
            answerId=examService.selectAnswerQuestion(user,examQuestion);
        }
        if(answerId==null||"".equals(answerId))
            espExamInfoForUser=examService.addExamQuestionAnswer(user,examQuestion,memo,examKey);
        else
            espExamInfoForUser=examService.updateExamQuestionAnswer(user,examQuestion,memo,examKey,answerId);
        return espExamInfoForUser;
    }
    //交卷
    //exam 考核id
    @ResponseBody
    @RequestMapping("/endExam.do")
    public RespStatus endExam(User user,Integer exam) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=examService.endExam(user,exam);
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改公示信息
    // id 考核id
    // publicity 公示内容
    // publicityRanking 公示名次
    @ResponseBody
    @RequestMapping("/updatePublicity.do")
    public RespStatus updatePublicity(User user,Integer id,String publicity,Integer publicityRanking) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=examService.updatePublicity(user,id,publicity,publicityRanking);
        respStatus.setStatus(status);
        return respStatus;
    }
    //按月按员工统计
    //peroid(年月组合202201)
    @ResponseBody
    @RequestMapping("/selectExamUserInfoList.do")
    public RespExamUserInfoList selectExamUserInfoList(Integer peroid,User user) throws IOException {
        examService.initializationUserPeroid();
        if(peroid==null) {
            Integer endYear = NewDateUtils.getYear(new Date());
            Integer endMonth = NewDateUtils.getMonth(new Date());
            peroid = endYear * 100 + endMonth;
        }
        RespExamUserInfoList respExamUserInfoList=examService.selectExamUserInfoList(user.getOid(),peroid);
        if(peroid==null)
        {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            respExamUserInfoList.setStartTime(calendar.getTime());//本月第一天
            respExamUserInfoList.setEndTime(NewDateUtils.getLastTimeOfMonth(new Date()));
        }else{
            int year=peroid/100;
            int month=peroid%100;
            String date;
            if(month<10)
                date=year+"-0"+month+"-01";
            else{
                date=year+"-"+month+"-01";
            }
            Date startTime=NewDateUtils.dateFromString(date,"yyyy-MM-dd");
            respExamUserInfoList.setStartTime(startTime);
            respExamUserInfoList.setEndTime(NewDateUtils.getLastTimeOfMonth(startTime));
        }
        return respExamUserInfoList;
    }
    //按月按员工详情
    //peroid(年月组合202201) userId
    @ResponseBody
    @RequestMapping("/selectExamUserInfo.do")
    public RespExamUserInfo selectExamUserInfo(Integer peroid,User user,Integer userId) throws IOException {
        if(peroid==null) {
            Integer endYear = NewDateUtils.getYear(new Date());
            Integer endMonth = NewDateUtils.getMonth(new Date());
            peroid = endYear * 100 + endMonth;
        }
        if(userId==null)
        {
            userId=user.getUserID();
        }
        RespExamUserInfo respExamUserInfo=examService.selectExamUserInfo(user.getOid(),peroid,userId);
        if(peroid==null)
        {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            respExamUserInfo.setStartTime(calendar.getTime());//本月第一天
            respExamUserInfo.setEndTime(NewDateUtils.getLastTimeOfMonth(new Date()));
        }else{
            int year=peroid/100;
            int month=peroid%100;
            String date;
            if(month<10)
                date=year+"-0"+month+"-01";
            else{
                date=year+"-"+month+"-01";
            }
            Date startTime=NewDateUtils.dateFromString(date,"yyyy-MM-dd");
            respExamUserInfo.setStartTime(startTime);
            respExamUserInfo.setEndTime(NewDateUtils.getLastTimeOfMonth(startTime));
        }
        return respExamUserInfo;
    }
    //按年统计
//已完成考核接口
    //type 排序规则 1考试截至时间降序排序，2考试截至时间升序排序
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    // endTime截至时间 createDate 创建时间 bankNum 题库数量   enabledTime终止时间  choiceNum+tfNum试题数

    //1.196新增传值,year(年2022)
    @ResponseBody
    @RequestMapping("/selectFinishExamListByYear.do")
    public RespExamListPage selectFinishExamListByYear(User user, Integer type, PageInfo pageInfo,Integer year) throws IOException {
        RespExamListPage respExamListPage =new RespExamListPage();
        examService.initializationPeroid();
        if(type==null)
            type=1;
        if(year==null)
        {
            Integer endYear=NewDateUtils.getYear(new Date());
            year=endYear;
            respExamListPage.setStartTime(NewDateUtils.getNewYearsDay());
            respExamListPage.setEndTime(new Date());
        }else{
            String date=year+"-01-01 00:00:00";
            Date startTime=NewDateUtils.dateFromString(date,"yyyy-MM-dd HH:mm:ss");
            respExamListPage.setStartTime(startTime);
            respExamListPage.setEndTime(NewDateUtils.getLastTimeOfYear(startTime));
        }
        List<TrainingExam> list=examService.selectFinishExamListByYear(user,type,pageInfo,year);
        respExamListPage.setTrainingExamList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respExamListPage.setPageInfo(pageInfo);
        return respExamListPage;
    }
    //某年按月统计
    @ResponseBody
    @RequestMapping("/selectExamListByMonth.do")
    public RespPeroidExamList selectExamListByMonth(User user,Integer year) throws IOException {
        RespPeroidExamList respPeroidExamList =examService.selectExamListByMonth(user.getOid(),year);
        respPeroidExamList.setYear(year);
        return respPeroidExamList;
    }

    //练习
    @ResponseBody
    @RequestMapping("/practiceExam.do")
    public RespExamInfoForUser practiceExam(User user,Integer exam) throws IOException {
        RespExamInfoForUser  respExamInfoForUser=examService.practiceExam(user,exam);
        return respExamInfoForUser;
    }

    //按月按员工 一次通过
    //peroid(年月组合202201) userId
    @ResponseBody
    @RequestMapping("/selectExamUserOnePassInfo.do")
    public RespExamUserInfo selectExamUserOnePassInfo(Integer peroid,User user) throws IOException {
        if(peroid==null) {
            Integer endYear = NewDateUtils.getYear(new Date());
            Integer endMonth = NewDateUtils.getMonth(new Date());
            peroid = endYear * 100 + endMonth;
        }
        RespExamUserInfo respExamUserInfo=examService.selectExamUserOnePassInfo(user.getOid(),peroid,user.getUserID());
        if(peroid==null)
        {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            respExamUserInfo.setStartTime(calendar.getTime());//本月第一天
            respExamUserInfo.setEndTime(NewDateUtils.getLastTimeOfMonth(new Date()));
        }else{
            int year=peroid/100;
            int month=peroid%100;
            String date;
            if(month<10)
                date=year+"-0"+month+"-01";
            else{
                date=year+"-"+month+"-01";
            }
            Date startTime=NewDateUtils.dateFromString(date,"yyyy-MM-dd");
            respExamUserInfo.setStartTime(startTime);
            respExamUserInfo.setEndTime(NewDateUtils.getLastTimeOfMonth(startTime));
        }
        return respExamUserInfo;
    }
    //按月按员工 补考
    //peroid(年月组合202201) userId
    @ResponseBody
    @RequestMapping("/selectExamUserAgainInfo.do")
    public RespExamUserInfo selectExamUserAgainInfo(Integer peroid,User user) throws IOException {
        if(peroid==null) {
            Integer endYear = NewDateUtils.getYear(new Date());
            Integer endMonth = NewDateUtils.getMonth(new Date());
            peroid = endYear * 100 + endMonth;
        }
        RespExamUserInfo respExamUserInfo=examService.selectExamUserAgainInfo(user.getOid(),peroid,user.getUserID());
        if(peroid==null)
        {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            respExamUserInfo.setStartTime(calendar.getTime());//本月第一天
            respExamUserInfo.setEndTime(NewDateUtils.getLastTimeOfMonth(new Date()));
        }else{
            int year=peroid/100;
            int month=peroid%100;
            String date;
            if(month<10)
                date=year+"-0"+month+"-01";
            else{
                date=year+"-"+month+"-01";
            }
            Date startTime=NewDateUtils.dateFromString(date,"yyyy-MM-dd");
            respExamUserInfo.setStartTime(startTime);
            respExamUserInfo.setEndTime(NewDateUtils.getLastTimeOfMonth(startTime));
        }
        return respExamUserInfo;
    }
    //查看考核详情
    //exam 考核id
    @ResponseBody
    @RequestMapping("/selectExamDetailByUser.do")
    public RespExamInfoForUser selectExamDetailByUser(User user,Integer exam) throws IOException {
        RespExamInfoForUser  respExamInfoForUser=examService.selectExamDetail(user,exam);
        return respExamInfoForUser;
    }
    //公示首页接口
    @ResponseBody
    @RequestMapping("/selectExamPublicity.do")
    public RespExamDetail selectExamPublicity(User user,Integer id) throws IOException {
        RespExamDetail respExamDetail =new RespExamDetail();
        if(id==null) {
           id = examService.selectLastExam(user);
        }
        if(id!=null&&id!=0)
        {
            TrainingExam trainingExam=examService.selectExamById(id);
            respExamDetail.setTrainingExam(trainingExam);
            List<RespExamBank> trainingExamBankList=examService.selectExamBankListByExam(id);
            respExamDetail.setRespExamBankList(trainingExamBankList);
            List<RespExamUser> respExamUserList=examService.selectExamUserListByExam(id);;
            respExamDetail.setRespExamUserList(respExamUserList);
        }
        return respExamDetail;
    }
    //公示详情接口
    @ResponseBody
    @RequestMapping("/selectExamDetailPublicityByUser.do")
    public RespExamInfoForUser selectExamDetailPublicityByUser(int userId,Integer exam) throws IOException {
        User user =new User();
        user.setUserID(userId);
        RespExamInfoForUser  respExamInfoForUser=examService.selectExamDetail(user,exam);
        return respExamInfoForUser;
    }
    //只看错题接口
    @ResponseBody
    @RequestMapping("/selectExamDetailErrorsByUser.do")
    public RespExamInfoForUser selectExamDetailErrorsByUser(int userId,Integer exam) throws IOException {
        User user =new User();
        user.setUserID(userId);
        RespExamInfoForUser  respExamInfoForUser=examService.selectExamDetailErrors(user,exam);
        return respExamInfoForUser;
    }
}
