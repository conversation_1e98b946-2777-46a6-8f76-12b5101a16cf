package cn.sphd.miners.modules.system.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.initializer.SaveRpcLog;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.dailyIndex.entity.DailyOrg;
import cn.sphd.miners.modules.dailyIndex.service.DailyOrgService;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.generalAffairs.service.UserImportService;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.system.dto.PopedomDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import cn.sphd.miners.modules.system.service.OrgService.OrgType;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService;
import com.alibaba.fastjson.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2017/10/20.
 */
@Controller
@RequestMapping("/org")
public class OrganizationController {

    @Autowired
    PopedomService popedomService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    AccountService accountService;
    @Autowired
    MaterielService materielService;
    @Autowired
    CodeService codeService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    RoleTmplService roleTmplService;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    SendMessageService sendMessageService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    RoleService roleService;
    @Autowired
    OrgPopedomItemService orgPopedomItemService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    RolePrincipalHistoryService rolePrincipalHistoryService;
    @Autowired
    UserImportService userImportService;
    @Autowired
    AuthService authService;
    @Autowired
    DailyOrgService dailyOrgService;
    @Autowired
    SmsService smsService;
    @Autowired
    ThirdPlatformService thirdPlatformService;


    /**
     * <AUTHOR>
     * @Date 2017/10/20 9:07
     * 保存管理平台新增机构
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/addOrg.do")
    public JsonResult addOrg(Long createDate,String midList,String fullName,String name,String userName, String phone, String operationUserMobile,String tyDomain,String uploadStorageType,String address,String registeredAddress, HttpServletRequest request,AuthInfoDto authInfo){
        JsonResult jsonResult;
        if (null != phone && 11 == phone.length()&&midList!=null) {
            List<OrgPopedom> orgPopedomList= JSON.parseArray(midList,OrgPopedom.class);
//            String str[]=midList.split(",");
//            for (String s:str){
//                OrgPopedom orgPopedom=new OrgPopedom();
//                orgPopedom.setMid(s);
//                orgPopedomList.add(orgPopedom);
//            }
            try {
                Map<String,Object> map= orgService.initialization(new Date(createDate),fullName,name,userName, phone,orgPopedomList,operationUserMobile,tyDomain,uploadStorageType,"0", OrgType.org,0,"initialBeforeSuper",authInfo,address,registeredAddress);// 1.330p地址优化  加的两地址 2025/02/17
                jsonResult=new JsonResult(1,map);
            } catch (Exception e) {
                e.printStackTrace();
                jsonResult=new JsonResult(new MyException(e.getMessage()));
            }
            return jsonResult;
//            return new JsonResult(1,organization.getId());
        }else {
            return new JsonResult(new MyException("参数错误，新增失败！"));
        }
    }

    /**
    * <AUTHOR>
    * @Date 2017/10/23 10:16
    * 启用/停用机构
     * 1-停用，0-启用
    */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/updateOrgState.do")
    public JsonResult updateOrgState(String state,Integer oid){
        if("1".equals(state)||"0".equals(state)){
            orgService.updateOrgState(state,oid);
            return new JsonResult(1,"1");//修改成功
        }else {
            return new JsonResult(new MyException("修改失败"));
        }
    }

    /**
     * <AUTHOR>
     * @Date 2017/10/13 16:28
     * 获取管理平台可勾选模块列表 （树状）
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/getPopedomList.do")
    public JsonResult getPopedomList(Integer oid){
        ArrayList<PopedomDto> list;
        ArrayList<String> mids = new ArrayList<>();
        if(oid==null||oid.equals(0)) {
            list = popedomService.getAllPopedomDtoList();
        }else{
            list = popedomService.getAllPopedomDtoListByOid(oid,mids);
        }
        HashMap<String,ArrayList> result = new HashMap<>();
        result.put("tree",list);
        result.put("mids",mids);
        return  new JsonResult(1,result);
    }

    /**
     * <AUTHOR>
     * @Date 2020/12/21 15：49
     * 取最新 菜单 返回给管理平台做 比对 （非树状）
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/synchroPopedoms.do")
    public JsonResult synchroPopedoms(){
        ArrayList<PopedomDto> list=popedomService.getAllPopedomDtos();
        HashMap<String,ArrayList> result = new HashMap<>();
        result.put("popedomDtos",list);
        return new JsonResult(1,result);
    }

    /**
    * <AUTHOR>
    * @Date 2017/10/26 14:08
    * 编辑机构可用菜单
    */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/updateOrgPopedom.do")
    public JsonResult updateOrgPopedom(Integer oid,String midList,String updateUserName){
        JsonResult jsonResult;

        if (null != oid && midList!=null) {
//            List<OrgPopedom> orgPopedomList= new ArrayList<OrgPopedom>();
//            String str[]=midList.split(",");
//            for (String s:str){
//                OrgPopedom orgPopedom=new OrgPopedom();
//                orgPopedom.setMid(s);
//                orgPopedomList.add(orgPopedom);
//            }
            List<OrgPopedom> orgPopedomList= JSON.parseArray(midList,OrgPopedom.class);
            List<String> mids=new ArrayList<>();
            for (OrgPopedom orgPopedom:orgPopedomList){
                if (!mids.contains(orgPopedom.getMid())) {
                    mids.add(orgPopedom.getMid());
                }else {
                    orgPopedomList.remove(orgPopedom);
                }
            }
            try {
                Organization org=orgService.getByOid(oid,true,false);
                orgPopedomService.deleteOrgPopedomAllByOid(org.getOrgPopedomHashSet());//删除之前机构拥有菜单

                orgPopedomService.saveOrgPopedom(orgPopedomList,oid,updateUserName);//把新菜单给机构
//                orgService.clearOrgUserPopedomCache(oid);//清除机构下属人员的权限缓存。
                List<User> userList=userService.getUserListByOrgId(oid);
                for (User user:userList){
                    userService.clearPopedomsCacheByUserId(user.getUserID()); // 清理机构下每个人员的权限缓存
                    popedomTaskService.updateUserBadgeNumber(user);//重新计算角标
                }
                orgPopedomItemService.deleteOrgPopedomItems(oid);//清掉启用管理
                jsonResult=new JsonResult(1,"1");//操作成功
            } catch (Exception e) {
                e.printStackTrace();
                jsonResult=new JsonResult(new MyException(e.getMessage()));
            }
            return jsonResult;
        }else {
            return new JsonResult(new MyException("参数错误，新增失败！"));
        }
    }

    /**
    * <AUTHOR>
    * @Date 2017/11/8 15:51
    * 管理平台编辑企业信息
    */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/updateOrgInfo.do")
    public JsonResult updateOrgInfo(String oid,String fullName,String name,String email,String contact,String officePhone,String homepage,String address){
        if (oid!=null) {
            Organization o = orgService.getByOid(Integer.parseInt(oid),true,false);
            o.setFullName(fullName);
            o.setName(name);
            o.setEmail(email);
            o.setContact(contact);
            o.setHomepage(homepage);
            o.setOfficePhone(officePhone);
            o.setAddress(address);
            orgService.updateOrg(o);

            JsonResult jsonResult = new JsonResult(1, "1");//操作成功
            return jsonResult;
        }else {
            return new JsonResult(new MyException("参数错误，修改失败！"));

        }

    }


    /**
    * <AUTHOR>
    * @Date 2018/8/9 14:44
    * 一键清空
    */
    @ResponseBody
    @RequestMapping("/deleteUserList.do")
    public  void  deleteUserList(User user,HttpServletResponse response) throws IOException {
        Integer oid= user.getOid();
        Map<String,Object> map=new HashMap<String,Object>();

        userService.deleteStaffUserListByOid(oid);
        map.put("status", 1);//编辑完成
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     * 获取当前机构下所有部门
     * -zy
     * */
    @RequestMapping("/getAllOrgByType.do")
    @ResponseBody
    public Map getAllOrgByType(Integer type,User user){
        Integer oid = user.getOid();
        Map map = orgService.getAllOrgByOidAndType(oid,type);
        ArrayList<HashMap> list = (ArrayList) map.get("data");
        if(list!=null && list.size()>0){
            for(HashMap m : list){
                Integer id = (Integer) m.get("id");
                List<User> users = userService.getAllUserByDepartment(id);
                m.put("userList",users);
            }
        }
        return map;
    }


    /**
     * <AUTHOR>
     * @Date 2018/8/10 11:44
     * 确定使用机构接口
     */
    @ResponseBody
    @RequestMapping("/confirmOrg.do")
    public Integer confirmOrg(User user,Integer sonOid, HttpServletRequest request,AuthInfoDto authInfo) throws IOException {
        if (user!=null) {
            Integer oid=user.getOid();
            if (sonOid!=null){
                oid=orgService.getOidByOrgSonOrg(oid,sonOid);
            }
            orgService.confirmOrg(user,oid,request,authInfo);//确定使用机构
            return 1;
        }
        return 0;
//        Map<String,Object> map=new HashMap<String,Object>();
//        map.put("status", 1);//成功
//        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     * <AUTHOR>
     * @Date 2020/6/15 11:44
     * 给特殊机构初始化 物料、财务、费用类别、票据种类等业务数据的方法
     */
    @ResponseBody
    @RequestMapping("/insertSpecialOrgData.do")
    public String insertSpecialOrgData() throws ParseException {
        List<ApprovalItem> approvalItemList=roleService.getApprovalItems(0,null);
        if (approvalItemList.size()==0){
            orgService.insertSpecialOrgData();
        }
        return "1";
    }


    /**
     * <AUTHOR>
     * @Date 2020/8/16 9:14
     * 1.123特殊机构登录记录
     * 给管理平台调用 查询机构 登录记录接口
     * type 1 本月 2- 本年 3自定义
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/getOrgUserLogs.do")
    public JsonResult getOrgUserLogs(Integer oid,Integer type,String beginDate,String endDate,String name,String createName,String createDate) throws ParseException {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=new Date();
        Date end=new Date();
        HashMap<String,Object> hashMap=new HashMap<>();
        switch (type){
            case 1:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin= NewDateUtils.changeMonth(new Date(),0);  //获取本月第一天
                beginDate=sdf.format(begin);
                endDate=sdf.format(end);
                break;
            case 2:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin= NewDateUtils.changeYear(new Date(),0);  //获取本年第一天
                beginDate=sdf.format(begin);
                endDate=sdf.format(end);
                break;
            case 3:
                begin=sdf.parse(beginDate);
                end=sdf.parse(endDate);
                end=NewDateUtils.getLastTimeOfDay(end);
                break;
        }
//        if (end.getTime()>new Date().getTime()){
//            end=new Date();
//        }
        List<Map<String,Object>> mapList=new ArrayList<>();

        if (begin.getYear()!=end.getYear()) { //跨年
            Date over=NewDateUtils.changeYear(end, 0);
            Long terminate=begin.getTime();
            over = over.getTime()>begin.getTime() ? over : begin;
            for (; end.getTime() >= terminate; over = NewDateUtils.changeYear(over, -1), end = NewDateUtils.getLastTimeOfYear(over)) {
                if (over.getTime() < terminate) {
                    over = begin;
                }
                Map<String, Object> result = userLogService.getUserLogsByOid(oid, over, end);
                mapList.add(result);
            }
        }else if (begin.getMonth()!=end.getMonth()) { //跨月
            Date over=NewDateUtils.changeMonth(end, 0);

            Long terminate=begin.getTime();
            over = over.getTime()>begin.getTime() ? over : begin;
            for (; end.getTime() >= terminate; over = NewDateUtils.changeMonth(over, -1), end = NewDateUtils.getLastTimeOfMonth(over)) {
                if (over.getTime() < terminate) {
                    over = begin;
                }
                Map<String, Object> result = userLogService.getUserLogsByOid(oid, over, end);
                mapList.add(result);
            }
        }else {//当月
            Date over=NewDateUtils.changeDay(end, 0);
//            Integer userNum=userService.getUserCountsByOid(oid);
//
//            over = over.getTime()>begin.getTime() ? over : begin;
//            for (; end.getTime() >= terminate; over = NewDateUtils.changeDay(over, -1), end = NewDateUtils.getLastTimeOfDay(over)) {
//                if (over.getTime() < terminate) {
//                    over = begin;
//                }
//                Map<String, Object> result = userLogService.getUserLogsByOid(oid, over, end);
//
//                    result.put("userNum",userNum);
//
//                mapList.add(result);
//            }
            mapList=userLogService.getUserLogListByOid(oid, begin, end);
        }
        Map<String,Object> map=new HashMap<>();
        map.put("statisticsList",mapList);
        map.put("name",name);
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        map.put("createName",createName);
        map.put("createDate",createDate);

        return new JsonResult(1,map);
    }

    
    /**
    * <AUTHOR>
    * @Date 2020/11/19 9:52
    *  启用前  添加 临时管理员 （1.139中枢管控2）
    */
    @ResponseBody
    @RequestMapping("/addTemporaryAdmin.do")
    public JsonResult addTemporaryAdmin(User user,String userName,String mobile,HttpServletRequest request,AuthInfoDto authInfo){
        Integer state=0;
        User repeatUser=userService.getUserByOidAndPhone(user.getOid(),mobile);
        if (mobile.length() == 11&&repeatUser==null) {
            userService.addTemporaryAdmin(user,user.getOrganization(),userName,mobile,request,authInfo);
            state=1;
        }
        return new JsonResult(1,state);
    }
    
    
    /**
    * <AUTHOR>
    * @Date 2020/11/19 10:38
    * 查看当前临时管理员接口
    */
    @ResponseBody
    @RequestMapping("/getTemporaryAdmin.do")
    public JsonResult getTemporaryAdmin(User user,Integer sonOid) throws IOException {
        Integer oid=user.getOid();
        if (sonOid!=null){
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        Map<String,Object> map=new HashMap<>();
        if (user!=null){
            user=userService.getUserByRoleCode(oid,"temporary");// 查询临时管理员
            map.put("user",user);
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2020/11/19 10:46
    * 更换临时管理员接口
    */
    @ResponseBody
    @RequestMapping("/updateTemporaryAdmin.do")
    public Integer updateTemporaryAdmin(User user,Integer userId,String userName,String mobile,HttpServletRequest request,AuthInfoDto authInfo){
        Integer state=0;
        if (user!=null&&mobile.length() == 11){
            User manageUser=userService.getUserByID(userId);// 查询临时管理员
            manageUser.setUserName(userName);
//            manageUser.setLogonPwd(userService.getLogonPwdByPhone(mobile));//获取该有的登录密码
            manageUser.setMobile(mobile);
//            user.setLogonName(mobile);
            manageUser.setUpdateDate(new Date());
            manageUser.setUpdateName(user.getUserName());
            manageUser.setUpdator(user.getUserID());

            AuthAcc authAcc= authService.newEnabledAccInOrg(manageUser.getMobile(),manageUser.getUserName(), manageUser.getOrganization());// 生成登陆账号
            manageUser.setAccId(authAcc.getId());

            userService.updateUser(manageUser);

//            String sms="我公司将使用电脑端网址为"+System.getProperty("BaseUrl")+"的管理系统。请使用你"+mobile+"手机号登录系统，之后将职工名单导入，并按页面提示操作。\n" + "有疑问可给我"+user.getMobile()+"回电话。";
//            sendMessageService.sendMessage(userId,manageUser.getOid(),mobile,sms);

            // 短信重构 2024/5/22 李旭更改
            String phoneNo=mobile;
            String url = GetLocalIPUtils.getRootPath(request);
            Map<String, String> params = new HashMap<String, String>(3) {{
                put("phoneNo", phoneNo);
                put("url", url);
                put("mobile", user.getMobile());
            }};
            smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.addTemporary, params, null);

            userImportService.deleteUserImportsByOid(user.getOid());

            state=1;
        }
        return state;
    }


    /**
    * <AUTHOR>
    * @Date 2020/11/20 9:23
    * 亲自操作删除临时管理员的接口
     * 设完临时负责人  后 选择亲自操作 删除临时管理员 和 临时管理员操作导入的数据
    */
    @ResponseBody
    @RequestMapping("/deleteTemporaryAdmin.do")
    public JsonResult deleteTemporaryAdmin(User user){
        Integer oid=user.getOid();

        User deleteUser=userService.getUserByRoleCode(oid,"temporary");// 查询临时管理员
        if (deleteUser!=null){
            userService.deleteUser(deleteUser); // 删除临时管理员
        }
        userImportService.deleteUserImportsByOid(oid);

        return new JsonResult(1,1);
    }

    /**
    * <AUTHOR>
    * @Date 2020/11/20 10:33
    * 1.139中枢管控2  高管管理 三种页面 去向区分接口
    */
    private Integer manageToWhere(Organization organization){
        Integer state=0; // 0 正式启用前，且没有设置临时管理员，  1- 正式启用前， 设置了临时管理员，但未导入完成 跳转至查看临时管理员页面，  2- 正式启用前， 导入完成 进入高管管理页面  3 启用后   4- 董事长 亲自操作导入，未导入完成，跳转到之前页面
        if (organization!=null){
            if (organization.getInitState().equals("0")) {//没正式启用
                Integer userNumbers=userService.getUserCountsByOid(organization.getId());// 获取人员数量（不含身份）
                if (userNumbers==0){ //未导入完成
                    User user=userService.getUserByRoleCode(organization.getId(),"temporary");// 查询临时管理员
                    if (user!=null){
                        state=1;
                    }else {
                        Integer importNumbers=userImportService.getImportNumberByOid(organization.getId());
                        if (importNumbers!=null){
                            state=4;
                        }
                    }
                }else { //导入完成
                    state=2;
                }
            }else {
                state=3;
            }
        }
        return state;
    }

    /**
     * <AUTHOR>
     * @Date 2020/12/7 13:33
     * 1.139中枢管控2  主页展示的机构信息
     */
    @ResponseBody
    @RequestMapping("/getControlInfo.do")
    public JsonResult getControlInfo(User user){
        User loginUser= user;
        loginUser=userService.getUserByID(loginUser.getUserID());
        Organization organization=loginUser.getOrganization();
        Map<String,Object> map=new HashMap<>();
        map.put("orgName",organization.getName()); //机构名
        map.put("today",new Date()); //今日时间
        map.put("week",NewDateUtils.getDay(new Date()));// 星期几
        map.put("superMobile",loginUser.getMobile()); //最高权限手机号
        map.put("createDate",loginUser.getCreateTime()); //成立时间
        map.put("address",organization.getAddress()); //地址
        map.put("userNumber",userService.getPresentUsers(organization.getId()).size()); //机构在职员工数量
        User smallUser=userService.getUserByRoleCode(organization.getId(),"smallSuper");
        map.put("smallUserName",smallUser!=null?smallUser.getUserName():null); //全权负责人名
        map.put("manageState",manageToWhere(organization));// 0 正式启用前，且没有设置临时管理员，  1- 正式启用前， 设置了临时管理员，但未导入完成 跳转至查看临时管理员页面，  2- 正式启用前， 导入完成 进入高管管理页面 或者 启用后

        if (organization.getOrgType().equals(1)) { // 当前机构是总机构  获取子机构列表
            List<Map<String, Object>> mapList = new ArrayList<>();
            List<Organization> sonOrgs = orgService.checkDepartmentByPId(loginUser.getOid(), 4);// 子机构列表
            for (Organization sonOrg : sonOrgs) {
                Map<String, Object> sonMap = new HashMap<>();
                sonMap.put("sonOid", sonOrg.getId());
                sonMap.put("sonName", sonOrg.getName());
                sonMap.put("sonAddress", sonOrg.getAddress());
                sonMap.put("sonUserNumber", userService.getPresentUsers(sonOrg.getId()).size()); //机构在职员工数量
                sonMap.put("sonManageState", manageToWhere(sonOrg));// 0 正式启用前，且没有设置临时管理员，  1- 正式启用前， 设置了临时管理员，但未导入完成 跳转至查看临时管理员页面，  2- 正式启用前， 导入完成 进入高管管理页面 或者 启用后
                mapList.add(sonMap);
            }
            map.put("sonOrgList", mapList); //子机构列表
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2020/12/14 9:38
     * 1.139中枢管控2  职工导入主页面
     */
    @RequestMapping("/toUserImport.do")
    public String toUserImport(){
        return "/generalAffairs/employeeImport";
    }


    /**
     * <AUTHOR>
     * @Date 2020/12/14 9:38
     * 1.139中枢管控2  职工导入 列表
     */
    @ResponseBody
    @RequestMapping("/getPresentUsers.do")
    public JsonResult getPresentUsers(User user,Integer sonOid) throws IOException {
        Integer oid=user.getOid();
        if (sonOid!=null){
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        List<User> userList=userService.getPresentUsers(oid);
        return new JsonResult(1,userList);
    }



    /**
     * <AUTHOR>
     * @Date 2022/4/24 8:20
     * 主播自行注册子机构接口 (1.210直播助手1)
     */
    @AuthPassport(manor = true,tpMember=true)
    @ResponseBody
    @RequestMapping("/registerSonOrg.do")
    public JsonResult registerSonOrg(AuthAcc acc,AuthInfoDto authInfo) throws ParseException, InvocationTargetException, IllegalAccessException {
        List<User> superUsers=userService.getSuperUsersByAccId(acc.getId()); // 该账户拥有的 超管列表
        for (User u:superUsers){
            if (u.getOrganization().getCode()!=null&&u.getOrganization().getCode().equals("liveHelper")&&0==u.getOrganization().getState()) {  // 作为超管的机构 已存在主播机构
                return new JsonResult(1, false);  // 返回新增失败
            }
        }
        List<OrgPopedom> orgPopedomList=new ArrayList<>();
        OrgPopedom orgPopedom=new OrgPopedom();
        orgPopedom.setMid("iba");
        orgPopedomList.add(orgPopedom);
        Organization organization=orgPopedomService.getOrgPopedomByMid("ib").get(0); //特殊普通机构
        orgService.initialization(new Date(),acc.getName()+"的工作室",acc.getName()+"的工作室",acc.getName(), acc.getMobile(),orgPopedomList,null,null,"NFS","1", OrgType.sonOrg,organization.getId(),"super",authInfo,null,null);
        return new JsonResult(1,true);
    }

    /**
     * <AUTHOR>
     * @Date 2022/4/25 8:15
     * 加入他人工作室接口 (1.210直播助手1)
     */
    @AuthPassport(manor = true,tpMember=true)
    @ResponseBody
    @RequestMapping("/joinTeam.do")
    public boolean joinTeam(AuthAcc acc,Integer oid,String submitState){
        Assert.notNull(acc, "未注册或登录");
        if (oid!=null){
            return userService.joinTeam(acc.getName(),acc.getMobile(),oid,submitState); // 加入成功，前端调登录接口
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Date 2022/4/28 8:30
     * 工作室成员列表接口 (1.210直播助手1)
     */
    @AuthPassport(tpMember=true)
    @ResponseBody
    @RequestMapping("/getTeamMembers.do")
    public JsonResult getTeamMembers(User user){
        List<UserHonePageDto> userList= userService.getUserUserHonePageDtoListByOid(user.getOid(),"1");
        return new JsonResult(1,userList);
    }

    /**
     * <AUTHOR>
     * @Date 2022/4/28 13:01
     * 将成员移出团队 (1.210直播助手1)
     */
    @AuthPassport(tpMember=true)
    @ResponseBody
    @RequestMapping("/memberOutTeam.do")
    public Boolean memberOutTeam(User user, Integer outUserId){
        return userService.memberOutTeam(user,outUserId);
    }

    /**
     * <AUTHOR>
     * @Date 2022/4/29 8:30
     * 工作室已移出团队的成员列表接口 (1.210直播助手1)
     */
    @AuthPassport(tpMember=true)
    @ResponseBody
    @RequestMapping("/getOutTeamMembers.do")
    public JsonResult getOutTeamMembers(User user){
        List<UserHonePageDto> userList= userService.getUserUserHonePageDtoListByOid(user.getOid(),"2");
        return new JsonResult(1,userList);
    }

    /**
     * <AUTHOR>
     * @Date 2022/4/29 13:01
     * 将成员重新移入团队 (1.210直播助手1)
     */
    @AuthPassport(tpMember=true)
    @ResponseBody
    @RequestMapping("/memberBackTeam.do")
    public Boolean memberBackTeam(User user, Integer backUserId){
        return userService.memberBackTeam(user,backUserId);
    }


    /**
     * <AUTHOR>
     * @Date 2022/5/12 13:01
     * 成员退出团队接口 (1.210直播助手1)
     */
    @AuthPassport(tpMember=true)
    @ResponseBody
    @RequestMapping("/activeMemberOutTeam.do")
    public Boolean activeMemberOutTeam(User user,HttpServletRequest request, HttpServletResponse response,String token){
        Boolean r=userService.memberOutTeam(user,user.getUserID());
        if (r) {
            authService.updateTokenRemoveUser(request, response, token);
        }
        return r;
    }


    /**
     * <AUTHOR>
     * @Date 2022/5/13 13:01
     * 注销工作室接口 (1.210直播助手1)
     */
    @AuthPassport(tpMember=true)
    @ResponseBody
    @RequestMapping("/cancellationTeam.do")
    public JsonResult cancellationTeam(User user,HttpServletRequest request, HttpServletResponse response,String token){
        orgService.closeOrg(user.getOid());//注销机构
        authService.updateTokenRemoveUser(request,response,token);
        return new JsonResult(1,1); //
    }

    /**
     * <AUTHOR>
     * @Date 2022/5/14 13:01
     * 注销账号接口 (1.210直播助手1) 同  /closeAcc.do
     */


    /**
     * <AUTHOR>
     * @Date 2024/6/24
     * 单独验证账号是否有主播机构
     */
    @AuthPassport(tpMember=true)
    @ResponseBody
    @RequestMapping("/checkLIVEOrg.do")
    public JsonResult checkLIVEOrg(String phone){
        return orgService.checkLIVEOrg(phone);
    }


    /**
     * <AUTHOR>
     * @Date 2025/5/8   1.339P菜单重命名
     * 给管理平台调用    对 普通机构机构 菜单重命名保存
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/followOrgMidRenames.do")
    public JsonResult followOrgMidRenames(Integer oid,String midRenames,String userName){
        if (StringUtils.isNotEmpty(midRenames)){  //重命名菜单处理
            orgPopedomService.updateOrgPopedom(oid,midRenames,userName);
        }
        return new JsonResult(1,"操作成功！"); //
    }


    /**
     * <AUTHOR>
     * @Date 2025/5/12   1.339P菜单重命名
     * 给管理平台调用   菜单重命名记录   按mid分组最新
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/selectOrgRenameHistories.do")
    public JsonResult selectOrgRenameHistories(Integer oid){
        List<OrgPopedomHistory> orgPopedomHistories=orgPopedomService.getOrgPopedomHistoriesByOid(oid);
        ArrayList<String> midList=new ArrayList<>();
        for (OrgPopedomHistory orgPopedomHistory:orgPopedomHistories){
            midList.add(orgPopedomHistory.getMid());
        }
        Map<String,String> midNameMap=new HashMap<>();
        ArrayList<PopedomDto> popedomDtos=popedomService.getAllPopedomDtoListByMids(midList);
        for (PopedomDto popedomDto:popedomDtos){
            midNameMap.put(popedomDto.getMid(),popedomDto.getName());
        }
        for (OrgPopedomHistory orgPopedomHistory:orgPopedomHistories){
            orgPopedomHistory.setInitialName(midNameMap.get(orgPopedomHistory.getMid()));
        }
        return new JsonResult(1,orgPopedomHistories);
    }

    /**
     * <AUTHOR>
     * @Date 2025/5/13   1.339P菜单重命名
     * 给管理平台调用   菜单重命名记录   按oid 和 mid 查历史列表
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/selectOrgRenameHistoryInfo.do")
    public JsonResult selectOrgRenameHistoryInfo(Integer oid,String mid){
        Popedom popedom=popedomService.getPopedomByMid(mid);
        OrgPopedom orgPopedom=orgPopedomService.getOrgPopedomByOidMid(oid,mid);
        List<OrgPopedomHistory> orgPopedomHistories=orgPopedomService.getOrgPopedomHistoriesByOidMid(oid,mid);
        Map<String,Object> map=new HashMap<>();
        map.put("initialName",popedom.getName()); // 初始名称
        map.put("currentName",orgPopedom.getNewName()); // 当前名称
        map.put("historyList",orgPopedomHistories);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2025/5/22  1.339P菜单重命名
     * 给管理平台调用   查询 机构当前被重命名的菜单
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/orgRenameList.do")
    public JsonResult orgRenameList(Integer oid){
        List<OrgPopedom> orgPopedomList=orgPopedomService.getOrgPopedomNewNameListByOid(oid);
        return new JsonResult(1,orgPopedomList);
    }

    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/getOidByCreateDate.do")
    public JsonResult getOidByCreateDate(Long createDate) {
        Integer oid=orgService.getOidBycreateDate(new Date(createDate));
        return new JsonResult(1,oid);
    }
}
