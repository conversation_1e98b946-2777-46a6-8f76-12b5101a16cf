package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2023/9/25
 * 人事_员工打卡表
 */
@Entity
@Table(name = "t_personnel_attendance_punch")
public class PersonnelAttendancePunch extends BaseEntity implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "user")
    private Integer user;//员工id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name = "org")
    private Integer org;//机构id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name = "dept")
    private Integer dept;//部门id

    @Column(name = "card_no", length = 50, nullable = true, unique = false)
    private String cardNo; //打卡卡号

    @Column(name = "sys_device")
    private Integer sysDevice;//打卡设备ID(对应的t_iot_terminal表)

    @Column(name = "punch_terminal")
    private Integer punchTerminal;//打卡终端ID(对应的t_personnel_attendance_terminal表id)

    @Column(name = "terminal_type")
    private Byte terminalType;//`terminal_type` TINYINT DEFAULT NULL COMMENT '类型:1-终端,2-webchat',
    @Column(name = "terminal_uuid", length = 36)
    private String terminalUuid;//`terminal_uuid` VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '唯一标识/webchat union_id',
    @Column(name = "terminal_brand", length = 50)
    private String terminalBrand;//`terminal_brand` VARCHAR(50) DEFAULT NULL COMMENT '品牌/昵称',
    @Column(name = "terminal_model", length = 50)
    private String terminalModel;//`terminal_model` VARCHAR(50) DEFAULT NULL COMMENT '型号',

    @Column(name = "punch_time")
    private Date punchTime;//打卡时间

    @Column(name = "workday_type")
    private Byte workdayType;//`workday_type` TINYINT DEFAULT NULL COMMENT '到岗类型: 类型:1-假/离岗,2-班/到岗,3-待定或到岗又是离岗'

    @Column(name = "punch_type")
    private Byte punchType;//`punch_type` TINYINT DEFAULT NULL COMMENT '打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-待定'

    @Column(name = "type")
    private Byte type;//'考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-待定'

    @Column(name = "business_service")
    private String businessService;//`business_service` VARCHAR(255) DEFAULT NULL COMMENT '业务表Service',

    @Column
    private Integer business;//`business` INTEGER DEFAULT NULL COMMENT '业务表id',

    @Column(name = "image")
    private String image;//快照图片内容

    @Column(name = "memo")
    private String memo;//备注

    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime; //创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getDept() {
        return dept;
    }

    public void setDept(Integer dept) {
        this.dept = dept;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getSysDevice() {
        return sysDevice;
    }

    public void setSysDevice(Integer sysDevice) {
        this.sysDevice = sysDevice;
    }

    public Integer getPunchTerminal() {
        return punchTerminal;
    }

    public void setPunchTerminal(Integer punchTerminal) {
        this.punchTerminal = punchTerminal;
    }

    public Byte getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(Byte terminalType) {
        this.terminalType = terminalType;
    }

    public String getTerminalUuid() {
        return terminalUuid;
    }

    public void setTerminalUuid(String terminalUuid) {
        this.terminalUuid = terminalUuid;
    }

    public String getTerminalBrand() {
        return terminalBrand;
    }

    public void setTerminalBrand(String terminalBrand) {
        this.terminalBrand = terminalBrand;
    }

    public String getTerminalModel() {
        return terminalModel;
    }

    public void setTerminalModel(String terminalModel) {
        this.terminalModel = terminalModel;
    }

    public Date getPunchTime() {
        return punchTime;
    }

    public void setPunchTime(Date punchTime) {
        this.punchTime = punchTime;
    }

    public Byte getWorkdayType() {
        return workdayType;
    }

    public void setWorkdayType(Byte workdayType) {
        this.workdayType = workdayType;
    }

    public Byte getPunchType() {
        return punchType;
    }

    public void setPunchType(Byte punchType) {
        this.punchType = punchType;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getBusinessService() {
        return businessService;
    }

    public void setBusinessService(String businessService) {
        this.businessService = businessService;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}