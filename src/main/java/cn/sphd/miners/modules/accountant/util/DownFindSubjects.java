package cn.sphd.miners.modules.accountant.util;

import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;


import java.util.ArrayList;
import java.util.List;

/**
 * Created by khmsoft on 2017/1/12.
 */
public class DownFindSubjects {

    private String subjects=null;

    private String allSubjects=null;

    private List findSubjects = new ArrayList();

    public List getFindSubjects() {
        return findSubjects;
    }

    public void setFindSubjects(List findSubjects) {
        this.findSubjects = findSubjects;
    }

    private List findCaterorys = new ArrayList();

    public List getFindCaterorys() {
        return findCaterorys;
    }

    public void setFindCaterorys(List findCaterorys) {
        this.findCaterorys = findCaterorys;
    }

    public String getSubjects() {
        return subjects;
    }

    public void setSubjects(String subjects) {
        this.subjects = subjects;
    }

    public String getAllSubjects() {
        return allSubjects;
    }

    public void setAllSubjects(String allSubjects) {
        this.allSubjects = allSubjects;
    }

    public void allSub(String subject, Integer oid, SubjectSettingService subjectSettingService)throws Exception{
        downFind(subject,oid,subjectSettingService);
        String sub[]=getSubjects().split(",");
        if((sub.length)==1){
            for (int i=0;i<sub.length;i++){
                    allSubjects=sub[i];
            }
        }else{
            for (int j=0;j<sub.length;j++){
               if(j==(sub.length)-1){
                   allSubjects=allSubjects+sub[j];
               }else if(j==0){
                   allSubjects=sub[j]+",";
               }else{
                   allSubjects=allSubjects+sub[j]+",";
               }
            }
        }
    }

    public void downFind(String subject,Integer oid, SubjectSettingService subjectSettingService)throws Exception{
        findSubjects.add(subject);
        List<TAccountantSubjectEntity> list=subjectSettingService.listChildSubject(subject,oid);
        if(list.isEmpty()){

        }else{
            for(TAccountantSubjectEntity l : list){
                subject = l.getSubject();
                downFind(subject, oid, subjectSettingService);
            }
        }
    }

}
