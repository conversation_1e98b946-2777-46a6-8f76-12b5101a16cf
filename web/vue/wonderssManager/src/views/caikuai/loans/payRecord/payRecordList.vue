<template>
  <!--  <p>贷款详情中的付款记录</p>-->
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true @mycloseFun="handleCustomClose" :showBack=true>
      </PageHeader>
    </div>
    <div class="ty-fff">
      <div v-for="(item,index) in repaymentList" :key="index"
           :class="{ 'f4': index % 2 !== 0 ,'ty-fff':index%2===0}">
        <p>{{ item.createName }}<span class="padding-0-10">{{ new
        Date(item.createDate).format('yyyy-MM-dd  hh:mm:ss')}}</span>录入</p>
        <div class="spaceBetween">
          <div>
            <span>付款日期</span>
            <span>{{ new
            Date(item.repaymentTime).format('yyyy-MM-dd')}}</span>
          </div>
          <div>
            <span>付款方式</span>
            <span>{{ this.chargePayMethod(item.repaymentMethod) }}</span>
          </div>
          <div>
            <span>付款金额</span>
            <span>{{ this.formatCurrency(item.repaymentAmount) }}</span>
          </div>
          <div class="ic" @click="rou(item.id)">
            <el-icon>
              <arrow-right/>
            </el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "payRecordList",
  mixins: [finance],
  data() {
    return {
      tit: "财会",
      repaymentList: []
    }
  },
  mounted() {
    this.getLoansDetails()
  },
  methods: {
    rou(id) {
      this.$router.push({path: '/payRecordDetails', query: {id: id}})
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getLoansDetails() {
      let params = {id: this.id, sourceType: 2}
      this.financeApi.ordLoanDetail(params).then(res1 => {
        this.repaymentList = res1.data.data.repaymentList
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    },
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";


.box {

  > :nth-child(2) {
    > div {
      line-height: 25px;

      > :first-child {
        padding: 6px 15px 4px 15px;
      }

      > :nth-child(2) {
        padding: 0 15px 6px 15px;
      }

      > .spaceBetween {
        > div {
          display: flex;
          flex-direction: column;
        }

        > div:first-child, > div:nth-child(2) {
          width: 25%;
          text-align: center;
        }

        > div:nth-child(3) {
          width: 30%;
          text-align: center;
        }

        .ic {
          align-items: center;
          justify-content: center;
          height: 50px;
        }
      }
    }
  }
}
</style>