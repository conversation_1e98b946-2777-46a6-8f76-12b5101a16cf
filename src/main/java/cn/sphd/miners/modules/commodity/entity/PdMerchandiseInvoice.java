package cn.sphd.miners.modules.commodity.entity;


import javax.persistence.*;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-05-24
 */

@Entity
@Table(name = "t_pd_merchandise_invoice")
public class PdMerchandiseInvoice {

    /**
     * ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 机构ID
     */
    @Column(name = "org")
    private Integer org;

    /**
     * 商品ID
     */
    @Column(name = "merchandise")
    private Integer merchandise;

    /**
     * 类型:1-代码和名称,2-规格型号
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 型式:type=1时,1-采用商品代号的当前数据;2- 采用商品名称的当前数据;3-上述代号与名称都使用,代号在前;4-上述代号与名称都使用,名称在前;5-自定义;
     * type=2时,1-发票上的"规格型号"为空,无需填写;2-采用规格的当前数据;3-采用型号的当前数据;4-上述规格与型号都使用,规格在前;5-上述规格与型号都使用,型号在前;6- 自定义
     */
    @Column(name = "pattern")
    private Integer pattern;

    /**
     * 用户自定义值
     */
    @Column(name = "user_defined")
    private String userDefined;

    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 创建人id
     */
    @Column(name = "creator")
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    private Date createDate;

    /**
     * 修改人id
     */
    @Column(name = "updator")
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name")
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_date")
    private Date updateDate;

    /**
     * 操作:1-增,2-删,3-改
     */
    @Column(name = "operation")
    private Integer operation;

    /**
     * 修改前记录ID
     */
    @Column(name = "previous_id")
    private Integer previousId;

    /**
     * 版本号,每次修改+1
     */
    @Column(name = "version_no")
    private Integer versionNo;



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getMerchandise() {
        return merchandise;
    }

    public void setMerchandise(Integer merchandise) {
        this.merchandise = merchandise;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPattern() {
        return pattern;
    }

    public void setPattern(Integer pattern) {
        this.pattern = pattern;
    }

    public String getUserDefined() {
        return userDefined;
    }

    public void setUserDefined(String userDefined) {
        this.userDefined = userDefined;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
