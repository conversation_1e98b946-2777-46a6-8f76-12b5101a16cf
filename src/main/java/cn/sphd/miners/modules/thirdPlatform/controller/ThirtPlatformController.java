package cn.sphd.miners.modules.thirdPlatform.controller;

import cn.sphd.miners.common.controller.BaseController;
import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.initializer.SaveRpcLog;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.rpcLog.entity.RpcLog;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService.SmsContentTemplate;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.thirdPlatform.entity.TpApp;
import cn.sphd.miners.modules.thirdPlatform.entity.TpMember;
import cn.sphd.miners.modules.thirdPlatform.entity.TpMemberHistory;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService.TgCode;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/tp")
public class ThirtPlatformController extends BaseController {
    @Autowired
    ThirdPlatformService platformService;
    @Autowired
    UserService userService;
    @Autowired
    SmsService smsService;
    @Autowired
    AuthService authService;

    public ThirtPlatformController() {
        //关闭父类参数自动转换
        setInitBinderConfig(InitBinderConfig.DisableXSSDefence, InitBinderConfig.DisableDateChange);
    }

    /**
     * <AUTHOR>
     * @description 接收微信第三方推送消息与事件的接口，微信第三方“授权事件接收配置”的地址；
     *                  由于推送过来的消息格式（xml/json）不同，此类接口微信与头条需要分开实现；
     *                  目前头条接口未实现；
     *                  目前微信接口只实现了ComponentVerifyTicket解析，其他消息解析需要在wxTgService.receiveAuthEvent方法中添加 else if 实现
     * @date Create at 2022/6/30 12:54
     * @method wxReceiveAuthEvent 接收微信第三方推送消息与事件的接口
     * @param postData
     * @param signature
     * @param timeStamp
     * @param nonce
     * @param encryptType
     * @param msgSignature
     * @return: java.lang.String:
    **/
    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping("/wxReceiveAuthEvent.do")
    @SaveRpcLog
    public String wxReceiveAuthEvent(@RequestBody(required = false) String postData,
                                     @RequestParam(value = "signature", required = false) String signature,
                                     @RequestParam(value = "timestamp", required = false) String timeStamp,
                                     @RequestParam(value = "nonce", required = false) String nonce,
                                     @RequestParam(value = "encrypt_type", required = false) String encryptType,
                                     @RequestParam(value = "msg_signature", required = false) String msgSignature) {
        logger.warn("ThirtPlatformController.wxReceiveAuthEvent.postData: \n" + postData);
        logger.warn("ThirtPlatformController.wxReceiveAuthEvent.signature: " + signature);
        logger.warn("ThirtPlatformController.wxReceiveAuthEvent.timestamp: " + timeStamp);
        logger.warn("ThirtPlatformController.wxReceiveAuthEvent.nonce: " + nonce);
        logger.warn("ThirtPlatformController.wxReceiveAuthEvent.encrypt_type: " + encryptType);
        logger.warn("ThirtPlatformController.wxReceiveAuthEvent.msg_signature: " + msgSignature);
        platformService.wxReceiveAuthEvent(postData, signature, timeStamp, nonce, encryptType, msgSignature);
        return "success";
    }

    /**
     * <AUTHOR>
     * @description 接收微信第三方代授权的公众号或小程序的接收平台推送的消息与事件接口，微信第三方“消息与事件接收配置”配置的接口
     *                  接口地址和参数已经配置好，具体实现需要在本函数return "success"前增加调用Service的实现方法即可
     * @date Create at 2022/6/30 13:05
     * @method wxCallback
     * @param xml 
     * @param appId 
     * @param signature 
     * @param timeStamp 
     * @param nonce 
     * @param encryptType 
     * @param msgSignature 
     * @return: java.lang.String: 
    **/
    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping(value = "/appid/{appId}/wxCallback.do")
    @SaveRpcLog
    public String wxCallback(@RequestBody(required = false) String xml,
                             @PathVariable(value = "appId") String appId,
                             @RequestParam(value = "signature", required = false) String signature,
                             @RequestParam(value = "timestamp", required = false) String timeStamp,
                             @RequestParam(value = "nonce", required = false) String nonce,
                             @RequestParam(value = "encrypt_type", required = false) String encryptType,
                             @RequestParam(value = "msg_signature", required = false) String msgSignature) {
        logger.warn("ThirtPlatformController.wxCallback.xml: " + xml);
        logger.warn("ThirtPlatformController.wxCallback.appid: " + appId);
        logger.warn("ThirtPlatformController.wxCallback.signature: " + signature);
        logger.warn("ThirtPlatformController.wxCallback.timestamp: " + timeStamp);
        logger.warn("ThirtPlatformController.wxCallback.nonce: " + nonce);
        logger.warn("ThirtPlatformController.wxCallback.encrypt_type: " + encryptType);
        logger.warn("ThirtPlatformController.wxCallback.msg_signature: " + msgSignature);
        return "success";
    }

    /**
     * <AUTHOR>
     * @description 接收微信支付回调的接口，微信支付“payRequest.setNotifyUrl”配置的地址；
     * @date Create at 2023/3/14 16:14
     * @method wxReceivePayNotify 接收微信支付回调的接口
     * @param mchid
     * @param serialNumber
     * @param signature
     * @param nonce
     * @param timestamp
     * @param signType
     * @param body
     * @return
     */
    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping("/mchid/{mchid}/wxReceivePayNotify.do")
    @SaveRpcLog
    public String wxReceivePayNotify(@PathVariable(value = "mchid") String mchid,
                                    @RequestHeader(value = "Wechatpay-Serial", required = false) String serialNumber,
                                    @RequestHeader(value = "Wechatpay-Signature", required = false) String signature,
                                    @RequestHeader(value = "Wechatpay-Nonce", required = false) String nonce,
                                    @RequestHeader(value = "Wechatpay-Timestamp", required = false) String timestamp,
                                    @RequestHeader(value = "Wechatpay-Signature-Type", required = false) String signType,
                                    @RequestBody(required = false) String body,
                                    RpcLog rpcLog) {
        logger.warn("ThirtPlatformController.wxReceivePayNotify.mchid: " + mchid);
        logger.warn("ThirtPlatformController.wxReceivePayNotify.serialNumber: " + serialNumber);
        logger.warn("ThirtPlatformController.wxReceivePayNotify.signature: " + signature);
        logger.warn("ThirtPlatformController.wxReceivePayNotify.nonce: " + nonce);
        logger.warn("ThirtPlatformController.wxReceivePayNotify.timestamp: " + timestamp);
        logger.warn("ThirtPlatformController.wxReceivePayNotify.signType: " + signType);
        logger.warn("ThirtPlatformController.wxReceivePayNotify.body: " + body);
        Map<String, Object> params = new HashMap<String, Object>(8) {{
            put("mchid", mchid);
            put("body", body);
            put("serialNumber", serialNumber);
            put("signature", signature);
            put("nonce", nonce);
            put("timestamp", timestamp);
            put("signType", signType);
            put("rpcLog", rpcLog);
        }};
        logger.warn("params:\n"+JSON.toJSONString(params));
        platformService.wxReceivePayNotify(params);
        return "";
    }

    /**
     * <AUTHOR>
     * @description 小程序登录
     * @date Create at 2022/6/30 10:52
     * @method mpCodeLogin 小程序登录
     * @param response
     * @param token
     * @param sessionid
     * @param tgCode 前端传回字符串：= "weChat" 微信，= "byteDance" 字节，参考TgCode枚举类的name
     * @param appId 小程序的appId，前端获取后传过来
     *                  说明，数据库数据：TpApp.needAcc = true 需要注册账号登录，登录成功后，token符合 manor = true, tpMember = true
     *                                                = false 无需注册账号即可登录，登录成功后，token符合 guest = true, tpMember = true
     *                                  可以据此设定后续接口的@AuthPassport注解
     *                        PS：短信验证注册的接口代码在AuthController中,sendMessageVerificationCodeRegister 和 checkVerificationCode。
     * @param code 小程序调login接口后返回的code
     * @param userInfo 小程序调getUserInfo后获取的userInfo，用encodeURIComponent(JSON.stringify(res.userInfo))编码，
     *                  如果字节小程序有获取phoneNumber，也可以作为userInfo的属性传过来，
     *                  如果没有获取userInfo不传这个参数就行
     * @param tokenId 移动端微信登录参数
     * @param deviceType 移动端微信登录参数
     * @param brand 移动端微信登录参数
     * @param model 移动端微信登录参数
     * @param deviceUuid 移动端微信登录参数
     * @param intent 移动端微信登录参数
     * @return: cn.sphd.miners.common.persistence.JsonResult: 成功的话，header会返回token，JsonResutl类中属性： success = 1, data 暂定为 JSON封装的TpMember
     *          调用失败返回值和wonderss登录接口基本一致，错误代码："2":"need verification"; "3":"need register"; "4", "login error, 可能是授权或者code问题，建议重试tt.login"; "5", 服务器问题；; "6", 账号被锁定；
    **/
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/mpCodeLogin.do")
    public JsonResult mpCodeLogin(HttpServletRequest request, HttpServletResponse response, String token, String sessionid, String tgCode, String appId, String code, String userInfo, String tokenId, String deviceType, String brand, String model, String deviceUuid, String intent) {
        System.out.println("userInfoMobile 1:"+userInfo);
        Map<String, String> info = new HashMap<>(MapUtils.emptyIfNull(JSON.parseObject(userInfo, Map.class)));
        System.out.println("userInfoMobile 1.1:"+JSON.toJSONString(info));
        if(StringUtils.isNotEmpty(tokenId)) {
            info.put("tokenId", tokenId);
        }
        if(StringUtils.isNotEmpty(deviceType)) {
            info.put("deviceType", deviceType);
        }
        if(StringUtils.isNotEmpty(brand)) {
            info.put("brand", brand);
        }
        if(StringUtils.isNotEmpty(model)) {
            info.put("model", model);
        }
        if(StringUtils.isNotEmpty(deviceUuid)) {
            info.put("deviceUuid", deviceUuid);
        }
        if(StringUtils.isNotEmpty(intent)) {
            info.put("intent", intent);
        }
        logger.warn("code2SessionMember mpCodeLogin sessionid：" + sessionid + " code：" + code);
        JsonResult result = platformService.code2SessionMember(request, response, token, sessionid, tgCode, appId, code, info);
        logger.warn("mpCodeLogin.do\n"+JSON.toJSONString(result));
        if(result.getSuccess().intValue()==1) {
            TpMember member = (TpMember) result.getData();
            result.setData(member.getId());
        }
        return result;
    }

    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/tpCodeBindAcc.do")
    public JsonResult tpCodeBindAcc(HttpServletResponse response, String tgCode, String appId, String code, AuthAcc acc, String userInfo) {
        System.out.println("userInfoMobile 2:"+userInfo);
        Map<String, String> info = JSON.parseObject(userInfo, Map.class);
        System.out.println("userInfoMobile 2.1:"+JSON.toJSONString(info));
        System.out.println("code2SessionMember tpCodeBindAcc code：" + code);
        return platformService.codeBindAcc(response, tgCode, appId, code, info, acc);
    }

    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/tpUnBindAcc.do")
    public JsonResult tpUnBindAcc(HttpServletResponse response, String token, String tgCode, String appId, AuthAcc acc) {
        return platformService.unBindAcc(response, token, tgCode, appId, acc);
    }

    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getOrGroupByAcc.do")
    public JsonResult getOrGroupByAcc(String tgCode, String appId, AuthAcc acc) {
        ThirdPlatformService.TgCode tgCodeObject = ThirdPlatformService.TgCode.getInstance(tgCode);
        TpApp app = platformService.getTpApp(tgCodeObject, appId, Boolean.TRUE);
        TpMember member = platformService.getOrGroupByAcc(app, acc);
        System.out.println("getOrGroupByAcc.do, accid: "+acc.getId()+", member："+JSON.toJSONString(member));
        if(member!=null) {
            Map<String, String> result = new HashMap<String, String>(2){{
                put("nickName", member.getNickName());
                put("avatar", member.getAvatar());
            }};
            return new JsonResult(1, result);
        } else {
            return new JsonResult(new MyException("2", "未绑定"));
        }
    }
    @ResponseBody
    @RequestMapping(value = "/debugRemoveAllTpMemberAndAccUserOrg.do", produces = "application/json;charset=utf-8")
    public JsonResult debugRemoveAllTpMemberAndAccUserOrg(HttpServletRequest request) {
        return platformService.debugRemoveAllTpMemberAndAccUserOrg(request);
    }

    /**
     * 绑定的操作记录
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/tpBindAccHistories.do")
    public JsonResult tpBindAccHistories(AuthAcc acc){
        List<TpMemberHistory> tpMemberHistories= platformService.getTpMemberHistories(acc.getId());
        return new JsonResult(1,tpMemberHistories);
    }

    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping("/test.do")
    @SaveRpcLog
    public String test(@RequestHeader(value = "Request-ID", required = false) String requestId, RpcLog rpcLog){
        platformService.test();
//        Map<String, Object> params = new HashMap<String, Object>(8) {{
//            put("mchid", "**********");
//            put("serialNumber", "1502DD255D4AADAD3DEC4B89F1E38699116B0E24");
//            put("signature", "ppp1qrui785nT5z8MuawdwsZCrLK6myayBLryCss/pmOLBhdeDNHsJ4QlmTwOlbXOJsCmT49HWUtuUOBntkw+uhAS758w9tz7Z1hoGKlvpqnfgEmfuHtBI4bWwep+1NZOFQdjKUmA5byPg8C54uBF3p0qH8sdCZdr028ctCTjQzfz32LaFQeUZqPDrrt8UkWm1blMonCIU0YDPGDk/ErWLCcyj1O72RCLytxqFmQWARbgQnTqNRmOQfjGW5jcyrNcWi0gNMYq4yK2zFZqlW2i2Q28CBqKarkQ0rYSK5u3wBCnOi984aCPR6vUw5D3PSob4/F2qxxrROHNzub8NVy+A==");
//            put("nonce", "blFjSEriosXY60RXJQDRZLERSN05tyu2");
//            put("timestamp", "**********");
//            put("signType", "WECHATPAY2-SHA256-RSA2048");
//            put("body", "{\"id\":\"399f4a14-1d1d-50ba-b618-602bee37ebee\",\"create_time\":\"2023-03-30T09:26:25+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"TRANSACTION.SUCCESS\",\"summary\":\"支付成功\",\"resource\":{\"original_type\":\"transaction\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"YcHTj9Pu8pXhPUEf7Ps7OINUweVZhuUyZZ4yXG59ohDhhR24RWEjih7gPkIe0IwBoN0gm6PK5a9ILwhBA6uJMi2ALqphiYeUr/4uOyoyYFWFIltIMRkzQLvGPP4hsEGPkgm2PuNsXyg3zxHV6ER0ZTRJWzdA0ALk9ffDkdIZ7WNx0C3Fjz0UF+EiZxu8kwkS+D7kHqrioyyfPpq2bxWXsL4eP0+J01oQH+rx5aZbECKf/haaSEWk8ASYNl6wYS1e7GY4Zb0+BZ4vLJYkveA1bFaMXUxfeAH6oerR5Sk5a+NbBsoa2elx9G1eUB2ZD28EwGpjZvUtFb5ZYwGjcP/949imAQuW85Rekc3yzE4T2++0ICnD02bnlXs+Tt6ryRrRT0B7AggbROmjDQYf0dNrmxDVksmvYgSdSJ0Yh5zQ1nDTd7plm3wkZWyGI0faxRhOBdk8Lz9eWUvhzrvmMXdCoERSqb4R28LgK4LYmwUIRisxr94R2ENmALXhZ0UHTeVypaBBZYs4nI2ZnSM1ym9R06/2ee7575CFOUGLMiIiPC+GRoolt8UeQzEzb30kV483vPP8ts3VSt2KqZYUXsE8qLj9ag==\",\"associated_data\":\"transaction\",\"nonce\":\"C52AhoUB5qVa\"}}");
//            put("rpcLog", rpcLog);
//        }};
//        System.out.println(JSON.toJSONString(rpcLog));
//        platformService.wxReceivePayNotify(params);
        return "";
    }

    //用于小程序登录时判断是否在发布
    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping("/tpAppGetToken.do")
    public String tpAppGetToken(String appId, String secretKey, String appVersion, String envType) {
        String key = platformService.appTokenKey(appId, secretKey, appVersion, envType);
        System.out.println("tpAppGetToken.do appId = " + appId + ", secretKey = " + secretKey + ", appVersion = " + appVersion + ", envType = " + envType);
        System.out.println("tpAppGetToken.do key length " + StringUtils.length(key) + ", key = " + key);
        return key;
    }


    /**
     * <AUTHOR>
     * @Date 2024/5/22 10:03
     * 小程序注册 验证码
     */
    @AuthPassport(guest = true, tpMember = true)
    @ResponseBody
    @RequestMapping("/tpRegisterVerificationCode.do")
    public JsonResult tpRegisterVerificationCode(String phone, AuthInfoDto authInfo) {
        final List<String> verifyMethodNames = new ArrayList<String>(1) {{
            add("tpCheckRegisterVerificationCode");
        }};
        TgCode tgCode = TgCode.getByIndex(authInfo.getTgCode());
        SmsContentTemplate template = null;
        switch (tgCode) {
            case weChat:
                template = SmsContentTemplate.wxAccLogin;
                break;
            case byteDance:
                template = SmsContentTemplate.accLogin;
                break;
        }
        if(template!=null) {
            return smsService.sendVerificationCodeForRegister(verifyMethodNames, phone, authInfo, template);
        } else { //TODO：目前注册模板是按照第三方类型代码写死，实际后续应改为不同的第三方小程序应该是不同的注册模板内容（需要修改数据库）。
            return new JsonResult(new MyException("-1", "找不到对应的短信模板"));
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024/5/22 14:20
     * 小程序 注册验证 验证码
     */
    @AuthPassport(guest = true, tpMember = true)
    @ResponseBody
    @RequestMapping("/tpCheckRegisterVerificationCode.do")
    public JsonResult tpCheckRegisterVerificationCode(HttpServletRequest request, HttpServletResponse response, String sessionid, TpMember tpMember, String phone, String code, AuthInfoDto authInfo) {
        final String sendMethordName = "tpRegisterVerificationCode";
        if (smsService.verificationCode(response, authInfo,sendMethordName,phone,code)) {
            return platformService.verificationCodeMpLogin(request, response, sessionid, tpMember, phone, code,sendMethordName,authInfo);
        } else {
            return new JsonResult(new MyException("-1", "验证失败"));
        }
    }

}