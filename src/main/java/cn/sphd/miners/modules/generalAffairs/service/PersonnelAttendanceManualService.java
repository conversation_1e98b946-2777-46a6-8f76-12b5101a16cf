package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceManual;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUser;

/**
 * PersonnelAttendanceManualService_in_generalAffairs
 *
 * @author: wuyu
 * @since: 4.0
 * @date: 2025-05-12 13:17:50
 **/
public interface PersonnelAttendanceManualService extends AttendanceEventSerivce{
    PersonnelAttendanceManual getOrNewByPau(PersonnelAttendanceUser pau);
    PersonnelAttendanceManual getByPau(PersonnelAttendanceUser pau);
    void saveOrUpdate(PersonnelAttendanceManual pam);
}
