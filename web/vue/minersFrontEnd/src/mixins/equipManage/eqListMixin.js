import * as api from "@/api/equipMent";

export default {
    name:'eqListMixin',
    components:{},
    data(){
        return {
            mainNum: 'page0',
            category: 0,
            recordNum: 1,
            backPrePage: 1,
            editSource: 1,
            msgEditTtl: '公共信息的修改',
            filterCat: '',
            selectCatType: '',
            equipInfo: {
                pageInfo: {
                    currentPageNo: 1,
                    pageSize: 20
                }
            },
            equipNoCategory: {},
            equipNoCategoryIndex: 0,
            eqGroupItemIndex: 0,
            eqScanDetail: {},
            equipmentInfo: {},
            selectedCatItem: {},
            recordScan:{},
            supplierForm:{},
            uniformCatName: {},
            categoryList: [],
            eqScanList: [],
            supplierList: [],
            eqGroupList: [],
            editRecordList: [],
            filterEqListData: {},
            batchEqData:{},
            selectCatLog: false,
            eqScanLog: false,
            amountChangeTipLog: false,
            msgEditLog: false,
            sflag: false,
            dotState: false,
            finishState: false,
            addSupLog: false,
            uniformSettingsLog: false,
            addUnitLog: false,
            editRecordLog: false,
            recordScanLog: false,
            addNameLog: false,
            batchSavedList:[],
            categoryProps:{
                label: 'name',
                children: 'children',
                isLeaf: 'leaf'
            }
        }
    },
    created(){},
    mounted(){},
    computed: {
        readySetNum: function () {
            let count = 0;
            let list = this.equipNoCategory.data
            list.forEach(item => {
                if (item.path !== '--') {
                    count++
                }
            })
            return count
        },
        batchSelectNum: function () {
            let count = 0;
            this.batchSavedList.forEach(item => {
                count += item.selectEqIds.length
            })
            return count
        },
        checkedNum: function () {
            let count = 0;
            this.batchEqData.list.forEach(item => {
                if (item.checked) count++
            })
            return count
        }
    },
    methods: {
        getCreatData() {
            this.category = 0
            this.getEquipList(1, 0, 0)
        },
        getEquipList(cur, category, page) {
            let param = {
                'pageSize': 20,
                'currentPageNo': cur
            }
            if (category !== undefined && category !== "") {
                param['category'] = category
            }
            api.getEqList(param).then(res => {
                let list = res.data.data || []
                list.map(item =>  {
                    let emCount = item.emCount && item.emCount !== "" ? item.emCount.split(",") : "";
                    item.ids = item.emCount;
                    item.length = emCount.length;
                    return item
                })
                this.mainNum = 'page' + page
                if (page === 0) {
                    this.equipInfo = JSON.parse(JSON.stringify(res.data))
                } else if (page === 1) {
                    this.equipNoCategory = JSON.parse(JSON.stringify(res.data))
                } else if (page === 4) {
                    this.filterEqListData = JSON.parse(JSON.stringify(res.data))
                }
            })
        },
        pageInfoControl(pageInfo){
            this.getEquipList(pageInfo.page, this.category, 0)
        },
        pageInfoUnHandle(pageInfo){
            this.getEquipList(pageInfo.page, -1, 1)
        },
        pageInfoFilter(pageInfo){
            this.getEquipList(pageInfo.page, this.category, 4)
        },
        equipUnHandle(){
            this.category = -1
            this.sflag = false
            this.getEquipList(1, -1, 1);
            this.getCatList('')
        },
        manageBtn(info) {
            this.eqScanLog = true
            this.eqScanDetail = info
            this.getEqDetail(info.emCount)
        },
        getEqDetail(ids) {
            let param = {"ids": ids}
            api.getEqDetail(param).then(res => {
                this.eqScanList = res.data || []
            })
        },
        selectPathBtn(index){
            this.eqGroupItemIndex = index
            this.filterCatBtn('selectPathBtn')
        },
        getCatList(info) {
            let param = { 'enabled': 1, 'parent': 0 }
            if (info !== '') {
                param.parent = info.id
            }
            this.firstFourCate = []
            api.getchildcat(param).then(res => {
                if (info === '') {
                    let list = res.data || []
                    this.categoryList = list
                    for (let r=0;r<4;r++){
                        this.firstFourCate.push(list[r].id)
                    }
                } else {
                    info.children = res.data
                }
            })
        },
        getSameEqList(info, num){
            this.equipNoCategoryIndex = num
            this.equipmentInfo = JSON.parse(JSON.stringify(info))
            if (info.length === 1) {
                this.filterCatBtn('moreSelectPathBtn');
            } else {
                // let icon = obj.data("align");
                // if(icon === 1) {
                //   info = JSON.parse(obj.children(".hd").html());
                //   $("#subPage").data("num", 1);
                // } else {
                //   info = JSON.parse(obj.siblings(".hd").html());
                //   $("#subPage").data("num", 3);
                // }
                this.mainNum = 'page5'
                this.finishState = false
                //catSelectList = [];
                let param = {"ids": info.emCount}
                api.getEqDetail(param)
                    .then(res => {
                        let list = res.data || []
                        this.eqGroupList = list
                    })
            }
        },
        initUniform(){
            this.uniformSettingsLog = true
            this.uniformCatName = {name: ''}
        },
        updateBtn() {
            let detail = JSON.parse(JSON.stringify(this.eqScanDetail))
            detail.equipment = detail.equipmentId
            detail.unitId = detail.unit_id
            this.msgEditLog = true
            this.editSource = 1
            this.msgEditTtl = '公共信息的修改'
            setTimeout(() => {
                this.$refs.baseFormRef.resetFields()
            }, 500);
            // 预备数据
            this.getNameList();
            this.getSupList();
            this.getUnit();
            this.baseForm = detail
        },
        updateEqBtn(detail,index) {
            let info = JSON.parse(JSON.stringify(detail))
            let baseInfo = JSON.parse(JSON.stringify(this.eqScanDetail))
            baseInfo.equipment = detail.equipmentId
            this.msgEditLog = true
            this.editSource = 2
            this.eqGroupItemIndex = index
            this.msgEditTtl = '修改装备器具的信息'
            setTimeout(() => {
                this.$refs.baseFormRef.resetFields()
            }, 500);
            // 预备数据
            this.getNameList();
            this.getSupList();
            baseInfo.unitId = baseInfo.unit_id
            if (baseInfo.length === 1) {
                this.getUnit();
            }
            info.lifeSpan = info.life_span
            info.originalValue = info.original_value
            info.rDate = new Date(detail.receive_date).format("yyyyMM")
            this.baseForm = {...info,...baseInfo}
        },
        editMsgOk() {
            let equipment = this.baseForm.equipment
            if( Number(equipment) === 0 ){
                this.$message({
                    'type':'error',
                    'offset':'26',
                    'message': "装备器具名称为必填项！"
                })
                return false
            }
            let source = this.editSource
            let url = "../equipment/model/edit";
            let data = {
                'emCount': this.baseForm.length,
                'modelName': this.baseForm.modelName,
                'supplier': this.baseForm.supplier
            }
            if (source === 1) { //公共信息的修改
                data.operation = 6;
                data.unitId = this.baseForm.unitId;
                if (data.unitId) {
                    let unitItem = this.unitList.find(item => item.id == data.unitId)
                    data.unit = unitItem.name;
                }
                if (Number(this.eqScanList.length) > 1) {
                    data.ids = this.baseForm.ids;
                    data.eid = equipment
                    url = "../equipment/model/emEdit";
                } else {
                    data.id = this.baseForm.id;
                    data.equipment = equipment
                }
            } else {//修改装备器具的信息
                data.id = this.baseForm.id;
                data.operation = 7;
                data.equipment = equipment
                data.category = this.baseForm.category;
                data.modelCode = this.baseForm.modelCode;
                data.originalValue = this.baseForm.originalValue;
                data.lifeSpan = this.baseForm.lifeSpan;
                data.rDate = this.baseForm.rDate;
                data.conditions = this.baseForm.conditions;
                data.memo = this.baseForm.memo;
                if (this.baseForm.unitId && Number(this.eqScanList.length) === 1) {
                    data.unitId = this.baseForm.unitId;
                    let unitItem = this.unitList.find(item => item.id == data.unitId)
                    data.unit = unitItem.name;
                }
            }
            api.editEquipment(data,url).then(res => {
                let {ids} = res.data
                this.msgEditLog = false
                if(equipment && equipment !== ""){
                    let eNameItem = this.eqNameList.find(item=> item.id === equipment)
                    this.eqScanDetail.equipmentName = eNameItem.fullName
                }
                if(data.supplier && data.supplier !== ""){
                    let eNameItem = this.supplierList.find(item=> item.id === data.supplier)
                    this.eqScanDetail.supplierName = eNameItem.fullName
                }
                this.eqScanDetail.modelName = data.modelName
                this.eqScanDetail.unitName = data.unit || ''
                this.eqScanDetail.unit_id = data.unitId || ''
                this.eqScanDetail.length = !ids || ids === "" ? 0: ids.split(",").length
                this.getEqDetail(ids)
                this.getEquipList(this.equipInfo.pageInfo.currentPageNo, this.category, 0)
            })
        },
        editLog(info, num) {
            this.recordNum = num
            this.editRecordLog = true
            let data = { id: info.id };
            num === 1? data.operation = 6:data.operation = 7;
            api.getHistories(data).then(res => {
                let list = res.data || []
                this.editRecordList = list
            })
        },
        logScan(info) {
            this.recordScan = info
            if (this.recordNum === 2) {
                this.recordScan.equipmentName = this.eqScanDetail.equipmentName
                this.recordScan.modelName = this.eqScanDetail.modelName
                this.recordScan.unitName = this.eqScanDetail.unitName
                this.recordScan.supplierName = this.eqScanDetail.supplierName
            }
            this.recordScanLog = true
        },
        batchClass() {
            this.mainNum = 'page2'
            let noList = JSON.parse(JSON.stringify(this.equipNoCategory.data))
            this.batchEqData = {
                allList: noList,
                list: noList
            }
        },
        toogeCircle() {
            this.sflag = !this.sflag
        },
        toogeCircle2() {
            this.dotState = !this.dotState
        },
        toogeCircle3() {
            this.finishState = !this.finishState
        },
        selectCatSure() {
            if(this.equipNoCategory.data.length <= 0){
                return false;
            }
            if(this.sflag){
                let str = ``;
                this.equipNoCategory.data.forEach(function (item) {
                    if (item.length === 1) {
                        if(item.category && item.category !== ""){
                            str += `ids=${item.category}-${item.id}&`
                        }
                    }
                })
                str = str.slice(0, -1)
                api.saveBatchCatOk(str).then(() => {
                    this.getEquipList(1, -1, 1)
                    this.toogeCircle()
                })
            }else{
                this.$message({
                    'type':'error',
                    'offset':'26',
                    'message': "请确认是不是已完成！"
                })
            }
        },
        uniformSettingsSure(){//ids的模板为 分类id-装备id
            let catName = this.uniformCatName.name
            if(catName !== ""){
                let catId = this.uniformCatName.id
                let str = catId;
                this.eqGroupList.forEach(item => {
                    str += `-${item.id}`
                })
                api.uniformSure(str).then(() => {
                    this.uniformSettingsLog = false
                    this.getEquipList(1, -1, 1)
                })

            }else{
                this.$message({
                    'type':'error',
                    'offset':'26',
                    'message': "请确认是不是已选择类别"
                })
            }
        },
        selectEqBtn(info) {
            this.mainNum = 'page3'
            //let isOk = this.batchSavedList.find(item=> item.id == info.id)
            let tempArr = []
            this.batchEqData.list = []
            this.batchEqData.allList.forEach(item => {
                let isOk = this.batchSavedList.find( idsItem=> idsItem.selectEqIds.includes(item.id))
                //if((isOk && isOk.selectEqIds.includes(item.id))) {
                if(isOk) {
                    item.checked = isOk.id == info.id
                    if (isOk.id == info.id) {
                        tempArr.push(item)
                    }
                } else {
                    item.checked = false
                    tempArr.push(item)
                }
            })
            this.batchEqData.list = tempArr
            this.selectedCatItem = info
        },
        checkBtn(info, icon) {
            let size = info.emCount.split(",").length
            if (size > 1) {
                this.backPrePage = 'page3'
                this.getSameEqList(info, icon)
            } else {
                //info.checked = !info.checked
                this.batchEqData.list[icon].checked = !info.checked
                // if (!info.checked) {
                //     info.checked = true
                // } else {
                //     info.checked = false
                // }
            }
        },
        selectEQOK() {
            this.mainNum = 'page2'
            let catInfo = this.selectedCatItem
            // 装备
            let eqIds = []
            this.batchEqData.list.forEach(function (item){
                if (item.checked) {
                    eqIds.push(Number(item.id))
                }
            })
            catInfo.selectEqIds = eqIds
            let isExist = this.batchSavedList.findIndex(item => item.id == this.selectedCatItem.id)
            if (isExist > -1) {
                this.batchSavedList[isExist] = catInfo
            } else {
                this.batchSavedList.push(catInfo)
            }
        },
        saveBatchCat() {
            if(this.dotState){
                let str = ``
                this.batchSavedList.forEach(function(catInfo){
                    if(catInfo.selectEqIds && catInfo.selectEqIds.length > 0){
                        str += `ids=${catInfo.id}-${ catInfo.selectEqIds.join('-')}&`
                    }
                })
                if (str !== '') {
                    str = str.slice(0, -1)
                    api.saveBatchCatOk(str).then(() => {
                        this.getEquipList(1, -1, 1)
                        this.toogeCircle2()
                        this.batchSavedList = []
                        this.batchEqData = {}
                    })
                }
            }
        },
        sameEqComplete(){
            if(!this.finishState){
                return false;
            }
            if(this.finishState){
                let str = ``;
                this.eqGroupList[this.eqGroupItemIndex].category = this.selectedCatItem.id
                this.eqGroupList.forEach(function (info){
                    let catId = info.category
                    if (catId) {
                        str += `ids=${catId}-${info.id}&`
                    }
                })
                str = str.slice(0, -1)
                api.saveBatchCatOk(str).then(() => {
                    this.getEquipList(1, -1, 1)
                    this.toogeCircle3()
                })
            }
        },
        hideEqScanLog(){
            this.eqScanLog = false
        },
        hideEditRecordLog(){
            this.editRecordLog = false
        },

        hideRecordScanLog(){
            this.recordScanLog = false
        },
        filterCatBack(){
            this.filterCat = ''
            this.mainNum = 'page0'
        },
        hideSelectCatLog(){
            this.selectCatLog = false
        },
        hideMsgEditLog(){
            this.msgEditLog = false
        },
        hideAmountChangeTipLog(){
            this.amountChangeTipLog = false
        },
        hideAddNameLog(){
            this.addNameLog = false
        },
        hideAddSupLog(){
            this.addSupLog = false
        },
        hideAddUnitLog(){
            this.addUnitLog = false
        },
        hideUniformSettingsLog(){
            this.uniformSettingsLog = false
        }
    },
    watch:{
        'mainNum'() {
            if (this.mainNum === 'main') {
                this.getInitData()
            } else if (this.mainNum === 'page0') {
                this.category = 0
                this.getEquipList(1, 0, 0)
            } else if (this.mainNum === 'page1') {
                this.category = -1
            }
        }
    }
}



/*补录结束*/
