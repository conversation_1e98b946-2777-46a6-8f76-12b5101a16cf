.fa-circle-o,.fa-dot-circle-o{ color:#48cfad;  }
.item{margin-bottom: 8px;}
.item_head,.item_content{display: inline-block;}
.item_head{width: 64px;text-align: right;margin-right: 8px;}
.ty-blue{color: #5d9cec;}
.item{display: flex;padding: 4px 0;}
.item .item_title{width: 90px;flex: none;color: #666;line-height:32px;padding: 0 8px;}
.item .item_content{margin-bottom: 4px;flex: auto;line-height:32px;}
.item .item_content .ty-radio, .item .item_content .ty-checkbox{width: 120px;padding: 0 24px;}
.item .item_content .ty-checkbox{border-radius: 3px;cursor: default;}
.item .item_content .ty-checkbox:hover, .item .item_content .ty-checkbox.checked{background: #daebff;}
.item .item_content input + label{left: 24px;top: calc(50% - 8px);}
.ty-circle-2{border-radius: 2px;}
.kj-input, .kj-select{height: 32px;border:1px solid #f0f0f0;color: #666;padding:0 8px;min-width: 167px;}
.kj-input-blue, .kj-select-blue{border-color: #5d9cec;}
.kj-input:disabled, .kj-select:disabled{border-color: #ccc;background-color: #f0f0f0;cursor: not-allowed;color: #aaa;}
.btn-group{flex: auto;}
.page{padding: 8px 0 8px 70px;max-width: 1200px;}
.page .page-content-avatar {margin-top: 8px;padding-left: 8px;}
.page .page-header{margin-top: 8px;padding: 8px 0;color: #76787d;}
.part{padding: 8px 0;margin-top: 8px;}
.panel-box{border-bottom: 1px solid #ddd;padding-top: 8px;margin-left: 12px;}
.panel-box:last-child{border: none}
.part_addBranches .panel-box{border-bottom:none;}
.kj-hr{border-bottom: 1px solid #eee;margin: 8px 0;}
.ty-alert{font-size: 14px;}
.main_item{width: 350px;}
.btn-group{flex: auto;}
.main-color-black{color: #666;margin: 0 4px;}
.stepItem{margin: 0 0 20px 0;}
.managerList{margin-bottom: 8px;}
#chooseIsCompeteManger{margin-bottom: 8px;}
.g_item_con{padding-top: 20px;}
.moreGuideCon {padding-left: 74px;padding-top: 40px;}
.moreGuideCon ul li {padding-bottom: 20px;}
.moreGuideCon ul li div{padding-left: 30px;}
.thin-btn{color: #0070c0;margin-right: 20px;}
.thin-btn:last-child{margin-right: 0;}
.part_addBranches{margin-left: 12px;width: 800px;}
.brance_input label{font-weight: normal;}
.branchInfo {margin-bottom: 20px;border-bottom: 1px solid #bebebe;}
.part_addBranches .ty-alert,.branchInfo .ty-alert{color: #101010;justify-content: space-between;align-items: flex-start;}
.part_addBranches .ty-alert,.branchInfo .ty-alert{line-height: 32px;}
.common-select{border: 1px solid #dcdfe6;border-radius: 2px;background-color: #fff;display: inline-block;height: 30px;line-height: 30px;background-image: none;
    box-sizing: border-box;color: #606266;font-size: inherit;outline: none;padding: 0 8px;transition: border-color .2s cubic-bezier(.645,.045,.355,1);}
.selectBySys{width: 168px;}
.preTtl{margin-left: 40px;}
.preTtl > span{float: left;line-height: 32px;}
.branceTitle{font-size: 20px;}
.item_name{font-size: 16px;font-weight: bold;}
.xing:before {content: "*";width: 5px;height: 5px;display: inline-block;color: red;font-weight: bolder;position: relative;}
.preTtl .tipMsg{font-size: 12px;line-height: 14px;}
.con-txt{padding-left: 10px;  margin-bottom: 12px;}
.clearInputVal {position: absolute;  bottom: 0;right: 4px; width: 10px;cursor: pointer; color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
input:focus + i.clearInputVal{  display: block;  }
.abPos{position: relative;}

/*批量导入*/
#leading .bounceMainCon{
    width: 450px;
    margin: 0 auto;
}
.customSelect{
    position: relative;
}
.inputDrop{
    display: none;
    position: absolute;
    left: 0;
    top: 33px;
    border: 1px solid #939393;
    z-index: 1;
    background: #fff;
    width: 100%;
    box-shadow: 0 0 4px #cacaca;
}
.inputDrop option{
    padding: 4px 8px;
}
.inputDrop option:hover{
    background: #eee;
}
.exportStep {  padding-left: 40px;  }
.stepItem {  margin-top: 20px;  }
.flexRow{  display: flex;  justify-content:space-between;  width: 356px; align-items: baseline; margin-left: 56px; }
.fileFullName{  width: 278px; background: #fff;text-align: center; flex: auto; line-height: 32px; }
.formItem{  position: relative; margin-top: 8px;  overflow: hidden;  clear: both;  }
.narrowLamp{width: 70%;min-width: 650px;}
.userForm{width: 300px;  margin: 0 auto 20px;}
.clearBtn{position: absolute;  right: 38px;  top: 4px;  width: 14px;  height: 30px;font-style: normal;  color: #b0b0b0;  cursor: pointer;}
.narrowBody{margin: 0 auto;width: 80%;}
.left{  display: inline-block;  width: 80px;  }

