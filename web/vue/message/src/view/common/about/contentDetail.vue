<template>
  <div id="contentDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="place">
        <div class="placeContainer">
          <p class="fileTtl" v-text="aboutFile.name"></p>
        </div>
      </div>
      <div class="place" v-if="params.mark === 'approveLast'">
        <div class="placeContainer">
          <div class="line-p">
            <span>审批记录</span>
          </div>
          <div class="line-p">
            <div class="script">
              <div v-if="approveRecord.length > 0">
                <span class="process_desName">申请人</span>
                <span class="process_name">{{approveRecord[0].userName}}</span>
                <span>{{approveRecord[0].createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
              </div>
              <div v-for="(item, index) in approveRecord" :key="index">
                <div v-if="item.approveStatus === '2'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                </div>
                <div v-if="item.approveStatus === '3'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red" v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red">
                    <span>驳回理由</span>
                    <span>{{item.approveMemo}}</span>
                  </div>
                </div>
              </div>
              <span v-if="params.mark !== 'query'">等待 <span class="font-orange">{{toUserName}}</span> 审批</span>
            </div>
          </div>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_error" value="驳回"  @click="approveFile(0)"/>
            <input type="submit" class="ui-btn ui-btn_info" value="批准"  @click="approveFile(1)"/>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell" v-text="aboutFile.fileSn"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell">{{aboutFile.allCategoryName}}</div>
          </div>
          <div class="line-p">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{aboutFile.versionNo}}</div>
          </div>
          <div class="line-p" v-if="aboutFile.versionNo === 0">
            <div class="tt-cell">说明</div>
            <div class="con-reason" v-text="aboutFile.description" :title="aboutFile.description"></div>
          </div>
          <div class="line-p" v-if="aboutFile.versionNo > 0">
            <div class="tt-cell">换版原因</div>
            <div class="con-reason" v-text="aboutFile.reason" :title="aboutFile.reason"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">内容</div>
            <div class="con-reason"><span class="color-blue-a" @click="seeDetail()">查看</span></div>
          </div>
        </div>
      </div>
      <div class="place" v-if="params.mark !== 'approveLast'">
        <div class="placeContainer">
          <div class="line-p">
            <span>审批记录</span>
          </div>
          <div class="line-p">
            <div class="script">
              <div v-if="approveRecord.length > 0">
                <span class="process_desName">申请人</span>
                <span class="process_name">{{approveRecord[0].userName}}</span>
                <span>{{approveRecord[0].createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
              </div>
              <div v-for="(item, index) in approveRecord" :key="index">
                <div v-if="item.approveStatus === '2'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                </div>
                <div v-if="item.approveStatus === '3'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red" v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red">
                    <span>驳回理由</span>
                    <span>{{item.approveMemo}}</span>
                  </div>
                </div>
              </div>
              <span v-if="params.mark.substr(0,5) !== 'query'">等待 <span class="font-orange">{{toUserName}}</span> 审批</span>
            </div>
            <!--<div class="script">-->
              <!--<div>-->
                <!--<span class="sm-ttl">申请人：</span>-->
                <!--<span class="sm-con" v-text="item.userName"></span>-->
                <!--<span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>-->
              <!--</div>-->
              <!--<div v-for="(item ,index) in approveRecord" :key="index">-->
                <!--<div>-->
                  <!--<span class="sm-ttl">审批人：</span>-->
                  <!--<span class="sm-con" v-text="item.userName"></span>-->
                  <!--<span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>-->
                <!--</div>-->
                <!--<div v-if="index === approveRecord.length-1">-->
                <!--<span v-if="params.mark !== 'query'">等待-->
                  <!--<span class="font-orange" v-if="item.toUserName">{{item.toUserName}}</span>-->
                  <!--<span class="font-orange" v-else>文管</span>-->
                  <!--审批-->
                <!--</span>-->
                <!--</div>-->
              <!--</div>-->
            <!--</div>-->
          </div>
          <div class="script"  v-if="params.mark === 'approve'">
            <el-form :rules="rules" :model="selectRule" class="handleFile">
              <el-form-item prop="radio">
                <el-radio-group v-model="selectRule.radio">
                  <el-radio :label="1">本人无异议，选择下一个审批人</el-radio>
                  <el-form-item v-show="selectRule.radio == '1'" class="approverNext" size="mini">
                    <el-select border v-model="selectRule.nextApprover" placeholder="选择下一位审批人" @change="nextAp">
                      <el-option
                        v-for="item in approverSelect"
                        :key="item.userID"
                        :label="item.userName"
                        :value="item.userID">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-radio :label="2" v-if="aboutFile.versionNo">本人无异议，文件可正式换版</el-radio>
                  <el-radio :label="2" v-else>本人无异议，文件可正式发布</el-radio>
                  <br/>
                  <el-radio :label="3">驳回该申请</el-radio>
                  <el-input v-show="selectRule.radio == '3'" type="textarea" v-model="opposeForm.reason" placeholder="在此不输入内容，也可点击确定。"></el-input>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
          <div class="handle_button text-center" v-if="params.mark === 'apply'">
            <input type="submit" class="ui-btn ui-btn_info" value="终止发布申请" :disabled="stopControl" @click="dialogVisible = true"/>
          </div>
          <div class="handle_button text-center" v-if="params.mark === 'update'">
            <input type="submit" class="ui-btn ui-btn_info" value="终止换版申请" :disabled="stopControl" @click="dialogVisible = true"/>
          </div>
          <div class="handle_button" v-if="params.mark === 'approve' || params.mark === 'approveLast'">
            <input type="submit" :class="{'ui-btn ui-btn_info': !btnControl, 'ui-btn ui-btn_disabled': btnControl}" :disabled="btnControl" value="确定" @click="toHandle()"/>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title=" ! 提示"
      :visible.sync="dialogVisible"
      width="80%" custom-class="def-danger">
      <p class="handle-center" v-if="params.mark === 'apply'">确定终止本次发布申请？</p>
      <p class="handle-center" v-if="params.mark === 'update'">确定终止本次换版申请？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fc-btn-normal" value="取消" @click="dialogVisible = false">
          <input type="submit" class="fc-btn ui-btn_error" value="确定" @click="stopFileProcess">
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title=" ! 提示"
      :visible.sync="opposeMsg"
      width="80%" custom-class="def-danger">
      <el-form ref="form" label-width="80px" :label-position="labelPosition">
        <el-form-item label="驳回理由">
          <el-input type="textarea" v-model="opposeForm.reason" placeholder="在此不输入内容，也可点击确定。"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fc-btn-normal" value="取消" @click="opposeMsg = false">
          <input type="submit" class="fc-btn ui-btn_error" value="确定" @click="approveIssueFile(3)">
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title=" ! 提示"
      :visible.sync="approveMsg"
      width="80%">
      <p class="handle-center" v-if="aboutFile.versionNo">您确定批准本次内容换版申请吗？</p>
      <p class="handle-center" v-else>您确定批准本次内容发布申请吗？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fd-btn fc-btn-yellow" value="取消" @click="approveMsg = false">
          <input type="submit" class="fd-btn fc-btn-green" value="确定" @click="approveIssueFile(2)">
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title=" ! 提示"
      :visible.sync="approveSure"
      width="80%" custom-class="def-danger">
      <p v-if="aboutFile.versionNo" class="handle-center">本次内容换版申请<span class="color-red">未经任何人审批。</span></p>
      <p v-else  class="handle-center">本次内容发布申请<span class="color-red">未经任何人审批。</span></p>
      <p  class="handle-center">您确定批准吗?</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fd-btn fc-btn-yellow" value="取消" @click="approveSure = false">
          <input type="submit" class="fd-btn fc-btn-green" value="确定" @click="approveIssueFile(2)">
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<style scoped="">
  .color-blue-a{
    cursor: pointer;
    color: #4797f1;
  }
  .color-blue-a:hover{
    text-decoration: underline;
  }
</style>

<script>
  import { gohistory } from '@/js/common'
  export default {
    name: 'contentDetail',
    data () {
      return {
        title: '内容发布申请',
        params: {},
        loading: true,
        isNotCharged: true,
        dialogVisible: false,
        pendingDialog: false,
        tipVisible: false,
        categoryName: '',
        aboutFile: {},
        approveRecord: [],
        toUserName: '',
        listenersUid: [],
        selectRule: {
          radio: '',
          nextApprover: ''
        },
        rules: {
          radio: [
            { required: true, message: '请选择下一下一个审批人', trigger: 'change' }
          ]
        },
        approverSelect: [],
        opposeForm: {
          reason: ''
        },
        selectedAp: {},
        lastApprover: {},
        msgDialog: false,
        opposeMsg: false,
        approveMsg: false,
        approveSure: false
      }
    },
    computed: {
      stopControl () {
        let len = this.approveRecord.length - 1
        if (len > 0) {
          if (this.approveRecord[len].approveStatus === '3' || this.approveRecord[len].approveStatus === '5' || (this.approveRecord[len].approveStatus === '2' && this.approveRecord[len].toMid === 'rb')) {
            return true
          }
        }
        return false
      }
    },
    created: function () {
      let that = this
      let fileId = this.$route.params.id
      this.axios.post('../../../about/fileHistorySingleByAbout.do', {
        id: fileId
      })
        .then(function (response) {
          let data = response.data.data
          that.loading = false
          console.log(response)
          that.approveRecord = data.listAp
          that.toUserName = data.listAp[data.listAp.length - 1].toUserName || '文管'
          that.aboutFile = data.aboutFileHistory
          if (data.listAp.length > 0) {
            let len = data.listAp.length - 1
            that.lastApprover = {
              'processId': data.listAp[len].id,
              'level': data.listAp[len].level
            }
          }
        })
        .catch(function (error) {
          console.log(error)
        })


      this.params = this.$route.params
      switch (this.params.mark) {
        case 'apply':
        case 'query1':
          this.title = '内容发布申请'
          break
        case 'update':
        case 'query2':
          this.title = '内容换版申请'
          break
        case 'approve':
        case 'approve2':
        case 'query3':
          this.title = '内容审批'
          break
        case 'approveLast':
        case 'query4':
          this.title = '内容发布/换版审批'
          break
        default:
          this.title = '内容发布申请'
      }
      if (this.params.mark === 'approve') {
        this.axios.post('../../../res/getAllOidUserByResource.do', {
          aboutId: fileId
        })
          .then(function (response) {
            that.approverSelect = response.data.data.list
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    methods: {
      stopFileProcess () {
        this.loading = true
        var userId = this.sphdSocket.user.userID
        var fileId = this.$route.params.id
        var processLength = this.approveRecord.length * 1 - 1
        var processId = this.approveRecord[processLength].id
        this.sphdSocket.send('stopFileProcess', { 'userID': userId, 'hisId': fileId, 'processId': processId })
        let that = this
        this.$http.post('../../../about/aboutStopFileProcess.do', { hisId: fileId, processId: processId }, {
          emulateJSON: true
        }).then((response) => {
          that.loading = false
          let reqData = response.body.data
          let state = reqData.state
          if (state === 1) {
            that.$kiko_message('终止成功！')
            gohistory(that)
          } else if (state === 2) {
            that.$kiko_message('请勿重复操作！')
          }
        }).catch(function () {
          that.loading = false
          that.$message({
            type: 'error',
            message: '系统错误，请重试！'
          })
        })

      },
      toHandle () {
        let fileId = this.$route.params.id
        let fileData = {
          file: fileId,
          processId: this.lastApprover.processId, // 审批流程的id
        }
        if (this.selectRule.radio === 1) {
          fileData.toUser = this.selectedAp.userID
          fileData.toUserName = this.selectedAp.userName
          fileData.approveStatus = '2'
          fileData.type = '1' // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
        } else if (this.selectRule.radio === 2) {
          fileData.type = '2'
          fileData.approveStatus = '2'
          if (fileData.toUser || fileData.toUserName) {
            delete fileData.toUser
            delete fileData.toUserName
          }
        } else if (this.selectRule.radio === 3) {
          fileData.approveMemo = this.opposeForm.reason
          fileData.approveStatus = '3'
          if (fileData.toUser || fileData.toUserName) {
            delete fileData.toUser
            delete fileData.toUserName
          }
        }
        let that = this
        this.loading = true
        this.axios.post('../../../about/aboutHandleFile.do', fileData)
          .then(function (response) {
            console.log(response)
            let state = response.data.data.state
            that.loading = false
            if (state === 1) {
              that.$kiko_message('操作成功！')
              gohistory(that)
            } else if(state === 2) {
              that.$kiko_message("请勿重复操作！")
            } else {
              that.$kiko_message('操作失败！')
            }
          })
          .catch(function (error) {
            console.log(error)
          })
      },
      approveFile: function (type) {
        if (type === 0) {
          this.opposeMsg = true
        } else {
          if (this.approveRecord.length > 1) {
            this.approveMsg = true
          } else {
            this.approveSure = true
          }
        }
      },
      approveIssueFile (option) { // option:2-同意 3-驳回
        let fileId = this.$route.params.id
        this.loading = true
        let fileData = {
          file: fileId,
          processId: this.lastApprover.processId, // 审批流程的id
          approveStatus : option,
          type: 3
        }
        if (option === 3) {
          fileData.approveMemo = this.opposeForm.reason
        }
        let that = this
        this.loading = true
        this.axios.post('../../../about/aboutHandleFile.do', fileData)
          .then(function (response) {
            that.loading = false
            let state = response.data.data.state
            if (state === 1) {
              that.$kiko_message('操作成功！')
              gohistory(that)
            } else if(state === 2) {
              that.$kiko_message("请勿重复操作！")
            } else {
              that.$kiko_message('操作失败！')
            }
          })
          .catch(function (error) {
            console.log(error)
          })
      },
      nextAp (vId) {
        this.selectedAp = this.approverSelect.find((item) => {
          return item.userID === vId // 筛选出匹配数据
        })
      },
      screen: function () {
        this.$router.push({
          path: `/contentApplyQuery`
        })
      },
      seeDetail: function () {
        let id = this.$route.params.id
        this.$router.push({
          path: `/contentShow/${id}`
        })
      }
    }
  }
</script>
