<template>
  <div id="reimburseApplyQuery" class="reimburseQuery">
    <TY_NavTop title="报销申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approveStatus" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div>
              <h4 class="item-header">一级费用类别</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.feeCatAll" size="small">
                  <el-radio label="0" border>全部</el-radio>
                  <el-radio label="1" style="margin-left: 70px">选择一级费用类别 <i class="el-icon-arrow-right"></i></el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.feeCatAll === '1'">
                <el-radio-group v-model="queryForm.feeCat" size="small">
                  <el-radio :label="item.id" border v-for="item in feeCat" v-bind:key="item.id" @change="setSecondFeeCat">{{item.name}}</el-radio>
                </el-radio-group>
              </div>
              <div v-show="secondFeeCatList.length > 0 && queryForm.feeCatAll === '1'">
                <div class="el-form-item">
                  <el-radio-group v-model="queryForm.secondFeeCatAll" size="small">
                    <el-radio label="0" border>全部</el-radio>
                    <el-radio label="1" style="margin-left: 70px">选择二级费用类别 <i class="el-icon-arrow-right"></i></el-radio>
                  </el-radio-group>
                </div>
                <div class="el-form-item" v-show="queryForm.secondFeeCatAll === '1'">
                  <el-radio-group v-model="queryForm.secondFeeCat" size="small">
                    <el-radio :label="item.id" border v-for="item in secondFeeCatList" v-bind:key="item.id">{{item.name}}</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
            <div>
              <h4 class="item-header">票据种类</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.billCatAll" size="small">
                  <el-radio label="0" border>全部</el-radio>
                  <el-radio label="1" style="margin-left: 100px">选择票据种类</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.billCatAll === '1'">
                <el-radio-group v-model="queryForm.billCat" size="small">
                  <el-radio :label="item.id" border v-for="item in billCat" v-bind:key="item.id">{{item.name}}</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div class="handle_button" style="margin-top: 20px">
              <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit" v-loading.fullscreen.lock="loading">
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { formatDate, filterLeaveType } from '../../js/common'
export default {
  name: 'applyReimburseQuery',
  data () {
    return {
      loading: true,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approveStatus: '2',
        feeCat: '',
        secondFeeCat: '',
        billCat: '',
        billCatAll: '0',
        feeCatAll: '0',
        secondFeeCatAll: '0'
      },
      billCat: [],
      feeCat: [],
      secondFeeCatList: []
    }
  },
  created: function () {
    let that = this
    this.axios.get('../../../reimburse/toCode.do')
      .then(function (response) {
        let feeCats = response.data.codeList
        console.log(response.data)
        that.feeCat = feeCats
        that.loading = false
      })
      .catch(function (error) {
        console.log(error)
      })
    this.axios.get('../../../expense/queryCodeCategory.do')
      .then(function (response) {
        let billCats = response.data.billCats
        console.log(response.data)
        that.billCat = billCats
        that.loading = false
      })
      .catch(function (error) {
        console.log(error)
      })
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      // feeCat 费用类别id
      // feeCatName 费用类别名称
      // billCat 票据种类 id
      // billCatName 票据种类名称
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        approveStatus: this.queryForm.approveStatus,
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        type: this.queryForm.type,
        userId: this.sphdSocket.user.userID
      }
      // 赋值票据类型id和name
      if (this.queryForm.billCatAll === '0') {
        queryParam.billCat = 0
      } else {
        let obj = this.billCat.find((item) => {
          return item.id === this.queryForm.billCat
        })
        queryParam.billCatName = obj.name
        queryParam.billCat = obj.id
      }
      // 赋值费用类别id和name（一级和二级）
      if (this.queryForm.feeCatAll === '0') {
        queryParam.feeCat = 0
      } else {
        let obj1 = this.feeCat.find((item) => {
          return item.id === this.queryForm.feeCat
        })
        if (this.queryForm.secondFeeCatAll === '0' || !this.queryForm.secondFeeCat) {
          queryParam.feeCat = this.queryForm.feeCat
          queryParam.feeCatName = obj1.name
        } else {
          queryParam.feeCat = this.queryForm.secondFeeCat
          let obj2 = this.secondFeeCatList.find((item) => {
            return item.id === this.queryForm.secondFeeCat
          })
          queryParam.feeCatName = obj1.name + '-' + obj2.name
        }
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/applyReimburseQueryPer',
        name: 'applyReimburseQueryPer',
        params: {
          data: queryParam
        }
      })
    },
    setSecondFeeCat: function (val) {
      this.loading = true
      let that = this
      this.$http.post('../../../reimburseWindow/getSecondCodeList.do', { id: val }, {
        emulateJSON: true
      }).then((response) => {
        that.loading = false
        let data = response.body
        if (data) {
          let data = response.data
          console.log(data)
          this.secondFeeCatList = data
        } else {
          console.log('加载失败！')
        }
      })
    }
  }
}
</script>
