<template>
  <!--  <p>公司现有空白的增值专用发票XX张，具体如下：</p>-->
  <div class="box">
    <div class="sticky">
      <p class="padding-0-15 ty-fff" style="font-size: 14px;line-height: 35px">公司现有空白的增值税专用发票{{ cou }}张，具体如下：</p>
      <div class="cai-liGray" style="height: 16px"></div>
    </div>
    <div>
      <ul>
        <li v-for="item in list1" :key="item.id">
          <div>
            <p>发票号码{{ item.id }}</p>
            <p>
              申领
              <span class="padding-0-10">{{ item.name }}</span>
              <span class="padding-0-10">{{ item.date }}</span>
            </p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: "otherInvoices",
  data() {
    return {
      cou: 1234,
      list1: [
        {id: 123456, name: "好人", date: "2000-01-01"},
        {id: 111111, name: "坏人", date: "2000-01-01"},
        {id: 666666, name: "人", date: "2000-01-01"},
        {id: 222222, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
        {id: 333333, name: "好人", date: "2000-01-01"},
      ]
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/account.scss";
@import "src/style/common.scss";

ul {
  li {
    display: flex;
    justify-content: space-between;
    padding: 5px 15px;
    background-color: #ffffff;
    align-items: center;

    P {
      line-height: 22px;
    }
  }

  > li:nth-of-type(even) {
    background-color: $tyBounceColorGray;
  }
}

.box {
  background-color: $tyColorBg;

}

</style>