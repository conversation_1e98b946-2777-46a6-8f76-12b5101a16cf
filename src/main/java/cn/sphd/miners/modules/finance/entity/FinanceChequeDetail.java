package cn.sphd.miners.modules.finance.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_finance_cheque_detail")
public class FinanceChequeDetail implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="cheque_no"  , length=50 , nullable=true , unique=false)
    private String chequeNo;  //支票号

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//1-转帐支票，2-现金汇票

    @Column(name="account"  , length=100 , nullable=true , unique=false)
    private String account;  //账号

    @Column(name="bank_no"  , length=100 , nullable=true , unique=false)
    private String bankNo;  //银行编号

    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName;  //银行名称

    @Column(name="category"  , length=1 , nullable=true , unique=false)
    private String category;  //种类:1-采购,2-报销

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;//1-未使用,2-已使用,3-作废

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;  //金额

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;  //票面金额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary; //摘要

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;  //用途

    @Column(name="expire_date"   , nullable=true , unique=false)
    private Date expireDate;  //支票到期日

    @Column(name="operate_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date operateDate;  //发生日期

    @Column(name="receive_corp"  , length=100 , nullable=true , unique=false)
    private String receiveCorp;  //收款单位

    @Column(name="receive_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date receiveDate;  //接收日期

    @Column(name="receiver"  , length=100 , nullable=true , unique=false)
    private String receiver;  //接收经手人

    @Column(name="operator"  , length=100 , nullable=true , unique=false)
    private String operator;  //支付经手人

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;  //审批次级

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;  //审批者ID

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName; //审批者

    @Column(name="audit_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date auditDate;  //审批日期

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;  //申请备注

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;  //审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="financial_handling"   , nullable=true , unique=false)
    private String financialHandling;//财务经手人

    @Column(name = "modity_status")
    private String modityStatus;  //数据修改状态  null/1-未修改  2-已修改(每条数据仅能修改一次)

    @Column(name="myself_id"   , nullable=true , unique=false)
    private Integer myselfId;  //数据修改时，对应修改前支票的id(本表)【如果不为空，说明此条数据是由原来的支票进行修改后生成的与原来支票信息基本一致的数据】

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;//修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;//版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="cheque_reg", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceCheque chequeReg;

    @Column(name="cheque_reg"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer chequeReg_;

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="account_id", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceAccount accountId;

    @Column(name="account_id"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer accountId_;


    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceAccountBill.class, fetch= FetchType.LAZY, mappedBy="cheque", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceAccountBill> financeAccountBillHashSet = new HashSet<FinanceAccountBill>();

    @Transient
    private String dateType;  //支票的用途  1-初始金额冲减,2-转帐交易,3-收入,4-支出

    @Transient
    private String billCat;  // 票据类型

    @Transient
    private Integer billQuantity;  //票据数量

    @Transient
    private String billPeriod;  //1-本月票据,2-非本月票据

    @Transient
    private String isBasic;  //是否为基本户

    @Transient
    private String subType;   //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出

    @Transient
    private Date billDate;  //票据日期

    @Transient
    private Date factDate;  //票据日期

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    public String getBillCat() {
        return billCat;
    }

    public void setBillCat(String billCat) {
        this.billCat = billCat;
    }

    public String getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(String billPeriod) {
        this.billPeriod = billPeriod;
    }

    public String getDateType() {
        return dateType;
    }

    public void setDateType(String dateType) {
        this.dateType = dateType;
    }

    public String getIsBasic() {
        return isBasic;
    }

    public void setIsBasic(String isBasic) {
        this.isBasic = isBasic;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getReceiveCorp() {
        return receiveCorp;
    }

    public void setReceiveCorp(String receiveCorp) {
        this.receiveCorp = receiveCorp;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public FinanceCheque getChequeReg() {
        return chequeReg;
    }

    public void setChequeReg(FinanceCheque chequeReg) {
        this.chequeReg = chequeReg;
    }

    public Integer getChequeReg_() {
        return chequeReg_;
    }

    public void setChequeReg_(Integer chequeReg_) {
        this.chequeReg_ = chequeReg_;
    }

    public Set<FinanceAccountBill> getFinanceAccountBillHashSet() {
        return financeAccountBillHashSet;
    }

    public void setFinanceAccountBillHashSet(Set<FinanceAccountBill> financeAccountBillHashSet) {
        this.financeAccountBillHashSet = financeAccountBillHashSet;
    }

    public FinanceAccount getAccountId() {
        return accountId;
    }

    public void setAccountId(FinanceAccount accountId) {
        this.accountId = accountId;
    }

    public Integer getAccountId_() {
        return accountId_;
    }

    public void setAccountId_(Integer accountId_) {
        this.accountId_ = accountId_;
    }

    public String getFinancialHandling() {
        return financialHandling;
    }

    public void setFinancialHandling(String financialHandling) {
        this.financialHandling = financialHandling;
    }

    public String getModityStatus() {
        return modityStatus;
    }

    public void setModityStatus(String modityStatus) {
        this.modityStatus = modityStatus;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Integer getMyselfId() {
        return myselfId;
    }

    public void setMyselfId(Integer myselfId) {
        this.myselfId = myselfId;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public Date getFactDate() {
        return factDate;
    }

    public void setFactDate(Date factDate) {
        this.factDate = factDate;
    }
}
