<template>
<!--  <div id='player' style='width:100%;height:100%;text-align:center'>-->
<!--&lt;!&ndash;-->

<!--    <video ref="video" id='video' controls preload style='width:80%;height:500px;' crossorigin='anonymous' playsinline='' webkit-playsinline='' x5-video-player-type='h5' >-->
<!--      <source src="https://dvm05.btransmission.com/upload/org/4113/202312/03a0e7aa8752492f832cbc2e34281611.mp3">-->
<!--      <track id='track' src='https://dvm05.btransmission.com/lyric/createSongRhythm.do?accId=2299' srclang='zh-cn' label='简体中文' kind='subtitles' default >-->
<!--    </video>-->
<!--&ndash;&gt;-->


<!--    <video id='video' ref="video" controls preload style='width:80%;height:500px;' crossorigin='anonymous' playsinline='' webkit-playsinline='' x5-video-player-type='h5' >-->
<!--      <source :src="playUrl" v-if="playUrl">-->
<!--      <track id='track' v-if="rhythmSrc" :src='rhythmSrc' srclang='zh-cn' label='简体中文' kind='subtitles' default >-->
<!--    </video>-->



<!--    <div id='display'>-->
<!--    </div>-->
<!--  </div>-->
</template>
<!--<script lang="js">-->
<!--import qs from 'qs'-->
<!--import { createSong, sendToServer } from '@/api/api.js'-->

<!--export default {-->
<!--  name: "audio2",-->
<!--  data() {-->
<!--    return {-->
<!--      video:'',-->
<!--      playName:'',-->
<!--      lycTxtInfo:'',-->
<!--      playUrl:'',-->
<!--      txtUrl:'',-->
<!--      rhythmSrc:'',-->
<!--    };-->
<!--  },-->
<!--  components: {-->
<!--  },-->
<!--  created(){-->
<!--    let params = qs.parse(location.search, { ignoreQueryPrefix: true })-->
<!--    this.type = Number(params.type)-->
<!--    this.accId = Number(params.accId)-->

<!--    console.log('this.accID', this.accId)-->
<!--    console.log('this.type', this.type)-->
<!--    this.rhythmSrc = 'https://dvm05.btransmission.com/lyric/createSongRhythm.do?accId=' + this.accId-->

<!--    this.getSongDetails()-->

<!--  },-->
<!--  methods: {-->
<!--    closePlayLyc (val) {-->
<!--      console.log('父级的 closePlayLyc')-->
<!--      let timer = localStorage.getItem('timer')-->
<!--      console.log('取消 clearInterval(t', timer)-->
<!--      clearInterval(timer)-->
<!--    },-->
<!--    getSongDetails(){-->
<!--      let that = this-->
<!--      let data = { 'accId': this.accID }-->
<!--      createSong(data).then(res => {-->
<!--        console.log('createSong okok=', res)-->
<!--        let data = (res.data && res.data.data) || ''-->
<!--        if(data){-->
<!--          let song = data.song-->
<!--          let playU = ''-->
<!--          if(this.type === 1){ // 1-伴奏，2-原唱-->
<!--            playU = song.bgmPath-->
<!--          }else if(this.type === 2){-->
<!--            playU = song.originalPath-->
<!--          }else {-->
<!--            console.log('无法获取播放类型')-->
<!--          }-->
<!--          if(playU) this.playUrl = 'https://dvm05.btransmission.com' + '/upload/' + playU-->
<!--          this.playName = song.name-->





<!--          // let video0 = document.getElementById('video')-->
<!--          // console.log('video0=', video0)-->
<!--          // let track = document.getElementById('track')-->
<!--          let video = this.$refs.video-->
<!--          that.video = this.$refs.video-->
<!--          console.log('video=', that.video)-->
<!--          // $.webRoot = 'https://dvm05.btransmission.com'-->
<!--          let videoInfoEvent = 'loadedmetadata'-->
<!--          if(video.canPlayType('application/vnd.apple.mpegurl')) {-->
<!--            videoInfoEvent = 'canplay'-->
<!--          }-->
<!--          video.addEventListener(videoInfoEvent, function () {-->
<!--            console.log(videoInfoEvent, video.currentTime)-->
<!--            let timeMillis = Math.round(video.currentTime * 1000)-->
<!--            that.createLyricLive(timeMillis)-->
<!--          })-->
<!--          video.addEventListener('play', function () {-->
<!--            console.log('play', video.currentTime)-->
<!--            let timeMillis = Math.round(video.currentTime * 1000)-->
<!--            that.playLiveTime(timeMillis)-->
<!--          })-->
<!--          video.addEventListener('playing', function () {-->
<!--            console.log('playing', video.currentTime)-->
<!--            let timeMillis = Math.round(video.currentTime * 1000)-->
<!--            that.playLiveTime(timeMillis)-->
<!--          })-->
<!--          video.addEventListener('pause', function () {-->
<!--            console.log('pause')-->
<!--            that.stopLiveTime()-->
<!--          })-->
<!--          video.addEventListener('waiting', function () {-->
<!--            console.log('waiting')-->
<!--            that.stopLiveTime()-->
<!--          })-->
<!--          video.addEventListener('seeked', function () {-->
<!--            console.log('seeked', video.currentTime)-->
<!--            let timeMillis = Math.round(video.currentTime * 1000)-->
<!--            that.seekLiveTime(timeMillis)-->
<!--          })-->



<!--        }-->







<!--      }).catch(err => {-->

<!--      })-->

<!--    },-->

<!--    createLyricLive(timeMillis) {-->
<!--      sendToServer('/lyric/createLyricLive.do',{-->
<!--        accId : this.accID,-->
<!--        timeMillis : timeMillis-->
<!--      })-->
<!--    },-->
<!--    playLiveTime(timeMillis) {-->
<!--      sendToServer('/lyric/playLiveTime.do',{-->
<!--        accId : this.accID,-->
<!--        timeMillis : timeMillis-->
<!--      })-->
<!--    },-->
<!--    stopLiveTime() {-->
<!--      sendToServer('/lyric/stopLiveTime.do',{-->
<!--        accId : this.accID,-->
<!--      })-->
<!--    },-->
<!--    seekLiveTime(timeMillis) {-->
<!--      sendToServer('/lyric/seekLiveTime.do',{-->
<!--        accId : this.accID,-->
<!--        timeMillis : timeMillis-->
<!--      })-->
<!--    }-->



<!--  },-->
<!--}-->
<!--</script>-->
<!--<style type='text/css'>-->
<!--body{-->
<!--  display: flex;-->
<!--  justify-content: center;-->
<!--}-->
<!--#display{-->
<!--  position:absolute;-->
<!--  display:block;-->
<!--  writing-mode: vertical-rl;-->
<!--  text-align :center;-->
<!--  color:yellow;-->
<!--  font-size:24px;-->
<!--  font-family:Comic Sans MS;-->
<!--  text-shadow: 0.1em 0.1em 0.15em #333;-->
<!--  z-index:100; /* set z-index to be sure div is on top */-->
<!--}-->
<!--</style>-->
<!--<style id='videoCue' type='text/css'>-->
<!--video {-->
<!--  border: 1px solid #aaa;-->
<!--  object-fit: initial;-->
<!--}-->
<!--::cue {-->
<!--  color:blue;-->
<!--  font-size:24px;-->
<!--  line-height:100px;-->
<!--  background-color:rgba(0,0,0,0) !important;-->
<!--}-->
<!--</style>-->