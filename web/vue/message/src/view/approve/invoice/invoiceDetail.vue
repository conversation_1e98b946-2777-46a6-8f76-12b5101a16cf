<template>
  <div id="openInvoiceSD">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container"  v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="sendContainer">
          <div><span>客户名称：</span><span>{{info.name}}</span></div>
          <div><span>需要发票  {{info.invoice_count}} <span v-if="info.invoice_count !== '--'">张</span></span><span class="wid"></span><span>总额{{info.invoice_amount | numberFilter(2)}}元</span></div>
          <div class="yard gapTop" v-if="info.create_name"><span>申请人：{{info.create_name}}</span><span>{{info.create_date | formatDate}}</span></div>
          <div class="yard" v-if="info.update_name"><span>审批人：{{info.update_name}}</span><span>{{info.update_date | formatDate}}</span></div>
          <div class="yard" v-if="info.state==5"><span>开票人：{{info.auditor_name}}</span><span>{{info.audit_date | formatDate}}</span></div>
          <div v-if="info.state == 2 || info.state == 4">
            <div><span class="red">！重要提示</span></div>
            <p><span class="red">目前条件下</span>，本申请中的发票如开具，公司缴纳增值税的参考金额{{info.attach}}元（附加税未计入），企业所得税的参考金额为{{info.company}}元。</p>
            <p v-if="info.state == 2">您可批准本申请。批准后财务将收到信息，并需开具发票。</p>
            <p v-if="info.state == 2">您也可“暂缓开票”。应缴税金随时可能变化。暂缓开票后请随时进入系统测算应缴税金，<span class="red">并在合适时批准本申请</span>。</p>
            <p style="height: 10px;"></p>
          </div>
        </div>
      </div>
      <p style="height: 10px;"></p>
      <div class="centerBtn" v-if="info.state == 2">
        <input type="submit" :disabled="isOk" class="ui-btn ui-btn_info" value="暂缓开票" @click="changeApplyBtn(4)">
        <input type="submit" :disabled="isOk" class="ui-btn ui-btn_info" value="批准" @click="changeApplyBtn(3)">
      </div>
      <div class="centerBtn" v-if="info.state == 4">
        <p style="height: 20px;"></p>
        <input type="submit" :disabled="isOk" class="ui-btn ui-btn_info" value="批准" @click="changeApplyBtn(3)">
      </div>
      <table class="table">
        <thead>
        <td>金额</td>
        <td>发票种类</td>
        <td v-if="info.state == 3 || info.state == 5">发票号码</td>
        <td v-if="info.state == 3 || info.state == 5">开票日期</td>
        <td>行数</td>
        </thead>
        <tbody>
        <tr @click="toInfo(item.id, applicationId, item.fid)" v-for="(item, index) in list" v-bind:key="index">
          <td>{{item.amount | numberFilter(2)}}</td>
          <td>{{item.invoice_category | filterInvoiceType}}</td>
          <td v-if="info.state == 3 || info.state == 5">{{item.invoice_no}}</td>
          <td v-if="info.state == 3 || info.state == 5">{{item.operate_date | formatDate('yyyy/MM/dd')}}</td>
          <td>{{item.line}}</td>
        </tr>
        </tbody>
      </table>
    </div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="97%" :before-close="handleClose">
      <el-form :label-position="left" label-width="70px">
          <p class="txt"> 暂缓开票后，<span class="red">切勿忘记</span>合适时，需要由<span class="red">您在系统里再次审批</span>，财务才能收到开票通知。</p>
          <p>确定暂缓开票？</p>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value="取消" @click="dialogVisible = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定"  @click="changeApply()">
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="less">
  #openInvoiceSD{
    .table td{ font-size: 12px; }
    .wid{ display: inline-block; width: 30px; }
    .txt{ text-indent:2em; }
    .red{ color:#ed5565;  }
    .gapTop{padding-top: 10px;  }
    .yard{
      text-align: right;
      span:first-child{
        display: inline-block;
        width: 120px;
      }
    }
    .centerBtn{
      text-align: center;
      .ui-btn{
        display: inline-block;
        border-radius: 15px;
        width: 70%;
        height: auto;
        line-height:1;
        vertical-align: middle;
        padding:8px 0 ;
      }
    }
  }

</style>
<script>
import { formatDate, filterInvoiceType, numberFilter } from '../../../js/common'
export default {
  name: 'approvalInvoiceDetail',
  data () {
    return {
      loading: true,
      isOk: false,
      dialogVisible: false,
      changeType: 0,
      reason: '',
      applicationId: '',
      list: [],
      listeners: [],
      info: {}
    }
  },
  filters: {
    formatDate,
    numberFilter,
    filterInvoiceType
  },
  created () {
    // 订阅更改状态
    let _this = this
    _this.applicationId = this.$route.params.id
    _this.listeners = [
      // 暂缓开票
      _this.sphdSocket.subscribe('reprieve', _this.subscribeCallBack, null, 'user'),
      // 已批准待开票
      _this.sphdSocket.subscribe('applyInvoice', _this.subscribeCallBack, null, 'user'),
      // 已开票未登记
      _this.sphdSocket.subscribe('invoiceRegister', _this.subscribeCallBack, null, 'user'),
      // 填完发票号的回调
      _this.sphdSocket.subscribe('invoiceHandle', _this.handleCallback, null, 'user')
    ]
    // 获取申请详情
    _this.getDetails()
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    chargeTitle: function (sta) {
      let str = '开票待审批'
      switch (sta) {
        case 3 :
          str = '已批准待开票'
          break
        case 4 :
          str = '暂缓开票'
          break
        case 5 :
        case 7 :
          str = '已开票未登记'
          break
        default :
      }
      return str
    },
    getDetails: function () {
      let _this = this
      _this.$http.post('../../../sale/getApplicationItemListByApplicationId.do', {
        'applicationId': _this.applicationId
      }, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        console.log('申请详情：', res)
        _this.list = res.data || []
        _this.info = res.base[0]
        _this.loading = false
        _this.title = _this.chargeTitle(Number(_this.info.state))
        _this.info['invoice_amount'] = 0
        _this.list.forEach(function (item) {
          _this.info['invoice_amount'] += Number(item['amount'])
        })
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    toInfo: function (id, apId, fid) {
      let _this = this
      this.$router.push({
        "path": `/applyInvoiceDetailInfo/${apId}/${id}/${_this.info.division_way}/${fid}`
      })
    },
    // 暂缓、批准本次开票申请
    changeApplyBtn: function (type) {
      // 4- 暂缓开票 ， 3 - 审批通过
      this.changeType = type
      if (type === 3) {
        this.$confirm('批准本次开票申请?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.changeApply()
        }).catch(() => {
        })
      } else {
        this.dialogVisible = true
      }
    },
    changeApply: function () {
      let _this = this
      _this.loading = true
      let userId = this.sphdSocket.user.userID
      let oid = this.sphdSocket.org.id
      console.log('打印一下超管批准的传值：', {
        'oid': oid,
        'userId': userId,
        'id': _this.applicationId,
        'state': _this.changeType,
        'terminateState': '',
        'terminateReason': ''
      })
      this.sphdSocket.send('changeStateById', {
        'oid': oid,
        'userId': userId,
        'id': _this.applicationId,
        'state': _this.changeType,
        'terminateState': '',
        'terminateReason': ''
      })
    },
    subscribeCallBack: function (res) {
      console.log('回调详情：', res)
      let data = JSON.parse(res)
      let state = data['data']['state'] // 4 - 暂缓 ， 3 批准
      console.log(state)
      let opreator = data['data']['updator']
      let curUserID = this.sphdSocket.user.userID
      if (curUserID === opreator) {
        if (state === '3' || state === '4') {
          this.$message({
            type: 'success',
            message: '操作成功！'
          })
          this.$router.push({
            path: `/invoiceApproval`
          })
        } else {
          this.$message({
            type: 'error',
            message: '操作失败！'
          })
        }
      } else {
        if (state === '3') {
          this.$message({
            type: 'success',
            message: '该操作已被批准！'
          })
        } else if (state === 4) {
          this.$message({
            type: 'error',
            message: '该操作已被暂缓开票！'
          })
        }
        this.getDetails()
      }
    },
    // 填完发票号的回调
    handleCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('开票登记回调', res)
      if (res['state'] === '5') { // 前面确定开票
        _this.$message({
          type: 'success',
          message: '财务人员开票登记了此申请！'
        })
      }
      _this.getDetails()
    }
  }
}
</script>
