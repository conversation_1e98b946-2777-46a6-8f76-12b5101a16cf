<template>
  <div class="desktop">
    <ul class="affair myDesktop">
      <li name="resume" id="resume" v-if="realPerson" @click="getPersonalInfor">
        <i class="fa fa-user"></i>
        <span class="icon-name" title="个人信息">
              <span>个人信息</span>
        </span>
      </li>
      <li name="addressBook" v-if="realPerson && insider" @click="goUrl('../user/toAddressList.do')">
        <i class="fa fa-address-book"></i>
        <span class="icon-name" title="通讯录">
              <span>通讯录</span>
        </span>
      </li>
      <li name="myWork" v-if="realPerson" @click="goUrl('../mywork/toMyworkPage.do')">
        <i class="fa fa-file-text"></i>
        <i class="fa fa-wordpress dbIcon" style="font-size:20px "></i>
        <span class="icon-name" title="我的工作记录">
            <span>我的工作记录</span>
        </span>
      </li>
      <li name="workAttendance" v-if="realPerson" @click="goUrl('../workAttendance/toMyAttendence.do')">
        <i class="fa fa-calendar"></i>
        <span class="icon-name" title="我的考勤">
            <span>我的考勤</span>
        </span>
      </li>
      <li name="userLog" @click="goUrl('../vue/minersFrontEnd/dist/index.html#/myLoginLog')">
        <i class="fa fa-file-text"></i>
        <i class="fa fa-user-o dbIcon" style="font-size:20px "></i>
        <span class="icon-name" title="我的登录记录">
            <span>我的登录记录</span>
        </span>
      </li>
    </ul>

    <TyDialog v-if="userInfoVisible"
              :dialogTitle="'个人信息'" :color="'blue'" :dialogHide="hideU" >
      <template #dialogFooter>
        <div v-if="userInfo.user.submitState == '0'">
          <el-radio v-model="userInfoEditRadio" label="1">我的资料已填写完成</el-radio>
          <el-button :class="userInfoEditRadio == 1 ? 'bounce-ok': 'bounce-cancel' " @click="userInfoEditRadioOk">确定</el-button>
        </div>
        <div v-else>
          <el-button @click="hideU">关闭</el-button>
        </div>

      </template>
      <template #dialogBody>
        <div class="">
          <p style="text-align: right; margin-bottom:10px; ">
            <el-button class="ty-btn-blue" @click="preView">预览</el-button>
          </p>
          <table class="ty-table ty-table-control ty-table-txtLeft" v-if="userInfo">
            <tbody>
              <tr class="ty-table-header">
                <td class="ty-td-txtCenter">资料名称</td><td class="ty-td-txtCenter">操作</td>
              </tr>
              <tr>
                <td class=" ty-bold">基本信息</td>
                <td class="">
                  <span class="ty-color-blue" @click="editBasic('update')">编辑</span>
                  <span class="ty-color-blue" @click="editBasic('updateLog')">修改记录</span>
                </td>
              </tr>
              <tr>
                <td class=" ty-bold">教育经历</td>
                <td>
                  <span class="ty-color-blue" @click="addEdu">新增</span>
                </td>
              </tr>
              <tr v-for="(edu, eduIndex) in userInfo.personalEducations" :key="eduIndex">
                <td class="pad40">{{ edu.collegeName}}</td>
                <td>
                  <span class="ty-color-blue" @click="editEdu(edu, 'update')">修改</span>
                  <span class="ty-color-blue" @click="editEdu(edu, 'updateLog')">修改记录</span>
                  <span class="ty-color-blue" @click="editEdu(edu, 'delete')">删除</span>
                </td>
              </tr>
              <tr>
                <td class=" ty-bold">工作经历</td>
                <td>
                  <span class="ty-color-blue" @click="addEOcc">新增</span>
                </td>
              </tr>
              <tr v-for="(occ, occIndex) in userInfo.personnelOccupations" :key="occIndex">
                <td class="pad40">{{ occ.corpName }}</td>
                <td>
                  <span class="ty-color-blue" @click="editOcc(occ, 'update')">修改</span>
                  <span class="ty-color-blue" @click="editOcc(occ, 'updateLog')">修改记录</span>
                  <span class="ty-color-blue" @click="editOcc(occ, 'delete')">删除</span>
                </td>
              </tr>
              <tr>
                <td class="ty-bold">紧急联系方式</td>
                <td>
                  <span class="ty-color-blue" @click="emergencyEdit">编辑</span>
                </td>
              </tr>
            </tbody>

          </table>

        </div>

      </template>
    </TyDialog>
    <TyDialog v-if="userInfoBasicVisible" width="800"
              dialogTitle="基本信息" color="blue" :dialogHide="hideU2" >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideU2">取消</el-button>
        <el-button class="bounce-ok" @click="userInfoBasicOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="">
          <table class="widthAll">
            <tbody>
              <tr>
                <td class="txt-right">
                  <span class="ttl1">姓名</span>
                </td>
                <td>
                  <span>{{ userBasic.realName }}</span>
                </td>
                <td class="txt-right">
                  <span class="ttl1">性别</span>
                </td>
                <td>
                  <span>
                    <el-radio v-model="userBasic.gender" label="1">男</el-radio>
                    <el-radio v-model="userBasic.gender" label="0">女</el-radio>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="txt-right">
                  <span class="ttl1">民族</span>
                </td>
                <td>
                  <el-input v-model="userBasic.nation" placeholder=""></el-input>
                </td>
                <td class="txt-right">
                  <span class="ttl1">出生日期</span>
                </td>
                <td>
                  <el-date-picker v-model="userBasic.birthday" type="date" value-format="YYYY/MM/DD" placeholder=""></el-date-picker>
                </td>
              </tr>
              <tr>
                <td class="txt-right">
                  <span class="ttl1">出生地</span>
                </td>
                <td>
                  <el-input v-model="userBasic.birthplace" placeholder=""></el-input>
                </td>
                <td class="txt-right">
                  <span class="ttl1">婚姻状况</span>
                </td>
                <td>
                  <el-select v-model="userBasic.marry" placeholder="">
                    <el-option label="未婚" :value="1"></el-option>
                    <el-option label="已婚" :value="0"></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="txt-right">
                  <span class="ttl1">政治面貌</span>
                </td>
                <td>
                  <el-input v-model="userBasic.politicalStatus" placeholder=""></el-input>
                </td>
                <td class="txt-right">
                  <span class="ttl1">最高学历</span>
                </td>
                <td>
                  <el-select v-model="userBasic.degree" placeholder="">
                    <el-option label="研究生" :value="1"></el-option>
                    <el-option label="本科" :value="2"></el-option>
                    <el-option label="大专" :value="3"></el-option>
                    <el-option label="中专或高中" :value="4"></el-option>
                    <el-option label="其他" :value="5"></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="txt-right">
                  <span class="ttl2">第一外语语种</span>
                </td>
                <td>
                  <el-input v-model="userBasic.firstLanguage" placeholder=""></el-input>
                </td>
                <td class="txt-right">
                  <span class="ttl2">水平或证书</span>
                </td>
                <td>
                  <el-input v-model="userBasic.firstForeignLevel" placeholder=""></el-input>
                </td>
              </tr>
              <tr>
                <td class="txt-right">
                  <span class="ttl2">第二外语语种</span>
                </td>
                <td>
                  <el-input v-model="userBasic.secondLanguage" placeholder=""></el-input>
                </td>
                <td class="txt-right">
                  <span class="ttl2">水平或证书</span>
                </td>
                <td>
                  <el-input v-model="userBasic.secondForeignLevel" placeholder=""></el-input>
                </td>
              </tr>
              <tr>
                <td class="txt-right">
                  <span class="ttl3">计算机水平或证书</span>
                </td>
                <td colspan="3">
                  <el-input v-model="userBasic.computerLevel" placeholder=""></el-input>
                </td>
              </tr>
              <tr>
                <td class="txt-right">
                  <span class="ttl3">其他技能描述</span>
                </td>
                <td colspan="3">
                  <el-input v-model="userBasic.otherSkills" placeholder=""></el-input>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <p class="tipBlue">
                    注：您的联系方式在设置为 <i class="ty-color-orange">同事可见</i> 前，您同事无法看见，且修改无需审批。
                  </p>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <span class="ttl3">手机号码</span>
                  <span>{{ userBasic.mobile }}</span>
                  <span class="ty-linkBtn" @click="addContactBtn">添加更多联系方式</span>
                  <el-select v-model="userBasic.typeSelect" @change="setTypeSelect" placeholder="" v-if="userBasic.showTypeSelect">
                    <el-option label="" value=""></el-option>
                    <el-option label="手机" :value="1"></el-option>
                    <el-option label="QQ" :value="2"></el-option>
                    <el-option label="Email" :value="3"></el-option>
                    <el-option label="微信" :value="4"></el-option>
                    <el-option label="微博" :value="5"></el-option>
                    <el-option label="自定义" :value="9"></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <div class="contacCon">
                    <div v-for="(contac, conIndex) in userBasic.userContacts" class="ty-left">
                      <div>
                        <span class="ttl4">{{ formatContactType(contac) }}</span>
                        <el-input v-model="contac.code" placeholder="" class="inputClass"></el-input>
                        <span class="ty-linkBtn" @click="delContactBtn(contac, conIndex)">删除</span>
                        <span v-if="(!contac.open && contac.code)  " class="ty-linkBtn" @click="openContactBtn(contac, conIndex)">同事可见</span>
                      </div>
                    </div>
                    <div class="clr"></div>
                  </div>
                </td>
              </tr>
            </tbody>

          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="openContactVisible" dialogTitle="提示" color="red" :dialogHide="hideU3" >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideU3">取消</el-button>
        <el-button class="bounce-ok" @click="openContactOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center" style="margin-top: 20px;">
          该联系方式将显示于您同事的通讯录中。
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="userBasicLogVisible" width="800" dialogTitle="基本信息修改记录" color="blue" :dialogHide="hideU5" >
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div class="">
          <p style="line-height:40px;">
            <span>当前数据为第{{ userBasicLog.logLast && userBasicLog.logLast.number }}次修改后的结果。</span>
            <span style="float: right;">修改时间：{{ userBasicLog.logLast && userBasicLog.logLast.updateName }}
              {{ (new Date( userBasicLog.logLast && userBasicLog.logLast.updateTime).format('yyyy/MM/dd hh:mm:ss')) }}
            </span>
          </p>
          <table class="ty-table">
            <tbody>
              <tr class="ty-table-header">
                <td>记录</td>
                <td>操作</td>
                <td>创建者/修改者</td>
              </tr>
              <tr v-for="(uLog, ulogIndex) in userBasicLog.userRoleHistoryList">
                <td>{{ uLog.dataState }}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="scanLog(uLog)">操作</span></td>
                <td>{{ uLog.updateName }} {{ (new Date(uLog.updateTime).format('yyyy/MM/dd hh:mm:ss')) }}</td>
              </tr>
            </tbody>
          </table>
          <TyPage v-if="userBasicLog.userRoleHistoryList"
                  :curPage="userBasicLog.pageInfo.currentPageNo" :pageSize="userBasicLog.pageInfo.pageSize"
                  :allPage="userBasicLog.pageInfo.totalPage" :pageClickFun="userBasicPageClick"></TyPage>

        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="userBasicLogDetailsVisible" width="600" dialogTitle="基本信息" color="blue" :dialogHide="hideU6" >
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div>
          <table class="widthAll" style="border-collapse: separate; border-spacing: 10px;">
            <tbody>
              <tr>
                <td class="txtRight">姓名</td><td>{{ userBasicLogDetails.user.userName }}</td>
                <td class="txtRight">性别</td><td>{{ formatGender(userBasicLogDetails.user.gender ) }}</td>
              </tr>
              <tr>
                <td class="txtRight">民族</td><td>{{ userBasicLogDetails.user.nation }}</td>
                <td class="txtRight">出生日期</td><td>{{ new Date(userBasicLogDetails.user.birthday).format('yyyy/MM/dd') }}</td>
              </tr>
              <tr>
                <td class="txtRight">出生地</td><td>{{ userBasicLogDetails.user.birthplace }}</td>
                <td class="txtRight">婚姻状况</td><td>{{ formatMarry(userBasicLogDetails.user.marry ) }}</td>
              </tr>
              <tr>
                <td class="txtRight">政治面貌</td><td>{{ userBasicLogDetails.user.politicalStatus }}</td>
                <td class="txtRight">最高学历</td><td>{{ formatDegree(userBasicLogDetails.user.degree ) }}</td>
              </tr>
              <tr>
                <td class="txtRight">第一外语语种</td><td>{{ userBasicLogDetails.user.firstLanguage }}</td>
                <td class="txtRight">水平或证书</td><td>{{ (userBasicLogDetails.user.firstForeignLevel ) }}</td>
              </tr>
              <tr>
                <td class="txtRight">第二外语语种</td><td>{{ userBasicLogDetails.user.secondLanguage }}</td>
                <td class="txtRight">水平或证书</td><td>{{ (userBasicLogDetails.user.secondForeignLevel ) }}</td>
              </tr>
              <tr>
                <td class="txtRight">计算机水平或证书</td><td colspan="3">{{ userBasicLogDetails.user.computerLevel }}</td>
              </tr>
              <tr>
                <td class="txtRight">其他技能描述</td><td colspan="3">{{ userBasicLogDetails.user.otherSkills }}</td>
              </tr>
              <tr>
                <td class="txtRight">手机号码</td><td colspan="3">{{ userBasicLogDetails.user.mobile }}</td>
              </tr>

            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <TyDialog v-if="editEduVisible" width="718" dialogTitle="教育经历" color="blue" :dialogHide="hideE1">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideE1">取消</el-button>
        <el-button class="bounce-ok" @click="editEduOk">确定</el-button>
      </template>
      <template #dialogBody>
        <table class="eduCon" style="border-collapse: separate; border-spacing: 10px;">
          <tbody>
            <tr>
              <td class="txtRight">学习时间</td>
              <td>
                <el-date-picker v-model="editEduObj.beginTime" type="month" value-format="YYYY/MM"></el-date-picker>
              </td>
              <td class="txtCenter"> - -  - -</td>
              <td>
                <el-date-picker v-model="editEduObj.endTime" type="month" value-format="YYYY/MM"></el-date-picker>
              </td>
            </tr>
            <tr>
              <td class="txtRight">毕业院校</td>
              <td colspan="3"><el-input v-model="editEduObj.collegeName" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">院系</td>
              <td><el-input v-model="editEduObj.departmentName" placeholder=""></el-input></td>
              <td class="txtRight">专业</td>
              <td><el-input v-model="editEduObj.major" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">学历/学位</td>
              <td><el-input v-model="editEduObj.degreeDesc" placeholder=""></el-input></td>
              <td></td><td></td>
            </tr>
            <tr>
              <td class="txtRight">专业描述</td>
              <td colspan="3"><el-input type="textarea" :rows="3" v-model="editEduObj.majorDesc" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">补充说明</td>
              <td colspan="3"><el-input type="textarea" :rows="3" v-model="editEduObj.memo" placeholder=""></el-input></td>
            </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>
    <TyDialog v-if="logEduVisible" width="718" dialogTitle="教育经历修改记录" color="blue" :dialogHide="hideE2">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div class="ty-center" v-if="logEdu.logLast && logEdu.logLast.number === 0">
          教育经历尚未修改过。
        </div>
        <div class="" v-else>
          <p style="line-height:40px;">
            <span>当前数据为第{{ logEdu.logLast && logEdu.logLast.number }}次修改后的结果。</span>
            <span style="float: right;">修改时间：{{ logEdu.logLast && logEdu.logLast.updateName }}
              {{ (new Date( logEdu.logLast && logEdu.logLast.updateTime).format('yyyy/MM/dd hh:mm:ss')) }}
            </span>
          </p>
          <table class="ty-table">
            <tbody>
              <tr class="ty-table-header">
                <td>记录</td>
                <td>操作</td>
                <td>创建者/修改者</td>
              </tr>
              <tr v-for="(uLog, ulogIndex) in logEdu.userRoleHistoryList">
                <td>{{ uLog.dataState }}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="scanLog(uLog)">操作</span></td>
                <td>{{ uLog.updateName }} {{ (new Date(uLog.updateTime).format('yyyy/MM/dd hh:mm:ss')) }}</td>
              </tr>
            </tbody>
          </table>
          <TyPage v-if="logEdu.userRoleHistoryList"
                  :curPage="logEdu.pageInfo.currentPageNo" :pageSize="logEdu.pageInfo.pageSize"
                  :allPage="logEdu.pageInfo.totalPage" :pageClickFun="logEduPageClick"></TyPage>

        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="delEduVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideE3">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideE3">取消</el-button>
        <el-button class="bounce-ok" @click="delEduOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center" style=" line-height: 30px;">
          删除后，您所预览的个人资料中将不再含有本条数据。<br/>
          如需要，您可在修改记录中查看。<br/>
          确定删除本条教育经历吗？
        </div>
      </template>
    </TyDialog>


    <TyDialog v-if="editOccVisible" width="718" dialogTitle="工作经历" color="green" :dialogHide="hideO1">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideO1">取消</el-button>
        <el-button class="bounce-ok" @click="editOccOk">确定</el-button>
      </template>
      <template #dialogBody>
        <table class="eduCon" style="border-collapse: separate; border-spacing: 10px;">
          <tbody>
            <tr>
              <td class="txtRight">服务时间</td>
              <td>
                <el-date-picker v-model="editOccObj.beginTime" type="month" value-format="YYYY/MM" format="YYYY/MM"></el-date-picker>
              </td>
              <td class="txtCenter"> - -  - -</td>
              <td>
                <el-date-picker v-model="editOccObj.endTime" type="month" value-format="YYYY/MM" format="YYYY/MM"></el-date-picker>
              </td>
            </tr>
            <tr>
              <td class="txtRight">公司名称</td>
              <td colspan="3"><el-input v-model="editOccObj.corpName" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">公司规模</td>
              <td><el-input v-model="editOccObj.corpSize" placeholder=""></el-input></td>
              <td class="txtRight">公司性质</td>
              <td><el-input v-model="editOccObj.corpNature" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">所在部门</td>
              <td><el-input v-model="editOccObj.corpDepartment" placeholder=""></el-input></td>
              <td class="txtRight">职位</td>
              <td><el-input v-model="editOccObj.post" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">工作描述</td>
              <td colspan="3"><el-input type="textarea" :rows="3" v-model="editOccObj.jobDesc" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">补充说明</td>
              <td colspan="3"><el-input type="textarea" :rows="3" v-model="editOccObj.memo" placeholder=""></el-input></td>
            </tr>
          </tbody>

        </table>
      </template>
    </TyDialog>
    <TyDialog v-if="logOccVisible" width="718" dialogTitle="工作经历修改记录" color="blue" :dialogHide="hideO2">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div class="ty-center" v-if="logOcc.logLast && logOcc.logLast.number === 0">
          工作经历尚未修改过。
        </div>
        <div class="" v-else>
          <p style="line-height:40px;">
            <span>当前数据为第{{ logOcc.logLast && logOcc.logLast.number }}次修改后的结果。</span>
            <span style="float: right;">修改时间：{{ logOcc.logLast && logOcc.logLast.updateName }}
              {{ (new Date( logOcc.logLast && logOcc.logLast.updateTime).format('yyyy/MM/dd hh:mm:ss')) }}
            </span>
          </p>
          <table class="ty-table">
            <tbody>
              <tr class="ty-table-header">
                <td>记录</td>
                <td>操作</td>
                <td>创建者/修改者</td>
              </tr>
              <tr v-for="(uLog, ulogIndex) in logOcc.userRoleHistoryList">
                <td>{{ uLog.dataState }}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="scanLog(uLog)">操作</span></td>
                <td>{{ uLog.updateName }} {{ (new Date(uLog.updateTime).format('yyyy/MM/dd hh:mm:ss')) }}</td>
              </tr>
            </tbody>

          </table>
          <TyPage v-if="logOcc.userRoleHistoryList"
                  :curPage="logOcc.pageInfo.currentPageNo" :pageSize="logOcc.pageInfo.pageSize"
                  :allPage="logOcc.pageInfo.totalPage" :pageClickFun="logOccPageClick"></TyPage>

        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="delOccVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideO3">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideO3">取消</el-button>
        <el-button class="bounce-ok" @click="delOccOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center" style=" line-height: 30px;">
          删除后，您所预览的个人资料中将不再含有本条数据。<br/>

          如需要，您可在修改记录中查看。<br/>

          确定删除本条工作经历吗？
        </div>
      </template>
    </TyDialog>

    <TyDialog v-if="emergencyEditVisible" width="700" dialogTitle="紧急联系人" color="green" :dialogHide="hideOEmerg0">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideOEmerg0">取消</el-button>
        <el-button class="bounce-ok" @click="editEmergOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <table class="allWidth">
            <tbody>
              <tr>
                <td class="eTtl">紧急联系人</td>
                <td>
                  <el-input v-model="emergencyObj.emergencyName" placeholder=""></el-input>
                </td>
                <td class="eTtl">紧急联系人的联系方式</td>
                <td>
                  <el-input v-model="emergencyObj.emergencyContact" placeholder=""></el-input>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="preViewVisible" width="800" dialogTitle="个人信息" color="green" :dialogHide="hidePreView">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div>

          <div class="preViewCC">
            <div id="baseInfoDetails">
              <div class="userBaseInfo">
                <div class="userImg">
                  <img :src="userInfo.user.imgPath" alt="" onerror="">
                </div>
                <div class="userCon">
                  <h4 class="userName">{{ userInfo.user.userName }}</h4>
                  <p class="baseDetail">
                    <span>{{ formatGender(userInfo.user.gender) }} / </span>
                    <span>{{ userInfo.user.nation }} / </span>
                    <span>{{ new Date(userInfo.user.birthday).format('yyyy年MM月dd日生') }} / </span>
                    <span>{{ userInfo.user.birthplace }} / </span>
                    <span>{{ userInfo.user.politicalStatus }} / </span>
                    <span>{{ formatMarry(userInfo.user.marry) }} / </span>
                    <span>{{ formatDegree(userInfo.user.degree) }} </span>
                  </p>
                  <div class="contectList clear">
                    <span class="contI">
                      <span class="tctImg fa fa-mobile"></span>
                      <span class="mobile">{{ userInfo.user.mobile }}</span>
                    </span>
                    <span class="contI">
                      <span class="tctImg fa fa-qq"></span>
                        <span class="qq">{{ userInfo.user.qq || '1027778908' }}</span>
                    </span>
                    <span class="contI">
                      <span class="tctImg fa fa-envelope"></span>
                      <span class="envelope">{{ userInfo.user.email || '<EMAIL>' }}</span>
                    </span>
                  </div>
                </div>

              </div>
              <div class="otherInfo">
                <h5 class="ttlH5">个人技能</h5>
                <div class="charact">
                  <div class="mmTtl">外语：</div>
                  <div class="mmCon">
                    <p>
                      <span>第一外语语种：{{ userInfo.user.firstLanguage }} {{ userInfo.user.firstForeignLevel }}</span>
                    </p>
                    <p>
                      <span>第二外语语种：{{ userInfo.user.secondLanguage }} {{ userInfo.user.secondForeignLevel }}</span>
                    </p>
                  </div>
                </div>
                <div class="charact">
                  <div class="mmTtl">计算机：</div>
                  <div class="mmCon">
                    <p>{{ userInfo.user.computerLevel }}</p>
                  </div>
                </div>
                <div class="charact">
                  <div class="mmTtl">其它技能描述：</div>
                  <div class="mmCon">
                    <p>{{ userInfo.user.otherSkills }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="otherInfo">
              <h5 class="ttlH5">教育经历</h5>
              <div id="eduHashMap">
                <div class="areaBood" v-for=" (education, eduIndex) in userInfo.personalEducations" :key="eduIndex">
                  <div class="charact">
                    <div class="mmTtl">{{ new Date(education.beginTime).format('yyyy/MM') }} - {{ new Date(education.endTime).format('yyyy/MM') }}</div>
                    <div class="timeSlot">
                      <span>{{ education.collegeName || '--' }} |
                        {{ education.departmentName || '--' }} |
                        {{ education.major || '--' }} |
                        {{ education.degreeDesc || '--' }} </span>
                    </div>
                  </div>
                  <div class="charact">
                    <div class="mmTtl">专业描述：</div>
                    <div class="mmCon"><p>{{ education.majorDesc }}</p></div>
                  </div>
                  <div class="charact">
                    <div class="mmTtl">补充说明：</div>
                    <div class="mmCon"><p>{{education.memo }}</p></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="otherInfo">
              <h5 class="ttlH5">工作经历</h5>
              <div id="workHashMap">
                <div class="areaBood" v-for=" (occupation, occIndex) in userInfo.personnelOccupations" :key="occIndex">
                  <div class="charact">
                    <div class="mmTtl">{{ new Date(occupation.beginTime).format('yyyy/MM') }} - {{ new Date(occupation.endTime).format('yyyy/MM') }}</div>
                    <div class="timeSlot">
                      <span>{{ occupation.corpName || '--' }} |
                        {{ occupation.corpName || '--' }} |
                        {{ occupation.corpNature || '--' }} |
                        {{ occupation.corpDepartment || '--' }} |
                        {{ occupation.post || '--' }} </span>
                    </div>
                  </div>
                  <div class="charact">
                    <div class="mmTtl">工作描述：</div>
                    <div class="mmCon"><p>{{'工作描述'}}</p></div>
                  </div>
                  <div class="charact">
                    <div class="mmTtl">补充说明：</div>
                    <div class="mmCon"><p>{{'补充说明'}}</p></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
  </div>
</template>
<script>
import { initNav, beforeRouteLeave, jumpToOldPage } from "@/utils/routeChange"
import { userInfoPreview, updateUserBasicInfo, getUserBasicInfoHistory,
  getUserHistory, addPersonalEducation, updatePersonalEducation, markDeleteEducation,
  personalEducationHistories, addPersonnelOccupation, updatePersonnelOccupation,
  personnelOccupationHistories, markDeleteOccupation, addEmergencyContact,submitUserInfo,  } from "@/api/userInfo"
import auth from '@/sys/auth'
import JSONBig from 'json-bigint'
import {nanoid} from 'nanoid'
import imgError from '@/assets/commonImg/avatar_default_small.png'

export default {
  data() {
    return {
      pageName: 'desktop',
      desktopData: [],
      realPerson: false,
      insider: false,
      userInfo: {
        personalEducations : [],
        personnelOccupations: [],
        user : {},
        userContacts : [],
      },
      userBasic:{},
      userBasicLog:{},
      userBasicLogDetails:{},
      userInfoVisible: false,
      userInfoEditRadio: '',
      userInfoBasicVisible: false,
      openContactVisible: false,
      userBasicLogVisible: false,
      userBasicLogDetailsVisible: false,
      editEduVisible: false,
      logEduVisible: false,
      delEduVisible: false,
      delOccVisible: false,
      editOccVisible: false,
      logOccVisible: false,
      emergencyEditVisible: false,
      preViewVisible: false,
      emergencyObj: {},
      editEduObj: {},
      editOccObj: {},
      editContactObj: {},
      logEdu: {},
      logOcc: {},
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  components: {
  },
  mounted() {
    initNav({mid: 'wodezhuomian', name: '我的桌面', pageName: this.pageName}, this.showHideByPrincipal, this)
  },
  methods: {
    showHideByPrincipal(){
      let roleCode = auth.getUser().roleCode
      // 超级浏览者--browse   代理会计agentAccounting   代理小会计--agentSmallAccounting 这三个值的 都是外部人员，不给展示通讯录
      const outsiderRole = ['browse', 'agentAccounting', 'agentSmallAccounting']
      // 身份不展示：个人信息、通讯录、我的工作记录、我的考勤
      const npcRole = ['finance', 'sale', 'general', 'accounting', 'smallSuper']
      this.insider = !outsiderRole.includes(roleCode)
      this.realPerson = !npcRole.includes(roleCode)
    },
    userInfoEditRadioOk(){
      if(this.userInfoEditRadio == 1){
        let user = auth.getUser()
        let data = { id: user.userID }
        submitUserInfo(data).then(res => {
          console.log('res=', res)
          let data = res.data
          var success = data['success'];
          if (success == 1) {
            // this.getPersonalInfor()
            this.userInfoVisible = false
          } else{
            this.$message({
              'type': 'error',
              'message': '获取失败！'
            })
          }
        }).catch(err => {
          console.log('err=', err)
        })
      }
    },
    hidePreView(){
      this.preViewVisible = false

    },
    preView(){
      this.preViewVisible = true

    },
    emergencyEdit(){
      this.emergencyEditVisible = true
      this.emergencyObj = this.userInfo.user

    },
    hideOEmerg0(){
      this.emergencyEditVisible = false
    },
    editEmergOk(){
      let data = {
        emergencyName: this.emergencyObj.emergencyName ,
        emergencyContact: this.emergencyObj.emergencyContact ,
      }
      addEmergencyContact(data).then(res => {
        let data = res.data
        var success = data['success'];
        this.emergencyEditVisible = false
        if (success == 1) {
          this.$message({
            type:'success',
            message:'修改成功！'
          })
          this.getPersonalInfor()

        } else{
          this.$message({
            type:'error',
            message:'提交失败！'
          })
        }
      }).catch(err => {

      })
    },
    editEduOk(){
      let data = {
        beginTime: this.editEduObj.beginTime,
        endTime: this.editEduObj.endTime,
        collegeName: this.editEduObj.collegeName,
        departmentName:this.editEduObj.departmentName,
        major: this.editEduObj.major,
        degreeDesc: this.editEduObj.degreeDesc,
        majorDesc: this.editEduObj.majorDesc,
        memo: this.editEduObj.memo,
      }
      if(this.editEduObj.type === 'add'){
        addPersonalEducation(data).then(res => {
          let data = res.data
          var success = data['success'];
          if (success == 1) {
            this.editEduVisible = false
            this.getPersonalInfor()
          } else{
            this.$message({
              type:'error',
              message:'提交失败！'
            })
          }
        }).catch(err => {
          this.$message({
            type:'error',
            message: err
          })
        })
      }
      else if(this.editEduObj.type === 'update'){
        data.id = this.editEduObj.id
        updatePersonalEducation(data).then(res => {
          let data = res.data
          var success = data['success'];
          if (success == 1) {
            this.editEduVisible = false
            this.getPersonalInfor()
          } else{
            this.$message({
              type:'error',
              message:'提交失败！'
            })
          }
        }).catch(err => {
          this.$message({
            type:'error',
            message: err
          })
        })
      }
    },
    editOccOk(){
      let data = {
        beginTime: this.editOccObj.beginTime,
        endTime: this.editOccObj.endTime,
        corpName: this.editOccObj.corpName,
        corpSize: this.editOccObj.corpSize,
        corpNature: this.editOccObj.corpNature,
        corpDepartment: this.editOccObj.corpDepartment,
        post: this.editOccObj.post,
        jobDesc: this.editOccObj.jobDesc,
        memo: this.editOccObj.memo,
      }
      if(this.editOccObj.type === 'add'){
        addPersonnelOccupation(data).then(res => {
          let data = res.data
          var success = data['success'];
          if (success == 1) {
            this.editOccVisible = false
            this.getPersonalInfor()
          } else{
            this.$message({
              type:'error',
              message:'提交失败！'
            })
          }
        }).catch(err => {
          this.$message({
            type:'error',
            message: err
          })
        })
      }
      else if(this.editOccObj.type === 'update'){
        data.id = this.editOccObj.id
        updatePersonnelOccupation(data).then(res => {
          let data = res.data
          var success = data['success'];
          if (success == 1) {
            this.editOccVisible = false
            this.getPersonalInfor()
          } else{
            this.$message({
              type:'error',
              message:'提交失败！'
            })
          }
        }).catch(err => {
          this.$message({
            type:'error',
            message: err
          })
        })
      }
    },
    editOcc(occ , type){
      // type: update-修改， updateLog-修改记录， delete-删除
      if(type === 'update'){
        this.editOccVisible = true
        this.editOccObj = {
          beginTime: occ.beginTime,
          endTime: occ.endTime,
          corpName:occ.corpName,
          corpSize: occ.corpSize,
          corpNature: occ.corpNature,
          corpDepartment: occ.corpDepartment,
          post: occ.post,
          jobDesc: occ.jobDesc,
          memo: occ.memo,
          id: occ.id,
          type: 'update',
        }

      }
      else if(type === 'updateLog'){
        this.logOccVisible = true
        this.editOccObj = occ
        let sdata = {
          id: this.editOccObj.id,
          pageSize: 10,
          currentPageNo: 1
        }
        this.personnelOccupationHistoriesFun(sdata)

      }
      else if(type === 'delete'){
        this.editOccObj = occ
        this.delOccVisible = true
      }
    },
    delOccOk(){
      this.delOccVisible = false
      let data = { id: this.editOccObj.id   }
      markDeleteOccupation(data).then(res => {
        let data = res.data
        var success = data['success'];
        if (success == 1) {
          this.getPersonalInfor();
          this.$message({
            type:'success',
            message:'删除成功！'
          })
        } else{
          this.$message({
            type:'error',
            message:'删除失败！'
          })
        }
      }).catch(err => {
        this.$message({
          type:'error',
          message:'删除失败！'
        })
      })
    },
    logOccPageClick(pageInfo){
      let data = {
        id: this.editOccObj.id,
        pageSize: 5 ,
        currentPageNo: pageInfo.page
      }
      this.personnelOccupationHistoriesFun(data)

    },
    personnelOccupationHistoriesFun(data){
      personnelOccupationHistories(data).then(res => {
        let data = res.data.data
        this.logOcc = data
        this.logOcc.logLast = {
          number: data.number ,
          updateName: data.updateName ,
          updateTime: data.updateTime
        }
      }).catch(err => {

      })
    },
    hideO1(){
      this.editOccVisible = false
    },
    hideO2(){
      this.logOccVisible = false
    },
    hideO3(){
      this.delOccVisible = false
    },
    hideE1(){
      this.editEduVisible = false
    },
    hideE2(){
      this.logEduVisible = false
    },
    hideE3(){
      this.delEduVisible = false
    },
    delEduOk(){
      this.delEduVisible = false
      let data = { id: this.editEduObj.id   }
      markDeleteEducation(data).then(res => {
        let data = res.data
        var success = data['success'];
        if (success == 1) {
          this.getPersonalInfor();
          this.$message({
            type:'success',
            message:'删除成功！'
          })
        } else{
          this.$message({
            type:'error',
            message:'删除失败！'
          })
        }
      }).catch(err => {
        this.$message({
          type:'error',
          message:'删除失败！'
        })
      })
    },
    addEdu(){
      this.editEduVisible = true
      this.editEduObj = {
        beginTime:'',
        endTime:'',
        collegeName:'',
        departmentName:'',
        major:'',
        degreeDesc:'',
        majorDesc:'',
        memo:'',
        type: 'add',
      }

    },
    addEOcc(){
      this.editOccVisible = true
      this.editOccObj = {
        beginTime:'',
        endTime:'',
        corpName:'',
        corpSize:'',
        corpNature:'',
        corpDepartment:'',
        post:'',
        jobDesc:'',
        memo:'',
        type: 'add',
      }

    },
    editEdu(edu , type){
      // type: update-修改， updateLog-修改记录， delete-删除
      if(type === 'update'){
        this.editEduVisible = true
        this.editEduObj = {
          beginTime: (new Date(edu.beginTime).format('yyyy-MM')) ,
          endTime: (new Date(edu.endTime).format('yyyy-MM')) ,
          collegeName:edu.collegeName,
          departmentName: edu.departmentName,
          major: edu.major,
          degreeDesc: edu.degreeDesc,
          majorDesc: edu.majorDesc,
          memo: edu.memo,
          id: edu.id,
          type: 'update',
        }
      }
      else if(type === 'updateLog'){
        this.editEduObj = edu
        this.logEduVisible = true
        let sData = {
          id: edu.id,
          pageSize: 10 ,
          currentPageNo: 1
        }
        this.personalEducationHistoriesFun(sData)

      }
      else if(type === 'delete'){
        this.delEduVisible = true
        this.editEduObj = edu
      }
    },
    logEduPageClick(pageInfo){
        let data = {
          id: this.editEduObj.id,
          pageSize: 5 ,
          currentPageNo: pageInfo.page
        }
        this.personalEducationHistoriesFun(data)

    },
    personalEducationHistoriesFun(data){
      personalEducationHistories(data).then(res => {
        let data = res.data.data
        this.logEdu = data
        this.logEdu.logLast = {
          number: data.number ,
          updateName: data.updateName ,
          updateTime: data.updateTime
        }

      }).catch(err => {

      })
    },
    formatGender(sex){
      let str = ''
      switch (Number(sex)){
        case 1: str = '男'; break;
        case 0: str = '女'; break;
        default: str = '';
      }
      return str;
    },
    formatMarry(marry){
      let str = ''
      switch (Number(marry)){
        case 1: str = '未婚'; break;
        case 0: str = '已婚'; break;
        default: str = '';
      }
      return str;
    },
    formatDegree(degree){
      let str = ''
      switch (Number(degree)){
        case 1: str = '研究生'; break;
        case 2: str = '本科'; break;
        case 3: str = '大专'; break;
        case 4: str = '中专或高中'; break;
        case 5: str = '其他'; break;
        default: str = '';
      }
      return str;
    },
    hideU6(){
      this.userBasicLogDetailsVisible = false
    },
    scanLog(uLog){
      let data = { id: uLog.id }
      getUserHistory(data).then(res => {
        this.userBasicLogDetailsVisible = true
        this.userBasicLogDetails = res.data.data

      }).catch(err=>{

      })
    },
    userBasicPageClick(pageInfo){
      let data = {
        pageSize: 5 ,
        currentPageNo: pageInfo.page
      }
      this.getUserBasicInfoHistoryFun(data)
    },
    hideU5(){
      this.userBasicLogVisible = false
    },
    setTypeSelect(){
      this.userBasic.showTypeSelect = false
      let info = {
        type:this.userBasic.typeSelect,
        name:'',
        code:'',
        open:false,
      }
      if(info.type == 9){
        info.name = '自定义'
      }
      this.userBasic.userContacts.push(info)
    },
    addContactBtn(){
      this.userBasic.showTypeSelect = true
      this.userBasic.typeSelect = ''
    },
    formatContactType(contac){
      let str = ''
      let type = contac.type
      switch (Number(type)){
        case 1: str = '手机'; break;
        case 2: str = 'QQ'; break;
        case 3: str = 'Email'; break;
        case 4: str = '微信'; break;
        case 5: str = '微博'; break;
        case 9:
          str = contac.name
          break;
        default:
          break;
      }
      return str;
    },
    editBasic(type){
      // type: update-修改， updateLog-修改记录
      if(type === 'update'){
        this.userInfoBasicVisible = true
        this.userBasic = this.userInfo.user
        this.userBasic.birthday = (new Date(this.userBasic.birthday).format('yyyy-MM-dd'))
        this.userBasic.userContacts = this.userInfo.userContacts
        let uu = auth.getUser()
        this.userBasic.realName = this.userBasic.realName || uu.userName
        console.log('this.userBasic =', this.userBasic)
        this.userBasic.showTypeSelect = false
      }
      else if(type === 'updateLog'){
        let data = {
          pageSize: 5,
          currentPageNo: 1
        }
        this.getUserBasicInfoHistoryFun(data)
      }
    },
    getUserBasicInfoHistoryFun(data){
      this.userBasicLog = {}
      getUserBasicInfoHistory(data).then(res => {
        let data = res.data.data
        this.userBasicLogVisible = true
        this.userBasicLog = data
        this.userBasicLog.curPage = data.currentPageNo
        this.userBasicLog.pageSize = data.pageSize
        this.userBasicLog.allPage = data.pageSize
        let len = this.userBasicLog.userRoleHistoryList.length
        this.userBasicLog.logLast = {
          number: this.userBasicLog.number,
          updateName: this.userBasicLog.updateName,
          updateTime: this.userBasicLog.updateTime
        }

      }).catch(err => {

      })
    },
    userInfoBasicOk(){
      let uu = auth.getUser()
      var userInfo = {
        'passiveUserId': uu.userID,
        'gender': this.userBasic.gender || '',
        'degree': this.userBasic.degree || 0 ,
        "nation": this.userBasic.nation|| '',
        "birthday": this.userBasic.birthday|| '',
        "birthplace": this.userBasic.birthplace|| '',
        "marry": this.userBasic.marry|| '',
        "politicalStatus": this.userBasic.politicalStatus|| '',
        "firstLanguage": this.userBasic.firstLanguage|| '',
        "firstForeignLevel": this.userBasic.firstForeignLevel|| '',
        "secondLanguage": this.userBasic.secondLanguage|| '',
        "secondForeignLevel": this.userBasic.secondForeignLevel|| '',
        "computerLevel": this.userBasic.computerLevel|| '',
        "otherSkills": this.userBasic.otherSkills|| ''
      }
      var contacts = [];
      console.log('userBasic.userContacts=', this.userBasic.userContacts)
      this.userBasic.userContacts.forEach(contact => {
        contacts.push({
          'type': contact.type,
          'name': contact.name,
          'code': contact.code,
          'isOpen': contact.open
        })
      })
      userInfo = JSONBig.stringify(userInfo);
      contacts = JSONBig.stringify(contacts);
      var params = {
        'userInfo': userInfo,
        'contacts': contacts
      };
      updateUserBasicInfo(params).then(res => {
        console.log('修改完=', res.data)
        res = res.data
        var success = res['success'];
        if (success == '1') {
          this.userInfoBasicVisible = false
          this.$message({
            'type':'success',
            'message':'修改成功'
          })
        } else{
          this.$message({
            'type':'error',
            'message':'提交失败！'
          })
        }
      }).catch(err => {
        console.log('updateUserBasicInfo err=', err)
      })

    },
    hideU2(){
      this.userInfoBasicVisible = false
    },
    hideU3(){
      this.openContactVisible = false
    },
    delContactBtn(contac, conIndex){
      // delete this.userBasic.userContacts[conIndex]
      this.userBasic.userContacts.splice(conIndex,1)
    },
    openContactBtn(contac, conIndex){
      this.editContactObj = { cc:contac , ii: conIndex  }
      this.openContactVisible = true
    },
    openContactOk(){
      this.userBasic.userContacts[this.editContactObj.ii]['open'] = true
      this.hideU3()
      console.log('this.userBasic.userContacts=', this.userBasic.userContacts)
    },
    hideU(){
      this.userInfoVisible = false
    },
    goUrl(urlStr){
      jumpToOldPage(urlStr)
    },
    getPersonalInfor(){
      userInfoPreview().then(res => {
        console.log('个人信息=', res)
        this.userInfoVisible = true
        this.userInfo = res.data.data
        console.log('this.userInfo =', this.userInfo )

        let webRoot = auth.webRoot
        let imgPath = this.userInfo.user.imgPath
        if(imgPath){
          this.userInfo.user.imgPath = webRoot + imgPath
        }else{
          this.userInfo.user.imgPath = imgError
        }


      }).catch(err => {
        console.log('err=', err)
        // this.$message({
        //   message: '恭喜你，这是一条成功消息',
        //   type: 'success',
        // })
      })
    },

  }
}

</script>

<style lang="scss" scoped>
.preViewCC{
  font-size:14px ;
  .userBaseInfo { padding: 10px 30px; margin-bottom: 20px; background: #595959; overflow: hidden; }
  .otherInfo { border-bottom: 1px solid #ccc; padding-top:10px;  }
  .userImg { float: left; width: 100px; }
  .userCon { float: left; color: #eee; margin-left: 20px;  }
  .userImg img { width: 100%; border-radius: 50%; }
  .contectList { padding-bottom: 10px;   }
  .contectList .contI{
    min-width:116px; padding:10px; float: left;
    .fa { font-size: 25px; color: #eee; position:relative;  }
    .fa-qq{ top:5px;  }
    .fa-envelope{ top:5px;  }
    .fa-mobile{ font-size: 40px; }
    .qq, .envelope, .mobile {
      position:relative; display: inline-block; padding: 0 0 0 10px;
    }
    .mobile{  top:-10px;   }
    .qq{  top:2px;   }
    .envelope{  top:2px;   }
  }
  .ttlH5 { color: #00a278; font-size: 14px; }
  .charact { margin-bottom: 6px; margin-left: 110px; overflow: hidden; }
  .mmTtl { float: left; width: 20%; min-width: 100px; }
  .mmCon, .timeSlot { float: left; width: 70%; }
  .areaBood{ padding: 10px 0;  }
  .userName{ font-size: 18px;  }
}
.desktop{
  .affair{ margin-right: 20px; padding: 20px; margin-top: 10px;  }
  .affair li{ float:left; margin: 15px 13px; width: 100px; height: 105px; text-align: center; position: relative; overflow: visible; border: 1px dashed transparent; border-radius: 2px; background-color: #f4f6fa; cursor: pointer; }
  .affair li:hover{  background-color: #f1f3f7;}
  .affair li i{ color: #5d9cec; font-size:40px; margin-top:18px;    }
  .affair li .icon-name{ display: block; width: 100%; height: 20px; line-height: 20px; color: #666; font-size: 12px; text-align: center; margin-top: 15px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
  .affair li.disabled { cursor: default; box-shadow: none; opacity:0.7;  }
  .affair li.disabled .icon-name{ color: #aaa; }

  .dbIcon{ position: absolute; font-size: 12px!important; left: 36px; color: #fff!important; top: 2px; }

  .widthAll{width: 100%; font-size:14px;  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif; }
  .widthAll td{ padding: 2px 5px; }
  .txt-right{ text-align: right;  }
  .ttl1, .ttl2, .ttl3, .ttl4 { text-align: right; display: inline-block;   }
  .ttl1{  width:80px;  }
  .ttl2{  width:90px;   }
  .ttl3{  width:114px;  }
  .ttl4{  width:100px; padding:0 10px;   }

  .tipBlue{
    margin-left: 35px;
    color: #5c81ff;
    i{
      font-style: normal;
      color: $ty-color-orange;
    }
  }
  .contacCon>div{ margin-bottom: 5px; }
  .inputClass{ width: 180px; }
  .eduCon .txtRight{ width: 74px;  }
  .txtCenter{ text-align: center; }
  .txtRight{ text-align: right; padding: 2px 10px; width: 120px;  }
  .txtRight+td{ background: #fff; box-shadow: 0 0 1px #ccc;  height:24px;    }

  .eTtl{
    text-align: right;
    padding: 0 10px 0 14px;
  }
  .pad40{
    padding-left: 40px;
  }
}
</style>
