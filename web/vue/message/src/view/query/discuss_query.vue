<template>
  <div id="discussQuery" class="query discussQuery">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.timeBegin" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.timeEnd" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approveStatus" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div v-if="name !== 'apply'">
              <h4 class="item-header">申请人</h4>
              <div class="el-form-item">
                <span :class="queryForm.creator === 0?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <div class="handle_button" style="margin-top: 20px">
              <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit" v-loading.fullscreen.lock="loading">
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>

<script>
// import { formatDate, filterLeaveType } from '../../js/common'
export default {
  name: 'discussQuery',
  data () {
    return {
      loading: true,
      name: '',
      title: '',
      queryForm: {
        eventType: 1,
        type: '1',
        timeBegin: '',
        timeEnd: '',
        approveStatus: '2',
        creator: 0,
        applyName: '选择申请人'
      },
      roleList: [],
      bookVisible: false
    }
  },
  created: function () {
    let that = this
    let name = this.$route.params.name
    let title = ''
    switch (name) {
      case 'apply':
        title = '讨论发起申请'
        this.queryForm.eventType = 1
        break
      case 'approveMiddle':
        title = '讨论发起申请'
        this.queryForm.eventType = 2
        break
      case 'approveLast':
        title = '讨论发起审批'
        this.queryForm.eventType = 3
        break
    }
    this.title = title
    this.name = name
    this.$http.post('../../../forum/getAllParticipants.do', { userID: this.sphdSocket.user.userID, type: 5 }, {
      emulateJSON: true
    }).then((response) => {
      that.loading = false
      let data = response.body
      console.log(response.body)
      if (data) {
        that.roleList = data.listPostUser
        console.log(that.roleList)
      } else {
        console.log('加载失败！')
      }
    })
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      // feeCat 费用类别id
      // feeCatName 费用类别名称
      // billCat 票据种类 id
      // billCatName 票据种类名称
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        eventType: this.queryForm.eventType,
        type: this.queryForm.type,
        approveStatus: this.queryForm.approveStatus,
        timeBegin: this.queryForm.timeBegin,
        timeEnd: this.queryForm.timeEnd,
        fromUser: this.queryForm.creator
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/discussQueryPer',
        name: 'discussQueryPer',
        params: {
          data: queryParam
        }
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.creator = 0
        this.queryForm.applyName = '请选择申请人'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.creator = data.userID
      this.queryForm.applyName = data.userName
    }
  }
}
</script>
