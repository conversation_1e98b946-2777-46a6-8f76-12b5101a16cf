import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/register1",
      name: "register1",
      component: () => import("@/views/register/register-1.vue"),
    },
    {
      path: "/register2",
      name: "register2",
      component: () => import("@/views/register/register-2.vue"),
    },
    {
      path: "/register3",
      name: "register3",
      component: () => import("@/views/register/register-3.vue"),
    },
    {
      path: "/register4",
      name: "register4",
      component: () => import("@/views/register/register-4.vue"),
    },
    {
      path: "/register5",
      name: "register5",
      component: () => import("@/views/register/register-5.vue"),
    },
    {
      path: "/register6",
      name: "register6",
      component: () => import("@/views/register/register-6.vue"),
    },
    {
      path: "/officeList",
      name: "officeList",
      component: () => import("@/views/register/officeList.vue"),
    },
    // {
    //   path: "/pricePolicy",
    //   name: "pricePolicy",
    //   component: () => import("@/views/register/pricePolicy.vue"),
    // },
    // {
    //   path: "/userAgreement",
    //   name: "userAgreement",
    //   component: () => import("@/views/register/userAgreement.vue"),
    // },
    {
      path: "/lyricTask",
      name: "lyricTask",
      component: () => import("@/views/music/lyricTask.vue"),
    },
    {
      path: "/lyricTaskXiQu",
      name: "lyricTaskXiQu",
      component: () => import("@/views/xiqu/lyricTask.vue"),
    },
    {
      path: "/lyricTaskHandle",
      name: "lyricTaskHandle",
      component: () => import("@/views/music/lyricTaskHandle.vue"),
    },
    {
      path: "/lyricTaskApprove",
      name: "lyricTaskApprove",
      component: () => import("@/views/music/lyricTaskApprove.vue"),
    },
    {
      path: "/newLyric",
      name: "newLyric",
      component: () => import("@/views/music/newLyric.vue"),
    },
    {
      path: "/newLyricXiQu",
      name: "newLyricXiQu",
      component: () => import("@/views/xiqu/newLyric.vue"),
    },
    {
      path: "/lyricTip",
      name: "lyricTip",
      component: () => import("@/views/music/lyricTip.vue"),
    },
    {
      path: "/lyricTipXiQu",
      name: "lyricTipXiQu",
      component: () => import("@/views/xiqu/lyricTip.vue"),
    },
    {
      path: "/lyricTxtPat",
      name: "lyricTxtPat",
      component: () => import("@/views/music/lyricTxtPat.vue"),
    },
    {
      path: "/lyricTxtPatSave",
      name: "lyricTxtPatSave",
      component: () => import("@/views/music/lyricTxtPatSave.vue"),
    },
    {
      path: "/assignTasks",
      name: "assignTasks",
      component: () => import("@/views/music/assignTasks.vue"),
    },
    {
      path: "/lycUpload",
      name: "lycUpload",
      component: () => import("@/views/music/lycUpload.vue"),
    },
    {
      path: "/selectUser",
      name: "selectUser",
      component: () => import("@/views/music/selectUser.vue"),
    },
    {
      path: "/lyricDetails",
      name: "lyricDetails",
      component: () => import("@/views/music/lyricDetails.vue"),
    },
    {
      path: "/lyricDetails2",
      name: "lyricDetails2",
      component: () => import("@/views/music/lyricDetails2.vue"),
    },
    {
      path: "/lycInfo",
      name: "lycInfo",
      component: () => import("@/views/music/lycInfo.vue"),
    },
    {
      path: "/lycEdit",
      name: "lycEdit",
      component: () => import("@/views/music/lycEdit.vue"),
    },
    {
      path: "/edutLyricInfo",
      name: "edutLyricInfo",
      component: () => import("@/views/music/edutLyricInfo.vue"),
    },
    {
      path: "/commissionPolicy",
      name: "commissionPolicy",
      component: () => import("@/views/manage/commissionPolicy.vue"),
    },
    {
      path: "/lycTaskApply",
      name: "lycTaskApply",
      component: () => import("@/views/manage/lycTaskApply.vue"),
    },
    {
      path: "/lyricApplyDetails",
      name: "lyricApplyDetails",
      component: () => import("@/views/manage/lyricApplyDetails.vue"),
    },
    {
      path: "/lycTaskHandel",
      name: "lycTaskHandel",
      component: () => import("@/views/manage/lycTaskHandel.vue"),
    },
    {
      path: "/lyricTaskAlready",
      name: "lyricTaskAlready",
      component: () => import("@/views/manage/lyricTaskAlready.vue"),
    },
    {
      path: "/lyricTaskAlreadyInfo",
      name: "lyricTaskAlreadyInfo",
      component: () => import("@/views/manage/lyricTaskAlreadyInfo.vue"),
    },
    {
      path: "/lycTaskCharge",
      name: "lycTaskCharge",
      component: () => import("@/views/manage/lycTaskCharge.vue"),
    },
    {
      path: "/lycTaskChargeDetails",
      name: "lycTaskChargeDetails",
      component: () => import("@/views/manage/lycTaskChargeDetails.vue"),
    },
    {
      path: "/memberList",
      name: "memberList",
      component: () => import("@/views/manage/memberList.vue"),
    },
    {
      path: "/memberOutList",
      name: "memberOutList",
      component: () => import("@/views/manage/memberOutList.vue"),
    },
    {
      path: "/accountDetails",
      name: "accountDetails",
      component: () => import("@/views/my/accountDetails.vue"),
    },
    {
      path: "/about",
      name: "about",
      component: () => import("@/views/my/about.vue"),
    },
    {
      path: "/cancellation",
      name: "cancellation",
      component: () => import("@/views/my/cancellation.vue"),
    },
    {
      path: "/officeListMy",
      name: "officeListMy",
      component: () => import("@/views/my/officeListMy.vue"),
    },
    {
      path: "/userMsg",
      name: "userMsg",
      component: () => import("@/views/my/privateSpace/userMsg.vue"),
    },
    {
      path: "/accEditLog",
      name: "accEditLog",
      component: () => import("@/views/my/privateSpace/accEditLog.vue"),
    },
    {
      path: "/userOfficeList",
      name: "userOfficeList",
      component: () => import("@/views/my/privateSpace/userOfficeList.vue"),
    },
    {
      path: "/home",
      name: "home",
      component: () => import("@/views/home/<USER>"),
      children: [
        {
          path: "/homeData",
          name: "homeData",
          component:() => import("@/views/home/<USER>"),
        },
        {
          path: "/homeManage",
          name: "homeManage",
          component: () => import("@/views/home/<USER>"),
        },
        {
          path: "/homeHandle",
          name: "homeHandle",
          component: () => import("@/views/home/<USER>"),
        },
        {
          path: "/homeMy",
          name: "homeMy",
          component: () => import("@/views/home/<USER>"),
        },
      ],
    },
    {
      path: "/privateSpace",
      name: "privateSpace",
      component: () => import("@/views/my/privateSpace/privateSpace.vue"),
      children: [
        {
          path: "/mine",
          name: "mine",
          component: () => import("@/views/my/privateSpace/mine.vue"),
        },
        {
          path: "/life",
          name: "life",
          component:() => import("@/views/my/privateSpace/life.vue"),
        },
        {
          path: "/mailList",
          name: "mailList",
          component: () => import("@/views/my/privateSpace/mailList.vue"),
        },
        {
          path: "/manage",
          name: "manage",
          component: () => import("@/views/my/privateSpace/manage.vue"),
        },
        {
          path: "/remind",
          name: "remind",
          component: () => import("@/views/my/privateSpace/remind.vue"),
        }
      ],
    },
    {
      path: "/unFinish",
      name: "unFinish",
      component: () => import("@/views/my/unFinish.vue"),
    },
    {
      path: "/aboutApp",
      redirect: { name: 'unFinish' }
    },
    {
      path: "/pricePolicy",
      name: "pricePolicy",
      component: () => import("@/views/my/about/pricePolicy.vue"),
    },
    {
      path: "/release/:id",
      name: "release",
      component: () => import("@/views/my/about/release.vue"),
    },
    {
      path: "/usingHelp/:id",
      name: "usingHelp",
      component: () => import("@/views/my/about/usingHelp.vue"),
    },
    {
      path: "/appDownload",
      redirect: { name: 'unFinish' }
    },
    {
      path: "/aboutFileList/:id",
      name: "aboutFileList",
      component: () => import("@/views/my/about/aboutFileList.vue"),
    },
    {
      path: "/aboutSearchList/:name",
      name: "aboutSearchList",
      component: () => import("@/views/my/about/aboutSearchList.vue"),
    },
    {
      path: "/aboutFinalDetail/:module/:id",
      name: "aboutFinalDetail",
      component: () => import("@/views/my/about/aboutFinalDetail.vue"),
    },
    {
      path: "/aboutShareDetail/:name",
      name: "aboutShareDetail",
      component: () => import("@/views/my/about/aboutShareDetail.vue"),
    }
  ]
})

export default router
