<!-- 模板管理/模板修改 详情页 -->
<template>
  <div id="templateOrProjectDetails" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container">
      <div class="panel" v-if="isApprove && isSearch === 0">
        <div class="handle_button">
          <span class="ui-btn ui-btn_error" @click="confirm(0)">驳回</span>
          <span class="ui-btn ui-btn_info" @click="confirm(1)">批准</span>
        </div>
      </div>
      <div class="panel" v-if="isSearch === 1">
        <p>申请人：<span class="infoName">{{ dataInfo.approvalProcess.createName }}</span> <span>{{ dataInfo.approvalProcess.createDate | formatDay}}</span></p>
        <p>审批人：<span class="infoName">{{ dataInfo.approvalProcess.toUserName }}</span> <span>{{ dataInfo.approvalProcess.handleTime | formatDay }}</span></p>
        <div v-if="dataInfo.approvalProcess.approveStatus === '3'">
          驳回理由：{{dataInfo.approvalProcess.reason}}
        </div>
      </div>
      <el-tabs v-model="activeName" v-if="isUpdate" @tab-click="handleClick" stretch="true">
        <el-tab-pane label="修改后" name="new"></el-tab-pane>
        <el-tab-pane label="修改前" name="old"></el-tab-pane>
      </el-tabs>
      <div class="panel panel_model" v-if="proOrModel === 'model'">
        <h4 class="h4_title">{{dataInfo.data.mpTmpl.name}}</h4>
      </div>
      <div class="panel panel_product" v-if="proOrModel === 'product'">
        <h4 :class="isUpdate?'h4_title color-red':'h4_title'">{{dataInfo.data.product.name}}</h4>
        <div>所用模板 <span :class="isUpdate?'subtitle color-red':'subtitle'">{{dataInfo.data.mpTmpl.name}}</span></div>
      </div>
      <div class="panel">
        <div class="ui-alert">已重命名模块的数量 <b class="renameNumber">{{dataInfo.data.reNameList.length}}</b> 个</div>
        <table class="ui-table ui-table-striped" style="width: 70%">
          <thead>
          <tr>
            <td>原名称</td>
            <td>新名称</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.reNameList" v-bind:key="index">
            <td>{{item.name}}</td>
            <td>{{item.newName}}</td>
          </tr>
          </tbody>
        </table>
        <div class="ui-alert" style="margin-top: 16px">
          主套餐内的模块
        </div>
        <table class="ui-table ui-table-striped">
          <thead>
          <tr>
            <td>模块名称</td>
            <td>下辖的一级菜单数量</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.mainModuleList" v-bind:key="index">
            <td class="name">{{item.name}}</td>
            <td>{{item.topMenu || '--'}}个</td>
            <td>
              <span class="link-blue" @click="seeModuleDetail(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="ui-alert" style="margin-top: 16px">
          使用本模板的用户增值功能的模块
        </div>
        <table class="ui-table ui-table-striped">
          <thead>
          <tr>
            <td>模块名称</td>
            <td>下辖的一级菜单数量</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.increaseModuleList" v-bind:key="index">
            <td class="name">{{item.name}}</td>
            <td>{{item.topMenu || '--'}}个</td>
            <td>
              <span class="link-blue" @click="seeModuleDetail(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="ui-alert" style="margin-top: 16px">
          与本模板增值功能模块对应的已有套餐
        </div>
        <table class="ui-table ui-table-striped">
          <thead>
          <tr>
            <td>套餐名称</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.mpSetList" v-bind:key="index">
            <td class="name">{{item.name}}</td>
            <td>
              <span class="link-blue" @click="seeMenuDetail(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="97%">
      <div class="ui-alert">请输入驳回理由</div>
      <el-input
        type="textarea"
        placeholder="请输入内容"
        v-model="reason"
        maxlength="50"
        show-word-limit
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value="取消" @click="dialogVisible = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定"  @click="approve(0)">
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style>
.h4_title{
  font-size: 16px;
  margin: 8px 0;
  color: #333;
}
.subtitle{
  color: #333;
}
</style>
<script>
import { gohistory } from '../../../js/common'
export default {
  name: 'templateOrProjectDetails',
  data () {
    return {
      loading: true,
      activeName: 'new', // 修改前后默认
      code: '', // 从哪个菜单跳转过来的菜单code
      approveID: '',
      title: '',
      isSearch: '', // 是不是搜索
      proOrModel: '',
      isUpdate: false,
      isApprove: false,
      dataInfo: {
        approvalProcess: [],
        data: {
          product: {
            name: ''
          },
          mpTmpl: {
            name: ''
          },
          mainModuleList: [],
          increaseModuleList: [],
          mpSetList: [],
          reNameList: []
        }, // 用来展示的数据
        old: {}, // 用来切换修改前后的数据，切换时赋值到data中
        new: {} // 用来切换修改前后的数据，切换时赋值到data中（默认显示此数据）
      },
      dialogVisible: false,
      reason: '',
    }
  },
  created: function () {
    this.code = this.$route.params.code
    this.isSearch = Number(this.$route.params.isSearch)
    this.approveID = this.$route.params.id
    this.getDetail()
  },
  methods: {
    getDetail: function () {
      let that = this
      let subscribeID = ''
      let subscribeStr = ''
      switch (this.code) {
        case 'templateFoundApply':
          this.title = '模板创建'
          this.proOrModel = 'model'
          subscribeStr = 'templateFoundApplyList'
          break;
        case 'templateFoundApproval':
          this.title = '模板创建'
          this.proOrModel = 'model'
          this.isApprove = true
          subscribeStr = 'templateFoundApprovalList'
          break
        case 'templateEditApply':
          this.title = '模板修改'
          this.proOrModel = 'model'
          this.isUpdate = true
          subscribeStr = 'templateEditApplyList'
          break
        case 'templateEditApproval':
          this.title = '模板修改'
          this.proOrModel = 'model'
          this.isApprove = true
          this.isUpdate = true
          subscribeStr = 'templateEditApprovalList'
          break
        case 'productFoundApply':
          this.title = '产品创建'
          this.proOrModel = 'product'
          subscribeStr = 'productFoundApplyList'
          break
        case 'productFoundApproval':
          this.title = '产品创建'
          this.proOrModel = 'product'
          this.isApprove = true
          subscribeStr = 'productFoundApprovalList'
          break
        case 'productEditApply':
          this.title = '产品修改'
          this.proOrModel = 'product'
          this.isUpdate = true
          subscribeStr = 'productEditApplyList'
          break
        case 'productEditApproval':
          this.title = '产品修改'
          this.proOrModel = 'product'
          this.isUpdate = true
          this.isApprove = true
          subscribeStr = 'productEditApprovalList'
          break
        case 'productRecoveryApply':
          this.title = '产品恢复使用'
          this.proOrModel = 'product'
          subscribeStr = 'productRecoveryApplyList'
          break
        case 'productRecoveryApproval':
          this.title = '产品恢复使用'
          this.proOrModel = 'product'
          this.isApprove = true
          subscribeStr = 'productRecoveryApprovalList'
          break
      }
      console.log('this.isUpdate', this.isUpdate)
      if(this.$route.params.isSearch === 1){
        this.title = '我的消息'
      }
      that.axios.post('../../../thali/getApplyInfo.do', { approvalId: this.approveID })
        .then(res => {
          that.loading = false
          let data = res.data.data
          that.dataInfo = data
          let thisData = {
            approvalProcess: data.approvalProcess || {},
            data: {}, // 用来展示的数据
            old: {}, // 用来切换修改前后的数据，切换时赋值到data中
            new: {} // 用来切换修改前后的数据，切换时赋值到data中（默认显示此数据）
          }
          // 将数据改为一致的结构 好展示
          if (this.proOrModel === 'model') {
            if (this.isUpdate) {
              // 模板修改 (暂未开发)

            } else {
              thisData.new = {
                mpTmpl: {
                  name: data.name
                },
                mainModuleList: data.mainModuleList || [],
                increaseModuleList: data.increaseModuleList || [],
                mpSetList: data.mpSetList || []
              }
              thisData.new.reNameList = data.midRenames ? JSON.parse(data.midRenames) : []
            }
          } else if (this.proOrModel === 'product') {
            if (this.isUpdate) {
              // 产品修改
              thisData.new = data.packageInfo
              thisData.new.product = {
                name: data.name
              }
              thisData.old = data.oldPackageInfo
              thisData.old.product = data.oldPackageInfo.mpPackages
            } else {
              // 产品创建
              thisData.new = {
                product: {
                  name: data.name
                },
                mpTmpl: data.mpTmpl || {},
                reNameList: data.reNameList || [],
                mainModuleList: data.mainModuleList || [],
                increaseModuleList: data.increaseModuleList || [],
                mpSetList: data.mpSetList || []
              }
            }
          }
          thisData.data = thisData.new
          that.dataInfo = thisData
          console.log('dataInfo', that.dataInfo)
      })
    },
    confirm: function (approveStatus) {
      let that = this
      let tipStr = '确定同意该申请吗？'
      switch (this.code) {
        case 'templateFoundApproval':
          tipStr = '确定同意创建该模板吗？'
          break
        case 'templateEditApproval':
          tipStr = '确定同意修改该模板吗？'
          break
        case 'productFoundApproval':
          tipStr = '确定同意创建该产品吗？'
          break
        case 'productEditApproval':
          tipStr = '确定同意修改该产品吗？'
          break
        case 'productRecoveryApproval':
          tipStr = '确定同意恢复该产品吗？'
          break
      }
      if (approveStatus === 1) {
        this.$confirm(tipStr, '!提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.approve(1)
        })
      } else {
        this.dialogVisible = true
        this.reason = ''
      }
    },
    approve: function(approveStatus) {
      this.loading = true
      let url = ''
      switch (this.code) {
        case 'templateFoundApproval':
          url = '../../../thali/approvalMpTmplApply.do'
          break
        case 'templateEditApproval':
          url = '../../../thali/approvalMpTmplEditApply.do'
          break
        case 'productFoundApproval':
          url = '../../../thali/approvalMpPackagesApply.do'
          break
        case 'productEditApproval':
          url = '../../../thali/approvalMpPackagesEditApply.do'
          break
        case 'productRecoveryApproval':
          url = '../../../thali/approvalPackageRecoveryApply.do'
          break
      }
      let data = {
        approvalId: this.approveID,
        approveStatus: approveStatus
      }
      if (approveStatus === 0) {
        data.reason = this.reason
      }
      this.loading = true
      let that = this
      this.axios.post(url, data)
        .then(res => {
          that.loading = false
          let data = res.data.data
          if(data === 1) {
            that.$kiko_message('操作成功')
            gohistory(that)
          } else if (data === 2) {
            that.$kiko_message('已审批过，无需重复审批！')
          } else {
            that.$kiko_message('操作失败，请查明原因！')
          }
        })
    },
    seeModuleDetail: function (item) {
      this.$router.push({
        path: `/moduleDetail`
      })
      localStorage.setItem('moduleInfo', JSON.stringify(item))
    },
    seeMenuDetail: function (item) {
      this.$router.push({
        name: 'menuDetail'
      })
      localStorage.setItem('menuInfo', JSON.stringify(item))
    },
    handleClick: function(tab, event) {
      this.dataInfo.data = this.dataInfo[tab._props.name]
      console.log('tab', tab._props.name)
    }
  }
}
</script>
