.mainCon{padding-left: 100px;display: none;}
.mainCon3,.mainCon4,.mainCon5{width: 90%;min-width: 960px;}
.unSection{margin-bottom:  40px;}
.lineQuery{margin-bottom:  20px;}
.unSection > span:nth-child(2){ margin-left:  200px;}
.unSection > span:last-child{ margin-left:  100px;}
.linkBtn {  color: #036bc0;cursor: default;font-weight: 700;}
.redLinkBtn {  color: #f80202;cursor: default;font-weight: 700;}
.tipCon{overflow: hidden;padding-top:20px;font-size: 12px;}
.spTtl{display: inline-block; width: 160px;}
.backPage{margin-bottom:  20px;}
.wrapLab{margin: 20px auto;width: 900px;}
.wrapLab .ty-table tbody td{padding: 10px 2px;text-align: left;}
.modeSelect .fa{margin-right: 10px;}
.modelPattern{margin-left: 100px;padding: 10px;}
.sectCon1,.sectCon{padding: 20px 0;}
.hrLine, .packageItem{border-top:  1px solid #949393;}
.sectCon input{width: 120px;}
.red {color: #f80202;}
.fmWrapper{margin: auto;width: 400px;}
.pack_form li{ padding: 10px 0;position: relative;}
.pack_form .ty-inputText,.pack_form .ty-inputSelect{ width: 100%;}
.clearInputVal {position: absolute;  top: 30px;  right: 4px;  color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
input:focus + i.clearInputVal{  display: block;  }
.pannelTab td{ padding:10px 50px; width: 462px;  }
.pannelTab td > div{ padding-left:2px;  }
.pannelTab{ width: 924px; margin: 10px auto;}
.line{ border-top: 1px solid #bfbfbf; width: 830px; margin: 0 auto; }
#selectPackage .bonceCon input,#selectPackage .bonceCon select{width: 100%;}
#selectPackage .bonceCon input:disabled, #selectPackage .bonceCon select:disabled, #commodityPacking .bonceCon input:disabled{background: #dddddd;}
#selectPackage .bonceCon .weightText{width: 260px;}
#selectPackage .bonceCon .weightSelect{width: 98px;}
.manualEct{margin: auto;width: 600px;}
.manualEct p{margin-bottom: 20px;}
.manualEct p > span{display: inline-block; width: 136px;}
.areaCon{position: relative;}
.usageUnitName,.unitVal{position: absolute;  right: 4px;  line-height: 30px;  font-size: 14px;}
.bTip{color: #366091}
.shapeCon{margin-top: 20px;padding-top: 20px;border-top: 1px solid #c2c1c1;}
.topName{padding: 20px 0;border-bottom: 1px solid #c2c1c1;}
.gapTtl{display: inline-block; width: 200px;}
.linkWrap .gap{margin-left: 140px;margin-right: 50px;}
.pckRow{padding: 20px 0 0 20px;}
.outer_form li{ padding: 10px 0;}
.outer_form li input{ width: 336px;}
.outerShape{display: flex;justify-content: space-between;}
.outerShape input{margin-right: 14px;}
.mainRows,.mainOnlyRow{padding-top: 20px;}
.gapLt{margin-left: 50px;}
.bonceCon hr{border-bottom: 1px solid #b1b1b1;}
.packageScanItem{padding-bottom: 20px;margin-bottom: 30px;border-bottom: 1px solid #b1b1b1;}
.packageScanItem:last-child{border-bottom: none;}
.keywordSearch input,.bonceCon .keywordSearch input {padding: 0 10px;font-size: 12px;min-width: 200px;height: 28px;line-height: 28px;border: 1px solid #b6b6b6;}
.limitWid{padding-bottom: 20px;}
.limitWid span:first-child{display: inline-block; width: 60px;}
.effectTimeCon{margin-top: 20px;padding-top: 20px;border-top: 1px solid #c1c1c1;}
.searchIcon{float: left;background: url(../accountant/icon/search.png) no-repeat center;background-size: 22px;height: 28px;width: 28px;cursor:pointer;}
.searchSect .searchBtn{padding: 0 22px;height: 28px;line-height: 28px;}










































