package cn.sphd.miners.modules.orderReview.service;

import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReviewHistory;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ITSlOrdersItemReviewHistoryService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlOrdersItemReviewHistory selectTSlOrdersItemReviewHistoryById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 集合
     */
    public List<TSlOrdersItemReviewHistory> selectTSlOrdersItemReviewHistoryList(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory);

    /**
     * 新增
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 结果
     */
    public int insertTSlOrdersItemReviewHistory(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory);

    /**
     * 修改
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 结果
     */
    public int updateTSlOrdersItemReviewHistory(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewHistoryByIds(Long[] ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewHistoryById(Long id);
}
