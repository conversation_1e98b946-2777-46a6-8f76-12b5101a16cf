package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Id;

/**
 * Created by khmsoft on 2017/1/11.
 */
@Alias("subjectDef")
public class SubjectDefEntity {
    private String subject;
    private String name;
    private int level;
    private String parent;
    private Integer orders;
    private  Integer maxChildSubjects;
    private Integer category;
    private  Integer leafCategory;
    private Byte enabled;
    private String balanceDirection;
    private String measureUnit;
    private String memo;

    @Id
    @Column(name = "subject", nullable = false, length = 10)
    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    @Basic
    @Column(name = "name", nullable = true, length = 50)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "level", nullable = false)
    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    @Basic
    @Column(name = "orders", nullable = true)
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "category", nullable = true)
    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    @Basic
    @Column(name = "enabled", nullable = true)
    public Byte getEnabled() {
        return enabled;
    }

    public void setEnabled(Byte enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "balance_direction", nullable = true, length = 1)
    public String getBalanceDirection() {
        return balanceDirection;
    }

    public void setBalanceDirection(String balanceDirection) {
        this.balanceDirection = balanceDirection;
    }

    @Basic
    @Column(name = "measure_unit", nullable = true, length = 50)
    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    @Basic
    @Column(name = "memo", nullable = true, length = 100)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public Integer getMaxChildSubjects() {
        return maxChildSubjects;
    }

    public void setMaxChildSubjects(Integer maxChildSubjects) {
        this.maxChildSubjects = maxChildSubjects;
    }

    public Integer getLeafCategory() {
        return leafCategory;
    }

    public void setLeafCategory(Integer leafCategory) {
        this.leafCategory = leafCategory;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SubjectDefEntity that = (SubjectDefEntity) o;

        if (level != that.level) return false;
        if (subject != null ? !subject.equals(that.subject) : that.subject != null) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (orders != null ? !orders.equals(that.orders) : that.orders != null) return false;
        if (category != null ? !category.equals(that.category) : that.category != null) return false;
        if (enabled != null ? !enabled.equals(that.enabled) : that.enabled != null) return false;
        if (balanceDirection != null ? !balanceDirection.equals(that.balanceDirection) : that.balanceDirection != null)
            return false;
        if (measureUnit != null ? !measureUnit.equals(that.measureUnit) : that.measureUnit != null) return false;
        if (memo != null ? !memo.equals(that.memo) : that.memo != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = subject != null ? subject.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + level;
        result = 31 * result + (orders != null ? orders.hashCode() : 0);
        result = 31 * result + (category != null ? category.hashCode() : 0);
        result = 31 * result + (enabled != null ? enabled.hashCode() : 0);
        result = 31 * result + (balanceDirection != null ? balanceDirection.hashCode() : 0);
        result = 31 * result + (measureUnit != null ? measureUnit.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        return result;
    }
}
