<template>
  <div id="issueFile" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="文件发布/换版/废止审批" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="datePane">今天是{{date_today}}  {{weekDay}}</div>
      <div class="ui-cells">
        <a class="ui-cell" v-for="(item, index) in listHis" :key="index" v-if="filterFile !== item.id" v-on:click="applyTo(item.id)">
          <div class="fileLists">
            <div class="fileType file_xls"></div>
            <div class="fileInfo">
              <div class="fileName" v-text="item.name" :title="item.name"></div>
              <div class="fileDetail">
                {{(!item.updateName || item.updateName === null || item.updateName === undefined)?item.createName:item.updateName}}
                {{(!item.updateDate || item.updateDate === null || item.updateDate === undefined)?item.createDate:item.updateDate}}
              </div>
            </div>
            <div class="fileFormat">
              <span class="fileVersion">G{{item.changeNum}}</span>
              <span class="fileNo">编号：{{item.fileSn}}</span>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import * as moment from 'moment'
import 'moment/locale/zh-cn'
export default {
  name: 'approveHistory',
  data () {
    return {
      loading: true,
      isBackHome: true,
      filterFile: 0,
      weekDay: '',
      date_today: moment(new Date()).format('YYYY/MM/DD'),
      listHis: [],
      listenersUid: []
    }
  },
  created: function () {
    let today = new Date()
    let week = today.getDay()
    let weekArr = ['日', '一', '二', '三', '四', '五', '六']
    this.weekDay = '周' + weekArr[week]
    let _this = this
    let parame = {
      'userID': _this.sphdSocket.user.userID
    }
    this.$http.post('../../../res/approveFileByFinal.do', parame, {
      emulateJSON: true
    }).then((response) => {
      let handle = response.body.data
      _this.listHis = handle.resHitoryList
      _this.loading = false
    }).catch(function () {
      _this.loading = false
      _this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
    this.listenersUid = [
      this.sphdSocket.subscribe('approveFileByFinal', function (data) { // 当前审批人选择进行最终审批后
        console.log('终审审批列表：' + data)
        let reqData = JSON.parse(data)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          console.log('增加一条')
          newDate = reqData.resHis
          _this.listHis.push(newDate)
        } else if (update < 0) { // 减少一条
          console.log('减少一条')
          let deletId = reqData.resHis.id
          _this.listHis.forEach(function (item, index) {
            if (deletId === item.id) {
              _this.listHis.splice(index, 1)
            }
          })
        }
      }, null, 'custom', this.sphdSocket.org.id + 'rbgeneral') // 机构id+rb
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    applyTo: function (id) {
      this.$router.push({
        path: `/approveHisDetails/${id}`
      })
    },
    screen: function () {
      this.$router.push({
        path: '/approveFileQuery',
        name: 'approveFileQuery',
        params: {
          event: 4
        }
      })
    }
  }
}
</script>
