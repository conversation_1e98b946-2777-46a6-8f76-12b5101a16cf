ul,li{  margin: 0;  padding:0;  list-style: none;  }
input,select,textarea,select{ outline: 0 }
textarea {  resize: none; }
.page-header.navbar .page-logo > a.goHome{ float: right;  font-size: 27px; margin: 7px -57px;  color: #bbb;}
.page-header.navbar .page-logo > a.goHome:hover{ color:#eaeaea;}
div.page-content-wrapper .page-content{ padding:0; overflow-x: auto ;  background-color: #fcfcfc; height: 100%}
.page-header-fixed .page-container{ margin-top:46px; height: calc(100% - 46px);           }
.page-header.navbar .page-logo{ background-color:#2B3643;   }
.page-content-wrapper{ position: relative; height: 100% }

@media (min-width: 992px){
    .page-sidebar-wrapper {width: 185px;}
    .page-sidebar-wrapper .page-sidebar { width: 185px;  }
    .page-content-wrapper .page-content {  margin-left:185px;}
}
@media (max-width: 991px){
    .page-sidebar-wrapper .page-sidebar {width:100%;  }
    .page-content-wrapper .page-content {  margin-left:0 }
}
.page-header.navbar .menu-toggler{ background-img:none; color:#fff;     }
.page-header.navbar .menu-toggler>span{ border:1px solid #fff; padding: 3px; border-radius:2px;    }
.page-sidebar-wrapper{ position: fixed; left: 0; top:46px; overflow-y: auto; overflow-x: hidden; z-index:2; height: calc(100% - 46px);}
.ty-headerFloat { position: fixed;  top: 46px; z-index: 999; background: #fff; }
.ty-headerFloat >footer{ height:15px; position: absolute; bottom:0; background: #f0f8ff; width:100%; left:0; border-top:2px solid #cccc; }
.ty-containerFloat{  margin-top: 180px;}
.ty-left{ float: left;  }
.ty-right{ float: right;  }

.ty-center {  text-align: center;  }
.clr{ clear:both;   }
a{ cursor: pointer  }
a:hover{ text-decoration:none;   }

.ty-container, .ty-header { font-size:14px; font-family: "Microsoft Yahei"; color: #101010;      }
.ty-container{ margin-top:35px; padding:10px;  }

.ty-header{ position:fixed; width:100%; left:185px;z-index:999;
    background:#e6e6e6;
    /*background:red;*/
}
.ty-header>ul {  }
.ty-header>ul>li{ display:inline-block;  background: #eee;  height:30px; line-height:30px; padding:0 15px; position: relative; margin-right: 5px ;border-radius: 1px 1px 0 0  }
.ty-header>ul>li>span{ margin-right:5px; color: #999 }
.ty-header>ul>li>i{ color:#909fb3;   }
.ty-header>ul>li.active{  background-color: #fcfcfc;  }
.ty-header>ul>li.active span{  color: #666;  }
.ty-header>ul>li.active i{  color: #999;  }
i.close_x{ display: inline-block; width: 12px;height:1px; background: #999;line-height: 0;font-size:0;vertical-align: middle;-webkit-transform: rotate(45deg); margin-top: -2px;}
i.close_x:after{content:'/';display:block;width: 12px;height:1px; background: #999;-webkit-transform: rotate(-90deg);}


.nav_{ font-family: 仿宋; font-size: 14px;  }
.navTxt{ color:#48cfad; font-weight:normal; font-size:14px; font-family:Microsoft YaHei;      }
.navTxt a{ color:#48cfad;}
.navTxt a:hover{ color:#37bc9b; text-decoration:underline; cursor:pointer;  }

/* 一级导航  */
.ty-firstTab{ line-height:40px;font-size:14px; height:40px;    }
.ty-firstTab li{ list-style:none; display:inline-block; padding:0 30px; cursor:pointer; background: #f2f2f2       }
.ty-firstTab li.ty-active{ color:#fff; background: #48cfad }

/* 二级导航栏 */
.ty-secondTab{ border-bottom:1px solid #eaeaea;  font-size:14px; line-height:50px; height:50px;      }
.ty-secondTab li{ list-style:none; display:inline-block; height:50px; padding:0 20px; cursor:pointer;      }
.ty-secondTab li.ty-active{ border-bottom:2px solid #48cfad; color:#48cfad;   }

/*  table  */
.ty-table{ width:100%;   }
.ty-table td{ border:1px solid #d7d7d7; font-size:14px; text-align:center; padding:0 15px; height:40px;  word-wrap: break-word;  word-spacing:normal;word-break: break-all ;color:#101010     }
.ty-table thead td{ border-color:#fff #fff #d7d7d7 #fff ;                   }
.ty-table tbody td{ background-color:#fff;                 }
.ty-table tbody tr:hover td{ background-color:#f0f8ff;              }
.ty-table-control tbody tr td span{ cursor:pointer; white-space:nowrap  }
.ty-table-control tbody tr td span{ cursor:pointer; white-space:nowrap;  }

/* table button controller */
.ty-color-gray { color:#aaa;   }
.ty-color-blue { color:#5d9cec;   }
.ty-color-green { color:#48cfad;   }
.ty-color-red { color:#ed5565;   }
.ty-color-cyan { color:#a0d468;   }
.ty-color-orange { color:#f58410;   }
.ty-color-darkBlue { color:#3f5266;  }
.color6{ color:#666;   }
.colorC{ color:#ccc;   }
.color3{ color:#333;   }
.colorGray{ color:#aaa;   }
.colorBlue{ color:#5d9cec;   }
.colorOrange{ color:#f58410;   }

.ty-table-control td:last-child span ,
.ty-table-control td:last-child a ,
td.ty-td-control span ,
td.ty-td-control a
{ font-size:12px; font-family: 宋体;  padding:5px 15px 5px; border-radius:3px; font-weight:bold; cursor:pointer;   }
.ty-table-control  td:last-child input{ width: 100%        }
.ty-table-control  td:last-child select{ border: 1px solid #ccc;  display: inline-block;  height: 30px;  line-height: 30px; width: 100%      }
.ty-table-control .ty-color-blue:hover,
.ty-td-control .ty-color-blue:hover { color:#fff; background-color:#5d9cec; transition: all .2s    }
.ty-table-control .ty-color-green:hover,
.ty-td-control .ty-color-green:hover{ color:#fff; background-color:#48cfad;     }
.ty-table-control .ty-color-red:hover, .ty-td-control .ty-color-red:hover{ color:#fff; background-color:#ed5565;     }
.ty-table-control .ty-color-cyan:hover, .ty-td-control .ty-color-cyan:hover{ color:#fff; background-color:#a0d468;     }
.ty-table-control .ty-color-orange:hover, .ty-td-control .ty-color-orange:hover{ color:#fff; background-color:#f58410;     }
.ty-table-control .ty-color-darkBlue:hover, .ty-td-control .ty-color-darkBlue:hover{ color:#fff; background-color:#3f5266;     }
.ty-table-none tbody tr td{ border:none; background-color: transparent; }
.ty-table-control .ty-color-gray:hover, .ty-td-control .ty-color-gray:hover{ color:#fff; background-color:#aaa;     }
.ty-table-left td {  text-align: left;  }

.ty-table td.ty-td-gray, .ty-table tbody tr:hover td.ty-td-gray {
    background: #eaeaea;
}
/*   button   */
.ty-btn{ padding:0 10px;cursor: pointer; background-color:#fff;color:#101010; height:24px; line-height:24px; display:inline-block; border:none       }
.ty-btn-big{ padding:0px 24px;height:32px; line-height:32px;        }
.ty-btn-gray{ background-color: #e8e8e8; color:#666;   }
.ty-btn-green{ background-color:#48cfad; color:#fff;      }
.ty-btn-cyan{ background-color:#a0d468; color:#fff;      }
.ty-btn-red{ background-color:#ed5565; color:#fff;      }
.ty-btn-blue{ background-color:#5d9cec; color:#fff;      }
.ty-btn-blue.active{ background-color: #5d93d9; color:#fff;      }
.ty-btn-orange{ background-color:#f58410; color:#fff;      }
.ty-btn-yellow{ background-color: #ffff00; color:#000;      }
.ty-btn-green:hover{ color:#fff;      }
.ty-btn-red:hover{ color:#fff;      }
.ty-btn-blue:hover{ color:#fff; background-color: #5d96e6;     }
.ty-btn-orange:hover{ color:#fff;      }
.ty-btn-disabled{  cursor: default;  background-color: #ccc;  color:#666;  }
.ty-btn-disabled:hover{  cursor: default;  background-color: #ccc;  color:#666;  }
.ty-btn:disabled {cursor: default;  background-color: #ccc;  color:#666;}

/*  circle   */
.ty-circle-5{ border-radius:5px;   }
.ty-circle-3{ border-radius:3px;   }
.ty-circle-10{ border-radius:10px;   }

/*input*/
.ty-inputText,.ty-textarea,.ty-inputSelect { background-color: #fff;padding:0 10px; font-size:12px; border-radius:5px; min-width:182px;  color: #666;  }
.ty-inputText:disabled,.ty-textarea:disabled,.ty-inputSelect:disabled{  background-color: #dff0ff;  }
.ty-inputText:focus,.ty-textarea:focus,.ty-inputSelect:focus{  border:1px solid #48cfad; }
.ty-table .ty-inputText,.ty-table .ty-textarea{  border: 1px solid #e3e3e3 ; border-radius: 0 }
.ty-table .ty-inputText:focus,.ty-table .ty-textarea:focus{  border: 1px solid #9fc6db }
/*search*/
.ty-search , .ty-searchInput,.ty-searchBtn{  display:inline-block; height:32px;border:none;  }
.ty-search{  position:relative;       }
.ty-search-ttl{padding:0 15px 0 0 ;     }
.ty-searchInput{ padding:0 10px; font-size:12px; border-radius:5px; min-width:182px; border:1px solid #5d9cec;  }
input.ty-searchInput:focus{ outline: 2px solid #5d9cec20;  }
.ty-searchBtn{ width:32px; background:#5d9cec url(icon/search.png) no-repeat;background-position:center center; background-size:20px 20px; border-radius:0 5px 5px 0; position:absolute; top:0;right:0;          }
.ty-searchBtn:hover{ background-color:#5d9cec;   }


/*text-align:*/
.text-right{ text-align:right;   }
.text-left{ text-align:left;   }
.text-center{ text-align:center;   }

/*  process  */
.ty-process{ position:relative;  margin:20px 0 0 0 ;   }
.ty-process-ttl{ position:absolute; top:-12px; left: 15px; }
.ty-process-container{ margin-left:35px;border-left:1px solid #d7d7d7;margin-top:8px; min-height:100px;  padding-top:15px;     }
.dot , .dot-no , .dot-wait { padding:4px;border-radius:4px;  position:absolute; left:-4px;top:20px;    }
.dot{  background-color:#48cfad;   }
.dot-no{ background-color:#ed5565;  }
.dot-wait{ background-color:#ccc;   }
.ty-process-item{ padding:9px 15px; line-height:20px; position:relative; line-height:30px; font-size:12px;        }
.ty-process-item p{ margin:0;  }
.ty-process-wait{ color:#a7a7a7 ;   }
.ty-process-item article{ word-wrap:break-word;   }
.ty-panel{ background:#f0f0f0;   }
.ty-procon{ padding:0 10px;    }
.ty-mainData{  margin:20px 0;     }

/*   bounce    */
.bounce_fixed{ position:fixed;  }
.bounce{ position:absolute;  }
.bounce{display:none; background: rgba(100,100,100,0.7);width: 100%; z-index:9999; min-height:100%;   } /*.bounce_fixed*/
.bounce_Fixed2 { position: fixed; z-index: 10002; background: rgba(100, 100, 100, 0.5);  }
.bounce_Fixed3 { position: fixed; z-index: 10005; background: rgba(100, 100, 100, 0.5);  }
.bonceContainer{ display:none;  margin:200px auto 100px;background:#f0f8ff; width:460px;border-radius:5px;     }

.bonceCon-center {  text-align: center;  }
.bounce_ttl {  width: 100px;  text-align: right;  display: inline-block;  padding-right: 15px;  }
.bounce_Fixed_fixed {  position: fixed;  min-height: 800px;  }
.bounce_Fixed {  position: fixed;  min-height: 100%;  }
.bounce_Fixed, .bounce_Fixed_fixed { display: none; background: rgba(100, 100, 100, 0.5); width: 100%; padding-bottom: 200px; z-index: 10001;  }

.bonceHead, .bonceCon , .bonceFoot{ background-color:#fff;  }
.bonceHead{ border-bottom:1px solid #eaeaea;height:40px; font-size:16px; line-height:40px;padding:0 15px;background-color:#fff; border-radius:5px 5px 0 0;    }
.bounce-blue .bonceHead{ border-bottom:1px solid #5d9cec; color:#5d9cec;  }
.bounce-red .bonceHead{ border-bottom:1px solid #ed5565; color:#ed5565;   }
.bounce-orange .bonceHead{ border-bottom:1px solid #f58410; color: #f58410; }
.bounce-green .bonceHead{ border-bottom:1px solid #48cfad; color: #48cfad;  }
.bounce-cyan .bonceHead{ border-bottom:1px solid #a0d468; color: #a0d468;  }

.bounce-blue .bounce-ok {  background: #5d9cec;  color: #fff;  }
.bounce-red .bounce-ok {  background: #ed5565;  color: #fff;  }
.bounce-green .bounce-ok {  background: #48cfad;  color: #fff;  }
.bounce-orange .bounce-ok {  background: #f58410;  color: #fff;  }
.bounce-cyan .bounce-ok {  background: #a0d468;  color: #fff;  }
.bonceFoot .bounce-cancel {  background: #e8e8e8;  }

.bounce_close{ width:16px; height:16px; background:url(icon/close.png)  no-repeat; background-position:0 -32px;  display:inline-block;float:right;   margin:10px 0; border-radius:11px;  }
.bounce_close:hover{ cursor:pointer; background-color:#eaeaea;   }
.bounce-orange .bounce_close{ background-position:0 0;    }
.bounce-red .bounce_close{ background-position:0 -16px;   }
.bounce-blue .bounce_close{ background-position:0 -32px;     }
.bounce-green .bounce_close{ background-position:0 -48px;    }
.bounce-cyan .bounce_close{ background-position:0 -48px;    }
.bonceCon{ padding:10px 15px;  background-color: #f0f8ff; max-height: 550px; overflow-y: auto }
.bonceCon input:not([type='radio']):not([type='checkbox']),.bonceCon select{  border:1px solid #dcdfe6; border-radius:2px; background-color: #fff;display:inline-block;  height:30px; line-height:30px;  background-image: none;  box-sizing: border-box;  color: #606266;  font-size: inherit;  outline: none;  padding: 0 8px;  transition: border-color .2s cubic-bezier(.645,.045,.355,1);  }
.bonceCon input:focus,.bonceCon textarea:focus{ border:1px solid #98e0ce;  background-color: #fff  }
.bonceCon textarea{  border:1px solid #dcdfe6;background-color: #fff;display:inline-block; line-height:18px;  width: 182px;padding: 8px }
/*.bonceCon select{  border:none;background-color: #fff;display:inline-block;  height:30px; line-height:30px; }*/
.bonceCon option{  height:30px; line-height:30px; }
.bonceFoot{ background-color: #f0f8ff;text-align:right; padding:10px 15px 20px;border-radius:0 0 5px 5px;  }

/*select*/
.ty-select{ display:inline-block; position:relative; cursor:default;      }
.ty-opTTL{  min-width:70px; height:30px; width: 100%;       }
.ty-opTTL>option:first-child{ border:none; background-color:#eee; border-radius:5px 0 0 5px; height:30px; line-height:25px; padding:0 0 0  15px; width:100%; display:block; text-align:left ;     }
.ty-down{ display:inline-block; width:25px; height:30px; position:absolute; left:100%;top:0; background:#48cfad url(icon/ty-down.png)  no-repeat center center !important; border-radius:0 5px 5px 0;       }
.ty-up{ display:inline-block; width:25px; height:30px; position:absolute; left:100%;top:0; background:#48cfad url(icon/ty-down.png)  no-repeat center center !important;  border-radius:5px 0 0 5px;  transform:rotate(180deg);  -ms-transform:rotate(7deg); 	/* IE 9 */  -moz-transform:rotate(180deg); 	/* Firefox */  -webkit-transform:rotate(180deg); /* Safari 和 Chrome */  -o-transform:rotate(180deg); 	      }
/*.ty-down{ height:4px;width:8px; background:; display:inline-block;  margin:13px 8px ;         }*/
.ty-opItems{ position:absolute; z-index:9999; background:#fff; top:30px; border:1px solid #ddd; border-top:none;  display:none; width:100%; max-height:200px; overflow-y: auto; line-height:30px;           }
.ty-opItem{ padding-left: 10px;  height:25px; overflow:hidden;display:block; background-color:#fff;            }
.ty-opItem:hover, .ty-select .ty-opItems .ty-active{ background-color:#48cfad; color:#fff;    }
.ty-select-bg .ty-opTTL>option:first-child{  background-color:#f0f8ff;   }


/*按钮组*/
.ty-btn-group{  position: relative;  display: inline-block;  vertical-align: middle; white-space: nowrap ;font-size: 0;  -webkit-text-size-adjust:none;}
.ty-btn-group > .ty-btn {  position: relative;   background-color: #f2f2f2;  color: #aaa; font-size: 14px }
.ty-btn-group > .ty-btn:last-child:not(:first-child) {  border-top-left-radius: 0;  border-bottom-left-radius: 0;  }
.ty-btn-group > .ty-btn:first-child:not(:last-child):not(.dropdown-toggle) {  border-top-right-radius: 0;  border-bottom-right-radius: 0;  }
.ty-btn-group > .ty-btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {  border-radius: 0;  }
.ty-btn-group > .ty-btn-active-green{  background-color:#48cfad; color:#fff;  }
.ty-btn-group > .ty-btn-active-blue{  background-color:#5d9cec; color:#fff;  }
.ty-btn-group > .ty-btn-active-red{  background-color:#ed5565; color:#fff;  }
/* 组合数字加减号 */
.ty-btn-group > .ty-inputNum{  float: left;  background-color: #fff; font-size:12px; width:48px;  color: #666; text-align: center ;border:1px solid #e3e3e3 ;margin-left: -1px; margin-right: -1px; }
.ty-btn-group > .ty-inputNum:focus{  background-color: #fff;}
.ty-btn-group > .ty-inputAdd,.ty-btn-group > .ty-inputReduce{  float: left; height:30px; line-height:30px; background-color: #fff;padding:0 10px; font-size:12px;  color: #666; border:1px solid #e3e3e3  }
.ty-btn-group > .ty-inputAdd:hover,.ty-btn-group > .ty-inputReduce:hover{  background-color: #48cfad;color: #fff; border:1px solid #48cfad  }

/*隐藏*/
.hd{ display:none;  }

/*dropdown Container 下拉框   */
.ty-dropdown{ position:relative; display:inline-block;  }
.ty-dropdownBtn{ display:inline-block; height:35px;   }
.ty-dropdownCon{ display:none; position:absolute; top: 45px; background:#f0f8ff; box-shadow: 3px 3px 3px #ccc; border:1px solid #48cfad;     }
/* arrow 向上小箭头 */
.ty-dropdown .ty-dropdownCon .ty-trigl-1 , .ty-tip .ty-tipcon .ty-trigl-1
{  padding:0 ; margin:0 ; width:0;height:0; border-width:0 10px 10px; border-style: solid ; border-color:transparent transparent #48cfad !important;
    position:relative;top: -10px; left: 35px;}
.ty-dropdown .ty-dropdownCon .ty-trigl-1 span , .ty-tip .ty-tipcon .ty-trigl-1 span
{ padding:0 ; margin:0 ; display:block; width:0; height:0; border-width: 0 8px 8px; border-style:solid; border-color:transparent transparent #f0f8ff !important;
    position:absolute; top: 3px; left: -7px; }

.ty-tip{ position:fixed; z-index:99999; display:none;  }
.ty-tip .ty-tipcon{ position:absolute; top: 45px; background:#f0f8ff; box-shadow: 3px 3px 3px #ccc; border:1px solid #48cfad; width:350px;   }

.ty-form-checkbox,.ty-form-checkbox *,.ty-form-radio,.ty-form-radio{  display: inline-block;  vertical-align: baseline;  }
.ty-form-checkbox{  height: auto !important;  line-height: normal !important;  border: none !important;  margin-right: 0;  padding-right: 0;  background: 0 0;  }
.ty-form-checkbox i {  position: absolute;  right: 0;  width: 30px;  color: #fff;  font-size: 20px;  text-align: center; }
.ty-form-checkbox[skin="green"] i {  position: relative;  top: 0;  width: 16px;  height: 16px;  line-height: 16px;  border: 1px solid #d2d2d2;  font-size: 12px;  border-radius: 2px; cursor: pointer; vertical-align: middle; background-color: #fff;  -webkit-transition: .1s linear;  transition: .1s linear;  }
.ty-form-checked[skin="green"] i {  border-color: #48cfad;  background-color: #48cfad;  color: #fff;  }
.ty-form-checked[skin="green"].ty-form-disabled[skin="green"] i {  border-color: #d2d2d2;  background-color: #d2d2d2;  color: #fff; cursor:not-allowed }
.ty-form-disabled[skin="green"] i {  border-color: #d2d2d2;  background-color: #d2d2d2;  color: #d2d2d2; cursor:not-allowed }
.ty-form-checkbox span {  padding: 0 10px;  padding-right: 10px;  height: 100%;  font-size: 14px;  background-color: #d2d2d2;  color: #fff;  overflow: hidden;  white-space: nowrap;  text-overflow: ellipsis;  }
.ty-form-checkbox[skin="green"] span {  float: right;  padding-right: 15px;  line-height: 18px;  background: 0 0;  color: #666;  }
.ty-checkbox-disabled[skin="green"] span{  color: #aaa;  }
.ty-checkbox-disabled:hover {  color: #d2d2d2 !important;  cursor: not-allowed !important;  }

/*   Alert    */
.ty-alert{width: 100%; padding: 8px 0; box-sizing: border-box; border-radius: 2px; position: relative; font-size: 13px; color: #909399; overflow: hidden; opacity: 1; display: flex; align-items: center; transition: opacity .2s; margin-bottom: 4px;}
.ty-alert-border{padding: 8px; border: 1px solid #d5e1ec; background: #e3f2ff;}
.ty-alert i.fa{ margin:0 6px; color: #909399; display: inline; width: auto; }
.ty-alert-success{  background-color: #f0f9eb;  color:#67c23a; padding: 8px; }
.ty-alert-success i.fa {color:#67c23a;  }
.ty-alert-info{  background-color: #f4f4f5;  color:#909399; padding: 8px; }
.ty-alert-info i.fa {color:#909399;  }
.ty-alert-warning{  background-color: #fdf6ec;  color:#e6a23c; padding: 8px; }
.ty-alert-warning i.fa {color:#e6a23c;  }
.ty-alert-error{  background-color: #fef0f0;  color:#f56c6c; padding: 8px; }
.ty-alert-error i.fa {color:#f56c6c;  }

/*    弹出框    */
.hel{  position: absolute;  width: 380px;  right: 0;  top: 40px;  padding: 20px;  background-color: #fff;  box-shadow: 0 0 3px #aaa;  z-index: 2;  }
.hel input{  height: 32px;  }
.hel .hel_foot{  margin-top: 12px;  text-align: right;  }

/*  triangle  */
.popper__arrow {  border-width: 6px;  border-bottom-width: 6px;  filter: drop-shadow(0 2px 12px rgba(0,0,0,.03));  }
.popper__arrow::after {  content: " ";  border-width: 6px;  bottom: 1px;  margin-left: -6px;  border-top-color: #fff;  }
.popper__arrow, .popper__arrow::after {  position: absolute;  display: block;  width: 0;  height: 0;  border-color: transparent;  border-top-color: transparent;  border-style: solid;  }
.popper__arrow {  top: -6px;  margin-right: 3px;  border-top-width: 0;  border-bottom-color: #bbb;  }
.popper__arrow::after {  top: 1px;  margin-left: -6px;  border-top-width: 0;  border-bottom-color: #fff;  }

/*小窗*/
.ui-cells{  margin-top:0.6rem;  }
.ui-cells__title {  margin-top: .77rem;  margin-bottom: .3rem;  padding-left: 1.5rem;  padding-right: 1.5rem;  color: #999999;  font-size: 1.2rem;  }
.ui-cells__title + .ui-cells {  margin-top: 0;  }
.page .ui-cells:nth-of-type(1){  margin-top: 0;  }
.ui-cell{ background-color: #fff; padding: 1rem 1.5rem; position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-align: center; -webkit-align-items: center; align-items: center; font-size: 1.2rem; overflow: hidden; color:inherit;  }
.ui-cell::before {  content: " ";  position: absolute;  left: 0;  top: 0;  width: 100%;  height: 0.1rem;  border-top: 0.1rem solid #d9d9d9;  color: #d9d9d9;  -webkit-transform-origin: 0 0;  transform-origin: 0 0;  -webkit-transform: scaleY(.5);  transform: scaleY(.5); /*left: 1.5rem;*/  }
.ui-cell:first-child:before {  display: none;  }

.text-justify  {
    text-align: justify;
}
.text-justify:after {
    content: " ";
    display: inline-block;
    width: 100%;
}
.clear:before, .clear:after {
    display: block;
    clear: both;
    content: "";
    visibility: hidden;
    height: 0
}
.ty-checkbox {
    position: relative;
    display: inline-block;
    margin-right: 12px;
}

.ty-checkbox input {
    vertical-align: middle;
    margin-top: -2px;
    margin-bottom: 1px;
    /* 前面三行代码是为了让radio单选按钮与文字对齐 */
    width: 15px;
    height: 15px;
    appearance: none;/*清楚默认样式*/
    -webkit-appearance: none;
    opacity: 0;
    outline: none;
    /* 注意不能设置为display:none*/
}
/*  checkbox  */
.ty-checkbox input+label {
    position: absolute;
    left: 0;
    top: 4px;
    /*z-index: -1;*/
    /*注意层级关系，如果不把label层级设为最低，会遮挡住input而不能单选*/
    width: 15px;
    height: 15px;
    border: 1px solid #acacac;
    border-radius: 2px;
    max-width: inherit;
}
.ty-checkbox input:disabled + label{
    border: 1px solid #d9d9d9;
    background: #f2f2f2;
    cursor: default;
}

.ty-checkbox input:checked+label {
    border: 1px solid #5D9CEC;
    background: #5D9CEC;
}
.ty-checkbox input:checked:disabled+label {
    border: 1px solid #d9d9d9;
    background: #d9d9d9;
}

.ty-checkbox input:checked+label::after {
    content: "";
    position: absolute;
    left: 5px;
    top: 0px;
    width: 5px;
    height: 11px;
    border-right: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: rotate(45deg);
}
/*  radio  */
.ty-radio {
    position: relative;
    display: inline-block;
    margin-right: 12px;
}

.ty-radio input {
    vertical-align: middle;
    margin-top: -2px;
    margin-bottom: 1px;
    /* 前面三行代码是为了让radio单选按钮与文字对齐 */
    width: 16px;
    height: 16px;
    appearance: none;/*清楚默认样式*/
    -webkit-appearance: none;
    opacity: 0;
    outline: none;
    /* 注意不能设置为display:none*/
}
.ty-radio input+label {
    position: absolute;
    left: 0;
    top: calc(50% - 8px);;
    /*z-index: -1;*/
    /*注意层级关系，如果不把label层级设为最低，会遮挡住input而不能单选*/
    width: 14px;
    height: 14px;
    border: 1px solid #acacac;
    border-radius: 50%;
    cursor: pointer;
    transition: .2s;
}
.ty-radio input:disabled + label{
    border: 1px solid #d9d9d9;
    background: #f2f2f2;
    cursor: default;
}

.ty-radio input:checked+label {
    border: 1px solid #3d8fec;
}
.ty-radio input:checked:disabled+label {
    border: 1px solid #dcdfe6;
    background-color: #f5f7fa;
}
.ty-radio input:checked+label::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    width: 6px;
    height: 6px;
    background: #5d9cec;
    border-radius: 50%;
    transform: translate(-50%,-50%) scale(1);
    -webkit-transition: transform .15s ease-in;
    -moz-transition: transform .15s ease-in;
    -ms-transition: transform .15s ease-in;
    -o-transition: transform .15s ease-in;
    transition: transform .15s ease-in;
}
.ty-radio input:checked:disabled+label::after {
    background: #c0c4cc;
}

.kj-table{
    width:100%;
    table-layout: auto;
    border-collapse: separate;
    border-left:1px solid #ebeef5;
    border-top:1px solid #ebeef5;
}
.kj-table thead{
    background-color: #f2f2f2;
    color: #5a5c60;
    font-weight: 500;
}

.kj-table thead td{
    font-weight: bold;
}
.kj-table.kj-thead-warn thead td{
    background-color: #ffe3b0;
    border-color: #ecdab9;
    color: #5e554d;
}
.kj-table td {
    padding: 10px;
    min-width: 0;
    box-sizing: border-box;
    text-overflow: ellipsis;
    vertical-align: middle;
    position: relative;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
    background-color: #fff;
    border-right: 1px solid #ebeef5;
}
.kj-table-center td{
    text-align: center;
}
.kj-table thead td{
    background-color: #f4f6f9;
    color: #46516a;
}
.kj-table tbody{
    font-size: 14px;
    color: #606266;
}
.kj-table-striped tbody tr:nth-of-type(2n) td{
    background-color: #f9f9f9;
}
.kj-table-none{
    border: none
}
.kj-table-none td{
    border: none;
    background: inherit;
}
.link-blue, .link-red, .link-orange, .link-gray, .link-green{
    display: inline-block;
    font-size: 13px;
    border: none;
    background: inherit;
    border-radius: 2px;
    font-weight: bold;
    cursor: pointer;
}
.link-green{
    color: #48cfad;
}
.link-blue{
    color: #5d9cec;
}
.link-red{
    color: #ed5565;
}
.link-orange{
    color: #f58410;
}
.link-gray{
    color: #aaa;
    cursor: default;
}
.link-green:hover {
    color: #40bf9f;
}
.link-blue:hover {
    color: #558ed7;
}
.link-red:hover {
    color: #d94f5d;
}
.link-orange:hover {
    color: #ec871f;
}
table .link-green, table .link-blue, table .link-red, table .link-orange, table .link-gray{
    padding: 1px 8px 2px 8px;
}
table .link-green:hover{
    background: #40bf9f;
    color: #fff;
    transition: 0.1s;
}
table .link-blue:hover{
    background: #5d9cec;
    color: #fff;
    transition: 0.1s;
}
table .link-red:hover {
    background: #d94f5d;
    color: #fff;
    transition: 0.1s;
}
table .link-orange:hover {
    background: #ec871f;
    color: #fff;
    transition: 0.1s;
}

.label{
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    color: #fff;
    vertical-align: baseline;
    border-radius: .25em;
}
.label-gray{
    background-color: #aaa;
}
.label-green{
    background-color: #40bf9f;
}
.label-red{
    background-color: #ed5565;
}
.label-blue{
    background-color: #5d9cec;
}
.label-orange{
    background-color: #f58410;
}

.kj-hr{
    margin: 8px 0;
    border-bottom: 1px solid #eee;
}
.kj-input, .kj-select{
    height: 32px;
    border:1px solid #dcdfe6;
    color: #666;
    padding:0 8px;
    min-width: 167px;
    background: inherit;
    transition: border-color .2s cubic-bezier(.645,.045,.355,1);
}
.kj-input-blue, .kj-select-blue{
    border-color: #5d9cec;
}
.kj-input:focus, .kj-select:focus{
    border:1px solid #5d9cec;
    box-shadow: 0 0 1px #1f81ff;
    background-color: #fff;
}
.kj-input:disabled, .kj-select:disabled{
    border-color: #ccc;
    background-color: #f0f0f0;
    cursor: not-allowed;
    color: #aaa;
}