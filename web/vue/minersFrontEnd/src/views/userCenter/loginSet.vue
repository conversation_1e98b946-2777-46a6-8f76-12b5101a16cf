<template>
  <div class="loginSet">
    <div>
      <div class="tip">您在此选择默认登录的企业后，再次登录系统时将直接进入该企业。</div>
      <table class="ty-table" style="width:600px; ">
        <tbody>
          <tr><td>企业简称</td><td></td></tr>
          <tr v-for="(org , index) in list" :key="index">
            <td>{{ org.name }}</td>
            <td :class="roleCode === 'browse'? 'ty-gray':''" @click="showCongirm(org)">
              <i class="fa" :class="org.defaulOrg == 2 ? 'fa-check-square-o' : 'fa-square-o' "></i>
              默认登录的企业
            </td>
          </tr>
        </tbody>

      </table>
      <TyPage v-if="pageShow"
              :curPage="pageInfo.currentPageNo" :pageSize="pageInfo.pageSize"
              :allPage="pageInfo.totalPage" :pageClickFun="pageClick"></TyPage>

      <TyDialog v-if="orgSetVisible" :dialogTitle="'确认信息'" color="orange" :dialogHide="hideU" >
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideU">取消</el-button>
          <el-button class="bounce-ok" @click="orgSetOk">确定</el-button>
        </template>
        <template #dialogBody>
          <div class="ty-center">
            <span v-if="editObj.curStatus">取消默认机构，下次登录将会提示所有公司以供选择</span>
            <span v-else>您再次登录系统时，将直接进入该公司</span>
          </div>
        </template>
      </TyDialog>
    </div>
  </div>
</template>

<script>
import auth from '@/sys/auth'
import { getAllOrgsByUserId, updateDefaulOrg } from '@/api/userInfo.js'
import { initNav, beforeRouteLeave } from "@/utils/routeChange"

export default {
  data() {
    return {
      pageName: 'loginSet',
      pageShow: false,
      pageInfo:{ 'currentPageNo': 0, 'pageSize': 20 , 'totalPage': 0  },
      list: [ ],
      editObj: '',
      orgSetVisible: '',
      roleCode: auth.getUser().roleCode
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'mi', name: '登陆设置', pageName: this.pageName}, this.getOrgs, this)
  },
  methods: {
    orgSetOk(){
      this.orgSetVisible = false
      let sta = 0 ;  // state（是否默认机构 1-否 2-是）
      if(this.editObj.curStatus){ // 当前选中，设置为不选中 ,
        sta = 1 ;
      }else{ // 当前不选中 ， 设置为选中
        sta = 2 ;
      }
      let data = { "orgId" : this.editObj.id , "state" : sta   }
      updateDefaulOrg(data).then(res => {
        let resData = res.data
        var state = resData["state"] ;  // 1-成功 0-失败
        if(state == 1){
          this.getOrgs()
        }else{
          this.$message({
            type: 'error',
            message: '设置失败'
          })
        }
      }).catch(err => {
        console.log('err=',err)
      })

    },
    hideU(){
      this.orgSetVisible = false
    },
    getOrgs(){
      let data = { pageSize: 20, currentPageNo:1 }
      this.getOrgsByData(data)
    },
    getOrgsByData(data){
      getAllOrgsByUserId(data).then(res => {
        res = res.data
        console.log('getAllOrgsByUserId res =', res)
        this.list = res.data.orgs || []
        let pageInfoRes = res.data.pageInfo
        console.log('pageInfoRes=', pageInfoRes)
        this.pageInfo = { 'currentPageNo': pageInfoRes.currentPageNo, 'pageSize': 20 , 'totalPage': pageInfoRes.totalPage  }
        if(pageInfoRes.totalPage > 1 ){
          this.pageShow = true
        }else{
          this.pageShow = false
        }

      }).catch(err => {
        console.log('err=', err)
      })
    },
    pageClick(pageInfo){
      console.log('点击的page=', pageInfo)
      let data = {
        pageSize: 20 ,
        currentPageNo: pageInfo.page
      }
      this.getOrgsByData(data)

    },
    showCongirm(org){
      let orgId = org.id
      let curStatus = org.defaulOrg == 2 ? true : false
      this.editObj = org
      this.editObj.curStatus = curStatus
      if(this.roleCode === 'browse'){

      }else{
        this.orgSetVisible = true
      }

    },

  }
}


</script>

<style lang="scss" scoped>
.loginSet{
  padding: 40px 100px;
  .tip{ padding: 10px; }


}
</style>
