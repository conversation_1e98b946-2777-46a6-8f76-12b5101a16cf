package cn.sphd.miners.modules.dailyAffairs.entity;

import javax.persistence.*;

/**
 * Created by Administrator on 2019/5/8.
 */
@Entity
@Table(name = "t_sys_suspend_url")
public class SuspendUrl {
    @Id
    @Column(name="id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="suspend_type"  , length=100 , nullable=true , unique=false)
    private String suspendType;//悬浮窗消息类型

    @Column(name="url_pc"  , length=255 , nullable=true , unique=false)
    private String urlPc;//'PC消息跳转详情的路径'

    @Column(name="url_ios"  , length=255 , nullable=true , unique=false)
    private String urlIos;//'ios消息跳转详情的路径'

    @Column(name="url_android"  , length=255 , nullable=true , unique=false)
    private String urlAndroid;//andriod消息跳转详情的路径

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSuspendType() {
        return suspendType;
    }

    public void setSuspendType(String suspendType) {
        this.suspendType = suspendType;
    }

    public String getUrlPc() {
        return urlPc;
    }

    public void setUrlPc(String urlPc) {
        this.urlPc = urlPc;
    }

    public String getUrlIos() {
        return urlIos;
    }

    public void setUrlIos(String urlIos) {
        this.urlIos = urlIos;
    }

    public String getUrlAndroid() {
        return urlAndroid;
    }

    public void setUrlAndroid(String urlAndroid) {
        this.urlAndroid = urlAndroid;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
