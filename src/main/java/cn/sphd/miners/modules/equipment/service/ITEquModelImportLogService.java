package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquModelImportLog;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
public interface ITEquModelImportLogService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquModelImportLog selectTEquModelImportLogById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquModelImportLog 
     * @return 集合
     */
    public List<TEquModelImportLog> selectTEquModelImportLogList(TEquModelImportLog tEquModelImportLog);

    /**
     * 新增
     * 
     * @param tEquModelImportLog 
     * @return 结果
     */
    public int insertTEquModelImportLog(TEquModelImportLog tEquModelImportLog);

    /**
     * 修改
     * 
     * @param tEquModelImportLog 
     * @return 结果
     */
    public int updateTEquModelImportLog(TEquModelImportLog tEquModelImportLog);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTEquModelImportLogByIds(String ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquModelImportLogById(Long id);
}
