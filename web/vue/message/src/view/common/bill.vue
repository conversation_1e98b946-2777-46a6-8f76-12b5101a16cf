<template>
  <div id="bill">
    <TY_NavTop :title="title" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div  v-if="isFinance === 0">
        <div class="billPage" v-if="reimburseDetail.billCatName !== ''">
          <div class="panel-title">
            {{paramsReimburse.createName}}票据中{{reimburseDetail.billCatName}}共 {{paramsReimburse.num}} 张，计 {{reimburseDetail.totalAmount}} 元
          </div>
          <div class="">
            <div class="bill"  v-for="item in financeReimburseBill" v-bind:key="item.id">
              <div class="bill_item" v-if="reimburseDetail.billCatName !== '定额发票'">
                所属月份为<span class="color-warning">{{item.issueDate | formatDay('YYYY年MM月')}}</span>、
                <span v-if="reimburseDetail.billCatName === '增值税专用发票'">价税合计</span>
                <span v-else>金额</span>为<span class="color-warning">{{item.itemAmount}}</span>的票据共<span class="color-warning">{{item.relativeBillQuantity}}</span>张
              </div>
              <div class="link_blue">
                <span @click="seePicture(item.pictures)">查看票据图片</span>
              </div>
              <el-table :data="item.financeReimburseBillItemList"
                fit
                border
                v-if="reimburseDetail.billCatName === '增值税专用发票' || reimburseDetail.billCatName === '增值税普通发票'">
                <el-table-column
                  prop="itemName"
                  label="货物或应税劳务、服务名称"
                  width="120"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="itemQuantity"
                  label="数量"
                  width="40"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="uniPrice"
                  label="单价"
                  width="65"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="price"
                  label="金额"
                  width="65"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="taxAmount"
                  label="税额"
                  :formatter="formatter"
                >
                </el-table-column>
              </el-table>
              <el-table
                :data="item.financeReimburseBillItemList"
                fit
                border
                v-if="reimburseDetail.billCatName === '其他普通发票'">
                <el-table-column
                  prop="itemName"
                  label="票据内容"
                  width="120"
                  :formatter="formatter"
                  >
                </el-table-column>
                <el-table-column
                  prop="itemQuantity"
                  label="数量"
                  :formatter="formatter"
                  >
                </el-table-column>
                <el-table-column
                  prop="uniPrice"
                  label="单价"
                  :formatter="formatter"
                  >
                </el-table-column>
                <el-table-column
                  prop="price"
                  label="金额"
                  :formatter="formatter"
                  >
                </el-table-column>
              </el-table>
              <el-table
                :data="item.financeReimburseBillItemList"
                fit
                border
                v-if="reimburseDetail.billCatName === '定额发票'">
                <el-table-column
                  prop="uniPrice"
                  label="单张票据金额"
                  width="120"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="itemQuantity"
                  label="数量"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="price"
                  label="金额"
                  :formatter="formatter"
                >
                </el-table-column>
              </el-table>
              <el-table
                :data="item.financeReimburseBillItemList"
                fit
                border
                v-if="reimburseDetail.billCatName === '收据'">
                <el-table-column
                  prop="itemName"
                  label="票据内容"
                  width="120"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="itemQuantity"
                  label="数量"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="uniPrice"
                  label="单价"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="price"
                  label="金额"
                  :formatter="formatter"
                >
                </el-table-column>
              </el-table>
              <div class="bill_item">
                备注: {{item.memo}}
              </div>
            </div>
          </div>
        </div>
        <div class="feePage" v-if="reimburseDetail.billCatName === ''">
          <div class="panel-title">
            {{paramsReimburse.createName}} {{paramsReimburse.feeCatName}} <span v-if="paramsReimburse.secondFeeCatName">- {{paramsReimburse.secondFeeCatName}}</span>支出共计 {{reimburseDetail.totalAmount}} 元
          </div>
          <div class="panel-content">
            <div>
              <el-table
                :data="financeReimburseBill"
                fit
                border
                >
                <el-table-column
                  prop="itemName"
                  label="货物或应税劳务、服务名称"
                  width="120"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="amount"
                  label="支出金额"
                  :formatter="formatter"
                >
                </el-table-column>
                <el-table-column
                  prop="pictures"
                  label="所属票据"
                  :formatter="formatter"
                >
                  <template slot="header" slot-scope="scope">
                    所属票据
                  </template>
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      @click="seePicture(scope.row.pictures)">查看票据图片</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <!--认证页面-->
      <div class="authApproval" v-if="isFinance === 1">
        <div class="panel-content">
          <div class="bill">
            <div class="bill_item">
              发票号码<span class="color-warning">{{financeReimburseBill.billNo}}</span> 开票月份<span class="color-warning">{{financeReimburseBill.issueDate | formatDay('YYYY年MM月')}}</span>
            </div>
            <div class="link_blue">
              <span @click="seePicture('auth')">查看票据图片</span>
            </div>
            <div class="link_blue">
              <span @click="seeReimbursement()">查看该次报销</span>
            </div>
            <el-table :data="financeReimburseBillItems" fit border :summary-method="getSummaries" show-summary>
              <el-table-column
                prop="itemName"
                label="货物或应税劳务、服务名称"
                width="120"
                :formatter="formatter"
              >
              </el-table-column>
              <el-table-column
                prop="itemQuantity"
                label="数量"
                width="40"
                :formatter="formatter"
              >
              </el-table-column>
              <el-table-column
                prop="uniPrice"
                label="单价"
                width="65"
                :formatter="formatter"
              >
              </el-table-column>
              <el-table-column
                prop="price"
                label="金额"
                width="65"
                :formatter="formatter"
              >
              </el-table-column>
              <el-table-column
                prop="taxAmount"
                label="税额"
                :formatter="formatter"
              >
              </el-table-column>
            </el-table>
            <div style="line-height:40px;">
              <el-row>
                <el-col :span="8">价税合计（大写）</el-col>
                <el-col :span="10">{{financeReimburseBill.itemAmount | numberToChinese}}</el-col>
                <el-col :span="6">(小写){{financeReimburseBill.itemAmount}}</el-col>
              </el-row>
            </div>
            <div class="ui-cell">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <el-row>
                    <el-col :span="4"><b>备注</b></el-col>
                    <el-col :span="20">{{financeReimburseBill.memo}}</el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .panel-title{
    padding: 0.8rem 1rem;
    background-color: #e0ecfb;
    color: #647689;
    font-weight: bold;
    font-size: 14px;
  }
  .panel-content{
    background: #fff;
    overflow: hidden;
    padding: 0.8rem 1rem;
  }
  .right{
    float: right;
  }
  .el-radio{
    display: block;
    padding: 8px;
  }
  .link_blue{
    display: block;
    text-align: right;
    padding:4px 8px;
  }
  .link_blue span{
    color: #4797f1;
    cursor: pointer;
  }
  .color-warning{
    color: #f58410;
  }
  .el-table{
    font-size: 12px;
  }
  .el-table td, .el-table th {
    padding: 6px 0;
  }
  .bill_item{
    padding: 4px 12px;
  }
  .authApproval .ui-cell{
    font-size: 12px;
  }
</style>

<script>
import { numberToChinese } from '../../js/common'
export default {
  name: 'bill',
  filters: {
    numberToChinese,
    formatCat: function (num) {
      switch (Number(num)){
        case 1:
          return "增值税专用发票";
          break;
        case 2:
          return "增值税普通发票";
          break;
        case 3:
          return "收据";
        case 4:
          return "其他发票";
          break;
        default:
          return ""
      }
    },
  },
  data () {
    return {
      title: '报销审批',
      listenersUid: [],
      loading: true,
      isFinance: 0,
      reimburseDetail: {},
      paramsReimburse: {},
      handleSchedule: {},
      financeReimburseBill: {},
      picture: [],
      financeReimburseBillItems: [],
      authApproval: {
        type: '',
        num: ''
      },
      rules: {
        type: [
          { required: true }
        ]
      }
    }
  },
  computed: {
    totalPrice: function () {
      let price = 0
      for (let i in this.financeReimburseBillItems) {
        price += this.financeReimburseBillItems[i].price
      }
      return price.toFixed(2)
    },
    totalTaxAmount: function () {
      let taxAmount = 0
      for (let i in this.financeReimburseBillItems) {
        taxAmount += this.financeReimburseBillItems[i].taxAmount
      }
      return taxAmount.toFixed(2)
    }
  },
  created () {
    let that = this
    // let billId = this.$route.params.id
    let params = this.$store.getters.getBillParam
    console.log('获取传值',params)
    if (params.name === 'apply') {
      this.title = '报销申请'
    } else if (params.name === 'auth') {
      this.title = '增票认证'
    }else if(params.name === 'purchaseInvoice'){
      this.title = '票据详情'
    }
    if(params.name === 'purchaseInvoice'){
      let paramCode = params.applicationId
      let code = Number(paramCode.substring(0, 2))
      let invoiceID = paramCode.substring(2, paramCode.length)

      let sendData = {
        "applicationId" : invoiceID ,
        "invoiceCategory": params.billCat || '',
        "feeCategory":params.feeCategory || '',
      }
      this.axios.post('../../../purchaseInvoice/getInvoiceItemDetail.do?', sendData)
        .then(function (response) {
          let data = response.data.data
          let financeReimburseBill = data['poPaymentInvoices']
          let listMap = data['listMap']
          that.loading = false
          // 报销申请信息
          that.paramsReimburse = {
            'num': params.num
          }
          that.reimburseDetail = {
            'billCatName': that.$options.filters.formatCat(params.billCat),
            'totalAmount':params.totalAmount
          }
          console.log('reimburseDetail',  that.reimburseDetail)
          if(financeReimburseBill){
            financeReimburseBill.forEach(function(item){
              item.pictures = item.poPaymentInvoiceAttachments
              item.poPaymentInvoiceItems.forEach(function(goodsItem){
                goodsItem.uniPrice = goodsItem.unitPrice
              })
              item.financeReimburseBillItemList = item.poPaymentInvoiceItems
              item.itemAmount = item.parAmount

            })
            that.financeReimburseBill = financeReimburseBill

          }else if(listMap){
            that.financeReimburseBill = listMap
          }

        })
        .catch(function (error) {
          console.log(error)
        })
    }else{
      this.listenersUid = [
        this.sphdSocket.subscribe('getReimburseBillDetail', function (data) {
          that.loading = false
          let getReimburseBillDetail = JSON.parse(data)
          // 报销申请信息
          console.log('报销申请信息')
          console.log(getReimburseBillDetail)
          that.reimburseDetail = getReimburseBillDetail
          if (that.reimburseDetail.billCatName === '') {
            that.financeReimburseBill = getReimburseBillDetail.listMap
          } else {
            let billDetail = getReimburseBillDetail.financeReimburseBills
            that.financeReimburseBill = billDetail
          }
        })
      ]
      if (params.id) {
        this.isFinance = 1
        let billId = params.id
        this.axios.get('../../../reimburseWindow/getAuthBillItemDetail.do?billId=' + billId)
          .then(function (response) {
            let data = response.data.data
            let financeReimburseBill = data['financeReimburseBill']
            let billPictures = data['billPictures']
            let financeReimburseBillItems = data['financeReimburseBillItems']
            console.log(financeReimburseBill)
            console.log(billPictures)
            console.log(financeReimburseBillItems)
            that.loading = false
            // 报销申请信息
            that.financeReimburseBill = financeReimburseBill
            that.picture = billPictures
            that.financeReimburseBillItems = financeReimburseBillItems
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        this.isFinance = 0
        this.paramsReimburse = params
        console.log(params)
        this.sphdSocket.send('getReimburseBillDetail', params)
      }
    }
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    openDialog: function () {
      this.dialogVisible = true
    },
    seePicture: function (pic) {
      console.log(pic)
      let picArr = pic
      if (picArr === 'auth') {
        picArr = this.picture
        console.log(picArr)
      }
      if (picArr.length > 0) {
        window.parent.seePicture(picArr[0].path)
      } else {
        this.$kiko_message('未上传附件')
      }
    },
    seeReimbursement: function () {
      let reimburseId = 'AA' + this.financeReimburseBill.reimburseId
      console.log(reimburseId)
      this.$router.push({
        path: '/paymentApprovalDetail/' + reimburseId + '/' + '0'
      })
    },
    formatter: function (row, column, cellValue, index) {
      return cellValue || '--'
    },
    getSummaries: function (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = sums[index].toFixed(2)
          if (index === 1 || index === 2) {
            sums[index] = ''
          }
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>
