package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.commodity.entity.PdFormula;
import cn.sphd.miners.modules.commodity.entity.PdFormulaHistory;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.List;
import java.util.Map;

public interface FormulaService  extends BadgeNumberCallback {

    Map<String,Object> getPdFoBaseList(Integer oid, Integer currPage, Integer pageSize);
    //获取待选择产品列表 无分页
    List<Map<String,Object>> getPdFoBaseList(Integer oid);
    //分页获取配方列表
    Map<String,Object> getFormulaList(Integer oid,String state, String keyword, Integer currPage, Integer pageSize);

    //分页获取停用配方列表
    Map<String,Object> getStopFormulaList(Integer oid,Integer currPage, Integer pageSize);
    //分页获取配方列表无分页
    List<Map<String,Object>>  getFormulaList(Integer oid,String state);

    PdFormula getPdFormula(Integer id);
    //模糊查询
    PdFormula getLikeByApproval(Integer approvalId);

    PdFormula getByInstance(Integer approvalId);

    PdFormulaHistory getPdFormulaHistoryById(Integer id);

    String addOrUpdateFormula(Integer oid, User user,String jsonData);
    //产品与配方绑定
    String bindOperation(String baseList,Integer formula,User user);
    //配方删除
    String deleteFormula(Integer id,Integer oid);

    //启停 id 配方id
    String startStopFormula(Integer id, String state,User user);


    //根据配方获取已绑定产品
    Map<String,Object> getBindingPdList(Integer id,Integer currPage, Integer pageSize);

    //根据配方获取曾经绑定产品
    Map<String,Object> getBeforeBindingPdList(Integer id,Integer currPage, Integer pageSize);

    //根据产品获取曾经绑定配方
    Map<String,Object> getFormulaListByPdBase(Integer id,Integer currPage, Integer pageSize);

    //根据材料获取已绑定配方
    Map<String,Object> getBindingFormulaList(Integer id,Integer currPage, Integer pageSize);

    //根据材料获取曾经绑定配方
    Map<String,Object> getBeforeBindingFormulaList(Integer id,Integer currPage, Integer pageSize);

    //配方审批
    String formulaApproval( ApprovalProcess approvalProcess,String sessionid);

}
