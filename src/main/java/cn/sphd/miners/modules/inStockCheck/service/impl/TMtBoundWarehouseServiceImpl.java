package cn.sphd.miners.modules.inStockCheck.service.impl;

import cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse;
import cn.sphd.miners.modules.inStockCheck.mapper.TMtBoundWarehouseMapper;
import cn.sphd.miners.modules.inStockCheck.service.TMtBoundWarehouseService;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
@Service
public class TMtBoundWarehouseServiceImpl implements TMtBoundWarehouseService {

    @Autowired
    private TMtBoundWarehouseMapper tMtBoundWarehouseMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return tMtBoundWarehouseMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByPrimaryKeyIn(List<Long> list) {
        return tMtBoundWarehouseMapper.deleteByPrimaryKeyIn(list);
    }

    @Override
    public int insert(TMtBoundWarehouse record) {
        return tMtBoundWarehouseMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(TMtBoundWarehouse record) {
        return tMtBoundWarehouseMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(TMtBoundWarehouse record) {
        return tMtBoundWarehouseMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(TMtBoundWarehouse record) {
        return tMtBoundWarehouseMapper.insertSelective(record);
    }

    @Override
    public TMtBoundWarehouse selectByPrimaryKey(Long id) {
        return tMtBoundWarehouseMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(TMtBoundWarehouse record) {
        return tMtBoundWarehouseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(TMtBoundWarehouse record) {
        return tMtBoundWarehouseMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<TMtBoundWarehouse> list) {
        return tMtBoundWarehouseMapper.updateBatch(list);
    }

    @Override
    public int updateBatchUseMultiQuery(List<TMtBoundWarehouse> list) {
        return tMtBoundWarehouseMapper.updateBatchUseMultiQuery(list);
    }

    @Override
    public int updateBatchSelective(List<TMtBoundWarehouse> list) {
        return tMtBoundWarehouseMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<TMtBoundWarehouse> list) {
        return tMtBoundWarehouseMapper.batchInsert(list);
    }

    @Override
    public int batchInsertSelectiveUseDefaultForNull(List<TMtBoundWarehouse> list) {
        return tMtBoundWarehouseMapper.batchInsertSelectiveUseDefaultForNull(list);
    }

}
