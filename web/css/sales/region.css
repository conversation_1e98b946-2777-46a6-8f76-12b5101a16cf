.regionMode{ position:relative;margin-bottom: 10px;}
.regionMode .sale_ttl1{ width: 180px;text-align: left;}
.regionCon{ width: 218px;border: 1px solid #dcdfe6;border-radius: 2px;background-color: #fff;display: inline-block;height: 30px;line-height: 30px;background-image: none;
    box-sizing: border-box;color: #606266;font-size: inherit;outline: none;padding: 0 8px;transition: border-color .2s cubic-bezier(.645,.045,.355,1);}
.regionBox{display: none;float: left;position: absolute;top: 29px;left: 194px;border: 1px solid #CECBCE;width: 390px;padding: 12px 12px 15px;background: #fff;
    -webkit-box-shadow: 0 0 5px #ddd;box-shadow: 0 0 5px #ddd;z-index: 100;font-size: 12px;}
.regionBox .region-tab {cursor: default;height: 25px;border-bottom: 1px solid #48cfad;overflow: visible;*overflow: hidden;}
.regionBox .region-tab li{position: relative;float: left;height: 23px;line-height: 23px;padding: 0 25px 1px 6px;margin-right: 4px;border: 1px solid #ddd;border-bottom: 0;
    color: #005AA0;text-align: center;cursor: pointer;overflow: hidden;}
.regionBox .region-tab li.region-current{height: 25px;background-color: #fff;border: 1px solid #48cfad;border-bottom-color: rgb(72, 207, 173);border-bottom-style: solid;
    border-bottom-width: 2px;border-bottom: 0;padding: 0 25px 0 6px;line-height: 22px;text-decoration: none;color: #48cfad;}
.region-content > ul{clear: none;padding-top: 10px;overflow: hidden;margin-bottom: -5px;}
.region-content > ul li{float: left;padding-bottom: 12px;line-height: 18px;min-width: 90px;}
.region-content > ul li a{padding: 2px 4px 2px 0;color: #005aa0;}
.region-content .region-content-current a {color: #48cfad;}
.regionText{display: inline-block;}
#requirements{width: 259px;}




























