<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResAclMapper">

    <select id="getSingle" parameterType="resAcl" resultType="resAcl">
        select
        id, resource, user, view, operate, creator, create_name AS createName, create_date AS createDate,
        updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
        message_id AS messageId
        from t_resource_acl
        where id = #{id}
    </select>

    <select id="listPage" parameterType="hashmap" resultType="resAcl">
        select
        id, resource, user, view, operate, creator, create_name AS createName, create_date AS createDate,
        updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
        message_id AS messageId
        from t_resource_acl
    </select>

    <select id="getFileAuth" resultType="resAcl">
        select
        id, resource, user, view, operate, creator, create_name AS createName, create_date AS createDate,
        updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
        message_id AS messageId
        from t_resource_acl
        where resource = #{FileId}
    </select>
    <select id="getAllUserIdsByFileId" resultType="Integer">
        select user
        from t_resource_acl
        where resource = #{FileId}
    </select>
    <select id="count" resultType="Integer">
        select count(*)
        from t_resource_acl
        where resource = #{resource} and user = #{user}
    </select>

    <select id="listUserByResource" resultType="cn.sphd.miners.modules.system.dto.UserDto">
        select userID, userName,postName, departName, department, mobile
        from t_sys_user
        where oid = #{oid} and isDuty in('1','9') and userID not in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        and role_code in
        <foreach item="item" index="index" collection="listAuth" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listAllUserByResource" resultType="cn.sphd.miners.modules.system.dto.UserDto">
        select userID, userName, postName, departName, department, mobile
        from t_sys_user
        where oid = #{oid} and isDuty in('1','9') and role_code in
        <foreach item="item" index="index" collection="listAuth" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="delete" parameterType="resAcl" >
        delete from t_resource_acl
        where id = #{id}
    </delete>

    <delete id="delAuthByCategoryAndUser">
        delete from t_resource_acl
        where user = #{user} and resource IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="delAllAuthByFile">
        delete from t_resource_acl
        where resource = #{fileId}
    </delete>

    <delete id="delAuthByFileAndUser">
        delete from t_resource_acl
        where resource = #{resource} and user = #{user}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="resAcl" >
        insert into t_resource_acl (resource, user,
        view, operate, creator,
        create_name, create_date, updator,
        update_name, update_date, approve_Item,
        approve_status, approve_level, auditor,
        auditor_name, audit_date, operation,
        apply_memo, approve_memo, message_id
        ) values (#{resource}, #{user}, #{view}, #{operate}, #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate}, #{approveItem}, #{approveStatus}, #{approveLevel},
        #{auditor}, #{auditorName}, #{auditDate}, #{operation}, #{applyMemo}, #{approveMemo}, #{messageId})
    </insert>

    <insert id="insertAuthByCategoryAuth">
        INSERT INTO t_resource_acl (resource, user) SELECT #{id},b.user from t_resource_category a,
        t_resource_category_acl b where a.id = b.category AND a.id = #{category};
    </insert>

    <update id="update" parameterType="resAcl" >
        update t_resource_acl
        <set >
            <if test="resource != null" >
                resource = #{resource},
            </if>
            <if test="user != null" >
                user = #{user},
            </if>
            <if test="view != null" >
                view = #{view},
            </if>
            <if test="operate != null" >
                operate = #{operate},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>