package cn.sphd.miners.modules.recruit.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

/**
 * 招聘启事历史表
 * <AUTHOR>
 * @date 20230506
 **/
@Entity
@Table(name = "t_rd_notice_history")
public class RdNoticeHistory {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    @Column
    private Integer org;//机构ID

    @Column(name="notice" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticeId;//启事ID

    @Column(length = 50)
    private String name; //名称

    @Column(name = "source_notice")
    private Long sourceNotice;//源启事ID(复制时用)

    @Column
    private Boolean enabled=true;//启用状态:0-不启用,1-启用

    @Column(name = "enabled_time")
    private Date enabledTime;//'启停用时间'

    @Column(name = "teminate_reason")
    private Integer teminateReason;//中止原因:1-有问题,作废;2-正常结束,3-招满

    @Column(name = "begin_time")
    private Date beginTime;//生效时间

    @Column(name = "end_time")
    private Date endTime;//失效时间

    @Column
    private Integer intervals;//间隔时长(分钟)

    @Column(name = "post_count")
    private Integer postCount;//岗位个数

    @Column(name = "plan_count")
    private Integer planCount;//计划人数

    @Column(name = "recieved_count")
    private Integer recievedCount;//已收简历份数

    @Column(name = "noticed_count")
    private Integer noticedCount;//已通知面试人数

    @Column(name = "interviewing_count")
    private Integer interviewingCount;//面试中人数

    @Column(name = "interviewed_count")
    private Integer interviewedCount;//已面试人数

    @Column(name = "entered_count")
    private Integer enteredCount;//已入职人数

    @Column(name = "image_path",length = 255)
    private String imagePath;//图片路径

    @Column(length = 100)
    private String keywords;//关键字

    @Column(length = 255)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"   , nullable=true , unique=false)
    private Integer operation; //操作:1-增,2-删,3-改,4-启用,5-停用,6-改起始日期,7-改截止日期

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNotice rdNotice ;//启事主表



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getSourceNotice() {
        return sourceNotice;
    }

    public void setSourceNotice(Long sourceNotice) {
        this.sourceNotice = sourceNotice;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getTeminateReason() {
        return teminateReason;
    }

    public void setTeminateReason(Integer teminateReason) {
        this.teminateReason = teminateReason;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getIntervals() {
        return intervals;
    }

    public void setIntervals(Integer intervals) {
        this.intervals = intervals;
    }

    public Integer getPostCount() {
        return postCount;
    }

    public void setPostCount(Integer postCount) {
        this.postCount = postCount;
    }

    public Integer getPlanCount() {
        return planCount;
    }

    public void setPlanCount(Integer planCount) {
        this.planCount = planCount;
    }

    public Integer getRecievedCount() {
        return recievedCount;
    }

    public void setRecievedCount(Integer recievedCount) {
        this.recievedCount = recievedCount;
    }

    public Integer getNoticedCount() {
        return noticedCount;
    }

    public void setNoticedCount(Integer noticedCount) {
        this.noticedCount = noticedCount;
    }

    public Integer getInterviewingCount() {
        return interviewingCount;
    }

    public void setInterviewingCount(Integer interviewingCount) {
        this.interviewingCount = interviewingCount;
    }

    public Integer getInterviewedCount() {
        return interviewedCount;
    }

    public void setInterviewedCount(Integer interviewedCount) {
        this.interviewedCount = interviewedCount;
    }

    public Integer getEnteredCount() {
        return enteredCount;
    }

    public void setEnteredCount(Integer enteredCount) {
        this.enteredCount = enteredCount;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public RdNotice getRdNotice() {
        return rdNotice;
    }

    public void setRdNotice(RdNotice rdNotice) {
        this.rdNotice = rdNotice;
    }
}
