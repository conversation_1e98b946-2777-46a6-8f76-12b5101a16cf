package cn.sphd.miners.modules.accountantReportTax.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxEntity;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;

/**
 * Created by 刘洪涛 on 2021/09/13
 */
@Controller
@RequestMapping("/accountant")
public class AccountantReportTaxController {

    @Autowired
    AcctReportTaxService acctReportTaxService;

    /**
     * 新增报表
     * periodArray形式：
     * {"data":[{"beginDate":"9月1日","endDate":"9月10日"},{"beginDate":"9月1日","endDate":"9月10日"}]}
     */
    @ResponseBody
    @RequestMapping("/addReport.do")
    public JsonResult addReport(User user, AcctReportEntity reportEntity) {
        JsonResult result = acctReportTaxService.addReport(user,reportEntity);
        return result;
    }

    /**
     * 新增税种，选择税种
     */
    @ResponseBody
    @RequestMapping("/addTax.do")
    public JsonResult addTax(User user, AcctTaxEntity taxEntity) {
        JsonResult result = acctReportTaxService.addTax(user,taxEntity);
        return result;
    }

    /**
     * 新增税种，只录名称
     */
    @ResponseBody
    @RequestMapping("/insertTax.do")
    public JsonResult insertTax(User user, AcctTaxEntity taxEntity) {
        JsonResult result = acctReportTaxService.insertTax(user,taxEntity);
        return result;
    }

    /**
     * 返回税种列表
     * PC端不返回社保和公积金，手机端全返回
     * type     pc端不用传，手机端传1
     */
    @ResponseBody
    @RequestMapping("/taxList.do")
    public JsonResult taxList(User user,Integer type) {
        JsonResult result = acctReportTaxService.taxList(user,type);
        return result;
    }

    /**
     * 删除税种
     * 系统自带的不能删，已绑定报表的不能删
     */
    @ResponseBody
    @RequestMapping("/delTax.do")
    public JsonResult delTax(User user,Integer id) {
        JsonResult result = acctReportTaxService.delTax(user,id);
        return result;
    }

    /**
     * 返回PC端需要的申报记录
     * startDate    开始日期
     * endDate      截止日期
     * taxId        税种ID
     */
    @ResponseBody
    @RequestMapping("/getApplyTaxList.do")
    public JsonResult getApplyTaxList(User user, Integer taxId,
                                      @DateTimeFormat(pattern="yyyy-MM-dd") Date startDate,
                                      @DateTimeFormat(pattern="yyyy-MM-dd") Date endDate) {

        JsonResult result = acctReportTaxService.getApplyTaxList(user,taxId,startDate,endDate);
        return result;
    }

    /**
     * 获取某机构全部报表
     */
    @ResponseBody
    @RequestMapping("/getReportList.do")
    public JsonResult getReportList(User user) {
        JsonResult result = acctReportTaxService.getReportList(user);
        return result;
    }

    /**
     * 获取某税种详情
     */
    @ResponseBody
    @RequestMapping("/getTaxDetial.do")
    public JsonResult getTaxDetial(User user,int taxId,int reportId) {
        JsonResult result = acctReportTaxService.getTaxDetial(user,taxId,reportId);
        return result;
    }

    /**
     * 获取某报表详情
     */
    @ResponseBody
    @RequestMapping("/getReportDetial.do")
    public JsonResult getReportDetial(User user,int reportId) {
        JsonResult result = acctReportTaxService.getReportDetial(user,reportId);
        return result;
    }

    /**
     * 获取某杂项详情
     */
    @ResponseBody
    @RequestMapping("/getTriflesDetial.do")
    public JsonResult getTriflesDetial(User user,int triflesId) {
        JsonResult result = acctReportTaxService.getTriflesDetial(user,triflesId);
        return result;
    }

    /**
     * 获取报税事务报表列表
     * year
     * month
     */
    @ResponseBody
    @RequestMapping("/getReportByDate.do")
    public JsonResult getReportByDate(User user,int year,int month) {
        JsonResult result = acctReportTaxService.getReportByDate(user,year,month);
        return result;
    }

    /**
     * 报表状态改成不再申报
     */
    @ResponseBody
    @RequestMapping("/setReportDisenabled.do")
    public JsonResult setReportDisenabled(User user, Integer reportId) {
        JsonResult result = acctReportTaxService.setReportDisenabled(user,reportId);
        return result;
    }

    /**
     * 税种状态改成不再申报
     */
    @ResponseBody
    @RequestMapping("/setTaxDisenabled.do")
    public JsonResult setTaxDisenabled(User user, Integer taxId) {
        JsonResult result = acctReportTaxService.setTaxDisenabled(user,taxId);
        return result;
    }

    /**
     * 报税
     * @param taxArray {"data":[{"taxHisId":1,"taxId":1,"fact_amount":1000}]}
     * @param taxDate 申报日期,YYYY-MM-DD
     * @param detailId 报税ID，列表里返回的字段
     * @param reportHisId 历史报表ID
     */
    @ResponseBody
    @RequestMapping("/setReportTax.do")
    public JsonResult setReportTax(User user, Integer detailId,int reportId,int reportHisId,String taxArray,String taxDate) {
        JsonResult result = acctReportTaxService.setReportTax(detailId,reportId,reportHisId,taxArray,taxDate,user);
        return result;
    }

    /**
     * 报税的编辑记录
     * reportId     报表ID
     * period       yyyy-MM
     */
    @ResponseBody
    @RequestMapping("/getEditRecord.do")
    public JsonResult getEditRecord(User user, int reportHistory,int reportId,String period) {
        JsonResult result = acctReportTaxService.getEditRecord(user,reportHistory,reportId,period);
        return result;
    }

    /**
     * 报税记录
     * @param   reportId     报表ID
     * @param   year
     * @param   type         1-报表的报税记录，2-税种的报税记录
     * @param   taxId        税种ID
     */
    @ResponseBody
    @RequestMapping("/getReportTaxRecord.do")
    public JsonResult getReportTaxRecord(User user, Integer reportId,Integer taxId,String year,int type) {
        JsonResult result = acctReportTaxService.getReportTaxRecord(user,reportId,taxId,year,type);
        return result;
    }

    /**
     * 修改报表名称
     * @param reportId     报表ID
     * @param name         新名称
     * @param type         1-改错别字，2-改新名称,3-改日期范围
     * @param periodArray:{"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}  时限数组
     * 改错别字需要改历史数据，改新名称不需要改历史
     */
    @ResponseBody
    @RequestMapping("/updateReportName.do")
    public JsonResult updateReportName(User user, int reportId,String name,String type,String periodArray) {
        JsonResult result = acctReportTaxService.updateReportName(user,reportId,name,type,periodArray);
        return result;
    }

    /**
     * 修改税种
     * @param taxId         税种ID
     * @param name          新名称
     * @param type          1-改错别字，2-改新名称,3-变更报表
     * @param curReportId   当前税种已绑定的报表ID
     * @param newReportId   非必传，新绑定的报表ID，也需要传上面那个ID
     * 改错别字需要改历史数据，改新名称不需要改历史
     */
    @ResponseBody
    @RequestMapping("/updateTax.do")
    public JsonResult updateTax(User user, int taxId,String name,String type,Integer curReportId,Integer newReportId) {
        JsonResult result = acctReportTaxService.updateTax(user,taxId,name,type,curReportId,newReportId);
        return result;
    }

    /**
     * 报表修改记录
     * @param reportId     报表ID
     */
    @ResponseBody
    @RequestMapping("/reportUpdateRecord.do")
    public JsonResult reportUpdateRecord(User user, int reportId) {
        JsonResult result = acctReportTaxService.reportUpdateRecord(user,reportId);
        return result;
    }

    /**
     * 税种修改记录
     * @param taxId     税种ID
     */
    @ResponseBody
    @RequestMapping("/taxUpdateRecord.do")
    public JsonResult taxUpdateRecord(User user, int taxId) {
        JsonResult result = acctReportTaxService.taxUpdateRecord(user,taxId);
        return result;
    }

    /**
     * 新增杂项
     * @param name          杂项名
     * @param frequency     频次
     * @param periodArray   {"data":[{"beginDate":"9月1日","endDate":"9月10日"},{"beginDate":"9月1日","endDate":"9月10日"}]}
     */
    @ResponseBody
    @RequestMapping("/addTrifles.do")
    public JsonResult addTrifles(User user, String name,String frequency,String periodArray) {
        JsonResult result = acctReportTaxService.addTrifles(user,name,frequency,periodArray);
        return result;
    }

    /**
     * 返回机构所有杂项
     */
    @ResponseBody
    @RequestMapping("/getTriflesList.do")
    public JsonResult getTriflesList(User user) {
        JsonResult result = acctReportTaxService.getTriflesList(user);
        return result;
    }

    /**
     * 杂项状态改成不再办理
     */
    @ResponseBody
    @RequestMapping("/setTriflesDisenabled.do")
    public JsonResult setTriflesDisenabled(User user, Integer triflesId) {
        JsonResult result = acctReportTaxService.setTriflesDisenabled(user,triflesId);
        return result;
    }

    /**
     * 修改杂项
     * @param triflesId     杂项ID
     * @param name          新名称
     * @param type          1-改错别字，2-改新名称,3-修改日期范围
     * @param periodArray:{"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}  时限数组
     * 改错别字需要改历史数据，改新名称不需要改历史
     */
    @ResponseBody
    @RequestMapping("/updateTrifles.do")
    public JsonResult updateTrifles(User user, int triflesId, String name, String type, String periodArray) {
        JsonResult result = acctReportTaxService.updateTrifles(user,triflesId,name,type,periodArray);
        return result;
    }

    /**
     * 杂项修改记录
     * @param triflesId     杂项ID
     */
    @ResponseBody
    @RequestMapping("/triflesUpdateRecord.do")
    public JsonResult triflesUpdateRecord(User user, int triflesId) {
        JsonResult result = acctReportTaxService.triflesUpdateRecord(user,triflesId);
        return result;
    }

    /**
     * 根据年月查询杂项
     * @param year
     * @param month
     */
    @ResponseBody
    @RequestMapping("/getTriflesByDate.do")
    public JsonResult getTriflesByDate(User user, int year,int month) {
        JsonResult result = acctReportTaxService.getTriflesByDate(user,year,month);
        return result;
    }

    /**
     * 杂项报税
     * @param handleDate 申报日期,YYYY-MM-DD
     * @param detailId 报税ID，列表里返回的字段
     * @param triflesId 杂项ID
     * @param triflesHisId 历史杂项ID
     */
    @ResponseBody
    @RequestMapping("/handleTrifles.do")
    public JsonResult handleTrifles(User user, Integer detailId,int triflesId,int triflesHisId,String handleDate) {
        JsonResult result = acctReportTaxService.handleTrifles(detailId,triflesId,triflesHisId,handleDate,user);
        return result;
    }

    /**
     * 杂项的编辑记录
     * triflesId     报表ID
     * period       yyyy-MM
     */
    @ResponseBody
    @RequestMapping("/getTriflesEditRecord.do")
    public JsonResult getTriflesEditRecord(User user, int triflesId,String period) {
        JsonResult result = acctReportTaxService.getTriflesEditRecord(user,triflesId,period);
        return result;
    }

    /**
     * 杂项的报税记录
     * @param   triflesId     报表ID
     * @param   year
     */
    @ResponseBody
    @RequestMapping("/getTriflesReportRecord.do")
    public JsonResult getTriflesReportRecord(User user, Integer triflesId,String year) {
        JsonResult result = acctReportTaxService.getTriflesReportRecord(user,triflesId,year);
        return result;
    }

    /**
     * 返回某年月内所有已缴税列表
     * period   yyyy-MM
     */
    @ResponseBody
    @RequestMapping("/getTaxedList.do")
    public JsonResult getTaxedList(User user,String period) {
        JsonResult result = acctReportTaxService.getTaxedList(user,period);
        return result;
    }

    /**
     * 返回某条已缴税款记录的详情
     * ID
     */
    @ResponseBody
    @RequestMapping("/getPayTaxList.do")
    public JsonResult getPayTaxList(User user,Integer detailId) {
        JsonResult result = acctReportTaxService.getPayTaxList(user,detailId);
        return result;
    }

    /**
     * 删除测试数据
     */
    @ResponseBody
    @RequestMapping("/delTestData.do")
    public JsonResult delTestData(User user) {
//        JsonResult result = acctReportTaxService.delTestData(user);
//        return result;
        return null;
    }


    /**
     * 测试
     */
    @ResponseBody
    @RequestMapping("/testAddTax.do")
    public JsonResult testAddTax(User user) {
//        acctReportTaxService.initialSystemTax(user.getOid(),user.getUserID(),user.getUserName());
        JsonResult json = new JsonResult();
        json.setSuccess(1);
        return json;
    }


}
