.double-sample{margin-right: 30px;width: 400px;}
.cell-sample {padding-left: 20px;}
.row-sample {padding: 6px 0;}
.row-sample span:last-child{padding-right: 20px;}
.elemFlex {  display: flex;  justify-content: space-between;  padding: 26px 0 8px 0;  }
/*左面*/
.Btop {  padding: 10px 0 10px 15px;  background: #bdd7ee;  }
.Btop > span {  font-weight: bold;  }
.faceul {  min-height: 400px;  list-style-type: none;  }
.faceul1:hover {  background: #eaeaea;  }
.faceul1 {  height: 35px;  line-height: 35px;  }
.faceul1 > a {  color: #0069b4;  text-decoration: none;  display: block;  padding: 0 0 0 26px;  }
.bottomTree .faceul1 > a {  font-size: 12px;  }
.back-btn, .back-btn:hover {  font-weight: 700;  font-size: 14px;  color: black;  }
.back-b {  padding: 50px 0 20px 30px;  }
.add-b > a {  color: #bf8f00;  }
.add-b > a:hover {  text-decoration: none;  }
.add-b {  float: left;  padding: 10px 15px;  }
div.add-b:hover {  background-color: #e3e3e3 !important;  }
.faceul1 > a > span {  display: inline-block;  vertical-align: middle;  }
.faceul1 > a > span:nth-child(1) {  max-width: 80%;  overflow: hidden;  height: 35px;  text-overflow: ellipsis;  white-space: nowrap;  }
.faceul1 > a > span:nth-child(2) {  width: 10%;  overflow: hidden;  text-align: center;  }
.faceul1 > a > span:nth-child(3) {  width: 25%;  min-width: 50px;  text-align: right;  }
.mar {  display: inline-block;  margin-right: 10px;  }
/*右面*/
.bottom{position: absolute;  bottom: 0;margin-bottom: 15px;  width: 100%;}
.left_container {  width: 245px;  min-height: 600px;  float: left;  position: relative;  padding-bottom: 80px; border: 1px solid #ddd;  border-radius: 2px; }
.right_container { margin-left: 255px;  padding: 15px;  min-width: 700px;min-height: 600px; border: 1px solid #ddd;  border-radius: 2px; }
.Right-label, .Left-label {  padding: 0;  }
.Left-label {  min-height: 86px;  }
.left-bottom {  margin-top: 45px;  }
.Right-label {  padding: 10px 15px 10px 15px;  min-width: 700px;  }
.scan-wrap{margin: auto;}
.scan-wrap hr{    border-top: 1px solid #d1d1d1;  }
#viewSettings i.ccc{ color:#ccc; }
#viewSettings .settingsCon>p { padding:5px 20px 5px 0;  }
#viewSettings .settingsCon { margin: auto;width: 82%; }
#viewSettings .leMar { padding-top: 12px; }
#viewSettings .leMar span { margin-right: 42px; display: inline-block;  min-width: 78px;}
#viewSettings .fa{ margin-right: 14px ;    }
#viewSettings .fa-dot-circle-o{ color:#48cfad ;    }
.editSourceSect{margin-top: 40px;}
.origin{margin-top: 10px;}
.origin select{width: 254px;}
.origin select[disabled]{ background: #d9d9d9 !important;  }
.gapRt{display: inline-block;width: 120px;}
.xing:before {  content: "*";  width: 5px;  height: 5px;  display: inline-block;
    color: red;  font-weight: bolder;  position: relative;  }
#updateParts input, #updateParts textarea {  width: 100%;  border: none;  }
.queryItem {  display: inline-block;  margin-left: 4%;  }
.queryItem select{  background: #fff;border: 1px solid #8f8f9d;  border-radius: 4px;  }
.lineQuery{  margin-bottom: 20px; line-height: 23px;  }
.bg-yellow tr:nth-child(even) {  background: #ddebf7;  }
.bg-yellow thead .tdSpecial{border-bottom: 1px solid #d7d7d7;}
.bg-yellow thead td {  border-color: #fff0;  background-color: #fff2cc;  }
.mainCon {display: none;}
.scropWrap{overflow-x:scroll;}
.innerElem{width: 1000px;}
.linkBtn{color:#5d9cec;}
.gspT{padding-top: 40px;}
#splitGS, #splitGS2, #partEntry{ width:970px;  }
#splitGS .bonceCon {max-height: 500px; overflow-y: auto;   }
#selecGS{ display: none; position: absolute; background:#fff;  width:192px; max-height:200px; overflow-y:auto; border-radius:4px; border:1px solid #ccc; border-top:none;   }
#selecGS option{ border-bottom:1px solid #eee; background:#f9f8f8;    }
#selecGS option:hover, #selecGS option.active{ background:#ddd; cursor: default;    }
#partEntry td[disabled], #partEntry td[disabled] input, #partEntry td[disabled] select,#partEntry td[disabled] textarea{ background:#eee;  }
select[disabled],input[disabled]{ background:#d9d9d9!important;  }
#partEntry input,#partEntry textarea{ width: 100%; border:none;  }
.editSourceSect option[disabled]{background:#eee; cursor: not-allowed;}
#searchKeyBase{width: 190px;}
.editSourceSect option.grayOpt{background: #eee;}
.selectDis{pointer-events: none;background: #d9d9d9 !important;}
.ty-table tbody td.algnL{text-align: left;}
.keywordSearch input,.bonceCon .keywordSearch input {  padding: 0 10px;  font-size: 12px;  min-width: 200px;  height: 24px;  line-height: 24px;
    border-radius: 4px 0 0 4px;  border: 1px solid #bbb;  }
.limitSize{width: 110px;}