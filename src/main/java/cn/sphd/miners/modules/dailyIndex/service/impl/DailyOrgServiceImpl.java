package cn.sphd.miners.modules.dailyIndex.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.HttpClientUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyIndex.dao.DailyOrgDao;
import cn.sphd.miners.modules.dailyIndex.entity.DailyOrg;
import cn.sphd.miners.modules.dailyIndex.service.DailyOrgService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.service.OfferService;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserLogService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by HIAPAD on 2022/6/9.
 */
@Service("dailyOrgService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DailyOrgServiceImpl implements DailyOrgService{

    @Autowired
    DailyOrgDao dailyOrgDao;
    @Autowired
    UserService userService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    OfferService offerService;
    @Autowired
    OrgService orgService;

    @Override
    public void saveDailyOrg(DailyOrg dailyOrg) {
        dailyOrgDao.save(dailyOrg);
    }

    @Override
    public void deleteDailyOrg(DailyOrg dailyOrg) {
        dailyOrgDao.delete(dailyOrg);
    }

    @Override
    public void updateDailyOrg(DailyOrg dailyOrg) {
        dailyOrgDao.update(dailyOrg);
    }

    @Override
    public DailyOrg getDailyOrgById(Integer id) {
        return dailyOrgDao.get(id);
    }

    @Override
    public DailyOrg dailyOrgIndexDay(Integer oid) {
        Date beginDate = NewDateUtils.changeDay(new Date(), -1); // 昨日零时
        Date endDate = NewDateUtils.changeDay(new Date(), 0); //今日零时

        Integer yearMonthDay=NewDateUtils.getYearMonthDay(beginDate); //昨日年月日， 每天统计前一天的

        String hql="from DailyOrg where yearMonthDay=:yearMonthDay and org=:oid";
        Map<String,Object> map=new HashMap();
        map.put("yearMonthDay",yearMonthDay);
        map.put("oid",oid);
        DailyOrg dailyOrg= (DailyOrg) dailyOrgDao.getByHQLWithNamedParams(hql,map);
        if (dailyOrg==null) {

            Integer userNum = userService.getUserCountsByOid(oid); //机构在职数量
            Integer viewerNum = userService.getBrowseCountsByOid(oid);// 浏览者数量

            Map<String,Long> resultMap=userLogService.getUserLoginCountsByOid(oid,beginDate,endDate,null);
            Integer allLoginCount = resultMap.get("userLoginCount").intValue();//机构总登录次数
            Integer allCount = resultMap.get("userCount").intValue();//机构总登录人数
            Integer allDuration = resultMap.get("userDuration")==null?0:resultMap.get("userDuration").intValue();//机构总登录时长

            List<Integer> delUserIds=userService.getUserIdsByRoleCode(oid,"browse");
            Map<String,Long> browseLogMap=userLogService.getUserLoginCountsByOid(oid,beginDate,endDate,delUserIds);
            Integer viewerLoginCount = browseLogMap.get("userLoginCount").intValue();//机构浏览登录次数
            Integer viewerCount = browseLogMap.get("userCount").intValue();//机构浏览登录人数
            Integer viewerDuration = browseLogMap.get("userDuration")==null?0:resultMap.get("userDuration").intValue();//机构浏览登录时长

            Integer userLoginCount = allLoginCount-viewerLoginCount;//机构用户登录次数(含员工 高管、董事长)
            Integer userCount = allCount-viewerCount;//机构用户登录人数(含员工 高管、董事长)
            Integer userDuration = allDuration-viewerDuration;//机构用户登录时长(含员工 高管、董事长)

            Integer respondentsCount = offerService.getOfferCountsByOid(oid, beginDate, endDate);
            dailyOrg = new DailyOrg(oid, yearMonthDay, userNum, viewerNum, userLoginCount, userCount, userDuration, viewerLoginCount, viewerCount, viewerDuration, respondentsCount);
            dailyOrgDao.save(dailyOrg);
        }
        return dailyOrg;
    }

    @Override
    public void dailyAllOrgIndexDay() {
        Integer oid=Integer.MIN_VALUE;
        Integer readLimit = 500;
        List<Organization> orgs = orgService.getOrgLimit(oid,readLimit);
        while (orgs.size()>0){
            for (Organization o : orgs) {
                oid = o.getId();
                dailyOrgIndexDay(oid);
            }
            orgs = orgService.getOrgLimit(oid,readLimit);
        }
    }

    @Override
    public  List<Map<String,Object>> getDailyOrgCounts(List<Integer> oids,Integer yearMonthDay,Integer type) {
        String hql="select sum(userLoginCount),sum(userDuration),sum(userCount),sum(userNum),sum(viewerNum),sum(respondentsCount),org from DailyOrg where org in(:oids) ";
        Map<String,Object> map=new HashMap<>();
        map.put("oids",oids);
        map.put("yearMonthDay",yearMonthDay);

        if (type==1){  // 查一天
            hql+=" and yearMonthDay=:yearMonthDay";
        }else {  // 这一天前的汇总
            hql+=" and yearMonthDay<=:yearMonthDay";
        }
        hql+=" group by org";

        List<Map<String,Object>> mapList=new ArrayList<>();
        List<Object[]> num = dailyOrgDao.getListByHQLWithNamedParams(hql, map);
        for (Object[] ob : num) {
            Integer oid= (int) ob[6];
            Map<String, Object> result = new HashMap<>();
            result.put("userLoginCounts", ob[0]);
            result.put("userDurations", ob[1]);
            result.put("userCounts",ob[2]);
            result.put("userNums", ob[3]);
            result.put("viewerNums", ob[4]);
            result.put("respondentsCounts",ob[5]);
            result.put("org",oid);
            Integer todayUserNum = userService.getUserCountsByOid(oid); //机构在职数量
            Integer toViewerNum = userService.getBrowseCountsByOid(oid);// 浏览者数量
            result.put("todayUserNum",todayUserNum);
            result.put("toViewerNum",toViewerNum);
            mapList.add(result);
        }

        return mapList;
    }

    @Override
    public List<DailyOrg> getDailyOrgList(Integer... oids) {
        String hql="from DailyOrg where org in(:oids) and yearMonthDay=:yearMonthDay";
        Map<String,Object> map=new HashMap<>();
        map.put("yearMonthDay",NewDateUtils.getYearMonthDay(NewDateUtils.changeDay(new Date(), -1))); //昨日年月日， 每天统计前一天的
        map.put("oids",oids);
        List<DailyOrg> dailyOrgList=dailyOrgDao.getListByHQLWithNamedParams(hql,map);
        return dailyOrgList;
    }

    @Override
    public  List<DailyOrg> getDailyOrgInfo(Integer oid, Integer yearMonth) {
        String hql="from DailyOrg where org=:oid and yearMonthDay between :beginDate and :endDate";
        Map<String,Object> map=new HashMap<>();
        map.put("beginDate",Integer.valueOf(yearMonth+"01")); // 年月 开始日
        map.put("endDate",Integer.valueOf(yearMonth+"31")); //年月 结束日
        map.put("oid",oid);
        List<DailyOrg> dailyOrgList=dailyOrgDao.getListByHQLWithNamedParams(hql,map);
        return dailyOrgList;
    }

    @Override
    public DailyOrg getDailyOrg(Integer oid, Integer yearMonthDay) {
        String hql="from DailyOrg where org=:oid and yearMonthDay=:yearMonthDay";
        Map<String,Object> map=new HashMap<>();
        map.put("yearMonthDay",yearMonthDay); // 年月日
        map.put("oid",oid);
        DailyOrg dailyOrg= (DailyOrg) dailyOrgDao.getByHQLWithNamedParams(hql,map);
        return dailyOrg;
    }
}
