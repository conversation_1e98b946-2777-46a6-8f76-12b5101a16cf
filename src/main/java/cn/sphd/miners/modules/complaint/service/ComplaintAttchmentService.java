package cn.sphd.miners.modules.complaint.service;

import cn.sphd.miners.modules.complaint.entity.ComplaintSupplement;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;

/**
 * Created by 朱思旭 on 2019/7/18.
 */
public interface ComplaintAttchmentService {

    void addComplaintAttachment(String listAttachment, Integer baseId, Integer detailId, Integer supplementId, User user, Date time, ComplaintSupplement complaintSupplement, String module);

    List<UserDto> getCoreSettingUserListByComplaint(Integer oid);
}
