<template>
  <div id="discussionBorrowApproval" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="讨论组阅览申请" isBackHome="false" isSearch="true" @toggleList="screen"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待审批" name="1" closable='false' :msgCount="approvalHandleCount">
        <div class="tip">下列讨论组查阅申请有待您审批。</div>
        <div class="ui-cells discussBorrow">
          <a class="ui-cell" v-on:click="jump(item.id, 'borrowApproval1')" v-for="(item, index) in toApprovalList" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div><span class="participantsNum">{{item.participantsNum}}人</span>{{item.title}}</div>
                <div class="create">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div class="ui-cells discussBorrow">
          <a class="ui-cell" v-on:click="jump(item.id, 'borrowApproval2')" v-for="(item, index) in approvedList" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div><span class="participantsNum">{{item.participantsNum}}人</span>{{item.title}}</div>
                <div class="create">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>
export default {
  name: 'discussionBorrowApproval',
  data () {
    return {
      loading: true,
      isBackHome: true,
      listenersUid: [],
      toApprovalList: [],
      approvedList: []
    }
  },
  created: function () {
    let that = this
    this.$http.post('../../../read/getApproveReadForum.do', { userID: this.sphdSocket.user.userID }, {
      emulateJSON: true
    }).then((response) => {
      let list = response.body.data
      that.toApprovalList = list.listReadForumByApply
      that.approvedList = list.listReadForumByApprove
      that.loading = false
    }).catch((err) => {
      console.log('获取数据错误了！', err)
    })
    that.setSubscribe()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  computed: {
    approvalHandleCount: function () {
      return this.toApprovalList.length
    }
  },
  methods: {
    setSubscribe: function () {
      let _this = this
      _this.loading = false
      this.listenersUid = [
        this.sphdSocket.subscribe('approveReadForumByApply', function (data) { // 讨论查阅申请待处理
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            newDate = reqData.rf
            _this.toApprovalList.push(newDate)
          } else if (update < 0) { // 减少一条
            let deleteId = reqData.rf.id
            _this.toApprovalList.forEach(function (item, index) {
              if (deleteId === item.id) {
                _this.toApprovalList.splice(index, 1)
              }
            })
          }
        }, null, 'user'),
        this.sphdSocket.subscribe('approveReadForum', function (data) { // 讨论查阅申请已批准
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            newDate = reqData.rf
            _this.approvedList.push(newDate)
          } else if (update < 0) { // 减少一条,文件被驳回时用
            let deleteId = reqData.rf.id
            _this.approvedList.forEach(function (item, index) {
              if (deleteId === item.id) {
                _this.approvedList.splice(index, 1)
              }
            })
          }
        }, null, 'user')
      ]
    },
    jump: function (id, mark) {
      this.$router.push({
        path: `/discussionBorrowDetail/${id}/${mark}`
      })
    },
    screen: function () {
      this.$router.push({
        path: '/discussionBorrowQuery/midApprove'
      })
    }
  }
}
</script>
