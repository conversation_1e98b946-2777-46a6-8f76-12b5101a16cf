package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantSettle;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface SettleMapper extends BaseMapper<TAccountantSettle> {

    /*得到结帐日期*/
    TAccountantSettle getSettleDay(TAccountantSettle settle);

    //查看此月份是否结账
    List<TAccountantSettle> getSettleByMonth(TAccountantSettle settle);


    //清除某期结账数据
    int delSettle(TAccountantSettle settle);

    //修改结账状态
    int updateSettleState(TAccountantSettle settle);

    //修改结账状态
    int updateNeedTrial(TAccountantSettle settle);

    TAccountantSettle judgeCharge(int oid);

    List<TAccountantSettle> all();

    TAccountantSettle getByOrg(int org);

    int updateTaxState(QueryData qd);

    TAccountantSettle isDisplayTaxBtn(int oid);

    Integer checkRebuild(QueryData qd);

    void updateRebuild(int oid);

    //此方法用来查找某个接收人在今天所接受到的消息id集合
    List<Integer> getMessageIDs(QueryData qd);

    Integer enableAccountant(int org);

    List<Integer> getAccountOrg();

    void deleteByDate(TAccountantSettle settle);

    List<TAccountantSettle> getSettleListByYear(TAccountantSettle param);
}