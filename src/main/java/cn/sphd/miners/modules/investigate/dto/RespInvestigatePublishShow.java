package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.modules.investigate.entity.InvestigateAnswer;
import cn.sphd.miners.modules.investigate.entity.InvestigatePublishShow;

import java.util.List;

public class RespInvestigatePublishShow  extends InvestigatePublishShow {
    private List<RespInvestigateAnswer> investigateAnswerList;

    public List<RespInvestigateAnswer> getInvestigateAnswerList() {
        return investigateAnswerList;
    }

    public void setInvestigateAnswerList(List<RespInvestigateAnswer> investigateAnswerList) {
        this.investigateAnswerList = investigateAnswerList;
    }
}
