package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "t_mt_base_history")
public class MtBaseHistory {
    private Integer id;
    private Integer material;
    private Integer org;
    private String name;
    private Integer product;
    private String code;
    private String enabled;
    private Date enabledTime;
    private String isInitialized;
    private String isPurchased;
    private String isAppointed;
    private String isCurrent;
    private String terminateOrders;
    private String model;
    private String specifications;
    private Integer category;
    private String source;
    private BigDecimal unitPrice;
    private String unit;
    private BigDecimal netWeight;
    private Long minimumPurchase;
    private BigDecimal lastUnitPrice;
    private Integer lastProvider;
    private String lastProviderName;
    private Byte isWaring;
    private Byte isIgnore;
    private Integer locationNumber;
    private Integer supplierNumber;
    private Integer cancelledNumber;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;

    private String origin;

    private Integer instance;
    private String instanceChain;
    private String approveStatus;
    private String rejectReasionDesc;

    //1.266新增字段

    private Integer expRequired;//是否有保质期方面的要求


    private Integer openDuration;//开封/开瓶后可使用天数


    private Integer relatedItem;//相关的数据:1-截止日期,2-生产日期

    private Integer sameExpiration;//保质期是否相同

    private Integer expirationDays;//保质期天数



    @Transient
    List<SrmSupplier> supplierList;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "material")
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "product")
    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    @Basic
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Basic
    @Column(name = "enabled")
    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time")
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "is_initialized")
    public String getIsInitialized() {
        return isInitialized;
    }

    public void setIsInitialized(String isInitialized) {
        this.isInitialized = isInitialized;
    }

    @Basic
    @Column(name = "is_purchased")
    public String getIsPurchased() {
        return isPurchased;
    }

    public void setIsPurchased(String isPurchased) {
        this.isPurchased = isPurchased;
    }

    @Basic
    @Column(name = "is_appointed")
    public String getIsAppointed() {
        return isAppointed;
    }

    public void setIsAppointed(String isAppointed) {
        this.isAppointed = isAppointed;
    }

    @Basic
    @Column(name = "is_current")
    public String getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(String isCurrent) {
        this.isCurrent = isCurrent;
    }

    @Basic
    @Column(name = "terminate_orders")
    public String getTerminateOrders() {
        return terminateOrders;
    }

    public void setTerminateOrders(String terminateOrders) {
        this.terminateOrders = terminateOrders;
    }

    @Basic
    @Column(name = "model")
    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    @Basic
    @Column(name = "specifications")
    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    @Basic
    @Column(name = "category")
    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    @Basic
    @Column(name = "source")
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Basic
    @Column(name = "unit_price")
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Basic
    @Column(name = "unit")
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Basic
    @Column(name = "net_weight")
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    @Basic
    @Column(name = "minimum_purchase")
    public Long getMinimumPurchase() {
        return minimumPurchase;
    }

    public void setMinimumPurchase(Long minimumPurchase) {
        this.minimumPurchase = minimumPurchase;
    }

    @Basic
    @Column(name = "last_unit_price")
    public BigDecimal getLastUnitPrice() {
        return lastUnitPrice;
    }

    public void setLastUnitPrice(BigDecimal lastUnitPrice) {
        this.lastUnitPrice = lastUnitPrice;
    }

    @Basic
    @Column(name = "last_provider")
    public Integer getLastProvider() {
        return lastProvider;
    }

    public void setLastProvider(Integer lastProvider) {
        this.lastProvider = lastProvider;
    }

    @Basic
    @Column(name = "last_provider_name")
    public String getLastProviderName() {
        return lastProviderName;
    }

    public void setLastProviderName(String lastProviderName) {
        this.lastProviderName = lastProviderName;
    }

    @Basic
    @Column(name = "is_waring")
    public Byte getIsWaring() {
        return isWaring;
    }

    public void setIsWaring(Byte isWaring) {
        this.isWaring = isWaring;
    }

    @Basic
    @Column(name = "is_ignore")
    public Byte getIsIgnore() {
        return isIgnore;
    }

    public void setIsIgnore(Byte isIgnore) {
        this.isIgnore = isIgnore;
    }

    @Basic
    @Column(name = "location_number")
    public Integer getLocationNumber() {
        return locationNumber;
    }

    public void setLocationNumber(Integer locationNumber) {
        this.locationNumber = locationNumber;
    }

    @Basic
    @Column(name = "supplier_number")
    public Integer getSupplierNumber() {
        return supplierNumber;
    }

    public void setSupplierNumber(Integer supplierNumber) {
        this.supplierNumber = supplierNumber;
    }

    @Basic
    @Column(name = "cancelled_number")
    public Integer getCancelledNumber() {
        return cancelledNumber;
    }

    public void setCancelledNumber(Integer cancelledNumber) {
        this.cancelledNumber = cancelledNumber;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
    //1.119新增字段 unit_id

    private Integer unitId;

    @Basic
    @Column(name="unit_id" )
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    @Basic
    @Column(name = "instance")
    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    @Basic
    @Column(name = "instance_chain")
    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    @Basic
    @Column(name = "approve_status")
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Basic
    @Column(name = "reject_reasion_desc")
    public String getRejectReasionDesc() {
        return rejectReasionDesc;
    }

    public void setRejectReasionDesc(String rejectReasionDesc) {
        this.rejectReasionDesc = rejectReasionDesc;
    }

    @Transient
    public List<SrmSupplier> getSupplierList() {
        return supplierList;
    }

    public void setSupplierList(List<SrmSupplier> supplierList) {
        this.supplierList = supplierList;
    }

    @Column(name="exp_required")
    public Integer getExpRequired() {
        return expRequired;
    }

    public void setExpRequired(Integer expRequired) {
        this.expRequired = expRequired;
    }

    @Column(name = "open_duration")
    public Integer getOpenDuration() {
        return openDuration;
    }

    public void setOpenDuration(Integer openDuration) {
        this.openDuration = openDuration;
    }

    @Column(name = "related_item")
    public Integer getRelatedItem() {
        return relatedItem;
    }

    public void setRelatedItem(Integer relatedItem) {
        this.relatedItem = relatedItem;
    }

    @Column(name = "same_expiration")
    public Integer getSameExpiration() {
        return sameExpiration;
    }

    public void setSameExpiration(Integer sameExpiration) {
        this.sameExpiration = sameExpiration;
    }

    @Column(name = "expiration_days")
    public Integer getExpirationDays() {
        return expirationDays;
    }

    public void setExpirationDays(Integer expirationDays) {
        this.expirationDays = expirationDays;
    }
}
