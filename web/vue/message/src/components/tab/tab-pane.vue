<template>
  <div class="pane" v-show="show">
    <slot></slot>
  </div>
</template>

<style>
  .ty-tab-pane{
    float: left;
    width: 33.3%;
    height: 48px;
    line-height: 48px;
    border-bottom: 2px solid #ff00ff;
    text-align: center;
  }
</style>

<script>
export default {
  name: 'tab-pane',
  props: {
    name: {
      type: String
    },
    label: {
      type: String,
      default: ''
    },
    closable: {
      type: String,
      default: 'true'
    },
    isTab: {
      type: String,
      default: 'true'
    },
    msgCount: {
      type: Number,
      default: 0
    }
  },
  data: function () {
    return {
      show: true
    }
  },
  methods: {
    updateNav: function () {
      this.$parent.updateNav()
    }
  },
  watch: {
    label: function () {
      this.updateNav()
    },
    msgCount: function () {
      this.updateNav()
    }
  },
  mounted: function () {
    this.updateNav()
  }
}
</script>
