package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

@Entity
@Table ( name ="t_tp_app_share" )
public class TpAppShare implements Serializable {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    /**
     * 子机构ID
     */
    @Column(name = "sub_org", nullable=true , unique=false )
    private Long subOrg;

    /**
     * 应用ID
     */
    @Column(name = "app", nullable=true , unique=false )
    private Long app;

    /**
     * 第三方应用帐号
     */
    @Column(name = "app_acc", length=128, nullable=true , unique=false )
    private String appAcc;

    /**
     * 发起人ID
     */
    @Column(name = "promoter", nullable=true , unique=false )
    private Long promoter;

    /**
     * 类型:
     *              1- 注册分享：需以“主播”身份注册的分享
     *              2- 加入分享：需以成员身份加入某工作室的分享
     *              3- 场次分享：工作室成员分享出去的场次计划
     *              4-节目分享：工作室成员分享出去的节目单
     *              5-大分享：用户点击抖音内直播助手外的分享
     */
    @Column(name = "type", nullable=true , unique=false )
    private Byte type;

    /**
     * 标题
     */
    @Column(name = "title", length=100, nullable=true , unique=false )
    private String title;

    /**
     * 内容
     */
    @Column(name = "content", length=255, nullable=true , unique=false )
    private String content;

    /**
     * 备注
     */
    @Column(name = "memo", length=255, nullable=true , unique=false )
    private String memo;

    /**
     * 图片路径
     */
    @Column(name = "image_path", length=255, nullable=true , unique=false )
    private String imagePath;

    /**
     * 创建人id
     */
    @Column(name = "creator", nullable=true , unique=false )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name", length=100, nullable=true , unique=false )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable=true , unique=false )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator", nullable=true , unique=false )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name", length=100, nullable=true , unique=false )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time", nullable=true , unique=false )
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSubOrg() {
        return subOrg;
    }

    public void setSubOrg(Long subOrg) {
        this.subOrg = subOrg;
    }

    public Long getApp() {
        return app;
    }

    public void setApp(Long app) {
        this.app = app;
    }

    public String getAppAcc() {
        return appAcc;
    }

    public void setAppAcc(String appAcc) {
        this.appAcc = appAcc;
    }

    public Long getPromoter() {
        return promoter;
    }

    public void setPromoter(Long promoter) {
        this.promoter = promoter;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
