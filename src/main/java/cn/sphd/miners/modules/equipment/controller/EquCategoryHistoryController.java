package cn.sphd.miners.modules.equipment.controller;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquCategoryHistory;
import cn.sphd.miners.modules.equipment.service.ITEquCategoryHistoryService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_分类历史
 * 20230209 1.247装备器具新增Controller
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment/category/history")
public class EquCategoryHistoryController {
    private String prefix = "system/history";

    @Autowired
    private ITEquCategoryHistoryService tEquCategoryHistoryService;

    @GetMapping()
    public String history() {
        return prefix + "/history";
    }

    /**
     * 查询装备管理_分类历史
     * 20230209 1.247装备器具新增列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TEquCategoryHistory tEquCategoryHistory) {
        List<TEquCategoryHistory> list = tEquCategoryHistoryService.selectTEquCategoryHistoryList(tEquCategoryHistory);
        return list;
    }


    /**
     * 新增装备管理_分类历史
     * 20230209 1.247装备器具新增
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存装备管理_分类历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquCategoryHistory tEquCategoryHistory) {
        return toAjax(tEquCategoryHistoryService.insertTEquCategoryHistory(tEquCategoryHistory));
    }

    /**
     * 修改装备管理_分类历史
     * 20230209 1.247装备器具新增
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TEquCategoryHistory tEquCategoryHistory = tEquCategoryHistoryService.selectTEquCategoryHistoryById(id);
        mmap.put("tEquCategoryHistory", tEquCategoryHistory);
        return prefix + "/edit";
    }

    /**
     * 修改保存装备管理_分类历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquCategoryHistory tEquCategoryHistory) {
        return toAjax(tEquCategoryHistoryService.updateTEquCategoryHistory(tEquCategoryHistory));
    }

    /**
     * 删除装备管理_分类历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquCategoryHistoryService.deleteTEquCategoryHistoryByIds(ids));
    }
}
