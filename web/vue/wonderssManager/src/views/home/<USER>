<template>
  <div className="tongxunlu">
    <div className="wating icon-shouye-active"></div>
    <div className="watTxt">敬请期待</div>
  </div>
</template>
<script>

export default {
  data() {
    return {}
  },
  components: {},
  created() {

  },
  mounted() {

  },
  destroyed() {

  },
  methods: {}
}

</script>

<style lang="scss" scoped>
.tongxunlu {
  .wating {
    display: block;
    width: 200px;
    height: 200px;
    opacity: 0.3;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 100px auto 0;
  }

  .icon-shouye-active {
    background-image: url("@/assets/tabBar/tongxunlu0.png");
  }

  .watTxt {
    font-size: 24px;
    text-align: center;
    opacity: 0.3;
  }
}
</style>
