<!--在本月流水页面 点击按账户查看， 展示账户列表-->

<template>
  <ul class="val_list">
    <li v-for="item in accountList" :key="item.id" class="ta-list">
      <div @click="rou(item)">
        <span>{{ item.name }}</span>
        <span style="display: inline-block;">
        当前余额 {{  item.moeny }}
        <span  class="btnM">
          <i class="fa fa-angle-right"></i>
        </span>
        </span>
      </div>
    </li>
  </ul>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "ViewBA2",
  mixins: [finance],
  data() {
    return {
      accountList: [
        {id: 0, name: "现金/备用金" , meony:0 , },
        {id: 1, name: "银行账户汇总"},
        {id: 0, name: "工行新宜白大道支行"},
        {id: 0, name: "中国农业银行黄埔江西路支行"}

      ]
    }
  },
  mounted() {
    this.getAccountList()
  },
  methods: {
    getAccountList(){
      let org = this.auth.getOrg()
      let params = { oid:org.id , state:2 }
      this.accountList = []
      this.financeApi.getDataByAccountApp(params).then(res1=>{
        let res = res1.data.data
        let accountPeriod1 = res.accountPeriod1
        let accountPeriod2 = res.accountPeriod2
        let accountPeriod3 = res.accountPeriod3 || []
        this.accountList = [
          {id: accountPeriod1.accountId, name: accountPeriod1.bankName, moeny:this.formatCurrency(accountPeriod1.balance)   },
          {id: accountPeriod2.accountId, name: accountPeriod2.bankName, moeny:this.formatCurrency(accountPeriod2.balance)   },
        ]
        accountPeriod3.forEach(bankItem=>{
          this.accountList.push({
            id: bankItem.accountId, name: bankItem.bankName, moeny: this.formatCurrency(bankItem.balance)
          })
        })

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    rou(item){
      console.log('item====', item)
      let f_beginDate = localStorage.getItem('f_beginDate')
      let f_endDate = localStorage.getItem('f_endDate')
      localStorage.setItem('currentAD2Data', JSON.stringify({ beginDate:f_beginDate, endDate:f_endDate }))
      let path = `/currentAD2/${item.id}`
      console.log(path)
      this.$router.push({ path:path })
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.ta-list {
  img {
    margin-top: 2px;
  }
}
.ta-list:nth-child(n+3){
  &>span:nth-child(1) {
    margin-left: 16px;
  }
}
</style>