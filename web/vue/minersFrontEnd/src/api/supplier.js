import {request} from "@/utils/request";
//creator:李玉婷 2024/7/30 08:10 获取供应商列表
export function getSupplierList(data){
    return request({
        url: '../supplier/getProcurementContractList.do',
        data: data
    })
}
//creator:李玉婷 2024/7/30 17:10 获取三种合同,type String 1-正常 2-终止 3-到期
export function getContractBaseList(type, cid){
    let data = {
        supplierId: cid, //供应商id
        type: type  //1-正常 2-终止 3-到期
    }
    return request({
        url: '../supplier/listContractBase.do',
        data: data
    })
}
// creator: 李玉婷，2024-11-09 10:27:24， 获取供应商下的合同详情
export function getContractBase(id) {
    return request({
        url: '../supplier/poContractBaseMes.do',
        data: {id: id}
    })
}
// creator: 李玉婷，2024-11-09 11:13:44， 获取材料
export function getContractMt(supplier) {
    let data = {type: 1, supplier: supplier}
    return request({
        url: '/supplier/getContractMt.do',
        data: data
    })
}
//creator:李玉婷 2024/8/03 13:10 新增合同确定
export function addContract(data){
    return request({
        url: '../supplier/insertSupplierContract.do',
        data: data
    })
}
//creator:李玉婷 2024/11/09 13:15 修改合同确定
export function changeContract(data){
    return request({
        url: '../supplier/updateSupplierContract.do',
        data: data
    })
}
//creator:李玉婷 2024/11/09 13:21 续约合同确定
export function renewContract(data){
    return request({
        url: '../supplier/renewalSupplierContract.do',
        data: data
    })
}
//creator:李玉婷 2024/7/30 08:10 获取供应商列表
export function getSupplierNameList(data){
    return request({
        url: '../supplier/getSupplierByAddContract.do',
        data: data
    })
}
// creator: 李玉婷，2024-11-14 13:17:24， 获取全部合同(正常 终止 到期)
export function getAllContractList(type) {
    return request({
        url: '/supplier/getListByAllSupplierContract.do',
        data: {
            type: type // 1-正常 2-终止 3-到期
        }
    })
}
// creator: 李玉婷，2024-11-14 14:55:03， 获取合同的修改记录
export function getContractChangeHis(id) {
    return request({
        url: '/supplier/poContractBaseHistory.do',
        data: {
            id: id
        }
    })
}
// creator: 李玉婷，2024-11-14 14:55:03， 获取某个合同的全部续约记录
export function getContractRenewHis(id) {
    return request({
        url: '/supplier/poContractSignRecord.do',
        data: {
            primaryId: id
        }
    })
}

// creator: 李玉婷，2024-11-14 14:55:03， 获取某个合同修改记录的详情
export function getContractChangeHisDetail(id) {
    return request({
        url: '/supplier/poContractBaseHisMes.do',
        data: {
            contractHisId: id
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 确定终止合同
export function stopContract(id) {
    return request({
        url: '/supplier/terminateContract.do',
        data: { id: id }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 启用合同时判断合同的状态
export function judgeRecoveryContract(id) {
    return request({
        url: '/supplier/checkReStartPoContractState.do',
        data: {
            id: id
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 确定启用合同
export function recoveryContract(id, type) {
    return request({
        url: '/supplier/reStartPoContract.do',
        data: {
            id: id,
            type: type
        }
    })
}



















