// import service as request from '../utils/request'
// import requests from "../utils/request";
// import auth from '../sys/auth'

import JSONBig from 'json-bigint'
import {request} from "@/utils/request";




/*
 * creator: 2022-5-30 hxz
 * test
 * params:
*/
export function getTxt () {
	return request({
		method: "GET",
		url: '/tp/text.do',
	})
}


//
// 刚吴老师 跟你们说那个 验证是否存在 主播机构接口
// /org/checkLIVEOrg.do     传值 phone 手机号
// 返回值     JsonResult(1, "可以创建")
// JsonResult(new MyException("-2", "工作室已存在"))
//


/*
 * creator: 2022-5-27 hxz
 * 注册页 发送短信验证码
 * params: phone 手机号
 * 返回值：data 中： 1-成功 0-失败，3-手机号格式不对，5 验证码已经发过还未过时
*/ 
export function sendMobileCode (data) {
	return request({
		method: "GET",
		// url: '/org/broadcastVerificationCode.do',
		url: '/tp/tpRegisterVerificationCode.do ',
		// url: '/auth/sendMessageVerificationCodeRegister.do',
		data: data
	})
}
 
/*
 * creator: 2022-5-27  hxz
 * 验证 验证码是否正确
 * params:  phone 手机号 ，code 验证码
*/ 
export function checkCode (data) {

	return request({
		method: "GET",
		url: '/tp/tpCheckRegisterVerificationCode.do',
		// url: '/org/checkBroadcastVerificationCode.do',
		// url: '/auth/verificationCodeMpLogin.do',
    	data:data
	})
}

/*
 * creator: 2022-5-27  hxz
 * 验证 主播自行注册子机构接口 
 * params: phone 手机号 ，userName 抖音名 
*/ 
export function registerSonOrg (data) {

	return request({
		method: "GET", 
		url: '/org/registerSonOrg.do',  
    	data:data
	})
}


/*
 * creator: 2022-5-27  hxz
 * 从机构进入领地 调用的 换token接口
 * params: token 原 token
*/ 
export function updateTokenRemoveUser (data) {
	return request({
		method: "POST",  
		url: '/auth/updateTokenRemoveUser.do',  
    	// data:data
	})
}


/*
 * creator: 2022-5-27  hxz
 * 工作室成员列表接口 (1.210直播助手1)
 * params:    
*/ 
export function getTeamMembers (data) {
	data = data || {}
	return request({
		method: "POST",  
		url: '/org/getTeamMembers.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 加入他人工作室接口
 * params: phone 手机号 ，userName 抖音名，oid 加入的机构id
*/ 
export function joinTeam (data) {
	return request({
		url: '/org/joinTeam.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 将成员移出团队 (
 * params: outUserId 要移出的用户id 
*/ 
export function memberOutTeam (data) {

	return request({
		url: '/org/memberOutTeam.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 工作室已移出团队的成员列表接口
 * params:  
*/ 
export function getOutTeamMembers (data) {
	data = data || {}
	return request({
		url: '/org/getOutTeamMembers.do',  
    	data:data
	})
}
/*
 * creator: 2022-8-16  hxz
 * 将成员重新移入团队 
 * params:  backUserId
*/ 
export function memberBackTeam (data) {
	data = data || {}
	return request({
		url: '/org/memberBackTeam.do',  
    	data:data
	})
}

/* ：成员退出团队接口 (1.210直播助手1)
 * creator: 2022-8-16  hxz
 * 传入参数：
   返回值： true 成功 false 失败
*/ 
export function activeMemberOutTeam () {
	return request({
		url: '/org/activeMemberOutTeam.do',  
	})
}

/*
 * creator: 2022-8-16  hxz
 * 注销工作室接口  
 * params:  
*/ 
export function cancellationTeam (data) {
	data = data || {}

	return request({
		url: '/org/cancellationTeam.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 * 注销账号  
 * params:  
*/ 
export function closeAcc (data) {
	data = data || {}

    return new Promise((resolve, reject) => {
        request({
            url: '/auth/closeAcc.do',
            data: data
        }).then(response => {
			resolve(response)
		}).catch( response => {
			if(response.startsWith('token check error')) {
				let list = response.split('\0')
				if (list instanceof Array && list.length === 2) {
					clearTimeout(list[1])
					response = {status:200,data:{success:1,data:'操作成功！ \n本账号已成功注销！'}}
					resolve(response)
				}
			}
			reject(response)
        })
    })
}

/*
 * creator: 2022-8-16  hxz
 *  领地 账号 信息
 * params:  
*/ 
export function getTerritoryAuthInfo (data) {
	data = data || {}
	return request({
		url: '/authView/getTerritoryAuthInfo.do',  
    	data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 *  领地 手机号修改记录
 * params:  
*/ 
export function getAccEditHistories (data) {
	data = data || {}

	return request({
		url: '/auth/getAccEditHistories.do',  
    	data:data
	})
}

// 
/*
 * creator: 2022-8-16  hxz
 *  领地 获取头像图片展示的跟路径
 * params:  
*/ 
export function getRootPathFile () {
	return request({
		url: '/uploads/getRootPath.do',  
	})
}
/*
 * creator: 2022-8-16  hxz
 *   机构列表 
 * params:  
*/ 
export function organizationList (data) {
	data = data || {}

	return request({
		url: '/appManage/organizationList.do',  
    	// data:data
	})
}

/*
 * creator: 2022-8-16  hxz
 *   登录某个工作室 
 * params:   mobile， oid
 * res: user{"userName"姓名 ,"roleCode",}
*/ 
export function sureLogin (data) {
	data = data || {}
	return request({
		url: '/sys/sureLogin.do',  
    	data:data
	})
}


/*
 * creator: 2022-9-27  hxz
 *   分享接口 
 * params:   
1 subOrg integer 当前登录的机构id
2 accId Integer 与小程序关联的一个id
3 type String 分享类型 1是注册 2是成员
4 module String 抖音这里传“ assistant ”，后续值在继续补充
 * res: 
1 tpAppShare 分享的详细信息
    1.1 id 分享的id 此值要回传
    1.2 type 分享的类型 1是注册 2是成员
    1.3 subOrg 子机构的id 此值也要回传 用途是在注册成员时得知道该机构是否已经有这个成员了 所以得传一下
    1.4 title 标题 可以用我返回的也可以自己写
    1.5 content 内容 可以用我返回的也可以自己写
    1.6 imagePath 图片路径 这里暂时还不知道咋处理等做完后在讨论吧
*/ 
export function ttAassistantSharing (data) {

	return request({
		// url: '/plarform/ttAassistantSharing.do',  
		url: '/mpa/getTtShareMes.do',  
    	// data: data
	})
}


/*
 * creator: 2023-10-27  zxb
 * 关于获取固定的四个模块接口
 * params: 无
*/
export function getAboutModuleList () {
	return request({
		url: '/about/getAboutInitialDirectory.do',
		method: 'post'
	})
}

/*
 * creator: 2023-10-27  zxb
 * 关于获取固定的四个模块接口
 * params:
 * 1.category 文件夹id
 * 2.currentPage 当前页
 * 3.pageSize 每页条数
*/
export function getAboutModuleDetail (data) {
	return request({
		url: '/about/getAboutDirectoryFile.do',
		method: 'post',
		data: data
	})
}

/*
 * creator: 2023-10-27  zxb
 * 关于获取使用帮助中下一级文件夹的接口
 * params:
 * 1.category 文件夹id
*/
export function getAboutNextModuleList (category) {
	return request({
		url: '/about/getAboutDirectoryAndChildDirection.do',
		method: 'post',
		data: {
			category: category
		}
	})
}

/*
 * creator: 2023-10-27  zxb
 * 关于获取使用帮助中文件列表的接口
 * params:
 * 1.category 文件夹id
*/
export function getAboutFileList (data) {
	return request({
		url: '/about/getAboutDirectoryFile.do',
		method: 'post',
		data: data
	})
}

/*
 * creator: 2023-10-27  zxb
 * 关于获取最终详情
 * params:
 * 1.fileId 文件id
*/
export function getAboutFinalDetail (fileId) {
	return request({
		url: '/about/aboutFileSingle.do',
		method: 'post',
		data: {
			fileId: fileId
		}
	})
}

/*
 * creator: 2023-10-28  zxb
 * 关于获取使用帮助中普通搜索接口
 * params:
 * 1.name 搜索的名称
 * 2.currentPage 当前页
 * 3.pageSize 每页条数
 * 4.type 每页条数 2-字幕精灵使用帮助中查询
*/
export function getAboutSearchList (data) {
	return request({
		url: '/about/aboutGeneralFindFile.do',
		method: 'post',
		data: data
	})
}

/*
 * creator: 2023-10-28  zxb
 * 关于获取使用帮助中普通搜索接口
 * params:
 * 1.name 搜索的名称
 * 2.currentPage 当前页
 * 3.pageSize 每页条数
 * 4.type 每页条数 2-字幕精灵使用帮助中查询
*/
export function getMesForShare () {
	return request({
		url: '/about/getMesForShare.do',
		method: 'post'
	})
}



