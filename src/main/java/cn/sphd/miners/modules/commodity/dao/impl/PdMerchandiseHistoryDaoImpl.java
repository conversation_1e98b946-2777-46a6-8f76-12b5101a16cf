package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class PdMerchandiseHistoryDaoImpl extends BaseDao<PdMerchandiseHistory,Serializable> implements PdMerchandiseHistoryDao {
    @Override
    public int insert(PdMerchandise product) {
        PdMerchandiseHistory pcph=new PdMerchandiseHistory();

        BeanUtils.copyProperties(product,pcph);
        pcph.setId(null);
        pcph.setProductCustomer(product.getId());
        if(product.getCustomer()!=null){
            pcph.setCustomer(product.getCustomer().getId());
        }

        save(pcph);

        getSession().flush();

        return pcph.getId();
    }

    @Override
    public int insert(PdMerchandise product, Integer phid) {
        PdMerchandiseHistory pcph=new PdMerchandiseHistory();

        BeanUtils.copyProperties(product,pcph);
        pcph.setId(null);
        pcph.setProductCustomer(product.getId());
        pcph.setProduct(phid);
        if(product.getCustomer()!=null){
            pcph.setCustomer(product.getCustomer().getId());
        }

        save(pcph);

        getSession().flush();

        return pcph.getId();
    }

    @Override
    public int insert(PdMerchandise product, String o) {
        PdMerchandiseHistory pcph=new PdMerchandiseHistory();

        BeanUtils.copyProperties(product,pcph);
        pcph.setId(null);
        pcph.setProductCustomer(product.getId());
        pcph.setProduct(product.getId());
        if(product.getCustomer()!=null){
            pcph.setCustomer(product.getCustomer().getId());
        }
        pcph.setOperation(o);
        save(pcph);

        getSession().flush();

        return pcph.getId();
    }
}
