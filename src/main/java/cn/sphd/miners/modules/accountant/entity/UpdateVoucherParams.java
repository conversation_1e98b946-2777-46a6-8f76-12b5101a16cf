package cn.sphd.miners.modules.accountant.entity;

/**
 * Created by root on 17-5-31.
 */
public class UpdateVoucherParams {

    private int id;
    private int voucher_id;
    private int approve_status;
    private int belong_peroid;
    private String subjectBorrow;
    private String subjectLoan;
    private String reason;
    private double price;//财务的票据金额

    private String cashjson;//现金流量数据的json数组，包含 cashitem,cashes

    private double creditQuantity;//借数量
    private double debitQuantity;//贷数量
    private double unitPrice;//单价

    public double getCreditQuantity() {
        return creditQuantity;
    }

    public void setCreditQuantity(double creditQuantity) {
        this.creditQuantity = creditQuantity;
    }

    public double getDebitQuantity() {
        return debitQuantity;
    }

    public void setDebitQuantity(int debitQuantity) {
        this.debitQuantity = debitQuantity;
    }

    public double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getCashjson() {
        return cashjson;
    }

    public void setCashjson(String cashjson) {
        this.cashjson = cashjson;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getVoucher_id() {
        return voucher_id;
    }

    public void setVoucher_id(int voucher_id) {
        this.voucher_id = voucher_id;
    }

    public int getApprove_status() {
        return approve_status;
    }

    public void setApprove_status(int approve_status) {
        this.approve_status = approve_status;
    }

    public int getBelong_peroid() {
        return belong_peroid;
    }

    public void setBelong_peroid(int belong_peroid) {
        this.belong_peroid = belong_peroid;
    }

    public String getSubjectBorrow() {
        return subjectBorrow;
    }

    public void setSubjectBorrow(String subjectBorrow) {
        this.subjectBorrow = subjectBorrow;
    }

    public String getSubjectLoan() {
        return subjectLoan;
    }

    public void setSubjectLoan(String subjectLoan) {
        this.subjectLoan = subjectLoan;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

}
