html,body,.bg{
    padding: 0;
    margin: 0;
	height: 100%;
	-webkit-font-smoothing: antialiased; 
	font: 14px/1.5 "Helvetica Neue",Helvetica,Arial,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}
form{
	margin:0;
	padding: 0
}
@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
.hd {
    display: none;
}
.bg{
    background:rgba(0,0,0,0.8) url("../../system/images/bg.png") no-repeat 50% 50%;
    -webkit-background-size:100% 100%;
    background-size:100% 100%;
    position: relative;
    overflow: hidden;
}
.content{
    position: relative;
    top: 30%;
}
.ty-right {
    float: right;
    display: inline-block;
}

.loading {
    z-index: 100;
    color: #3cb0a7;
}
.loading i{
    -webkit-animation: spin 1s linear 1s 5 alternate;
    animation: spin 1s linear infinite;
}


.avatar{
    display: -webkit-box; /* Chrome 4+, Safari 3.1, iOS Safari 3.2+ */
    display: -moz-box; /* Firefox 17- */
    display: -webkit-flex; /* Chrome 21+, Safari 6.1+, iOS Safari 7+, Opera 15/16 */
    display: -moz-flex; /* Firefox 18+ */
    display: -ms-flexbox; /* IE 10 */
    display: flex; /* Chrome 29+, Firefox 22+, IE 11+, Opera 12.1/17/18, Android 4.4+ */
    justify-content: space-between;
    width: 960px;
    margin:auto;
}
.seeAllAgency{
    width: 90%;
    margin:auto;
}
.logo_avatar{
    width: 450px;
}
.logo_avatar .logo{
    color: #fff;
    font-size: 90px;
    line-height: 90px;
    margin: 0;
    padding:0;
}
.logo_avatar[type='small'] .logo{
    font-size: 46px;
    line-height: 46px;
}
.logo_avatar .logo_w{
    color: #3cb0a7;
}
.logo_avatar .logo_welcome{
    color: #9aa4a3;
    font-size: 36px;
    line-height: 36px;
}
.logo_avatar[type='small'] .logo_welcome{
    font-size: 18px;
    line-height: 18px;
}
.login_avatar, .agency_avatar, .apply_avatar{
    width: 420px;
    background-color: #fbfafa;
    border-radius: 2px;
    padding: 20px 40px;
    box-shadow: 0 0 6px #242626;
    border: 2px solid #fff;
}
.agency_avatar{
    margin-top: -120px;
}
.agency_avatar_big{
    width: 100%;
    background-color: #fbfafa;
    border-radius: 2px;
    padding: 20px 40px;
    box-shadow: 0 0 6px #242626;
    border: 2px solid #fff;
    margin-top: 8px;
}
.login_avatar .input_avatar{
    border-bottom: 1px solid #dadfdf;
    line-height:46px;
    display: -webkit-box; /* Chrome 4+, Safari 3.1, iOS Safari 3.2+ */
    display: -moz-box; /* Firefox 17- */
    display: -webkit-flex; /* Chrome 21+, Safari 6.1+, iOS Safari 7+, Opera 15/16 */
    display: -moz-flex; /* Firefox 18+ */
    display: -ms-flexbox; /* IE 10 */
    display: flex; /* Chrome 29+, Firefox 22+, IE 11+, Opera 12.1/17/18, Android 4.4+ */
    justify-content: space-between;
    position: relative;
}
.login_avatar .input_avatar .input_title{
    display: inline-block;
    height: 46px;
    width: 70px;
    font-size: 16px;
}
.login_avatar .input_avatar input{
    border: none;
    background-color: inherit;
    padding: 6px 4px;
    height: 46px;
    line-height:46px;
    font-size: 16px;
    color: #666;
    width: 270px;
}
.input_avatar .clearInput{
    position: absolute;
    top: 1px;
    right: 0;
    color: #ccc;
    line-height: 46px;
    font-size: 16px;
    display: none;
}
.input_avatar  input:focus + i.clearInput{
    display: block;
}
.login_avatar .item{
    margin-top: 24px;
}
.agency_avatar .item, .agency_avatar_big .item, .apply_avatar .item {
    margin-top: 8px;
}
.login_avatar .item:first-child{
    margin-top: 8px;
}
.login_avatar .item .error{
    max-width: 210px;
}
.ty-btn{
    display: block;
    border: none;
    height: 36px;
    line-height:36px;
    text-align: center;
    background-color: #3cb0a7;
    border-radius: 2px;
    padding: 0 12px;
    color: #fff;
    text-decoration: none;
}
.ty-btn:hover{
    background-color: #39a79e;
    color: #fff;
    text-decoration: none;
}
.ty-btn:active, .ty-btn:visited, .ty-btn:link{
    text-decoration: none;
}
.ty-btn:disabled{
    background-color: #ddd;
    color: #a3a3a3;
    cursor: default;
}
.ty-btn:hover:disabled{
    background-color: #ddd;
    color: #a3a3a3;
}
.login_btn, .logout_btn, .apply_btn, .reback_btn{
    width: 100%;
    font-size: 16px;
    height: 46px;
    line-height:46px;
    font-weight: bold;
}
.logout_btn, .reback_btn{
    background-color: #ccc;
    color: #999;
}
.logout_btn:hover, .reback_btn:hover{
    background-color: #c2c2c2;
    color: #999;
}
.other_btn{
    display: -webkit-box; /* Chrome 4+, Safari 3.1, iOS Safari 3.2+ */
    display: -moz-box; /* Firefox 17- */
    display: -webkit-flex; /* Chrome 21+, Safari 6.1+, iOS Safari 7+, Opera 15/16 */
    display: -moz-flex; /* Firefox 18+ */
    display: -ms-flexbox; /* IE 10 */
    display: flex; /* Chrome 29+, Firefox 22+, IE 11+, Opera 12.1/17/18, Android 4.4+ */
    justify-content: space-between;
}
.linkbb a{
    float: right;
    font-size: 0.9em;
}
.riMar{
    margin-right: 10px;
}
.allMethodC a:nth-child(1){
    text-align: left;
}
.allMethodC a:nth-child(3){
    text-align: right;
}
.allMethodC a{
    text-align: center;
    color: #666;
    font-size: 12px;
    width: 33.3%;
}
.allMethodC .fa{
    font-size: 20px;
}
.allMethodC .fa-weixin{
    color: #56c171;
}

.allMethodC .fa-envelope{
    color: #db8427;
}

.allMethodC .fa-user-circle-o{
    color: #3cb0a7;
    position: relative;
    left: -15px;
}

hr{ border-top:1px solid #ddd; }
.allMethod{
    position: relative;
    top: 30px;
    background: #fbfafa;
    width: 197px;
    left: 73px;
    display: inline-block;
    text-align: center;
}
.loginOther {
    position: relative;
    top: -87px;
    height: 20px;
}
#wxLogin{
    border: none;
    width: 300px;
    margin: 0 auto;
}
.getCodeBtn{
    margin-top: 5px;
}
.tip{
    color: #999;
}
.color-red{
    color: #ed5565;
}
.color-orange{
    color: #f58410;
}
.color-blue{
    color: #3cb0a7;
}
a{
    cursor: pointer;
}
.input_choose_list{
    position: absolute;
    width: 265px;
    top: 46px;
    left: 70px;
    border: 1px solid #dcdfe6;
    display: flex;
    flex-direction: column;
    background: #fff;
    display: none;
    max-height: 200px;
    overflow: auto;
    box-shadow: 0 1px 2px #a3acaa;
    z-index: 50;
}
.input_choose_item{
    padding: 6px 10px;
    cursor: pointer;
    line-height: 24px;
}
.input_choose_item:hover{
    background-color: #e9ecee;
}
ul.agencyList{
    list-style: none;
    margin: 0;
    padding: 0;
}
ul.agencyList li{
    border-bottom: 1px solid #2fa59c;
    background-color: #3cb0a7;
    color: #fff;
    margin: 0;
    font-size: 14px;
    /*font-weight: bold;*/
    padding: 10px 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}
ul.agencyList_big{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0;
}
ul.agencyList_big li{
    flex-grow: 1;
    background-color: #3cb0a7;
    color: #fff;
    font-size: 14px;
    /*font-weight: bold;*/
    padding: 10px 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    width: 250px;
    margin: 5px;
    border-radius: 2px;
}
ul.agencyList li .message_corner, ul.agencyList_big li .message_corner{
    float: right;
}
.phone{
    font-size: 36px;
    color: #999;
    font-weight: bold;
}
.page-new{
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    height: 100%;
    background-color: #fff;
    z-index: 10;
}
.page-header{
    position: fixed;
    right: 0;
    left: 0;
    top: 0;
    width: 100%;
    margin: 0;
    border: 0;
    padding: 0;
    height: 46px;
    min-height: 46px;
    filter: none;
    background-image: none;
    background-color: #2b3643;
}
.page-logo{
    float: left;
    display: block;
    width: 185px;
    height: 46px;
    line-height: 46px;
}
.minersLogo{
    display: inline-block;
    color: #eaeaea;
    font-size: 25px;
    position: relative;
    cursor: default;
    font-family: 'Arial';
    width: 140px;
    text-align: center;
}
.green_logo {
    color: #3CB0A7;
}
.page-container{
    height: calc(100vh - 46px);
    margin-top: 46px;
    display: flex;
    flex-direction: row;
}
.page-sidebar-wrapper{
    width: 185px;
    background-color: #364150;
    height: 100%;
}
.page-content{
    height: 100%;
    background-color: #fcfcfc;
    flex: auto;
}
.ty-container{
    margin-top: 36px;
    padding: 10px;
    overflow: auto;
    height: calc(100% - 36px);
}
.SMSVerification{
    width: 500px;
    margin-left: 100px;
    margin-top: 60px;
}
.SMSVerification .item{
    margin-top: 30px;
}
.SMSVerification .input_avatar {
    border-bottom: 1px solid #dadfdf;
    line-height: 46px;
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    justify-content: space-between;
    position: relative;
}
.SMS_phone{
    display: inline-block;
    width: 430px;
    padding: 6px 4px;
    height: 46px;
    line-height: 34px;
    font-size: 16px;
    color: #666;
}
.SMSVerification .input_avatar .input_title {
    display: inline-block;
    height: 46px;
    width: 70px;
    font-size: 16px;
}
.SMSVerification .input_avatar input {
    border: none;
    background-color: inherit;
    padding: 6px 4px;
    height: 46px;
    line-height: 46px;
    font-size: 16px;
    color: #666;
    width: 270px;
}
.text-btn{
    cursor: pointer;
    background-color: #fff;
    color: #101010;
    display: inline-block;
    border: none;
    border-radius: 2px;
    padding: 0px 24px;
    height: 32px;
    line-height: 32px;
}
.text-btn-green{
    background-color: #5d9cec;
    color: #fff;
}
.text-btn-red{
    background-color: #ed5565;
    color: #fff;
}
.text-btn:disabled{
    background-color: #ccc;
    color: #666;
}
.tip{
    color: #5d9cec
}
#checkCode{
    width: 90px;
    height: 36px;
}
.verificationCode{
    line-height: 26px;
    margin-top: -16px;
    margin-left: 8px;
}
.verificationCode span{
    font-size: 12px;
}
.title_big{
    font-size: 16px;
    text-align: center;
}