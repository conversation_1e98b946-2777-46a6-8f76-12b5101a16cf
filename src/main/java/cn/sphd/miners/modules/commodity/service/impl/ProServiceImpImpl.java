package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.PartsService;
import cn.sphd.miners.modules.commodity.service.ProService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.processManagement.dao.PpmProcessStatDao;
import cn.sphd.miners.modules.processManagement.service.ProcessSettingsService;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResMapper;
import cn.sphd.miners.modules.sales.dao.*;
import cn.sphd.miners.modules.sales.entity.*;
import cn.sphd.miners.modules.sales.service.ContractBaseNewService;
import cn.sphd.miners.modules.sales.service.ContractBaseService;
import cn.sphd.miners.modules.sales.service.SaleService;
import cn.sphd.miners.modules.sales.service.SlOrdersService;
import cn.sphd.miners.modules.system.dao.OrgDao;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class ProServiceImpImpl implements ProService {
    @Autowired
    PdMerchandiseDao productDao;

    @Autowired
    SlOrdersItemDao ordersItemDao;
    @Autowired
    PdPackDao pdPackDao;
    @Autowired
    PdAssembleDao pdAssembleDao;

    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    PdMerchandiseHistoryDao productHistoryDao;
    @Autowired
    PdBaseHistoryDao pdBaseHistoryDao;
    @Autowired
    OrgDao orgDao;
    @Autowired
    PdCustomerDao customerDao;
    @Autowired
    UnitService unitService;
    @Autowired
    UploadService uploadService;

    @Autowired
    PdCommodityMediaDao pdCommodityMediaDao;
    @Autowired
    PdCommodityMediaHistoryDao pdCommodityMediaHistoryDao;
    @Autowired
    SlContractCommodityDao slContractCommodityDao;
    @Autowired
    SlContractCommodityHistoryDao slContractCommodityHistoryDao;
    @Autowired
    ContractBaseService contractBaseService;
    @Autowired
    SlContractBaseHistoryDao slContractBaseHistoryDao;
    @Autowired
    SlContractBaseDao slContractBaseDao;
    @Autowired
    RoleService roleService;

    @Autowired
    PartsService partsService;
    @Autowired
    PdCustomerProductAddressDao pdCustomerProductAddressDao;
    @Autowired
    PdCustomerProductAddressHistoryDao pdCustomerProductAddressHistoryDao;
    @Autowired
    PdCustomerAddressDao pdCustomerAddressDao;
    @Autowired
    PdCustomerAddressHistoryDao pdCustomerAddressHistoryDao;
    @Autowired
    private PdMerchandiseInvoiceDao merchandiseInvoiceDao;
    @Autowired
    private PdMerchandiseInvoiceHistoryDao merchandiseInvoiceHistoryDao;
    @Autowired
    SlOrdersService ordersService;

    @Autowired
    ProcessSettingsService processSettingsService;
    @Autowired
    PpmProcessStatDao ppmProcessStatDao;

    @Autowired
    SaleService saleService;

    @Autowired
    PdResourceDao pdResourceDao;
    @Autowired
    PdResourceHistoryDao pdResourceHistoryDao;
    @Autowired
    PdImageDao pdImageDao;
    @Autowired
    PdImageHistoryDao pdImageHistoryDao;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserPopedomService popedomService;
    @Autowired
    ResMapper resMapper;
    @Autowired
    ContractBaseNewService contractBaseNewService;

    @Override
    public String addProduct(PdMerchandise product, String commodityMediaList, User user) {
        Date newDate = new Date();
        PdMerchandise p = productDao.getByHQL("from PdMerchandise where outerSn='" + product.getOuterSn() + "' and org=" + user.getOid());
        if (p != null) {
            return "该商品代号已存在!";
        }
        //是否简易模式
        ApprovalItem jyItem = roleService.getCurrentItem(user.getOid(),"finishedProductCheck");
        //是否自动关联
        ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "commodityProduct");
        PdModelSettings pdModelSettings = null;
        if (approvalItem != null||jyItem!=null) {
            pdModelSettings = roleService.getPdModelSettings(approvalItem.getId());
            if (pdModelSettings != null||(jyItem!=null&&jyItem.getStatus()==0)) {
                PdBase pdBase = pdBaseDao.getByHQL("from PdBase where innerSn='" + product.getOuterSn() + "' and oid=" + user.getOid());
                if (pdBase != null) {
                    return "该图号已存在";
                }

            }
        }


        //获取当前的机构状态
        if (jyItem!=null&&jyItem.getStatus()==0){
            ///当前简易模式
            product.setUnitDeduction(0);
        }


        if (product.getUnitPrice() != null) {
            product.setHasInvoice("1");
        }

        if (product.getUnitPriceReference() != null) {
            product.setHasInvoice("0");
        }
        if (product.getUnitPriceNoinvoice() != null) {
            product.setHasInvoice("0");
        }
        if (product.getUnitPriceNotax() != null) {
            product.setHasInvoice("1");
        }
        if (product.getUnitPriceInvoice() != null) {
            product.setHasInvoice("1");
        }

        product.setEnabled("1");
        product.setCreator(user.getUserID());
        product.setCreateName(user.getUserName());
        product.setCreateDate(newDate);
        product.setPeroid(Integer.valueOf(DateUtil.getDy(product.getCreateDate())));
        product.setOperation("1");
        product.setVersionNo(1);

        if (product.getCustomer() != null) {
            SlCustomer customer = customerDao.get(product.getCustomer().getId());
            if (customer != null) {
                product.setCustomerName(customer.getName());
            }
        }
        productDao.save(product);

        int module = 1;
        if ("1".equals(product.getType())) {
            module = 1;
        } else {
            module = 2;
        }

        unitService.selectUnit(product.getUnitId(), module);
        Integer phId = productHistoryDao.insert(product);
        productHistoryDao.insert(product, "5");

        //判断是否新增产品
        if (approvalItem != null||jyItem!=null) {
            if (("1".equals(product.getType()) && pdModelSettings!=null && pdModelSettings.getGeneralModel() == 2)
                    || ("2".equals(product.getType()) && (pdModelSettings!=null && pdModelSettings.getDedicatedModel() == 2))
                    ||(jyItem!=null&&jyItem.getStatus()==0)) {
                //自动关联产品
                PdBase pdBase = new PdBase();
                pdBase.setType("1");
                pdBase.setUnit(product.getUnit());
                pdBase.setUnitId(product.getUnitId());
                pdBase.setModel(product.getModel());
                pdBase.setSpecifications(product.getSpecifications());
                pdBase.setInnerSn(product.getOuterSn());
                pdBase.setName(product.getOuterName());
                pdBase.setOid(user.getOid());
                pdBase.setEnabled("1");
                pdBase.setProcessDeptName("未知");
                pdBase.setCreateName("系统");
                pdBase.setCreateDate(newDate);
                pdBase.setOperation("1");
                pdBase.setVersionNo(1);

                if ("".equals(pdBase.getWeightUnit())) {
                    pdBase.setWeightUnit(null);
                }
                if ("".equals(pdBase.getComposition())) {
                    pdBase.setComposition(null);
                }
                pdBaseDao.save(pdBase);
                pdBaseDao.getSession().flush();
                pdBaseHistoryDao.setHistory(pdBase);

                product.setProduct(pdBase);
                //自动关联
                User u = new User();
                u.setUserName("系统");
                confirmAssociation(product.getId(), pdBase.getId(), u);
                if (pdBase.getUnitId() != null) {
                    //更新计量单位
                    unitService.selectUnit(pdBase.getUnitId(), 3);
                }
//                    PdProductMerchandise pdProductMerchandise=new PdProductMerchandise();
//                    pdProductMerchandise.setOrg(user.getOid());
//                    pdProductMerchandise.setMerchandise(product.getId());
//                    pdProductMerchandise.setProduct(product.getId());
//                    pdProductMerchandise.setType();
//                    pdProductMerchandiseDao.save(pdProductMerchandise);


            }

        }


        //判断是否关联合同
        if (product.getContractId() != null && !"0".equals(product.getContractId().toString())) {

            //新增时关联合同的新方法 -----zsx
            contractBaseNewService.addZSProductForUpContract(user,product.getContractId(),product.getId());


            /*SlContractBase slContractBase = contractBaseService.getContractBase(product.getContractId());
            if (slContractBase != null) {
                slContractBase.setOperation("3");
                slContractBase.setVersionNo(slContractBase.getVersionNo() + 1);
                SlContractBaseHistory his = slContractBaseHistoryDao.insert(slContractBase);
                //查找所有商品,添加歷史
                List<SlContractCommodity> pros = slContractCommodityDao.getListByHQL("from SlContractCommodity where contract=" + slContractBase.getId());
                for (SlContractCommodity sc : pros) {
                    slContractCommodityHistoryDao.insert(sc, his.getId(), null);
                }
                SlContractCommodity scc = new SlContractCommodity();
                scc.setOrg(user.getOid());
                scc.setCreator(user.getUserID());
                scc.setCreateDate(newDate);
                scc.setCreateName(user.getUserName());
                scc.setOrders(0);
                scc.setContract(product.getContractId());
                scc.setCommodity(product.getId());
                scc.setOperation("1");
                scc.setVersionNo(0);
                slContractCommodityDao.save(scc);
                slContractCommodityHistoryDao.insert(scc, his.getId(), phId);
            }*/
        }

        //添加商品文件
        if (commodityMediaList != null && !"".equals(commodityMediaList)) {
            JSONArray cml = JSON.parseArray(commodityMediaList);
            if (cml.size() > 0) {
                for (int i = 0; i < cml.size(); i++) {
                    JSONObject a = cml.getJSONObject(i);
                    //上次路径
                    String uplaodPath = a.getString("uplaodPath");
                    //排序吗
                    Integer orders = a.getInteger("orders");
                    //类型
                    String type = a.getString("type");
                    //文件名
                    String title = a.getString("title");

                    PdCommodityMedia pdCommodityMedia = new PdCommodityMedia();
                    pdCommodityMedia.setOrg(user.getOid());
                    pdCommodityMedia.setCreator(user.getUserID());
                    pdCommodityMedia.setCreateDate(newDate);
                    pdCommodityMedia.setCreateName(user.getUserName());
                    pdCommodityMedia.setUplaodPath(uplaodPath);
                    pdCommodityMedia.setOrders(orders);
                    pdCommodityMedia.setType(type);
                    pdCommodityMedia.setTitle(title);
                    pdCommodityMedia.setCommodity(product.getId());
                    pdCommodityMedia.setOperation("1");
                    pdCommodityMedia.setVersionNo(0);
                    pdCommodityMediaDao.save(pdCommodityMedia);
                    pdCommodityMediaDao.getSession().flush();
                    PdCommodityMediaHistory cmh = pdCommodityMediaHistoryDao.insert(pdCommodityMedia, phId);

                    //添加文件库
                    ProductUsing cmu = new ProductUsing(pdCommodityMedia.getId(), PdCommodityMedia.class);
                    uploadService.addFileUsing(cmu, pdCommodityMedia.getUplaodPath(), title, user, "商品管理");
                    ProductUsing sihu = new ProductUsing(cmh.getId(), PdCommodityMediaHistory.class);
                    uploadService.addFileUsing(sihu, cmh.getUplaodPath(), title, user, "商品管理");

                }
            }
        }

        if (StringUtils.isNotEmpty(product.getAddressListString())) {
            JSONArray array = JSON.parseArray(product.getAddressListString());
            if (array.size() > 0) {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject a = array.getJSONObject(i);
                    //收获地址id
                    Integer customerAddress = a.getInteger("customerAddress");

                    PdCustomerAddress pdCustomerAddress = pdCustomerAddressDao.get(customerAddress);
                    if (pdCustomerAddress == null) {
                        continue;
                    }

                    PdCustomerProductAddress pdCustomerProductAddress = new PdCustomerProductAddress();
                    pdCustomerProductAddress.setEnabled(1);
                    pdCustomerProductAddress.setEnabledTime(newDate);
                    pdCustomerProductAddress.setCreator(user.getUserID());
                    pdCustomerProductAddress.setCreateName(user.getUserName());
                    pdCustomerProductAddress.setCreateDate(newDate);
                    pdCustomerProductAddress.setCustomerAddress(customerAddress);
                    pdCustomerProductAddress.setProductCustomer(product.getId());
                    pdCustomerProductAddress.setOperation("1");
                    pdCustomerProductAddressDao.save(pdCustomerProductAddress);


                    PdCustomerAddressHistory pdCustomerAddressHistory = pdCustomerAddressHistoryDao.insertNoCustomerAddress(pdCustomerAddress);


                    //添加商品地址记录
                    pdCustomerProductAddressHistoryDao.insert(pdCustomerProductAddress, phId, pdCustomerAddressHistory.getId());

                }
            }

        }


        return "success";

    }

    @Override
    public Map<String, Object> getTyProductListByCategory(Integer oid, String category, String param, PageInfo pageInfo, String type, int isgl, Integer userId) {
        Object[] p = new Integer[]{};
        String sql = "SELECT new Map(p.id AS id,\n" +
                "p.outerSn AS outerSn,\n" +
                "p.outerName as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.unitId as unitId,\n" +
                "p.priceDesc as priceDesc,\n" +
                "p.unitPriceReference as unitPriceReference,\n" +
                "p.unitPriceNoinvoice as unitPriceNoinvoice,\n" +
                "p.unitPriceInvoice as unitPriceInvoice,\n" +
                "p.invoiceCategory as invoice_category,\n" +
                "p.taxRate as taxRate,\n" +
                "p.unitPrice as unitPrice,\n" +
                "p.unitPriceNotax as unitPriceNotax,\n" +
                "p.invoiceCategory as invoiceCategory,\n" +
                "p.initialStock as initialStock,\n" +
                "p.currentStock as currentStock,\n" +
                "p.availableStock as availableStock,\n" +
                "p.minimumStock as minimumStock,\n" +
                "p.createName as createName,\n" +
                "p.createDate as createDate,\n" +
                "p.type as type,(select count(si.id) FROM SlOrdersItem as si left join SlOrders so on so.id=si.orders where coalesce(so.outState,0) not in (5,6) and si.salesRelationship = p.id ) as orderNum ) "+
                "from \n" +
                "PdMerchandise p\n" +
                "WHERE\n" +
                "p.org =" + oid + "\n" +
                "and p.enabled ='1' \n" +
                "and p.type =" + type + "\n";
        if ("2".equals(type)) {
            if (userId != null) {
                sql = sql + " and p.creator=" + userId;
            }

        }
        if (category != null && !"".equals(category)) {
            sql = sql + " and p.category=" + category;
        }
        if (param != null && !"".equals(param)) {
            sql = sql + " and (p.outerSn like '%" + param + "%' or p.outerName like '%" + param + "%')\n";
        }

        if (isgl == 1) {
            sql += " and coalesce(p.product,0)!=0";
        }
        sql = sql + " ORDER BY p.createDate";

        List< Map<String, Object>> list=productDao.getListByHQLWithNamedParams(sql, new HashMap<>(),pageInfo);

        Map<String,Object> map=new HashMap<>();
        map.put("data",list);
        try {
            map.put("totalRows",pageInfo.getPageBean().getTotalResult());
        }catch (Exception e){
            map.put("totalRows",0);
        }
        map.put("totalPage",pageInfo.getTotalPage());
        map.put("code","200");

        return map;
    }

    @Override
    public List<Map<String, Object>> getProductList(Integer oid, String type, Integer customerId) {

        String sql = "SELECT\n" +
                "new Map(p.id AS id,\n" +
                "p.outerSn AS outerSn,\n" +
                "p.outerName as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.unitId as unitId) \n" +
                "from PdMerchandise p\n" +
                "WHERE\n" +
                "p.org =" + oid + "\n" +
                "and p.enabled ='1' \n" +
                "and p.type =" + type + " and (p.product is null or  p.product ='')\n ";

        if (customerId != null) {
            sql = sql + " and p.customer=" + customerId;
        }

        sql = sql + " ORDER BY p.createDate";

        return productDao.getListByHQLWithNamedParams(sql, null);
    }

    @Override
    public Map<String, Object> getZSProductListByCustomer(Integer oid, String customerId, String param, PageInfo pageInfo, Integer userId) {

        if ("0".equals(customerId)) {
            return getTyProductListByCategory(oid, null, param, pageInfo, "1", 0, userId);
        }

        Object[] p = new Integer[]{};
        String sql = "SELECT new Map(\n" +
                "p.id AS id,\n" +
                "p.outerSn AS outerSn,\n" +
                "p.outerName as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.unitId as unitId,\n" +
                "p.initialStock as initialStock,\n" +
                "p.currentStock as currentStock,\n" +
                "p.availableStock as availableStock,\n" +
                "p.minimumStock as minimumStock,\n" +
                "p.createName as createName,\n" +
                "p.createDate as createDate,\n" +
                "p.type as type," +
                "c.name as name," +
                "c.deliveryWay as deliveryWay,\n" +
                "c.deliveryType as deliveryType,\n" +
                "(select count(si.id) From SlOrdersItem si left join SlOrders so on so.id=si.orders where coalesce(so.outState,0) not in (5,6) and si.salesRelationship=p.id) as orderNum )\n" +
                "from \n" +
                "PdMerchandise p\n" +
                "LEFT JOIN SlCustomer c on p.customer_ =c.id\n" +
                "WHERE\n" +
                "p.org =" + oid + "\n" +
                "and p.enabled ='1' \n";


        if (customerId != null && !"".equals(customerId)) {
            if ("0".equals(customerId)) {
                sql = sql + " and  (p.customer_ is null or p.customer_ ='')";
            } else {
                sql = sql + " and p.customer_=" + customerId;
            }
            sql = sql + " and p.type ='2'";
        } else {
            if (userId != null) {
                sql = sql + " and (( c.principal=" + userId + " and p.type='2') or  p.type='1' )";
            }
        }
        if (param != null && !"".equals(param)) {
            sql = sql + " and (p.outerSn like '%" + param + "%' or p.outerName like '%" + param + "%')\n";
        }


        sql = sql + " ORDER BY p.createDate";

        List< Map<String, Object>> list=productDao.getListByHQLWithNamedParams(sql, new HashMap<>(),pageInfo);


           for (Map<String, Object> m:list
             ) {
            m.remove("customer");
        }
        Map<String,Object> map=new HashMap<>();
        map.put("data",list);
        try {
            map.put("totalRows",pageInfo.getTotalResult());
        }catch (Exception e){
            map.put("totalRows",0);
        }
        map.put("totalPage",pageInfo.getTotalPage());
        map.put("code","200");
        return map;
    }


    @Override
    public PdMerchandise getProductOne(Integer productId) {

        PdMerchandise product = productDao.get(productId);
        /*SlContractBase slContractBase = contractBaseService.getContractBaseByProduct(productId);
        if (slContractBase != null) {
            product.setContractId(slContractBase.getId());
            product.setContractNumber(slContractBase.getSn());
        }*/

        List<SlContractBase> listContract = contractBaseNewService.listContractByCommodityId(productId);
        if (!listContract.isEmpty()) {
            StringBuffer contractNumber = new StringBuffer();
            for (SlContractBase s : listContract) {
                contractNumber.append(s.getSn()).append(" ");
            }
            product.setContractNumber(contractNumber.toString());
        }

        if (product.getProduct_() != null) {
            PdBase pdBase = pdBaseDao.get(product.getProduct_());
            if (pdBase != null) {
                product.setProductName(pdBase.getName());
                product.setInnerSn(pdBase.getInnerSn());
                if ("系统".equals(pdBase.getCreateName())) {
                    product.setAddType("1");
                }
                if ("系统".equals(product.getCreateName())) {
                    product.setAddType("2");
                }
            }
        }
        //获取文件列表
        //获取文件列表
        String sql = "select i.orders,i.id,i.uplaod_path uplaodPath,i.title,i.type from t_pd_commodity_media i\n" +
                "where i.commodity=" + productId;
        List<Map<String, Object>> list = pdCommodityMediaDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
        product.setFileList(list);

        //获取启停记录
        String sql2 = "select enabled,update_date updateDate,update_name updateName from t_pd_merchandise_history where product_customer=" + productId + " and operation='7' ";
        List<Map<String, Object>> list2 = productHistoryDao.getSession().createSQLQuery(sql2).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
        product.setStartAndStopList(list2);

        List<SlOrdersItem> sls = ordersItemDao.getListByHQL("from SlOrdersItem where salesRelationship=" + productId);

        product.setOrderNum(sls.size());

        //获取价格元素
        PdMerchandiseHistory m1 = pdMerchandiseHistory(productId, "D", "1");
        if (m1 != null) {
            //判断是否生效
            if ("1".equals(m1.getEffectiveOption())) {
                //立即生效，不用管
                product.setExpirationDate(null);
            } else {
                //到生效时间，替换
                if (new Date().getTime() > m1.getEffectiveDate().getTime()) {
                    product.setEffectiveDate(m1.getEffectiveDate());
                    product.setExpirationDate(null);
                    product.setInvoiceCategory(m1.getInvoiceCategory());
                    product.setTaxRate(m1.getTaxRate());
                    product.setUnitPrice(m1.getUnitPrice());
                    product.setUnitPriceNotax(m1.getUnitPriceNotax());
                    product.setUnitPriceInvoice(m1.getUnitPriceInvoice());
                    product.setUnitPriceNotax(m1.getUnitPriceNotax());
                    product.setUnitPriceReference(m1.getUnitPriceReference());
                    product.setHasInvoice(m1.getHasInvoice());
                } else {
                    //没到生效时间，给当前价格一个截至日期
                    product.setExpirationDate(m1.getEffectiveDate());
                }
            }
        }
        return product;
    }

    @Override
    public String suspendProduct(Integer productId, String state, User user) {
        PdMerchandise product = productDao.get(productId);
        if (product == null) {
            return "商品信息错误";
        }
        product.setEnabled(state);
        product.setUpdateName(user.getUserName());
        product.setUpdateDate(new Date());
        product.setUpdator(user.getUserID());
        product.setOperation("7");
        productDao.update(product);

        productHistoryDao.insert(product);

        return "success";
    }

    @Override
    public String suspendProductJudge(Integer productId, String state) {
        if ("0".equals(state)) {
            PdMerchandise product = productDao.get(productId);
            if (product == null) {
                return "0";
            }

            if (product.getCurrentStock() != null && product.getCurrentStock().compareTo(new BigDecimal(0)) == 1) {
                return "-1";
            }

        }
        return "1";
    }

    @Override
    public String deleteProduct(Integer productId) {
        PdMerchandise product = productDao.get(productId);
        if (product == null) {
            return "0";
        }
        if (product.getProduct() != null) {
            return "0";
        }
        List<SlOrdersItem> sls = ordersItemDao.getListByHQL("from SlOrdersItem where salesRelationship=" + productId);
        if (sls.size() > 0) {
            return "0";
        }

        List<PdPack> pps = pdPackDao.getListByHQL(" from PdPack where outer_id=" + productId);
        if (pps.size() > 0) {
            return "0";
        }

        List<SlContractCommodity> scList = slContractCommodityDao.getListByHQL(" from SlContractCommodity where commodity=" + productId);
        if (scList.size() > 0) {
            return "0";
        }


        productDao.delete(product);


        return "success";
    }

    @Override
    public String deleteProductJudge(Integer productId) {
        PdMerchandise product = productDao.get(productId);
        if (product == null) {
            return "0";
        }
        if (product.getProduct() != null) {
            return "0";
        }
        List<SlOrdersItem> sls = ordersItemDao.getListByHQL("from SlOrdersItem where salesRelationship=" + productId);
        if (sls.size() > 0) {
            return "0";
        }

        List<PdPack> pps = pdPackDao.getListByHQL(" from PdPack where outer_id=" + productId);
        if (pps.size() > 0) {
            return "0";
        }

        return "1";
    }

    @Override
    public String updateProductBase(PdMerchandise product, String commodityMediaList, User user, String type) {
        PdMerchandise p = productDao.get(product.getId());
        Date newDate = new Date();

        if (p == null) {
            return "商品信息错误";
        }

        p.setUpdateDate(newDate);
        p.setUpdateName(user.getUserName());
        p.setUpdator(user.getUserID());
        if ("jb".equals(type)) {
            PdMerchandise pcp = productDao.getByHQL("from PdMerchandise where outerSn='" + product.getOuterSn() + "' and org=" + user.getOid());
            if (pcp != null) {
                if (pcp.getId() != p.getId()) {
                    return "该商品代号已存在!";
                }
            }
            if (p.getProduct_() != null) {
                //是否简易模式
                ApprovalItem jyItem = roleService.getCurrentItem(user.getOid(),"finishedProductCheck");

                ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "commodityProduct");
                PdModelSettings pdModelSettings = null;
                if (approvalItem != null||jyItem!=null) {
                    pdModelSettings = roleService.getPdModelSettings(approvalItem.getId());
                    if (pdModelSettings != null||(jyItem!=null&&jyItem.getStatus()==0)) {
                        PdBase pb = pdBaseDao.getByHQL("from PdBase where innerSn='" + product.getOuterSn() + "' and oid=" + user.getOid());
                        PdBase pdBase = pdBaseDao.get(p.getProduct_());
                        if (pb != null && pdBase.getId() != pb.getId()) {
                            return "该产品图号已存在";
                        } else {
                            //修改产品
//                            pdBase.setUnit(product.getUnit());
//                            pdBase.setUnitId(product.getUnitId());
                            pdBase.setModel(product.getModel());
                            pdBase.setSpecifications(product.getSpecifications());
                            pdBase.setInnerSn(product.getOuterSn());
                            pdBase.setName(product.getOuterName());
                            pdBase.setOid(user.getOid());
                            // pdBase.setEnabled("1");
                            //  pdBase.setProcessDeptName("未知");
                            //pdBase.setCreateName("系统");
                            pdBase.setUpdateDate(newDate);
                            pdBase.setOperation("3");
                            pdBase.setVersionNo(pdBase.getVersionNo() + 1);
                            pdBase.setUpdateName(user.getUserName());
                            pdBase.setUpdator(user.getUserID());

                            pdBaseDao.update(pdBase);
                            pdBaseHistoryDao.setHistory(pdBase);
                        }
                    }
                }

            }


            //最低库存修改
            if (p.getMinimumStock() == null && product.getMinimumStock() != null) {
                p.setMinimumStock(product.getMinimumStock());
                productHistoryDao.insert(p, "5");
            }


            //最低库存修改
            if (p.getMinimumStock() != null && product.getMinimumStock() != null) {
                if (p.getMinimumStock().doubleValue() != product.getMinimumStock().doubleValue()) {
                    p.setMinimumStock(product.getMinimumStock());
                    productHistoryDao.insert(p, "5");
                }
            }
            p.setOuterSn(product.getOuterSn());
            p.setOuterName(product.getOuterName());
            p.setModel(product.getModel());
            p.setSpecifications(product.getSpecifications());
//            p.setUnit(product.getUnit());
//            p.setUnitId(product.getUnitId());


            p.setVersionNo(p.getVersionNo() + 1);
            p.setOperation("3");//修改基本信息
            p.setExclusiveMonth(product.getExclusiveMonth());
            p.setExclusiveTime(product.getExclusiveTime());

            p.setBasicReason(product.getBasicReason());
            if (StringUtils.isNotEmpty(product.getMemo())) {
                p.setMemo(product.getMemo());
            }
            if (StringUtils.isNotEmpty(product.getPriceDesc())) {
                p.setPriceDesc(product.getPriceDesc());
            }
            productDao.update(p);
            //1.254修改基本信息后部分数据进入待处理-zy
            //获取该商品关联到的未完结订单
            Map map = saleService.getOccBySalesRelation(user.getOid(), null, null, p.getId());

            //获取有发货管理的用户
            List<UserPopedom> sendUsers = popedomService.getAllUserPopedomByMid(user.getOid(), "yb");
            //获取有订单管理的用户
            List<UserPopedom> saleUsers = popedomService.getAllUserPopedomByMid(user.getOid(), "qc");

//            saleUsers.addAll(sendUsers);

            ArrayList<HashMap> list = (ArrayList) map.get("data");
            Integer ald = 0;//待提出角标
            Integer alc = 0;//待处理角标
            if (list != null) {
                for (HashMap m : list) {
                    Integer orderId = (Integer) m.get("id");
                    List<SlOrdersItem> items = ordersItemDao.getListByHQL(" from SlOrdersItem o where o.orders_=?0 and o.salesRelationship.id=?1", orderId, p.getId());
                    if (items != null) {
                        for (SlOrdersItem item : items) {
                            //若当前数据已经在待提出中，减少待提出角标
                            if (item.getOperation() != null && item.getOperation() == 7)
                                ald = 1;
                            //若当前数据已经在待处理中，减少待处理角标
                            if (item.getOperation() != null && item.getOperation() == 8)
                                alc = 1;
                            item.setOperation(7);//设置为7，代表修改过
                            item.setDeliveredMeasure("");
                            item.setUndeliveredMeasure(null);
                            ordersItemDao.update(item);

                            //添加方案待提出角标，只给有订单管理的人加
                            HashMap ms = (HashMap) ordersService.getSuList(user, 0);
                            for (UserPopedom u : saleUsers) {
                                swMessageService.rejectSend(1, 1, ms, u.getUserId() + "", "productChange", null, null, u.getUser(), "productChangePlanApproval");
//                                swMessageService.rejectSendToMid(1, 1, ms,u.getOid(),"qc", "productChangePlanApproval", "productChange", null, "");
                                if (ald != 0)
                                    swMessageService.rejectSend(-ald, -ald, ms, u.getUserId() + "", "productChange", null, null, u.getUser(), "productChangePlanApproval");
                            }

                            for (UserPopedom u : sendUsers) {
                                if (alc != 0)
                                    swMessageService.rejectSend(-alc, -alc, ms, u.getUserId() + "", "productChange", null, null, u.getUser(), "productChangePlanApproval");
                            }
                        }
                    }
                }


            }

            int hisId = productHistoryDao.insert(p);


        } else {

            Integer hisPdbase = null;
            if (p.getProduct() != null) {
                PdBase pdBase = pdBaseDao.get(p.getProduct_());
                if (pdBase != null) {
                    hisPdbase = pdBaseHistoryDao.setHistory(pdBase);
                }

            }
            p.setBasicReason(product.getBasicReason());
            if (StringUtils.isNotEmpty(product.getMemo())) {
                p.setMemo(product.getMemo());
            }
            if (StringUtils.isNotEmpty(product.getPriceDesc())) {
                p.setPriceDesc(product.getPriceDesc());
            }

            p.setUnit(product.getUnit());
            p.setUnitId(product.getUnitId());
            p.setMinimumStock(product.getMinimumStock());
            p.setVersionNo(p.getVersionNo() + 1);
            p.setOperation("B");//修改其他信息
            productDao.update(p);
            int hisId = productHistoryDao.insert(p, hisPdbase);


            int module = 1;


            if ("1".equals(product.getType())) {
                module = 1;
            } else {
                module = 2;
            }
            unitService.selectUnit(product.getUnitId(), module);

            //删除商品图片
            List<PdCommodityMedia> fileList = pdCommodityMediaDao.getListByHQL("from PdCommodityMedia where commodity=" + product.getId());
            for (PdCommodityMedia ci : fileList) {
                List<PdCommodityMediaHistory> hlist = pdCommodityMediaHistoryDao.getListByHQL("from PdCommodityMediaHistory where commodityMedia=" + ci.getId());
                for (PdCommodityMediaHistory sih : hlist) {
                    sih.setCommodityMedia(null);

                }

                ProductUsing cbu = new ProductUsing(ci.getId(), PdCommodityMedia.class);
                uploadService.delFileUsing(cbu, ci.getUplaodPath(), user);
                pdCommodityMediaDao.deleteById(ci.getId());
            }
            //添加商品文件
            if (commodityMediaList != null && !"".equals(commodityMediaList)) {
                JSONArray cml = JSON.parseArray(commodityMediaList);
                if (cml.size() > 0) {
                    for (int i = 0; i < cml.size(); i++) {
                        JSONObject a = cml.getJSONObject(i);
                        //上次路径
                        String uplaodPath = a.getString("uplaodPath");
                        //排序吗
                        Integer orders = a.getInteger("orders");
                        //类型
                        String fileType = a.getString("type");
                        //文件名
                        String title = a.getString("title");

                        PdCommodityMedia pdCommodityMedia = new PdCommodityMedia();
                        pdCommodityMedia.setOrg(user.getOid());
                        pdCommodityMedia.setCreator(user.getUserID());
                        pdCommodityMedia.setCreateDate(newDate);
                        pdCommodityMedia.setCreateName(user.getUserName());
                        pdCommodityMedia.setUplaodPath(uplaodPath);
                        pdCommodityMedia.setOrders(orders);
                        pdCommodityMedia.setType(fileType);
                        pdCommodityMedia.setTitle(title);
                        pdCommodityMedia.setCommodity(product.getId());
                        pdCommodityMedia.setOperation("1");
                        pdCommodityMedia.setVersionNo(0);
                        pdCommodityMediaDao.save(pdCommodityMedia);
                        pdCommodityMediaDao.getSession().flush();
                        PdCommodityMediaHistory cmh = pdCommodityMediaHistoryDao.insert(pdCommodityMedia, hisId);
                        //添加文件库
                        ProductUsing cmu = new ProductUsing(pdCommodityMedia.getId(), PdCommodityMedia.class);
                        uploadService.addFileUsing(cmu, pdCommodityMedia.getUplaodPath(), title, user, "商品管理");
                        ProductUsing sihu = new ProductUsing(cmh.getId(), PdCommodityMediaHistory.class);
                        uploadService.addFileUsing(sihu, cmh.getUplaodPath(), title, user, "商品管理");

                    }
                }
            }

            //删除的旧的收获地址
            Map<String, Object> params = new HashMap<>();
            params.put("product", product.getId());
            String hql = "delete from PdCustomerProductAddress where  productCustomer=:product";
            pdCustomerProductAddressDao.queryHQLWithNamedParams(hql, params);
            //绑定收获地址
            if (StringUtils.isNotEmpty(product.getAddressListString())) {
                JSONArray array = JSON.parseArray(product.getAddressListString());
                if (array.size() > 0) {
                    for (int i = 0; i < array.size(); i++) {


                        JSONObject a = array.getJSONObject(i);
                        //收获地址id
                        Integer customerAddress = a.getInteger("customerAddress");

                        PdCustomerAddress pdCustomerAddress = pdCustomerAddressDao.get(customerAddress);
                        if (pdCustomerAddress == null) {
                            continue;
                        }

                        PdCustomerProductAddress pdCustomerProductAddress = new PdCustomerProductAddress();
                        pdCustomerProductAddress.setEnabled(1);
                        pdCustomerProductAddress.setEnabledTime(newDate);
                        pdCustomerProductAddress.setCreator(user.getUserID());
                        pdCustomerProductAddress.setCreateName(user.getUserName());
                        pdCustomerProductAddress.setCreateDate(newDate);
                        pdCustomerProductAddress.setCustomerAddress(customerAddress);
                        pdCustomerProductAddress.setProductCustomer(product.getId());
                        pdCustomerProductAddress.setOperation("1");
                        pdCustomerProductAddressDao.save(pdCustomerProductAddress);


                        PdCustomerAddressHistory pdCustomerAddressHistory = pdCustomerAddressHistoryDao.insertNoCustomerAddress(pdCustomerAddress);


                        //添加商品地址记录
                        pdCustomerProductAddressHistoryDao.insert(pdCustomerProductAddress, hisId, pdCustomerAddressHistory.getId());


                    }
                }
            }

            //判断是否关联合同
            if (product.getContractId() != null && !"0".equals(product.getContractId().toString())) {

                SlContractBase slContractBase = contractBaseService.getContractBase(product.getContractId());
                //查询之前绑定的合同
                SlContractBase sb = contractBaseService.getContractBaseByProduct(product.getId());
                if (sb != null) {  //如果之前已绑定
                    sb.setOperation("3");
                    sb.setVersionNo(slContractBase.getVersionNo() + 1);
                    SlContractBaseHistory sbh = slContractBaseHistoryDao.insert(sb);
                    SlContractCommodity oldSc = slContractCommodityDao.getByHQL("from SlContractCommodity where commodity=" + product.getId() + " and contract=" + sb.getId());
                    if (oldSc != null) {

                        oldSc.setContract(null);
                        oldSc.setCommodity(null);
                        slContractCommodityDao.update(oldSc);


                        //查找所有商品,添加歷史
                        Map<String, Object> map = new HashMap<>();
                        map.put("contract", sb.getId());
                        List<SlContractCommodity> pros = slContractCommodityDao.getListByHQLWithNamedParams("from SlContractCommodity where contract=:contract", map);
                        for (SlContractCommodity sc : pros) {
                            if (!sc.getId().toString().equals(oldSc.getId().toString())) {
                                slContractCommodityHistoryDao.insert(sc, sbh.getId(), null);
                            }
                        }
                    } else {
                        //查找所有商品,添加歷史
                        Map<String, Object> map = new HashMap<>();
                        map.put("contract", sb.getId());
                        List<SlContractCommodity> pros = slContractCommodityDao.getListByHQLWithNamedParams("from SlContractCommodity where contract=:contract", map);
                        for (SlContractCommodity sc : pros) {
                            slContractCommodityHistoryDao.insert(sc, sbh.getId(), null);
                        }
                    }

                    //如果合同不是同一个，绑定新的商品。。如果为新合同，继续往下执行
                    if (sb.getId().toString().equals(product.getContractId().toString())) {
                        SlContractCommodity scc = new SlContractCommodity();
                        scc.setOrg(user.getOid());
                        scc.setCreator(user.getUserID());
                        scc.setCreateDate(newDate);
                        scc.setCreateName(user.getUserName());
                        scc.setOrders(0);
                        scc.setContract(product.getContractId());
                        scc.setCommodity(product.getId());
                        scc.setOperation("1");
                        scc.setVersionNo(0);
                        slContractCommodityDao.save(scc);
                        slContractCommodityHistoryDao.insert(scc, sbh.getId(), hisId);
                        return "success";
                    }
                }
                //如果之前未绑定  或更换新的合同
                slContractBase.setOperation("3");
                slContractBase.setVersionNo(slContractBase.getVersionNo() + 1);
                SlContractBaseHistory his = slContractBaseHistoryDao.insert(slContractBase);
                //查找所有商品,添加歷史
                List<SlContractCommodity> pros = slContractCommodityDao.getListByHQL("from SlContractCommodity where contract=" + slContractBase.getId());
                for (SlContractCommodity sc : pros) {
                    slContractCommodityHistoryDao.insert(sc, his.getId(), null);
                }
                SlContractCommodity scc = new SlContractCommodity();
                scc.setOrg(user.getOid());
                scc.setCreator(user.getUserID());
                scc.setCreateDate(newDate);
                scc.setCreateName(user.getUserName());
                scc.setOrders(0);
                scc.setContract(product.getContractId());
                scc.setCommodity(product.getId());
                scc.setOperation("1");
                scc.setVersionNo(0);
                slContractCommodityDao.save(scc);
                slContractCommodityHistoryDao.insert(scc, his.getId(), hisId);


            } else {
                //删除已关联的合同信息
                SlContractBase slContractBase = contractBaseService.getContractBaseByProduct(product.getId());
                if (slContractBase != null) {
                    slContractBase.setOperation("3");
                    slContractBase.setVersionNo(slContractBase.getVersionNo() + 1);
                    SlContractBaseHistory sbh = slContractBaseHistoryDao.insert(slContractBase);
                    SlContractCommodity oldSc = slContractCommodityDao.getByHQL("from SlContractCommodity where commodity=" + product.getId() + " and contract=" + slContractBase.getId());

                    if (oldSc != null) {
                        oldSc.setContract(null);
                        oldSc.setCommodity(null);
                        slContractCommodityDao.update(oldSc);
                        //查找所有商品,添加歷史
                        List<SlContractCommodity> pros = slContractCommodityDao.getListByHQL("from SlContractCommodity where contract=" + slContractBase.getId());
                        for (SlContractCommodity sc : pros) {
                            if (!sc.getId().toString().equals(oldSc.getId().toString())) {
                                slContractCommodityHistoryDao.insert(sc, sbh.getId(), null);
                            }
                        }
                    } else {
                        //查找所有商品,添加歷史
                        List<SlContractCommodity> pros = slContractCommodityDao.getListByHQL("from SlContractCommodity where contract=" + slContractBase.getId());
                        for (SlContractCommodity sc : pros) {
                            slContractCommodityHistoryDao.insert(sc, sbh.getId(), null);
                        }
                    }
                }

//            PdBaseHistory pdBaseHistory=pdBaseHistoryDao.get(product.getProduct_());
//            if(pdBaseHistory!=null){
//                product.setProductName(pdBaseHistory.getName());
//            }
            }


        }

        return "success";
    }

    @Override
    public Map<String, Object> suspendProductList(Integer oid, String param, PageInfo pageInfo, String type) {
        Object[] p = new Integer[]{};
        String sql = "SELECT new Map(\n" +
                "p.id AS id,\n" +
                "p.outerSn AS outerSn,\n" +
                "p.outerName as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.unitId as unitId,\n" +
                "p.initialStock as initialStock,\n" +
                "p.currentStock as currentStock,\n" +
                "p.availableStock as availableStock,\n" +
                "p.minimumStock as minimumStock,\n" +
                "p.createName as createName,\n" +
                "p.createDate as createDate,\n" +
                "p.updateName as updateName,\n" +
                "p.updateDate as updateDate )\n" +
                "from \n" +
                "PdMerchandise p\n" +
                "WHERE\n" +
                "p.org =" + oid + "\n" +
                "and p.enabled ='0' \n";


        if (type != null && !"".equals(type)) {
            sql = sql + "and p.type =" + type + "\n";
        }
        if (param != null && !"".equals(param)) {
            sql = sql + "and (p.outerSn like '%" + param + "%' or p.outerName like '%" + param + "%')\n";
        }


        sql = sql + "ORDER BY p.createDate";

        List< Map<String, Object>> list=productDao.getListByHQLWithNamedParams(sql, new HashMap<>(),pageInfo);

        Map<String,Object> map=new HashMap<>();
        map.put("data",list);
        try {
            map.put("totalRows",pageInfo.getPageBean().getTotalResult());
        }catch (Exception e){
            map.put("totalRows",0);
        }
        map.put("totalPage",pageInfo.getTotalPage());
        map.put("code","200");
        return map;

    }

    @Override
    public List<Map<String, Object>> suspendProductListForList(Integer oid, String param, String type) {
        String sql = "SELECT new Map(\n" +
                "p.id AS id,\n" +
                "p.outerSn AS outerSn,\n" +
                "p.outerName as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.unitId as unitId,\n" +
                "p.initialStock as initialStock,\n" +
                "p.currentStock as currentStock,\n" +
                "p.availableStock as availableStock,\n" +
                "p.minimumStock as minimumStock,\n" +
                "p.createName as createName,\n" +
                "p.createDate as createDate )\n" +

                "FROM\n" +
                "PdMerchandise p\n" +
                "WHERE\n" +
                "p.org =" + oid + "\n" +
                "and p.enabled ='0' \n";
        if (type != null && !"".equals(type)) {
            sql = sql + "and p.type =" + type + "\n";
        }
        List<Map<String, Object>> list=productDao.getListByHQLWithNamedParams(sql,new HashMap<>());
        return list;
    }

    @Override
    public List<Map<String, Object>> getProductRecordBaseList(Integer customerProductId, String operation) {
        String hql = "select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate,basicReason as basicReason,memo as memo) from PdMerchandiseHistory where productCustomer=" + customerProductId + " and (operation='1' or operation='" + operation + "')";
        List<Map<String, Object>> list = pdBaseDao.getListByHQLWithNamedParams(hql, new HashMap<>());
        return list;
    }

    @Override
    public PdMerchandiseHistory getProductRecordBaseDetails(Integer id) {
        PdMerchandiseHistory product = productHistoryDao.get(id);
        SlContractBaseHistory slContractBase = contractBaseService.getContractBaseByProductHistory(id);
        if (slContractBase != null) {
            product.setContractId(slContractBase.getId());
            product.setContractNumber(slContractBase.getSn());
        }

//
        if (product.getProduct() != null) {
            PdBaseHistory pdBase = pdBaseHistoryDao.get(product.getProduct());
            if (pdBase != null) {
                product.setProductName(pdBase.getName());
                product.setInnerSn(pdBase.getInnerSn());
            }
        }
        //获取文件列表
        String sql = "select new Map(i.orders as orders,i.id as id,i.uplaodPath  as uplaodPath,i.title as title,i.type as type) from PdCommodityMediaHistory i\n" +
                "where i.commodityHistory=:commodityHistory";
        Map<String, Object> param = new HashMap<>();
        param.put("commodityHistory", id);
        List<Map<String, Object>> list = pdCommodityMediaDao.getListByHQLWithNamedParams(sql, param);
        product.setFileList(list);

        product.setAddressList(this.getProductAddressListHistory(id));
//
//        //获取启停记录
//        String sql2="select enabled,update_date updateDate,update_name updateName from t_pd_merchandise_history where product="+productId+" and operation='7' ";
//        List<Map<String,Object>> list2=productHistoryDao.getSession().createSQLQuery(sql2).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
//        product.setStartAndStopList(list2);
//
//        List<SlOrdersItem> sls=ordersItemDao.getListByHQL("from SlOrdersItem where salesRelationship="+productId);
//
//        product.setOrderNum(sls.size());

        return product;
    }

    @Override
    public List<Map<String, Object>> getProductGuanLianList(Integer customerProductId) {
        String hql = "select new map(pph.id as id,pph.createName as createName,pph.createDate as createDate,pph.updateName as updateName,pph.updateDate as updateDate,pph.correlaterName as correlaterName,pph.correlateDate as correlateDate,ph.name as name,ph.innerSn as innerSn) from PdMerchandiseHistory pph left join PdBaseHistory ph on pph.product= ph.id where pph.productCustomer =" + customerProductId + " and ph.id is not null  and pph.operation='6' ";
        List<Map<String, Object>> list = pdBaseDao.getListByHQLWithNamedParams(hql, new HashMap<>());
        return list;
    }

    @Override
    public Map<String, Object> getPdBaseGuanLianList(Integer oid, String param, String source, String composition, Integer pageNo, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "SELECT\n" +
                "b.id as id,\n" +
                "b.inner_sn as innerSn,\n" +
                "b.name as name,\n" +
                "b.specifications as specifications,\n" +
                "b.model as model,\n" +
                "b.source as source," +
                "b.composition as composition," +
                "b.unit as unit," +
                "b.net_weight as netWeight," +
                "b.weight_unit as weightUnit," +
                "b.create_name as createName," +
                "b.create_date as createDate," +
                "count( p.id ) as num,\n" +
                "GROUP_CONCAT(\n" +
                "CONCAT( p.outer_sn, '/', p.outer_name ))  as productNames \n" +
                "FROM\n" +
                "t_pd_base b\n" +
                "LEFT JOIN t_pd_merchandise p ON b.id = p.product \n" +
                "where b.oid=" + oid + " and b.id  in (select product from  t_pd_merchandise p2 where p2.product=b.id)";
        if (StringUtils.isNotEmpty(param)) {
            sql = sql + " and (b.inner_sn like '%" + param + "%' or b.name like '%" + param + "%')\n";
        }
        if (StringUtils.isNotEmpty(source)) {
            sql = sql + " and b.process=" + source;
        }
        if (StringUtils.isNotEmpty(composition)) {
            sql = sql + "  and b.composition=" + composition;
        }
        sql = sql + " GROUP BY\n" +
                "b.id";

        return productDao.findMapByConditionByPage(sql, "", p, pageNo, pageSize);
    }

    @Override
    public PdMerchandiseHistory getProductRecordOtherDetails(Integer id) {
        PdMerchandiseHistory product = productHistoryDao.get(id);
        Map<String, Object> param = new HashMap<>();
        param.put("commodityHistory", product.getId());
        SlContractCommodityHistory slContractCommodityHistory = (SlContractCommodityHistory) pdCommodityMediaHistoryDao.getByHQLWithNamedParams("from SlContractCommodityHistory where commodityHistory=:commodityHistory", param);
        if (slContractCommodityHistory != null) {
            SlContractBaseHistory slContractBaseHistory = slContractBaseHistoryDao.get(slContractCommodityHistory.getContractHistory());
            if (slContractBaseHistory != null) {
                product.setContractId(slContractBaseHistory.getId());
                product.setContractNumber(slContractBaseHistory.getSn());
            }
        }
        if (product.getProduct() != null) {
            PdBaseHistory pdBaseHistory = pdBaseHistoryDao.get(product.getProduct());
            if (pdBaseHistory != null) {
                product.setProductName(pdBaseHistory.getName());
            }
        }

        //获取收货地址
        product.setAddressList(getProductAddressHistoryList(product.getId()));
        return product;
    }

    @Override
    public Map<String, Object> getWaitRelevanceProList(Integer oid, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS outerSn,\n" +
                "p.outer_name as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate,\n" +
                "if(p.type=1,'通用',c.full_name) customerName\n" +

                "FROM\n" +
                "t_pd_merchandise p\n" +
                "left join t_sl_customer c on c.id=p.customer\n" +
                "WHERE\n" +
                "p.org =" + oid + " and (p.product is null or product='') ";


        sql = sql + " ORDER BY p.create_date";

        //List<Map<String,Object>> list= productDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();


        return productDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getAlreadyProList(Integer oid, String param, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS outerSn,\n" +
                "p.outer_name as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate,\n" +
                "p.correlater_name as correlaterName,\n" +
                "p.correlate_date as correlateDate,\n" +
                "if(p.type=1,'通用',c.full_name) customerName\n" +

                "FROM\n" +
                "t_pd_merchandise p\n" +
                "left join t_sl_customer c on c.id=p.customer\n" +
                "WHERE\n" +
                "p.org =" + oid + " and p.product is not null and product!='' ";


        if (param != null && !"".equals(param)) {
            sql = sql + " and (p.outer_sn like '%" + param + "%' or p.outer_name like '%" + param + "%')\n";
        }


        sql = sql + " ORDER BY p.create_date";

        return productDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }


    @Override
    public Map<String, Object> getWaitPdPerList(Integer oid, String param, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "select id as id,inner_sn as innerSn,name as name,model as model,specifications as specifications,unit as unit,source as source,composition as composition,net_weight as netWeight,IF(weight_unit='',null,weight_unit) as weightUnit,create_name as createName,create_date as createDate  from  t_pd_base where oid=" + oid + " and enabled=1 and type='1'";
        if (param != null && !"".equals(param)) {
            sql = sql + "  and (inner_sn like '%" + param + "%' or name like '%" + param + "%') ";
        }


        return pdBaseDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    public List<Map<String, Object>> getProductListByPdBase(Integer pdBaseId) {


        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS outerSn,\n" +
                "p.outer_name as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate,\n" +
                "ifnull(p.correlater_name,p.create_name) as correlaterName,\n" +
                "ifnull(p.correlate_date,p.create_date) as correlateDate,\n" +
                "if(p.type=1,'通用',c.full_name) customerName\n" +

                "FROM\n" +
                "t_pd_merchandise p\n" +
                "left join t_sl_customer c on c.id=p.customer\n" +
                "WHERE\n" +
                "p.product =" + pdBaseId;


        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public String confirmAssociation(Integer pdBaseId, Integer productId, User user) {
        PdMerchandise product = productDao.get(productId);
        if (product == null) {
            return "商品信息错误";
        }

        PdBase pdBase = pdBaseDao.get(pdBaseId);
        if (pdBase == null) {
            return "产品信息错误";
        }
        pdBase.setOperation("9");
        Integer phid = pdBaseHistoryDao.setHistory(pdBase);
        product.setProduct(pdBase);
        product.setCorrelater(user.getUserID());
        product.setCorrelaterName(user.getUserName());
        product.setCorrelateDate(new Date());

        product.setOperation("6");

        productHistoryDao.insert(product, phid);
        return "success";
    }

    @Override
    public Map<String, Object> getProductCorrelation(Integer productId) {
        Map<String, Object> map = new HashMap<>();
        PdMerchandise product = productDao.get(productId);
        if (product != null) {
            String pdBaseHql = "select new map(id as id,innerSn as innerSn,name as name,model as model,specifications as specifications,unit as unit,source as source,composition as composition,netWeight as netWeight,weightUnit as weightUnit,createName as createName,createDate as createDate)  from  PdBase where id=" + product.getProduct_();

            Map<String, Object> pdBase = (Map<String, Object>) pdBaseDao.getByHQLWithNamedParams(pdBaseHql, new HashMap<>());
            map.put("pdBase", pdBase);

            map.put("productList", getProductListByPdBase(product.getProduct_()));
        }

        return map;
    }

    @Override
    public Map<String, Object> getPdBaseList(Integer oid, String category, String process, String composition, String param, String type, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.source AS source,\n" +
                "p.composition AS composition,\n" +
                "p.net_weight AS netWeight,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate\n" +
                "FROM\n" +
                "t_pd_base p\n" +
                "WHERE\n" +
                " p.oid =" + oid + " and p.enabled=1 and type='" + type + "'  ";


        if (process != null && !"".equals(process)) {
            sql = sql + " and p.process='" + process + "'";
        }
        if (composition != null && !"".equals(composition)) {
            sql = sql + " and p.composition='" + composition + "'";
        }
        if (param != null && !"".equals(param)) {
            sql = sql + " and (p.inner_sn like '%" + param + "%' or p.name like '%" + param + "%') ";
        }
        if ("2".equals(type)) {
            sql = sql + " and  (select COUNT(0) from t_pd_composition pc where p.id=pc.product) > 0 ";
        }
        System.out.println(sql);
        return pdBaseDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }


    @Override
    public List<Map<String, Object>> getPdBaseList(Integer oid) {
        Object[] p = new Integer[]{};
        String sql = "SELECT\n" +
                "new Map(id AS id,\n" +
                "innerSn as innerSn,\n" +
                "name AS name,\n" +
                "model AS model,\n" +
                "specifications AS specifications,\n" +
                "unit AS unit,\n" +
                "source AS source,\n" +
                "composition AS composition,\n" +
                "netWeight AS netWeight,\n" +
                "weightUnit AS weightUnit,\n" +
                "createName AS createName,\n" +
                "createDate AS createDate )\n" +
                "FROM\n" +
                "PdBase\n" +
                "WHERE\n" +
                "oid =" + oid + " and enabled=1 and type='1' ";


        return pdBaseDao.getListByHQLWithNamedParams(sql, null);
    }


    @Override
    public String addPdBase(PdBase pdBase, String zsProductList, String tyProductId, String resourceList, String cptzImage, String xhImage, User user) {

        PdBase p = pdBaseDao.getByHQL("from PdBase where innerSn='" + pdBase.getInnerSn() + "' and oid=" + user.getOid());
        if (p != null) {
            return "该图号已存在";
        }
        pdBase.setEnabled("1");
        pdBase.setCreator(user.getUserID());
        pdBase.setCreateName(user.getUserName());
        pdBase.setCreateDate(new Date());
        pdBase.setOperation("1");
        pdBase.setVersionNo(1);

        if ("".equals(pdBase.getWeightUnit())) {
            pdBase.setWeightUnit(null);
        }
        if ("".equals(pdBase.getComposition())) {
            pdBase.setComposition(null);
        }
        pdBaseDao.save(pdBase);
        pdBaseDao.getSession().flush();
        pdBaseHistoryDao.setHistory(pdBase);

        if (pdBase.getUnitId() != null) {
            //更新计量单位
            unitService.selectUnit(pdBase.getUnitId(), 3);
        }


        //判断是否需要关联商品
        if (StringUtils.isNotEmpty(zsProductList)) {
            JSONArray array = JSON.parseArray(zsProductList);
            if (array != null) {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject a = array.getJSONObject(i);
                    Integer productId = a.getInteger("productId");
                    confirmAssociation(pdBase.getId(), productId, user);
                }
            }
        }

        //判断是否新增资源文件
        if (StringUtils.isNotEmpty(resourceList)) {
            addResourceList(resourceList, pdBase.getId(), user);
        }
        //新增图片
        if (StringUtils.isNotEmpty(cptzImage)) {
            PdImage pdImage = JSONObject.parseObject(cptzImage, PdImage.class);
            pdImage.setCreateDate(new Date());
            pdImage.setCreator(user.getUserID());
            pdImage.setCreateName(user.getUserName());
            pdImage.setUpdateDate(new Date());
            pdImage.setUpdateName(user.getUserName());
            pdImage.setUpdator(user.getUserID());
            pdImage.setOperation("1");
            pdImage.setProduct(pdBase.getId());
            pdImage.setCategory(1);
            pdImageDao.save(pdImage);

            PdImageHistory ih = pdImageHistoryDao.insert(pdImage);

            //添加文件库
            PdBaseImageUsing pu = new PdBaseImageUsing(pdImage.getId(), PdImage.class);
            uploadService.addFileUsing(pu, pdImage.getUplaodPath(), pdImage.getTitle(), user, "产品档案");
            PdBaseImageUsing puh = new PdBaseImageUsing(ih.getId(), PdImageHistory.class);
            uploadService.addFileUsing(puh, ih.getUplaodPath(), ih.getTitle(), user, "产品档案");
        }
        if (StringUtils.isNotEmpty(xhImage)) {
            PdImage pdImage = JSONObject.parseObject(xhImage, PdImage.class);
            pdImage.setCreateDate(new Date());
            pdImage.setCreator(user.getUserID());
            pdImage.setCreateName(user.getUserName());
            pdImage.setOperation("1");
            pdImage.setProduct(pdBase.getId());
            pdImage.setCategory(2);
            pdImage.setUpdateDate(new Date());
            pdImage.setUpdateName(user.getUserName());
            pdImage.setUpdator(user.getUserID());
            pdImageDao.save(pdImage);
            PdImageHistory ih = pdImageHistoryDao.insert(pdImage);

            //添加文件库
            PdBaseImageUsing pu = new PdBaseImageUsing(pdImage.getId(), PdImage.class);
            uploadService.addFileUsing(pu, pdImage.getUplaodPath(), pdImage.getTitle(), user, "产品档案");
            PdBaseImageUsing puh = new PdBaseImageUsing(ih.getId(), PdImageHistory.class);
            uploadService.addFileUsing(puh, ih.getUplaodPath(), ih.getTitle(), user, "产品档案");
        }
        //判断是否需要关联商品
        if (tyProductId != null) {
            String[] tyProductIds = tyProductId.split(",");
            for (String id : tyProductIds
            ) {
                confirmAssociation(pdBase.getId(), Integer.valueOf(id), user);
            }
        }


        return "success";
    }

    @Override
    public PdBase getPdBaseOne(Integer pdBaseId) {
        String hql = "select new map(count(si.id) as  orderNum) from SlOrdersItem si left join SlOrders so on so.id=si.orders  where IFNULL(so.outState,0) not in (5,6) and si.salesRelationship in (SELECT id from PdMerchandise where product=" + pdBaseId + ")";
        PdBase pdBase = pdBaseDao.get(pdBaseId);
        Map<String, Object> m = (Map<String, Object>) pdBaseDao.getDtoByHql(hql);
        pdBase.setOrderNum(String.valueOf(m.get("orderNum")));

        PdImage image1 = pdImageDao.getByProductAndCategory(pdBaseId, 1);
        pdBase.setCptzImage(image1);
        PdImage image2 = pdImageDao.getByProductAndCategory(pdBaseId, 2);
        pdBase.setXhImage(image2);

        List<PdResource> list = pdResourceDao.getListByHQLWithNamedParams("from PdResource where product=" + pdBaseId, new HashMap<>());

        for (PdResource resource : list) {
            ResEntity r = resMapper.getSingle(new ResEntity(resource.getResource()));
            resource.setResEntity(r);
        }
        pdBase.setResourceList(list);

        return pdBase;
    }

    @Override
    public String updatePdBase(PdBase pdBase, User user, String flag) {
        PdBase p = pdBaseDao.get(pdBase.getId());


        if (p == null) {
            return "商品信息错误";
        }
        p.setInnerSn(pdBase.getInnerSn());
        p.setName(pdBase.getName());
        p.setModel(pdBase.getModel());
        p.setSpecifications(pdBase.getSpecifications());
        p.setUnit(pdBase.getUnit());
        p.setUnitId(pdBase.getUnitId());
        p.setMemo(pdBase.getMemo());
        p.setWeightUnit(pdBase.getWeightUnit());
        p.setNetWeight(pdBase.getNetWeight());
        p.setPhrase(pdBase.getPhrase());
        p.setProcessDept(pdBase.getProcessDept());
        p.setProcessDeptName(pdBase.getProcessDeptName());
        p.setUpdateDate(new Date());
        p.setUpdateName(user.getUserName());
        p.setUpdator(user.getUserID());
        p.setVersionNo(p.getVersionNo() + 1);
        p.setOperation("3");
        pdBaseDao.update(p);
        int hisId = pdBaseHistoryDao.setHistory(p);

        if (pdBase.getUnitId() != null) {
            //更新计量单位
            unitService.selectUnit(pdBase.getUnitId(), 3);
        }
        //1修改未完结的商品
        if ("1".equals(flag)) {
//            String hql="update SlOrdersItem set  salesRelationshipHistory=:salesRelationshipHistory where salesRelationship=:salesRelationship";
//            Map<String,Object> map=new HashMap<String,Object>();
//            map.put("salesRelationshipHistory",hisId);
//            map.put("salesRelationship",p.getId());
//            productHistoryDao.queryHQLWithNamedParams(hql,map);
        }

        return "success";
    }

    @Override
    public String suspensionOfPdBase(Integer pdBaseId, String state, User user) {
        PdBase pdBase = pdBaseDao.get(pdBaseId);

        if ("0".equals(state)) {
            List<PdMerchandise> list = productDao.getListByHQL("from PdMerchandise where product=" + pdBaseId);
            if (list.size() > 0) {
                return "0";
            }
        }
        JSONArray array = JSONArray.parseArray(pdBase.getJson());

        String ids = ConstituteUtil.getIds(array, 1);

        if (StringUtils.isNotEmpty(ids)) {
            //查看零组件

            String sql = "FROM\n" +
                    "PdBase p\n" +
                    "WHERE\n" +
                    "p.id  in ( " + ids + " )";
            Map<String, Object> p = new HashMap<>();
            List<PdBase> list = pdBaseDao.getListByHQLWithNamedParams(sql, p);

            if ("1".equals(state)) {
                for (PdBase pb : list) {
                    pb.setEnabled(state);
                    pb.setUpdateDate(new Date());
                    pb.setUpdator(user.getUserID());
                    pb.setUpdateName(user.getUserName());
                    pdBaseDao.update(pb);
                }
            } else {
                for (PdBase pb : list) {
                    PdAssemble pab = pdAssembleDao.getByPdBaseId(pb.getId());
                    if (pab != null) {
                        if ((pab.getDirectPartProduct() == 1 && pab.getDirectPartComposition() == 0) || (pab.getDirectPartComposition() == 1 && pab.getDirectPartProduct() == 0)) {
                            pb.setEnabled(state);
                            pb.setUpdateDate(new Date());
                            pb.setUpdator(user.getUserID());
                            pb.setUpdateName(user.getUserName());
                            pdBaseDao.update(pb);
                        }
                    }

                }
            }

        }
        pdBase.setEnabled(state);
        pdBase.setUpdateDate(new Date());
        pdBase.setUpdator(user.getUserID());
        pdBase.setUpdateName(user.getUserName());
        pdBase.setOperation("6");
        pdBaseDao.update(pdBase);

        pdBaseHistoryDao.setHistory(pdBase);
        return "success";
    }

    @Override
    public String suspensionOfPdBaseJudge(Integer pdBaseId, String state) {

        if ("0".equals(state)) {
            List<PdMerchandise> list = productDao.getListByHQL("from PdMerchandise where product=" + pdBaseId);
            if (list.size() > 0) {
                return "0";
            }
        }

        return "1";
    }

    @Override
    public List<Map<String, Object>> getProductStopAndStartRecord(Integer pdBaseId) {
        String hql = "select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate,enabled as enabled ) from PdBaseHistory where product=:product and  operation='6' ";
        String hql2 = "select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate,enabled as enabled ) from PdBaseHistory where product=:product and operation='6' ";
        Map<String, Object> param = new HashMap<>();
        param.put("product", pdBaseId);
        List<Map<String, Object>> list = pdBaseHistoryDao.getListByHQLWithNamedParams(hql2, param);
        return list;
    }

    @Override
    public String deletePdBase(Integer pdBaseId) {


        List<PdMerchandise> list = productDao.getListByHQL("from PdMerchandise where product=" + pdBaseId);
        if (list.size() > 0) {
            return "0";
        }
        List<PdAssemble> pdAssembles = pdAssembleDao.getListByHQL("from PdAssemble where product=" + pdBaseId);
        if (pdAssembles.size() > 0) {
            pdAssembleDao.deleteAll(pdAssembles);
        }

        pdBaseDao.deleteById(pdBaseId);


        return "success";
    }

    @Override
    public String deletePdBaseJudge(Integer pdBaseId) {
        List<PdMerchandise> list = productDao.getListByHQL("from PdMerchandise where product=" + pdBaseId);
        if (list.size() > 0) {
            return "0";
        }

        //判断有没有被构成管理
        List<PdMerchandise> list2 = productDao.getListByHQL("from PdComposition where product=" + pdBaseId);
        if (list2.size() > 0) {
            return "0";
        }

        //判断有没有被构成管理
        List<PdMerchandise> list3 = productDao.getListByHQL("from PdComposition where parent=" + pdBaseId);
        if (list3.size() > 0) {
            return "0";
        }

        //判断有没有被构成管理
        List<PdMerchandise> list4 = productDao.getListByHQL("from PdComposition where find_in_set(" + pdBaseId + ", path)>0");
        if (list4.size() > 0) {
            return "0";
        }
        return "1";
    }

    @Override
    public Map<String, Object> suspendPdBaseList(Integer oid, String source, String composition, String param, String type, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "SELECT\n" +
                "pd.id AS id,\n" +
                "pd.inner_sn as innerSn,\n" +
                "pd.name AS name,\n" +
                "pd.model AS model,\n" +
                "pd.specifications AS specifications,\n" +
                "pd.unit AS unit,\n" +
                "pd.source AS source,\n" +
                "pd.composition AS composition,\n" +
                "pd.net_weight AS netWeight,\n" +
                "IF(pd.weight_unit='',null,pd.weight_unit) as weightUnit,\n" +
                "pd.create_name AS createName,\n" +
                "pd.create_date AS createDate,\n" +
                "pd.update_name AS updateName,\n" +
                "pd.update_date AS updateDate\n" +
                "FROM\n" +
                "t_pd_base pd\n" +
                "WHERE\n" +
                "pd.oid =" + oid + " and pd.enabled=0 and pd.type='" + type + "'";

        if (param != null && !"".equals(param)) {
            sql = sql + " and (pd.inner_sn like '%" + param + "%' or pd.name like '%" + param + "%') ";
        }
        if ("2".equals(type)) {
            sql = sql + " and  (select COUNT(0) from t_pd_composition pc where pd.id=pc.product) = 0 ";
        }

        if (composition != null && !"".equals(composition)) {
            sql = sql + " and pd.composition='" + composition + "'";
        }
        if (param != null && !"".equals(param)) {
            sql = sql + " and (pd.inner_sn like '%" + param + "%' or pd.name like '%" + param + "%') ";
        }

        return pdBaseDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    public List<Map<String, Object>> getOrg() {
        String sql = "SELECT o.id,\n" +
                "(SELECT COUNT(id) from t_pd_merchandise p where p.org=o.id and date_format( p.create_date, '%Y-%m-%d' )=DATE_SUB(curdate(),INTERVAL 1 DAY)) sum\n" +
                " from t_sys_org o\n" +
                "where o.pid=0\n" +
                "HAVING sum>0";
        return orgDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> zrtj(Integer org) {
        String sql = "select p.creator userId,p.create_name userName,DATE_FORMAT(p.create_date,'%Y-%m-%d %H:%i:%S') date,COUNT(p.id) sum from t_pd_merchandise p\n" +
                "WHERE p.org=" + org + " and date_format( p.create_date, '%Y-%m-%d' )=DATE_SUB(curdate(),INTERVAL 1 DAY)\n" +
                "GROUP BY p.creator";

        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> getRecordBaseList(Integer pdBaseId) {
        List<Map<String, Object>> list = pdBaseDao.getListByHQLWithNamedParams("select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate) from PdBaseHistory where product=" + pdBaseId + " and (operation='1' or operation='3')", new HashMap<>());
        return list;
    }

    @Override
    public Map<String, Object> getRecordBaseDetails(Integer id, Integer frontId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "SELECT\n" +
                "new Map(id AS id,\n" +
                "innerSn as innerSn,\n" +
                "name AS name,\n" +
                "model AS model,\n" +
                "specifications AS specifications,\n" +
                "unit AS unit,\n" +
                "source AS source,\n" +
                "composition AS composition,\n" +
                "netWeight AS netWeight,\n" +
                "weightUnit AS weightUnit,\n" +
                "createName AS createName,\n" +
                "createDate AS createDate," +
                "processDeptName AS processDeptName,\n" +
                "phrase AS phrase," +
                "memo as memo)\n" +
                "FROM\n" +
                "PdBaseHistory\n" +
                "WHERE\n" +
                "id=:id";
        Map param = new HashMap();
        param.put("id", id);
        Map<String, Object> now = (Map<String, Object>) pdBaseDao.getByHQLWithNamedParams(hql, param);


        Map<String, Object> front = null;
        if (frontId != null && frontId != 0) {
            param.put("id", frontId);
            front = (Map<String, Object>) pdBaseDao.getByHQLWithNamedParams(hql, param);
        }
        map.put("now", now);
        map.put("front", front);
        return map;
    }

    @Override
    public List<Map<String, Object>> getProductAddressList(Integer productId) {
        String hql = "SELECT new Map(id as id,id as customerAddress,contact as contact,mobile as mobile,address as address,postcode as postcode,enabled as enabled, customerContact as customerContact,type as type,regionCode as regionCode,requirements as requirements) from PdCustomerAddress where id in ( select customerAddress from PdCustomerProductAddress where productCustomer = " + productId + " and enabled=1  )";
        return pdCustomerProductAddressDao.getListByHQLWithNamedParams(hql, new HashMap<>());
    }

    @Override
    public List<Map<String, Object>> getProductAddressListHistory(Integer productHistoryId) {
        String hql = "SELECT new Map(id as id,id as customerAddress,contact as contact,mobile as mobile,address as address,postcode as postcode,enabled as enabled, customerContact as customerContact,type as type,regionCode as regionCode,requirements as requirements) from PdCustomerAddressHistory where id in ( select customerAddressHistory from PdCustomerProductAddressHistory where productCustomerHistory = " + productHistoryId + " and enabled=1  )";
        return pdCustomerProductAddressHistoryDao.getListByHQLWithNamedParams(hql, new HashMap<>());
    }

    @Override
    public List<Map<String, Object>> getProductHistoryByCode(Integer oid, String code) {
        String hql = "select new Map( p.id as id,p.outerSn as outerSn,p.outerName as outerName,p.createDate as createDate,p.createName as  createName,(select min(CONCAT(updateDate, ',', updateName)) from PdMerchandiseHistory h where h.productCustomer=p.id and p. outerSn!=:code ) as  updateDate) from PdMerchandise p\n" +
                "where p.org=:org and p.id in (select h.productCustomer from PdMerchandiseHistory h where h.outerSn=:code and h.productCustomer is not null and (h.operation='1' or h.operation='3') ) and p. outerSn!=:code \n";
        Map param = new HashMap();
        param.put("org", oid);
        param.put("code", code);
        return productDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public List<Map<String, Object>> getProductByCode(Integer oid, String code) {

        String hql = "from PdMerchandise where outerSn=:code and org=:org";
        Map param = new HashMap();
        param.put("org", oid);
        param.put("code", code);
        return productDao.getListByHQLWithNamedParams(hql, param);
    }


    @Override
    public String editPdMerchandiseInvoice(PdMerchandiseInvoice merchandiseInvoice) {

        if (1 == merchandiseInvoice.getType()) {
            String hql = "delete from PdMerchandiseInvoice where merchandise=:merchandise and type=1";
            Map<String, Object> map = new HashMap<>();
            map.put("merchandise", merchandiseInvoice.getMerchandise());
            merchandiseInvoiceDao.queryHQLWithNamedParams(hql, map);

            merchandiseInvoiceDao.save(merchandiseInvoice);
            ;
            merchandiseInvoiceHistoryDao.insert(merchandiseInvoice);
        } else {
            String hql = "delete from PdMerchandiseInvoice where merchandise=:merchandise and type=2";
            Map<String, Object> map = new HashMap<>();
            map.put("merchandise", merchandiseInvoice.getMerchandise());
            merchandiseInvoiceDao.queryHQLWithNamedParams(hql, map);

            merchandiseInvoiceDao.save(merchandiseInvoice);
            ;
            merchandiseInvoiceHistoryDao.insert(merchandiseInvoice);
        }

        return "success";
    }

    @Override
    public PdMerchandiseInvoice getPdMerchandiseInvoice(Integer merchandise, Integer type) {
        String hql = " from PdMerchandiseInvoice where merchandise=:merchandise and type=:type ";
        Map<String, Object> map = new HashMap<>();
        map.put("merchandise", merchandise);
        map.put("type", type);
        List<PdMerchandiseInvoice> list = merchandiseInvoiceDao.getListByHQLWithNamedParams(hql, map);
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<PdMerchandiseInvoiceHistory> getPdMerchandiseInvoiceRecordList(Integer merchandise, Integer type) {
        String hql = "from PdMerchandiseInvoiceHistory where merchandise=" + merchandise + "  and type=" + type;
        Map<String, Object> map = new HashMap<>();
        map.put("merchandise", merchandise);
        map.put("type", type);
        List<PdMerchandiseInvoiceHistory> list = merchandiseInvoiceHistoryDao.getListByHQLWithNamedParams(hql, new HashMap<>());
        return list;
    }

    @Override
    public PdMerchandiseInvoiceHistory getPdMerchandiseInvoiceRecordDetails(Integer id) {
        return merchandiseInvoiceHistoryDao.get(id);
    }

    @Override
    public String updatePdMerchandisePrice(PdMerchandise product) {
        productHistoryDao.insert(product);


        productDao.update(product);

        return "success";
    }

    @Override
    public String updatePdMerchandisePrice(PdMerchandise product, PdMerchandiseHistory history) {
        productHistoryDao.save(history);

        productDao.update(product);

        return "success";
    }

    @Override
    public List<PdMerchandiseHistory> getPdMerchandisePriceRecordList(Integer merchandise, String operation) {
        String hql = "from PdMerchandiseHistory where productCustomer=" + merchandise + " and (operation='1' or operation=:operation)";
        Map<String, Object> param = new HashMap<>();
        param.put("operation", operation);
        List<PdMerchandiseHistory> list = productDao.getListByHQLWithNamedParams(hql, param);
        return list;
    }

    @Override
    public PdMerchandiseHistory getPdMerchandisePriceRecordDetails(Integer id) {
        return productHistoryDao.get(id);
    }

    @Override
    public PdMerchandiseHistory pdMerchandiseHistory(Integer id, String operation, String state) {

        String sql = "from PdMerchandiseHistory where productCustomer=:productCustomer and operation=:operation and enabled=:state order by id desc";
        Map<String, Object> map = new HashMap<>();
        map.put("productCustomer", id);
        map.put("operation", operation);
        map.put("state", state);
        List<PdMerchandiseHistory> list = productDao.getListByHQLWithNamedParams(sql, map);

        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public String removePdBaseImage(Integer id, Integer category, User user) {
        PdImage image = pdImageDao.getByProductAndCategory(id, category);
        if (image == null) {
            return "图片不存在";
        }

        //添加文件库
        PdBaseImageUsing pu = new PdBaseImageUsing(image.getId(), PdImage.class);
        uploadService.delFileUsing(pu, image.getUplaodPath(), user);


        image.setOperation("2");
        pdImageHistoryDao.insert(image);
        pdImageDao.delete(image);
        return "success";
    }

    @Override
    public String saveOrUpdatePdBaseImage(PdImage pdImage, User user) {
        PdImage image = pdImageDao.getByProductAndCategory(pdImage.getProduct(), pdImage.getCategory());
        if (image == null) {
            pdImage.setOperation("1");
            pdImage.setCreateDate(new Date());
            pdImage.setCreateName(user.getUserName());
            pdImage.setCreator(user.getUserID());
            pdImage.setUpdateDate(new Date());
            pdImage.setUpdateName(user.getUserName());
            pdImage.setUpdator(user.getUserID());
            pdImage.setOrg(user.getOid());
            pdImageDao.save(pdImage);
            PdImageHistory ih = pdImageHistoryDao.insert(pdImage);

            //添加文件库
            PdBaseImageUsing pu = new PdBaseImageUsing(pdImage.getId(), PdImage.class);
            uploadService.addFileUsing(pu, pdImage.getUplaodPath(), pdImage.getTitle(), user, "产品档案");
            PdBaseImageUsing puh = new PdBaseImageUsing(ih.getId(), PdImageHistory.class);
            uploadService.addFileUsing(puh, ih.getUplaodPath(), ih.getTitle(), user, "产品档案");
        } else {
            //添加文件库
            PdBaseImageUsing cbu = new PdBaseImageUsing(image.getId(), PdImage.class);
            uploadService.delFileUsing(cbu, image.getUplaodPath(), user);


            image.setOperation("3");
            image.setUpdateDate(new Date());
            image.setUpdateName(user.getUserName());
            image.setUpdator(user.getUserID());
            image.setTitle(pdImage.getTitle());
            image.setType(pdImage.getType());
            image.setCategory(pdImage.getCategory());
            image.setUplaodPath(pdImage.getUplaodPath());
            pdImageDao.update(image);

            PdImageHistory ih = pdImageHistoryDao.insert(image);

            //添加文件库
            PdBaseImageUsing pu = new PdBaseImageUsing(image.getId(), PdImage.class);
            uploadService.addFileUsing(pu, image.getUplaodPath(), pdImage.getTitle(), user, "产品档案");
            PdBaseImageUsing puh = new PdBaseImageUsing(ih.getId(), PdImageHistory.class);
            uploadService.addFileUsing(puh, ih.getUplaodPath(), ih.getTitle(), user, "产品档案");
        }


        return "success";
    }

    @Override
    public List<PdImageHistory> getPdBaseImageRecord(Integer product, Integer category) {
        String hql = "from PdImageHistory where product=:product and category=:category";
        Map<String, Object> map = new HashMap<>();
        map.put("product", product);
        map.put("category", category);
        List<PdImageHistory> list = productDao.getListByHQLWithNamedParams(hql, map);
        return list;
    }

    @Override
    public List<PdResourceHistory> getPdResourceRecord(Integer product) {
        String hql = "from PdResourceHistory where product=:product";
        Map<String, Object> map = new HashMap<>();
        map.put("product", product);
        List<PdResourceHistory> list = productDao.getListByHQLWithNamedParams(hql, map);
        return list;
    }

    @Override
    public String saveOrUpdatePdResource(PdResource pdResource, User user) {

        PdResource r = new PdResource();
        r.setCreateDate(new Date());
        r.setCreator(user.getUserID());
        r.setCreateName(user.getUserName());
        r.setOperation("1");
        r.setProduct(pdResource.getProduct());
        r.setResource(pdResource.getResource());
        r.setType(pdResource.getType());
        r.setTitle(pdResource.getTitle());
        r.setUpdateDate(new Date());
        r.setUpdateName(user.getUserName());
        r.setUpdator(user.getUserID());
        pdResourceDao.save(r);

        pdResourceHistoryDao.insert(r);
        return "success";
    }

    @Override
    public String securePdResource(Integer product, Integer resourceId, User user) {

        String hql = "from PdResource where product=:product and resource=:resource";
        Map<String, Object> map = new HashMap<>();
        map.put("product", product);
        map.put("resource", resourceId);
        List<PdResource> resources = pdResourceDao.getListByHQLWithNamedParams(hql, map);
        if (resources.size() == 0) {
            return "关联关系不存在，或已解除关联";
        }

        for (PdResource resource : resources
        ) {
            resource.setOperation("2");
            resource.setUpdateDate(new Date());
            resource.setUpdateName(user.getUserName());
            resource.setUpdator(user.getUserID());
            pdResourceHistoryDao.insert(resource);
            pdResourceDao.delete(resource);
        }


        return "success";
    }


    private void addResourceList(String resourceList, Integer productId, User user) {
        List<PdResource> pdResources = JSONObject.parseArray(resourceList, PdResource.class);
        for (PdResource r : pdResources) {
            r.setCreateDate(new Date());
            r.setCreator(user.getUserID());
            r.setCreateName(user.getUserName());
            r.setOperation("1");
            r.setProduct(productId);
            r.setUpdateDate(new Date());
            r.setUpdateName(user.getUserName());
            r.setUpdator(user.getUserID());
            pdResourceDao.save(r);

            pdResourceHistoryDao.insert(r);
        }


    }

    public List<Map<String, Object>> getProductAddressHistoryList(Integer productHisId) {
        String hql = "SELECT new Map(id as id,id as customerAddress,contact as contact,mobile as mobile,address as address,postcode as postcode,enabled as enabled, customerContact as customerContact,type as type,regionCode as regionCode,requirements as requirements) from PdCustomerAddressHistory where id in ( select customerAddressHistory from PdCustomerProductAddressHistory where productCustomerHistory = " + productHisId + " and enabled=1  )";
        return pdCustomerProductAddressDao.getListByHQLWithNamedParams(hql, new HashMap<>());
    }
}
