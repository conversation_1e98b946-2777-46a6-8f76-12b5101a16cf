package cn.sphd.miners.modules.purchaseInvoice.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.purchaseInvoice.dao.PoPaymentInvoiceDao;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoice;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * lyx on 2021/6/16
 */
@Repository
public class PoPaymentInvoiceDaoImpl extends BaseDao<PoPaymentInvoice, Serializable> implements PoPaymentInvoiceDao {
}
