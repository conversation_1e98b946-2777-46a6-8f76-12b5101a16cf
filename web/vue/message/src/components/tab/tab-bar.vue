<template>
  <div :class="tabAct(bool)">
    <div>
      {{ label }}
      <span class="tabCount" v-show="msgCount !== 0">{{ msgCount }}</span>
    </div>
  </div>
</template>

<style lang="less">
  .tabCount{
    position: absolute;
    top: 5px;
    left: calc(50% + 25px);
    display: inline-block;
    background-color: #fff;
    color: #36c6d3;
    border-radius: 10px;
    padding: 0 6px;
    text-align: center;
    line-height: 18px;
    margin-left: 2px;
  }
</style>

<script>
export default {
  name: 'tab-bar',
  props: {
    label: {
      type: String,
      default: ''
    },
    msgCount: {
      type: Number,
      default: 0
    },
    bool: {
      type: Boolean,
      default: false
    }
  },
  data: function () {
    return {
    }
  },
  methods: {
    tabAct: function (bool) {
      if (bool) {
        return 'tabs-tab tabs-tab-active'
      } else {
        return 'tabs-tab'
      }
    }
  },
  watch: {
  }
}
</script>
