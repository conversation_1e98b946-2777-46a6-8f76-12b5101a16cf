<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showBack=true :showClose=true
                  @mycloseFun="handleCustomClose">
      </PageHeader>
    </div>
    <div class="sticky">
      <div class="padding-10-15 ty-fff">
        <p>{{ new
        Date(list.beginTime).format('yyyy-MM-dd') }}至{{ new
        Date(list.endTime).format('yyyy-MM-dd') }}期间</p>
        <p>开具发票{{list.totalNum}}张，金额{{list.totalAmount}}元</p>
      </div>
    </div>
    <div class="re">
      <router-view/>
    </div>
  </div>
</template>

<script>
import {getInvoiceStatistics} from '@/api/financeBill'

export default {
  name: "annualList",
  data() {
    return {
      tit: "财会",
      invoiceList: {},
      beginTime: '',
      list: {},
    }
  },
  mounted() {
    let invoiceList = localStorage.getItem('invoice');
    this.invoiceList = JSON.parse(invoiceList);
    this.getList(this.invoiceList)

  },
  methods: {
    // handleCustomBack() {
    //   this.$router.push("/invoiceHome");
    // },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getList(invoiceList) {
      if (invoiceList.index == 0) {
        this.beginTime = new Date(new Date().getFullYear(), 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 1) {
        this.beginTime = new Date(new Date().getFullYear() - 1, 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 2) {
        this.beginTime = new Date(new Date().getFullYear() - 2, 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 3) {
        this.beginTime = new Date(this.invoiceList.result, 0, 1).format('yyyy-MM-dd');
      }

      const params = {beginDate: this.beginTime, typeMonth: invoiceList.typeMonth, dateType: invoiceList.dateType}
      getInvoiceStatistics(params).then(res1 => {
        this.list = res1.data.data
        let beginDate = new Date(this.list.beginTime).format('yyyy-MM-dd')
        const typeMonth = 1
        const dateType = 1
        let invoiceTypeList = {beginDate, typeMonth, dateType}
        console.log(invoiceTypeList, 'invoiceTypeListttttttttttttt')
        localStorage.setItem('invoiceTypeList', JSON.stringify(invoiceTypeList))
      }).catch(error => {
        console.error('Error:', error);
      });

    }
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.sticky {
  line-height: 22px;

  p {
    font-size: 0.9em;
  }
}

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.re {
  position: relative;
  top: 40px;
}
</style>