package cn.sphd.miners.modules.accountant.entity;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2018-09-21 10:09
 * @description
 * 会计建账时如果财务对公户余额和关联的科目余额不同的话
 * 需要显示余额对比结果
 * 该类用于存储需要对比的字段
 **/
public class BalanceComparison {
    String bankName;//财务对公户的开户行
    String accountNO;//财务对公户的账户
    BigDecimal financeBalance;//对公户的余额
    BigDecimal accountantBalance;//关联科目的期末余额

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAccountNO() {
        return accountNO;
    }

    public void setAccountNO(String accountNO) {
        this.accountNO = accountNO;
    }

    public BigDecimal getFinanceBalance() {
        return financeBalance;
    }

    public void setFinanceBalance(BigDecimal financeBalance) {
        this.financeBalance = financeBalance;
    }

    public BigDecimal getAccountantBalance() {
        return accountantBalance;
    }

    public void setAccountantBalance(BigDecimal accountantBalance) {
        this.accountantBalance = accountantBalance;
    }
}
