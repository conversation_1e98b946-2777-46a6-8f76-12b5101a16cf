package cn.sphd.miners.modules.commodity.entity;

import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author：孙文
 * @Package：cn.sphd.miners.modules.commodity.entity
 * @Project：trunk
 * @name：PdMerchandiseVo
 * @Date：2022/12/16 9:51
 * @Filename：PdMerchandiseVo
 */
public class PdMerchandiseVo {

    private Integer id;

    private Integer org;   //机构

    private Integer peroid;   //机构

    private String innerSn;   //内部图号

    private String outerSn;   //商品图号

    private String outerName;   //商品名称

    private String customerName;   //客户名称

    private BigDecimal minimumStock;   //最低库存

    private String miniPackingMode;   //小包装方式

    private String miniPackingMaterial;   //小包装材料

    private Long miniPackingAmount;   //小包装数量

    private String outerPackingMode;    //外包装方式

    private String outerPackingMaterial;   //外包装材料

    private Long outerPackingAmount;   //外包装数量

    private BigDecimal unitPrice;   //含税单价(增专)

    private BigDecimal taxRate;  //税率

    private BigDecimal unitPriceNotax;   //不含税单价(增专)

    private BigDecimal unitPriceInvoice;//开票单价(增通)

    private BigDecimal unitPriceNoinvoice;//不开票单价

    private BigDecimal unitPriceReference;//参考单价

    private String contractSn;    //供货合同编号

    private String operation;  //操作:操作:1-增,2-删,3-修改,4-修改初始库存,5-修改最小库存,6-关联,7-暂停销售/恢复销售,8-修改关联商品,9-修改图片,A-修改视频

    private String technicalPrincipal;

    private String technicalPrincipalName;

    private Integer processDept;

    private String processDeptName;

    private String phrase;

    private String isContract;

    private Date expirationDate;

    private String effectiveOption;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;

    private Date signatureDate;

    private String isContain;

    private String hasInvoice;

    private String invoiceCategory;

    private String isFreight;

    private String memo;    //备注

    private Integer creator;

    private String createName;

    private Date createDate;

    private Integer updator;

    private String updateName;

    private Date updateDate;
    //是否在售
    private String enabled;
    //是否销售过
    private String isSaled;

    private Integer product;

    private Integer product_;


    private Integer customer_;
    private String type; //类型:1-通用,2-专属

    private String model;//型号

    private String specifications;  //规格

    private String unit; //单位

    private Integer correlater;

    private String correlaterName;

    private Date correlateDate; //关联时间

    private Integer previousId;

    private Integer versionNo;

    private String priceDesc; //价格说明

    private String category;

    private BigDecimal netWeight; //产品重量

    private BigDecimal currentStock;        //bigint comment '当前库存',

    private BigDecimal availableStock;      //bigint comment '可用库存',

    private BigDecimal initialStock;      //bigint comment '初始库存',

    private double pack;

    private String productName;


    private String cInnerSn;//内部图号(在此表中暂时使用上面的innerSn)


    private String cInnerSnName;//内部名称


    private String position;


    private Integer stockEdited;

    //1.119新增字段 unit_id

    private Integer unitId;
    //合同ids

    private Integer contractId;
    //订单数量

    private Integer orderNum;
    //合同编号

    private String contractNumber;
    //文件列表

    private List fileList;
    //启停记录

    private List startAndStopList;

    //地址列表

    private List<Map<String,Object>> addressList;

    //添加权限  1商品 2产品
    private String addType;


    private String addressListString;


    private String ztName;

    private Date ztDate;

    private String hfName;

    private Date hfDate;

    private PdMerchandiseInvoice nameInvoice;

    private PdMerchandiseInvoice glInvoice;

    private Integer exclusiveTime;      //成为专属时间:1-今年,2-去年,3-更久之前

    private String exclusiveMonth;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getPeroid() {
        return peroid;
    }

    public void setPeroid(Integer peroid) {
        this.peroid = peroid;
    }

    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn(String innerSn) {
        this.innerSn = innerSn;
    }

    public String getOuterSn() {
        return outerSn;
    }

    public void setOuterSn(String outerSn) {
        this.outerSn = outerSn;
    }

    public String getOuterName() {
        return outerName;
    }

    public void setOuterName(String outerName) {
        this.outerName = outerName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public String getMiniPackingMode() {
        return miniPackingMode;
    }

    public void setMiniPackingMode(String miniPackingMode) {
        this.miniPackingMode = miniPackingMode;
    }

    public String getMiniPackingMaterial() {
        return miniPackingMaterial;
    }

    public void setMiniPackingMaterial(String miniPackingMaterial) {
        this.miniPackingMaterial = miniPackingMaterial;
    }

    public Long getMiniPackingAmount() {
        return miniPackingAmount;
    }

    public void setMiniPackingAmount(Long miniPackingAmount) {
        this.miniPackingAmount = miniPackingAmount;
    }

    public String getOuterPackingMode() {
        return outerPackingMode;
    }

    public void setOuterPackingMode(String outerPackingMode) {
        this.outerPackingMode = outerPackingMode;
    }

    public String getOuterPackingMaterial() {
        return outerPackingMaterial;
    }

    public void setOuterPackingMaterial(String outerPackingMaterial) {
        this.outerPackingMaterial = outerPackingMaterial;
    }

    public Long getOuterPackingAmount() {
        return outerPackingAmount;
    }

    public void setOuterPackingAmount(Long outerPackingAmount) {
        this.outerPackingAmount = outerPackingAmount;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getUnitPriceNotax() {
        return unitPriceNotax;
    }

    public void setUnitPriceNotax(BigDecimal unitPriceNotax) {
        this.unitPriceNotax = unitPriceNotax;
    }

    public BigDecimal getUnitPriceInvoice() {
        return unitPriceInvoice;
    }

    public void setUnitPriceInvoice(BigDecimal unitPriceInvoice) {
        this.unitPriceInvoice = unitPriceInvoice;
    }

    public BigDecimal getUnitPriceNoinvoice() {
        return unitPriceNoinvoice;
    }

    public void setUnitPriceNoinvoice(BigDecimal unitPriceNoinvoice) {
        this.unitPriceNoinvoice = unitPriceNoinvoice;
    }

    public BigDecimal getUnitPriceReference() {
        return unitPriceReference;
    }

    public void setUnitPriceReference(BigDecimal unitPriceReference) {
        this.unitPriceReference = unitPriceReference;
    }

    public String getContractSn() {
        return contractSn;
    }

    public void setContractSn(String contractSn) {
        this.contractSn = contractSn;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getTechnicalPrincipal() {
        return technicalPrincipal;
    }

    public void setTechnicalPrincipal(String technicalPrincipal) {
        this.technicalPrincipal = technicalPrincipal;
    }

    public String getTechnicalPrincipalName() {
        return technicalPrincipalName;
    }

    public void setTechnicalPrincipalName(String technicalPrincipalName) {
        this.technicalPrincipalName = technicalPrincipalName;
    }

    public Integer getProcessDept() {
        return processDept;
    }

    public void setProcessDept(Integer processDept) {
        this.processDept = processDept;
    }

    public String getProcessDeptName() {
        return processDeptName;
    }

    public void setProcessDeptName(String processDeptName) {
        this.processDeptName = processDeptName;
    }

    public String getPhrase() {
        return phrase;
    }

    public void setPhrase(String phrase) {
        this.phrase = phrase;
    }

    public String getIsContract() {
        return isContract;
    }

    public void setIsContract(String isContract) {
        this.isContract = isContract;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Date getSignatureDate() {
        return signatureDate;
    }

    public void setSignatureDate(Date signatureDate) {
        this.signatureDate = signatureDate;
    }

    public String getIsContain() {
        return isContain;
    }

    public void setIsContain(String isContain) {
        this.isContain = isContain;
    }

    public String getHasInvoice() {
        return hasInvoice;
    }

    public void setHasInvoice(String hasInvoice) {
        this.hasInvoice = hasInvoice;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public String getIsFreight() {
        return isFreight;
    }

    public void setIsFreight(String isFreight) {
        this.isFreight = isFreight;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getIsSaled() {
        return isSaled;
    }

    public void setIsSaled(String isSaled) {
        this.isSaled = isSaled;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getCorrelater() {
        return correlater;
    }

    public void setCorrelater(Integer correlater) {
        this.correlater = correlater;
    }

    public String getCorrelaterName() {
        return correlaterName;
    }

    public void setCorrelaterName(String correlaterName) {
        this.correlaterName = correlaterName;
    }

    public Date getCorrelateDate() {
        return correlateDate;
    }

    public void setCorrelateDate(Date correlateDate) {
        this.correlateDate = correlateDate;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getPriceDesc() {
        return priceDesc;
    }

    public void setPriceDesc(String priceDesc) {
        this.priceDesc = priceDesc;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(BigDecimal availableStock) {
        this.availableStock = availableStock;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    public double getPack() {
        return pack;
    }

    public void setPack(double pack) {
        this.pack = pack;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getcInnerSn() {
        return cInnerSn;
    }

    public void setcInnerSn(String cInnerSn) {
        this.cInnerSn = cInnerSn;
    }

    public String getcInnerSnName() {
        return cInnerSnName;
    }

    public void setcInnerSnName(String cInnerSnName) {
        this.cInnerSnName = cInnerSnName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Integer getStockEdited() {
        return stockEdited;
    }

    public void setStockEdited(Integer stockEdited) {
        this.stockEdited = stockEdited;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getContractId() {
        return contractId;
    }

    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public List getFileList() {
        return fileList;
    }

    public void setFileList(List fileList) {
        this.fileList = fileList;
    }

    public List getStartAndStopList() {
        return startAndStopList;
    }

    public void setStartAndStopList(List startAndStopList) {
        this.startAndStopList = startAndStopList;
    }

    public List<Map<String, Object>> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<Map<String, Object>> addressList) {
        this.addressList = addressList;
    }

    public String getAddressListString() {
        return addressListString;
    }

    public void setAddressListString(String addressListString) {
        this.addressListString = addressListString;
    }

    public Integer getProduct_() {
        return product_;
    }

    public void setProduct_(Integer product_) {
        this.product_ = product_;
    }

    public Integer getCustomer_() {
        return customer_;
    }

    public void setCustomer_(Integer customer_) {
        this.customer_ = customer_;
    }

    public String getAddType() {
        return addType;
    }

    public void setAddType(String addType) {
        this.addType = addType;
    }

    public PdMerchandiseInvoice getNameInvoice() {
        return nameInvoice;
    }

    public void setNameInvoice(PdMerchandiseInvoice nameInvoice) {
        this.nameInvoice = nameInvoice;
    }

    public PdMerchandiseInvoice getGlInvoice() {
        return glInvoice;
    }

    public void setGlInvoice(PdMerchandiseInvoice glInvoice) {
        this.glInvoice = glInvoice;
    }

    public String getZtName() {
        return ztName;
    }

    public void setZtName(String ztName) {
        this.ztName = ztName;
    }

    public Date getZtDate() {
        return ztDate;
    }

    public void setZtDate(Date ztDate) {
        this.ztDate = ztDate;
    }

    public String getHfName() {
        return hfName;
    }

    public void setHfName(String hfName) {
        this.hfName = hfName;
    }

    public Date getHfDate() {
        return hfDate;
    }

    public void setHfDate(Date hfDate) {
        this.hfDate = hfDate;
    }

    public String getEffectiveOption() {
        return effectiveOption;
    }

    public void setEffectiveOption(String effectiveOption) {
        this.effectiveOption = effectiveOption;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Integer getExclusiveTime() {
        return exclusiveTime;
    }

    public void setExclusiveTime(Integer exclusiveTime) {
        this.exclusiveTime = exclusiveTime;
    }

    public String getExclusiveMonth() {
        return
                exclusiveMonth;
    }

    public void setExclusiveMonth(String exclusiveMonth) {
        this.exclusiveMonth = exclusiveMonth;
    }
}
