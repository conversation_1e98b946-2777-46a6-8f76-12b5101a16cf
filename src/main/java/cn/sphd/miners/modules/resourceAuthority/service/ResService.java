package cn.sphd.miners.modules.resourceAuthority.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.resourceAuthority.dto.ChunkResultDto;
import cn.sphd.miners.modules.resourceAuthority.entity.*;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2017/11/15.
 */
public interface ResService extends BadgeNumberCallback {

    //判断文件夹是否存在文件
    List<ResEntity> judgementFileByCategoryId(Integer categoryId);

    QueryData judgementFileByCategoryIdCentre(User user,Integer categoryId);

    //分页获取文件夹下的文件
    QueryData getFile(User user, Integer categoryId, PageInfo pageInfo, String type, String teminateState);

    //分页获取文件夹下的文件
    QueryData getFileByManager(User user, Integer categoryId, PageInfo pageInfo, String type, String teminateState);

    //分页获取发布的文件
    QueryData getPublishFileByApproveStatus(User user, PageInfo pageInfo, String approveStatus);

    //文件发布申请(用于资源中心长连接“文件发布申请”)
    List<ResHistory> getApplyPublichFile(User user);

    //资源中心确定新增文件(用于资源中心的长连接)
    String insertReourceCentreFile(User user, String files, Integer category, String content, Integer changeNum,
                                   String type, Integer auditor, String auditName, String noticeType);

    //确定上传一个文件夹
    JsonResult insertReourceCentreFolder(User user, Integer parent, List<ChunkResultDto> fileResultList, String module);

    //资料管理确定新增文件
    ResEntity insertResManageFile(User user, ResEntity resEntity, String module);

    //获取一个文件的详细信息
    QueryData getFileMessage(Integer id, User user);

    //获取一个文件的基本信息用于长连接
    HashMap oneFileMessage(Integer hisId);

    //换版不用审批的文件
    QueryData affirdUpdateVersion(User user, ResEntity resEntity, String module);

    //换版需要审批的文件（用于资源中心长连接换版文件）
    void affridVersionForApprove(User user, Integer file, String content,String path,
                                 String size, String version, Integer category, String name, String fileSn,
                                 String type, Integer approveId, String approveName, String module);

    //复用文件
    Integer reuseRes(User user, Integer id);

    //处理需要审批的文件(用于资源中心长连接审批人审批文件)
    void handleFileByNeedtoApproval(User user, ResHistory resHistory, ApprovalProcess approvalProcess, String type);

    //申请人终止发布文件(用于资源中心长连接)
    String userStopFileProcess(User user, ResHistory resHistory, Integer processId);

    //分页获取换版管理的所有记录
    QueryData getPageUpdatePublishFile(User user, PageInfo pageInfo, String approveStatus);

    //换版申请中文件（用于资源中心长连接“文件换版申请”）
    List<ResHistory> applyChangeFileVersion(User user);

    //得到文件审批中文件（用于资源中心长连接“文件审批”）
    List<ResHistory> resourceApproveFile(User user, String approveStatus);

    //得到终审的文件（用于资源中心长连接“文件发布/换版审批”）
    List<ResHistory> resourceFinalApproveFile(User user);

    //资源中心新增或换版文件时获得机构不重复的人员（用于资源中心长连接）
    List<UserDto> userList(User user, Integer hisId, Integer borrowId, Integer aboutId, String type);

    //获取换版管理中文件的详情
    QueryData getHistoryPublishFileMessage(Integer hisId, User user);

    //分页获取某文件的换版记录
    QueryData getUpFile(User user, PageInfo pageInfo, Integer fileId);

    //获取某条文件的基本信息（资源）
    QueryData getFileMes(Integer id, User user);

    //获取某条文件的基本信息（资料）
    QueryData getFileMesByReference(Integer id, User user);

    //获取某条文件的（资源）
    QueryData getHisFileMes(Integer id, User user);

    //获取某条文件的（资料）
    QueryData getHisFileMesByReference(Integer id);

    //移动文件(资料)
    Integer removeStatus(Integer id, Integer categoryId);

    //移动文件(资源)
    String removeStatusByCentre(Integer id, Integer categoryId, User user, ResEntity res, Integer newResHisId, String type);

    //移动文件时删除权限（资源）
    void changeAuthByMoveFile(String type, Integer id, Integer newResHisId, Integer categoryId, User user, ResEntity res, List<Integer> userID, String allCategoryName, String operateType);

    //修改文件名字
    Integer updateFileName(String name, User user, Integer id);

    //修改文件编号
    Integer updateFileSn(User user, Integer id, String fileSn);

    //记录浏览和下载次数
    Integer recordViewAndDownload(User user, Integer id, String operation);

    //查看修改和移动记录
    List<FiveRecordEntity> getRecordByOperation(String operation, Integer fileId);

    //查看浏览和下载次数
    List<ResVisit> getRecordByType(String type, Integer fileid);

    //wyu: Begin of Forder Auth
    ResEntity getSingle(Integer fileId);
    List<OrganizationDto> getAllUserIdsByFileId(Integer oid, Integer fileId);
    void updateFileAuth(Integer fileId, ArrayList<Integer> userID, ResEntity res, User user);
    //wyu: End of Forder Auth

    //普通搜索文件的功能。
    QueryData generalSearchFile(User user, PageInfo pageInfo, ResEntity resEntity, String type);

    //高级检索文件功能
    QueryData advancedSearchFile(User user, PageInfo pageInfo, ResEntity resEntity);

    //模糊搜索文件夹
    QueryData searchFolder(User user,PageInfo pageInfo, String name);

    //app获取某个文件的权限
    QueryData getFileUserAuth(Integer fileId);

    //app查看某天的浏览情况
    List<ResVisit> BrowseOrDownloadRecordByDate(Integer oid, String eventType, String type, String date, String startTime, String endTime, PageInfo pageInfo);

    // app查看某段时间的浏览或下载详情
    ResVisit getBrowseOrDownloadNum(Integer oid, String type, Date startTime, Date endTime, Integer fileId, Integer userId);

    //app获取某人的详细浏览下载记录
    List<ResVisit> browseOrDownloadDetailRecordByPerson(Integer userId ,String type, String date, PageInfo pageInfo);

    //app获取某文件的全部人员的浏览或下载次数
    QueryData getbrowseOrDownloadAllNumByfile(Integer file ,String type, String date, PageInfo pageInfo);

    //app获取某人的全部文件的浏览或下载次数
    QueryData getbrowseOrDownloadAllNumByCreator(Integer userId,String type, String date, PageInfo pageInfo);

    //app获取某人某文件的详细记录
    QueryData getDetailRecord(String type, Integer file, Integer userId, String date, PageInfo pageInfo);

    //获取某人某段月内发布的批准或驳回的文件（1.57处理）
    List<ResHistory> publicFileByApproveStatus(HashMap hisMap);

    //获取某月发布文件的次数（1.57处理）
    Integer publishFileNumByUserAndApproveStatus(HashMap hisMap);

    //获取某人某月换版的批准或驳回的文件（1.57处理）
    List<ResHistory> changeFileByApproveStatus(HashMap hisMap);

    //获取某月换版文件的次数（1.57处理）
    Integer changeFileNumByUserAndApproveStatus(HashMap hisMap);

    //获取某人某月审批的文件（1.57处理）
    List<ResHistory> approveFileByUserAndApproveStatus(Integer userID, String approveStatus, String timeBegin, String timeEnd, Integer fromUser);

    //获取某人审批的文件次数（1.57处理）
    Integer approveFileNum(Integer userID, String approveStatus, Integer fromUser, String timeBegin);

    //获取某总务某月终审的文件（1.57处理）
    List<ResHistory> monthFinalApproveFileByUserAndApproveStatus(Integer userID, String approveStatus, String timeBegin, String timeEnd, Integer oid, Integer fromUser);

    //获取某总务终审的文件次数
    Integer finalApproveFileNumByFind(Integer userID, String approveStatus, String toMid, Integer oid, Integer fromUser, String timeBegin);

    //悬浮窗中得到文件发布换版审批的数量（1.57处理）
    Integer finalApproveFileNum(Integer userID, String approveStatus, String toMid, Integer oid, Integer fromUser, String timeBegin);

    //悬浮窗中得到文件审批的数量
    Integer needApproveFileNum(Integer userID, String approveStatus);

    //修改审批流程表中的某条审批流程
    ApprovalProcess updateApprovalProcessForResource(ApprovalProcess approvalProcess);

    //得到一条历史文件实体
    ResHistory gethisSingle(ResHistory resHistory);

    //修改一条历史实体后并返回这个新的实体
    ResHistory updateResHisSingle(ResHistory resHistory);

    //新增文件实体
    ResEntity insertFileSingle(ResEntity r, User user, String module);

    //修改文件实体
    void updateFileSingle(ResEntity r);

    //给文件新增权限
    void insertFileAuthByCategoryAuth(ResEntity r);

    //循环得到某文件夹的全部父级文件夹名字或路径（如“图书/历史图书/中国历史”这种字样或 “1,2“）
    String allParentCategoryName(Integer parent, String categoryName, String path, String type);

    //新增移动的历史
    Integer addHisMoveFile(Integer fileId, Integer category, String categoryName, String version, String categoryPath);

    //新增名字的历史
    Integer addHisUpNameFile(Integer fileId, Integer category, String name, String version);

    //新增编号历史
    Integer addHisUpFileSn(Integer fileId, Integer category, String FileSn, String version);

    //资源中心发送新的消息形式
    UserSuspendMsg sendNewMessageByResource(Integer userID, String type, Integer changeNum, String name, Integer hisId, String date, String bussiness);

    //给资源中心发送新增的文件
    void sendFile(Integer fileId, User user, String type, String operationTag, List<Integer> userIDs, String AuthType, ResEntity res);

    //给资源中心发送新增的文件夹
    void sendCategory(ResCategory category, List<Integer> userIDs, User user, String type, String operationTag);

    /* //给我的消息发送要新增消息
     void sendMes(UserSuspendMsg userSuspendMsg, ApprovalProcess ap, String userId);
 */
    //文件批准或驳回时同时发给多个审批人的消息
    void sendApprovalsFile(Integer hisId, HashMap mapHis, Integer processId);

    //给搜索列表发送跟新动态的长连接
    void sendSearchByFile(ResEntity r, User user, Integer id);

    //查询当前机构是否进行了权限设置
    Integer checkResAuth(Integer oid);

    //获取某文件及其历史文件的借阅情况 type=1是查看是否有借阅type=2是对借阅文件进行处理
    Integer handleOrGetBorrowedFile(ResEntity resEntity, String type, User user);

    //获取某历史文件的借阅情况 type=1是查看是否有借阅type=2是对借阅文件进行处理
    Integer handleOrGetBorrowedFileHistory(Integer fileHisId, String type, User user);

    //查看某移动的文件下借阅的人的详细记录
    HashMap<String, Object> getReadroomUserRecordByFileid(User user, Integer fileId, Integer fileHisId);

    //还原垃圾文件
    Integer restoreFile(User user, Integer fileId);

    //删除垃圾文件
    Integer updateFileValid(User user, Integer fileId);

    //获取即将消失的文件
    List<ResEntity> getAboutToDisapperFile(User user);

    //查看即将消失的文件的详情
    HashMap<String, Object> getAboutToDisapperFileMessage(Integer fileId);

    //还原即将消失的文件
    Integer restoreDelFile(User user, Integer fileId);

    //获取有效和垃圾文件大小
    String getFileSize(User user, String categoryPath, String type);

    //禁用历史文件版本
    Integer disablefileHistoryVersion(User user, Integer fileHisId);

    //定时每天00:16去清理要即将删除的文件
    void regularlyCleanedDisapperFile();

    //获取某文件最新历史记录的id
    Integer getNewFileHisId(Integer id);

    //申请废止或换版文件要先锁定文件
    Integer updateResOperation(User user, Integer id);

    //废止或换版失败后手工解锁文件
    Integer unlockFile(User user, Integer id);

    //修改文件锁id
    void updateResUuid(Integer fileId);

    //获取某批次上传的没有审批的文件个数
    Long getNoApproveFileNum(String batchUuid, String approveStatus);

    //获取某批次上传的具体文件
    List<ResHistory> listHisFileByBatchUuid(String batchUuid);

    //获取禁用和启用的记录
    List<ResHistory> listHisFileByTemiante(Integer fileId);

}
