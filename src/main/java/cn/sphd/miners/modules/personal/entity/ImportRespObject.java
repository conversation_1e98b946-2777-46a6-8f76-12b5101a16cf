package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.system.entity.User;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ImportRespObject
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 9:46
 * @Version 1.0
 */
public class ImportRespObject {
    private Integer status;//0失败，1成功
    private List<User> trueUserList;//成功导入员工列表
    private List<User> falseUserList;//无法导入员工列表
    private Integer importSum;//导入总数
    private Integer falseImportSum;//无法导入总数
    private List<MtBase> trueMtBaseList;//成功导入物料列表
    private List<MtBase> falseMtBaseList;//无法导入物料列表
    public List<MtBase> getFalseMtBaseList() {
        return falseMtBaseList;
    }

    public void setFalseMtBaseList(List<MtBase> falseMtBaseList) {
        this.falseMtBaseList = falseMtBaseList;
    }

    public List<MtBase> getTrueMtBaseList() {
        return trueMtBaseList;
    }

    public void setTrueMtBaseList(List<MtBase> trueMtBaseList) {
        this.trueMtBaseList = trueMtBaseList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<User> getTrueUserList() {
        return trueUserList;
    }

    public void setTrueUserList(List<User> trueUserList) {
        this.trueUserList = trueUserList;
    }

    public List<User> getFalseUserList() {
        return falseUserList;
    }

    public void setFalseUserList(List<User> falseUserList) {
        this.falseUserList = falseUserList;
    }

    public Integer getImportSum() {
        return importSum;
    }

    public void setImportSum(Integer importSum) {
        this.importSum = importSum;
    }

    public Integer getFalseImportSum() {
        return falseImportSum;
    }

    public void setFalseImportSum(Integer falseImportSum) {
        this.falseImportSum = falseImportSum;
    }
}
