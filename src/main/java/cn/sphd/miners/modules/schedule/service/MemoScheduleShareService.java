package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleShare;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleShareRecipients;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * Created by Administrator on 2020/7/31.
 */
public interface MemoScheduleShareService {

    List<Integer> getRecipientIdsByScheduleId(Integer scheduleId);

    void  addMemoScheduleShareByMemoSchedule(MemoSchedule memoSchedule, User user,Integer ... shareableUserIds);

    List<MemoScheduleShare> getMemoScheduleSharesByScheduleId(Integer scheduleId, PageInfo pageInfo);

    List<MemoScheduleShare> getMemoScheduleSharesByCreator(Integer creator,PageInfo pageInfo);

    List<MemoScheduleShare> getMemoScheduleSharesByRecipient(Integer recipient,PageInfo pageInfo);

    MemoScheduleShare getMemoScheduleShareById(Integer id);

    void deleteAllMemoScheduleShare(List<MemoScheduleShare> memoScheduleShareList);
}
