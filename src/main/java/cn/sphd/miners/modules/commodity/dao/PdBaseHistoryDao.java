package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdBaseHistory;
import com.alibaba.fastjson.JSONArray;

import java.io.Serializable;

public interface PdBaseHistoryDao extends IBaseDao<PdBaseHistory,Serializable> {

    int setHistory(PdBase pdBase);

    int setHistory(PdBase pdBase, String path, JSONArray jsonArray);
}
