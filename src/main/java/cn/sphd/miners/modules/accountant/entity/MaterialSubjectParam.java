package cn.sphd.miners.modules.accountant.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2018-04-18 16:49
 * @description
 * 当物料给会计法消息后，需要关联生成以供应商名称和物料名称命名的科目
 * 这个类是用于存放参数用的
 **/
public class MaterialSubjectParam implements Serializable {

    private String supplier;//供应商名称
    private String material;//物料名称
    private String supplierSubject;//供应商科目编号
    private String supplierParentSubject;//供应商的父科目编号
    private String relevanceSupplierType;//供应商关联类型:1-商品，2-物料，3-供应商
    private Integer relevanceSupplierItem;//供应商关联项的id,根据代码去不同的表中查,商品去找商品表找id，物料去找物料表id

    private String relevanceMaterialType;//物料关联类型:1-商品，2-物料，3-供应商
    private Integer relevanceMaterialItem;//物料关联项的id,根据代码去不同的表中查,商品去找商品表找id，物料去找物料表id

    private String measureUnit;//物料单位
    private Integer messageid;//消息id，当保存完科目后需要把对应的消息变成已读

    public Integer getMessageid() {
        return messageid;
    }

    public void setMessageid(Integer messageid) {
        this.messageid = messageid;
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getSupplierSubject() {
        return supplierSubject;
    }

    public void setSupplierSubject(String supplierSubject) {
        this.supplierSubject = supplierSubject;
    }

    public String getSupplierParentSubject() {
        return supplierParentSubject;
    }

    public void setSupplierParentSubject(String supplierParentSubject) {
        this.supplierParentSubject = supplierParentSubject;
    }

    public String getRelevanceSupplierType() {
        return relevanceSupplierType;
    }

    public void setRelevanceSupplierType(String relevanceSupplierType) {
        this.relevanceSupplierType = relevanceSupplierType;
    }

    public Integer getRelevanceSupplierItem() {
        return relevanceSupplierItem;
    }

    public void setRelevanceSupplierItem(Integer relevanceSupplierItem) {
        this.relevanceSupplierItem = relevanceSupplierItem;
    }

    public String getRelevanceMaterialType() {
        return relevanceMaterialType;
    }

    public void setRelevanceMaterialType(String relevanceMaterialType) {
        this.relevanceMaterialType = relevanceMaterialType;
    }

    public Integer getRelevanceMaterialItem() {
        return relevanceMaterialItem;
    }

    public void setRelevanceMaterialItem(Integer relevanceMaterialItem) {
        this.relevanceMaterialItem = relevanceMaterialItem;
    }
}
