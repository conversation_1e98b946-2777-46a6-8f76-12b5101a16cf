package cn.sphd.miners.modules.complaint.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.complaint.dao.ComplaintProductOutputDao;
import cn.sphd.miners.modules.complaint.entity.ComplaintProductOutput;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * Created by 朱思旭 on 2019/5/6.
 */
@Repository
public class ComplaintProductOutputDaoImpl extends BaseDao<ComplaintProductOutput, Serializable> implements ComplaintProductOutputDao {
}
