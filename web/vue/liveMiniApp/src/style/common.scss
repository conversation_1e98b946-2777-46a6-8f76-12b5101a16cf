
$livePageHeadBg : #53b5a8;
$livePageHeadBgLight : rgba(83,181,168, 0.2);
$liveLinkColor : #3f89db;
$liveRed : #f60421;
$liveYellow : #f6ba04;
$liveGray : #cccccc;
$normalFontSize:16px;

body {
  background-color: #f8f8f8;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin:0;
  color: #333;
}
@media only screen and (min-width: 320px){
  html {
    font-size: 100% !important;
  }
}
@media only screen and (min-width: 640px){
  html {
    font-size: 125% !important;
  }
}
@media only screen and (min-width: 750px){
  html {
    font-size: 150% !important;
  }
}
@media only screen and (min-width: 1242px){
  html {
    font-size: 187.5% !important;
  }
}


.live-container{  padding:10px 16px; }
.live-panel{ background:#fff; color:#333; padding:10px 16px; margin-bottom: 16px; }
.live-tr{ line-height: 30px; }
.live-title{ line-height: 40px; }
.live-tr.live-tr-line{ border-bottom: 1px solid #eaeaea;  }
.live-panel.live-pad .live-tr{
  padding:6px 0;
}
.live-panel.live-pad .live-tr:not(:first-child), .live-panel.live-pad .live-tr:not(:last-child){
  padding:9px 0;
}
.live-right{ float: right; }
.live-bottomDiv{ position: fixed; bottom: -21px; width: 100%; background: #fff; left: 0; padding: 5px 20px 10px; box-sizing: border-box; }
.line-link{ color: $liveLinkColor; cursor: pointer  }
.live-red{ color: $liveRed;  }
// 按钮
.live-button{display: block;line-height: 40px;text-align: center;border-radius: 4px;margin-top: 10px;}
.primary{background: $livePageHeadBg; color:#fff;border:1px solid $livePageHeadBg;}
.cancel{color:#666;border:1px solid #999;}
.live-actionSheet{ background: #fff; margin:10px 0 0; border-radius: 4px; text-align: center; line-height: 40px; }
// 敬请期待
.wating{ display: block; height: 200px; width: 200px; margin: 200px auto 40px; background-repeat: no-repeat!important; opacity: 0.4; background-position:center center;   }
.watTxt{ color: #ccc; font-size: 36px; text-align: center;  }


.loadingKlass{ color: #fff!important; }

// ----------------- cells ---------------------
.live-cells{
  .live-cell{
    display: flex;
    align-items: center;
    line-height: 1.5;
    border-bottom: 1px solid #ececec;
    padding: 12px 16px;
    background: #fff;
    .arrow-right{
      margin: 0 8px 0 auto;
      font-weight: bold;
      color: $livePageHeadBg;
    }
    &:last-child{
      border-bottom: none;
    }
    &.gap{
      border: none;
      margin-bottom: 12px;
    }
  }
}




 
