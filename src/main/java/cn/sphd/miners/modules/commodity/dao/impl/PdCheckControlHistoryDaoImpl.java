package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.modules.commodity.dao.PdCheckControlHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdCheckControl;
import cn.sphd.miners.modules.commodity.entity.PdCheckControlHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PdCheckControlHistoryDaoImpl extends BaseDao<PdCheckControlHistory,Serializable> implements PdCheckControlHistoryDao {
    @Override
    public void insertHistory(PdCheckControl pdCheckControl) {
        PdCheckControlHistory history = new PdCheckControlHistory();
        BeanUtils.copyPropertiesIgnoreNull(pdCheckControl, history);
        history.setId(null);
        history.setCheckControl(pdCheckControl.getId());
        this.save(history);
    }
}
