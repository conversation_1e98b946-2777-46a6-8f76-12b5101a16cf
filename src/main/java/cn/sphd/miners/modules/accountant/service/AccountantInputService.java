package cn.sphd.miners.modules.accountant.service;

import cn.sphd.miners.modules.accountant.entity.SubjectChooseParams;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucherSubject;
import cn.sphd.miners.modules.accountant.entity.UpdateVoucherParams;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Created by root on 17-1-4.
 */
public interface AccountantInputService {
    JSONArray getVouchers(int oid);

    JSONObject insertVoucher(SubjectChooseParams params);

    int deleteVoucher(int id);

    JSONObject updateVoucher(SubjectChooseParams params);

    JSONObject judgeMinorSettle(int org, UpdateVoucherParams params);

    JSONObject getSubjects(TAccountantSubjectEntity param);
}
