package cn.sphd.miners.modules.publicInformation.entity;


import cn.sphd.miners.common.persistence.BaseEntity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_pi_category_history")
public class PiCategoryHistory extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "pi_category")
    private Integer piCategory;
    @Column(name = "org")
    private Integer org;
    @Column(name = "org_name")
    private String orgName;
    @Column(name = "parent")
    private Integer parent;
    @Column(name = "type")
    private Byte type;
    @Column(name = "is_system")
    private Byte isSystem;
    @Column(name = "valid")
    private Boolean valid;
    @Column(name = "max_child_categories")
      private Integer maxChildCategories;
    @Column(name = "children")
    private Integer children;
    @Column(name = "descendant")
    private Long descendant;
    @Column(name = "leafs")
    private Long leafs;
    @Column(name = "path")
    private String path;
    @Column(name = "size")
    private long size;
    @Column(name = "name")
    private String name;
    @Column(name = "is_trash")
    private Boolean isTrash;
    @Column(name = "memo")
    private String memo;
    @Column(name = "orders")
    private Integer orders;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_date")
    private Date createDate;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_date")
    private Date updateDate;
    @Column(name = "approve_item")
    private Integer approveItem;
    @Column(name = "approve_status")
    private Byte approveStatus;
    @Column(name = "approve_level")
    private Integer approveLevel;
    @Column(name = "auditor")
    private Integer auditor;
    @Column(name = "auditor_name")
    private String auditorName;
    @Column(name = "audit_date")
    private Date auditDate;
    @Column(name = "operation")
    private Byte operation;
    @Column(name = "apply_memo")
    private String applyMemo;
    @Column(name = "approve_memo")
    private String approveMemo;
    @Column(name = "message_id")
    private Integer messageId;
    @Column(name = "previous_id")
    private Integer previousId;
    @Column(name = "version_no")
    private Integer versionNo;

    public Integer getId() {
      return id;
    }

    public void setId(Integer id) {
      this.id = id;
    }

    public Integer getPiCategory() {
      return piCategory;
    }

    public void setPiCategory(Integer piCategory) {
      this.piCategory = piCategory;
    }

    public Integer getOrg() {
      return org;
    }

    public void setOrg(Integer org) {
      this.org = org;
    }

    public String getOrgName() {
      return orgName;
    }

    public void setOrgName(String orgName) {
      this.orgName = orgName;
    }

    public Integer getParent() {
      return parent;
    }

    public void setParent(Integer parent) {
      this.parent = parent;
    }

    public Byte getType() {
      return type;
    }

    public void setType(Byte type) {
      this.type = type;
    }

    public Byte getIsSystem() {
      return isSystem;
    }

    public void setIsSystem(Byte isSystem) {
      this.isSystem = isSystem;
    }

    public Boolean getValid() {
      return valid;
    }

    public void setValid(Boolean valid) {
      this.valid = valid;
    }

    public Integer getMaxChildCategories() {
      return maxChildCategories;
    }

    public void setMaxChildCategories(Integer maxChildCategories) {
      this.maxChildCategories = maxChildCategories;
    }

    public Integer getChildren() {
      return children;
    }

    public void setChildren(Integer children) {
      this.children = children;
    }

    public Long getDescendant() {
      return descendant;
    }

    public void setDescendant(Long descendant) {
      this.descendant = descendant;
    }

    public Long getLeafs() {
        return leafs;
    }

    public void setLeafs(Long leafs) {
        this.leafs = leafs;
    }

    public String getPath() {
      return path;
    }

    public void setPath(String path) {
      this.path = path;
    }

    public long getSize() {
      return size;
    }

    public void setSize(long size) {
      this.size = size;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public Boolean isTrash() {
      return isTrash;
    }

    public void setTrash(Boolean trash) {
      isTrash = trash;
    }

    public String getMemo() {
      return memo;
    }

    public void setMemo(String memo) {
      this.memo = memo;
    }

    public Integer getOrders() {
      return orders;
    }

    public void setOrders(Integer orders) {
      this.orders = orders;
    }

    public Integer getCreator() {
      return creator;
    }

    public void setCreator(Integer creator) {
      this.creator = creator;
    }

    public String getCreateName() {
      return createName;
    }

    public void setCreateName(String createName) {
      this.createName = createName;
    }

    public Date getCreateDate() {
      return createDate;
    }

    public void setCreateDate(Date createDate) {
      this.createDate = createDate;
    }

    public Integer getUpdator() {
      return updator;
    }

    public void setUpdator(Integer updator) {
      this.updator = updator;
    }

    public String getUpdateName() {
      return updateName;
    }

    public void setUpdateName(String updateName) {
      this.updateName = updateName;
    }

    public Date getUpdateDate() {
      return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
      this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
      return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
      this.approveItem = approveItem;
    }

    public Byte getApproveStatus() {
      return approveStatus;
    }

    public void setApproveStatus(Byte approveStatus) {
      this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
      return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
      this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
      return auditor;
    }

    public void setAuditor(Integer auditor) {
      this.auditor = auditor;
    }

    public String getAuditorName() {
      return auditorName;
    }

    public void setAuditorName(String auditorName) {
      this.auditorName = auditorName;
    }

    public Date getAuditDate() {
      return auditDate;
    }

    public void setAuditDate(Date auditDate) {
      this.auditDate = auditDate;
    }

    public Byte getOperation() {
      return operation;
    }

    public void setOperation(Byte operation) {
      this.operation = operation;
    }

    public String getApplyMemo() {
      return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
      this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
      return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
      this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
      return messageId;
    }

    public void setMessageId(Integer messageId) {
      this.messageId = messageId;
    }

    public Integer getPreviousId() {
      return previousId;
    }

    public void setPreviousId(Integer previousId) {
      this.previousId = previousId;
    }

    public Integer getVersionNo() {
      return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
      this.versionNo = versionNo;
    }
}
