n<template>
  <div id="batchFile">
    <TY_NavTop title="需批量签收的文件"></TY_NavTop>
    <div class="container">
      <div class="place">
        <div class="placeContainer">
          您获得了一些文件的使用权限，请及时查阅并签收！
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          点击“查阅”按钮，即可查阅到这些文件。<br>
          在“文件与资料”中，您还可对这些文件进行更多管理。
          <div class="handle_button">
            <button class="ui-btn ui-btn_info" @click="seeDetail()">查阅</button>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          文件使用权限可能被调整。<br>
          获得其他文件使用权限后，您将收到类似的系统消息。
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          认为应获得更多文件的使用权限，或认为不该获得某些文件的
          使用权限，请向文管提出。
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          公司组织针对文件的学习或考试时，将发送系统消息。
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <el-checkbox v-model="checked"></el-checkbox> 我已知晓上述事项，现予批量签收！
          <div class="handle_button">
            <button class="ui-btn ui-btn_info" :disabled="!checked" @click="toHandle()">确定</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped="">
.place .placeContainer {
  line-height: 1.5;
  padding: 8px;
}
.handle_button .ui-btn {
  padding: 6px 12px;
}
</style>

<script>
import { gohistory } from '@/js/common'
export default {
  name: 'approveFileDetail',
  data () {
    return {
      checked: false
    }
  },
  created: function () {

  },
  methods: {
    toHandle() {
      let batchData = localStorage.getItem('batch')
      batchData = JSON.parse(batchData)
      let that = this
      let id = this.$route.params.id
      let type =  batchData.type
      this.loading = true
      this.axios.post(this.auth.webRoot + '/res/signedResource.do', {
        type: type,
        id: id
      })
        .then(function (response) {
          that.loading = false
          console.log(response)
          let data = response.data.data
          let state = data.state
          if (state === 1) {
            that.$kiko_message('操作成功')
            gohistory(that)
          } else {
            that.$kiko_message('操作失败')
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    seeDetail: function () {
      this.$router.push({
        path: `/batchFileDetail`
      })
    }
  }
}
</script>
