
import {request} from "@/utils/request";

//材料管理模块
/*
*   creator: sy 2024-09-05  获取已设置列表
 */
export function getsetlink(data){
    data = data || {};
    return request({
        url:"/mtPacking/getPackagingList.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-06  获取未设置材料数量
 */
export function getunset(data){
    data = data || {};
    return request({
        url:"/mtPacking/getWszNum.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-06  获取未设置材料列表数据
 */
export function getunsetlink(data){
    data = data || {};
    return request({
        url:"/mtPacking/getWszPackagingList.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-09  获取供应商列表
 */
export function getsuplink(data){
    data = data || {};
    return request({
        url:"/mtPacking/getSupplierByMtBase.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-10  新增包装物
 */
export function addnewpiket(data){
    data = data || {};
    return request({
        url:"/mtPacking/addBzw.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-11  获取包装物
 */
export function getpiclist(data){
    data = data || {};
    return request({
        url: "/mtPacking/getBzwList.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-14  包装物信息查看
 */
export function seapackinfon(data){
    data = data || {};
    return request({
        url:"/mtPacking/getPackagingDetail.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-18  新增包装方式
 */
export function addPacking(data){
    data = data || {};
    return request({
        url:"/mtPacking/addPackaging.do",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2025-03-21  修改包装方式
 */
export function updatePacking(data){
    data = data || {};
    return request({
        url:"/mtPacking/updatePackaging.do",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2024-09-23  暂停或启用包装
 */
export function stuppact(data){
    data = data || {};
    return request({
        url:"/mtPacking/stopOrStartPackaging.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-24  获取操作记录
 */
export function getcatlink(data){
    data = data || {};
    return request({
        url: "/mtPacking/getPackagingRecordList.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-25  确定设置
 */
export function madesett(data){
    data = data || {};
    return request({
        url:"/mtPacking/submitSettings.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-09-30  获取操作记录详情
 */
export function getlistisg(data){
    data = data || {};
    return request({
        url:"/mtPacking/getPackagingRecordDetail.do",
        method: 'post',
        data: data
    })
}