package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.finance.entity.FinanceStakeholder;
import cn.sphd.miners.modules.finance.entity.FinanceStakeholderHistory;
import cn.sphd.miners.modules.finance.service.StakeholderService;
import cn.sphd.miners.modules.sales.service.PoService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/11/19
 * @Description 1.179财务优化2101--收款单位部分
 **/
@Controller
@RequestMapping("/stakeholder")
public class StakeholderController {

    @Autowired
    StakeholderService stakeholderService;
    @Autowired
    UserService userService;
    @Autowired
    PoService poService;

    /**
     * <AUTHOR>
     * @Description  查询收款单位列表
     * @Date 2021/11/19
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getStakeholderList.do")
    public JsonResult getStakeholderList(User user){
        Map<String,Object> map = new HashMap<>();
        List<FinanceStakeholder> stakeholdersAvailable = stakeholderService.getStakeholderList(user.getOid(),1,null);
        List<FinanceStakeholder> stakeholdersStop = stakeholderService.getStakeholderList(user.getOid(),0,null);
        map.put("stakeholdersAvailable",stakeholdersAvailable);
        map.put("stakeholdersStop",stakeholdersStop);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 新增收款单位
     * @Date 2021/11/19
     * @param  name名称  remark：说明
     * @return
     **/
    @ResponseBody
    @RequestMapping("/addStakeholder.do")
    public JsonResult addStakeholder(User user,String name,String remark){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(name)){
            List<User> users = userService.getUserListByOidRoleCode(user.getOid(),"staff",name,null);  //查询机构中的职工【页面中的获取公司职工的也是这个接口】
            List<FinanceStakeholder> stakeholders = stakeholderService.getStakeholderList(user.getOid(),null,name);  //查询机构中的收款单位
            Integer suppliersExit = 0;  //供应商是否有同名的
            Map map1 = poService.getSuppliers(user.getOid(),null,null,3,null,user);
            List<Map<String,Object>> listMap = (List<Map<String, Object>>) map1.get("data");
            if (listMap!=null) {
                for (int i = 0; i < listMap.size(); i++) {
                    String fullName = (String) listMap.get(i).get("full_name");
                    if (name.equals(fullName)) {
                        suppliersExit = 1;
                    }
                }
            }
            if (users.size()>0||stakeholders.size()>0||suppliersExit==1){
                map.put("status",2);
                map.put("content","系统里已有该收款单位！");
            }else {
                FinanceStakeholder financeStakeholder = stakeholderService.addStakeholder(user,name,remark);  //新增数据
                map.put("financeStakeholder",financeStakeholder);  //新增成功的收款单位
                map.put("status",1);  //操作成功
                map.put("content","操作成功");
            }
        }else {
            map.put("status",0);  //操作失败
            map.put("content","操作失败");
        }
        return new JsonResult(1,map);
    }

    /**
     *
     * <AUTHOR>
     * @Description  启/停用收款单位
     * @Date 2021/11/22
     * @param enable 1-启用 0-停用
     * @return
     **/
    @ResponseBody
    @RequestMapping("/deactivate.do")
    public JsonResult deactivate(User user,Integer stakeholderId,Integer enable){
        Map<String,Object> map = new HashMap<>();
        if (stakeholderId!=null&&enable!=null){
            stakeholderService.deactivate(user,stakeholderId,enable);
            map.put("status",1);  //操作失败
            map.put("content","操作成功");
        }else {
            map.put("status",0);  //操作失败
            map.put("content","操作失败");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 修改收款单位
     * @Date 2021/11/22
     * @param type 1-改名称中的别字,2-改新名,3-修改说明
     * @return
     **/
    @ResponseBody
    @RequestMapping("/updateStakeholder.do")
    public JsonResult updateStakeholder(User user,Integer stakeholderId,String name,String remark,Integer type){
        Map<String,Object> map = new HashMap<>();
        if (stakeholderId!=null&&type!=null){
            if (type==1||type==2){
                List<User> users = userService.getUserListByOidRoleCode(user.getOid(),"staff",name,null);  //查询机构中的职工【页面中的获取公司职工的也是这个接口】
                List<FinanceStakeholder> stakeholders = stakeholderService.getStakeholderList(user.getOid(),null,name);  //查询机构中的收款单位
                Integer suppliersExit = 0;  //供应商是否有同名的
                Map map1 = poService.getSuppliers(user.getOid(),null,null,3,null,user);
                List<Map<String,Object>> listMap = (List<Map<String, Object>>) map1.get("data");
                if (listMap!=null) {
                    for (int i = 0; i < listMap.size(); i++) {
                        String fullName = (String) listMap.get(i).get("full_name");
                        if (name.equals(fullName)) {
                            suppliersExit = 1;
                        }
                    }
                }
                if (users.size()>0||stakeholders.size()>0||suppliersExit==1){
                    map.put("status",2);
                    map.put("content","系统里已有该收款单位！");
                }else {
                    stakeholderService.updateStakeholder(user, stakeholderId, name, remark, type);
                    map.put("status", 1);  //操作成功
                    map.put("content", "操作成功");
                }
            }else {
                stakeholderService.updateStakeholder(user, stakeholderId, name, remark, type);
                map.put("status", 1);  //操作成功
                map.put("content", "操作成功");
            }
        }else {
            map.put("status",0);  //操作失败
            map.put("content","操作失败");
        }
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 查询修改记录
     * @Date 2021/11/22
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getRecordList.do")
    public JsonResult getRecordList(Integer stakeholderId){
        Map<String,Object> map = new HashMap<>();
        List<FinanceStakeholderHistory> financeStakeholderHistories = stakeholderService.getStakeholderHistory(stakeholderId);
        map.put("stakeholderHistories",financeStakeholderHistories);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 获取公司职工
     * @Date 2021/11/29
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getUsers.do")
    public JsonResult getUsers(User user){
        Map<String,Object> map = new HashMap<>();
        List<User> users = userService.getUserListByOidRoleCode(user.getOid(),"staff",null,null);  //查询机构中的职工【页面中的获取公司职工的也是这个接口】
        map.put("users",users);
        return new JsonResult(1,map);
    }

}
