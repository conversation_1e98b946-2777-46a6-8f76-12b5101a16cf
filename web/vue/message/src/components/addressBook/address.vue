<template>
  <div class="role" v-show="show">
    <div class="role_wrapper" id="navigationBar">
      <div class="role_handle">
        <div class="role_back" @click="cancelShow">取消</div>
        <div class="role_title">{{title}}</div>
      </div>
      <div class="role_search_wrapper">
        <img src="../../images/search.png">
        <input type="text" placeholder="请输入姓名" class="role_search" v-model="searchInput">
      </div>
    </div>
    <div class="role_container">
      <div v-for="it in roleData.letterList" :key="it">
        <div class="tip tip-success">{{it}}</div>
        <div class="ui-cell" v-if="item.firstLetter == it" v-for="item in roleData.rolelist" :key="item.userName" v-on:click="selectVal(item)">
          <div class="ui-cell__hd"></div>
          <div class="ui-cell__bd">
            {{item.userName}} {{item.mobile}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .role{
    position: fixed;
    top: 0;
    left: 0;
    z-index:10000;
    width: 100%;
    height: 100%;
    background-color: #fff;
  }
  .role_wrapper {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    display: block;
    height: 96px;
    background-color: #36c6d3;
    z-index: 999;
  }
  .role_handle, .role_search_wrapper{
    height: 48px;
    position: relative;
  }
  .role_handle{
    color: #fff;
    text-align: center;
    line-height:48px;
    font-size: 14px;
  }
  .role_back{
    position: absolute;
    top: 0;
    display: block;
    width: 64px;
    height: 48px;
    cursor: pointer;
  }
  .role_search_wrapper{
    display: block;
    width: 300px;
    height: 36px;
    margin: auto;
    background-color: #2ca0aa;
    border-radius: 5px;
    color: #d1e8ea;
    padding: 0 12px;
  }
  .role_search_wrapper img{
    vertical-align: middle;
  }
  .role_search{
    border: none;
    height: 32px;
    margin-left: 8px;
    background-color: #2ca0aa;
    font-size: 14px;
    width: 260px;
    color: #d1e8ea;
  }
  .role_container{
    width: 100%;
    overflow: auto;
    position: absolute;
    top: 96px;
    z-index: 10;
    height: calc(100% - 96px);
  }
  .role .tip{
    margin: 0;
  }
  .noPerson{
    text-align: center;
    margin-top: 50px;
  }
</style>

<script>
export default {
  name: 'addressBook',
  props: {
    title: {
      type: 'String',
      default: '请选择申请人'
    },
    rolelist: Array,
    show: Boolean
  },
  data () {
    return {
      searchInput: ''
    }
  },
  computed: {
    roleData: function () {
      let fetch = this.rolelist
      console.log('address rolelist', this.rolelist)
      let data = {
        rolelist: [],
        letterList: []
      }
      let word = this.searchInput
      for (let i in fetch) {
        let userName = fetch[i].userName
        // 过滤符合条件的名字
        if (userName.indexOf(word) !== -1) {
          // 获取符合条件名字首字母
          fetch[i].firstLetter = this.pyfl(userName).substring(0, 1)
          console.log('pyfl', fetch[i].firstLetter)
          // 判断字母表中是否含有此字母
          if (data.letterList.indexOf(fetch[i].firstLetter) === -1) {
            data.letterList.push(fetch[i].firstLetter)
          }
          // 将符合条件的数据放入新数组
          data.rolelist.push(fetch[i])
        }
      }
      console.log('address new rolelist', JSON.stringify(data))
      return data
    }
  },
  methods: {
    cancelShow: function () {
      this.$emit('update:show', false)
    },
    selectVal: function (item) {
      this.cancelShow()
      let data = item
      this.$emit('select_role', data)
    }
  }
}
</script>
