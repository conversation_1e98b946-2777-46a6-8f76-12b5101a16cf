<template>
  <div id="questionList" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="试卷"></TY_NavTop>
    <div class="container">
      <div class="top">
        <p><span class="ttl">剩余答题时间</span>
          <span class="red">{{ leftTimeStr }}</span>
          <span style="float: right; font-size:16px; " class="linkBtn" @click="handInBtn">交卷</span></p>
      </div>
      <div class="question">
        <div class="title"><span>一、单选题</span><span style="margin-left:15px;">共{{chooseAllFen}}分</span></div>
        <div>
          <table class="def-table" width="100%" v-if="chooseList.length > 0">
            <thead>
            <tr>
              <td width="20%">题目</td>
              <td width="20%">分数</td>
              <td width="40%">您的选择</td>
              <td width="20%"></td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(choice, index) in chooseList" :key="index">
              <td>{{index + 1}}</td>
              <td>{{choice.trainingExamQuestion.score}}</td>
              <td>{{choice.trainingExamUserAnswer.memoStr}}</td>
              <td @click="goQues(index, choice)">
                <div class="rowPos">
                  <div class="ui-cell__ft"></div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="title"><span>二、判断题</span><span style="margin-left:20px;">共{{tfAllFen}}分</span></div>
        <div>
          <table class="def-table" width="100%" v-if="tfList.length > 0">
            <thead>
            <tr>
              <td width="20%">题目</td>
              <td width="20%">分数</td>
              <td width="40%">您的选择</td>
              <td width="20%"></td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(choice, index) in tfList" :key="index">
              <td>{{index + 1}}</td>
              <td>{{choice.trainingExamQuestion.score}}</td>
              <td>{{choice.trainingExamUserAnswer.memoStr}}</td>
              <td @click="goQues(index, choice)">
                <div class="rowPos">
                  <div class="ui-cell__ft"></div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <el-dialog title="提示" :visible.sync="stopDialog" width="70%" class="def-danger">
      <p class="st-center">确定要交卷吗？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="stopDialog = false">取消</a>
          <a class="fc-btn ui-btn_info" ref="operate" @click="handIn">确定</a>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="less">
  #questionList{
    overflow: auto;color: #000001;
    .ttl{font-size: 14px;}
    .linkBtn{margin-right: 0;}
    .red{ color:red; font-size:18px; display: inline-block; margin-left:40px;  }
    .title{ background:#eee;padding:5px 15px;  }
    .top{ padding:10px 15px; background: #fff;   }
    .def-table{
      thead {  color: #909399;  font-weight: 500;  }
      tr {  background-color: #fff;  }
      td{  padding: 6px 0;font-size: 12px;  word-wrap: break-word;  word-spacing: normal;  word-break: break-all;  color: #606266; text-align: center; }
    }
    .rowPos{
      position: relative;
      .ui-cell__ft::after{ border-color: #606266; }
    }
  }
</style>
<script>
import { gohistory } from '../../../js/common'
export default {
  name: 'questionList',
  data () {
    return {
      chooseList: [],
      tfList: [],
      chooseAllFen: 0,
      tfAllFen: 0,
      info: {},
      stopDialog: false,
      leftTimeStr: {},
      loading: true,
      exam: '',
      timeRun: 1
    }
  },
  created () {
    this.getList()
  },
  destroyed: function () {
    let _this = this
    _this.timeRun = 0
  },
  methods: {
    handInBtn:function() {
      let that = this
      that.stopDialog = true
    },
    handIn:function() {
      let that = this
      let url = '../../../examManage/endExam.do'
      let data = { 'exam': this.exam }
      that.loading = true
      that.axios.post(url, data).then((response) => {
        that.loading = false
        let res = response.data
        let status = res.status
        if(status == 1){
          that.setPath()
        }else{
          that.$kiko_message('提交失败，请重试！')
        }
      }).catch(function (res) {
        console.log(res)
      })
    },
    getList: function() {
      this.exam = this.$route.params.id
      let that = this
      let url = '../../../examManage/startExam.do'
      let data = { 'exam': this.exam }
      let chNum = 0, tfNum = 0
      that.axios.post(url, data).then((response) => {
        that.loading = false
        let data = response.data
        let nowDate = new Date(response.headers.date)
        if (data) {
          that.info = {
            'exam': this.exam,
            'startTime': data.startTime,
            'endTime': data.endTime,
            'date': nowDate
          }
          that.setLeftTime()
          let questionList = data.respExamQuestionList || []
          questionList.forEach(function(item,index) {
            item.index = index
            let Ans = item.trainingExamUserAnswer || {}
            item.trainingExamUserAnswer = Ans
            let memo = Ans.memo
            if (memo) {
            } else {
              Ans.memoStr = '尚未选择'
            }
            let type = item.trainingQuestion.type
            if (Number(type) === 1) {
              if (memo) { Ans.memoStr = memo }
              chNum++
              item.orders = chNum
              that.chooseList.push(item)
              that.chooseAllFen += Number(item.trainingExamQuestion.score)
            } else {
              if (memo === '1' || memo === 1) {
                Ans.memoStr = '√'
              } else if (memo === '0' || memo === 0) {
                Ans.memoStr = '×'
              }
              tfNum++
              item.orders = tfNum
              that.tfList.push(item)
              that.tfAllFen += Number(item.trainingExamQuestion.score)
            }
          })
          that.info.respExamQuestionList = questionList
        } else {
          that.$kiko_message('加载失败')
        }
      }).catch(function (res) {
        console.log(res)
      })
    },
    goQues: function (index, row) {
      row.orders = index + 1
      let res = { 'ques': row, 'info': this.info }
      localStorage.setItem('res', JSON.stringify(res))
      this.$router.push({
        path: `/question`
      })
    },
    setLeftTime: function () {
      let that = this
      if (that.timeRun === 1) {
        let now = (new Date(that.info.date)).getTime();
        let end = (new Date(that.info.endTime)).getTime();
        let leftTime = end - now;
        let h, m, s;
        h = Math.floor(leftTime/1000/60/60%24);
        m = Math.floor(leftTime/1000/60);
        s = Math.floor(leftTime/1000%60);
        if (m < 10) { m = '0' + m}
        if (s < 10) { s = '0' + s}
        if (leftTime > 1) {
          that.leftTimeStr = `${m}:${s}`
          now = now + 1000
          that.info.date = now
          setTimeout(that.setLeftTime,1000);
        } else {
          that.leftTimeStr = `00:00`
          that.$kiko_message('答题时间已过')
          that.setPath()
        }
      }
    },
    setPath: function () {
      let that = this
      let floatingUrl = window.localStorage.getItem('floating')
      if (floatingUrl) {
        let pathArr = floatingUrl.split('-')
        let newArr = pathArr.slice(0, pathArr.length - 2)
        this.$router.push({
          path: `/examResult/${this.exam}`
        })
        window.localStorage.setItem('floating', newArr.join('-'))
      }
    }
  }
}
</script>
