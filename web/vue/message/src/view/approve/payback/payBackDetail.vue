<template>
  <div id="backDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="回款入账" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="lHead">
            <p class="line-s">{{customer}}</p>
            <p class="listCon line-s">本次回款 {{detail.method | incomeType}} {{detail.amount | formatMoney}}元</p>
          </div>
          <div v-if="loop === false">
            <div class="lHead sectCenter">
              <div class="checkWay" v-if="detail.method === '1'">
                <div class="line-p">
                  <span class="tt-cl">收到日期</span>
                  <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
              </div>
              <div class="checkWay" v-if="detail.method === '3'">
                <div class="line-p">
                  <span class="tt-cl">收到日期</span>
                  <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">支票号</span>
                  <span class="sub-cl">{{detail.returnNo}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">到期日</span>
                  <span class="sub-cl">{{detail.expireDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">出具的单位</span>
                  <span class="sub-cl">{{detail.originalCorp}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">出具的银行</span>
                  <span class="sub-cl">{{detail.bankName}}</span>
                </div>
              </div>
              <div class="checkWay" v-if="detail.method === '4'">
                <div class="line-p">
                  <span class="tt-cl">收到日期</span>
                  <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">汇票号</span>
                  <span class="sub-cl">{{detail.returnNo}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">到期日</span>
                  <span class="sub-cl">{{detail.expireDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">最初出具的单位</span>
                  <span class="sub-cl ellipsisSet">{{detail.originalCorp}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">出具的银行</span>
                  <span class="sub-cl ellipsisSet">{{detail.bankName}}</span>
                </div>
              </div>
              <div class="checkWay" v-if="detail.method === '5'">
                <div class="line-s">
                  <span class="tt-cl">预计到账的日期范围</span>
                </div>
                <div class="line-p">
                  <span class="sub-cl">{{detail.expectBeginDate | formatDate('yyyy年MM月dd日')}}</span>
                  -
                  <span class="sub-cl">{{detail.expectEndDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
              </div>
              <p class="line-p inputerInfo">录入者：{{detail.createName}} {{detail.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
            </div>
            <div class="sureTip">
              <p class="tipH">！提示</p>
              <div class="tipB">
                <p>如您尚未收到上述款项，则无需操作。</p>
                <p>如该笔回款确已收到且已录入系统，请选择究竟为哪笔</p>
                <el-form ref="accountFinishForm" :model="accountFinishForm">
                  <el-form-item>
                    <el-radio-group v-model="accountFinishForm.caseRadio" @change="caseChange" class="defined-radio">
                      <div v-for="(item, index) in collectConfirmedList" :key="index" v-if="index < showNum">
                        <el-radio :label="item.id" :disabled="caseRadioCtrl(item)"></el-radio>
                        <span @click="seeCollectDetail(item.id)">{{item.method | incomeType}} {{item.amount | formatMoney}}元  {{item.approvalDate | formatDate('yyyy/MM/dd hh:mm:ss')}} ></span>
                      </div>
                    </el-radio-group>
                  </el-form-item>
                </el-form>
                <div class="handle_button handle-center" v-if="collectConfirmedList.length > 6">
                  <input v-if="showNum <= collectConfirmedList.length" type="button" class="fc-btn fc-btn-green" @click="showMore" value="在更多回款中选择"/>
                  <p v-else>该客户在系统中没有更多的回款数据了</p>
                </div>
                <div class="iAgree">
                  <el-form ref="agreeForm" :model="agreeForm">
                    <el-form-item>
                      <el-radio-group v-model="agreeForm.type" @change="agreeChange">
                        <el-radio :label="true">该款项尚未录入至系统，但已收到。</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
              <div>
                <div v-if="agreeForm.type && detail.method === '5'" class="displace">
                  <el-form ref="transForm" :model="transForm" :rules="transRules" size="mini">
                    <el-form-item label="收款银行" prop="receiveBank">
                      <el-select placeholder="请选择" v-model="transForm.receiveBank">
                        <el-option
                          v-for="item in accounts"
                          :key="item.id"
                          :label="item.bankNameStr"
                          :value="item.id">
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="到账日期" prop="receiveDate">
                      <el-date-picker type="date" placeholder="请选择" v-model="transForm.receiveDate"></el-date-picker>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="handle_button handle-center">
                  <input type="submit" class="fd-btn fc-btn-yellow" value="取消" @click="stopIssueFile()"/>
                  <input v-if="agreeForm.type === true" type="submit" :class="{'fd-btn fc-btn-green': !loapCtrl || !isCan, 'fd-btn ui-btn_disabled': loapCtrl || isCan}" :disabled="isCan || loapCtrl" value="确定" @click="approveSure()"/>
                </div>
              </div>
            </div>
          </div>
          <div v-if="loop === true">
            <div class="lHead sectCenter">
              <div class="checkWay" v-if="collectConfirmed.method === '1'">
                <div class="line-p">
                  <span class="tt-cl">收到日期</span>
                  <span class="sub-cl">{{collectConfirmed.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
              </div>
              <div class="checkWay" v-if="collectConfirmed.method === '3'">
                <div class="line-p">
                  <span class="tt-cl">收到日期</span>
                  <span class="sub-cl">{{collectConfirmed.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">支票号</span>
                  <span class="sub-cl">{{collectConfirmed.returnNo}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">到期日</span>
                  <span class="sub-cl">{{collectConfirmed.expireDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">出具的单位</span>
                  <span class="sub-cl">{{collectConfirmed.originalCorp}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">出具的银行</span>
                  <span class="sub-cl">{{collectConfirmed.bankName}}</span>
                </div>
              </div>
              <div class="checkWay" v-if="collectConfirmed.method === '4'">
                <div class="line-p">
                  <span class="tt-cl">收到日期</span>
                  <span class="sub-cl">{{collectConfirmed.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">汇票号</span>
                  <span class="sub-cl">{{collectConfirmed.returnNo}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">到期日</span>
                  <span class="sub-cl">{{collectConfirmed.expireDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">最初出具的单位</span>
                  <span class="sub-cl ellipsisSet">{{collectConfirmed.originalCorp}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">出具的银行</span>
                  <span class="sub-cl ellipsisSet">{{collectConfirmed.bankName}}</span>
                </div>
              </div>
              <div class="checkWay" v-if="collectConfirmed.method === '5'">
                <div class="line-p">
                  <span class="tt-cl">收到日期</span>
                  <span class="sub-cl">{{collectConfirmed.expireDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-p">
                  <span class="tt-cl">收款银行</span>
                  <span class="sub-cl">{{collectConfirmed.bankName}}</span>
                </div>
              </div>
              <p class="line-p inputerInfo">录入者：{{collectConfirmed.createName}} {{collectConfirmed.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
              <p class="line-p inputerInfo">财  务：{{collectConfirmed.financerName}} {{collectConfirmed.approvalDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
            </div>
            <div class="handle_button handle-center">
              <p class="sureTip">系统中此笔款就是该{{collectConfirmed.amount | formatMoney}}元回款？</p>
              <input type="submit" class="fd-btn fc-btn-yellow" value="取消" @click="reSelection"/>
              <input type="submit" class="fd-btn fc-btn-green" value="确定" @click="combineSure"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .tipB{
    margin-left:30px;
  }
  .defined-radio .el-radio__input.is-disabled .el-radio__inner{
    background-color: #c7cace;
    border-color: #a3a5a8;
  }
</style>

<script>
import { incomeType, formatDate, formatMoney, handleNull } from '../../../js/common'
export default {
  name: 'payApproveDetail',
  filters: {
    incomeType,
    formatDate,
    formatMoney,
    handleNull
  },
  data () {
    return {
      isCan: false,
      loop: false,
      detail: {},
      customer: '',
      accounts: [],
      loading: true,
      transForm: {
        receiveBank: '',
        receiveDate: ''
      },
      transRules: {
        receiveBank: [
          { required: true, message: '请选择收款银行', trigger: 'change' }
        ],
        receiveDate: [
          { type: 'date', required: true, message: '请到账日期', trigger: 'change' }
        ]
      },
      showNum: 6,
      collectConfirmed: {},
      collectConfirmedList: [],
      accountFinishForm: {
        caseRadio: ''
      },
      agreeForm: {
        type: false
      },
      listenersUid: []
    }
  },
  computed: {
    loapCtrl () {
      let method = this.detail.method
      let bool = true
      switch (method) {
        case '1':
        case '2':
        case '3':
        case '4':
          if (this.agreeForm.type) {
            bool = false
          } else {
            bool = true
          }
          break
        case '5':
          if (this.transForm.receiveBank !== '' && this.transForm.receiveDate !== '') {
            bool = false
          } else {
            bool = true
          }
          break
      }
      return bool
    }
  },
  created () {
    let _this = this
    let dlId = _this.$route.params.sId
    let gId = _this.sphdSocket.user.oid
    _this.listenersUid = [
      _this.sphdSocket.subscribe('getSlCollectDetail', function (data) {
        console.log('返回的回款信息：' + data)
        let resDetail = JSON.parse(data)
        _this.customer = resDetail.customerName
        _this.detail = resDetail.slCollectApplication
        _this.loading = false
        let confirmedParam = {
          oid: gId,
          customer: resDetail.slCollectApplication.customer
        }
        _this.axios.post('../../../collectWindow/getCollectFinance.do', confirmedParam).then(function (response) {
          _this.collectConfirmedList = response.data.data.slCollectApplications
        }).catch(function (error) {
          console.log(error)
        })
      }),
      _this.sphdSocket.subscribe('collectPayment', function (data) {
        let res = JSON.parse(data)
        let state = res.state
        if (state === 1 || state === '1') {
          _this.loading = false
          _this.$message({
            type: 'success',
            message: '审批成功！'
          })
          _this.stopIssueFile()
        } else {
          _this.$message({
            type: 'error',
            message: '审批失败！'
          })
        }
      }, null, 'session', _this.sphdSocket.sessionid)
    ]
    _this.sphdSocket.send('getSlCollectDetail', { 'collectId': dlId, 'session': _this.sphdSocket.sessionid })
  },
  destroyed: function () {
    let _this = this
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    approveSure: function () {
      var method = this.detail.method
      let json = {
        'type': '1',
        'userId': this.sphdSocket.user.userID,
        'collectId': this.$route.params.sId,
        'customerName': this.customer,
        'session': this.sphdSocket.sessionid
      }
      if (method === '1' || method === '2' || method === '3' || method === '4') {
        json.receiveDate = ''
        json.financeAccountId = ''
      } else {
        json.receiveDate = this.transForm.receiveDate
        json.financeAccountId = this.transForm.receiveBank
      }
      this.sphdSocket.send('collectPayment', json)
      this.isCan = true
      this.loading = true
    },
    stopIssueFile: function () {
      let floatingUrl = window.localStorage.getItem('floating')
      if (floatingUrl) {
        floatingUrl = floatingUrl.split('-')
        if (floatingUrl.length > 0) {
          floatingUrl.pop()
          if (floatingUrl.length > 0) {
            this.$router.push({
              path: floatingUrl[floatingUrl.length - 1]
            })
          } else {
            this.$router.push({
              path: '/'
            })
          }
          floatingUrl.pop()
          window.localStorage.setItem('floating', floatingUrl.join('-'))
        } else {
          this.$router.push({
            path: '/'
          })
        }
      }
    },
    caseChange: function () {
      this.loop = true
      this.agreeForm.type = false
      let getData = this.collectConfirmedList.find(item => Number(item.id) === Number(this.accountFinishForm.caseRadio))
      if (getData !== undefined) {
        this.collectConfirmed = getData
      }
    },
    agreeChange: function () {
      let that = this
      if (this.agreeForm.type === true) {
        this.accountFinishForm.caseRadio = ''
        if (this.detail.method === '5') {
          this.$http.post('../../../reimburseWindow/getAccounts.do', {
            'oid': this.sphdSocket.user.oid
          }, {
            emulateJSON: true
          }).then((response) => {
            console.log('success')
            let accounts = response.body.data
            console.log(accounts)
            if (accounts && accounts.length > 0) {
              accounts.forEach(function (item) {
                console.log(item)
                let bankNameStr = item["name"] + ' ' + that.formatAccount(item["account"])+ ' ' + item["bankName"] ;
                if(item.isPublic == 1){
                  bankNameStr = that.formatAccount(item["account"])+ ' ' + item["bankName"] ;
                }
                item.bankNameStr = bankNameStr
                console.log(item)
              })
              that.accounts = accounts
              console.log('accounts 结果：')
              console.log(accounts)
            }
          }).catch(function () {
            console.log('error')
            this.$message({
              type: 'error',
              message: '系统错误，请重试！'
            })
          })
        }
      }
    },
    // create:hxz 2022-03-07 格式化 账号
    formatAccount(account){
      let accountStr = `****`
      if(account.length >3){
        accountStr += account.substr(account.length-4,4)
      }else{
        let n = 4-account.length ;
        for(let i = 0 ; i < n; n++){
          accountStr += '*'
        }
        accountStr += account
      }
      return accountStr
    },
    reSelection: function () {
      this.loop = false
      this.accountFinishForm.caseRadio = ''
    },
    seeCollectDetail: function (id) {
      this.$router.push({
        path: `/collectDetail/${id}`
      })
    },
    showMore: function () {
      if (this.showNum < this.collectConfirmedList.length) {
        this.showNum = this.showNum + 6
      }
    },
    combineSure: function () {
      this.loading = true
      let json = {
        'userId': this.sphdSocket.user.userID,
        'collectId': this.$route.params.sId,
        'collectPayId': this.collectConfirmed.id,
        'session': this.sphdSocket.sessionid
      }
      this.sphdSocket.send('getDetermine', json)
      this.stopIssueFile()
    },
    caseRadioCtrl (data) {
      if (Number(data.method) === Number(this.detail.method) && formatMoney(data.amount) === formatMoney(this.detail.amount)) {
        switch (data.method) {
          case '1':
          case '5':
            return false
          case '3':
          case '4':
            if (data.returnNo === this.detail.returnNo && data.expireDate === this.detail.expireDate && data.originalCorp === this.detail.originalCorp && data.bankName === this.detail.bankName) {
              return false
            } else {
              return true
            }
        }
      } else {
        return true
      }
    }
  }
}
</script>
