<template>
  <div id="potentialContact" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="def-content">
        <table class="sect-table table">
          <thead>
          <tr>
            <td>名称</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>基本信息</td>
            <td>
              <span class="sect-blue" @click="jumpBase">修改</span>
              <span class="sect-blue" @click="jumpRecordList(1, 0)">修改记录</span>
            </td>
          </tr>
          <tr>
            <td>联系人</td>
            <td>
              <span class="sect-blue" @click="jumpContact(0, 'new')">新增</span>
              <span class="sect-blue" @click="jumpDelData(2)">已被删除的数据</span>
            </td>
          </tr>
          <tr v-for="(contact, index) in details.contactsList" :key="index">
            <td class="sm-text">{{contact.name}}  {{contact.post && (contact.post).substr(0,10)}}</td>
            <td v-if="contact.enabled !== 0">
              <span class="sect-blue" @click="jumpContact(contact.id, 'update')">修改</span>
              <span class="sect-blue" @click="jumpRecordList(2, contact.id)">修改记录</span>
              <span class="sect-red" @click="deleteRecord(2, contact.id)">删除</span>
            </td>
          </tr>
          <tr>
            <td>访谈记录</td>
            <td>
              <span class="sect-blue" @click="jumpInterView(0, 'new')">新增</span>
              <span class="sect-blue" @click="jumpDelData(3)">已被删除的数据</span>
            </td>
          </tr>
          <tr v-for="(interview, index) in details.interviewList" :key="index">
            <td>访谈对象 {{interview.interviewerNames }}</td>
            <td class="sm-text" v-if="interview.operation !== '2'">
              <span class="sect-blue" @click="jumpInterView(interview.id, 'update')">修改</span>
              <span class="sect-blue" @click="jumpRecordList(3, interview.id)">修改记录</span>
              <span class="sect-red" @click="deleteRecord(3, interview.id)">删除</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="deleteDialog"
      width="70%" class="def-danger">
      <p class="st-center">删除后，本{{deleteTip}}依旧显示在{{deleteTip}}的列表中，但仅可查看。</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="deleteDialog = false">取消</a>
          <a class="fc-btn ui-btn_info" ref="operate" @click="deleteSure">确定</a>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="less">
  .sm-text{font-size: 12px;}
</style>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'potentialEdit',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      details: {
        contactsList: [],
        interviewList: []
      },
      deleteId: '',
      deleteTip: '',
      deleteType: '',
      deleteDialog: false
    }
  },
  created () {
    this.getUpdateDetail()
  },
  methods: {
    getUpdateDetail: function () {
      let _this = this
      _this.axios.post('../../../sale/getPotentialUpdatePageData.do', {
        'customerId': _this.$route.params.updateId
      }).then(function (response) {
        _this.loading = false
        _this.details = response.data

        if (response.data.interviewList && response.data.interviewList.length > 0){
          response.data.interviewList.forEach(function (item){
            let names = item.interviewerNames.split(",")
            for(let i = 0; i < names.length; i++) {
              names[i] === "" ? names[i] = '--' : "";
            }
            item.interviewerNames = names.toString()
          })
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    jumpBase: function () {
      let detailId = this.$route.params.updateId
      this.$router.push({
        path: `/baseDetailUpdate/${detailId}`
      })
    },
    jumpContact: function (id, handleType) {
      let detailId = this.$route.params.updateId
      this.$router.push({
        path: `/contactDetailUpdate/${detailId}/${id}/${handleType}`
      })
    },
    jumpInterView: function (id, handleType) {
      let detailId = this.$route.params.updateId
      this.$router.push({
        path: `/viewDetailUpdate/${detailId}/${id}/${handleType}`
      })
    },
    jumpRecordList: function (tab, id) {
      let detailId = this.$route.params.updateId
      console.log('修改记录：' + detailId)
      this.$router.push({
        path: `/potentialRecord/${detailId}/${id}/${tab}`
      })
    },
    deleteRecord: function (type, itemId) {
      this.deleteDialog = true
      this.deleteId = itemId
      this.deleteType = type
      if (type === 2) {
        this.deleteTip = '联系人'
      } else {
        this.deleteTip = '访谈记录'
      }
    },
    jumpDelData: function (type) {
      let detailId = this.$route.params.updateId
      this.$router.push({
        path: `/potentialDelList/${type}/${detailId}`
      })
    },
    deleteSure: function () {
      let _this = this
      _this.loading = true
      let urlStr = ''
      let param = {}
      let delId = _this.deleteId
      let delType = _this.deleteType
      if (delType === 2) {
        urlStr = '../../../sales/deleteCustomerContact.do'
        param = {
          'contactId': delId
        }
      } else {
        urlStr = '../../../sales/delCustomerInterview.do'
        param = {
          'id': delId
        }
      }
      this.axios.post(urlStr, param).then(function (response) {
        _this.details = response.data
        _this.getUpdateDetail()
        _this.deleteDialog = false
        _this.loading = false
      }).catch(function (error) {
        console.log(error)
      })
    }
  }
}
</script>
