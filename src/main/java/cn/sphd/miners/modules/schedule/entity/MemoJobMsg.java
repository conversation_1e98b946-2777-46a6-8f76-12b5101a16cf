package cn.sphd.miners.modules.schedule.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2020/1/2.
 * 备忘与日程_提醒处理表
 */
@Entity
@Table(name = "t_memo_job_msg")
public class MemoJobMsg implements Serializable {

    @Id
    @Column(name="id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="job"   , nullable=true , unique=false)
    private Integer job;//提醒ID

    @Column(name="schedule"   , nullable=true , unique=false)
    private Integer schedule;//日程ID

    @Column(name="job_uid"  ,length = 72, nullable=true , unique=false)
    private String jobUid;//提醒的唯一标识符,可用日程UID+提醒首次运行时间 生成，再次提醒时此值保持不变

    @Column(name="user"  , nullable=true , unique=false)
    private Integer userId;//接收者ID

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;//0-草稿，1-已发送，2-收方已读，3—已处理（预留）4-已删除

//    @Column(name="client_type"  , length=1 , nullable=true , unique=false)
//    private String clientType;// 终端:1-android,2-IOS,3-PC

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;//标题

    @Column(name="content"  , length=255 , nullable=true , unique=false)
    private String content;//  内容

    @Column(name="uri"  , length=255 , nullable=true , unique=false)
    private String uri;// 消息跳转详情的路径

    //    @Column(name="time_desc"  , length=255 , nullable=true , unique=false)
//    private String timeDesc;//时间描述
//
    @Column(name="send_time"   , nullable=false , unique=false)
    private Date sendTime;//时间

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//说明

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="business_type"   , nullable=true , unique=false)
    private Integer businessType;

    @Column(name="callback_class"  , length=100 , nullable=true , unique=false)
    private String callbackClass;//存方法Sevice

    @Column(name="callback_params"   , nullable=true , unique=false)
    private String callbackParams;//数据id


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getJob() {
        return job;
    }

    public void setJob(Integer job) {
        this.job = job;
    }

    public Integer getSchedule() {
        return schedule;
    }

    public void setSchedule(Integer schedule) {
        this.schedule = schedule;
    }

    public String getJobUid() {
        return jobUid;
    }

    public void setJobUid(String jobUid) {
        this.jobUid = jobUid;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }


    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getCallbackClass() {
        return callbackClass;
    }

    public void setCallbackClass(String callbackClass) {
        this.callbackClass = callbackClass;
    }

    public String getCallbackParams() {
        return callbackParams;
    }

    public void setCallbackParams(String callbackParams) {
        this.callbackParams = callbackParams;
    }

}
