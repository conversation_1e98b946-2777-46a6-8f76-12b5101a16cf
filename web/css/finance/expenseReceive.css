
*{ font-family: "Microsoft Yahei";   }
td,th{ 
	text-align:center!important; 
	vertical-align:middle !important;
	overflow:hidden;white-space:normal; word-break:break-all;text-overflow:ellipsis;
	
 }
.hd{
	display:none;
}
.clr{ clear:both;  }
.Con_head{
	font-size: 18px;
	font-weight: 700;

}
.Con_continer{
	padding-top: 15px;
}
.Con_con{
	border: 1px solid #e4e4e4;
}
.Con_bor{
	padding: 15px 15px 15px 20px;
	width: 100%;

}
.Con_left{
	float: left;
	width: 160px;
}
.Con_right{
	padding-left: 164px; 
}
.catecon_left{
	float: left;
	width: 60%;
}
.catecon_right{
	float: right;
	width: 35%;
	/*margin-left: 35px;*/
	/*padding-left: 1000px;*/
}
.Con_deal,.category_set{
	border-left: 2px solid #5b9bd1;
	background-color: #f6f9fb;
	height: 45px;
	width: 160px;
	font-weight: 400;
	color: #5b9bd1;

}
.Con_deal>a,.category_set>a{
	margin-left: 22px;
	line-height: 45px;
	font-size: 13px;
	text-decoration: none;
	color: #5b9bd1;
}
.ConLeft{
	width: 160px;
}
.Con_set,.category_deal{
	height: 45px;
	width: 160px;
	font-weight: 400;
	color: #5a7391;
}
.Con_set>a,.category_deal>a{
	margin-left: 24px;
	line-height: 45px;
	font-size: 13px;
	text-decoration: none;
	color: #5a7391;
}

.right_continer{
	min-width: 500px;
}

.right_head{
	padding: 15px 0;
	font-size: 16px;
	font-weight: 700;
	color: #4276a4;
}
.right_Con{
	padding-left:20px; 
}
.right_bor{
	border-top: 1px solid #e4e4e4;
	padding: 20px 0 5px 0;
	height: 55px;
	
}

.right_bor>span{
	font-weight: 700;
	border-bottom: 4px solid #35c6d3;
	padding: 8px 5px;
	margin-right: 35px;
	color: #333;
	cursor: pointer;
}
.right_bor>span:nth-child(2),.right_bor>span:nth-child(3),.right_bor>span:nth-child(4){
	font-weight: 700;
	border-bottom: 4px solid #fff;
	padding: 8px 5px;
	margin-right: 35px;
	color: #333;
}
.right_tab{
	margin-top: 5px;
	padding-right: 20px;
}
.bg{
	background-color: #f1f1f1;
}
.hide_balance,.hide_wipe,.hide_reject,.finance_hide,.balance_seehide,.wipe_seehide,.reject_seehide{
	display: none;
}
.pic{
	margin: 30px 0;
	padding:9px 30px 14px 20px;
	background: #fff 
	url(../../assets/img/goback.png) no-repeat center center; 
	background-size: 40px;
	cursor: pointer;

}
.pic_yes{
	margin: 30px 0;
	padding:14px 21px 8px 24px;
	background: #f2f2f2 
	url(../../assets/img/yes.png) no-repeat center center; 
	background-size: 30px;
}
.pic_yes1{
	margin: 30px 0;
	padding:14px 21px 10px 23px;
	background: #e0ebf9 
	url(../../assets/img/yes1.png) no-repeat center center; 
	background-size: 40px;
}
.pic_deal{
	margin: 30px 0;
	padding:11px 21px 10px 23px;
	background: #f2f2f2 
	url(../../assets/img/deal.png) no-repeat center center; 
	background-size: 35px;
}
.pic_reject{
	margin: 30px 0;
	padding:11px 21px 10px 23px;
	background: #fee7e8 
	url(../../assets/img/reject.png) no-repeat center center; 
	background-size: 40px;
}
.pic_rejectBack{
	margin: 30px 0;
	padding:11px 35px 10px 10px;
	background: #fee7e8 
	url(../../assets/img/rejectBack.png) no-repeat center center; 
	background-size: 40px;
}
.finance_See{
	border: 1px solid #e4e4e4;
}
.dealSee{
	padding: 10px 20px 10px 20px;
}
.dealSee_head{
	padding: 10px 0 20px 5px;
	border-bottom: 1px solid #e4e4e4;
	font-size: 22px;
	font-weight: 400;
	color: #333;
}
.dealSee_con{
	background-color: #f2f2f2;
	margin-top: 20px;
}
.dealSee_conwipe{
	background-color: #e0ebf9;
	margin-top: 20px;
}
.dealSee_conreject{
	background-color: #fee7e8;
	margin-top: 20px;
}
.aboutbtn{ 
	display:inline-block; 	
	cursor:pointer; 
	margin-right: 5px; 
	padding:5px 25px;
	font-weight:400;  
}
.aboutbtn_reject{ 
	display:inline-block; 	
	cursor:pointer; 
	margin-right: 5px; 
	padding:5px 15px;
	font-weight:400;  
}
.aboutbtn_cate{
	display:inline-block; 	
	cursor:pointer; 
	margin-right: 5px; 
	padding:5px 30px;
	font-weight:400;
	border: 1px solid #e4e4e4;
}
.azury,.azury:hover{ 
	color:#fff;
	background:#3598dc;
	text-decoration: none;		
}
.azury:hover{
	color: #fff;
	background-color: #217ebd;
}
.azury_reject,.azury_reject:hover{ 
	color:#fff;
	background:#e7505a;
	text-decoration: none;		
}
.azury_reject:hover{
	color: #fff;
	background-color: #e12330;
}
.azury_cata,.azury_cata:hover{ 
	color:#333;
	background:#fff;
	text-decoration: none;		
}
.azury_cata:hover{
	color: #333;
	background-color: #eef1f5;
}
.resourceAddBtn{ position: relative; top:-4px; }
.dealSee_Fon{
	color: #999;

}
.dealSee_Fon>span:nth-child(7){
	margin-left: 35px;

}
.dealSee_process>div:nth-child(1),.dealSee_processwipe>div:nth-child(1){
	padding:23px 0 8px 0;
}
.dealSee_process>div:nth-child(2),.dealSee_process>div:nth-child(3),.dealSee_process>div:nth-child(4),.dealSee_process>div:nth-child(5),
.dealSee_processwipe>div:nth-child(2),.dealSee_processwipe>div:nth-child(3),.dealSee_processwipe>div:nth-child(4),.dealSee_processwipe>div:nth-child(5){
	padding: 8px 0;
}
.dealSee_process>div:last-child{
	color: #3598dc;
}
.dealSee_person{
	display: inline-block;
	width: 230px;
}
.dealSee_time{
	display: inline-block;
	width: 200px;
}
.table_con{
	margin-top: 50px;
	font-size: 14px;

}
.t_left{
	text-align: right !important;
	font-weight: 700;
}
.t_right{
	text-align: left !important;
	font-weight: 400;
}
/*.bonceContainer{*/
	/*width: 600px;*/
	/*font-size: 16px;*/
/*}*/
.reject_Input>input{
	height: 86px !important;
	width: 260px !important;
	font-weight: 400;
	color: #999;
	font-size: 13px;
}
.reject_Input>span{
	width: 90px;
	display: inline-block;
}
.category_con{
	padding:0 15px;
	
}
.cate_continer{
	border-top: 1px solid #e4e4e4;
	padding: 0 10px;
}
.categoty_button{
	padding: 20px 0 10px 0;
}
.cate_tab>a{
	color: #3096da;
	text-decoration: none;

}
.cate_tab>a:nth-child(1){
	/*border-right: 1px solid #999;*/
	padding-right: 10px;
}
.cate_tab>a:nth-child(2){
	padding-left: 10px;

}
.catecon_right>span{
	font-size: 16px;
	font-weight: 700;
	color: #666;

}
.category_table{
	margin-top: 5px;
}
.bonceHead>span{
	font-weight: 700;
	font-size: 14px;

}
.catagory_Input>input{
	width: 42%;
}
.cata_col{
	color: #3096da;
	cursor:pointer;

}
.wipe_deal>span{
	font-size: 20px;
	font-weight: 700;
	color: #3598dc;

}
.reject_font,.reject_font1{
	color: #e7505a;
	padding: 8px 0;
}
.reject_font1{
	margin-left: 45px;
}
.op_hover{
	cursor: pointer;
}
.reject_be{
	position: relative; top:-20px; 
}
.deal_Nav{
	border-bottom: 4px solid #fff !important;	
}
.deal_active{
	border-bottom: 4px solid #35c6d3 !important;
}

.ok-icon { background: url(../user/icon/ok.png) no-repeat center center;  }
.no-icon { background: url(../user/icon/no.png) no-repeat center center;  }
.wait-icon { background: url(../user/icon/wait.png) no-repeat center center;  }
.ok-icon , .no-icon , .wait-icon { background-size:25px 25px; display:inline-block; width:25px; height:25px;margin-right:15px;    }
.processTr{ padding-top:15px;      }
.touserInfo{ margin-right:30px;    }
.touserInfo , .timeInfo{ position:relative; top:-8px;    }
.memoInfo{  padding:5px 0 5px 50px;   }
.noProcess{ color:#E7505A;  }

.applyYes{ background: rgb(224, 235, 249);   }
.applyNo{ background: rgb(254, 231, 232);  }
.dealSee_continer{ margin:20px 0 ; padding:30px; color:#3598dc;   }
.dealSee_continer .ttl { font-size:20px;     }

#balanceReceive{ width:740px;  }
.receiveTr{ padding:5px;  }
.receiveTr>span{ display: inline-block; padding:5px; text-align: right; width:150px;       }
.receiveTr>select{ display: inline-block; padding:5px; width:440px;       }
.receiveTr>input{ display: inline-block; padding:5px; width:440px;       }

#info_images>img{ height:50px;      }
/*// 大图的展示*/
#info_img>img{ height:50px ; max-width: 200px   }
#bigImagcon{ background: #fff ; margin:0 auto; position: relative ;   }
#imgController{ background: #101010; z-index:1;  font-size: 23px; width: 400px; height:45px; position: absolute;  bottom: 100px;  text-align: center ; padding:5px 0 ;    }
#imgController .rotate , #bigImagcon .closeImg{  color:#ddd; margin-right: 25px;  }
#imgController .closeImg{ font-size: 25px;   }
#imgController .closeImg:hover{ color:#ed5565;  }
#imgController .rotate:hover { color:#fff;   }
.bigImagC{ position: relative;  }
#bigImag{  display:block ; box-shadow:0 0 5px #6e6c6c; margin: 0 auto ;   }
.bounce_Fixed{ flex-direction:column; justify-content:center; }
#amountTip{ color:red; font-size:11px;  }


#confirm{ width:50%; }
.gap-tp{padding: 10px 0;}
.limitBody{padding: 0 100px;}

