<template>
  <div class="authority">
    <TyDialog v-if="dialog_visible_permissionAssignment" width="900" dialogTitle="权限分配" color="green" :dialogHide="dialogHide">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="dialog_visible_permissionAssignment = false">取消</el-button>
        <el-button class="bounce-ok" @click="permissionAssignment_submit">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 800px; margin: 0 auto">
          <el-tabs v-model="active">
            <el-tab-pane label="仅能分配给一人的权限" name="first">
              <div>
                <el-text>
                  下列权限仅可分配给一人，请勾选需分配给{{form_editAuthority.cusInfo.userName}}的权限。
                </el-text>
              </div>
              <div style="margin-top: 4px">
                <el-text size="small" type="danger">
                  <el-icon><WarningFilled /></el-icon> 某权限如已分配给某职工，则再次勾选生效后，原职工将不再拥有此权限。
                </el-text>
              </div>
              <el-checkbox-group v-model="checkList" style="margin-top: 16px">
                <table class="kj-table kj-table-left">
                  <thead>
                  <tr>
                    <td style="width: 25%">模块</td>
                    <td style="width: 75%">
                      <table class="kj-table" frame="void">
                        <thead>
                        <tr>
                          <td style="width: 35%">子模块</td>
                          <td style="width: 65%">
                            <table class="kj-table" frame="void">
                              <thead>
                              <tr>
                                <td style="width: 50%">二级子模块</td>
                                <td style="width: 50%">已拥有此权限者</td>
                              </tr>
                              </thead>
                            </table>
                          </td>
                        </tr>
                        </thead>
                      </table>
                    </td>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(item, index1) in menuData" :key="index1">
                    <td style="width: 25%"><el-checkbox :label="item.name" :value="item.mid" :disabled="item.disabled" :checked="item.checked" style="margin-left: 16px" @change="checked =>menuChange(item, checked)"></el-checkbox></td>
                    <td style="width: 75%">
                      <table class="kj-table" frame="void">
                        <template v-if="item.subPopdoms.length > 0">
                          <tr v-for="(it, index2) in item.subPopdoms" :key="index2">
                            <td style="width: 35%"><el-checkbox :label="it.name" :value="it.mid" :disabled="it.disabled" :checked="it.checked" style="margin-left: 16px" @change="checked => menuChange(it, checked)"></el-checkbox></td>
                            <td style="width: 65%">
                              <table class="kj-table" frame="void">
                                <template v-if="it.subPopdoms.length > 0">
                                  <tr v-for="(ite, index3) in it.subPopdoms" :key="index3">
                                    <td style="width: 50%"><el-checkbox :label="ite.name" :value="ite.mid" :disabled="ite.disabled" :ite="ite.checked" style="margin-left: 16px" @change="checked =>menuChange(ite, checked)"></el-checkbox></td>
                                    <td style="width: 50%"><el-text style="margin-left: 16px">{{getUserStr(ite.users)}}</el-text></td>
                                  </tr>
                                </template>
                                <template v-else>
                                  <tr>
                                    <td style="width: 50%"></td>
                                    <td style="width: 50%"><el-text style="margin-left: 16px">{{getUserStr(it.users)}}</el-text></td>
                                  </tr>
                                </template>
                              </table>
                            </td>
                          </tr>
                        </template>
                        <template v-else>
                          <tr>
                            <td style="width: 35%"></td>
                            <td style="width: 65%">
                              <table class="kj-table" frame="void">
                                <tr>
                                  <td style="width: 50%"></td>
                                  <td style="width: 50%"><el-text style="margin-left: 16px">{{getUserStr(item.users)}}</el-text></td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </template>
                      </table>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </el-checkbox-group>
            </el-tab-pane>
            <el-tab-pane label="可分配给多人的权限" name="second">
              <el-text>
                请勾选需分配给{{form_editAuthority.cusInfo.userName}}的权限。
              </el-text>
              <el-checkbox-group v-model="checkList2" style="margin-top: 16px">
                <table class="kj-table kj-table-left">
                <thead>
                <tr>
                  <td style="width: 25%">模块</td>
                  <td style="width: 75%">
                    <table class="kj-table" frame="void">
                      <thead>
                      <tr>
                        <td style="width: 35%">子模块</td>
                        <td style="width: 65%">
                          <table class="kj-table" frame="void">
                            <thead>
                            <tr>
                              <td style="width: 50%">二级子模块</td>
                              <td style="width: 50%">已拥有此权限者</td>
                            </tr>
                            </thead>
                          </table>
                        </td>
                      </tr>
                      </thead>
                    </table>
                  </td>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item, index1) in menuData2" :key="index1">
                  <td style="width: 25%"><el-checkbox :label="item.name" :value="item.mid" :disabled="item.disabled" :checked="item.checked" style="margin-left: 16px" @change="checked =>menuChange2(item, checked)"></el-checkbox></td>
                  <td style="width: 75%">
                    <table class="kj-table" frame="void">
                      <template v-if="item.subPopdoms.length > 0">
                        <tr v-for="(it, index2) in item.subPopdoms" :key="index2">
                          <td style="width: 35%"><el-checkbox :label="it.name" :value="it.mid" :disabled="it.disabled" :checked="it.checked" style="margin-left: 16px" @change="checked => menuChange2(it, checked)"></el-checkbox></td>
                          <td style="width: 65%">
                            <table class="kj-table" frame="void">
                              <template v-if="it.subPopdoms.length > 0">
                                <tr v-for="(ite, index3) in it.subPopdoms" :key="index3">
                                  <td style="width: 50%"><el-checkbox :label="ite.name" :value="ite.mid" :disabled="ite.disabled" :ite="ite.checked" style="margin-left: 16px" @change="checked =>menuChange2(ite, checked)"></el-checkbox></td>
                                  <td style="width: 50%"><el-text style="margin-left: 16px">{{getUserStr(ite.users)}}</el-text></td>
                                </tr>
                              </template>
                              <template v-else>
                                <tr>
                                  <td style="width: 50%"></td>
                                  <td style="width: 50%"><el-text style="margin-left: 16px">{{getUserStr(it.users)}}</el-text></td>
                                </tr>
                              </template>
                            </table>
                          </td>
                        </tr>
                      </template>
                      <template v-else>
                        <tr>
                          <td style="width: 35%"></td>
                          <td style="width: 65%">
                            <table class="kj-table" frame="void">
                              <tr>
                                <td style="width: 50%"></td>
                                <td style="width: 50%"><el-text style="margin-left: 16px">{{getUserStr(item.users)}}</el-text></td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </template>
                    </table>
                  </td>
                </tr>
                </tbody>
              </table>
              </el-checkbox-group>
            </el-tab-pane>
          </el-tabs>
        </div>
      </template>
    </TyDialog>
    <div class="ty-container" id="home">
      <div class="page">
        <el-table :data="userList" border style="width: 100%; margin-top: 16px">
          <el-table-column label="姓名" prop="userName"></el-table-column>
          <el-table-column label="性别" prop="name">
            <template #default="scope">
              {{filter_gender(scope.row.gender)}}
            </template>
          </el-table-column>
          <el-table-column label="手机号" prop="mobile"></el-table-column>
          <el-table-column label="部门" prop="departName"></el-table-column>
          <el-table-column label="职位" prop="postName"></el-table-column>
          <el-table-column label="直接上级" prop="leaderName"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link :disabled="scope.row.roleCode === 'agent'" @click="permissionAssignment(scope.row)"><b>权限分配</b></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ty-container{
  margin-top:35px; padding:10px;
}
.page{
  padding: 8px 0;
  max-width: 1200px;
  position: relative;
  margin: 0 70px;
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
.text-center{
  text-align: center;
}
.kj-table thead td{
  text-align: center;
}
.kj-table td .text {
  padding: 0 10px;
  color: #666;
}
.kj-table{
  width: 100%;
  border-collapse: collapse;
  td{
    border: 1px solid #ebeef5;
    font-size: 14px;
    text-align: left;
    height: 40px;
    word-wrap: break-word;
    word-spacing: normal;
    word-break: break-all;
    color: #101010;
    padding: 0;
    label{
      margin: 0 8px;
      white-space: normal;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import auth from '@/sys/auth'
import * as api from "@/api/authority.js"
export default {
  data() {
    return {
      pageName: 'authority',
      pageShow: false,
      pageInfo:{ 'currentPageNo': 0, 'pageSize': 20 , 'totalPage': 0  },
      active: 'first',
      checkList: [],
      checkList2: [],
      userList: [],
      menuData: [],
      menuData2: [],
      dialog_visible_permissionAssignment: false,
    }
  },
  components: {},
  computed: {
    option_shelves() {
      return this.shelvesList.map(item => {
        return {
          label: item.shelfCode + '/' + item.shelfName,
          value: item.id,
          disabled: item.amount === item.usedAmount
        }
      })
    },
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'eb', name: '权限设置', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      this.getUserList()
    },
    getUserList() {
      api.getUserList()
      .then(res => {
        this.userList = res.data?.userList || []
      })
    },
    permissionAssignment(item) {
      this.dialog_visible_permissionAssignment = true
      this.form_editAuthority = {
        type: 0,
        cusInfo: item
      }
      this.active = 'first'
      let manageId = auth.getUserID()
      let userId = this.form_editAuthority.cusInfo.userID
      this.menuData = []
      this.menuData2 = []
      this.checkList = []
      this.checkList2 = []
      api.getOneMenu(manageId, userId)
          .then(res => {
            this.menuData = res.data.data
          })
      api.getAllMenu(manageId, userId)
          .then(res => {
            this.menuData2 = res.data.data
          })
    },
    menuChange2(item, checked) {
      let mid = item.mid
      // 基础逻辑

      let checkList = this.checkList2// 当前所有的勾选项 如下结构 ['aa', 'bb']

      // 权限管理的特殊设置 ：
      // 1.勾选“权限设置eb”或“审批设置ec”，
      //       则 “权限设置eb”、“审批设置ec”、“当前权限ed”、“职工权限ef” 四项都必选 ，
      //       且“当前权限”、“职工权限” 不能勾掉 ;
      //      “审批查看ee” 必不勾选
      // 2.勾选“当前权限ed” ，则 “审批查看ee” 必勾选
      // 3.文件夹管理rb表格管理rd 不能单独选择， 必须选中权限设置eb
      // 4.内容管理-目录aca 不能单独选择， 必须选中权限设置eb,内容管理acb受联动影响，但是不需要勾选权限设置


      if (checked) {
        if (mid !== 'ea') {
          // 权限管理下有互斥模块，所以不自动选中权限管理下的模块

          // 此段代码获取父节点子节点选中
          let pacNode = this.getParentNodeAndChildrenNode(item)
          checkList = [...checkList, ...pacNode]
          console.log('pacNode', pacNode)
        }
        // 处理权限的特殊情况（e开头的都是权限）
        if (mid === 'eb' || mid === 'ec') {
          if (checkList.includes('ee')) {
            this.$message({
              type: 'error',
              message: '审批设置 与 审批查看 不能同时选中！'
            })
            checkList = checkList.filter(item => item !== mid)
          } else {
            // 头两个其中一个选中，下方的所有模块自动选中
            checkList = [...checkList, ...['eb', 'ec', 'ed', 'ef', 'ra', 'rb', 'rd', 'md', 'ac', 'aca', 'acb']]
          }
        } else if (mid === 'ed') {
          checkList = [...checkList, ...['ee']]
        } else if (mid === 'ee') {
          if (checkList.includes('ec')) {
            this.$message({
              type: 'error',
              message: '审批设置 与 审批查看 不能同时选中！'
            })
            checkList = checkList.filter(item => item !== mid)
          }
        } else if (mid === 'rb' || mid === 'ra' || mid === 'rd') {
          if (!checkList.includes('eb')) {
            this.$message({
              type: 'error',
              message: '先选择权限设置才能设置此模块！'
            })
            let arr = ['rb', 'ra', 'rd']
            checkList = checkList.filter(item => !arr.includes(item));
          }
        } else if (mid === 'ac' || mid === 'aca' ||  mid === 'acb') {
          if (!checkList.includes('eb')) {
            this.$message({
              type: 'error',
              message: '先选择权限设置才能设置此模块！'
            })
            let arr = ['ac', 'aca', 'acb']
            checkList = checkList.filter(item => !arr.includes(item));
          }
        } else if (mid === 'kb' || mid === 'kj' || mid === 'kk') {
          let arr = ['kb', 'kj', 'kk']
          checkList = [...checkList, ...arr]
        } else if (mid === 'qb' || mid === 'qc' || mid === 'qd' || mid === 'qe' || mid === 'qg') {
          let arr = this.menuData2.find(item => item.mid === 'qa')?.subPopdoms.map(item => item.mid)
          checkList = [...checkList, ...arr, ...['qa']]
        }
      } else {
        if (mid === 'eb' || mid === 'ec') {
          let arr = ['eb', 'ec', 'ed', 'ef', 'ra', 'rb', 'rd', 'md', 'ac', 'aca', 'acb']
          checkList = checkList.filter(item => !arr.includes(item));
        } else if (mid === 'ed') {
          if (checkList.includes('eb')) {
            this.$message({
              type: 'error',
              message: '不可取消'
            })
            checkList = [...checkList, ...[mid]]
          } else {
            checkList = checkList.filter(item => item !== 'ee')
          }
        } else if (mid === 'ee') {
          if (checkList.includes('ed')) {
            this.$message({
              type: 'error',
              message: '不可取消'
            })
            checkList = [...checkList, ...[mid]]
          }
        } else if (mid === 'ef') {
          if (checkList.includes('eb') && checkList.includes('ec') && checkList.includes('ed')) {
            this.$message({
              type: 'error',
              message: '不可取消'
            })
            checkList = [...checkList, ...[mid]]
          }
        } else if (mid === 'kb' || mid === 'kj' || mid === 'kk') {
          let arr = ['kb', 'kj', 'kk']
          checkList = checkList.filter(item => !arr.includes(item));
        } else if (mid === 'qb' || mid === 'qc' || mid === 'qd' || mid === 'qe' || mid === 'qg') {
          let arr = this.menuData2.find(item => item.mid === 'qa')?.subPopdoms.map(item => item.mid)
          console.log('arrrrr', arr)
          checkList = checkList.filter(item => ![...arr, ...['qa']].includes(item));
        }
      }
      this.checkList2 = Array.from(new Set(checkList))
    },
    menuChange(item,) {
      let checkList = this.checkList // 当前所有的勾选项 如下结构 ['aa', 'bb']
      let pacNode = this.getParentNodeAndChildrenNode(item)
      checkList = [...checkList, ...pacNode]
      this.checkList = Array.from(new Set(checkList))
    },
    getParentNodeAndChildrenNode(item) {
      let menu = this.menuData
      let arrMenu = this.treeToArr(menu)
      let parent =  this.getParent(arrMenu, item.pid)
      let children =  this.getChildren(item)
      return [...parent, ...children]
    },
    treeToArr(data, pid = null, res = [] ) {
      data.forEach(item => {
        res.push({pid: item.pid, mid: item.mid, name: item.name})
        if (item.subPopdoms.length > 0) {
          this.treeToArr(item.subPopdoms, item.mid, res)
        }
      })
      return res
    },
    getParent(data, pid, res = []) {
      data.forEach(item => {
        if (item.mid === pid) {
          res.push(item.mid)
          this.getParent(data, item.pid, res)
        }
      })
      return res
    },
    getChildren(data, res = []) {
      if (data.subPopdoms.length > 0) {
        let sub = data.subPopdoms
        sub.forEach(item => {
          res.push(item.mid)
          this.getChildren(item, res)
        })
      }
      return res
    },
    permissionAssignment_submit() {
      const userId = this.form_editAuthority.cusInfo.userID
      const checkList = [...this.checkList, ...this.checkList2]
      let midStr = checkList.join(',')
      api.saveUserPopedom(userId, midStr)
          .then(res => {
            let status = res.data.status
            if (status === 1) {
              this.$message({
                type: 'success',
                message: '保存成功！'
              })
              this.dialog_visible_permissionAssignment = false
            } else if (status === 0){
              this.$message({
                type: 'error',
                message: '保存失败，请重试！'
              })
            } else {
              this.$message({
                type: 'error',
                message: '系统错误！'
              })
            }
          })
    },
    filter_gender (gender) {
      switch (gender){
        case '0' :
          return '女'
          break
        case '1' :
          return '男'
          break
        default:
          return ''
      }
    },
    getUserStr(users) {
      let newUsers = users.map(item => item.userName)
      return newUsers.join('、')
    },
    dialogHide() {
      this.dialog_visible_permissionAssignment = false
    }
  }
}
</script>
