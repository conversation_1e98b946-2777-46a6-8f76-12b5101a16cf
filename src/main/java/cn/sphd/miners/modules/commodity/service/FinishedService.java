package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface FinishedService {
    //获取未录入初始库存的数据
    Map<String,Object> getNotEnteredList(Integer oid, Integer currPage, Integer pageSize);
    //获取未录入初始库存的数据
    List<Map<String,Object>>  getNotEnteredList(Integer oid);
    //录入初始库存
    String addInitialStock(Integer productId,String locationList,User user);
    //修改初始库存
    String updateInitialStock(Integer id, BigDecimal num, User user);
    //成品列表
    Map<String,Object> getPdProductList(Integer oid, String type,Integer categoryId,String param,Integer currPage, Integer pageSize);
    //获取暂停成品列表
    Map<String,Object> getSuspendPdProductList(Integer oid, Integer currPage, Integer pageSize);
    //成品列表
    List<Map<String,Object>> getPdProductList(Integer oid);
    //获取初始库存修改记录
    List<Map<String,Object> >  getInitialStockRecord(Integer id);

    //获取最低库存修改记录
    List<Map<String,Object> >  getMinimumStockRecord(Integer id);
    //获取库位列表
    Map<String,Object> getLocationList(Integer oid);

    //查看库位详情
    Map<String, Object> getLocationDetail(Integer locationId);

    //查看商品占位情况
    Map<String,Object>  getLocationListByProduct(Integer id);

    //修改库位
    String updateLocation(Integer productId,String locationList,User user);
    //搜索
    Map<String,Object> search(Integer oid, String param,Integer currPage, Integer pageSize);
    //根据分类获取字分类
    List<Map<String,Object>> getMtCategory(Integer id,Integer oid);
    //仓库总计
    Map<String,Object> totalWarehouse(Integer oid);

}
