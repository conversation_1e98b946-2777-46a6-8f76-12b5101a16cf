package cn.sphd.miners.modules.forumArea.entity;

/**
 * <AUTHOR>
 * @create 2018-02-1
 * @description
 **/

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity(name="ForumPostAttachmentHistory")
@Table(name = "t_forum_post_attachment_history")
public class ForumPostAttachmentHistory implements Serializable {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "org")
    private Integer org;
    @Column(name = "post")
    private Integer post;
    @Column(name = "post_history")
    private Integer postHistory;
    @Column(name = "post_attachment")
    private Integer postAttachment;
    @Column(name = "title")
    private String title;
    @Column(name = "type")
    private String type;
    @Column(name = "description")
    private String description;
    @Column(name = "path")
    private String path;
    @Column(name = "size")
    private Integer size;
    @Column(name = "orders")
    private Integer orders;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "operation")
    private String operation;
    @Column(name = "previous_id")
    private Integer previousId;
    @Column(name = "version_no")
    private Integer versionNo;
    @Column(name = "place")
    private String place;
    @Column(name = "location")
    private String location;
  /**
   * 内部文件资源ID
   */
  @Column(name = "resource_id", nullable=true , unique=false )
  private Integer resourceId;

  /**
   * 内部文件资源版本
   */
  @Column(name = "resource_version", length=50, nullable=true , unique=false )
  private String resourceVersion;

  /**
   * 内部文件资源历史ID
   */
  @Column(name = "resource_history", nullable=true , unique=false )
  private Integer resourceHistory;

    public Integer getId() {
      return id;
    }

    public void setId(Integer id) {
      this.id = id;
    }

    public Integer getOrg() {
      return org;
    }

    public void setOrg(Integer org) {
      this.org = org;
    }

    public Integer getPost() {
      return post;
    }

    public void setPost(Integer post) {
      this.post = post;
    }

    public Integer getPostHistory() {
      return postHistory;
    }

    public void setPostHistory(Integer postHistory) {
      this.postHistory = postHistory;
    }

    public Integer getPostAttachment() {
      return postAttachment;
    }

    public void setPostAttachment(Integer postAttachment) {
      this.postAttachment = postAttachment;
    }

    public String getTitle() {
      return title;
    }

    public void setTitle(String title) {
      this.title = title;
    }

    public String getType() {
      return type;
    }

    public void setType(String type) {
      this.type = type;
    }

    public String getDescription() {
      return description;
    }

    public void setDescription(String description) {
      this.description = description;
    }

    public String getPath() {
      return path;
    }

    public void setPath(String path) {
      this.path = path;
    }

    public Integer getSize() {
      return size;
    }

    public void setSize(Integer size) {
      this.size = size;
    }

    public Integer getOrders() {
      return orders;
    }

    public void setOrders(Integer orders) {
      this.orders = orders;
    }

    public String getMemo() {
      return memo;
    }

    public void setMemo(String memo) {
      this.memo = memo;
    }

    public Integer getCreator() {
      return creator;
    }

    public void setCreator(Integer creator) {
      this.creator = creator;
    }

    public String getCreateName() {
      return createName;
    }

    public void setCreateName(String createName) {
      this.createName = createName;
    }

    public Date getCreateTime() {
      return createTime;
    }

    public void setCreateTime(Date createTime) {
      this.createTime = createTime;
    }

    public Integer getUpdator() {
      return updator;
    }

    public void setUpdator(Integer updator) {
      this.updator = updator;
    }

    public String getUpdateName() {
      return updateName;
    }

    public void setUpdateName(String updateName) {
      this.updateName = updateName;
    }

    public Date getUpdateTime() {
      return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
      this.updateTime = updateTime;
    }

    public String getOperation() {
      return operation;
    }

    public void setOperation(String operation) {
      this.operation = operation;
    }

    public Integer getPreviousId() {
      return previousId;
    }

    public void setPreviousId(Integer previousId) {
      this.previousId = previousId;
    }

    public Integer getVersionNo() {
      return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
      this.versionNo = versionNo;
    }

    public String getPlace() {
      return place;
    }

    public void setPlace(String place) {
      this.place = place;
    }

    public String getLocation() {
      return location;
    }

    public void setLocation(String location) {
      this.location = location;
    }

  public Integer getResourceId() {
    return resourceId;
  }

  public void setResourceId(Integer resourceId) {
    this.resourceId = resourceId;
  }

  public String getResourceVersion() {
    return resourceVersion;
  }

  public void setResourceVersion(String resourceVersion) {
    this.resourceVersion = resourceVersion;
  }

  public Integer getResourceHistory() {
    return resourceHistory;
  }

  public void setResourceHistory(Integer resourceHistory) {
    this.resourceHistory = resourceHistory;
  }
}
