package cn.sphd.miners.modules.equipment.controller;

import java.util.List;
import java.util.Map;

import cn.sphd.miners.modules.equipment.entity.TEquEquipmentHistory;
import cn.sphd.miners.modules.equipment.service.ITEquEquipmentHistoryService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_装备器具历史
 * 20230209 1.247装备器具新增
 * Controller
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment/history")
public class EquEquipmentHistoryController {

    @Autowired
    private ITEquEquipmentHistoryService tEquEquipmentHistoryService;


    /**
     * 查询装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     * 列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TEquEquipmentHistory tEquEquipmentHistory) {
        List<TEquEquipmentHistory> list = tEquEquipmentHistoryService.selectTEquEquipmentHistoryList(tEquEquipmentHistory);
        return list;
    }


    /**
     * 新增保存装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquEquipmentHistory tEquEquipmentHistory) {
        return toAjax(tEquEquipmentHistoryService.insertTEquEquipmentHistory(tEquEquipmentHistory));
    }

    /**
     * 修改保存装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquEquipmentHistory tEquEquipmentHistory) {
        return toAjax(tEquEquipmentHistoryService.updateTEquEquipmentHistory(tEquEquipmentHistory));
    }

    /**
     * 删除装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquEquipmentHistoryService.deleteTEquEquipmentHistoryByIds(ids));
    }

    @PostMapping("details")
    @ResponseBody
    public List<Map<String, Object>> history(Long id){
        return tEquEquipmentHistoryService.history(id);
    }
}
