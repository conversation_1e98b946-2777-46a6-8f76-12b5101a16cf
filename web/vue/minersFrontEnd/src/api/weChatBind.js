
import {request} from "@/utils/request";

/*
 * creator: 2024-6-19  hxz
 *   验证 验证码是否正确
 * params:
*/
export function checkBindingWX(url,data) {
    return request({
        url: url,
        data: data
    })
}


/*
 * creator: 2024-6-19  hxz
 *   获取短信验证码 （绑定微信， 更换绑定）
 * params:
*/
export function getWxCodeFun(url,data) {
    return request({
        url: url,
        data: data
    })
}

/*
 * creator: 2024-6-19  hxz
 *   绑定微信
 * params:
*/
export function tpCodeBindAcc(data) {
    return request({
        url: '/tp/tpCodeBindAcc.do',
        data: data
    })
}

/*
 * creator: 2024-6-19  hxz
 *   flushWxMember
 * params:
*/
export function getOrGroupByAcc(data) {
    return request({
        url: '/tp/getOrGroupByAcc.do',
        data: data
    })
}


/*
 * creator: 2024-6-19  hxz
 *   查看绑定 记录
 * params:
*/
export function tpBindAccHistories() {
    return request({
        url: '/tp/tpBindAccHistories.do',
    })
}


/*
 * creator: 2024-6-19  hxz
 *   解除绑定
 * params:
*/
export function tpUnBindAcc(data) {
    return request({
        url: '/tp/tpUnBindAcc.do',
        data
    })
}





