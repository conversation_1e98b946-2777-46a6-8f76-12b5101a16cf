package cn.sphd.miners.modules.thirdPlatform.utils;

import cn.sphd.miners.common.utils.AesUtils;
import cn.sphd.miners.common.utils.DigestUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 微信加解密包，微信加解密特殊的部分
 * @date Create at 2022/6/20 16:02
**/
public class WXBizMsgCrypt {
    protected static final long serialVersionUID = 1L;
    String password;

    public WXBizMsgCrypt() {
    }

    public WXBizMsgCrypt(String edKey) {
        this.setPassword(edKey);
    }

    public void setPassword(String edKey) {
        this.password = edKey + "=";
    }

    public boolean verifyMsgSignature(String verifToken, String timeStamp, String nonce, String encrypt, String msgSignature) {
        String[] array = new String[] { verifToken, timeStamp, nonce, encrypt };
        StringBuffer sb = new StringBuffer();
        Arrays.sort(array);
        for (int i = 0; i < 4; i++) {
            sb.append(array[i]);
        }
        String digest = DigestUtils.sha1(sb.toString());
        return digest.equalsIgnoreCase(msgSignature);
    }

    public Map<String, String> decrypt(String text) {
        if(StringUtils.isNotEmpty(password)) {
            AesUtils aes = new AesUtils(password, "AES/CBC/PKCS7Padding", AesUtils.Mode.decode);
            byte[] decrypt = aes.decrypt(Base64.decodeBase64(text));
            byte[] networkOrder = Arrays.copyOfRange(decrypt, 16, 20);
            int xmlLength = recoverNetworkBytesOrder(networkOrder);
            String xml = new String(Arrays.copyOfRange(decrypt, 20, 20 + xmlLength), StandardCharsets.UTF_8);
            String appId = new String(Arrays.copyOfRange(decrypt, 20 + xmlLength, decrypt.length), StandardCharsets.UTF_8);
            Map<String, String> result = new HashMap<>(2);
            result.put("xml", xml);
            result.put("appId", appId);
            return result;
        } else {
            return null;
        }
    }
    private int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        for (int i = 0; i < 4; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }
}
