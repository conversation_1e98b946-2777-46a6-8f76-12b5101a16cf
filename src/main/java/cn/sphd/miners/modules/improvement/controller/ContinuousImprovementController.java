package cn.sphd.miners.modules.improvement.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.generalAffairs.controller.CoreSettingController;
import cn.sphd.miners.modules.improvement.entity.TCiBase;
import cn.sphd.miners.modules.improvement.service.CiBaseService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2018/5/21.
 */
@Controller
@RequestMapping("/improvement")
public class ContinuousImprovementController {

    @Autowired
    CiBaseService ciBaseService;
    @Autowired
    CoreSettingController coreSettingController;

    //新增持续改进立案
    @ResponseBody
    @RequestMapping("/insertImprovementCase.do")
    public JsonResult insertImprovementCase(User user, TCiBase tCiBase){
        if(tCiBase.getStartTimeFind() != null){
//            tCiBase.setBeginTime(Tools.str2Date(tCiBase.getStartTimeFind()+ " 00:00:00"));
            tCiBase.setBeginTime(NewDateUtils.dateFromString(tCiBase.getStartTimeFind(), "yyyy-MM-dd"));
        }
        if (tCiBase.getEndTimeFind() != null) {
//            tCiBase.setEndTime(Tools.str2Date(tCiBase.getEndTimeFind()+ " 00:00:00"));
            tCiBase.setEndTime(NewDateUtils.dateFromString(tCiBase.getEndTimeFind(), "yyyy-MM-dd"));
        }
        ciBaseService.addImprovement(user, tCiBase);
        return new JsonResult(1,"新增成功");
    }

    //获取案子
    @ResponseBody
    @RequestMapping("/getImprovementCase.do")
    public void getImprovementCase(User user, HttpServletResponse response, TCiBase tCiBase, Integer role)throws IOException{
        List<TCiBase> listImprovement = ciBaseService.getCiBase(user, tCiBase, role, null);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listImprovement",listImprovement);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    //批准或者驳回持续改进立案
    @ResponseBody
    @RequestMapping("/approvalImprovementCase.do")
    public JsonResult approvalImprovementCase(User user, TCiBase tCiBase){
        ciBaseService.updateCiBase(user, tCiBase);
        return new JsonResult(1,"成功");
    }

    //核心人物直接结案
    @ResponseBody
    @RequestMapping("/centreRoleSettle.do")
    public JsonResult centreRoleSettle(User user, TCiBase tCiBase){
        ciBaseService.centreSettleCase(user, tCiBase);
        return new JsonResult(1,"成功");
    }

    //查看案子
    @ResponseBody
    @RequestMapping("/showCaseMessage.do")
    public void showCaseMessage(User user, HttpServletResponse response, Integer id, Integer role)throws IOException {
        HashMap map = ciBaseService.getCiBaseMessage(id, role, user.getUserID());
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    //根据时间获取结案的案子
    @ResponseBody
    @RequestMapping("/getSettleCase.do")
    public void getSettleCase(User user, HttpServletResponse response, Integer buttenType, Integer role,
                              TCiBase tCiBase, PageInfo pageInfo) throws IOException{
        Date dateBegin;
        Date dateEnd;
        if (buttenType == 1) {
            dateBegin = NewDateUtils.getNewYearsDay();
            dateEnd = new Date();
        }else if(buttenType == 2){
            dateBegin = NewDateUtils.getNewYearsDay(NewDateUtils.changeYear(new Date(),-1));
            dateEnd = NewDateUtils.getLastTimeOfYear(dateBegin);
        }else if(buttenType == 3){
            dateBegin = NewDateUtils.getNewYearsDay(NewDateUtils.changeYear(new Date(),-2));
            dateEnd = NewDateUtils.getLastTimeOfYear(dateBegin);
        }else {
            dateBegin = NewDateUtils.dateFromString(tCiBase.getStartTimeFind(), "yyyy-MM-dd HH:mm:ss");
            dateEnd = NewDateUtils.dateFromString(tCiBase.getEndTimeFind(), "yyyy-MM-dd HH:mm:ss");
        }
        tCiBase.setSettleApproveBegin(dateBegin);
        tCiBase.setSettleApproveEnd(dateEnd);
        List<TCiBase> listBase = ciBaseService.getCiBase(user, tCiBase, role, pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listBase", listBase);
        map.put("timeBegin", tCiBase.getSettleApproveBegin());
        map.put("timeEnd", tCiBase.getSettleApproveEnd());
        map.put("pageInfo", pageInfo);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    //修改立案人
    @ResponseBody
    @RequestMapping("/upCaseCreator.do")
    public void upCaseCreator(Integer id, User user, String userName, HttpServletResponse response) throws IOException {
        HashMap map = ciBaseService.upCreator(id, user.getUserID(), userName);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    //跳转页面的接口
    @RequestMapping("/integrateManage.do")
    public String integrateManage(User user, Model model ){
        model.addAttribute("user",user);
        return "/continualImprove/integrateManage";
    }

    @RequestMapping("/improvementFiling.do")
    public String improvementFiling(User user, Model model ){
        model.addAttribute("user",user);
        return "/continualImprove/improvementFiling";
    }

}
