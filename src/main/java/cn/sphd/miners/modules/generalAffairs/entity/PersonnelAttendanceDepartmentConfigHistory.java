package cn.sphd.miners.modules.generalAffairs.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2018/5/7.
 */
@Entity
@Table(name = "t_personnel_attendance_department_config_history")
public class PersonnelAttendanceDepartmentConfigHistory implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="dept"   , nullable=true , unique=false)
    private Integer dept;//部门id 0-其他(没有部门的人员)

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构id

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="record"   , nullable=true , unique=false)
    private Integer record;//修改记录ID

    @Column(name="previous_config"   , nullable=true , unique=false)
    private Integer previousConfig;//记录修改前设置ID

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="rule", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelAttendanceConfigHistory rule;//考勤设置历史表

    @Column(name="rule"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer ruleId;//规则id

    @Transient
    private String deptName; //部门名称

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDept() {
        return dept;
    }

    public void setDept(Integer dept) {
        this.dept = dept;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public PersonnelAttendanceConfigHistory getRule() {
        return rule;
    }

    public void setRule(PersonnelAttendanceConfigHistory rule) {
        this.rule = rule;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getRecord() {
        return record;
    }

    public void setRecord(Integer record) {
        this.record = record;
    }

    public Integer getPreviousConfig() {
        return previousConfig;
    }

    public void setPreviousConfig(Integer previousConfig) {
        this.previousConfig = previousConfig;
    }
}
