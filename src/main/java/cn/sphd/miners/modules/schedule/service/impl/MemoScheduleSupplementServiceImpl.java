package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.modules.resourceAuthority.service.impl.ResUsing;
import cn.sphd.miners.modules.schedule.dao.MemoJobDao;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleSupplementDao;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleSupplementImageDao;
import cn.sphd.miners.modules.schedule.entity.MemoJob;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleSupplement;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleSupplementImage;
import cn.sphd.miners.modules.schedule.service.MemoScheduleSupplementService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/14.
 */
@Service("memoScheduleSupplementService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoScheduleSupplementServiceImpl implements MemoScheduleSupplementService {
    @Autowired
    MemoScheduleSupplementDao memoScheduleSupplementDao;
    @Autowired
    MemoJobDao memoJobDao;
    @Autowired
    MemoScheduleSupplementImageDao scheduleSupplementImageDao;
    @Autowired
    UploadService uploadService;

    @Override
    public List<MemoScheduleSupplement> getMemoScheduleSupplementListBySid(Integer scheduleId) {
        String hql=" from MemoScheduleSupplement where scheduleId=:scheduleId";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        List<MemoScheduleSupplement> memoScheduleSupplementList=memoScheduleSupplementDao.getListByHQLWithNamedParams(hql,params);
        return memoScheduleSupplementList;
    }

    @Override
    public boolean addMemoScheduleSupplement(User user, Integer memoJobId, String description, String[] imagePaths) {
        MemoJob memoJob=memoJobDao.get(memoJobId);
        MemoScheduleSupplement memoScheduleSupplement=new MemoScheduleSupplement();
        memoScheduleSupplement.setCreateName(user.getUserName());
        memoScheduleSupplement.setCreator(user.getCreator());
        memoScheduleSupplement.setCreateDate(new Date());
        memoScheduleSupplement.setUpdator(user.getUserID());
        memoScheduleSupplement.setUpdateName(user.getUserName());
        memoScheduleSupplement.setUpdateDate(new Date());
        memoScheduleSupplement.setDescription(description);
        memoScheduleSupplement.setSchedule(memoJob.getSchedule());
        memoScheduleSupplement.setMemoJobId(memoJobId);
        memoScheduleSupplementDao.save(memoScheduleSupplement);
        Integer orders=1;
        for (String imagePath:imagePaths){
            MemoScheduleSupplementImage memoScheduleSupplementImage=new MemoScheduleSupplementImage();
            memoScheduleSupplementImage.setScheduleId(memoJob.getScheduleId());
            memoScheduleSupplementImage.setScheduleSupplement(memoScheduleSupplement);
            memoScheduleSupplementImage.setDescription(description);
            memoScheduleSupplementImage.setCreateName(user.getUserName());
            memoScheduleSupplementImage.setCreator(user.getCreator());
            memoScheduleSupplementImage.setCreateDate(new Date());
            memoScheduleSupplementImage.setUpdator(user.getUserID());
            memoScheduleSupplementImage.setUpdateName(user.getUserName());
            memoScheduleSupplementImage.setUpdateDate(new Date());
            memoScheduleSupplementImage.setNormal(imagePath);
            scheduleSupplementImageDao.save(memoScheduleSupplementImage);

            String name=memoJob.getTitle()+"(补充记录)";
            if (imagePaths.length>1){ //多个文件
                name=name+orders;
            }
            uploadService.addFileUsing(new MemoUsing(memoScheduleSupplementImage.getId(), memoScheduleSupplementImage.getClass()), memoScheduleSupplementImage.getNormal(), name, user, "备忘与日程"); // 补充记录附件加的附件引用
            orders++;
        }
        memoJob.setSupplement(memoJob.getSupplement()+description);//提醒中拼上 补充内容，方便筛选
        return true;
    }

    @Override
    public boolean deleteMemoScheduleSupplement(Integer supplementId) {
        memoScheduleSupplementDao.deleteById(supplementId);
        return true;
    }

    @Override
    public List<MemoScheduleSupplement> getMemoScheduleSupplementListByJobId(Integer memoJobId) {
        String hql=" from MemoScheduleSupplement where memoJobId=:memoJobId";
        Map<String,Object> params=new HashMap<>();
        params.put("memoJobId",memoJobId);
        List<MemoScheduleSupplement> memoScheduleSupplementList=memoScheduleSupplementDao.getListByHQLWithNamedParams(hql,params);
        return memoScheduleSupplementList;
    }

    @Override
    public MemoScheduleSupplement getMemoScheduleSupplementById(Integer id) {
        return memoScheduleSupplementDao.get(id);
    }

    @Override
    public MemoScheduleSupplementImage getMemoScheduleSupplementImageById(Integer id) {
        return scheduleSupplementImageDao.get(id);
    }
}
