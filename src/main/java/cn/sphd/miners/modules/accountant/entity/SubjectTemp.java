package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import java.math.BigDecimal;

/**
 * Created by root on 17-1-11.
 */
@Alias("subjectTemp")
public class SubjectTemp {

    private String subject;
    private BigDecimal amount;
    private String name;
    private String subjectNames;
    private int id;
    private double creditQuantity;
    private double debitQuantity;
    private BigDecimal unitPrice;
    private String measureUnit;
    private String quantityAssistingAccounting;
    private Integer reimburseBill;//个人报销的票据id
    private Integer reimburseBillItem;//个人报销的票据详情id
    private String memo;//个人报销的备注

    public Integer getReimburseBill() {
        return reimburseBill;
    }

    public void setReimburseBill(Integer reimburseBill) {
        this.reimburseBill = reimburseBill;
    }

    public Integer getReimburseBillItem() {
        return reimburseBillItem;
    }

    public void setReimburseBillItem(Integer reimburseBillItem) {
        this.reimburseBillItem = reimburseBillItem;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getQuantityAssistingAccounting() {
        return quantityAssistingAccounting;
    }

    public void setQuantityAssistingAccounting(String quantityAssistingAccounting) {
        this.quantityAssistingAccounting = quantityAssistingAccounting;
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }


    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSubjectNames() {
        return subjectNames;
    }

    public void setSubjectNames(String subjectNames) {
        this.subjectNames = subjectNames;
    }

    public double getCreditQuantity() {
        return creditQuantity;
    }

    public void setCreditQuantity(double creditQuantity) {
        this.creditQuantity = creditQuantity;
    }

    public double getDebitQuantity() {
        return debitQuantity;
    }

    public void setDebitQuantity(double debitQuantity) {
        this.debitQuantity = debitQuantity;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
