<template>
  <div id="discussionBorrowQuery" class="reimburseQuery">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，得到相应的查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approveStatus" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div v-if="eventType > 1">
              <h4 class="item-header">申请人</h4>
              <div class="el-form-item">
                <span :class="queryForm.applier === 0?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <div class="handle_button" style="margin-top: 20px">
              <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit" v-loading.fullscreen.lock="loading">
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>
<style>
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  #reimburseApproveQuery .el-radio__input, #reimburseApplyQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
</style>

<script>
export default {
  name: 'discussionBorrowQuery',
  data () {
    return {
      loading: true,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approveStatus: '2',
        applier: 0,
        applyName: '选择申请人'
      },
      roleList: [],
      bookVisible: false,
      eventType: 0,
      title: '讨论组阅览申请'
    }
  },
  created: function () {
    let that = this
    let name = this.$route.params.name
    console.log('name', name)
    this.chargeName(name)

    this.$http.post('../../../res/getAllOidUserByResource.do', { userID: this.sphdSocket.user.userID }, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body
      if (data) {
        let data = response.data
        let list = data.data.list
        that.roleList = list
      } else {
        console.log('加载失败！')
      }
    }).catch(function () {
      that.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  },
  methods: {
    chargeName: function (name) {
      switch (name) {
        case 'apply':
          this.eventType = 1
          this.title = '讨论组阅览申请'
          break
        case 'midApprove':
          this.eventType = 2
          this.title = '讨论组阅览申请'
          break
        case 'lastApprove':
          this.eventType = 3
          this.title = '讨论组阅览审批'
          break
      }
    },
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        oid: this.sphdSocket.user.oid,
        userID: this.sphdSocket.user.userID,
        eventType: this.eventType,
        type: this.queryForm.type,
        timeBegin: this.queryForm.beginDate,
        timeEnd: this.queryForm.endDate,
        approveStatus: this.queryForm.approveStatus
      }
      if (this.eventType > 1) {
        if (this.queryForm.applier) {
          queryParam.fromUser = this.queryForm.applier
          queryParam.fromUserName = this.queryForm.applyName
        }
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/discussionBorrowQueryPer',
        name: 'discussionBorrowQueryPer',
        params: {
          data: queryParam
        }
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.applier = 0
        this.queryForm.applyName = '请选择申请人'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.applier = data.userID
      this.queryForm.applyName = data.userName
    }
  }
}
</script>
