package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.SubjectTemp;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucherSubject;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
@Component
public interface VoucherSubjectMapper extends BaseMapper<TAccountantVoucherSubject> {

    /*得到某凭证的科目金额*/
    List<TAccountantVoucherSubject> getSubjectByDirection(TAccountantVoucherSubject param);

    /*根据凭证id得到借贷科目*/
    List<TAccountantVoucherSubject> getSubjectByVoucher(TAccountantVoucherSubject param);

    /*只更新原科目表的科目编号*/
    int updateSubjectNo(TAccountantVoucherSubject param);

    /*得到凭证所对应的科目代码、名称和金额*/
    List<SubjectTemp> getSubjectInfo(QueryData qd);

    /*删除原凭证科目表*/
    int deleteSubject(TAccountantVoucherSubject param);

    /*查看此科目是否在凭证科目表中被使用*/
    List<TAccountantVoucherSubject> listByVoucherSubject(TAccountantVoucherSubject v);

    /*查看这些科目是否在凭证科目表中被使用*/
    List<TAccountantVoucherSubject> listByVoucherSubjectes(HashMap map);

    //得到凭证表中本月财务录入的有关利润表的科目
    List<TAccountantVoucherSubject> listByProfitSubject(TAccountantVoucherSubject v);

    //根据凭证ID和方向来获取某个方向的凭证科目
    List<TAccountantVoucherSubject> listByVoucherDirection(TAccountantVoucherSubject v);

    //根据期数获取这一期的所有科目
    List<TAccountantVoucherSubject> listByVoucherProfit(TAccountantVoucherSubject v);

    List<TAccountantVoucherSubject> getSubjectByAmount(TAccountantVoucherSubject param);

}