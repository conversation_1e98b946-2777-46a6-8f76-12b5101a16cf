package cn.sphd.miners.modules.finance.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2020/09/28
 * 2.68/2.69薪资宝转出
 */
@Entity
@Table(name = "t_finance_payment_history")
public class FinancePaymentHistory implements Serializable{

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "finance_payment" ,insertable = true)
    private Integer financePayment;//支出表ID

    @Column(name = "org" ,insertable = true)
    private Integer org;//机构id

    @Column(name = "business_type",length = 20,nullable = true,unique = false)
    private String businessType;//业务类型:reimburse-报销,treasure薪资宝,procurement-采购票款',prepayment-预付款(1.229采购之预付款),overpayment-多收来的款(1.233差额处理2)

    @Column(name = "business" ,insertable = true)
    private Integer business;//业务ID

    @Column(name = "business_no",nullable = true,unique = false)
    private Integer businessNo;//业务号(如薪薪宝的业务流水号)

    @Column(name = "instance" ,insertable = true)
    private Integer instance;//申请实例ID

    @Column(name = "instance_chain",length = 255,nullable = true,unique = false)
    private String instanceChain;//申请实例ID链,以逗号分隔

    @Column(name = "process",nullable = true,unique = false)
    private Integer process;//票据审批过程ID

    @Column(name = "purpose",length = 255,nullable = true,unique = false)
    private String purpose;//理由

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;   //申请金额

    @Column(name = "method",length = 1,nullable = true,unique = false)
    private String method;//支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐

    @Column(name = "account_id " ,insertable = true)
    private Integer  accountId ;//帐户ID

    @Column(name = "account_detail " ,insertable = true)
    private Integer  accountDetail ;//帐户明细ID

    @Column(name = "account_bill " ,insertable = true)
    private Integer  accountBill ;//帐户票据ID

    @Column(name = "account_bill_history " ,insertable = true)
    private Integer  accountBillHistory ;//帐户票据历史ID(查询票据历史的时候使用)

    @Column(name="plan_date"   , nullable=true , unique=false)
    private Date planDate;//计划时间

    @Column(name="plan_amount"   , nullable=true , unique=false)
    private BigDecimal planAmount;   //计划金额

    @Column(name = "plan_account",length = 255,nullable = true,unique = false)
    private String planAccount;//'计划银行帐号'

    @Column(name = "plan_method",length = 1,nullable = true,unique = false)
    private String planMethod;//计划支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐

    @Column(name="fact_date"   , nullable=true , unique=false)
    private Date factDate;//实际时间

    @Column(name="fact_amount"   , nullable=true , unique=false)
    private BigDecimal factAmount;   //实际金额

    @Column(name = "fact_account",length = 255,nullable = true,unique = false)
    private String factAccount;//'实际银行帐号'

    @Column(name = "fact_method",length = 1,nullable = true,unique = false)
    private String factMethod;//实际支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐

    @Column(name = "summary",length = 255,nullable = true,unique = false)
    private String summary;//'摘要

    @Column(name="status"  , length=1 , nullable=true , unique=false)
    private String status;  //状态:0-撤回,1-待申请,2-申请,3-申请通过,4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成

    @Column(name = "memo",length = 255,nullable = true,unique = false)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;  //'创建人id'

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;   //'创建人'

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;   //'创建时间'

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;   //'修改人id'

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;   //'修改人'

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;    //'修改时间'

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作:1-增,2-删,3-修改 4-调整数量

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    //1.231差额处理(1)添加--2022/11/15
    @Column(name="audit_instance"  , nullable=true , unique=false)
    private Integer auditInstance;   //付款复核实例ID

    @Column(name="audit_instance_chain"  , length=255 , nullable=true , unique=false)
    private String auditInstanceChain ;   //付款复核实例链

    @Column(name="audit_process"  , nullable=true , unique=false)
    private Integer auditProcess;   //付款复核过程ID

    @Column(name="audit_state"  , length=1 , nullable=true , unique=false)
    private String auditState ;   //付款复核状态:0-暂存,1-录入,2-提交,3-复核通过,4-复核驳回

    @Column(name="pay_reject_reason"  , length=50 , nullable=true , unique=false)
    private String payRejectReason ;   //驳回原因:1-该供应商提供的产品或服务有问题;2-公司资金紧张,过些日子才能付款;3-没必要提前付款;4-其他原因

    @Column(name="pay_reject_desc"  , length=255 , nullable=true , unique=false)
    private String payRejectDesc ;   //驳回原因说明

    @Column(name="audit_reject_reason"  , length=50 , nullable=true , unique=false)
    private String auditRejectReason ;   //复核驳回原因(多选以逗号分隔):1-该供应商提供的产品或服务有问题;2-公司资金紧张,过些日子才能付款;3-没必要提前付款;4-其他原因

    @Column(name="audit_reject_desc"  , length=255 , nullable=true , unique=false)
    private String auditRejectDesc ;   //复核驳回原因说明

    @Column(name="teminate_state"  , length=1 , nullable=true , unique=false)
    private String teminateState ;   //终止状态,0-未终止(默认),1-已终止

    @Column(name="teminate_time"   , nullable=true , unique=false)
    private Date teminateTime;  //终止时间

    @Column(name="teminate_reason"  , length=255 , nullable=true , unique=false)
    private String teminateReason ;   //终止原因

    @Column(name="teminater"   , nullable=true , unique=false)
    private Integer teminater;   //终止人ID

    @Column(name="teminater_name"  , length=100 , nullable=true , unique=false)
    private String teminaterName;   //终止人姓名

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }


    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Date getFactDate() {
        return factDate;
    }

    public void setFactDate(Date factDate) {
        this.factDate = factDate;
    }

    public BigDecimal getFactAmount() {
        return factAmount;
    }

    public void setFactAmount(BigDecimal factAmount) {
        this.factAmount = factAmount;
    }

    public String getFactMethod() {
        return factMethod;
    }

    public void setFactMethod(String factMethod) {
        this.factMethod = factMethod;
    }

    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }


    public Integer getFinancePayment() {
        return financePayment;
    }

    public void setFinancePayment(Integer financePayment) {
        this.financePayment = financePayment;
    }

    public String getPlanMethod() {
        return planMethod;
    }

    public void setPlanMethod(String planMethod) {
        this.planMethod = planMethod;
    }

    public String getPlanAccount() {
        return planAccount;
    }

    public void setPlanAccount(String planAccount) {
        this.planAccount = planAccount;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public String getFactAccount() {
        return factAccount;
    }

    public void setFactAccount(String factAccount) {
        this.factAccount = factAccount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getPlanDate() {
        return planDate;
    }

    public void setPlanDate(Date planDate) {
        this.planDate = planDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(Integer businessNo) {
        this.businessNo = businessNo;
    }

    public Integer getProcess() {
        return process;
    }

    public void setProcess(Integer process) {
        this.process = process;
    }

    public Integer getAccountBill() {
        return accountBill;
    }

    public void setAccountBill(Integer accountBill) {
        this.accountBill = accountBill;
    }

    public Integer getAccountBillHistory() {
        return accountBillHistory;
    }

    public void setAccountBillHistory(Integer accountBillHistory) {
        this.accountBillHistory = accountBillHistory;
    }

    public Integer getAuditInstance() {
        return auditInstance;
    }

    public void setAuditInstance(Integer auditInstance) {
        this.auditInstance = auditInstance;
    }

    public String getAuditInstanceChain() {
        return auditInstanceChain;
    }

    public void setAuditInstanceChain(String auditInstanceChain) {
        this.auditInstanceChain = auditInstanceChain;
    }

    public Integer getAuditProcess() {
        return auditProcess;
    }

    public void setAuditProcess(Integer auditProcess) {
        this.auditProcess = auditProcess;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getPayRejectReason() {
        return payRejectReason;
    }

    public void setPayRejectReason(String payRejectReason) {
        this.payRejectReason = payRejectReason;
    }

    public String getPayRejectDesc() {
        return payRejectDesc;
    }

    public void setPayRejectDesc(String payRejectDesc) {
        this.payRejectDesc = payRejectDesc;
    }

    public String getAuditRejectReason() {
        return auditRejectReason;
    }

    public void setAuditRejectReason(String auditRejectReason) {
        this.auditRejectReason = auditRejectReason;
    }

    public String getAuditRejectDesc() {
        return auditRejectDesc;
    }

    public void setAuditRejectDesc(String auditRejectDesc) {
        this.auditRejectDesc = auditRejectDesc;
    }

    public String getTeminateState() {
        return teminateState;
    }

    public void setTeminateState(String teminateState) {
        this.teminateState = teminateState;
    }

    public Date getTeminateTime() {
        return teminateTime;
    }

    public void setTeminateTime(Date teminateTime) {
        this.teminateTime = teminateTime;
    }

    public String getTeminateReason() {
        return teminateReason;
    }

    public void setTeminateReason(String teminateReason) {
        this.teminateReason = teminateReason;
    }

    public Integer getTeminater() {
        return teminater;
    }

    public void setTeminater(Integer teminater) {
        this.teminater = teminater;
    }

    public String getTeminaterName() {
        return teminaterName;
    }

    public void setTeminaterName(String teminaterName) {
        this.teminaterName = teminaterName;
    }
}
