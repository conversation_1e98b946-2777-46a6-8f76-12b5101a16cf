package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.generalAffairs.dto.PersonnelAttendanceMonthlyDto;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceMonthly;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

public interface PersonnelAttendanceMonthlyService {
    enum WholeDayStatus {
        noAttendanced("NoAttendanced", (byte)1),//非考勤人员
        noNeedDay("noNeedDay", (byte)1),//无需考勤
        workingDay("WorkingDay", (byte)2),//工作日
        leaveDay("LeaveDay", (byte)3),//请假日
        overtimeDay("OvertimeDay", (byte)4);//非工作日加班
        private String name;
        private Byte index;
        WholeDayStatus(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
        public static Map<String, Set<Date>> getEmptyWholeDays() {
            return new HashMap<String, Set<Date>>(5) {{
                put(WholeDayStatus.workingDay.getName(), new HashSet<>());
                put(WholeDayStatus.noNeedDay.getName(), new HashSet<>());
                put(WholeDayStatus.leaveDay.getName(), new HashSet<>());
                put(WholeDayStatus.overtimeDay.getName(), new HashSet<>());
                put(WholeDayStatus.noAttendanced.getName(), new HashSet<>());
            }};
        }
    }
//    Integer getPersonnelAttendanceMonthlyId(Integer uid, Integer yearmonth);//缓存函数
    PersonnelAttendanceMonthly NewEntity(User user, Integer yearmonth);//仅用于游离对象创建
    void AddUpdateUserInfo(User user,Date day);//更新姓名和部门
    void ClearDay(User user, Date day);//清除某天的月表数据（考）。
    void SetRemoveUser(Integer oid,List<Integer> uids, Date date); //设置当前不在考勤人员中的人为无需考勤（非）。
    void SetNewUserNoNeed(User user, Date day, Date firstDate);//设置当前新加人员无需考勤，不能垮月（非）。
    void SetNoNeed(User user, Date day, Boolean value);//设置无需考勤（假）
    void SetWorkingHours(User user, Date day, Float value);//设置工作时间
    void SetLeave(User user, Date day, Byte value, Float duration);//设置请假,必须在设置工作时间后执行，duration<=workingHours
    void SetTravel(User user, Date day, Byte value);//设置出差次数
    void SetLate(User user, Date day, Boolean value);//设置上班迟到
    void SetLeaveEarly(User user, Date day, Boolean value);//设置下班早退
    void SetOutside(User user, Date day, Byte value);//设置外出次数
    void SetAbsenteeisme(User user, Date day, Byte value);//设置旷工次数
    void SetOvertime(User user, Date day, Byte num, Float duration);//设置加班次数和时长
    Boolean GetNoNeed(User user, Date day);//获取无需考勤
    Float GetWorkingHours(User user, Date day);//获取工作时间
    Pair<Byte, Float> GetLeave(User user, Date day);//获取请假次数
    Byte GetTravel(User user, Date day);//获取出差次数
    Boolean GetLate(User user, Date day);//获取上班迟到
    Boolean GetLeaveEarly(User user, Date day);//获取下班早退
    Byte GetOutside(User user, Date day);//获取外出次数
    Byte GetAbsenteeisme(User user, Date day);//获取旷工次数
    Pair<Byte, Float> GetOvertime(User user, Date day);//获取加班次数和时长
    PersonnelAttendanceMonthly getAttendanceMonthly(Integer yearmonth,Integer userId);
    List<PersonnelAttendanceMonthly> getAttendanceMonthliesOrderbyName(Integer oid, Integer yearmonth, Integer deptId,Integer loginUserId,Integer userId, String userName, PageInfo pageInfo);
    List<PersonnelAttendanceMonthly> getAttendanceMonthliesOrderbyNameWithImage(Integer oid, Integer yearmonth, Integer deptId,Integer loginUserId,Integer userId, String userName, PageInfo pageInfo);
    List<PersonnelAttendanceMonthly> getAttendanceMonthliesByPeriod(Integer oid, Integer yearmonthBegin,Integer yearmonthEnd, Integer deptId,Integer loginUserId,Integer userId, String userName, PageInfo pageInfo);
    List<PersonnelAttendanceMonthly> getAttendanceMonthliesByPeriod(Integer oid, Integer yearmonthBegin,Integer yearmonthEnd, Collection<Integer> userIds);
    List<PersonnelAttendanceMonthly> getAllAttendanceMonthliesForInit();
    List<PersonnelAttendanceMonthlyDto>getAttendanceMonthlyStatistics(Integer oid, Integer yearmonth, Integer deptId,Integer loginUserId,Integer userId, String userName, PageInfo pageInfo);
    List<Map<String, Object>> getAttendanceUsers(Integer oid, Integer yearmonth,Integer userId);
}