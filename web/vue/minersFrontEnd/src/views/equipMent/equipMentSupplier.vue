<template>
  <div class="equipMentSupplier">
    <div class="main" style="margin: 0 96px;" v-if="mainNum === 'sup1'">
      <div class="Search">
        <el-input placeholder="供应商名称/供应商代号" v-model="searchVal" width="200px">
          <template #append>
            <span @click="searchEqSupplier"><i class="fa fa-search"></i> 搜索</span>
          </template>
        </el-input>
      </div>
      <div class="pull-right">
        <span class="panel_4 ty-btn ty-btn-big ty-btn-cyan ty-circle-3" id="contractImport"
              @click="importBtn()">批量导入</span>
        <span class="panel_4 ty-btn ty-btn-big ty-btn-green ty-circle-3" id="addCot"
              @click="addSupplierInfo()">新增供应商</span>
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" @click="suspendedSupplier()"
              id="suspendedContractBtn">已暂停采购的供应商</span>
      </div>
      <div class="opinionCon">
        <div class="gapLg">本公司处于正常状态的供应商共<span>{{indexPage.totalResult}}</span>个，具体如下：</div>
        <el-table stripe class="ty-table-control" :data="supplierUsingList" max-height="700" border style="width: 100%">
          <el-table-column label="供应商" width="260">
            <template #default="scope">
              {{(scope.row.name || '')}} / {{ (scope.row.codeName || '')}}
            </template>
          </el-table-column>
          <el-table-column label="创建人" width="280">
            <template #default="scope">
              {{(scope.row.createName || '')}} {{new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss')}}
            </template>
          </el-table-column>
          <el-table-column label="已到位的装备器具">
            <template #default="scope">
              <div class="ty-td-control">
                <span class="ty-color-blue" @click="showEqMsg(scope.row)">{{scope.row["supplyCount"] || '0'}}种</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <span class="ty-color-blue" @click="lookSupplier(scope.row)">查看</span>
              <span class="ty-color-blue" @click="supplierManage(scope.row)">管理</span>
              <span class="ty-color-red" @click="delSupplier(scope.row)">删除</span>
              <span class="ty-color-blue" @click="stopSupplier(scope.row, 1)">暂停采购</span>
            </template>
          </el-table-column>
        </el-table>
        <div id="ye_suppliecontract"></div>
      </div>
      <div class="clr"></div>
    </div>
    <!-- 已暂停采购的供应商 -->
     <div class="ty-container" id="procurement" v-if="mainNum === 'sup2'">
       <el-button type="primary" @click="mainNum = 'sup1'" style="margin: 0 96px;">返回上一页</el-button>
       <div class="pageStyle container_item">
         <div class="initBody" style="margin: 0 96px;">
           <div class="title" style="margin-top: 42px;">
             本公司处于暂停采购状态的供应商共<span>{{supplierSuspendedList.length}}</span>个，具体如下：
           </div>
           <el-table stripe class="ty-table-control" :data="supplierSuspendedList" height="700" border style="width: 100%; margin-top: 20px;">
             <el-table-column prop="fullName" label="供应商"> </el-table-column>
             <el-table-column prop="codeName" label="代号"> </el-table-column>
             <el-table-column label="创建人">
               <template #default="scope">
                 {{scope.row.createName}} {{new Date(scope.row["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}
               </template>
             </el-table-column>
             <el-table-column label="“暂停采购”的操作者">
               <template #default="scope">
                 {{scope.row.updateName}} {{new Date(scope.row["updateDate"]).format('yyyy-MM-dd hh:mm:ss')}}
               </template>
             </el-table-column>
             <el-table-column label="已到位的装备器具">
               <template #default="scope">
                 <div class="ty-td-control">
                   <span class="ty-color-blue" @click="showEqMsg(scope.row)">{{scope.row.supplyCount}}项</span>
                 </div>
               </template>
             </el-table-column>
             <el-table-column label="操作">
               <template #default="scope">
                 <span class="ty-color-blue" @click="lookSupplier(scope.row)">查看</span>
                 <span class="ty-color-blue" @click="stopSupplier(scope.row, 2)">恢复采购</span>
               </template>
             </el-table-column>
           </el-table>
           <TyPage v-if="susPage"
                   :curPage="susPage.currentPageNo" :pageSize="susPage.pageSize"
                   :allPage="susPage.totalPage" :pageClickFun="susPageClick"></TyPage>
         </div>
       </div>
     </div>
    <!-- 搜索后显示的 -->
     <div class="ty-container" v-if="mainNum === 'sup3'">
       <el-button type="primary" @click="mainNum = 'sup1'" style="margin: 0 96px;">返回上一页</el-button>
       <div class="pageStyle container_item">
         <div class="initBody" id="chose" style="margin: 0 96px;">
           <div class="title" style="margin-top: 42px;">
             符合条件的数据共<span>XX</span>条，具体如下：
           </div>
           <table class="ty-table ty-table-control" id="message">
             <thead style="background: none;">
             <td>供应商</td>
             <td>创建人</td>
             <td>已到位的装备器具</td>
             <td>操作</td>
             </thead>
             <tbody></tbody>
             <tr v-for="(item, index) in supplierSearchList" :key="index">
               <td>{{item.fullName}}/{{item.codeName}}</td>
               <td>{{item.createName}} {{new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
               <td class="ty-td-control">
                 <span class="ty-color-blue" @click="showEqMsg(item)">{{item.supplyCount}}种</span>
               </td>
               <td>
                 <span class="ty-color-blue" @click="lookSupplier(item)">查看</span>
                 <span class="ty-color-blue" @click="supplierManage(item)">修改</span>
                 <span class="ty-color-red"  @click="delSupplier(item)">删除</span>
                 <span class="ty-color-blue" @click="stopSupplier(item, 1)">暂停采购</span>
               </td>
             </tr>
           </table>
         </div>
       </div>
     </div>
    <!--  装备器具已到位 -->
     <div class="ty-container eqMsg" v-if="mainNum === 'sup4'">
       <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="mainNum = 'sup1'">返回</span>
       <div class="pageGap">
         <p class="eqMsg1 gapLg">以下装备器具已到位，共{{eqListCount.length}}项，系由{{supplierItem.fullName}}提供</p>
         <el-table stripe class="ty-table-control" :data="eqListCount" border style="width: 100%;">
           <el-table-column prop="lpadId" label="系统赋予的编码"> </el-table-column>
           <el-table-column prop="no" label="装备器具编号">
             <template #default="scope">
               {{scope.row.modelCode || ''}}
             </template>
           </el-table-column>
           <el-table-column label="装备器具名称">
             <template #default="scope">
               {{scope.row.equipmentName || ''}}
             </template>
           </el-table-column>
           <el-table-column label="型号">
             <template #default="scope">
               {{scope.row.modelName || ''}}
             </template>
           </el-table-column>
           <el-table-column prop="path" label="所属类别">
           </el-table-column>
           <el-table-column prop="no" label="创建">
             <template #default="scope">
               {{scope.row.createName}} {{new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss')}}
             </template>
           </el-table-column>
           <el-table-column label="参与加工的产品">
             <template #default="scope">
               <div class="ty-td-control"><span class="ty-color-blue">{{scope.row.productCount || '0'}}种</span></div>
             </template>
           </el-table-column>
           <el-table-column label="操作">
             <template #default="scope">
               <span class="ty-color-blue" @click="manageBtn(scope.row)">查看</span>
             </template>
           </el-table-column>
         </el-table>
       </div>
     </div>

    <!-- 新增供应商-->
    <TyDialog v-if="addData.visible" width="1060" dialogTitle="新增供应商" color="blue" :dialogHide="addDataHideFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="addDataHideFun('addData')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addSupplierDataOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="supplierAddCC">
          <table>
            <tbody>
            <tr>
              <td colspan="6" class="ttl">基本信息</td>
            </tr>
            <tr>
              <td width="140px;"><span class="marL30"><span class="ty-color-red">*</span>供应商名称：</span></td>
              <td colspan="5"><el-input v-model="addData.base.fullName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30"><span class="ty-color-red">*</span>供应商简称：</span></td>
              <td colspan="3"><el-input v-model="addData.base.name" placeholder="请录入"></el-input></td>
              <td><span class="marL10"><span class="ty-color-red">*</span>供应商代号：</span></td>
              <td><el-input v-model="addData.base.codeName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30">全景照片：</span></td>
              <td colspan="5">
                <div class="pa20" >
                  <uploadFile ref="uploadFile" class="ty-right"
                              module="供应商"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg,'
                              extMsg='请上传照片！'
                              :multiple="true"
                              :successFun="qjPicSuccess">
                    <template #btnArea>
                      <span class="ty-btn ty-btn-blue ">上传</span>
                    </template>
                  </uploadFile>
                </div>
                <span>(共可上传9张)</span>
              </td>
            </tr>
            <tr v-if="addData.base.quanImg.length > 0">
              <td></td>
              <td colspan="5">
                <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in addData.base.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg')"><i class="fa fa-close"></i></span>
                      </span>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="2"><span class="marL30">是否接受挂账？</span></td>
              <td colspan="2">
                <span class="marL30">
                  <el-radio v-model="addData.base.chargeAcceptable" :label='1'>接受</el-radio>
                  <el-radio class="marL30" v-model="addData.base.chargeAcceptable" :label='0'>不接受</el-radio>
                </span>
              </td>
              <td><span class="marL30" v-if="addData.base.chargeAcceptable == 1">请录入已约定的账期</span></td>
              <td>
                <div v-if="addData.base.chargeAcceptable == 1">
                  <div class="lineRow"><el-input type="text" id="spcontwek" v-model="addData.base.chargePeriod" @input="getLimitVal('chargePeriod')" /></div>天
                </div>
              </td>
            </tr>
            <tr v-if="addData.base.chargeAcceptable == 1">
              <td colspan="2">
                <span class="marL30">请选择从何时开始计算账期？</span>
              </td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="addData.base.chargeBegin" label="1">自货入库之日起</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.chargeBegin" label="2">自发票入账之日起</el-radio>
                    </span>
              </td>
            </tr>
            <tr>
              <td colspan="2">
                <span class="marL30">是否需要预付款？</span>
              </td>
              <td colspan="5">
                <span class="marL30">
                      <el-radio v-model="addData.base.isImprest" label="1">需要</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.isImprest" label="2">不需要</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.isImprest" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            <tr v-if="addData.base.isImprest === '1'">
              <td colspan="2"><span class="marL30">请录入需预付的比例</span></td>
              <td colspan="2">
                <div class="lineRow marL30"><el-input v-model="addData.base.imprestProportion" label="0" @input="getLimitVal('imprestProportion')"></el-input></div>
                %
              </td>
              <td>
                <el-radio  class="marL30" v-model="addData.base.uncertainty" label="1" @click="uncertainty">比例不确定</el-radio>
              </td>
            </tr>
            <tr>
              <td colspan="6" class="ttl">开票信息</td>
            </tr>
            <tr>
              <td colspan="2"><span class="marL30"><span class="ty-color-red">*</span>该供应商是否能开发票？</span></td>
              <td colspan="4">
                <span class="marL30">
                  <el-radio v-model="addData.base.invoicable" label="1">是</el-radio>
                  <el-radio  class="marL30" v-model="addData.base.invoicable" label="2">否</el-radio>
                </span>
              </td>
            </tr>
            <tr v-if="addData.base.invoicable === '1'">
              <td colspan="2"><span class="marL30">是否能开增值税专用发票？</span></td>
              <td colspan="2">
                <span class="marL30">
                      <el-radio v-model="addData.base.vatsPayable" label="1">是</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.vatsPayable" label="2">否</el-radio>
                    </span>
              </td>
              <td colspan="2">
                <div v-if="addData.base.vatsPayable === '1'">
                  <span class="marL30">
                  <span>请录入税率</span>
                </span>
                  <div class="lineRow marL30">
                    <el-input v-model="addData.base.taxRate" placeholder="请输入"></el-input>
                  </div>%
                </div>
              </td>
            </tr>
            <tr v-if="addData.base.invoicable === '1'">
              <td colspan="2"><span class="marL30">是否可接受汇票？</span></td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="addData.base.draftAcceptable" label="1">可接受</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.draftAcceptable" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            <tr>
              <td class=" ttl">合同信息</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addContractInfo('add')"> 新增</span>
              </td>
            </tr>
            <tr v-if="addData.contract.tableData.length >0">
              <td colspan="6">
                <el-table :data="addData.contract.tableData" stripe border style="width: 100%">
                  <el-table-column prop="sn" label="合同编号" width="180"> </el-table-column>
                  <el-table-column label="签署日期" width="180">
                    <template #default="scope">
                      {{new Date(scope.row.signTime).format('yyyy-MM-dd')}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="validTime" label="合同的有效期" width="220">
                    <template #default="scope">
                      {{$filter.format(scope.row.validTime?.[0], 'day')}} 至 {{$filter.format(scope.row.validTime?.[1], 'day')}}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button  @click="updateContract(scope.row,scope.$index)" type="text" size="small">修改</el-button>
                      <el-button @click="delContract(scope.row,scope.$index)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class=" ttl">邮寄信息</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addMailBtn">新增</span>
              </td>
            </tr>
            <tr v-if="addData.mail.tableData.length > 0">
              <td colspan="6">
                <el-table :data="addData.mail.tableData" stripe border style="width: 100%">
                  <el-table-column prop="address" label="邮寄地址" width="180"> </el-table-column>
                  <el-table-column prop="recivePerson" label="联系人" width="180"> </el-table-column>
                  <el-table-column prop="mailNo" label="邮政编码" width="180"> </el-table-column>
                  <el-table-column prop="tel" label="手机" width="180"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button  @click="updateMail(scope.row, scope.$index)" type="text" size="small">修改</el-button>
                      <el-button @click="delMail(scope.row, scope.$index)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class=" ttl">联系人</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addContact('contact')">新增</span>
              </td>
            </tr>
            <tr v-if="addData.contactLianxi.tableData.length > 0">
              <td colspan="6">
                <el-table v-if="addData.contactLianxi.tableData.length <= 1" :data="contactLianxiTwo" stripe border style="width: 50%">
                  <el-table-column prop="name" label="姓名" width="100"> </el-table-column>
                  <el-table-column prop="post" label="职位" width="200"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                      <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-table v-else :data="contactLianxiTwo" stripe border style="width: 100%">
                  <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                  <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                      <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name2" label="姓名" width="80"> </el-table-column>
                  <el-table-column prop="post2" label="职位" width="100"> </el-table-column>
                  <el-table-column label="操作" >
                    <template #default="scope">
                          <span v-if="scope.row.name2">
                            <el-button @click="updateContact(scope.row, 2)" type="text" size="small">修改</el-button>
                            <el-button @click="delContact(scope.row, 2)" type="text" size="small">删除</el-button>
                          </span>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="scanSupplierLog" width="1200" dialogTitle="供应商信息查看" color="blue" :dialogHide="scanSupplierHideFun">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="scanSupplierHideFun">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="scanSupplierCon" style="margin: 43px;">
          <div class="part">
            <div class="flexItem">
              <span class="item_title">供应商名称:</span>
              <span class="item_content" style="width: 600px" id="see_name">{{seeSupplierData.info.fullName}}</span>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">供应商简称:</span>
                <span class="item_content" id="see_fullname" style="width: 300px">{{seeSupplierData.info.fullName}}</span></div>
              <div class="flexItem" style="margin-left: 348px;">
                <span class="item_title" style="margin-right: 0;">供应商代号:</span>
                <span class="item_content" id="see_codde" style="width: 200px">{{seeSupplierData.info.codeName}}</span>
              </div>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">创建者:</span>
                <span class="item_content">
                  <span id="see_createName">{{seeSupplierData.info.createName}}</span>
                  <span id="see_createDate">{{new Date(seeSupplierData.info.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
                </span>
              </div>
              <div class="right_btn">
                <button class="ty-btn ty-btn-blue ty-circle-3" @click="getSuspendRecordList">
                  暂停采购/恢复采购的操作记录
                </button>
              </div>
              <div class="right_btn">
                <button class="ty-btn ty-btn-blue ty-circle-3" data-type="spbaserecord"
                        data-obj="see" @click="getRecordList('scanBase')">修改记录
                </button>
              </div>
            </div>
            <div class="item">
              <div class="item" id="prient" v-if="false">
                <span class="item_title" style="text-align: left;">暂停采购:</span>
                <span class="item_content" style="width: 300px"></span>
              </div>
            </div>
          </div>
          <div class="part">
            <div class="flexItem">
              <div class="item_title">全景照片：</div>
              <div class="item_content" id="overallImgUpload">
                <div class="imgsthumb">
                  <span class="imgI" v-for="(img, indexC2) in seeSupplierData.info.qImages" :key="indexC2">
                    <el-image class="img" :src="rootPath.fileUrl + img.normal" fit="contain" :preview-src-list="[rootPath.fileUrl + img.normal]" style="width: 100px; height: 80px"></el-image>
                  </span>
                </div>
              </div>
            </div>
            <div id="gotpose">
              <p style="margin: 0 0 0;">{{acceptable}}</p>
            </div>
          </div>
          <div class="part">
            <div class="flexItem" style="justify-content: space-between">
              <span class="item_title">开票信息</span>
              <button class="ty-btn ty-btn-blue ty-circle-3" data-type="spinvoiceRecords"
                      data-obj="see" @click="getRecordList('invoice')">
                修改记录
              </button>
            </div>
            <div id="shuil" style="display: flex">
              <p style="margin: 0 0 0;">{{invoicableInfo}}</p>
            </div>
          </div>
          <div class="addOtherInfo">
            <span class="par_ttl" style="margin-left: 8px;">合同信息</span>
            <span style="color: #5399c2;font-size: 0.8em;margin-left: 50px;"></span>
            <div class="dataList contractList" v-if="seeSupplierData.contractBaseList.length >0">
              <el-table :data="seeSupplierData.contractBaseList" stripe border width="100%">
                <el-table-column prop="sn" label="合同编号"> </el-table-column>
                <el-table-column label="签署日期">
                  <template #default="scope">
                    {{new Date(scope.row.signTime).format('yyyy-MM-dd')}}
                  </template>
                </el-table-column>
                <el-table-column label="合同的有效期">
                  <template #default="scope">
                    {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="contractIScan(scope.row,'scan')">查看</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="addOtherInfo">
            <span class="par_ttl" style="margin-left: 8px;">邮寄信息</span>
            <div class="dataList receiveList" v-if="seeSupplierData.info.yjAddressList.length >0">
              <el-table :data="seeSupplierData.info.yjAddressList" stripe border style="width: 100%">
                <el-table-column prop="address" label="邮寄地址" width="340"> </el-table-column>
                <el-table-column prop="name" label="联系人" width="180"> </el-table-column>
                <el-table-column prop="postcode" label="邮政编码" width="180"> </el-table-column>
                <el-table-column prop="mobile" label="手机" width="180"> </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="getRecordList('mail',scope.row, scope.$index)" >修改记录</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="addOtherInfo">
            <span class="par_ttl" style="margin-left: 8px;margin-right: 38px;">联系人</span>
            该供应商现共有如下<span class="contactNum">{{seeSupplierData.info.contactsList.length}}</span>位联系人
            <div class="dataList receiveListo">
              <el-table v-if="seeSupplierData.info.contactsList.length === 1" :data="seeSupplierData.info.contactLianxiTwo" stripe border style="width:55%">
                <el-table-column prop="name" label="姓名" width="100"> </el-table-column>
                <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                <el-table-column prop="mobile" label="手机" width="110"> </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="getContactWay(scope.row, 1)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 0)">查看名片</span>
                            <span class="ty-color-blue" @click="getRecordList('contact',scope.row)">修改记录</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
              <el-table v-if="seeSupplierData.info.contactsList.length > 1" :data="seeSupplierData.info.contactLianxiTwo" stripe border style="width: 100%">
                <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                <el-table-column prop="mobile" label="手机" width="100"> </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                           <span class="ty-td-control">
                            <span class="ty-color-blue" @click="getContactWay(scope.row, 1)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 1)">查看名片</span>
                            <span class="ty-color-blue" @click="getRecordList('contact',scope.row)">修改记录</span>
                          </span>
                  </template>
                </el-table-column>
                <el-table-column prop="name2" label="姓名" width="80"> </el-table-column>
                <el-table-column prop="post2" label="职位" width="100"> </el-table-column>
                <el-table-column prop="mobile2" label="手机" width="100"> </el-table-column>
                <el-table-column label="操作" >
                  <template #default="scope">
                          <span class="ty-td-control" v-if="scope.row.name2">
                            <span class="ty-color-blue" @click="getContactWay(scope.row, 2)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 2)">查看名片</span>
                            <span class="ty-color-blue" @click="getRecordList('contact',scope.row)">>修改记录</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>

    <!-- 联系人 名片-->
    <TyDialog class="receiveInfo" v-if="contactCard.visible" width="600" dialogTitle="名片" color="blue" :dialogHide="hideCrD">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideCrD">关闭</span>
      </template>
      <template #dialogBody>
        <div>
          <el-image
              style=" width: 580px; margin: 10px auto; height: 280px;"
              :src="contactCard.visitCard"
              fit="contain"
          >
            <template #placeholder>
              <div style="font-size: 28px; text-align: center; color: #666; line-height:100px; ">
                <i class="fa fa-spinner rotating"></i> 加载中 ...
              </div>
            </template>
            <template #error>
              <div style="font-size: 28px; text-align: center; color: #666;  line-height:100px; ">
                <i class="fa fa-file-image-o"></i> 加载失败！
              </div>
            </template>
          </el-image>
        </div>
      </template>
    </TyDialog>

    <!-- 管理-->
    <TyDialog class="updateCustomerPanel" v-if="supplierManageData.visible" width="800" dialogTitle="供应商常规信息管理" color="blue" :dialogHide="hideUpdateCustomerFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideUpdateCustomerFun">确定</span>
      </template>
      <template #dialogBody>
        <div class="" style="max-height:400px; ">
          <div class="cusName">{{ supplierManageData.editOrg.name }}</div>
          <table class="ty-table ty-table-control ty-table-txtLeft">
            <tbody>
            <tr class="manageTtl">
              <td>名称</td>
              <td>操作</td>
            </tr>
            <tr>
              <td>基本信息</td><td>
              <span class="ty-color-blue" @click="baseUpdate">修改</span>
              <span class="ty-color-blue" @click="getRecordList('scanBase')">修改记录</span>
            </td>
            </tr>
            <tr>
              <td>开票信息</td>
              <td>
                <span class="ty-color-blue" @click="invoiceManageUpdate">修改</span>
                <span class="ty-color-blue" @click="getRecordList('invoice')">修改记录</span>
              </td>
            </tr>
            <tr>
              <td>合同信息</td>
              <td>
                <span class="ty-color-blue" @click="addContractInfo('add')">新增</span>
                <span class="ty-color-blue" @click="seeExpireContract">已到期的合同</span>
                <span class="ty-color-blue" @click="seeStopContract">已暂停/终止的合同</span>
              </td>
            </tr>
            <tr v-for="(contractItem, contractIndex) in supplierManageData.contractBaseList" :key="contractIndex">
              <td class="pdL40" v-html="contractItem.sn"></td>
              <td>
                <span class="ty-color-blue" @click="contractIScan(contractItem)">查看</span>
                <span class="ty-color-blue" @click="manage_change(contractItem, contractIndex)">修改合同信息</span>
                <span class="ty-color-blue" @click="manage_renew(contractItem,1, contractIndex)">续约</span>
                <span class="ty-color-red" @click="manage_stop(contractItem, contractIndex)">暂停履约/合同终止</span>
              </td>
            </tr>
            <tr>
              <td>邮寄信息</td>
              <td>
                <span class="ty-color-blue" @click="fpAdd">新增</span>
                <span class="ty-color-blue" @click="adressStopList">已被停用的数据</span>
              </td>
            </tr>
            <tr v-for="(fpItem, fpIndex) in supplierManageData.info.yjAddressList" :key="fpIndex">
              <td class="pdL40" v-html="fpItem.address"></td>
              <td>
                  <div v-if="fpItem.enabled">
                    <span class="ty-color-blue" @click="fpAdressUpdate(fpItem, fpIndex)">修改</span>
                    <span class="ty-color-blue" @click="getRecordList('mail',fpItem, fpIndex)">修改记录</span>
                    <span class="ty-color-red" @click="addressStopBtn(fpItem, fpIndex)">停用</span>
                  </div>
              </td>
            </tr>
            <tr>
              <td>联系人</td>
              <td>
                <span class="ty-color-blue" @click="addContact('manageAdd')">新增</span>
                <span class="ty-color-blue" @click="addContactList">已被删除的数据</span>
              </td>
            </tr>
            <tr v-for="(contactItem, contactIndex) in supplierManageData.info.contactsList" :key="contactIndex">
              <td class="pdL40">
                  <span v-if="contactItem.enabled">
                    {{ contactItem.name }}  {{ contactItem.post }}  {{ contactItem.mobile }}
                  </span>
              </td>
              <td>
                  <div v-if="contactItem.enabled">
                    <span class="ty-color-blue" @click="contactUpdate(contactItem, contactIndex)">修改</span>
                    <span class="ty-color-blue" @click="getRecordList('contactManage',contactItem)">修改记录</span>
                    <span class="ty-color-red" @click="contactDel(contactItem, contactIndex)">删除</span>
                  </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 供应商 基本信息 修改-->
    <TyDialog v-if="manageBaseDataLog" width="1060" dialogTitle="修改基本信息" color="blue" :dialogHide="dialogHideFun" :dialogName="'manageBaseDataLog'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('manageBaseDataLog')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="manageBaseOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="manageBaseOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="supplierAddCC">
          <table>
            <tbody>
            <tr>
              <td colspan="6" class="ttl">基本信息</td>
            </tr>
            <tr>
              <td width="140px;"><span class="marL30"><span class="ty-color-red">*</span>供应商名称：</span></td>
              <td colspan="5"><el-input v-model="manageBaseData.fullName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30"><span class="ty-color-red">*</span>供应商简称：</span></td>
              <td colspan="3"><el-input v-model="manageBaseData.name" placeholder="请录入"></el-input></td>
              <td><span class="marL10"><span class="ty-color-red">*</span>供应商代号：</span></td>
              <td><el-input v-model="manageBaseData.codeName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30">全景照片：</span></td>
              <td colspan="5">
                <div class="pa20" >
                  <uploadFile ref="uploadFile" class="ty-right"
                              module="供应商"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg,'
                              extMsg='请上传照片！'
                              :multiple="true"
                              :successFun="qjPicSuccess_manage">
                    <template #btnArea>
                      <span class="ty-btn ty-btn-blue">上传</span>
                    </template>
                  </uploadFile>
                </div>
                <span>(共可上传9张)</span>
                <div class="imgList" v-if="manageBaseData.quanImg.length > 0">
                   <span class="imgI" v-for="(img, indexC2) in manageBaseData.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg', 'manage')"><i class="fa fa-close"></i></span>
                      </span>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="2"><span class="marL30">是否接受挂账？</span></td>
              <td colspan="2">
                <span class="marL30">
                  <el-radio v-model="manageBaseData.chargeAcceptable" :label='1'>接受</el-radio>
                  <el-radio class="marL30" v-model="manageBaseData.chargeAcceptable" :label='0'>不接受</el-radio>
                </span>
              </td>
              <td><span class="marL30" v-if="manageBaseData.chargeAcceptable == 1">请录入已约定的账期</span></td>
              <td>
                <div v-if="manageBaseData.chargeAcceptable == 1">
                  <div class="lineRow"><el-input type="text" id="spcontwek" v-model="manageBaseData.chargePeriod" @input="getLimitVal('chargePeriodMg')" /></div>天
                </div>
              </td>
            </tr>
            <tr v-if="manageBaseData.chargeAcceptable == 1">
              <td colspan="2">
                <span class="marL30">请选择从何时开始计算账期？</span>
              </td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="manageBaseData.chargeBegin" label="1">自货入库之日起</el-radio>
                      <el-radio  class="marL30" v-model="manageBaseData.chargeBegin" label="2">自发票入账之日起</el-radio>
                    </span>
              </td>
            </tr>
            <tr>
              <td colspan="2">
                <span class="marL30">是否需要预付款？</span>
              </td>
              <td colspan="5">
                <span class="marL30">
                      <el-radio v-model="manageBaseData.isImprest" label="1">需要</el-radio>
                      <el-radio  class="marL30" v-model="manageBaseData.isImprest" label="2">不需要</el-radio>
                      <el-radio  class="marL30" v-model="manageBaseData.isImprest" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            <tr v-if="manageBaseData.isImprest == '1'">
              <td colspan="2"><span class="marL30">请录入需预付的比例</span></td>
              <td colspan="2">
                <div class="lineRow marL30"><el-input v-model="manageBaseData.imprestProportion" @input="getLimitVal('imprestProportionMg')"></el-input></div>
                %
              </td>
              <td>
                <el-radio  class="marL30" v-model="manageBaseData.uncertainty" label="1" @click="uncertainty">比例不确定</el-radio>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 修改发票-->
    <TyDialog v-if="manageInvoiceData.visible" width="1060" dialogTitle="修改开票信息" color="blue" :dialogHide="dialogHideFun" :dialogName="'manageInvoiceData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('manageInvoiceData')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="manageInvoiceOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="supplierAddCC">
          <table>
            <tbody>
            <tr>
              <td colspan="2"><span class="marL30">该供应商是否能开发票？</span></td>
              <td colspan="4">
                <span class="marL30">
                  <el-radio v-model="manageInvoiceData.info.invoicable" label="1">是</el-radio>
                  <el-radio  class="marL30" v-model="manageInvoiceData.info.invoicable" label="2">否</el-radio>
                </span>
              </td>
            </tr>
            <tr v-if="manageInvoiceData.info.invoicable === '1'">
              <td colspan="2"><span class="marL30">是否能开增值税专用发票？</span></td>
              <td colspan="2">
                <span class="marL30">
                      <el-radio v-model="manageInvoiceData.info.vatsPayable" label='1'>是</el-radio>
                      <el-radio  class="marL30" v-model="manageInvoiceData.info.vatsPayable" label='2'>否</el-radio>
                    </span>
              </td>
              <td colspan="2">
                <div v-if="manageInvoiceData.info.vatsPayable == '1'">
                  <span class="marL30">
                  <span>请录入税率</span>
                </span>
                  <div class="lineRow marL30">
                    <el-input v-model="manageInvoiceData.info.taxRate" placeholder="请输入" @input="getLimitVal('taxRateMg')"></el-input>
                  </div>%
                </div>
              </td>
            </tr>
            <tr v-if="manageInvoiceData.info.invoicable == '1'">
              <td colspan="2"><span class="marL30">是否可接受汇票？</span></td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="manageInvoiceData.info.draftAcceptable" label="1">可接受</el-radio>
                      <el-radio  class="marL30" v-model="manageInvoiceData.info.draftAcceptable" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <!-- 新增邮寄地址 -->
    <TyDialog v-if="mailInfoData.visible" width="460" :dialogTitle="mailInfoData.title" color="green" :dialogHide="dialogHideFun" :dialogName="'mailInfoData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('mailInfoData')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="mailInfoOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="mailInfoOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="newMailInfo">
          <table>
            <tbody>
            <tr>
              <td class="txtRight">邮寄地址：</td>
              <td><el-input v-model="mailInfoData.address" placeholder="请输入内容" clearable></el-input></td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight">邮寄编码：</td>
              <td><el-input v-model="mailInfoData.code" @change="chargeCode" placeholder="请输入内容" clearable></el-input>
              </td>
              <td></td>
            </tr>
            <tr v-if="!mailInfoData.mailValidSta" >
              <td></td>
              <td class="ty-color-red" style="font-size: 14px; line-height: 20px;">请输入正确的邮寄编码！</td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight">联系人：</td>
              <td>
                <div class="mask" @click="showContact('mailInfo')"></div>
                <el-input v-model="mailInfoData.contact.name" placeholder="请选择">
                  <template #append>
                    <i class="fa fa-angle-down"></i>
                  </template>
                </el-input></td>
              <td>
                <span class="ty-linkBtn" @click="addContact('mailInfo')">新增</span>
              </td>
            </tr>
            </tbody>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 选择联系人-->
    <TyDialog class="chooseCusContact" v-if="chooseCusContactData.visible" width="460" dialogTitle="选择联系人" color="blue" :dialogHide="dialogHideFun" :dialogName="'chooseCusContact'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('chooseCusContact')">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="chooseCusContactOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="chooseCusContactcc">
          <div>以下为可供选择的客户联系人</div>
          <div class="table">
            <table>
              <tr v-for="(Conac, indexConac) in chooseCusContactData.contactList" :key="indexConac">
                <td width="80%">
                    <span @click="clickItem(Conac)" class="clickArea">
                      <i class="fa" :class="chooseCusContactData.selectConact && chooseCusContactData.selectConact.id == Conac.id ? 'fa-dot-circle-o' : 'fa-circle-o' "></i>
                      <span>{{ Conac.name }}</span>
                      <span>{{ Conac.post }}</span>
                      <span>{{ (Conac.info && Conac.info.tel ? Conac.info.tel : Conac.mobile)}}</span>
                    </span>
                </td>
                <td class="txtRight"><span class="ty-linkBtn" @click="detailsConac(Conac)">查看</span></td>
              </tr>
            </table>
          </div>
        </div>
      </template>
    </TyDialog>

    <!-- 新增联系人-->
    <TyDialog class="editContactLianXi" v-if="editContactLianXiData.visible" width="750" :dialogTitle="editContactLianXiData.title" color="green" :dialogHide="dialogHideFun" :dialogName="'contactLianXi'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('contactLianXi')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="editContactLianXiOkStatus ? 'bounce-ok':'bounce-cancel'" @click="editContactLianXiOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="editContactLianXicc">
          <table>
            <tr>
              <td class="txtRight">联系人标签：</td>
              <td colspan="3">{{ editContactLianXiData.tag }}</td>
            </tr>
            <tr>
              <td class="txtRight">姓名：</td>
              <td><el-input v-model="editContactLianXiData.name" placeholder="请录入"></el-input></td>
              <td class="txtRight">职位：</td>
              <td><el-input v-model="editContactLianXiData.postName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">手机：</td>
              <td><el-input v-model="editContactLianXiData.tel" placeholder="请录入"></el-input></td>
              <td><span class="ty-linkBtn" @click="showOptionsFun">添加更多联系方式</span></td>
              <td>
                <el-select v-if="editContactLianXiData.showOptions" v-model="editContactLianXiData.defineType" placeholder="请选择" @change="setLink">
                  <el-option
                      v-for="(item, indexI) in editContactLianXiData.typeList"
                      :key="item.value"
                      :label="item.label"
                      :value="indexI"
                      :disabled="item.disabled"
                  >
                  </el-option>
                </el-select>
              </td>
            </tr>
            <tr v-for="(contact, index) in editContactLianXiData.contactList" :key="index">
              <td class="txtRight">{{ contact.name }}：</td>
              <td><el-input v-model="contact.val" @input="addContactVal(contact.val, index)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 1)">删除</span></td>
              <td class="txtRight"><span v-if="contact.name2">{{ contact.name2 }}：</span></td>
              <td>
                <div v-if="contact.name2" >
                  <el-input v-model="contact.val2" @input="addContactVal(contact.val2, index+1)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 2)">删除</span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="txtRight">名片：</td>
              <td colspan="3">
              </td>
            </tr>
            <tr >
              <td class="txtRight"></td>
              <td colspan="3">
                <div v-if="editContactLianXiData.picInfo.src" class="imgcc">
                  <img :src="editContactLianXiData.picInfo.src" alt="名片">
                  <span class="ty-linkBtn-red" @click="delPic">删除</span>
                </div>
                <div v-else
                     v-loading="editContactLianXiData.loading"
                     element-loading-background="rgba(100, 100, 100, 0.1)"
                     element-loading-text="正在上传...."
                     style="width: 200px; min-height: 40px; max-height: 150px;">
                  <uploadFile ref="uploadFile"
                              module="装备器具"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg'
                              extMsg='请上传名片！'
                              :multiple="false"
                              :beforeUploadFun="beforeUploadLianXi"
                              :successFun="fileSuccessLianXi">
                    <template #btnArea>
                      <span class="ty-linkBtn lix">{{ editContactLianXiData.loadingBtn }}</span>
                    </template>
                  </uploadFile>
                </div>

              </td>

            </tr>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 自定义标签-->
    <TyDialog class="useDefinedLabel" v-if="useDefinedLabelData.visible" width="450" dialogTitle="自定义标签" color="blue" :dialogHide="dialogHideFun" :dialogName="'useDefined'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('useDefined')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="useDefinedLabelData.val?'bounce-ok':'ty-btn-gray'" @click="useDefinedLabelOk">使用</span>
      </template>
      <template #dialogBody>
        <div class="useDefinedLabelcc">
          <span>自定义标签</span><br>
          <el-input v-model="useDefinedLabelData.val"></el-input>
        </div>
      </template>
    </TyDialog>

    <!-- 收货地址adress 停用 提示 -->
    <TyDialog v-if="stopAdresstData.visible" dialogTitle="提示" color="red" :dialogHide="hideShAdress">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideShAdress">关闭</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="shAdressStopOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 50px;">
          确定停用吗？
        </div>
      </template>
    </TyDialog>

    <!-- 删除联系人提示 -->
    <TyDialog v-if="stopContactData.visible" dialogTitle="提示" color="red" :dialogHide="hideStopCt">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideStopCt">关闭</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="stopCtOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 50px;">
          删除后，本联系人依旧显示在查看中，但仅可查看。
        </div>
      </template>
    </TyDialog>
    <!-- 已被停用的邮寄信息-->
    <TyDialog v-if="stopShAdressData.visible" width="600" dialogTitle="已被停用的邮寄信息" color="red" :dialogHide="hideStopShAdress">
      <template #dialogFooter>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideStopShAdress">关闭</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
          <table class="ty-table ty-table-control ">
            <tbody>
            <tr><td class="txtLeft">名称</td><td class="txtLeft" style="padding-left:30px;">操作</td></tr>
            <tr v-for="(adressItem, adItm) in stopShAdressData.list" :key="adItm">
              <td class="txtLeft">{{ adressItem.address }}</td>
              <td class="txtLeft">
                <span class="ty-color-red" @click="startAdress(adressItem)">启用</span>
                <span class="ty-color-blue" @click="getRecordList('mail',adressItem)">修改记录</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 已被删除的联系人数据-->
    <TyDialog v-if="contactStopData.visible" width="600" dialogTitle="已被删除的联系人" color="red" :dialogHide="hideContactStop">
      <template #dialogFooter>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideContactStop">关闭</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
          <table class="ty-table ty-table-control ">
            <tbody>
            <tr><td class="txtLeft">名称</td><td class="txtLeft" style="padding-left:30px;">操作</td></tr>
            <tr v-for="(contactItem, ccii) in contactStopData.list" :key="ccii">
              <td class="txtLeft">
                {{ contactItem.name }}
                <span class="ty-right" v-html="(contactItem.post && contactItem.post.substr(0,8)) || ''"></span>
              </td>
              <td class="txtLeft">
                <span class="ty-color-blue" @click="getRecordList('contactManage',contactItem)">修改记录</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 修改记录 -->
    <TyDialog class="updateRecords" v-if="updateRecordsData.visible" width="700" :dialogTitle="updateRecordsData.title" color="blue" :dialogHide="dialogHideFun" :dialogName="'recordsData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('recordsData')">关闭</span>
      </template>
      <template #dialogBody>
        <div class="recInfo">
          <span>{{ updateRecordsData.recordTip }}</span>
          <span v-if="this.updateRecordsData.type !== 'mail' && this.updateRecordsData.type !== 'contact'" class="ty-right" v-html="updateRecordsData.recordEditer"></span>
        </div>
        <div v-if="updateRecordsData.list.length > 0">
          <el-table :data="updateRecordsData.list"  border style="width: 100%">
            <el-table-column prop="infoTxt" label="记录"></el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                  <span class="ty-td-control">
                    <span class="ty-color-blue" @click="cus_recordDetail(scope.row,scope.$index)">查看</span>
                  </span>
              </template>
            </el-table-column>
            <el-table-column prop="handler" label="创建者/修改者"> </el-table-column>
          </el-table>

        </div>
      </template>
    </TyDialog>


    <!-- 已到期的合同 -->
    <TyDialog class="updateRecords" v-if="contractEndData.visible" width="600" dialogTitle="已到期的合同" color="blue" :dialogHide="dialogHideFun" :dialogName="'contractEndData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('contractEndData')">关闭</span>
      </template>
      <template #dialogBody>
        <div>
          <el-table :data="contractEndData.contractBaseList"  border style="width: 100%">
            <el-table-column prop="infoTxt" label="合同编号"></el-table-column>
            <el-table-column prop="infoTxt" label="到期日"></el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                  <span class="ty-td-control">
                    <span class="ty-color-blue" @click="contractIScan(scope.row, 'scan')">查看</span>
                    <span class="ty-color-blue" @click="manage_renew(scope.row)">续约</span>
                  </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <!-- 已暂停/终止的合同 -->
    <TyDialog class="updateRecords" v-if="contractStopData.visible" width="600" dialogTitle="已暂停履约/终止的合同" color="blue" :dialogHide="dialogHideFun" :dialogName="'contractStopData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('contractStopData')">关闭</span>
      </template>
      <template #dialogBody>
        <div>
          <el-table :data="contractStopData.contractBaseList"  border style="width: 100%">
            <el-table-column prop="sn" label="合同编号" width="110"></el-table-column>
            <el-table-column label="暂停履约/终止的时间" width="230">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                  <span class="ty-td-control">
                    <span class="ty-color-blue" @click="contractIScan(scope.row, 'scan')">查看</span>
                    <span class="ty-color-blue" @click="manage_recovery(scope.row)">恢复履约/重启合作</span>
                  </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>

    <!-- 删除供应商 -->
    <TyDialog v-if="delCusData.visible" width="600" dialogTitle="!提示" color="red" :dialogHide="hideDelCus">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideDelCus">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="delSupplierOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 50px;">
          确定删除吗?
        </div>
      </template>
    </TyDialog>
    <!-- 暂停合作 供应商 -->
    <TyDialog v-if="paulsCusData.visible" width="600" dialogTitle="！提示" color="blue" :dialogHide="hidePaulsCus">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hidePaulsCus">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="paulsCusOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: left;padding: 12px 57px;" v-if="paulsCusData.type === 1">
          <p>
            对某供应商“暂停采购”意味着暂停其供应资格。点击本页面上的“确定”后：
          </p>
          <br />
          <p>
            1、为材料选择定点供应商时，该供应商不再出现于选项中。
          </p>
          <br />
          <p>
            2、再下订单时，为要采购的材料选择供应商时，该供应商不再出现于选项中。
          </p>
          <br />
          <br />
          <p>
            需要时，可在系统中对其“恢复采购”，但可购买其什么材料，需另行设置。
          </p>
          <br />
          <br />
          <p>
            确定暂停该供应商的供应资格吗？
          </p>
        </div>
        <div style="text-align: center; line-height: 50px;" v-if="paulsCusData.type === 2">
          确定恢复该供应商的供应资格吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="baseRecordScanData.visible" width="900" dialogTitle="基本信息查看" color="blue" :dialogHide="dialogHideFun" :dialogName="'baseRecordScanLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialogHideFun('baseRecordScanLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="scanSupplierCon" style="margin: 43px;">
          <div class="part">
            <div class="flexItem">
              <span class="item_title">供应商名称:</span>
              <span class="item_content" style="width: 600px" id="see_name">{{baseRecordScanData.info.fullName}}</span>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">供应商简称:</span>
                <span class="item_content" id="see_fullname" style="width: 300px">{{baseRecordScanData.info.name}}</span></div>
              <div class="flexItem">
                <span class="item_title" style="margin-right: 0;">供应商代号:</span>
                <span class="item_content" id="see_codde" style="width: 200px">{{baseRecordScanData.info.codeName}}</span>
              </div>
            </div>
          </div>
          <div class="part">
            <div class="flexItem">
              <div class="item_title">全景照片：</div>
              <div class="item_content" id="overallImgUpload">
                <div class="imgsthumb">
                  <span class="imgI" v-for="(img, indexC2) in baseRecordScanData.info.qImages" :key="indexC2">
                    <el-image class="img" :src="rootPath.fileUrl + img.normal" fit="contain" :preview-src-list="[rootPath.fileUrl + img.normal]" style="width: 100px; height: 80px"></el-image>
                  </span>
                </div>
              </div>
            </div>
            <div id="gotpose">
              <p>{{record_acceptable}}</p>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="invoiceRecordScanData.visible" width="800" dialogTitle="开票信息查看" color="blue" :dialogHide="dialogHideFun" :dialogName="'invoiceRecordScanLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialogHideFun('invoiceRecordScanLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="scanSupplierCon" style="margin: 43px;">
          <div class="part">
            <div class="flexItem">
              <span class="item_title">供应商名称:</span>
              <span class="item_content" style="width: 600px" id="see_name">{{invoiceRecordScanData.info.fullName}}</span>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">供应商简称:</span>
                <span class="item_content" id="see_fullname" style="width: 300px">{{invoiceRecordScanData.info.fullName}}</span></div>
              <div class="flexItem">
                <span class="item_title" style="margin-right: 0;">供应商代号:</span>
                <span class="item_content" id="see_codde" style="width: 200px">{{invoiceRecordScanData.info.codeName}}</span>
              </div>
            </div>
          </div>
          <div class="part">
            <div id="shuil" style="display: flex">
              <p style="margin: 0 0 0;">{{record_invoicableInfo}}</p>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="mailRecordScanData.visible" width="800" dialogTitle="开票信息查看" color="blue" :dialogHide="dialogHideFun" :dialogName="'mailRecordScanLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialogHideFun('mailRecordScanLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="recordMain">
          <p class="fpDisUse ty-color-red" v-if="mailRecordScanData.info.operation == '2'">
            本地址于{{new Date(mailRecordScanData.info.enabledTime).format('yyyy/MM/dd hh:mm:ss')}}被{{mailRecordScanData.info.updateName}}{{mailRecordScanData.info.enabled == '1'?'启用。':'停用。'}}</p>
          <p>
            <span class="sale_ttl1">邮寄地址：</span>
            <span class="sale-con" id="fpAddress" :class="{'redFlag':compareRed(mailRecordScanData.front.address, mailRecordScanData.info.address)}">{{mailRecordScanData.info.address}}</span>
          </p>
          <p>
            <span class="sale_ttl1">发票接收人：</span>
            <span class="sale-con" id="fpName">{{compareD(mailRecordScanData.front.contact, mailRecordScanData.info.contact)}}</span>
          </p>
          <p>
            <span class="sale_ttl1">联系电话：</span>
            <span class="sale-con" id="fpMobile">{{compareD(mailRecordScanData.front.mobile, mailRecordScanData.info.mobile)}}</span>
          </p>
          <p>
            <span class="sale_ttl1">邮寄编码：</span>
            <span class="sale-con" id="fpNumber" :class="{'redFlag':compareRed(mailRecordScanData.front.postcode, mailRecordScanData.info.postcode)}">{{mailRecordScanData.info.postcode}}</span>
          </p>
        </div>
      </template>
    </TyDialog>

    <!-- 联系人修改记录 详情 -->
    <TyDialog class="editContactLianXi" v-if="contactUpdateLogDetailsData.visible" width="600" :dialogTitle="contactUpdateLogDetailsData.title" color="blue" :dialogHide="hidetitleFun">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok"  @click="hidetitleFun">关闭</span>
      </template>
      <template #dialogBody>
        <div class="editContactLianXicc contactUpdateLogDetails">
          <table>
            <tr>
              <td width="80">姓名：</td>
              <td v-html="contactUpdateLogDetailsData.name"></td>
              <td width="80">职位：</td>
              <td width="200" v-html="contactUpdateLogDetailsData.postName"></td>
            </tr>
            <tr>
              <td>手机：</td>
              <td v-html="contactUpdateLogDetailsData.tel"></td>
              <td></td>
              <td></td>
            </tr>
            <tr v-for="(contact, index) in contactUpdateLogDetailsData.contactList" :key="index">
              <td v-html="contact.name + ':'"></td>
              <td v-html="contact.val"></td>
              <td><span v-if="contact.name2" v-html="contact.name2 + ':'"></span></td>
              <td >
                <div v-if="contact.name2" v-html="contact.val2">
                </div>
              </td>
            </tr>
            <tr>
              <td>名片：</td>
              <td colspan="3"></td>
            </tr>
            <tr >
              <td></td>
              <td colspan="3">
                <div v-if="contactUpdateLogDetailsData.picSrc" class="imgcc">
                  <img :src="contactUpdateLogDetailsData.picSrc" alt="名片">
                </div>
              </td>
            </tr>
          </table>

        </div>
      </template>
    </TyDialog>
    <!-- 暂停合作/恢复合作的操作记录-->
    <TyDialog class="suspendRecord" v-if="suspendRecordData.visible" width="600" dialogTitle="暂停合作/恢复合作的操作记录" color="blue" :dialogHide="hideSuspendRecord">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSuspendRecord">关闭</span>
      </template>
      <template #dialogBody>
        <div class="">
          <el-table :data="suspendRecordData.list" border style="width: 100%">
            <el-table-column prop="handleTxt" label="操作性质"> </el-table-column>
            <el-table-column prop="handler" label="操作者"> </el-table-column>
          </el-table>

        </div>
      </template>
    </TyDialog>
    <!-- 联系人联系方式 详情 -->
    <TyDialog class="editContactLianXi" v-if="contactDetailsData.visible" width="600" dialogTitle="供应商联系人" color="blue" :dialogHide="hideLianXiFun">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok"  @click="hideLianXiFun">关闭</span>
      </template>
      <template #dialogBody>
        <div class="contactFlex">
          <div style="display: inline-block;margin-left: 29px;">
            <span>姓名:</span>
            <span class="contact-con" id="con-contactName" v-html="contactDetailsData.name"></span>
          </div>
          <div style="display:inline-block;">
            <span class="sale_ttl1">职位:</span>
            <span class="contact-con" id="con-contactpost" v-html="contactDetailsData.postName"></span>
          </div>
        </div>
        <div class="contactflex">
          <div style="margin-left: 29px;">
            <span>联系人标签:</span>
            <span class="contact-con" id="con-contacttag">{{contactDetailsData.tags? contactDetailsData.tags: ''}}</span>
          </div>
        </div>
        <table class="see-otherContact ty-table" style="width: 442px;margin: 20px auto;">
          <tbody>
          <tr v-for="(contact, index) in contactDetailsData.contactList" :key="index">
            <td>{{contact.name}}</td>
            <td>{{contact.val}}</td>
            <td>{{contact.name2}}</td>
            <td>{{contact.val2}}</td>
          </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>

    <!--  弹窗的高低顺序 是按照代码的前后顺序   -->
    <TyDialog v-if="addPurContractLog" width="600" :dialogTitle="editContractTitle" :dialogHide="hideFun" :dialogName="'addPurContractLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('addPurContractLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addPurContractMid">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form
              :rules="editContractRules"
              label-position="top"
              ref="editContractForm"
              class="editContract"
              :model="form_editContract"
              label-width="110"
          >
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="供应商名称" prop="fullName" v-if="(rootCusBtn === 'update')">
                  <el-input v-model="form_editContract.supInfo.fullName" placeholder="请录入" readonly disabled/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="合同编号" prop="sn">
                  <el-input v-model="form_editContract.sn" placeholder="请录入"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="签署日期" prop="signTime">
                  <el-date-picker
                      v-model="form_editContract.signTime"
                      type="date"
                      placeholder="请录入"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="合同的有效期" prop="validTime">
                  <el-date-picker
                      v-model="form_editContract.validTime"
                      type="daterange"
                      range-separator="到"
                      start-placeholder="请选择"
                      end-placeholder="请选择"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :unlink-panels="true"
                      :disabled-date="disabledDate"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="合同的扫描件或照片(共可上传9张)" prop="contractBaseImages" class="widthLimit">
              <template #label>
                合同的扫描件或照片(共可上传9张)
                <div style="float: right">
                  <el-button type="primary" link @click="$refs.uploadImgBtn.click()">上传</el-button>
                </div>
              </template>
              <div class="fileBox">
                <el-upload
                    v-model:file-list="form_editContract.contractBaseImages"
                    :headers="imgUpload.uploadHeaders"
                    :action="imgUpload.uploadAction"
                    :accept="imgUpload.uploadLyc"
                    :data="imgUpload.postData"
                    :limit="9"
                    :multiple="true"
                    :on-success="editContract_imgSuccess"
                    :on-preview="editContract_imgPreview"
                    :on-remove="editContract_imgRemove"
                    :on-exceed="editContract_imgHandleExceed"
                    list-type="picture-card"
                >
                  <span style="display: none" ref="uploadImgBtn">上传</span>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="合同的可编辑版" prop="fileList" class="widthLimit" width="300">
              <template #label>
                合同的可编辑版
                <div style="float: right">
                  <el-button type="primary" link @click="$refs.uploadFileBtn.click()">上传</el-button>
                </div>
              </template>
              <div class="fileBox">
                <el-upload
                    class="upload_contract-file"
                    v-model:file-list="form_editContract.fileList"
                    :headers="fileUpload.uploadHeaders"
                    :action="fileUpload.uploadAction"
                    :accept="fileUpload.uploadLyc"
                    :data="fileUpload.postData"
                    ref="upload"
                    :limit="1"
                    :on-exceed="editContract_fileHandleExceed"
                    :on-success="editContract_fileSuccess"
                >
                  <span style="display: none" ref="uploadFileBtn">上传</span>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="备注" prop="memo">
              <el-input v-model="form_editContract.memo" placeholder="请录入" show-word-limit maxlength="255"/>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <el-dialog v-model="dialog_visible_imgShow">
      <div class="text-center">
        <img w-full :src="imgShow" alt="Preview Image" style="width: 100%"/>
      </div>
    </el-dialog>
    <TyDialog v-if="dialog_visible_manage_img" width="700" dialogTitle="合同的扫描件或照片" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_img'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_img')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-image
              v-for="(item, index) in list_seeContractImg.list"
              :key= index
              style="width: 80px; height: 80px"
              :src="item.url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="list_seeContractImg.preview"
              :initial-index="index"
              fit="cover"
          />
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_recoveryExpire" width="500" dialogTitle="！提示" :dialogHide="hideFun" :dialogName="'dialog_visible_recoveryExpire'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_recoveryExpire')">关闭</el-button>
        <el-button type="primary" @click="manage_recoveryExpire_submit()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 400px; margin: 0 auto">
          本合同已于{{recoveryExpire.validEnd}}到期。请确定具体要进行哪项操作：
          <el-radio-group v-model="recoveryExpire.radio" style="flex-direction: column; align-items: flex-start; margin: 16px 0; padding-left: 46px">
            <el-radio :value="1">不再执行，转入“已到期的合同”</el-radio>
            <el-radio :value="2">续约</el-radio>
          </el-radio-group>
        </div>
      </template>
    </TyDialog>

    <!-- 查看合同详情 -->
    <TyDialog v-if="contractScanData.visible" width="800" dialogTitle="查看合同" color="blue" :dialogHide="hideCScan">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCScan">关闭</span>
      </template>
      <template #dialogBody>
        <div class="contractScan">
          <div style="line-height:40px; ">本版本合同的创建 {{ contractScanData.contractBase.createName }} {{ new Date( contractScanData.contractBase.createDate).format('yyyy-MM-dd hh:mm:ss') }}</div>
          <table class="ty-table ty-table-control ty-table-txtLeft" v-if="contractScanData.contractBase.sn">
            <tbody>
            <tr><td width="200px;">合同编号</td><td>{{ contractScanData.contractBase.sn }}</td></tr>
            <tr><td>签署日期</td><td>{{ new Date(contractScanData.contractBase.signTime).format('yyyy-MM-dd')   }}</td></tr>
            <tr><td>合同的有效期</td><td>{{ new Date(contractScanData.contractBase.validStart).format('yyyy-MM-dd') }} 至 {{ new Date(contractScanData.contractBase.validEnd).format('yyyy-MM-dd') }}</td></tr>
            <tr><td>合同的扫描件或照片</td><td>
              <el-image
                  v-for="(item, index) in contractScanData.listImage"
                  :key= index
                  style="width: 40px; height: 40px"
                  :src="item.src"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="contractScanData.listImage.previewList"
                  :initial-index="index"
                  fit="cover"
                  :preview-teleported="true"
              />
            </td></tr>
            <tr><td>合同的可编辑版</td><td><span class="ty-color-blue" @click="preview_File(contractScanData.contractBase.filePath)" v-if="contractScanData.contractBase.filePath">查看</span></td></tr>
            <tr><td>备注</td><td>{{ contractScanData.contractBase.memo }}</td></tr>
            <tr><td>本版本合同的修改记录</td><td><span class="ty-color-blue" @click="manage_changeHis(contractScanData.contractBase)">查看</span></td></tr>
            <tr><td>本合同的续约记录</td><td><span class="ty-color-blue" @click="manage_renewHis(contractScanData.contractBase)">查看</span></td></tr>
            </tbody>
          </table>
          <div class="txtRight hisList">
            <div v-for="(enIm, index) in contractScanData.listHis" :key="index">
              <span class="warpHis"> {{ enIm.suspend ? "暂停履约/终止合作" : "恢复履约/重启合作" }} </span>
              <span> {{ enIm.createName }} </span>
              <span> {{ new Date(enIm.suspendTime).format('yyyy-MM-dd hh:mm:ss')  }} </span>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>

    <TyDialog v-if="dialog_visible_manage_renewHis" width="700" dialogTitle="本合同的续约记录" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_renewHis'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_renewHis')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-table :data="seeContractRenewHis.list" border style="width: 100%; margin-top: 16px">
            <el-table-column label="版本" width="200">
              <template #default="scope">
                {{scope.$index === 0?'第1版（原始版本）':'第' + (scope.$index + 1) + '版（第' + scope.$index + '次续约后）'}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'renew')"><b>查看</b></el-button>
              </template>
            </el-table-column>
            <el-table-column label="创建（时间为各版本合同的续约时间）" width="300">
              <template #default="scope">
                {{scope.row.updateName || scope.row.createName}}
                {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_manage_changeHis" width="700" dialogTitle="本版本合同的修改记录" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_changeHis'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_changeHis')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <div class="row">
            当前数据为本版本合同第{{seeContractChangeHis.list.length - 1}}次修改后的结果。
            <div class="rightBtn">
              修改时间：{{$filter.format(seeContractChangeHis.list.at(-1)?.updateDate || seeContractChangeHis.list.at(-1)?.createDate)}}
            </div>
          </div>
          <el-table :data="seeContractChangeHis.list" border style="width: 100%; margin-top: 16px">
            <el-table-column label="记录">
              <template #default="scope">
                {{scope.$index === 0?'本版本合同的原始信息':'第' +scope.$index+ '次修改后'}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'change')"><b>查看</b></el-button>
              </template>
            </el-table-column>
            <el-table-column label="创建者/修改者" width="250">
              <template #default="scope">
                {{scope.row.updateName || scope.row.createName}}
                {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>

    <TyDialog v-if="dialog_visible_seeContract" width="700" dialogTitle="查看合同" :dialogHide="hideFun" :dialogName="'dialog_visible_seeContract'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_seeContract')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-descriptions
              class="margin-top"
              :column="1"
              border
          >
            <el-descriptions-item label="合同编号" width="200">{{seeContractDetail.info.sn}}</el-descriptions-item>
            <el-descriptions-item label="签署日期">{{seeContractDetail.info.signTime}}</el-descriptions-item>
            <el-descriptions-item label="合同的有效期">
              {{seeContractDetail.info.validStart}} 至 {{seeContractDetail.info.validEnd}}
            </el-descriptions-item>
            <el-descriptions-item label="合同的扫描件或照片">
              <el-image
                  v-for="(item, index) in seeContractDetail.info.imgList"
                  :key= index
                  style="width: 40px; height: 40px"
                  :src="item.url"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="seeContractDetail.info.previewList"
                  :initial-index="index"
                  fit="cover"
                  :preview-teleported="true"
              />
            </el-descriptions-item>
            <el-descriptions-item label="合同的可编辑版">
              <el-button type="primary" link @click="preview_File(seeContractDetail.info.filePath)" v-if="seeContractDetail.info.filePath"><b>查看</b></el-button>
            </el-descriptions-item>
            <el-descriptions-item label="备注">{{seeContractDetail.info.memo}}</el-descriptions-item>
            <el-descriptions-item label="本版本合同的修改记录" v-show="seeContractDetail.info.origin === 'renew'">
              <el-button type="primary" link @click="manage_changeHis(seeContractDetail.info)"><b>查看</b></el-button>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="eqScanLog" width="1000" dialogTitle="装备器具管理" color="blue" :dialogHide="hideEqScanLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideEqScanLog">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div class="singleSect" v-if="eqScanList.length === 1">
          <span>基本信息</span>
        </div>
        <table class="ty-table ty-table-control">
          <tr>
            <td>
              <div>装备器具名称</div>
            </td>
            <td>
              <div>型号</div>
            </td>
            <td>
              <div>供应商/加工方</div>
            </td>
            <td>
              <div>单位</div>
            </td>
            <td>
              <div>数量</div>
            </td>
            <td>
              <div>操作</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="name">{{(eqScanDetail.equipmentName || '')}}</div>
            </td>
            <td>
              <div class="model">{{eqScanDetail.modelName || ''}}</div>
            </td>
            <td>
              <div class="sup">{{eqScanDetail.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）'}}</div>
            </td>
            <td>
              <div class="unit">{{eqScanDetail.unitName || ''}}</div>
            </td>
            <td>
              <div class="eqCount">{{eqScanDetail.length}}</div>
            </td>
            <td>
              <span class="ty-color-blue" @click="editLog(eqScanDetail,1)">修改记录</span>
            </td>
          </tr>
        </table>
        <div class="moreBd" v-if="eqScanList.length > 1"></div>
        <div class="singleSect gapTp" v-if="eqScanList.length === 1">
          <span>其他信息</span>
        </div>
        <div class="eqDetails">
          <div v-for="(item, index) in eqScanList" :key="index">
            <div>
              <span>创建： {{item.createName}} {{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
              <span>系统赋予的编码：{{item.lpadId}}</span>
              <span>装备器具的编号：{{item.modelCode}}</span>
              <span class="ty-right cateCon" title="${item.path}">类别：{{item.path}}</span>
            </div>
            <table class="ty-table ty-table-control">
              <tr>
                <td>
                  <div>到厂日期</div>
                </td>
                <td>
                  <div>到厂时的新旧情况</div>
                </td>
                <td>
                  <div>原值</div>
                </td>
                <td>
                  <div>预期的使用寿命</div>
                </td>
                <td>
                  <div>备注</div>
                </td>
                <td>
                  <div>操作</div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="factDate">{{new Date(item.receive_date).format('yyyyMM')}}</div>
                </td>
                <td>
                  <div class="newOrOld"> {{item.conditions == 1 ? '新' : '' }}{{item.conditions == 2 ? '旧' : '' }}</div>
                </td>
                <td>
                  <div class="oral">{{(item.original_value ? item.original_value + '元' : '')}}</div>
                </td>
                <td>
                  <div class="expireYear">{{item.life_span || ''}}</div>
                </td>
                <td>
                  <div class="memo" :title="(item.memo || '')">{{item.memo || ""}}</div>
                </td>
                <td>
                  <span class="ty-color-blue" @click="editLog(item, 2)">修改记录</span>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </template>
    </TyDialog>

    <TyDialog v-if="editRecordLog" width="700" dialogTitle="修改记录" color="blue" :dialogHide="hideEditRecordLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideEditRecordLog">关 闭</el-button>
      </template>
      <template #dialogBody>
        <p v-if="editRecordList.length <= 0"><span>当前资料尚未经修改。</span><span class="ty-right"> 创建人：{{ eqScanDetail.createName }} {{ new Date(eqScanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span></p>
        <div v-if="editRecordList.length > 0">
          <p>
            <span>当前资料为第{{ editRecordList.length-1 }}次修改后的结果。</span><span class="ty-right"> 修改人：{{ editRecordList[editRecordList.length-1].createName }} {{ new Date(editRecordList[editRecordList.length-1].createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </p>
          <table class="ty-table" id="editLogTab">
            <tbody>
            <tr>
              <td>资料状态</td>
              <td>操作</td>
              <td>创建人/修改人</td>
            </tr>
            <tr v-for="(item, index) in editRecordList" :key="index">
              <td>{{ (index === 0 ? '原始信息': '第' + index +'次修改后') }}</td>
              <td class="ty-td-control">
                <span class="ty-color-blue" @click="logScan(item)">查看</span>
              </td>
              <td>{{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="recordScanLog" width="1000" dialogTitle="装备器具信息查看" color="blue" :dialogHide="hideRecordScanLog">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <table class="ty-table">
          <tr>
            <td>
              <div>装备器具名称</div>
            </td>
            <td>
              <div>型号</div>
            </td>
            <td>
              <div>供应商/加工方</div>
            </td>
            <td>
              <div>单位</div>
            </td>
            <td class="baseLogScan" v-if="recordNum === 1">
              <div>数量</div>
            </td>
          </tr>
          <tr>
            <td>{{recordScan.equipmentName}}</td>
            <td>{{recordScan.modelName}}</td>
            <td>{{recordScan.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）'}}</td>
            <td>{{recordScan.unitName}}</td>
            <td class="baseLogScan" v-if="recordNum === 1">
              <div class="amountLog">{{recordScan.quantity || 0}}</div>
            </td>
          </tr>
        </table>
        <table class="ty-table subLogScan" v-if="recordNum === 2">
          <tr>
            <td>
              <div>装备器具编号</div>
            </td>
            <td>
              <div>原值</div>
            </td>
            <td>
              <div>预期的使用寿命</div>
            </td>
            <td>
              <div>到厂日期</div>
            </td>
            <td>
              <div>到厂时的新旧情况</div>
            </td>
            <td>
              <div>类别</div>
            </td>
            <td>
              <div>备注</div>
            </td>
          </tr>
          <tr>
            <td>{{recordScan.modelCode}}</td>
            <td>{{recordScan.originalValue ? recordScan.originalValue +` 元` : ''}} </td>
            <td>{{recordScan.lifeSpan}} </td>
            <td>{{new Date(recordScan.receiveDate).format('yyyyMM')}} </td>
            <td>{{recordScan.conditions == 1 ? '新' : ''}}{{recordScan.conditions == 2 ? '旧' : ''}} </td>
            <td>{{recordScan.categoryName}} </td>
            <td>{{recordScan.memo}} </td>
          </tr>
        </table>
      </template>
    </TyDialog>
  </div>
 </template>
 <script >
 import contractMixin from "@/mixins/equipManage/contractMixin";
 import {initNav} from "@/utils/routeChange";
 import uploadFile from "@/components/uploadFile.vue";
 import eqListMixin from "@/mixins/equipManage/eqListMixin";
 import eqSupplierMixin from "@/mixins/equipManage/eqSupplierMixin";
 export default {
   components: {uploadFile},
   mixins: [eqListMixin, contractMixin, eqSupplierMixin],
   data() {
     return {
       pageName: 'equipMentSupplier',
       mainNum: 'sup1',
       page: 'seeContractBySup',
       rootPath:{},

     }
   },
   mounted() {
     initNav({mid: 'qt', name: '供应商', pageName: this.pageName}, this.initIndex, this)
   },
   methods: {
     getLimitVal(name) {
       let newVal = ''
       switch (name) {
         case 'taxRate':
         case 'chargePeriod':
         case 'imprestProportion':
           newVal = this.addData.base[name]
           break;
         case 'chargePeriodMg':
           newVal = this.manageBaseData.chargePeriod
           break;
         case 'taxRateMg':
           newVal = this.manageInvoiceData.info.taxRate
           break;
       }
       let result = this.limitInput(newVal)
       switch (name) {
         case 'taxRate':
         case 'chargePeriod':
         case 'imprestProportion':
           this.addData.base[name] = result
           break;
         case 'chargePeriodMg':
           this.manageBaseData.chargePeriod = result
           break;
         case 'imprestProportionMg':
           this.manageBaseData.imprestProportion = result
           break;
         case 'taxRateMg':
           this.manageInvoiceData.info.taxRate = result
           break;
       }
     },
     limitInput(val) {
       let value =
           val
               .replace(/[^\d.]/g, "")
               .replace(/^0+(\d)/, "$1")
               .replace(/^\./, "0.")
               .match(/^\d*(\.?\d{0,2})/g)[0] || "";
       return value
     }
   }
 }
 </script>


 <style scoped lang="scss">
 @use "@/style/contrct.scss";
 @use "@/style/equiptCommon.scss";
 .equipMentSupplier {
   margin-top: 20px;
   font-size: 14px;
 }
 </style>