<template>
  <div id="discussionBorrowApprovalLast" isBackHome="false" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="讨论组阅览审批" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="tip">下列讨论组查阅申请有待您审批。</div>
    <div class="ui-cells discussBorrow">
      <a class="ui-cell" v-on:click="jump(item.id, 'borrowApproveLast')" v-for="(item, index) in toApprovalList" v-bind:key="index">
        <div class="ui-cell__bd">
          <div class="ui-cell_con">
            <div><span class="participantsNum">{{item.participantsNum}}人</span>{{item.title}}</div>
            <div class="create">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          </div>
        </div>
        <div class="ui-cell__ft"></div>
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'discussionBorrowApprovalLast',
  data () {
    return {
      loading: true,
      isBackHome: true,
      listenersUid: [],
      toApprovalList: []
    }
  },
  created: function () {
    let that = this
    this.$http.post('../../../read/getLastApproveReadForum.do', { userID: this.sphdSocket.user.userID, oid: this.sphdSocket.user.oid }, {
      emulateJSON: true
    }).then((response) => {
      let list = response.body.data
      that.toApprovalList = list.listApproveReadFile
      that.loading = false
    }).catch((err) => {
      console.log('获取数据错误了！', err)
    })
    that.setSubscribe()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    setSubscribe: function () {
      let _this = this
      _this.loading = false
      this.listenersUid = [
        this.sphdSocket.subscribe('FinalApproveReadForum', function (data) {
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            newDate = reqData.rf
            _this.toApprovalList.push(newDate)
          } else if (update < 0) { // 减少一条,文件被驳回时用
            let deleteId = reqData.rf.id
            _this.toApprovalList.forEach(function (item, index) {
              if (deleteId === item.id) {
                _this.toApprovalList.splice(index, 1)
              }
            })
          }
        }, null, 'custom', this.sphdSocket.user.oid + 'AuthApprovalgeneral')
      ]
    },
    jump: function (id, mark) {
      this.$router.push({
        path: `/discussionBorrowDetail/${id}/${mark}`
      })
    },
    screen: function () {
      this.$router.push({
        path: '/discussionBorrowQuery/lastApprove'
      })
    }
  }
}
</script>
