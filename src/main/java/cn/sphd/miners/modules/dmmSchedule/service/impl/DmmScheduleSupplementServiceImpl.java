package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmJobDao;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmScheduleSupplementDao;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmScheduleSupplementImageDao;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmJob;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleSupplement;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleSupplementImage;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleSupplementService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/14.
 */
@Service("dmmScheduleSupplementService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DmmScheduleSupplementServiceImpl implements DmmScheduleSupplementService {
    @Autowired
    DmmScheduleSupplementDao dmmScheduleSupplementDao;
    @Autowired
    DmmJobDao dmmJobDao;
    @Autowired
    DmmScheduleSupplementImageDao scheduleSupplementImageDao;
    @Autowired
    UploadService uploadService;

    @Override
    public List<DmmScheduleSupplement> getDmmScheduleSupplementListBySid(Integer scheduleId) {
        String hql=" from DmmScheduleSupplement where scheduleId=:scheduleId";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        List<DmmScheduleSupplement> dmmScheduleSupplementList=dmmScheduleSupplementDao.getListByHQLWithNamedParams(hql,params);
        return dmmScheduleSupplementList;
    }

    @Override
    public boolean addDmmScheduleSupplement(AuthAcc acc, Integer dmmJobId, String description, String[] imagePaths) {
        DmmJob dmmJob=dmmJobDao.get(dmmJobId);
        DmmScheduleSupplement dmmScheduleSupplement=new DmmScheduleSupplement();
        dmmScheduleSupplement.setCreateName(acc.getName());
//        dmmScheduleSupplement.setCreator(user.getCreator());
        dmmScheduleSupplement.setCreateDate(new Date());
//        dmmScheduleSupplement.setUpdator(user.getUserID());
        dmmScheduleSupplement.setUpdateName(acc.getName());
        dmmScheduleSupplement.setUpdateDate(new Date());
        dmmScheduleSupplement.setDescription(description);
        dmmScheduleSupplement.setSchedule(dmmJob.getSchedule());
        dmmScheduleSupplement.setMemoJobId(dmmJobId);
        dmmScheduleSupplementDao.save(dmmScheduleSupplement);
        Integer orders=1;
        for (String imagePath:imagePaths){
            DmmScheduleSupplementImage dmmScheduleSupplementImage=new DmmScheduleSupplementImage();
            dmmScheduleSupplementImage.setScheduleId(dmmJob.getScheduleId());
            dmmScheduleSupplementImage.setScheduleSupplement(dmmScheduleSupplement);
            dmmScheduleSupplementImage.setDescription(description);
            dmmScheduleSupplementImage.setCreateName(acc.getName());
//            dmmScheduleSupplementImage.setCreator(user.getCreator());
            dmmScheduleSupplementImage.setCreateDate(new Date());
//            dmmScheduleSupplementImage.setUpdator(user.getUserID());
            dmmScheduleSupplementImage.setUpdateName(acc.getName());
            dmmScheduleSupplementImage.setUpdateDate(new Date());
            dmmScheduleSupplementImage.setNormal(imagePath);
            scheduleSupplementImageDao.save(dmmScheduleSupplementImage);

            String name=dmmJob.getTitle()+"(补充记录)";
            if (imagePaths.length>1){ //多个文件
                name=name+orders;
            }
            uploadService.addFileUsing(new DmmUsing(dmmScheduleSupplementImage.getId(), dmmScheduleSupplementImage.getClass()), dmmScheduleSupplementImage.getNormal(), name, acc, "备忘与日程"); // 补充记录附件加的附件引用
            orders++;
        }
        dmmJob.setSupplement(dmmJob.getSupplement()+description);//提醒中拼上 补充内容，方便筛选
        return true;
    }

    @Override
    public boolean deleteDmmScheduleSupplement(Integer supplementId) {
        dmmScheduleSupplementDao.deleteById(supplementId);
        return true;
    }

    @Override
    public List<DmmScheduleSupplement> getDmmScheduleSupplementListByJobId(Integer dmmJobId) {
        String hql=" from DmmScheduleSupplement where memoJobId=:dmmJobId";
        Map<String,Object> params=new HashMap<>();
        params.put("dmmJobId",dmmJobId);
        List<DmmScheduleSupplement> dmmScheduleSupplementList=dmmScheduleSupplementDao.getListByHQLWithNamedParams(hql,params);
        return dmmScheduleSupplementList;
    }

    @Override
    public DmmScheduleSupplement getDmmScheduleSupplementById(Integer id) {
        return dmmScheduleSupplementDao.get(id);
    }

    @Override
    public DmmScheduleSupplementImage getDmmScheduleSupplementImageById(Integer id) {
        return scheduleSupplementImageDao.get(id);
    }
}
