package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.material.entity.*;

public interface SupplierImageService {

    PoContractBase getPoContractBase(Integer id);

    PoContractBaseHistory getPoContractBaseHistory(Integer id);

    SrmSupplier getSrmSupplier(Integer id);

    PoContractImage getPoContractImage(Integer id);

    PoContractImageHistory getPoContractImageHistory(Integer id);

    SrmSupplierContact getSrmSupplierContact(Integer id);
    SrmSupplierContactHistory getSrmSupplierContactHistory(Integer id);

    SrmSupplierImage getSrmSupplierImage(Integer id);

    SrmSupplierImageHistory getSrmSupplierImageHistory(Integer id);
}
