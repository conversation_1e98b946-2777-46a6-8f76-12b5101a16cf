<template id="error">
  <div class="error">
    <van-empty :description="message">
      <van-button round type="danger" class="bottom-button" @click="close()">关闭</van-button>
    </van-empty>
  </div>
</template>
<script>
export default {
  data () {
    return {
      message: '出错啦!'
    }
  },
  mounted () {
    let message = this.$route.params.message
    if (typeof (message) !== 'undefined') {
      this.message = message
    } else {
      this.message = '出错啦!'
    }
  },
  methods: {
    close () {
      if (typeof (window.WeixinJSBridge) !== 'undefined') {
        window.WeixinJSBridge.call('closeWindow')
      } else if (navigator.userAgent.indexOf('MSIE') > 0) {
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
          window.opener = null
          window.close()
        } else {
          window.open('', '_top')
          window.top.close()
        }
      } else {
        try {
          this.focus()
          self.opener = null
          self.close()
        } catch (e) {
        }
        try {
          window.opener = null
          window.open('', '_self', '')
          window.close()
        } catch (e) {
        }
        try {
          window.open(window.location, '_self').close()
        } catch (e) {
        }
        window.location.replace('about:blank')
      }
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  h1, h2 {
    font-weight: normal;
  }
  .error{
    background-color: #f1f1f2;
    border-radius: 4px;
    text-align: center;
    padding: 20px;
  }
  .error i{
    color: #F56C6C;
  }
  .tip{
    margin-left: 8px;
  }
</style>
