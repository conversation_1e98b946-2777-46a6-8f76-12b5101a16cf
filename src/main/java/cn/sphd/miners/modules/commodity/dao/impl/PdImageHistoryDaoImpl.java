package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdImageHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdImage;
import cn.sphd.miners.modules.commodity.entity.PdImageHistory;
import cn.sphd.miners.modules.sales.entity.SlContractBaseHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;


@Repository
public class PdImageHistoryDaoImpl extends BaseDao<PdImageHistory, Serializable> implements PdImageHistoryDao {
    @Override
    public PdImageHistory insert(PdImage pdImage) {
        PdImageHistory history=new PdImageHistory();

        BeanUtils.copyProperties(pdImage,history);
        history.setPdImage(pdImage.getId());
        history.setId(null);
        save(history);
        return history;
    }
}
