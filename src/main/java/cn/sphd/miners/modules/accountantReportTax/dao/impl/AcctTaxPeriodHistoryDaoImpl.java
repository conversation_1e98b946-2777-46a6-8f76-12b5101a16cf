package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodHistoryEntity;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class AcctTaxPeriodHistoryDaoImpl extends BaseDao<AcctTaxPeriodHistoryEntity, Serializable> implements AcctTaxPeriodHistoryDao {
}
