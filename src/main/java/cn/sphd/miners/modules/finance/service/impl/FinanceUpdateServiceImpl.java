package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxDetailHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.*;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.sales.dao.SlCollectApplicationDao;
import cn.sphd.miners.modules.sales.entity.SlCollectApplication;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/22
 * 财务修改
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceUpdateServiceImpl extends BaseServiceImpl implements FinanceUpdateService{

    @Autowired
    UserDao userDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    AccountDetailHistoryDao accountDetailHistoryDao;
    @Autowired
    FinanceChequeDetailDao financeChequeDetailDao;
    @Autowired
    FinanceAccountBillHistoryDao financeAccountBillHistoryDao;
    @Autowired
    FinanceReturnDao financeReturnDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    FinanceAccountBillImageDao financeAccountBillImageDao;
    @Autowired
    AcctTaxDetailHistoryDao taxDetailHistoryDao;
    @Autowired
    SlCollectApplicationDao slCollectApplicationDao;

    @Autowired
    DataService dataService;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    UserService userService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    FinanceChequeService financeChequeService;
    @Autowired
    FinanceReturnService financeReturnService;
    @Autowired
    AccountService accountService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    UploadService uploadService;
    @Autowired
    AcctReportTaxService acctReportTaxService;

    /**
     *提交纯现金/银行转账修改申请
     * @param userId  用户id
     * @param detailId 详情id
     * @param genre  类别
     * @param categoryDesc  当类别为其他时候的说明
     * @param summary  摘要
     * @param credit  收入
     * @param debit  支出
     * @param purpose  用途
     * @param auditorName  经手人
     * @param oppositeCorp  付款单位
     * @param memo 备注
     * @param receiveAccountDate 到账时间
     * @param accountId  账户id
     * @param billQuantity  票据数量
     * @param billAmount  票面金额
     * @param stakeholderCategory  类别1-供应商,2-员工,3-自行录入
     * @param stakeholder  干系人ID
     * @param billDate  票据日期
     * @param factDate 实际(付款/支出)日期
     * @param imgType  1-图片有修改  0-图片未修改
     * @param imgPaths 图片链接
     * @return
     */
    @Override
    public Map<String, Object> updateCash(Integer userId, Integer detailId, String genre,String categoryDesc, String summary, BigDecimal credit,
           BigDecimal debit, String purpose, String auditorName, String oppositeCorp, String memo, Date receiveAccountDate, Integer accountId,
           Integer billQuantity, BigDecimal billAmount,String stakeholderCategory,Integer stakeholder, Date billDate,Date factDate,Integer imgType,String imgPaths,
           Integer taxId,String taxCategoryName,Integer report,Integer businessPeriodBegin,Integer businessPeriodEnd,String periodBegin,String periodEnd,
           Integer taxPeriodIdInsert,Integer taxPeriodIdUpdate) {
        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
        AccountDetail oldDetail = new AccountDetail();
        AccountDetailHistory accountDetailHistory = new AccountDetailHistory();
        Double balance = 0.0;
        Double balance1 = 0.0;
        FinanceAccount financeAccount = new FinanceAccount();
        FinanceAccount financeAccount1 = new FinanceAccount();

        String des="";  //存修改字段的数据
        if (detailId!=null){
            oldDetail = accountDetailDao.get(detailId);  //原来的数据(与要修改的数据进行对比accountDetail)
//            FinanceAccountBill financeAccountBill = oldDetail.getBillDetail();
//            if (financeAccountBill!=null){
////                oldDetail.setBillPeriod(financeAccountBill.getBillPeriod());  //票据所属月份
////                oldDetail.setBillCat(financeAccountBill.getBillCat()); //票据类型
//                oldDetail.setBillQuantity(financeAccountBill.getBillQuantity());  //票据数量
//            }

            //校验数据是否已修改
            if (!MyStrings.nulltoempty(oldDetail.getModityStatus()).isEmpty() && "2".equals(oldDetail.getModityStatus())){
                map.put("state",3);  //此数据已修改
                map.put("content","已修改，不可重复操作");  //此数据已修改
            }else {
                financeAccount = financeAccountDao.get(oldDetail.getAccountId().getId());  //原来的账户

                BeanUtils.copyProperties(oldDetail,accountDetailHistory);

                if (StringUtils.isNotEmpty(genre) && !genre.equals(oldDetail.getGenre())){
                    accountDetailHistory.setGenre(genre); //类别
                    accountDetailHistory.setCategoryDesc(categoryDesc);
                    des+="1"+";";
//                    descriptions+="类别"+"、";
                }else {
                    accountDetailHistory.setGenre(oldDetail.getGenre());
                    accountDetailHistory.setCategoryDesc(oldDetail.getCategoryDesc());  //当类别为其他时存入
                }

                if (StringUtils.isNotEmpty(summary)&&!summary.equals(oldDetail.getSummary())) {
                    accountDetailHistory.setSummary(summary);  //摘要
                    des += "2" + ";";
//                    descriptions+="摘要"+"、";
                } else {
                    accountDetailHistory.setSummary(oldDetail.getSummary());
                }

                if (credit!=null && credit.doubleValue()!=oldDetail.getCredit().doubleValue()){
                    //收入
                    accountDetailHistory.setCredit(credit);
//                    balance = financeAccount.getBalance().subtract(oldDetail.getCredit()).add(credit).doubleValue();
                    //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                    //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
                    BigDecimal me = new BigDecimal(oldDetail.getCredit().toString());
                    BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
                    balance = ba.subtract(me).add(credit).doubleValue();
                    des+="3"+";";
//                    descriptions+="金额"+"、";
                }else {
                    accountDetailHistory.setCredit(oldDetail.getCredit());
                }

                if (debit!=null && debit.doubleValue()!=oldDetail.getDebit().doubleValue()){
                    //支出
                    accountDetailHistory.setDebit(debit);
//                    balance = financeAccount.getBalance().add(oldDetail.getDebit()).subtract(debit).doubleValue();
                    //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                    //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
                    BigDecimal me = new BigDecimal(debit.toString());
                    BigDecimal he = financeAccount.getBalance().add(oldDetail.getDebit());
                    BigDecimal ba = new BigDecimal(he.toString());
                    balance = ba.subtract(me).doubleValue();
                    des+="4"+";";
//                    descriptions+="金额"+"、";
                }else {
                    accountDetailHistory.setDebit(oldDetail.getDebit());
                }

                if (StringUtils.isNotEmpty(purpose) && !purpose.equals(oldDetail.getPurpose())){
                    accountDetailHistory.setPurpose(purpose);  //用途
                    des+="5"+";";
//                    descriptions+="用途"+"、";
                }else {
                    accountDetailHistory.setPurpose(oldDetail.getPurpose());
                }
                if (StringUtils.isNotEmpty(auditorName) && !auditorName.equals(oldDetail.getAuditorName())){
                    accountDetailHistory.setAuditorName(auditorName);  //经手人
                    des+="6"+";";
//                    descriptions+="经手人"+"、";
                }else {
                    accountDetailHistory.setAuditorName(oldDetail.getAuditorName());  //经手人
                }

                if (StringUtils.isNotEmpty(oppositeCorp)&&!oppositeCorp.equals(oldDetail.getOppositeCorp()) || stakeholder!=null&&!oldDetail.getStakeholder().equals(stakeholder)){
                    accountDetailHistory.setOppositeCorp(oppositeCorp);  //付款单位(收款单位)
                    accountDetailHistory.setStakeholder(stakeholder);  //干系人ID
                    des+="7"+";";
//                    descriptions+="付款单位"+"、";
                }else {
                    accountDetailHistory.setOppositeCorp(oldDetail.getOppositeCorp());
                    accountDetailHistory.setStakeholder(oldDetail.getStakeholder());
                }

                if (StringUtils.isNotEmpty(memo) && !memo.equals(oldDetail.getMemo())){
                    accountDetailHistory.setMemo(memo);  //备注
                    des+="8"+";";
//                    descriptions+="备注"+"、";
                }else {
                    accountDetailHistory.setMemo(oldDetail.getMemo());
                }

                if (receiveAccountDate!=null && NewDateUtils.today(receiveAccountDate).getTime()!= NewDateUtils.today(oldDetail.getReceiveAccountDate()).getTime()){
                    accountDetailHistory.setReceiveAccountDate(receiveAccountDate);  //到账时间
                    des+="9"+";";
                }else {
                    if (oldDetail.getReceiveAccountDate()!=null) {
                        accountDetailHistory.setReceiveAccountDate(NewDateUtils.today(oldDetail.getReceiveAccountDate()));
                    }
                }

                if (accountId!=null && !accountId.equals(oldDetail.getAccountId().getId())){
                        financeAccount1 = financeAccountDao.get(accountId);
                        if (!"".equals(financeAccount1.getAccount())&&financeAccount1.getAccount()!=null){  //银行账户
                            accountDetailHistory.setAccountBank(financeAccount1.getBankName()+financeAccount1.getAccount());
                        }else {
                            accountDetailHistory.setAccountBank(financeAccount1.getBankName());
                        }

                        if (accountDetailHistory.getDebit()!=null){
//                            balance1 = financeAccount1.getBalance().subtract(accountDetailHistory.getDebit()).doubleValue();
                            //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                            //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
                            BigDecimal me = new BigDecimal(accountDetailHistory.getDebit().toString());
                            BigDecimal ba = new BigDecimal(financeAccount1.getBalance().toString());
                            balance1 = ba.subtract(me).doubleValue();
                        }
                        accountDetailHistory.setAccountId(accountId);
                        des+="10"+";";
//                        descriptions+="银行账户"+"、";
                }else {
                    accountDetailHistory.setAccountId(financeAccount.getId());
                    accountDetailHistory.setAccountBank(oldDetail.getAccountBank());
                }

//                if (billCat!=null && !billCat.equals(oldDetail.getBillCat())){
//                    accountDetailHistory.setBillCat(billCat);  //票据类型
//                    des+="11"+";";
////                    descriptions+="票据类型"+"、";
//                }else {
//                    accountDetailHistory.setBillCat(oldDetail.getBillCat());
//                }

                if (billQuantity!=null && billQuantity.compareTo(oldDetail.getBillQuantity())!=0){
                    accountDetailHistory.setBillQuantity(billQuantity);  //票据数量
                    des+="12"+";";
//                    descriptions+="票据数量"+"、";
                }else {
                    accountDetailHistory.setBillQuantity(oldDetail.getBillQuantity());
                }

//                if (billPeriod!=null && !billPeriod.equals(oldDetail.getBillPeriod())){
//                    accountDetailHistory.setBillPeriod(billPeriod); //票据所属月份
//                    des+="13"+";";
////                    descriptions+="票据所属月份"+"、";
//                }else {
//                    accountDetailHistory.setBillPeriod(oldDetail.getBillPeriod());
//                }

                if (billAmount!=null && billAmount.doubleValue()!=oldDetail.getBillAmount().doubleValue()){
                    accountDetailHistory.setBillAmount(billAmount);  //票面金额
                    des+="14"+";";
//                    descriptions+="票面金额"+"、";
                }else {
                    accountDetailHistory.setBillAmount(oldDetail.getBillAmount());
                }

                if (StringUtils.isNotEmpty(stakeholderCategory) && !oldDetail.getStakeholderCategory().equals(stakeholderCategory)){
                    accountDetailHistory.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
                    des+="15"+";";
                }else {
                    accountDetailHistory.setStakeholderCategory(oldDetail.getStakeholderCategory());
                }

                if (billDate!=null&&oldDetail.getBillDate()!=billDate){
                    accountDetailHistory.setBillDate(billDate);  //票据日期
                    des+="16"+";";
                }else {
                    accountDetailHistory.setBillDate(oldDetail.getBillDate());
                }

                if (factDate!=null&&oldDetail.getFactDate()!=factDate){
                    accountDetailHistory.setFactDate(factDate);  //实际(付款/支出)日期
                    des+="17"+";";
                }else {
                    accountDetailHistory.setFactDate(oldDetail.getFactDate());
                }
                if (imgType!=null&&1==imgType){  //图片修改了
                    des+="18"+";";
                }

                if ("5".equals(oldDetail.getSubType())) {  //税款时单独处理
                    String summary1 = null;  //是否有修改的摘要,摘要展示为“XXXXXXXX-XXXXXXXX期间的税款（XXXXXXXXX）”，其中“日期”取该税款的所属时期，括号内取相应税种的名称
                    if (businessPeriodBegin!=null&&!businessPeriodBegin.equals(oldDetail.getBusinessPeriodBegin())){ //所属时期开始时间
                        accountDetailHistory.setBusinessPeriodBegin(businessPeriodBegin);
                        summary1=businessPeriodBegin.toString();
                        accountDetailHistory.setBillDate(NewDateUtils.dateFromString(businessPeriodBegin.toString(),"yyyy-MM-dd")); //票据日期
                        des+="19"+";";
                    }else {
                        accountDetailHistory.setBusinessPeriodBegin(oldDetail.getBusinessPeriodBegin());
                        accountDetailHistory.setBillDate(oldDetail.getBillDate());
                        summary1=oldDetail.getBusinessPeriodBegin().toString();
                    }
                    summary1+="-";
                    if (businessPeriodEnd!=null&&!businessPeriodEnd.equals(oldDetail.getBusinessPeriodEnd())){  //所属时期结束时间
                        accountDetailHistory.setBusinessPeriodEnd(businessPeriodEnd);
                        summary1+=businessPeriodEnd.toString();
                        des+="20"+";";
                    }else {
                        accountDetailHistory.setBusinessPeriodEnd(oldDetail.getBusinessPeriodEnd());
                        summary1+=oldDetail.getBusinessPeriodEnd().toString();
                    }
                    summary1+="期间的税款（";
                    Map<String,Object> res = acctReportTaxService.getById(oldDetail.getBusiness());
                    if (taxId != null && !taxId.equals(oldDetail.getBusinessKey())) {  //报税id
                        accountDetailHistory.setBusinessKey(taxId);
                        summary1+=taxCategoryName;
                        des+="21"+";";
                    } else {
                        accountDetailHistory.setBusinessKey(oldDetail.getBusinessKey());
                        summary1+= (String) res.get("taxName");  //税种名称
                    }
                    accountDetailHistory.setSummary(summary1);
                    accountDetailHistory.setPurpose(summary1);

                    String periodBegin1 = (String) res.get("periodBegin"); //税款的申报记录开始时间
                    String periodEnd1 = (String) res.get("periodEnd"); //税款的申报记录结束时间
                    if (businessPeriodBegin!=null&&!businessPeriodBegin.equals(oldDetail.getBusinessPeriodBegin())      //所属时期开始时间
                            ||businessPeriodEnd!=null&&!businessPeriodEnd.equals(oldDetail.getBusinessPeriodEnd())     //所属时期结束时间
                            ||taxId != null && !taxId.equals(oldDetail.getBusinessKey())    //报税id
                            ||debit!=null && debit.doubleValue()!=oldDetail.getDebit().doubleValue()   //金额
                            ||StringUtils.isNotEmpty(periodBegin)&&!periodBegin1.equals(periodBegin)    //税款的申报记录开始时间
                            ||StringUtils.isNotEmpty(periodEnd)&&!periodEnd1.equals(periodEnd)
                            ||!taxPeriodIdInsert.equals(taxPeriodIdUpdate)){     //申报记录id
                        Integer acctTaxDetailId = dataService.setTaxFactAmount(user, taxId, report, debit, periodBegin.substring(0, 7), accountDetailHistory.getFactDate(), oldDetail.getId(),businessPeriodBegin,businessPeriodEnd,periodBegin,periodEnd,taxPeriodIdInsert,taxPeriodIdUpdate);
                        accountDetailHistory.setBusiness(acctTaxDetailId);
                    }else {
                        accountDetailHistory.setBusiness(oldDetail.getBusiness());
                    }
                }

                accountDetailHistory.setCreator(userId);
                accountDetailHistory.setCreateName(user.getUserName());
                accountDetailHistory.setCreateDate(new Date());
                accountDetailHistory.setDetailId(oldDetail);
                accountDetailHistory.setApproveStatus("1");//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核',
                accountDetailHistory.setPreviousId(null);
                accountDetailHistory.setSubType(oldDetail.getSubType());

                //审批流程
                ApprovalProcess approvalProcess = new ApprovalProcess();
                User administrators = userService.getUserByRoleCode(user.getOid(),"super");  //查找机构下的超管
                approvalProcess.setLevel(1);  //审批级次
                approvalProcess.setApproveStatus("1");  //审批状态
                approvalProcess.setToUser(administrators.getUserID());  //审批人
                approvalProcess.setBusiness(oldDetail.getId()); //数据修改前的id（财务修改 修改前的id（本表中））
                approvalProcess.setOldId(oldDetail.getId());  //财务修改 修改前的id（本表中）
                approvalProcess.setBusinessType(1); //业务类型  1-财务修改，2- 加班，3-请假
                approvalProcess.setToUserName("超管");  //审批人总称
                approvalProcess.setUserName(administrators.getUserName());  //审批人名字
                approvalProcess.setUpdateDes(des);  //修改说明(需要修改的字段使用数字代替)
                String descriptions = "";
                if ("2".equals(oldDetail.getType())){  //转账交易
//                    descriptions = "关于"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(oldDetail.getCreateDate())+"所录入的财务转账的修改";
                    descriptions = "关于"+NewDateUtils.dateToString(oldDetail.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"所录入的财务转账的修改";
                }else if ("3".equals(oldDetail.getType())){  //收入
//                    descriptions = "关于"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(oldDetail.getCreateDate())+"所录入的财务收入的修改";
                    descriptions = "关于"+NewDateUtils.dateToString(oldDetail.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"所录入的财务收入的修改";
                }else if ("4".equals(oldDetail.getType())){  //支出
//                    descriptions = "关于"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(oldDetail.getCreateDate())+"所录入的财务支出的修改";
                    descriptions = "关于"+NewDateUtils.dateToString(oldDetail.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"所录入的财务支出的修改";
                }
                approvalProcess.setDescription(descriptions);  //修改记录中的申请事件/标题
                approvalProcess.setAskName(user.getUserName());  //申请人
                approvalProcess.setFromUser(user.getUserID());  //申请人id
                approvalProcess.setType(1);  //状态 1-纯现金/银行转账 2-内部非支出性转账 3-报销 4-承兑汇票和转账支票中的外部支票 5-数据来源为支出中的转账支票(内部的)
                approvalProcess.setCreateDate(new Date());
                approvalProcess.setOrg(user.getOid());

                if (balance>=0 && balance1>=0){
                    accountDetailHistoryDao.save(accountDetailHistory);
                    approvalProcess.setNewId(accountDetailHistory.getId()); //财务修改 修改中的id（历史表中的id）
                    approvalProcessDao.save(approvalProcess);
                    oldDetail.setModityStatus("2");
                    accountDetailDao.update(oldDetail);

                    if (imgType!=null&&1==imgType){
                        JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                        List imgPathList = (List) JSONArray.toCollection(jsonValues);
                        for (int i=0;i<imgPathList.size();i++){
                            FinanceAccountBillImageHistory financeAccountBillImageHistory = dataService.addBillImageHistory(user,oldDetail.getId(),accountDetailHistory.getId(),null,null,"1",imgPathList.get(i).toString(),i+1);

                            FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImageHistory.getId(),FinanceAccountBillImageHistory.class);
                            uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表
                        }
                    }else {  //不修改的-将原本的数据保存
                        List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(user.getOid(),oldDetail.getId(),null);
                        for (FinanceAccountBillImage financeAccountBillImage:financeAccountBillImages) {
                            dataService.addBillImageHistory(user,oldDetail.getId(),accountDetailHistory.getId(),null,null,"1",financeAccountBillImage.getUplaodPath(),financeAccountBillImage.getOrders());
                        }
                    }

                    //给财务修改-申请人的待处理发送
                    this.updateRejectSend(0,1,approvalProcess,userId,"/updateApplyHandle",null,null,"financeModify");

                    //给财务修改-审批人的待处理发送
                    this.updateRejectSend(1,1,approvalProcess,approvalProcess.getToUser(),"/updateApprovalHandle","有一条申请待审批",descriptions,"financeModifyApproval");

                    map.put("state",1);  //修改成功
                    map.put("content","操作成功");  //修改成功
                }else {
                    map.put("state",2);  //余额不足
                    map.put("content","余额不足");  //修改成功
                }
            }
        }else {
            map.put("state",0);  //修改失败
            map.put("content","操作失败");
        }
        return map;
    }

    //财务修改 长连接推送   pass 通道  superscript 角标
    public void updateRejectSend(int loginNum, int operate, ApprovalProcess approvalProcess, Integer toUserId, String pass, String title, String content, String code){
        System.out.println("财务修改推送开始:"+new Date());
        System.out.println("审批流程id："+approvalProcess.getId()+" userId: "+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("approvalProcess", approvalProcess);
        User approvalUser= userService.getUserByID(toUserId); //推送人
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,approvalUser,code);
        System.out.println("财务修改推送结束:"+new Date());

    }

    /**
     *数据来源为内部非支出性转账的修改申请
     * @param userId  用户id
     * @param detailId  详情id
     * @param credit 收入
     * @param debit 支出
     * @param auditDate  业务发生时间
     * @param chequeId 支票id
     * @param memo  备注
     * @return
     */
    @Override
    public Map<String, Object> updateTransferAccounts(Integer userId,Integer detailId,BigDecimal credit,BigDecimal debit,Date auditDate,Integer chequeId,String memo) {
        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
        AccountDetailHistory accountDetailHistory = new AccountDetailHistory();
        AccountDetail oldDetail = new AccountDetail();
        FinanceAccount financeAccount = new FinanceAccount();
        FinanceAccount financeAccount1 = new FinanceAccount();
        Double balance = 0.0; //余额
        Double balance1 = 0.0;  //余额
        String des = "";  //修改数据
//        String descriptions = "";  //消息中的申请事件使用
        if (detailId!=null){
            oldDetail = accountDetailDao.get(detailId);  //修改前的数据
            financeAccount = financeAccountDao.get(oldDetail.getAccountId().getId());//本账户
            financeAccount1 = financeAccountDao.get(oldDetail.getOppositeId());//另一个账户的信息

            //校验数据是否已修改
            if (!MyStrings.nulltoempty(oldDetail.getModityStatus()).isEmpty() && "2".equals(oldDetail.getModityStatus())) {
                map.put("state",3);  //次数据已修改
                map.put("content","已修改，不可重复修改");
            }else {

                BeanUtils.copyProperties(oldDetail,accountDetailHistory);
                if (chequeId!=null){
                    if (!chequeId.equals(oldDetail.getBillDetail().getCheque().getId())){
                        FinanceChequeDetail financeChequeDetail = financeChequeDetailDao.get(chequeId);  //新支票的信息

                        FinanceAccountBillHistory financeAccountBillHistory = new FinanceAccountBillHistory();
                        financeAccountBillHistory.setCheque(financeChequeDetail.getId());  //现金支票id
                        financeAccountBillHistory.setBillNo(financeChequeDetail.getChequeNo()); //支票号
                        financeAccountBillHistory.setCreateDate(new Date());
                        financeAccountBillHistory.setCreateName(user.getUserName());
                        financeAccountBillHistory.setCreator(user.getUserID());
                        financeAccountBillHistory.setApproveStatus("1");//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核',
                        financeAccountBillHistory.setBillDetail(oldDetail.getBillDetail()); //bill表的id
                        if (credit!=null){
                            financeAccountBillHistory.setBillAmount(credit);
                            financeAccountBillHistory.setAmount(credit);
                        }
                        if (debit!=null){
                            financeAccountBillHistory.setBillAmount(debit);
                            financeAccountBillHistory.setAmount(debit);
                        }
                        financeAccountBillHistory.setAuditDate(auditDate);//业务发生时间
                        financeAccountBillHistory.setMemo(memo);//备注
                        financeAccountBillHistoryDao.save(financeAccountBillHistory);  //新支票信息添加到bill历史表中

                        accountDetailHistory.setBillHistoryId(financeAccountBillHistory.getId());
                        des+="1"+";";  //现金支票修改(支票号)
//                        descriptions+="支票号"+"、";
                    }else {
                        accountDetailHistory.setChequeId(oldDetail.getBillDetail().getCheque().getId());
                        accountDetailHistory.setChequeNo(oldDetail.getBillDetail().getCheque().getChequeNo());
                    }
                }

                if (credit!=null && credit.doubleValue()!=oldDetail.getCredit().doubleValue()){
                    accountDetailHistory.setCredit(credit);  //收入
//                    balance = financeAccount.getBalance().subtract(oldDetail.getCredit()).add(credit).doubleValue();  //这个是收入
//                    balance1 = financeAccount1.getBalance().add(oldDetail.getCredit()).subtract(credit).doubleValue();  //相对的这个账户为支出

                    //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                    //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
                    BigDecimal me = new BigDecimal(oldDetail.getCredit().toString());
                    BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
                    balance = ba.subtract(me).add(credit).doubleValue();  //这个是收入

                    BigDecimal me1 = new BigDecimal(credit.toString());
                    BigDecimal he = financeAccount1.getBalance().add(oldDetail.getCredit());
                    BigDecimal ba1 = new BigDecimal(he.toString());
                    balance1 = ba1.subtract(me1).doubleValue();  //相对的这个账户为支出

                    des+="2"+";";
//                    descriptions+="金额"+"、";
                }else {
                    accountDetailHistory.setCredit(oldDetail.getCredit());
                }

                if (debit!=null && debit.doubleValue()!=oldDetail.getDebit().doubleValue()){
                    accountDetailHistory.setDebit(debit);  //支出
//                    balance = financeAccount.getBalance().add(oldDetail.getDebit()).subtract(debit).doubleValue();  //这个账户是支出
//                    balance1 = financeAccount.getBalance().subtract(oldDetail.getDebit()).add(debit).doubleValue();  //相对的这个账户为收入

                    //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                    //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
                    BigDecimal me = new BigDecimal(debit.toString());
                    BigDecimal he = financeAccount.getBalance().add(oldDetail.getDebit());
                    BigDecimal ba = new BigDecimal(he.toString());
                    balance = ba.subtract(me).doubleValue();  //这个账户是支出

                    BigDecimal me1 = new BigDecimal(oldDetail.getDebit().toString());
                    BigDecimal ba1 = new BigDecimal(financeAccount.getBalance().toString());
                    balance1 = ba1.subtract(me1).add(debit).doubleValue();  //相对的这个账户为收入
                    des+="3"+";";
//                    descriptions+="金额"+"、";
                }else {
                    accountDetailHistory.setDebit(oldDetail.getDebit());
                }

                if (auditDate!=null && NewDateUtils.today(auditDate).getTime()!=NewDateUtils.today(oldDetail.getAuditDate()).getTime()){
                    accountDetailHistory.setAuditDate(NewDateUtils.today(auditDate));  //业务发生时间
                    des += "4" + ";";
                }else {
                    if (oldDetail.getAuditDate()!=null) {
                        accountDetailHistory.setAuditDate(NewDateUtils.today(oldDetail.getAuditDate()));
                    }
                }

                if (!memo.equals(oldDetail.getMemo())){
                    accountDetailHistory.setMemo(memo);  //备注
                    des+="5"+";";
//                    descriptions+="备注"+"、";
                }else {
                    accountDetailHistory.setMemo(oldDetail.getMemo());
                }
                accountDetailHistory.setCreator(userId);
                accountDetailHistory.setCreateName(user.getUserName());
                accountDetailHistory.setCreateDate(new Date());
                accountDetailHistory.setDetailId(oldDetail);
                accountDetailHistory.setApproveStatus("1");//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核',
                accountDetailHistory.setPreviousId(null);

                //审批流程
                ApprovalProcess approvalProcess = new ApprovalProcess();
                User administrators = userService.getUserByRoleCode(user.getOid(),"super");  //查找机构下的超管
                approvalProcess.setLevel(1);  //审批级次
                approvalProcess.setApproveStatus("1");  //审批状态
                approvalProcess.setToUser(administrators.getUserID());  //审批人
                approvalProcess.setBusiness(oldDetail.getId()); //数据修改前的id（财务修改 修改前的id（本表中））
                approvalProcess.setOldId(oldDetail.getId());  //财务修改 修改前的id（本表中）
                approvalProcess.setBusinessType(1); //业务类型  1-财务修改，2- 加班，3-请假
                approvalProcess.setToUserName("超管");  //审批人总称
                approvalProcess.setUserName(administrators.getUserName());  //审批人名字
                approvalProcess.setUpdateDes(des);  //修改说明(需要修改的字段使用数字代替)
                approvalProcess.setDescription("关于"+NewDateUtils.dateToString(oldDetail.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"所录入的财务转账的修改");  //修改记录中的申请事件/标题
                approvalProcess.setAskName(user.getUserName());  //申请人
                approvalProcess.setFromUser(user.getUserID());  //申请人id
                approvalProcess.setType(2);  //状态 1-纯现金/银行转账 2-内部非支出性转账 3-报销 4-承兑汇票和转账支票中的外部支票 5-数据来源为支出中的转账支票(内部的)
                approvalProcess.setCreateDate(new Date());
                approvalProcess.setOrg(user.getOid());

                if (balance>=0 && balance1>=0){
                    dataService.addAccountDetailHistory(accountDetailHistory);
                    approvalProcess.setNewId(accountDetailHistory.getId()); //财务修改 修改中的id（历史表中的id）
                    approvalProcessDao.save(approvalProcess);
                    oldDetail.setModityStatus("2");
                    accountDetailDao.update(oldDetail);

                    if (oldDetail.getDetailId()!=null){
                        AccountDetail lingYiDetail = dataService.getOneDetail(oldDetail.getDetailId());
                        lingYiDetail.setModityStatus("2");
                        accountDetailDao.update(lingYiDetail);
                    }

                    //给财务修改-申请人的待处理发送
                    this.updateRejectSend(0,1,approvalProcess,userId,"/updateApplyHandle",null,null,"financeModify");

                    //给财务审批-审批人的待处理发送
                    this.updateRejectSend(1,1,approvalProcess,approvalProcess.getToUser(),"/updateApprovalHandle","有一条申请待审批","关于"+NewDateUtils.dateToString(oldDetail.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"所录入的财务转账的修改","financeModifyApproval");

                    map.put("state",1);
                    map.put("content","操作成功");
                }else {
                    map.put("state",2);  //余额不足
                    map.put("content","余额不足");
                }
            }
        } else {
            map.put("state",0);
            map.put("content","操作失败");
        }
        return map;
    }

    /**
     * 承兑汇票和转账支票中支票的修改申请
     * @param userId  用户id
     * @param returnId  票据详情id
     * @param accountId  存入银行id
     * @param depositDate  存入时间
     * @param depositorName  存入经手人
     * @param receiveAccountDate  //到账时间
     * @param imgType  1-图片有修改  0-图片未修改
     * @param imgPaths 图片链接
     * @param updateType 1-修改其他信息(修改存入银行的信息，默认) 2-更换承兑汇票
     * @param returnIdNew 新的票据id
     * @return
     */
    @Override
    public Map<String, Object> updateReturn(Integer userId, Integer returnId, Integer accountId, Date depositDate, String depositorName, Date receiveAccountDate,Integer imgType,String imgPaths,Integer updateType,Integer returnIdNew) {
        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
        FinanceReturn oldReturn = new FinanceReturn();
        FinanceAccountBill financeAccountBill = new FinanceAccountBill();
        FinanceAccount f = new FinanceAccount();
        FinanceReturn newReturn = new FinanceReturn();
        FinanceAccountBill newFinanceAccountBill = new FinanceAccountBill();

        Double balance = 0.0;
        String des = "";
        if (returnId!=null){
            oldReturn = financeReturnDao.get(returnId); //原来的票据信息
            financeAccountBill = dataService.getAccountBillByReturnId(returnId);//（老数据）通过returnId查找bill表的数据
            f = financeAccountDao.get(oldReturn.getAccountId());  //老账户

            //此数据是否已修改
            if (!"".equals(oldReturn.getModityStatus()) && "2".equals(oldReturn.getModityStatus())) {
                map.put("state",3);  //次数据已修改
                map.put("content","已修改，不可重复修改");
            }else {
                FinanceAccountBillHistory financeAccountBillHistory = new FinanceAccountBillHistory();
                if (!accountId.equals(oldReturn.getAccountId())) {  //存入银行账户
                    FinanceAccount financeAccountNew = financeAccountDao.get(accountId);  //修改后的账户
                    financeAccountBillHistory.setAccountId(accountId); //存入银行账户的id
                    financeAccountBillHistory.setSaveBankName(financeAccountNew.getBankName()); //存入银行的银行名称
                    financeAccountBillHistory.setAccout(financeAccountNew.getAccount());  //存入银行账户
//                    financeAccountBillHistory.setReturnBill(oldReturn.getId());  //汇款票据id

                    //换票或者换账户，只需要处理旧账户和就票得相减即可，对于新账户则是加得。
                    BigDecimal me = new BigDecimal(oldReturn.getAmount().toString());
                    BigDecimal ba = new BigDecimal(f.getBalance().toString());
                    balance = ba.subtract(me).doubleValue();  //相对的这个账户为收入

                    des += "1" + ";";
//                    descriptions+="存入银行账户"+"、";
                } else {
                    financeAccountBillHistory.setAccountId(oldReturn.getAccountId()); //存入银行账户的id
                    financeAccountBillHistory.setSaveBankName(oldReturn.getSaveBankName()); //存入银行的银行名称
                    financeAccountBillHistory.setAccout(oldReturn.getAccout());  //存入银行账户
                    financeAccountBillHistory.setReturnBill(oldReturn.getId());  //汇款票据id
                }

                if (depositDate != null && NewDateUtils.today(depositDate).getTime() != NewDateUtils.today(oldReturn.getDepositDate()).getTime()) {
                    financeAccountBillHistory.setDepositDate(NewDateUtils.today(depositDate));  //存入时间
                    des += "2" + ";";
                } else {
                    if (oldReturn.getDepositDate() != null)
                        financeAccountBillHistory.setDepositDate(NewDateUtils.today(oldReturn.getDepositDate()));  //存入时间
                }
                if (!depositorName.equals(oldReturn.getDepositorName())) {
                    financeAccountBillHistory.setDepositorName(depositorName);  //存入经手人
                    des += "3" + ";";
//                    descriptions+="存入经手人"+"、";
                } else {
                    financeAccountBillHistory.setDepositorName(oldReturn.getDepositorName());  //存入经手人
                }
                if (receiveAccountDate != null&&oldReturn.getReceiveAccountDate()!=null && NewDateUtils.today(receiveAccountDate).getTime() != NewDateUtils.today(oldReturn.getReceiveAccountDate()).getTime()) {
                    financeAccountBillHistory.setReceiveAccountDate(NewDateUtils.today(receiveAccountDate)); //到账时间
                    des += "4" + ";";
                } else {
                    if (oldReturn.getReceiveAccountDate() != null)
                        financeAccountBillHistory.setReceiveAccountDate(NewDateUtils.today(oldReturn.getReceiveAccountDate())); //到账时间
                }

                if (imgType != null && 1 == imgType) {  //图片修改了
                    des += "5" + ";";
                }

                if (returnIdNew!=null&&updateType==2){   //换承兑汇票
                    newReturn = financeReturnDao.get(returnIdNew); //新的票据信息
                    newFinanceAccountBill = dataService.getAccountBillByReturnId(returnIdNew);//（新数据）通过returnId查找bill表的数据

                    if (accountId.equals(oldReturn.getAccountId())) {   //没换账户得
                        BigDecimal meOld = new BigDecimal(oldReturn.getAmount().toString());  //老票据的金额
//                        BigDecimal meNew = new BigDecimal(newReturn.getAmount().toString());  //新票据的金额
                        BigDecimal ba = new BigDecimal(f.getBalance().toString());  //账户中的余额
//                        balance = ba.subtract(meOld).add(meNew).doubleValue();  //没换账户，此时的这个账户为收入(换的新票据是收入)
                        balance = ba.subtract(meOld).doubleValue();  //没换账户，此时的这个账户为收入(换的新票据是收入)
                    }
                    financeAccountBillHistory.setReturnBill(newReturn.getId());  //汇款票据id
                    financeAccountBillHistory.setBillDetail(newFinanceAccountBill);
                    financeAccountBillHistory.setBillDate(newFinanceAccountBill.getBillDate());

                    des = "7";  //更换所存入的承兑汇票(更换票据后就只有这个修改说明)
                    if (oldReturn.getType()==1){
                        des = "6";  //更换所存入的转账支票
                    }
                }else {
                    financeAccountBillHistory.setReturnBill(oldReturn.getId());  //汇款票据id
                    financeAccountBillHistory.setBillDetail(financeAccountBill);
                    financeAccountBillHistory.setBillDate(financeAccountBill.getBillDate());
                }

                financeAccountBillHistory.setCreator(userId);
                financeAccountBillHistory.setCreateDate(new Date());
                financeAccountBillHistory.setCreateName(user.getUserName());
                financeAccountBillHistory.setApproveStatus("1");

                //审批流程
                ApprovalProcess approvalProcess = new ApprovalProcess();
                User administrators = userService.getUserByRoleCode(user.getOid(),"super");  //查找机构下的超管
                approvalProcess.setLevel(1);  //审批级次
                approvalProcess.setApproveStatus("1");  //审批状态
                approvalProcess.setToUser(administrators.getUserID());  //审批人
                approvalProcess.setBusiness(financeAccountBill.getId()); //数据修改前的id（财务修改 修改前的id（本表中））
                approvalProcess.setOldId(financeAccountBill.getId());  //财务修改 修改前的id（本表中）
                approvalProcess.setBusinessType(1); //业务类型  1-财务修改，2- 加班，3-请假
                approvalProcess.setToUserName("超管");  //审批人总称
                approvalProcess.setUserName(administrators.getUserName());  //审批人名字
                approvalProcess.setUpdateDes(des);  //修改说明(需要修改的字段使用数字代替)
                approvalProcess.setDescription("关于"+NewDateUtils.dateToString(oldReturn.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"所录入的财务收入的修改");  //修改记录中的申请事件/标题
                approvalProcess.setAskName(user.getUserName());  //申请人
                approvalProcess.setFromUser(user.getUserID());  //申请人id
                approvalProcess.setType(4);  //状态 1-纯现金/银行转账 2-内部非支出性转账 3-报销 4-承兑汇票和转账支票中的外部支票 5-数据来源为支出中的转账支票(内部的)
                approvalProcess.setCreateDate(new Date());
                approvalProcess.setOrg(user.getOid());

                if (balance>=0){
                    dataService.addFinanceAccountBillHistory(financeAccountBillHistory);
                    approvalProcess.setNewId(financeAccountBillHistory.getId()); //财务修改 修改中的id（历史表中的id）
                    approvalProcessDao.save(approvalProcess);

                    oldReturn.setModityStatus("2");
                    financeReturnService.updateReturn(oldReturn);

                    financeAccountBill.setModityStatus("2");
                    dataService.updateFinanceAccountBill(financeAccountBill);

                    if (returnIdNew!=null){
                        newReturn.setModityStatus("2");
                        financeReturnService.updateReturn(newReturn);

                        newFinanceAccountBill.setModityStatus("2");
                        dataService.updateFinanceAccountBill(newFinanceAccountBill);
                    }

                    AccountDetail accountDetail = dataService.getByBillId(financeAccountBill.getId());
                    accountDetail.setModityStatus("2");
                    accountDetailDao.update(accountDetail);

                    if (imgType!=null&&1==imgType){
                        JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                        List imgPathList = (List) JSONArray.toCollection(jsonValues);
                        for (int i=0;i<imgPathList.size();i++){
                            FinanceAccountBillImageHistory financeAccountBillImageHistory = dataService.addBillImageHistory(user,null,null,financeAccountBill.getId(),financeAccountBillHistory.getId(),"1",imgPathList.get(i).toString(),i+1);

                            FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImageHistory.getId(),FinanceAccountBillImageHistory.class);
                            uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表
                        }
                    }else {  //不修改的-将原本的数据保存
                        List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(user.getOid(),null,financeAccountBill.getId());
                        for (FinanceAccountBillImage financeAccountBillImage:financeAccountBillImages) {
                            dataService.addBillImageHistory(user,null,null,financeAccountBill.getId(),financeAccountBillHistory.getId(),"1",financeAccountBillImage.getUplaodPath(),financeAccountBillImage.getOrders());
                        }
                    }

                    //给财务修改-申请人的待处理发送
                    this.updateRejectSend(0,1,approvalProcess,userId,"/updateApplyHandle",null,null,"financeModify");

                    //给财务审批-审批人的待处理发送
                    this.updateRejectSend(1,1,approvalProcess,approvalProcess.getToUser(),"/updateApprovalHandle","有一条申请待审批","关于"+NewDateUtils.dateToString(oldReturn.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"所录入的财务收入的修改","financeModifyApproval");

                    map.put("state",1);
                    map.put("content","操作成功");
                }else {
                    map.put("state",2);  //余额不足
                    map.put("content","余额不足");
                }
            }
        } else {
            map.put("state",0);  //修改失败
            map.put("content","操作失败");
        }
        return map;
    }

    /**
     * 数据来源为支出中的转账支票(内部的转账支票或者改为外部的汇款票据)的修改申请
     * @param userId
     * @param chequeDetailId
     * @param accountId
     * @param summary
     * @param billQuantity
     * @param amount
     * @param billAmount
     * @param purpose
     * @param financialHandling
     * @param type
     * @param chequeNo
     * @param expireDate
     * @param receiveCorp
     * @param receiveDate
     * @param receiver
     * @param operator
     * @param memo
     * @param chequeId
     * @param stakeholderCategory
     * @param stakeholder
     * @param billDate
     * @param factDate
     * @return
     */
    @Override
    public Map<String, Object> updateBankTransfer(Integer userId, Integer chequeDetailId, Integer accountId, String summary, Integer billQuantity,
            BigDecimal amount, BigDecimal billAmount, String purpose, String financialHandling, String type, String chequeNo, Date expireDate,
            String receiveCorp, Date receiveDate, String receiver, String operator, String memo,Integer chequeId,
            String stakeholderCategory,Integer stakeholder, Date billDate,Date factDate,Integer imgType,String imgPaths) {

        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
        FinanceAccountBillHistory financeAccountBillHistory = new FinanceAccountBillHistory();
        FinanceChequeDetail oldChequeDetail = new FinanceChequeDetail();
        FinanceAccountBill oldAccountBill = new FinanceAccountBill();
        String des = "";
        FinanceAccount financeAccount = new FinanceAccount();
        FinanceAccount financeAccount1 = null;
        Double balance = 0.0;
        Double balance1 = 0.0;
        Integer status = 1;  // 1-正常  2-修改后的支票已使用
        if (accountId!=null){
            financeAccount1 = financeAccountDao.get(accountId);  //修改后的新账户
        }
        if (chequeDetailId!=null){
            oldChequeDetail = financeChequeDetailDao.get(chequeDetailId);

            if (!"".equals(oldChequeDetail.getModityStatus()) && "2".equals(oldChequeDetail.getModityStatus())){
                map.put("state", 3);  //已修改，不可重复修改
                map.put("content", "已修改，不可重复修改");
            }else {
                oldAccountBill = dataService.getAccountBillByChequeId(chequeDetailId);
                AccountDetail accountDetail = dataService.getByBillId(oldAccountBill.getId());
                oldChequeDetail.setBillCat(oldAccountBill.getBillCat());  // 票据类型
                oldChequeDetail.setBillPeriod(oldAccountBill.getBillPeriod());  //1-本月票据,2-非本月票据
                oldChequeDetail.setBillQuantity(oldAccountBill.getBillQuantity());  //票据数量
                financeAccount = oldChequeDetail.getAccountId();  //老账户

                //校验此条数据是否已经进行过修改
//                    if (!billCat.equals(oldChequeDetail.getBillCat())) {
//                        financeAccountBillHistory.setBillCat(billCat);  //票据种类
//                        des += "1" + ";";
////                    descriptions += "票据种类" + "、";
//                    } else {
//                        financeAccountBillHistory.setBillCat(oldChequeDetail.getBillCat());
//                    }
//
//                    if (!billPeriod.equals(oldChequeDetail.getBillPeriod())) {
//                        financeAccountBillHistory.setBillPeriod(billPeriod);  //票据所属月份
//                        des += "2" + ";";
////                    descriptions += "票据所属月份" + "、";
//                    } else {
//                        financeAccountBillHistory.setBillPeriod(oldChequeDetail.getBillPeriod());
//                    }

                if (!summary.equals(oldChequeDetail.getSummary())) {
                    financeAccountBillHistory.setSummary(summary);  //摘要
                    des += "3" + ";";
//                    descriptions += "摘要" + "、";
                } else {
                    financeAccountBillHistory.setSummary(oldChequeDetail.getSummary());
                }

                if (!billQuantity.equals(oldChequeDetail.getBillQuantity())) {
                    financeAccountBillHistory.setBillQuantity(billQuantity);  //票据数量
                    des += "4" + ";";
//                    descriptions += "票据数量" + "、";
                } else {
                    financeAccountBillHistory.setBillQuantity(oldChequeDetail.getBillQuantity());
                }

                if (amount != null && amount.doubleValue() != oldChequeDetail.getAmount().doubleValue()) {
                    financeAccountBillHistory.setAmount(amount);  //金额
                    des += "5" + ";";
//                    descriptions += "金额" + "、";
                } else {
                    financeAccountBillHistory.setAmount(oldChequeDetail.getAmount());
                }

                if (!purpose.equals(oldChequeDetail.getPurpose())) {
                    financeAccountBillHistory.setPurpose(purpose);  //用途
                    des += "6" + ";";
//                    descriptions += "用途" + "、";
                } else {
                    financeAccountBillHistory.setPurpose(oldChequeDetail.getPurpose());
                }

                if (!financialHandling.equals(oldChequeDetail.getFinancialHandling())) {
                    financeAccountBillHistory.setFinancialHandling(financialHandling);  //经手人
                    des += "7" + ";";
//                    descriptions += "经手人" + "、";
                } else {
                    financeAccountBillHistory.setFinancialHandling(oldChequeDetail.getFinancialHandling());
                }

                if (!type.equals(oldChequeDetail.getType())) {
                    financeAccountBillHistory.setType(type);  //8-支出方式(type)[1-转帐支票，2-现金汇票 3-承兑汇票(仅在此处用)暂时 4-外部的转账支票(仅在此处用)]
                    des += "8" + ";";
//                    descriptions += "支出方式" + "、";
                } else {
                    financeAccountBillHistory.setType(oldChequeDetail.getType());  //8-支出方式(type)[1-转帐支票，2-现金汇票 3-承兑汇票(仅在此处用)暂时]
                }

                if (accountId != null && !accountId.equals(oldChequeDetail.getAccountId().getId())) {
                    FinanceAccount financeAccountNew = financeAccountDao.get(accountId);  //新的账户
                    financeAccountBillHistory.setAccountId(accountId);  //账户id
                    financeAccountBillHistory.setAccout(financeAccountNew.getAccount());  //账号
                    financeAccountBillHistory.setBankName(financeAccountNew.getBankName()); //银行名称
                    balance = financeAccount.getBalance().add(oldChequeDetail.getAmount()).doubleValue(); //老账户的余额
//                        balance1 = financeAccount1.getBalance().subtract(amount).doubleValue(); //新账户的余额

                    BigDecimal me = new BigDecimal(amount.toString());
                    BigDecimal ba = new BigDecimal(financeAccount1.getBalance().toString());
                    balance1 = ba.subtract(me).doubleValue();  //新账户的余额

                    des += "9" + ";";
//                    descriptions += "银行账户" + "、";
                } else {
                    financeAccountBillHistory.setAccountId(oldChequeDetail.getAccountId().getId());  //账户id
                    financeAccountBillHistory.setAccout(oldChequeDetail.getAccount());  //账号
                    financeAccountBillHistory.setBankName(oldChequeDetail.getBankName()); //银行名称

//                        balance = financeAccount.getBalance().add(oldChequeDetail.getAmount()).subtract(amount).doubleValue(); //老账户的余额
                    BigDecimal me = new BigDecimal(amount.toString());
                    BigDecimal he = financeAccount.getBalance().add(oldChequeDetail.getAmount());
                    BigDecimal ba = new BigDecimal(he.toString());
                    balance = ba.subtract(me).doubleValue();  //老账户的余额
                }

                if (chequeId != null && !chequeDetailId.equals(chequeId)) {
                    if (!type.equals(oldChequeDetail.getType())) {  //是否修改了支出方式
                        FinanceReturn financeReturn = financeReturnDao.get(chequeId);
                        if ("1".equals(financeReturn.getState())) {
                            financeAccountBillHistory.setReturnBill(financeReturn.getId());
                            financeAccountBillHistory.setBillNo(financeReturn.getReturnNo());
                        }else {
                            status = 2;  //所选择的票据不可用
                        }
                    } else {
                        FinanceChequeDetail financeChequeDetail1 = financeChequeService.getChequeDetailById(chequeId);
                        if ("1".equals(financeChequeDetail1.getState())) {
                            financeAccountBillHistory.setCheque(chequeId);
                            financeAccountBillHistory.setBillNo(financeChequeDetail1.getChequeNo());
                        }else {
                            status = 2;  //所选择的票据不可用
                        }
                    }
                    des += "10" + ";";
//                    descriptions += "支票号" + "、";
                } else {
                    financeAccountBillHistory.setCheque(oldChequeDetail.getId());
                    financeAccountBillHistory.setBillNo(oldChequeDetail.getChequeNo());
                }

                if (expireDate != null && NewDateUtils.today(expireDate).getTime() != NewDateUtils.today(oldChequeDetail.getExpireDate()).getTime()) {
                    financeAccountBillHistory.setExpireDate(NewDateUtils.today(expireDate));  //支票到期日
                    des += "11" + ";";
                } else {
                    if (oldChequeDetail.getExpireDate()!=null)
                    financeAccountBillHistory.setExpireDate(NewDateUtils.today(oldChequeDetail.getExpireDate()));
                }

                if ((StringUtils.isNotEmpty(receiveCorp)&&!receiveCorp.equals(oldChequeDetail.getReceiveCorp())) || (stakeholder!=null&&stakeholder!=oldAccountBill.getStakeholder()) ) {
                    financeAccountBillHistory.setReceiveCorp(receiveCorp);  //收款单位
                    financeAccountBillHistory.setStakeholder(stakeholder);
                    financeAccountBillHistory.setOppositeCorp(receiveCorp);
                    des += "12" + ";";
//                    descriptions += "收款单位" + "、";
                } else {
                    financeAccountBillHistory.setReceiveCorp(oldChequeDetail.getReceiveCorp());
                    financeAccountBillHistory.setStakeholder(oldAccountBill.getStakeholder());
                    financeAccountBillHistory.setOppositeCorp(oldChequeDetail.getReceiveCorp());
                }

                if (receiveDate != null && NewDateUtils.today(receiveDate).getTime() != NewDateUtils.today(oldChequeDetail.getReceiveDate()).getTime()) {
                    financeAccountBillHistory.setReceiveDate(NewDateUtils.today(receiveDate));  //接收日期
                    des += "13" + ";";
                } else {
                    if (oldChequeDetail.getReceiveDate()!=null)
                    financeAccountBillHistory.setReceiveDate(NewDateUtils.today(oldChequeDetail.getReceiveDate()));  //接收日期
                }

                if (receiver != null && !receiver.equals(oldChequeDetail.getReceiver())) {
                    financeAccountBillHistory.setReceiver(receiver);  //接收经手人
                    des += "14" + ";";
//                    descriptions += "接收经手人" + "、";
                } else {
                    financeAccountBillHistory.setReceiver(oldChequeDetail.getReceiver());  //接收经手人
                }

                if (operator != null && !operator.equals(oldChequeDetail.getOperator())) {
                    financeAccountBillHistory.setOperator(operator);  //支付经手人
                    des += "15" + ";";
//                    descriptions += "支付经手人" + "、";
                } else {
                    financeAccountBillHistory.setOperator(oldChequeDetail.getOperator());  //支付经手人
                }

                if (!memo.equals(oldChequeDetail.getMemo())) {
                    financeAccountBillHistory.setMemo(memo);
                    des += "16" + ";";
//                    descriptions += "备注" + "、";
                } else {
                    financeAccountBillHistory.setMemo(oldChequeDetail.getMemo());
                }

                if (billAmount != null && billAmount.doubleValue() != oldChequeDetail.getBillAmount().doubleValue()) {
                    financeAccountBillHistory.setBillAmount(billAmount);
                    des += "17" + ";";
//                    descriptions += "票面金额" + "、";
                } else {
                    financeAccountBillHistory.setBillAmount(oldAccountBill.getBillAmount());
                }

                if (StringUtils.isNotEmpty(stakeholderCategory)&&!stakeholderCategory.equals(oldAccountBill.getStakeholderCategory())){
                    financeAccountBillHistory.setStakeholderCategory(stakeholderCategory);
                    des += "18" + ";";  //类别:1-供应商,2-员工,3-自行录入
                }else {
                    financeAccountBillHistory.setStakeholderCategory(oldAccountBill.getStakeholderCategory());
                }

                if (billDate!=null&&!billDate.equals(oldAccountBill.getBillDate())){
                    financeAccountBillHistory.setBillDate(billDate);
                    des += "19" + ";";  //票据日期
                }else {
                    financeAccountBillHistory.setBillDate(oldAccountBill.getBillDate());
                }

                if (factDate!=null&&!factDate.equals(oldAccountBill.getFactDate())){
                    financeAccountBillHistory.setFactDate(factDate);
                    des += "20" + ";";  //实际(付款/支出)日期
                }else {
                    financeAccountBillHistory.setFactDate(oldAccountBill.getFactDate());
                }
                if (imgType!=null&&1==imgType){
                    des += "21" + ";";  //图片修改
                }

                financeAccountBillHistory.setSubType(oldAccountBill.getSubType());
                financeAccountBillHistory.setCreateName(user.getUserName());
                financeAccountBillHistory.setCreateDate(new Date());
                financeAccountBillHistory.setCreator(user.getUserID());
                financeAccountBillHistory.setBillDetail(oldAccountBill);
                financeAccountBillHistory.setApproveStatus("1");

                //审批流程
                ApprovalProcess approvalProcess = new ApprovalProcess();
                User administrators = userService.getUserByRoleCode(user.getOid(), "super");  //查找机构下的超管
                approvalProcess.setLevel(1);  //审批级次
                approvalProcess.setApproveStatus("1");  //审批状态
                approvalProcess.setToUser(administrators.getUserID());  //审批人
                approvalProcess.setBusiness(oldAccountBill.getId()); //数据修改前的id（财务修改 修改前的id（本表中））
                approvalProcess.setOldId(oldAccountBill.getId());  //财务修改 修改前的id（本表中）
                approvalProcess.setBusinessType(1); //业务类型  1-财务修改，2- 加班，3-请假
                approvalProcess.setToUserName("超管");  //审批人总称
                approvalProcess.setUserName(administrators.getUserName());  //审批人名字
                approvalProcess.setUpdateDes(des);  //修改说明(需要修改的字段使用数字代替)
                approvalProcess.setDescription("关于" + NewDateUtils.dateToString(accountDetail.getCreateDate(),"yyyy-MM-dd HH:mm:ss") + "所录入的财务支出的修改");  //修改记录中的申请事件/标题
                approvalProcess.setAskName(user.getUserName());  //申请人
                approvalProcess.setFromUser(user.getUserID());  //申请人id
                approvalProcess.setType(5);  //状态 1-纯现金/银行转账 2-内部非支出性转账 3-报销 4-承兑汇票和转账支票中的外部支票 5-数据来源为支出中的转账支票(内部的)
                approvalProcess.setCreateDate(new Date());
                approvalProcess.setOrg(user.getOid());

                if (status==1) {  //若修改票据后判断新票据是否为可用的  1-可用 2-不可用
                    if ((financeAccount1 == null && balance >= 0.0) || (financeAccount1 != null && balance >= 0.0 && balance1 >= 0.0)) {
                        if (balance >= 0.0 && balance1 >= 0.0) {
                            dataService.addFinanceAccountBillHistory(financeAccountBillHistory);
                            approvalProcess.setNewId(financeAccountBillHistory.getId()); //财务修改 修改中的id（历史表中的id）
                            approvalProcessDao.save(approvalProcess);

                            oldChequeDetail.setModityStatus("2");
                            financeChequeService.updateCashChequeState(oldChequeDetail);

                            accountDetail.setModityStatus("2");
                            accountDetailDao.update(accountDetail);

                            if (imgType!=null&&1==imgType){
                                JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                                List imgPathList = (List) JSONArray.toCollection(jsonValues);
                                for (int i=0;i<imgPathList.size();i++){
                                    FinanceAccountBillImageHistory financeAccountBillImageHistory = dataService.addBillImageHistory(user,null,null,oldAccountBill.getId(),financeAccountBillHistory.getId(),"1",imgPathList.get(i).toString(),i+1);

                                    FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImageHistory.getId(),FinanceAccountBillImageHistory.class);
                                    uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表
                                }
                            }else {  //不修改的-将原本的数据保存
                                List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(user.getOid(),null,oldAccountBill.getId());
                                for (FinanceAccountBillImage financeAccountBillImage:financeAccountBillImages) {
                                    dataService.addBillImageHistory(user,null,null,oldAccountBill.getId(),financeAccountBillHistory.getId(),"1",financeAccountBillImage.getUplaodPath(),financeAccountBillImage.getOrders());
                                }
                            }

                            //给财务修改-申请人的待处理发送
                            this.updateRejectSend(0, 1, approvalProcess, userId, "/updateApplyHandle", null,null, "financeModify");

                            //给财务审批-审批人的待处理发送
                            this.updateRejectSend(1, 1, approvalProcess, approvalProcess.getToUser(), "/updateApprovalHandle", "有一条申请待审批", "关于" +NewDateUtils.dateToString(oldChequeDetail.getCreateDate(),"yyyy-MM-dd HH:mm:ss") + "所录入的财务支出的修改", "financeModifyApproval");

                            map.put("state", 1);
                            map.put("content", "操作成功");

                        } else {
                            map.put("state", 2); //余额不足
                            map.put("content", "余额不足");
                        }
                    }else {
                        if (balance<0 || balance1<0){
                            map.put("state", 2); //余额不足
                            map.put("content", "余额不足");
                        }
                    }
                }else {  //2-不可用
                    map.put("state", 4); //所选择的票据不可用
                    map.put("content", "选择的新票据不可用");
                }

            }
        } else {
            map.put("state", 0);  //修改失败
            map.put("content","操作失败");
        }
        return map;
    }

    /**
     * 纯现金/银行转账修改申请的审批--批准/驳回
     * @param userId 用户id
     * @param approvalProcessId  审批流程id
     * @param state 1-批准 0-驳回
     * @param reason 驳回理由
     * @return
     */
    @Override
    public Map<String, Object> approvalCash(Integer userId,Integer approvalProcessId,Integer state,String reason) {
        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
        AccountPeriod accountPeriod = new AccountPeriod();  //修改前账户的月结
        AccountPeriod accountPeriod1 = new AccountPeriod();  //修改后账户的月结
        AccountPeriod accountPeriodDay = new AccountPeriod();  //修改前账户的日结
        AccountPeriod accountPeriodDay1 = new AccountPeriod();  //修改后账户的日结
        AccountDetail accountDetail1 = new AccountDetail();//批准后，账务明细表里添加一条新数据(正确)
        AccountDetail accountDetail2 = new AccountDetail();//批准后，账务明细表里添加一条新数据(金额为负)

        if (approvalProcessId!=null){
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            AccountDetail accountDetail = accountDetailDao.get(approvalProcess.getOldId());
//            FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
            AccountDetailHistory accountDetailHistory = accountDetailHistoryDao.get(approvalProcess.getNewId());
            FinanceAccount f = financeAccountDao.get(accountDetail.getAccountId().getId());  //修改前的银行账户
            FinanceAccount f1 = financeAccountDao.get(accountDetailHistory.getAccountId());  //修改后的银行账户

            if (f==f1){   //没有修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(f.getId(), new Date());  //账户的月结
                accountPeriodDay = accountService.getAccountPeriodByDay(f.getId(),new Date());  //账户的日结
            }else {    //修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(f.getId(), new Date());  //修改前账户的月结
                accountPeriod1 = accountService.getAccountPeriodByMonth(f1.getId(), new Date());  //修改后账户的账户月结
                accountPeriodDay = accountService.getAccountPeriodByDay(f.getId(), new Date());   //修改前账户的日结
                accountPeriodDay1 = accountService.getAccountPeriodByDay(f1.getId(), new Date());  //修改后账户的日结
            }

            Double newBanlance = 0.0; //账户修改前新余额
            Double newBanlance1 = 0.0 ; //账户修改后的账户新余额
            Double newCredit = 0.0 ; //修改前收入
            Double newDebit = 0.0 ; //修改前支出
            Double newCredit1 = 0.0 ; //修改后的收入
            Double newDebit1 = 0.0 ; //修改后的支出
            Integer type = 1;  //1-仅是覆盖数据的信息  0-需要冲账
            if ("1".equals(approvalProcess.getApproveStatus())) {
                if (state == 1) {
                    accountDetailHistory.setApproveStatus("2");  //批准
                    approvalProcess.setHandleTime(new Date()); //处理时间
                    approvalProcess.setApproveStatus("2");
//                userMessage.setApprovalStatus(2); //批准

                    //金额是否修改
                    if (!f.getId().equals(f1.getId()) || accountDetail.getCredit() != null && accountDetail.getCredit().doubleValue() != accountDetailHistory.getCredit().doubleValue() || accountDetail.getDebit() != null && accountDetail.getDebit().doubleValue() != accountDetailHistory.getDebit().doubleValue()) {
                        if (accountDetail.getCredit() != null) {
                            if (accountPeriod1.getId() != null) {
                                //收入修改 新余额=现在账户余额-修改前的收入+修改后的新收入
                                newBanlance = f.getBalance().subtract(accountDetail.getCredit()).doubleValue(); //修改前的账户的余额
                                newBanlance1 = f1.getBalance().add(accountDetailHistory.getCredit()).doubleValue();  //修改后的账户余额
                                newCredit = accountDetail.getCredit().doubleValue();
                                newCredit1 = accountDetailHistory.getCredit().doubleValue();
                            } else {
                                //收入修改 新余额=现在账户余额-修改前的收入+修改后的新收入
                                newBanlance = f.getBalance().subtract(accountDetail.getCredit()).add(accountDetailHistory.getCredit()).doubleValue();
                                newCredit = accountDetail.getCredit().doubleValue();
                                newCredit1 = accountDetailHistory.getCredit().doubleValue();
                            }
                        } else {
                            if (accountPeriod1.getId() != null) {
                                //支出修改 新余额=现在账户余额+修改前的支出-修改后的新支出
                                newBanlance = f.getBalance().add(accountDetail.getDebit()).doubleValue();  //修改前的账户的余额
                                newBanlance1 = f1.getBalance().subtract(accountDetailHistory.getDebit()).doubleValue(); //修改后的账户余额
                                newDebit = accountDetail.getDebit().doubleValue();
                                newDebit1 = accountDetailHistory.getDebit().doubleValue();
                            } else {
                                //支出修改 新余额=现在账户余额+修改前的支出-修改后的新支出
                                newBanlance = f.getBalance().add(accountDetail.getDebit()).subtract(accountDetailHistory.getDebit()).doubleValue();
                                newDebit = accountDetail.getDebit().doubleValue();
                                newDebit1 = accountDetailHistory.getDebit().doubleValue();
                            }
                        }
                        type = 0;
                    }

                    if (type==1){    //1-仅是覆盖数据的信息  0-需要冲账
                        //批准后，如果金额和账户均没有修改则直接覆盖数据
                        accountDetail.setAuditDate(new Date());
                        accountDetail.setAuditorName(accountDetailHistory.getAuditorName());//经手人
                        accountDetail.setMemo(accountDetailHistory.getMemo());
                        accountDetail.setGenre(accountDetailHistory.getGenre());
                        accountDetail.setCategoryDesc(accountDetailHistory.getCategoryDesc());
                        accountDetail.setSummary(accountDetailHistory.getSummary());
                        accountDetail.setPurpose(accountDetailHistory.getPurpose());//用途
                        accountDetail.setOppositeCorp(accountDetailHistory.getOppositeCorp());//付款单位(收款单位)
                        accountDetail.setModityStatus("1");  //没有进行冲账数据可以再次修改
                        accountDetail.setReceiveAccountDate(accountDetailHistory.getReceiveAccountDate());
                        accountDetail.setBillAmount(accountDetailHistory.getBillAmount());  //票面金额
                        accountDetail.setStakeholderCategory(accountDetailHistory.getStakeholderCategory());  //类别:1-供应商,2-员工,3-自行录入
                        accountDetail.setStakeholder(accountDetailHistory.getStakeholder());  //收款单位id（相关系人id）
                        accountDetail.setBillDate(accountDetailHistory.getBillDate());  //票据日期
                        accountDetail.setFactDate(accountDetailHistory.getFactDate());   //实际(付款/支出)日期')
                        accountDetail.setBillQuantity(accountDetailHistory.getBillQuantity());  //票据数量
                        accountDetail.setSubType(accountDetailHistory.getSubType()); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出

                        if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())){  //报税的
                            accountDetail.setBusiness(accountDetailHistory.getBusiness());
                            accountDetail.setBusinessKey(accountDetailHistory.getBusinessKey());
                            accountDetail.setBusinessPeriodBegin(accountDetailHistory.getBusinessPeriodBegin());
                            accountDetail.setBusinessPeriodEnd(accountDetailHistory.getBusinessPeriodEnd());
                        }
                    }else {
                        //如果金额修改了或者是银行账户修改了
                        if (newBanlance >= 0 || !f.getId().equals(f1.getId())) {

                            //批准后，账务明细表里添加一条新数据(正确)
                            accountDetail1.setCreateDate(new Date());
                            accountDetail1.setCreateName(user.getUserName());
                            accountDetail1.setCreator(user.getUserID());
                            accountDetail1.setOrg(user.getOrganization());
                            accountDetail1.setPreviousId(accountDetail.getId());
                            if (accountPeriod1.getId() != null) {
                                accountDetail1.setAccount(accountPeriod1.getId().toString());
                            } else {
                                accountDetail1.setAccount(accountPeriod.getId().toString());
                            }
                            accountDetail1.setAuditDate(new Date());
                            accountDetail1.setCredit(accountDetailHistory.getCredit());
                            accountDetail1.setDebit(accountDetailHistory.getDebit());
                            accountDetail1.setBillAmount(accountDetailHistory.getBillAmount());  //票面金额
                            accountDetail1.setAuditorName(accountDetailHistory.getAuditorName());//经手人
                            accountDetail1.setMethod(accountDetail.getMethod());//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                            accountDetail1.setMemo(accountDetailHistory.getMemo());
                            accountDetail1.setGenre(accountDetailHistory.getGenre());
                            accountDetail1.setCategoryDesc(accountDetailHistory.getCategoryDesc());
                            accountDetail1.setSummary(accountDetailHistory.getSummary());
                            accountDetail1.setPurpose(accountDetailHistory.getPurpose());//用途
                            accountDetail1.setOppositeCorp(accountDetailHistory.getOppositeCorp());//付款单位
                            accountDetail1.setAccountBank(accountDetailHistory.getAccountBank());
                            accountDetail1.setAccountantStatus(accountDetail.getAccountantStatus());//会计数据状态  1-未选择  2-已选择
                            accountDetail1.setFid(f1.getId().toString());
                            accountDetail1.setAccountId(f1);//账户外键
//                            accountDetail1.setBillDetail(financeAccountBill);
                            accountDetail1.setReceiveAccountDate(accountDetailHistory.getReceiveAccountDate());
                            accountDetail1.setType(accountDetail.getType());//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                            accountDetail1.setSource(accountDetail.getSource());
                            accountDetail1.setStakeholderCategory(accountDetailHistory.getStakeholderCategory());  //类别:1-供应商,2-员工,3-自行录入
                            accountDetail1.setStakeholder(accountDetailHistory.getStakeholder());  //收款单位id（相关系人id）
                            accountDetail1.setBillDate(accountDetailHistory.getBillDate());  //票据日期
                            accountDetail1.setFactDate(accountDetailHistory.getFactDate());   //实际(付款/支出)日期')
                            accountDetail1.setBillQuantity(accountDetailHistory.getBillQuantity());  //票据数量
                            accountDetail1.setSubType(accountDetailHistory.getSubType()); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
                            accountDetail1.setPreviousId(accountDetail.getId());
                            accountDetail1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                            if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())){  //报税的
                                accountDetail1.setBusiness(accountDetailHistory.getBusiness());  //报表详情id
                                accountDetail1.setBusinessKey(accountDetailHistory.getBusinessKey());  //税种id
                                accountDetail1.setBusinessPeriodBegin(accountDetailHistory.getBusinessPeriodBegin());  //所属日期开始时间
                                accountDetail1.setBusinessPeriodEnd(accountDetailHistory.getBusinessPeriodEnd());  //所属日期结束时间

                                accountDetail2.setBusiness(accountDetail.getBusiness());
                                accountDetail2.setBusinessKey(accountDetail.getBusinessKey());
                                accountDetail2.setBusinessPeriodBegin(accountDetail.getBusinessPeriodBegin());
                                accountDetail2.setBusinessPeriodEnd(accountDetail.getBusinessPeriodEnd());
                            }

                            //批准后，账务明细表里添加一条新数据(金额为负)
                            accountDetail2.setCreateDate(new Date());
                            accountDetail2.setCreateName(user.getUserName());
                            accountDetail2.setCreator(user.getUserID());
                            accountDetail2.setOrg(user.getOrganization());
                            accountDetail2.setPreviousId(accountDetail.getId());
                            accountDetail2.setAccount(accountPeriod.getId().toString());
                            accountDetail2.setAuditDate(new Date());
                            if (accountDetailHistory.getCredit() != null) {  //收入负数
                                accountDetail2.setCredit(accountDetail.getCredit().multiply(new BigDecimal(-1)));  //负数
                            } else {  //支出负数
                                accountDetail2.setDebit(accountDetail.getDebit().multiply(new BigDecimal(-1)));
                            }
                            accountDetail2.setBillAmount(accountDetail.getBillAmount());  //票面金额
                            accountDetail2.setAuditorName(accountDetail.getAuditorName());//经手人
                            accountDetail2.setMethod(accountDetail.getMethod());//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                            accountDetail2.setMemo(accountDetail.getMemo());
                            accountDetail2.setGenre(accountDetail.getGenre());
                            accountDetail2.setCategoryDesc(accountDetail.getCategoryDesc());
                            accountDetail2.setSummary(accountDetail.getSummary());
                            accountDetail2.setPurpose(accountDetail.getPurpose());//用途
                            accountDetail2.setOppositeCorp(accountDetail.getOppositeCorp());//付款单位
                            accountDetail2.setAccountBank(accountDetail.getAccountBank());
                            accountDetail2.setAccountantStatus(accountDetail.getAccountantStatus());//会计数据状态  1-未选择  2-已选择
                            accountDetail2.setFid(f.getId().toString());
                            accountDetail2.setAccountId(f);//账户外键
                            accountDetail2.setType(accountDetail.getType());//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                            accountDetail2.setBalance(BigDecimal.valueOf(newBanlance)); //余额
//                            accountDetail2.setBillDetail(financeAccountBill);
                            accountDetail2.setReceiveAccountDate(accountDetail.getReceiveAccountDate());
                            accountDetail2.setModityStatus("2");
                            accountDetail2.setSource(accountDetail.getSource());
                            accountDetail2.setStakeholderCategory(accountDetail.getStakeholderCategory());  //类别:1-供应商,2-员工,3-自行录入
                            accountDetail2.setStakeholder(accountDetail.getStakeholder());  //收款单位id（相关系人id）
                            accountDetail2.setBillDate(accountDetail.getBillDate());  //票据日期
                            accountDetail2.setFactDate(accountDetail.getFactDate());   //实际(付款/支出)日期')
                            accountDetail2.setBillQuantity(accountDetail.getBillQuantity());  //票据数量
                            accountDetail2.setSubType(accountDetail.getSubType()); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
                            accountDetail2.setPreviousId(accountDetail.getId());
                            accountDetail2.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                            //账户是否改变
                            if (accountPeriod1.getId() != null) {   //账户修改
                                accountDetail1.setBalance(BigDecimal.valueOf(newBanlance1)); //正确的新余额

                                //修改前账户的月结
                                accountPeriod.setCredit(accountPeriod.getCredit().subtract(BigDecimal.valueOf(newCredit)));
                                accountPeriod.setDebit(accountPeriod.getDebit().subtract(BigDecimal.valueOf(newDebit)));
                                accountPeriod.setBalance(BigDecimal.valueOf(newBanlance));

                                //修改后账户的月结
                                accountPeriod1.setCredit(accountPeriod1.getCredit().add(BigDecimal.valueOf(newCredit1)));
                                accountPeriod1.setDebit(accountPeriod1.getDebit().add(BigDecimal.valueOf(newDebit1)));
                                accountPeriod1.setBalance(BigDecimal.valueOf(newBanlance1));

                                //修改前的日结
                                accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(BigDecimal.valueOf(newCredit)));
                                accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(BigDecimal.valueOf(newDebit)));
                                accountPeriodDay.setBalance(BigDecimal.valueOf(newBanlance));

                                //修改后的日结
                                accountPeriodDay1.setCredit(accountPeriodDay1.getCredit().add(BigDecimal.valueOf(newCredit1)));
                                accountPeriodDay1.setDebit(accountPeriodDay1.getDebit().add(BigDecimal.valueOf(newDebit1)));
                                accountPeriodDay1.setBalance(BigDecimal.valueOf(newBanlance1));

                                //修改前账户余额
                                f.setCredit(f.getCredit().subtract(BigDecimal.valueOf(newCredit)));
                                f.setDebit(f.getDebit().subtract(BigDecimal.valueOf(newDebit)));
                                f.setBalance(BigDecimal.valueOf(newBanlance));

                                //修改后账户余额
                                f1.setCredit(f1.getCredit().add(BigDecimal.valueOf(newCredit1)));
                                f1.setDebit(f1.getDebit().add(BigDecimal.valueOf(newDebit1)));
                                f1.setBalance(BigDecimal.valueOf(newBanlance1));

                            } else {  //账户未修改
                                accountDetail1.setBalance(BigDecimal.valueOf(newBanlance)); //正确的新余额

                                //修改后的月结
                                accountPeriod.setCredit(accountPeriod.getCredit().subtract(BigDecimal.valueOf(newCredit)).add(BigDecimal.valueOf(newCredit1)));
                                accountPeriod.setDebit(accountPeriod.getDebit().subtract(BigDecimal.valueOf(newDebit)).add(BigDecimal.valueOf(newDebit1)));
                                accountPeriod.setBalance(BigDecimal.valueOf(newBanlance));

                                //修改后的日结
                                accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(BigDecimal.valueOf(newCredit)).add(BigDecimal.valueOf(newCredit1)));
                                accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(BigDecimal.valueOf(newDebit)).add(BigDecimal.valueOf(newDebit1)));
                                accountPeriodDay.setBalance(BigDecimal.valueOf(newBanlance));

                                //修改后的账户余额
                                f.setCredit(f.getCredit().subtract(BigDecimal.valueOf(newCredit)).add(BigDecimal.valueOf(newCredit1)));
                                f.setDebit(f.getDebit().subtract(BigDecimal.valueOf(newDebit)).add(BigDecimal.valueOf(newDebit1)));
                                f.setBalance(BigDecimal.valueOf(newBanlance));
                            }
                            accountDetail.setModityStatus("2");  //数据已进行冲账，不可以再修改
                            accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                        }
                    }

                } else {
                    accountDetailHistory.setApproveStatus("3");  //驳回
                    approvalProcess.setHandleTime(new Date()); //处理时间
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setReason(reason);
                    accountDetail.setModityStatus("1");  //数据可以再修改
                }

                if (newBanlance >= 0 && newBanlance1 >= 0) {
                    if (type==1){
                        accountDetailDao.update(accountDetail);  //仅覆盖数据

                        if (state==1) {   //批准的
                            List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(), accountDetail.getId(), null);
                            for (FinanceAccountBillImage billImage : billImages) {  //原本的图片直接删除，修改后的直接覆盖
                                financeAccountBillImageDao.delete(billImage);
                            }

                            List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(), accountDetailHistory.getId(), null);
                            for (FinanceAccountBillImageHistory billImageHistory : billImageHistories) {  //将修改后的图片加入到修改后正确的数据里
                                dataService.addBillImage(user, accountDetail.getId(), null, billImageHistory.getType(), billImageHistory.getUplaodPath(), billImageHistory.getOrders());
                            }

                            if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())) {  //报税的
                                Map<String, Object> mapOld = acctReportTaxService.getById(accountDetail.getBusiness());
                                Map<String, Object> mapNew = acctReportTaxService.getById(accountDetailHistory.getBusiness());
                                Integer periodIdOld = (Integer) mapOld.get("periodId");  //修改前的申报记录id
                                Integer periodIdNew = (Integer) mapNew.get("periodId");  //修改后的申报记录id
                                if (periodIdOld != null && periodIdNew != null && !periodIdNew.equals(periodIdOld)) {
                                    acctReportTaxService.releasePeriodById(periodIdOld);
                                }
                            }
                        }else {   //驳回的
                            if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())) {  //报税的
                                Map<String, Object> mapOld = acctReportTaxService.getById(accountDetail.getBusiness());
                                Map<String, Object> mapNew = acctReportTaxService.getById(accountDetailHistory.getBusiness());
                                Integer periodIdOld = (Integer) mapOld.get("periodId");  //修改前的申报记录id
                                Integer periodIdNew = (Integer) mapNew.get("periodId");  //修改后的申报记录id
                                if (periodIdOld != null && periodIdNew != null && !periodIdNew.equals(periodIdOld)) {
                                    acctReportTaxService.releasePeriodById(periodIdNew);
                                }
                            }
                        }

                    }else {
                        if (accountPeriod1.getId() != null) {  //修改账户
                            accountPeroidDao.update(accountPeriod1);
                            accountPeroidDao.update(accountPeriodDay1);
                            financeAccountDao.update(f1);
                            accountPeroidDao.update(accountPeriod);  //修改前账户额月结
                            accountPeroidDao.update(accountPeriodDay);  //修改前账户的日结
                            financeAccountDao.update(f);  //没有修改账户，但是账户金额发生变化
                            accountDetailDao.save(accountDetail2);//负数明细
                            accountDetailDao.save(accountDetail1);//正确的新明细

                            List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(),accountDetail.getId(),null);
                            for (FinanceAccountBillImage billImage:billImages) {  //负金额的是将原本的图片录入
                                dataService.addBillImage(user,accountDetail2.getId(),null,billImage.getType(),billImage.getUplaodPath(),billImage.getOrders());
                            }

                            List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),accountDetailHistory.getId(),null);
                            for (FinanceAccountBillImageHistory billImageHistory:billImageHistories) {  //将修改后的图片加入到修改后正确金额数据里
                                dataService.addBillImage(user,accountDetail1.getId(),null,billImageHistory.getType(),billImageHistory.getUplaodPath(),billImageHistory.getOrders());
                            }

                            if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())){  //报税的
                                AcctTaxDetailHistoryEntity acctTaxDetailHistoryEntity = taxDetailHistoryDao.get(accountDetailHistory.getBusiness());
                                acctTaxDetailHistoryEntity.setAccountDetail(accountDetail1.getId());
                                taxDetailHistoryDao.update(acctTaxDetailHistoryEntity);

                                Map<String, Object> mapOld = acctReportTaxService.getById(accountDetail.getBusiness());
                                Map<String, Object> mapNew = acctReportTaxService.getById(accountDetailHistory.getBusiness());
                                Integer periodIdOld = (Integer) mapOld.get("periodId");  //修改前的申报记录id
                                Integer periodIdNew = (Integer) mapNew.get("periodId");  //修改后的申报记录id
                                if (periodIdOld!=null&&periodIdNew!=null&&!periodIdNew.equals(periodIdOld)){
                                    acctReportTaxService.releasePeriodById(periodIdOld);  //将修改前的申报记录id释放
                                }
                            }

                        } else {  //未修改账户
                            if (accountDetail1.getDebit() != null || accountDetail1.getCredit() != null) {  //判断是否只修改了金额
                                accountPeroidDao.update(accountPeriod);  //修改前账户额月结
                                accountPeroidDao.update(accountPeriodDay);  //修改前账户的日结
                                financeAccountDao.update(f);  //没有修改账户，但是账户金额发生变化
                                accountDetailDao.save(accountDetail2);//负数明细
                                accountDetailDao.save(accountDetail1);//正确的新明细

                                List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(),accountDetail.getId(),null);
                                for (FinanceAccountBillImage billImage:billImages) {  //负金额的是将原本的图片录入
                                    dataService.addBillImage(user,accountDetail2.getId(),null,billImage.getType(),billImage.getUplaodPath(),billImage.getOrders());
                                }

                                List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),accountDetailHistory.getId(),null);
                                for (FinanceAccountBillImageHistory billImageHistory:billImageHistories) {  //将修改后的图片加入到修改后正确金额数据里
                                    dataService.addBillImage(user,accountDetail1.getId(),null,billImageHistory.getType(),billImageHistory.getUplaodPath(),billImageHistory.getOrders());
                                }

                                if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())){  //报税的
                                    AcctTaxDetailHistoryEntity acctTaxDetailHistoryEntity = taxDetailHistoryDao.get(accountDetailHistory.getBusiness());
                                    acctTaxDetailHistoryEntity.setAccountDetail(accountDetail1.getId());
                                    taxDetailHistoryDao.update(acctTaxDetailHistoryEntity);

                                    Map<String, Object> mapOld = acctReportTaxService.getById(accountDetail.getBusiness());
                                    Map<String, Object> mapNew = acctReportTaxService.getById(accountDetailHistory.getBusiness());
                                    Integer periodIdOld = (Integer) mapOld.get("periodId");  //修改前的申报记录id
                                    Integer periodIdNew = (Integer) mapNew.get("periodId");  //修改后的申报记录id
                                    if (periodIdOld!=null&&periodIdNew!=null&&!periodIdNew.equals(periodIdOld)){
                                        acctReportTaxService.releasePeriodById(periodIdOld);
                                    }
                                }

                            } else {
                                accountDetailDao.update(accountDetail);  //仅覆盖数据

                                if (1==state) {  //批准的
                                    List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(), accountDetail.getId(), null);
                                    for (FinanceAccountBillImage billImage : billImages) {  //原本的图片直接删除，修改后的直接覆盖
                                        financeAccountBillImageDao.delete(billImage);
                                    }

                                    List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(), accountDetailHistory.getId(), null);
                                    for (FinanceAccountBillImageHistory billImageHistory : billImageHistories) {  //将修改后的图片加入到修改后正确的数据里
                                        dataService.addBillImage(user, accountDetail.getId(), null, billImageHistory.getType(), billImageHistory.getUplaodPath(), billImageHistory.getOrders());
                                    }

                                    if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())) {  //报税的
                                        Map<String, Object> mapOld = acctReportTaxService.getById(accountDetail.getBusiness());
                                        Map<String, Object> mapNew = acctReportTaxService.getById(accountDetailHistory.getBusiness());
                                        Integer periodIdOld = (Integer) mapOld.get("periodId");  //修改前的申报记录id
                                        Integer periodIdNew = (Integer) mapNew.get("periodId");  //修改后的申报记录id
                                        if (periodIdOld != null && periodIdNew != null && !periodIdNew.equals(periodIdOld)) {
                                            acctReportTaxService.releasePeriodById(periodIdOld);
                                        }
                                    }

                                }else {   //驳回的
                                    if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())) {  //报税的
                                        Map<String, Object> mapOld = acctReportTaxService.getById(accountDetail.getBusiness());
                                        Map<String, Object> mapNew = acctReportTaxService.getById(accountDetailHistory.getBusiness());
                                        Integer periodIdOld = (Integer) mapOld.get("periodId");  //修改前的申报记录id
                                        Integer periodIdNew = (Integer) mapNew.get("periodId");  //修改后的申报记录id
                                        if (periodIdOld != null && periodIdNew != null && !periodIdNew.equals(periodIdOld)) {
                                            acctReportTaxService.releasePeriodById(periodIdNew);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    approvalProcessDao.update(approvalProcess);

                    map.put("state", 1);//操作成功
                    map.put("content","操作成功");
                } else {
                    map.put("state", 2);  //余额不足
                    map.put("content","余额不足");
                }
                this.publicPushInterface(map,approvalProcess,state);  //审批成功后公共的推送接口
            }else {
                map.put("state", 3);  //已审批，不能重复审批
                map.put("content","已审批，不能重复审批");
            }
        }else {
            map.put("state",0);  //操作失败
            map.put("content","操作失败");
        }

        return map;
    }

    /**
     * 承兑汇票和转账支票中的外部支票的修改申请的审批
     * @param userId 用户id
     * @param approvalProcessId  审批流程id
     * @param state 1-批准 0-驳回
     * @param reason 驳回理由
     * @return
     */
    @Override
    public Map<String, Object> approvalReturn(Integer userId, Integer approvalProcessId, Integer state, String reason) {
        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
        if (approvalProcessId!=null){
            ApprovalProcess approcalProcess = approvalProcessDao.get(approvalProcessId);
            FinanceAccountBill financeAccountBill = financeAccountBillDao.get(approcalProcess.getOldId());  //老数据
            AccountDetail accountDetail = dataService.getByBillId(financeAccountBill.getId());
            FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();  //老数据
            FinanceAccount financeAccount = new FinanceAccount();
            FinanceAccount financeAccount1 = new FinanceAccount();
            AccountPeriod accountPeriodDay = new AccountPeriod();
            AccountDetail accountDetail1 = new AccountDetail();  /**生成的负数据(老账户的)*/
            AccountPeriod accountPeriodMonth = new AccountPeriod();
            FinanceReturn financeReturn1 = new FinanceReturn();  //添加一条和原来的支票信息差不多的信息(正确的存入银行的信息)
            FinanceAccountBill financeAccountBill1 = new FinanceAccountBill();  //bill表中新添加支票的明细
            AccountPeriod accountPeriodDay1 = new AccountPeriod();
            AccountDetail accountDetail2 = new AccountDetail();  /*添加账务明细账表（正确账户）, 如果新添加一条汇款票据，那么bill表里也需要添加一条数据*/
            AccountPeriod accountPeriodMonth1 = new AccountPeriod();

            FinanceAccountBillHistory financeAccountBillHistory = financeAccountBillHistoryDao.get(approcalProcess.getNewId()); //修改的内容
            FinanceReturn financeReturnNew = financeReturnDao.get(financeAccountBillHistory.getReturnBill());  //是否换了票据

            if ("1".equals(approcalProcess.getApproveStatus())) {
                if (state == 1) {    //批准
                    approcalProcess.setHandleTime(new Date()); //处理时间
                    approcalProcess.setApproveStatus("2");
                    financeAccountBillHistory.setApproveStatus("2");

                    //是否修改了账户
                    if (financeReturn != null && (!financeReturn.getAccountId().equals(financeAccountBillHistory.getAccountId())||!financeReturn.getId().equals(financeReturnNew.getId()))) {
                            financeAccount = financeAccountDao.get(financeReturn.getAccountId());  //老账户
                            financeAccount1 = financeAccountDao.get(financeAccountBillHistory.getAccountId());

                        //修改前的账户信息(老账户)
                        if (financeAccount.getId() != null) {
                            financeAccount.setBalance(financeAccount.getBalance().subtract(financeReturn.getAmount()));
                            financeAccount.setCredit(financeAccount.getCredit().subtract(financeReturn.getAmount()));
                        }

                        /**生成的负数据(老账户的)*/
                        BeanUtils.copyPropertiesIgnoreNull(accountDetail,accountDetail1);
                        accountDetail1.setAccountDetailHistories(null);
                        accountDetail1.setId(null);
                        accountDetail1.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
                        accountDetail1.setAccountId(financeAccount);
                        accountDetail1.setFid(financeAccount.getId().toString());  //账户id
                        accountDetail1.setBillDetail(financeReturnService.getBill(financeReturn.getId()));  //对应的bill表的数据
                        accountDetail1.setOrg(user.getOrganization());
//                        accountDetail1.setMethod(accountDetail.getMethod());  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                        accountDetail1.setCredit(financeReturn.getAmount().multiply(new BigDecimal(-1)));  //负数表示
                        accountDetail1.setBillAmount(financeAccountBill.getBillAmount());  //票面金额
                        accountDetail1.setCreateName(user.getUserName());
                        accountDetail1.setCreateDate(new Date());
                        accountDetail1.setCreator(user.getUserID());
                        accountDetail1.setBalance(financeAccount.getBalance());
                        accountDetail1.setModityStatus("2");
                        accountDetail1.setAuditDate(new Date());
                        accountDetail1.setBillDate(new Date());
                        accountDetail1.setPreviousId(accountDetail.getId());
                        accountDetail1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                        //修改前账户的日结
                        accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());
                        if (accountPeriodDay != null) {
                            accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(financeReturn.getAmount()));  //收入
                            accountPeriodDay.setBalance(accountPeriodDay.getBalance().subtract(financeReturn.getAmount()));  // 本期余额

                            accountDetail1.setAccount(accountPeriodDay.getId().toString());//期表日结的id(记录属于哪一期)
                        }

                    /*修改前账户的月结*/
                        accountPeriodMonth = accountService.getAccountPeriodByMonth(financeReturn.getAccountId(),new Date());
                        if (accountPeriodMonth != null) {
                            accountPeriodMonth.setCredit(accountPeriodMonth.getCredit().subtract(financeReturn.getAmount()));
                            accountPeriodMonth.setBalance(accountPeriodMonth.getBalance().subtract(financeReturn.getAmount()));
                        }

                        if (financeReturn.getId().equals(financeReturnNew.getId())) {

                            //添加一条和原来的支票信息差不多的信息(正确的存入银行的信息)
                            BeanUtils.copyPropertiesIgnoreNull(financeReturn, financeReturn1);
                            financeReturn1.setId(null);
                            financeReturn1.setState("2"); //1-有效,2-存入银行,3-作废,4-已支出
                            financeReturn1.setSaveBankName(financeAccountBillHistory.getSaveBankName()); //存入银行的名称
                            financeReturn1.setAccout(financeAccountBillHistory.getAccout()); //银行账号
                            financeReturn1.setDepositDate(financeAccountBillHistory.getDepositDate()); //存入时间
                            financeReturn1.setDepositorName(financeAccountBillHistory.getDepositorName()); //经手人
                            financeReturn1.setReceiveAccountDate(financeAccountBillHistory.getReceiveAccountDate());  //到账日期
                            financeReturn1.setAccountId(financeAccountBillHistory.getAccountId());  //存入银行的id
                            financeReturn1.setCreateDate(new Date());
                            financeReturn1.setCreateName(financeReturn.getCreateName());
                            financeReturn1.setCreator(user.getUserID());
                            financeReturn1.setOrg(financeReturn.getOrg());
                            financeReturn1.setMyselfId(financeReturn.getId());  //对应修改前的支票id

                            //bill表中新添加支票的明细
                            BeanUtils.copyPropertiesIgnoreNull(financeAccountBill, financeAccountBill1);
                            financeAccountBill1.setId(null);
                            financeAccountBill1.setCreator(userId);
                            financeAccountBill1.setCreateName(user.getUserName());
                            financeAccountBill1.setCreateDate(new Date());
                            financeAccountBill1.setType("1");//1-收入，2-支出
                            financeAccountBill1.setAmount(financeReturn.getAmount());//金额
                            financeAccountBill1.setBillAmount(financeReturn.getBillAmount()); //票面金额
                            financeAccountBill1.setSummary(financeReturn1.getSummary());//摘要
                            financeAccountBill1.setPurpose(financeReturn1.getPurpose());//用途
                            financeAccountBill1.setBillNo(financeReturn1.getReturnNo());//支票号码
                            financeAccountBill1.setOrg(financeAccountBill.getOrg());//机构
                            financeAccountBill1.setOperatorName(financeReturn1.getOperatorName());//经手人
                            financeAccountBill1.setMemo(financeReturn1.getMemo());//备注
                            financeAccountBill1.setAccountantStatus("3"); //会计数据状态  1-未选择  2-已选择  3-财务修改的时使用(修改后正确的数据添加的bill表数据)
                            financeAccountBill1.setModityStatus("2");
                            financeAccountBill1.setMyselfId(financeAccountBill.getId());  //数据修改时，对应修改前支票所对应的bill表的数据(本表)
                            financeAccountBill1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
//                        financeAccountBill1.setSource(financeAccountBill.getSource());
                            /*添加账务明细账表（正确账户）, 如果新添加一条汇款票据，那么bill表里也需要添加一条数据*/
                            accountDetail2.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
                            accountDetail2.setAccountId(financeAccount1);
                            accountDetail2.setFid(financeAccount1.getId().toString());  //账户id
                            accountDetail2.setOrg(user.getOrganization());
                            accountDetail2.setMethod(accountDetail.getMethod());  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                            accountDetail2.setCredit(financeReturnNew.getAmount());
                            accountDetail2.setBillAmount(financeReturnNew.getBillAmount());//票面金额
                            accountDetail2.setCreateName(user.getUserName());
                            accountDetail2.setCreateDate(new Date());
                            accountDetail2.setCreator(user.getUserID());
                            accountDetail2.setAccountBank(financeReturn1.getBankName() + financeReturn1.getAccout());//银行名和账号
                            accountDetail2.setBalance(financeAccount1.getBalance());
                            accountDetail2.setSummary(financeReturn1.getSummary());
                            accountDetail2.setAuditorName(financeReturn1.getOperatorName());
                            accountDetail2.setMemo(financeReturn1.getMemo());
                            accountDetail2.setPurpose(financeReturn1.getPurpose());
                            accountDetail2.setAuditDate(new Date());
                            accountDetail2.setSource(accountDetail.getSource());
                            accountDetail2.setBillDate(new Date());
                            accountDetail2.setPreviousId(accountDetail.getId());
                            accountDetail2.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                            //修改后的账户信息
                            if (financeAccount1.getId() != null) {
                                financeAccount1.setBalance(financeAccount.getBalance().add(financeReturn1.getAmount()));
                                financeAccount1.setCredit(financeAccount.getCredit().add(financeReturn1.getAmount()));
                            }

                            /*修改后账户的日结*/
                            accountPeriodDay1 = accountService.getAccountPeriodByDay(financeAccount1.getId(),new Date());
                            if (accountPeriodDay1 != null) {
                                accountPeriodDay1.setCredit(accountPeriodDay1.getCredit().add(financeReturn1.getAmount()));  //收入
                                accountPeriodDay1.setBalance(accountPeriodDay1.getBalance().add(financeReturn1.getAmount()));  // 本期余额

                                accountDetail2.setAccount(accountPeriodDay1.getId().toString());//期表日结的id(记录属于哪一期)
                            }

                            /*修改后账户的月结*/
                            accountPeriodMonth1 = accountService.getAccountPeriodByMonth(financeAccount1.getId(),new Date());
                            if (accountPeriodMonth1 != null) {
                                accountPeriodMonth1.setCredit(accountPeriodMonth1.getCredit().add(financeReturn1.getAmount()));
                                accountPeriodMonth1.setBalance(accountPeriodMonth1.getBalance().add(financeReturn1.getAmount()));
                            }
//                            financeReturn.setModityStatus("2");  //数据已修改
                            accountDetail.setModityStatus("2");  //数据已修改
                            accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                            financeAccountBill.setModityStatus("2");
                        }else {  //换票的，相当于新票的存入银行

                            if (financeAccount.getBalance().compareTo(new BigDecimal(0))<0){  //余额不足的直接返回吧
                                map.put("state", 2);  //余额不足
                                map.put("content", "余额不足");
                                return map;
                            }else {
                                if (!"1".equals(financeReturnNew.getState())){   //1-有效,2-存入银行,3-作废，4-已支出
                                    map.put("state", 2);  //余额不足
                                    map.put("content", "新票已使用，不在手中");
                                    return map;
                                }
                            }

                            financeReturnNew.setState("2"); //1-有效,2-存入银行,3-作废,4-已支出
                            financeReturnNew.setSaveBankName(financeReturn.getBankName()); //存入银行的名称
                            financeReturnNew.setAccout(financeAccount.getAccount()); //银行账号
                            financeReturnNew.setDepositDate(financeReturn.getDepositDate()); //存入时间
                            financeReturnNew.setDepositorName(financeReturn.getDepositorName()); //经手人
                            financeReturnNew.setReceiveAccountDate(financeReturn.getReceiveAccountDate());  //到账日期
                            financeReturnNew.setAccountId(financeReturn.getAccountId());
                            financeReturnNew.setUpdateDate(new Date());
                            financeReturnNew.setUpdator(user.getUserID());
                            financeReturnNew.setUpdateName(user.getUserName());
                            financeReturnNew.setModityStatus("1");//数据修改状态  null/1-未修改  2-已修改

                            /*总账户中*/
                            if (financeAccount.getId() != null) {
                                financeAccount.setBalance(financeAccount.getBalance().add(financeReturnNew.getAmount()));
                                financeAccount.setCredit(financeAccount.getCredit().add(financeReturnNew.getAmount()));
                                financeAccountService.updateFinanceAccount(financeAccount);
                            }

                            /*添加账务明细账表*/
                            FinanceAccountBill financeAccountBillNew = dataService.getAccountBillByReturnId(financeReturnNew.getId());

                            /*添加账务明细账表（正确账户）, 如果新添加一条汇款票据，那么bill表里也需要添加一条数据*/
                            accountDetail2.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
                            accountDetail2.setAccountId(financeAccount);
                            accountDetail2.setFid(financeReturn.getAccountId().toString());
                            accountDetail2.setOrg(financeReturnNew.getOrg());
                            if (financeReturn.getType() == 1) {
                                accountDetail2.setMethod("3");  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                            } else {
                                accountDetail2.setMethod("4");  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                            }
                            accountDetail2.setCredit(financeReturnNew.getAmount());
                            accountDetail2.setBillAmount(financeReturnNew.getBillAmount());  //票面金额
                            accountDetail2.setCreateName(user.getUserName());
                            accountDetail2.setCreateDate(new Date());
                            accountDetail2.setCreator(user.getUserID());
                            accountDetail2.setAccountBank(financeReturnNew.getBankName() + financeAccount.getAccount());//银行名和账号
                            accountDetail2.setBalance(financeAccount.getBalance());
                            accountDetail2.setSummary(financeReturnNew.getSummary());
                            accountDetail2.setAuditorName(financeReturnNew.getOperatorName());
                            accountDetail2.setMemo(financeReturnNew.getMemo());
                            accountDetail2.setPurpose(financeReturnNew.getPurpose());
                            accountDetail2.setAuditDate(new Date());
                            accountDetail2.setSource("1");
                            accountDetail2.setBillDate(new Date());
                            accountDetail2.setReceiveAccountDate(financeReturnNew.getReceiveAccountDate());  //到账日期   1.290首页财会（承接李亚星项目）  lixu 加
                            accountDetail2.setPreviousId(accountDetail.getId());
                            accountDetail2.setModityStatus("1"); //数据修改状态  null/1-未修改  2-已修改
                            accountDetail2.setModify(false);  //是否修改,false-未修改(0),true-已修改(1)
                            accountDetail2.setAccountantStatus("1"); //会计数据状态  1-未选择  2-已选择
                            if (financeAccountBillNew != null) {
                                accountDetail2.setSource(financeAccountBillNew.getSource());
                                accountDetail2.setBusiness(financeAccountBillNew.getBusiness());
                                accountDetail2.setBusinessDate(financeAccountBillNew.getBusinessDate());
                                accountDetail2.setBusinessType(financeAccountBillNew.getBusinessType());
                                accountDetail2.setBillDetail(financeAccountBillNew);

                                if ("3".equals(financeAccountBill.getSource())) {  //来源为回款录入
                                    SlCollectApplication slCollectApplication = slCollectApplicationDao.get(financeAccountBill.getBusiness());
                                    slCollectApplication.setReceiveBank(financeAccount.getId().toString());  //收款银行（存入银行）
                                    slCollectApplicationDao.update(slCollectApplication);
                                }
                            }

                            /*日结*/
                            AccountPeriod accountPeriod = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());
                            /*月结*/
                            AccountPeriod accountPeriod1 = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
                            //Debug finish ********
                            if (accountPeriod != null) {
                                accountPeriod.setCredit(accountPeriod.getCredit().add(financeReturnNew.getAmount()));  //收入
                                accountPeriod.setBalance(accountPeriod.getBalance().add(financeReturnNew.getAmount()));  // 本期余额
                                accountPeroidDao.update(accountPeriod);

                                accountDetail2.setAccount(accountPeriod.getId().toString());//期表日结的id(记录属于哪一期)
                            }
                            if (accountPeriod1!=null){
                                accountPeriod1.setCredit(accountPeriod1.getCredit().add(financeReturnNew.getAmount()));
                                accountPeriod1.setBalance(accountPeriod1.getBalance().add(financeReturnNew.getAmount()));
                                accountPeroidDao.update(accountPeriod1);
                            }
                            //原本的那个支票存入银行数据清空
                            financeReturn.setState("1"); //1-有效,2-存入银行,3-作废,4-已支出
                            financeReturn.setSaveBankName(null); //存入银行的名称
                            financeReturn.setAccout(null); //银行账号
                            financeReturn.setDepositDate(null); //存入时间
                            financeReturn.setDepositorName(null); //经手人
                            financeReturn.setReceiveAccountDate(null);  //到账日期
                            financeReturn.setAccountId(null);
                            financeReturn.setUpdateDate(new Date());
                            financeReturn.setUpdator(user.getUserID());
                            financeReturn.setUpdateName(user.getUserName());
                            financeReturn.setModityStatus("1");  //数据已修改  票据复原 成未修改
                            financeReturnDao.update(financeReturn);  //原本的那个支票存入银行数据清空

                            accountDetail.setModityStatus("2");  //数据已修改
                            accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                            financeAccountBill.setModityStatus("2");
                        }

                        } else {
                        //如果账户没有修改，原来的数据被直接覆盖
                        financeReturn.setReceiveAccountDate(financeAccountBillHistory.getReceiveAccountDate());  //到账时间
                        financeReturn.setDepositorName(financeAccountBillHistory.getDepositorName());  //存入经手人
                        financeReturn.setDepositDate(financeAccountBillHistory.getDepositDate());  //存入时间

                        financeReturn.setModityStatus("1");  //数据已修改还可以再改
                        accountDetail.setModityStatus("1");  //数据已修改还可以再改
                        financeAccountBill.setModityStatus("1");  //数据已修改还可以再改
                    }

                } else {  //驳回
                    approcalProcess.setApproveStatus("3");
                    approcalProcess.setHandleTime(new Date()); //处理时间
                    approcalProcess.setReason(reason);
                    financeAccountBillHistory.setApproveStatus("3");

                    financeReturn.setModityStatus("1");  //数据已修改但是还可以再重新修改
                    accountDetail.setModityStatus("1");  //数据已修改但是还可以再重新修改
                    financeAccountBill.setModityStatus("1");

                    if (!financeReturn.getId().equals(financeReturnNew.getId())){
                        financeReturnNew.setModityStatus("1");
                        financeAccountBill1 = dataService.getAccountBillByReturnId(financeReturnNew.getId());
                        financeAccountBill1.setModityStatus("1");
                    }
                }

                //是否修改账户或者换票了
                if (financeAccount.getId() != null) {
                    if (financeAccount.getBalance().doubleValue() >= 0) {
                        if (financeReturn.getId().equals(financeReturnNew.getId())) {
                            accountPeroidDao.update(accountPeriodDay);
                            accountDetailDao.save(accountDetail1);
                            accountPeroidDao.update(accountPeriodMonth);
                            financeAccountDao.update(financeAccount);
                            financeReturnDao.save(financeReturn1);

                            financeAccountBill1.setFinanceReturn(financeReturn1);//回款票据外键
                            financeAccountBillDao.save(financeAccountBill1);
                            accountPeroidDao.update(accountPeriodDay1);

                            accountDetail2.setBillDetail(financeAccountBill1);  //对应的bill表的数据
                            accountDetailDao.save(accountDetail2);
                            accountPeroidDao.update(accountPeriodMonth1);
                            financeReturnDao.update(financeReturn);
                            accountDetailDao.update(accountDetail);
                            approvalProcessDao.update(approcalProcess);
                            financeAccountBillHistoryDao.update(financeAccountBillHistory);

                            List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(), accountDetail.getId(), null);
                            for (FinanceAccountBillImage billImage : billImages) {  //负金额的是将原本的图片录入
                                dataService.addBillImage(user, accountDetail1.getId(), financeAccountBill.getId(), billImage.getType(), billImage.getUplaodPath(), billImage.getOrders());
                            }

                            List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(), null, financeAccountBillHistory.getId());
                            for (FinanceAccountBillImageHistory billImageHistory : billImageHistories) {  //将修改后的图片加入到修改后正确金额数据里
                                dataService.addBillImage(user, accountDetail2.getId(), financeAccountBill1.getId(), billImageHistory.getType(), billImageHistory.getUplaodPath(), billImageHistory.getOrders());
                            }
                        }else {  //换票据了的

                            financeReturnDao.update(financeReturnNew);
                            accountPeroidDao.update(accountPeriodDay);
                            accountDetailDao.save(accountDetail1);
                            accountDetailDao.save(accountDetail2);
                            accountPeroidDao.update(accountPeriodMonth);
                            financeAccountDao.update(financeAccount);
                            accountDetailDao.update(accountDetail);

                            List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(), accountDetail.getId(), null);
                            for (FinanceAccountBillImage billImage : billImages) {  //负金额的是将原本的图片录入
                                dataService.addBillImage(user, accountDetail1.getId(), financeAccountBill.getId(), billImage.getType(), billImage.getUplaodPath(), billImage.getOrders());
                            }

                            List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(), null, financeAccountBillHistory.getId());
                            for (FinanceAccountBillImageHistory billImageHistory : billImageHistories) {  //将修改后的图片加入到修改后正确金额数据里
                                dataService.addBillImage(user, accountDetail2.getId(), financeAccountBill1.getId(), billImageHistory.getType(), billImageHistory.getUplaodPath(), billImageHistory.getOrders());
                            }
                        }
                        map.put("state", 1);
                        map.put("content", "操作成功");
                    } else {
                        map.put("state", 2);  //余额不足
                        map.put("content", "余额不足");
                    }

                } else {
                    financeReturnDao.update(financeReturn);
                    accountDetailDao.update(accountDetail);
                    approvalProcessDao.update(approcalProcess);
                    financeAccountBillHistoryDao.update(financeAccountBillHistory);

                    if (1==state) {  //批准的
                        List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(), accountDetail.getId(), null);
                        for (FinanceAccountBillImage billImage : billImages) {  //原本的图片直接删除，修改后的直接覆盖
                            financeAccountBillImageDao.delete(billImage);
                        }

                        List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(), null, financeAccountBillHistory.getId());
                        for (FinanceAccountBillImageHistory billImageHistory : billImageHistories) {  //将修改后的图片加入到修改后正确的数据里
                            dataService.addBillImage(user, accountDetail.getId(), financeAccountBill.getId(), billImageHistory.getType(), billImageHistory.getUplaodPath(), billImageHistory.getOrders());
                        }
                    }
                    map.put("state", 1);
                    map.put("content", "操作成功");
                }
                this.publicPushInterface(map,approcalProcess,state);  //审批成功后公共的推送接口
            }else {
                map.put("state", 3); //已审批，不可重复操作
                map.put("content", "已审批，不可重复审批");
            }
        }else {
            map.put("state",0);
            map.put("content", "操作失败");
        }
        return map;
    }

    /**
     * 数据来源为支出中的转账支票(内部的转账支票或者改为外部的汇款票据)的修改申请审批
     * @param userId 用户id
     * @param approvalProcessId 审批流程id
     * @param state 1-批准 0-驳回
     * @param reason 驳回原因
     * @return
     */
    @Override
    public Map<String, Object> approvalBankTransfer(Integer userId, Integer approvalProcessId, Integer state, String reason) {
        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
//        Integer status = 1; // 是否修改过支票 1-修改后的支票可用 2-修改后的支票不可用
        if (approvalProcessId!=null){
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);

            if ("1".equals(approvalProcess.getApproveStatus())) {
                FinanceAccountBill financeAccountBill = financeAccountBillDao.get(approvalProcess.getOldId());  //修改前的数据
                AccountDetail accountDetail = dataService.getByBillId(financeAccountBill.getId());  //老数据对应的财务详情
                FinanceAccountBillHistory financeAccountBillHistory = financeAccountBillHistoryDao.get(approvalProcess.getNewId()); //修改的数据
                FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();  //修改前的支票
                FinanceAccount financeAccount = financeChequeDetail.getAccountId(); //修改前的账户
                FinanceAccount financeAccount1 = financeAccountDao.get(financeAccountBillHistory.getAccountId());  //修改后的账户
                FinanceReturn financeReturn = new FinanceReturn();
                FinanceAccountBill f = new FinanceAccountBill();    //支票明细（正确）
                AccountDetail accountDetail1 = new AccountDetail();     //负数据(冲)
                AccountDetail accountDetail2 = new AccountDetail();  //正确的数据(新)
                FinanceChequeDetail financeChequeDetail1 = new FinanceChequeDetail();
                FinanceAccountBill f1 = new FinanceAccountBill();              //支票明细
                AccountPeriod accountPeriodDay = new AccountPeriod();
                AccountPeriod accountPeriodMonth = new AccountPeriod();
                AccountPeriod accountPeriodDay1 = new AccountPeriod();
                AccountPeriod accountPeriodMonth1 = new AccountPeriod();
                AccountDetail accountDetail3 = new AccountDetail();    //负数据(冲)

                //当没有修改支票时，添加一条与原来的支票信息基本一样的信息
                FinanceChequeDetail financeChequeDetail2 = new FinanceChequeDetail();
                //支票明细
                FinanceAccountBill financeAccountBill1 = new FinanceAccountBill();
                if (state == 1) {  //批准
                    //支出方式修改(支出方式改变，支票定改(支票从内部的改为外部的转账支票或者是承兑汇票)，账户不一定)
                    if (!financeChequeDetail.getType().equals(financeAccountBillHistory.getType())) {
                        financeReturn = financeReturnDao.get(financeAccountBillHistory.getReturnBill());
                        financeReturn.setState("4");//1-有效,2-存入银行,3-作废,4-已使用

                        //支票明细（正确）
                        f.setOperatorName(financeAccountBillHistory.getFinancialHandling());//经手人
                        f.setCreateDate(new Date());
                        f.setCreateName(user.getUserName());
                        f.setCreator(user.getUserID());
                        f.setType("2");//1-收入，2-支出
                        f.setAmount(financeAccountBillHistory.getAmount());//金额
                        f.setBillAmount(financeAccountBillHistory.getBillAmount()); //票面金额
                        f.setSummary(financeAccountBillHistory.getSummary());//摘要k
                        f.setPurpose(financeAccountBillHistory.getPurpose());//用途
                        f.setBillQuantity(financeAccountBillHistory.getBillQuantity());//票据数量
                        f.setBillPeriod(financeAccountBillHistory.getBillPeriod());//是否本月票据
                        f.setBillCat(financeAccountBillHistory.getBillCat());//票据种类
                        f.setOppositeCorp(financeAccountBillHistory.getOppositeCorp());//收款单位
                        f.setBillNo(financeAccountBillHistory.getBillNo());//发票号码
                        f.setFinanceReturn(financeReturn);//回款票据外键
                        f.setMemo(financeAccountBillHistory.getMemo());
                        f.setOrg(user.getOrganization());
                        f.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择 3-财务修改的时使用(修改后正确的数据添加的bill表数据)
                        f.setMyselfId(financeAccountBill.getId());
                        f.setSource(financeAccountBill.getSource());
                        f.setStakeholderCategory(financeAccountBillHistory.getStakeholderCategory());
                        f.setStakeholder(financeAccountBillHistory.getStakeholder());
                        f.setBillDate(financeAccountBillHistory.getBillDate());
                        f.setFactDate(financeAccountBillHistory.getFactDate());
                        f.setSubType(financeAccountBillHistory.getSubType());
                        f.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                        financeChequeDetail.setState("3"); //1-未使用,2-已使用,3-作废
                        financeChequeDetail.setUpdateDate(new Date());
                        financeChequeDetail.setUpdateName(user.getUserName());
                        financeChequeDetail.setUpdator(userId);

                        //负数据(冲)
                        accountDetail1.setCreateDate(new Date());
                        accountDetail1.setCreator(user.getUserID());
                        accountDetail1.setCreateName(user.getUserName());
                        accountDetail1.setOrg(user.getOrganization());
                        accountDetail1.setPreviousId(accountDetail.getId());
                        accountDetail1.setAccount(accountDetail.getAccount());
                        accountDetail1.setAuditDate(new Date());
                        accountDetail1.setAuditorName(accountDetail.getAuditorName());//经手人
                        accountDetail1.setDebit(accountDetail.getDebit().multiply(new BigDecimal(-1)));
                        accountDetail1.setBillAmount(accountDetail.getBillAmount());  //票面金额
                        accountDetail1.setBalance(financeAccount.getBalance().add(financeAccountBillHistory.getAmount()));
                        accountDetail1.setMethod(accountDetail.getMethod());
                        accountDetail1.setSummary(accountDetail.getSummary());
                        accountDetail1.setPurpose(accountDetail.getPurpose());//用途
                        accountDetail1.setOppositeCorp(accountDetail.getOppositeCorp());//收款单位
                        accountDetail1.setMemo(accountDetail.getMemo());
                        accountDetail1.setFid(accountDetail.getFid());
                        accountDetail1.setAccountId(financeAccount);//账户外键
                        accountDetail1.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                        accountDetail1.setAccountBank(accountDetail.getAccountBank());
                        accountDetail1.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                        accountDetail1.setBillDetail(accountDetail.getBillDetail());
                        accountDetail1.setModityStatus("2"); //冲的负数据不能修改
                        accountDetail1.setSource(accountDetail.getSource());
                        accountDetail1.setStakeholderCategory(accountDetail.getStakeholderCategory());
                        accountDetail1.setStakeholder(accountDetail.getStakeholder());
                        accountDetail1.setBillDate(accountDetail.getBillDate());
                        accountDetail1.setFactDate(accountDetail.getFactDate());
                        accountDetail1.setSubType(accountDetail.getSubType());
                        accountDetail1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                        accountDetail.setModityStatus("2");  //数据已修改
                        accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                    } else if (!financeChequeDetail.getId().equals(financeAccountBillHistory.getCheque()) || financeAccount != financeAccount1 || !financeChequeDetail.getAmount().equals(financeAccountBillHistory.getAmount())) {

                        //修改支票
                        if (!financeChequeDetail.getId().equals(financeAccountBillHistory.getCheque())) {
                            financeChequeDetail1 = financeChequeDetailDao.get(financeAccountBillHistory.getCheque());
                            if ("1".equals(financeChequeDetail1.getState())) {
                                financeChequeDetail1.setSummary(financeAccountBillHistory.getSummary());//摘要
                                financeChequeDetail1.setPurpose(financeAccountBillHistory.getPurpose());//用途
                                financeChequeDetail1.setReceiver(financeAccountBillHistory.getReceiver());//接收经手人
                                financeChequeDetail1.setAmount(financeAccountBillHistory.getAmount());
                                financeChequeDetail1.setBillAmount(financeAccountBillHistory.getBillAmount()); //票面金额
                                financeChequeDetail1.setOperator(financeAccountBillHistory.getOperator());//支付经手人
                                financeChequeDetail1.setFinancialHandling(financeAccountBillHistory.getFinancialHandling());//财务经手人
                                financeChequeDetail1.setExpireDate(financeAccountBillHistory.getExpireDate());//支票到期日
                                financeChequeDetail1.setReceiveDate(financeAccountBillHistory.getReceiveDate());//接收日期
                                financeChequeDetail1.setMemo(financeAccountBillHistory.getMemo());
                                financeChequeDetail1.setState("2");//1-未使用,2-已使用,3-作废
                                financeChequeDetail1.setReceiveCorp(financeAccountBillHistory.getReceiveCorp());//收款单位
                                financeChequeDetail1.setUpdateDate(new Date());
                                financeChequeDetail1.setUpdateName(user.getUserName());
                                financeChequeDetail1.setUpdator(userId);

                                //支票明细
                                f1.setOperatorName(financeAccountBillHistory.getFinancialHandling());//经手人
                                f1.setCreateDate(new Date());
                                f1.setCreateName(user.getUserName());
                                f1.setCreator(user.getUserID());
                                f1.setType("2");//1-收入，2-支出
                                f1.setAmount(financeAccountBillHistory.getAmount());//金额
                                f1.setBillAmount(financeAccountBillHistory.getBillAmount());  //票面金额
                                f1.setSummary(financeAccountBillHistory.getSummary());//摘要
                                f1.setPurpose(financeAccountBillHistory.getPurpose());//用途
                                f1.setBillQuantity(financeAccountBillHistory.getBillQuantity());//票据数量
                                f1.setBillPeriod(financeAccountBillHistory.getBillPeriod());//是否本月票据
                                f1.setOppositeCorp(financeAccountBillHistory.getReceiveCorp());//收款单位
                                f1.setBillCat(financeAccountBillHistory.getBillCat());//票据种类
                                f1.setBillNo(financeAccountBillHistory.getBillNo());//发票号码
                                f1.setMemo(financeAccountBillHistory.getMemo());
                                f1.setOrg(user.getOrganization());
                                f1.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                                f1.setMyselfId(financeAccountBill.getId());
                                f1.setSource(financeAccountBill.getSource());
                                f1.setStakeholderCategory(financeAccountBillHistory.getStakeholderCategory());
                                f1.setStakeholder(financeAccountBillHistory.getStakeholder());
                                f1.setBillDate(financeAccountBillHistory.getBillDate());
                                f1.setFactDate(financeAccountBillHistory.getFactDate());
                                f1.setSubType(financeAccountBillHistory.getSubType());
                                f1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                                financeChequeDetail.setState("3");
                                financeChequeDetail.setUpdateDate(new Date());
                                financeChequeDetail.setUpdateName(user.getUserName());
                                financeChequeDetail.setUpdator(userId);
                            }else {
                                map.put("state", 4);
                                map.put("content", "选择的票据已使用，请驳回!");
                                return map;
                            }

                        } else {
                            financeAccountBill.setAmount(financeAccountBillHistory.getAmount());
                        }

                        //修改账户或者修改金额(冲账)
                        if (financeAccount != financeAccount1 || !financeChequeDetail.getAmount().equals(financeAccountBillHistory.getAmount())) {

                            if (financeAccount != financeAccount1) {  //修改账户
                                //老账户
                                financeAccount.setDebit(financeAccount.getDebit().subtract(financeAccountBillHistory.getAmount()));
                                financeAccount.setBalance(financeAccount.getBalance().add(financeAccountBillHistory.getAmount()));

                                //老账户的日结
                                accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());
                                if (accountPeriodDay != null) {
                                    accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(financeAccountBillHistory.getAmount()));  //支出
                                    accountPeriodDay.setBalance(accountPeriodDay.getBalance().add(financeAccountBillHistory.getAmount()));  // 本期余额
                                }

                                //老账户的月结
                                accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(),new Date());
                                if (accountPeriodMonth != null) {
                                    accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().subtract(financeAccountBillHistory.getAmount()));
                                    accountPeriodMonth.setBalance(accountPeriodMonth.getBalance().add(financeAccountBillHistory.getAmount()));
                                }
                                //新账户
                                financeAccount1.setDebit(financeAccount1.getDebit().add(financeAccountBillHistory.getAmount()));
                                financeAccount1.setBalance(financeAccount1.getBalance().subtract(financeAccountBillHistory.getAmount()));

                                //新账户的日结
                                accountPeriodDay1 = accountService.getAccountPeriodByDay(financeAccount1.getId(),new Date());
                                if (accountPeriodDay1 != null) {
                                    accountPeriodDay1.setDebit(accountPeriodDay1.getDebit().add(financeAccountBillHistory.getAmount()));  //支出
                                    accountPeriodDay1.setBalance(accountPeriodDay1.getBalance().subtract(financeAccountBillHistory.getAmount()));  // 本期余额
                                }

                                //新账户的月结
                                accountPeriodMonth1 = accountService.getAccountPeriodByMonth(financeAccount1.getId(),new Date());
                                if (accountPeriodMonth1 != null) {
                                    accountPeriodMonth1.setDebit(accountPeriodMonth1.getDebit().add(financeAccountBillHistory.getAmount()));
                                    accountPeriodMonth1.setBalance(accountPeriodMonth1.getBalance().subtract(financeAccountBillHistory.getAmount()));
                                }
                            } else {
                                //老账户
                                financeAccount.setDebit(financeAccount.getDebit().subtract(accountDetail.getDebit()).add(financeAccountBillHistory.getAmount()));
                                financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getDebit()).subtract(financeAccountBillHistory.getAmount()));

                                //老账户的日结
                                accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());
                                if (accountPeriodDay != null) {
                                    accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(accountDetail.getDebit()).add(financeAccountBillHistory.getAmount()));  //支出
                                    accountPeriodDay.setBalance(accountPeriodDay.getBalance().add(financeAccountBillHistory.getAmount()).subtract(financeAccountBillHistory.getAmount()));  // 本期余额
                                }

                                //老账户的月结
                                accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(),new Date());
                                if (accountPeriodMonth != null) {
                                    accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().subtract(accountDetail.getDebit()).add(financeAccountBillHistory.getAmount()));
                                    accountPeriodMonth.setBalance(accountPeriodMonth.getBalance().add(accountDetail.getDebit()).subtract(financeAccountBillHistory.getAmount()));
                                }
                            }
                        }
                        //负数据(冲)
                        accountDetail3.setCreateDate(new Date());
                        accountDetail3.setCreator(user.getUserID());
                        accountDetail3.setCreateName(user.getUserName());
                        accountDetail3.setPreviousId(accountDetail.getId());
                        accountDetail3.setOrg(user.getOrganization());
                        accountDetail3.setAccount(accountDetail.getAccount());
                        accountDetail3.setAuditDate(new Date());
                        accountDetail3.setAuditorName(accountDetail.getAuditorName());//经手人
                        accountDetail3.setDebit(accountDetail.getDebit().multiply(new BigDecimal(-1)));
                        accountDetail3.setBillAmount(accountDetail.getBillAmount());  //票面金额
                        accountDetail3.setBalance(financeAccount.getBalance().add(financeAccountBillHistory.getAmount()));
                        accountDetail3.setMethod(accountDetail.getMethod());
                        accountDetail3.setSummary(accountDetail.getSummary());
                        accountDetail3.setPurpose(accountDetail.getPurpose());//用途
                        accountDetail3.setOppositeCorp(accountDetail.getOppositeCorp());//收款单位
                        accountDetail3.setMemo(accountDetail.getMemo());
                        accountDetail3.setFid(accountDetail.getFid());
                        accountDetail3.setAccountId(financeAccount);//账户外键
                        accountDetail3.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                        accountDetail3.setAccountBank(accountDetail.getAccountBank());
                        accountDetail3.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                        accountDetail3.setBillDetail(accountDetail.getBillDetail());
                        accountDetail3.setModityStatus("2"); //冲的负数据不能修改
                        accountDetail3.setSource(accountDetail.getSource());
                        accountDetail3.setStakeholderCategory(accountDetail.getStakeholderCategory());
                        accountDetail3.setStakeholder(accountDetail.getStakeholder());
                        accountDetail3.setBillDate(accountDetail.getBillDate());
                        accountDetail3.setFactDate(accountDetail.getFactDate());
                        accountDetail3.setSubType(accountDetail.getSubType());
                        accountDetail3.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                        //正确的数据(新)
                        accountDetail2.setCreateDate(new Date());
                        accountDetail2.setCreator(user.getUserID());
                        accountDetail2.setCreateName(user.getUserName());
                        accountDetail2.setOrg(user.getOrganization());
                        accountDetail2.setPreviousId(accountDetail.getId());
                        if (accountPeriodMonth1.getId() != null) {
                            accountDetail2.setAccount(accountPeriodMonth1.getId().toString());
                        } else if (accountPeriodMonth.getId() != null) {
                            accountDetail2.setAccount(accountPeriodMonth.getId().toString());
                        }
                        accountDetail2.setAuditDate(new Date());
                        accountDetail2.setAuditorName(financeAccountBillHistory.getFinancialHandling());//经手人
                        accountDetail2.setDebit(financeAccountBillHistory.getAmount());
                        accountDetail2.setBillAmount(financeAccountBillHistory.getBillAmount());  //票面金额
                        accountDetail2.setBalance(financeAccount1.getBalance().subtract(financeAccountBillHistory.getAmount()));
                        accountDetail2.setMethod(accountDetail.getMethod());
                        accountDetail2.setSummary(financeAccountBillHistory.getSummary());
                        accountDetail2.setPurpose(financeAccountBillHistory.getPurpose());//用途
                        accountDetail2.setOppositeCorp(financeAccountBillHistory.getOppositeCorp());//收款单位
                        accountDetail2.setMemo(financeAccountBillHistory.getMemo());
                        accountDetail2.setFid(financeAccount1.getId().toString());
                        accountDetail2.setAccountId(financeAccount1);//账户外键
                        accountDetail2.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                        accountDetail2.setAccountBank(financeAccount1.getBankName() + financeAccount.getAccount());
                        accountDetail2.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                        accountDetail2.setSource(accountDetail.getSource());
                        accountDetail2.setStakeholderCategory(financeAccountBillHistory.getStakeholderCategory());
                        accountDetail2.setStakeholder(financeAccountBillHistory.getStakeholder());
                        accountDetail2.setBillDate(financeAccountBillHistory.getBillDate());
                        accountDetail2.setFactDate(financeAccountBillHistory.getFactDate());
                        accountDetail2.setSubType(financeAccountBillHistory.getSubType());
                        accountDetail2.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                        //当没有修改支票时，添加一条与原来的支票信息基本一样的信息
                        financeChequeDetail2.setBankName(financeChequeDetail.getBankName());
                        financeChequeDetail2.setAccount(financeChequeDetail.getAccount());
                        financeChequeDetail2.setAccountId(financeChequeDetail.getAccountId());
                        financeChequeDetail2.setChequeNo(financeChequeDetail.getChequeNo());
                        financeChequeDetail2.setChequeReg(financeChequeDetail.getChequeReg());
                        financeChequeDetail2.setType(financeChequeDetail.getType());  //支票类型 '1-转帐支票，2-现金汇票',
                        financeChequeDetail2.setSummary(financeAccountBillHistory.getSummary());//摘要
                        financeChequeDetail2.setPurpose(financeAccountBillHistory.getPurpose());//用途
                        financeChequeDetail2.setReceiver(financeAccountBillHistory.getReceiver());//接收经手人
                        financeChequeDetail2.setAmount(financeAccountBillHistory.getAmount());
                        financeChequeDetail2.setBillAmount(financeAccountBillHistory.getBillAmount()); //票面金额
                        financeChequeDetail2.setOperator(financeAccountBillHistory.getOperator());//支付经手人
                        financeChequeDetail2.setFinancialHandling(financeAccountBillHistory.getFinancialHandling());//财务经手人
                        financeChequeDetail2.setExpireDate(financeAccountBillHistory.getExpireDate());//支票到期日
                        financeChequeDetail2.setReceiveDate(financeAccountBillHistory.getReceiveDate());//接收日期
                        financeChequeDetail2.setMemo(financeAccountBillHistory.getMemo());
                        financeChequeDetail2.setState("2");//1-未使用,2-已使用,3-作废
                        financeChequeDetail2.setModityStatus("1");
                        financeChequeDetail2.setMyselfId(financeChequeDetail.getId());  //对应本表的id
                        financeChequeDetail2.setReceiveCorp(financeAccountBillHistory.getReceiveCorp());//收款单位
                        financeChequeDetail2.setCreateDate(financeChequeDetail.getCreateDate());
                        financeChequeDetail2.setCreateName(financeChequeDetail.getCreateName());
                        financeChequeDetail2.setCreator(financeChequeDetail.getCreator());
                        financeChequeDetail2.setUpdateDate(new Date());
                        financeChequeDetail2.setUpdator(userId);
                        financeChequeDetail2.setUpdateName(user.getUserName());

                        //支票明细
                        financeAccountBill1.setOperatorName(financeAccountBillHistory.getFinancialHandling());//经手人
                        financeAccountBill1.setCreateDate(new Date());
                        financeAccountBill1.setCreateName(user.getUserName());
                        financeAccountBill1.setCreator(user.getUserID());
                        financeAccountBill1.setType("2");//1-收入，2-支出
                        financeAccountBill1.setAmount(financeAccountBillHistory.getAmount());//金额
                        financeAccountBill1.setBillAmount(financeAccountBillHistory.getBillAmount());  //票面金额
                        financeAccountBill1.setSummary(financeAccountBillHistory.getSummary());//摘要
                        financeAccountBill1.setPurpose(financeAccountBillHistory.getPurpose());//用途
                        financeAccountBill1.setBillQuantity(financeAccountBillHistory.getBillQuantity());//票据数量
                        financeAccountBill1.setBillPeriod(financeAccountBillHistory.getBillPeriod());//是否本月票据
                        financeAccountBill1.setOppositeCorp(financeAccountBillHistory.getReceiveCorp());//收款单位
                        financeAccountBill1.setBillCat(financeAccountBillHistory.getBillCat());//票据种类
                        financeAccountBill1.setBillNo(financeAccountBillHistory.getBillNo());//发票号码
                        financeAccountBill1.setMemo(financeAccountBillHistory.getMemo());
                        financeAccountBill1.setOrg(user.getOrganization());
                        financeAccountBill1.setAccountantStatus("3");//会计数据状态  1-未选择  2-已选择 3-财务修改的时使用(修改后正确的数据添加的bill表数据)
                        financeAccountBill1.setMyselfId(financeAccountBill.getId());
                        financeAccountBill1.setSource(financeAccountBill.getSource());
                        financeAccountBill1.setStakeholderCategory(financeAccountBillHistory.getStakeholderCategory());
                        financeAccountBill1.setStakeholder(financeAccountBillHistory.getStakeholder());
                        financeAccountBill1.setBillDate(financeAccountBillHistory.getBillDate());
                        financeAccountBill1.setFactDate(financeAccountBillHistory.getFactDate());
                        financeAccountBill1.setSubType(financeAccountBillHistory.getSubType());
                        financeAccountBill1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                        accountDetail.setModityStatus("2");  //数据已修改
                        accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                    } else {
                        //不用冲账的修改数据，直接覆盖
                        financeAccountBill.setBillCat(financeAccountBillHistory.getBillCat());
                        financeAccountBill.setBillQuantity(financeAccountBillHistory.getBillQuantity());
                        financeAccountBill.setBillPeriod(financeAccountBillHistory.getBillPeriod());
                        financeAccountBill.setSummary(financeAccountBillHistory.getSummary());
                        financeAccountBill.setPurpose(financeAccountBillHistory.getPurpose());
                        financeAccountBill.setOperatorName(financeAccountBillHistory.getFinancialHandling());  //经手人
                        financeAccountBill.setOppositeCorp(financeAccountBillHistory.getReceiveCorp());
                        financeAccountBill.setMemo(financeAccountBillHistory.getMemo());
                        financeAccountBill.setModityStatus("1");
                        financeAccountBill.setStakeholderCategory(financeAccountBillHistory.getStakeholderCategory());
                        financeAccountBill.setStakeholder(financeAccountBillHistory.getStakeholder());
                        financeAccountBill.setBillDate(financeAccountBillHistory.getBillDate());
                        financeAccountBill.setFactDate(financeAccountBillHistory.getFactDate());
                        financeAccountBill.setSubType(financeAccountBillHistory.getSubType());

                        financeChequeDetail.setSummary(financeAccountBillHistory.getSummary());
                        financeChequeDetail.setPurpose(financeAccountBillHistory.getPurpose());
                        financeChequeDetail.setFinancialHandling(financeAccountBillHistory.getFinancialHandling());
                        financeChequeDetail.setExpireDate(financeAccountBillHistory.getExpireDate());
                        financeChequeDetail.setReceiveCorp(financeAccountBillHistory.getReceiveCorp());  //收款单位
                        financeChequeDetail.setReceiveDate(financeAccountBillHistory.getReceiveDate());  //接收日期
                        financeChequeDetail.setReceiver(financeAccountBillHistory.getReceiver());  //接收经手人
                        financeChequeDetail.setOperator(financeAccountBillHistory.getOperator());  //支付经手人
                        financeChequeDetail.setMemo(financeAccountBillHistory.getMemo());
                        financeChequeDetail.setModityStatus("1");
                        financeChequeDetail.setUpdateDate(new Date());
                        financeChequeDetail.setUpdateName(user.getUserName());
                        financeChequeDetail.setUpdator(userId);

                        accountDetail.setModityStatus("1");  //数据已修改
                        accountDetail.setSummary(financeAccountBillHistory.getSummary());
                        accountDetail.setPurpose(financeAccountBillHistory.getPurpose());
                        accountDetail.setAuditorName(financeAccountBillHistory.getFinancialHandling());
                    }

                    approvalProcess.setHandleTime(new Date()); //处理时间
                    approvalProcess.setApproveStatus("2");
                    financeAccountBillHistory.setApproveStatus("2");

                } else {   //驳回
                    approvalProcess.setHandleTime(new Date()); //处理时间
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setReason(reason);
                    financeAccountBillHistory.setApproveStatus("3");

                    accountDetail.setModityStatus("1");
                    financeChequeDetail.setModityStatus("1");
                }

                if (state == 1) {  //批准
                    if (financeReturn.getId() != null) {   //修改了支出方式
                        financeReturnDao.update(financeReturn);
                        financeAccountBillDao.save(f);
                        accountDetailDao.save(accountDetail1);   //负数据(冲)
                        accountDetailDao.update(accountDetail);   //老数据对应的财务详情
                        approvalProcessDao.update(approvalProcess);
                        financeAccountBillHistoryDao.update(financeAccountBillHistory);
//                        accountDetailDao.update(accountDetail);
                        financeChequeDetailDao.update(financeChequeDetail);

                        List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(),accountDetail.getId(),null);
                        for (FinanceAccountBillImage billImage:billImages) {  //负金额的是将原本的图片录入
                            dataService.addBillImage(user,accountDetail1.getId(),null,billImage.getType(),billImage.getUplaodPath(),billImage.getOrders());
                        }

//                        List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),null,financeAccountBillHistory.getId());
//                        for (FinanceAccountBillImageHistory billImageHistory:billImageHistories) {  //将修改后的图片加入到修改后正确金额数据里
//                            dataService.addBillImage(user,accountDetail2.getId(),financeAccountBill1.getId(),billImageHistory.getType(),billImageHistory.getUplaodPath(),billImageHistory.getOrders());
//                        }

                        map.put("state", 1);
                        map.put("content", "操作成功");
                    } else if (!financeChequeDetail.getId().equals(financeAccountBillHistory.getCheque()) || financeAccount != financeAccount1 || !financeChequeDetail.getAmount().equals(financeAccountBillHistory.getAmount())) {
                        if (!financeChequeDetail.getId().equals(financeAccountBillHistory.getCheque())) {  //仅仅修改了支票
                            f1.setCheque(financeChequeDetail1);//转账支票外键
                            financeChequeDetailDao.update(financeChequeDetail1);
                            financeAccountBillDao.save(f1);
                            financeChequeDetailDao.update(financeChequeDetail);

                            accountDetailDao.save(accountDetail3);//冲账-负数据(冲)

                            if (!financeChequeDetail.getId().equals(financeAccountBillHistory.getCheque())) {
                                accountDetail2.setBillDetail(f1);
                            } else {
                                accountDetail2.setBillDetail(accountDetail.getBillDetail());
                            }
                            accountDetailDao.save(accountDetail2);//正确的数据(新)
                            accountDetailDao.update(accountDetail);   //老数据对应的财务详情

                            List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(),accountDetail.getId(),null);
                            for (FinanceAccountBillImage billImage:billImages) {  //负金额的是将原本的图片录入
                                dataService.addBillImage(user,accountDetail3.getId(),null,billImage.getType(),billImage.getUplaodPath(),billImage.getOrders());
                            }

                            List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),null,financeAccountBillHistory.getId());
                            for (FinanceAccountBillImageHistory billImageHistory:billImageHistories) {  //将修改后的图片加入到修改后正确金额数据里
                                dataService.addBillImage(user,accountDetail2.getId(),null,billImageHistory.getType(),billImageHistory.getUplaodPath(),billImageHistory.getOrders());
                            }

                            map.put("state", 1);
                            map.put("content", "操作成功");
                        } else {
                            if (financeAccount.getBalance().doubleValue() >= 0 || financeAccount1.getBalance().doubleValue() >= 0) {
                                if (financeAccount != financeAccount1) {
                                    financeAccountDao.update(financeAccount1);
                                    if (accountPeriodDay1 != null && accountPeriodMonth1 != null) {
                                        accountPeroidDao.update(accountPeriodDay1);
                                        accountPeroidDao.update(accountPeriodMonth1);
                                    }
                                }
                                financeAccountDao.update(financeAccount);
                                if (accountPeriodDay != null) {
                                    accountPeroidDao.update(accountPeriodDay);
                                }
                                if (accountPeriodMonth != null) {
                                    accountPeroidDao.update(accountPeriodMonth);
                                }
                                accountDetailDao.save(accountDetail3);  //冲账-负数据(冲)

                                if (!financeChequeDetail.getId().equals(financeAccountBillHistory.getCheque())) {
                                    accountDetail2.setBillDetail(f1);
                                } else {
                                    financeChequeDetailDao.save(financeChequeDetail2);

                                    financeAccountBill1.setCheque(financeChequeDetail2);
                                    financeAccountBillDao.save(financeAccountBill1);
                                    accountDetail2.setBillDetail(financeAccountBill1);
                                }
                                accountDetailDao.save(accountDetail2); //正确的数据(新)
                                accountDetailDao.update(accountDetail);  //老数据对应的财务详情

                                if (financeChequeDetail.getMyselfId() != null) {
                                    FinanceChequeDetail financeChequeDetail3 = new FinanceChequeDetail();
                                    Integer chequeDetailId = financeChequeDetail.getMyselfId();
                                    while (chequeDetailId != null) {
                                        financeChequeDetail3 = financeChequeDetailDao.get(chequeDetailId);
                                        chequeDetailId = financeChequeDetail3.getMyselfId();
                                    }
                                    financeChequeDetail3.setAmount(financeAccountBillHistory.getAmount());
                                    financeChequeDetailDao.update(financeChequeDetail3);

                                } else {
                                    financeChequeDetail.setAmount(financeAccountBillHistory.getAmount());
                                }
                                financeChequeDetailDao.update(financeChequeDetail);
                                financeAccountBillDao.update(financeAccountBill);

                                List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(),accountDetail.getId(),null);
                                for (FinanceAccountBillImage billImage:billImages) {  //负金额的是将原本的图片录入
                                    dataService.addBillImage(user,accountDetail3.getId(),financeAccountBill.getId(),billImage.getType(),billImage.getUplaodPath(),billImage.getOrders());
                                }

                                List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),null,financeAccountBillHistory.getId());
                                for (FinanceAccountBillImageHistory billImageHistory:billImageHistories) {  //将修改后的图片加入到修改后正确金额数据里
                                    dataService.addBillImage(user,accountDetail2.getId(),accountDetail2.getBillDetail_(),billImageHistory.getType(),billImageHistory.getUplaodPath(),billImageHistory.getOrders());
                                }

                                map.put("state", 1);
                                map.put("content", "操作成功");
                            } else {
                                map.put("state", 2);  //余额不足
                                map.put("content", "余额不足");
                            }
                        }
                    } else {  //不需要冲账，仅仅覆盖数据
                        approvalProcessDao.update(approvalProcess);
                        financeAccountBillHistoryDao.save(financeAccountBillHistory);
                        accountDetailDao.update(accountDetail);
                        financeChequeDetailDao.update(financeChequeDetail);
                        financeAccountBillDao.update(financeAccountBill);
                        financeChequeDetailDao.update(financeChequeDetail);

                        List<FinanceAccountBillImage> billImages = dataService.getBillImageByIds(user.getOid(), accountDetail.getId(), null);
                        for (FinanceAccountBillImage billImage : billImages) {  //原本的图片直接删除，修改后的直接覆盖
                            financeAccountBillImageDao.delete(billImage);
                        }
                        List<FinanceAccountBillImageHistory> billImageHistories = dataService.getBillImageHistoryByIds(user.getOid(), null, financeAccountBillHistory.getId());
                        for (FinanceAccountBillImageHistory billImageHistory : billImageHistories) {  //将修改后的图片加入到修改后正确的数据里
                            dataService.addBillImage(user, accountDetail.getId(), financeAccountBill.getId(), billImageHistory.getType(), billImageHistory.getUplaodPath(), billImageHistory.getOrders());
                        }

                        map.put("state", 1);
                        map.put("content", "操作成功");
                    }
                } else {  //驳回
                    approvalProcessDao.update(approvalProcess);
                    financeAccountBillHistoryDao.save(financeAccountBillHistory);
                    accountDetailDao.update(accountDetail);
                    financeChequeDetailDao.update(financeChequeDetail);

                    map.put("state", 1);
                    map.put("content", "操作成功");
                }

                this.publicPushInterface(map,approvalProcess,state);  //审批成功后公共的推送接口
            }else {
                map.put("state", 3);  //已审批，不可重复操作
                map.put("content", "已审批，不可重复审批");
            }
        }else {
            map.put("state",0);
            map.put("content", "操作失败");
        }
        return map;
    }

    /**
     * 内部非支出性转账 批准 驳回
     * @param userId
     * @param approvalProcessId
     * @param state
     * @param reason
     * @return
     */
    @Override
    public Map<String, Object> approvalTransferAccounts(Integer userId, Integer approvalProcessId, Integer state, String reason) {
        User user = userDao.get(userId);
        Map<String, Object> map = new HashMap<String, Object>();
        if (approvalProcessId != null) {
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            AccountDetail oldDetail = accountService.getAccountDetailById(approvalProcess.getOldId());
            if ("1".equals(approvalProcess.getApproveStatus())) {
                if (state == 1) {
                    AccountDetailHistory newDetail = accountService.getAccountDetailHistoryById(approvalProcess.getNewId());

                    //本账户
                    FinanceAccount financeAccount = accountService.getFinanceAccountById(Integer.valueOf(oldDetail.getFid()));

                    //本账户 月结 日结
                    AccountPeriod yue = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
                    AccountPeriod ri = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());

                    //另一个账户
                    FinanceAccount lingFinance = accountService.getFinanceAccountById(oldDetail.getOppositeId());
                    AccountPeriod lingyue = accountService.getAccountPeriodByMonth(lingFinance.getId(), new Date());
                    AccountPeriod lingri = accountService.getAccountPeriodByDay(lingFinance.getId(), new Date());

                    //新明细
                    AccountDetail accountDetail = new AccountDetail();
                    accountDetail.setPreviousId(oldDetail.getId());
                    accountDetail.setType(oldDetail.getType());
                    accountDetail.setModityStatus("1");
                    accountDetail.setAccountantStatus(oldDetail.getAccountantStatus());
                    accountDetail.setAccountBank(oldDetail.getAccountBank());
                    accountDetail.setAccount(oldDetail.getAccount());
                    accountDetail.setAccountId(oldDetail.getAccountId());
                    accountDetail.setAccountName(oldDetail.getAuditorName());
                    accountDetail.setAccountType(oldDetail.getAccountType());
                    accountDetail.setApproveItem(oldDetail.getApproveItem());
                    accountDetail.setApplyMemo(oldDetail.getApplyMemo());
                    accountDetail.setBillDetail(oldDetail.getBillDetail());
                    accountDetail.setAuditor(oldDetail.getAuditor());
                    accountDetail.setAuditorName(oldDetail.getAuditorName());
                    accountDetail.setBillCat(oldDetail.getBillCat());
                    accountDetail.setChequeId(oldDetail.getChequeId());
                    accountDetail.setChequeNo(oldDetail.getChequeNo());
                    accountDetail.setMethod(oldDetail.getMethod());
                    accountDetail.setType(oldDetail.getType());
                    accountDetail.setFid(oldDetail.getFid());
                    accountDetail.setAuditDate(new Date());
                    accountDetail.setMemo(newDetail.getMemo());
                    accountDetail.setCreateDate(new Date());
                    accountDetail.setOrg(oldDetail.getOrg());
                    accountDetail.setSummary(oldDetail.getSummary());
                    accountDetail.setOppositeAccount(oldDetail.getOppositeAccount());//（收款时为付款账号，付款为收款账号）
                    accountDetail.setOppositeId(oldDetail.getOppositeId());//（收款时为付款账号id，付款为收款账号id）
                    accountDetail.setAccountBank(oldDetail.getAccountBank());
                    accountDetail.setCreateName(user.getUserName());
                    accountDetail.setCreator(userId);
                    accountDetail.setSource(oldDetail.getSource());
                    accountDetail.setBillDate(new Date());
                    accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)


                    //冲账明细
                    AccountDetail chong = new AccountDetail();
                    chong.setPreviousId(oldDetail.getId());
                    chong.setModityStatus("2");
                    chong.setType(oldDetail.getType());
                    chong.setAccountantStatus(oldDetail.getAccountantStatus());
                    chong.setAccountBank(oldDetail.getAccountBank());
                    chong.setAccount(oldDetail.getAccount());
                    chong.setAccountId(oldDetail.getAccountId());
                    chong.setApproveItem(oldDetail.getApproveItem());
                    chong.setApplyMemo(oldDetail.getApplyMemo());
                    chong.setBillDetail(oldDetail.getBillDetail());
                    chong.setAuditor(oldDetail.getAuditor());
                    chong.setAuditorName(oldDetail.getAuditorName());
                    chong.setBillCat(oldDetail.getBillCat());
                    chong.setChequeId(oldDetail.getChequeId());
                    chong.setChequeNo(oldDetail.getChequeNo());
                    chong.setMethod(oldDetail.getMethod());
                    chong.setType(oldDetail.getType());
                    chong.setFid(oldDetail.getFid());
                    chong.setAuditDate(new Date());
                    chong.setMemo(newDetail.getMemo());
                    chong.setCreateDate(new Date());
                    chong.setOrg(oldDetail.getOrg());
                    chong.setSummary(oldDetail.getSummary());
                    chong.setOppositeAccount(oldDetail.getOppositeAccount());//（收款时为付款账号，付款为收款账号）
                    chong.setOppositeId(oldDetail.getOppositeId());//（收款时为付款账号id，付款为收款账号id）
                    chong.setAccountBank(oldDetail.getAccountBank());
                    chong.setCreateName(user.getUserName());
                    chong.setCreator(userId);
                    chong.setSource(oldDetail.getSource());
                    chong.setBillDate(new Date());
                    chong.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                    //另一个账户新明细
                    AccountDetail lingDetail = new AccountDetail();
                    lingDetail.setPreviousId(oldDetail.getId());
                    lingDetail.setType(oldDetail.getType());
                    lingDetail.setModityStatus("1");
                    lingDetail.setAccountantStatus(oldDetail.getAccountantStatus());
                    lingDetail.setApproveItem(oldDetail.getApproveItem());
                    lingDetail.setApplyMemo(oldDetail.getApplyMemo());
                    lingDetail.setBillDetail(oldDetail.getBillDetail());
                    lingDetail.setAuditor(oldDetail.getAuditor());
                    lingDetail.setAuditorName(oldDetail.getAuditorName());
                    lingDetail.setBillCat(oldDetail.getBillCat());
                    lingDetail.setChequeId(oldDetail.getChequeId());
                    lingDetail.setChequeNo(oldDetail.getChequeNo());
                    lingDetail.setMethod(oldDetail.getMethod());
                    lingDetail.setCreateDate(new Date());
                    lingDetail.setAuditDate(new Date());
                    lingDetail.setMemo(newDetail.getMemo());
                    lingDetail.setFid(lingFinance.getId().toString());
                    lingDetail.setAccountId(lingFinance);
                    lingDetail.setOppositeId(financeAccount.getId());
                    lingDetail.setAccountBank(lingFinance.getBankName());
                    lingDetail.setSource(oldDetail.getSource());
                    if (lingFinance.getOperation() != null) {
                        lingDetail.setAccountBank(lingDetail.getAccountBank() + "（" + lingFinance.getOperation() + "）");
                    }
                    if (lingFinance.getAccount() != null) {
                        lingDetail.setAccountBank(lingDetail.getAccountBank() + lingFinance.getAccount());
                    }
                    lingDetail.setOppositeAccount(financeAccount.getBankName());
                    if (financeAccount.getOperation() != null) {
                        lingDetail.setOppositeAccount(lingDetail.getOppositeAccount() + "（" + financeAccount.getOperation() + "）");
                    }
                    if (financeAccount.getAccount() != null) {
                        lingDetail.setOppositeAccount(lingDetail.getOppositeAccount() + financeAccount.getAccount());
                    }
                    lingDetail.setBillDate(new Date());
                    lingDetail.setCreateDate(new Date());
                    lingDetail.setOrg(oldDetail.getOrg());
                    lingDetail.setSummary(oldDetail.getSummary());
                    lingDetail.setOppositeId(financeAccount.getId());
                    lingDetail.setAccountBank(lingFinance.getBankName());
                    lingDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                    if (lingFinance.getOperation() != null) {
                        lingDetail.setAccountBank(lingDetail.getAccountBank() + "（" + lingFinance.getOperation() + "）");
                    }
                    if (lingFinance.getAccount() != null) {
                        lingDetail.setAccountBank(lingDetail.getAccountBank() + lingFinance.getAccount());
                    }

                    lingDetail.setOppositeAccount(financeAccount.getBankName());
                    if (financeAccount.getOperation() != null) {
                        lingDetail.setOppositeAccount(lingDetail.getOppositeAccount() + "（" + financeAccount.getOperation() + "）");
                    }
                    if (financeAccount.getAccount() != null) {
                        lingDetail.setOppositeAccount(lingDetail.getOppositeAccount() + financeAccount.getAccount());
                    }

                    //另一个账户冲账明细
                    AccountDetail lingchong = new AccountDetail();
                    lingchong.setPreviousId(oldDetail.getId());
                    lingchong.setModityStatus("2");
                    lingchong.setType(oldDetail.getType());
                    lingchong.setAccountantStatus(oldDetail.getAccountantStatus());
                    lingchong.setApproveItem(oldDetail.getApproveItem());
                    lingchong.setApplyMemo(oldDetail.getApplyMemo());
                    lingchong.setBillDetail(oldDetail.getBillDetail());
                    lingchong.setAuditor(oldDetail.getAuditor());
                    lingchong.setAuditorName(oldDetail.getAuditorName());
                    lingchong.setBillCat(oldDetail.getBillCat());
                    lingchong.setChequeId(oldDetail.getChequeId());
                    lingchong.setChequeNo(oldDetail.getChequeNo());
                    lingchong.setMethod(oldDetail.getMethod());
                    lingchong.setMemo(newDetail.getMemo());
                    lingchong.setAuditDate(new Date());
                    lingchong.setMemo(newDetail.getMemo());
                    lingchong.setFid(lingFinance.getId().toString());
                    lingchong.setAccountId(lingFinance);
                    lingchong.setAccountBank(lingFinance.getBankName());
                    lingchong.setSource(oldDetail.getSource());
                    if (lingFinance.getOperation() != null) {
                        lingchong.setAccountBank(lingchong.getAccountBank() + "（" + lingFinance.getOperation() + "）");
                    }
                    if (lingFinance.getAccount() != null) {
                        lingchong.setAccountBank(lingchong.getAccountBank() + lingFinance.getAccount());
                    }
                    lingchong.setOppositeAccount(financeAccount.getBankName());
                    if (financeAccount.getOperation() != null) {
                        lingchong.setOppositeAccount(lingchong.getOppositeAccount() + "（" + financeAccount.getOperation() + "）");
                    }
                    if (financeAccount.getAccount() != null) {
                        lingchong.setOppositeAccount(lingchong.getOppositeAccount() + financeAccount.getAccount());
                    }
                    lingchong.setCreateDate(new Date());
                    lingchong.setOrg(oldDetail.getOrg());
                    lingchong.setSummary(oldDetail.getSummary());
                    lingchong.setOppositeAccount(lingDetail.getOppositeAccount());//（收款时为付款账号，付款为收款账号）
                    lingchong.setOppositeId(lingDetail.getOppositeId());//（收款时为付款账号id，付款为收款账号id）
                    lingchong.setAccountBank(lingDetail.getAccountBank());
                    lingchong.setBillDate(new Date());
                    lingchong.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                    if (newDetail.getCredit() != null && newDetail.getCredit().doubleValue() != oldDetail.getCredit().doubleValue() || newDetail.getDebit() != null && newDetail.getDebit().doubleValue() != oldDetail.getDebit().doubleValue()) {
                        if (newDetail.getCredit() != null && newDetail.getCredit().doubleValue() != oldDetail.getCredit().doubleValue()) {
                            //本账户
                            accountDetail.setBalance(financeAccount.getBalance().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));
                            accountDetail.setCredit(newDetail.getCredit());//新收入
                            accountDetail.setBillAmount(newDetail.getCredit());  //票面金额

                            chong.setBalance(financeAccount.getBalance().subtract(oldDetail.getCredit()));
                            chong.setCredit(new BigDecimal(0).subtract(oldDetail.getCredit()));
                            chong.setBillAmount(oldDetail.getCredit());

                            financeAccount.setBalance(financeAccount.getBalance().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));
                            financeAccount.setCredit(financeAccount.getCredit().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));

                            yue.setBalance(financeAccount.getBalance());
                            yue.setCredit(yue.getCredit().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));

                            ri.setBalance(financeAccount.getBalance());
                            ri.setCredit(ri.getCredit().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));

                            //另一个账户
                            lingDetail.setBalance(lingFinance.getBalance().add(oldDetail.getCredit()).subtract(newDetail.getCredit()));
                            lingDetail.setDebit(newDetail.getCredit());
                            lingDetail.setBillAmount(newDetail.getCredit());

                            lingchong.setBalance(lingFinance.getBalance().add(oldDetail.getCredit()));
                            lingchong.setDebit(oldDetail.getCredit().multiply(new BigDecimal(-1)));
                            lingchong.setBillAmount(oldDetail.getCredit());

                            lingFinance.setBalance(lingFinance.getBalance().add(oldDetail.getCredit()).subtract(newDetail.getCredit()));
                            lingFinance.setDebit(lingFinance.getDebit().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));//总支出减去旧支出，加上新支出

                            lingyue.setBalance(lingFinance.getBalance());
                            lingyue.setDebit(lingyue.getDebit().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));//总支出减去旧支出，加上新支出

                            lingri.setBalance(lingFinance.getBalance());
                            lingri.setDebit(lingri.getDebit().subtract(oldDetail.getCredit()).add(newDetail.getCredit()));//总支出减去旧支出，加上新支出


                        } else {
                            //本账户
                            accountDetail.setBalance(financeAccount.getBalance().add(oldDetail.getDebit()).subtract(newDetail.getDebit()));
                            accountDetail.setDebit(newDetail.getDebit());//新支出
                            accountDetail.setBillAmount(newDetail.getDebit());

                            chong.setBalance(financeAccount.getBalance().add(oldDetail.getDebit()));
                            chong.setDebit(new BigDecimal(0).subtract(oldDetail.getDebit()));
                            chong.setBillAmount(oldDetail.getDebit());

                            financeAccount.setBalance(financeAccount.getBalance().add(oldDetail.getDebit()).subtract(newDetail.getDebit()));
                            financeAccount.setDebit(financeAccount.getDebit().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));

                            yue.setBalance(financeAccount.getBalance());
                            yue.setDebit(yue.getDebit().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));

                            ri.setBalance(financeAccount.getBalance());
                            ri.setDebit(ri.getDebit().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));

                            //另一个账户
                            lingDetail.setBalance(lingFinance.getBalance().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));
                            lingDetail.setCredit(newDetail.getDebit());
                            lingDetail.setBillAmount(newDetail.getDebit());

                            lingchong.setBalance(lingFinance.getBalance().subtract(oldDetail.getDebit()));
                            lingchong.setCredit(new BigDecimal(0).subtract(oldDetail.getDebit()));
                            lingchong.setBillAmount(oldDetail.getDebit());

                            lingFinance.setBalance(lingFinance.getBalance().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));
                            lingFinance.setCredit(lingFinance.getCredit().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));

                            lingyue.setBalance(lingFinance.getBalance());
                            lingyue.setCredit(lingyue.getCredit().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));

                            lingri.setBalance(lingFinance.getBalance());
                            lingri.setCredit(lingri.getCredit().subtract(oldDetail.getDebit()).add(newDetail.getDebit()));
                        }

                        if (financeAccount.getBalance().doubleValue() >= 0 && lingFinance.getBalance().doubleValue() >= 0) {
                            chong.setAccount(yue.getId().toString());
                            accountService.saveAccountDetail(chong);
                            accountDetail.setAccount(yue.getId().toString());
                            accountService.saveAccountDetail(accountDetail);
                            lingchong.setAccount(lingyue.getId().toString());
                            accountService.saveAccountDetail(lingchong);
                            lingDetail.setAccount(lingyue.getId().toString());

                            lingDetail.setDetailId(accountDetail.getId());//新明细互斥id
                            accountService.saveAccountDetail(lingDetail);
                            accountDetail.setDetailId(lingDetail.getId());
                            accountService.updateAccountDetail(accountDetail);

                            accountService.updateAccountPeroid(yue);
                            accountService.updateAccountPeroid(ri);
                            accountService.updateFinanceAccount(financeAccount);
                            accountService.updateAccountPeroid(lingyue);
                            accountService.updateAccountPeroid(lingri);
                            accountService.updateFinanceAccount(lingFinance);

                            oldDetail.setModityStatus("2");//已冲账
                            oldDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                            accountService.updateAccountDetail(oldDetail);

                            if (oldDetail.getDetailId() != null) {
                                AccountDetail lingOldDetail = accountService.getAccountDetailById(oldDetail.getDetailId());
                                lingOldDetail.setModityStatus("2");//已冲账
                                lingOldDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                                accountService.updateAccountDetail(lingOldDetail);
                            }

                            approvalProcess.setHandleTime(new Date()); //处理时间
                            approvalProcess.setApproveStatus("2");
                            approvalProcessDao.update(approvalProcess);

                            map.put("state", 1);//批准成功
                            map.put("content", "操作成功");//批准成功
                        } else {
                            map.put("state", 2);//账户余额不足
                            map.put("content", "余额不足");//批准成功
                        }

                    } else {
                        oldDetail.setAuditDate(newDetail.getAuditDate());
                        oldDetail.setMemo(newDetail.getMemo());
                        oldDetail.setModityStatus("1");
                        accountService.updateAccountDetail(oldDetail);//直接更新老数据

                        if (oldDetail.getDetailId() != null) {
                            AccountDetail lingOldDetail = accountService.getAccountDetailById(oldDetail.getDetailId());
                            lingOldDetail.setAuditDate(newDetail.getAuditDate());
                            lingOldDetail.setMemo(newDetail.getMemo());
                            lingOldDetail.setModityStatus("1");//未冲账
                            accountService.updateAccountDetail(lingOldDetail);
                        }
                        approvalProcess.setHandleTime(new Date()); //处理时间
                        approvalProcess.setApproveStatus("2");
                        approvalProcessDao.update(approvalProcess);
                        map.put("state", 1);//批准成功
                        map.put("content", "操作成功");//批准成功
                    }

                    if (map.get("state").equals(1)) {
                        if (newDetail.getBillHistoryId() != null) {

                            FinanceAccountBillHistory financeAccountBillHistory = dataService.getBillHistoryById(newDetail.getBillHistoryId());

                            FinanceChequeDetail oldChequeDetail = financeChequeService.getChequeByDetailId(oldDetail.getBillDetail().getCheque_());
                            oldChequeDetail.setState("3");//1-未使用,2-已使用,3-作废
                            financeChequeService.updateCashChequeState(oldChequeDetail);

                            FinanceChequeDetail financeChequeDetail = financeChequeService.getChequeByDetailId(financeAccountBillHistory.getCheque());
                            financeChequeDetail.setAmount(newDetail.getCredit());
                            financeChequeDetail.setBillAmount(oldChequeDetail.getBillAmount());
                            financeChequeDetail.setOperateDate(oldChequeDetail.getOperateDate()); //业务发生时间
                            financeChequeDetail.setState("2");//1-未使用,2-已使用,3-作废
                            financeChequeDetail.setUpdateDate(new Date());
                            financeChequeDetail.setUpdateName(user.getUserName());
                            financeChequeDetail.setUpdator(userId);
                            financeChequeService.updateCashChequeState(financeChequeDetail);

                            FinanceAccountBill financeAccountBill = new FinanceAccountBill();
                            financeAccountBill.setAuditDate(oldDetail.getBillDetail().getAuditDate());
                            financeAccountBill.setBillCat(oldDetail.getBillDetail().getBillCat());
                            financeAccountBill.setBillNo(oldDetail.getBillDetail().getBillNo());
                            financeAccountBill.setBillPeriod(oldDetail.getBillDetail().getBillPeriod());
                            financeAccountBill.setBillQuantity(oldDetail.getBillDetail().getBillQuantity());
                            financeAccountBill.setCreateName(oldDetail.getBillDetail().getCreateName());
                            financeAccountBill.setCreator(oldDetail.getBillDetail().getCreator());
                            financeAccountBill.setMemo(oldDetail.getBillDetail().getMemo());
                            financeAccountBill.setOppositeCorp(oldDetail.getBillDetail().getOppositeCorp());
                            financeAccountBill.setPurpose(oldDetail.getBillDetail().getPurpose());
                            financeAccountBill.setSummary(oldDetail.getBillDetail().getSummary());
                            financeAccountBill.setType(oldDetail.getBillDetail().getType());
                            financeAccountBill.setOperatorName(oldDetail.getBillDetail().getOperatorName());
                            financeAccountBill.setOrg(oldDetail.getBillDetail().getOrg());
                            financeAccountBill.setAccountantStatus(oldDetail.getBillDetail().getAccountantStatus());
                            financeAccountBill.setModityStatus(oldDetail.getBillDetail().getModityStatus());
                            financeAccountBill.setSource(oldDetail.getSource());
                            financeAccountBill.setBillDate(oldDetail.getBillDate());
                            financeAccountBill.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                            financeAccountBill.setCreateDate(new Date());
                            if (newDetail.getCredit() != null && newDetail.getCredit().doubleValue() != oldDetail.getCredit().doubleValue()) {
                                financeAccountBill.setAmount(newDetail.getCredit());
                                financeAccountBill.setBillAmount(newDetail.getCredit());
                            } else {
                                financeAccountBill.setAmount(newDetail.getDebit());
                                financeAccountBill.setBillAmount(newDetail.getDebit());
                            }
                            financeAccountBill.setCheque(financeChequeDetail);
                            financeAccountBill.setMyselfId(oldDetail.getBillDetail().getId());//老bill的id
                            dataService.saveFinanceAccountBill(financeAccountBill);

                            chong.setBillDetail(oldDetail.getBillDetail());//旧票放到本账户冲账明细
                            lingchong.setBillDetail(oldDetail.getBillDetail());//旧票放到另一账户冲账明细
                            accountDetail.setBillDetail(financeAccountBill);//新票放到本账户新明细
                            lingDetail.setBillDetail(financeAccountBill);//新票放到对方账户新明细

                            if (newDetail.getCredit() != null && newDetail.getCredit().doubleValue() != oldDetail.getCredit().doubleValue() || newDetail.getDebit() != null && newDetail.getDebit().doubleValue() != oldDetail.getDebit().doubleValue()) {
                                if (chong.getId() != null)
                                    accountService.updateAccountDetail(chong);
                                if (lingchong.getId() != null)
                                    accountService.updateAccountDetail(lingchong);
                                if (accountDetail.getId() != null)
                                    accountService.updateAccountDetail(accountDetail);
                                if (lingDetail.getId() != null)
                                    accountService.updateAccountDetail(lingDetail);
                            } else {
                                oldDetail.setModityStatus("1");//未冲账
                                oldDetail.setBillDetail(financeAccountBill);
                                accountService.updateAccountDetail(oldDetail);

                                if (oldDetail.getDetailId() != null) {
                                    AccountDetail lingOldDetail = accountService.getAccountDetailById(oldDetail.getDetailId());
                                    lingOldDetail.setAuditDate(newDetail.getAuditDate());
                                    lingOldDetail.setMemo(newDetail.getMemo());
                                    lingOldDetail.setModityStatus("1");//未冲账
                                    lingOldDetail.setBillDetail(financeAccountBill);
                                    accountService.updateAccountDetail(lingOldDetail);
                                }
                            }
                        } else {
                            if (oldDetail.getBillDetail() != null) {
                                FinanceAccountBill financeAccountBill = dataService.getAccountBillByBillId(oldDetail.getBillDetail().getId());

                                FinanceChequeDetail financeChequeDetail = financeChequeService.getChequeByDetailId(financeAccountBill.getCheque().getId());
                                financeChequeDetail.setState("2");//1-未使用,2-已使用,3-作废
                                financeChequeDetail.setUpdateDate(new Date());
                                financeChequeDetail.setUpdateName(user.getUserName());
                                financeChequeDetail.setUpdator(userId);
                                if (newDetail.getCredit() != null && newDetail.getCredit().doubleValue() != oldDetail.getCredit().doubleValue()) {
                                    financeAccountBill.setAmount(newDetail.getCredit());
                                    financeAccountBill.setBillAmount(newDetail.getCredit());  //票面金额
                                    financeAccountBill.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                                    financeChequeDetail.setAmount(newDetail.getCredit());
                                    financeChequeDetail.setBillAmount(newDetail.getCredit());  //票面金额
                                } else {
                                    financeAccountBill.setAmount(newDetail.getDebit());
                                    financeAccountBill.setBillAmount(newDetail.getDebit());  //票面金额
                                    financeAccountBill.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                                    financeChequeDetail.setAmount(newDetail.getDebit());
                                    financeChequeDetail.setBillAmount(newDetail.getDebit());  //票面金额
                                }
                                dataService.updateFinanceAccountBill(financeAccountBill);
                                financeChequeService.updateCashChequeState(financeChequeDetail);
                            }
                        }
                    }
                } else {
                    //否则为驳回
                    approvalProcess.setHandleTime(new Date()); //处理时间
                    approvalProcess.setApproveStatus("3");//驳回
                    approvalProcess.setReason(reason);
                    approvalProcessDao.update(approvalProcess);

                    oldDetail.setModityStatus("1");
                    accountService.updateAccountDetail(oldDetail);

                    if (oldDetail.getDetailId() != null) {
                        AccountDetail lingOldDetail = accountService.getAccountDetailById(oldDetail.getDetailId());
                        lingOldDetail.setModityStatus("1");//未冲账
                        accountService.updateAccountDetail(lingOldDetail);
                    }
                    map.put("state", 1);//驳回成功
                    map.put("content", "操作成功");
                }
                this.publicPushInterface(map,approvalProcess,state);  //审批成功后公共的推送接口

            }else {
                map.put("state", 3);
                map.put("content", "已审批，不可重复审批");
            }
        }else {
            map.put("state", 0);//操作失败
            map.put("content", "操作失败");//操作失败
        }
        return map;
    }

    //审批成功后公共的推送接口
    private void publicPushInterface(Map<String,Object> map,ApprovalProcess approvalProcess,Integer state){
        Integer status = (Integer) map.get("state");
        if (1==status){  //操作成功（批准/驳回）
            //给财务修改-申请人的待处理发送
            this.updateRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/updateApplyHandle", null, null, "financeModify");

            //给财务审批-审批人的待处理发送
            this.updateRejectSend(-1, -1, approvalProcess, approvalProcess.getToUser(), "/updateApprovalHandle", null, null, "financeModifyApproval");

            if (state == 1) {  //批准
                // 批准给申请人发消息
                userSuspendMsgService.saveUserSuspendMsg(1, "您提交的财务修改申请已经被批准了！", "审批时间 " +"董事长 "+ NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"), approvalProcess.getFromUser(), "financeUpdateDetail",approvalProcess.getId());  //给前端要查看详情的链接
            } else if (state==0){  //驳回
                // 驳回给申请人发消息
                userSuspendMsgService.saveUserSuspendMsg(1, " 您提交的财务修改申请被驳回了！", "审批时间 " +"董事长 "+ NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"), approvalProcess.getFromUser(), "financeUpdateDetail",approvalProcess.getId());  //给前端要查看详情的链接
            }
        }
    }
}
