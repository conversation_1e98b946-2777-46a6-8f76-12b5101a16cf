package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtSupplierMaterialDao;
import cn.sphd.miners.modules.material.entity.MtSupplierMaterial;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/9/19.
 */
@Repository
public class MtSupplierMaterialDaoImpl extends BaseDao<MtSupplierMaterial,Serializable> implements MtSupplierMaterialDao {
    @Override
    public List<MtSupplierMaterial> getMtSupplierMaterialByNameAndCodeName(Integer oid, String codeName, String name) {
        String hql = "from MtSupplierMaterial o ";
        if(oid!=null)
            hql+=" where o.supplier.oid="+oid;
        if(codeName!=null)
            hql+=" AND o.codeName= '"+codeName+"'";
        if(name!=null)
            hql+=" AND o.supplier.name= '"+name+"'";
        return getListByHQL(hql);
    }

    @Override
    public MtSupplierMaterial getMtSupplierMaterialByMIdANDSID(int mid, int sid) {
        return getByHQL(" from MtSupplierMaterial o where o.material.id = ?0 AND o.supplier.id = ?1", mid, sid);
    }

    @Override
    public Map getLastPurchasedData(Integer oid) {
        String sql = "select mb.org,msm.create_date,msm.create_name from t_mt_supplier_material msm left join t_mt_base mb on msm.material = mb.id where msm.create_date is not null and mb.org=?0 order by msm.create_date desc limit 0,1";
        return findMapByConditionNoPage(sql,new Object[]{oid});
    }

    @Override
    public List<MtSupplierMaterial> getBySupplierId(Integer supplierId) {
        String hql = " from MtSupplierMaterial o where o.supplier=?0";
        return getListByHQL(hql,supplierId);
    }

    @Override
    public MtSupplierMaterial getById(Integer supplierMaterial) {
        return getByHQL(" from MtSupplierMaterial o where id=?0",supplierMaterial);
    }
}
