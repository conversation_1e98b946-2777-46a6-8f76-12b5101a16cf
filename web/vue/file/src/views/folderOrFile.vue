<template>
  <div class="home">
    <loading v-show="isLoad" />
    <div class="logo">
      <span>Endress + Hauser</span>
      <img src="../assets/logo.png" class="logoimg" />
    </div>
    <div v-if="search == 1">
      <div class="search-wrapper">
        <input
          placeholder="搜索常见问题"
          type="text"
          placeholder-class="search-placeholder"
          class="search-input"
          v-model="searchtxt"
        />
        <img src="../assets/search.png" class="search" />
        <span class="searchbtn" @click="searchFileBtn">搜索</span>
      </div>
    </div>
    <div v-else class="pfName">{{ pfName }}</div>
    <!-- folders -->
    <div class="conta" v-for="item in list" :key="item">
      <div class="folerItem" @click="getChild(item)">
        <i class="icon-folder"></i>
        <span class="ti">{{ item.name }}</span>
        <van-icon name="arrow" class="vicon" color="rgb(123 180 237)" />
      </div>
    </div>
    <!-- files -->
    <div class="conta" v-for="item in list2" :key="item">
      <div class="out" @click="goDetails(item)">
        <div class="kd">
          <van-icon name="arrow" class="vicon" color="rgb(123 180 237)" />
          <span class="ti">{{ item.name }}</span>
          <span v-if='item.isFee' class='fr'>{{ item.fee }}元</span>
          <span v-if='item.exp != null && item.exp <= dayjs().add(100, "year").valueOf()' class='fr'>有效期至：{{ dayjs(item.exp).format('YYYY年MM月DD日 HH:mm:ss') }}</span>
          <span v-else-if='item.exp != null && item.exp > dayjs().add(100, "year").valueOf()' class='fr'>有效期无限</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.conta {
  padding: 0 16px;
}

.search-wrapper {
  height: 40px;
  padding: 6px 15px;
  position: relative;
  margin-bottom: 15px;
  .search-input {
    width: 100%;
    height: 100%;
    padding-left: 40px;
    font-size: 14px;
    color: #666;
    box-sizing: border-box;
    border: 1px solid #ccc;
  }
  .search {
    position: absolute;
    width: 20px;
    height: 20px;
    left: 24px;
    top: 17px;
  }
  .search-placeholder {
    font-size: 14px;
    // font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #999999;
  }
  .searchbtn {
    position: absolute;
    top: 6px;
    right: 15px;
    background-color: #63a8c9;
    color: #fff;
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
  }
}

.folerItem {
  color: #333;
  padding: 10px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
  position: relative;
  .icon-folder {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url("../assets/folder.png") no-repeat;
    background-size: 100%;
    position: relative;
    top: 5px;
    margin-right: 16px;
  }
}
.logo {
  text-align: right;
  height: 66px;
  line-height: 66px;
  padding: 0 20px;
  span {
    font-size: 24px;
  }
  .logoimg {
    display: inline-block;
    width: 44px;
  }
}
.vicon {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 16px;
  height: 16px;
  display: inline-block;
  // background: url("../assets/arrow.png");
  // background-size: 100%;
  // transform: rotate(180deg);
}
.out {
  border-left: 5px solid #61aad9;
  margin-top: 10px;
  box-shadow: 0 2px 2px #eee;
  .kd {
    border: 1px solid #eee;
    text-align: left;
    padding: 16px;
    position: relative;
    .ti {
      font-size: 14px;
    }
    .fr{
      float: right;
      margin-right: 1.5em;
    }
  }
}
</style>

<script>
import dayjs from 'dayjs'
import JSONBig from 'json-bigint'
import { getWxParentAndChildDir, getSearchFile, payResult } from "@/api/api"
import loading from "@/components/loading.vue"

export default {
  data() {
    return {
      dayjs: dayjs,
      pfName: "",
      search: "",
      isLoad: true,
      list: [],
      list2: [],
    };
  },
  components: {
    loading,
  },
  mounted() {
    this.sentPayResult(this.$route?.params??JSON.parse(localStorage.getItem('webViewParams')))
    let params = localStorage.getItem('param')//获取存储的元素
    console.log("folderOrFile params",params)
    params = JSONBig.parse(params)
    // let id = this.$route.params.id ;  // 文件夹ID
    // this.pfName = this.$route.params.p; // 文件夹名字 或者 搜索的名字
    // this.search = this.$route.params.s; // 是否搜索

    let id = params.id  // 文件夹ID
    this.pfName = params.p // 文件夹名字 或者 搜索的名字
    this.search = params.s // 是否搜索
    console.log('this', this)
    console.log('this.$route', this.$route)
    console.log('page 2 ', id,  this.pfName, this.search )
    if (this.search == 1) {
      this.searchtxt = this.pfName;
      // this.searchFile(id);
      //this.getList(id);
      this.searchFile();
    } else {
      this.getList(id);
    }
  },
  methods: {
    sentPayResult(params) {
      if(params) {
        console.log('sentPayResult', params)
        if(params.orderId && params.payRes) {
          payResult(params.orderId, params.payRes)
        }
      }
    },
    getChild(item) {
      let id = item.id;
      this.getList(id);
    },
    goDetails(item) {
      //进行点击事件效果
      if(item.skuId && (item.exp == null || item.exp <= new Date().getTime())) {
        localStorage.setItem('createOrder', JSONBig.stringify({skuIds:[item.skuId]}))
        this.$router.push({name: "createOrder", params: {skuIds:[item.skuId]}})
      } else {
        let params = {id: item.id}
        localStorage.setItem('param2', JSONBig.stringify(params))
        this.$router.push({name: "details", params: params});
      }
    },
    getList(id) {
      this.isLoad = true;
      getWxParentAndChildDir(id).then((dd) => {
        let res = dd.data
        console.log(res);
        this.isLoad = false;
        let childFolder = res.childFolder;
        let filelist = res.list;
        if (childFolder && childFolder.length > 0) {
          this.list = childFolder;
          this.list2 = []; //list是文件夹，list2为文件夹里的内容
          console.log('list', childFolder)
        } else if (filelist) {
          console.log(filelist);
          this.list = [];
          this.list2 = filelist || [];
          console.log('list2', this.list2 )
        }
      });
    },
    searchFileBtn() {
      this.isLoad = true;
      getSearchFile(this.searchtxt).then((res) => {
        console.log(res);
        this.isLoad = false;
        let sechlist = res.list || [];
        this.list2 = sechlist;
        this.list = [];
      });
    },
    searchFile() {
      this.isLoad = true;
      getSearchFile(this.searchtxt).then((res) => {
        this.isLoad = false;

        console.log(res);
        let sechlist = res.list || [];
        this.list2 = sechlist;
        this.list = [];
      });
    },
  },
};
</script>