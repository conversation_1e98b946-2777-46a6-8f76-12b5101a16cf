
.gapTp{margin-top: 30px;}
.sm-ttl{display: inline-block;width: 100px;}
.sm-con{display: inline-block;width: 130px;}
.rws-con{display: inline-block;width: 250px;}
.des-box{margin-left: 130px;min-height: 30px; padding: 4px 8px;border: 1px solid #dcdfe6;background: #fff;}
.back-btn {  margin-bottom: 20px;  }
.mainCon{display: none;}
.mainCon .ty-alert{justify-content: space-between;color: #101010;}
.narrowBody{margin: auto;width: 88%;}
.changeDot .fa-dot-circle-o{color:#48cfad ;}
.changeDot{padding: 8px 10px;}
.changeDot i{margin-right: 10px;}
.dotItem .changeDot{float: left; width:150px; }
.xing:before {  content: "*";  width: 5px;  height: 5px;  display: inline-block;color: red;  font-weight: bolder;  position: relative;  }
.priceForm input{width: 100%;}
.origin{margin-top: 10px;}
.origin select,.origin input{width: 124px;}
.origin select[disabled]{ background: #d9d9d9 !important;  }
.gapRt{display: inline-block;width: 198px;}
.gapRt0{display: inline-block;width: 270px;}
.gapLn{margin-top: 20px;padding-top: 20px;border-top: 1px solid #CCCCCC;}
.flexBox{width: 100%;padding: 8px 0;box-sizing: border-box;border-radius: 2px;position: relative;font-size: 13px;overflow: hidden;opacity: 1;display: flex;align-items: center;
    justify-content: space-between;transition: opacity .2s;margin-bottom: 8px;}
.def-btn {color: #0070bf;padding: 0 5px;}
.del-btn {color: red;padding: 0 5px;}
.withBold{font-weight: bold;}
.price-box{margin-left: 100px;margin-top: 10px;}
.delSite{ position: relative;}
.price-box .del-btn{top: 5px;right: -34px;position: absolute;font-weight: bold;}
.price-box select,.price-box input{width: 140px;}
.parts,.priceTp{margin-top: 16px;}
.price-box input.middleSize{width: 374px;}
.midPay{margin-top: 10px;}
.dotItem{margin-top: 12px;}
.elemFlex{  display: flex;justify-content: space-between;align-items: center;padding-bottom: 10px;}
.scan-wrap .elemFlex{padding-top: 26px;}
#seeServiceNoCycle hr, #seeServiceNoCycle hr{border-top: 1px solid #c6c3c3;}
#seeServiceNoCycle .manyTime > div{margin-bottom: 12px;}
.noByCycleSet{margin-top: 20px;}
.bonceContainer .bonceCon input:disabled{ background: #ececec}
.posLeft td:not(.posCenter) {text-align: left;}
.trialSect {padding-top: 18px;}
.trialSect .typeDot {float: left;width: 280px;}
.trialSect .chargeDot,.trialSect .acceptDot {  float: left;width: 50px;margin-right: 34px;}
.scanTb .ty-table td{text-align: left;}
.careTip{color: #0070bf;font-size: 12px;}
.cateChoose{margin-top: 10px;}
.cateChoose > div{padding-bottom: 16px;}
.cateChoose > div span {margin-right: 10px;}
.marNoCircle {display: none;}
.operations {float: right;padding: 16px 0;}
.oprationName{margin: 0 10px;display: inline-block;min-width: 60px;}
.oper{display: inline-block;width: 100px;text-align:right;}
.gapR{margin-right: 10px;}
.wrapWidth{width: 90%;margin: auto;}
.copyIcon{padding: 20px 0;margin: 10px 0;border-top: 1px solid #ccc;border-bottom: 1px solid #ccc;}
.ty-table td.pos-left{text-align: left;}
.repeatTip1,.repeatTip2{display: none;}
.inv_ttl1,.inv_ttl2{padding: 10px 0 22px 0;margin-bottom: 16px;border-bottom: 1px solid #ccc;}
.ty-tip-blue{color: #5d9cec;}
.ty-alert .btn-group{flex: auto; text-align: right}
.ty-alert .btn-group>span{margin-left: 8px}
#addService .kj-table{
    margin-top: 8px;
}
.bounceMainCon .kj-hr{
    border-color: #d4dee6;
    margin: 12px 0;
}
.bounceMainCon{
    width: 90%;
    margin: 8px auto;
}
section.menu_content{
    margin-top: 8px;
}
.item-flex{
    display: flex;
    margin-bottom: 8px;
    align-items: baseline;
}
.item-flex .item-title{
    flex: none;
}
.item60{
    width: 60px;
}
.item120{
    width: 120px;
}
.item180{
    width: 180px;
}
.item240{
    width: 240px;
}
.question{
    margin: 18px 0;
    font-size: 13px;
}
#addGoods1 .ty-radio{
    width: 180px;
}




