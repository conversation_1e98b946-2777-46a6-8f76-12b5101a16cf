<template>
  <div class="audioCon">
    <div class='daudio'>
      <el-icon class="closeLyc" @click="closeLyc">
        <close-bold />
      </el-icon>
      <div class="videoCon">
        <video src="" class="videoB"></video>
      </div>
      <div class="AudioCon">
        <AudioC :fileUrl="fileUrl" ref="Lyc" :playName="playName" @audioPlayLyc="audioPlayLyc" @endPlayLyc="endPlayLyc"></AudioC>
      </div>
      <div>
        <div class="kuangCon" ref="kuangCon">
          <div class="curLycTxt">
            <div>
              <p class="curTxt">{{ curTxt }}</p>
            </div>
          </div>
          <div v-if="false" class="alltxtCon" ref="alltxtCon" :style="{ top: topStr  }">
            <div ref="alreadTxt">
              <div v-for=" (item , index) in songPatArr" :key="index">
                <p class="alreadTxt" v-if="item.state === 2">{{ item.txt }}</p>
              </div>
            </div>
            <div ref="noAlreadTxt">
              <div v-for=" (item2 , index2) in songPatArr" :key="index2">
                <p class="grayTxt" v-if="item2.state === 0">{{ item2.txt }}</p>
                <p class="activeTxt" v-if="item2.state === 1">{{ item2.txt }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import AudioC from '@/components/AudioC.vue'
export default {
  name: "LyricPlayer",
  data() {
    return {
      curTxt:'',
      songPatArr: [],
      topStr:0,
      timer:0,
    };
  },
  components: {
    AudioC
  },
  props: {
    fileUrl: {
      type: String,
      default: ''
    },
    playName: {
      type: String,
      default: ''
    },
    lyricTxt: {
      type: String,
      default: ''
    },
  },
  mounted() {
    console.log('fileUrl 播放什么？', this.fileUrl)
    if(this.lyricTxt.indexOf('WEBVTT') > -1){
      let alltxt = this.lyricTxt.split('WEBVTT\n\n')[1]
      let LycArr = alltxt.split('\n\n')
      let songTatArr = []
      LycArr.forEach(item => {
        let lyArr = item.split('\n');
        let txt = lyArr[1] || ''
        let timeArr = lyArr[0].split(' --> ')
        let beginTime = timeArr[0]
        let endTime = timeArr[1]
        songTatArr.push({ beginTime:beginTime, endTime:endTime, txt:txt, state:0 })
      })
      this.songPatArr = songTatArr
      console.log('this.songPatArr ==', this.songPatArr )
    }else{
      this.songPatArr = []
    }

    this.startPat()
  },
  methods: {
    audioPlayLyc(status){
      let that = this
      if(status){
        let timer = localStorage.getItem('timer')
        clearInterval(timer)
        console.log('新建 setInterval')
        timer = setInterval(function () {
          that.watchFun()
          let Lyc = that.$refs.Lyc
          Lyc.playLiveTime()
        } , 200)
        localStorage.setItem('timer', timer)
      }else {
        let timer = localStorage.getItem('timer')
        console.log('取消 clearInterval', timer)
        clearInterval(timer)
      }
    },
    endPlayLyc(){
      console.log('父级 endPlayLyc ', )
      let timer = localStorage.getItem('timer')
      console.log('取消 clearInterval(t', timer)
      clearInterval(timer)
    },
    closeLyc(){
      console.log('去调用父级 closePlayLyc ')
      this.$emit("closePlayLyc", false)
    },
    startPat(){
      let that = this
      let Lyc = this.$refs.Lyc
      // console.log('Lyc=', Lyc)
      Lyc.audioPlay(true);
      let timer = localStorage.getItem('timer')
      if(timer && timer > 0){
        clearInterval(timer)
      }
      timer = setInterval(function () {
        that.watchFun()
      } , 200)
      localStorage.setItem('timer', timer)

    },
    watchFun(){
      let Lyc = this.$refs.Lyc
      if(Lyc && Lyc.getCurrent){
        let cur = Lyc.getCurrent(true) ;
        let that = this
        this.songPatArr.forEach( item => {
          let begin = that.changeToss(item.beginTime)
          let end = that.changeToss(item.endTime)
          if(cur < begin){
            item.state = 0
          }else if(cur > end){
            item.state = 2

          }else{
            item.state = 1
            if(that.curTxt === item.txt){
            }else{
              that.curTxt = item.txt
              Lyc.setLyricLiveFun(item.txt)
            }

          }
        })
      }

    },
    changeToss(begin){ // 12:12:12.123
      if(begin){
        let beginTime = 0
        let beginArr = begin.split(':')
        if(beginArr.length === 3){
          beginTime = Number(beginArr[0]) * 3600 + Number(beginArr[1]) * 60 + Number(beginArr[2])
        }else{
          beginTime = Number(beginArr[0]) * 60 + Number(beginArr[1])
        }
        return beginTime
      }else{
        return ''
      }

    },

  },
  destroyed(){
    let timer = localStorage.getItem('timer')
    clearInterval(timer)
  }
};
</script>


<style scoped lang="scss">
.audioCon{
  width: 100%;
  height: 100%;
  //background: rgba(100,100,100,0.9);
  top:0;
  left: 0;
  .closeLyc{
    position: absolute;
    top: -30px;
    color: #fff;
    right: 40px;
    font-size: 24px;
  }
  .daudio{
    position: relative;
    .videoCon{
      width: 400px;
      height: 250px;
      margin:0 auto;
      background: #ddd;
      .videoB{
        width: 100%;
        height: 100%;
      }
    }
  }
  .AudioCon{
    width: 400px;
    margin:0 auto ;
  }


}

.kuangCon{
  overflow: auto;
  position: absolute;
  border-radius: 4px;
  width: 100%;
  bottom: 20px;

  .curLycTxt{
    color: #0b94ea;
    font-size: 20px;
  }



  .alltxtCon{
    position: absolute;
    color:#fff;
    text-align: center;
    line-height: 40px;
    width: 84%;
  }
  .activeTxt{
    color:#F6BA04 ;
  }
  .grayTxt{
    color:#CCCCCC ;
  }
  .alreadTxt{
    color:#CCCCCC ;
  }
  .curTxt{
    color: #0b94ea;
    text-align: center;
  }
}

</style>

