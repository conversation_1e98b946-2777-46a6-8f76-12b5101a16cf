package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingExamKey;
import cn.sphd.miners.modules.trainManage.entity.TrainingExamQuestion;
import cn.sphd.miners.modules.trainManage.entity.TrainingExamUserAnswer;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestion;

import java.util.List;

/**
 * @ClassName RespExamQuestion
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/2 16:52
 * @Version 1.0
 */
public class RespExamQuestion {
    private TrainingExamQuestion trainingExamQuestion;
    private TrainingQuestion trainingQuestion;
    private List<TrainingExamKey> trainingExamKeyList;
    private TrainingExamUserAnswer trainingExamUserAnswer;

    public TrainingExamUserAnswer getTrainingExamUserAnswer() {
        return trainingExamUserAnswer;
    }

    public void setTrainingExamUserAnswer(TrainingExamUserAnswer trainingExamUserAnswer) {
        this.trainingExamUserAnswer = trainingExamUserAnswer;
    }

    public TrainingQuestion getTrainingQuestion() {
        return trainingQuestion;
    }

    public void setTrainingQuestion(TrainingQuestion trainingQuestion) {
        this.trainingQuestion = trainingQuestion;
    }

    public TrainingExamQuestion getTrainingExamQuestion() {
        return trainingExamQuestion;
    }

    public void setTrainingExamQuestion(TrainingExamQuestion trainingExamQuestion) {
        this.trainingExamQuestion = trainingExamQuestion;
    }

    public List<TrainingExamKey> getTrainingExamKeyList() {
        return trainingExamKeyList;
    }

    public void setTrainingExamKeyList(List<TrainingExamKey> trainingExamKeyList) {
        this.trainingExamKeyList = trainingExamKeyList;
    }
}
