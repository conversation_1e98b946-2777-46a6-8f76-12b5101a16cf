package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.commodity.entity.PdAssemble;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.service.PartsService;
import cn.sphd.miners.modules.commodity.service.ProService;
import cn.sphd.miners.modules.commodity.service.impl.ConstituteUtil;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 零组件档案
 */
@Controller
@RequestMapping("/parts")
public class PartsController {

    @Autowired
    private PartsService partsService;
    @Autowired
    private ProService proService;





    @RequestMapping("/partsIndex.do")
    public String partsIndex() {
        return "/technology/partsCenter";
    }

    /**
     * 获取零组件列表
     */
    @ResponseBody
    @RequestMapping("/getPartsList.do")
    public Map<String, Object> getPartsList(User user,Integer currPage,Integer pageSize, String category, String param,String process,String composition) {
        Integer oid = user.getOid();
        Map<String,Object> map = partsService.getPartsList(oid, category, process,composition,"1",param , currPage, pageSize);
        Map<String, Object> type = new HashMap<>();
        type.put("id", 0);
        type.put("value", "待分类");
        Integer num = 0;
        if (map.get("totalRows") != null) {
            num = (Integer) map.get("totalRows");
        }
        type.put("num", num);
        List list = new ArrayList();
        list.add(type);
        Map<String, Object> stopList = proService.suspendPdBaseList(oid,null,null, "","2", currPage,pageSize);
        Integer suspendNum = 0;
        if (stopList.get("totalRows") != null) {
            suspendNum = (Integer) stopList.get("totalRows");
        }
        map.put("currPage", currPage);
        map.put("countNum",num);
        map.put("suspendNum", suspendNum);
        map.put("mtCategories", list);
        map.put("code", 200);
        return map;
    }

    /**
     * 暂存于系统的零组件列表接口
     */
    @ResponseBody
    @RequestMapping("/getPartsListForTemporary.do")
    public Map<String, Object>  getPartsListForTemporary(User user,Integer currPage,Integer pageSize, String param, String source, String composition) {
        Integer oid = user.getOid();
        Map map = partsService.getPartsListForTemporary(oid, source,composition,param,currPage,pageSize);
        String code= (String) map.get("code");
        if("400".equals(code)){
            map.put("currPage",currPage);
            map.put("totalPage",0);
            map.put("totalRows",0);
        }
        return map;
    }

    /**
     * 暂停零组件列表
     */
    @ResponseBody
    @RequestMapping("/suspendPartsList.do")
    public Map<String, Object> suspendPartsList(User user,String source, String composition, String param, Integer currPage, Integer pageSize) {
        Integer oid = user.getOid();

        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        Map<String,Object> map = partsService.getPartsList(oid, null, source,composition,"0",param , currPage, pageSize);
        map.put("currPage", currPage);

        Integer suspendNum = 0;
        if (map.get("totalRows") != null) {
            suspendNum = (Integer) map.get("totalRows");
        }

        List<Map<String, Object>> countList = proService.getPdBaseList(oid);
        map.put("countNum", countList.size());
        map.put("suspendNum", suspendNum);
        return map;
    }

    /**
     *零组件查看
     */
    @ResponseBody
    @RequestMapping("/getPartsDetail.do")
    public JsonResult getPartsDetail(User user,Integer id) {

        Map<String,Object>map=new HashMap<>();
        PdBase pdBase=proService.getPdBaseOne(id);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);

        List<Map<String,Object>> list=proService.getProductListByPdBase(id);
        if("系统".equals(pdBase.getCreateName())){
            pdBase.setAddType("1");
        }else{
            if(list.size()==1){
                Map<String,Object> m=list.get(0);
                if("系统".equals(m.get("createName").toString())){
                    pdBase.setAddType("2");
                }
            }
        }
        if(pdBase.getUpdateDate()==null){
            pdBase.setUpdateName(pdBase.getCreateName());
            pdBase.setUpdateDate(pdBase.getUpdateDate());
        }

        PdAssemble pdAssemble=partsService.getPdAssemble(id);
        map.put("part",pdBase);
        map.put("pdAssemble",pdAssemble);
        return new JsonResult(1,map);
    }

    /**
     *直接含有本件的产品列表
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getProductListForZhiJie.do")
    public JsonResult getProductListForZhiJie(User user,Integer id) {


        Map<String,Object>map=new HashMap<>();
        PdBase pdBase=proService.getPdBaseOne(id);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);
        pdBase.setJson(null);

        List<Map<String, Object>> list = partsService.getProductListForZhiJie(id,"1");
        for (Map<String, Object> m:list) {
            JSONArray jsonArray=JSONArray.parseArray((String) m.get("json"));
            JSONObject obj =ConstituteUtil.getNode(jsonArray,"id", id);
            m.put("level",obj.get("level"));
            m.put("amount",obj.get("amount"));
            m.put("date",obj.get("date"));
            m.put("gcName",obj.get("gcName"));
        }

        map.put("list",list);
        map.put("part",pdBase);

        return new JsonResult(1,map);
    }


    /**
     *间接含有有本件的产品列表
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getProductListForJanJie.do")
    public JsonResult getProductListForJanJie(User user,Integer id) {

        Map<String,Object>map=new HashMap<>();
        PdBase pdBase=proService.getPdBaseOne(id);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);
       // pdBase.setJson(null);

        List<Map<String, Object>> list = partsService.getProductListForJianJie(id,"1");
        for (Map<String, Object> m:list) {
            JSONArray jsonArray=JSONArray.parseArray((String) m.get("json"));
            JSONObject obj= ConstituteUtil.getNode(jsonArray,"id", id);
            m.put("level",obj.get("level"));
            m.put("amount",obj.get("amount"));
            m.put("date",obj.get("date"));
            m.put("gcName",obj.get("gcName"));
        }
        map.put("part",pdBase);
        map.put("list",list);
        return new JsonResult(1,map);
    }


    /**
     *直接含有有本件的组件
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getPartsListForZhiJie.do")
    public JsonResult getPartsListForZhiJie(User user,Integer id) {

        Map<String,Object>map=new HashMap<>();
        PdBase pdBase=proService.getPdBaseOne(id);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);

        List<Map<String, Object>> list = partsService.getProductListForZhiJie(id,"2");
        for (Map<String, Object> m:list) {
            JSONArray jsonArray=JSONArray.parseArray((String) m.get("json"));
            JSONObject obj =ConstituteUtil.getNode(jsonArray,"id", id);
            m.put("level",obj.get("level"));
            m.put("amount",obj.get("amount"));
            m.put("date",obj.get("date"));
            m.put("gcName",obj.get("gcName"));
        }

        map.put("list",list);
        map.put("part",pdBase);
        return new JsonResult(1,map);
    }

    /**
     *曾直接含有有本件的产品列表
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getProductListForBeforeZhiJie.do")
    public JsonResult getProductListForBeforeZhiJie(User user,Integer id) {

        Map<String,Object>map=new HashMap<>();
        PdBase pdBase=proService.getPdBaseOne(id);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);
        List<Map<String, Object>> list = partsService.getProductListForBeforeZhiJie(id,"1");
        map.put("part",pdBase);
        map.put("list",getList(list,id));
        return new JsonResult(1,map);
    }

    /**
     *曾间接含有有本件的产品列表
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getProductListForBeforeJianjie.do")
    public JsonResult getProductListForBeforeJianjie(User user,Integer id) {
        Map<String,Object>map=new HashMap<>();
        PdBase pdBase=proService.getPdBaseOne(id);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);
        List<Map<String, Object>> list = partsService.getProductListForBeforeJianjie(id,"1");
        map.put("part",pdBase);
        map.put("list",getList(list,id));
        return new JsonResult(1,map);
    }

    /**
     *曾直接含有有本件的组件
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getPartsListForBeforeZhiJie.do")
    public JsonResult getPartsListForBeforeZhiJie(User user,Integer id) {
        Map<String,Object>map=new HashMap<>();
        PdBase pdBase=proService.getPdBaseOne(id);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);
        List<Map<String, Object>> list = partsService.getProductListForBeforeZhiJie(id,"2");
        map.put("part",pdBase);
        map.put("list",getList(list,id));
        return new JsonResult(1,map);
    }

    /**
     *查看当前全部的零组件
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getAllPartsByParts.do")
    public JsonResult getAllPartsByParts(User user,Integer id,Integer level) {
        List<Map<String, Object>> list = partsService.getAllPartsByParts(id,level);

        return new JsonResult(1,list);
    }

    /**
     *查看曾全部的零组件
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getAllPartsByBeforeParts.do")
    public JsonResult getAllPartsByBeforeParts(User user,Integer id) {
        List<Map<String,Object>> list = partsService.getAllPartsByBeforeParts(id);
        return new JsonResult(1,list);
    }

    /**
     *查看材料列表
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getMaterialList.do")
    public JsonResult getMaterialList(User user,Integer id) {
        List<Map<String, Object>> list = partsService.getMaterialList(id);
        return new JsonResult(1,list);
    }

    /**
     *查看配方列表
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getFormulaList.do")
    public JsonResult getFormulaList(User user,Integer id) {
        List<Map<String, Object>> list = partsService.getFormulaList(id);
        return new JsonResult(1,list);
    }
    /**
     *获取零组件设置
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/getViewSetting.do")
    public JsonResult getViewSetting(User user) {
        Map<String,Object> map=partsService.getPdViewSetting(user);
        return new JsonResult(1,map);
    }

    /**
     *零组件设置确认
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/setViewSetting.do")
    public JsonResult setViewSetting(User user,String product,String component,String frame) {
        partsService.setPdViewSetting(user,product,component,frame);
        return new JsonResult(1,null);
    }


    /**
     *构成修改
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/updateComposition.do")
    public JsonResult updateComposition(User user,Integer id,String composition,String process ) {
        partsService.updateComposition(user,id,composition,process);
        return new JsonResult(1,null);
    }

    /**
     *零组件基础信息修改
     * id:零组件id
     */
    @ResponseBody
    @RequestMapping("/updatePartsBase.do")
    public JsonResult updatePartsBase(User user,PdBase pdBase ) {
        String res=partsService.updatePartsBase(user,pdBase);
        if("success".equals(res)){
            return new JsonResult(1,null);
        }else{
            return new JsonResult(0,"","400",res);

        }

    }

    /*
     **
     *<AUTHOR>
     *获取基本信息修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getRecordBaseList.do")
    public JsonResult getRecordBaseList( Integer id) {

        List<Map<String, Object>> list = proService.getRecordBaseList(id);

        Map<String, Object> map = this.getRecord(list);

        return new JsonResult(1,map);
    }

    /*
     **
     *<AUTHOR>
     * 获取基本信息修改记录详情
     */
    @ResponseBody
    @RequestMapping("/getRecordBaseDetails.do")
    public Map<String, Object> getRecordBaseDetails( Integer id, Integer front) {
        Map<String, Object> map = new HashMap<String, Object>();

        Map<String, Object> data = proService.getRecordBaseDetails(id, front);
        map.put("status", 1);
        map.put("data", data);
        return map;
    }

    /*
     **
     *<AUTHOR>
     *获取构成修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getCompositionRecordBaseList.do")
    public JsonResult getCompositionRecordBaseList( Integer id) {

        List<Map<String, Object>> list = partsService.getCompositionRecordBaseList(id);

        Map<String, Object> map = this.getRecord(list);

        return new JsonResult(1,map);
    }
    /*
     **
     *<AUTHOR>
     * 获取构成修改记录详情
     */
    @ResponseBody
    @RequestMapping("/getCompositionRecordBaseDetails.do")
    public Map<String, Object> getCompositionRecordBaseDetails( Integer id, Integer front) {
        Map<String, Object> map = new HashMap<String, Object>();

        Map<String, Object> data = partsService.getCompositionRecordBaseDetails(id, front);
        map.put("status", 1);
        map.put("data", data);
        return map;
    }

    /**
     * 筛选出曾经的列表
     */
    private List getList(List<Map<String, Object>> list,Integer id){
        List<Map<String, Object>> result=new ArrayList<>();
        List<JSONObject> haveList=new ArrayList<>();
        for (Map<String, Object> m:list) {
            JSONArray jsonArray=JSONArray.parseArray((String) m.get("json"));
            JSONObject obj =ConstituteUtil.getNode(jsonArray,"id", id);
            int flag=0;
            for (JSONObject o:haveList) {
                if(obj.getInteger("level").toString().equals(o.getInteger("level").toString())
                &&new BigDecimal(obj.get("amount")+"").compareTo(new BigDecimal(o.get("amount")+""))==0
                &&obj.getString("date").equals(o.getString("date"))
               && obj.getInteger("id").toString().equals(o.getInteger("id").toString())){
                    flag++;
                    haveList.add(o);
                    break;
                }
            }
            if(flag==0){

                m.put("level",obj.get("level"));
                m.put("amount",obj.get("amount"));
                m.put("date",obj.get("date"));
                m.put("gcName",obj.get("gcName"));
                result.add(m);
            }
        }
        return result;
    }

    private Map<String, Object> getRecord(List<Map<String, Object>> list) {
        Map map = new HashMap();
        if (list.size() == 1) {
            map.put("createName", list.get(0).get("createName"));
            map.put("createDate", list.get(0).get("createDate"));
            map.put("updateName", list.get(0).get("updateName"));
            map.put("updateDate", list.get(0).get("updateDate"));
            map.put("list", new ArrayList<>());
        } else if (list.size() > 1) {
            map.put("createName", list.get(list.size() - 1).get("createName"));
            map.put("createDate", list.get(list.size() - 1).get("createDate"));
            map.put("updateName", list.get(list.size() - 1).get("updateName"));
            map.put("updateDate", list.get(list.size() - 1).get("updateDate"));
            map.put("list", list);
        } else {
            map.put("createName", null);
            map.put("createDate", null);
            map.put("updateName", null);
            map.put("updateDate", null);
            map.put("list", list);
        }

        return map;
    }
}
