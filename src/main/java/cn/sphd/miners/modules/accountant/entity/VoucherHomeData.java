package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import java.io.Serializable;

/**
* 新凭证管理首页数据
* */
@Alias("VoucherHomeData")
public class VoucherHomeData implements Serializable {
    private static final long serialVersionUID =  1L;

    private String settleMonth;//结账月份

    private Integer billQuantity;//票据数量

    private Integer manualSelection;//手动选择的凭证数量

    private Integer osSelection;//系统选择的凭证数量

    private Integer accountantSelection;//会计选择的凭证数量

    private Integer billsNotRecorded;//尚未入会计帐的票据

    private Integer billsNotAccount;//已制作过凭证但选为“不予做账”的


    public String getSettleMonth() {
        return settleMonth;
    }

    public void setSettleMonth(String settleMonth) {
        this.settleMonth = settleMonth;
    }

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    public Integer getManualSelection() {
        return manualSelection;
    }

    public void setManualSelection(Integer manualSelection) {
        this.manualSelection = manualSelection;
    }

    public Integer getOsSelection() {
        return osSelection;
    }

    public void setOsSelection(Integer osSelection) {
        this.osSelection = osSelection;
    }

    public Integer getAccountantSelection() {
        return accountantSelection;
    }

    public void setAccountantSelection(Integer accountantSelection) {
        this.accountantSelection = accountantSelection;
    }

    public Integer getBillsNotRecorded() {
        return billsNotRecorded;
    }

    public void setBillsNotRecorded(Integer billsNotRecorded) {
        this.billsNotRecorded = billsNotRecorded;
    }

    public Integer getBillsNotAccount() {
        return billsNotAccount;
    }

    public void setBillsNotAccount(Integer billsNotAccount) {
        this.billsNotAccount = billsNotAccount;
    }
}
