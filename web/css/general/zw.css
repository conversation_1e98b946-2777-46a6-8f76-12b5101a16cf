*{ font-family: "微软雅黑" ;   }
td,th{ text-align:center!important;  }
.clr{ clear:both;  }
.hd{ display:none;   }
/*按钮*/
.hand{ cursor:pointer; }
.right{float:right; }
/*右面*/
div.contn{ padding:10px;  }
div.Con{ color:#333; position:relative;    }


.manageNav{ font-size:18px;font-weight:bold; padding:5px 0; color:#666;   }
.manageNav a{ color:#555; }
.staffCon{ position:relative; padding:20px;  }
.staffImage{position: absolute;  text-align: center;  padding: 30px;  right: 60px;  top: 80px; border: 1px dashed #ccc; width:300px ; overflow: hidden }
.staffImage>img{ display:inline-block; border:5px solid rgba(54, 198, 211,.05); width:130px; height:160px; background:#efefef; margin: 0 auto;outline: none}

.staffInfo{ padding:0 30px 30px 30px; min-height:625px   }
.stf_info_div{ padding:15px 5px; border-bottom:1px solid #ccc;       }
.stf_info_div>div:nth-child(1){   }
.stfinfo_ttl{ font-size:20px; float:left; color:#36C6D3;   }
.stfinfo_edit{ float:right; }
.stfimg_edit{ margin-top:30px;  }
.stfinfo_edit,.stfimg_edit{ font-size:12px;border:1px solid #36C6D3; border-radius:13px!important; color:#36C6D3;width:60px; line-height:26px; text-align:center; display:inline-block;  }
.stfinfo_edit:hover,.stfimg_edit:hover{color:#fff; background:#36C6D3;  cursor:pointer;    }
.stf_info_div>div:nth-child(2){ font-size:16px; margin-top:30px;  }
.stf_info_div>div:nth-child(2)>p>span:nth-child(1),.stf_info_div>div:nth-child(2)>p>span:nth-child(3){width:130px; display:inline-block;     }
.stf_info_div>div:nth-child(2)>p>span:nth-child(3){ margin-left:100px; }
.stf_info_div>div:nth-child(2)>p>span:nth-child(2),.stf_info_div>div:nth-child(2)>p>span:nth-child(4){ color:#888;     }
.stf_info_div>div:nth-child(2) th{ font-size:14px;  }
.stf_info_div>div:nth-child(2) td{ color:#666;font-size:14px; }

.editNav{font-size:18px;padding:5px 0; color:#666;margin:20px 0; border-bottom:2px solid #e4e4e4; padding:30px  0 20px;    }
.editDiv{ position:relative; z-index: 1000; }
.editDiv>div:nth-child(2){ position:relative; }
.editDiv>div:nth-child(2) select{ display:block; width:91.5%;   }
.editDiv>div:nth-child(2) input { display:block; width:90%;  }
.editDiv>div:nth-child(2) img{ position:absolute; left:90%; margin-top:-40px; }
.option_con{  background:#eaeaea; padding:15px; border:1px solid #ccc; font-size:12px;  width:500px;margin:-1px 0 0;   }
.option_con>div{ margin-top:15px;  }
.option_con>div>div>a.depatlevel{ border: none; font-weight:bold; color:inherit; display:inline-block; margin-right:20px; }
/*.option_con>div>div>span{cursor:pointer; border:1px solid #666; padding:2px 15px; display:inline-block; margin: 0 10px 10px 0; border-radius:4px!important; background:#fff;   }*/
.control{ float:right; width:250px; text-align:right;margin-top: 3px     }
.control>a{color:#36C6D3; cursor:pointer; margin-right:10px;  }
.departItem , .zhiItem { cursor: pointer; display: inline-block; padding: 3px 10px; margin:3px 10px; border: 1px solid #ccc;border-radius: 4px!important;  }
.departItem:hover, .levelItem .active , #zhiCon .active{ background: #8acfd3; color:#fff;  }
.editDepar{ text-align:center;     }
.upBtn{ cursor:pointer; padding:3px 15px; display:inline-block; margin:10px; border:1px solid #ccc;   }
.upBtn:hover{ background: #ccc;  }


#right_tbl tr td:nth-child(1){ text-align:right!important; padding-right:30px; }
#right_tbl tr td:nth-child(2){ text-align:left!important; padding-left:30px; }
#right_tbl tr td>div{ display: inline-block; margin-right: 50px;}

.mutiEdit>div{ text-align:center; }
.mutiEdit>div>span:nth-child(1){ width:80px; display:inline-block; text-align:right; padding-right:20px;  }

.editDiv3{ width: 30%; float:left; }
.editDiv2{ width: 50%; float:left; }
.editDiv2>div>input,.editDiv3>div>input,.editDiv3>div>select{ width:80%;   }


/*new*/
.staffinfo_nav{margin-top:50px}
.staffinfo_nav>ul>li{ background:#fff; color:#5A7391; line-height:50px; text-align:left; padding-left:50px; list-style:none; }
.staffinfo_nav>ul>li.stfNav_active{ border-left:2px solid #5B9BD1; color:#5B9BD1;  }
.stfNav_normal{ border-left:2px solid #fff; }
.deepBlue{color:#5A7391; font-weight:bolder;  }
.staffnew{ margin-top:-65px;   }


.new_litTTL{ background:#fafafa; border-bottom:1px solid #e8e8e8; line-height:50px;padding:0 0 0 30px;  }
.new_litTTL>a{  color:#a2a2a2; font-weight: bolder; }
.new_container{ padding:10px 15px;  }
.new_container>div:first-child{ font-size:28px; line-height:30px;        }
.newCon{ border:1px solid #e8e8e8; padding:10px;   }
.new_editNav{font-size:18px;padding:15px 0 20px; border-bottom:2px solid #e4e4e4; margin-bottom:40px; }
.new_editItem{ margin:10px 0 0 0; }
.new_editItem>span{ width:25%; min-width:100px; display:inline-block; text-align: right;padding-right:15px; vertical-align: top;  line-height:35px;}
.new_editItem input,.new_editItem select{ width:350px; line-height: 25px;    }
.new_editItem>select{   height:35px}
.new_editItem>input[type="file"]{ display:inline-block; }
.select-down{ padding:14px 12px;   display:inline-block;   }
.downpositin{ position:relative; left:-25px; float:left; top:1px;     }
.new_footer{ line-height: 80px; text-align:center; border:1px solid #e8e8e8; border-top:none;  }
.litBlue{ background:#32C5D2; color:#fff; }
.litBlue:hover{ background: #84B1DF; color:#666; }

.xing:after{ content:"*"; width:5px; height:5px; display:inline-block; color:red; font-weight:bolder; position:relative;    }

/*
!*  Bounce   *!
.bounce{display:none; background: rgba(100,100,100,0.5); width: 100%; height:1000px; position:absolute; z-index:9999;  }
.bonceContainer{ border:1px solid #ccc; box-shadow:0 0 5px #333; border-radius:4px; margin:200px auto 0;background:#fff;  }
.bonceHead{ border-bottom:1px solid #ccc;   }
.bonceFoot{ border-top  :1px solid #ccc; text-align:center;   }
.bonceHead,.bonceFoot{ padding:10px 15px; font-size:14px;  }
.bounce_close{ width:25px; height:25px; background: url("../img/close.png") no-repeat center; display:inline-block;float:right;  }

*/

.bounce1{  background:#fff; width:500px; margin:200px auto 0 ; height:300px;    }
.bonuceCon_depar div{ padding:10px ; text-align:center; }
.bonuceCon_depar div.controler_depar{ margin-top:60px;   }


.chooseDepart>div{  margin-top: 5px;  line-height: 28px;  }
.choosePosition>div{  margin-top: 5px;  line-height: 28px;  }

#editGang input{
 text-indent: 8px;
}
#editGang select{
 text-indent: 2px;
}
input[type="text"], select{ border: 1px solid #c2cad8; height:35px; line-height:35px; padding: 0 8px }
.form-control[disabled]{
 background-color: #f2f2f2;
 color: #aaa;
}
.laydate-icon {
 border: 1px solid #c2cad8;
 height: 35px;
}
.newCon{
 width: 800px;
 margin: auto;
}






























