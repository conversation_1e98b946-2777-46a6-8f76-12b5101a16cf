package cn.sphd.miners.modules.accountant.service.impl;


import cn.sphd.miners.modules.accountant.entity.TaccountantEstablishEntity;
import cn.sphd.miners.modules.accountant.mapper.TaccountantEstablishEntityMapper;
import cn.sphd.miners.modules.accountant.service.TaccountantEstablishService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly=false)
public class TaccountantEstablishServiceImpl implements TaccountantEstablishService {

    @Autowired
    TaccountantEstablishEntityMapper establishEntityMapper;
    @Autowired
    GeneralSubjectRecord generalSubjectRecord;
    @Autowired
    VoucherUpdate voucherUpdate;

    @Override
    public List<TaccountantEstablishEntity> getEstablishRecords(User user) {
        Integer eid = generalSubjectRecord.getEstablishByOrg(user.getOid());
        if (eid == null) {
            voucherUpdate.establishCompatible(user.getOid(),user.getUserID(),user.getUserName());
        }
        List<TaccountantEstablishEntity> list = establishEntityMapper.getEstablishRecords(user.getOid());
        return list;
    }
}
