<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TBalanceSheetMapper">


    <select id="getSingle" resultType="tBalanceSheet" parameterType="tBalanceSheet" >
        select
        id, org, period, type, begin_date, end_date, cell_code, balance, beginning_balance,
        memo, creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from
        t_accountant_balance_sheet
        where
        id = #{id}
    </select>

    <select id="listPage" resultType="tBalanceSheet" parameterType="hashmap" >
        select
        id, org, period, type, begin_date, end_date, cell_code, balance, beginning_balance,
        memo, creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from
        t_accountant_balance_sheet
    </select>


    <select id="listByTBalanceSheet" resultType="tBalanceSheet">
        select
        id, org, period, type, begin_date, end_date, cell_code AS cellCode, balance, beginning_balance AS beginningBalance,
        memo, creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from
        t_accountant_balance_sheet
        where
        org = #{org} AND period = #{period} AND begin_date = #{beginDate} AND end_date = #{endDate}
    </select>

    <select id="getTBalanceSheet" resultType="tBalanceSheet">
        select
        id, org, period, type, begin_date, end_date, cell_code AS cellCode, balance, beginning_balance AS beginningBalance,
        memo, creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from
        t_accountant_balance_sheet
        where
        org = #{org} AND period = #{period} AND cell_code = #{cellCode}
    </select>

    <insert id="insert" parameterType="tBalanceSheet" useGeneratedKeys="true" keyProperty="id" >
        insert into t_accountant_balance_sheet (org, period,
        type, begin_date, end_date, cell_code,
        balance, beginning_balance, memo,
        creator, create_name, create_date,
        updator, update_name, update_date,
        approve_Item, approve_status, approve_level,
        auditor, auditor_name, audit_date,
        operation, apply_memo, approve_memo,
        message_id,state)
        values (#{org}, #{period},
        #{type}, #{beginDate}, #{endDate}, #{cellCode},
        #{balance}, #{beginningBalance}, #{memo},
        #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate},
        #{approveItem}, #{approveStatus}, #{approveLevel},
        #{auditor}, #{auditorName}, #{auditDate},
        #{operation}, #{applyMemo}, #{approveMemo},
        #{messageId},#{state})
    </insert>

    <update id="update" parameterType="tBalanceSheet" >
        update t_accountant_balance_sheet
        <set >
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="period != null" >
                period = #{period},
            </if>
            <if test="type != null" >
                type = #{type},
            </if>
            <if test="beginDate != null" >
                begin_date = #{beginDate},
            </if>
            <if test="endDate != null" >
                end_date = #{endDate},
            </if>
            <if test="cellCode != null" >
                cell_code = #{cellCode},
            </if>
            <if test="balance != null" >
                balance = #{balance},
            </if>
            <if test="beginningBalance != null" >
                beginning_balance = #{beginningBalance},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="state != null" >
                state = #{state},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="tBalanceSheet" >
        delete from t_accountant_balance_sheet
        where id = #{id}
    </delete>

    <delete id="delSheet">
        delete from t_accountant_balance_sheet
        where org = #{org} AND period = #{period}
    </delete>

</mapper>