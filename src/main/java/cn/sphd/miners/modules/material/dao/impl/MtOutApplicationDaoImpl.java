package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtOutApplicationDao;
import cn.sphd.miners.modules.material.entity.MtOutApplication;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class MtOutApplicationDaoImpl extends BaseDao<MtOutApplication,Serializable> implements MtOutApplicationDao {
}
