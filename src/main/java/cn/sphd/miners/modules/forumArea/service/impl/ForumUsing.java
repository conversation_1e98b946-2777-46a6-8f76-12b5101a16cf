package cn.sphd.miners.modules.forumArea.service.impl;

import cn.sphd.miners.modules.forumArea.entity.*;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

/**
 * Created by 朱思旭 on 2021/3/11.
 */
public class ForumUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;


    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            ForumService service = ac.getBean(ForumService.class, "forumService");
            switch (entityClass) {
                case "ForumPostAttachment":
                    ForumPost forumPost;
                    ForumPostAttachment forumPostAttachment = service.getSingleByPostAtt(id);
                    if (forumPostAttachment != null && (forumPost = service.getSingleByForum(forumPostAttachment.getPost())) != null && forumPost.getIsCleaned()==1) {
                        return filename.equals(forumPostAttachment.getPath());
                    }
                case "ForumReplyAttachment":
                    ForumReplyAttachment forumReplyAttachment = service.getSingleByReplyAtt(id);
                    ForumReply forumReply;
                    if(forumReplyAttachment != null && (forumReply = service.oneForumReply(forumReplyAttachment.getReply())) != null && forumReply.getValid()==1 ) {
                        return  filename.equals(forumReplyAttachment.getPath());
                    }
                case "ForumReplyAttHis":
                    ForumReplyAttHis forumReplyAttHis = service.getSingleByReplyAttHis(id);
                    if (forumReplyAttHis != null) {
                        return  filename.equals(forumReplyAttHis.getPath());
                    }
            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }



    public ForumUsing(Integer id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }

    public ForumUsing(){

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }
}
