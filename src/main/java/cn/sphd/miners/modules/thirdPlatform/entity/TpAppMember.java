package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table ( name ="t_tp_app_member" )
public class TpAppMember implements Serializable {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    /**
     * 应用ID
     */
    @Column(name = "app", nullable=true , unique=false )
    private Long app;

    /**
     * 子机构ID
     */
    @Column(name = "sub_org", nullable=true , unique=false )
    private Long subOrg;

    /**
     * 第三方应用帐号
     */
    @Column(name = "app_acc", length=128, nullable=true , unique=false )
    private String appAcc;

    /**
     * 会员ID
     */
    @Column(name = "member", nullable=true , unique=false )
    private Long member;

    /**
     * 会员union_id
     */
    @Column(name = "union_id", length=128, nullable=true , unique=false )
    private String unionId;

    /**
     * 会员union_id
     */
    @Column(name = "open_id", length=128, nullable=true , unique=false )
    private String openId;

    /**
     * 头像
     */
    @Column(name = "avatar", length=255, nullable=true , unique=false )
    private String avatar;

    /**
     * 手机号
     */
    @Column(name = "mobile", length=50, nullable=true , unique=false )
    private String mobile;

    /**
     * 昵称
     */
    @Column(name = "nick_name", length=50, nullable=true , unique=false )
    private String nick_name;

    /**
     * 房间角色:1-主播,2-助手,3-嘉宾,9-粉丝
     */
    @Column(name = "room_role", nullable=true , unique=false )
    private Integer roomRole;

    /**
     * 等级
     */
    @Column(name = "rank", nullable=true , unique=false )
    private Integer rank;

    /**
     * 状态:1-正常,2-开除,3-注销
     */
    @Column(name = "state", nullable=true , unique=false )
    private Byte state;

    /**
     * 手机是否对成员可见
     */
    @Column(name = "is_secret", nullable=true , unique=false )
    private Byte isSecret;

    /**
     * 加入时间
     */
    @Column(name = "join_time", nullable=true , unique=false )
    private Date joinTime;

    /**
     * 开除时间
     */
    @Column(name = "fire_time", nullable=true , unique=false )
    private Date fireTime;

    /**
     * 取消关注时间
     */
    @Column(name = "cancel_time", nullable=true , unique=false )
    private Date cancelTime;

    /**
     * 创建人id
     */
    @Column(name = "creator", nullable=true , unique=false )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name", length=100, nullable=true , unique=false )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable=true , unique=false )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator", nullable=true , unique=false )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name", length=100, nullable=true , unique=false )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time", nullable=true , unique=false )
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSubOrg() {
        return subOrg;
    }

    public void setSubOrg(Long subOrg) {
        this.subOrg = subOrg;
    }

    public Long getApp() {
        return app;
    }

    public void setApp(Long app) {
        this.app = app;
    }

    public String getAppAcc() {
        return appAcc;
    }

    public void setAppAcc(String appAcc) {
        this.appAcc = appAcc;
    }

    public Long getMember() {
        return member;
    }

    public void setMember(Long member) {
        this.member = member;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNick_name() {
        return nick_name;
    }

    public void setNick_name(String nick_name) {
        this.nick_name = nick_name;
    }

    public Integer getRoomRole() {
        return roomRole;
    }

    public void setRoomRole(Integer roomRole) {
        this.roomRole = roomRole;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Byte getIsSecret() {
        return isSecret;
    }

    public void setIsSecret(Byte isSecret) {
        this.isSecret = isSecret;
    }

    public Date getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(Date joinTime) {
        this.joinTime = joinTime;
    }

    public Date getFireTime() {
        return fireTime;
    }

    public void setFireTime(Date fireTime) {
        this.fireTime = fireTime;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
