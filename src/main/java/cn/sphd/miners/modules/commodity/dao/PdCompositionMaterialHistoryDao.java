package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdCompositionMaterial;
import cn.sphd.miners.modules.commodity.entity.PdCompositionMaterialHistory;
import cn.sphd.miners.modules.commodity.entity.PdCustomerAddress;

import java.io.Serializable;

public interface PdCompositionMaterialHistoryDao extends IBaseDao<PdCompositionMaterialHistory, Serializable> {

    //添加历史记录
    void insert(PdCompositionMaterial pdCompositionMaterial,Integer mtHisId);
    //添加历史记录 mtHisId材料历史 pdHisId产品id
    void insert(PdCompositionMaterial pdCompositionMaterial,Integer mtHisId,Integer pdHisId);

    //添加历史记录 forId配方历史id pdHisId产品id
    void insertPdFormula(PdCompositionMaterial pdCompositionMaterial,Integer forId,Integer pdHisId);
}
