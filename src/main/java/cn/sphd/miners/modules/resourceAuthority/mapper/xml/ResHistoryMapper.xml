<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResHistoryMapper">

    <select id="getSingle" parameterType="resHis" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid,  type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted, is_trash AS isTrash,
            teminate_state AS teminateState, DATE_FORMAT(teminate_time,'%Y-%m-%d') AS teminateTime, teminater,
            teminater_name AS teminaterName, batch_uuid AS batchUuid
        from t_resource_history
        where id = #{id}
    </select>

    <select id="listPage" parameterType="hashmap" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize,org, enabled, is_deleted AS isDeleted
        from t_resource_history
        where id = #{id}
    </select>

    <select id="getAllUpdatePublishlistPage" resultType="resHis">
        select
        id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
        opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
        verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
        auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
        approve_status AS approveStatus, approver, approver_name AS approverName,
        DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
        create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
        update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, total_size AS totalSize,org, enabled, is_deleted AS isDeleted
        from t_resource_history
        where approve_status = #{approveStatus} and operation = 4 and type = 1 and updator is not null
        and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by audit_date desc
    </select>

    <select id="getApplyUpdateFileVersionByUser" resultType="resHis">
        select
            r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
            r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
            r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
            DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
            r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
            r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
            DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
            r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
            r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
            r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            r.message_id AS messageId, r.total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted,
            r.teminate_state AS teminateState, DATE_FORMAT(r.teminate_time,'%Y-%m-%d') AS teminateTime, r.teminater,
            r.teminater_name AS teminaterName
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.from_user = #{userID}
          AND r.updator = #{userID} AND r.approve_status = 1
        order by
            r.audit_date desc
    </select>

    <select id="changeFileVersionNum" resultType="java.lang.Integer">
        select
            COUNT(r.id) num
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.from_user = #{userID}
          AND r.updator = #{userID} AND r.approve_status = #{approveStatus}
          AND DATE_FORMAT(r.audit_date, '%Y-%m') = #{timeBegin}
        order by
            r.audit_date desc
    </select>

    <select id="getMonthChangeFile" resultType="resHis">
        select
            r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
            r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
            r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
            DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
            r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
            r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
            DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
            r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
            r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
            r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            r.message_id AS messageId, r.total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.from_user = #{userID}
          AND r.updator = #{userID} AND r.approve_status = #{approveStatus}
          AND DATE(r.audit_date) between #{timeBegin} and #{timeEnd}
        order by
            r.audit_date desc
    </select>

    <select id="getApproveFileByUser" resultType="resHis">
        select
        r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
        r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
        r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
        DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
        r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
        r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
        r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
        DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
        r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
        r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
        r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        r.message_id AS messageId, r.total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted,
        r.teminate_state AS teminateState, DATE_FORMAT(r.teminate_time,'%Y-%m-%d') AS teminateTime, r.teminater,
        r.teminater_name AS teminaterName
        from t_resource_history r, t_sys_approval_process ap
        where
        r.id = ap.business AND ap.business_type = 14 AND ap.to_user = #{userID}
        AND ap.approve_status = #{approveStatus} AND ap.to_mid is null
        <if test= 'approveStatus == "2"' >
            AND r.approve_status = 1
        </if>
        order by
        r.audit_date desc
    </select>

    <select id="getNeedApproveFile" resultType="java.lang.Integer">
        select
            COUNT(r.id)
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.to_user = #{userID}
          AND ap.approve_status = #{approveStatus} AND ap.to_mid is null
    </select>

    <select id="getApproveFileByUserNum" resultType="java.lang.Integer">
        select
        COUNT(r.id)
        from t_resource_history r, t_sys_approval_process ap
        where
        r.id = ap.business AND ap.business_type = 14 AND ap.to_user = #{userID} AND ap.to_mid is null
        AND r.approve_status = #{approveStatus}
        <if test="creator != null" >
            AND r.creator = #{creator} AND r.change_num = 0
        </if>
        <if test="updator != null" >
            AND r.updator = #{updator} AND r.change_num > 0
        </if>
        <if test="timeBegin != null" >
            AND DATE_FORMAT(r.audit_date, '%Y-%m') = #{timeBegin}
        </if>

    </select>

    <select id="getMonthApproveFile" resultType="resHis">
        select
        r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
        r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
        r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
        DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
        r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
        r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
        r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
        DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
        r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
        r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
        r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        r.message_id AS messageId, r.total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted
        from t_resource_history r, t_sys_approval_process ap
        where
        r.id = ap.business AND ap.business_type = 14 AND ap.to_user = #{userID} AND ap.to_mid is null
        AND r.approve_status = #{approveStatus}
        AND DATE(r.audit_date) between #{timeBegin} and #{timeEnd}
        <if test="creator != null" >
            AND r.creator = #{creator} AND r.change_num = 0
        </if>
        <if test="updator != null" >
            AND r.updator = #{updator} AND r.change_num > 0
        </if>
    </select>

    <select id="getFinalFile" resultType="resHis">
        select
            r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
            r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
            r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
            DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
            r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
            r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
            DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
            r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
            r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
            r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            r.message_id AS messageId, r.total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted,
            r.teminate_state AS teminateState, DATE_FORMAT(r.teminate_time,'%Y-%m-%d') AS teminateTime, r.teminater,
            r.teminater_name AS teminaterName
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg}
          AND r.approve_status = 1
        order by
            r.audit_date desc
    </select>

    <select id="getFinalFileNum" resultType="java.lang.Integer">
        select
        COUNT(r.id)
        from t_resource_history r, t_sys_approval_process ap
        where
        r.id = ap.business AND ap.business_type = 14 AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg}
        AND r.approve_status = #{approveStatus}
        <if test="fromUser != null" >
            AND ( r.creator = #{fromUser} OR r.updator = #{fromUser})
        </if>
        <if test="timeBegin != null" >
            AND ap.to_user = #{userID} AND DATE_FORMAT(r.audit_date, '%Y-%m') = #{timeBegin}
        </if>
    </select>

    <select id="getFinalFileByUserNum" resultType="java.lang.Integer">
        select
        COUNT(r.id)
        from t_resource_history r, t_sys_approval_process ap
        where
        r.id = ap.business AND ap.business_type = 14 AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg}
        AND r.approve_status = #{approveStatus}
        <if test="creator != null" >
            AND r.creator = #{creator} AND r.change_num = 0
        </if>
        <if test="updator != null" >
            AND r.updator = #{updator} AND r.change_num > 0
        </if>
        <if test="timeBegin != null" >
            AND ap.to_user = #{userID} AND DATE_FORMAT(r.audit_date, '%Y-%m') = #{timeBegin}
        </if>
    </select>

    <select id="getMonthFinalFile" resultType="resHis">
        select
        r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
        r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
        r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
        DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
        r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
        r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
        r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
        DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
        r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
        r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
        r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        r.message_id AS messageId, r.total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted
        from t_resource_history r, t_sys_approval_process ap
        where
        r.id = ap.business AND ap.business_type = 14 AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg}
        AND ap.to_user = #{userID} AND r.approve_status = #{approveStatus}
        AND DATE(r.audit_date) between #{timeBegin} and #{timeEnd}
        <if test="creator != null" >
            AND r.creator = #{creator} AND r.change_num = 0
        </if>
        <if test="updator != null" >
            AND r.updator = #{updator} AND r.change_num > 0
        </if>
    </select>

    <select id="getFileByPublishlistPage" resultType="resHis">
        select
        id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
        opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
        verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
        auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
        approve_status AS approveStatus, approver, approver_name AS approverName,
        DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
        create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
        update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted
        from t_resource_history
        where approve_status = #{approveStatus} and type = 1 and updator is null and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by audit_date desc
    </select>

    <select id="getPublishFileByUser" resultType="resHis">
        select
            r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
            r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
            r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
            DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
            r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
            r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
            DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
            r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
            r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
            r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            r.message_id AS messageId, total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.from_user = #{userID} AND r.creator = #{userID}
          AND r.updator IS NULL AND r.approve_status = 1
        order by
            r.audit_date desc
    </select>

    <select id="publishFileNum" resultType="java.lang.Integer">
        select
            COUNT(r.id) num
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.from_user = #{userID} AND r.creator = #{userID}
          AND r.updator IS NULL AND r.approve_status = #{approveStatus}
          AND DATE_FORMAT(r.audit_date, '%Y-%m') = #{timeBegin}
        order by
            r.audit_date desc
    </select>

    <select id="getMonthPublishFile" resultType="resHis">
        select
            r.id, r.file, r.category, r.name, r.file_sn AS fileSn, r.content, r.reason, r.path, r.size,
            r.change_num AS changeNum, r.download_num AS downloadNum, r.version, r.keyword, r.valid, r.type,
            r.is_stick AS isStick, r.operator, r.opertator_name AS opertatorName,
            DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
            r.verifier, r.verifier_name AS verifierName, DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate,
            r.auditor, r.audit_name AS auditName, DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            r.approve_status AS approveStatus, r.approver, r.approver_name AS approverName,
            DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate,
            r.operation, r.apply_memo AS applyMemo, r.approve_memo AS approveMemo, r.memo, r.creator,
            r.create_name AS createName, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, r.updator,
            r.update_name AS updateName, DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            r.message_id AS messageId, r.total_size AS totalSize ,r.org, r.enabled, r.is_deleted AS isDeleted
        from t_resource_history r, t_sys_approval_process ap
        where
            r.id = ap.business AND ap.business_type = 14 AND ap.from_user = #{userID} AND r.creator = #{userID}
          AND r.updator IS NULL AND r.approve_status = #{approveStatus}
          AND DATE(r.audit_date) between #{timeBegin} and #{timeEnd}
        order by
            r.audit_date desc
    </select>

    <select id="getFirstFileHistory" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted
        from t_resource_history
        where file = #{file} and operation = 4 order by audit_date asc limit 0, 1
    </select>

    <select id="getNewFileHistory" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, is_trash AS isTrash, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted,
            teminate_state AS teminateState, DATE_FORMAT(teminate_time,'%Y-%m-%d') AS teminateTime, teminater,
            teminater_name AS teminaterName, batch_uuid AS batchUuid
        from t_resource_history
        where file = #{file} AND  approve_status = 2 order by update_date desc limit 0, 1
    </select>

    <select id="getNewFileHistoryByVersion" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, is_trash AS isTrash, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted,
            teminate_state AS teminateState, DATE_FORMAT(teminate_time,'%Y-%m-%d') AS teminateTime, teminater,
            teminater_name AS teminaterName, batch_uuid AS batchUuid
        from t_resource_history
        where file = #{file} AND  approve_status = 2 AND teminate_state = 0 order by update_date desc limit 0, 1
    </select>


    <select id="getHistoryByFilelistPage" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted,
            teminate_state AS teminateState, DATE_FORMAT(teminate_time,'%Y-%m-%d') AS teminateTime, teminater,
            teminater_name AS teminaterName
        from t_resource_history
        where file = #{file} and  change_num != #{changeNum} and approve_status = 2
        order by audit_date desc
    </select>

    <select id="getAllFileHislistPage" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted
        from t_resource_history
        where file = #{file} and approve_status = 2 and operation = 4
        order by id desc
    </select>

    <select id="getRecordByOperation" resultType="resHis">
        select
            id, file, category, category_name AS categoryName, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted
        from t_resource_history
        where file = #{file}  and operation = #{operation}
    </select>

    <select id="getMovetrashRecord" resultType="resHis">
        select
            id, file, category, category_name AS categoryName, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org
        from t_resource_history
        where file = #{file}  and operation = 5 and is_trash = #{isTrash}
        order by create_date desc
    </select>

    <select id="getMoveRecordDesc" resultType="resHis">
        select
            id, file, category, category_name AS categoryName, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, is_trash AS isTrash, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org
        from t_resource_history
        where file = #{file}  and operation = 5
        order by create_date desc
    </select>

    <select id="getLastMoveRecordlistPage" resultType="resHis">
        select
            id, file, category, category_name AS categoryName, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org
        from t_resource_history
        where file = #{file}  and operation = 5
        order by create_date desc
    </select>


    <select id="getHIsFileByCategoryidAndApproveStatus" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, content, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, version, keyword, valid, type, is_stick AS isStick, operator,
            opertator_name AS opertatorName, DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate,
            verifier, verifier_name AS verifierName, DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate,
            auditor, audit_name AS auditName, DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate,
            approve_status AS approveStatus, approver, approver_name AS approverName,
            DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,
            update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, total_size AS totalSize ,org, enabled, is_deleted AS isDeleted
        from t_resource_history
        where category = #{category} and approve_status = 1
    </select>

    <select id="checckFileSnByHistory" resultType="java.lang.Integer">
        select
        COUNT(id) num
        from t_resource_history
        where file_sn = #{fileSn} and approve_status = 1 and operation = 4  and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="searchHisByHeadNum" resultType="resHis">
        select
        id, file, category, name, file_sn AS fileSn, content
        from t_resource_history
        where file_sn like CONCAT(#{FileSn},'%') and approve_status = 1 and operation = 4  and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getHisFileSize" resultType="java.lang.Long">
        select
        SUM(size) size
        from t_resource_history
        where
        <if test="org != null" >
            org = #{org}
        </if>
        <if test="categoryPath != null" >
            category_path like CONCAT(#{categoryPath},'%')
        </if>
        and approve_status = 2 and operation = 4
    </select>

    <select id="getNotApproveHisFileNum" resultType="java.lang.Long">
        select
            COUNT(id) num
        from t_resource_history
        where
            batch_uuid = #{batchUuid} and approve_status = #{approveStatus}
    </select>

    <select id="getListHisFileByBatchUuid" resultType="resHis">
        select
            id, file, category, name, file_sn AS fileSn, change_num AS changeNum, version,
            create_name AS createName, DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate
        from
            t_resource_history
        where
            batch_uuid = #{batchUuid} and approve_status = 2
        order by audit_date desc
    </select>

    <select id="listHisFileByTemiante" resultType="resHis">
        select
            id,  name, file_sn AS fileSn, change_num AS changeNum, update_name AS updateName,
            DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, teminate_state AS teminateState
        from
            t_resource_history
        where
            file = #{fileId} and operation = 4 and approve_status = 2 and (teminate_state = 1 or teminate_state = 2)
    </select>

    <delete id="delete" parameterType="resHis" >
        delete from t_resource_history
        where id = #{id}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="resHis" >
        insert into t_resource_history (file, category, category_name, category_path,
                                        enabled, enabled_time,
                                        name, file_sn, content, reason,
                                        path, size, change_num,
                                        download_num, version, keyword,
                                        valid, type, is_stick, operator, opertator_name,
                                        operate_date, verifier, verifier_name,
                                        verify_date, auditor, audit_name,
                                        audit_date, approve_status, approver,
                                        approver_name, approve_date, operation,
                                        apply_memo, approve_memo, memo,
                                        creator, create_name, create_date,
                                        updator, update_name, update_date,
                                        message_id, total_size ,org, is_trash, is_deleted,
                                        teminate_state, teminate_time, teminater, teminater_name,
                                        batch_uuid)
        values (#{file}, #{category}, #{categoryName},#{categoryPath},
                #{enabled}, #{enabledTime},
                #{name}, #{fileSn}, #{content}, #{reason},
                #{path}, #{size}, #{changeNum},
                #{downloadNum}, #{version}, #{keyword},
                #{valid}, #{type}, #{isStick}, #{operator}, #{opertatorName},
                #{operateDate}, #{verifier}, #{verifierName},
                #{verifyDate}, #{auditor}, #{auditName},
                #{auditDate}, #{approveStatus}, #{approver},
                #{approverName}, #{approveDate}, #{operation},
                #{applyMemo}, #{approveMemo}, #{memo},
                #{creator}, #{createName}, #{createDate},
                #{updator}, #{updateName}, #{updateDate},
                #{messageId}, #{totalSize}, #{org}, #{isTrash}, #{isDeleted},
                #{teminateState}, #{teminateTime}, #{teminater}, #{teminaterName},
                #{batchUuid})
    </insert>

    <update id="update" parameterType="resHis" >
        update t_resource_history
        <set >
            <if test="file != null" >
                file = #{file},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="enabled != null" >
                enabled = #{enabled},
            </if>
            <if test="enabledTime != null" >
                enabled_time = #{enabledTime},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="fileSn != null" >
                file_sn = #{fileSn},
            </if>
            <if test="content != null" >
                content = #{content},
            </if>
            <if test="reason != null" >
                reason = #{reason},
            </if>
            <if test="path != null" >
                path = #{path},
            </if>
            <if test="size != null" >
                size = #{size},
            </if>
            <if test="changeNum != null" >
                change_num = #{changeNum},
            </if>
            <if test="downloadNum != null" >
                download_num = #{downloadNum},
            </if>
            <if test="version != null" >
                version = #{version},
            </if>
            <if test="keyword != null" >
                keyword = #{keyword},
            </if>
            <if test="valid != null" >
                valid = #{valid},
            </if>
            <if test="type != null" >
                type = #{type},
            </if>
            <if test="isStick != null" >
                is_stick = #{isStick},
            </if>
            <if test="operator != null" >
                operator = #{operator},
            </if>
            <if test="opertatorName != null" >
                opertator_name = #{opertatorName},
            </if>
            <if test="operateDate != null" >
                operate_date = #{operateDate},
            </if>
            <if test="verifier != null" >
                verifier = #{verifier},
            </if>
            <if test="verifierName != null" >
                verifier_name = #{verifierName},
            </if>
            <if test="verifyDate != null" >
                verify_date = #{verifyDate},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditName != null" >
                audit_name = #{auditName},
            </if>
            <if test="auditDate != null"          >
                audit_date = #{auditDate},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approver != null" >
                approver = #{approver},
            </if>
            <if test="approverName != null" >
                approver_name = #{approverName},
            </if>
            <if test="approveDate != null" >
                approve_date = #{approveDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="totalSize != null" >
                total_size = #{totalSize},
            </if>
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="isDeleted != null" >
                is_deleted = #{isDeleted},
            </if>
            <if test="teminateState != null" >
                teminate_state = #{teminateState},
            </if>
            <if test="teminateTime != null" >
                teminate_time = #{teminateTime},
            </if>
            <if test="teminater != null" >
                teminater = #{teminater},
            </if>
            <if test="teminaterName != null" >
                teminater_name = #{teminaterName},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateHIstoryByfirst" parameterType="resHis">
        update t_resource_history
        <set >
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="fileSn != null" >
                file_sn = #{fileSn},
            </if>
            <if test="reason != null" >
                reason = #{reason},
            </if>
            <if test="path != null" >
                path = #{path},
            </if>
            <if test="categoryPath != null" >
                category_path = #{categoryPath},
            </if>
            <if test="enabled != null" >
                enabled = #{enabled},
            </if>
            <if test="enabledTime != null" >
                enabled_time = #{enabledTime},
            </if>
            <if test="isTrash != null" >
                is_trash = #{isTrash},
            </if>
            <if test="size != null" >
                size = #{size},
            </if>
            <if test="changeNum != null" >
                change_num = #{changeNum},
            </if>
            <if test="downloadNum != null" >
                download_num = #{downloadNum},
            </if>
            <if test="version != null" >
                version = #{version},
            </if>
            <if test="keyword != null" >
                keyword = #{keyword},
            </if>
            <if test="valid != null" >
                valid = #{valid},
            </if>
            <if test="type != null" >
                type = #{type},
            </if>
            <if test="isStick != null" >
                is_stick = #{isStick},
            </if>
            <if test="operator != null" >
                operator = #{operator},
            </if>
            <if test="opertatorName != null" >
                opertator_name = #{opertatorName},
            </if>
            <if test="operateDate != null" >
                operate_date = #{operateDate},
            </if>
            <if test="verifier != null" >
                verifier = #{verifier},
            </if>
            <if test="verifierName != null" >
                verifier_name = #{verifierName},
            </if>
            <if test="verifyDate != null" >
                verify_date = #{verifyDate},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditName != null" >
                audit_name = #{auditName},
            </if>
            <if test="auditDate != null"          >
                audit_date = #{auditDate},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approver != null" >
                approver = #{approver},
            </if>
            <if test="approverName != null" >
                approver_name = #{approverName},
            </if>
            <if test="approveDate != null" >
                approve_date = #{approveDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="content != null" >
                content = #{content},
            </if>
            <if test="totalSize != null" >
                total_size = #{totalSize},
            </if>
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="isDeleted != null" >
                is_deleted = #{isDeleted},
            </if>
            <if test="teminateState != null" >
                teminate_state = #{teminateState},
            </if>
            <if test="teminateTime != null" >
                teminate_time = #{teminateTime},
            </if>
            <if test="teminater != null" >
                teminater = #{teminater},
            </if>
            <if test="teminaterName != null" >
                teminater_name = #{teminaterName},
            </if>
        </set>
        where file = #{file} and operation = 4
    </update>

    <update id="updateHisResCategoryPath" parameterType="resHis">
        update t_resource_history
        <set >
            <if test="categoryPath != null" >
                category_path = #{categoryPath},
            </if>
            <if test="isTrash != null" >
                is_trash = #{isTrash},
            </if>
        </set>
        where category = #{category} and operation = 4
    </update>

    <update id="updateHisResCategoryPathAndName">
        update t_resource_history
        <set >
            <if test="categoryPath != null" >
                category_path = #{categoryPath},
            </if>
            <if test="categoryName != null" >
                category_name = #{categoryName},
            </if>
        </set>
        where category = #{category} and operation = 5
    </update>

</mapper>