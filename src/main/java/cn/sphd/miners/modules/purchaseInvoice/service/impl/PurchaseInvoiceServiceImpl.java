package cn.sphd.miners.modules.purchaseInvoice.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.*;
import cn.sphd.miners.modules.loan.dao.LoanBizDao;
import cn.sphd.miners.modules.loan.entity.LoanBiz;
import cn.sphd.miners.modules.loan.service.LoanBizService;
import cn.sphd.miners.modules.material.dao.SrmSupplierDao;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.purchaseInvoice.dao.PoPaymentApplicationDao;
import cn.sphd.miners.modules.purchaseInvoice.dao.PoPaymentInvoiceAttachmentDao;
import cn.sphd.miners.modules.purchaseInvoice.dao.PoPaymentInvoiceDao;
import cn.sphd.miners.modules.purchaseInvoice.dao.PoPaymentInvoiceItemDao;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentApplication;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoice;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoiceAttachment;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoiceItem;
import cn.sphd.miners.modules.purchaseInvoice.service.PurchaseInvoiceService;
import cn.sphd.miners.modules.sales.dao.PoOrdersDao;
import cn.sphd.miners.modules.sales.dao.PoOrdersItemDao;
import cn.sphd.miners.modules.sales.dao.PoOrdersPrepaymentDao;
import cn.sphd.miners.modules.sales.model.PoOrders;
import cn.sphd.miners.modules.sales.model.PoOrdersItem;
import cn.sphd.miners.modules.sales.model.PoOrdersPrepayment;
import cn.sphd.miners.modules.sales.service.PoService;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * lyx on 2021/6/16
 */

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PurchaseInvoiceServiceImpl implements PurchaseInvoiceService {

    @Autowired
    PoPaymentApplicationDao poPaymentApplicationDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    UserDao userDao;
    @Autowired
    PoOrdersDao poOrdersDao;
    @Autowired
    PoOrdersItemDao poOrdersItemDao;
    @Autowired
    SrmSupplierDao srmSupplierDao;
    @Autowired
    PoPaymentInvoiceDao poPaymentInvoiceDao;
    @Autowired
    PoPaymentInvoiceItemDao poPaymentInvoiceItemDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    FinanceChequeDetailDao financeChequeDetailDao;
    @Autowired
    FinanceReturnDao financeReturnDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    FinanceChequeDetailHistoryDao financeChequeDetailHistoryDao;
    @Autowired
    FinanceReturnHistoryDao financeReturnHistoryDao;
    @Autowired
    FinanceAccountBillHistoryDao financeAccountBillHistoryDao;
    @Autowired
    FinancePaymentDao financePaymentDao;
    @Autowired
    FinancePaymentHistoryDao financePaymentHistoryDao;
    @Autowired
    PoPaymentInvoiceAttachmentDao poPaymentInvoiceAttachmentDao;
    @Autowired
    PoOrdersPrepaymentDao poOrdersPrepaymentDao;
    @Autowired
    PoLoanDao poLoanDao;
    @Autowired
    LoanBizDao loanBizDao;

    @Autowired
    UserService userService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    RoleService roleService;
    @Autowired
    FinancePaymentService financePaymentService;
    @Autowired
    AccountService accountService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    UploadService uploadService;
    @Autowired
    PoService poService;
    @Autowired
    LoanService loanService;
    @Autowired
    FinanceCommonService financeCommonService;
    @Autowired
    LoanBizService loanBizService;


    /**
     * 票款处理的票据录入
     * @param user
     * @param type  类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
     * @param purchaseInvoice  录入的票据信息
     * @return
     */
    @Override
    public Map<String, Object> purchaseInvoiceApply(User user,String type,Double amountTotal,Double billAmountTotal,Integer orders,String applicationReason,String applicationDesc,String purchaseInvoice) {
        Map<String,Object> map = new HashMap<>();
        String content = "操作成功";
        ApprovalItem approvalItem1 = roleService.getCurrentItem(user.getOid(),"paymentApproval");  //付款审批流程
        ApprovalItem approvalItem2 = roleService.getApprovalItemByPreviousItem(approvalItem1.getId()); //付款复核的流程
        PoPaymentApplication poPaymentApplication = new PoPaymentApplication();
        poPaymentApplication.setOrg(user.getOid());
        poPaymentApplication.setApplicant(user.getUserID());  //申请人id
        poPaymentApplication.setApplicantName(user.getUserName());  //申请人名称
        poPaymentApplication.setType(type);  //类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
        poPaymentApplication.setFeeCategory("采购材料");  //费用类型
        poPaymentApplication.setBillState("1");  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
        poPaymentApplication.setPayState("1");   //票据审批状态:0-暂存,1-录入,2-提交,3-采购审批通过,4-采购审批驳回
        poPaymentApplication.setApproveStatus("1"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
        poPaymentApplication.setBillCount(0);     //票据数量[整个票据申请的总数]
        poPaymentApplication.setAmount(new BigDecimal(amountTotal));  //申请金额
        if (billAmountTotal==null){
            poPaymentApplication.setBillAmount(new BigDecimal(amountTotal));    //票据金额
        }else {
            poPaymentApplication.setBillAmount(new BigDecimal(billAmountTotal));    //票据金额
        }
        poPaymentApplication.setOrders(orders);   //订单ID
        poPaymentApplication.setApplicationReason(applicationReason);  //申请原因:1-符合合同约定（默认）;2-不符合合同约定
        poPaymentApplication.setApplicationDesc(applicationDesc);  //付款原因说明(申请原因选择2-不符合合同约定时录入的具体原因)
        poPaymentApplication.setCreateDate(new Date());
        poPaymentApplication.setCreateName(user.getUserName());
        poPaymentApplication.setCreator(user.getUserID());
        poPaymentApplication.setPayInstance(approvalItem1.getId());  //付款审批流程
        poPaymentApplication.setAuditInstance(approvalItem2.getId());  //付款复核流程

        PoOrders poOrders = poOrdersDao.get(orders);   //订单信息

        if (!"3".equals(type)){   //1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请
            poPaymentApplication.setBillState("2");  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
            Integer billCount = 0;  //计算票据数量
            if (!StringUtils.isEmpty(purchaseInvoice)){
                poPaymentApplicationDao.save(poPaymentApplication);

                JSONArray jsonArray = JSONArray.fromObject(purchaseInvoice,new JsonConfig());
                List<PoPaymentInvoice> purchaseInvoiceList = (List<PoPaymentInvoice>) JSONArray.toCollection(jsonArray);
                for (int i=0;i<purchaseInvoiceList.size();i++) {
                    JSONObject poInvoiceJsonList = JSONObject.fromObject(purchaseInvoiceList.get(i));
                    List poPaymentInvoiceList = poInvoiceJsonList.getJSONArray("poPaymentInvoices");
                    String  invoiceCategory = poInvoiceJsonList.getString("invoiceCategory"); //发票种类:1-专用票,2-普通票,3-收据,4-其他发票
                    Integer pid = 0;  //一次录入多张票据的主票据id
                    for (int z=0;z<poPaymentInvoiceList.size();z++) {
                        JSONObject paymentInvoice = JSONObject.fromObject(poPaymentInvoiceList.get(z));
                        List billNos = paymentInvoice.getJSONArray("billNos");//号码
                        PoPaymentInvoice poPaymentInvoice = new PoPaymentInvoice();
                        poPaymentInvoice.setOrg(user.getOid());
                        poPaymentInvoice.setApplication(poPaymentApplication.getId());  //申请ID
                        poPaymentInvoice.setInvoiceCategory(invoiceCategory);  //发票种类:1-专用票,2-普通票,3-收据,4-其他发票

                        Integer relativeBillQuantity = paymentInvoice.getInt("relativeBillQuantity");//相同票据数量【票据数量】
                        poPaymentInvoice.setRelativeBillQuantity(relativeBillQuantity); //相同票据数量【票据数量】
                        if (relativeBillQuantity>1){
                            Double a = paymentInvoice.getDouble("amount")*relativeBillQuantity;
                            Double ba = paymentInvoice.getDouble("billAmount")*relativeBillQuantity;
                            poPaymentInvoice.setAmount(new BigDecimal(a));  //金额(单张票据金额的总金额)
                            poPaymentInvoice.setBillAmount(new BigDecimal(ba)); //票据金额
                        }else {
                            poPaymentInvoice.setAmount(new BigDecimal(paymentInvoice.getDouble("amount")));  //金额
                            poPaymentInvoice.setBillAmount(new BigDecimal(paymentInvoice.getDouble("billAmount"))); //票据金额
                        }
                        poPaymentInvoice.setParAmount(new BigDecimal(paymentInvoice.getDouble("billAmount")));  //面值(单张票据金额)
                        poPaymentInvoice.setItemCount(paymentInvoice.getInt("itemCount"));  //单张票据行数
                        String taxAmount1 = paymentInvoice.getString("taxAmount");
                        if (StringUtils.isNotEmpty(taxAmount1)) {
                            poPaymentInvoice.setTaxAmount(new BigDecimal(taxAmount1));   //税额
                        }
                        if (billNos.size()>0) {
                            poPaymentInvoice.setInvoiceNo(billNos.get(0).toString()); //发票号码
                        }

//                        poPaymentInvoice.setRelativeBillNo(poInvoiceJsonList.getString("billNos"));//相同票据号码,多个以逗号分隔
                        poPaymentInvoice.setPrimaryBill(pid);//主票号码ID,多张票录入时用
                        poPaymentInvoice.setCertificationState(0);
                        poPaymentInvoice.setCreateDate(new Date());
                        poPaymentInvoice.setCreateName(user.getUserName());
                        poPaymentInvoice.setCreator(user.getUserID());
                        poPaymentInvoiceDao.save(poPaymentInvoice);
                        pid=poPaymentInvoice.getId();

                        List<PoPaymentInvoiceItem> purchaseInvoiceItemList = paymentInvoice.getJSONArray("paymentInvoiceItems");
                        for (int j = 0; j < purchaseInvoiceItemList.size(); j++) {
                            JSONObject poInvoiceItemJsonList = JSONObject.fromObject(purchaseInvoiceItemList.get(j));
                            PoPaymentInvoiceItem poPaymentInvoiceItem = new PoPaymentInvoiceItem();
                            poPaymentInvoiceItem.setOrg(user.getOid());
                            poPaymentInvoiceItem.setApplication(poPaymentApplication.getId());  //申请ID
                            poPaymentInvoiceItem.setInvoice(poPaymentInvoice.getId()); //发票ID
                            poPaymentInvoiceItem.setOrders(orders);
                            poPaymentInvoiceItem.setOrdersItem(poInvoiceItemJsonList.getInt("ordersItem"));  //订单明细项ID
                            poPaymentInvoiceItem.setCustomer(poOrders.getSupplier().intValue());  //顾客ID(暂时为供应商id)
                            poPaymentInvoiceItem.setItemName(poInvoiceItemJsonList.getString("itemName"));  //明细项名称(货物或应税劳务、服务名称)
                            poPaymentInvoiceItem.setModel(poInvoiceItemJsonList.getString("model"));   //规格型号
                            if (StringUtils.isNotEmpty(poInvoiceItemJsonList.getString("itemQuantity"))) {  //判断是否为空
                                poPaymentInvoiceItem.setItemQuantity(poInvoiceItemJsonList.getDouble("itemQuantity"));   //数量(注意double是为了凑税额)
                            }
                            poPaymentInvoiceItem.setUnit(poInvoiceItemJsonList.getString("unit"));  //单位
                            poPaymentInvoiceItem.setUnitPrice(poInvoiceItemJsonList.getDouble("unitPrice"));  //单价(注意double是为了凑税额)
                            poPaymentInvoiceItem.setPrice(new BigDecimal(poInvoiceItemJsonList.getDouble("price"))); //价格金额
                            if(!"3".equals(invoiceCategory)) {  //收据不传
                                String taxRate = poInvoiceItemJsonList.getString("taxRate");
                                if (!MyStrings.nulltoempty(taxRate).isEmpty()) {
                                    poPaymentInvoiceItem.setTaxRate(Integer.getInteger(taxRate));   //税率
                                }
                                String taxAmount = poInvoiceItemJsonList.getString("taxAmount");
                                if (!MyStrings.nulltoempty(taxAmount).isEmpty()) {
                                    poPaymentInvoiceItem.setTaxAmount(new BigDecimal(taxAmount));   //税额
                                }
                            }

                            if ("3".equals(invoiceCategory)) {
                                poPaymentInvoiceItem.setAmount(new BigDecimal(poInvoiceItemJsonList.getDouble("price")));  //价税合计
                            }else {
                                poPaymentInvoiceItem.setAmount(new BigDecimal(poInvoiceItemJsonList.getDouble("amount")));  //价税合计
                            }
                            poPaymentInvoiceItem.setCreateDate(new Date());
                            poPaymentInvoiceItem.setCreateName(user.getUserName());
                            poPaymentInvoiceItem.setCreator(user.getUserID());
                            poPaymentInvoiceItemDao.save(poPaymentInvoiceItem);
                        }

                        List imgPaths = paymentInvoice.getJSONArray("imgPaths");//多附件路径
                        int l = 0;  //添加图片
                        while (l < imgPaths.size() && imgPaths.size() > 0) {//附件
                            if (!MyStrings.nulltoempty(imgPaths.get(l).toString()).isEmpty()){
                                PoPaymentInvoiceAttachment poPaymentInvoiceAttachment = new PoPaymentInvoiceAttachment();
                                poPaymentInvoiceAttachment.setPath(imgPaths.get(l).toString());
                                poPaymentInvoiceAttachment.setInvoice(poPaymentInvoice.getId());
                                poPaymentInvoiceAttachment.setOrg(user.getOid());
                                poPaymentInvoiceAttachmentDao.save(poPaymentInvoiceAttachment);

                                PurchaseInvoiceUsing reimburseUsing = new PurchaseInvoiceUsing(poPaymentInvoiceAttachment.getId());
                                uploadService.addFileUsing(reimburseUsing, imgPaths.get(l).toString(),null, user, "采购");//新增引用表
                            }
                            l++;
                        }

                        for (int m = 1; m < relativeBillQuantity; m++) {   //有相同票据的内容【第一个上面已添加】
                            PoPaymentInvoice poPaymentInvoice1 = new PoPaymentInvoice();
                            BeanUtils.copyPropertiesIgnoreNull(poPaymentInvoice, poPaymentInvoice1);  //相同的部分已复制
                            if (billNos.size()>0) {
                                poPaymentInvoice1.setInvoiceNo(billNos.get(m).toString()); //发票号码
                            }
                            poPaymentInvoice1.setRelativeBillQuantity(0); //相同票据数量
                            poPaymentInvoice1.setPrimaryBill(pid);//主票号码ID,多张票录入时用
                            poPaymentInvoice1.setCreateDate(new Date());
                            poPaymentInvoice1.setCreateName(user.getUserName());
                            poPaymentInvoice1.setCreator(user.getUserID());
                            poPaymentInvoiceDao.save(poPaymentInvoice1);

                            for (int j = 0; j < purchaseInvoiceItemList.size(); j++) {
                                JSONObject poInvoiceItemJsonList = JSONObject.fromObject(purchaseInvoiceItemList.get(j));
                                PoPaymentInvoiceItem poPaymentInvoiceItem = new PoPaymentInvoiceItem();
                                poPaymentInvoiceItem.setOrg(user.getOid());
                                poPaymentInvoiceItem.setApplication(poPaymentApplication.getId());  //申请ID
                                poPaymentInvoiceItem.setInvoice(poPaymentInvoice1.getId()); //发票ID
                                poPaymentInvoiceItem.setOrders(orders);
                                poPaymentInvoiceItem.setOrdersItem(poInvoiceItemJsonList.getInt("ordersItem"));  //订单明细项ID
                                poPaymentInvoiceItem.setCustomer(poOrders.getSupplier().intValue());  //顾客ID(暂时为供应商id)
                                poPaymentInvoiceItem.setItemName(poInvoiceItemJsonList.getString("itemName"));  //明细项名称(货物或应税劳务、服务名称)
                                poPaymentInvoiceItem.setModel(poInvoiceItemJsonList.getString("model"));   //规格型号
                                poPaymentInvoiceItem.setItemQuantity(poInvoiceItemJsonList.getDouble("itemQuantity"));   //数量(注意double是为了凑税额)
                                poPaymentInvoiceItem.setUnit(poInvoiceItemJsonList.getString("unit"));  //单位
                                poPaymentInvoiceItem.setUnitPrice(poInvoiceItemJsonList.getDouble("unitPrice"));  //单价(注意double是为了凑税额)
                                poPaymentInvoiceItem.setPrice(new BigDecimal(poInvoiceItemJsonList.getDouble("price"))); //价格金额
                                if(!"3".equals(invoiceCategory)) {  //收据不传
                                    String taxRate = poInvoiceItemJsonList.getString("taxRate");
                                    if (!MyStrings.nulltoempty(taxRate).isEmpty()) {
                                        poPaymentInvoiceItem.setTaxRate(Integer.getInteger(taxRate));   //税率
                                    }
                                    String taxAmount = poInvoiceItemJsonList.getString("taxAmount");
                                    if (!MyStrings.nulltoempty(taxAmount).isEmpty()) {
                                        poPaymentInvoiceItem.setTaxAmount(new BigDecimal(taxAmount));   //税额
                                    }  //税额
                                }
                                if ("3".equals(invoiceCategory)) {
                                    poPaymentInvoiceItem.setAmount(new BigDecimal(poInvoiceItemJsonList.getDouble("price")));  //价税合计
                                }else {
                                    poPaymentInvoiceItem.setAmount(new BigDecimal(poInvoiceItemJsonList.getDouble("amount")));  //价税合计
                                }
                                poPaymentInvoiceItem.setCreateDate(new Date());
                                poPaymentInvoiceItem.setCreateName(user.getUserName());
                                poPaymentInvoiceItem.setCreator(user.getUserID());
                                poPaymentInvoiceItemDao.save(poPaymentInvoiceItem);
                            }

                            int q = 0;  //添加图片
                            while (q < imgPaths.size() && imgPaths.size() > 0) {//附件
                                if (!MyStrings.nulltoempty(imgPaths.get(q).toString()).isEmpty()){
                                    PoPaymentInvoiceAttachment poPaymentInvoiceAttachment = new PoPaymentInvoiceAttachment();
                                    poPaymentInvoiceAttachment.setPath(imgPaths.get(q).toString());
                                    poPaymentInvoiceAttachment.setInvoice(poPaymentInvoice1.getId());
                                    poPaymentInvoiceAttachment.setOrg(user.getOid());
                                    poPaymentInvoiceAttachmentDao.save(poPaymentInvoiceAttachment);

                                    PurchaseInvoiceUsing purchaseInvoiceUsing = new PurchaseInvoiceUsing(poPaymentInvoiceAttachment.getId());
                                    uploadService.addFileUsing(purchaseInvoiceUsing, imgPaths.get(q).toString(),null, user, "采购");//新增引用表
                                }
                                q++;
                            }
                        }
                        billCount = billCount+relativeBillQuantity;
                    }
                }
                poPaymentApplication.setBillCount(billCount);  //计算总的票据数量

                //财务出纳-待在线审核的流程
                ApprovalProcess process = new ApprovalProcess();
                process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                process.setLevel(1);
                process.setToUserName("财务处理者");
                process.setToMid("lp");  //报销受理模块
                process.setCreateDate(new Date());
                process.setOrg(user.getOid());
                process.setUserName("财务处理者");
                process.setFromUser(user.getUserID());
                process.setAskName(user.getUserName());
                process.setHandleTime(new Date());
                process.setBusiness(poPaymentApplication.getId());
                process.setBusinessType(45);
                approvalProcessDao.save(process);

                HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,poOrders,null,null);  //推送的内容
                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                for (UserPopedom u : userPopedomList) {
                    //财务审批人待在线审核+1
                    this.purchaseInvoiceRejectSend(1,1,hashMap, u.getUserId(), "/paymentOnlineHandle",null,null,"purchaseBillFinanceApproval");
                }

                //申请人-采购部门的票据审核
                this.purchaseInvoiceRejectSend(0, 1, hashMap, user.getUserID(), "/invoiceApplyHanle", null, null, "purchaseBillApply");//申请人
            }else {
                content = "操作失败";
            }
        }else {   //3-仅提交付款申请,不录入票据
            poPaymentApplication.setPayState("2");   //票据审批状态:0-暂存,1-录入,2-提交,3-采购审批通过,4-采购审批驳回
//            poPaymentApplication.setBillCount(1);
            poPaymentApplicationDao.save(poPaymentApplication);

            User superUser = userService.getUserByRoleCode(user.getOid(),"super");  //默认审批人是超管【采购审批者】
            ApprovalProcess approvalProcess = new ApprovalProcess();
            approvalProcess.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回
            approvalProcess.setLevel(1);
            approvalProcess.setToUser(superUser.getUserID());  //审批人id
            approvalProcess.setToUserName("指定审批人");  //审批人总称
            approvalProcess.setUserName(superUser.getUserName()); //审批人名称
            approvalProcess.setCreateDate(new Date());
            approvalProcess.setOrg(user.getOid());
            approvalProcess.setFromUser(user.getUserID());
            approvalProcess.setAskName(user.getUserName());
            approvalProcess.setBusiness(poPaymentApplication.getId());
            approvalProcess.setBusinessType(48);
            approvalProcessDao.save(approvalProcess);

            HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,poOrders,null,null);
            //采购审批者-采购部门的付款-待处理
            this.purchaseInvoiceRejectSend(1,1,hashMap, superUser.getUserID(), "/purchasePaymentHandle",null,null,"purchasePaymentApproval");

            //申请人-采购部门的付款
            this.purchaseInvoiceRejectSend(0, 1, hashMap, user.getUserID(), "/paymentApplyHandle", null, null, "purchasePaymentApply");//申请人
        }
        map.put("content",content);
        return map;
    }

    //整合需要推送内容
    @Override
    public HashMap<String,Object> rejectSendType(PoPaymentApplication poPaymentApplication,PoOrders poOrders, Integer orderId,PoOrdersPrepayment poOrderPrepayment){
        HashMap<String,Object> hashMap=new HashMap<>();
        if (poOrders==null) {
            poOrders = poOrdersDao.get(orderId);
        }
        if (poOrders!=null){
            SrmSupplier srmSupplier = getSupplier(poOrders.getId());
            if (srmSupplier!=null) {
                hashMap.put("fullName", srmSupplier.getFullName()); //供应商全称(应该是简称吧)
                hashMap.put("codeName", srmSupplier.getCodeName());  //代号
                hashMap.put("poPaymentApplication", poPaymentApplication);   //票据的内容

                 //订单预付款信息
                hashMap.put("poOrderPrepayment", poOrderPrepayment);
                hashMap.put("poOrders", poOrders); //订单内容
            }
        }
        return hashMap;
    }

    //整合需要推送内容中的订单详情
    public SrmSupplier getSupplier(Integer orderId){
        Map<String,Object> map = new HashMap<>();
        String hql = "from SrmSupplier where id in (select supplier from PoOrders where id=:id)";
//        String hql = "select ms.* from PoOrders po,SrmSupplier ms where po.supplier=ms.id and po.id=:id";
        map.put("id",orderId);
        SrmSupplier srmSupplier = (SrmSupplier) srmSupplierDao.getByHQLWithNamedParams(hql,map);
        return srmSupplier;
    }

    //票款处理推送的接口
    public void purchaseInvoiceRejectSend(int loginNum,int operate,HashMap<String,Object> hashMap,Integer toUserId,String pass, String title, String content, String code){
        System.out.println("采购的票款处理或预付款推送开始:"+new Date());
        PoPaymentApplication poPaymentApplication = (PoPaymentApplication) hashMap.get("poPaymentApplication");
        if (poPaymentApplication!=null) {
            System.out.println("票款处理id：" + poPaymentApplication.getId() + " userId: " + toUserId);
        }
        User user = userDao.get(toUserId);  // 推送人
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,user,code);
        System.out.println("采购的票款处理或预付款推送结束:"+new Date());
    }

    @Override  // type 1-排除多次进行付款方式修改的付款复核的已批准数据  businessTypePayment-预付款的审批类型  approveStatusPayment-预付款的审批状态
    public List<Map<String,Object>> getPurchaseInvoices(Integer org,Integer toUser,Integer fromUser, String approveStatus,String billState,String payState,String approveStatusApplication,Integer type,Integer businessTypePayment,String approveStatusPayment,Integer... businessType) {
        List<Map<String,Object>> listMap = new ArrayList<>();
        if (businessTypePayment!=null) {
            List<Map<String, Object>> advancePayments = getAdvancePayments(org,toUser, fromUser, businessTypePayment, approveStatus, approveStatusPayment,type);
            if (advancePayments.size()>0) {
                listMap.addAll(advancePayments);
            }
        }
        if (businessType!=null) {
            List<Map<String, Object>> purchaseInvoiceList= getPurchaseInvoiceList(org, toUser, fromUser, approveStatus, billState, payState, approveStatusApplication, type, businessType);  //获取票据内容的
            if (purchaseInvoiceList.size()>0) {
                listMap.addAll(purchaseInvoiceList);
            }
        }
        return listMap;
    }

    private List<Map<String,Object>> getPurchaseInvoiceList(Integer org,Integer toUser,Integer fromUser, String approveStatus,String billState,String payState,String approveStatusApplication,Integer type,Integer... businessType){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> listMap = new ArrayList<>();
//        String hql = "from PoPaymentApplication where id in (select business from ApprovalProcess where org=:org";
        String hql = "select ppa.id,ap.businessGroup from PoPaymentApplication ppa, ApprovalProcess ap where ap.org=:org and ppa.id=ap.business";
        map.put("org",org);
        if (toUser!=null){
//            hql+=" and toUser=:toUser";
            hql+=" and ap.toUser=:toUser";
            map.put("toUser",toUser);
        }
        if (fromUser!=null){
            hql+=" and ap.fromUser=:fromUser";
            map.put("fromUser",fromUser);
        }
        if (!StringUtils.isEmpty(approveStatus)){
            hql+=" and ap.approveStatus=:approveStatus";
            map.put("approveStatus",approveStatus);
        }
        if (businessType!=null){
            hql+=" and ap.businessType in (:businessType)";
            map.put("businessType",businessType);
        }
        if (type!=null&&type==1) {   //复核审批人已批准查询的：排除掉多次进行修改付款方式后复核审批人审批通过后正在等待审批的数据
            hql += " and ap.businessGroup not in (select businessGroup from ApprovalProcess where org=:org and toUser=:toUser and approveStatus='1' and businessType=51)";
        }
//        hql+=")";   //子查询结尾的括号
        if (!StringUtils.isEmpty(billState)){
            hql+=" and ppa.billState=:billState";
            map.put("billState",billState);
        }
        if (!StringUtils.isEmpty(payState)){
            hql+=" and ppa.payState=:payState";
            map.put("payState",payState);
        }
        if (!StringUtils.isEmpty(approveStatusApplication)){   //总的状态
            if ("5".equals(approveStatusApplication)){
                hql+=" and ppa.approveStatus in ('1','4')";
            }else {
                hql += " and ppa.approveStatus=:approveStatusApplication";
                map.put("approveStatusApplication", approveStatusApplication);
            }
        }
        hql+=" group by ppa.id,ap.businessGroup";
        List<Object[]> objects = poPaymentApplicationDao.getListByHQLWithNamedParams(hql,map);
//        List<PoPaymentApplication> poPaymentApplicationList = poPaymentApplicationDao.getListByHQLWithNamedParams(hql,map);
//        for (PoPaymentApplication poPaymentApplication:poPaymentApplicationList) {
        for (Object[] ob:objects) {
            Integer applicationId = (Integer) ob[0];
            PoPaymentApplication poPaymentApplication = poPaymentApplicationDao.get(applicationId);
            Map<String,Object> map1 = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);
            if (map1.size()!=0) {
                String businessGroup = (String) ob[1];
                map1.put("businessGroup",businessGroup);
                listMap.add(map1);
            }
        }
        return listMap;
    }

    @Override
    public List<Map<String, Object>> getPurchaseInvoicesByState(Integer userId,Integer org,String approveStatus,String type, String billState, String payState,Integer billStateType) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PoPaymentApplication where org=:org";
        map.put("org",org);
        if (userId!=null){
            hql+=" and applicant=:applicant";  //申请人id
            map.put("applicant",userId);
        }
        if (!StringUtils.isEmpty(approveStatus)){
            if ("5".equals(approveStatus)){  //查询的状态待审批和待处理的
                hql+=" and approveStatus in ('1','4')";
            }else {
                hql += " and approveStatus=:approveStatus";
                map.put("approveStatus", approveStatus);
            }
        }
        if (!StringUtils.isEmpty(type)){
            if ("4".equals(type)){  //查询的状态待审批和待处理的
                hql+=" and type in ('1','2')";
            }else {
                hql += " and type=:type";
                map.put("type", type);
            }
        }
        if (!StringUtils.isEmpty(billState)){
            hql+=" and billState=:billState";
            map.put("billState",billState);
        }
        if (!StringUtils.isEmpty(payState)){
            if ("5".equals(payState)){
                hql += " and payState in ('2','3')";
            }else {
                hql += " and payState=:payState";
                map.put("payState", payState);
            }
        }
        if (billStateType!=null){
            if (1==billStateType){
                hql+=" and billState in ('2','3','5','7')";
            }
        }
        List<PoPaymentApplication> poPaymentApplicationList = poPaymentApplicationDao.getListByHQLWithNamedParams(hql,map);
        List<Map<String,Object>> listMap = new ArrayList<>();
        for (PoPaymentApplication poPaymentApplication:poPaymentApplicationList) {
            Map<String,Object> map1 = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);
            if (map1.size()>0) {
                listMap.add(map1);
            }
        }
        return listMap;
    }

    @Override
    public Map<String, Object> paymentOnlineApproval(User user, Integer approvalProcessId, String approveStatus,String reason,String approveSelect) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else {
            approvalProcess.setApproveStatus(approveStatus);
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setToUser(user.getUserID());
            approvalProcessDao.update(approvalProcess);

            PoPaymentApplication poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
            if ("2".equals(approveStatus)) {   //批准
                poPaymentApplication.setBillState("3");  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
                poPaymentApplication.setUpdateDate(new Date());
                poPaymentApplication.setUpdateName(user.getUserName());
                poPaymentApplication.setUpdator(user.getUserID());
                poPaymentApplicationDao.update(poPaymentApplication);

                User superUser = userService.getUserByRoleCode(user.getOid(), "super");  //默认审批人是超管【采购审批者】
                ApprovalProcess approvalProcess1 = new ApprovalProcess();
                approvalProcess1.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回
                approvalProcess1.setLevel(1);
                approvalProcess1.setToUser(superUser.getUserID());  //审批人id
                approvalProcess1.setToUserName("指定审批人");  //审批人总称
                approvalProcess1.setUserName(superUser.getUserName()); //审批人名称
                approvalProcess1.setCreateDate(new Date());
                approvalProcess1.setHandleTime(new Date());
                approvalProcess1.setOrg(user.getOid());
                approvalProcess1.setFromUser(approvalProcess.getFromUser());
                approvalProcess1.setAskName(approvalProcess.getAskName());
                approvalProcess1.setBusiness(poPaymentApplication.getId());
                approvalProcess1.setBusinessType(46);
                approvalProcessDao.save(approvalProcess1);

                HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                for (UserPopedom u : userPopedomList) {
                    //财务审批人待在线审核-1
                    this.purchaseInvoiceRejectSend(-1,-1,hashMap, u.getUserId(), "/paymentOnlineHandle",null,null,"purchaseBillFinanceApproval");
                    //财务审批人在线审核ok+1
                    this.purchaseInvoiceRejectSend(0,1,hashMap, u.getUserId(), "/paymentOnlineOKHandle",null,null,"purchaseBillFinanceApproval");
                }
                //采购审批者-采购部门的票据审核-待处理+1
                this.purchaseInvoiceRejectSend(1,1,hashMap, superUser.getUserID(), "/purchaseInvoiceHandle",null,null,"purchaseBillApproval");

            } else if ("3".equals(approveStatus)) {
                poPaymentApplication.setBillState("4");  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
                poPaymentApplication.setApproveStatus("3"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
                poPaymentApplication.setCashierRejectDesc(reason);  //出纳驳回理由描述
                poPaymentApplication.setCashierRejectReason(approveSelect);  //出纳驳回理由 数字拼接
                poPaymentApplication.setUpdateDate(new Date());
                poPaymentApplication.setUpdateName(user.getUserName());
                poPaymentApplication.setUpdator(user.getUserID());
                poPaymentApplicationDao.update(poPaymentApplication);

                approvalProcess.setReason(reason);
                approvalProcess.setApproveSelect(approveSelect); //1-照片不清楚，或信息被遮挡，以至于无法审核 2-无法入会计帐的票据较多 3-包含公司不允许报销的票据  4-票据内容与所录入的信息不一致  5-其他原因

                HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                for (UserPopedom u : userPopedomList) {
                    //出纳待在线审核-1
                    this.purchaseInvoiceRejectSend(-1,-1,hashMap, u.getUserId(), "/paymentOnlineHandle",null,null,"purchaseBillFinanceApproval");
                }
                //申请人-采购部门的票据审核-1
                this.purchaseInvoiceRejectSend(0, -1, hashMap, approvalProcess.getFromUser(), "/invoiceApplyHanle", null, null, "purchaseBillApply");//申请人
                //驳回给申请人发消息
                Integer applicationId = Integer.valueOf("35"+poPaymentApplication.getId().toString());  //id前的“35”是前端要求的，进行判断详情页面
                userSuspendMsgService.saveUserSuspendMsg(1, "您提交的票据已驳回了！", "操作时间 "+user.getUserName()+" "+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail",applicationId);  //给前端要查看详情的链接
            }

            map.put("content","操作成功");
        }
        return map;
    }

    @Override
    public Map<String, Object> purchaseOnlineApproval(User user, Integer approvalProcessId, String approveStatus,String reason) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else {
            approvalProcess.setApproveStatus(approveStatus);
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setToUser(user.getUserID());
            approvalProcessDao.update(approvalProcess);

            PoPaymentApplication poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
            if (poPaymentApplication!=null && !"3".equals(poPaymentApplication.getType())){    //类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
                if ("2".equals(approveStatus)){  //批准
                    poPaymentApplication.setBillState("5");  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
                    poPaymentApplication.setUpdateDate(new Date());
                    poPaymentApplication.setUpdateName(user.getUserName());
                    poPaymentApplication.setUpdator(user.getUserID());
                    poPaymentApplicationDao.update(poPaymentApplication);

                    //财务出纳-待线下审核的流程
                    ApprovalProcess process = new ApprovalProcess();
                    process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                    process.setLevel(1);
                    process.setToUserName("财务处理者");
                    process.setToMid("lp");  //报销受理模块
                    process.setCreateDate(new Date());
                    process.setOrg(user.getOid());
                    process.setUserName("财务处理者");
                    process.setFromUser(approvalProcess.getFromUser());
                    process.setAskName(approvalProcess.getAskName());
                    process.setHandleTime(new Date());
                    process.setBusiness(poPaymentApplication.getId());
                    process.setBusinessType(47);
                    approvalProcessDao.save(process);

                    HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //财务审批人在线审核ok-1
                        this.purchaseInvoiceRejectSend(0,-1,hashMap, u.getUserId(), "/paymentOnlineOKHandle",null,null,"purchaseBillFinanceApproval");
                        //财务审批人待线下审核+1
                        this.purchaseInvoiceRejectSend(1,1,hashMap, u.getUserId(), "/paymentOfflineHandle",null,null,"purchaseBillFinanceApproval");
                    }
                    //采购审批者-采购部门的票据审核-待处理-1
                    this.purchaseInvoiceRejectSend(-1,-1,hashMap, user.getUserID(), "/purchaseInvoiceHandle",null,null,"purchaseBillApproval");
                    //采购审批者-采购部门的票据审核-已批准+1
                    this.purchaseInvoiceRejectSend(0,1,hashMap, user.getUserID(), "/purchaseInvoiceApproved",null,null,"purchaseBillApproval");
                }else if ("3".equals(approveStatus)){      //驳回
                    poPaymentApplication.setBillState("6");  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
                    poPaymentApplication.setApproveStatus("3"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
                    poPaymentApplication.setBuyerRejectDesc(reason);
                    poPaymentApplication.setUpdateDate(new Date());
                    poPaymentApplication.setUpdateName(user.getUserName());
                    poPaymentApplication.setUpdator(user.getUserID());
                    poPaymentApplicationDao.update(poPaymentApplication);

                    approvalProcess.setReason(reason);

                    HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //财务审批人在线审核ok-1
                        this.purchaseInvoiceRejectSend(0,-1,hashMap, u.getUserId(), "/paymentOnlineOKHandle",null,null,"purchaseBillFinanceApproval");
                    }
                    //采购审批者-采购部门的票据审核-待处理-1
                    this.purchaseInvoiceRejectSend(-1,-1,hashMap, user.getUserID(), "/purchaseInvoiceHandle",null,null,"purchaseBillApproval");

                    //申请人-采购部门的票据审核-1
                    this.purchaseInvoiceRejectSend(0, -1, hashMap, approvalProcess.getFromUser(), "/invoiceApplyHanle", null, null, "purchaseBillApply");//申请人
                    //驳回给申请人发消息
                    Integer applicationId = Integer.valueOf("35"+poPaymentApplication.getId());  //id前的“35”是前端要求的，进行判断详情页面
                    userSuspendMsgService.saveUserSuspendMsg(1, "您提交的票据已驳回了！", "操作时间 "+user.getUserName()+" "+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), poPaymentApplication.getCreator(), "purchaseInvoiceDetail",applicationId);  //给前端要查看详情的链接

                }
                map.put("content","操作成功");

            }else {
                map.put("content","操作失败");
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> purchasePaymentApproval(User user, Integer approvalProcessId, String approveStatus,String reason,String approveSelect) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else {
            PoPaymentApplication poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
            if (poPaymentApplication!=null && "3".equals(poPaymentApplication.getType())){  //类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
                approvalProcess.setApproveStatus(approveStatus);
                approvalProcess.setUserName(user.getUserName());
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setToUser(user.getUserID());
                approvalProcessDao.update(approvalProcess);
                if ("2".equals(approveStatus)){  //批准
                    poPaymentApplication.setPayState("3");  //付款审批状态:0-暂存,1-录入,2-提交,3-采购审批通过,4-采购审批驳回
                    poPaymentApplication.setApproveStatus("4"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
                    poPaymentApplication.setUpdateDate(new Date());
                    poPaymentApplication.setUpdateName(user.getUserName());
                    poPaymentApplication.setUpdator(user.getUserID());
                    poPaymentApplicationDao.update(poPaymentApplication);

                    //付款审批者-待付款审批的流程
                    ApprovalItem approvalItem = new ApprovalItem();
                    if (poPaymentApplication.getPayInstance()!=null) {
                        approvalItem = roleService.getApprovalItemById(poPaymentApplication.getPayInstance());  //查询当前使用的付款审批(暂时默认一级)
                    }else {    //老数据的没有存这个id
                        approvalItem = roleService.getCurrentItem(user.getOid(), "paymentApproval");  //查询当前使用的付款审批(暂时默认一级)
                        poPaymentApplication.setPayInstance(approvalItem.getId());
                        if (poPaymentApplication.getAuditInstance()==null){   //复核的审批流程
                            ApprovalItem approvalItem1 = roleService.getApprovalItemByPreviousItem(approvalItem.getId());
                            poPaymentApplication.setAuditInstance(approvalItem1.getId());
                        }
                    }
                    HashMap<String, Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                    //付款审批者-待付款审批的流程
//                    ApprovalItem approvalItem  = roleService.getCurrentItem(user.getOid(),"paymentApproval");  //查询当前使用的付款审批(暂时默认一级)
                    if (1==approvalItem.getStatus()) {    //需要付款审批
                        ApprovalProcess process = new ApprovalProcess();
                        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process.setLevel(1);
                        process.setToUser(approvalItem.getApprovalFlowHashSet().get(0).getToUserId());  //审批人id
                        process.setToUserName(approvalItem.getApprovalFlowHashSet().get(0).getToUser());  //审批人总称
                        process.setUserName(approvalItem.getApprovalFlowHashSet().get(0).getUserName()); //审批人名称
                        process.setCreateDate(new Date());
                        process.setHandleTime(new Date());
                        process.setOrg(user.getOid());
                        process.setFromUser(approvalProcess.getFromUser());
                        process.setAskName(approvalProcess.getAskName());
                        process.setBusiness(poPaymentApplication.getId());
                        process.setBusinessType(49);
                        approvalProcessDao.save(process);

                        //付款审批者-采购部门的付款-待处理+1
                        this.purchaseInvoiceRejectSend(1, 1, hashMap, process.getToUser(), "/purchasePaymentHandle", null, null, "purchasePaymentApplyApproval");

                        List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                        for (UserPopedom u : userPopedomList) {
                            //财务审批人-采购部门的付款-待付款审批+1
                            this.purchaseInvoiceRejectSend(0, 1, hashMap, u.getUserId(), "/payApprovalFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                        }
                    }else {
                        poPaymentApplication.setApproveStatus("4"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理

                        //财务出纳-可付款的流程
                        ApprovalProcess process = new ApprovalProcess();
                        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process.setLevel(1);
                        process.setToUserName("财务处理者");
                        process.setToMid("lp");  //报销受理模块
                        process.setCreateDate(new Date());
                        process.setOrg(user.getOid());
                        process.setUserName("财务处理者");
                        process.setFromUser(approvalProcess.getFromUser());
                        process.setAskName(approvalProcess.getAskName());
                        process.setHandleTime(new Date());
                        process.setBusiness(poPaymentApplication.getId());
                        process.setBusinessType(50);
                        approvalProcessDao.save(process);

                        List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                        for (UserPopedom u : userPopedomList) {
                            //财务审批人可付款+1
                            this.purchaseInvoiceRejectSend(1,1,hashMap, u.getUserId(), "/payableFinanceHandle",null,null,"purchasePaymentFinanceApproval");
                        }
                    }

                    //采购审批者-采购部门的付款-待处理-1
                    this.purchaseInvoiceRejectSend(-1, -1, hashMap, user.getUserID(), "/purchasePaymentHandle", null, null, "purchasePaymentApproval");
                    //采购审批者-采购部门的付款-已批准
                    this.purchaseInvoiceRejectSend(0, 1, hashMap, user.getUserID(), "/purchasePaymentApproved", null, null, "purchasePaymentApproval");

                }else if ("3".equals(approveStatus)){      //驳回
                    poPaymentApplication.setPayState("4");  //付款审批状态:0-暂存,1-录入,2-提交,3-采购审批通过,4-采购审批驳回
                    poPaymentApplication.setApproveStatus("3"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
                    poPaymentApplication.setPayRejectDesc(reason);
                    poPaymentApplication.setPayRejectReason(approveSelect);
                    poPaymentApplication.setUpdateDate(new Date());
                    poPaymentApplication.setUpdateName(user.getUserName());
                    poPaymentApplication.setUpdator(user.getUserID());
                    poPaymentApplicationDao.update(poPaymentApplication);

                    approvalProcess.setReason(reason);
                    approvalProcess.setApproveSelect(approveSelect);

                    HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                    //采购审批者-采购部门的付款-待处理-1
                    this.purchaseInvoiceRejectSend(-1,-1,hashMap, user.getUserID(), "/purchasePaymentHandle",null,null,"purchasePaymentApproval");
                    //申请人-采购部门的付款-1
                    this.purchaseInvoiceRejectSend(0, -1, hashMap, approvalProcess.getFromUser(), "/paymentApplyHandle", null, null, "purchasePaymentApply");//申请人
                    //驳回给申请人发消息
                    Integer applicationId = Integer.valueOf("35"+poPaymentApplication.getId());  //id前的“35”是前端要求的，进行判断详情页面
                    userSuspendMsgService.saveUserSuspendMsg(1, "您提交的付款申请已驳回了！", "操作时间 "+user.getUserName()+" "+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail",applicationId);  //给前端要查看详情的链接

                }
                map.put("content","操作成功");
            }else {
                map.put("content","操作失败");
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> purchaseOfflineApproval(User user, Integer approvalProcessId, String approveStatus) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else {
            approvalProcess.setApproveStatus(approveStatus);
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setToUser(user.getUserID());
            approvalProcessDao.update(approvalProcess);

            PoPaymentApplication poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
            if ("2".equals(approveStatus)){  //批准
                poPaymentApplication.setBillState("7"); //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
                poPaymentApplication.setUpdateDate(new Date());
                poPaymentApplication.setUpdateName(user.getUserName());
                poPaymentApplication.setUpdator(user.getUserID());

                HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                for (UserPopedom u : userPopedomList) {
                    //财务审批人待线下审核-1
                    this.purchaseInvoiceRejectSend(-1,-1,hashMap, u.getUserId(), "/paymentOfflineOKHandle",null,null,"purchaseBillFinanceApproval");
                }
                if ("1".equals(poPaymentApplication.getType())){  //1-仅提交票据,不提交付款申请;
                    poPaymentApplication.setApproveStatus("2");
                    poPaymentApplicationDao.update(poPaymentApplication);
                    PoOrders poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
                    this.ordersCount(1,poPaymentApplication,poOrders,null);  //计算数量与金额的

                    this.commonEnd(poPaymentApplication,poPaymentApplication.getOrders(),null,null,3);  //推送内容
                    //申请人发消息
                    Integer applicationId = Integer.valueOf("35"+poPaymentApplication.getId());  //id前的“35”是前端要求的，进行判断详情页面
                    userSuspendMsgService.saveUserSuspendMsg(1, "您提交的采购报销已处理完成", "操作时间 "+user.getUserName()+" "+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail",applicationId);  //给前端要查看详情的链接
                }else {
                    poPaymentApplication.setApproveStatus("4");
                    poPaymentApplicationDao.update(poPaymentApplication);

                    //付款审批者-待付款审批的流程
                    ApprovalItem approvalItem = new ApprovalItem();
                    if (poPaymentApplication.getPayInstance()!=null) {
                        approvalItem = roleService.getApprovalItemById(poPaymentApplication.getPayInstance());  //查询当前使用的付款审批(暂时默认一级)
                    }else {    //老数据的没有存这个id
                        approvalItem = roleService.getCurrentItem(user.getOid(), "paymentApproval");  //查询当前使用的付款审批(暂时默认一级)
                        poPaymentApplication.setPayInstance(approvalItem.getId());
                        if (poPaymentApplication.getAuditInstance()==null){   //复核的审批流程
                            ApprovalItem approvalItem1 = roleService.getApprovalItemByPreviousItem(approvalItem.getId());
                            poPaymentApplication.setAuditInstance(approvalItem1.getId());
                        }
                    }
                    if (1==approvalItem.getStatus()) {    //需要付款审批
                        ApprovalProcess process = new ApprovalProcess();
                        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process.setLevel(1);
                        process.setToUser(approvalItem.getApprovalFlowHashSet().get(0).getToUserId());  //审批人id
                        process.setToUserName(approvalItem.getApprovalFlowHashSet().get(0).getToUser());  //审批人总称
                        process.setUserName(approvalItem.getApprovalFlowHashSet().get(0).getUserName()); //审批人名称
                        process.setCreateDate(new Date());
                        process.setHandleTime(new Date());
                        process.setOrg(user.getOid());
                        process.setFromUser(approvalProcess.getFromUser());
                        process.setAskName(approvalProcess.getAskName());
                        process.setBusiness(poPaymentApplication.getId());
                        process.setBusinessType(49);
                        approvalProcessDao.save(process);

                        //付款审批者-采购部门的付款-待处理+1
                        this.purchaseInvoiceRejectSend(1,1,hashMap, process.getToUser(), "/paymentApprovalHandle",null,null,"purchasePaymentApplyApproval");
                        //财务审批人待付款审批
                        for (UserPopedom u : userPopedomList) {
                            //财务审批人-采购部门的付款-待付款审批+1
                            this.purchaseInvoiceRejectSend(0,1,hashMap, u.getUserId(), "/payApprovalFinanceHandle",null,null,"purchasePaymentFinanceApproval");
                        }

                    }else {    //无需付款审批的，直接进入可付款

                        poPaymentApplication.setApproveStatus("4"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理

                        //财务出纳-可付款的流程
                        ApprovalProcess process = new ApprovalProcess();
                        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process.setLevel(1);
                        process.setToUserName("财务处理者");
                        process.setToMid("lp");  //报销受理模块
                        process.setCreateDate(new Date());
                        process.setOrg(user.getOid());
                        process.setUserName("财务处理者");
                        process.setFromUser(approvalProcess.getFromUser());
                        process.setAskName(approvalProcess.getAskName());
                        process.setHandleTime(new Date());
                        process.setBusiness(poPaymentApplication.getId());
                        process.setBusinessType(50);
                        approvalProcessDao.save(process);

//                        HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication);  //推送的内容
//                        List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                        for (UserPopedom u : userPopedomList) {
                            //财务审批人可付款+1
                            this.purchaseInvoiceRejectSend(1,1,hashMap, u.getUserId(), "/payableFinanceHandle",null,null,"purchasePaymentFinanceApproval");
                        }
                    }
                    //申请人发消息
                    Integer applicationId = Integer.valueOf("35"+poPaymentApplication.getId());  //id前的“35”是前端要求的，进行判断详情页面
                    userSuspendMsgService.saveUserSuspendMsg(1, "您提交的票据已获审批通过！", "操作时间 "+user.getUserName()+" "+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail",applicationId);  //给前端要查看详情的链接
                }

            }else if ("3".equals(approveStatus)){      //驳回
                poPaymentApplication.setBillState("8");  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回
                poPaymentApplication.setApproveStatus("3"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
                poPaymentApplication.setPayRejectDesc("实际票据与在线审核通过的不一致。");
//                poPaymentApplication.setPayRejectReason(approveSelect);
                poPaymentApplication.setUpdateDate(new Date());
                poPaymentApplication.setUpdateName(user.getUserName());
                poPaymentApplication.setUpdator(user.getUserID());
                poPaymentApplicationDao.update(poPaymentApplication);

                approvalProcess.setReason("实际票据与在线审核通过的不一致。");
//                approvalProcess.setApproveSelect(approveSelect);

                HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                for (UserPopedom u : userPopedomList) {
                    //财务审批人待线下审核-1
                    this.purchaseInvoiceRejectSend(-1,-1,hashMap, u.getUserId(), "/paymentOfflineOKHandle",null,null,"purchaseBillFinanceApproval");
                }
                //申请人-采购部门的票据审核-1
                this.purchaseInvoiceRejectSend(0, -1, hashMap, approvalProcess.getFromUser(), "/invoiceApplyHandle", null, null, "purchaseBillApply");//申请人
                //驳回给申请人发消息
                Integer applicationId = Integer.valueOf("35"+poPaymentApplication.getId());  //id前的“35”是前端要求的，进行判断详情页面
                userSuspendMsgService.saveUserSuspendMsg(1, "您提交的票据已驳回了！", "操作时间 "+user.getUserName()+" "+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail",applicationId);  //给前端要查看详情的链接
            }
            map.put("content","操作成功");
        }
        return map;
    }

    //往订单与订单明细中记录金额和数量  type 1-票据进来的 2-预付款的
    private void ordersCount(Integer type,PoPaymentApplication poPaymentApplication,PoOrders poOrders,PoOrdersPrepayment poOrdersPrepayment){
        if (type!=null&&1==type) {  //票据需要统计的
            if ("1".equals(poPaymentApplication.getType())) {  //类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
//            PoOrders poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
                BigDecimal invoicedAmount = poOrders.getInvoicedAmount();  //票据已提交金额
                poOrders.setInvoicedAmount(invoicedAmount == null ? poPaymentApplication.getAmount() : invoicedAmount.add(poPaymentApplication.getAmount()));

                List<PoPaymentInvoiceItem> poPaymentInvoiceItems = getInvoicesItemById(poPaymentApplication.getId(), null);
                for (PoPaymentInvoiceItem poPaymentInvoiceItem : poPaymentInvoiceItems) {
                    if (poPaymentInvoiceItem.getOrdersItem() != null) {
                        PoOrdersItem poOrdersItem = poOrdersItemDao.get(poPaymentInvoiceItem.getOrdersItem());
                        if (poOrdersItem != null) {
                            BigDecimal ordersItemAmount = poOrdersItem.getInvoicedAmount(); //票据已提交金额
                            poOrdersItem.setInvoicedAmount(ordersItemAmount == null ? poPaymentInvoiceItem.getAmount() : ordersItemAmount.add(poPaymentInvoiceItem.getAmount()));

                            //票据已提交数量
                            BigDecimal orderItemQuantity = poOrdersItem.getInvoicedQuantity() == null ? new BigDecimal(0) : poOrdersItem.getInvoicedQuantity();
                            BigDecimal paymentInvoiceItemQuantity = poPaymentInvoiceItem.getItemQuantity() == null ? new BigDecimal(0) : new BigDecimal(poPaymentInvoiceItem.getItemQuantity());
                            BigDecimal proportion = orderItemQuantity.add(paymentInvoiceItemQuantity);  //原来的数量不为空累加算比例
                            poOrders.setInvoicedQuantity(proportion);

                        }
                    }
                }
                poOrdersDao.update(poOrders);
            } else {      //类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
//            PoOrders poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
                BigDecimal payedAmount = poOrders.getPayedAmount();  //已付款金额
                poOrders.setPayedAmount(payedAmount == null ? poPaymentApplication.getAmount() : payedAmount.add(poPaymentApplication.getAmount()));

                List<PoPaymentInvoiceItem> poPaymentInvoiceItems = getInvoicesItemById(poPaymentApplication.getId(), null);
                for (PoPaymentInvoiceItem poPaymentInvoiceItem : poPaymentInvoiceItems) {
                    if (poPaymentInvoiceItem.getOrdersItem() != null) {
                        PoOrdersItem poOrdersItem = poOrdersItemDao.get(poPaymentInvoiceItem.getOrdersItem());
                        if (poOrdersItem != null) {
                            BigDecimal ordersItemPayedAmount = poOrdersItem.getPayedAmount(); //已付款金额
                            poOrdersItem.setInvoicedAmount(ordersItemPayedAmount == null ? poPaymentInvoiceItem.getAmount() : ordersItemPayedAmount.add(poPaymentInvoiceItem.getAmount()));

                            //已付款数量
                            BigDecimal payedQuantity = poOrdersItem.getPayedQuantity() == null ? new BigDecimal(0) : poOrdersItem.getPayedQuantity();
                            Double itemQuantity = poPaymentInvoiceItem.getItemQuantity() == null ? new Double(0) : poPaymentInvoiceItem.getItemQuantity();
                            BigDecimal proportion = payedQuantity.equals(new BigDecimal(0)) ? new BigDecimal(itemQuantity) : payedQuantity.add(new BigDecimal(itemQuantity));  //原来的金额不为空累加算比例
                            poOrders.setPayedQuantity(proportion);

                        }
                    }
                }
                poOrdersDao.update(poOrders);
            }
        }else if (type!=null&&2==type){   //2==预付款需要统计的
            BigDecimal payedAmount = poOrders.getPayedAmount();  //已付款金额
            poOrders.setPayedAmount(payedAmount == null ? poOrdersPrepayment.getPlanAmount() : payedAmount.add(poOrdersPrepayment.getPlanAmount()));
            poOrdersDao.update(poOrders);
        }
    }

    @Override
    public Map<String, Object> paymentApprovalApproval(User user, Integer approvalProcessId) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else {
            approvalProcess.setApproveStatus("2");  //只有批准
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setToUser(user.getUserID());
            approvalProcessDao.update(approvalProcess);

            PoPaymentApplication poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
//            poPaymentApplication.setPayState("3");  //付款审批状态:0-暂存,1-录入,2-提交,3-采购审批通过,4-采购审批驳回
            poPaymentApplication.setApproveStatus("4"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
            poPaymentApplication.setUpdateDate(new Date());
            poPaymentApplication.setUpdateName(user.getUserName());
            poPaymentApplication.setUpdator(user.getUserID());
            poPaymentApplicationDao.update(poPaymentApplication);

            //财务出纳-可付款的流程
            ApprovalProcess process = new ApprovalProcess();
            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
            process.setLevel(1);
            process.setToUserName("财务处理者");
            process.setToMid("lp");  //报销受理模块
            process.setCreateDate(new Date());
            process.setOrg(user.getOid());
            process.setUserName("财务处理者");
            process.setFromUser(approvalProcess.getFromUser());
            process.setAskName(approvalProcess.getAskName());
            process.setHandleTime(new Date());
            process.setBusiness(poPaymentApplication.getId());
            process.setBusinessType(50);
            approvalProcessDao.save(process);

            HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
            for (UserPopedom u : userPopedomList) {
                //财务审批人可付款+1
                this.purchaseInvoiceRejectSend(1,1,hashMap, u.getUserId(), "/payableFinanceHandle",null,null,"purchasePaymentFinanceApproval");

                //财务审批人待付款审批-1
                this.purchaseInvoiceRejectSend(0,-1,hashMap, u.getUserId(), "/payApprovalFinanceHandle",null,null,"purchasePaymentFinanceApproval");
            }
            //付款审批者-采购部门的付款-待处理-1
            this.purchaseInvoiceRejectSend(-1,-1,hashMap, user.getUserID(), "/paymentApprovalHandle",null,null,"purchasePaymentApplyApproval");
            //付款审批者-采购部门的付款-已批准
            this.purchaseInvoiceRejectSend(0,1,hashMap, user.getUserID(), "/paymentApprovalApproved",null,null,"purchasePaymentApplyApproval");
            map.put("content","操作成功");
        }
        return map;
    }

    /**
     * 出纳-采购部门的付款-可付款审批(录入付款方式)
     * @param user
     * @param approvalProcessId 审批流程id
     * @param method 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐， 6-转帐支票(内部的，只在此用)
     * @param planDate  计划付款时间
     * @param factDate 实际付款时间
     * @param accountId 账户id
     * @param summary 摘要
     * @param invoiceId  票据id
     * @param expireDate 支票到期日
     * @param receiveDate 接收日期
     * @param receiver 接收经手人
     * @param operator 支付经手人
     * @param operatorName 接收经手人（收款单位经手人）
     * @return
     */
    @Override
    public Map<String, Object> payableFinanceApproval(User user, Integer approvalProcessId, String method, Date planDate, Date factDate,
              Integer accountId,String summary,Integer invoiceId,String expireDate,String receiveDate,String receiver,String operator,String oppositeCorp,String operatorName,String factAmount) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else {
            String businessTypeF = "procurement";  //付款方式的  procurement-票款处理的
            if (64==approvalProcess.getBusinessType()){
                businessTypeF = "prepayment";  //预付款的
            }

            BigDecimal balance = new BigDecimal(0);
            FinanceAccount financeAccount = new FinanceAccount();
            PoPaymentApplication poPaymentApplication = new PoPaymentApplication();
            PoOrdersPrepayment poOrdersPrepayment = new PoOrdersPrepayment();
            PoOrders poOrders = new PoOrders();
            BigDecimal amount = new BigDecimal(0);
            BigDecimal billAmount = new BigDecimal(factAmount);
            Integer businessTypeR = null;  //待复核审批流程
            String source = "5";  //5-票款处理(1.169采购的票款处理)
            Integer approvalItemReview = null;  //复核审批流程的id
            BigDecimal difference = BigDecimal.ZERO; //差额(负数代表少付)
            BigDecimal lockedAmount = BigDecimal.ZERO; //锁定(正在申批中)金额
            BigDecimal paidAmount = BigDecimal.ZERO;  //已付金额
            if (50 == approvalProcess.getBusinessType()) {
                poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
                amount = poPaymentApplication.getAmount();
                poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
//                    businessTypeF = "procurement";  //采购的票款处理
                businessTypeR = 51;
                approvalItemReview = poPaymentApplication.getAuditInstance();

                lockedAmount = poPaymentApplication.getLockedAmount() != null ? poPaymentApplication.getLockedAmount().add(new BigDecimal(factAmount)) : new BigDecimal(factAmount);
                paidAmount = poPaymentApplication.getPaidAmount() != null ? poPaymentApplication.getPaidAmount() : new BigDecimal(0);
                difference = lockedAmount.add(paidAmount).subtract(poPaymentApplication.getAmount());  //差额

            } else if (64 == approvalProcess.getBusinessType()) {
                poOrdersPrepayment = poOrdersPrepaymentDao.get(approvalProcess.getBusiness().longValue());
                if (poOrdersPrepayment != null) {
                    approvalItemReview = poOrdersPrepayment.getAuditInstance();  //复核审批流程的id
                }
                poOrders = poOrdersDao.get(poOrdersPrepayment.getOrders());
                amount = poOrdersPrepayment.getPlanAmount();
//                    businessTypeF = "prepayment";  //预付款
                businessTypeR = 65;  //待复核审批流程
                source = "7";   //7-预付款(1.229采购之预付款)

                lockedAmount = poOrdersPrepayment.getLockedAmount() != null ? poOrdersPrepayment.getLockedAmount().add(new BigDecimal(factAmount)) : new BigDecimal(factAmount);
                paidAmount = poOrdersPrepayment.getPaidAmount() != null ? poOrdersPrepayment.getPaidAmount() : new BigDecimal(0);
                difference = lockedAmount.add(paidAmount).subtract(poOrdersPrepayment.getPlanAmount());  //差额
           }

            if (accountId != null) {
                financeAccount = financeAccountDao.get(accountId);

                //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
//                BigDecimal amount = new BigDecimal(poPaymentApplication.getAmount().toString());
                BigDecimal balance1 = new BigDecimal(financeAccount.getBalance().toString());
                balance = balance1.subtract(new BigDecimal(factAmount));
            }
            Integer num = financeCommonService.getFinancePaymentNum(approvalProcess.getBusiness(),"10",businessTypeF);  //待复核的付款笔数
            if (num>0&&difference.compareTo(BigDecimal.ZERO)>0&&("3".equals(method)||"4".equals(method))){  //待复核中有正在进行的且有多支付的，则不能录入承兑汇票和外部的转账支票
                String content = "操作失败！还有“待复核的付款”时，不支持选将导致多支付的转账支票！";
                if("4".equals(method)){
                    content = "操作失败！还有“待复核的付款”时，不支持选将导致多支付的承兑汇票！";
                }
                map.put("content", content);
            }else if (financeAccount.getId() != null && financeAccount.getAccountStatus() == 0) {
                map.put("content", "此账户已冻结");
            } else if (balance.compareTo(new BigDecimal(0)) < 0) {
                if ("1".equals(method)) {    //现金
                    map.put("content", "系统中现金余额不足，请确认。");
                } else if ("5".equals(method) || "6".equals(method)) {    //银行转账
                    map.put("content", "系统中该账户的余额不足!");
                }
            } else {
                if ("5".equals(source)) {
                    poPaymentApplication.setLockedAmount(lockedAmount); //锁定(正在申批中)金额
                    poPaymentApplication.setPaidAmount(paidAmount); //'已付金额'
                    poPaymentApplication.setBalanceAmount(difference);  //差额(负数代表少付)
                    if (difference.compareTo(BigDecimal.ZERO) < 0) {
                        poPaymentApplication.setPayStatus(1);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    }else {
                        poPaymentApplication.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    }
                    poPaymentApplicationDao.update(poPaymentApplication);
                }else {
                    poOrdersPrepayment.setLockedAmount(lockedAmount); //锁定(正在申批中)金额
                    poOrdersPrepayment.setPaidAmount(paidAmount); //'已付金额'
                    poOrdersPrepayment.setBalanceAmount(difference);  //差额(负数代表少付)
                    if (difference.compareTo(BigDecimal.ZERO) < 0) {
                        poOrdersPrepayment.setPayStatus(1);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    }else {
                        poPaymentApplication.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    }
                    poOrdersPrepaymentDao.update(poOrdersPrepayment);
                }
                if (StringUtils.isEmpty(oppositeCorp)) {  //收款单位
                    SrmSupplier srmSupplier = getSupplier(poOrders.getId());  //供应商信息
                    oppositeCorp = srmSupplier.getFullName();
                }
                //新增上的可付款方式
                FinancePayment financePayment = financePaymentService.addFinancePayment(approvalProcess.getBusiness(), businessTypeF, amount, new BigDecimal(factAmount), new BigDecimal(factAmount), user, method, planDate, factDate, accountId, null, summary);

                approvalProcess.setApproveStatus("2");  //只有批准
                approvalProcess.setUserName(user.getUserName());
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setToUser(user.getUserID());
                approvalProcess.setBusinessGroup(financePayment.getId().toString());
                approvalProcessDao.update(approvalProcess);

                if (difference.compareTo(BigDecimal.ZERO) < 0) {   //差额如果为负时，说明还没有可付款完成了，需要新增一条审批流程
                    ApprovalProcess process1 = new ApprovalProcess();
                    BeanUtils.copyPropertiesIgnoreNull(approvalProcess, process1);
                    process1.setCreateDate(new Date());
                    process1.setId(null);
                    process1.setBusinessGroup(null);
                    process1.setApproveStatus("1");  //待处理
                    process1.setUserName("财务处理者");
                    process1.setHandleTime(new Date());
                    process1.setToUser(null);
                    approvalProcessDao.save(process1);
                }else if (difference.compareTo(BigDecimal.ZERO)>0){
                    financePayment.setStatus("A");  //状态:0-撤回,1-待申请,2-申请,3-申请通过,4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成,A-付款完成且多付，会生成借款
                }

                if ("1".equals(method)) {   //现金直接进账
                    map = cashPurchase(user, poPaymentApplication, poOrders, poOrdersPrepayment,null, new BigDecimal(factAmount), billAmount, Integer.parseInt(approvalProcess.getBusinessGroup()), source, method, accountId, summary, factDate, financePayment);  //现金进账处理

                    HashMap<String, Object> hashMap = rejectSendType(poPaymentApplication, poOrders, null, poOrdersPrepayment);  //推送的内容
                    if (difference.compareTo(BigDecimal.ZERO) >= 0) {  //如果未支付完的，可付款的就不减数据与角标
                        List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                        for (UserPopedom u : userPopedomList) {
                            //财务审批人可付款-1
                            this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/payableFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                        }
                    }

                    //申请人发消息
                    if (50 == approvalProcess.getBusinessType()) {
                        if (difference.compareTo(BigDecimal.ZERO) >= 0) {
                            //申请人-采购部门的票据审核-1
                            this.purchaseInvoiceRejectSend(0, -1, hashMap, approvalProcess.getFromUser(), "/invoiceApplyHandle", null, null, "purchaseBillApply");//申请人
                        }
                        Integer applicationId = Integer.valueOf("36"+financePayment.getId());  //id前的“36”是前端用的，进行判断详情页面，票据有付款方式的
                        if ("2".equals(poPaymentApplication.getType())) {    //2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
                            userSuspendMsgService.saveUserSuspendMsg(1, "您提交的采购报销已处理完成", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail", applicationId);  //给前端要查看详情的链接
                        } else {    //3-仅提交付款申请,不录入票据
                            userSuspendMsgService.saveUserSuspendMsg(1, "您提交的付款已获审批通过！", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail", applicationId);  //给前端要查看详情的链接
                        }
                    } else if (64 == approvalProcess.getBusinessType()) {
                        if (difference.compareTo(BigDecimal.ZERO) >= 0) {
                            //申请人-采购部门的预付款申请-1
                            this.purchaseInvoiceRejectSend(-1, -1, hashMap, approvalProcess.getFromUser(), "/advancePaymentApplyHandle", null, null, "purchaseAdvancePayApply");//申请人
                        }
                        Integer poOrdersPayment = Integer.valueOf("38"+financePayment.getId()); //id前的“38”是前端要求的，进行判断详情页面，预付款有付款方式的
                        userSuspendMsgService.saveUserSuspendMsg(1, "您提交的预付款已获审批通过！", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "advancePaymentDetail", poOrdersPayment);  //给前端要查看详情的链接

                    }
                } else {   //其他付款方式的需要进入待复核
                    if (!"5".equals(method)) {  //银行转账的不用走此流程
                        map = invoicePurchase(user, new BigDecimal(factAmount), billAmount, approvalProcess.getBusiness(), source, financePayment, method, accountId, summary, invoiceId, expireDate, receiveDate, receiver, operator, oppositeCorp);
                    }

                    ApprovalItem approvalItem = new ApprovalItem();
                    if (approvalItemReview != null) {   //复核的审批流程
                        approvalItem = roleService.getApprovalItemById(approvalItemReview);
                    } else {
                        approvalItem = roleService.getCurrentItem(user.getOid(), "paymentAudit");  //原来的可能没有，查当前的复核流程
                        if (poPaymentApplication != null && poPaymentApplication.getId() != null) {
                            financePayment.setAuditInstance(approvalItem.getId());
                            financePayment.setAuditState("2");//付款复核状态:0-暂存,1-录入,2-提交,3-复核通过,4-复核驳回
                        }
                    }
                    if (1 == approvalItem.getStatus()) {  //需要付款复核的
                        //待复核的流程
                        User user1 = userService.getUserByRoleCode(user.getOid(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                        ApprovalProcess process = new ApprovalProcess();
                        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process.setLevel(1);
                        process.setToUser(user1.getUserID());  //审批人id
                        process.setToUserName(approvalProcess.getToUserName());  //审批人总称
                        process.setUserName(user1.getUserName()); //审批人名称
                        process.setCreateDate(new Date());
                        process.setOrg(user.getOid());
                        process.setFromUser(approvalProcess.getFromUser());
                        process.setAskName(approvalProcess.getAskName());
                        process.setBusiness(approvalProcess.getBusiness());
                        process.setBusinessType(businessTypeR);
                        process.setHandleTime(new Date());
                        process.setBusinessGroup(approvalProcess.getBusinessGroup());
                        approvalProcessDao.save(process);  //结束

                        HashMap<String, Object> hashMap = rejectSendType(poPaymentApplication, null, poOrders.getId(), poOrdersPrepayment);  //推送的内容
                        List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                        for (UserPopedom u : userPopedomList) {
                            if (difference.compareTo(BigDecimal.ZERO) >= 0) {  //如果未支付完的，可付款的就不减数据与角标
                                //财务审批人可付款-1
                                this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/payableFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                            }
                            //财务审批人待复核+1
                            this.purchaseInvoiceRejectSend(0, 1, hashMap, u.getUserId(), "/reviewedApprovalHandle", null, null, "purchasePaymentFinanceApproval");
                        }
                        //复核审批者-待复核+1
                        this.purchaseInvoiceRejectSend(1, 1, hashMap, user1.getUserID(), "/payableFinanceHandle", null, null, "purchaseFinanceReimburse");
                    } else {     //无需付款复核的
                        this.financeCommonContent(poPaymentApplication, poOrders, poOrdersPrepayment, financePayment, new BigDecimal(factAmount), billAmount, source, user, approvalProcess, map, factDate, summary, operatorName, 2); //进入财务的内容
                    }
                }
                map.put("content", "操作成功");
            }
        }
        return map;
    }

    @Override
    public Map<String,Object> cashPurchase(User user, PoPaymentApplication poPaymentApplication, PoOrders poOrders, PoOrdersPrepayment poOrdersPrepayment, LoanBiz loanBiz,
         BigDecimal amount, BigDecimal billAmount, Integer business, String source, String method, Integer accountId, String summary, Date factDate, FinancePayment financePayment){
        Map<String,Object> map = new HashMap<>();
        FinanceAccount financeAccount = new FinanceAccount();
        if ("1".equals(method)){
            financeAccount = accountService.getFinanceAccountByOidAndType(user.getOid(), 1);//  现金/备用金
        }else {
            financeAccount = financeAccountDao.get(accountId);
        }
        //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
        //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
        BigDecimal balance1 = new BigDecimal(financeAccount.getBalance().toString());
        BigDecimal balance = balance1.subtract(amount);
        if (balance.compareTo(new BigDecimal(0))<0){
            map.put("content", "系统中现金余额不足，请确认。");
        }else{
            Integer loanStatus = 1;  //是否有借款(生成借款的信息) 1-无，2-有
//            Integer loanType = 1;  //类型:1-货款(票据处理的),2-预付款,3-多收来的款(1.233差额处理2)
            Byte bizType = LoanBizService.LoanBizBizType.procure.getIndex(); //业务类型:1-采购,3-报销,4-销售,5-支付
            Byte loanType = LoanBizService.LoanBizType.procurement.getIndex();//类型1-采购货款(默认的),2-采购预付款,3-报销多付款,4-销售多收款,:5-支付多付款

            Integer paymentApply = null;
            Integer ordersPrepayment = null;
            BigDecimal loanAmount = BigDecimal.ZERO;
            String tracePath = null;//追踪路径(用于存来回多付款ID,以逗号分隔)
            Integer srmSupplier = null;
            String srmSupplierName = null;
            Integer origBiz = null;
            if (poPaymentApplication!=null&&poPaymentApplication.getId()!=null) {
                paymentApply = poPaymentApplication.getId();

                poPaymentApplication.setLockedAmount(poPaymentApplication.getLockedAmount().subtract(amount)); //锁定(正在申批中)金额
                poPaymentApplication.setPaidAmount(poPaymentApplication.getPaidAmount().add(amount));  //已付金额
                poPaymentApplication.setPayNum(poPaymentApplication.getPayNum()!=null?poPaymentApplication.getPayNum()+1:1); //支付笔数
                if (poPaymentApplication.getPaidAmount().compareTo(poPaymentApplication.getAmount())>=0){
                    poPaymentApplication.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    if (poPaymentApplication.getPaidAmount().compareTo(poPaymentApplication.getAmount())>0) {
                        loanAmount = poPaymentApplication.getPaidAmount().subtract(poPaymentApplication.getAmount());
                        poPaymentApplication.setLoanAmount(loanAmount);  //借款金额
                        loanStatus = 2;
                    }
                    poPaymentApplication.setApproveStatus("2");
                }else {
                    poPaymentApplication.setPayStatus(1);  //支付现状:0-未支付,1-部分支付,2-支付完成
                }
                poPaymentApplicationDao.update(poPaymentApplication);
                this.ordersCount(1,poPaymentApplication,poOrders,poOrdersPrepayment);  //计算金额和数量

                if (poOrders!=null&&poOrders.getId()!=null){
                    SrmSupplier srmSupplier1 = srmSupplierDao.get(poOrders.getSupplier().intValue());
                    srmSupplier = srmSupplier1.getId();
                    srmSupplierName = srmSupplier1.getName();
                }
            }else if (poOrdersPrepayment!=null&&poOrdersPrepayment.getId()!=null){
//                loanType=2;
                loanType=LoanBizService.LoanBizType.prepayment.getIndex();
                ordersPrepayment=poOrdersPrepayment.getId().intValue();

                poOrdersPrepayment.setFactDate(factDate);
                poOrdersPrepayment.setUpdateDate(new Date());
                poOrdersPrepayment.setUpdateName(user.getUserName());
                poOrdersPrepayment.setUpdator(user.getUserID().longValue());

                poOrdersPrepayment.setLockedAmount(poOrdersPrepayment.getLockedAmount().subtract(amount)); //锁定(正在申批中)金额
                poOrdersPrepayment.setPaidAmount(poOrdersPrepayment.getPaidAmount().add(amount));  //已付金额
                poOrdersPrepayment.setPayNum(poOrdersPrepayment.getPayNum()!=null?poOrdersPrepayment.getPayNum()+1:1); //支付笔数
                if (poOrdersPrepayment.getPaidAmount().compareTo(poOrdersPrepayment.getPlanAmount())>=0){
                    poOrdersPrepayment.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    if (poOrdersPrepayment.getPaidAmount().compareTo(poOrdersPrepayment.getPlanAmount())>0) {
                        loanAmount = poOrdersPrepayment.getPaidAmount().subtract(poOrdersPrepayment.getPlanAmount());
                        poOrdersPrepayment.setLoanAmount(loanAmount);  //借款金额
                        loanStatus = 2;
                    }
                    poOrdersPrepayment.setPrepaymentStatus("2"); //预付款审批状态:0-未申请,1-申请提交(正在走财务-出纳审批的),2-通过审核,3-否决审核,4-撤回

                }else {
                    poOrdersPrepayment.setPayStatus(1);  //支付现状:0-未支付,1-部分支付,2-支付完成
                }
                poOrdersPrepaymentDao.update(poOrdersPrepayment);
                this.ordersCount(2,poPaymentApplication,poOrders,poOrdersPrepayment); //计算金额和数量

                if (poOrders!=null&&poOrders.getId()!=null){
                    SrmSupplier srmSupplier1 = srmSupplierDao.get(poOrders.getSupplier().intValue());
                    srmSupplier = srmSupplier1.getId();
                    srmSupplierName = srmSupplier1.getName();
                }
            }else if (loanBiz!=null&&"9".equals(source)){  //多收来的款(1.233差额处理2)
                bizType= LoanBizService.LoanBizBizType.payment.getIndex();//业务类型:1-采购,3-报销,4-销售,5-支付
                loanType=LoanBizService.LoanBizType.payOverpayment.getIndex(); //类型1-采购货款,2-采购预付款,3-报销多付款,4-销售多收款,:5-支付多付款
                paymentApply = loanBiz.getId();
                loanBiz.setLockedAmount(loanBiz.getLockedAmount().subtract(amount)); //锁定(正在申批中)金额
                loanBiz.setPaidAmount(loanBiz.getPaidAmount().add(amount));  //已付金额
                loanBiz.setPaidTimes(loanBiz.getPaidTimes()!=null?loanBiz.getPaidTimes()+1:1); //支付笔数
                if (loanBiz.getPaidAmount().compareTo(loanBiz.getAmount())>=0){
                    loanBiz.setFinishTime(new Date()); //完成时间
                    if (loanBiz.getPaidAmount().compareTo(loanBiz.getAmount())>0) {
                        loanAmount = loanBiz.getPaidAmount().subtract(loanBiz.getAmount());
                        loanStatus = 2;
                    }
                }
                loanBizDao.update(loanBiz);
                tracePath = loanBiz.getTracePath();
                srmSupplier = loanBiz.getSupplier();
                srmSupplierName = loanBiz.getSupplierName();
                origBiz = loanBiz.getOrigBiz();
                if (loanBiz.getOrigBiz()==null){
                    origBiz = loanBiz.getId();
                }
            }
            if (2==loanStatus){   //生成借款信息(需收回的款)

//                this.addPoLoanAll(user,paymentApply,financePayment,ordersPrepayment,loanAmount,loanType,poOrders);
                this.addLoanBizAll(user,paymentApply,financePayment,ordersPrepayment,bizType,loanType,loanAmount,poOrders,tracePath,srmSupplier,srmSupplierName,origBiz);
            }

            //生成财务明细,走账户的
            AccountDetail accountDetail = financeCommonService.cashDisbursements(user,financeAccount,amount,billAmount,balance,summary,summary,method,business,source,srmSupplierName,factDate,"2");

            financePayment.setStatus("9");  //付款完成
            financePayment.setAccountDetail(accountDetail.getId());
            financePayment.setUpdator(user.getUserID());
            financePayment.setUpdateName(user.getUserName());
            financePayment.setUpdateDate(new Date());
            financePaymentDao.update(financePayment);
        }
        return map;
    }

    //method(1-现金,2-现金支票,3-转帐支票(外部),4-承兑汇票,5-银行转帐 6-转帐支票(内部))
    @Override
    public Map<String,Object> invoicePurchase(User user,BigDecimal amount,BigDecimal billAmount,Integer business,String source,FinancePayment financePayment,
        String method,Integer accountId,String summary,Integer invoiceId,String expireDate,String receiveDate,String receiver,String operator,String oppositeCorp){
        Map<String,Object> map = new HashMap<>();
        FinanceAccount financeAccount = new FinanceAccount();
        BigDecimal balance = new BigDecimal(0);
        if (accountId!=null){
            financeAccount = financeAccountDao.get(accountId);
            balance = financeAccount.getBalance().subtract(amount);
        }
        if (balance.compareTo(new BigDecimal(0))<0){
            map.put("content", "账户余额不足");
        }else{   //5-银行转帐的不用走此流程
            //转账支票、承兑汇票和内部转账支票的支出(内部银行转账的还没走账，只是在审批过程中)
            FinanceAccountBill financeAccountBill = financeCommonService.invoiceDisbursements(user,amount,billAmount,method,invoiceId,summary,summary,source,business,oppositeCorp,receiver,operator,expireDate,receiveDate,"2");

            financePayment.setStatus("5"); //5-付款方式确认
            financePayment.setAccountBill(financeAccountBill.getId());
            financePaymentDao.update(financePayment);
            map.put("financeAccountBill",financeAccountBill);
        }
        return map;
    }

    @Override
    public Map<String, Object> reviewedApprovalApproval(User user, Integer approvalProcessId) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else {
            approvalProcess.setApproveStatus("2");  //只有批准
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setToUser(user.getUserID());
            approvalProcessDao.update(approvalProcess);
            PoPaymentApplication poPaymentApplication =new PoPaymentApplication();
            PoOrdersPrepayment poOrdersPrepayment = new PoOrdersPrepayment();
            Integer business = null;
            Integer orderId = null;
            Integer businessType = 52; //52-待付款（采购的票款处理,出纳-待付款）
            FinancePayment financePayment = financePaymentDao.get(Integer.parseInt(approvalProcess.getBusinessGroup()));
            if (51==approvalProcess.getBusinessType()) {
                poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
//            poPaymentApplication.setPayState("3");  //付款审批状态:0-暂存,1-录入,2-提交,3-采购审批通过,4-采购审批驳回
                poPaymentApplication.setUpdateDate(new Date());
                poPaymentApplication.setUpdateName(user.getUserName());
                poPaymentApplication.setUpdator(user.getUserID());
                poPaymentApplicationDao.update(poPaymentApplication);
                business = poPaymentApplication.getId();
                orderId = poPaymentApplication.getOrders();
            }else if (65==approvalProcess.getBusinessType()){
                business = approvalProcess.getBusiness();
                poOrdersPrepayment = poOrdersPrepaymentDao.get(business.longValue());
                orderId = business;
                businessType=66; //66-待付款(1.229采购的预付款中)
            }

            if (!"A".equals(financePayment.getStatus())){
                financePayment.setStatus("6"); //6-付款复核通过
            }
            financePayment.setAuditState("3");//3-付款复核通过
            financePaymentDao.update(financePayment);

            //财务出纳-待付款的流程
            ApprovalProcess process = new ApprovalProcess();
            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
            process.setLevel(1);
            process.setToUserName("财务处理者");
            process.setToMid("lp");  //报销受理模块
            process.setCreateDate(new Date());
            process.setOrg(user.getOid());
            process.setUserName("财务处理者");
            process.setFromUser(approvalProcess.getFromUser());
            process.setAskName(approvalProcess.getAskName());
            process.setHandleTime(new Date());
            process.setBusiness(business);
            process.setBusinessType(businessType);
            process.setBusinessGroup(approvalProcess.getBusinessGroup());
            approvalProcessDao.save(process);

            HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,orderId,poOrdersPrepayment);  //推送的内容
            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
            for (UserPopedom u : userPopedomList) {
                //财务审批人待付款+1
                this.purchaseInvoiceRejectSend(1,1,hashMap, u.getUserId(), "/payFinanceHandle",null,null,"purchasePaymentFinanceApproval");

                //财务审批人待复核-1
                this.purchaseInvoiceRejectSend(0,-1,hashMap, u.getUserId(), "/reviewedFinanceHandle",null,null,"purchasePaymentFinanceApproval");
            }
            //复核审批者-采购部门的报销/预付款-待处理-1
            this.purchaseInvoiceRejectSend(-1,-1,hashMap, user.getUserID(), "/reviewedApprovalHandle",null,null,"purchaseFinanceReimburse");
            //复核审批者-采购部门的报销/预付款-已批准
            this.purchaseInvoiceRejectSend(0,1,hashMap, user.getUserID(), "/reviewedApprovalApproved",null,null,"purchaseFinanceReimburse");
            map.put("content","操作成功");
        }
        return map;
    }

    /**
     * 出纳-待付款审批
     * @param user
     * @param approvalProcessId 审批流程id
     * @param factDate 实际付款日期(收款单位接收日期)
     * @param summary 摘要
     * @param operatorName 接收经手人（收款单位经手人）
     * @return
     */
    @Override
    public Map<String, Object> payFinanceApproval(User user,Integer approvalProcessId,Date factDate, String summary,String operatorName) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("content","不可重复操作");
        }else{
            PoPaymentApplication poPaymentApplication = new PoPaymentApplication();
            PoOrdersPrepayment poOrdersPrepayment = new PoOrdersPrepayment();
            PoOrders poOrders = new PoOrders();
//            BigDecimal amount = new BigDecimal(0);
//            BigDecimal billAmount = new BigDecimal(0);
            String source = "5";
            if (52==approvalProcess.getBusinessType()) {
                poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
                poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
//                amount = poPaymentApplication.getAmount();
//                billAmount = poPaymentApplication.getBillAmount();
            }else if (66==approvalProcess.getBusinessType()){
                poOrdersPrepayment = poOrdersPrepaymentDao.get(approvalProcess.getBusiness().longValue());
                poOrders = poOrdersDao.get(poOrdersPrepayment.getOrders());
//                amount = poOrdersPrepayment.getPlanAmount();
//                billAmount = amount;
                source = "7";  //预付款的
            }
            FinancePayment financePayment = financePaymentDao.get(Integer.parseInt(approvalProcess.getBusinessGroup()));
            this.financeCommonContent(poPaymentApplication,poOrders,poOrdersPrepayment,financePayment,financePayment.getPlanAmount(),financePayment.getPlanAmount(),source,user,approvalProcess,map,factDate,summary,operatorName,1);  //最后确定的进账
        }
        return map;
    }

    //进入财务账的内容（最后确定的）  type 1-待付款的 2-无需复核的可付款  factDate-实际付款日期 summary-摘要  operatorName-接收经手人（收款单位经手人）
    @Override
    public Map<String,Object> financeCommonContent(PoPaymentApplication poPaymentApplication,PoOrders poOrders,PoOrdersPrepayment poOrdersPrepayment,FinancePayment financePayment,BigDecimal amount,BigDecimal billAmount,
        String source,User user,ApprovalProcess approvalProcess,Map<String,Object> map,Date factDate,String summary,String operatorName,Integer type){
        if (financePayment.getId()!=null){
            FinanceAccount financeAccount = new FinanceAccount();
            BigDecimal balance = new BigDecimal(0);
            if (financePayment.getAccountId()!=null){
                financeAccount = financeAccountDao.get(financePayment.getAccountId());

                //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
//                BigDecimal amount = new BigDecimal(poPaymentApplication.getAmount().toString());
                BigDecimal balance1 = new BigDecimal(financeAccount.getBalance().toString());
                balance = balance1.subtract(amount);
            }

            if (balance.compareTo(new BigDecimal(0))<0){
                map.put("content", "账户余额不足");
            }else{
                approvalProcess.setApproveStatus("2");  //只有批准
                approvalProcess.setUserName(user.getUserName());
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setToUser(user.getUserID());
                approvalProcessDao.update(approvalProcess);

                Integer loanStatus = 1;  //是否有借款(生成借款的信息) 1-无，2-有,可付款减数据减角标 3-正好付完，可付款减数据减角标
//                Integer loanType = 1;  //类型:1-货款(票据处理的),2-预付款
                Byte bizType = LoanBizService.LoanBizBizType.procure.getIndex(); //业务类型:1-采购,3-报销,4-销售,5-支付
                Byte loanType = LoanBizService.LoanBizType.procurement.getIndex();//类型1-采购货款(默认的),2-采购预付款,3-报销多付款,4-销售多收款,:5-支付多付款
                Integer paymentApply = null;
                Integer ordersPrepayment = null;
                BigDecimal loanAmount = BigDecimal.ZERO;
                Integer srmSupplier = null;
                String srmSupplierName = null;
                if ("5".equals(source)) {   //票款处理数据
                    paymentApply=poPaymentApplication.getId();

                    poPaymentApplication.setUpdateDate(new Date());
                    poPaymentApplication.setUpdateName(user.getUserName());
                    poPaymentApplication.setUpdator(user.getUserID());

                    poPaymentApplication.setLockedAmount(poPaymentApplication.getLockedAmount().subtract(amount)); //锁定(正在申批中)金额
                    poPaymentApplication.setPaidAmount(poPaymentApplication.getPaidAmount().add(amount));  //已付金额
                    poPaymentApplication.setPayNum(poPaymentApplication.getPayNum()!=null?poPaymentApplication.getPayNum()+1:1); //支付笔数
                    if (poPaymentApplication.getPaidAmount().compareTo(poPaymentApplication.getAmount())>=0){
                        poPaymentApplication.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                        loanStatus = 3;
                        if (poPaymentApplication.getPaidAmount().compareTo(poPaymentApplication.getAmount())>0) {
                            loanAmount = poPaymentApplication.getPaidAmount().subtract(poPaymentApplication.getAmount());
                            poPaymentApplication.setLoanAmount(loanAmount);  //借款金额
                            loanStatus = 2;
                        }
                        poPaymentApplication.setApproveStatus("2"); //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
                    }else {
                        poPaymentApplication.setPayStatus(1);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    }
                    poPaymentApplicationDao.update(poPaymentApplication);

                    this.ordersCount(1,poPaymentApplication,poOrders,poOrdersPrepayment);  //计算金额和数量

                    if (poOrders!=null&&poOrders.getId()!=null){
                        SrmSupplier srmSupplier1 = srmSupplierDao.get(poOrders.getSupplier().intValue());
                        srmSupplier = srmSupplier1.getId();
                        srmSupplierName = srmSupplier1.getName();
                    }
                }else if ("7".equals(source)){  //预付款数据
//                    loanType=2;
                    loanType = LoanBizService.LoanBizType.prepayment.getIndex();
                    ordersPrepayment=poOrdersPrepayment.getId().intValue();

                    poOrdersPrepayment.setFactDate(factDate);
                    poOrdersPrepayment.setUpdateDate(new Date());
                    poOrdersPrepayment.setUpdateName(user.getUserName());
                    poOrdersPrepayment.setUpdator(user.getUserID().longValue());

                    poOrdersPrepayment.setLockedAmount(poOrdersPrepayment.getLockedAmount().subtract(amount)); //锁定(正在申批中)金额
                    poOrdersPrepayment.setPaidAmount(poOrdersPrepayment.getPaidAmount().add(amount));  //已付金额
                    poOrdersPrepayment.setPayNum(poOrdersPrepayment.getPayNum()!=null?poOrdersPrepayment.getPayNum()+1:1); //支付笔数
                    if (poOrdersPrepayment.getPaidAmount().compareTo(poOrdersPrepayment.getPlanAmount())>=0){
                        poOrdersPrepayment.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                        loanStatus = 3;
                        if (poOrdersPrepayment.getPaidAmount().compareTo(poOrdersPrepayment.getPlanAmount())>0) {
                            loanAmount = poOrdersPrepayment.getPaidAmount().subtract(poOrdersPrepayment.getPlanAmount());
                            poOrdersPrepayment.setLoanAmount(loanAmount);  //借款金额
                            loanStatus = 2;
                        }
                        poOrdersPrepayment.setPrepaymentStatus("2"); //预付款审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-撤回
                    }else {
                        poOrdersPrepayment.setPayStatus(1);  //支付现状:0-未支付,1-部分支付,2-支付完成
                    }
                    poOrdersPrepaymentDao.update(poOrdersPrepayment);
                    this.ordersCount(2,poPaymentApplication,poOrders,poOrdersPrepayment);  //计算金额和数量

                    if (poOrders!=null&&poOrders.getId()!=null){
                        SrmSupplier srmSupplier1 = srmSupplierDao.get(poOrders.getSupplier().intValue());
                        srmSupplier = srmSupplier1.getId();
                        srmSupplierName = srmSupplier1.getName();
                    }
                }

                financePayment.setFactDate(factDate);
                financePayment.setSummary(summary);
                financePaymentDao.update(financePayment);

                if (2==loanStatus){   //生成借款信息
//                    this.addPoLoanAll(user,paymentApply,financePayment,ordersPrepayment,loanAmount,loanType,poOrders);
                    this.addLoanBizAll(user,paymentApply,financePayment,ordersPrepayment,bizType,loanType,loanAmount,poOrders,null,srmSupplier,srmSupplierName,null);
                }

                financeCommonService.invoiceDisbursementsApproval(user,financeAccount,financePayment,amount,billAmount,balance,summary,summary,source,poOrders.getSupplierName(),operatorName,factDate,approvalProcess.getBusiness(),"2");
                //改为上面的接口
//                if ("5".equals(financePayment.getMethod())){  //银行转账
//                    //生成财务明细
//                    AccountDetail accountDetail = new AccountDetail();
//                    accountDetail.setDebit(amount);//合计报销金额 录入支出
//                    accountDetail.setBalance(balance);
//                    accountDetail.setCreator(user.getUserID());
//                    accountDetail.setCreateName(user.getUserName());
//                    accountDetail.setCreateDate(new Date());
//                    accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
//                    accountDetail.setOrg(user.getOrganization());
//                    accountDetail.setSummary(summary);
//                    accountDetail.setPurpose(summary);  //摘要与用途一样
//                    accountDetail.setAuditor(user.getUserID());
//                    accountDetail.setAuditorName(user.getUserName());
//                    accountDetail.setBillAmount(billAmount);
//                    accountDetail.setModityStatus("2");//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
//                    accountDetail.setMethod("5");//银行转账
//                    accountDetail.setSource(source);  //5-票款处理(1.169采购的票款处理) 7-预付款(1.229采购之预付款)
//                    accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
//                    accountDetail.setOppositeCorp(poOrders.getSupplierName()); //付款单位(收款单位)
//                    accountDetail.setPartnerName(operatorName); //合作方经手人
//                    if (financeAccount.getAccount()!=null){
//                        accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
//                    }else {
//                        accountDetail.setAccountBank(financeAccount.getBankName());
//                    }
//                    accountDetail.setFactDate(factDate);
//                    accountDetail.setBusiness(approvalProcess.getBusiness());
//
//
//                    //更新账户
//                    financeAccount.setDebit(financeAccount.getDebit().add(amount));//总支出加上
//                    financeAccount.setBalance(balance);//在余额中减去
//                    accountService.updateFinanceAccount(financeAccount);
//
//                    //月结
//                    AccountPeriod yue = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
//                    yue.setDebit(yue.getDebit().add(amount));
//                    yue.setBalance(balance);
//                    accountService.updateAccountPeroid(yue);
//
//                    //日结
//                    AccountPeriod ri = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());
//                    ri.setDebit(ri.getDebit().add(amount));
//                    ri.setBalance(balance);
//                    accountService.updateAccountPeroid(ri);
//
//                    accountDetail.setFid(financeAccount.getId().toString());
//                    accountDetail.setAccountId(financeAccount);
//                    accountDetail.setAccount(yue.getId().toString());
//                    accountService.saveAccountDetail(accountDetail);
//
//                    financePayment.setStatus("9"); //付款完成
//                    financePayment.setAccountDetail(accountDetail.getId());
//                    financePayment.setUpdator(user.getUserID());
//                    financePayment.setUpdateName(user.getUserName());
//                    financePayment.setUpdateDate(new Date());
//                    financePaymentDao.update(financePayment);
//                }else if ("3".equals(financePayment.getMethod())||"4".equals(financePayment.getMethod())){
//                    //支出的外部转账支票 或者是 承兑汇票
//                    FinanceAccountBill financeAccountBill = financeAccountBillDao.get(financePayment.getAccountBill());
//                    if (financeAccountBill.getCheque_()!=null){ //支出的内部转账支票
//                        //支出的内部银行转账支票
//                        FinanceChequeDetail financeChequeDetail = financeChequeDetailDao.get(financeAccountBill.getCheque_());
//                        financeChequeDetail.setReceiver(operatorName); //接收经手人
////                        financeChequeDetail.setReceiveDate(receiveDate);//接收日期
//                        financeChequeDetail.setFinancialHandling(user.getUserName());//财务经手人
//                        financeChequeDetail.setUpdateDate(new Date());
//                        financeChequeDetail.setUpdateName(user.getUserName());
//                        financeChequeDetail.setUpdator(user.getUserID());
//                        financeChequeDetail.setSummary(summary);
//                        financeChequeDetail.setPurpose(summary);
//                        financeChequeDetailDao.update(financeChequeDetail);
//
//                        //生成财务明细
//                        AccountDetail accountDetail = new AccountDetail();
//                        accountDetail.setDebit(poPaymentApplication.getAmount());//合计报销金额 录入支出
//                        accountDetail.setBalance(balance);
//                        accountDetail.setCreator(user.getUserID());
//                        accountDetail.setCreateName(user.getUserName());
//                        accountDetail.setCreateDate(new Date());
//                        accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
//                        accountDetail.setOrg(user.getOrganization());
//                        accountDetail.setSummary(summary);
//                        accountDetail.setPurpose(summary);
//                        accountDetail.setAuditor(user.getUserID());
//                        accountDetail.setAuditorName(user.getUserName());
//                        accountDetail.setBillAmount(billAmount);
//                        accountDetail.setModityStatus("2");//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
//                        accountDetail.setMethod(financePayment.getMethod());//银行转账
//                        accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
//                        accountDetail.setBillDetail(financeAccountBill);
//                        accountDetail.setBillDetail_(financeAccountBill.getId());
//                        accountDetail.setOppositeCorp(poOrders.getSupplierName()); //付款单位(收款单位)
//                        accountDetail.setPartnerName(operatorName);
//                        accountDetail.setFactDate(factDate);  //实际付款日期，收款单位接收日期
//                        accountDetail.setBusiness(approvalProcess.getBusiness());
//
//                        //更新账户
//                        financeAccount.setDebit(financeAccount.getDebit().add(amount));//总支出加上
//                        financeAccount.setBalance(balance);//在余额中减去
//                        accountService.updateFinanceAccount(financeAccount);
//
//                        //月结
//                        AccountPeriod yue = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
//                        yue.setDebit(yue.getDebit().add(amount));
//                        yue.setBalance(balance);
//                        accountService.updateAccountPeroid(yue);
//
//                        //日结
//                        AccountPeriod ri = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());
//                        ri.setDebit(ri.getDebit().add(amount));
//                        ri.setBalance(balance);
//                        accountService.updateAccountPeroid(ri);
//
//                        accountDetail.setFid(financeAccount.getId().toString());
//                        accountDetail.setAccountId(financeAccount);
//                        accountDetail.setAccount(yue.getId().toString());
//                        accountDetail.setSource(source);
//                        accountService.saveAccountDetail(accountDetail);
//
//                        financePayment.setStatus("9"); //付款完成
//                        financePayment.setAccountDetail(accountDetail.getId());
//                        financePayment.setUpdator(user.getUserID());
//                        financePayment.setUpdateName(user.getUserName());
//                        financePayment.setUpdateDate(new Date());
//                        financePaymentDao.update(financePayment);
//                    }else {
//                        //支出的外部转账支票或承兑汇票
//                        FinanceReturn fr = financeReturnDao.get(financeAccountBill.getFinanceReturn().getId());
//        //                      fr.setState("4");//1-有效,2-存入银行,3-作废,4-已支出
//                        fr.setOperatorName(operatorName); //接收人
////                        fr.setReceiveDate(receiveDate);  //接收日期
//                        fr.setSummary(summary);
//                        fr.setPurpose(summary);
//                        fr.setUpdateDate(new Date());
//                        fr.setUpdateName(user.getUserName());
//                        fr.setUpdator(user.getUserID());
//                        financeReturnDao.update(fr);
//
//                    }
//                    financeAccountBill.setFactDate(factDate); //实际付款日期，收款单位接收日期
//                    financeAccountBillDao.update(financeAccountBill);  //其他的在添加的时候已经录入了
//
//                    financePayment.setStatus("9"); //付款完成
//                    financePayment.setUpdator(user.getUserID());
//                    financePayment.setUpdateName(user.getUserName());
//                    financePayment.setUpdateDate(new Date());
//                    financePaymentDao.update(financePayment);
//                }

                    //无论那阶段审批通过，都需给历史审批人已批准中减数据
                HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,poOrders,null,poOrdersPrepayment);  //推送的内容
                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                for (UserPopedom u : userPopedomList) {
                    if (2==type&&1!=loanStatus) {   //无需复核的可付款(若还在可付款，则可付款就不减推送了)
                        //财务审批人可付款-1
                        this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/payableFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                    }
                    if (1==type) {   //待付款进行的
                        //财务出纳-待付款-1
                        this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/payFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                    }
                }
                this.commonEnd(poPaymentApplication,poOrders.getId(),poOrdersPrepayment,financePayment,loanStatus);  //审批完后的推送
                if (approvalProcess.getBusinessType()<60) {   //采购的票据审核的
                    //申请人发消息
                    Integer applicationId = Integer.valueOf("36" + financePayment.getId());  //id前的“36”是前端用的，进行判断详情页面，票据有付款方式的
                    if ("2".equals(poPaymentApplication.getType())) {    //2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
                        userSuspendMsgService.saveUserSuspendMsg(1, "您提交的采购报销已处理完成", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail", applicationId);  //给前端要查看详情的链接
                    } else {    //3-仅提交付款申请,不录入票据
                        userSuspendMsgService.saveUserSuspendMsg(1, "您提交的付款已获审批通过！", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail", applicationId);  //给前端要查看详情的链接
                    }
                }else if (approvalProcess.getBusinessType()<70){   //采购的预付款的
                    Integer poOrdersPayment = Integer.valueOf("38"+financePayment.getId()); //id前的“38”是前端要求的，进行判断详情页面，预付款有付款方式的
                    userSuspendMsgService.saveUserSuspendMsg(1, "您提交的预付款已获审批通过！", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "advancePaymentDetail", poOrdersPayment);  //给前端要查看详情的链接
                }
                map.put("content","操作成功");
            }
        }else {
            map.put("content","操作失败");
        }
         return map;
    }

    //最后审批后推送的内容  loanStatus：是否有借款(生成借款的信息) 1-无，2-有,可减数据减角标 3-正好付完，可减数据减角标
    private void commonEnd(PoPaymentApplication poPaymentApplication,Integer orderId,PoOrdersPrepayment poOrdersPrepayment,FinancePayment financePayment,Integer loanStatus){
        //无论那阶段审批通过，都需给历史审批人已批准中减数据
        HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,orderId,poOrdersPrepayment);  //推送的内容
        List<ApprovalProcess> approvalProcessList = new ArrayList<>();
        if (poPaymentApplication!=null&&poPaymentApplication.getId()!=null) {  //采购票据的推送
            if (financePayment!=null&&financePayment.getId()!=null&&1==loanStatus){
                //不生成借款的，就是没有直接结束的，还得在可付款呆着
                approvalProcessList = approvalProcessService.getApprovalProcessByGroup(null,null,financePayment.getId().toString(),"2",51);
            }else if (financePayment!=null&&financePayment.getId()!=null){
                approvalProcessList = approvalProcessService.getApprovalProcesses(null, poPaymentApplication.getId(), financePayment.getId(), null,46, 48, 49, 51);
            }else {
                approvalProcessList = approvalProcessService.getApprovalProcesses(null, poPaymentApplication.getId(), null, null,46, 48, 49, 51);
            }
            for (ApprovalProcess appProcess : approvalProcessList) {
                if (!"lp".equals(appProcess.getToMid())) {
                    if (46 == appProcess.getBusinessType()) {  //46-采购的票款处理-采购审批者的采购部门的票据审核已批准
                        this.purchaseInvoiceRejectSend(0, -1, hashMap, appProcess.getToUser(), "/purchaseInvoiceApproved", null, null, "purchaseBillApproval");
                    } else if (48 == appProcess.getBusinessType()) {   //采购审批者的采购部门的付款(采购的票款处理)
                        //采购审批者-采购部门的付款-已批准
                        this.purchaseInvoiceRejectSend(0, -1, hashMap, appProcess.getToUser(), "/purchasePaymentApproved", null, null, "purchasePaymentApproval");
                    } else if (49 == appProcess.getBusinessType()) {
                        //付款审批者-采购部门的付款-已批准
                        this.purchaseInvoiceRejectSend(0, -1, hashMap, appProcess.getToUser(), "/paymentApprovalApproved", null, null, "purchasePaymentApplyApproval");
                    } else if (51 == appProcess.getBusinessType()) {
                        //复核审批者-采购部门的报销-已批准
                        this.purchaseInvoiceRejectSend(0, 1, hashMap, appProcess.getToUser(), "/reviewedApprovalApproved", null, null, "purchaseFinanceReimburse");
                    }
                }
            }
            if (1!=loanStatus) {  //2-有,可减数据减角标 3-正好付完，可减数据减角标     就是付款完成的
                if (!"3".equals(poPaymentApplication.getType())) {
                    //申请人-采购部门的票据审核-1
                    this.purchaseInvoiceRejectSend(0, -1, hashMap, poPaymentApplication.getApplicant(), "/invoiceApplyHandle", null, null, "purchaseBillApply");//申请人
                } else {
                    //申请人-采购部门的付款
                    this.purchaseInvoiceRejectSend(0, -1, hashMap, poPaymentApplication.getApplicant(), "/paymentApplyHandle", null, null, "purchasePaymentApply");//申请人
                }
            }
        }else if (orderId!=null ){   //预付款的推送
//            approvalProcessList = approvalProcessService.getApprovalProcessAll(null, poOrdersPrepayment.getId().intValue(), null, 63,65);
            if (financePayment!=null&&financePayment.getId()!=null&&1==loanStatus){
                //不生成借款的，就是没有直接结束的，还得在可付款呆着
                approvalProcessList = approvalProcessService.getApprovalProcessByGroup(null,null,financePayment.getId().toString(),"2",65);
            }else if (financePayment!=null&&financePayment.getId()!=null){
                approvalProcessList = approvalProcessService.getApprovalProcesses(null, poOrdersPrepayment.getId().intValue(), financePayment.getId(), null,63,65);
            }else {
                approvalProcessList = approvalProcessService.getApprovalProcesses(null, poOrdersPrepayment.getId().intValue(), null, null,63,65);
            }
            for (ApprovalProcess appProcess : approvalProcessList) {
                if (!"lp".equals(appProcess.getToMid())) {
                    if (63 == appProcess.getBusinessType()) {  //付款审批者-待付款审批-已批准(1.229采购的预付款中)
                        this.purchaseInvoiceRejectSend(0, -1, hashMap, appProcess.getToUser(), "/advancePaymentPayApproved", null, null, "purchaseAdvancePayApproval");
                    } else if (65 == appProcess.getBusinessType()) {
                        //复核审批者-采购部门的付款-已批准
                        this.purchaseInvoiceRejectSend(0, 1, hashMap, appProcess.getToUser(), "/reviewedApprovalApproved", null, null, "purchaseFinanceReimburse");
                    }
                }
            }
            if (1!=loanStatus) {  //2-有,可付款减数据减角标 3-正好付完，可付款减数据减角标     就是付款完成的
                //申请人-采购预付款申请-1
                this.purchaseInvoiceRejectSend(-1, -1, hashMap, poOrdersPrepayment.getCreator().intValue(), "/advancePaymentApplyHandle", null, null, "purchaseAdvancePayApply");//申请人
            }
        }
    }


    @Override
    public Map<String, Object> getPurchaseInvoiceDetail(Integer applicationId,Integer financePaymentId) {
        Map<String,Object> map = new HashMap<>();
        PoPaymentApplication poPaymentApplication = new PoPaymentApplication();
        if (applicationId!=null) {
            poPaymentApplication = poPaymentApplicationDao.get(applicationId);  //报销的总信息
        }
        FinancePayment financePayment = new FinancePayment();
        if (financePaymentId!=null){
            financePayment = financePaymentDao.get(financePaymentId);
            if (applicationId==null){
                applicationId = financePayment.getBusiness();
                poPaymentApplication = poPaymentApplicationDao.get(applicationId);  //报销的总信息
            }
        }
        PoOrders poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
        SrmSupplier srmSupplier = getSupplier(poPaymentApplication.getOrders());  //供应商信息
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcesses(null,poPaymentApplication.getId(),financePaymentId,1,45,46,47,48,49,50,51,52,53);

        List<Map<String,Object>> listMapBillCat = new ArrayList<>();
        List<Map<String,Object>> listMapFeeCat = new ArrayList<>();

        // 按票据种类----需要的为票面金额
        Map<String,Object> params = new HashMap<>();
        String hql = "select id,invoiceCategory,sum(relativeBillQuantity),sum(billAmount) from PoPaymentInvoice where " +
                "relativeBillQuantity!=0 and application=:application group by invoiceCategory order by sum(billAmount) desc";
        params.put("application",applicationId);
        List<Object[]> listObjects = poPaymentInvoiceDao.getListByHQLWithNamedParams(hql,params);
        for (Object[] listObject:listObjects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("invoiceId",listObject[0]);  //票据id
            map1.put("invoiceCategory",listObject[1]);  //票据种类名字
            map1.put("num",listObject[2]);  //此票据种类的票据数量
            map1.put("totalBillAmount",listObject[3]);   //此票据种类的总票据金额
            listMapBillCat.add(map1);
        }

        // 按费用类别
        Map<String,Object> map2 = new HashMap<>();
        map2.put("feeCategory","采购材料");   //费用类别
        map2.put("totalBillAmount",poPaymentApplication.getBillAmount());   //此费用类别的总票据金额
        listMapFeeCat.add(map2);

        //付款信息
//        FinancePayment financePayment = financePaymentService.getPaymentByBusiness(poPaymentApplication.getId(),"procurement");
        FinanceAccount financeAccount = new FinanceAccount();
        FinanceChequeDetail financeChequeDetail = new FinanceChequeDetail();
        FinanceReturn financeReturn = new FinanceReturn();
        Integer invoiceType = 1;  //主要来区分是内部转账支票还是外部转账支票
        Integer status = 0;

        if (financePaymentId!=null){
            if (financePayment.getAccountId()!=null) {
                financeAccount = financeAccountDao.get(financePayment.getAccountId());
            }

            if ("3".equals(financePayment.getMethod()) || "4".equals(financePayment.getMethod())) {
                FinanceAccountBill financeAccountBill = financeAccountBillDao.get(financePayment.getAccountBill());
                if (financeAccountBill.getCheque_()!=null){
                    invoiceType = 2;  //内部票据
                    financeChequeDetail = financeChequeDetailDao.get(financeAccountBill.getCheque_());
                }else if (financeAccountBill.getFinanceReturn().getId()!=null){
                    invoiceType = 3;  //外部转账票据
                    financeReturn = financeAccountBill.getFinanceReturn();
                }
            }

            List<FinancePaymentHistory> financePaymentHistorys = financePaymentService.getPaymentHistoryByBusiness(applicationId,financePaymentId,"procurement");
            if (financePaymentHistorys.size()>=2){
                status = 1;   //付款方式进行了修改
            }
        }

        Integer num = financeCommonService.getFinancePaymentNum(applicationId,"10","procurement");  //待复核的付款笔数
        map.put("numApplication",num);

        map.put("poPaymentApplication",poPaymentApplication);  //报销的总信息
        map.put("fullName",srmSupplier.getFullName()); //供应商名称
        map.put("codeName",srmSupplier.getCodeName());  //代号
        map.put("approvalProcessList",approvalProcessList);   //审批流程
        map.put("listMapBillCat",listMapBillCat);  //按票据种类
        map.put("listMapFeeCat",listMapFeeCat);  //按费用类别
        map.put("financePayment",financePayment);  //付款方式信息
        map.put("financeAccount",financeAccount);  //账户信息
        map.put("poOrders",poOrders);  //订单信息
        map.put("invoiceType",invoiceType);  // 1-其他的付款方式  2-内部转账票据  3-外部转账票据
        map.put("financeChequeDetail",financeChequeDetail);  //内部转账支票信息
        map.put("financeReturn",financeReturn);   //外部转账支票或者承兑汇票信息
        map.put("status",status);   //0-付款方式没有进行修改  1-付款方式进行修改
        return map;
    }

    @Override
    public Map<String, Object> getFinancePayment(Integer financePaymentId,String businessTypePH) {
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isEmpty(businessTypePH)){
            businessTypePH="procurement"; //默认是票据的，因为新加的此字段
        }
        List<FinancePaymentHistory> financePaymentHistorys = financePaymentService.getPaymentHistoryByBusiness(null,financePaymentId,businessTypePH);
        PoPaymentApplication poPaymentApplication = new PoPaymentApplication();
        PoOrdersPrepayment poOrdersPrepayment = new PoOrdersPrepayment();
        PoOrders poOrders = new PoOrders();
        SrmSupplier srmSupplier = new SrmSupplier();
        Integer[] businessType = {45,46,47,48,49,50,51,52,53};  //默认票据的
        List<ApprovalProcess> approvalProcessList = new ArrayList<>();
        if (financePaymentHistorys.size()>0){
            Integer business = financePaymentHistorys.get(0).getBusiness();
            if ("procurement".equals(financePaymentHistorys.get(0).getBusinessType())){  //采购票据
                poPaymentApplication = poPaymentApplicationDao.get(business);  //报销的总信息
                poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
                srmSupplier = getSupplier(poOrders.getId());  //供应商信息
            }else if ("prepayment".equals(financePaymentHistorys.get(0).getBusinessType())){  //采购的预付款
                poOrdersPrepayment = poOrdersPrepaymentDao.get(business.longValue());
                poOrders = poOrdersDao.get(poOrdersPrepayment.getOrders());
                srmSupplier = getSupplier(poOrders.getId());  //供应商信息
                businessType = new Integer[]{63,64,65,66,67};
            }
            approvalProcessList = approvalProcessService.getApprovalProcesses(null,business,financePaymentId,1,businessType);
            Integer num = financeCommonService.getFinancePaymentNum(business,"10",businessTypePH);  //待复核的付款笔数
            map.put("numApplication",num);
        }

//        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessAll(null,applicationId,null,businessType);
        Integer invoiceType = 1;  //主要来区分是内部转账支票还是外部转账支票

        FinancePaymentHistory financePaymentHistory = new FinancePaymentHistory();
        FinanceAccount financeAccount = new FinanceAccount();
        FinanceReturn financeReturn = new FinanceReturn();
        FinanceChequeDetail financeChequeDetail = new FinanceChequeDetail();
        if(financePaymentHistorys.size()==2){
            financePaymentHistory=financePaymentHistorys.get(0);
            if (financePaymentHistory.getAccountId()!=null) {
                financeAccount = financeAccountDao.get(financePaymentHistory.getAccountId());
            }
            if (financePaymentHistory.getAccountBillHistory()!=null){
                FinanceAccountBillHistory financeAccountBillHistory = financeAccountBillHistoryDao.get(financePaymentHistory.getAccountBillHistory());
                if (financeAccountBillHistory.getCheque()!=null){
                    invoiceType = 2;  //内部票据
                    financeChequeDetail = financeChequeDetailDao.get(financeAccountBillHistory.getCheque());
                }
                if (financeAccountBillHistory.getReturnBill()!=null){
                    invoiceType = 3;  //外部转账票据
                    financeReturn = financeReturnDao.get(financeAccountBillHistory.getReturnBill());
                }
            }
        }

        map.put("poPaymentApplication",poPaymentApplication);  //报销的总信息
        map.put("poOrdersPrepayment",poOrdersPrepayment);  //报销的总信息
        map.put("financePaymentHistory",financePaymentHistory);  //付款方式信息
        map.put("financeAccount",financeAccount);  //账户信息
        map.put("financeReturnHistory",financeReturn);  //外部转账支票或承兑汇票信息
        map.put("financeChequeDetail",financeChequeDetail);  //内部转账支票
        map.put("fullName",srmSupplier.getFullName()); //供应商名称
        map.put("codeName",srmSupplier.getCodeName());  //代号
        map.put("srmSupplier",srmSupplier);  //代号
        map.put("approvalProcessList",approvalProcessList);   //审批流程
        map.put("poOrders",poOrders);   //订单信息
        map.put("invoiceType",invoiceType);   // 1-其他的付款方式  2-内部转账票据  3-外部转账票据
        return map;
    }

    @Override
    public Map<String, Object> cancelPurchaseInvoice(User user, Integer applicationId) {
        Map<String,Object> map = new HashMap<>();
        PoPaymentApplication poPaymentApplication = poPaymentApplicationDao.get(applicationId);
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessAll(null,poPaymentApplication.getId(),null,45,46,47,48,49,50,51,52,53);
        if (approvalProcessList.size()>1){
            map.put("content","已进行审批，不可撤回");
        }else {
            List<PoPaymentInvoice> poPaymentInvoices = getInvoicesByApplicationId(applicationId);
            List<PoPaymentInvoiceItem> poPaymentInvoiceItems = getInvoicesItemById(applicationId,null);

            HashMap<String,Object> hashMap = rejectSendType(poPaymentApplication,null,poPaymentApplication.getOrders(),null);  //推送的内容
            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
            for (UserPopedom u : userPopedomList) {
                if (!"3".equals(poPaymentApplication.getType())) {
                    //财务审批人待在线审核-1
                    this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/paymentOnlineHandle", null, null, "purchaseBillFinanceApproval");
                }else {
                    //财务审批人待付款审批-1
                    this.purchaseInvoiceRejectSend(0, -1, hashMap, u.getUserId(), "/payApprovalFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                }
            }

            if (!"3".equals(poPaymentApplication.getType())) {  //类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
                //申请人-采购部门的票据审核
                this.purchaseInvoiceRejectSend(0, -1, hashMap, user.getUserID(), "/invoiceApplyHanle", null, null, "purchaseBillApply");//申请人
            }else {
                //采购审批者-采购部门的付款-待处理
                this.purchaseInvoiceRejectSend(-1,-1,hashMap, approvalProcessList.get(0).getToUser(), "/purchasePaymentHandle",null,null,"purchasePaymentApproval");
                //申请人-采购部门的付款
                this.purchaseInvoiceRejectSend(0, -1, hashMap, user.getUserID(), "/paymentApplyHandle", null, null, "purchasePaymentApply");//申请人
            }

            for (PoPaymentInvoice poPaymentInvoice:poPaymentInvoices) {
                PoPaymentInvoiceAttachment poPaymentInvoiceAttachment = getInvoicePictureById(poPaymentInvoice.getId());
                poPaymentInvoiceAttachmentDao.delete(poPaymentInvoiceAttachment);
            }
            poPaymentInvoiceItemDao.deleteAll(poPaymentInvoiceItems);
            poPaymentInvoiceDao.deleteAll(poPaymentInvoices);
            approvalProcessDao.deleteAll(approvalProcessList);
            poPaymentApplicationDao.delete(poPaymentApplication);
            map.put("content","操作成功");
        }
        return map;
    }

    @Override
    public List<PoPaymentInvoice> getInvoicesByApplicationId(Integer applicationId){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PoPaymentInvoice where application=:application";
        map.put("application",applicationId);
        return poPaymentInvoiceDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override
    public List<PoPaymentInvoiceItem> getInvoicesItemById(Integer applicationId,Integer invoiceId){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PoPaymentInvoiceItem where application=:application";
        map.put("application",applicationId);
        if (invoiceId!=null){
            hql+=" and invoice=:invoice";
            map.put("invoice",invoiceId);
        }
        return poPaymentInvoiceItemDao.getListByHQLWithNamedParams(hql,map);
    }

    /**
     * 修改付款方式
     * @param user
     * @param type  1-待复核修改的 2-待付款修改的
     * @param approvalProcessId  审批流程id
     * @param method method(1-现金,2-现金支票,3-转帐支票(外部),4-承兑汇票,5-银行转帐 6-转帐支票(内部))
     * @param planDate  计划时间
     * @param factDate  实际时间
     * @param accountId 账户id
     * @param summary 摘要
     * @param invoiceId 票据id
     * @param expireDate  支票到期日
     * @param receiveDate 接收日期
     * @param receiver 接收经手人
     * @param operator 支付经手人
     * @param oppositeCorp 收款单位
     * @return
     */
    @Override
    public Map<String, Object> getUpdateFinancePayment(User user, Integer type, Integer approvalProcessId, String method, Date planDate,
            Date factDate, Integer accountId, String summary, Integer invoiceId, String expireDate, String receiveDate, String receiver, String operator, String oppositeCorp,String factAmount) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcessId);
        PoPaymentApplication poPaymentApplication = new PoPaymentApplication();
        PoOrdersPrepayment poOrdersPrepayment = new PoOrdersPrepayment();
        PoOrders poOrders = new PoOrders();
        FinancePayment financePayment = new FinancePayment();
        BigDecimal amount = new BigDecimal(factAmount);
        BigDecimal bullAmount = new BigDecimal(factAmount);
        Integer businessTypeUpdateMethod = 53;  //修改付款方式的
        Integer businessTypePay = 52; //如果是现金就直接结账的
        Integer businessTypeReview = 51; //如果是待付款修改的，直接就进入待复核了
        String source = "5";
        BigDecimal difference = new BigDecimal(0); //差额(负数代表少付)
        Integer payStatusOld = 1; //支付现状:0-未支付,1-部分支付,2-支付完成
        Integer payStatusNew = 1; //支付现状:0-未支付,1-部分支付,2-支付完成
        String businessTypeFinancePayment = "procurement"; //票据处理的
        BigDecimal lockedAmount = BigDecimal.ZERO;//锁定(正在申批中)金额
        BigDecimal paidAmount = BigDecimal.ZERO;//已付金额
        if (approvalProcess.getBusinessType()<60) {
            poPaymentApplication = poPaymentApplicationDao.get(approvalProcess.getBusiness());
            poOrders = poOrdersDao.get(poPaymentApplication.getOrders());
            if (StringUtils.isNotEmpty(approvalProcess.getBusinessGroup())){
                financePayment = financePaymentDao.get(Integer.parseInt(approvalProcess.getBusinessGroup()));
            }else {
                financePayment = financePaymentService.getPaymentByBusiness(null,poPaymentApplication.getId(), businessTypeFinancePayment);
            }
            payStatusOld = poPaymentApplication.getPayStatus();
            if (financePayment.getPlanAmount().compareTo(new BigDecimal(factAmount))!=0) {
                lockedAmount = poPaymentApplication.getLockedAmount() != null ? poPaymentApplication.getLockedAmount().subtract(financePayment.getPlanAmount()).add(new BigDecimal(factAmount)) : new BigDecimal(factAmount);
//                poPaymentApplication.setLockedAmount(lockedAmount); //锁定(正在申批中)金额
                paidAmount = poPaymentApplication.getPaidAmount() != null ? poPaymentApplication.getPaidAmount(): new BigDecimal(0);
//                poPaymentApplication.setPaidAmount(paidAmount);  //已付金额
                difference = lockedAmount.add(paidAmount).subtract(poPaymentApplication.getAmount());  //差额
            }
        }else {  //预付款的
            businessTypeFinancePayment = "prepayment";
            if (StringUtils.isNotEmpty(approvalProcess.getBusinessGroup())){
                financePayment = financePaymentDao.get(Integer.parseInt(approvalProcess.getBusinessGroup()));
            }else {
                financePayment = financePaymentService.getPaymentByBusiness(null,approvalProcess.getBusiness(), businessTypeFinancePayment);
            }
            poOrdersPrepayment = poOrdersPrepaymentDao.get(approvalProcess.getBusiness().longValue());
            businessTypeUpdateMethod = 67;
            businessTypePay = 66;
            businessTypeReview = 65;
            poOrders = poOrdersDao.get(poOrdersPrepayment.getOrders());
            source = "7";
            payStatusOld = poOrdersPrepayment.getPayStatus();
            if (financePayment.getPlanAmount().compareTo(new BigDecimal(factAmount))!=0) {
                lockedAmount = poOrdersPrepayment.getLockedAmount() != null ? poOrdersPrepayment.getLockedAmount().subtract(financePayment.getPlanAmount()).add(new BigDecimal(factAmount)) : new BigDecimal(factAmount);
//                poOrdersPrepayment.setLockedAmount(lockedAmount); //锁定(正在申批中)金额
                paidAmount = poOrdersPrepayment.getPaidAmount() != null ? poOrdersPrepayment.getPaidAmount(): new BigDecimal(0);
//                poOrdersPrepayment.setPaidAmount(paidAmount);  //已付金额
                difference = lockedAmount.add(paidAmount).subtract(poOrdersPrepayment.getPlanAmount());  //差额
            }
        }
        FinanceAccountBill financeAccountBill = new FinanceAccountBill();
        BigDecimal balance = new BigDecimal(0);
        Integer updateState = 0;  //0-可以修改  1-是同一张票或同一个账户，不能进行修改
        if ("1".equals(method)||"5".equals(method)||"6".equals(method)){
            FinanceAccount financeAccount = financeAccountDao.get(accountId);
            if (financePayment.getAccountBill()!=null) {
                financeAccountBill = financeAccountBillDao.get(financePayment.getAccountBill());
                balance = financeAccount.getBalance().subtract(amount);  //新账户的余额
                if ("6".equals(method) && financeAccountBill.getCheque_() != null && method.equals(financePayment.getMethod()) && invoiceId.equals(financeAccountBill.getCheque_())) {
                    updateState = 1;
                } else if ("5".equals(method) && financePayment.getAccountId() != null && method.equals(financePayment.getMethod()) && accountId.equals(financePayment.getAccountId())) {
                    updateState = 1;
                }
            }else if (accountId.equals(financePayment.getAccountId()) && method.equals(financePayment.getMethod())){
                updateState = 1;
            }
        }else {
            if (financePayment.getAccountBill()!=null) {
                financeAccountBill = financeAccountBillDao.get(financePayment.getAccountBill());
                if (method.equals(financePayment.getMethod()) && invoiceId.equals(financeAccountBill.getFinanceReturn().getId())) {
                    updateState = 1;
                }
            }
        }

        if (balance.compareTo(new BigDecimal(0))<0){
            if ("1".equals(method)){
                map.put("content", "系统中现金余额不足，请确认。");
            }else if ("5".equals(method)||"6".equals(method)) {    //银行转账或内部转账支票
                map.put("content", "系统中该账户的余额不足!");
            }
        }else if (updateState==1){
            map.put("content","修改的为同一张票或同一个账户");
        }else {
            Integer num = financeCommonService.getFinancePaymentNum(approvalProcess.getBusiness(),"10",businessTypeFinancePayment);  //待复核的付款笔数
            //有待审批的数据时且产生了多支付的，改为承兑汇票或者外部转账支票的不能修改【num=1的说明只有一条是待审批的，修改的还是此条数据，则可以进行修改】
            if (num>1&&difference.compareTo(BigDecimal.ZERO) >0&&("3".equals(method)||"4".equals(method))){
                String content = "操作失败！还有“待复核的付款”时，不支持选将导致多支付的转账支票！";
                if("4".equals(method)){
                    content = "操作失败！还有“待复核的付款”时，不支持选将导致多支付的承兑汇票！";
                }
                map.put("content", content);
            }else {
                if ("5".equals(source)) {
                    poPaymentApplication.setLockedAmount(lockedAmount); //锁定(正在申批中)金额
                    poPaymentApplication.setPaidAmount(paidAmount); //'已付金额'
                    poPaymentApplication.setBalanceAmount(difference);  //差额(负数代表少付)
                    if (difference.compareTo(BigDecimal.ZERO) >= 0) {
                        poPaymentApplication.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                        payStatusNew = 2;
                        if (difference.compareTo(BigDecimal.ZERO)>0){
                            financePayment.setStatus("A");  //状态:0-撤回,1-待申请,2-申请,3-申请通过,4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成,A-付款完成且多付，会生成借款
                        }

                    }
                    poPaymentApplicationDao.update(poPaymentApplication);
                }else {
                    poOrdersPrepayment.setLockedAmount(lockedAmount); //锁定(正在申批中)金额
                    poOrdersPrepayment.setPaidAmount(paidAmount); //'已付金额'
                    poOrdersPrepayment.setBalanceAmount(difference);  //差额(负数代表少付)
                    if (difference.compareTo(BigDecimal.ZERO) >= 0) {
                        poOrdersPrepayment.setPayStatus(2);  //支付现状:0-未支付,1-部分支付,2-支付完成
                        payStatusNew = 2;
                        if (difference.compareTo(BigDecimal.ZERO)>0){
                            financePayment.setStatus("A");  //状态:0-撤回,1-待申请,2-申请,3-申请通过,4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成,A-付款完成且多付，会生成借款
                        }
                    }
                    poOrdersPrepaymentDao.update(poOrdersPrepayment);
                }

                //将新的付款方式更新
                financePayment.setAccountId(accountId);
                financePayment.setMethod(method);
                if ("6".equals(method)) {  //内部的转账支票要转为3-转账支票(实体类中的)，根据票id区分是内部还是外部
                    financePayment.setMethod("3");
                }
                financePayment.setFactMethod(financePayment.getMethod());
                financePayment.setPlanMethod(financePayment.getMethod());
                financePayment.setSummary(summary);
                financePayment.setFactDate(factDate);
                financePayment.setPlanDate(planDate);
                financePayment.setPlanAmount(new BigDecimal(factAmount));
                financePayment.setFactAmount(new BigDecimal(factAmount));
                financePaymentDao.update(financePayment);

                //付款方式修改的流程
                ApprovalProcess process1 = new ApprovalProcess();
                process1.setApproveStatus("2");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                process1.setLevel(1);
                process1.setToUser(user.getUserID());  //审批人id
                process1.setToUserName(approvalProcess.getToUserName());  //审批人总称
                process1.setUserName(user.getUserName()); //审批人名称
                process1.setCreateDate(new Date());
                process1.setOrg(user.getOid());
                process1.setFromUser(approvalProcess.getFromUser());
                process1.setBusiness(approvalProcess.getBusiness());
                process1.setBusinessType(businessTypeUpdateMethod);
                process1.setHandleTime(new Date());
                process1.setBusinessGroup(financePayment.getId().toString());
                approvalProcessDao.save(process1);  //结束

                if (StringUtils.isEmpty(oppositeCorp)) {  //收款单位
                    SrmSupplier srmSupplier = getSupplier(poOrders.getId());  //供应商信息
                    oppositeCorp = srmSupplier.getFullName();
                }
                HashMap<String, Object> hashMap = rejectSendType(poPaymentApplication, poOrders, null, poOrdersPrepayment);  //推送的内容
                //修改后的数据处理
                if ("1".equals(method)) {   //现金直接进账
                    map = cashPurchase(user, poPaymentApplication, poOrders, poOrdersPrepayment,null, amount, bullAmount, approvalProcess.getBusiness(), source, financePayment.getMethod(), financePayment.getAccountId(), summary, factDate, financePayment);  //现金进账处理
                    //修改前的数据处理
                    financeCommonService.updateBeforeFinancePayment(user, financePayment, financeAccountBill, null);
                    Integer approvalUser = approvalProcess.getToUser(); //原来的审批人

                    //现金直接进账，无论哪步修改的，审批都直接完成
                    approvalProcess.setApproveStatus("2");  //只有批准
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setToUser(user.getUserID());
                    approvalProcess.setBusinessType(businessTypePay);
                    approvalProcess.setCreateDate(new Date());
                    approvalProcessDao.update(approvalProcess);

                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                    for (UserPopedom u : userPopedomList) {
                        if (1 == type) {  //待复核修改的
                            //财务审批人待复核-1
                            this.purchaseInvoiceRejectSend(0, -1, hashMap, u.getUserId(), "/reviewedFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                        } else {  //待付款修改的
                            //出纳-待付款-1
                            this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/payFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                        }
                    }
                    if (1 == type) {
                        //复核审批人待复核-1
                        this.purchaseInvoiceRejectSend(-1, -1, hashMap, approvalUser, "/reviewedApprovalHandle", null, null, "purchaseFinanceReimburse");
                    }
                    this.commonEnd(poPaymentApplication, poOrders.getId(), poOrdersPrepayment,financePayment,payStatusNew);
                    if ("5".equals(source)) {
                        //申请人发消息
                        Integer applicationId = Integer.valueOf("36" + financePayment.getId());  //id前的“36”是前端要求的，进行判断详情页面，票据有付款方式
                        if ("2".equals(poPaymentApplication.getType())) {    //2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据
                            userSuspendMsgService.saveUserSuspendMsg(1, "您提交的采购报销已处理完成", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail", applicationId);  //给前端要查看详情的链接
                        } else {    //3-仅提交付款申请,不录入票据
                            userSuspendMsgService.saveUserSuspendMsg(1, "您提交的付款已获审批通过！", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "purchaseInvoiceDetail", applicationId);  //给前端要查看详情的链接
                        }
                    } else {  //预付款的
                        Integer poOrdersPaymentId = Integer.valueOf("38" + financePayment.getId());  //id前的“38”是前端要求的，进行判断详情页面，预付款有付款方式的
                        userSuspendMsgService.saveUserSuspendMsg(1, "您提交的预付款已获审批通过！", "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "advancePaymentDetail", poOrdersPaymentId);  //给前端要查看详情的链接
                    }
                } else {   //其他付款方式的需要进入待复核
                    FinanceAccountBill financeAccountBillNew = new FinanceAccountBill();
                    if (!"5".equals(method)) {  //银行转账的不用走此流程
                        map = invoicePurchase(user, amount, bullAmount, approvalProcess.getBusiness(), source, financePayment, method, financePayment.getAccountId(), financePayment.getSummary(), invoiceId, expireDate, receiveDate, receiver, operator, oppositeCorp);
                        financeAccountBillNew = (FinanceAccountBill) map.get("financeAccountBill");
                    }

                    //修改前的数据处理
                    financeCommonService.updateBeforeFinancePayment(user, financePayment, financeAccountBill, financeAccountBillNew);

                    if (1 == type) {   //1-待复核修改的
//                    approvalProcess.setApproveStatus("1");  //只有批准
                        approvalProcess.setHandleTime(new Date());
                        approvalProcess.setCreateDate(new Date());
                        approvalProcessDao.update(approvalProcess);
                        //没有角标的变化

                    } else {   //2-待付款修改的
                        //待复核的流程【原来的待付款流程改为待复核流程】
                        User user1 = userService.getUserByRoleCode(user.getOid(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                        approvalProcess.setApproveStatus("1");  //只有批准
                        approvalProcess.setUserName(user1.getUserName());
                        approvalProcess.setHandleTime(new Date());
                        approvalProcess.setToUser(user1.getUserID());
                        approvalProcess.setCreateDate(new Date());
                        approvalProcess.setBusinessType(businessTypeReview);
                        approvalProcessDao.update(approvalProcess);

                        financePayment.setAuditState("7");//7-付款方式修改
                        financePayment.setAuditState("2");//付款复核状态:0-暂存,1-录入,2-提交,3-复核通过,4-复核驳回
                        financePaymentDao.update(financePayment);

                        List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                        for (UserPopedom u : userPopedomList) {  //待付款修改的
                            //出纳-待付款-1
                            this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/payFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                            //出纳-待复核+1
                            this.purchaseInvoiceRejectSend(0, 1, hashMap, u.getUserId(), "/reviewedFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                        }

                        //复核审批人待复核+1
                        this.purchaseInvoiceRejectSend(1, 1, hashMap, user1.getUserID(), "/reviewedApprovalHandle", null, null, "purchaseFinanceReimburse");
                    }
                }

                Integer businessType = null;
                //payStatusOld--支付现状:0-未支付,1-部分支付,2-支付完成  当payStatusOld与payStatusNew相等时，上面已经处理
                if (1==payStatusOld&&2==payStatusNew){  //原本的未支付完，修改后的支付完了，将可付款中的数据减去
                    if ("5".equals(source)){
                        poPaymentApplication.setApproveStatus("2"); //票据处理审批状态:0-未申请,1-申请提交(正在走财务-出纳审批的),2-通过审核,3-否决审核,4-撤回
                        poPaymentApplicationDao.update(poPaymentApplication);
                        businessType = 50; //50-可付款（采购的票款处理,出纳的可付款）
                    }else {
                        poOrdersPrepayment.setPrepaymentStatus("2"); //预付款审批状态:0-未申请,1-申请提交(正在走财务-出纳审批的),2-通过审核,3-否决审核,4-撤回
                        poOrdersPrepaymentDao.update(poOrdersPrepayment);
                        businessType = 64; //64-可付款(1.229采购的预付款中)
                    }
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //财务审批人可付款-1
                        this.purchaseInvoiceRejectSend(-1, -1, hashMap, u.getUserId(), "/payableFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                    }
                    ApprovalProcess approvalProcess1 = approvalProcessService.getApprovalProcessByBusToUser(approvalProcess.getBusiness(),businessType,null,"1");
                    if (approvalProcess1!=null&&"1".equals(approvalProcess1.getApproveStatus())){  //将可付款中的审批流程删除
                        approvalProcessDao.delete(approvalProcess1);
                    }
                }else if (2==payStatusOld&&1==payStatusNew){  //原本是支付完的，修改后变成未支付完
                    if ("5".equals(source)){
                        poPaymentApplication.setApproveStatus("1"); //票据处理审批状态:0-未申请,1-申请提交(正在走财务-出纳审批的),2-通过审核,3-否决审核,4-撤回
                        poPaymentApplicationDao.update(poPaymentApplication);
                        businessType = 50; //50-可付款（采购的票款处理,出纳的可付款）
                    }else {
                        poOrdersPrepayment.setPrepaymentStatus("1"); //预付款审批状态:0-未申请,1-申请提交(正在走财务-出纳审批的),2-通过审核,3-否决审核,4-撤回
                        poOrdersPrepaymentDao.update(poOrdersPrepayment);
                        businessType = 64; //64-可付款(1.229采购的预付款中)
                    }
                    //原本是状态A的，改为5
                    financePayment.setStatus("5");  //状态:0-撤回,1-待申请,2-申请,3-申请通过,4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成,A-付款完成且多付，会生成借款
                    financePaymentDao.update(financePayment);
                    //财务出纳-可付款的流程
                    ApprovalProcess process = new ApprovalProcess();
                    process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                    process.setLevel(1);
                    process.setToUserName("财务处理者");
                    process.setToMid("lp");  //报销受理模块
                    process.setCreateDate(new Date());
                    process.setOrg(user.getOid());
                    process.setUserName("财务处理者");
                    process.setFromUser(approvalProcess.getFromUser());
                    process.setAskName(approvalProcess.getAskName());
                    process.setHandleTime(new Date());
                    process.setBusiness(approvalProcess.getBusiness());
                    process.setBusinessType(businessType);
                    approvalProcessDao.save(process);
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //财务审批人可付款+1
                        this.purchaseInvoiceRejectSend(1, 1, hashMap, u.getUserId(), "/payableFinanceHandle", null, null, "purchasePaymentFinanceApproval");
                    }
                }
                map.put("content", "修改成功");
            }
        }
        return map;
    }

    @Override
    public PoPaymentInvoiceAttachment getInvoicePictureById(Integer invoiceId) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PoPaymentInvoiceAttachment where invoice=:invoice";
        map.put("invoice",invoiceId);
        return (PoPaymentInvoiceAttachment) poPaymentInvoiceAttachmentDao.getByHQLWithNamedParams(hql,map);
    }

    @Override
    public List<PoPaymentInvoiceAttachment> getInvoicePicturesById(Integer invoiceId) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PoPaymentInvoiceAttachment where invoice=:invoice";
        map.put("invoice",invoiceId);
        return poPaymentInvoiceAttachmentDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override     //invoiceCategory（发票种类:1-专用票,2-普通票,3-收据,4-其他发票）  feeCategory（费用类别 在此默认为“采购材料”）
    public Map<String, Object> getInvoiceItemDetail(Integer applicationId, String invoiceCategory, String feeCategory) {
        Map<String,Object> map = new HashMap<>();
        Map<String,Object> map1 = new HashMap<>();
        if (StringUtils.isNotEmpty(invoiceCategory)){
            String hql = "from PoPaymentInvoice where application=:application and invoiceCategory=:invoiceCategory and relativeBillQuantity!=0";
            map1.put("application",applicationId);
            map1.put("invoiceCategory",invoiceCategory);
            List<PoPaymentInvoice> poPaymentInvoices = poPaymentInvoiceDao.getListByHQLWithNamedParams(hql,map1);

            for (PoPaymentInvoice poPaymentInvoice:poPaymentInvoices) {
                List<PoPaymentInvoiceItem> poPaymentInvoiceItems = getInvoicesItemById(applicationId,poPaymentInvoice.getId());
                poPaymentInvoice.setPoPaymentInvoiceItems(poPaymentInvoiceItems);
                List<PoPaymentInvoiceAttachment> poPaymentInvoiceAttachments = getInvoicePicturesById(poPaymentInvoice.getId());
                poPaymentInvoice.setPoPaymentInvoiceAttachments(poPaymentInvoiceAttachments);
            }
            map.put("poPaymentInvoices",poPaymentInvoices);
        }else if(StringUtils.isNotEmpty(feeCategory)){
            List<Map<String,Object>> listMap = new ArrayList<>();
            String hql = "select itemName,amount,invoice from PoPaymentInvoiceItem where application=:application";
            map1.put("application",applicationId);
            List<Object[]> listObjects = poPaymentInvoiceItemDao.getListByHQLWithNamedParams(hql,map1);
            for (Object[] listObject:listObjects) {
                Map<String,Object> map2 = new HashMap<>();
                map2.put("itemName",listObject[0]);  //明细项名称(货物或应税劳务、服务名称)
                map2.put("amount",listObject[1]);   //金额

                List<PoPaymentInvoiceAttachment> poPaymentInvoiceAttachments = getInvoicePicturesById((Integer) listObject[2]);
                map2.put("pictures",poPaymentInvoiceAttachments);
                listMap.add(map2);
            }
            map.put("listMap",listMap);
        }
        return map;
    }

    @Override
    public PoPaymentInvoiceAttachment getInvoiceAttachment(Integer id) {
        return poPaymentInvoiceAttachmentDao.get(id);
    }

    @Override
    public PoPaymentApplication getApplicationById(Integer applicationId) {
        return poPaymentApplicationDao.get(applicationId);
    }

    @Override
    public List<Map<String, Object>> getAdvancePayments(Integer org,Integer toUser, Integer fromUser,Integer businessType,String approveStatus,String prepaymentStatus,Integer type) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<Object[]> poOrderPrepayments = poService.getPurchaseOrderPrepayments(org,toUser,fromUser, businessType, null, approveStatus, prepaymentStatus,null,null,null,type);
//        for (PoOrdersPrepayment poOrderPrepayment:poOrderPrepayments) {
        for (Object[] ob:poOrderPrepayments) {
            Long prepaymentId = (Long) ob[0];
            HashMap<String, Object> map = new HashMap<>();
            if (prepaymentId!=null) {
                PoOrdersPrepayment poOrderPrepayment = poOrdersPrepaymentDao.get(prepaymentId);
                map = rejectSendType(null, null, poOrderPrepayment.getOrders(), poOrderPrepayment);
            }
            if (map.size()>0){
                String businessGroup = (String) ob[1];
                if (StringUtils.isNotEmpty(businessGroup)){
                    FinancePayment financePayment = financePaymentDao.get(Integer.parseInt(businessGroup));
                    if (!"9".equals(financePayment.getStatus())){
                        map.put("businessGroup",businessGroup);
                        mapList.add(map);
                    }
                }else {
                    map.put("businessGroup",businessGroup);
                    mapList.add(map);
                }
            }
        }
        return mapList;
    }

    @Override
    public Map<String, Object> advancePaymentDetail(Integer orderPrepaymentId,Integer financePaymentId) {
        Map<String,Object> map = new HashMap<>();
        PoOrdersPrepayment poOrdersPrepayment = new PoOrdersPrepayment();
        FinancePayment financePayment = new FinancePayment();
        FinanceAccount financeAccount = new FinanceAccount();
        FinanceChequeDetail financeChequeDetail = new FinanceChequeDetail();
        FinanceReturn financeReturn = new FinanceReturn();
        Integer invoiceType = 1;  //主要来区分是内部转账支票还是外部转账支票
        Integer status = 0;
        if (orderPrepaymentId!=null) {
            poOrdersPrepayment = poOrdersPrepaymentDao.get(orderPrepaymentId.longValue());  //订单预付款信息
        }
        if (financePaymentId!=null){
            financePayment = financePaymentDao.get(financePaymentId);
            if (orderPrepaymentId==null){
                orderPrepaymentId = financePayment.getBusiness();
                poOrdersPrepayment = poOrdersPrepaymentDao.get(orderPrepaymentId.longValue());  //订单预付款信息
            }
        }
        if (financePaymentId != null) {
            if (financePayment.getAccountId() != null) {
                financeAccount = financeAccountDao.get(financePayment.getAccountId());
            }

            if ("3".equals(financePayment.getMethod()) || "4".equals(financePayment.getMethod())) {
                FinanceAccountBill financeAccountBill = financeAccountBillDao.get(financePayment.getAccountBill());
                if (financeAccountBill.getCheque_() != null) {
                    invoiceType = 2;  //内部票据
                    financeChequeDetail = financeChequeDetailDao.get(financeAccountBill.getCheque_());
                } else if (financeAccountBill.getFinanceReturn().getId() != null) {
                    invoiceType = 3;  //外部转账票据
                    financeReturn = financeAccountBill.getFinanceReturn();
                }
            }

            List<FinancePaymentHistory> financePaymentHistorys = financePaymentService.getPaymentHistoryByBusiness(orderPrepaymentId, financePaymentId, "prepayment");
            if (financePaymentHistorys.size() >= 2) {
                status = 1;   //付款方式进行了修改
            }
        }
        Integer orderId = poOrdersPrepayment.getOrders();
        PoOrders poOrders = poOrdersDao.get(orderId);  //订单详情
        SrmSupplier srmSupplier = getSupplier(orderId);  //供应商信息
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcesses(null,orderPrepaymentId,financePaymentId,1,63,64,65,66,67);
        Integer num = financeCommonService.getFinancePaymentNum(orderPrepaymentId,"10","prepayment"); //待复核的付款笔数

        map.put("num",num);//待复核的付款笔数
        map.put("poOrders",poOrders);//订单详情
        map.put("srmSupplier",srmSupplier);//供应商信息
        map.put("poOrdersPrepayment",poOrdersPrepayment);//订单预付款信息
        map.put("approvalProcessList",approvalProcessList); //审批流程
        map.put("financePayment",financePayment);  //付款方式信息
        map.put("financeAccount",financeAccount);  //账户信息
        map.put("invoiceType",invoiceType);  // 1-其他的付款方式  2-内部转账票据  3-外部转账票据
        map.put("financeChequeDetail",financeChequeDetail);  //内部转账支票信息
        map.put("financeReturn",financeReturn);   //外部转账支票或者承兑汇票信息
        map.put("status",status);   //0-付款方式没有进行修改  1-付款方式进行修改
        return map;
    }

    @Override
    public Map<String, Object> advancePaymentPayApproved(User user,Integer approvalProcessId) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        PoOrdersPrepayment poOrdersPrepayment = poOrdersPrepaymentDao.get(approvalProcess.getBusiness().longValue());
        if (!"1".equals(approvalProcess.getApproveStatus())){
            map.put("state",2);
            map.put("content","不可重复操作");
        }else {
            approvalProcess.setApproveStatus("2");  //只有批准
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setToUser(user.getUserID());
            approvalProcessDao.update(approvalProcess);

            //财务出纳-可付款的流程
            ApprovalProcess process = new ApprovalProcess();
            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
            process.setLevel(1);
            process.setToUserName("财务处理者");
            process.setToMid("lp");  //报销受理模块
            process.setCreateDate(new Date());
            process.setOrg(user.getOid());
            process.setUserName("财务处理者");
            process.setFromUser(approvalProcess.getFromUser());
            process.setAskName(approvalProcess.getAskName());
            process.setHandleTime(new Date());
            process.setBusiness(approvalProcess.getBusiness());
            process.setBusinessType(64);
            approvalProcessDao.save(process);

            HashMap<String,Object> hashMap = rejectSendType(null,null,poOrdersPrepayment.getOrders(),poOrdersPrepayment);  //推送的内容
            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
            for (UserPopedom u : userPopedomList) {
                //财务审批人可付款+1
                this.purchaseInvoiceRejectSend(1,1,hashMap, u.getUserId(), "/payableFinanceHandle",null,null,"purchasePaymentFinanceApproval");

                //财务审批人待付款审批-1
                this.purchaseInvoiceRejectSend(0,-1,hashMap, u.getUserId(), "/payApprovalFinanceHandle",null,null,"purchasePaymentFinanceApproval");
            }
            //付款审批者-采购的预付款-待处理-1
            this.purchaseInvoiceRejectSend(-1,-1,hashMap, user.getUserID(), "/advancePaymentPayHandle",null,null,"purchaseAdvancePayApproval");
            //付款审批者-采购的预付款-已批准
            this.purchaseInvoiceRejectSend(0,1,hashMap, user.getUserID(), "/advancePaymentPayApproved",null,null,"purchaseAdvancePayApproval");
            map.put("content","操作成功");
            map.put("state",1);
        }
        return map;
    }


    @Override
    public PoOrdersPrepayment getPrepaymentById(Long prepaymentId) {
        PoOrdersPrepayment poOrdersPrepayment = poOrdersPrepaymentDao.get(prepaymentId);
        return poOrdersPrepayment;
    }



    @Override
    public Map<String, Object> payDetail(Integer applicationId, Integer orderPrepaymentId,String status,String businessType) {
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> invoiceDetails = new ArrayList<>();
        if (applicationId!=null){
            if (StringUtils.isEmpty(businessType)){
                businessType = "procurement"; //票据处理
            }
            invoiceDetails = getPurchaseInvoicePayDetail(applicationId,businessType,status);
        }else if (orderPrepaymentId!=null){
            invoiceDetails = getPurchaseInvoicePayDetail(orderPrepaymentId,"prepayment",status);
        }
        map.put("invoiceDetails",invoiceDetails);
        return map;
    }

    private List<Map<String,Object>> getPurchaseInvoicePayDetail(Integer business,String businessType,String status){
        Map<String,Object> map = new HashMap<>();
        //除现金外的其他付款方式
        String contition = "select fp.id,fp.planAmount,fp.method,fp.accountBill,fp.updateDate,fp.updateName,b.cheque_,b.returnBill from FinancePayment fp,FinanceAccountBill b where (fp.accountBill=b.id or fp.method in ('1','5'))";
        if (StringUtils.isNotEmpty(status)){
            if (status.equals("9")){
                contition+=" and fp.status='9'";
            }else {
                contition+=" and fp.status!='9'";
            }
        }
        if (business!=null){
            contition+=" and fp.business=:business";
            map.put("business",business);
        }
        if (businessType!=null){
            contition+=" and fp.businessType=:businessType";
            map.put("businessType",businessType);
        }
        contition+=" group by fp.id";
        List<Object[]> objects = financePaymentDao.getListByHQLWithNamedParams(contition,map);
        List<Map<String,Object>> detail = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("id",ob[0]);  //id
            map1.put("amount",ob[1]);  //金额
            map1.put("method",ob[2]);  //支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            map1.put("accountBill",ob[3]);  //票据id
            map1.put("updateDate",ob[4]);  //修改时间
            map1.put("updateName",ob[5]);  //修改人
            map1.put("cheque",ob[6]);  //内部支出票据
            map1.put("returnBill",ob[7]);  //外部的
            detail.add(map1);
        }
        return detail;
    }

    @Override
    public Map<String, Object> getMostDebitLoan(User user, String yearMonth, Integer finishType, PageInfo pageInfo) {
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> poLoans = new ArrayList<>();
//        Byte type = LoanBizService.LoanBizType.prepayment.getIndex();
        List<Map<String,Object>> advancePaymentLoans = this.getMostDebitLoanList(user.getOid(),LoanBizService.LoanBizType.prepayment.getIndex(),yearMonth,finishType,pageInfo);  //预付款的
        if (advancePaymentLoans.size()>0){
            poLoans.addAll(advancePaymentLoans);
        }
//        Byte type1 = LoanBizService.LoanBizType.procurement.getIndex();
        List<Map<String,Object>> purchaseInvoiceLoans = this.getMostDebitLoanList(user.getOid(),LoanBizService.LoanBizType.procurement.getIndex(),yearMonth,finishType,pageInfo);  //票款处理的
        if (purchaseInvoiceLoans.size()>0){
            poLoans.addAll(purchaseInvoiceLoans);
        }
//        Byte type2 = LoanBizService.LoanBizType.payOverpayment.getIndex();
        List<Map<String,Object>> payOverpayments = this.getMostDebitLoanList(user.getOid(),LoanBizService.LoanBizType.payOverpayment.getIndex(),yearMonth,finishType,pageInfo);  //来源是多收来的款进入的需收回的款
        if (payOverpayments.size()>0){
            poLoans.addAll(payOverpayments);
        }
        map.put("poLoans",poLoans);
        return map;
    }

    private List<Map<String,Object>> getMostDebitLoanList(Integer org,Byte type,String yearMonth,Integer finishType,PageInfo pageInfo){
        Map<String,Object> map = new HashMap<>();
        String hql = "select pl.id,pl.financePayment,pl.type,pl.amount,pl.createName,pl.createDate,pl.supplierName,pl.finishTime,ppa.amount,ppa.paidAmount,ppa.payNum from LoanBiz pl,PoPaymentApplication ppa where pl.paymentApply=ppa.id";  //票据的
        if (LoanBizService.LoanBizType.prepayment.getIndex().compareTo(type)==0) { //预付款的
            hql = "select pl.id,pl.financePayment,pl.type,pl.amount,pl.createName,pl.createDate,pl.supplierName,pl.finishTime,pop.planAmount,pop.paidAmount,pop.payNum from LoanBiz pl,PoOrdersPrepayment pop where pl.ordersPrepayment=pop.id";   //预付款的
        }
        if (LoanBizService.LoanBizType.payOverpayment.getIndex().compareTo(type)==0) { //支付多收款的
            hql = "select pl.id,pl.financePayment,pl.type,pl.amount,pl.createName,pl.createDate,pl.supplierName,pl.finishTime,pop.amount,pop.paidAmount,pop.paidTimes from LoanBiz pl,LoanBiz pop where pl.paymentApply=pop.id";   //预付款的
        }
        if (org!=null){
            hql+=" and pl.org=:org";
            map.put("org",org);
        }
        if (type!=null){
            hql+=" and pl.type=:type";
            map.put("type",type);
        }
        if (StringUtils.isNotEmpty(yearMonth)){
            hql+=" and date_format(pl.createDate,'%Y%m')=:yearMonth";
            map.put("yearMonth",yearMonth);
        }
        if (finishType!=null&&1==finishType){
            hql+=" and pl.finishTime is not null ";
        }else {
            hql+=" and pl.finishTime is null ";
        }
        List<Object[]> objects = loanBizDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        List<Map<String,Object>> listMap = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("id",ob[0]);  //借款id
            map1.put("financePaymentId",ob[1]);  //付款方式id
            map1.put("type",ob[2]);  //类型:1-货款(票据处理的),2-预付款
            map1.put("amount",ob[3]);  //收回金额(产生借款的金额)
            map1.put("createName",ob[4]);  //创建人
            map1.put("createDate",ob[5]);  //创建时间
            map1.put("supplierName",ob[6]);  //供应商
            map1.put("finishTime",ob[7]);  //完结时间
            map1.put("planAmount",ob[8]);  //申请金额
            map1.put("paidAmount",ob[9]);  //已支付金额
            map1.put("payNum",ob[10]);  //支付笔数
            listMap.add(map1);
        }
        return listMap;
    }

    @Override
    public void addLoanBizAll(User user,Integer paymentApply, FinancePayment financePayment, Integer ordersPrepayment,Byte bizType,Byte type,BigDecimal loanAmount, PoOrders poOrders,String tracePath,Integer supplier, String supplierName,Integer origBiz){
//        PoLoan poLoan = loanService.addPoLoan(user,paymentApply,financePayment.getId(),ordersPrepayment,loanAmount,loanType,srmSupplier.getId(),srmSupplier.getName(),poOrders.getId());
        Integer poOrdersId = null;
        if (poOrders!=null&&poOrders.getId()!=null){
            poOrdersId = poOrders.getId();
        }

        LoanBiz loanBiz = loanBizService.addLoanBiz(user,paymentApply,financePayment.getId(),ordersPrepayment,bizType,type,loanAmount,supplier,supplierName,poOrdersId,origBiz,tracePath,null);

        //财务出纳-可付款的流程
        ApprovalProcess process = new ApprovalProcess();
        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
        process.setLevel(1);
        process.setToUserName("财务处理者");
        process.setToMid("lp");  //报销受理模块
        process.setCreateDate(new Date());
        process.setOrg(user.getOid());
        process.setUserName("财务处理者");
        process.setFromUser(financePayment.getCreator());
        process.setAskName(financePayment.getCreateName());
        process.setHandleTime(new Date());
//        process.setBusiness(poLoan.getId());
        process.setBusiness(loanBiz.getId());
        process.setBusinessType(69); //69-需收回的款待处理
        approvalProcessDao.save(process);

        List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
        for (UserPopedom u : userPopedomList) {
            //需收回的款 审批人待处理+1
            this.loanBizRejectSend(1,1,loanBiz, u.getUserId(), "/poLoanHandle",null,null,"needRecoveredAmount");
        }
    }

    //需收回的款推送的接口
    @Override
    public void loanBizRejectSend(int loginNum,int operate,LoanBiz loanBiz,Integer toUserId,String pass, String title, String content, String code){
        System.out.println("需收回的款推送开始:"+new Date());
        System.out.println("需收回的款id：" + loanBiz.getId() + " userId: " + toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("loanBiz", loanBiz);
        User user = userDao.get(toUserId);  // 推送人
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,user,code);
        System.out.println("需收回的款推送结束:"+new Date());
    }
}
