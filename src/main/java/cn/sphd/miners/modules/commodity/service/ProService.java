package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * 商品分解
 */
public interface ProService {

    /**
     * 录入销售商品
     */
    String addProduct(PdMerchandise product, String commodityMediaList,User user);

    //获取通用商品列表
    //flage true  从专属中查通用商品，需要判断创建人是否是自己  isgl是否关联
    Map<String,Object> getTyProductListByCategory(Integer oid, String category, String param, PageInfo pageInfo, String type, int isgl, Integer userId);
    //获取商品列表
    List<Map<String ,Object>> getProductList(Integer oid,String type,Integer customerId);
    //获取专属商品列表
    Map<String,Object> getZSProductListByCustomer(Integer oid, String customerId, String param,PageInfo pageInfo,Integer userId);
    //获取商品信息
    PdMerchandise getProductOne(Integer productId);
    //暂停销售
    String suspendProduct(Integer productId,String state,User user);
    //暂停销售判断
    String suspendProductJudge(Integer productId,String state);
    //删除商品
    String deleteProduct(Integer productId);
    //删除商品判断
    String deleteProductJudge(Integer productId);
    //修改商品基本信息
    String updateProductBase(PdMerchandise product,String commodityMediaList,User user,String type);
    //暂停销售列表
    Map<String,Object> suspendProductList(Integer oid,  String param,PageInfo pageInfo,String type);
    //暂停销售列表
    List<Map<String,Object>> suspendProductListForList(Integer oid,  String param,String type);

    //获取商品基本信息修改记录
    List<Map<String,Object>> getProductRecordBaseList(Integer customerProductId,String operation);
    //获取基本信息修改记录详情
    PdMerchandiseHistory getProductRecordBaseDetails(Integer id);
    //获取商品关联记录
    List<Map<String,Object>> getProductGuanLianList(Integer customerProductId);
    //获取产品关联记录
   Map<String,Object> getPdBaseGuanLianList(Integer oid, String param,String source,String composition,Integer pageNo,Integer pageSize);

    //获取其他信息修改记录详情
    PdMerchandiseHistory getProductRecordOtherDetails(Integer id);
    //获取待关联商品列表
    Map<String,Object> getWaitRelevanceProList(Integer oid, Integer currPage, Integer pageSize);

    //获取已关联商品列表
    Map<String,Object> getAlreadyProList(Integer oid,String param, Integer currPage, Integer pageSize);

    //关联管理--未管理产品查看
     Map<String,Object>  getWaitPdPerList(Integer oid, String param,Integer currPage, Integer pageSize);

    //关联管理--根据产品查询关联的商品
    List< Map<String,Object> > getProductListByPdBase( Integer pdBaseId);

    //确认关联
    String confirmAssociation(Integer pdBaseId,Integer productId,User user);

    //关联查看
    Map<String,Object> getProductCorrelation(Integer productId);

    //产品档案--获取产品列表
    Map<String,Object> getPdBaseList(Integer oid, String category,String process,String composition, String param,String type,Integer currPage, Integer pageSize);
    //产品档案--获取产品列表
    List<Map<String, Object>>  getPdBaseList(Integer oid);

    //产品档案--录入产品
    String addPdBase(PdBase pdBase,String zsProductList,String tyProductId,String resourceList,String cptzImage,String xhImage,User user);

    //产品当啊--获取产品信息
    PdBase getPdBaseOne(Integer pdBaseId);


    //产品档案--修改产品信息
    String updatePdBase(PdBase pdBase,User user,String flag);


    //产品档案--暂停生产
    String suspensionOfPdBase(Integer pdBaseId,String state,User user);
    //产品档案--暂停生产判断
    String suspensionOfPdBaseJudge(Integer pdBaseId,String state);
    //获取产品启停记录
    List<Map<String,Object>> getProductStopAndStartRecord(Integer pdBaseId);
    //产品档案--删除产品
    String deletePdBase(Integer pdBaseId);

    //产品档案--删除产品判断
    String deletePdBaseJudge(Integer pdBaseId);

    //产品/零组件暂停销售列表
    Map<String,Object> suspendPdBaseList(Integer oid,String source, String composition, String param, String type,Integer currPage, Integer pageSize);

    //获取昨天有录入商品的机构
    List<Map<String, Object>> getOrg();

    //统计昨日录入者的录入数量
    List<Map<String,Object>> zrtj(Integer org);
    //获取基本信息修改记录 pdBase
    List<Map<String,Object>> getRecordBaseList(Integer pdBaseId);
    //获取基本信息修改记录详情
    Map<String,Object> getRecordBaseDetails(Integer id,Integer frontId);
    //获取商品收获地址
    List<Map<String,Object>> getProductAddressList(Integer productId);
    //获取商品收获地址
    List<Map<String,Object>> getProductAddressListHistory(Integer productHistoryId);
    //根据代号获取使用过该代号的商品
    List<Map<String,Object>> getProductHistoryByCode(Integer oid,String code);
    //根据代号获取使用过该代号的商品
    List<Map<String,Object>> getProductByCode(Integer oid,String code);
    //
    String editPdMerchandiseInvoice(PdMerchandiseInvoice merchandiseInvoice);

    PdMerchandiseInvoice getPdMerchandiseInvoice(Integer  merchandise,Integer type);

    List<PdMerchandiseInvoiceHistory> getPdMerchandiseInvoiceRecordList(Integer merchandise,Integer type);

    PdMerchandiseInvoiceHistory getPdMerchandiseInvoiceRecordDetails(Integer id);

    String updatePdMerchandisePrice(PdMerchandise product);

    String updatePdMerchandisePrice(PdMerchandise product,PdMerchandiseHistory history);

    List<PdMerchandiseHistory> getPdMerchandisePriceRecordList(Integer merchandise,String operation);

    PdMerchandiseHistory getPdMerchandisePriceRecordDetails(Integer id);

    PdMerchandiseHistory pdMerchandiseHistory(Integer id,String operation,String state);
    //删除产品图纸
    String removePdBaseImage(Integer product,Integer category,User user);
    //上传，更换产品图纸
    String saveOrUpdatePdBaseImage(PdImage pdImage,User user);
    //获取图纸操作记录
    List<PdImageHistory> getPdBaseImageRecord(Integer product,Integer category);
    //资源文件操作记录
    List<PdResourceHistory> getPdResourceRecord(Integer product);
    //资源文件关联
    String saveOrUpdatePdResource(PdResource pdResource,User user);
    //解除文件关联
    String securePdResource(Integer product,Integer resourceId,User user);
}
