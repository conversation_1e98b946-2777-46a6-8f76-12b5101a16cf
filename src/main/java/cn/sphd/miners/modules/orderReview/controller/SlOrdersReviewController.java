package cn.sphd.miners.modules.orderReview.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReview;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReviewHistory;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersReview;
import cn.sphd.miners.modules.orderReview.mapper.TSlOrdersItemReviewMapper;
import cn.sphd.miners.modules.orderReview.service.ITSlOrdersItemReviewHistoryService;
import cn.sphd.miners.modules.orderReview.service.ITSlOrdersItemReviewService;
import cn.sphd.miners.modules.orderReview.service.ITSlOrdersReviewService;
import cn.sphd.miners.modules.sales.entity.SlOrders;
import cn.sphd.miners.modules.sales.entity.SlOrdersHistory;
import cn.sphd.miners.modules.sales.entity.SlOrdersItem;
import cn.sphd.miners.modules.sales.entity.SlOrdersItemReview;
import cn.sphd.miners.modules.sales.service.SaleService;
import cn.sphd.miners.modules.sales.service.SlOrdersService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.*;

@Controller
@RequestMapping("/orders/review")
public class SlOrdersReviewController {
    @Autowired
    private ITSlOrdersReviewService tSlOrdersReviewService;
    @Autowired
    private ITSlOrdersItemReviewService itemReviewService;
    @Autowired
    private ITSlOrdersItemReviewHistoryService itemReviewHistoryService;
    @Autowired
    SlOrdersService ordersService;

    @Autowired
    SaleService saleService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;

    @Autowired
    TSlOrdersItemReviewMapper tSlOrdersItemReviewMapper;
    /**
     * 查询列表
     */

    @RequestMapping("/orderList")
    @ResponseBody
    public List<Map<String, Object>> orderList(TSlOrdersItemReview ordersItemReview, User user,Integer produce, PageInfo pageInfo) {

        ordersItemReview.setOrg(Long.valueOf(user.getOid()));
        ordersItemReview.setCreator(Long.valueOf(user.getUserID()));
        if(produce!=null){
            ordersItemReview.setReviewer(user.getUserID().longValue());
        }
        List<Map<String, Object>> records = tSlOrdersReviewService.selectTSlOrdersReviewListByCondition(ordersItemReview, pageInfo);

        //查找待销售评审的订单

        //查找待生产评审的订单

        return records;
    }

    //去评审
    @RequestMapping("reviewDetails")
    @ResponseBody
    public Map reviewDetails(Integer orderId, User user) {

        Map records = itemReviewService.getReviewDetails(orderId, user);
        Map orderBase = saleService.getOrderBase(orderId);
        records.put("orderBase", orderBase.get("data"));
        TSlOrdersItemReview tr = new TSlOrdersItemReview();
        tr.setOrders(orderId.longValue());

        List<TSlOrdersItemReview> itemReviews = itemReviewService.selectTSlOrdersItemReviewList(tr);
        if (itemReviews != null&&!itemReviews.isEmpty()) {
            records.put("reviewsVersion",itemReviews.get(0).getVersionNo());
        }

        return records;
    }

    @RequestMapping("/produceHandle")
    @ResponseBody
    public Map produceHandle(@RequestBody(required = false) List<TSlOrdersItemReview> ordersItemReviews, User user, Integer orderId, Long versionNo) {

        //判断当前评审时 销售有没有修改订单
        SlOrders orders = ordersService.getById(orderId);
        if (orders != null) {
            if (versionNo != null) {
                Long version = orders.getVersionNo() == null ? 0L : orders.getVersionNo();
                if (!version.equals(versionNo)) {
                    if (orders.getOperation() == 3) {
                        String msg = "操作失败！因为销售已调整了要货需求！";


                        return error(msg);

                    }
                    if (orders.getOperation() == 6) {


                        String msg = "操作失败！因为销售刚刚修改了订单，您需重新评审！";
                        return error(msg);
                    }
                }
            }
            //判断当前的评审人
            if (!orders.getProductionPrincipal().equals(user.getUserID())) {
                return error("操作失败！因为" + orders.getSn() + "的生产评审人已换为他人！");
            }
        }



        //如果又分批情况 先拆分订单 拆分后 先删除被拆分的明细 删除 分批数据
        List<Integer> deleteItems = new ArrayList<>();
        SlOrdersItem ordersItem;

        for (TSlOrdersItemReview ordersItemReview : ordersItemReviews) {
            if (ordersItemReview.getId() == null) {
                TSlOrdersItemReview tr = new TSlOrdersItemReview();
                tr.setOrders(orderId.longValue());

                List<TSlOrdersItemReview> itemReviews = itemReviewService.selectTSlOrdersItemReviewList(tr);

                ordersItemReview.setOrg(Long.valueOf(user.getOid()));
                ordersItemReview.setOperation(3L);
                ordersItemReview.setCreator(Long.valueOf(user.getUserID()));
                ordersItemReview.setCreateName(user.getUserName());
                ordersItemReview.setCreateDate(new Date());
                ordersItemReview.setVersionNo(itemReviews.size()+1L);
                itemReviewService.insertTSlOrdersItemReview(ordersItemReview);

                if(ordersItemReview.getOrdersReview()!=null){//传递此数据代表是对分批的进行评审，生成订单明细
                    //加入分批发货数据
                    TSlOrdersItemReview itemReview;
                    itemReview = new TSlOrdersItemReview();
                    itemReview.setOrdersItem(ordersItemReview.getOrdersItem());
                    itemReview.setOrg(Long.valueOf(user.getOid()));
                    itemReview.setState(3L);

                    ordersItem = ordersService.getSlOrdersItemById(Math.toIntExact(ordersItemReview.getOrdersItem()));

                    //获取生产评审明细
                    List<TSlOrdersItemReview> partialShipment =  tSlOrdersItemReviewMapper.selectTSlOrdersItemReviewList(itemReview);

                    if (partialShipment!=null&& !partialShipment.isEmpty()){
                        ordersItem.setOperation(9);//设置为9-已拆分
                        ordersService.updateSlOrdersItem(ordersItem);

                        if(!deleteItems.contains(ordersItem.getId()))
                            deleteItems.add(ordersItem.getId());

                        for (TSlOrdersItemReview tSlOrdersItemReview : partialShipment) {
                            if (tSlOrdersItemReview.getId().equals(ordersItemReview.getOrdersReview())){
                                SlOrdersItem newItem = new SlOrdersItem();
                                BeanUtils.copyProperties(ordersItem,newItem);
                                newItem.setId(null);
                                newItem.setAmount(tSlOrdersItemReview.getNewAmount());
                                newItem.setDeliveryDate(tSlOrdersItemReview.getDeliveryDate());
                                newItem.setDeliveryAddressId(Math.toIntExact(tSlOrdersItemReview.getNewAddressId()));

                                ordersService.addSlOrdersItem(newItem);
                                tSlOrdersItemReviewMapper.deleteTSlOrdersItemReviewById(tSlOrdersItemReview.getId());

                                ordersItemReview.setOrdersItem(Long.valueOf(newItem.getId()));
                                ordersItemReview.setOrdersReview(null);
                                itemReviewService.updateTSlOrdersItemReview(ordersItemReview);
                            }
                        }

                    }


                }


            } else {
                //修改时先查询当前订单状态，若销售已接受 则不允许修改
                if (orders != null)
                    if ("2".equals(orders.getIsScheduled())) {
                        return error("操作失败！因为销售已接受了之前的评审结果。如有问题，请与销售线下沟通！");
                    }

                TSlOrdersItemReviewHistory history = new TSlOrdersItemReviewHistory();
                history.setOrdersItem(ordersItemReview.getOrdersItem());
                List<TSlOrdersItemReviewHistory> reviewHistories = itemReviewHistoryService.selectTSlOrdersItemReviewHistoryList(history);
                //查出当前评审结果
                TSlOrdersItemReview sor = itemReviewService.selectTSlOrdersItemReviewById(ordersItemReview.getId());
                //将当前评审结果存为历史
                TSlOrdersItemReviewHistory slOrdersItemReviewHistory = new TSlOrdersItemReviewHistory();
                BeanUtils.copyProperties(sor, slOrdersItemReviewHistory);
                slOrdersItemReviewHistory.setItemReview(ordersItemReview.getId());
                slOrdersItemReviewHistory.setCreateDate(new Date());
                slOrdersItemReviewHistory.setCreator(Long.valueOf(user.getUserID()));
                slOrdersItemReviewHistory.setCreateName(user.getUserName());
                slOrdersItemReviewHistory.setCreateDate(new Date());
                slOrdersItemReviewHistory.setVersionNo(reviewHistories.size()+1L);
                slOrdersItemReviewHistory.setId(null);
                itemReviewHistoryService.insertTSlOrdersItemReviewHistory(slOrdersItemReviewHistory);

                //改过之后更新versionNo
                sor.setVersionNo(sor.getVersionNo()==null?1L:sor.getVersionNo()+1L);
                if(ordersItemReview.getIsScheduled()!=null)
                    sor.setIsScheduled(ordersItemReview.getIsScheduled());
                sor.setScheduledAmount(ordersItemReview.getScheduledAmount());

                sor.setSurplusDate(ordersItemReview.getSurplusDate());
                sor.setMemo(ordersItemReview.getMemo());
                sor.setOperation(4L);
                itemReviewService.updateTSlOrdersItemReview(sor);

            }
        }
        TSlOrdersReview ordersReview = tSlOrdersReviewService.selectTSlOrdersReviewByOrderId(Long.valueOf(orderId));
        if (ordersReview == null) {
            ordersReview = new TSlOrdersReview();

            ordersReview.setOrders(Long.valueOf(orderId));
            tSlOrdersReviewService.insertTSlOrdersReview(ordersReview);
        }
        ordersReview = tSlOrdersReviewService.selectTSlOrdersReviewByOrderId(Long.valueOf(orderId));
        ordersReview.setPhase(1L);
        ordersReview.setReviewerName(user.getUserName());
        ordersReview.setReviewer(Long.valueOf(user.getUserID()));
        ordersReview.setReviewDate(new Date());

        tSlOrdersReviewService.updateTSlOrdersReview(ordersReview);

        String msg = user.getUserName() + "于" + DateUtil.datefomat(new Date(), 1) + "完成了" + orders.getSn() + "订单的评审，请及时评审";

        userSuspendMsgService.saveUserSuspendMsg(1, "",msg, "", orders.getCreator(), "ordersReview",orders.getId());

        //删除掉已分批的id
        ordersService.deleteSlOrdersItems(deleteItems);

        return success("success");

    }

    @RequestMapping("/saleHandle")
    @ResponseBody
    public AjaxResult saleHandle(Integer orderId, Long state, User user, Long reviewVersion, HttpServletRequest request, HttpServletResponse response) {
        Date d = new Date();
        //获取最新的评审结果
        SlOrders orders = ordersService.getById(orderId);

        if (orders != null) {
            if (state == 2L) {//接受
                //接受时先检测生产有没有改过评审结果u
                if (reviewVersion!=null){
                    TSlOrdersItemReview tr = new TSlOrdersItemReview();
                    tr.setOrders(orders.getId().longValue());

                    List<TSlOrdersItemReview> itemReviews = itemReviewService.selectTSlOrdersItemReviewList(tr);
                    if (itemReviews != null&&!itemReviews.isEmpty()) {
                        if(!reviewVersion.equals(itemReviews.get(itemReviews.size()-1).getVersionNo())){
                            return error("操作失败！因为生产已修改了评审结果！");
                        }
                    }
                }
                TSlOrdersReview slOrdersReview = tSlOrdersReviewService.selectTSlOrdersReviewByOrderId(orderId.longValue());
                if (slOrdersReview != null) {
                    slOrdersReview.setPhase(4L);
                    slOrdersReview.setSalesResult(1L);
                    slOrdersReview.setSalesDate(new Date());
                    tSlOrdersReviewService.updateTSlOrdersReview(slOrdersReview);
                }
                String msg = orders.getSn()+"订单的评审结果已于"+DateUtil.datefomat(new Date(),1)+"获得确认，请及时安排生产";

                userSuspendMsgService.saveUserSuspendMsg(1, "",msg, "", orders.getProductionPrincipal(), "ordersReview",orders.getId());

                //根据评审结果修改订单
                orders.setIsScheduled("2");
                ordersService.updateSlOrders(orders);

                TSlOrdersItemReview tr = new TSlOrdersItemReview();
                tr.setOrders(Long.valueOf(orderId));
                List<TSlOrdersItemReview> itemReviews = itemReviewService.selectTSlOrdersItemReviewList(tr);

                boolean isAllScheduled = itemReviews.stream().allMatch(item -> item.getIsScheduled() == 1);
               if(!isAllScheduled){
                   //1.317:符合条件时拆分订单
                   itemReviewService.orderSplit(request,response,orderId,user);
               }


            }
        }

        return success("success");
    }

    @GetMapping("/list")
    public List list(TSlOrdersReview tSlOrdersReview) {
        List<TSlOrdersReview> list = tSlOrdersReviewService.selectTSlOrdersReviewList(tSlOrdersReview);
        return list;
    }


    /**
     * 获取详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tSlOrdersReviewService.selectTSlOrdersReviewById(id));
    }

    /**
     * 新增
     */
    @PostMapping
    public AjaxResult add(@RequestBody TSlOrdersReview tSlOrdersReview) {
        return toAjax(tSlOrdersReviewService.insertTSlOrdersReview(tSlOrdersReview));
    }

    /**
     * 修改
     */
    @PutMapping
    public AjaxResult edit(@RequestBody TSlOrdersReview tSlOrdersReview) {
        return toAjax(tSlOrdersReviewService.updateTSlOrdersReview(tSlOrdersReview));
    }

    /**
     * 删除
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tSlOrdersReviewService.deleteTSlOrdersReviewByIds(ids));
    }
}
