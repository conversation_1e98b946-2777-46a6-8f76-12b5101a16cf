package cn.sphd.miners.modules.mpWeChatRes.service.impl;

import cn.sphd.miners.common.initializer.DsEcSku;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.ec.entity.EcOrderDetail;
import cn.sphd.miners.modules.ec.entity.EcSku;
import cn.sphd.miners.modules.ec.service.DsService;
import cn.sphd.miners.modules.ec.service.EcService;
import cn.sphd.miners.modules.ec.service.EcService.SkuStatus;
import cn.sphd.miners.modules.ec.service.EcService.SkuType;
import cn.sphd.miners.modules.mpWeChatRes.dao.*;
import cn.sphd.miners.modules.mpWeChatRes.entity.*;
import cn.sphd.miners.modules.mpWeChatRes.service.WxResCatService;
import cn.sphd.miners.modules.mpWeChatRes.service.WxResService;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by 朱思旭 on 2021/12/21.
 */
@Service("wxResService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class WxResServiceImpl implements WxResService, DsService {

    @Autowired
    WxResDao wxResDao;
    @Autowired
    WxResHisDao wxResHisDao;
    @Autowired
    WxResAttDao wxResAttDao;
    @Autowired
    WxResAttHisDao wxResAttHisDao;
//    @Autowired
//    WxResourceExpDao wxResourceExpDao;
    @Autowired
    WxResourceValidPeriodDao wxResourceValidPeriodDao;
    @Autowired
    WxResCatService wxResCatService;
    @Autowired
    ResService resService;

    @Autowired
    EcService ecService;
    @Autowired
    AuthService authService;


    @Override
    public WxResource getWxResSingle(Integer file) {
        WxResource res = wxResDao.get(file);
        return res;
    }

    @Override
    public WxResourceAttachment getWxResAttSingle(Integer attId) {
        WxResourceAttachment resAtt = wxResAttDao.get(attId);
        return resAtt;
    }

    @Override
    public WxResourceHistory getWxResHisSingle(Integer hisFile) {
        WxResourceHistory his = wxResHisDao.get(hisFile);
        return his;
    }

    @Override
    public Long getWxFileNum(String categoryPath) {
        String hql = "select count(id) from WxResource";
        HashMap<String, Object> param = new HashMap<>();
        if (categoryPath != null) {
            hql = hql + " where categoryPath like :categoryPath";
            param.put("categoryPath", categoryPath+"%");
        }
        Long num = (Long) wxResDao.getByHQLWithNamedParams(hql, param);
        return num;
    }

    @Override
    public HashMap<String, Object> getWxFileByCategory(Integer categoryId, PageInfo pageInfo) {
        String hql = "from WxResource where category = :category order by auditDate desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("category", categoryId);
        List<WxResource> list = wxResDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        if (!(list.isEmpty())){
            WxResourceCategory category = wxResCatService.getWxResCatSingle(categoryId);
            String allname = wxResCatService.wxAllParentCatNameOrPath(category.getParent(),category.getName(),null,"1");
            for (WxResource resource : list) {
                resource.setCategoryName(allname);
                fillEcId(resource);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("pageInfo", pageInfo);
        map.put("list", list);
        return map;
    }

    @Override
    public String wxFileIsExistByCategory(Integer category) {
        String hql = "from WxResource where category = :category order by auditDate desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("category", category);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(1);
        List<WxResource> list = wxResDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        String status = "1";
        if (!list.isEmpty()) {
            status = "2";
        }
        return status;
    }

    @Override
    @DsEcSku(serviceName = serviceName)
    public HashMap<String, Object> wxAddFile(User user, WxResource resource, String listResAtt) {
        WxResourceCategory cat = wxResCatService.getWxResCatSingle(resource.getCategory());
        Integer state = 1;
        HashMap<String, Object> map = new HashMap<>();
        if (cat.getValid()) {
            Integer fileNameState = this.checkWxfileName(resource.getName(),resource.getCategory());
            if (fileNameState == 1) {
                WxResource res = this.addRes(user, resource, cat);
                WxResourceHistory resHis = this.addResHis(user,cat,res.getId(),res.getName(),res.getIsFee()?"1":"0",res.getFee().toString(),res.getChangeNum(),false,res.getAuditDate(),"1");
                List<WxResourceAttachment> listAtt = JSON.parseArray(listResAtt,WxResourceAttachment.class);
                for (WxResourceAttachment att : listAtt) {
                    this.addResAtt(att,user,res.getId(),res.getChangeNum(),res.getUpdateDate());
                    this.addResAttHis(att,resHis.getId());
                }
                map.put("newFile",res);
            }else {
                state = 2;
            }
        }else {
            state = 3;
        }
        map.put("state", state);
        return map;
    }

    @Override
    @DsEcSku(serviceName = serviceName)
    public HashMap<String, Object> wxUpFile(User user, WxResource resource, String listResAtt) {
        WxResource res = this.getWxResSingle(resource.getId());
        Integer state = 1;
        HashMap<String, Object> map = new HashMap<>();
        if (!resource.getName().equals(res.getName())) {
            state = this.checkWxfileName(resource.getName(),res.getCategory());
        }
        if (state == 2) {
            map.put("state", state);
            return map;
        }else {
            BeanUtils.copyPropertiesIgnoreNull(resource, res);
            if(EcService.CTpye.unlimit.getIndex().equals(res.getCtypeId())) {
                res.setCtypeExpires(null);
            }
            res.setChangeNum(res.getChangeNum()+1);
            res.setAuditDate(new Date(System.currentTimeMillis()));
            res.setCreator(user.getUserID());
            res.setCreateName(user.getUserName());
            WxResourceCategory cat = wxResCatService.getWxResCatSingle(res.getCategory());
            String fee = null;
            if (res.getFee() != null) {
                fee = res.getFee().toString();
            }
            WxResourceHistory resHis = this.addResHis(user,cat,res.getId(),res.getName(),res.getIsFee()?"1":"0",fee,res.getChangeNum(),false,res.getAuditDate(),"3");
            List<WxResourceAttachment> listAtt = JSON.parseArray(listResAtt,WxResourceAttachment.class);
            String hql = "delete from WxResourceAttachment where file = " + res.getId();
            int delState = wxResAttHisDao.queryHQLWithNamedParams(hql, null);
            for (WxResourceAttachment att : listAtt) {
                this.addResAtt(att,user,res.getId(),res.getChangeNum(),res.getAuditDate());
                this.addResAttHis(att,resHis.getId());
            }
            map.put("newFile",res);
            map.put("state", state);
            return map;
        }
    }

    @Override
    @DsEcSku(serviceName = serviceName)
    public Integer wxStopFile(User user, Integer file) {
        WxResource res = this.getWxResSingle(file);
        Integer state = 1;
        if (!res.getTrash()) {
            WxResourceCategory cat = wxResCatService.wxTrashCategory(user.getOid());
            Date date = new Date();
            String isFee;
            String fee = null;
            if (res.getIsFee()) {
                isFee = "1";
                fee = res.getFee().toString();
            }else {
                isFee = "0";
            }
            WxResourceHistory resHis = this.addResHis(user,cat,res.getId(),res.getName(),isFee,fee,res.getChangeNum(),true,date,"8");
            res.setTrash(true);
            res.setCategory(cat.getId());
            res.setCategoryPath(cat.getPath());
        }else {
            state = 2;
        }
        return state;
    }

    @Override
    @DsEcSku(serviceName = serviceName)
    public Integer wxFileRestore(User user, Integer file) {
        WxResource res = this.getWxResSingle(file);
        Integer state = 1;
        if (res.getTrash()) {
            String hql = " from WxResourceHistory where file = :file order by auditDate desc ";
            HashMap<String, Object> param = new HashMap<>();
            param.put("file", file);
            List<WxResourceHistory> list = wxResHisDao.getListByHQLWithNamedParams(hql,param);
            Integer tag = 0;
            WxResourceCategory cat = null;
            for (WxResourceHistory rh : list) {
                if(tag == 1){
                    cat = wxResCatService.getWxResCatSingle(rh.getCategory());
                    break;
                }
                if (rh.getTrash()) {
                    tag++;
                }
            }
            if (cat.getValid()) {
                Integer nameState = this.checkWxfileName(res.getName(),cat.getId());
                if (nameState == 1) {
                    Date date = new Date();
                    String isFee;
                    String fee = null;
                    if (res.getIsFee()) {
                        isFee = "1";
                        fee = res.getFee().toString();
                    }else {
                        isFee = "0";
                    }
                    WxResourceHistory resHis = this.addResHis(user,cat,res.getId(),res.getName(),isFee,fee,res.getChangeNum(),false,date,"9");
                    res.setTrash(false);
                    res.setCategory(cat.getId());
                    res.setCategoryPath(cat.getPath());
                }else {
                    state = 4;
                }
            }else {
                state = 3;
            }
        }else {
            state = 2;
        }
        return state;
    }

    @Override
    public List<WxResourceHistory> listWxfileRecord(Integer file, PageInfo pageInfo) {
        String hql = " from WxResourceHistory where file = :file order by auditDate asc ";
        HashMap<String, Object> param = new HashMap<>();
        param.put("file", file);
        List<WxResourceHistory> list = wxResHisDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        return list;
    }

    @Override
    public HashMap<String, Object> getWxResMessage(Integer file) {
        WxResource res = this.getWxResSingle(file);
        fillEcId(res);
        WxResourceCategory category = wxResCatService.getWxResCatSingle(res.getCategory());
        String allname = wxResCatService.wxAllParentCatNameOrPath(category.getParent(),category.getName(),null,"1");
        res.setCategoryName(allname);
        List<WxResourceAttachment> listResAtt = this.listResAtt(file);
        HashMap<String, Object> map = new HashMap<>();
        map.put("res",res);
        map.put("listResAtt",listResAtt);
        return map;
    }

    @Override
    public HashMap<String, Object> getWxHisResMessage(Integer hisFile) {
        WxResourceHistory resHis = this.getWxResHisSingle(hisFile);
        WxResourceCategory category = wxResCatService.getWxResCatSingle(resHis.getCategory());
        String allname = wxResCatService.wxAllParentCatNameOrPath(category.getParent(),category.getName(),null,"1");
        resHis.setCategoryName(allname);
        String hql = " from WxResourceAttachmentHistory where fileHistory = :fileHistory order by orders asc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("fileHistory", hisFile);
        List<WxResourceAttachmentHistory> list = wxResAttHisDao.getListByHQLWithNamedParams(hql,param);
        if (!list.isEmpty()) {
            for (WxResourceAttachmentHistory rah : list) {
                if(rah.getType() != "1"){
                    ResEntity resEntity = resService.getSingle(rah.getWondersFile());
                    rah.setResEntity(resEntity);
                }
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("resHis",resHis);
        map.put("listResAttHis",list);
        return map;
    }

    @Override
    public HashMap<String, Object> findFile(String fileName, PageInfo pageInfo) {
        String hql = "from WxResource where name like :name";
        HashMap<String,Object> param = new HashMap<>();
        param.put("name", "%"+fileName+"%");
        List<WxResource> list = wxResDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        for(WxResource resource : list) {
            fillEcId(resource);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list",list);
        map.put("pageInfo",pageInfo);
        return map;
    }

    //检查文章名称是否重复
    private Integer checkWxfileName(String name, Integer category) {
        String hql = "select count(id) from WxResource where name = :name and category = :category";
        HashMap<String,Object> param = new HashMap<>();
        param.put("name",name);
        param.put("category",category);
        Long num = (Long) wxResDao.getByHQLWithNamedParams(hql,param);
        Integer state = 1;
        if (num > 0) {
            state = 2;
        }
        return state;
    }

    //新增文件的方法
    private WxResource addRes(User user, WxResource res, WxResourceCategory cat){
//       /* res.setApp(wxApp.getId());
//        res.setAppId(wxApp.getAppId());*/
        res.setOrg(user.getOid());
        res.setCategory(cat.getId());
        res.setCategoryPath(cat.getPath());
//        res.setName(name);
//        if("1".equals(isFee)){
//            res.setIsFee(true);
//            res.setFee(new BigDecimal(fee));
//        }else {
//            res.setIsFee(false);
//        }
        res.setChangeNum(0L);
        res.setTrash(false);
        res.setAuditDate(new Date(System.currentTimeMillis()));
        res.setCreator(user.getUserID());
        res.setCreateName(user.getUserName());
        wxResDao.save(res);
        return res;
    }

    //新增历史文件版本
    private WxResourceHistory addResHis(User user, WxResourceCategory cat, Integer file, String name, String isFee, String fee, Long changeNum, Boolean isTrash, Date date, String operation){
        WxResourceHistory resHis = new WxResourceHistory();
       /* resHis.setApp(wxApp.getId());
        resHis.setAppId(wxApp.getAppId());*/
        resHis.setFile(file);
        resHis.setOrg(user.getOid());
        resHis.setCategory(cat.getId());
        resHis.setCategoryPath(cat.getPath());
        resHis.setName(name);
        if("1".equals(isFee)){
            resHis.setIsFee(true);
            resHis.setFee(new BigDecimal(fee));
        } else {
            resHis.setIsFee(false);
        }
        resHis.setChangeNum(changeNum);
        resHis.setTrash(isTrash);
        resHis.setOperation(operation);
        resHis.setAuditDate(date);
        resHis.setCreator(user.getUserID());
        resHis.setCreateName(user.getUserName());
        resHis.setCreateDate(date);
        wxResHisDao.save(resHis);
        return resHis;
    }

    //新增文章附件
    private WxResourceAttachment addResAtt(WxResourceAttachment resAtt,User user,Integer file, Long changeNum, Date date){
        /*resAtt.setApp(wxApp.getId());
        resAtt.setAppId(wxApp.getAppId());*/
        resAtt.setOrg(user.getOid());
        resAtt.setFile(file);
        resAtt.setChangeNum(changeNum);
        resAtt.setCreator(user.getUserID());
        resAtt.setCreateName(user.getUserName());
        resAtt.setCreateTime(date);
        wxResAttDao.save(resAtt);
        return resAtt;
    }

    //新增文章附件历史版本
    private void addResAttHis(WxResourceAttachment resAtt,Integer fileHisId){
        WxResourceAttachmentHistory resAtthis = new WxResourceAttachmentHistory();
        resAtthis.setApp(resAtt.getApp());
        resAtthis.setAppid(resAtt.getAppId());
        resAtthis.setOrg(resAtt.getOrg());
        resAtthis.setFile(resAtt.getFile());
        resAtthis.setFileHistory(fileHisId);
        resAtthis.setChangeNum(resAtt.getChangeNum());
        resAtthis.setCreator(resAtt.getCreator());
        resAtthis.setCreateName(resAtt.getCreateName());
        resAtthis.setCreateTime(resAtt.getCreateTime());
        resAtthis.setOrders(resAtt.getOrders());
        resAtthis.setWondersFile(resAtt.getWondersFile());
        resAtthis.setType(resAtt.getType());
        resAtthis.setContent(resAtt.getContent());
        resAtthis.setTitle(resAtt.getTitle());
        resAtthis.setDescription(resAtt.getDescription());
        wxResAttHisDao.save(resAtthis);
    }

    //获取某文件的附件
    private List<WxResourceAttachment> listResAtt(Integer file){
        String hql = " from WxResourceAttachment where file = :file order by orders asc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("file", file);
        List<WxResourceAttachment> listResAtt = wxResAttDao.getListByHQLWithNamedParams(hql,param);
        if (!listResAtt.isEmpty()) {
            for (WxResourceAttachment ra : listResAtt) {
                if (ra.getType() != "1") {
                    ResEntity resEntity = resService.getSingle(ra.getWondersFile());
                    ra.setResEntity(resEntity);
                }
            }
        }
        return listResAtt;
    }

    private void fillEcId(WxResource resource) {
        if(Boolean.TRUE.equals(resource.getIsFee())) {
            EcSku sku = ecService.getSkuByDs(serviceName, resource.getId().toString());
            if(sku!=null) {
                resource.setSkuId(sku.getId());
                fillValid(resource);
            }
        }
    }

    private void fillValid(WxResource resource) {
        AuthInfoDto authInfo = authService.getAuthInfoFromToken(authService.getTokenFromDefaultResponseRequest());
        if(authInfo.getAccId()!=null || authInfo.getMemberId()!=null) {//authInfo.getAccId()!=null => authInfo.getUserId()!=null
            resource.setExp(getAuthResourceExp(authInfo, resource, new Date(System.currentTimeMillis())));
        }
    }

    @Override
    public List<WxResourceValidPeriod> getAllAuthResourceValidPeriod(AuthInfoDto authInfo, WxResource resource, Date now) {
        List<WxResourceValidPeriod> result = new ArrayList<>();
        if(!authInfo.logged()) {
            return result;
        }
        StringBuffer hql = new StringBuffer("from WxResourceValidPeriod where wxResourceId=:wxResourceId and validFrom <= :now and validThru > :now and");
        Map<String, Object> params = new HashMap<>();
        params.put("wxResourceId", resource.getId());
        params.put("now", now);
        List<String> where = new ArrayList<>();
        authInfo.addHql(where, params);
        hql.append(String.join(" and ", where));
        hql.append(" order by validThru, createTime, id");
        List<WxResourceValidPeriod> list = wxResourceValidPeriodDao.getListByHQLWithNamedParams(hql.toString(), params);
        while(!list.isEmpty()) {
            now = list.isEmpty() ? null : list.stream().map(WxResourceValidPeriod::getValidThru).max(Date::compareTo).get();
            params.put("now", new Date(now.getTime() + 1));
            result.addAll(list);
            list = wxResourceValidPeriodDao.getListByHQLWithNamedParams(hql.toString(), params);
        }
        return result;
    }

    @Override
    public Date getAuthResourceExp(AuthInfoDto authInfo, WxResource resource, Date now) {
        if(!authInfo.logged()) {
            return null;
        }
        String first = "select 1";
        String second = " from WxResourceValidPeriod where wxResourceId=:wxResourceId and ";
        String third = "validFrom <= :now and validThru > :now";
        StringBuffer hql = new StringBuffer(first).append(second).append(third);
        Map<String, Object> params = new HashMap<>();
        params.put("wxResourceId", resource.getId());
        params.put("now", now);
        if(authInfo.getAccId() != null) {
            hql.append(" and accId=:accId");
            params.put("accId", authInfo.getAccId());
        }
        if(authInfo.getUserId() != null) {
            hql.append(" and userId=:userId");
            params.put("userId", authInfo.getUserId());
        }
        if(authInfo.getMemberId() != null) {
            hql.append(" and memberId=:memberId");
            params.put("memberId", authInfo.getMemberId());
        }
        if(wxResourceValidPeriodDao.getByHQLWithNamedParams(hql.toString(), params)!=null) {
            String newFirst = "select max(validThru)";
            hql.replace(0, first.length(), newFirst).replace(newFirst.length() + second.length(), newFirst.length() + second.length() + third.length(), "validThru >= :now");
            return (Date) wxResourceValidPeriodDao.getByHQLWithNamedParams(hql.toString(), params);
        } else {
            return null;
        }
    }

    @Override
    public List<WxResource> getWithSkuByIds(Integer[] ids) {
        if(ObjectUtils.isNotEmpty(ids)) {
            String hql = "from WxResource where id in (:ids)";
            Map<String, Object> params = new HashMap<String, Object>(1) {{
                put("ids", ids);
            }};
            return wxResDao.getListByHQLWithNamedParams(hql, params);
        } else {
            return new ArrayList<>(0);
        }
    }

    @Override
    public void createValidPeriod(WxResource resource, EcSku sku, EcOrderDetail orderDetail, Long value, AuthInfoDto authInfo) {
        Integer num = resource.getCtypeExpires() == null || value == null ? 1 : (int)(resource.getCtypeExpires() * value);
        EcService.CTpye ctype = EcService.CTpye.getByIndex(resource.getCtypeId());
        Date now = new Date(System.currentTimeMillis());
        Date oldExp;
        if((oldExp = getAuthResourceExp(authInfo, resource, now)) == null) {
            oldExp = now;
        }
        Date newExp = EcService.CTpye.getExp(oldExp, num, ctype);
        WxResourceValidPeriod period = new WxResourceValidPeriod(orderDetail.getId(), authInfo, resource, num, ctype, oldExp, newExp, (byte)1);
        wxResourceValidPeriodDao.save(period);
    }

    @Override
    public WxResourceValidPeriod checkValidPeriod(EcOrderDetail orderDetail, Date now) {
        String hql = "from WxResourceValidPeriod where orderDetailId=:orderDetailId and validFrom>:now and status=:status";
        Map<String, Object> params = new HashMap<String, Object>(3){{
            put("orderDetailId", orderDetail.getId());
            put("now", now);
            put("status", (byte)1);
        }};
        WxResourceValidPeriod result = (WxResourceValidPeriod) wxResourceValidPeriodDao.getByHQLWithNamedParams(hql, params);
        return result;
    }

    @Override
    public void SaveToEc(String mathodName, Map<String, Object> params, Object result) {
        User user = getCurrentUser(params);
        WxResource record = getRecord(mathodName, params, result);
        SkuStatus operate = getOperate(record, mathodName);
        EcSku oldSku = ecService.getSkuByDs(serviceName, record.getId().toString());
        if(SkuStatus.offline.equals(operate) && oldSku==null) {//下架商品没找到，不操作。
            operate = SkuStatus.pending;
        }
        if(!SkuStatus.pending.equals(operate)) {
            Integer sMaxMun = EcService.CTpye.unlimit.getIndex().equals(record.getCtypeId()) ? 1 : 100;//无限有效期的产品限购1件，一般产品限购100件
            EcSku newSku=new EcSku(null, record.getName(), null, null, defaultMchid, SkuType.virtual, operate, record.getFee(), record.getStock(), null, 0L, record.getCtypeExpires(), record.getCtypeId(), 1, sMaxMun, module, serviceName, record.getId().toString(), user);
            ecService.addOrUpdateSkuAndSpu(oldSku, newSku);
        }
    }
    private SkuStatus getOperate(WxResource record, String mathodName) {
        if(record == null) {
            return SkuStatus.pending;
        } else if(!record.getIsFee() || record.getTrash() || mathodName.equalsIgnoreCase("wxStopFile")) {
            return SkuStatus.offline;
        } else { //"wxAddFile" "wxUpFile" "wxFileRestore"
            return SkuStatus.online;
        }
    }
    private WxResource getRecord(String mathodName, Map<String, Object> params, Object result){
        WxResource record = null;
        switch(mathodName) {
            case "wxStopFile":
            case "wxFileRestore":
                Integer file = (Integer) getParamsByTypeIndex(params, "file", Integer.class);
                record = this.getWxResSingle(file);
                break;
            default:// "wxAddFile" "wxUpFile"
                HashMap<String, Object> resMap = (HashMap<String, Object>) result;
                if((Integer)resMap.get("state")==1) {
                    record = (WxResource) resMap.get("newFile");
                }
        }
        return record;
    }

    @Override
    public void lockStockByEc(EcSku sku, Long value) {
        //库存锁定由电商部分实现。
    }

    @Override
    public void soldByEc(EcSku sku, EcOrderDetail orderDetail, Long value, AuthInfoDto authInfo) {
        if(0L != value) {
            WxResource resource = deducteStock(sku, value);
            createValidPeriod(resource, sku, orderDetail, value, authInfo);
            ecService.updateOrderDetailShipStatus(orderDetail, EcService.ShippingStatus.received);
        }
    }

    @Override
    public void returnByEc(EcSku sku, EcOrderDetail orderDetail, Long value, AuthInfoDto authInfo) {
        if(0L != value) {
            returnStock(sku, value);
        }
    }

    private WxResource deducteStock(EcSku sku, Long value) {
        WxResource resource = getWxResSingle(Integer.valueOf(sku.getDsKey()));
        Map<String, Object> params = new HashMap<String, Object>(2) {{
            put("stock", 0L - value);
        }};
        return wxResDao.incAttr(resource, params);
    }
    private WxResource returnStock(EcSku sku, Long value) {
        WxResource resource = getWxResSingle(Integer.valueOf(sku.getDsKey()));
        return resource;
    }

    @Override
    public boolean checkRefund(EcOrderDetail detail, AuthInfoDto authInfo, Date now) {
        boolean result = false;
        authInfo = SerializationUtils.clone(authInfo);
        authInfo.setSessionId(null);
        if(authInfo != null && ecService.getAuthInfoByOrder(detail.getOrder()).compareTo(authInfo) == 0) {
            return checkValidPeriod(detail, now)!=null;
        }
        return result;
    }
}