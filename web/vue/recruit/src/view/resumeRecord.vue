<template id="resumeRecord">
  <div class="resumeRecord">
    <van-nav-bar
      title="简历提交记录"
      left-arrow
      @click-left="onClickLeft"
    />
    <van-overlay :show="loading">
      <div class="wrapper">
        <van-loading />
      </div>
    </van-overlay>
    <div class="main_container">
      <van-cell-group :border="false">
        <van-cell :title="today"/>
      </van-cell-group>
      <van-cell-group :border="false">
        <van-cell title="招聘单位" :value="rdResume.orgName" :border="false"/>
        <van-cell title="简历查看" is-link @click="goHome()" :border="false"/>
        <van-cell title="提交时间" :value="rdResume.createDate | formatDay" :border="false">
          <template #label>
            <span class="color-blue">注：提交后{{rdNotice.intervals}}分钟内还可修改！</span>
          </template>
        </van-cell>
      </van-cell-group>
      <van-cell-group :border="false">
        <van-cell title="其他数据" is-link @click="goOtherData"/>
      </van-cell-group>
      <van-cell class="importInstruction">
        <div class="title"><b>重要说明</b></div>
        <div class="des">注册后，登录Wonderss系统小程序或其他端口，均可稳定地访问已提交数据，并可使用更多功能。注册免费。</div>
        <div class="des">未注册的只可通过点击本分享，且只有使用原设备、通过原途径、在未清缓存条件下，才能找到已有数据。</div>
        <van-row gutter="16" style="margin-top: 16px">
          <van-col span="12" class="text-center">
            <b class="color-blue" @click="goRegist">免费注册</b>
          </van-col>
          <van-col span="12" class="text-center">
            <b class="color-blue" @click="goLogin">登录系统</b>
          </van-col>
        </van-row>
      </van-cell>
    </div>
  </div>
</template>

<style>
body{
  padding:0;
  margin: 0;
}
.importInstruction{
  margin-top: 36px;
}
.importInstruction .des{
  text-indent: 24px;
  color: #666;
  font-size: 13px;
}
.importInstruction .title{
  text-align: center;
  font-size: 14px;
  margin-bottom: 8px;
}
.color-blue{
  color: #5d9cec;
}
.resumeRecord .van-cell-group{
  margin-bottom: 8px;
}
.text-center{
  text-align: center;
}
</style>

<script>
// wyu：引相关的库
export default {
  name: 'resumeRecord',
  data () {
    return {
      today: '',
      rdResume: {},
      rdNotice: {
        intervals: 0
      },
      loading: true
    }
  },
  created () {
    this.today = '今天是' + this.$moment().format('YYYY年MM月DD日 dddd')
    let recruit = this.$store.getters.getRecruit

    let owxrecord = localStorage.getItem('wxrecord')
    if (owxrecord) {
      owxrecord = JSON.parse(owxrecord)
    }
    this.$http.get(window.parent.$.webRoot + '/resume/getResumeSubmitHistories.do?noticeId=' + recruit.noticeId + '&noticePostId=' + recruit.postId + '&hashKey=' + owxrecord.hash).then(res => {
      console.log(res)
      let data = res.body.data
      this.rdResume = data.rdResume
      this.rdNotice = data.rdNotice
      this.loading = false
    }).catch(err => {
      console.log(err)
    })
  },
  methods: {
    onClickLeft () {
      this.$router.go(-1)
    },
    goRegist () {
      this.$router.push({
        path: 'regist',
        name: 'regist'
      })
    },
    goLogin () {
      this.$router.push({
        path: 'login',
        name: 'login'
      })
    },
    goOtherData () {
      let id = this.rdResume.id
      this.$router.push({
        path: `/otherData/${id}`
      })
    },
    goHome () {
      let owxrecord = localStorage.getItem('wxrecord')
      let offerId = this.rdResume.offer

      if (owxrecord) {
        owxrecord = JSON.parse(owxrecord)
        let hasInput = owxrecord.data.findIndex(item => item.pid === this.rdResume.noticePostId && Number(item.nid) === this.rdResume.noticeId)
        if (hasInput === -1) {
          console.log('未在本地存储找到此岗位对应的信息')
          return false
        }
        let diff = this.$moment().diff(this.$moment(owxrecord.data[hasInput].create), 'minutes')
        let intervals = this.rdNotice.intervals // 简历可修改的截止时间
        console.log(diff)
        if (diff < intervals) {
          let that = this
          let recruit = this.$store.getters.getRecruit
          let otherData = {
            offerId: offerId,
            resumeId: that.rdResume.id, // 这个是修改信息表需要提交的简历id
            hashKey: that.rdResume.hashKey
          }
          console.log('recruit', recruit)
          console.log('otherData', otherData)
          console.log('recruitotherData', {...recruit, ...otherData})
          this.$store.dispatch('setRecruit', {...recruit, ...otherData})
          this.$router.push({name: 'home'})
        } else {
          this.$router.push({
            path: `/resume/${offerId}`
          })
        }
      } else {
        this.$router.push({
          path: `/resume/${offerId}`
        })
      }
    }
  }
}
</script>
