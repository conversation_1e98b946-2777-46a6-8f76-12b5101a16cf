<!--在财会首页 点击本月按钮后的页面-->

<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :runMyBackFun="true" @myBackFun="backFun"  :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <p class="margin-10-15" >{{ timeDur }}</p>

  <div class="hr_gray"></div>
  <div class="current-balance">
    <p>当前余额<span>{{ allBalance }}</span></p>
  </div>

  <div class="hr_gray"></div>
  <div class="flex">
    <div class="balance1" v-for="(item) in bala_list" :key="item.id" >
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <div class="date-account" >
    <div @click="ViewB_A(0)" :class="{padding_bottom_green:isActive==0}">按月流水查看</div>
    <div @click="ViewB_A(1)" :class="{padding_bottom_green:isActive==1}">按日期查看</div>
    <div @click="ViewB_A(2)" :class="{padding_bottom_green:isActive==2}">按账户查看</div>
  </div>
  <div style="flex-grow: 1">
    <router-view />
  </div>

</template>

<script>

import finance from '@/mixins/finance.js'

export default {
  name: "currentMonth",
  mixins: [finance],
  data() {
    return {
      timeDur: '' ,
      allBalance: 0 ,
      bala_list: [],
      isActive: 0,

    }
  },
  mounted() {
    let name = this.$route.name
    if(name === 'ViewByTimeB'){ // 本月的收支流水
      this.isActive = 0
    }else if(name === 'ViewBA2'){
      this.isActive = 2
    }else if(name === 'timeBudget'){
      this.isActive = 1
    }
    this.getFourData()
  },
  methods: {
    backFun(){
      this.$router.push("/caikuai");
    },
    getFourData(){
      let org = this.auth.getOrg()
      this.financeApi.financeAmount({ oid:org.id , state:2  }).then(res1=>{
        let res = res1.data.data
        console.log('financeAmount res=',res)
        let beginDate = (new Date(res.beginDate).format('yyyy-MM-dd'))
        let endDate = (new Date(res.endDate).format('yyyy-MM-dd'))
        localStorage.setItem('f_beginDate', beginDate)
        localStorage.setItem('f_endDate', endDate)
        this.timeDur = beginDate + ' ~ ' + endDate
        this.allBalance = this.formatCurrency(res.allBalance)

        this.bala_list = [
          {id: 0, da: "上月余额", money: this.formatCurrency(res.allPreviousBalance)  , sr: this.balance1 },
          {id: 1, da: "本月收入", money: this.formatCurrency(res.allCredit)  , sr: this.balance2 , style: "ty-color-green"},
          {id: 2, da: "本月支出", money:this.formatCurrency(res.allDebit) , sr: this.balance3 , style: "ty-color-red"},
        ]

      }).catch(err=>{
        this.$message.error('链接错误')
        console.log('err=', err)
      })

    },
    ViewB_A(index) {
      if (index == 0) {
        this.$router.push("/ViewByTimeB");
        this.isActive = 0;
      } else if (index == 2) {
        this.$router.push("ViewBA2");
        this.isActive = 2;
      } else if (index == 1) {
        this.$router.push('timeBudget')
        this.isActive = 1;
      }
    },

  }
}
</script>

<style scoped lang="scss">
@import "../../style/account.scss";
@import "../../style/common.scss";

.date-account {
  padding: 0 20px;

  div {
    font-size: 16px;
    //padding: 10px;
    line-height: 40px;
  }
  
}
.balance1{
  margin: 8px 0px;
}
.flex{
  padding: 5px 20px;
  justify-content: space-between;
}
.page{

  //z-index: 88;
  position: absolute;
  margin-top: 0px;
  bottom: 10vh;
}
</style>