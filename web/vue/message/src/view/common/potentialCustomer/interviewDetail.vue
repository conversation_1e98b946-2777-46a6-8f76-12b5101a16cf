<template>
  <div id="potentialContact" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="24" class="createDetail">录入者：{{interviewInfo.createName}}  {{interviewInfo.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</el-col>
        </el-row>
      </div>
      <div class="panel-content def-gap">
        <div class="sect-con">
          <el-row>
            <el-col :span="8">访谈对象</el-col>
            <el-col :span="14">共{{interviewInfo.interviewerList.length}}人：{{interviewInfo.interviewerNames | handleNull | formatName}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">访谈日期</el-col>
            <el-col :span="14">{{interviewInfo.interviewDate | formatDate('yyyy-MM-dd')}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">同行者（同事）</el-col>
            <el-col :span="14">共{{interviewInfo.tsList.length}}人：{{interviewInfo.tsNames}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">同行者（非同事）</el-col>
            <el-col :span="14">共{{interviewInfo.ftsList.length}}人：{{interviewInfo.ftxNames}}</el-col>
          </el-row>
        </div>
      </div>
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="24">访谈目标</el-col>
        </el-row>
        <el-row><el-col :span="24">{{ interviewInfo.mbContent }}</el-col></el-row>
      </div>
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="24">谈话要点</el-col>
        </el-row>
        <el-row v-for="(participat, index) in interviewInfo.thyd" :key="index">
          <el-col :span="6">{{ participat.participatorName }}</el-col>
          <el-col :span="18">{{ participat.content }}</el-col>
        </el-row>
      </div>
      <div class="panel-content">
        <el-row>
          <el-col :span="24">成果/结论</el-col>
        </el-row>
        <el-row v-for="(cgContent, index) in interviewInfo.cgContent" :key="index">
          <el-col :span="24">{{ cgContent }}</el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<style>
  .sect-con .el-col{
    line-height: 32px;
  }
</style>
<script>
import { formatDate, formatName, handleNull } from '../../../js/common'
export default {
  name: 'potentialInteview',
  filters: {
    formatDate,
    formatName,
    handleNull
  },
  data () {
    return {
      loading: true,
      interviewInfo: {
        interviewer: '',
        tsIds: '',
        ftxIds: '',
        interviewDate: '',
        content: '',
        mbContent: '',
        tsList: [],
        interviewerList: [],
        thyd: [],
        cgContent: [],
        ftsList: []
      }
    }
  },
  created () {
    let _this = this
    let interviewId = _this.$route.params.interviewId
    this.axios.post('../../../saleInterview/getInterview.do', {id: interviewId}).then(function (response) {
      _this.loading = false
      let details = response.data.data
      _this.interviewInfo = details
      _this.interviewInfo.cgContent = details.cgContent.split("$a$")
    }).catch(function (error) {
      console.log(error)
    })
  }
}
</script>
