package cn.sphd.miners.modules.orderReview.mapper;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReview;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersReview;
import cn.sphd.miners.modules.system.entity.User;

import javax.persistence.MapKey;
import java.util.List;
import java.util.Map;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface TSlOrdersReviewMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlOrdersReview selectTSlOrdersReviewById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlOrdersReview 
     * @return 集合
     */
    public List<TSlOrdersReview> selectTSlOrdersReviewList(TSlOrdersReview tSlOrdersReview);

    /**
     * 新增
     * 
     * @param tSlOrdersReview 
     * @return 结果
     */
    public int insertTSlOrdersReview(TSlOrdersReview tSlOrdersReview);

    /**
     * 修改
     * 
     * @param tSlOrdersReview 
     * @return 结果
     */
    public int updateTSlOrdersReview(TSlOrdersReview tSlOrdersReview);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlOrdersReviewById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTSlOrdersReviewByIds(Long[] ids);

    List<Map<String, Object>> selectTSlOrdersReviewListByCondition(TSlOrdersItemReview ordersItemReview);

    TSlOrdersReview selectTSlOrdersReviewByOrderId(Long orders);
}
