package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.common.utils.NewDateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2018/4/11.
 * 员工考勤表
 */
@Entity
@Table(name = "t_personnel_attendance_user")
public class PersonnelAttendanceUser extends BaseEntity {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="user")
    private Integer user;//用户id ，考虑到以前好多接口，暂时未加集联关系
    @Column(name="user_name")
    private String userName;  //用户名称

    @Column(name="org")
    private Integer org;//机构id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="dept")
    private Integer dept;//部门id ，考虑到以前好多接口，暂时未加集联关系 (值为0的人员，为无部门人员)
    @Column(name="dept_name")
    private String deptName;  //部门名称

    @Column(name="attendance_date")
    private Date attendanceDate;//'考勤日期'

    @Column
    private Byte type;//`type` TINYINT DEFAULT NULL COMMENT '考勤类型：0-未考勤，1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,9-其他，8-加班(周六日加班的)，-1 无需考勤'

    @Column(name="begin_time")
    private Date beginTime;//上班时间

    @Column(name="end_time")
    private Date endTime;//下班时间

    @Column(name="middle_break")
    private Boolean middleBreak;//`middle_break` BOOLEAN DEFAULT NULL COMMENT '是否中午考勤'

    @Column(name="break_begin")
    private Date breakBegin;//午休开始时间(上午下班时间)

    @Column(name="break_end")
    private Date breakEnd;//午休结束时间(下午上班时间)

    @Column(name="begin_state"  , length=1 ,columnDefinition = "0")
    private String beginState;//上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他

    @Column(name="end_state", length=1 ,columnDefinition = "0")
    private String endState;//下班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他

    @Column(name="break_begin_state")
    private Byte breakBeginState;//`break_begin_state` TINYINT DEFAULT NULL COMMENT '午休开始状态，middle_break=true有效'

    @Column(name="break_end_state")
    private Byte breakEndState;//`break_end_state` TINYINT NULL DEFAULT NULL COMMENT '午休结束状态，middle_break=true有效'

    @Column(name="am_attendance")
    private Date amAttendance; //上午考勤时间

    @Column(name="pm_attendance")
    private Date pmAttendance; //下午考勤时间

    @Column(name="creator")
    private Integer creator;

    @Column(name="create_name"  , length=100)
    private String createName;

    @Column(name="create_date")
    @CreationTimestamp
    private Date createDate;

    @Column(name="updator")
    private Integer updator;

    @Column(name="update_name"  , length=100)
    private String updateName;

    @Column(name="update_date")
    @UpdateTimestamp
    private Date updateDate;

//    @Column(name="approve_status"  , length=1)
//    private String approveStatus;  //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理
//
//    @Column(name="operation"  , length=1)
//    private String operation;  //操作:1-增,2—删,3-改
//
//    @Column(name="previous_id")
//    private Integer previousId;  //修改前记录ID
//
//    @Column(name="version_no")
//    private Integer versionNo; //版本号,每次修改+1

    @Column(name="source")
    private Integer source; //来源:1-手工录入(默认),2-打卡

    @Column(name="no_need")
    private Boolean noNeed; //`no_need` BOOLEAN COMMENT '无需考勤'
    @Column(name="final_modified")
    private Boolean finalModified; //`final_modified` BOOLEAN COMMENT '修改考勤'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="scan_start_time")
    private Date scanStartTime; //`scan_start_time` DATETIME(3) COMMENT '考勤事件开始时间'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="scan_end_time")
    private Date scanEndTime; //`scan_end_time` DATETIME(3) COMMENT '考勤事件结束时间'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="scan_time")
    private Date scanTime; //`scan_time` DATETIME(3) COMMENT '考勤扫描时间'
    @Column(name = "last_scan_punch_id",columnDefinition = "0")
    private Integer lastScanPunchId;//`last_scan_punch_id` INTEGER DEFAULT 0 COMMENT '上次扫描打卡id'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="p1_before")
    private Long p1Before;  //`p1_before` LONG DEFAULT NULL COMMENT '上班赦免(毫秒),null全天可用
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="p1_after")
    private Long p1After;  //`p1_after` LONG DEFAULT NULL COMMENT '下班赦免(毫秒),null全天可用'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="p2_before")
    private Long p2Before;  //`p2_before` LONG DEFAULT NULL COMMENT '请假离岗赦免(毫秒),null全天可用'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="p2_after")
    private Long p2After;  //`p2_after` LONG DEFAULT NULL COMMENT '请假返岗赦免(毫秒),null全天可用'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="omit_before",columnDefinition = "59999")
    private Integer omitBefore;  //`omit_before` INTEGER NOT NULL DEFAULT 59999 COMMENT '到岗忽略（毫秒）,0没有忽略时间'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="omit_after", columnDefinition = "0")
    private Integer omitAfter;  //`omit_after` INTEGER NOT NULL DEFAULT 0 COMMENT '离岗忽略（毫秒）,0没有忽略时间'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="late_limit")
    private Long lateLimit;//`late_limit` BIGINT DEFAULT NULL COMMENT '迟到时限(毫秒)'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="early_limit")
    private Long earlyLimit;//`early_limit` BIGINT DEFAULT NULL COMMENT '早退时限(毫秒)'

    public Boolean getNoNeed() {
        return noNeed;
    }

    public void setNoNeed(Boolean noNeed) {
        this.noNeed = noNeed;
    }

    public Boolean getFinalModified() {
        return finalModified;
    }

    public void setFinalModified(Boolean finalModified) {
        this.finalModified = finalModified;
    }

    public Date getScanStartTime() {
        return scanStartTime;
    }

    public void setScanStartTime(Date scanStartTime) {
        this.scanStartTime = scanStartTime;
    }

    public Date getScanEndTime() {
        return scanEndTime;
    }

    public void setScanEndTime(Date scanEndTime) {
        this.scanEndTime = scanEndTime;
    }

    public Date getScanTime() {
        return scanTime;
    }

    public void setScanTime(Date scanTime) {
        this.scanTime = scanTime;
    }

    public Integer getLastScanPunchId() {
        return lastScanPunchId;
    }

    public void setLastScanPunchId(Integer lastScanPunchId) {
        this.lastScanPunchId = lastScanPunchId;
    }

    public Long getP1Before() {
        return p1Before;
    }

    public void setP1Before(Long p1Before) {
        this.p1Before = p1Before;
    }

    public Long getP1After() {
        return p1After;
    }

    public void setP1After(Long p1After) {
        this.p1After = p1After;
    }

    public Long getP2Before() {
        return p2Before;
    }

    public void setP2Before(Long p2Before) {
        this.p2Before = p2Before;
    }

    public Long getP2After() {
        return p2After;
    }

    public void setP2After(Long p2After) {
        this.p2After = p2After;
    }

    public Integer getOmitBefore() {
        return omitBefore;
    }

    public void setOmitBefore(Integer omitBefore) {
        this.omitBefore = omitBefore;
    }

    public Integer getOmitAfter() {
        return omitAfter;
    }

    public void setOmitAfter(Integer omitAfter) {
        this.omitAfter = omitAfter;
    }

    public Long getLateLimit() {
        return lateLimit;
    }

    public void setLateLimit(Long lateLimit) {
        this.lateLimit = lateLimit;
    }

    public Long getEarlyLimit() {
        return earlyLimit;
    }

    public void setEarlyLimit(Long earlyLimit) {
        this.earlyLimit = earlyLimit;
    }

    //考勤历史表
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity=PersonnelAttendanceUserHistory.class, fetch= FetchType.LAZY, mappedBy="personnelAttendanceUser", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<PersonnelAttendanceUserHistory> personnelAttendanceUserHistoryHashSet;

    //考勤明细表
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity=PersonnelAttendanceUserDetail.class, fetch= FetchType.LAZY, mappedBy="personnelAttendanceUser", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailHashSet;

    public PersonnelAttendanceUser() {
    }

    //全参数构造函数，used by getPersonnelAttendanceUserByOid，如果本表字段修改，请修改本构造函数，并修改getPersonnelAttendanceUserByOid的HQL
    public PersonnelAttendanceUser(Integer id, Integer user, String userName, Integer org, Integer dept, String deptName, Date attendanceDate, Byte type, Date beginTime, Date endTime, Date breakBegin, Date breakEnd,
                                   String beginState, String endState, Byte breakBeginState, Byte breakEndState, Date amAttendance, Date pmAttendance, Integer creator,
                                   String createName, Date createDate, Integer updator, String updateName, Date updateDate, Integer source, Boolean noNeed, Boolean finalModified,
                                   Date scanStartTime, Date scanEndTime, Date scanTime, Integer lastScanPunchId, Long p1Before, Long p1After, Long p2Before, Long p2After, Integer omitBefore,
                                   Integer omitAfter, Long lateLimit, Long earlyLimit, String imgPath) {
        this.id = id;
        this.user = user;
        this.userName = userName;
        this.org = org;
        this.dept = dept;
        this.deptName = deptName;
        this.attendanceDate = attendanceDate;
        this.type = type;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.breakBegin = breakBegin;
        this.breakEnd = breakEnd;
        this.beginState = beginState;
        this.endState = endState;
        this.breakBeginState = breakBeginState;
        this.breakEndState = breakEndState;
        this.amAttendance = amAttendance;
        this.pmAttendance = pmAttendance;
        this.creator = creator;
        this.createName = createName;
        this.createDate = createDate;
        this.updator = updator;
        this.updateName = updateName;
        this.updateDate = updateDate;
        this.source = source;
        this.noNeed = noNeed;
        this.finalModified = finalModified;
        this.scanStartTime = scanStartTime;
        this.scanEndTime = scanEndTime;
        this.scanTime = scanTime;
        this.lastScanPunchId = lastScanPunchId;
        this.p1Before = p1Before;
        this.p1After = p1After;
        this.p2Before = p2Before;
        this.p2After = p2After;
        this.omitBefore = omitBefore;
        this.omitAfter = omitAfter;
        this.lateLimit = lateLimit;
        this.earlyLimit = earlyLimit;
        this.imgPath = imgPath;
    }

    @Transient
    private Date beginTimeType;// 旷工/请假/加班的开始时间【明细里面的时间】

    @Transient
    private Date endTimeType;//旷工/请假/加班的结束时间【明细里面的时间】

//    @Transient
//    public String upState ; //上班考勤具体状态
//
//    @Transient
//    public String downState ; //下班考勤具体状态

    @Transient
    public List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList;  //请假/旷工/加班 单独的明细

    @Transient
    private String imgPath;  //职工图片

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getDept() {
        return dept;
    }

    public void setDept(Integer dept) {
        dept = dept==null?0:dept;
        this.dept = dept;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Date getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        if(this.type!=null && !this.type.equals((byte)0) && !this.type.equals((byte)1)) {//总类型：0-未考勤，1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,9-其他，8-加班(周六日加班的)，-1 无需考勤
            String stack = JSON.toJSONString(Arrays.stream(Thread.currentThread().getStackTrace()).skip(2).toArray(StackTraceElement[]::new));
            if (stack!=null && stack.contains("attendanceSettleDay")) {
                Logger.getLogger(getClass()).warn("PersonnelAttendanceUser setType by AttendanceTask.attendanceSettleDay from " + this.type + " to " + type + " userId : " + this.user + " attendanceDate : " + (this.attendanceDate == null ? "null" : NewDateUtils.dateToString(this.attendanceDate, "yyyy-MM-dd HH:mm:ss.SSS")));
            } else {
                Logger.getLogger(getClass()).warn("PersonnelAttendanceUser setType from " + this.type + " to " + type + " userId : " + this.user + " attendanceDate : " + (this.attendanceDate == null ? "null" : NewDateUtils.dateToString(this.attendanceDate, "yyyy-MM-dd HH:mm:ss.SSS")));
                Logger.getLogger(getClass()).warn("PersonnelAttendanceUser setType StackTrace " + stack);
            }
        }
        this.type = type;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getMiddleBreak() {
        return middleBreak;
    }

    public void setMiddleBreak(Boolean middleBreak) {
        this.middleBreak = middleBreak;
    }

    public Date getBreakBegin() {
        return breakBegin;
    }

    public void setBreakBegin(Date breakBegin) {
        this.breakBegin = breakBegin;
    }

    public Date getBreakEnd() {
        return breakEnd;
    }

    public void setBreakEnd(Date breakEnd) {
        this.breakEnd = breakEnd;
    }

    public Date getAmAttendance() {
        return amAttendance;
    }

    public void setAmAttendance(Date amAttendance) {
        this.amAttendance = amAttendance;
    }

    public Date getPmAttendance() {
        return pmAttendance;
    }

    public void setPmAttendance(Date pmAttendance) {
        this.pmAttendance = pmAttendance;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Set<PersonnelAttendanceUserHistory> getPersonnelAttendanceUserHistoryHashSet() {
        return personnelAttendanceUserHistoryHashSet;
    }

    public void setPersonnelAttendanceUserHistoryHashSet(Set<PersonnelAttendanceUserHistory> personnelAttendanceUserHistoryHashSet) {
        this.personnelAttendanceUserHistoryHashSet = personnelAttendanceUserHistoryHashSet;
    }

    public Set<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailHashSet() {
        return personnelAttendanceUserDetailHashSet!=null?personnelAttendanceUserDetailHashSet:new HashSet<>(0);
    }

    public void setPersonnelAttendanceUserDetailHashSet(Set<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailHashSet) {
        this.personnelAttendanceUserDetailHashSet = personnelAttendanceUserDetailHashSet;
    }

    public String getBeginState() {
        return beginState;
    }

    public void setBeginState(String beginState) {
        this.beginState = beginState;
    }

    public String getEndState() {
        return endState;
    }

    public void setEndState(String endState) {
        this.endState = endState;
    }

    public Byte getBreakBeginState() {
        return breakBeginState;
    }

    public void setBreakBeginState(Byte breakBeginState) {
        this.breakBeginState = breakBeginState;
    }

    public Byte getBreakEndState() {
        return breakEndState;
    }

    public void setBreakEndState(Byte breakEndState) {
        this.breakEndState = breakEndState;
    }

    public Date getBeginTimeType() {
        return beginTimeType;
    }

    public void setBeginTimeType(Date beginTimeType) {
        this.beginTimeType = beginTimeType;
    }

    public Date getEndTimeType() {
        return endTimeType;
    }

    public void setEndTimeType(Date endTimeType) {
        this.endTimeType = endTimeType;
    }

//    public String getUpState() {
//        return upState;
//    }
//
//    public void setUpState(String upState) {
//        this.upState = upState;
//    }
//
//    public String getDownState() {
//        return downState;
//    }
//
//    public void setDownState(String downState) {
//        this.downState = downState;
//    }

    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailList() {
        return personnelAttendanceUserDetailList!=null?personnelAttendanceUserDetailList:new ArrayList<>();
    }

    public void setPersonnelAttendanceUserDetailList(List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList) {
        this.personnelAttendanceUserDetailList = personnelAttendanceUserDetailList;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

//    public String getApproveStatus() {
//        return approveStatus;
//    }
//
//    public void setApproveStatus(String approveStatus) {
//        this.approveStatus = approveStatus;
//    }
//
//    public String getOperation() {
//        return operation;
//    }
//
//    public void setOperation(String operation) {
//        this.operation = operation;
//    }
//
//    public Integer getPreviousId() {
//        return previousId;
//    }
//
//    public void setPreviousId(Integer previousId) {
//        this.previousId = previousId;
//    }
//
//    public Integer getVersionNo() {
//        return versionNo;
//    }
//
//    public void setVersionNo(Integer versionNo) {
//        this.versionNo = versionNo;
//    }
//
    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Double GetWorkHours() {
        long milliseconds = 0L;
        if(beginTime != null && endTime != null && attendanceDate!=null) {
            long start,end;
            if(breakBegin != null && breakEnd != null) {
                milliseconds += calDuration(attendanceDate, beginTime, breakBegin);
                milliseconds += calDuration(attendanceDate, breakEnd, endTime);
            } else {
                milliseconds += calDuration(attendanceDate, beginTime, endTime);
            }
        }
        return Double.valueOf(milliseconds)/ TimeUnit.HOURS.toMillis(1);
    }
    private long calDuration(Date attendanceDate, Date beginTime, Date endTime) {
        if(beginTime != null && endTime != null && attendanceDate!=null) {
            long start = NewDateUtils.joinDateTime(attendanceDate, beginTime).getTime();
            long end = NewDateUtils.joinDateTime(attendanceDate, endTime).getTime();
            if(end > start) {
                return end - start;
            }
        }
        return 0L;
    }
    public List<Pair<Date, Date>> GetWorkHoursList() {
        List<Pair<Date, Date>> result = new ArrayList<>();
        if(beginTime != null && endTime != null && attendanceDate!=null) {
            if(breakBegin != null && breakEnd != null) {
                result.add(Pair.of(NewDateUtils.joinDateTime(attendanceDate, beginTime), NewDateUtils.joinDateTime(attendanceDate, breakBegin)));
                result.add(Pair.of(NewDateUtils.joinDateTime(attendanceDate, breakEnd), NewDateUtils.joinDateTime(attendanceDate, endTime)));
            } else {
                result.add(Pair.of(NewDateUtils.joinDateTime(attendanceDate, beginTime), NewDateUtils.joinDateTime(attendanceDate, endTime)));
            }
        }
        return result;
    }
    public Map<Long, Long> GetSliceWorkHoursList() {
        List<Pair<Date, Date>> wholes = GetWorkHoursList();
        return wholes.stream().collect(Collectors.toMap(item->item.getLeft().getTime(), item -> item.getRight().getTime(), (key1, key2) -> key1));
    }

    public String getDepartName() {
        return deptName;
    }
}