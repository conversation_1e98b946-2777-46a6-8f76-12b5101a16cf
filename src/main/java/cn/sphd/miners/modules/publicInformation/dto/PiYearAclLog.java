package cn.sphd.miners.modules.publicInformation.dto;

import cn.sphd.miners.modules.publicInformation.entity.PiAclDetail;
import cn.sphd.miners.modules.publicInformation.entity.PiAclLog;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class PiYearAclLog implements Serializable {

    private Date time;
    private List<PiAclLog> listAclLog;
    private List<PiAclDetail> lsitAclDetail;

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public List<PiAclLog> getListAclLog() {
        return listAclLog;
    }

    public void setListAclLog(List<PiAclLog> listAclLog) {
        this.listAclLog = listAclLog;
    }

    public List<PiAclDetail> getLsitAclDetail() {
        return lsitAclDetail;
    }

    public void setLsitAclDetail(List<PiAclDetail> lsitAclDetail) {
        this.lsitAclDetail = lsitAclDetail;
    }

    public PiYearAclLog(){

    }

    public PiYearAclLog(Date time, List<PiAclLog> listAclLog) {
        this.time = time;
        this.listAclLog = listAclLog;
    }

    public PiYearAclLog(Date time, List<PiAclLog> listAclLog, List<PiAclDetail> lsitAclDetail) {
        this.time = time;
        this.listAclLog = listAclLog;
        this.lsitAclDetail = lsitAclDetail;
    }
}
