package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.entity.PdCheckControl;
import cn.sphd.miners.modules.commodity.entity.PdFeatureItem;
import cn.sphd.miners.modules.commodity.entity.PdRank;
import cn.sphd.miners.modules.commodity.entity.PdRankHistory;
import cn.sphd.miners.modules.commodity.service.FeatureService;
import cn.sphd.miners.modules.equipment.entity.TEquCategory;
import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import cn.sphd.miners.modules.equipment.entity.TEquFixedAssets;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.service.ITEquCategoryService;
import cn.sphd.miners.modules.equipment.service.ITEquEquipmentService;
import cn.sphd.miners.modules.equipment.service.ITEquFixedAssetsService;
import cn.sphd.miners.modules.equipment.service.ITEquModelService;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/feature")
public class ProductFeatureController {

    @Autowired
    private FeatureService featureService;

    @Autowired
    private ITEquCategoryService equCategoryService;

    @Autowired
    private ITEquEquipmentService equEquipmentService;

    @Autowired
    private ITEquModelService equModelService;


    @Autowired
    private ITEquFixedAssetsService equFixedAssetsService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 获取待初始化列表
     */
    @RequestMapping("/getProductFeatureList.do")
    @ResponseBody
    public JsonResult getProductFeatureList(User user, Integer type, PageInfo pageInfo) {
        List<Map<String, Object>> list = featureService.getProductFeatureList(user.getOid(), type, pageInfo);
        return new JsonResult(0, list, pageInfo);
    }

    /**
     * 获取已设置数量
     */
    @RequestMapping("/getYszNum.do")
    @ResponseBody
    public JsonResult getYszNum(User user) {
        Object num = featureService.getYszNum(user.getOid());
        return new JsonResult(0, num);
    }

    /**
     * 获取等级设置
     */
    @RequestMapping("/getRankList.do")
    @ResponseBody
    public JsonResult getRankList(User user) {
        List<PdRank> list = featureService.getRankList(user.getOid());
        if (list.size() == 0) {
            List<PdRank> addList = new ArrayList<>();
            PdRank pdRank1 = new PdRank();
            pdRank1.setSymbol("");
            pdRank1.setName("一般特性");

            PdRank pdRank2 = new PdRank();
            pdRank2.setSymbol("icon-tx-sanjiaoxing");
            pdRank2.setName("重要特性");

            PdRank pdRank3 = new PdRank();
            pdRank3.setSymbol("icon-tx-fill-sanjiaoxing");
            pdRank3.setName("关键特性");

            PdRank pdRank4 = new PdRank();
            pdRank4.setSymbol("icon-tx-fill-wujiaoxingxing");
            pdRank4.setName("安全性法规性特性");
            addList.add(pdRank1);
            addList.add(pdRank2);
            addList.add(pdRank3);
            addList.add(pdRank4);
            featureService.getRankSetting(addList, user);
        }
        list = featureService.getRankList(user.getOid());
        return new JsonResult(0, list);
    }

    /**
     * 判断等级设置是否使用
     */
    @RequestMapping("/rankUse.do")
    @ResponseBody
    public JsonResult rankUse(User user, Integer rankId) {
        List<PdFeatureItem> list = featureService.getFeatureItemListByRank(rankId);

        if (list.size() > 0) {
            return new JsonResult(0, 1);
        } else {
            return new JsonResult(0, 0);
        }


    }


    /**
     * 等级设置提交
     */
    @RequestMapping("/getRankSetting.do")
    @ResponseBody
    public JsonResult getRankSetting(User user, HttpServletRequest request) {
        String data = request.getParameter("data");

        List<PdRank> list = JSON.parseArray(data, PdRank.class);

        featureService.getRankSetting(list, user);
        return new JsonResult(0, null);
    }


    /**
     * 新增等级设置
     */
    @RequestMapping("/addRankSetting.do")
    @ResponseBody
    public JsonResult addRankSetting(User user, PdRank pdRank) {

        featureService.addRankSetting(pdRank, user);
        return new JsonResult(0, null);
    }

    /**
     * 修改等级设置
     */
    @RequestMapping("/updateRankSetting.do")
    @ResponseBody
    public JsonResult updateRankSetting(User user, PdRank pdRank) {

        featureService.updateRankSetting(pdRank, user);
        return new JsonResult(0, null);
    }

    /**
     * 删除等级设置
     */
    @RequestMapping("/deleteRankSetting.do")
    @ResponseBody
    public JsonResult deleteRankSetting(User user, Long id) {

        featureService.deleteRankSetting(id, user);
        return new JsonResult(0, null);
    }


    /**
     * 获取设置记录列表
     */
    @RequestMapping("/getRankRecord.do")
    @ResponseBody
    public JsonResult getRankRecord(User user) {
        List<PdRankHistory> list = featureService.getRankRecord(user.getOid());
        return new JsonResult(0, list);
    }

    /**
     * 获取设置记录详情
     */
    @RequestMapping("/getRankRecordDetail.do")
    @ResponseBody
    public JsonResult getRankRecordDetail(User user, Long id) {
        List<PdRankHistory> list = featureService.getRankRecordDetail(id);
        return new JsonResult(0, list);
    }

    /**
     * 初始化设置提交
     */
    @RequestMapping("/productFeatureSetting.do")
    @ResponseBody
    public JsonResult productFeatureSetting(User user, Integer product) {
        String res = featureService.productFeatureSetting(product, user);
        return new JsonResult(0, null);
    }


    /**
     * 获取控制方法列表
     */
    @RequestMapping("/getCheckControlList.do")
    @ResponseBody
    public JsonResult getCheckControlList(User user, Integer enabled) {
        List<PdCheckControl> list = featureService.getPdCheckControlList(user.getOid(), enabled);
        return new JsonResult(0, list);
    }

    /**
     * 新增控制方法
     */
    @RequestMapping("/addCheckControl.do")
    @ResponseBody
    public JsonResult addCheckControl(User user, PdCheckControl checkControl) {
        if (StringUtils.isEmpty(checkControl.getName())) {
            return new JsonResult(0, "", "400", "请填写名称");
        }
        if (StringUtils.isEmpty(checkControl.getCode())) {
            return new JsonResult(0, "", "400", "请填写编码");
        }
        checkControl.setOperation(1);
        checkControl.setCreator(user.getUserID());
        checkControl.setCreateName(user.getUserName());
        checkControl.setCreateDate(new Date());
        checkControl.setEnabled(1);
        checkControl.setOrg(user.getOid());
        String res = featureService.addCheckControl(checkControl);
        return new JsonResult(0, null);
    }

    /**
     * 修改控制方法
     */
    @RequestMapping("/updateCheckControl.do")
    @ResponseBody
    public JsonResult updateCheckControl(User user, PdCheckControl checkControl) {
        if (StringUtils.isEmpty(checkControl.getName())) {
            return new JsonResult(0, "", "400", "请填写名称");
        }
        if (StringUtils.isEmpty(checkControl.getCode())) {
            return new JsonResult(0, "", "400", "请填写编码");
        }
        PdCheckControl old = featureService.getCheckControlById(checkControl.getId());
        if (old != null) {
            old.setName(checkControl.getName());
            old.setCode(checkControl.getCode());
            old.setBriefDesc(checkControl.getBriefDesc());
            old.setOperation(3);
            old.setUpdator(user.getUserID());
            old.setUpdateName(user.getUserName());
            old.setUpdateDate(new Date());

            String res = featureService.updateCheckControl(old);
            return new JsonResult(0, null);
        }
        return new JsonResult(0, "", "400", "控制方法不存在");
    }

    /**
     * 启停控制方法
     */
    @RequestMapping("/startOrStopCheckControl.do")
    @ResponseBody
    public JsonResult startOrStopCheckControl(User user, Long id, Integer enabled) {

        String res = featureService.startOrStopCheckControl(id, enabled, user);
        return new JsonResult(0, null);
    }

    /**
     * 删除控制方法
     */
    @RequestMapping("/deleteCheckControl.do")
    @ResponseBody
    public JsonResult deleteCheckControl(User user, Long id) {

        String res = featureService.deleteCheckControl(id);
        return new JsonResult(0, null);
    }


    /**
     * 获取控制方法记录列表
     */
    @RequestMapping("/getCheckControlRecord.do")
    @ResponseBody
    public JsonResult getCheckControlRecord(User user, Long id) {
        List<Map<String, Object>> list = featureService.getCheckControlRecord(id);
        return new JsonResult(0, list);
    }

    /**
     * 获取控制方法录详情
     */
    @RequestMapping("/getCheckControlRecordDetail.do")
    @ResponseBody
    public JsonResult getCheckControlRecordDetail(User user, Long id) {

        return new JsonResult(0, featureService.getCheckControlRecordDetail(id));
    }

    /**
     * 获取特性列表
     */
    @RequestMapping("/getFeatureItemList.do")
    @ResponseBody
    public JsonResult getFeatureItemList(User user, Integer product) {
        return new JsonResult(0, featureService.getFeatureItemList(product, user.getOid()));
    }

    /**
     * 新增特性
     */
    @RequestMapping("/addFeatureItem.do")
    @ResponseBody
    public JsonResult addFeatureItem(User user, PdFeatureItem pdFeatureItem) {
        return new JsonResult(0, featureService.addFeatureItem(pdFeatureItem, user));
    }

    /**
     * 修改特性
     */
    @RequestMapping("/updateFeatureItem.do")
    @ResponseBody
    public JsonResult updateFeatureItem(User user, PdFeatureItem pdFeatureItem) {
        return new JsonResult(0, featureService.updateFeatureItem(pdFeatureItem, user));
    }

    /**
     * 删除特性
     */
    @RequestMapping("/deleteFeatureItem.do")
    @ResponseBody
    public JsonResult deleteFeatureItem(User user, Long id) {
        return new JsonResult(0, featureService.deleteFeatureItem(id));
    }

    /**
     * 获取设备列表
     */
    @RequestMapping("/getEquEquipmentList.do")
    @ResponseBody
    public JsonResult addFeatureItem(User user,String category) {

        TEquEquipment tEquEquipment = new TEquEquipment();
        tEquEquipment.setOrg(user.getOid().longValue());
        TEquCategory param = new TEquCategory();
        if ("1".equals(category)) {
            param.setName("三类装备");
        } else {
            param.setName("四类装备");
        }
        param.setOrg(user.getOid().longValue());
        List<TEquEquipment> list = equEquipmentService.selectTEquEquipmentList(tEquEquipment);
        List<TEquEquipment> haveList = new ArrayList<>();
        List<TEquCategory> categories = equCategoryService.selectTEquCategoryList(param);
        if (list.size() == 0) {
            return new JsonResult(0, new ArrayList<>());
        }
        TEquFixedAssets tEquModel = new TEquFixedAssets();
        tEquModel.setCategory(categories.get(0).getId().intValue());
        List<TEquFixedAssets> ms = equFixedAssetsService.selectTEquFixedAssetsList(tEquModel);
        for (TEquEquipment equipment : list) {
            for (TEquFixedAssets equFixedAssets : ms) {
                if (equipment.getId().longValue() == equFixedAssets.getEquipment()) {
                    System.out.println(equipment.getId());
                    haveList.add(equipment);
                    break;
                }
            }
        }
        return new JsonResult(0, haveList);
    }

    /**
     * 根据设备id获取型号
     */
    @RequestMapping("/getEquModelListByEquipment.do")
    @ResponseBody
    public JsonResult getEquModelListByEquipment(User user, String category, Long equipment) {

        TEquCategory param = new TEquCategory();
        if ("1".equals(category)) {
            param.setName("三类装备");
        } else {
            param.setName("四类装备");
        }

        param.setOrg(user.getOid().longValue());
        List<TEquCategory> list = equCategoryService.selectTEquCategoryList(param);
        if (list.size() == 0) {
            return new JsonResult(0, new ArrayList<>());
        }

        TEquModel tEquModel = new TEquModel();
        tEquModel.setCategory(list.get(0).getId());
        tEquModel.setEquipment(equipment);
        return new JsonResult(0,equModelService.selectEquModelDetailList(tEquModel));
    }

    /**
     * 获取编码
     */
    @RequestMapping("/getCodeNumber.do")
    @ResponseBody
    public JsonResult getCodeNumber(User user) {

        String key = "CodeNumber:" + user.getOid();
        Integer number = (Integer) redisTemplate.opsForValue().get(key);
        if (number == null) {
            Object maxCode = featureService.getMaxCode(user.getOid());
            if (maxCode == null) {
                number = 1;
            } else {
                number = Integer.parseInt(String.valueOf(maxCode));
                number++;
            }

        } else {
            number++;

        }
        redisTemplate.opsForValue().set(key, number);


        if (number < 10) {
            return new JsonResult(0, "00000" + number);
        }
        if (number < 100) {
            return new JsonResult(0, "0000" + number);
        }
        if (number < 1000) {
            return new JsonResult(0, "000" + number);
        }
        if (number < 10000) {
            return new JsonResult(0, "00" + number);
        }

        if (number < 100000) {
            return new JsonResult(0, "0" + number);
        }
        return new JsonResult(0, number + "");
    }
}
