<template>
  <div id="purchasePaymentFinanceMethodInfo"  v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="detailInfo" v-if="details.financePayment">
      <div v-if="details.financePayment.method==1">
        <div>
          <span><span class="ttl1">付款方式</span><span>现金 </span></span>
        </div>
        <div >
          <span class="ttl1">付款金额</span><span> {{ details.financePayment.planAmount && details.financePayment.planAmount.toFixed(2) }}元</span>
        </div>
        <div>
          <span class="ttl1">付款日期</span><span> {{ details.financePayment.factDate | formatDay('YYYY-MM-DD')}} </span>
        </div>
        <div>
          <span class="ttl1">摘要</span><br><span> {{details.financePayment.summary }} </span>
        </div>
      </div>

      <div v-if="details.financePayment.method==5">
        <div>
          <span><span class="ttl1">{{type === 1? '拟付款方式 ': '付款方式'}} </span><span>银行转账 </span></span>
        </div>
        <div>
          <span><span class="ttl1">银行账户 </span><span>{{details.financeAccount.name }} </span></span>
        </div>
        <div>
          <span><span class="ttl1">账户类型 </span><span>{{details.financeAccount.isPublic == '1' ? '对公户 ': '非对公户'  }} </span></span>
        </div>
        <div>
          <span><span class="ttl1">开户行 </span><span>{{details.financeAccount.bankName }} </span></span>
        </div>
        <div>
          <span><span class="ttl1">账号 </span><span>{{details.financeAccount.account }} </span></span>
        </div>
        <div >
          <span class="ttl1">{{type === 1? '拟付款金额 ': '付款金额'}} </span><span> {{ details.financePayment.planAmount && details.financePayment.planAmount.toFixed(2) }}元</span>
        </div>
        <div>
          <span class="ttl1">{{type === 1? '拟付款日期 ': '付款日期'}} </span><span> {{details.financePayment.planDate | formatDay('YYYY-MM-DD') }} </span>
        </div>
        <div v-if="type === 2">
          <span class="ttl1">摘要</span><br><span> {{details.financePayment.summary }} </span>
        </div>

      </div>

      <div v-if="details.financePayment.method==4">
        <div>
          <span><span class="ttl1">{{type === 1? '拟付款方式 ': '付款方式'}} </span><span>承兑汇票（外）</span></span>
        </div>
        <div @click="seeticket">
          <span><span class="ttl1">汇票号 </span><span>{{ details.financeReturn.returnNo }}<span class="el-icon-arrow-right"></span></span></span>
        </div>
        <div>
          <span><span class="ttl1">票据金额 </span><span>{{ details.financeReturn.billAmount.toFixed(2) }}元</span></span>
        </div>
        <div>
          <span><span class="ttl1">汇票到期日 </span><span>{{ details.financeReturn.expireDate | formatDay('YYYY-MM-DD') }}</span></span>
        </div>
        <div>
          <span><span class="ttl1">出具汇票银行 </span><span>{{ details.financeReturn.bankName }}</span></span>
        </div>
        <div v-if="type === 2">
          <div>
            <span><span class="ttl1">收款单位接收日期 </span><span>{{ details.financePayment.factDate | formatDay('YYYY-MM-DD') }}</span></span>
          </div>
          <div>
            <span><span class="ttl1">收款单位接收人 </span><span>{{ details.financeReturn.operatorName }}</span></span>
          </div>
          <div>
            <span class="ttl1">摘要</span><br><span> {{details.financePayment.summary }} </span>
          </div>
        </div>

      </div>

      <div v-if="details.financePayment.method==3">
        <div v-if="details.invoiceType == '3'">
          <div>
            <span><span class="ttl1">{{type === 1? '拟付款方式 ': '付款方式'}}</span><span>转帐支票（外）</span></span>
          </div>
          <div @click="seeticket">
            <span class="ttl1">支票号</span><span> {{ details.financeReturn.returnNo }}<span class="el-icon-arrow-right"></span></span>
          </div>
          <div>
            <span class="ttl1">票据金额</span><span> {{details.financeReturn.billAmount.toFixed(2) }}元 </span>
          </div>
          <div>
            <span class="ttl1">支票到期日</span><span> {{details.financeReturn.expireDate | formatDay('YYYY-MM-DD') }} </span>
          </div>
          <div>
            <span class="ttl1">出具支票银行</span><span> {{details.financeReturn.bankName }} </span>
          </div>
          <div v-if="type === 2">
            <div>
              <span class="ttl1">收款单位接收日期</span><span> {{details.financePayment.factDate | formatDay('YYYY-MM-DD') }} </span>
            </div>
            <div>
              <span class="ttl1">收款单位接收人</span><span> {{details.financeReturn.operatorName }} </span>
            </div>
            <div>
              <span class="ttl1">摘要</span><br><span> {{details.financePayment.summary }} </span>
            </div>
          </div>

        </div>
        <div v-if="details.invoiceType == '2'">
          <div>
            <span><span class="ttl1">{{type === 1? '拟付款方式 ': '付款方式'}}</span><span>转帐支票（内）</span></span>
          </div>
          <div>
            <span><span class="ttl1">转帐支票的开户行 </span><span>{{ details.financeAccount.bankName }}</span></span>
          </div>
          <div>
            <span><span class="ttl1">转帐支票的支票号 </span><span>{{ details.financeChequeDetail.chequeNo }}</span></span>
          </div>
          <div v-if="type === 1">
            <span><span class="ttl1">支票金额 </span><span>{{ details.financeChequeDetail.amount.toFixed(2) }}元</span></span>
          </div>
          <div>
            <span><span class="ttl1">支票到期日 </span><span>{{ details.financeChequeDetail.expireDate | formatDay('YYYY-MM-DD') }}</span></span>
          </div>
          <div v-if="type === 2">
            <div>
              <span><span class="ttl1">收款单位接收日期 </span><span>{{ details.financePayment.factDate | formatDay('YYYY-MM-DD') }}</span></span>
            </div>
            <div>
              <span><span class="ttl1">收款单位接收人 </span><span>{{ details.financeChequeDetail.receiver }}</span></span>
            </div>
            <div>
              <span class="ttl1">摘要</span><br><span> {{details.financePayment.summary }} </span>
            </div>
          </div>

        </div>
      </div>


    </div>
    <div class="marT20" v-if="nowApproveItem">
      <h4>审批记录</h4>
      <div>
        <div class="process">
          <div>
            <div class="item-content">

              <div v-for="it in details.approvalProcessList" v-bind:key="it.id">
<!--                50-可付款（采购的票款处理,出纳的可付款） 51-待复核（采购的票款处理,出纳与复核审批人-待复核） 52-待付款（采购的票款处理,出纳-待付款）-->
<!--                // 53-付款方式修改（采购的票款处理,出纳-付款方式修改）-->

<!--                63-待付款审批(1.229采购的预付款中) 64-可付款(1.229采购的预付款中) 65-待复核(1.229采购的预付款中-待复核) 66-待付款(1.229采购的预付款中)-->
<!--                //67-付款方式修改(1.229采购的预付款中)-->

                <div v-if="it.approveStatus === '2' && it.businessType == 63" class="processItem">
                  <div>付款审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 64" class="processItem">
                  <div>付款方式确认</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 65" class="processItem">
                  <div>付款复核通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 66" class="processItem">
                  <div v-if="details.financePayment.method === '1'">现金付讫</div>
                  <div v-if="details.financePayment.method === '3'">报销款已转支票</div>
                  <div v-if="details.financePayment.method === '4'">报销款已转承兑汇票</div>
                  <div v-if="details.financePayment.method === '5'">报销款已转账</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 67" class="processItem">
                  <div>付款方式修改</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <!--  888888888888  -->
                <div v-if="it.approveStatus === '2' && (it.businessType == 50 || it.businessType == 70)" class="processItem">
                  <div>付款方式确认</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && (it.businessType == 51 || it.businessType == 71)" class="processItem">
                  <div>付款复核通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && (it.businessType == 53 || it.businessType == 73)" class="processItem">
                  <div>付款方式修改</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '5'" class="processItem">
                  <div>
                    <span v-if="financePayment.businessType == '1'">现金付讫</span>
                    <span v-if="financePayment.businessType == '2'">报销款已转支票</span>
                    <span v-if="financePayment.businessType == '3'">报销款已转支票</span>
                    <span v-if="financePayment.businessType == '4'">报销款已转承兑汇票</span>
                    <span v-if="financePayment.businessType == '5'">报销款已转账</span>
                  </div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <!-- 最后一条审批记录展示 -->
              <div class="color-blue">
                <!--<span v-else-if="nowApproveItem.approveStatus === '1'&& nowApproveItem.toUser>0 && !isNowApprover ">{{ nowApproveItem.userName }}为下一个审批人</span>-->
                <span v-else-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
              </div>

              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && (nowApproveItem.businessType=== 63 || it.businessType == 73)">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 64">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue">
                <span v-if="details.approveStatus == '2'">本次报销已完结！</span>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.el-icon-arrow-right{
  margin-left: 10px;
}
.tbddd .el-table__header-wrapper{
  position: relative;
    left: 18px;
}
  .marT20{
    margin-top: 20px;
    background: #fff;
    padding: 20px;
  }
  .detailInfo{
    padding:10px 20px 10px 20px;
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
    line-height: 26px;
    .ttl1{
      display: inline-block;
      width: 114px;
      margin-right: 10px;
    }
    .ttl1+span{
      float: right;
    }
  }
</style>
<script>
import * as moment from 'moment'
export default {
  name: 'purchasePaymentFinanceMethodInfo',
  data () {
    return {
      title: '已付款',
      loading: true,
      pund: '',
      type: '',
      toggle: -1,
      details: {
      },
      nowApproveItem:null
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    this.type = Number(this.$route.params.type) // 1:待复核的付款 ； 2: 已付款
    let invoiceID =  this.$route.params.invoiceID
    let pund =  Number(this.$route.params.pund)
    let payid =  this.$route.params.id
    this.pund = pund
    this.title = this.type === 1? '待复核的付款 ': '已付款'
    let url = '../../../purchaseInvoice/getPurchaseInvoiceDetail.do'
    let params = { applicationId: invoiceID, financePaymentId: payid}
    if(Number(pund) === 1){ // 预付款
      url = '../../../purchaseInvoice/advancePaymentDetail.do'
      params = { orderPrepaymentId: invoiceID, financePaymentId: payid}
    } else if (Number(pund) === 3) {
      url = '../../../overpayment/getOverpaymentDetail.do'
      params = { loanBizId: invoiceID, financePaymentId: payid}
    }
    this.getInfo(url,params)
  },
  destroyed: function () {
  },
  methods: {
    seeticket: function () {
      let that = this
      let item = this.details.financeReturn
      item.pund = that.pund
      if (this.details.financePayment.method === '4') {
        item.type = 3
      } else if (this.details.financePayment.method === '3') {
        item.type = 2
      }
      console.log(item)
      localStorage.setItem('invoiceTicketInfo', JSON.stringify(item))
      that.$router.push({
        path: `/purchasePaymentInvoiceInfo`
      })
    },
    getInfo: function (url,params) {
      let that = this
      that.loading = false
      this.axios.post(url, params)
        .then(({ data }) => {
          that.details = data.data
          console.log('that.details', that.details)
          let arr = that.details.approvalProcessList
          that.nowApproveItem =  arr[arr.length-1]
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  },
  computed: {
  }
}
</script>
