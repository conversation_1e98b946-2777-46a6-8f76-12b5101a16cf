package cn.sphd.miners.modules.recruit.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

/**
 * 招聘启事其他事项表
 * <AUTHOR>
 * @date 20230506
 **/
@Entity
@Table(name = "t_rd_notice_additional")
public class RdNoticeAdditional {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    @Column
    private Integer org;//机构ID

    @Column(name="notice" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticeId;//启事ID

    @Column(name = "notice_post" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticePostId;//招聘岗位ID

    @Column
    private Integer age;//'年龄'

    @Column(name = "age_threshold")
    private Integer ageThreshold=0;//年龄阈: 0不限 -1-以下,2-以上

    @Column(name = "gender")
    private Integer gender=0;//性别:0-不限,1-女,2-男

    @Column(name = "education")
    private Integer education;//学历:1-小学以下,2-小学,3-初中,4-高中,5-中专,6-大专,7-本科,8-硕士,9-博士

    @Column(name = "education_threshold")
    private Integer educationThreshold;//学历阈:-1-以下,2-以上

    @Column(name = "residence")
    private Integer residence=0;//户籍:0-不限,1-本地,2-非本地

    @Column(name = "experiences",length = 255)
    private String experiences; //'工作经验'

    @Column(name = "characters",length = 255)
    private String characters; //'性格

    @Column(length = 255)
    private String memo; //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"   , nullable=true , unique=false)
    private Integer operation; //操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNotice rdNotice ;//启事主表

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice_post", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNoticePost rdNoticePost ;//招聘岗位表


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public Long getNoticePostId() {
        return noticePostId;
    }

    public void setNoticePostId(Long noticePostId) {
        this.noticePostId = noticePostId;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getAgeThreshold() {
        return ageThreshold;
    }

    public void setAgeThreshold(Integer ageThreshold) {
        this.ageThreshold = ageThreshold;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getEducation() {
        return education;
    }

    public void setEducation(Integer education) {
        this.education = education;
    }

    public Integer getEducationThreshold() {
        return educationThreshold;
    }

    public void setEducationThreshold(Integer educationThreshold) {
        this.educationThreshold = educationThreshold;
    }

    public Integer getResidence() {
        return residence;
    }

    public void setResidence(Integer residence) {
        this.residence = residence;
    }

    public String getExperiences() {
        return experiences;
    }

    public void setExperiences(String experiences) {
        this.experiences = experiences;
    }

    public String getCharacters() {
        return characters;
    }

    public void setCharacters(String characters) {
        this.characters = characters;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public RdNotice getRdNotice() {
        return rdNotice;
    }

    public void setRdNotice(RdNotice rdNotice) {
        this.rdNotice = rdNotice;
    }

    public RdNoticePost getRdNoticePost() {
        return rdNoticePost;
    }

    public void setRdNoticePost(RdNoticePost rdNoticePost) {
        this.rdNoticePost = rdNoticePost;
    }
}
