package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfo;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfoHistory;
import cn.sphd.miners.modules.commodity.entity.PdPackagingStructure;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

public interface PdPackagingService {
    List<Map<String, Object>> getPackagingList(Integer oid, String param, int type, PageInfo pageInfo);

    Object getWszNum(Integer oid);

    //获取包装类别id
    Integer getBzTypeId(Integer oid);

    List<Map<String, Object>> getMtBaseByType(Integer oid, Integer typeId);

    void deletePackaging(Integer id);
    //添加包装
    String addPackaging(PdPackagingInfo info, User user);
    //修改包装
    String updatePackaging(PdPackagingInfo info, List<PdPackagingStructure> pdPackagingStructureList, User user);

    String updatePackagingInfo(PdPackagingInfo info);
    //获取包装详情
    PdPackagingInfo getPackagingDetail(Integer id);
    //获取包装详情
    PdPackagingInfo getPackaging(Integer id);
    //获取所有包装信息
    List<PdPackagingInfoHistory> getPackagingList(Integer productId);

    //获取操作记录
    List<PdPackagingInfoHistory> getPackagingRecordList(Integer id);
    //获取操作记录详情
    PdPackagingInfoHistory getPackagingRecordDetail(Integer recordId);

    //获取无需包装
    List<PdPackagingInfo>  getWxPackagingList(Integer productId);
}
