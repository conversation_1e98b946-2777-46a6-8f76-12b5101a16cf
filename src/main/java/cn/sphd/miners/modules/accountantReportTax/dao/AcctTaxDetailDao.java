package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto;
import cn.sphd.miners.modules.accountantReportTax.dto.AlreadyDeclaredTaxDto;
import cn.sphd.miners.modules.accountantReportTax.dto.PayTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AcctTaxDetailDao extends IBaseDao<AcctTaxDetailEntity,Serializable> {
    List<AcctTaxDetailEntity> getListByDeclareDate(int reportId, String period);

    int updateByReportId(AcctTaxDetailEntity taxDetailEntity);//更新成最新的报税时间

    int updateByTaxId(AcctTaxDetailEntity taxDetailEntity);

    Map<String,Object> getReportTaxRecord(AcctTaxDetailEntity taxDetailEntity, int type);

    List<AcctReportHistoryEntity> getReportedList(User user, int year, int month);//已报报表

    List<AcctTaxDto> getTaxDetailByReport(AcctReportHistoryEntity reportEntity, int year, int month);//获取该报表的税种列表

    List<AcctReportHistoryEntity> getReportListByMonth(User user, int year, int month);//应报报表

    Integer setTaxFactAmount(AcctTaxDetailEntity detailEntity);

    List<AlreadyDeclaredTaxDto> getTaxedList(User user, String period);

}
