package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.system.entity.User;
//import io.swagger.models.auth.In;

import java.util.List;
import java.util.Map;

public interface ConstituteService {


    //获取待确认加工列表
   Map<String,Object> getConfirmedList(Integer oid,Integer currPage, Integer pageSize);
    //构成管理首页 统计数据
    Map<String,Object> getDataNumber(Integer oid);

   String processingConfirmation(String baseList,String process,User user);
   //待确认构成列表
   Map<String,Object> getCompositionToBeConfirmedList(Integer oid,Integer currPage, Integer pageSize);

   //获取产品信息
    PdBase getPdBase(Integer id);
    //待确认构成处理确认
    String compositionToBeConfirmed(String baseList,String composition);
    //获取待拆分列表
    Map<String,Object> getSplitList(Integer oid,Integer currPage, Integer pageSize);
    //添加零组件
    String addPdBaseLingJian(PdBase pdBase, Integer pdBaseId,Integer oldId, User user,Integer amount);
    // 根据产品，或零组件id 获取零组件列表
    Map<String,Object> getPdOrLingJianList(Integer id);
    //拆分完成
    String splitComplete(Integer id,User user);
    //零组件删除
    String lingJianDelete(Integer id,Integer parentId,User user);

    List<Map<String,Object>> selectLingJianListByParent(Integer oid,Integer id);





}
