package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.material.entity.TSrmSupplierImportLog;

import java.util.List;

/**
 * 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface ITSrmSupplierImportLogService 
{
    /**
     * 查询供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     * 
     * @param id 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入主键
     * @return 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     */
    public TSrmSupplierImportLog selectTSrmSupplierImportLogById(Long id);

    /**
     * 查询供应商管理_供应商导入日志
20231108 1.267供应商之批量导入列表
     * 
     * @param tSrmSupplierImportLog 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     * @return 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入集合
     */
    public List<TSrmSupplierImportLog> selectTSrmSupplierImportLogList(TSrmSupplierImportLog tSrmSupplierImportLog);

    /**
     * 新增供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     * 
     * @param tSrmSupplierImportLog 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     * @return 结果
     */
    public int insertTSrmSupplierImportLog(TSrmSupplierImportLog tSrmSupplierImportLog);

    /**
     * 修改供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     * 
     * @param tSrmSupplierImportLog 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     * @return 结果
     */
    public int updateTSrmSupplierImportLog(TSrmSupplierImportLog tSrmSupplierImportLog);

    /**
     * 批量删除供应商管理_供应商导入日志
20231108 1.267供应商之批量导入
     * 
     * @param ids 需要删除的供应商管理_供应商导入日志
20231108 1.267供应商之批量导入主键集合
     * @return 结果
     */
    public int deleteTSrmSupplierImportLogByIds(String ids);

    /**
     * 删除供应商管理_供应商导入日志
20231108 1.267供应商之批量导入信息
     * 
     * @param id 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入主键
     * @return 结果
     */
    public int deleteTSrmSupplierImportLogById(Long id);
}
