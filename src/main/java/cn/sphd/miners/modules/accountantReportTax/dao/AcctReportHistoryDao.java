package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.util.List;

public interface AcctReportHistoryDao extends IBaseDao<AcctReportHistoryEntity,Serializable> {

    void setDisenabled(Integer id);//把最后一条report数据更新成不再申报

    AcctReportHistoryEntity getLastByReportId(int reportId);//获取最新的历史报表

    List<AcctReportHistoryEntity> reportUpdateRecord(Integer org, int reportId);

}
