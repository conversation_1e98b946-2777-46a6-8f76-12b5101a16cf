<template>
  <div class="customer">
    <div>
      <div v-if="curPage === 'main'" class="main">
        <div>
          <table style="width: 100%; margin-bottom: 10px;">
            <tr>
              <td width="300px">
                <el-input placeholder="客户名称/客户代号" v-model="mainData.searchVal">
                  <template #append>
                    <span @click="searchSaler"><i class="fa fa-search"></i> 搜索</span>
                  </template>
                </el-input>
              </td>
              <td></td>
              <td class="btnccc" style="text-align: right">
                <span class="ty-btn ty-btn-cyan ty-btn-big ty-circle-3 " @click="uploadSalerBtn"><i class="fa fa-cloud-upload"></i> 批量导入</span>
                <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3 " @click="addSalerBtn"><i class="fa fa-user-plus"></i> 新增客户</span>
                <span v-if="specialOrgPart" class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="outOfOrgList"><i class="fa fa-user-times"></i> 已暂停服务的机构</span>
                <span v-else class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="stopSalerCon"><i class="fa fa-user-times"></i> 已暂停合作的客户</span>
              </td>
            </tr>
          </table>
        </div>
        <el-table stripe class="ty-table-control" :data="mainData.list" max-height="700" border style="width: 100%">
          <el-table-column prop="no" label="序号" width="55"> </el-table-column>
          <el-table-column prop="code" label="客户代号" width="140"> </el-table-column>
          <el-table-column prop="fullName" label="客户名称" width="140"> </el-table-column>
          <el-table-column prop="create" label="创建者" width="260"> </el-table-column>
          <el-table-column prop="principalName" label="销售负责人" width="140"> </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <span class="ty-color-blue" @click="scanSaler(scope.row)">查看</span>
              <span class="ty-color-blue" @click="manageSaler(scope.row)">管理</span>
              <span class="ty-color-red" @click="delSaler(scope.row)">删除</span>
              <span class="ty-color-orange" v-if="!specialOrgPart" @click="stopSaler(scope.row , 1)">暂停合作</span>
                <span class="ty-color-blue" v-if="specialOrgPart" @click="getOrganizationList(scope.row )">机构管理</span>
            </template>
          </el-table-column>
        </el-table>
        <TyPage v-if="mainData.pageShow"
                :curPage="mainData.pageInfo.currentPageNo" :pageSize="mainData.pageInfo.pageSize"
                :allPage="mainData.pageInfo.totalPage" :pageClickFun="mainPageClick"></TyPage>

      </div>

      <div v-if="curPage === 'suspendOrg'">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="backMain"><i class="fa fa-chevron-left"></i> 返回</span>
        <div style="margin-top: 20px;">已被暂停服务的机构如下：</div>
        <el-table stripe class="ty-table-control" :data="outOfOrgData.list" height="700" border style="width: 100%; margin-top: 20px;">
          <el-table-column prop="no" label="序号" width="55"></el-table-column>
          <el-table-column prop="supervisorMobile" width="180px" label="拥有最高权限的手机号码"> </el-table-column>
          <el-table-column prop="supervisorName" label="超管姓名" width="100"> </el-table-column>
          <el-table-column prop="fullName" label="机构名称" > </el-table-column>
          <el-table-column prop="updater" label="暂停服务的操作者" width="170px"> </el-table-column>
          <el-table-column prop="mpTenantName" label="所属租户" width="90"> </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <span class="ty-color-blue" @click="orgInfo(scope.row)">机构信息</span>
              <span class="ty-color-blue" @click="proInfo(scope.row)">产品信息</span>
              <span class="ty-color-blue" @click="addService(scope.row)">增值服务</span>
              <span class="ty-color-blue" @click="loginRecord(scope.row)">登录记录</span>
              <span class="ty-color-blue" @click="resouceManage(scope.row)">空间与流量</span>
              <span class="ty-color-blue" @click="startService(scope.row)">恢复服务</span>
            </template>
          </el-table-column>
        </el-table>

        <TyPage v-if="outOfOrgData.pageShow"
                :curPage="outOfOrgData.pageInfo.currentPageNo" :pageSize="outOfOrgData.pageInfo.pageSize"
                :allPage="outOfOrgData.pageInfo.totalPage" :pageClickFun="stopOrgClick"></TyPage>
      </div>

      <div v-if="curPage === 'suspend'">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="backMain"><i class="fa fa-chevron-left"></i> 返回</span>
        <el-table stripe class="ty-table-control" :data="suspendCusData.list" height="700" border style="width: 100%; margin-top: 20px;">
          <el-table-column prop="no" label="序号" width="55"> </el-table-column>
          <el-table-column prop="code" label="客户代号" width="140"> </el-table-column>
          <el-table-column prop="name" label="客户名称" width="140"> </el-table-column>
          <el-table-column prop="create" label="创建者" width="260"> </el-table-column>
          <el-table-column prop="suspendTime" label="暂停合作的时间" width="260"> </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <span class="ty-color-blue" @click="scanSaler(scope.row)">查看</span>
              <span class="ty-color-green" @click="stopSaler(scope.row, 0)">恢复合作</span>
            </template>
          </el-table-column>
        </el-table>

        <TyPage v-if="suspendCusData.pageShow"
                :curPage="suspendCusData.pageInfo.currentPageNo" :pageSize="suspendCusData.pageInfo.pageSize"
                :allPage="suspendCusData.pageInfo.totalPage" :pageClickFun="suspendPageClick"></TyPage>
      </div>

      <div v-if="curPage === 'orgList'">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="backMain"><i class="fa fa-chevron-left"></i> 返回</span>
        <div style="margin-top:20px; ">
          {{ orgListData.editObj.name }} 名下有如下机构：
          <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="addOrg(orgListData.editObj.supervisorName, orgListData.editObj.supervisorMobile)">新增机构</span>
        </div>
        <div class="orgList" >

          <table class="ty-table ty-table-control">
            <tbody>
            <tr>
              <td>机构名称</td>
              <td>拥有最高权限的手机号码</td>
              <td>超管姓名</td>
              <td>创建人</td>
              <td width="300px">操作</td>
            </tr>
            <tr v-for="(item, index) in orgListData.list" :key="index">
              <td>{{ item.name }}</td>
              <td>{{ item.supervisorMobile }}</td>
              <td>{{ item.supervisorName }}</td>
              <td>{{ item.create }}</td>
              <td>
                <span class="ty-color-blue" @click="orgInfo(item)">机构信息</span>
                <span class="ty-color-blue" @click="proInfo(item)">产品信息</span>
                <span class="ty-color-red" @click="stopService(item)">暂停服务</span>
                <div class="sl-dropdown" @mouseout="slDropdownHide" >
                  <div :class="dropdownData.showType === 'normal'? 'dropdownCon' : 'dropdownCon2'" v-if="dropdownData.visibleIndex === index"  @mouseover="slDropdownClick(index)">
                    <div v-if="item.mpPackageId" class="dropdownItem" ><span @click="addService(item)">增值服务</span></div>
                    <div v-else class="disableItem" ><span>增值服务</span></div>

                    <div class="dropdownItem"><span @click="loginRecord(item, 'orgList')">登录记录</span></div>
                    <div class="dropdownItem"><span @click="resouceManage(item)">空间与流量</span></div>
                  </div>
                  <span class="ty-color-blue" @click.stop="slDropdownClick(index, $event)">更多</span>
                </div>
              </td>
            </tr>
            </tbody>
          </table>


        </div>
      </div>
      <div v-show="curPage === 'loginRecord'">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="backOrgList2"><i class="fa fa-chevron-left"></i> 返回</span>
        <div style="margin-top:20px; ">
          <div>
            登录记录
            <div class="btnss">
               <el-popover placement="bottom" :width="300" trigger="manual" v-model:visible="orgLoginData.visible">
                <template #reference>
                   <span class="ty-btn-group" style="margin:0 10px ">
                      <span class="ty-btn ty-btn-big" :class="orgLoginData.searchType === 3 ? 'ty-btn-blue' : 'ty-btn-gray' "  @click="defaultSearchBtn">自定义查询</span>
                    </span>
                </template>
                <div>
                  <el-form-item label="起时间：">
                    <el-date-picker v-model="orgLoginData.beginDate" :teleported="false" type="date" placeholder="选择日期">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label="止时间：">
                    <el-date-picker v-model="orgLoginData.endDate" :teleported="false" type="date" placeholder="选择日期">
                    </el-date-picker>
                  </el-form-item>
                  <div style="text-align: right;">
                    <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" @click="orgLoginData.visible = false">取消</span>
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="searchType(3)">确定</span>
                  </div>
                </div>
              </el-popover>
              <span class="ty-btn-group" style="margin:0 10px ">
                <span class="ty-btn ty-btn-big" :class="orgLoginData.searchType === 1 ? 'ty-btn-blue' : 'ty-btn-gray' "  @click="searchType(1)">本月</span>
                <span class="ty-btn ty-btn-big" :class="orgLoginData.searchType === 2 ? 'ty-btn-blue' : 'ty-btn-gray' "  style="border-left:1px solid #eee; " @click="searchType(2)">本年</span>
              </span>
            </div>

          </div>
          <div style="margin-top: 20px;">
            机构名称：{{ orgLoginData.logData.name   }}
            <span style="margin-left:100px; ">创建人：{{ orgLoginData.creatInfo }}  </span>
          </div>
        </div>
        <div class="orgList">
          <el-table stripe class="ty-table-control" :data="orgLoginData.logData.statisticsList" height="700" border >
            <el-table-column prop="dateFormat" :label="orgLoginData.td1Txt" ></el-table-column>
            <el-table-column prop="sumUser" label="登录人数" > </el-table-column>
            <el-table-column prop="sumDuration" label="登录总时长"> </el-table-column>
            <el-table-column prop="sumLogin" label="登录总次数" ></el-table-column>
            <el-table-column prop="sumPc" label="电脑端登录总次数" > </el-table-column>
            <el-table-column prop="sumApp" label="手机端登录总次数" > </el-table-column>
          </el-table>
        </div>
      </div>

    </div>

    <!--  弹窗的高低顺序 是按照代码的前后顺序   -->
    <div>


      <!-- 恢复服务 -->
      <TyDialog v-if="stopServiceData.visible2" dialogTitle="！！提示" color="blue" :dialogHide="hideSSD2">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSSD2">关闭</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="SSDok2">确定</span>
        </template>
        <template #dialogBody>
          <div style="line-height:40px; text-align: center;  ">
            确定恢复向该客户提供服务吗？
          </div>
        </template>
      </TyDialog>

      <!-- 暂停服务 -->
      <TyDialog v-if="stopServiceData.visible" dialogTitle="！！提示" color="red" :dialogHide="hideSSD">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSSD">关闭</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="SSDok">确定</span>
        </template>
        <template #dialogBody>
          <div style="line-height:40px; text-align: center;  ">
            机构如被暂停服务，其成员将无法登录。 <br>
            确定继续操作吗?
          </div>
        </template>
      </TyDialog>

      <!-- 空间与流量 -->
      <TyDialog v-if="resourceManageData.visible" width="600" dialogTitle="空间与流量" color="blue" :dialogHide="hideRM">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideRM">关闭</span>
        </template>
        <template #dialogBody>
          <div class="rMMsg">
            <div><span class="ttl">机构名称</span><span class="con">{{ resourceManageData.info.fullName || ' --' }}</span></div>
            <div><span class="ttl">机构简称</span><span class="con">{{ resourceManageData.info.name || ' --' }}</span></div>
            <div class="hr"></div>
            <div><span class="ttl">空间上限</span><span class="con">{{  resourceManageData.info.ratedSpace || ' --' }}</span></div>
            <div><span class="ttl">已用空间</span><span class="con">{{  resourceManageData.info.usedSpace || ' --' }}</span></div>
              <div class="hr"></div>
            <div>{{ resourceManageData.info.beginDate ? new Date(resourceManageData.info.beginDate).format("yyyy年MM月dd日") : ' -- ' }} -
              {{ resourceManageData.info.endDate ? new Date(resourceManageData.info.endDate).format("yyyy年MM月dd日") : ' --' }}</div>
            <div><span class="ttl">期间的流量上限</span><span class="con">{{ resourceManageData.info.ratedTraffic || ' --' }}</span></div>
            <div><span class="ttl">此期间已用流量</span><span class="con">{{ resourceManageData.info.usedTraffic ||  ' --' }}</span></div>
                <div class="hr"></div>
          </div>
        </template>
      </TyDialog>

      <!-- 产品信息 -->
      <TyDialog v-if="proInfoData.visible" width="600" dialogTitle="产品信息" color="blue" :dialogHide="hideProInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideProInfo">关闭</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <table>
              <tbody>
              <tr> <td>机构名称</td><td style="width: 270px;" v-html="proInfoData.info.fullName"></td></tr>
              <tr> <td>机构简称</td> <td v-html="proInfoData.info.name"></td></tr>
              <tr> <td>机构地址</td> <td v-html="proInfoData.info.address"></td></tr>
              <tr> <td>超管姓名</td> <td v-html="proInfoData.info.supervisorName"></td></tr>
              <tr> <td>超管手机</td> <td v-html="proInfoData.info.supervisorMobile"></td></tr>
              <tr> <td>数据存储地点</td><td v-html="proInfoData.info.uploadStorageType"></td></tr>
              <tr> <td>所选产品</td><td v-html="proInfoData.info.mpPackageName"></td></tr>
              <tr> <td>所选产品修改记录</td><td>
                <span class="ty-linkBtn" @click="updatePro">修改所选产品</span> &nbsp;&nbsp;&nbsp;
                <span class="ty-linkBtn" @click="scanSelectPackage(proInfoData.info.mpPackageId)">查看所选产品</span>
              </td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 机构信息 -->
      <TyDialog v-if="orgInfoData.visible" width="600" dialogTitle="机构信息" color="blue" :dialogHide="hideOrgInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgInfo">关闭</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <div style="text-align: right">
              <span class="ty-linkBtn" @click="updateOrg">修改机构信息</span>
              <span class="ty-linkBtn" @click="getOrgHisList">机构信息变动记录</span>
            </div>
            <div style="text-align: right">
              <span class="ty-linkBtn" @click="getOrgCtrlList">暂停服务/恢复服务的操作记录</span>
            </div>
            <table>
              <tbody>
              <tr> <td>机构名称</td><td style="width: 270px;" v-html="orgInfoData.info.fullName"></td></tr>
              <tr> <td>机构简称</td> <td v-html="orgInfoData.info.name"></td></tr>
              <tr> <td>经营地址</td> <td v-html="orgInfoData.info.address"></td></tr>
              <tr> <td>超管姓名</td> <td v-html="orgInfoData.info.supervisorName"></td></tr>
              <tr> <td>超管手机</td> <td v-html="orgInfoData.info.supervisorMobile"></td></tr>
              <tr> <td>数据存储地点</td><td v-html="orgInfoData.info.uploadStorageType"></td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 机构信息变动记录 -->
      <TyDialog v-if="orgInfoHisData.visible" width="600" dialogTitle="机构信息变动记录" color="blue" :dialogHide="hideOrgInfoHis">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgInfoHis">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <table class="ty-table">
              <tbody>
              <tr>
                <td>资料状态</td>
                <td>操作</td>
                <td>创建人/修改人</td>
              </tr>
              <tr v-if="orgInfoHisData.list.length >0" v-for="(item , indexI) in orgInfoHisData.list" :key="indexI">
                <td>{{ item.dataState }}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="orgHisDetails(item, indexI)">查看</span></td>
                <td></td>
              </tr>
              <tr v-else><td colspan="3"> 暂无数据 </td></tr>
              </tbody>
            </table>

            <TyPage v-if="orgInfoHisData.pageShow"
                    :curPage="orgInfoHisData.pageInfo.currentPageNo" :pageSize="orgInfoHisData.pageInfo.pageSize"
                    :allPage="orgInfoHisData.pageInfo.totalPage" :pageClickFun="orgHisClick"></TyPage>



          </div>
        </template>
      </TyDialog>


      <!-- 机构信息变动记录 详情 -->
      <TyDialog v-if="orgHisInfoData.visible" width="600" dialogTitle="机构信息查看" color="blue" :dialogHide="hideOrgHisInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgHisInfo">关闭</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <table>
              <tbody>
              <tr> <td>机构名称</td><td style="width: 270px;" v-html="orgHisInfoData.info.fullName"></td></tr>
              <tr> <td>机构简称</td> <td v-html="orgHisInfoData.info.name"></td></tr>
              <tr> <td>经营地址</td> <td v-html="orgHisInfoData.info.address"></td></tr>
              <tr> <td>超管姓名</td> <td v-html="orgHisInfoData.info.supervisorName"></td></tr>
              <tr> <td>超管手机</td> <td v-html="orgHisInfoData.info.supervisorMobile"></td></tr>
              <tr> <td>数据存储地点</td><td v-html="orgHisInfoData.info.uploadStorageType"></td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 暂停服务/恢复服务的操作记录 -->
      <TyDialog v-if="orgEditLogData.visible" width="600" dialogTitle="暂停服务/恢复服务的操作记录" color="blue" :dialogHide="hideOrgEditLog">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgEditLog">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <table class="ty-table">
              <tbody>
              <tr>
                <td>操作性质</td>
                <td>操作者</td>
              </tr>
              <tr v-if="orgEditLogData.list.length > 0" v-for="(item,index) in orgEditLogData.list">
                <td>{{ item.operationNature }}</td>
                <td>{{ item.timeStr }}</td>
              </tr>
              <tr v-else>
                <td colspan="2">暂无数据</td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 新增机构 / 修改机构 / 修改产品信息 -->
      <TyDialog v-show="orgEditData.visible" width="600" :dialogTitle="orgEditData.title" color="blue" :dialogHide="hideOrgEdit">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgEdit">取消</span>
          <span class="ty-btn  ty-btn-big ty-circle-5" :class="orgEditOKBtn ? 'bounce-ok' : 'bounce-cancel'" @click="orgEditOK">确定</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <table>
              <tbody>
              <tr> <td>机构名称<i class="ty-color-red">*</i></td> <td style="width: 270px;"><el-input v-model="orgEditData.fullName" placeholder="请输入内容" @change="setName3" :disabled="orgEditData.type === 'update' || orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>机构简称<i class="ty-color-red">*</i></td> <td><el-input v-model="orgEditData.name" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>经营地址</td> <td><el-input v-model="orgEditData.address" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>超管姓名</td> <td><el-input v-model="orgEditData.supervisorName" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>超管手机<i class="ty-color-red">*</i></td> <td><el-input v-model="orgEditData.supervisorMobile" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>数据存储地点<i class="ty-color-red">*</i></td> <td>
                <el-select v-model="orgEditData.uploadStorageType" placeholder="请选择" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' ">
                  <el-option label="请选择" value=""></el-option>
                  <el-option label="NFS" value="NFS"></el-option>
                  <el-option label="Seafile" value="Seafile" disabled></el-option>
                </el-select>
              </td></tr>
              <tr v-show="orgEditData.type === 'add' || orgEditData.type === 'updatepro'"> <td>所选产品<i class="ty-color-red">*</i></td> <td>
                <el-select v-model="orgEditData.packageId" placeholder="请选择">
                  <el-option
                      v-for="item in orgEditData.packageList"
                      :key="item.id" :label="item.name" :value="item.id" >
                  </el-option>
                </el-select>
              </td></tr>
              <tr v-show="orgEditData.type === 'add'"><td></td><td><span class="ty-linkBtn" @click="scanSelectPackage(orgEditData.packageId)">查看所选产品</span></td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 删除客户 -->
      <TyDialog v-if="delCusData.visible" width="600" dialogTitle="删除客户" color="red" :dialogHide="hideDelCus">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideDelCus">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="delCusOK">确定</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 50px;">
            {{ delCusData.tip }}
          </div>
        </template>
      </TyDialog>


      <!-- 产品查看 -->
      <TyDialog v-if="packageData.visible" width="600" dialogTitle="产品查看" color="blue" :dialogHide="hidePackage">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hidePackage">关闭</span>
        </template>
        <template #dialogBody>
          <div class="packageInfo">
            <div class="packageName"> {{ packageData.info.mpPackages && packageData.info.mpPackages.name }} </div>
            <div>
              <div class="smallT">
                所选模板： <span class="pName">{{ packageData.info.mpTmpl && packageData.info.mpTmpl.name }}</span>
                <span class="ty-right">操作人：{{ packageData.info.mpPackages.createName }} {{ new Date(packageData.info.mpPackages.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </div>

              <div class="marT39">已重命名模块的数量 {{ packageData.info.reNameList.length }} 个</div>
              <el-table :data="packageData.info.reNameList || []" border style="width:360px">
                <el-table-column prop="name" label="原名称" width="180"> </el-table-column>
                <el-table-column prop="newName" label="新名称" width="180"> </el-table-column>
              </el-table>
              <div class="marT39">主套餐内的模块</div>
              <el-table :data="packageData.info.mainModuleList || []" border>
                <el-table-column prop="name" label="模块名称" width="180"> </el-table-column>
                <el-table-column label="下辖的一级菜单数量" width="180">
                  <template #default="scope1" class="ty-td-control"> {{ scope1.row.topMenu || '' }}个 </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope" >
                    <div class="ty-td-control"><span class="ty-color-blue" @click="seeModule(scope.row)">查看</span></div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="marT39">使用本模板的用户增值功能的模块</div>
              <el-table :data="packageData.info.increaseModuleList || []" border>
                <el-table-column prop="name" label="模块名称" width="180"> </el-table-column>
                <el-table-column label="下辖的一级菜单数量" width="180">
                  <template #default="scope1" class="ty-td-control"> {{ scope1.row.topMenu || '' }}个 </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope" >
                    <div class="ty-td-control"><span class="ty-color-blue" @click="seeModule(scope.row)">查看</span></div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="marT39">
                与本模板增值功能模块对应的已有套餐
                <span class="ty-right ty-linkBtn ccc" @click="packageList"> 查 看 </span>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 增值服务 -->
      <TyDialog v-if="addServiceData.visible" width="600" dialogTitle="增值服务" color="blue" :dialogHide="hideService">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideService">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="packageSubmit">提交</span>
        </template>
        <template #dialogBody>
          <div class="addService">
            <el-form-item label="机构名称">
              <el-input v-model="addServiceData.info.orgInfo.fullName" disabled></el-input>
            </el-form-item>
            <el-form-item label="机构简称">
              <el-input v-model="addServiceData.info.orgInfo.name" disabled></el-input>
            </el-form-item>
            <el-form-item label="所用产品">
              <el-input v-model="addServiceData.info.mpPackageInfo.name" disabled></el-input>
            </el-form-item>
            <el-form-item label="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
              <span class="ty-linkBtn" @click="seeModule(addServiceData.info.mpPackageInfo)">查看所选产品</span>
            </el-form-item>

            <div class="radioB">
              <span @click="toggleType(1)"><i class="fa" :class="addServiceData.type === 1 ? 'fa-dot-circle-o':'fa-circle-o'"></i>作为该产品的增值服务，仅从模块的角度勾选</span>
            </div>
            <el-table :data="addServiceData.info.increaseModuleList" border style="width: 100%">
              <el-table-column label="" width="50">
                <template #default="scope1">
                  <el-checkbox v-model="addServiceData.tab1" :label="scope1.row.id">&nbsp;</el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="模块名称" width="180">
              </el-table-column>
              <el-table-column prop="name" label="下辖的一级菜单数量" width="180">
                <template #default="scope2">
                  {{ scope2.row.topMenu || '--' }} 个
                </template>
              </el-table-column>
              <el-table-column prop="address" label="操作">
                <template #default="scope">
                  <div>
                    <span class="ty-color-blue" @click="seeModule(scope.row)">查看</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="radioB">
              <span @click="toggleType(2)"><i class="fa" :class="addServiceData.type === 2 ? 'fa-dot-circle-o':'fa-circle-o'"></i>作为该产品的增值服务，仅从模块的角度勾选</span>
            </div>
            <el-table :data="addServiceData.info.mpSetList" border style="width: 100%">
              <el-table-column label="" width="50">
                <template #default="scope1">
                  <el-radio v-model="addServiceData.tab2" :label="scope1.row.id">&nbsp;</el-radio>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="模块名称" width="180">
              </el-table-column>
              <el-table-column prop="name" label="下辖的一级菜单数量" width="180">
                <template #default="scope2">
                  {{ scope2.row.topMenu || '--' }} 个
                </template>
              </el-table-column>
              <el-table-column prop="address" label="操作">
                <template #default="scope">
                  <div>
                    <span class="ty-color-blue" @click="seeMenu(scope.row)">查看</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>

          </div>
        </template>
      </TyDialog>

      <!-- seeSetMenu -->
      <TyDialog v-if="seeSetMenuData.visible" width="600" dialogTitle="套餐查看" color="blue" :dialogHide="hideSeeSetMenu">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideSeeSetMenu">关闭</span>
        </template>
        <template #dialogBody>
          <div class="seeSetMenu">
            <h4><span class="setMenuName">{{ seeSetMenuData.setMenuName }}</span></h4>
            <div class="ty-alert">
              组成本套餐的模块
              <div class="ty-right">
                创建 <span class="create">{{ seeSetMenuData.create }}</span>
              </div>
            </div>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr><td>模块名称</td>
                <td>下辖一级菜单数量</td>
                <td>操作</td></tr>
              <tr v-for="(iitem , iiIndex) in seeSetMenuData.list" :key="iiIndex">
                <td>{{ iitem.name }}</td>
                <td>{{ iitem.topMenu || '' }}</td>
                <td>
                  <span class="ty-color-blue" @click="seeModule(iitem)">查看</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>




      <!-- 暂停合作 客户 -->
      <TyDialog v-if="paulsCusData.visible" width="600" dialogTitle="！提示" color="blue" :dialogHide="hidePaulsCus">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hidePaulsCus">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="paulsCusOK">确定</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 50px;" v-html="paulsCusData.tip">
          </div>
        </template>
      </TyDialog>

      <!-- 批量导入-->
      <TyDialog class="custormerLeading" v-if="custormerLeadingData.visible" width="550" dialogTitle="批量导入" color="blue" :dialogHide="hideloadingFun">
        <template #dialogBody>
          <div>
            <div class="exportStep">
              <div class="stepItem">
                <p>第一步：下载空白的“客户清单”。</p>
                <div class="flexRow">
                  <span>客户清单</span>
                  <a :href="customer_blank"
                     id="mould1" download="客户清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
                </div>
              </div>
              <div class="stepItem">
                第二步：在空白的“客户清单”中填写内容，并存至电脑。
              </div>
              <div class="stepItem">
                <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                <div class="flexRow">
                  <div class="upload_sect viewBtn">
                    <div>
                      <el-input readonly id="uploadFile" placeholder="尚未选择文件" v-model="custormerLeadingData.fileName">
                        <template #prepend>
                          <uploadFile ref="uploadFile" class="ty-right"
                                      module="销售管理"
                                      committed.async="false"
                                      :showSelect="false"
                                      :showFileList="false"
                                      :autoUpload="true"
                                      ext='.xls,.xlsx'
                                      extMsg='请按照客户清单模版上传！'
                                      :successFun="fileSuccess"
                          >
                            <template #btnArea>
                              <div>
                                <span @click="uploadFile">浏 览</span>
                              </div>
                            </template>
                          </uploadFile>
                        </template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </div>
              <div class="stepItem">
                <p>第四步：点击“导入”。</p>
                <div class="flexRow">
                  <span class="ty-btn ty-btn-yellow ty-btn-middle" @click="hideloadingFun">取 消</span>
                  <span class="ty-btn ty-btn-blue ty-btn-middle" @click="matImportOk">导 入</span>
                </div>
              </div>
              <div class="importIntro stepItem">
                <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                <div style="text-align:left;">
                  <span>1、请勿增加、删除或修改所下载“客户清单”空白表的“列”，否则上传会失败。</span><br>
                  <span>2、在电脑上保存“客户清单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 管理-->
      <TyDialog class="updateCustomerPanel" v-if="updateCustomerData.visible" width="800" dialogTitle="客户信息管理" color="blue" :dialogHide="hideUpdateCustomerFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideUpdateCustomerFun">确定</span>
        </template>
        <template #dialogBody>
          <div class="" style="max-height:400px; ">
            <div class="cusName">{{ updateCustomerData.editCustomer.name }}</div>
            <table class="ty-table ty-table-control ty-table-txtLeft">
              <tbody>
              <tr class="manageTtl">
                <td>名称</td>
                <td>操作</td>
              </tr>
              <tr>
                <td>基本信息</td><td>
                  <span class="ty-color-blue" @click="baseUpdate">修改</span>
                  <span class="ty-color-blue" @click="baseUpdateLog">修改记录</span>
              </td>
              </tr>
              <tr>
                <td>开票信息</td>
                <td>
                  <span class="ty-color-blue" @click="invoiceManageUpdate">修改</span>
                  <span class="ty-color-blue" @click="invoiceManageUpdateLog">修改记录</span>
                </td>
              </tr>
              <tr>
                <td>合同信息</td>
                <td>
                  <span class="ty-color-blue" @click="contractIAdd">新增</span>
                  <span class="ty-color-blue" @click="contractIEnds">已到期的合同</span>
                  <span class="ty-color-blue" @click="contractIStops">已暂停/终止的合同</span>
                </td>
              </tr>
              <tr v-for="(contractItem, contractIndex) in updateCustomerData.contractBaseList" :key="contractIndex">
                <td class="pdL40" v-html="contractItem.sn"></td>
                <td>
                  <span class="ty-color-blue" @click="contractIScan(contractItem, contractIndex)">查看</span>
                  <span class="ty-color-blue" @click="contractIUpdate(contractItem, contractIndex)">修改合同信息</span>
                  <span class="ty-color-blue" @click="contractIXu(contractItem, contractIndex)">续约</span>
                  <span class="ty-color-red" @click="contractIStop(contractItem, 'terminateContract')">暂停履约/合同终止</span>
                </td>
              </tr>
              <tr>
                <td> {{ updateCustomerData.info.deliveryType === 2 ? '收货地址（需送货上门情况下）' : '不向该客户提供实体货物，故未编辑收货信息' }} </td>
                <td>
                  <span v-if="updateCustomerData.info.deliveryType === 2" class="marLl20">
                    <span class="ty-color-blue" @click="adressAdd">新增</span>
                    <span class="ty-color-blue" @click="adressStopList(1)">已被停用的数据</span>
                  </span>
                  <span v-else class="marLl20">
                    <span class="ty-color-blue" @click="shInfoEdit">编辑</span>
                  </span>
                </td>
              </tr>
              <tr v-for="(shItem, indexSh) in updateCustomerData.adressList" :key="indexSh">
                <td class="pdL40">{{ shItem.address }}</td>
                <td>
                  <span class="ty-color-blue" @click="shItemUpdate(shItem, indexSh)">修改</span>
                  <span class="ty-color-blue" @click="shItemUpdateLog(shItem, 'adress')">修改记录</span>
                  <span class="ty-color-red" @click="addressStopBtn(shItem, indexSh)">停用</span>
                </td>
              </tr>
              <tr v-if="updateCustomerData.info.deliveryType === 2">
                <td>到货区域（需配送至某城市情况下）</td>
                <td>
                  <span class="ty-color-blue" @click="addArea('manageAdd')">新增</span>
                  <span class="ty-color-blue" @click="adressStopList(3)">已被停用的数据</span>
                </td>
              </tr>
              <tr v-if="updateCustomerData.info.deliveryType === 2" v-for="(areaItem, indexArea) in updateCustomerData.areaList" :key="indexArea">
                <td v-html="areaItem.address" class="pdL40"></td>
                <td>
                  <span class="ty-color-blue" @click="shAreaItemUpdate(areaItem, indexArea)">修改</span>
                  <span class="ty-color-blue" @click="shItemUpdateLog(areaItem, 'area')">修改记录</span>
                  <span class="ty-color-red" @click="addressStopBtn(areaItem, indexArea)">停用</span>
                </td>
              </tr>
              <tr>
                <td>发票邮寄信息</td>
                <td>
                  <span class="ty-color-blue" @click="fpAdd">新增</span>
                  <span class="ty-color-blue" @click="adressStopList(2)">已被停用的数据</span>
                </td>
              </tr>
              <tr v-for="(fpItem, fpIndex) in updateCustomerData.info.fpAddressList" :key="fpIndex">
                <td class="pdL40" v-html="fpItem.address"></td>
                <td>
                  <span v-if="fpItem.enabled" class="marLl20">
                    <span class="ty-color-blue" @click="fpAdressUpdate(fpItem, fpIndex)">修改</span>
                    <span class="ty-color-blue" @click="shItemUpdateLog(fpItem, 'fp')">修改记录</span>
                    <span class="ty-color-red" @click="addressStopBtn(fpItem, fpIndex)">停用</span>
                  </span>
                  <span v-else class="marLl20">
                    <span class="ty-color-red" @click="fpAdressStart(fpItem, fpIndex)">启用</span>
                    <span class="ty-color-blue" @click="fpAdressUpdateLog(fpItem, fpIndex)">修改记录</span>
                  </span>
                </td>
              </tr>
              <tr>
                <td>联系人</td>
                <td>
                  <span class="ty-color-blue" @click="addContact('manageAdd')">新增</span>
                  <span class="ty-color-blue" @click="addContactList">已被删除的数据</span>
                </td>
              </tr>
              <tr v-for="(contactItem, contactIndex) in updateCustomerData.info.contactsList" :key="contactIndex">
                <td class="pdL40">
                  <span v-if="contactItem.enabled">
                    {{ contactItem.name }}  {{ contactItem.post }}  {{ contactItem.mobile }}
                  </span>
                  <span v-else>
                    {{ contactItem.name }}
                  </span>
                </td>
                <td>
                  <span v-if="contactItem.enabled" class="marLl20">
                    <span class="ty-color-blue" @click="contactUpdate(contactItem, contactIndex)">修改</span>
                    <span class="ty-color-blue" @click="contactUpdateLog(contactItem, contactIndex)">修改记录</span>
                    <span class="ty-color-red" @click="contactDel(contactItem, contactIndex)">删除</span>
                  </span>
                  <span v-else class="marLl20">
                    <span class="ty-color-blue" @click="contactUpdateLog(contactItem, contactIndex)">修改记录</span>
                  </span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 已被删除的联系人数据-->
      <TyDialog v-if="contactStopData.visible" width="600" dialogTitle="已被删除的联系人" color="red" :dialogHide="hideContactStop">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideContactStop">关闭</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
            <table class="ty-table ty-table-control ">
              <tbody>
              <tr><td class="txtLeft">名称</td><td class="txtLeft" style="padding-left:30px;">操作</td></tr>
              <tr v-for="(contactItem, ccii) in contactStopData.list" :key="ccii">
                <td class="txtLeft">
                  {{ contactItem.name }}
                  <span class="ty-right" v-html="(contactItem.post && contactItem.post.substr(0,8)) || ''"></span>
                </td>
                <td class="txtLeft">
                  <span class="ty-color-blue" @click="contactUpdateLog(contactItem)">修改记录</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 客户信息查看-->
      <TyDialog class="custormerAdd cusScan" v-if="seeAccountData.visible" width="1226" dialogTitle="客户信息查看" color="blue" :dialogHide="hideSeeFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSeeFun">关闭</span>
        </template>
        <template #dialogBody>
          <div class="custormerAddCC">
            <table style="width:1100px;">
              <tbody>
              <tr>
                <td colspan="6" class="ttl">基本信息</td>
              </tr>
              <tr>
                <td class="txtRight" width="120px;">客户名称：</td>
                <td colspan="3"><span v-html="seeAccountData.info.fullName"></span></td>
                <td class="txtRight">客户代号：</td>
                <td><span v-html="seeAccountData.info.code"></span>
                </td>
              </tr>
              <tr>
                <td class="txtRight">地址：</td>
                <td colspan="3">
                  <span v-html="seeAccountData.info.address"></span>
                </td>
                <td class="txtRight">客户简称：</td>
                <td><span v-html="seeAccountData.info.name"></span></td>
              </tr>
              <tr>
                <td colspan="6">
                  <el-alert v-if="seeAccountData.info.initialType === null" :title="(new Date(seeAccountData.info.createDate).format('yyyy-MM-dd hh:mm:ss') ) + '之前该客户尚未购买过本公司的商品'" type="error" :closable="false"></el-alert>
                  <div v-else>
                    <el-alert v-if="seeAccountData.info.initialType === '1'" :title="seeAccountData.info.initialPeriod.substr(0,4) +'年' + seeAccountData.info.initialPeriod.substr(4,2) + '月' + '该客户首次购买过本公司的商品'" type="success" :closable="false"></el-alert>
                    <el-alert v-else-if="seeAccountData.info.initialType === '2'" title="去年该客户首次购买过本公司的商品" type="success" :closable="false"></el-alert>
                    <el-alert v-else-if="seeAccountData.info.initialType === '3'" :title="seeAccountData.info.initialPeriod + '年之前该客户首次购买过本公司的商品'" type="success" :closable="false"></el-alert>
                    <el-alert v-else title="去年之前该客户首次购买过本公司的商品" type="success" :closable="false"></el-alert>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">创建者：</td>
                <td colspan="3">
                  <span v-html="seeAccountData.info.createName + ' ' + new Date(seeAccountData.info.createDate).format('yyyy-MM-dd hh:mm:ss')"></span>
                </td>
                <td class="txtRight" colspan="2">
                  <span class="ty-btn ty-btn-blue ty-circle-3" @click="getSuspendRecordList">暂停合作/恢复合作的操作记录</span>
                  <span class="ty-btn ty-btn-blue ty-circle-3" style="margin-left:50px; " @click="getRecordList">修改记录</span>
                </td>
              </tr>
              <tr>
                <td class="txtRight">全景照片：</td>
                <td colspan="5">
                </td>
              </tr>
              <tr>
                <td></td>
                <td colspan="5">
                  <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in seeAccountData.info.qImages" :key="indexC2">
                        <el-image class="img" :src="rootPath.fileUrl + img.normal" fit="cover" :preview-src-list="[rootPath.fileUrl + img.normal]"></el-image>
                      </span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">产品图片：</td>
                <td colspan="5">
                </td>
              </tr>
              <tr>
                <td></td>
                <td colspan="5">
                  <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in seeAccountData.info.pImages" :key="indexC2">
                        <el-image class="img" :src="rootPath.fileUrl + img.normal" fit="cover" :preview-src-list="[rootPath.fileUrl + img.normal]"></el-image>
                      </span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">首次接触时间：</td>
                <td>
                  <span v-html="new Date(seeAccountData.info.firstContactTime).format('yyyy-MM-dd') "></span>
                </td>
                <td class="txtRight" width="120px">首次接触地点：</td>
                <td><span v-html="seeAccountData.info.firstContactAddress"></span></td>
                <td class="txtRight" width="120px">信息获取渠道：</td>
                <td><span v-html="seeAccountData.info.infoSource"></span></td>
              </tr>
              <tr>
                <td class="txtRight">备注：</td>
                <td colspan="5"><span v-html="seeAccountData.info.memo"></span></td>
              </tr>
              <tr>
                <td colspan="6"><el-divider></el-divider></td>
              </tr>
              <tr>
                <td class="ttl">开票信息</td>
                <td colspan="5" class="txtRight">
                  <span>客户对发票方面的要求</span>
                  <span class="marL30">{{ formatInviceRequire(seeAccountData.info.invoiceRequire) }}</span>
                  <span class="ty-btn ty-btn-blue ty-circle-3 marL30" @click="invoiceEditLog">修改记录</span>
                </td>
              </tr>
              <tr>
                <td class="txtRight">公司名称：</td>
                <td>
                  <span v-html="seeAccountData.info.invoiceName"></span>
                </td>
                <td class="txtRight">地址：</td>
                <td><span v-html="seeAccountData.info.invoiceAddress"></span></td>
                <td class="txtRight">电话：</td>
                <td><span v-html="seeAccountData.info.telephone"></span></td>
              </tr>
              <tr>
                <td class="txtRight">开户行：</td>
                <td><span v-html="seeAccountData.info.bankName"></span></td>
                <td class="txtRight">账号：</td>
                <td><span v-html="seeAccountData.info.bank_no"></span></td>
                <td class="txtRight">税号：</td>
                <td><span v-html="seeAccountData.info.taxpayerID"></span></td>
              </tr>
              <tr>
                <td class=" ttl">联系人</td>
                <td colspan="5">
                  该客户现共有如下1位联系人
                </td>
              </tr>
              <tr>
                <td colspan="6" v-if="seeAccountData.info.contactsList && seeAccountData.info.contactsList.length > 0">
                  <el-table v-if="seeAccountData.info.contactsList.length === 1" :data="seeAccountData.info.contactLianxiTwo" stripe border style="width:50%">
                    <el-table-column prop="name" label="姓名" width="100"> </el-table-column>
                    <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                    <el-table-column label="操作">
                      <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="ctctDetails(scope.row, 1)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 0)">查看名片</span>
                            <span class="ty-color-blue" @click="contactUpdateLogSee(scope.row,0)">修改记录</span>
                          </span>
                      </template>
                    </el-table-column>
                  </el-table>

                  <el-table v-else :data="seeAccountData.info.contactLianxiTwo" stripe border style="width: 100%">
                    <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                    <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                    <el-table-column label="操作">
                      <template #default="scope">
                           <span class="ty-td-control">
                            <span class="ty-color-blue" @click="ctctDetails(scope.row, 1)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 1)">查看名片</span>
                            <span class="ty-color-blue" @click="contactUpdateLogSee(scope.row,1)">修改记录</span>
                          </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name2" label="姓名" width="80"> </el-table-column>
                    <el-table-column prop="post2" label="职位" width="100"> </el-table-column>
                    <el-table-column label="操作" >
                      <template #default="scope">
                          <span class="ty-td-control" v-if="scope.row.name2">
                            <span class="ty-color-blue" @click="ctctDetails(scope.row, 2)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 2)">查看名片</span>
                            <span class="ty-color-blue" @click="contactUpdateLogSee(scope.row,2)">修改记录</span>
                          </span>
                      </template>
                    </el-table-column>
                  </el-table>

                </td>
              </tr>
              <tr>
                <td colspan="6" class="ttl">货物的交付地点与方式</td>
              </tr>
              <tr>
                <td colspan="6" style="padding-left: 40px;">
                  {{ seeAccountData.info.deliveryType === 2 ? '向该客户提供实体货物' :'向该客户只提供服务，不提供实体货物，不需编辑收货地址' }}
                  <span class="ty-btn ty-btn-blue marL30 ty-circle-3 ty-right" @click="showHuo">查看</span>
                </td>
              </tr>
              <tr>
                <td class=" ttl">合同信息</td>
                <td colspan="5">
                </td>
              </tr>
              <tr v-if="seeAccountData.contractBaseList.length >0">
                <td colspan="6">
                  <el-table :data="seeAccountData.contractBaseList" stripe border width="100%">
                    <el-table-column prop="no" label="合同编号" width="160" > </el-table-column>
                    <el-table-column prop="date" label="签署日期" width="120"> </el-table-column>
                    <el-table-column prop="expDur" label="合同的有效期" width="200"></el-table-column>
                    <el-table-column prop="tyNum" label="本合同下的通用型商品" > </el-table-column>
                    <el-table-column prop="zsNum" label="本合同下的专属商品" > </el-table-column>
                    <el-table-column label="操作" width="90">
                      <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="contractIScan(scope.row,scope.$index)">查看</span>
                          </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </td>
              </tr>
              <tr>
                <td class="ttl">发票邮寄信息</td>
                <td colspan="5">
                </td>
              </tr>
              <tr v-if="seeAccountData.info.fpAddressList && seeAccountData.info.fpAddressList.length > 0">
                <td colspan="6">
                  <el-table :data="seeAccountData.info.fpAddressList" stripe border style="width: 100%">
                    <el-table-column type="index" label="序号" width="80"> </el-table-column>
                    <el-table-column prop="address" label="邮寄地址" width="340"> </el-table-column>
                    <el-table-column prop="contact" label="发票接收人" width="180"> </el-table-column>
                    <el-table-column prop="postcode" label="邮政编码" width="180"> </el-table-column>
                    <el-table-column label="操作">
                      <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="shItemUpdateLog(scope.row, 'fp')" >修改记录</span>
                          </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </td>
              </tr>

              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 联系人 名片-->
      <TyDialog class="receiveInfo" v-if="contactCard.visible" width="600" dialogTitle="名片" color="blue" :dialogHide="hideCrD">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideCrD">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <el-image
                style=" width: 600px; margin: 10px auto; height: 280px;"
                :src="contactCard.visitCard"
                fit="contain"
            >
              <template #placeholder>
                <div style="font-size: 28px; text-align: center; color: #666; width:600px; line-height:100px; ">
                  <i class="fa fa-spinner rotating"></i> 加载中 ...
                </div>
              </template>
              <template #error>
                <div style="font-size: 28px; text-align: center; color: #666; width:600px; line-height:100px; ">
                  <i class="fa fa-file-image-o"></i> 加载失败！
                </div>
              </template>
            </el-image>
          </div>
        </template>
      </TyDialog>

      <!-- 联系方式查看-->
      <TyDialog class="receiveInfo" v-if="contactDetails.visible" width="600" dialogTitle="客户联系人" color="blue" :dialogHide="hideCD">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideCD">关闭</span>
        </template>
        <template #dialogBody>
          <div style="line-height:30px; ">
            <table>
              <tbody>
              <tr>
                <td>姓名：</td>
                <td width="200px" v-html="contactDetails.info.name"></td>
                <td>职位：</td>
                <td v-html="contactDetails.info.post"></td>
              </tr>
              <tr>
                <td>联系人标签：</td>
                <td colspan="3" v-html="contactDetails.info.tags"></td>
              </tr>

              </tbody>
            </table>
            <table class="ty-table" v-if="contactDetails.contactList.length > 0" style="width:90%;margin:0 auto; ">
              <tbody>
              <tr v-for="(contact, index) in contactDetails.contactList" :key="index">
                <td class="txtRight" v-html="contact.name + ':'"></td>
                <td v-html="contact.val" class="txtLeft"></td>
                <td class="txtRight"><span v-if="contact.name2" v-html="contact.name2 + ':'"></span></td>
                <td class="txtLeft">
                  <div v-if="contact.name2" v-html="contact.val2">
                  </div>
                </td>
              </tr>
              </tbody>
            </table>

          </div>
        </template>
      </TyDialog>


      <!-- 收货信息查看-->
      <TyDialog class="receiveInfo" v-if="seeAccountData.visibleSH" width="800" dialogTitle="收货信息" color="blue" :dialogHide="hideReceiveInfoSHFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideReceiveInfoSHFun">关闭</span>
        </template>
        <template #dialogBody>
          <div class="nextcc" style="width: 80%; margin:0 auto;">
            <div style="border-top: none;">
              <span>客户提供送货上门服务</span>
              <br>
              <el-table v-if="seeAccountData.addressList.length >0" :data="seeAccountData.addressList" border stripe style="width: 100%">
                <el-table-column prop="address" label="收货地址" width="180"> </el-table-column>
                <el-table-column prop="contact" label="收货人" width="180"> </el-table-column>
                <el-table-column prop="mobile" label="收货电话"> </el-table-column>
              </el-table>
            </div>
            <div>
              <span>向该客户提供配送至某城市的服务</span>
              <br>
              <el-table v-if="seeAccountData.areaList.length > 0" :data="seeAccountData.areaList" border stripe style="width: 100%">
                <el-table-column prop="address" label="到货区域" width="280"> </el-table-column>
                <el-table-column prop="contact" label="收货人" width="100"> </el-table-column>
                <el-table-column prop="mobile" label="收货电话"> </el-table-column>
              </el-table>
            </div>
            <div>
              <span v-html="seeAccountData.selfStateTxt"></span>
              <br>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 联系人修改记录 -->
      <TyDialog v-if="contactUpdateLogData.visible" width="600" dialogTitle="联系人修改记录" color="blue" :dialogHide="hideContactUpdateLog">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideContactUpdateLog">关闭</span>
        </template>
        <template #dialogBody>
          <div style="padding: 20px 0;">
            <span v-html="contactUpdateLogData.tip"></span>
            <span class="ty-right" v-html="contactUpdateLogData.tipTime"></span>
          </div>
          <div>
            <table class="ty-table ty-table-control " v-if="contactUpdateLogData.list.length>0">
              <tbody>
              <tr><td>记录</td><td>操作</td><td>创建者/修改者</td></tr>
              <tr v-for="(logItem, indexLog) in contactUpdateLogData.list" :key="indexLog">
                <td>{{ indexLog === 0 ? '原始信息' : '第' + indexLog + '修改后' }}</td>
                <td class="ty-td-control">
                  <span class="ty-color-blue" @click="contactlogDetail(logItem, indexLog)">查看</span>
                </td>
                <td>
                  {{ indexLog === 0 ? logItem.createName : logItem.updateName  }}
                  {{ new Date( indexLog === 0 ? logItem.createDate : logItem.updateDate ).format('yyyy-MM-dd hh:mm:ss') }}
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 联系人修改记录 详情 -->
      <TyDialog class="editContactLianXi" v-if="contactUpdateLogDetailsData.visible" width="750" :dialogTitle="contactUpdateLogDetailsData.title" color="blue" :dialogHide="hidetitleFun">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok"  @click="hidetitleFun">关闭</span>
        </template>
        <template #dialogBody>
          <div class="editContactLianXicc contactUpdateLogDetails">
            <table>
              <tr>
                <td class="txtRight">姓名：</td>
                <td v-html="contactUpdateLogDetailsData.name"></td>
                <td class="txtRight">职位：</td>
                <td v-html="contactUpdateLogDetailsData.postName"></td>
              </tr>
              <tr>
                <td class="txtRight">手机：</td>
                <td v-html="contactUpdateLogDetailsData.tel"></td>
                <td></td>
                <td></td>
              </tr>
              <tr v-for="(contact, index) in contactUpdateLogDetailsData.contactList" :key="index">
                <td class="txtRight" v-html="contact.name + ':'"></td>
                <td v-html="contact.val"></td>
                <td class="txtRight"><span v-if="contact.name2" v-html="contact.name2 + ':'"></span></td>
                <td >
                  <div v-if="contact.name2" v-html="contact.val2">
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">名片：</td>
                <td colspan="3"></td>
              </tr>
              <tr >
                <td class="txtRight"></td>
                <td colspan="3">
                  <div v-if="contactUpdateLogDetailsData.picSrc" class="imgcc">
                    <img :src="contactUpdateLogDetailsData.picSrc" alt="名片">
                  </div>
                </td>
              </tr>
            </table>

          </div>
        </template>
      </TyDialog>

      <!-- 被停用的收货信息-->
      <TyDialog v-if="stopShAdressData.visible" width="600" :dialogTitle="stopShAdressData.title" color="red" :dialogHide="hideStopShAdress">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideStopShAdress">关闭</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
            <table class="ty-table ty-table-control ">
              <tbody>
              <tr><td class="txtLeft">名称</td><td class="txtLeft" style="padding-left:30px;">操作</td></tr>
              <tr v-for="(adressItem, adItm) in stopShAdressData.list" :key="adItm">
                <td class="txtLeft">{{ adressItem.address }}</td>
                <td class="txtLeft">
                  <span class="ty-color-red" @click="startAdress(adressItem)">启用</span>
                  <span class="ty-color-blue" @click="shItemUpdateLog(adressItem, stopShAdressData.typeStr)">修改记录</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 收货信息 到货区域  修改记录 发票邮寄信息-->
      <TyDialog v-if="adressLogData.visible"  width="600" :dialogTitle="adressLogData.title" color="blue" :dialogHide="hideAdressLog">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideAdressLog">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <span v-html="adressLogData.tip"></span>
            <span class="ty-right" v-html="adressLogData.tipTime"></span>
          </div>
          <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
            <table class="ty-table " v-if="adressLogData.list.length >0">
              <tbody>
              <tr><td>记录</td><td>操作</td><td>创建者/修改者</td></tr>
              <tr v-for="(logItem, indexLog) in adressLogData.list" :key="indexLog">
                <td>{{ indexLog === 0 ? '原始信息' : '第' + indexLog + '修改后' }}</td>
                <td class="ty-td-control">
                  <span class="ty-color-blue" @click="logDetail(logItem, indexLog)">查看</span>
                </td>
                <td>
                  {{ indexLog === 0 ? logItem.createName : logItem.updateName  }}
                  {{ new Date( indexLog === 0 ? logItem.createDate : logItem.updateDate ).format('yyyy-MM-dd hh:mm:ss') }}
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <TyDialog v-if="adressLogDetailsData.visible" :dialogTitle="adressLogDetailsData.title" color="blue" :dialogHide="hideAdressLogDetails">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideAdressLogDetails">关闭</span>
        </template>
        <template #dialogBody>
          <div class="adressLogDetails">
            <div style="color: red; padding:20px;" v-html="adressLogDetailsData.shDisUse"></div>
            <table v-if="adressLogData.type === 'fp'">
              <tbody>
              <tr><td>邮寄地址：</td><td v-html="adressLogDetailsData.shAddress"></td></tr>
              <tr><td>发票接收人：</td><td v-html="adressLogDetailsData.shName"></td></tr>
              <tr><td>联系电话：</td><td v-html="adressLogDetailsData.shNumber"></td></tr>
              <tr><td>邮寄编码：</td><td v-html="adressLogDetailsData.shCode"></td></tr>
              </tbody>

            </table>
            <table v-else>
              <tbody>
              <tr><td>收货地址：</td><td v-html="adressLogDetailsData.shAddress"></td></tr>
              <tr><td>收货人：</td><td v-html="adressLogDetailsData.shName"></td></tr>
              <tr><td>收货电话：</td><td v-html="adressLogDetailsData.shNumber || '' "></td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 修改收货地址 提示 -->
      <TyDialog v-if="updateContactData.visible1" dialogTitle="！提示" color="blue" :dialogHide="hideUpdateCt1">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideUpdateCt1">关闭</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="updateCtOK1">继续操作</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
            此处的修改，适用于该地址中错别字之类的修改。<br>
            修改为另一个地点，请勿使用本“修改”！
          </div>
        </template>
      </TyDialog>

      <!-- 收货地址adress 停用 提示 -->
      <TyDialog v-if="stopAdresstData.visible" dialogTitle="提示" color="red" :dialogHide="hideShAdress">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideStopCt">关闭</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="shAdressStopOK">确定</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 50px;">
            确定停用吗？
          </div>
        </template>
      </TyDialog>

      <!-- 删除联系人提示 -->
      <TyDialog v-if="stopContactData.visible" dialogTitle="提示" color="red" :dialogHide="hideStopCt">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideStopCt">关闭</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="stopCtOK">确定</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 50px;">
            删除后，本联系人依旧显示在查看中，但仅可查看。
          </div>
        </template>
      </TyDialog>

      <!-- 已暂停/终止的合同 列表-->
      <TyDialog class="updateCustomerPanel" v-if="stopContractData.visible" width="800" dialogTitle="已暂停履约/终止的合同" color="blue" :dialogHide="hideStopC">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideStopC">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>合同编号</td>
                <td>暂停履约/终止的时间</td>
                <td>操作</td>
              </tr>
              <tr v-for="(item, index) in stopContractData.list" :key="index">
                <td>{{ item.sn }}</td>
                <td>{{ new Date(item.suspendTime).format("yyyy-MM-dd hh:mm:ss")  }}</td>
                <td>
                  <span class="ty-color-blue" @click="contractIScan(item, index)">查看</span>
                  <span class="ty-color-blue" @click="contractIStop(item, 'reStartContract')">恢复履约/重启合作</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 已到期的合同 列表-->
      <TyDialog class="updateCustomerPanel" v-if="endContractData.visible" width="600" dialogTitle="已到期的合同" color="blue" :dialogHide="hideEndC">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideEndC">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>合同编号</td>
                <td>到期日</td>
                <td>操作</td>
              </tr>
              <tr v-for="(item, index) in endContractData.list" :key="index">
                <td>{{ item.sn }}</td>
                <td>{{ new Date(item.validEnd).format("yyyy-MM-dd")  }}</td>
                <td>
                  <span class="ty-color-blue" @click="contractIScan(item, index)">查看</span>
                  <span class="ty-color-blue" @click="contractIXu(item, index, 'end')">续约</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 暂停/终止 或者  恢复履约/重启合作-->
      <TyDialog class="updateCustomerPanel" v-if="contractStopData.visible" width="460" dialogTitle="！提示" :color="contractStopData.color" :dialogHide="hideCS">
        <template #dialogFooter>
          <span class="ty-btn bounce-cencel ty-btn-big ty-circle-5" @click="hideCS">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="contractIStopOK">确定</span>
        </template>
        <template #dialogBody>
          <div class="contractStop" v-html="contractStopData.tipTxt">
          </div>
        </template>
      </TyDialog>

      <!-- 基本信息 修改 -->
      <TyDialog class="manageBase" v-show="manageBaseData.visible" width="1060" dialogTitle="基本信息修改" color="blue" :dialogHide="hideManageBase">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideManageBase">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5 " :class="manageBaseOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="manageBaseOk">提交</span>
        </template>
        <template #dialogBody>
          <div class="custormerAddCC">
            <table>
              <tbody>
              <tr>
                <td class="txtRight" width="120px;"><i class="ty-color-red">*</i>客户名称：</td>
                <td colspan="3"><el-input v-model="manageBaseData.fullName" placeholder="请输入" @change="setName"></el-input></td>
                <td class="txtRight">客户代号：</td>
                <td><el-input v-model="manageBaseData.cuscoding" placeholder="请输入"></el-input></td>
              </tr>
              <tr>
                <td class="txtRight">地址：</td>
                <td colspan="3"><el-input v-model="manageBaseData.address1" placeholder="请输入"></el-input></td>
                <td class="txtRight">客户简称：</td>
                <td><el-input v-model="manageBaseData.name" placeholder="请输入"></el-input></td>
              </tr>
              <tr v-if="specialOrgPart">
                <td class="txtRight" width="120px;"><i class="ty-color-red">*</i>最高负责人：</td>
                <td colspan="2"><el-input v-model="manageBaseData.supervisorName" placeholder="请输入"></el-input></td>
                <td colspan="3" style="padding-left: 80px; position: relative; ">
                  <span style="position: absolute; left:30px;"><i class="ty-color-red">*</i>手机：</span>
                  <el-input v-model="manageBaseData.supervisorMobile" placeholder="请输入"></el-input>
                </td>
              </tr>
              <tr>
                <td colspan="6">
                  <span class="marL30"><i class="ty-color-red">*</i>成为公司客户的时间？</span>
                  <span class="marL30">
                      <span class="radiop" @click="toggefirstTime(1, 'manage')"><i class="fa" :class="manageBaseData.firstTime === 1? 'fa-dot-circle-o':'fa-circle-o'"></i>今年</span>
                      <span v-show="manageBaseData.firstTime === 1">
                         <el-date-picker v-model="manageBaseData.initialPeriod" type="month" placeholder="选择月" @change="setFirstTimeN"
                                         style="width: 100px" format="M月" value-format="YYYYMM" :disabled-date="disabledDate"></el-date-picker>
                          <span class="radiop" @click="toggefirstTimeN('manage')"><i class="fa" :class="manageBaseData.firstTimeN? 'fa-dot-circle-o':'fa-circle-o'"></i>不确定月份</span>
                      </span>
                      <span class="radiop" @click="toggefirstTime(2, 'manage')"><i class="fa" :class="manageBaseData.firstTime === 2? 'fa-dot-circle-o':'fa-circle-o'"></i>去年</span>
                      <span class="radiop" @click="toggefirstTime(3, 'manage')"><i class="fa" :class="manageBaseData.firstTime === 3? 'fa-dot-circle-o':'fa-circle-o'"></i>更久之前</span>
                    </span>
                </td>
              </tr>
              <tr>
                <td class="txtRight">全景照片：</td>
                <td colspan="5">
                  <span></span>
                  <div class="pa20" >
                    <uploadFile ref="uploadFile2" class="ty-right"
                                module="客户管理"
                                committed.async="false"
                                :showSelect="false"
                                :showFileList="false"
                                :autoUpload="true"
                                ext='.png,.jpg,.jpeg,'
                                extMsg='请上传照片！'
                                :multiple="true"
                                :beforeUploadFun="beforeUpload1_manage"
                                :successFun="fileSuccess1_manage">
                      <template #btnArea>
                        <span class="ty-btn ty-btn-blue " @click="uploadFile1_manage">上传</span>
                      </template>
                    </uploadFile>
                  </div>
                  <span>(共可上传9张)</span>
                </td>
              </tr>
              <tr v-if="manageBaseData.quanImg.length > 0">
                <td></td>
                <td colspan="5">
                  <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in manageBaseData.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg', 'manage')"><i class="fa fa-close"></i></span>
                      </span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">产品图片：</td>
                <td colspan="5">
                  <div class="pa20" >
                    <uploadFile ref="uploadFile3" class="ty-right"
                                module="客户管理"
                                committed.async="false"
                                :showSelect="false"
                                :showFileList="false"
                                :autoUpload="true"
                                ext='.png,.jpg'
                                extMsg='请上传照片！'
                                :multiple="true"
                                :beforeUploadFun="beforeUpload2_manage"
                                :successFun="fileSuccess2_manage">
                      <template #btnArea>
                        <span class="ty-btn ty-btn-blue " @click="uploadFile2_manage">上传</span>
                      </template>
                    </uploadFile>
                  </div>
                  <span>(共可上传9张)</span>
                </td>
              </tr>
              <tr v-if="manageBaseData.chanImg.length > 0">
                <td></td>
                <td colspan="5">
                  <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in manageBaseData.chanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'chanImg', 'manage')"><i class="fa fa-close"></i></span>
                      </span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">首次接触时间：</td>
                <td>
                  <el-date-picker v-model="manageBaseData.firstContactTime" type="date" placeholder="选择日期">
                  </el-date-picker>
                </td>
                <td class="txtRight" width="120px">首次接触地点：</td>
                <td><el-input v-model="manageBaseData.firstContactAddress" format="YYYY/MM/DD" value-format="YYYY/MM/DD" placeholder="请输入"></el-input></td>
                <td class="txtRight" width="120px">信息获取渠道：</td>
                <td><el-input v-model="manageBaseData.infoSource" placeholder="请输入"></el-input></td>
              </tr>
              <tr>
                <td class="txtRight">备注：</td>
                <td colspan="5"><el-input v-model="manageBaseData.memo" placeholder=""></el-input></td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 开票信息 修改-->
      <TyDialog class="manageInvoice" v-if="manageInvoiceData.visible" width="1030" dialogTitle="开票信息修改" color="blue" :dialogHide="hideManageInvoiceFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideManageInvoiceFun">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="manageInvoiceOk">提交</span>
        </template>
        <template #dialogBody>
          <div class="custormerAddCC">
            <table>
              <tbody>
              <tr>
                <td class="ttl"></td>
                <td colspan="5" class="txtRight">
                  <span>客户对发票方面的要求</span>
                  <span class="marL30">{{ (manageInvoiceData.invoiceType && manageInvoiceData.invoiceType.txt) || '尚未设置'}}</span>
                  <span class="ty-linkBtn marL30" @click="goSet('manage')">去设置</span>
                </td>
              </tr>
              <tr>
                <td width="100px" class="txtRight">公司名称：</td>
                <td>
                  <el-input v-model="manageInvoiceData.invoiceName" placeholder="请输入"></el-input>
                </td>
                <td width="100px" class="txtRight">地址：</td>
                <td><el-input v-model="manageInvoiceData.invoiceAddress" placeholder="请输入"></el-input></td>
                <td width="100px" class="txtRight">电话：</td>
                <td><el-input v-model="manageInvoiceData.telephone" placeholder="请输入"></el-input></td>
              </tr>
              <tr>
                <td class="txtRight">开户行：</td>
                <td>
                  <el-input v-model="manageInvoiceData.bankName" placeholder="请输入"></el-input>
                </td>
                <td class="txtRight">账号：</td>
                <td><el-input v-model="manageInvoiceData.bankNo" placeholder="请输入"></el-input></td>
                <td class="txtRight">税号：</td>
                <td><el-input v-model="manageInvoiceData.taxpayerID" placeholder="请输入"></el-input></td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 暂停合作/恢复合作的操作记录-->
      <TyDialog class="suspendRecord" v-if="suspendRecordData.visible" width="600" dialogTitle="暂停合作/恢复合作的操作记录" color="blue" :dialogHide="hideSuspendRecord">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSuspendRecord">关闭</span>
        </template>
        <template #dialogBody>
          <div class="">
            <el-table :data="suspendRecordData.list" border style="width: 100%">
              <el-table-column prop="handleTxt" label="操作性质"> </el-table-column>
              <el-table-column prop="handler" label="操作者"> </el-table-column>
            </el-table>

          </div>
        </template>
      </TyDialog>

      <!-- 修改记录 -->
      <TyDialog class="updateRecords" v-if="updateRecordsData.visible" width="600" :dialogTitle="updateRecordsData.title" color="blue" :dialogHide="hideUpdateRecords">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideUpdateRecords">关闭</span>
        </template>
        <template #dialogBody>
          <div class="recInfo">
            <span>{{ updateRecordsData.recordTip }}</span>
            <span class="ty-right" v-html="updateRecordsData.recordEditer"></span>
          </div>
          <div v-if="updateRecordsData.list.length > 0">
            <el-table :data="updateRecordsData.list"  border style="width: 100%">
              <el-table-column prop="infoTxt" label="记录" width="160"></el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <span class="ty-td-control">
                    <span class="ty-color-blue" @click="cus_recordDetail(scope.row, scope.$index)">查看</span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="handler" label="创建者/修改者"> </el-table-column>
            </el-table>

          </div>
        </template>
      </TyDialog>

      <!-- 基本信息 修改记录详情 -->
      <TyDialog class="manageBase" v-if="manageBaseLogData.visible" width="1060" :dialogTitle="manageBaseLogData.title" color="blue" :dialogHide="hideManageBaseLogInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideManageBaseLogInfo">关闭</span>
        </template>
        <template #dialogBody>
          <div class="custormerAddCC">
            <table>
              <tbody>
              <tr>
                <td class="txtRight" width="120px;">客户名称：</td>
                <td colspan="3" v-html="manageBaseLogData.info.fullName " width="400px"></td>
                <td class="txtRight">客户代号：</td>
                <td v-html="manageBaseLogData.info.code"></td>
              </tr>
              <tr>
                <td class="txtRight">地址：</td>
                <td colspan="3" v-html="manageBaseLogData.info.address"></td>
                <td class="txtRight">客户简称：</td>
                <td v-html="manageBaseLogData.info.name"></td>
              </tr>
              <tr v-if="specialOrgPart">
                <td class="txtRight" width="120px;">最高负责人：</td>
                <td colspan="2" v-html="manageBaseLogData.info.supervisorName "> </td>
                <td colspan="3" style="padding-left: 80px; position: relative; ">
                  <span style="position: absolute; left:30px;">手机：</span>
                  <span v-html=" manageBaseLogData.info.supervisorMobile"></span>
                </td>
              </tr>
              <tr>
                <td colspan="6" v-html="manageBaseLogData.hasBuyStr + manageBaseLogData.hasContractStr"></td>
              </tr>
              <tr>
                <td class="txtRight">全景照片：</td>
                <td colspan="5">
                </td>
              </tr>
              <tr v-if="manageBaseLogData.quanImg.length > 0">
                <td></td>
                <td colspan="5">
                  <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in manageBaseLogData.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg', 'manage')"><i class="fa fa-close"></i></span>
                      </span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">产品图片：</td>
                <td colspan="5">
                </td>
              </tr>
              <tr v-if="manageBaseLogData.chanImg.length > 0">
                <td></td>
                <td colspan="5">
                  <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in manageBaseLogData.chanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'chanImg', 'manage')"><i class="fa fa-close"></i></span>
                      </span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">首次接触时间：</td>
                <td v-html="manageBaseLogData.info.firstContactTime"></td>
                <td class="txtRight" width="120px">首次接触地点：</td>
                <td v-html="manageBaseLogData.info.firstContactAddress"></td>
                <td class="txtRight" width="120px">信息获取渠道：</td>
                <td v-html="manageBaseLogData.info.infoSource"></td>
              </tr>
              <tr>
                <td class="txtRight">备注：</td>
                <td colspan="5" v-html="manageBaseLogData.info.memo"></td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 开票信息 修改记录详情-->
      <TyDialog class="manageInvoice" v-if="manageInvoiceLogData.visible" width="1030" :dialogTitle="manageInvoiceLogData.title" color="blue" :dialogHide="hideManageInvoiceLogFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideManageInvoiceLogFun">取消</span>
        </template>
        <template #dialogBody>
          <div class="custormerAddCC">
            <table>
              <tbody>
              <tr>
                <td width="100px" class="txtRight">公司名称：</td>
                <td width="180px" v-html="manageInvoiceLogData.info.invoiceName"></td>
                <td width="100px" class="txtRight">地址：</td>
                <td width="180px" v-html="manageInvoiceLogData.info.invoiceAddress"></td>
                <td width="100px" class="txtRight">电话：</td>
                <td width="180px" v-html="manageInvoiceLogData.info.telephone"></td>
              </tr>
              <tr>
                <td class="txtRight">开户行：</td>
                <td v-html="manageInvoiceLogData.info.bankName">
                </td>
                <td class="txtRight">账号：</td>
                <td v-html="manageInvoiceLogData.info.bankNo"></td>
                <td class="txtRight">税号：</td>
                <td v-html="manageInvoiceLogData.info.taxpayerID"></td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>



      <!-- 新增客户-->
      <TyDialog class="custormerAdd" v-show="addData.visible" width="1060" dialogTitle="新增客户" color="blue" :dialogHide="hideAddFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideAddFun">取消</span>
          <span v-if="!specialOrgPart" class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addDataOk">提交</span>
          <span v-if="specialOrgPart" class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addDataOkSpecial(0)">录入完毕，暂不选择产品</span>
          <span v-if="specialOrgPart" class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addDataOkSpecial(1)">录入完毕，选择产品</span>
        </template>
        <template #dialogBody>
          <div class="custormerAddCC" style="max-height: 200px;">
            <table>
              <tbody>
                <tr>
                  <td colspan="6" class="ttl">基本信息</td>
                </tr>
                <tr>
                  <td class="txtRight" width="120px;"><i class="ty-color-red">*</i>客户名称：</td>
                  <td colspan="3"><el-input v-model="addData.base.fullName" placeholder="请输入" @change="setName2"></el-input></td>
                  <td class="txtRight">客户代号：</td>
                  <td><el-input v-model="addData.base.cuscoding" placeholder="请输入"></el-input></td>
                </tr>
                <tr>
                  <td class="txtRight">地址：</td>
                  <td colspan="3"><el-input v-model="addData.base.address1" placeholder="请输入"></el-input></td>
                  <td class="txtRight">客户简称：</td>
                  <td><el-input v-model="addData.base.name" placeholder="请输入"></el-input></td>
                </tr>
                <tr v-if="specialOrgPart">
                  <td class="txtRight" width="120px;"><i class="ty-color-red">*</i>最高负责人：</td>
                  <td colspan="2"><el-input v-model="addData.base.supervisorName" placeholder="请输入"></el-input></td>
                  <td colspan="3" style="padding-left: 80px; position: relative; ">
                    <span style="position: absolute; left:30px;"><i class="ty-color-red">*</i>手机：</span>
                    <el-input v-model="addData.base.supervisorMobile" placeholder="请输入"></el-input>
                  </td>
                </tr>
                <tr>
                  <td colspan="6">
                    <span class="marL30"><i class="ty-color-red">*</i>成为公司客户的时间？</span>
                    <span class="marL30">
                      <span class="radiop" @click="toggefirstTime(1)"><i class="fa" :class="this.addData.base.firstTime === 1? 'fa-dot-circle-o':'fa-circle-o'"></i>今年</span>
                      <span v-show="addData.base.firstTime === 1">
                         <el-date-picker v-model="addData.base.initialPeriod" type="month" placeholder="选择月" @change="setFirstTimeN"
                             style="width: 100px" format="M月" value-format="YYYYMM" :disabled-date="disabledDate"></el-date-picker>
                          <span class="radiop" @click="toggefirstTimeN"><i class="fa" :class="this.addData.base.firstTimeN? 'fa-dot-circle-o':'fa-circle-o'"></i>不确定月份</span>
                      </span>
                      <span class="radiop" @click="toggefirstTime(2)"><i class="fa" :class="this.addData.base.firstTime === 2? 'fa-dot-circle-o':'fa-circle-o'"></i>去年</span>
                      <span class="radiop" @click="toggefirstTime(3)"><i class="fa" :class="this.addData.base.firstTime === 3? 'fa-dot-circle-o':'fa-circle-o'"></i>更久之前</span>
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="txtRight">全景照片：</td>
                  <td colspan="5">
                    <span></span>
                    <div class="pa20" >
                      <uploadFile ref="uploadFile2" class="ty-right"
                                  module="客户管理"
                                  committed.async="false"
                                  :showSelect="false"
                                  :showFileList="false"
                                  :autoUpload="true"
                                  ext='.png,.jpg,.jpeg,'
                                  extMsg='请上传照片！'
                                  :multiple="true"
                                  :beforeUploadFun="beforeUpload1"
                                  :successFun="fileSuccess1">
                        <template #btnArea>
                          <span class="ty-btn ty-btn-blue " @click="uploadFile1">上传</span>
                        </template>
                      </uploadFile>
                    </div>
                    <span>(共可上传9张)</span>
                  </td>
                </tr>
                <tr v-if="addData.base.quanImg.length > 0">
                  <td></td>
                  <td colspan="5">
                    <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in addData.base.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg')"><i class="fa fa-close"></i></span>
                      </span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="txtRight">产品图片：</td>
                  <td colspan="5">
                    <div class="pa20" >
                      <uploadFile ref="uploadFile3" class="ty-right"
                                  module="客户管理"
                                  committed.async="false"
                                  :showSelect="false"
                                  :showFileList="false"
                                  :autoUpload="true"
                                  ext='.png,.jpg'
                                  extMsg='请上传照片！'
                                  :multiple="true"
                                  :beforeUploadFun="beforeUpload2"
                                  :successFun="fileSuccess2">
                        <template #btnArea>
                          <span class="ty-btn ty-btn-blue " @click="uploadFile2">上传</span>
                        </template>
                      </uploadFile>
                    </div>
                    <span>(共可上传9张)</span>
                  </td>
                </tr>
                <tr v-if="addData.base.chanImg.length > 0">
                  <td></td>
                  <td colspan="5">
                    <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in addData.base.chanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'chanImg')"><i class="fa fa-close"></i></span>
                      </span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="txtRight">首次接触时间：</td>
                  <td>

                    <el-date-picker v-model="addData.base.firstContactTime" type="date" placeholder="选择日期">
                    </el-date-picker>
                  </td>
                  <td class="txtRight" width="120px">首次接触地点：</td>
                  <td><el-input v-model="addData.base.firstContactAddress" format="YYYY/MM/DD" value-format="YYYY/MM/DD" placeholder="请输入"></el-input></td>
                  <td class="txtRight" width="120px">信息获取渠道：</td>
                  <td><el-input v-model="addData.base.infoSource" placeholder="请输入"></el-input></td>
                </tr>
                <tr>
                  <td class="txtRight">备注：</td>
                  <td colspan="5"><el-input v-model="addData.base.memo" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ttl">开票信息</td>
                  <td colspan="5" class="txtRight">
                    <span>客户对发票方面的要求</span>
                    <span class="marL30">{{ (addData.invoice.invoiceType && addData.invoice.invoiceType.txt) || '尚未设置'}}</span>
                    <span class="ty-linkBtn marL30" @click="goSet">去设置</span>
                  </td>
                </tr>
                <tr>
                  <td class="txtRight">公司名称：</td>
                  <td>
                    <el-input v-model="addData.invoice.invoiceName" placeholder="请输入"></el-input>
                  </td>
                  <td class="txtRight">地址：</td>
                  <td><el-input v-model="addData.invoice.invoiceAddress" placeholder="请输入"></el-input></td>
                  <td class="txtRight">电话：</td>
                  <td><el-input v-model="addData.invoice.telephone" placeholder="请输入"></el-input></td>
                </tr>
                <tr>
                  <td class="txtRight">开户行：</td>
                  <td>
                    <el-input v-model="addData.invoice.bankName" placeholder="请输入"></el-input>
                  </td>
                  <td class="txtRight">账号：</td>
                  <td><el-input v-model="addData.invoice.bankNo" placeholder="请输入"></el-input></td>
                  <td class="txtRight">税号：</td>
                  <td><el-input v-model="addData.invoice.taxpayerID" placeholder="请输入"></el-input></td>
                </tr>
                <tr>
                  <td colspan="6">
                    <span class="ttl"><i class="ty-color-red">*</i>货物的交付地点与方式</span>
                    <span class="ty-btn ty-btn-blue marL30" @click="huoEdit">编辑</span>
                  </td>
                </tr>
                <tr>
                  <td colspan="6" style="padding-left: 40px;">
                    {{ addData.huo.txt }}
                  </td>
                </tr>
                <tr>
                  <td class=" ttl">合同信息</td>
                  <td colspan="5">
                    <span class="ty-btn ty-btn-blue marL50" @click="addContract"> 新增</span>
                    <span class="ty-color-blue marL30">注：点击“新增”，可录入该客户的合同信息。如与该客户有多个合同，需多次“新增”。</span>
                  </td>
                </tr>
                <tr v-if="addData.contract.tableData.length >0">
                  <td colspan="6">
                    <el-table :data="addData.contract.tableData" stripe border>
                      <el-table-column prop="no" label="合同编号" width="200"> </el-table-column>
                      <el-table-column prop="date" label="签署日期" width="160"> </el-table-column>
                      <el-table-column prop="expDur" label="合同的有效期" width="240"> </el-table-column>
                      <el-table-column label="操作">
                        <template #default="scope">
                          <el-button  @click="updateContract(scope.row,scope.$index)" type="text" size="small">修改</el-button>
                          <el-button @click="delContract(scope.row,scope.$index)" type="text" size="small">删除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </td>
                </tr>
                <tr>
                  <td class=" ttl">发票邮寄信息</td>
                  <td colspan="5">
                    <span class="ty-btn ty-btn-blue marL50" @click="addMail">新增</span>
                  </td>
                </tr>
                <tr v-if="addData.mail.tableData.length > 0">
                  <td colspan="6">
                    <el-table :data="addData.mail.tableData" stripe border style="width: 100%">
                      <el-table-column type="index" label="序号" width="80"> </el-table-column>
                      <el-table-column prop="address" label="邮寄地址" width="180"> </el-table-column>
                      <el-table-column prop="recivePerson" label="发票接收人" width="180"> </el-table-column>
                      <el-table-column prop="mailNo" label="邮政编码" width="180"> </el-table-column>
                      <el-table-column prop="tel" label="联系电话" width="180"> </el-table-column>
                      <el-table-column label="操作" width="">
                        <template #default="scope">
                          <el-button @click="updateMail(scope.row, scope.$index)" type="text" size="small">修改</el-button>
                          <el-button @click="delMail(scope.row, scope.$index)" type="text" size="small">删除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </td>
                </tr>
                <tr>
                  <td class=" ttl">联系人</td>
                  <td colspan="5">
                    <span class="ty-btn ty-btn-blue marL50" @click="addContact('contact')">新增</span>
                  </td>
                </tr>
                <tr v-if="addData.contactLianxi.tableData.length > 0">
                  <td colspan="6">
                    <el-table v-if="addData.contactLianxi.tableData.length === 1" :data="contactLianxiTwo" stripe border style="width: 50%">
                      <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                      <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                      <el-table-column label="操作">
                        <template #default="scope">
                          <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                          <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>

                    <el-table v-else :data="contactLianxiTwo" stripe border style="width: 100%">
                      <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                      <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                      <el-table-column label="操作">
                        <template #default="scope">
                          <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                          <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                        </template>
                      </el-table-column>
                      <el-table-column prop="name2" label="姓名" width="80"> </el-table-column>
                      <el-table-column prop="post2" label="职位" width="100"> </el-table-column>
                      <el-table-column label="操作" >
                        <template #default="scope">
                          <span v-if="scope.row.name2">
                            <el-button @click="updateContact(scope.row, 2)" type="text" size="small">修改</el-button>
                            <el-button @click="delContact(scope.row, 2)" type="text" size="small">删除</el-button>
                          </span>
                        </template>
                      </el-table-column>
                    </el-table>

                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 特殊机构 新增客户提示 -->
      <TyDialog v-if="addTipData.visible" width="460" dialogTitle="！！提示" color="red" :dialogHide="hideAddTip">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideAddTip">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="addTipOK">确定</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; line-height: 100px;">
            确定暂不勾选模块吗？
          </div>
        </template>
      </TyDialog>


      <!-- 特殊机构 新增客户提示2 -->
      <TyDialog v-if="addTipData.visible2" width="460" dialogTitle="！！提示" color="red" :dialogHide="hideAddTip2">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideAddTip2">确定</span>
        </template>
        <template #dialogBody>
          <div style="text-align: center; padding: 40px; font-size:14px;line-height: 30px;  ">
            系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！
          </div>
        </template>
      </TyDialog>

      <!-- 新增发票邮寄地址 -->
      <TyDialog v-if="mailInfoData.visible" width="460" :dialogTitle="mailInfoData.title" color="green" :dialogHide="hideMail">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideMail">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5" :class="mailInfoOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="mailInfoOk">提交</span>
        </template>
        <template #dialogBody>
          <div class="newMailInfo">
            <table>
              <tbody>
              <tr>
                <td class="txtRight">邮寄地址：</td>
                <td><el-input v-model="mailInfoData.address" placeholder="请输入内容" clearable></el-input></td>
                <td></td>
              </tr>
              <tr>
                <td class="txtRight">邮寄编码：</td>
                <td><el-input v-model="mailInfoData.code" @change="chargeCode" placeholder="请输入内容" clearable></el-input>
                </td>
                <td></td>
              </tr>
              <tr v-if="!mailInfoData.mailValidSta" >
                <td></td>
                <td class="ty-color-red" style="font-size: 14px; line-height: 20px;">请输入正确的邮寄编码！</td>
                <td></td>
              </tr>
              <tr>
                <td class="txtRight">发票接收人：</td>
                <td>
                  <div class="mask" @click="showContact('mailInfo')"></div>
                  <el-input v-model="mailInfoData.contact.name" placeholder="请选择">
                    <template #append>
                      <i class="fa fa-angle-down"></i>
                    </template>
                  </el-input></td>
                <td>
                  <span class="ty-linkBtn" @click="addContact('mailInfo')">新增</span>
                </td>
              </tr>
              </tbody>
            </table>

          </div>
        </template>
      </TyDialog>

      <!-- 编辑收货信息-->
      <TyDialog class="receiveInfo" v-show="receiveData.visible" width="800" dialogTitle="编辑收货信息" color="blue" :dialogHide="hideReceiveInfoFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideReceiveInfoFun">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="receiveInfoOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="bcc">
            <span @click="toggleReceiveDataType(1)"> <i class="fa" :class="receiveData.type === 1? 'fa-dot-circle-o':'fa-circle-o'"></i> 向该客户只提供服务，不提供实体货物，不需编辑收货地址 </span>
            <br>
            <span @click="toggleReceiveDataType(2)"> <i class="fa" :class="receiveData.type === 2? 'fa-dot-circle-o':'fa-circle-o'"></i> 向该客户提供实体货物 </span>
            <br>
          </div>
          <div v-if="receiveData.type == 2" class="nextcc">
            <div>
              <span>如向该客户提供送货上门服务，请点击“新增收货地址”，以录入可能的收货地址</span>
              <span class="ty-linkBtn ty-right" @click="addReceive">新增收货地址</span>
              <br>
              <el-table v-if="receiveData.addressList.length >0" :data="receiveData.addressList" border stripe style="width: 100%">
                <el-table-column prop="addr" label="收货地址" width="180"> </el-table-column>
                <el-table-column prop="name" label="收货人" width="180"> </el-table-column>
                <el-table-column prop="tel" label="收货电话"> </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="scope">
                    <span class="ty-table-control">
                      <span class="ty-color-blue" @click="addressItemEdit(scope.row,scope.$index)">修改</span>
                      <span class="ty-color-red" @click="addressItemDel(scope.row,scope.$index)">删除</span>
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div>
              <span>如向该客户提供配送至某城市的服务，请点击“新增到货区域”，以录入可能的到货区域</span>
              <span class="ty-linkBtn ty-right" @click="addArea('add')">新增到货区域</span>
              <br>
              <el-table v-if="receiveData.areaList.length > 0" :data="receiveData.areaList" border stripe style="width: 100%">
                <el-table-column prop="addr" label="到货区域" width="280"> </el-table-column>
                <el-table-column prop="name" label="收货人" width="100"> </el-table-column>
                <el-table-column prop="tel" label="收货电话"> </el-table-column>
                <el-table-column label="操作" width="160">
                  <template #default="scope">
                    <div class="ty-table-control">
                      <span class="ty-btn ty-btn-blue ty-circle-5" @click="areaItemEdit(scope.row, scope.$index)">修改</span>
                      <span class="ty-btn ty-btn-red ty-circle-5" @click="areaItemDel(scope.row,scope.$index)">删除</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div>
              <span>本选项为“上门自提”选项。当前状态为“{{ receiveData.status === 0 ? '未开启' : '开启'}}”。如需要可“改变状态”。</span>
              <span class="ty-linkBtn ty-right" @click="toggleSta">改变状态</span>
              <br>
              <span class="ty-color-blue">
                注：状态为“未开启”的，录入该客户商品或订单的到货地点时，“上门自提”选项不展示，“开启”则反之
              </span>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 新增/修改 收货地址-->
      <TyDialog class="addressEdit" v-show="addressEditData.visible" width="460" :dialogTitle="addressEditData.title" color="blue" :dialogHide="hideaddressEditFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideaddressEditFun">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="addressEditOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="addressEditcc">
            <table>
              <tr>
                <td class="txtRight"><i class="ty-color-red">*</i>收货地址</td>
                <td><el-input v-model="addressEditData.address" placeholder="请输入内容"></el-input></td>
                <td></td>
              </tr>
              <tr>
                <td class="txtRight"><i class="ty-color-red">*</i>联系人</td>
                <td>
                  <div class="mask" @click="showContact('addressEdit')"></div>
                  <el-input v-model="addressEditData.contact.name" placeholder="">
                  <template #append>
                    <i class="fa fa-angle-down"></i>
                  </template>
                </el-input></td>
                <td>
                  <span class="ty-linkBtn" @click="addContact('addressEdit')">新增</span>
                </td>
              </tr>
            </table>
          </div>
        </template>
      </TyDialog>
      
      <!-- 新增到货区域-->
      <TyDialog class="receiveArea" v-show="receiveAreaInfoData.visible" width="560" :dialogTitle="receiveAreaInfoData.title" color="blue" :dialogHide="hideReceiveAreaInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideReceiveAreaInfo">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="receiveAreaOK">确定</span>
        </template>
        <template #dialogBody>
          <div class="addressEditcc">
            <table>
              <tbody>
              <tr>
                <td class="txtRight"><i class="ty-color-red">*</i>货物需到达的城市或地区</td>
                <td>
                  <el-cascader style="width:230px "
                               v-model="receiveAreaInfoData.areaArr"
                               :options="receiveAreaInfoData.options"
                               separator="" @change="handleChange"
                  ></el-cascader>

                </td>
                <td></td>
              </tr>
              <tr>
                <td class="txtRight">对需到达地点的特殊要求</td>
                <td><el-input v-model="receiveAreaInfoData.requirements" placeholder="请输入内容" clearable></el-input></td>
                <td></td>
              </tr>
              <tr>
                <td class="txtRight"><i class="ty-color-red">*</i>联系人</td>
                <td>
                  <div class="mask" @click="showContact('receiveArea')"></div>
                  <el-input v-model="receiveAreaInfoData.contact.name" placeholder="">
                    <template #append>
                      <i class="fa fa-angle-down"></i>
                    </template>
                  </el-input></td>
                <td>
                  <span class="ty-linkBtn" @click="addContact('receiveArea')">新增</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 客户对发票方面要求的设置-->
      <TyDialog class="invoiceSet" v-if="invoiceSetData.visible" width="400" dialogTitle="客户对发票方面要求的设置" color="blue" :dialogHide="hideinvoiceSet">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideinvoiceSet">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="invoiceSetOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="chooseCusContactcc">
            <div>请选择此客户对发票方面的要求</div>
            <div class="line30">
              <table>
                <tr v-for="(invoiceItem, indexIv) in invoiceSetData.options" :key="indexIv">
                  <td @click="clickInvoice(invoiceItem)" >
                    <span class="clickArea">
                      <i class="fa" :class="invoiceSetData.selectType && invoiceSetData.selectType.type === invoiceItem.type ? 'fa-dot-circle-o' : 'fa-circle-o' "></i> {{ invoiceItem.txt }}
                    </span>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </template>
      </TyDialog>
      
      <!-- 选择客户联系人-->
      <TyDialog class="chooseCusContact" v-if="chooseCusContactData.visible" width="460" dialogTitle="选择客户联系人" color="blue" :dialogHide="hideChooseCusContactFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideChooseCusContactFun">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="chooseCusContactOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="chooseCusContactcc">
            <div>以下为可供选择的客户联系人</div>
            <div class="table">
              <table>
                <tr v-for="(Conac, indexConac) in chooseCusContactData.contactList" :key="indexConac">
                  <td width="80%">
                    <span @click="clickItem(Conac)" class="clickArea">
                      <i class="fa" :class="chooseCusContactData.selectConact && chooseCusContactData.selectConact.id == Conac.id ? 'fa-dot-circle-o' : 'fa-circle-o' "></i>
                      {{ Conac.name }}
                      <span style="margin-left: 16px;">{{ Conac.post }}</span>
                      <span style="margin-left: 16px;">{{ Conac.mobile }}</span>
                    </span>
                  </td>
                  <td class="txtRight"><span class="ty-linkBtn" @click="detailsConac(Conac)">查看</span></td>
                </tr>
              </table>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 新增联系人-->
      <TyDialog class="editContactLianXi" v-if="editContactLianXiData.visible" width="750" :dialogTitle="editContactLianXiData.title" color="green" :dialogHide="hideEditContactLianXiFun">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideEditContactLianXiFun">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5" :class="editContactLianXiOkStatus ? 'bounce-ok':'bounce-cancel'" @click="editContactLianXiOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="editContactLianXicc">
            <table>
              <tr>
                <td class="txtRight">联系人标签：</td>
                <td colspan="3">{{ editContactLianXiData.tag }}</td>
              </tr>
              <tr>
                <td class="txtRight">姓名：</td>
                <td><el-input v-model="editContactLianXiData.name" placeholder="请录入"></el-input></td>
                <td class="txtRight">职位：</td>
                <td><el-input v-model="editContactLianXiData.postName" placeholder="请录入"></el-input></td>
              </tr>
              <tr>
                <td class="txtRight">手机：</td>
                <td><el-input v-model="editContactLianXiData.tel" placeholder="请录入"></el-input></td>
                <td><span class="ty-linkBtn" @click="showOptionsFun">添加更多联系方式</span></td>
                <td>
                  <el-select v-if="editContactLianXiData.showOptions" v-model="editContactLianXiData.defineType" placeholder="请选择" @change="setLink">
                    <el-option
                        v-for="(item, indexI) in editContactLianXiData.typeList"
                        :key="item.value"
                        :label="item.label"
                        :value="indexI"
                        :disabled="item.disabled"
                    >
                    </el-option>
                  </el-select>
                </td>
              </tr>
              <tr v-for="(contact, index) in editContactLianXiData.contactList" :key="index">
                <td class="txtRight">{{ contact.name }}：</td>
                <td><el-input v-model="contact.val" @change="setArrVal(contact,1)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 1)">删除</span></td>
                <td class="txtRight"><span v-if="contact.name2">{{ contact.name2 }}：</span></td>
                <td >
                  <div v-if="contact.name2" >
                    <el-input v-model="contact.val2" @change="setArrVal(contact,2)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 2)">删除</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="txtRight">名片：</td>
                <td colspan="3"></td>
              </tr>
              <tr >
                <td class="txtRight"></td>
                <td colspan="3">
                  <div v-if="editContactLianXiData.picInfo.src" class="imgcc">
                    <img :src="editContactLianXiData.picInfo.src" alt="名片">
                    <span class="ty-linkBtn-red" @click="delPic">删除</span>
                  </div>
                  <div v-else
                       v-loading="editContactLianXiData.loading"
                       element-loading-background="rgba(100, 100, 100, 0.1)"
                       element-loading-text="正在上传...."
                       style="width: 200px; min-height: 40px; max-height: 150px;">
                    <uploadFile ref="uploadFile"
                                module="客户管理"
                                committed.async="false"
                                :showSelect="false"
                                :showFileList="false"
                                :autoUpload="true"
                                ext='.png,.jpg,.jpeg'
                                extMsg='请上传名片！'
                                :multiple="false"
                                :beforeUploadFun="beforeUploadLianXi"
                                :successFun="fileSuccessLianXi">
                      <template #btnArea>
                        <span class="ty-linkBtn lix" @click="uploadFileLianXi">{{ editContactLianXiData.loadingBtn }}</span>
                      </template>
                    </uploadFile>
                  </div>

                </td>

              </tr>
            </table>

          </div>
        </template>
      </TyDialog>

      <!-- 自定义标签-->
      <TyDialog class="useDefinedLabel" v-if="useDefinedLabelData.visible" width="450" dialogTitle="自定义标签" color="blue" :dialogHide="hideUseDefinedLabel">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideUseDefinedLabel">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5" :class="useDefinedLabelData.val?'bounce-ok':'ty-btn-gray'" @click="useDefinedLabelOk">使用</span>
        </template>
        <template #dialogBody>
          <div class="useDefinedLabelcc">
            <span>自定义标签</span><br>
            <el-input v-model="useDefinedLabelData.val"></el-input>
          </div>
        </template>
      </TyDialog>

      <!-- 新增、修改、续约 合同-->
      <TyDialog class="contractInfo" v-show="contractInfoData.visible" width="500" :dialogTitle="contractInfoData.title" color="blue" :dialogHide="hideContractInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideContractInfo">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="contractInfoOk">提交</span>
        </template>
        <template #dialogBody>
          <div class="contractInfo" v-if="contractInfoData">
            <table>
              <tbody>
              <tr v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
                <td>客户</td>
              </tr>
              <tr v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
                <td colspan="3">
                  <el-input v-model="contractInfoData.cusName" disabled="true"></el-input>
                </td>
              </tr>
              <tr>
                <td><i class="ty-color-red">* </i>合同编号</td>
                <td width="50px;"></td>
                <td>签署日期</td>
              </tr>
              <tr>
                <td><el-input v-model="contractInfoData.cNo" placeholder="请录入"></el-input></td>
                <td></td>
                <td><el-date-picker v-model="contractInfoData.cSignDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker></td>
              </tr>
              <tr class="pt">
                <td colspan="3"><i class="ty-color-red">* </i>合同的有效期</td>
              </tr>
              <tr>
                <td><el-date-picker v-model="contractInfoData.cStartDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker></td>
                <td></td>
                <td><el-date-picker v-model="contractInfoData.cEndDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker></td>
              </tr>
              <tr class="pt">
                <td colspan="2">合同的扫描件或照片(共可上传9张)</td>
                <td class="txtRight">
                  <uploadFile ref="uploadFile"
                              module="客户管理"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg,.pdf'
                              extMsg='请上传扫描件或照片！'
                              :multiple="true"
                              :beforeUploadFun="beforeUploadContract"
                              :successFun="fileSuccessContract">
                    <template #btnArea>
                      <span class="ty-linkBtn">上传</span>
                    </template>
                  </uploadFile>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="box">
                  <span v-for="(pic, index) in contractInfoData.scanPics" :key="index" class="scanPicItem">
                    {{ index+1 }} <i class="fa fa-close" @click="delSP(index)"></i>
                  </span>
                </td>
              </tr>
              <tr class="pt">
                <td colspan="2">合同的可编辑版</td>
                <td class="txtRight">
                  <uploadFile ref="uploadFile"
                              module="客户管理"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.doc,.docx,.xls,.xlsx,.ppt,.txt'
                              extMsg='请上传合同的可编辑版！'
                              :multiple="true"
                              :successFun="fileSuccessContract2">
                    <template #btnArea>
                      <span class="ty-linkBtn">上传</span>
                    </template>
                  </uploadFile>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="box">
                   <span class="scanPicItem" v-if="contractInfoData.scanFile && contractInfoData.scanFile.name">
                    <i class="fa fa-file-word-o"></i> {{ contractInfoData.scanFile.name }} <i class="fa fa-close" @click="delSF"></i>
                  </span>
                </td>
              </tr>
              <tr class="pt" v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
                <td colspan="2">本合同下的专属商品 <span class="ty-color-blue">{{ (contractInfoData.selectZSGs && contractInfoData.selectZSGs.length) || 0 }}</span> 种</td>
                <td class="txtRight">
                  <span class="ty-linkBtn" @click="removeTyGs('zs')">移出商品</span>
                  <span class="ty-linkBtn" @click="addTyGs('zs')">添加商品</span>
                </td>
              </tr>
              <tr v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
                <td colspan="3" class="box">
                  <span class="scanPicItem" v-for="(gs,index) in contractInfoData.selectZSGs" :key="index">{{ gs.outerName }}</span>
                </td>
              </tr>
              <tr class="pt">
                <td colspan="2">本合同下的通用型商品 <span class="ty-color-blue">{{ (contractInfoData.selectGs && contractInfoData.selectGs.length ) || 0}}</span> 种</td>
                <td class="txtRight">
                  <span class="ty-linkBtn" @click="removeTyGs">移出商品</span>
                  <span class="ty-linkBtn" @click="addTyGs">添加商品</span>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="box">
                  <span class="scanPicItem" v-for="(gs,index) in contractInfoData.selectGs" :key="index">{{ gs.outerName }}</span>
                </td>
              </tr>
              <tr class="pt">
                <td colspan="3">备注</td>
              </tr>
              <tr>
                <td colspan="3"><el-input v-model="contractInfoData.cMemo" placeholder="请录入"></el-input></td>
              </tr>
              <tr>

              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 向本合同添加商品 从本合同移出商品 本合同下的商品-->
      <TyDialog v-if="contractGoodsData.visible" width="800" :dialogTitle="contractGoodsData.title" color="blue" :dialogHide="hideCG">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideCG">取消</span>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="contractGoodsOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="tipcontractGoods">
             <p class="mar" >系统内共有以下{{ contractGoodsData.gslist && contractGoodsData.gslist.length }}种通用型商品，均可选择
             <span class="ty-right">已选 {{ contractGoodsData.num }} 种</span>
             </p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td></td>
                <td>商品代号</td>
                <td>商品名称</td>
                <td>型号</td>
                <td>规格</td>
                <td>计量单位</td>
                <td>已包含的合同</td>
              </tr>
              <tr v-for="(gs, index) in contractGoodsData.gslist" :key="index">
                <td width="40px" @click="toggleSelect(gs)"><i class="fa" :class="gs.selected ? 'fa-check-square-o':'fa-square-o'"></i></td>
                <td>{{gs.outerSn }}</td>
                <td>{{gs.outerName }}</td>
                <td>{{gs.model }}</td>
                <td>{{gs.specifications }}</td>
                <td>{{gs.unit }}</td>
                <td>
                  <span class="ty-color-blue" @click="gsContracts(gs)">{{ gs.contractNum  }} 个</span>
                </td>
              </tr>
              </tbody>
            </table>

          </div>
        </template>
      </TyDialog>


      <!-- 续约记录  -->
      <TyDialog v-if="contractBaseSignData.visible" width="800" dialogTitle="续约记录" color="blue" :dialogHide="hideCBS">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCBS">关闭</span>
        </template>
        <template #dialogBody>
          <div class="tipcontractGoods">
            <p class="mar">{{ contractHisData.txt  }}</p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>版本</td>
                <td>操作</td>
                <td>创建（时间为各版本合同的续约时间）</td>
              </tr>
              <tr v-for="(his, index) in contractBaseSignData.list" :key="index">
                <td>{{his.txt }}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="contractIScan(his, index)">查看</span> </td>
                <td>{{his.create }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 查看合同详情 -->
      <TyDialog v-if="contractScanData.visible" width="800" dialogTitle="查看合同" color="blue" :dialogHide="hideCScan">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCScan">关闭</span>
        </template>
        <template #dialogBody>
          <div class="contractScan">
            <div style="line-height:40px; ">本版本合同的创建 {{ contractScanData.contractBase.createName }} {{ new Date( contractScanData.contractBase.createDate).format('yyyy-MM-dd hh:mm:ss') }}</div>
            <table class="ty-table ty-table-control ty-table-txtLeft" v-if="contractScanData.contractBase.sn">
              <tbody>
              <tr><td width="400px;">合同编号</td><td>{{ contractScanData.contractBase.sn }}</td></tr>
              <tr><td>签署日期</td><td>{{ new Date(contractScanData.contractBase.signTime).format('yyyy-MM-dd')   }}</td></tr>
              <tr><td>合同的有效期</td><td>{{ new Date(contractScanData.contractBase.validStart).format('yyyy-MM-dd') }} 至 {{ new Date(contractScanData.contractBase.validEnd).format('yyyy-MM-dd') }}</td></tr>
              <tr><td>合同的扫描件或照片</td><td>
                <span class="ty-color-blue imgBtn" v-for="(img, indexImg) in contractScanData.listImage" :key="indexImg">
                  <el-image class="img" :class="img.opacity === 1? 'opacity1' : 'opacity0'"
                            hide-on-click-modal="true" :src="img.src" fit="cover" :preview-src-list="[img.src]"
                            @close="scanImgClose(img)" @click="scanImg(img)">
                  </el-image>
                  {{ indexImg+ 1}}
                </span>
              </td></tr>
              <tr><td>合同的可编辑版</td><td><span class="ty-color-blue" @click="docScan">查看</span></td></tr>
              <tr><td>本合同下专属的商品（共{{ contractScanData.listZS.length  }}种）</td><td><span class="ty-color-blue" @click="gsScan('zs')">查看</span></td></tr>
              <tr><td>本合同下通用型的商品（共{{ contractScanData.listTY.length  }}种）</td><td><span class="ty-color-blue" @click="gsScan('ty')">查看</span></td></tr>
              <tr><td>备注</td><td>{{ contractScanData.contractBase.memo }}</td></tr>
              <tr><td>本版本合同的修改记录</td><td><span class="ty-color-blue" @click="hisScan">查看</span></td></tr>
              <tr><td>本合同的续约记录</td><td><span class="ty-color-blue" @click="xuScan">查看</span></td></tr>
              </tbody>
            </table>
            <div class="txtRight hisList">
              <div v-for="(enIm, index) in contractScanData.listHis" :key="index">
                <span> {{ enIm.suspend ? "暂停履约/终止合作" : "恢复履约/重启合作" }} </span>
                <span> {{ enIm.createName }} </span>
                <span> {{ new Date(enIm.suspendTime).format('yyyy-MM-dd')  }} </span>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>


      <!-- 本合同的修改记录  -->
      <TyDialog v-if="contractHisData.visible" width="500" dialogTitle="修改记录" color="blue" :dialogHide="hideCHS">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCHS">关闭</span>
        </template>
        <template #dialogBody>
          <div class="tipcontractGoods">
            <p class="mar">{{ contractHisData.txt  }}</p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>记录</td>
                <td>操作</td>
                <td>创建者/修改者</td>
              </tr>
              <tr v-for="(his, index) in contractHisData.hisList" :key="index">
                <td>{{his.txt }}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="hisDetail(his, 'contractHisData')">查看</span> </td>
                <td>{{his.create }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 修改记录 查看合同详情 -->
      <TyDialog v-if="contractScanData.visible2" width="800" dialogTitle="查看合同" color="blue" :dialogHide="hideCScan2">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCScan2">关闭</span>
        </template>
        <template #dialogBody>
          <div class="contractScan">
            <table class="ty-table ty-table-control ty-table-txtLeft" v-if="contractScanData.contractBase2 && contractScanData.contractBase2.sn">
              <tbody>
              <tr><td width="400px;">合同编号</td><td>{{ contractScanData.contractBase2.sn }}</td></tr>
              <tr><td>签署日期</td><td>{{ new Date(contractScanData.contractBase2.signTime).format('yyyy-MM-dd')   }}</td></tr>
              <tr><td>合同的有效期</td><td>{{ new Date(contractScanData.contractBase2.validStart).format('yyyy-MM-dd') }} 至 {{ new Date(contractScanData.contractBase.validEnd).format('yyyy-MM-dd') }}</td></tr>
              <tr><td>合同的扫描件或照片</td><td>
                <span class="ty-color-blue imgBtn" v-for="(img, indexImg) in contractScanData.listImage2" :key="indexImg">
                  <el-image class="img" :class="img.opacity === 1? 'opacity1' : 'opacity0'"
                            hide-on-click-modal="true" :src="img.src" fit="cover" :preview-src-list="[img.src]"
                            @close="scanImgClose(img)" @click="scanImg(img)">
                  </el-image>
                  {{ indexImg+ 1}}
                </span>
              </td></tr>
              <tr><td>合同的可编辑版</td><td><span class="ty-color-blue" @click="docScan(2)">查看</span></td></tr>
              <tr><td>本合同下专属的商品（共{{ contractScanData.listZS2.length }}种）</td><td><span class="ty-color-blue" @click="gsScan('zs', 2)">查看</span></td></tr>
              <tr><td>本合同下通用型的商品（共{{ contractScanData.listTY2.length }}种）</td><td><span class="ty-color-blue" @click="gsScan('ty', 2)">查看</span></td></tr>
              <tr><td>备注</td><td>{{ contractScanData.contractBase2.memo }}</td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 本合同下的 专属/通用 商品  -->
      <TyDialog v-if="contractGsScanData.visible" width="800" :dialogTitle="contractGsScanData.title" color="blue" :dialogHide="hideCSG">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCSG">关闭</span>
        </template>
        <template #dialogBody>
          <div class="tipcontractGoods">
            <p class="mar" v-html="contractGsScanData.txt"></p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>商品代号</td>
                <td>商品名称</td>
                <td>型号</td>
                <td>规格</td>
                <td>计量单位</td>
                <td>已包含的合同</td>
              </tr>
              <tr v-for="(gs, index) in contractGsScanData.gslist" :key="index">
                <td>{{gs.outerSn }}</td>
                <td>{{gs.outerName }}</td>
                <td>{{gs.model }}</td>
                <td>{{gs.specifications }}</td>
                <td>{{gs.unit }}</td>
                <td>
                  <span class="ty-color-blue" @click="gsContracts(gs)">{{ gs.contractNum  }} 个</span>
                </td>
              </tr>
              </tbody>
            </table>

          </div>
        </template>
      </TyDialog>


      <!-- 包含某商品的合同  -->
      <TyDialog v-if="contractByGsData.visible" width="800" dialogTitle="已包含该商品的销售合同" color="blue" :dialogHide="hideCBG">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCBG">关闭</span>
        </template>
        <template #dialogBody>
          <div class="tipcontractGoods">
            <p class="mar">{{ contractByGsData.gsInfo  }}</p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>合同编号</td>
                <td>所涉商品</td>
                <td>有效期</td>
                <td>签署日期</td>
                <td>本版本合同的创建</td>
              </tr>
              <tr v-for="(contract, index) in contractByGsData.cList" :key="index">
                <td>{{contract.sn }}</td>
                <td>{{contract.commodityCount }}</td>
                <td>{{contract.valid }}</td>
                <td>{{contract.sign }}</td>
                <td>{{contract.create }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 模块查看  -->
      <TyDialog v-if="proScanData.visible" width="800" dialogTitle="模块查看" color="blue" :dialogHide="hideProScan">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideProScan">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <div class="bdTtl">{{ proScanData.name }}</div>
            <TyMenuTree :list="proScanData.list"></TyMenuTree>
          </div>
        </template>
      </TyDialog>




    </div>

    <div>

    </div>

  </div>
</template>


<script>
import customerManageJs from '@/mixins/customerManage.js'
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
export default {
  mixins: [customerManageJs],
  data() {
    return {
      pageName:'customerManage',
    }
  },
  created(){
    console.log('开始初始化！！！！！！')
    initNav({mid: 'qb', name: '客户管理', pageName: this.pageName}, this.getList, this)
  },
}

</script>

<style lang="scss" scoped>
.customer{
  min-height: 900px;
  font-family: "Microsoft YaHei"!important;
  padding:20px;

  .sl-dropdown{
    display: inline-block; position: relative;
    .dropdownCon{
      position: absolute;
      background: #fff;
      border: 1px solid #ccc;
      border-bottom: none;
      width: 120px;
      top: 23px;
      opacity: 1;
      left: -33px;
      border-radius: 4px;
      box-shadow: 0 0 5px #ccc;
      z-index: 1;
      padding-top: 5px;
      &:before{
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        transform: rotate(45deg);
        position: absolute;
        top: -6px;
        left: 55px;
        background: #fff;
        border-bottom: none;
        border-right: none;
      }
      .disableItem{
        border-bottom: 1px solid #ccc; color: #666;
        line-height: 30px;
        background-color: #eee;
      }
      .dropdownItem:hover{
        background-color: $tyBounce-color-blue-hover;
        color: #fff;
      }
      .dropdownItem{
        border-bottom: 1px solid #ccc;
        color: $tyBounce-color-blue-hover;
        line-height: 30px;
      }
    }
    .dropdownCon2{
      position: absolute;
      background: #fff;
      border: 1px solid #ccc;
      //border-bottom: none;
      width: 120px;
      height: 120px;
      top: -93px;
      opacity: 1;
      left: -33px;
      border-radius: 4px;
      box-shadow: 0 0 5px #ccc;
      z-index: 1;
      padding-top: 5px;
      &:before{
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        transform: rotate(45deg);
        position: absolute;
        top: 121px;
        left: 54px;
        background: #fff;
        border-top: none;
        border-left: none;
      }
      .disableItem{
        border-bottom: 1px solid #ccc; color: #666;
        line-height: 30px;
        background-color: #eee;
      }
      .dropdownItem:hover{
        background-color: $tyBounce-color-blue-hover;
        color: #fff;
      }
      .dropdownItem{
        border-bottom: 1px solid #ccc;
        color: $tyBounce-color-blue-hover;
        line-height: 30px;
      }
    }
  }

  .rotating {
    animation: rotate-animation 1.5s linear infinite;
  }

  @keyframes rotate-animation {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .txtRight{
    text-align: right;
  }
  .txtLeft{
    text-align: left;
  }
  .btnss{ width:350px; float: right;   }
  .rMMsg{
    padding:20px 50px; line-height: 30px;
    .ttl{ width: 150px; display: inline-block;  }
    .hr{ border-bottom: 1px solid #ccc; margin: 10px 0;  }
  }
  .addService{
    .radioB{
      line-height:40px; cursor: pointer;
      .fa{ margin-right:10px; margin-left:10px; color:$tyBounce-color-green; font-size: 18px;  }
    }
  }
  .packageList{
    .tip{ font-size: 14px; line-height:20px; color: #666; padding: 10px 20px;    }
  }
  .bdTtl{ font-weight:bold; font-size: 14px; color:#666; line-height: 40px;   }
  .packageInfo{
    line-height:30px ;
    .packageName{ font-weight: bold; font-size:24px;  }
    .marT39{ margin-top: 12px; }
    .smallT{ font-size:14px; color: #666;  }
    .ccc{ font-size: 14px; font-weight: bold; margin-right:90px;   }
  }
  .orgEdit{
    line-height: 40px;
    table{ margin-left: 50px; }
    .el-select{ width: 100%; }
    td:nth-child(2){ padding-left: 20px; }
    td:nth-child(1){
      text-align: right; padding-left: 16px;
    }

  }
  .orgList{
    font-size:14px ; width: 100%; margin-top: 20px;
  }
  .contactUpdateLogDetails{
    td{ padding-left: 20px!important; }
  }
  .adressLogDetails{
    padding-left: 50px;
    td{ line-height: 40px; }
    td:nth-child(1){ text-align: right; }
  }

  .contractStop{ line-height: 40px; text-align: center; margin:20px 0;  }
  .contractScan{
    .imgBtn{ overflow: hidden; position: relative; width: 20px; height: 30px; }
    .img{ width:100%; height:100%; left:0; top:0;  opacity:0.5; z-index: 1; position: absolute;  }
    .opacity1{ opacity:1;  }
    .opacity0{ opacity:0; }
    .hisList{ padding: 6px 10px; line-height: 22px; font-size: 14px;  color: #666; }
  }
  .updateCustomerPanel{
    .marLl20{ margin-left:-15px;  }
    .pdL40{ padding-left:40px;  }
    .cusName{ color: $tyBounce-color-green; font-size:16px; padding: 10px 0; font-weight: bold;    }
    .manageTtl td{ background-color:#eee; font-weight: bold; color:#333;    }
  }
  .updateRecords{
    font-size:16px ;
    .recInfo{ line-height: 60px }
  }
  .newMailInfo{
    line-height: 40px;
    td{ padding-left:10px; position: relative; }
    .fa-angle-down{ font-size: 18px; }
    .mask{ position: absolute; width: 100%; height: 100%; z-index: 1 }

  }
  .tipcontractGoods{
    .mar{ margin:10px 0;  }
    .fa{ color:$tyBounce-color-blue; font-weight: bold;   }
  }
  .contractInfo{
    font-size: 15px; line-height: 24px;
    tr.pt td{ padding-top: 10px }
    .box{ border: 1px solid #ccc; border-radius: 2px; height: 30px; background: #fff;   }
    .fa{ color:$tyBounce-color-blue;   }
    .fa-close:hover{ color:$tyBounce-color-red;   }
    .scanPicItem{ padding:0 10px; }

  }
  .cusScan{
    .ttl{
      position: relative; padding-left: 16px;
      &::before{
        border-left:4px solid #bbb; display: inline-block; height: 18px;
        position: absolute;left: 6px;top: 11px; content: "";
      }
    }
  }
  .custormerAddCC{
    line-height:40px; font-size: 14px; padding-left:40px;
    .ttl{
      font-weight: bold; font-size: 13px;
    }
    .marL30{ margin-left:30px;  }
    .marL50{ margin-left:42px;  }
    .radiop{ padding: 0 10px;
      .fa{ color:$tyBounce-color-blue; margin-right: 6px; font-size: 18px; }
    }
    .pa20{ padding:0 20px; display: inline-block; position: relative; top:8px;  }
    .imgList{
      .imgI{
        position: relative; margin-right: 10px;
        .delBtn{ cursor: pointer;
          .fa{ font-size: 20px; opacity:0.2; margin:0 10px; }
          .fa:hover{ opacity:1;  }
        }
        .img, img{width: 90px; height:60px;display: inline-block; background-size: 100%;
          background-position: center center; box-shadow:0 0 3px #ccc ; background-color: #fff;}
      }
    }
  }
  .btnccc{
    text-align: right;
    span{ margin-right: 10px; }
  }
  .line30{
    line-height: 20px; margin-top: 10px; margin-left: 20px; font-size:15px ;
  }
  .custormerLeading{
    font-size: 14px; line-height: 30px;
    .exportStep{ padding-left: 40px; }
    .stepItem { margin-bottom: 20px;  }
    .upload_sect{ width: 352px; }
    #uploadFile{ cursor: default;  }
    .flexRow { margin-left: 50px; display: flex; justify-content: space-between; width: 356px; }
  }
  .receiveInfo{
    .bcc{
      padding-left: 50px; line-height: 40px; cursor: pointer;
      .fa{ color: $tyBounce-color-blue;  }
    }
    .nextcc{
      padding-top: 30px; line-height:26px;
      &>div{ border-top: 1px solid #cdcdcd; padding: 30px 0; font-size: 14px;  }
      .ty-table-control{
        span{ cursor: pointer; padding:3px 6px; margin:2px 5px; border-radius: 3px }
      }
    }
  }
  .addressEditcc{
    line-height: 40px;
    td{ padding-left:10px; position: relative; }
    .fa-angle-down{ font-size: 18px; }
    .mask{ position: absolute; width: 100%; height: 100%; z-index: 1 }
  }
  .chooseCusContactcc{
    line-height:20px;
    .fa{ color: $tyBounce-color-blue; margin-right: 5px;   }
    .table{ background: #fff; padding: 10px; margin:10px;   }
    .clickArea{ cursor: pointer;
      -webkit-user-select: none; /* Safari */
      -moz-user-select: none; /* Firefox */
      -ms-user-select: none; /* IE10+/Edge */
      user-select: none; /* Standard syntax */
    }
    table{ width: 100%; }

  }
  .editContactLianXicc{
    line-height: 30px; font-size: 15px;
    table{ width: 100%;  }
    .imgcc{
      img{ max-width:100px; max-height: 80px; }
    }
    .lix{
      position: relative;
    }
  }
  .useDefinedLabelcc{
    text-align: center; padding: 20px 100px; line-height: 40px;
  }


}

</style>
<style lang="scss">
.el-table .cell{ text-align: center;  }
.editContactLianXicc{
  .el-input{ width: 180px; }
}
.el-input-group__append, .el-input-group__prepend{ cursor: default; background:$ty-color-blue-btn; border-color:$ty-color-blue-btn; color: #fff;   }
</style>
