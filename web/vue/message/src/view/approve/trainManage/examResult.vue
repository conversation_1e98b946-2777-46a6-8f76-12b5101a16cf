<template>
  <div id="examResult" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="试卷" :isReBack="ableWrong" @reback="closeShow" ></TY_NavTop>
    <div class="container">
      <div class="top">
        <div class="personName">
          <span class="ttl">{{ auth.getAcc().name}}</span>
          <span class="gapLt">{{ auth.getAcc().mobile }}</span>
        </div>
        <el-row>
          <el-col :span="4">名次</el-col>
          <el-col :span="6">{{answerInfo.trainingExamUser.ranking ? "第"+ answerInfo.trainingExamUser.ranking+"名":"未知"}}</el-col>
          <el-col :span="5">交卷时间</el-col>
          <el-col :span="9">{{answerInfo.trainingExamUser.stopTime | formatDate('MM-dd hh:mm:ss')}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="4">成绩</el-col>
          <el-col :span="6">{{answerInfo.trainingExamUser.score}}</el-col>
          <el-col :span="5">答题用时</el-col>
          <el-col :span="9">{{parseFloat(((answerInfo.trainingExamUser.stopTime - answerInfo.trainingExamUser.startTime)/1000/60).toFixed(2))}}</el-col>
        </el-row>
        <div class="colorTip" v-if="ableWrong">
          <span class="smCon">错题列表</span>
        </div>
        <div class="colorTip" v-else>
          <span class="smCon">答错了的题使用了蓝底色标识！</span>
          <span style="float: right; font-weight: 600;" class="linkBtn" @click="scanWrongQues">只看错题</span>
        </div>
      </div>
      <div class="resultDetail">
        <div class="title" v-if="chooseList.length > 0"><span><span v-if="tfList.length > 0">一、</span>单选题</span><span style="margin-left:50px;">题目共{{chooseAllFen}}分，实际共得{{choosePersFen}}分</span></div>
        <div>
          <table class="def-table" width="100%" v-if="chooseList.length > 0">
            <thead>
            <tr>
              <td width="15%">题目</td>
              <td width="20%">题目分数</td>
              <td width="20%">标准答案</td>
              <td width="20%">本题得分</td>
              <td width="25%"></td>
            </tr>
            </thead>
            <tbody>
            <tr v-if="!ableWrong || ableWrong && choice.trainingExamUserAnswer.score === 0" v-for="(choice, index) in chooseList" :key="index">
              <td>{{index + 1}}</td>
              <td>{{choice.trainingExamQuestion.score}}</td>
              <td>{{choice.trainingExamUserAnswer.codeOk}}</td>
              <td :class="{'flagScore' : !choice.trainingExamUserAnswer.score || choice.trainingExamUserAnswer.score === 0}">{{choice.trainingExamUserAnswer.score || 0}}</td>
              <td @click="goQues(index, choice)">
                <div class="rowPos">
                  <div class="ui-cell__ft"></div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="title" v-if="tfList.length > 0"><span><span v-if="chooseList.length > 0">二、</span>判断题</span><span style="margin-left:50px;">题目共{{tfAllFen}}分，实际共得{{tfPersFen}}分</span></div>
        <div>
          <table class="def-table" width="100%" v-if="tfList.length > 0">
            <thead>
            <tr>
              <td width="15%">题目</td>
              <td width="20%">题目分数</td>
              <td width="20%">标准答案</td>
              <td width="20%">本题得分</td>
              <td width="25%"></td>
            </tr>
            </thead>
            <tbody>
            <tr v-if="!ableWrong || ableWrong && choice.trainingExamUserAnswer.score === 0" v-for="(choice, index) in tfList" :key="index">
              <td>{{index + 1}}</td>
              <td>{{choice.trainingExamQuestion.score}}</td>
              <td>{{choice.trainingExamUserAnswer.codeOk}}</td>
              <td :class="{'flagScore': !choice.trainingExamUserAnswer.score || choice.trainingExamUserAnswer.score === 0}">{{choice.trainingExamUserAnswer.score || 0}}</td>
              <td @click="goQues(index, choice)">
                <div class="rowPos">
                  <div class="ui-cell__ft"></div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#examResult{
  overflow: auto;color: #333;font-size: 14px;
  .personName{
    font-size: 16px;
    .gapLt{margin-left: 14px;}
  }
  .linkBtn{margin-right: 0;}
  .title{ background:#eee;padding:6px 15px; font-size:12px; }
  .smCon{ font-size:12px; color: #333;}
  .colorTip{margin-top: 40px;}
  .top{ padding:10px 15px; background: #fff;
    .el-row{ padding-top: 12px;}
  }
  .def-table{
    thead {  color: #909399;  font-weight: 500;  }
    tr {  background-color: #fff;  }
    td{  padding: 6px 0;font-size: 12px;  word-wrap: break-word;  word-spacing: normal;  word-break: break-all;  color: #606266; text-align: center; }
    td.flagScore{ background: #5d9cec;color: #fff;}
  }
  .rowPos{
    position: relative;
    .ui-cell__ft::after{ border-color: #606266; }
  }
}
</style>
<script>
import {formatDate} from '../../../js/common'
export default {
  name: 'examResult',
  data () {
    return {
      chooseList: [],
      tfList: [],
      chooseAllFen: 0,
      tfAllFen: 0,
      answerInfo: {
        "trainingExamUser": {}
      },
      leftTimeStr: {},
      loading: false,
      exam: '',
      timeRun: 1,
      choosePersFen: 0,
      tfPersFen: 0,
      ableWrong: false
    }
  },
  filters: {
    formatDate
  },
  created () {
    this.getList()
  },
  destroyed: function () {
    let _this = this
    _this.timeRun = 0
  },
  methods: {
    handIn:function() {
      let that = this
      let url = '../../../examManage/endExam.do'
      let data = { 'exam': this.exam }
      that.loading = true
      that.axios.post(url, data).then((response) => {
        that.loading = false
        let res = response.data
        let status = res.status
        if(status == 1){
          that.setPath()
        }else{
          that.$kiko_message('提交失败，请重试！')
        }
      }).catch(function (res) {
        console.log(res)
      })
    },
    getList: function() {
      this.exam = this.$route.params.id
      let that = this
      let url = '../../../examManage/selectExamDetailPublicityByUser.do'
      let data = { 'exam': this.exam, 'userId': this.sphdSocket.user.userID }
      that.axios.post(url, data).then((response) => {
        that.loading = false
        let data = response.data
        if (data) {
          let questionList = data.respExamQuestionList || []
          that.answerInfo = data.respExamUser || {};
          questionList.forEach(function(item,index) {
            item.index = index
            let Ans = item.trainingExamUserAnswer || {}
            item.trainingExamUserAnswer = Ans
            let type = item.trainingQuestion.type
            let keyList = item.trainingExamKeyList
            let result = {}
            Ans.score = item.trainingExamUserAnswer.score || 0
            if (Number(type) === 1) {
              result = keyList.find(function (value){
                return value.isKey === 1
              })
              Ans.codeOk = result.code
              that.chooseList.push(item)
              that.chooseAllFen += Number(item.trainingExamQuestion.score)
              that.choosePersFen += Number(Ans.score)
            } else {
              if (keyList[0].isKey === '1' || keyList[0].isKey === 1) {
                Ans.codeOk = '√'
              } else if (keyList[0].isKey === '0' || keyList[0].isKey === 0) {
                Ans.codeOk = '×'
              }
              that.tfList.push(item)
              that.tfAllFen += Number(item.trainingExamQuestion.score)
              that.tfPersFen += Number(Ans.score)
            }
          })
        } else {
          that.$kiko_message('加载失败')
        }
      }).catch(function (res) {
        console.log(res)
      })
    },
    goQues: function (index, row) {
      let res = { 'ques': row }
      localStorage.setItem('res', JSON.stringify(res))
      this.$router.push({
        path: `/answerDetail`
      })
    },
    scanWrongQues: function (index, row) {
      this.ableWrong = true
    },
    closeShow: function () {
      this.ableWrong = false
      this.$emit('update:isReBack', false)
    }
  }
}
</script>
