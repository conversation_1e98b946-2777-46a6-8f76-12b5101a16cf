package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.PoContractImage;
import cn.sphd.miners.modules.material.entity.PoContractImageHistory;

import java.io.Serializable;

public interface PoContractImageHistoryDao extends IBaseDao<PoContractImageHistory, Serializable> {

    PoContractImageHistory insert(PoContractImage poContractImage,Integer hisId);
}
