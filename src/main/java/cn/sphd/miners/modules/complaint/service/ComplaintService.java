package cn.sphd.miners.modules.complaint.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.complaint.dto.ComplaintTH;
import cn.sphd.miners.modules.complaint.dto.ProductMessageByComplaint;
import cn.sphd.miners.modules.complaint.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-01-17 9:34
 * @description
 **/
public interface ComplaintService extends BadgeNumberCallback{

    //从商品出库表中获取签收时间
    List<ProductMessageByComplaint> listOutPutTime(Integer product);

    //新增投诉
    void saveComplaint(ComplaintBase complaintBase, ComplaintContact complaintContact, String listComplaintDetail, User user, String module);

    //立案者获取自己的投诉录入
    HashMap ComplaintEntryList(User user, String time, Integer oid);

    //根据权限获取立案待审批、待结案、街案待审批的列表
    List<ComplaintBase> getComplaintByStateAndRole(User user, String state, String role);

    //根据条件查询完结的案子
    HashMap listCompleteBase(User user, String role, String time, String settleType, Integer customerId);

    //公司总览去查询投诉
    HashMap listOverviewCompleteBase(Integer oid, String type, Integer customer, String year, String month, PageInfo pageInfo, String state, String settleType);

    //根据客户和状态查询投诉
    List<ComplaintBase> overviewComplaintByCustomerAndState(User user, String state, Integer customer);

    //查询公司某月所有的投诉
    HashMap<String, Object> getAllComplaintByOid(User user, Integer peroid, PageInfo pageInfo);

    //1.189和1.190的宏观数据
    ComplaintTH macroComplaintByCustomer(User user, String time, Integer customer, String type);

    //核心人物审批立案
    void userCentreApproveComplaint(User user, ComplaintBase complaintBase, Integer processId);

    //处理者或立案者申请结案
    void applyFinishComplaintByRole(User user, ComplaintBase complaintBase, String role, Integer processId);

    //核心人物结案
    void userCentreFinishComplaint(User user, ComplaintBase complaintBase, Integer processId);

    //核心人物更换处理者
    void userCentreChangeHandler(ComplaintBase complaintBase);

    //核心人物停权立案者
    void userCentreStoppedPermission(Integer userID, Integer baseId);

    //查看处理者历史记录
    List<ProductMessageByComplaint> listConplaintBaseHistory(Integer baseId);

    //查看客户联系人
    List<ComplaintContact> listComplaintContact(Integer baseId);

    //新增客户联系人
    ComplaintContact addCustomerContact(ComplaintContact complaintContact);

    //修改客户联系人
    ComplaintContact upContact(User user, ComplaintContact complaintContact);

    //查看客户联系人历史记录
    List<ComplaintContactHistory> listContactHistory(Integer contactId);

    //新增补充材料
    ComplaintSupplement addSupplement(ComplaintSupplement complaintSupplement, User user);

    //投诉详细信息
    HashMap getComplaintBase(Integer baseId);

    //给投诉填入一些名字
    ComplaintBase getSingelBase(ComplaintBase complaintBase);

    //获取投诉处理的角标
    Integer getComplaintNum(Integer userId, Integer oid);

    void sendComplaintMessage(Integer baseId, String type);
    //获取附件实体
    ComplaintAttachment getSingelByComAtt(Integer id);
    //获取投诉详情实体
    ComplaintDetail getSingleByComDet(Integer id);
    //获取投诉实体
    ComplaintBase getSingleByCom(Integer id);
    //获取补充材料实体
    ComplaintSupplement getSingleBySupplement(Integer id);

    Date getFirstComplaintBaseDate(Integer oid);

}
