package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/12/16.
 */
public interface DataService {
    AccountPeriod getDataMonth(Integer accountId);

    List<AccountPeriod> getDataByDay(Integer accountId, String beginDate, String endDate);

    AccountPeriod getDetail(Integer accountPerId);

    List<AccountDetail> getMonthDetailDay(Integer accountId, String beginDate, String endDate);

    List<AccountDetail> getDayDetail(Integer accountId, String beginDate);

    AccountDetail getOneDetail(Integer detailId);

    void addMonth(AccountPeriod accountPeriod);

    void addDay(AccountPeriod accountPeriod1);

    void addInTransfers(AccountDetail accountDetail);

    void saveFinanceReturn(FinanceReturn financeReturn);

    void updateFinanceReturn(FinanceReturn financeReturn);

    void saveFinanceAccountBill(FinanceAccountBill financeAccountBill);

    void updateFinanceAccountBill(FinanceAccountBill financeAccountBill);

    void addAccountDetail(AccountDetail accountDetail);

    AccountPeriod getDataDayByAccountId(Integer accountId);

    void updateAccountPeriod(AccountPeriod accountPeriod);

    List<AccountPeriod> getAccountData(Integer oid);

    List<AccountPeriod> getDataByMonth(Integer accountId, String beginDate, String endDate);

    List<FinanceAccountBill> getDataByOid(Integer oid,String accountantStatus);

    List<AccountDetail> getAccountingByOid(Integer oid);

    FinanceAccountBill getAccountBillByBillId(Integer detailId);

    AccountDetail getByBillId(Integer detailId);

    /**会计数据状态的更新
     kind 1-除收据外的报销两讫后的数据详情和收入中现金/银行转账的数据(detail表) 2-收入所有和支出除收据外的数据详情(bill表)*/
    String updateAccountantStatus(Integer detailId, String kind,String state);

    List<AccountPeriod> getAccountDataByOidDay(Integer oid, Date day);

    List<AccountPeriod> getDataMonthByDate(Integer accountId, String beginDate, String endDate, Integer periodType);

    AccountPeriod getDataByMonth1(Integer accountId, String beginDate);

    /**财务修改*/
    void addAccountDetailHistory(AccountDetailHistory accountDetailHistory);

    void addFinanceAccountBillHistory(FinanceAccountBillHistory financeAccountBillHistory);

    FinanceAccountBill getAccountBillByReturnId(Integer returnId);

    FinanceAccountBill getAccountBillByChequeId(Integer chequeId);

    AccountDetailHistory getAccountDetailHistoryById(Integer id);

    FinanceAccountBillHistory getBillHistoryById(Integer billHistoryId);

    void updateFinanceAccountBillHistory(FinanceAccountBillHistory financeAccountBillHistory);

    AccountDetail getDetailByReimburse(Integer reimburseId);

    List<AccountDetail> getDetailByMonth(Integer oid,Date beginDate,Date endDate,List<Integer> orgIntegerList,PageInfo pageInfo);  //按月流水

    FinanceAccountBillImage addBillImage(User user,Integer accountDetail,Integer accountBill,String type,String uplaodPath,Integer orders);

    FinanceAccountBillImageHistory addBillImageHistory(User user,Integer accountDetail,Integer accountDetailHistory,Integer accountBill,Integer accountBillHistory,String type,String uplaodPath,Integer orders);

    FinanceAccountBillImage getBillImage(Integer id);

    FinanceAccountBillImageHistory getBillImageHistory(Integer id);

    List<FinanceAccountBillImage> getBillImageByIds(Integer org,Integer accountDetail,Integer accountBill);

    List<FinanceAccountBillImageHistory> getBillImageHistoryByIds(Integer org,Integer accountDetailHistory,Integer accountBillHistory);

    //支出汇划费部分
//    Map<String,Object> remittanceFee(User user,String method,Date factDate, String summary, BigDecimal amount, String imgPaths, String memo,FinanceAccount financeAccount,AccountPeriod accountPeriodDay,AccountPeriod accountPeriodMonth);
    //-----以下是1.209会计优化项目的接口-----------------------
    Map<String,Object> getNumTotal(Integer org,Date beginDate,Date endDate);  //获取可做凭证、不予入账、未入会计帐这三类的统计

    Map<String,Object> voucherList(Integer org,Date beginDate,Date endDate);

    Map<String,Object> noAccountingList(Integer org,Date beginDate,Date endDate,Boolean isAccount,Integer type);

    Map<String,Object> notRecordedList(Integer org,Date beginDate,Date endDate,Integer type);

    List<AccountDetail> getNotRecordedDetailList(Integer org,Date beginDate,Date endDate);

    List<FinanceAccountBill> getNotRecordedBillList(Integer org,Date beginDate,Date endDate);

    List<AccountDetail> getNoAccountingDetailList(Integer org,Date beginDate,Date endDate,Boolean isAccount);

    List<FinanceAccountBill> getNoAccountingBillList(Integer org,Date beginDate,Date endDate,Boolean isAccount);

    Integer updateVoucher(Integer datailId,Integer billId,Integer voucher,Boolean isAccount);  //会计数据状态的更新 datail/bill中的数据是否选择了凭证或者不予下账--1.209会计优化

    AccountDetail getAccountDetailByMethod(Integer org,Integer accountId,String method);  //获取银行账户的初始金额的数据

    Map<String,Object> getDetailBillCreateDate(Integer datailId,Integer billId);  //会计使用的，返回创建时间与创建人

    Map<String,Object> debitTaxCategory(User user,String taxCategorys);

    Integer setTaxFactAmount(User user,Integer taxId,Integer report,BigDecimal money,String period,Date factDate,Integer accountDetail,Integer belongsBegin,Integer belongsEnd,String periodBegin,String periodEnd,Integer taxPeriodIdInsert,Integer taxPeriodIdUpdate);//将数据保存进报税事务

    Map<String,Object> getTaxMethod(User user,Integer financeDetailId); //1.207财务优化之报税--获取支出方式和图片(刘洪涛用) financeDetailId-财务明细id
}
