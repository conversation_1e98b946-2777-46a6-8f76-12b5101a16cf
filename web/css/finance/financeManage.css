.bonceCon input.hd{display: none}
.btn{ border:1px solid #00a0e9 ; margin-top:15px;   }
.btn:hover{ background:#eaeaea; color:#444;  }
.init{display: none;}
.bonceCon textarea{width: 460px;}
.ty-radio{
    cursor: pointer;
    margin-right:10px;
}
.ty-table td.form-title{
    width: 32%;
    text-align: left;
}
.ty-table td.form-con{
    text-align: left;
}
#form1 .ty-inputText{
    width: 220px;
}
.radioBox{
    width: 220px;
    padding:5px 5px;
    background-color: #e8f0f5;
}
.radioDisabled,.radioDisabled .ty-radio{
    color: #ccc;
    cursor: not-allowed;
}
.chooseDisable{
    color: #ccc;
    cursor: not-allowed;
}
.pack{position:relative;width:220px;}
.accountsList tbody td input[disabled = "disabled"]{
    border:none;
    background:none;
}
.entrySect{
    display:none;
}
#new-finance{
    min-height:150px;
}
.editCon{
    text-align:center;
}
.editCon .form-title{
    display:inline-block;
}

.update_publicType span{
    margin-right:10px;
    display:inline-block;
    width:70px;
    min-height:1px;
}
.markRed td,.markRed span,.markRed i{
    margin-right:10px;
    margin-lefht: 10px;
    color:red;
    vertical-align: middle;
}
.fa-long-arrow-right{
    font-size:36px;
}
.accountName.radioDisabled{
    color: #ccc;
    cursor: not-allowed;
}
.ty-table td.line-title{
    width:120px;
    text-align:left;
}
.ty-table td.line-con{
    text-align:left;
}
.ty-table td.line-con span:first-child{
    display: inline-block;
    width:40%;
    text-align:right;

}
#checkAccount .bonceCon{
    max-height:450px;
    overflow-y: auto;
}
.screenBody{display: inline-block;overflow: hidden;}
.screenBody span{ margin-right: 12px;display: inline-block;line-height: 32px;}
#searchOrg{padding: 0px 20px;background: #fff;border: 1px solid #ccc;border-radius: 5px;line-height: 35px;height: 35px;}
.mainContent{padding: 10px 100px;}
.limitWrap{margin: auto;width: 500px;}
.initFund{padding: 10px 0;margin-bottom: 20px;}































