@charset "utf-8";

@import "variable";

p, span {
  font-size: 14px;
}

//html, body {
//  height: 100%;
//  width: 100%;
//}

.box {
  background-color: $tyColorBg;
  height: 100vh;
  position: relative;
}

.sticky {
  position: sticky;
  top: 44px;
  z-index: 999;
}

.ty-fff {
  background-color: #ffffff;
}

.spaceBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.h35f14 {
  line-height: 35px;
  font-size: 14px;
}

.margin-10-15 {
  margin: 10px 20px;
}

.padding-r-5 {
  padding-right: 5px;
}

.padding-10-15 {
  padding: 10px 15px;
}

.padding-0-15 {
  padding: 0 15px;
}

.padding-0-10 {
  padding: 0 10px;
}

.padding-0-5 {
  padding: 0 5px;
}

.padding-10-30 {
  padding: 10px 30px;
}

.margin_top_16 {
  margin-top: 16px;
}

.margin_bottom_16 {
  margin-bottom: 16px;
}

.hH {
  word-break: break-all;
  white-space: normal;
}

.end-text {
  text-align: right;
}

.cai-liGray {
  background-color: $tyBounceColorGray;
}

.f4 {
  background-color: #f4f4f4;
}

.padding_bottom_green {
  border-bottom: 2px solid $tyColorGreen;
  color: $tyColorGreen;
}

.padding_bottom_gray {
  border-bottom: 2px solid $tyColorGray;
}

.ic {
  width: 10%;
  display: flex;
  justify-content: end;
}

.flex {
  display: flex;
    justify-content: space-between;
}