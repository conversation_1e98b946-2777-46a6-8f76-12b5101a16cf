<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.SubjectBalanceSheetMapper" >

    <select id="getSubBalanSheetByPeroid" resultType="balanceSheet">

    SELECT
    sub.subject AS subject,
    sub.name AS subName,
    sub.level AS level,
    sub.balance_direction AS subDirection,sub.max_child_subjects AS  maxChildSubjects,
    p.beginning_balance AS beginningBalance,p.beginning_direction AS beginningDirection,
    p.previous_balance AS previousBalance,p.previous_direction AS previousDirection,
    p.credit_accumulative AS creditAccumulative,p.debit_accumulative AS debitAccumulative,
    sub.org,sub.parent,p.credit,p.debit,p.balance,p.period AS peroid,p.balance_direction AS balanceDirection,p.subject_id AS subjectId
    FROM `t_accountant_subject_history` sub,`t_accountant_subject_peroid` p
    WHERE sub.org=#{oid} AND p.period=#{peroid} and sub.subject_id=p.subject_id and sub.state=1 order by p.subject


    </select>
    <select id="getChildrenBalanceSheet"
            resultType="balanceSheet">

            SELECT
    sub.subject AS subject,sub.name AS subName,sub.level AS level,sub.balance_direction AS subDirection,
    p.beginning_balance AS beginningBalance,p.beginning_direction AS beginningDirection,
    p.previous_balance AS previousBalance,p.previous_direction AS previousDirection,
    p.credit_accumulative AS creditAccumulative,p.debit_accumulative AS debitAccumulative,
    sub.org,sub.parent,p.credit,p.debit,p.balance,p.period AS peroid,p.balance_direction AS balanceDirection
    FROM `t_accountant_subject_history` sub,`t_accountant_subject_peroid` p
    WHERE sub.org=#{oid} AND p.period=#{peroid} and sub.parent like CONCAT(#{parent},'%') and sub.state=1 and sub.subject_id=p.subject_id

    </select>

    <select id="getTopSubject" resultType="balanceSheet">

    SELECT sub.subject AS subject,
    sub.name AS subName,
    sub.level AS level,
    sub.balance_direction AS subDirection,
    p.beginning_balance AS beginningBalance,
    p.beginning_direction AS beginningDirection,
    p.previous_balance AS previousBalance,
    p.previous_direction AS previousDirection,
    p.credit_accumulative AS creditAccumulative,
    p.debit_accumulative AS debitAccumulative,
    sub.org AS oid,
    sub.parent,
    p.credit,
    p.debit,
    p.balance,
    p.period AS peroid,p.balance_direction AS balanceDirection
    FROM
    `t_accountant_subject_history` sub,`t_accountant_subject_peroid` p
    WHERE sub.org=#{oid} AND p.period=#{peroid} and sub.subject_id=p.subject_id and sub.state=1 and sub.level=1 order by p.subject

    </select>

    <select id="getAccumulative" resultType="balanceSheet">

        SELECT SUM(credit) AS creditAccumulative,SUM(debit) AS debitAccumulative FROM `t_accountant_subject_peroid`
        WHERE period LIKE CONCAT(#{curYear},'%') AND CHAR_LENGTH(period)=7 AND period &lt;= #{peroid} AND subject=#{subject} AND subject_id=#{subjectId};

    </select>

</mapper>