package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.TaccountantSettingHistoryEntity;
import cn.sphd.miners.modules.accountant.mapper.TaccountantSettingHistoryEntityMapper;
import cn.sphd.miners.modules.accountant.service.CommodityRelevanceService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly=false)
public class CommodityRelevanceServiceImpl implements CommodityRelevanceService {

    @Autowired
    TaccountantSettingHistoryEntityMapper settingHistoryEntityMapper;

    /*
     * 获取关联记录
     * */
    @Override
    public List<TaccountantSettingHistoryEntity> getRelationRecords(User user) {
        List<TaccountantSettingHistoryEntity> list = settingHistoryEntityMapper.getRecordsByOrg(user.getOid());
        return list;
    }
}
