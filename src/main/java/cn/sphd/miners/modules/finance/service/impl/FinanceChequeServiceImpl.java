package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dao.FinanceChequeDao;
import cn.sphd.miners.modules.finance.dao.FinanceChequeDetailDao;
import cn.sphd.miners.modules.finance.entity.FinanceCheque;
import cn.sphd.miners.modules.finance.entity.FinanceChequeDetail;
import cn.sphd.miners.modules.finance.service.FinanceChequeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by Administrator on 2016/12/7.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceChequeServiceImpl extends BaseServiceImpl implements FinanceChequeService {

    @Autowired
    FinanceChequeDao financeChequeDao;
    @Autowired
    FinanceChequeDetailDao financeChequeDetailDao;

    @Override
    public void addCashCheque(FinanceCheque financeCheque) {
        financeChequeDao.save(financeCheque);
    }

    @Override
    public List<FinanceCheque> getCheque(Integer oid,List<Integer> orgIntegerList, String type, PageInfo pageInfo) {
//        String hql = " and o.org_= " + oid + " and o.type = '" + type + "' order by createDate desc";
//        List<FinanceCheque> financeCheque = financeChequeDao.findCollectionByConditionNoPage(hql,null,null);
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceCheque where";
        if (oid!=null){
            hql+=" org_=:org";
            params.put("org",oid);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0){ //此里面包含上面的oid，所以此有值，oid已在前面赋值为null
            hql+=" org_ in (:orgIntegerList)";
            params.put("orgIntegerList",orgIntegerList);
        }
        if (StringUtils.isNotEmpty(type)){
            hql+=" and type=:type";
            params.put("type",type);
        }
        hql+=" order by createDate desc";
        List<FinanceCheque> financeCheque = financeChequeDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return financeCheque;
    }

    @Override
    public void addCashChequeDetail(FinanceChequeDetail financeChequeDetail) {
        financeChequeDetailDao.save(financeChequeDetail);
    }

    @Override
    public List<FinanceChequeDetail> getChequeDetail(Integer chequeReg) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceChequeDetail where id in (select max(id) from FinanceChequeDetail where chequeReg_=:chequeReg group by chequeNo) order by state,chequeNo+0";
        params.put("chequeReg",chequeReg);
        List<FinanceChequeDetail> financeChequeDetails = financeChequeDetailDao.getListByHQLWithNamedParams(hql,params);
        return financeChequeDetails;
    }

    @Override
    public FinanceChequeDetail getChequeByDetailId(Integer detailId) {
        FinanceChequeDetail financeChequeDetail = financeChequeDetailDao.get(detailId);
        return financeChequeDetail;
    }

    @Override
    public void updateCashChequeState(FinanceChequeDetail financeChequeDetail) {
        financeChequeDetailDao.update(financeChequeDetail);
    }

    @Override
    public List<FinanceChequeDetail> getChequeByAccountID(Integer accountId) {
        String hql = " from FinanceChequeDetail o where o.state = 1 and o.type = 1 and o.accountId = "+accountId;
        List<FinanceChequeDetail> financeChequeDetails = financeChequeDetailDao.getListByHQL(hql);
        return financeChequeDetails;
    }

    @Override
    public FinanceCheque getFinanceChequeById(Integer id) {
        return financeChequeDao.get(id);
    }

    @Override
    public FinanceChequeDetail getChequeDetailById(Integer chequeId) {
        return financeChequeDetailDao.get(chequeId);
    }


    @Override
    public List<FinanceChequeDetail> getChequeListByFid(Integer fid,String state,String type) {
        String hql = " and o.accountId = "+fid;
        if (state!=null&&!"".equals(state)){
            hql+=" and o.state='"+state+"'";
        }
        if (type!=null&&!"".equals(type)){
            hql+=" and o.type ='"+type+"'";
        }
        List<FinanceChequeDetail> financeChequeDetails = financeChequeDetailDao.findCollectionByConditionNoPage(hql,null,null);
        return financeChequeDetails;
    }

    @Override
    public FinanceChequeDetail getChequeDetailByMyselfId(Integer myselfId) {
        String hql = " from FinanceChequeDetail o where o.myselfId = "+myselfId;
        return financeChequeDetailDao.getByHQL(hql);
    }

    @Override
    public Map<String, Object> getFinanceChequeTime(Integer oid) {
        Map<String,Object> map = new HashMap<>();
        Integer cashTime = this.getBlankCheque(oid, "2", null,"1");  //空白的现金支票
        Integer transferTime = this.getBlankCheque(oid, "1", null,"1");  //空白的转账支票
        Integer yearTime = this.getCheque(oid, null, NewDateUtils.getYear(new Date()));  //今年申领次数
        Integer lastYearTime = this.getCheque(oid, null, NewDateUtils.getYear(NewDateUtils.changeYear(new Date(), -1)));  //去年申领次数
        Integer beforeYearTime = this.getCheque(oid, null, NewDateUtils.getYear(NewDateUtils.changeYear(new Date(), -2)));  //前年申领次数
        map.put("cashTime",cashTime);
        map.put("transferTime",transferTime);
        map.put("yearTime",yearTime);
        map.put("lastYearTime",lastYearTime);
        map.put("beforeYearTime",beforeYearTime);
        return map;
    }

    private Integer getBlankCheque(Integer oid,String type ,Integer year,String state) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id) from FinanceChequeDetail where chequeReg_ in (select id from FinanceCheque where org_=:org)";
        map.put("org",oid);
        if (!MyStrings.nulltoempty(type).isEmpty()){   //1-转帐支票，2-现金汇票
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (year!=null){
            hql+=" and YEAR(createDate)=:year";
            map.put("year",year);
        }
        if (!MyStrings.nulltoempty(state).isEmpty()){
            hql+=" and state=:state";
            map.put("state",state);
        }
        Long num = (Long) financeChequeDao.getByHQLWithNamedParams(hql,map);
        return num.intValue();
    }

    private Integer getCheque(Integer oid,String type ,Integer year) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id) from FinanceCheque where org_=:org";
        map.put("org",oid);
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (year!=null){
            hql+=" and YEAR(createDate)=:year";
            map.put("year",year);
        }
        Long num = (Long) financeChequeDao.getByHQLWithNamedParams(hql,map);
        return num.intValue();
    }

    @Override
    public Map<String, Object> getAccountCheques(Integer oid, String type,Integer accountId,Map<String,Object> map) {
        Map<String,Object> map2 = new HashMap<>();
        String hql = "select COUNT(fcd.id),fc.account,fc.bankName,fcd.accountId_ from FinanceChequeDetail fcd, FinanceCheque fc " +
                "where fcd.state='1' and fc.id=fcd.chequeReg_";
        if (oid!=null){
            hql+=" and fc.org_=:org";
            map2.put("org",oid);
        }
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and fcd.type=:type";
            map2.put("type",type);
        }
        if (accountId!=null){
            hql+=" and fcd.accountId_=:accountId";
            map2.put("accountId",accountId);
        }
        hql+=" group by fcd.accountId_";
        List<Object[]> objects = financeChequeDao.getListByHQLWithNamedParams(hql,map2);

        List<Map<String,Object>> obs = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("num",ob[0]);  //张数
            map1.put("account",ob[1]);  //账号
            map1.put("bankName",ob[2]);  //开户行
            map1.put("accountId",ob[3]);  //账户id
            obs.add(map1);
        }
        map.put("accountCheques",obs);
        return map;
    }

    @Override
    public Map<String, Object> getAccountChequeList(Integer accountId,String type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select fcd.id,fcd.chequeNo,fc.buyerName,fc.buyDate from FinanceChequeDetail fcd, FinanceCheque fc " +
                "where fcd.state='1' and fc.id=fcd.chequeReg_";
        if (accountId!=null){
            hql+=" and fcd.accountId_=:accountId";
            map.put("accountId",accountId);
        }
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and fcd.type=:type";
            map.put("type",type);
        }
        List<Object[]> objects = financeChequeDao.getListByHQLWithNamedParams(hql,map);
        map.clear();
        List<Map<String,Object>> chequeDetails = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("chequeDetailId",ob[0]);  //支票详情id
            map1.put("chequeNo",ob[1]);  //支票号
            map1.put("buyerName",ob[2]);  //购买者
            map1.put("buyDate",ob[3]);  //购买时间
            chequeDetails.add(map1);
        }
        map.put("chequeDetails",chequeDetails);
        map = this.getAccountCheques(null,type,accountId,map);
        return map;
    }

    @Override
    public List<FinanceCheque> getYearCheques(Integer oid, Date beginDate,Date endDate) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceCheque where org_=:org";
        map.put("org",oid);
        if (beginDate!=null && endDate!=null){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        List<FinanceCheque> financeCheques = financeChequeDao.getListByHQLWithNamedParams(hql,map);
        return financeCheques;
    }
}
