package cn.sphd.miners.modules.publicInformation.entity;


import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_pi_info_acl")
public class PiInfoAcl extends BaseEntity {

  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
  @Column(name = "org")
  private Integer org;
  @Column(name = "file")
  private Integer file;
  @Column(name = "user")
  private Integer user;
  @Column(name = "view")
  private Byte view;
  @Column(name = "operate")
  private String operate;
  @Column(name = "granted")
  private String granted;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_date")
  private Date createDate;
  @Column(name = "updator")
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_date")
  private Date updateDate;
  @Column(name = "approve_item")
  private Integer approveItem;
  @Column(name = "approve_status")
  private Byte approveStatus;
  @Column(name = "approve_level")
  private Integer approveLevel;
  @Column(name = "auditor")
  private Integer auditor;
  @Column(name = "auditor_name")
  private String auditorName;
  @Column(name = "audit_date")
  private Date auditDate;
  @Column(name = "operation")
  private String operation;
  @Column(name = "apply_memo")
  private String applyMemo;
  @Column(name = "approve_memo")
  private String approveMemo;
  @Column(name = "message_id")
  private Integer messageId;


  @Transient
  private UserHonePageDto userHonePageDto;
  @Transient
  private PiInfo piInfo;


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public Integer getFile() {
    return file;
  }

  public void setFile(Integer file) {
    this.file = file;
  }

  public Integer getUser() {
    return user;
  }

  public void setUser(Integer user) {
    this.user = user;
  }

  public Byte getView() {
    return view;
  }

  public void setView(Byte view) {
    this.view = view;
  }

  public String getOperate() {
    return operate;
  }

  public void setOperate(String operate) {
    this.operate = operate;
  }

  public String getGranted() {
    return granted;
  }

  public void setGranted(String granted) {
    this.granted = granted;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getApproveItem() {
    return approveItem;
  }

  public void setApproveItem(Integer approveItem) {
    this.approveItem = approveItem;
  }

  public Byte getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(Byte approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApproveLevel() {
    return approveLevel;
  }

  public void setApproveLevel(Integer approveLevel) {
    this.approveLevel = approveLevel;
  }

  public Integer getAuditor() {
    return auditor;
  }

  public void setAuditor(Integer auditor) {
    this.auditor = auditor;
  }

  public String getAuditorName() {
    return auditorName;
  }

  public void setAuditorName(String auditorName) {
    this.auditorName = auditorName;
  }

  public Date getAuditDate() {
    return auditDate;
  }

  public void setAuditDate(Date auditDate) {
    this.auditDate = auditDate;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public UserHonePageDto getUserHonePageDto() {
    return userHonePageDto;
  }

  public void setUserHonePageDto(UserHonePageDto userHonePageDto) {
    this.userHonePageDto = userHonePageDto;
  }

  public PiInfo getPiInfo() {
    return piInfo;
  }

  public void setPiInfo(PiInfo piInfo) {
    this.piInfo = piInfo;
  }
}
