package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.modules.personal.dao.PersonnelInterviewDao;
import cn.sphd.miners.modules.personal.entity.PersonnelInterview;
import cn.sphd.miners.modules.personal.service.PersonnelInterviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2018/4/13.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PersonnelInterviewServiceImpl implements PersonnelInterviewService{
    @Autowired
    PersonnelInterviewDao personnelInterviewDao;

    @Override
    public void savePersonnelInterview(PersonnelInterview personnelInterview) {
        personnelInterviewDao.save(personnelInterview);
    }

    @Override
    public void updatePersonnelInterview(PersonnelInterview personnelInterview) {
        personnelInterviewDao.update(personnelInterview);
    }

    @Override
    public PersonnelInterview getPersonnelInterviewById(Integer id) {
        return personnelInterviewDao.get(id);
    }

    @Override
    public Integer getNumberBySuggestion(Integer offerId,String suggestion) {
        String hql="select count(id) from PersonnelInterview where offer=:offerId and suggestion=:suggestion group by creator";
        Map<String,Object> map=new HashMap<>();
        map.put("suggestion",suggestion);
        map.put("offerId",offerId);
        Long number= (Long) personnelInterviewDao.getByHQLWithNamedParams(hql,map);
        return number==null?0:number.intValue();
    }
}
