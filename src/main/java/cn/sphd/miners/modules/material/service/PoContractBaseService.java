package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.material.entity.PoContractBase;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * 供应商合同
 */
public interface PoContractBaseService {

    //添加合同列表
    String addContractBaseList(String contractBaseList,Integer supplierId, User user);

    //获取合同列表
    List<Map<String, Object>> getContractBaseListBySupplier(Integer supplierId);
    //新增合同
    String addContractBase(String contractBase,Integer supplierId, User user);
    //修改合同
    String updateContractBase(String contractBase, User user);
    //续约合同
    String renewalContractBase(String contractBase, User user);

    PoContractBase getContractBase(Integer id);
    //暂停合同列表
    List<Map<String,Object>> getSuspendContractBaseList(Integer supplierId);
    //合约到期合同列表
    List<Map<String,Object>> getContractBaseEndList(Integer customer);
    //暂停启用合同
    String suspendOrStartContractBase(Integer id,Integer enabled,User user);
    //获取没有合同的材料列表
    List<Map<String, Object>>  getNoContractMaterialList(Integer org,Integer supplierId);
    //根据合同获取材料列表
    List<Map<String, Object>> getMtListByContract(Integer id);

    String removeContract(Integer id, User user);
}
