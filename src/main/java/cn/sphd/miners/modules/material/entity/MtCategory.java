package cn.sphd.miners.modules.material.entity;

import cn.sphd.miners.modules.system.entity.Organization;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2016/9/19.
 */
@Entity(name="MtCategory")
@Table(name="t_mt_category")
public class MtCategory implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;

    @Column(name="level"   , nullable=true , unique=false)
    private Integer level;

    @Column(name="is_locked"   , nullable=true , unique=false)
    private Boolean isLocked;

    @Column(name="descriptiom"  , length=255 , nullable=true , unique=false)
    private String descriptiom;

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;

    @Column(name="keywords"  , length=100 , nullable=true , unique=false)
    private String keywords;

    @Column(name="amount"   , nullable=true , unique=false)
    private Integer amount;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="first_grade_id"   , nullable=true , unique=false)
    private Integer firstGradeId;//所属一级分类id


    @ManyToOne (fetch=FetchType.EAGER )
    @JoinColumn(name="parent", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MtCategory parent;

    @Column(name="parent"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer parent_;

    @ManyToOne (fetch=FetchType.LAZY )
    @JoinColumn(name="org", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private Organization org;

    @Column(name="org"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer org_;

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany (targetEntity=MtBase.class, fetch=FetchType.LAZY, mappedBy="category", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<MtBase> mtBaseHashSet = new HashSet<MtBase>();

    @JsonIgnore @JSONField(serialize = false)
    @OneToMany (targetEntity=MtCategory.class, fetch=FetchType.LAZY, mappedBy="parent", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set <MtCategory> mtCategoryHashSet = new HashSet<MtCategory>();


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getLocked() {
        return isLocked;
    }

    public void setLocked(Boolean locked) {
        isLocked = locked;
    }

    public String getDescriptiom() {
        return descriptiom;
    }

    public void setDescriptiom(String descriptiom) {
        this.descriptiom = descriptiom;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public MtCategory getParent() {
        return parent;
    }

    public void setParent(MtCategory parent) {
        this.parent = parent;
    }

    public Integer getParent_() {
        return parent_;
    }

    public void setParent_(Integer parent_) {
        this.parent_ = parent_;
    }

    public Organization getOrg() {
        return org;
    }

    public void setOrg(Organization org) {
        this.org = org;
    }

    public Integer getOrg_() {
        return org_;
    }

    public void setOrg_(Integer org_) {
        this.org_ = org_;
    }

    public Set<MtBase> getMtBaseHashSet() {
        return mtBaseHashSet;
    }

    public void setMtBaseHashSet(Set<MtBase> mtBaseHashSet) {
        this.mtBaseHashSet = mtBaseHashSet;
    }

    public Set<MtCategory> getMtCategoryHashSet() {
        return mtCategoryHashSet;
    }

    public void setMtCategoryHashSet(Set<MtCategory> mtCategoryHashSet) {
        this.mtCategoryHashSet = mtCategoryHashSet;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getFirstGradeId() {
        return firstGradeId;
    }

    public void setFirstGradeId(Integer firstGradeId) {
        this.firstGradeId = firstGradeId;
    }
}
