package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2016/9/19.
 */
@Entity(name="MtStockInfo")
@Table (name="t_mt_stock_info")
public class MtStockInfo {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "current_stock", nullable = true, unique = false)
    private BigDecimal currentStock;

    @Column(name = "available_stock ", nullable = true, unique = false)
    private BigDecimal availableStock;

    @Column(name = "minimum_stock", nullable = true, unique = false)
    private BigDecimal minimumStock;

    @Column(name = "stock", nullable = true, unique = false)
    private BigDecimal stock;

    @Column(name = "stock_position", length = 100, nullable = true, unique = false)
    private String stockPosition;

    @Column(name = "stock_requirements", length = 255, nullable = true, unique = false)
    private String stockRequirements;

    @Column(name = "creator", nullable = true, unique = false)
    private Integer creator;

    @Column(name = "create_name", length = 100, nullable = true, unique = false)
    private String createName;

    @Column(name = "create_date", nullable = true, unique = false)
    private Date createDate;

    @Column(name = "updator", nullable = true, unique = false)
    private Integer updator;

    @Column(name = "update_name", length = 100, nullable = true, unique = false)
    private String updateName;

    @Column(name = "update_date", nullable = true, unique = false)
    private Date updateDate;

    @Column(name = "initial_stock", nullable = true, unique = false)
    private BigDecimal initialStock;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "material", referencedColumnName = "id", nullable = false, unique = false, insertable = true, updatable = true)
    private MtBase material;

    @Column(name = "material", nullable = false, unique = false, insertable = false, updatable = false)
    private Integer material_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public String getStockPosition() {
        return stockPosition;
    }

    public void setStockPosition(String stockPosition) {
        this.stockPosition = stockPosition;
    }

    public String getStockRequirements() {
        return stockRequirements;
    }

    public void setStockRequirements(String stockRequirements) {
        this.stockRequirements = stockRequirements;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }


    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }


    public MtBase getMaterial() {
        return material;
    }

    public void setMaterial(MtBase material) {
        this.material = material;
    }

    public Integer getMaterial_() {
        return material_;
    }

    public void setMaterial_(Integer material_) {
        this.material_ = material_;
    }


    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(BigDecimal availableStock) {
        this.availableStock = availableStock;
    }

    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }
}
