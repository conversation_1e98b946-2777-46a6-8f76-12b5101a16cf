package cn.sphd.miners.modules.mpWeChatRes.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.ec.entity.EcSku;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-12-21 
 */

@Entity
@Table ( name ="t_wx_resource" )
public class WxResource extends BaseEntity {

	/**
	 * ID
	 */
	@Id
	@Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 微信应用账号ID
	 */
   	@Column(name = "app", nullable=true , unique=false )
	private Integer app;

	/**
	 * 微信应用账号ID
	 */
   	@Column(name = "app_id", length=128, nullable=true , unique=false )
	private String appId;

	/**
	 * 机构ID
	 */
   	@Column(name = "org", nullable=true , unique=false )
	private Integer org;

	/**
	 * 所属分类
	 */
   	@Column(name = "category", nullable=true , unique=false )
	private Integer category;

	/**
	 * 类型名称
	 */
   	@Column(name = "category_name", nullable=true , unique=false )
	private String categoryName;

	/**
	 * 目录路径,存各级祖先节点,以逗号分隔
	 */
   	@Column(name = "category_path", nullable=true , unique=false )
	private String categoryPath;

	/**
	 * 状态:false-不启用,true-启用
	 */
   	@Column(name = "enabled", nullable=true , unique=false )
	private boolean enabled;

	/**
	 * 启停用时间
	 */
   	@Column(name = "enabled_time", nullable=true , unique=false )
	private Date enabledTime;

	/**
	 * 文件名称
	 */
   	@Column(name = "name", length=255, nullable=true , unique=false )
	private String name;

	/**
	 * 文件编号
	 */
   	@Column(name = "file_sn", length=255, nullable=true , unique=false )
	private String fileSn;

	/**
	 * 内容说明
	 */
   	@Column(name = "content", nullable=true , unique=false )
	private String content;

	/**
	 * 换版原因
	 */
   	@Column(name = "reason", length=255, nullable=true , unique=false )
	private String reason;

	/**
	 * 文件存储位置（服务器路径）
	 */
   	@Column(name = "path", length=255, nullable=true , unique=false )
	private String path;

	/**
	 * 是否收费
	 */
   	@Column(name = "is_fee", nullable=true , unique=false )
	private Boolean isFee;

	/**
	 * 费额
	 */
	@Column(name = "fee", nullable=true , unique=false )
	private BigDecimal fee;

	@Column(name = "ctype_expires")
	private Integer ctypeExpires;//`ctype_expires` INTEGER DEFAULT NULL AFTER `fee` COMMENT '期限',
	@Column(name = "ctype_id")
	private Short ctypeId;//`ctype_id` SMALLINT DEFAULT NULL AFTER `expires` COMMENT '期限单位（ctype_id）',
	@Column
	private Long stock;//`stock` BIGINT DEFAULT NULL comment '可售数量',

	/**
	 * 文件大小(字节数,精确到K)
	 */
   	@Column(name = "size", nullable=true , unique=false )
	private Long size;

	/**
	 * 总体文件大小(字节数,精确到K)
	 */
   	@Column(name = "total_size", nullable=true , unique=false )
	private Long totalSize;

	/**
	 * 换版次数
	 */
   	@Column(name = "change_num", nullable=true , unique=false )
	private Long changeNum;

	/**
	 * 下载次数
	 */
   	@Column(name = "download_num", nullable=true , unique=false )
	private Long downloadNum;

	/**
	 * 浏览次数
	 */
   	@Column(name = "view_num", nullable=true , unique=false )
	private Long viewNum;

	/**
	 * 移动次数
	 */
   	@Column(name = "move_num", nullable=true , unique=false )
	private Long moveNum;

	/**
	 * 修改次数
	 */
   	@Column(name = "modify_num", nullable=true , unique=false )
	private Long modifyNum;

	/**
	 * 是否垃圾
	 */
   	@Column(name = "is_trash", nullable=true , unique=false )
	private Boolean isTrash;

	/**
	 * 是否已删除
	 */
   	@Column(name = "is_deleted", nullable=true , unique=false )
	private Boolean isDeleted;

	/**
	 * 删除时间
	 */
   	@Column(name = "delete_time", nullable=true , unique=false )
	private Date deleteTime;

	/**
	 * 版本号
	 */
   	@Column(name = "version", length=50, nullable=true , unique=false )
	private String version;

	/**
	 * 关键词
	 */
   	@Column(name = "keyword", length=100, nullable=true , unique=false )
	private String keyword;

	/**
	 * 是否有效
	 */
   	@Column(name = "valid", nullable=true , unique=false )
	private Boolean valid;

	/**
	 * 上传类型:1-需要审批,2-不需要审批
	 */
   	@Column(name = "type", length=1, nullable=true , unique=false )
	private String type;

	/**
	 * 是否置顶
	 */
   	@Column(name = "is_stick", nullable=true , unique=false )
	private Boolean isStick;

	/**
	 * 制作者
	 */
   	@Column(name = "operator", nullable=true , unique=false )
	private Integer operator;

	/**
	 * 制作者姓名
	 */
   	@Column(name = "opertator_name", length=100, nullable=true , unique=false )
	private String opertatorName;

	/**
	 * 制做时间
	 */
   	@Column(name = "operate_date", nullable=true , unique=false )
	private Date operateDate;

	/**
	 * 审批实例ID
	 */
   	@Column(name = "approval_instance", nullable=true , unique=false )
	private Integer approvalInstance;

	/**
	 * 终止状态,0-未终止,1-已终止
	 */
   	@Column(name = "teminate_state", length=1, nullable=true , unique=false )
	private String teminateState;

	/**
	 * 终止时间
	 */
   	@Column(name = "teminate_time", nullable=true , unique=false )
	private Date teminateTime;

	/**
	 * 终止原因
	 */
   	@Column(name = "teminate_reason", length=255, nullable=true , unique=false )
	private String teminateReason;

	/**
	 * 审核者ID
	 */
   	@Column(name = "verifier", nullable=true , unique=false )
	private Integer verifier;

	/**
	 * 审核者姓名
	 */
   	@Column(name = "verifier_name", length=100, nullable=true , unique=false )
	private String verifierName;

	/**
	 * 审核时间
	 */
   	@Column(name = "verify_date", nullable=true , unique=false )
	private Date verifyDate;

	/**
	 * 文件审批者ID
	 */
   	@Column(name = "auditor", nullable=true , unique=false )
	private Integer auditor;

	/**
	 * 文件审批者姓名
	 */
   	@Column(name = "audit_name", length=100, nullable=true , unique=false )
	private String auditName;

	/**
	 * 文件审批时间
	 */
   	@Column(name = "audit_date", nullable=true , unique=false )
	private Date auditDate;

	/**
	 * 审批状态:0-未申请 ,1-申请提交,2-通过审核,3-否决审核
	 */
   	@Column(name = "approve_status", length=1, nullable=true , unique=false )
	private String approveStatus;

	/**
	 * 批准者ID
	 */
   	@Column(name = "approver", nullable=true , unique=false )
	private Integer approver;

	/**
	 * 批准者姓名
	 */
   	@Column(name = "approver_name", length=100, nullable=true , unique=false )
	private String approverName;

	/**
	 * 批准时间
	 */
   	@Column(name = "approve_date", nullable=true , unique=false )
	private Date approveDate;

	/**
	 * 操作：1-增,2—删,3-改,4-换版,5-移动,6-修改文件名称,7-修改文件编号,8-停用,9-还原
	 */
   	@Column(name = "operation", length=1, nullable=true , unique=false )
	private String operation;

	/**
	 * 申请备注
	 */
   	@Column(name = "apply_memo", length=255, nullable=true , unique=false )
	private String applyMemo;

	/**
	 * 审批备注
	 */
   	@Column(name = "approve_memo", length=255, nullable=true , unique=false )
	private String approveMemo;

	/**
	 * 备注
	 */
   	@Column(name = "memo", length=255, nullable=true , unique=false )
	private String memo;

	/**
	 * 创建人(上传人)id
	 */
   	@Column(name = "creator", nullable=true , unique=false )
	private Integer creator;

	/**
	 * 创建人(上传人)姓名
	 */
   	@Column(name = "create_name", length=100, nullable=true , unique=false )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date", nullable=true , unique=false )
	@CreationTimestamp
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator", nullable=true , unique=false )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name", length=100, nullable=true , unique=false )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date", nullable=true , unique=false )
	@UpdateTimestamp
	private Date updateDate;

	/**
	 * 对应消息表的id
	 */
   	@Column(name = "message_id", nullable=true , unique=false )
	private Integer messageId;

	@Transient
	private Long skuId;

	@Transient
	private EcSku sku;
	@Transient
	private Date exp;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApp() {
		return app;
	}

	public void setApp(Integer app) {
		this.app = app;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public boolean isEnabled() {
		return enabled;
	}

	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}

	public Date getEnabledTime() {
		return enabledTime;
	}

	public void setEnabledTime(Date enabledTime) {
		this.enabledTime = enabledTime;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFileSn() {
		return fileSn;
	}

	public void setFileSn(String fileSn) {
		this.fileSn = fileSn;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Long getSize() {
		return size;
	}

	public void setSize(Long size) {
		this.size = size;
	}

	public Long getTotalSize() {
		return totalSize;
	}

	public void setTotalSize(Long totalSize) {
		this.totalSize = totalSize;
	}

	public Long getChangeNum() {
		return changeNum;
	}

	public void setChangeNum(Long changeNum) {
		this.changeNum = changeNum;
	}

	public Long getDownloadNum() {
		return downloadNum;
	}

	public void setDownloadNum(Long downloadNum) {
		this.downloadNum = downloadNum;
	}

	public Long getViewNum() {
		return viewNum;
	}

	public void setViewNum(Long viewNum) {
		this.viewNum = viewNum;
	}

	public Long getMoveNum() {
		return moveNum;
	}

	public void setMoveNum(Long moveNum) {
		this.moveNum = moveNum;
	}

	public Long getModifyNum() {
		return modifyNum;
	}

	public void setModifyNum(Long modifyNum) {
		this.modifyNum = modifyNum;
	}

	public Boolean getTrash() {
		return isTrash;
	}

	public void setTrash(Boolean trash) {
		isTrash = trash;
	}

	public Boolean getDeleted() {
		return isDeleted;
	}

	public void setDeleted(Boolean deleted) {
		isDeleted = deleted;
	}

	public Date getDeleteTime() {
		return deleteTime;
	}

	public void setDeleteTime(Date deleteTime) {
		this.deleteTime = deleteTime;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public Boolean getValid() {
		return valid;
	}

	public void setValid(Boolean valid) {
		this.valid = valid;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Boolean getStick() {
		return isStick;
	}

	public void setStick(Boolean stick) {
		isStick = stick;
	}

	public Integer getOperator() {
		return operator;
	}

	public void setOperator(Integer operator) {
		this.operator = operator;
	}

	public String getOpertatorName() {
		return opertatorName;
	}

	public void setOpertatorName(String opertatorName) {
		this.opertatorName = opertatorName;
	}

	public Date getOperateDate() {
		return operateDate;
	}

	public void setOperateDate(Date operateDate) {
		this.operateDate = operateDate;
	}

	public Integer getApprovalInstance() {
		return approvalInstance;
	}

	public void setApprovalInstance(Integer approvalInstance) {
		this.approvalInstance = approvalInstance;
	}

	public String getTeminateState() {
		return teminateState;
	}

	public void setTeminateState(String teminateState) {
		this.teminateState = teminateState;
	}

	public Date getTeminateTime() {
		return teminateTime;
	}

	public void setTeminateTime(Date teminateTime) {
		this.teminateTime = teminateTime;
	}

	public String getTeminateReason() {
		return teminateReason;
	}

	public void setTeminateReason(String teminateReason) {
		this.teminateReason = teminateReason;
	}

	public Integer getVerifier() {
		return verifier;
	}

	public void setVerifier(Integer verifier) {
		this.verifier = verifier;
	}

	public String getVerifierName() {
		return verifierName;
	}

	public void setVerifierName(String verifierName) {
		this.verifierName = verifierName;
	}

	public Date getVerifyDate() {
		return verifyDate;
	}

	public void setVerifyDate(Date verifyDate) {
		this.verifyDate = verifyDate;
	}

	public Integer getAuditor() {
		return auditor;
	}

	public void setAuditor(Integer auditor) {
		this.auditor = auditor;
	}

	public String getAuditName() {
		return auditName;
	}

	public void setAuditName(String auditName) {
		this.auditName = auditName;
	}

	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public String getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(String approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Integer getApprover() {
		return approver;
	}

	public void setApprover(Integer approver) {
		this.approver = approver;
	}

	public String getApproverName() {
		return approverName;
	}

	public void setApproverName(String approverName) {
		this.approverName = approverName;
	}

	public Date getApproveDate() {
		return approveDate;
	}

	public void setApproveDate(Date approveDate) {
		this.approveDate = approveDate;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public String getApplyMemo() {
		return applyMemo;
	}

	public void setApplyMemo(String applyMemo) {
		this.applyMemo = applyMemo;
	}

	public String getApproveMemo() {
		return approveMemo;
	}

	public void setApproveMemo(String approveMemo) {
		this.approveMemo = approveMemo;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getMessageId() {
		return messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public Boolean getIsFee(){
		return isFee;
	}

	public void setIsFee(Boolean isFee){
		this.isFee = isFee;
	}

	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

	public Integer getCtypeExpires() {
		return ctypeExpires;
	}

	public void setCtypeExpires(Integer ctypeExpires) {
		this.ctypeExpires = ctypeExpires;
	}

	public Short getCtypeId() {
		return ctypeId;
	}

	public void setCtypeId(Short ctypeId) {
		this.ctypeId = ctypeId;
	}

	public Long getStock() {
		return stock;
	}

	public void setStock(Long stock) {
		this.stock = stock;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public EcSku getSku() {
		return sku;
	}

	public void setSku(EcSku sku) {
		this.sku = sku;
	}

	public Date getExp() {
		return exp;
	}

	public void setExp(Date exp) {
		this.exp = exp;
	}
}