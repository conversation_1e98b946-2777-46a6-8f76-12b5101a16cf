/*原辅助查询*/
.ty-container{
    text-align: center;
    position: relative;
    margin-left: 20px;
    margin-top: 241px;
}
.ty-otdd{
    margin-left: 20px;
    display: none;
    margin-top: 58px;
}
.ty-page{
    width: 100px;
    text-align: center;
    position: absolute;
    left: 46%;top: -64%;
}
#search_text{
    padding: 14px 95px;
    width: 359px;
    text-align: center;
}
.next{
    border: none;
    background: white;
    color: #5d9cec;
    font-width: bold;
}
#bocked{
    width: 359px;
    position: absolute;
    display: none;
    left: 48.7%;
    text-align: center;
    background: #fce4d6;
    transform: translate(-50%,-50%);
    top: 136%;
}
#bocked option:hover{
    background-color:#f4b084;
}
#back{
    float: left;
    color: #0070c0;
}
.mdi{
    margin-top: 72px;
    float: left;
    margin-left: -135px;
    margin-bottom: 11px;
}
#stork{
    background: #fff2cc;
}
.odd{
    background: #ffffff;
}
.even{
    background:#DDEBF7;
}
#stork #boder tr:nth-child(odd){
    background: #ffffff;
}
#stork #boder tr:nth-child(even){
    background: #DDEBF7;
}