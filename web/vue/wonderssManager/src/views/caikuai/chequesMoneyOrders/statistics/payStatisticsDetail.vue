<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true :showBack=true @mycloseFun="handleCustomClose">
      </PageHeader>
    </div>
    <div class="sticky ty-fff" v-if="payStatisticsDetail && list">
      <p class="padding-0-15">{{ new
      Date(list.beginDate).format('yyyy-MM-dd') }}至{{ new
      Date(list.endDate).format('yyyy-MM-dd') }}期间</p>
      <p class="padding-0-15">付给{{ payStatisticsDetail.oppositeCorp }}的承兑汇票：</p>
    </div>
    <div class="re margin_top_16" v-if="list.financeReturns">
      <div class="spaceBetween" v-for="(item,index) in list.financeReturns" :key="index"
           :class="{ 'f4': index % 2 !== 0 ,'ty-fff':index%2===0}">
        <div>
          <p>
            <span>到期日</span><span class="padding-0-5">{{ new
          Date(item.expireDate).format('yyyy-MM-dd') }}</span>
            <span class="padding-0-10">票据金额<span class="padding-0-5">{{ this.formatCurrency(item.amount) }}</span></span>
          </p>
          <p>汇票号<span class="padding-0-5">{{ item.returnNo }}</span></p>
          <p>录入者<span class="padding-0-5">{{ item.createName }}</span><span>{{ new
          Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </p>
        </div>
        <div class="ic" @click="rou(item.id)">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import {formatCurrency} from '@/utils/utils'
import {getReturnStatisticsList} from '@/api/financeBill'

export default {
  name: "payStatisticsDetail",
  data() {
    return {
      tit: "财会",
      formatCurrency: formatCurrency,
      list: {},
      payStatisticsDetail: {}
    }
  },
  computed: {},
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getList(payStatisticsDetail) {
      const params = {
        type: 2,
        oppositeCorp: payStatisticsDetail.oppositeCorp,
        beginTime: payStatisticsDetail.beginTime,
        returnId: payStatisticsDetail.returnId
      }
      getReturnStatisticsList(params).then(res1 => {
        this.list = res1.data.data
      })
    },
    rou(billId) {
      this.$router.push({path: "/moneyOrderDetail", query: {billId: billId}})
    },
  },
  mounted() {
    let payStatisticsDetail = localStorage.getItem('payStatisticsDetail');
    this.payStatisticsDetail = JSON.parse(payStatisticsDetail);
    this.getList(this.payStatisticsDetail)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.sticky {
  padding: 10px 0 10px 0;
  line-height: 20px;
}

.re {
  position: relative;
  top: 40px;

  .spaceBetween {
    > div {
      padding: 10px 15px;
    }

    * {
      line-height: 20px;
    }
  }
}
</style>