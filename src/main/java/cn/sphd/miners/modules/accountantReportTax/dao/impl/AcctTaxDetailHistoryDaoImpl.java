package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxDetailDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxDetailHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dto.PayTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodHistoryEntity;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AcctTaxDetailHistoryDaoImpl extends BaseDao<AcctTaxDetailHistoryEntity, Serializable> implements AcctTaxDetailHistoryDao {

    @Autowired
    AcctTaxDetailHistoryDao taxDetailHistoryDao;
    @Autowired
    AcctTaxPeriodHistoryDao taxPeriodHistoryDao;
    @Autowired
    AcctTaxHistoryDao taxHistoryDao;

    @Override
    public Map<String,Object> getEditRecord(AcctTaxDetailHistoryEntity taxDetailHistoryEntity) {
//        String hql = "from AcctTaxDetailHistoryEntity where report=:report and org=:org and tax=null and DATE_FORMAT(delareTime,'%Y-%m-%d')=:delareTime order by createDate desc";
//        String hql = "from AcctTaxDetailHistoryEntity where report=:report and period=:period and org=:org and tax=null";
        String hql = "";
        Integer org = taxDetailHistoryEntity.getOrg();
        Integer report = taxDetailHistoryEntity.getReport();
        String period = taxDetailHistoryEntity.getPeriod();
        Integer reportHistory = taxDetailHistoryEntity.getReportHistory();
        Integer creator = taxDetailHistoryEntity.getCreator();

        Map<String,Object> result = new HashMap<String,Object>();
        setPeriod(reportHistory,period,org,creator,result);

        HashMap<String, Object> param = new HashMap<>();
        param.put("report", report);
        param.put("org", org);
        List<AcctTaxDetailHistoryEntity> list = null;
        if (period.length() == 7) {
            hql = "from AcctTaxDetailHistoryEntity where report=:report and period=:period and org=:org and tax=null and state=:state order by createDate desc";
            param.put("period", period);
        }
        else {
            hql = "from AcctTaxDetailHistoryEntity where report=:report and org=:org and tax=null and DATE_FORMAT(delareTime,'%Y-%m-%d')=:delareTime and state=:state order by createDate desc";
            param.put("delareTime", period);
        }
        param.put("state", "1");
        list = taxDetailHistoryDao.getListByHQLWithNamedParams(hql,param);
        result.put("recordList",list);
        return result;
    }

    private void setPeriod(Integer reportHistory, String period, Integer org, Integer creator,Map<String,Object> result) {
        //根据reportHistory和period的月份，查询需报税的时段
        int month = 0;
        int year = 0;
        if (period != null && period.length() > 0) {
            month = Integer.parseInt(period.substring(5,7));
            year = Integer.parseInt(period.substring(0,4));
        }

        String hql = "from AcctTaxPeriodHistoryEntity where org=:org and reportHistory=:reportHistory and beginMonth=:beginMonth and creator=:creator";
        HashMap<String, Object> param = new HashMap<>();
        param.put("reportHistory", reportHistory);
        param.put("beginMonth", month);
        param.put("creator", creator);
        param.put("org", org);
        AcctTaxPeriodHistoryEntity periodHistoryEntity = (AcctTaxPeriodHistoryEntity)taxPeriodHistoryDao.getByHQLWithNamedParams(hql,param);
        if (periodHistoryEntity != null) {
            Integer beginDate = periodHistoryEntity.getBeginDate();
            Integer endDate = periodHistoryEntity.getEndDate();
            String periodStr = "" + year + "年" + String.format("%02d",month) + "月" + String.format("%02d",beginDate) + "日" + " — " + String.format("%02d",endDate) + "日";
            result.put("period",periodStr);
        }
        else {
            result.put("period","");
        }
    }

    /**
     * 返回已缴税款的编辑记录
     * */
    @Override
    public List<PayTaxDto> getPayTaxList(User user, Integer detailId) {
        String hql = "select new cn.sphd.miners.modules.accountantReportTax.dto.PayTaxDto(dh.payTime,dh.updateDate,dh.updateName) from AcctTaxDetailHistoryEntity dh where dh.taxDetail=:taxDetail and dh.state=:state";
        HashMap<String, Object> param = new HashMap<>();
        param.put("taxDetail", detailId);
        param.put("state", "0");
        List<PayTaxDto> list = taxDetailHistoryDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

}
