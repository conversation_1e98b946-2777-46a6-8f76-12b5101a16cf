package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.modules.personal.service.PersonnelContractService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import org.springframework.context.ApplicationContext;

/**
 * Created by 朱思旭 on 2025/1/21.
 */

public class PersonnelContractRemind implements DelayCallback {

    private Integer userId;
    private String memo;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public PersonnelContractRemind(Integer userId, String memo) {
        this.userId = userId;
        this.memo = memo;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        PersonnelContractService personnelContractService = ac.getBean(PersonnelContractService.class);
        personnelContractService.sendPerConMes(userId,memo);
    }

}
