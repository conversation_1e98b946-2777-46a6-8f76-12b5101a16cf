<template>
  <div id="templateOrProjectSearch" class="templateOrProjectSearch">
    <TY_NavTop  :title="title" isButton="true" @toggleSubmit="submit"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item" style="margin-top:5px;">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" style="margin-top:5px;" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approveStatus" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>

          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<style>
  #templateOrProjectSearch .el-radio{ display: inline-block;  }
</style>
<script>
// import { formatDate, filterLeaveType } from '../../js/common'
export default {
  name: 'templateOrProjectSearch',
  data () {
    return {
      code: '',
      title: '',
      loading: false,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approveStatus: '2'
      }
    }
  },
  created: function () {
    let that = this
    this.code = this.$route.params.code
    switch (this.code){
      case 'templateFoundApply':
      case 'templateFoundApproval':
        this.title = '模板创建'
        break
      case 'templateEditApply':
      case 'templateEditApproval':
        this.title = '模板修改'
        break
      case 'productFoundApply':
      case 'productFoundApproval':
        this.title = '产品创建'
        break
      case 'productEditApply':
      case 'productEditApproval':
        this.title = '产品修改'
        break
      case 'productRecoveryApply':
      case 'productRecoveryApproval':
        this.title = '产品恢复使用'
        break
      default:
        console.log("没有匹配到类型")
    }
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      let isOk = true
      if(this.queryForm.type === '3'){
        if(!this.queryForm.beginDate || !this.queryForm.endDate){
          isOk = false
        }
      }
      console.log(JSON.stringify(this.queryForm))
      if(isOk){
        let queryParam = {
          approveStatus: this.queryForm.approveStatus,
          beginDate: this.queryForm.beginDate,
          endDate: this.queryForm.endDate,
          type: this.queryForm.type,
          userId: this.sphdSocket.user.userID
        }
        localStorage.setItem('query', JSON.stringify(queryParam))
        let that = this
        this.$router.push({
          path: `/templateOrProjectSearchList/${that.code}`
        })
      }else{
        that.$message({
          type: 'error',
          message: '请选择自定义时间！'
        })
      }

    }
  }
}
</script>
