<template>
  <div id="overtimeAssign_detail">
    <TY_NavTop title="被指派的加班"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="bg">
        <div v-if="mark !== 'message'">
          <div class="item">
            <div class="item_title">指派人</div>
            <div class="item_content">{{detail.assignerName}} {{detail.assigneTime | formatDay}}</div>
          </div>
          <div class="item">
            <div class="item_title">指派原因</div>
            <div class="item_content">{{detail.reason}}</div>
          </div>
          <div class="item">
            <div class="item_title">需加班日期</div>
            <div class="item_content">{{detail.beginTime | formatDay("YYYY-MM-DD")}}</div>
          </div>
          <div class="item">
            <div class="item_title">时间</div>
            <div class="item_content">{{detail.beginTime | formatDay("HH:mm")}} - {{detail.endTime | formatDay("HH:mm")}}</div>
          </div>
          <div class="item">
            <div class="item_title">加班时长</div>
            <div class="item_content">{{detail.duration}}h</div>
          </div>

        </div>
        <div v-if="mark === 'message'">
          <div class="item">
            <div class="item_title">需加班下属</div>
            <div class="item_content">{{processList[0].userName}}</div>
          </div>
          <div class="item">
            <div class="item_title">指派原因</div>
            <div class="item_content">{{detail.reason}}</div>
          </div>
          <div class="item">
            <div class="item_title">需加班日期</div>
            <div class="item_content">{{detail.beginTime | formatDay("YYYY-MM-DD")}}</div>
          </div>
          <div class="item">
            <div class="item_title">时间</div>
            <div class="item_content">{{detail.beginTime | formatDay("HH:mm")}} - {{detail.endTime | formatDay("HH:mm")}}</div>
          </div>
          <div class="item">
            <div class="item_title">加班时长</div>
            <div class="item_content">{{detail.duration}}h</div>
          </div>
        </div>
        <div class="kj-panel">
          <div class="kj-panel-title">
            <b>操作记录</b>
          </div>
          <div class="processList">
            <div class="process">
              <div>指派时间 <span class="processName">{{detail.assignerName}}</span> {{detail.assigneTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              <div v-for="(it, index) in processList" v-bind:key="index">
                <div v-if="it.approveStatus === '2'">同意时间 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                <div v-if="it.approveStatus === '3'" class="red">不同意时间 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                <div v-if="it.approveStatus === '3'" class="red">理由 {{it.reason}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="kj-panel" v-if="mark === 'approve'">
          请根据自己实际情况进行选择：
          <el-form :rules="rules" :model="selectRule" class="handleFile">
            <el-form-item prop="radio">
              <el-radio-group v-model="selectRule.radio" size="small">
                <el-radio :label="1">同意加班</el-radio>
                <br/>
                <el-radio :label="2">有事，不能加班。</el-radio>
                <el-input v-show="selectRule.radio == '2'" type="textarea" v-model="selectRule.reason" placeholder="请在此录入有事的原因"></el-input>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="approveOverTime()"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.bg{
  background: #fff;
  padding: 4px 16px;
}
.item{
  display: flex;
  line-height: 18px;
  .item_title{
    width: 90px;
    flex: none;
    color: #666;
    line-height: 2rem;
    padding: 0;
  }
  .item_content{
    flex: auto;
    line-height: 2rem;
    text-align: left;
  }
}
.kj-panel{
  border-top:1px solid #ddd;
  line-height: 2;
  padding: 4px 0;
}
.item .item_title.color-red{
  color: red;
}
</style>
<script>
import { gohistory } from '@/js/common'
export default {
  name: 'overtimeAssign_detail',
  data () {
    return {
      loading: false,
      selectRule: {
        radio: 1,
        reason: ''
      },
      processList: {},
      detail: {},
      mark: 'apply'
    }
  },
  created () {
    this.getOverTimeDetail()
    this.mark = this.$route.params.mark
  },
  methods: {
    getOverTimeDetail: function () {
      this.loading = true
      let that = this
      let assignOvertimeId = this.$route.params.id
      this.$http.post('../../../leaveAndOutTime/assignDetail.do', { assignOvertimeId: assignOvertimeId}, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data

        that.detail = reqData.personnelOvertime // 指派加班详情
        that.processList = reqData.processes // 指派加班审批流程

        that.loading = false
      }).catch(function () {
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    approveOverTime () {
      let that = this
      let data = {
        approveStatus: that.selectRule.radio,
        assignOvertimeId: that.$route.params.id
      }
      if (that.selectRule.radio === 2) {
        data.reason = that.selectRule.reason
      }
      that.loading = true
      this.axios.post('../../../leaveAndOutTime/approvalAssignOutTime.do', data)
        .then(function (response) {
          console.log(response)
          let getData = response.data.data
          let status = getData.status
          if (status === 1) {
            that.loading = false
            that.$kiko_message('操作成功！')
            gohistory(that)
          } else if(status === 7) {
            that.loading = false
            that.$kiko_message('操作失败，因为您选择的时间已有加班！')
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  }
}
</script>
