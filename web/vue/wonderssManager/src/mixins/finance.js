 /*
 * 首页财会页用的
 *
 * */

import * as financeApi from "@/api/finance";
import auth from '@/sys/auth'
import { getWeekday, formatCurrency } from '@/utils/utils'

import balance1 from '@/assets/caikuai_img/yue.png'
import balance2 from '@/assets/caikuai_img/shouru.png'
import balance3 from '@/assets/caikuai_img/zhichu.png'

export default {
    name:'finance',
    components:{},
    data(){
        return {
            financeApi: financeApi,
            getWeekday: getWeekday,
            formatCurrency: formatCurrency,
            auth: auth,
            balance1: balance1,
            balance2: balance2,
            balance3: balance3,
        }
    },
    created(){},
    mounted(){
        // document.title = this.title || "财会";
    },
    computed(){},
    methods:{
        formatWageType(type){
            let str = ''
            switch (type) {
                case 1 : str = '职工工资'; break;
                case 2 : str = '个人所得税'; break;
                case 3 : str = '职工社保'; break;
                case 4 : str = '职工公积金'; break;
            }
            return str
        },
        compareTxt(str1, str2){
            if(str1 == str2){
                return str2 || ''
            }else{
                return `<span class="ty-color-red">${ str2 }</span>`
            }
        },
        closeFun(){
            this.$router.push({ path:'/caikuai' })
        },
        seeDetails(item){
            let xiangQingItem = item
            let method = xiangQingItem.info.method    //数据类型
            let type = xiangQingItem.info.type    //数据类型
            let source = xiangQingItem.info.source  //区分是借款的字段，为2则是借款数据,3=回款数据  6的是工资管理的
            let isGo = true // true-可以跳转查看详情的， false-不可以跳转查看详情的
            if (type !== '1') {
                if (source === '8') {
                    // 这是不允许查看的
                    isGo = false
                }
            }
            if(method === '0'){
                isGo = false
            }
            if(isGo){
                localStorage.setItem('xiangQingItem', JSON.stringify(item))
                this.$router.push("/xiangQing")
            }else{
                this.$message.info('暂不允许查看此详情')
            }

        },
        chargePayMethod(type) {
            // 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            type = String(type)
            switch (type) {
                case '1':
                    return '现金';
                    break;
                case '2':
                    return '现金支票';
                    break;
                case '3':
                    return '转账支票';
                    break;
                case '4':
                    return '承兑汇票';
                    break;
                case '5':
                    return '银行转账';
                    break;
            }
        },
        chargeInterestMethod (type) {
            //利息的支付方式：0-无利息,1-按日付,2-按月付,3-按年付
            switch (type) {
                case '0':
                    return '无利息';
                case '1':
                    return '按日付';
                case '2':
                    return '按月付';
                case '3':
                    return '按年付';
            }
        }
    }
}



