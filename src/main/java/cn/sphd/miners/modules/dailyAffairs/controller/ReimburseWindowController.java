package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.accountant.util.Result;
import cn.sphd.miners.modules.dailyAffairs.entity.CommonInvoiceCertification;
import cn.sphd.miners.modules.dailyAffairs.service.PaymentApprovalService;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBill;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillAttachment;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillItem;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillAttachmentService;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillItemService;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.Code;
import cn.sphd.miners.modules.system.entity.CodeCategory;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.CodeService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/4/26.
 */
@Controller
@RequestMapping("/reimburseWindow")
public class ReimburseWindowController {

    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    CodeService codeService;
    @Autowired
    FinanceReimburseBillService financeReimburseBillService;
    @Autowired
    FinanceReimburseBillItemService financeReimburseBillItemService;
    @Autowired
    FinanceReimburseBillAttachmentService financeReimburseBillAttachmentService;
    @Autowired
    PaymentApprovalService paymentApprovalService;
    @Autowired
    UploadService uploadService;


    /**
     * 申请人--报销申请 待处理
     *<AUTHOR>
     *@date 2019/4/28 9:28
    */
    @ResponseBody
    @RequestMapping("/reimburseApply.do")
    @MessageMapping("/reimburseApply")
    private JsonResult reimburseApply(String sessionid,User user){
        List<PersonnelReimburse> personnelReimbursePend = personnelReimburseService.getPersonnelReimburseByUser(user.getUserID(),"1");  //待处理
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/personnelReimbursePend",null,null,null,null, JSON.toJSONString(personnelReimbursePend));  //订阅的通道
        Map<String,Object> map=new HashMap<>();
        map.put("personnelReimbursePend",personnelReimbursePend);  //手机端时使用的返回值
        return new JsonResult(1,map);
    }

    /**
     * 报销详情
     *<AUTHOR>
     *@date 2019/4/28 11:30
    */
    @ResponseBody
    @RequestMapping("/getReimburseDetail.do")
    @MessageMapping("/getReimburseDetail")
    private JsonResult getReimburseDetail(String json,String sessionid,Integer reimburseId){
        JSONObject jsonObject = JSON.parseObject(json);
        if (reimburseId==null) {
            reimburseId = jsonObject.getInteger("reimburseId");
        }
        Map<String,Object> map = new HashMap<>();
        if (reimburseId!=null){
            map = personnelReimburseService.getReimburseDetail(reimburseId);
            clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/getReimburseDetail",null,null,null,null, map);  //订阅的通道
        }
        return new JsonResult(1,map);
    }

    /**
     * 按发票种类/费用类别查询发票详情
     *<AUTHOR>
     *@date 2019/4/28 15:27
    */
    @ResponseBody
    @RequestMapping("/getReimburseBillDetail.do")
    @MessageMapping("/getReimburseBillDetail")
    private JsonResult getReimburseBillDetail(String json,String sessionid){
        JSONObject jsonObject = JSON.parseObject(json);
        Integer reimburseId = jsonObject.getInteger("reimburseId");  //报销id
        Integer billCat = jsonObject.getInteger("billCat");  //票据种类id
        Integer feeCat = jsonObject.getInteger("feeCat");  //一级费用类别id
        Integer secondFeeCat = jsonObject.getInteger("secondFeeCat");  //二级费用类别id
        String billCatName=jsonObject.getString("billCatName"); //票据种类名称
        String feeCatName=jsonObject.getString("feeCatName"); //一级费用类别名称
        String secondFeeCatName=jsonObject.getString("secondFeeCatName"); //二级费用类别名称
        Double totalAmount = jsonObject.getDouble("totalAmount");  //总金额
        Map<String,Object> map = new HashMap<>();
        if (reimburseId!=null){
            map = personnelReimburseService.getReimburseBillDetail(reimburseId,billCat,feeCat,secondFeeCat,map);
        }
        map.put("billCatName",billCatName);
        map.put("feeCatName",feeCatName);
        map.put("secondFeeCatName",secondFeeCatName);
        map.put("totalAmount",totalAmount);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/getReimburseBillDetail",null,null,null,null, map);  //订阅的通道
        return new JsonResult(1,map);
    }

    /**
     * 撤销功能
     *<AUTHOR>
     *@date 2019/4/29 9:27
    */
    @ResponseBody
    @RequestMapping("/cancel.do")
    @MessageMapping("/cancel")
    private JsonResult cancel(String json,String sessionid,Integer reimburseId){
        JSONObject jsonObject = JSON.parseObject(json);
        if (reimburseId==null) {
            reimburseId = jsonObject.getInteger("reimburseId");
        }
        Map<String,Object> map = new HashMap<>();
        if (reimburseId!=null){
            personnelReimburseService.cancel(reimburseId,map);
        }else {
            map.put("state",0);  //删除失败
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/cancel",null,null,null,null, JSON.toJSONString(map));  //订阅的通道
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/4/29 11:29
     *审批人---报销审批待处理/已批准列表
    */
    @ResponseBody
    @RequestMapping("/getApprovalList.do")
    @MessageMapping("/getApprovalList")
    private JsonResult getApprovalList(String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        if (!MyStrings.nulltoempty(sessionid).isEmpty() && user!=null){
            List<PersonnelReimburse> reimburseHandle = personnelReimburseService.getPersonnelReimburseByApprover(user.getUserID(),"1"); //报销待处理
            List<PersonnelReimburse> reimbursesApproval = personnelReimburseService.getPersonnelReimburseByApprover(user.getUserID(),"2"); //报销已批准

            clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/reimburseHandle",null,null,null,null,JSON.toJSONString(reimburseHandle));  //待处理订阅的通道
            clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/reimbursesApproval",null,null,null,null, JSON.toJSONString(reimbursesApproval));  //已批准订阅的通道
            map.put("reimburseHandle",reimburseHandle);
            map.put("reimbursesApproval",reimbursesApproval);
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/4/29 16:29 1.264处理之报销 改(以后直接传值，不用json的)
     *报销审批 批准or驳回
    */
    @ResponseBody
    @RequestMapping("/approvalReimburse.do")
    @MessageMapping("/approvalReimburse")
    private JsonResult approvalReimburse(String json, String sessionid, User user, Integer approvalProcessId, String approvalStatus, String reason){
//        System.out.println("approvalReimburse.do？ /reimburseNotice sessionid = " + sessionid);
//        System.out.println("approvalReimburse.do？ /reimburseNotice approvalProcessId = " + approvalProcessId);
//        System.out.println("approvalReimburse.do？ /reimburseNotice approvalStatus = " + approvalStatus);
//        System.out.println("approvalReimburse.do？ /reimburseNotice reason = " + reason);
//        System.out.println("approvalReimburse.do？ /reimburseNotice user.id = " + user.getUserID());
//        System.out.println("approvalReimburse.do？ /reimburseNotice json = " + json);
        if (approvalProcessId==null || StringUtils.isBlank(approvalStatus)) {
            JSONObject jsonObject = JSON.parseObject(json);
            if (approvalProcessId==null) {
                approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
            }

            if (StringUtils.isBlank(approvalStatus)) {
                approvalStatus = jsonObject.getString("approvalStatus");  //1-批准 2-驳回
            }
            if (StringUtils.isEmpty(reason)) {
                reason = jsonObject.getString("reason");  //驳回理由
            }
        }

        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null && StringUtils.isNotBlank(approvalStatus)){
            ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcessId);
            if ("2".equals(approvalProcess.getApproveStatus()) || !approvalProcess.getToUser().equals(user.getUserID())){  //zhuagn
                map.put("status",2);//已批准 ，不与重复批准
            }else {
                personnelReimburseService.approvalReimburse1(user.getUserID(), approvalProcessId, approvalStatus, reason);
                map.put("status", 1);//批准成功
            }
        } else {
            map.put("status", 0);//批准失败
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/reimburseNotice",null,null,null,null,JSON.toJSONString(map));  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/4/30 17:51
     *财务出纳-个人报销的付款-待处理（待票据审核）/待付款审批/可付款/待复核/待两讫（待付款）/待认证列表
    */
    @ResponseBody
    @RequestMapping("/cashier.do")
    @MessageMapping("/cashier")
    private JsonResult cashier(String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        List<PersonnelReimburse> paymentHandle = paymentApprovalService.cashierPaymentHandle(user.getOid(),"1",17);  //待付款审批
        List<PersonnelReimburse> financePayable = paymentApprovalService.cashierPaymentHandle(user.getOid(),"1",21);  //财务可付款
        List<PersonnelReimburse> financeReview = paymentApprovalService.cashierPaymentHandle(user.getOid(),"1",20);   //财务待复核
        List<PersonnelReimburse> cashierTwoSettled = personnelReimburseService.cashier(null,user.getOid(),"4");  //财务待两讫（待付款）
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/cashierPaymentHandle",null,null,null,null,JSON.toJSONString(paymentHandle));  //返回结
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/financePayable",null,null,null,null,JSON.toJSONString(financePayable));  //财务可付款返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/financeReview",null,null,null,null,JSON.toJSONString(financeReview));  //财务待复核返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/cashierTwoSettled",null,null,null,null,JSON.toJSONString(cashierTwoSettled));  //返回结果的订阅

        map.put("paymentHandle", paymentHandle);//批准成功
        map.put("financePayable", financePayable);//批准成功
        map.put("financeReview", financeReview);//批准成功
        map.put("cashierTwoSettled", cashierTwoSettled);//批准成功

        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/5/7 10:37
     *财务出纳 --个人报销的付款 待两讫(待付款) 驳回 两讫
    */
    @ResponseBody
    @RequestMapping("/cashierTwoApproval.do")
    @MessageMapping("/cashierTwoApproval")
    private JsonResult cashierTwoApproval(String json,String sessionid,User user,Integer approvalProcessId,String factDate,String summary){
        JSONObject jsonObject = JSON.parseObject(json);
        Date factDate1 = null;
        if (StringUtils.isNotEmpty(factDate)){
            factDate1 = NewDateUtils.dateFromString(factDate,"yyyy-MM-dd");
        }
        if (approvalProcessId==null) {
            approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
            factDate1 = jsonObject.getDate("factDate");  //实际付款日期
            summary = jsonObject.getString("summary");  //摘要
        }

        Map<String,Object> map = new HashMap<>();
        map = personnelReimburseService.cashierTwoApproval(user.getUserID(),approvalProcessId,factDate1,summary);
        System.out.println("cashierTwoApproval.do /reimburseNotice sessionid = " + sessionid + " ,result = " + JSON.toJSONString(map));
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/reimburseNotice",null,null,null,null,JSON.toJSONString(map));  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/5/7 16:44 【此类接口以后使用/account/getAccountKinds.do接口】
     *查询系统可用的账户
    */
    @ResponseBody
    @RequestMapping("/getAccounts.do")
    public JsonResult getAccounts(User user){
        List<FinanceAccount> financeAccounts = financeAccountService.getAccountByAccountStatus(user.getOid(), 1,null);
        return new JsonResult(1,financeAccounts);
    }

    /**
    * <AUTHOR>
    * @Date 2019/5/5 10:12
    * 报销申请
    * 此项目与财务中的/debitReimburse.do接口基本类似，共用的。
    */
    @ResponseBody
    @RequestMapping("/submitReimburseApply.do")
    public JsonResult submitReimburseApply(HttpServletRequest request,User user) throws Exception {
        String  reimburse=request.getParameter("reimburse");//报销
        String  commonBills=request.getParameter("commonBills");//公用的票据部分
        String content="申请失败";
        if (!StringUtil.isNullOrEmpty(reimburse)&&!StringUtil.isNullOrEmpty(commonBills)&&user!=null) {
            content = personnelReimburseService.reimburseApply(user.getUserID(), reimburse, commonBills,null);
        }
        Map<String,Object> map = new HashMap<>();
        map.put("content",content);
        return new JsonResult(1,map);

    }

    /**
    * <AUTHOR>
    * @Date 2019/5/8 11:20
    * 新增二级费用类别
    */
    @ResponseBody
    @RequestMapping("/addSecondCode.do")
    public void addSecondCode(User user, Code code, Integer pid, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (code!=null) {
            Integer oid = user.getOid();
            CodeCategory codeCategory = codeService.getCodeCategoryByNameOid(oid, "费用类别");
            Code c=codeService.getCodeByName(oid,code.getName());
            if (c!=null){
                map.put("status", 2);//此类别已经存在，臭不要脸
            }else {
                Code p=codeService.getCodeById(pid);
                code.setParent(p);
                code.setCategory(codeCategory);
                code.setOrders(200);
                code.setEnabled(1);
                codeService.addCode(code);
                map.put("code", code);
                map.put("status", 1);
            }
        }else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map,new String[]{"parent","category"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2019/5/13 9:53
    * 财务数据查看报销详情
    */
    @ResponseBody
    @RequestMapping("/getReimburseInfo.do")
    public void  getReimburseInfo(Integer reimburseId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (reimburseId!=null){
            map = personnelReimburseService.getReimburseInfo(reimburseId);
        }
        ObjectToJson.objectToJson1(map,new String[]{"reimburse","financeReimburseBillItemHashSet","financeReimburseBillAttachmentHashSet","approvalFlow","user","personnelReimbursetAttachmentHashSet","approvalProcessHashSet","financeReimburseBillHashSet","financeReimburseBill"},response);

    }

    /**
    * <AUTHOR>
    * @Date 2019/5/13 10:19
    * 财务数据查看 票据内容接口
    */
    @ResponseBody
    @RequestMapping("/getReimburseBill.do")
    public void getReimburseBill(Integer reimburseBillId,HttpServletResponse response) throws IOException {
        FinanceReimburseBill financeReimburseBill=financeReimburseBillService.getFinanceReimburseBillById(reimburseBillId);
        List<FinanceReimburseBill> financeReimburseBillList = financeReimburseBillService.getFinanceReimburseBillByPid(reimburseBillId);
        for (FinanceReimburseBill f:financeReimburseBillList) {
            f.setBillCatName(codeService.getCodeById(f.getBillCat()).getName());
            List<FinanceReimburseBillItem> financeReimburseBillItemList=financeReimburseBillItemService.getFinanceReimburseBillItemListByBillId(f.getId());
            f.setFinanceReimburseBillItemList(financeReimburseBillItemList);
            List<FinanceReimburseBillAttachment> financeReimburseBillAttachments = financeReimburseBillAttachmentService.getFinanceReimburseBillAttachmentListByBillId(f.getId(),2);
            if (financeReimburseBillAttachments.size()>0) {
                f.setPictures(financeReimburseBillAttachments);
            }
        }
        if (financeReimburseBill!=null && financeReimburseBill.getItemCount()==0) {
            Integer num = financeReimburseBillList.size();
            for (FinanceReimburseBill f : financeReimburseBillList) {
                 f.setBillAmount(f.getBillAmount().divide(new BigDecimal(num)));
                f.setAmount(f.getAmount().divide(new BigDecimal(num)));
            }
        }
        Map<String,Object> map=new HashMap<>();
        map.put("financeReimburseBillList",financeReimburseBillList);
        ObjectToJson.objectToJson1(map,new String[]{"reimburseBill","reimburse","financeReimburseBillItemHashSet","financeReimburseBillAttachmentHashSet"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2019/5/13 11:02
    * 财务数据查看  票据图片
     * type 1-查询报销详情的图片(某一行综合的所有图片)  2-查看某张票具体的图片
    */
    @ResponseBody
    @RequestMapping("/getBillAttachmentList.do")
    public void getBillAttachmentList(Integer reimburseBillId,Integer type,HttpServletResponse response) throws IOException {
        List<FinanceReimburseBillAttachment> fList= financeReimburseBillAttachmentService.getFinanceReimburseBillAttachmentListByBillId(reimburseBillId,type);
        Map<String,Object> map= new HashMap<>();
        map.put("billAttachmentList",fList);
        ObjectToJson.objectToJson1(map,new String[]{"financeReimburseBill"},response);
    }
    
    /**
    * <AUTHOR>
    * @Date 2019/5/13 12:46
    * 获取一级费用类别的二级费用类别列表
    */
    @ResponseBody
    @RequestMapping("/getSecondCodeList.do")
    public void getSecondCodeList(Integer id,HttpServletResponse response) throws IOException {
        List<Code> codeList=codeService.getSecondCodeList(id);
        ObjectToJson.objectToJson(codeList,new String[]{"parent","category"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2019/5/15 9:07
    * 申请人 报销查询
     * type 1-近七日，2-本月，3-自定义
     * approveStatus 2- 批准 3- 驳回
     * feeCat 费用类别id
     * feeCatName 费用类别名称
     * billCat 票据种类 id
     * billCatName 票据种类名称
    */
    @ResponseBody
    @RequestMapping("/myReimburseQuery.do")
    public JsonResult  myReimburseQuery(Integer userId,Integer type,String beginDate,String endDate,Integer approveStatus,Integer feeCat,String feeCatName,Integer billCat,String billCatName) throws ParseException {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=new Date();
        Date end=new Date();
        HashMap<String,Object> hashMap=new HashMap<>();
        switch (type){
            case 1:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin=NewDateUtils.changeDay(end,-6);
                break;
            case 2:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin= NewDateUtils.changeMonth(new Date(),0);  //获取本月第一天
                break;
            case 3:
                begin=sdf.parse(beginDate);
                end=sdf.parse(endDate);
                end=NewDateUtils.getLastTimeOfDay(end);
                break;
        }
        beginDate=sdf.format(begin);
        endDate=sdf.format(end);
        long between = NewDateUtils.getDaies(end.getTime() - begin.getTime());
        Integer state=0;
        if (between<=31){
            List<PersonnelReimburse> pList=financeReimburseBillItemService.myReimburseQueryList(userId,begin,end,approveStatus,feeCat,billCat,hashMap,userId);
            hashMap.put("personnelReimburseList",pList);
            state = 1;//有明细
        }else {
            financeReimburseBillItemService.myReimburseQuery(userId,begin,end,approveStatus,feeCat,billCat,hashMap,userId,1);
        }
        hashMap.put("state",state);// 1-有明细 0-没明细
        hashMap.put("beginDate",beginDate);
        hashMap.put("endDate",endDate);
        hashMap.put("approveStatus",approveStatus);
        hashMap.put("feeCatName",feeCatName);
        hashMap.put("billCatName",billCatName);
        return new JsonResult(1,hashMap);
    }

    /**
    * <AUTHOR>
    * @Date 2019/6/4 11:15
    * 审批人 报销查询
     * type 1-近七日，2-本月，3-自定义
     * applyId 申请人id
     * approveStatus 2- 批准 3- 驳回 5-已报销 8-财务驳回
     * feeCat 费用类别id
     * feeCatName 费用类别名称
     * billCat 票据种类 id
     * billCatName 票据种类名称
    */
    @ResponseBody
    @RequestMapping("/approvalReimburseQuery.do")
    public JsonResult  approvalReimburseQuery(User user,Integer type,Integer applyId,String applyUserName,String beginDate,String endDate,Integer approveStatus,Integer feeCat,String feeCatName,Integer billCat,String billCatName) throws ParseException {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=new Date();
        Date end=new Date();
        HashMap<String,Object> hashMap=new HashMap<>();
        switch (type){
            case 1:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin=NewDateUtils.changeDay(end,-6);
                beginDate=sdf.format(begin);
                endDate=sdf.format(end);
                break;
            case 2:
                end=NewDateUtils.getLastTimeOfDay(new Date());
                begin= NewDateUtils.changeMonth(new Date(),0);  //获取本月第一天
                beginDate=sdf.format(begin);
                endDate=sdf.format(end);
                break;
            case 3:
                begin=sdf.parse(beginDate);
                end=sdf.parse(endDate);
                end=NewDateUtils.getLastTimeOfDay(end);
                break;
        }
        long between = NewDateUtils.getDaies(end.getTime() - begin.getTime());
        Integer state=0;
        if (between<=31){
            List<PersonnelReimburse> pList=financeReimburseBillItemService.myReimburseQueryList(user.getUserID(),begin,end,approveStatus,feeCat,billCat,hashMap,applyId);
            hashMap.put("personnelReimburseList",pList);
            state = 1;//有明细
        }else {
            financeReimburseBillItemService.myReimburseQuery(user.getUserID(),begin,end,approveStatus,feeCat,billCat,hashMap,applyId,1);
        }
        hashMap.put("state",state);// 1-有明细 0-没明细
        hashMap.put("beginDate",beginDate);
        hashMap.put("endDate",endDate);
        hashMap.put("approveStatus",approveStatus);
        hashMap.put("feeCatName",feeCatName);
        hashMap.put("billCatName",billCatName);
        hashMap.put("applyUserName",applyUserName);
        return new JsonResult(1,hashMap);
    }
    
    /**
    * <AUTHOR>
    * @Date 2019/6/12 10:52
    * 报销审批人获取 审批过的申请人列表 按姓名排序
    */
    @ResponseBody
    @RequestMapping("/getReimburseApplyUserList.do")
    public JsonResult getReimburseApplyUserList(User user,String userName,Integer businessType,Integer oid){
        List<UserHonePageDto> uList=approvalProcessService.getUserHonePageDtoListByToUserId(user.getUserID(),userName,businessType,oid);
        return new JsonResult(1,uList);
    }

    /**
    * <AUTHOR>
    * @Description  出纳的查询
    * @Date 2020/8/31
    * type 1-近七日，2-本月，3-自定义
     * applyId 申请人id
     * approveStatus  2- 批准 3- 驳回 5-已报销 8-财务驳回
     * feeCat 费用类别id
     * feeCatName 费用类别名称
     * billCat 票据种类 id
     * billCatName 票据种类名称
    */
    @ResponseBody
    @RequestMapping("/cashierQuery.do")
    public JsonResult cashierQuery(User user,Integer type,Integer applyId,String applyUserName,String beginDate,String endDate,Integer approveStatus,Integer feeCat,String feeCatName,Integer billCat,String billCatName) throws ParseException {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=new Date();
        Date end=new Date();
        HashMap<String,Object> hashMap=new HashMap<>();
        switch (type){
            case 1:
                begin=NewDateUtils.changeDay(end,-6);
                end=NewDateUtils.getLastTimeOfDay(new Date());
                break;
            case 2:
                begin=NewDateUtils.changeMonth(new Date(),0);  //获取本月第一天
                end=NewDateUtils.getLastTimeOfDay(new Date());
                break;
            case 3:
                begin=NewDateUtils.today(sdf.parse(beginDate));
                end=sdf.parse(endDate);
                end=NewDateUtils.getLastTimeOfDay(end);
                break;
        }
        long between = NewDateUtils.getDaies(end.getTime() - begin.getTime());
        Integer state=0;
        if (between<=31){
            List<PersonnelReimburse> pList=financeReimburseBillItemService.getCashierQuery(user.getUserID(),begin,end,approveStatus,feeCat,billCat,hashMap,applyId);
            hashMap.put("personnelReimburseList",pList);
            state = 1;//有明细
        }else {
            financeReimburseBillItemService.myReimburseQuery(user.getUserID(),begin,end,approveStatus,feeCat,billCat,hashMap,applyId,2);
        }
        hashMap.put("state",state);// 1-有明细 0-没明细
        hashMap.put("beginDate",begin);
        hashMap.put("endDate",end);
        hashMap.put("approveStatus",approveStatus);
        hashMap.put("feeCatName",feeCatName);
        hashMap.put("billCatName",billCatName);
        hashMap.put("applyUserName",applyUserName);
        return new JsonResult(1,hashMap);
    }

    /**
     *<AUTHOR>
     *@date 2019/8/5 15:04
     *获取当前时间，额，前端让给返回的
    */
    @ResponseBody
    @RequestMapping("/getCurrentTime.do")
    public void getCurrentTime(HttpServletResponse response) throws IOException {
        Date currentTime = NewDateUtils.today();
        ObjectToJson.objectToJson1(currentTime,new String[]{},response);
    }

    //-------------以下是1.135增票认证的某些接口-----------------------//
    /**
     *<AUTHOR>
     *@date 2020/11/12 9:24
     *出纳审批人查询增票认证列表接口
    */
    @ResponseBody
    @RequestMapping("/cashierAuthentication.do")
    @MessageMapping("/cashierAuthentication")
    private JsonResult cashierAuthentication(String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> cashierAuthentication = personnelReimburseService.cashierAuthentication(user.getOid());  //财务待认证
        map.put("cashierAuthentication", cashierAuthentication);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/cashierAuthenticationHandle",null,null,null,null,map);  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2020/11/12 16:02
     *增票认证中-增专发票的具体货物详情(票据信息)
     * billId-报销票据id
    */
    @ResponseBody
    @RequestMapping("/getAuthBillItemDetail.do")
    @MessageMapping("/getAuthBillItemDetail")
    private JsonResult getAuthBillItemDetail(Integer billId){
        Map<String,Object> map = new HashMap<>();
        if (billId!=null){
            personnelReimburseService.authBillItemDetail(billId,map);
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/5/9 9:30
     *财务增票认证 确定审批
     */
    @ResponseBody
    @RequestMapping("/authApproval.do")
    @MessageMapping("/authApproval")
    private JsonResult authApproval(String json,String sessionid){
        JSONObject jsonObject = JSON.parseObject(json);
        String approvalStatus = jsonObject.getString("approvalStatus");  //1-认证已通过 2-认证未通过，不抵扣，仅报销  3-此张发票认证失败，换了新的发票
        String bills = jsonObject.getString("bills");  //新的发票号码【有id，票据号，时间】
        Map<String,Object> map = personnelReimburseService.authApproval(approvalStatus, bills);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/reimburseNotice",null,null,null,null,JSON.toJSONString(map));  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2020/11/13 11:57
     *出纳增票认证的查询
     * type 1-近七日，2-本月，3-自定义
    */
    @ResponseBody
    @RequestMapping("/getAuthenticationQuery.do")
    public JsonResult getAuthenticationQuery(User user,Integer type,String approvalStatus,String beginDate,String endDate) throws ParseException {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=new Date();
        Date end=new Date();
        HashMap<String,Object> hashMap=new HashMap<>();
        switch (type){
            case 1:
                begin=NewDateUtils.changeDay(end,-6);
                end=NewDateUtils.getLastTimeOfDay(new Date());
                break;
            case 2:
                begin=NewDateUtils.changeMonth(new Date(),0);  //获取本月第一天
                end=NewDateUtils.getLastTimeOfDay(new Date());
                break;
            case 3:
                begin=NewDateUtils.today(sdf.parse(beginDate));
                end=sdf.parse(endDate);
                end=NewDateUtils.getLastTimeOfDay(end);
                break;
        }

        List<FinanceReimburseBill> financeReimburseBills = personnelReimburseService.getAuthenticationQuery(user.getOid(),begin,end,approvalStatus);
        hashMap.put("beginDate",begin);
        hashMap.put("endDate",end);
        hashMap.put("financeReimburseBills",financeReimburseBills);
        return new JsonResult(1,hashMap);
    }


    //--------------------以下是1.137个人报销之审批优化4的接口------------------------------------//

    /**
     *<AUTHOR>
     *@date 2021/1/12
     *个人报销的票据审核的列表
    */
    @ResponseBody
    @RequestMapping("/verificationBills.do")
    @MessageMapping("/verificationBills")
    private JsonResult verificationBills(String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        List<PersonnelReimburse> onlineAuditHandle = paymentApprovalService.cashierPaymentHandle(user.getOid(),"1",32);  //待在线审核
        List<PersonnelReimburse> onlineAuditOK = personnelReimburseService.getOnlineAuditOK(user.getOid(),"1");  //在线审核OK[报销的基本审批流程]
        List<PersonnelReimburse> offlineAuditHandle = paymentApprovalService.cashierPaymentHandle(user.getOid(),"1",33);   //待线下审核
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/onlineAuditHandle",null,null,null,null,JSON.toJSONString(onlineAuditHandle));  //返回结
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/onlineAuditOK",null,null,null,null,JSON.toJSONString(onlineAuditOK));  //财务可付款返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/offlineAuditHandle",null,null,null,null,JSON.toJSONString(offlineAuditHandle));  //财务待复核返回结果的订阅
        map.put("onlineAuditHandle", onlineAuditHandle);//批准成功
        map.put("onlineAuditOK", onlineAuditOK);//批准成功
        map.put("offlineAuditHandle", offlineAuditHandle);//批准成功

        return new JsonResult(1,map);
    }


    /**
     *<AUTHOR>
     *@date 2021/1/13 15:54
     *待在线审核的审批
    */
    @ResponseBody
    @RequestMapping("/onlineAuditApproval.do")
    @MessageMapping("/onlineAuditApproval")
    private JsonResult onlineAuditApproval(String json,String sessionid,User user,Integer approvalProcessId,String approveStatus,String reason,String approveSelect){
        JSONObject jsonObject = JSON.parseObject(json);
        if (approvalProcessId==null) { //id肯定不能为空，那么当id为空的时候那就是使用的json这种格式;id不为空，那就是直接传的
            approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id

            if (StringUtils.isEmpty(approveStatus)) {
                approveStatus = jsonObject.getString("approveStatus");  //2-审批通过  3-驳回
            }
            if (StringUtils.isEmpty(reason)) {
                reason = jsonObject.getString("reason");  //驳回理由（其他的时候录入的）
            }
            if (StringUtils.isEmpty(approveSelect)) {
                approveSelect = jsonObject.getString("approveSelect");  //驳回理由(数字以逗号分隔)
            }
        }

        Map<String,Object> map = new HashMap<>();
        map = personnelReimburseService.onlineAuditApproval(user.getUserID(),approvalProcessId,approveStatus,reason,approveSelect);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/reimburseNotice",null,null,null,null,map);  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/1/13 15:54
     *待线下审核的审批
     */
    @ResponseBody
    @RequestMapping("/offlineAuditApproval.do")
    @MessageMapping("/offlineAuditApproval")
    private JsonResult offlineAuditApproval(String json,String sessionid,User user,Integer approvalProcessId,String approveStatus){
        JSONObject jsonObject = JSON.parseObject(json);
        if (approvalProcessId==null) {
             approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
        }
        if (StringUtils.isEmpty(approveStatus)) {
            approveStatus = jsonObject.getString("approveStatus");  //2-审批通过  3-驳回
        }
        Map<String,Object> map = new HashMap<>();
        map = personnelReimburseService.offlineAuditApproval(user.getUserID(),approvalProcessId,approveStatus);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/reimburseNotice",null,null,null,null,map);  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description  个人报销的票据审核的查询
     * @Date 2021/1/14
     * type 1-近七日，2-本月，3-自定义
     * applyId 申请人id
     * approveStatus  1-票据在线审批未通过 2-审批流程被驳回 3-线下票据不一致
     */
    @ResponseBody
    @RequestMapping("/verificationBillsQuery.do")
    public JsonResult verificationBillsQuery(User user,Integer type,Integer applyId,String applyUserName,String beginDate,String endDate,String approveStatus) throws ParseException {
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=new Date();
        Date end=new Date();
        HashMap<String,Object> hashMap=new HashMap<>();
        switch (type){
            case 1:
                begin=NewDateUtils.changeDay(end,-6);
                end=NewDateUtils.getLastTimeOfDay(new Date());
                break;
            case 2:
                begin=NewDateUtils.changeMonth(new Date(),0);  //获取本月第一天
                end=NewDateUtils.getLastTimeOfDay(new Date());
                break;
            case 3:
                begin=NewDateUtils.today(sdf.parse(beginDate));
                end=sdf.parse(endDate);
                end=NewDateUtils.getLastTimeOfDay(end);
                break;
        }
        List<PersonnelReimburse> pList=personnelReimburseService.verificationBillsQuery(user.getUserID(),begin,end,approveStatus,applyId);
        hashMap.put("personnelReimburseList",pList);
        hashMap.put("beginDate",begin);
        hashMap.put("endDate",end);
        hashMap.put("applyUserName",applyUserName);
        return new JsonResult(1,hashMap);
    }
    
    
    //--------------------1.193项目---------------------------
    
    /**
     * <AUTHOR>
     * @Description 
     * @Date 2022/1/13
     * @param  文件上传，并识别二维码
     * @filePath    发票文件全路径
     * @isPdf       发票是否是PDF格式，true - 是，false - 不是
     * 图片格式接受"bmp" "png" "jpg" "gif" "tiff"等常用格式，只要颜色模式是RGB的就行
     * 解析出的数据：01,10,012002000411,73805096,100.00,20210716,08851416825810364157 【第一个暂时找不到对应的】
     * 发票类型--10 【10：增值税电子普通发票，04：增值税普通发票，01：增值税专用发票】
     * 发票代码--012002000411 发票号码--73805096 合计金额--100.00 开票日期--20210716 发票校验码--08851416825810364157 [专票的解析出来为空]
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/getUploadQR.do")
    public JsonResult getUploadQR(String filePath) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Result result = new Result();
        if (StringUtils.isNotEmpty(filePath)){
            QRcodeUtils qru = new QRcodeUtils();
            File file = uploadService.copyTempFile(filePath);
            String res = qru.getQRcodeFromInvoice(file);//识别文件中的二维码
            if(StringUtils.isNotEmpty(res)) {
                result.setData(res);
                result.setMsg("操作成功");
                map.put("status", 1);
                map.put("result",result);  //返回识别二维码的识别码
                return new JsonResult(1,map);
            }else {
                map.put("status",2);
//                result.setMsg("未识别成功");
                return new JsonResult(new MyException("403", "系统没能识别这个文件！")); //操作失败，二维码未识别成功！
            }
        }else {
            map.put("status",0);
//            result.setMsg("操作失败");
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
//        map.put("result",result);  //返回识别二维码的识别码
//        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description
     * @Date 2022/1/14
     * @param 查询二维码的识别码返回的全部发票数据
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getInvoiceByQR.do")
    public JsonResult getInvoiceByQR(User user,String qrInfo,String key2,String key3,String key6,String nowtime,String yzm,String callback,String _ntime){
        Map<String,Object> map = new HashMap<>();
        //返回系统中的票据种类，可供各个前端使用
        CodeCategory codeCategory=codeService.getCodeCategoryByNameOid(user.getOid(),"票据种类");
        if(codeCategory!=null){
            List<Code> billCats = codeService.getCodesByCategory(codeCategory.getId());
            map.put("billCats", billCats);
        }else {
            map.put("billCats", "");//"数据库‘字典分类表’中不存在可用‘票据种类！’"
        }
        //发票内容
        if (StringUtils.isNotEmpty(qrInfo)){
            CommonInvoiceCertification commonInvoiceCertification = personnelReimburseService.getCertificationByCode(qrInfo);
            if (commonInvoiceCertification!=null){
                System.out.println("getInvoiceByQR.do 获取数据正确1");

                map.put("commonInvoiceCertification",commonInvoiceCertification);  //返回的发票数据
                map.put("status",1);  //操作成功
                return new JsonResult(1,map);
            }else {
                System.out.println("getInvoiceByQR.do开始2");

                String requestUrl = System.getProperty("miners.inv_host") + "/api/inv/vat/";
                Map<String, String> queryMap = new HashMap<>();
                queryMap.put("qr_info", qrInfo);

                Integer secondQuery = 1;  //判断是否要自动循环查询一次  1-不需要(第一次验证的) 2-需要(进行验证录录入，验证失败的，需要重新自动循环查询一次)
                //第二次验证所需添加的传值
                if (StringUtils.isNotEmpty(key2) && StringUtils.isNotEmpty(key3) && StringUtils.isNotEmpty(key6) && StringUtils.isNotEmpty(nowtime) && StringUtils.isNotEmpty(yzm)) {
                    queryMap.put("key2", key2);
                    queryMap.put("key3", key3);
                    queryMap.put("key6", key6);
                    queryMap.put("nowtime", NewDateUtils.dateToString(new Date(System.currentTimeMillis()),"yyyy-MM-dd HH:mm:ss"));
                    queryMap.put("yzm", yzm);
                    queryMap.put("callback",callback);
                    queryMap.put("_ntime",_ntime);

                    secondQuery = 2;
                }
                System.out.println("getInvoiceByQR.do 3，参数=" + queryMap);

                HttpClientUtils httpClientUtils = new HttpClientUtils(requestUrl);
//                Map<String, Object> result = httpClientUtils.jsonResponseToT(httpClientUtils.doGet(null, requestUrl, null, queryMap), Map.class);
                Map<String, Object> result = httpClientUtils.jsonResponseToT(httpClientUtils.doPost(null, requestUrl, null, null,queryMap), Map.class);
                if (result != null) {
                    Map<String, Object> data1 = (Map<String, Object>) result.get("data");
                    String code1 = (String) data1.get("code");
                    if (code1.equals("yzmQuery")) {
                        if (2==secondQuery){
                            System.out.println("getInvoiceByQR.do开始 6");
                            String requestUrl2 = System.getProperty("miners.inv_host")+"/api/inv/vat/";
                            Map<String, String> queryMap2 = new HashMap<>();
                            queryMap2.put("qr_info",qrInfo);

                            System.out.println("getInvoiceByQR.do 7，参数="+queryMap2);
                            HttpClientUtils httpClientUtils2 = new HttpClientUtils(requestUrl2);
//                            Map<String, Object> result2 = httpClientUtils2.jsonResponseToT(httpClientUtils2.doGet(null, requestUrl2, null, queryMap2), Map.class);
                            Map<String, Object> result2 = httpClientUtils2.jsonResponseToT(httpClientUtils2.doPost(null, requestUrl2, null, null,queryMap2), Map.class);
                            if (result2 != null) {
                                Map<String, Object> data2 = (Map<String, Object>) result.get("data");
                                String code2 = (String) data2.get("code");
                                if (code2.equals("yzmQuery")) {
                                    map.put("resultMap", result2);  //返回的发票数据
                                    System.out.println("getInvoiceByQR.do获取失败 8");
                                    map.put("status", 2);  //操作成功
                                } else {// if (code1.equals("vatQuery"))
                                    CommonInvoiceCertification commonInvoiceCertification1 = personnelReimburseService.addInvoiceCertification(qrInfo, data2);
                                    map.put("commonInvoiceCertification", commonInvoiceCertification1);  //返回的发票数据
                                    System.out.println("getInvoiceByQR.do成功结束 9");
                                    map.put("status", 1);  //操作成功
                                }
                                return new JsonResult(1,map);
                            } else {
                                return new JsonResult(new MyException("403", "操作失败，发票服务器报错！"));
                            }
                        }else {
                            System.out.println("getInvoiceByQR.do获取失败，重新获取 4");
                            map.put("resultMap", result);  //返回的发票数据
                            map.put("status", 2);  //操作成功
                        }

                    } else {  // if (code1.equals("vatQuery"))
                        CommonInvoiceCertification commonInvoiceCertification1 = personnelReimburseService.addInvoiceCertification(qrInfo, data1);
                        map.put("commonInvoiceCertification", commonInvoiceCertification1);  //返回的发票数据
                        System.out.println("getInvoiceByQR.do成功结束 5");
                        map.put("status", 1);  //操作成功
                    }
                    return new JsonResult(1, map);
                } else {
                    return new JsonResult(new MyException("403", "操作失败，发票服务器报错！"));
                }

            }
        }
        return new JsonResult(new MyException("400", "操作失败，参数错误！"));
    }

//

    /**
     * <AUTHOR>
     * @Description
     * @Date 2022/1/24
     * @param 再一次用qrInfo识别(暂时没有用)
     * @return
     **/
//    private JsonResult getInvoiceByQRCommon(String qrInfo){
//        Map<String,Object> map = new HashMap<>();
//        System.out.println("getInvoiceByQR.do开始 6");
//        String requestUrl = System.getProperty("miners.inv_host")+"/api/inv/vat/";
//        Map<String, String> queryMap = new HashMap<>();
//        queryMap.put("qr_info",qrInfo);
//
//        System.out.println("getInvoiceByQR.do 7，参数="+queryMap);
//        HttpClientUtils httpClientUtils = new HttpClientUtils(requestUrl);
//        Map<String, Object> result = httpClientUtils.jsonResponseToT(httpClientUtils.doGet(null, requestUrl, null, queryMap), Map.class);
//        if (result != null) {
//            Map<String, Object> data1 = (Map<String, Object>) result.get("data");
//            String code1 = (String) data1.get("code");
//            if (code1.equals("yzmQuery")) {
//                map.put("resultMap", result);  //返回的发票数据
//                System.out.println("getInvoiceByQR.do获取失败 8");
//                map.put("status", 2);  //操作成功
//            } else {// if (code1.equals("vatQuery"))
//                CommonInvoiceCertification commonInvoiceCertification1 = personnelReimburseService.addInvoiceCertification(qrInfo, data1);
//                map.put("commonInvoiceCertification", commonInvoiceCertification1);  //返回的发票数据
//                System.out.println("getInvoiceByQR.do成功结束 9");
//                map.put("status", 1);  //操作成功
//            }
//            return new JsonResult(1,map);
//        } else {
//            return new JsonResult(new MyException("403", "操作失败，发票服务器报错！"));
//        }
//    }

    @ResponseBody
    @RequestMapping("/test.do")
    public JsonResult test(User user,String filePath) {
        Map<String,Object> map = new HashMap<>();
        QRcodeUtils qru = new QRcodeUtils();
        File file = null;
        try {
            file = uploadService.copyTempFile(filePath);
        } catch (IOException e) {
            Logger.getLogger(getClass()).error("读取文件失败，", e);
        }
        if(file!=null) {
            String res = qru.getQRcodeFromInvoice(file);//识别文件中的二维码
            map.put("res",res);
        }
        return new JsonResult(1,map);
    }
}
