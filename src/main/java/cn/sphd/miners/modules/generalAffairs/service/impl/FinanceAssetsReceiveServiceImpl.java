package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.modules.generalAffairs.dao.FinanceAssetsReceiveDao;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsReceive;
import cn.sphd.miners.modules.generalAffairs.service.FinanceAssetsReceiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2017/7/11.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceAssetsReceiveServiceImpl implements FinanceAssetsReceiveService {
         @Autowired
         FinanceAssetsReceiveDao financeAssetsReceiveDao;


    @Override
    public void addFinanceAssetsReceive(FinanceAssetsReceive financeAssetsReceive) {
        financeAssetsReceiveDao.save(financeAssetsReceive);
    }

    @Override
    public void updateFinanceAssetsReceive(FinanceAssetsReceive financeAssetsReceive) {
        financeAssetsReceiveDao.update(financeAssetsReceive);
    }

    @Override
    public List<FinanceAssetsReceive> getFinanceAssetsReceiveList(Integer aid) {
        String hql="from FinanceAssetsReceive o where o.assets="+aid+" order by o.createDate desc";
        return financeAssetsReceiveDao.getListByHQL(hql);
    }

    @Override
    public FinanceAssetsReceive getFinanceAssetsReceive(Integer farId) {
        return financeAssetsReceiveDao.get(farId);
    }
}
