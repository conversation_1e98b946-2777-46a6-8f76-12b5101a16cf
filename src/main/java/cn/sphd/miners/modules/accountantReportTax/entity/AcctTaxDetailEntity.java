package cn.sphd.miners.modules.accountantReportTax.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description  税种详情
 * <AUTHOR>
 * @Date 2021-09-06 
 */
@Entity ( name ="AcctTaxDetailEntity" )
@Table ( name ="t_accountant_tax_detail" )
public class AcctTaxDetailEntity implements Serializable {

	private static final long serialVersionUID =  293761714461769089L;

	/**
	 * 时间范围
	 */
	@Transient
	private List<AcctTaxPeriodHistoryEntity> periodHistoryList;

	/**
	 * 时限ID
	 * getApplyTaxList.do 这个接口返回值里的ID
	 * PC端录入税款时，申报记录中每条的ID
	 * 当PC端录入完成税款后需要把它标记为enable=0，下次返回报税记录的时候不再返回
	 * taxPeriodIdInsert	新增税款的时候ID或是修改后的新ID
	 */
	@Transient
	private Integer taxPeriodIdInsert;

	/**
	 * 税款修改的时候需要把修改前的ID也传过来
	 * */
	@Transient
	private Integer taxPeriodIdUpdate;

	/**
	 * ID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
   	@Column(name = "id" )
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 税种报表ID
	 */
   	@Column(name = "tax_report" )
	private Integer taxReport;

	/**
	 * 税种ID
	 */
   	@Column(name = "tax" )
	private Integer tax;

	/**
	 * 报表ID
	 */
   	@Column(name = "report" )
	private Integer report;


	/**
	 * 申报期起始YYYYMMDD
	 */
	@Column(name = "period_begin" )
	private String periodBegin;

	/**
	 * 申报期截止YYYYMMDD
	 */
	@Column(name = "period_end" )
	private String periodEnd;

	/**
	 * 税款所属开始时期YYYYMMDD
	 */
	@Column(name = "belongs_begin" )
	private String belongsBegin;

	/**
	 * 税款所属姐追时期YYYYMMDD
	 */
	@Column(name = "belongs_end" )
	private String belongsEnd;

	/**
	 * 财务明细账ID
	 */
	@Column(name = "account_detail" )
	private Integer accountDetail;

	/**
	 * 报表历史ID
	 */
	@Column(name = "report_history" )
	private Integer reportHistory;

	/**
	 * 税种历史ID
	 */
	@Column(name = "tax_history" )
	private Integer taxHistory;

	/**
	 * 申报期YYYYMMDD
	 */
   	@Column(name = "period" )
	private String period;

	/**
	 * 状态:0-未报,1-已报
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 申报时间
	 */
   	@Column(name = "delare_time" )
	private Date delareTime;

	/**
	 * 应缴金额
	 */
   	@Column(name = "plan_amount" )
	private BigDecimal planAmount;

	/**
	 * 实缴金额
	 */
   	@Column(name = "fact_amount" )
	private BigDecimal factAmount;

	/**
	 * 缴款时间
	 */
   	@Column(name = "pay_time" )
	private Date payTime;

	/**
	 * 赁证路径
	 */
   	@Column(name = "document_path" )
	private String documentPath;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getTaxPeriodIdInsert() {
		return taxPeriodIdInsert;
	}

	public void setTaxPeriodIdInsert(Integer taxPeriodIdInsert) {
		this.taxPeriodIdInsert = taxPeriodIdInsert;
	}

	public Integer getTaxPeriodIdUpdate() {
		return taxPeriodIdUpdate;
	}

	public void setTaxPeriodIdUpdate(Integer taxPeriodIdUpdate) {
		this.taxPeriodIdUpdate = taxPeriodIdUpdate;
	}

	public String getBelongsBegin() {
		return belongsBegin;
	}

	public void setBelongsBegin(String belongsBegin) {
		this.belongsBegin = belongsBegin;
	}

	public String getBelongsEnd() {
		return belongsEnd;
	}

	public void setBelongsEnd(String belongsEnd) {
		this.belongsEnd = belongsEnd;
	}

	public String getPeriodBegin() {
		return periodBegin;
	}

	public void setPeriodBegin(String periodBegin) {
		this.periodBegin = periodBegin;
	}

	public String getPeriodEnd() {
		return periodEnd;
	}

	public void setPeriodEnd(String periodEnd) {
		this.periodEnd = periodEnd;
	}

	public Integer getAccountDetail() {
		return accountDetail;
	}

	public void setAccountDetail(Integer accountDetail) {
		this.accountDetail = accountDetail;
	}

	public List<AcctTaxPeriodHistoryEntity> getPeriodHistoryList() {
		return periodHistoryList;
	}

	public void setPeriodHistoryList(List<AcctTaxPeriodHistoryEntity> periodHistoryList) {
		this.periodHistoryList = periodHistoryList;
	}

	public Integer getReportHistory() {
		return reportHistory;
	}

	public void setReportHistory(Integer reportHistory) {
		this.reportHistory = reportHistory;
	}

	public Integer getTaxHistory() {
		return taxHistory;
	}

	public void setTaxHistory(Integer taxHistory) {
		this.taxHistory = taxHistory;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getTaxReport() {
		return this.taxReport;
	}

	public void setTaxReport(Integer taxReport) {
		this.taxReport = taxReport;
	}

	public Integer getTax() {
		return this.tax;
	}

	public void setTax(Integer tax) {
		this.tax = tax;
	}

	public Integer getReport() {
		return this.report;
	}

	public void setReport(Integer report) {
		this.report = report;
	}

	public String getPeriod() {
		return this.period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Date getDelareTime() {
		return this.delareTime;
	}

	public void setDelareTime(Date delareTime) {
		this.delareTime = delareTime;
	}

	public BigDecimal getPlanAmount() {
		return this.planAmount;
	}

	public void setPlanAmount(BigDecimal planAmount) {
		this.planAmount = planAmount;
	}

	public BigDecimal getFactAmount() {
		return this.factAmount;
	}

	public void setFactAmount(BigDecimal factAmount) {
		this.factAmount = factAmount;
	}

	public Date getPayTime() {
		return this.payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public String getDocumentPath() {
		return this.documentPath;
	}

	public void setDocumentPath(String documentPath) {
		this.documentPath = documentPath;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
