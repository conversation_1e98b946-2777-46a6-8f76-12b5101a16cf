$(function(){
	App.init(); 
	getServiceList(1,5);
	var ue = UE.getEditor('editor');
	
});


//显示服务协议列表
function getServiceList(cur , perNum){
	$.ajax({
		url:"site_showAgreement.action",
		data:{"pernum":perNum , "curpage":cur },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function(data){
			data = eval('('+ data+')');
			var status = data["status"];//0权限不符，1成功，2.session丢失
			if(status == 2){
				alert("登陆失效，请重新登陆！");
				location.href = "site_loginpage.action";
			}else if(status == 0){
				alert("您的登陆与权限不符，请重新登陆！");
				location.href = "site_loginpage.action";
			}else if(status == 1){
				var list = data["list"];
				var cur = data["curpage"];
				var countall = data["countpage"];
				$("#serviceTbl").children(":eq(1)").html("");
				$("#ye").html("");
				setPage($("#ye"),cur,countall,"service");
				if(list.length > 0){
					for(var i=0 ; i<list.length ; i++){
						 var content = list[i]["content"];
						 var id = list[i]["id"];
						 var valid = list[i]["valid"];
						 var content_id = list[i]["content_id"];
						 var create_date = list[i]["create_date"];
						 var reason = list[i]["reason"];
						 var create_name = list[i]["create_name"];
						 var type = list[i]["type"];
						 var is_stick = list[i]["is_stick"];
						 var creator = list[i]["creator"];
							var chargeTime = list[i]["operate_time"]; // 审批时间
							var chargeName = list[i]["operator_name"]; // 审批人
							var chargeTip = list[i]["approve_desc"];  // 审批意见
						 var status = list[i]["approve_status"]; // 审批状态	
						 chargeTime = getNull(chargeTime);
						 chargeName = getNull(chargeName);
						 chargeTip = getNull(chargeTip);
						 status = getNull(status);
						 content = getNull(content);
						 create_date = getNull(create_date);
						 reason = getNull(reason);
						 create_name = getNull(create_name);
						 
						var sta = "";
						if(status == 0 ){sta= "nosolve"; status = "待审批";  }
						else if(status == 1 ){sta= "solve"; status = "已批准";  } 
						else if(status == 2 ){sta= "unsolve"; status = "已驳回";  } 
						
						var str = "<tr>" +
								"<td>"+ create_date +"</td>" +
								"<td>"+ create_name +"</td>" +
								"<td>"+ reason +"</td>" +
								"<td>"+ chargeTime +"</td>" +
								"<td>"+ chargeName +"</td>" +
								"<td>"+ chargeTip +"</td>" +
								"<td class='"+ sta +"'>"+ status +"</td>" +
								"<td> <span onclick='privacyDetails($(this))' class='hand' >[ 查看 ]</span><span class='hd'>"+ content +"</span> </td>" +
								"</tr>";
						
						$("#serviceTbl").append(str);
					}
				}
				
			}
			
			
		},
		error:function(){ alert("连接错误，请稍后重试！"); } ,
		complete:function(){ chargeClose(1) ;  }
	});
}

// 返回，显示主页面
function ConToggle(){
	$("#addpri_reason").val("");
	UE.getEditor('editor').setContent('');
	$("#privacyCon").show().siblings().hide();;
}
// 点击上传
function addScan(obj){
	$("#privacyAdd").show().siblings().hide();
}
// 查看详情
function privacyDetails(obj){
	var status = obj.parent().prev().html();
	var reson = obj.parent().siblings(":eq(2)").html();
	var explain = obj.parent().siblings(":eq(5)").html();
	var content = obj.siblings(".hd").html();
	
	$("#pri_reson").html(reson);
	$("#pri_status").html(status);
	$("#pri_explain").html(explain);
	$("#pri_content").html(content);

	$("#privacyDetails").show().siblings().hide();
	
}

// 确定上传
function addPri_ok(){
 	var addpri_reason = $("#addpri_reason").val();
// 	var addpri_con = $("#editor").val();
 	var addpri_con = UE.getEditor('editor').getContent();
 	if(addpri_reason == "" || addpri_reason == null || addpri_con.length < 10 || addpri_con == null)
 		alert("请将上传原因及上传内容补充完整！");
 	else{
 		if(confirm("确定提交该上传？")){
 			ConToggle();
 			$.ajax({
 				url:"site_intAgreement.action",
 				data:{ "reason":addpri_reason, "content":addpri_con  },
 				type:"post",
				timeout:5000 ,
				beforeSend:function(){ loading.open() ;  } ,
 				success:function(data){
 					data = eval('('+ data +')');
 					var status = data["status"];
 					if(status == 1){
 						var listcon = data["listhis"];
 						var listapp = data["listapp"];
 						
 						var content = listcon[0]["content"];
 						var id = listcon[0]["id"];
 						var valid = listcon[0]["valid"];
 						var content_id = listcon[0]["content_id"];
 						var create_date = listcon[0]["create_date"];
 						var reason = listcon[0]["reason"];
 						var create_name = listcon[0]["create_name"];
 						var type = listcon[0]["type"];
 						var is_stick = listcon[0]["is_stick"];
 						var creator = listcon[0]["creator"];
 						var chargeTime = listapp[0]["operate_time"]; // 审批时间
 						var chargeName = listapp[0]["operator_name"]; // 审批人
 						var chargeTip = listapp[0]["approve_desc"];  // 审批意见
 						var status = listapp[0]["approve_status"]; // 审批状态	
 						chargeTime = getNull(chargeTime);
 						chargeName = getNull(chargeName);
 						chargeTip = getNull(chargeTip);
 						status = getNull(status);
 						content = getNull(content);
 						create_date = getNull(create_date);
 						reason = getNull(reason);
 						create_name = getNull(create_name);
 						
 						var sta = "";
 						if(status == 0 ){sta= "nosolve"; status = "待审批";  }
 						else if(status == 1 ){sta= "solve"; status = "已批准";  } 
 						else if(status == 2 ){sta= "unsolve"; status = "已驳回";  } 
 						
 						var str = "<tr>" +
 						"<td>"+ create_date +"</td>" +
 						"<td>"+ create_name +"</td>" +
 						"<td>"+ reason +"</td>" +
 						"<td>"+ chargeTime +"</td>" +
 						"<td>"+ chargeName +"</td>" +
 						"<td>"+ chargeTip +"</td>" +
 						"<td class='"+ sta +"'>"+ status +"</td>" +
 						"<td> <span onclick='privacyDetails($(this))' class='hand' >[ 查看 ]</span><span class='hd'>"+ content +"</span> </td>" +
 						"</tr>";
 						
 						$("#serviceTbl").children(":eq(1)").children(":eq(4)").remove();
 						$("#serviceTbl").prepend(str);
 						
 					}else{
 						alert("新增失败，请稍后重试");
 					}
 					
 				},
 				error:function(){ alert("连接失败，请重试！"); } ,
				complete:function(){ loading.close() ;  }
 			});
 			
 		}
 		
 	}
 	
 	
 	
 }