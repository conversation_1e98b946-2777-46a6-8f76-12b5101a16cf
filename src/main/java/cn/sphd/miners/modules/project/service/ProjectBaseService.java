package cn.sphd.miners.modules.project.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.project.entity.ProjectBase;

import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2018/5/31.
 */
public interface ProjectBaseService {

    List<ProjectBase> getProjectBaseByState(Integer oid,String state,Integer initialReceipter, PageInfo pageInfo);

    void addProjectBase(ProjectBase projectBase);

    void updateProjectBase(ProjectBase projectBase);

    ProjectBase getProjectBaseById(Integer id);

    List<ProjectBase> getProjectBaseByDate(Integer oid, String state, Integer initialReceipter, Date beginDate,Date endDate, PageInfo pageInfo);
}
