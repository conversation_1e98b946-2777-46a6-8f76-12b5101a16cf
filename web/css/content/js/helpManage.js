$(function(){
	App.init(); 
	getHelpList(1 , 5);
	getCatoryList();
	
});
// 获取模块的列表
function getCatoryList(){
	$.ajax({
		url:"site_showModule.action",
		type:"post",
		dataType:"json",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function(data){
			var list = data["list"];
			if(list.length > 0){
				var str="";
				for(var i=0; i<list.length; i++){
					str += "<option value='"+ list[i]["id"] +"'>"+ list[i]["name"] +"</option>";
				};
				$("#helpModel").html(str);
				
			};
		},
		error:function(){ alert("获取列表失败"); } ,
		complete:function(){ chargeClose(1) ;  }
	});
}

//显示扫描列表 
function getHelpList(cur , perNum){  
	$.ajax({
		url:"site_showUsehelp.action",
		data:{"pernum":perNum , "curpage":cur },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ; ajaxArr[2] = 1 ; },
		success:function(data){
			data = eval('('+ data+')');
			var status = data["status"];//0权限不符，1成功，2.session丢失
			if(status == 2){
				alert("登陆失效，请重新登陆！");
				location.href = "site_loginpage.action";
			}else if(status == 0){
				alert("您的登陆与权限不符，请重新登陆！");
				location.href = "site_loginpage.action";
			}else if(status == 1){
				var list = data["list"];
				var cur = data["curpage"];
				var countall = data["countpage"];
				$("#helpTbl").children(":eq(1)").html("");
				$("#ye").html("");
				setPage($("#ye"),cur,countall,"help");
				if(list.length > 0){
					for(var i=0 ; i<list.length ; i++){
						 var content = list[i]["content"];
						 var id = list[i]["id"];
						 var valid = list[i]["valid"];
						 var content_id = list[i]["content_id"];
						 var create_date = list[i]["create_date"];
						 var reason = list[i]["reason"];
						 var create_name = list[i]["create_name"];
						 var type = list[i]["type"];
						 var is_stick = list[i]["is_stick"];
						 var creator = list[i]["creator"];
							var chargeTime = list[i]["operate_time"]; // 审批时间
							var chargeName = list[i]["operator_name"]; // 审批人
							var chargeTip = list[i]["approve_desc"];  // 审批意见
						 var status = list[i]["approve_status"]; // 审批状态	
						 chargeTime = getNull(chargeTime);
						 chargeName = getNull(chargeName);
						 chargeTip = getNull(chargeTip);
						 status = getNull(status);
						 content = getNull(content);
						 create_date = getNull(create_date);
						 reason = getNull(reason);
						 create_name = getNull(create_name);
						 
						var sta = "";
						if(status == 0 ){sta= "nosolve"; status = "待审批";  }
						else if(status == 1 ){sta= "solve"; status = "已批准";  } 
						else if(status == 2 ){sta= "unsolve"; status = "已驳回";  } 
						
						var str = "<tr>" +
								"<td>"+ create_date +"</td>" +
								"<td>"+ create_name +"</td>" +
								"<td>"+ reason +"</td>" +
								"<td>"+ chargeTime +"</td>" +
								"<td>"+ chargeName +"</td>" +
								"<td>"+ chargeTip +"</td>" +
								"<td class='"+ sta +"'>"+ status +"</td>" +
								"<td> <span onclick='privacyDetails($(this))' class='hand' >[ 查看 ]</span><span class='hd'>"+ content_id +"</span> </td>" +
								"</tr>";
						
						$("#helpTbl").append(str);
					}
				}
			}
		},
		error:function(){ alert("连接错误，请稍后重试！"); } ,
		complete:function(){ chargeClose( 2 ) ;  }
	});
}

//保存编辑的帮助item
var helpItem_edit = { itemID:"", itemModel:"", itemTtl:"", itemCon:"" , itemObj:null } ; // 标记是不是在进行 helpItem 的编辑（修改）
function saveHelpItem(){  
	var model = $("#helpModel").val();
	var title = $("#helpTtl").val();
	var content = $("#helpContent").val();
	if (title == "" || title == null || content == "" || content == null) {
		alert("请将标题和内容补充完整");
	} else{
		helpItem_edit.itemModel = model;
		helpItem_edit.itemTtl= title;
		helpItem_edit.itemCon = content;

		var url = "";
		var data = {} ; 
		var isupdate = 0 ;  // 0 is NO, 1 is OK
		if (helpItem_edit.itemObj == null) {
			// 新增
			url = "site_intHelp.action";
			data = { "category_id":helpItem_edit.itemModel, "title" :helpItem_edit.itemTtl, "content":helpItem_edit.itemCon};
		} else{
			// 修改
			isupdate = 1 ; 
			if(isAddHelp){ url = "site_updateHelp.action";	}
			else{ url = "site_uphelp.action";	}
			data = { "id": helpItem_edit.itemID , "category_id": helpItem_edit.itemModel , "title": helpItem_edit.itemTtl , "content": helpItem_edit.itemCon  } ;
		};
		$.ajax({
			url:url,
			data:data,
			type:"post",
			dataType:"json",
			timeout:5000 ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function(data){
				var status = data["status"];
				if(status == 1){
					var listcon = data["listcon"];
					var h_id = listcon[0]["id"];
					var h_category = listcon[0]["category"];
					var h_name = listcon[0]["name"];
					var h_reason = listcon[0]["reason"];
					var h_content = listcon[0]["content"];
					
					if (isupdate == 0) {
						//页面做新增
						var str = "<tr><td title='"+ h_category +"'>"+ h_name +"</td><td>"+ h_reason +"</td>"+
								"<td>"+ h_content +"</td>"+
								"<td class=''>" +
								"<span onclick='edit_helpItem($(this))' class='hand' > [ 编辑 ] </span>"+
								"<span onclick='del_helpItem($(this))' class='hand' > [ 删除 ] </span>" +
								"<span class='hd'>"+ h_id +"</span>" +
								"</td></tr>" ; 
						$("#helpUpdate_tbl").append(str);
						
						clearEdit();
						
					}else if (isupdate == 1){
						// 页面做修改
						var upObj = helpItem_edit.itemObj ; 
						upObj.parent().siblings(":eq(0)").html(h_name);
						upObj.parent().siblings(":eq(0)").attr("title", h_id );
						upObj.parent().siblings(":eq(1)").html(h_reason);
						upObj.parent().siblings(":eq(2)").html(h_content);
					};
					
				}else{
					alert("操作失败，请稍后重试！");
				}
				// 复原 helpItem_edit 
				helpItem_edit = { itemID:"", itemModel:"", itemTtl:"", itemCon:"" , itemObj:null } ; 
				clearEdit();
			},
			error:function(){
				alert("保存失败");
			} ,
			complete:function(){ loading.close() ;  }
		});
	};
}

// 点击编辑helpItem 
function edit_helpItem(obj){
	var id = obj.siblings(".hd").html();
	var model_name =  obj.parent().siblings(":eq(0)").html();
	var model =  obj.parent().siblings(":eq(0)").attr("title");
	var title = obj.parent().siblings(":eq(1)").html();
	var content = obj.parent().siblings(":eq(2)").html();
	 
	$("#helpModel").children().each(function(){
		var val_name = $(this).html();
		if(val_name == model_name){ $(this).attr("selected",true); }
		
	});
	 
	$("#helpTtl").val(title);
	$("#helpContent").val(content);
	helpItem_edit = { itemID:id, itemModel:model, itemTtl:title, itemCon:content , itemObj:obj } ;
}
// 删除helpItem 
function del_helpItem(obj){ 
	var mod = obj.parent().siblings(":eq(0)").html();
	var str = " 您确定要删除这条 "+ mod + "模块的使用帮助？ ";
	var id = obj.siblings(".hd").html();
	if(confirm(str)){
		$.ajax({
			url:"site_deleteHelp.action",
			data:{ "id":id },
			type:"post",
			dataType:"json",
			timeout:5000 ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function(data){
				var status = data["status"];
				if(status == 0){
					alert("删除失败，请稍后重试！");
				}else{
					obj.parent().parent().remove();					
				}
			},
			error:function(){
				 alert("连接错误，请稍后重试！");
			} ,
			complete:function(){ loading.close() ;  }
		});
		
	}
	
}
// 清空编辑的内容
function clearEdit(){
	$("#helpModel").val("0");
	$("#helpTtl").val("");
	$("#helpContent").val("");
}

// 点击上传
var isAddHelp = false; // 标记当前是不是在上传新的使用帮助,不是在原来基础上修改的
function addScan(obj){
	isAddHelp = true;
	clearEdit();
	$("#helpUpdate_tbl").children(":eq(1)").html("");
	$("#addpri_reason").val("");
	$("#privacyAdd").show().siblings().hide();
	$.ajax({
		url:"site_clickUpload.action",
		data:{    } ,
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			data = eval('('+ data +')');
			var status = data["status"];
			if(status == 1){
				var list = data["listhis"];
				var id = list[0]["id"];
				var version = list[0]["version"];
				$("#editHelpID").html(id);
				$("#editHelpVersion").html(version);
				isIn_add = { is_add:true , addId:id };
			} 
			else alert("操作错误，请返回重试");
		},
		error:function(){ alert("连接失败，请重试"); },
		complete:function(){ loading.close() ;  }
	});
}
// 查看详情
var scandetailsObj = null ; 
function privacyDetails(obj ){
	deleteCancel();
	scandetailsObj = obj;
	var status = obj.parent().prev().html();
	var reson = obj.parent().siblings(":eq(2)").html();
	
	var addStyle = " color:#fff; ";
	if (status == "已批准") { addStyle += " background:#009DDA; "; $("#updateRight").show(); } 
	else if ( status == "已驳回") { addStyle += " background:#FD8A23; "; $("#updateRight").show(); }
	else if ( status == "待审批") { addStyle += " background:#999; "; $("#updateRight").hide();  } 
	
	$("#status_1").html(status).attr("style" , addStyle);
	$("#pri_reson").html(reson);
	
	updateHelpTbl(1, 5);
	$("#privacyDetails").show().siblings().hide();

}
// 刷新查看详情页
function updateHelpTbl(cur, per){ 
	var id = scandetailsObj.siblings(".hd").html() ;
	$.ajax({
		url:"site_showBasic.action",
		data:{ "id":id , "curpage":cur , "pernum":per  },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			data = eval('('+ data +')');
			var status = data["status"];
			
			$("#helpDetails_tbl").children(":eq(1)").html("");
			$("#ye1").html("");
			
			if(status == 1){
				var list = data["list"];
				var countpage = data["countpage"];
				var curpage = data["curpage"];
				setPage($("#ye1"),curpage,countpage,"helpDetails");
				
				if(list == null || list.length == 0){
					alert("暂未获得详情！");
				}else{
					for(var i = 0; i<list.length ; i++){
						 
						var category = list[i]["name"];
						var title = list[i]["reason"];
						var con = list[i]["content"];
						var str = "<tr>" +
								"<td>"+ category +"</td>" +
								"<td>"+ title +"</td>" +
								"<td>"+ con +"</td>" +
								"<td class='upcon_help'>" +
									"<span onclick='edit_helpItem($(this))' class='hand' > [ 编辑 ] </span> " +
									"<span onclick='del_helpItem($(this))' class='hand' > [ 删除 ] </span>" +
									 
								"</td></tr>";
						$("#helpDetails_tbl").append(str);
					}
					
				}

				
			}else{ alert("获取失败，请稍后重试！"); }
		},
		error:function(){
			alert("连接错误稍后重试");
		},
		complete:function(){ loading.close() ;  }
	});
	
}

// 确定上传
function addPri_ok(){
 	var addpri_reason = $("#addpri_reason").val();
 	var addpri_con = $("#").val();
 	
 	ConToggle();
}

 // 点击“我要修改”
function turnEdit(){
	var id = scandetailsObj.siblings(".hd").html() ;
	$.ajax({
		url:"site_clickUpdate.action",
		data:{ "id":id, "pernum":5, "curpage":1 },
		type:"post",
		dataType:"json",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			var status = data["status"];
			if(status == 1){
				var countpage = data["countpage"];
				var curpage = data["curpage"];
				var list = data["list"];  // 问题记录
				var listcon = data["listcon"];	//  主版本记录 
				var upHelp_id = listcon[0]["id"];
				var upHelp_version = listcon[0]["version"];
				$("#helpUpdate_tbl").children(":eq(1)").html("");
				$("#addpri_reason").val("");
				isIn_add = { is_add:true , addId:upHelp_id };
				setPage($("#ye2"),curpage,countpage,"help_add");
				$("#editHelpID").html(upHelp_id);
				$("#editHelpVersion").html(upHelp_version);
				for(var i = 0; i<list.length ; i++){
					var itemId = list[i]["id"];
					var category = list[i]["name"];			
					var title = list[i]["reason"];		
					var con = list[i]["content"];	
					var str = "<tr>" +		
							"<td>"+ category +"</td>" +
							"<td>"+ title +"</td>" +
							"<td>"+ con +"</td>" +
							"<td class=''>" +
								"<span onclick='edit_helpItem($(this))' class='hand' > [ 编辑 ] </span> " +
								"<span onclick='del_helpItem($(this))' class='hand' > [ 删除 ] </span>" +
								"<span class='hd' >"+ itemId +"</span>" +
							"</td></tr>";
					$("#helpUpdate_tbl").append(str);
				}
			}else{ alert("添加修改失败，请稍后重试！");   }
			
		},
		error:function(){ alert("连接失败，请稍后重试"); } ,
		complete:function(){ loading.close() ;  }
	});
	$("#privacyAdd").show().siblings().hide();	
}

/*刷新修改基础上的页面*/
function upHelp(cur, per){ 
	var id= $("#editHelpID").html();
	$.ajax({
		url:"site_showclickUpdateson.action",
		data:{ "id":id , "curpage":cur , "pernum":per  },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			data = eval('('+ data +')');
			var status = data["status"];
			$("#helpUpdate_tbl").children(":eq(1)").html("");
			$("#ye2").html("");
			if(status == 1){
				var list = data["list"];
				var countpage = data["countpage"];
				var curpage = data["curpage"];
				setPage($("#ye2"),curpage,countpage,"help_add");
				
				if(list == null || list.length == 0){
					alert("暂未获得详情！");
				}else{
					for(var i = 0; i<list.length ; i++){
						var itemId = list[i]["id"];
						var category = list[i]["name"];
						var title = list[i]["reason"];
						var con = list[i]["content"];
						var str = "<tr>" +
								"<td>"+ category +"</td>" +
								"<td>"+ title +"</td>" +
								"<td>"+ con +"</td>" +
								"<td class=''>" +
									"<span onclick='edit_helpItem($(this))' class='hand' > [ 编辑 ] </span> " +
									"<span onclick='del_helpItem($(this))' class='hand' > [ 删除 ] </span>" +
									"<span class='hd' >"+ itemId +"</span>" +
								"</td></tr>";
						$("#helpUpdate_tbl").append(str);
					}
				}
				
			}else{ alert("获取失败，请稍后重试！"); }
		},
		error:function(){
			alert("连接错误稍后重试");
		} ,
		complete:function(){ loading.close() ;  }
	});
	
}

// 确认提交help 的修改或新增
function submitHelp(){
	//检查有无未保存的item
	var tip_ttl = $("#helpTtl").val();
	var tip_con = $("#helpContent").val();
	var isSubmit = true ;
	if(tip_ttl == "" || tip_ttl == null || tip_con == "" || tip_con == null){
		isSubmit = true ;
	}else {
		if(confirm("您当前还有未保存的问题与答案，是否确定提交")){
			isNull = true ;
		}else{
			isSubmit = false ;
		}
	}
	if(isSubmit){
		var id = $("#editHelpID").html();
		var reason = $("#addpri_reason").val();
		if(id == "" || id == null ){
			alert("系统错误，请稍后重试");
			$("#privacyCon").show().siblings().hide();
		}else if( reason=="" || reason == null){
			alert("请将上传理由补充完整！");
		}else{
			if(confirm("确定提交？")){
				$.ajax({
					url:"site_clicksubmit.action",
					data:{ "id":id, "reason":reason },
					type:"post",
					dataType:"json",
					timeout:5000 ,
					beforeSend:function(){ loading.open() ;  } ,
					success:function(data){
						var status = data["status"];
						if(status == 1){
							isIn_add = { is_add:false , addId:null };
							
							var listhis = data["listhis"];
							var list = data["listhis"];
							var id = listhis[0]["id"];
							var create_date = listhis[0]["create_date"];
							var reason = listhis[0]["reason"];
							var create_name = listhis[0]["create_name"];
							var status = list[0]["approve_status"];
							var chargeTime = list[0]["operate_time"]; // 审批时间
							var chargeName = list[0]["operator_name"]; // 审批人
							var chargeTip = list[0]["approve_desc"];  // 审批意见
							chargeTime = getNull(chargeTime);
							chargeName = getNull(chargeName);
							chargeTip = getNull(chargeTip);
							status = getNull(status);
							create_date = getNull(create_date);
							reason = getNull(reason);
							create_name = getNull(create_name);
							
							var sta = "";
							if(status == 0 ){sta= "nosolve"; status = "待审批";  }
							else if(status == 1 ){sta= "solve"; status = "已批准";  } 
							else if(status == 2 ){sta= "unsolve"; status = "已驳回";  } 
							
							var str = "<tr>" +
							"<td>"+ create_date +"</td>" +
							"<td>"+ create_name +"</td>" +
							"<td>"+ reason +"</td>" +
							"<td>"+ chargeTime +"</td>" +
							"<td>"+ chargeName +"</td>" +
							"<td>"+ chargeTip +"</td>" +
							"<td class='"+ sta +"'>"+ status +"</td>" +
							"<td> <span onclick='privacyDetails($(this))' class='hand' >[ 查看 ]</span><span class='hd'>"+ id +"</span> </td>" +
							"</tr>";
							
							$("#helpTbl").children(":eq(1)").children(":gt(3)").remove();
							$("#helpTbl").prepend(str);
							
						}
						
					},
					error:function(){ alert("连接错误，请稍后重试！");  } , 
					complete:function(){ loading.close() ;  }
				});
				$("#privacyCon").show().siblings().hide();
				
			}
		
	}
	}
	
}
//返回，显示主页面
function ConToggle(){
	deleteCancel();
	isAddHelp = false;
	$("#privacyCon").show().siblings().hide();
}








