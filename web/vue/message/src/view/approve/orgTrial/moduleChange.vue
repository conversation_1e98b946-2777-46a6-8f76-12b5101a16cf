<template>
  <div id="moduleChangeApproval">
    <TY_NavTop title="机构所用产品的修改" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toApproval" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.name}}</div>
              <div style="margin-left: 20px">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'moduleChangeApproval',
  data () {
    return {
      toApproval: [],
      listenersUid: []
    }
  },
  created () {
    let that = this
    this.axios.post('../../../special/getEditOrgPopedomApprovals.do', {
      userId: this.sphdSocket.user.userID
    })
      .then(function (response) {
        console.log(response)
        let getData = response.data.data
        that.toApproval = getData
      })
      .catch(function (error) {
        console.log(error)
      })
  },
  methods: {
    jump: function (id) {
      parent.location.href = '../../../special/toModuleChangeHandle.do?type=1&id=' + id
    },
    search: function () {
      this.$router.push({
        path: `/moduleChangeApprovalQuery`
      })
    }
  }
}
</script>
