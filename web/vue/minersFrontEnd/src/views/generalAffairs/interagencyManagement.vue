<template>
  <div class="orgsManage">
<!--  操作指南  -->
    <TyDialog v-if="moreGuide" dialogTitle="操作指南" color="blue" :dialogHide="moreGuideHide" width="620">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="moreGuide = false">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="moreGuideCon">
          <p>1、每位职工都有且只有一个所属机构。所属机构可修改。</p>
          <p>2、初始状态下，所有职工都没有可访问的机构，但可通过修改，设置职工可访问哪些机构。</p>
          <p>3、某职工可访问某机构，意味着该职工可与该机构的其他职工一样使用通讯录、讨论区、备忘与日程、文件与资料以及阅览室模块的功能。</p>
          <p>4、您可随时修改职的所属机构或可访问机构的状态，修改生效后，该职工即立即能够或立即无法登录该机构。</p>
          <p>5、任何修改均将生成无法修改的操作日志，故请谨慎操作。</p>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="userSeeOrgsLog" dialogTitle="修改可访问机构" color="blue" width="620" :dialogHide="moreGuideHide">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="userSeeOrgsLog = false">取 消</el-button>
        <el-button class="bounce-ok" @click="modAccessibleOrganSure">确 定</el-button>
      </template>
      <template #dialogBody>
        <div class="modMain">
          <p class="employeeInfo">{{employeeInfo.userName}} {{employeeInfo.mobile}}</p>
          <div>修改生效后，该职工即立即能够或立即无法登录该机构。</div>
          <div>另外，修改将生成无法修改的操作日志，请谨慎操作！</div>
          <div class="gapTp">
            <el-form ref="form" :model="orgsCheckForm">
              <el-form-item label="">
                <el-checkbox-group v-model="orgsCheckForm.oids">
                  <el-checkbox v-for="(item) in userSeeOrgs" :label="item.oid" name="oid" :key="item.oid">{{item.orgName}}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="modbelongOrganScanLog" dialogTitle="跨机构管理操作记录—修改所属机构—修改前" color="blue" :dialogHide="modbelongScanHide">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="modbelongOrganScanLog = false">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="modLogMain">
          <div class="paceVm">
            <span class="employeeInfo">{{employeeInfo.userName}} {{employeeInfo.mobile}}</span>
            所属的机构
            <span class="employeeOrg"></span>
          </div>
          <div>
            修改后，原所属机构XXXXXX未设置为XXX的可登录机构。
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="modAccessOrganScanLog" :dialogTitle="'跨机构管理操作记录—修改所属机构—' + modAccessOrganData.title" width="580" color="blue" :dialogHide="accessOrganHisHide">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="modAccessOrganScanLog = false">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="modMain">
          <div>
            <span class="employeeInfo">{{modAccessOrganData.userName}} {{modAccessOrganData.mobile}}</span>
          </div>
          <div class="gapTp">
            <ul class="organs clear" v-if="modAccessOrganData.source === 1">
              <li v-for="item in modAccessOrganData.beforeJson" :key="item.oid">
                <i class="fa" :class="{'fa-circle-o': !item.select, 'fa-circle': item.select}"></i>总机构
              </li>
            </ul>
            <ul class="organs clear" v-if="modAccessOrganData.source === 2">
              <li v-for="item in modAccessOrganData.afterJson" :key="item.oid">
                <i class="fa" :class="{'fa-circle-o': !item.select, 'fa-circle': item.select}"></i>总机构
              </li>
            </ul>
          </div>
        </div>
      </template>
    </TyDialog>
    <div class="ty-container">
      <div class="ty-mainData">
        <div class="mainCon mainCon1" v-if="mainCon === 1">
          <div class="txtRight">
            <el-button type="primary" @click="guideSee">操作指南</el-button>
            <el-button type="primary" @click="operationLog">操作日志</el-button>
          </div>
          <div class="vtip">下表中的某职工右侧，带√的机构代表该职工可访问，其中带深色底纹的是该职工所属的机构</div>
          <div class="scrollWrap">
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td class="biaotou">
                  <div class="oblique_line">
                    <span style="float:left">职工</span>
                    <span style="float:right;">机构</span>
                  </div>
                </td>
                <td v-for="(item, index) in indexInteraGencyData.organizationList" :key="index">{{item.name}}</td>
                <td>修改所属机构</td>
                <td>修改可访问机构</td>
              </tr>
              <tr v-for="(item, index1) in indexInteraGencyData.userList" :key="index1">
                <td>{{item.userName}} {{item.mobile}}</td>
                <td v-for="(item2, index2) in indexInteraGencyData.organizationList" :key="index2" :class="{'gray': computedBelong(item.seeOrgsList, item2.name) > -1 }">
                  {{computedBelong(item.seeOrgsList, item2.name) > -1 ? '√':''}}
                </td>
                <td><span class="ty-color-gray">修改</span></td>
                <td><span class="ty-color-blue" @click="editAddressable(item)">修改</span></td>
              </tr>
              </tbody>
            </table>
          </div>
          <TyPage v-if="indexInteraGencyData.pageInfo"
              :curPage="indexInteraGencyData.pageInfo.currentPageNo" :pageSize="indexInteraGencyData.pageInfo.pageSize"
                  :allPage="indexInteraGencyData.pageInfo.totalPage" :pageClickFun="tabIndexPageClick"></TyPage>
        </div>
        <div class="mainCon mainCon2" v-if="mainCon === 2">
          <div><span class="funBtn ty-btn-blue" @click="mainCon = 1">返 回</span></div>
          <div class="logHead flexBt">
            <div id="todayCon">今天是{{new Date().format('yyyy年MM月dd日')}}  星期{{weekDay[new Date().getDay()-1]}}</div>
            <div class="clear">
              <div class="ty-right">
                查看其他月份的数据
                <el-date-picker
                    v-model="searchMonth"
                    type="month"
                    value-format="YYYY-MM"
                    placeholder="请选择月份">
                </el-date-picker>
                <span class="ty-right ty-btn ty-btn-big ty-btn-blue" @click="getSearchKey()">确 定</span>
              </div>
            </div>
          </div>
          <p>有跨机构管理操作的日期在日历中显示为了红色，点击后所见为详情页！</p>
          <div class="tb-main">
            <p id="queryMonth">{{new Date(searchMonth).format('yyyy年MM月')}}</p>
            <table class="ty-table" id="opertionLog">
              <tbody>
              <tr>
                <td>星期一</td>
                <td>星期二</td>
                <td>星期三</td>
                <td>星期四</td>
                <td>星期五</td>
                <td>星期六</td>
                <td>星期日</td>
              </tr>
              <tr v-for="(item,index) in computedCalendar" :key="index">
                <td v-for="(item2,index2) in item" :key="index2">
                  <span v-if="item2 === ''"></span>
                  <span v-else-if="item2.number > 0" class="red" @click="dayLog(item2)">{{item2.mark + 1}} </span>
                  <span v-else>{{item2.mark + 1}}</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="mainCon mainCon3" v-if="mainCon === 3">
          <div><span class="funBtn ty-btn-blue" @click="mainCon = 2">返 回</span></div>
          <p class="gapTp"><span class="dateInfo">{{new Date(searchDay).format('yyyy年MM月dd日')}}</span>跨机构管理的操作日志</p>
          <div class="tb">
            <table class="ty-table ty-table-control" id="opertionDayLog">
              <tbody>
              <tr>
                <td>操作对象</td>
                <td>修改内容</td>
                <td>修改时间</td>
                <td>修改前</td>
                <td>修改后</td>
              </tr>
              <tr v-for=" (item, occIndex) in dayHistoriesListData.userOrgLogList" :key="occIndex">
                <td>{{item.userName}} {{item.mobile}}</td>
                <td>{{item.type === '1'? '修改所属机构':'修改可访问机构'}}</td>
                <td>{{item.createName}} {{new Date(item.createTime).format('yyyy-MM-dd hh:mm:ss')}}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="scanDayHistories(item, 1)">查看</span></td>
                <td><span class="ty-color-blue" type="btn" @click="scanDayHistories(item, 2)">查看</span></td>
              </tr>
              </tbody>
            </table>

            <TyPage v-if="dayHistoriesListData.pageInfo"
                    :curPage="dayHistoriesListData.pageInfo.currentPageNo" :pageSize="dayHistoriesListData.pageInfo.pageSize"
                    :allPage="dayHistoriesListData.pageInfo.totalPage" :pageClickFun="dayHistoriesPageClick"></TyPage>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange";
import * as generApi from "@/api/generalAffairs";

export default {
  data() {
    return {
      pageName: 'interagency',
      mainCon: 1,
      colCount: 5,
      indexInteraGencyData: {
        userList: [],
        organizationList: [],
      },
      historiesListData: {
        data: []
      },
      dayHistoriesListData: {
        userOrgLogList: []
      },
      searchMonth: '',
      searchDay: '',
      weekDay: ["一", "二", "三", "四", "五", "六","日"],
      employeeInfo: {userName:'',mobile:''},
      moreGuide: false,
      userSeeOrgs: [],
      orgsCheckForm: {oids: []},
      modAccessOrganData:{
        title: ''
      },
      userSeeOrgsLog: false,
      modAccessOrganScanLog: false,
      modbelongOrganScanLog: false,
    }
  },
  computed: {
    // 计算属性
    computedCalendar() {
      let arr = []
      let month = []
      if (this.historiesListData.data.length > 0) {
        let list = this.historiesListData.data
        let firstDay = new Date(this.historiesListData.data[0].day);  //获得month的1号
        let firstDayWeekday = firstDay.getDay();
        if (firstDayWeekday === 0) firstDayWeekday = 7
        let before = firstDayWeekday-1;
        let end = this.historiesListData.data.length + firstDayWeekday -1;
        let line = Math.ceil(end / 7)
        for(let i = 0; i < line; i++) {
          arr = []
          for(let g = i * 7; g < 7*(i+1); g++){
            if (g < before || g >= end) {
              arr.push('')
              console.log(arr[g])
            } else {
              let mark = g-firstDayWeekday + 1;
              list[mark].mark = mark
              arr.push(list[mark])
              console.log(arr[g])
            }
          }
          month.push(arr)
        }
      }
      return month
    },
    computedBelong(){
      return function(belong, name) {
        let index = belong.findIndex(item => item.orgName === name);
        return index
      }
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: '', name: '跨机构管理', pageName: this.pageName}, this.getIndexInteraGency(1), this)
  },
  methods: {
    getIndexInteraGency(cur) {
      let json = {
        "currentPageNo": cur,
        "pageSize": 20
      }
      generApi.spanOrgManage(json)
          .then(res => {
            this.indexInteraGencyData = res.data["data"]
          })
    },
    tabIndexPageClick(pageItem) {
      this.indexInteraGencyData.pageInfo.currentPageNo = pageItem.page
      this.getIndexInteraGency(pageItem.page)
    },
    guideSee() {
      this.moreGuide = true
    },
    operationLog() {
      this.mainCon = 2
      this.orgsOptionHistories()
    },
    moreGuideHide(){
      this.moreGuide = false
    },
    orgsOptionHistories(month) {
      let json = {
        "yearMonth": month
      }
      generApi.getMonthHistories(json)
          .then((res) => {
            this.historiesListData = res.data
          })
    },
    editAddressable(info) {
      this.employeeInfo = info
      //$("#modAccessibleOrgan .employeeInfo").html(info.userName+ ' ' + info.mobile);
      this.seeOrgs(info.userID);
    },
    seeOrgs(userId) {
      let json = {
        "passiveUserId":userId
      }
      this.userSeeOrgsLog = true
      generApi.getUserSeeOrgs(json)
          .then((res) => {
                this.userSeeOrgs = res.data.data || [];
          })
    },
    modAccessibleOrganSure(){
      let oids = this.orgsCheckForm.oids
      let json = {
        "oids": oids.toString(),
        "passiveUserId": this.employeeInfo.userID
      }
      generApi.updateUserSeeOrgs(json)
          .then(() => {
            this.userSeeOrgsLog = false
            this.getIndexInteraGency(1)
          })
    },
    dayLog(info){
      this.orgsOptionDayHistories(info.day, 1, 20)
    },
    getSearchKey(){
      this.orgsOptionHistories(this.searchMonth, 1, 20)
    },
    orgsOptionDayHistories(day, currPage  , pageSize) {
      //点击查看日历某日的操作历史记录接口
      //let obj = $("#dayLog_ye").data("obj");
      this.mainCon = 3
      this.searchDay = day
      let json = {
        "yearMonthDay": new Date(day).format('yyyy-MM-dd'),
        "currentPageNo":currPage,
        "pageSize":pageSize
      }
      generApi.getSeeOrgDayHistories(json)
          .then((res) => {
            //let list = res.data.data.userOrgLogList;
            this.dayHistoriesListData = res.data.data
          })
    },
    dayHistoriesPageClick(pageItem) {
      this.dayHistoriesListData.pageInfo.currentPageNo = pageItem.page
      this.orgsOptionDayHistories(this.searchDay, pageItem.page, 20)
    },
    scanDayHistories(detail, source) {
      //查看某个职工可访问的子机构列表
      if(detail.type === 1 || detail.type === '1') {//修改所属机构
        this.modbelongOrganScanLog = true
        this.employeeInfo = detail
      } else {
        this.modAccessOrganScanLog = true
        this.modAccessOrganData = detail
        this.modAccessOrganData.source = source
        if (source === 1) {//修改前
          this.modAccessOrganData.title = "修改前"
          this.modAccessOrganData.beforeJson = JSON.parse(this.modAccessOrganData.beforeJson || '[]')
        } else{
          this.modAccessOrganData.title = "修改后"
          this.modAccessOrganData.afterJson = JSON.parse(this.modAccessOrganData.afterJson || '[]')
        }
      }
    },
    accessOrganHisHide(){
      this.modAccessOrganScanLog = false
    },
    modbelongScanHide(){
      this.modbelongOrganScanLog = false
    }
  }
}
</script>'

<style scoped lang="scss">
.orgsManage{
  font-size: 14px;
  font-family: "Microsoft Yahei";
  color: #101010;
  .ty-mainData{padding-top: 20px; }
  .mainCon{margin: 0 90px;}
  .organs li{float: left;width: 150px;line-height: 40px;height: 40px;}
  .organs li:nth-child(2n+1){margin-right: 100px;}
  .organs li i{margin-right: 14px;}
  .flexBt{display: flex;justify-content: space-between;}
  #otherMonth{padding: 5px 10px;margin-left: 20px;width: 150px;border: 1px solid #bebebe;}
  #otherMonth + span{border-bottom-right-radius: 4px;border-top-right-radius: 4px;}
  .logHead{padding-bottom: 20px;margin: 32px 0;border-bottom: 1px solid #bebebe;}
  .modLogMain{margin: 100px 0 0 100px;}
  .funBtn{width: 130px;height: 40px;line-height: 40px;text-align: center;border-radius: 3px;cursor: pointer;display: inline-block;border: none;}
  .vtip{margin: 20px 0;clear: both;}
  .moreGuideCon{margin: auto;width:85%;}
  .moreGuideCon p{padding-top: 20px}
  .tb-main{padding-top: 10px;}
  .employeeInfo{margin-right: 62px;}
  .employeeOrg{margin-left: 12px;}
  .modMain{width: 80%;margin-left: 100px;margin-top: 20px;}
  .modMain .employeeInfo, .paceVm{margin-bottom: 20px;}
  .gapTp {padding-top: 40px}
  .biaotou{width: 200px;}
  .oblique_line{position: relative;}
  .oblique_line:before{content: "";position: absolute;width: 1px;height: 235px;top: -12px;left: -18px;background-color: #d7d7d7;display: block;transform: rotate(-80.5deg);
    transform-origin: top;}
  #opertionLog .red {padding: 10px;color: red;cursor: pointer}
  .mainCon1 table .gray {background: #e5e3e3;}
  .mainCon1 .ty-table td {max-height: 40px;}
  .scrollWrap{width:100%; overflow-x:scroll;}
  .scrollWrap .ty-table td{white-space: nowrap;}
  .txtRight {text-align: right;}
  #queryMonth, p{margin-bottom: 10px;}
}
</style>