package cn.sphd.miners.modules.mpWeChatRes.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.mpWeChatRes.dao.WxResCatDao;
import cn.sphd.miners.modules.mpWeChatRes.dao.WxResCatHisDao;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceCategory;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceCategoryHistory;
import cn.sphd.miners.modules.mpWeChatRes.service.WxResCatService;
import cn.sphd.miners.modules.mpWeChatRes.service.WxResService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2021/12/21.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class WxResCatServiceImpl implements WxResCatService{

    @Autowired
    WxResCatDao wxResCatDao;
    @Autowired
    WxResCatHisDao wxResCatHisDao;


    @Autowired
    WxResService wxResService;

    @Override
    public WxResourceCategory getWxResCatSingle(Integer id) {
        WxResourceCategory wxResourceCategory = wxResCatDao.get(id);
        return wxResourceCategory;
    }

    @Override
    public HashMap<String, Object> mimiProgramInitialDirectory(User user) {
        String hql = "from WxResourceCategory where parent is null and valid = 1 order by createDate asc";
        List<WxResourceCategory> listFirstCategory = wxResCatDao.getListByHQLWithNamedParams(hql,null);
        if (listFirstCategory.isEmpty()) {
            listFirstCategory = this.addInitialDirectory(user.getOid(),null,null,null,null,null,null);
        }
        Long allFileNum = wxResService.getWxFileNum(null);
        WxResourceCategory catRecycleBin = new WxResourceCategory();
        for (WxResourceCategory r : listFirstCategory) {
            if (r.getTrash()) {
                catRecycleBin = r;
            }else {
                Long childNum = this.getChildCategoryNum(r.getId());
                Long childFileNum = wxResService.getWxFileNum(r.getPath());
                r.setChildren(childNum.intValue());
                r.setLeafs(childFileNum.intValue());
            }
        }
        List<WxResourceCategory> firstCategtyList = new ArrayList<>();
        if (catRecycleBin.getId() != null) {
            listFirstCategory.remove(catRecycleBin);
            firstCategtyList.addAll(listFirstCategory);
            firstCategtyList.add(catRecycleBin);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("listFirstFolder", firstCategtyList);
        map.put("fileNum", allFileNum);
        return map;
    }

    @Override
    public HashMap<String, Object> getWxHomePageMes() {
        String hql = "from WxResourceCategory where parent is null and valid = 1 and isTrash = 0 order by createDate asc";
        List<WxResourceCategory> listFirstCategory = wxResCatDao.getListByHQLWithNamedParams(hql,null);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listFirstCategory", listFirstCategory);
        if (!listFirstCategory.isEmpty()) {
            for (WxResourceCategory c : listFirstCategory) {
                List<WxResourceCategory> listChildCategory = this.listChildCategory(c.getId());
                if (!listChildCategory.isEmpty()) {
                    map.put("childFolder",listChildCategory);
                }
                break;
            }
        }
        return map;
    }

    @Override
    public HashMap<String, Object> minidirectionAndChild(Integer categoryId) {
        WxResourceCategory wxResourceCategory = wxResCatDao.get(categoryId);
        Long childNum = this.getChildCategoryNum(categoryId);
        Long childFileNum = wxResService.getWxFileNum(wxResourceCategory.getPath());
        wxResourceCategory.setChildren(childNum.intValue());
        wxResourceCategory.setLeafs(childFileNum.intValue());
        HashMap<String, Object> map = null;
        if (childNum < 1) {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(20);
            pageInfo.setCurrentPageNo(1);
            map = wxResService.getWxFileByCategory(categoryId, pageInfo);
        } else {
            map = new HashMap<>();
            List<WxResourceCategory> listChildCategory = this.listChildCategory(categoryId);
            for(WxResourceCategory r : listChildCategory){
                Long acChildNum = this.getChildCategoryNum(r.getId());
                Long acChildFileNum = wxResService.getWxFileNum(r.getPath());
                r.setChildren(acChildNum.intValue());
                r.setLeafs(acChildFileNum.intValue());
            }
            map.put("childFolder", listChildCategory);
        }
        map.put("parentFolder", wxResourceCategory);
        return map;
    }

    @Override
    public HashMap<String, Object> getWxParentAndChild(Integer category) {
        List<WxResourceCategory> listChildCategory = this.listChildCategory(category);
        HashMap<String, Object> map;
        if (!listChildCategory.isEmpty()) {
            map = new HashMap<>();
            map.put("childFolder", listChildCategory);
        }else {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(20);
            pageInfo.setCurrentPageNo(1);
            map = wxResService.getWxFileByCategory(category, pageInfo);
        }
        return map;
    }

    @Override
    public String wxAllParentCatNameOrPath(Integer categoryId, String categoryName, String path, String type) {
        while (categoryId != null){
            WxResourceCategory wxCat = this.getWxResCatSingle(categoryId);
            if("1".equals(type)){
                categoryName = wxCat.getName() + "-" + categoryName;
            } else {
                path = wxCat.getId()+ "," + path;
            }
            categoryId = wxCat.getParent();
        }
        if("1".equals(type)){
            return  categoryName;
        }else {
            return  path;
        }
    }

    @Override
    public HashMap<String, Object> addWxDirectory(User user, Integer parent, String categoryName) {
        WxResourceCategory oldCat = this.checkCategoryName(parent,categoryName);
        String status = "1";
        HashMap<String, Object> map = new HashMap<>();
        if (oldCat == null) {
            if (parent != null) {
                status = wxResService.wxFileIsExistByCategory(parent);     //"1"代表没有文件继续进行，为2代表有文件，就等于2返回
            }
            if ("1".equals(status)) {
                WxResourceCategory category = new WxResourceCategory();
                /*WxApp wxApp = wxAppService.getWxAppSingle(user.getOid());
                category.setApp(wxApp.getId());
                category.setAppId(wxApp.getAppId());*/
                category.setOrg(user.getOid());
                category.setName(categoryName);
                category.setValid(true);
                category.setTrash(false);
                category.setParent(parent);
                category.setVersionNo(0);
                category.setCreator(user.getUserID());
                category.setCreateName(user.getUserName());
                category.setCreateDate(new Date());
                wxResCatDao.save(category);
                if (parent != null) {
                    String treePath = this.wxAllParentCatNameOrPath(parent,null,category.getId().toString(),"2");
                    category.setPath(treePath);
                }else {
                    category.setPath(category.getId().toString());
                }
                map.put("newCategory",category);
            }
        } else {
            status = "3";
        }
        map.put("status",status);
        return map;
    }

    @Override
    public HashMap<String, Object> upWxCategoryName(User user, Integer category, Integer parent, String categoryName) {
        WxResourceCategory checkCat = this.checkCategoryName(parent,categoryName);
        Integer state = 1;
        HashMap<String, Object> map = new HashMap<>();
        if (checkCat == null) {
            WxResourceCategory oldCat = this.getWxResCatSingle(category);
            List<WxResourceCategoryHistory> listWxCategoryHistory = this.listWxCategoryHistory(category);
            if (listWxCategoryHistory.isEmpty()) {
                WxResourceCategoryHistory catHis = new WxResourceCategoryHistory();
                catHis.setApp(oldCat.getApp());
                catHis.setAppId(oldCat.getAppId());
                catHis.setResourceCategory(oldCat.getId());
                catHis.setName(oldCat.getName());
                catHis.setValid(true);
                catHis.setParent(parent);
                catHis.setVersionNo(oldCat.getVersionNo());
                catHis.setCreator(oldCat.getCreator());
                catHis.setCreateName(oldCat.getCreateName());
                catHis.setCreateDate(oldCat.getCreateDate());
                wxResCatHisDao.save(catHis);
            }
            oldCat.setName(categoryName);
            oldCat.setVersionNo(oldCat.getVersionNo()+1);
            oldCat.setCreator(user.getCreator());
            oldCat.setCreateName(user.getUserName());
            oldCat.setCreateDate(new Date());
            WxResourceCategoryHistory catHisNew = new WxResourceCategoryHistory();
            catHisNew.setApp(oldCat.getApp());
            catHisNew.setAppId(oldCat.getAppId());
            catHisNew.setResourceCategory(oldCat.getId());
            catHisNew.setName(oldCat.getName());
            catHisNew.setValid(true);
            catHisNew.setParent(parent);
            catHisNew.setVersionNo(oldCat.getVersionNo());
            catHisNew.setCreator(oldCat.getCreator());
            catHisNew.setCreateName(oldCat.getCreateName());
            catHisNew.setCreateDate(oldCat.getCreateDate());
            wxResCatHisDao.save(catHisNew);
            map.put("category",oldCat);
        }else {
            state = 2;
        }
        map.put("state",state);
        return map;
    }

    @Override
    public List<WxResourceCategoryHistory> listWxCategoryHistory(Integer category) {
        String hql = "from WxResourceCategoryHistory where resourceCategory = :resourceCategory";
        HashMap<String, Object> param = new HashMap<>();
        param.put("resourceCategory", category);
        List<WxResourceCategoryHistory> list = wxResCatHisDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public Integer delWxCategory(Integer category) {
        WxResourceCategory cat = this.getWxResCatSingle(category);
        Integer state = 1;
        if (cat.getValid()) {
            Long childNum = this.getChildCategoryNum(category);
            if (childNum < 1) {
                String childFileStatus = wxResService.wxFileIsExistByCategory(category);
                if (childFileStatus.equals("1")) {
                    cat.setValid(false);
                }else {
                    state = 3;
                }
            }else {
                state = 3;
            }
        }else {
            state = 2;
        }
        return state;
    }

    @Override
    public WxResourceCategory wxTrashCategory(Integer org) {
        String hql = " from WxResourceCategory where org = :org and isTrash = 1";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        WxResourceCategory cat = (WxResourceCategory) wxResCatDao.getByHQLWithNamedParams(hql,param);
        return cat;
    }

    @Override
    public List<WxResourceCategory> addInitialDirectory(Integer org, String appId, String appname, String appsecret, String token, Boolean canpay, String module) {
        /*WxApp wxApp;
        if (appId != null && appname != null && appsecret != null && token != null && canpay != null && module != null) {
            wxApp = wxAppService.insertWxApp(org, appId, appname, appsecret, token, canpay, module);
        } else {
            wxApp = wxAppService.insertWxApp(org, "wxb8859c1edcdb4a8e", "贝塔小程序", "ee2aab2d67c8fbf3413be103dff09797", "wonderssResouce", false, "resource");
        }*/
        WxResourceCategory category = new WxResourceCategory();
        /*category.setApp(wxApp.getId());
        category.setAppId(wxApp.getAppId());*/
        category.setOrg(org);
        category.setName("已被停用的文章");
        category.setValid(true);
        category.setTrash(true);
        category.setCreateName("系统");
        category.setCreateDate(new Date());
        wxResCatDao.save(category);
        category.setPath(category.getId().toString());
        List<WxResourceCategory> list = new ArrayList<>();
        list.add(category);
        return list;
    }

    //获取某目录下子级目录的个数
    private Long getChildCategoryNum(Integer categoryId){
        String hql = "select count(id) from WxResourceCategory where valid = 1 and parent = :parent";
        HashMap<String, Object> param = new HashMap<>();
        param.put("parent", categoryId);
        Long num = (Long) wxResCatDao.getByHQLWithNamedParams(hql,param);
        return num;
    }

    //查询是否有名字重复的文件夹
    private WxResourceCategory checkCategoryName(Integer parent, String categoryName){
        String hql = "from WxResourceCategory where valid = 1 and name = :name ";
        HashMap<String, Object> param = new HashMap<>();
        param.put("name", categoryName);
        if (parent != null) {
            param.put("parent", parent);
            hql = hql + " and parent = :parent";
        }else {
            hql = hql + " and parent is null";
        }
        WxResourceCategory category = (WxResourceCategory) wxResCatDao.getByHQLWithNamedParams(hql,param);
        return category;
    }

    //获取某文件夹下的子级文件夹
    private List<WxResourceCategory> listChildCategory(Integer category){
        String hql = "from WxResourceCategory where parent = :parent and valid = 1 order by id asc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("parent", category);
        List<WxResourceCategory> listChildCategory = wxResCatDao.getListByHQLWithNamedParams(hql,param);
        return listChildCategory;
    }
}
