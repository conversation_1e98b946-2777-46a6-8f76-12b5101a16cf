package cn.sphd.miners.modules.commodity.entity;

import cn.sphd.miners.modules.material.entity.MtBase;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2017/7/24.
 * 商品包装表
 */
@Entity
@Table(name="t_pd_pack")
public class PdPack {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="outer_id"   , nullable=true , unique=false)
    private Integer outerId;

    @Column(name="outer_sn"  , length=50 , nullable=true , unique=false)
    private String outerSn;

    @Column(name="level"   , nullable=true , unique=false)
    private Integer level;

    @Column(name="manner"  , length=100 , nullable=true , unique=false)
    private String manner;

    @Column(name="amount"   , nullable=true , unique=false)
    private Integer amount;

    @Column(name="total_weight"   , nullable=true , unique=false)
    private java.math.BigDecimal totalWeight;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_customer",referencedColumnName = "id", nullable=true , unique=false , insertable=true, updatable=true)
    private PdMerchandise productCustomer;

    @Column(name="customer"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer productCustomer_;

    @ManyToOne (fetch=FetchType.EAGER)
    @JoinColumn(name="material", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MtBase material;

    @Column(name="material"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer material_;

    @JsonIgnore @JSONField(serialize = false)
    @ManyToOne (fetch=FetchType.LAZY )
    @JoinColumn(name="parent", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PdBase parent;

    @Column(name="parent" , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer parent_;

    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity=PdPack.class, fetch = FetchType.LAZY, mappedBy = "parent",cascade = CascadeType.REMOVE)
    private Set<PdPack> pdPackHashSet=new HashSet<PdPack>();


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOuterId() {
        return outerId;
    }

    public void setOuterId(Integer outerId) {
        this.outerId = outerId;
    }

    public String getOuterSn() {
        return outerSn;
    }

    public void setOuterSn(String outerSn) {
        this.outerSn = outerSn;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getManner() {
        return manner;
    }

    public void setManner(String manner) {
        this.manner = manner;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public PdMerchandise getProductCustomer() {
        return productCustomer;
    }

    public void setProductCustomer(PdMerchandise productCustomer) {
        this.productCustomer = productCustomer;
    }

    public Integer getProductCustomer_() {
        return productCustomer_;
    }

    public void setProductCustomer_(Integer productCustomer_) {
        this.productCustomer_ = productCustomer_;
    }

    public MtBase getMaterial() {
        return material;
    }

    public void setMaterial(MtBase material) {
        this.material = material;
    }

    public Integer getMaterial_() {
        return material_;
    }

    public void setMaterial_(Integer material_) {
        this.material_ = material_;
    }

    public PdBase getParent() {
        return parent;
    }

    public void setParent(PdBase parent) {
        this.parent = parent;
    }

    public Integer getParent_() {
        return parent_;
    }

    public void setParent_(Integer parent_) {
        this.parent_ = parent_;
    }

    public Set<PdPack> getPdPackHashSet() {
        return pdPackHashSet;
    }

    public void setPdPackHashSet(Set<PdPack> pdPackHashSet) {
        this.pdPackHashSet = pdPackHashSet;
    }


}
