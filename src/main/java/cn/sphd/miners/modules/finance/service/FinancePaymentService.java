package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.FinancePayment;
import cn.sphd.miners.modules.finance.entity.FinancePaymentHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * lyx 2021/6/22
 */
public interface FinancePaymentService{

    FinancePayment addFinancePayment(Integer business,String businessType, BigDecimal amount, BigDecimal planAmount,BigDecimal factAmount,User user, String method, Date planDate, Date factDate, Integer accountId,Integer accountBill,String summary);

    List<FinancePaymentHistory> getPaymentHistorysByPaymentId(Integer financePaymentId);

    FinancePaymentHistory getPaymentHistoryByPaymentId(Integer financePaymentId);

    FinancePayment getPaymentByBusiness(Integer id,Integer business,String businessType);

    List<FinancePaymentHistory> getPaymentHistoryByBusiness(Integer business,Integer financePayment,String businessType);

    FinancePaymentHistory addPaymentHistory(FinancePayment financePayment,User user);
    }
