import auth from "@/sys/auth";
import * as api from "@/api/equipMent";
import {validatePostCode} from "@/utils/formTest";

export default {
    name:'eqSupplierMixin',
    components:{},
    data(){
        return {
            supBack: '',
            searchVal: '',
            indexPage: {},
            supplierUsingList: [],
            supplierSuspendedList: [],
            supplierSearchList: [],
            eqListCount:[],
            addData:{
                visible: false,
                chanNum: 0,
                quanNum: 0,
                chanSum: 9,
                quanSum: 1,
                base:{
                    quanImg: [],
                    memo:'',
                },
                contract:{
                    tableData:[{
                        no:'',
                        date:'',
                        expDur:'',
                        info:'',
                    }],
                },
                mail:{
                    tableData:[{
                        no:'',
                        address:'',
                        recivePerson:'',
                        mailNo:'',
                        tel:'',
                        info:'',
                    }],
                },
                contactLianxi:{
                    tableData:[{
                        name:'',
                        post:'',
                        info:'',
                    }],
                },
            },
            rootCusBtn: '',
            susPage: {},
            mailInfoData:{
                visible:false,
                editMailIndex:'',
                type:'',
                mailValidSta:false,
                address:'',
                code: '',
                contact: { name:'' },

            },
            chooseCusContactData:{
                visible:false,
                type:'',
                contactList:[{ id:1, name:'联系人' }],
                selectConact: ''
            },
            editContactLianXiData:{
                typeStr:'', // 哪里的新增联系人按钮
                visible:false,
                tag: '' ,
                editIndex: '' ,
                showOptions: false ,
                loading:false,
                loadingBtn:'点击此处上传名片',
                name:'',
                postName:'',
                tel:'',
                defineType:'',
                type:'',
                typeList:[
                    { label: '手机', value:1 ,disabled: false },
                    { label: 'QQ', value:2 ,disabled: false },
                    { label: 'Email', value:3 ,disabled: false },
                    { label: '微信', value:4 ,disabled: false },
                    { label: '微博', value:5 ,disabled: false },
                    { label: '自定义', value:9 ,disabled: false },
                ],
                contactArr:[],
                contactList:[],
                picInfo:{}
            },
            useDefinedLabelData:{
                visible: false,
                val:'',
            },
            scanSupplierLog: false,
            seeSupplierData:{
                visible : false ,
                info:{
                    yjAddressList: [],
                    contactsList:[]
                },
                contractBaseList:[],
            },
            updateRecordsData:{
                visible: false,
                type: '',
                title: '',
                recordTip: '',
                recordEditer: '',
                list: [],
            },
            supplierManageData:{
                visible : false ,
                editOrg:{},
                info: {} ,
                base: {} ,
                adressList: [] ,
                areaList: [] ,
            },
            contractEndData:{
                visible : false ,
                contractBaseList: [] ,
            },
            contractStopData:{
                visible : false ,
                contractBaseList: [] ,
            },
            stopAdresstData:{
                visible1: false,
                editObj: null,
            },
            stopContactData:{
                visible: false,
                editObj:null,
            },
            manageBaseData:{},
            manageBaseDataLog: false,
            paulsCusData:{
                visible: false,
                editObj: '',
                state: ''
            },
            delCusData:{
                visible:false,
                editObj:''
            },
            manageInvoiceData:{
                visible:false,
                info: {}
            },
            supplierItem:{},
            baseRecordScanData:{
                visible:false,
                info: {}
            },
            invoiceRecordScanData:{
                visible:false,
                info: {}
            },
            mailRecordScanData:{
                visible:false,
                info: {}
            },
            contactUpdateLogDetailsData:{
                visible : false ,
                title : '' ,
                tel : '' ,
                postName : '' ,
                name : '' ,
                contactList : [] ,
                picInfo:{ src:'' },
            },
            contactDetailsData:{
                visible : false ,
                title : '' ,
                tel : '' ,
                postName : '' ,
                name : '' ,
                contactList : [] ,
                picInfo:{ src:'' },
            },
            suspendRecordData:{
                visible: false,
                list: [],
            },
            stopShAdressData:{
                visible: false,
                list: [],
            },
            contactStopData:{
                visible : false,
                list : false,
            },
            contractScanData:{
                visible: false,
                editContract:{},
                contractBase:{},
                opacity: false,
            },
            contactCard:{
                visible: false,
                visitCard: ''
            },
            rootPath:''
        }
    },
    created(){},
    mounted(){},
    computed: {
        editContractTitle() {
            let arr = ['新增合同', '修改合同', '续约合同']
            return arr[this.form_editContract.editType - 1]
        },
        addDataOkBtn(){
            let status = false
            if(this.addData.base.fullName && this.addData.base.name && this.addData.base.codeName){ status = true; }
            return status
        },
        manageBaseOkBtn(){
            let status = false
            if(this.manageBaseData.fullName ){ status = true; }
            return status
        },
        mailInfoOkBtn(){
            let status = false
            if(this.mailInfoData.address && this.mailInfoData.code && this.mailInfoData.contact.name){ status = true; }
            return status
        },
        editContactLianXiOkStatus(){
            let status = false
            if(this.editContactLianXiData.name){ status = true; }
            return status
        },
        contactLianxiTwo(){
            let newList = []
            let twoItem = {}
            let list = this.addData.contactLianxi.tableData
            list.forEach((item,index) =>{
                if(index%2 === 0){
                    twoItem = { name:item.name, post:item.post, mobile:item.mobile, index1: index, info1: item.info }
                    if(index+1 === list.length){
                        newList.push(twoItem)
                    }
                }else{
                    twoItem.name2 = item.name
                    twoItem.post2 = item.post
                    twoItem.info2 = item.info
                    twoItem.mobile2 = item.mobile
                    twoItem.index2 = index
                    newList.push(twoItem)
                }
            })
            return newList
        },
        acceptable(){
            let str1 = this.acceptableStr(this.seeSupplierData.info)
            return str1
        },
        record_acceptable(){
            let str1 = this.acceptableStr(this.baseRecordScanData.info)
            return str1
        },
        invoicableInfo(){
            var str2 = this.invoicableInfoStr(this.seeSupplierData.info)
            return str2
        },
        record_invoicableInfo(){
            var str2 = this.invoicableInfoStr(this.invoiceRecordScanData.info)
            return str2
        }
    },
    methods:{
        initIndex() {
            this.mainNum = 'sup1'
            this.supBack = 'sup1'
            this.getEqSupplier(1,20);
            let rootPath = localStorage.getItem('rootPath')
            this.rootPath = JSON.parse(rootPath)
        },
        searchEqSupplier() {
            this.mainNum = 'sup3'
            this.supBack = 'sup3'
            var keyword = this.searchVal
            this.getEqSupplierBySearch(1,20,keyword)
        },
        getEqSupplierBySearch(currentPageNo,pageSize,keyword) {
            let param = {
                "currentPageNo": currentPageNo,
                "pageSize": pageSize,
                "keyword":keyword,
                'type': 2
            }
            api.searchSupplier(param)
                .then(res => {
                    let list = res.data.data["list"] || [];
                    this.indexPage = res.data.data.pageInfo || {}
                    this.supplierSearchList = list
                })
        },
        importBtn(){
            this.$message({
                'offset':'26',
                'message': "功能完善中，敬请期待。"
            })
        },
        addSupplierInfo() {
            this.addData.visible = true
            this.addDataInit()
            this.rootCusBtn = 'add'
        },
        addDataInit(){
            this.addData.base = {
                quanImg: [],
                memo:'',
            }
            this.addData.contract.tableData = []
            this.addData.mail.tableData = []
            this.addData.contactLianxi.tableData = []
        },
        addSupplierDataOk(){
            if(this.addDataOkBtn){
                let params = {
                    'type': 2
                }
                params.fullName = this.addData.base.fullName
                params.codeName = this.addData.base.codeName
                params.name = this.addData.base.name
                params.memo = this.addData.base.memo
                params.chargeAcceptable = this.addData.base.chargeAcceptable
                params.chargeBegin = this.addData.base.chargeBegin
                params.chargePeriod = this.addData.base.chargePeriod
                params.isImprest = this.addData.base.isImprest
                params.invoicable = this.addData.base.invoicable
                params.vatsPayable = this.addData.base.vatsPayable
                params.draftAcceptable = this.addData.base.draftAcceptable
                params.taxRate = this.addData.base.taxRate
                params.imprestProportion = this.addData.base.imprestProportion

                if( params.name.length == 0){
                    this.$message.error('请填写供应商名称')
                    return false;
                }else if( params.fullName.length == 0){
                    this.$message.error('请填写供应商简称')
                    return false;
                }else if( params.codeName.length == 0){
                    this.$message.error('请填写供应商代号')
                    return false;
                }else if(params.invoicable == null || params.invoicable == "") {
                    this.$message.error('请选择‘该供应商是否能开发票')
                    return false;
                }else if(params.invoicable == 1 && params.vatsPayable == 1){
                    if(params.taxRate == null || params.taxRate == "" || params.taxRate == "null" || params.taxRate == undefined){
                        this.$message.error('请录入税率')
                        return false;
                    }
                }else if(params.isImprest == 1){    //是否需要预付款       下面两个必须选一个（预付款比例和预付款比例不确定）
                    if(params.imprestProportion == null || params.imprestProportion == ""){ //预付款比例
                        if(params.uncertainty == null || params.uncertainty == ""){ //预付款比例不确定
                            this.$message.error('请填写预付款比例')
                            return false;
                        }
                    }
                }
                if(params.invoicable == "1"){
                    if(params.vatsPayable == ""){
                        params.vatsPayable = "1";
                    }else if(params.vatsPayable == "2"){
                        params.vatsPayable = "0";
                    }
                }
                let qImages = []
                this.addData.base.quanImg.forEach(qImg=>{
                    qImages.push({ normal:qImg.filename  })
                })
                params.qImages = JSON.stringify(qImages); // 全景

                let contractList = []; //合同
                this.addData.contract.tableData.forEach(contract=>{
                    let infoc = contract
                    let imgs = []
                    infoc.contractBaseImages.forEach((im) => {
                        imgs.push({
                            url:  auth.webRoot + '/upload/' + im.uplaodPath,
                            uplaodPath: im.normalPath,
                            type: 1,
                            title: im.name,
                            operation: 1
                        })
                    })
                    let item = {
                        sn: infoc.sn,
                        contractSignTime: infoc.signTime,
                        contractStartTime:infoc.validTime?.[0],
                        contractEndTime:infoc.validTime?.[1],
                        memo: infoc.memo,
                        contractBaseImages: imgs
                    }

                    if (infoc.fileList.length > 0) {
                        item.filePath = infoc.fileList[0].normalPath
                        item.fileName = infoc.fileList[0].name
                    }
                    contractList.push(item)
                })
                params.contractBaseList = JSON.stringify(contractList)
                let mailList = []
                this.addData.mail.tableData.forEach(mailI=>{
                    let item={
                        //number: this.getFive(),
                        name: mailI.name ,
                        address: mailI.address ,
                        contact:mailI.recivePerson ,
                        postcode:mailI.mailNo ,
                        mobile:mailI.tel ,
                        supplierContact:mailI.contact.id ,
                    }
                    mailList.push(item)
                })
                params.yjAddressList = JSON.stringify(mailList)
                let contactsList = []
                this.addData.contactLianxi.tableData.forEach(lxI=>{
                    let socialList = []
                    lxI.info.contactArr.forEach(cc=>{
                        socialList.push({
                            code: cc.val,
                            type: cc.value,
                            name: cc.name,
                        })
                    })
                    let item = {
                        memo: lxI.info.tag,
                        name: lxI.name,
                        post: lxI.post,
                        mobile: lxI.info.tel,
                        cardPath: lxI.info.picInfo.filename ,
                        socialList: socialList,
                        id: lxI.id
                    }
                    contactsList.push(item)
                })
                params.contactsList = JSON.stringify(contactsList)
                api.addSupplierDo(params).then(res => {
                    if(res.data.success === 1){
                        this.$message({
                            'type':'success',
                            'offset':'26',
                            'message': "新增成功"
                        })
                        this.addData.visible = false
                        this.getEqSupplier(1,20);
                    }else{
                        this.$message({
                            'type':'error',
                            'offset':'26',
                            'message': "新增失败！"
                        })
                    }
                }).catch(err=>{
                    console.log('err=', err)
                })
            }
        },
        //点击“已暂停采购的供应商”按钮
        suspendedSupplier(cur) {
            this.mainNum = 'sup2'
            this.supBack = 'sup2'
            let param = {
                "currentPageNo": cur,
                "pageSize": 20,
                'type': 2
            }
            api.getSuspendedList(param)
                .then(res => {
                    let list = res.data.data["list"] || [];
                    this.susPage = res.data.data.pageInfo || {}
                    this.supplierSuspendedList = list
                })
        },
        susPageClick(pageItem){
            this.suspendedSupplier(pageItem.page)
        },
        lookSupplier(info){
            this.scanSupplierLog = true
            this.rootCusBtn = 'scan'
            let lookid = info.id;
            if(lookid == undefined || lookid == ""){
                this.$message({
                    'type':'error',
                    'offset':'26',
                    'message': "加载错误，请稍后再试。"
                })
                return false;
            }
            this.getContract(info.id, 1)
            api.getSupplierData({id:lookid})
                .then(res => {
                    let data = res.data.data
                    this.seeSupplierData.info = data
                    this.seeSupplierData.info.contactLianxiTwo = []
                    if(data.contactsList.length >0){
                        let newList = []
                        let twoItem = {}
                        let list = data.contactsList
                        list.forEach((item, index)=>{
                            if(index%2 === 0){
                                twoItem = { name:item.name, post:item.post, mobile:item.mobile, index1: index, info1: item }
                                if(index+1 === list.length){
                                    newList.push(twoItem)
                                }
                            }else{
                                twoItem.name2 = item.name
                                twoItem.post2 = item.post
                                twoItem.mobile2 = item.mobile
                                twoItem.info2 = item
                                twoItem.index2 = index
                                newList.push(twoItem)
                            }
                        })
                        this.seeSupplierData.info.contactLianxiTwo = newList
                        console.log('newList=', newList)
                    }
                })
        },
        getContract(id, type) {
            let param = {
                supplierId: id, //供应商id
                type: type  //1-正常 2-终止 3-到期
            }
            api.getContract(param)
                .then(res => {
                    var state = res.data.success;
                    if(state === "1"|| state === 1) {
                        this.seeSupplierData.contractBaseList = res.data.data.contractBaseList || [] // 合同
                    }
                })
        },
        showEqMsg(info) {
            let param = { supplier: info.id, pageSize:9999, currentPageNo:1 }
            this.supplierItem = info
            api.getEqList(param).then(result=> {
                let list = result.data.data || [];
                this.eqListCount = list
                this.mainNum = 'sup4'
            })
        },
        invoiceManageUpdate(){
            this.manageInvoiceData.visible = true
            this.manageInvoiceData.rootType = 'update'
            api.getInvoiceInfo({ supplierId: this.supplierManageData.editOrg.id}).then(result=> {
                this.manageInvoiceData.info = result.data
                this.manageInvoiceData.info.draftAcceptable = result.data.draftAcceptable.toString()
            })
        },
        manageInvoiceOk(){
            let info = this.manageInvoiceData.info
            let params = {
                supplierId: this.supplierManageData.editOrg.id,
                invoicable: info.invoicable,//能否开发票
                vatsPayable: info.vatsPayable,//能否开增值税发票
                draftAcceptable: info.draftAcceptable,//可否接受汇票
                taxRate: info.taxRate,//税率
            }
            if(params.invoicable == null || params.invoicable == "") {
                this.$message.error('请选择‘该供应商是否能开发票')
                return false;
            }else if(params.invoicable == 1 && params.vatsPayable == 1){
                if(params.taxRate == null || params.taxRate == "" || params.taxRate == "null" || params.taxRate == undefined){
                    this.$message.error('请录入税率')
                    return false;
                }
                if(params.draftAcceptable == null || params.draftAcceptable == ""){
                    this.$message.error('请选择是否接收汇票')
                    return false;
                }
            }
            if(params.invoicable == "1"){
                if(params.vatsPayable == ""){
                    params.vatsPayable = "1";
                }else if(params.vatsPayable == "2"){
                    params.vatsPayable = "0";
                }
            }
            api.updateInvoiceData(params).then(() => {
                this.manageInvoiceData.visible = false
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        acceptableStr(info){
            let str1 = ``
            if(info.chargeAcceptable == "1"){
                str1 += `可接受挂账,`
                if(info.chargePeriod){
                    str1 += `账期` + info.chargePeriod+ `天,`
                }
                if(info.chargeBegin == "1"){
                    str1 += "自货物入库之日起计算,";
                }else if(info.chargeBegin == "2"){
                    str1 += "自发票入账之日起计算,";
                }
            } else {
                str1 += "不接受挂账,";
            }
            if(info.isImprest == "1"){
                str1 += "需要预付款.";
            }else if(info.isImprest == "2"){
                str1 += "不需要预付款.";
            }else {
                str1 += "不确定预付款.";
            }
            return str1
        },
        invoicableInfoStr(info){
            var str2 ="";//draftAcceptable
            let draftAcceptable = Number( info.draftAcceptable );
            if(info.invoicable == "1"){
                if(info.vatsPayable == "1"){
                    if(info.taxRate){
                        str2 += "该供应商能开税率为"+ info.taxRate +"%的增值税专用发票,";
                    }else{
                        str2 += "该供应商能开增值税专用发票,";
                    }
                }else{
                    str2 += "该供应商仅能开普通发票,";
                }
                if(draftAcceptable === 0){
                    str2 += "不确定能接受承兑汇票.";
                }else {
                    str2 += "可接收承兑汇票.";
                }
            }else{
                str2 += "该供应商不能开发票.";
            }
            return str2
        },
        initPage(type){
            if(type === 1) {
                this.getlistContractBase(this.supplierManageData.editOrg.id, 1)
            }if(type === 2) {
                this.seeStopContract()
            }
        },
        supplierManage(info){
            this.rootCusBtn = 'update'
            this.supplierManageData.editOrg = info
            this.getSupplierManageData(info.id)
            this.getlistContractBase(info.id, 1)
        },
        delSupplier(item){
            this.delCusData.visible = true
            this.delCusData.editObj = item
        },
        stopSupplier(item , type){
            this.paulsCusData.visible = true
            this.paulsCusData.editObj = item
            this.paulsCusData.state = type
            this.paulsCusData.type = type
        },
        getEqSupplier(currentPageNo, pageSize, keyword) {
            let param = {
                "currentPageNo": currentPageNo,
                "pageSize": pageSize,
                'type': 2
            }
            // 获得供应商列表
            api.getSupplierList(param)
                .then(res => {
                    let list = res.data.data["list"] || [];
                    this.indexPage = res.data.data.pageInfo || {}
                    this.supplierUsingList = list
                })
        },
        getSupplierManageData(id){
            api.getSupplierData({ id: id  }).then(res1=>{
                let res = res1.data
                this.supplierManageData.visible = true
                this.supplierManageData.info = res.data
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        getlistContractBase(supId, type){
            // 获取管理客户的合同列表
            let param = {
                supplierId: supId, //供应商id
                type: type  //1-正常 2-终止 3-到期
            }
            api.getContract(param).then(res1=>{
                let res = res1.data
                let list = res.data.contractBaseList
                if (type === 1) {
                    this.supplierManageData.contractBaseList = list
                } else if (type === 3) {
                    this.contractEndData.contractBaseList = list
                } else if (type === 2) {
                    this.contractStopData.contractBaseList = list
                }
            }).catch(err=>{
                console.log('err=',err)
            })
        },
        qjPicSuccess(file, files) {
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            this.addData.base.quanImg.push(imgItem)
        },
        qjPicSuccess_manage(file, files, ofile, totalFiles){
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            this.manageBaseData.quanImg.push(imgItem)
        },
        uncertainty(){
            if (this.rootCusBtn === 'add') {
                if (this.addData.base.imprestProportion === '0') {
                    this.addData.base.imprestProportion = ''
                }
            } else {
                if (this.manageBaseData.imprestProportion === '0') {
                    this.manageBaseData.imprestProportion = ''
                }
            }

        },
        addContractInfo(type,info, index){
            this.form_editContract.editType = 1
            if (this.rootCusBtn === 'add') {
                if (type === 'add') {
                    this.resetEditContractForm()
                } else if (type === 'update') {
                    const temp = JSON.parse(JSON.stringify(info))
                    this.form_editContract = {...temp}
                    this.form_editContract.editContractIndex = index
                }
            }
            this.form_editContract.supInfo = {}
            if (this.rootCusBtn === 'update') {
                this.form_editContract.supInfo = this.supplierManageData.editOrg
                this.form_editContract.editType = 2
                this.initContract(1)
            }
            this.form_editContract.type = type
            this.addPurContractLog = true
        },
        addPurContractMid(){
            let contractItem = JSON.parse(JSON.stringify(this.form_editContract))
            if (this.rootCusBtn === 'add') {
                this.$refs['editContractForm'].validate((valid) => {
                    if (valid) {
                        if (this.form_editContract.type === 'add') { // 新增供应商-新增合同
                            this.addData.contract.tableData.push(contractItem)
                        } else if (this.form_editContract.type === 'update') { // 新增供应商-修改合同
                            this.addData.contract.tableData[this.form_editContract.editContractIndex] = contractItem
                        }
                        this.addPurContractLog = false
                    }
                })
            } else {
                this.page = 'seeContractBySup'
                this.addPurContractSure()
            }
        },
        updateContract(item, index){
            this.addContractInfo('update', item, index)
        },
        delContract(item, index){
            this.addData.contract.tableData.splice(index, 1)
        },
        addMailBtn(){
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'add'
            this.mailInfoData.title = '新增邮寄信息'
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = ''
            this.mailInfoData.code = ''
            this.mailInfoData.contact = {}
        },

        mailInfoOk(){
            if(this.mailInfoOkBtn){
                if(this.rootCusBtn === 'add'){ // 新增客户
                    let item = {
                        address: this.mailInfoData.address ,
                        recivePerson: this.mailInfoData.contact.name ,
                        mailNo: this.mailInfoData.code ,
                        tel:this.mailInfoData.contact.info.tel ,
                        contact: this.mailInfoData.contact,
                    }
                    if(this.mailInfoData.type === 'add'){
                        this.addData.mail.tableData.push(item)
                    }else if(this.mailInfoData.type === 'update'){
                        this.addData.mail.tableData[this.mailInfoData.editMailIndex] = item
                    }
                }else if(this.rootCusBtn === 'update') { // 修改客户
                    let param = {}
                    let data = {
                        supplierContact: this.mailInfoData.contact.id,
                        address: this.mailInfoData.address ,
                        contact: this.mailInfoData.contact.name ,
                        postcode: this.mailInfoData.code ,
                        mobile: this.mailInfoData.contact.mobile
                    }
                    let url = ``
                    if (this.mailInfoData.type === 'manageAdd') {
                        param.supplierId = this.supplierManageData.editOrg.id
                        param.address = JSON.stringify(data)
                        url = "../supplier/addSupplierAddress.do"
                    } else if (this.mailInfoData.type === 'manageUpdate') {
                        data.id = this.mailInfoData.editObj.id
                        param = data
                        url = "../supplier/updateSupplierAddress.do"
                    }
                    api.manageUpdateAddress(param, url).then(res1=>{
                        let status = res1.data.success;
                        if(status === '1'|| status === 1){
                            this.getSupplierManageData(this.supplierManageData.editOrg.id)
                        }else{
                            this.$message.error('修改失败')
                        }
                    }).catch(err=>{
                        console.log('err=', err)
                    })
                }
                this.mailInfoData.visible = false
            }
        },
        delMail(item, index){
            this.addData.mail.tableData.splice(index,1)
        },
        updateMail(item, index){
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'update'
            this.mailInfoData.title = '修改邮寄信息'
            this.mailInfoData.editMailIndex = index
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = item.address
            this.mailInfoData.code = item.mailNo
            this.mailInfoData.contact = item.contact
        },
        showContact(type){
            if(this.rootCusBtn === 'update'){ // 管理
                this.chooseCusContactData.contactList = this.supplierManageData.info.contactsList
            }else if(this.rootCusBtn === 'add'){
                this.chooseCusContactData.contactList = this.addData.contactLianxi.tableData
            }
            if(this.chooseCusContactData.contactList.length === 0){
                this.$message.info('暂无客户数据，请通过新增添加！')
            }else{
                this.chooseCusContactData.visible = true
                this.chooseCusContactData.type = type
                this.chooseCusContactData.selectConact = {}
                let selectConact = null
                switch (type) {
                    case 'mailInfo' :
                        selectConact = this.mailInfoData.contact
                        break
                    default:
                }
                this.chooseCusContactData.selectConact = selectConact
            }
        },
        chooseCusContactOk(){
            if(this.chooseCusContactData.selectConact.id){
                switch (this.chooseCusContactData.type){
                    case 'mailInfo': // 新增邮寄地址
                        this.mailInfoData.contact = this.chooseCusContactData.selectConact
                        break
                    default:
                        console.log('没有找到对应的type')
                }
                this.chooseCusContactData.visible = false
            }else{
                this.$message.error('请先选择联系人')
            }

        },
        showOptionsFun(){
            this.editContactLianXiData.showOptions = true
            this.editContactLianXiData.defineType = ''
        },
        useDefinedLabelOk(){
            let indexI = this.editContactLianXiData.defineType
            let type = this.editContactLianXiData.typeList[indexI]
            let newItem = {
                name: this.useDefinedLabelData.val,
                value:type.value,
                val:''
            }
            // let newItem = {
            //     code: this.useDefinedLabelData.val,
            //     type:type.value,
            //     name:''
            // }
            this.editContactLianXiData.contactArr.push(newItem)
            this.setContactList()
            this.editContactLianXiData.showOptions = false
            this.editContactLianXiData.typeList[indexI].disabled = true
            this.useDefinedLabelData.visible = false
        },
        beforeUploadLianXi(){
            this.editContactLianXiData.loading = true
            this.editContactLianXiData.loadingBtn = ''
        },
        fileSuccessLianXi(file, files, ofile, totalFiles){
            file.src = this.rootPath.fileUrl + file.filename
            this.editContactLianXiData.picInfo = file
            console.log('file=', file)
            this.editContactLianXiData.loading = false
            this.editContactLianXiData.loadingBtn = '点击此处上传名片'

        },
        delPic(){
            this.editContactLianXiData.picInfo = {}
        },
        delLink(contact, type){
            let value = contact['value' + type]
            let index = contact['index'+ type]
            this.editContactLianXiData.typeList.forEach(typeItem => {
                if(typeItem.value == value){
                    console.log('找到了嘛')
                    typeItem.disabled = false
                }
            })
            this.editContactLianXiData.contactArr.splice(index, 1)
            this.setContactList()
        },
        setLink(){
            let indexI = this.editContactLianXiData.defineType
            let type = this.editContactLianXiData.typeList[indexI]
            this.editContactLianXiData.showOptions = false

            if(type.value === 9){
                this.useDefinedLabelData.visible = true
            }else{
                let newItem = { name: type.label, value:type.value, val:''  }
                this.editContactLianXiData.contactArr.push(newItem)
                this.setContactList()
                this.editContactLianXiData.typeList[indexI].disabled = true
            }
        },
        setContactList(){
            let c = this.editContactLianXiData.contactArr, list = []
            c.forEach((linkI, index0)=>{
                let yu = index0%2
                if(yu === 0){
                    list.push({
                        name:linkI.name, val:linkI.val, value1: linkI.value, index1: index0,
                        name2:'', val2:"", value2: '' , index2: '',
                    })
                }else{
                    let latI = list[list.length-1]
                    latI.name2 = linkI.name
                    latI.val2 = linkI.val
                    latI.value2 = linkI.value
                    latI.index2 = index0
                }
            })
            this.editContactLianXiData.contactList = list
        },
        hideUseDefinedLabel(){
            this.useDefinedLabelData.visible = false
        },

        clickItem(Conac){
            if(this.chooseCusContactData.selectConact && this.chooseCusContactData.selectConact.id == Conac.id){
                this.chooseCusContactData.selectConact = null
            }else{
                this.chooseCusContactData.selectConact = Conac
            }
        },
        hideEditContactLianXiFun(){
            this.editContactLianXiData.visible = false
        },
        addContact(typeStr){
            this.editContactLianXiData = {
                typeStr: typeStr,
                visible:true,
                showOptions:false,
                loading:false,
                loadingBtn:'点击此处上传名片',
                name:'',
                postName:'',
                type:'add',
                title: '新增联系人',
                typeList:[
                    { label: '手机', value:1 ,disabled: false },
                    { label: 'QQ', value:2 ,disabled: false },
                    { label: 'Email', value:3 ,disabled: false },
                    { label: '微信', value:4 ,disabled: false },
                    { label: '微博', value:5 ,disabled: false },
                    { label: '自定义', value:9 ,disabled: false },
                ],
                contactArr:[],
                contactList:[],
                picInfo:{}
            }
            if(typeStr === 'mailInfo'){
                this.editContactLianXiData.tag = '联系人'
            } else if(typeStr === 'contact'){
                this.editContactLianXiData.tag = '其他'
            }
        },
        addContactVal(val, num){
            this.editContactLianXiData.contactArr[num].val = val
        },
        updateContact(item, type){
            let info = item['info'+ type]
            let index = item['index'+ type]
            this.editContactLianXiData.visible = true
            this.editContactLianXiData.editIndex = index
            this.editContactLianXiData.type = 'update'
            this.editContactLianXiData.title = '修改联系人'
            this.editContactLianXiData.showOptions = false
            this.editContactLianXiData.loading =  false
            this.editContactLianXiData.loadingBtn = '点击此处上传名片'
            this.editContactLianXiData.name = info.name
            this.editContactLianXiData.postName = info.postName
            this.editContactLianXiData.tel = info.tel
            this.editContactLianXiData.typeStr = info.typeStr
            this.editContactLianXiData.tag = info.tag
            this.editContactLianXiData.newId = info.newId
            this.editContactLianXiData.contactArr = info.contactArr
            this.editContactLianXiData.picInfo = info.picInfo
            this.editContactLianXiData.typeList = info.typeList
            this.editContactLianXiData.contactList = info.contactList
        },
        delContact(item, type){
            let index = item['index'+ type]
            this.addData.contactLianxi.tableData.splice(index,1)
        },
        chargeCode(){
            this.mailInfoData.mailValidSta = validatePostCode(this.mailInfoData.code)
        },
        getSuspendRecordList(){
            let customerId = this.seeSupplierData.info.id
            api.getSuspendList({ id: customerId }).then(res1=>{
                let res = res1.data
                let data = res.data
                this.suspendRecordData.visible = true
                let list = []
                data.forEach(item=>{
                    let iit = {
                        handleTxt: item.enabled == '1'?'恢复合作':'暂停合作',
                        handler: item.updateName + ' ' + (new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss'))
                    }
                    list.push(iit)
                })
                this.suspendRecordData.list = list

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hideSuspendRecord(){
            this.suspendRecordData.visible = false
        },
        getRecordList(str,info){
            let id = '', url = ''
            this.updateRecordsData.type = str
            switch (str){
                case "scanBase":
                    this.updateRecordsData.title = '基本信息修改记录'
                    url = "../supplier/getRecordBaseList.do"
                    break;
                case "invoice":
                    this.updateRecordsData.title = '开票信息修改记录'
                    url = '../supplier/getRecordInvoiceList.do'
                    break;
                case "mail":
                    this.updateRecordsData.title = '邮寄地址修改记录'
                    url = '../supplier/getRecordAddressList.do'
                    break;
                case "contact":
                case "contactManage":
                    this.updateRecordsData.title = '联系人修改记录'
                    url = '../supplier/getRecordContactList.do'
                    break;
                default:
                    console.log('没找到str=', str)
            }
            if (str === 'scanBase' || str === 'invoice') {
                if (this.rootCusBtn === 'scan') {
                    id = this.seeSupplierData.info.id
                } else if (this.rootCusBtn === 'update') {
                    id = this.supplierManageData.editOrg.id
                }
            } else if (str === 'contact'){
                id = info.info1.id
            } else {
                id = info.id
            }
            this.getRecordBaseListFun(id, url)
        },
        getRecordBaseListFun(customerId, url){
            api.getRecordBaseList({ id: customerId }, url).then(res1=>{
                let res =  res1.data.data
                let status = res1.data.success;
                if (status == 1) {
                    this.setRecordsList(res);
                } else {
                    this.$message.error("查看失败！")
                }

            }).catch(err=>{
                console.log('err=',err)
            })
        },
        setRecordsList(data){
            this.updateRecordsData.visible = true
            let list = data.list
            this.updateRecordsData.list = []
            if(list.length > 0){
                this.updateRecordsData.recordTip = '当前数据为第' + (list.length -1) + '次修改后的结果。'
                this.updateRecordsData.recordEditer = "修改时间：" + data.updateName + '&nbsp;&nbsp;' + ( new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss') )
                let recordList = []
                list.forEach((item, index)=>{
                    let iitem={
                        ...item,
                        infoTxt: index === 0 ? '原始信息' : `第${ index }次修改后` ,
                        handler: index === 0 ? `${ item.createName } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')  }` : `${ item.updateName } ${ new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss')  }`  ,
                    }
                    recordList.push(iitem)
                })
                this.updateRecordsData.list = recordList
            }else{
                this.updateRecordsData.recordTip = "当前资料未经修改"
                this.updateRecordsData.recordEditer = "创建人：" + data.createName + '&nbsp;&nbsp;' + ( new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss') )
            }
        },
        cus_recordDetail(info, icon){
            let type = this.updateRecordsData.type
            let url = ``
            let front = icon ? icon -1 : 0;
            let ttl = icon ? `第${icon}次修改后` : '原始信息';
            let param = {'id': info.id, 'front': this.updateRecordsData.list[front].id}
            switch (type){
                case "scanBase":
                    url = "../supplier/getRecordBaseDetails.do"
                    break;
                case "invoice":
                    url = '../supplier/getRecordInvoiceDetails.do'
                    break;
                case "mail":
                    url = '../supplier/getRecordShAddressDetails.do'
                    break;
                case "contact":
                case "contactManage":
                    url = '../supplier/getRecordContactDetails.do'
                    break;
                default:
                    console.log('没找到str=', type)
                    break
            }
            api.getRecordDetails(param, url)
                .then(res => {
                    let nowData = res.data.data.now
                    let frontData = res.data.data.front
                    switch (type){
                        case "scanBase":
                            this.baseRecordScanData.visible = true
                            this.baseRecordScanData.info = nowData;
                            break;
                        case "invoice":
                            this.invoiceRecordScanData.visible = true
                            this.invoiceRecordScanData.info = nowData;
                            break;
                        case "mail":
                            this.mailRecordScanData.title = ttl
                            this.mailRecordScanData.visible = true
                            this.mailRecordScanData.info = nowData;
                            this.mailRecordScanData.front = frontData;
                            break;
                        case "contact":
                        case "contactManage":
                            this.contactUpdateLogDetailsData.visible = true
                            this.contactUpdateLogDetailsData.title = ttl
                            this.contactUpdateLogDetailsData.picSrc = nowData.cardPath ?  this.rootPath.fileUrl + nowData.cardPath : ''
                            if(frontData == null){
                                this.contactUpdateLogDetailsData.name = nowData.name || ''
                                this.contactUpdateLogDetailsData.postName = nowData.post || ''
                                this.contactUpdateLogDetailsData.tel = nowData.mobile || ''
                                this.contactUpdateLogDetailsData.contactList = this.setTable(nowData.socialList)

                            }else {
                                this.contactUpdateLogDetailsData.name = this.compareD(frontData.name, nowData.name)
                                this.contactUpdateLogDetailsData.postName = this.compareD(frontData.post, nowData.post)
                                this.contactUpdateLogDetailsData.tel = this.compareD(frontData.mobile, nowData.mobile)

                                nowData.socialList.forEach(newItem => {
                                    let frontItem = this.getSocialItem(newItem.type, frontData.socialList)
                                    if (frontItem) {
                                        if (newItem.name !== frontItem.name) {
                                            newItem.name = '<span style="color:#ed5565;">' + (newItem.name || '') + '</span>'
                                        }
                                        if (newItem.code !== frontItem.code) {
                                            newItem.code = '<span style="color:#ed5565;">' + (newItem.code || '') + '</span>'
                                        }
                                    } else {
                                        newItem.name = '<span style="color:#ed5565;">' + (newItem.name || '') + '</span>'
                                        newItem.code = '<span style="color:#ed5565;">' + (newItem.code || '') + '</span>'
                                    }
                                })
                                this.contactUpdateLogDetailsData.contactList = this.setTable(nowData.socialList)
                            }
                            break
                        default:
                            console.log('没找到str=', type)
                            break
                    }

                }).catch(err=>{
                console.log('err=', err)
            })
        },
        hidetitleFun(){
            this.contactUpdateLogDetailsData.visible = false
        },
        compareD(front,now){
            let str = ''
            if(front === now){
                str = now || ''
            }else{
                str = '<span style="color:#ed5565;">' + (now || '') + '</span>'
            }
            console.log('返回', str)
            return str;
        },
        compareRed(front,now){
            let able = false
            if(front !== now){
                able = true
            }
            return able;
        },
        manageBaseOk(){
            if(this.manageBaseOkBtn){
                let params = {
                    id: this.supplierManageData.editOrg.id,
                    qImages: []
                }
                params.fullName = this.manageBaseData.fullName
                params.codeName = this.manageBaseData.codeName
                params.name = this.manageBaseData.name
                params.chargeAcceptable = this.manageBaseData.chargeAcceptable
                params.chargeBegin = this.manageBaseData.chargeBegin
                params.chargePeriod = this.manageBaseData.chargePeriod
                params.isImprest = this.manageBaseData.isImprest
                params.imprestProportion = this.manageBaseData.imprestProportion

                if( params.name.length == 0){
                    this.$message.error('请填写供应商名称')
                    return false;
                }else if( params.fullName.length == 0){
                    this.$message.error('请填写供应商简称')
                    return false;
                }else if( params.codeName.length == 0){
                    this.$message.error('请填写供应商代号')
                    return false;
                }else if(params.isImprest == 1){    //是否需要预付款       下面两个必须选一个（预付款比例和预付款比例不确定）
                    if(params.imprestProportion == null || params.imprestProportion == ""){ //预付款比例
                        if(params.uncertainty == null || params.uncertainty == ""){ //预付款比例不确定
                            this.$message.error('请填写预付款比例')
                            return false;
                        }
                    }
                }
                let qImages = []
                this.manageBaseData.quanImg.forEach(qImg=>{
                    let item = { 'normal':qImg.filename  }
                    qImages.push(item)
                })
                params.imagesString = JSON.stringify(qImages); // 全景
                //params.qImages = qImages// 全景

                //params.taxRate = Number(this.addData.base.taxRate)  // 是否需要开票
                api.updateSupplierBase(params).then(res1 => {
                    let res = res1.data
                    let status = res.status
                    if(status == 0){
                        this.$message.error("操作失败！")
                    }else { // 修改成功
                        this.manageBaseData = false
                        // 更新管理页
                        this.supplierManageData.editOrg.name = params.name
                        this.supplierManage(this.supplierManageData.editOrg)
                        // 更新 主页
                        this.getEqSupplier(1,20)
                        this.manageBaseDataLog = false
                    }
                }).catch(err=>{
                    console.log('err=', err)
                })
            }
        },
        //合同
        seeExpireContract(){
            this.contractEndData.visible = true
            this.getlistContractBase(this.supplierManageData.editOrg.id, 3)
        },
        seeStopContract(){
            this.contractStopData.visible = true
            this.getlistContractBase(this.supplierManageData.editOrg.id, 2)
        },
        baseUpdate(){
            // 基本信息 修改
            api.getSupplierData({ id: this.supplierManageData.editOrg.id }).then(res1=>{
                let res = res1.data.data
                this.manageBaseDataLog = true
                //this.manageBaseData.chargeAcceptable = res.chargeAcceptable.toString()
                let quanList = []
                res.qImages.forEach(qImg=>{
                    quanList.push({
                        src: this.rootPath.fileUrl + qImg.normal,
                        filename:qImg.normal
                    })
                })
                res.quanImg = quanList
                this.manageBaseData = res
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        fpAdd(){
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'manageAdd'
            this.mailInfoData.title = '新增邮寄信息'
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = ''
            this.mailInfoData.code = ''
            this.mailInfoData.contact = {}
        },
        adressStopList(){ // 获取地址停用列表
            api.getSuspendAddressList({
                supplierId: this.supplierManageData.editOrg.id
            }).then(res1=>{
                let res = res1.data
                this.stopShAdressData.list = res['data']|| []
                this.stopShAdressData.visible = true
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        fpAdressUpdate(fpItem){
            console.log('fpItem=', fpItem)
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'manageUpdate'
            this.mailInfoData.title = '修改邮寄信息'
            this.mailInfoData.editObj = fpItem
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = fpItem.address
            this.mailInfoData.code = fpItem.postcode
            this.mailInfoData.contact = {
                name: fpItem.name ,
                id: fpItem.supplierContact ,
                mobile: fpItem.mobile
            }
        },
        addressStopBtn(address, indexArea){
            // 发票邮寄地址，收货地址，收货区域 的 停用 都这一个方法
            this.stopAdresstData.visible = true
            this.stopAdresstData.editObj = address
        },
        startAdress(adressItem){
            api.manageAddressAble({
                addressId: adressItem.id,
                enabled: 1
            }).then(res1=>{
                let res = res1.data
                if(res.success === 1){
                    this.$message.success('操作成功')
                    this.adressStopList()
                    this.getSupplierManageData(this.supplierManageData.editOrg.id)
                }else{
                    this.$message.error('操作失败')
                }

            }).catch(err=>{
                console.log('err=',err)
            })
        },
        hideStopCt(){
            this.stopContactData.visible = false
        },
        stopCtOK(){
            api.deleteSupplierContact({ contactId:this.stopContactData.editObj.id  }).then(res1=>{
                let data = res1.data
                let status = data.success;
                if (status === 1) {
                    this.getSupplierManageData(this.supplierManageData.editOrg.id)
                    this.stopContactData.visible = false
                    this.$message.success("操作成功！")
                } else {
                    this.$message.error("停止失败！")
                }
            }).catch(err=>{
                console.log('err=', err)
            })

        },
        shAdressStopOK(){
            let params = {
                addressId: this.stopAdresstData.editObj.id,
                enabled: Number(this.stopAdresstData.editObj.enabled)^1
            }
            api.manageAddressAble(params).then(res1=>{
                let status = res1.data.success;
                if (status === 1) {
                    this.getSupplierManageData(this.supplierManageData.editOrg.id)
                    this.stopAdresstData.visible = false
                    this.$message.success('操作成功！')
                } else {
                    this.$message.error('操作失败！')
                }

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hideShAdress(){
            this.stopAdresstData.visible = false
        },
        hideContactStop(){
            this.contactStopData.visible = false
        },
        addContactList(){
            api.getDeleteContactsListSupplier({ id: this.supplierManageData.editOrg.id }).then(res1=>{
                let res = res1.data
                this.contactStopData.visible = true
                this.contactStopData.list = res.data
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        contactUpdate(contactItem){
            console.log('contactItem=', contactItem)
            api.getContactsSocialData({ contactId: contactItem.id }).then(res1=>{
                let res = res1.data.data
                this.editContactLianXiData.type = 'manageUpdate'
                this.editContactLianXiData.visible = true
                this.editContactLianXiData.title = '修改联系人'
                this.editContactLianXiData.showOptions = false
                this.editContactLianXiData.loading =  false
                this.editContactLianXiData.loadingBtn = '点击此处上传名片'

                this.editContactLianXiData.name = res.name
                this.editContactLianXiData.postName = res.post
                this.editContactLianXiData.tel = res.mobile
                this.editContactLianXiData.tag = res.tags
                this.editContactLianXiData.newId = res.id
                this.editContactLianXiData.id = res.id
                this.editContactLianXiData.contactArr = []
                this.editContactLianXiData.picInfo = {}
                let typeList = [
                    { label: '手机', value:1 ,disabled: false },
                    { label: 'QQ', value:2 ,disabled: false },
                    { label: 'Email', value:3 ,disabled: false },
                    { label: '微信', value:4 ,disabled: false },
                    { label: '微博', value:5 ,disabled: false },
                    { label: '自定义', value:9 ,disabled: false },
                ]
                if(res.socialList.length > 0){
                    res.socialList.forEach(item=>{
                        let ii = {
                            name: item.name ,
                            value: item.type ,
                            val: item.code
                        }
                        this.editContactLianXiData.contactArr.push(ii)
                        typeList.forEach(typeI=>{
                            if(typeI.value === Number(item.type)){
                                typeI.disabled = true
                            }
                        })
                    })
                }
                this.editContactLianXiData.typeList = typeList
                this.setContactList()
                if (res.cardPath) {
                    this.editContactLianXiData.picInfo = {
                        src : this.rootPath.fileUrl + res.cardPath  ,
                        filename :  res.cardPath  ,
                    }
                }
            }).catch(err=>{
                console.log('err=', err)
            })

        },
        seeNameID(item, type){
            let contactItem = type ===2 ? item.info2 : item.info1
            api.getContactsSocialData({ contactId: contactItem.id }).then(res1=>{
                let res = res1.data
                if(res.success == 1 && res.data.cardPath){
                    this.contactCard.visible = true
                    this.contactCard.visitCard = this.rootPath.fileUrl + res.data.cardPath

                }else{
                    this.$message.info('没有可供查看的名片！')
                }
            }).catch(err=>{
                this.$message.error('链接失败')
                console.log('err=', err)
            })

        },
        hideCrD(){
            this.contactCard.visible = false
        },
        editContactLianXiOk(){
            let lianXiItem ={
                tag: this.editContactLianXiData.tag ,
                typeStr: this.editContactLianXiData.typeStr,
                name: this.editContactLianXiData.name,
                postName: this.editContactLianXiData.postName,
                tel: this.editContactLianXiData.tel,
                contactArr: this.editContactLianXiData.contactArr,
                contactList: this.editContactLianXiData.contactList,
                typeList: this.editContactLianXiData.typeList,
                picInfo: this.editContactLianXiData.picInfo
            }
            if(this.rootCusBtn == 'add'){ // 新增客户
                console.log('this.editContactLianXiData.type=', this.editContactLianXiData.type)
                if(this.editContactLianXiData.type === 'add'){
                    lianXiItem.newId = this.getFive()
                    let newContact = {
                        name:lianXiItem.name ,
                        post:lianXiItem.postName ,
                        id:lianXiItem.newId ,
                        info:lianXiItem,
                    }
                    console.log('联系人完整数据：', newContact)
                    this.addData.contactLianxi.tableData.push(newContact)
                    switch (this.editContactLianXiData.typeStr){
                        case 'mailInfo': // 新增发票邮寄地址
                            this.mailInfoData.contact = newContact
                            break
                        default:
                            console.log('没有找到对应的 typeStr')
                    }
                }
                else if(this.editContactLianXiData.type === 'update'){
                    lianXiItem.newId = this.editContactLianXiData.newId
                    let index = this.editContactLianXiData.editIndex
                    let newContact = {
                        name:lianXiItem.name ,
                        post:lianXiItem.postName ,
                        id:lianXiItem.newId ,
                        info:lianXiItem,
                    }
                    this.addData.contactLianxi.tableData[index] = newContact
                    // 修改上面的
                    if(this.addData.mail.tableData && this.addData.mail.tableData.length > 0){
                        let mailTable = this.addData.mail.tableData
                        mailTable.forEach((mailItem) => {
                            if(mailItem.contact.id === newContact.id){
                                mailItem.recivePerson = newContact.name
                                mailItem.tel = newContact.info.tel
                                mailItem.contact = newContact
                                // mailTable[mailIndex] = mailItem
                            }
                        })
                    }
                }
                this.editContactLianXiData.visible = false
            }
            else if(this.rootCusBtn == 'update'){ // 管理客户
                let params = {
                    tags: lianXiItem.tag,
                    name: lianXiItem.name,
                    post: lianXiItem.postName,
                    mobile: lianXiItem.tel,
                    cardPath: '',
                    socialList: [],
                }
                if(lianXiItem.picInfo && lianXiItem.picInfo.filename){
                    params.cardPath = lianXiItem.picInfo.filename
                }
                if(lianXiItem.contactArr && lianXiItem.contactArr.length > 0){
                    lianXiItem.contactArr.forEach(cI=>{
                        let cc = {
                            code: cI.val ,
                            type: cI.value ,
                            name: cI.name
                        }
                        params.socialList.push(cc)
                    })
                }
                params.socialList = JSON.stringify(params.socialList)
                if(this.editContactLianXiData.type === 'manageUpdate'){
                    params.contactId = this.editContactLianXiData.id
                    api.updateContactsSure(params).then(res1=>{
                        let res = res1.data
                        let status = res.success
                        if(status === 1){
                            this.getSupplierManageData(this.supplierManageData.editOrg.id)
                            this.$message.success('操作成功！')
                            this.editContactLianXiData.visible = false
                        }else{
                            this.$message.error('操作失败')
                        }
                    }).catch(err=>{
                        console.log('err=',err)
                    })
                }else if(this.editContactLianXiData.type === 'add'){
                    params.supplierId = this.supplierManageData.editOrg.id
                    api.addContactsSure(params).then(res1=>{
                        let res = res1.data
                        let status = res.success
                        if(status === 1){
                            this.getSupplierManageData(this.supplierManageData.editOrg.id)
                            this.editContactLianXiData.visible = false
                            if (this.editContactLianXiData.typeStr === 'mailInfo'){
                                lianXiItem.newId = res.data
                                let newContact = {
                                    name:lianXiItem.name ,
                                    post:lianXiItem.postName ,
                                    mobile:lianXiItem.tel ,
                                    id:lianXiItem.newId ,
                                    info:lianXiItem,
                                }
                                this.mailInfoData.contact = newContact
                            }
                        }else{
                            this.$message.error('操作失败,请重试')
                        }
                    }).catch(err=>{
                        console.log('err=', err)
                    })
                }
            }
        },
        contactDel(contactItem){
            this.stopContactData.visible = true
            this.stopContactData.editObj = contactItem
        },
        contractIScan(contractItem){
            this.contractScanData.visible = true
            api.contractBaseScan({ id: contractItem.id }).then(res1=>{
                let res = res1.data
                let data = res.data
                let contractBase = data.contractBase // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
                let listImage = data.listImage || [] // 合同的扫描件或照片
                let listHis = data.listHis || [] // 暂停恢复记录
                let previewList = []
                listImage.forEach(img=>{
                    img.src = this.rootPath.fileUrl + img.uplaodPath
                    previewList.push(auth.webRoot + '/upload/' + img.uplaodPath)
                })
                listImage.previewList = previewList
                this.contractScanData.contractBase = contractBase
                this.contractScanData.listImage = listImage
                this.contractScanData.listHis = listHis


            }).catch(err=>{
                console.log('err=',err)
            })
        },
        hideCScan(){
            this.contractScanData.visible = false
        },
        delImg(index, imgType, type){
            if(type === 'manage'){
                this.manageBaseData[imgType].splice(index,1)
            }else{
                this.addData.base[imgType].splice(index,1)
            }
        },
        getFive() {
            const randomNumber = Math.floor(Math.random() * 100000);
            const formattedNumber = String(randomNumber).padStart(5, '0');
            return formattedNumber;
        },
        hideDelCus(){
            this.delCusData.visible = false
        },
        hideUpdateCustomerFun(){
            this.supplierManageData.visible = false
        },
        delSupplierOK(){
            api.deleteSupplierOne({ id: this.delCusData.editObj.id }).then(res1=>{
                let res = res1.data
                if(res.success == 1){
                    this.$message.success('删除成功！')
                    this.getEqSupplier(1,20);
                }else {
                    this.$message.error('操作失败，因为此供应商下已有材料数据！')
                }
            }).catch(err=>{
                console.log('err=', err)
            })
            this.delCusData.visible = false
        },
        paulsCusOK(){
            let url = ``
            if (this.paulsCusData.type == 1) {
                url = '../supplier/stopSupplier.do'
            } else {
                url = '../supplier/recoverySrmSupplier.do'
            }
            api.stopSupplierRes({ id:this.paulsCusData.editObj.id },url).then(res1=>{
                let res = res1.data
                if(res.success === 1){
                    this.paulsCusData.visible = false
                    this.$message.success('操作成功！')
                    this.getEqSupplier(1,20);
                    if(this.paulsCusData.type != 1){
                        this.suspendedSupplier(1)
                    }
                }else{
                    this.$message.error(res.errorMsg)
                }
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hidePaulsCus(){
            this.paulsCusData.visible = false
        },
        detailsConac(nowData){
            this.contactDetailsData.visible = true
            this.contactDetailsData.name = nowData.name || ''
            this.contactDetailsData.postName = nowData.post || ''
            this.contactDetailsData.tags = nowData.tag || ''
            this.contactDetailsData.contactList = []
        },
        getContactWay(info, num){
            api.getContactsSocialData({ contactId: info['info' + num].id }).then(res1=>{
                let state = res1.data.success;
                if(state === "1"|| state === 1){
                    let nowData= res1.data.data
                    this.contactDetailsData.visible = true
                    this.contactDetailsData.name = nowData.name || ''
                    this.contactDetailsData.postName = nowData.post || ''
                    this.contactDetailsData.tags = nowData.tags
                    this.contactDetailsData.contactList = this.setTable(nowData.socialList)
                }
            })
        },
        hideLianXiFun(){
            this.contactDetailsData.visible = false
        },
        setTable(socialList){
            let list = []
            if(socialList.length > 0){
                socialList.forEach((linkI, index0) => {
                    let yu = index0%2
                    if(yu === 0){
                        list.push({
                            name:linkI.name, val: linkI.code ,
                            name2:'',  val2: '' ,
                        })
                    }else{
                        let latI = list[list.length-1]
                        latI.name2 = linkI.name
                        latI.val2 = linkI.code
                    }

                })
            }
            return list
        },
        hideStopShAdress(){
            this.stopShAdressData.visible = false
        },
        addDataHideFun(){
            this.addData.visible = false
        },
        scanSupplierHideFun(){
            this.scanSupplierLog = false
        },
        dialogHideFun(str) {
            switch (str) {
                case 'addData':
                    this.addData.visible = false
                    break
                case 'scanSupplierLog':
                    this.scanSupplierLog = false
                    break
                case 'mailInfoData':
                    this.mailInfoData.visible = false
                    break
                case 'chooseCusContact':
                    this.chooseCusContactData.visible = false
                    break
                case 'contactLianXi':
                    this.editContactLianXiData.visible = false
                    break
                case 'useDefined':
                    this.useDefinedLabelData.visible = false
                    break
                case 'recordsData':
                    this.updateRecordsData.visible = false
                    break
                case 'manageBaseDataLog':
                    this.manageBaseDataLog = false
                    break
                case 'updateCustomerPanel':
                    this.updateCustomerPanel = false
                    break
                case 'hideShAdress':
                    this.stopAdresstData.visible = false
                    break
                case 'hideStopCt':
                    this.stopContactData.visible = false
                    break
                case 'contractStopData':
                    this.contractStopData.visible = false
                    break
                case 'hidePaulsCus':
                    this.paulsCusData.visible = false
                    break
                case 'manageInvoiceData':
                    this.manageInvoiceData.visible = false
                    break
                case 'baseRecordScanLog':
                    this.baseRecordScanData.visible = false
                    break
                case 'invoiceRecordScanLog':
                    this.invoiceRecordScanData.visible = false
                    break
                case 'mailRecordScanLog':
                    this.mailRecordScanData.visible = false
                    break
                case 'contractEndData':
                    this.contractEndData.visible = false
                    break
                case 'eqScanLog':
                    this.eqScanLog = false
                    break
            }
        }
    },
    watch:{
        'addData.base.fullName'(){
            if (this.addData.base.fullName) {
                this.addData.base.name = this.addData.base.fullName.substring(0,6)
            }
        },
        'manageBaseData.fullName'(){
            if (this.manageBaseData.fullName) {
                this.manageBaseData.name = this.manageBaseData.fullName.substring(0,6)
            }
        },
    },
}



