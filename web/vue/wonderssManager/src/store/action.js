

function setNewVersion (context, obj) { // 调用：this.$store.dispatch('setNewMenu'，obj)
  context.commit('SET_VERSION', obj)
}

function setActivePage (context, obj) {
  context.commit('SET_ACTIVE_PAGE', obj)

}

function setManage (context, num) {
  context.commit('SET_MANAGE', num)
}

function setManageCode (context, code) {
  context.commit('SET_MANAGE_CODE', code)
}


function setManageParam(context, obj) {
  console.log('setManageParam', obj)
  context.commit('SET_MANAGE_PARAM', obj)
}

export default {
  setNewVersion,
  setActivePage,
  setManage,
  setManageCode,
  setManageParam
}
