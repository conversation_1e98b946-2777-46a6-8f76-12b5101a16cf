package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.DigestUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.generalAffairs.dao.*;
import cn.sphd.miners.modules.generalAffairs.dto.*;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.*;
import cn.sphd.miners.modules.iot.entity.IotTerminal;
import cn.sphd.miners.modules.iot.service.IotService;
import cn.sphd.miners.modules.personal.dao.PersonnelOvertimeDao;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.ApprovalFlowDao;
import cn.sphd.miners.modules.system.dao.ApprovalItemDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import com.alibaba.fastjson.JSON;

import com.icbc.api.internal.apache.http.impl.cookie.S;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.serializer.SerializerFeature;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.resource.transaction.spi.TransactionStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * WorkAttendanceServiceImpl_in_generalAffairs
 * @author: wuyu
 * @since: 3.9
 * @date: 2025-04-21 13:43:10
 **/
@Service("workAttendanceService")
public class WorkAttendanceServiceImpl extends BaseServiceImpl implements WorkAttendanceService {
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    TaskExecutor taskExecutor;
    @Autowired
    WorkAttendanceService workAttendanceService;
    @Autowired
    PersonnelAttendanceManualService personnelAttendanceManualService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Autowired
    PersonnelAttendanceOrgMonitorDao personnelAttendanceOrgMonitorDao;
    @Autowired
    PersonnelAttendanceConfigDao personnelAttendanceConfigDao;
    @Autowired
    PersonnelAttendanceExceptionDao personnelAttendanceExceptionDao;
    @Autowired
    UserDepartmentHistoryDao userDepartmentHistoryDao;
    @Autowired
    PersonnelAttendanceUserDao personnelAttendanceUserDao;
    @Autowired
    PersonnelAttendancePunchDao personnelAttendancePunchDao;
    @Autowired
    PersonnelAttendanceUserDetailDao personnelAttendanceUserDetailDao;
    @Autowired
    PersonnelAttendanceDepartmentConfigDao personnelAttendanceDepartmentConfigDao;
    @Autowired
    PersonnelAttendanceTerminalDao personnelAttendanceTerminalDao;
    @Autowired
    PersonnelAttendanceRecordDao personnelAttendanceRecordDao;
    @Autowired
    UserDao userDao;
    @Autowired
    PersonnelAttendanceUserDetailPunchDao personnelAttendanceUserDetailPunchDao;
    @Autowired
    ApprovalFlowDao approvalFlowDao;
    @Autowired
    ApprovalItemDao approvalItemDao;
    @Autowired
    PersonnelOvertimeDao personnelOvertimeDao;
    @Autowired
    PersonnelAttendanceIotTerminalDao personnelAttendanceIotTerminalDao;

    @Autowired
    DlmService dlmService;
    @Autowired
    ScheduleService scheduleService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    PersonnelAttendanceMonthlyService personnelAttendanceMonthlyService;
    @Autowired
    IotService iotService;
    @Autowired
    OvertimeService overtimeService;
    @Autowired
    LeaveService leaveService;

    private final ConcurrentHashMap<String, AttendanceEventSerivce> eventServices = new ConcurrentHashMap<>();

    @Override
    public AttendanceEventSerivce getAttendanceEventService(String serviceName) {
        AttendanceEventSerivce result = eventServices.get(serviceName);
        if (result == null) {
            result = WorkAttendanceService.super.getAttendanceEventService(serviceName);
            eventServices.put(serviceName, result);
        }
        return result;
    }

    @Override
    public String getDlmtLock() {
        return dlmService.getLock(methodName, operateInteval);
    }

    @Override
    public String getDlmtLock(int times) {
        return dlmService.getLock(methodName, operateInteval * times);
    }

    @Override
    public void releaseDlmLock(String lockKey) {
        dlmService.releaseLock(methodName, lockKey);
    }

    /**
     * runAttendanceTask 执行考勤扫描任务，定时15分钟执行，需要加锁。
     *
     * <AUTHOR>
     * @date 2025-04-21 14:12:59
     * @since 4.0
     **/
    @Override
    public void runAttendanceTask() {
        //修改部门任务
        orgService.postSettleDay();
        Schedule schedule = scheduleService.getScheduleByCode(methodName);
        Date now = new Date(System.currentTimeMillis());
        if (schedule.getRunTime().getTime() + TimeUnit.DAYS.toMillis(1) <= now.getTime()) {//用Schedule类控制每天只执行一次
            runDefaultWorkAttendance();
            schedule.setRunTime(NewDateUtils.today(now));
        }
        updateAttendanceUserEvents();//处理考勤事件队列
        runAttendance(now);
    }

    @Override
    public void debugRunAttendanceTask(Date now, Integer oid) {
        //修改部门任务
        orgService.postSettleDay();
        Schedule schedule = scheduleService.getScheduleByCode(methodName);
        if (now == null) now = new Date(System.currentTimeMillis());
        if (schedule.getRunTime().getTime() + TimeUnit.DAYS.toMillis(1) <= now.getTime()) {//用Schedule类控制每天只执行一次
            debugRunDefaultWorkAttendance(NewDateUtils.today(now));
            schedule.setRunTime(NewDateUtils.today(now));
        }
        updateAttendanceUserEvents();//处理考勤事件队列
        if (oid != null) {
            runAttendance(now, oid);
        } else {
            runAttendance(now);
        }
    }

    /**
     * runDefaultWorkAttendance 生成/更新空白考勤表和考勤明细数据
     *
     * @return void
     * <AUTHOR>
     * @date 2025-04-21 15:21:55
     * @since 4.0
     **/
    private void runDefaultWorkAttendance() {
        final Date now = new Date(System.currentTimeMillis());   //当前时间
        final Date today = NewDateUtils.today(now);
        List<PersonnelAttendanceOrgMonitor> attendanceOrgMonitors = getAllOrgMonitor();
        for (PersonnelAttendanceOrgMonitor attendanceOrgMonitor : attendanceOrgMonitors) {
            Integer oid = attendanceOrgMonitor.getOrg();
            System.out.println("考勤初始化日任务 start oid=" + oid);
            //初始化空白考勤表，循环起点(上次考勤日scanDate的第二天)
            Date scanDate = attendanceOrgMonitor.getScanDate();
            byte scanTimes = attendanceOrgMonitor.getScanTimes().byteValue();
            if (scanTimes == 3 && today.before(scanDate)) {//启用考勤第一天 attendanceOrgMonitor.getScanTimes()==3
                Date systemTime = getStartUsingSystemTime(oid);//获取机构首次考勤生效事件
                //考勤第一天，非每月1日添加当月之前的日期月表无需考勤
                if (NewDateUtils.changeMonth(systemTime, 0).getTime() != systemTime.getTime()) {
                    List<UserDto> userDtoList = getUser(systemTime, oid, null);  //考勤第一天需考勤的人员
                    for (UserDto userDto : userDtoList) {
                        User user = userService.getUserByID(userDto.getUserID());   //要添加无需考勤的人员
                        personnelAttendanceMonthlyService.SetNewUserNoNeed(user, scanDate, null);//设置无需考勤，firstDate为空从当月第一天开始设置
                    }
                }
            }
            if (scanDate.before(today)) {
                //判断并添加作息时间，从当前考勤日第二天添加到今天的下月底。
                fillDefalutPersonnelAttendanceException(oid, scanDate);
                Date nextWorkday = getNextDateByWork(scanDate, oid);
                if (scanTimes < 3) {//今天一次跑，处理之前的考勤，scanTimes==3只在首次启用设置，无需处理之前的考勤
                    List<UserDto> userDtoList = getUser(scanDate, oid, null);  //需考勤的人员
                    if (!userDtoList.isEmpty()) {
                        for (UserDto userDto : userDtoList) {
                            User user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
                            PersonnelAttendanceUser pau = getPersonnelAttendanceUser(user, NewDateUtils.today(scanDate));
                            runAttendance(pau, now);
                        }
                    }
                }//endof首次启用考勤
                //切换日期，创建考勤日表
                for (scanDate = NewDateUtils.tomorrow(scanDate); scanDate.before(now); scanDate = NewDateUtils.tomorrow(scanDate)) {
                    if (!nextWorkday.after(scanDate)) {//已经到了下一个工作日，重新获取下一个工作日
                        nextWorkday = getNextDateByWork(scanDate, oid);
                    }
                    List<UserDto> userDtoList = getUser(scanDate, oid, null);  //需考勤的人员
                    if (!userDtoList.isEmpty()) {
                        for (UserDto userDto : userDtoList) {
                            //初始化，取出第一条数据
                            User user = userService.getUserByID(userDtoList.get(0).getUserID());   //要默认添加考勤的人员
                            PersonnelAttendanceUser pau;
                            PersonnelAttendanceException attendanceException = getPersonnelAttendanceException(oid, scanDate);
                            Integer deptId = NumberUtils.toInt(user.getDepartment(), 0);
                            ReturnTimeDto returnTime = getReturnTime(user.getOid(), deptId, scanDate);
                            int tmpId;
                            user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
                            if (!deptId.equals(tmpId = NumberUtils.toInt(user.getDepartment(), 0))) {//部门切换时 重新获取returnTime
                                deptId = tmpId;
                                returnTime = getReturnTime(user.getOid(), deptId, scanDate);
                            }
                            pau = addNewDefaultPersonnelAttendanceUser(user, null, returnTime, attendanceException, nextWorkday);//生成新考勤表和考勤明细表
                            runAttendance(pau, now);//立即执行一次扫描，解决停机跨日问题。
                        }
                    }
                }
            }
            attendanceOrgMonitor.setScanDate(now);
            attendanceOrgMonitor.setScanTimes((byte) 2);//扫描中，未完成
            personnelAttendanceOrgMonitorDao.saveOrUpdate(attendanceOrgMonitor);
            System.out.println("考勤初始化日任务 finish oid=" + oid);
        }
    }

    private void debugRunDefaultWorkAttendance(Date now) {
        final Date today = NewDateUtils.today(now);
        List<PersonnelAttendanceOrgMonitor> attendanceOrgMonitors = getAllOrgMonitor();
        for (PersonnelAttendanceOrgMonitor attendanceOrgMonitor : attendanceOrgMonitors) {
            Integer oid = attendanceOrgMonitor.getOrg();
            System.out.println("考勤初始化日任务 debug start oid=" + oid);
            //初始化空白考勤表，循环起点(上次考勤日scanDate的第二天)
            Date scanDate = attendanceOrgMonitor.getScanDate();
            byte scanTimes = attendanceOrgMonitor.getScanTimes().byteValue();
            if (scanTimes == 3 && today.before(scanDate)) {//启用考勤第一天 attendanceOrgMonitor.getScanTimes()==3
                Date systemTime = getStartUsingSystemTime(oid);//获取机构首次考勤生效事件
                //考勤第一天，非每月1日添加当月之前的日期月表无需考勤
                if (NewDateUtils.changeMonth(systemTime, 0).getTime() != systemTime.getTime()) {
                    List<UserDto> userDtoList = getUser(systemTime, oid, null);  //考勤第一天需考勤的人员
                    for (UserDto userDto : userDtoList) {
                        User user = userService.getUserByID(userDto.getUserID());   //要添加无需考勤的人员
                        personnelAttendanceMonthlyService.SetNewUserNoNeed(user, scanDate, null);//设置无需考勤，firstDate为空从当月第一天开始设置
                    }
                }
            }
            if (scanDate.before(today)) {
                //判断并添加作息时间，从当前考勤日第二天添加到今天的下月底。
                fillDefalutPersonnelAttendanceException(oid, scanDate);
                Date nextWorkday = getNextDateByWork(scanDate, oid);
                if (scanTimes < 3) {//今天一次跑，处理之前的考勤，scanTimes==3只在首次启用设置，无需处理之前的考勤
                    List<UserDto> userDtoList = getUser(scanDate, oid, null);  //需考勤的人员
                    if (!userDtoList.isEmpty()) {
                        for (UserDto userDto : userDtoList) {
                            User user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
                            PersonnelAttendanceUser pau = getPersonnelAttendanceUser(user, NewDateUtils.today(scanDate));
                            runAttendance(pau, now);
                        }
                    }
                }//endof首次启用考勤
                //切换日期，创建考勤日表
                for (scanDate = NewDateUtils.tomorrow(scanDate); !scanDate.after(now); scanDate = NewDateUtils.tomorrow(scanDate)) {
                    if (!nextWorkday.after(scanDate)) {//已经到了下一个工作日，重新获取下一个工作日
                        nextWorkday = getNextDateByWork(scanDate, oid);
                    }
                    List<UserDto> userDtoList = getUser(scanDate, oid, null);  //需考勤的人员
                    if (!userDtoList.isEmpty()) {
                        for (UserDto userDto : userDtoList) {
                            //初始化，取出第一条数据
                            User user = userService.getUserByID(userDtoList.get(0).getUserID());   //要默认添加考勤的人员
                            PersonnelAttendanceUser pau;
                            PersonnelAttendanceException attendanceException = getPersonnelAttendanceException(oid, scanDate);
                            Integer deptId = NumberUtils.toInt(user.getDepartment(), 0);
                            ReturnTimeDto returnTime = getReturnTime(user.getOid(), deptId, scanDate);
                            int tmpId;
                            user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
                            if (!deptId.equals(tmpId = NumberUtils.toInt(user.getDepartment(), 0))) {//部门切换时 重新获取returnTime
                                deptId = tmpId;
                                returnTime = getReturnTime(user.getOid(), deptId, scanDate);
                            }
                            pau = addNewDefaultPersonnelAttendanceUser(user, null, returnTime, attendanceException, nextWorkday);//生成新考勤表和考勤明细表
                            runAttendance(pau, now);//立即执行一次扫描，解决停机跨日问题。
                        }
                    }
                }
            }
            attendanceOrgMonitor.setScanDate(now);
            attendanceOrgMonitor.setScanTimes((byte) 2);//扫描中，未完成
            personnelAttendanceOrgMonitorDao.saveOrUpdate(attendanceOrgMonitor);
            System.out.println("考勤初始化日任务 debug finish oid=" + oid);
        }
    }

    /**
     * getAllOrgMonitor 读取全部考勤监控表数据列表
     *
     * @return List<PersonnelAttendanceOrgMonitor>
     * <AUTHOR>
     * @date 2025-05-29 09:09:27
     * @since 4.0
     **/
    private List<PersonnelAttendanceOrgMonitor> getAllOrgMonitor() {
        String hql = "from PersonnelAttendanceOrgMonitor";
        return personnelAttendanceOrgMonitorDao.getListByHQLWithNamedParams(hql, null);
    }

    /**
     * getOrgMonitor 从数据库读取某机构考勤监控表数据。
     *
     * @param oid
     * @return PersonnelAttendanceOrgMonitor
     * @apiNote 版本更新后不再自动创建新记录，由后续代码处理新建记录问题。2025-05-29，目前只用于考勤设置初始化本表数据前查询用
     * <AUTHOR>
     * @date 2025-05-19 11:00:01
     * @since 4.0
     **/
    private PersonnelAttendanceOrgMonitor getOrgMonitor(Integer oid) {
        String hql = "from PersonnelAttendanceOrgMonitor where org=:org";
        HashMap<String, Object> params = new HashMap<>(1) {{
            put("org", oid);
        }};
        return (PersonnelAttendanceOrgMonitor) personnelAttendanceOrgMonitorDao.getByHQLWithNamedParams(hql, params);
    }

    /**
     * runAttendance 机构考勤扫描
     *
     * @param now
     * @return void
     * <AUTHOR>
     * @date 2025-05-29 09:18:35
     * @since 4.0
     **/
    @Override
    public void runAttendance(Date now) {
        List<PersonnelAttendanceOrgMonitor> attendanceOrgMonitors = getAllOrgMonitor();
        for (PersonnelAttendanceOrgMonitor attendanceOrgMonitor : attendanceOrgMonitors) {
            System.out.println("考勤机构扫描任务 start oid=" + attendanceOrgMonitor.getOrg() + ", now : " + NewDateUtils.dateToString(now, sdf));
            runAttendance(attendanceOrgMonitor, now);
            System.out.println("考勤机构扫描任务 finish oid=" + attendanceOrgMonitor.getOrg() + ", now : " + NewDateUtils.dateToString(now, sdf));
        }
    }

    public void runAttendance(Date now, Integer oid) {
        PersonnelAttendanceOrgMonitor attendanceOrgMonitor = getOrgMonitor(oid);
        System.out.println("考勤机构扫描任务 start oid=" + attendanceOrgMonitor.getOrg() + ", now : " + NewDateUtils.dateToString(now, sdf));
        runAttendance(attendanceOrgMonitor, now);
        System.out.println("考勤机构扫描任务 finish oid=" + attendanceOrgMonitor.getOrg() + ", now : " + NewDateUtils.dateToString(now, sdf));
    }

    private void runAttendance(PersonnelAttendanceOrgMonitor attendanceOrgMonitor, Date now) {
        Integer oid = attendanceOrgMonitor.getOrg();
        Organization organization = orgService.getByOid(oid);
        Date scanDate;
        if ((scanDate = attendanceOrgMonitor.getScanDate()) != null && scanDate.getTime() + operateInteval < now.getTime()) {
            List<UserDto> userDtoList = getUser(scanDate, oid, null);  //需考勤的人员
            if (!userDtoList.isEmpty()) {
                for (UserDto userDto : userDtoList) {
                    User user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
                    PersonnelAttendanceUser pau = getPersonnelAttendanceUser(user, NewDateUtils.today(scanDate));
                    if (pau == null) {//兼容性处理，正常情况扫描阶段不应出现日表不存在的问题。
                        pau = getOrAddNewPersonnelAttendanceUser(user, now);
                        logger.warn("runAttendance 考勤扫描错误，考勤日表为空，新生成的考勤日表记录为：\n" + JSON.toJSONString(pau), new RuntimeException("考勤扫描错误，考勤日表为空！"));
                    }
                    runAttendance(pau, now);//考勤扫描
                }
                if (ConfigAttendancePattern.manualEntry.equals(getReturnTime(oid, null, scanDate).getCap())//手工录入模式
                        && getInputTime(oid, getCurrentWorkDate(scanDate, oid)).before(now)) {//超过录入时间
                        setSysAbsenteeism(organization, now);
                }
            }
            attendanceOrgMonitor.setScanDate(now);
            attendanceOrgMonitor.setScanTimes((byte) 2);//新建考勤日表，未考勤扫描
            personnelAttendanceOrgMonitorDao.saveOrUpdate(attendanceOrgMonitor);
        }
    }

    private void runAttendance(PersonnelAttendanceUser pau, Date now) {
        Assert.notNull(pau, "考勤日表不能为空");
        if (!pau.getFinalModified() //未修改考勤，可以重新加载考勤事件
                && (pau.getScanTime() == null //首次扫描，需要根据blocks更新pau。
                || pau.getScanEndTime().after(pau.getScanTime()) //未扫描到扫描截止时间
                && pau.getScanTime().getTime() + operateInteval < now.getTime())) {//上次扫描时间到现在已经超过规定时间，或扫描时间被非扫描事件修改
            Map<String, Object> params;
            switch (ConfigAttendancePattern.getBySource(pau.getSource())) {
                case manualEntry://手工录入数据
                    PersonnelAttendanceManual pam = personnelAttendanceManualService.getByPau(pau);
                    updatePauWithPam(pau, pam);
                    break;
                case attendanceTreasure://考勤宝打卡数据
                    params = new HashMap<>(4) {{
                        put("id", pau.getLastScanPunchId());
                        put("user", pau.getUser());
                        put("startTime", pau.getScanStartTime());
                        put("endTime", new Date(Math.min(pau.getScanEndTime().getTime(), now.getTime())));
                    }};
                    List<AttendancePunchDto> paps = personnelAttendancePunchDao.getListByHQLWithNamedParams("select new cn.sphd.miners.modules.generalAffairs.dto.AttendancePunchDto(id,user,punchTime,workdayType,punchType,type,businessService,business,createTime) from PersonnelAttendancePunch where id>:id and user=:user and punchTime between :startTime and :endTime order by punchTime", params);
                    updatePauWithPaps(pau, paps, now);
                    break;
            }
            setDetailSysAbsenteeism(pau, now);
            updatePausByDetails(pau);
            pau.setScanTime(now);
            personnelAttendanceUserDao.update(pau);
        }
    }
    private void updatePauWithPam(PersonnelAttendanceUser pau, PersonnelAttendanceManual pam) {
        //pam不为空，且beginCommited或endCommited至少有一个为FALSE（有数据未提交明细）
        if (pam!=null && (Boolean.FALSE.equals(pam.getBeginCommited())||Boolean.FALSE.equals(pam.getEndCommited()))) {
            Set<PersonnelAttendanceUserDetail> detailSet = pau.getPersonnelAttendanceUserDetailHashSet();
            //处理上班
            if(Boolean.FALSE.equals(pam.getBeginCommited())) {
                if (!AttendanceType.getEventTypes().contains(AttendanceType.getByName(pau.getBeginState()))//如果是申请的考勤状态，已申请为准，无须操作考勤表和明细表
                        || !AttendanceType.absenteeism.equals(AttendanceType.getByIndex(pam.getBeginState()))) {//手工录入表的状态是旷工无须处理，由后面旷工部分一起处理
                    PersonnelAttendanceUserDetail detail = detailSet.stream().filter(d -> WorkdayType.checkMultipleState(WorkdayType.getByState(d.getState()), WorkdayType.Workday)).findAny().orElse(null);
                    if (detail == null) {
                        detail = new PersonnelAttendanceUserDetail(pau, WorkdayType.Workday);
                        detail.setCreator(pam.getCreator());
                        detail.setCreateName(pam.getCreateName());
                        detail.setWorkdayType(WorkdayType.Workday.getIndex());
                        detail.setState(WorkdayType.Workday.getState());
                    }
                    detail.setUpdator(pam.getUpdator());
                    detail.setUpdateName(pam.getUpdateName());
                    AttendanceType type = AttendanceType.getByIndex(pam.getBeginState());
                    detail.setType(type.getName());
                }
                pam.setBeginCommited(Boolean.TRUE);
            }
            //处理下班
            if(Boolean.FALSE.equals(pam.getEndCommited())) {
                if (!AttendanceType.getEventTypes().contains(AttendanceType.getByName(pau.getEndState()))//如果是申请的考勤状态，已申请为准，无须操作考勤表和明细表
                        || !AttendanceType.absenteeism.equals(AttendanceType.getByIndex(pam.getEndState()))) {//手工录入表的状态是旷工无须处理，由后面旷工部分一起处理
                    PersonnelAttendanceUserDetail detail = detailSet.stream().filter(d -> WorkdayType.checkMultipleState(WorkdayType.getByState(d.getState()), WorkdayType.DayOff)).findAny().orElse(null);
                    AttendanceType type = AttendanceType.getByIndex(pam.getEndState());
                    if(detail != null && WorkdayType.Multiple.getIndex().equals(detail.getWorkdayType())
                            && !detail.getType().equals(type.getName())) {//上下班状态不同，需要拆分detail
                       detail.setWorkdayType(WorkdayType.Workday.getIndex());
                       personnelAttendanceUserDetailDao.update(detail);
                       detail = null;
                    }
                    if (detail == null) {
                        detail = new PersonnelAttendanceUserDetail(pau, WorkdayType.Workday);
                        detail.setCreator(pam.getCreator());
                        detail.setCreateName(pam.getCreateName());
                        detail.setWorkdayType(WorkdayType.DayOff.getIndex());
                        detail.setState(WorkdayType.DayOff.getState());
                    }
                    detail.setUpdator(pam.getUpdator());
                    detail.setUpdateName(pam.getUpdateName());
                    detail.setType(type.getName());
                }
                pam.setEndCommited(Boolean.TRUE);
            }
            //处理旷工
            if(pam.getAbsMd5()!=null && StringUtils.isNotEmpty(pam.getAbsenteeisms()) && (StringUtils.isBlank(pam.getAbsMd5()) || !pam.getAbsMd5().equals(DigestUtils.md5(pam.getAbsenteeisms())))) {
                List<WorkOffBlockDto> absenteeisms = JSON.parseArray(pam.getAbsenteeisms(), WorkOffBlockDto.class);
                List<Integer> removeDetailIds = WorkOffBlockDto.mergeWithLimit(absenteeisms,
                        WorkOffBlockDto.getListFromDetailSet(detailSet, AttendanceType.getEventTypes(), WorkdayType.getMultipleStates(WorkdayType.DayOff)),//将detailSet只取申请类、离岗类明细生成List<WorkOffBlockDto>
                        pau.getBeginTime(), pau.getEndTime());
                if(!absenteeisms.isEmpty()) {
                    absenteeisms.forEach(absenteeism->{
                        PersonnelAttendanceUserDetail detail = null;
                        boolean begin = false, end = false;
                        if(AttendanceType.absenteeism.getName().equals(pau.getBeginState())//考勤开始状态是旷工
                                && !absenteeism.GetBegin().after(pau.getBeginTime())//当前旷工时间段包含考勤开始时间
                                && !absenteeism.GetEnd().before(pau.getBeginTime())) {
                            begin = true;
                            detail = detailSet.stream().filter(d -> WorkdayType.checkMultipleState(WorkdayType.getByState(d.getState()), WorkdayType.Workday)).findAny().orElse(null);
                        }
                        if(AttendanceType.absenteeism.getName().equals(pau.getEndState())//考勤结束状态是旷工
                                && !absenteeism.GetBegin().after(pau.getEndTime())//当前旷工时间段包含考勤结束时间
                                && !absenteeism.GetEnd().before(pau.getEndTime())) {
                            end = true;
                            detail = detailSet.stream().filter(d -> WorkdayType.checkMultipleState(WorkdayType.getByState(d.getState()), WorkdayType.DayOff)).findAny().orElse(null);
                        }
                        if(absenteeism.getDetailId()!=null) {
                            if(detail == null) {
                                detail = detailSet.stream().filter(d -> absenteeism.getDetailId().equals(d.getId())).findAny().orElse(null);
                                if (detail == null) {
                                    detail = personnelAttendanceUserDetailDao.get(absenteeism.getDetailId());
                                }
                            } else if(!absenteeism.getDetailId().equals(detail.getId())) {
                                removeDetailIds.add(absenteeism.getDetailId());
                            }
                        }
                        if (detail == null) {
                            detail = new PersonnelAttendanceUserDetail(pau, WorkdayType.DayOff);
                            detail.setCreator(pam.getCreator());
                            detail.setCreateName(pam.getCreateName());
                            detail.setMiddleBreak(pau.getMiddleBreak());
                            detail.setSource("2");//来源:1-审批,2-录入，3-系统默认旷工
                        }
                        detail.setType(AttendanceType.absenteeism.getName());
                        detail.setWorkdayType(WorkdayType.DayOff.getIndex());
                        detail.setUpdator(pam.getUpdator());
                        detail.setUpdateName(pam.getUpdateName());
                        detail.setBeginTime(absenteeism.GetBegin());
                        detail.setEndTime(absenteeism.GetEnd());
                        detail.setBreakBegin(pau.getBreakBegin());
                        detail.setBreakEnd(pau.getBreakEnd());;
                        if(begin && end) {
                            detail.setState(WorkdayType.Multiple.getState());
                        } else if(begin) {
                            detail.setState(WorkdayType.Workday.getState());
                        } else if(end) {
                            detail.setState(WorkdayType.DayOff.getState());
                        }
                        detail.autoSetDuration();
                        personnelAttendanceUserDetailDao.saveOrUpdate(detail);
                        absenteeism.setDetailId(detail.getId());
                    });
                    pam.setAbsenteeisms(JSON.toJSONString(absenteeisms));
                    pam.setAbsMd5(DigestUtils.md5(pam.getAbsenteeisms()));
                }
                //删除已合并的detail
                removeDetailIds.forEach(id->personnelAttendanceUserDetailDao.deleteById(id));
            }
        }
    }
    private void updatePauWithPaps(PersonnelAttendanceUser pau, List<AttendancePunchDto> paps, Date now) {
        if (ObjectUtils.isNotEmpty(paps) && (pau.getScanTime()==null || pau.getScanTime().before(pau.getScanEndTime()))) {//没有打卡或者扫描结束标识已设置
            Set<PersonnelAttendanceUserDetail> detailSet = pau.getPersonnelAttendanceUserDetailHashSet();
            paps = paps.stream().filter(punch -> punch.getId() > pau.getLastScanPunchId()).toList();
            Set<PersonnelAttendanceUserDetail> removedSet = new HashSet<>(), newSet = new HashSet<>();
            //上次扫描之后有新的打卡
            paps.forEach(punch -> {
                detailSet.forEach(detail -> {
                    WorkdayType workdayType = WorkdayType.getByIndex(detail.getWorkdayType());
                    long beginCheck=detail.getBeginTime().getTime() + pau.getOmitBefore(),endCheck=detail.getEndTime().getTime() - pau.getOmitAfter();
                    if(detail.getBreakBegin()!=null && detail.getBreakEnd()!=null) {
                        if (detail.getBeginTime().getTime() >= detail.getBreakBegin().getTime()) {
                            beginCheck = detail.getBreakEnd().getTime() + pau.getOmitBefore();
                        }
                        if (detail.getEndTime().getTime() <= detail.getBreakEnd().getTime()) {
                            endCheck = detail.getBreakBegin().getTime() - pau.getOmitAfter();
                        }
                    }
                    switch (AttendanceType.getByName(detail.getType())) {
                        case pending://正常班，未打卡
                            if (WorkdayType.checkMultipleState(WorkdayType.getByIndex(punch.getWorkdayType()), WorkdayType.Workday)) {//到岗打卡
                                if (punch.getPunchTime().getTime() > beginCheck && detail.getStartPunchTime() == null) {//迟到
                                    if (detail.getDefaultBeginTime() == null) {
                                        PersonnelAttendanceUserDetail newDetail = SerializationUtils.clone(detail);//新建下段班detail
                                        newDetail.setId(null);
                                        newDetail.setDefaultBeginTime(detail.getBeginTime());
                                        newDetail.setBeginTime(punch.getPunchTime());
                                        newDetail.setStartPunchTime(punch.getPunchTime());
                                        newDetail.setStartPunchId(punch.getId());
                                        newDetail.setPrevId(detail.getId());
                                        newDetail.setNextId(detail.getNextId());
                                        newDetail.setCreator(punch.getUserID());
                                        newDetail.setCreateDate(null);
                                        newDetail.setUpdateDate(null);
                                        if (WorkdayType.Multiple.equals(WorkdayType.getByState(detail.getState()))) {//原details包含上下班，需要分别设置上下班，否则新detail保持null，旧detail不变即可
                                            newDetail.setState(WorkdayType.DayOff.getState());
                                            detail.setState(WorkdayType.Workday.getState());
                                        }
                                        newDetail.autoSetDuration();
                                        personnelAttendanceUserDetailDao.saveOrUpdate(newDetail);
                                        newSet.add(newDetail);
                                        detail.setNextId(newDetail.getId());
                                    }
                                    //当前detail改成迟到或旷工
                                    detail.setEndTime(punch.getPunchTime());
                                    detail.setEndPunchId(punch.getId());
                                    detail.setEndPunchTime(punch.getPunchTime());
                                    detail.setCreator(punch.getUserID());
                                    detail.setUpdator(punch.getUserID());
                                    if (punch.getPunchTime().getTime() > detail.getBeginTime().getTime() + pau.getLateLimit()) {
                                        detail.setType(AttendanceType.absenteeism.getName());
                                    } else {
                                        detail.setType(AttendanceType.late.getName());
                                    }
                                    detail.setWorkdayType(WorkdayType.DayOff.getIndex());
                                    detail.autoSetDuration();
                                }
                            }//穿透到normal
                        case normal://正常班，已打卡/未打卡
                            if (WorkdayType.checkMultipleState(WorkdayType.getByIndex(punch.getWorkdayType()), WorkdayType.Workday)) {//到岗打卡
                                if (punch.getPunchTime().getTime() >= detail.getScanStartTime().getTime() && punch.getPunchTime().getTime() <= beginCheck//正常打卡
                                        && (detail.getStartPunchTime() == null
                                        || punch.getPunchTime().getTime() <= detail.getBeginTime().getTime() && punch.getPunchTime().getTime() > detail.getStartPunchTime().getTime())) {//打卡时间更晚
                                    detail.setStartPunchId(punch.getId());
                                    detail.setStartPunchTime(punch.getPunchTime());
                                    detail.setCreator(punch.getUserID());
                                    detail.setType(AttendanceType.normal.getName());
                                }
                            }//继续穿透，处理离岗打卡
                        case leaveEarly://早退，已打卡
                        case absenteeism://早退旷工，已打卡
                            if (WorkdayType.checkMultipleState(WorkdayType.getByIndex(punch.getWorkdayType()), WorkdayType.DayOff)) {//离岗打卡
                                if (punch.getPunchTime().getTime() >= endCheck && punch.getPunchTime().getTime() <= detail.getScanEndTime().getTime()//正常打卡
                                        && (detail.getEndPunchTime() == null
                                        || punch.getPunchTime().getTime() >= detail.getEndTime().getTime() && punch.getPunchTime().getTime() < detail.getEndPunchTime().getTime())) {//下班后正常打卡时间越早越好
                                    detail.setEndPunchId(punch.getId());
                                    detail.setEndPunchTime(punch.getPunchTime());
                                    detail.setUpdator(punch.getUserID());
                                    detail.setType(AttendanceType.normal.getName());
                                    if (detail.getDefaultEndTime() != null) {
                                        if (punch.getPunchTime().getTime() >= detail.getDefaultEndTime().getTime()) {
                                            detail.setEndTime(detail.getDefaultEndTime());
                                            detail.setDefaultEndTime(null);
                                            PersonnelAttendanceUserDetail next = detail.getNext();//获取下一个班，如果是早退旷工需要删除
                                            if (next != null && WorkdayType.DayOff.getIndex().equals(next.getWorkdayType())//下一个是离岗detail
                                                    && Arrays.asList(AttendanceType.leaveEarly, AttendanceType.absenteeism).contains(AttendanceType.getByName(next.getType()))) {//是早退或旷工
                                                detail.setNextId(next.getNextId());
                                                removedSet.remove(next);
                                                personnelAttendanceUserDetailDao.delete(next);
                                            }
                                        } else {
                                            detail.setEndTime(punch.getPunchTime());
                                        }
                                    }
                                    detail.autoSetDuration();
                                } else if (punch.getPunchTime().getTime() < endCheck) {//早退
                                    if (WorkdayType.Workday.equals(workdayType) && detail.getDefaultBeginTime() != null) {//detail是班 第一次打下班卡早退
                                        PersonnelAttendanceUserDetail newDetail = SerializationUtils.clone(detail);//新建上段班detail
                                        newDetail.setId(null);
                                        newDetail.setDefaultEndTime(detail.getEndTime());
                                        newDetail.setEndTime(punch.getPunchTime());
                                        newDetail.setEndPunchTime(punch.getPunchTime());
                                        newDetail.setEndPunchId(punch.getId());
                                        newDetail.setPrevId(detail.getPrevId());
                                        newDetail.setNextId(detail.getId());
                                        newDetail.setCreator(punch.getUserID());
                                        newDetail.setUpdator(punch.getUserID());
                                        newDetail.setCreateDate(null);
                                        newDetail.setUpdateDate(null);
                                        if (WorkdayType.Multiple.equals(WorkdayType.getByState(detail.getState()))) {//原details包含上下班，需要分别设置上下班，否则新detail保持null，旧detail不变即可
                                            newDetail.setState(WorkdayType.Workday.getState());
                                            detail.setState(WorkdayType.DayOff.getState());
                                        }
                                        newDetail.autoSetDuration();
                                        personnelAttendanceUserDetailDao.saveOrUpdate(newDetail);
                                        newSet.add(newDetail);
//                                        detail.setDefaultBeginTime(detail.getBeginTime());
                                        detail.setPrevId(newDetail.getId());
                                        detail.setBeginTime(punch.getPunchTime());
                                        detail.setStartPunchId(punch.getId());
                                        detail.setStartPunchTime(punch.getPunchTime());
                                        detail.setCreator(punch.getUserID());
                                        detail.setUpdator(punch.getUserID());
                                        if (punch.getPunchTime().getTime() < pau.getEndTime().getTime() - pau.getEarlyLimit()) {
                                            detail.setType(AttendanceType.absenteeism.getName());
                                        } else {
                                            detail.setType(AttendanceType.leaveEarly.getName());
                                        }
                                        detail.setWorkdayType(WorkdayType.DayOff.getIndex());
                                        detail.autoSetDuration();
                                    }
                                    //在正常班部分处理，此处重复
//                                    if(WorkdayType.DayOff.equals(workdayType) && punch.getPunchTime().getTime() >= detail.getStartPunchTime().getTime()) {//打卡时间更晚
//                                        detail.setBeginTime(punch.getPunchTime());
//                                        detail.setStartPunchId(punch.getId());
//                                        detail.setStartPunchTime(punch.getPunchTime());
//                                        if (punch.getPunchTime().getTime() < pau.getEndTime().getTime() - pau.getEarlyLimit()) {
//                                            detail.setType(AttendanceType.absenteeism.getName());
//                                        } else {
//                                            detail.setType(AttendanceType.leaveEarly.getName());
//                                        }
//                                        detail.autoSetDuration();
//                                        PersonnelAttendanceUserDetail prev = detail.getPrev();//获取上一个班，修改结束时间
//                                        if (prev != null && WorkdayType.Workday.getIndex().equals(prev.getWorkdayType())) {
//                                            prev.setEndTime(punch.getPunchTime());
//                                            prev.setEndPunchTime(punch.getPunchTime());
//                                            prev.setEndPunchId(punch.getId());
//                                        }
//                                    }
                                }
                            }
                            break;
                        case overtime://加班，已打卡/未打卡
                            if (WorkdayType.checkMultipleState(WorkdayType.getByIndex(punch.getWorkdayType()), WorkdayType.Workday)) {//到岗打卡
                                if (punch.getPunchTime().getTime() >= detail.getScanStartTime().getTime() && detail.getStartPunchTime() == null //第一次打，正常或者迟到
                                        || punch.getPunchTime().getTime() >= detail.getScanStartTime().getTime() && punch.getPunchTime().getTime() <= detail.getBeginTime().getTime() + pau.getOmitBefore()//正常打卡
                                        && punch.getPunchTime().getTime() <= detail.getBeginTime().getTime() && punch.getPunchTime().getTime() > detail.getStartPunchTime().getTime()) {//正常打卡 打卡时间更晚
                                    detail.setStartPunchId(punch.getId());
                                    detail.setStartPunchTime(punch.getPunchTime());
                                    if (detail.getDefaultBeginTime() == null && punch.getPunchTime().getTime() > detail.getBeginTime().getTime()) {//加班迟到
                                        detail.setDefaultBeginTime(detail.getBeginTime());
                                    }
                                    detail.setBeginTime(new Date(Math.max(detail.getBeginTime().getTime(), punch.getPunchTime().getTime())));
                                    detail.autoSetDuration();
                                }
                            }
                            if (WorkdayType.checkMultipleState(WorkdayType.getByIndex(punch.getWorkdayType()), WorkdayType.DayOff)) {//离岗打卡
                                if (punch.getPunchTime().getTime() >= detail.getEndTime().getTime() - pau.getOmitAfter() && (detail.getEndPunchTime() == null //第一次打，正常或者早退
                                        || punch.getPunchTime().getTime() >= detail.getEndTime().getTime() - pau.getOmitAfter() && punch.getPunchTime().getTime() <= detail.getScanEndTime().getTime() //正常打卡
                                        && punch.getPunchTime().getTime() < detail.getEndPunchTime().getTime())) {//正常打卡 越早越好
                                    detail.setEndPunchId(punch.getId());
                                    detail.setEndPunchTime(punch.getPunchTime());
                                    if (detail.getDefaultEndTime() == null && punch.getPunchTime().getTime() < detail.getEndTime().getTime()) {//第一次打卡，下班早退
                                        detail.setDefaultEndTime(detail.getEndTime());
                                        detail.setEndTime(new Date(Math.min(detail.getEndTime().getTime(), punch.getPunchTime().getTime())));
                                    } else if (detail.getDefaultEndTime() != null) {//早退情况下又打了卡
                                        if (punch.getPunchTime().getTime() < detail.getDefaultEndTime().getTime()) {//还是早退
                                            detail.setEndTime(punch.getPunchTime());
                                        } else {//早退打卡后又打了正常加班离岗
                                            detail.setEndTime(detail.getDefaultEndTime());
                                            detail.setDefaultEndTime(null);
                                        }
                                    }
                                    detail.autoSetDuration();
                                }
                            }
                            break;
                        case leave://请假，已打卡/未打卡
                        case goOut:
                        case travel:
                            if (WorkdayType.checkMultipleState(WorkdayType.getByIndex(punch.getWorkdayType()), WorkdayType.DayOff)) {//离岗打卡
                                if (punch.getPunchTime().getTime() >= detail.getBeginTime().getTime() && punch.getPunchTime().getTime() <= detail.getScanStartTime().getTime()) {//正常打卡 越早越好
                                    detail.setStartPunchId(punch.getId());
                                    detail.setStartPunchTime(punch.getPunchTime());
                                }
                            }
                            if (WorkdayType.checkMultipleState(WorkdayType.getByIndex(punch.getWorkdayType()), WorkdayType.Workday)) {//到岗打卡
                                if (punch.getPunchTime().getTime() <= detail.getEndTime().getTime() && punch.getPunchTime().getTime() >= detail.getScanEndTime().getTime()
                                        && (detail.getEndPunchTime() == null || detail.getEndPunchTime().getTime() < punch.getPunchTime().getTime())) {//正常打卡 越晚越好
                                    detail.setEndPunchId(punch.getId());
                                    detail.setEndPunchTime(punch.getPunchTime());
                                }
                            }
                            break;
                    }
                });
                detailSet.removeAll(removedSet);
                detailSet.addAll(newSet);
                removedSet.clear();
                newSet.clear();
            });
            pau.setLastScanPunchId(Math.max(pau.getLastScanPunchId(), paps.stream().map(AttendancePunchDto::getId).max(Integer::compareTo).orElse(0)));
            pau.setScanTime(new Date(Math.min(now.getTime(), pau.getScanEndTime().getTime())));//设置扫描标识
        }
    }
    private void fillDetailsNames(User defaultUser, Collection<PersonnelAttendanceUserDetail> details) {
        Assert.notNull(defaultUser, "用户不能为空");
        details.stream().forEach(detail -> {
            if(defaultUser.getUserID().equals(detail.getCreator()) && !defaultUser.getUserName().equals(detail.getCreateName())) {
                detail.setCreateName(defaultUser.getUserName());
            }
            if(defaultUser.getUserID().equals(detail.getUpdator()) && !defaultUser.getUserName().equals(detail.getUpdateName())) {
                detail.setUpdateName(defaultUser.getUserName());
            }
            if(detail.getCreator()!=null && !defaultUser.getUserID().equals(detail.getCreator()) && (StringUtils.isBlank(detail.getCreateName())||detail.getCreateName().equals("系统"))) {
                User u = userService.getUserByID(detail.getCreator());
                if(u!=null) {
                    detail.setCreateName(u.getUserName());
                }
            }
            if(detail.getUpdator()!=null && !defaultUser.getUserID().equals(detail.getUpdator()) && (StringUtils.isBlank(detail.getUpdateName())||detail.getUpdateName().equals("系统"))) {
                User u = userService.getUserByID(detail.getUpdator());
                if(u!=null) {
                    detail.setUpdateName(u.getUserName());
                }
            }
        });
    }
    private void setDetailSysAbsenteeism(PersonnelAttendanceUser pau, Date now) {//考勤宝，班时间段未打卡处理
        Set<PersonnelAttendanceUserDetail> detailSet = pau.getPersonnelAttendanceUserDetailHashSet();
        detailSet.stream().filter(detail -> detail.getDefaultBeginTime()==null && detail.getDefaultEndTime()==null//原始detail未被打卡修改
                && (detail.getStartPunchId()==null || detail.getEndPunchId()==null)//未打卡或缺打卡
                && (AttendanceType.pending.getName().equals(detail.getType()) || AttendanceType.normal.getName().equals(detail.getType()))//状态或者正常
                && (pau.getLateLimit()!=null && pau.getEarlyLimit()!=null && now.after(detail.getScanEndTime()))//考勤宝超过扫描时间未打卡
        ).forEach(detail -> {
            if(detail.getStartPunchId()!=null && detail.getPrev()!=null && (AttendanceType.late.getName().equals(detail.getPrev().getType()) || AttendanceType.absenteeism.getName().equals(detail.getPrev().getType()))) {
                detail.setPrevId(detail.getPrev().getPrevId());
                detail.setBeginTime(detail.getDefaultBeginTime());
                detail.setDefaultBeginTime(null);
                personnelAttendanceUserDetailDao.delete(detail.getPrev());
            }
            if(detail.getEndPunchId()!=null && detail.getNext()!=null && (AttendanceType.leaveEarly.getName().equals(detail.getNext().getType()) || AttendanceType.absenteeism.getName().equals(detail.getNext().getType()))) {
                detail.setNextId(detail.getNext().getNextId());
                detail.setEndTime(detail.getDefaultEndTime());
                detail.setDefaultEndTime(null);
                personnelAttendanceUserDetailDao.delete(detail.getNext());
            }
            detail.setWorkdayType(WorkdayType.DayOff.getIndex());
            detail.setType(AttendanceType.absenteeism.getName());
            detail.setReason("系统默认旷工");
            personnelAttendanceUserDetailDao.saveOrUpdate(detail);
        });
    }
    private void updatePausByDetails(PersonnelAttendanceUser pau) {
        Set<PersonnelAttendanceUserDetail> detailSet = pau.getPersonnelAttendanceUserDetailHashSet();
        User user;
        if((user = userService.getUserByID(pau.getUser()))!=null) {
            fillDetailsNames(user, detailSet);
        }
        detailSet.stream().filter(detail->detail.getState()!=null).sorted(Comparator.comparing(PersonnelAttendanceUserDetail::getState)).forEach(detail->{
            if(!AttendanceType.pending.getName().equals(detail.getType())) {
                switch (WorkdayType.getByState(detail.getState())) {
                    case Multiple:
                        pau.setBeginState(detail.getType());
                        pau.setEndState(detail.getType());
                        pau.setAmAttendance(detail.getStartPunchTime()!=null ? detail.getStartPunchTime() : detail.getCreateDate());
                        pau.setPmAttendance(detail.getEndPunchTime()!=null ? detail.getEndPunchTime() : detail.getUpdateDate());
                        pau.setCreator(detail.getCreator());
                        pau.setCreateName(detail.getCreateName());
                        pau.setUpdator(detail.getUpdator());
                        pau.setUpdateName(detail.getUpdateName());
                        break;
                    case Workday:
                        if(WorkdayType.Workday.getIndex().equals(detail.getWorkdayType())) {//detail是班
                            pau.setPmAttendance(detail.getStartPunchTime() != null ? detail.getStartPunchTime() : detail.getCreateDate());
                        } else {//detail是假
                            pau.setPmAttendance(detail.getEndPunchTime() != null ? detail.getEndPunchTime() : detail.getUpdateDate());
                        }
                        pau.setBeginState(detail.getType());
                        pau.setCreator(detail.getCreator());
                        pau.setCreateName(detail.getCreateName());
                        pau.setUpdator(detail.getUpdator());
                        pau.setUpdateName(detail.getUpdateName());
                        break;
                    case DayOff:
                        if(WorkdayType.DayOff.getIndex().equals(detail.getWorkdayType())) {//detail是假
                            pau.setPmAttendance(detail.getStartPunchTime() != null ? detail.getStartPunchTime() : detail.getCreateDate());
                        } else {//detail是班
                            pau.setPmAttendance(detail.getEndPunchTime() != null ? detail.getEndPunchTime() : detail.getUpdateDate());
                        }
                        pau.setEndState(detail.getType());
                        pau.setAmAttendance(detail.getEndPunchTime()!=null ? detail.getEndPunchTime() : detail.getUpdateDate());
                        pau.setUpdator(detail.getUpdator());
                        pau.setUpdateName(detail.getUpdateName());
                        break;
                }
            }
        });
        //使用detail的状态设置考勤总状态
        final Set<String> mTypes = AttendanceType.getMultiple().stream().map(AttendanceType::getName).collect(Collectors.toSet());
        Set<AttendanceType> types = new HashSet<>();
        detailSet.stream().filter(detail -> mTypes.contains(detail.getType())).forEach(detail-> types.add(AttendanceType.getByName(detail.getType())));
        if(types.size()>1) {//多余一种非正常状态，其他
            pau.setType(AttendanceType.other.getIndex());
        } else {//设置唯一的非正常状态，或者正常/待定/无须考勤状态
            AttendanceType defaultType = Boolean.TRUE.equals(pau.getNoNeed()) ? AttendanceType.noNeed : (AttendanceType.getByName(pau.getBeginState())!=null ? AttendanceType.getByName(pau.getBeginState()):AttendanceType.pending);
            pau.setType(types.stream().findFirst().orElse(defaultType).getIndex());
        }
//        //使用开始结束detail的状态设置考勤总状态
//        if(pau.getNoNeed()) {
//            pau.setType(AttendanceType.noNeed.getIndex());
//        } else if(pau.getBeginState().equals(pau.getEndState()) || AttendanceType.getMultiple().contains(AttendanceType.getByName(pau.getBeginState())) && !AttendanceType.getMultiple().contains(AttendanceType.getByName(pau.getEndState()))) {
//            pau.setType(AttendanceType.getByName(pau.getBeginState()).getIndex());
//        } else if(!AttendanceType.getMultiple().contains(AttendanceType.getByName(pau.getBeginState())) && AttendanceType.getMultiple().contains(AttendanceType.getByName(pau.getEndState()))) {
//            pau.setType(AttendanceType.getByName(pau.getEndState()).getIndex());
//        } else {
//            pau.setType(AttendanceType.other.getIndex());
//        }
        updatePersonnelAttendanceMonthly(pau);
    }

    private void updatePersonnelAttendanceMonthly(PersonnelAttendanceUser pau) {
        Set<PersonnelAttendanceUserDetail> detailSet = pau.getPersonnelAttendanceUserDetailHashSet();
        User user = userService.getUserByID(pau.getUser());
        Date day = pau.getAttendanceDate();
        personnelAttendanceMonthlyService.ClearDay(user, day);
        if(Boolean.TRUE.equals(pau.getNoNeed())) {
            personnelAttendanceMonthlyService.SetNoNeed(user, day, Boolean.TRUE);
        }
        if(detailSet.size()>0) {
            boolean late=false,leaveEarly=false;
            byte leave=0,travel=0,outside=0,absenteeisme=0,overtime=0;
            Double workingHours=0.0,leaveDuration=0.0,overtimeDuration=0.0;
            Iterator<PersonnelAttendanceUserDetail> iterator = detailSet.iterator();
            while(iterator.hasNext()) {
                PersonnelAttendanceUserDetail detail = iterator.next();
                switch (AttendanceType.getByName(detail.getType())) {
                    case late:
                        late=true;
                        break;
                    case leaveEarly:
                        leaveEarly=true;
                        break;
                    case leave:
                        leave++;
                        leaveDuration+=detail.getDuration();
                        break;
                    case travel:
                        travel++;
                        break;
                    case goOut:
                        outside++;
                        break;
                    case absenteeism:
                        absenteeisme++;
                        break;
                    case normal:
                    case pending:
                        workingHours+=detail.getDuration();
                        break;
                    case overtime:
                        overtime++;
                        overtimeDuration+=detail.getDuration();
                        break;
                }
            }
            if(late) {
                personnelAttendanceMonthlyService.SetLate(user, day, late);
            }
            if(leaveEarly) {
                personnelAttendanceMonthlyService.SetLeaveEarly(user, day, leaveEarly);
            }
            if(leave>0) {
                personnelAttendanceMonthlyService.SetLeave(user, day, leave, leaveDuration.floatValue());
            }
            if(travel>0) {
                personnelAttendanceMonthlyService.SetTravel(user, day, travel);
            }
            if(outside>0) {
                personnelAttendanceMonthlyService.SetOutside(user, day, outside);
            }
            if(absenteeisme>0) {
                personnelAttendanceMonthlyService.SetAbsenteeisme(user, day, absenteeisme);
            }
            if(workingHours>0) {
                personnelAttendanceMonthlyService.SetWorkingHours(user, day, workingHours.floatValue());
            }
            if(overtime>0) {
                personnelAttendanceMonthlyService.SetOvertime(user, day, overtime, overtimeDuration.floatValue());
            }
        }
    }

    @Override
    public void setAttendanceUserEvent(User user, Date startTime, AttendanceType type) {
        Assert.notNull(startTime, "startTime must not be null!");
        if(!AttendanceType.getEventTypes().contains(type)) {
            logger.warn("考勤事件不合法，请检查代码，Attendance type is not supported! type : " + type + ",\n只允许下列类型的考勤事件 : " + AttendanceType.getEventTypes(), new IllegalArgumentException("考勤事件不合法，请检查代码，Attendance type is not supported!"));
//            throw new IllegalArgumentException("考勤事件不合法，请检查代码，Attendance type is not supported! type : " + type + ",\n只允许下列事件 : " + AttendanceType.getEventTypes());
        }
        PersonnelAttendanceUser pau = getOrAddNewPersonnelAttendanceUser(user, NewDateUtils.today(startTime));
        //更新pau的扫描时间不晚于startTime
        Date scanTime = pau.getScanTime();
        if(scanTime==null || scanTime.after(startTime)) {
            pau.setScanTime(startTime);
            personnelAttendanceUserDao.saveOrUpdate(pau);
        }
        redisTemplate.opsForList().leftPush(attendanceUserEventKey, Triple.of(user.getUserID(), startTime, type));
        System.out.println(redisTemplate.opsForList().size(attendanceUserEventKey));
    }
    @Override
    public void setAttendanceUserEvent(Pair<Integer, WorkOffBlockDto> eventMap) {
        Date startTime = eventMap.getRight().GetBegin();
        if(startTime.before(NewDateUtils.getLastTimeOfDay(NewDateUtils.today()))) {
            AttendanceType type = eventMap.getRight().getAttendanceType();
            User user = userService.getUserByID(eventMap.getLeft());
            setAttendanceUserEvent(user, startTime, type);
        }
    }
    @Override
    public void updateAttendanceUserEvents() {
        Triple<Integer, Date, AttendanceType> eventMap;
        Set<String> duplicate=new HashSet<>();
        while((eventMap = (Triple<Integer, Date, AttendanceType>) redisTemplate.opsForList().rightPop(attendanceUserEventKey))!=null) {
            Date date = NewDateUtils.today(eventMap.getMiddle());
            String dupkey=eventMap.getLeft()+":"+date.getTime();
            if(!duplicate.contains(dupkey)) {//重复pau只处理一次
                duplicate.add(dupkey);
                User user = userService.getUserByID(eventMap.getLeft());
                PersonnelAttendanceUser pau = getOrAddNewPersonnelAttendanceUser(user, date);
                List<WorkOffBlockDto> offBlocks = new ArrayList<>();//离岗申请块
                List<WorkOffBlockDto> workBlocks = new ArrayList<>();//到岗块
                boolean isWorkday = Boolean.FALSE.equals(pau.getNoNeed());
                switch (eventMap.getRight()) {
                    case punch://考勤打卡
//                    Map<String, Object>params = new HashMap<>(4) {{
//                        put("id", pau.getLastScanPunchId());
//                        put("user", pau.getUser());
//                        put("startTime", pau.getScanStartTime());
//                        put("endTime", new Date(Math.min(pau.getScanEndTime().getTime(), now.getTime())));
//                    }};
//                    List<AttendancePunchDto> paps = personnelAttendancePunchDao.getListByHQLWithNamedParams("select new cn.sphd.miners.modules.generalAffairs.dto.AttendancePunchDto(id,user,punchTime,workdayType,punchType,type,businessService,business,createTime) from PersonnelAttendancePunch where id>:id and user=:user and punchTime between :startTime and :endTime order by punchTime", params);
//                    updatePauWithPaps(pau, paps, now);
                        break;
                    case workdayLieu://考勤录入
                        PersonnelAttendanceManual pam = personnelAttendanceManualService.getByPau(pau);
                        updatePauWithPam(pau, pam);
                        break;
                    case leave://请假申请
                        if (isWorkday) {//工作日需要检查请假
                            offBlocks.addAll(getAttendanceEventService("leaveService").getBlocks(user, NewDateUtils.today(eventMap.getMiddle()), NewDateUtils.getLastTimeOfDay(eventMap.getMiddle())));
                        }
                        break;
                    case overtime://加班申请
                        workBlocks.addAll(getAttendanceEventService("overtimeService").getBlocks(user, NewDateUtils.today(eventMap.getMiddle()), NewDateUtils.getLastTimeOfDay(eventMap.getMiddle())));
                        break;
                    default:
                        throw new RuntimeException("考勤事件不合法，请检查队列代码，Attendance type is not supported! type :" + eventMap.getRight());
                }
                updateAttendanceUserScanStartEndTimeByWorkBlocks(pau, workBlocks);
                //生成考勤明细表
                pau = createOrUpdateAttendanceDetail(pau, offBlocks, workBlocks);
                pau.setScanTime(null);
                pau.setLastScanPunchId(0);
                runAttendance(pau, new Date(Math.min(NewDateUtils.getLastTimeOfDay(date).getTime(), System.currentTimeMillis())));//考勤扫描
            }
        }
    }
    /**
     * fillDefalutPersonnelAttendanceException 按当前需求生成设置默认作息时间，判断并生成当前至下月底的作息时间
     * @apiNote 从给定日期开始，生成作息时间到给定日期和当前日期的较大值的下个月月底
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @param currentTime
     * @return void
     * @date 2025-04-23 09:21:07
     **/
    private void fillDefalutPersonnelAttendanceException(Integer oid, Date currentTime) {
        fillDefalutPersonnelAttendanceException(oid, currentTime, NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(new Date(Math.max(currentTime.getTime(), NewDateUtils.getLastTimeOfToday().getTime())),1)));
    }
     /**
     * fillPersonnelAttendanceException 设置默认日期段的作息时间
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @param beginTime
     * @param endTime
     * @return boolean true 有生成，false 没有生成，已有数据
     * @date 2025-04-23 09:18:15
     **/
    private boolean fillDefalutPersonnelAttendanceException(Integer oid, Date beginTime, Date endTime) {
        //设置默认值
        if(beginTime == null) {
            beginTime = NewDateUtils.today();
        }
        if(endTime == null) {
            endTime = NewDateUtils.getLastTimeOfMonth(beginTime);
        }
        //变量时间值归零
        beginTime = NewDateUtils.today(beginTime);
        endTime = NewDateUtils.today(endTime);
        //天数和作息记录数相同就不需要补，如果不同需要在没有数据的日期插入
        if (countPersonnelAttendanceException(oid, beginTime, endTime) < NewDateUtils.countDates(beginTime, endTime)) {
            Session session = personnelAttendanceExceptionDao.getSession();
            Transaction transaction = session.getTransaction();
            boolean commitBegin = true;
            if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
                commitBegin = false;
            }
            List<PersonnelAttendanceException> personnelAttendanceExceptions = getPersonnelAttendanceExceptionByMonth(oid, beginTime, endTime);
            Set<Date> existDays = personnelAttendanceExceptions.stream().map(item->NewDateUtils.today(item.getExceptionDate())).collect(Collectors.toSet());
            List<Date> dayList = NewDateUtils.getEveryDate(beginTime,endTime);
            for (Date day : dayList) {
                if(!existDays.contains(day)) {
                    addPersonnelAttendanceException(oid, day, null, null);
                }
            }
            transaction.commit();
            if (commitBegin) {
                transaction.begin();
            }
            return true;
        } else {
            return false;
        }
    }
    private long countPersonnelAttendanceException(Integer oid, Date beginTime, Date endTime) {
        String hql = "select count(*) from PersonnelAttendanceException where org=:org and exceptionDate between :beginTime and :endTime";
        Map<String, Object> params = new HashMap<>(3){{
            put("org", oid);
            put("beginTime", beginTime);
            put("endTime", endTime);
        }};
        return (Long)personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, params);
    }
    /**
     * addPersonnelAttendanceException 设置指定日志的作息时间
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @param exceptionDate
     * @param type
     * @param user
     * @return void
     * @date 2025-04-22 17:09:11
     **/
    private void addPersonnelAttendanceException(Integer oid, Date exceptionDate, WorkdayType type, User user) {
        PersonnelAttendanceException personnelAttendanceException = new PersonnelAttendanceException();
        personnelAttendanceException.setOrg(oid);
        if (user != null) {
            personnelAttendanceException.setCreator(user.getUserID());
            personnelAttendanceException.setCreateName(user.getUserName());
        } else {
            personnelAttendanceException.setCreateName("系统");
        }
        if (type==null) {   //type为空，或者为null：1-假，2-班
            type = NewDateUtils.isWeekend(exceptionDate) ? WorkdayType.DayOff : WorkdayType.Workday;
        }
        personnelAttendanceException.setType(type); //1-假 2-班
        personnelAttendanceException.setExceptionDate(exceptionDate); //考勤日期
        personnelAttendanceExceptionDao.save(personnelAttendanceException);
    }
    /**
     * hasPersonnelAttendanceExceptionByMonth 判断机构是否已经设置作息表
     * @apiNote 两个时间值都是空的话，查机构全部作息时间，如果只给出一个时间，beginTime查该时间到月底，endTime查月初到该时间
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @param beginTime
     * @param endTime
     * @return boolean
     * @date 2025-04-23 09:09:04
     **/
    @Override
    public boolean hasPersonnelAttendanceExceptionByMonth(@NotNull Integer oid, Date beginTime, Date endTime) {
        if(beginTime==null && endTime!=null) {
            beginTime = NewDateUtils.changeMonth(endTime, 0);
        } else if(endTime==null && beginTime!=null) {
            endTime = NewDateUtils.getLastTimeOfMonth(beginTime);
        }
        StringBuilder hql = new StringBuilder("select 1 from PersonnelAttendanceException where org=:org");
        Map<String, Object> params = new HashMap<>(){{
            put("org", oid);
        }};
        if (beginTime != null/* && endTime != null*/) {
            hql.append(" and exceptionDate between :beginTime and :endTime");
            params.put("beginTime", beginTime);
            params.put("endTime", endTime);
        }
        return personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql.toString(), params) != null;
    }
    /**
     * getPreWorkDate 取距离今天/昨天最近的日期，此日期必须是“班” 1-假，2-班
     * <AUTHOR>
     * @since 4.0
     * @param currendDate
     * @param oid
     * @return Date
     * @date 2025-06-04 16:09:13
     **/
    @Override
    public Date getPreWorkDate(Date currendDate,Integer oid) {
        Date date = NewDateUtils.getLastTimeOfDay(currendDate);
        String hql = "from PersonnelAttendanceException where exceptionDate<=:date and org=:oid and type=:type order by exceptionDate desc";//type 1-假 2-班
        HashMap<String, Object> params = new HashMap<>(3) {{
            put("date", date);
            put("oid", oid);
            put("type", WorkdayType.Workday.getName());
        }};
        PersonnelAttendanceException exception = (PersonnelAttendanceException) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, params);
        if (exception != null) {
            return exception.getExceptionDate();
        } else {
            return NewDateUtils.getMaxDate();
        }
    }
    /**
     * getCurrentWorkDate 获取当前及之后第一个工作日，数据有缓存
     * <AUTHOR>
     * @since 4.0
     * @param currendDate
     * @param oid
     * @return Date
     * @date 2025-06-05 09:08:17
     **/
    private Date getCurrentWorkDate(Date currendDate, Integer oid) {
        Date date = NewDateUtils.today(currendDate);
        String cacheName = "miners:Attendance:getCurrentWorkDate:" + oid + ":" + date.getTime();
        Date result = (Date) redisTemplate.opsForValue().get(cacheName);
        if(result!=null) {
            return result;
        } else {
            String hql = "from PersonnelAttendanceException where exceptionDate>=:date and org=:oid and type=:type order by exceptionDate";//type 1-假 2-班
            HashMap<String, Object> params = new HashMap<>(3) {{
                put("date", date);
                put("oid", oid);
                put("type", WorkdayType.Workday.getName());
            }};
            PersonnelAttendanceException exception = (PersonnelAttendanceException) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, params);
            if (exception != null && (result=exception.getExceptionDate())!=null) {
                redisTemplate.opsForValue().set(cacheName, result, 15, TimeUnit.DAYS);
                return result;
            } else {
                return NewDateUtils.getMaxDate();
            }
        }
    }
    private Date getNextDateByWork(Date currendDate, Integer oid) {
        return getCurrentWorkDate(NewDateUtils.tomorrow(currendDate), oid);
    }
    /**
     * clearWorkDateCaches 清除工作日缓存，更新或者删除作息时间后需要调用
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @return void
     * @date 2025-06-05 09:26:16
     **/
    @Override
    public void clearWorkDateCaches(Integer oid) {
        String cacheName = "miners:Attendance:getCurrentWorkDate:" + oid + ":*";
        ScanOptions options = ScanOptions.scanOptions().match(cacheName).count(1000L).build();
        // 放在try中自动释放cursor
        try (Cursor<byte[]> cursor = redisTemplate.getConnectionFactory().getConnection().scan(options)) {
            while (cursor.hasNext()) {
                byte[] key = cursor.next();
                redisTemplate.delete(key);
            }
        }
    }
    @Override
    public Date getInputTime(Integer oid, Date openDate) {
        Date result = null, inputTime;
        PersonnelAttendanceConfig personnelAttendanceConfig = getOnePersonnelAttendanceConfigByOpenDate(oid, openDate);
        if (personnelAttendanceConfig != null) {
            if (ConfigAttendancePattern.manualEntry.getIndex().equals(personnelAttendanceConfig.getAttendancePattern())) {  //手动录入
                if (personnelAttendanceConfig.getOldCreateTime() != null && personnelAttendanceConfig.getEffectDate().getTime() > new Date().getTime()) {
                    inputTime = personnelAttendanceConfig.getOldCreateTime();
                } else {
                    inputTime = personnelAttendanceConfig.getInputTime();
                }
                if (inputTime != null) {
                    return NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(inputTime, "HH:mm")); //上班时间，拼接上年月日
                }
             } else if (ConfigAttendancePattern.attendanceTreasure.getIndex().equals(personnelAttendanceConfig.getAttendancePattern())) {  //考勤宝
                Date beginTime = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfig.getBeginTime(), "HH:mm:ss")); //上班时间，拼接上年月日
                Short lateLimit = personnelAttendanceConfig.getLateLimit();  //迟到时限(分钟)
                long lateLimitMills = TimeUnit.MINUTES.toMillis(lateLimit); //将分钟转换成毫秒
                result = new Date(beginTime.getTime() + lateLimitMills);
            }
        }
        return result;
    }
    @Override    //根据部门查找规则
    public PersonnelAttendanceConfig getOnePersonnelAttendanceConfigByOpenDate(Integer oid, Date openDate) {
        List<PersonnelAttendanceConfig> list = getPersonnelAttendanceConfigsByOpenDate(oid, openDate);
        if (!list.isEmpty()) {
            return list.get(0);
        } else {
            return null;
        }
    }
    /**
     * 添加新增考勤人员，当月变更前为无需考勤
     *
     * @param oid
     * @param day
     */
    private void setPersonelAttendanceNoNeed(Integer oid, Date day) {
        if (NewDateUtils.changeMonth(day, 0).getTime() == day.getTime()) {//每月1日无需修改
            return;
        }
        List<String> todayDetpList, yesterdayDetpList;
        PersonnelAttendanceConfig personnelAttendanceConfig = getOnePersonnelAttendanceConfigByOpenDate(oid, day);
        if (personnelAttendanceConfig.getOpenDate().getTime() == day.getTime()) {//考勤生效日，同时处理部门包括个人
            todayDetpList = getDeptIds(day, oid);
            if (todayDetpList.isEmpty()) {//今天没有设置考勤
                return;
            }
            yesterdayDetpList = getDeptIds(NewDateUtils.yesterday(day), oid);
            Set<Integer> uids = new HashSet<>();
            List<UserDto> todayUserDtos = getUser(day, oid, todayDetpList);
            for (UserDto userDto : todayUserDtos) {
                uids.add(userDto.getUserID());
            }
            List<UserDto> yesterdayUserDtos = getUser(day, oid, yesterdayDetpList);
            for (UserDto userDto : yesterdayUserDtos) {
                uids.remove(userDto.getUserID());
            }
            for (Integer uid : uids) {
                personnelAttendanceMonthlyService.SetNewUserNoNeed(userService.getUserByID(uid), NewDateUtils.yesterday(day), null);
            }
//            //已经处理过了，个人部分直接设置isUsed即可。
//            List<UserDepartmentHistory> dailyUserMonitors = getDailyUserMonitor(day, oid);
//            for (UserDepartmentHistory userMonitor : dailyUserMonitors) {
//                userMonitor.setUsed(true);
//            }
        } else { //处理个人变更部门
            List<UserDepartmentHistory> dailyUserMonitors = getDailyUserMonitor(day, oid);
            if (!dailyUserMonitors.isEmpty()) {
                todayDetpList = getDeptIds(day, oid);
                if (todayDetpList.isEmpty()) {//今天没有设置考勤
                    return;
                }
                yesterdayDetpList = getDeptIds(NewDateUtils.yesterday(day), oid);
                for (UserDepartmentHistory userMonitor : dailyUserMonitors) {
                    User user;
                    if (!yesterdayDetpList.contains(userMonitor.getOldDeptId()) && todayDetpList.contains(userMonitor.getNewDeptId())
                            && (user=userService.getUserByID(userMonitor.getUser()))!=null) { //从无需考勤到需要考勤
                        personnelAttendanceMonthlyService.SetNewUserNoNeed(user, NewDateUtils.yesterday(day), null);
                    }
                    userMonitor.setUsed(true);
                }
            }
        }
    }
    /**
     * getUser 机构部门指定时间需要考勤的人员，有缓存，请勿修改参数和返回值
     * <AUTHOR>
     * @since 4.0
     * @param currentTime
     * @param oid
     * @param deptIds 空的话默认返回所有参与考勤的部门
     * @return List<UserDto>
     * @date 2025-04-23 15:50:40
     **/
    private List<UserDto> getUser(Date currentTime, Integer oid, List<String> deptIds) {
        List<UserDto> result;
        if (deptIds == null || deptIds.isEmpty()) {
            deptIds = getDeptIds(currentTime, oid);
        }
        if(deptIds.isEmpty()) {
            return Collections.emptyList();
        }
        String deptIdsString = StringUtils.join(deptIds, ",");
        String cacheName = "miners:Attendance:getUser:" + oid + ":" + currentTime.getTime() + ":" + deptIdsString;
        String userDtosString = (String) redisTemplate.opsForValue().get(cacheName);
        if (userDtosString == null || userDtosString.isEmpty()) {
            List<UserDto> addList = new ArrayList<>();
            List<Integer> removeUids = getUserChange(currentTime, oid, deptIds, null, addList);
            result = userService.getUserDtoByOidAndDeptIdsLocking(oid, null, deptIds, null, removeUids);
            if (!addList.isEmpty()) {
                result.addAll(addList);
            }
            if (!result.isEmpty()) {
                redisTemplate.opsForValue().set(cacheName, JSON.toJSONString(result), 10, TimeUnit.DAYS);
            }
        } else {
            result = JSON.parseArray(userDtosString, UserDto.class);
        }
        return result;
    }
    private List<UserDto> getUser(Date currentTime, Integer oid, Integer userId, String deptId, String userName) {
        List<String> deptIds;
        deptIds = getDeptIds(currentTime, oid);
        if(!StringUtils.isBlank(deptId)) {
            if(deptIds.contains(deptId)) {//如果deptId是需要考勤的部门
                deptIds = new ArrayList<>(1) {{add(deptId);}};
            } else {//如果deptId部门无需考勤
                return Collections.emptyList();
            }
        }
        if (userId == null && userName == null) {
            return getUser(currentTime, oid, deptIds);
        } else {
            List<UserDto> addList = new ArrayList<>();
            List<Integer> removeUids = getUserChange(currentTime, oid, deptIds, userName, addList);
            List<UserDto> result = userService.getUserDtoByOidAndDeptIdsLocking(oid, userId, deptIds, userName, removeUids);
            if (!addList.isEmpty()) {
                result.addAll(addList);
            }
            return result;
        }
    }
    private List<Integer> getUserChange(Date currentTime, Integer oid, List<String> deptIds, String userName, List<UserDto> addList) {
        List<Integer> result = new ArrayList<>();
        if (!deptIds.isEmpty()) {//只在考勤部门中处理
            List<UserDepartmentHistory> list = getChangeUserMonitor(currentTime, oid);
            if (!list.isEmpty()) {
                for (UserDepartmentHistory item : list) {
                    if (item.getOldDeptId() == null //当时还未入职的人员需要删除
                            || !deptIds.contains(item.getOldDeptId()) && item.getNewDeptId() != null && deptIds.contains(item.getNewDeptId())) {//当时在部门组外现在转入部门组的人需要删除
                        result.add(item.getUser());
                    } else if (addList != null//数组不为空
                            && (item.getNewDeptId() == null  //当时在职现在已离职的人员需要加入
                            || !deptIds.contains(item.getNewDeptId()))) {//这些人当时在部门组/部门中，需要加回来
                        fillUserDtoAndAddList(item, userName, addList);
                    }
                }
            }
        }
        return result;
    }
    private List<UserDto> getNoUser(Date currentTime, Integer oid, Integer userId, String deptId, String userName) {
        List<UserDto> result = new ArrayList<>();
        List<String> deptIds = getDeptIds(currentTime, oid);
        if(!StringUtils.isBlank(deptId)) {
            if(deptIds.contains(deptId)) {//如果deptId是需要考勤的部门
                return Collections.emptyList();
            } else {//如果deptId部门无需考勤
                deptIds = new ArrayList<>(1) {{add(deptId);}};
            }
        }
        ArrayList<UserDto> addList = new ArrayList<>();
        List<Integer> removeUids;
        removeUids = getNoUserChange(currentTime, oid, deptIds, deptId, userName, addList);
        result = userService.getUserDtoByOidAndNoDeptIdsLocking(oid, userId, deptIds, userName, removeUids);
        if (!addList.isEmpty()) {
            result.addAll(addList);
        }
        return result;
    }
    private List<Integer> getNoUserChange(Date currentTime, Integer oid, List<String> deptIds, String deptId, String userName, List<UserDto> addList) {
        List<UserDepartmentHistory> list = getChangeUserMonitor(currentTime, oid);
        List<Integer> result = new ArrayList<>();
        if (!list.isEmpty()) {
            for (UserDepartmentHistory item : list) {
                if ((item.getOldDeptId() == null //当时还未入职的人员
                        || deptIds.contains(item.getOldDeptId())) //当时在需要考勤的部门
                        && item.getNewDeptId() != null && !deptIds.contains(item.getNewDeptId())) {//现在不在需要考勤的部门且未离职的人需要删除
                    result.add(item.getUser());
                } else if (addList != null//数组不为空
                        && item.getOldDeptId() != null
                        && (deptId == null && !deptIds.contains(item.getOldDeptId()) || item.getOldDeptId().equals(deptId))
                        && (item.getNewDeptId() == null || deptIds.contains(item.getNewDeptId()))) {//旧部门无需考勤，现离职或在需要考勤的部门+当时不需要考勤，补充所有离职人员
                    fillUserDtoAndAddList(item, userName, addList);
                }
            }
        }
        return result;
    }
    private List<UserDepartmentHistory> getChangeUserMonitor(Date currentTime, Integer oid) {
        String cacheName = "miners:Attendance:changeUserMonitor:" + oid;
        Set<String> cacheStrings = (Set<String>) redisTemplate.opsForZSet().rangeByScore(cacheName, currentTime.getTime(), currentTime.getTime());
        List<UserDepartmentHistory> result;
        if (cacheStrings.isEmpty()) {
            String hql = "from UserDepartmentHistory where org=:org and effdt between :firstDate and :lastDate order by effdt";
            HashMap<String, Object> params = new HashMap<>();
            params.put("org", oid);
            params.put("firstDate", NewDateUtils.tomorrow(currentTime));
            params.put("lastDate", NewDateUtils.tomorrow());
            List<UserDepartmentHistory> list = userDepartmentHistoryDao.getListByHQLWithNamedParams(hql, params);
            HashMap<Integer, UserDepartmentHistory> map = new HashMap<>();
            UserDepartmentHistory tmp;
            for (UserDepartmentHistory rec : list) {
                userDepartmentHistoryDao.getSession().evict(rec);
                tmp = map.get(rec.getUser());
                if (tmp != null) {
                    tmp.setNewDeptId(rec.getNewDeptId());
                } else {
                    map.put(rec.getUser(), rec);
                }
            }
            result = new ArrayList<>(new HashSet<>(map.values()));
            redisTemplate.opsForZSet().add(cacheName, JSON.toJSONString(result), currentTime.getTime());
        } else {
            result = JSON.parseArray(cacheStrings.iterator().next(), UserDepartmentHistory.class);
        }
        return result;
    }
    private void fillUserDtoAndAddList(UserDepartmentHistory item, String userName, List<UserDto> addList) {
        UserDto dto = userService.getUserDtoByUserId(item.getUser());
        if (userName == null || dto.getUserName().contains(userName)) {//模糊查询
            dto.setDepartment(item.getOldDeptId());
            if (item.getOldDeptId().equalsIgnoreCase("0")) {
                dto.setDepartName("");//其他
            } else {
                dto.setDepartName(orgService.getOrgByOid(Integer.valueOf(item.getOldDeptId()), OrgService.OrgType.department).getName());
            }
            addList.add(dto);
        }
    }
    //获取机构/部门考勤设置
    private ReturnTimeDto getReturnTime(Integer oid, Integer deptId, Date day) {
        Date currentWorkday = getCurrentWorkDate(day, oid);
        Date nextWorkday = getNextDateByWork(day, oid);
        PersonnelAttendanceConfig personnelAttendanceConfigs = getOnePersonnelAttendanceConfigByOpenDate(oid, day);
        if (personnelAttendanceConfigs != null && day != null) {
            return new ReturnTimeDto(day, personnelAttendanceConfigs, currentWorkday, nextWorkday);
        } else {
            return null;
        }
    }
    private List<UserDepartmentHistory> getDailyUserMonitor(Date currentTime, Integer oid) {
        String hql = "from UserDepartmentHistory where org=:org and effdt=:effdt and isUsed=:isUsed";
        HashMap<String, Object> params = new HashMap<>();
        params.put("org", oid);
        params.put("effdt", NewDateUtils.today(currentTime));
        params.put("isUsed", false);
        return userDepartmentHistoryDao.getListByHQLWithNamedParams(hql, params);
    }

    /**
     * getPersonnelAttendanceConfigsOpenDates 获取机构所有有效的考勤设置时间
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @return List<Date>
     * @date 2025-04-22 15:08:33
     **/
    @Override
    @Cacheable(value = "getPersonnelAttendanceConfigsOpenDates")
    public List<Date> getPersonnelAttendanceConfigsOpenDates(Integer oid) {
        StringBuilder hql = new StringBuilder("select distinct(openDate) from PersonnelAttendanceConfig where org=:org order by openDate desc");
        Map<String, Object> params = new HashMap<>(1){{
            put("org", oid);
        }};
        return personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql.toString(), params);
    }
    /**
     * getMaxdateByOpenDate 找出小于openDate的机构考勤设置的最大openDate
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @param openDate
     * @return Date
     * @date 2025-04-22 15:06:34
     **/
    public Date getMaxdateByOpenDate(Integer oid, Date openDate) {
        List<Date> list = workAttendanceService.getPersonnelAttendanceConfigsOpenDates(oid);
        final Date[] result = {null};
        list.stream().filter(date -> date.getTime()<=openDate.getTime()).findFirst().ifPresent(date -> {result[0] = date;});
        return result[0];
    }

    /**
     * getPersonnelAttendanceConfigsByMaxOpenDate 获取机构指定生效时间openDate的全部考勤设置，left为考勤部门和考勤设置的Map，right为考勤设置id和考勤设置的Map
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @param openDate
     * @return Pair<Map<Integer, Integer>, Map<Integer,PersonnelAttendanceConfig>>
     * @date 2025-04-22 15:09:29
     **/
    @Override
    @Cacheable(value = "getPersonnelAttendanceConfigsByExactOpenDate", key = "#openDate.getTime()+'_'+#oid", condition = "#oid!=null&&#openDate!=null")
    public Pair<Map<Integer, Integer>, Map<Integer,PersonnelAttendanceConfig>> getPersonnelAttendanceConfigsByExactOpenDate(Integer oid, Date openDate) {
        String hql = "from PersonnelAttendanceConfig c join fetch c.personnelAttendanceDepartmentConfigHashSet d where c.org=:org and c.enabled=:enabled and c.openDate=:openDate";
        Map<String, Object> params = new HashMap<>(3){{
            put("org", oid);
            put("enabled", Boolean.TRUE);
            put("openDate", openDate);
        }};
        List<PersonnelAttendanceConfig> list = personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql, params);
        Map<Integer, Integer> department = new HashMap<>(list.size());
        Map<Integer, PersonnelAttendanceConfig>  personnelAttendanceConfigMap = new HashMap<>(list.size());
        list.stream().forEach(personnelAttendanceConfig -> {
            personnelAttendanceConfigMap.put(personnelAttendanceConfig.getId(), personnelAttendanceConfig);
            personnelAttendanceConfig.getPersonnelAttendanceDepartmentConfigHashSet().stream().forEach(departmentConfig -> {
                department.put(departmentConfig.getDept(), personnelAttendanceConfig.getId());
            });
        });
        return Pair.of(department, personnelAttendanceConfigMap);
    }
    private List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigsByOpenDate(Integer oid, Date openDate) {
        Date maxOpenDate = getMaxdateByOpenDate(oid, openDate);
        Pair<Map<Integer, Integer>, Map<Integer, PersonnelAttendanceConfig>> pair = workAttendanceService.getPersonnelAttendanceConfigsByExactOpenDate(oid, maxOpenDate);
        if (!pair.getRight().isEmpty()) {
            return pair.getRight().values().stream().toList();
        }
        return Collections.emptyList();
    }
    @Override    //根据部门查找规则
    public PersonnelAttendanceConfig getPersonnelAttendanceConfigByDept(Integer oid, Integer deptId, Date openDate) {
        Date maxOpenDate = getMaxdateByOpenDate(oid, openDate);
        Pair<Map<Integer, Integer>, Map<Integer, PersonnelAttendanceConfig>> pair = workAttendanceService.getPersonnelAttendanceConfigsByExactOpenDate(oid, maxOpenDate);
        if (!pair.getLeft().isEmpty()) {
            Integer rule = pair.getLeft().get(deptId);
            if(rule!=null) {
                return pair.getRight().get(rule);
            }
        }
        return null;
    }
    /**
     * getDeptIds 指定机构和日期获取当时需要考勤的部门
     * <AUTHOR>
     * @since 4.0
     * @param currentTime
     * @param oid
     * @return List<String> 需要考勤机构部门id.toString()，没有设部门人员的返回部门id值为"0"，如果当前没有部门需考勤，返回空List
     * @date 2025-05-29 11:31:00
     **/
    private List<String> getDeptIds(Date currentTime, Integer oid) {
        Date maxOpenDate = getMaxdateByOpenDate(oid, currentTime);
        Pair<Map<Integer, Integer>, Map<Integer, PersonnelAttendanceConfig>> pair = workAttendanceService.getPersonnelAttendanceConfigsByExactOpenDate(oid, maxOpenDate);
        if (!pair.getRight().isEmpty()) {
            return new ArrayList<>(pair.getLeft().keySet().stream().map(String::valueOf).toList());
        }
        return Collections.emptyList();
    }
    /**
     * getStartUsingSystemTime 获取机构首次启用考勤的时间
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @return Date
     * @date 2025-05-29 11:30:09
     **/
    @Override
    public Date getStartUsingSystemTime(Integer oid) {
        List<Date> list = workAttendanceService.getPersonnelAttendanceConfigsOpenDates(oid);
        if (!list.isEmpty()) {
            return list.get(list.size() - 1);//按时间倒排，首次启用时间取最后一条。
        } else {
            return null;
        }
    }
    /**
     * getPersonnelAttendanceExceptionByMonth 按时间段获取机构作息时间表
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @param beginTime
     * @param endTime
     * @return List<PersonnelAttendanceException>
     * @date 2025-04-23 09:48:38
     **/
    @Override
    public List<PersonnelAttendanceException> getPersonnelAttendanceExceptionByMonth(Integer oid, Date beginTime, Date endTime) {
        StringBuilder hql = new StringBuilder("from PersonnelAttendanceException where org=:org");
        Map<String, Object> params = new HashMap<>(){{
            put("org", oid);
        }};
        if (beginTime != null && endTime != null) {
            hql.append(" and exceptionDate between :beginTime and :endTime");
            params.put("beginTime", beginTime);
            params.put("endTime", endTime);
        }
        return personnelAttendanceExceptionDao.getListByHQLWithNamedParams(hql.append(" order by exceptionDate desc").toString(), params);
    }

    @Override
    public PersonnelAttendanceException getPersonnelAttendanceException(Integer oid, Date exceptionDate) {
        String hql = "from PersonnelAttendanceException where org=:oid and exceptionDate=:exceptionDate";
        Map<String, Object> params = new HashMap<>(2){{
            put("oid", oid);
            put("exceptionDate", exceptionDate);
        }};
        return (PersonnelAttendanceException) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, params);
    }
    /**
     * addNewDefaultPersonnelAttendanceUser 生成默认空白的考勤日表，并添加考勤明细表，如果考勤日表存在，报错。
     * <AUTHOR>
     * @since 4.0
     * @param user
     * @param operator
     * @param returnTime
     * @return PersonnelAttendanceUser
     * @date 2025-04-25 09:39:16
     **/
    private PersonnelAttendanceUser addNewDefaultPersonnelAttendanceUser(User user, User operator, ReturnTimeDto returnTime, PersonnelAttendanceException attendanceException, Date nextWorkday) {
        PersonnelAttendanceUser pau = getPersonnelAttendanceUser(user, returnTime.getDay());
        Assert.isNull(pau, "addNewDefaultPersonnelAttendanceUser throw error： PersonnelAttendanceUser不为空， " + JSON.toJSONString(pau));
        pau = new PersonnelAttendanceUser();
        if (operator == null) {
            operator = new User();
            operator.setUserName("系统");
        }
        if (nextWorkday == null) {
            nextWorkday = getNextDateByWork(returnTime.getDay(), user.getOid());
        }
        pau.setCreator(operator.getUserID());
        pau.setCreateName(operator.getUserName());
        pau.setUpdator(operator.getUserID());
        pau.setUpdateName(operator.getUserName());
        pau.setUser(user.getUserID());
        pau.setUserName(user.getUserName());
        pau.setAttendanceDate(returnTime.getDay());  //考勤日期
        pau.setOrg(user.getOid());
        pau.setEndState(AttendanceType.pending.getName());
        pau.setBeginState(AttendanceType.pending.getName());  //上班状态:0-未考勤,1-已考勤，2-请假, 7-旷工(系统默认的旷工)
        pau.setType(AttendanceType.pending.getIndex());  //0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
        pau.setDept(NumberUtils.toInt(user.getDepartment(), 0));
        if(pau.getDept()>0) {
            Organization org;
            if(StringUtils.isNotBlank(user.getDepartName())) {
                pau.setDeptName(user.getDepartName());
            } else if((org=orgService.getOrgByOid(pau.getDept(), OrgService.OrgType.department))!=null) {
                pau.setDeptName(org.getName());
            }
        }
        pau.setNoNeed(attendanceException.getWorkdayType().noNeed());//班还是假
        if (Boolean.FALSE.equals(pau.getNoNeed())) {//工作日，非工作日保留空
            pau.setBeginTime(returnTime.getBeginTime());  //上班时间
            pau.setEndTime(returnTime.getEndTime());   //下班时间
            pau.setMiddleBreak(returnTime.getMiddleBreak());
            pau.setBreakBegin(returnTime.getBreakBegin());  //午休开始时间(上午下班时间)
            pau.setBreakEnd(returnTime.getBreakEnd());   //午休结束时间(下午上班时间)
            if(Boolean.TRUE.equals(returnTime.getBreakBegin())) {
                pau.setBreakBeginState(AttendanceType.pending.getIndex());
                pau.setBreakEndState(AttendanceType.pending.getIndex());
            }
            pau.setEarlyLimit(returnTime.getEarlyLimit());
            pau.setLateLimit(returnTime.getLateLimit());
        }
        pau.setOmitBefore(returnTime.getOmitBefore());
        pau.setOmitAfter(returnTime.getOmitAfter());
        pau.setSource(returnTime.getSource());  //来源:1-手工录入(默认),2-打卡
        pau.setFinalModified(Boolean.FALSE);
        if (ConfigAttendancePattern.manualEntry.equals(returnTime.getCap())) {//手工录入模式
            pau.setScanStartTime(returnTime.getDay());
            pau.setScanEndTime(getInputTime(user.getOid(), nextWorkday));//下一个工作日的录入时间
        } else {//ConfigAttendancePattern.attendanceTreasure 考勤宝模式
            if (returnTime.getP1Before() != null && Boolean.FALSE.equals(pau.getNoNeed())) {//设置P1Before且是工作日
                pau.setScanStartTime(new Date(returnTime.getBeginTime().getTime() + returnTime.getP1Before()));
            } else {
                pau.setScanStartTime(returnTime.getDay());
            }
            if (returnTime.getP1After() != null && Boolean.FALSE.equals(pau.getNoNeed())) {//设置P1After且是工作日
                pau.setScanEndTime(new Date(returnTime.getEndTime().getTime() - returnTime.getP1After()));
            } else {
                pau.setScanEndTime(NewDateUtils.getLastTimeOfDay(returnTime.getDay()));
            }
        }
        pau.setLastScanPunchId(0);
        List<WorkOffBlockDto> offBlocks = new ArrayList<>();//离岗申请块
        List<WorkOffBlockDto> workBlocks = new ArrayList<>();//到岗块
        boolean isWorkday = Boolean.FALSE.equals(pau.getNoNeed());
        AttendanceType.getApprovalAttendanceEventTypes().forEach(type -> {
            switch (type) {
                case leave:
                    if(isWorkday) {//工作日需要检查请假
                        offBlocks.addAll(getAttendanceEventService("leaveService").getBlocks(user, returnTime.getDay(), NewDateUtils.getLastTimeOfDay(returnTime.getDay())));
                    }
                    break;
                case overtime:
                    workBlocks.addAll(getAttendanceEventService("overtimeService").getBlocks(user, returnTime.getDay(), NewDateUtils.getLastTimeOfDay(returnTime.getDay())));
                    break;
                default://case goOut: case travel:
                    //外出和出差还未实现！
                    break;
            }
        });
        updateAttendanceUserScanStartEndTimeByWorkBlocks(pau, workBlocks);
        personnelAttendanceUserDao.save(pau);
        //生成考勤明细表
        return createOrUpdateAttendanceDetail(pau, offBlocks, workBlocks);
    }
    /**
     * createOrUpdateAttendanceDetail 创建考勤明细表数据
     * @apiNote 新建考勤日表后立即执行
     * <AUTHOR>
     * @since 4.0
     * @param pau
     * @param offBlocks
     * @param workBlocks
      * @return PersonnelAttendanceUser
      * @date 2025-06-19 11:59:42
      **/
    private PersonnelAttendanceUser createOrUpdateAttendanceDetail(PersonnelAttendanceUser pau, List<WorkOffBlockDto> offBlocks, List<WorkOffBlockDto> workBlocks) {
        //获取现有detail列表并按businessClass和business为key转为Map<String businessService, Map<Integer business, PersonnelAttendanceUserDetail>>
        Set<PersonnelAttendanceUserDetail> detailSet;
        Map<Integer, PersonnelAttendanceUserDetail> detailMap;
        List<String> businessServices = new ArrayList<>();
        if((detailSet = pau.getPersonnelAttendanceUserDetailHashSet())==null) {
            detailSet = new HashSet<>(0);
            detailMap = new HashMap<>();
        } else {
            detailMap = new HashMap<>(detailSet.stream().collect(Collectors.toMap(PersonnelAttendanceUserDetail::getId, Function.identity())));
        }
        detailSet.stream().forEach(detail -> {if(StringUtils.isNotBlank(detail.getBusinessService())) {detail.setDiscard(Boolean.TRUE);}});
        Map<String, Map<Integer, PersonnelAttendanceUserDetail>> businessDetails = detailSet.stream().filter(d->d.getBusinessService()!=null).collect(Collectors.groupingBy(PersonnelAttendanceUserDetail::getBusinessService,Collectors.toMap(PersonnelAttendanceUserDetail::getBusiness, Function.identity(), (existing, replacement) -> replacement)));
        if(!offBlocks.isEmpty()) {//请假等离岗行为
            offBlocks.forEach(block -> {
                PersonnelAttendanceUserDetail detail = businessDetails.getOrDefault(block.getBusinessService(), new HashMap<>(0)).getOrDefault(block.getBusiness(), new PersonnelAttendanceUserDetail(pau, block));
                if(detail.getId()==null || block.getUpdateDate().after(detail.getUpdateDate())) {//detail是新建的或者业务数据已变更，需要使用业务数据更新detail
                    getAttendanceEventService(detail.getBusinessService()).fillNewDetail(detail);
                    personnelAttendanceUserDetailDao.saveOrUpdate(detail);
                    detailMap.put(detail.getId(),detail);
                    businessServices.add(block.getBusinessService());
                }
                detail.setDiscard(Boolean.FALSE);
            });
        }
        if(!workBlocks.isEmpty()) {//加班等到岗行为
            workBlocks.forEach(block -> {
                PersonnelAttendanceUserDetail detail = businessDetails.getOrDefault(block.getBusinessService(), new HashMap<>(0)).getOrDefault(block.getBusiness(), new PersonnelAttendanceUserDetail(pau, block));
                if(detail.getId()==null || block.getUpdateDate().after(detail.getUpdateDate())) {//detail是新建的或者业务数据已变更，需要使用业务数据更新detail
                    getAttendanceEventService(detail.getBusinessService()).fillNewDetail(detail);
                    personnelAttendanceUserDetailDao.saveOrUpdate(detail);
                    detailMap.put(detail.getId(),detail);
                    businessServices.add(block.getBusinessService());
                }
                detail.setDiscard(Boolean.FALSE);
            });
        }
        //获取在detailHashSet但不在blocks中的相同（businessService）的detail，这些detail需要删除（实际未请假之类）
        Set<Integer> removeIds = businessServices.stream().filter(businessDetails::containsKey).flatMap(key -> businessDetails.get(key).entrySet().stream()).filter(entry -> entry.getValue()!=null && Boolean.TRUE.equals(entry.getValue().getDiscard())).map(Map.Entry::getKey).collect(Collectors.toSet());
        //合并重复的details
        removeIds.addAll(sliceDetails(detailMap));
        if (!removeIds.isEmpty()) {
            personnelAttendanceUserDetailDao.queryHQLWithNamedParams("delete from PersonnelAttendanceUserDetail where id in (:removeIds)", new HashMap<>(1) {{put("removeIds", removeIds);}});
            detailMap.keySet().removeAll(removeIds);
        }
        if(!pau.getNoNeed()) {//班
            PersonnelAttendanceUserDetail detail = null;
            if(detailMap.values().stream().filter(d -> WorkdayType.getByIndex(d.getWorkdayType()).noNeed()).noneMatch(d-> pau.getEndTime().after(d.getBeginTime()) && pau.getBeginTime().before(d.getEndTime()))) {
                //班，没有请假
                detail = new PersonnelAttendanceUserDetail(pau, WorkdayType.Workday);
                detail.setState(WorkdayType.Multiple.getState());
                detail.setBeginTime(pau.getBeginTime());
                detail.setEndTime(pau.getEndTime());
                detail.autoSetDuration();
                personnelAttendanceUserDetailDao.save(detail);
            } else if (detailMap.values().stream().noneMatch(d->WorkdayType.checkMultipleState(WorkdayType.getByState(d.getState()),WorkdayType.Workday))) {
                //班，没有请假覆盖上班，添加正常上班空白明细
                Date endTime=detailMap.values().stream().filter(d -> d.getBeginTime() != null && WorkdayType.DayOff.getIndex().equals(d.getWorkdayType())).map(PersonnelAttendanceUserDetail::getBeginTime).min(Date::compareTo).orElse(pau.getEndTime());
                detail = new PersonnelAttendanceUserDetail(pau, WorkdayType.Workday);
                detail.setState(WorkdayType.Workday.getState());
                detail.setBeginTime(pau.getBeginTime());
                detail.setEndTime(endTime);
                detail.autoSetDuration();
                personnelAttendanceUserDetailDao.save(detail);
            } else if (detailMap.values().stream().noneMatch(d->WorkdayType.checkMultipleState(WorkdayType.getByState(d.getState()),WorkdayType.DayOff))) {
                //班，没有请假覆盖下班，添加正常下班空白明细
                Date beginTime=detailMap.values().stream().filter(d -> d.getEndTime() != null && WorkdayType.DayOff.getIndex().equals(d.getWorkdayType())).map(PersonnelAttendanceUserDetail::getEndTime).max(Date::compareTo).orElse(pau.getBeginTime());
                detail = new PersonnelAttendanceUserDetail(pau, WorkdayType.Workday);
                detail.setState(WorkdayType.DayOff.getState());
                detail.setBeginTime(beginTime);
                detail.setEndTime(pau.getEndTime());
                detail.autoSetDuration();
                personnelAttendanceUserDetailDao.save(detail);
            }
            if(detail!=null) {
                detailMap.put(detail.getId(),detail);
            }
            if(ConfigAttendancePattern.attendanceTreasure.equals(ConfigAttendancePattern.getBySource(pau.getSource()))) {//考勤宝模式
                fillDetailsHole(pau, detailMap.values());
            }
        }
        Session session = personnelAttendanceUserDao.getSession();
        Transaction transaction = session.getTransaction();
        boolean commitBegin = true;
        if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
            commitBegin = false;
        }
        transaction.commit();
        session.evict(pau);
        if (commitBegin) {
            transaction.begin();
        }
        return personnelAttendanceUserDao.get(pau.getId());
    }
    /**
     * sliceDetails 修改考勤明细被考勤申请变动覆盖的部分
     * @apiNote 用于考勤申请修改请假加班时间之后，修改直接作用与参数detailMap，并返回需要从数据库删除的id集合
     * <AUTHOR>
     * @since 4.0
     * @param detailMap 需要调整的detailMap
     * @return Set<Integer> 待删除的detail.id集合
     * @date 2025-06-19 08:14:06
     **/
    private Set<Integer> sliceDetails(Map<Integer, PersonnelAttendanceUserDetail> detailMap) {
        Set<Integer> removeIds = new HashSet<>();
        Set<PersonnelAttendanceUserDetail> businessDetails = detailMap.values().stream().filter(d->d.getBusinessService()!=null).collect(Collectors.toSet());//请假加班等申请details
        Set<PersonnelAttendanceUserDetail> otherDetails = detailMap.values().stream().filter(d->d.getBusinessService()==null).collect(Collectors.toSet());//其他自动生成的details
        businessDetails.forEach(busD-> otherDetails.stream().filter(d ->
                busD.getBeginTime().getTime()<d.getEndTime().getTime() && busD.getEndTime().getTime()>d.getBeginTime().getTime()//申请detail(busD)和其他detail(d)在开始结束时间上有交集
        ).forEach(d -> {
            if(busD.getBeginTime().getTime()<=d.getBeginTime().getTime() && busD.getEndTime().getTime()>=d.getEndTime().getTime()) {//busD包含d，或busD与d相同
                busD.setPrevId(d.getPrevId());
                busD.setNextId(d.getNextId());
                removeIds.add(d.getId());
            } else if(busD.getBeginTime().getTime()>d.getBeginTime().getTime() && busD.getEndTime().getTime()<d.getEndTime().getTime()) {//busD被d包含
                d.setStartPunchTime(null);
                d.setStartPunchId(null);
                d.setEndPunchTime(null);
                d.setEndPunchId(null);
                PersonnelAttendanceUserDetail newD = SerializationUtils.clone(d);//新newD分割后的后段，原始d改为前段
                newD.setId(null);
                newD.setDefaultBeginTime(null);
                newD.setBeginTime(busD.getEndTime());
                newD.setScanStartTime(busD.getScanEndTime());
                newD.setPrevId(busD.getId());
                newD.setType(AttendanceType.pending.getName());
                newD.setCreateDate(null);
                newD.setUpdateDate(null);
                if(WorkdayType.Multiple.equals(WorkdayType.getByState(d.getState()))) {//原details包含上下班，需要分别设置上下班
                    newD.setState(WorkdayType.DayOff.getState());
                    d.setState(WorkdayType.Workday.getState());
                } else if(WorkdayType.Workday.equals(WorkdayType.getByState(d.getState()))) {
                    newD.setState(null);
                } else if(WorkdayType.DayOff.equals(WorkdayType.getByState(d.getState()))) {
                    d.setState(null);
                }
                personnelAttendanceUserDetailDao.saveOrUpdate(newD);
                d.setDefaultEndTime(null);
                d.setEndTime(busD.getBeginTime());
                d.setScanEndTime(busD.getScanStartTime());
                d.setNextId(busD.getId());
                d.setType(AttendanceType.pending.getName());
                busD.setPrevId(d.getId());
                busD.setNextId(newD.getId());
            } else if(busD.getBeginTime().getTime()>d.getBeginTime().getTime() && busD.getBeginTime().getTime()<d.getEndTime().getTime()) {//bus开始与d后段相交
                d.setDefaultEndTime(null);
                d.setEndTime(busD.getBeginTime());
                d.setScanEndTime(busD.getScanStartTime());
                d.setStartPunchTime(null);
                d.setStartPunchId(null);
                d.setEndPunchTime(null);
                d.setEndPunchId(null);
                d.setNextId(busD.getId());
                d.setType(AttendanceType.pending.getName());
                busD.setPrevId(d.getId());
            } else if(busD.getEndTime().getTime()>d.getBeginTime().getTime() && busD.getEndTime().getTime()<d.getEndTime().getTime()) {//bus结束与d前段相交
                d.setDefaultBeginTime(null);
                d.setBeginTime(busD.getEndTime());
                d.setScanStartTime(busD.getScanEndTime());
                d.setStartPunchTime(null);
                d.setStartPunchId(null);
                d.setEndPunchTime(null);
                d.setEndPunchId(null);
                d.setPrevId(busD.getId());
                d.setType(AttendanceType.pending.getName());
                busD.setNextId(d.getId());
            }
        }));
        detailMap.keySet().removeAll(removeIds);
        return removeIds;
    }
    /**
     * fillDetailsHole 考勤宝模式正常班中补中间空洞，并加上前后链表节点。
     * <AUTHOR>
     * @since 4.0
     * @param pau
     * @param details
     * @return void
     * @date 2025-06-13 09:14:38
     **/
    private void fillDetailsHole(PersonnelAttendanceUser pau, Collection<PersonnelAttendanceUserDetail> details) {
        Iterator<PersonnelAttendanceUserDetail> iterator = details.stream().sorted(Comparator.comparing(detail -> detail.getBeginTime().getTime())).iterator();
        Integer prevId=null;
        long preTime=pau.getBeginTime().getTime();
        Date scanStart=pau.getScanStartTime();
        PersonnelAttendanceUserDetail prev=null, workDayPre=null;
        while(iterator.hasNext()) {
            PersonnelAttendanceUserDetail current = iterator.next();
            if(preTime < current.getBeginTime().getTime() && pau.getBeginTime().getTime() < current.getBeginTime().getTime() && pau.getEndTime().getTime() > preTime) {//有空洞位于上班时间段内，加一个待定的普通班在前一节点和当前节点之间
                PersonnelAttendanceUserDetail detail = new PersonnelAttendanceUserDetail(pau, WorkdayType.Workday);
                detail.setPrevId(prevId);
                detail.setBeginTime(new Date(preTime));
                detail.setEndTime(current.getBeginTime());
                detail.autoSetDuration();
                personnelAttendanceUserDetailDao.save(detail);
                if(prev!=null) {
                    prev.setNextId(detail.getId());
                }
                prevId=detail.getId();
                prev=detail;
                preTime=current.getBeginTime().getTime();
            }
            if(preTime >= current.getBeginTime().getTime()) {//就是等于，时间段连上了
                //组建双向链表节点
                if (prev != null) {
                    prev.setNextId(current.getId());
                    current.setPrevId(prevId);
                    if (WorkdayType.Workday.getIndex().equals(prev.getWorkdayType())) {//前项是班
                        if (WorkdayType.Workday.getIndex().equals(current.getWorkdayType())) {//当前班
                            current.setScanStartTime(scanStart);//连续班，开始时间相同。
                        } else {//当前假
                            scanStart = current.getBeginTime();
                        }
                    }
                }
            } else if(workDayPre!=null) {//时间段断开，但前面有班，扫描起止不越界
                scanStart=new Date(Math.max(current.getScanStartTime().getTime(),workDayPre.getEndTime().getTime()));
                current.setScanStartTime(scanStart);
                Date scanEnd=new Date(Math.min(workDayPre.getScanEndTime().getTime(), current.getBeginTime().getTime()));
                workDayPre.setScanEndTime(scanEnd);
                while((prev=workDayPre.getPrev())!=null) {
                    prev.setScanEndTime(scanEnd);
                }
            }
            prevId = current.getId();
            prev = current;
            preTime = current.getEndTime().getTime();
            if(WorkdayType.Workday.getIndex().equals(current.getWorkdayType())) {//班
                workDayPre = current;
            }
        }
    }
    /**
     * getPersonnelAttendanceUser 从数据库获取考勤日数据，内部使用，一般使用请用下两个函数
     * <AUTHOR>
     * @since 4.0
     * @param user
     * @param date
     * @return PersonnelAttendanceUser
     * @date 2025-05-14 09:28:42
     **/
    private PersonnelAttendanceUser getPersonnelAttendanceUser(User user, Date date) {
        Assert.isTrue(user!=null && user.getUserID()!=null, "用户不能为空！");
        Assert.notNull(date, "日期不能为空");
        String hql = "from PersonnelAttendanceUser where user=:user and attendanceDate=:attendanceDate";
        Map<String, Object> params = new HashMap<>(2){{
            put("user", user.getUserID());
            put("attendanceDate", date);
        }};
        return (PersonnelAttendanceUser) personnelAttendanceUserDao.getByHQLWithNamedParams(hql, params);
    }
    /**
     * getOrAddNewPersonnelAttendanceUser 获取或者初始化考勤表/考勤明细表，当单人运行且不清楚是否存在记录时使用
     * <AUTHOR>
     * @since 4.0
     * @param user
     * @param day
     * @return PersonnelAttendanceUser
     * @date 2025-05-14 09:26:06
     **/
    private PersonnelAttendanceUser getOrAddNewPersonnelAttendanceUser(User user, Date day) {
        PersonnelAttendanceUser result = getPersonnelAttendanceUser(user, day);
        if (result == null) {
            Date nextWorkday = getNextDateByWork(day, user.getOid());
            PersonnelAttendanceException attendanceException = getPersonnelAttendanceException(user.getOid(), day);
            ReturnTimeDto returnTime = getReturnTime(user.getOid(), NumberUtils.toInt(user.getDepartment(), 0), day);
            return addNewDefaultPersonnelAttendanceUser(user, null, returnTime, attendanceException, nextWorkday);
        } else {
            return result;
        }
    }
    /**
     * addNewDefaultPersonnelAttendanceUser 获取或者新增考勤日表，并记录当前操作人员，用于考勤修改或者考勤录入
     * <AUTHOR>
     * @since 4.0
     * @param user
     * @param operator
     * @param day
     * @return PersonnelAttendanceUser
     * @date 2025-05-14 09:26:28
     **/
    private PersonnelAttendanceUser addNewDefaultPersonnelAttendanceUser(User user, User operator, Date day) {
        PersonnelAttendanceUser pau = getPersonnelAttendanceUser(user, day);
        if (pau == null) {
            Date nextWorkday = getNextDateByWork(day, user.getOid());
            PersonnelAttendanceException attendanceException = getPersonnelAttendanceException(user.getOid(), day);
            ReturnTimeDto returnTime = getReturnTime(user.getOid(), NumberUtils.toInt(user.getDepartment(), 0), day);
            pau = addNewDefaultPersonnelAttendanceUser(user, operator, returnTime, attendanceException, nextWorkday);
        } else {//记录当前操作人员
            pau.setUpdator(user.getUserID());
            pau.setUpdateName(user.getUserName());
        }
        return pau;
    }
    /**
     * updateAttendanceUserScanStartEndTimeByWorkBlocks 用加班更新扫描开始时间和结束时间
     * <AUTHOR>
     * @since 4.0
     * @param user
     * @param day
     * @return boolean pau的scanTime非空且晚于加班开始时间，被修改为加班开始时间并返回true，否则false
     * @date 2025-05-14 14:19:17
     **/
    private boolean updateAttendanceUserScanStartEndTimeByWorkBlocks(User user, Date day){
        PersonnelAttendanceUser pau = getOrAddNewPersonnelAttendanceUser(user, day);
        List<WorkOffBlockDto> workBlocks = getAttendanceEventService("overtimeService").getBlocks(user, NewDateUtils.today(pau.getAttendanceDate()), NewDateUtils.getLastTimeOfDay(pau.getAttendanceDate()));
        boolean result = updateAttendanceUserScanStartEndTimeByWorkBlocks(pau,workBlocks);
        personnelAttendanceUserDao.saveOrUpdate(pau);
        return result;
    }
    /**
     * updateAttendanceUserScanStartEndTimeByWorkBlocks 用加班更新扫描开始时间和结束时间，用于定时任务
     * <AUTHOR>
     * @since 4.0
     * @param pau
     * @param workBlocks
     * @return boolean pau的scanTime非空且晚于加班开始时间，被修改为加班开始时间并返回true，否则false
     * @date 2025-05-14 13:52:40
     **/
    private boolean updateAttendanceUserScanStartEndTimeByWorkBlocks(PersonnelAttendanceUser pau,List<WorkOffBlockDto> workBlocks){
        Stream<WorkOffBlockDto> streamMin,streamMax;
        if (pau.getScanTime() == null) {//首次执行，获取全部blocks的最早开始最晚结束时间
            streamMin = workBlocks.stream();
            streamMax = workBlocks.stream();
        } else {//再次执行，只获取上次扫描至今有更新的blocks的最早开始最晚结束时间
            streamMin = workBlocks.stream().filter(workBlock -> pau.getScanTime().before(workBlock.getUpdateDate()));
            streamMax = workBlocks.stream().filter(workBlock -> pau.getScanTime().before(workBlock.getUpdateDate()));
        }
        //更新pau的扫描开始结束时间
        Date scanStart = streamMin.min(Comparator.comparing(WorkOffBlockDto::getBegin)).orElseGet(()->new WorkOffBlockDto()).GetBegin();
        Date scanEnd = streamMax.max(Comparator.comparing(WorkOffBlockDto::getEnd)).orElseGet(()->new WorkOffBlockDto()).GetEnd();
        boolean startChanged=false;
        if(scanStart.before(pau.getAttendanceDate())) {
            pau.setScanStartTime(pau.getAttendanceDate());
            startChanged=true;
        } else if(pau.getP1Before()!=null) {
            if(scanStart.getTime()-pau.getP1Before()<pau.getScanStartTime().getTime()) {
                pau.setScanStartTime(new Date(scanStart.getTime()-pau.getP1Before()));
                startChanged=true;
            }
        } else if(scanStart.before(pau.getScanStartTime())) {
            pau.setScanStartTime(scanStart);
            startChanged=true;
        }
        //工作日和非工作日scanStartTime和scanEndTime定义不同，手工录入的工作日scanEndTime晚于当天，不会被更新。
        if(scanEnd.after(NewDateUtils.getLastTimeOfDay(pau.getAttendanceDate()))) {
            pau.setScanEndTime(NewDateUtils.getLastTimeOfDay(pau.getAttendanceDate()));
        } else if(pau.getP1After()!=null) {
            if(scanEnd.getTime()+pau.getP1After()>pau.getScanEndTime().getTime()) {
                pau.setScanEndTime(new Date(scanEnd.getTime()+pau.getP1After()));
            }
        } else if(scanEnd.after(pau.getScanEndTime())) {
            pau.setScanEndTime(scanEnd);
        }
        if(pau.getScanTime() != null || startChanged) {
            pau.setScanTime(pau.getScanStartTime());
            return true;
        } else {
            return false;
        }
    }
    private void setSysAbsenteeism(Organization organization, Date now) {
        Map<String, Object> params = new HashMap<>(5) {{
            put("org", organization.getId());
            put("noNeed", Boolean.FALSE);
            put("finalModified", Boolean.FALSE);
            put("beginState", AttendanceType.pending.getName());
            put("endState", AttendanceType.pending.getName());
        }};
        List<PersonnelAttendanceUser> paus = personnelAttendanceUserDao.getListByHQLWithNamedParams(
                "from PersonnelAttendanceUser where org=:org and noNeed=:noNeed and finalModified=:finalModified and (beginState=:beginState or endState=:endState)",
                params);
        paus.forEach(pau -> {
            boolean setPau = false;
            if(AttendanceType.pending.getName().equals(pau.getBeginState())
                    && (pau.getLateLimit()!=null && now.after(new Date(pau.getBeginTime().getTime() + pau.getLateLimit()))//考勤宝
                    || pau.getScanStartTime().after(pau.getBeginTime()) && now.after(pau.getScanStartTime()))) {//人工录入
                pau.setBeginState(AttendanceType.absenteeism.getName());
                pau.setAmAttendance(now);
                pau.setCreator(null);
                pau.setCreateName("系统");
                //设置详情表明细；
                pau.getPersonnelAttendanceUserDetailHashSet().stream().filter(detail ->
                        WorkdayType.getByState(detail.getState())!=null && WorkdayType.getByState(detail.getState()).onWork() && AttendanceType.pending.getName().equals(detail.getType())
                ).findAny().ifPresent(detail -> {detail.setType(AttendanceType.absenteeism.getName());detail.setReason("系统默认旷工");detail.setSource("3");});
                setPau = true;
            }
            if(AttendanceType.pending.getName().equals(pau.getEndState()) && now.after(pau.getScanEndTime())) {
                pau.setEndState(AttendanceType.absenteeism.getName());
                pau.setPmAttendance(now);
                pau.setUpdator(null);
                pau.setUpdateName("系统");
                //设置详情表明细；
                pau.getPersonnelAttendanceUserDetailHashSet().stream().filter(detail ->
                        WorkdayType.getByState(detail.getState())!=null && WorkdayType.getByState(detail.getState()).noNeed() && AttendanceType.pending.getName().equals(detail.getType())
                ).findAny().ifPresent(detail -> {detail.setType(AttendanceType.absenteeism.getName());detail.setReason("系统默认旷工");detail.setSource("3");});
                setPau = true;
            }
            //此处设置过默认过旷工的话，如果考勤日表开始结束状态不一致，且都是非正常上下班状态，设置为其他，否则设置为旷工。
            if(setPau) {
                if (!pau.getBeginState().equals(pau.getEndState())
                        && AttendanceType.getMultiple().contains(AttendanceType.getByName(pau.getBeginState()))
                        && AttendanceType.getMultiple().contains(AttendanceType.getByName(pau.getEndState()))) {
                    pau.setType(AttendanceType.other.getIndex());
                } else {
                    pau.setType(AttendanceType.absenteeism.getIndex());
                }
            }
        });
    }

    @Override
    public void attendanceTimeSetting(String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,String personnelAttendanceConfigs,User user) {
        JSONArray jsonArray = JSONArray.fromObject(personnelAttendanceConfigs);
        List personnelAttendanceConfigList = JSONArray.toList(jsonArray);
        if(!personnelAttendanceConfigList.isEmpty()) {
            PersonnelAttendanceConfig config = null;
            for (int i = 0; i < personnelAttendanceConfigList.size(); i++) {
                JSONObject jo = JSONObject.fromObject(personnelAttendanceConfigList.get(i));
                String inputTimeString = (String) jo.get("inputTime");
                Date inputTime = null; //每日考勤录入时间
                if (StringUtils.isNotEmpty(inputTimeString)) {
                    inputTime = NewDateUtils.dateFromString(jo.getString("inputTime"), "HH:mm");
                }
                Date beginTime = NewDateUtils.dateFromString(jo.getString("beginTime"), "HH:mm"); //上班时间
                Date endTime = NewDateUtils.dateFromString(jo.getString("endTime"), "HH:mm"); //下班时间
                Date openDate = NewDateUtils.dateFromString(effectDate, "yyyy-MM-dd");
                String type = jo.getString("type"); //类型:1-正常班,2-倒班
                Boolean isBreak = jo.getBoolean("isBreak");//是否中间休息(是否中午考勤),true-是
                Date breakBegin = NewDateUtils.dateFromString(jo.getString("breakBegin"), "HH:mm");//午休开始时间
                Date breakEnd = NewDateUtils.dateFromString(jo.getString("breakEnd"), "HH:mm");//午休结束时间
                String departmentIds = jo.getString("departmentIds"); //部门id，以”，“分割
                Byte attendancePatternByte;
                if (attendancePattern != null && attendancePattern == 1) {
                    attendancePatternByte = WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex();
                } else {
                    attendancePatternByte = WorkAttendanceOldService.ConfigAttendancePattern.manualEntry.getIndex();
                }
                //使用service调用保存考勤设置，清理旧缓存
                config = workAttendanceService.addPersonnelAttendanceConfig(user, inputTime, beginTime, endTime, openDate, type, isBreak, breakBegin, breakEnd, WorkAttendanceOldService.AttendanceConfigOperation.create.getIndex(), attendancePatternByte, lateLimit, earlyLimit, leaveWork, null, departmentIds);
            }
            if(config!=null) {//健壮性
                //如果首次设置考勤，新增考勤监控表数据，没有考勤监控表数据的话，该机构的考勤任务不会执行
                PersonnelAttendanceOrgMonitor attendanceOrgMonitor = getOrgMonitor(config.getOrg());//获取或者新建考勤扫描表记录，
                if (attendanceOrgMonitor == null || attendanceOrgMonitor.getScanTimes().equals((byte) 3)) {//考勤还未启用；启用后就无需处理考勤监控表；
                    Date systemTime = getStartUsingSystemTime(config.getOrg());//获取机构首次考勤生效时间
                    if (systemTime != null) {//健壮性
                        Date yesterday = NewDateUtils.yesterday(systemTime);
                        Date lastTimeOfYesterday = NewDateUtils.getLastTimeOfDay(yesterday);
                        if(attendanceOrgMonitor==null) {
                            attendanceOrgMonitor = new PersonnelAttendanceOrgMonitor(config.getOrg(), null, (byte)3, null, NewDateUtils.getMinDate());
                        }
                        if (attendanceOrgMonitor.getScanDate() == null//新建记录
                                || attendanceOrgMonitor.getScanTimes().equals((byte) 3)
                                && (!lastTimeOfYesterday.equals(attendanceOrgMonitor.getScanDate())
                                || !yesterday.equals(attendanceOrgMonitor.getStartDate()))//考勤首次生效时间被修改
                        ) {
                            attendanceOrgMonitor.setStartDate(yesterday);
                            attendanceOrgMonitor.setScanDate(NewDateUtils.getLastTimeOfDay(yesterday));
                            attendanceOrgMonitor.setScanTimes((byte) 3);
                            //首次插入作息时间
                            fillDefalutPersonnelAttendanceException(config.getOrg(), systemTime);
                            personnelAttendanceOrgMonitorDao.saveOrUpdate(attendanceOrgMonitor);
                        }
                    }
                }
            }
        }
    }

    @Override
    @CacheEvict(value = "getPersonnelAttendanceConfigsByExactOpenDate")
    public PersonnelAttendanceConfig addPersonnelAttendanceConfig(User user,Date inputTime,Date beginTime,Date endTime,Date openDate,String type,Boolean isBreak, Date breakBegin,Date breakEnd,Byte operation,Byte attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,Integer recordId,String departmentIds) {
        PersonnelAttendanceConfig personnelAttendanceConfig = new PersonnelAttendanceConfig();
        personnelAttendanceConfig.setCreator(user.getUserID());
        personnelAttendanceConfig.setCreateDate(new Date());
        personnelAttendanceConfig.setCreateName(user.getUserName());
        personnelAttendanceConfig.setUpdator(user.getUserID());
        personnelAttendanceConfig.setUpdateDate(new Date());
        personnelAttendanceConfig.setUpdateName(user.getUserName());
        personnelAttendanceConfig.setOrg(user.getOid());
        personnelAttendanceConfig.setEnabled(true);//是否有效,true-有效,false-无效

        personnelAttendanceConfig.setInputTime(inputTime);//每日考勤录入时间(如未按时录入,则为旷工)
        personnelAttendanceConfig.setBeginTime(beginTime); //上班时间
        personnelAttendanceConfig.setEndTime(endTime);  //下班时间
        personnelAttendanceConfig.setOpenDate(openDate); //规则启用日期
        personnelAttendanceConfig.setType(type);//类型:1-正常班,2-倒班
        personnelAttendanceConfig.setMiddleBreak(isBreak);//是否中间休息(是否中午考勤),true-是
        personnelAttendanceConfig.setBreakBegin(breakBegin);//午休开始时间
        personnelAttendanceConfig.setBreakEnd(breakEnd);//午休结束时间
        //修改或新增的
        personnelAttendanceConfig.setOperation(operation);//操作：0-正常,1-增,2-删,3-改,4-修改录入日期,5-修改迟到早退和请假期设置
        if (attendancePattern!=null&&1==attendancePattern){
            personnelAttendanceConfig.setAttendancePattern(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex()); //考勤模式:1-考勤宝,2-手工录入(默认)
            personnelAttendanceConfig.setLateLimit(lateLimit);//迟到时限(分钟)
            personnelAttendanceConfig.setEarlyLimit(earlyLimit); //早退时限(分钟)
            personnelAttendanceConfig.setLeaveWork(leaveWork); //请假到岗是否使用考勤宝:0-不使用(默认,false),1-使用(true)
        }else {
            personnelAttendanceConfig.setAttendancePattern(WorkAttendanceOldService.ConfigAttendancePattern.manualEntry.getIndex()); //2-手工录入(默认)
        }
        personnelAttendanceConfig.setRecord(recordId);
        personnelAttendanceConfigDao.save(personnelAttendanceConfig);

        if (StringUtils.isNotEmpty(departmentIds)) {
            String[] deptIds = departmentIds.split(",");
            System.out.println("/addPersonnelAttendanceConfig deptIds size="+deptIds.length);
            for (int j = 0; j < deptIds.length; j++) {
                if (StringUtils.isNotEmpty(deptIds[j])){
//                    workAttendanceOldService.addPersonnelAttendanceDepartmentConfig(deptIds[j],user,personnelAttendanceConfig,null,1);
                    PersonnelAttendanceDepartmentConfig personnelAttendanceDepartmentConfig = new PersonnelAttendanceDepartmentConfig();
                    personnelAttendanceDepartmentConfig.setCreator(user.getUserID());
                    personnelAttendanceDepartmentConfig.setCreateDate(new Date());
                    personnelAttendanceDepartmentConfig.setCreateName(user.getUserName());
                    personnelAttendanceDepartmentConfig.setDept(Integer.parseInt(deptIds[j]));
                    personnelAttendanceDepartmentConfig.setRule(personnelAttendanceConfig);
                    personnelAttendanceDepartmentConfig.setOrg(user.getOid());
                    personnelAttendanceDepartmentConfigDao.save(personnelAttendanceDepartmentConfig);
                    System.out.println("/addPersonnelAttendanceConfig add deptId:"+deptIds[j]);
                }
            }
        }
        return personnelAttendanceConfig;
    }

    @Override
    public void addOrUpdatePersonnelAttendanceManual(PersonnelAttendanceUser personnelAttendanceUser, Date date, Integer dtype, String kuangGong, User operator) {
        Date now = new Date(System.currentTimeMillis());
        PersonnelAttendanceUser pau = personnelAttendanceUserDao.get(personnelAttendanceUser.getId());
        AttendanceType type = AttendanceType.getByIndex(pau.getType());
        User user = userService.getUserByID(pau.getUser());   //要考勤的职工
        PersonnelAttendanceManual pam = personnelAttendanceManualService.getOrNewByPau(pau);
        AttendanceType state, oldState;
        if (dtype == 1) {  //1-今天上班考勤
            state = AttendanceType.getByName(personnelAttendanceUser.getBeginState());
            Assert.notNull(state, "提交的上班状态值非法！");
            oldState = AttendanceType.getByName(pau.getBeginState());
            if(AttendanceType.getEventTypes().contains(oldState)){//如果是申请的考勤状态，已申请为准
                state = oldState;
            }
            pau.setCreateName(operator.getUserName());
            pau.setCreator(operator.getUserID());
            pau.setUpdator(operator.getUserID());
            pau.setUpdateName(operator.getUserName());
            pau.setAmAttendance(now);//上午考勤时间
            pau.setBeginState(state.getName());//上班状态:0-未考勤,1-已考勤
            pam.setCreator(operator.getUserID());
            pam.setCreateName(operator.getUserName());
            pam.setDate(pau.getAttendanceDate());
            pam.setBeginState(state.getIndex());
            pam.setBeginCommited(Boolean.FALSE);
        } else {//2-昨天下班考勤2
            state = AttendanceType.getByName(personnelAttendanceUser.getEndState());
            Assert.notNull(state, "提交的下班状态值非法！");
            oldState = AttendanceType.getByName(pau.getEndState());
            if(AttendanceType.getEventTypes().contains(oldState)){//如果是申请的考勤状态，已申请为准
                state = oldState;
            }
            pau.setPmAttendance(now);//下午考勤时间
            pau.setEndState(state.getName());  //下班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,9-其它
            pam.setEndState(state.getIndex());
            pam.setEndCommited(Boolean.FALSE);
        }
        pau.setUpdator(operator.getUserID());
        pau.setUpdateName(operator.getUserName());
        personnelAttendanceUserDao.update(pau);
        if(StringUtils.isNotEmpty(kuangGong)){
            List<WorkOffBlockDto> absenteeisms = JSON.parseArray(kuangGong, WorkOffBlockDto.class);
            if(StringUtils.isNotEmpty(pam.getAbsenteeisms())) {
                absenteeisms.addAll(JSON.parseArray(pam.getAbsenteeisms(), WorkOffBlockDto.class));
            }
            List<Integer> removeDetailIds = WorkOffBlockDto.mergeWithLimit(absenteeisms, pau.getBeginTime(), pau.getEndTime());
            if(!absenteeisms.isEmpty()) {
                pam.setAbsenteeisms(JSON.toJSONString(absenteeisms));
                pam.setAbsMd5("");
                if(absenteeisms.stream().anyMatch(item->pau.getBeginTime().equals(item.getBegin()))) {
                    pam.setBeginState(AttendanceType.absenteeism.getIndex());
                    if (dtype == 1) {
                        state = AttendanceType.absenteeism;
                    }
                }
                if(absenteeisms.stream().anyMatch(item->pau.getEndTime().equals(item.getEnd()))) {
                    pam.setEndState(AttendanceType.absenteeism.getIndex());
                    if (dtype != 1) {
                        state = AttendanceType.absenteeism;
                    }
                }
            }
            if(!removeDetailIds.isEmpty()) {
                removeDetailIds.forEach(id->personnelAttendanceUserDetailDao.deleteById(id));
            }
        }
        pam.setUpdator(operator.getUserID());
        pam.setUpdateName(operator.getUserName());
        if(type != null && AttendanceType.getMultiple().contains(type.getIndex()) && !type.equals(state)) {
            pam.setType(AttendanceType.other.getIndex());
        } else {
            pam.setType(state.getIndex());
        }
        personnelAttendanceManualService.saveOrUpdate(pam);
        setAttendanceUserEvent(user, now, AttendanceType.workdayLieu);
        asyncUpdateAttendanceUserEvents();
    }

    @Override
    public JsonResult clock(User user, WorkdayType type, Date punchTime, String deviceUuid, AttendanceTerminaType terminaType, String terminalUuid, String brand, String model, PunchType punchType, Integer leaveId, Integer overTimeId, String image) {
        JsonResult result;
        IotTerminal iotTerminal = iotService.attendanceDevice(user.getOid(), Boolean.TRUE, deviceUuid, null, null);
        if (iotTerminal!=null) {
            PersonnelAttendancePunch pap = new PersonnelAttendancePunch();
            pap.setWorkdayType(type.getIndex());
            pap.setOrg(user.getOid());
            pap.setUser(user.getUserID());
            pap.setDept(StringUtils.isNotEmpty(user.getDepartment()) ? Integer.getInteger(user.getDepartment()) : null);
            pap.setSysDevice(iotTerminal.getId()); //打卡设备ID
            pap.setPunchTerminal(iotTerminal.getId()); //人事打卡设备ID
            pap.setPunchTime(punchTime);  //打卡时间
            pap.setPunchType(punchType.getIndex());  //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
            pap.setTerminalType(terminaType.getIndex());
            pap.setTerminalUuid(terminalUuid);
            pap.setTerminalBrand(brand);
            pap.setTerminalModel(model);
            pap.setImage(image); //快照图片内容
            pap.setType(AttendanceType.pending.getIndex());
            if(leaveId!=null) {
                pap.setBusinessService("leaveService");
                pap.setBusiness(leaveId);
            } else if (overTimeId!=null) {
                pap.setBusinessService("overtimeService");
                pap.setBusiness(overTimeId);
            }
            personnelAttendancePunchDao.save(pap);
            PunchDto dto = new PunchDto(pap);
            fillAttributes(dto);
            result = new JsonResult(1, dto);
        } else {
            result = new JsonResult(new MyException("-4", "不是本机构的打卡机"));
        }
        return result;
    }

    private PersonnelAttendanceTerminal getPersonnelAttendanceTerminal(String terminalUuid, String brand, String model, Byte type) {
        StringBuilder hql = new StringBuilder("from PersonnelAttendanceTerminal");
        Map<String,Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        if (StringUtils.isNotEmpty(terminalUuid)){
            where.add("terminalUuid=:terminalUuid");   //唯一标识/webchat union_id
            params.put("terminalUuid",terminalUuid);
        }
        if (StringUtils.isNotEmpty(brand)){
            where.add("brand=:brand");  //品牌/昵称
            params.put("brand",brand);
        }
        if (StringUtils.isNotEmpty(model)){
            where.add("model=:model");  //型号
            params.put("model",model);
        }
        if (type!=null){
            where.add("type=:type");   //类型:1-设备,2-webchat
            params.put("type",type);
        }
        if (!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        }
        PersonnelAttendanceTerminal personnelAttendanceTerminal = (PersonnelAttendanceTerminal) personnelAttendanceTerminalDao.getByHQLWithNamedParams(hql.toString(),params);
        return personnelAttendanceTerminal;
    }

    @Override
    public List<PunchDto> getPersonnelAttendancePunches(Date start, NewDateUtils.Period period, Date end, PunchDto search, User user, PageInfo pageInfo) {
        List<PunchDto> result = new ArrayList<>();
        StringBuilder hql = new StringBuilder("select new cn.sphd.miners.modules.generalAffairs.dto.PunchDto(user,id,cardNo,punchTime,punchType,terminalType,terminalUuid,terminalBrand,terminalModel, image, sysDevice) from PersonnelAttendancePunch where org=:org");
        List<String> where = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(){{
            put("org", user.getOid());
        }};
        List<Integer> searchParamIds;
        Pair<Map<Integer, User>, Map<Integer, IotTerminal>> attributesMap = Pair.of(new HashMap<>(), new HashMap<>());
        if ((searchParamIds = searchUsers(search, user, attributesMap))!=null) {//查询用户、部门
            switch (searchParamIds.size()) {
                case 0:
                    return result;//new ArrayList<>(0);
                case 1:
                    where.add("user=:user");
                    params.put("user", searchParamIds.get(0));
                    break;
                default:
                    where.add("user in (:users)");
                    params.put("users", searchParamIds);
            }
        }
        if ((searchParamIds = searchSysDevice(search, user, attributesMap))!=null) {//查询考勤机
            switch (searchParamIds.size()) {
                case 0:
                    return result;//new ArrayList<>(0);
                case 1:
                    where.add("sysDevice=:sysDevice");
                    params.put("sysDevice", searchParamIds.get(0));
                    break;
                default:
                    where.add("sysDevice in (:sysDevices)");
                    params.put("sysDevice", searchParamIds);
            }
        }
        //查询打卡表数据
        if(search.getType()!=null) {
            where.add("terminalType=:terminalType");
            params.put("terminalType", search.getType());
        }
        if(StringUtils.isNotBlank(search.getTerminalUuid())) {
            where.add("terminalUuid like :terminalUuid");
            params.put("terminalUuid", "%" + search.getTerminalUuid() + "%");
        }
        if(StringUtils.isNotBlank(search.getBrand())) {
            where.add("terminalBrand like :terminalBrand");
            params.put("terminalBrand", "%" + search.getBrand() + "%");
        }
        if(StringUtils.isNotBlank(search.getModel())) {
            where.add("terminalModel like :terminalModel");
            params.put("terminalModel", "%" + search.getModel() + "%");
        }
        if(StringUtils.isNotBlank(search.getCardNo())) {
            where.add("cardNo like :cardNo");
            params.put("cardNo", "%" + search.getCardNo() + "%");
        }
        if(search.getPunchType() != null) {
            where.add("punchType=:punchType");
            params.put("punchType", search.getPunchType());
        }
        if (start!=null || end!=null) {
            fillDateWhere(start, period, end, where, params);
        }
        if (!where.isEmpty()) {
            hql.append(" and ").append(StringUtils.join(where, " and "));
        }
        hql.append(" order by punchTime desc");
        result = personnelAttendancePunchDao.getListByHQLWithNamedParams(hql.toString(), params, pageInfo);
        fillAttributes(result, attributesMap);
        return result;
    }
    private void fillDateWhere(Date start, NewDateUtils.Period period, Date end, List<String> where, Map<String, Object> params) {
        //start,period不为空，end失效
        if(start!=null && period!=null && !NewDateUtils.Period.unlimit.equals(period)) {
            end = NewDateUtils.Period.changePeriodEnd(start, 1, period);
        }
        //start,end均为整日,period为空，end取当天结束
        if(period==null && NewDateUtils.isTimeZero(end) && (start==null || NewDateUtils.isTimeZero(start))) {
            end = NewDateUtils.getLastTimeOfDay(end);
            if (start.equals(NewDateUtils.today())){
                end = new Date();
            }
        }
        if(start!=null && end==null) {
            where.add("punchTime>=:start");
            params.put("start",start);
        } else if (start==null && end!=null) {
            where.add("punchTime<=:end");
            params.put("end",end);
        } else if (start!=null && end!=null) {
            where.add("punchTime between :start and :end");
            params.put("start",start.getTime()<=end.getTime()?start:end);
            params.put("end",start.getTime()<=end.getTime()?end:start);
        }
    }
    private void fillAttributes(PunchDto result) {
        User user;
        IotTerminal iotTerminal;
        if (result.getUserID() != null) {
            if ((user = userService.getUserByID(result.getUserID())) != null) {
                if (StringUtils.isNumeric(user.getDepartment())) {
                    Organization org = orgService.getOrgByOid(Integer.valueOf(user.getDepartment()), OrgService.OrgType.department);
                    user.setDepartName(org.getName());
                }
                result.setUserName(user.getUserName());
                result.setDepartment(user.getDepartment());
                result.setName(user.getDepartName());
            }
        }
        if (result.getSysDevice() != null) {
            if ((iotTerminal = iotService.getById(result.getSysDevice())) != null) {
                result.setDeviceUuid(iotTerminal.getDeviceUuid());
                result.setSn(iotTerminal.getSn());
            }
        }
    }
    private void fillAttributes(List<PunchDto> result, Pair<Map<Integer, User>, Map<Integer, IotTerminal>> attributesMap) {
        User user;
        IotTerminal iotTerminal;
        for(PunchDto item : result) {
            if (item.getUserID()!=null) {
                if((attributesMap.getLeft().get(item.getUserID()))==null
                        && (user = userService.getUserByID(item.getUserID()))!=null) {
                    if (StringUtils.isNumeric(user.getDepartment())) {
                        Organization org = orgService.getOrgByOid(Integer.valueOf(user.getDepartment()), OrgService.OrgType.department);
                        user.setDepartName(org.getName());
                    }
                    attributesMap.getLeft().put(user.getUserID(), user);
                }
                if((user = attributesMap.getLeft().get(item.getUserID()))!=null) {
                    item.setUserName(user.getUserName());
                    item.setDepartment(user.getDepartment());
                    item.setName(user.getDepartName());
                }
            }
            if (item.getSysDevice()!=null) {
                if((attributesMap.getRight().get(item.getSysDevice()))==null
                        && (iotTerminal = iotService.getById(item.getSysDevice()))!=null) {
                    attributesMap.getRight().put(iotTerminal.getId(), iotTerminal);
                }
                if(( iotTerminal = attributesMap.getRight().get(item.getSysDevice()))!=null) {
                    item.setDeviceUuid(iotTerminal.getDeviceUuid());
                    item.setSn(iotTerminal.getSn());
                }
            }
        }
    }
    private List<Integer> searchUsers(PunchDto search, User user, Pair<Map<Integer, User>, Map<Integer, IotTerminal>> attributesMap) {
        if (search.getUserID()!=null) {
            return new ArrayList<>(1) {{
                add(search.getUserID());
            }};
        } else {
            if(StringUtils.isNotEmpty(search.getUserName()) || StringUtils.isNotBlank(search.getDepartment()) || StringUtils.isNotBlank(search.getName())) {
                User userSearch = new User();
                userSearch.setUserName(search.getUserName());
                userSearch.setDepartment(search.getDepartment());
                List<User> users = userService.getAllUsersByOrgDepart(user.getOid(), userSearch, search.getName());
                attributesMap.getLeft().putAll(users.stream().collect(Collectors.toMap(User::getUserID,item->item)));
                return users.stream().map(User::getUserID).toList();
            }
            return null;
        }
    }
    private List<Integer> searchSysDevice(PunchDto search, User user, Pair<Map<Integer, User>, Map<Integer, IotTerminal>> attributesMap) {
        if (search.getSysDevice()!=null) {
            return new ArrayList<>(1) {{
                add(search.getSysDevice());
            }};
        } else if (StringUtils.isNotBlank(search.getTerminalUuid()) || StringUtils.isNotBlank(search.getSn())){
            IotTerminal iotSearch = new IotTerminal();
            iotSearch.setDeviceUuid(search.getDeviceUuid());
            iotSearch.setSn(search.getSn());
            List<IotTerminal> iotTerminals= iotService.searchIotTerminals(iotSearch, user.getOid());
            attributesMap.getRight().putAll(iotTerminals.stream().collect(Collectors.toMap(IotTerminal::getId,item->item)));
            return iotTerminals.stream().map(IotTerminal::getId).toList();
        } else {
            return null;
        }
    }

    @Override
    public Map<String, Object> getAllTypeAttendance(Integer oid, Integer deptId, Integer userId, Date currentTime) {
        String key = "miners:WorkAttendance:getAllTypeAttendance:"+NewDateUtils.getYearMonthDay(currentTime)+oid+":"+(deptId==null?"-1":deptId)+(userId==null?"-1":userId);
        String cache;
        if((cache = (String) redisTemplate.opsForValue().get(key))!=null) {
            try {
                Map<String, Object> result = JSON.parseObject(cache, Map.class);
                if (redisTemplate.getExpire(key, TimeUnit.MINUTES) < 5) {//缓存还剩5分钟（一半时间），更新缓存
                    taskExecutor.execute(new UpdategAllTypeAttendanceCache(oid, deptId, userId, currentTime, key, this));
                }
                return result;
            } catch (JSONException e) {
                logger.error("getAllTypeAttendance JSON.parseObject error, cache = " + cache, e);
            }
        }
        Map<String, Object> result = innerGetAllTypeAttendance(oid, deptId, userId, currentTime, key);
        return result;
    }
    //内部方法，供更新缓存调用
    public Map<String, Object> innerGetAllTypeAttendance(Integer oid, Integer deptId, Integer userId, Date currentTime, String key) {
        Map<AttendanceType, Set<PersonnelAttendanceUser>> pauMap = new HashMap<>();
        List<Integer> pids = getPidByOid(oid, deptId, userId, currentTime, null, null, 1); //参与考勤人员（应出勤人）考勤日表id
        List<PersonnelAttendanceUser> pauList = new ArrayList<>();//参与考勤人员（应出勤人）考勤日表数据
        List<PersonnelAttendanceUser> realUser = new ArrayList<>();//实际出勤人
        List<UserDto> notInAttendanceUser = new ArrayList<>();  //不参与考勤人员
        PersonnelAttendanceException personnelAttendanceException = getPersonnelAttendanceException(oid, currentTime);
        WorkdayType workOrRest=null;  //当天是上班还是修假  1-假 2-班
        if (personnelAttendanceException != null && !"".equals(personnelAttendanceException.getType()) && personnelAttendanceException.getType() != null
                &&(workOrRest = WorkdayType.getByName(personnelAttendanceException.getType()))!=null) {//当天是上班还是休息  1-假 2-班
            switch (workOrRest) {
                case Workday://班
                    notInAttendanceUser = getNoUser(currentTime, oid, userId, deptId!=null?deptId.toString():null, null);  //不参与考勤人员
                    pids.forEach(pid->{
                        PersonnelAttendanceUser pau = personnelAttendanceUserDao.get(pid);
                        pauList.add(pau);
                        if (!AttendanceType.pending.getName().equals(pau.getBeginState())//只有上班考勤录入后，才能算出勤
                                && pau.getPersonnelAttendanceUserDetailHashSet().stream().
                                filter(detail -> WorkdayType.Workday.getIndex().equals(detail.getWorkdayType())//出勤
                                        && !AttendanceType.overtime.getName().equals(detail.getType())//不算加班
                                        && detail.getDuration()>0).findAny().isPresent()) {//上班时间不为空

                            realUser.add(pau);   //实际出勤人员
                        }
                        pau.getPersonnelAttendanceUserDetailHashSet().stream().filter(detail->AttendanceType.getShowList().contains(AttendanceType.getByName(detail.getType()))).forEach(detail -> {
                            AttendanceType type = AttendanceType.getByName(detail.getType());
                            List<PersonnelAttendanceUserDetail> detailList = pau.getPersonnelAttendanceUserDetailList();
                            detailList.add(detail);
                            pau.setPersonnelAttendanceUserDetailList(detailList);
                            Set<PersonnelAttendanceUser> set = pauMap.getOrDefault(type, new HashSet<>());
                            set.add(pau);
                            pauMap.put(type, set);
                        });
                    });
                    break;
                case DayOff://当天为无需考勤的
                    pids.forEach(pid->{
                        PersonnelAttendanceUser pau = personnelAttendanceUserDao.get(pid);
                        pau.getPersonnelAttendanceUserDetailHashSet().stream().filter(detail -> AttendanceType.overtime.getName().equals(detail.getType())).forEach(detail -> {
                            AttendanceType type = AttendanceType.getByName(detail.getType());
                            List<PersonnelAttendanceUserDetail> detailList = pau.getPersonnelAttendanceUserDetailList();
                            detailList.add(detail);
                            pau.setPersonnelAttendanceUserDetailList(detailList);
                            Set<PersonnelAttendanceUser> set = pauMap.getOrDefault(type, new HashSet<>());
                            set.add(pau);
                            pauMap.put(type, set);
                        });
                    });
                    break;
            }
        }
        Map<String, Object> result = new HashMap<>(24);
        result.put("workOrRest", workOrRest!=null?workOrRest.getName():WorkdayType.DayOff.getName()); //当天是上班还是休息  1-假 2-班
        result.put("userList", pauList); //应出勤人员
        result.put("userTotalNum", pauList.size());//应出勤人数
        result.put("notInAttendanceUser", notInAttendanceUser);//不参与考勤人员
        result.put("notInAttendance", notInAttendanceUser.size());//不参与考勤人数
        result.put("realUser", realUser);//实际出勤人
        result.put("realNum", realUser.size());//实际出勤人数
        result.put("lateUser", pauMap.getOrDefault(AttendanceType.late, Collections.emptySet()));//迟到人员
        result.put("lateNum", pauMap.getOrDefault(AttendanceType.late, Collections.emptySet()).size());//迟到人数
        result.put("leaveEarlyUser", pauMap.getOrDefault(AttendanceType.leaveEarly, Collections.emptySet()));//早退人员
        result.put("leaveEarlyNum", pauMap.getOrDefault(AttendanceType.leaveEarly, Collections.emptySet()).size());//早退人数
        result.put("outsideUser", pauMap.getOrDefault(AttendanceType.goOut, Collections.emptySet()));//外出人员
        result.put("outsideNum", pauMap.getOrDefault(AttendanceType.goOut, Collections.emptySet()).size());//外出人数
        result.put("leaveUser", pauMap.getOrDefault(AttendanceType.leave, Collections.emptySet()));//请假人员
        result.put("leaveNum", pauMap.getOrDefault(AttendanceType.leave, Collections.emptySet()).size());//请假人数
        result.put("travelUser", pauMap.getOrDefault(AttendanceType.travel, Collections.emptySet()));//出差人员
        result.put("travelNum", pauMap.getOrDefault(AttendanceType.travel, Collections.emptySet()).size());//出差人数
        result.put("absenteeismUser", pauMap.getOrDefault(AttendanceType.absenteeism, Collections.emptySet()));//旷工人员
        result.put("absenteeismNum", pauMap.getOrDefault(AttendanceType.absenteeism, Collections.emptySet()).size());//旷工人数
        result.put("overTimeUser", pauMap.getOrDefault(AttendanceType.overtime, Collections.emptySet()));//加班人员
        result.put("overTimeNum", pauMap.getOrDefault(AttendanceType.overtime, Collections.emptySet()).size());//加班人数
        redisTemplate.opsForValue().set(key, JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect), 10, TimeUnit.MINUTES);//缓存10分钟
        return result;
    }
    //sorcType 排序规则(1-名字首字母排序  2-id倒序)
    public List<Integer> getPidByOid(Integer oid, Integer dept, Integer userId, Date openDate, String beginState, String userName, Integer sorcType) {
        Assert.notNull(oid, "机构不能为空");
        List<String> where = new ArrayList<>();
        StringBuffer hql = new StringBuffer("select p.id from PersonnelAttendanceUser p,User u where p.user=u.userID and oid=:oid");
        Map<String, Object> params = new HashMap<>() {{
            put("oid", oid);
        }};
        if (openDate != null) {
            where.add("p.attendanceDate=:attendanceDate");
            params.put("attendanceDate", openDate);
        }
        if (StringUtils.isNotBlank(beginState)) {
            where.add("p.beginState=:beginState");
            params.put("beginState", beginState);
        }
        if (dept != null) {
            if (dept != 0) {
                where.add("p.dept=:dept");
                params.put("dept", dept);
            } else {
                where.add("p.dept is null");
            }
        }
        if (StringUtils.isNotBlank(userName)) {
            where.add("u.userName like :userName");
            params.put("userName", "%" + userName + "%");
        }
        if (userId != null) {  //取此人员的下属以及间接下属
            where.add("(u.userID=:userId or u.rankUrl like :rankUrl)");
            params.put("userId", userId); //取职工自己
            params.put("rankUrl", "%/" + userId + "/%");  //取本职工的直接以及间接下级
        }
        if (!where.isEmpty()) {
            hql.append(" and ").append(StringUtils.join(where, " and "));
        }
        if (sorcType == 1) {
            hql.append(" order by convert(u.userName, 'gbk') asc");
        } else if (sorcType == 2) {
            hql.append(" order by id desc");
        }
        return personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params);
    }

    @Override
    public void attendance(Model model, User user) {
        List<PersonnelAttendanceRecord> personnelAttendanceRecords = this.getPersonnelAttendanceRecordByOid(user.getOid(),"4",new Date(),null); //查询要生效的修改记录
        for (PersonnelAttendanceRecord personnelAttendanceRecord:personnelAttendanceRecords) {
            personnelAttendanceRecord.setOperation("3");
            personnelAttendanceRecordDao.update(personnelAttendanceRecord);
        }

        Date startUsingSystemTime = this.getStartUsingSystemTime(user.getOid()); //获取系统开始使用时间
        model.addAttribute("startUsingSystemTime",startUsingSystemTime==null?startUsingSystemTime:NewDateUtils.dateToString(startUsingSystemTime, "yyyy-MM-dd"));  //系统开始使用时间

        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = this.getPersonnelAttendanceConfigList(user.getOid(),null,null);
        if (!personnelAttendanceConfigs.isEmpty()){
            model.addAttribute("status",1);  //已进行考勤设置
        }else {
            model.addAttribute("status",0);  //未进行考勤设置
        }
    }

    @Override
    public List<PersonnelAttendanceRecord> getPersonnelAttendanceRecordByOid(Integer oid, String operation, Date effectDate, PageInfo page) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelAttendanceRecord where org=:org";
        map.put("org",oid);
        if (!"".equals(operation) && operation != null) {
            hql+= " and operation=:operation";
            map.put("operation",operation);
        }
        if (effectDate != null) {
            hql += " and effectDate<=:effectDate";
            map.put("effectDate",effectDate);  //NewDateUtils.dateFromString(effectDate,"yyyy-MM-dd HH:mm:ss")
        }
        List<PersonnelAttendanceRecord> personnelAttendanceRecords = personnelAttendanceRecordDao.getListByHQLWithNamedParams(hql,map,page);
        return personnelAttendanceRecords;
    }

    @Override
    public List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigList(Integer oid, String type, Date openDate) {
        Map<String, Object> map = new HashMap<>();
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceConfig where enabled = 1");  //有效的规则
        if (oid != null) {
            hql.append(" and org=:org");
            map.put("org", oid);
        }
        if (type != null) {
            hql.append(" and type=:type");
            map.put("type", type);
        }
        if (openDate != null) {
            hql.append(" and openDate=:openDate");
            map.put("openDate", openDate);
        }
        hql.append(" order by id desc");
        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql.toString(), map);
        return personnelAttendanceConfigs;
    }

    @Override
    public Map<String, Object> setTimeTable(User user, List timeTable) {
        Map<String,Object> map = new HashMap<>();
        for (int j = 0; j < timeTable.size(); j++) {
            Date dateAttendance = null;
            Integer oldState = 1; //1-有老数据 0-没有老数据了

            List timeTable1 = JSONArray.fromObject(timeTable.get(j));
            for(int n = 0; n < timeTable1.size(); n++) {
                JSONObject jo1 = JSONObject.fromObject(timeTable1.get(n));
                Date dateAttendance1 = NewDateUtils.today(NewDateUtils.dateFromString(jo1.getString("date"), "yyyy-MM-dd"));
                if (dateAttendance == null) {
                    dateAttendance = dateAttendance1;
                }
                if (1 == oldState) {
                    Date beginTime = NewDateUtils.changeMonth(dateAttendance, 0);  //获取本月第一天
                    Date endTime = NewDateUtils.getLastTimeOfMonth(beginTime);//获取本月最后一天
                    List<PersonnelAttendanceException> personnelAttendanceExceptionOlds = this.getPersonnelAttendanceExceptionByMonth(user.getOid(), beginTime, endTime);
                    for (PersonnelAttendanceException pOld : personnelAttendanceExceptionOlds) {
                        personnelAttendanceExceptionDao.delete(pOld);
                    }
                    workAttendanceService.clearWorkDateCaches(user.getOid());//清除工作日缓存
                    oldState = 0;
                }
                this.addPersonnelAttendanceException(user.getOid(), dateAttendance1, WorkdayType.getByName(jo1.getString("typeSet")), user); //添加作息日期
            }
        }
        map.put("status",1);  //设置成功

        return map;
    }

    @Override
    public Map<String, Object> getTimeTable(Integer oid,Date timeTable) {
        Map<String, Object> map = new HashMap<>();
        if (timeTable==null){
            timeTable = NewDateUtils.today();
        }
        Date beginTime = NewDateUtils.changeMonth(timeTable, 0);  //获取某月第一天
        Date endTime = NewDateUtils.getLastTimeOfMonth(beginTime);//获取某月最后一天
        List<PersonnelAttendanceException> personnelAttendanceExceptions = this.getPersonnelAttendanceExceptionByMonth(oid, beginTime, endTime);
        map.put("thisMonth", getYearAndMonth(beginTime));  //获取某月的年和月
        map.put("personnelAttendanceExceptions", personnelAttendanceExceptions);
        map.put("today", NewDateUtils.dateToString(NewDateUtils.today(), "yyyy-MM-dd"));  //今天日期
        return map;
    }

    /**
     *<AUTHOR>
     *@date 2018/5/8 10:04
     *获取年和月（返回 xxxx年x月）
     */
    public static String getYearAndMonth(Date d){
        long yearMonth = NewDateUtils.getYearMonth(d);
        return (yearMonth/100)+"年"+(yearMonth%100)+"月";
    }

    /**
     * getStartUsingSystemTime 获取机构首次启用考勤的时间
     * <AUTHOR>
     * @since 4.0
     * @param oid
     * @return Date
     * @date 2025-07-16 17:30:09
     **/
    @Override
    public Date getCurrentsingSystemTime(Integer oid) {
        List<Date> list = workAttendanceService.getPersonnelAttendanceConfigsOpenDates(oid); //按时间倒排，首次启用时间取最后一条。
        Date date = null;
        if (!list.isEmpty()) {
            for (Date date1 : list) {
                if (date1.getTime() <= new Date().getTime()) {
                    date = date1;
                    break;
                }
            }
        }
        return date;
    }

    /**
     * attendanceDept 查询当前的规则使用的所有部门
     * <AUTHOR>
     * @since 4.0
     * @param oid(机构id) 、 type(1-正常班 2-倒班)
     * @date 2018/5/10 16:27
     */
    @Override
    public List<PersonnelAttendanceConfig> attendanceDept(Date openDate, Integer oid, String type) {
        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = workAttendanceService.getPersonnelAttendanceConfigList(oid, type, openDate);
        for (PersonnelAttendanceConfig ppp : personnelAttendanceConfigs) {
            ppp.setBeginTimeString(NewDateUtils.dateToString(ppp.getBeginTime(), "HH:mm"));
            ppp.setEndTimeString(NewDateUtils.dateToString(ppp.getEndTime(), "HH:mm"));
            ppp.setBreakBeginString(NewDateUtils.dateToString(ppp.getBreakBegin(), "HH:mm"));
            ppp.setBreakEndString(NewDateUtils.dateToString(ppp.getBreakEnd(), "HH:mm"));
            if (ppp.getInputTime()!=null) {
                ppp.setInputTimeString(NewDateUtils.dateToString(ppp.getInputTime(), "HH:mm"));
            }
            //1.342字段已改名
            //ppp.setMiddleBreak(ppp.isBreak());
            String departmentIds = "";
            Set<PersonnelAttendanceDepartmentConfig> personnelAttendanceDepartmentConfigs = ppp.getPersonnelAttendanceDepartmentConfigHashSet();
            for (PersonnelAttendanceDepartmentConfig p : personnelAttendanceDepartmentConfigs) {
                if (p.getDept() != null && p.getDept() == 0) {
                    p.setDeptName("其他");
                } else if (p.getDept() != null) {
                    Organization org = orgService.getOrgByOid(p.getDept(), OrgService.OrgType.department);
                    if (org != null) {
                        p.setDeptName(org.getName());
                    }
                }
                departmentIds = StringUtils.isNotEmpty(departmentIds)?departmentIds+","+p.getDept():p.getDept().toString();
            }
            ppp.setDepartmentIds(departmentIds);

        }
        return personnelAttendanceConfigs;
    }

    @Override
    public JsonResult getAttendanceTime(User user, String type) {
        Integer oid = user.getOid();
        Map<String, Object> map = new HashMap<>();
        List<PersonnelAttendanceConfig> personnelAttendanceConfigList = new ArrayList<>();

        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(oid);  //获取系统时间
        if (startUsingSystemTime!=null ){
            if (new Date().before(startUsingSystemTime)){  //还没到系统开始启用时间
                personnelAttendanceConfigList = this.attendanceDept(startUsingSystemTime,oid,type);  //查询当前的规则使用的所有部门
            }else {   //系统已开始使用
//                Date openDate = workAttendanceOldService.getPersonnelAttendanceConfigOpenDate(oid,type,new Date());
                Date openDate = workAttendanceService.getCurrentsingSystemTime(oid);
//                if (openDate.getTime()<=new Date().getTime()){
                personnelAttendanceConfigList = this.attendanceDept(openDate,oid,type);   //查询当前的规则使用的所有部门
//                }
            }
            map.put("startUsingSystemTime",NewDateUtils.dateToString(startUsingSystemTime,"yyyy-MM-dd"));  //系统开始使用时间
        }else {
            map.put("startUsingSystemTime",null);  //系统开始使用时间
        }

        Organization organization = new Organization();
        organization.setId(0);
        organization.setName("其他");
        organization.setLevel(1);
        organization.setOrgType(2);
        List<Organization> personnelAttendanceDepartmentConfigs = new ArrayList<>();  //设置过的部门
        for (PersonnelAttendanceConfig p:personnelAttendanceConfigList) {
            for (PersonnelAttendanceDepartmentConfig pp:p.getPersonnelAttendanceDepartmentConfigHashSet()) {
                if (pp.getDept()!=null){
                    if (pp.getDept()!=0){
                        personnelAttendanceDepartmentConfigs.add(orgService.getOrgByOid(pp.getDept(), OrgService.OrgType.department));
                    }else {
                        personnelAttendanceDepartmentConfigs.add(organization);
                    }
                }
            }
        }

        List<Organization> orgs = orgService.getAllDepartmentById(oid);  //获取机构的所有部门
        orgs.add(organization);
        if (!personnelAttendanceDepartmentConfigs.isEmpty()){
            orgs.removeAll(personnelAttendanceDepartmentConfigs);   //取差集
            if (!orgs.isEmpty()){
                map.put("status",1);  //有未设置的部门
            }else {
                map.put("status",0);  //没有未设置的部门
            }
        }else {
            map.put("status", 1);  //有未设置的部门
        }

        //判断系统中是否设置了作息时间
        if (this.hasPersonnelAttendanceExceptionByMonth(oid,null,null)){
            map.put("exceptionType",1);  //已设置了作息时间
        }else {
            map.put("exceptionType",0);  //未设置作息时间
        }

        map.put("personnelAttendanceConfigs",personnelAttendanceConfigList);
        return new JsonResult(1,map);
    }

class UpdategAllTypeAttendanceCache implements Runnable {
    private Integer oid;
    private Integer deptId;
    private Integer userId;
    private Date currentTime;
    private String key;
    private WorkAttendanceServiceImpl service;

    public UpdategAllTypeAttendanceCache(Integer oid, Integer deptId, Integer userId, Date currentTime, String key, WorkAttendanceServiceImpl service) {
        this.oid = oid;
        this.deptId = deptId;
        this.userId = userId;
        this.currentTime = currentTime;
        this.key = key;
        this.service = service;
    }

    @Override
    public void run() {
        service.innerGetAllTypeAttendance(oid, deptId, userId, currentTime,key);
    }

}

    @Override
    public List<PersonnelAttendanceUserDto> getAttendanceList(Integer oid, Date today, Date yesterday, PageInfo pageInfo) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select distinct new cn.sphd.miners.modules.generalAffairs.dto.PersonnelAttendanceUserDto( u.userID,u.userName,u.department,u.departName) from PersonnelAttendanceUser p, User u where p.user=u.userID and u.roleCode!='super'");
        if (oid != null && !oid.equals(0)) {
            hql.append(" and u.oid=:oid");
            params.put("oid", oid);
        }
        if (today != null && yesterday != null) {
            hql.append(" and (p.attendanceDate=:today or p.attendanceDate=:yesterday)");
            params.put("today", today);
            params.put("yesterday", yesterday);
        }
        hql.append(" order by convert(u.userName, 'gbk')");
        List<PersonnelAttendanceUserDto> result = personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params, pageInfo);
        if (result != null && result.size() > 0) {
            List<Integer> uids = new ArrayList<>();
            for (PersonnelAttendanceUserDto u : result) {
                uids.add(u.getUserID());
            }
            hql.setLength(0);
            params.clear();
            hql.append("from PersonnelAttendanceUser where user in (:uids) and attendanceDate=:day");
            params.put("uids", uids);
            params.put("day", today);
            List<PersonnelAttendanceUser> todays = personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params);
            HashMap<Integer, PersonnelAttendanceUser> todaymap = new HashMap(todays.size());
            for (PersonnelAttendanceUser u : todays) {
                todaymap.put(u.getUser(), u);
            }
            params.put("day", yesterday);
            List<PersonnelAttendanceUser> yesterdays = personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params);
            HashMap<Integer, PersonnelAttendanceUser> yesterdaymap = new HashMap(todays.size());
            for (PersonnelAttendanceUser u : yesterdays) {
                yesterdaymap.put(u.getUser(), u);
            }
            for (PersonnelAttendanceUserDto rec : result) {
                Date beginTime = getBeginTime(oid, Integer.valueOf(rec.getDeptId()), today);
                PersonnelAttendanceUser u = todaymap.get(rec.getUserID());
                if (u != null) {
                    rec.setIdToday(u.getId());
                    rec.setBeginStateToday(u.getBeginState());
                    rec.setAmAttendanceToday(u.getAmAttendance());
                }
                u = yesterdaymap.get(rec.getUserID());
                if (u != null) {
                    rec.setIdYesterday(u.getId());
                    rec.setBeginStateYesterday(u.getBeginState());
                    rec.setEndStateYesterday(u.getEndState());
                    rec.setAmAttendanceYesterday(u.getAmAttendance());
                    rec.setPmAttendanceYesterday(u.getPmAttendance());
                }
                if (beginTime != null) {
                    rec.setWorkBeginTime(NewDateUtils.dateToString(beginTime, "HH:mm"));
                } else {
                    rec.setWorkBeginTime("00:00");
                }
            }
        }
        return result;
    }

    @Override
    public Date getBeginTime(Integer oid, Integer deptId, Date openDate) {
        PersonnelAttendanceConfig config = this.getPersonnelAttendanceConfigByDept(oid, deptId, openDate);
        if (config != null) {
            return config.getBeginTime();
        } else {
            return null;
        }
    }

//    /**
//     * 批量设置默认考勤。除了正常默认考勤外，如果涉及考勤设置变更
//     * 本方法1、把考勤开始前当月月初到开始前一天设为无需考勤；2、把不在考勤人员，但当月其他日期有考勤月记录的人员自动设置无需考勤。
//     * 考勤开始后人员从无需考勤设为需要考勤，需要另外调用月方法设置从月初到设置当天设为无需考勤。
//     *
//     * @param oid
//     * @return true 机构已启用考勤； false 机构未启用考勤
//     */
//    @Override
//    public void setDefaultWorkAttendance(Integer oid) {
//        if (oid < 0) {//禁用旧扫描
//            Date start, startWorkday, inputDate;
//            final long end = NewDateUtils.today().getTime();   //今天
//            Map<String, Object> map = new HashedMap();
//            String workBegin, workEnd, breakBegin, breakEnd;
//            boolean hasInput, isNewAbsent;
//            PersonnelAttendanceUser personnelAttendanceUser;
//            PersonnelAttendanceOrgMonitor attendanceOrgMonitor = getOrgMonitor(oid);
//            ArrayList<Integer> uids = new ArrayList<>();
//            if (attendanceOrgMonitor.getStartDate() == null) { //跑过一次第一个考勤工作日考勤录入时间之后，就不再执行（第一个工作日的上午需要特殊处理）。
//                Date systemTime = getStartUsingSystemTime(oid);
//                if (systemTime == null) {
//                    return;//系统还没有设置考勤
//                }
//                systemTime = NewDateUtils.today(systemTime);
//                if (NewDateUtils.today().getTime() < systemTime.getTime()) {
//                    return;//系统还没有开始启用考勤
//                } else if (systemTime.getTime() <= NewDateUtils.today().getTime()) {  //已启用，但是还没有考勤数据
//                    addAttendanceException(oid, systemTime);//判断并添加作息时间
//                    //获取第一个工作日
//                    startWorkday = getNextDateByWork(NewDateUtils.yesterday(systemTime), oid);
//                    inputDate = getInputTime(oid, NewDateUtils.today(startWorkday));
//                    if (System.currentTimeMillis() < inputDate.getTime()) { //手动录入的，还没到第一一个工作日的考勤录入时间，无需处理上班考勤
//                        if ((start = attendanceOrgMonitor.getScanDate()) != null) {//跑过假attendanceOrgMonitor.getScanDate()不为空
//                            if (attendanceOrgMonitor.getScanTimes().equals((byte) 2)) {//上次是假
//                                start = NewDateUtils.tomorrow(start);//从第二天开始跑
//                                attendanceOrgMonitor.setScanTimes((byte) 0);
//                            }// else 上次是第一个工作日，但是没过考勤录入时间，无需再跑。
//                        } else {
//                            start = systemTime;//没跑过假
//                        }
//                        hasInput = false;
//                    } else {
//                        start = startWorkday;
//                        hasInput = true;
//                    }
//                    //考勤第一天，非每月1日添加无需考勤
//                    if (attendanceOrgMonitor.getScanTimes().equals((byte) 0) || hasInput) {
//                        //设置考勤开始前无需考勤
//                        if (attendanceOrgMonitor.getScanTimes().equals((byte) 0) && NewDateUtils.changeMonth(systemTime, 0).getTime() != systemTime.getTime()) {
//                            List<UserDto> userDtoList = getUser(systemTime, oid, getDeptIds(systemTime, oid));  //考勤第一天需考勤的人员
//                            for (UserDto userDto : userDtoList) {
//                                User user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
//                                personnelAttendanceMonthlyService.SetNewUserNoNeed(user, NewDateUtils.yesterday(systemTime), null);//设置无需考勤
//                            }
//                        }
//                        for (; start.getTime() <= end && start.getTime() <= startWorkday.getTime(); start = NewDateUtils.changeDay(start, 1)) {
//                            setPersonelAttendanceNoNeed(oid, start);
//                            uids.clear();
//                            List<UserDto> userDtoList = this.getUser(start, oid, getDeptIds(start, oid));  //需考勤的人员
//                            if (start.getTime() == startWorkday.getTime()) {//工作日
//                                if (hasInput || attendanceOrgMonitor.getScanDate() != null || attendanceOrgMonitor.getScanTimes().equals((byte) 0)) {//第一个工作日考勤录入时间后第一次跑，或者当天第一次跑(分跑过假或者第一天就是工作日两种情况)
//                                    for (UserDto userDto : userDtoList) {
//                                        uids.add(userDto.getUserID());
//                                        Integer deptId = NumberUtils.toInt(userDto.getDepartment(), 0);
//                                        map = this.returnTime(oid, deptId, startWorkday, map);  //获取此时此部门所在的考勤规则的三种时间
//                                        workBegin = (String) map.get("beginTime");   //开始上班时间  时分
//                                        workEnd = (String) map.get("endTime");  //下班时间   时分
//                                        breakBegin = (String) map.get("breakBegin");
//                                        breakEnd = (String) map.get("breakEnd");
//                                        Integer source = (Integer) map.get("source"); //来源:1-手工录入(默认),2-打卡
//                                        personnelAttendanceUser = getOrAddNewPersonnelAttendanceUser(oid, deptId, userDto.getUserID(), startWorkday, workBegin, workEnd, breakBegin, breakEnd, source);
//                                        if (hasInput && "0".equals(personnelAttendanceUser.getBeginState())) {
//                                            isNewAbsent = this.countPersonnelAttendanceUserDetailBykg(null, personnelAttendanceUser.getUser(), personnelAttendanceUser.getAttendanceDate(), "3").equals(0L);  //取系统默认的旷工
//                                            clockiAbsent(personnelAttendanceUser, inputDate, startWorkday, isNewAbsent);
//                                        }
//                                    }
//                                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
//                                    if (hasInput) {
//                                        attendanceOrgMonitor.setScanDate(startWorkday);
//                                        attendanceOrgMonitor.setStartDate(startWorkday);
//                                        attendanceOrgMonitor.setScanTimes((byte) 2);
//                                    } else {
//                                        attendanceOrgMonitor.setScanTimes((byte) 1);
//                                        return;//没到第一天的考勤录入时间，不需要再执行。
//                                    }
//                                }
//                            } else { //考勤开始第一天是假
//                                scanWeekend(userDtoList, oid, start, uids);
//                                personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
//                                attendanceOrgMonitor.setScanDate(start);
//                                attendanceOrgMonitor.setScanTimes((byte) 2);
//                            }
//                        }
//                    }
//                }
//                if (attendanceOrgMonitor.getScanDate() == null || attendanceOrgMonitor.getStartDate() == null) {//考勤PersonnelAttendanceOrgMonitor未跑出完整数据，下面不再跑。
//                    return;
//                }
//            }
//            addAttendanceException(oid, attendanceOrgMonitor.getStartDate());//判断并添加作息时间
//            if (attendanceOrgMonitor.getExceptionDate().getTime() < end) {
//                if (attendanceOrgMonitor.getExceptionDate().getTime() > NewDateUtils.getMinDate().getTime()) {
//                    start = NewDateUtils.today(attendanceOrgMonitor.getExceptionDate());
//                } else {
//                    start = attendanceOrgMonitor.getScanDate();
//                }
//                Date nextBeginTime = NewDateUtils.changeMonth(start, 1);  //获取下个月第一天
//                Date nextEndTime = NewDateUtils.getLastTimeOfMonth(nextBeginTime);//获取下个月最后一天
//                addPersonnelAttendanceException(oid, nextBeginTime, nextEndTime);
//                attendanceOrgMonitor.setExceptionDate(nextEndTime);
//            }
//            //start 扫描时间， startWorkday 当前扫描的工作日， nextWorkday 下一个工作日
//            startWorkday = attendanceOrgMonitor.getStartDate();
//            Date nextWorkday = getNextDateByWork(startWorkday, oid);
//            inputDate = getInputTime(oid, NewDateUtils.today(startWorkday));
//            Date nextInputDate = getInputTime(oid, NewDateUtils.today(nextWorkday));
//            if (System.currentTimeMillis() < nextInputDate.getTime()) { //还没到下一个工作日的考勤录入时间，无需处理上个工作日的下班考勤
//                start = NewDateUtils.tomorrow(attendanceOrgMonitor.getScanDate());
//                hasInput = false;
//            } else {
//                start = startWorkday;
//                hasInput = true;
//            }
//            Date scanDate = attendanceOrgMonitor.getScanDate();
//            for (; start.getTime() < end; start = NewDateUtils.changeDay(start, 1)) {   //遍历两个时间之间的时间
//                setPersonelAttendanceNoNeed(oid, start);
//                uids.clear();
//                List<UserDto> userDtoList = this.getUser(start, oid, getDeptIds(start, oid));  //某天需考勤的人员
//                if (start.getTime() == nextWorkday.getTime()) {//更新今明两个工作日
//                    startWorkday = nextWorkday;
//                    nextWorkday = getNextDateByWork(startWorkday, oid);
//                    inputDate = nextInputDate;
//                    nextInputDate = getInputTime(oid, NewDateUtils.today(nextWorkday));
//                    if (System.currentTimeMillis() < nextInputDate.getTime()) { //还没到下一个工作日的考勤录入时间，无需处理上个工作日的下班考勤
//                        hasInput = false;
//                    } else {
//                        hasInput = true;
//                    }
//                }
//
//                if (start.getTime() == startWorkday.getTime()) {    //如果今天是班，则需考勤，进行考勤录入
//                    for (UserDto userDto : userDtoList) {
//                        uids.add(userDto.getUserID());
//                        Integer deptId = NumberUtils.toInt(userDto.getDepartment(), 0);
//                        map = this.returnTime(oid, deptId, start, map);  //获取此时此部门所在的考勤规则的三种时间
//                        workBegin = (String) map.get("beginTime");   //开始上班时间  时分
//                        workEnd = (String) map.get("endTime");  //下班时间   时分
//                        breakBegin = (String) map.get("breakBegin");   //午休开始时间(上午下班时间)
//                        breakEnd = (String) map.get("breakEnd");  //午休结束时间(下午上班时间)
//                        Integer source = (Integer) map.get("source"); //来源:1-手工录入(默认),2-打卡
//                        personnelAttendanceUser = getOrAddNewPersonnelAttendanceUser(oid, deptId, userDto.getUserID(), startWorkday, workBegin, workEnd, breakBegin, breakEnd, source);
//
//                        isNewAbsent = this.countPersonnelAttendanceUserDetailBykg(null, personnelAttendanceUser.getUser(), personnelAttendanceUser.getAttendanceDate(), "3").equals(0L);  //取系统默认的旷工
//                        if ("0".equals(personnelAttendanceUser.getBeginState())) {
//                            isNewAbsent = clockiAbsent(personnelAttendanceUser, inputDate, startWorkday, isNewAbsent);
//                        }
//
//                        if (hasInput && "0".equals(personnelAttendanceUser.getEndState())) {
//                            clockoutAbsent(personnelAttendanceUser, nextInputDate, startWorkday, isNewAbsent);
//                        }
//                    }
//                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
//                    attendanceOrgMonitor.setStartDate(startWorkday);
//                } else if (start.getTime() > scanDate.getTime()) {    //如果今天是假，则查看有无加班,和前一天的考勤是否已录入，已经扫描过的可以跳过
//                    scanWeekend(userDtoList, oid, start, uids);
//                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
//                }
//            }
//            if (start.getTime() == end && (attendanceOrgMonitor.getScanDate().getTime() < end //今天第一次跑
//                    || attendanceOrgMonitor.getScanTimes() < 2 && inputDate.getTime() < System.currentTimeMillis())) {  //上次跑在考勤录入时间前，现在已经过了考勤录入时间
//                setPersonelAttendanceNoNeed(oid, start);
//                List<UserDto> userDtoList = this.getUser(start, oid, getDeptIds(start, oid));  //今天需考勤的人员
//                uids.clear();
//                if (start.getTime() == nextWorkday.getTime()) {//更新今明两个工作日，考勤第一天有用
//                    startWorkday = nextWorkday;
//                    inputDate = nextInputDate;
//                    if (System.currentTimeMillis() < inputDate.getTime()) { //还没到下一个工作日的考勤录入时间，无需处理上个工作日的下班考勤
//                        hasInput = false;
//                    } else {
//                        hasInput = true;
//                    }
//                }
//
//                if (start.getTime() == startWorkday.getTime()) {//今天是工作日
//                    for (UserDto userDto : userDtoList) {
//                        Integer deptId = NumberUtils.toInt(userDto.getDepartment(), 0);
//                        map = this.returnTime(oid, deptId, start, map);  //获取此时此部门所在的考勤规则的三种时间
//                        workBegin = (String) map.get("beginTime");   //开始上班时间  时分
//                        workEnd = (String) map.get("endTime");  //下班时间   时分
//                        breakBegin = (String) map.get("breakBegin");   //午休开始时间(上午下班时间)
//                        breakEnd = (String) map.get("breakEnd");  //午休结束时间(下午上班时间)
//                        Integer source = (Integer) map.get("source"); //来源:1-手工录入(默认),2-打卡
//
//                        uids.add(userDto.getUserID());
//                        personnelAttendanceUser = getOrAddNewPersonnelAttendanceUser(oid, deptId, userDto.getUserID(), startWorkday, workBegin, workEnd, breakBegin, breakEnd, source);
//                        if (hasInput && "0".equals(personnelAttendanceUser.getBeginState())) {
//                            isNewAbsent = this.countPersonnelAttendanceUserDetailBykg(null, personnelAttendanceUser.getUser(), personnelAttendanceUser.getAttendanceDate(), "3").equals(0L);
//                            clockiAbsent(personnelAttendanceUser, inputDate, startWorkday, isNewAbsent);
//                        }
//                    }
//                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
//                    if (hasInput) {
//                        attendanceOrgMonitor.setScanTimes((byte) 2);//当前时间<考勤录入时间?1:2
//                        attendanceOrgMonitor.setStartDate(startWorkday);
//                    } else {
//                        attendanceOrgMonitor.setScanTimes((byte) 1);//当前时间<考勤录入时间?1:2
//                    }
//                } else { //今天是假
//                    scanWeekend(userDtoList, oid, start, uids);
//                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
//                    attendanceOrgMonitor.setScanTimes((byte) 2);
//                }
//                attendanceOrgMonitor.setScanDate(start);
//            }
//        }
//    }

    //1-系统默认考勤中用
    private final boolean clockoutAbsent(PersonnelAttendanceUser personnelAttendanceUser, Date inputDate, Date day, Boolean isNewAbsent) {
        User user = userService.getUserByID(personnelAttendanceUser.getUser());
        //下班考勤未录入
        personnelAttendanceUser.setEndState("7");
        personnelAttendanceUser.setPmAttendance(inputDate);
        personnelAttendanceUser.setUpdateDate(inputDate);
        if ("0".equals(personnelAttendanceUser.getType()) || "1".equals(personnelAttendanceUser.getType()) || "7".equals(personnelAttendanceUser.getType()) || "8".equals(personnelAttendanceUser.getType())) {
            personnelAttendanceUser.setType((byte)7);
        } else {
            personnelAttendanceUser.setType((byte)9);
        }

        if (isNewAbsent) {
            workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", personnelAttendanceUser, null, null, null, null, inputDate);
            Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user, day);  //旷工次数
            personnelAttendanceMonthlyService.SetAbsenteeisme(user, day, (byte) (absenteeismeNum + 1));
        }
        return false;
    }

    //是否需要添加作息时间
    private void addAttendanceException(Integer oid, Date start) {
        Date now = new Date(System.currentTimeMillis());
        Map<String, Object> map = new HashMap<>();
        String hql = "select max(exceptionDate) from PersonnelAttendanceException where org=:oid";
        map.put("oid", oid);
        Date prvDate = (Date) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, map);
        Date day;
        if (prvDate == null) {
            day = NewDateUtils.today(start);
        } else {
            day = NewDateUtils.tomorrow(prvDate);//最新考勤作息时间的0时0分0秒000
        }
        Date lastDay = NewDateUtils.today(NewDateUtils.changeMonth(now, 1));   //下个月的1日0时0分0秒000
        while (day.getTime() < lastDay.getTime()) {
            addPersonnelAttendanceException(oid, day, null, null);
            day = NewDateUtils.tomorrow(day);
        }
    }

    public Map<String, Object> returnTime(Integer oid, Integer deptId, Date openDate, Map<String, Object> map) {
        Map<String, Object> result = map;
        PersonnelAttendanceConfig personnelAttendanceConfigs = getPersonnelAttendanceConfigByDept(oid, deptId, openDate);
        if (personnelAttendanceConfigs != null) {
            WorkAttendanceService.ConfigAttendancePattern attendancePattern = WorkAttendanceService.ConfigAttendancePattern.getByIndex(personnelAttendanceConfigs.getAttendancePattern()); //考勤模式:1-考勤宝,2-手工录入(默认)//考勤人员的来源:1-手工录入(默认),2-打卡
            Date date1 = personnelAttendanceConfigs.getBeginTime();   //上班时间
            Date date2 = personnelAttendanceConfigs.getEndTime();   //下班时间
            Date date3 = personnelAttendanceConfigs.getBreakBegin();   //午休开始时间(上午下班时间)
            Date date4 = personnelAttendanceConfigs.getBreakEnd();   //午休结束时间(下午上班时间)
            result.put("beginTime", NewDateUtils.dateToString(date1, "HH:mm"));  //上班时间
            result.put("endTime", NewDateUtils.dateToString(date2, "HH:mm")); //下班时间
            result.put("breakBegin", NewDateUtils.dateToString(date3, "HH:mm"));  //午休开始时间(上午下班时间)
            result.put("breakEnd", NewDateUtils.dateToString(date4, "HH:mm")); //午休结束时间(下午上班时间)
            result.put("attendancePattern", attendancePattern.getIndex()); //考勤模式:1-考勤宝,2-手工录入
            result.put("source", attendancePattern.getSource()); //来源:1-手工录入(默认),2-打卡
        }
        return result;
    }

    @Override
    public Long countPersonnelAttendanceUserDetailBykg(Integer attendanceId, Integer userId, Date begin, String source) {
        StringBuffer hql = new StringBuffer("select count(*) from PersonnelAttendanceUserDetail where type=7");   //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
        HashMap<String, Object> params = new HashMap<>();
        if (begin != null) {
            hql.append(" and attendanceDate = :attendanceDate");
            params.put("attendanceDate", begin);
        }
        if (!MyStrings.nulltoempty(source).isEmpty()) {
            if ("4".equals(source)) {
                hql.append(" and source<3 ");
            } else {
                hql.append(" and source=:source");
                params.put("source", source);
            }
        }
        if (userId != null) {
            hql.append(" and user=:user");
            params.put("user", userId);
        }
        if (attendanceId != null) {
            hql.append(" and personnelAttendanceUser in (select id from PersonnelAttendanceUser where id =:attendanceId)");
            params.put("attendanceId", attendanceId);
        }
        return (Long) personnelAttendanceUserDetailDao.getByHQLWithNamedParams(hql.toString(), params);
    }

    @Override
    public PersonnelAttendanceUser getPersonnelAttendanceUserByUserIdAndId(Integer id, Integer userId, Date time, String endState) {
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceUser");
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer where = new StringBuffer();
        if (id != null) {
            where.append(" and id = :id");
            params.put("id", id);
        }
        if (userId != null) {
            where.append(" and user = :user");
            params.put("user", userId);
        }
        if (!MyStrings.nulltoempty(endState).isEmpty()) {    //上班状态:0-未考勤,1-已考勤，2-请假
            where.append(" and endState = :endState");
            params.put("endState", endState);
        }
        if (time != null) {
            where.append(" and attendanceDate = :attendanceDate");
            params.put("attendanceDate", NewDateUtils.today(time));
        }
        if (!where.isEmpty()) {
            hql.append(" where ").append(where.substring(4));
        }
        return (PersonnelAttendanceUser) personnelAttendanceUserDao.getByHQLWithNamedParams(hql.toString(), params);
    }

    private final void scanWeekend(List<UserDto> userDtoList, Integer oid, Date start, List<Integer> uids) {
        String workBegin, workEnd,breakBegin,breakEnd;
        for (UserDto userDto : userDtoList) {
//            System.out.println("scanWeekend userDtoList oid:"+oid+" count:"+userDtoList.size());
            if (uids != null) {
                uids.add(userDto.getUserID());
            }
            User user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
            Integer deptId;
            try {
                deptId = Integer.valueOf(userDto.getDepartment());
            } catch (NumberFormatException e) {
                deptId = Integer.valueOf(0);
            }
            HashMap<String, Object> map = new HashMap<>();
            personnelAttendanceMonthlyService.SetNoNeed(user, start, true);
            //无需考勤日系统里的审批完成的加班
            this.returnTime(oid, deptId, start, map);  //获取此时此部门所在的考勤规则的三种时间
            workBegin = (String) map.get("beginTime");   //开始上班时间  时分
            workEnd = (String) map.get("endTime");  //下班时间   时分
            breakBegin = (String) map.get("breakBegin");   //午休开始时间(上午下班时间)
            breakEnd = (String) map.get("breakEnd");  //午休结束时间(下午上班时间)
            Integer source = (Integer) map.get("source"); //来源:1-手工录入,2-打卡
            Date workBegin1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), workBegin);
            Date workEnd1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), workEnd);
            Date breakBegin1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), breakBegin);
            Date breakEnd1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), breakEnd);
            this.defaultOverTime(null, user, deptId, start, 1, workBegin1, workEnd1,breakBegin1,breakEnd1,source);
        }
    }

    //系统里默认将添加添加到考勤中   type 1-无需考勤 2-正常考勤   attendanceUserId(要考勤的人员)  workBegin(上班开始时间)  workEnd(上班结束时间) deptId(要考勤人员部门id)
    //  generalUser(操作考勤的人员，此默认总务)
    public void defaultOverTime(PersonnelAttendanceUser personnelAttendanceUser, User attendanceUser, Integer deptId, Date start1, Integer type, Date workBegin, Date workEnd,Date breakBegin, Date breakEnd,Integer source) {
        List<PersonnelOvertime> personnelOvertimes = overtimeService.getPersonnelOvertimeListByTime(attendanceUser.getUserID(), 4, NewDateUtils.today(start1), NewDateUtils.getLastTimeOfDay(start1));
        if (!personnelOvertimes.isEmpty()) {
            if (type == 1) {
                personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, attendanceUser.getUserID(), start1, null);
                if (personnelAttendanceUser == null) {
                    personnelAttendanceUser.setOrg(attendanceUser.getOid());
                    personnelAttendanceUser.setDept(deptId);
                    personnelAttendanceUser.setBeginState("8");
                    personnelAttendanceUser = this.addPersonnelAttendanceUser1(attendanceUser.getOid(), deptId, null, "8", "8", "8", attendanceUser.getUserID(), start1, workBegin, workEnd,breakBegin,breakEnd,source);
                }
            }
            Double overDuration = 0.0;
            Integer workOverNum = 0;  //此次计入考勤加班的次数

            for (PersonnelOvertime p : personnelOvertimes) {
                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailByBusiness(null, "8", null, p.getId(), null);
                if (personnelAttendanceUserDetails.size() <= 0) {
                    this.addPersonnelAttendanceUserDetail(p.getActualBeginTime(), p.getActualEndTime(), "8", "1", p.getType(),null, p.getId(), p.getActualReason(), personnelAttendanceUser, null, String.valueOf(p.getApproveDuration()), null, null, new Date());
                }else {
                    PersonnelAttendanceUserDetail personnelAttendanceUserDetail = personnelAttendanceUserDetails.get(0);
                    personnelAttendanceUserDetail.setBeginTime(p.getActualBeginTime());
                    personnelAttendanceUserDetail.setEndTime(p.getActualEndTime());
                    personnelAttendanceUserDetail.setDuration(p.getApproveDuration());
                    personnelAttendanceUserDetailDao.update(personnelAttendanceUserDetail);  //可能是打卡的已经生产了详情，这次将实际的加班时间和时长更新下。
                }
                overDuration = overDuration + p.getApproveDuration();
                workOverNum += 1;
            }
            Pair<Byte, Float> overResult = personnelAttendanceMonthlyService.GetOvertime(attendanceUser, start1);
            personnelAttendanceMonthlyService.SetOvertime(attendanceUser, start1, (byte) (overResult.getLeft() + workOverNum), Float.valueOf(overResult.getRight() + overDuration.floatValue()));
        }

        //计划加班申报的，添加默认打卡
        if (2==type){  //无需考勤的没有默认打卡，只有正常考勤有
            List<PersonnelOvertime> personnelOvertimes1 = overtimeService.getPersonnelOvertimeListByBeginTime(attendanceUser.getUserID(), 2, NewDateUtils.today(personnelAttendanceUser.getAttendanceDate()), NewDateUtils.getLastTimeOfDay(personnelAttendanceUser.getAttendanceDate()), "0", null, null);
            for (PersonnelOvertime p1:personnelOvertimes1) {
                this.addDefaultClockRecord(p1,1,personnelAttendanceUser,null);
            }

        }
    }

    @Override   //user(考勤录入操作人)   userId(被考勤职工id)
    public PersonnelAttendanceUser addPersonnelAttendanceUser1(Integer oid, Integer deptId, User user, String beginState, String endState, String type, Integer userId, Date openDate, Date workBegin, Date workEnd,Date breakBegin, Date breakEnd,Integer source) {
        PersonnelAttendanceUser personnelAttendanceUser = new PersonnelAttendanceUser();
        if (user != null) {
            personnelAttendanceUser.setCreator(user.getUserID());
            personnelAttendanceUser.setCreateName(user.getUserName());
            personnelAttendanceUser.setUpdator(user.getUserID());
            personnelAttendanceUser.setUpdateName(user.getUserName());
        } else {
            personnelAttendanceUser.setCreateName("系统");
            personnelAttendanceUser.setUpdateName("系统");
        }
        personnelAttendanceUser.setCreateDate(new Date());
        personnelAttendanceUser.setUser(userId);
        personnelAttendanceUser.setAttendanceDate(openDate);  //考勤日期
        personnelAttendanceUser.setOrg(oid);
        personnelAttendanceUser.setBeginTime(workBegin);  //上班时间
        personnelAttendanceUser.setEndTime(workEnd);   //下班时间
        personnelAttendanceUser.setBreakBegin(breakBegin);  //午休开始时间(上午下班时间)
        personnelAttendanceUser.setBreakEnd(breakEnd);   //午休结束时间(下午上班时间)
        personnelAttendanceUser.setEndState(endState);
        personnelAttendanceUser.setBeginState(beginState);  //上班状态:0-未考勤,1-已考勤，2-请假, 7-旷工(系统默认的旷工)
        personnelAttendanceUser.setType(Byte.valueOf(type));  //0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
        personnelAttendanceUser.setDept(deptId);
        personnelAttendanceUser.setSource(source);  //来源:1-手工录入(默认),2-打卡
        personnelAttendanceUserDao.save(personnelAttendanceUser);
        return personnelAttendanceUser;
    }

    @Override
    public PersonnelAttendanceUserDetail addPersonnelAttendanceUserDetail(Date beginDate, Date endDate, String type, String source, String businessType,Integer leaveType, Integer business, String reason, PersonnelAttendanceUser p, User user, String overDuration, String overMemo, String state, Date createDate) {
        PersonnelAttendanceUserDetail personnelAttendanceUserDetail = new PersonnelAttendanceUserDetail();
        personnelAttendanceUserDetail.setType(type);//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班
        if (beginDate != null) {
            personnelAttendanceUserDetail.setBeginTime(beginDate);
        }
        if (endDate != null) {
            personnelAttendanceUserDetail.setEndTime(endDate);
        }
        if (!MyStrings.nulltoempty(overDuration).isEmpty()) {
            personnelAttendanceUserDetail.setDuration(Double.valueOf(overDuration));
        } else if (beginDate != null && endDate != null) {
            personnelAttendanceUserDetail.setDuration((double) TimeUnit.MILLISECONDS.toHours(endDate.getTime() - beginDate.getTime()));
        }
        personnelAttendanceUserDetail.setMemo(overMemo);
        personnelAttendanceUserDetail.setAttendanceId(p.getId());
//        personnelAttendanceUserDetail.setPersonnelAttendanceUser(p);//考勤
        personnelAttendanceUserDetail.setSource(source);//来源:1-审批,2-录入
        if (!MyStrings.nulltoempty(businessType).isEmpty()) {
            personnelAttendanceUserDetail.setBusinessType(businessType);
        }
        personnelAttendanceUserDetail.setLeaveType(leaveType);
        personnelAttendanceUserDetail.setBusiness(business);
        personnelAttendanceUserDetail.setReason(reason);
        personnelAttendanceUserDetail.setUser(p.getUser());
        personnelAttendanceUserDetail.setState(state);
        personnelAttendanceUserDetail.setAttendanceDate(p.getAttendanceDate());//考勤日期
        if (user != null) {
            personnelAttendanceUserDetail.setCreator(user.getUserID());
            personnelAttendanceUserDetail.setCreateName(user.getUserName());
        } else {
//            personnelAttendanceUserDetail.setCreator(0);
            personnelAttendanceUserDetail.setCreateName("系统");
        }
        personnelAttendanceUserDetail.setOrg(p.getOrg());
        personnelAttendanceUserDetailDao.save(personnelAttendanceUserDetail);
        return personnelAttendanceUserDetail;
    }

    @Override
    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByBusiness(Integer attendanceId, String type, String source, Integer business, String state) {
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceUserDetail");
        List<String> where = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        if (attendanceId != null) {
            where.add("attendanceId=:attendanceId");
            params.put("attendanceId", attendanceId);
        }
        if (!StringUtils.isBlank(type)) {
            if ("10".equals(type)){  //除了正常和加班的，加班的另外算
                where.add("type not in (:type)");
                params.put("type", Arrays.asList("1","8"));
            }else {
                where.add("type=:type");
                params.put("type", type);
            }
        }
        if (!StringUtils.isBlank(source)) {
            if ("4".equals(source)) {   //系统默认旷工的排除
                where.add("source!=:source");
                params.put("source", "3");
            } else {
                where.add("source=:source");
                params.put("source", source);
            }
        }
        if (business != null) {
            where.add("business=:business");
            params.put("business", business);
        }
        if (!StringUtils.isBlank(state)) {
            where.add("state!=:state");
            params.put("state", state);
        }
        if(!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        } else {
            logger.warn("getPersonnelAttendanceUserDetailByBusiness params is empty, 查询参数为空！");
        }
        return personnelAttendanceUserDetailDao.getListByHQLWithNamedParams(hql.toString(), params);
    }

    //1-系统默认考勤中用
    private final boolean clockiAbsent(PersonnelAttendanceUser personnelAttendanceUser, Date inputDate, Date day, Boolean isNewAbsent) {
        User user = userService.getUserByID(personnelAttendanceUser.getUser());
        //上班考勤未录入
        personnelAttendanceUser.setBeginState("7");
        personnelAttendanceUser.setAmAttendance(inputDate);
        personnelAttendanceUser.setUpdateDate(inputDate);
        if ("0".equals(personnelAttendanceUser.getType()) || "1".equals(personnelAttendanceUser.getType()) || "7".equals(personnelAttendanceUser.getType()) || "8".equals(personnelAttendanceUser.getType())) {
            personnelAttendanceUser.setType((byte)7);
        } else {
            personnelAttendanceUser.setType((byte)9);
        }

        if (isNewAbsent) {
            workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", personnelAttendanceUser, null, null, null, null, inputDate);
            Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user, day);  //旷工次数
            personnelAttendanceMonthlyService.SetAbsenteeisme(user, day, (byte) (absenteeismeNum + 1));
        }
        return false;
    }

    @Override  //默认添加加班的打卡 overUserId:加班人员id   type：1-考勤定时任务运行时的 2-加班申请审批时的
    public void addDefaultClockRecord(PersonnelOvertime outTime,Integer type,PersonnelAttendanceUser personnelAttendanceUser,Integer detailId) {
        User overUser = userService.getUserByID(outTime.getUser_());
        Integer dept = 0;  //部门其他的
        if (StringUtils.isNotEmpty(overUser.getDepartment())) {
            dept = Integer.parseInt(overUser.getDepartment());
        }
        Date newDate = new Date();
        Byte detailPunchType = WorkAttendanceOldService.DetailPunchType.begin.getIndex();  //加班的   类型:1-开始,2-结束
        Byte punchType = WorkAttendanceOldService.PunchType.beforeOvertime.getIndex(); //加班打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
        Byte punchType1 = WorkAttendanceOldService.PunchType.offWork.getIndex(); //上下班打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
        Byte detailPunchType1 = WorkAttendanceOldService.DetailPunchType.end.getIndex();  //上下班的  类型:1-开始,2-结束
        PersonnelAttendanceConfig personnelAttendanceConfig = getPersonnelAttendanceConfigByDept(overUser.getOid(),dept,outTime.getBeginTime());  //获取加班人员那天的考勤规则
        if (personnelAttendanceConfig!=null&&personnelAttendanceConfig.getAttendancePattern().equals(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex())) {  //考勤宝的
            PersonnelAttendanceException personnelAttendanceException = this.getPersonnelAttendanceExceptionByExceptionDate(overUser.getOid(),NewDateUtils.today(outTime.getBeginTime()));
            if (personnelAttendanceException!=null&&"2".equals(personnelAttendanceException.getType())){  //上班日需要添加默认加班，休息日不需要添加
                if (personnelAttendanceUser==null){
                    personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, overUser.getUserID(), NewDateUtils.today(outTime.getBeginTime()), null); //查询某人某天的考勤记录
                }
                if (personnelAttendanceUser!=null&&personnelAttendanceUser.getId()!=null){
                    Integer colckState = 1 ; //1-不用默认打卡  2-默认打卡
                    if (personnelAttendanceUser.getBeginTime().getTime()==outTime.getEndTime().getTime()){ //上班时间和加班结束时间一样得
                        newDate = outTime.getEndTime();
                        colckState = 2;
                        detailPunchType = WorkAttendanceOldService.DetailPunchType.end.getIndex();
                        punchType = WorkAttendanceOldService.PunchType.afterOvertime.getIndex();
                        punchType1 = WorkAttendanceOldService.PunchType.goToWork.getIndex();
                        detailPunchType1 = WorkAttendanceOldService.DetailPunchType.begin.getIndex();
                    }else if (personnelAttendanceUser.getEndTime().getTime()==outTime.getBeginTime().getTime()){  //下班时间和加班开始时间一样得
                        newDate = outTime.getBeginTime();
                        colckState =2;
                    }
                    if (2==colckState) {
                        if (2==type) {
                            //默认加班的打卡
                            PersonnelAttendanceUserDetail personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "8", outTime.getId(), null);
                            if (personnelAttendanceUserDetail == null) {
                                personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(outTime.getBeginTime(), outTime.getEndTime(), "8", "1", outTime.getType(), null, outTime.getId(), outTime.getReason(), personnelAttendanceUser, overUser, String.valueOf(outTime.getDuration()), null, null, newDate); //加班
                            }
                            OverPunchRecord overPunchRecord = new OverPunchRecord(overUser.getUserID(), newDate, punchType, detailPunchType, personnelAttendanceUserDetail.getId(), outTime.getId(), WorkAttendanceOldService.AttendanceType.overtime.getIndex());
                            clusterMessageSendingOperations.delayCall(newDate, overPunchRecord);

                            //默认上下班的打卡
                            PersonnelAttendanceUserDetail personnelAttendanceUserDetail1 = workAttendanceService.getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "1", null, null);
                            if (personnelAttendanceUserDetail1 == null) {
                                personnelAttendanceUserDetail1 = workAttendanceService.addPersonnelAttendanceUserDetail(personnelAttendanceUser.getBeginTime(), personnelAttendanceUser.getEndTime(), "1", "1", null, null, null, null, personnelAttendanceUser, overUser, null, null, "1", new Date()); //正常上班
                            }
                            OverPunchRecord overPunchRecord1 = new OverPunchRecord(overUser.getUserID(), newDate, punchType1, detailPunchType1, personnelAttendanceUserDetail1.getId(), null, WorkAttendanceOldService.AttendanceType.normal.getIndex());
                            clusterMessageSendingOperations.delayCall(newDate, overPunchRecord1);

                        }else if (1==type){
                            //默认加班打卡的
                            if (detailId==null) {
                                PersonnelAttendanceUserDetail personnelAttendanceUserDetail = getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "8", outTime.getId(), null);
                                if (personnelAttendanceUserDetail == null) {
                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(outTime.getBeginTime(), outTime.getEndTime(), "8", "1", outTime.getType(), null, outTime.getId(), outTime.getReason(), personnelAttendanceUser, overUser, String.valueOf(outTime.getDuration()), null, null, newDate); //加班
                                }
                                detailId = personnelAttendanceUserDetail.getId();
                            }
                            this.overPunchTurn(overUser.getUserID(), newDate, punchType, detailPunchType, detailId, outTime.getId(), WorkAttendanceOldService.AttendanceType.overtime.getIndex());

                            //默认山下班打卡的
                            PersonnelAttendanceUserDetail personnelAttendanceUserDetail1 = getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "1", null, null);
                            if (personnelAttendanceUserDetail1 == null) {
                                personnelAttendanceUserDetail1 = workAttendanceService.addPersonnelAttendanceUserDetail(personnelAttendanceUser.getBeginTime(), personnelAttendanceUser.getEndTime(), "1", "1", null, null, null, null, personnelAttendanceUser, overUser, null, null, "1", new Date()); //正常上班
                            }
                            this.overPunchTurn(overUser.getUserID(), newDate, punchType1, detailPunchType1, personnelAttendanceUserDetail1.getId(), null, WorkAttendanceOldService.AttendanceType.normal.getIndex());
                        }

                        if ("0".equals(personnelAttendanceUser.getType())){  //考勤的总状态是未考勤的，则变为正常，其他的状态的则不用改。
                            personnelAttendanceUser.setType((byte)1);  //总状态
                            if (punchType1.equals(WorkAttendanceOldService.PunchType.goToWork.getIndex())){    //上班的状态
                                personnelAttendanceUser.setBeginState("1");
                                personnelAttendanceUser.setAmAttendance(newDate);
                            }else if (punchType1.equals(WorkAttendanceOldService.PunchType.offWork.getIndex())){  //下班的
                                if ("0".equals(personnelAttendanceUser.getEndState())){  //下班的状态，如果是未考勤的那么默认的是正常1，如果已经有其他状态的，则不用更改
                                    personnelAttendanceUser.setEndState("1");
                                    personnelAttendanceUser.setPmAttendance(newDate);
                                }
                            }
//                            personnelAttendanceUser.setUpdator(user.getUserID());
//                            personnelAttendanceUser.setUpdateName(user.getUserName());
                            personnelAttendanceUser.setUpdateDate(newDate);
                            personnelAttendanceUserDao.update(personnelAttendanceUser);
                        }else {   // 考勤总状态是其他，但是上班或下班还有未考勤的
                            if (punchType1.equals(WorkAttendanceOldService.PunchType.goToWork.getIndex())&&"0".equals(personnelAttendanceUser.getBeginState())){  //上班的状态，如果是未考勤的那么默认的是正常1，如果已经有其他状态的，则不用更改
                                personnelAttendanceUser.setBeginState("1");
                                personnelAttendanceUser.setAmAttendance(newDate);
                                personnelAttendanceUser.setUpdateDate(newDate);
                                personnelAttendanceUserDao.update(personnelAttendanceUser);
                            }else if (punchType1.equals(WorkAttendanceOldService.PunchType.offWork.getIndex())&&"0".equals(personnelAttendanceUser.getEndState())){  //下班的状态，如果是未考勤的那么默认的是正常1，如果已经有其他状态的，则不用更改
                                personnelAttendanceUser.setEndState("1");
                                personnelAttendanceUser.setPmAttendance(newDate);
                                personnelAttendanceUser.setUpdateDate(newDate);
                                personnelAttendanceUserDao.update(personnelAttendanceUser);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public PersonnelAttendanceUserDetail getPersonnelAttendanceUserDetail(Integer user,Date attendanceDate,String type,Integer business,String state){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PersonnelAttendanceUserDetail where user=:user";
        map.put("user",user);
        if (attendanceDate!=null){
            hql+=" and attendanceDate=:attendanceDate";
            map.put("attendanceDate",attendanceDate);
        }
        if (StringUtils.isNotEmpty(type)){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (business!=null){
            hql+=" and business=:business";
            map.put("business",business);
        }
        if (StringUtils.isNotEmpty(state)){
            hql+=" and state=:state";
            map.put("state",state);
        }
        PersonnelAttendanceUserDetail personnelAttendanceUserDetail = (PersonnelAttendanceUserDetail) personnelAttendanceUserDetailDao.getByHQLWithNamedParams(hql,map);
        return personnelAttendanceUserDetail;
    }

    @Override    //punchType 打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
    public void overPunchTurn(Integer overUserId, Date punchTime, Byte punchType, Byte detailPunchType, Integer detailId, Integer overtimeId,Byte type) {
        User user = userDao.get(overUserId);
        PersonnelAttendanceUserDetailPunch personnelAttendanceUserDetailPunch1 = getUserDetailPunch(user.getOid(),detailId,detailPunchType,punchTime,"系统");  //查默认添加记录的是否已添加
        if (personnelAttendanceUserDetailPunch1==null) {  //没添加的再加上
            //添加员工打卡信息
            PersonnelAttendancePunch personnelAttendancePunch = new PersonnelAttendancePunch();
            personnelAttendancePunch.setOrg(user.getOid());
            personnelAttendancePunch.setUser(user.getUserID());
            personnelAttendancePunch.setDept(StringUtils.isNotEmpty(user.getDepartment()) ? Integer.getInteger(user.getDepartment()) : null);
            personnelAttendancePunch.setPunchTime(punchTime);  //打卡时间
            personnelAttendancePunch.setPunchType(punchType);  //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
            personnelAttendancePunch.setType(type); //考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-其它
            personnelAttendancePunchDao.save(personnelAttendancePunch);

            //员工考勤明细打卡对照信息
            PersonnelAttendanceUserDetailPunch personnelAttendanceUserDetailPunch = new PersonnelAttendanceUserDetailPunch();
            personnelAttendanceUserDetailPunch.setOrg(user.getOid());
            personnelAttendanceUserDetailPunch.setUser(user.getUserID());
            personnelAttendanceUserDetailPunch.setDetail(detailId); //明细id
            personnelAttendanceUserDetailPunch.setPunch(personnelAttendancePunch.getId()); //打卡ID
            personnelAttendanceUserDetailPunch.setType(detailPunchType);//类型:1-开始,2-结束
            personnelAttendanceUserDetailPunch.setCreateName("系统");  //overUser.getUserName()
            personnelAttendanceUserDetailPunch.setCreateTime(punchTime);
            personnelAttendanceUserDetailPunchDao.save(personnelAttendanceUserDetailPunch);
        }
    }

    @Override
    public PersonnelAttendanceUserDetailPunch getUserDetailPunch(Integer org,Integer detailId, Byte detailPunchType, Date punchTime, String createName) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer(" from PersonnelAttendanceUserDetailPunch where org=:org");
        params.put("org", org);
        if (detailId != null) {
            hql.append(" and detail=:detail");
            params.put("detail", detailId);
        }
        if (detailPunchType != null) {
            hql.append(" and type=:type");
            params.put("type", detailPunchType);
        }
        if (detailId != null) {
            hql.append(" and createTime=:createTime");
            params.put("createTime", punchTime);
        }
        if (StringUtils.isNotEmpty(createName)) {
            hql.append(" and createName=:createName");
            params.put("createName", createName);
        }
        PersonnelAttendanceUserDetailPunch personnelAttendanceUserDetailPunch = (PersonnelAttendanceUserDetailPunch) personnelAttendanceUserDetailPunchDao.getByHQLWithNamedParams(hql.toString(),params);
        return personnelAttendanceUserDetailPunch;
    }

    @Override
    public PersonnelAttendanceException getPersonnelAttendanceExceptionByExceptionDate(Integer oid, Date exceptionDate) {
        Map<String, Object> params = new HashMap<>();
        String hql = " from PersonnelAttendanceException where org=:oid and exceptionDate=:exceptionDate";
        params.put("oid", oid);
        params.put("exceptionDate", exceptionDate);
        return (PersonnelAttendanceException) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, params);
    }

    public void initializeWorkAttendanceApply(int org) {
        User superUser = userService.getUserByRoleCode(org, "super");//董事长
        String hql = "from ApprovalItem where belongTo=" + org + " and code='workAttendanceApply'";
        List<ApprovalItem> approvalItemList = approvalItemDao.getListByHQL(hql);
        ApprovalItem approvalItem = approvalItemList.get(0);
        String hqlFlow = "from ApprovalFlow where item = " + approvalItem.getId();
        List<ApprovalFlow> approvalFlowList = approvalFlowDao.getListByHQL(hqlFlow);
        if (approvalItem.getStatus() == 0 && approvalFlowList.isEmpty()) {
            approvalItem.setStatus(1);
            approvalItem.setLevel(1);
            approvalItem.setCreateDate(new Date());
            approvalItem.setOpenDate(NewDateUtils.today(new Date()));
            approvalItem.setApproveStatus("2");
            approvalItem.setAuditDate(new Date());
            approvalItem.setUpperLimit(new BigDecimal(-1));
            approvalItemDao.update(approvalItem);

            ApprovalFlow kaoQinFlow = new ApprovalFlow();
            kaoQinFlow.setToUser("董事长");
            kaoQinFlow.setType(1);
            kaoQinFlow.setLevel(1);
            kaoQinFlow.setItem(approvalItem);
            kaoQinFlow.setLimitAmount(new BigDecimal(-1));
            kaoQinFlow.setAmountCeiling((double) -1);
            kaoQinFlow.setToUserId(superUser.getUserID());
            kaoQinFlow.setUserName(superUser.getUserName());
            approvalFlowDao.save(kaoQinFlow);
        }
    }

    /**
     * @param ttType   1-某天的考勤情况  2-某月的考勤情况
     * @param type     1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他（请假类型）
     * @param beginDay 考勤日
     * @param userId   职工id
     * @return
     */
    @Override
    public Map<String, Object> getAttendanceMonthOrDay(Integer ttType, String type, Date beginDay, Integer userId) {
        Map<String, Object> map = new HashMap<>();
        if (ttType != null && userId != null && beginDay != null) {
            User user = userService.getUserByID(userId);
            if (ttType == 1) {  //1-某天的考勤情况
                map = this.getAttendanceDay(beginDay, user);
            } else {  //2-某月的考勤情况
                map = this.getAttendanceMonth(beginDay, userId, type);
            }
            map.put("userName", user.getUserName());  //职工名称
        }
        return map;
    }

    private Map<String, Object> getAttendanceDay(Date beginDay, User user) {
        Map<String, Object> map = new HashMap<>();
        PersonnelAttendanceUser personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, user.getUserID(), beginDay, null);
        if (personnelAttendanceUser != null) {
            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailsNew = new ArrayList<>();
            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = getPersonnelAttendanceUserDetailByType(personnelAttendanceUser.getId(), beginDay, "4");
            for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetails) {
                if ("5".equals(pd.getType())){
                    pd.setLeaveTypeName(leaveService.getLeaveTypeName(pd.getLeaveType(),pd.getBusinessType()));
                    personnelAttendanceUserDetailsNew.add(pd);
                }else if ("8".equals(pd.getType())){
                    if (pd.getBusiness()!=null){
                        PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
                        if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
                            personnelAttendanceUserDetailsNew.add(pd);
                        }
                    }else {
                        personnelAttendanceUserDetailsNew.add(pd);
                    }
                }else {
                    personnelAttendanceUserDetailsNew.add(pd);
                }
            }
            map.put("personnelAttendanceUserDetail", personnelAttendanceUserDetailsNew);  //职工考勤明细列表
        }
        map.put("personnelAttendanceUser1", personnelAttendanceUser);  //职工考勤类型信息列表

        Integer deptId = 0;
        if (!"".equals(user.getDepartment()) && user.getDepartment() != null) {
            deptId = Integer.parseInt(user.getDepartment());
        }
        Date systemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());  //获取系统时间
        if (systemTime != null) {   //返回的每日考勤的开始时间、结束时间、每日考勤录入时间
            if (NewDateUtils.today(beginDay).getTime() >= systemTime.getTime()) {
                map = returnTime(user.getOid(), deptId, NewDateUtils.today(beginDay), map);
                String beginDate = (String) map.get("beginTime");
                if (MyStrings.nulltoempty(beginDate).isEmpty()) {
                    returnTime(user.getOid(), null, systemTime, map);
                }
            } else {
                returnTime(user.getOid(), deptId, systemTime, map);
            }
        }
        return map;
    }

    private Map<String, Object> getAttendanceMonth(Date beginDay, Integer userId, String type) {
        Map<String, Object> map = new HashMap<>();
        Date begin = NewDateUtils.changeMonth(beginDay, 0);  //获取某月第一天
        Date end = NewDateUtils.today();  //若当月则取昨天，当天的有统计不显示的话有问题
        Date nowMonth = NewDateUtils.changeMonth(new Date(), 0);   //当前月的第一天
        Date beginMonth = NewDateUtils.changeMonth(beginDay, 0);   //传值来的月的第一天
        if (nowMonth.compareTo(beginMonth) != 0) {
            end = NewDateUtils.getLastTimeOfMonth(beginDay);   //获取某月最后一天
        }
        if (!MyStrings.nulltoempty(type).isEmpty()) {
            if (!"1".equals(type)) {   //5-请假,7-旷工,8-加班
                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailsNew = new ArrayList<>();
                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = getPersonnelAttendanceUserDetailByMonth(userId, null, begin, end, type);
                for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetails) {
                    if ("5".equals(pd.getType())){
                        pd.setLeaveTypeName(leaveService.getLeaveTypeName(pd.getLeaveType(),pd.getBusinessType()));
                        personnelAttendanceUserDetailsNew.add(pd);
                    }else if ("8".equals(pd.getType())){
                        if (pd.getBusiness()!=null){
                            PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
                            if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
                                personnelAttendanceUserDetailsNew.add(pd);
                            }
                        }else {
                            personnelAttendanceUserDetailsNew.add(pd);
                        }
                    }else {
                        personnelAttendanceUserDetailsNew.add(pd);
                    }
                }
                map.put("listMap", personnelAttendanceUserDetailsNew);
            }
        }
        return map;
    }

    @Override
    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByType(Integer attendanceId, Date begin, String source) {

        String hql = " and (o.type=5 or o.type=7 or o.type=8 or o.type=9)";   //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
        if (attendanceId != null) {
            hql += " and o.attendanceId = " + attendanceId;
        }
        if (begin != null) {
            hql += " and TO_DAYS(o.attendanceDate) = TO_DAYS('" + new SimpleDateFormat("yyyy-MM-dd").format(begin) + "')";
        }
        if (!"".equals(source) && source != null) {
            if ("4".equals(source)) {
                hql += " and o.source!=3";   //不取系统默认的旷工，且没有进行下班录入的
            } else {
                hql += " and o.source=" + source;
            }
        }
        return personnelAttendanceUserDetailDao.findCollectionByConditionNoPage(hql, null, null);
    }

    @Override
    public List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigByOpenDate(Integer oid, Integer type, Date openDate) {
        Map<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceConfig where enabled = 1");
        if (oid != null) {
            hql.append(" and org=:oid");
            params.put("oid", oid);
        }
        if (type != null) {
            hql.append(" and type=:type");
            params.put("type", type.toString());
        }
        if (openDate != null) {
            hql.append(" and openDate<=:openDate");
            params.put("openDate", openDate);
        }
        hql.append(" order by id desc");
        return (List<PersonnelAttendanceConfig>) personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql.toString(), params);
    }

    @Override
    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByMonth(Integer userId, Integer attendanceId, Date begin, Date end, String type) {
        Map<String, Object> params = new HashMap<>();
        String hql = "from PersonnelAttendanceUserDetail o where o.user =:userId";
        params.put("userId", userId);  //userId不为空
        if (attendanceId != null) {
            hql += " and o.personnelAttendanceUser.id =:attendanceId";
            params.put("attendanceId", attendanceId);
        }
        if (begin != null) {
            hql += " and o.attendanceDate >=:beginDate";
            params.put("beginDate", begin);
        }
        if (end != null) {
            hql += " and o.attendanceDate <=:endDate";
            params.put("endDate", end);
        }
        if (!MyStrings.nulltoempty(type).isEmpty()) {
            hql += " and o.type =:type";
            params.put("type", type);
        }
        hql+=" order by o.attendanceDate";
        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = (List<PersonnelAttendanceUserDetail>) personnelAttendanceUserDetailDao.getListByHQLWithNamedParams(hql, params);
        return personnelAttendanceUserDetails;
    }

    @Override
    public void addPersonnelAttendanceIotTerminal(IotTerminal iotTerminal, IotTerminal iotTerminalOld, AuthInfoDto authInfo,Byte operation) {
        PersonnelAttendanceIotTerminal personnelAttendanceIotTerminalLast = this.getIotTerminalHistoryLast(iotTerminal.getId()); //上一次的修改
        if (personnelAttendanceIotTerminalLast == null) {
            PersonnelAttendanceIotTerminal personnelAttendanceIotTerminal = new PersonnelAttendanceIotTerminal();
            BeanUtils.copyPropertiesIgnoreNull(iotTerminalOld, personnelAttendanceIotTerminal);
            personnelAttendanceIotTerminal.setId(null);
            personnelAttendanceIotTerminal.setIotTerminal(iotTerminal.getId());
            personnelAttendanceIotTerminal.setOperation(IotService.TerminalOperation.turnOn.getIndex()); //一开始是开启的
            personnelAttendanceIotTerminal.setUpdateName(authInfo.getName());
            personnelAttendanceIotTerminal.setUpdator(authInfo.getUserID());
            personnelAttendanceIotTerminal.setVersionNo(0);
            personnelAttendanceIotTerminalDao.save(personnelAttendanceIotTerminal);

            personnelAttendanceIotTerminalLast = personnelAttendanceIotTerminal;
        }

        //添加最新的修改历史
        PersonnelAttendanceIotTerminal personnelAttendanceIotTerminal1 = new PersonnelAttendanceIotTerminal();
        BeanUtils.copyPropertiesIgnoreNull(iotTerminal, personnelAttendanceIotTerminal1);
        personnelAttendanceIotTerminal1.setId(null);
        personnelAttendanceIotTerminal1.setIotTerminal(iotTerminal.getId());
        personnelAttendanceIotTerminal1.setOperation(operation);
        personnelAttendanceIotTerminal1.setUpdateName(authInfo.getName());
        personnelAttendanceIotTerminal1.setUpdator(authInfo.getUserID());
        personnelAttendanceIotTerminal1.setPreviousId(personnelAttendanceIotTerminalLast.getId());
        personnelAttendanceIotTerminal1.setVersionNo(personnelAttendanceIotTerminalLast.getVersionNo() + 1);
        personnelAttendanceIotTerminalDao.save(personnelAttendanceIotTerminal1);
    }

    @Override
    public PersonnelAttendanceIotTerminal getIotTerminalHistoryLast(Integer iotTerminal) {
        StringBuilder hql = new StringBuilder("from PersonnelAttendanceIotTerminal where iotTerminal=:iotTerminal");
        Map<String, Object> params = new HashMap<>(){{
            put("iotTerminal",iotTerminal);
        }};
        hql.append(" order by id desc");
        PersonnelAttendanceIotTerminal personnelAttendanceIotTerminal = (PersonnelAttendanceIotTerminal) personnelAttendanceIotTerminalDao.getByHQLWithNamedParams(hql.toString(),params);
        return personnelAttendanceIotTerminal;
    }
}