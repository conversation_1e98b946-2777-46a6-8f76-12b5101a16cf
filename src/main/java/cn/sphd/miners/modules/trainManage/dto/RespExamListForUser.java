package cn.sphd.miners.modules.trainManage.dto;

import java.util.List;

/**
 * @ClassName RespExamListForUser
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/15 10:43
 * @Version 1.0
 */
public class RespExamListForUser {
    private List<RespExamForUser> respExamForUserList;
    private List<RespExamForUser> respExamForUserAgainList;
    private Integer examSum;
    private Integer examAgainSum;

    public List<RespExamForUser> getRespExamForUserList() {
        return respExamForUserList;
    }

    public void setRespExamForUserList(List<RespExamForUser> respExamForUserList) {
        this.respExamForUserList = respExamForUserList;
    }

    public List<RespExamForUser> getRespExamForUserAgainList() {
        return respExamForUserAgainList;
    }

    public void setRespExamForUserAgainList(List<RespExamForUser> respExamForUserAgainList) {
        this.respExamForUserAgainList = respExamForUserAgainList;
    }

    public Integer getExamSum() {
        return examSum;
    }

    public void setExamSum(Integer examSum) {
        this.examSum = examSum;
    }

    public Integer getExamAgainSum() {
        return examAgainSum;
    }

    public void setExamAgainSum(Integer examAgainSum) {
        this.examAgainSum = examAgainSum;
    }
}
