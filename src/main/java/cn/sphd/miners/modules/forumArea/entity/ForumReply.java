package cn.sphd.miners.modules.forumArea.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;

import javax.persistence.*;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @create 2018-02-1
 * @description
 **/
@Entity
@Table(name = "t_forum_reply")
public class ForumReply  extends BaseEntity {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "post")
  private Integer post;
  @Column(name = "attachment_id")
  private Integer attachmentId;
  @Column(name = "parent")
  private Integer parent;
  @Column(name = "level")
  private Integer level;
  @Column(name = "name")
  private String name;
  @Column(name = "title")
  private String title;
  @Column(name = "file_sn")
  private String fileSn;
  @Column(name = "content")
  private String content;
  @Column(name = "path")
  private String path;
  @Column(name = "valid")
  private Integer valid;
  @Column(name = "is_stick")
  private Integer isStick;
  @Column(name = "orders")
  private Integer orders;
  @Column(name = "size")
  private Integer size;
  @Column(name = "version")
  private String version;
  @Column(name = "keyword")
  private String keyword;
  @Column(name = "memo")
  private String memo;
  @Column(name = "parent_content")
  private String parentContent;
  @Column(name = "participants_num")
  private Integer participantsNum;
  @Column(name = "read_num")
  private Integer readNum;
  @Column(name = "reply_num")
  private Integer replyNum;
  @Column(name = "lastest_reply_time")
  private Date lastestReplyTime;
  @Column(name = "download_num")
  private Integer downloadNum;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_date")
  private Date createDate;
  @Column(name = "updator")
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_date")
  private Date updateDate;
  @Column(name = "approve_status")
  private String approveStatus;
  @Column(name = "approver")
  private Integer approver;
  @Column(name = "approver_name")
  private String approverName;
  @Column(name = "approve_date")
  private Date approveDate;
  @Column(name = "operation")
  private String operation;
  @Column(name = "apply_memo")
  private String applyMemo;
  @Column(name = "approve_memo")
  private String approveMemo;
  @Column(name = "message_id")
  private Long messageId;
  @Column(name = "org")
  private Integer org;
  @Column(name = "msg_type")
  private String msgType;
  /**
   * ios判断类型用的值 null是系统文件 1是文本 2是图片 3是文件 4是内部文件
   */
  @Column(name = "reply_type")
  private Integer replyType;
  @Column(name = "resource_id", nullable=true , unique=false )
  private Integer resourceId;
  @Column(name = "resource_version" )
  private Integer resourceVersion;
  @Column(name = "resource_history", nullable=true , unique=false )
  private Integer resourceHistory;
  @Column(name = "is_revised")
  private Byte isRevised;
  @Column(name = "is_uploaded")
  private Byte isUploaded;

  @Transient
  private List<ForumReplyAttachment> forumReplyAttachmentHashSet;

  @Transient
  private String listforumReplyAttachment;

  @Transient
  private List<ForumReply> ListForumReply;

  @Transient
  private Integer atTag;//@标记

  @Transient
  private Integer replyTag;//回复标记

  @Transient
  private BigInteger unreadCMum;//未读回复数

  @Transient
  private PostUserMussage postUserMussage;

  @Transient
  private Integer readStatus;//是否是新回复标识 1是新回复 0是已读回复

  @Transient
  private Integer atMum;//@回复标识 1是@的回复 其他则不是@的回复

  @Transient
  private Integer delStatus;//删除状态 1是可以删除 0是不可以删除

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getPost() {
    return post;
  }

  public void setPost(Integer post) {
    this.post = post;
  }

  public Integer getAttachmentId() {
    return attachmentId;
  }

  public void setAttachmentId(Integer attachmentId) {
    this.attachmentId = attachmentId;
  }

  public Integer getParent() {
    return parent;
  }

  public void setParent(Integer parent) {
    this.parent = parent;
  }

  public Integer getLevel() {
    return level;
  }

  public void setLevel(Integer level) {
    this.level = level;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getFileSn() {
    return fileSn;
  }

  public void setFileSn(String fileSn) {
    this.fileSn = fileSn;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public Integer getValid() {
    return valid;
  }

  public void setValid(Integer valid) {
    this.valid = valid;
  }

  public Integer getIsStick() {
    return isStick;
  }

  public void setIsStick(Integer isStick) {
    this.isStick = isStick;
  }

  public Integer getOrders() {
    return orders;
  }

  public void setOrders(Integer orders) {
    this.orders = orders;
  }

  public Integer getSize() {
    return size;
  }

  public void setSize(Integer size) {
    this.size = size;
  }

  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }

  public String getKeyword() {
    return keyword;
  }

  public void setKeyword(String keyword) {
    this.keyword = keyword;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getParticipantsNum() {
    return participantsNum;
  }

  public void setParticipantsNum(Integer participantsNum) {
    this.participantsNum = participantsNum;
  }

  public Integer getReadNum() {
    return readNum;
  }

  public void setReadNum(Integer readNum) {
    this.readNum = readNum;
  }

  public Integer getReplyNum() {
    return replyNum;
  }

  public void setReplyNum(Integer replyNum) {
    this.replyNum = replyNum;
  }

  public Date getLastestReplyTime() {
    return lastestReplyTime;
  }

  public void setLastestReplyTime(Date lastestReplyTime) {
    this.lastestReplyTime = lastestReplyTime;
  }

  public Integer getDownloadNum() {
    return downloadNum;
  }

  public void setDownloadNum(Integer downloadNum) {
    this.downloadNum = downloadNum;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  public String getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(String approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApprover() {
    return approver;
  }

  public void setApprover(Integer approver) {
    this.approver = approver;
  }

  public String getApproverName() {
    return approverName;
  }

  public void setApproverName(String approverName) {
    this.approverName = approverName;
  }

  public Date getApproveDate() {
    return approveDate;
  }

  public void setApproveDate(Date approveDate) {
    this.approveDate = approveDate;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public Long getMessageId() {
    return messageId;
  }

  public void setMessageId(Long messageId) {
    this.messageId = messageId;
  }

  public String getListforumReplyAttachment() {
    return listforumReplyAttachment;
  }

  public void setListforumReplyAttachment(String listforumReplyAttachment) {
    this.listforumReplyAttachment = listforumReplyAttachment;
  }

  public List<ForumReply> getListForumReply() {
    return ListForumReply;
  }

  public void setListForumReply(List<ForumReply> listForumReply) {
    ListForumReply = listForumReply;
  }

  public Integer getAtTag() {
    return atTag;
  }

  public void setAtTag(Integer atTag) {
    this.atTag = atTag;
  }

  public Integer getReplyTag() {
    return replyTag;
  }

  public void setReplyTag(Integer replyTag) {
    this.replyTag = replyTag;
  }

  public BigInteger getUnreadCMum() {
    return unreadCMum;
  }

  public void setUnreadCMum(BigInteger unreadCMum) {
    this.unreadCMum = unreadCMum;
  }

  public PostUserMussage getPostUserMussage() {
    return postUserMussage;
  }

  public void setPostUserMussage(PostUserMussage postUserMussage) {
    this.postUserMussage = postUserMussage;
  }

  public Integer getReadStatus() {
    return readStatus;
  }

  public void setReadStatus(Integer readStatus) {
    this.readStatus = readStatus;
  }

  public Integer getAtMum() {
    return atMum;
  }

  public void setAtMum(Integer atMum) {
    this.atMum = atMum;
  }

    public Integer getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(Integer delStatus) {
        this.delStatus = delStatus;
    }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public String getMsgType() {
    return msgType;
  }

  public void setMsgType(String msgType) {
    this.msgType = msgType;
  }

  public Integer getReplyType() {
    return replyType;
  }

  public void setReplyType(Integer replyType) {
    this.replyType = replyType;
  }

  public String getParentContent() {
    return parentContent;
  }

  public void setParentContent(String parentContent) {
    this.parentContent = parentContent;
  }

  public List<ForumReplyAttachment> getForumReplyAttachmentHashSet() {
    return forumReplyAttachmentHashSet;
  }

  public void setForumReplyAttachmentHashSet(List<ForumReplyAttachment> forumReplyAttachmentHashSet) {
    this.forumReplyAttachmentHashSet = forumReplyAttachmentHashSet;
  }

  public Integer getResourceId() {
    return resourceId;
  }

  public void setResourceId(Integer resourceId) {
    this.resourceId = resourceId;
  }

  public Integer getResourceVersion() {
    return resourceVersion;
  }

  public void setResourceVersion(Integer resourceVersion) {
    this.resourceVersion = resourceVersion;
  }

  public Integer getResourceHistory() {
    return resourceHistory;
  }

  public void setResourceHistory(Integer resourceHistory) {
    this.resourceHistory = resourceHistory;
  }

  public Byte getIsRevised() {
    return isRevised;
  }

  public void setIsRevised(Byte isRevised) {
    this.isRevised = isRevised;
  }

  public Byte getIsUploaded() {
    return isUploaded;
  }

  public void setIsUploaded(Byte isUploaded) {
    this.isUploaded = isUploaded;
  }
}
