import auth from '@/sys/auth'
import {request} from "@/utils/request";


/*
 * creator: 2024-2-14 hxz
 *   个人信息
*/
export function userInfoPreview(data) {
    data = data || {}
    return request({
        url: '/userInfo/userInfoPreview.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   个人信息 修改基本信息
*/
export function updateUserBasicInfo(data) {
    data = data || {}
    return request({
        url: '/userInfo/updateUserBasicInfo.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   个人信息 修改记录
*/
export function getUserBasicInfoHistory(data) {
    data = data || {}
    return request({
        url: '/userInfo/getUserBasicInfoHistory.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   个人信息 修改记录 查看
*/
export function getUserHistory(data) {
    data = data || {}
    return request({
        url: '/userInfo/getUserHistory.do',
        data: data
    })
}


/*
 * creator: 2024-2-14 hxz
 *   教育经历 新增
*/
export function addPersonalEducation(data) {
    data = data || {}
    return request({
        url: '/userInfo/addPersonalEducation.do',
        data: data
    })
}


/*
 * creator: 2024-2-14 hxz
 *   教育经历 修改
*/
export function updatePersonalEducation(data) {
    data = data || {}
    return request({
        url: '/userInfo/updatePersonalEducation.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   教育经历 修改记录
*/
export function personalEducationHistories(data) {
    data = data || {}
    return request({
        url: '/userInfo/personalEducationHistories.do',
        data: data
    })
}
/*
 * creator: 2024-2-14 hxz
 *   教育经历 删除
*/
export function markDeleteEducation(data) {
    data = data || {}
    return request({
        url: '/userInfo/markDeleteEducation.do',
        data: data
    })
}


/*
 * creator: 2024-2-14 hxz
 *   工作经历 新增
*/
export function addPersonnelOccupation(data) {
    data = data || {}
    return request({
        url: '/userInfo/addPersonnelOccupation.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   工作经历 修改
*/
export function updatePersonnelOccupation(data) {
    data = data || {}
    return request({
        url: '/userInfo/updatePersonnelOccupation.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   工作经历 修改记录
*/
export function personnelOccupationHistories(data) {
    data = data || {}
    return request({
        url: '/userInfo/personnelOccupationHistories.do',
        data: data
    })
}


/*
 * creator: 2024-2-14 hxz
 *   工作经历 删除
*/
export function markDeleteOccupation(data) {
    data = data || {}
    return request({
        url: '/userInfo/markDeleteOccupation.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   紧急联系人 修改
*/
export function addEmergencyContact(data) {
    data = data || {}
    return request({
        url: '/userInfo/addEmergencyContact.do',
        data: data
    })
}


/*
 * creator: 2024-2-14 hxz
 *   登陆设置  获取机构信息
*/
export function getAllOrgsByUserId(data) {
    data = data || {}
    return request({
        url: '/loginSettings/getAllOrgsByUserId.do',
        data: data,
        LOADING_TEXT: '正在获取登录机构……'
    })
}


/*
 * creator: 2024-2-14 hxz
 *   登陆设置  修改
*/
export function updateDefaulOrg(data) {
    data = data || {}
    return request({
        url: '/loginSettings/updateDefaulOrg.do',
        data: data
    })
}


/*
 * creator: 2024-2-14 hxz
 *   个人信息 全部修改确认
*/
export function submitUserInfo(data) {
    data = data || {}
    return request({
        url: '/userInfo/submitUserInfo.do',
        data: data
    })
}


/*
 * creator: 2024-2-14 hxz
 *   系统设置 获取系统信息
*/
export function getSystemSetStatus(data) {
    data = data || {}
    return request({
        //     user/updateLoginStatus.do
        url: '/user/getSystemSetStatus.do',
        data: data
    })
}

/*
 * creator: 2024-2-14 hxz
 *   系统设置 获取系统信息
*/
export function updateloginstatus(data) {
    data = data || {}
    return request({
        url: '/user/updateLoginStatus.do?loginStatus='+ data.state,
    })
}








