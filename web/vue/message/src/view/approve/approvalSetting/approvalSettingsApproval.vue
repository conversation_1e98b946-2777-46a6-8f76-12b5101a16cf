<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="approvalSetting" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="审批设置的修改审批" isBackHome="true" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="ttl3" v-if="bicolumn">
        <div v-bind:style="{ 'border-bottom-color': preColor }" @click="toggle(0)">待处理</div>
        <div v-bind:style="{ 'border-bottom-color': nextColor }" @click="toggle(1)">已批准</div>
      </div>
      <div class="ui-cells">
        <!-- 待审批 -->
        <a class="ui-cell" @click="applyDetail(item.id)" v-if="type === 0" v-for='(item, index) in list' v-bind:key='index'>
          <div class="dd1">
            <p>{{item.description}}</p>
            <p class="right"><span>{{item.askName}}</span> <span>{{item.createDateItem | formatDay('YYYY-MM-DD HH:mm:ss')}}</span></p>
          </div>
        </a>
        <!-- 已批准 -->
        <a class="ui-cell" @click="applyDetail(item.id)" v-if="type === 1" v-for='(item, index) in listed' v-bind:key='index'>
          <div class="dd1">
            <p>{{item.description}}</p>
            <p class="right"><span>{{item.askName}}</span> <span>{{item.createDateItem | formatDay('YYYY-MM-DD HH:mm:ss')}}</span></p>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #approvalSetting{
    .dd1{ width:100%; box-sizing:border-box;
      .right{
        text-align:right; color:#aaa; font-size:0.7em;
        span{ margin-right: 20px;  }
      }
    }
    .ttl3{
      display: flex;
      div{ flex:1; border-bottom: 2px solid #ddd; text-align:center; padding:10px;   }
    }

  }
</style>
<script>
export default {
  name: 'approvalSettingsApproval',
  data () {
    return {
      loading: true,
      nextColor: '#ddd',
      preColor: 'rgb(54, 198, 211)',
      listenersUid: [],
      list: [],
      listed: [],
      bicolumn: true,
      type: 0
    }
  },
  created: function () {
    let _this = this
    _this.getList()
    _this.listenersUid = [
      // 审批人待审批
      _this.sphdSocket.subscribe('handleApproval', function (data) {
        console.log('获取订阅审批结果:' + data)
        _this.getList()
      }, function () {
        console.log('审批人待审批 失败！')
      }, 'user'),
      // 审批人已批准
      _this.sphdSocket.subscribe('approvalApproval', function (data) {
        console.log('审批人已批准结果:' + data)
        _this.applyDetail()
      }, function () {
        console.log('审批人已批准订阅 Error:')
      }, 'user')
    ]
  },
  methods: {
    toggle: function (type) {
      this.type = type
      if (type === 0) {
        this.preColor = 'rgb(54, 198, 211)'
        this.nextColor = '#ddd'
      } else {
        this.nextColor = 'rgb(54, 198, 211)'
        this.preColor = '#ddd'
      }
    },
    getList: function () {
      let _this = this
      this.axios.post('../../../popedom/handleApproval.do', { session:this.sphdSocket.sessionid, oid: this.sphdSocket.user.oid, userId: this.sphdSocket.user.userID })
        .then(function (response) {
            _this.loading = false
            let data = response.data.data
            _this.list = data.approvalProcessList // 待审批列表
            _this.listed = data.approvedProcesses // 已批准列表
            _this.bicolumn = data.bicolumn
          }).catch(function (error) {
            _this.$kiko_message("链接错误，请重试！")
          })
    },
    search: function () {
      this.$router.push({
        path: `/approvalSettingsSearch/2`
      })
    },
    applyDetail: function (itemID) {
      this.$router.push({
        path: '/approvalSettingsApprovalDetails/' + itemID
      })
    }
  },
  destroyed: function () {
    let _this = this
    console.log('destroyed')
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  }
}
</script>
