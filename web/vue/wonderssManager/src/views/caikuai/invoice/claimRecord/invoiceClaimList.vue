<template>
  <!--  <p>该时间段申领发票列表</p>-->
  <div class="box">
    <div class="sticky">
      <p class="padding-0-15 ty-fff" style="font-size: 14px;line-height: 35px">
        {{ year1 }}至{{ formattedDate }}期间共申领发票{{ cou }}次
      </p>
      <div class="cai-liGray" style="height: 16px"></div>
    </div>
    <ul>
      <li v-for="item in list1" :key="item.id" class="flex">
        <div>
          <p>
            <span>{{ item.cname }}</span>
            <span>起止号码{{ item.cid }}</span>
          </p>
          <p>
            申领
            <span class="padding-0-10">{{ item.name }}</span>
            {{ item.date }}
          </p>
        </div>
        <el-icon style="font-size: 20px" @click="xQ">
          <arrow-right/>
        </el-icon>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: "invoiceClaimList",
  data() {
    return {
      //今年/去年/前年....年份起始日期
      year1: '2024-01-01',
      cou: "(申领次数)",
      list1: [
        {id: 0, cid: "789456-123456", cname: "增值税专用发票", name: "刘总监", date: "2024-01-01"},
        {id: 0, cid: "789456-123456", cname: "增值税普通发票", name: "刘总监", date: "2024-01-01"},
        {id: 0, cid: "789456-123456", cname: "XXX发票", name: "刘总监", date: "2024-01-01"},
        {id: 0, cid: "789456-123456", cname: "XXXX增值税专用发票", name: "刘总监", date: "2024-01-01"},
      ]
    }
  }, computed: {
    currentDateTime() {
      return new Date().toLocaleDateString(); // 获取当前日期和时间(本地格式 很丑)
    },

    formattedDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  },
  methods: {
    xQ() {
      this.$router.push("/invoiceClaimDetails")
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/account.scss";
@import "src/style/common.scss";

.flex {
  padding: 10px 15px;
  align-items: center;

}

ul {
  > li:nth-of-type(odd) {
    background-color: #ffffff;
  }

  > li:nth-of-type(even) {
    background-color: $tyBounceColorGray;
  }

  > li {
    > div {
      width: 100%;

      > p:nth-child(1) {
        display: flex;

        > span {
          display: block;
          //width: 50%
        }

        > span:nth-child(1) {
          width: 45%;
        }

        > span:nth-child(2) {
          width: 55%;
        }
      }
    }
  }
}

.box {
  background-color: $tyColorBg;
}
</style>