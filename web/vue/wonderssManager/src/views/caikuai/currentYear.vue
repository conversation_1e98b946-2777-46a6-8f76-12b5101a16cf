<!--点击本年按钮后页面-->

<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :showBack="true" :runMyBackFun="true" @myBackFun="backFun"  :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <p class="margin-10-15" >{{ timeDur }}</p>
  <div class="hr_gray"></div>
  <div class="current-balance">
    <p>当前余额<span>{{ allBalance }}</span></p>
  </div>
  <div class="hr_gray"></div>
  <div class="flex">
    <div class="balance1" v-for="(item) in bala_list" :key="item.id">
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <div class="flex date-account">
    <div @click="ViewB_A(0)" :class="{padding_bottom_green:isActive}">按时间查看</div>
    <div @click="ViewB_A(1)" :class="{padding_bottom_green:isActive==false}">按账户查看</div>
  </div>
  <div>
    <router-view/>
  </div>
</template>

<script>

import ViewByAccount from "../caikuai/cai-views/ViewByAccount.vue";
import finance from '@/mixins/finance.js'
export default {
  name: "currentYear",
  mixins: [finance],
  components: {ViewByAccount},
  data() {
    return {
      activeIndex: 4,
      timeDur:'',
      allBalance: 0 ,
      bala_list: [],
      accountList: [],
      isActive: true,

    }
  },

  mounted() {
    this.getFourData()
  },
  methods: {
    backFun(){
      this.$router.push("/caikuai");
    },
    getFourData(){
      let org = this.auth.getOrg()
      this.financeApi.financeAmount({ oid:org.id , state:4  }).then(res1=>{
        let res = res1.data.data
        let beginDate = (new Date(res.beginDate).format('yyyy-MM-dd'))
        let endDate = (new Date(res.endDate).format('yyyy-MM-dd'))
        localStorage.setItem('f_beginDate', beginDate)
        localStorage.setItem('f_endDate', endDate)
        this.timeDur = beginDate + ' ~ ' + endDate
        this.allBalance = this.formatCurrency(res.allBalance)

        this.bala_list = [
          {id: 0, da: "去年余额", money: this.formatCurrency(res.allPreviousBalance)  , sr: this.balance1 },
          {id: 1, da: "本年收入", money: this.formatCurrency(res.allCredit)  , sr: this.balance2 , style: "ty-color-green"},
          {id: 2, da: "本年支出", money:this.formatCurrency(res.allDebit) , sr: this.balance3 , style: "ty-color-red"},
        ]

      }).catch(err=>{
        this.$message.error('链接错误')
        console.log('err=', err)
      })

    },
    ViewB_A(index) {
      if (index == 0) {
        this.isActive = true
        this.$router.push({path:"/ViewByTimeYear"})

      } else if (index == 1) {
        this.isActive = false;
        this.$router.push({path:"/ViewByAccountYear"})
      }
    },
    rou(index) {
      if (index == 0) {
        this.curr = 1
        this.mes = "该月的日列表"
        this.sho = false
        console.log("日列表")
        this.back = false
      } else if (index == 1) {
        this.curr = 6
        this.mes = "该日的流水"
        this.hea = false
        console.log("该日的流水")
      } else if (index == 2) {
        this.curr = 4;
        this.mes = "该账户该年的月列表"
        this.back = false
        this.aa = true
        this.sho = false
        // this.s=1
      } else if (index == 3) {
        this.mes = "该账户该年该月该日的流水"
        this.hea = false
        this.curr = 6
      }

    },
    hp1() {
      this.sho = false
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../style/account.scss";
@import "../../style/common.scss";

.page {
  //z-index: 88;
  //display: flex;
  //position: absolute;
  margin-top: 20px;
}

.ta-list {
  position: relative;

  span:nth-child(2) {
    position: absolute;
    right: 35%;
  }

  span:nth-child(3) {
    position: absolute;
    right: 18%;
  }
}
.balance1{
  margin: 0px 0px;
}
.flex{
  padding: 5px 20px;
  justify-content: space-between;
}

</style>