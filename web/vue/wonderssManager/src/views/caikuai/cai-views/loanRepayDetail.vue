<!--日流水的具体详情-->
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>

    <div v-if="info">

      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li>
          <span>{{ info.createName }}
            {{ new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss') }}创建
          </span>
          <span class="ty-linkBtn" @click="editLog"> 修改记录 </span>
        </li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>收款金额</span><span>{{ formatCurrency(info.repaymentAmount) }}</span></li>
        <li><span>收款日期</span><span>{{ new Date(info.repaymentTime).format('yyyy-MM-dd')  }}</span></li>
        <li><span>支付方式</span><span>{{ chargePayMethod(info.repaymentMethod)  }}</span></li>
        <li v-if="info.loanType === '2'"><span>收款经手人</span><span>{{ info.receiveOperatorName  }}</span></li>
<!--        <li><span>到账日期</span><span>{{ new Date(info.arriveDate).format('yyyy-MM-dd')  }}</span></li>-->
<!--        <li><span>收款银行</span><span>{{ (info.receiveBank)  }}</span></li>-->

      </ul>
      <div class="hr_gray"></div>


    </div>



  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "loanDetail",
  data() {
    return {
      id:'',
      info: null,
    }
  },
  mixins: [finance],
  mounted() {
    let loanRepayDetail = localStorage.getItem('loanRepayDetail')
    loanRepayDetail = JSON.parse(loanRepayDetail)
    console.log('loanRepayDetail=', loanRepayDetail)
    this.id = loanRepayDetail.id
    this.getLoanRepayDetail(loanRepayDetail.id)
  },
  methods: {
    getLoanRepayDetail(id){
      this.financeApi.ordRepaymentDetail({ id : id }).then(res1=>{
        let res = res1.data.data
        this.info = res

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    editLog(){
      localStorage.setItem('loanRepayEditList', JSON.stringify(this.info))
      this.$router.push({ path:`/loanRepayEditList` })

    },


  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.turnHang{
  display: inline-block;  white-space:normal; line-height: 20px;
  max-width: 60%;
}
.loan{
  .fa-angle-right{ font-size: 20px;  }

}
</style>