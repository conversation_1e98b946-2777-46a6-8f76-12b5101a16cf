import {request} from "@/utils/request";

// creator: 张旭博，2024-12-10 03:14:07， 获取中枢管控主页面数据
export function getControlInfo () {
    return request({
        url: '/org/getControlInfo.do'
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 添加临时管理员
export function addTemporaryAdmin (data) {
    return request({
        url: '/org/addTemporaryAdmin.do',
        data: data
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 查看临时管理员
export function getTemporaryAdmin () {
    return request({
        url: '/org/getTemporaryAdmin.do'
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 更新临时管理员
export function updateTemporaryAdmin (data) {
    return request({
        url: '/org/updateTemporaryAdmin.do',
        data: data
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 获取高管列表
export function getManageList () {
    return request({
        url: '/sys/dynamicManageList.do'
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 可选成 全权负责人的（总经理） 人员列表 （只能给总经理选择用）
export function getSelectStaffs (roleCode) {
    return request({
        url: '/sys/getSelectStaffs.do',
        data: {
            roleCode: roleCode
        }
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 可选成 高管的人员列表 （财务、总务、销售、会计选择非代理机构人员时）
export function getSelectStaffUsers (roleCode) {
    return request({
        url: '/sys/getSelectStaffUsers.do',
        data: {
            roleCode: roleCode
        }
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 添加全权负责人
export function addOrgSmallSuper (passiveUserId) {
    return request({
        url: '/sys/addOrgSmallSuper.do',
        data: {
            passiveUserId: passiveUserId
        }
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 暂不设置最高负责人
export function noSmallSuper (oldUserId) {
    return request({
        url: '/sys/noSmallSuper.do',
        data: {oldUserId: oldUserId}
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 设置会计高管
export function addAccounting (data) {
    return request({
        url: '/sys/addAccounting.do',
        data: data
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 修改代理状态
export function editAccountingAgentType (data) {
    return request({
        url: '/sys/editAccountingAgentType.do',
        data: data
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 设置其他高管（总务、财务、销售）
export function addManager (roleCode, passiveUserId) {
    return request({
        url: '/sys/addManage.do',
        data: {
            roleCode: roleCode, // 管理者类型
            passiveUserId: passiveUserId // 被换的人的id
        }
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 修改全权负责人
export function updateOrgSmallSuper (oldUserId, newUserId) {
    return request({
        url: '/sys/updateOrgSmallSuper.do',
        data: {
            oldUserId: oldUserId,
            newUserId: newUserId
        }
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 修改会计
export function updateAccounting (data) {
    return request({
        url: '/sys/editManage.do',
        data: data
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 修改其他高管（总务、财务、销售）
export function updateManager (manageId, passiveUserId) {
    return request({
        url: '/sys/replaceManages.do',
        data: {
            manageId: manageId,
            passiveUserId: passiveUserId
        }
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 修改其他高管（总务、财务、销售）
export function confirmOrg () {
    return request({
        url: '/org/confirmOrg.do'
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 修获取历任负责人
export function getChargeHistory (manageId) {
    return request({
        url: '/sys/getRolePrincipalHistories.do',
        data: {
            manageId: manageId
        }
    })
}

// creator: 张旭博，2024-12-10 03:14:07， 放弃导入
export function giveUpImportUser () {
    return request({
        url: '/userImport/giveUpImportUser.do'
    })
}