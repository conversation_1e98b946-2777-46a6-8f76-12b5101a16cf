package cn.sphd.miners.modules.resourceAuthority.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAclLog;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategoryHistory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResDepartAndEmployee;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONObject;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2017/10/31.
 */
public interface ResCategoryService {

    //资源中心不是超管、总务、小总务的人和资料管理获取初始文件夹
    QueryData getFirstType(User user, String type);

    //文件管理超管、总务、小总务获取初始文件夹
    HashMap<String, Object> getFirstTypeByManager(User user);

    //获取本级文件夹和子级文件夹的信息
    QueryData getCategoryAndChildCategory(User user, String type, Integer categoryId);

    //资源中心超管、总务、小总务获取本级文件夹和子集文件夹
    QueryData getCategoryAndChildCategoryManager(User user, Integer categoryId);

    List<ResDepartAndEmployee> getDepartAndEmployee(int oid);

    JSONObject getDepartment(int oid, int pid);

    //新增文件夹(资源中心)
    ResCategory addSameCategoryByResource(User user, Integer parent, String categoryName, String type, List<Integer> listUserIDs);

    //新增文件夹(资源管理)
    QueryData addSameCategoryByData(User user, Integer parent, String categoryName, String type);

    ResCategory getSingle(Integer categoryId);
    List<OrganizationDto> getAuthOrgWithUserTreeByOid(Integer oid, Integer categoryId, String type);
    String updateFolderAuth(Integer categoryId, List<Integer> userID, User user, String operateType, String allCategoryName, Long leafs);
    //wyu: End of Forder Auth

    //修改文件夹名字
    QueryData UpdateCategoryName(User user, ResCategory resCategory);

    //修改文件夹锁Uuid
    void updateResCatUuid(Integer categoryId);

    //删除文件夹
    String deleteCategory(Integer categoryId);

    //手机获取某个文件夹下的全部不人员权限
    QueryData getCategoryAuthUser(Integer categoryId);

    //循环给文件夹的文件个数和大小进行累加或递减
    void sumCategoryLeafsAndSize(ResCategory category, BigInteger size, Integer leafs);

    //获取修改文件夹名字的历史
    List<ResCategoryHistory> listCategoryHistory(Integer categoryId);

    //判断文件夹是否存在
    ResCategory checkFolder(User user, Integer parent, String categoryName, String type);

    //新增权限修改记录
    void insertResAclLog(Integer categoryId, Integer fileId, List<Integer> newUsers, List<Integer> delUsers,
                         User user, Date now, String categoryName, Integer oid, String operateType);

    //获取权限的修改记录
    List<ResAclLog> listResAclLog(Integer oid, String year, String month, String name, String type, PageInfo pageInfo);

    //获取某条修改记录
    QueryData resAclLogMessage(Integer aclId, String type);

    //获取文件夹的权限
    List<Integer> getAllUserIdsByCategory(Integer categoryId);

    //获取文件夹的权限
    List<Integer> getAllUserIdsByFile(Integer resId);

    //移动文件夹（资源）
    Integer changeCategory(User user, Integer oldCategoryId, Integer newCategoryId, String resLockUuid, String userID);

    //判断文件夹是否可以移动
    HashMap<String, Object>  judgementMove(User user, Integer oldCategoryId, Integer fileId, Integer newCategoryId, String type);

}



