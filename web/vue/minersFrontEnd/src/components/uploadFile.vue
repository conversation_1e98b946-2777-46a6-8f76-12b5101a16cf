<!--
  上传图片子组件
----desc：该子组件不是全局公用组件
----pages（用到的页面）：['views/demoUpload.vue', ]
-->
<template>
 <div class="uploadFile" ref="uploadFile">
   <el-upload ref="upload"
              :show-file-list="showFileList"
              :multiple="multiple"
              :auto-upload="autoUpload"
              :accept="ext"
              :http-request="upload"
              :before-remove="beforeRemove"
              :before-upload="beforeUpload"
              :on-change="handleChange"
              :on-preview="handlePreview"
              :on-remove="uploadRemove"
              :on-error="handleError"
              :on-exceed="handleExceed"
   >
     <template #trigger>
       <slot name="btnArea">
         <span class="txtLink">{{ status_txt }}</span>
       </slot>
       <el-button size="small" type="primary" v-if="showSelect && filesMap.size<=0">选取文件<span v-if="selectFolder">夹</span></el-button>
     </template>
     <el-button v-if="showFileList && !autoUpload && filesMap.size>0 && !start"
         style="margin-left: 10px;"
         size="small"
         type="success"
         @click="submitUpload"
     >上传到服务器</el-button>
   </el-upload>
   <div
   >
   </div>

 </div>
</template>
<script>
import auth from '@/sys/auth'
import { nanoid } from 'nanoid'
import SparkMD5 from 'spark-md5'
import JSONBig from 'json-bigint'
import { formatBytes } from '@pantajoe/bytes'
import { request, noloadingHttp } from "@/utils/request"

const CHUNK_SIZE = 512 * 1024
const MAX_UNCHUNK_SIZE = 1 * 1024 * 1024
const MAX_UPLOAD_SIZE = 3 * 512 * 1024 * 1024

export default {
  name: "uploadFile",
  data() {
    return {
      filesMap: new Map(),
      fileChunks: new Map(),
      groupUuid: null,
      start: false,
      intevalLogout: null,
      stopILTime: null,
      uploadingCount: 0,
      exts: null,
      files: [],
      status_txt: ''
    }
  },
  props: {
    module: {type: String, default: null},//模块，不能为空
    showSelect: {type: Boolean, default: true},//是否显示选择文件按钮
    showFileList: {type: Boolean, default: true},//是否显示文件列表
    multiple: {type: Boolean, default: false},//是否多选文件上传
    autoUpload: {type: Boolean, default: true},//是否选完文件自动上传
    selectFolder: {type: Boolean, default: false},//文件夹上传
    maxSizeLimit: {type: Number, default: MAX_UPLOAD_SIZE},//单个文件最大限制。
    ext: {type: String, default: null},//文件类型限制
    extMsg: {type: String, default: null}, //文件类型错误信息
    committed: {type: Boolean, default: false},//数据已提交,父类提交业务数据后后再修改
    addFun: {
      type: Function, default: (file, fileList) => {
      }
    },
    removeFun: {
      type: Function, default: (file) => {
      }
    },
    //返回false禁用默认函数效果
    successFun: {
      type: Function, default: (file, files, ofile, totalFiles) => {
        return true
      }
    },
    beforeUploadFun: {
      type: Function, default: (file) => {
        return true
      }
    },
    //返回false禁用默认函数效果
    processFun: {
      type: Function, default: (file, process) => {
        return true
      }
    }
  },
  mounted() {
    if(this.selectFolder) {
      this.$refs.upload.$el.getElementsByClassName('el-upload__input')[0].webkitdirectory = true
    }
    this.exts = this.getAllExts()
  },
  beforeUnmount() {
    this.clearAll()
  },
  methods: {
    upload(options) {
      this.start = true
      this.status_txt = '正在上传...'
      this.setDelayLogoutTime(new Date().getTime() + 72 * 3600 * 1000)//三天
      let file = options.file
      if (file.size < MAX_UNCHUNK_SIZE) {
        this.sendFileSingle(options, file)
      } else {
        this.sendFileChunks(options, file)
      }
      // console.log('this.upload', file.uid, file.name)
    },
    sendFileSingle(options,file) {
      let that = this
      const formData = new FormData()
      formData.append('module', this.module)
      formData.append('uploadUid', file.uid)
      if (this.groupUuid != null) {
        formData.append('groupUuid', this.groupUuid)
      }
      formData.append('file', file)
      noloadingHttp({
        url: auth.webRoot + '/uploads/uploadfyByFile.do',
        data: formData,
        transformRequest: [function (data, headers) {
          // 去除post请求默认的Content-Type
          delete headers['Content-Type']
          return data
        }],
      }).then(res => {
        // console.log('data', res)
        if (res.status == '200') {
          let data = res.data
          console.log('sendFileSingle', data)
          that.showOK(data, file)
          options.onProgress({percent: 100})
        } else {
          that.$message.error(data.message)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    showOK (data, file) {
      this.files.push(data)
      if(this.successFun(data, this.files, file, Array.from(this.filesMap.values()))!==false) {
        // this.$message.success(data.displayName + '上传成功')
      }
      if(this.files.length >= this.filesMap.size) {//文件全部上传，可以提交业务数据
        this.status_txt = '上传成功，'+this.files.length+'个文件已上传！'
      }
      this.setDelayLogoutTime(new Date().getTime() + 72 * 3600 * 1000)//三天
    },
    sendFileChunks(options, file) {
      this.chunkFile(file).then(() => {
        let fileBlocks = this.fileChunks.get(file.uid)
        for (let i = 0; i < fileBlocks.length; i++) {
          this.sendFile(options, file, i, fileBlocks[i])
        }
      })
    },
    chunkFile(file) {
      return new Promise(resolve => {
        let totalChunks = Math.ceil(file.size / CHUNK_SIZE)
        let fileBlocks = []
        let resolveCount = 0
        for (let i = 0; i < totalChunks; i++) {
          setTimeout(() => {
            let chunkIndex = i
            let fileBlock = file.slice(CHUNK_SIZE * i, CHUNK_SIZE * (i + 1))
            fileBlocks.push(new Map().set('chunk', fileBlock).set('uploading', false).set('totalChunks', totalChunks))
            fileBlock.arrayBuffer().then(data => {
              fileBlocks[chunkIndex].set('hash', SparkMD5.ArrayBuffer.hash(data))
              if (++resolveCount >= totalChunks) {
                resolve('success')
              }
            })
          }, 0)
        }
        this.fileChunks.set(file.uid, fileBlocks)
      })
    },
    sendFile(options, file, chunkIndex, fileChunk) {
      let that = this
      let totalChunks = fileChunk.get('totalChunks')
      if (!fileChunk.get('uploading')) {
        fileChunk.set('uploading', true)
        const formData = new FormData()
        formData.append('metadata', JSONBig.stringify({
          chunkIndex: chunkIndex,
          contentType: file.type,
          fileName: file.name,
          relativePath: file.webkitRelativePath,
          totalFileSize: file.size,
          totalChunks: totalChunks,
          uploadUid: file.uid,
          hash: fileChunk.get('hash')
        }))
        formData.append('module', this.module)
        if (this.groupUuid != null) {
          formData.append('groupUuid', this.groupUuid)
        }
        formData.append('file', fileChunk.get('chunk'))
        setTimeout(function () {
          // console.log('formData', formData)
          noloadingHttp({
            url: auth.webRoot + '/uploads/uploadfyByKendo.do',
            data: formData,
            transformRequest: [function (data, headers) {
              // 去除post请求默认的Content-Type
              delete headers['Content-Type']
              return data
            }],
          }).then(res => {
            const fileItem = that.filesMap.get(file.uid)
            // console.log('res', res)
            if (res.status == '200') {
              fileChunk.set('uploading', false)
              that.uploadingCount--
              let data = res.data
              if (data.uploaded) {
                fileItem.process = that.updateProcess(fileItem.process, 100)
                // console.log("finish process", fileItem.process)
                that.showOK(data ,file)
              } else if (data.minMissIndex >= 0) {
                fileItem.process = that.updateProcess(fileItem.process, 80 + data.minMissIndex * 20 / totalChunks)
                // console.log('this.upload re sendFile', file.uid, file.name, data.minMissIndex, chunkIndex)
                that.sendFile(options, file, data.minMissIndex, that.fileChunks.get(file.uid)[data.minMissIndex])
              } else {
                fileItem.process = that.updateProcess(fileItem.process, chunkIndex * 80 / totalChunks)
              }
              if(that.processFun(file, fileItem.process)!==false) {
                options.onProgress({percent: fileItem.process})
              }
              // console.log('this.upload my process', file.uid, file.name, fileItem.process, totalChunks, chunkIndex, data.minMissIndex)
            } else {
              that.$message.error(data.message)
            }
          }).catch(error => {
            console.error(error)
          })
        }, that.uploadingCount++ * 2000)
      }
    },
    updateProcess(oldProcess, newProcess) {
      return Math.ceil(Math.min(Math.max(oldProcess, newProcess), 100))
    },
    beforeRemove(file, fileList) {
      // console.log('beforeRemove 0000')
      // console.log('this.postData=', this.postData)
      // console.log(' uploadAction ', this.uploadAction)
      // console.log(' file ', file)
      // console.log(' fileList ', fileList)
      if(this.committed) {
        this.$message.error('数据已提交，无法删除')
        return false
      } else {
        return true
      }
    },
    submitUpload() {
      console.log('submitUpload 出发了')
      console.log('this.$refs=', this.$refs)
      console.log('this.$refs.upload=', this.$refs.upload)
      this.$refs.upload.submit()
    },
    handleExceed(files, fileList) {
      console.log('handleExceed  ')
    },
    handleError(err, file, fileList) {
      console.log('handleError file=', file)
    },
    handlePreview(file) {
      console.log('handlePreview file=', file)
    },
    handleChange(file, fileList) {
      let originalFilename = file.name
      file.name = file.raw.webkitRelativePath ? file.raw.webkitRelativePath : file.name
      if(!this.removeInvalid(file, originalFilename, fileList)) {
        for (let item of fileList) {
          if (!this.filesMap.get(item.uid)) {
            this.filesMap.set(item.uid, {'fileItem': item, 'process': '0'})
          }
        }
        if (this.filesMap.size > 1 && this.groupUuid == null) {
          this.groupUuid = nanoid()
        }
        this.addFun(file, fileList)
      }
    },
    removeInvalid(file, originalFilename, fileList) {
      if (this.sizeInvalid(file) || this.extInvalid(originalFilename, file)) {//检查限制
        if(typeof fileList !== 'undefined') {
          this.doRemove(file, fileList)
        }
        return true
      } else {
        return false
      }
    },
    moduleInvalid() {
      if(this.module) {
        return false
      } else {
        this.$message.error('前端代码问题，模块（module）不能为空！')
        return true
      }
    },
    sizeInvalid(file) {
      if (this.maxSizeLimit > 0 && file.size > this.maxSizeLimit) {//检查文件大小
        this.$message.error('”' + file.name + '“文件大小超过' + formatBytes(this.maxSizeLimit, {decimals: 2}) + '限制，无法上传！')
        return true
      } else {
        return false
      }
    },
    extInvalid(filename,file) {
      if (this.exts && !this.exts.includes(this.getFilenameExt(filename))) {//检查文件扩展名
        let err = this.extMsg ?? '不符合文件扩展名限制'
        this.$message.error(err+'，”' + file.name + '“无法上传')
        return true
      } else {
        return false
      }
    },
      getFilenameExt(filename) {
      let index = filename.lastIndexOf('.')
      if(index>=0 && index < filename.length+1) {
        return filename.substring(index+1)
      } else {
        return ''
      }
    },
    getAllExts() {
      if(this.ext && this.ext != '*' && this.ext != '*.*') {
        return this.ext.split(',').map(item=>item.substring(1))
      } else {
        return null
      }
    },
    doRemove(file, fileList) {
      fileList.splice(fileList.indexOf(file), 1)
    },
    beforeUpload(file) {
      if(this.moduleInvalid() || this.removeInvalid(file, file.name)) {
        return false
      } else {
        return this.beforeUploadFun(file)
      }
    },
    uploadRemove(file, fileList) {
      if (this.filesMap.get(file.uid)) {
        this.filesMap.delete(file.uid)
        this.removeRemote(file)
      }
      if (this.filesMap.size <= 1) {
        this.groupUuid = null
      }
      this.removeFun(file)
    },
    removeRemote(file) {
      let index
      console.log('removeRemote', this.files.findIndex(item => item.fileUid == file.uid), this.files, file.uid)
      if ((index = this.files.findIndex(item => item.fileUid == file.uid)) >= 0) {//已经上传完成
        request({
          url: auth.webRoot + '/uploads/removeByFile.do',
          data: {fileUid: file.uid},
        }).then(res => {
          if (res.status == '200') {
            this.files.slice(index, 1)
          } else {
            that.$message.error(data.message)
          }
        }).catch(error => {
          console.log(error)
        })
      }
    },
    removeRemoteByGroup() {
      if (this.files.length > 0) {//已经上传完成
        request({
          url: auth.webRoot + '/uploads/removeFilesByGroup.do',
          data: {groupUuid: this.groupUuid},
        }).then(res => {
          this.files.length = 0
          console.log(res)
        }).catch(error => {
          console.log(error)
        })
      }
    },
    removeRemoteAll() {
      if(this.files.length >0) {
        if (this.groupUuid) {
          this.removeRemoteByGroup(this.groupUuid)
        } else {
          for(let file in this.files) {
            this.removeRemote(file)
          }
        }
      }
    },
    clearAll() {
      if(!this.committed && this.files.length>0) {
        this.removeRemoteAll()
      }
      this.clearDelayLogoutTime()
    },
    /**
     * 调用防止注销方法window.delayLogout.setDelayLogoutTime(atime)
     * <AUTHOR>
     * @since 2021/2/20 18:33
     * @Param atime: 超时的时间点（与服务器时间比较）的getTime()
     * @return: null
     */
    setDelayLogoutTime(atime) {
      if ((!isNaN(atime) && typeof atime === 'number') && (isNaN(this.stopILTime) || typeof this.stopILTime !== 'number' || this.stopILTime < atime)) {
        //atime为数字且stopILTime为空或者stopILTime < atime
        this.stopILTime = atime
      }
      if (this.intevalLogout == null) {
        this.refreshLogoutTime()
        this.intevalLogout = setInterval(this.refreshLogoutTime, 8 * 60 * 1000)//8分钟调用一次
      }
    },
    refreshLogoutTime() {
      if(!isNaN(this.stopILTime) && typeof this.stopILTime === 'number' && this.stopILTime > new Date().getTime()) {
        let event = new MouseEvent('mousewheel', {
          'view': window,
          'bubbles': true,
          'cancelable': true
        })
        this.$refs.uploadFile.dispatchEvent(event)
      } else {
        this.clearDelayLogoutTime()
        this.$message.error('超过3天没有完成！')
      }
    },
    clearDelayLogoutTime() {
      clearInterval(this.intevalLogout)
      this.intevalLogout = null
      this.stopILTime = null
    }
  },
}
</script>

<style lang="scss" scoped>
.uploadFile ::v-deep(*){
  .el-upload-list__item-info {
    a.el-upload-list__item-name {
      width: 25%;
      span.el-upload-list__item-file-name {
        white-space: pre-wrap;
      }
    }
    .el-progress {
      top: 0;
      left: 25%;
      width: 75%;
    }
  }
}
</style>