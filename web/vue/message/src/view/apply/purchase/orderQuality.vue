<template>
  <div id="orderQuality" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="不合格品处理" :isBackHome=false></TY_NavTop>
    <div class="container">
      <div class="tip">该不合格品所属订单的质量情况如下：</div>
      <div class="ui-cells">
        <a class="ui-cell" @click="seeList('')">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>共报验 <span class="blue">{{quality.size}}</span> 种物料</div>
            </div>
          </div>
          <div class="ui-cell__ft">查看</div>
        </a>
        <a class="ui-cell" @click="seeList('3')">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div> <span class="blue">{{quality.accept}}</span> 种物料检验已通过</div>
            </div>
          </div>
          <div class="ui-cell__ft">查看</div>
        </a>
        <a class="ui-cell" @click="seeList('4')">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div> <span class="blue">{{quality.unqualified}}</span> 种物料检验不合格</div>
            </div>
          </div>
          <div class="ui-cell__ft">查看</div>
        </a>
      </div>
      <div class="ui-row right">
        <div>采购 {{quality.create_name}} {{quality.create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
        <div>检验 {{quality.check_name}} {{quality.check_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
      </div>
    </div>
  </div>
</template>

<style>
  #orderQuality .ui-cell{
    font-size: 12px;
  }
</style>

<script>
export default {
  name: 'orderQuality',
  data () {
    return {
      isBackHome: false,
      listenersUid: [],
      quality: {},
      loading: true
    }
  },
  created: function () {
    let applicationId = this.$route.params.id
    let that = this
    this.axios.post('../../../inStock/qualitySituation', {
      applicationId: applicationId
    }).then(function (response) {
      let res = response.data.data
      console.log(res[0])
      that.quality = res[0]
      that.loading = false
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/orderQualityList/${id}`
      })
    },
    seeList: function (state) {
      window.localStorage.setItem('orderQualityState', state)
      this.$router.push({
        path: `/orderQualityList/${this.quality.application_id}`
      })
    }
  }
}
</script>
