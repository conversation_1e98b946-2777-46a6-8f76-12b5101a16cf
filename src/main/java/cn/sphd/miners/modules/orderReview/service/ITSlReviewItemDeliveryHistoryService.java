package cn.sphd.miners.modules.orderReview.service;

import cn.sphd.miners.modules.orderReview.entity.TSlReviewItemDeliveryHistory;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ITSlReviewItemDeliveryHistoryService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlReviewItemDeliveryHistory selectTSlReviewItemDeliveryHistoryById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 集合
     */
    public List<TSlReviewItemDeliveryHistory> selectTSlReviewItemDeliveryHistoryList(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory);

    /**
     * 新增
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 结果
     */
    public int insertTSlReviewItemDeliveryHistory(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory);

    /**
     * 修改
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 结果
     */
    public int updateTSlReviewItemDeliveryHistory(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTSlReviewItemDeliveryHistoryByIds(Long[] ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlReviewItemDeliveryHistoryById(Long id);
}
