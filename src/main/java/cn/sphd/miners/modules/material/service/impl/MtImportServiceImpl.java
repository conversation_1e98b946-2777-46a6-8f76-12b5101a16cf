package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MtImportService;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName MtImportServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 11:19
 * @Version 1.0
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class MtImportServiceImpl implements MtImportService {
    @Autowired
    MtCategoryDao mtCategoryDao;
    @Autowired
    MtMaterialImportLogDao mtMaterialImportLogDao;
    @Autowired
    MtUnitDao mtUnitDao;
    @Autowired
    MtBaseDao mtBaseDao;
    @Autowired
    UnitService unitService;
    @Autowired
    MtStockService stockService;


    @Override
    public List<MtBase> getMtByOid(int org) {
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL("from MtBase where org=" + org);
        List<MtBase> mtBaseList1 = new ArrayList<>();
        for (MtBase mtBase : mtBaseList) {
            MtBase mtBase1 = new MtBase();
            BeanUtils.copyProperties(mtBase, mtBase1);
            mtBase1.setCategory(null);
            mtBaseList1.add(mtBase1);
        }
        return mtBaseList1;
    }

    @Override
    public RespStatus updateFalseUserEnter(MtBase mtBase) {
        RespStatus respStatus = new RespStatus();
        List<MtUnit> mtUnitList = unitService.selectStopUnitList(mtBase.getOrg());
        MtBase m = mtBaseDao.getByHQL("from MtBase where code='" + mtBase.getCode() + "' and org=" + mtBase.getOrg());
        if (m != null) {
            respStatus.setStatus(2);
            respStatus.setName(m.getName());
        } else
            respStatus.setStatus(1);
        if (respStatus.getStatus() == 1) {
            for (MtUnit mtUnit : mtUnitList) {
                if (mtBase.getUnit().equals(mtUnit.getName())) {
                    respStatus.setStatus(3);
                }
            }
        }
        return respStatus;
    }

    @Override
    public int whetherUnfinishedImport(int org, String isPurchased) {
        int status;
        String hql = "from MtBase where org=" + org + " and source='0' and isPurchased='" + isPurchased + "'";
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL(hql);
        if (mtBaseList.isEmpty())
            status = 1;
        else
            status = 0;
        return status;
    }

    @Override
    public ReqMtObject allImportMtEnter(ReqMtObject reqMtObject) {
        ReqMtObject reqMt = new ReqMtObject();
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL("from MtBase where org=" + reqMtObject.getOrg());
        List<MtUnit> mtUnitList = unitService.selectStopUnitList(reqMtObject.getOrg());
        int trueSum = 0;
        int falseSum = 0;
        for (int i = 0; i < reqMtObject.getMtBaseList().size(); i++) {
            MtBase m = reqMtObject.getMtBaseList().get(i);
            int status = 1;
            //名称或代号未录入
            //计量单位未录入
            if (m.getName() == null || "".equals(m.getName())
                    || m.getCode() == null || "".equals(m.getCode())
                    || m.getUnit() == null || "".equals(m.getUnit())
            ) {
                status = 0;
                falseSum++;
            }
            //本次录入的代号互相重复
            if (status == 1) {
                for (int j = 0; j < reqMtObject.getMtBaseList().size(); j++) {
                    if (m.getCode().equals(reqMtObject.getMtBaseList().get(j).getCode()) && i != j) {
                        status = 0;
                        falseSum++;
                        break;
                    }
                }
            }
            //本次录入的代号,和已有代号重复
            if (status == 1) {
                for (MtBase mtBase : mtBaseList) {
                    if (mtBase.getCode().equals(m.getCode())) {
                        status = 0;
                        falseSum++;
                        break;
                    }
                }
            }
            //1.109改，新增判断，已停用计量单位判断
            if (status == 1) {
                for (MtUnit mtUnit : mtUnitList) {
                    if (mtUnit.getName().equals(m.getUnit())) {
                        status = 0;
                        falseSum++;
                        break;
                    }
                }
            }
            if (status == 1) {
                trueSum++;
            }
        }
        reqMt.setImportSum(reqMtObject.getImportSum());
        reqMt.setFalseImportSum(falseSum);
        reqMt.setTureImportSum(trueSum);
        return reqMt;
    }

    @Override
    public ReqMtObject saveImportMt(ReqMtObject reqMtObject, User user, MtCategory mtCategory) {
        ReqMtObject reqMt = new ReqMtObject();
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL("from MtBase where org=" + reqMtObject.getOrg());
        List<MtUnit> mtUnitList = unitService.selectStopUnitList(reqMtObject.getOrg());
        List<MtUnit> unitList = unitService.selectUpUnitList(reqMtObject.getOrg());
        List<MtBase> trueMtList = new ArrayList<>();
        List<MtBase> falseMtList = new ArrayList<>();
        MtMaterialImportLog mtMaterialImportLog = new MtMaterialImportLog();
        mtMaterialImportLog.setOrg(user.getOid());
        mtMaterialImportLog.setIsPurchased(Integer.valueOf(reqMtObject.getIsPurchased()));
        mtMaterialImportLog.setCreator(user.getUserID());
        mtMaterialImportLog.setCreateName(user.getUserName());
        mtMaterialImportLog.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        mtMaterialImportLog.setOperation("1");
        mtMaterialImportLog.setVersionNo(1);
        mtMaterialImportLogDao.save(mtMaterialImportLog);
        for (int i = 0; i < reqMtObject.getMtBaseList().size(); i++) {
            MtBase m = reqMtObject.getMtBaseList().get(i);
            int status = 1;
            //名称或代号未录入
            if (m.getName() == null || "".equals(m.getName()) || m.getCode() == null || "".equals(m.getCode()) || m.getUnit() == null || "".equals(m.getUnit())) {
                status = 0;
                falseMtList.add(m);
            }
            //本次录入的代号互相重复
            if (status == 1) {
                for (int j = 0; j < reqMtObject.getMtBaseList().size(); j++) {
                    if (m.getCode().equals(reqMtObject.getMtBaseList().get(j).getCode()) && i != j) {
                        status = 0;
                        falseMtList.add(m);
                        break;
                    }
                }
            }
            //本次录入的代号,和已有代号重复
            if (status == 1) {
                for (MtBase mtBase : mtBaseList) {
                    if (mtBase.getCode().equals(m.getCode())) {
                        status = 0;
                        falseMtList.add(m);
                        break;
                    }
                }
            }
            //1.109改，新增判断，已停用计量单位判断
            if (status == 1) {
                for (MtUnit mtUnit : mtUnitList) {
                    if (mtUnit.getName().equals(m.getUnit())) {
                        status = 0;
                        falseMtList.add(m);
                        break;
                    }
                }
            }
            if (status == 1) {
                if (m.getUnitId() == null) {
                    int type = 1;
                    for (MtUnit mtUnit : unitList) {
                        if (mtUnit.getName().equals(m.getUnit())) {
                            type = 0;
                            m.setUnitId(mtUnit.getId());
                            break;
                        }
                    }
                    if (type == 1) {
                        MtUnit mtUnit = new MtUnit();
                        mtUnit.setName(m.getUnit());
                        mtUnit.setOrg(reqMtObject.getOrg());
                        mtUnit.setCreator(user.getUserID());
                        mtUnit.setCreateDate(new Date());
                        mtUnit.setCreateName(user.getUserName());
                        mtUnit.setOperation("1");
                        mtUnit.setVersionNo(1);
                        mtUnit.setEnalbed(true);
                        mtUnitDao.save(mtUnit);
                        m.setUnitId(mtUnit.getId());
                        unitList.add(mtUnit);
                    }
                }
                m.setOperation("1");
                m.setCategory(mtCategory);
                m.setCategory_(mtCategory.getId());
                m.setCreator(user.getUserID());
                m.setCreateName(user.getUserName());
                m.setCreateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
                m.setIsPurchased(reqMtObject.getIsPurchased());
                m.setEnabled("0");
                m.setEnabledTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
                m.setSource("0");
                m.setVersionNo(1);
                m.setEnabledTime(new Date());
                m.setOrg(user.getOid());
                m.setImportLog(mtMaterialImportLog.getId());
                mtBaseDao.save(m);
                MtBase mtBase = new MtBase();
                BeanUtils.copyProperties(m, mtBase);
                mtBase.setCategory(null);
                trueMtList.add(mtBase);
            }
        }
        mtMaterialImportLog.setMaterialCount(reqMtObject.getImportSum());
        mtMaterialImportLog.setMaterialSuccess(trueMtList.size());
        mtMaterialImportLogDao.save(mtMaterialImportLog);
        reqMt.setImportSum(reqMtObject.getImportSum());
        reqMt.setFalseImportSum(falseMtList.size());
        reqMt.setTureImportSum(trueMtList.size());
        reqMt.setMtBaseList(trueMtList);
        return reqMt;
    }

    @Override
    public int getNoUnitNumberCountByOrg(int org, String isPurchased) {
        String hql = "from MtBase where org=" + org + " and isPurchased='" + isPurchased + "' and source='0' and unitId is null";
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL(hql);
        int count = mtBaseList.size();
        return count;
    }

    @Override
    public ReqMtObject unfinishedImportMt(String isPurchased, User user) {
        ReqMtObject reqMtObject = new ReqMtObject();
        MtMaterialImportLog mtMaterialImportLog = new MtMaterialImportLog();
        String hql = "from MtBase where org=" + user.getOid() + " and isPurchased='" + isPurchased + "' and source='0'";
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL(hql);
        List<MtBase> mtBaseList1 = new ArrayList<>();
        MtBase mb = new MtBase();
        if (mtBaseList.size() > 0) {
            mtMaterialImportLog = mtMaterialImportLogDao.get(mtBaseList.get(0).getImportLog());
            mb = mtBaseList.get(0);
        }
        StringBuilder sb = new StringBuilder();
        MtCategory c = mb.getCategory();
        sb.append(">").append(c.getName());
        while (c.getParent() != null) {
            sb.insert(0, c.getParent().getName()).insert(0, ">");
            c = c.getParent();
        }

        for (MtBase mtBase : mtBaseList) {
            MtBase mtBase1 = new MtBase();
            BeanUtils.copyProperties(mtBase, mtBase1);
            mtBase1.setCategory(null);
            mtBaseList1.add(mtBase1);
        }
        reqMtObject.setMtBaseList(mtBaseList1);
        reqMtObject.setImportSum(mtMaterialImportLog.getMaterialCount());
        reqMtObject.setTureImportSum(mtMaterialImportLog.getMaterialSuccess());
        reqMtObject.setCategory(sb.toString());
        if (reqMtObject.getCategory() != null && reqMtObject.getCategory().startsWith(">")) {
            reqMtObject.setCategory(reqMtObject.getCategory().substring(1));
        }

        return reqMtObject;
    }

    @Override
    public int finishImportMt(String isPurchased, User user, int type, Integer category) {
        int status = 0;
        int org = user.getOid();

        MtMaterialImportLog mtMaterialImportLog = new MtMaterialImportLog();
        String hql = "from MtBase where org=" + org + " and isPurchased='" + isPurchased + "' and source='0'";
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL(hql);
        if (mtBaseList.size() > 0) {
            mtMaterialImportLog = mtMaterialImportLogDao.get(mtBaseList.get(0).getImportLog());
        }
        if (type == 1) {
            MtCategory mtCategory = new MtCategory();
            if (category != null)
                mtCategory = mtCategoryDao.get(category);
            String hqlError = "from MtBase where org=" + org + " and isPurchased='" + isPurchased + "' and source='0' and unitId is null";
            List<MtBase> mtBaseListError = mtBaseDao.getListByHQL(hqlError);
            mtBaseDao.deleteAll(mtBaseListError);
            String hqlRight = "from MtBase where org=" + org + " and isPurchased='" + isPurchased + "' and source='0' and unitId is not null";
            List<MtBase> mtBaseListRight = mtBaseDao.getListByHQL(hqlRight);
            for (MtBase mtBase : mtBaseListRight) {
                mtBase.setEnabled("1");
                mtBase.setSource("3");
                if (category != null) {
                    mtBase.setCategory(mtCategory);
                    mtBase.setCategory_(category);
                }
//                mtBase.setExpRequired(expRequired);
//                mtBase.setOpenDuration(openDuration);
//                mtBase.setRelatedItem(relatedItem);
//                mtBase.setSameExpiration(sameExpiration);
//                mtBase.setExpirationDays(expirationDays);
                mtBaseDao.update(mtBase);
                BigDecimal zero = new BigDecimal(0);
                MtStockInfo stockInfo = new MtStockInfo();
                stockInfo.setCurrentStock(zero);
                stockInfo.setAvailableStock(zero);
                stockInfo.setMinimumStock(zero);
                stockInfo.setMaterial(mtBase);
                stockInfo.setCreateDate(new Date());
                stockInfo.setCreateName(user.getUserName());
                stockInfo.setCreator(user.getUserID());
                stockService.saveMtStockInfo(stockInfo);
            }
            if (mtBaseList.size() > 0) {
                mtMaterialImportLog.setMaterialSuccess(mtBaseListRight.size());
                mtMaterialImportLogDao.update(mtMaterialImportLog);
            }
            status = 1;
        } else if (type == 0) {
            mtBaseDao.deleteAll(mtBaseList);
            mtMaterialImportLogDao.delete(mtMaterialImportLog);
            status = 1;
        }
        return status;
    }

    @Override
    public RespStatus updateImportMt(MtBase mtBase) {

        RespStatus respStatus = new RespStatus();
        MtBase m = mtBaseDao.getByHQL("from MtBase where code='" + mtBase.getCode() + "' and org=" + mtBase.getOrg() + " and id!=" + mtBase.getId());
        if (m == null) {
            respStatus.setStatus(1);
            MtBase updateMt = mtBaseDao.get(mtBase.getId());
            if (mtBase.getMemo() != null)
                updateMt.setMemo(mtBase.getMemo());
            if (mtBase.getName() != null)
                updateMt.setName(mtBase.getName());
            if (mtBase.getUnitId() != null)
                updateMt.setUnitId(mtBase.getUnitId());
            if (mtBase.getUnit() != null)
                updateMt.setUnit(mtBase.getUnit());
            if (mtBase.getCode() != null)
                updateMt.setCode(mtBase.getCode());
            if (mtBase.getSpecifications() != null)
                updateMt.setSpecifications(mtBase.getSpecifications());
            if (mtBase.getModel() != null)
                updateMt.setModel(mtBase.getModel());
            if (mtBase.getExpRequired() != null)
                updateMt.setExpRequired(mtBase.getExpRequired());
            if (mtBase.getExpirationDays() != null)
                updateMt.setExpirationDays(mtBase.getExpirationDays());
            if (mtBase.getOpenDuration() != null)
                updateMt.setOpenDuration(mtBase.getOpenDuration());
            if (mtBase.getRelatedItem() != null)
                updateMt.setRelatedItem(mtBase.getRelatedItem());
            if (mtBase.getSameExpiration() != null)
                updateMt.setSameExpiration(mtBase.getSameExpiration());
            mtBaseDao.update(updateMt);
        } else {
            respStatus.setStatus(-1);
            respStatus.setName(m.getName());
        }

        return respStatus;
    }

    @Override
    public int deleteImportMt(int id, User user, String isPurchased) {
        MtBase mtBase = mtBaseDao.get(id);
        mtBaseDao.deleteById(id);
        int org = user.getOid();
        String hql = "from MtBase where org=" + org + " and isPurchased='" + isPurchased + "' and source='0'";
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL(hql);
        if (mtBaseList.isEmpty()) {
            mtMaterialImportLogDao.deleteById(mtBase.getImportLog());
        }
        return 1;
    }

    @Override
    public int finishImportMtEnter(String isPurchased, User user) {
        int org = user.getOid();
        String hql = "from MtBase where org=" + org + " and isPurchased='" + isPurchased + "' and source='0' and unitId is null";
        List<MtBase> mtBaseList = mtBaseDao.getListByHQL(hql);
        int count = mtBaseList.size();
        return count;
    }
}
