package cn.sphd.miners.modules.trainManage.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-02-03 
 */

@Entity ( name ="TrainingExam" )
@Table ( name ="t_training_exam" )
public class TrainingExam  implements Serializable {

	private static final long serialVersionUID =  8918504237753745618L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;
	/**
	 * 期数:存年月YYYYMM,增加索引,提高查询统计效率
	 */
	@Column(name = "peroid" )
	private Integer peroid;
	/**
	 * 代码
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 状态:0-未开始,1-进行中,2-补考中,3-已结束
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 补考时记原考核ID
	 */
   	@Column(name = "primary_exam" )
	private Integer primaryExam;

	/**
	 * 补考次数
	 */
   	@Column(name = "resit_times" )
	private Integer resitTimes;

	/**
	 * 是否启用
	 */
   	@Column(name = "enabled" )
	private Integer enabled;

	/**
	 * 启/停用时间
	 */
   	@Column(name = "enabled_time" )
	private Date enabledTime;

	/**
	 * 考核目标
	 */
   	@Column(name = "goal" )
	private String goal;

	/**
	 * 公示
	 */
   	@Column(name = "publicity" )
	private String publicity;

	/**
	 * 开始时间
	 */
   	@Column(name = "begin_time" )
	private Date beginTime;

	/**
	 * 截止时间
	 */
   	@Column(name = "end_time" )
	private Date endTime;

	/**
	 * 考试分钟数
	 */
   	@Column(name = "answer_duration" )
	private Integer answerDuration;

	/**
	 * 公示级次:0-不公示
	 */
	@Column(name = "publicity_ranking" )
	private Integer publicityRanking;

	/**
	 * 题库数量
	 */
   	@Column(name = "bank_num" )
	private Integer bankNum;

	/**
	 * 选择题数量
	 */
   	@Column(name = "choice_num" )
	private Integer choiceNum;

	/**
	 * 判断题数量
	 */
   	@Column(name = "tf_num" )
	private Integer tfNum;

	/**
	 * 考核人数
	 */
   	@Column(name = "user_num" )
	private Integer userNum;

	/**
	 * 一次通过人数
	 */
   	@Column(name = "one_pass_num" )
	private Integer onePassNum;

	/**
	 * 未通过人数
	 */
   	@Column(name = "fail_num" )
	private Integer failNum;

	/**
	 * 第一人开始时间
	 */
   	@Column(name = "start_time" )
	private Date startTime;

	/**
	 * 及格分
	 */
   	@Column(name = "passing_score" )
	private Integer passingScore;

	/**
	 * 平均分
	 */
   	@Column(name = "mean_score" )
	private Double meanScore;

	/**
	 * 最高分
	 */
   	@Column(name = "highest_score" )
	private Double highestScore;

	/**
	 * 最低分
	 */
   	@Column(name = "lowest_score" )
	private Double lowestScore;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getPeroid() {
		return peroid;
	}

	public void setPeroid(Integer peroid) {
		this.peroid = peroid;
	}

	public Integer getPublicityRanking() {
		return publicityRanking;
	}

	public void setPublicityRanking(Integer publicityRanking) {
		this.publicityRanking = publicityRanking;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Integer getPrimaryExam() {
		return this.primaryExam;
	}

	public void setPrimaryExam(Integer primaryExam) {
		this.primaryExam = primaryExam;
	}

	public Integer getResitTimes() {
		return this.resitTimes;
	}

	public void setResitTimes(Integer resitTimes) {
		this.resitTimes = resitTimes;
	}

	public Integer getEnabled() {
		return this.enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Date getEnabledTime() {
		return this.enabledTime;
	}

	public void setEnabledTime(Date enabledTime) {
		this.enabledTime = enabledTime;
	}

	public String getGoal() {
		return this.goal;
	}

	public void setGoal(String goal) {
		this.goal = goal;
	}

	public String getPublicity() {
		return this.publicity;
	}

	public void setPublicity(String publicity) {
		this.publicity = publicity;
	}

	public Date getBeginTime() {
		return this.beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Integer getAnswerDuration() {
		return this.answerDuration;
	}

	public void setAnswerDuration(Integer answerDuration) {
		this.answerDuration = answerDuration;
	}

	public Integer getBankNum() {
		return this.bankNum;
	}

	public void setBankNum(Integer bankNum) {
		this.bankNum = bankNum;
	}

	public Integer getChoiceNum() {
		return this.choiceNum;
	}

	public void setChoiceNum(Integer choiceNum) {
		this.choiceNum = choiceNum;
	}

	public Integer getTfNum() {
		return this.tfNum;
	}

	public void setTfNum(Integer tfNum) {
		this.tfNum = tfNum;
	}

	public Integer getUserNum() {
		return this.userNum;
	}

	public void setUserNum(Integer userNum) {
		this.userNum = userNum;
	}

	public Integer getOnePassNum() {
		return this.onePassNum;
	}

	public void setOnePassNum(Integer onePassNum) {
		this.onePassNum = onePassNum;
	}

	public Integer getFailNum() {
		return this.failNum;
	}

	public void setFailNum(Integer failNum) {
		this.failNum = failNum;
	}

	public Date getStartTime() {
		return this.startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Integer getPassingScore() {
		return this.passingScore;
	}

	public void setPassingScore(Integer passingScore) {
		this.passingScore = passingScore;
	}

	public Double getMeanScore() {
		return this.meanScore;
	}

	public void setMeanScore(Double meanScore) {
		this.meanScore = meanScore;
	}

	public Double getHighestScore() {
		return this.highestScore;
	}

	public void setHighestScore(Double highestScore) {
		this.highestScore = highestScore;
	}

	public Double getLowestScore() {
		return this.lowestScore;
	}

	public void setLowestScore(Double lowestScore) {
		this.lowestScore = lowestScore;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
