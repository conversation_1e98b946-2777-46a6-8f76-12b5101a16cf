.green{
    background-color: #36C6D3;
    color: #fff;
}
.clear{
    clear:both;
}
/*左侧*/
.tb_left{
    width: 20%;
    float:left;
    border: 1px solid #ccc;
}
.tb_left>.head,.tb_right>.tit_msg{
    padding-left:3%;
    height: 40px;
    line-height:40px;
}
.tb_left>.head{
    padding-left:10%;
    background-color:#e4e4e4;
    border-bottom: 1px solid #ccc;
    font-weight: bolder;
}
.tb_left>.cont{
    margin:5%;
    position: relative;
}
.tb_left>.cont input{
    width:100%;
    font-size: 13px;
    font-weight: 400;
    height: 34px;
    margin-bottom:10px;
}
.search{
    background: url(./icon/search.png) no-repeat ;
    background-size:30px;
    height: 30px;
    width: 30px;
    position: absolute;
    top:2px;
    right:0;
    cursor:pointer;
}
/*右侧*/
.tb_right{
    width:75%;
    float:right;
}
.tbl {
    margin: 0 auto 200px;
    border: 1px solid #ccc;
    position: relative;
}
.theader {
    position: absolute;
    top: 0;
    background: #e4e4e4;
    width: 100%;
    border-bottom: 1px solid #ccc;
    font-weight: bolder;
    box-shadow: 0 1px #ddd;
}

.margin_r {
    margin-right: -1px;
}
.ttl_control {
    border-right: 0;
    padding-left:0;
}

#son_cont{
    height: 500px;
    overflow-y: auto;
}
.fontSize{
    line-height:35px;
    display:inline-block;
}