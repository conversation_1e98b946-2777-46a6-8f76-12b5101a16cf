package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2017/11/29.
 * 人事 面试意见表
 */
@Entity
@Table(name = "t_personnel_interview")
public class PersonnelInterview implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "user" ,insertable = true)
    private Integer userId;//用户id

    @Column(name = "interviewer" )
    private Integer interviewer;//面试官ID

    @Column(name = "interviewer_name",length = 100)
    private String interviewerName;//面试官姓名

    @Column(name ="interview_time")
    private Date interviewTime;//面试时间

    @Column(name ="duty_time")
    private Date dutyTime;//可到岗日期

    @Column(name = "professional_knowledge",length = 1)
    private String professionalKnowledge;//专业知识 4-优、3-良、2-中、1-差

    @Column(name = "appearance_attitude",length = 1)
    private String appearanceAttitude;//态度仪表: 4-优、3-良、2-中、1-差

    @Column(name = "language_competence",length = 1)
    private String languageCompetence;//语言能力: 4-优、3-良、2-中、1-差

    @Column(name = "reaction_capacity",length = 1)
    private String reactionCapacity;//反应能力: 4-优、3-良、2-中、1-差

    @Column(name = "sense_trust",length = 1)
    private String senseTrust;//信赖感: 4-优、3-良、2-中、1-差

    @Column(name = "behavior_motivation",length = 1)
    private String behaviorMotivation;//应征动机: 4-优、3-良、2-中、1-差

    @Column(name = "overall_assessment",length = 255)
    private String overallAssessment;//综合评价

    @Column(name = "probation_salary")
    private float probationSalary;//试用工资

    @Column(name = "salary")
    private float salary;//转正后工资

    @Column(name = "probation_period",length = 255)
    private String probationPeriod;//试用期(月)

    @Column(name = "suggestion",length = 1)
    private String suggestion;//个人意见:1-建议试用、,2-暂缓决定,3-不建议试用

    @Column(name = "memo",length = 255)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="offer")
    private Integer offer;//招聘id

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=false, updatable=false)
    private User user;



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getInterviewer() {
        return interviewer;
    }

    public void setInterviewer(Integer interviewer) {
        this.interviewer = interviewer;
    }

    public String getInterviewerName() {
        return interviewerName;
    }

    public void setInterviewerName(String interviewerName) {
        this.interviewerName = interviewerName;
    }

    public Date getInterviewTime() {
        return interviewTime;
    }

    public void setInterviewTime(Date interviewTime) {
        this.interviewTime = interviewTime;
    }

    public Date getDutyTime() {
        return dutyTime;
    }

    public void setDutyTime(Date dutyTime) {
        this.dutyTime = dutyTime;
    }

    public String getProfessionalKnowledge() {
        return professionalKnowledge;
    }

    public void setProfessionalKnowledge(String professionalKnowledge) {
        this.professionalKnowledge = professionalKnowledge;
    }

    public String getAppearanceAttitude() {
        return appearanceAttitude;
    }

    public void setAppearanceAttitude(String appearanceAttitude) {
        this.appearanceAttitude = appearanceAttitude;
    }

    public String getLanguageCompetence() {
        return languageCompetence;
    }

    public void setLanguageCompetence(String languageCompetence) {
        this.languageCompetence = languageCompetence;
    }

    public String getReactionCapacity() {
        return reactionCapacity;
    }

    public void setReactionCapacity(String reactionCapacity) {
        this.reactionCapacity = reactionCapacity;
    }

    public String getSenseTrust() {
        return senseTrust;
    }

    public void setSenseTrust(String senseTrust) {
        this.senseTrust = senseTrust;
    }

    public String getBehaviorMotivation() {
        return behaviorMotivation;
    }

    public void setBehaviorMotivation(String behaviorMotivation) {
        this.behaviorMotivation = behaviorMotivation;
    }

    public String getOverallAssessment() {
        return overallAssessment;
    }

    public void setOverallAssessment(String overallAssessment) {
        this.overallAssessment = overallAssessment;
    }

    public float getProbationSalary() {
        return probationSalary;
    }

    public void setProbationSalary(float probationSalary) {
        this.probationSalary = probationSalary;
    }

    public float getSalary() {
        return salary;
    }

    public void setSalary(float salary) {
        this.salary = salary;
    }

    public String getProbationPeriod() {
        return probationPeriod;
    }

    public void setProbationPeriod(String probationPeriod) {
        this.probationPeriod = probationPeriod;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getOffer() {
        return offer;
    }

    public void setOffer(Integer offer) {
        this.offer = offer;
    }
}
