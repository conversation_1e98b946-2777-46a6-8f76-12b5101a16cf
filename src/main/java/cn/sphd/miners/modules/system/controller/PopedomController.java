package cn.sphd.miners.modules.system.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.initializer.SaveRpcLog;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentApplication;
import cn.sphd.miners.modules.purchaseInvoice.service.PurchaseInvoiceService;
import cn.sphd.miners.modules.sales.entity.PdModelSettings;
import cn.sphd.miners.modules.sales.model.PoOrdersPrepayment;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.PopedomDto;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * Created by Administrator on 2017/10/13.
 */
@Controller
@RequestMapping("/popedom")
public class PopedomController {

    @Autowired
    PopedomService popedomService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    AccountService accountService;
    @Autowired
    MaterielService materielService;
    @Autowired
    CodeService codeService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    RoleService roleService;
    @Autowired
    RoleTmplService roleTmplService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    PurchaseInvoiceService purchaseInvoiceService;

    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    OrgInitService orgInitService;

    /**
    * <AUTHOR>
    * @Date 2017/10/13 16:40
    * 跳转常规权限设置主页面
    */
    @RequestMapping("/toRoutinePowerSetting.do")
    public String toRoutinePowerSetting(){
        return "/authority/generalSettings";
    }

    /**
    * <AUTHOR>
    * @Date 2017/10/13 16:43
    * 跳转审批权限设置主页面
    */
    @RequestMapping("/toApprovalPowerSetting.do")
    public String toApprovalPowerSetting(User user, Model model){
//        List<ApprovalItem> approvalItems = roleService.getAllApprovalItem(user.getOid());
//        model.addAttribute("approvalItems",approvalItems);

        return "/authority/approveSettings";
    }

    /**
    * <AUTHOR>
    * @Date 2017/10/13 16:45
    * 跳转当前常规权限查看主页面
    */
    @RequestMapping("/toCurrentRoutinePower.do")
    public String toCurrentRoutinePower(){
        return "/authority/currentSettings";
    }


    /**
     * <AUTHOR>
     * @Date 2018/03/13
     * 跳转 修改记录 页面
     */
    @RequestMapping("/toUpdateLog.do")
    public String toUpdateLog(){
        return "/authority/updateLog";
    }

    /**
     * <AUTHOR>
     * @Date 2018/03/13
     * 跳转 职工权限 页面
     */
    @RequestMapping("/toWorkerAuthority.do")
    public String toWorkerAuthority(){
        return "/authority/workerAuthority";
    }

    
    /**
    * <AUTHOR>
    * @Date 2017/10/16 16:48
    * 获取常规权限设置 机构人员列表
    */
    @RequestMapping("/getUserList.do")
    public void  getUserList(User user, HttpServletResponse response) throws IOException {
        user=userService.getUserByID(user.getUserID());
        Map<String,Object> map=new HashMap<String,Object>();
        List<User>  users=new ArrayList<User>();
        if ("general".equals(user.getManagerCode())){
            users=userService.getUserByCurrentOidAndNameLocking(user.getOid(),null);//是总务高管下的，获取机构除超管浏览者外所有人
        }else {
            users=userService.getUserListByCodeOidLocking(user.getOid(),user.getManagerCode());//获取本高管权下所有人
        }
        if (popedomService.isSmallManager(user)){//是小高管
//            User manage=userService.getUserByID(user.getManager());//高管
            User manage=userService.getUserByRoleCode(user.getOid(),user.getManagerCode());
            users.remove(manage);//小高管去除自己的高管
        }
        map.put("userList",users);
        ObjectToJson.objectToJson1(map,new String[]{"parent","userMessages","personnelOccupations","personalEducations",
                "personnelSalaryLogUser","personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser",
                "userLogs","personnelReimburseHashSet","roles","organization"},response);
    }


    /**
    * <AUTHOR>
    * @Date 2017/10/23 12:00
    * 分配人对被分配人的可分配权限列表
    */
    @ResponseBody
    @RequestMapping("/getPopedomListByManager.do")
    public JsonResult getPopedomListByManager(Integer manageId,Integer userId)throws IOException {
        User manage=userService.getUserByID(manageId);
        User user=userService.getUserByID(userId);
        List<PopedomDto> popedomList= null;
        List<MyException> myExceptionList=new ArrayList<>();
        popedomList = popedomService.getUserPopedomDtoListByManager(manage,user,myExceptionList,false); //1.129独一权限 lixu 改
        if(myExceptionList.isEmpty()){
            return new JsonResult(1,popedomList);
        } else {
            MyException e=myExceptionList.get(0);
            e.printStackTrace();
            return new JsonResult(e);
        }

    }

    /**
     * <AUTHOR>
     * @Date 2017/10/23 17:33
     * 保存给被分配人的权限
     */
    @ResponseBody
    @RequestMapping("/saveUserPopedom.do")
    public void saveUserPopedom(User user,Integer userId,HttpServletResponse response,String... mid) throws IOException {
        user=userService.getUserByID(user.getUserID());
        Integer oid=user.getOid();
        Map<String,Object> map=new HashMap<String,Object>();
        if (userId!=null&&mid!=null){
            User selectUser=userService.getUserByID(userId);

//            userPopedomService.deleteUserPopedomAllBy(user,manage.getManagerCode(),true,null,mid);
            List<String> deleteMids=popedomService.getExclusiveMidsByMids(mid);// 查询变更菜单中的独一权限
            if (deleteMids.size()>0) {
                List<User> userList=userPopedomService.getUserByOidMids(oid,deleteMids);
                orgInitService.deleteUserInitByOidMid(oid, deleteMids);// 删除分配表的 之前拥有独一权限人员权限
                userPopedomService.deleteUserPopedomByOidMid(oid, deleteMids);// 删除实际表 之前拥有独一权限的人员权限
                for (User u:userList){
                    userService.getPopedomStringByUser(u,true);//变更权限时用。
                    popedomTaskService.updateUserBadgeNumber(u);//重新计算角标
                }
            }

            //1.259 查询特殊模块初始化未完成不能 给予的权限 2023/12/25
            List<String> midList=new ArrayList<>();
            List<String> delMids=orgInitService.getInitMids();  //需业务初始化的总权限
            List<String> removeMids=orgInitService.getInitMidsByOid(oid,1); // 机构业务初始化完成的 权限
            delMids.removeAll(removeMids); // 机构 业务初始化未完成，不可分配实际user的权限

            for (String m:mid){
                if (!delMids.contains(m)){
                    midList.add(m);
                }
            }

            userPopedomService.updateUserPopedoms(selectUser,user,midList.toArray(new String[midList.size()]));//更新分配的权限

            // 1.259初始化加  记录 选择的全部权限（直接生效 和 业务初始化才生效的全部权限）
            orgInitService.saveUserInits(user,selectUser,mid);

//            List<PopedomDto> popedomList= null;
//            List<MyException> myExceptionList=new ArrayList<>();
//            popedomList = popedomService.getUserPopedomDtoListByManager(manage,user,myExceptionList,false);
            userService.getPopedomStringByUser(selectUser,true);//变更权限时用。
            popedomTaskService.updateUserBadgeNumber(selectUser);//重新计算角标
            map.put("status", 1);//成功
//            map.put("popedomList",popedomList);
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     * <AUTHOR>
     * @Date 2020/12/30 8:00
     * 1.129 独一权限 分配人对被分配人的可分配 的独一权限列表
     */
    @ResponseBody
    @RequestMapping("/getExclusivePopedomsByManager.do")
    public JsonResult getExclusivePopedomsByManager(Integer manageId,Integer userId)throws IOException {
        User manage=userService.getUserByID(manageId);
        User user=userService.getUserByID(userId);
        List<PopedomDto> popedomList= null;
        List<MyException> myExceptionList=new ArrayList<>();
        popedomList = popedomService.getUserPopedomDtoListByManager(manage,user,myExceptionList,true);
        List<PopedomDto> popedomDtos=new ArrayList<>();

        // 1.339p菜单重命名   需求新要求 权限分配 看到的菜单名称 也要 重命名 展示  2025/7/8  lixu
        Map<String,String> newNameMap=popedomService.getNewNameMapByOid(manage.getOid());
        for (PopedomDto popedomDto:popedomList){
            if (newNameMap.get(popedomDto.getMid())!=null){
                popedomDto.setName(newNameMap.get(popedomDto.getMid())); //有重命名 就用重命名
            }

            if(popedomDto.getSubPopdoms().size()>0){
                popedomDtos.add(popedomDto);
            }
        }
        if(myExceptionList.isEmpty()){
            return new JsonResult(1,popedomDtos);
        } else {
            MyException e=myExceptionList.get(0);
            e.printStackTrace();
            return new JsonResult(e);
        }

    }

     /**
     * <AUTHOR>
     * @Date 2020/12/31 11:25
     * 1.129 独一权限 分配人对被分配人的可分配 的独一权限列表
      *   2021/1/5 由于需求说 独一权限和 多人权限  分配完 是一起保存，所以这个接口暂时没用了
     */
//     @ResponseBody
//     @RequestMapping("/saveExclusiveUserPopedom.do")
//     public Integer saveExclusiveUserPopedom(User user,Integer userId,String... mid) throws IOException {
//         User manage= (User) session.getAttribute("user");
//         manage=userService.getUserByID(manage.getUserID());
////         Map<String,Object> map=new HashMap<String,Object>();
//         Integer state=0;
//         if (userId!=null&&mid!=null) {
//             User user = userService.getUserByID(userId);
//             userPopedomService.deleteUserPopedomByOidMid(manage.getOid(), mid);
//             userPopedomService.updateUserPopedoms(user, manage, mid);//更新分配的权限
//             List<MyException> myExceptionList = new ArrayList<>();
//             List<PopedomDto> popedomList = popedomService.getUserPopedomDtoListByManager(manage, user, myExceptionList, false);
//             userService.getPopedomStringByUser(user, true);//变更权限时用。
//             popedomTaskService.updateUserBadgeNumber(user);//重新计算角标
//             state=1;
//         }
//         return state;
//
//     }


    /**
    * <AUTHOR>
    * @Date 2017/10/23 18:03
    * 获取已分配和未分配权限列表及对应人员
    */
    @ResponseBody
    @RequestMapping("/getOrgPopedomShow.do")
    public JsonResult getOrgPopedomShow(User user) throws IOException {
        user=userService.getUserByID(user.getUserID());
        HashMap<String,ArrayList<PopedomDto>> map= orgService.getOrgPopedomShow(user);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2018/4/9 18:08
    * 跳转查看审批设置页面
    */
    @RequestMapping("/toPowerSetting.do")
    public String toPowerSetting(User user, Model model){
        List<ApprovalItem> approvalItems = roleService.getAllApprovalItem(user.getOid());
        model.addAttribute("approvalItems",approvalItems);
        return "/authority/approveLooking";
    }

    //---------------------pc端1.81加班、iOS2.30加班审批设置、pc端1.87付款审批、pc端1.98请假审批设置、2.45安卓请假、1.100审批设置报销--------------------//
    /**
     *<AUTHOR>
     *@date 2019/9/29 19:37
     *查看审批权限设置主页面
     * 1.186机构流程优化2101 lyx/20210923
     * 1.183付款审批设置优化
     * 1.246集锦 lyx/20230131
    */
    @ResponseBody
    @RequestMapping("/approvalPowerSettingPage.do")
    public JsonResult approvalPowerSettingPage(User user){
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<ApprovalItem> approvalItemApp = roleService.getApprovalItems(oid,"overTimeApply");
        List<ApprovalItem> approvalItemPC = roleService.getApprovalItems(oid,null);
        map.put("approvalItemApp",approvalItemApp);
        map.put("approvalItemPC",approvalItemPC);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/11 10:54
     *修改审批权限设置主页面
    */
    @ResponseBody
    @RequestMapping("/updateApprovalPowerSettingPage.do")
    public JsonResult updateApprovalPowerSettingPage(User user){
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<ApprovalItem> approvalItems = roleService.getApprovalItems(oid,null);
        map.put("approvalItems",approvalItems);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/9/30 17:05
     *查看审批设置详情（1.186机构流程优化2101 lyx/20210923  1.154请假加班优化--20220314 1.246集锦--lyx/20230131 1.248加班优化-lyx/20230327）
    */
    @ResponseBody
    @RequestMapping("/getItemDetail.do")
    public JsonResult getItemDetail(String json,Integer itemId){
        Map<String,Object> map = new HashMap<>();
        Integer status = 1;  //正常的
        JSONObject jsonObject = JSONObject.parseObject(json);
        if (itemId==null) {
            itemId = jsonObject.getInteger("itemId");  //审批设置id
        }
        ApprovalItem approvalItem = roleService.getApprovalItemById(itemId);  //审批流程设置信息
        ApprovalItem supplementary = new ApprovalItem();
        ApprovalItem rule = new ApprovalItem();
        if (approvalItem!=null) {
            if ("paymentApproval".equals(approvalItem.getCode())) {  //付款中的付款复核
                ApprovalItem approvalItem1 = roleService.getApprovalItemByPreviousItem(itemId);
                User paymentAuditUser = userService.getUserByRoleCode(approvalItem.getBelongTo(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                if (paymentAuditUser != null) {
                    approvalItem1.setApproveUser(paymentAuditUser.getUserName() + " " + paymentAuditUser.getMobile());
                    map.put("approvalItem1", approvalItem1);
                } else {
                    status = 0;
                    map.put("content", "请设置财务高管");
                }
            } else if ("overTimeApply".equals(approvalItem.getCode())){   //加班中的补报加班和加班时间限制(提前量)
                supplementary = roleService.getCurrentItem(approvalItem.getBelongTo(),"supplementaryOvertime");  //加班补报
                rule = roleService.getCurrentItem(approvalItem.getBelongTo(),"overtimeRule");  //加班限制
                ApprovalItem submitRule = roleService.getCurrentItem(approvalItem.getBelongTo(),"submitOvertimeRules"); //提交实际加班时限规则
                map.put("submitRule",submitRule);
            }else if ("leaveApply".equals(approvalItem.getCode())){  //请假中的补报请假和请假时间限制(提前量)
                supplementary = roleService.getCurrentItem(approvalItem.getBelongTo(),"supplementaryLeave");  //请假补报
                rule = roleService.getCurrentItem(approvalItem.getBelongTo(),"leaveRule");  //请假限制
            }

        }

        if (approvalItem!=null&&"commodityProduct".equals(approvalItem.getCode())){  //产品基本信息的创建模式
            PdModelSettings pdModelSettings = roleService.getPdModelSettings(itemId);
            map.put("pdModelSettings",pdModelSettings);
        }else {
            List<ApprovalFlow> approvalFlows = roleService.getApprovalFlowByItemId(itemId);  //设置的审批次级
            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(itemId,10,null);  //全部的审批流程
            map.put("approvalFlows",approvalFlows);
            map.put("approvalProcessList",approvalProcessList);
        }
        map.put("approvalItem",approvalItem);
        map.put("status",status);  //1-查询成功 0-查询失败
        map.put("supplementary", supplementary);  //加班/请假补报
        map.put("rule", rule);  //加班/请假时间限制（提前量）
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/9/30 17:28
     *修改记录-1.186机构流程优化2101 lyx/20210923
    */
    @ResponseBody
    @RequestMapping("/getRecordList.do")
    public JsonResult getRecordList(String json,User user,String code){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer oid = user.getOid();  //机构id
        if(StringUtils.isEmpty(code)) {
            code = jsonObject.getString("code");  //审批设置的Code
        }
        map = roleService.getRecordList(oid,code);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/8 15:56  1.246集锦--2023/2/3改
     *修改加班、付款、请假、报销审批设置(暂时只有加班、付款、请假审批、报销审批、订单评审、材料入库检验、成品入库检验、产品基本信息的创建模式)-2021/9/23 lyx改
     * String sessionId 浏览器sessionID
     * 产品基本信息的创建模式：添加的新字段 generalModel-通用模式:1-模式1(默认),2-模式2  dedicatedModel-专用模式:1-模式1(默认),2-模式2  reviseModel-修改人模式:1-由有权限创建商品的职工修改(默认),2-由有产品操作权限的职工修改
    */
    @ResponseBody
    @RequestMapping("/updateItemApply.do")
    @MessageMapping("/updateItemApply")
    public JsonResult updateItemApply(String json,User user,String sessionid){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
//        Integer userId = user.getUserID();  //登陆人id
        Integer itemId = jsonObject.getInteger("itemId");  //审批设置id
        Date openDate = jsonObject.getDate("openDate");  //执行日期
        Integer state = jsonObject.getInteger("state");  //状态   0-不需要(自动关联),1-需要审批(检验、评审、手动关联)
        String approvalFlows = jsonObject.getString("approvalFlows"); //审批定义
        Integer paymentAuditState = jsonObject.getInteger("paymentAuditState");  //付款复核的状态 0-不需要 1-需要
        Integer generalModel = jsonObject.getString("generalModel")!=null?jsonObject.getInteger("generalModel"):null;
        Integer dedicatedModel = jsonObject.getString("dedicatedModel")!=null?jsonObject.getInteger("dedicatedModel"):null;
        Integer reviseModel = jsonObject.getString("reviseModel")!=null?jsonObject.getInteger("reviseModel"):null;
        map = roleService.updateItemApply(user,itemId,openDate,state,approvalFlows,paymentAuditState,generalModel,dedicatedModel,reviseModel);
        map.put("today",NewDateUtils.today());  //当前时间
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/updateItemApply",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/9 17:14
     *修改的查看详情
     * 参数传递方式直接添加了approvalProcessId，以后逐渐去掉json的传值--2021/9/23 lyx改
     */
    @ResponseBody
    @RequestMapping("/updateItemDetail.do")
    public JsonResult updateItemDetail(String json,Integer approvalProcessId){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId==null) {
            JSONObject jsonObject = JSONObject.parseObject(json);
            approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        }

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcessId);
        ApprovalItem approvalItem = roleService.getApprovalItemById(approvalProcess.getNewId());

        if("purchaseApprovalSettings".equals(approvalItem.getCode())){
            JSONObject obj=JSONObject.parseObject(approvalProcess.getApproveData());
            obj.put("createName",approvalProcess.getAskName());// 审批人
            obj.put("createDate",approvalProcess.getCreateDate());//申请时间
            obj.put("userName",approvalProcess.getToUserName());//审批人
            obj.put("approvalItem", approvalItem);  //修改后的审批项目
            obj.put("approveStatus",approvalProcess.getApproveStatus());// 审批状态
            obj.put("reason",approvalProcess.getReason());
            obj.put("approvalId",approvalProcess.getId());
            obj.put("approvalDate",approvalProcess.getHandleTime());
            return new JsonResult(1, obj);
        }else{
            ApprovalItem approvalItemOld = roleService.getApprovalItemById(approvalProcess.getOldId());

            if ("paymentApproval".equals(approvalItem.getCode())) {  //付款审批的要带着付款复核的内容
                ApprovalItem approvalItemAudit = roleService.getApprovalItemByPreviousItem(approvalItem.getId());  //修改后的付款复核审批项目
                ApprovalItem approvalItemOldAudit = roleService.getApprovalItemByPreviousItem(approvalItemOld.getId()); //修改前的付款复核审批项目
                User paymentAuditUser = userService.getUserByRoleCode(approvalItem.getBelongTo(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                map.put("approvalItemAudit", approvalItemAudit);  //修改后的付款复核审批项目
                map.put("approvalItemOldAudit", approvalItemOldAudit);  //修改前的付款复核审批项目
                map.put("paymentAuditUser",paymentAuditUser);   //付款复核的审批人
            }else if ("commodityProduct".equals(approvalItem.getCode())){   //产品基本信息的创建模式
                PdModelSettings pdModelSettingsNew = roleService.getPdModelSettings(approvalProcess.getNewId());
                PdModelSettings pdModelSettingsOld = roleService.getPdModelSettings(approvalProcess.getOldId());
                map.put("pdModelSettingsNew", pdModelSettingsNew);  //修改后的产品模式设置信息
                map.put("pdModelSettingsOld", pdModelSettingsOld);  //修改前的产品模式设置信息
            }
            List<ApprovalFlow> approvalFlows = roleService.getApprovalFlowByItemId(approvalProcess.getNewId());
            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(approvalItem.getId(), 10, null);  //全部的审批流程
            List<ApprovalFlow> approvalFlowsOld = roleService.getApprovalFlowByItemId(approvalProcess.getOldId());  //修改前的审批定义
            map.put("approvalItem", approvalItem);  //修改后的审批项目
            map.put("approvalItemOld", approvalItemOld);  //修改前的审批项目
            map.put("approvalFlows", approvalFlows);  //修改后的审批定义
            map.put("approvalProcessList", approvalProcessList);  //审批修改申请的审批流程
            map.put("approvalFlowsOld", approvalFlowsOld);  //修改前的审批定义
            return new JsonResult(1,map);
        }
    }

    /**
     *<AUTHOR>
     *@date 2019/10/9 17:11
     *审批设置加班/付款/请假/报销修改的审批(暂时只有加班、付款、请假审批、报销审批、订单评审、材料入库检验、成品入库检验、商品和产品关联)--20210924/lyx
    */
    @ResponseBody
    @RequestMapping("/updateOutTimeApproval.do")
    @MessageMapping("/updateOutTimeApproval")
    public JsonResult updateOutTimeApproval(String json,User user,String sessionid){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Integer approveStatus = jsonObject.getInteger("approveStatus");  //1-批准 0-驳回
        String reason = jsonObject.getString("reason");  //驳回理由
        map = roleService.updateOutTimeApproval(user,approvalProcessId,approveStatus,reason);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/updateOutTimeApproval",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/8 13:27
     *申请人待处理列表(1.186机构流程优化2101 lyx/20210923)
    */
    @ResponseBody
    @RequestMapping("/handleApply.do")
    @MessageMapping("/handleApply")
    public JsonResult handleApply(User user,String sessionid){
        Map<String,Object> map = new HashMap<>();
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByFromUser(user.getOid(),user.getUserID(),"1",10,null,null,"asc");
        map.put("approvalProcessList",approvalProcessList);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/handleApply",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/8 14:54
     *审批人待处理/已批准列表(1.186机构流程优化2101 lyx/20210923)
    */
    @ResponseBody
    @RequestMapping("/handleApproval.do")
    @MessageMapping("/handleApproval")
    public JsonResult handleApproval(User user,String sessionid){
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();  //机构id
        Integer userId = user.getUserID();  //登陆人id

        List<ApprovalProcess> approvedProcesses = new ArrayList<>();  //已批准
        boolean bicolumn = false;
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByUserId(oid,userId, "1","1", 10,null,null,"asc");
        map.put("approvalProcessList",approvalProcessList);  //待处理

        ApprovalItem approvalItem = approvalService.getApprovalItemByOidAndCode(oid, "itemApply");
        if (approvalItem.getLevel()>1) {
            for (ApprovalFlow approvalFlow:approvalItem.getApprovalFlowHashSet()) {
                if (userId.equals(approvalFlow.getToUserId())) {
                    if (approvalFlow.getLevel() < approvalItem.getApprovalFlowHashSet().size()) {
                        approvedProcesses = approvalProcessService.getApprovalProcessByUserId(oid,userId, "2","1", 10,null,null,"asc");
                        bicolumn = true;
                        break;
                    }
                }
            }
        }
        map.put("approvedProcesses", approvedProcesses);  //已批准
        map.put("bicolumn", bicolumn);  //显示待处理、已批准
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/handleApproval",null,null,null,null,JSON.toJSONString(map));  //待处理
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/9 16:05
     *申请人查询(1.186机构流程优化2101 lyx/20210923)
    */
    @ResponseBody
    @RequestMapping("/getAllItemApply.do")
    public JsonResult getAllItemApply(String json,User user){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer userId = user.getUserID();  //申请人id（登录人）
        Integer type = jsonObject.getInteger("type");  //1-近七日 2-本月 3-自定义
        String approvalStatus = jsonObject.getString("approvalStatus");   //2-已批准 3-驳回
        Date beginTime = jsonObject.getDate("beginTime");  //开始时间
        Date endTime = jsonObject.getDate("endTime");  //结束时间
        if (type!=null){
            if (type==1){   //近七日
                beginTime = NewDateUtils.changeDay(new Date(),-6);
                endTime = new Date();
            }else if (type==2){ //本月
                beginTime = NewDateUtils.changeMonth(new Date(),0);
                endTime = new Date();
            }else if (type==3){
                if (endTime.getTime()>new Date().getTime()|| endTime.getTime()==NewDateUtils.today(new Date()).getTime()){
                    endTime = new Date();
                }
            }

        }
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByFromUserId(user.getOid(),userId,approvalStatus,approvalStatus,10,beginTime,endTime,"asc");
        map.put("approvalProcessList",approvalProcessList);
        map.put("beginTime",beginTime);  //开始时间
        map.put("endTime",endTime);  //结束时间
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/9 16:07
     *审批人查询(1.186机构流程优化2101 lyx/20210923)
    */
    @ResponseBody
    @RequestMapping("/getAllItemApproval.do")
    public JsonResult getAllItemApproval(String json,User user){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer userId = user.getUserID();  //审批人id（登录人）
        Integer type = jsonObject.getInteger("type");  //1-近七日 2-本月 3-自定义
        String approvalStatus = jsonObject.getString("approvalStatus");   //2-已批准 3-驳回
        Date beginTime = jsonObject.getDate("beginTime");  //开始时间
        Date endTime = jsonObject.getDate("endTime");  //结束时间

        if (type!=null){
            if (type==1){   //近七日
                beginTime = NewDateUtils.changeDay(new Date(),-6);
                endTime = new Date();
            }else if (type==2){ //本月
                beginTime = NewDateUtils.changeMonth(new Date(),0);
                endTime = new Date();
            } if (type==3) {
                if (endTime.getTime() > new Date().getTime() || endTime.getTime()==NewDateUtils.today(new Date()).getTime()) {
                    endTime = new Date();
                }
            }
        }
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByUserId(user.getOid(),userId,approvalStatus,approvalStatus,10,beginTime,endTime,"asc");
        map.put("approvalProcessList",approvalProcessList);
        map.put("beginTime",beginTime);  //开始时间
        map.put("endTime",endTime);  //结束时间
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2017/2/10 11:30
     * 获取可选审批者列表接口
     * 此接口与/approval/getOptionalUser.do的区别是返回值格式不同，其他相同（以后多用此接口） 1.186机构流程优化2101-2021/9/23 李娅星
     */
    @ResponseBody
    @RequestMapping("/getOptionalUser.do")
    public JsonResult getOptionalUser(User user,String userName,Integer... userId) {
        Integer oid=user.getOid();
        List<User> userList = new ArrayList<User>();
        User superUser=userService.getUserByRoleCode(oid,userName,"super");
        userList.add(superUser);
        List<User> users=userService.getNotDirectLowerGrade(oid,userName,userId);
        if (userId.length==0||userId[userId.length-1]==0){
            User user1=new User();
            user1.setUserID(0);
            user1.setUserName("直接上级");
            userList.add(user1);//把直接上级 加入可选人员列表中
        }
        for (User u : users) {
            if ((u.getOrdinaryEmployees() == null || u.getOrdinaryEmployees() == 0)&& !"2".equals(u.getIsDuty())) {
                userList.add(u);
            }
        }

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("users", userList);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description
     * @Date 2021/10/29
     * @param 查询当前使用的审批设置状态
     * @param businessId-需查询的业务id  type(1-报销  2-采购申请的票据处理 3-采购申请的预付款 4-多收来的款)    code(paymentApproval-付款审批 付款复核-paymentAudit)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getCurrentItem.do")
    public JsonResult getCurrentSetting(User user,Integer businessId,Integer type,String code){
        Map<String, Object> map = new HashMap<String, Object>();
        if (businessId!=null||type!=null||StringUtils.isNotEmpty(code)){
            Integer itemId = null;
            if (type!=null) {
                if (type == 1) {
                    PersonnelReimburse personnelReimburse = personnelReimburseService.personnelReimburseById(businessId);
                    if ("paymentApproval".equals(code)) {
                        itemId = personnelReimburse.getInstance();
                    } else {
                        itemId = personnelReimburse.getAuditInstance();
                    }
                } else if (type == 2) {
                    PoPaymentApplication poPaymentApplication = purchaseInvoiceService.getApplicationById(businessId);
                    if ("paymentApproval".equals(code)) {
                        itemId = poPaymentApplication.getPayInstance();
                    } else {
                        itemId = poPaymentApplication.getAuditInstance();
                    }
                } else if (type == 3) {
                    PoOrdersPrepayment poOrdersPrepayment = purchaseInvoiceService.getPrepaymentById(businessId.longValue());
                    if ("paymentApproval".equals(code)) {
                        itemId = poOrdersPrepayment.getPayInstance();
                    } else {
                        itemId = poOrdersPrepayment.getAuditInstance();
                    }
                }else if (type==4){
                    itemId = businessId; //多收来的款传的id直接是复核审批的id
                }
            }
            ApprovalItem approvalItem = new ApprovalItem();
            if (itemId!=null){
                approvalItem = roleService.getApprovalItemById(itemId);
            }else {
                approvalItem = roleService.getCurrentItem(user.getOid(), code);  //如果是
            }
            if ("commodityProduct".equals(approvalItem.getCode())){
                PdModelSettings pdModelSettings = roleService.getPdModelSettings(approvalItem.getId());
                map.put("pdModelSettings", pdModelSettings);  //产品设置模式信息
            }
            map.put("status", approvalItem.getStatus());   //状态   0-不需要(自动关联),1-需要审批(检验、评审、手动关联)
            map.put("content", "成功");   //状态   0-不需要(自动关联),1-需要审批(检验、评审、手动关联)
        }else {
            map.put("status", null);   //状态   0-不需要(自动关联),1-需要审批(检验、评审、手动关联)
            map.put("content", "失败");
        }
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 加班/请假时间限制（提前量）修改  提交实际加班时限规则-lyx/20230327(1.248加班优化)
     * @Date 2022/3/15
     * @param ruleTime时间，-1是随时都可以(提交实际加班时限规则)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/updateRule.do")
    public JsonResult updateSupplementary(User user,Integer itemId,Double ruleTime){
        Map<String,Object> map = new HashMap<>();
        if (itemId!=null&&ruleTime!=null){
            map = roleService.updateItem(user,itemId,ruleTime,null);
        }else {
            map.put("status",0);
            map.put("content", "修改失败");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 加班/请假补报修改
     * @Date 2022/3/15
     * @param enableType 1-开启 0-关闭
     * @return
     **/
    @ResponseBody
    @RequestMapping("/updateSupplementary.do")
    public JsonResult updateSupplementary(User user,Integer itemId,Integer enableType){
        Map<String,Object> map = new HashMap<>();
        if (itemId!=null&&enableType!=null){
            Boolean enable = true;
            if (enableType==0){
                enable = false;
            }
            map = roleService.updateItem(user,itemId,null,enable);
        }else {
            map.put("status",0);
            map.put("content", "修改失败");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 获取补报加班、请假/加班、请假提前量、提交实际加班时限规则的审批设置【后续可以继续使用】
     * @Date 2022/3/15
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getItemSupplementary.do")
    public JsonResult getItemSupplementary(User user,String code){
        Map<String,Object> map = new HashMap<>();
        ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(),code);
        map.put("approvalItem",approvalItem);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/10/14 11:57
     *测试
    */
    @ResponseBody
    @RequestMapping("/test.do")
    public JsonResult test(){
        Map<String,Object> map = new HashMap<>();
        Integer result = approvalProcessService.getApprovalProcessCounts(4065,10);
//        ApprovalItem approvalItem2 = this.getCurrentItem(oid,"leaveApply");  //请假审批
        ApprovalItem approvalItem1 = roleService.getCurrentItem(2560, "leaveApply");
        map.put("approvalItem1",approvalItem1);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2023/1/4 11:00
     * 1.245 公司总览 权限菜单 主页面
     */
    @RequestMapping("/toAuthority.do")
    public String toAuthority(){
        return "/companyOverview/authority";
    }



    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/followUpdatePopedomValid.do")
    public JsonResult followUpdatePopedomValid(String json){
        return popedomService.updatePopedomValid(json);
    }
}
