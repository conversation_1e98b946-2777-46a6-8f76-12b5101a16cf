package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.material.entity.MtStockInfoHistory;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2018-01-20.
 */
public interface MtStockInfoHistoryService {
    void saveMtStockInfoHistory(MtStockInfoHistory mtStockInfoHistory);
    List<MtStockInfoHistory> getMtStockInfoHistoryListByMaterial(Integer material);
    //获取最低，初始库存记录 state 1最低，2 初始
    List<Map>  getMtSIHListByStockInfoIdForMap(Integer material,String state,Integer supplier);

    MtStockInfoHistory get(MtStockInfoHistory m);

}
