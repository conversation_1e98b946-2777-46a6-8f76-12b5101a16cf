package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdAssembleDao;
import cn.sphd.miners.modules.commodity.entity.PdAssemble;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Repository
public class PdAssembleDaoImpl extends BaseDao<PdAssemble, Serializable> implements PdAssembleDao {
    @Override
    public PdAssemble getByPdBaseId(Integer pdBaseId) {
        Map<String,Object> map = new HashMap<>();
        String hql = " from PdAssemble where product=:product ";
        map.put("product",pdBaseId);
        return (PdAssemble) getByHQLWithNamedParams(hql,map);
    }
}
