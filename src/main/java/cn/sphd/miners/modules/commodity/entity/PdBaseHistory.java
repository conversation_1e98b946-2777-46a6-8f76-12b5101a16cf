package cn.sphd.miners.modules.commodity.entity;



import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



@Entity (name="PdBaseHistory")
@Table (name="t_pd_base_history")

public class PdBaseHistory implements Serializable {


    @Column(name="product"  , nullable=true , unique=false)
    private Integer product;


    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "inner_sn", length = 50, nullable = true, unique = false)
    private String innerSn;   //内部图号

    @Column(name = "name", length = 100, nullable = true, unique = false)
    private String name;  //商品名称

    @Column(name = "code", length = 20, nullable = true, unique = false)
    private String code;   //条码

    @Column(name = "type", length = 1, nullable = true, unique = false)
    private String type; //1-商品,2-半成品/商品,3-半成品,4-外购成品,5-包装材料

    @Column(name = "model", length = 100, nullable = true, unique = false)
    private String model;    //型号

    @Column(name = "specifications", length = 255, nullable = true, unique = false)
    private String specifications;  //规格

    @Column(name = "memo", length = 255, nullable = true, unique = false)
    private String memo;  //备注

    @Column(name = "unit", length = 100, nullable = true, unique = false)
    private String unit;   //计量单位

    @Column(name = "net_weight", nullable = true, unique = false)
    private BigDecimal netWeight;  //净重(单重)

    @Column(name = "weight_unit", nullable = true, unique = false)
    private String weightUnit;  //重量单位:1-毫克(mg),2-克(g),3-千克(kg),4-吨(T)

    @Column(name = "minimumi_stock", nullable = true, unique = false)
    private Long minimumiStock;   //最小库存

    @Column(name = "current_stock", nullable = true, unique = false)
    private Long currentStock;        //bigint comment '当前库存',

    @Column(name = "available_stock", nullable = true, unique = false)
    private Long availableStock;      //bigint comment '可用库存',

    @Column(name = "initial_stock", nullable = true, unique = false)
    private Long initialStock;      //bigint comment '初始库存',

    @Column(name = "reserve_stock", nullable = true, unique = false)
    private Long reserveStock;        //bigint comment '每日预设库存',

//    @Column(name="composition"  , length=255 , nullable=true , unique=false)
//    private String composition;

    @Column(name = "creator", nullable = true, unique = false)
    private Integer creator;  //创建人id

    @Column(name = "create_name", length = 100, nullable = true, unique = false)
    private String createName;   //创建人

    @Column(name = "create_date", nullable = true, unique = false)
    private Date createDate;   //创建时间

    @Column(name = "updator", nullable = true, unique = false)
    private Integer updator;

    @Column(name = "update_name", length = 100, nullable = true, unique = false)
    private String updateName;

    @Column(name = "update_date", nullable = true, unique = false)
    private Date updateDate;

    @Column(name = "approve_Item", nullable = true, unique = false)
    private Integer approveItem;   //申批项目

    @Column(name = "approve_status", length = 1, nullable = true, unique = false)
    private String approveStatus;   //审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name = "approve_level", nullable = true, unique = false)
    private Integer approveLevel;   //审批次级

    @Column(name = "auditor", nullable = true, unique = false)
    private Integer auditor;    //审批者ID

    @Column(name = "auditor_name", length = 100, nullable = true, unique = false)
    private String auditorName;   //审批者

    @Column(name = "audit_date", nullable = true, unique = false)
    private Date auditDate;  //审批日期

    @Column(name = "operation", length = 1, nullable = true, unique = false)
    private String operation;  // 1-增,2-删,3-修改基本信息,4-修改初始库存,5-修改最小库存',

    @Column(name = "apply_memo", length = 255, nullable = true, unique = false)
    private String applyMemo;   //申请备注

    @Column(name = "approve_memo", length = 255, nullable = true, unique = false)
    private String approveMemo;   //审批备注

    @Column(name = "message_id", nullable = true, unique = false)
    private Integer messageId;   //消息ID

    @Column(name = "oid", nullable = true, unique = false)
    private Integer oid;//机构id

    @Column(name = "source", length = 1, nullable = true, unique = false)
    private String source;// 来源 1-外购,2-自制

    @Column(name = "composition", length = 1, nullable = true, unique = false)
    private String composition;//1-购买,2-制造,3-装配

    @Column(name = "stuff", length = 255, nullable = true, unique = false)
    private String stuff;//材料

    @Column(name = "assembly_quantity", nullable = true, unique = false)
    private Integer assemblyQuantity;//装配数量

    @Column(name = "version_no", nullable = true, unique = false)
    private Integer versionNo;//版本号

    @Column(name="process_dept"   , nullable=true , unique=false)
    private Integer processDept;
    @Column(name="process_dept_name"   , nullable=true , unique=false)
    private String processDeptName;
    @Column(name="phrase"   , nullable=true , unique=false)
    private String phrase;

    @Basic
    @Column(name = "resource_num")
    private Integer resourceNum; //关联内部文件个数

    @Transient
    private String stockPosition;//库存位

    @Transient
    private String categoryName;//分类名称

    //构成path;
    @Basic
    @Column(name="path"  )
    private String path;
    //构成json
    @Basic
    @Column(name="json"   )
    private String json;

    @Basic
    @Column(name = "process")
    private String process;

    //1.119新增字段 unit_id
    @Column(name="unit_id"  , nullable=true , unique=false)
    private Integer unitId;
    @Column(name="enabled" )
    private String enabled;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn(String innerSn) {
        this.innerSn = innerSn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }


    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }


    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }


    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }


    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public Long getMinimumiStock() {
        return minimumiStock;
    }

    public void setMinimumiStock(Long minimumiStock) {
        this.minimumiStock = minimumiStock;
    }


    public String getComposition() {
        return composition;
    }

    public void setComposition(String composition) {
        this.composition = composition;
    }//MP-MANAGED-UPDATABLE-ENDING

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }


    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }


    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }


    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }


    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }


    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }


    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }


    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }


    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }




    public Integer getOid() {
        return oid;
    }

    public void setOid(Integer oid) {
        this.oid = oid;
    }


    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getStuff() {
        return stuff;
    }

    public void setStuff(String stuff) {
        this.stuff = stuff;
    }


    public Integer getAssemblyQuantity() {
        return assemblyQuantity;
    }

    public void setAssemblyQuantity(Integer assemblyQuantity) {
        this.assemblyQuantity = assemblyQuantity;
    }

    public String getStockPosition() {
        return stockPosition;
    }

    public void setStockPosition(String stockPosition) {
        this.stockPosition = stockPosition;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(Long currentStock) {
        this.currentStock = currentStock;
    }

    public Long getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(Long availableStock) {
        this.availableStock = availableStock;
    }

    public Long getReserveStock() {
        return reserveStock;
    }

    public void setReserveStock(Long reserveStock) {
        this.reserveStock = reserveStock;
    }

    public Long getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(Long initialStock) {
        this.initialStock = initialStock;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public Integer getProcessDept() {
        return processDept;
    }

    public void setProcessDept(Integer processDept) {
        this.processDept = processDept;
    }

    public String getProcessDeptName() {
        return processDeptName;
    }

    public void setProcessDeptName(String processDeptName) {
        this.processDeptName = processDeptName;
    }

    public String getPhrase() {
        return phrase;
    }

    public void setPhrase(String phrase) {
        this.phrase = phrase;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getProcess() {
        return process;
    }

    public void setProcess(String process) {
        this.process = process;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Integer getResourceNum() {
        return resourceNum;
    }

    public void setResourceNum(Integer resourceNum) {
        this.resourceNum = resourceNum;
    }
}
