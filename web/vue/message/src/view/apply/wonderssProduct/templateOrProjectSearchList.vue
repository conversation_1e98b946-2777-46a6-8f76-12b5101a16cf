<template>
  <div id="templateOrProjectSearchList">
    <TY_NavTop  :title="title"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.beginDate | formatDay('YYYY年MM月DD日')}}-{{queryData.endDate | formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent">
              <span v-if="queryData.approveStatus == 2">被审批通过</span><span v-if="queryData.approveStatus == 3">被驳回</span>的{{ title }}申请
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的报销申请共如下{{list.length}}条
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item)" v-for="(item, index) in list" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_title">
                <span class="approve_name" v-html="item.description"></span>
                <p class="txt-right">{{item.userName }} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .txt-right{ padding:5px 15px; text-align: right; font-size: 0.8em; color:#999; }
  .queryCondition{ background: #fffaeb; font-size: 12px; padding: 8px; margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'templateOrProjectSearchList',
  data () {
    return {
      loading: true,
      code: '',
      title: '',
      list: [],
      paramQuery: {},
      queryData: {
        beginDate: 1560408078000,
        endDate: 1560408078000,
        approveStatus: 2,
      },
      state: ''
    }
  },
  created: function () {
    this.paramQuery = JSON.parse(localStorage.getItem('query'))
    this.getList()
  },
  methods: {
    getList: function () {
      let that = this
      this.code = this.$route.params.code
      let data = {
        'userId': that.sphdSocket.user.userID,
        'type': that.paramQuery.type,
        'beginTime': that.paramQuery.beginDate,
        'endTime': that.paramQuery.endDate,
        'approveStatus': that.paramQuery.approveStatus
      }
      that.queryData.approveStatus = that.paramQuery.approveStatus
      let url = '../../../productApproval/selectProductApprovalList.do'
      switch (this.code){
        case 'templateFoundApply':
          this.title = '模板创建'
          data.userType = 1
          data.businessType = 37
          break;
        case 'templateFoundApproval':
          this.title = '模板创建'
          data.userType = 2
          data.businessType = 37
          break
        case 'templateEditApply':
          this.title = '模板修改'
          data.userType = 1
          data.businessType = 38
          break
        case 'templateEditApproval':
          this.title = '模板修改'
          data.userType = 2
          data.businessType = 38
          break
        case 'productFoundApply':
          this.title = '产品创建'
          data.userType = 1
          data.businessType = 39
          break
        case 'productFoundApproval':
          this.title = '产品创建'
          data.userType = 2
          data.businessType = 39
          break
        case 'productEditApply':
          this.title = '产品修改'
          data.userType = 1
          data.businessType = 40
          break
        case 'productEditApproval':
          this.title = '产品修改'
          data.userType = 2
          data.businessType = 40
          break
        case 'productRecoveryApply':
          this.title = '产品恢复使用'
          data.userType = 1
          data.businessType = 41
          break
        case 'productRecoveryApproval':
          this.title = '产品恢复使用'
          data.userType = 2
          data.businessType = 41
          break
        default:
          console.log("没有匹配到类型")
      }
      console.log(url,data)
      that.axios.post(url, {
        ...data
      }).then(function (response) {
        that.loading = false
        that.list = response.data.list || []
        that.queryData.beginDate = response.data.beginTime
        that.queryData.endDate = response.data.endTime
        console.log('列表',  response)
      }).catch(function (error) {
        console.log(error)
        that.loading = false
        that.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    jump: function (item) {
      let that = this
      this.$router.push({
        path: `/templateOrProjectDetails/${that.code}/${item.id}/1`
      })
    }
  }
}
</script>
