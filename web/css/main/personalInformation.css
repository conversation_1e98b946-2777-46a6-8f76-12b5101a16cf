.bounce_Fixed2 {
    position: fixed;
    z-index: 10002;
    background: rgba(100, 100, 100, 0.5);
    padding-bottom: 200px;
}

.clear:before, .clear:after {
    display: block;
    clear: both;
    content: "";
    visibility: hidden;
    height: 0
}

.wrap-panel {
    padding: 30px 50px;
}

.panelTtl {
    margin-left: 20px;
    margin-right: 100px;
    color: #00a278;
    font-size: 16px;
    line-height: 32px;
}

.opertionList {
    padding-top: 30px;
    margin-left: 90px;
    width: 70%;
}

.agreenTip {
    margin-right: 20px;
    cursor: pointer;
}

.agreenTip i {
    margin-right: 20px;
}

.agreenTip .fa-dot-circle-o {
    color: #48cfad;
}

.baseSect {
    padding: 10px 0;
}

.baseSect li {
    float: left;
    width: 50%;
    margin-bottom: 10px;
}

.baseSect li select {
    width: 162px;
}

.baseSect li span:first-child {
    margin-right: 6px;
    display: inline-block;
    min-width: 120px;
    text-align: right;
}

.baseSect li.resetStyle {
    width: 100%;
}

.baseSect li.resetStyle input {
    width: 612px;
}

.addMoreContact {
    margin-left: 40px;
}

.editSect {
    margin: auto;
    width: 540px;
}

.editSect li {
    margin-bottom: 10px;
}

.editSect li span:first-child {
    display: inline-block;
    width: 80px;
    text-algin: right;
}

.editSect .longSize {
    width: 390px;
}

.baseSect .longSize {
    width: 510px;
}

.pickTop {
    vertical-align: top;
}

.recordMain {
    margin-left: 100px;
}

.ltTtl {
    float: left;
    margin-right: 6px;
    min-width: 140px;
    text-align: right;
}

.rtCon {
    float: left;
    width: 170px;
    min-height: 24px;
    border: 1px solid #ccc;
    padding-left: 6px;
}
.seeMoreContact .rtCon{
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
}
.rtArea {
    padding-left: 4px;
    width: 200px;
    overflow: hidden;
    min-height: 50px;
    display: inline-block;
    border: 1px solid #ccc;
}

.opertionList .ty-table td {
    text-align: left;
}

.opertionList tr td.tct {
    text-align: center;
}

.notEdit {
    margin-left: 90px;
}

.tipMessage {
    text-indent: 2em;
    width: 58%;
    margin: auto;
}

.see-btn {
    padding: 4px 6px;
}

.recordSee .ltTtl {
    text-align: center;
}

.recordSee .longSize, .recordSee .rtArea {
    width: 446px;
}

.recordSee li:before, .recordSee li:after {
    display: block;
    clear: both;
    content: "";
    visibility: hidden;
    height: 0
}

.recordSee li {
    margin-bottom: 10px;
}

.userBaseInfo {
    padding: 4px 30px;
    margin-bottom: 20px;
    background: #595959;
    overflow: hidden;
}

.userImg {
    float: left;
    width: 100px;
}

.userImg img {
    width: 100%;
    border-radius: 50%;
}

.userCon {
    float: left;
    width: 600px;
    color: #eee;
}

.baseDetail span {
    font-size: 12px;
}

.baseDetail span i{
    margin-left: 10px;
    margin-right: 10px
}

.baseDetail span:first-child {
    margin-left: 0;
}
.contectList{
    padding-bottom: 10px;
}
.contectList li {
    float: left;
    width: 33%;
    margin-bottom: 10px;
}
.contectList li .ty-left{
    max-width: 78%;
}
.contectList li .ty-left p{
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.btnSect{margin-bottom: 12px;}
.tctImg {
    float: left;
    margin-right: 10px;
    min-width: 28px;
    height: 30px;
}

.contectList .mobile {
    background: url("../../css/icon/mobile.png") no-repeat center;
    background-size: 16px;
}

.contectList .qq {  background: url("../../css/icon/QQ.png") no-repeat;
    background-size: 24px;
}

.contectList .email {
    background: url("../../css/icon/email.png") no-repeat;
    background-size: 24px;
}

.contectList .weixin {
    background: url("../../css/icon/weixin.png") no-repeat;
    background-size: 24px;
}

.contectList .weibo {
    background: url("../../css/icon/weibo.png") no-repeat;
    background-size: 24px;
}

.ttlH5 {
    color: #00a278;
}

.charact {
    margin-bottom: 6px;
    margin-left: 110px;
    overflow: hidden;
}

.mmTtl {
    float: left;
    width: 20%;
    min-width: 100px;
}

.mmCon, .timeSlot {
    float: left;
    width: 70%;
}

.mmCon span:first-child {
    margin-right: 10px;
}

.timeSlot i {
    margin: 0 10px;
}

.deleteClear {
    display: none;
    text-align: center;
}

.deleteHold {
    display: none;
    text-align: left;
    text-indent: 2em;
}

.xing:before {
    content: "*";
    width: 5px;
    height: 5px;
    display: inline-block;
    color: red;
    font-weight: bolder;
    position: relative;
}

.cansee {
    margin-left: 10px;
}

.definedCon {
    position: relative;
    margin: auto;
    text-align: center;
    width: 300px;
}

.definedCon .ttl {
    font-weight: 600;
}

.definedCon input {
    padding-right: 10px;
    width: 100%;
}

.clearLableText {
    position: absolute;
    right: 8px;
    bottom: 6px;
    cursor: pointer;
}

.otherInfo {
    border-bottom: 1px solid #ccc;
}

.canSeeTip {
    width: 100%;
}

.canSeeTip p {
    margin-left: 35px;
    color: #5c81ff;
}

.canSeeTip i {
    font-style: normal;
}

.clearKey {
    margin-left: -16px;
    background: #fff;
    color: #009fff;
    cursor: pointer;
}

.otherContactType .ty-color-red {
    padding-left: 10px;
}

.addMoreSelect option[disabled] {
    background: #ddd;
    cursor: pointer;
}
.deltedTtl{
    margin-right:30px;
}
.redFlag{
    color:red;
}
#workHashMap .areaBood,#eduHashMap .areaBood{
    border-bottom: 1px solid #ccc;
    padding: 10px 0;
}
#workHashMap .areaBood:last-child,#eduHashMap .areaBood:last-child{
    border-bottom: none;
}
#skills{
    display: none;
}
