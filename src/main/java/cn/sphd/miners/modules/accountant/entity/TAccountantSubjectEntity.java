package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import javax.persistence.Basic;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * Created by root on 17-5-4.
 */
@Alias("accountSubject")
public class TAccountantSubjectEntity implements Comparable<TAccountantSubjectEntity>{
    private Integer id;

    @Id
    @javax.persistence.Column(name = "id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    private String subject;

    @Basic
    @javax.persistence.Column(name = "subject")
    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    private Integer org;

    @Basic
    @javax.persistence.Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    private String parent;

    @Basic
    @javax.persistence.Column(name = "parent")
    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    private String name;
    private String gqName;//改前那么，修改科目的时候要生成修改前修改后的记录

    public String getGqName() {
        return gqName;
    }

    public void setGqName(String gqName) {
        this.gqName = gqName;
    }

    @Basic
    @javax.persistence.Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private Integer category;

    @Basic
    @javax.persistence.Column(name = "category")
    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    private Integer level;

    @Basic
    @javax.persistence.Column(name = "level")
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    private Integer orders;

    @Basic
    @javax.persistence.Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    private Byte state;

    @Basic
    @javax.persistence.Column(name = "state")
    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    private Integer maxChildSubjects;

    @Basic
    @javax.persistence.Column(name = "max_child_subjects")
    public Integer getMaxChildSubjects() {
        return maxChildSubjects;
    }

    public void setMaxChildSubjects(Integer maxChildSubjects) {
        this.maxChildSubjects = maxChildSubjects;
    }

    private BigDecimal initialAmount;

    @Basic
    @javax.persistence.Column(name = "initial_amount")
    public BigDecimal getInitialAmount() {
        return initialAmount;
    }

    public void setInitialAmount(BigDecimal initialAmount) {
        this.initialAmount = initialAmount;
    }

    private BigDecimal beginningBalance;

    @Basic
    @javax.persistence.Column(name = "beginning_balance")
    public BigDecimal getBeginningBalance() {
        return beginningBalance;
    }

    public void setBeginningBalance(BigDecimal beginningBalance) {
        this.beginningBalance = beginningBalance;
    }


    private BigDecimal previousBalance;

    @Basic
    @javax.persistence.Column(name = "previous_balance")
    public BigDecimal getPreviousBalance() {
        return previousBalance;
    }

    public void setPreviousBalance(BigDecimal previousBalance) {
        this.previousBalance = previousBalance;
    }

    private String previousDirection;

    public String getPreviousDirection() {
        return previousDirection;
    }

    public void setPreviousDirection(String previousDirection) {
        this.previousDirection = previousDirection;
    }

    private String carriedForwardDate;

    @Basic
    @javax.persistence.Column(name = "carried_forward_date")
    public String getCarriedForwardDate() {
        return carriedForwardDate;
    }

    public void setCarriedForwardDate(String carriedForwardDate) {
        this.carriedForwardDate = carriedForwardDate;
    }

    private BigDecimal balance;

    @Basic
    @javax.persistence.Column(name = "balance")
    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    private Double quantity;

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }


    private String summary;

    @Basic
    @javax.persistence.Column(name = "summary")
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    private BigDecimal credit;

    @Basic
    @javax.persistence.Column(name = "credit")
    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    private BigDecimal debit;

    @Basic
    @javax.persistence.Column(name = "debit")
    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    private BigDecimal creditAccumulative;

    @Basic
    @javax.persistence.Column(name = "credit_accumulative")
    public BigDecimal getCreditAccumulative() {
        return creditAccumulative;
    }

    public void setCreditAccumulative(BigDecimal creditAccumulative) {
        this.creditAccumulative = creditAccumulative;
    }

    private BigDecimal debitAccumulative;

    @Basic
    @javax.persistence.Column(name = "debit_accumulative")
    public BigDecimal getDebitAccumulative() {
        return debitAccumulative;
    }

    public void setDebitAccumulative(BigDecimal debitAccumulative) {
        this.debitAccumulative = debitAccumulative;
    }

    private String balanceDirection;

    @Basic
    @javax.persistence.Column(name = "balance_direction")
    public String getBalanceDirection() {
        return balanceDirection;
    }

    public void setBalanceDirection(String balanceDirection) {
        this.balanceDirection = balanceDirection;
    }

    public String getEndDirection() {
        return endDirection;
    }

    public void setEndDirection(String endDirection) {
        this.endDirection = endDirection;
    }

    private String endDirection;


    private String measureUnit;

    @Basic
    @javax.persistence.Column(name = "measure_unit")
    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    private String memo;

    @Basic
    @javax.persistence.Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    private Integer creator;

    @Basic
    @javax.persistence.Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    private String createName;

    @Basic
    @javax.persistence.Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    private String createDate;

    @Basic
    @javax.persistence.Column(name = "create_date")
    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    private Integer updator;

    @Basic
    @javax.persistence.Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    private String updateName;

    @Basic
    @javax.persistence.Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    private String updateDate;

    @Basic
    @javax.persistence.Column(name = "update_date")
    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    private Integer approveItem;

    @Basic
    @javax.persistence.Column(name = "approve_Item")
    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    private String approveStatus;

    @Basic
    @javax.persistence.Column(name = "approve_status")
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    private Integer approveLevel;

    @Basic
    @javax.persistence.Column(name = "approve_level")
    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    private Integer auditor;

    @Basic
    @javax.persistence.Column(name = "auditor")
    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    private String auditorName;

    @Basic
    @javax.persistence.Column(name = "auditor_name")
    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    private String auditDate;

    @Basic
    @javax.persistence.Column(name = "audit_date")
    public String getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(String auditDate) {
        this.auditDate = auditDate;
    }

    private String operation;//操作:1-增,2-删,3-改,4-启用,5-停用,6-重新建账

    @Basic
    @javax.persistence.Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    private String applyMemo;

    @Basic
    @javax.persistence.Column(name = "apply_memo")
    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    private String approveMemo;

    @Basic
    @javax.persistence.Column(name = "approve_memo")
    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }


    private Integer previousId;
    @Basic
    @javax.persistence.Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    private Integer versionNo;
    @Basic
    @javax.persistence.Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    private Integer establish;
    @Basic
    @javax.persistence.Column(name = "establish")
    public Integer getEstablish() {
        return establish;
    }

    public void setEstablish(Integer establish) {
        this.establish = establish;
    }


    private Integer messageId;

    @Basic
    @javax.persistence.Column(name = "message_id")
    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    private String categoryname;

    @Basic
    @javax.persistence.Column(name = "categoryname")
    public String getCategoryname() {
        return categoryname;
    }

    public void setCategoryname(String categoryname) {
        this.categoryname = categoryname;
    }

    private String parentName;

    @Basic
    @javax.persistence.Column(name = "parentName")
    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    private String beginningDirection;

    public String getBeginningDirection() {
        return beginningDirection;
    }

    public void setBeginningDirection(String beginningDirection) {
        this.beginningDirection = beginningDirection;
    }

    private Integer subjectId;

    public Integer getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }

    private Integer code;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    private Integer specialState;

    public Integer getSpecialState() {
        return specialState;
    }

    public void setSpecialState(Integer specialState) {
        this.specialState = specialState;
    }

    private String quantityAssistingAccounting;

    public String getQuantityAssistingAccounting() {
        return quantityAssistingAccounting;
    }

    public void setQuantityAssistingAccounting(String quantityAssistingAccounting) {
        this.quantityAssistingAccounting = quantityAssistingAccounting;
    }

    private String useable;

    public String getUseable() {
        return useable;
    }

    public void setUseable(String useable) {
        this.useable = useable;
    }

    private String relevanceType;

    public String getRelevanceType() {
        return relevanceType;
    }

    public void setRelevanceType(String relevanceType) {
        this.relevanceType = relevanceType;
    }

    private Integer relevanceItem;

    public Integer getRelevanceItem() {
        return relevanceItem;
    }

    public void setRelevanceItem(Integer relevanceItem) {
        this.relevanceItem = relevanceItem;
    }

    @Transient
    private boolean quantityDifferent=true;//余额数量差异，false，计算结果和用户输入不同，页面需要标红，true-默认值-计算结果与用户输入一致
    @Transient
    private boolean balanceDifferent=true;//期末余额差异，false，计算结果和用户输入不同，页面需要标红，true-默认值-计算结果与用户输入一致
    @Transient
    private int buildState;//建账状态，资产负债表和利润表中要用到科目余额，如果建账完成后余额来自凭证科目表中的数据，如果建账时数据来自用户输入


    public int getBuildState() {
        return buildState;
    }

    public void setBuildState(int buildState) {
        this.buildState = buildState;
    }

    public boolean isQuantityDifferent() {
        return quantityDifferent;
    }

    public void setQuantityDifferent(boolean quantityDifferent) {
        this.quantityDifferent = quantityDifferent;
    }

    public boolean isBalanceDifferent() {
        return balanceDifferent;
    }

    public void setBalanceDifferent(boolean balanceDifferent) {
        this.balanceDifferent = balanceDifferent;
    }

    private Double initialQuantity;//初始数量
    private Double beginningQuantity;//年初数量
    private Double previousQuantity;//期初数量
    private Double creditAccumulativeQuantity;//本年累计借方数量
    private Double debitAccumulativeQuantity;//本年累计贷方数量

    public Double getInitialQuantity() {
        return initialQuantity;
    }

    public void setInitialQuantity(Double initialQuantity) {
        this.initialQuantity = initialQuantity;
    }

    public Double getBeginningQuantity() {
        return beginningQuantity;
    }

    public void setBeginningQuantity(Double beginningQuantity) {
        this.beginningQuantity = beginningQuantity;
    }

    public Double getPreviousQuantity() {
        return previousQuantity;
    }

    public void setPreviousQuantity(Double previousQuantity) {
        this.previousQuantity = previousQuantity;
    }

    public Double getCreditAccumulativeQuantity() {
        return creditAccumulativeQuantity;
    }

    public void setCreditAccumulativeQuantity(Double creditAccumulativeQuantity) {
        this.creditAccumulativeQuantity = creditAccumulativeQuantity;
    }

    public Double getDebitAccumulativeQuantity() {
        return debitAccumulativeQuantity;
    }

    public void setDebitAccumulativeQuantity(Double debitAccumulativeQuantity) {
        this.debitAccumulativeQuantity = debitAccumulativeQuantity;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TAccountantSubjectEntity that = (TAccountantSubjectEntity) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (subject != null ? !subject.equals(that.subject) : that.subject != null) return false;
        if (org != null ? !org.equals(that.org) : that.org != null) return false;
        if (parent != null ? !parent.equals(that.parent) : that.parent != null) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (category != null ? !category.equals(that.category) : that.category != null) return false;
        if (level != null ? !level.equals(that.level) : that.level != null) return false;
        if (orders != null ? !orders.equals(that.orders) : that.orders != null) return false;
        if (state != null ? !state.equals(that.state) : that.state != null) return false;
        if (maxChildSubjects != null ? !maxChildSubjects.equals(that.maxChildSubjects) : that.maxChildSubjects != null)
            return false;
        if (initialAmount != null ? !initialAmount.equals(that.initialAmount) : that.initialAmount != null)
            return false;
        if (beginningBalance != null ? !beginningBalance.equals(that.beginningBalance) : that.beginningBalance != null)
            return false;
        if (previousBalance != null ? !previousBalance.equals(that.previousBalance) : that.previousBalance != null)
            return false;
        if (carriedForwardDate != null ? !carriedForwardDate.equals(that.carriedForwardDate) : that.carriedForwardDate != null)
            return false;
        if (balance != null ? !balance.equals(that.balance) : that.balance != null) return false;
        if (summary != null ? !summary.equals(that.summary) : that.summary != null) return false;
        if (credit != null ? !credit.equals(that.credit) : that.credit != null) return false;
        if (debit != null ? !debit.equals(that.debit) : that.debit != null) return false;
        if (creditAccumulative != null ? !creditAccumulative.equals(that.creditAccumulative) : that.creditAccumulative != null)
            return false;
        if (debitAccumulative != null ? !debitAccumulative.equals(that.debitAccumulative) : that.debitAccumulative != null)
            return false;
        if (balanceDirection != null ? !balanceDirection.equals(that.balanceDirection) : that.balanceDirection != null)
            return false;
        if (measureUnit != null ? !measureUnit.equals(that.measureUnit) : that.measureUnit != null) return false;
        if (memo != null ? !memo.equals(that.memo) : that.memo != null) return false;
        if (creator != null ? !creator.equals(that.creator) : that.creator != null) return false;
        if (createName != null ? !createName.equals(that.createName) : that.createName != null) return false;
        if (createDate != null ? !createDate.equals(that.createDate) : that.createDate != null) return false;
        if (updator != null ? !updator.equals(that.updator) : that.updator != null) return false;
        if (updateName != null ? !updateName.equals(that.updateName) : that.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(that.updateDate) : that.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(that.approveItem) : that.approveItem != null) return false;
        if (approveStatus != null ? !approveStatus.equals(that.approveStatus) : that.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(that.approveLevel) : that.approveLevel != null) return false;
        if (auditor != null ? !auditor.equals(that.auditor) : that.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(that.auditorName) : that.auditorName != null) return false;
        if (auditDate != null ? !auditDate.equals(that.auditDate) : that.auditDate != null) return false;
        if (operation != null ? !operation.equals(that.operation) : that.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(that.applyMemo) : that.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(that.approveMemo) : that.approveMemo != null) return false;
        if (messageId != null ? !messageId.equals(that.messageId) : that.messageId != null) return false;
        if (categoryname != null ? !categoryname.equals(that.categoryname) : that.categoryname != null) return false;
        if (parentName != null ? !parentName.equals(that.parentName) : that.parentName != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (subject != null ? subject.hashCode() : 0);
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (parent != null ? parent.hashCode() : 0);
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (category != null ? category.hashCode() : 0);
        result = 31 * result + (level != null ? level.hashCode() : 0);
        result = 31 * result + (orders != null ? orders.hashCode() : 0);
        result = 31 * result + (state != null ? state.hashCode() : 0);
        result = 31 * result + (maxChildSubjects != null ? maxChildSubjects.hashCode() : 0);
        result = 31 * result + (initialAmount != null ? initialAmount.hashCode() : 0);
        result = 31 * result + (beginningBalance != null ? beginningBalance.hashCode() : 0);
        result = 31 * result + (previousBalance != null ? previousBalance.hashCode() : 0);
        result = 31 * result + (carriedForwardDate != null ? carriedForwardDate.hashCode() : 0);
        result = 31 * result + (balance != null ? balance.hashCode() : 0);
        result = 31 * result + (summary != null ? summary.hashCode() : 0);
        result = 31 * result + (credit != null ? credit.hashCode() : 0);
        result = 31 * result + (debit != null ? debit.hashCode() : 0);
        result = 31 * result + (creditAccumulative != null ? creditAccumulative.hashCode() : 0);
        result = 31 * result + (debitAccumulative != null ? debitAccumulative.hashCode() : 0);
        result = 31 * result + (balanceDirection != null ? balanceDirection.hashCode() : 0);
        result = 31 * result + (measureUnit != null ? measureUnit.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        result = 31 * result + (categoryname != null ? categoryname.hashCode() : 0);
        result = 31 * result + (parentName != null ? parentName.hashCode() : 0);
        return result;
    }

    @Override
    public int compareTo(TAccountantSubjectEntity o) {
//        int i = Integer.parseInt(o.getSubject()) - Integer.parseInt(this.getSubject());
//        int i = Integer.parseInt(this.getSubject()) - Integer.parseInt(o.getSubject());

        long thisSubject = Long.parseLong(this.getSubject());
        long subject = Long.parseLong(o.getSubject());
        return this.getSubject().compareTo(o.getSubject());
    }
}
