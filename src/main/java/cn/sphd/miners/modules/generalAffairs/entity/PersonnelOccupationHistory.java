package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2019/10/12.
 * 工作经历历史表
 */
@Entity
@Table(name = "t_personnel_occupation_history")
public class PersonnelOccupationHistory implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "occupation")
    private Integer occupationId;//工作经历ID
    @Column(name = "user")
    private Integer userId;//用户ID
    @Column(name = "user_history")
    private Integer userHistoryId;//用户历史表ID
    @Column(name="corp_name"  , length=100 )
    private String corpName;//单位名称
    @Column(name="begin_time")
    private Date beginTime;//起始时间
    @Column(name="end_time")
    private Date endTime;//终止时间
    @Column(name="post"  , length=100)
    private String post;//岗位
    @Column(name="salary")
    private Double salary;//月薪
    @Column(name="memo"  , length=255)
    private String memo;//离职原因
    @Column(name="operating_duty"  , length=255)
    private String operatingDuty;//工作职责
    @Column(name="offer")
    private Integer offer;//招聘id
    @Column(name="corp_size"  , length=100)
    private String corpSize;//公司规模
    @Column(name="corp_nature"  , length=100)
    private String corpNature;//公司性质
    @Column(name="corp_department"  , length=100)
    private String corpDepartment;//所在部门
    @Column(name="job_desc"  , length=100)
    private String jobDesc;//工作描述
    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;
    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;
    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;
    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;
    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;
    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;
    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation; //操作:1-增,2-删,3-改
    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID
    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号
    @Column(name="version_memo"  , length=100 , nullable=true , unique=false)
    private String versionMemo; //版本说明
    @Column(name="sub_previous_id"   , nullable=true , unique=false)
    private Integer subPreviousId; //子表修改前记录ID
    @Column(name="sub_version_no"   , nullable=true , unique=false)
    private Integer subVersionNo; //子表版本号
    @Column(name="sub_version_memo"  , length=100 , nullable=true , unique=false)
    private String subVersionMemo; //子表版本说明
    @Column(name = "is_deleted")
    private boolean isDeleted=false;//逻辑删除标志:true-已删除

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOccupationId() {
        return occupationId;
    }

    public void setOccupationId(Integer occupationId) {
        this.occupationId = occupationId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getUserHistoryId() {
        return userHistoryId;
    }

    public void setUserHistoryId(Integer userHistoryId) {
        this.userHistoryId = userHistoryId;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public Double getSalary() {
        return salary;
    }

    public void setSalary(Double salary) {
        this.salary = salary;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getOperatingDuty() {
        return operatingDuty;
    }

    public void setOperatingDuty(String operatingDuty) {
        this.operatingDuty = operatingDuty;
    }

    public Integer getOffer() {
        return offer;
    }

    public void setOffer(Integer offer) {
        this.offer = offer;
    }

    public String getCorpSize() {
        return corpSize;
    }

    public void setCorpSize(String corpSize) {
        this.corpSize = corpSize;
    }

    public String getCorpNature() {
        return corpNature;
    }

    public void setCorpNature(String corpNature) {
        this.corpNature = corpNature;
    }

    public String getCorpDepartment() {
        return corpDepartment;
    }

    public void setCorpDepartment(String corpDepartment) {
        this.corpDepartment = corpDepartment;
    }

    public String getJobDesc() {
        return jobDesc;
    }

    public void setJobDesc(String jobDesc) {
        this.jobDesc = jobDesc;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getVersionMemo() {
        return versionMemo;
    }

    public void setVersionMemo(String versionMemo) {
        this.versionMemo = versionMemo;
    }

    public Integer getSubPreviousId() {
        return subPreviousId;
    }

    public void setSubPreviousId(Integer subPreviousId) {
        this.subPreviousId = subPreviousId;
    }

    public Integer getSubVersionNo() {
        return subVersionNo;
    }

    public void setSubVersionNo(Integer subVersionNo) {
        this.subVersionNo = subVersionNo;
    }

    public String getSubVersionMemo() {
        return subVersionMemo;
    }

    public void setSubVersionMemo(String subVersionMemo) {
        this.subVersionMemo = subVersionMemo;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }
}
