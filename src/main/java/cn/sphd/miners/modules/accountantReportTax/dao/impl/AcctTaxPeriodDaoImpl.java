package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodDao;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTaxPeriodDaoImpl extends BaseDao<AcctTaxPeriodEntity, Serializable> implements AcctTaxPeriodDao {

    @Autowired
    AcctTaxPeriodDao taxPeriodDao;

    /**
     * 根据历史报表ID获取时间范围list
     * */
    @Override
    public List<AcctTaxPeriodHistoryEntity> getListByReportId(Integer reportHisId) {
        String hql = "from AcctTaxPeriodHistoryEntity periodHistory where periodHistory.reportHistory=:reportHisId";
        HashMap<String, Object> param = new HashMap<>();
        param.put("reportHisId", reportHisId);
        List<AcctTaxPeriodHistoryEntity> list = taxPeriodDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public List<AcctReportEntity> getReportList(User user) {
        Integer org = user.getOid();
        String hql = "from AcctReportEntity report where report.org=:org";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        List<AcctReportEntity> list = taxPeriodDao.getListByHQLWithNamedParams(hql,param);
        for (AcctReportEntity item : list) {

            String getTaxsHql = "select tax from AcctTaxEntity tax,AcctTaxReportEntity taxreport where " +
                    "tax.org=:org and tax.id=taxreport.tax and taxreport.report=:report";

            param.put("report", item.getId());
            List<AcctTaxEntity> taxList = taxPeriodDao.getListByHQLWithNamedParams(getTaxsHql,param);
            item.setTaxList(taxList);//获取该报表对应的tax集合

            String getPeriodsHql = "from AcctTaxPeriodEntity where org=:org and report=:report";
            param.put("report", item.getId());
            List<AcctTaxPeriodEntity> periodList = taxPeriodDao.getListByHQLWithNamedParams(getPeriodsHql,param);
            item.setPeriodList(periodList);//获取该报表对应的period集合

        }
        return list;
    }

    @Override
    public List<AcctTaxPeriodEntity> getByBeginMonth(AcctTaxPeriodEntity periodEntityParam) {
        String getPeriodsHql = "from AcctTaxPeriodEntity where org=:org and report=:report and beginMonth=:beginMonth " +
                "and enabled=1 and DATE_FORMAT(createDate,'%Y-%m')<=DATE_FORMAT(:createDate,'%Y-%m')";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", periodEntityParam.getOrg());
        param.put("report", periodEntityParam.getReport());
        param.put("beginMonth", periodEntityParam.getBeginMonth());
        param.put("createDate", periodEntityParam.getCreateDate());
        List<AcctTaxPeriodEntity> periodList = taxPeriodDao.getListByHQLWithNamedParams(getPeriodsHql,param);
        return periodList;
    }

    @Override
    public AcctTaxPeriodEntity getByDate(AcctTaxPeriodEntity periodEntity) {
        String hql = "from AcctTaxPeriodEntity where org=:org and report=:report and beginMonth=:beginMonth and beginDate=:beginDate and endMonth=:endMonth and endDate=:endDate";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", periodEntity.getOrg());
        param.put("report", periodEntity.getReport());
        param.put("beginMonth", periodEntity.getBeginMonth());
        param.put("beginDate", periodEntity.getBeginDate());
        param.put("endMonth", periodEntity.getEndMonth());
        param.put("endDate", periodEntity.getEndDate());
        List<AcctTaxPeriodEntity> periodList = taxPeriodDao.getListByHQLWithNamedParams(hql,param);
        if (periodList.size() > 0)
            return periodList.get(0);
        return null;
    }


}
