package cn.sphd.miners.modules.equipment.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 装备管理_型号产品参与对象 t_equ_model_product
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public class TEquModelProduct {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 组织机构ID
     */

    private Long org;

    /**
     * 型号ID
     */

    private Long model;

    /**
     * 产品ID
     */

    private Long product;

    /**
     * 状态:0-不启用,1-启用
     */

    private Long enabled;

    /**
     * 启停用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date enabledTime;

    /**
     * 备注
     */

    private String memo;

    /**
     * 创建人id
     */

    private Long creator;

    /**
     * 创建人
     */

    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改人id
     */

    private Long updator;

    /**
     * 修改人
     */

    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 操作:1-增,2-删,3-更改,4-启用,5-停用
     */

    private Long operation;

    /**
     * 修改前记录ID
     */

    private Long previousId;

    /**
     * 版本号,每次修改+1
     */

    private Long versionNo;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOrg(Long org) {
        this.org = org;
    }

    public Long getOrg() {
        return org;
    }

    public void setModel(Long model) {
        this.model = model;
    }

    public Long getModel() {
        return model;
    }

    public void setProduct(Long product) {
        this.product = product;
    }

    public Long getProduct() {
        return product;
    }

    public void setEnabled(Long enabled) {
        this.enabled = enabled;
    }

    public Long getEnabled() {
        return enabled;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }

    public Long getUpdator() {
        return updator;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setOperation(Long operation) {
        this.operation = operation;
    }

    public Long getOperation() {
        return operation;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setVersionNo(Long versionNo) {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() {
        return versionNo;
    }

}
