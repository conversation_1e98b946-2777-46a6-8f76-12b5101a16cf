package cn.sphd.miners.modules.material.controller;

import java.util.List;

import cn.sphd.miners.modules.material.entity.TSrmSupplierImportLog;
import cn.sphd.miners.modules.material.service.ITSrmSupplierImportLogService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 供应商管理_供应商导入日志
 * 20231108 1.267供应商之批量导入Controller
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Controller
@RequestMapping("/supplier/import/log")
public class TSrmSupplierImportLogController {

    @Autowired
    private ITSrmSupplierImportLogService tSrmSupplierImportLogService;


    /**
     * 查询供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TSrmSupplierImportLog tSrmSupplierImportLog) {
        List<TSrmSupplierImportLog> list = tSrmSupplierImportLogService.selectTSrmSupplierImportLogList(tSrmSupplierImportLog);
        return list;
    }


    /**
     * 新增保存供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TSrmSupplierImportLog tSrmSupplierImportLog) {
        return toAjax(tSrmSupplierImportLogService.insertTSrmSupplierImportLog(tSrmSupplierImportLog));
    }

    /**
     * 删除供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tSrmSupplierImportLogService.deleteTSrmSupplierImportLogByIds(ids));
    }
}
