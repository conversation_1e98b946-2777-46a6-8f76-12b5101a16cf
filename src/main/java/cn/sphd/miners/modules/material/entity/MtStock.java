
package cn.sphd.miners.modules.material.entity;

import cn.sphd.miners.modules.personal.entity.ApprovalProcess;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table (name="t_mt_stock")
/**
*物料出入库表
*/
public class MtStock implements Serializable {

    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="apply_id"   , nullable=true , unique=false)
    private Integer applyId;

    @Column(name="oid"   , nullable=true , unique=false)
    private Integer oid;//组织机构ID

    @Column(name="order_id"   , nullable=true , unique=false)
    private Integer orderId;


    @Column(name="order_no"  , length=255 , nullable=true , unique=false)
    private String orderNo; 

    @Column(name="bill_no"  , length=100 , nullable=true , unique=false)
    private String billNo; 

    @Column(name="inspection_no"  , length=100 , nullable=true , unique=false)
    private String inspectionNo; 

    @Column(name="account_no"  , length=100 , nullable=true , unique=false)
    private String accountNo; 

    @Column(name="storage_no"  , length=100 , nullable=true , unique=false)
    private String storageNo; 

    @Column(name="material"   , nullable=true , unique=false)
    private Integer material; 

    @Column(name="material_name"  , length=100 , nullable=true , unique=false)
    private String materialName; 

    @Column(name="manufacture_date"   , nullable=true , unique=false)
    private Date manufactureDate; 

    @Column(name="invalid_date"   , nullable=true , unique=false)
    private Date invalidDate; 

    @Column(name="storage_location"  , length=255 , nullable=true , unique=false)
    private String storageLocation;

    @Column(name="unit_price"   , nullable=true , unique=false)
    private java.math.BigDecimal unitPrice; 

    @Column(name="in_plan"   , nullable=true , unique=false)
    private BigDecimal inPlan;

    @Column(name="in_fact"   , nullable=true , unique=false)
    private BigDecimal inFact;

    @Column(name="out_plan"   , nullable=true , unique=false)
    private BigDecimal outPlan;

    @Column(name="out_fact"   , nullable=true , unique=false)
    private BigDecimal outFact;

    @Column(name="qualified"  , length=1 , nullable=true , unique=false)
    private String qualified; 

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state; 

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo; 

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description; 

    @Column(name="source"  , length=100 , nullable=true , unique=false)
    private String source;

    @Column(name="destination"  , length=100 , nullable=true , unique=false)
    private String destination;

    @Column(name="operator"   , nullable=true , unique=false)
    private Integer operator;

    @Column(name="productId"   , nullable=true , unique=false)
    private Integer productId;

    @Column(name="operator_name"  , length=100 , nullable=true , unique=false)
    private String operatorName; 

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator; 

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName; 

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName; 

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name = "reject")
    private String reject;

    //1.80:新字段

    @Column(name = "application_in")
    private Integer applicationIn;
    @Column(name = "application_out")
    private Integer applicationOut;

    //1.316新增字段
    @Column(name = "iws_location")
    private Integer iwsLocation;

    @Transient
    private String InnerSn;//内部图号
    @Transient
    private String InnerSnName;//内部名称
    @Transient
    private String outerSn;//外部图号
    @Transient
    private String outerName;//外部名称
    @Transient
    private String unit;//单位

    @Transient
    private List<ApprovalProcess> approcessList;

    public List<ApprovalProcess> getApprocessList() {
        return approcessList;
    }

    public void setApprocessList(List<ApprovalProcess> approcessList) {
        this.approcessList = approcessList;
    }

    public Integer getProductId() {
        return productId;
    }


    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getInnerSn() {
        return InnerSn;
    }

    public void setInnerSn(String innerSn) {
        InnerSn = innerSn;
    }

    public String getInnerSnName() {
        return InnerSnName;
    }

    public void setInnerSnName(String innerSnName) {
        InnerSnName = innerSnName;
    }

    public String getOuterSn() {
        return outerSn;
    }

    public void setOuterSn(String outerSn) {
        this.outerSn = outerSn;
    }

    public String getOuterName() {
        return outerName;
    }

    public void setOuterName(String outerName) {
        this.outerName = outerName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getOid() {
        return oid;
    }

    public void setOid(Integer oid) {
        this.oid = oid;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    

    public Integer getApplyId() {
        return applyId;
    }
	
    public void setApplyId (Integer applyId) {
        this.applyId =  applyId;
    }


    public Integer getIwsLocation() {
        return iwsLocation;
    }

    public void setIwsLocation(Integer iwsLocation) {
        this.iwsLocation = iwsLocation;
    }

    public Integer getOrderId() {
        return orderId;
    }
	
    public void setOrderId (Integer orderId) {
        this.orderId =  orderId;
    }
	

    public String getOrderNo() {
        return orderNo;
    }
	
    public void setOrderNo (String orderNo) {
        this.orderNo =  orderNo;
    }
	

    public String getBillNo() {
        return billNo;
    }
	
    public void setBillNo (String billNo) {
        this.billNo =  billNo;
    }

    public String getReject() {
        return reject;
    }

    public void setReject(String reject) {
        this.reject = reject;
    }

    public Integer getApplicationIn() {
        return applicationIn;
    }

    public void setApplicationIn(Integer applicationIn) {
        this.applicationIn = applicationIn;
    }

    public Integer getApplicationOut() {
        return applicationOut;
    }

    public void setApplicationOut(Integer applicationOut) {
        this.applicationOut = applicationOut;
    }

    public String getInspectionNo() {
        return inspectionNo;
    }
	
    public void setInspectionNo (String inspectionNo) {
        this.inspectionNo =  inspectionNo;
    }
	

    public String getAccountNo() {
        return accountNo;
    }
	
    public void setAccountNo (String accountNo) {
        this.accountNo =  accountNo;
    }
	

    public String getStorageNo() {
        return storageNo;
    }
	
    public void setStorageNo (String storageNo) {
        this.storageNo =  storageNo;
    }
	

    public Integer getMaterial() {
        return material;
    }
	
    public void setMaterial (Integer material) {
        this.material =  material;
    }

    public String getMaterialName() {
        return materialName;
    }
	
    public void setMaterialName (String materialName) {
        this.materialName =  materialName;
    }
	

    public Date getManufactureDate() {
        return manufactureDate;
    }
	
    public void setManufactureDate (Date manufactureDate) {
        this.manufactureDate =  manufactureDate;
    }
	

    public Date getInvalidDate() {
        return invalidDate;
    }
	
    public void setInvalidDate (Date invalidDate) {
        this.invalidDate =  invalidDate;
    }
	

    public String getStorageLocation() {
        return storageLocation;
    }
	
    public void setStorageLocation (String storageLocation) {
        this.storageLocation =  storageLocation;
    }
	

    public java.math.BigDecimal getUnitPrice() {
        return unitPrice;
    }
	
    public void setUnitPrice (java.math.BigDecimal unitPrice) {
        this.unitPrice =  unitPrice;
    }


    public BigDecimal getInPlan() {
        return inPlan;
    }

    public void setInPlan(BigDecimal inPlan) {
        this.inPlan = inPlan;
    }

    public BigDecimal getInFact() {
        return inFact;
    }

    public void setInFact(BigDecimal inFact) {
        this.inFact = inFact;
    }

    public BigDecimal getOutPlan() {
        return outPlan;
    }

    public void setOutPlan(BigDecimal outPlan) {
        this.outPlan = outPlan;
    }

    public BigDecimal getOutFact() {
        return outFact;
    }

    public void setOutFact(BigDecimal outFact) {
        this.outFact = outFact;
    }

    public String getQualified() {
        return qualified;
    }
	
    public void setQualified (String qualified) {
        this.qualified =  qualified;
    }
	

    public String getState() {
        return state;
    }
	
    public void setState (String state) {
        this.state =  state;
    }
	

    public String getMemo() {
        return memo;
    }
	
    public void setMemo (String memo) {
        this.memo =  memo;
    }
	

    public String getDescription() {
        return description;
    }
	
    public void setDescription (String description) {
        this.description =  description;
    }

    public String getSource() {
        return source;
    }
	
    public void setSource (String source) {
        this.source =  source;
    }
	

    public String getDestination() {
        return destination;
    }
	
    public void setDestination (String destination) {
        this.destination =  destination;
    }
	

    public Integer getOperator() {
        return operator;
    }
	
    public void setOperator (Integer operator) {
        this.operator =  operator;
    }
	

    public String getOperatorName() {
        return operatorName;
    }
	
    public void setOperatorName (String operatorName) {
        this.operatorName =  operatorName;
    }
	

    public Integer getCreator() {
        return creator;
    }
	
    public void setCreator (Integer creator) {
        this.creator =  creator;
    }
	

    public String getCreateName() {
        return createName;
    }
	
    public void setCreateName (String createName) {
        this.createName =  createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }


    public Integer getUpdator() {
        return updator;
    }
	
    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }
	

    public String getUpdateName() {
        return updateName;
    }
	
    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }

}
