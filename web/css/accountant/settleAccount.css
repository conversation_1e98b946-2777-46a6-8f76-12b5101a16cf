.mainCon1{width: 977px;}
.mainCon1,.mainCon2,.mainCon3,.mainCon4,.mainCon6{padding: 0 76px;}
.settle-date{font-size: 18px;}
.vcher-mod{padding-top: 20px;border-bottom: 1px solid #ccc;}
.vcher-mod-no{padding-top: 20px;}
.row-con{margin: 2px 0 30px 0;}
.row-con::before, .row-con::after,.row-con-no::before, .row-con-no::after {  display: block;  clear: both;  content: "";  visibility: hidden;
    height: 0;  }
.left-count{width: 400px;  float: left;  line-height: 32px;}
.overSect{float: left;width: 60px;}
.gapL{margin-left: 60px;}
.careful{font-size: 12px;color: #0070c0;clear: both}
.otherYear{margin-left: 20px;}
.back-btn{margin-bottom: 30px;}
.intend2{padding-left: 38px;}
.mainCon{display: none;}

/*对公报表*/
.turnLeft .ty-table td {  text-align: left;  }
.turnLeft .ty-table thead td {  border-color: #d7d7d7;  background: #f2f2f2;  text-align: center;  }
.turnLeft .ty-table td>span[level='0']{  font-weight: bold;  }
.turnLeft .ty-table td>span[level='1']{  margin-left: 30px;  }
.turnLeft .ty-table td>span[level='2']{  margin-left: 50px;  }
.turnLeft .ty-table td>span[level='3']{  margin-left: 90px;  }
.laydate_table thead,.laydate_table tbody{  display: none;  }
.carryover{  background-color: #dff0ff;  border: 1px solid #e3e3e3;  display: inline-block;  height: 30px;
    line-height: 30px;  min-width: 182px;  }
.carryover>.carryoverType{  padding: 0 8px;  font-size: 12px;  color: #999;  }
.carryover>.carryoverType i{  font-size: 14px;  margin-right: 5px;  }
.tblContainer{margin-top: 20px;}
.mainCon6{width: 70%;}
/*账簿*/
.bookMainCon{display: flex;margin: 20px 0;}
.mainCon7{  position: relative;  min-height: 702px;  }
.mainCon7 .ty-colFileTree{  width: 300px;  }
.mainCon7 .ty-colFileTree .level1{  height: 580px;  overflow: auto;  }
.mainCon7 .ty-colFileTree .ty-treeItem{  overflow: hidden;  text-overflow: ellipsis;  }
.mainCon7 .ty-fileContent{  width:100%;  padding-left:20px;  }
.treeName{  width: 100%;  height: 40px;  line-height: 40px;  font-weight: bold;  text-align: center;
    background-color: #e9e9e9;  color: #444;  }
.subjectInfo{  font-weight: bold;  border-left: 3px solid #5d9ded;  line-height: 26px;  text-indent: 20px;
    color: #666;  margin-bottom:20px;  }
.bookMainCon .ty-table td {  text-align: left;  }
.bookMainCon .ty-table thead td {  border-color: #d7d7d7;  background: #f2f2f2;  text-align: center;  }
.mainCon7 .bookDetail {margin-left: 100px;display: inline-block;}
.mainCon5 td > div ,.mainCon8 td > div { margin: 0 -5px;height: 35px; line-height: 35px; border-bottom: 1px solid #d7d7d7;  }
.mainCon5 td > div:last-child ,.mainCon8 td > div:last-child { border-bottom: none; }
.ellipsis{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.mainCon .ty-table{ table-layout: fixed;}
.funBtn{width: 134px;height: 32px;line-height: 32px;text-align: center;border-radius: 3px;cursor: pointer;display: inline-block;
    border: none;}