package cn.sphd.miners.modules.inStockCheck.mapper;

import java.util.List;

import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting;
import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory;
import org.apache.ibatis.annotations.Param;

public interface TMtStockFlowSettingHistoryMapper {
    int deleteByPrimaryKey(Integer id);

    int deleteByPrimaryKeyIn(List<Integer> list);

    int insert(TMtStockFlowSettingHistory record);

    int insertOrUpdate(TMtStockFlowSettingHistory record);

    int insertOrUpdateSelective(TMtStockFlowSettingHistory record);

    int insertSelective(TMtStockFlowSettingHistory record);

    TMtStockFlowSettingHistory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TMtStockFlowSettingHistory record);

    int updateByPrimaryKey(TMtStockFlowSettingHistory record);

    int updateBatch(@Param("list") List<TMtStockFlowSettingHistory> list);

    int updateBatchUseMultiQuery(@Param("list") List<TMtStockFlowSettingHistory> list);

    int updateBatchSelective(@Param("list") List<TMtStockFlowSettingHistory> list);

    int batchInsert(@Param("list") List<TMtStockFlowSettingHistory> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<TMtStockFlowSettingHistory> list);

    List<TMtStockFlowSettingHistory> selectBySettingId(Integer id);

    TMtStockFlowSetting selectByValue(Integer id);

}