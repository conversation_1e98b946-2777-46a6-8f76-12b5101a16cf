.ty-alert{
    font-size: 14px;
}
.ty-circle-2{
    border-radius: 2px;
}
.kj-input, .kj-select{
    height: 32px;
    border:1px solid #f0f0f0;
    color: #666;
    padding:0 8px;
    min-width: 167px;
    background: inherit;
}
.kj-input-blue, .kj-select-blue{
    border-color: #5d9cec;
}
.kj-input:disabled, .kj-select:disabled, .kj-textarea:disabled{
    border-color: #ccc;
    background-color: #f0f0f0;
    cursor: default;
    color: #aaa;
}
.bonceCon input.kj-input:disabled, .bonceCon .kj-select:disabled, .bonceCon .kj-textarea:disabled{
    border-color: #ccc;
    background-color: #f0f0f0;
    cursor: default;
    color: #aaa;
}
.btn-group{
    flex: auto;
    position: relative;
}
.item{
    display: flex;
    padding: 4px;
}
.item .item_content{
    width: 300px;
    line-height:20px;
    color: #666;
}
.ty-panel{
    padding:4px 0;
    margin-bottom: 8px;
    font-size: 13px;
    background: none;
}
.ty-panel.short{
    width: 800px;
}
.ty-panel .ty-alert{
    font-size: 13px;
    padding: 0;
}
.ty-panel .ty-panel-title{
    display: flex;
    padding: 4px 8px;
    font-size: 14px;
}
.ty-panel .ty-panel-title .title{
    display: inline-block;
    padding:2px 0;
    line-height:18px;
    border-bottom: 2px solid #0b94ea;
    font-weight: bold;
}
.ty-panel .ty-panel-content{
    padding: 4px 8px;
}
.kj-table .markRed{
    color: #ed5565;
    cursor: pointer;
}
.short{
    width: 64px;
    min-width: 0;
    margin-right: 8px;
}
.bounceItem .bounceItem_title {
    width: 100px;
}
.kj-panel{
    border-top:1px solid #ddd;
}
#editRoutineWork .kj-panel{
    display: none;
    border: none;
}
#editInnerWork .kj-panel{
    display: none;
    border: none;
}
#editDailyWork .kj-panel{
    display: none;
    border: none;
}
.kj-hr{
    margin: 8px 0;
    border-bottom: 1px solid #eee;
}
.leave{
    display: flex;
}
.leaveList button{
    margin-bottom: 4px;
}
.main{
    padding: 4px 8px;
}
.thisMonth{
    margin-top: 16px;
}
.btn-input{
    border-bottom: 1px solid #eee;
}
.kj-table.text-left th, .kj-table.text-left tbody td{
    text-align: left;
    padding:8px;
}
.day_avatar{
    position: relative;
    font-size: 16px;
}
.day_avatar .work_state{
    height: 20px;
    position: absolute;
    right: 8px;
    top: 0;
    text-align: right;
}
.day_avatar .work_state .workState_item{
    display: inline-block;
    padding: 0 4px;
    line-height: 19px;
    text-align: center;
    background-color: #eee;
    color: #8f9299;
    font-size: 12px;
    margin-left: 2px;
    border-radius: 2px;
}
.day_avatar .work_state .workState_item.red{
    background-color: #ed5565;
    color: #fff;
}
.work_dayNo{
    display: inline-block;
    height: 48px;
    line-height: 48px;
    min-width: 48px;
}
.work_dayNo.red{
    color: #ed5565;
}
.work_dayNo.black{
    color: #222;
}
.ty-form{
    overflow: hidden;
    margin: auto;
}
.formItem{
    margin-top: 8px;
    overflow: hidden;
}
.formItem .form_label{
    float:left;
    margin-right:6px;
    vertical-align: top;
    width:60px;
    height:34px;
    line-height:34px;
}
.formItem .form_other{
    float:left;
    margin-right:4px;
    line-height:34px;
}
.formItem .small{
    width:100px;
}
.formItem .mid{
    width:150px;
}
.form_content{
    float: left;
    padding-left:12px;
    margin-right:30px;
    border:1px solid #ccc;
    width:383px;
    min-height:32px;
    line-height:32px;
}
.formItem input[type="text"], .formItem textarea{
    width: 100%;
}
.formItem select{}
.formItem input[type="radio"]{
    margin-left:80px;
}
.formItem .text-justify::after {
    display: inline;
}
.formItem pre{
    min-height:50px;
    max-height: 200px;
    overflow-y: auto;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    line-height: 1.42857;
    color: #34495e;
    background: inherit;
    word-wrap: break-word;
    white-space: pre-wrap;
}
.nowDay .work_dayNo{
    font-size: 20px;
    border-radius: 50%;
    border: 2px solid #48cfad;
    line-height: 44px;
}
.bounceItem_content textarea.autoFill{
    width: 100%;
}
.ty-title{
    font-size: 18px;
    color: #333;
}
.queryItem {
    display: inline-block;
    width: 666px;
}
/*review*/
.review-item-avatar{
    font-size: 13px;
}
.review-item-avatar .review-avatar{
}
.review-item-avatar .reply-avatar{
    margin-left: 48px;
    background-color: #edf2f7;
    padding: 4px 8px;
    color: #5d636a;
}

.review-item{
    padding: 4px 8px;
}
.review-item .review-title{
    width: 100%;
    box-sizing: border-box;
    border-radius: 2px;
    position: relative;
    font-size: 13px;
    overflow: hidden;
    opacity: 1;
    display: flex;
    align-items: center;
    transition: opacity .2s;
    color: #777
}
.review-item .review-content{
    color: #4a5268;
}
.review-item-avatar .reply-avatar .review-content{
    color: #5e626a;
}
.bonceCon .autoFill{
    width: 100%;
}
.bounceItem{
    display: flex;
    padding: 4px 0;
}
.bounceItem .bounceItem_title{
    width: 90px;
    flex: none;
    color: #666;
    line-height:32px;
}
.bounceItem .bounceItem_content{
    margin-bottom: 4px;
    flex: auto;
    line-height:32px;
}
.bounceItem .bounceItem_content .ty-checkbox{
    border-radius: 3px;
    cursor: default;
}
.bounceItem .bounceItem_content .ty-checkbox:hover, .bounceItem .bounceItem_content .ty-checkbox.checked{
    background: #daebff;
}
.bounceItem .bounceItem_content .ty-radio, .bounceItem .bounceItem_content .ty-checkbox {
    padding: 0;
    width: 200px;
    margin-right: 24px;
}
.bounceItem .bounceItem_content input + label{
    left: 0;
}
.page{
    padding: 4px 8px;
}
td.rest{  color:#ed5565;}
.holiday_sign{  position: absolute;  left: 2px;  top: 2px;  display: block;  width: 15px;  height: 15px;  color: #fff;  text-align: left;
    text-indent: 1px;  line-height: 14px;  overflow: hidden;  }
td.rest .holiday_sign{  background: #f43;  }
td.work .holiday_sign{  background: #969799;  }
td.active .fa-minus{ background: #969799; color: #fff; }
.clendars table td{  text-align:center;  height:40px;  line-height: 40px;  padding:0;  }
.timeinput{ width:250px; border: 1px solid #48cfad!important; ;  }
.clendars table thead td{  color: #48cfad; font-weight: bold;  }
.clendars td[disabled]{ background:#e6e4e4; color:#666;  }
.clendars table{ display: none; }
.clendars table:first-child{ display: table; }
.day-rel{  position:relative;  }
.defineColor{  background-color: #9beae4;  }
.otherSetting{  margin-top:20px;  }
.detailsHead{  margin-bottom:12px;  }
.compareSect{  margin:30px 0;  padding-top:10px;  border-top:1px solid #ccc;  }
.sect-title{  width:100px;  text-align:right;  position: absolute;  left: 0;  }
.detCon{  width:45%;  }
.detCon ul li{  position: relative;  line-height:34px;  padding-left: 112px;  }
.editAfter{  background: rgba(254, 17, 24, 0.53);  position:relative;  }
.editAfter .flag_arrow{  position: absolute;  left: -77px;  color: rgba(254, 17, 24, 0.53);  display: inline-block;  width: 55px;  font-size: 52px;  bottom: 16px;  line-height: 0px;  }
.atTip{ font-size:0.6em ; color:#aaa;   }
.atTip>span{ color: red;   }
.panel-review-item{
    border-top: 1px solid #dbe4ec;
}
.panel-review-item:first-child{
    border:none
}
#seeAllReviewBtn{
    margin-left: 16px;
}
.blueFont{color: #0070bf;}
