<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showBack=true :showClose=true
                  @mycloseFun="handleCustomClose"
      ></PageHeader>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14 " v-if="bmRecordDetail.itemIndex>0">
      <p>以下为第{{ bmRecordDetail.itemIndex }}次修改后数据，其中本次修改的被标为了<span style="color:#ed5565;">红色</span></p>
      <p> {{ bmRecordDetail.createName }} <span class="padding-0-10">{{ new
      Date(bmRecordDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>修改</p>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14" v-else>
      <p>以下为原始信息</p>
      <p> {{ bmRecordDetail.createName }} <span class="padding-0-10">{{ new
      Date(bmRecordDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>创建</p>
    </div>
    <div class="re margin_top_16" v-if="info">
      <div class="padding-0-15 ty-fff">
        <p>
          <span>本金金额</span>
          <span v-html="info.principalAmount" class="hH"></span>
        </p>
        <p>
          <span>收款方</span>
          <span style="width: 70%;text-align: right" v-html="info.borrower" class="hH"></span>
        </p>
        <p>
          <span>本金型式</span>
          <span v-html="info.incomeMethod" class="hH"></span>
        </p>
        <p v-if="info.incomeMethodType==='1'||info.incomeMethodType==='5'">
          <span>付款日期</span>
          <span v-html="info.paymentDate" class="hH"></span>
        </p>
        <div v-if="info.incomeMethodType==='5'">
          <p>
            <span>付款银行</span>
            <span v-html="info.receiveBank" class="hH"></span>
          </p>
          <p>
            <span>付款经手人</span>
            <span v-html="info.operatorName" class="hH"></span>
          </p>
          <p>
            <span>借款方账号名称</span>
            <span v-html="info.oppositeAccount" class="hH"></span>
          </p>
          <p>
            <span>开户行</span>
            <span v-html="info.oppositeBankno" class="hH"></span>
          </p>
          <p>
            <span>账号</span>
            <span v-html="info.oppositeBankcode" class="hH"></span>
          </p>
        </div>
        <div v-else-if="info.incomeMethodType === '3'|| info.incomeMethodType==='4'">
          <p v-if="info.withinOrAbroad==='1'">
            <span>银行账户</span>
            <span v-html="info.account" class="hH"></span>
          </p>
          <p>
            <span>{{info.incomeMethodType === '3' ? '支' : info.incomeMethodType === '4' ? '汇' : 'err'}}票号</span>
            <span v-html="info.returnNo"></span>
          </p>
          <p v-if="info.withinOrAbroad==='1'">
            <span>支票到期日</span>
            <span v-html="info.expireDate"></span>
          </p>
          <p>
            <span>付款日期</span>
            <span class="hH" v-html="info.paymentDate"></span>
          </p>
        </div>

        <div v-if="info.incomeMethodType!=='5'">
          <p>
            <span>付款经手人</span>
            <span v-html="info.operatorName" class="hH"></span>
          </p>
          <p>
            <span>收款经手人</span>
            <span class="hH" v-html="info.partnerName"></span>
          </p>
        </div>
        <p>
          <span>应归还本金的日期</span>
          <span v-html="info.repaymentDate"></span>
        </p>
        <p>
          <span>名义利率</span>
          <span v-html="info.nominalRate"></span>
        </p>
        <p>
          <span>利息的支付方式</span>
          <span v-html="info.interestMethodStr"></span>
        </p>
        <div v-if="info.interestMethod!=='0'">
          <p>
            <span>开始计息日期</span>
            <span v-html="info.interestAccrualDate"></span>
          </p>
          <p>
            <span>每{{ info.interestMethod === '1' ? '日' : info.interestMethod === '2' ? '月' : info.interestMethod === '3' ? '年' : '错误'
              }}还款日</span>
            <span v-html="info.periodRepaymentDate"></span>
          </p>
          <p>
            <span>每次应付款金额</span>
            <span v-html="info.periodRepaymentAmount"></span>
          </p>
        </div>
        <p>
          <span>备注</span>
          <span class="hH" style="width: 70%" v-html="info.memo"></span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent} from "vue";
import finance from '@/mixins/finance.js'

export default defineComponent({
  name: "bmRecordDetail",
  mixins: [finance],
  computed: {
    itemIndex() {
      return this.$route.query.itemIndex
    },
  },
  data() {
    return {
      tit: "财会",
      list: {},
      info: null,
      bmRecordDetail: {}
    }
  },
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    compareWithUnit(before, after, unit = '') {
      const formattedBefore = this.formatCurrency(before) + unit
      const formattedAfter = this.formatCurrency(after) + unit
      return this.compareTxt(formattedBefore, formattedAfter)
    },
    chargePay(type,withinOrAbroad) {
      type = String(type)
      switch (type) {
        case '1':
          return '现金';
          break;
        case '2':
          return '现金支票';
          break;
        case '3':
          if(withinOrAbroad==0){
            return '外部转账支票';
          } else{
            return '内部转账支票';
          }
          break;
        case '4':
          return '承兑汇票';
          break;
        case '5':
          return '银行转账';
          break;
      }
    },
    getEditHisDetails(ID) {
      let params = {tBorrowCommonHistoryId: ID}
      this.financeApi.getLoanHistoryDetailCompare(params).then(res1 => {
        let res = res1.data.data
        let loanHistoryDetailCompare = res.loanHistoryDetailCompare
        let after = loanHistoryDetailCompare.after
        let before = loanHistoryDetailCompare.before
        console.log('after', after)
        console.log('before', before)
        let newInfo = {}
        if (before) {
          newInfo = {
            principalAmount: this.compareWithUnit(before.principalAmount, after.principalAmount, '元'),
            borrower: this.compareTxt(before.borrower, after.borrower),
            incomeMethodType:after.incomeMethod,
            withinOrAbroad:before.withinOrAbroad,
            incomeMethod: this.chargePay(after.incomeMethod,before.withinOrAbroad),
            paymentDate: this.compareTxt((new Date(before.paymentDate).format('yyyy-MM-dd')), (new Date(after.paymentDate).format('yyyy-MM-dd'))),
            receiveBank: this.compareTxt(before.receiveBank, after.receiveBank),
            operatorName: this.compareTxt(before.operatorName, after.operatorName),
            oppositeBankno: this.compareTxt(before.oppositeBankno, after.oppositeBankno),
            oppositeBankcode: this.compareTxt(before.oppositeBankcode, after.oppositeBankcode),
            interestMethod: after.interestMethod,
            interestMethodStr: this.compareTxt(this.chargeInterestMethod(before.interestMethod), this.chargeInterestMethod(after.interestMethod)),
            repaymentDate: this.compareTxt((before.repaymentDate ? (new Date(before.repaymentDate).format('yyyy-MM-dd')) : '未约定具体日期'), (after.repaymentDate ? (new Date(after.repaymentDate).format('yyyy-MM-dd')) : '未约定具体日期')),
            nominalRate: this.compareWithUnit(((before.nominalRate * 100).toFixed(2)), ((after.nominalRate * 100).toFixed(2)), '%'),
            memo: this.compareTxt(before.memo, after.memo),
            oppositeAccount:this.compareTxt(before.oppositeAccount, after.oppositeAccount),
            periodRepaymentAmount: this.compareWithUnit(before.periodRepaymentAmount, after.periodRepaymentAmount, '元'),
            periodRepaymentDate: this.compareTxt((before.periodRepaymentDate), (after.periodRepaymentDate)),
            interestAccrualDate: this.compareTxt(new Date(before.interestAccrualDate).format('yyyy-MM-dd'), new Date(after.interestAccrualDate).format('yyyy-MM-dd')),
            partnerName: this.compareTxt(before.partnerName, after.partnerName),
            account:this.compareTxt(before.account, after.account),
            billEndDate:this.compareTxt((new Date(before.billEndDate).format('yyyy-MM-dd')), (new Date(after.billEndDate).format('yyyy-MM-dd'))),
            returnNo: this.compareTxt(before.returnNo, after.returnNo),
            expireDate: this.compareTxt((new Date(before.expireDate).format('yyyy-MM-dd')), (new Date(after.expireDate).format('yyyy-MM-dd'))),
          }
        } else {
          newInfo = {
            operatorName: after.operatorName,
            oppositeBankno: after.oppositeBankno,
            oppositeBankcode: after.oppositeBankcode,
            incomeMethod: this.chargePay(after.incomeMethod,after.withinOrAbroad),
            oppositeAccount:after.oppositeAccount,
            account:after.account,
            incomeMethodType: after.incomeMethod,
            withinOrAbroad: after.withinOrAbroad,
            expireDate: (new Date(after.expireDate).format('yyyy-MM-dd')),
            returnNo: after.returnNo,
            receiveBank: after.receiveBank,
            principalAmount: `${this.formatCurrency(after.principalAmount)}元`,
            borrower: after.borrower,
            paymentDate: new Date(after.paymentDate).format('yyyy-MM-dd'),
            partnerName: after.partnerName,
            repaymentDate: after.repaymentDate ? (new Date(after.repaymentDate).format('yyyy-MM-dd')) : '未约定具体日期',
            nominalRate: (after.nominalRate * 100).toFixed(2) + '%',
            interestMethodStr: this.chargeInterestMethod(after.interestMethod),
            interestMethod: after.interestMethod,
            memo: after.memo,
            interestAccrualDate: new Date(after.interestAccrualDate).format('yyyy-MM-dd'),
            periodRepaymentDate: after.periodRepaymentDate,
            periodRepaymentAmount: `${this.formatCurrency(after.periodRepaymentAmount)}元`,
          }
        }
        this.info = newInfo
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('getEditHisDetails链接失败，请重试！')
      })
    }
  },
  mounted() {
    let bmRecordDetail = localStorage.getItem('bmRecordDetail')
    this.bmRecordDetail = JSON.parse(bmRecordDetail)
    // console.log('bmRecordDetail=', this.bmRecordDetail)
    this.getEditHisDetails(this.bmRecordDetail.id)
  },

})
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";


.re {
  position: relative;
  top: 40px;
  line-height: 30px;

  > p {
    display: flex;
    justify-content: space-between;
    padding: 2px 0;
  }

  > div {
    > p, > div > p {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
      align-items: center;
    }
  }
}

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.hH {
  width: 70%;
  text-align: end;
  line-height: 22px;
}

</style>