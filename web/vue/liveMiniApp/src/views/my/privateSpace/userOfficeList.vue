<template>
  <!-- 小程序》我的 账号信息 -->
  <div class="userOfficeList">
    <Pageheader title="可登录的工作室" :showBack="true"/>
    <div class="live-panel">
      <div class="live-tr litF">
        当前，账号XXXXXXXXXXX可登录的机构有如下XX个
      </div>
    </div>
    <div class="live-panel">
      <el-table :data="tableData" stripe style="width: 100%">
        <el-table-column prop="name" label="机构简称" > </el-table-column>
        <el-table-column prop="date" label="录入至该机构的时间"> </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<style lang="scss" scoped>
.litF{ font-size: 0.9em;  }
</style>

<script setup>
// import { ref } from 'vue'
import JSONBig from 'json-bigint'
import auth from "../../../sys/auth";

let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
console.log(' let jsonData = ', jsonData)
const org = auth.getOrg()
const user = auth.getUser()
const officeList = jsonData.officeList
let tableData = []
officeList.forEach(off => {
  tableData.push({
    name: off.name,
    date: new Date(off.createDate).format("yyyy-M-d hh:mm:ss")
  })
})

</script>