package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.forumArea.entity.ForumPost;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.resourceAuthority.entity.ReadingRoomEntity;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.resourceAuthority.entity.ResHistory;
import cn.sphd.miners.modules.resourceAuthority.mapper.ReadingRoomMapper;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResHistoryMapper;
import cn.sphd.miners.modules.resourceAuthority.service.ReadRoomService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCategoryService;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2020/10/23.
 */
@Service("readRoomService")
@Transactional(readOnly=false)
public class ReadRoomServiceImpl implements ReadRoomService{

    @Autowired
    ReadingRoomMapper readingRoomMapper;
    @Autowired
    ResService resService;
    @Autowired
    ResHistoryMapper resHistoryMapper;
    @Autowired
    ResCategoryService resCategoryService;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    UserService userService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    ForumService forumService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Override
    public Integer applyBorrowingFile(User user, Integer fileId, String type, Integer auditor, String auditorName, String idType) {
        ResHistory resHis = null;
        if("1".equals(idType)){
            resHis = resHistoryMapper.getNewFileHistory(fileId);
        }else {
            ResHistory rh = new ResHistory();
            rh.setId(fileId);
            resHis = resHistoryMapper.getSingle(rh);
        }
        Integer state = 1;
        if(!resHis.getEnabled()){
            state = 2;
        }else if (Byte.valueOf("1").equals(resHis.getIsTrash())) {
            state = 3;
        }else if ("1".equals(resHis.getTeminateState())) {
            state = 2;
        } else {
            ReadingRoomEntity readingRoom = this.addReadingRoom(user, resHis.getId(), null);
            HashMap<String,Object> map =new HashMap<>();
            ReadingRoomEntity rd = readingRoomMapper.readingRoomSingle(readingRoom.getId());
            map.put("rd", rd);
            String taskbar = user.getUserName()+"在"+  NewDateUtils.dateToString(readingRoom.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"提交了文件借阅申请";
            if (type.equals("1")) {
                this.addApprovalProcessForReadingRoom(readingRoom.getId(), user, 1, auditor, auditorName, readingRoom.getCreateDate(),"");
                User userAuditor = userService.getUserByID(auditor);
                swMessageService.rejectSend(1,1,map,auditor.toString(),"/approveReadFileByApply", taskbar, taskbar, userAuditor,"approvalFileBorrowApply");   //给审批人"文件借阅申请"待审批
            }else {
                this.addApprovalProcessForReadingRoom(readingRoom.getId(), user, 1, null, null, readingRoom.getCreateDate(),"AuthApproval");
                swMessageService.rejectSendToMidManageCode(1,1,map,user.getOid(),"AuthApproval","/FinalApproveReadFile","fileBorrowApproval", taskbar,taskbar,"general");   //发给大小总务
            }
            swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/applyReadFile", null, null, user,"fileBorrowApply");   //给申请人"文件借阅申请"待审批
        }
        return state;
    }

    @Override
    public String handleBorrowFile(User user, Integer borrowId, Integer processId, String approveStatus, String approveMemo, Integer toUser, String toUserName, String type) {
        ApprovalProcess ap = approvalProcessDao.get(processId);
        QueryData query = new QueryData();
        Date newDate = new Date();
        String status = "1";
        HashMap<String,Object> map =new HashMap<>();
        if("1".equals(ap.getApproveStatus())){
            ReadingRoomEntity rd = readingRoomMapper.readingRoomSingle(borrowId);
            map.put("rd", rd);
            ReadingRoomEntity readingRoom = this.getReadingFile(borrowId);
            ap.setApproveStatus(approveStatus);
            ap.setHandleTime(newDate);
            if ("3".equals(approveStatus)) {
                readingRoom.setApproveStatus(approveStatus);
                readingRoomMapper.update(readingRoom);
                ap.setApproveMemo(approveMemo);
                if ("3".equals(type)){
                    ap.setToUser(user.getUserID());
                    ap.setToUserName(user.getUserName());
                    swMessageService.rejectSendToMidManageCode(-1,-1,map,user.getOid(),"AuthApproval","/FinalApproveReadFile","fileBorrowApproval", null,null,"general");   //发给大小总务-1
                } else {
                    swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/approveReadFileByApply", null, null, user,"approvalFileBorrowApply");   //给审批人"文件借阅申请"待审批-1
                }
                String taskbar = "您提交的《"+rd.getName()+"》文件借阅申请被驳回了！";
                User userCreator = userService.getUserByID(readingRoom.getCreator());
                userSuspendMsgService.saveUserSuspendMsg(1, taskbar, "驳回时间 "+user.getUserName()+" "+NewDateUtils.dateToString(newDate,"yyyy-MM-dd HH:mm:ss"), userCreator.getUserID(), "borrowFileDetail", borrowId); //驳回时要发消息
                swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/applyReadFile", taskbar, taskbar, userCreator,"fileBorrowApply");   //给申请人"文件借阅申请"待审批-1
                if (ap.getLevel() > 1) {
                    this.sendApprovedReadFile(borrowId,map,processId,"1");   //发给文件借阅中间审批人的已批准-1
                }
            } else {
                if ("3".equals(type)) {
                    readingRoom.setApproveStatus(approveStatus);
                    readingRoom.setAuditDate(newDate);
                    readingRoomMapper.update(readingRoom);
                    ap.setToUser(user.getUserID());
                    ap.setToUserName(user.getUserName());
                    swMessageService.rejectSendToMidManageCode(-1,-1,map,user.getOid(),"AuthApproval","/FinalApproveReadFile","fileBorrowApproval", null,null,"general");   //发给大小总务-1
                    User userCreator = userService.getUserByID(readingRoom.getCreator());
                    swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/applyReadFile", null, null, userCreator, "fileBorrowApply");   //给申请人"文件借阅申请"待审批-1
                    if (ap.getLevel() > 1) {
                        this.sendApprovedReadFile(borrowId,map,processId,"1");   //发给文件借阅中间审批人的已批准-1
                    }
                    swMessageService.rejectSend(1,1,map,userCreator.getUserID().toString(),"/approvedReadFile", null, null, userCreator, "fileBorrowApply");   //给申请人"文件借阅申请"近期可阅览文件+1
                } else {
                    String taskbar = readingRoom.getCreateName()+"在"+ NewDateUtils.dateToString(readingRoom.getCreateDate(),"yyyy-MM-dd HH:mm:ss") +"提交了文件借阅申请";
                    if ("1".equals(type)) {
                        User approveUser = userService.getUserByID(toUser);
                        swMessageService.rejectSend(1,1,map,toUser.toString(),"/approveReadFileByApply", taskbar, taskbar, approveUser,"approvalFileBorrowApply");   //给审批人"文件借阅申请"待审批+1
                        this.addApprovalProcessForReadingRoom(borrowId, user, ap.getLevel()+1, toUser, toUserName, newDate,"");
                    }else if ("2".equals(type)) {
                        swMessageService.rejectSendToMidManageCode(1,1,map,user.getOid(),"AuthApproval","/FinalApproveReadFile","fileBorrowApproval", taskbar,taskbar,"general");   //发给大小总务+1
                        this.addApprovalProcessForReadingRoom(borrowId, user, ap.getLevel()+1, null, null, newDate,"AuthApproval");
                    }
                    swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/approveReadFileByApply", null, null, user,"approvalFileBorrowApply");   //给审批人"文件借阅申请"待审批-1
                    swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/approveReadFile",null,null,user,"approvalFileBorrowApply");   //给审批人“文件借阅申请”已批准+1
                }
            }
        }else {
            status = "2";
        }
        return status;
    }

    @Override
    public ReadingRoomEntity getReadingFile(Integer id) {
        ReadingRoomEntity readingRoom = new ReadingRoomEntity();
        readingRoom.setId(id);
        readingRoom = readingRoomMapper.getSingle(readingRoom);
        return readingRoom;
    }

    @Override
    public QueryData getApplyReadFile(Integer userID) {
        ReadingRoomEntity readingRoomEntity = new ReadingRoomEntity();
        readingRoomEntity.setCreator(userID);
        readingRoomEntity.setApproveStatus("1");
        List<ReadingRoomEntity> listApplyReadFile = readingRoomMapper.getApplyReadFile(readingRoomEntity);
        readingRoomEntity.setApproveStatus("2");
        String timeBegin = NewDateUtils.dateToString(NewDateUtils.changeDay(new Date(),-29),"yyyy-MM-dd HH:mm:ss");
        String timeEnd = NewDateUtils.dateToString(NewDateUtils.tomorrow(),"yyyy-MM-dd HH:mm:ss");
        readingRoomEntity.setTimeBegin(timeBegin);
        readingRoomEntity.setTimeEnd(timeEnd);
        List<ReadingRoomEntity> listApprovedReadFile = readingRoomMapper.getApplyReadFile(readingRoomEntity);
        Integer unreadFileNum = readingRoomMapper.countUnreadFile(readingRoomEntity);
        QueryData query = new QueryData();
        query.put("listApplyReadFile", listApplyReadFile);
        query.put("listApprovedReadFile", listApprovedReadFile);
        query.put("unreadFileNum", unreadFileNum);
        return query;
    }

    @Override
    public QueryData getApproveReadFile(Integer userId) {
        QueryData query = new QueryData();
        query.put("toUser", userId);
        query.put("approveStatus", "1");
        List<ReadingRoomEntity> listReadFileByApply = readingRoomMapper.getApproveReadFile(query);
        query.clear();
        query.put("toUser", userId);
        query.put("approveStatus", "2");
        List<ReadingRoomEntity> listReadFileByApprove = readingRoomMapper.getApproveReadFile(query);
        query.put("listReadFileByApply", listReadFileByApply);
        query.put("listReadFileByApprove", listReadFileByApprove);
        return query;
    }

    @Override
    public List<ReadingRoomEntity> getLastReadFile(Integer oid) {
        QueryData query = new QueryData();
        query.put("toMid", "AuthApproval");
        query.put("fromOrg", oid);
        List<ReadingRoomEntity> list = readingRoomMapper.getLastApproveReadFile(query);
        return list;
    }

    @Override
    public List<ReadingRoomEntity> findReadFileByPage(QueryData query, String type) {
        List<ReadingRoomEntity> list = null;
        if ("1".equals(type)) {
            list = readingRoomMapper.getReadFileByApproveStatuslistPage(query);
        }else if("2".equals(type)){
            list = readingRoomMapper.getApproveReadFilelistPage(query);
        }else if("3".equals(type)){
            list = readingRoomMapper.getLastApproveReadFilelistPage(query);
        }
        return list;
    }

    @Override
    public QueryData readFileMes(Integer borrowId, User user) {
        ReadingRoomEntity readingRoomEntity = this.getReadingFile(borrowId);
        readingRoomEntity.setIsValid(0);
        if ("2".equals(readingRoomEntity.getApproveStatus())) {
            if (user.getUserID().equals(readingRoomEntity.getCreator())) {
                if (readingRoomEntity.getIsViewed() == 0) {
                    readingRoomEntity.setIsViewed(1);
                    readingRoomEntity.setViewTime(new Date());
                    readingRoomMapper.update(readingRoomEntity);
                    ReadingRoomEntity rd = readingRoomMapper.readingRoomSingle(borrowId);
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("rd", rd);
                    User userCreator = userService.getUserByID(readingRoomEntity.getCreator());
                    swMessageService.rejectSend(-1,0,map,readingRoomEntity.getCreator().toString(),"/approvedReadFile", null, null, userCreator, "fileBorrowApply");   //给申请人"文件借阅申请"近期可阅览文件-1
                }
            }
            Date dateBegin = NewDateUtils.changeDay(new Date(), -29);
            Date dateEnd = NewDateUtils.tomorrow();
            if (!(readingRoomEntity.getAuditDate().after(dateBegin) && readingRoomEntity.getAuditDate().before(dateEnd))) {
                readingRoomEntity.setIsValid(1);
            }
        }
        ResHistory rh = new ResHistory();
        rh.setId(readingRoomEntity.getResourceHistory());
        rh = resService.gethisSingle(rh);
        ResCategory cat = resCategoryService.getSingle(rh.getCategory());
        String allCategoryName = resService.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");      //获取文件类别全名
        rh.setCategoryName(allCategoryName);
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(borrowId, 29, "");
        ResEntity res = resService.getSingle(rh.getFile());
        QueryData query = new QueryData();
        query.put("readingRoom", readingRoomEntity);
        query.put("resHis", rh);
        query.put("listAp", listAp);
        query.put("isTrash",res.getIsTrash());
        return query;
    }

    @Override
    public String getNewFilePlace(Integer fileId) {
        ResHistory rh = new ResHistory();
        rh.setFile(fileId);
        rh.setOperation("5");
        List<ResHistory> list = resHistoryMapper.getRecordByOperation(rh);
        String allCategoryName = null;
        if(list.size() > 1){
            ResCategory cat = resCategoryService.getSingle(list.get(list.size()-1).getCategory());
            allCategoryName = resService.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");      //获取文件类别全名
        }
        return allCategoryName;
    }

    @Override
    public void cleanedReadFileByDaysAgo() {
        Date timeBegin = NewDateUtils.changeDay(new Date(), -29);
        List<ReadingRoomEntity> list = readingRoomMapper.getDaysAgoReadFile(timeBegin);
        if (!list.isEmpty()) {
            for (ReadingRoomEntity rd : list) {
                ReadingRoomEntity r = new ReadingRoomEntity();
                r.setId(rd.getId());
                r.setIsViewed(1);
                r.setViewTime(new Date());
                readingRoomMapper.update(r);
                HashMap<String, Object> map = new HashMap<>();
                map.put("rd", rd);
                User userCreator = userService.getUserByID(rd.getCreator());
                swMessageService.rejectSend(-1,-1,map,rd.getCreator().toString(),"/approvedReadFile", null, null, userCreator, "fileBorrowApply");   //给申请人"文件借阅申请"近期可阅览文件-1
            }
        }
        List<ReadingRoomEntity> listForum = readingRoomMapper.getDaysAgoReadForum(timeBegin);
        if (!listForum.isEmpty()) {
            for (ReadingRoomEntity rd : listForum) {
                ReadingRoomEntity r = new ReadingRoomEntity();
                r.setId(rd.getId());
                r.setIsViewed(1);
                r.setViewTime(new Date());
                readingRoomMapper.update(r);
                HashMap<String, Object> map = new HashMap<>();
                map.put("rd", rd);
                User userCreator = userService.getUserByID(rd.getCreator());
                swMessageService.rejectSend(-1,-1,map,userCreator.getUserID().toString(),"/approvedReadForum", null, null, userCreator, "forumBorrowApply");   //给申请人"讨论借阅申请"近期可阅览讨论-1
            }
        }

    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=null;
        switch (code){
            case "fileBorrowApply": //近期可阅览文件
                ReadingRoomEntity readingRoomEntity = new ReadingRoomEntity();
                readingRoomEntity.setCreator(user.getUserID());
                readingRoomEntity.setApproveStatus("2");
                number = readingRoomMapper.countUnreadFile(readingRoomEntity);
                break;
            case "approvalFileBorrowApply": //文件借阅申请
                number = readingRoomMapper.countApproveReadFile(user.getUserID());
                break;
            case "fileBorrowApproval": //文件借阅审批
                if ("general".equals(user.getManagerCode())) {
                    QueryData query = new QueryData();
                    query.put("toMid", "AuthApproval");
                    query.put("fromOrg", user.getOid());
                    number = readingRoomMapper.countLastApproveReadFile(query);
                }
                break;
            case "forumBorrowApply": //近期可阅览讨论组
                ReadingRoomEntity readingRoomEntityForum = new ReadingRoomEntity();
                readingRoomEntityForum.setCreator(user.getUserID());
                readingRoomEntityForum.setApproveStatus("2");
                number = readingRoomMapper.countUnreadForum(readingRoomEntityForum);
                break;
            case "approvalForumBorrowApply": //讨论组查阅申请
                number = readingRoomMapper.countApproveReadForum(user.getUserID());
                break;
            case "forumBorrowApproval": //讨论组查阅审批
                if ("general".equals(user.getManagerCode())) {
                    HashMap<String,Object> map = new HashMap<>();
                    map.put("toMid", "AuthApproval");
                    map.put("fromOrg", user.getOid());
                    number = readingRoomMapper.countLastApproveReadForum(map);
                }
                break;
        }
        return number;
    }

    @Override
    public void applyBorrowingForum(User user, Integer postId, String type, Integer auditor, String auditorName) {
        ReadingRoomEntity readingRoom = this.addReadingRoom(user, null, postId);
        HashMap<String,Object> map =new HashMap<>();
        ReadingRoomEntity rf = readingRoomMapper.readingRoomSingleByForum(readingRoom.getId());
        map.put("rf", rf);
        String taskbar = user.getUserName()+"在"+ NewDateUtils.dateToString(readingRoom.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"提交了讨论查阅申请";
        if (type.equals("1")) {
            this.addApprovalProcessForReadingRoom(readingRoom.getId(), user, 1, auditor, auditorName, readingRoom.getCreateDate(),"");
            User userAuditor = userService.getUserByID(auditor);
            swMessageService.rejectSend(1,1,map,auditor.toString(),"/approveReadForumByApply", taskbar, taskbar, userAuditor,"approvalForumBorrowApply");   //给审批人"讨论借阅申请"待审批
        } else {
            this.addApprovalProcessForReadingRoom(readingRoom.getId(), user, 1, null, null, readingRoom.getCreateDate(),"AuthApproval");
            swMessageService.rejectSendToMidManageCode(1,1,map,user.getOid(),"AuthApproval","/FinalApproveReadForum","forumBorrowApproval", taskbar,taskbar,"general");   //发给大小总务
        }
        swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/applyReadForum", null, null, user,"forumBorrowApply");   //给申请人"文件借阅申请"待审批
    }

    @Override
    public String handleBorrowForum(User user, Integer borrowId, Integer processId, String approveStatus, String approveMemo, Integer toUser, String toUserName, String type) {
        ApprovalProcess ap = approvalProcessDao.get(processId);
        QueryData query = new QueryData();
        Date newDate = new Date();
        String status = "1";
        HashMap<String,Object> map =new HashMap<>();
        if("1".equals(ap.getApproveStatus())){
            ReadingRoomEntity rf = readingRoomMapper.readingRoomSingleByForum(borrowId);
            map.put("rf", rf);
            ReadingRoomEntity readingRoom = this.getReadingFile(borrowId);
            ap.setApproveStatus(approveStatus);
            ap.setHandleTime(newDate);
            if ("3".equals(approveStatus)) {
                readingRoom.setApproveStatus(approveStatus);
                readingRoomMapper.update(readingRoom);
                ap.setApproveMemo(approveMemo);
                if ("3".equals(type)){
                    ap.setToUser(user.getUserID());
                    ap.setToUserName(user.getUserName());
                    swMessageService.rejectSendToMidManageCode(-1,-1,map,user.getOid(),"AuthApproval","/FinalApproveReadForum","forumBorrowApproval", null,null,"general");   //发给大小总务-1
                }else {
                    swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/approveReadForumByApply", null, null, user,"approvalForumBorrowApply");   //给审批人"讨论借阅申请"待审批-1
                }
                String taskbar = "您提交的《"+rf.getTitle()+"》讨论组查阅申请被驳回了！";
                User userCreator = userService.getUserByID(readingRoom.getCreator());
                userSuspendMsgService.saveUserSuspendMsg(1, taskbar, "驳回时间 "+user.getUserName()+" "+NewDateUtils.dateToString(newDate,"yyyy-MM-dd HH:mm:ss"), userCreator.getUserID(), "borrowForumDetail", borrowId); //驳回时要发消息
                swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/applyReadFroum", taskbar, taskbar, userCreator,"forumBorrowApply");   //给申请人"讨论组查阅申请"待审批-1
                if (ap.getLevel() > 1) {
                    this.sendApprovedReadFile(borrowId,map,processId,"2");   //发给讨论借阅中间审批人的已批准-1
                }
            } else {
                if ("3".equals(type)) {
                    readingRoom.setApproveStatus(approveStatus);
                    readingRoom.setAuditDate(newDate);
                    readingRoomMapper.update(readingRoom);
                    ap.setToUser(user.getUserID());
                    ap.setToUserName(user.getUserName());
                    swMessageService.rejectSendToMidManageCode(-1,-1,map,user.getOid(),"AuthApproval","/FinalApproveReadForum","forumBorrowApproval", null,null,"general");   //发给大小总务-1
                    User userCreator = userService.getUserByID(readingRoom.getCreator());
                    swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/applyReadForum", null, null, userCreator, "forumBorrowApply");   //给申请人"讨论借阅申请"待审批-1
                    if (ap.getLevel() > 1) {
                        this.sendApprovedReadFile(borrowId,map,processId,"2");   //发给讨论借阅中间审批人的已批准-1
                    }
                    swMessageService.rejectSend(1,1,map,userCreator.getUserID().toString(),"/approvedReadForum", null, null, userCreator, "forumBorrowApply");   //给申请人"讨论借阅申请"近期可阅览文件+1
                } else {
                    String taskbar = readingRoom.getCreateName()+"在"+ NewDateUtils.dateToString(readingRoom.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"提交了讨论组查阅申请";
                    if ("1".equals(type)) {
                        User approveUser = userService.getUserByID(toUser);
                        swMessageService.rejectSend(1,1,map,toUser.toString(),"/approveReadForumByApply", taskbar, taskbar, approveUser,"approvalForumBorrowApply");   //给审批人"讨论借阅申请"待审批+1
                        this.addApprovalProcessForReadingRoom(borrowId, user, ap.getLevel()+1, toUser, toUserName, newDate,"");
                    }else if ("2".equals(type)) {
                        swMessageService.rejectSendToMidManageCode(1,1,map,user.getOid(),"AuthApproval","/FinalApproveReadFoeum","forumBorrowApproval", taskbar,taskbar,"general");   //发给大小总务+1
                        this.addApprovalProcessForReadingRoom(borrowId, user, ap.getLevel()+1, null, null, newDate,"AuthApproval");
                    }
                    swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/approveReadForumByApply", null, null, user,"approvalForumBorrowApply");   //给审批人"讨论借阅申请"待审批-1
                    swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/approveReadForum",null,null,user,"approvalForumBorrowApply");   //给审批人“讨论借阅申请”已批准+1
                }
            }
        } else {
            status = "2";
        }
        return status;
    }

    @Override
    public QueryData getApplyReadForum(Integer userID) {
        ReadingRoomEntity readingRoomEntity = new ReadingRoomEntity();
        readingRoomEntity.setCreator(userID);
        readingRoomEntity.setApproveStatus("1");
        List<ReadingRoomEntity> listApplyReadForum = readingRoomMapper.getApplyReadForum(readingRoomEntity);
        readingRoomEntity.setApproveStatus("2");
        String timeBegin = NewDateUtils.dateToString(NewDateUtils.changeDay(new Date(),-29),"yyyy-MM-dd HH:mm:ss");
        String timeEnd = NewDateUtils.dateToString(NewDateUtils.tomorrow(),"yyyy-MM-dd HH:mm:ss");
        readingRoomEntity.setTimeBegin(timeBegin);
        readingRoomEntity.setTimeEnd(timeEnd);
        List<ReadingRoomEntity> listApprovedReadForum = readingRoomMapper.getApplyReadForum(readingRoomEntity);
        Integer unreadForumNum = readingRoomMapper.countUnreadForum(readingRoomEntity);
        QueryData query = new QueryData();
        query.put("listApplyReadForum", listApplyReadForum);
        query.put("listApprovedReadForum", listApprovedReadForum);
        query.put("unreadForumNum", unreadForumNum);
        return query;
    }

    @Override
    public QueryData getApproveReadForum(Integer userID) {
        QueryData query = new QueryData();
        query.put("toUser", userID);
        query.put("approveStatus", "1");
        List<ReadingRoomEntity> listReadForumByApply = readingRoomMapper.getApproveReadForum(query);
        query.clear();
        query.put("toUser", userID);
        query.put("approveStatus", "2");
        List<ReadingRoomEntity> listReadForumByApprove = readingRoomMapper.getApproveReadForum(query);
        QueryData queryBack = new QueryData();
        queryBack.put("listReadForumByApply", listReadForumByApply);
        queryBack.put("listReadForumByApprove", listReadForumByApprove);
        return queryBack;
    }

    @Override
    public List<ReadingRoomEntity> getLastReadForum(Integer oid) {
        QueryData query = new QueryData();
        query.put("toMid", "AuthApproval");
        query.put("fromOrg", oid);
        List<ReadingRoomEntity> list = readingRoomMapper.getLastApproveReadForum(query);
        return list;
    }

    @Override
    public List<ReadingRoomEntity> findReadForumByPage(QueryData query, String eventType) {
        List<ReadingRoomEntity> list = null;
        if ("1".equals(eventType)) {
            list = readingRoomMapper.getReadForumByApproveStatuslistPage(query);
        }else if("2".equals(eventType)){
            list = readingRoomMapper.getApproveReadForumlistPage(query);
        }else if("3".equals(eventType)){
            list = readingRoomMapper.getLastApproveReadForumlistPage(query);
        }
        return list;
    }

    @Override
    public QueryData readForumPostMes(Integer borrowId, User user) {
        ReadingRoomEntity readingRoomEntity = this.getReadingFile(borrowId);
        readingRoomEntity.setIsValid(0);
        if ("2".equals(readingRoomEntity.getApproveStatus())) {
            if (user.getUserID().equals(readingRoomEntity.getCreator())) {
                if (readingRoomEntity.getIsViewed() == 0) {
                    readingRoomEntity.setIsViewed(1);
                    readingRoomEntity.setViewTime(new Date());
                    readingRoomMapper.update(readingRoomEntity);
                    ReadingRoomEntity rf = readingRoomMapper.readingRoomSingleByForum(borrowId);
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("rf", rf);
                    User userCreator = userService.getUserByID(readingRoomEntity.getCreator());
                    swMessageService.rejectSend(-1,0,map,userCreator.getUserID().toString(),"/approvedReadForum", null, null, userCreator, "forumBorrowApply");   //给申请人"讨论借阅申请"近期可阅览讨论-1
                }
            }
            Date dateBegin = NewDateUtils.changeDay(new Date(), -29);
            Date dateEnd = NewDateUtils.tomorrow();
            if (!(readingRoomEntity.getAuditDate().after(dateBegin) && readingRoomEntity.getAuditDate().before(dateEnd))) {
                readingRoomEntity.setIsValid(1);
            }
        }
        ForumPost forumPost = forumService.forumPostMessageByhead(readingRoomEntity.getResourceForum());
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(borrowId, 29, "");
        QueryData query = new QueryData();
        query.put("readingRoom", readingRoomEntity);
        query.put("forumPost", forumPost);
        query.put("listAp", listAp);
        return query;
    }

    //向审批流程表中存入数据
    private void addApprovalProcessForReadingRoom(Integer borrowId, User user, Integer level, Integer toUser,
                                                  String toUserName, Date createDate, String toMid){
        ApprovalProcess ap = new ApprovalProcess();
        ap.setApproveStatus("1");
        ap.setBusiness(borrowId);
        ap.setBusinessType(29);
        ap.setLevel(level);
        ap.setOrg(user.getOid());
        ap.setFromUser(user.getUserID());
        ap.setUserName(user.getUserName());
        if (toMid == "") {
            ap.setToUser(toUser);
            ap.setToUserName(toUserName);
        } else {
            ap.setToMid(toMid);
        }
        ap.setCreateDate(createDate);
        approvalProcessDao.save(ap);
    }

    //循环给中间审批人“文件借阅申请”或“文件借阅审批”的已批准
    private void sendApprovedReadFile(Integer borrowId, HashMap map, Integer processId, String type){
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(borrowId, 29, "");
        String toMid= listAp.get(listAp.size()-1).getToMid();
        if("AuthApproval".equals(toMid)){
            listAp.remove(listAp.size()-1);
        }
        if (!listAp.isEmpty()) {
            for(ApprovalProcess ap : listAp){
                if (!ap.getId().equals(processId)){
                    User user = userService.getUserByID(ap.getToUser());
                    if ("1".equals(type)) {
                        swMessageService.rejectSend(0,-1,map,ap.getToUser().toString(),"/approveReadFile",null,null,user,"approvalFileBorrowApply");
                    } else {
                        swMessageService.rejectSend(0,-1,map,ap.getToUser().toString(),"/approveReadForum",null,null,user,"approvalForumBorrowApply");
                    }
                }
            }
        }
    }

    //想借阅表新增借阅实例
    ReadingRoomEntity addReadingRoom(User user, Integer resHisId, Integer postId){
        ReadingRoomEntity readingRoom = new ReadingRoomEntity();
        readingRoom.setOrg(user.getOid());
        ResHistory resHis = null;
        if (resHisId != null) {
            readingRoom.setResourceHistory(resHisId);
        }
        if (postId != null) {
            readingRoom.setResourceForum(postId);
        }
        readingRoom.setUser(user.getUserID());
        readingRoom.setIsViewed(0);
        //readingRoom.setOriginalCategory(resHis.getCategory());
        readingRoom.setApproveStatus("1");
        readingRoom.setCreator(user.getUserID());
        readingRoom.setCreateName(user.getUserName());
        readingRoom.setCreateDate(new Date());
        readingRoomMapper.insert(readingRoom);
        return readingRoom;
    }

}
