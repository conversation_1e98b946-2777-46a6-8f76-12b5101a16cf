<template>
  <div id="moduleChangeApprovalQuery" class="moduleChangeApprovalQuery">
    <TY_NavTop title="机构所用产品的修改">
      <div class="nav_btn"  @click="submit" v-loading.fullscreen.lock="loading">确定</div>
    </TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approvalStatus" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div>
              <h4 class="item-header">申请人</h4>
              <div class="el-form-item">
                <span :class="queryForm.applyUserId === 0?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyUserName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>

<style>
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  .moduleChangeApprovalQuery .el-radio__input, .moduleChangeApprovalQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
  .moduleChangeApprovalQuery .el-form-item{
    margin-bottom: 8px;
  }
  .moduleChangeApprovalQuery .el-radio-group .el-radio {
    display: inline-block;
    margin: 0 5px 5px 0;
  }
  .el-radio.is-bordered + .el-radio.is-bordered
  .moduleChangeApprovalQuery .el-checkbox{
    margin-bottom: 8px;
  }
  .moduleChangeApprovalQuery .el-radio:first-child{
    margin-left: 10px;
  }
</style>

<script>
export default {
  name: 'moduleChangeApprovalQuery',
  data () {
    return {
      loading: true,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approvalStatus: '2',
        applyUserId: 0,
        applyUserName: '选择申请人'
      },
      roleList: [],
      bookVisible: false
    }
  },
  created: function () {
    let that = this
    this.$http.post('../../../special/getEditOrgPopedomUsers.do', { userId: this.sphdSocket.user.userID }, {
      emulateJSON: true
    }).then((response) => {
      that.loading = false
      let data = response.body
      if (data) {
        let data = response.data
        let list = data.data
        that.roleList = list
      } else {
        console.log('加载失败！')
      }
    })
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approvalStatus 2- 批准 3- 驳回
      // feeCat 费用类别id
      // feeCatName 费用类别名称
      // billCat 票据种类 id
      // billCatName 票据种类名称
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        approvalStatus: this.queryForm.approvalStatus,
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        type: this.queryForm.type,
        applyUserId: this.queryForm.applyUserId,
        applyUserName: this.queryForm.applyUserName,
        userId: this.sphdSocket.user.userID
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/moduleChangeApprovalQueryPer',
        name: 'moduleChangeApprovalQueryPer',
        params: {
          data: queryParam
        }
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.applyUserId = 0
        this.queryForm.applyUserName = '请选择申请人'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.applyUserId = data.userID
      this.queryForm.applyUserName = data.userName
    }
  }
}
</script>
