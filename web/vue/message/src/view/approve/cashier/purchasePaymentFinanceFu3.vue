<template>
  <div id="purchasePaymentFinanceFu3"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="原定的付款方式"></TY_NavTop>
    <div class="detailInfo" v-if="false">
      <!--票据-->
      <div v-if="pund === 0">
        <div @click="jumpDetail" >
          <span><span class="ttl1">申请付款金额 </span><span> {{detailInfo.amount && detailInfo.amount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span>  {{details.fullName }}</span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailInfo.payNum || 0 }}笔，共{{(detailInfo.paidAmount && detailInfo.paidAmount.toFixed(2))|| 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.numApplication || 0 }}笔，共{{(detailInfo.lockedAmount && detailInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
      <!--预付款-->
      <div v-if="pund === 1">
        <div @click="jumpDetail" >
          <span><span class="ttl1">申请付款金额 </span><span> {{detailInfo.planAmount && detailInfo.planAmount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span> {{details.srmSupplier && details.srmSupplier.fullName }} </span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailInfo.payNum || 0 }}笔，共{{(detailInfo.paidAmount && detailInfo.paidAmount.toFixed(2))|| 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailInfo.lockedAmount && detailInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">计划付款金额</span>{{detailInfo.planAmount && detailInfo.planAmount.toFixed(2)}}元
        </div>
        <div>
          <span class="ttl1">计划付款时间</span>{{detailInfo.planDate |formatDay('YYYY-MM-DD')}}
          <span class="redTxt"><span class="ttl1">计划付款方式</span>{{isture}}</span>
        </div>
        <div class="martop10">
          <span class="ttl1">付款事由</span>采购的预付款
        </div>
      </div>
      <!--多收来的款-->
      <div v-if="pund === 3">
        <div>
          <span class="ttl1">原始的付款方</span><span> {{detailInfo.supplierName }} </span>
        </div>
        <div>
          <span><span class="ttl1">多收来的金额 </span><span>  {{detailInfo.amount && detailInfo.amount.toFixed(2)}} 元 </span></span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailInfo.payNum || 0 }}笔，共{{(detailInfo.paidAmount && detailInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailInfo.lockedAmount && detailInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
    </div>
    <div v-if="details.financePaymentHistory">
      <el-form :label-position="left" label-width="180px" >

        <div v-if="details.financePaymentHistory.method === '5'" >
          <el-form-item label="付款方式"><span>银行转账</span></el-form-item>
          <el-form-item label="银行账户"><span>{{ details.financeAccount.name }}元</span></el-form-item>
          <el-form-item label="账户类型"><span>{{ details.financeAccount.isPublic == '1' ? '对公户 ': '非对公户' }} </span></el-form-item>
          <el-form-item label="开户行"><span>{{details.financeAccount.bankName}}</span></el-form-item>
          <el-form-item label="账号"><span>{{details.financeAccount.account}}</span></el-form-item>
          <el-form-item label="付款金额"><span>{{details.financePaymentHistory.factAmount.toFixed(2)}}</span></el-form-item>
          <el-form-item v-if="false" label="拟付款日期"><span>{{ details.financePaymentHistory.planDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>
        </div>

        <div v-if="details.financePaymentHistory.method === '4'">
          <el-form-item label="付款方式"><span>承兑汇票（外）</span></el-form-item>
          <el-form-item label="汇票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
          <el-form-item label="所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount }}</span></el-form-item>
          <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
          <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName }}</span></el-form-item>
        </div>


        <div v-if="details.financePaymentHistory.method === '3'">
          <div v-if="details.invoiceType == '2'">  <!--(1-其他付款方式  2-内部转账支票 3-外部转账支票)-->
            <el-form-item label="付款方式"><span>转账支票（内）</span></el-form-item>
            <el-form-item label="转帐支票的开户行"><span>{{ details.financeAccount.bankName  }}</span></el-form-item>
            <el-form-item label="转帐支票的支票号"><span>{{ details.financeChequeDetail.chequeNo }}</span></el-form-item>
            <el-form-item label="支票金额"><span>{{ details.financeChequeDetail.amount.toFixed(2) }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeChequeDetail.expireDate | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
          </div>
          <div v-if="details.invoiceType == '3'">
            <el-form-item label="付款方式"><span>转账支票（外）</span></el-form-item>
            <el-form-item label="支票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
            <el-form-item label="所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
            <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName}}</span></el-form-item>
          </div>
        </div>

      </el-form>
    </div>
  </div>
</template>
<style lang="less">
#purchasePaymentFinanceFu3{
  background: #fff;
  overflow: auto!important;
  .el-icon-arrow-right{ float: right; font-size: 16px; }
  .linkBtn {color: #5d9cec;cursor: default;margin-right: 20px;}
  .linkBtn:hover {text-decoration: underline;}
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  .detailInfo{
    padding:10px 20px 0px 20px;
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
    line-height: 26px;
    .ttl1{
      display: inline-block;
      width: 86px;
      text-align: right;
      margin-right: 10px;
    }
  }
  .mar30{ margin-right:30px;  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  .flexC{
    display: flex;margin-bottom:15px ;
    &>span{ display:block; flex: 1;  }
  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  //.rejectPng {position: absolute;top: calc(50% - 60px);left: calc(50% - 60px);
  //  z-index: 50;filter: alpha(opacity=60);opacity: 0.6;-moz-opacity: 0.6;-khtml-opacity: 0.6}
  .con1 {background: #fff;margin-bottom: 10px;padding: 5px 15px;font-size: 14px;}
  .con1 > p {text-align: right;font-size: 0.8em;}
  .oInfo {padding: 5px 20px;}
  .txt_right {text-align: right;}
  .right {float: right;}
  .ui-cell {display: block;}
}
</style>
<script>
var that
export default {
  name: 'purchasePaymentFinanceFu3',
  data () {
    return {
      loading: true,
      toggle: -1, // 审批记录控制显隐
      balance: 0,
      selectOk: '',
      isSpecialBill: 0,
      isPublic: 0,
      selectFin: {},
      detailInfo:{},
      details: {
        financePaymentHistory: {},
        poPaymentApplication: {},
        poOrders: {}
      },
      alreadyPay: false,
      selectInvoice: {}, // 选择好的 票据信息
      personnelReimburse: {},
      nowApproveItem: {},
      approvalProcessList: {},
      financeAccounts: {},
      isNowApprover: false,
      square: { 'factDate': '', 'summary': '' },
      rules: {
        factDate: [
          { required: true }
        ],
        summary: [
          { required: true }
        ]
      },
      // ttl: "采购部门的票据审核",
      isReject: false, // 判断是否驳回来显示驳回图片
      isRevoke: false, // 判断是否显示撤销
      // rejectPng: rejectPng, // 驳回图片
      dialogVisible: false,
      listenersUid: [],
      approveSelect: [],
      approveSta: '',
      approveStatus: '0',
      reason: '',
      invoiceID: '',
      busyid: '',
      activeName: 'bill',
      orderProgressRade: false,
      code: 0,
      pund: 0
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    },
    formReason: function (num, applicationDesc) {
      let str = ''
      switch (Number(num)) {
        case 1:
          str = '符合合同约定'
          break
        case 2:
          str = applicationDesc
          break
      }
      return str
    },
    formatCat: function (num) {
      let str = ''
      switch (Number(num)) {
        case 1:
          str = '增值税专用发票'
          break
        case 2:
          str = '增值税普通发票'
          break
        case 3:
          str = '收据'
          break
        case 4:
          str = '其他发票'
          break
      }
      return str
    },
    formatType: function (num, amount) {
      let str = ''
      switch (Number(num)) {
        case 1:
          str = '本次仅提交票据，不付款'
          break
        case 2:
          str = `申请付款：${amount.toFixed(2)}元`
          break
        case 3:
          str = `申请付款：${amount.toFixed(2)}元`
          break
      }
      return str
    }
  },
  created () {
    that = this
    let paramCode = this.$route.params.id
    let parampun = Number(this.$route.params.pun)
    let paramyid = Number(this.$route.params.yid)
    that.code = Number(paramCode.substring(0, 2)) // 31-待在线审核; 32-在线审核ok; 33-待线下审核; 34 - 付款的查看详情
    that.invoiceID = paramCode.substring(2, paramCode.length) // 票据的id
    that.pund = Number(parampun)
    that.yid = paramyid
    console.log('pundpund:',that.pund , 'yid=', that.yid )
    that.getDetailOldPay()
  },
  destroyed: function () {
  },
  methods: {
    getAlreadyPayDetails () {
      if(this.pund === 0){
        this.details.pund = this.pund
        this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      }else{
        this.detailsote.pund = this.pund
        this.$store.dispatch('setPurchaseFinanceInfo', this.detailsote)
      }
      this.$router.push({
        path: `/purchasePaymentFinanceAlready/${this.invoiceID}`
      })
    },
    getCheckDetails () {
      console.log('getCheckDetails ')
      if(this.pund === 0){
        this.details.pund = this.pund
        this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      }else{
        this.detailsote.pund = this.pund
        this.$store.dispatch('setPurchaseFinanceInfo', this.detailsote)
      }
      this.$router.push({
        path: `/purchasePaymentFinanceCheck/${this.invoiceID}`
      })
    },
    jumpDetail: function (pun) {
      let that = this
      that.$router.push({
        path: `/purchaseInvoiceDetails3/34${that.invoiceID}/${pun}/${that.yid}`
      })
    },
    strSub: function () {
      let reasonLen = this.reason.length
      if (reasonLen > 40) {
        this.reason = this.reason.substr(0, 40)
      }
    },
    getDetailOldPay: function () {
      let businessTypePH = 'prepayment' // 预付款的// businessTypePH的值分别是两个单词，票据一个预付款一个
      let dparams = { "financePaymentId": this.yid }
      let url = '../../../purchaseInvoice/getFinancePayment.do'
      if (that.pund === 0) {
        dparams.businessType = "procurement"
      } else if (that.pund === 3) {
        dparams.businessType = "overpayment" //多收来的款
        url = '../../../overpayment/getFinancePayment.do'
      } else {
        dparams.businessType = "prepayment"
      }
      this.axios.post( url , dparams)
        .then(function (response) {
          let res = response.data.data
          console.log('详情：', response)
          that.details = res
          if (that.pund === 0) {
            that.detailInfo = res.poPaymentApplication
          } else if (that.pund === 3) {
            that.detailInfo = res.loanBiz
          } else {
            that.detailInfo = res.poOrdersPrepaymen
          }
          that.loading = false
        })
        .catch(function (error) {
          console.log(error)
        })
    },

  }
}
</script>
