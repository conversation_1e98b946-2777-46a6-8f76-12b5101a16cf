package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.modules.schedule.dao.MemoFavoriteDao;
import cn.sphd.miners.modules.schedule.dao.MemoFavoriteScheduleDao;
import cn.sphd.miners.modules.schedule.entity.MemoFavorite;
import cn.sphd.miners.modules.schedule.entity.MemoFavoriteSchedule;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.schedule.service.MemoFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoFavoriteServiceImpl implements MemoFavoriteService {

    @Autowired
    MemoFavoriteDao memoFavoriteDao;
    @Autowired
    MemoFavoriteScheduleDao memoFavoriteScheduleDao;

    @Override
    public void saveMemoFavorite(MemoFavorite memoFavorite) {
        memoFavoriteDao.save(memoFavorite);
    }

    @Override
    public MemoFavorite getMemoFavoriteById(Integer id) {
        return memoFavoriteDao.get(id);
    }

    @Override
    public void updateMemoFavorite(MemoFavorite memoFavorite) {
        memoFavoriteDao.update(memoFavorite);
    }

    @Override
    public void deleteMemoFavorite(MemoFavorite memoFavorite) {
        memoFavoriteDao.delete(memoFavorite);
    }

    @Override
    public List<MemoFavorite> getFirstFavorites(Integer userId) {
        String hql=" from MemoFavorite where userId=:userId and level=:level";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("level",1);
        List<MemoFavorite> memoFavoriteList=memoFavoriteDao.getListByHQLWithNamedParams(hql,map);
        return memoFavoriteList;
    }

    @Override
    public List<MemoFavorite> getFavoritesByPid(Integer pid) {
        String hql=" from MemoFavorite where parentId=:pid";
        Map<String,Object> map=new HashMap<>();
        map.put("pid",pid);
        List<MemoFavorite> memoFavoriteList=memoFavoriteDao.getListByHQLWithNamedParams(hql,map);
        return memoFavoriteList;
    }

    @Override
    public List<Integer> getFavoriteIdsByPath(String path) {
        String hql="select id from MemoFavorite where path=:path";
        Map<String,Object> map=new HashMap<>();
        map.put("path",path);
        List<Integer> ids=memoFavoriteDao.getListByHQLWithNamedParams(hql,map);
        return ids;
    }

    @Override
    public Integer getCountsByFavoriteIds(List<Integer> favoriteIds) {
        String hql="select count(0) from MemoFavoriteSchedule where favoriteId in(:favoriteIds)";
        Map<String,Object> map=new HashMap<>();
        map.put("favoriteIds",favoriteIds);
        Long counts= (Long) memoFavoriteScheduleDao.getByHQLWithNamedParams(hql,map);
        return counts.intValue();
    }

    @Override
    public List<MemoSchedule> getMemoSchedulesByFavoriteId(Integer userId, Integer favoriteId) {
        String hql="select m.schedule from MemoFavoriteSchedule m where m.schedule.user=:userId and m.favoriteId=:favoriteId";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("favoriteId",favoriteId);
        List<MemoSchedule> memoScheduleList=memoFavoriteScheduleDao.getListByHQLWithNamedParams(hql,map);
        return memoScheduleList;
    }

    @Override
    public MemoFavoriteSchedule getMemoFavoriteScheduleByScheduleId(Integer userId,Integer oldFavoriteId,Integer scheduleId) {
        String hql=" from MemoFavoriteSchedule m where m.schedule.user=:userId and m.favoriteId=:oldFavoriteId and m.scheduleId=:scheduleId";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("oldFavoriteId",oldFavoriteId);
        map.put("scheduleId",scheduleId);
        MemoFavoriteSchedule memoFavoriteSchedule= (MemoFavoriteSchedule) memoFavoriteScheduleDao.getByHQLWithNamedParams(hql,map);
        return memoFavoriteSchedule;
    }

    @Override
    public void updateMemoFavoriteSchedule(MemoFavoriteSchedule memoFavoriteSchedule) {
        memoFavoriteScheduleDao.update(memoFavoriteSchedule);
    }

    @Override
    public void saveMemoFavoriteSchedule(MemoFavoriteSchedule memoFavoriteSchedule) {
        memoFavoriteScheduleDao.save(memoFavoriteSchedule);
    }

    @Override
    public void deleteMemoFavoriteSchedule(MemoFavoriteSchedule memoFavoriteSchedule) {
        memoFavoriteScheduleDao.delete(memoFavoriteSchedule);
    }

    @Override
    public void deleteAllMemoFavoriteSchedule(List<MemoFavoriteSchedule> memoFavoriteScheduleList){
        memoFavoriteScheduleDao.deleteAll(memoFavoriteScheduleList);
    }

    @Override
    public List<MemoFavoriteSchedule> getMemoFavoriteSchedulesByScheduleId(Integer scheduleId) {
        String hql=" from MemoFavoriteSchedule where scheduleId=:scheduleId";
        Map<String,Object> map=new HashMap<>();
        map.put("scheduleId",scheduleId);
        List<MemoFavoriteSchedule> memoFavoriteScheduleList= memoFavoriteScheduleDao.getListByHQLWithNamedParams(hql,map);
        return memoFavoriteScheduleList;
    }
}
