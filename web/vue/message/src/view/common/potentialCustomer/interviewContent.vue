<template>
  <div id="contactList" class="potential" v-show="show">
    <TY_NavTop title="潜在客户申请" :isReBack="true" isButton="true" @reback="closeShow" @toggleSubmit="setContent"></TY_NavTop>
    <div class="container">
      <div class="panel-content">
        <div class="gapTop">
          <p v-if="source === 1">请录入本次访谈的访谈目标<span class="posRt blueTip" @click="clearCon()">清空</span></p>
          <div v-else-if="source === 2">
            <el-row type="flex" justify="space-between">
              <el-col :span="6"><span class="red">*</span>发言者</el-col>
              <el-col :span="18">
                <el-select v-model="participator" placeholder="请选择" size="small" >
                  <el-option
                    v-for="(item, index) in personnelListD"
                    :key="index"
                    :label="item.memo || '--'"
                    :value="item.participator">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="20"><span class="red">*</span>请录入该发言者的谈话要点</el-col>
              <el-col :span="4"><span class="posRt blueTip" @click="clearCon()">清空</span></el-col>
            </el-row>
          </div>
          <p v-if="source === 3">请录入本次访谈的成果/结论<span class="posRt blueTip" @click="clearCon()">清空</span></p>
        </div>
        <el-form ref="form" label-width="80px" label-position="top">
          <el-form-item>
            <el-input
              type="textarea"
              placeholder=""
              v-model="content"
              maxlength="200"
              show-word-limit
            >
            </el-input> </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<style lang="less">
.potential{
  .gapR{
    height: 20px;
    background: #eee;
  }
  .gapTop{
    margin-top: 20px;
  }
  .blueTip{
    color: #0070bf;
  }
}
#contactList {
  position: fixed;
  top: 0;
  left: 0;
  z-index:1000;
  width: 100%;
  height: 100%;
  background: #fff;
  .el-row{
    margin-left: 10px;
  }
  .dot-btn {
    border: 1px solid #dcdfe6;
    border-radius: 100%;
    width: 14px;
    height: 14px;
    background-color: #fff;
    position: relative;
    cursor: pointer;
    display: inline-block;
    box-sizing: border-box;
    vertical-align: middle;
  }
  .dot-btn:hover {
    border-color:#409eff
  }
}
</style>
<script>
export default {
  name: 'contentInput',
  props: {
    source: Number,
    editType: Number,
    content: String,
    participator: String,
    personnelListD: Array,
    show: Boolean
  },
  methods: {
    clearCon: function () {
      this.content = ""
    },
    setContent: function () {
      let that = this
      let data = ``
      if (that.source === 2) {
        let item = that.personnelListD.find(value => value.participator === that.participator)
        if (item && item.abled) {
          this.$kiko_message("已有此人，无需再选！")
          return false
        }
        if (that.content === "") {
          this.$kiko_message("请录入该发言者的谈话要点！")
          return false
        }
        data = {
          type: item.type,
          content: that.content,
          participator: item.participator,
          participatorName: item.memo,
        }
      } else {
        data = that.content
      }
      this.$emit('select_content', data)
      this.closeShow()
    },
    closeShow: function () {
      this.$emit('update:show', false)
    }
  }
}
</script>
