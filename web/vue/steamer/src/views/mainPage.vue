<template>
  <div class="mainPage">
    <el-container>
      <el-header>
        <el-row :gutter="20" class="header">
          <el-col :span="6">
            <div class="logoAll">
              <img class="logo" :src="zimuLg" alt="字幕精灵">
              <span class="logoTxt">字幕精灵</span>
            </div>
          </el-col>
          <el-col :span="18">
            <div class="txtRight">
              <el-button type="text" class="canzuoBtn" @click="goCaoZuo">操作说明</el-button>
              <el-dropdown class="dropdown">
                <span class="el-dropdown-link" v-if="user">
                  <img :src="user.imgPath" alt="touxiang" class="touxiang">
                  <span class="userii">
                    {{ user.userName }}（{{ org.oidName|| org.name }}）<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
<!--                    <el-dropdown-item><div class="downItem" @click="changeOrgBtn"><img :src="change" alt="" class="change"> 切换机构</div></el-dropdown-item>-->
                    <el-dropdown-item><div class="downItem last" @click="goOutBtn"><img :src="tui" alt="" class="tui"> 退出</div></el-dropdown-item>
<!--                    <el-dropdown-item disabled>双皮奶</el-dropdown-item>-->
<!--                    <el-dropdown-item divided>蚵仔煎</el-dropdown-item>-->
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-col>
        </el-row>
      </el-header>
      <el-main>
        <router-view/>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import auth from '@/sys/auth'
import zimuLogo from '@/assets/img/zimuSpritsLogo.png'
import change from '@/assets/img/change2.png'
import tui from '@/assets/img/tui.png'
import { logOut } from "@/api/api";
import { ElMessage } from 'element-plus'
export default {
  data() {
    return {
      zimuLg: zimuLogo,
      change: change,
      tui: tui,
      user:{},
      org:{}
    }
  },
  mounted() {
    this.user = auth.getUser()
    this.org = auth.getOrg()

    if(this.user.imgPath && this.user.imgPath.length > 10){

    }else{
      this.user.imgPath = zimuLogo
    }
    console.log('this.user =', this.user )
    console.log('this.org =', this.org )

  },
  methods: {
    goCaoZuo(){
      this.$router.push('operatInstruction')
    },
    changeOrgBtn(){
      this.$router.push('operatInstruction')
    },
    goOutBtn(){
      logOut().then(res => {
        console.log('logOut res=', res)
        let status = res.status
        if(status === 200){
          let curUrl = window.location.href
          window.location.href= curUrl.split('#')[0]
        }else{
          ElMessage.error('退出失败！')
        }

      }).catch(err => {
        console.log('logOut err=', err)

      })
    },

  }
}
</script>

<style lang="scss">
.redXing{  color: red; }
.fright{
  float: right;
}
</style>
<style lang="scss" scoped>
.change, .tui {display: inline-block; width: 20px; height:20px; margin-right: 10px; position: relative; top:5px;   }
.downItem{ padding-bottom: 6px; min-width: 200px; }
.downItem:not(.last){
  border-bottom: 1px solid #ccc;
}
.mainPage{
  font-size: 15px;
  .header{
    .tui{   }
    //border-bottom: 1px solid #ddd;
    .txtRight{
      text-align: right;
      font-size: 16px;
      font-weight: bold;
      margin-top: 10px;

      .touxiang{
        display: inline-block;
        width: 20px;
        box-shadow: 0 0 2px #000;
        border-radius: 10px;
        position: relative;
        top:5px;
        margin-right: 10px;
      }
      .userii{
        position: relative;
      }
    }
    .canzuoBtn{ position: relative;  margin-right: 20px;
    }
    .logoAll{
      text-align: left;
    }
    .logo{
      display: inline-block;
      height: 50px;
    }
    .logoTxt{
      font-size: 20px;
      font-weight: bolder;
      color: #7524fd;
      position: relative;
      top:-15px;
    }


  }
}
</style>
