<template>
  <div class="productFeatures" v-loading="uploadLoading">
    <div v-if="mainNum === 1">
      <div class="mainHead">
        <el-row >
          <el-col :span="14"></el-col>
          <el-col :span="8" class="txtRight">什么是产品特性？</el-col>
          <el-col :span="2" class="txtRight"><el-link type="info" :underline="false">查看</el-link></el-col>
        </el-row>
        <el-row >
          <el-col :span="6">有待产品特性初始化的产品共如下{{ unInitPt.length }}条：</el-col>
          <el-col :span="8"><el-link type="primary" :underline="false" @click="getRankListBtn">特性等级设置</el-link></el-col>
          <el-col :span="8" class="txtRight">产品特性初始化已完成的产品共{{ yszNum }}项</el-col>
          <el-col :span="2" class="txtRight"><el-link type="primary" :underline="false" @click="initedPt">查看</el-link></el-col>
        </el-row>
      </div>
      <div class="mainBody">
        <el-table :data="unInitPt" border="true" header-align="center" :cell-style="indexTbStyle" :header-cell-style="tableCellStyle" style="width: 100%">
          <el-table-column label="图号（代号）/名称/规格/型号/计量单位">
            <template #default="scope">
              <span>{{ scope.row.innerSn}}/{{ scope.row.name}}/{{ scope.row.specifications}}/{{ scope.row.model}}/{{ scope.row.unit}}</span>
            </template>
          </el-table-column>
          <el-table-column label="单重" align="center">
            <template #default="scope">
              <span>{{ scope.row.netWeight }} {{ chargeUnit(scope.row.weightUnit, 'weightUnit') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品的创建" align="center">
            <template #default="scope">
              <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标记有产品特性顺序号的图纸" align="center">
            <template #default="scope">
              <span>{{ scope.row.xhPath ? '已':'未' }}上传</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button size="mini" @click="ptFeaturesInit(scope.row)" type="primary" plain>开始初始化</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pageCon">
          <TyPage
              :curPage="indexPage.currentPageNo" :pageSize="indexPage.pageSize"
              :allPage="indexPage.totalPage" :pageClickFun="getProductPage"></TyPage>
        </div>
      </div>
    </div>
    <div v-if="mainNum === 2">
      <div class="backPage">
        <el-button type="primary" @click="backPre(77);">返 回</el-button>
        <div class="ty-right tjAll" v-show="initState === 0">
          <i :class="{'fa fa-circle': sflag, 'fa fa-circle-o': !sflag}" @click="toogeCircle"></i>
          本产品的产品特性初始化已完成
          <el-button type="primary" class="gapLtFar" :disabled="!sflag" @click="initFinishSure()">确  定</el-button>
        </div>
      </div>
      <div class="ty-clear">
        <div class="ty-left keepCon">
          <div class="gapLarg">{{ productDetail.innerSn}}/{{ productDetail.name}}/{{ productDetail.specifications}}/{{ productDetail.model}}/{{ productDetail.unit}}/{{ productDetail.netWeight}}</div>
          <el-row>
            <el-col :span="14">本产品已设置产品特性共{{ productFeatureList.length }}项</el-col>
            <el-col :span="6" v-show="initState === 0"> <el-link type="primary" :underline="false" @click="addPtFeaturesBtn()">增加</el-link></el-col>
            <el-col :span="4" v-show="initState === 0"><el-link type="primary" :underline="false" @click="pdImageManage()">图纸管控</el-link></el-col>
          </el-row>
        </div>
        <div class="ty-right pictureArea">
          <div class="tipSpace" v-if="!pdImageInfo.xhImage || pdImageInfo.xhImage === 'null'">
            <div>标记有控制点顺序号的图纸尚未上传！</div>
            <div>要上传，请点击左下方的“图纸管控”按钮！</div>
          </div>
          <img v-if="pdImageInfo.xhImage && pdImageInfo.xhImage !== 'null'" :src="realPath(pdImageInfo.xhImage.uplaodPath)" class="avatar" />
        </div>
      </div>
      <el-table
          :data="productFeatureList"
          border="true"
          header-align="center"
          :cell-style="tableCellStyle"
          :header-cell-style="tableCellStyle"
          style="width: 100%">
        <el-table-column align="left" label="产品特性" class="centerHead">
          <template #default="scope">
            <span class="orderCss">{{scope.row.orders}}</span>
            {{scope.row.name}}
            <span v-show="scope.row.category === 1">
              {{scope.row.baseSize}}
              <span v-if="Number(scope.row.ecartSuperieur) === Number(scope.row.ecartInferieur)">{{'土' + scope.row.ecartSuperieur}}</span>
            <div v-else class="iconEcartWrap">
              <div class="iconEcart">{{scope.row.ecartSuperieur > 0? '+' + scope.row.ecartSuperieur: scope.row.ecartSuperieur}}</div><div class="iconEcart">{{scope.row.ecartInferieur > 0? '+' + scope.row.ecartInferieur: scope.row.ecartInferieur}}</div>
            </div>
            </span>
            <span v-show="scope.row.category >= 2 && scope.row.category <= 7">
              {{scope.row.baseSize && scope.row.baseSize !== "" ? '基准值为'+scope.row.baseSize + sizeUnitCharge(scope.row.sizeUnit): '无基准值'}}
              {{scope.row.ecartSuperieur && scope.row.ecartSuperieur !== "" ? '上限为' + scope.row.ecartSuperieur + sizeUnitCharge(scope.row.sizeUnit): '无上限'}}
              {{scope.row.ecartInferieur && scope.row.ecartInferieur !== "" ? '下限为' + scope.row.ecartInferieur + sizeUnitCharge(scope.row.sizeUnit): '无下限'}}
            </span>
            <span v-show="scope.row.category >= 8 && scope.row.category <= 14">
              {{scope.row.controlContent}}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="特性等级" width="100">
          <template #default="scope">
            <span class="icon iconfont" :class="scope.row.rankSymbol">{{scope.row.rankSymbol === ''? '无符号':''}}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="检查方法">
          <template #default="scope">
            {{scope.row.itemCheckList1.length > 0 ? '可采用检测设备/仪器' + scope.row.itemCheckList1.length + '种': ''}}
            {{scope.row.itemCheckList2.length > 0 ? '可采用量具/检具等检验工具' + scope.row.itemCheckList2.length + '种': ''}}
            {{scope.row.itemCheckList3.length > 0 ? '可采用设备/工具以外的方法' + scope.row.itemCheckList3.length + '种': ''}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="备注" width="200">
          <template #default="scope">
            <div class="text-ellipsis">
              <span v-show="scope.row.category >= 1 && scope.row.category <= 7" :title="'测量值恰好为上限时'+ (scope.row.upperQualified === 1 ? '算': '不算') + '合格，测量值恰好为下限时' + (scope.row.lowerQualified === 1 ? '算': '不算') +'合格'">
                测量值恰好为上限时{{scope.row.upperQualified === 1 ? '算': '不算'}}合格，测量值恰好为下限时{{scope.row.lowerQualified === 1 ? '算': '不算'}}合格
              </span>
              <span v-show="scope.row.category === 9">所录入纯文本的字数上限为{{scope.row.textLimit}}</span>
              <span v-show="scope.row.category >= 10 && scope.row.category <= 11">所录入值的小数点后可带{{scope.row.decimalsLimit}}位有效数字</span>
              <span v-show="scope.row.category === 8 || scope.row.category >= 12 && scope.row.category <= 14"></span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建">
          <template #default="scope">
            {{ scope.row.updateName }} {{ new Date(scope.row.updateDate).format('yyyy-MM-dd hh:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <div v-show="initState === 0">
              <el-button type="primary" plain @click="ptFeaturesScan(scope.row)">查看</el-button>
              <el-button type="primary" plain @click="ptFeaturesEdit(scope.row)">修改</el-button>
              <el-button type="primary" plain @click="ptFeaturesDel(scope.row)">删除</el-button>
            </div>
            <div v-show="initState === 1">
              <el-button type="primary" plain @click="ptFeaturesScan(scope.row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div>特性符号图例：
        <span v-for="(item, index) in rankScanData" v-bind:key="index">
          {{item.name}}：<span class="icon iconfont" :class="item.symbol">{{item.symbol === ''? '无符号':''}}</span>
          {{index === rankScanData.length-1 ? '':'；'}}
        </span>
      </div>
    </div>
    <div v-if="mainNum === 3">
      <div class="backPage">
        <el-button type="primary" @click="backPre(1);">返 回</el-button>
      </div>
      <div class="mainHead">
        <el-row >
          <el-col :span="24">以下{{ initPt.length }}项产品的产品特性初始化已完成，发生设计变更或发现需修改的笔误时，请到工序-产品特性中操作！</el-col>
        </el-row>
      </div>
      <div class="mainBody">
        <el-table :data="initPt" border="true" header-align="center" :cell-style="indexTbStyle" :header-cell-style="tableCellStyle" style="width: 100%">
          <el-table-column label="图号（代号）/名称/规格/型号/计量单位">
            <template #default="scope">
              <span>{{ scope.row.innerSn}}/{{ scope.row.name}}/{{ scope.row.specifications}}/{{ scope.row.model}}/{{ scope.row.unit}}</span>
            </template>
          </el-table-column>
          <el-table-column label="单重" align="center">
            <template #default="scope">
              <span>{{ scope.row.netWeight }} {{ chargeUnit(scope.row.weightUnit, 'weightUnit') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品的创建" align="center">
            <template #default="scope">
              <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品特性初始化的完成" align="center">
            <template #default="scope">
              <span>{{ scope.row.xhPath ? '已':'未' }}上传</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button size="mini" @click="ptFeaturesInit(scope.row)" type="primary" plain>查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <TyDialog
        v-show="addPtFeaturesLog"
        :dialogTitle="featureTtl"
        width="850"
        :dialogHide="addPtFeaturesHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="addPtFeaturesHide">取 消</el-button>
        <el-button class="bounce-ok" @click="addPtFeaturesSure">完 成</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <div>
            <el-row>
              <el-col :span="24">{{ productDetail.innerSn}}/{{ productDetail.name}}/{{ productDetail.specifications}}/{{ productDetail.model}}/{{ productDetail.unit}}/{{ productDetail.netWeight}}</el-col>
            </el-row>
            <el-row v-show="logType === 'add'">
              <el-col :span="20">标记有产品特性顺序号的图纸</el-col>
              <el-col :span="4" class="txtRight"> <el-link type="primary" @click="pdImageManage()">图纸管控</el-link></el-col>
            </el-row>
            <div v-if="logType === 'edit'">
              <el-row>
                <el-col :span="10">图片—标记有产品特性顺序号的图纸</el-col>
                <el-col :span="6" v-if="editImageInfo.xhImage === null"> </el-col>
                <el-col :span="6" class="ty-center" v-if="editImageInfo.xhImage === null">
                  <el-upload
                      multiple
                      v-model:file-list="xhImageList"
                      :headers="imgUpload.uploadHeaders"
                      :action="imgUpload.uploadAction"
                      :accept="imgUpload.uploadLyc"
                      :data="imgUpload.postData"
                      :before-upload="uploadLoadingFun"
                      :on-success="
                (response, file, fileList) => {
                  return pdImgSuccess(response, file, fileList, 2)
                }
                "
                      :show-file-list="false"
                  >
                    <el-link type="primary" :underline="false">上传</el-link>
                  </el-upload>
                </el-col>
                <el-col :span="4" v-if="editImageInfo.xhImage !== null" class="txtRight">
                  <el-link type="primary" :underline="false" :path="editImageInfo.xhImage.uplaodPath" :download="editImageInfo.xhImage.uplaodPath" @click="oprationFile($event, 1)">查看</el-link>
                </el-col>
                <el-col :span="4" v-if="editImageInfo.xhImage !== null" class="txtRight largeFont">
                  <el-upload
                      multiple
                      v-model:file-list="xhImageList"
                      :headers="imgUpload.uploadHeaders"
                      :action="imgUpload.uploadAction"
                      :accept="imgUpload.uploadLyc"
                      :data="imgUpload.postData"
                      :before-upload="uploadLoadingFun"
                      :on-success="(response, file, fileList) => {
                  return pdImgSuccess(response, file, fileList, 2)
                }
                "
                      :show-file-list="false"
                  >
                    <el-link type="primary" :underline="false">更换</el-link>
                  </el-upload>
                </el-col>
                <el-col :span="4" class="ty-center" v-if="editImageInfo.xhImage !== null">
                  <el-link type="primary" :underline="false" @click="pdImageDel(2)">移除</el-link>
                </el-col>
                <el-col :span="2" class="txtRight"><el-link type="primary" :underline="false" @click="pdImageRecord(2)">操作记录</el-link></el-col>
              </el-row>
              <el-row>
                <el-col :span="10">不带有控制点顺序号的图纸图片</el-col>
                <el-col :span="6" v-if="editImageInfo.cptzImage === null"> </el-col>
                <el-col :span="6"  class="ty-center" v-if="editImageInfo.cptzImage === null">
                  <el-upload
                      multiple
                      v-model:file-list="cptzImageList"
                      :headers="imgUpload.uploadHeaders"
                      :action="imgUpload.uploadAction"
                      :accept="imgUpload.uploadLyc"
                      :data="imgUpload.postData"
                      :before-upload="uploadLoadingFun"
                      :on-success="
                (response, file, fileList) => {
                  return pdImgSuccess(response, file, fileList, 1)
                }"
                      :show-file-list="false"
                  >
                    <el-link type="primary" :underline="false">上传</el-link>
                  </el-upload>
                </el-col>
                <el-col :span="4" v-if="editImageInfo.cptzImage !== null" class="txtRight">
                  <el-link type="primary" :underline="false" :path="editImageInfo.cptzImage.uplaodPath" :download="editImageInfo.cptzImage.uplaodPath" @click="oprationFile($event, 1)">查看</el-link>
                </el-col>
                <el-col :span="4" class="txtRight largeFont" v-if="editImageInfo.cptzImage !== null">
                  <el-upload
                      multiple
                      v-model:file-list="fileList"
                      :headers="imgUpload.uploadHeaders"
                      :action="imgUpload.uploadAction"
                      :accept="imgUpload.uploadLyc"
                      :data="imgUpload.postData"
                      :before-upload="uploadLoadingFun"
                      :on-success="
                (response, file, fileList) => {
                  return pdImgSuccess(response, file, fileList, 1)
                }"
                      :show-file-list="false"
                  >
                    <el-link type="primary" :underline="false">更换</el-link>
                  </el-upload>
                </el-col>
                <el-col :span="4"  class="ty-center" v-if="editImageInfo.cptzImage !== null">
                  <el-link type="primary" :underline="false" @click="pdImageDel(1)">移除</el-link>
                </el-col>
                <el-col :span="2" class="txtRight"><el-link type="primary" :underline="false" @click="pdImageRecord(1)">操作记录</el-link></el-col>
              </el-row>
              <el-row>
                <el-col :span="10">与系统“文件与资料”中所关联的文件</el-col>
                <el-col :span="4" class="txtRight"><el-link type="primary" :underline="false" @click="relateResourceSee()">查看</el-link></el-col>
                <el-col :span="2"></el-col>
                <el-col :span="6" class="ty-center"><el-link type="primary" :underline="false" @click="addFile(1)">增加文件</el-link></el-col>
                <el-col :span="2" class="txtRight"><el-link type="primary" :underline="false" @click="pdImageRecord(3)">操作记录</el-link></el-col>
              </el-row>
              <div class="kk-hr"></div>
            </div>
            <el-form :key="featureFrom.category" ref="featureFrom" :model="featureFrom" :rules="addFeaturerules" label-position="left" label-width="140px">
              <el-form-item label="产品特性的顺序号" prop="orders">
                <el-input v-model="featureFrom.orders" placeholder="请录入上图中的某个顺序号" @input="limitIntWord()"></el-input>
              </el-form-item>
              <el-form-item label="该特性的类型" prop="category">
                <el-select v-model="featureFrom.category" placeholder="请选择" @change="changeCatergray">
                  <el-option label="尺寸" value='1'></el-option>
                  <el-option label="类似尺寸，有基准值，并有上限的计量型特性" value='2'></el-option>
                  <el-option label="有基准值与上限，但无下限的计量型特性" value="3"></el-option>
                  <el-option label="有基准值与下限，但无上限的计量型特性" value="4"></el-option>
                  <el-option label="没有基准值，仅有上限与下限的计量型特性" value="5"></el-option>
                  <el-option label="没有基准值与上限，仅有下限的计量型特性" value="6"></el-option>
                  <el-option label="没有基准值与下限，仅有上限的计量型特性" value="7"></el-option>
                  <el-option label="需在“合格”与“不合格”中选择的计数型特性" value="8"></el-option>
                  <el-option label="需在自定义的两个或多个选项中选择的计数型特性" value="9"></el-option>
                  <el-option label="需录入为纯文本的数据的计数型特性" value="10"></el-option>
                  <el-option label="需录入百分比（百分号系统自带）的计数型特性" value="11"></el-option>
                  <el-option label="需在日历中选择某日期的计数型特性" value="12"></el-option>
                  <el-option label="需在日历中选择某月份的计数型特性" value="13"></el-option>
                  <el-option label="需在日历中选择某年份的计数型特性" value="14"></el-option>
                </el-select>
              </el-form-item>
              <div class="kk-hr" v-show="featureFrom.category >= 1 && featureFrom.category <= 14"></div>
              <div v-if="Number(featureFrom.category) === 1">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="产品特性的名称">
                      <el-input v-model="featureFrom.name" value="尺寸" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="单位" class="right-align">
                      <el-select v-model="featureFrom.sizeUnit" placeholder="请选择">
                        <el-option
                            v-for="item in sizeUnitArr"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="基本尺寸" prop="baseSize" :rules="[
                      { required: true, message: '', trigger: 'blur'   },
                      { validator: this.baseSizeRule, trigger: 'blur' }]">
                      <el-input v-model="featureFrom.baseSize"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="上偏差" class="right-align" prop="ecartSuperieur" :rules="{ required: true, validator: checkIsTen, trigger: 'blur'   }">
                      <el-input v-model="featureFrom.ecartSuperieur" :class="{'isBlueTip': isBlueTip}"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="下偏差" prop="ecartInferieur" :rules="{ required: true, validator: checkIsTen, trigger: 'blur'   }">
                      <el-input v-model="featureFrom.ecartInferieur" :class="{'isBlueTip': isBlueTip}"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="特性等级" class="right-align">
                      <el-select v-model="featureFrom.rank" placeholder="请选择">
                        <el-option
                            v-for="item in rankScanData"
                            :key="item.id"
                            :label="item.symbol"
                            :value="item.id"
                        >
                          <span class="icon iconfont" :class="item.symbol">{{item.symbol === ''? '无符号':''}}</span>
                        </el-option>
                        <template #label>
                          <span class="icon iconfont" :class="rankSymbolScan">{{featureFrom.rank === ''? '无符号':''}}</span>
                        </template>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="space-between">
                  <el-col :span="12" class="midSize">
                    <el-form-item label="测量值恰好为上限时是否算合格？" required label-width="100%"></el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="upperQualified" :rules="[
                      { required: true, message: '请选择', trigger: 'change' }]">
                      <el-radio-group v-model="featureFrom.upperQualified">
                        <el-radio label="1">算合格</el-radio>
                        <el-radio label="0">算不合格</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="space-between">
                  <el-col :span="12" class="midSize">
                    <el-form-item label="测量值恰好为下限时是否算合格？" required label-width="100%"></el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="lowerQualified" :rules="[
                      { required: true, message: '请选择', trigger: 'change' }]">
                      <el-radio-group v-model="featureFrom.lowerQualified">
                        <el-radio label="1">算合格</el-radio>
                        <el-radio label="0">算不合格</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div v-if="featureFrom.category >= 2 && featureFrom.category <= 7">
                <el-row>
                  <el-col :span="24" class="txtRight">
                    <el-link type="primary" @click="addUnit">新增</el-link>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="产品特性的名称" prop="name" :rules="[
                      { required: true, message: '', trigger: 'blur' }
                  ]">
                      <el-input v-model="featureFrom.name" maxlength="20"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="单位" class="right-align" prop="sizeUnit" :rules="[
                  { required: true, message: '', trigger: 'change' }
                  ]">
                      <el-select v-model="featureFrom.sizeUnit" placeholder="请选择">
                        <el-option
                            v-for="item in unitList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item v-if="Number(featureFrom.category) < 5" label="基准值" prop="baseSize" :rules="[
                      { required: true, message: '', trigger: 'blur'   },
                      { validator: baseSizeRule, message: '系统不支持录入小数点后有效数字超过10位的数字！', trigger: 'blur' }]">
                      <el-input v-model="featureFrom.baseSize"></el-input>
                    </el-form-item>
                    <el-form-item v-if="Number(featureFrom.category) >= 5 && Number(featureFrom.category) <= 7" label="基准值" prop="baseSize">
                      <el-input v-model="featureFrom.baseSize" :disabled="true"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item v-if="Number(featureFrom.category) !== 4 && Number(featureFrom.category) !== 6" label="上限" class="right-align" prop="ecartSuperieur"
                                  :rules="{ required: true, validator: checkIsTen, trigger: 'blur'   }"
                    >
                      <el-input v-model="featureFrom.ecartSuperieur" :class="{'isBlueTip': isBlueTip}"></el-input>
                    </el-form-item>
                    <el-form-item v-if="Number(featureFrom.category) === 4 || Number(featureFrom.category) === 6" label="上限" class="right-align" prop="ecartSuperieur">
                      <el-input v-model="featureFrom.ecartSuperieur" :disabled="true"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item v-if="Number(featureFrom.category) !== 3 && Number(featureFrom.category) !== 7" label="下限" prop="ecartInferieur" :rules="[
                      { required: true,validator: checkIsTen, trigger: 'blur'   }]">
                      <el-input v-model="featureFrom.ecartInferieur" :class="{'isBlueTip': isBlueTip}"></el-input>
                    </el-form-item>
                    <el-form-item v-if="Number(featureFrom.category) === 3 || Number(featureFrom.category) === 7" label="下限" prop="ecartInferieur">
                      <el-input v-model="featureFrom.ecartInferieur" :disabled="true"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="特性等级" class="right-align">
                      <el-select v-model="featureFrom.rank" placeholder="请选择">
                        <el-option
                            v-for="item in rankScanData"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                          <span class="icon iconfont" :class="item.symbol">{{item.symbol === ''? '无符号':''}}</span>
                        </el-option>
                        <template #label>
                          <span class="icon iconfont" :class="rankSymbolScan">{{featureFrom.rank === ''? '无符号':''}}</span>
                        </template>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="space-between" v-if="Number(featureFrom.category) === 2 || Number(featureFrom.category) === 3 || Number(featureFrom.category) === 5 || Number(featureFrom.category) === 7">
                  <el-col :span="12" class="midSize">
                    <el-form-item label="测量值恰好为上限时是否算合格？" required label-width="100%"></el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="upperQualified" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                      <el-radio-group v-model="featureFrom.upperQualified">
                        <el-radio label="1">算合格</el-radio>
                        <el-radio label="0">算不合格</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="space-between" v-if="Number(featureFrom.category) === 2 || Number(featureFrom.category) >= 4 && Number(featureFrom.category) <= 6">
                  <el-col :span="12" class="midSize">
                    <el-form-item label="测量值恰好为下限时是否算合格？" required label-width="100%"></el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="lowerQualified" :rules="[
                      { required: true, message: '请选择', trigger: 'change' }]">
                      <el-radio-group v-model="featureFrom.lowerQualified">
                        <el-radio label="1">算合格</el-radio>
                        <el-radio label="0">算不合格</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div v-if="Number(featureFrom.category) >= 8 && Number(featureFrom.category) <= 14">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="产品特性的名称" prop="name" :rules="[
                      { required: true, message: '', trigger: 'blur' }
                  ]">
                      <el-input v-model="featureFrom.name" maxlength="20"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="特性等级" class="right-align">
                      <el-select v-model="featureFrom.rank" placeholder="请选择">
                        <el-option
                            v-for="item in rankScanData"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                          <span class="icon iconfont" :class="item.symbol">{{item.symbol === ''? '无符号':''}}</span>
                        </el-option>
                        <template #label>
                          <span class="icon iconfont" :class="rankSymbolScan">{{featureFrom.rank === ''? '无符号':''}}</span>
                        </template>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="要控制的内容" prop="controlContent" :rules="[
                      { required: true, message: '', trigger: 'blur' },{ validator: this.checkContent, message: '', trigger: 'blur' }
                  ]">
                      <el-input v-model="featureFrom.controlContent" maxlength="100"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="Number(featureFrom.category) === 9" class="txtRight">
                  <el-col :span="24">
                    <el-link type="primary" @click="optionSettings" :underline="false">选项设置</el-link>
                  </el-col>
                </el-row>
                <el-row v-if="Number(featureFrom.category) === 9">
                  <el-col :span="24">
                    <el-form-item label="选项" prop="featureOptionListStr" :rules="[
                      { required: true, message: '', trigger: 'blur' }
                  ]">
                      <el-input v-model="featureFrom.featureOptionListStr" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="featureFrom.category === '10'">
                  <el-col :span="24">
                    <el-form-item label="所录入纯文本的字数上限" prop="textLimit" class="limitHt" :rules="[
                       { required: true, message: '', trigger: 'blur' },
          { pattern: /^(\d{1,2}||100)/, message: '', trigger: 'blur' }
                  ]">
                      <el-input v-model="featureFrom.textLimit" placeholder="—请录入不大于100的正整数—" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="featureFrom.category === '11'">
                  <el-col :span="24">
                    <el-form-item label="所录入值的小数点后可带几位有效数字" prop="decimalsLimit" class="limitHt" :rules="[
                       { required: true, message: '', trigger: 'blur' },
          { pattern: /^(\d{1,2}||100)/, message: '', trigger: 'blur' }
                  ]">
                      <el-input v-model="featureFrom.decimalsLimit" placeholder="—请录入不大于100的正整数—" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div v-show="featureFrom.category >= 1 && featureFrom.category <= 14">
                <div class="kk-hr"></div>
                <el-row>
                  <el-col :span="24">
                    本产品特性可采用哪些方法检查？请至少设置一项！
                    <span class="ty-color-red">*</span>
                  </el-col>
                </el-row>
                <el-checkbox-group class="testRadios" v-model="examArr" @change="changeExam">
                  <el-row justify="space-between">
                    <el-col :span="12">
                      <el-checkbox label="检测设备/仪器" value="1" />
                    </el-col>
                    <el-col :span="5" class="txtRight flexBox">
                      <el-text class="mx-1">已选{{featureFrom.itemCheckJson1.length}}种</el-text>
                    </el-col>
                    <el-col :span="4" class="txtRight">
                      <el-link type="primary" :underline="false" :disabled="examArr.indexOf('1') === -1" @click="testEquipmentScan(1)">查看</el-link>
                    </el-col>
                    <el-col :span="3" class="txtRight">
                      <el-link type="primary" :underline="false" :disabled="examArr.indexOf('1') === -1" @click="selectTestMethod(1)">去选择</el-link>
                    </el-col>
                  </el-row>
                  <el-row justify="space-between">
                    <el-col :span="12">
                      <el-checkbox value="2" label="量具/检具或检验工具" />
                    </el-col>
                    <el-col :span="5" class="txtRight flexBox">
                      <el-text class="mx-1">已选{{featureFrom.itemCheckJson2.length}}种</el-text>
                    </el-col>
                    <el-col :span="4" class="txtRight">
                      <el-link type="primary" :underline="false" :disabled="examArr.indexOf('2') === -1" @click="testEquipmentScan(2)">查看</el-link>
                    </el-col>
                    <el-col :span="3" class="txtRight">
                      <el-link type="primary" :underline="false" :disabled="examArr.indexOf('2') === -1" @click="selectTestMethod(2)">去选择</el-link>
                    </el-col>
                  </el-row>
                  <el-row justify="space-between">
                    <el-col :span="12">
                      <el-checkbox label="设备/工具以外的方法" value="3" />
                    </el-col>
                    <el-col :span="5" class="txtRight flexBox">
                      <el-text class="mx-1">已选{{featureFrom.itemCheckJson3.length}}种</el-text>
                    </el-col>
                    <el-col :span="4" class="txtRight">
                      <el-link type="primary" :underline="false" :disabled="examArr.indexOf('3') === -1" @click="testEquipmentScan(3)">查看</el-link>
                    </el-col>
                    <el-col :span="3" class="txtRight">
                      <el-link type="primary" :underline="false" :disabled="examArr.indexOf('3') === -1" @click="selectTestMethod(3)">去选择</el-link>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </div>
            </el-form>
          </div>
        </div>
      </template>
    </TyDialog>

    <TyDialog
        v-if="ptFeaturesScanLog"
        dialogTitle="查看产品特性"
        width="900"
        :dialogHide="ptFeaturesScanHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="ptFeaturesScanHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="fScan">
          <div style="width: 800px; margin: 0 auto;">
            <el-row>
              <el-col :span="24">{{ productDetail.innerSn}}/{{ productDetail.name}}/{{ productDetail.specifications}}/{{ productDetail.model}}/{{ productDetail.unit}}/{{ productDetail.netWeight}}</el-col>
            </el-row>
            <el-row>
              <el-col :span="16">图片—标记有产品特性顺序号的图纸</el-col>
              <el-col :span="2">{{ pdImageInfo.xhImage ? '已':"未" }}上传</el-col>
              <el-col :span="3" class="txtRight">
                <el-link type="info" :underline="false" v-if="pdImageInfo.xhImage === null">查看</el-link>
                <el-link type="primary" :underline="false" v-if="pdImageInfo.xhImage !== null"
                         :path="pdImageInfo.xhImage !== null ? pdImageInfo.xhImage.uplaodPath: ''"
                         :download="pdImageInfo.xhImage !== null ? pdImageInfo.xhImage.uplaodPath: ''" @click="oprationFile($event, 1)">查看</el-link>
              </el-col>
              <el-col :span="3" class="txtRight">
                <el-link type="primary" @click="pdImageRecord(2)">操作记录</el-link>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">图片—图片格式的产品图纸</el-col>
              <el-col :span="2">{{ pdImageInfo.cptzImage ? '已':"未" }}上传</el-col>
              <el-col :span="3" class="txtRight">
                <el-link type="info" :underline="false" v-if="pdImageInfo.cptzImage === null" >查看</el-link>
                <el-link type="primary" :underline="false" v-if="pdImageInfo.cptzImage !== null" :path="pdImageInfo.cptzImage.uplaodPath" :download="pdImageInfo.cptzImage.uplaodPath" @click="oprationFile($event, 1)">查看</el-link>
              </el-col>
              <el-col :span="3" class="txtRight">
                <el-link type="primary" @click="pdImageRecord(1)">操作记录</el-link>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">本产品的图纸或其他有关的内部文件</el-col>
              <el-col :span="2">共{{ pdImageInfo.resourceList.length}}个</el-col>
              <el-col :span="3" class="txtRight">
                <el-link type="primary" :underline="false" @click="relateResourceSee()">查看</el-link>
              </el-col>
              <el-col :span="3" class="txtRight">
                <el-link type="primary" @click="pdImageRecord(3)">操作记录</el-link>
              </el-col>
            </el-row>
            <div class="kk-hr"></div>
            <div class="kk-decrease">
              <el-row>
                <el-col :span="14">产品特性的类型</el-col>
                <el-col :span="10" class="txtRight">计{{Number(ptFeatureDetail.category) >= 1 && Number(ptFeatureDetail.category) <= 7? '量':'数'}}型/{{categoryCharge}}</el-col>
              </el-row>
              <el-row>
                <el-col :span="4">产品特性的名称</el-col>
                <el-col :span="6">
                  <div :class="{'scanBox':Number(ptFeatureDetail.category) !== 1, 'grayBox': Number(ptFeatureDetail.category) === 1} ">
                    {{ptFeatureDetail.name}}
                  </div>
                </el-col>
                <el-col :span="4"></el-col>
                <el-col :span="10">
                  <span class="orderTtl">顺序号</span>
                  <span class="scanBox">
                  {{ptFeatureDetail.orders}}
                </span>
                  <div class="txtRight ty-color-blue">注：图纸等技术文件上本产品特性的顺序号。</div>
                </el-col>
              </el-row>
              <div v-show="ptFeatureDetail.category === 1">
                <el-row>
                  <el-col :span="4">单位</el-col>
                  <el-col :span="6">
                    <div class="scanBox">
                      {{chargeUnit(ptFeatureDetail.sizeUnit, 'sizeUnit')}}
                    </div>
                  </el-col>
                  <el-col :span="4"></el-col>
                  <el-col :span="4"><div class="ttlRt">基本尺寸</div></el-col>
                  <el-col :span="6">
                    <div class="scanBox">
                      {{ptFeatureDetail.baseSize}}
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="4">上偏差</el-col>
                  <el-col :span="6">
                    <div class="scanBox">
                      {{ptFeatureDetail.ecartSuperieur}}
                    </div>
                  </el-col>
                  <el-col :span="4"></el-col>
                  <el-col :span="4"><div class="ttlRt">下偏差</div></el-col>
                  <el-col :span="6">
                    <div class="scanBox">
                      {{ptFeatureDetail.ecartInferieur}}
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div v-show="ptFeatureDetail.category >= 2 && ptFeatureDetail.category <= 7">
                <el-row>
                  <el-col :span="4">单位</el-col>
                  <el-col :span="6">
                    <div class="scanBox">
                      {{ptFeatureDetail.unit}}
                    </div>
                  </el-col>
                  <el-col :span="4"></el-col>
                  <el-col :span="4"><div class="ttlRt">基准值</div></el-col>
                  <el-col :span="6">
                    <div :class="{'scanBox': Number(ptFeatureDetail.category) <= 4, 'grayBox': Number(ptFeatureDetail.category) > 4}">
                      {{ptFeatureDetail.baseSize}}
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="4">上限</el-col>
                  <el-col :span="6">
                    <div :class="{'scanBox': (Number(ptFeatureDetail.category) !== 4 && Number(ptFeatureDetail.category) !== 6),
                  'grayBox': Number(ptFeatureDetail.category) === 4 || Number(ptFeatureDetail.category) === 6}">
                      {{ptFeatureDetail.ecartSuperieur}}
                    </div>
                  </el-col>
                  <el-col :span="4"></el-col>
                  <el-col :span="4"><div class="ttlRt">下限</div></el-col>
                  <el-col :span="6">
                    <div :class="{'scanBox': Number(ptFeatureDetail.category) !== 3 && Number(ptFeatureDetail.category) !== 7,'grayBox': Number(ptFeatureDetail.category) === 3 || Number(ptFeatureDetail.category) === 7}">
                      {{ptFeatureDetail.ecartInferieur}}
                    </div>
                  </el-col>
                </el-row>
              </div>
              <el-row v-show="ptFeatureDetail.category >= 8">
                <el-col :span="4">要控制的内容</el-col>
                <el-col :span="20">{{ptFeatureDetail.controlContent}}</el-col>
              </el-row>
              <el-row v-show="Number(ptFeatureDetail.category) === 9">
                <el-col :span="4">选项</el-col>
                <el-col :span="20">{{featureOptionStr }}</el-col>
              </el-row>
              <el-row v-show="Number(ptFeatureDetail.category) === 10" class="smTtl">
                <el-col :span="4">所录入纯文本的字数上限</el-col>
                <el-col :span="20">{{ptFeatureDetail.textLimit}}</el-col>
              </el-row>
              <el-row v-show="Number(ptFeatureDetail.category) === 11" class="smTtl">
                <el-col :span="4">所录入值的小数点后可带几位有效数字</el-col>
                <el-col :span="20">{{ptFeatureDetail.decimalsLimit}}</el-col>
              </el-row>
            </div>
            <el-row v-show="ptFeatureDetail.ecartSuperieur && ptFeatureDetail.ecartSuperieur !== ''">
              <el-col :span="24">测量值恰好达到上限时，算{{[0, '0'].indexOf(ptFeatureDetail.upperQualified) > -1? "不":''}}合格。</el-col>
            </el-row>
            <el-row v-show="ptFeatureDetail.ecartInferieur && ptFeatureDetail.ecartInferieur !== ''">
              <el-col :span="24">测量值恰好达到下限时，算{{[0, '0'].indexOf(ptFeatureDetail.lowerQualified) > -1? "不":''}}合格。</el-col>
            </el-row>
            <el-row>
              <el-col :span="4">特性等级符号</el-col>
              <el-col :span="16">
                <span class="icon iconfont" :class="ptFeatureDetail.rankSymbol">{{ptFeatureDetail.rankSymbol === ''? '无符号':''}}</span>
              </el-col>
            </el-row>
            <div class="kk-hr"></div>
            <el-row>
              <el-col :span="24">检查本产品特性可采用的方法</el-col>
            </el-row>
            <el-row>
              <el-col :span="12">检测设备/仪器</el-col>
              <el-col :span="2">已选{{ptFeatureDetail.itemCheckList1.length}}种</el-col>
              <el-col :span="10" class="txtRight">
                <el-link type="primary" @click="itemCheckListScan(1)">查看</el-link>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">量具/检具等检验工具</el-col>
              <el-col :span="2">已选{{ptFeatureDetail.itemCheckList2.length}}种</el-col>
              <el-col :span="10" class="txtRight">
                <el-link type="primary" @click="itemCheckListScan(2)">查看</el-link>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">设备/工具以外的方法</el-col>
              <el-col :span="2">已选{{ptFeatureDetail.itemCheckList3.length}}种</el-col>
              <el-col :span="10" class="txtRight">
                <el-link type="primary" @click="itemCheckListScan(3)">查看</el-link>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="pdImageLog"
        dialogTitle="产品图纸管控"
        width="850"
        :dialogHide="pdImageHide"
    >
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div class="pdImageCss" style="width: 700px; margin: 0 auto">
          <el-row>
            <el-col :span="14">{{ productDetail.innerSn}}/{{ productDetail.name}}/{{ productDetail.specifications}}/{{ productDetail.model}}/{{ productDetail.unit}}/{{ productDetail.netWeight}}</el-col>
            <el-col :span="10" class="txtRight">
              <el-link type="primary" :underline="false" @click="imgExplainLog = true">操作说明</el-link>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div>标记有控制点顺序号的图纸图片</div>
              <div class="ty-color-blue">注：本图片将展示于系统的工艺、质量等操作人员所见页面上。如未上传，请上传！</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="16" v-if="!pdImageInfo.xhImage || pdImageInfo.xhImage === 'null'"></el-col>
            <el-col :span="5" class="ty-center" v-if="!pdImageInfo.xhImage || pdImageInfo.xhImage === 'null'">
              <el-upload
                  multiple
                  v-model:file-list="xhImageList"
                  :headers="imgUpload.uploadHeaders"
                  :action="imgUpload.uploadAction"
                  :accept="imgUpload.uploadLyc"
                  :data="imgUpload.postData"
                  :before-upload="uploadLoadingFun"
                  :on-success="
                (response, file, fileList) => {
                  return pdImgSuccess(response, file, fileList, 2)
                }
                "
                  :show-file-list="false"
              >
                <el-link type="primary" :underline="false">上传</el-link>
              </el-upload>
            </el-col>
            <el-col :span="15" class="txtRight" v-if="pdImageInfo.xhImage && pdImageInfo.xhImage !== 'null'">
              <el-link type="primary" :underline="false" :path="pdImageInfo.xhImage.uplaodPath" :download="pdImageInfo.xhImage.uplaodPath" @click="oprationFile($event, 1)">查看</el-link>
            </el-col>
            <el-col :span="2" v-if="pdImageInfo.xhImage && pdImageInfo.xhImage !== 'null'">
            </el-col>
            <el-col :span="2" class="largeFont" v-if="pdImageInfo.xhImage && pdImageInfo.xhImage !== 'null'" >
              <el-upload
                  multiple
                  v-model:file-list="xhImageList1"
                  :headers="imgUpload.uploadHeaders"
                  :action="imgUpload.uploadAction"
                  :accept="imgUpload.uploadLyc"
                  :data="imgUpload.postData"
                  :before-upload="uploadLoadingFun"
                  :on-success="
                (response, file, fileList) => {
                  return pdImgSuccess(response, file, fileList, 2)
                }
                "
                  :show-file-list="false"
              >
                <el-link type="primary" :underline="false">更换</el-link>
              </el-upload>
            </el-col>
            <el-col :span="2" class="linkGapLt" v-if="pdImageInfo.xhImage && pdImageInfo.xhImage !== 'null'">
              <el-link type="primary" :underline="false" @click="pdImageDel(2)">移除</el-link>
            </el-col>
            <el-col :span="3" class="txtRight"><el-link type="primary" :underline="false" @click="pdImageRecord(2)">操作记录</el-link></el-col>
          </el-row>
          <div class="kk-hr"></div>
          <el-row>
            <el-col :span="10">不带有控制点顺序号的图纸图片</el-col>
            <el-col :span="4">{{ pdImageInfo.cptzImage && pdImageInfo.cptzImage !== 'null' ? '已上传':"尚未上传" }}</el-col>
            <el-col :span="2" v-if="!pdImageInfo.cptzImage || pdImageInfo.cptzImage === 'null'"></el-col>
            <el-col :span="5"  class="ty-center" v-if="!pdImageInfo.cptzImage || pdImageInfo.cptzImage === 'null'">
              <el-upload
                  multiple
                  v-model:file-list="cptzImageList"
                  :headers="imgUpload.uploadHeaders"
                  :action="imgUpload.uploadAction"
                  :accept="imgUpload.uploadLyc"
                  :data="imgUpload.postData"
                  :before-upload="uploadLoadingFun"
                  :on-success="
                   (response, file, fileList) => {
                     return pdImgSuccess(response, file, fileList, 1)
                   }"
                  :show-file-list="false"
              >
                <el-link type="primary" :underline="false">上传</el-link>
              </el-upload>
            </el-col>
            <el-col :span="3" v-if="pdImageInfo.cptzImage && pdImageInfo.cptzImage !== 'null'">
              <el-link type="primary" :underline="false" :path="pdImageInfo.cptzImage.uplaodPath" :download="pdImageInfo.cptzImage.uplaodPath" @click="oprationFile($event, 1)">查看</el-link>
            </el-col>
            <el-col :span="2" v-if="pdImageInfo.cptzImage && pdImageInfo.cptzImage !== 'null'" class="largeFont">
              <el-upload
                  multiple
                  v-model:file-list="fileList1"
                  :headers="imgUpload.uploadHeaders"
                  :action="imgUpload.uploadAction"
                  :accept="imgUpload.uploadLyc"
                  :data="imgUpload.postData"
                  :before-upload="uploadLoadingFun"
                  :on-success="
                   (response, file, fileList) => {
                     return pdImgSuccess(response, file, fileList, 1)
                   }"
                  :show-file-list="false"
              >
                <el-link type="primary" :underline="false">更换</el-link>
              </el-upload>
            </el-col>
            <el-col :span="2" v-if="pdImageInfo.cptzImage && pdImageInfo.cptzImage !== 'null'">
              <el-link type="primary" :underline="false" @click="pdImageDel(1)">移除</el-link>
            </el-col>
            <el-col :span="3" class="txtRight"><el-link type="primary" :underline="false" @click="pdImageRecord(1)">操作记录</el-link></el-col>
          </el-row>
          <el-row>
            <el-col :span="10">与系统“文件与资料”中所关联的文件</el-col>
            <el-col :span="4">共{{ pdImageInfo.resourceList.length }}个</el-col>
            <el-col :span="2" class=""><el-link type="primary" :underline="false" @click="relateResourceSee()">查看</el-link></el-col>
            <el-col :span="5" class="ty-center"><el-link type="primary" :underline="false" @click="addFile(1)">增加文件</el-link></el-col>
            <el-col :span="3" class="txtRight"><el-link type="primary" :underline="false" @click="pdImageRecord(3)">操作记录</el-link></el-col>
          </el-row>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="imgExplainLog"
        dialogTitle="操作说明"
        width="850"
        :dialogHide="imgExplainHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="imgExplainHide">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div class="explainCon" style="width: 700px; margin: 0 auto">
          <div>1、工艺、质量或操作人员等想快速查看产品图纸时，可查看所上传的图片格式的产品图纸。</div>
          <div>2、工艺、质量等操作人员进行产品检验或其他工作时，往往需要图纸上的控制点带有统一的顺序号。另行上传标记有控制点顺序号的图片，有利于上述工作。</div>
          <div>
            3、在“图纸/其他技术文件”中，将内部文件与相应产品建立关联，有利于工艺、质量或操作等人员更方便地查看文件，同时也有利于对技术工作的整理梳理。
            <div class="ty-color-blue">注1 内部文件系指“文件与资料”中已按规则上传的文件；</div>
            <div class="ty-color-blue">注2 您无法选择没有使用权限的文件，如一定要选择，则请自行向管理员提出；</div>
            <div class="ty-color-blue">注3 工艺类或检验类的文件，建议在其他模块中由相关人员操作。</div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-show="testLog"
        :dialogTitle="(this.examType === 1 ?'选择检测设备':'选择检验工具')"
        width="850"
        :dialogHide="testLogHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="testLogHide">取 消</el-button>
        <el-button class="bounce-ok" @click="testEquipmentSure">确 定</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-row>
            <el-col :span="24">
              <div>请选择！</div>
              <div class="ty-color-blue">注：如需多选，请一项一项选。</div>
            </el-col>
          </el-row>
          <el-form ref="testEqForm" :rules="testEqFormRules" :model="testEqForm" label-width="230px" label-position="left">
            <el-form-item :label="this.examType === 1 ?'检测设备的名称':'量具/检具或其他检验工具的名称'" prop="id">
              <el-select v-model="testEqForm.id" placeholder="请选择" @change="getEquModelList(testEqForm.id)">
                <el-option
                    v-for="item in testEquipment"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="this.examType === 1 ?'检测设备的型号':'量具/检具或其他检验工具的型号'" prop="model">
              <el-select v-model="testEqForm.model" placeholder="请选择">
                <el-option
                    v-for="item in testEquipmentModel"
                    :key="item.mid"
                    :label="item.modelName"
                    :value="item.mid"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="kk-hr"></div>
          <el-row>
            <div v-show="examType === 1">您所选择的检测设备共有{{testEquipmentModel.length}}台/套</div>
            <div v-show="examType === 2">您所选择的检验工具共有{{testEquipmentModel.length}}台/套</div>
          </el-row>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-show="controlMethodsLog"
        dialogTitle="选择控制方法"
        width="1200"
        :dialogHide="controlMethodsHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="controlMethodsHide">取 消</el-button>
        <el-button class="bounce-ok" @click="testEquipmentSure">确 定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-row>
            <el-col :span="19">请选择（可多选）！</el-col>
            <el-col :span="3"><el-link type="primary" @click="getControlList(0)">已停用的控制方法</el-link></el-col>
            <el-col :span="2"><el-link type="primary" @click="addControlMethod">增加控制方法</el-link></el-col>
          </el-row>
          <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="controlMethodsList"
              header-align="center"
              :cell-style="tableCellStyle"
              :header-cell-style="tableCellStyle"
              fit style="width: 100%">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column align="center" prop="code" label="系统赋予的编码" width="150"> </el-table-column>
            <el-table-column align="center" prop="name" label="控制方法的名称" width="150"> </el-table-column>
            <el-table-column align="center" prop="briefDesc" label="简要说明"> </el-table-column>
            <el-table-column align="center" label="创建" width="300">
              <template #default="scope">
                <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="300">
              <template #default="scope">
                <div class="linkGap">
                  <el-link type="primary" :underline="false" @click="controlMethodEdit(scope.row, 1)">修改</el-link>
                  <el-link type="primary" :underline="false" @click="controlMethodStop(0, scope.row.id, scope.$index)"> 停用</el-link>
                  <el-link type="primary" :underline="false" @click="controlMethodDel(scope.row.id, 1, scope.$index)">删除</el-link>
                  <el-link type="primary" :underline="false" @click="controlMethodRecordBtn(scope.row.id)">操作记录</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="controlMethodsSusLog"
        dialogTitle="已停用的控制方法"
        width="1100"
        :dialogHide="controlMethodsSusHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="controlMethodsSusHide">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-table
              tooltip-effect="dark"
              :data="controlMethodsSusList"
              header-align="center"
              fit style="width: 100%">
            <el-table-column align="center" prop="code" label="系统赋予的编码"> </el-table-column>
            <el-table-column align="center" prop="name" label="控制方法的名称"> </el-table-column>
            <el-table-column align="center" prop="briefDesc" label="简要说明"> </el-table-column>
            <el-table-column align="center" label="创建" width="220">
              <template #default="scope">
                <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="停用时间" width="220">
              <template #default="scope">
                <span>{{ scope.row.updateName }} {{ new Date(scope.row.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="220">
              <template #default="scope">
                <div class="linkGap">
                  <el-link type="primary" @click="controlMethodEdit(scope.row, 0)">修改</el-link>
                  <el-link type="primary" @click="controlMethodStop(1, scope.row.id,scope.$index)">启用</el-link>
                  <el-link type="primary" @click="controlMethodDel(scope.row.id, 0, scope.$index)">删除</el-link>
                  <el-link type="primary" @click="controlMethodRecordBtn(scope.row.id)">操作记录</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="controlMethodRecordLog"
        dialogTitle="操作记录"
        width="850"
        :dialogHide="controlMethodRecordHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="controlMethodRecordHide">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-table
              :data="controlMethodRecord"
              border="true"
              header-align="center"
              style="width: 100%">
            <el-table-column align="center" prop="code" label="操作的名称">
              <template #default="scope">
                <span>{{ chargeOperation(scope.row.operation, 4) }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作者与时间">
              <template #default="scope">
                <span v-if="scope.$index === 0">{{scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
                <span v-else>{{ scope.row.updateName }} {{ new Date(scope.row.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作后结果的查看">
              <template #default="scope">
                <div v-if="scope.row.operation === 4 || scope.row.operation === 5" class="ty-color-blue">——</div>
                <el-button v-else @click="controlMethodRecordScan(scope.row.id)" type="primary" plain>查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="controlMethodRecordScanLog"
        dialogTitle="控制方法查看"
        width="850"
        :dialogHide="controlMethodRecordScanHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="controlMethodRecordScanHide">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-row>
            <el-col :span="8">控制方法的名称</el-col>
            <el-col :span="16">{{controlMethodRecordDetail.name	}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">简要说明</el-col>
            <el-col :span="16">{{controlMethodRecordDetail.briefDesc}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">系统赋予的编码</el-col>
            <el-col :span="16">{{controlMethodRecordDetail.code}}</el-col>
          </el-row>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="addcontrolMethodLog"
        :dialogTitle="(editType === 'add' ? '增加控制方法':'修改自定义的检验方式')"
        width="850"
        :dialogHide="addcontrolMethodHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="addcontrolMethodHide">取 消</el-button>
        <el-button class="bounce-ok" @click="addControlMethodSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 760px;margin: 0 auto;">
          <el-form ref="controlMethod" :model="controlMethod" :rules="controlMethodRule" label-width="150px" label-position="left">
            <el-form-item label="控制方法的名称" prop="name">
              <el-input v-model="controlMethod.name" clearable></el-input>
            </el-form-item>
            <el-form-item label="简要说明">
              <el-input v-model="controlMethod.briefDesc" clearable></el-input>
            </el-form-item>
            <el-form-item label="系统赋予的编码">
              <el-input v-model="controlMethod.code" disabled></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="testScanLog"
        :dialogTitle="testScanTtl"
        width="800"
        :dialogHide="testScanHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="testScanHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-row>{{ productDetail.innerSn}}/{{ productDetail.name}}/{{ productDetail.specifications}}/{{ productDetail.model}}/{{ productDetail.unit}}/{{ productDetail.netWeight}}</el-row>
          <el-row>
            <el-col :span="4">产品特性<span class="icon iconfont ty-right" :class="rankSymbolCharge(testScanInfo.rank.id)">{{testScanInfo.rank === ''? '无符号':''}}</span></el-col>
            <el-col :span="4" class="ty-center">
              <span class="orderCss">{{ testScanInfo.orders }}</span>
              <span class="ty-right" v-if="Number(testScanInfo.category) === 1 ">{{ testScanInfo.baseSize }}</span>
            </el-col>
            <el-col :span="16" v-if="Number(testScanInfo.category) === 1">
              <span v-if="Number(testScanInfo.ecartSuperieur) === Number(testScanInfo.ecartInferieur)">{{'土' + testScanInfo.ecartSuperieur}}</span>
              <span v-else class="posTop">
              <div class="iconEcart">{{testScanInfo.ecartSuperieur > 0? '+' + testScanInfo.ecartSuperieur: testScanInfo.ecartSuperieur}}</div>
              <div class="iconEcart">{{testScanInfo.ecartInferieur > 0? '+' + testScanInfo.ecartInferieur: testScanInfo.ecartInferieur}}</div>
            </span>
            </el-col>
            <el-col :span="15" v-show="Number(testScanInfo.category) >= 2 && Number(testScanInfo.category) <= 7">
              {{testScanInfo.baseSize && testScanInfo.baseSize !== "" ? '基准值为'+testScanInfo.baseSize + sizeUnitCharge(featureFrom.sizeUnit): '无基准值'}}
              {{testScanInfo.ecartSuperieur && testScanInfo.ecartSuperieur !== "" ? '上限为' + testScanInfo.ecartSuperieur + sizeUnitCharge(testScanInfo.sizeUnit): '无上限'}}
              {{testScanInfo.ecartInferieur && testScanInfo.ecartInferieur !== "" ? '下限为' + testScanInfo.ecartInferieur + sizeUnitCharge(testScanInfo.sizeUnit): '无下限'}}
            </el-col>
            <el-col :span="15" v-if="Number(testScanInfo.category) > 7 && Number(testScanInfo.category) <= 14">
              {{ testScanInfo.controlContent }}
            </el-col>
          </el-row>
          <el-row v-if="examType === 1">所需要的检测设备如下：</el-row>
          <el-row v-if="examType === 2">所需要的检验工具如下：</el-row>
          <el-row v-if="examType === 3">所选的控制方法如下：</el-row>
          <el-table border="true" v-show="examType === 1" :data="testScanList1" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" header-align="center" fit style="width: 100%">
            <el-table-column align="center" prop="name" label="名称"> </el-table-column>
            <el-table-column align="center" prop="modelName" label="型号"> </el-table-column>
            <el-table-column align="center" label="此种器具的数量">
              <template #default="scope">
                {{ scope.row.length }}台/套
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
              <template #default="scope">
                <el-button type="danger" @click="testScanListDel(1, scope.$index)" plain>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table border="true" v-show="examType === 2" :data="testScanList2" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" header-align="center" fit style="width: 100%">
            <el-table-column align="center" prop="name" label="名称"> </el-table-column>
            <el-table-column align="center" prop="modelName" label="型号"> </el-table-column>
            <el-table-column align="center" label="此种器具的数量">
              <template #default="scope">
                {{ scope.row.length }}台/套
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
              <template #default="scope">
                <el-button type="danger" @click="testScanListDel(2, scope.$index)" plain><b>删除</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table v-show="examType === 3" :data="multipleSelection" border="true" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" header-align="center" fit style="width: 100%">
            <el-table-column align="center" prop="code" label="系统赋予的编码"> </el-table-column>
            <el-table-column align="center" prop="name" label="控制方法的名称"> </el-table-column>
            <el-table-column align="center" prop="briefDesc" label="简要说明"> </el-table-column>
            <el-table-column align="center" label="控制方法的创建" width="230">
              <template #default="scope">

                <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
              <template #default="scope">
                <el-button type="danger" @click="testScanListDel(3, scope.$index)" plain>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="detailTestScanLog"
        :dialogTitle="testScanTtl"
        width="750"
        :dialogHide="detailTestScanHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="detailTestScanHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-row>{{ productDetail.innerSn}}/{{ productDetail.name}}/{{ productDetail.specifications}}/{{ productDetail.model}}/{{ productDetail.unit}}/{{ productDetail.netWeight}}</el-row>
          <el-row>
            <el-col :span="3">产品特性<span class="icon iconfont ty-right" :class="detailTestScanInfo.rankSymbol">{{detailTestScanInfo.rankSymbol === ''? '无符号':''}}</span></el-col>
            <el-col :span="2" class="txtRight">
              <span class="orderCss">{{ detailTestScanInfo.orders }}</span>
              <span v-if="Number(detailTestScanInfo.category) === 1 ">{{ detailTestScanInfo.baseSize }}</span>
            </el-col>
            <el-col :span="4" v-if="Number(detailTestScanInfo.category) === 1">
              <span v-if="Number(detailTestScanInfo.ecartSuperieur) === Number(detailTestScanInfo.ecartInferieur)">{{'土' + detailTestScanInfo.ecartSuperieur}}</span>
              <span v-else class="posTop">
              <div class="iconEcart">{{detailTestScanInfo.ecartSuperieur > 0? '+' + detailTestScanInfo.ecartSuperieur: detailTestScanInfo.ecartSuperieur}}</div>
              <div class="iconEcart">{{detailTestScanInfo.ecartInferieur > 0? '+' + detailTestScanInfo.ecartInferieur: detailTestScanInfo.ecartInferieur}}</div>
            </span>
            </el-col>
            <el-col :span="19" v-show="Number(detailTestScanInfo.category) >= 2 && Number(detailTestScanInfo.category) <= 7">
              {{detailTestScanInfo.baseSize && detailTestScanInfo.baseSize !== "" ? '基准值为'+detailTestScanInfo.baseSize + sizeUnitCharge(detailTestScanInfo.sizeUnit): '无基准值'}}
              {{detailTestScanInfo.ecartSuperieur && detailTestScanInfo.ecartSuperieur !== "" ? '上限为' + detailTestScanInfo.ecartSuperieur + sizeUnitCharge(detailTestScanInfo.sizeUnit): '无上限'}}
              {{detailTestScanInfo.ecartInferieur && detailTestScanInfo.ecartInferieur !== "" ? '下限为' + detailTestScanInfo.ecartInferieur + sizeUnitCharge(detailTestScanInfo.sizeUnit): '无下限'}}
            </el-col>
            <el-col :span="19" v-if="Number(detailTestScanInfo.category) > 7 && Number(detailTestScanInfo.category) <= 14">
              {{ detailTestScanInfo.controlContent }}
            </el-col>
          </el-row>
          <el-row v-if="examType === 1">所需要的检测设备如下：</el-row>
          <el-row v-if="examType === 2">所需要的检验工具如下：</el-row>
          <el-row v-if="examType === 3">所选的控制方法如下：</el-row>
          <el-table border="true" v-show="examType === 1 || examType === 2" :data="detailTestScanList" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" header-align="center" fit style="width: 100%">
            <el-table-column align="center" prop="equEquipmentName" label="名称"> </el-table-column>
            <el-table-column align="center" prop="equModelName" label="型号"> </el-table-column>
            <el-table-column align="center" label="此种器具的数量">
              <template #default="scope">
                {{ scope.row.num }}台/套
              </template>
            </el-table-column>
          </el-table>
          <el-table v-show="examType === 3" :data="detailTestScanList" border="true" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" header-align="center" fit style="width: 100%">
            <el-table-column align="center" prop="checkControlObj.code" label="系统赋予的编码"> </el-table-column>
            <el-table-column align="center" prop="checkControlObj.name" label="控制方法的名称"> </el-table-column>
            <el-table-column align="center" prop="checkControlObj.briefDesc" label="简要说明"> </el-table-column>
            <el-table-column align="center" label="控制方法的" width="230">
              <template #default="scope">
                <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="rankScanLog"
        dialogTitle="特性等级设置"
        width="850"
        :dialogHide="rankScanHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="rankScanHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <div>
            <el-row>
              <el-col :span="24">如需要，可重新设置特性等级。</el-col>
            </el-row>
            <div class="kk-hr"></div>
            <el-row>
              <el-col :span="14">特性等级的当前值</el-col>
              <el-col :span="7" class="txtRight">
                <el-link type="primary" @click="rankReset">重新设置</el-link>
              </el-col>
              <el-col :span="3" class="txtRight">
                <el-link type="primary" @click="rankSettingsRecord">设置记录</el-link>
              </el-col>
            </el-row>
            <el-table :data="rankScanData" header-align="center" border="true" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" fit style="width: 100%">
              <el-table-column align="center" prop="name" label="特性等级名称"> </el-table-column>
              <el-table-column align="center" label="特性等级符号">
                <template #default="scope">
                  <span class="icon iconfont" :class="scope.row.symbol">{{scope.row.symbol === ''? '无符号':''}}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="rankReLog"
        dialogTitle="重新设置特性等级"
        width="850"
        :dialogHide="rankReHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="rankReHide">取消</el-button>
        <el-button class="bounce-ok" @click="addRankListSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-row>
            <el-col :span="18">重新设置并点击“确定”后，系统各处的特性等级将全部展示为设置后的数据！</el-col>
            <el-col :span="6" class="txtRight">
              <el-link type="primary" @click="addMoreRank">增加一行</el-link>
            </el-col>
          </el-row>
          <el-table :data="rankData" header-align="center" border="true" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" fit style="width: 100%">
            <el-table-column align="center" prop="name" label="特性等级名称" width="150"> </el-table-column>
            <el-table-column align="center" label="特性等级符号" width="150">
              <template #default="scope">
                <span class="icon iconfont" :class="scope.row.symbol">{{scope.row.symbol === ''? '无符号':''}}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="400">
              <template #default="scope">
                <el-button type="primary" plain @click="editName(scope.row, scope.$index)">修改名称</el-button>
                <el-button type="primary" plain @click="editSymbole(scope.row, scope.$index)">更换符号</el-button>
                <el-button type="primary" plain @click="delRankData(scope.$index)">删除本行</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="addRankLog"
        dialogTitle="增加一行"
        width="850"
        :dialogHide="addRankHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="addRankHide">取消</el-button>
        <el-button class="bounce-ok" @click="addRankSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-row>请录入要增加这行特性的名称与符号。</el-row>
          <div class="kk-hr"></div>
          <el-form ref="rankFormRef" :model="rankForm" :rules="rankFormRules" label-width="120px" label-position="left">
            <el-form-item label="特性等级名称" prop="name">
              <el-input v-model="rankForm.name" clearable></el-input>
            </el-form-item>
            <el-form-item label="特性等级符号" prop="symbolData">
              <el-select v-model="rankForm.symbolData">
                <el-option
                    v-for="(item ,i) in symbolInfo"
                    :key="i"
                    :label="item"
                    :value="item"
                >
                  <span class="icon iconfont" :class="item">{{item === ''? '无符号':''}}</span>
                </el-option>
                <template #label>
                  <span class="icon iconfont" :class="rankForm.symbolData">{{rankForm.symbolData === ''? '无符号':''}}</span>
                </template>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="editNameLog"
        dialogTitle="修改名称"
        width="850"
        :dialogHide="editNameHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="editNameHide">取消</el-button>
        <el-button class="bounce-ok" @click="rankEditNameSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-row>确定后，系统各处将均展示为新名称！</el-row>
          <div class="kk-hr"></div>
          <el-form ref="rankEditForm" :model="rankEditForm" :rules="rankEditRules" label-width="80px">
            <el-form-item label="当前名称">
              <el-input v-model="rankEditForm.old" disabled></el-input>
            </el-form-item>
            <el-form-item label="新名称" prop="name">
              <el-input v-model="rankEditForm.name" clearable></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-show="editSymbolLog"
        dialogTitle="更换符号"
        width="850"
        :dialogHide="editSymbolHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="editSymbolHide">取消</el-button>
        <el-button class="bounce-ok" @click="rankEditSymbolSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-row>确定后，系统各处将均展示为新符号！</el-row>
          <div class="kk-hr"></div>
          <el-form ref="rankSmEditForm" :model="rankSmEditForm" :rules="rankSmEditRules" label-width="80px">
            <el-form-item label="当前符号">
              <span class="icon iconfont" :class="rankSmEditForm.old">{{rankSmEditForm.old === ''? '无符号':''}}</span>
            </el-form-item>
            <el-form-item label="新符号" prop="symbol">
              <el-select v-model="rankSmEditForm.symbol">
                <el-option
                    v-for="(item ,i) in symbolInfo"
                    :key="i"
                    :label="item"
                    :value="item"
                >
                  <span class="icon iconfont" :class="item">{{item === ''? '无符号':''}}</span>
                </el-option>
                <template #label>
                  <span class="icon iconfont" :class="rankSmEditForm.symbol">{{rankSmEditForm.symbol === ''? '无符号':''}}</span>
                </template>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="rankRecordLog"
        dialogTitle="特性等级的设置记录"
        width="850"
        :dialogHide="rankRecordHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="rankRecordHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto;max-height: 400px;">
          <el-table :data="rankRecordList" header-align="center" border="true" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" fit style="width: 100%"
                    :default-sort="{ prop: 'createDate', order: 'descending' }"
          >
            <el-table-column align="center" prop="name" label="事件">
              <template #default="scope">
                <span v-if="scope.$index < rankRecordList.length -1">第{{ rankRecordList.length-scope.$index -1 }}次设置后</span>
                <span v-if="scope.$index === rankRecordList.length -1">默认值</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="设置者及时间" prop="createDate">
              <template #default="scope">
                <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="设置后的特性等级">
              <template #default="scope">
                <el-button type="primary" plain @click="rankRecordScan(scope.row.id)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="rankRecordScanLog"
        dialogTitle="特性等级查看"
        width="850"
        :dialogHide="rankRecordScanHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="rankRecordScanHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-table :data="rankRecordDetailList" border="true" header-align="center" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" fit style="width: 100%">
            <el-table-column align="center" prop="name" label="特性等级名称"> </el-table-column>
            <el-table-column align="center" label="特性等级符号">
              <template #default="scope">
                <span class="icon iconfont" :class="scope.row.symbol">{{scope.row.symbol === ''? '无符号':''}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-show="addUnitLog"
        dialogTitle="新增计量单位"
        width="600"
        :dialogHide="addUnitHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="addUnitHide">取消</el-button>
        <el-button class="bounce-ok" @click="addUnitOkBtn(3)">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form ref="unitForm" :model="unitForm" label-width="80px" label-position="top">
            <el-form-item label="计量单位">
              <el-input type="text" v-model="unitForm.name"  placeholder="请录入计量单位的名称"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-show="optionSettingsLog"
        dialogTitle="选项设置"
        width="600"
        :dialogHide="optionSettingsHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="optionSettingsHide">取消</el-button>
        <el-button class="bounce-ok" @click="addOptionSure()">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 540px; margin: 0 auto">
          <el-row>
            <el-col :span="24">请设置不少于两个选项，每个选项均需在5个字以内</el-col>
          </el-row>
          <div class="kk-hr"></div>
          <el-row>
            <el-col :span="24" class="txtRight">
              <el-link type="primary" @click="addOneOption()">增加一行</el-link>
            </el-col>
          </el-row>
          <el-form ref="optionForm" :model="optionForm" label-width="80px">
            <el-form-item
                label="选项"
                v-for="(item, index) in optionForm.optionList"
                v-bind:key="index"
                :prop="'optionList.' + index + '.name'"
                :rules="{
                required: true, message: '请录入',trigger: 'blur'
              }"
            >
              <el-input class="limitWid" type="text" v-model="item.name" maxlength="5" placeholder="—请录入—" clearable />
              <el-link v-show="optionForm.optionList.length > 2" class="gapLt" type="danger" @click="delOptionList(index)">删除</el-link>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="ptImgRecordListLog"
        dialogTitle="操作记录"
        width="770"
        :dialogHide="ptImgRecordListHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="ptImgRecordListHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div style="width: 700px; margin: 0 auto">
          <el-table :data="ptImgRecordList" header-align="center" border="true" :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" fit style="width: 100%">
            <el-table-column align="center" label="操作的名称">
              <template #default="scope">
                <span>{{ chargeOperation(scope.row.operation, recordSource) }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作者">
              <template #default="scope">
                <span>{{ scope.row.createName }} {{ new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" :label="recordSource === 3?'操作对象':'操作后的图片'">
              <template #default="scope">
                <el-link v-if="recordSource !== 3" type="primary" :underline="false" :path="scope.row.uplaodPath" :download="scope.row.uplaodPath" @click="oprationFile($event, 1)">查看</el-link>
                <el-link v-else type="primary" :underline="false" @click="recordSee(scope.row.resource)">查看</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <!-- 可供选择的文件列表 List of files for selection-->
    <TyDialog
        v-if="chooseSaveFolderLog"
        dialogTitle="请选择内部文件"
        width="1350"
        :dialogHide="chooseSaveFolderHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="chooseSaveFolderHide">关  闭</el-button>
        <el-button class="bounce-ok" @click="sureChooseFolder">确  定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <div class="ty-fileContent">
            <!--文件夹列表 -->
            <div class="ty-colFileTree folder_list" data-name="main">
              <div class="null" v-show="listFirstFolder.length <= 0"><img :src="nodata" alt=""><p>暂无数据</p></div>
              <folderList :folderData="listFirstFolder" @fileEvent="fileListStr"></folderList>
            </div>
            <!-- 文件列表 -->
            <div class="mar">
              <ul class="ty-secondTab" id="fileSort" v-show="listFirstFolder.length > 0 && fileInfo.fileList.length > 0">
                <li :class="[fileSortType === 1 ? 'ty-active' : '']" @click="fileSort(1)">发布时间<span class="sort sortName" v-if="fileSortType === 1"><i class="fa" :class="[fileSortDown ? 'fa-long-arrow-down' : 'fa-long-arrow-up']"></i></span></li>
                <li :class="[fileSortType === 2 ? 'ty-active' : '']" @click="fileSort(2)">文件编号<span class="sort sortName" v-if="fileSortType === 2"><i class="fa" :class="[fileSortDown ? 'fa-long-arrow-down' : 'fa-long-arrow-up']"></i></span></li>
              </ul>
              <div class="ty-fileList mainFileList" >
                <!--文件列表-->
                <div class="ty-fileNull" v-show="treeItemActiveId !=='' && fileInfo.fileList.length === 0"><img :src="nodata" alt=""><p>暂无文件</p></div>
                <div class="ty-fileItem fileItemRadioChoose" v-for="(item, index) in fileInfo.fileList" v-bind:key="index">
                  <div class="ty-radio">
                    <el-radio v-model="fileSelectId" :value="item.id"></el-radio>
                  </div>
                  <div class="ty-fileType sized" :class="'ty-file_'+ item.version"></div>
                  <div class="ty-fileInfo">
                    <div class="ty-fileRow">
                      <div class="ty-fileName" :title="item.name+ '-G' + item.changeNum">{{ item.name }}</div>
                      <div class="ty-fileNo"  :title="item.fileSn">编号 ：{{ item.fileSn }}</div>
                      <div class="ty-fileVersion">G{{ item.changeNum }}</div>
                    </div>
                    <div class="ty-fileRow">
                      <div class="ty-fileDetail">{{ item.updateName  === null || item.updateName  == undefined?item.createName:item.updateName }}
                        &nbsp;&nbsp;{{ item.size < 102400 ? parseFloat(item.size/1024).toFixed(2) + 'KB': parseFloat(item.size/1048576).toFixed(2) + 'MB'}} &nbsp;&nbsp;
                        {{ item.updateDate  === null || item.updateDate  == undefined? item.createDate:item.updateDate}}</div>
                      <div class="ty-fileHandle">
                        <a class="aBtn" type="btn" name="seeOnline" :path="item.path" @click="oprationFile($event, 1)">在线预览</a>
                        <a class="aBtn" type="btn" name="download" :path="item.path" :download="item.name+ '.' + item.version" @click="oprationFile($event, 2)">下载</a>
                        <a class="aBtn" type="btn" name="basicMsg" @click="basicMessageBtn(item.id)">基本信息</a>
                        <a class="aBtn" type="btn" name="changeVersionRecord" @click="chargeRecord(item.id)">换版记录</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div id="ye_conOk" v-show="fileInfo.fileList.length > 0">
                <TyPage
                    :curPage="fileInfo.pageInfo.currentPageNo" :pageSize="fileInfo.pageInfo.pageSize"
                    :allPage="fileInfo.pageInfo.totalPage" :pageClickFun="getFileListPage"></TyPage>
              </div>
            </div>
            <div class="clr"></div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="resHistorylog"
        dialogTitle="查看"
        width="1200"
        :dialogHide="resHistoryHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="resHistoryHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-searchContent">
          <div class="mar">
            <div class="fileList">
              <!--文件列表-->
              <div class="ty-fileItem fileItemRadioChoose" v-for="(item, index) in versionHistory" v-bind:key="index">
                <div class="ty-fileType sized" :class="'ty-file_'+ item.version"></div>
                <div class="ty-fileInfo sizeo oterme">
                  <div class="ty-fileRow">
                    <div class="ty-fileName sizenm" :title="item.name+ '-G' + item.changeNum">{{ item.name }}</div>
                    <div class="ty-fileNo sizeno"  :title="item.fileSn">编号 ：{{ item.fileSn }}</div>
                    <div class="ty-fileVersion sizesi">G{{ item.changeNum }}</div>
                  </div>
                  <div class="ty-fileRow">
                    <div class="ty-fileDetail">{{ item.updateName  === null || item.updateName  == undefined?item.createName:item.updateName }}
                      &nbsp;&nbsp;{{ item.size < 102400 ? parseFloat(item.size/1024).toFixed(2) + 'KB': parseFloat(item.size/1048576).toFixed(2) + 'MB'}} &nbsp;&nbsp;
                      {{ item.updateDate  === null || item.updateDate  == undefined? new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'):new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss')}}</div>
                    <div class="ty-fileHandle sizehd">
                      <a class="aBtn" type="btn" name="seeOnline" :path="item.path" @click="oprationFile($event, 1)">在线预览</a>
                      <a class="aBtn" type="btn" name="download" :path="item.path" :download="item.name+ '.' + item.version	" @click="oprationFile($event, 2)">下载</a>
                      <a class="aBtn" type="btn" name="basicMsg" @click="basicMessageBtn(item.file)">基本信息</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div id="ye_record"></div>
          </div>
        </div>
        <div></div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="relateFileLog"
        dialogTitle="查看"
        width="1200"
        :dialogHide="relateFileHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="relateFileHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="pageBg">
          <el-row>{{ productDetail.innerSn}}/{{ productDetail.name}}/{{ productDetail.specifications}}/{{ productDetail.model}}/{{ productDetail.unit}}/{{ productDetail.netWeight}}</el-row>
          <!-- 文件列表 -->
          <div>
            <el-row>本产品已与如下{{ pdImageInfo.resourceList.length }}个文件关联：</el-row>
            <div class="ty-fileList mainFileList" >
              <!--文件列表-->
              <div class="ty-fileItem fileItemRadioChoose" v-for="(item, index) in pdImageInfo.resourceList" v-bind:key="index">
                <div class="ty-fileType sized" :class="'ty-file_'+ item.resEntity.version"></div>
                <div class="ty-fileInfo sizeo oterme">
                  <div class="ty-fileRow">
                    <div class="ty-fileName sizenm" :title="item.resEntity.name+ '-G' + item.resEntity.changeNum">{{ item.resEntity.name }}</div>
                    <div class="ty-fileNo sizeno"  :title="item.resEntity.fileSn">编号 ：{{ item.resEntity.fileSn }}</div>
                    <div class="ty-fileVersion sizesi">G{{ item.resEntity.changeNum }}</div>
                  </div>
                  <div class="ty-fileRow">
                    <div class="ty-fileDetail">{{ item.updateName  === null || item.updateName  == undefined?item.createName:item.updateName }}
                      &nbsp;&nbsp;{{ item.resEntity.size < 102400 ? parseFloat(item.resEntity.size/1024).toFixed(2) + 'KB': parseFloat(item.resEntity.size/1048576).toFixed(2) + 'MB'}} &nbsp;&nbsp;
                      {{ item.updateDate  === null || item.updateDate  == undefined? new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'):new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss')}}</div>
                    <div class="ty-fileHandle sizehd">
                      <a class="aBtn" type="btn" name="seeOnline" :path="item.resEntity.path" @click="oprationFile($event, 1)">在线预览</a>
                      <a class="aBtn" type="btn" name="download" :path="item.resEntity.path" :download="item.resEntity.name+ '.' + item.resEntity.version" @click="oprationFile($event, 2)">下载</a>
                      <a class="aBtn" type="btn" name="basicMsg" @click="basicMessageBtn(item.resource)">基本信息</a>
                      <a class="aBtn" type="btn" name="changeVersionRecord" @click="chargeRecord(item.resource)">换版记录</a>
                    </div>
                  </div>
                </div>
                <div class="sizeote">
                  <div>
                    <div>选择：{{ item.createName }}{{ new Date(item.resEntity.auditDate).format('yyyy-MM-dd hh:mm:ss') }}</div>
                  </div>
                  <div>
                    <a @click="overconact(item.resource)" id="dettenadd" class="aBtn">解除关联</a>
                  </div>
                </div>
              </div>
            </div>
            <div id="ye_con" v-show="fileInfo.fileList.length > 0"></div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="docInfoScanLog"
        dialogTitle="基本信息"
        width="804"
        :dialogHide="docInfoScanHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="docInfoScanHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="bonceCon ">
          <div class="infoCon clearfix">
            <h4 class="info_title" id="info_title">{{basicMessage.resource.name}}</h4>
            <div class="ty-left">
              <div class="trItem"><span class="ttl">文件编号：</span><span class="con" id="info_sn">{{basicMessage.resource.fileSn}}</span></div>
              <div class="trItem"><span class="ttl">保存位置：</span><span class="con" id="info_category">{{basicMessage.categoryName}}</span></div>
              <div class="trItem"><span class="ttl">文件大小：</span><span class="con" id="info_size">
              {{basicMessage.resource.size < 102400 ? parseFloat(basicMessage.resource.size/1024).toFixed(2) + 'KB': parseFloat(basicMessage.resource.size/1048576).toFixed(2) + 'MB'}}
            </span></div>
              <div class="trItem"><span class="ttl">文件类型：</span><span class="con" id="info_version">{{basicMessage.resource.version || "未知"}}</span></div>
              <div class="trItem"><span class="ttl">{{basicMessage.resource.changeNum > 0?'换版原因':'说明'}}：</span><span class="con" id="info_content">{{basicMessage.resource.content}}</span></div>
            </div>
            <div class="ty-left">
              <div class="trItem"><span class="ttl2">版本号：</span><span class="con" id="info_gn">G{{basicMessage.resource.changeNum}}</span></div>
              <div class="trItem"><span class="ttl2">创建人：</span><span class="con" id="info_creator">
              <span class="info_createName info_name">{{basicMessage.resource.createName}}</span> <span class="info_createDate">{{basicMessage.resource.createDate}}</span>
            </span></div>
              <div class="trItem"><span class="ttl2">换版人：</span><span class="con" id="info_updater">
              <span v-show="basicMessage.resource.changeNum === 0">--</span>
              <span class="info_updateName info_name" v-show="basicMessage.resource.changeNum !== 0">{{basicMessage.resource.updateName}}</span>
              <span class="info_updateDate" v-show="basicMessage.resource.changeNum !== 0">{{basicMessage.resource.updateDate}}</span>
            </span></div>
            </div>
          </div>
          <div class="infoCon clearfix">
            <div class="ty-left processHistory">
              <div class="item-header">
                审批记录
              </div>
              <div class="processList">
                <div class="trItem">
                  <span class="ttl">申请人：</span>
                  <span class="con" style="max-width: 280px">
                  <span class="info_name">{{basicMessage.listAp[0].userName}}</span>
                  <span>{{new Date(basicMessage.listAp[0].createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
                </span>
                </div>
                <div class="trItem" v-for="(item, index) in basicMessage.listAp" :key="index">
                  <span class="ttl">{{index === basicMessage.listAp.length - 1 ? '文 管': '审批人'}}：</span>
                  <span class="con" style="max-width: 280px">
                  <span class="info_name">{{item.toUserName}}</span>
                  <span>{{new Date(item.handleTime).format('yyyy-MM-dd hh:mm:ss')}}</span>
                </span>
                </div>
              </div>
            </div>
            <div class="ty-left censusInfo">
              <div class="item-header">
                统计信息
              </div>
              <div class="trItem">
                <span class="ttl3">浏览次数</span>
                <span class="con times view_num">{{basicMessage.resource.viewNum}}</span>
                <span class="link-blue" id="viewNumBtn" @click="seeHandelRecordBtn(1)">浏览记录</span>
              </div>
              <div class="trItem">
                <span class="ttl3">下载次数</span>
                <span class="con times download_num">{{basicMessage.resource.downloadNum}}</span>
                <span class="link-blue" id="downloadNumBtn" @click="seeHandelRecordBtn(2)">下载记录</span>
              </div>
              <div class="trItem">
                <span class="ttl3">移动次数</span>
                <span class="con times move_num">{{basicMessage.resource.moveNum}}</span>
                <span class="link-blue" id="moveNumBtn" @click="seeHandelRecordBtn(5)">移动记录</span>
              </div>
              <div class="trItem">
                <span class="ttl3">文件名称修改次数</span>
                <span class="con times name_num">{{basicMessage.upName}}</span>
                <span class="link-blue" id="changeNameNumBtn" @click="seeHandelRecordBtn(6)">修改记录</span>
              </div>
              <div class="trItem">
                <span class="ttl3">文件编号修改次数</span>
                <span class="con times no_num">{{basicMessage.upFileSn || 0}}</span>
                <span class="link-blue" id="changeNoNumBtn" @click="seeHandelRecordBtn(7)">修改记录</span>
              </div>
            </div>
          </div>

        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="fileHandleRecordLog"
        :dialogTitle="recordTitleName"
        width="800"
        :dialogHide="fileHandleRecordHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="fileHandleRecordHide">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="bonceCon" style="max-height:300px; overflow-y: auto; ">
          <table class="ty-table recordTable">
            <thead v-if="fileHandleRecordSource === 1">
            <tr>
              <td>序号</td>
              <td>浏览人</td>
              <td>浏览时间</td>
            </tr>
            </thead>
            <thead v-else-if="fileHandleRecordSource === 2">
            <tr>
              <td>序号</td>
              <td>下载人</td>
              <td>下载时间</td>
            </tr>
            </thead>
            <thead v-else-if="fileHandleRecordSource === 5">
            <tr>
              <td>序号</td>
              <td>移动前</td>
              <td>移动后</td>
              <td>移动人</td>
              <td>移动时间</td>
            </tr>
            </thead>
            <thead v-else-if="fileHandleRecordSource === 6 || fileHandleRecordSource === 7">
            <tr>
              <td>序号</td>
              <td>修改前</td>
              <td>修改后</td>
              <td>修改人</td>
              <td>修改时间</td>
            </tr>
            </thead>
            <tbody v-if="fileHandleRecordSource === 1 || fileHandleRecordSource === 2">
            <tr v-for="(item, index) in fileRecordList" v-bind:key="index">
              <td>{{index+1}}</td>
              <td>{{item.createName}}</td>
              <td>{{item.createDate.substring(0,19)}}</td>
            </tr>
            </tbody>
            <tbody v-else>
            <tr v-for="(item, index) in fileRecordList" v-bind:key="index">
              <td>{{index+1}}</td>
              <td>{{item.nameBefore}}</td>
              <td>{{item.nameAfter}}</td>
              <td>{{item.createName}}</td>
              <td>{{item.createDate}}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="ableControlLog"
        dialogTitle="！提示"
        width="400"
        :dialogHide="ableControlHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="ableControlHide">取消</el-button>
        <el-button class="bounce-ok" @click="controlMethodStopSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          {{controlTip}}
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="delControlLog"
        dialogTitle="！提示"
        width="400"
        :dialogHide="delControlHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="delControlHide">取消</el-button>
        <el-button class="bounce-ok" @click="delControlSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定删除此种控制方法吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="delFeatureLog"
        dialogTitle="！提示"
        width="400"
        :dialogHide="delFeatureHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="delFeatureHide">取消</el-button>
        <el-button class="bounce-ok" @click="delFeatureSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定删除该条产品特性吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="delRankLog"
        dialogTitle="！提示"
        width="400"
        :dialogHide="delRankHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="delRankHide">关  闭</el-button>
        <el-button class="bounce-ok" @click="delRankSure">完 成</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定删除这个特性等级吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog
        v-if="relasecntactLog"
        dialogTitle="！提示"
        width="400"
        :dialogHide="relasecntactHide"
    >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="relasecntactHide">关  闭</el-button>
        <el-button class="bounce-ok" @click="madesure">完 成</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <p>确定后，本产品与本文件将解除关联！ </p>
        </div>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import nodata from "@/assets/commonImg/nodata.svg"
import {initNav} from "@/utils/routeChange"
import auth from '@/sys/auth'
import * as api from "@/api/processInit"
import folderList from "@/components/folderTree/folderChilren.vue"
import { previewOrDownloadFun } from "@/utils/downloadFile"

export default {
  data() {
    return {
      pageName: 'productFeatures',
      mainNum: 1,
      yszNum: 0,
      nodata: nodata,
      sflag: false,
      uploadLoading: false,
      examType: 0,
      initState: 0,
      fileId: '',
      fileSelectId: '',
      editType: '',
      logType: '',
      controlTip: '',
      controlType: '',
      controlIndex: '',
      controlAbleId: '',
      unitList: [],
      productDetail: {},
      ptFeatureDetail: {},
      featureIndex: '',
      pdImageInfo: {
        xhImage: null,
        cptzImage: null,
        resourceList:[]
      },
      editImageInfo: {
        xhImage: null,
        cptzImage: null,
        resourceList:[]
      },
      rankForm: {},
      testEqForm: {},
      featureFrom: {
        itemCheckJson1:[],
        itemCheckJson2:[],
        itemCheckJson3:[]
      },
      controlMethod: {},
      indexPage: {},
      unInitPt: [],
      examArr: [],
      initPt: [],
      rankData: [],
      rankScanData: [],
      rankIndex: ``,
      rankRecordList: [],
      rankRecordDetailList: [],
      testEquipment: [],
      testEquipmentModel: [],
      controlMethodsList: [],
      controlMethodsSusList: [],
      testScanList1: [],
      testScanList2: [],
      detailTestScanList: [],
      multipleSelection: [],//查看已选的控制方法
      controlMethodRecord: [],
      productFeatureList: [],
      ptImgRecordList: [],
      testScanTtl: '',
      controlMethodRecordDetail: {},
      unitForm: {},
      optionForm: {
        optionList:[{name:''},{name:''}]
      },
      rankEditForm: {},
      rankSmEditForm: {},
      listFirstFolder: [],
      basicMessage: {
        listAp: [],
        resource: {},
        listUser: []
      },
      fileInfo:{
        pageInfo: {},
        fileList: []
      },
      testScanInfo: {},
      detailTestScanInfo: {},
      fileSortType: 1,
      sort: 1,
      recordSource: 0,
      treeItemActiveId: '',
      fileRecordList: [],
      xhImageList: [],
      xhImageList1: [],
      featureTtl: '增加产品特性',
      recordTitleName: '浏览记录',
      versionHistory: [],
      delRankList: [],
      resHistorylog: false,
      fileSortDown: true,
      delFeatureLog: false,
      addUnitLog: false,
      delRankLog: false,
      delControlLog: false,
      ableControlLog: false,
      docInfoScanLog: false,
      fileHandleRecordLog: false,
      fileHandleRecordSource: 1,
      addPtFeaturesLog: false,
      rankScanLog: false,
      rankReLog: false,
      addRankLog: false,
      editNameLog: false,
      rankRecordLog: false,
      rankRecordScanLog: false,
      testScanLog: false,
      detailTestScanLog: false,
      testLog: false,
      editSymbolLog: false,
      controlMethodsLog: false,
      addcontrolMethodLog: false,
      controlMethodsSusLog: false,
      controlMethodRecordLog: false,
      controlMethodRecordScanLog: false,
      imgExplainLog: false,
      pdImageLog: false,
      relateFileLog: false,
      optionSettingsLog: false,
      ptFeaturesScanLog: false,
      ptImgRecordListLog: false,
      chooseSaveFolderLog: false,
      relasecntactLog: false,
      imgUpload: {
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.png,.jpeg,.jpg,.gif',
        postData: {
          module: '工序初始化', userId: auth.getUserID()
        },
      },
      sizeUnitArr: [
        {value: 1, label: 'mm'},
        {value: 2, label: 'cm'},
        {value: 3, label: 'dm'},
        {value: 5, label: 'm'}
      ],
      symbolInfo: ['icon-tx-fill-lingxing','icon-tx-fill-babianxing','icon-tx-fill-zhengfangxing','icon-tx-fill-tixing','icon-tx-fill-pinghangsibianxing',
        'icon-tx-fill-shizixing','icon-tx-fill-liujiaoxingxing','icon-tx-fill-sanjiaoxing','icon-tx-fill-liubianxing','icon-tx-fill-tuoyuanxing',
        'icon-tx-fill-changfangxing','icon-tx-fill-wujiaoxingxing','icon-tx-fill-yuanxing','icon-tx-fill-wubianxing','icon-tx-fill-xieshizixing',
        'icon-tx-babianxing','icon-tx-sanjiaoxing','icon-tx-pinghangsibianxing','icon-tx-lingxing','icon-tx-liubianxing','icon-tx-xieshizixing',
        'icon-tx-wubianxing','icon-tx-tuoyuanxing','icon-tx-tixing','icon-tx-wujiaoxingxing','icon-tx-liujiaoxingxing','icon-tx-shizixing',
        'icon-tx-changfangxing','icon-tx-zhengfangxing','icon-tx-yuanxing'],
      ruleEmpty: {required: false},
      addFeaturerules: {
        orders: [
          { required: true, message: '', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '', trigger: 'change' }
        ]
      },
      rankFormRules: {
        name: [
          { required: true, message: '', trigger: 'blur' }
        ],
        symbolData: [
          { required: true, message: '', trigger: 'blur' }
        ]
      },
      rankEditRules: {
        name: [
          { required: true, message: '', trigger: 'blur' }
        ]
      },
      rankSmEditRules: {
        symbol: [
          { required: true, message: '', trigger: 'blur' }
        ]
      },
      controlMethodRule: {
        name: [
          { required: true, message: '请录入', trigger: 'blur' }
        ]
      },
      testEqFormRules: {
        id: [
          { required: true, message: '', trigger: 'change' }
        ],
        model: [
          { required: true, message: '', trigger: 'change' }
        ],
      },
      isBlueTip: false,
      rootPath:{}
    }
  },
  components: {
    folderList
  },
  computed: {
    categoryCharge() {
      let categoryList = [
        '','尺寸',
        '类似尺寸，有基准值，并有上限的计量型特性',
        '有基准值与上限，但无下限的计量型特性',
        '有基准值与下限，但无上限的计量型特性',
        '没有基准值，仅有上限与下限的计量型特性',
        '没有基准值与上限，仅有下限的计量型特性',
        '没有基准值与下限，仅有上限的计量型特性',
        '需在“合格”与“不合格”中选择的计数型特性',
        '需在自定义的两个或多个选项中选择的计数型特性',
        '需录入为纯文本的数据的计数型特性',
        '需录入百分比（百分号系统自带）的计数型特性',
        '需在日历中选择某日期的计数型特性',
        '需在日历中选择某月份的计数型特性',
        '需在日历中选择某年份的计数型特性'
      ]
      return categoryList[Number(this.ptFeatureDetail.category)]
    },
    featureOptionStr(){
      let str = ``
      this.ptFeatureDetail.featureOptionList.forEach(function (item){
        str = `选项“${item.name}”，`
      })
      str = str.slice(0, -1)
      return str
    },
    rankSymbolScan(){
      let info = this.rankScanData.find(item => item.id === this.featureFrom.rank )
      return info.symbol
    }
  },
  mounted() {
    initNav({mid: 'pp', name: '产品特性', pageName: this.pageName}, this.createdInit, this)
  },
  methods: {
    createdInit() {
      this.initState = 0
      this.setRankData()
      this.getProduct(0, 1)
      this.unitListResult()
      api.getYszNum({})
          .then(res => {
            this.yszNum = res.data.data || 0
          })
      let rootPath = localStorage.getItem('rootPath')
      this.rootPath = JSON.parse(rootPath)
    },
    getProductPage(curr){
      this.getProduct(0, curr)
    },
    getProduct(type, curr) {//1=已初始化 0=未初始化
      let json = {
        currentPageNo: curr,
        pageSize: 20,
        type: type
      }
      api.getProductList(json)
          .then(res => {
            let list = res.data.data || []
            if (type === 1) {
              this.initPt = list
            } else {
              this.unInitPt = list
              this.indexPage = res.data.page
            }
          })
    },
    ptFeaturesInit(info){
      this.sflag = false
      this.productDetail = info
      this.pdImageInfo = {}
      this.productFeatureList = []
      this.getMidFeatureList()
      this.getControlList(1)
      api.getPdBaseOne(this.productDetail.id)
          .then(res => {
            const detail = JSON.stringify(res.data.data || {})
            this.pdImageInfo = JSON.parse(detail)
            this.mainNum = 2
          })
    },
    initedPt(){
      this.mainNum = 3
      this.initState = 1
      this.getProduct(1, 1)
    },
    unitListResult(){
      api.getUnitList()
          .then(res => {
            this.unitList = res.data.list || []
          })
    },
    getMidFeatureList(){
      this.productFeatureList = []
      api.getFeatureItemList(this.productDetail.id)
          .then(res => {
            const list = JSON.stringify(res.data.data || [])
            this.productFeatureList = JSON.parse(list)
          })
    },
    addPtFeaturesBtn() {
      this.logType = 'add'
      this.featureTtl = '增加产品特性'
      this.addPtFeaturesLog = true
      this.examArr = []
      this.testScanList1 = []
      this.testScanList2 = []
      this.multipleSelection = []
      this.featureFrom = {
        itemCheckJson1:[],
        itemCheckJson2:[],
        itemCheckJson3:[]
      }
      this.isBlueTip = false
      this.setRankData()
    },
    addPtFeaturesSure() {
      this.$refs['featureFrom'].validate((valid) => {
        if (valid) {
          if (Number(this.featureFrom.category) === 1){
            this.compareOffset()
          } else if (Number(this.featureFrom.category) > 1 && Number(this.featureFrom.category) <= 7){
            this.limitTopSize()
          }
          if (this.examArr.length <= 0 || this.examArr === "") {
            this.$message.error('本产品特性可采用哪些方法检查？请至少设置一项！')
            return false
          }
          let empty = 0
          for (let i=0;i<this.examArr.length;i++) {
            if (this.featureFrom['itemCheckJson' + this.examArr[i]].length === 0) {
              empty++
            }
          }
          if (empty === this.examArr.length) {
            return false
          }
          let url = `/feature/addFeatureItem.do`
          let json = {
            'orders': Number(this.featureFrom.orders),
            'category': Number(this.featureFrom.category),
            'name': this.featureFrom.name,
            'rank': this.featureFrom.rank,
            'product': this.productDetail.id,
            'itemCheckJson1': JSON.stringify(this.featureFrom.itemCheckJson1),
            'itemCheckJson2': JSON.stringify(this.featureFrom.itemCheckJson2),
            'itemCheckJson3': []
          }
          this.addPtFeaturesLog = false
          if (Number(this.featureFrom.category) <= 7) {
            if (Number(this.featureFrom.category) === 1) {
              json.name = '尺寸'
            }
            //this.featureFrom.sizeUnit != ''? json.sizeUnit = this.featureFrom.sizeUnit:''
            json.unitId = this.featureFrom.sizeUnit
            json.baseSize = this.featureFrom.baseSize
            json.sizeUnit = this.featureFrom.sizeUnit
            json.ecartSuperieur = this.featureFrom.ecartSuperieur
            json.ecartInferieur = this.featureFrom.ecartInferieur
            json.upperQualified = this.featureFrom.upperQualified
            json.lowerQualified = this.featureFrom.lowerQualified
          } else if (Number(this.featureFrom.category) === 9) {
            json.featureOptionJson = JSON.stringify(this.featureFrom.featureOptionJson)
          } else if (Number(this.featureFrom.category) === 10) {
            json.textLimit = this.featureFrom.textLimit
          } else if (Number(this.featureFrom.category) === 11) {
            json.decimalsLimit = this.featureFrom.decimalsLimit
          }
          if (Number(this.featureFrom.category) >= 8) {
            json.controlContent = this.featureFrom.controlContent
          }
          let arr3 = []
          this.featureFrom['itemCheckJson3'].forEach(function (item) {
            arr3.push({
              checkControl: item.id
            })
          })
          json['itemCheckJson3'] = JSON.stringify(arr3)
          if (this.logType === 'edit') {
            json.id = this.featureFrom.id
            url = `/feature/updateFeatureItem.do`
          }
          api.addPtFeatures(json, url)
              .then(() => {
                this.getMidFeatureList()
              })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    compareOffset(){
      if (this.featureFrom.ecartSuperieur !== "" && this.featureFrom.ecartInferieur !== "" && this.featureFrom.ecartSuperieur && this.featureFrom.ecartInferieur )
        if (this.featureFrom.ecartSuperieur * 1 <= this.featureFrom.ecartInferieur * 1) {
          this.$message.error("您录入的上下偏差不合逻辑。请检查并修正！")
          this.isBlueTip = true
          return false
        }
    },
    limitTopSize(){
      if (this.featureFrom.ecartSuperieur && this.featureFrom.ecartSuperieur !== "" && this.featureFrom.ecartInferieur !== "" && this.featureFrom.ecartInferieur )
        if (this.featureFrom.ecartSuperieur * 1 <= this.featureFrom.ecartInferieur * 1) {
          this.$message.error("您录入的上下限不合逻辑。请检查并修正！")
          this.isBlueTip = true
          return false
        }
    },
    initFinishSure() {
      if (this.sflag) {
        this.initState = 0
        this.mainNum = 1
        api.productFeatureSetting(this.productDetail.id)
            .then(() => {
              this.getProduct(0, 1)
              api.getYszNum({})
                  .then(res => {
                    this.yszNum = res.data.data || 0
                  })
            })
      }
    },
    getRankListBtn() {
      this.rankScanLog = true
    },
    setRankData (){
      api.getRankList()
          .then(res => {
            let list = res.data.data || []
            this.rankScanData = [...list]
          })
    },
    rankReset() {
      this.rankReLog = true
      const list = JSON.stringify(this.rankScanData)
      this.rankData = JSON.parse(list)
    },
    addRankSure() {
      this.$refs['rankFormRef'].validate((valid) => {
        if (valid) {
          let json = {
            "name": this.rankForm.name,
            "symbol": this.rankForm.symbolData
          }
          this.rankData.push(json)
          this.addRankLog = false
        } else {
          console.log("还有必填项未填！")
          return false;
        }
      });
    },
    addRankListSure() {
      let list = []
      for (let i=0;i<this.rankData.length;i++) {
        let json = {
          "name": this.rankData[i].name,
          "symbol": this.rankData[i].symbol
        }
        if (this.rankData[i].id) {
          json.id = this.rankData[i].id
        }
        list.push(json)
      }
      let data = {
        data: JSON.stringify(list)
      }
      api.addRank(data)
          .then(() => {
            this.rankReLog = false
            this.setRankData()
          })
    },
    editName(info, rankIndex){
      if (info.id) {
        info = this.rankScanData.find(item => Number(item.id) === Number(info.id))
      }
      this.rankEditForm.old = info.name
      this.rankEditForm.name = ''
      this.rankIndex = rankIndex
      this.editNameLog = true
    },
    editSymbole(info, rankIndex){
      if (info.id) {
        info = this.rankScanData.find(item => Number(item.id) === Number(info.id))
      }
      this.rankSmEditForm.old = info.symbol
      this.rankSmEditForm.symbol = ''
      this.rankIndex = rankIndex
      this.editSymbolLog = true
    },
    delRankData(rankIndex){
      let id = this.rankData[rankIndex].id
      if (id) {
        let param = {rankId:id}
        api.delRankUse(param).then(res => {
          let state = res.data.data
          if (state == 1) { //已使用
            this.$message({
              'type':'error',
              'offset':'26',
              'message': "操作失败，因为这个特性等级已被使用！"
            })
          } else {
            this.rankIndex = rankIndex
            this.delRankLog = true
          }
        })
      } else {
        this.rankIndex = rankIndex
        this.delRankLog = true
      }
    },
    delRankSure (){
      this.rankData.splice(this.rankIndex, 1)
      this.delRankLog = false
    },
    rankEditNameSure( ){
      this.$refs['rankEditForm'].validate((valid) => {
        if (valid) {
          this.rankData[this.rankIndex].name = this.rankEditForm.name
          this.editNameLog = false
        } else {
          console.log("链接超时，请重试！")
        }
      })
    },
    rankEditSymbolSure( ){
      this.$refs['rankSmEditForm'].validate((valid) => {
        if (valid) {
          this.rankData[this.rankIndex].symbol = this.rankSmEditForm.symbol
          this.editSymbolLog = false
        } else {
          console.log("链接超时，请重试！")
        }
      })
    },
    rankSettingsRecord( ){
      this.rankRecordLog = true
      api.getRankRecord()
          .then(res => {
            this.rankRecordList = res.data.data
          })
    },
    rankRecordScan( id){
      this.rankRecordScanLog = true
      api.getRankRecordDetail(id)
          .then(res => {
            this.rankRecordDetailList = res.data.data
          })
    },
    addMoreRank(){
      this.addRankLog = true
      this.$refs['rankFormRef'].resetFields()
    },
    getEqList(type) {
      let param = {category: type}
      api.getEquEquipmentList(param)
          .then(res => {
            this.testEquipment = res.data.data
          })
    },
    getEquModelList(id) {
      let json = {
        category: this.examType,
        equipment: id,
      }
      this.testEqForm.model = ''
      api.getEquModel(json)
          .then(res => {
            this.testEquipmentModel = res.data.data || []
          })
    },
    getControlList(abled, editType, code) {
      api.getCheckControlList(abled)
          .then(res => {
            if (abled === 1) {
              let list = res.data.data || []
              if (editType === 'add') {
                list.forEach(item => {
                  if(item.code == code) {
                    this.controlMethodsList.push(item)
                  }
                })
              } else if (editType === 'edit') {
                let icon = this.controlMethodsList.findIndex(item => item.code == code)
                let newItem = list.find(item => item.code == code)
                console.log('icocn',icon)
                console.log('ceferfre',newItem)
                if (newItem) this.controlMethodsList[icon] = newItem
              } else {
                this.controlMethodsList = res.data.data
              }
            } else if (abled === 0) {
              this.controlMethodsSusList = res.data.data
              this.controlMethodsSusLog = true
            }
          })
    },
    changeExam(val){
      if (val.indexOf('1') < 0) {
        this.featureFrom.itemCheckJson1 = []
        this.testScanList1 = []
      }
      if (val.indexOf('2') < 0) {
        this.testScanList2 = []
        this.featureFrom.itemCheckJson2 = []
      }
      if (val.indexOf('3') < 0) this.featureFrom.itemCheckJson3 = []
    },
    testEquipmentSure() {
      if (this.examType === 1 || this.examType === 2) {
        this.$refs['testEqForm'].validate((valid) => {
          if (valid) {
            let arr = {
              equEquipment: this.testEqForm.id,
              equModel: this.testEqForm.model
            }
            let result = this.testEquipment.find(item => item.id == this.testEqForm.id)
            let modelResult = this.testEquipmentModel.find(item => item.mid == this.testEqForm.model)
            let temp = {...result,...modelResult}
            if (this.examType === 1) {
              this.featureFrom.itemCheckJson1.push(arr)
              this.testScanList1.push(temp)
              this.testLog = false
            } else if (this.examType === 2) {
              this.featureFrom.itemCheckJson2.push(arr)
              this.testLog = false
              this.testScanList2.push(temp)
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        })

      } else if (this.examType === 3) {
        let arr3 = []
        let rows = this.$refs.multipleTable.getSelectionRows()
        this.featureFrom.itemCheckJson3 = []
        rows.forEach((row) => {
          arr3.push(row)
        })
        this.featureFrom.itemCheckJson3 = arr3
        this.multipleSelection = arr3
        this.controlMethodsLog = false
      }
    },
    testEquipmentScan(type){
      if (type === 1) {
        this.testScanTtl = '查看所选的检测设备'
      } else if (type === 2) {
        this.testScanTtl = '查看所选的检验工具'
      } else if (type === 3) {
        this.testScanTtl = '查看已选的控制方法'
      }
      this.examType = type
      this.testScanInfo = this.featureFrom
      this.testScanLog = true
    },
    itemCheckListScan(type){
      let that = this
      if (type === 1) {
        this.testScanTtl = '查看所选的检测设备'
        this.detailTestScanList = this.ptFeatureDetail.itemCheckList1
      } else if (type === 2) {
        this.testScanTtl = '查看所选的检验工具'
        this.detailTestScanList = this.ptFeatureDetail.itemCheckList2
      } else if (type === 3) {
        this.testScanTtl = '查看已选的控制方法'
        this.detailTestScanList = this.ptFeatureDetail.itemCheckList3
      }
      this.detailTestScanInfo = that.ptFeatureDetail
      this.examType = type
      this.detailTestScanLog = true
    },
    selectTestMethod(type){
      this.examType = type
      if (type === 1 || type === 2) {
        this.testLog = true
        this.testEquipmentModel = []
        this.getEqList(type)
        setTimeout(() => {
          this.$refs.testEqForm.resetFields()
        }, 500);
      } else if (type === 3) {
        this.controlMethodsLog = true
        this.$nextTick(this.controlMethodInit)
      }
    },
    controlMethodInit(){
      let that = this
      this.$refs.multipleTable.clearSelection();
      this.featureFrom.itemCheckJson3.forEach((method) => {
        that.$refs.multipleTable.toggleRowSelection(method, true)
      })
    },
    handleSelectionChange(val) {
      //this.multipleSelection = val
    },
    addControlMethod() {
      this.editType = 'add'
      this.controlMethod = {}
      this.addcontrolMethodLog = true
      api.getCodeNumber().then(res => {
        this.controlMethod.code = res.data.data
      })
    },
    addControlMethodSure() {
      this.$refs['controlMethod'].validate((valid) => {
        if (valid) {
          if (this.editType === 'add') {
            api.addCheckControl(this.controlMethod)
                .then(() => {
                  this.getControlList(1, 'add',this.controlMethod.code	)
                })
          } else if (this.editType === 'edit') {
            let json = {
              "id": this.controlMethod.id,
              "name": this.controlMethod.name,
              "code": this.controlMethod.code,
              "briefDesc": this.controlMethod.briefDesc
            }
            api.updateCheckControl(json)
                .then(() => {
                  this.getControlList(this.controlType, 'edit',this.controlMethod.code)
                })
          }
          this.addcontrolMethodLog = false
        } else {
          console.log('error submit!!');
          return false;
        }
      })

    },
    controlMethodEdit(info, source) {
      this.editType = 'edit'
      this.controlType = source
      this.controlMethod = info
      this.addcontrolMethodLog = true
    },
    controlMethodStop(type, id, index) {
      this.controlIndex = index
      this.controlType = type
      this.controlAbleId = id
      if(type === 1) {
        this.controlTip = '确定后，此种控制方法将能再次被选择到！'
      } else if(type === 0) {
        this.controlTip = '确定后，此种控制方法将不再能被选择到！'
      }
      this.ableControlLog = true
    },
    controlMethodDel(id, source, index) {
      if (source === 1) {
        let isOk = this.multipleSelection.find(item => item.id == id)
        if (isOk) {
          this.$message('操作失败！\n被应用过的控制方法不可删除！如需要，可停用！');
          return false
        }
      }
      this.controlIndex = index
      this.controlAbleId = id
      this.controlType = source
      this.delControlLog = true
    },
    controlMethodRecordBtn(id) {
      api.getCheckControlRecord(id)
          .then(res => {
            this.controlMethodRecord = res.data.data
            this.controlMethodRecordLog = true
          })
    },
    controlMethodRecordScan(id) {
      api.getCheckControlRecordDetail(id)
          .then(res => {
            this.controlMethodRecordDetail = res.data.data
            this.controlMethodRecordScanLog = true
          })
    },
    controlMethodStopSure() {
      let json = {
        id: this.controlAbleId,
        enabled: this.controlType
      }
      api.startOrStopCheckControl(json)
          .then(() => {
            if (this.controlType === 0) {
              this.controlMethodsList.splice(this.controlIndex, 1)
            } else {
              this.controlMethodsList.push(this.controlMethodsSusList[this.controlIndex])
              this.controlMethodsSusList.splice(this.controlIndex, 1)
            }
          })
      this.ableControlLog = false
    },
    delControlSure() {
      this.delControlLog = false
      api.deleteCheckControl(this.controlAbleId).then(() => {
        if (this.controlType === 1) {
          this.controlMethodsList.splice(this.controlIndex, 1)
        } else {
          this.controlMethodsSusList.splice(this.controlIndex, 1)
        }
      })
    },
    pdImageManage(){
      api.getPdBaseOne(this.productDetail.id)
          .then(res => {
            this.pdImageInfo = res.data.data
            this.pdImageLog = true
          })
    },
    pdImgSuccess(response, file, fileList,type){
      let json = {
        product: this.productDetail.id,
        category: type,//类型：1-产品图纸,2-控制点序号图
        title: response.displayName,
        type: 1,
        uplaodPath: response.filename
      }
      api.savePdBaseImage(json)
          .then(() => {
            this.pdImageManage()
            this.uploadLoading = false
          })
    },
    uploadLoadingFun(){
      this.uploadLoading = true
    },
    pdImageDel(type){
      let json = {
        product: this.productDetail.id,
        category: type //类型：1-产品图纸,2-控制点序号图
      }
      api.removePdBaseImage(json)
          .then(() => {
            if (type === 1) {
              this.pdImageInfo.cptzImage = null
            } else if (type === 2) {
              this.pdImageInfo.xhImage = null
            }
          })
    },
    optionSettings(){
      const list = JSON.stringify(this.featureFrom.featureOptionJson || [{name:''},{name:''}])
      this.optionForm.optionList = JSON.parse(list)
      this.optionSettingsLog = true
    },
    addOneOption(){
      this.optionForm.optionList.push({name:''})
    },
    delOptionList(index){
      this.optionForm.optionList.splice(index, 1)
    },
    addOptionSure(){
      this.$refs['optionForm'].validate((valid) => {
        if (valid) {
          let str = ``
          this.featureFrom.featureOptionJson = this.optionForm.optionList
          this.optionForm.optionList.forEach(function (item){
            str += `选项“${item.name}”，`
          })
          str = str.slice(0, -1)
          this.featureFrom.featureOptionListStr = str
          this.optionSettingsLog = false
        } else {
          this.$message('操作失败，还有必做项尚未完成！');
          return false;
        }
      })
    },
    ptFeaturesScan(info){
      const result = JSON.stringify(info)
      this.ptFeaturesScanLog = true
      this.ptFeatureDetail = JSON.parse(result)
    },
    ptFeaturesEdit(info){
      let that = this
      const result = JSON.stringify(info)
      const featureDetail = JSON.parse(result)
      let str = ``
      let arr3 = []
      this.logType = 'edit'
      this.featureTtl = '修改产品特性'
      this.addPtFeaturesLog = true
      this.featureFrom = featureDetail
      this.featureFrom.category = featureDetail.category + ''
      this.featureFrom.featureOptionJson = featureDetail.featureOptionList
      this.examArr = []
      if (featureDetail.itemCheckList1.length > 0) {
        this.examArr.push('1')
        featureDetail.itemCheckList1.map(item => {
          item.name = item.equEquipmentName
          item.modelName = item.equModelName
        }	)
        this.testScanList1 = featureDetail.itemCheckList1
      }
      if (featureDetail.itemCheckList2.length > 0) {
        this.examArr.push('2')
        featureDetail.itemCheckList2.map(item => {
          item.name = item.equEquipmentName
          item.modelName = item.equModelName
        }	)
        this.testScanList2 = featureDetail.itemCheckList2
      }
      if (featureDetail.itemCheckList3.length > 0) {
        this.examArr.push('3')
        featureDetail.itemCheckList3.forEach(item => {
          if (item.checkControlObj){
            let val= that.controlMethodsList.find(value => value.id == item.checkControlObj.id)
            if (val) {
              arr3.push(val)
            }
          }
        })
      }
      this.featureFrom.itemCheckJson1 = featureDetail.itemCheckList1
      this.featureFrom.itemCheckJson2 = featureDetail.itemCheckList2
      this.featureFrom.itemCheckJson3 = arr3
      this.multipleSelection = arr3
      featureDetail.featureOptionList.forEach(function (item){
        str += `选项“${item.name}”，`
      })
      str = str.slice(0, -1)
      this.featureFrom.featureOptionListStr = str
      this.setRankData()
      api.getPdBaseOne(this.productDetail.id)
          .then(res => {
            let detail = JSON.stringify(res.data.data)
            this.editImageInfo = JSON.parse(detail)
          })
      // this.getEqList()
      if (featureDetail.category <= 7) {
        this.featureFrom.upperQualified = featureDetail.upperQualified + ''
        this.featureFrom.lowerQualified = featureDetail.lowerQualified + ''
      }
    },
    testScanListDel(type, index){
      if (type === 1) {
        let icon = this.featureFrom.itemCheckJson1.findIndex(item => item.equEquipment == this.testScanList1[index].equipmentId)
        if (icon > -1) this.featureFrom.itemCheckJson1.splice(icon, 1)
        this.testScanList1.splice(index, 1)
      } else  if (type === 2) {
        let icon = this.featureFrom.itemCheckJson2.findIndex(item => item.equEquipment == this.testScanList2[index].equipmentId)
        if (icon > -1) this.featureFrom.itemCheckJson2.splice(icon, 1)
        this.testScanList2.splice(index, 1)
      } else  if (type === 3) {
        let icon = this.featureFrom.itemCheckJson3.findIndex(item => item.checkControl == this.multipleSelection[index].equipmentId)
        if (icon > -1) this.featureFrom.itemCheckJson3.splice(icon, 1)
        this.multipleSelection.splice(index, 1)
      }
    },
    ptFeaturesDel (info, index) {
      this.ptFeatureDetail = info
      this.featureIndex = index
      this.delFeatureLog = true
    },
    delFeatureSure () {
      api.deleteFeature(this.ptFeatureDetail.id)
          .then(() => {
            this.productFeatureList.splice(this.featureIndex, 1)
            this.delFeatureLog = false
          })
    },
    realPath (path) {
      return auth.webRoot + '/upload/' + path
    },
    pdImageRecord(type){
      this.recordSource = type
      let json = {
        product: this.productDetail.id,
        category: type
      }
      let url = ``
      if (type === 1 || type === 2) {
        url = '/product/getPdBaseImageRecord.do'
      } else {
        url = `../product/getPdResourceRecord.do`
      }
      api.getPdBaseImageRecord(json, url).then(res => {
        var list = res.data['data'] || [];
        this.ptImgRecordList = list
        this.ptImgRecordListLog = true
      })
    },
    relateResourceSee(){
      this.relateFileLog = true
    },
    addFile(){
      this.chooseSaveFolderLog = true
      this.fileSortDown = true
      this.fileInfo = {
        pageInfo: {},
        fileList: []
      }
      api.getFirstDoc().then(res => {
        var list = res.data.data['listFirstFolder'] || [];
        this.listFirstFolder = list
      })
    },
    overconact(resourceId){
      this.relasecntactLog = true
      this.resourceId = resourceId
    },
    madesure(){
      let json = {
        product: this.productDetail.id,
        resourceId: this.resourceId
      }
      api.securePdResource(json).then(() => {
        this.relasecntactLog = false
        let index = this.pdImageInfo.resourceList.findIndex(value => Number(value.resource) === Number(this.resourceId))
        if (index > -1) {
          this.pdImageInfo.resourceList.splice(index, 1)
        }
      })
    },
    fileListStr(data){
      this.fileInfo = data
      this.fileSelectId = ''
      this.treeItemActiveId = data.categoryId
    },
    fileSort(type){
      let sort = 1
      if (type === this.fileSortType) {
        this.fileSortDown = !this.fileSortDown
      } else {
        this.fileSortType = type
        this.fileSortDown = true
      }
      if (type === 1) {
        if (this.fileSortDown) {
          sort = 1
        } else {
          sort = 2
        }
      } else {
        if (this.fileSortDown) {
          sort = 4
        } else {
          sort = 3
        }
      }
      this.sort = sort
      /* let json = {
         "categoryId" : this.treeItemActiveId,
         "currentPageNo" : 1,
         "pageSize" : 20,
         "type" : sort
       }*/
      this.getFileList(1)
    },
    getFileList( curr){
      let json = {
        "categoryId" : this.treeItemActiveId,
        "currentPageNo" : curr,
        "pageSize" : 20,
        "type" : this.sort
      }
      api.getFile(json).then(res => {
        this.fileInfo.fileList = res.data.data.list
      })
    },
    getFileListPage(pageItem){
      this.getFileList(pageItem.page)
    },
    basicMessageBtn(id){
      let json = {"id" : id}
      let url = '../res/getFileMessage.do'
      let history = false // 是否是换版记录中的文件
      this.fileId = id
      if (history) {
        url = "../res/getUpFileMes.do"
      }
      api.getBasicMessage(json, url).then(res => {
        this.basicMessage = res.data.data || {}
        // listUser, 总务的列表
        this.docInfoScanLog = true
      })
    },
    chargeRecord(id){
      let json = {
        "fileId": id,
        "currentPageNo": 1,
        "pageSize": 20
      }
      api.changeVersionRecord(json).then(res => {
        let data = res["data"] ;
        var pageInfo = data.data["pageInfo"],
            fileInfo = data.data["list"] || [];
        this.versionHistory = fileInfo
        this.resHistorylog = true
      })
    },
    // creator: 张旭博，2018-05-16 16:29:28，查看各种操作记录
    seeHandelRecordBtn(type){
      //更改弹窗标题
      var recordTitleName = '';
      switch (type){
        case 1:
          recordTitleName = '浏览记录';
          break;
        case 2:
          recordTitleName = '下载记录';
          break;
        case 5:
          recordTitleName = '移动记录';
          break;
        case 6:
          recordTitleName = '文件名称修改记录';
          break;
        case 7:
          recordTitleName = '文件编号修改记录';
          break;
      }
      this.recordTitleName = recordTitleName
      this.fileHandleRecordLog = true
      this.fileHandleRecordSource = type

      //渲染操作记录表格
      this.setFileHandelRecord(type)
    },
// creator: 张旭博，2018-05-16 16:46:20，获取各种操作记录
    setFileHandelRecord(type) {
      var currentFileId = this.fileId
      if(type === 1||type === 2){
        let json = {
          "id"  : currentFileId,
          "type": type
        }
        api.getRecordByShow(json).then(res => {
          if(res.data["success"] === 1) {
            this.fileRecordList = res.data.data || []
          }
        })
      }else{
        let json = {
          "fileId"  : currentFileId,
          "operation": type
        }
        api.getMoveRecord(json).then(res => {
          if(res.data["success"] === 1) {
            this.fileRecordList = res.data.data || []
          }
        })
      }
    },
    /* creator : 侯杏哲 2017-12-16 判断当前是否为总务 */
    chargeZW(testID , arr) {
      var isZW = false ;
      if(arr && arr.length > 0){
        for(var i = 0 ; i < arr.length ; i++){
          var id = arr[i]["userID"] ;
          if( id == testID){ isZW = true ;  }
        }
      }
      return isZW ;
    },
    sureChooseFolder() {
      if (this.fileSelectId !== '') {
        let fileList = this.fileInfo.fileList
        let info = fileList.find(item => Number(item.id) === Number(this.fileSelectId))
        var version = info.version;
        let answer = ``
        switch (version) {
          case "txt"://文件
          case 'doc':
          case 'docx':
          case 'xls':
          case 'xlsx':
          case 'ppt':
            answer = 3;
            break;
          case 'jpg'://图片
          case 'jpeg':
          case 'png':
          case 'gif':
            answer = 1;
            break;
          case 'mp4'://视频
            answer = 2;
            break;
        }
        let json = {
          product: this.productDetail.id,
          resource: this.fileSelectId,
          type: answer,
          title:info.name
        }
        this.chooseSaveFolderLog = false
        api.chooseFolderSure(json).then(() => {
          this.pdImageManage()
        })
      }

    },
    addUnit() {
      this.unitForm.name = ""
      this.addUnitLog = true
    },
    addUnitOkBtn(module) {
      var name = this.unitForm.name;
      api.addUnitOk({module,name}).then(res => {
        var status = res.data['status'] ;
        if(status ==1){
          this.$message({
            'type':'success',
            'message':'新增成功！'
          })
          this.addUnitLog = false
          api.getUnitList({ 'module': module }).then(res => {
            var list = res.data['list'] || [];
            this.unitList = list
          })
        }else {
          var tipStr = '' ;
          if(status == 0){
            tipStr = '新增失败！系统中已经有这个计量单位了。' ;
          }else if(status == 2){
            tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
          }
          this.$message({
            'type':'error',
            'message': tipStr
          })
        }
      })
    },

    backPre(num) {
      let page = num
      if (num === 77){
        this.initState === 0 ? page = 1:page = 3
      } else if (page === 1) {
        this.initState = 0
      }
      this.mainNum = page
    },
    toogeCircle(){
      if (this.productFeatureList.length > 0) {
        this.sflag = !this.sflag
      } else {
        this.$messageBox.alert(
            '<p class="ty-center">操作失败！</p><p>请至少录入一项产品特性！</p>',
            '',
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '知道了',
              center: true
            })
      }
    },
    tableCellStyle() {
      return {'border-color': '#c9c6c6'};
    },
    indexTbStyle() {
      return {'border-color': '#c9c6c6', "padding": '2px 0'};
    },
    recordSee (proid) {
      let keys = {
        resIds: proid
      }
      api.getResForProduct(keys)
          .then(res => {
            let url = ``
            var lanks = res.data.data.list;//lanks是个数组
            var eleLink = document.createElement('a');
            eleLink.style.display = 'none';
            eleLink.target = '_blank';

            lanks.forEach(item =>{
              var id = item.id;
              if(id == proid){
                var path = item.path;//文件的地址
                //该怎么把文件内容展示出来呢？？？？？
                var pathArr = path.split(".");
                var type = pathArr[pathArr.length - 1].toLowerCase() ;
                switch (type){//瞧着似乎已经对照范例判断了不同格式的文件，可是怎么让它跳转到新的页面展示呢？？？？
                  case 'doc':case 'docx':
                  case 'xls':case 'xlsx':
                  case 'ppt':case 'pptx':
                    url = 'https://view.officeapps.live.com/op/view.aspx?src='+this.rootPath.fileUrl + path
                    break;
                  case 'png':case 'jpg':case 'jpeg':case 'gif':
                  case "pdf":
                    url = this.rootPath.fileUrl + path
                    break;
                  case "md":
                    url = this.rootPath.webRoot+'/assets/md/index.html?src='+ this.rootPath.fileUrl + path
                    break;
                  default:
                    url = this.rootPath.ow365url + path
                    break;
                }
                eleLink.href = url;
                document.body.appendChild(eleLink);
                eleLink.click();
                document.body.removeChild(eleLink);
              }
            })
          })
    },
    oprationFile (event, type) {
      let el = event.currentTarget
      previewOrDownloadFun(el, type, this)
    },
    seeOnline (event, id) {
      let json = {"id" : id}
      let url = '../res/getFileMessage.do'
      let el = event.currentTarget
      api.getBasicMessage(json, url)
      previewOrDownloadFun(el, 1, this)
    },
    sizeUnitCharge(unitId){
      let str = ``
      let unitItem = this.unitList.find(item => item.id == unitId)
      if (unitItem) str = unitItem.name
      return str
    },
    rankSymbolCharge(rank){
      let str = ``
      let rankItem = this.rankScanData.find(item => item.id == rank)
      if (rankItem) str = rankItem.symbol
      return str
    },
    changeCatergray(){
      this.featureFrom.name = ''
      this.featureFrom.sizeUnit = ''
      this.featureFrom.baseSize = ''
      this.featureFrom.ecartSuperieur = ''
      this.featureFrom.ecartInferieur = ''
      this.featureFrom.rank = ''
      this.featureFrom.upperQualified = ''
      this.featureFrom.lowerQualified = ''
      this.featureFrom.controlContent = ''
      this.featureFrom.featureOptionListStr = ''
      this.featureFrom.featureOptionList = []
      this.featureFrom.textLimit = ''
      this.featureFrom.decimalsLimit = ''
    },
    baseSizeRule(rule, val, callback){
      console.log("rule:", JSON.stringify(rule))
      let mesg = ``;
      if (Number(this.featureFrom.category) === 1) {
        mesg = `请录入基本尺寸!`;
      } else {
        mesg = `请录入基准值!`;
      }
      if (val === '') {
        callback(new Error(mesg));
      } else {
        let reg = ``
        if (Number(this.featureFrom.category) === 1) {
          reg = /^(\d*)(\.\d{1,10})?$/
          if (Number(val) <= 0) {
            callback(new Error('只允许录入正数'))
            return false
          }
        } else if (Number(this.featureFrom.category) >= 2 && Number(this.featureFrom.category) <= 7) {
          reg = /^-?(\d*)(\.\d{1,10})?$/
        }
        if(!reg.test(val)) {
          callback(new Error('系统不支持录入小数点后有效数字超过10位的数字！'));
        } else {
          callback();
        }
      }
    },
    checkIsTen(rule, val, callback){
      let mesg = ``;
      this.isBlueTip = false
      if (Number(this.featureFrom.category) === 1) {
        if (rule.field === 'ecartSuperieur'){
          mesg = `请录入上偏差!`;
        } else {
          mesg = `请录入下偏差!`;
        }
        this.compareOffset()
      } else {
        if (rule.field === 'ecartSuperieur'){
          mesg = `请录入上限!`;
        } else {
          mesg = `请录入下限!`;
        }
        this.limitTopSize()
      }
      if (val === "") {
        callback(new Error(mesg));
      } else {
        let isNum = /^-?\d+(\.\d+)?$/
        if (!isNum.test(val)) {
          callback(new Error('请录入数字！'));
        } else if (!/^-?\d+(\.\d{1,10})?$/.test(val)) {
          callback(new Error('系统不支持录入小数点后有效数字超过10位的数字！'));
        } else {
          callback();
        }
      }
    },
    chargeOperation(type, source){
      let str = ``
      if (source === 1 || source === 2){
        switch (Number(type)) {//1-上传,2-移除,3-更换
          case 1:
            str = `上传`
            break;
          case 2:
            str = `移除`
            break;
          case 3:
            str = `更换`
            break;
        }
      } else if (source === 3){
        if (Number(type) === 1) {
          str = `与文件关联`
        } else {
          str = `与文件解除关联`
        }
      } else if (source === 4) {
        if (Number(type) === 1) { //操作:1-增,2-删,3-改,4-停用,5-启用
          str = `创建`
        } else if (Number(type) === 2) {
          str = `删除`
        } else if (Number(type) === 3) {
          str = `修改`
        } else if (Number(type) === 4) {
          str = `停用`
        }  else if (Number(type) === 5) {
          str = `启用`
        }
      }
      return str
    },
    chargeUnit(val , type) {
      var str = ""
      switch (type){
        case 'weightUnit':
          if(val == "1"){ str = "毫克"; }
          if(val == "2"){ str = "克"; }
          if(val == "3"){ str = "千克"; }
          if(val == "4"){ str = "吨"; }
          break;
        case 'sizeUnit':
          if(val == "1"){ str = "mm"; }
          if(val == "2"){ str = "cm"; }
          if(val == "3"){ str = "dm"; }
          if(val == "5"){ str = "m"; }
          break;
      }
      return str
    },
    limitIntWord(){
      let value = ``
      let newVal = this.featureFrom.orders
      if (newVal && newVal !== '') {
        value = newVal.replace(/[^\d]/g, '');
        if (value === '0' || value === 0) {
          value = '';
        }
      }
      this.featureFrom.orders = value
    },
    addPtFeaturesHide() {
      this.addPtFeaturesLog = false
    },
    ptFeaturesScanHide() {
      this.ptFeaturesScanLog = false
    },
    pdImageHide() {
      this.pdImageLog = false
    },
    imgExplainHide() {
      this.imgExplainLog = false
    },
    testLogHide() {
      this.testLog = false
    },
    controlMethodsHide() {
      this.controlMethodsLog = false
    },
    controlMethodsSusHide() {
      this.controlMethodsSusLog = false
    },
    controlMethodRecordHide() {
      this.controlMethodRecordLog = false
    },
    controlMethodRecordScanHide() {
      this.controlMethodRecordScanLog = false
    },
    addcontrolMethodHide() {
      this.addcontrolMethodLog = false
    },
    testScanHide() {
      this.testScanLog = false
    },
    detailTestScanHide() {
      this.detailTestScanLog = false
    },
    rankScanHide() {
      this.rankScanLog = false
    },
    rankReHide() {
      this.rankReLog = false
    },
    addRankHide() {
      this.addRankLog = false
    },
    editNameHide() {
      this.editNameLog = false
    },
    editSymbolHide() {
      this.editSymbolLog = false
    },
    rankRecordHide() {
      this.rankRecordLog = false
    },
    rankRecordScanHide() {
      this.rankRecordScanLog = false
    },
    addUnitHide() {
      this.addUnitLog = false
    },
    optionSettingsHide() {
      this.optionSettingsLog = false
    },
    ptImgRecordListHide() {
      this.ptImgRecordListLog = false
    },
    chooseSaveFolderHide() {
      this.chooseSaveFolderLog = false
    },
    resHistoryHide() {
      this.resHistorylog = false
    },
    relateFileHide() {
      this.relateFileLog = false
    },
    docInfoScanHide() {
      this.docInfoScanLog = false
    },
    fileHandleRecordHide() {
      this.fileHandleRecordLog = false
    },
    ableControlHide() {
      this.ableControlLog = false
    },
    delControlHide() {
      this.delControlLog = false
    },
    delFeatureHide() {
      this.delFeatureLog = false
    },
    delRankHide() {
      this.delRankLog = false
    },
    relasecntactHide() {
      this.relasecntactLog = false
    },
  }
}
</script>

<style lang="scss" scoped>
.productFeatures{
  font-size: 14px;
  .iconEcart{display: inline-block;width: 100%;}
  .posTop{position: absolute;top: -10px;}
  >div{ margin-left: 100px;margin-right: 50px;}
  .gapLt{margin-left: 10px;}
  .gapLtFar{margin-left: 50px;}
  .gapLarg{margin-bottom: 250px;}
  .keepCon{ width: 500px;}
  .txtRight{text-align: right;}
  .symSize{ width: 80%;border: 1px solid #dedede;line-height: 32px;height: 32px;}
  .symSize img,.symLimit img{max-height: 100%;}
  .symLimit{border: 1px solid #dedede;line-height: 32px;height: 32px;background: #dddddd;}
  .symBtn{ margin-left: 30px;}
  .el-row{margin-bottom: 16px;}
  .mainHead{margin-top: 20px;margin-bottom: 26px;}
  .kk-hr{margin: 16px 0;width: 100%;height: 1px;background: #d7d7d7;}
  .el-row .el-form-item{margin-bottom: 0;}
  .flexRight .el-form-item__label{justify-content: flex-end!important;}
  .linkGap .el-link {margin-left: 12px;}
  .linkGapLt .el-link {padding-left: 16px;}
  .explainCon > div{margin-bottom: 40px;}
  .el-table {border: 1px solid #c6c6c6;}
  .el-table .cell{color: #5b5959;}
  .el-table .el-table__cell{padding: 0}
  .mainBody .el-table .el-table__cell{padding: 20px 0!important;}
  .el-table .el-button{border: none; background: none;font-size: 12px}
  .el-table .el-button:hover{border: 1px solid #409eff; background: #409eff;font-size: 12px}
  .el-table th.gutter{display: table-cell!important;}
  .sizeImg img{line-height: 26px;max-height: 26px;}
  .text-ellipsis {width: 190px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
  .gapRt{margin-right: 20px;}
  .gapBro{margin: 0 20px;}
  .el-radio-group.testRadios{display: block;}
  .testRadios .el-link {line-height: 30px;}
  .limitWid{width: 80%;}
  .centerHead{text-align: center;}
  .kk-decrease {margin-bottom: 16px; }
  .kk-decrease .el-row {margin-top: 10px;margin-bottom: 0;height: 28px;line-height: 28px; }
  .scanBox{ padding: 2px 16px;background: #fff;width: 120px;display: inline-block;height: 28px;}
  .pictureArea {width: 450px;height:280px;background: #e1edd9;}
  .pictureArea img{max-height:100%;background: #e1edd9;}
  .tipSpace >div{margin-bottom: 20px;text-align: center;}
  .tipSpace >div:first-child{padding-top: 100px;}
  .tipSpace >img{max-height: 100%;}
  .backPage{margin: 20px 0;}
  .orderCss{margin-top: -2px;
    border: 1px solid #666;
    border-radius: 10px;
    min-width: 17px;
    display: inline-block;
    text-align: center;
    font-size: 10px;
    line-height: 17px;}
  .iconEcartWrap{display:inline-block;width: 100px;font-size: 12px;}
  .ttlRt{padding-right: 30px; text-align: right;}
  .sizeo {flex: none !important;width: 627px !important;position: relative !important;}
  .sizenm {text-align: left;width: 322px !important;}
  .sizeno {top: 0px !important;}
  .sizesi {top: 0px !important;}
  .sizehd {bottom: 0px !important;}
  .sizeote {width: 400px;text-align: right;}
  .pageBg{padding: 10px;background-color: #f0f8ff; }
  .pdImageCss .el-row{margin-bottom: 24px;}
  .largeFont .el-link{margin-top: 2px;}
  .pageCon{margin-top: 40px;}
  .el-form-item__error{white-space: nowrap;}
  .midSize{line-height: 32px;}
  .link-blue{color: #0b94ea;cursor: pointer;}
  .link-gray{color: #d3d3d3;cursor: not-allowed;}
  .el-pagination{justify-content: center;}
  .fScan {padding: 20px 0;background-color: #dceaf5;}
  .fScan .el-row {margin-bottom: 26px;}
  .fScan .el-row:not(.smTtl) {line-height: 28px;}
  .orderTtl{display: inline-block;width: 104px;padding-right: 30px;text-align: right;}
  .iconfont{font-size: 14px;}
  .flexBox{display: flex}
  .grayBox{
    height: 28px;
    background: #ccc;
    padding: 2px 16px;
    width: 120px;
  }
}
</style>
<style lang="scss">
.right-align .el-form-item__label {justify-content: right;}
.limitHt .el-form-item__label{line-height: inherit;}
.limitHt label{line-height: inherit;}
.el-form-item__error{white-space: nowrap; }
.isBlueTip .el-input__wrapper{box-shadow: 0 0 0 1px var(--el-color-primary) inset;}
</style>
<style>
@import '../../components/folderTree/fileTree.css'; /*这个分号一定要写，不然会报错*/
@import '../../components/folderTree/wenjian.css';
@import '../../style/iconFont/iconfont.css';
</style>