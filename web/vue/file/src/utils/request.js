import auth from "@/sys/auth"
import axios from "axios"

//提前auth初始化
auth.init({isGuest: true})
// const request = axios

//可以添加多个不同的request
const requests = {
    default: axios.create({
        // baseURL: auth.webRoot,
    }),
    // second:axios.create()
}
console.log('requests', requests)
//所有request都需要添加auth.init中设置的默认拦截器
Object.keys(requests).forEach((name) =>{
    requests[name].interceptors.request.handlers.push(...axios.interceptors.request.handlers)
    requests[name].interceptors.response.handlers.push(...axios.interceptors.response.handlers)
})
//给指定request需要添加拦截器
requests.default.interceptors.response.use(
  res => {
      // console.log("request 响应拦截器")
      if ( res.status !== 200) {
          return Promise.reject("响应非200！");
      } else {
          return res.data
      }
  },
  error => {
      return Promise.reject(error)
  }
)
export const { default: Request, } = requests