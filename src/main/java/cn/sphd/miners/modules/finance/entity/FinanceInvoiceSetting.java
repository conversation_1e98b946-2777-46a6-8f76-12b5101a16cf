package cn.sphd.miners.modules.finance.entity;

import cn.sphd.miners.modules.system.entity.Organization;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2017/7/7.
 * 发票设置表
 */
@Entity
@Table(name="t_finance_invoice_setting")
public class FinanceInvoiceSetting implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="lines_limited"   , nullable=true , unique=false)
    private Integer linesLimited;  //每张发票能开的行数上限

//    @Column(name="tax_rate"   , nullable=true , unique=false)
//    private BigDecimal taxRate;  //税率

    @Column(name="amount_limited"   , nullable=true , unique=false)
    private Integer amountLimited;  //每张发票能开的金额上限

    @Column(name="category"  , length=1 , nullable=true , unique=false)
    private String category; //'发票类型:1-增值税专用票,2-增值税普通票,3-其它普通票'

    @Column(name = "has_attachment")
    private boolean hasAttachment;//是否含有附件,true-是/false-否

    @Column(name = "multiple_rate")
    private boolean multipleRate;//是否包含多税率,true-是/false-否

    @Column(name="enable_tax_rate"  , length=100 , nullable=true , unique=false)
    private String enableTaxTate;//有效税率,多税率以,分隔

    @Column(name="disable_tax_rate"  , length=100 , nullable=true , unique=false)
    private String disableTaxRate;//失效税率,多税率以,分隔

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;


    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="org", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private Organization organization;

    @Column(name="org"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer org_;

    //与发票设置历史表 一对多
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceInvoiceSettingHistory.class, fetch= FetchType.LAZY, mappedBy="setting", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceInvoiceSettingHistory> financeInvoiceSettingHistoryHashSet = new HashSet<FinanceInvoiceSettingHistory>();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getLinesLimited() {
        return linesLimited;
    }

    public void setLinesLimited(Integer linesLimited) {
        this.linesLimited = linesLimited;
    }

//    public BigDecimal getTaxRate() {
//        return taxRate;
//    }
//
//    public void setTaxRate(BigDecimal taxRate) {
//        this.taxRate = taxRate;
//    }

    public Integer getAmountLimited() {
        return amountLimited;
    }

    public void setAmountLimited(Integer amountLimited) {
        this.amountLimited = amountLimited;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Integer getOrg_() {
        return org_;
    }

    public void setOrg_(Integer org_) {
        this.org_ = org_;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public boolean isHasAttachment() {
        return hasAttachment;
    }

    public void setHasAttachment(boolean hasAttachment) {
        this.hasAttachment = hasAttachment;
    }

    public boolean isMultipleRate() {
        return multipleRate;
    }

    public void setMultipleRate(boolean multipleRate) {
        this.multipleRate = multipleRate;
    }

    public String getEnableTaxTate() {
        return enableTaxTate;
    }

    public void setEnableTaxTate(String enableTaxTate) {
        this.enableTaxTate = enableTaxTate;
    }

    public String getDisableTaxRate() {
        return disableTaxRate;
    }

    public void setDisableTaxRate(String disableTaxRate) {
        this.disableTaxRate = disableTaxRate;
    }

    public Set<FinanceInvoiceSettingHistory> getFinanceInvoiceSettingHistoryHashSet() {
        return financeInvoiceSettingHistoryHashSet;
    }

    public void setFinanceInvoiceSettingHistoryHashSet(Set<FinanceInvoiceSettingHistory> financeInvoiceSettingHistoryHashSet) {
        this.financeInvoiceSettingHistoryHashSet = financeInvoiceSettingHistoryHashSet;
    }
}
