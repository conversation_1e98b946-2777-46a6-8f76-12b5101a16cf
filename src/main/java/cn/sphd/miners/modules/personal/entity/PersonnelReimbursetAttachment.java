package cn.sphd.miners.modules.personal.entity;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_personnel_reimburset_attachment")
public class PersonnelReimbursetAttachment implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description;

    @Column(name="path"  , length=255 , nullable=true , unique=false)
    private String path;

    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="reimburset", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelReimburse reimburset;

    @Column(name="reimburset"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer reimburset_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public PersonnelReimburse getReimburset() {
        return reimburset;
    }

    public void setReimburset(PersonnelReimburse reimburset) {
        this.reimburset = reimburset;
    }

    public Integer getReimburset_() {
        return reimburset_;
    }

    public void setReimburset_(Integer reimburset_) {
        this.reimburset_ = reimburset_;
    }
}
