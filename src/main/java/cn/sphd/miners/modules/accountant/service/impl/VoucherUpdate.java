package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.Accountant1_209_Service;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by root on 17-1-16.
 */
@Service
@Transactional(readOnly=false)
public class VoucherUpdate {

    @Autowired
    TaccountantSubjectRecordMapper recordMapper;
    @Autowired
    SettleMapper settleMapper;
    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    DetailMapper detailMapper;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    TAccountantSubjectCashMapper subjectCashMapper;
    @Autowired
    SubjectHistoryMapper subjectHistoryMapper;
    @Autowired
    SubjectPeroidMapper subjectPeroidMapper;
    @Autowired
    TAccountantCashMapper cashMapper;
    @Autowired
    DetailHistoryMapper detailHistoryMapper;
    @Autowired
    TAccountantSubjectCashHistoryMapper subjectCashHistoryMapper;
    @Autowired
    VoucherHistoryMapper voucherHistoryMapper;
    @Autowired
    GeneralSubjectRecord generalSubjectRecord;
    @Autowired
    TaccountantEstablishEntityMapper establishEntityMapper;
    @Autowired
    BuildAccountMapper buildAccountMapper;
    @Autowired
    Accountant1_209_Service accountant1_209_service;

    public JSONObject insertVoucher(SubjectChooseParams params) {

        User user = params.getUser();
        /*得到本月（或上月）最大凭证序号*/
        /*由于凭证日期无法确定所以咱数不知到在那个月份上递增序号*/
        JSONObject result = new JSONObject();
        if (user == null)
        {
            return result;
        }
        int belong_peroid = params.getBelong_peroid();
        int bill_period = params.getBill_period();
        /*如果本月票据选择了非本月凭证日期返回失败*/
        if(belong_peroid == 2 && bill_period == 1)
        {
            return result;
        }

        String voucherDate = "";
        int source = params.getSource();
        int oid = params.getOid();
        //如果不是会计录入的凭证，则根据票据月份生成凭证日期
        if(!("1".equals(source + "")))
        {
            voucherDate = getVoucherDate(belong_peroid);
        }
        else
        {
            //返回未结帐月份的最后一天
            voucherDate = judgeSettledMonth(oid);
            String[] strs = voucherDate.split("-");
            Calendar c = Calendar.getInstance();
            int month = c.get(Calendar.MONTH);
            if(Integer.parseInt(strs[1]) == month)
                params.setBelong_peroid(2);
        }


        int state = 0;
        /*新增凭证,并把新产生的凭证返回*/
        String summary = params.getSummary();
        int mode = params.getMode();
        int userID = user.getUserID();
        String userName = user.getUserName();
        int is_account = params.getIs_account();
        String reason = params.getReason();
        Integer bill_detail = params.getBill_detail();
        String memo = params.getMemo();
        int approve_status = params.getApprove_status();
        String operatorName = params.getOperatorName();
        params.setVoucherDate(voucherDate);
        int res = newVoucher(params,null);
        //如果是上月凭证的话更新结帐表中的need_trial字段值为1
        if (belong_peroid == 2 && res > 0)
        {
            updateNeedTrialField(oid,"1");
        }
        state = 1;
        if(!voucherDate.equals(""))
        {
            voucherDate = voucherDate.replaceFirst("-","年");
            voucherDate = voucherDate.replaceFirst("-","月");
            voucherDate += "日";
        }
        result.put("id",res);
        result.put("belong_peroid",belong_peroid);
        result.put("mode",mode);
        result.put("type",1);
        result.put("is_account",is_account);
        result.put("reason",reason);
        result.put("bill_detail",bill_detail);
        result.put("summary",summary);
        result.put("operator_name",userName);
        result.put("memo",memo);
        result.put("creator",userID);
        result.put("create_name",operatorName);
        result.put("create_date",voucherDate);
        result.put("approve_status",approve_status);
        result.put("source",source);
        result.put("state",state);
        result.put("settleType",0);
        return result;
    }

    /*判断那个月份没有结帐，返回凭证日期*/
    public String judgeSettledMonth(int oid)
    {
        SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM");
        Calendar c = Calendar.getInstance();
        c.set(Calendar.MONTH,c.get(Calendar.MONTH) - 1);
        String premonth = simpleFormat.format(c.getTime());
        TAccountantSettle settled = new TAccountantSettle();
        settled.setOrg(oid);
        settled.setPeriod(premonth);
        String voucherDate = "";
        //获得上个月结帐的数据
        settled = settleMapper.getSettleDay(settled);
        //如果上个月没有结帐就返回上个月日期，否则返回当月日期
        if(settled == null)
        {
            //上月
            voucherDate = getVoucherDate(2);

        }
        else
            voucherDate = getVoucherDate(1);//当月
        return voucherDate;
    }

    /*
     * 根据票据月份得到凭证日期
     * 1-本月票据
     * 2-上月票据
     * */
    public String getVoucherDate(int belong_peroid)
    {
        String voucherDate = "";
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //本月票据
        if(belong_peroid == 1)
        {
            //当月最后一天
            int cur = calendar.getActualMaximum(Calendar.DATE);
            calendar.set(Calendar.DATE, cur);
            voucherDate = sdf.format(calendar.getTime());
        }
        else
        {
            //上月最后一天
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.DATE, -1);
            voucherDate = sdf.format(calendar.getTime());
        }
        return voucherDate;
    }

    /*凭证修改，如果没有结帐可直接修改，否则进行冲洪*/
    public JSONObject updateVoucher(SubjectChooseParams params) {

        JSONObject result = new JSONObject();
        int id = params.getId();
        String summary = params.getSummary();
        String subjectBorrow = params.getSubjectBorrow();
        String subjectLoan = params.getSubjectLoan();
        double price = params.getPrice();
        User user = params.getUser();
        int oid = user.getOid();
        String cashjson = params.getCashjson();
        int userID = user.getUserID();
        String userName = user.getUserName();
        int bill_detail = params.getBill_detail();
        /*判断是否已结帐*/
        TAccountantVoucher voucherParam = new TAccountantVoucher();
        voucherParam.setId(id);
        TAccountantVoucher voucher = voucherMapper.getSingle(voucherParam);
        int isSettled = voucher.getIsSettled();

        result.put("is_settled",isSettled);
        if(isSettled == 0)
        {
            /*没有结帐可直接进行修改
             * 1、先删除原有数据（凭证科目、科目详情、现金流量）
             * 2、新建科目和详情
             * */
            TAccountantVoucherSubject paramSubject = new TAccountantVoucherSubject();
            TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
            TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
            paramSubject.setVoucherId(id);
            List<TAccountantVoucherSubject> subjects = voucherSubjectMapper.getSubjectByVoucher(paramSubject);
            //删除老的凭证科目
            deleteSubjectAndDetail(subjects,detailParam,cashParam);

            //新建科目和详情，现金明细
            newSubjectAndDetail(voucher.getCreateDate(),subjectBorrow,subjectLoan,oid,id,userID,userName,summary,cashjson);

            TAccountantVoucher param = new TAccountantVoucher();
            param.setSummary(summary);
            param.setId(id);
            int res = voucherMapper.update(param);
            if (res > 0)
            {
                //更新结帐表的need_trial=1
                updateNeedTrialField(oid,"1");
            }
            result.put("res",1);
            return result;
        }
        else
        {
            /*如果已经结帐则需先冲洪*/
            int redid = redInvoice(id,oid,7,cashjson);
//            afterApprove(1,redid,oid,cashjson,voucherSubjectMapper,detailMapper,tAccountSubjectMapper,subjectPeroidMapper,
//                    cashMapper,subjectCashMapper,voucherMapper);

//            String voucherDate = judgeSettledMonth(oid,settleMapper);
            /*洪冲之后还需要在产生一个新凭证，注意新产生的凭证和之前冲洪的凭证都有一样的voucher，这个字段可以将这三个凭证链接起来*/

            params.setBelong_peroid(1);
            params.setMode(1);
            params.setCategory(1);
            params.setIs_account(1);
            params.setReason("");
            params.setMemo("");
            params.setSource(1);
            params.setApprove_status(2);
            params.setPricetype(0);
            params.setPurpose("");
            params.setBill_quantity(0);
            params.setBill_period(1);
            params.setKind("");
            params.setOperatorName(userName);
            params.setOperatorFinance(userName);
            String voucherDate = judgeSettledMonth(oid);
            params.setVoucherDate(voucherDate);

            int newid = newVoucher(params,"3");
            //因为新增加的凭证的voucher等于他自己的id，所以在有冲洪的情况下voucher应该等于已经存在的voucher，目的是在页面显示的时候能和老数据显示在一起
            int voucherid = voucher.getVoucher();

            TAccountantVoucher param = new TAccountantVoucher();
            param.setVoucher(voucherid);
            param.setId(newid);
            voucherMapper.update(param);

            result.put("res",1);
            QueryData qd = new QueryData();
            qd.put("redid",redid);
            qd.put("newid",newid);
            List<TAccountantVoucher> list = voucherMapper.getRedVoucher(qd);
            JSONArray arrayVouchers = getAccountantVouchers(list);
            result.put("vouchers",arrayVouchers);
            return result;
        }

    }

    public boolean isMinorSettle(int org,int vid) {
        /*
         * 判断该凭证是否当前是小结账
         * 先查询到他的两个科目中的随意一个
         * 然后去科目表找他的id
         * 然后根据该科目编号和该科目的id去周期表中查state
         * 如果=2的话说明是小结账
         * */
        TAccountantVoucher voucher = new TAccountantVoucher();
        voucher.setId(vid);
        voucher = voucherMapper.getSingle(voucher);
        Byte isSettle = voucher.getIsSettled();
        if (isSettle == 0)
        {
            return false;
        }
        TAccountantVoucherSubject voucherSubject = new TAccountantVoucherSubject();
        voucherSubject.setVoucherId(vid);
        voucherSubject.setDirection("1");
        List<TAccountantVoucherSubject> listBorrow = voucherSubjectMapper.listByVoucherDirection(voucherSubject) ;
        TAccountantVoucherSubject avs = listBorrow.get(0);
        TAccountantSubjectEntity subjectEntity = new TAccountantSubjectEntity();
        subjectEntity.setOrg(org);
        subjectEntity.setSubject(avs.getSubject());
        subjectEntity = tAccountSubjectMapper.getByOrgAndSubject(subjectEntity);
        TAccountantSubjectPeroid subjectPeroid = new TAccountantSubjectPeroid();
        subjectPeroid.setSubjectId(subjectEntity.getId());
        List<TAccountantSubjectPeroid> listPeroid = subjectPeroidMapper.getPeroidListBySubjectID(subjectPeroid);
        subjectPeroid = listPeroid.get(0);
        String state = subjectPeroid.getState();
        if ("2".equals(state))
            return true;
        else
            return false;
    }

    /*删除指定的所有凭证科目、科目明细、现金明细，都是只原凭证的*/
    public void deleteSubjectAndDetail(List<TAccountantVoucherSubject> subjects, TAccountantSubjectDetail detailParam, TAccountantSubjectCash cashParam)
    {

        for (TAccountantVoucherSubject tvs:subjects)
        {
            int subjectID = tvs.getId();
            //删除会计录入的凭证、科目、明细,删除明细之前先把现金流量表的相关数据删除，有外键约束
            detailParam.setSubjectId(subjectID);
            TAccountantSubjectDetail detailSub = detailMapper.getSubjectDetail(detailParam);
            if (detailSub != null)
            {
                int detailid = detailSub.getId();
                cashParam.setDetailId(detailid);
                subjectCashMapper.deleteByDetailID(cashParam);
            }

            detailMapper.deleteDetail(detailParam);
            voucherSubjectMapper.deleteSubject(tvs);
        }
    }

    //删除历史表的科目和明细
    public void deleteHistorySubjectAndDetail(List<TAccountantVoucherSubjectHistory> subjects, TAccountantSubjectDetailHistory detailParam, TAccountantSubjectCashHistory cashParam)
    {

        for (TAccountantVoucherSubjectHistory tvs:subjects)
        {
            int subjectID = tvs.getId();
            //删除会计录入的凭证、科目、明细,删除明细之前先把现金流量表的相关数据删除，有外键约束
            detailParam.setSubjectId(subjectID);
            TAccountantSubjectDetailHistory detailSubHistory = detailHistoryMapper.getSubjectDetail(detailParam);
            if (detailSubHistory != null)
            {
                int detailid = detailSubHistory.getId();
//                cashParam.setDetailId(detailid);
                cashParam.setDetailHistoryId(detailid);
                subjectCashHistoryMapper.deleteByDetailID(cashParam);
            }

//            detailHistoryMapper.deleteDetail(detailParam);
            detailHistoryMapper.deleteHistoryDetail(detailParam);

            subjectHistoryMapper.deleteHistorySubject(tvs);
        }
    }

    public void newSubjectAndDetail(String settleMonth,String subjectBorrow, String subjectLoan, int org, int vid, int userID, String userName, String summary, String cashjson)
    {
        insertSubjectAndDetail(settleMonth,1,subjectBorrow,subjectLoan,org,vid,userID,userName,summary,cashjson);
    }

    /*
     * flag:1 表示写原表  2 表示写历史表
     * */
    private void insertSubjectAndDetail(String settleMonth,int flag,String subjectBorrow,String subjectLoan,int org,int vid,int userID,String userName,String summary,String cashjson)
    {
        SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleFormat.format(new Date());
        List<Integer> borids = new ArrayList<Integer>();
        TAccountantSubjectEntity subjectParam = new TAccountantSubjectEntity();
        JSONArray borrowArray = new JSONArray(subjectBorrow);
        Object paramSubject = null;
        Object paramDetail = null;
        if (flag == 1)
        {
            paramSubject = new TAccountantVoucherSubject();
            paramDetail = new TAccountantSubjectDetail();
        }
        else
        {
            paramSubject = new TAccountantVoucherSubjectHistory();
            paramDetail = new TAccountantSubjectDetailHistory();
        }
        if(borrowArray != null) {

            for (int i = 0; i < borrowArray.length(); i++) {
                JSONObject bor = borrowArray.getJSONObject(i);
                String subBor = bor.optString("subject");
                String subjectNames = bor.optString("subjectNames");
                double subPrice = bor.optDouble("price",0);
                //如果是来自个人报销的票据的话需要把相关产品信息的id写到科目表
                int productID = bor.optInt("productID");
                int resB = 0;
                if (flag == 1)
                {
                    ((TAccountantVoucherSubject)paramSubject).setOrg(org);
                    ((TAccountantVoucherSubject)paramSubject).setVoucherId(vid);
                    ((TAccountantVoucherSubject)paramSubject).setSubject(subBor);
                    ((TAccountantVoucherSubject)paramSubject).setSubjectNames(subjectNames);
                    ((TAccountantVoucherSubject)paramSubject).setDirection("1");
                    ((TAccountantVoucherSubject)paramSubject).setAmount(BigDecimal.valueOf(subPrice));
                    ((TAccountantVoucherSubject)paramSubject).setCreator(userID);
                    ((TAccountantVoucherSubject)paramSubject).setCreateName(userName);
                    ((TAccountantVoucherSubject)paramSubject).setApproveStatus(1 + "");
                    ((TAccountantVoucherSubject)paramSubject).setReimburseBillItem(productID);
                    ((TAccountantVoucherSubject)paramSubject).setAcountDate(settleMonth);
                    voucherSubjectMapper.insert(((TAccountantVoucherSubject)paramSubject));
                    resB = ((TAccountantVoucherSubject)paramSubject).getId();
                    //更新该科目为已用，不能在修改
                    subjectParam.setSubject(subBor);
                    subjectParam.setOrg(org);
                    if(subBor != null && subBor.length() > 4)
                    {
                        subjectParam.setParent(subBor.substring(0,subBor.length() - 4));
                    }
                    else
                    {
                        subjectParam.setParent(subBor);
                    }
                    subjectParam.setUseable("0");
                    tAccountSubjectMapper.updateUseable(subjectParam);
                }
                else
                {
                    ((TAccountantVoucherSubjectHistory)paramSubject).setOrg(org);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setVoucherId(vid);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setSubject(subBor);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setSubjectNames(subjectNames);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setDirection("1");
                    ((TAccountantVoucherSubjectHistory)paramSubject).setAmount(BigDecimal.valueOf(subPrice));
                    ((TAccountantVoucherSubjectHistory)paramSubject).setCreator(userID);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setCreateName(userName);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setApproveStatus(1 + "");
                    ((TAccountantVoucherSubjectHistory)paramSubject).setReimburseBillItem(productID);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setAcountDate(settleMonth);
                    subjectHistoryMapper.insert(((TAccountantVoucherSubjectHistory)paramSubject));
                    resB = ((TAccountantVoucherSubjectHistory)paramSubject).getId();
                    //更新该科目为已用，不能在修改
                    subjectParam.setSubject(subBor);
                    subjectParam.setOrg(org);
                    if(subBor != null && subBor.length() > 4)
                    {
                        subjectParam.setParent(subBor.substring(0,subBor.length() - 4));
                    }
                    else
                    {
                        subjectParam.setParent(subBor);
                    }
                    subjectParam.setUseable("0");
                    tAccountSubjectMapper.updateUseable(subjectParam);
                }

                String lastSub = subBor;
                if (subBor.contains("-"))
                {
                    lastSub = subBor.substring(subBor.lastIndexOf("-") + 1);
                }

                //得到科目的余额方向
                TAccountantSubjectEntity s = new TAccountantSubjectEntity();
                s.setSubject(lastSub);
                s.setOrg(org);
                TAccountantSubjectEntity list = tAccountSubjectMapper.listByFirstSubject(s);
                String borDirec = null;
                int detailBorrowID = 0;
                if(list != null)
                {
                    borDirec = list.getBalanceDirection();
                }

                double creditQuantity = bor.optDouble("creditQuantity",0);
                double debitQuantity = bor.optDouble("debitQuantity",0);
                double unitPrice = bor.optDouble("unitPrice",0);

                //如果改科目是借方科目，则科目余额为借方减贷方,否则（即，在借方科目选择的地方选择了贷方科目）
                if (flag == 1)
                {
                    ((TAccountantSubjectDetail)paramDetail).setSubjectId(resB);
                    ((TAccountantSubjectDetail)paramDetail).setSubject(subBor);
                    ((TAccountantSubjectDetail)paramDetail).setType("1");
                    ((TAccountantSubjectDetail)paramDetail).setSummary(summary);
                    ((TAccountantSubjectDetail)paramDetail).setCredit(BigDecimal.valueOf(subPrice));
                    ((TAccountantSubjectDetail)paramDetail).setDebit(BigDecimal.valueOf(0));
                    ((TAccountantSubjectDetail)paramDetail).setBalanceDirection("1");
                    ((TAccountantSubjectDetail)paramDetail).setCreator(userID);
                    ((TAccountantSubjectDetail)paramDetail).setCreateName(userName);
                    ((TAccountantSubjectDetail)paramDetail).setApproveStatus(1 + "");
                    ((TAccountantSubjectDetail)paramDetail).setCreditQuantity(creditQuantity);
                    ((TAccountantSubjectDetail)paramDetail).setDebitQuantity(debitQuantity);
                    ((TAccountantSubjectDetail)paramDetail).setUnitPrice(unitPrice);
                    ((TAccountantSubjectDetail)paramDetail).setBalance(BigDecimal.valueOf(subPrice));
                    detailMapper.insert(((TAccountantSubjectDetail)paramDetail));
                    detailBorrowID = ((TAccountantSubjectDetail)paramDetail).getId();
                }
                else
                {
                    ((TAccountantSubjectDetailHistory)paramDetail).setSubjectId(resB);
                    ((TAccountantSubjectDetailHistory)paramDetail).setSubject(subBor);
                    ((TAccountantSubjectDetailHistory)paramDetail).setType("1");
                    ((TAccountantSubjectDetailHistory)paramDetail).setSummary(summary);
                    ((TAccountantSubjectDetailHistory)paramDetail).setCredit(BigDecimal.valueOf(subPrice));
                    ((TAccountantSubjectDetailHistory)paramDetail).setDebit(BigDecimal.valueOf(0));
                    ((TAccountantSubjectDetailHistory)paramDetail).setBalanceDirection("1");
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreator(userID);
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreateName(userName);
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreateDate(now);
                    ((TAccountantSubjectDetailHistory)paramDetail).setApproveStatus(1 + "");
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreditQuantity(creditQuantity);
                    ((TAccountantSubjectDetailHistory)paramDetail).setDebitQuantity(debitQuantity);
                    ((TAccountantSubjectDetailHistory)paramDetail).setUnitPrice(unitPrice);
                    ((TAccountantSubjectDetailHistory)paramDetail).setBalance(BigDecimal.valueOf(subPrice));
                    detailHistoryMapper.insert(((TAccountantSubjectDetailHistory) paramDetail));
                    detailBorrowID = ((TAccountantSubjectDetailHistory)paramDetail).getId();
                }

                if (lastSub.startsWith("1001") || lastSub.startsWith("1002"))
                {
                    borids.add(detailBorrowID);
                }

            }

        }

        List<Integer> loanids = new ArrayList<Integer>();
        JSONArray loanArray = new JSONArray(subjectLoan);
        if(loanArray != null)
        {
            for (int i=0;i<loanArray.length();i++)
            {
                JSONObject loan = loanArray.getJSONObject(i);
                String loanSub = loan.optString("subject");
                String subjectNames = loan.optString("subjectNames");
                double loanPrice = loan.optDouble("price",0);
                int resL = 0;
                if (flag == 1)
                {
                    ((TAccountantVoucherSubject)paramSubject).setOrg(org);
                    ((TAccountantVoucherSubject)paramSubject).setVoucherId(vid);
                    ((TAccountantVoucherSubject)paramSubject).setSubject(loanSub);
                    ((TAccountantVoucherSubject)paramSubject).setSubjectNames(subjectNames);
                    ((TAccountantVoucherSubject)paramSubject).setDirection("2");
                    ((TAccountantVoucherSubject)paramSubject).setAmount(BigDecimal.valueOf(loanPrice));
                    ((TAccountantVoucherSubject)paramSubject).setCreator(userID);
                    ((TAccountantVoucherSubject)paramSubject).setCreateName(userName);
                    ((TAccountantVoucherSubject)paramSubject).setApproveStatus(1 + "");
                    ((TAccountantVoucherSubject)paramSubject).setAcountDate(settleMonth);
                    voucherSubjectMapper.insert(((TAccountantVoucherSubject)paramSubject));
                    resL = ((TAccountantVoucherSubject)paramSubject).getId();
                    //更新该科目为已用，不能在修改
                    subjectParam.setSubject(loanSub);
                    subjectParam.setOrg(org);
                    if(loanSub != null && loanSub.length() > 4)
                    {
                        subjectParam.setParent(loanSub.substring(0,loanSub.length() - 4));
                    }
                    else
                    {
                        subjectParam.setParent(loanSub);
                    }
                    subjectParam.setUseable("0");
                    tAccountSubjectMapper.updateUseable(subjectParam);
                }
                else
                {
                    ((TAccountantVoucherSubjectHistory)paramSubject).setOrg(org);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setVoucherId(vid);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setSubject(loanSub);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setSubjectNames(subjectNames);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setDirection("2");
                    ((TAccountantVoucherSubjectHistory)paramSubject).setAmount(BigDecimal.valueOf(loanPrice));
                    ((TAccountantVoucherSubjectHistory)paramSubject).setCreator(userID);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setCreateName(userName);
                    ((TAccountantVoucherSubjectHistory)paramSubject).setApproveStatus(1 + "");
                    ((TAccountantVoucherSubjectHistory)paramSubject).setAcountDate(settleMonth);
                    subjectHistoryMapper.insert(((TAccountantVoucherSubjectHistory)paramSubject));
                    resL = ((TAccountantVoucherSubjectHistory)paramSubject).getId();
                    //更新该科目为已用，不能在修改
                    subjectParam.setSubject(loanSub);
                    subjectParam.setOrg(org);
                    if(loanSub != null && loanSub.length() > 4)
                    {
                        subjectParam.setParent(loanSub.substring(0,loanSub.length() - 4));
                    }
                    else
                    {
                        subjectParam.setParent(loanSub);
                    }
                    subjectParam.setUseable("0");
                    tAccountSubjectMapper.updateUseable(subjectParam);
                }

                String lastSub = loanSub;
                if (loanSub.contains("-"))
                {
                    lastSub = loanSub.substring(loanSub.lastIndexOf("-") + 1);
                }

                TAccountantSubjectEntity l = new TAccountantSubjectEntity();
                l.setSubject(lastSub);
                l.setOrg(org);
                TAccountantSubjectEntity listL = tAccountSubjectMapper.listByFirstSubject(l);
                String loanDirec = null;
                if(listL != null)
                {
                    loanDirec = listL.getBalanceDirection();
                }

                int detailLoanID = 0;
                double creditQuantity = loan.optDouble("creditQuantity",0);
                double debitQuantity = loan.optDouble("debitQuantity",0);
                double unitPrice = loan.optDouble("unitPrice",0);
                if (flag == 1)
                {
                    ((TAccountantSubjectDetail)paramDetail).setSubjectId(resL);
                    ((TAccountantSubjectDetail)paramDetail).setSubject(loanSub);
                    ((TAccountantSubjectDetail)paramDetail).setType("1");
                    ((TAccountantSubjectDetail)paramDetail).setSummary(summary);
                    ((TAccountantSubjectDetail)paramDetail).setCredit(BigDecimal.valueOf(0));
                    ((TAccountantSubjectDetail)paramDetail).setDebit(BigDecimal.valueOf(loanPrice));
                    ((TAccountantSubjectDetail)paramDetail).setBalanceDirection("2");
                    ((TAccountantSubjectDetail)paramDetail).setCreator(userID);
                    ((TAccountantSubjectDetail)paramDetail).setCreateName(userName);
                    ((TAccountantSubjectDetail)paramDetail).setApproveStatus(1 + "");
                    ((TAccountantSubjectDetail)paramDetail).setCreditQuantity(creditQuantity);
                    ((TAccountantSubjectDetail)paramDetail).setDebitQuantity(debitQuantity);
                    ((TAccountantSubjectDetail)paramDetail).setUnitPrice(unitPrice);
                    ((TAccountantSubjectDetail)paramDetail).setBalance(BigDecimal.valueOf(loanPrice));
                    detailMapper.insert(((TAccountantSubjectDetail)paramDetail));
                    detailLoanID = ((TAccountantSubjectDetail)paramDetail).getId();
                }
                else
                {
                    ((TAccountantSubjectDetailHistory)paramDetail).setSubjectId(resL);
                    ((TAccountantSubjectDetailHistory)paramDetail).setSubject(loanSub);
                    ((TAccountantSubjectDetailHistory)paramDetail).setType("1");
                    ((TAccountantSubjectDetailHistory)paramDetail).setSummary(summary);
                    ((TAccountantSubjectDetailHistory)paramDetail).setCredit(BigDecimal.valueOf(0));
                    ((TAccountantSubjectDetailHistory)paramDetail).setDebit(BigDecimal.valueOf(loanPrice));
                    ((TAccountantSubjectDetailHistory)paramDetail).setBalanceDirection("2");
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreator(userID);
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreateName(userName);
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreateDate(now);
                    ((TAccountantSubjectDetailHistory)paramDetail).setApproveStatus(1 + "");
                    ((TAccountantSubjectDetailHistory)paramDetail).setCreditQuantity(creditQuantity);
                    ((TAccountantSubjectDetailHistory)paramDetail).setDebitQuantity(debitQuantity);
                    ((TAccountantSubjectDetailHistory)paramDetail).setUnitPrice(unitPrice);
                    ((TAccountantSubjectDetailHistory)paramDetail).setBalance(BigDecimal.valueOf(loanPrice));
                    detailHistoryMapper.insert(((TAccountantSubjectDetailHistory)paramDetail));
                    detailLoanID = ((TAccountantSubjectDetailHistory)paramDetail).getId();
                }

                if (lastSub.startsWith("1001") || lastSub.startsWith("1002"))
                {
                    loanids.add(detailLoanID);
                }
            }
        }

        if (cashjson != null && !"[]".equals(cashjson))
        {
            if (flag == 1)
            {
                /*新凭证生成的时候需要在现金明细表里生成状态为1的明细，即审批状态为待审批*/
                insertCashDetail(vid,cashjson,borids,loanids);
            }
            else
            {
                /*新凭证生成的时候需要在现金明细表里生成状态为1的明细，即审批状态为待审批*/
                insertHistoryCashDetail(vid,cashjson,borids,loanids);
            }
        }

    }


    //新增历史表的科目和详情
    public void newHistorySubjectAndDetail(String settleMonth,String subjectBorrow, String subjectLoan, int org, int vid, int userID, String userName, String summary, String cashjson)
    {
        insertSubjectAndDetail(settleMonth,2,subjectBorrow,subjectLoan,org,vid,userID,userName,summary,cashjson);
    }


    /*
     * 冲红
     * id 凭证id
     * 返回冲洪后的id
     * 冲洪后的凭证和新产生的凭证都是未结帐状态
     * */
    public int redInvoice(int id, int oid, int approve_status, String cashjson)
    {
        SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleFormat.format(new Date());
        //冲洪的凭证日期都是当月的最后一天
        String voucherDate = judgeSettledMonth(oid);

        TAccountantVoucher vParam = new TAccountantVoucher();
        vParam.setId(id);
        TAccountantVoucher voucherOld = voucherMapper.getSingle(vParam);
        voucherOld.setBelongPeroid("1");
        voucherOld.setIsSettled((byte) 0);
        voucherOld.setCreateDate(voucherDate);
        voucherOld.setApproveStatus(approve_status + "");
        //复制上条凭证写回原凭证表
        TAccountantVoucher vouHisParam = new TAccountantVoucher();
        vouHisParam.setOrg(voucherOld.getOrg());
        vouHisParam.setVoucher(voucherOld.getVoucher());
        vouHisParam.setBelongPeroid(voucherOld.getBelongPeroid());
        vouHisParam.setMode(voucherOld.getMode());
        vouHisParam.setType(voucherOld.getType());
        vouHisParam.setCategory(voucherOld.getCategory());
        vouHisParam.setSn(voucherOld.getSn());
        vouHisParam.setSettlement(voucherOld.getSettlement());
        vouHisParam.setIsAccount(voucherOld.getIsAccount());
        vouHisParam.setIsSettled(voucherOld.getIsSettled());
        vouHisParam.setReason(voucherOld.getReason());
        vouHisParam.setBillDetail(voucherOld.getBillDetail());
        vouHisParam.setSummary(voucherOld.getSummary());
        vouHisParam.setOperatorName(voucherOld.getOperatorName());
        vouHisParam.setOperatorFinance(voucherOld.getOperatorFinance());
        vouHisParam.setMemo(voucherOld.getMemo());
        vouHisParam.setCreator(voucherOld.getCreator());
        vouHisParam.setCreateName(voucherOld.getCreateName());
        vouHisParam.setCreateDate(voucherOld.getCreateDate());
        vouHisParam.setApproveStatus(voucherOld.getApproveStatus());
        vouHisParam.setSource(voucherOld.getSource());
        vouHisParam.setPricetype(voucherOld.getPricetype());
        vouHisParam.setPurpose(voucherOld.getPurpose());
        vouHisParam.setBillQuantity(voucherOld.getBillQuantity());
        vouHisParam.setBillPeriod(voucherOld.getBillPeriod());
        vouHisParam.setKind(voucherOld.getKind());
        vouHisParam.setUpdateName(voucherOld.getUpdateName());
        vouHisParam.setUpdator(voucherOld.getUpdator());
        vouHisParam.setUpdateName(voucherOld.getUpdateName());
        vouHisParam.setAddtime(now);
        vouHisParam.setDetailId(voucherOld.getDetailId());
        if (voucherOld.getBillDate() != null) {
            vouHisParam.setBillDate(voucherOld.getBillDate());
        }
        voucherMapper.insert(vouHisParam);
        int res = vouHisParam.getId();

        //把两个凭证的状态都改成2，type=2，表示改凭证是冲洪的凭证
        QueryData qd = new QueryData();
        qd.put("oldid",id);
        qd.put("redid",res);
        voucherMapper.updateVoucherAfterRed(qd);

        //得到老的科目id，用在写新的科目明细张的时候把老明细拿出来

        TAccountantVoucherSubject paramSubject = new TAccountantVoucherSubject();
        paramSubject.setVoucherId(id);
        List<TAccountantVoucherSubject> oldSubjects = voucherSubjectMapper.getSubjectByVoucher(paramSubject);
        TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
        TAccountantSubjectDetail newDetailParam = new TAccountantSubjectDetail();
        TAccountantVoucherSubject subjectParam = new TAccountantVoucherSubject();
        TAccountantVoucherSubject newSubjectParam = new TAccountantVoucherSubject();
        TAccountantSubjectCash newCash = new TAccountantSubjectCash();
        TAccountantSubjectCash cashDetail = new TAccountantSubjectCash();
        List<Integer> ids = new ArrayList<Integer>();
        for (TAccountantVoucherSubject oldSub : oldSubjects)
        {
            BigDecimal amount = oldSub.getAmount();
            newSubjectParam.setOrg(oldSub.getOrg());
            newSubjectParam.setVoucherId(res);
            newSubjectParam.setSubject(oldSub.getSubject());
            newSubjectParam.setSubjectNames(oldSub.getSubjectNames());
            newSubjectParam.setDirection(oldSub.getDirection());
            newSubjectParam.setAmount(amount.negate());
            newSubjectParam.setCreator(oldSub.getCreator());
            newSubjectParam.setCreateName(oldSub.getCreateName());
            newSubjectParam.setApproveStatus(oldSub.getApproveStatus());
            newSubjectParam.setReimburseBillItem(oldSub.getReimburseBillItem());
            newSubjectParam.setAcountDate(voucherDate);
            voucherSubjectMapper.insert(newSubjectParam);
            int resSub = newSubjectParam.getId();
            detailParam.setSubjectId(oldSub.getId());
            TAccountantSubjectDetail detailOld = detailMapper.getSubjectDetail(detailParam);
            //写借方的科目明细张
            newDetailParam.setSubjectId(resSub);
            newDetailParam.setSubject(detailOld.getSubject());
            newDetailParam.setType(detailOld.getType());
            newDetailParam.setSummary(detailOld.getSummary());
            BigDecimal credit = detailOld.getCredit() == null ? BigDecimal.valueOf(0) : detailOld.getCredit().negate();
            BigDecimal debit = detailOld.getDebit() == null ? BigDecimal.valueOf(0) : detailOld.getDebit().negate();

            newDetailParam.setCredit(credit);
            newDetailParam.setDebit(debit);
            if(credit.doubleValue() != 0)
            {
                newDetailParam.setBalanceDirection("1");
                newDetailParam.setBalance(credit);
            }
            else if(debit.doubleValue() != 0)
            {
                newDetailParam.setBalanceDirection("2");
                newDetailParam.setBalance(debit);
            }
            else
            {
                newDetailParam.setBalanceDirection("3");
                newDetailParam.setBalance(BigDecimal.valueOf(0));
            }

            newDetailParam.setCreator(detailOld.getCreator());
            newDetailParam.setCreateName(detailOld.getCreateName());
            newDetailParam.setCreateDate(detailOld.getCreateDate());
            newDetailParam.setApproveStatus(detailOld.getApproveStatus());
            newDetailParam.setCreditQuantity(detailOld.getCreditQuantity());
            newDetailParam.setDebitQuantity(detailOld.getDebitQuantity());
            newDetailParam.setUnitPrice(detailOld.getUnitPrice());
            detailMapper.insert(newDetailParam);
            int detailID = newDetailParam.getId();
            String oldSubject = oldSub.getSubject();
            String lastSub = oldSubject;
            if(oldSubject.contains("-"))
            {
                lastSub = oldSubject.substring(oldSubject.lastIndexOf("-") + 1);
            }
            if(lastSub.startsWith("1001") || lastSub.startsWith("1002"))
            {
                ids.add(detailID);
            }


        }

        if (cashjson != null && !"".equals(cashjson))
        {

            JSONArray array = new JSONArray(cashjson);
            for (int i=0;i<ids.size();i++)
            {
                JSONObject obj = array.getJSONObject(i);
                TAccountantSubjectCash subjectCash = subjectCashMapper.getSingleByDetailID(cashDetail);
                if (subjectCash != null)
                {
                    newCash.setDetailId(ids.get(i));
                    newCash.setCashFlowCode(obj.optString("cashitem"));
                    newCash.setCredit(new BigDecimal(obj.optString("cashes","0")).negate());
                    newCash.setApproveStatus("1");
                    subjectCashMapper.insert(newCash);
                }
            }
        }

        return res;
    }

    /*
     * 产生一个新的凭证,并返回新凭证的id
     * */
    public int newVoucher(SubjectChooseParams params,String type)
    {
        SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleFormat.format(new Date());
        User user = params.getUser();
        if (user == null){
            return 0;
        }
        String summary = params.getSummary();
        int mode = params.getMode();
        double price = params.getPrice();
        int category = params.getCategory();
        String subjectBorrow = params.getSubjectBorrow();
        String subjectLoan = params.getSubjectLoan();
        int userID = user.getUserID();
        String userName = user.getUserName();
        int is_account = params.getIs_account();
        String reason = params.getReason();
        Integer bill_detail = params.getBill_detail();
        String memo = params.getMemo();
        int approve_status = params.getApprove_status();
        if (approve_status == 0) approve_status = 2;//新增凭证时默认是审批通过
        int pricetype = params.getPricetype();
        String  purpose = params.getPurpose();
        int bill_quantity = params.getBill_quantity();
        String kind = params.getKind();
        String operatorName = params.getOperatorName();
        String operatorFinance = params.getOperatorFinance();
        String cashjson = params.getCashjson();
        int source = params.getSource();
        int oid = params.getOid();
        int belong_peroid = params.getBelong_peroid();
        int bill_period = params.getBill_period();
        String voucherDate = params.getVoucherDate();
        Date billDate = params.getBillDate();//票据日期
        Integer detailId = params.getDetailId();//财务明细ID
//        int creditQuantity = params.getCreditQuantity();
//        int debitQuantity = params.getDebitQuantity();
//        BigDecimal unitPrice = params.getUnitPrice();
        TAccountantVoucher voucher = new TAccountantVoucher();
        if(source == 1)
        {
            voucher.setOrg(oid);
            voucher.setBelongPeroid(belong_peroid + "");
            voucher.setMode(mode + "");
            if(type != null)
                voucher.setType(type);
            else
                voucher.setType("1");
            voucher.setCategory(category + "");
            voucher.setSn(1);
            voucher.setSettlement("无");
            voucher.setIsAccount((byte) is_account);
            voucher.setIsSettled((byte) 0);
            voucher.setReason(reason);
            voucher.setBillDetail(bill_detail);
            voucher.setSummary(summary);
            voucher.setOperatorName(userName);
            voucher.setOperatorFinance(userName);
            voucher.setMemo(memo);
            voucher.setCreator(userID);
            voucher.setCreateName(userName);
            voucher.setUpdateName(userName);
            voucher.setAuditorName(userName);//审核人
            voucher.setAuditor(userID);
            voucher.setAuditDate(new Date());
            voucher.setCreateDate(voucherDate);
//            voucher.setApproveStatus(approve_status + "");
            voucher.setApproveStatus(2 + "");//
            voucher.setSource(source + "");
            voucher.setPricetype(pricetype + "");
            voucher.setPurpose(purpose);
            voucher.setBillQuantity(bill_quantity);
            voucher.setBillPeriod(bill_period);
            voucher.setKind(kind);
        }
        else
        {
            voucher.setOrg(oid);
            voucher.setBelongPeroid(belong_peroid + "");
            voucher.setMode(mode + "");
            voucher.setType("1");
            voucher.setCategory(category + "");
            voucher.setSn(1);
            voucher.setSettlement("无");
            voucher.setIsAccount((byte) is_account);
            voucher.setIsSettled((byte) 0);
            voucher.setReason(reason);
            voucher.setBillDetail(bill_detail);
            voucher.setSummary(summary);

            voucher.setOperatorFinance(operatorFinance);
            voucher.setMemo(memo);
            voucher.setCreator(userID);
            voucher.setUpdateName(null);
            voucher.setCreateDate(voucherDate);//凭证日期

            if ("3".equals(source)) {
                voucher.setCreateName("会计高管");//制单人、经手人
                voucher.setAuditorName("会计高管");//审核人
                voucher.setOperatorName("会计高管");
            }
            else {
                voucher.setCreateName(operatorName);
                voucher.setAuditorName(userName);//审核人
                voucher.setOperatorName(userName);
            }
            voucher.setAuditor(userID);
            voucher.setAuditDate(new Date());
            voucher.setApproveStatus(approve_status + "");
            voucher.setSource(source + "");
            voucher.setPricetype(pricetype + "");
            voucher.setPurpose(purpose);
            voucher.setBillQuantity(bill_quantity);
            voucher.setBillPeriod(bill_period);
            voucher.setKind(kind);
            voucher.setDetailId(detailId);
            if (billDate != null)
                voucher.setBillDate(billDate);
        }
        voucher.setAddtime(now);
        int f = voucherMapper.insert(voucher);
        int res = voucher.getId();

        /*voucher 作为相关连凭证链接的字段，比如有冲洪的凭证和修改后的凭证，就用这个字段值链接起来*/
        TAccountantVoucher paramVoucher = new TAccountantVoucher();
        paramVoucher.setVoucher(res);
        paramVoucher.setId(res);
        voucherMapper.update(paramVoucher);

        if (subjectBorrow.length() > 0 && subjectLoan.length() > 0) {
            //借方、贷方科目分别都有多个
            newSubjectAndDetail(voucherDate,subjectBorrow,subjectLoan,oid,res,userID,userName,summary,cashjson);
        }

        return res;
    }

    /*新凭证生成的时候需要在现金明细表里生成状态为1的明细，即审批状态为待审批
     * vid               凭证id
     * subjectBorrow     借方科目
     * subjectLoan       贷方科目
     * cashjson          现金流量表里的项目包含cashitem,cashes
     * detailBorrowID    借方明细ID
     * detailLoanID      贷方明细ID
     * */
    public void insertCashDetail(int vid,String cashjson,List<Integer> borids, List<Integer> loanids)
    {
        if(vid > 0 && cashjson != null)
        {
            insertCash(1,borids,loanids,cashjson);
        }
    }

    public void insertHistoryCashDetail(int vid,String cashjson,List<Integer> borids,
                                        List<Integer> loanids )
    {
        if(vid > 0 && cashjson != null)
        {
            /*把用户输入的金额写到现金详情表中*/
            insertCash(2,borids,loanids,cashjson);
        }
    }

    /*
     * flag:1 表示写到原表 2 表示写到历史表
     * */
    private void insertCash(int flag, List<Integer> borids, List<Integer> loanids, String cashjson)
    {
        if (cashjson == null)
            return;
        if("[]".equals(cashjson))
            return;
        Object cashParam = null;
        if (flag == 1)
        {
            cashParam = new TAccountantSubjectCash();
        }
        else
        {
            cashParam = new TAccountantSubjectCashHistory();
        }

        JSONArray array = new JSONArray(cashjson);

        for (int i=0;i<borids.size();i++)
        {
            JSONObject obj = array.getJSONObject(i);
            insertCashDetail(borids.get(i),flag,cashParam,obj);
        }

        int j = 0;
        if(array.length() > 0)
        {
            for (int i=0;i<borids.size();i++)
            {
                j = i;
                if (j >= 0 && array.length() > 0)
                {
                    if(j > 0)
                        j -= 1;
                    array.remove(j);
                }

            }
        }


        if(array.length() > 0)
        {
            for (int i=0;i<loanids.size();i++)
            {
                JSONObject obj = array.getJSONObject(i);
                insertCashDetail(loanids.get(i),flag,cashParam,obj);
            }
        }

    }

    private void insertCashDetail(int i, int flag, Object cashParam, JSONObject cashjson)
    {
        if (flag == 1)
        {
            ((TAccountantSubjectCash)cashParam).setDetailId(i);
            ((TAccountantSubjectCash)cashParam).setCashFlowCode(cashjson.optString("cashitem"));
            ((TAccountantSubjectCash)cashParam).setCredit(BigDecimal.valueOf(cashjson.optDouble("cashes",0)));
            ((TAccountantSubjectCash)cashParam).setApproveStatus("1");
            subjectCashMapper.insert(((TAccountantSubjectCash)cashParam));
        }
        else
        {
//            ((TAccountantSubjectCashHistory)cashParam).setDetailId(i);
            ((TAccountantSubjectCashHistory)cashParam).setDetailHistoryId(i);
            ((TAccountantSubjectCashHistory)cashParam).setCashFlowCode(cashjson.optString("cashitem"));
            ((TAccountantSubjectCashHistory)cashParam).setCredit(BigDecimal.valueOf(cashjson.optDouble("cashes",0)));
            ((TAccountantSubjectCashHistory)cashParam).setApproveStatus("1");
            subjectCashHistoryMapper.insert(((TAccountantSubjectCashHistory)cashParam));
        }
    }

    /*当凭证修改的时候先删除老的现金明细在判断是否要生成新的*/
    public JSONObject deleteCashDetail(int vid)
    {
        int detailBorrowID = 0;
        int detailLoanID = 0;
        JSONObject result = new JSONObject();
        TAccountantVoucherSubject param = new TAccountantVoucherSubject();
        param.setVoucherId(vid);
        List<TAccountantVoucherSubject> listVoucherSubject = voucherSubjectMapper.getSubjectByVoucher(param);
        if(listVoucherSubject != null)
        {
            for (TAccountantVoucherSubject subject : listVoucherSubject)
            {
                TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
                detailParam.setSubjectId(subject.getId());
                TAccountantSubjectDetail detail = detailMapper.getSubjectDetail(detailParam);
                if("1".equals(subject.getDirection()))
                {
                    detailBorrowID = detail.getId();
                    result.put("borrowid",detailBorrowID);
                }
                else if("2".equals(subject.getDirection()))
                {
                    detailLoanID = detail.getId();
                    result.put("loanid",detailLoanID);
                }
                TAccountantSubjectCash cashDetail = new TAccountantSubjectCash();
                cashDetail.setDetailId(detail.getId());
                subjectCashMapper.deleteByDetailID(cashDetail);
            }
        }
        return result;
    }

    /*得到凭证所对应的科目代码、名称和金额*/
    public JSONObject getSubject(JSONObject item, int org, int id, int voucher_id)
    {
        JSONObject item2 = item;
        JSONArray subBorArray = new JSONArray();
        JSONArray subLoanArray = new JSONArray();
        List<SubjectTemp> listBorrow = null;
        QueryData qd = new QueryData();
        if(voucher_id == 0)
        {
            qd.put("id",id);
            qd.put("direction","1");
            qd.put("org",org);
            listBorrow = voucherSubjectMapper.getSubjectInfo(qd);
        }
        else
        {
            qd.put("id",id);
            qd.put("direction","1");
            qd.put("org",org);
            listBorrow = subjectHistoryMapper.getSubjectHistoryInfo(qd);
        }


        BigDecimal sum = new BigDecimal(0);
        for (SubjectTemp st : listBorrow)
        {
            JSONObject subBors = new JSONObject();
            sum = sum.add(st.getAmount());
            subBors.put("borrowSubject",st.getSubject());
//            subBors.put("borrowName",st.getName());
            subBors.put("borrowAmount",st.getAmount());
            subBors.put("borrowNames",st.getSubjectNames());
            subBors.put("creditQuantity",st.getCreditQuantity());
            subBors.put("debitQuantity",st.getDebitQuantity());
            subBors.put("unitPrice",st.getUnitPrice());
            subBors.put("measureUnit",st.getMeasureUnit());
            subBors.put("quantityAssistingAccounting",st.getQuantityAssistingAccounting());
            int pid = st.getReimburseBillItem() == null ? 0 : st.getReimburseBillItem();
            if (pid > 0){
                subBors.put("productID",pid);
            }

//            Integer productID = st.getReimburseBillItem();
//            if(productID != null)
//            {
//                //把个人报销的详情查出来返回，需要个人报销模块的配合
//            }
            subBorArray.put(subBors);
        }
        item2.put("amount",sum.doubleValue());
        item2.put("borinfo",subBorArray);
        List<SubjectTemp> listLoan = null;
        if(voucher_id == 0)
        {
            qd.put("id",id);
            qd.put("direction","2");
            qd.put("org",org);
            listLoan = voucherSubjectMapper.getSubjectInfo(qd);
        }
        else
        {
            qd.put("id",id);
            qd.put("direction","2");
            qd.put("org",org);
            listLoan = subjectHistoryMapper.getSubjectHistoryInfo(qd);
        }

        for (SubjectTemp st : listLoan)
        {
            JSONObject subLoans = new JSONObject();
            subLoans.put("loanSubject",st.getSubject());
//            subLoans.put("loanName",st.getName());
            subLoans.put("loanAmount",st.getAmount());
            subLoans.put("loanNames",st.getSubjectNames());
            subLoans.put("creditQuantity",st.getCreditQuantity());
            subLoans.put("debitQuantity",st.getDebitQuantity());
            subLoans.put("unitPrice",st.getUnitPrice());
            subLoans.put("measureUnit",st.getMeasureUnit());
            subLoans.put("quantityAssistingAccounting",st.getQuantityAssistingAccounting());
            subLoanArray.put(subLoans);
        }
        item2.put("loaninfo",subLoanArray);
        return item2;
    }

    /*
     * 凭证历史表的数据如果审批通过后将和之前的数据进行交换
     * 1、把历史表的数据写到原凭证表
     * 2、把历史表的数据删除
     * 3、把原凭证的数据写到历史表
     * approve_status
     * voucher_id        凭证历史表的字段，大于0说明存在历史数据，等于原凭证的id
     * updator           审批人的id
     * update_name       审批人姓名
     * id                历史表的凭证id
     * */
    public int swap(int id, int approve_status, int voucher_id, int updator, String update_name, int isChecked)
    {
        SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleFormat.format(new Date());
        //数据交换发生在审批历史数据，且只有凭证状态发生改变，所以先修改历史表的凭证状态
        TAccountantVoucherHistory param = new TAccountantVoucherHistory();
        param.setApproveStatus(approve_status + "");
        param.setUpdateName(update_name);
        param.setUpdator(updator);
        param.setId(id);
        voucherHistoryMapper.update(param);

        //现将历史表的数据都写道原凭证表里，然后把历史表删除，最后把原凭证数据写道历史表中
        TAccountantVoucherHistory paramHistory = new TAccountantVoucherHistory();
        paramHistory.setId(id);
        TAccountantVoucherHistory historyVoucher = voucherHistoryMapper.getSingle(paramHistory);
        historyVoucher.setType("1");
        historyVoucher.setIsSettled((byte) 0);
        historyVoucher.setApproveStatus(approve_status + "");

        TAccountantVoucher paramVoucher = new TAccountantVoucher();
        paramVoucher.setOrg(historyVoucher.getOrg());
        paramVoucher.setVoucher(historyVoucher.getVoucher());
        paramVoucher.setBelongPeroid(historyVoucher.getBelongPeroid());
        paramVoucher.setBookDate(historyVoucher.getBookDate());
        paramVoucher.setMode(historyVoucher.getMode());
        paramVoucher.setType(historyVoucher.getType());
        paramVoucher.setCategory(historyVoucher.getCategory());
        paramVoucher.setSn(historyVoucher.getSn());
        paramVoucher.setSettlement(historyVoucher.getSettlement());
        paramVoucher.setCheckNo(historyVoucher.getCheckNo());
        paramVoucher.setIsSettled(historyVoucher.getIsSettled());
        paramVoucher.setIsAccount(historyVoucher.getIsAccount());
        paramVoucher.setReason(historyVoucher.getReason());
        paramVoucher.setBillDetail(historyVoucher.getBillDetail());
        paramVoucher.setSummary(historyVoucher.getSummary());
        paramVoucher.setOperator(historyVoucher.getOperator());
        paramVoucher.setOperatorName(historyVoucher.getOperatorName());
        paramVoucher.setOperatorFinance(historyVoucher.getOperatorFinance());
        paramVoucher.setMemo(historyVoucher.getMemo());
        paramVoucher.setCreator(historyVoucher.getCreator());
        paramVoucher.setCreateName(historyVoucher.getCreateName());
        paramVoucher.setCreateDate(historyVoucher.getCreateDate());
        paramVoucher.setApproveStatus(historyVoucher.getApproveStatus());
        paramVoucher.setSource(historyVoucher.getSource());
        paramVoucher.setUpdator(historyVoucher.getUpdator());
        paramVoucher.setUpdateName(historyVoucher.getUpdateName());
        paramVoucher.setPricetype(historyVoucher.getPricetype());
        paramVoucher.setBillQuantity(historyVoucher.getBillQuantity());
        paramVoucher.setPurpose(historyVoucher.getPurpose());
        paramVoucher.setBillPeriod(historyVoucher.getBillPeriod());
        paramVoucher.setDetailId(historyVoucher.getDetailId());
        if (historyVoucher.getBillDate() != null)
            paramVoucher.setBillDate(historyVoucher.getBillDate());
        paramVoucher.setKind(historyVoucher.getKind());
        paramVoucher.setAddtime(now);
        paramVoucher.setAuditor(historyVoucher.getAuditor());
        paramVoucher.setAuditorName(historyVoucher.getAuditorName());
        paramVoucher.setAuditDate(historyVoucher.getAuditDate());
        voucherMapper.insert(paramVoucher);

        int voucher = paramVoucher.getId();

        //将历史表的科目和明细张数据写道原科目表中
        TAccountantVoucherSubject paramVoucherSubject = new TAccountantVoucherSubject();
        TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
        TAccountantSubjectCashHistory cashHistoryParam = new TAccountantSubjectCashHistory();
        TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
        TAccountantSubjectDetailHistory paramDetail = new TAccountantSubjectDetailHistory();
        TAccountantVoucherSubjectHistory paramVoucherSubjectHistory = new TAccountantVoucherSubjectHistory();
        paramVoucherSubjectHistory.setVoucherId(id);
        List<TAccountantVoucherSubjectHistory> subjectHistory = subjectHistoryMapper.getSubjectByVoucher(paramVoucherSubjectHistory);
        for (TAccountantVoucherSubjectHistory tvsh : subjectHistory)
        {
            //将历史的凭证科目写回到原凭证科目表
            paramVoucherSubject.setOrg(tvsh.getOrg());
            paramVoucherSubject.setVoucherId(voucher);
            paramVoucherSubject.setAcountDate(tvsh.getAcountDate());
            paramVoucherSubject.setSubject(tvsh.getSubject());
            paramVoucherSubject.setSubjectNames(tvsh.getSubjectNames());
            paramVoucherSubject.setDirection(tvsh.getDirection());
            paramVoucherSubject.setAmount(tvsh.getAmount());
            paramVoucherSubject.setSubjectDetail(tvsh.getSubjectDetail());
            paramVoucherSubject.setCreator(tvsh.getCreator());
            paramVoucherSubject.setCreateName(tvsh.getCreateName());
            paramVoucherSubject.setCreateDate(tvsh.getCreateDate());
            paramVoucherSubject.setApproveStatus(tvsh.getApproveStatus());
            paramVoucherSubject.setReimburseBillItem(tvsh.getReimburseBillItem());
            voucherSubjectMapper.insert(paramVoucherSubject);
            int subID = paramVoucherSubject.getId();

            //将历史表的科目详情写道原表中
            paramDetail.setSubjectId(tvsh.getId());
            TAccountantSubjectDetailHistory detailHistory = detailHistoryMapper.getSubjectDetail(paramDetail);
            detailParam.setSubjectId(subID);
            detailParam.setSubject(detailHistory.getSubject());
            detailParam.setType(detailHistory.getType());
            detailParam.setSummary(detailHistory.getSummary());
            detailParam.setCredit(detailHistory.getCredit());
            detailParam.setDebit(detailHistory.getDebit());
            detailParam.setBalance(detailHistory.getBalance());
            detailParam.setBalanceDirection(detailHistory.getBalanceDirection());
            detailParam.setCreator(detailHistory.getCreator());
            detailParam.setCreateName(detailHistory.getCreateName());
            detailParam.setCreditQuantity(detailHistory.getCreditQuantity());
            detailParam.setDebitQuantity(detailHistory.getDebitQuantity());
            detailParam.setUnitPrice(detailHistory.getUnitPrice());
            detailMapper.insert(detailParam);
            int detail = detailParam.getId();

            //将现金明细历史表的数据写回到原现金明细表
            cashHistoryParam.setDetailHistoryId(detailHistory.getId());
            TAccountantSubjectCashHistory cashHistory = subjectCashHistoryMapper.getSingleByDetailID(cashHistoryParam);
            if(cashHistory != null)
            {
                cashParam.setDetailId(detail);
                cashParam.setCashFlowCode(cashHistory.getCashFlowCode());
                cashParam.setCredit(cashHistory.getCredit());
                cashParam.setApproveStatus("2");
                subjectCashMapper.insert(cashParam);
                int cid = cashParam.getId();
                if (cid > 0)
                {
                    //删除现金历史表
                    subjectCashHistoryMapper.delete(cashHistory);
                }
            }
            if (detail > 0)
            {
                //删除历史详情数据
                detailHistoryMapper.deleteHistoryDetail(detailHistory);
            }
            if (subID > 0)
            {
                //删除历史凭证科目数据
                subjectHistoryMapper.deleteHistorySubject(tvsh);
            }

        }

        //删除凭证历史表的数据
        voucherHistoryMapper.delete(historyVoucher);

        //把老凭证数据写回到历史表中
        TAccountantVoucher paramV = new TAccountantVoucher();
        paramV.setId(voucher_id);
        TAccountantVoucher voucherParam = voucherMapper.getSingle(paramV);

        TAccountantVoucherHistory voucherHistory = new TAccountantVoucherHistory();
        voucherHistory.setOrg(voucherParam.getOrg());
        voucherHistory.setVoucher(voucherParam.getVoucher());
        voucherHistory.setBelongPeroid(voucherParam.getBelongPeroid());
        voucherHistory.setBookDate(voucherParam.getBookDate());
        voucherHistory.setMode(voucherParam.getMode());
        voucherHistory.setType(voucherParam.getType());
        voucherHistory.setCategory(voucherParam.getCategory());
        voucherHistory.setSn(voucherParam.getSn());
        voucherHistory.setSettlement(voucherParam.getSettlement());
        voucherHistory.setCheckNo(voucherParam.getCheckNo());
        voucherHistory.setIsSettled((byte) 0);
        voucherHistory.setIsAccount(voucherParam.getIsAccount());
        voucherHistory.setReason(voucherParam.getReason());
        voucherHistory.setBillDetail(voucherParam.getBillDetail());
        voucherHistory.setSummary(voucherParam.getSummary());
        voucherHistory.setOperator(voucherParam.getOperator());
        voucherHistory.setOperatorName(voucherParam.getOperatorName());
        voucherHistory.setOperatorFinance(voucherParam.getOperatorFinance());
        voucherHistory.setMemo(voucherParam.getMemo());
        voucherHistory.setCreator(voucherParam.getCreator());
        voucherHistory.setCreateName(voucherParam.getCreateName());
        voucherHistory.setCreateDate(voucherParam.getCreateDate());
        voucherHistory.setApproveStatus(approve_status + "");
        voucherHistory.setSource(voucherParam.getSource());
        voucherHistory.setVoucherId(id);
        voucherHistory.setPricetype(voucherParam.getPricetype());
        voucherHistory.setPurpose(voucherParam.getPurpose());
        voucherHistory.setBillQuantity(voucherParam.getBillQuantity());
        voucherHistory.setBillPeriod(voucherParam.getBillPeriod());
        voucherHistory.setDetailId(voucherParam.getDetailId());
        if (voucherParam.getBillDate() != null)
            voucherHistory.setBillDate(voucherParam.getBillDate());
        voucherHistory.setKind(voucherParam.getKind());
        voucherHistory.setAddtime(now);
        voucherHistory.setAuditDate(voucherParam.getAuditDate());
        voucherHistory.setAuditorName(voucherParam.getAuditorName());
        voucherHistory.setAuditor(voucherParam.getAuditor());
        voucherHistoryMapper.insert(voucherHistory);
        int voucherHistoryID = voucherHistory.getId();

        String summary = voucherParam.getSummary();
        String creatorName = voucherParam.getCreateName();
        int creator = voucherParam.getCreator();
        //将原凭证科目表的数据写到历史表中
        TAccountantVoucherSubject voucherSubjectParam = new TAccountantVoucherSubject();
        voucherSubjectParam.setVoucherId(voucher_id);
        TAccountantVoucherSubjectHistory subHistory = new TAccountantVoucherSubjectHistory();
        TAccountantSubjectDetail originalDetailParam = new TAccountantSubjectDetail();
        TAccountantSubjectDetailHistory historyParam = new TAccountantSubjectDetailHistory();
        List<TAccountantVoucherSubject> voucherSubject = voucherSubjectMapper.getSubjectByVoucher(voucherSubjectParam);
        for (TAccountantVoucherSubject tvs : voucherSubject)
        {
            //将原凭证科目数据写到历史凭证科目表
            subHistory.setVoucherId(voucherHistoryID);
            subHistory.setOrg(tvs.getOrg());
            subHistory.setAcountDate(tvs.getAcountDate());
            subHistory.setSubject(tvs.getSubject());
            subHistory.setSubjectNames(tvs.getSubjectNames());
            subHistory.setDirection(tvs.getDirection());
            subHistory.setAmount(tvs.getAmount());
            subHistory.setSubjectDetail(tvs.getSubjectDetail());
            subHistory.setCreator(tvs.getCreator());
            subHistory.setCreateName(tvs.getCreateName());
            subHistory.setCreateDate(tvs.getCreateDate());
            subHistory.setApproveStatus(tvs.getApproveStatus());
            subHistory.setVoucherSubjectId(tvs.getVoucherId());
            subHistory.setReimburseBillItem(tvs.getReimburseBillItem());
            subjectHistoryMapper.insert(subHistory);
            int subBorrowHistory = subHistory.getId();
            //将原明细数据写到历史明细中
            originalDetailParam.setSubjectId(tvs.getId());
            TAccountantSubjectDetail originalDetail = detailMapper.getSubjectDetail(originalDetailParam);
            historyParam.setSubjectId(subBorrowHistory);
            historyParam.setBalanceDirection(originalDetail.getBalanceDirection());
            historyParam.setDetailId(originalDetail.getId());
            historyParam.setCredit(originalDetail.getCredit());
            historyParam.setDebit(originalDetail.getDebit());
            historyParam.setBalance(originalDetail.getBalance());
            historyParam.setType("1");
            historyParam.setSummary(summary);
            historyParam.setCreator(creator);
            historyParam.setCreateName(creatorName);
            historyParam.setCreateDate(now);
            historyParam.setCreditQuantity(originalDetail.getCreditQuantity());
            historyParam.setDebitQuantity(originalDetail.getDebitQuantity());
            historyParam.setUnitPrice(originalDetail.getUnitPrice());
            detailHistoryMapper.insert(historyParam);
            int historyDetail = historyParam.getId();
            //将现金明细表的数据写回到现金明细历史表
            cashParam.setDetailId(originalDetail.getId());
            TAccountantSubjectCash cashOriginal = subjectCashMapper.getSingleByDetailID(cashParam);
            int cid = 0;
            if(cashOriginal != null)
            {
//                cashHistoryParam.setDetailId(historyDetail);
                cashHistoryParam.setDetailHistoryId(historyDetail);
                cashHistoryParam.setCashFlowCode(cashOriginal.getCashFlowCode());
                cashHistoryParam.setCredit(cashOriginal.getCredit());
                cashHistoryParam.setApproveStatus("2");
                subjectCashHistoryMapper.insert(cashHistoryParam);
                cid = cashHistoryParam.getId();
            }
            if(isChecked == 0)
            {
                if (cid > 0)
                {
                    //删除原现金数据
                    subjectCashMapper.delete(cashOriginal);
                }
                if (historyDetail > 0)
                {
                    //删除原科目明细数据
                    detailMapper.delete(originalDetail);
                }
                if (subBorrowHistory > 0)
                {
                    //删除原凭证科目数据
                    voucherSubjectMapper.delete(tvs);
                }
            }
        }

        //如果原凭证已结帐的话则保留原数据，否则删除原数据
        if(isChecked == 0)
        {
            //没有结帐，删除原数据,删除明细的时候要判断在现金流量表中是否存在数据，存在的话要先删除，有外键约束
            voucherMapper.delete(voucherParam);
        }

        return voucher;
    }

    /*
     * 得到会计录入的凭证
     * */
    public JSONArray getAccountantVouchers(List<TAccountantVoucher> list) {
        JSONArray resultArray = new JSONArray();
        JSONObject item = null;
        String vouchers = JSON.toJSONString(list);
        JSONArray arrayVouchers = new JSONArray(vouchers);
        for (int i=0;i<arrayVouchers.length();i++) {
            JSONObject itemVoucher = arrayVouchers.getJSONObject(i);
            int id = itemVoucher.optInt("id");
            int voucher = itemVoucher.optInt("voucher");
            int is_settled = itemVoucher.optInt("isSettled");
            String create_date = itemVoucher.optString("createDate");
            String summary = itemVoucher.optString("summary");
            String type = itemVoucher.optString("type");
            int org = itemVoucher.optInt("org");
            item = new JSONObject();
            item.put("id", id);
            item.put("voucher", voucher);
            item.put("is_settled", is_settled);
            item.put("create_date", create_date);
            item.put("summary", summary);
            item.put("type", type);
            item = getSubject(item, org, id, 0);

            //得到现金流量数据
            TAccountantVoucherSubject param = new TAccountantVoucherSubject();
            param.setVoucherId(id);
            List<TAccountantVoucherSubject> listVoucherSubject = voucherSubjectMapper.getSubjectByVoucher(param);
            JSONArray cashOriginal = new JSONArray();
            if (listVoucherSubject != null) {
                for (TAccountantVoucherSubject sub : listVoucherSubject) {

                    TAccountantSubjectDetail paramDet = new TAccountantSubjectDetail();
                    paramDet.setSubjectId(sub.getId());
                    TAccountantSubjectDetail subDetail = detailMapper.getSubjectDetail(paramDet);
                    TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
                    cashParam.setDetailId(subDetail.getId());
                    TAccountantSubjectCash subjectCash = subjectCashMapper.getSingleByDetailID(cashParam);
                    if (subjectCash != null) {
                        JSONObject obj = new JSONObject();
                        obj.put("cash", subjectCash.getCredit());
                        obj.put("cashitem", subjectCash.getCashFlowCode());
                        obj.put("cashitemName", subjectCash.getCodeName());
                        cashOriginal.put(obj);
                    }
                }
                item.put("cashjson", cashOriginal);


            }
            resultArray.put(i, item);

        }
        return resultArray;
    }

    /*
     * 当调用科目选择或凭证修改的时候
     * 跟据用户选择的凭证日期 1-本月 2-非本月
     * 判断这两个月份是否已经结帐，如果结帐的话则不能再次选择
     * */
    public TAccountantSettle judgeSettled(int belong_period, int oid)
    {
        int year = 0;
        int month = 0;
        Calendar calendar = Calendar.getInstance();
        month = calendar.get(Calendar.MONTH) + 1;
        year = calendar.get(Calendar.YEAR);
        String cur = String.format("%02d",month);
        //当前年月
        String curDate = year + "-" + cur;

        calendar.set(Calendar.MONTH,calendar.get(Calendar.MONTH) - 1);
        month = calendar.get(Calendar.MONTH) + 1;
        year = calendar.get(Calendar.YEAR);
        String pre = String.format("%02d",month);
        //上个月的，年月
        String preDate = year + "-" + pre;
        TAccountantSettle settle = new TAccountantSettle();
        settle.setOrg(oid);
        settle.setPeriod(preDate);
        settle = settleMapper.getSettleDay(settle);
        if(settle == null)
        {
            return null;
        }

        String settleDay = settle.getPeriod();

        if(belong_period == 1 && curDate.equals(settleDay))
//            return false;
            settle.setSettled(false);//本月已结账
        if(belong_period != 1 && preDate.equals(settleDay))
//            return false;
            settle.setSettled(false);//上月已结账
        else
//            return true;
            settle.setSettled(true);//要选择的月份没有结账
        return  settle;
    }


    /*当凭证审批后需要把相关的现金流量数据写进数据库*/
    private void insertCashAfterApproveVoucher(int detailID, int oid, JSONObject cashjson)
    {
        if (cashjson == null)
            return;
        TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
        cashParam.setDetailId(detailID);
        cashParam.setCashFlowCode(cashjson.getString("cashitem"));
        cashParam.setCredit(BigDecimal.valueOf(cashjson.optDouble("cashes",0)));
        cashParam.setApproveStatus("2");
        //先删除老的现金明细，状态为1的
        subjectCashMapper.deleteOldCashDetail(cashParam);
        //在把新的写进去
        subjectCashMapper.insert(cashParam);
        //将该现金数累加到现金流量表的暂存表中
        TAccountantCash cashTempParam = new TAccountantCash();
        cashTempParam.setCashFlowCode(cashjson.getString("cashitem"));
        cashTempParam.setOrg(oid);
        TAccountantCash cashTemp = cashMapper.getSingleByCashFlowCode(cashTempParam);
        if(cashTemp != null)
        {
            BigDecimal cashCachePeriod = cashTemp.getAmount() == null ? BigDecimal.valueOf(0) : cashTemp.getAmount();
            BigDecimal cashCacheAccumulative = cashTemp.getAccumulative() == null ? BigDecimal.valueOf(0) : cashTemp.getAccumulative();
            cashTemp.setOrg(oid);
            cashTemp.setAmount(cashCachePeriod.add(BigDecimal.valueOf(cashjson.optDouble("cashes",0))));
            cashTemp.setAccumulative(cashCacheAccumulative.add(BigDecimal.valueOf(cashjson.optDouble("cashes",0))));
            cashMapper.update(cashTemp);
        }
    }

    /*
     * needTrial 是否需要试算，0-不许要，1-需要
     *
     * */
    public int updateNeedTrialField(int org,String needTrial)
    {
        SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM");
        Calendar c = Calendar.getInstance();
        c.set(Calendar.MONTH,c.get(Calendar.MONTH) - 1);
        String premonth = simpleFormat.format(c.getTime());
        TAccountantSettle settle = new TAccountantSettle();
        settle.setOrg(org);
        settle.setNeedTrial(needTrial);
        settle.setPeriod(premonth);
        int res = settleMapper.updateNeedTrial(settle);
        return res;
    }

    /*检查借方金额是否等于贷方金额同时等于票据总金额（如果存在票据金额的话）
     * 会计录入和结转损益的时候没有票据金额所以不用判断
     * */
    public boolean checkPrice(String subjectBor,String subjectLoan,double totalPrice)
    {
        if ("".equals(subjectBor) && "".equals(subjectLoan)) {
            return true;//如果没有借贷方科目信息说明科目选择时选了不予下账，不用验证价格
        }
        JSONArray borrowArray = new JSONArray(subjectBor);
        double sumBor = 0;
        if(borrowArray != null) {
            for (int i = 0; i < borrowArray.length(); i++) {
                JSONObject bor = borrowArray.getJSONObject(i);
                double subPrice = bor.optDouble("price",0);
                sumBor += subPrice;
            }
        }

        JSONArray loanArray = new JSONArray(subjectLoan);
        double sumLoan = 0;
        if(borrowArray != null) {
            for (int i = 0; i < loanArray.length(); i++) {
                JSONObject loan = loanArray.getJSONObject(i);
                double loanPrice = loan.optDouble("price",0);
                sumLoan += loanPrice;
            }
        }

        if (totalPrice > 0)
        {
            boolean res = sumBor == sumLoan && sumBor == totalPrice;
            return res;
        }
        else
        {
            boolean res = sumBor == sumLoan;
            return res;
        }

    }

    /**/
    public DirAndBalance getBalanceAndDirection(String preDir, BigDecimal preBalance, String curDir, BigDecimal curBalance)
    {
        DirAndBalance res = new DirAndBalance();
        if(preDir != null)
        {
            if(curDir.equals(preDir))
            {
                res.setDir(preDir);
                res.setBalance(curBalance.add(preBalance));
            }
            else
            {

                if(preBalance.compareTo(curBalance) > 0)
                {
                    res.setDir(preDir);
                    res.setBalance(preBalance.subtract(curBalance));
                }
                if(preBalance.compareTo(curBalance) == 0)
                {
                    res.setDir("3");
                    res.setBalance(BigDecimal.valueOf(0));
                }
                if(preBalance.compareTo(curBalance) < 0)
                {
                    res.setDir(curDir);
                    res.setBalance(curBalance.subtract(preBalance));
                }
            }
        }
        else
        {
            res.setDir(curDir);
            res.setBalance(curBalance);
        }
        return res;
    }


    public AccumulationEntity accumulationThisPeroid(AccumulationEntity parentPeroid, AccumulationEntity childPeroid) {

        //把子科目的余额加到父科目上
//        BigDecimal CpreviousBalance = childPeroid.getPreBalance() == null ? BigDecimal.valueOf(0) : childPeroid.getPreBalance(); //子科目期初余额
        BigDecimal Cbalance = childPeroid.getBalance() == null ? BigDecimal.valueOf(0) : childPeroid.getBalance();                 //子科目期末余额
        BigDecimal Ccredit = childPeroid.getCredit() == null ? BigDecimal.valueOf(0) : childPeroid.getCredit();                   //子科目的本期借方发生额
        BigDecimal Cdebit = childPeroid.getDebit() == null ? BigDecimal.valueOf(0) : childPeroid.getDebit();                     //子科目的本期贷方发生额

//        String childDirPerent = childPeroid.getPreBalanceDirection() == null ? "3" : childPeroid.getPreBalanceDirection();
        String childBalanceDir = childPeroid.getBalanceDirection() == null ? "3" : childPeroid.getBalanceDirection();

//        BigDecimal PpreviousBalance = parentPeroid.getPreBalance() == null ? BigDecimal.valueOf(0) : parentPeroid.getPreBalance(); //父科目期初余额
        BigDecimal Pbalance = parentPeroid.getBalance() == null ? BigDecimal.valueOf(0) : parentPeroid.getBalance();                 //父科目期末余额
        BigDecimal Pcredit = parentPeroid.getCredit() == null ? BigDecimal.valueOf(0) : parentPeroid.getCredit();                   //父科目的本期借方发生额
        BigDecimal Pdebit = parentPeroid.getDebit() == null ? BigDecimal.valueOf(0) : parentPeroid.getDebit();                     //父科目的本期贷方发生额


//        String preDirPerent = parentPeroid.getPreBalanceDirection() == null ?  "3" : parentPeroid.getPreBalanceDirection();
        String preBalanceDir = parentPeroid.getBalanceDirection() == null ?  "3" : parentPeroid.getBalanceDirection();

//        DirAndBalance dab = getBalanceAndDirection(preDirPerent,PpreviousBalance,childDirPerent,CpreviousBalance);
//        parentPeroid.setPreBalance(dab.getBalance());
//        parentPeroid.setPreBalanceDirection(dab.getDir());
        DirAndBalance dab = getBalanceAndDirection(preBalanceDir,Pbalance,childBalanceDir,Cbalance);
        parentPeroid.setBalance(dab.getBalance());
        parentPeroid.setBalanceDirection(dab.getDir());

        parentPeroid.setCredit(Pcredit.add(Ccredit));
        parentPeroid.setDebit(Pdebit.add(Cdebit));

        return parentPeroid;
    }

    public String getPreMonthLastDay(Integer org)
    {

        String period = accountant1_209_service.getSettleMonth(org);//需结账的月份
        period = period.replace("年","-").replace("月","");
        Date date = NewDateUtils.dateFromString(period + "-01","yyyy-MM-dd");
        date = NewDateUtils.getLastTimeOfMonth(date);
        String lastDay = NewDateUtils.dateToString(date,"yyyy-MM-dd");
        return lastDay;
    }

    /*根据*/
    public List<TAccountantSubjectDetail> getDetailsByVouchers(int oid, String voucherDate) {
        List<TAccountantSubjectDetail> listDetails = new ArrayList<TAccountantSubjectDetail>();
        TAccountantVoucher voucherParam = new TAccountantVoucher();
        voucherParam.setOrg(oid);
        voucherParam.setCreateDate(voucherDate);
        //得到所有已经结帐的凭证
        List<TAccountantVoucher> listVouchers = voucherMapper.getApproveVouchers(voucherParam);
        if (listVouchers != null) {
            for (TAccountantVoucher voucher1 : listVouchers) {
                TAccountantVoucherSubject subjectParam = new TAccountantVoucherSubject();
                subjectParam.setVoucherId(voucher1.getId());

                List<TAccountantVoucherSubject> listVoucherSubject = voucherSubjectMapper.getSubjectByVoucher(subjectParam);
                if (listVoucherSubject != null) {
                    for (TAccountantVoucherSubject sub : listVoucherSubject) {
                        TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
                        detailParam.setSubjectId(sub.getId());
                        TAccountantSubjectDetail detail = detailMapper.getSubjectDetail(detailParam);
                        if (detail != null) {
                            detail.setVoucherid(voucher1.getId());
                            listDetails.add(detail);
                        }
                    }
                }
            }
        }
        return listDetails;
    }

    /*
     * 兼容老机构的建账数据
     * 老机构的setting表里没有established记录，所以会缺少建账记录、科目变动记录等数据
     * 而且科目变动的时候也会报错
     * 老机构在操作科目、查看建账记录、查看科目变动记录时，检测setting表是否有establish字段
     * 没有的话，需要做以下几件事
     * 1、新增建账记录
     * 2、在setting表新增establish建账值
     * 3、写科目变动记录
     * */
    public Integer establishCompatible(Integer org,Integer userId,String userName) {
        Date date = new Date();
        TaccountantEstablishEntity establishEntity = new TaccountantEstablishEntity();
        establishEntity.setOrg(org);
        establishEntity.setCreator(userId);
        establishEntity.setCreateName(userName);
        establishEntity.setCreateDate(date);
        establishEntity.setType("1");
        establishEntityMapper.insert(establishEntity);//写建账记录
        int eid = establishEntity.getId();

        TAccountantSetting checkEstablishParam = new TAccountantSetting();
        checkEstablishParam.setOrg(org);
        checkEstablishParam.setKey_("establish_id");
        TAccountantSetting res = buildAccountMapper.checkSettingKey(checkEstablishParam);
        if (res == null) {
            TAccountantSetting link = new TAccountantSetting();
            link.setOrg(org);
            link.setKey_("establish_id");
            link.setValue_(eid + "");
            buildAccountMapper.insertSettingKey(link);//将establish_id写进setting表
        }
        else {
            Map<String,Object> initMonth = new HashMap<String,Object>();
            //更新建账ID
            initMonth.put("key_","establish_id");
            initMonth.put("value_",eid);
            buildAccountMapper.updateSettingByOrg(initMonth);
        }

//        TAccountantSetting link = new TAccountantSetting();
//        link.setOrg(org);
//        link.setKey_("establish_id");
//        link.setValue_(eid + "");
//        buildAccountMapper.insertSettingKey(link);//将establish_id写进setting表
        //新增建账记录和科目变动记录
        generalSubjectRecord.insertJournal(org,eid,userId,userName,date,false);//新增journal表和change表
        //建账之后需要新增操作记录，写进record表
        TaccountantSubjectRecord recordDtoParam = new TaccountantSubjectRecord();
        recordDtoParam.setOrg(org);
        recordDtoParam.setState(1);//1-修改后，0-修改前
        recordDtoParam.setEstablish(eid);
        recordDtoParam.setOperation("6");//操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
        recordDtoParam.setCreateDate(date);
        recordDtoParam.setCreator(userId);
        recordDtoParam.setCreateName(userName);
        recordDtoParam.setSubject("");
        recordDtoParam.setLevel(0);
//        recordMapper.copySubjectToRecordTable(recordDtoParam);
        recordMapper.insert(recordDtoParam);
        return eid;
    }

}
