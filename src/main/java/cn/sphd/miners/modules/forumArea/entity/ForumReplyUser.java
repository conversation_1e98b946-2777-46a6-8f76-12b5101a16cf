package cn.sphd.miners.modules.forumArea.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-02-1
 * @description
 **/
@Entity(name="ForumRepyUser")
@Table(name = "t_forum_reply_user")
public class ForumReplyUser implements Serializable {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "reply")
  private Integer reply;
  @Column(name = "post")
  private Integer post;
  @Column(name = "userID")
  private Integer userID;
  @Column(name = "total_num")
  private Integer totalNum;
  @Column(name = "has_read")
  private Integer hasRead;
  @Column(name = "read_num")
  private Integer readNum;
  @Column(name = "at_mum")
  private Integer atMum;
  @Column(name = "memo")
  private String memo;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_date")
  private Date create_date;
  @Column(name = "updator")
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_date")
  private Date updatedate;
  @Column(name = "approve_item")
  private Integer approveItem;
  @Column(name = "approve_status")
  private String approveStatus;
  @Column(name = "approve_level")
  private Integer approveLevel;
  @Column(name = "auditor")
  private Integer auditor;
  @Column(name = "auditor_name")
  private String auditorName;
  @Column(name = "audit_date")
  private Date auditDate;
  @Column(name = "operation")
  private String operation;
  @Column(name = "apply_memo")
  private String applyMemo;
  @Column(name = "approve_memo")
  private String approveMemo;
  @Column(name = "message_id")
  private Integer messageId;
  @Column(name = "org")
  private Integer org;

  /*@ManyToOne(fetch= FetchType.LAZY )
  @JoinColumn(name="reply", referencedColumnName = "id" , nullable=false , unique=false , insertable=false, updatable=false)
  private ForumReply forumReply;*/

  @Transient
  private Integer messageNum;

  @Transient
  private Integer atTag;//@标记

  @Transient
  private Integer replyTag;//回复标记





  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getReply() {
    return reply;
  }

  public void setReply(Integer reply) {
    this.reply = reply;
  }

  public Integer getPost() {
    return post;
  }

  public void setPost(Integer post) {
    this.post = post;
  }

  public Integer getUserID() {
    return userID;
  }

  public void setUserID(Integer userID) {
    this.userID = userID;
  }

  public Integer getTotalNum() {
    return totalNum;
  }

  public void setTotalNum(Integer totalNum) {
    this.totalNum = totalNum;
  }

  public Integer getHasRead() {
    return hasRead;
  }

  public void setHasRead(Integer hasRead) {
    this.hasRead = hasRead;
  }

  public Integer getReadNum() {
    return readNum;
  }

  public void setReadNum(Integer readNum) {
    this.readNum = readNum;
  }

  public Integer getAtMum() {
    return atMum;
  }

  public void setAtMum(Integer atMum) {
    this.atMum = atMum;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreate_date() {
    return create_date;
  }

  public void setCreate_date(Date create_date) {
    this.create_date = create_date;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdatedate() {
    return updatedate;
  }

  public void setUpdatedate(Date updatedate) {
    this.updatedate = updatedate;
  }

  public Integer getApproveItem() {
    return approveItem;
  }

  public void setApproveItem(Integer approveItem) {
    this.approveItem = approveItem;
  }

  public String getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(String approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApproveLevel() {
    return approveLevel;
  }

  public void setApproveLevel(Integer approveLevel) {
    this.approveLevel = approveLevel;
  }

  public Integer getAuditor() {
    return auditor;
  }

  public void setAuditor(Integer auditor) {
    this.auditor = auditor;
  }

  public String getAuditorName() {
    return auditorName;
  }

  public void setAuditorName(String auditorName) {
    this.auditorName = auditorName;
  }

  public Date getAuditDate() {
    return auditDate;
  }

  public void setAuditDate(Date auditDate) {
    this.auditDate = auditDate;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public Integer getMessageNum() {
    return messageNum;
  }

  public void setMessageNum(Integer messageNum) {
    this.messageNum = messageNum;
  }

  public Integer getAtTag() {
    return atTag;
  }

  public void setAtTag(Integer atTag) {
    this.atTag = atTag;
  }

  public Integer getReplyTag() {
    return replyTag;
  }

  public void setReplyTag(Integer replyTag) {
    this.replyTag = replyTag;
  }
}
