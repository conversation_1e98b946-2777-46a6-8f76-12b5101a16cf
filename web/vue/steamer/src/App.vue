<template>
  <div id="nav">
    <router-view/>
  </div>

</template>

<style lang="scss">
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
</style>
<script setup>
import auth from '@/sys/auth'
import initStorage from '@/sys/initStorage'
import sphdSocket from '@/sys/sphd'
auth.init({isGuest: true})
initStorage({})
sphdSocket.start()
</script>