package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantCash;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface TAccountantCashMapper extends BaseMapper<TAccountantCash> {
    List<TAccountantCash> getCashItems();

    TAccountantCash getSingleByCashFlowCode(TAccountantCash cashTempParam);

    List<TAccountantCash> getCashTempList(TAccountantCash cashTempParam);

    void plus27to26(TAccountantCash plus27Param);

    void plus33to32(TAccountantCash plus27Param);

    void statistic5(TAccountantCash plus27Param);

    void statistic10(TAccountantCash plus27Param);

    void updateBycode(TAccountantCash plus27Param);

    void statistic18(TAccountantCash plus27Param);

    void statistic23(TAccountantCash plus27Param);

    void statistic30(TAccountantCash plus27Param);

    void statistic35(TAccountantCash plus27Param);

    void statistic38(TAccountantCash plus27Param);

    void statistic40(TAccountantCash plus27Param);

    void cleanData(TAccountantCash cashP);

    void updateCash(TAccountantCash cash);

    void updateAccumulative(QueryData qd);

    void statistic39(TAccountantCash plus39Param);

    Integer addInitializationCash(Integer org);

    void updateBuildAccumulative(QueryData qd);
}