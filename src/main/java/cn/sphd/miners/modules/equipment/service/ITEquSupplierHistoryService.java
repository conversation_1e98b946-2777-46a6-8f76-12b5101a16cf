package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquSupplierHistory;

import java.util.List;

/**
 * 装备管理_供应商名录历史
 * 20230209 1.247装备器具新增Service接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public interface ITEquSupplierHistoryService {
    /**
     * 查询装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_供应商名录历史
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     */
    public TEquSupplierHistory selectTEquSupplierHistoryById(Long id);

    /**
     * 查询装备管理_供应商名录历史
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 装备管理_供应商名录历史
     * 20230209 1.247装备器具新增集合
     */
    public List<TEquSupplierHistory> selectTEquSupplierHistoryList(TEquSupplierHistory tEquSupplierHistory);

    /**
     * 新增装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquSupplierHistory(TEquSupplierHistory tEquSupplierHistory);

    /**
     * 修改装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquSupplierHistory(TEquSupplierHistory tEquSupplierHistory);

    /**
     * 批量删除装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_供应商名录历史
     *            20230209 1.247装备器具新增主键集合
     * @return 结果
     */
    public int deleteTEquSupplierHistoryByIds(String... ids);

    /**
     * 删除装备管理_供应商名录历史
     * 20230209 1.247装备器具新增信息
     *
     * @param id 装备管理_供应商名录历史
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    public int deleteTEquSupplierHistoryById(Long id);
}
