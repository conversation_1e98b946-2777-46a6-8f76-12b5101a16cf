package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.entity.PdCommodityImportAttach;
import cn.sphd.miners.modules.commodity.service.CommodityImportService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class CommodityImportUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;

    @Override
    public boolean checkUsing(String filename) {
        if (StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            CommodityImportService service = ac.getBean(CommodityImportService.class, "commodityImportService");
            switch (entityClass) {
                case "TrainingQuestionBankAttachment":
                    PdCommodityImportAttach commodityImportAttach;
                    if ((commodityImportAttach = service.getPdCommodityImportAttach(id)) != null
                            && service.getPdCommodityImport(commodityImportAttach.getImportCommodity()) != null
                    ) {
                        return filename.equals(commodityImportAttach.getUplaodPath());
                    }
                    break;
            }
        }
        return false;
    }

    @Override
    public String getKey() {
        return id + entityClass;
    }
    public CommodityImportUsing(Integer id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.') + 1);
    }

    public CommodityImportUsing() {
    }

    public String getEntityClass () {
        return entityClass;
    }

    public void setEntityClass (String entityClass){
        this.entityClass = entityClass;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
