<template>
  <div class="operatInstruction">
    <div>
      <el-button @click="backMain">&lt; 返回</el-button>
    </div>
    <div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="grayCon">
            <div class="ttl">
              字幕精灵操作说明
            </div>
            直播时，如需向直播间加载歌词，且同步播放伴奏/原唱，需使用字幕精灵。字幕精灵有小程序端、电脑直播端与网页操控端。这是网页操控端
            <br/>
            <br/>
            <el-button type="text" class="fright" @click="Instruction(1)">OBS直播简介</el-button>

            1 前提条件
            <br/>
            1.1 字幕精灵的电脑直播端为OBS软件。主播需能熟练用OBS电脑直播
            <br/>
            1.2 歌词、伴奏/原唱需通过字幕精灵的小程序端上传至系统。主播既可发动团队完成，也可自行完成
            <br/>
            <el-button type="text" class="fright" @click="Instruction(2)">字幕配置指南</el-button>
            1.3 OBS需完成字幕配置
            <br/>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grayCon grayCon2">
            2 直播时的操作
            <br/>
            ——使用完成字幕配置的OBS直播
            <br/>
            ——登录本网页操控端

            <br/>
            ——在本网页操控端找到要演唱曲目，点击“播放伴奏”/“播放原唱”
            <br/>
          </div>

        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
    }
  },
  components: {
  },
  mounted() {

  },
  methods: {
    Instruction(type) {
      this.$router.push(`instruction${type}`)
    },
    backMain(type) {
      this.$router.push('mainPage')
    },

  }
}
</script>

<style lang="scss" scoped>
.operatInstruction{
  padding: 40px;
  .fright{ float: right }
  .ttl{ text-align: center; font-weight: bold; margin-bottom: 20px; }
  .grayCon{ padding: 20px; margin: 40px; background: #eee; border-radius: 4px; line-height: 30px;  }
  .grayCon2{ padding-top: 150px; padding-bottom:90px  }
}
</style>
