package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingExam;

import java.util.Date;

/**
 * @ClassName ReqTrainingExam
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/2 8:21
 * @Version 1.0
 */
public class ReqTrainingExam {
    private String userList;//考核人员列表
    private String examBank;//考核题库列表
    private String questionScore;//试题类型分值列表
    private TrainingExam trainingExam;
    private String goal;//考核目标
    private String publicity;// 公示
    private Integer publicityRanking;// 公示名次
    private String endTime; //截至时间
    private String answerDuration;//考试分钟数
    private String passingScore;//及格分数

    public Integer getPublicityRanking() {
        return publicityRanking;
    }

    public void setPublicityRanking(Integer publicityRanking) {
        this.publicityRanking = publicityRanking;
    }

    public String getGoal() {
        return goal;
    }

    public void setGoal(String goal) {
        this.goal = goal;
    }

    public String getPublicity() {
        return publicity;
    }

    public void setPublicity(String publicity) {
        this.publicity = publicity;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getAnswerDuration() {
        return answerDuration;
    }

    public void setAnswerDuration(String answerDuration) {
        this.answerDuration = answerDuration;
    }

    public String getPassingScore() {
        return passingScore;
    }

    public void setPassingScore(String passingScore) {
        this.passingScore = passingScore;
    }

    public String getUserList() {
        return userList;
    }

    public void setUserList(String userList) {
        this.userList = userList;
    }

    public String getExamBank() {
        return examBank;
    }

    public void setExamBank(String examBank) {
        this.examBank = examBank;
    }

    public String getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(String questionScore) {
        this.questionScore = questionScore;
    }

    public TrainingExam getTrainingExam() {
        return trainingExam;
    }

    public void setTrainingExam(TrainingExam trainingExam) {
        this.trainingExam = trainingExam;
    }
}
