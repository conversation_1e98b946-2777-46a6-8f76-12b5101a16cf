<template>
	<div class="register4">
    <div class="live-panel center">
      <div class="p1" v-if="shareMesage.type == 2"> 主播<span class="activeColor">{{ shareMesage.shareName}}</span>邀请你加入ta的工作室！ </div>
      <div>
        <el-checkbox v-model="checked1" label="加入ta的工作室"></el-checkbox>
      </div>
      <div class="live-container">
        <!--				<navigator class="cancel" open-type="exit" target="miniProgram">退出</navigator>-->
        <span @click="goNext()" class="primary live-button">下一步</span>
        <span @click="out" class=" cancel live-button">退  出</span>
      </div>
    </div>
	</div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { joinTeam, organizationList } from '@/api/api.js'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'
import auth from '@/sys/auth'

const router = useRouter();

  const checked1 = ref(false)
  const jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  console.log('jsonData ----', jsonData)
  const shareMesage = jsonData.share
  const loginRes = jsonData.loginRes
  const userInfoSys = auth.getUser()

  onMounted(() => {


  })

  const out = () => {
    ElMessage.warning('直接点击右上角"关闭"按钮关闭小程序')
  }
  const chargeSuper = () => {
    let shareOid = shareMesage.oid
   let havOffice = false
    organizationList().then(res => {
      console.log('机构列表返回值', res)
      res.organizationList.forEach( off => {
        if(off.id == shareOid){
          havOffice = true
        }
      })

      if(havOffice){ // 已经加入过该机构
        router2.push({ name: 'officeList' });
      }else{
        joinTeam({ 'submitState': 1, 'oid':shareMesage.oid }).then(res => {
          console.log('joinTeam res =', res)
          if(res){
            router.push('officeList')
          }else{
            router.push('mine')
          }
        }).catch(err => {
          console.log('joinTeam err=', joinTeam)
        })
      }

    }).catch( err => {
      ElMessage.error('获取用户信息失败，请稍后重试！');
    })
  }
  const goNext = () => {
    if(checked1.value){
      if(loginRes.success > 0){
        // 注册过了,有账号了， 判断下是不是加入过该机构， 没加入的 调加入接口， 加入的直接跳转 机构列表
        chargeSuper()

      }else{ // 还没注册过， 先注册
        router.push('register5')

      }

    }else{
      ElMessage.warning('请勾选加入ta的工作室');
    }
  }
</script>
<style lang="scss" scoped>
.register4{
  .center{ text-align: center; }
  .p1{ margin:50px 0 30px 0;  }
  .activeColor{
    color: $livePageHeadBg;
    font-weight: bold;
  }
}
</style>



