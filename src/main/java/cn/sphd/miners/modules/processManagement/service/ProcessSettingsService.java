package cn.sphd.miners.modules.processManagement.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.processManagement.dto.SonProcessesDto;
import cn.sphd.miners.modules.processManagement.entity.*;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2023/5/10
 * 1.251工序1
 */
public interface ProcessSettingsService {

    //活动类型:2-子工序,3-操作,4-套餐
    enum ActivityType {
        process("process", (byte)1),
        sonProcess("sonProcess", (byte)2),
        operate("operate", (byte)3),
        thail("thail", (byte)4);
        private String name;
        private Byte index;
        ActivityType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }
    //活动的时间点:1-准备工作,2-正式操作,3-收尾工作
    enum ActivityTiming {
        preparation("preparation", (byte)1),
        formal("formal", (byte)2),
        closing("closing", (byte)3);
        private String name;
        private Byte index;
        ActivityTiming(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }
    //类别的类型:1-工序,2-子工序,3-操作,4-套餐
    enum ActivityCategoryType {
        process("process", (byte)1),
        sonProcess("sonProcess", (byte)2),
        operate("operate", (byte)3),
        thail("thail", (byte)4);
        private String name;
        private Byte index;
        ActivityCategoryType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }
    //生产_工序统计(PpmProcessStat)的操作:1-制造/装配,2-包装
    enum ProcessStatAssembly {
        manufacturing("manufacturing", (byte)1),
        pack("pack", (byte)2);
        private String name;
        private Byte index;
        ProcessStatAssembly(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //生产_工序统计的类型:0-全部,1-产品,2-零组件
    enum ProcessStatType {
        all("all", (byte)0),
        product("product", (byte)1),
        spareParts("spareParts", (byte)2);
        private String name;
        private Byte index;
        ProcessStatType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //时间单位:1-秒,2-分钟,3-小时,4-天
    enum Timeunit {
        second("seconds", (byte)1),
        point("points", (byte)2),
        hour("operate", (byte)3),
        day("thail", (byte)4);
        private String name;
        private Byte index;
        Timeunit(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    Integer getProcessNum(Integer org,Boolean enabled);

    Integer getActivityNum(Integer org,Boolean enabled,Byte type);

    List<PpmProcess> getPpmProcessByRepeatName(Integer org, String code, String name);  //工序查询是否有相同的代号或名称

    Map<String,Object> createProcess(User user,PpmProcess ppmProcess, PpmActivityCyclicity ppmActivityCyclicity);

    Map<String,Object> createCategory(User user,String name,Integer type);

    PpmActivityCategory getPpmActivityCategory(Integer org, String name,Byte type);  //查询是否有相同名称的类别

    List<PpmActivityCategory> getCategoryList(Integer org,Byte type,Integer enabled);

    Map<String,Object> updateCategoryEnabled(User user,Integer categoryId,Integer enabled);

    List<PpmProcess> getProcessList(Integer org,Boolean enabled,Integer category,List<Long> processIds);

    List<PpmActivity> getActivityList(Integer org, Boolean enabled, Integer category, Byte type,Byte timing,List<Long> activityIds);

    List<Map<String,Object>> getActivityByCategoryList(Integer org,Boolean enabled,Integer category,Byte type);

    List<PpmActivity> getPpmActivityByRepeatName(Integer org,String code, String name,Byte type);  //子工序/操作查询是否有相同的代号或名称

    Map<String,Object> createActivity(User user, PpmActivity ppmActivity , PpmActivityCyclicity ppmActivityCyclicity);

    Map<String,Object> getSettingsList(User user,Integer product);

    List<Long> getProcessIds(Integer org,Integer product);

    List<PpmProcess> getProcessByProductList(Integer org, Integer product); //获取某产品下的工序

    Map<String,Object> chooseProcess(User user,Integer product,String processSJson);

    //获取应设置的列表-孙文 type 1产品 2零组件
    List<Map<String, Object>> getPdBaseList(User user,String type);

    //获取产品中有工序统计的列表(以上面的getPdBaseList方法为基础，getPdBaseList方法改，此方法跟着改) type 1产品 2零组件
    List<Map<String, Object>> getProductionStatList(User user,String type, PageInfo pageInfo);

    //获取工序查看列表-孙文
    List<Map<String, Object>> getPdBaseProcessList(User user);

    List<PpmProcessStat> getPpmProcessStatList(Integer org,Byte type,Byte assembly);

    PpmProcessStat getPpmProcessStat(Integer org,Byte type,Byte assembly);

    List<PpmProcessStat> getProcessSettingsList(User user);

    Map<String,Object> getToManageList(User user,Byte type, PageInfo pageInfo);

    Integer havingPpmProductionStatList(User user, Byte type,Integer product); //判断是否有工序(生产)的统计,没有就添加

    PpmProductionStat getPpmProductionStat(Integer org,Integer product,Byte type);

    List<PpmActivity> getActivityByProduct(Integer org,Integer product,Long processId,Long parent);

    List<SonProcessesDto> getActivityByProductAndProcessId(Integer org, Integer product, Long processId, Long parent);//根据产品id、工序id，查找子工序或操作

    List<PdProductProcess> getPdProductProcessList(Integer product);

    PdProductProcess getPdProductProcess(Integer product,Long process);

    Map<String,Object> updateProcessOrders(User user,Integer product,Long processId,Long processIdAfter,Integer orderStatus);

    List<PpmFlow> getPpmFlowList(Integer product,Long process,Long parent);

    List<PpmFlow> getPpmFlowAll(Integer product,Long process,Long parent);  //查询产品下的某工序中的所有子工序和操作，以及子工序中的操作

    PpmFlow getPpmFlow(Integer product,Long process,Long parent,Long activity);

    Map<String,Object> updateSonProcessOrders(User user,Integer product,Long processId,Long parent,Long sonProcessId,Long sonProcessIdAfter,Integer orderStatus);

    Map<String,Object> deleteProcess(Integer product,Long processId);

    Integer getPpmFlowNumAll(Integer product,Long process,Byte type); //查询产品下的某工序中的子工序或操作个数

    Map<String,Object> deleteSonProcess(Integer product,Long processId,Long parent, Long activityId);

    List<PpmProcessStat> updateProcessSettingsSonProcess(User user);

    //获取工序实体
    PpmProcess getPpmProcessSingle(Long id);

    //获取子工序或操作实体
    PpmActivity getPpmActivitySingle(Long id);

}
