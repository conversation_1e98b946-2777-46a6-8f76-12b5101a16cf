package cn.sphd.miners.modules.authAccount.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.ec.service.EcService;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_auth_acc_valid_period")
public class AuthAccValidPeriod extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT '主键id',
    @Column(name = "order_detail_id")
    private Long orderDetailId;//`order_detail_id` BIGINT DEFAULT NULL COMMENT '订单明细主键',//退货需要
    @Column(name = "acc_id")
    private Long accId;//`acc_id` BIGINT DEFAULT NULL COMMENT '账号id',
    @Column(name = "user_id")
    private Integer userId;//`user_id` INTEGER DEFAULT NULL COMMENT '用户id',
    @Column(name = "member_id")
    private Long memberId;//`member_id` BIGINT DEFAULT NULL COMMENT '第三方平台用户id',
    @Column(name = "acc_mdse_id")
    private Long accMdseId;//商品id
    @Column(name = "ctype_expires")
    private Integer ctypeExpires;//`ctype_expires` INTEGER DEFAULT NULL COMMENT '期限',
    @Column(name = "ctype_id")
    private Short ctypeId;//`ctype_id` SMALLINT DEFAULT NULL COMMENT '期限单位（ctype_id）',
    @Column(name = "valid_from")
    private Date validFrom;//`valid_from` DATETIME(3) DEFAULT NULL COMMENT '有效期开始时间',
    @Column(name = "valid_thru")
    private Date validThru;//`valid_thru` DATETIME(3) DEFAULT NULL COMMENT '有效期截止时间',
    @Column
    private Byte status;//`status` TINYINT DEFAULT NULL COMMENT '卡状态1有效2已退',
    @JSONField(serialize = false)
    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;// `create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',
    @JSONField(serialize = false)
    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;//`update_time` DATETIME(3) DEFAULT NULL COMMENT '更新时间',

    @Transient
    private BigDecimal factFee;

    @Transient
    private BigDecimal annualFee;

    @Transient
    private BigDecimal discount;   //折扣

    @Transient
    private BigDecimal spaceFee;                               //'空间使用费',

    @Transient
    private Long spaceAllocation;                              //'空间分配量:b为单位',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Long orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getAccMdseId() {
        return accMdseId;
    }

    public void setAccMdseId(Long accMdseId) {
        this.accMdseId = accMdseId;
    }

    public Integer getCtypeExpires() {
        return ctypeExpires;
    }

    public void setCtypeExpires(Integer ctypeExpires) {
        this.ctypeExpires = ctypeExpires;
    }

    public Short getCtypeId() {
        return ctypeId;
    }

    public void setCtypeId(Short ctypeId) {
        this.ctypeId = ctypeId;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public Date getValidThru() {
        return validThru;
    }

    public void setValidThru(Date validThru) {
        this.validThru = validThru;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getFactFee() {
        return factFee;
    }

    public void setFactFee(BigDecimal factFee) {
        this.factFee = factFee;
    }

    public BigDecimal getAnnualFee() {
        return annualFee;
    }

    public void setAnnualFee(BigDecimal annualFee) {
        this.annualFee = annualFee;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getSpaceFee() {
        return spaceFee;
    }

    public void setSpaceFee(BigDecimal spaceFee) {
        this.spaceFee = spaceFee;
    }

    public Long getSpaceAllocation() {
        return spaceAllocation;
    }

    public void setSpaceAllocation(Long spaceAllocation) {
        this.spaceAllocation = spaceAllocation;
    }

    public AuthAccValidPeriod() {

    }

    public AuthAccValidPeriod(Long orderDetailId, AuthInfoDto authInfo, AuthAccMdse authAccMdse, Integer ctypeExpires, EcService.CTpye ctype, Date validFrom, Date validThru, Byte status) {
        this.orderDetailId = orderDetailId;
        this.accId = authInfo.getAccId();
        this.accMdseId = authAccMdse.getId();
        this.ctypeExpires = ctypeExpires;
        this.ctypeId = ctype.getIndex();
        this.validFrom = validFrom;
        this.validThru = validThru;
        this.status = status;
    }

    public AuthAccValidPeriod(Integer id, Long orderDetailId, Long accId, Integer userId, Long memberId, Long accMdseId, Integer ctypeExpires, Short ctypeId, Date validFrom, Date validThru, Byte status, Date createTime, Date updateTime) {
        this.id = id;
        this.orderDetailId = orderDetailId;
        this.accId = accId;
        this.userId = userId;
        this.memberId = memberId;
        this.accMdseId = accMdseId;
        this.ctypeExpires = ctypeExpires;
        this.ctypeId = ctypeId;
        this.validFrom = validFrom;
        this.validThru = validThru;
        this.status = status;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
}
