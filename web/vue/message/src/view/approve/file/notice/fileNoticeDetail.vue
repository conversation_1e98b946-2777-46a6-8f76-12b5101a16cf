n<template>
  <div id="fileNoticeDetail">
    <TY_NavTop :title="titleName"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="place">
        <div class="placeContainer">
          <p v-if="type === 2">
            您已获得本文件的使用权限，请及时查阅并签收！<br>
            此外，公司可能组织针对本文件的学习或考试，具体请留
            意系统消息。
          </p>
          <p v-if="type === 3">
            本文件已被换版，您需查阅及签收，请及时办理。<br>
            此外，公司可能组织针对本文件的学习或考试，具体请留
            意系统消息。
          </p>
          <p v-if="type === 4">
            本文件已被废止，您需知悉，并需签收。
          </p>
          <p v-if="type === 5">
            本文件废止后又被复用，您需知悉，并需签收。
          </p>
          <p v-if="type === 8">
            本文件停止使用后又被复用，您需知悉，并需签收。
          </p>
          <p>
            <el-checkbox v-model="checked"></el-checkbox>
            <span v-if="type === 2 || type === 3">我已读过该文件，且已知晓上述事项，现予签收！</span>
            <span v-if="type === 4">我已收到关于{{fileDetails.details.name}}文件已被废止的通知！</span>
            <span v-if="type === 5">我已收到关于{{fileDetails.details.name}}文件被废止后又被复用的通知！</span>
            <span v-if="type === 8">我已收到关于{{fileDetails.details.name}}文件停止使用后又被复用的通知！</span>
          </p>
          <div class="handle_button">
            <button class="ui-btn ui-btn_info" @click="toHandle()">确定</button>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-btn-groups" @click="showDialog(0)">
            <div class="line-p">
              <div class="tt-cell"><b>{{fileDetails.details.name}}</b></div>
              <div class="con-cell">
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell">{{fileDetails.details.fileSn}}</div>
          </div>
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell" v-html="filePosition"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件类型</div>
            <div class="con-cell">{{fileDetails.details.version}}</div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件大小</div>
            <div class="con-cell">{{fileDetails.details.size | formatByte}}</div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{fileDetails.details.changeNum}}</div>
          </div>
          <div class="line-p" v-if="!fileDetails.details.changeNum">
            <div class="tt-cell">说明</div>
            <div class="con-cell" v-text="fileDetails.details.content"></div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">换版原因</div>
            <div class="con-cell">{{fileDetails.details.content}}</div>
          </div>
        </div>

      </div>
      <div class="place" v-if="type === 3">
        <div class="placeContainer">
          <div class="line-btn-groups">
            <div class="line-p">
              <div class="tt-cell">文件的历史版本</div>
              <div class="con-cell">
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="record">
            <div class="record_title">审批记录</div>
            <div class="script">
              <div>
                <span class="sm-ttl">申请人：</span>
                <span class="sm-con" v-text="approveRecord[0].userName"></span>
                <span>{{approveRecord[0].createDate | formatDay}}</span>
              </div>
              <div v-for="(item ,index) in approveRecord" :key="index">
                <div v-if="index === approveRecord.length - 1">
                  <span class="sm-ttl">文管：</span>
                  <span class="sm-con" v-text="item.toUserName"></span>
                  <span>{{item.handleTime | formatDay}}</span>
                </div>
                <div v-else>
                  <span class="sm-ttl">审批人：</span>
                  <span class="sm-con" v-text="item.toUserName"></span>
                  <span>{{item.handleTime | formatDay}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileHandleDetail.path" :download="fileHandleDetail.name + '.' + fileHandleDetail.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { gohistory, canSeeOnline } from '@/js/common'
export default {
  name: 'approveFileDetail',
  data () {
    return {
      type: 0,
      loading: true,
      checked: false,
      pendingDialog: false,
      browser: 1,
      fileHandleDetail: {},
      fileDetails: {
        categoryName: '',
        details: {},
        listAp: [],
        fileVersion: ''
      },
      approveRecord: [{
        userName: '未知',
        createDate: ''
      }],
      titleName: '换版',
      dialogTitle: '',
      seeOnlineDisabled: true
    }
  },
  computed: {
    filePosition () {
      let pos = this.fileDetails.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    let resourceHistory = this.$route.params.id
    let type = this.$route.query.type
    let title = this.$route.query.title
    console.log('type', this.$route.query.type)
    this.type = type ? Number(type) : 0
    this.titleName = title
    console.log(this.auth.webRoot)
    var that = this
    this.axios.post(this.auth.webRoot + '/res/getUpFileMes.do', {
      id: resourceHistory
    })
      .then(function (response) {
        that.loading = false
        console.log(response)
        let fileData = response.data.data
        if (fileData.listAp.length > 0) {
          that.approveRecord = fileData.listAp
        }
        that.fileDetails.details = fileData.resource
        that.fileDetails.categoryName = fileData.categoryName
      })
      .catch(function (error) {
        console.log(error)
      })
  },
  methods: {
    toHandle() {
      let that = this
      let noticeId = this.$route.query.noticeId
      if (!this.checked) {
        that.$kiko_message('请勾选')
        return false
      }
      this.loading = true
      this.axios.post(this.auth.webRoot + '/res/signedResource.do', {
        type: this.type,
        id: noticeId
      })
        .then(function (response) {
          that.loading = false
          console.log(response)
          let data = response.data.data
          let state = data.state
          if (state === 1) {
            that.$kiko_message('操作成功')
            gohistory(that)
          } else {
            that.$kiko_message('操作失败')
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    showDialog: function (type) {
      let fileHandleDetail = {}
      this.dialogTitle = '文件'
      fileHandleDetail = {
        path: this.fileDetails.details.path,
        name: this.fileDetails.details.name,
        version: this.fileDetails.details.version
      }
      this.seeOnlineDisabled = !canSeeOnline(fileHandleDetail.version)
      this.fileHandleDetail = fileHandleDetail
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    }
  }
}
</script>
