// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import ElementUI from 'element-ui'
import App from './App'
import axios from 'axios'
import router from './router'
import VueResource from 'vue-resource'
import Vuex from 'vuex'
import store from '@/store/index'
import Vant from 'vant'
import 'vant/lib/index.css'
import VeeValidate, { Validator } from 'vee-validate'
import zh from 'vee-validate/dist/locale/zh_CN'
import * as moment from 'moment'
import 'moment/locale/zh-cn'
// import Auth from '@/js/auth.js'
import auth from '@/sys/auth'
import initStorage from '@/sys/initStorage'

Vue.config.productionTip = false
Vue.use(ElementUI)
Vue.use(VueResource)
Vue.use(Vuex)
Vue.use(Vant)
Vue.prototype.$moment = moment
Vue.prototype.axios = axios

auth.init({Vue: Vue, isGuest: true})
initStorage({})

// 定义一个全局过滤器实现日期格式化
Vue.filter('formatDay', function (input, fmtstring = 'YYYY-MM-DD HH:mm:ss') {
  // 使用momentjs这个日期格式化类库实现日期的格式化功能
  return moment(input).format(fmtstring)
})

// 配置中文
Validator.localize('zh_CN', zh)
const config = {
  errorBagName: 'errors', // change if property conflicts.
  fieldsBagName: 'fieldBags', // 报冲突时 可自定义修改字段名称
  delay: 0, // 错误提示的延迟时间
  strict: true, // 没有设置规则的表单不进行校验，
  enableAutoClasses: false,
  locale: 'zh_CN', // 对语言（中文）的配置
  classNames: {
    touched: 'touched', // the control has been blurred
    untouched: 'untouched', // the control hasn't been blurred
    valid: 'valid', // model is valid
    invalid: 'invalid', // model is invalid
    pristine: 'pristine', // control has not been interacted with
    dirty: 'dirty' // control has been interacted with
  },
  events: 'input', //* *input|blur** 在用户输入和表单失去焦点时都进行校验 可单独写  blur或input
  inject: true
}
Vue.use(VeeValidate, config)

// 自定义validate
const dictionary = {
  zh_CN: {
    messages: {
      email: () => '请输入正确的邮箱格式',
      required: (field) => '请输入' + field
    },
    attributes: {
      userName: '姓名',
      offerSalaty: '期望薪资',
      mobile: '联系电话',
      email: '邮箱',
      salary: '薪资水平'
    }
  }
}

Validator.localize(dictionary)

Validator.extend('mobile', {
  messages: {
    zh_CN: field => field + '必须是11位手机号码'
  },
  validate: value => {
    return value.length === 11 && /^((13|14|15|17|18)[0-9]{1}\d{8})$/.test(value)
  }
})

Validator.extend('idCard', {
  messages: {
    zh_CN: field => '请输入正确的' + field
  },
  validate: value => {
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)
  }
})

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
