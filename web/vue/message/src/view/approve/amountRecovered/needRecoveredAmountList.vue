<template>
    <div id="financeApprove">
      <TY_NavTop title="需收回的款" :isBackHome=false></TY_NavTop>
      <div class="container">
        <div class="tipC">
            采用承兑汇票等向供应商支付货款，票据金额大于应付款金额时，多支付的款项需收回。以下为需收回款的清单
        </div>
        <div class="ui-cells">
          <div class="listItem" v-on:click="jump(item)" v-for="(item, index) in list" v-bind:key="index">
            <div class="">
              <div class="">供应商：{{item.name}} <span class="">{{ item.codeName }}</span></div>
              <div class="">
                <p> 需收回的金额：{{ item.amount.toFixed(2) }}元</p>
              </div>
              <div class="txt_right">采购：{{ item.createName }} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
            <div class="ui-cell__ft"></div>
          </div>
        </div>
      </div>
    </div>
  </template>
<script>
export default {
  name: 'needRecoveredAmountList',
  data () {
    return {
      isBackHome: false,
      listenersUid: [],
      list: []
    }
  },
  created: function () {
    let that = this
    this.getList()
    this.listenersUid = [
      // 需收回的款审批人推送
      this.sphdSocket.subscribe('poLoanHandle', function (data) {
        console.log('poLoanHandle OK:')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.list.unshift(getData)
        } else if (operate < 0) {
          let id = getData.id
          let _index = -1
          that.list.forEach(function (item, index) {
            if (item.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.list.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('overpaymentPayable', function (data) {
        console.log('overpaymentPayable OK:')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.list.unshift(getData)
        } else if (operate < 0) {
          let id = getData.id
          let _index = -1
          that.list.forEach(function (item, index) {
            if (item.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.list.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
  },
  methods: {
    jump: function (item) {
      let id = item.id
      console.log('oooooopppppppppppppppppp')
      console.log(id)
      this.$router.push({
        path: `/amountRecoveredDetails/${id}`
      })
    },
    getList: function () {
      let that = this
      this.axios.get('../../../amountRecovered/getAmountRecoveredList.do')
        .then(function (response) {
          let res = response.data.data
          console.log('列表：', res)
          that.loading = false
          that.list = res.poLoanHandle || []
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  }
}
</script>
<style scoped>
.tipC{
  background: #d0e5a6;
  font-size: 0.8em;
  padding:5px 10px;
}
.txt_right{
  text-align: right;
}
.listItem{
  background-color: #fafafb;
  padding: 0.8rem 1.5rem;
  position: relative;
  font-size: 1.2rem;
  overflow: hidden;
  color: inherit;
  cursor: pointer;
  border-top: 1px solid #eee;
}
</style>
