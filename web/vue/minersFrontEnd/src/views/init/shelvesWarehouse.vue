<template>
  <div class="shelvesWarehouse">
    <el-dialog v-model="dialog_visible_imgShow">
      <div class="text-center">
        <img w-full :src="step3_shelfImgUrl" alt="Preview Image" style="width: 100%"/>
      </div>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_editShelf"
        :title="dialog_title_editShelf"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        <el-form
            label-position="top"
            :rules="newShelfRules"
            ref="editShelfForm"
            class="editShelfForm"
            :model="form_editShelf"
        >
          <el-form-item label="代号" prop="shelfCode">
            <el-input v-model="form_editShelf.shelfCode" placeholder="请录入代号"/>
          </el-form-item>
          <el-form-item label="名称" prop="shelfName">
            <el-input v-model="form_editShelf.shelfName" placeholder="请录入名称"/>
          </el-form-item>
          <el-form-item label="层数" prop="layers">
            <el-input v-model="form_editShelf.layers" placeholder="请录入正整数" v-onlyInteger/>
          </el-form-item>
          <el-form-item prop="unit">
            <template #label>尺寸（长 * 宽 * 高）
              <span style="float: right;vertical-align: top">
                尺寸单位
                  <el-select v-model="form_editShelf.unit" size="small" placeholder="请选择" style="width: 130px; vertical-align: top">
                    <el-option
                        v-for="item in options_unit"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                  </el-select></span>
            </template>
            <div class="row">
              <el-col :span="7"><el-form-item prop="length"><el-input v-model="form_editShelf.length" placeholder="请录入“长”" v-onlyInteger/></el-form-item></el-col>
              <el-col :span="1" class="text-center"></el-col>
              <el-col :span="7"><el-form-item prop="width"><el-input v-model="form_editShelf.width" placeholder="请录入“宽”" v-onlyInteger/></el-form-item></el-col>
              <el-col :span="1" class="text-center"></el-col>
              <el-col :span="8"><el-form-item prop="height"><el-input v-model="form_editShelf.height" placeholder="请录入“高”" v-onlyInteger/></el-form-item></el-col>
            </div>
          </el-form-item>
          <el-form-item label="数量" prop="amount">
            <el-input v-model="form_editShelf.amount" placeholder="请录入正整数" v-onlyInteger/>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editShelf = false">取消</el-button>
          <el-button type="primary" @click="step1_submit_editShelf">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_editShelfOfWh"
        title="编辑某仓库所放置的货架"
        width="900"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 800px; margin: 0 auto">
        <p>请录入 <b>{{form_editShelfOfWh.warehouse.warehouseName}}</b> 所放置货架的数量。</p>
        <small class="ty-color-blue">注：没有放置的货架，无需操作！</small>
        <el-table
            :data="form_editShelfOfWh.step2_shelvesList"
            border
            :summary-method="getSummaries"
            show-summary
            style="width: 100%; margin-top: 16px">
          <el-table-column label="代号/名称">
            <template #default="scope">
              {{ scope.row.shelfBase.shelfCode }} / {{ scope.row.shelfBase.shelfName }}
            </template>
          </el-table-column>
          <el-table-column label="尺寸" width="150">
            <template #default="scope">
              {{filter_size(scope.row.shelfBase)}}
            </template>
          </el-table-column>
          <el-table-column label="层数" width="80">
            <template #default="scope">
              {{scope.row.shelfBase.layers}}层
            </template>
          </el-table-column>
          <el-table-column label="总数量 / 可选数量" width="150">
            <template #default="scope">
              {{scope.row.shelfBase.amount}} / <span class="ty-color-blue">{{scope.row.canChooseNum}}</span>
            </template>
          </el-table-column>
          <el-table-column label="本仓库所放置的数量" width="180">
            <template #default="scope">
              <el-input placeholder="请录入数量" v-model="scope.row.setedNum" v-onlyInteger></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editShelfOfWh = false">取消</el-button>
          <el-button type="primary" @click="step2_submit_editShelfOfWh">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_resetWhName"
        title="重新设置仓库名称"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        <el-form>
          <el-form-item label="原名称">
            <el-input v-model="form_editShelfOfWh.warehouse.warehouseName" disabled/>
          </el-form-item>
          <el-form-item label="新名称">
            <el-input v-model="form_editShelfOfWh.warehouse.newWarehouseName" placeholder="请录入新名称"/>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_resetWhName = false">取消</el-button>
          <el-button type="primary" @click="step2_submit_resetWhName">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_resetWhCode"
        title="重新设置仓库代号"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        <el-form>
          <el-form-item label="原代号">
            <el-input v-model="form_editShelfOfWh.warehouse.warehouseCode" disabled/>
          </el-form-item>
          <el-form-item label="新代号">
            <el-input v-model="form_editShelfOfWh.warehouse.newWarehouseCode" placeholder="请录入新代号"/>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_resetWhCode = false">取消</el-button>
          <el-button type="primary" @click="step2_submit_resetWhCode">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_setShelfNo"
        title="设置某仓库内货架的货架号"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <p>请编辑 <b>{{form_editShelfOfWh.warehouse.warehouseName}}</b> 仓库所放置货架的货架号。</p>
        <el-table :data="form_editShelfOfWh.step3_shelvesList" border style="width: 100%; margin-top: 16px">
          <el-table-column label="代号/名称" width="200">
            <template #default="scope">
              {{ scope.row.shelfBase.shelfCode }} / {{ scope.row.shelfBase.shelfName }}
            </template>
          </el-table-column>
          <el-table-column label="尺寸/层数" width="180">
            <template #default="scope">
              {{filter_size(scope.row.shelfBase)}}/{{scope.row.shelfBase.layers}}层
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="数量"/>
          <el-table-column label="状态" width="80">
            <template #default="scope">
              {{scope.row.isSeted === 1?'已设置':'未设置'}}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" size="small" link @click="step3_setShelfNo_edit(scope.row)" :disabled="scope.row.isSeted === 1">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
            v-model="dialog_visible_setShelfNo_edit"
            title="设置某仓库内货架的货架号"
            width="700"
            :close-on-click-modal="false"
            append-to-body
            class="setShelfNo_edit"
            :draggable="true"
        >
          <div class="mainCon" style="width: 600px; margin: 0 auto">
            <p>{{form_editShelfOfWh.warehouse.warehouseName}}仓库内放置以下货架共{{form_editShelfOfWh.step3_editShelf.base.amount}}个</p>
            <p>
              货架信息：
              {{form_editShelfOfWh.step3_editShelf.base.shelfBase.shelfCode}} /
              {{form_editShelfOfWh.step3_editShelf.base.shelfBase.shelfName}} /
              {{filter_size(form_editShelfOfWh.step3_editShelf.base.shelfBase)}} /
              {{form_editShelfOfWh.step3_editShelf.base.shelfBase.layers}} 层
            </p>
            <div class="ty-hr"></div>
            <p class="des">请设置货架号的位数，并根据实际情况，选择一种编辑方式来编辑这些货架的货架号。</p>
            <el-form
                label-width="400"
                label-position="left"
            >
              <el-form-item label="货架号的位数">
                <el-input-number v-model="form_editShelfOfWh.step3_editShelf.placeNum" :min="1" :max="10" placeholder="请录入正整数" v-onlyInteger :disabled="isPlaceNumFixed"/>
              </el-form-item>
              <div class="ty-hr"></div>
              <p class="des">编辑方式1 完全连号式，适用于所编辑货架的货架号为连续号码的情形。</p>
              <el-form-item label="请录入最小的号码">
                <ElInputPlus v-model="form_editShelfOfWh.step3_editShelf.minimumValue" :placeNum="form_editShelfOfWh.step3_editShelf.placeNum" @input-event="input_shelfNo(1)"/>
              </el-form-item>
              <div class="ty-hr"></div>
              <p class="des">编辑方式2 不完全连号式，适用于所编辑货架的货架号不完全是连续号码的情形。</p>
              <p style="align-items: baseline; margin-top: 16px">
                <el-row :gutter="20">
                  <el-col :span="10">连续号码的起号码</el-col>
                  <el-col :span="10">连续号码的止号码</el-col>
                </el-row>
              </p>
              <p>
                <el-row :gutter="20" style="align-items: baseline;margin-top: 4px" v-for="(limit, index) in form_editShelfOfWh.step3_editShelf.limits" :key="index">
                  <el-col :span="10">
                    <ElInputPlus v-model="limit.lowerLimit" :placeNum="form_editShelfOfWh.step3_editShelf.placeNum" @input-event="input_shelfNo(2)"/>
                  </el-col>
                  <el-col :span="10">
                    <ElInputPlus v-model="limit.upperLimit" :placeNum="form_editShelfOfWh.step3_editShelf.placeNum" @input-event="input_shelfNo(2)"/>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="primary" link @click="step3_setShelfNo_edit_addRow()" v-if="index === 0">增加一组</el-button>
                    <el-button type="danger" link @click="step3_setShelfNo_edit_delRow(index)" v-else>删除</el-button>
                  </el-col>
                </el-row>
              </p>
              <el-row style="align-items: center">
                <el-checkbox v-model="form_editShelfOfWh.step3_editShelf.isStartsInputSingle" @change="change_checkbox_isStartsInputSingle"></el-checkbox> <span style="margin-left: 8px">连续号码的货架号已录完，录入不连续的货架号</span>
                <el-button type="primary" link @click="step3_setShelfNo_edit_submit_checkbox" style="margin-left: 32px">确定</el-button>
              </el-row>
              <p style="margin: 0" v-show="form_editShelfOfWh.step3_editShelf.visible_startInputSingle">
                还有{{form_editShelfOfWh.step3_editShelf.singleValues.length}}个货架没有货架号，请逐个录入！
                <el-row :gutter="20" style="align-items: baseline; margin-top: 8px">
                  <el-col :span="10" v-for="(item, index) in form_editShelfOfWh.step3_editShelf.singleValues" :key="index">
                    <el-form-item label="货架号" label-width="80" style="margin-bottom: 8px">
                      <el-input v-model="form_editShelfOfWh.step3_editShelf.singleValues[index]" placeholder="请录入正整数" v-onlyInteger clearable @blur="blur_judgePlaceNum($event)"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </p>
            </el-form>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="dialog_visible_setShelfNo_edit = false">取消</el-button>
              <el-button type="primary" @click="step3_submit_setShelfNo_edit">确定</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_setShelfNo = false">取消</el-button>
          <el-button type="primary" @click="dialog_visible_setShelfNo = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeShelfNo"
        title="查看某仓库内货架的货架号"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <p><b>{{form_checkAndCorrect.step5_editShelf.warehouse.warehouseName}}</b> 仓库所放置货架的货架号。</p>
        <el-row :gutter="20" style="align-items: baseline; margin-top: 16px">
          <el-col :span="6" v-for="(item, index) in form_checkAndCorrect.step5_editShelf.shelfNoListOfWh" :key="index">
            <div class="see_shelfNo_box">
              货架号： {{item.shelfCode}}
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeShelfNo = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_uploadShelfImg"
        title="上传某仓库的布置图"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <div class="row">
          {{form_editImgOfWh.warehouse.warehouseName}}仓库的布置图已上传{{form_editImgOfWh.step3_imgList.length}}张
        </div>
        <el-upload
            v-model:file-list="form_editImgOfWh.step3_imgList"
            :headers="imgUpload.uploadHeaders"
            :action="imgUpload.uploadAction"
            :accept="imgUpload.uploadLyc"
            :data="imgUpload.postData"
            :on-success="step3_shelfImgSuccess"
            :on-preview="step3_shelfImgPreview"
            list-type="picture-card"
            style="margin-top: 16px"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_uploadShelfImg = false">取消</el-button>
          <el-button type="primary" @click="step3_submit_uploadShelfImg">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeShelfImg"
        title="查看某仓库的布置图"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <div class="row">
          {{form_editImgOfWh.warehouse.warehouseName}}仓库的布置图已上传{{form_editImgOfWh.warehouse.warehouseImages.length}}张
        </div>
        <div style="margin-top: 16px">
          <el-image
              style="width: 150px; height: 150px"
              :src="realPath(item.normalPath)"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="form_editImgOfWh.warehouse.prewUrls"
              :initial-index="index"
              fit="scale-down"
              v-for="(item, index) in form_editImgOfWh.warehouse.warehouseImages"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeShelfImg = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_setArrow"
        title="箭头设置"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <p>下表展示为各仓库当前采用“向上”与“向下”箭头的库位，如需调整，请点击“调整”。</p>
        <div class="row">
          此外，当前箭头样式为“
          <InitArrow :arrowType="form_label.arrowStyle"></InitArrow>
          ”，如需要，可更换！
          <div class="rightBtn">
            <el-button type="primary" link @click="step4_setArrowStyle">更换箭头样式</el-button>
          </div>
        </div>
        <el-table :data="whList" border style="width: 100%; margin-top: 16px">
          <el-table-column label="仓库名称/代号">
            <template #default="scope">
              {{scope.row.warehouseName}} / {{scope.row.warehouseCode}}
            </template>
          </el-table-column>
          <el-table-column prop="up" label="采用“向上”箭头的库位"/>
          <el-table-column prop="down" label="采用“向下”箭头的库位" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link @click="step4_editLocationsArrowOfWh(scope.row)">调整</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-dialog
            v-model="dialog_visible_editArrowOfLocation"
            title="调整数量"
            width="800"
            :close-on-click-modal="false"
            append-to-body
            :draggable="true"
        >
          <div class="mainCon" style="width: 700px; margin: 0 auto">
            <p>进入本页面时，{{form_editLabel.form_editArrowOfLocation.whInfo.warehouseName}}/{{form_editLabel.form_editArrowOfLocation.whInfo.warehouseCode}}内采用“向上”箭头的库位共{{form_editLabel.form_editArrowOfLocation.whInfo.up}}个，“向下”箭头的库位共{{form_editLabel.form_editArrowOfLocation.whInfo.down}}个。</p>
            <div class="ty-hr"></div>
            <div class="row">
              如需改变哪个库位箭头的方向，点击该库位号右侧的箭头即可。
              <div class="rightBtn">
                <el-button type="primary" link @click="step4_changeAllArrow">全部换向</el-button>
              </div>
            </div>
            <div class="row">
              对于本次换向的库位，本页面上暂给予橙色底色！
              <div class="rightBtn">
                当前，本页面橙色底色的库位共{{filter_orangeLength(4)}}个
              </div>
            </div>
            <div class="label_avatar">
              <div class="row" v-for="(item, index) of form_editLabel.form_editArrowOfLocation.shelvesList" :key="index">
                <div :class="['label', it.isArrowChanged?'active':'']" v-for="(it, i) of item" :key="i" @click="step4_changeArrow(it)">
                  {{it.shelfLocationCode}}
                  <InitArrow :arrow-type="form_label.arrowStyle" :arrow-direction="it.tIwsLocationTags.arrowDirection"></InitArrow>
                </div>
              </div>
            </div>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="dialog_visible_editArrowOfLocation = false">取消</el-button>
              <el-button type="primary" @click="step4_submit_editLocationsArrowOfWh">确定</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_setArrow = false">取消</el-button>
          <el-button type="primary" @click="dialog_visible_setArrow = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_setArrowStyle"
        title="更换箭头样式"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <p>请选择您觉得更合适的箭头样式！</p>
        <el-radio-group v-model="form_editLabel.labelInfo.arrowStyle">
          <el-radio :value="index" size="large" v-for="index in 5" :key="index">
            <InitArrow :arrow-type="index"></InitArrow>
          </el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_setArrowStyle = false">取消</el-button>
          <el-button type="primary" @click="step4_submit_setLabel">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_setWhCode"
        title="仓库代号的设置"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <div class="question">1 仓库代号是否显示？</div>
        <el-radio-group v-model="form_editLabel.labelInfo.codeShowable">
          <el-radio :value="1" size="large">是</el-radio>
          <el-radio :value="2" size="large">否</el-radio>
        </el-radio-group>
        <div v-show="form_editLabel.labelInfo.codeShowable === 1">
          <div class="question">2 仓库代号的位置</div>
          <el-radio-group v-model="form_editLabel.labelInfo.codePosition">
            <el-radio :value="1" size="large">在货架号前</el-radio>
            <el-radio :value="2" size="large">在层号后</el-radio>
          </el-radio-group>
        </div>
        <div class="question" v-show="form_editLabel.labelInfo.codeShowable === 1">
          3 重新设置仓库代号
          <el-button type="primary" link @click="step4_resetWhCode_all" style="float:right">去设置</el-button>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_setWhCode = false">取消</el-button>
          <el-button type="primary" @click="step4_submit_setLabel">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_resetWhCode_all"
        title="重新设置仓库代号"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <el-table :data="whList" style="width: 100%">
          <el-table-column prop="warehouseName" label="仓库名称（系统自带）" width="300" />
          <el-table-column label="仓库代号">
            <el-table-column prop="systemCode" label="系统自带" width="120" />
            <el-table-column prop="warehouseCode" label="当前数据" width="120"/>
          </el-table-column>
          <el-table-column label="重新设置仓库代号">
            <template #default="scope">
              <el-button type="primary" link @click="step4_resetWhCode(scope.row)">去操作</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_resetWhCode_all = false">取消</el-button>
          <el-button type="primary" @click="dialog_visible_resetWhCode_all = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_setQrCodePosition"
        title="二维码的位置设置"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <p>二维码的位置</p>
        <el-radio-group v-model="form_editLabel.labelInfo.qrPosition">
          <el-radio :value="1" size="large">在最左侧</el-radio>
          <el-radio :value="2" size="large">在最右侧</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_setQrCodePosition = false">取消</el-button>
          <el-button type="primary" @click="step4_submit_setLabel">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_setShelfLabelSize"
        title="库位标签的尺寸设置"
        width="700"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 600px; margin: 0 auto">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="库位标签的长">
              <el-input v-model="form_editLabel.labelInfo.labelLengh" placeholder="请录入正整数" v-onlyInteger>
                <template #append>mm</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库位标签的宽">
              <el-input v-model="form_editLabel.labelInfo.labelWidth" placeholder="请录入正整数" v-onlyInteger>
                <template #append>mm</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="ty-hr"></div>
        <p><small class="ty-color-blue">注1 设置时需考虑库位铭牌的材质、型式，及安装位置的尺寸；</small></p>
        <p><small class="ty-color-blue">注2 A4纸的尺寸为210mm×297mm，此数据供设置时参考。</small></p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_setShelfLabelSize = false">取消</el-button>
          <el-button type="primary" @click="step4_submit_setLabel">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_addShelf"
        title="增加货架"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <p>每次只能增加一种型式的货架！</p>
        <div class="ty-hr"></div>
        <div>
          <el-form
              :rules="addShelfRules"
              label-position="top"
              ref="addShelfForm"
              class="addShelfForm"
              :model="form_checkAndCorrect.step5_editShelf.addShelf"
          >
            <el-form-item label="在哪个仓库中增加货架" prop="chooseWh">
              <el-select v-model="form_checkAndCorrect.step5_editShelf.addShelf.chooseWh" @change="load_shelves">
                <el-option
                    v-for="item in option_warehouses"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="chooseShelf" v-show="form_checkAndCorrect.step5_editShelf.addShelf.chooseWh">
              <template #label style="padding-right: 0;">货架代号/名称
                <el-button type="primary" link style="float: right;vertical-align: top" @click="step1_addShelf">增加新型式的货架</el-button>
              </template>
              <el-select v-model="form_checkAndCorrect.step5_editShelf.addShelf.chooseShelf">
                <el-option
                    v-for="item in option_shelves"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="数量" prop="amount" v-show="form_checkAndCorrect.step5_editShelf.addShelf.chooseWh">
              <el-input
                  v-model="form_checkAndCorrect.step5_editShelf.addShelf.amount"
                  placeholder="请录入正整数"
                  v-onlyInteger
                  @blur="blur_createShelfNoInput"
                  @input="change_createShelfNoInput"
              >
              </el-input>
            </el-form-item>
            <div v-show="form_checkAndCorrect.step5_editShelf.addShelf.visible_inputShelfNo">
              <el-form-item label="请录入这些货架的货架号" prop="addShelfNo">
                <el-row :gutter="20" style="align-items: baseline; margin-top: 8px; flex: auto">
                  <el-col :span="8" v-for="(item, index) in form_checkAndCorrect.step5_editShelf.addShelf.addShelfNo" :key="index">
                    <el-form-item label="货架号" :prop="`addShelfNo.${index}`" :rules="addShelfRules.addShelfNo" label-width="80" style="margin-bottom: 8px">
                      <ElInputPlus v-model="form_checkAndCorrect.step5_editShelf.addShelf.addShelfNo[index]" :placeNum="placeNum"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_addShelf = false">取消</el-button>
          <el-button type="primary" @click="step5_submit_addShelf" :disabled="!form_checkAndCorrect.step5_editShelf.addShelf.visible_inputShelfNo">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_delShelf"
        title="减少货架"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <p>每次只能减少一种型式的货架！</p>
        <div class="ty-hr"></div>
        <el-form
            label-position="top"
            ref="delShelfForm"
            class="delShelfForm"
            :rules="delShelfRules"
            :model="form_checkAndCorrect.step5_editShelf.delShelf"
        >
          <el-form-item label="要减少哪个仓库的货架" prop="chooseWh">
            <el-select v-model="form_checkAndCorrect.step5_editShelf.delShelf.chooseWh" @change="load_shelves">
              <el-option
                  v-for="item in option_warehouses"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="货架代号/名称" prop="chooseShelf" v-show="form_checkAndCorrect.step5_editShelf.delShelf.chooseWh">
            <el-select v-model="form_checkAndCorrect.step5_editShelf.delShelf.chooseShelf" @change="load_locations">
              <el-option
                  v-for="item in form_checkAndCorrect.option_shelves"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="请选择具体要减少哪些货架" prop="delShelfNoList" v-show="form_checkAndCorrect.step5_editShelf.delShelf.chooseWh && form_checkAndCorrect.step5_editShelf.delShelf.chooseShelf">
            <el-checkbox-group v-model="form_checkAndCorrect.step5_editShelf.delShelf.delShelfNoList">
              <el-checkbox :label="'货架号：' + item.shelfCode" :value="item.shelfCode" v-for="(item, index) in form_checkAndCorrect.step5_editShelf.delShelf.canDelShelfNoList"/>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_delShelf = false">取消</el-button>
          <el-button type="primary" @click="step5_submit_delShelf">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_otherChange"
        title="其他修改"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <el-radio-group v-model="form_otherChanges.changeType" style="flex-direction: column; align-items: flex-start">
          <el-radio :value="1" size="large">修改已有“库位代号”中的“货架号”</el-radio>
          <el-radio :value="2" size="large">修改仓库的名称</el-radio>
          <el-radio :value="3" size="large">编辑某些仓库的“仓库布置图”</el-radio>
          <el-radio :value="4" size="large">重新设置库位标签的格式</el-radio>
        </el-radio-group>
        <br>
        <el-radio-group v-model="form_otherChanges.labelChangeType" style="flex-direction: column; align-items: flex-start; margin-left: 32px" v-if="form_otherChanges.changeType === 4">
          <el-radio :value="1" size="large">重新设置箭头</el-radio>
          <el-radio :value="2" size="large">重新设置仓库代号</el-radio>
          <el-radio :value="3" size="large">重新设置二维码的位置</el-radio>
          <el-radio :value="4" size="large">重新设置库位标签的尺寸</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_otherChange = false">取消</el-button>
          <el-button type="primary" @click="step5_submit_otherChange">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!--    以下为其他修改中的弹窗-->
    <el-dialog
        v-model="dialog_visible_otherChange_changeShelfNo"
        title="修改已有“库位代号”中的“货架号”"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <el-form
            :rules="changeShelfNoRules"
            label-position="left"
            ref="changeShelfNoForm"
            class="changeShelfNoForm"
            :model="form_otherChanges.step5_editShelfNo"
        >
          <el-row :gutter="20" style="align-items: baseline; margin-top: 8px">
            <el-col :span="12" v-for="(item, index) in form_otherChanges.step5_editShelfNo.shelvesList" :key="index">
              <el-form-item label="货架号" :prop="`shelvesList[${index}].shelfCode`" :rules="changeShelfNoRules.shelfCode" label-width="80" style="margin-bottom: 16px">
                <ElInputPlus v-model="item.shelfCode" :placeNum="placeNum" @change="item.changed = true"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_otherChange_changeShelfNo = false">取消</el-button>
          <el-button type="primary" @click="step5_submit_otherChange_changeShelfNo">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_otherChange_changeWhName"
        title="修改仓库的名称"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <el-table :data="whList" border style="width: 100%; margin-top: 16px">
          <el-table-column label="系统自带数据">
            <template #default="scope">
              {{scope.row.systemCode}}
            </template>
          </el-table-column>
          <el-table-column label="当前数据">
            <template #default="scope">
              {{scope.row.warehouseName}}
            </template>
          </el-table-column>
          <el-table-column label="修改仓库名称">
            <template #default="scope">
              <el-button type="primary" link @click="step2_resetWhName(scope.row)">去操作</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_otherChange_changeWhName = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_otherChange_changeWhImage"
        title="编辑某些仓库的“仓库布置图”"
        width="1000"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 900px; margin: 0 auto">
        <el-table :data="whList" border style="width: 100%; margin-top: 16px">
          <el-table-column label="仓库名称/代号">
            <template #default="scope">
              {{ scope.row.warehouseName}} / {{ scope.row.warehouseCode  }}
            </template>
          </el-table-column>
          <el-table-column label="放置货架的数量" >
            <template #default="scope">
              {{scope.row.shelfNum}}个
            </template>
          </el-table-column>
          <el-table-column label="货架号的设置情况">
            <template #default="scope">
              {{scope.row.setedNum > 0?'已':'未'}}设置
            </template>
          </el-table-column>
          <el-table-column label="布置图的上传情况">
            <template #default="scope">
              <span v-if="!scope.row.imageNum">尚未上传</span>
              <span v-else>已上传{{scope.row.imageNum}}张</span>
              <el-button type="primary" link style="margin-left: 16px" :disabled="!scope.row.imageNum" @click="step3_seeShelfImg(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" link @click="step3_uploadShelfImg(scope.row)" :disabled="!scope.row.setedNum">上传布置图</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_otherChange_changeWhImage = false">取消</el-button>
          <el-button type="primary" @click="dialog_visible_otherChange_changeWhImage = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_clearAll"
        title="清空已有的全部数据"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        <div class="row">
          <el-icon class="ty-color-red" style="font-size: 26px"><WarnTriangleFilled /></el-icon> 确定后，您将从“第一步”重新开始本步初始化！
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_clearAll = false">取消</el-button>
          <el-button type="danger" @click="step5_submit_clearAll">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_shelvesLabelSeeAndExport"
        title="库位标签的查看与导出"
        width="900"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 800px; margin: 0 auto">
        <el-row :gutter="20" style="margin-top: 16px">
          <el-col :span="12" style="align-items: end">
            <p>本页面的下方展示为{{form_export.step6_edit.whInfo.warehouseName}} / {{form_export.step6_edit.whInfo.warehouseCode}}内的 {{form_export.step6_edit.locationLength}} 个库位，各库位为按货架号排列。</p>
            <p style="margin-top: 16px">本页面右侧图展示为库位标签导出后的型式。</p>
            <p style="margin-top: 16px">请选择要导出的数据。</p>
          </el-col>
          <el-col :span="12">
            <div class="">
              <div class="effect_title" style="text-align: center">
                <h3>库位标签效果图</h3>
              </div>
              <LabelEffect v-bind="form_label"></LabelEffect>
            </div>
          </el-col>
        </el-row>
        <div class="ty-hr"></div>
        <div class="row">
          橙色底色者为已选数据，共{{filter_orangeLength(6)}}个
          <div class="rightBtn">
            <el-button type="primary" link @click="step6_allGiveUp">全部放弃</el-button>
            <el-button type="primary" link @click="step6_allChoose">全部选中</el-button>
          </div>
        </div>
        <div class="label_avatar">
          <div class="row" v-for="(item, index) of form_export.step6_edit.locationList" :key="index">
            <div :class="['label', it.isSelected?'active':'']" v-for="(it, i) of item" :key="i" @click="step6_changeSelect(it)">
              {{it.shelfLocationCode}}
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_shelvesLabelSeeAndExport = false">取消</el-button>
          <el-button type="primary" @click="dialog_visible_shelvesLabelSeeAndExport = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <div class="ty-container" id="home">
      <div class="page">
        <div v-show="page === 8" style="display: flex; font-size: 18px"><el-icon color="#67c23a"><SuccessFilled /></el-icon><span style="margin-left: 8px">初始化已完成</span></div>
        <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px" v-show="page > 1">
          <el-button type="default" @click="prevStep" v-show="page < 8">上一步</el-button>
          <div class="rightBtn">
            <el-radio value="1" size="large" v-model="radios.step6Ok" v-show="page === 6">库位标签的导出与库位铭牌的制作都已完成。</el-radio>
            <el-radio value="1" size="large" v-model="radios.step7Ok" :disabled="!(radios.labelMakeDone === '1' && radios.labelGiveDone === '1' && radios.labelInstallDone === '1')" v-show="page === 7">本项初始化已完成！</el-radio>
            <el-button type="primary" @click="nextStep" v-show="page < 7">下一步</el-button>
            <el-button type="primary" @click="confirm" v-show="page === 7">确定</el-button>
          </div>
        </div>
        <section v-show="page === 1">
          <div class="row head" style="display: flex; align-items: center">
            <h3>第一步　录入货架信息</h3>
            <div class="rightBtn">
              <el-button type="success" @click="step1_addShelf">增加货架</el-button>
              <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
          </div>
          <p><small>智能仓库模式下，系统要求公司需要有货架。</small></p>
          <p><small>请统计各仓库所用货架的种类与数量，之后点击“增加货架”，以将相关信息录入至系统。</small></p>
          <p><small>全部货架的信息都录入后，请点击“下一步”。</small></p>
          <el-table :data="shelvesList" border style="width: 100%; margin-top: 16px">
            <el-table-column label="货架的代号/名称">
              <template #default="scope">
                {{ scope.row.shelfCode }} / {{ scope.row.shelfName }}
              </template>
            </el-table-column>
            <el-table-column label="尺寸">
              <template #default="scope">
                {{filter_size(scope.row)}}
              </template>
            </el-table-column>
            <el-table-column label="层数">
              <template #default="scope">
                {{scope.row.layers}}层
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="数量" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="step1_editShelf(scope.row)">修改基本信息</el-button>
                <el-button type="danger" link @click="step1_deleteShelf(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 2">
          <div class="row head">
            <h3>第二步　仓库的初始设置</h3>
          </div>
          <p><small>每个仓库放置了哪些货架？请统计，并逐仓库编辑。</small></p>
          <p><small class="ty-color-blue">注 下表中的“仓库代号”与“仓库名称”如需要均可重新设置，其中“仓库代号”在编辑“库位代号”时可能用到。</small></p>
          <el-table :data="whList" border style="width: 100%; margin-top: 16px">
            <el-table-column prop="warehouseName" label="仓库名称" width="180" />
            <el-table-column prop="warehouseCode" label="仓库代号" width="180" />
            <el-table-column label="所放置的货架">
              <template #default="scope">
                <span class="valign-middle" v-if="scope.row.setedNum > 0">已编辑 {{scope.row.setedNum}}个</span>
                <span class="valign-middle" v-else>未编辑</span>
                <el-button type="primary" link @click="step2_editShelfOfWh(scope.row)" style="margin-left: 16px">编辑</el-button>
              </template>
            </el-table-column>
            <el-table-column label="重新设置">
              <template #default="scope">
                <el-button type="primary" link @click="step2_resetWhName(scope.row)">仓库名称</el-button>
                <el-button type="primary" link @click="step2_resetWhCode(scope.row)">仓库代号</el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 3">
          <div class="row head">
            <h3>第三步　库位代号中的货架号</h3>
          </div>
          <p><small>系统内，“库位代号”包含“货架号”与“层号”。“层号”无需设置，“货架号”则需在本步骤设置。</small></p>
          <div class="ty-hr"></div>
          <p><small>下表中，“货架号”为必做项，请编辑；各仓库布置图的上传将为各仓库负责人与领料者的日常工作带来便利，故虽不是必做项但建议上传。</small></p>
          <p><small class="ty-color-blue">注　所上传的仓库布置图上，应标识出货架的位置。</small></p>
          <el-table :data="whList" border style="width: 100%; margin-top: 16px">
            <el-table-column label="仓库名称/代号">
              <template #default="scope">
                {{ scope.row.warehouseName}} / {{ scope.row.warehouseCode  }}
              </template>
            </el-table-column>
            <el-table-column label="放置货架的数量" >
              <template #default="scope">
                {{scope.row.shelfNum}}个
              </template>
            </el-table-column>
            <el-table-column label="货架号的设置情况">
              <template #default="scope">
                {{scope.row.locationNum > 0?'已':'未'}}设置
              </template>
            </el-table-column>
            <el-table-column label="布置图的上传情况">
              <template #default="scope">
                <span class="valign-middle" v-if="!scope.row.imageNum">尚未上传</span>
                <span class="valign-middle" v-else>已上传{{scope.row.imageNum}}张</span>
                <el-button type="primary" link style="margin-left: 16px" :disabled="!scope.row.imageNum" @click="step3_seeShelfImg(scope.row)">查看</el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="step3_setShelfNo(scope.row)" :disabled="!scope.row.setedNum">设置货架号</el-button>
                <el-button type="primary" link @click="step3_uploadShelfImg(scope.row)" :disabled="!scope.row.setedNum">上传布置图</el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <!-- 第四步 -->
        <section v-show="page === 4">
          <div class="row head">
            <h3>第四步　库位标签的格式设置</h3>
          </div>
          <p><small>“货架号”设置完后，各仓库全部库位的代号主体部分即已确定，同时，系统已为这些库位生成了二维码。</small></p>
          <p><small>请对库位标签的格式进行设置，之后点击“下一步”。</small></p>
          <p><small class="ty-color-blue">注：初始化完成、各仓库正式启用后，可通过扫描某库位的二维码，查看该库位所防物品的信息。</small></p>
          <div class="ty-hr"></div>
          <p><small>左下方为库位标签格式方面需设置的项目，右下方库位标签效果图。请按公司要求及实际情况进行设置。</small></p>
          <p><small class="ty-color-blue">注 进行某项设置后，效果图的库位代号“CP”、货架号12及层号3不跟随设置而变化，其他将跟设置而变化。</small></p>
          <el-row :gutter="20" style="margin-top: 16px">
            <el-col :span="12">
              <el-table :data="table_labelSet" border style="width: 100%;">
                <el-table-column prop="name" label="项目"/>
                <el-table-column prop="des" label="说明" width="200"/>
                <el-table-column label="操作">
                  <template #default="scope">
                    <el-button type="primary" link @click="step4_setLabel(scope.row.code)">设置</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="12">
              <div class="effect_avatar">
                <div class="effect_title" style="text-align: center">
                  <h3>库位标签效果图</h3>
                </div>
                <LabelEffect v-bind="form_label"></LabelEffect>
              </div>
            </el-col>
          </el-row>
        </section>
        <!-- 第五步 -->
        <section v-show="page === 5">
          <div class="row head" style="display: flex; align-items: center">
            <h3>第五步　对已有数据的检查与修正</h3>
            <div class="rightBtn">
              <el-button type="primary" link @click="step5_addShelf">增加货架</el-button>
              <el-button type="primary" link @click="step5_delShelf">减少货架</el-button>
              <el-button type="primary" link @click="step5_otherChange">其他修改</el-button>
              <el-button type="primary" link @click="step5_clearAll">清空已有的全部数据</el-button>
            </div>
          </div>
          <div class="row">
            <p><small>请对本项已初始化的数据进行检查与修正。检查无误后，请点击右上角的“下一步”。</small></p>
          </div>
          <div class="ty-hr"></div>
          <el-row :gutter="20" style="margin-top: 16px">
            <el-col :span="12">
              <p><small>本项已初始化的数据如下：</small></p>
              <p style="margin-top: 16px"><small>1、货架共录入{{form_checkAndCorrect.shelfCateNum}}种，{{form_checkAndCorrect.shelfNum}}个</small></p>
              <p style="margin-top: 8px"><small>2、库位标签的型式如右侧的效果图所示</small></p>
            </el-col>
            <el-col :span="12">
              <div class="effect_avatar">
                <div class="effect_title" style="text-align: center">
                  <h3>库位标签效果图</h3>
                </div>
                <LabelEffect v-bind="form_label"></LabelEffect>
              </div>
            </el-col>
          </el-row>
          <p><small>3、各仓库货架号的编辑情况及布置图的上传情况如下表：</small></p>
          <el-row style="margin-top: 16px">
            <el-table :data="whList" border style="width: 100%; margin-top: 16px">
              <el-table-column prop="warehouseName" label="仓库名称"/>
              <el-table-column prop="warehouseCode" label="仓库代号"/>
              <el-table-column label="放置货架的数量" >
                <template #default="scope">
                  {{scope.row.shelfNum}}个
                </template>
              </el-table-column>
              <el-table-column label="货架号的编辑情况">
                <template #default="scope">
                  <span class="valign-middle">{{scope.row.setedNum > 0?'已':'未'}}编辑</span>
                  <el-button type="primary" link style="margin-left: 16px" @click="step5_seeShelfNo(scope.row)">查看</el-button>
                </template>
              </el-table-column>
              <el-table-column label="布置图的上传状态">
                <template #default="scope">
                  <span class="valign-middle" v-if="!scope.row.imageNum">尚未上传</span>
                  <span class="valign-middle" v-else>已上传{{scope.row.imageNum}}张</span>
                  <el-button type="primary" link style="margin-left: 16px" :disabled="!scope.row.imageNum" @click="step3_seeShelfImg(scope.row)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </section>
        <!-- 第六步 -->
        <section v-show="page === 6">
          <div class="row head">
            <h3>第六步　库位标签的导出与库位铭牌的制作</h3>
          </div>
          <p><small>请点击各仓库的“去操作”，以将库位标签导出至电脑，进而制作库位铭牌。这两项工作都完成后，请点击右上角的“下一步”。</small></p>
          <el-row style="margin-top: 16px">
            <el-table :data="whList" border style="width: 100%;">
              <el-table-column prop="warehouseName" label="仓库名称"/>
              <el-table-column prop="warehouseCode" label="仓库代号"/>
              <el-table-column prop="shelfNum" label="仓库内的货架数"/>
              <el-table-column prop="locationNum" label="仓库内的库位数"/>
              <el-table-column label="查看与导出">
                <template #default="scope">
                  <el-button type="primary" link @click="step6_exportHandle(scope.row)">去操作</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </section>
        <!-- 第六步 -->
        <section v-show="page === 7">
          <div class="row head">
            <h3>第七步　库位铭牌的交付与安装，及本项初始化的完成</h3>
          </div>
          <p><small>请确认以下各项工作的进展，完成的请勾选“是”。</small></p>
          <p><small>都完成后，请勾选右上角的“本项初始化已完成”并点击“确定”，以结束本项初始化工作。</small></p>
          <el-row :gutter="20" style="margin-top: 16px">
            <el-col :span="16">
              1、库位铭牌是否已制作好？
            </el-col>
            <el-col :span="8">
              <el-radio-group v-model="radios.labelMakeDone">
                <el-radio value="1" size="large">是</el-radio>
                <el-radio value="2" size="large">否</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 16px" v-show="radios.labelMakeDone === '1'">
            <el-col :span="16">
              2、制作好的库位铭牌是否已交付至铭牌安装者手中？
              <p><small class="ty-color-blue">注：铭牌安装者需由贵公司自定，可以是各仓库负责人，也可以是其他人。</small></p>
            </el-col>
            <el-col :span="8">
              <el-radio-group v-model="radios.labelGiveDone">
                <el-radio value="1" size="large">是</el-radio>
                <el-radio value="2" size="large">否</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 16px" v-show="radios.labelMakeDone === '1' && radios.labelGiveDone === '1'">
            <el-col :span="16">
              3、库位铭牌是否已安装到相应的库位上？
            </el-col>
            <el-col :span="8">
              <el-radio-group v-model="radios.labelInstallDone">
                <el-radio value="1" size="large">是</el-radio>
                <el-radio value="2" size="large">否</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
        </section>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ty-container{
  margin-top:35px; padding:10px;
}
.ty-page-header{
  display: flex;
  line-height: 24px;
  padding: 0 0 0 70px;
  color: #5d9cec;
  margin-top: 16px;
  .page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 16px;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      background-color: #dcdfe6;
    }
    .icon-back {
      font-size: 18px;
      margin-right: 6px;
      align-self: center;
      position: relative;
      top: 1px;
    }
  }
}
.page{
  padding: 8px 0;
  max-width: 1000px;
  position: relative;
  margin: 0 70px;
  .panel-box{
    border-top: 1px solid #ddd;
    padding-top: 8px;
    &:first-child{
      border: none
    }
  }
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.effect_avatar{
  background: rgb(64, 158, 255);
  padding: 24px 64px;
  border-radius: 3px;
  .effect_title{
    color: #fff;
  }
  .effect_main_avatar{
    .effect_main{
      background: #fff;
    }
  }
}
.effect_main_avatar{
  margin-top: 16px;
  margin-bottom: 4px;
  .effect_main{
    display: flex;
    background: #f5f5f5;
    padding: 16px;
    text-align: center;
    border-radius: 5px;
    font-size: 24px;
    align-items: center;
    .effect_code{
      flex: auto;
    }
    .effect_shelfNum{
      flex: auto;
    }
    .effect_floorNum{
      flex: auto;
    }
    .effect_qrCode{
      width: 100px;
      flex: none;
      height: 48px;
    }
    .effect_des{
      color: #666;
    }
    h2{
      font-family: Helvetica;
    }
  }
}
.label_avatar{
  margin-top: 16px;
  .label{
    width: 139px;
    height: 36px;
    background: #efefef;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    border: 1px solid #d9d9d9;
    margin-left: -1px;
    margin-top: -1px;
    &.active{
      background: $ty-color-orange;
      color: #fff;
      border-color: #d97610;
    }
    .el-icon{
      vertical-align: middle;
    }
  }
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
:deep(.el-form--label-top) {
  .el-form-item__label{
    width: 100%;
    padding: 0;
  }
}
.text-center{
  text-align: center;
}
.des{
  color: #333;
}
.setShelfNo_edit{
  p{
    margin-bottom: 8px;
  }
}
div.question{
  margin-top: 8px;
}
.valign-middle{
  vertical-align: middle;
}
.see_shelfNo_box{
  padding: 8px 4px;
}
.loginSet{
  padding: 40px 100px;
  .tip{ padding: 10px; }
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import auth from '@/sys/auth'
import * as api from "@/api/init.js"
import InitArrow from "@/components/shelfWh/InitArrow.vue"
import ElInputPlus from "@/components/shelfWh/ElInputPlus.vue"
import LabelEffect from "@/components/shelfWh/LabelEffect.vue"
export default {
  data() {
    return {
      imgUpload: {
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.png,.jpeg,.jpg,.gif',
        postData: {
          module: '仓库布置图', userId: auth.getUserID()
        },
      },
      pageName: 'init_shelvesWarehouse',
      pageShow: false,
      pageInfo:{ 'currentPageNo': 0, 'pageSize': 20 , 'totalPage': 0  },
      options_unit: [{
        value: 1,
        label: '米（m）'
      }, {
        value: 2,
        label: '厘米（cm）'
      }, {
        value: 3,
        label: '毫米（mm）'
      }],
      stepList: [],
      shelvesList: [],
      form_editShelfOfWh: {
        warehouse: {}, // 要编辑的仓库信息（主要用来展示）公用
        step2_shelvesList: [], // 第二步使用 可选择的货架列表（包括要录入的数量）
        step3_shelvesList: [], // 第三步获取当前库的已录入的货架列表
        step3_editShelf: {
          base: {},
          placeNum: '3',
          markType: '',
          limits: [{lowerLimit: '', upperLimit: ''}],
          minimumValue: '',
          isStartsInputSingle: false, // 是否开始录入录入不连续的货架号
          singleValues: [],
          visible_startInputSingle: false
        } // 第三步编辑存储的当前货架的信息（包括后面具体的货架编号）
      }, // 用来存储编辑仓库的货架时的信息
      form_editImgOfWh: {
        warehouse: {}, // 要编辑的仓库信息（主要用来展示）公用
        step3_imgList: []
      },
      whList: [],
      step3_shelfImgUrl: '',
      dialog_visible_imgShow: false,
      dialog_visible_editShelf: false,
      dialog_title_editShelf: '新增货架',
      dialog_visible_editShelfOfWh: false,
      dialog_visible_resetWhName: false,
      dialog_visible_resetWhCode: false,
      dialog_visible_setShelfNo: false,
      dialog_visible_setShelfNo_edit: false,
      dialog_visible_seeShelfNo: false,
      dialog_visible_uploadShelfImg: false,
      dialog_visible_seeShelfImg: false,
      dialog_visible_setArrow: false,
      dialog_visible_setArrowStyle: false,
      dialog_visible_editArrowOfLocation: false,
      dialog_visible_setWhCode: false,
      dialog_visible_resetWhCode_all: false,
      dialog_visible_setQrCodePosition: false,
      dialog_visible_setShelfLabelSize: false,
      dialog_visible_addShelf: false,
      dialog_visible_delShelf: false,
      dialog_visible_otherChange: false,
      dialog_visible_otherChange_changeShelfNo: false,
      dialog_visible_otherChange_changeWhName: false,
      dialog_visible_otherChange_changeWhImage: false,
      dialog_visible_clearAll: false,
      dialog_visible_shelvesLabelSeeAndExport: false,
      radios: {
        labelMakeDone: '',
        labelGiveDone: '',
        labelInstallDone: '',
        step6Ok: '',
        step7Ok: ''
      },
      page: 1,
      form_editShelf: {
        shelfName: '',
        shelfCode: '',
        layers: '',
        unit: 1,
        amount: '',
        width: '',
        length: '',
        height: ''
      },
      form_label: {
        id: 0,
        codeShowable: 1, // 是否显示仓库代号
        codePosition: 1, // 仓库代号位置:1-在货架号前,2-在层号后
        qrPosition: 2, // 二维码位置:1-在最左侧,2-在最右侧
        labelLengh: '', // 标签长度(mm)
        labelWidth: '', // 标签宽度(mm)
        labelImage: '', // 标签图片地址
        arrowStyle: 1
      },
      form_editLabel: {
        whInfo: {},
        step4_shelvesList: [],
        form_editArrowOfLocation: {
          whInfo: {},
          shelvesList: []
        },
        labelInfo: {
          codeShowable: 1, // 是否显示仓库代号
          codePosition: 1, // 仓库代号位置:1-在货架号前,2-在层号后
          qrPosition: 2, // 二维码位置:1-在最左侧,2-在最右侧
          labelLengh: '', // 标签长度(mm)
          labelWidth: '', // 标签宽度(mm)
          labelImage: '', // 标签图片地址
          arrowStyle: 1
        },
        editContentName: '',
        step4_whList_all: []
      },
      form_otherChanges: {
        changeType: 0,
        labelChangeType: 0,
        step5_editShelfNo: {
          shelvesList: []
        },
        step5_editWhName: {
          whList: []
        }
      },
      form_checkAndCorrect: {
        shelfCateNum: 0,
        shelfNum: 0,
        option_warehouses: [],
        option_shelves: [],
        step5_editShelf: {
          addShelf: {
            chooseWh: '',
            chooseShelf: '',
            amount: '',
            addShelfNo: [],
            visible_inputShelfNo: false
          },
          delShelf: {
            chooseWh: '',
            chooseShelf: '',
            canDelShelfNoList: [],
            delShelfNoList: [],

          },
          warehouse: 0,
          shelfNoListOfWh: [],
        }
      },
      form_export: {
        step6_edit: {
          whInfo: {},
          locationList: [],
          locationLength: 0
        }
      },
      placeNum: 3, //位数
      isPlaceNumFixed: true, //是否固定(设置过一次库位号位数就固定了）
      newShelfRules: {
        shelfName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        shelfCode: [
          { required: true, message: '请输入代号', trigger: 'blur' }
        ],
        layers: [
          { required: true, message: '请输入层数', trigger: 'blur' }
        ],
        length: [
          { required: true, message: '请输入长', trigger: 'blur' }
        ],
        width: [
          { required: true, message: '请输入宽', trigger: 'blur' }
        ],
        height: [
          { required: true, message: '请输入高', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请选择单位', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ]
      },
      addShelfRules: {
        chooseWh: [
          { required: true, message: '请选择仓库', trigger: 'change' }
        ],
        chooseShelf: [
          { required: true, message: '请选择货架', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        addShelfNo: [
          { required: true, message: '请输入货架号', trigger: 'blur' }
        ]
      },
      delShelfRules: {
        chooseWh: [
          { required: true, message: '请选择仓库', trigger: 'change' }
        ],
        chooseShelf: [
          { required: true, message: '请选择货架', trigger: 'change' }
        ],
        delShelfNoList: [
          { type: 'array', required: true, message: '请至少选择一个货架号'}
        ]
      },
      changeShelfNoRules: {
        shelfCode: [
          { required: true, message: '请输入货架号', trigger: 'blur' }
        ]
      },
      table_labelSet: []
    }
  },
  components: {
    InitArrow,
    ElInputPlus,
    LabelEffect
  },
  computed: {
    option_warehouses() {
      return this.whList.map(item => {
        return {
          label: item.warehouseName,
          value: item.id
        }
      })
    },
    option_shelves() {
      return this.shelvesList.map(item => {
        return {
          label: item.shelfCode + '/' + item.shelfName,
          value: item.id,
          disabled: item.amount === item.usedAmount
        }
      })
    },
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'vg', name: '货架与库位', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      console.log('rootPath.fileUrl', this.rootPath)
      this.table_labelSet = [{
        name: '箭头',
        code: 'setArrow',
        des: '有默认值，可不设置'
      }, {
        name: '仓库代号',
        code: 'setWhCode',
        des: '有默认值，可不设置'
      }, {
        name: '二维码的位置',
        code: 'setQrCodePosition',
        des: '有默认值，可不设置'
      }, {
        name: '库位标签的尺寸',
        code: 'setShelfLabelSize',
        des: '需设置'
      }]
      this.getShelvesList()
      this.getWarehouseList()
      this.getArrowSet()
      api.stepList()
          .then(res => {
            let stepList = res.data.data
            this.stepList = stepList
            let step = stepList.findLastIndex(item => item.state === 1)
            this.page = step + 2
            console.log('page' + this.page)
          })
      api.getPlaceNum()
          .then(res => {
            this.placeNum = res.data || 3
            this.isPlaceNumFixed = !!res.data
          })
      this.countByShelf()
    },
    countByShelf() {
      api.countShelfNumAndLocationNum()
          .then(res1 => {
            let data = res1.data.data
            this.form_checkAndCorrect.shelfCateNum = data.cateNum
            this.form_checkAndCorrect.shelfNum = data.amount
          })
    },
    getShelvesList () {
      let that = this
      api.shelvesList().then(res => {
        that.shelvesList = res.data
      })
    },
    getWarehouseList () {
      let that = this
      api.warehouseList().then(res => {
        that.whList = res.data
      })
    },
    getArrowSet () {
      let that = this
      api.labelOfWh()
          .then(res => {
            that.form_label = res.data[0]
          })
    },
    step1_addShelf () {
      this.dialog_visible_editShelf = true
      this.dialog_title_editShelf = '新增货架'
      this.$nextTick(() => {
        this.$refs['editShelfForm'].resetFields()
      })
    },
    step1_editShelf (item) {
      this.dialog_visible_editShelf = true
      this.dialog_title_editShelf = '修改基本信息'
      let thisData = JSON.parse(JSON.stringify(item))
      let keyword = ['id', 'shelfCode', 'shelfName', 'layers', 'number', 'amount', 'unit', 'length', 'width', 'height']
      keyword.forEach(item => {
        if (item === 'unit') {
          this.form_editShelf[item] = Number(thisData[item])
        } else {
          this.form_editShelf[item] = thisData[item]
        }
      })
    },
    step1_submit_editShelf () {
      let that = this
      this.$refs['editShelfForm'].validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(that.form_editShelf))
          let id = that.form_editShelf.id
          if (id) {
            // 修改
            api.editShelf(data).then(res => {
              that.msg(res)
              if (res.data.code === 0) {
                that.dialog_visible_editShelf = false
                that.getShelvesList()
              }
            })
          } else {
            // 新增
            api.newShelf(data).then(res => {
              that.msg(res)
              if (res.data.code === 0) {
                that.dialog_visible_editShelf = false
                that.getShelvesList()
              }
            })
          }

        } else {
          that.$message({
            type: 'error',
            message: '验证失败，请检查'
          })
        }
      });
    },
    step1_deleteShelf (id) {
      let that = this
      this.$messageBox.confirm(
          '确定删除这行数据吗？',
          '！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
      ).then(() => {
        api.deleteShelf(id).then(res => {
          if (res.data.code === 0) {
            that.getShelvesList()
          }
          that.msg(res)
        })
      })
    },
    step2_editShelfOfWh (item) {
      this.dialog_visible_editShelfOfWh = true
      // 额外使用一个对象承载要编辑的内容
      this.form_editShelfOfWh.warehouse = item
      // 获取所有的货架
      api.shelvesListOfWh(item.id)
          .then(res => {
            let data = res.data
            let newData = {}
            console.log(data)
            // 如果一次都没有录入，那么可编辑的列表需要从初始的仓库列表获取，并且改为后台返回某仓库列表的结构
            if (data.length > 0) {
              data.forEach(it => {
                it.setedNum = it.amount || '' // 页面显示的0改为空
                it.canChooseNum = it.shelfBase.amount - it.shelfBase.usedAmount + it.amount
              })
              newData = data
            } else {
              let shelvesList = this.shelvesList
              newData = shelvesList.map(it => {
                return {
                  id: '',
                  amount: '',
                  setedNum: '',
                  canChooseNum: it.amount - it.usedAmount,
                  shelfBase: it
                }
              })
            }
            this.form_editShelfOfWh.step2_shelvesList = newData
          })
    },
    step2_submit_editShelfOfWh () {
      let id = this.form_editShelfOfWh.warehouse.id
      let chooseShelvesList = this.form_editShelfOfWh.step2_shelvesList
      let changedLength = 0
      let overShelf = []
      let chooseShelvesData = chooseShelvesList.map(item => {
        let data = {
          shelf: item.shelfBase.id,
          amount: item.setedNum || 0
        }
        if (item.id) {
          data.id = item.id
        }
        if (data.amount !== 0) {
          changedLength++
        }
        if (data.amount > item.canChooseNum) {
          overShelf.push(item.shelfBase.shelfName)
        }
        return data
      })
      if (overShelf.length > 0) {
        this.$message({
          type: 'error',
          message: overShelf.join('、') + '超出可选数量'
        })
        return false
      }
      if (changedLength === 0) {
        this.$messageBox.alert(
            '操作失败！仓库里至少要有一个货架！',
            '！提示',
            {
              confirmButtonText: '知道了',
              type: 'warning',
            })
        return false
      }
      let data = {
        id: id,
        shelfNum: this.form_editShelfOfWh.shelfNum,
        shelfCategory: chooseShelvesData.length,
        iwss: JSON.stringify(chooseShelvesData)
      }
      let that = this
      api.changeWh_editBase(data)
          .then(res => {
            if (res.data.code === 0) {
              that.dialog_visible_editShelfOfWh = false
              that.getWarehouseList()
              that.getShelvesList()
            }
            that.msg(res)
          })
    },
    step2_resetWhName (item) {
      this.form_editShelfOfWh.warehouse = item
      this.form_editShelfOfWh.warehouse.newWarehouseName = ''
      this.dialog_visible_resetWhName = true
    },
    step2_resetWhCode (item) {
      this.dialog_visible_resetWhCode = true
      this.form_editShelfOfWh.warehouse = item
      this.form_editShelfOfWh.warehouse.newWarehouseCode = ''
    },
    step2_submit_resetWhName () {
      let data = {
        id: this.form_editShelfOfWh.warehouse.id,
        warehouseName: this.form_editShelfOfWh.warehouse.newWarehouseName
      }
      if (data.warehouseName === '') {
        this.$message({
          type: 'error',
          message: '请录入仓库名称！'
        })
        return false
      }
      let that = this
      api.changeWarehouseBase(data)
          .then(res => {
            if (res.data.code === 0) {
              that.dialog_visible_resetWhName = false
              that.getWarehouseList()
            }
            that.msg(res)
          })
    },
    step2_submit_resetWhCode () {
      let data = {
        id: this.form_editShelfOfWh.warehouse.id,
        warehouseCode: this.form_editShelfOfWh.warehouse.newWarehouseCode
      }
      if (data.warehouseCode === '') {
        this.$message({
          type: 'error',
          message: '请录入仓库代号！'
        })
        return false
      }
      let that = this
      api.changeWarehouseBase(data)
          .then(res => {
            if (res.data.code === 0) {
              that.dialog_visible_resetWhCode = false
              that.getWarehouseList()
            }
            that.msg(res)
          })
    },
    step3_setShelfNo (item) {
      this.dialog_visible_setShelfNo = true
      // 获取所有的货架
      this.form_editShelfOfWh.warehouse = item
      let that = this
      api.shelvesListOfWh(item.id)
          .then(res => {
            let data = res.data
            that.form_editShelfOfWh.step3_shelvesList = data.filter(item => item.amount !== 0)
            console.log('shelfAllArr', this.form_editShelfOfWh.step3_shelvesList)
          })
    },
    step3_setShelfNo_edit (item) {
      this.dialog_visible_setShelfNo_edit = true
      this.form_editShelfOfWh.step3_editShelf = {
        base: {},
        placeNum: this.placeNum,
        markType: '',
        limits: [{lowerLimit: '', upperLimit: ''}],
        minimumValue: '',
        isStartsInputSingle: false, // 是否开始录入录入不连续的货架号
        singleValues: [],
        visible_startInputSingle: false
      }
      this.form_editShelfOfWh.step3_editShelf.base = item
    },
    step3_uploadShelfImg (item) {
      this.dialog_visible_uploadShelfImg = true
      console.log(item)
      this.form_editImgOfWh.warehouse = item
      this.form_editImgOfWh.step3_imgList = JSON.parse(JSON.stringify(item.warehouseImages))
      this.form_editImgOfWh.step3_imgList.map(item => {
        item.url = auth.webRoot + '/upload/' + item.normalPath
        item.name = ''
      })
      console.log(item)
    },
    step3_shelfImgSuccess(response, file, fileList) {
      // 这里的response是服务器返回的数据
      // file是上传成功的文件对象
      // fileList是上传的文件列表
      response.url = auth.webRoot + '/upload/' + response.filename
      response.normalPath = response.filename
      fileList[fileList.length - 1] = response
      console.log('文件上传成功', fileList);
      // 在这里执行你需要的操作
    },
    step3_shelfImgPreview(file) {
      this.dialog_visible_imgShow = true
      this.step3_shelfImgUrl = file.url
    },
    step3_submit_uploadShelfImg () {
      let images = this.form_editImgOfWh.step3_imgList
      if (images.length === 0) {
        this.$message({
          type: 'error',
          message: '至少上传一个文件！'
        })
        return false
      }
      let imagesHandle = images.map(item => {
        return {
          warehouse: this.form_editImgOfWh.warehouse.id,
          normalPath: item.normalPath,
          thumbnailPath: ''
        }
      })
      let data = {
        warehouse: this.form_editImgOfWh.warehouse.id,
        images: JSON.stringify(imagesHandle)
      }
      let that = this
      api.submit_uploadShelfImg(data)
          .then(res => {
            if (res.data.code === 0) {
              that.dialog_visible_uploadShelfImg = false
              that.getWarehouseList()
            }
            that.msg(res)
          })
    },
    step3_seeShelfImg (item) {
      this.dialog_visible_seeShelfImg = true
      this.form_editImgOfWh.warehouse = item
      this.form_editImgOfWh.warehouse.prewUrls = item.warehouseImages.map(it => this.realPath(it.normalPath))
      console.log(this.form_editImgOfWh.warehouse)
    },
    step3_setShelfNo_edit_addRow () {
      this.resetSingleValues()
      this.form_editShelfOfWh.step3_editShelf.limits.push({lowerLimit: '', upperLimit: ''})
    },
    step3_setShelfNo_edit_delRow (index) {
      this.resetSingleValues()
      this.form_editShelfOfWh.step3_editShelf.limits.splice(index, 1)
    },
    step3_setShelfNo_edit_submit_checkbox () {
      if (!this.form_editShelfOfWh.step3_editShelf.isStartsInputSingle) {
        this.$message({
          type: 'error',
          message: '请勾选！'
        })
        return false
      }
      // 处理数据
      let editShelf = this.form_editShelfOfWh.step3_editShelf
      let limits = editShelf.limits
      console.log('editShelf', editShelf)
      let selfAmount = editShelf.base.amount
      let isNull = 0
      limits.forEach(item => {
        if (item.lowerLimit === '' || item.upperLimit === '') {
          isNull++
        }
      })
      if (isNull > 0) {
        this.$message({
          type: 'error',
          message: '您还有输入框未录入'
        })
      } else {
        let inputSumAmount = 0
        limits.forEach(item => {
          inputSumAmount += (item.upperLimit - item.lowerLimit + 1)
        })
        let limitsArray = limits.map(item => {
          return [Number(item.lowerLimit), Number(item.upperLimit)]
        })
        console.log('limitsArray', limitsArray)
        let overlapping = this.hasOverlap(limitsArray)
        if (overlapping) {
          this.$message({
            type: 'error',
            message: '区间范围有重叠，请检查'
          })
        } else {
          console.log('inputSumAmount And selfAmount', inputSumAmount + '/' + selfAmount)
          if (inputSumAmount > selfAmount) {
            this.$message({
              type: 'error',
              message: '超出数量上限，请检查！'
            })
          } else {
            this.form_editShelfOfWh.step3_editShelf.visible_startInputSingle = true
            let restAmount = selfAmount - inputSumAmount
            editShelf.singleValues = Array(restAmount).fill('')
          }
        }
      }
    },
    step3_submit_setShelfNo_edit () {
      let step3_editShelf = this.form_editShelfOfWh.step3_editShelf
      console.log(step3_editShelf)
      let editShelf = JSON.parse(JSON.stringify(step3_editShelf))


      let data = {
        warehouseShelf: editShelf.base.id,
        placeNum: editShelf.placeNum,
        minimumValue: editShelf.minimumValue,
      }
      if (data.minimumValue) {
        data.markType = 1
        data.limits = ''
        data.singleValues = ''
      } else {
        let state = 0
        for(let item of editShelf.limits) {
          if (!item.lowerLimit || !item.upperLimit) {
            state++
          }
        }
        if (state > 0) {
          this.$message({
            type: 'error',
            message: '请至少选择一种方式录入正确的数据！'
          })
          return false
        }
        let startInputSingle= this.form_editShelfOfWh.step3_editShelf.visible_startInputSingle
        if (!startInputSingle) {
          this.$message({
            type: 'error',
            message: '请检查是否开始录入单个货架号！'
          })
          return false
        }
        let hasEmptyData = editShelf.singleValues.some(item => !item);
        if (hasEmptyData) {
          this.$message({
            type: 'error',
            message: '还有单个的货架没有录入货架号！'
          })
          return false
        }
        let isIn = this.areNumbersInIntervals(editShelf.singleValues, editShelf.limits)
        let isRepeat = new Set(editShelf.singleValues).size !== editShelf.singleValues.length;
        if (isRepeat) {
          this.$message({
            type: 'error',
            message: '录入的单个货架号之间有重复数据'
          })
          return false
        }
        if (!isIn) {
          this.$message({
            type: 'error',
            message: '录入的数值与区间重叠，请检查'
          })
          return false
        }
        data.markType = 2
        data.limits = JSON.stringify(editShelf.limits)
        data.singleValues = editShelf.singleValues.join(',')
      }
      console.log(data)
      let that = this
      api.submit_setShelfNo_edit(data)
          .then(res => {
            if (res.data.code === 0) {
              that.dialog_visible_setShelfNo_edit = false
              that.getWarehouseList()
              api.shelvesListOfWh(that.form_editShelfOfWh.warehouse.id)
                  .then(res => {
                    let data = res.data
                    that.form_editShelfOfWh.step3_shelvesList = data
                  })
              that.placeNum = data.placeNum
              that.isPlaceNumFixed = true
            }
            that.msg(res)
          })
    },
    step4_setLabel (data) {
      let dialogName = 'dialog_visible_' + data
      this[dialogName] = true
      this.form_editLabel.labelInfo = JSON.parse(JSON.stringify(this.form_label))
      this.form_editLabel.editContentName = data
      console.log('editContentName', this.form_editLabel.editContentName)

    },
    step4_submit_setLabel () {
      let editContentName = this.form_editLabel.editContentName
      let dialogName = 'dialog_visible_' + editContentName
      let data = {
        id: this.form_editLabel.labelInfo.id
      }
      switch (editContentName) {
        case 'setArrowStyle':
          data.arrowStyle = this.form_editLabel.labelInfo.arrowStyle
          break;
        case 'setWhCode':
          data.codeShowable = this.form_editLabel.labelInfo.codeShowable
          data.codePosition = this.form_editLabel.labelInfo.codePosition
          break;
        case 'setQrCodePosition':
          data.qrPosition = this.form_editLabel.labelInfo.qrPosition
          break;
        case 'setShelfLabelSize':
          data.labelLengh = this.form_editLabel.labelInfo.labelLengh
          data.labelWidth = this.form_editLabel.labelInfo.labelWidth
          break;
      }
      if (editContentName === 'setShelfLabelSize') {
        if (data.labelLengh === '') {
          this.$message({
            type: 'error',
            message: '请录入库位标签的长！'
          })
          return false
        }
        if (data.labelWidth === '') {
          this.$message({
            type: 'error',
            message: '请录入库位标签的宽！'
          })
          return false
        }
      }
      let that = this
      api.editLabelOfWh(data)
          .then(res => {
            if (res.data.code === 0) {
              that[dialogName] = false
              that.getArrowSet()
            }
            that.msg(res)
          })
    },
    step4_setArrowStyle () {
      this.form_editLabel.arrowStyle = 1
      this.form_editLabel.editContentName = 'setArrowStyle'
      this.form_editLabel.labelInfo = JSON.parse(JSON.stringify(this.form_label))
      this.dialog_visible_setArrowStyle = true
    },
    step4_editLocationsArrowOfWh(item) {
      this.dialog_visible_editArrowOfLocation = true
      this.form_editLabel.form_editArrowOfLocation.whInfo = item
      this.form_editLabel.form_editArrowOfLocation.shelvesList = []
      let data = {
        warehouse: item.id
      }
      api.locationOfWh(data)
          .then(res => {
            let data = res.data
            data.map(item => {
              item.isArrowChanged = false
            })
            const groupedArray = data.reduce((acc, current) => {
              const key = current.shelfLocationCode.split('-')[0];
              if (!acc[key]) acc[key] = [];
              acc[key].push(current);
              return acc;
            }, {});
            console.log('groupedArray', groupedArray)
            this.form_editLabel.form_editArrowOfLocation.shelvesList = groupedArray
          })
    },
    step4_submit_editLocationsArrowOfWh () {
      let shelvesList = this.form_editLabel.form_editArrowOfLocation.shelvesList
      const shelves = Object.keys(shelvesList).reduce((acc, key) => {
        return acc.concat(shelvesList[key]);
      }, []);
      let changed = shelves.filter(item => item.isArrowChanged)
      let ids = changed.map(item => {
        return item.id
      })
      if (ids.length === 0) {
        this.$message({
          type: 'error',
          message: '请至少调整一项！'
        })
        return false
      }
      let data = {
        ids: ids.join(',')
      }
      let that = this
      api.submit_locationOfWh_edit(data)
          .then(res => {
            if (res.data.code === 0) {
              that.dialog_visible_editArrowOfLocation = false
              that.getWarehouseList()
            }
            that.msg(res)
          })
    },
    step4_changeArrow (item) {
      item.isArrowChanged = !item.isArrowChanged
      item.tIwsLocationTags.arrowDirection = item.tIwsLocationTags.arrowDirection === 1?2:1
    },
    step4_changeAllArrow () {
      let shelvesList = this.form_editLabel.form_editArrowOfLocation.shelvesList
      const shelves = Object.keys(shelvesList).reduce((acc, key) => {
        return acc.concat(shelvesList[key]);
      }, []);
      shelves.map(item => {
        item.isArrowChanged = !item.isArrowChanged
        item.tIwsLocationTags.arrowDirection = item.tIwsLocationTags.arrowDirection === 1?2:1
      })
    },
    step4_resetWhCode_all () {
      this.dialog_visible_resetWhCode_all = true
    },
    step4_resetWhCode (item) {
      this.dialog_visible_resetWhCode = true
      this.form_editShelfOfWh.warehouse = item
      this.form_editShelfOfWh.warehouse.newWarehouseCode = ''
    },
    step5_addShelf () {
      this.dialog_visible_addShelf = true
      this.$nextTick(() => {
        this.$refs['addShelfForm'].resetFields()
      })
      this.form_checkAndCorrect.step5_editShelf.addShelf.visible_inputShelfNo = false
    },
    step5_submit_addShelf () {
      let that = this
      this.$refs['addShelfForm'].validate((valid) => {
        if (valid) {
          let chooseWh = this.form_checkAndCorrect.step5_editShelf.addShelf.chooseWh
          let chooseShelf = this.form_checkAndCorrect.step5_editShelf.addShelf.chooseShelf
          let addShelfNoList = this.form_checkAndCorrect.step5_editShelf.addShelf.addShelfNo
          let amount = this.form_checkAndCorrect.step5_editShelf.addShelf.amount
          let data = {
            warehouse: chooseWh,
            shelfId: chooseShelf,
            singleValues: addShelfNoList.join(','),
            amount: amount
          }
          let that = this
          api.addShelfCodes(data)
              .then(res => {
                that.msg(res)
                if (res.data.code === 0) {
                  that.dialog_visible_addShelf = false
                  that.getWarehouseList()
                  that.countByShelf()
                }
              })

        } else {
          that.$message({
            type: 'error',
            message: '验证失败，请检查'
          })
        }
      });

    },
    step5_delShelf () {
      this.dialog_visible_delShelf = true
      this.$nextTick(() => {
        this.$refs['delShelfForm'].resetFields()
      })
    },
    step5_submit_delShelf () {
      let that = this
      this.$refs['delShelfForm'].validate((valid) => {
        if (valid) {
          let chooseWh = this.form_checkAndCorrect.step5_editShelf.delShelf.chooseWh
          let chooseShelf = this.form_checkAndCorrect.step5_editShelf.delShelf.chooseShelf
          let delShelfNoList = this.form_checkAndCorrect.step5_editShelf.delShelf.delShelfNoList
          let data = {
            warehouse: chooseWh,
            shelf: chooseShelf,
            codes: delShelfNoList.join(',')
          }
          let _this = this
          api.deleteShelfCodes(data)
              .then(res => {
                _this.msg(res)
                if (res.data.code === 0) {
                  _this.dialog_visible_delShelf = false
                  _this.getWarehouseList()
                  _this.countByShelf()
                }
              })

        } else {
          that.$message({
            type: 'error',
            message: '验证失败，请检查'
          })
        }
      });

    },
    step5_otherChange () {
      this.dialog_visible_otherChange = true
      this.form_otherChanges.changeType = 0
      this.form_otherChanges.labelChangeType = 0
    },
    step5_submit_otherChange () {
      let changeType = this.form_otherChanges.changeType
      let labelChangeType = this.form_otherChanges.labelChangeType
      console.log(changeType)
      console.log(labelChangeType)
      if (!changeType || (changeType === 4 && !labelChangeType)) {
        this.$message({
          type: 'error',
          message: '请勾选！'
        })
        return false
      }
      let firstLevelArr = ['changeShelfNo', 'changeWhName', 'changeWhImage', 'changeLabel']
      let secondLevelArr = ['arrow', 'whCode', 'qr', 'size']
      let suffix = 'otherChange_' + firstLevelArr[changeType-1] + (changeType === 4?('_' + secondLevelArr[labelChangeType-1]):'')
      let functionName = 'step5_' + suffix
      let dialogName = 'dialog_visible_' + suffix
      console.log(functionName)
      if (this[functionName]) this[functionName]()
      this[dialogName] = true
      this.dialog_visible_otherChange = false
    },
    step5_otherChange_changeShelfNo () {
      this.$nextTick(() => {
        this.$refs['changeShelfNoForm'].resetFields()
      })
      api.shelfNoList()
          .then(res => {
            let data = res.data
            data.map(item => {
              item.oldShelfCode = item.shelfCode
            })
            this.form_otherChanges.step5_editShelfNo.shelvesList = res.data
          })
    },
    step5_submit_otherChange_changeShelfNo () {
      this.$refs['changeShelfNoForm'].validate((valid) => {
        if (valid) {
          // 验证通过
          let shelvesList = this.form_otherChanges.step5_editShelfNo.shelvesList
          let changed = shelvesList.filter(item => item.changed)
          if (changed.length === 0) {
            this.$message({
              type: 'error',
              message: '请修改后提交！'
            })
            return false
          }
          let dataArr = changed.map(item => {
            return {
              oldShelfCode: item.oldShelfCode,
              newShelfCode: item.shelfCode,
              warehouse: item.warehouse
            }
          })
          let data = {
            data: JSON.stringify(dataArr)
          }
          let that = this
          api.submit_changeShelfNo(data)
              .then(res => {
                if (res.data.code === 0) {
                  that.dialog_visible_otherChange_changeShelfNo = false
                }
                that.msg(res)
              })

        } else {
          this.$message({
            type: 'error',
            message: '验证失败，请检查'
          })
        }
      });

    },
    step5_otherChange_changeLabel_arrow () {
      this.step4_setArrowStyle()
    },
    step5_otherChange_changeLabel_whCode () {
      this.step4_resetWhCode_all()
    },
    step5_otherChange_changeLabel_qr () {
      this.step4_setLabel('setQrCodePosition')
    },
    step5_otherChange_changeLabel_size () {
      this.step4_setLabel('setShelfLabelSize')
    },
    step5_clearAll () {
      this.dialog_visible_clearAll = true
    },
    step5_submit_clearAll () {
      let that = this
      api.clearAllData()
          .then(res => {
            this.msg(res)
            let code = res.data.code
            if (code === 0) {
              that.step5_clearStep()
            }
          })
    },
    step5_clearStep () {
      let that = this
      api.clearStep({ 'code': 'shelves'})
          .then(res => {
            that.$router.go(0)
          })
    },
    step5_seeShelfNo(item) {
      this.dialog_visible_seeShelfNo = true
      this.form_checkAndCorrect.step5_editShelf.warehouse = item
      let data = {
        warehouse: item.id
      }
      api.shelfNoList(data)
          .then(res => {
            let data = res.data
            console.log(data)
            this.form_checkAndCorrect.step5_editShelf.shelfNoListOfWh = data
          })
    },
    step6_exportHandle (item) {
      this.dialog_visible_shelvesLabelSeeAndExport = true
      this.form_export.step6_edit.whInfo = item
      let data = {
        warehouse: item.id
      }
      api.locationOfWh(data)
          .then(res => {
            let data = res.data
            console.log(data)
            data.map(item => {
              item.isArrowChanged = false
            })
            const groupedArray = data.reduce((acc, current) => {
              const key = current.shelfLocationCode.split('-')[0];
              if (!acc[key]) acc[key] = [];
              acc[key].push(current);
              return acc;
            }, {});
            console.log('groupedArray', groupedArray)
            this.form_export.step6_edit.locationList = groupedArray
            this.form_export.step6_edit.locationLength = data.length
          })
    },
    step6_allGiveUp () {
      let shelvesList = this.form_export.step6_edit.locationList
      const shelves = Object.keys(shelvesList).reduce((acc, key) => {
        return acc.concat(shelvesList[key]);
      }, []);
      shelves.map(item => {
        item.isSelected = false
      })
    },
    step6_allChoose () {
      let shelvesList = this.form_export.step6_edit.locationList
      const shelves = Object.keys(shelvesList).reduce((acc, key) => {
        return acc.concat(shelvesList[key]);
      }, []);
      shelves.map(item => {
        item.isSelected = true
      })
    },
    step6_changeSelect (it) {
      it.isSelected = !it.isSelected
    },
    load_shelves (value) {
      this.form_checkAndCorrect.option_shelves = []
      this.form_checkAndCorrect.step5_editShelf.delShelf.chooseShelf = ''
      api.shelvesListOfWh(value)
          .then(res => {
            let data = res.data
            let options = data.map(item => {
              return {
                label: item.shelfBase.shelfCode + '/' + item.shelfBase.shelfName,
                value: item.shelfBase.id
              }
            })
            this.form_checkAndCorrect.option_shelves = options
          })
      console.log(value)
    },
    load_locations () {
      let chooseWh = this.form_checkAndCorrect.step5_editShelf.delShelf.chooseWh
      let chooseShelf = this.form_checkAndCorrect.step5_editShelf.delShelf.chooseShelf
      let data = {
        shelfId: chooseShelf,
        warehouse: chooseWh
      }
      api.shelfNoList(data)
          .then(res => {
            this.form_checkAndCorrect.step5_editShelf.delShelf.canDelShelfNoList = res.data
            // this.form_checkAndCorrect.step5_editShelf.delShelf.delShelfNoList = []
            this.$refs['delShelfForm'].clearValidate()
            console.log(this.form_checkAndCorrect.step5_editShelf.delShelf.delShelfNoList)
          })
    },
    validateNumber (event) {
      const regex = /^[0-9]*$/;
      if (!regex.test(event.target.value)) {
        // 如果输入值不是正整数，则将其设置为上一个有效值
        event.target.value = event.target.value.substring(0, event.target.value.length - 1);
      }
    },
    hasOverlap(intervals) {
      if (intervals.length < 2) return false; // 如果区间少于2个，则不可能重叠
      // 对区间按起始值排序
      intervals.sort((a, b) => a[0] - b[0]);
      // 记录上一个区间的结束位置
      let end = intervals[0][1];
      for (let i = 1; i < intervals.length; i++) {
        // 如果当前区间的起始位置小于等于上一个区间的结束位置，则有重叠
        if (intervals[i][0] <= end) {
          return true; // 存在重叠，返回true
        }
        // 更新end为当前区间的结束位置
        end = intervals[i][1];
      }
      return false; // 不存在重叠，返回false
    },
    areNumbersInIntervals(numbers, intervals) {
      let intervalsArr = intervals.map(item => [Number(item.lowerLimit), Number(item.upperLimit)] )
      let numbersArr = numbers.map(Number)
      return numbersArr.every(number => {
        return intervalsArr.some(([min, max]) => number < min || number > max);
      });
    },
    getSummaries (param) {
      const { columns, data } = param;
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
        } else if (index === 4) {
          let shelvesList = this.form_editShelfOfWh.step2_shelvesList
          let count = 0
          shelvesList.forEach(item => {
            if (item.setedNum) {
              count += Number(item.setedNum)
            }
          })
          sums[index] = count;
          this.form_editShelfOfWh.shelfNum = count
        } else {
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = '';
          }
        }
      });

      return sums;
    },
    change_checkbox_isStartsInputSingle () {
      if (!this.form_editShelfOfWh.step3_editShelf.isStartsInputSingle) {
        this.resetSingleValues()
      }
    },
    input_shelfNo (type) {
      console.log(type)
      if (type === 2) {
        this.form_editShelfOfWh.step3_editShelf.minimumValue = ''
      } else {
        this.form_editShelfOfWh.step3_editShelf.limits = [{lowerLimit: '', upperLimit: ''}]
      }
      this.resetSingleValues()
    },
    blur_createShelfNoInput () {
      let num = this.form_checkAndCorrect.step5_editShelf.addShelf.amount
      if (num !== '') {
        if (num < 101) {
          console.log('num', num)
          this.form_checkAndCorrect.step5_editShelf.addShelf.addShelfNo = Array(Number(num)).fill('')
          this.form_checkAndCorrect.step5_editShelf.addShelf.visible_inputShelfNo = true
        } else {
          this.$message({
            type: 'error',
            message: '最大限制100！'
          })
        }
      }
    },
    change_createShelfNoInput () {
      this.form_checkAndCorrect.step5_editShelf.addShelf.addShelfNo = []
      this.form_checkAndCorrect.step5_editShelf.addShelf.visible_inputShelfNo = false
    },
    resetSingleValues() {
      this.form_editShelfOfWh.step3_editShelf.isStartsInputSingle = false
      this.form_editShelfOfWh.step3_editShelf.visible_startInputSingle = false
      this.form_editShelfOfWh.step3_editShelf.singleValues = []
    },
    filter_size (item) {
      let arr = ['m', 'cm', 'mm']
      let unit = arr[Number(item.unit) - 1]
      return item.length + unit + '*' + item.width + unit + '*' + item.height + unit
    },
    filter_orangeLength(step) {
      let shelvesList = []
      if (step === 4) {
        shelvesList = this.form_editLabel.form_editArrowOfLocation.shelvesList
      } else if (step === 6) {
        shelvesList = this.form_export.step6_edit.locationList
      }
      const shelves = Object.keys(shelvesList).reduce((acc, key) => {
        return acc.concat(shelvesList[key]);
      }, []);
      let orange = []
      if (step === 4) {
        orange = shelves.filter(item => item.isArrowChanged)
      } else if (step === 6) {
        orange = shelves.filter(item => item.isSelected)
      }
      return orange.length
    },
    nextStep () {
      let page = this.page
      if (page === 1) {
        if (this.shelvesList.length === 0) {
          this.$messageBox.alert(
              '操作失败！请至少录入一种货架！',
              '！提示',
              {
                confirmButtonText: '知道了',
                type: 'warning',
              })
          return false
        }
      }
      if (page === 2) {
        let hasShelfWh = this.whList.filter(item => item.shelfNum > 0)
        let hasShelf = hasShelfWh.length > 0
        if (!hasShelf) {
          this.$messageBox.alert(
              '操作失败！请至少在一个仓库里设置货架！',
              '！提示',
              {
                confirmButtonText: '知道了',
                type: 'warning',
              })
          return false
        }
        let restAmount = 0
        let allAmount = 0
        for(let item of this.shelvesList) {
          let amount = item.amount
          let usedAmount = item.usedAmount || 0
          let rest = amount - usedAmount
          restAmount += rest
          allAmount += amount
        }
        if (!(restAmount === 0)) {
          this.$messageBox.confirm(
              `各种型式货架共${allAmount}个，还有${restAmount}个未安置。<br>确定将纳入下一步吗？`,
              '！！提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }
          )
              .then(() => {
                api.saveStep({id: this.stepList[this.page - 1].id})
                    .then(res => {
                      let success = res.data.success
                      if (success === 1) {
                        ++this.page
                      } else {
                        this.$message({
                          type: 'error',
                          message: '保存步骤失败！'
                        })
                      }
                    })
              })
          return false
        }
      }
      if (page === 4) {
        let labelLength = this.form_label.labelLengh
        let labelWidth = this.form_label.labelWidth
        if (labelLength === '' || labelWidth === '') {
          this.$messageBox.alert(
              '操作失败！库位标签的尺寸必须设置！',
              '！提示',
              {
                confirmButtonText: '知道了',
                type: 'warning',
              })
          return false
        }
      }
      if (page === 6) {
        if (!this.radios.step6Ok) {
          this.$message({
            type: 'error',
            message: '请勾选！'
          })
          return false
        }
      }
      if (page === 7) {
        if (!this.radio.step7Ok) {
          this.$message({
            type: 'error',
            message: '请勾选！'
          })
          return false
        }
      }
      let data = {
        id: this.stepList[this.page - 1].id
      }
      console.log(data)
      api.saveStep(data)
          .then(res => {
            let success = res.data.success
            if (success === 1) {
              ++this.page
              if (this.page === 5) {
                api.countShelfNumAndLocationNum()
                    .then(res1 => {
                      let data = res1.data.data
                      this.form_checkAndCorrect.shelfCateNum = data.cateNum
                      this.form_checkAndCorrect.shelfNum = data.amount
                    })
              }
            } else {
              this.$message({
                type: 'error',
                message: '保存步骤失败！'
              })
            }
          })
    },
    prevStep () {
      --this.page
    },
    confirm () {
      this.$messageBox.confirm(
          '确定提交吗',
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      )
          .then(() => {
            let data = {
              id: this.stepList[this.page - 1].id
            }
            let that = this
            api.saveStep(data)
                .then(res => {
                  let success = res.data.success
                  if (success === 1) {
                    that.$router.go(0)
                  } else {
                    this.$message({
                      type: 'error',
                      message: '保存步骤失败！'
                    })
                  }
                })
          })
    },
    msg(res) {
      let code = res.data.code
      let msg = res.data.msg
      let type = ''
      switch (code) {
        case 0:
          type = 'success'
          break
        case 301:
          type = 'warning'
          break
        case 500:
          type = 'error'
          break
      }
      this.$message({
        type: type,
        message: msg,
        plain: true
      })
    },
    realPath (path) {
      return auth.webRoot + '/upload/' + path
    },
    blur_judgePlaceNum(event) {
      let target = event.target.value
      if (target.length > this.placeNum) {
        this.$message({
          type: 'error',
          message: '超出最大位数！'
        })
        event.target.value = ''
      } else {
        if (target !== '') {
          event.target.value = target.padStart(this.placeNum, '0')
        }
      }
    },
    blur_limits(event, item) {
      let target = event.target.value
      if (target.length > this.placeNum) {
        this.$message({
          type: 'error',
          message: '超出最大位数！'
        })
        event.target.value = ''
      } else {
        this.form_editShelfOfWh.step3_editShelf.singleValues = []
        let l = item.lowerLimit
        let u = item.upperLimit
        if (l !== '' && u !== '' && Number(l) >= Number(u)) {
          this.$message({
            type: 'error',
            message: '请录入正确的起止号码！'
          })
          event.target.value = ''
        } else {
          if (target !== '') {
            event.target.value = target.padStart(this.placeNum, '0')
          }
        }
      }
    }
  },
  directives: {
    // 在模板中启用 v-focus
    onlyInteger: (el) => {
      // 这会在 `mounted` 和 `updated` 时都调用
      let ele = el.querySelector('input')
      ele.addEventListener('input', () => {
        // 获取输入框的当前值
        let value = ele.value;
        // 使用正则表达式匹配非正整数的部分，并将其替换为空字符串
        value = value.replace(/[^\d]/g, '');
        // 如果值为0，则直接设置为空字符串，因为0不是正整数
        if (value === '0') {
          value = '';
        }
        // 更新输入框的值
        ele.value = value;
      });
    }
  }
}


</script>
