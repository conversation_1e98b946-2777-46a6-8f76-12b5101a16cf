<template>
  <div id="dailyAffairs">
    <TY_NavTop title="请假申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="bg">
        <div class="item">
          <div class="item_title">{{leaveDetails.createName}}</div>
          <div class="item_content"></div>
        </div>
        <div class="item">
          <div class="item_title">请假记录</div>
          <div class="item_content"></div>
        </div>
        <!--提前结束请假部分-->
        <div v-for="(item, index) in personnelLeaveItemList" v-bind:key="index">
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">提前结束请假</div>
              <div class="item_content"></div>
            </div>
            <div class="item">
              <div class="item_title">计划上班时间</div>
              <div class="item_content">{{item.actualEndTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">说明</div>
              <div class="item_content">{{item.actualReason}}</div>
            </div>
            <div v-if="is.approveStatus === '3'" v-for="(is, index) in item.processList" v-bind:key="index">
              <div class="item color-red">
                提前结束请假的申请被{{is.createName}}驳回！
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{is.reason}}</div>
              </div>
            </div>
          </div>
          <div class="process">
            <div>申请人 <span class="processName">{{item.createName}}</span> {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div v-if="it.approveStatus !== '1'" v-for="(it, index) in item.processList" v-bind:key="index">
              审批人 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}
            </div>
          </div>
        </div>
        <!--主要申请部分-->
        <div>
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">开始时间</div>
              <div class="item_content">{{leaveDetails.beginTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">结束时间</div>
              <div class="item_content">{{leaveDetails.endTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假类型</div>
              <div class="item_content">{{leaveDetails.leaveTypeName}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假事由</div>
              <div class="item_content">{{leaveDetails.reason}}</div>
            </div>
            <div v-if="td.approveStatus === '3'" v-for="(td, index) in processList" v-bind:key="index">
              <div class="item color-red">
                请假申请被{{td.userName}}驳回！
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{td.reason}}</div>
              </div>
            </div>
          </div>
          <div class="process">
            <div>申请人 <span class="processName">{{leaveDetails.createName}}</span> {{leaveDetails.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div v-if="ta.approveStatus !== '1'" v-for="(ta, index) in processList" v-bind:key="index">
              <div>审批人 <span class="processName">{{ta.userName}}</span> {{ta.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
        </div>
        <div class="handle_button" v-if="advanceLeaveBtn === 1">
          <button class="ui-btn ui-btn_info" @click="openDialog()">提前结束请假</button>
        </div>
        <div class="handle_button" v-if="currentProcess.approveStatus==='1' && currentProcess.level===1">
          <input type="submit" class="ui-btn ui-btn_info" value="撤销申请" @click="revokeApply()">
        </div>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="98%"
      :before-close="handleClose">
      <el-form :label-position="left" :rules="rules" label-width="110px" :model="leaveAhead" ref="leaveAhead">
        <el-form-item label="计划上班时间" prop="endDate">
          <el-date-picker
            type="date"
            placeholder="请选择日期"
            value-format="yyyy-MM-dd"
            v-model="leaveAhead.endDate"
            size="small"
            :picker-options="pickerOptions"
            @change="setTimeLine"
            style="width: 100%"></el-date-picker>
        </el-form-item>
        <el-form-item label=" " prop="endTime">
          <el-select v-model="leaveAhead.endTime" placeholder="请选择时间" size="small">
            <el-option
              v-for="item in timeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明">
          <el-input v-model="leaveAhead.actualReason" size="small"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value=" 取消" @click="dialogVisible = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="endLeaveAdvance">
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
  .bg{
    background: #fff;
    padding: 4px 16px;
  }
  .item{
    display: flex;
    padding: 4px 0;
    line-height: 18px;
    .item_title{
      width: 90px;
      flex: none;
      color: #666;
      line-height: 2rem;
      padding: 0;
    }
    .item_content{
      flex: auto;
      line-height: 2rem;
      text-align: left;
    }
  }
  .kj-panel{
    border-top:1px solid #ddd;
  }
  .process{
    font-size: 12px;
    color: #666;
    margin-top: 8px;
    margin-bottom: 8px;
    margin-left: 90px;
    .processName{
      display: inline-block;
      width: 52px;
      margin-left: 4px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
      text-align: left;
    }
  }
  .item .item_title.color-red{
    color: red;
  }
</style>
<script>
import auth from '@/sys/auth'
import * as moment from 'moment'
import 'moment/locale/zh-cn'
import { formatDate, gohistory } from '@/js/common'
export default {
  name: 'applyLeaveDetail',
  filters: { formatDate },
  data () {
    var vue = this
    return {
      dialogVisible: false,
      loading: false,
      leaveDetails: {},
      currentProcess: {},
      processList: [],
      personnelLeaveItemList: [],
      leaveAhead: {},
      timeOption: [],
      listenersUid: [],
      advanceLeaveBtn: '',
      rules: {
        endDate: [
          { required: true }
        ],
        endTime: [
          { required: true }
        ]
      },
      pickerOptions: {
        disabledDate (time) {
          let beginTime = moment(vue.leaveDetails.beginTime).format("YYYY-MM-DD")
          let endTime = moment(vue.leaveDetails.endTime).format("YYYY-MM-DD")
          let thisTime = moment(time).format("YYYY-MM-DD")
          return moment(thisTime).diff(moment(beginTime)) < 0 || moment(thisTime).diff(moment(endTime)) > 0
        }
      }
    }
  },
  created () {
    let that = this
    this.loading = true
    // this.tabValue = this.$route.params.tabValue || '1'
    this.getLeaveDetail()
    let leaveId = this.$route.params.leaveId
    this.listenersUid = [
      this.sphdSocket.subscribe('notice', function (data) {
        that.loading = false
        // 提前结束请假成功后notice通道会返回1状态
        let state = Number(data)
        if (state === 1) {
          gohistory(that)
        } else if (state === 2) {
          that.$kiko_message('请假时间已过，无法提交')
        } else if (state === 3) {
          that.$kiko_message('提前结束请假时间应在计划请假时间段内')
        } else if (state === 4) {
          that.$kiko_message('操作失败，因为该条申请已被审批')
          gohistory(that)
          // that.$router.push({
          //   path: '/applyLeave',
          //   name: 'applyLeave',
          //   params: {
          //     tabValue: that.tabValue || '2'
          //   }
          // })
          // that.$kiko_message('已提交申请，不能重复提交')
        }
      })
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.leaveAhead = {
      session: session,
      userId: userId,
      leaveId: leaveId
    }
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getLeaveDetail: function () {
      this.loading = true
      let that = this
      let params = {
        leaveId: this.$route.params.leaveId,
        session: ''
      }
      this.$http.post(auth.webRoot + '/leaveAndOutTime/leaveDetails.do', { json: JSON.stringify(params) }, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data
        console.log("response", reqData)
        let leaveDetails = reqData.personnelLeave // 计划请假详情
        let processList = reqData.processList // 计划请假的审批流程
        let personnelLeaveItemList = reqData.personnelLeaveItemList // 提前结束请假详情
        let processId = reqData.approvalProcessId
        that.advanceLeaveBtn = reqData.button
        that.leaveDetails = leaveDetails
        that.processList = processList
        that.personnelLeaveItemList = personnelLeaveItemList
        that.loading = false
        if(processId) {
          for(let precess of processList) {
            if(processId === precess.id) {
              console.log('leaveDetails.do0', processId, precess)
              that.currentProcess = precess
              return
            }
          }
          for(let item of personnelLeaveItemList) {
            for(let precess of item.processList) {
              if (processId === precess.id) {
                console.log('leaveDetails.do1', processId, precess)
                that.currentProcess = precess
                return
              }
            }
          }
        }
      }).catch((e) => {
        console.log('系统错误，请重试！', e)
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    jump: function (path) {
      this.$router.push({
        path: '/' + path,
        name: path
      })
    },
    openDialog: function () {
      this.dialogVisible = true
    },
    endLeaveAdvance: function () {
      let that = this
      this.leaveAhead.endTime1 = this.leaveAhead.endDate + ' ' + this.leaveAhead.endTime + ':00'
      console.log(JSON.stringify(this.leaveAhead))
      this.$refs['leaveAhead'].validate((valid) => {
        if (valid) {
          delete this.leaveAhead.endDate
          delete this.leaveAhead.endTime
          if (!this.leaveAhead.actualReason) {
            this.leaveAhead.actualReason = ''
          }
          this.sphdSocket.send('leaveAhead', this.leaveAhead)
          that.loading = true
          console.log('leaveAhead had touched', this.leaveAhead)
        } else {
          that.$kiko_message('请填必填项')
          return false
        }
      })
    },
    revokeApply: function () {
      this.$confirm('确定撤销本条申请吗?', '！提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let that = this
        // 逐级审批请假申请 progressiveApprovalLeave 所需参数：approvalProcessId, reason（原因，撤销不需要）, approvalStatus 1-批准 2-驳回 9-撤销
        let data = {
          approvalStatus: '9',
          approvalProcessId: this.currentProcess.id
        }
        that.sphdSocket.send('progressiveApprovalLeave', data)
        that.loading = true
        that.tabValue = 1
      })
    },
    setTimeLine: function (value) {
      console.log(value)
      // 获取考勤设置的起始时间和午休时间，并初始化各个选择框
      let data = {
        oid: this.sphdSocket.user.oid,
        attendanceDate: moment(value).format("YYYY-MM-DD"), // 请假日期
        userId: this.sphdSocket.user.userID
      }
      let that = this
      this.$http.post('../../../workAttendance/getWorkTime.do', data, {
        emulateJSON: true
      }).then((response) => {
        let data = response.body.data
        that.workTimeJson = data
        that.initTimeLine(data)

        that.loading = false
      }).catch( (e) => {
        console.log('系统错误，请重试1！', e)
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试1！'
        })
      })

    },
    initTimeLine: function (data) {
      let beginTime = data.beginTime
      let endTime = data.endTime
      let breakBegin = data.breakBegin
      let breakEnd = data.breakEnd
      let workOrNo = data.workOrNo
      let canChooseTimeArr = []
      if ( workOrNo === '2' ) {
        canChooseTimeArr = [beginTime + '-' + breakBegin, breakEnd + '-' + endTime]
      } else {
        canChooseTimeArr = ['00:00-23:30']
      }
      this.timeOption = this.getTimeLine(canChooseTimeArr)
    },
    // 获取时间范围数据（适用到下拉框）
    getTimeLine: function (range) {
      // range 示例 ['00:00-09:00', '10:00-11:00'] 需要显示的区间（剩下的禁用）
      if (!range) {
        range = ['00:00-23:30']
      }
      let data = []
      for (var i = 0; i <= 23; i++) {
        var textMM = i < 10 ? '0' + i : i
        data.push({value: textMM + ':' + '00', label: textMM + ':' + '00', disabled: true})
        if (i !== 24) {
          data.push({value: textMM + ':' + '30', label: textMM + ':' + '30', disabled: true})
        }
      }
      let that = this
      range.forEach((elem, index) => {
        let start = elem.split("-")[0]
        let end = elem.split("-")[1]

        let startNum = that.chargeToNum(start)
        let endNum = that.chargeToNum(end)
        for (let j = startNum; j <= endNum; j++) {
          data[j].disabled = false
        }
      })
      console.log('range', range)
      console.log('data', data)
      return data
    },
    // 时间转换位数字好计算
    chargeToNum: function (time) {
      let timeMM = Number(time.split(":")[0]);
      let timeSS = time.split(":")[1];
      timeSS = timeSS === '30'?  0.5: 0
      return Number((- ( -timeMM - timeSS)) * 2 )
    }
  }
}
</script>
