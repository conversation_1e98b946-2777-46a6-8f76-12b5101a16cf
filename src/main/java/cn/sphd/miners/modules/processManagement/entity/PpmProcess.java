package cn.sphd.miners.modules.processManagement.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by lyx on 2023/5/9
 * 生产_工序表  1.251工序1
 */
@Entity
@Table(name="t_ppm_process")
public class PpmProcess implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   //ID

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="category"   , nullable=true , unique=false)
    private Integer category;   //类别ID

    @Column(name="code"  , length=50 , nullable=true , unique=false)
    private String code;   //工序代号

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;   //工序名称

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;   //同级排序

    @Column(name="timing"  , length=1 , nullable=true , unique=false)
    private Byte timing;  //时间点:1-准备工作,2-正式操作,3-收尾工作

    @Column(name="kind"  , length=1 , nullable=true , unique=false)
    private Byte kind;  //操作类型:1-主要,2-辅助

    @Column(name="is_leaf")
    private Boolean isLeaf; //叶子节点标识:true-叶子,false-否(默认)

    @Column(name="is_cyclicity")
    private Boolean isCyclicity; //是否周期性:true-是,false-否(默认)

    @Column(name="enabled")
    private Boolean enabled; //启用标志:true-启用(默认),false-停用

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;   //创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;  //创建人

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;   //创建时间

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;   //修改人id

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;   //修改人

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;   //修改时间

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private Byte operation;  //操作:1-增,2-删,3-改,4-暂停,5-恢复

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Long previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    @Transient
    private String periodicity; //给前端拼接的“周期属性”字段

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Byte getTiming() {
        return timing;
    }

    public void setTiming(Byte timing) {
        this.timing = timing;
    }

    public Byte getKind() {
        return kind;
    }

    public void setKind(Byte kind) {
        this.kind = kind;
    }

    public Boolean getIsCyclicity() { return isCyclicity; }

    public void setIsCyclicity(Boolean isCyclicity) { this.isCyclicity = isCyclicity; }

    public String getPeriodicity() {
        return periodicity;
    }

    public void setPeriodicity(String periodicity) {
        this.periodicity = periodicity;
    }
}
