<template>
  <div id="backDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="我的消息" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="lHead">
            <p class="line-s">{{customer}}</p>
            <p class="listCon line-s">本次回款 {{detail.method | incomeType}} {{detail.amount}}元</p>
          </div>
          <div class="sectCenter">
            <div class="checkWay" v-if="detail.method === '1'">
              <div class="line-p">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="detail.method === '3'">
              <div class="line-p">
                <span class="tt-cl">收到支票日期</span>
                <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">支票号</span>
                <span class="sub-cl">{{detail.returnNo}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">支票到期日</span>
                <span class="sub-cl">{{detail.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具支票单位</span>
                <span class="sub-cl">{{detail.originalCorp}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具支票银行</span>
                <span class="sub-cl">{{detail.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="detail.method === '4'">
              <div class="line-p">
                <span class="tt-cl">收到汇票日期</span>
                <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">汇票号</span>
                <span class="sub-cl">{{detail.returnNo}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">汇票到期日</span>
                <span class="sub-cl">{{detail.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">原始出具汇票单位</span>
                <span class="sub-cl ellipsisSet">{{detail.originalCorp}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具汇票银行</span>
                <span class="sub-cl ellipsisSet">{{detail.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="detail.method === '5'">
              <div class="line-s">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{detail.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">收款银行</span>
                <span class="sub-cl">{{detail.bankName}}</span>
              </div>
            </div>
            <p class="line-p inputerInfo">录入者：{{detail.createName}} {{detail.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
            <p class="line-p inputerInfo">财 &nbsp;&nbsp;务：{{detail.financerName}} {{detail.approvalDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { incomeType, formatDate } from '../../js/common'
export default {
  name: 'payBackMsg',
  filters: {
    incomeType,
    formatDate
  },
  data () {
    return {
      detail: {},
      customer: '',
      loading: false,
      agreeForm: {
        type: false
      },
      listenersUid: []
    }
  },
  computed: {
  },
  created () {
    let _this = this
    let dlId = _this.$route.params.id
    _this.listenersUid = [
      _this.sphdSocket.subscribe('getSlCollectDetail', function (data) {
        let resDetail = JSON.parse(data)
        _this.customer = resDetail.customerName
        _this.detail = resDetail.slCollectApplication
      })
    ]
    _this.sphdSocket.send('getSlCollectDetail', { 'collectId': dlId, 'session': _this.sphdSocket.sessionid })
  },
  destroyed: function () {
    let _this = this
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
  }
}
</script>
