<template>
  <div id="discussionBorrowApply">
    <TY_NavTop title="讨论组阅览申请" isBackHome="false" isSearch="true" @toggleList="screen"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待审批" name="1" closable='false'>
        <div class="tip">您已提交下列讨论组的阅览申请，有待审批。</div>
        <div class="ui-cells discussBorrow">
          <a class="ui-cell" v-on:click="jump(item.id, 'borrowApply1')" v-for="(item, index) in listApplyDiscuss" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div><span class="participantsNum">{{item.participantsNum}}人</span>{{item.title}}</div>
                <div class="create">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="近期可阅览的讨论组" name="2" closable='false' :msgCount="unreadForumNum">
        <div class="tip">您已获得以下讨论组的阅览权限！</div>
        <div class="ui-cells discussBorrow">
          <a class="ui-cell" v-on:click="seeDiscuss(item)" v-for="(item, index) in listApproveDiscuss" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div><span class="participantsNum">{{item.participantsNum}}人</span>{{item.title}}</div>
                <div class="create">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<style lang="less">
  #discussionBorrowApply {
    .tabCount{
      position: absolute;
      left: calc(50% + 50px)
    }
  }
  .discussBorrow .participantsNum{
    display: inline-block;
    width: 30px;
    height: 16px;
    vertical-align: top;
    margin-top: 2px;
    position: relative;
    text-align: center;
    background-color: #5badff;
    color: #fff;
    font-size: 12px;
    border-radius: 1px;
    margin-right: 6px;
    line-height:16px;
  }
  .discussBorrow .create{
    margin-left: 36px;
    font-size: 12px;
    color: rgb(151, 161, 172);
  }
</style>

<script>
import * as moment from 'moment'
import 'moment/locale/zh-cn'
export default {
  name: 'discussionBorrowApply',
  data: function () {
    return {
      loading: true,
      listApplyDiscuss: [],
      listApproveDiscuss: [],
      listenersUid: [],
      unreadForumNum: 0
    }
  },
  created: function () {
    let that = this
    let userId = that.sphdSocket.user.userID
    this.$http.post('../../../read/getApplyReadForum.do', { 'userID': userId }, {
      emulateJSON: true
    }).then((response) => {
      let reqData = response.body.data
      that.listApplyDiscuss = reqData.listApplyReadForum
      that.listApproveDiscuss = reqData.listApprovedReadForum
      that.unreadForumNum = reqData.unreadForumNum
      that.loading = false
    }).catch(function () {
      that.loading = false
      that.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
    this.listenersUid = [
      this.sphdSocket.subscribe('applyReadForum', function (data) { // 终审时更新数据
        console.log('讨论组阅览申请待审批通道:' + data)
        let reqData = JSON.parse(data)
        let operate = reqData.operate
        if (operate > 0) { // 增加一条
          console.log('增加一条')
          let newFile = reqData.rf
          that.listApplyDiscuss.push(newFile)
        } else if (operate < 0) { // 减少一条
          console.log('减少一条')
          let deleteId = reqData.rf.id
          that.listApplyDiscuss.forEach(function (item, index) {
            if (deleteId === item.id) {
              that.listApplyDiscuss.splice(index, 1)
            }
          })
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('approvedReadForum', function (data) { // 终审时更新数据
        console.log('讨论组阅览申请近期可阅览文件通道:' + data)
        let reqData = JSON.parse(data)
        let update = reqData.operate
        if (update > 0) { // 增加一条
          console.log('增加一条')
          let newFile = reqData.rf
          that.listApproveDiscuss.push(newFile)
          that.unreadForumNum = that.unreadForumNum + 1
        } else if (update < 0) { // 减少一条
          console.log('减少一条')
          let deleteId = reqData.rf.id
          that.listApproveDiscuss.forEach(function (item, index) {
            if (deleteId === item.id) {
              that.listApproveDiscuss.splice(index, 1)
            }
          })
          that.unreadForumNum = that.unreadForumNum - 1
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id, mark) {
      this.$router.push({
        path: `/discussionBorrowDetail/${id}/${mark}`
      })
    },
    seeDiscuss: function (borrowDetail) {
      let borrowId = borrowDetail.id
      let id = borrowDetail.resourceForum
      let isOpen = borrowDetail.isOpen
      if (Number(isOpen) === 1) {
        if (borrowDetail.isCleaned === 1) {
          let that = this
          this.$http.post('../../../read/getReadForumPostMessage.do', { 'borrowId': borrowId }, {
            emulateJSON: true
          }).then(() => {
            window.parent.location.href = window.parent.$.webRoot + '/forum/discussionOtherIndex.do?id=' + id
            that.loading = false
          }).catch(function () {
            that.loading = false
            that.$message({
              type: 'error',
              message: '系统错误，请重试！'
            })
          })
        } else {
          let msg = '该讨论组已于' + moment(borrowDetail.cleanTime).format('YYYY-MM-DD HH:mm:ss') + '被' + borrowDetail.compereName + '清除，您已无法再阅览！'
          this.$kiko_message(msg)
        }
      } else {
        this.$kiko_message('该讨论组您已无法再阅览！')
      }
    },
    screen: function () {
      this.$router.push({
        path: '/discussionBorrowQuery/apply'
      })
    }
  }
}
</script>
