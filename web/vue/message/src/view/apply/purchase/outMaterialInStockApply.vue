<template>
  <div id="outMaterialInStockApplyTrue">
    <TY_NavTop title="外购物料入库申请" isSearch="true" @toggleList="search" :isBackHome=false></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待检验" name="1" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item, 1)" v-for="(item, index) in applyHandleCheck" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.name}}</div>
                <div>采购 {{item.create_name}} {{item.create_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待入库" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item, 2)" v-for="(item, index) in applyHandleStorage" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="数量异议待处理" name="3" closable='false' :msgCount="applyHandleNumCount">
        <div class="tip">
          您申请入库的物料中，仓库清点后认为如下物料数量不对。
          请及时与仓库或供应商等确认，并妥善处理！
        </div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item, 3)" v-for="(item, index) in applyHandleNum" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<style>
  #outMaterialInStockApplyTrue .tabs-tab{
    width: 33.3%;
  }
  #outMaterialInStockApplyTrue .ui-cell{
    font-size: 12px;
  }
</style>

<script>
export default {
  name: 'applyOverTime',
  data () {
    return {
      tabValue: '1',
      applyHandleCheck: [],
      applyHandleStorage: [],
      applyHandleNum: [],
      listenersUid: []
    }
  },
  computed: {
    applyHandleNumCount: function () {
      return this.applyHandleNum.length
    }
  },
  created () {
    this.tabValue = this.$route.params.tabValue || '1'
    let that = this
    this.axios.post('../../../inStock/list', {
      oid: that.sphdSocket.user.oid
    }).then(function (response) {
      let list = response.data.data
      if (list) {
        that.applyHandleCheck = list
      }
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(3)',
      users: 'zb'
    }).then(function (response) {
      let list = response.data.data
      if (list) {
        that.applyHandleStorage = list
      }
      console.log('待入库' + JSON.stringify(list))
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(9)',
      users: 'zb'
    }).then(function (response) {
      let list = response.data.data
      if (list) {
        that.applyHandleNum = list
      }
      console.log('数量异议待处理' + JSON.stringify(list))
    })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (item, tab) {
      this.$store.dispatch('setMaterialParam', {
        user: 'zbi',
        tab: tab
      })
      if (tab === 1) {
        this.$router.push({
          path: `/outMaterialInStockApplyList/${item.id}`
        })
        localStorage.setItem('applyItem', JSON.stringify(item))
      } else {
        this.$router.push({
          path: `/materialDetail/${item.id}`
        })
      }
    },
    search: function () {
      this.$router.push({
        path: `/purchaseQuery/1`
      })
    }
  }
}
</script>
