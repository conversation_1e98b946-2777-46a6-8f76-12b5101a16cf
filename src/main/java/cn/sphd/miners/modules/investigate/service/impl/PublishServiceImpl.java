package cn.sphd.miners.modules.investigate.service.impl;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.investigate.dao.*;
import cn.sphd.miners.modules.investigate.dto.*;
import cn.sphd.miners.modules.investigate.entity.*;
import cn.sphd.miners.modules.investigate.service.InvestigationService;
import cn.sphd.miners.modules.investigate.service.PublishService;
import cn.sphd.miners.modules.investigate.utils.ExportExcel;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.trainManage.entity.TrainingExam;
import cn.sphd.miners.modules.trainManage.service.impl.ExamOverCallback;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * @ClassName PublishServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/3 8:13
 * @Version 1.0
 */
@Service("PublishService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PublishServiceImpl implements PublishService {
    @Autowired
    InvestigatePublishDao investigatePublishDao;
    @Autowired
    InvestigateSubjectTagDao investigateSubjectTagDao;
    @Autowired
    InvestigationService investigationService;
    @Autowired
    OrgService orgService;
    @Autowired
    InvestigateQuestionDao investigateQuestionDao;
    @Autowired
    InvestigateSubjectDao investigateSubjectDao;
    @Autowired
    InvestigateAnswerStatDao investigateAnswerStatDao;
    @Autowired
    InvestigateObjectDao investigateObjectDao;
    @Autowired
    InvestigateObjectHistoryDao investigateObjectHistoryDao;
    @Autowired
    InvestigateAnswerDao investigateAnswerDao;
    @Autowired
    InvestigateAnswerHistoryDao investigateAnswerHistoryDao;
    @Autowired
    InvestigateQuestionKeyDao investigateQuestionKeyDao;
    @Autowired
    InvestigateQrDao investigateQrDao;
    @Autowired
    InvestigatePublishShowDao investigatePublishShowDao;

    @Override
    public List<InvestigatePublish> selectInvestigatePublishList(User user, PageInfo pageInfo, Integer status, String month) {
        Date beginTime=new Date();
        Date endTime=new Date();
        if(!"".equals(month)&& month!=null) {
        month=month+"-01";
        String formatter = "yyyy-MM-dd";
        beginTime=NewDateUtils.dateFromString(month,formatter);
        endTime=NewDateUtils.getLastTimeOfMonth(beginTime);
        }
        String hql="";
        Map<String, Object> params = new HashMap<>();
        params.put("org", user.getOid());
        params.put("status", status);
        if(status==0) {
            hql = "from InvestigatePublish where org=:org and enabled=:status and enabledTime  between :beginTime and :endTime ORDER BY createDate desc";
            params.put("beginTime", beginTime);
            params.put("endTime", endTime);
        }else
            hql = "from InvestigatePublish where org=:org and enabled=:status ORDER BY createDate desc";
        List<InvestigatePublish> list = investigatePublishDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        for(InvestigatePublish investigatePublish:list){
            Calendar calendar=Calendar.getInstance();
            if(investigatePublish.getModifyLimit() ==null)
                investigatePublish.setModifyLimit(10);
            calendar.add(Calendar.MINUTE,-investigatePublish.getModifyLimit());
            Date endDate=calendar.getTime();
            String hql1="from InvestigateObject where publish="+investigatePublish.getId()+" and createDate <:endDate ";
            Map<String, Object> params1= new HashMap<>();
            params1.put("endDate",endDate);
            List<InvestigateObject> list1=new ArrayList<>();
            if(status==1)
            list1 = investigateObjectDao.getListByHQLWithNamedParams(hql1,params1);
            else if(status==0){
                hql1="from InvestigateObject where publish="+investigatePublish.getId();
                list1 = investigateObjectDao.getListByHQLWithNamedParams(hql1,null);
            }
            investigatePublish.setFeedbackNum(list1.size());
            investigatePublishDao.update(investigatePublish);
        }
        return list;
    }

    @Override
    public int countInvestigatePublish(User user, Integer status, String month) {
        Date beginTime=new Date();
        Date endTime=new Date();
        if(!"".equals(month)&& month!=null) {
            month = month + "-01";
            String formatter = "yyyy-MM-dd";
            beginTime = NewDateUtils.dateFromString(month, formatter);
            endTime = NewDateUtils.getLastTimeOfMonth(beginTime);
        }
        String hql="";
        Map<String, Object> params = new HashMap<>();
        params.put("org", user.getOid());
        params.put("status", status);
        if(status==0) {
            hql = "from InvestigatePublish where org=:org and enabled=:status and enabledTime  between :beginTime and :endTime ORDER BY createDate desc";
            params.put("beginTime", beginTime);
            params.put("endTime", endTime);
        }else
            hql = "from InvestigatePublish where org=:org and enabled=:status ORDER BY createDate desc";
        List<InvestigatePublish> list = investigatePublishDao.getListByHQLWithNamedParams(hql,params);
        return list.size();
    }

    @Override
    public RespInvestigateStopByMonthList selectSubjectStopByMonthList(User user, String year) {
        RespInvestigateStopByMonthList respInvestigateStopByMonthList=new RespInvestigateStopByMonthList();
        if(year==null||"".equals(year))
        {
            year=String.valueOf(NewDateUtils.getYear(new Date()));
        }
        respInvestigateStopByMonthList.setYear(year);
        String sql="SELECT DATE_FORMAT(enabled_time,'%Y-%m') AS `month`,count(*) AS sum FROM `t_investigate_publish` WHERE LEFT(enabled_time,4)='"+year+"' AND org="+user.getOid()+" AND enabled=0 GROUP BY DATE_FORMAT(enabled_time,'%Y-%m')";
        List<Object[]> objectList= investigatePublishDao.getObjectListBySQL(sql);
        int sum=0;
        List<RespInvestigateStopByMonth> list=new ArrayList<>();
        if (objectList.size() > 0 && !objectList.isEmpty()) {
            for (int i = 0; i <objectList.size(); i++) {
                RespInvestigateStopByMonth respInvestigateStopByMonth =new RespInvestigateStopByMonth();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(objectList.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                respInvestigateStopByMonth.setMonth(objects.getString(0));
                respInvestigateStopByMonth.setSum(Integer.valueOf(objects.getString(1)));
                list.add(respInvestigateStopByMonth);
                sum=sum+respInvestigateStopByMonth.getSum();
            }
        }
        respInvestigateStopByMonthList.setRespInvestigateStopByMonthList(list);
        respInvestigateStopByMonthList.setSum(sum);
        return respInvestigateStopByMonthList;
    }

    @Override
    public List<InvestigatePublish> selectPublishByNameList(User user, PageInfo pageInfo, String keyword) {
        String hql = "from InvestigatePublish where org="+user.getOid()+" and enabled=1 and name like '%"+keyword+"%' ORDER BY createDate desc";
        List<InvestigatePublish> list = investigatePublishDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public List<InvestigateSubject> selectSubjectOptionsList(Integer org) {
        String hql = "from InvestigateSubject where org="+org+" and enabled=1 ";
        List<InvestigateSubject> list = investigateSubjectDao.getListByHQLWithNamedParams(hql,null);
        return list;
    }

    @Override
    public InvestigatePublish selectInvestigatePublishByid(Integer id) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        return investigatePublish;
    }

    @Override
    public int addPublish(Long expireDate, User user, Integer id, Integer modifyLimit,String coverImage) {
        Date newDate=new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(expireDate);
        Date date = cal.getTime();
        Date today= NewDateUtils.getLastTimeOfDay(date);
        InvestigateSubject investigateSubject=investigateSubjectDao.get(id);
        InvestigatePublish investigatePublish=new InvestigatePublish();
        investigatePublish.setOrg(user.getOid());
        investigatePublish.setCoverImage(coverImage);
        investigatePublish.setSubject(investigateSubject.getId());
        investigatePublish.setBeginTime(newDate);
        investigatePublish.setEndTime(today);
        investigatePublish.setModifyLimit(modifyLimit);
        investigatePublish.setName(investigateSubject.getTitle());
        investigatePublish.setPublishTime(newDate);
        investigatePublish.setQrGeneraeTime(newDate);
        investigatePublish.setQrDeadline(today);
        investigatePublish.setEnabled(1);
        investigatePublish.setFeedbackNum(0);
        investigatePublish.setCreator(user.getUserID());
        investigatePublish.setCreateDate(newDate);
        investigatePublish.setCreateName(user.getUserName());
        investigatePublishDao.save(investigatePublish);
        int order=1;
        String hql = "from InvestigateQuestion where subject="+investigateSubject.getId()+" and isShow=1";
        List<InvestigateQuestion> list = investigateQuestionDao.getListByHQLWithNamedParams(hql,null);
        for(InvestigateQuestion investigateQuestion:list) {
            InvestigatePublishShow investigatePublishShow = new InvestigatePublishShow();
            investigatePublishShow.setPublish(investigatePublish.getId());
            investigatePublishShow.setOrg(investigatePublish.getOrg());
            investigatePublishShow.setQuestion(investigateQuestion.getId());
            investigatePublishShow.setCreator(user.getUserID());
            investigatePublishShow.setCreateDate(newDate);
            investigatePublishShow.setCreateName(user.getUserName());
            investigatePublishShow.setEnabled(1);
            investigatePublishShow.setOrders(order);
            investigatePublishShow.setContent(investigateQuestion.getContent());
            investigatePublishShowDao.save(investigatePublishShow);
            order++;
        }

        InvestigateQr investigateQr =new InvestigateQr();
        investigateQr.setOrg(user.getOid());
        investigateQr.setPublish(investigatePublish.getId());
        investigateQr.setQrDeadline(today);
        investigateQr.setQrGeneraeTime(newDate);
        investigateQr.setCreator(user.getUserID());
        investigateQr.setCreateDate(newDate);
        investigateQr.setCreateName(user.getUserName());
        investigateQrDao.save(investigateQr);
        return 1;
    }

    @Override
    public int updatePublish(Long expireDate, User user, Integer id) {
        Date newDate=new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(expireDate);
        Date date = cal.getTime();
        Date today= NewDateUtils.getLastTimeOfDay(date);
        String hql="from InvestigateQr where qrTerminateTime is null and publish="+id;
        List<InvestigateQr> list=investigateQrDao.getListByHQLWithNamedParams(hql,null);
        for(InvestigateQr investigateQr:list)
        {
            investigateQr.setQrTerminateTime(newDate);
            investigateQr.setUpdateName(user.getUserName());
            investigateQr.setUpdateDate(newDate);
            investigateQr.setUpdator(user.getUserID());
            investigateQrDao.update(investigateQr);
        }
        InvestigateQr investigateQr =new InvestigateQr();
        investigateQr.setOrg(user.getOid());
        investigateQr.setPublish(id);
        investigateQr.setQrDeadline(today);
        investigateQr.setQrGeneraeTime(newDate);
        investigateQr.setCreator(user.getUserID());
        investigateQr.setCreateDate(newDate);
        investigateQr.setCreateName(user.getUserName());
        investigateQrDao.save(investigateQr);
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        investigatePublish.setUpdateName(user.getUserName());
        investigatePublish.setUpdateDate(newDate);
        investigatePublish.setUpdator(user.getUserID());
        investigatePublish.setEndTime(today);
        investigatePublish.setQrGeneraeTime(newDate);
        investigatePublish.setQrDeadline(today);
        investigatePublishDao.update(investigatePublish);
        return 1;
    }
    @Override
    public int updatePublishCoverImage(String coverImage, User user, Integer id) {
        Date newDate=new Date();
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        investigatePublish.setUpdateName(user.getUserName());
        investigatePublish.setCoverImage(coverImage);
        investigatePublish.setUpdateDate(newDate);
        investigatePublish.setUpdator(user.getUserID());
        investigatePublishDao.update(investigatePublish);
        return 1;
    }
    @Override
    public int updatePublishModifyLimit(Integer modifyLimit, User user, Integer id) {
        Date newDate=new Date();
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        investigatePublish.setUpdateName(user.getUserName());
        if(modifyLimit!=null)
            investigatePublish.setModifyLimit(modifyLimit);
        investigatePublish.setUpdateDate(newDate);
        investigatePublish.setUpdator(user.getUserID());
        investigatePublishDao.update(investigatePublish);
        return 1;
    }
    @Override
    public int stopPublish(User user, Integer id) {
        Date newDate=new Date();
        String hql="from InvestigateQr where qrTerminateTime is null and publish="+id;
        List<InvestigateQr> list=investigateQrDao.getListByHQLWithNamedParams(hql,null);
        for(InvestigateQr investigateQr:list)
        {
            investigateQr.setQrTerminateTime(newDate);
            investigateQr.setUpdateName(user.getUserName());
            investigateQr.setUpdateDate(newDate);
            investigateQr.setUpdator(user.getUserID());
            investigateQrDao.update(investigateQr);
        }
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        investigatePublish.setEnabled(0);
        investigatePublish.setEnabledTime(newDate);
        investigatePublish.setUpdateName(user.getUserName());
        investigatePublish.setUpdateDate(newDate);
        investigatePublish.setUpdator(user.getUserID());
        investigatePublishDao.update(investigatePublish);
        return 1;
    }

    @Override
    public RespShowQuestion completeSubjectList(PageInfo pageInfo,Integer id,Integer type) {
        RespShowQuestion respShowQuestion =new RespShowQuestion();
        List<RespInvestigateObject> respInvestigateObjectList=new ArrayList<>();
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);

        String hql="from InvestigatePublishShow where publish="+id;
        List<InvestigatePublishShow> investigatePublishShowList=investigatePublishShowDao.getListByHQLWithNamedParams(hql,null);
        int number=1;
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MINUTE,-investigatePublish.getModifyLimit());
        Date endDate=calendar.getTime();
        String hqlObject="from InvestigateObject where publish="+id+" and subsumable="+type+"  and createDate < :endDate order by createDate desc";
        Map<String, Object> params1= new HashMap<>();
        params1.put("endDate",endDate);
        List<InvestigateObject> investigateObjectList=investigateObjectDao.getListByHQLWithNamedParams(hqlObject,params1,pageInfo);
        for(InvestigateObject investigateObject:investigateObjectList){
            List<RespInvestigateAnswer> respInvestigateAnswerList=new ArrayList<>();
            RespInvestigateObject respInvestigateObject=new RespInvestigateObject();
            BeanUtils.copyProperties(investigateObject,respInvestigateObject);
            for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList){
                InvestigateQuestion investigateQuestion=investigateQuestionDao.get(investigatePublishShow.getQuestion());
                List<InvestigateQuestionKey> investigateQuestionKeyList=new ArrayList<>();
                if("4".equals(investigateQuestion.getType())) {
                    String hqlKey = "from InvestigateQuestionKey where question=" + investigateQuestion.getId();
                    List<InvestigateQuestionKey> investigateQuestionKeys = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlKey, null);
                    for(InvestigateQuestionKey investigateQuestionKey:investigateQuestionKeys){
                        investigateQuestionKeyList.add(investigateQuestionKey);
                    }
                }
                String answerHql="from InvestigateAnswer where question="+investigatePublishShow.getQuestion()+" and objective="+investigateObject.getId();
                List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,null);
                if(investigateAnswerList.size()>0){
                    if(investigateAnswerList.get(0).getAnswer()!=null&&!"".equals(investigateAnswerList.get(0).getAnswer())&&!"[]".equals(investigateAnswerList.get(0).getAnswer()))
                    {
                        investigateAnswerList.get(0).setContent(selectContentByAnswers(investigateAnswerList.get(0).getAnswer()));
                        if("4".equals(investigateQuestion.getType())){
                            String content="";
                            List<String> list=Arrays.asList(investigateAnswerList.get(0).getAnswer().split(","));
                            for(int i=0;i<list.size();i++)
                            {
                               for(int j=0;j<investigateQuestionKeyList.size();j++)
                               {
                                    if(Integer.valueOf(list.get(i)).intValue()==investigateQuestionKeyList.get(j).getId()){
                                        content=content+" "+investigateQuestionKeyList.get(j).getOrders();
                                    }
                               }
                            }
                            investigateAnswerList.get(0).setContent(content);
                        }
                    }
                    String content="";
                    if("9".equals(investigateQuestion.getType())&&investigateQuestion.getIsMultiple()>0){
                        for(InvestigateAnswer investigateAnswer :investigateAnswerList){
                            content=content+" "+investigateAnswer.getContent();
                        }
                    }else {
                        content =investigateAnswerList.get(0).getContent();
                    }
                    RespInvestigateAnswer respInvestigateAnswer=new RespInvestigateAnswer();
                    respInvestigateAnswer.setQuestion(investigatePublishShow.getQuestion());
                    respInvestigateAnswer.setContent(content);
                    respInvestigateAnswerList.add(respInvestigateAnswer);
                }
            }
            respInvestigateObject.setRespInvestigateAnswerList(respInvestigateAnswerList);
            respInvestigateObjectList.add(respInvestigateObject);
        }
        respShowQuestion.setInvestigatePublishShowList(investigatePublishShowList);
        respShowQuestion.setRespInvestigateObjectList(respInvestigateObjectList);
        return respShowQuestion;
    }
    @Override
    public RespShowQuestion investigateAnswerListByQuestion(int publish,int question, PageInfo pageInfo,Integer type) {
        RespShowQuestion respShowQuestion =new RespShowQuestion();
        List<RespInvestigateObject> respInvestigateObjectList=new ArrayList<>();
        InvestigatePublish investigatePublish=investigatePublishDao.get(publish);
        RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
        InvestigateQuestion investigateQuestion=investigateQuestionDao.get(question);
        respShowQuestion.setInvestigateQuestion(investigateQuestion);
        BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MINUTE,-investigatePublish.getModifyLimit());
        Date endDate=calendar.getTime();
        Map<String, Object> params1= new HashMap<>();
        params1.put("endDate",endDate);
        String hql="from InvestigateObject where publish="+publish+" and subsumable=1  and createDate < :endDate" ;
        List<InvestigateObject> investigateObjectList=investigateObjectDao.getListByHQLWithNamedParams(hql,params1);
        respShowQuestion.setObjSum(investigateObjectList.size());
        String answerHql1="from InvestigateAnswer where question="+question;
        List<InvestigateAnswer> investigateAnswerList1=investigateAnswerDao.getListByHQLWithNamedParams(answerHql1,null);
        List<InvestigateObject> investigateObjectList1 = new ArrayList<>();
        for(InvestigateAnswer investigateAnswer:investigateAnswerList1) {
            for (InvestigateObject investigateObject : investigateObjectList) {
                if (investigateObject.getId().intValue() ==investigateAnswer.getObjective().intValue() &&!"".equals(investigateAnswer.getContent()))
                {
                    int x=0;
                    for (InvestigateObject investigateObject1 : investigateObjectList1) {
                        if (investigateObject.getId().intValue()==investigateObject1.getId().intValue()){
                            x=1;
                        }
                    }
                    if(x==0)
                    {
                        investigateObjectList1.add(investigateObject);
                    }
                }
            }
        }
        respShowQuestion.setAnswerSum(investigateObjectList1.size());

        List<InvestigatePublishShow> investigatePublishShowList=new ArrayList<>();
        //普通问答题
        if(type==1){
            String sql="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and isShow=1" ;
            List<InvestigateQuestion> questionList=investigateQuestionDao.getListByHQLWithNamedParams(sql,null);
            int x=1;
            int a=0;//查看本题是否为默认题
            for(InvestigateQuestion investigateQuestion1:questionList){
                InvestigatePublishShow investigatePublishShow=new InvestigatePublishShow();
                investigatePublishShow.setQuestion(investigateQuestion1.getId());
                investigatePublishShow.setPublish(publish);
                investigatePublishShow.setEnabled(1);
                investigatePublishShow.setContent(investigateQuestion1.getContent());
                investigatePublishShow.setOrders(x);
                x++;
                investigatePublishShowList.add(investigatePublishShow);
                if(investigateQuestion1.getId()==investigateQuestion.getId())
                    a=1;
            }
            if(a==0){
                InvestigatePublishShow investigatePublishShow=new InvestigatePublishShow();
                investigatePublishShow.setQuestion(investigateQuestion.getId());
                investigatePublishShow.setPublish(publish);
                investigatePublishShow.setEnabled(1);
                investigatePublishShow.setContent(investigateQuestion.getContent());
                investigatePublishShow.setOrders(x);
                x++;
                investigatePublishShowList.add(investigatePublishShow);
            }
            for(InvestigateObject investigateObject:investigateObjectList1){
                List<RespInvestigateAnswer> respInvestigateAnswerList=new ArrayList<>();
                RespInvestigateObject respInvestigateObject=new RespInvestigateObject();
                BeanUtils.copyProperties(investigateObject,respInvestigateObject);
                for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList){
                    InvestigateQuestion investigateQuestion2=investigateQuestionDao.get(investigatePublishShow.getQuestion());
                    List<InvestigateQuestionKey> investigateQuestionKeyList=new ArrayList<>();
                    if("4".equals(investigateQuestion2.getType())) {
                        String hqlKey = "from InvestigateQuestionKey where question=" + investigateQuestion2.getId();
                        List<InvestigateQuestionKey> investigateQuestionKeys = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlKey, null);
                        for(InvestigateQuestionKey investigateQuestionKey:investigateQuestionKeys){
                            investigateQuestionKeyList.add(investigateQuestionKey);
                        }
                    }
                    String answerHql="from InvestigateAnswer where question="+investigatePublishShow.getQuestion()+" and objective="+investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,null);
                    if(investigateAnswerList.size()>0){
                        if(investigateAnswerList.get(0).getAnswer()!=null&&!"".equals(investigateAnswerList.get(0).getAnswer())&&!"[]".equals(investigateAnswerList.get(0).getAnswer()))
                        {
                            investigateAnswerList.get(0).setContent(selectContentByAnswers(investigateAnswerList.get(0).getAnswer()));
                            if("4".equals(investigateQuestion2.getType())){
                                String content="";
                                List<String> list=Arrays.asList(investigateAnswerList.get(0).getAnswer().split(","));
                                for(int i=0;i<list.size();i++)
                                {
                                    for(int j=0;j<investigateQuestionKeyList.size();j++)
                                    {
                                        if(Integer.valueOf(list.get(i)).intValue()==investigateQuestionKeyList.get(j).getId()){
                                            content=content+" "+investigateQuestionKeyList.get(j).getOrders();
                                        }
                                    }
                                }
                                investigateAnswerList.get(0).setContent(content);
                            }
                        }
                        String content="";
                        if("9".equals(investigateQuestion2.getType())&&investigateQuestion2.getIsMultiple()>0){
                            for(InvestigateAnswer investigateAnswer :investigateAnswerList){
                                content=content+" "+investigateAnswer.getContent();
                            }
                        }else {
                            content =investigateAnswerList.get(0).getContent();
                        }
                        RespInvestigateAnswer respInvestigateAnswer=new RespInvestigateAnswer();
                        respInvestigateAnswer.setQuestion(investigatePublishShow.getQuestion());
                        respInvestigateAnswer.setContent(content);
                        respInvestigateAnswerList.add(respInvestigateAnswer);
                    }
                }
                respInvestigateObject.setRespInvestigateAnswerList(respInvestigateAnswerList);
                respInvestigateObjectList.add(respInvestigateObject);
            }
            respShowQuestion.setInvestigatePublishShowList(investigatePublishShowList);
            respShowQuestion.setRespInvestigateObjectList(respInvestigateObjectList);
        }
        //地址类
        else if (type==3){
            respShowQuestion.setLevel(Integer.parseInt(investigateQuestion.getSpecialTab()));
            List<RespInvestigateArea> investigateAreaList=new ArrayList<>();
            if("1".equals(investigateQuestion.getSpecialTab())){
                for(InvestigateObject investigateObject:investigateObjectList){
                    String answerHql="from InvestigateAnswer where question="+investigateQuestion.getId()+" and objective="+investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,null);
                    for(InvestigateAnswer investigateAnswer:investigateAnswerList)
                    {
                        if(!"".equals(investigateAnswer.getContent())&&!" ".equals(investigateAnswer.getContent())) {
                            int x = 0;
                            for (RespInvestigateArea investigateArea : investigateAreaList) {
                                if (investigateArea.getName().equals(investigateAnswer.getContent())) {
                                    x = 1;
                                    investigateArea.setSum(investigateArea.getSum() + 1);
                                }
                            }
                            if (x == 0) {
                                RespInvestigateArea respInvestigateArea = new RespInvestigateArea();
                                respInvestigateArea.setName(investigateAnswer.getContent());
                                respInvestigateArea.setSum(1);
                                investigateAreaList.add(respInvestigateArea);
                            }
                        }
                    }
                }
                respShowQuestion.setInvestigateAreaList(investigateAreaList);
            }
            else if("2".equals(investigateQuestion.getSpecialTab())){
                for(InvestigateObject investigateObject:investigateObjectList) {
                    String answerHql = "from InvestigateAnswer where question=" + investigateQuestion.getId() + " and objective=" + investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList = investigateAnswerDao.getListByHQLWithNamedParams(answerHql, null);
                    for(InvestigateAnswer investigateAnswer:investigateAnswerList) {
                        if (!"".equals(investigateAnswer.getContent()) && !" ".equals(investigateAnswer.getContent())) {
                            List<String> list = Arrays.asList(investigateAnswer.getContent().split(" / "));
                            int x = 0;
                            for (RespInvestigateArea investigateArea : investigateAreaList) {
                                if (investigateArea.getName().equals(list.get(0))) {
                                    x = 1;
                                    investigateArea.setSum(investigateArea.getSum() + 1);
                                    int y = 0;
                                    for (RespInvestigateArea investigateArea1 : investigateArea.getInvestigateAreaList()) {
                                        if (list.size() > 1 && investigateArea1.getName().equals(list.get(1))) {
                                            y = 1;
                                            investigateArea1.setSum(investigateArea1.getSum() + 1);
                                        }
                                    }
                                    if (y == 0) {
                                        RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                        respInvestigateArea1.setName(list.get(1));
                                        respInvestigateArea1.setSum(1);
                                        investigateArea.getInvestigateAreaList().add(respInvestigateArea1);
                                    }
                                }
                            }
                            if (x == 0) {
                                RespInvestigateArea respInvestigateArea = new RespInvestigateArea();
                                respInvestigateArea.setName(list.get(0));
                                respInvestigateArea.setSum(1);
                                if (list.size() > 1) {
                                    RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                    respInvestigateArea1.setName(list.get(1));
                                    respInvestigateArea1.setSum(1);
                                    List<RespInvestigateArea> respInvestigateAreas = new ArrayList<>();
                                    if (respInvestigateArea.getInvestigateAreaList() != null)
                                        respInvestigateAreas = respInvestigateArea.getInvestigateAreaList();
                                    respInvestigateAreas.add(respInvestigateArea1);
                                    respInvestigateArea.setInvestigateAreaList(respInvestigateAreas);
                                }
                                investigateAreaList.add(respInvestigateArea);
                            }
                        }
                    }
                }
                respShowQuestion.setInvestigateAreaList(investigateAreaList);
            }
            else if("3".equals(investigateQuestion.getSpecialTab())||"4".equals(investigateQuestion.getSpecialTab())){
                for(InvestigateObject investigateObject:investigateObjectList) {
                    String answerHql = "from InvestigateAnswer where question=" + investigateQuestion.getId() + " and objective=" + investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList = investigateAnswerDao.getListByHQLWithNamedParams(answerHql, null);
                    for(InvestigateAnswer investigateAnswer:investigateAnswerList) {
                        if (investigateAnswer.getContent() != null && !"".equals(investigateAnswer.getContent())&& !" ".equals(investigateAnswer.getContent()))
                        {
                            List<String> list = Arrays.asList(investigateAnswer.getContent().split(" / "));
                            if ("4".equals(investigateQuestion.getSpecialTab())&&list.size() > 2) {
                                int index = list.get(2).indexOf("--");
                                if(index!=-1)
                                list.set(2, list.get(2).substring(0, index));
                            }
                            int x = 0;
                            for (RespInvestigateArea investigateArea : investigateAreaList) {
                                if (investigateArea.getName().equals(list.get(0))) {
                                    x = 1;
                                    investigateArea.setSum(investigateArea.getSum() + 1);
                                    int y = 0;
                                    for (RespInvestigateArea investigateArea1 : investigateArea.getInvestigateAreaList()) {
                                        if (list.size() > 1 && investigateArea1.getName().equals(list.get(1))) {
                                            y = 1;
                                            investigateArea1.setSum(investigateArea1.getSum() + 1);
                                            int z = 0;
                                            for (RespInvestigateArea investigateArea2 : investigateArea1.getInvestigateAreaList()) {
                                                if (list.size() > 2 && investigateArea2.getName().equals(list.get(2))) {
                                                    z = 1;
                                                    investigateArea2.setSum(investigateArea2.getSum() + 1);
                                                }
                                            }
                                            if (z == 0 && list.size() > 2) {
                                                RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                                respInvestigateArea1.setName(list.get(2));
                                                respInvestigateArea1.setSum(1);
                                                investigateArea1.getInvestigateAreaList().add(respInvestigateArea1);
                                            }
                                        }
                                    }
                                    if (y == 0 && list.size() > 1) {
                                        RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                        respInvestigateArea1.setName(list.get(1));
                                        respInvestigateArea1.setSum(1);
                                        List<RespInvestigateArea> respInvestigateAreas1 = new ArrayList<>();
                                        if(list.size() > 2) {
                                            RespInvestigateArea respInvestigateArea2 = new RespInvestigateArea();
                                            respInvestigateArea2.setName(list.get(2));
                                            respInvestigateArea2.setSum(1);
                                            respInvestigateAreas1.add(respInvestigateArea2);
                                            respInvestigateArea1.setInvestigateAreaList(respInvestigateAreas1);
                                        }
                                        investigateArea.getInvestigateAreaList().add(respInvestigateArea1);
                                    }
                                }
                            }
                            if (x == 0) {
                                if (list.size() > 0) {
                                    RespInvestigateArea respInvestigateArea = new RespInvestigateArea();
                                    respInvestigateArea.setName(list.get(0));
                                    respInvestigateArea.setSum(1);
                                    RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                    if (list.size() > 1) {
                                        respInvestigateArea1.setName(list.get(1));
                                        respInvestigateArea1.setSum(1);
                                        RespInvestigateArea respInvestigateArea2 = new RespInvestigateArea();
                                        if (list.size() > 2) {
                                            respInvestigateArea2.setName(list.get(2));
                                            respInvestigateArea2.setSum(1);
                                            List<RespInvestigateArea> respInvestigateAreas=new ArrayList<>();
                                            if(respInvestigateArea1.getInvestigateAreaList()!=null)
                                                respInvestigateAreas=respInvestigateArea1.getInvestigateAreaList();
                                            respInvestigateAreas.add(respInvestigateArea2);
                                            respInvestigateArea1.setInvestigateAreaList(respInvestigateAreas);
                                        }
                                        List<RespInvestigateArea> respInvestigateAreas=new ArrayList<>();
                                        if(respInvestigateArea.getInvestigateAreaList()!=null)
                                            respInvestigateAreas=respInvestigateArea.getInvestigateAreaList();
                                        respInvestigateAreas.add(respInvestigateArea1);
                                        respInvestigateArea.setInvestigateAreaList(respInvestigateAreas);
                                    }
                                    investigateAreaList.add(respInvestigateArea);
                                }
                            }
                        }
                    }
                }
                respShowQuestion.setInvestigateAreaList(investigateAreaList);
            }
        }
        //日期类
        else if (type==4){
            respShowQuestion.setLevel(Integer.parseInt(investigateQuestion.getSpecialTab()));
            List<RespInvestigateArea> investigateAreaList=new ArrayList<>();
            if("1".equals(investigateQuestion.getSpecialTab())){
                for(InvestigateObject investigateObject:investigateObjectList){
                    String answerHql="from InvestigateAnswer where question="+investigateQuestion.getId()+" and objective="+investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,null);
                    for(InvestigateAnswer investigateAnswer:investigateAnswerList) {
                        if (investigateAnswer.getContent() != null && !"".equals(investigateAnswer.getContent())&& !" ".equals(investigateAnswer.getContent()))
                        {
                        int x = 0;
                        for (RespInvestigateArea investigateArea : investigateAreaList) {
                            if (investigateArea.getName().equals(investigateAnswer.getContent())) {
                                x = 1;
                                investigateArea.setSum(investigateArea.getSum() + 1);
                            }
                        }
                        if (x == 0) {
                            RespInvestigateArea respInvestigateArea = new RespInvestigateArea();
                            respInvestigateArea.setName(investigateAnswer.getContent());
                            respInvestigateArea.setSum(1);
                            investigateAreaList.add(respInvestigateArea);
                        }
                    }
                    }
                }
                respShowQuestion.setInvestigateAreaList(investigateAreaList);
            }
            else if("2".equals(investigateQuestion.getSpecialTab())){
                for(InvestigateObject investigateObject:investigateObjectList) {
                    String answerHql = "from InvestigateAnswer where question=" + investigateQuestion.getId() + " and objective=" + investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList = investigateAnswerDao.getListByHQLWithNamedParams(answerHql, null);
                    for(InvestigateAnswer investigateAnswer:investigateAnswerList)
                    {
                        if(investigateAnswer.getContent()!=null&&!"".equals(investigateAnswer.getContent())&&!" ".equals(investigateAnswer.getContent()))
                        {
                            List<String> list = Arrays.asList(investigateAnswer.getContent().split("年"));
                            int x=0;
                            for(RespInvestigateArea investigateArea:investigateAreaList)
                            {
                                if(investigateArea.getName().equals(list.get(0)+"年")){
                                    x=1;
                                    investigateArea.setSum(investigateArea.getSum()+1);
                                    int y=0;
                                    for(RespInvestigateArea investigateArea1:investigateArea.getInvestigateAreaList())
                                    {
                                        if(investigateArea1.getName().equals(list.get(1))){
                                            y=1;
                                            investigateArea1.setSum(investigateArea1.getSum()+1);
                                        }
                                    }
                                    if(y==0)
                                    {
                                        RespInvestigateArea respInvestigateArea1=new RespInvestigateArea();
                                        respInvestigateArea1.setName(list.get(1));
                                        respInvestigateArea1.setSum(1);
                                        investigateArea.getInvestigateAreaList().add(respInvestigateArea1);
                                    }
                                }
                            }
                            if(x==0)
                            {
                                RespInvestigateArea respInvestigateArea=new RespInvestigateArea();
                                respInvestigateArea.setName(list.get(0)+"年");
                                respInvestigateArea.setSum(1);
                                if(list.size()>1) {
                                    RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                    respInvestigateArea1.setName(list.get(1));
                                    respInvestigateArea1.setSum(1);
                                    List<RespInvestigateArea> respInvestigateAreas = new ArrayList<>();
                                    if (respInvestigateArea.getInvestigateAreaList() != null)
                                        respInvestigateAreas = respInvestigateArea.getInvestigateAreaList();
                                    respInvestigateAreas.add(respInvestigateArea1);
                                    respInvestigateArea.setInvestigateAreaList(respInvestigateAreas);
                                    investigateAreaList.add(respInvestigateArea);
                                }
                            }
                        }
                    }
                    respShowQuestion.setInvestigateAreaList(investigateAreaList);
                }
            }
            else if("3".equals(investigateQuestion.getSpecialTab())){
                for(InvestigateObject investigateObject:investigateObjectList) {
                    String answerHql = "from InvestigateAnswer where question=" + investigateQuestion.getId() + " and objective=" + investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList = investigateAnswerDao.getListByHQLWithNamedParams(answerHql, null);
                    for(InvestigateAnswer investigateAnswer:investigateAnswerList) {
                        if (investigateAnswer.getContent() != null && !"".equals(investigateAnswer.getContent())&& !" ".equals(investigateAnswer.getContent())) {
                            List<String> list1 = Arrays.asList(investigateAnswer.getContent().split("年"));
                            List<String> list2 = Arrays.asList(list1.get(1).split("月"));
                            List<String> list = new ArrayList<>();
                            list.add(list1.get(0));
                            list.add(list2.get(0));
                            list.add(list2.get(1));
                            int x = 0;
                            for (RespInvestigateArea investigateArea : investigateAreaList) {
                                if (investigateArea.getName().equals(list.get(0) + "年")) {
                                    x = 1;
                                    investigateArea.setSum(investigateArea.getSum() + 1);
                                    int y = 0;
                                    for (RespInvestigateArea investigateArea1 : investigateArea.getInvestigateAreaList()) {
                                        if (investigateArea1.getName().equals(list.get(1) + "月")) {
                                            y = 1;
                                            investigateArea1.setSum(investigateArea1.getSum() + 1);
                                            int z = 0;
                                             if(investigateArea1.getInvestigateAreaList()!=null&&list.size()>2)
                                            {
                                                for (RespInvestigateArea investigateArea2 : investigateArea1.getInvestigateAreaList()) {
                                                    if (investigateArea2.getName().equals(list.get(2))) {
                                                        z = 1;
                                                        investigateArea2.setSum(investigateArea2.getSum() + 1);
                                                    }
                                                }
                                            }
                                            if (z == 0) {
                                                RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                                respInvestigateArea1.setName(list.get(2));
                                                respInvestigateArea1.setSum(1);
                                                List<RespInvestigateArea> respInvestigateAreas = new ArrayList<>();
                                                if (investigateArea1.getInvestigateAreaList()!= null)
                                                    respInvestigateAreas = investigateArea1.getInvestigateAreaList();
                                                respInvestigateAreas.add(respInvestigateArea1);
                                                investigateArea1.setInvestigateAreaList(respInvestigateAreas);
                                            }
                                        }
                                    }
                                    if (y == 0) {
                                        RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                        respInvestigateArea1.setName(list.get(1) + "月");
                                        respInvestigateArea1.setSum(1);
                                        List<RespInvestigateArea> respInvestigateAreas1 = new ArrayList<>();
                                        RespInvestigateArea respInvestigateArea2 = new RespInvestigateArea();
                                        respInvestigateArea2.setName(list.get(2));
                                        respInvestigateArea2.setSum(1);
                                        respInvestigateAreas1.add(respInvestigateArea2);
                                        respInvestigateArea1.setInvestigateAreaList(respInvestigateAreas1);

                                        List<RespInvestigateArea> respInvestigateAreas = new ArrayList<>();
                                        if (investigateArea.getInvestigateAreaList()!= null)
                                            respInvestigateAreas = investigateArea.getInvestigateAreaList();
                                        respInvestigateAreas.add(respInvestigateArea1);
                                        investigateArea.setInvestigateAreaList(respInvestigateAreas);

                                    }
                                }
                            }
                            if (x == 0) {
                                RespInvestigateArea respInvestigateArea = new RespInvestigateArea();
                                respInvestigateArea.setName(list.get(0) + "年");
                                respInvestigateArea.setSum(1);
                                RespInvestigateArea respInvestigateArea1 = new RespInvestigateArea();
                                respInvestigateArea1.setName(list.get(1) + "月");
                                respInvestigateArea1.setSum(1);
                                RespInvestigateArea respInvestigateArea2 = new RespInvestigateArea();
                                respInvestigateArea2.setName(list.get(2));
                                respInvestigateArea2.setSum(1);

                                List<RespInvestigateArea> respInvestigateAreas1 = new ArrayList<>();
                                if (respInvestigateArea1.getInvestigateAreaList() != null)
                                    respInvestigateAreas1 = respInvestigateArea1.getInvestigateAreaList();
                                respInvestigateAreas1.add(respInvestigateArea2);
                                respInvestigateArea1.setInvestigateAreaList(respInvestigateAreas1);

                                List<RespInvestigateArea> respInvestigateAreas = new ArrayList<>();
                                if (respInvestigateArea.getInvestigateAreaList() != null)
                                    respInvestigateAreas = respInvestigateArea.getInvestigateAreaList();
                                respInvestigateAreas.add(respInvestigateArea1);
                                respInvestigateArea.setInvestigateAreaList(respInvestigateAreas);
                                investigateAreaList.add(respInvestigateArea);
                            }
                        }
                    }
                }
                respShowQuestion.setInvestigateAreaList(investigateAreaList);
            }
        }
        if(type==3||type==4){
            //省级
            List<RespInvestigateArea> investigateAreaList=respShowQuestion.getInvestigateAreaList();
            if(investigateAreaList!=null)
            {
                List<RespInvestigateArea> investigateAreaListNew=new ArrayList<>();
                RespInvestigateArea respInvestigateArea4 =new RespInvestigateArea();
                 for(RespInvestigateArea respInvestigateArea:investigateAreaList){
                     //市级
                     if(respInvestigateArea.getInvestigateAreaList()!=null)
                     {
                         List<RespInvestigateArea> investigateAreaList4= new ArrayList<>();
                         RespInvestigateArea respInvestigateArea5 =new RespInvestigateArea();
                         for(RespInvestigateArea respInvestigateArea1:respInvestigateArea.getInvestigateAreaList()){
                             //地区
                             if(respInvestigateArea1.getInvestigateAreaList()!=null)
                             {
                                 List<RespInvestigateArea> investigateAreaList5= respInvestigateArea1.getInvestigateAreaList();
                                 Collections.sort(investigateAreaList5);
                                 respInvestigateArea5.setInvestigateAreaList(investigateAreaList5);
                             }
                         }
                         investigateAreaList4=respInvestigateArea.getInvestigateAreaList();
                         Collections.sort(investigateAreaList4);
                         respInvestigateArea4.setInvestigateAreaList(investigateAreaList4);
                     }
                 }
                investigateAreaListNew=investigateAreaList;
                Collections.sort(investigateAreaListNew);
                respShowQuestion.setInvestigateAreaList(investigateAreaListNew);
            }
        }
        return respShowQuestion;
    }

    @Override
    public RespShowQuestion areaListByQuestion(int publish, int question, PageInfo pageInfo, Integer type, String content) {
        RespShowQuestion respShowQuestion =new RespShowQuestion();
        List<RespInvestigateObject> respInvestigateObjectList=new ArrayList<>();
        InvestigatePublish investigatePublish=investigatePublishDao.get(publish);
        RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
        InvestigateQuestion investigateQuestion=investigateQuestionDao.get(question);
        respShowQuestion.setInvestigateQuestion(investigateQuestion);
        BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MINUTE,-investigatePublish.getModifyLimit());
        Date endDate=calendar.getTime();
        Map<String, Object> params1= new HashMap<>();
        params1.put("endDate",endDate);
        String hql="from InvestigateObject where publish="+publish+" and subsumable=1  and createDate < :endDate" ;
        List<InvestigateObject> investigateObjectList=investigateObjectDao.getListByHQLWithNamedParams(hql,params1);
        respShowQuestion.setObjSum(investigateObjectList.size());
        String answerHql2 = "from InvestigateAnswer where question=" + question;
        List<InvestigateAnswer> investigateAnswerList2 = investigateAnswerDao.getListByHQLWithNamedParams(answerHql2, null);
        List<InvestigateObject> investigateObjectList2 = new ArrayList<>();
        for(InvestigateAnswer investigateAnswer:investigateAnswerList2) {
            for (InvestigateObject investigateObject : investigateObjectList) {
                if (investigateObject.getId().intValue() ==investigateAnswer.getObjective().intValue()&&!"".equals(investigateAnswer.getContent()))
                {
                    int x=0;
                    for (InvestigateObject investigateObject2 : investigateObjectList2) {
                        if (investigateObject.getId().intValue()==investigateObject2.getId().intValue()){
                            x=1;
                        }
                    }
                    if(x==0)
                    {
                        investigateObjectList2.add(investigateObject);
                    }
                }
            }
        }
        respShowQuestion.setContentSum(investigateObjectList2.size());

        List<InvestigateAnswer> investigateAnswerList1=new ArrayList<>();
        if("".equals(content)||content==null) {
            String answerHql1 = "from InvestigateAnswer where question=" + question;
            investigateAnswerList1 = investigateAnswerDao.getListByHQLWithNamedParams(answerHql1, null);
        }else {

            String answerHql1 = "from InvestigateAnswer where question=" + question + " and content like :content";
            Map<String, Object> params11 = new HashMap<>();
            params11.put("content", content + "%");
            investigateAnswerList1 = investigateAnswerDao.getListByHQLWithNamedParams(answerHql1, params11);
        }
        List<InvestigateObject> investigateObjectList1 = new ArrayList<>();
        for(InvestigateAnswer investigateAnswer:investigateAnswerList1) {
            for (InvestigateObject investigateObject : investigateObjectList) {
                if (investigateObject.getId().intValue()==investigateAnswer.getObjective().intValue()&&!"".equals(investigateAnswer.getContent()))
                {
                    int x=0;
                    for (InvestigateObject investigateObject1 : investigateObjectList1) {
                        if (investigateObject.getId().intValue()==investigateObject1.getId().intValue()){
                            x=1;
                        }
                    }
                    if(x==0)
                    {
                        investigateObjectList1.add(investigateObject);
                    }
                }
            }
        }
        respShowQuestion.setAnswerSum(investigateObjectList1.size());


        List<InvestigatePublishShow> investigatePublishShowList=new ArrayList<>();
        List<InvestigatePublishShow> investigatePublishShowList1=new ArrayList<>();
        String sql="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and isShow=1" ;
        List<InvestigateQuestion> questionList=investigateQuestionDao.getListByHQLWithNamedParams(sql,null);
        int x=1;
        int a=0;//查看本题是否为默认题
        for(InvestigateQuestion investigateQuestion1:questionList){
            InvestigatePublishShow investigatePublishShow=new InvestigatePublishShow();
            investigatePublishShow.setQuestion(investigateQuestion1.getId());
            investigatePublishShow.setPublish(publish);
            investigatePublishShow.setEnabled(1);
            investigatePublishShow.setContent(investigateQuestion1.getContent());
            investigatePublishShow.setOrders(x);
            x++;
            investigatePublishShowList.add(investigatePublishShow);
            if(investigateQuestion1.getId()==investigateQuestion.getId())
                a=1;
        }
        if(a==0){
            InvestigatePublishShow investigatePublishShow=new InvestigatePublishShow();
            investigatePublishShow.setQuestion(investigateQuestion.getId());
            investigatePublishShow.setPublish(publish);
            investigatePublishShow.setEnabled(1);
            investigatePublishShow.setContent(investigateQuestion.getContent());
            investigatePublishShow.setOrders(x);
            x++;
            investigatePublishShowList.add(investigatePublishShow);
        }
        //地址题
        if(type==3){
            for(InvestigateObject investigateObject:investigateObjectList1){
                int obj=0;//标记人物，用于筛选
                List<RespInvestigateAnswer> respInvestigateAnswerList=new ArrayList<>();
                RespInvestigateObject respInvestigateObject=new RespInvestigateObject();
                BeanUtils.copyProperties(investigateObject,respInvestigateObject);
                for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList){
                    if(investigatePublishShow.getQuestion().intValue()==question&&content!=null&&!"".equals(content)){
                        String answerHql="from InvestigateAnswer where question="+investigatePublishShow.getQuestion()+" and objective="+investigateObject.getId()+ " and content like :content";
                        Map<String, Object> params= new HashMap<>();
                        params.put("content",content+"%");
                        List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,params);
                        if(investigateAnswerList.size()==0)
                        obj=1;
                    }
                   String answerHql="from InvestigateAnswer where question="+investigatePublishShow.getQuestion()+" and objective="+investigateObject.getId();
                   List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,null);
                   if(investigateAnswerList.size()>0){
                        if(investigateAnswerList.get(0).getAnswer()!=null&&!"".equals(investigateAnswerList.get(0).getAnswer())&&!"[]".equals(investigateAnswerList.get(0).getAnswer()))
                        {
                            investigateAnswerList.get(0).setContent(selectContentByAnswers(investigateAnswerList.get(0).getAnswer()));

                        }
                        String content1= investigateAnswerList.get(0).getContent();
                        List<String> list=new ArrayList<>();
                        List<String> list2 = Arrays.asList(content1.split(" / "));
                        for(String str:list2)
                        {
                            list.add(str);
                        }
                        if("4".equals(investigateQuestion.getSpecialTab())&& list2.size()>2){
                            List<String> list1 = Arrays.asList(list2.get(2).split("--"));
                            list.set(2,list1.get(0));
                            if (list1.size()>1){
                              String str=list1.get(1);
                              list.add(str);
                            }
                        }
                        if(investigateQuestion.getId().intValue()==investigatePublishShow.getQuestion().intValue())
                            for(int i=0;i<list.size();i++)
                            {
                                RespInvestigateAnswer respInvestigateAnswer=new RespInvestigateAnswer();
                                respInvestigateAnswer.setQuestion(investigateQuestion.getId()*10+i+1);
                                respInvestigateAnswer.setContent(list.get(i));
                                respInvestigateAnswerList.add(respInvestigateAnswer);
                            }
                        else {
                            for (int i = 0; i < list.size(); i++) {
                                RespInvestigateAnswer respInvestigateAnswer = new RespInvestigateAnswer();
                                respInvestigateAnswer.setQuestion(investigatePublishShow.getQuestion());
                                respInvestigateAnswer.setContent(list.get(i));
                                respInvestigateAnswerList.add(respInvestigateAnswer);
                            }
                        }
                    }
                }
                respInvestigateObject.setRespInvestigateAnswerList(respInvestigateAnswerList);
                if(obj==0)
                respInvestigateObjectList.add(respInvestigateObject);
            }
            for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList)
            {
                if(investigatePublishShow.getQuestion()!=question)
                {
                    investigatePublishShowList1.add(investigatePublishShow);
                }else{
                    if("1".equals(investigateQuestion.getSpecialTab())){
                        InvestigatePublishShow investigatePublishShow1=new InvestigatePublishShow();
                        investigatePublishShow1.setQuestion(investigateQuestion.getId()*10+1);
                        investigatePublishShow1.setPublish(publish);
                        investigatePublishShow1.setEnabled(1);
                        investigatePublishShow1.setContent("省");
                        investigatePublishShow1.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow1);
                    }
                    else if("2".equals(investigateQuestion.getSpecialTab())){
                        InvestigatePublishShow investigatePublishShow1=new InvestigatePublishShow();
                        investigatePublishShow1.setQuestion(investigateQuestion.getId()*10+1);
                        investigatePublishShow1.setPublish(publish);
                        investigatePublishShow1.setEnabled(1);
                        investigatePublishShow1.setContent("省");
                        investigatePublishShow1.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow1);
                        InvestigatePublishShow investigatePublishShow2=new InvestigatePublishShow();
                        investigatePublishShow2.setQuestion(investigateQuestion.getId()*10+2);
                        investigatePublishShow2.setPublish(publish);
                        investigatePublishShow2.setEnabled(1);
                        investigatePublishShow2.setContent("市");
                        investigatePublishShow2.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow2);
                    }
                    else if("3".equals(investigateQuestion.getSpecialTab())){
                        InvestigatePublishShow investigatePublishShow1=new InvestigatePublishShow();
                        investigatePublishShow1.setQuestion(investigateQuestion.getId()*10+1);
                        investigatePublishShow1.setPublish(publish);
                        investigatePublishShow1.setEnabled(1);
                        investigatePublishShow1.setContent("省");
                        investigatePublishShow1.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow1);
                        InvestigatePublishShow investigatePublishShow2=new InvestigatePublishShow();
                        investigatePublishShow2.setQuestion(investigateQuestion.getId()*10+2);
                        investigatePublishShow2.setPublish(publish);
                        investigatePublishShow2.setEnabled(1);
                        investigatePublishShow2.setContent("市");
                        investigatePublishShow2.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow2);
                        InvestigatePublishShow investigatePublishShow3=new InvestigatePublishShow();
                        investigatePublishShow3.setQuestion(investigateQuestion.getId()*10+3);
                        investigatePublishShow3.setPublish(publish);
                        investigatePublishShow3.setEnabled(1);
                        investigatePublishShow3.setContent("地区");
                        investigatePublishShow3.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow3);
                    }
                    else if("4".equals(investigateQuestion.getSpecialTab())){
                        InvestigatePublishShow investigatePublishShow1=new InvestigatePublishShow();
                        investigatePublishShow1.setQuestion(investigateQuestion.getId()*10+1);
                        investigatePublishShow1.setPublish(publish);
                        investigatePublishShow1.setEnabled(1);
                        investigatePublishShow1.setContent("省");
                        investigatePublishShow1.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow1);
                        InvestigatePublishShow investigatePublishShow2=new InvestigatePublishShow();
                        investigatePublishShow2.setQuestion(investigateQuestion.getId()*10+2);
                        investigatePublishShow2.setPublish(publish);
                        investigatePublishShow2.setEnabled(1);
                        investigatePublishShow2.setContent("市");
                        investigatePublishShow2.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow2);
                        InvestigatePublishShow investigatePublishShow3=new InvestigatePublishShow();
                        investigatePublishShow3.setQuestion(investigateQuestion.getId()*10+3);
                        investigatePublishShow3.setPublish(publish);
                        investigatePublishShow3.setEnabled(1);
                        investigatePublishShow3.setContent("地区");
                        investigatePublishShow3.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow3);
                        InvestigatePublishShow investigatePublishShow4=new InvestigatePublishShow();
                        investigatePublishShow4.setQuestion(investigateQuestion.getId()*10+4);
                        investigatePublishShow4.setPublish(publish);
                        investigatePublishShow4.setEnabled(1);
                        investigatePublishShow4.setContent("具体地址");
                        investigatePublishShow4.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow4);
                    }
                }
            }
            respShowQuestion.setInvestigatePublishShowList(investigatePublishShowList1);
            respShowQuestion.setRespInvestigateObjectList(respInvestigateObjectList);
        }
        //日期类
        else if(type==4){
            for(InvestigateObject investigateObject:investigateObjectList1){
                int obj=0;//标记人物，用于筛选
                List<RespInvestigateAnswer> respInvestigateAnswerList=new ArrayList<>();
                RespInvestigateObject respInvestigateObject=new RespInvestigateObject();
                BeanUtils.copyProperties(investigateObject,respInvestigateObject);
                for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList){
                    if(investigatePublishShow.getQuestion().intValue()==question&&content!=null&&!"".equals(content)){
                        String answerHql="from InvestigateAnswer where question="+investigatePublishShow.getQuestion()+" and objective="+investigateObject.getId()+ " and content like :content";
                        Map<String, Object> params= new HashMap<>();
                        params.put("content",content+"%");
                        List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,params);
                        if(investigateAnswerList.size()==0)
                            obj=1;
                    }
                    String answerHql="from InvestigateAnswer where question="+investigatePublishShow.getQuestion()+" and objective="+investigateObject.getId();
                    List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,null);
                    if(investigateAnswerList.size()>0){
                        if(investigateAnswerList.get(0).getAnswer()!=null&&!"".equals(investigateAnswerList.get(0).getAnswer())&&!"[]".equals(investigateAnswerList.get(0).getAnswer()))
                        {
                            investigateAnswerList.get(0).setContent(selectContentByAnswers(investigateAnswerList.get(0).getAnswer()));

                        }
                        String content1= investigateAnswerList.get(0).getContent();
                        if(investigatePublishShow.getQuestion()==question&&!"".equals(content1)) {
                            List<String> list = new ArrayList<>();
                            List<String> list1 = Arrays.asList(content1.split("年"));
                            if (list1.size() > 0)
                                list.add(list1.get(0) + "年");
                            if (list1.size() > 1) {
                                List<String> list2 = Arrays.asList(list1.get(1).split("月"));
                                list.add(list2.get(0) + "月");
                                if (list2.size() > 1)
                                    list.add(list2.get(1));
                            }
                            if(investigateQuestion.getId()==investigatePublishShow.getQuestion())
                                for (int i = 0; i < list.size(); i++) {
                                    RespInvestigateAnswer respInvestigateAnswer = new RespInvestigateAnswer();
                                    respInvestigateAnswer.setQuestion(investigateQuestion.getId() * 10 + i + 1);
                                    respInvestigateAnswer.setContent(list.get(i));
                                    respInvestigateAnswerList.add(respInvestigateAnswer);
                                }
                            else{
                                for (int i = 0; i < list.size(); i++) {
                                    RespInvestigateAnswer respInvestigateAnswer = new RespInvestigateAnswer();
                                    respInvestigateAnswer.setQuestion(investigatePublishShow.getQuestion());
                                    respInvestigateAnswer.setContent(list.get(i));
                                    respInvestigateAnswerList.add(respInvestigateAnswer);
                                }
                            }
                        }else {
                            RespInvestigateAnswer respInvestigateAnswer = new RespInvestigateAnswer();
                            respInvestigateAnswer.setQuestion(investigatePublishShow.getQuestion());
                            respInvestigateAnswer.setContent(content1);
                            respInvestigateAnswerList.add(respInvestigateAnswer);
                        }
                    }
                }
                respInvestigateObject.setRespInvestigateAnswerList(respInvestigateAnswerList);
                if(obj==0)
                    respInvestigateObjectList.add(respInvestigateObject);
            }
            for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList)
            {
                if(investigatePublishShow.getQuestion()!=question)
                {
                    investigatePublishShowList1.add(investigatePublishShow);
                }else{
                    if("1".equals(investigateQuestion.getSpecialTab())){
                        InvestigatePublishShow investigatePublishShow1=new InvestigatePublishShow();
                        investigatePublishShow1.setQuestion(investigateQuestion.getId()*10+1);
                        investigatePublishShow1.setPublish(publish);
                        investigatePublishShow1.setEnabled(1);
                        investigatePublishShow1.setContent("年");
                        investigatePublishShow1.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow1);
                    }
                    else if("2".equals(investigateQuestion.getSpecialTab())){
                        InvestigatePublishShow investigatePublishShow1=new InvestigatePublishShow();
                        investigatePublishShow1.setQuestion(investigateQuestion.getId()*10+1);
                        investigatePublishShow1.setPublish(publish);
                        investigatePublishShow1.setEnabled(1);
                        investigatePublishShow1.setContent("年");
                        investigatePublishShow1.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow1);
                        InvestigatePublishShow investigatePublishShow2=new InvestigatePublishShow();
                        investigatePublishShow2.setQuestion(investigateQuestion.getId()*10+2);
                        investigatePublishShow2.setPublish(publish);
                        investigatePublishShow2.setEnabled(1);
                        investigatePublishShow2.setContent("月");
                        investigatePublishShow2.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow2);
                    }
                    else if("3".equals(investigateQuestion.getSpecialTab())){
                        InvestigatePublishShow investigatePublishShow1=new InvestigatePublishShow();
                        investigatePublishShow1.setQuestion(investigateQuestion.getId()*10+1);
                        investigatePublishShow1.setPublish(publish);
                        investigatePublishShow1.setEnabled(1);
                        investigatePublishShow1.setContent("年");
                        investigatePublishShow1.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow1);
                        InvestigatePublishShow investigatePublishShow2=new InvestigatePublishShow();
                        investigatePublishShow2.setQuestion(investigateQuestion.getId()*10+2);
                        investigatePublishShow2.setPublish(publish);
                        investigatePublishShow2.setEnabled(1);
                        investigatePublishShow2.setContent("月");
                        investigatePublishShow2.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow2);
                        InvestigatePublishShow investigatePublishShow3=new InvestigatePublishShow();
                        investigatePublishShow3.setQuestion(investigateQuestion.getId()*10+3);
                        investigatePublishShow3.setPublish(publish);
                        investigatePublishShow3.setEnabled(1);
                        investigatePublishShow3.setContent("日");
                        investigatePublishShow3.setOrders(x);
                        x++;
                        investigatePublishShowList1.add(investigatePublishShow3);
                    }
                }
            }
            respShowQuestion.setInvestigatePublishShowList(investigatePublishShowList1);
            respShowQuestion.setRespInvestigateObjectList(respInvestigateObjectList);
        }
        return respShowQuestion;
    }

    @Override
    public RespShowQuestion exportExcelSubjectList(Integer id, String updateQuestionJson) {
        List<RespInvestigatePublishShow> investigatePublishShowList = JSONArray.parseArray(updateQuestionJson,RespInvestigatePublishShow.class);
        int x=1;
        for(RespInvestigatePublishShow investigatePublishShow:investigatePublishShowList)
        {
            InvestigateQuestion investigateQuestion=investigateQuestionDao.get(investigatePublishShow.getQuestion());
            investigatePublishShow.setPublish(id);
            investigatePublishShow.setEnabled(1);
            if(!"4".equals(investigateQuestion.getType()))
                investigatePublishShow.setContent(investigateQuestion.getContent());
            else{
                String hqlKey="from InvestigateQuestionKey where question="+investigateQuestion.getId();
                List<InvestigateQuestionKey> investigateQuestionKeyList=investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlKey,null);
                String content=investigateQuestion.getContent();
                for (InvestigateQuestionKey investigateQuestionKey:investigateQuestionKeyList){
                    content=content+" "+investigateQuestionKey.getOrders()+"."+investigateQuestionKey.getContent();
                }
                investigatePublishShow.setContent(content);
            }
            investigatePublishShow.setOrders(x);
            x++;
        }
        RespShowQuestion respShowQuestion =new RespShowQuestion();
        List<RespInvestigateObject> respInvestigateObjectList=new ArrayList<>();
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        int number=1;
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MINUTE,-investigatePublish.getModifyLimit());
        Date endDate=calendar.getTime();
        Map<String, Object> params1= new HashMap<>();
        params1.put("endDate",endDate);
        String hqlObject="from InvestigateObject where publish="+id+" and subsumable=1 and createDate < :endDate order by createDate desc";
        List<InvestigateObject> investigateObjectList=investigateObjectDao.getListByHQLWithNamedParams(hqlObject,params1);

        for(InvestigateObject investigateObject:investigateObjectList) {
            RespInvestigateObject respInvestigateObject = new RespInvestigateObject();
            BeanUtils.copyProperties(investigateObject, respInvestigateObject);
            for (InvestigatePublishShow investigatePublishShow : investigatePublishShowList) {
                InvestigateQuestion investigateQuestion = investigateQuestionDao.get(investigatePublishShow.getQuestion());
                List<InvestigateQuestionKey> investigateQuestionKeyList = new ArrayList<>();
                if ("4".equals(investigateQuestion.getType())) {
                    String hqlKey = "from InvestigateQuestionKey where question=" + investigateQuestion.getId();
                    List<InvestigateQuestionKey> investigateQuestionKeys = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlKey, null);
                    for (InvestigateQuestionKey investigateQuestionKey : investigateQuestionKeys) {
                        investigateQuestionKeyList.add(investigateQuestionKey);
                    }
                }
                String answerHql = "from InvestigateAnswer where question=" + investigatePublishShow.getQuestion() + " and objective=" + investigateObject.getId();
                List<InvestigateAnswer> investigateAnswerList = investigateAnswerDao.getListByHQLWithNamedParams(answerHql, null);
                if (investigateAnswerList.size() > 0) {
                    if (investigateAnswerList.get(0).getAnswer() != null && !"".equals(investigateAnswerList.get(0).getAnswer()) && !"[]".equals(investigateAnswerList.get(0).getAnswer())) {
                        investigateAnswerList.get(0).setContent(selectContentByAnswers(investigateAnswerList.get(0).getAnswer()));
                        if ("4".equals(investigateQuestion.getType())) {
                            String content = "";
                            List<String> list = Arrays.asList(investigateAnswerList.get(0).getAnswer().split(","));
                            for (int i = 0; i < list.size(); i++) {
                                for (int j = 0; j < investigateQuestionKeyList.size(); j++) {
                                    if (Integer.valueOf(list.get(i)).intValue() == investigateQuestionKeyList.get(j).getId()) {
                                        content = content + " " + investigateQuestionKeyList.get(j).getOrders();
                                    }
                                }
                            }
                            investigateAnswerList.get(0).setContent(content);
                        }
                    }
                }
            }
        }
        for(InvestigateObject investigateObject:investigateObjectList){
            RespInvestigateObject respInvestigateObject=new RespInvestigateObject();
            BeanUtils.copyProperties(investigateObject,respInvestigateObject);
            for(RespInvestigatePublishShow investigatePublishShow:investigatePublishShowList){
                InvestigateQuestion investigateQuestion = investigateQuestionDao.get(investigatePublishShow.getQuestion());
                List<RespInvestigateAnswer> investigateAnswerList1=investigatePublishShow.getInvestigateAnswerList();
                if(investigateAnswerList1==null)
                    investigateAnswerList1=new ArrayList<>();

                String answerHql="from InvestigateAnswer where question="+investigatePublishShow.getQuestion()+" and objective="+investigateObject.getId();
                List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerHql,null);
                if(investigateAnswerList.size()>0){
                    String content="";
                    if("9".equals(investigateQuestion.getType())&&investigateQuestion.getIsMultiple()>0){
                        RespInvestigateAnswer inanswer=new RespInvestigateAnswer();
                        for(InvestigateAnswer investigateAnswer :investigateAnswerList){
                            content=content+" "+investigateAnswer.getContent();
                            BeanUtils.copyProperties(investigateAnswer,inanswer);
                        }
                        inanswer.setContent(content);
                        investigateAnswerList1.add(inanswer);
                    }else {
                        for(InvestigateAnswer investigateAnswer:investigateAnswerList) {
                            RespInvestigateAnswer inanswer=new RespInvestigateAnswer();
                            BeanUtils.copyProperties(investigateAnswer,inanswer);
                            investigateAnswerList1.add(inanswer);
                        }
                    }
                }
                else{
                    RespInvestigateAnswer inanswer=new RespInvestigateAnswer();
                    inanswer.setContent("");
                    inanswer.setQuestion(investigatePublishShow.getQuestion());
                    inanswer.setObjective(investigateObject.getId());
                    investigateAnswerList1.add(inanswer);
                }
                investigatePublishShow.setInvestigateAnswerList(investigateAnswerList1);
            }
            respInvestigateObjectList.add(respInvestigateObject);
        }
        respShowQuestion.setRespInvestigateObjectList(respInvestigateObjectList);
        respShowQuestion.setRespInvestigatePublishShowList(investigatePublishShowList);
        return respShowQuestion;
    }

    @Override
    public List<RespInvestigateQuestion> selectQuestionOptionsList(Integer id) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        InvestigateSubject investigateSubject=investigateSubjectDao.get(investigatePublish.getSubject());
        String hql1="from InvestigatePublishShow where publish="+id;
        List<InvestigatePublishShow> investigatePublishShowList=investigatePublishShowDao.getListByHQLWithNamedParams(hql1,null);

        List<RespInvestigateQuestion> respInvestigateQuestionList=new ArrayList<>();

        String hql = "from InvestigateQuestion where subject="+investigateSubject.getId();
        List<InvestigateQuestion> list = investigateQuestionDao.getListByHQLWithNamedParams(hql,null);
        for(InvestigateQuestion investigateQuestion:list)
        {
            RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
            BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
            respInvestigateQuestion.setIsShow(0);
            for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList){
                if(investigateQuestion.getId()==investigatePublishShow.getQuestion())
                    respInvestigateQuestion.setIsShow(1);
            }
            respInvestigateQuestionList.add(respInvestigateQuestion);
        }
        return respInvestigateQuestionList;
    }

    @Override
    public int updateShowQuestion(User user, Integer id, String updateQuestionJson) {
        Date newDate=new Date();
        String hql1="from InvestigatePublishShow where publish="+id;
        List<InvestigatePublishShow> investigatePublishShowList=investigatePublishShowDao.getListByHQLWithNamedParams(hql1,null);
        List<InvestigatePublishShow> updateList = JSONArray.parseArray(updateQuestionJson,InvestigatePublishShow.class);
        for(InvestigatePublishShow investigatePublishShow:updateList)
        {
            int type=0;
            for(InvestigatePublishShow investigatePublishShowOld:investigatePublishShowList)
            {
                if(investigatePublishShow.getQuestion()==investigatePublishShowOld.getQuestion())
                {
                    type=1;
                    break;
                }
            }
            if(type==0)
            {
                InvestigateQuestion investigateQuestion=investigateQuestionDao.get(investigatePublishShow.getQuestion());
                investigatePublishShow.setPublish(id);
                investigatePublishShow.setEnabled(1);
                investigatePublishShow.setOrg(user.getOid());
                if(!"4".equals(investigateQuestion.getType()))
                    investigatePublishShow.setContent(investigateQuestion.getContent());
                else{
                    String hqlKey="from InvestigateQuestionKey where question="+investigateQuestion.getId();
                    List<InvestigateQuestionKey> investigateQuestionKeyList=investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlKey,null);
                    String content=investigateQuestion.getContent();
                    for (InvestigateQuestionKey investigateQuestionKey:investigateQuestionKeyList){
                        content=content+" "+investigateQuestionKey.getOrders()+"."+investigateQuestionKey.getContent();
                    }
                    investigatePublishShow.setContent(content);
                }
                investigatePublishShow.setCreator(user.getUserID());
                investigatePublishShow.setCreateDate(newDate);
                investigatePublishShow.setCreateName(user.getUserName());
                investigatePublishShowDao.save(investigatePublishShow);
            }
        }
        for(InvestigatePublishShow investigatePublishShowOld:investigatePublishShowList)
        {
            int type=0;
            for(InvestigatePublishShow investigatePublishShow:updateList)
            {
                if(investigatePublishShow.getQuestion()==investigatePublishShowOld.getQuestion())
                {
                    type=1;
                    break;
                }
            }
            if(type==0)
            {
                investigatePublishShowDao.delete(investigatePublishShowOld);
            }
        }
        List<InvestigatePublishShow> investigatePublishShowList1=investigatePublishShowDao.getListByHQLWithNamedParams(hql1,null);
        int order=1;
        for(InvestigatePublishShow investigatePublishShow:investigatePublishShowList1)
        {
            investigatePublishShow.setOrders(order);
            order++;
            investigatePublishShowDao.update(investigatePublishShow);
        }
        return 1;
    }

    @Override
    public int updateObjectSubsumable(Integer id, Integer type) {
        InvestigateObject investigateObject=investigateObjectDao.get(id);
        investigateObject.setSubsumable(type);
        investigateObjectDao.update(investigateObject);

        String hql2 = "from InvestigateAnswer where objective="+investigateObject.getId();
        List<InvestigateAnswer> investigateAnswerList1= investigateAnswerDao.getListByHQL(hql2, null);
        for(InvestigateAnswer investigateAnswer:investigateAnswerList1) {
            this.investigateAnswerQuestionStat(investigateObject.getPublish(), investigateAnswer.getQuestion(), type, investigateObject.getOrg());
            if(investigateAnswer.getAnswer()!=null&&!"".equals(investigateAnswer.getAnswer())&&!"[]".equals(investigateAnswer.getAnswer()))
            {
                String[] split = investigateAnswer.getAnswer().split(",");
                for (int i = 0; i < split.length; i++) {
                    int answer=Integer.valueOf(split[i]).intValue();
                    this.investigateAnswerAnswerStat(investigateObject.getPublish(),answer,type,investigateObject.getOrg());
                }
            }
        }
        return 1;
    }

    @Override
    public RespInvestigateSubject selectObjectById(Integer id) {
        InvestigateObject investigateObject=investigateObjectDao.get(id);
        RespInvestigateSubject respInvestigateSubject=investigationService.selectSubject(investigateObject.getSubject());
        respInvestigateSubject.setObjective(id);
        InvestigatePublish investigatePublish=investigatePublishDao.get(investigateObject.getPublish());
        respInvestigateSubject.setPublishEnabled(investigatePublish.getEnabled());
        Date tomorrow= NewDateUtils.tomorrow(investigatePublish.getEndTime());
        Date newDate=new Date();
        if(tomorrow.getTime()>newDate.getTime()) {
            Calendar calendar=Calendar.getInstance();
            calendar.setTime(investigateObject.getCreateDate());
            calendar.add(Calendar.MINUTE,investigatePublish.getModifyLimit());
            Date endTime=calendar.getTime();
            if(endTime.getTime()>newDate.getTime())
                respInvestigateSubject.setStatus(1);
            else
                respInvestigateSubject.setStatus(2);
        }
        else
            respInvestigateSubject.setStatus(2);
        for(RespInvestigateSubjectTag respInvestigateSubjectTag: respInvestigateSubject.getRespInvestigateSubjectTagList()){
            for(RespInvestigateQuestion investigateQuestion:respInvestigateSubjectTag.getInvestigateQuestionList())
            {
                List<InvestigateAnswer> investigateAnswerLists=new ArrayList<>();
                String hql2 = "from InvestigateAnswer where question="+investigateQuestion.getId()+" and objective="+id;
                List<InvestigateAnswer> investigateAnswerList= investigateAnswerDao.getListByHQLWithNamedParams(hql2, null);
                for(InvestigateAnswer investigateAnswer :investigateAnswerList){
                    InvestigateAnswer newInvestigateAnswer=new InvestigateAnswer();
                    BeanUtils.copyProperties(investigateAnswer,newInvestigateAnswer);
                    if(newInvestigateAnswer.getAnswer()!=null&&!"".equals(newInvestigateAnswer.getAnswer())&&!"[]".equals(newInvestigateAnswer.getAnswer()))
                    {
                        newInvestigateAnswer.setContent(selectContentByAnswers(newInvestigateAnswer.getAnswer()));
                    }
                    investigateAnswerLists.add(newInvestigateAnswer);
                }
                investigateQuestion.setInvestigateAnswerList(investigateAnswerLists);
            }
        }
        return respInvestigateSubject;
    }

    @Override
    public InvestigatePublish selectObjectIdByPublishId(Integer id) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        return investigatePublish;
    }

    @Override
    public InvestigateObject addInvestigateObject(ReqInvestigateObject reqInvestigateObject) {
        InvestigateObject investigateObject=new InvestigateObject();
        investigateObject.setPublish(reqInvestigateObject.getPublish());
        List<InvestigateAnswer> investigateAnswerList = JSONArray.parseArray(reqInvestigateObject.getInvestigateAnswerJson(), InvestigateAnswer.class);

        Date newDate=new Date();
        InvestigatePublish investigatePublish=investigatePublishDao.get(reqInvestigateObject.getPublish());
        InvestigateSubject investigateSubject=investigateSubjectDao.get(investigatePublish.getSubject());
        investigateObject.setOrg(investigateSubject.getOrg());
        investigateObject.setSubject(investigateSubject.getId());
        investigateObject.setSubsumable(1);
        investigateObject.setCreateDate(newDate);
        investigateObject.setHashKey(reqInvestigateObject.getHashKey());
        investigateObjectDao.save(investigateObject);

        InvestigateObjectHistory investigateObjectHistory=new InvestigateObjectHistory();
        BeanUtils.copyProperties(investigateObject,investigateObjectHistory);
        investigateObjectHistory.setId(null);
        investigateObjectHistory.setObjective(investigateObject.getId());
        investigateObjectHistory.setCreateDate(new Date());
        investigateObjectHistoryDao.save(investigateObjectHistory);
        List<Integer> questionList=new ArrayList<>();
        for(InvestigateAnswer investigateAnswer: investigateAnswerList){
            if(investigateAnswer.getContent()!=null&&!"".equals(investigateAnswer.getContent()))
            {
                int x=0;
                for(Integer i:questionList){
                    if(investigateAnswer.getQuestion()==i){
                        x=1;
                    }
                }
                if(x==0) {
                    this.investigateAnswerQuestionStat(reqInvestigateObject.getPublish(), investigateAnswer.getQuestion(), 1, investigateSubject.getOrg());
                    questionList.add(investigateAnswer.getQuestion());
                }
            }
            if(investigateAnswer.getAnswer()!=null&&!"".equals(investigateAnswer.getAnswer())&&!"[]".equals(investigateAnswer.getAnswer()))
            {
                String[] split = investigateAnswer.getAnswer().split(",");
                for (int i = 0; i < split.length; i++) {
                    int answer=Integer.valueOf(split[i]).intValue();
                    this.investigateAnswerAnswerStat(reqInvestigateObject.getPublish(),answer,1,investigateSubject.getOrg());
                }
                investigateAnswer.setContent(null);
            }
            investigateAnswer.setOrg(investigateObject.getOrg());
            investigateAnswer.setObjective(investigateObject.getId());
            investigateAnswer.setCreateDate(newDate);
            investigateAnswerDao.save(investigateAnswer);

            InvestigateAnswerHistory investigateAnswerHistory=new InvestigateAnswerHistory();
            BeanUtils.copyProperties(investigateAnswer,investigateAnswerHistory);
            investigateAnswerHistory.setId(null);
            investigateAnswerHistory.setObjectiveHistory(investigateObjectHistory.getId());
            investigateAnswerHistory.setCreateDate(new Date());
            investigateAnswerHistoryDao.save(investigateAnswerHistory);
        }
        return investigateObject;
    }

    @Override
    public InvestigateObject updateInvestigateObject(ReqInvestigateObject reqInvestigateObject) {
        InvestigateObject investigateObject=investigateObjectDao.get(reqInvestigateObject.getObjective());
        String hql2 = "from InvestigateAnswer where objective="+investigateObject.getId();
        List<InvestigateAnswer> investigateAnswerList1= investigateAnswerDao.getListByHQL(hql2, null);
        List<Integer> questionList=new ArrayList<>();
        for(InvestigateAnswer investigateAnswer:investigateAnswerList1)
        {
            if(investigateAnswer.getContent()!=null&&!"".equals(investigateAnswer.getContent()) ||(investigateAnswer.getAnswer()!=null&&!"".equals(investigateAnswer.getAnswer())&&!"[]".equals(investigateAnswer.getAnswer())))
            {
                int x=0;
                for(Integer i:questionList){
                    if(investigateAnswer.getQuestion()==i){
                        x=1;
                    }
                }
                if(x==0) {
                    this.investigateAnswerQuestionStat(reqInvestigateObject.getPublish(),investigateAnswer.getQuestion(),0,investigateObject.getOrg());
                    questionList.add(investigateAnswer.getQuestion());
                }
            }
            if(investigateAnswer.getAnswer()!=null&&!"".equals(investigateAnswer.getAnswer())&&!"[]".equals(investigateAnswer.getAnswer()))
            {
                String[] split = investigateAnswer.getAnswer().split(",");
                for (int i = 0; i < split.length; i++) {
                    int answer=Integer.valueOf(split[i]).intValue();
                    this.investigateAnswerAnswerStat(reqInvestigateObject.getPublish(),answer,0,investigateObject.getOrg());
                }
            }
            investigateAnswerDao.delete(investigateAnswer);
        }
        List<InvestigateAnswer> investigateAnswerList = JSONArray.parseArray(reqInvestigateObject.getInvestigateAnswerJson(), InvestigateAnswer.class);
        Date newDate=new Date();
        investigateObject.setCreateDate(newDate);
        investigateObjectDao.update(investigateObject);

        InvestigateObjectHistory investigateObjectHistory=new InvestigateObjectHistory();
        BeanUtils.copyProperties(investigateObject,investigateObjectHistory);
        investigateObjectHistory.setId(null);
        investigateObjectHistory.setObjective(investigateObject.getId());
        investigateObjectHistory.setCreateDate(new Date());
        investigateObjectHistoryDao.save(investigateObjectHistory);
        List<Integer> questionList1=new ArrayList<>();
        for(InvestigateAnswer investigateAnswer: investigateAnswerList){
            if(investigateAnswer.getContent()!=null&&!"".equals(investigateAnswer.getContent()))
            {
                int x=0;
                for(Integer i:questionList1){
                    if(investigateAnswer.getQuestion()==i){
                        x=1;
                    }
                }
                if(x==0) {
                    this.investigateAnswerQuestionStat(reqInvestigateObject.getPublish(),investigateAnswer.getQuestion(),1,investigateObject.getOrg());
                    questionList1.add(investigateAnswer.getQuestion());
                }
            }
            if(investigateAnswer.getAnswer()!=null&&!"".equals(investigateAnswer.getAnswer())&&!"[]".equals(investigateAnswer.getAnswer()))
            {
                String[] split = investigateAnswer.getAnswer().split(",");
                for (int i = 0; i < split.length; i++) {
                    int answer=Integer.valueOf(split[i]).intValue();
                    this.investigateAnswerAnswerStat(reqInvestigateObject.getPublish(),answer,1,investigateObject.getOrg());
                }
                investigateAnswer.setContent(null);
            }
            investigateAnswer.setOrg(investigateObject.getOrg());
            investigateAnswer.setObjective(investigateObject.getId());
            investigateAnswer.setCreateDate(newDate);
            investigateAnswerDao.save(investigateAnswer);

            InvestigateAnswerHistory investigateAnswerHistory=new InvestigateAnswerHistory();
            BeanUtils.copyProperties(investigateAnswer,investigateAnswerHistory);
            investigateAnswerHistory.setId(null);
            investigateAnswerHistory.setObjectiveHistory(investigateObjectHistory.getId());
            investigateAnswerHistory.setCreateDate(new Date());
            investigateAnswerHistoryDao.save(investigateAnswerHistory);
        }
        return investigateObject;
    }

    @Override
    public List<InvestigateObjectHistory> investigateObjectHistoryList(Integer id) {
        String hql = "from InvestigateObjectHistory where objective=" +id+ " order by createDate desc";
        List<InvestigateObjectHistory> investigateObjectHistoryList= investigateObjectHistoryDao.getListByHQLWithNamedParams(hql, null);
        return investigateObjectHistoryList;
    }
    public List<RespInvestigateObject> investigateObjectOtherList(Integer id,String hashKey) {
        List<RespInvestigateObject>  respInvestigateObjectList= new ArrayList<>();
        String hql = "from InvestigateObject where id   !=" +id+ " and hashKey='"+hashKey+"' order by createDate desc";
        List<InvestigateObject> investigateObjectList= investigateObjectDao.getListByHQLWithNamedParams(hql, null);
        for(InvestigateObject investigateObject:investigateObjectList)
        {
            RespInvestigateObject respInvestigateObject=new RespInvestigateObject();
            BeanUtils.copyProperties(investigateObject,respInvestigateObject);
            respInvestigateObject.setOrgName(orgService.orgName(investigateObject.getOrg()));
            respInvestigateObjectList.add(respInvestigateObject);
        }

        return respInvestigateObjectList;
    }

    @Override
    public RespInvestigateSubject investigateAnswerHistoryDetails(Integer id) {
        InvestigateObjectHistory investigateObjectHistory=investigateObjectHistoryDao.get(id);
        RespInvestigateSubject respInvestigateSubject=new RespInvestigateSubject();
        InvestigateSubject investigateSubject=investigateSubjectDao.get(investigateObjectHistory.getSubject());
        InvestigatePublish investigatePublish=investigatePublishDao.get(investigateObjectHistory.getPublish());
        Date tomorrow=NewDateUtils.tomorrow(investigatePublish.getEndTime());
        Date newDate=new Date();
        if(tomorrow.getTime()>newDate.getTime())
            respInvestigateSubject.setStatus(1);
        else
            respInvestigateSubject.setStatus(2);
        BeanUtils.copyProperties(investigateSubject,respInvestigateSubject);
        List<RespInvestigateSubjectTag> respInvestigateSubjectTagList=new ArrayList<>();
        String hql = "from InvestigateSubjectTag where subject=" +investigateSubject.getId();
        List<InvestigateSubjectTag> investigateSubjectTagList= investigateSubjectTagDao.getListByHQLWithNamedParams(hql, null);
        for(InvestigateSubjectTag investigateSubjectTag:investigateSubjectTagList){
            RespInvestigateSubjectTag respInvestigateSubjectTag=new RespInvestigateSubjectTag();
            BeanUtils.copyProperties(investigateSubjectTag,respInvestigateSubjectTag);
            List<RespInvestigateQuestion> respInvestigateQuestionList=new ArrayList<>();
            String hql1 = "from InvestigateQuestion where tag=" +investigateSubjectTag.getId()+" order by orders asc";
            List<InvestigateQuestion> investigateQuestionList= investigateQuestionDao.getListByHQLWithNamedParams(hql1, null);
            for(InvestigateQuestion investigateQuestion:investigateQuestionList){
                RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
                BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);

                String hql2 = "from InvestigateAnswerHistory where question="+investigateQuestion.getId()+" and objectiveHistory="+id;
                List<InvestigateAnswerHistory> investigateAnswerHistoryList= investigateAnswerHistoryDao.getListByHQL(hql2, null);
                List<InvestigateAnswer> investigateAnswerList=new ArrayList<>();
                for(InvestigateAnswerHistory investigateAnswerHistory:investigateAnswerHistoryList)
                {
                    InvestigateAnswer investigateAnswer=new InvestigateAnswer();
                    BeanUtils.copyProperties(investigateAnswerHistory,investigateAnswer);
                    if(investigateAnswer.getAnswer()!=null&&!"".equals(investigateAnswer.getAnswer())&&!"[]".equals(investigateAnswer.getAnswer()))
                    {
                        investigateAnswer.setContent(selectContentByAnswers(investigateAnswer.getAnswer()));
                    }
                    investigateAnswerList.add(investigateAnswer);
                }
                String hql3 = "from InvestigateQuestionKey where question=" +investigateQuestion.getId()+" order by orders asc";
                List<InvestigateQuestionKey> investigateQuestionKeyList= investigateQuestionKeyDao.getListByHQLWithNamedParams(hql3, null);
                respInvestigateQuestion.setInvestigateQuestionKeyList(investigateQuestionKeyList);
                respInvestigateQuestion.setInvestigateAnswerList(investigateAnswerList);
                respInvestigateQuestionList.add(respInvestigateQuestion);
            }
            respInvestigateSubjectTag.setInvestigateQuestionList(respInvestigateQuestionList);
            respInvestigateSubjectTagList.add(respInvestigateSubjectTag);
        }
        respInvestigateSubject.setRespInvestigateSubjectTagList(respInvestigateSubjectTagList);
        return respInvestigateSubject;
    }
    public String selectContentByAnswers(String answers){
        String content="";
            String[] result = answers.split(",");
            for (String r : result) {
                Integer id = Integer.valueOf(r);
                InvestigateQuestionKey investigateQuestionKey = investigateQuestionKeyDao.get(id);
                content = content + investigateQuestionKey.getContent();
            }
        return content;
    }
    @Override
    public RespInvestigateQrList selectInvestigateQr(User user, Integer id) {
        RespInvestigateQrList respInvestigateQrList=new RespInvestigateQrList();
        String hql="from InvestigateQr where publish="+id;
        List<InvestigateQr> list=investigateQrDao.getListByHQLWithNamedParams(hql,null);
        respInvestigateQrList.setInvestigateQrs(list);
        respInvestigateQrList.setSum(list.size());
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        Date tomorrow= NewDateUtils.tomorrow(investigatePublish.getEndTime());
        Date newDate=new Date();
        if(tomorrow.getTime()>newDate.getTime()) {
            respInvestigateQrList.setEndTime(investigatePublish.getEndTime());
            respInvestigateQrList.setCreateTime(investigatePublish.getQrGeneraeTime());
        }
        return respInvestigateQrList;
    }

    @Override
    public Integer selectHashKey(String hashKey, int id) {
        String hql = "from InvestigateObject where hashKey='"+hashKey+"'and publish=" + id;
        List<InvestigateObject> investigateObjects= investigateObjectDao.getListByHQLWithNamedParams(hql, null);
        if (investigateObjects.isEmpty())
        {
            return 0;
        }
        else{
            return investigateObjects.get(0).getId();
        }
    }

    @Override
    public void initializationQr(Integer id) {
        Date today= NewDateUtils.today(new Date());
        String hql="from InvestigateQr where qrTerminateTime is null and publish="+id;
        List<InvestigateQr> list=investigateQrDao.getListByHQLWithNamedParams(hql,null);
        for(InvestigateQr investigateQr:list)
        {
            int compareTo = investigateQr.getQrDeadline().compareTo(today);
           if(compareTo==-1)
           {
               investigateQr.setQrTerminateTime(investigateQr.getQrDeadline());
               investigateQr.setUpdateName("系统");
               investigateQr.setUpdateDate(investigateQr.getQrDeadline());
               investigateQrDao.update(investigateQr);
           }
        }
    }

    @Override
    public void exportExcel(RespShowQuestion respShowQuestion, Integer id, HttpServletResponse response) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        InvestigateSubject investigateSubject=investigateSubjectDao.get(investigatePublish.getSubject());
        ExportExcel.getValue(respShowQuestion,investigateSubject,response);
//测试6万行分页
//        RespShowQuestion respShowQuestion1=new RespShowQuestion();
//        List<InvestigatePublishShow> investigatePublishShowList=new ArrayList<>();
//        InvestigatePublishShow investigatePublishShow=new InvestigatePublishShow();
//        investigatePublishShow.setContent("姓名");
//        investigatePublishShowList.add(investigatePublishShow);
//        List<RespInvestigateObject> respInvestigateObjectList=new ArrayList<>();
//        for(int i=0;i<100000;i++)
//        {
//            RespInvestigateObject respInvestigateObject=new RespInvestigateObject();
//            respInvestigateObject.setShowValue1(i+"");
//            respInvestigateObject.setCreateDate(new Date());
//            respInvestigateObjectList.add(respInvestigateObject);
//        }
//        respShowQuestion1.setInvestigatePublishShowList(investigatePublishShowList);
//        respShowQuestion1.setRespInvestigateObjectList(respInvestigateObjectList);
//        ExportExcel.getValue(respShowQuestion1,investigateSubject,response);
    }

    @Override
    public void exportExcelQuestionStatistics(Integer id,Integer type,RespInvestigateQuestionStatistics respInvestigateQuestionStatistics, HttpServletResponse response) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(id);
        InvestigateSubject investigateSubject=investigateSubjectDao.get(investigatePublish.getSubject());
        if(type==1)
            ExportExcel.getValue1(respInvestigateQuestionStatistics,investigateSubject,response);
        else if(type==2)
            ExportExcel.getValue2(respInvestigateQuestionStatistics,investigateSubject,response);
        else if (type==3)
            ExportExcel.getValue3(respInvestigateQuestionStatistics,investigateSubject,response);
    }

    @Override
    public void exportExcelEssayQuestion(Integer piblish, Integer question, Integer type, RespShowQuestion respShowQuestion,String content, HttpServletResponse response) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(piblish);
        InvestigateSubject investigateSubject=investigateSubjectDao.get(investigatePublish.getSubject());
        if(type==1)
            ExportExcel.getValue8(respShowQuestion,content,investigateSubject,response);
        else if(type==3 || type==4)
            ExportExcel.getValue9(respShowQuestion,investigateSubject,response);
    }

    @Override
    public void exportExcelMultipleChoiceQuestion(Integer piblish, Integer question, RespMultipleChoiceQuestion respMultipleChoiceQuestion, HttpServletResponse response) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(piblish);
        InvestigateSubject investigateSubject=investigateSubjectDao.get(investigatePublish.getSubject());
        ExportExcel.getValue7(respMultipleChoiceQuestion,investigateSubject,response);
    }

    @Override
    public void investigateAnswerQuestionStat(int publish,int question, int type,int org) {
        String hql="from InvestigateAnswerStat where question="+question+" and publish="+publish;
        List<InvestigateAnswerStat> list=investigateAnswerStatDao.getListByHQLWithNamedParams(hql,null);
        if(list.isEmpty()){
            InvestigateAnswerStat investigateAnswerStat=new InvestigateAnswerStat();
            investigateAnswerStat.setQuestion(question);
            investigateAnswerStat.setPublish(publish);
            investigateAnswerStat.setQuestionCount(1);
            investigateAnswerStat.setCreateDate(new Date());
            investigateAnswerStat.setOrg(org);
            investigateAnswerStatDao.save(investigateAnswerStat);
        }else{
            if(type==1) {
                list.get(0).setQuestionCount(list.get(0).getQuestionCount() + 1);
            }else{
                list.get(0).setQuestionCount(list.get(0).getQuestionCount() - 1);
            }
            investigateAnswerStatDao.update(list.get(0));
        }
    }

    @Override
    public void investigateAnswerAnswerStat(int publish, int answer, int type, int org) {
        String hql="from InvestigateAnswerStat where answerKey="+answer+" and publish="+publish;
        List<InvestigateAnswerStat> list=investigateAnswerStatDao.getListByHQLWithNamedParams(hql,null);
        if(list.isEmpty()){
            InvestigateAnswerStat investigateAnswerStat=new InvestigateAnswerStat();
            investigateAnswerStat.setAnswerKey(answer);
            investigateAnswerStat.setPublish(publish);
            investigateAnswerStat.setKeyCount(1);
            investigateAnswerStat.setCreateDate(new Date());
            investigateAnswerStat.setOrg(org);
            investigateAnswerStatDao.save(investigateAnswerStat);
        }else{
            if(type==1) {
                list.get(0).setKeyCount(list.get(0).getKeyCount() + 1);
            }else{
                list.get(0).setKeyCount(list.get(0).getKeyCount() - 1);
            }
            investigateAnswerStatDao.update(list.get(0));
        }
    }

    @Override
    public RespAnswerStatistics investigateAnswerStatistics(int publish) {
        RespAnswerStatistics respAnswerStatistics=new RespAnswerStatistics();
        InvestigatePublish investigatePublish= investigatePublishDao.get(publish);
        String hql="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and type in(1,9)";
        List<InvestigateQuestion> list=investigateQuestionDao.getListByHQLWithNamedParams(hql,null);
        respAnswerStatistics.setEssayQuestionCount(list.size());

        String hql1="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and type in(2,3)";
        List<InvestigateQuestion> list1=investigateQuestionDao.getListByHQLWithNamedParams(hql1,null);
        respAnswerStatistics.setSingleChoiceCount(list1.size());

        String hql2="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and type =4";
        List<InvestigateQuestion> list2=investigateQuestionDao.getListByHQLWithNamedParams(hql2,null);
        respAnswerStatistics.setMultipleChoiceCount(list2.size());

        String hql3="from InvestigateObject where publish="+publish+" and subsumable=1 ";
        List<InvestigateObject> list3 = investigateObjectDao.getListByHQLWithNamedParams(hql3,null);
        respAnswerStatistics.setObjectCount(list3.size());
        return respAnswerStatistics;
    }

    @Override
    public void updateInvestigateAnswerStatistics(int publish) {
        InvestigatePublish investigatePublish=investigatePublishDao.get(publish);
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MINUTE,-investigatePublish.getModifyLimit());
        Date endDate=calendar.getTime();
        Map<String, Object> params1= new HashMap<>();
        params1.put("endDate",endDate);
        String objhql="from InvestigateObject where publish="+publish+" and subsumable=1  and createDate < :endDate" ;
        List<InvestigateObject> investigateObjectList=investigateObjectDao.getListByHQLWithNamedParams(objhql,params1);
        String questionHql="from InvestigateQuestion where subject="+investigatePublish.getSubject();
        List<InvestigateQuestion> investigateQuestionList=investigateQuestionDao.getListByHQLWithNamedParams(questionHql,null);
        for(InvestigateQuestion investigateQuestion:investigateQuestionList){
            String answerhql="from InvestigateAnswer where question="+investigateQuestion.getId() ;
            List<InvestigateAnswer> investigateAnswerList=investigateAnswerDao.getListByHQLWithNamedParams(answerhql,null);
            int sum=0;
            List<InvestigateObject> investigateObjectList1 = new ArrayList<>();
            for(InvestigateAnswer investigateAnswer:investigateAnswerList) {
                for (InvestigateObject investigateObject : investigateObjectList) {
                    if (investigateObject.getId().intValue() ==investigateAnswer.getObjective().intValue()&&!"".equals(investigateAnswer.getContent()))
                    {
                        int x=0;
                        for (InvestigateObject investigateObject1 : investigateObjectList1) {
                            if (investigateObject.getId().intValue() ==investigateObject1.getId().intValue() ){
                                x=1;
                            }
                        }
                        if(x==0)
                        {
                            investigateObjectList1.add(investigateObject);
                        }
                    }
                }
            }
            sum=investigateObjectList1.size();
            String statHql="from InvestigateAnswerStat where question="+investigateQuestion.getId()+" and publish="+publish;
            List<InvestigateAnswerStat> list=investigateAnswerStatDao.getListByHQLWithNamedParams(statHql,null);
            if(list.isEmpty()){
                InvestigateAnswerStat investigateAnswerStat=new InvestigateAnswerStat();
                investigateAnswerStat.setQuestion(investigateQuestion.getId());
                investigateAnswerStat.setPublish(publish);
                investigateAnswerStat.setQuestionCount(sum);
                investigateAnswerStat.setCreateDate(new Date());
                investigateAnswerStat.setOrg(investigatePublish.getOrg());
                investigateAnswerStatDao.save(investigateAnswerStat);
            }else{
                InvestigateAnswerStat investigateAnswerStat=  list.get(0);
                investigateAnswerStat.setQuestionCount(sum);
                investigateAnswerStatDao.update(investigateAnswerStat);
            }
        }
    }

    @Override
    public RespInvestigateQuestionStatistics investigateQuestionStatistics(int publish, int type,PageInfo pageInfo) {
        RespInvestigateQuestionStatistics respInvestigateQuestionStatistics=new RespInvestigateQuestionStatistics();
        InvestigatePublish investigatePublish= investigatePublishDao.get(publish);
        String hql="from InvestigateQuestion where subject="+investigatePublish.getSubject();
        List<InvestigateQuestion> list=investigateQuestionDao.getListByHQLWithNamedParams(hql,null);
        respInvestigateQuestionStatistics.setSum(list.size());
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MINUTE,-investigatePublish.getModifyLimit());
        Date endDate=calendar.getTime();
        Map<String, Object> params1= new HashMap<>();
        params1.put("endDate",endDate);
        String hql1="from InvestigateObject where publish="+publish+"  and subsumable=1  and createDate < :endDate" ;
        List<InvestigateObject> list1 = investigateObjectDao.getListByHQLWithNamedParams(hql1,params1);
        respInvestigateQuestionStatistics.setObjectSum(list1.size());
        List<RespInvestigateQuestion> respInvestigateQuestionList=new ArrayList<>();
        if(type==1){//问答题
            String hql11="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and type in(1,9) order by orders asc";
            List<InvestigateQuestion> list11=investigateQuestionDao.getListByHQLWithNamedParams(hql11,null,pageInfo);
            for(InvestigateQuestion investigateQuestion:list11){
                RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
                BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
                String hql21="from InvestigateAnswerStat where publish="+publish+" and question="+investigateQuestion.getId();
                List<InvestigateAnswerStat> list21=investigateAnswerStatDao.getListByHQLWithNamedParams(hql21,null);
                if (list21.size()==0)
                    respInvestigateQuestion.setAnswerSum(0);
                else
                    respInvestigateQuestion.setAnswerSum(list21.get(0).getQuestionCount());
                respInvestigateQuestionList.add(respInvestigateQuestion);
            }
        }else if(type==2){//单选判断
            String hql12="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and type in(2,3) order by orders asc";
            List<InvestigateQuestion> list12=investigateQuestionDao.getListByHQLWithNamedParams(hql12,null,pageInfo);
            for(InvestigateQuestion investigateQuestion:list12){
                RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
                BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
                String hql22="from InvestigateAnswerStat where publish="+publish+" and question="+investigateQuestion.getId();
                List<InvestigateAnswerStat> list22=investigateAnswerStatDao.getListByHQLWithNamedParams(hql22,null);
                if (list22.size()==0)
                    respInvestigateQuestion.setAnswerSum(0);
                else
                    respInvestigateQuestion.setAnswerSum(list22.get(0).getQuestionCount());
                List<RespInvestigateQuestionKey>respInvestigateQuestionKeyList=new ArrayList<>();
                String hql32="from InvestigateQuestionKey where question="+investigateQuestion.getId();
                List<InvestigateQuestionKey> list32=investigateQuestionKeyDao.getListByHQLWithNamedParams(hql32,null);
                for(InvestigateQuestionKey investigateQuestionKey:list32){
                    RespInvestigateQuestionKey respInvestigateQuestionKey=new RespInvestigateQuestionKey();
                    BeanUtils.copyProperties(investigateQuestionKey,respInvestigateQuestionKey);
                    String hql42="from InvestigateAnswerStat where publish="+publish+" and answerKey="+respInvestigateQuestionKey.getId();
                    List<InvestigateAnswerStat> list42=investigateAnswerStatDao.getListByHQLWithNamedParams(hql42,null);
                    if (list42.size()==0)
                        respInvestigateQuestionKey.setSum(0);
                    else
                        respInvestigateQuestionKey.setSum(list42.get(0).getKeyCount());
                    if(respInvestigateQuestion.getAnswerSum()>0&&respInvestigateQuestionKey.getSum()>0)
                    {
                        DecimalFormat df= new DecimalFormat("0.0000");
                        respInvestigateQuestionKey.setPercentage(df.format((double)respInvestigateQuestionKey.getSum()/respInvestigateQuestion.getAnswerSum()));
                    }else{
                        respInvestigateQuestionKey.setPercentage("0.0000");
                    }
                    respInvestigateQuestionKeyList.add(respInvestigateQuestionKey);
                }
                respInvestigateQuestion.setRespInvestigateQuestionKeyList(respInvestigateQuestionKeyList);
                respInvestigateQuestionList.add(respInvestigateQuestion);
            }
        }else if(type==3){//多选题
            String hql13="from InvestigateQuestion where subject="+investigatePublish.getSubject()+" and type =4 order by orders asc";
            List<InvestigateQuestion> list13=investigateQuestionDao.getListByHQLWithNamedParams(hql13,null,pageInfo);
            for(InvestigateQuestion investigateQuestion:list13){
                RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
                BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
                String hql23="from InvestigateAnswerStat where publish="+publish+" and question="+investigateQuestion.getId();
                List<InvestigateAnswerStat> list23=investigateAnswerStatDao.getListByHQLWithNamedParams(hql23,null);
                if (list23.size()==0)
                    respInvestigateQuestion.setAnswerSum(0);
                else
                    respInvestigateQuestion.setAnswerSum(list23.get(0).getQuestionCount());
                List<RespInvestigateQuestionKey>respInvestigateQuestionKeyList=new ArrayList<>();
                String hql33="from InvestigateQuestionKey where question="+investigateQuestion.getId();
                List<InvestigateQuestionKey> list33=investigateQuestionKeyDao.getListByHQLWithNamedParams(hql33,null);
                for(InvestigateQuestionKey investigateQuestionKey:list33){
                    RespInvestigateQuestionKey respInvestigateQuestionKey=new RespInvestigateQuestionKey();
                    BeanUtils.copyProperties(investigateQuestionKey,respInvestigateQuestionKey);
                    String hql43="from InvestigateAnswerStat where publish="+publish+" and answerKey="+investigateQuestionKey.getId();
                    List<InvestigateAnswerStat> list43=investigateAnswerStatDao.getListByHQLWithNamedParams(hql43,null);
                    if (list43.size()==0)
                        respInvestigateQuestionKey.setSum(0);
                    else
                        respInvestigateQuestionKey.setSum(list43.get(0).getKeyCount());
                    if(respInvestigateQuestion.getAnswerSum()>0&&respInvestigateQuestionKey.getSum()>0)
                    {
                        DecimalFormat df= new DecimalFormat("0.0000");
                        respInvestigateQuestionKey.setPercentage(df.format((double)respInvestigateQuestionKey.getSum()/respInvestigateQuestion.getAnswerSum()));
                    }else{
                        respInvestigateQuestionKey.setPercentage("0.0000");
                    }
                    respInvestigateQuestionKeyList.add(respInvestigateQuestionKey);
                }
                respInvestigateQuestion.setRespInvestigateQuestionKeyList(respInvestigateQuestionKeyList);
                respInvestigateQuestionList.add(respInvestigateQuestion);
            }
        }
        respInvestigateQuestionStatistics.setRespInvestigateQuestionList(respInvestigateQuestionList);
        return respInvestigateQuestionStatistics;
    }



    @Override
    public RespMultipleChoiceQuestion answerListByMultipleChoiceQuestion(int publish, int question) {
        RespMultipleChoiceQuestion respMultipleChoiceQuestion=new RespMultipleChoiceQuestion();
        String hql1="from InvestigateObject where publish="+publish+" and subsumable=1";
        List<InvestigateObject> list1 = investigateObjectDao.getListByHQLWithNamedParams(hql1,null);
        respMultipleChoiceQuestion.setObjectSum(list1.size());
        InvestigateQuestion investigateQuestion=investigateQuestionDao.get(question);
        RespInvestigateQuestion respInvestigateQuestion=new RespInvestigateQuestion();
        BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
        String hql33="from InvestigateQuestionKey where question="+question;
        List<InvestigateQuestionKey> list33=investigateQuestionKeyDao.getListByHQLWithNamedParams(hql33,null);
        respInvestigateQuestion.setInvestigateQuestionKeyList(list33);
        String sql="select a.answer,COUNT(*)as sum from `t_investigate_answer` as a ,(select * from t_investigate_object where publish="+publish+" and subsumable=1)as b where a.objective=b.id and a.question ="+question+" GROUP BY a.answer ORDER BY  length(answer) asc,answer asc";
        List<Object[]> objectList= investigatePublishDao.getObjectListBySQL(sql);
        int sum=0;
        List<RespAnswerCount> list=new ArrayList<>();
        if (objectList.size() > 0 && !objectList.isEmpty()) {
            for (int i = 0; i <objectList.size(); i++) {
                RespAnswerCount respAnswerCount =new RespAnswerCount();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(objectList.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                respAnswerCount.setAnswer(objects.getString(0));
                respAnswerCount.setSum(Integer.valueOf(objects.getString(1)));
                if(respAnswerCount.getAnswer()!=null&&!"".equals(respAnswerCount.getAnswer())){
                    list.add(respAnswerCount);
                    sum=sum+respAnswerCount.getSum();
                }
            }
        }
        for(RespAnswerCount respAnswerCount:list){
            if(sum>0)
            {
                DecimalFormat df= new DecimalFormat("0.0000");
                respAnswerCount.setPercentage(df.format((double)respAnswerCount.getSum()/sum));
            }else{
                respAnswerCount.setPercentage("0.0000");
            }
        }
        respMultipleChoiceQuestion.setSum(sum);
        respMultipleChoiceQuestion.setRespAnswerCountList(list);
        respMultipleChoiceQuestion.setRespInvestigateQuestion(respInvestigateQuestion);
        return respMultipleChoiceQuestion;
    }

    @Override
    public void selectTask() {
        Date beginDate=NewDateUtils.today();
        Map<String, Object> params = new HashMap<>();
        params.put("beginDate", beginDate);
        String hql = "from InvestigatePublish where enabled=1 and endTime < :beginDate";
        List<InvestigatePublish> list = investigatePublishDao.getListByHQLWithNamedParams(hql,params);
         for(InvestigatePublish investigatePublish:list)
        {
            investigatePublish.setEnabled(0);
            investigatePublish.setEnabledTime(new Date());
            investigatePublish.setUpdateName("系统自动");
            investigatePublish.setUpdateDate(new Date());
            investigatePublishDao.update(investigatePublish);
        }
    }
}
