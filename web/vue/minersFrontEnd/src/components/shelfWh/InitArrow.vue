<!--  箭头样式-->
<template>
  <span class="InitArrow">
    <el-icon v-if="arrowType === 1">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" v-show="arrowDirection === 1">
        <path d="M817.8,339.3L610.7,124.8c-15.3-15.8-35-25-55.3-27.5c-5.9-1.2-12.1-1.8-18.3-1.8h0c-3.6,0-7.1,0.2-10.6,0.6
          c-22.6,1.2-44.9,10.6-61.7,28L257.5,338.7c-35.4,36.7-34.4,95.6,2.3,131l0,0c36.7,35.4,95.6,34.4,131-2.3l53.5-55.4v331.6
          c0,51,41.7,92.7,92.7,92.7h0c51,0,92.7-41.7,92.7-92.7V411.3l54.8,56.8c35.4,36.7,94.4,37.7,131,2.3l0,0
          C852.2,435,853.2,376,817.8,339.3z"/>
      </svg>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" v-show="arrowDirection === 2">
        <path d="M257.5,592.5L464.6,807c15.3,15.8,35,25,55.3,27.5c5.9,1.2,12.1,1.8,18.3,1.8l0,0c3.6,0,7.1-0.2,10.6-0.6
          c22.6-1.2,44.9-10.6,61.7-28l207.3-214.6c35.4-36.7,34.4-95.6-2.3-131l0,0c-36.7-35.4-95.6-34.4-131,2.3L631,519.8V188.2
          c0-51-41.7-92.7-92.7-92.7l0,0c-51,0-92.7,41.7-92.7,92.7v332.3l-54.8-56.8c-35.4-36.7-94.4-37.7-131-2.3l0,0
          C223.1,496.8,222.1,555.8,257.5,592.5z"/>
      </svg>
    </el-icon>
    <el-icon v-if="arrowType === 2">
      <Top v-show="arrowDirection === 1" />
      <Bottom v-show="arrowDirection === 2" />
    </el-icon>
    <el-icon v-if="arrowType === 3">
      <CaretTop v-show="arrowDirection === 1" />
      <CaretBottom v-show="arrowDirection === 2" />
    </el-icon>
    <el-icon v-if="arrowType === 4">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" v-show="arrowDirection === 1">
        <path d="M512,320L192,704h639.9L512,320z M512,371.6l249,298.9H262.9L512,371.6z"/>
      </svg>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" v-show="arrowDirection === 2">
        <path d="M511.9,704l320-384H192L511.9,704z M511.9,652.4l-249-298.9H761L511.9,652.4z"/>
      </svg>
    </el-icon>
    <el-icon v-if="arrowType === 5">
      <ArrowUp v-show="arrowDirection === 1" />
      <ArrowDown v-show="arrowDirection === 2" />
    </el-icon>
  </span>
</template>
<style lang="scss" scoped>
.el-icon{
  vertical-align: middle;
}
</style>
<script>
export default {
  name: "InitArrow",
  props:{
    arrowType: { 'type': Number, 'default': 1 }, // 箭头样式
    arrowDirection: { 'type': Number, 'default': 1 } // 箭头方向（只有向上向下）
  }
}
</script>

<style lang="scss" scoped>
.TyDemo{
  .ttl{
    background: linear-gradient(to right, #ff7e5f, #feb47b);
    color: #fff;
    padding: 0 20px;
    line-height: 50px;
    margin-bottom: 10px;
    font-weight: bold;
  }
}
</style>