package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.generalAffairs.dao.*;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.*;

import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName UserImportServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 11:19
 * @Version 1.0
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class UserImportServiceImpl implements UserImportService {

    @Autowired
    UserImportLogDao userImportLogDao;
    @Autowired
    UserImportDao userImportDao;
    @Autowired
    UserDao userDao;
    @Autowired
    UserLockDao userLockDao;
    @Autowired
    UserImportService userImportService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;

    @Override
    public ReqUserObject saveImportUser(ReqUserObject reqUserObject, User user, List<User> userList,Integer oid ) {
        ReqUserObject reqUser=new ReqUserObject();
        List<UserImport> userImportList=new ArrayList<>();
        List<User> trueUserList= new ArrayList<>();
        List<User> falseUserList= new ArrayList<>();
        List<User> userLockList = userImportService.getAllLockUsersByOrganization(oid);
        UserImportLog userImportLog=new UserImportLog();
        userImportLog.setOrg(oid);
        userImportLog.setUserCount(reqUserObject.getImportSum());
        userImportLog.setCreator(user.getUserID());
        userImportLog.setCreateName(user.getUserName());
        userImportLog.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        userImportLog.setOperation("1");
        userImportLog.setVersionNo(1);
        for (int i=0;i<reqUserObject.getUserList().size();i++) {
            User u=reqUserObject.getUserList().get(i);
            int status=1;
            //1.手机号位数不为11位的， 姓名，手机号为空的
            if(u.getMobile()==null||"".equals(u.getMobile())||u.getUserName()==null||"".equals(u.getUserName()))
            {
                status=0;
                falseUserList.add(u);
            }else if(u.getMobile().length()!=11){
                status=0;
                falseUserList.add(u);
            }
            //2.手机号自身重复的
            if(status==1) {
                for (int j = 0; j < reqUserObject.getUserList().size(); j++) {
                    if(u.getMobile().equals(reqUserObject.getUserList().get(j).getMobile())&&i!=j){
                        status=0;
                        falseUserList.add(u);
                        break;
                    }
                }
            }
            //3.手机号和系统内存在的出现重复的
            if(status==1){
                for(User user1: userList){
                    if(user1.getMobile().equals(u.getMobile())){
                        status=0;
                        falseUserList.add(u);
                        break;
                    }
                }
            }
            //4.手机号和被冻结的手机号重复的
            if(status==1){
                for(User user2: userLockList){
                    if(user2.getMobile().equals(u.getMobile())){
                        status=0;
                        falseUserList.add(u);
                        break;
                    }
                }
            }
           if(status==1){
                UserImport userImport=new UserImport();
                u.setCreator(user.getUserID());
                u.setCreateName(user.getUserName());
                u.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
                u.setSource("0");
                u.setOid(oid);
                u.setOrganization(user.getOrganization());
                u.setIsDuty("0");
//                u.setLogonName(u.getMobile());
                u.setLogonState(0l);
                u.setStatus("1");
                u.setOrdinaryEmployees(0);
                u.setRoleCode("staff");
                trueUserList.add(u);
                BeanUtils.copyProperties(u,userImport);
                userImport.setOrg(u.getOid());
                userImportList.add(userImport);
            }
        }
        userImportLog.setUserSuccess(trueUserList.size());
        userImportLogDao.save(userImportLog);
        reqUser.setImportSum(reqUserObject.getImportSum());
        reqUser.setFalseImportSum(falseUserList.size());
        reqUser.setTureImportSum(trueUserList.size());
        reqUser.setUserImportList(userImportList);
        return reqUser;
    }

    @Override
    public ReqUserObject unfinishedImportUser(Integer oid) {
        ReqUserObject reqUserObject=new ReqUserObject();
        UserImportLog userImportLog=new UserImportLog();
        String hql="from UserImport where org="+oid+" and source='0'";
        List<UserImport> userList=userImportDao.getListByHQL(hql);
        if(userList.size()>0)
        {
            String hqlUserImportLog="from UserImportLog where org="+oid;
            List<UserImportLog> userImportLogList=userImportLogDao.getListByHQL(hqlUserImportLog);
            if(userImportLogList.size()>0)
                userImportLog=userImportLogList.get(userImportLogList.size()-1);
        }
        reqUserObject.setUserImportList(userList);
        reqUserObject.setImportSum(userImportLog.getUserCount());
        reqUserObject.setTureImportSum(userImportLog.getUserSuccess());
        return reqUserObject;
    }

    @Override
    public List<User> getNotDirectLowerGrade(Integer oid) {
        String hql="from User where oid="+oid+" and roleCode in('staff','agent') and isDuty in ('1','9')";
        List<User> users=userDao.getListByHQL(hql);
        return users;
    }

    @Override
    public List<UserImport> getOptionalImportUser(Integer oid,Integer id) {
        List<UserImport> userImportList=new ArrayList<>();
        String hql="from UserImport where org="+oid+" and source='0' and id!="+id;
         List<UserImport> userList=userImportDao.getListByHQL(hql);
        for(UserImport userImport:userList)
        {
            int status=1;
            if (userImport.getOrdinaryEmployees()==1){
               status=0;
            }else {
                if (userImport.getLeader() != null && !"".equals(userImport.getLeader()) && userImport.getLeaderSource() == null) {
                    userImport.setLeaderSource(2);//历史数据默认为超管时，没有此值，进行默认
                }
                if (userImport.getLeader() != null && !"".equals(userImport.getLeader()) && userImport.getLeaderSource() == 1) {
                    status = noUnderImportUser(userImport,id);
                }
            }
            if(status==1)
            userImportList.add(userImport);
        }
        return userImportList;
    }
    public int noUnderImportUser(UserImport userImport,Integer id){
            if(id.equals(Integer.valueOf(userImport.getLeader()))){
                int x=id;
                return 0;
            }else{
                UserImport userImport1=userImportDao.get(Integer.valueOf(userImport.getLeader()));
                if(userImport1.getLeader()!=null&&!"".equals(userImport1.getLeader())&&userImport1.getLeaderSource()==1) {
                    return  noUnderImportUser(userImport1, id);
                }else{
                    return 1;
                }
            }
        }
    @Override
    public int deleteImportUser(int id) {
        String hql="from UserImport where leaderSource='1' and leader="+id;
        List<UserImport> userList=userImportDao.getListByHQL(hql);
        if(userList.size()>0)
        {
            for(UserImport userImport:userList)
            {
                userImport.setLeader(null);
                userImport.setLeaderName(null);
                userImport.setRankUrl(null);
                userImportDao.update(userImport);
            }
        }
        userImportDao.deleteById(id);
        return 1;
    }

    @Override
    public int updateImportUser(UserImport userImport) {
        userImportDao.save(userImport);
        return 1;
    }

    @Override
    public int updateImportUserMobile(UserImport userImport) {
        String hql="from UserImport where org="+userImport.getOrg()+" and source='0' and mobile='"+userImport.getMobile()+"' and id!="+userImport.getId();
        List<UserImport> userList=userImportDao.getListByHQL(hql);
        if (userList.size()>0)
            return  -1;
        else{
            UserImport user =userImportDao.get(userImport.getId());
            user.setMobile(userImport.getMobile());
            user.setUserName(userImport.getUserName());
            userImportDao.save(user);
            return 1;
        }
    }

    @Override
    public int giveUpImportUser(Integer oid) {
        String hql="from UserImport where org="+oid+" and source='0'";
        List<User> userList=userDao.getListByHQL(hql);
        userDao.deleteAll(userList);
        return 1;
    }

    @Override
    public List<User> completeImportUser(HttpServletRequest request, AuthInfoDto authInfo,User loginUser,int org) {
        List<User> userList=new ArrayList<>();
        String hql="from UserImport where org="+org+" and source='0'";
        List<UserImport> userImportList=userImportDao.getListByHQL(hql);
        for(UserImport userImport:userImportList)
        {
            User user=new User();
            user.setCreator(userImport.getCreator());
            user.setUserName(userImport.getUserName());
            user.setMobile(userImport.getMobile());
            user.setCreateName(userImport.getCreateName());
            user.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
            user.setOid(org);
//            user.setLogonName(userImport.getLogonName());
            user.setLogonState(userImport.getLogonState());
            user.setStatus(userImport.getStatus());
            user.setOrdinaryEmployees(userImport.getOrdinaryEmployees());
            user.setRoleCode(userImport.getRoleCode());
            user.setSuperId(userImport.getSuperId());
//            user.setLogonPwd(userImport.getLogonPwd());
            user.setLeader(userImport.getLeader());
            user.setLeaderName(userImport.getLeaderName());
            user.setRankUrl(userImport.getRankUrl());
            user.setSource("2");
            user.setIsDuty("1");
//            user.setManager(userImport.getManager());
            user.setManagerCode(userImport.getManagerCode());
//            User superUser=userService.getUserByRoleCode(org,"super");
            Organization o = orgService.getByOid(org,true,false);
//            user.setLogonPwd(userService.getLogonPwdByPhone(us er.getMobile()));//获取密码
            user.setOrganization(o);
            userService.addUser(user, o);

            userService.sendH5UnLockedActiveCode(request,authInfo,loginUser,user.getMobile()); // 发送h5激活短信 1.289账号验证与安全设置

            userList.add(user);
            userImport.setSource("3");
            userImport.setIsDuty("1");
            userImport.setUser(user.getUserID());
            userImportDao.save(userImport);
        }
        for(UserImport userImport:userImportList)
        {
            if(userImport.getLeaderSource()==1){
                String rankUrl ="";
                UserImport userImport1=userImportDao.get(Integer.valueOf(userImport.getLeader()));//查出直接上级，本表对应生成的user是谁
                User user=userDao.get(userImport.getUser());
                user.setLeader(userImport1.getUser().toString());//将user表中的自己，附上user表中的leader
                String rank=leaderNotImportUser(userImport);
                user.setRankUrl(rank);
                userDao.update(user);
                userImport.setRankUrl(rank);
                userImportDao.update(userImport);
            }
        }
        return userList;
    }

    public String leaderNotImportUser(UserImport userImport){
        if(userImport.getLeaderSource()==1)//如果他的上级还是导入表，继续遍历，直到不是导入表为止
        {
            UserImport userImport1=userImportDao.get(Integer.valueOf(userImport.getLeader()));
            return leaderNotImportUser(userImport1)+"/"+userImport1.getUser();
        }else{
            return "/"+userImport.getRankUrl();
        }
    }

    public List<User> addSalaryUserForImportUser(int org) {
        String hql="from User where oid="+org+" and source='2' and IsDuty='1'";
        List<User> userList=userDao.getListByHQL(hql);
        return userList;
    }
    @Override
    public List<User> getAllUsersByOrganization(Integer orgId,User user) {
        String condition = " and o.oid = "+orgId +" and o.isDuty!='2'";
        List<Object> paramsList = new ArrayList<Object>();
        Integer i=0;
        if(user!=null&& StringUtils.isNotBlank(user.getUserName())){
            condition += " and o.userName like ?"+(i++).toString();
            paramsList.add("%"+user.getUserName()+"%");
        }
        if(user!=null&&StringUtils.isNotBlank(user.getDepartment())){
            condition += " and o.department = ?"+(i++).toString();
            paramsList.add(user.getDepartment());
        }
        if(user!=null&&StringUtils.isNotBlank(user.getPostID())){
            condition += " and o.postID = ?"+(i++).toString();
            paramsList.add(user.getPostID());
        }
        List<User> users = userDao.findCollectionByConditionNoPage(condition,paramsList.toArray(),null);
        return users;
    }

    @Override
    public List<User> getAllLockUsersByOrganization(Integer org) {
        List<User> users=new ArrayList<>();
        String hql = "from User where oid = "+ org +" and roleCode='agent' and isDuty='1'";
        List<User> list= userDao.getListByHQL(hql);
        for(User u:list)
        {
            String hql1 = "from SysUserLock where user = "+ u.getUserID() +" and isValid=1";
            List<SysUserLock> sysUserLockList=userLockDao.getListByHQL(hql1);
            User user=new User();
            BeanUtils.copyProperties(u,user);
            user.setMobile(sysUserLockList.get(0).getLockedMobile());
            users.add(user);
        }
        return users;
    }

    @Override
    public List<SysUserLock> getUserLockListByOidMobile(Integer org, String mobile) {
        String hql1 = "from SysUserLock where lockedMobile ="+ mobile +" and isValid=1 and org="+org;
        List<SysUserLock> sysUserLockList=userLockDao.getListByHQL(hql1);
        return sysUserLockList;
    }

    @Override
    public UserImport addUserImport(UserImport user,int importId) {
        if (importId>0)
            user.setImportId(importId);
        userImportDao.save(user);
        return user;
    }

    @Override
    public int getImportId(int org) {
        int id=0;
        String hqlUserImportLog="from UserImportLog where org="+org;
        List<UserImportLog> userImportLogList=userImportLogDao.getListByHQL(hqlUserImportLog);
        if(userImportLogList.size()>0)
            id=userImportLogList.get(userImportLogList.size()-1).getId();
        return id;
    }

    @Override
    public Integer getNoLeaderUserCountByOid(Integer org) {
        String hql = "select count(id) from UserImport where leader is null and isDuty='0' and org=:oid ";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",org);
        Long result = (Long) userImportDao.getByHQLWithNamedParams(hql,params);
        return result.intValue();
    }

    @Override
    public void updateImportLeader(Integer id) {
        String hql="from UserImport where leaderSource='1' and leader="+id;
        List<UserImport> userList=userImportDao.getListByHQL(hql);
        if(userList.size()>0)
        {
            for(UserImport userImport:userList)
            {
                userImport.setLeader(null);
                userImport.setLeaderName(null);
                userImport.setRankUrl(null);
                userImportDao.update(userImport);
            }
        }
    }

    @Override
    public Integer getNoManageUserCountByOid(Integer org) {
        String hql = "select count(id) from UserImport where managerCode is null and isDuty='0' and org=:oid ";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",org);
        Long result = (Long) userImportDao.getByHQLWithNamedParams(hql,params);
        return result.intValue();
    }

    @Override
    public UserImport getUserImportByID(int importId) {
        UserImport userImport=userImportDao.get(importId);
        return userImport;
    }

    @Override
    public void deleteUserImportsByOid(Integer oid) {
        String hql="delete from UserImport where org=:oid";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        userImportDao.queryHQLWithNamedParams(hql,params);
    }

    @Override
    public void updateUserForE() {
        String hql = "from User where roleCode='general'";
        List<User> list= userDao.getListByHQL(hql);
        for(User u:list){
            String hql1 = "from User where manager is null and oid = "+ u.getOid()+" and source='2'";
            List<User> list1= userDao.getListByHQL(hql1);
            for(User u1:list1)
            {
//                u1.setManager(u.getUserID());
                u1.setManagerCode("general");
                userDao.update(u1);
            }
        }

    }

    @Override
    public Integer getImportNumberByOid(Integer oid) {
        String hql="select 1 from UserImport where source='0' and org=:oid ";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        Integer number= (Integer) userImportDao.getByHQLWithNamedParams(hql,map);
        return number;
    }
}
