package cn.sphd.miners.modules.steamer.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.lyric.entity.CmwPlaylist;
import cn.sphd.miners.modules.lyric.entity.CmwPlaylistSongs;
import cn.sphd.miners.modules.lyric.entity.CmwSongs;
import cn.sphd.miners.modules.steamer.dto.PlaylistSongsDto;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

public interface SteamerService {

    //获取全部歌曲或者在获取全部歌曲时标记出歌单中的歌曲
    List<CmwSongs> getPlayListSongs(User user, Long playListId);

    //我的歌单
    Map<String, Object> getAllPlaylist(User user, String time);

    //获取歌单信息
    Map<String, Object> getPlaylistMes(User user, Long id);

    //分页获取歌单歌曲
    List<PlaylistSongsDto> moreSongs(User user, Long id, String name, PageInfo pageInfo);

    //新增歌单
    Integer addPlayList(User user, String liveDate, String songIds);

    //修改歌单
    Integer upPlayList(User user, Long id, String songIds, String liveDate);

    //删除歌单
    Integer delPlayList(Long id);

    //移除歌曲
    Integer removePlayListSong(Long id, Integer playListNum);

}
