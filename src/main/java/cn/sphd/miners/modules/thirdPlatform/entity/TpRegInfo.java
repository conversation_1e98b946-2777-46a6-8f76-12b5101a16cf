package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-09-29 
 */

@Entity ( name ="TpRegInfo" )
@Table ( name ="t_tp_reg_info" )
public class TpRegInfo  implements Serializable {

	private static final long serialVersionUID =  616611301047998873L;

	/**
	 * ID
	 */
	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
	@Column(name = "org" )
	private Integer org;
	/**
	 * 独立小程序平台代码
	 */
   	@Column(name = "tg_code" )
	private String tgCode;

	/**
	 * 平台ID
	 */
   	@Column(name = "platform" )
	private Integer platform;

	/**
	 * 小程序ID
	 */
   	@Column(name = "app" )
	private Integer app;

	/**
	 * 企业名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 代码类型:1-统一社会信用代码(18 位),2-组织机构代码(9位xxxxxxxx-x),3-营业执照注册号(15 位)
	 */
   	@Column(name = "code_type" )
	private Integer codeType;

	/**
	 * 代码
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 法人微信号
	 */
   	@Column(name = "legal_persona_wechat" )
	private String legalPersonaWechat;

	/**
	 * 法人姓名（绑定银行卡）
	 */
   	@Column(name = "legal_persona_name" )
	private String legalPersonaName;

	/**
	 * 第三方联系电话
	 */
   	@Column(name = "component_phone" )
	private String componentPhone;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public String getTgCode() {
		return this.tgCode;
	}

	public void setTgCode(String tgCode) {
		this.tgCode = tgCode;
	}

	public Integer getPlatform() {
		return this.platform;
	}

	public void setPlatform(Integer platform) {
		this.platform = platform;
	}

	public Integer getApp() {
		return this.app;
	}

	public void setApp(Integer app) {
		this.app = app;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getCodeType() {
		return this.codeType;
	}

	public void setCodeType(Integer codeType) {
		this.codeType = codeType;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getLegalPersonaWechat() {
		return this.legalPersonaWechat;
	}

	public void setLegalPersonaWechat(String legalPersonaWechat) {
		this.legalPersonaWechat = legalPersonaWechat;
	}

	public String getLegalPersonaName() {
		return this.legalPersonaName;
	}

	public void setLegalPersonaName(String legalPersonaName) {
		this.legalPersonaName = legalPersonaName;
	}

	public String getComponentPhone() {
		return this.componentPhone;
	}

	public void setComponentPhone(String componentPhone) {
		this.componentPhone = componentPhone;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
