import { createApp } from 'vue'
import App from '@/App.vue'
import router from '@/router'
import ElementPlus from "element-plus"
import "element-plus/dist/index.css"
import auth from '@/sys/auth'
import initStorage from '@/sys/initStorage'
import sphdSocket from '@/sys/sphd'
import Vant from 'vant'
import 'vant/lib/index.css'
import VConsole from 'vconsole'
const vConsole = new VConsole()
console.log("router==", router)
// console.log('VUE_APP_WEB_ROOT',process.env.VUE_APP_WEB_ROOT)

auth.init({isGuest: true})
const app = createApp(App).use(router).use(ElementPlus)
app.use(Vant);
initStorage({})
sphdSocket.start()
parent.sphdSocket = sphdSocket

app.mount('#app')