package cn.sphd.miners.modules.uploads.dto;

import java.io.Serializable;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 上传文件名生成和处理类
 * @date Create at 2020/3/4 09:41
**/
public class ResFileDto implements Serializable{
    private String originalFilename;//上传的原始文件名
    private String relativePath;
    private String fileName;//新文件名
    private String extName;//wyu:扩展名
    private Long minMissIndex;

    public ResFileDto() {
        originalFilename = "";
        relativePath = "";
        fileName = "";
        extName = "";
        minMissIndex = -1L;
    }
    public ResFileDto(Long minMissIndex) {
        this();
        this.minMissIndex = minMissIndex;
    }

    public ResFileDto(String originalFilename) {
        this();
        String[] paths = originalFilename.split("[/|\\\\]");
        String name = paths[paths.length-1];
        this.originalFilename = name;
        relativePath = "";
        if(originalFilename.length()>name.length()) {
            this.relativePath = originalFilename.substring(0, originalFilename.length() - name.length());
        }
        int index = name.lastIndexOf(".");
        String lastAppendName = "";
        extName = "";
        if(index>=0) {
            lastAppendName = name.substring(index);//wyu：“.扩展名”，包括点的扩展名。
            if(Pattern.compile("[\\x00-\\x7F]*").matcher(lastAppendName).matches()) {//没有非ASCII字符
                this.extName = lastAppendName.substring(1);
            } else {//非ASCII字符不作为扩展名。
                lastAppendName = "";
            }
        }
        fileName = "";
        this.fileName = UUID.randomUUID().toString().replaceAll("-", "") + lastAppendName;
    }

    public ResFileDto(String originalFilename, String relativePath, Long minMissIndex) {
        this(originalFilename);
        int index=relativePath.indexOf(originalFilename);
        this.relativePath = relativePath.substring(0,index<0?0:index);
        this.minMissIndex = minMissIndex;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getExtName() {
        return extName;
    }

    public void setExtName(String extName) {
        this.extName = extName;
    }

    public Long getMinMissIndex() {
        return minMissIndex;
    }

    public void setMinMissIndex(Long minMissIndex) {
        this.minMissIndex = minMissIndex;
    }
}
