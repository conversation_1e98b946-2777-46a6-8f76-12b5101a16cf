package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.modules.personal.entity.PersonnelContract;
import cn.sphd.miners.modules.personal.entity.PersonnelContractHistory;
import cn.sphd.miners.modules.personal.entity.PersonnelContractImage;
import cn.sphd.miners.modules.personal.entity.PersonnelContractImageHistory;
import cn.sphd.miners.modules.personal.service.PersonnelContractService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class PersonnelContarctUsing implements FileUsingCallback {

    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;

    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            PersonnelContractService service = ac.getBean(PersonnelContractService.class, "personnelContractService");
            switch (entityClass) {
                case "PersonnelContract":
                    PersonnelContract perCon = service.perConSingle(Long.valueOf(id));
                    if (perCon.getFilePath() != null && perCon.getFilePath() != "") {
                        return filename.equals(perCon.getFilePath());
                    }
                    break;
                case "PersonnelContractHistory":
                    PersonnelContractHistory perConHis = service.perConHisSingle(Long.valueOf(id));
                    if (perConHis.getFilePath() != null && perConHis.getFilePath() != "") {
                        return filename.equals(perConHis.getFilePath());
                    }
                    break;
                case "PersonnelContractImage":
                    PersonnelContractImage perConImage = service.perConImage(Long.valueOf(id));
                    if (perConImage.getUplaodPath() != null && perConImage.getUplaodPath() != "") {
                        return filename.equals(perConImage.getUplaodPath());
                    }
                    break;
                case "PersonnelContractImageHistory":
                    PersonnelContractImageHistory perConImageHis = service.perConImageHis(Long.valueOf(id));
                    if (perConImageHis.getUplaodPath() != null && perConImageHis.getUplaodPath() != "") {
                        return filename.equals(perConImageHis.getUplaodPath());
                    }
                    break;
            }
        }
        return false;
    }

    @Override
    @JsonIgnore
    @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public PersonnelContarctUsing(Integer id, Class entityClass){
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }

    public PersonnelContarctUsing(){

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }

}
