package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.material.entity.MtUnit;
import cn.sphd.miners.modules.material.entity.RespStatus;

import java.util.List;

/**
 * @ClassName UnitService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/23 17:39
 * @Version 1.0
 */
public interface UnitService {
    List<MtUnit> selectUnitList(Integer org, Integer status, PageInfo pageInfo);

    List<MtUnit> selectStopUnitList(Integer org);

    List<MtUnit> selectUpUnitList(Integer org);

    List<MtUnit> selectUnitList(Integer org);

    int selectUnitNumber(Integer org);

    MtUnit selectMtUnit(Integer id);

    RespStatus addUnit(MtUnit mtUnit, Integer module);

    int updateUnit(MtUnit mtUnit);

    int stopUnitEnter(int id);
    //模块代码: 哪个模块选择的计量单位，module就传对应的代码
//    1销售管理—通用型商品
//    2销售管理—专属商品
//    3技术管理—产品档案
//    4技术管理—构成管理
//    5配方管理-配方
//    6配方管理-材料
//    7采购-材料录入
//    8技术管理-材料
    //9-初始设置 - 货架与库位
    void selectUnit(Integer unitId, Integer module);

    //org  机构id，module模块代码
    List<MtUnit> selectUnitListForModule(Integer org, Integer module);

    MtUnit selectMtUnitByName(String unit, Integer oid);
}
