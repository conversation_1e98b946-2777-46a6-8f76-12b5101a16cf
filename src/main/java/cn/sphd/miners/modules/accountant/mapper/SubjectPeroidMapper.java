package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectPeroid;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface SubjectPeroidMapper extends BaseMapper<TAccountantSubjectPeroid> {

    TAccountantSubjectPeroid judgeSubjectPeroid(TAccountantSubjectPeroid param);

    TAccountantSubjectPeroid judgeAnnualReport(TAccountantSubjectPeroid peroidParam);

    //得到总帐的列表，不包含年报数据
    List<TAccountantSubjectPeroid> getPeroidList(TAccountantSubjectPeroid param);
    //得到明细张的列表，不包含年报数据
    List<TAccountantSubjectPeroid> getDetailPeroidList(QueryData param);

    TAccountantSubjectPeroid getAnnual(TAccountantSubjectPeroid param);

    TAccountantSubjectPeroid getAnnualperid(TAccountantSubjectPeroid param);

    //根据期数删除结账数据
    int delSubjectPeriod(QueryData qd);

    //修改分期表中状态
    int updatePeriodState(TAccountantSubjectPeroid param);

    //根据月末日期找到本月的明细账科目
    List<TAccountantSubjectPeroid> subjectPeriod(TAccountantSubjectPeroid param);

    Integer insertImportantData(QueryData qd);

    int updateAccumulative(QueryData param);

    TAccountantSubjectPeroid getBeginningData(TAccountantSubjectPeroid paramPeroid);

    TAccountantSubjectPeroid getBeginningQuantity(TAccountantSubjectPeroid annualBeginPeroidParam);

    List<TAccountantSubjectPeroid> getPeroidListBySubjectID(TAccountantSubjectPeroid subjectPeroid);

    TAccountantSubjectPeroid getLastPeroid(TAccountantSubjectPeroid peroid);

    void delTestData();

}