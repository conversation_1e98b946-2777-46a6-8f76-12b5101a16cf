<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TAccountSubjectMapper" >


    <select id = "getSingle" resultType = "accountSubject" parameterType = "accountSubject" >
        select
        id, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,beginning_direction AS beginningDirection,
        previous_balance AS  previousBalance,previous_direction AS previousDirection,carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting,
        relevance_type AS relevanceType, relevance_item AS relevanceItem,establish
        from t_accountant_subject
        where id = #{id}
    </select>

    <delete id="delete" parameterType="accountSubject" >
        delete from t_accountant_subject
        where id = #{id}
    </delete>
    <delete id="deleteSubject">
        delete from t_accountant_subject
        where subject like CONCAT(#{subject},'%') AND org=#{org}
    </delete>

    <insert id="insert" parameterType="accountSubject" useGeneratedKeys="true" keyProperty="id" >
        insert into t_accountant_subject (id, subject, org,
        parent, name, category,
        level, orders, state, max_child_subjects,
        initial_amount, beginning_balance,beginning_direction, previous_balance,previous_direction,
        carried_forward_date, balance, summary,
        credit, debit, credit_accumulative,
        debit_accumulative, balance_direction, measure_unit,
        memo, creator, create_name,
        create_date, updator, update_name,
        update_date, approve_Item, approve_status,
        approve_level, auditor, auditor_name,
        audit_date, operation, apply_memo,
        approve_memo, message_id,end_direction,quantity_assisting_accounting)
        values (#{id}, #{subject}, #{org},
        #{parent}, #{name}, #{category},
        #{level}, #{orders}, #{state}, #{maxChildSubjects},
        #{initialAmount}, #{beginningBalance},#{beginningDirection}, #{previousBalance},#{previousDirection}
        #{carriedForwardDate}, #{balance}, #{summary},
        #{credit}, #{debit}, #{creditAccumulative},
        #{debitAccumulative}, #{balanceDirection}, #{measureUnit},
        #{memo}, #{creator}, #{createName},
        #{createDate}, #{updator}, #{updateName},
        #{updateDate}, #{approveItem}, #{approveStatus},
        #{approveLevel}, #{auditor}, #{auditorName},
        #{auditDate}, #{operation}, #{applyMemo},
        #{approveMemo}, #{messageId},#{endDirection},#{quantityAssistingAccounting})
    </insert>
    <insert id="insertSubject">
        INSERT INTO t_accountant_subject
        (subject, org, parent, name, category, level, state,
        max_child_subjects, balance_direction, measure_unit, creator, create_name, create_date,quantity_assisting_accounting,
        relevance_type, relevance_item,useable)
        VALUES
        (#{subject}, #{org}, #{parent}, #{name}, #{category}, #{level}, #{state},
        #{maxChildSubjects}, #{balanceDirection}, #{measureUnit}, #{creator}, #{createName}, #{createDate},#{quantityAssistingAccounting},
        #{relevanceType}, #{relevanceItem},#{useable})
    </insert>

    <insert id="addInitializationSubject">
        insert into t_accountant_subject (subject, org, parent, name, category, level, state,
        max_child_subjects, beginning_balance, previous_balance, balance, credit, debit, credit_accumulative,
        debit_accumulative, creator,useable, balance_direction, quantity_assisting_accounting) select subject, #{org}, parent, name, category, level, 1,
        max_child_subjects, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0,1, balance_direction, 0 from t_accountant_subject_def
    </insert>

    <update id="update" parameterType="accountSubject" >
        update t_accountant_subject
        <set >
            <if test="subject != null" >
                subject = #{subject},
            </if>
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="parent != null" >
                parent = #{parent},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="level != null" >
                level = #{level},
            </if>
            <if test="orders != null" >
                orders = #{orders},
            </if>
            <if test="state != null" >
                state = #{state},
            </if>
            <if test="maxChildSubjects != null" >
                max_child_subjects = #{maxChildSubjects},
            </if>
            <if test="initialAmount != null" >
                initial_amount = #{initialAmount},
            </if>
            <if test="beginningBalance != null" >
                beginning_balance = #{beginningBalance},
            </if>
            <if test="beginningDirection != null">
                beginning_direction = #{beginningDirection},
            </if>
            <if test="previousBalance != null" >
                previous_balance = #{previousBalance},
            </if>

            <if test="previousDirection != null" >
                previous_direction = #{previousDirection},
            </if>

            <if test="endDirection != null" >
                end_direction = #{endDirection},
            </if>

            <if test="carriedForwardDate != null" >
                carried_forward_date = #{carriedForwardDate},
            </if>
            <if test="balance != null" >
                balance = #{balance},
            </if>
            <if test="quantity != null" >
                quantity = #{quantity},
            </if>
            <if test="summary != null" >
                summary = #{summary},
            </if>
            <if test="credit != null" >
                credit = #{credit},
            </if>
            <if test="debit != null" >
                debit = #{debit},
            </if>
            <if test="creditAccumulative != null" >
                credit_accumulative = #{creditAccumulative},
            </if>
            <if test="debitAccumulative != null" >
                debit_accumulative = #{debitAccumulative},
            </if>
            <if test="balanceDirection != null" >
                balance_direction = #{balanceDirection},
            </if>
            <if test="measureUnit != null" >
                measure_unit = #{measureUnit},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="quantityAssistingAccounting != null" >
                quantity_assisting_accounting = #{quantityAssistingAccounting},
            </if>
            <if test="relevanceType != null" >
                relevance_type = #{relevanceType},
            </if>
            <if test="relevanceItem != null" >
                relevance_item = #{relevanceItem},
            </if>

            <if test="initialQuantity != null" >
                initial_quantity = #{initialQuantity},
            </if>
            <if test="beginningQuantity != null" >
                beginning_quantity = #{beginningQuantity},
            </if>
            <if test="previousQuantity != null" >
                previous_quantity = #{previousQuantity},
            </if>
            <if test="creditAccumulativeQuantity != null" >
                credit_accumulative_quantity = #{creditAccumulativeQuantity},
            </if>
            <if test="debitAccumulativeQuantity != null" >
                debit_accumulative_quantity = #{debitAccumulativeQuantity},
            </if>


        </set>
         where id = #{id}
    </update>

    <update id="upadteParentSubject" >
        UPDATE
        t_accountant_subject
        SET
        max_child_subjects = #{maxChildSubjects}
        WHERE
        subject = #{subject} and org = #{org}
    </update>
    <update id="updateSubject">
        UPDATE
        t_accountant_subject
        SET
        name = #{name}, measure_unit = #{measureUnit}, updator = #{updator},
        update_name = #{updateName}, update_date = #{updateDate},quantity_assisting_accounting=#{quantityAssistingAccounting}
        WHERE
        subject = #{subject} and org = #{org}
    </update>
    <update id="updateStateForSubject">
        UPDATE
        t_accountant_subject
        SET
        state = #{state}
        WHERE
        subject = #{subject} AND org = #{org}
    </update>


    <update id="updateOrgSubject">

        UPDATE `t_accountant_subject` SET balance=0,credit=0,debit=0 WHERE org=#{org}

    </update>

    <update id="updateBehiningMonth">
        update t_accountant_subject
        <set >
            <if test="parent != null" >
                parent = #{parent},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="level != null" >
                level = #{level},
            </if>
            <if test="orders != null" >
                orders = #{orders},
            </if>
            <if test="state != null" >
                state = #{state},
            </if>
            <if test="maxChildSubjects != null" >
                max_child_subjects = #{maxChildSubjects},
            </if>
            <if test="initialAmount != null" >
                initial_amount = #{initialAmount},
            </if>
            <if test="beginningBalance != null" >
                beginning_balance = #{beginningBalance},
            </if>
            <if test="beginningDirection != null">
                beginning_direction = #{beginningDirection},
            </if>
            <if test="previousBalance != null" >
                previous_balance = #{previousBalance},
            </if>

            <if test="previousDirection != null" >
                previous_direction = #{previousDirection},
            </if>

            <if test="endDirection != null" >
                end_direction = #{endDirection},
            </if>

            <if test="carriedForwardDate != null" >
                carried_forward_date = #{carriedForwardDate},
            </if>
            <if test="balance != null" >
                balance = #{balance},
            </if>
            <if test="summary != null" >
                summary = #{summary},
            </if>
            <if test="credit != null" >
                credit = #{credit},
            </if>
            <if test="debit != null" >
                debit = #{debit},
            </if>
            <if test="creditAccumulative != null" >
                credit_accumulative = #{creditAccumulative},
            </if>
            <if test="debitAccumulative != null" >
                debit_accumulative = #{debitAccumulative},
            </if>
            <if test="balanceDirection != null" >
                balance_direction = #{balanceDirection},
            </if>
            <if test="measureUnit != null" >
                measure_unit = #{measureUnit},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="endDirection != null">
                end_direction = #{endDirection}
            </if>
            <if test="quantityAssistingAccounting != null" >
                quantity_assisting_accounting = #{quantityAssistingAccounting},
            </if>
        </set>
        WHERE
        subject = #{subject} AND org = #{org}
    </update>
    <update id="updateBeginning">

        update t_accountant_subject set beginning_direction=end_direction,beginning_balance=balance,credit_accumulative=0,debit_accumulative=0,credit_accumulative_quantity=0,debit_accumulative_quantity=0

    </update>

    <update id="updateUseable">

        UPDATE `t_accountant_subject` SET useable=#{useable} WHERE org=#{org} AND (`subject`=#{subject} or `subject`=#{parent})

    </update>
    <update id="reduceChild">
        UPDATE `t_accountant_subject` SET max_child_subjects=max_child_subjects-1 WHERE id=#{id}
    </update>

    <update id="financeRelevanceModify">
        UPDATE `t_accountant_subject` SET `name`=#{name} ,updator=#{updator},update_name=#{updateName},update_date=#{updateDate}
        WHERE org=#{org} AND `level`=#{level} AND relevance_item=#{relevanceItem} AND relevance_type=#{relevanceType} and useable='1';
    </update>

    <update id="updateEstablishId">
        UPDATE `t_accountant_subject` SET establish=#{establish} where org=#{org} and establish is null
    </update>

    <select id = "listPage" resultType = "accountSubject" parameterType = "hashmap" >
        select
        id, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,previous_direction AS previousDirection,beginning_direction AS beginningDirection, previous_balance AS  previousBalance, carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting,useable, relevance_type AS relevanceType, relevance_item AS relevanceItem
        from t_accountant_subject
    </select>

    <select id="listBySubjects" resultType="accountSubject">
        select
        subject, parent, name, category, level, state, balance_direction AS balanceDirection
        from t_accountant_subject
        <where>
            org = #{org} AND (subject=#{subject1} or subject=#{subject2})
        </where>
    </select>

    <select id="listByAccountSubject" resultType="accountSubject">
        select
        a.subject, a.parent, a.name, a.category, a.level, a.state, a.balance_direction AS balanceDirection,
        a.creator, a.max_child_subjects AS maxChildSubjects, s.name AS categoryname,a.end_direction AS  endDirection,
        a.quantity_assisting_accounting AS  quantityAssistingAccounting,a.measure_unit AS measureUnit,a.useable
        from t_accountant_subject a, t_accountant_subject_category s
        <where>
            a.category = s.id AND a.org = #{org} AND a.category IN
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="subjectState==1">
                AND a.state=1
            </if>
<!--            <if test="eid != null">-->
<!--                AND (a.establish=#{eid} or a.establish is null)-->
<!--            </if>-->
<!--            <if test="eid == null">-->
<!--                AND a.establish is null-->
<!--            </if>-->
        </where>
        order by a.subject asc
    </select>
    <select id="listByFirstSubject" resultType="accountSubject">
        SELECT
        a.id,a.name, a.subject, a.max_child_subjects AS maxChildSubjects, a.balance_direction AS balanceDirection,
        a.level, a.category, a.state, a.parent, a.measure_unit AS measureUnit,a.quantity_assisting_accounting AS quantityAssistingAccounting,
        a.previous_balance AS previousBalance, s.name AS categoryname,a.org,a.end_direction AS  endDirection,a.useable
        FROM t_accountant_subject a, t_accountant_subject_category s
        WHERE a.category = s.id AND a.org = #{org} AND a.subject = #{subject} order by a.create_date desc limit 1
    </select>
    <select id="newSubject" resultType="string">
        SELECT  CONCAT(#{subject},LPAD(#{cs},4,'0'))
    </select>
    <select id="childSubject" resultType="accountSubject">
        SELECT
        id, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,beginning_direction AS beginningDirection,
        previous_balance AS  previousBalance,previous_direction AS previousDirection, carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,useable,
        relevance_type AS relevanceType, relevance_item AS relevanceItem
        FROM
        t_accountant_subject
        WHERE
        parent = #{subject} AND org = #{org}
    </select>
    <select id="listAllSubject" resultType="accountSubject">

        select  id, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,beginning_direction AS beginningDirection,
         previous_balance AS  previousBalance,previous_direction AS previousDirection, carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,useable,
        relevance_type AS relevanceType, relevance_item AS relevanceItem
        FROM
        t_accountant_subject
        WHERE
        org = #{org} order by subject

    </select>
    <select id="lastSubject" resultType="accountSubject">
        SELECT
        subject
        FROM t_accountant_subject
        WHERE org = #{org} AND parent = #{parent}
        ORDER BY id DESC
    </select>
    <select id="listAll" resultType="accountSubject">
        SELECT
        subject, org, balance,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting,useable
        FROM
        t_accountant_subject
    </select>
    <select id="subjectAllMessage" resultType="accountSubject">
        SELECT
        a.id,a.name, a.subject, a.max_child_subjects AS maxChildSubjects, a.balance_direction AS balanceDirection,a.credit_accumulative AS creditAccumulative,a.debit_accumulative AS debitAccumulative,
        a.level, a.category, a.state, a.parent, a.measure_unit AS measureUnit,a.balance,a.beginning_balance AS beginningBalance,a.beginning_direction AS beginningDirection,
        a.previous_balance AS previousBalance, s.name AS categoryname,a.org,a.end_direction AS  endDirection,a.quantity_assisting_accounting AS  quantityAssistingAccounting,a.useable
        FROM t_accountant_subject a, t_accountant_subject_category s
        WHERE a.category = s.id AND a.org = #{org} AND a.subject = #{subject}
    </select>

    <select id="listProfitAndLossSubject" resultType="accountSubject">
        select
        id, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,beginning_direction AS beginningDirection,
        previous_balance AS  previousBalance,previous_direction AS previousDirection,carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,useable,
        relevance_type AS relevanceType, relevance_item AS relevanceItem
        from t_accountant_subject
        where org = #{org} AND (subject like '5%')
    </select>

    <select id="getByOrgAndSubject" resultType="accountSubject">
        select
        id, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,previous_direction AS previousDirection,beginning_direction AS beginningDirection, previous_balance AS  previousBalance, carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting,useable,
        relevance_type AS relevanceType, relevance_item AS relevanceItem
        from t_accountant_subject WHERE  org = #{org} AND subject = #{subject}
    </select>
    <select id="getSubjects" resultType="accountSubject">
              SELECT
              id,balance,subject,name,level,max_child_subjects AS maxChildSubjects,org
              FROM `t_accountant_subject` WHERE  org=#{org}  AND  subject like CONCAT(left(#{subject},4),'%')  ORDER BY subject
    </select>

    <select id="getSpecifySubjectInfo" resultType="accountSubject">
        SELECT id, subject, org, parent, name, category, level, max_child_subjects AS maxChildSubjects,balance_direction AS balanceDirection,useable,quantity_assisting_accounting AS  quantityAssistingAccounting,measure_unit AS measureUnit FROM `t_accountant_subject` WHERE org=#{oid}  AND (`subject`='1413' OR `subject`='********');
    </select>

    <select id="checkNameDuplicate" resultType="accountSubject">
        SELECT `subject` FROM `t_accountant_subject`
        <where>
            org=#{org} AND `name`=#{name} and (subject like #{parent})
<!--            <if test="establish != null">-->
<!--                AND (establish=#{establish} or establish is null)-->
<!--            </if>-->
<!--            <if test="establish == null">-->
<!--                AND establish is null-->
<!--            </if>-->
        </where>
        order by id desc LIMIT 1
    </select>
    <select id="getFinanceRelevanceSubject" resultType="accountSubject">
        SELECT id,balance FROM `t_accountant_subject` WHERE relevance_item=#{relevanceItem} AND relevance_type=#{relevanceType} AND `level`=#{level} AND org=#{org};
    </select>
    <select id="getByPhone" resultType="accountSubject">
        select
        id, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,beginning_direction AS beginningDirection,
        previous_balance AS  previousBalance,previous_direction AS previousDirection,carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting,
        relevance_type AS relevanceType, relevance_item AS relevanceItem
        from t_accountant_subject
        where org=#{org} AND parent = #{parent} and name=#{name}
    </select>
    <select id="getByName" resultType="accountSubject">
        select
          subject
        from t_accountant_subject
        where org=#{org} and name=#{name} ORDER BY create_date LIMIT 1;
    </select>
    <select id="getSubjectCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM `t_accountant_subject` WHERE org=#{org} and level=#{level};
    </select>

    <select id="getSubjectAndChildren" resultType="accountSubject">
        select id, subject, org, name, category, level, state, balance_direction AS balanceDirection,creator, create_name AS createName, create_date AS createDate from `t_accountant_subject` WHERE org=#{org} and subject like CONCAT(#{subject},'%')  ORDER BY subject
    </select>

</mapper>