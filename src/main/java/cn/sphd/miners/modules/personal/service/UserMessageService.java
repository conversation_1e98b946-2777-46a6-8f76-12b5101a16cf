package cn.sphd.miners.modules.personal.service;


import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.system.entity.Code;
import cn.sphd.miners.modules.system.entity.CodeCategory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 *
 * Created by Administrator on 2015/10/12.
 */
public interface UserMessageService {

    List<UserMessage> findUserMessageByUserId(Integer uid, Integer state);

    List<UserMessage> getUserMessageBySenderId(Integer sid, Integer state);

    List<UserMessage> getUserMessageByAccountId(Integer accountId, Integer state);

    //wyu：目前仅会计使用此函数********
    void addUserMassage(UserMessage userMessage);
    //wyu: 查询用户消息数，从MessageService移植过来。
    public Long getMessageCountByUidAndState(Integer uid, Integer status);

    UserMessage getUserMessageById(Integer id);

    List<UserMessage> getManyMessageByMtStock(Integer mtStockId);

    void deleteMessage(UserMessage userMessage);

    void updateUserMassage(UserMessage userMessage);

    void updatePersonnelOvertime(PersonnelOvertime personnelOvertime);

    void updatePersonnelLeave(PersonnelLeave personnelLeave);

    PersonnelLeave getPersonnelLeaveById(Integer id);

    PersonnelReimburse getPersonnelReimburseById(Integer id);

    UserMessage getUserMessageByMessageId(Integer messageId,String messageType);  //报销中根据approvalProcessId查找消息

    UserMessage getUserMessageByMessageIdAndUserId(Integer messageId, String messageType, Integer UserId); //讨论区获取一条消息

    List<UserMessage> getUserMessageListByMessageId(Integer messageId,String messageType);

    List<UserMessage> getUserMessageListDelete(Integer messageId,String messageType);

    void updatePersonnelLeaveItem(PersonnelLeaveItem personnelLeaveItem);

    PersonnelLeaveItem getPersonnelLeaveItemById(Integer id);

    List<PersonnelLeaveItem> getPersonnelLeaveItemByLeaveId(Integer leaveId);

    UserMessage getMesByUserAndType(Integer messageId, String messageType, Integer UserId, String approvalStatus, Integer state);

    void updateUserMessagesByReceiveUserId(Integer oldUserId,Integer newUserId);

    void approvalUserMessage(UserMessage userMessage1, User user, Integer approvalStatus, String eventType, String messageType, ApprovalProcess approvalProcess, Integer personnelReimburId,String reason);

    List<UserMessage> findUserMessageByUserIdAndStateAndMessageState(Integer oldId, Integer state, Integer messageType);

    List<UserMessage> getUserMessageListHou();
}
