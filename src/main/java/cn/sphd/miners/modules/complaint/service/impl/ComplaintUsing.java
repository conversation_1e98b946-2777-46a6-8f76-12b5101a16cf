package cn.sphd.miners.modules.complaint.service.impl;

import cn.sphd.miners.modules.complaint.entity.ComplaintAttachment;
import cn.sphd.miners.modules.complaint.service.ComplaintService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

/**
 * Created by 朱思旭 on 2021/3/13.
 */
public class ComplaintUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;


    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            ComplaintService service = ac.getBean(ComplaintService.class, "complaintService");
            ComplaintAttachment attachment;
//            //wyu：假如有软删除方法且对象的.getType()=="1"表示对象未被删除，参考此处注释代码
//            ComplaintDetail detail;
//            ComplaintBase base;
//            if ((attachment = service.getSingelByComAtt(id)) != null && "1".equals(attachment.getType())
//                    && (detail = service.getSingleByComDet(attachment.getComplaintDetail()))!=null && "1".equals(detail.getType())
//                    && (base = service.getSingleByCom(attachment.getComplaint()))!=null && "1".equals(base.getType())
//            ) {
//                return filename.equals(attachment.getPath());
//            }
            //wyu：由于投诉不存在删除，直接判断主表对象存在即可。
            if ((attachment = service.getSingelByComAtt(id)) != null
                    && service.getSingleByComDet(attachment.getComplaintDetail())!=null
                    && service.getSingleByCom(attachment.getComplaint())!=null
            ) {
                return filename.equals(attachment.getPath());
            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是单实体使用，只需要使用id；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id.toString();
    }

    public ComplaintUsing(Integer id) {
        this.id = id;
    }

    public ComplaintUsing() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
