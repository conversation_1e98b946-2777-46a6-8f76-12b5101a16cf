<template>
  <div class="privacyPolicy">
    <Pageheader title="版本说明" :showBack="true"/>
    <div class="live-contain">
      <div class="live-cells">
        <div class="live-cell" v-for="(item, index) in list" :key="item.id" @click="jumpDetail(item)">
          <div>
            {{item.name}} <br>
            <div class="des">版本号：{{item.fileSn}}</div>
          </div>
          <el-icon class="arrow-right"><ArrowRight /></el-icon>
        </div>
      </div>
      <el-pagination
          small
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :page-count="pageInfo.totalPage"
          class="mt-4"
          @current-change="handleCurrentChange"
          hide-on-single-page
      />
    </div>
  </div>
</template>

<script setup>
import { getAboutModuleDetail } from "@/api/api.js";
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";

let list = ref([])
let pageInfo = ref({totalPage: 0})
const pageSize = 10
const router = useRouter();
const route = useRoute();
console.log('params', route.params)
let categoryId = route.params.id

onMounted(() => {
  getModuleDetail(1)
  console.log('list', list)
})

const getModuleDetail = (currentPageNo) => {
  let data = {
    category: categoryId,
    currentPageNo: currentPageNo,
    pageSize: pageSize
  }
  getAboutModuleDetail(data).then(res => {
    let data = res.data.list
    let page = res.data.pageInfo
    list.value = data
    pageInfo.value = page
  })
}

const handleCurrentChange = (val) => {
  getModuleDetail(val)
}
const jumpDetail = (item) => {
  router.push({ path: '/aboutFinalDetail/release/' + item.id });
}
</script>

<style lang="scss" scoped>
.mt-4{
  margin-top: 8px;
  justify-content: center;
}
.des{
  font-size: 14px;
  color: #666;
}
</style>

