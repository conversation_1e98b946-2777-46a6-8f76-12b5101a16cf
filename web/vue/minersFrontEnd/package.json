{"name": "minersFrontEnd", "version": "1.0.0", "private": true, "scripts": {"serve": "vite --host 0.0.0.0 --port 443", "dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "fast-glob": "^3.3.3", "font-awesome": "4.7.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0"}, "devDependencies": {"@pantajoe/bytes": "^1.0.2", "@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "axios": "^1.5.0", "element-plus": "^2.3.12", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "file-saver": "^2.0.5", "jquery": "^3.7.1", "js-base64": "^3.7.5", "js-cookie": "^3.0.5", "json-bigint": "^1.0.0", "moment": "^2.30.1", "nanoid": "^5.0.6", "prettier": "^3.0.3", "qs": "^6.11.2", "sass": "^1.67.0", "spark-md5": "^3.0.2", "vite": "^4.4.9", "vite-plugin-svg-icons": "^2.0.1", "vue-next-wxlogin": "^1.0.4", "vuex-persistedstate": "^4.1.0"}}