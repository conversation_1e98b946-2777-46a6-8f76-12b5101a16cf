package cn.sphd.miners.modules.accountantReportTax.dto;

import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 税种dto
 * */
public class AcctTaxDetailDto implements Serializable {

    private AcctTaxEntity taxEntity;//税种
    private AcctReportEntity reportEntity;//报表

    public AcctTaxDetailDto(AcctTaxEntity taxEntity,AcctReportEntity reportEntity){
        this.taxEntity = taxEntity;
        this.reportEntity = reportEntity;
    }

    public AcctTaxDetailDto(){}

    public AcctTaxEntity getTaxEntity() {
        return taxEntity;
    }

    public void setTaxEntity(AcctTaxEntity taxEntity) {
        this.taxEntity = taxEntity;
    }

    public AcctReportEntity getReportEntity() {
        return reportEntity;
    }

    public void setReportEntity(AcctReportEntity reportEntity) {
        this.reportEntity = reportEntity;
    }
}
