package cn.sphd.miners.modules.accountant.entity;

import java.math.BigDecimal;

/*
* 这是一个用于存放中间值的类
* 用于记录返回余额方向和金额，在试算时
* */
public class DirAndBalance {
    private String dir;
    private BigDecimal balance;

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
}
