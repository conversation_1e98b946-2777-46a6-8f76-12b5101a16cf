package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.modules.accountant.service.BuildAccountService;
import cn.sphd.miners.modules.accountant.util.Result;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-06-14 18:49
 * @description     这个类主要是关于建账和重新建账功能的新接口
 **/
@Controller
@RequestMapping("/accountant")
public class BuildAccountController {

    private static final String SUCCESS = "1";
    private static final String ERROR = "0";

    @Autowired
    BuildAccountService buildAccountService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;

    /*返回该机构所有的最后一级的科目，用于录入建账数据*/
    @ResponseBody
    @RequestMapping("/getBaseSubjectsByOrg.do")
    public Object getBaseSubjectsByOrg(User user) {
        Result result = new Result();
//        User user = (User) session.getAttribute("user");
        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
//        List<TAccountantSubjectEntity> subjectList = buildAccountService.getBaseSubjectsByOrg(org);
//        List<TAccountantCash> cashList = buildAccountService.getTempCashFlowByOrg(org);//返回建账时某机构的临时现金流量表数据t_accountant_cash现金暂存表
        JSONObject jsonObject = buildAccountService.getBaseSubjectsByOrg(org);
        result.setCode(SUCCESS);
        result.setMsg("");
        result.setData(jsonObject);
        return result;
    }

    /*获取当前建账状态
    * 0  -  建账提示
    * 1  -  建账提示中选择了是,进入的科目设置
    * 2  -  建账提示中选择了否,进入的科目设置
    * 3  -  科目已设置完,进入金额录入页面
    * 4  -  建账整体流程完成
    * */
    @ResponseBody
    @RequestMapping("/getBuildAccountState.do")
    public Object getBuildAccountState(User user) {
        Result result = new Result();
//        User user = (User) session.getAttribute("user");
        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        String res = buildAccountService.getBuildAccountState(org);
        JSONObject json = new JSONObject();
        json.put("res",res);
        json.put("time",getSysTime());
        if (res != null) {
            result.setCode(SUCCESS);
            result.setMsg("");
            result.setData(json);
        }
        else {
            result.setCode(ERROR);
            result.setMsg("");
            result.setData(json);
        }
        return result;
    }

    /*设置当前建账状态
    * state 当前建账状态
    * */
    @ResponseBody
    @RequestMapping("/setBuildAccountState.do")
    public Object setBuildAccountState(User user,String state) {
        Result result = new Result();
//        User user = (User) session.getAttribute("user");
//        int org = user.getOid();
        int res = buildAccountService.setBuildAccountState(user,state);
        JSONObject json = new JSONObject();
        json.put("res",res);
        json.put("time",getSysTime());
        setResult(res,result,json);
        return result;
    }

    /*
    * 保存科目建账数据
    * 最后一级科目的金额及数量保存
    * 页面上点暂存按钮时触发
    * */
    @ResponseBody
    @RequestMapping("/saveBuildAccount.do")
    public Object saveBuildAccount(User user,String listSubjects, String cashFlow) {
        Result result = new Result();
//        User user = (User) session.getAttribute("user");
        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        int res = buildAccountService.saveBuildAccount(org,listSubjects,cashFlow);
        setResult(res,result,res);

        return result;
    }

    /*
    * 金额核查
    * 1、返回最后一级的科目及其上一层父科目
    * 2、对比期末余额和期末余额数量是否和输入的一致
    * 把不同的地方统计出来总数并用红字标注
    *
    * */
    @ResponseBody
    @RequestMapping("/amountVerification.do")
    public Object amountVerification(User user,String listSubjects, String cashFlow) {
//        User user = (User) session.getAttribute("user");
        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        Result result = new Result();
        JSONObject json = buildAccountService.amountVerification(org,listSubjects,cashFlow);
        result.setCode(SUCCESS);
        result.setData(json);
        return result;
    }

    /*
    * 报表核查
    * 将试算结果写进科目周期表、现金流量表、资产负债表、利润表
    * 查看表报数据时需要调用之前已经做好的接口
    * 该接口只是把报表数据写进各个表
    * */
    @ResponseBody
    @RequestMapping("/reportVerification.do")
    public Object reportVerification(User user) {
//        User user = (User) session.getAttribute("user");
//        int org = user.getOid();
        Result result = new Result();
        JSONObject json = buildAccountService.reportVerification(user);
        result.setCode(SUCCESS);
        result.setData(json);
        return result;
    }

    /*
    * 建账完成
    * 把各报表的状态改成3，即总结账
    * 结账完成相当于是把上月的数据总结张
    * 需要在结账表中新增一条
    * 更新setting表的建账状态
    * */
    @ResponseBody
    @RequestMapping("/buildFinish.do")
    public Object buildFinish(User user) {
//        User user = (User) session.getAttribute("user");
//        int org = user.getOid();
        Result result = new Result();
        int res = buildAccountService.buildFinish(user);
        result.setCode(SUCCESS);
        result.setData(res);
        return result;
    }

    /*
    * 重新建账
    * 1、清除全部该机构的会计数据，科目、凭证、报表、账簿
    * 2、重置setting表里的rebuild状态和operate_state状态
    * */
    @ResponseBody
    @RequestMapping("/rebuild.do")
    public Object rebuild(User user) {
//        User user = (User) session.getAttribute("user");
        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        Result result = new Result();
        int res = buildAccountService.rebuild(org,user);
        result.setCode(SUCCESS);
        result.setData(res);
        return result;
    }

    /*
    * 检测是否需要重新建账
    * 1     需要重新建账
    * 0     不需要重新建账
    * */
    @ResponseBody
    @RequestMapping("/checkRebuildFlag.do")
    public Object checkRebuildFlag(User user) {
        int org = user.getOid();
        Result result = new Result();
//        int res = buildAccountService.checkRebuildFlag(org);
        result.setCode(SUCCESS);
        result.setData(1);//
        return result;
    }

    private void setResult(int res,Result result,Object data) {
        if (res > 0) {
            result.setCode(SUCCESS);
            result.setMsg("");
            result.setData(data);
        }
        else {
            result.setCode(ERROR);
            result.setMsg("");
            result.setData(data);
        }
    }

    private String getSysTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
        return sdf.format(new Date());
    }

}
