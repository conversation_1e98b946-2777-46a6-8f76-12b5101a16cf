package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquSupplierHistory;
import cn.sphd.miners.modules.equipment.mapper.TEquSupplierHistoryMapper;
import cn.sphd.miners.modules.equipment.service.ITEquSupplierHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_供应商名录历史
 * 20230209 1.247装备器具新增Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquSupplierHistoryServiceImpl implements ITEquSupplierHistoryService {
    @Autowired
    private TEquSupplierHistoryMapper tEquSupplierHistoryMapper;

    /**
     * 查询装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_供应商名录历史
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public TEquSupplierHistory selectTEquSupplierHistoryById(Long id) {
        return tEquSupplierHistoryMapper.selectTEquSupplierHistoryById(id);
    }

    /**
     * 查询装备管理_供应商名录历史
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public List<TEquSupplierHistory> selectTEquSupplierHistoryList(TEquSupplierHistory tEquSupplierHistory) {
        return tEquSupplierHistoryMapper.selectTEquSupplierHistoryList(tEquSupplierHistory);
    }

    /**
     * 新增装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int insertTEquSupplierHistory(TEquSupplierHistory tEquSupplierHistory) {
        return tEquSupplierHistoryMapper.insertTEquSupplierHistory(tEquSupplierHistory);
    }

    /**
     * 修改装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int updateTEquSupplierHistory(TEquSupplierHistory tEquSupplierHistory) {
        return tEquSupplierHistoryMapper.updateTEquSupplierHistory(tEquSupplierHistory);
    }

    /**
     * 批量删除装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_供应商名录历史
     *            20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquSupplierHistoryByIds(String... ids) {
        return tEquSupplierHistoryMapper.deleteTEquSupplierHistoryByIds(ids);
    }

    /**
     * 删除装备管理_供应商名录历史
     * 20230209 1.247装备器具新增信息
     *
     * @param id 装备管理_供应商名录历史
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquSupplierHistoryById(Long id) {
        return tEquSupplierHistoryMapper.deleteTEquSupplierHistoryById(id);
    }
}
