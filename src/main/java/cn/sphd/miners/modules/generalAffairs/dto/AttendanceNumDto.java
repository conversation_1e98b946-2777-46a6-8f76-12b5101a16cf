package cn.sphd.miners.modules.generalAffairs.dto;

import java.io.Serializable;

/**
 * Created by Administrator on 2018/8/2.
 */
public class AttendanceNumDto implements Serializable {
    Integer lateNum;
    Integer leaveEarlyNum;
    Integer outsideNum;
    Integer leaveNum;
    Integer travelNum;
    Integer absenteeismNum;
    Integer overTimeNum;

    public Integer getLateNum() {
        return lateNum;
    }

    public void setLateNum(Integer lateNum) {
        this.lateNum = lateNum;
    }

    public Integer getLeaveEarlyNum() {
        return leaveEarlyNum;
    }

    public void setLeaveEarlyNum(Integer leaveEarlyNum) {
        this.leaveEarlyNum = leaveEarlyNum;
    }

    public Integer getOutsideNum() {
        return outsideNum;
    }

    public void setOutsideNum(Integer outsideNum) {
        this.outsideNum = outsideNum;
    }

    public Integer getLeaveNum() {
        return leaveNum;
    }

    public void setLeaveNum(Integer leaveNum) {
        this.leaveNum = leaveNum;
    }

    public Integer getTravelNum() {
        return travelNum;
    }

    public void setTravelNum(Integer travelNum) {
        this.travelNum = travelNum;
    }

    public Integer getAbsenteeismNum() {
        return absenteeismNum;
    }

    public void setAbsenteeismNum(Integer absenteeismNum) {
        this.absenteeismNum = absenteeismNum;
    }

    public Integer getOverTimeNum() {
        return overTimeNum;
    }

    public void setOverTimeNum(Integer overTimeNum) {
        this.overTimeNum = overTimeNum;
    }

    public AttendanceNumDto() {
    }

    public AttendanceNumDto(Long lateNum, Long leaveEarlyNum, Long outsideNum, Long leaveNum, Long travelNum, Long absenteeismNum, Long overTimeNum) {
        if (lateNum!=null){
            this.lateNum = lateNum.intValue();
        }else {
            this.lateNum = 0;
        }

        if (leaveEarlyNum!=null){
            this.leaveEarlyNum = leaveEarlyNum.intValue();
        }else {
            this.leaveEarlyNum = 0;
        }

        if (outsideNum!=null){
            this.outsideNum = outsideNum.intValue();
        }else {
            this.outsideNum = 0;
        }

        if (leaveNum!=null){
            this.leaveNum = leaveNum.intValue();
        }else {
            this.leaveNum = 0;
        }

        if (travelNum!=null){
            this.travelNum = travelNum.intValue();
        }else {
            this.travelNum = 0;
        }

        if (absenteeismNum!=null){
            this.absenteeismNum = absenteeismNum.intValue();
        }else {
            this.absenteeismNum = 0;
        }

        if (overTimeNum!=null){
            this.overTimeNum = overTimeNum.intValue();
        }else {
            this.overTimeNum = 0;
        }

    }
}
