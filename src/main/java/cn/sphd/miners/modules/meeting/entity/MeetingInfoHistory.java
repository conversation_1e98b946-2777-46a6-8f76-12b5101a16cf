package cn.sphd.miners.modules.meeting.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "t_meeting_info_history")
public class MeetingInfoHistory {


    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name="org"  , nullable=true , unique=false)
    private Integer org;//

    @Column(name="meeting"  ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long meetingId;//会议ID

    @Column(name="topic"  , length=50 , nullable=true , unique=false)
    private String topic;   //会议名称

    @Column(name="plan_time"   , nullable=true , unique=false)
    private Date planTime;//计划开始时间

    @Column(name="`plan_duration`"  , nullable=true , unique=false)
    private Integer planDuration;//计划时长(分钟)

    @Column(name="fact_time"   , nullable=true , unique=false)
    private Date factTime;//实际开始时间

    @Column(name="finish_time"  ,  nullable=true , unique=false)
    private Date finishTime;   //'结束时间'

    @Column(name="state"  ,nullable=true , unique=false)
    private Integer state=0;   //状态:0-未开始,1-进行中,2-已结束,3-已过期

    @Column(name="pattern"   , nullable=true , unique=false)
    private Integer pattern;//型式:1-wondess线上会议,2-其它线上会议,3-线下会议'

    @Column(name="address"  , length=255 , nullable=true , unique=false)
    private String address;   //会议地址

    @Column(name="matters"  , length=255 , nullable=true , unique=false)
    private String matters;   //注意事项或其它说明

    @Column(name="enabled"  ,nullable=true , unique=false)
    private Integer enabled=1;   //状态:0-不启用,1-启用

    @Column(name="enabled_time"  ,  nullable=true , unique=false)
    private Date enabled_time;   //'启停用时间'

    @Column(name="approval_type"  ,nullable=true , unique=false)
    private Integer approvalType=2;   //审批类型:1-文管,2-逐级审批

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="approval_instance"  ,nullable=true , unique=false)
    private Integer approvalInstance;   //审批实例ID

    @Column(name="teminate_state"  ,nullable=true , unique=false)
    private Integer teminateState=0;   //终止状态,0-未终止,1-已终止,2-已复用

    @Column(name="teminate_time"  ,  nullable=true , unique=false)
    private Date teminateTime;   //'终止时间'

    @Column(name="teminate_reason"  , length=255 , nullable=true , unique=false)
    private String teminateReason;   //终止原因

    @Column(name="teminater"  ,nullable=true , unique=false)
    private Integer teminater;   //终止人ID

    @Column(name="teminater_name"  , length=100 , nullable=true , unique=false)
    private String teminaterName;   //终止人姓名

    @Column(name="auditor"  ,nullable=true , unique=false)
    private Integer auditor;   //文管ID

    @Column(name="audit_name"  , length=100 , nullable=true , unique=false)
    private String auditName;   //文管姓名

    @Column(name="audit_date"  ,  nullable=true , unique=false)
    private Date auditDate;   //''文管审批时间''

    @Column(name="approve_status"  ,nullable=true , unique=false)
    private Integer approveStatus=1;   //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,5-终止申请 撤回

    @Column(name="approver"  ,nullable=true , unique=false)
    private Integer approver;   //批准者ID

    @Column(name="approver_name"  , length=100 , nullable=true , unique=false)
    private String approverName;   //批准者姓名

    @Column(name="approve_date"  ,  nullable=true , unique=false)
    private Date approveDate;   //批准时间

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;   //申请备注

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;   //审批备注/驳回原因

    @Column(name="question_num"  ,nullable=true , unique=false)
    private Integer questionNum;   //问题个数

    @Column(name="participant_num"  ,nullable=true , unique=false)
    private Integer participantNum;   //参与人数

    @Column(name="sign_num"  ,nullable=true , unique=false)
    private Integer signNum;   //签收人数

    @Column(name="present_num"  ,nullable=true , unique=false)
    private Integer presentNum;   //出席人数

    @Column(name="others_num"  ,nullable=true , unique=false)
    private Integer othersNum=0;   //其它人数

    @Column(name="receipt_num"  ,nullable=true , unique=false)
    private Integer receiptNum;   //签收记录人数

    @Column(name="creator"  ,nullable=true , unique=false)
    private Integer creator;   //创建人/发起人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  ,nullable=true , unique=false)
    private Integer operation=1;   //操作:1-增,2-删,3-更改会议信息,4-更改参会者,5-更改问题

    @Column(name="previous_id"  ,nullable=true , unique=false)
    private Integer previousId;   //修改前记录ID

    @Column(name="version_no"  ,nullable=true , unique=false)
    private Integer versionNo;   //版本号,每次修改+1


    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="meeting", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MeetingInfo meetingInfo ;//

    //会议问题历史
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= MeetingQuestionHistory.class, fetch= FetchType.EAGER, mappedBy="meetingInfoHistory", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<MeetingQuestionHistory> meetingQuestionHashSet = new HashSet<MeetingQuestionHistory>();

    //会议参与人历史
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= MeetingParticipantHistory.class, fetch= FetchType.EAGER, mappedBy="meetingInfoHistory", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<MeetingParticipantHistory> meetingParticipantHashSet = new HashSet<MeetingParticipantHistory>();

    @Transient
    private Integer recorderUserId;  //参与人id

    @Transient
    private String recorderUserName;  //参与人姓名


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }



    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Date getPlanTime() {
        return planTime;
    }

    public void setPlanTime(Date planTime) {
        this.planTime = planTime;
    }

    public Integer getPlanDuration() {
        return planDuration;
    }

    public void setPlanDuration(Integer planDuration) {
        this.planDuration = planDuration;
    }

    public Date getFactTime() {
        return factTime;
    }

    public void setFactTime(Date factTime) {
        this.factTime = factTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getPattern() {
        return pattern;
    }

    public void setPattern(Integer pattern) {
        this.pattern = pattern;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMatters() {
        return matters;
    }

    public void setMatters(String matters) {
        this.matters = matters;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Date getEnabled_time() {
        return enabled_time;
    }

    public void setEnabled_time(Date enabled_time) {
        this.enabled_time = enabled_time;
    }

    public Integer getApprovalType() {
        return approvalType;
    }

    public void setApprovalType(Integer approvalType) {
        this.approvalType = approvalType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getApprovalInstance() {
        return approvalInstance;
    }

    public void setApprovalInstance(Integer approvalInstance) {
        this.approvalInstance = approvalInstance;
    }

    public Integer getTeminateState() {
        return teminateState;
    }

    public void setTeminateState(Integer teminateState) {
        this.teminateState = teminateState;
    }

    public Date getTeminateTime() {
        return teminateTime;
    }

    public void setTeminateTime(Date teminateTime) {
        this.teminateTime = teminateTime;
    }

    public String getTeminateReason() {
        return teminateReason;
    }

    public void setTeminateReason(String teminateReason) {
        this.teminateReason = teminateReason;
    }

    public Integer getTeminater() {
        return teminater;
    }

    public void setTeminater(Integer teminater) {
        this.teminater = teminater;
    }

    public String getTeminaterName() {
        return teminaterName;
    }

    public void setTeminaterName(String teminaterName) {
        this.teminaterName = teminaterName;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditName() {
        return auditName;
    }

    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApprover() {
        return approver;
    }

    public void setApprover(Integer approver) {
        this.approver = approver;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(Integer questionNum) {
        this.questionNum = questionNum;
    }

    public Integer getParticipantNum() {
        return participantNum;
    }

    public void setParticipantNum(Integer participantNum) {
        this.participantNum = participantNum;
    }

    public Integer getSignNum() {
        return signNum;
    }

    public void setSignNum(Integer signNum) {
        this.signNum = signNum;
    }

    public Integer getPresentNum() {
        return presentNum;
    }

    public void setPresentNum(Integer presentNum) {
        this.presentNum = presentNum;
    }

    public Integer getOthersNum() {
        return othersNum;
    }

    public void setOthersNum(Integer othersNum) {
        this.othersNum = othersNum;
    }

    public Integer getReceiptNum() {
        return receiptNum;
    }

    public void setReceiptNum(Integer receiptNum) {
        this.receiptNum = receiptNum;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public MeetingInfo getMeetingInfo() {
        return meetingInfo;
    }

    public void setMeetingInfo(MeetingInfo meetingInfo) {
        this.meetingInfo = meetingInfo;
    }

    public Set<MeetingQuestionHistory> getMeetingQuestionHashSet() {
        return meetingQuestionHashSet;
    }

    public void setMeetingQuestionHashSet(Set<MeetingQuestionHistory> meetingQuestionHashSet) {
        this.meetingQuestionHashSet = meetingQuestionHashSet;
    }

    public Set<MeetingParticipantHistory> getMeetingParticipantHashSet() {
        return meetingParticipantHashSet;
    }

    public void setMeetingParticipantHashSet(Set<MeetingParticipantHistory> meetingParticipantHashSet) {
        this.meetingParticipantHashSet = meetingParticipantHashSet;
    }

    public Integer getRecorderUserId() {
        return recorderUserId;
    }

    public void setRecorderUserId(Integer recorderUserId) {
        this.recorderUserId = recorderUserId;
    }

    public String getRecorderUserName() {
        return recorderUserName;
    }

    public void setRecorderUserName(String recorderUserName) {
        this.recorderUserName = recorderUserName;
    }
}
