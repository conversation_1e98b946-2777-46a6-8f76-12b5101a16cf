package cn.sphd.miners.modules.resourceAuthority.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAclLog;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by 朱思旭 on 2020/9/25.
 */
@Component
public interface ResAclLogMapper  extends BaseMapper<ResAclLog>{

    List<ResAclLog> resAclLoglistPage(QueryData query);

}
