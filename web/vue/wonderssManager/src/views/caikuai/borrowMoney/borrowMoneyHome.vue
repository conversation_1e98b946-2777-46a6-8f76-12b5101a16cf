<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit"
                  :showClose=true
                  :runMyBackFun=true
                  @mycloseFun="handleCustomClose"
                  :showBack=true
                  @myBackFun="handleCustomBack">
      </PageHeader>
    </div>
    <div class="sticky ty-fff">
      <p class="h35f14 padding-0-15 spaceBetween">
        <span>以下为公司的借款</span>
        <span style="color:#337ab7;" @click="wJ()">已完结的数据</span>
      </p>
    </div>
    <div class="re margin_top_16">
      <el-table
          :data="list"
          style="width: 100%"
          stripe
          header-row-class-name="el_title"
          :default-sort="{prop: 'date', order: 'descending'}"
          :header-cell-style="{background: '#ffffff',color:'#2c3e50'}"
          @cell-click="rou"
      >
        <el-table-column prop="borrower" label="收款方" header-align="center" align="center" min-width="130"
                         label-class-name="h35f16"
                         show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="principalAmount" label="本金" align="center" header-align="center" min-width="100"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="arriveDate" label="收款日期" align="center" header-align="center" min-width="100"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column min-width="40" align="center">
          <el-icon>
            <arrow-right/>
          </el-icon>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "borrowMoneyHome",
  mixins: [finance],

  data() {
    return {
      tit: "财会",
      list: []
    }
  },
  mounted() {
    this.getLoans()
  },
  methods: {
    rou(row) {
      this.$router.push({
        path: '/borrowMoneyDetails', query: {id: row.id}
      })
    },
    getLoans() {
      const params = {state: true, loanType: 2}
      this.financeApi.getLoansList(params).then(res1 => {
        this.list = res1.data.data.list
        this.list.forEach(item => {
          item.arriveDate = new Date(item.arriveDate).format('yyyy-MM-dd');
          item.principalAmount = this.formatCurrency(item.principalAmount)
        });
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    wJ() {
      this.$router.push({path: '/bmEndDataList'})
    },
    handleCustomBack() {
      this.$router.push("/caikuai");
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";


.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.box {
  background-color: $tyColorBg;
}

::v-deep .el-table__row td, .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-bottom: none;
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #ffffff;
}

//非斑马纹颜色
::v-deep .el-table tr {
  background: #e8e8e8;
}

.re {
  position: relative;
  top: 40px;
}
</style>