package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2020-10-10 
 */

@Entity ( name ="MtMaterialImportLog" )
@Table ( name ="t_mt_material_import_log" )
public class MtMaterialImportLog  implements Serializable {
	/** ID */

	private Integer id;

	/** 机构ID*/

	private Integer org;

	/**导入总数*/

	private Integer materialCount;

	/** 导入成功数*/

	private Integer materialSuccess;

	/**是否采购过,true-是,false-否*/

	private Integer isPurchased;

	/**备注*/

	private String memo;

	/**创建人id*/

	private Integer creator;

	/**创建人*/

	private String createName;

	/** 创建时间	 */

	private Date createTime;

	/**修改人id	 */

	private Integer updator;

	/** 修改人	 */

	private String updateName;

	/**修改时间 */

	private Date updateTime;

	/**操作:1-增,2-删,3-修改 */

	private String operation;

	/**修改前记录ID */

	private Integer previousId;

	/**版本号,每次修改+1*/

	private Integer versionNo;

	//1.266
	private Integer state;//状态:1-上传成功,2-导入临时表,3-完成

	private String uploadFile;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	@Column(name = "org" )
	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}
	@Column(name = "material_count" )
	public Integer getMaterialCount() {
		return materialCount;
	}

	public void setMaterialCount(Integer materialCount) {
		this.materialCount = materialCount;
	}
	@Column(name = "material_success" )
	public Integer getMaterialSuccess() {
		return materialSuccess;
	}

	public void setMaterialSuccess(Integer materialSuccess) {
		this.materialSuccess = materialSuccess;
	}
	@Column(name = "is_purchased" )
	public Integer getIsPurchased() {
		return isPurchased;
	}

	public void setIsPurchased(Integer isPurchased) {
		this.isPurchased = isPurchased;
	}
	@Column(name = "memo" )
	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
	@Column(name = "creator" )
	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}
	@Column(name = "create_name" )
	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}
	@Column(name = "create_time" )
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	@Column(name = "updator" )
	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}
	@Column(name = "update_name" )
	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}
	@Column(name = "update_time" )
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	@Column(name = "operation" )
	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}
	@Column(name = "previous_id" )
	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}
	@Column(name = "version_no" )
	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

	@Column(name = "state")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "upload_file")
	public String getUploadFile() {
		return uploadFile;
	}

	public void setUploadFile(String uploadFile) {
		this.uploadFile = uploadFile;
	}
}
