package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import org.springframework.context.ApplicationContext;

/**
 * Created by Administrator on 2019/1/24.
 * 备忘与日程 延时调用
 */
public class DmmScheduleRemind implements DelayCallback {
    private Integer id;

    public Integer getId() {
        return id;
    }

    public DmmScheduleRemind(Integer id) {
        this.id = id;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        DmmScheduleService dmmScheduleService = ac.getBean(DmmScheduleService.class);
        dmmScheduleService.addDmmScheduleRemind(id);
    }
}
