<template>
  <div id="shList" class="potential">
    <TY_NavTop :title="searchTtl"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div>
              <div class="queryContent">{{queryData.beginDate | formatDate('yyyy年MM月dd日')}}-{{queryData.endDate | formatDate('yyyy年MM月dd日')}}期间</div>
              <div class="queryContent">
                <span v-if="paramQuery.state === '0'"><span v-if="source === '2' && paramQuery.creator !== ''">{{paramQuery.creatorName}}录入的</span>终止接触的潜在客户申请</span>
                <span v-if="paramQuery.state === '1'"><span v-if="source === '2' && paramQuery.creator !== ''">{{paramQuery.creatorName}}录入的</span>已正式合作的潜在客户申请</span>
              </div>
              <div class="queryContent" v-if="paramQuery.sale !== ''">
                <span>销售负责人为{{paramQuery.saleName}}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的潜在客户申请共如下{{resultList.length}}条。
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells">
          <a class="ui-col" v-for="(item,index) in resultList" :key="index" @click="jumpDetail(item.id)">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div>{{item.fullName}}</div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
            <div class="ui-col__ft">
              <div class="createDetail">录入者 {{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'potentialQuerylist',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      searchTtl: '',
      paramQuery: {},
      resultList: [],
      queryData: {
        beginDate: 1560408078000,
        endDate: 1560408078000
      },
      state: '',
      source: 1
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    // state （1- 自定义 ，2 -仅看年报，3- 仅看月报）
    // type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    console.log(paramQuery)
    this.paramQuery = paramQuery
    let that = this
    that.source = paramQuery.source
    if (paramQuery.source === '1') {
      that.searchTtl = '潜在客户申请'
    } else {
      that.searchTtl = '潜在客户受理'
    }
    if (paramQuery.date === '7day') {
      let today = new Date().getTime()
      that.queryData.beginDate = Number(today) - 604800000
      that.queryData.endDate = today
    } else if (paramQuery.date === 'thisMonth') {
      let firstDay = new Date(new Date().setDate(1))
      that.queryData.beginDate = firstDay.getTime()
      that.queryData.endDate = new Date().getTime()
    } else {
      that.queryData.beginDate = new Date(paramQuery.startTime + ' 00:00:01').getTime()
      that.queryData.endDate = new Date(paramQuery.endTime + ' 00:00:01').getTime()
    }
    this.$http.post('../../../sale/selectPotentialCustomer.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body
      if (data) {
        that.resultList = data.list
      } else {
        console.log('加载失败！')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    jumpDetail: function (id) {
      let source = this.source
      console.log('搜索列表： ' + source)
      this.$router.push({
        path: `/potentialQueryDetail/${id}/${source}`
      })
    }
  }
}
</script>
