<!--日流水的具体详情-->
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>

    <!--    DataDetail 数据录入的详情 -->
    <div v-if="infoType === 'DataDetail' && info && info.content && info">
      <div v-if="info.kind == 5">
        <ul :class="le === '支出'? 'xiangC1' :'xiangC2' ">
          <div>
            <li> {{ new Date(info.content.updateDate).format('yyyy-MM-dd') }} {{ info.content.saveBankName }} {{ info.content.accout }} </li>
          </div>
          <div>
            <li> {{le}} {{ info.content.amount }}元</li>
          </div>
        </ul>
        <div class="hr_gray"></div>
        <ul class="xiangUlB margin-10-15">
          <li><span>用途/摘要</span>
            <span class="turnHang" >{{ info.content.purpose }} / {{ info.content.summary }}</span>
          </li>
          <li><span>票据</span><span @click="showImg">共{{ info.content.billQuantity || info.financeAccountBillImages?.length || 0 }}张 <i class="fa fa-angle-right"></i></span></li>
        </ul>
        <div class="hr_gray"></div>
        <ul class="xiangUlB margin-10-15" v-if="info.content.billType === '1'">
          <li><span>{{ le }}类别</span><span>转账支票存入</span></li>
          <li><span>票据金额</span><span>{{ info.content.amount }}</span></li>
          <li><span>支票到期日</span><span>{{ new Date(info.content.expireDate).format('yyyy-MM-dd') }}</span></li>
          <li><span>付款单位</span><span>{{ info.content.payer }}</span></li>
          <li><span>支票号</span><span>{{ info.content.returnNo }}</span></li>
          <li @click="ticketInfo"><span>转账支票更多信息</span> <span class="fa fa-angle-right"></span></li>

          <li><span>备注</span><span class="turnHang">{{ info.content.memo }}</span></li>
          <li class="endLi">
            <div  style="width: 70%">
              <span>操作者</span>
              <span>{{ info.content.updateName }}</span>
              <span>{{ new Date(info.content.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
            </div>
          </li>
        </ul>

        <ul class="xiangUlB margin-10-15" v-if="info.content.billType === '2'">
          <li><span>{{ le }}类别</span><span>承兑汇票存入</span></li>
          <li><span>票据金额</span><span>{{ info.content.amount }}</span></li>
          <li><span>汇票到期日</span><span>{{ new Date(info.content.expireDate).format('yyyy-MM-dd') }}</span></li>
          <li><span>付款单位</span><span>{{ info.content.payer }}</span></li>
          <li><span>汇票号</span><span>{{ info.content.returnNo }}</span></li>
          <li @click="ticketInfo"><span>承兑汇票更多信息</span> <span class="fa fa-angle-right"></span></li>

          <li><span>备注</span><span class="turnHang">{{ info.content.memo }}</span></li>
          <li class="endLi">
            <div  style="width: 70%">
              <span>操作者</span>
              <span>{{ info.content.updateName }}</span>
              <span>{{ new Date(info.content.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
            </div>
          </li>
        </ul>
      </div>
      <div v-else-if="info.kind == 4">
        <ul :class="le === '支出'? 'xiangC1' :'xiangC2' ">
          <div>
            <li> {{ new Date(info.content.createDate).format('yyyy-MM-dd') }} {{ info.content.bankName }} {{ info.content.account }} </li>
          </div>
          <div>
            <li> {{le}} {{ info.content.amount.toFixed(2) }}元（{{ info.content.type ? '转账支票' : '承兑汇票' }}{{ info.financeAccountBill.billNo }}）</li>
          </div>
        </ul>
        <div class="hr_gray"></div>
        <ul class="xiangUlB margin-10-15">
          <li><span>用途/摘要</span>
            <span class="turnHang" >{{ info.content.purpose }} / {{ info.content.summary }}</span>
          </li>
          <li><span>票据</span><span @click="showImg">共{{ info.content.billQuantity || info.financeAccountBillImages?.length || 0 }}张 <i class="fa fa-angle-right"></i></span></li>
        </ul>
        <div class="hr_gray"></div>
        <ul class="xiangUlB margin-10-15">
          <li><span>{{ le }}类别</span><span>{{ formatSubType(info.content) }}</span></li>
          <li><span>票据金额</span><span>{{  info.content.billAmount  }}</span></li>
          <li><span>支票到期日</span><span>{{ new Date(info.content.expireDate).format('yyyy-MM-dd')  }}</span></li>
          <li><span>收款单位</span><span>{{ info.content.receiveCorp }}</span></li>
          <li><span>支票号</span><span>{{ info.financeAccountBill.billNo }}</span></li>
          <li><span>转账支票更多信息</span><span class="fa fa-angle-right fz20"> </span></li>

          <li><span>备注</span><span class="turnHang">{{ info.content.memo }}</span></li>
          <li class="endLi">
            <div  style="width: 70%">
              <span>操作者</span>
              <span>{{ info.content.createName }}</span>
              <span>{{ new Date(info.content.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
            </div>
          </li>
        </ul>


      </div>
      <div v-else>
        <ul :class="le === '支出'? 'xiangC1' :'xiangC2' ">
          <div>
            <li> {{ new Date(info.content.auditDate).format('yyyy-MM-dd') }} {{ info.content.accountBank }}</li>
          </div>
          <div>
            <li> {{le}} {{ le === '支出'? info.content.debit : info.content.credit  }}元 {{ (info.kind != 6 && info.kind != 7 && info.kind != 8)? '(' + chargePayMethod(info.content.method) + ')' : '' }} </li>
          </div>
        </ul>
        <div class="hr_gray"></div>
        <ul class="xiangUlB margin-10-15">
          <li><span>用途/摘要</span>
            <span class="turnHang" >{{ info.content.purpose }} / {{ info.content.summary }}</span>
          </li>
          <li><span>票据</span><span @click="showImg">共{{ info.content.billQuantity || info.financeAccountBillImages?.length || 0 }}张 <i class="fa fa-angle-right"></i></span></li>
        </ul>
        <div class="hr_gray"></div>
        <ul class="xiangUlB margin-10-15">
          <li><span>{{ le }}类别</span><span>{{ formatSubType(info.content) }}</span></li>

          <div v-if="info.kind ==='2' "></div>
          <div v-if="info.kind ==='3'"></div>
          <div v-if="info.kind ==='4'"></div>
          <div v-if="info.kind ==='5'"></div>

          <div v-if="info.kind == 6 || info.kind == 8">
            <li v-if="le === '支出'"><span>收款方</span><span>{{ info.content.credit ? info.content.accountBank : info.content.oppositeAccount }}</span></li>
            <li v-else><span>付款方</span><span>{{ info.content.credit ? info.content.oppositeAccount : info.content.accountBank }}</span></li>
          </div>
          <div v-if="info.kind == 7">
            <li v-if="le === '支出'"><span>收款方</span><span>{{ (info.content.oppositeAccount) }}</span></li>
            <li v-else><span>付款方</span><span>{{ (info.content.oppositeAccount) }}</span></li>
          </div>
          <div v-if="formatSubType(info.content) === '其他财务费用'">
            <li><span>票据日期</span><span>{{ new Date(info.content.billDate).format('yyyy-MM-dd') }}</span></li>
            <li><span>收款方</span><span>{{ info.content.oppositeCorp }}</span></li>
            <li><span>经手人</span><span>{{ info.content.auditorName }}</span></li>
          </div>

          <div v-if="formatSubType(info.content) === '其他支出'">
            <li><span>票据日期</span><span>{{ new Date(info.content.billDate).format('yyyy-MM-dd') }}</span></li>
            <li><span>票据金额合计</span><span>{{ info.content.billAmount }}</span></li>
            <li><span>收款方</span><span>{{ info.content.oppositeCorp }}</span></li>
            <li><span>经手人</span><span>{{ info.content.auditorName }}</span></li>
          </div>



          <li><span>备注</span><span class="turnHang">{{ info.content.memo }}</span></li>
          <li class="endLi">
            <div  style="width: 70%">
              <span>录入者</span>
              <span>{{ info.content.createName }}</span>
              <span>{{ new Date(info.content.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!--    OrdLoanDetail 常规借款 -->
    <div v-if="infoType === 'OrdLoanDetail' && info && info.loanDetail">
      <ul class="xiangC1" v-if="le === '支出'">
        <li> {{ new Date(info.loanDetail.paymentDate).format('yyyy-MM-dd') }} {{ info.loanDetail.incomeMethod === '3' ? info.loanDetail.billBank + info.loanDetail.account : info.loanDetail.receiveBank}} </li>
        <li> {{le}} {{info.loanDetail.principalAmount }}元 {{ info.loanDetail.incomeMethod === '3' ? '(' + '转账支票' + info.loanDetail.billNo + ')' : '(' +chargePayMethod(info.loanDetail.incomeMethod)+')' }} </li>
      </ul>
      <ul class="xiangC2" v-else>
        <li> {{ new Date(info.loanDetail.arriveDate).format('yyyy-MM-dd') }} {{ info.loanDetail.receiveBank }}</li>
        <li> {{le}} {{  info.loanDetail.principalAmount }}元</li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>用途/摘要</span>
          <span class="turnHang" >{{info.purpose === info.summary ? info.purpose : info.purpose + info.summary }} 向公司借款</span>
        </li>
        <li><span>票据</span><span @click="showImgTip">共0张 <i class="fa fa-angle-right"></i></span></li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <div v-if="info.loanDetail.loanType === '1'">
          <li><span>收入类别</span><span>款项借入</span></li>
          <li><span>收入方式</span><span>{{ chargePayMethod(info.loanDetail.incomeMethod) }}</span></li>
          <li><span>付款方</span><span>{{ info.loanDetail.lender }}</span></li>

        </div>
        <div v-else>
          <li><span>支出类别</span><span>款项借出</span></li>
          <li><span>收款方</span><span>{{ info.loanDetail.borrower }}</span></li>
        </div>
        <li class="loan" @click="goLoanDetail"><span>借款详情</span><span class="fa fa-angle-right"></span></li>
        <li class="endLi">
          <div  style="width: 70%">
            <span>创建</span>
            <span>{{ info.loanDetail.createName }}</span>
            <span>{{ new Date(info.loanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </div>
        </li>
      </ul>

<!--      ========================-->


    </div>


    <!--    ordRepaymentDetail 常规借款的 收付款 -->
    <div v-if="infoType === 'ordRepaymentDetail' && info && info.id">
      <ul :class="le === '支出'? 'xiangC1' :'xiangC2' ">
        <li> {{ new Date(info.repaymentTime).format('yyyy-MM-dd') }} {{ info.receiveBank || info.saveBankAccount }} </li>
        <li> {{le}} {{ info.repaymentAmount }}元
          <span v-if="info.repaymentMethod != '1'">({{ chargePayMethod(info.repaymentMethod) }}{{ info.billNo || '' }})</span>
        </li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>用途/摘要</span>
          <span class="turnHang" >{{  info.purpose === info.summary ? info.purpose : info.purpose + info.summary   }}</span>
        </li>
        <li><span>票据</span><span @click="showImgTip">共0张 <i class="fa fa-angle-right"></i></span></li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <div v-if="info.loanType === '1'">
          <li><span>支出类别</span><span>还款</span></li>
          <li><span>收款方</span><span>{{ info.oppositeCorp }}</span></li>

        </div>
        <div v-else>
          <li><span>收入类别</span><span>收回借款</span></li>
          <li><span>收入方式</span><span>{{ chargePayMethod(info.repaymentMethod) }}</span></li>
          <li><span>付款方</span><span>{{ info.oppositeCorp }}</span></li>
        </div>
        <li class="loan" @click="goLoanPayDetail"><span>借款详情</span><span class="fa fa-angle-right"></span></li>
        <li class="endLi">
          <div  style="width: 70%">
            <span>创建</span>
            <span>{{ info.createName }}</span>
            <span>{{ new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </div>
        </li>
      </ul>


    </div>


    <!--    WageDetail 工资，个税，公积金，社保  -->
    <div v-if="infoType === 'WageDetail' && info && info.period">
      <ul :class="le === '支出'? 'xiangC1' :'xiangC2' ">
        <li> {{ new Date(info.personnelPayHistory.createDate).format('yyyy-MM-dd') }} {{ info.financeAccount.bankName }} {{ info.financeAccount.account }} </li>
        <li> {{le}} {{ info.personnelPayHistory.pay }}元</li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>用途/摘要</span>
          <span class="turnHang" >{{  info.purpose === info.summary ? info.purpose : info.purpose + info.summary   }}</span>
        </li>
        <li><span>票据</span><span @click="showImgTip">共0张 <i class="fa fa-angle-right"></i></span></li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>支出类别</span><span>{{ formatWageType( info.type) }}</span></li>
        <li class="loan" @click="goEmployeeDetail"><span>详情</span><span class="fa fa-angle-right"></span></li>
        <li class="endLi">
          <div  style="width: 70%">
            <span>创建</span>
            <span>{{ info.personnelPayHistory.createName }}</span>
            <span>{{ new Date(info.personnelPayHistory.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </div>
        </li>
      </ul>

<!--      ========================-->


    </div>


    <!--    CollectDetail 回款详情  -->
    <div v-if="infoType === 'CollectDetail' && info && info.slCollectApplication">
      <ul :class="le === '支出'? 'xiangC1' :'xiangC2' ">
        <li v-if="info.slCollectApplication.method == 3 || info.slCollectApplication.method == 4"> {{ new Date(info.slCollectApplication.receiveDate || info.slCollectApplication.expireDate).format('yyyy-MM-dd') }} {{ info.saveBankAccount }}   </li>
        <li v-else> {{ new Date(info.slCollectApplication.receiveDate || info.slCollectApplication.expireDate).format('yyyy-MM-dd') }} {{ info.slCollectApplication.receiveBankName }}   </li>
        <li> {{le}} {{ info.slCollectApplication.amount }}元</li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>用途/摘要</span>
          <span class="turnHang" >{{  info.purpose === info.summary ? info.purpose : info.purpose||'' + info.summary||''   }}</span>
        </li>
        <li><span>票据</span><span @click="showImgTip">共0张 <i class="fa fa-angle-right"></i></span></li>
      </ul>
      <div class="hr_gray"></div>

      <ul class="xiangUlB margin-10-15" v-if="info.slCollectApplication.method == 3 || info.slCollectApplication.method == 4">
        <li><span>收入类别</span><span>{{ info.slCollectApplication.method == 3 ? '转账支票' : '承兑汇票' }}存入</span></li>
        <li><span>票据金额</span><span>{{ info.slCollectApplication.amount }}</span></li>
        <li><span>{{ info.slCollectApplication.method == 3 ? '转账' : '汇票' }}到期日</span><span>{{ new Date(info.slCollectApplication.expireDate).format('yyyy-MM-dd') }}</span></li>
        <li><span>付款单位</span><span>{{ info.customerName }}</span></li>
        <li><span>{{ info.slCollectApplication.method == 3 ? '转账' : '汇票' }}号</span><span>{{ info.slCollectApplication.returnNo }}</span></li>
        <li @click="ticketInfo2"><span>{{ info.slCollectApplication.method == 3 ? '转账支票' : '承兑汇票' }}更多信息</span><span class="fa fa-angle-right ddw"> </span></li>
        <li><span>备注</span><span>{{ info.slCollectApplication.memo }}</span></li>
        <li class="endLi" style="line-height: 20px; margin-bottom: 40px">
          <div  style="width: 70%">
            <span>操作者</span>
            <span>{{ info.slCollectApplication.financerName }}</span>
            <span>{{ new Date(info.slCollectApplication.financerTime).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </div>
        </li>
      </ul>
      <ul class="xiangUlB margin-10-15" v-else>
        <li><span>收入类别</span><span>客户的回款</span></li>
        <li><span>收入方式</span><span>{{ chargePayMethod(info.slCollectApplication.method) }}</span></li>
        <li><span>付款单位</span><span>{{ info.customerName }}</span></li>
        <li class="endLi" style="line-height: 20px; margin-top: 40px">
          <div  style="width: 70%">
            <span>数据创建</span>
            <span>{{ info.slCollectApplication.createName }}</span>
            <span>{{ new Date(info.slCollectApplication.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </div>
        </li>
        <li class="endLi" style="line-height: 20px; margin-bottom: 40px">
          <div  style="width: 70%">
            <span>财务确认</span>
            <span>{{ info.slCollectApplication.financerName }}</span>
            <span>{{ new Date(info.slCollectApplication.financerTime).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </div>
        </li>
      </ul>

    </div>



  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "xiangQing",
  data() {
    return {
      currJobData: null,
      info: null,
      le: null,
      infoType: '',
    }
  },
  mixins: [finance],
  mounted() {
    let xiangQingItem = localStorage.getItem('xiangQingItem')
    if(xiangQingItem){
      xiangQingItem = JSON.parse(xiangQingItem)
      console.log('xiangQingItem=', xiangQingItem)
      this.le = xiangQingItem.le
      let info = xiangQingItem.info
      let type = info.type    //数据类型
      let source = info.source  //区分是借款的字段，为2则是借款数据,3=回款数据  6的是工资管理的
      let businessType = info.businessType //0101-常规借款借入,0102-常规借款还款，0103-常规借款借出，0104-常规借款收款
      var business = info.business      //借款id
      var accountDetailId = info.id      //
      var reimburseId = info.reimburseId   //报销id，也用来判断是否为报销数据
      var modityStatus= info.modityStatus  //判断修改是否被禁用

      if (type !== '1') {
        if (source && source === '2') {
          if(businessType == "0101" || businessType == "0103"){ // 常规借款详情
            this.seeOrdLoanDetail({ 'id' : business, sourceType:2 , accountDetailId: accountDetailId })

          }else if(businessType === "0102" || businessType === "0104"){
            // 常规借款 收付款 详情
            this.ordRepaymentDetailFun({ id: business })
          }else {
            // 如果是支出的话
            var param = { 'repaymentID' : business  }
          }

        }
        else if (source === '3') {
          this.seeCollectDetail (business)

        } else if (source === '6') {
          this.seeWageDetail(xiangQingItem.info)

        } else if (source === '8') {
          // 这是不允许查看的
        } else{
          if (reimburseId) {
            this.getReimburseDetail(reimburseId)

          } else {
            this.getDataDetailFun(xiangQingItem.id)
          }
        }
      }



    }else{
      this.$message.error('参数错误')
      this.$router.back()
    }

  },
  methods: {
    ticketInfo(){
      let path = `/ticketInfo/${ this.info.content.id }`
      this.$router.push({ path: path });
    },
    ticketInfo2(){
      let path = `/ticketInfo/${ this.info.slCollectApplication.returnId }`
      this.$router.push({ path: path });
    },
    goLoanPayDetail(){
      // localStorage.setItem('loanPayDetail', JSON.stringify(this.info))
      this.$router.push({ path:`/loanPayDetail/${ this.info.id }` })
    },
    ordRepaymentDetailFun(params){
      this.infoType = 'ordRepaymentDetail'
      this.financeApi.ordRepaymentDetail(params).then(res1=>{
        let res = res1.data.data
        console.log('ordRepaymentDetail=', res)
        this.info = res

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    getReimburseDetail(reimburseId){
      // 获取报销详情
      this.infoType = 'ReimburseDetail'
    },
    goEmployeeDetail(){
      localStorage.setItem('employeeSalary', JSON.stringify(this.info))
      this.$router.push({ path:`/employeeSalary` })
    },
    seeWageDetail(xiangQingItem){
      // 工资支付详情
      this.infoType = 'WageDetail'
      this.financeApi.getPayHistory({ businessHistory: xiangQingItem.businessHistory }).then(res1=>{
        let res = res1.data.data
        this.info = res


      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })


    },
    seeCollectDetail(business){
      // 查看回款详情
      this.infoType = 'CollectDetail'
      this.financeApi.getSlCollectDetailPC({ 'collectId': business }).then(res1=>{
        let res = res1.data
        console.log('res=',res)
        this.info = res

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    seeOrdLoanDetail(param){
      // 查看借款详情
      this.infoType = 'OrdLoanDetail'
      this.financeApi.ordLoanDetail(param).then(res1=>{
        let res = res1.data.data
        this.info = res

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })

    },
    goLoanDetail(){
      localStorage.setItem('loanDetail', JSON.stringify(this.info))
      this.$router.push("loanDetail")
    },
    formatSubType(content){
      let method = content.method
      let subType = content.subType
      let genre = content.genre
      let str = '其他'
      if(method === '6' || method === '7'|| method === '8'){
        str = '内部非支出性转账'
      }else if(method === '1' || method === '5'){
        if(subType){
          switch (Number(subType)) {
            case 1:  str = '本人的公务支出' ; break;
            case 2:  str = '其他同事的公务支出' ; break;
            case 3:  str = '需入库物品的报销' ; break;
            case 4:  str = '社保/公积金' ; break;
            case 5:  str = '税款' ; break;
            case 6:  str = '汇划费' ; break;
            case 7:  str = '其他财务费用' ; break;
            case 8:  str = '其他支出' ; break;
            default:
              str = ''
          }
        }else if(genre){
          switch (Number(genre)) {
            case 1:  str += '-货款' ; break;
            case 2:  str += '-借款' ; break;
            case 3:  str += '-投资款' ; break;
            case 4:  str += '-废品' ; break;
            case 5:  str += '其他 - '+ content.categoryDesc ; break;
            default:
              str = ''
          }
        }

      }
      else{
        switch (Number(subType)) {
          case 1:  str = '本人的公务支出' ; break;
          case 2:  str = '其他同事的公务支出' ; break;
          case 3:  str = '需入库物品的报销' ; break;
          case 4:  str = '社保/公积金' ; break;
          case 5:  str = '税款' ; break;
          case 6:  str = '汇划费' ; break;
          case 7:  str = '其他财务费用' ; break;
          case 8:  str = '其他支出' ; break;
          default:
            str = '其他财务费用'
        }
      }

      return str

    },
    showImgTip(){
      this.$message.info('没有票据')
    },
    showImg(){
      if(this.info.financeAccountBillImages && this.info.financeAccountBillImages.length >0){
        localStorage.setItem('picList', JSON.stringify(this.info.financeAccountBillImages))
        this.$router.push('picList')
      }else{
        this.$message.info('没有票据')
      }
    },
    getDataDetailFun(detailId){
      this.infoType = 'DataDetail'
      this.financeApi.getDataDetail({ detailId: detailId }).then(res1=>{
        let res = res1.data.data
        console.log('res getDataDetail =', res)
        this.info = res

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('err=',err)
      })
    },
    rou(index) {
      if (index == 14) {
        this.$router.push("/employeeSalary")
        this.$store.commit('saveCurrTitle', 1)
      } else if (index == 15) {
        this.$router.push("/employeeSalary")
        //通过Vuex的store提交一个名为saveCurrTitle的mutation，并传递数字作为参数。
        this.$store.commit('saveCurrTitle', 3)


      }

    }
  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
.xiangC1{
  padding: 20px;
  background-color: #eee;
  color: $tyColorRed;
  line-height: 30px;
}
.xiangC2{
  padding: 20px;
  background-color: #eee;
  color: $tyColorGreen;
  line-height: 30px;
}
.turnHang{
  display: inline-block;  white-space:normal; line-height: 20px;
  max-width: 60%;
}
.loan{
  .fa-angle-right{ font-size: 20px;  }
}
.fz20{ font-size: 20px; }
.ddw{ font-size: 20px; position: relative; top:5px;  }
</style>