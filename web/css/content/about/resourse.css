*{ font-family: "微软雅黑" ;   }
td,th{ text-align:center!important;  }
.clr{ clear:both;  }
.hd{ display:none;   }

select, input , textarea{ background: #fff; border:1px solid #ccc; min-height:35px;   }
option { background: #fff; min-height:35px;   }
/*右面*/
div.contn{ padding:10px;  }
div.conNav{ font-size:14px; font-weight:bolder; /*line-height:50px;*/border-bottom: 2px solid #36C6D3; }
div.scdNav>a{ font-size:14px; font-weight:bolder;  padding:5px; /*line-height:50px;*/ color:#999999;   }
div.Con{ color:#333; position:relative;    }
.page-content{ min-height:1000px; }
/*按钮*/
/*.aboutbtn,.opbtn { display:inline-block; padding:5px 10px; cursor:pointer; font-size:14px;   }
.aboutbtn:after{ display:block; content: " ";  }
.bigBtn{ padding:7px 30px;  font-weight:bolder; font-size:16px;  }
.azury, .gray, .blu, .orange{ color:#fff;  }
.azury{ background:#32C5D2;
    !*margin-left: 148px; *!
}
.azury0{ background:#32C5D2;
    !*margin-left: 148px; *!
    color: white;
}
.azury1{
    background:#e7505a;
    color: white;
    !*margin-left: 100px;*!
}
.azury2{
    background:#3598dc;
    color: white;
}
.azury3{
    background:#009dd9;
    color: white;
    margin-left: 5px;
}
.azury4{ background:#32C5D2;
    !*margin-left: 122px; *!
    color: white;
}
.azury5{ background:#32C5D2;
    color: white;

}
.azury:hover{ background:#26A1AB; }*/
.gray{ background:#999; }
.gray:hover{ background:#333; }
.blu{ background:#009DDA; }
.blu:hover{ background:#26A1AB; }
.orange{ background:#FD8A23;}
.orange:hover{ background:#26A1AB;     }
.hand{ cursor:pointer; }

/* 输入项 */
.opItem{
    padding:10px 0;
    font-size:16px;
}
.opItem2{
    font-size:16px;
    margin:10px 0;
}
.opItem3{
    font-size:16px;
}
.opItem1{
    color: #999999;
    font-size: 12px;
    font-weight: 400;

}
.opItem1>input{
}

.opItem_control{ text-align:center; margin-top:30px; margin-bottom:30px;       }
.itemTtl , .itemTtl1{  display:inline-block;  text-align:right; vertical-align:top;margin-right:15px;  }
.itemTtl{  font-size: 14px; min-width:100px; text-align: right; width:30%; }
.itemTtl1{ font-size: 24px; font-weight:400;    }
.xx{
    color: red;
}
.itemCon{ width:450px; display:inline-block; color:#333; font-size:14px;  }
.itemCon_textarea{ width:535px; margin-left:100px; height:200px;    }
.itemTip{ color:#666; font-size:12px;   }


/*Scan*/
.scanCon{  text-align:center; padding-top:100px;   }
.scanCon img:first-child{  display:inline-block; width:80px;    }     /*软件Logo*/
.scanCon span:nth-child(2){  display:block;  line-height: 50px; font-weight:bold;     }    /*软件版本号*/
.scanCon img:nth-child(3){ display:inline-block; width:200px;   }	/*软件二维码*/
.scanCon h3:nth-child(4){  margin-top:30px;    }	/*解释二维码*/

/*Opinion*/
.addOpinion{ margin: 15px 0; color:#fff;  }
.solve{ color:#009DDA; }
.unsolve{ color: #FF9900; }
.opinionDetails{ cursor:pointer;  }
.op_ttl{ font-size: 14px; display: inline-block;}
.op_date{  color:#666;  }
.op_question{ text-indent:2em; font-size:16px; color:#222;line-height:25px; }
.op_detl{ padding:20px 0 10px;  color:#333; }
.op_item_qs{ background:#ebebeb;   }
.op_item_as{  background:#fff;  }
.op_item_as, .op_item_qs{  position:relative; padding:10px 15px;    }

.op_identity{ width:100px; text-align:center; position: absolute; left: 0;     }
.op_iden_qs, .op_iden_as{ font-weight:bold; }
.op_iden_as{ color:#0099FC;   }
.op_iden_qs{ color:#FF9900;  }
.op_con{ margin-left:80px;  font-size:12px;  }
.op_time{  color:#ababab; }
.op_end{ border-top:2px solid #ccc; padding:30px 0 ; }

.thanks{ display: none; text-align:center; width:400px; margin:200px auto 0 ; background: #fff; z-index: 1; }
.thanks div:nth-child(1){ padding:50px; font-size:18px; }
.thanks div:nth-child(2){ padding:30px; font-size:16px; font-weight:bold; color:#00AEED; border-top:1px solid #ccc;    }

.op_con textarea{ display:block; border:1px solid #ccc; height:40px; width:100%;    }


/* version */
.versionCon{ font-size:12px; font-weight:normal; line-height:30px;  }
.vrIcon{ padding:3px 10px; font-size:10px;  position:relative; top:-5px;  }
.vrSpan{ margin-right:30px; display:inline-block; color:#bbb;  }
.vrContent{ margin-right:300px; border-right:1px solid #ccc; padding:50px 20px; min-height:700px;   }
.vr_history{ position:absolute; right:0; top:0; width:240px; padding:30px;       }
.vr_history_item{ height:50px; vertical-align:middle; font-size:16px; cursor:pointer;  }

/*service*/
.textContent{  padding:50px 20px; min-height:700px;   }


/* help */
.searchNav{ padding:100px 0 50px 0;  }
.searchDiv{    max-width:800px; min-width:400px; width:70%; position:relative; margin:0 auto;      }
.search_inout{ border:1px solid #32C5D2!important; line-height:40px; height:40px!important; display: inline-block;  width:100%;    }
.searchBtn{ position:absolute; right:-13px; top:0; font-size:18px; cursor:pointer; color:#fff; font-weight:bolder;line-height:40px; height:40px; padding:5px 25px;  }
.searchCon{ padding:0 50px;              }
.Q_div{ width:33%; float:left;   }
.Q_div .Q_ttl, .Q_div>div{ margin-left:80px;    }
.Q_ttl{ color:#009DDA; font-size:20px; font-weight:bolder;    }
.Q_item{ padding:15px 0; font-size:16px; cursor:pointer;     }
.Q_item:hover{ text-decoration:underline;     }
.opinion_ttl{ display:block;  }
.opinion_con{ display:block; height:150px; }
.opinion_submit{ font-size:16px; padding:5px 10px; cursor:pointer;     }

.resultDiv{ background:#E4E4E4; padding:20px 0;      }
.goback{ width:50px; display:inline-block; margin-left:30px; position:relative; top:-5px; cursor:pointer;             }
.research_input{ border:1px solid #ccc!important; width:600px; height:30px!important;          }
.researchBtn{ position:relative; left:-56px; top:-3px; font-size:16px; height:30px; display:inline-block; padding:4px 10px; line-height:30px;           }

.resultCon{ padding:20px 0 100px 30px;          }
.resultKey{ padding:15px 0; font-size:14px;               }
.resultKey>span{ font-weight:bolder;               }
.answerItem{ margin-top:30px;font-size:16px;        }
.as_ttl{ font-weight: bolder; color:#32C5D2; margin-bottom:10px;   }

/*feedback*/
.manageNav{font-size:16px; font-weight:bolder; padding:5px; line-height:50px; color:#333;    }


/* scanManage */
.det_img{ max-height:100px;  }
.upload{ margin-left:100px;  }
#upload,#uploadFou{text-align:center;}

/* privacy */
.resultKey>label{ display:inline-block; }

/* helpManage */
.upcon_help{ display:none; }
.helpBtn_update{ float:right;  margin-right:20px;  margin-top:15px;    }
.saveHelpBtn{ margin-left:100px;   }
.cconon{
    height: 40px;
}
.conon{
    padding: 5px 10px;
    display: inline-block;
    /*float: left; */
    /*text-align:left;*/
}

.conon0{
    display: inline-block;

}
.conon1{

    display: inline-block;


}
.conon2{
    display: inline-block;
}

.conon3{

    display: inline-block;


}
.conon4{
    /*height: 30px;*/
    display: inline-block;
    /*margin-left: 874px;*/
    position: relative;left: 827px;

}

.conon>a,.concon>a:hover{
    color: #909090;
    font-family: "方正兰亭超细黑简体";
    font-weight: 700;
}
.resourceAddBtn{ position: relative; top:-8px;   }
.conon0>p{
    display: inline-block;


}
.conon00{
    display: inline-block;
}

.conon>span{

    margin-bottom:10px;
}
.conon>font{
    /*margin-left: 1200px;*/
    position: relative;left: 1143px;

}
.conon>h5{
    /*margin-left: 1100px;*/
    display: inline-block;
    position: relative;left: 974px;
}
.conon>p,.conon>p.a:hover{
    display: inline-block;
    padding: 0 5px;
    font-weight: 700;
}

.Con>p{
    font-size: 24px;
    font-weight: 400;
    margin-top: 20px;
}

td.tz{
    text-align:left!important;
    padding: 10px 30px;
}
.tz>a{
    display: block;
    color: black;
    text-decoration: none;
}
.tz1>a{
    display: block;
    color: black;
    text-decoration: none;
}
.tt{
    color: black!important;
    font-weight: lighter !important;
}
.ch{
    margin-left: 966px!important;
}
.conon>a.ls{
    padding:7px 12px;
    cursor:pointer;
    font-size:14px;
    background:#3598dc;
    color: white !important;
    font-weight: lighter!important;
    text-decoration: none;
    margin-left: 5px;
}

.shu{
    background-color:#e4e4e4 !important;
}
.shch{
    font-weight: bold;
    font-size: 13px;
    width:100px;
    text-align: right;
    display: inline-block;
}
.rq{
    margin-left: 10px;
    font-size: 13px;
    text-overflow:ellipsis;
    white-space:nowrap;
    display: inline;
    width:200px;
}

.docScanItem>.rq{
    margin-left: 10px;
    font-size: 13px;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
    display: inline-block;
    width:200px;
    position: relative;
    top: 10px;
}
.zuo{
    text-align: right;
    display: inline-block;


}
.you{
    text-align: left;
    display: inline-block;
}
.chang{
    text-align: center;
    padding: 25px;

}
.shch1{
    line-height: 32px;
}

.nav{
    margin-left: 42px;
    font-size: 13px!important;
    font-weight: 400;

}

.container_nav{
    text-align: right;
}

.conon0>a:first-child{ display:inline-block; margin-right:30px; color:black; text-decoration: none; }
.conon1>a:first-child{display:inline-block; margin-right:30px; color:black;text-decoration: none;}
.conon2>a:first-child{display:inline-block; margin-right:160px; color:black;text-decoration: none;}
.conon3>a:last-child{display:inline-block; margin-right:160px; color:black;text-decoration: none;}
.conon1>a:nth-child(2):hover{
    color:white;
    text-decoration: none;
}
.conon>a:nth-child(2),.conon>a:nth-child(2):hover{
    color:black;
    font-weight: lighter;
    text-decoration: none;
}


/*wenjian*/
.center{ text-align:center; padding-bottom:20px; }


/*文件名一行显示*/
.up_filename{
    display:block;
}





















