package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpTmplHistoryDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpTmplHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpTmplHistoryDaoImpl extends BaseDao<TpTmplHistory, Serializable> implements TpTmplHistoryDao {
}
