<!-- 模板管理/模板修改 详情页 -->
<template>
  <div id="moduleDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container">
      <div class="panel">
        <h4 class="moduleName">{{info.name}}</h4>
      </div>
      <div class="panel">
        <table class="ui-table moduleTable">
          <thead>
          <tr>
            <td class="thisLevel">一级菜单</td>
            <td class="nextLevel">
              <table class="ui-table">
                <thead>
                <tr>
                  <td class="thisLevel">二级菜单</td>
                  <td class="nextLevel">
                    <table class="ui-table">
                      <thead>
                      <tr>
                        <td class="thisLevel">三级菜单</td>
                      </tr>
                      </thead>
                    </table>
                  </td>
                </tr>
                </thead>
              </table>
            </td>
          </tr>
          </thead>
          <tbody v-html="treeStr"></tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<style>
/* 模块表格特殊处理 - 开始 */
.moduleTable td.thisLevel{
  width: 130px;
}
.moduleTable td.nextLevel{
  padding: 0;
  border: none;
}
.moduleTable td.nextLevel>.ui-table{
  border: none;
}
/* 模块表格特殊处理  - 结束 */
.btn-group{
  text-align: right;
  flex: auto;
}
</style>
<script>
export default {
  name: 'moduleDetail',
  data () {
    return {
      loading: true,
      title: '模块查看',
      info: {
        name: ''
      },
      treeStr: ''
    }
  },
  created: function () {
    let item = JSON.parse(localStorage.getItem('moduleInfo'))
    this.info = item
    this.getModuleDetail(item.id)
  },
  methods: {
    getModuleDetail: function (moduleId) {
      let that = this
      this.axios.post('../../../thali/getModuleInfo.do', { id: moduleId })
        .then(res => {
          that.loading = false
          let data = res.data.data
          if (data && data.length > 0) {
            that.loading = false
            // 一层模块数据 -> 树型数据，将对应的子级放在subPopdoms中
            let newData = that.changeDataToTree(data)
            // 将处理后的数据渲染表格
            let tbodyStr = that.renderModuleToTable(newData, 1)
            that.treeStr = tbodyStr
          }
        })
    },
    changeDataToTree: function(originData) {
      let newData = []
      for (let item of originData) {
        for (let it of originData) {
          if (it.pid === item.mid) {
            item.subPopdoms.push(it)
          }
        }
        if (item.pid === '0') {
          newData.push(item)
        }
      }
      return newData
    },
    renderModuleToTable: function(originData, level) {
      // type : 1 勾选 2 查看
      let listStr = ''
      for (let item of originData) {
        let nextData = item.subPopdoms
        let NextStr = level < 3 ? this.renderModuleToTable(nextData, level+1): ''
        listStr +=  '<tr>' +
          '   <td class="thisLevel">' + item.name +
          (level < 3? '   <td class="nextLevel">' +
          '       <table class="ui-table">'+
          '           <tbody>'+ NextStr + '</tbody>'+
          '       </table>'+
          '   </td>':'') +
          '</tr>'
      }
      if (originData.length > 0) {
        return listStr
      } else {
        // 占位
        return '<tr><td><span style="visibility: hidden">--</span></td></tr>'
      }
    }
  }
}
</script>
