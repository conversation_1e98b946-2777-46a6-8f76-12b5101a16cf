<template>
	<div class="live-table">
		<div class="live-head">
			<div :style="{flex:(item.flex || 1) }" class="live-td" v-for="(item, index) in columns" :key="index" v-html="item.title">
			</div>
		</div>
		<div class="live-body">
			<div class="live-tr" v-for="(item2, index2) in list" :key="index2"
           @click="longPress(item2,index2)"
           @touchstart="gtouchstart(item2,index2)"
           @touchmove="gtouchmove()"
           @touchend="showDeleteButton(item2, index2)">
				<div :style="{flex:(item3.flex || 1) }" class="live-td" v-for="(item3, index3) in columns" :key="index3">
				 {{ item2[item3.key] }}
				</div>
			</div>
		</div>
	</div>
</template>
<script>
	export default {
		name:"LiveTable",
		props: {
			columns: {
				type: Array,
				default: [{
					title: "ID",
					key: "id"
				}]
			},
			list: {
				type: Array,
				default: []
			}, 
		},
		data() {
			return {
				time:"",
				timeOutEvent: null,
			};
		},
    mounted(){
		  console.log('进来了', this.columns)
		  console.log('进来了', this.list)
    },
		methods:{
      gtouchstart(item, index) { //长按事件（起始）
        console.log('长按事件（起始）')
        var self = this;
        this.timeOutEvent = setTimeout(function () {
          self.longPress(item, index);
        }, 500); //这里设置定时器，定义长按500毫秒触发长按事件
        return false;
      },
      showDeleteButton(item, index) { //手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
        console.log('手释放）')
        clearTimeout(this.timeOutEvent); //清除定时器
        if (this.timeOutEvent != 0) {
          //这里写要执行的内容（如onclick事件）
          console.log("点击但未长按");
        }
        return false;
      },
      gtouchmove() { // 如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
        console.log('如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按')
        clearTimeout(this.timeOutEvent); //清除定时器
        this.timeOutEvent = 0;
      },
      longPress(item, index) { //真正长按后应该执行的内容
        this.timeOutEvent = 0;
        console.log("长按");
        this.$emit('longPress',item, index)
      },

		}

	}
</script>

<style lang="scss" scoped>
	.live-table{
		.live-head{
			background-color: #dade76;
		}
		.live-head, .live-tr{
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			.live-td{
				flex: 1;
				 //overflow: hidden;
				 //white-space: nowrap;
				 //text-overflow: ellipsis;
				text-align: center;
				line-height: 40px;
				padding:5px 0;
				font-size: 0.8em;
			}
		}
		.live-body{
			background-color: #fff;
			.live-tr:nth-child(odd){
				background-color: #f8f8f8;
			}
		}
	}
	
</style>
