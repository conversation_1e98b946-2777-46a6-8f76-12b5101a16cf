package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectHistory;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectPeroid;
import cn.sphd.miners.modules.accountant.service.AccountBookManagerService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;

/**
 * Created by root on 17-5-8.
 */
@Controller
@RequestMapping("/accountant")
public class AccountBookManagerController {

    private static final String SUCCESS = "1";
    private static final String ERROR = "0";

    @Autowired
    AccountBookManagerService accBookService;

    @ResponseBody
    @RequestMapping("/getAllSubjecs.do")
    public void getAllSubjects(User user, HttpServletResponse response)
    {
        JSONObject result = new JSONObject();
//        User user = (User) session.getAttribute("user");
        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        List<TAccountantSubjectHistory> listSubjects = accBookService.getAllSubjects(org);

    //    return new Result("1","",list);
        result.put("code", 1);
        result.put("msg", "");
        result.put("list",listSubjects);
        WriteJSONUtil.writeJSON(result.toString(), response);
    }

    /*得到某科目的总帐
    * flag 0 总帐
    * flag 1 明细帐
    * subject 页面上选择的科目
    * queryTime 查询时间
    * */
    @ResponseBody
    @RequestMapping("/getSubjectLedger.do")
    public void getSubjectLedger(User user, String subject, int flag, String queryTime, HttpServletResponse response) throws ParseException {
        JSONObject result = new JSONObject();
//        User user = (User) session.getAttribute("user");
        int oid = user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        TAccountantSubjectHistory subjectEntity = new TAccountantSubjectHistory();
        subjectEntity.setOrg(oid);
        subjectEntity.setSubject(subject);
        List<TAccountantSubjectPeroid> list = accBookService.getSubjectLedger(subjectEntity,flag,queryTime);
        //return new Result(SUCCESS,"",list);
        result.put("code", 1);
        result.put("msg", "");
        result.put("data", list);
        WriteJSONUtil.writeJSON(result.toString(), response);
    }


}
