package cn.sphd.miners.modules.material.entity;

import cn.sphd.miners.modules.sales.model.PoDeliveryAddress;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2016/9/19.
 */
@Entity(name = "MtSupplierMaterial")
@Table(name = "t_mt_supplier_material")
public class MtSupplierMaterial implements Serializable {


    @Id
    @Column(name = "id")
    //@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    //1.234；供应商mci
    @Column(name = "last_date")
    private Date LastDate;//	datetime
    @Column(name = "last_quantity")
    private BigDecimal LastQuantity;//	decimal
    @Column(name = "last_unit_price")
    private BigDecimal LastUnitPrice;//	decimal
    @Column(name = "current_year_num")
    private Integer CurrentYearNum;//	int	11
    @Column(name = "current_year_quantity")
    private BigDecimal CurrentYearQuantity;//	deci
    @Column(name = "current_year_unit_price")
    private BigDecimal CurrentYearUnitPrice;//
    @Column(name = "total_num")
    private Integer TotalNum;//	int	11
    @Column(name = "total_quantity")
    private BigDecimal TotalQuantity;//	deci
    @Column(name = "total_unit_price")
    private BigDecimal TotalUnitPrice;
    @Column(name = "begin_date", nullable = true, unique = false)
    private Date beginDate;

    /**
     * 最低采购量
     */
    @Column(name = "minimum_purchase", nullable = true, unique = false)
    private Integer minimumPurchase;

    @Column(name = "perchase_cycle", nullable = true, unique = false)
    private Integer perchaseCycle;

    @Column(name = "unit_price", nullable = true, unique = false)
    private BigDecimal unitPrice;

    @Column(name = "tax_rate", nullable = true, unique = false)
    private BigDecimal taxRate;

    @Column(name = "unit_price_notax", nullable = true, unique = false)
    private BigDecimal unitPriceNotax;

    @Column(name = "contract_sn", length = 100, nullable = true, unique = false)
    private String contractSn;

    @Column(name = "memo", length = 255, nullable = true, unique = false)
    private String memo;

    @Column(name = "creator", nullable = true, unique = false)
    private Integer creator;

    @Column(name = "create_name", length = 100, nullable = true, unique = false)
    private String createName;

    @Column(name = "create_date", nullable = true, unique = false)
    private Date createDate;

    @Column(name = "updator", nullable = true, unique = false)
    private Integer updator;

    @Column(name = "update_name", length = 100, nullable = true, unique = false)
    private String updateName;


    @Column(name = "update_date", nullable = true, unique = false)
    private Date updateDate;

    @Column(name = "delete_type", nullable = true, unique = false)
    private Integer deleteType;//删除状态，1-可以删，0-不可删

    @Column(name = "minimum_stock", length = 100, nullable = true, unique = false)
    private BigDecimal minimumStock;    //'最低库存',
    @Column(name = "initial_stock", length = 100, nullable = true, unique = false)
    private BigDecimal initialStock;    //'初始库存',


    //物料与会计之关联
    @Column(name = "price_stable", length = 100, nullable = true, unique = false)
    private String priceStable;//'价格是否稳定:1-相对稳定,2-变动频繁',
    @Column(name = "draft_acceptable", length = 100, nullable = true, unique = false)
    private Integer draftAcceptable;//'可否接受汇票:true-接受,false-不接受',
    @Column(name = "code_name")
    private String codeName;
    @Column(name = "charge_acceptable")
    private Integer chargeAcceptable;    //boolean comment '是否接受挂帐',
    @Column(name = "charge_begin", length = 100, nullable = true, unique = false)
    private String chargeBegin;         //char(1) comment '挂帐开始类型:1-自入库之日起,2-自发票提交之日起 ',
    @Column(name = "charge_period", length = 100, nullable = true, unique = false)
    private Integer chargePeriod;        //int comment '帐期(天)',
    @Column(name = "is_include", length = 100, nullable = true, unique = false)
    private String isInclude;           //boolean comment '合同是否包含本物料:true-包含,false—不包含',
    @Column(name = "material_invoiceable", length = 100, nullable = true, unique = false)
    private int materialInvoicable;  //boolean comment '本物料可否开具发票::true-可开具,false-不能开',
    @Column(name = "material_invoice_category", length = 100, nullable = true, unique = false)
    private String materialInvoiceCategory; //char(1) comment '本物料发票类型:1-增值税专票,2-普通发票',
    @Column(name = "material_tax_rate", length = 100, nullable = true, unique = false)
    private BigDecimal materialTaxRate;    //decimal(10,5) comment '本物料税率',
    @Column(name = "is_imprest", length = 100, nullable = true, unique = false)
    private String isImprest;           //b是否需要预付款:0-不确定,1-需要,2-不需要',
    @Column(name = "inclusive_freight")
    private int inclusiveFreight;
    //包装方式 包装方式:1-基本固定,2-型式不定
    @Column(name = "package_method", length = 1, nullable = true, unique = false)
    private String packageMethod;

    @Column(name = "has_contact", length = 100, nullable = true, unique = false)
    private String hasContact;  //'是否有合同;true-有,false-无',
    @Column(name = "valid_date", length = 100, nullable = true, unique = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validDate;//'有效期至',
    @Column(name = "sign_date", length = 100, nullable = true, unique = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date signDate;//'签署日期',
    @Column(name = "invoice_able", length = 100, nullable = true, unique = false)
    private String invoicable;  //'可否开具发票::true-可开具,false-不能开',
    @Column(name = "invoice_category", length = 100, nullable = true, unique = false)
    private String invoiceCategory;//'发票类型:1-增值税专票,2-普通发票',
    @Column(name = "is_tax", length = 100, nullable = true, unique = false)
    private Integer isTax;
    @Column(name = "at_par", length = 100, nullable = true, unique = false)
    private String atPar;
    @Column(name = "pay_method", length = 100, nullable = true, unique = false)
    private String payMethod;

    //1.239
    @Column(name = "delivery_address")
    private String deliveryAddress;

    //1.266
    @Column(name = "expiration_days")
    private Integer expirationDays;//保质期天数
    @Transient
    private List<PoDeliveryAddress> addresses;

    @Column(name = "supplier_invoice")
    private Integer supplierInvoice;


    @Transient
    private String fullName;

    @Column(name = "is_par_value", nullable = true, unique = false)
    private String isParValue;//是否为开票价格

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material", referencedColumnName = "id")
    private MtBase material;

    @Column(name = "material", nullable = false, unique = true, insertable = false, updatable = false)
    private Integer material_;

    @Column(name = "supplier")
    private Integer supplier;


    //1.95

    @Column(name = "enabled")
    private String enabled;

    @Column(name = "enabled_time")
    private Date enabledTime;

    @Column(name = "is_appointed")
    private String isAppointed;

    @Column(name = "appointed_time")
    private Date appointedTime;

    @Column(name = "appointed_user")
    private String appointedUser;

    @Column(name = "instance")
    private Integer instance;
    @Column(name = "instance_chain")
    private String instanceChain;
    @Column(name = "approve_status")
    private String approveStatus;
    @Column(name = "reject_reasion_desc")
    private String rejectReasionDesc;

    @Column(name = "operation")
    private String operation;

    @Transient
    private String name;

//    public Integer getId() {
//        return id;
//    }
//
//    public void setId(Integer id) {
//        this.id = id;
//    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getLastDate() {
        return LastDate;
    }

    public void setLastDate(Date lastDate) {
        LastDate = lastDate;
    }

    public BigDecimal getLastQuantity() {
        return LastQuantity;
    }

    public void setLastQuantity(BigDecimal lastQuantity) {
        LastQuantity = lastQuantity;
    }

    public Integer getExpirationDays() {
        return expirationDays;
    }

    public void setExpirationDays(Integer expirationDays) {
        this.expirationDays = expirationDays;
    }

    public BigDecimal getLastUnitPrice() {
        return LastUnitPrice;
    }

    public void setLastUnitPrice(BigDecimal lastUnitPrice) {
        LastUnitPrice = lastUnitPrice;
    }

    public Integer getCurrentYearNum() {
        return CurrentYearNum;
    }

    public void setCurrentYearNum(Integer currentYearNum) {
        CurrentYearNum = currentYearNum;
    }

    public BigDecimal getCurrentYearQuantity() {
        return CurrentYearQuantity;
    }

    public void setCurrentYearQuantity(BigDecimal currentYearQuantity) {
        CurrentYearQuantity = currentYearQuantity;
    }

    public BigDecimal getCurrentYearUnitPrice() {
        return CurrentYearUnitPrice;
    }

    public void setCurrentYearUnitPrice(BigDecimal currentYearUnitPrice) {
        CurrentYearUnitPrice = currentYearUnitPrice;
    }

    public Integer getTotalNum() {
        return TotalNum;
    }

    public void setTotalNum(Integer totalNum) {
        TotalNum = totalNum;
    }

    public BigDecimal getTotalQuantity() {
        return TotalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        TotalQuantity = totalQuantity;
    }

    public BigDecimal getTotalUnitPrice() {
        return TotalUnitPrice;
    }

    public void setTotalUnitPrice(BigDecimal totalUnitPrice) {
        TotalUnitPrice = totalUnitPrice;
    }

    public Integer getMinimumPurchase() {
        return minimumPurchase;
    }

    public void setMinimumPurchase(Integer minimumPurchase) {
        this.minimumPurchase = minimumPurchase;
    }

    public Integer getPerchaseCycle() {
        return perchaseCycle;
    }

    public void setPerchaseCycle(Integer perchaseCycle) {
        this.perchaseCycle = perchaseCycle;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getIsAppointed() {
        return isAppointed;
    }

    public void setIsAppointed(String isAppointed) {
        this.isAppointed = isAppointed;
    }

    public Date getAppointedTime() {
        return appointedTime;
    }

    public void setAppointedTime(Date appointedTime) {
        this.appointedTime = appointedTime;
    }

    public String getAppointedUser() {
        return appointedUser;
    }

    public void setAppointedUser(String appointedUser) {
        this.appointedUser = appointedUser;
    }

    public BigDecimal getUnitPriceNotax() {
        return unitPriceNotax;
    }

    public void setUnitPriceNotax(BigDecimal unitPriceNotax) {
        this.unitPriceNotax = unitPriceNotax;
    }

    public String getContractSn() {
        return contractSn;
    }

    public void setContractSn(String contractSn) {
        this.contractSn = contractSn;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public MtBase getMaterial() {
        return material;
    }

    public void setMaterial(MtBase material) {
        this.material = material;
    }

    public Integer getMaterial_() {
        return material_;
    }

    public void setMaterial_(Integer material_) {
        this.material_ = material_;
    }

    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public Integer getDeleteType() {
        return deleteType;
    }

    public void setDeleteType(Integer deleteType) {
        this.deleteType = deleteType;
    }

    public String getName() {
        return name;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPriceStable() {
        return priceStable;
    }

    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public void setPriceStable(String priceStable) {
        this.priceStable = priceStable;
    }

    public Integer getDraftAcceptable() {
        return draftAcceptable;
    }

    public void setDraftAcceptable(Integer draftAcceptable) {
        this.draftAcceptable = draftAcceptable;
    }

    public String getHasContact() {
        return hasContact;
    }

    public void setHasContact(String hasContact) {
        this.hasContact = hasContact;
    }

    public Date getValidDate() {
        return validDate;
    }

    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public String getInvoicable() {
        return invoicable;
    }

    public void setInvoicable(String invoicable) {
        this.invoicable = invoicable;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public Integer getIsTax() {
        return isTax;
    }

    public void setIsTax(Integer isTax) {
        this.isTax = isTax;
    }

    public String getAtPar() {
        return atPar;
    }

    public void setAtPar(String atPar) {
        this.atPar = atPar;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getChargeAcceptable() {
        return chargeAcceptable;
    }

    public void setChargeAcceptable(Integer chargeAcceptable) {
        this.chargeAcceptable = chargeAcceptable;
    }

    public String getChargeBegin() {
        return chargeBegin;
    }

    public void setChargeBegin(String chargeBegin) {
        this.chargeBegin = chargeBegin;
    }

    public Integer getChargePeriod() {
        return chargePeriod;
    }

    public void setChargePeriod(Integer chargePeriod) {
        this.chargePeriod = chargePeriod;
    }

    public String getIsInclude() {
        return isInclude;
    }

    public List<PoDeliveryAddress> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<PoDeliveryAddress> addresses) {
        this.addresses = addresses;
    }

    public void setIsInclude(String isInclude) {
        this.isInclude = isInclude;
    }

    public int getMaterialInvoicable() {
        return materialInvoicable;
    }

    public void setMaterialInvoicable(int materialInvoicable) {
        this.materialInvoicable = materialInvoicable;
    }

    public String getMaterialInvoiceCategory() {
        return materialInvoiceCategory;
    }

    public void setMaterialInvoiceCategory(String materialInvoiceCategory) {
        this.materialInvoiceCategory = materialInvoiceCategory;
    }

    public BigDecimal getMaterialTaxRate() {
        return materialTaxRate;
    }

    public void setMaterialTaxRate(BigDecimal materialTaxRate) {
        this.materialTaxRate = materialTaxRate;
    }

    public String getIsImprest() {
        return isImprest;
    }

    public void setIsImprest(String isImprest) {
        this.isImprest = isImprest;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public int getInclusiveFreight() {
        return inclusiveFreight;
    }


    public void setInclusiveFreight(int inclusiveFreight) {
        this.inclusiveFreight = inclusiveFreight;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPackageMethod() {
        return packageMethod;
    }

    public void setPackageMethod(String packageMethod) {
        this.packageMethod = packageMethod;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getIsParValue() {
        return isParValue;
    }

    public void setIsParValue(String isParValue) {
        this.isParValue = isParValue;
    }

    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getRejectReasionDesc() {
        return rejectReasionDesc;
    }

    public void setRejectReasionDesc(String rejectReasionDesc) {
        this.rejectReasionDesc = rejectReasionDesc;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getSupplierInvoice() {
        return supplierInvoice;
    }

    public void setSupplierInvoice(Integer supplierInvoice) {
        this.supplierInvoice = supplierInvoice;
    }
}
