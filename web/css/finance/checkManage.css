@charset "utf-8";

*{ font-family: "Microsoft Yahei" ;   }

.bonceContainer .table td{
	font-size: 13px;
}

.contn{
	margin-left: 2px;
}

.conNav{
	margin-left: 2px;
	font-size: 18px;
	font-weight: 700;
	margin-top: -2px;
}

.Con-pwd{
	margin: 30px 0 5px 22px;
}

.tx1,.tx2{
	cursor:pointer;
	float: left;
	font-size: 13px;
	font-weight: 700;
	color: black;
	height: 28px;

}
.txn{
	border-bottom: 4px solid #35c6d3;
}





.tx1:hover,.tx2:hover{
	border-bottom: 4px solid #a7e6ec;
}
.tx2{
	margin-left: 39px;
}
.tx3{
	float: right;
	font-size: 13px;
	background-color: #36C6D3;
	width: 72px;
	height: 30px;
	text-align: center;
	line-height: 32px;
	color: white;
	cursor:pointer;
}
.tx3:hover{
	background-color: #26A1AB;
}




/*1111111111111111111111111111111111111111111111111111111111111111111111111*/
.Border-big{
	margin-right: 3.5%;
}



.table-bordered{
	text-align: center;
}

.bg{
	background-color: #F2F2F2;
}
/*支票录入框*/
.bigBank{
	margin-top: 0;
}
.bonceContainer a{
	color: black;
}
.bonceContainer a:hover	{
	text-decoration:none;
}
.bonceFoot{
	text-align: right;
	padding-right: 30px;
}

.bank{
	height: 34px;
	width: 369px;
}
.addpayDetails{
	margin: 0 30px 0 0;
}
.addpayDetails p{
	text-align: right;
}

/*.bonceCon*/
.bonceCon input{
	height:34px; width: 369px; 
}
.bonceFoot{
	text-align: right;
}

/*点击起止号码后的框*/
 
.cash1{
	margin-right: 0;
}


.btnbd{
	    border-color: grey;
	    width: 88px;
	    height: 36px;
}

/*作废页面*/
#invalid{
	font-size: 16px;
	position: absolute;
	width: 510px;
	left: 50%;
	margin-left: -255px;
	top: 10%;
	
}
#invalid>.bonceHead{
	font-size: 16px;
}

#invalid .addpayDetails{
	margin: 0;
}
#mt_invalid{
	display: inline-block;
}

.bounce_close{
	cursor: pointer;
}
.bounce_close1{ width:25px; height:25px; background: url("../../assets/bounce/img/close.png") no-repeat center;
	display:inline-block;float:right;  }
#invalid .shu1{
	display: inline-block;
	width: 411px;
}
.bonceFoot{
	padding: 10px 25px 10px 15px;
}
#mt_invalid{
	margin: 0;
}
#mt_invalid1{
	margin: 10px 0 0 0;
}
.btnColor{
	background-color: #990000;
	color: white;
}
.btnColor:hover{
	color: #333;
}
.add_cash span{
	display: inline-block;
	width: 46px;
	height: 22px;
	background-color: #E4E4E4;
	line-height: 22px;
}

.add_cash .cash2{
	background-color: #36C6D3;
}

.add_cash .cash2:hover{
	background-color: #26A1AB;
}



