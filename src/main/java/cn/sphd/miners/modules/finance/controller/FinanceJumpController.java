package cn.sphd.miners.modules.finance.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Created by Administrator on 2016/12/28.
 */
@Controller
@RequestMapping("/financeJump")
public class FinanceJumpController {

    //支票管理
    @RequestMapping("/checkManage.do")
    public String checkManage(){
        return "/finance/checkManage";
    }

    //数据录入
    @RequestMapping("/dataImport.do")
    public String dataImport(){
        return "/finance/dataImport";
    }

    //报销受理-报销受理
    @RequestMapping("/expenseReceive.do")
    public String expenseReceive(){
        return "/finance/expenseReceive";
    }

    //报销受理-类别设置
    @RequestMapping("/expenseReceiveCateroy.do")
    public String expenseReceiveCateroy(){
        return "/finance/expenseReceiveCateroy";
    }

    //回款票据
    @RequestMapping("/replyBill.do")
    public String replyBill(){
        return "/finance/replyBill";
    }

}
