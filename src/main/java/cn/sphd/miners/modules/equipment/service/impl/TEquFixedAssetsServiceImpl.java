package cn.sphd.miners.modules.equipment.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import cn.sphd.miners.modules.equipment.entity.TEquFixedAssets;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.mapper.TEquFixedAssetsMapper;
import cn.sphd.miners.modules.equipment.service.ITEquEquipmentService;
import cn.sphd.miners.modules.equipment.service.ITEquFixedAssetsService;
import cn.sphd.miners.modules.equipment.service.ITEquModelService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
@Transactional
public class TEquFixedAssetsServiceImpl implements ITEquFixedAssetsService
{
    @Autowired
    private TEquFixedAssetsMapper tEquFixedAssetsMapper;

    @Autowired
    ITEquEquipmentService equEquipmentService;

    @Autowired
    ITEquModelService equModelService;
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TEquFixedAssets selectTEquFixedAssetsById(Long id)
    {
        return tEquFixedAssetsMapper.selectTEquFixedAssetsById(id);
    }

    /**
     * 查询列表
     * 
     * @param tEquFixedAssets 
     * @return 
     */
    @Override
    public List<TEquFixedAssets> selectTEquFixedAssetsList(TEquFixedAssets tEquFixedAssets)
    {
        return tEquFixedAssetsMapper.selectTEquFixedAssetsList(tEquFixedAssets);
    }

    /**
     * 新增
     * 
     * @param tEquFixedAssets 
     * @return 结果
     */
    @Override
    public int insertTEquFixedAssets(TEquFixedAssets tEquFixedAssets)
    {
        return tEquFixedAssetsMapper.insertTEquFixedAssets(tEquFixedAssets);
    }

    /**
     * 修改
     * 
     * @param tEquFixedAssets 
     * @return 结果
     */
    @Override
    public int updateTEquFixedAssets(TEquFixedAssets tEquFixedAssets)
    {
        return tEquFixedAssetsMapper.updateTEquFixedAssets(tEquFixedAssets);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTEquFixedAssetsByIds(String ids)
    {
        return tEquFixedAssetsMapper.deleteTEquFixedAssetsByIds(ids.split(","));
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTEquFixedAssetsById(Long id)
    {
        return tEquFixedAssetsMapper.deleteTEquFixedAssetsById(id);
    }

    @Override
    public boolean generateEquipmentTools(List<String> subjectNames, User user) {
        if (subjectNames != null) {
            for (String subjectName : subjectNames) {
                if(!subjectName.isBlank()){
                    //先新增为装备名称
                    TEquEquipment equipment = new TEquEquipment();
                    equipment.setOrg(Long.valueOf(user.getOid()));
                    equipment.setCreateDate(new Date());
                    equipment.setCreator(Long.valueOf(user.getUserID()));
                    equipment.setCreateName(user.getUserName());
                    equipment.setFullName(subjectName);
                    if(subjectName.length()>6){
                        equipment.setName(subjectName.substring(0,6));
                    }else{
                        equipment.setName(subjectName);
                    }
                    int id = equEquipmentService.insertTEquEquipment(equipment);

                    //再将此装备名称新增为单独的待处理的装备
                    TEquModel equModel = new TEquModel();

                    equModel.setEquipment(equipment.getId());
                    equModel.setCategory(-1L);
                    equModelService.batchModelAdd(Collections.singletonList(equModel), user);
                }

            }

            return true;
        }


        return false;
    }
}
