package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.PdBaseDao;
import cn.sphd.miners.modules.commodity.dao.PdCommodityMediaDao;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseDao;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdCommodityMedia;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseHistory;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/10/12.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ProductServiceImpl implements ProductService{
    @Autowired
    PdMerchandiseDao pdMerchandiseDao;
    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    PdMerchandiseHistoryDao pcpdDao;
    @Autowired
    private PdCommodityMediaDao pdCommodityMediaDao;

    @Override
    public List<PdMerchandise> getByNameInnerSn(Integer oid,String customerName, String innerSn,String outerSn) {
        String condition = " and o.product.oid="+oid;
        if (!"".equals(customerName)&&customerName!=null){
            condition+= " and o.customerName like '%" + customerName +"%'";
        }
        if (!"".equals(innerSn)&&innerSn!=null){
            condition+=" and o.innerSn like '%" + innerSn + "%'";
        }
        if (!"".equals(outerSn)&&outerSn!=null){
            condition+=" and o.outerSn like '%" + outerSn + "%'";
        }
        List<PdMerchandise> pdMerchandises = pdMerchandiseDao.findCollectionByConditionNoPage(condition,null,null);
        return pdMerchandises;
    }

    @Override
    public PdBase getById(Integer id) {
        if(id!=null){
            PdBase pdBase = pdBaseDao.get(id);
            return pdBase;
        }
      return null;
    }

    @Override
    public List<PdMerchandise> getByProductId(Integer productId,Integer cusProId) {
        String hql = " and o.id = " + cusProId + " and o.product_ = "+productId;
        List<PdMerchandise> pdMerchandises = pdMerchandiseDao.findCollectionByConditionNoPage(hql,null,null);
        return pdMerchandises;
    }

    @Override
    public List<PdMerchandise> getListByOid(Integer oid, String type) {
        String hql="from PdMerchandise where org=:org and type=:type ";
        Map<String,Object> param=new HashMap<>();
        param.put("org",oid);
        param.put("type",type);
        return pdMerchandiseDao.getListByHQLWithNamedParams(hql,param);
    }

    @Override
    public void deleteByCusProId(PdMerchandise pdMerchandise) {
        pdMerchandiseDao.delete(pdMerchandise);
    }

    @Override
    public void addPdBase(PdBase pdBase) {
        if("".equals(pdBase.getWeightUnit())){
            pdBase.setWeightUnit(null);
        }
        if("".equals(pdBase.getComposition())){
            pdBase.setComposition(null);
        }
        pdBaseDao.save(pdBase);
    }

    @Override
    public void addPdMerchandise(PdMerchandise pdCusPro) {
        pdMerchandiseDao.save(pdCusPro);
    }

    @Override
    public void updatePdBase(PdBase pdBase) {
        pdBaseDao.update(pdBase);
    }

    @Override
    public void updatePdMerchandise(PdMerchandise pdMerchandise) {
        pdMerchandiseDao.update(pdMerchandise);
    }

    @Override
    public PdBase getByInnerSn(Integer oid,String innerSn) {
        String hql = "from PdBase o where o.oid = " + oid + " and o.innerSn = '" + innerSn+"' ";
        PdBase pdBase = pdBaseDao.getByHQL(hql);
        return pdBase;
    }

    @Override
    public List<PdBase> getByInnerSnVague(Integer oid,String innerSn) {
        String hql = "from PdBase o where o.oid = ?0 and o.type='1' and o.enabled=1 ";
        List<PdBase> pdBase;
        if (!"".equals(innerSn)&&innerSn!=null){
            hql+=" and o.innerSn like ?1";
            Object[] params={oid,"%"+innerSn+"%"};
            pdBase = pdBaseDao.getListByHQL(hql,params);
        }else{
            Object[] params={oid};
            pdBase = pdBaseDao.getListByHQL(hql,params);
        }

        return pdBase;
    }

    @Override
    public void deletePdBase(PdBase pdBase) {
        pdBaseDao.delete(pdBase);
    }

    @Override
    public PdMerchandise getByPdCusId(Integer id) {
        return pdMerchandiseDao.get(id);
    }


    @Override
    public PdMerchandise getByMtStock0(Integer Id) {
        String hql=" from PdMerchandise o where o.id="+Id;
        return pdMerchandiseDao.getByHQL(hql);
    }


    @Override
    public PdMerchandise getPdBycusIdAndOuterSn(Integer cusId, String outerSn) {
        String hql=" from PdMerchandise o where o.customer="+cusId+" and o.outerSn='"+outerSn+"'";
        return pdMerchandiseDao.getByHQL(hql);
    }



    @Override
    public PdMerchandise getBySalesRelationShip(Integer salesRelationship_) {
        return pdMerchandiseDao.get(salesRelationship_);
    }

    @Override
    public List<PdMerchandise> getByProductIdAndOid(Integer id, Integer oid) {
        String hql = " from PdMerchandise o where o.product_="+id;
        return pdMerchandiseDao.getListByHQL(hql);
    }

    @Override
    public List<PdMerchandise> getByINnerSn(String innerSn, Integer productId) {
        String hql = " from PdMerchandise o where o.innerSn=?0 and o.product_=?1";
        return pdMerchandiseDao.getListByHQL(hql,innerSn,productId);
    }

    @Override
    public void deleteById(Integer product_) {
        pdBaseDao.deleteById(product_);
    }

    @Override
    public void addPdMerchandiseHistory(PdMerchandiseHistory pcph) {

        pcpdDao.save(pcph);
    }

    @Override
    public List<PdBase> getListByType(Integer oid, String type) {
        String hql = "from PdBase o where o.oid ="+oid+" and type='"+type+"' and enabled='1'";
        List<PdBase> pdBase;
        pdBase = pdBaseDao.getListByHQL(hql);
        return pdBase;
    }


    @Override
    public PdMerchandise getByCusProIdAndProId(Integer cusProId,Integer productId) {
        String hql = "from PdMerchandise o where 1=1 and o.id = " + cusProId + " and o.product_ = "+productId;
        PdMerchandise pdMerchandise1 = pdMerchandiseDao.getByHQL(hql);
        return pdMerchandise1;
    }

    @Override
    public List<PdMerchandise> getCusProByProductId(Integer productId) {
        String hql = " and o.product_ = "+productId;
        List<PdMerchandise> pdMerchandises = pdMerchandiseDao.findCollectionByConditionNoPage(hql,null,null);
        return pdMerchandises;
    }





    @Override
    public PdMerchandise getByPdCustomerId(Integer id) {
        return pdMerchandiseDao.get(id);
    }

    @Override
    public List<Map<String,Object>> getListByCustomer(Integer customerId) {
        String hql = "select new Map(o.id as id,o.outerName as outerName,o.invoiceCategory as invoiceCategory,o.outerSn as outerSn,o.specifications as specifications,o.model as model," +
                "o.unit as unit,o.unitPrice as unitPrice,o.unitPriceNotax as unitPriceNotax,o.unitPriceInvoice as unitPriceInvoice,o.unitPriceNoinvoice as unitPriceNoinvoice," +
                "o.unitPriceReference as unitPriceReference)from PdMerchandise o where o.customer_ =:customer ";

        HashMap<String, Object> params = new HashMap<>();
        params.put("customer",  customerId);
        List<Map<String,Object>> list=pdMerchandiseDao.getListByHQLWithNamedParams(hql,params);
        for (Map<String,Object> pp:list) {
            //获取文件列表
            String sql="select i.orders,i.id,i.uplaod_path uplaodPath,i.title,i.type from t_pd_commodity_media i\n" +
                    "where i.commodity="+pp.get("id");
            List<Map<String,Object>> fileList=pdMerchandiseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
            pp.put("fileList",fileList);
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getProductForJF(Integer customer) {
        String hql="select new Map(o.id as id,o.unit as unit,o.outerName as outerName,o.outerSn as outerSn,o.specifications as specifications,o.model as model,sum( CASE i.invoiceRequire WHEN 1 THEN ( oi.unitPrice * s.signAmount ) WHEN 2 THEN ( oi.unitPriceInvoice * s.signAmount ) WHEN 3 THEN ( oi.unitPriceNoinvoice * s.signAmount ) END ) as  amount ,sum(s.signAmount) as num) from PdInoutStock s " +
                "left join SlOrdersItem oi on oi.id=s.ordersItem " +
                "left join PdMerchandise o on s.product=o.id " +
                "left join SlOrders i on i.id=s.orderNo where i.customer_=:customer and s.signPeroid is not null\n" +
                "GROUP BY o.id";
        HashMap<String, Object> params = new HashMap<>();
        params.put("customer",  customer);
        List<Map<String,Object>> list=pdMerchandiseDao.getListByHQLWithNamedParams(hql,params);
        return list;
    }

    @Override
    public List<Map<String, Object>> getProductJFForYearById(Integer productId,Integer customerId) {
        String hql="select new Map( substring(cast(s.signPeroid as string),1,4) as year,\n" +
                "sum( CASE o.invoiceRequire WHEN 1 THEN ( oi.unitPrice * s.signAmount ) WHEN 2 THEN ( oi.unitPriceInvoice * s.signAmount ) WHEN 3 THEN ( oi.unitPriceNoinvoice * s.signAmount ) END ) as  amount,sum(s.signAmount) as num) from PdInoutStock s " +
                "left join  SlOrders o on o.id=s.orderNo " +
                "left join SlOrdersItem oi on oi.id=s.ordersItem " +
                " where  s.product =:product  and o.customer_=:customer and s.signPeroid is not null \n" +
                "GROUP BY substring(cast(s.signPeroid as string),1,4)";
        HashMap<String, Object> params = new HashMap<>();
        params.put("product",  productId);
        params.put("customer",customerId);
        List<Map<String,Object>> list=pdMerchandiseDao.getListByHQLWithNamedParams(hql,params);
        return list;
    }

    @Override
    public List<Map<String, Object>> getProductJFForMonthById(Integer productId,String year,Integer customerId) {
        String hql="select new Map( s.signPeroid as month,\n" +
                "sum( CASE o.invoiceRequire WHEN 1 THEN ( oi.unitPrice * s.signAmount ) WHEN 2 THEN ( oi.unitPriceInvoice * s.signAmount ) WHEN 3 THEN ( oi.unitPriceNoinvoice * s.signAmount ) END ) as  amount,sum(s.signAmount) as num) from PdInoutStock s " +
                "left join  SlOrders o on o.id=s.orderNo  " +
                "left join SlOrdersItem oi on oi.id=s.ordersItem " +
                " where  s.product =:product  and o.customer_=:customer and  substring(cast(s.signPeroid as string),1,4)  = :year \n" +
                "GROUP BY s.signPeroid ";
        HashMap<String, Object> params = new HashMap<>();
        params.put("product",  productId);
        params.put("year",year);
        params.put("customer",customerId);
        List<Map<String,Object>> list=pdMerchandiseDao.getListByHQLWithNamedParams(hql,params);
        return list;
    }

    @Override
    public List<Map<String, Object>> getProductJfOrderById(Integer productId, Integer month,Integer customerId,String sort,String direction) {
        String hql="select new Map( s.signPeroid as month,\n" +
                "sum( CASE o.invoiceRequire WHEN 1 THEN ( oi.unitPrice * s.signAmount ) WHEN 2 THEN ( oi.unitPriceInvoice * s.signAmount ) WHEN 3 THEN ( oi.unitPriceNoinvoice * s.signAmount ) END ) as  amount,s.signAmount as num,o.sn as sn) from PdInoutStock s " +
                "left join  SlOrders o on o.id=s.orderNo " +
                "left join SlOrdersItem oi on oi.id=s.ordersItem " +
                " where  s.product =:product and o.customer_=:customer and  s.signPeroid  = :month \n" ;

        if(StringUtils.isNotEmpty(sort)){
            if("amount".equals(sort)){
                hql+=" order by  CASE o.invoiceRequire WHEN 1 THEN ( oi.unitPrice * s.signAmount ) WHEN 2 THEN ( oi.unitPriceInvoice * s.signAmount ) WHEN 3 THEN ( oi.unitPriceNoinvoice * s.signAmount ) END  ";
            }
            if("num".equals(sort)){
                hql+=" order by s.signAmount ";
            }
        }

        if(StringUtils.isNotEmpty(direction)){
            hql+=" "+direction;
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put("product",  productId);
        params.put("month",month);
        params.put("customer",customerId);
        List<Map<String,Object>> list=pdMerchandiseDao.getListByHQLWithNamedParams(hql,params);
        return list;
    }

    @Override
    public List<Map<String, Object>> getProductListByType(String type,Integer oid,String enabled) {
        String hql = "select new Map(o.id as id,o.outerName as outerName,o.invoiceCategory as invoiceCategory,o.outerSn as outerSn,o.specifications as specifications,o.model as model," +
                "o.unit as unit,o.unitPrice as unitPrice,o.unitPriceNotax as unitPriceNotax,o.unitPriceInvoice as unitPriceInvoice,o.unitPriceNoinvoice as unitPriceNoinvoice," +
                "o.unitPriceReference as unitPriceReference)from PdMerchandise o where o.org =:oid and o.enabled=:enabled ";

       if("1".equals(type)){
           hql+=" and o.type='1'";
       }
        if("2".equals(type)){
            hql+=" and o.type='2'";
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid",  oid);
        params.put("enabled",enabled);
        List<Map<String,Object>> list=pdMerchandiseDao.getListByHQLWithNamedParams(hql,params);
        List<PdCommodityMedia> files=pdCommodityMediaDao.getListByHQL("from PdCommodityMedia where org="+oid);
        for (Map<String,Object> pp:list) {
            //获取文件列表
            List<PdCommodityMedia> fileList=new ArrayList<>();

            for (PdCommodityMedia f:files ) {
                if(pp.get("id").toString().equals(f.getCommodity().toString())){
                    fileList.add(f);
                }
            }
             pp.put("fileList",fileList);
        }
        return list;
    }



    @Override
    public PdMerchandise getPdByCusProId(Integer cusProId) {
        return pdMerchandiseDao.get(cusProId);
    }

    @Override
    public Map getIncMerchandises(User user, String ids) {
        String sql = "select soi.id as item_id, pm.id, pm.outer_name, pm.outer_sn, pm.model, pm.specifications, pm.org, pmi.pattern, pmi.user_defined, group_concat(pmi.type) pmi_type, ( case pmi.pattern when 1 then '' when 2 then pm.specifications when 3 then pm.model when 4 then concat(pm.specifications, pm.model) when 5 then concat(pm.model, pm.specifications) when 6 then pmi.user_defined end ) as spemodel from t_pd_merchandise pm left join t_pd_merchandise_invoice pmi on pmi.merchandise = pm.id left join t_sl_orders_item soi on soi.sales_relationship = pm.id where 1=1 ";

        sql+= " and pm.org="+user.getOid();

        if(StringUtils.isNotEmpty(ids)){
            sql+= " and soi.id in ("+ids+")";
        }

        sql+="group by pm.id having locate(1, ifnull(pmi_type,0)) = 0";
        return pdMerchandiseDao.findMapByConditionNoPage(sql,new Object[]{}) ;
    }
}
