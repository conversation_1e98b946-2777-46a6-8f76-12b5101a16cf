package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022-12-21
 */

@Entity ( name ="TpAppHistory" )
@Table ( name ="t_tp_app_history" )
public class TpAppHistory  implements Serializable {

    private static final long serialVersionUID =  6082638678459505577L;

    /**
     * ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 应用ID
     */
    @Column(name = "app" )
    private Integer app;

    /**
     * 第三方大厂(1-weChat,2-byteDance)
     */
    @Column(name = "tg_code" )
    private Integer tgCode;

    /**
     * 应用ID
     */
    @Column(name = "app_id" )
    private String appId;

    /**
     * 应用秘钥
     */
    @Column(name = "app_secret" )
    private String appSecret;

    /**
     * 需要注册AuthAcc登录使用
     */
    @Column(name = "need_acc" )
    private Integer needAcc;

    /**
     * 需要获取第三方UserInfo
     */
    @Column(name = "need_user_info" )
    private Integer needUserInfo;

    /**
     * 类型:true-第三方小程序,false-独立小程序
     */
    @Column(name = "tp_type" )
    private Integer tpType;

    /**
     * 平台ID
     */
    @Column(name = "platform_id" )
    private Integer platformId;

    /**
     * 第三方应用名称appName/nickName,tt:4-30chars;wx:4-30chars
     */
    @Column(name = "app_name" )
    private String appName;

    /**
     * 有效：true-受wonderss管理，更新accesstoken，false/null-不受wonderss管理，不获取accesstoken
     */
    @Column(name = "enabled" )
    private Integer enabled;

    /**
     * 备注
     */
    @Column(name = "memo" )
    private String memo;

    /**
     * 创建人id
     */
    @Column(name = "creator" )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name" )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time" )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator" )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name" )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time" )
    private Date updateTime;

    /**
     * 修改前记录ID
     */
    @Column(name = "previous_id" )
    private Integer previousId;

    /**
     * 版本号,每次修改+1
     */
    @Column(name = "version_no" )
    private Integer versionNo;

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getApp() {
        return this.app;
    }

    public void setApp(Integer app) {
        this.app = app;
    }

    public Integer getTgCode() {
        return this.tgCode;
    }

    public void setTgCode(Integer tgCode) {
        this.tgCode = tgCode;
    }

    public String getAppId() {
        return this.appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return this.appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public Integer getNeedAcc() {
        return this.needAcc;
    }

    public void setNeedAcc(Integer needAcc) {
        this.needAcc = needAcc;
    }

    public Integer getNeedUserInfo() {
        return this.needUserInfo;
    }

    public void setNeedUserInfo(Integer needUserInfo) {
        this.needUserInfo = needUserInfo;
    }

    public Integer getTpType() {
        return this.tpType;
    }

    public void setTpType(Integer tpType) {
        this.tpType = tpType;
    }

    public Integer getPlatformId() {
        return this.platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getAppName() {
        return this.appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Integer getEnabled() {
        return this.enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public String getMemo() {
        return this.memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return this.creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return this.updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return this.updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getPreviousId() {
        return this.previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return this.versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

}
