package cn.sphd.miners.modules.finance.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by lyx on 2021/11/18.
 * 财务_帐务票据图片库   1.179财务优化2101
 */
@Entity
@Table(name="t_finance_account_bill_image")
public class FinanceAccountBillImage implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="account_detail"   , nullable=true , unique=false)
    private Integer accountDetail;   //帐户明细ID

    @Column(name="account_id"   , nullable=true , unique=false)
    private Integer accountId;   //帐户ID,这里外键未画出

    @Column(name="account_bill"   , nullable=true , unique=false)
    private Integer accountBill;   //帐户票据ID

    @Column(name="account_bill_history"  , length=255 , nullable=true , unique=false)
    private String accountBillHistory;  //帐户票据历史ID,多个以逗号分隔

    @Column(name="title"  , length=255 , nullable=true , unique=false)
    private String title;  //标题

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;  //类型:1-图片,2-视频,3-文档

    @Column(name="uplaod_path"  , length=255 , nullable=true , unique=false)
    private String uplaodPath;   //文件上传路径

    @Column(name="orders"  , length=11 , nullable=true , unique=false)
    private Integer orders;   //排序

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作：1-增,2-删,3-改;4-启停用

    @Column(name="version_no"  , nullable=true , unique=false)
    private String versionNo;  //版本号,每次修改+1

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getAccountBill() {
        return accountBill;
    }

    public void setAccountBill(Integer accountBill) {
        this.accountBill = accountBill;
    }

    public String getAccountBillHistory() {
        return accountBillHistory;
    }

    public void setAccountBillHistory(String accountBillHistory) {
        this.accountBillHistory = accountBillHistory;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUplaodPath() {
        return uplaodPath;
    }

    public void setUplaodPath(String uplaodPath) {
        this.uplaodPath = uplaodPath;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }
}
