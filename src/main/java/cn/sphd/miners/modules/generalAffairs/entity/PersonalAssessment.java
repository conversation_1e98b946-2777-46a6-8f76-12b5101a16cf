package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.modules.system.entity.User;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2015/12/18.
 * 评价表
 */
@Entity
@Table(name = "t_personal_assessment")
public class PersonalAssessment implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="assess_date"   , nullable=true , unique=false)
    private Date assessDate;//评价日期

    @Column(name="assess_user"   , nullable=true , unique=false)
    private Integer assessUser;//评价人id

    @Column(name="type"  , length=100 , nullable=true , unique=false)
    private String type;//类型

    @Column(name="content"  , length=255 , nullable=true , unique=false)
    private String content;//评价内容

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="assess_user_name" ,length=100, nullable = true,unique = false)
    private String assessUserName; //评价人

    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=true, updatable=true)
    private User user;

    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer user_;



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getAssessDate() {
        return assessDate;
    }

    public void setAssessDate(Date assessDate) {
        this.assessDate = assessDate;
    }

    public Integer getAssessUser() {
        return assessUser;
    }

    public void setAssessUser(Integer assessUser) {
        this.assessUser = assessUser;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getUser_() {
        return user_;
    }

    public void setUser_(Integer user_) {
        this.user_ = user_;
    }

    public String getAssessUserName() {
        return assessUserName;
    }

    public void setAssessUserName(String assessUserName) {
        this.assessUserName = assessUserName;
    }
}
