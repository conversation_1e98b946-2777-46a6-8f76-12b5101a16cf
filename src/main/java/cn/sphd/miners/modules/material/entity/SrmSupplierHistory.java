package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Entity
@Table(name = "t_srm_supplier_history")
public class SrmSupplierHistory {
    private Integer id;
    private Integer org;
    private Integer supplier;
    private String name;
    private String fullName;
    private String codeName;
    private Integer contactCount;
    private String contact;
    private String mobile;
    private String telephone;
    private String fax;
    private String email;
    private String address;
    private String paymentType;
    private String paymentMethod;
    private String bankCode;
    private String bankName;
    private String bankNo;
    private Integer principal;
    private String principalName;
    private String principalMobile;
    private String keywords;
    private Integer supplyCount;
    private Integer exclusiveCount;
    private Integer cutCount;
    private Integer chargeAcceptable;
    private String chargeBegin;
    private Integer chargePeriod;
    private String isImprest;
    private String invoicable;
    private String vatsPayable;
    private Integer draftAcceptable;
    private Integer vatsCount;
    private Integer otherInvoiceCount;
    private Integer notInvoiceCount;
    private Integer enabled;
    private Date enabledTime;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;
    //税率
    private BigDecimal taxRate;


    private BigDecimal imprestProportion;

    @Transient
    private List<Map<String,Object>> qImages;
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org", nullable = true)
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "name", nullable = true, length = 100)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "full_name", nullable = true, length = 255)
    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    @Basic
    @Column(name = "code_name", nullable = true, length = 20)
    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    @Basic
    @Column(name = "contact_count", nullable = true)
    public Integer getContactCount() {
        return contactCount;
    }

    public void setContactCount(Integer contactCount) {
        this.contactCount = contactCount;
    }

    @Basic
    @Column(name = "contact", nullable = true, length = 100)
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Basic
    @Column(name = "mobile", nullable = true, length = 100)
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Basic
    @Column(name = "telephone", nullable = true, length = 100)
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    @Basic
    @Column(name = "fax", nullable = true, length = 100)
    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    @Basic
    @Column(name = "email", nullable = true, length = 100)
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Basic
    @Column(name = "address", nullable = true, length = 100)
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Basic
    @Column(name = "payment_type", nullable = true, length = 1)
    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @Basic
    @Column(name = "payment_method", nullable = true, length = 1)
    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    @Basic
    @Column(name = "bank_code", nullable = true, length = 100)
    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    @Basic
    @Column(name = "bank_name", nullable = true, length = 100)
    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    @Basic
    @Column(name = "bank_no", nullable = true, length = 100)
    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    @Basic
    @Column(name = "principal", nullable = true)
    public Integer getPrincipal() {
        return principal;
    }

    public void setPrincipal(Integer principal) {
        this.principal = principal;
    }

    @Basic
    @Column(name = "principal_name", nullable = true, length = 100)
    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    @Basic
    @Column(name = "principal_mobile", nullable = true, length = 50)
    public String getPrincipalMobile() {
        return principalMobile;
    }

    public void setPrincipalMobile(String principalMobile) {
        this.principalMobile = principalMobile;
    }

    @Basic
    @Column(name = "keywords", nullable = true, length = 100)
    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    @Basic
    @Column(name = "supply_count", nullable = true)
    public Integer getSupplyCount() {
        return supplyCount;
    }

    public void setSupplyCount(Integer supplyCount) {
        this.supplyCount = supplyCount;
    }

    @Basic
    @Column(name = "exclusive_count", nullable = true)
    public Integer getExclusiveCount() {
        return exclusiveCount;
    }

    public void setExclusiveCount(Integer exclusiveCount) {
        this.exclusiveCount = exclusiveCount;
    }

    @Basic
    @Column(name = "cut_count", nullable = true)
    public Integer getCutCount() {
        return cutCount;
    }

    public void setCutCount(Integer cutCount) {
        this.cutCount = cutCount;
    }

    @Basic
    @Column(name = "charge_acceptable")
    public Integer getChargeAcceptable() {
        return chargeAcceptable;
    }

    public void setChargeAcceptable(Integer chargeAcceptable) {
        this.chargeAcceptable = chargeAcceptable;
    }

    @Basic
    @Column(name = "charge_begin", nullable = true, length = 1)
    public String getChargeBegin() {
        return chargeBegin;
    }

    public void setChargeBegin(String chargeBegin) {
        this.chargeBegin = chargeBegin;
    }

    @Basic
    @Column(name = "charge_period", nullable = true)
    public Integer getChargePeriod() {
        return chargePeriod;
    }

    public void setChargePeriod(Integer chargePeriod) {
        this.chargePeriod = chargePeriod;
    }

    @Basic
    @Column(name = "is_imprest", nullable = true, length = 1)
    public String getIsImprest() {
        return isImprest;
    }

    public void setIsImprest(String isImprest) {
        this.isImprest = isImprest;
    }

    @Basic
    @Column(name = "invoicable", nullable = true, length = 1)
    public String getInvoicable() {
        return invoicable;
    }

    public void setInvoicable(String invoicable) {
        this.invoicable = invoicable;
    }

    @Basic
    @Column(name = "vats_payable", nullable = true, length = 1)
    public String getVatsPayable() {
        return vatsPayable;
    }

    public void setVatsPayable(String vatsPayable) {
        this.vatsPayable = vatsPayable;
    }

    @Basic
    @Column(name = "draft_acceptable", nullable = true)
    public Integer getDraftAcceptable() {
        return draftAcceptable;
    }

    public void setDraftAcceptable(Integer draftAcceptable) {
        this.draftAcceptable = draftAcceptable;
    }

    @Basic
    @Column(name = "vats_count", nullable = true)
    public Integer getVatsCount() {
        return vatsCount;
    }

    public void setVatsCount(Integer vatsCount) {
        this.vatsCount = vatsCount;
    }

    @Basic
    @Column(name = "other_invoice_count", nullable = true)
    public Integer getOtherInvoiceCount() {
        return otherInvoiceCount;
    }

    public void setOtherInvoiceCount(Integer otherInvoiceCount) {
        this.otherInvoiceCount = otherInvoiceCount;
    }

    @Basic
    @Column(name = "not_invoice_count", nullable = true)
    public Integer getNotInvoiceCount() {
        return notInvoiceCount;
    }

    public void setNotInvoiceCount(Integer notInvoiceCount) {
        this.notInvoiceCount = notInvoiceCount;
    }

    @Basic
    @Column(name = "enabled", nullable = true)
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time", nullable = true)
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "creator", nullable = true)
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name", nullable = true, length = 100)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator", nullable = true)
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name", nullable = true, length = 100)
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date", nullable = true)
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation", nullable = true, length = 1)
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id", nullable = true)
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no", nullable = true)
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
    @Basic
    @Column(name = "supplier", nullable = true)
    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }
    @Transient
    public List<Map<String, Object>> getqImages() {
        return qImages;
    }

    public void setqImages(List<Map<String, Object>> qImages) {
        this.qImages = qImages;
    }

    @Basic
    @Column(name = "tax_rate", nullable = true)
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }
    @Basic
    @Column(name = "imprest_proportion")
    public BigDecimal getImprestProportion() {
        return imprestProportion;
    }

    public void setImprestProportion(BigDecimal imprestProportion) {
        this.imprestProportion = imprestProportion;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SrmSupplierHistory that = (SrmSupplierHistory) o;
        return id == that.id && Objects.equals(org, that.org) && Objects.equals(name, that.name) && Objects.equals(fullName, that.fullName) && Objects.equals(codeName, that.codeName) && Objects.equals(contactCount, that.contactCount) && Objects.equals(contact, that.contact) && Objects.equals(mobile, that.mobile) && Objects.equals(telephone, that.telephone) && Objects.equals(fax, that.fax) && Objects.equals(email, that.email) && Objects.equals(address, that.address) && Objects.equals(paymentType, that.paymentType) && Objects.equals(paymentMethod, that.paymentMethod) && Objects.equals(bankCode, that.bankCode) && Objects.equals(bankName, that.bankName) && Objects.equals(bankNo, that.bankNo) && Objects.equals(principal, that.principal) && Objects.equals(principalName, that.principalName) && Objects.equals(principalMobile, that.principalMobile) && Objects.equals(keywords, that.keywords) && Objects.equals(supplyCount, that.supplyCount) && Objects.equals(exclusiveCount, that.exclusiveCount) && Objects.equals(cutCount, that.cutCount) && Objects.equals(chargeAcceptable, that.chargeAcceptable) && Objects.equals(chargeBegin, that.chargeBegin) && Objects.equals(chargePeriod, that.chargePeriod) && Objects.equals(isImprest, that.isImprest) && Objects.equals(invoicable, that.invoicable) && Objects.equals(vatsPayable, that.vatsPayable) && Objects.equals(draftAcceptable, that.draftAcceptable) && Objects.equals(vatsCount, that.vatsCount) && Objects.equals(otherInvoiceCount, that.otherInvoiceCount) && Objects.equals(notInvoiceCount, that.notInvoiceCount) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, name, fullName, codeName, contactCount, contact, mobile, telephone, fax, email, address, paymentType, paymentMethod, bankCode, bankName, bankNo, principal, principalName, principalMobile, keywords, supplyCount, exclusiveCount, cutCount, chargeAcceptable, chargeBegin, chargePeriod, isImprest, invoicable, vatsPayable, draftAcceptable, vatsCount, otherInvoiceCount, notInvoiceCount, enabled, enabledTime, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
