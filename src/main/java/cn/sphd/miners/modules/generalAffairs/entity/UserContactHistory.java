package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2019/10/12.
 */
@Entity
@Table(name = "t_sys_user_contact_history")
public class UserContactHistory implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "contact")
    private Integer contactId;//客户社交号ID
    @Column(name = "user")
    private Integer userId;//用户ID
    @Column(name = "org")
    private Integer org;//机构ID
    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//类型:1-mobile,2-qq,3-email,4-weixin,5-weibo,9-其他自定义标签
    @Column(name="name"  , length=50 , nullable=true , unique=false)
    private String name;//社交中文名称
    @Column(name="code"  , length=50 , nullable=true , unique=false)
    private String code;//号码
    @Column(name = "orders")
    private Integer orders;//排序
    @Column(name = "is_open")
    private boolean isOpen;//是否公开:true-公开,false-保密
    @Column(name = "enabled")
    private boolean enabled;//状态：0-不启用,1-启用
    @Column(name="enabled_time"   , nullable=true , unique=false)
    private Date enabled_time;//启停用时间
    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注
    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;
    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;
    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;
    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;
    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;
    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;
    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation; //操作:1-增,2-删,3-改
    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID
    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号
    @Column(name="version_memo"  , length=100 , nullable=true , unique=false)
    private String versionMemo; //版本说明
    @Column(name="sub_previous_id"   , nullable=true , unique=false)
    private Integer subPreviousId; //子表修改前记录ID
    @Column(name="sub_version_no"   , nullable=true , unique=false)
    private Integer subVersionNo; //子表版本号
    @Column(name="sub_version_memo"  , length=100 , nullable=true , unique=false)
    private String subVersionMemo; //子表版本说明


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getContactId() {
        return contactId;
    }

    public void setContactId(Integer contactId) {
        this.contactId = contactId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Date getEnabled_time() {
        return enabled_time;
    }

    public void setEnabled_time(Date enabled_time) {
        this.enabled_time = enabled_time;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getVersionMemo() {
        return versionMemo;
    }

    public void setVersionMemo(String versionMemo) {
        this.versionMemo = versionMemo;
    }

    public Integer getSubPreviousId() {
        return subPreviousId;
    }

    public void setSubPreviousId(Integer subPreviousId) {
        this.subPreviousId = subPreviousId;
    }

    public Integer getSubVersionNo() {
        return subVersionNo;
    }

    public void setSubVersionNo(Integer subVersionNo) {
        this.subVersionNo = subVersionNo;
    }

    public String getSubVersionMemo() {
        return subVersionMemo;
    }

    public void setSubVersionMemo(String subVersionMemo) {
        this.subVersionMemo = subVersionMemo;
    }
}
