*{ font-family: "Microsoft Yahei" ;   }
td,th{ text-align:center!important; font-size:14px; vertical-align:middle!important; }
.clr{ clear:both;  }
.hd{ display:none;   }
input{min-height:35px!important;}
/*瀵艰埅*/
.navLogo, .nav_normal {  border-left:5px solid #364150 !important ;  }
.nav_normal:hover{  border-left:5px solid #2B3643 !important ; }
.navLogo span, .nav_normal span, .nav_acive span{  font-size:16px ;  padding-left:15px;           }
.nav_acive{ border-left:5px solid #32C5D2 !important; background:#2B3643!important;     }
ul>li.navLogo{ margin-top: 50px; }
.navLogo span{ font-size: 23px; line-height:50px; display:inline-block; color:#fff; margin-left:20px;     }
/*鍙抽潰*/
div.contn{ padding:10px;  }
div.conNav{ font-size:16px; font-weight:bolder; border-bottom:1px solid #C9C9C9; padding:5px; line-height:50px; color:#333;   }
div.Con{ color:#333; position:relative;    }
.page-content{ min-height:1000px; }
/*鎸夐挳*/
.aboutbtn,.opbtn { display:inline-block; padding:3px 10px; cursor:pointer; font-size:14px; margin-right:20px;    }
.aboutbtn:after{ display:block; content: " ";  }
.bigBtn{ padding:7px 30px;  font-weight:bolder; font-size:16px;  }
.azury, .gray, .blu, .orange{ color:#fff;  }
.azury{ background:#32C5D2; }
.azury:hover{ background:#26A1AB; }
.gray{ background:#999; }
.gray:hover{ background:#333; }
.blu{ background:#009DDA; }
.blu:hover{ background:#26A1AB; }
.orange{ background:#FD8A23;}	
.orange:hover{ background:#26A1AB;     }
.hand{ cursor:pointer; }

/* 杈撳叆椤�*/
.opItem{ font-size:16px; padding:10px 0;  }
.opItem_control{ text-align:center; margin-top:30px; margin-bottom:30px;       }
.itemTtl{ display:inline-block; width:100px; text-align:right; vertical-align:top; }
.itemCon{ width:450px; display:inline-block; color:#333; font-size:14px;  }
.itemCon_textarea{ width:535px; margin-left:100px; height:200px;    }
.itemTip{ color:#666; font-size:12px;   }


/*Scan*/
.scanCon{  text-align:center; padding-top:100px;   }
.scanCon img:first-child{  display:inline-block; width:80px;    }     /*杞欢Logo*/
.scanCon span:nth-child(2){  display:block;  line-height: 50px; font-weight:bold;     }    /*杞欢鐗堟湰鍙�/
.scanCon img:nth-child(3){ display:inline-block; width:200px;   }	/*杞欢浜岀淮鐮�/
.scanCon h3:nth-child(4){  margin-top:30px;    }	/*瑙ｉ噴浜岀淮鐮�/

/*Opinion*/
.addOpinion{ margin: 15px 0; color:#fff;  }
.solve{ color:#009DDA; }  /* yes */
.unsolve{ color: #FF9900; } 	/* no */
.nosolve{ color:#444;  }		/* undo */
.opinionDetails{ cursor:pointer;  }		
.op_ttl{ }
.op_date{  color:#666;  }
.op_question{ text-indent:2em; font-size:16px; color:#222;line-height:25px; }
.op_detl{ padding:20px 0 10px;  color:#333; }
.op_item_qs{ background:#fff;   }
.op_item_as{  background:#ebebeb;  }
.op_item_as, .op_item_qs{  position:relative; padding:10px 15px; border-bottom:1px solid #eee;   }

.op_identity{ width:65px; text-align:center; position: absolute;    }
.op_iden_qs, .op_iden_as{ font-weight:bold;   }
.op_iden_as{ color:#0099FC;   }
.op_iden_qs{ color:#FF9900;  }  
.op_con{ margin-left:80px;  font-size:12px;  }
.op_time{  color:#ababab; }
.op_end{ border-top:2px solid #ccc; padding:30px 0 ; }

.thanks{ display: none; text-align:center; width:400px; margin:200px auto 0 ; background: #fff; z-index: 1; }
.thanks div:nth-child(1){ padding:50px; font-size:18px; }
.thanks div:nth-child(2){ padding:30px; font-size:16px; font-weight:bold; color:#00AEED; border-top:1px solid #ccc;    }

.op_con textarea{ display:block; border:1px solid #ccc; height:40px; width:100%;    }


/* version */
.versionCon{ font-size:12px; font-weight:normal; line-height:30px;  }
.vrIcon{ padding:3px 10px; font-size:10px;  position:relative; top:-5px;  }
.vrSpan{ margin-right:30px; display:inline-block; color:#bbb;  }
.vrContent{ margin-right:300px; border-right:1px solid #ccc; padding:50px 20px; min-height:700px;   }
.vr_history{ position:absolute; right:0; top:0; width:240px; padding:30px;       }
.vr_history_item{ height:50px; vertical-align:middle; font-size:16px; cursor:pointer;  }

/*service*/
.textContent{  padding:50px 20px; min-height:700px;   }


/* help */
.searchNav{ padding:100px 0 50px 0;  }
.searchDiv{    max-width:800px; min-width:400px; width:70%; position:relative; margin:0 auto;      }
.search_inout{ border:1px solid #32C5D2!important; line-height:40px; height:40px!important; display: inline-block;  width:100%;    }
.searchBtn{ position:absolute; right:-13px; top:0; font-size:18px; cursor:pointer; color:#fff; font-weight:bolder;line-height:40px; height:40px; padding:5px 25px;  }
.searchCon{ padding:0 50px;              }
.Q_div{ width:33%; float:left;   }
.Q_div .Q_ttl, .Q_div>div{ margin-left:80px;    }
.Q_ttl{ color:#009DDA; font-size:20px; font-weight:bolder;    }
.Q_item{ padding:15px 0; font-size:16px; cursor:pointer;     }
.Q_item:hover{ text-decoration:underline;     }
.opinion_ttl{ display:block;  }
.opinion_con{ display:block; height:150px; }
.opinion_submit{ font-size:16px; padding:5px 10px; cursor:pointer;     }

.resultDiv{ background:#E4E4E4; padding:20px 0;      }
.goback{ width:50px;height:50px;  display:inline-block; margin-left:30px; position:relative; top:30px;
	cursor:pointer; background:url(/css/content/img/goback.png) no-repeat; background-size:50px ;            }
.research_input{ border:1px solid #ccc!important; width:600px; height:30px!important;          }
.researchBtn{ position:relative; left:-56px; top:-3px; font-size:16px; height:30px; display:inline-block; padding:4px 10px; line-height:30px;           }

.resultCon{ padding:20px 0 100px 30px;          }
.resultKey{ padding:15px 0; font-size:14px;               }
.resultKey>span{ font-weight:bolder;               }
.answerItem{ margin-top:30px;font-size:16px;        }
.as_ttl{ font-weight: bolder; color:#32C5D2; margin-bottom:10px;   }


/*feedback*/
.manageNav{font-size:16px; font-weight:bolder; padding:5px; line-height:50px; color:#333;    }
#opinionDetailsCon{ width:80%; }

/* scanManage */
.det_img{ max-height:100px;  }
.upload{ margin-left:100px;  }
.bonceContainer{ width:80%; }

/* privacy */
.resultKey>label{ display:inline-block; }

/* helpManage */
.upcon_help{ display:none; }
.helpBtn_update{ float:right;  margin-right:20px;  margin-top:15px;    }
.saveHelpBtn{ margin-left:100px;   }
.editDiv{ margin-top:50px; }
.modelSelect{ margin-bottom:10px;  }

/* charge */
.answer_content{ margin:0 0 20px; }
.chargeInput{ background:#fff; display:inline-block; border:1px solid #ccc; width:80%;  border-radius:5px; margin-top:20px; }
#charge_content{ padding-left:50px; }
#paContainer{  padding:0; }
textarea, input, select{ border:1px solid #ccc;  }
select{ height:35px; }
#chargeCon{ width:600px;  }
#charge_content>img{ max-width:435px; }
.scanVersion{ padding:10px; }


