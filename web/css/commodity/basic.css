.conAll {
    width: 100%;
    overflow-x: auto;
}

#Goods {
    width: 1370px;
}

#minStockLog, #curStockLog {
    width: 800px;
}

#curOrders {
    width: 825px;
}

#Goods i.fa {
    color: #48cfad;
    margin-right: 15px;
}

#list {
    min-width: 1750px;
}

.xing {
    color: #ed5565;
}

.ttl, .longTtl {
    margin-right: 5px;
    display: inline-block;
}

.ttl {
    min-width: 75px;
}

.longTtl {
    min-width: 315px;
}

.item {
    margin-left: 50px;
}

.mar {
    margin-left: 40px;
}

.mar15 {
    margin-left: 15px;
}

.trItem {
    line-height: 35px;
}

/*#Goods .hd{display: block!important;      }*/
.scan {
    display: none;
}

.goodsCon .scan {
    display: inline-block;
    text-align: left;
    width: 188px;
    color: #5d9cec;
}
#importCon {
    width: 95%;
    height: 450px;
    border: none;
}

#createOrg, #inPerson {
    cursor: pointer;
}

input[disabled],select[disabled]{ background:#eaeaea;   }

.relative {
    position: relative;
    width: 162px;
    display: inline-block;
    height: 40px;
}

.select {
    position: absolute;
    width: 100%;
    z-index: 1;
    max-height: 200px;
    overflow: auto;
    background: #fff;
    border: 1px solid #48cfad;
    border-top: none;
    top: 32px;
    display: none;
}

.select option {
    height: 30px;
    line-height: 30px;
    padding-left: 10px;
}

.select option:hover {
    background: #eaeaea;
}

.ty-colFileTree > ul ul {
    display: none;
    padding-left: 15px
}

.ty-colFileTree .ty-treeItemActive > i.fa-file {
    color: #fff;
}

#rate {
    display: inline-block;
    width: 188px;
}

/*通用型商品*/

.notice-blue{
    font-size: 12px;
    color: #0070c0;
}
.main-nav{
    padding: 10px 0;
}
.con-wrap{
    padding: 10px 0;
}
.editBtn{
    margin: 0 40px 0 20px;
}
.ty-table tbody td.bg-gray{
    background: #ddd;
}
.auxTb{
    display: none;
}
.indexInput .faceul .disabled{
    background: #cbcbcb;
    cursor: not-allowed;
}
.indexInput .faceul .disabled a{
    color: #777;
}
.indexInput .faceul button{
    padding: 0;
    border: none;
    background: none;
}
.indexInput .faceul button:disabled {
    cursor: not-allowed;
}
.flex-box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;  }
.flex-box > div {
    position: relative;
    margin-bottom: 12px;
}
.linkBtn {  color: #036bc0;
    line-height: 30px;
    padding: 0 20px;
    cursor: default;
}

#newContractInfo .uploadify-button {
    border: none;
    line-height: 12px;
    padding: 3px 6px;
    font-size: 12px;
    color: #1685ea;
    cursor: pointer;
    text-decoration: none;
    font-weight: normal;
    display: inline-block;
    vertical-align: middle;
}
.fileCon {
    margin-bottom: 10px;
    border: 1px solid #ddd;
    height: 30px;
    border-radius: 2px;
    background: #fff;
    line-height: 30px;
    padding: 0 9px;
}
.fileCon .fileIm {
    margin: 0;
}
.fileCon .fileIm .fa {
    margin: 0 8px;
    color: #5d9cec;
}

#leadingBefore hr{
    border-top: 1px solid #ccc;
}
#leadingBefore .tipBlue, #leadingBefore .fa{
    color: #5d9cec;
}
#leadingBefore .fap{
    cursor: default;
}
.funCon{
    width: 90%;
    margin: auto ;
}
.funCon >div{
    margin-top: 30px ;
    margin-bottom: 40px ;
}
.align-right{
    text-align: right;
}
.blueCare{
    color: #0070bf;
}
.intend{
    text-indent: 28px;
}
.placeList {
    margin:30px 0;
    width:90%;
}
.placeList>li{
    list-style: none;
    background:#fff;
    line-height:30px;
}
.placeList>li:nth-child(even){
    background:#f5f5f5;
}
.placeList .fa{
    margin-right: 20px;
}
.bounce_Fixed4,.bounce_Fixed5 {
    position: fixed;
    display: none;
    background: rgba(100, 100, 100, 0.5);
    width: 100%;
    padding-bottom: 200px;
}
.bounce_Fixed4 {
    z-index: 100014;
}
.bounce_Fixed5 {
    z-index: 100015;
}
.gray-box{
    min-height: 30px;
    padding: 4px 8px;
    border: 1px solid #dcdfe6;
    background: #d8d8d8;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 422px;
}
.lineBtn{
    color: #0070bf;
    cursor: pointer;
    font-weight: bold;
}
.placeBtn .linkBtn{
    padding: 0 0 0 16px;
    line-height: normal;
}
.receiveMethod > div {
    margin-top: 10px;
    padding: 20px 0;
    border-top: 1px solid #ccc;
}
.receiveMethod > div:first-child {
    border-top: none;
}
.padd{
    padding: 8px 0;
}
.addressListString{
    width : 600px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.wideBody{
    margin: auto;
    width: 80%;
}
.receiveMethod > div{
    padding: 30px 0;
    border-top: 1px solid #dddada;
}
.receiveMethod .linkBtn{
    padding-right: 0;
    font-weight: bold
}
.careful{
    color: #0070bf;
}
.flexBox{
    width: 100%;
    box-sizing: border-box;
    border-radius: 2px;
    position: relative;
    font-size: 13px;
    overflow: hidden;
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: opacity .2s;
}
.sale_ttl1{
    margin-right: 10px;
    display: inline-block;
    min-width: 90px;
    text-align: right;
    line-height: 32px;
    vertical-align: top;
}
.sale_con1{
    display: inline-block;
}
#ReceiveName{
    height: 30px;
    width: 180px;
    line-height: 30px;
}
#contactFlag{
    line-height:30px;
}
#chooseCusContact .fa{
    color: #50a9ea;
}
.chooseCusBtn{
    background: #ddd;
    width: 40px;
    display:inline-block;
    text-align: center;
    line-height: 30px;
    height: 30px;
    margin-left: -5px;
}
.cusList {
    margin:20px auto;
    width:90%;
}
.cusList>li{
    list-style: none;
    background:#fff;
    line-height:30px;
    padding:0 15px;
}
.cusList>li:nth-child(even){
    background:#f5f5f5;
}
.sale_con1>input{
    width: 220px;
}
.linkUploadify .uploadify-button {
    margin: 0;
    background-color: inherit;
    border: none;
    line-height: 12px;
    padding: 3px 6px;
    font-size: 12px;
    color: #1685ea;
    cursor: pointer;
    text-decoration: none;
    font-weight: normal;
    display: inline-block;
    vertical-align: middle;
}
.linkUploadify .uploadify-button:active{
    border: none;
}
.saleUploadBtn .uploadify-button {
    margin: 0;
    background-color: #5d9cec;
    color: #fff;
    padding: 0 24px;
    height: 32px;
    line-height: 32px;
    border: none;border-radius: 0;
}
.saleUploadBtn .uploadify-button:hover {
    color:#fff;
    background-color: #5d96e6;
}
.uploadify-button:visited{
    text-decoration: none
}
#newContectInfo .uploadify-button{
    line-height: 28px;
}
.filePic{
    background-repeat: no-repeat;
}
.imgsthumb{
    position: relative;
    margin: 10px 10px 10px 0;
    width:50px;display: inline-block;
    overflow: hidden;
}
.imgsthumb .filePic{
    display: inline-block;
    width: 24px; height: 30px;
    background-size: 100%;
    background-position: center center;
}
.imgsthumb span{
    padding: 2px;  color: red;
    cursor: pointer;  }
.imgsthumb a{
    display: none;
    /*background-color: rgba(93, 156, 236,.9);*/
    /*color: #fff;  */
    color: #0b94ea;
    height: 18px;
    line-height:18px;  width:36px;
    margin: 0 auto 5px;  font-size: 12px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
    /*position: absolute; top: 28px;  left: 28px;*/
}
.imgsthumb:hover a{
    /*display: block;*/
}
.imgWall{  display: -webkit-flex;
    display: flex;align-items:center;
    flex-wrap: wrap;
}
#picShow{
    position: fixed;
    display: inline-flex;
    justify-content: center;
    border: 1px solid #aaa;
    background: rgba(7, 7, 7, 0.64);
    z-index: 100009;  width: 100%;
    height: 100%;  text-align: center;
    vertical-align: middle;  top: 0;
    left: 0;  align-items: center;
}
#picShow img{
    margin-top:30px;
}
#uploadCard{
    display:inline-block;
}
.bussnessCard{
    display: inline-block;
}
.bussnessCard .filePic{
    display:inline-block;
    width: 260px;
    height: 150px;
    background-size: contain;
    background-repeat: no-repeat;
}
.flex-box{
    display: flex;
    justify-content:space-between;
    flex-wrap: wrap;
}
.flex-box>div{
    margin-bottom: 12px;
}
.flex-box .fieldSm{
    width:150px;
}
.flex-box .fieldMid span{
    margin-right: 20px;
}
#addMoreContact option[disabled]{
    background: #ddd;cursor: pointer;
}
.definedCon{
    position: relative;margin: auto;
    text-align: center;  width: 300px;
}
.definedCon .ttl{
    font-weight:600;
}
.definedCon input{
    padding-right:10px;width:100%;
}
.clearLableText{
    position: absolute;  right: 8px;
    bottom: 6px;  cursor: pointer;
}
.sale_gap{
    margin-right:40px;
}
.see_otherContact,.record_otherContact{
    margin: 20px auto;
}
.see_otherContact tr>td:nth-child(odd), .record_otherContact tr>td:nth-child(odd){
    width:20%;
}
.see_otherContact tr>td:nth-child(even), .record_otherContact tr>td:nth-child(even){
    width: 30%;
}
.contactflex{
    display:flex;padding-left: 30px;
}
.contactflex > div{
    line-height: 32px;
}
.gap{
    margin-right:4%;
}

#classifiedGoods td, #normalMaterial td{
    font-size: 11px!important;
}

.main80 .fa{ color: #0b94ea; margin-right: 15px; }
.main44{ padding: 30px 0; }
.main80{
    width: 80%;
    overflow: hidden;
    margin: 20px auto;
}
.main33{
    margin:40px 0;
}
.main99{display: none;}
#codeRepeatTip{ width: 700px; }

.tyable{
    width: 100%;
}
.tyable td{
    padding:4px 20px;
}
.tyable td.hrLine{
    border-bottom: 1px solid #ccc;
}
.tyable td.pad{
    padding:20px 20px;
}
.tyable td.pad2{
    padding-bottom:20px;
}
.tyable .inputC{
    display: block;
    position: relative;
}
.tyable .inputC input{
    padding: 3px 20px 3px 8px!important;
}
.tyable .inputC .fa-close{
    position: absolute;
    right: 7px;
    top: 8px;
    color: #ddd;
}
.tyable .inputC .fa-close:hover{
    color: #aaa;
}
.faGroup1{
    display: inline-block;
    width:200px ;
}
.faGroup1 .fa{
    color: #0b94ea;
    display: inline-block;
    margin-right: 5px;
}
.litBlue{
    font-size: 0.8em;
    color: #0b94ea;
}
.lineC{
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    padding:20px;
    margin:20px 0;
}
.flexdc{
    display: flex;
}
.flexdc>div{
    margin-left: 20px;

}
.flexdc .form-control{
    width: 80px;

}
#date1{
    display: inline-block;
    width: 166px;
}
.faI{
    display: inline-block;
    margin-right: 20px;
    cursor: pointer;
}
.faI .fa{
    color: #0b94ea;
    margin-right: 10px;
}
#price11, #price21{
    width: 150px;
}
.otherContact{
    overflow-y: hidden;
}
.otherContact li{
    float:left;
    margin-bottom:10px;
    width:338px;
}
.otherContact .gap{
    margin-right: 10px;
}
#newContectData .addMore{margin-left: 8px;margin-right: 19px;}

.row-flex{
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}
.btn-group{
    flex: auto;
    text-align: right;
}
#newContractInfo .link-blue {
    padding: 1px 8px 2px 8px;
}


.firstTimeCCon .fa{
    color: #0c9587; margin-left: 20px; margin-right:10px ; font-size: 17px;
}

.firMonthcon{ display: none; }


