<template>
	<div class="uni-container">
		<Pageheader :title="offName" :showBack="false">
				<div @click="bigShare" class="share"></div>
		</Pageheader>
    <div class="live-panel live-tr" >
      今天是{{ curDate }} 星期{{ weekDay }}
    </div>
    <div class="live-panel " v-if="user.roleCode !== 'staff'">
      <div class="live-tr">
        <div @click="goPage('memberList')">
          <span class="icon timer"></span>团队成员管理
          <el-icon class="live-right relaRight">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
    </div>
    <div class="live-panel live-pad" v-if="user.roleCode !== 'staff'">
      <div class="live-tr live-tr-line">
        <div @click="goPage('lyricTask')">
          <span class="icon gequ"></span>歌曲管理
          <el-icon class="live-right relaRight" >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-tr live-tr-line">
        <div @click="goPage('lyricTaskXiQu')">
          <span class="icon xiqu"></span>戏曲管理
          <el-icon class="live-right relaRight" >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-tr">
        <div @click="goPage('zimuSet')">
          <span class="icon zimu"></span>字幕设置
          <el-icon class="live-right relaRight" >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
    </div>

	</div>
</template>
<script>
	import { ttAassistantSharing } from '@/api/api.js'
  import JSONBig from 'json-bigint'
  import auth from '../../sys/auth'


export default {
	data() {
		return {
			memberUrl: '/pages/manage/memberList/memberList',
			shareCbUrl: '/pages/startPage/startPage',
			user: "",
			curDate: "",
			weekDay: "", 
			shareParams:"",
      offName:"",
			isShowData:true
		};
	},
	mounted() {
		this.weekDay = "日一二三四五六".charAt(new Date().getDay()); 
		this.curDate = (new Date()).format("yyyy年M月d日")

    this.user = auth.getUser()
    const officeItem = auth.getOrg()
    this.offName = officeItem.name
    this.postMessage()
    // this.getShareData()
	},
	methods: {
    goPage(url){
      console.log('$router.push goPage=', url)
      this.$router.push({name: url});
    },
    bigShare(){
      // console.log('要跳转 bigShare 了 shareTtAppMes000000',  this.shareParams)
      // tt.miniProgram.redirectTo({
      //   url: `/pages/start/start?${ this.shareParams}`, //demo示例，一般路径形式：/pages/detail/detail?key=${value}
      //   success(res) {
      //     console.log('跳转到 小程序的分享页');
      //     console.log(res);
      //   },
      //   fail(res) {
      //     console.log('跳转到 小程序的分享页 失败');
      //     console.log("navigateTo调用失败");
      //   },
      // });
    },
    getShareData(){
      const userInfoSys =  auth.getUser()
      let sendDate = {
        subOrg:userInfoSys.oid,
        accId:userInfoSys.accId,
        type:1,
        module:"assistant",
      }
      ttAassistantSharing(sendDate).then(res => {
        let ddata = res.data.shareTtAppMes
        console.log('ttAassistantSharing response', ddata)
        this.shareParams = `shareTime=${ ddata.shareTime }&shareName=${ ddata.shareName }&type=1&oid=${ userInfoSys.oid }`
      }).catch(res =>{
        console.log('ttAassistantSharing error', res)
      });
    },
    postMessage(){
      console.log('pppppostMsg')
      // tt.miniProgram.postMessage({
      //   data: {
      //     msg: "postMessage1",
      //   },
      //   // success 回调表示把 data 数据存储成功。
      //   // 存储成功的数据会在小程序后退、组件销毁、页面分享时，通过 bindmessage 发送给用户
      //   success(res) {
      //     console.log("网页消息");
      //   },
      //   // fail 回调表示没有把 data 数据存储成功
      //   fail(err) {
      //     console.log(err);
      //   },
      // });
      console.log('pppppostMsg2')
    }
	}
};
</script>

<style scoped>
.houseSty8{
  position: relative;
  top: 7px;
}
 .mar30{ margin-top: 30px; }
 .share{
    width: 25px;
    height: 25px;
    background-image: url("../../../../liveMiniApp/src/assets/img/share.png");
    background-size: 100%;
    background-repeat: no-repeat;
    margin:8px 10px 0 5px;
 }
 .timer{ background: url('../../../../liveMiniApp/src/assets/img/tuanduichengyuan.png') no-repeat; }
 .gequ{ background: url('../../../../liveMiniApp/src/assets/img/xiqu.png') no-repeat; }
 .xiqu{ background: url('../../../../liveMiniApp/src/assets/img/xiqu.png') no-repeat; }
 .zimu{ background: url('../../../../liveMiniApp/src/assets/img/zimushezhi.png') no-repeat; }

 .icon{
   width: 20px;
   height: 20px;
   display: inline-block;
   background-size: 20px;
   margin-right: 10px;
   position: relative;
   top:5px;
 }
</style>
