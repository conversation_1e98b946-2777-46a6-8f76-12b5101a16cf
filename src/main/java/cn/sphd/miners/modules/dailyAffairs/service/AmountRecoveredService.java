package cn.sphd.miners.modules.dailyAffairs.service;

import cn.sphd.miners.modules.finance.entity.FinanceReceipt;
import cn.sphd.miners.modules.finance.entity.PoLoan;
import cn.sphd.miners.modules.loan.entity.LoanBiz;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2022/12/30
 */
public interface AmountRecoveredService {

    List<Map<String,Object>> getPoLoanList(Integer org, Integer toUser, Integer fromUser, String approveStatus, Integer business, Integer businessType);

    Map<String,Object> getPoLoanDetail(Integer poLoanId);

    Map<String,Object> amountRecoveredApproval(User user, Integer poLoanId, String method, String money, Integer financeAccountId, String oppositeCorp,
       String receiveAccountDate, String receiveDate, String expireDate, String originalCorp, String bankName, String returnNo);

    List<Map<String,Object>> getRecoveredList(Integer org,String businessType,Integer business);

    Map<String,Object> getRecoveredDetail(Integer recoveredId);
}
