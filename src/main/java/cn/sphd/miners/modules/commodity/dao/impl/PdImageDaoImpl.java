package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdImageDao;
import cn.sphd.miners.modules.commodity.entity.PdImage;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Repository
public class PdImageDaoImpl extends BaseDao<PdImage, Serializable> implements PdImageDao {
    @Override
    public PdImage getByProductAndCategory(Integer product, Integer category) {
        String sql="from PdImage where product=:product and category=:category ";
        Map<String,Object> map=new HashMap<>();
        map.put("product",product);
        map.put("category",category);
        PdImage image= (PdImage)getByHQLWithNamedParams(sql,map);
        return image;
    }
}
