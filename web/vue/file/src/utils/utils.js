export function fullScreenElement(doc) {
    doc = doc ?? document
    console.log('fullScreenElement',doc)
    return doc.fullscreenElement
        || doc.webkitFullscreenElement
        || doc.mozFullScreenElement
        || doc.webkitCurrentFullScreenElement
}
export function getHtmlFontSize(obj) {
    let htmlFontSize = getComputedStyle(obj)['font-size']
    return parseInt(htmlFontSize.slice(0, htmlFontSize.indexOf('px')))
}

export function isWeixinBrowser() {// 判断是否为微信浏览器
    console.log('navigator.userAgent', navigator.userAgent)
    let ua = navigator.userAgent.toLowerCase();
    return /micromessenger/.test(ua) ? true : false;
}

export function getDeviceType(){
    console.log("getDeviceType", navigator.userAgent)
    if (isAppleDevice()) {
        console.log('getDeviceType is IOS')
        return "IOS";
    } else if (isHarmonyOS()) {
        console.log('getDeviceType is HarmonyOS')
        return "HarmonyOS";
    } else if (isAndroidDevice()){
        console.log('getDeviceType is Andriod')
        return "Andriod";
    } else {
        console.log('getDeviceType is unkown')
        return "Andriod";
    }
}
// js判断是否是苹果设备
function isAppleDevice() {
    var u = navigator.userAgent
    var ios = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
    var iPad = u.indexOf("iPad") > -1
    var iPhone = u.indexOf("iPhone") > -1 || u.indexOf("Mac") > -1
    if (ios || iPad || iPhone) {
        return true
    } else {
        return false
    }
}
//js判断是否为Android设备
function isAndroidDevice() {
    var u = navigator.userAgent
    if (u.indexOf("Android") > -1 || u.indexOf("Adr") > -1) {
        return true
    } else {
        return false
    }
}
//js判断是否为鸿蒙系统 chos是鸿蒙webview的标识
function isHarmonyOS() {
    var u = navigator.userAgent
    if (u.indexOf("ohos") > -1) {
        return true
    } else {
        return false
    }
}