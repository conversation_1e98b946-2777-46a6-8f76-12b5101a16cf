.suspendSup, .mainCon2,.mainCon3,.mainCon4,.mainCon5, .hd{ display:none;  }
.mainCon>div{ border:1px solid #ddd;border-radius:2px;  }
div.leCat{ width: 250px; min-height:600px;   float: left; color:#0b94ea; line-height:30px; position: relative; padding-bottom: 80px;   }
div.leCat:hover{ cursor: default;  }
.leCat>.bottom{position: absolute; bottom:0; width:100%;   }
.leCat>div{ color: #333; padding-left:15px; font-weight: bold; background: #c0d3ea;    }
.leCat>ul>li{ padding-left:25px;font-size:13px; line-height: 25px;    }
.leCat>ul>li>.name{ white-space: nowrap; text-overflow: ellipsis; overflow: hidden; word-break: break-all; width:160px; display:inline-block;     }
.leCat>ul>li:hover{ background: #cdddea; font-weight: bold; color: #0f62ea;   }
.btnCat{ padding-right:15px; color: #0f62ea; font-weight: bold; }
.btnCat:hover{ text-decoration:underline; cursor: default; }
.catNum{ float: right; padding-right:15px;  }
.lePad{ padding-left:40px;  }
.topPad{ padding-top:30px;  }
.txt-right{ text-align: right;  }
.fixDis{ display: flex;  }
.fixDis>span{ flex:1;  }



div.riMt{ margin-left:255px; padding:15px; min-height:600px;    }
thead{ background: #f7e5baa6;  }
.search{ float:right;  }
.search>input{ height:30px; padding:0 5px; border: 1px solid #ccc; border-right:none;
    border-radius:4px 0 0 4px; width:250px;  }
.search>span{ display:inline-block;height:30px; float:right; border: 1px solid #ccc; cursor: default;
    background: #0b94ea; color:#fff; line-height:25px; padding:0 10px; border-radius:0 4px 4px 0; }

.item{ line-height:35px; }
.red{ color:red; }
.item-title{ display:inline-block; width:80px; text-align:center; }
input,select{ width:185px;  }

.singepannel{ padding:15px 15px 15px 20px;  }
.brCon{ border-top:1px solid #ccc; margin:15px 0; padding:15px 0; }
table{ margin-top:15px; }
.ctrlGp>span{  }
.ctrlGp>span.ty-color-gray{ color:#aaa; }
#mtScan{ min-width:950px;   }

#addMtSup{ width:1215px;  }
.nextCon{ border-top:1px solid #ccc; padding:15px 50px;  }
#fixedScan .fa, #addMtSup .fa{ color:#48cfad ;    }

/*1.88 */
#addSupply i.ccc{ color:#ccc; }
#addSupply .threeCon>p { padding:5px 20px 5px 0;  }
#addSupply .threeCon>p:hover { background:#eaeaea;   }
#addSupply .threeCon .leMar { padding-left:50px;  }

.supInfoTip{ border: 1px solid #ccc; margin: 5px 50px; padding: 5px; border-radius: 4px; font-size:13px;  }
.tipBlue{ color:#0b9df9; width: 240px!important;  }
.stable,.supInfo,.canInvoice2,.incoiceType2{ display: none; }
.supInfo>.purUnit{width:55px; height: 30px; overflow: hidden; line-height: 30px; position: relative; top: 10px; left: 10px; }
.supGreen, .supOrange, .supBlue{ width: auto!important; padding-right:0!important;   }
.supGreen{ color:#5d9285;  }
.supOrange{ color:#f58410;}
.supBlue{ color:#5d9cec; }
.fa[disabled]{ color:#7b7979; }
.radioCon[disabled]{ background:#ddd;  }
p>span.widthAuto{ width: auto}
.leMar{ padding-left: 50px; }

.threeCon span.radioCon{ width:auto; text-align: left; padding-left:15px;     }
.threeCon p>span{ width:150px; text-align:right; display:inline-block;padding-right:18px;    }
.threeCon i{ font-style: normal;    }

.persentSto{  }
.persentSto td{ text-align: center; width:50px; border: 1px solid #ccc;    }
.persentSto .td1{ background:orange;  color:#fff; line-height:50px; border-color:orange;   }
#initStock, #storeHouse{ width:800px;  }
#addPurchase .fa, #addSupply .fa{ color:#48cfad ;    }
#addSupply{ width: 1130px;  }
.xing{ color:red;   }
#addSupply input[disabled] {  background: #eaeaea;}
#addSupply i.fa[disabled]{ color:#aaa;  }
#addSupply .bonceCon{ max-height: 400px; overflow-y: scroll;   }
#addSupply .radioCon[disabled]{ background:#ddd;  }

.editTip{ margin:10px;border:1px solid #ccc; border-radius:3px; padding:10px 15px;    }

.bor3{ border-top: 1px solid #ccc; padding-top: 25px; margin-left: -50px; margin-right: -50px; }


.fileCon{
    margin-bottom: 10px;border: 1px solid #ddd;height: 30px;
    border-radius: 2px;background: #fff;line-height: 30px;padding: 0 9px;
}

.gap {  margin: 10px 0;  }
.supKuItem{ border:1px solid #ccc; border-radius:5px; margin-top:15px; padding:10px; width: 770px;  }
.supKuItem div p{ line-height:25px;  }
.supKuItem>div{display:flex;   }
.supKuItem>div>div{ flex:1;   }
#onlyNumInfo{ padding:25px;  }

.ty-table tbody td.td-orange {background-color: #f4b084;text-align: center;}
.pt-table tbody td.td-orange {background-color: #f4b084;text-align: center;}

.book{border: 1px solid #ddd;background: #eee;}

.linkBtn:hover {color: #0482ea;}

.linkBtn {color: #036bc0;line-height: 30px;padding: 0 20px;cursor: default;}
div.leMar {margin-bottom: 10px;}
.flexBox{width: 100%;box-sizing: border-box;border-radius: 2px;position: relative;font-size: 13px;overflow: hidden;opacity: 1;display: flex;align-items: center;
    justify-content: space-between;transition: opacity .2s;}
.bounce_Fixed2 , .bounce_Fixed3 , .bounce_Fixed4 {  position: fixed;  display: none;  background: rgba(100, 100, 100, 0.5);  width: 100%;  padding-bottom: 200px;  }
.bounce_Fixed2   { z-index: 10002;  }
.bounce_Fixed3 { z-index: 10003;  }
.bounce_Fixed4 { z-index: 10004;  }
.blueCare{color: #0085db;}
.intend{
    text-indent: 28px;
}
.placeList {
    margin:20px auto;
    width:90%;
}
.placeList>li{
    list-style: none;
    background:#fff;
    line-height:30px;
    padding:0 15px;
}
.placeList>li:nth-child(even){
    background:#f5f5f5;
}
.placeList .fa{
    margin-right: 20px;
}
.taxWrap{margin-left: 40px}
.manageAddress{padding-left: 234px;display: none;width: 848px;}
.manageAddress >span{width: 182px;}
.manageAddressList,.manageAreaList{min-height: 50px;line-height: 32px;}
.wideBody{margin: auto;width: 80%;}
.gapR{margin-right: 40px;}
.sale_gap{margin-right: 40px;}
#newContectData .addMore{margin-left: 8px;margin-right: 19px;}
.otherContact{overflow-y: hidden; }
.otherContact li{float:left;margin-bottom:10px;width:332px;}
.otherContact li .sale_gap{margin-left: 4px;margin-right: 8px;}
#chooseCusContact .fa{ color: #50a9ea;    }
.sale-con{padding-left:10px;margin-right:20px;min-width: 142px;display: inline-block;line-height:32px;}
.see_otherContact {margin: 20px auto;}
.sale_ttl1{margin-right: 10px;display: inline-block;min-width: 90px;text-align: right;line-height: 32px;vertical-align: top;}
.sale_con1>input{width: 220px;}
.chooseCusCon{margin-right: 16px;}
.chooseCusBtn{ background: #ddd; width: 40px; display:inline-block; text-align: center; line-height: 30px; height: 30px; margin-left: -5px;  }
.linkUploadify .uploadify-button { margin: 0; background-color: inherit; border: none;  line-height: 12px;  padding: 3px 6px;  font-size: 12px;  color: #1685ea;  cursor: pointer;  text-decoration: none;
    font-weight: normal;  display: inline-block;  vertical-align: middle;  }
.linkUploadify .uploadify-button:active{border: none;}
#newContectInfo .uploadify-button{line-height: 28px;}
.uploadify-button:visited{text-decoration: none}
#uploadCard{display:inline-block;}
#ReceiveName,#receiveAreaName {height: 30px;width: 181px;line-height: 30px;}
.bussnessCard{display: inline-block;}
.bussnessCard .filePic{display:inline-block;width: 260px; height: 150px; background-size: contain;  background-repeat: no-repeat;}
.cusList { margin:20px auto; width:90%;   }
.cusList>li{ list-style: none; background:#fff; line-height:30px; padding:0 15px;  }
.cusList>li:nth-child(even){ background:#f5f5f5;  }
.contactflex{display:flex;padding-left: 30px;}
#newReceiveInfo .linkBtn{padding: 0;}
.taxRateBody{position: relative;display: inline-block;}
.taxRateList{position: absolute;top: 30px;display: none;width: 120px;background:#fff;max-height: 150px;left: 0;border: 1px solid #a8a6a6;}
.taxRateList > span{display: inline-block;width: 100%;}
.taxRateList > span:hover{background: #3398fe;color: #fff;}
.taxRateList > span >span:first-child{width: 74px;display: inline-block;padding: 4px 20px 4px 20px;}
.taxRateList > span >span:last-child{color: red;font-size: 12px;padding: 4px 8px;}
.taxRateBody input{width: 120px;}
.areaForm {margin: auto;width: 80%;}
.areaForm p span:first-child{width: 180px;text-align: left;}
.clearVal{position: relative;}
input:focus + i.clearInputVal{  display: block;  }
.clearVal .clearInputVal{position: absolute;  display: none;right: 8px;font-style: normal;color: #7a7b7e;top: -1px;cursor: pointer;}
#addMoreContact option[disabled]{background: #ddd;cursor: pointer;}

.flex-box{display: flex;justify-content:space-between;flex-wrap: wrap;}
.flex-box>div{margin-bottom: 12px;}
.flex-box .fieldSm{width:150px;}
.flex-box .fieldMid span{  margin-right: 20px;  }
.row-flex {display: flex;align-items: center;margin-bottom: 8px;}
.btn-group {text-align: right;flex: auto;}
.btn-group {display: block;}
#newContractInfo .link-blue {padding: 1px 8px 2px 8px;}
.fileCon .fileIm .fa {margin: 0 8px;color: #5d9cec;}










