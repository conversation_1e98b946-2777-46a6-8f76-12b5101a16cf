package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingMaterial;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestion;

import java.util.List;

/**
 * @ClassName RespTrainQuestionList
 * @Description 试题列表
 * <AUTHOR>
 * @Date 2021/2/18 14:46
 * @Version 1.0
 */
public class RespTrainQuestionList {
    private Integer status;
    private TrainingMaterial trainingMaterial;
    private List<TrainingQuestion> trainingQuestionList;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public TrainingMaterial getTrainingMaterial() {
        return trainingMaterial;
    }

    public void setTrainingMaterial(TrainingMaterial trainingMaterial) {
        this.trainingMaterial = trainingMaterial;
    }

    public List<TrainingQuestion> getTrainingQuestionList() {
        return trainingQuestionList;
    }

    public void setTrainingQuestionList(List<TrainingQuestion> trainingQuestionList) {
        this.trainingQuestionList = trainingQuestionList;
    }
}
