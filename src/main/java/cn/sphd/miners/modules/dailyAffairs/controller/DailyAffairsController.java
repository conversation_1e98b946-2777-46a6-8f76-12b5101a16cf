package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.complaint.service.ComplaintService;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.dailyAffairs.service.CollectWindowService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.personal.entity.PersonnelLeave;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.personal.service.*;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.sales.service.SaleService;
import cn.sphd.miners.modules.sales.service.SalesReceivableService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.PopedomTask;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgPopedomService;
import cn.sphd.miners.modules.system.service.PopedomService;
import cn.sphd.miners.modules.system.service.PopedomTaskService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2018/9/13.
 */
@Controller
@RequestMapping("/daily")
public class DailyAffairsController {
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    LeaveService leaveService;
    @Autowired
    OvertimeService overtimeService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    PopedomService popedomService;
    @Autowired
    ResService resService;
    @Autowired
    SaleService saleService;
    @Autowired
    CollectWindowService collectWindowService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    SalesReceivableService salesReceivableService;
    @Autowired
    ComplaintService complaintService;

    /**
     * <AUTHOR>
     * @date 2019/09/05
     * 跳转日常事务
     */
    @RequestMapping("/dailyAffairs.do")
    public String dailyAffairs() {
        return "/dailyAffairs/dailyAffairs";
    }


    //    /**
//    * <AUTHOR>
//    * @Date 2018/9/19 10:58
//     * 跳转 角标与处理分类  js形式页面传值
//     */
//    @RequestMapping("/getCornerMarkerProcessing.do")
//    public ModelAndView getCornerMarkerProcessing(Integer userId) throws IOException {
//        User user=userService.getUserByID(userId);
//        ModelAndView map = new ModelAndView("/dailyAffairs/cornerMarkerProcessing");
//        map.addObject("applyList", arrayBillItem);
//        map.addObject("approvalList",JSON.toJSONString(approvalList));
//        return map;
//    }
    @ResponseBody
    @RequestMapping("/getCornerMarkerProcessing.do")
    public void getCornerMarkerProcessing(User user, HttpServletResponse response) throws IOException {
        System.out.println("悬浮窗内菜单开始：" + new Date().getTime());
        List<PopedomTask> popedomTaskList = popedomTaskService.getCornerMarkerProcessing(user.getUserID());//我的悬浮窗
        Map<String,Object> map=new HashMap<>();
        map.put("popedomTaskList", popedomTaskList);
        ObjectToJson.objectToJson1(map, new String[]{}, response);
        System.out.println("悬浮窗内菜单结束：" + new Date().getTime());

    }


    /**
     * <AUTHOR>
     * @Date 2018/9/20 10:17
     * 加班申请 待处理 已批准 已驳回
     */
    @ResponseBody
    @RequestMapping("/applyOutTime.do")
    @MessageMapping("/applyOutTime")
    public JsonResult applyOutTime(User user,String sessionid) {
//        JSONObject jsonObject = JSONObject.parseObject(json);
//        String sessionid = jsonObject.getString("session");
//        Integer userId = jsonObject.getInteger("userId");
        Integer userId=user.getUserID();
        List<PersonnelOvertime> untreated = overtimeService.getPersonnelOvertimeListByUser(user.getOid(),userId,null, 1, null, null);
        List<PersonnelOvertime> approved = overtimeService.getPersonnelOvertimeListByUser(user.getOid(),userId, null,2, null, null);

        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/applyOutTimeHandle", null, null, null, null, JSON.toJSONString(untreated));//推送 待处理
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/applyOutTimeApproval", null, null, null, null, JSON.toJSONString(approved));//推送 已批准
        Map<String, Object> map = new HashMap<>();
        map.put("untreated", untreated);
        map.put("approved", approved);
        return new JsonResult(1, map);
    }

    /**
     * <AUTHOR>
     * @Date 2018/9/20 10:31
     * 请假申请  待处理 已批准 已驳回
     */
    @ResponseBody
    @RequestMapping("/applyLeave.do")
    @MessageMapping("/applyLeave")
    public JsonResult applyLeave(User user,String sessionid) throws ParseException {
//        JSONObject jsonObject = JSONObject.parseObject(json);
//        String sessionid = jsonObject.getString("session");
//        Integer userId = jsonObject.getInteger("userId");
        Integer userId=user.getUserID();

        List<PersonnelLeave> untreated1 = leaveService.getPersonnelLeaveListByUser(userId,user.getOid(), "1", "0", null, null,null);  //请假待处理
        List<PersonnelLeave> untreated2 = leaveService.getPersonnelLeaveListByUser(userId,user.getOid(),"1", "1", null, null,null);  //提前结束请假待处理
        List<PersonnelLeave> untreated = new ArrayList<>();
        if (untreated1.size() > 0) {
            untreated.addAll(untreated1);
        }
        if (untreated2.size() > 0) {
            untreated.addAll(untreated2);
        }
        for (PersonnelLeave leave:untreated) {
            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));
        }
        List<PersonnelLeave> approved = leaveService.getPersonnelLeaveListByUser(userId,user.getOid(), "2", null, null, null,null);   //已批准
        for (PersonnelLeave leave:approved) {
            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));
        }

        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/applyLeaveHandle", null, null, null, null, JSON.toJSONString(untreated));//推送 待处理
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/applyLeaveApproval", null, null, null, null, JSON.toJSONString(approved));//推送 已批准
        HashMap<String, Object> map = new HashMap<>();
        map.put("untreated", untreated);
        map.put("approved", approved);
        return new JsonResult(1, map);
    }


    /**
     * <AUTHOR>
     * @Date 2018/9/20 14:26
     * 加班审批 待处理 已批准 已驳回
     */
    @ResponseBody
    @RequestMapping("/approvalOutTime.do")
    @MessageMapping("/approvalOutTime")
    public JsonResult approvalOutTime(User user,String sessionid) {
//        JSONObject jsonObject = JSONObject.parseObject(json);
//        String sessionid = jsonObject.getString("session");
//        Integer userId = jsonObject.getInteger("userId");
        Integer userId=user.getUserID();
        List<PersonnelOvertime> untreated1 = overtimeService.getPersonnelOvertimeByHandle(userId,null, "1", 2,null);        //计划加班待处理
        List<PersonnelOvertime> untreated2 = overtimeService.getPersonnelOvertimeByHandle(userId,null, "1", 6,null);       //申报加班待处理

        List<PersonnelOvertime> untreated = new ArrayList<>();
        untreated.addAll(untreated1);
        untreated.addAll(untreated2);

        List<PersonnelOvertime> approved = overtimeService.getPersonnelOvertimeByApproval(userId,null,null);        //计划加班已批准
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/approvalOutTimeHandle", null, null, null, null, JSON.toJSONString(untreated));//推送 待处理
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/approvalOutTimeApproval", null, null, null, null, JSON.toJSONString(approved));//推送 已批准
        HashMap<String, Object> map = new HashMap<>();
        map.put("untreated", untreated);
        map.put("approved", approved);
        return new JsonResult(1, map);

    }

    /**
     * <AUTHOR>
     * @Date 2018/9/21 10:49
     * 请假审批 待处理 已批准 已驳回
     * approvalStatus 1-待处理 2-批准 3-驳回
     */
    @ResponseBody
    @RequestMapping("/approvalLeave.do")
    @MessageMapping("/approvalLeave")
    public JsonResult approvalLeave(User user,String sessionid) {
//        JSONObject jsonObject = JSONObject.parseObject(json);
//        String sessionid = jsonObject.getString("session");
//        Integer userId = jsonObject.getInteger("userId");  //审批人id

        Integer userId=user.getUserID();
        List<PersonnelLeave> untreated1 = leaveService.getPersonnelLeaveByApproval(user.getOid(),userId, "1", 3);  //请假待处理
        List<PersonnelLeave> untreated2 = leaveService.getPersonnelLeaveByApprovalAdvance(userId, "1", 8);  //提前结束请假待处理
        List<PersonnelLeave> untreated = new ArrayList<>();
        if (untreated1.size() > 0) {
            untreated.addAll(untreated1);
        }
        if (untreated2.size() > 0) {
            untreated.addAll(untreated2);
        }
        for (PersonnelLeave leave:untreated) {
            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));
        }
        List<PersonnelLeave> approved = leaveService.getPersonnelLeaveByApprovalState(userId);  //已批准
        for (PersonnelLeave leave:approved) {
            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/approvalLeaveHandle", null, null, null, null, JSON.toJSONString(untreated));//推送 待处理
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/approvalLeaveApproval", null, null, null, null, JSON.toJSONString(approved));//推送 已批准
        HashMap<String, Object> map = new HashMap<>();
        map.put("untreated", untreated);
        map.put("approved", approved);
        return new JsonResult(1, map);
    }

    /**
     * <AUTHOR>
     * @Date 2018/10/31 10:03
     * 我的消息初始  值
     */
    @ResponseBody
    @RequestMapping("/myMessages.do")
    @MessageMapping("/myMessages")
    public JsonResult myMessages(String sessionid, User user/*, String json*/) {
//        JSONObject jsonObject = JSONObject.parseObject(json);
//        String sessionid = jsonObject.getString("session");
//        Integer userId = jsonObject.getInteger("userId");
//        System.out.println("我的消息开始：" + new Date());
//        List<UserSuspendMsg> userSuspendMsgs = userSuspendMsgService.getUserSuspendMsgListByUserId(userId);
        List<UserSuspendMsg> userSuspendMsgs = userSuspendMsgService.getUserSuspendMsgListByUserId(user.getUserID());
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userSuspendMsgs", userSuspendMsgs);
        hashMap.put("superscript", userSuspendMsgs.size());
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/message", null, null, null, null, JSON.toJSONString(userSuspendMsgs));//推送 已批准
//        System.out.println("我的消息结束：" + new Date());
        return new JsonResult(1, hashMap);
    }

    /**
     * <AUTHOR>
     * @Date 2018/10/30 16:54
     * 查看消息 变更为已读
     */
    @ResponseBody
    @RequestMapping("/updateUserSuspendMsg.do")
    @MessageMapping("/updateUserSuspendMsg")
    public JsonResult updateUserSuspendMsg(String json,User user) {
        JSONObject jsonObject = JSONObject.parseObject(json);
//        Integer userId = jsonObject.getInteger("userId");
        Integer messageId = jsonObject.getInteger("messageId");
        if (user==null||messageId==null) {
            System.out.println("发送方式调用消息变已读：updateUserSuspendMsg 。 json：" + json);
        }
        userSuspendMsgService.updateUserSuspendMsg("2", user.getUserID(),messageId);
        return new JsonResult(1, 1);
    }


    @ResponseBody
    @RequestMapping("/test.do")
    public void test(HttpServletResponse response) throws ParseException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain; charset=utf-8");
        response.setHeader("Cache-Control", "no-store");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("expires", -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date a = sdf.parse("2018-03-31 18:00:00");

        Calendar calendar = Calendar.getInstance();
        Date nextRunTime;
        int i = 1;
        do {
            calendar.setTime(a);
            calendar.add(Calendar.MONTH, i); // 月份再调至下个月；
            nextRunTime = calendar.getTime();
            String bb = sdf.format(nextRunTime);
            i++;
            System.out.println("");
        } while (!NewDateUtils.getDate(a).equals(NewDateUtils.getDate(nextRunTime)));// 这月提醒 日期那天 不等于 下月提醒日期那天
        String aa = sdf.format(nextRunTime);
        System.out.println();
    }

    @ResponseBody
    @RequestMapping("/getUserBadgeNumbers.do")
    @MessageMapping("/getUserBadgeNumbers")
    public JsonResult getDailyMenuNumber(User user) {
        HashMap<String, Object> userBadgeNumbers = popedomTaskService.getUserBadgeNumbers(user.getUserID());
        clusterMessageSendingOperations.convertAndSendToUser(String.valueOf(user.getUserID()), "/getUserBadgeNumbers", null, null, null, null, JSON.toJSONString(userBadgeNumbers));//推送 各种菜单角标数量
        return new JsonResult(1, userBadgeNumbers);
    }

    @ResponseBody
    @RequestMapping("/renewuser.do")
    public JsonResult reNewUser(Integer uid, String code, Integer change, String sessionid) {
        HashMap<String,Object> result = new HashMap<>();
        Long operatortime = System.currentTimeMillis() + TimeUnit.DAYS.toMillis(3);
        redisTemplate.opsForValue().set("miners:operatortime:" + sessionid, operatortime, 3, TimeUnit.HOURS);//设置缓存3小时失效
        redisTemplate.opsForValue().set("miners:logout:"+sessionid,operatortime,3, TimeUnit.HOURS);//wyu：3小时过期
        User u =  userService.getUserByID(uid);
        popedomTaskService.updateUserBadgeNumber(u);
        HashMap<String, Object> userBadgeNumbers1 = popedomTaskService.getUserBadgeNumbers(uid);
        result.put("userBadgeNumbers1",userBadgeNumbers1);
        if(!MyStrings.nulltoempty(code).isEmpty()&&change!=null){
            HashMap<String, Object> userBadgeNumbers2 = popedomTaskService.updateUserBadgeNumberWithCode(u,code,change);
            result.put("userBadgeNumbers2",userBadgeNumbers2);
        }
        return new JsonResult(1, result);
    }


    /**
     * <AUTHOR>
     * @Date 2020/5/22 13:54
     * 1.97 我都消息优化 将要消失的消息
     */
    @ResponseBody
    @RequestMapping("/willDisappearMessages.do")
    public JsonResult willDisappearMessages(User user/*,Integer userId*/){
//        List<UserSuspendMsg> userSuspendMsgs=userSuspendMsgService.getWillDisappearMessages(userId);
        List<UserSuspendMsg> userSuspendMsgs=userSuspendMsgService.getWillDisappearMessages(user.getUserID());
        return new JsonResult(1,userSuspendMsgs);
    }

    /**
     * <AUTHOR>
     * @Date 2020/5/22 13:54
     * 1.97 我都消息优化 多选变已读
     */
    @ResponseBody
    @RequestMapping("/userSuspendMsgsToRead.do")
    public JsonResult userSuspendMsgsToRead(User user,Integer... messageIds){
        userSuspendMsgService.updateUserSuspendMsg("2",user.getUserID(),messageIds);
        return new JsonResult(1, 1);
    }

    /**
     * <AUTHOR>
     * @Date 2020/5/22 13:54
     * 1.97 我都消息优化 多选删除
     */
    @ResponseBody
    @RequestMapping("/deleteSuspendMsgs.do")
    public JsonResult deleteSuspendMsgs(User user,Integer... messageIds){
        userSuspendMsgService.updateUserSuspendMsg("4",user.getUserID(),messageIds);
        return new JsonResult(1, 1);
    }

}