//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package mob.push.api.http;

import cn.sphd.miners.common.initializer.SaveRpcLog;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.rpcLog.entity.RpcLog;
import cn.sphd.miners.modules.rpcLog.service.RpcLogService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import mob.push.api.config.MobPushConfig;
import mob.push.api.exception.ApiException;
import mob.push.api.utils.MD5;
import mob.push.api.utils.MobHelper;
import mob.push.api.utils.SafeUtil;
import okhttp3.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class Http {
    private static final Logger log = LoggerFactory.getLogger("MobPush");
    private static final OkHttpClient CLIENT;
    private static final String WEB_ERROR;
    public static final MediaType JSON_MEDIA = MediaType.parse("application/json; charset=utf-8");

    public Http() {
    }

    public static <T> Result<T> get(String url, Map<String, String> headers, Class<T> cls) {
        ArrayList<String> error = new ArrayList<>();
        if (headers == null) {
            headers = new HashMap();
        }

        ((Map)headers).put("sign", serverSign("", MobPushConfig.appSecret));
        ((Map)headers).put("key", MobPushConfig.appkey);
        Request request = (new Request.Builder()).url(url).headers(Headers.of((Map)headers)).get().build();
        Result response = callAndGet(CLIENT.newCall(request), cls, (String)null, error);
        saveRpcLog(url, headers, null, response, error);
        return response;
    }

    private static <T> Result<T> callAndGet(Call call, final Type cls, final String data, ArrayList<String> error) {
        final CountDownLatch latch = new CountDownLatch(1);
        final Result<T> result = new Result();
        call.enqueue(new Callback() {
            public void onFailure(Call call, IOException e) {
                try {
                    result.setStatus(Result.ERROR);
                    result.setError("网络请求错误: " + e.getMessage());
                    error.add(result.toString());
                    latch.countDown();
                } finally {
                    latch.countDown();
                }

            }

            public void onResponse(Call call, Response response) {
                try {
                    result.setResponseCode(response.code());
                    String res = (String)SafeUtil.nullExecRes(response.body(), ResponseBody::string, Http.WEB_ERROR);
                    Http.log.info("MobRequest response: {}, request: {}", res, data);
                    JSONObject obj = JSON.parseObject(res);
                    Integer status = obj.getInteger("status");
                    if (Result.SUCCESS.equals(status)) {
                        String dataStr = obj.getString("res");
                        T datax = JSON.parseObject(dataStr, cls);
                        result.setRes(datax);
                    } else {
                        result.setStatus(status);
                        result.setError(obj.getString("error"));
                        error.add(result.toString());
                    }
                } catch (Exception var11) {
                    result.setStatus(Result.ERROR);
                    error.add(var11.getMessage());
                    throw var11;
                } finally {
                    latch.countDown();
                }

            }
        });
        SafeUtil.safeExec(latch::await);
        return result;
    }

    public static <T> Result<T> post(String url, Map<String, String> headers, String data, Type cls) throws ApiException {
        ArrayList<String> error = new ArrayList<>();
        Result response = null;
        try {
            if (headers == null) {
                headers = new HashMap();
            }

            ((Map)headers).put("sign", serverSign(data, MobPushConfig.appSecret));
            ((Map)headers).put("key", MobPushConfig.appkey);
            RequestBody body = RequestBody.create(JSON_MEDIA, data);
            Request request = (new Request.Builder()).url(url).headers(Headers.of((Map)headers)).post(body).build();
            response = callAndGet(CLIENT.newCall(request), cls, data, error);
            return response;
        } catch (Exception var6) {
            error.add(var6.getMessage());
            throw new ApiException(var6);
        } finally {
            saveRpcLog(url, headers, data, response, error);
        }
    }

    public static StringResult post(String url, Map<String, String> headers, String data) throws ApiException {
        ArrayList<String> error = new ArrayList<>();
        StringResult response = null;
        try {
            if (headers == null) {
                headers = new HashMap();
            }

            ((Map)headers).put("sign", serverSign(data, MobPushConfig.appSecret));
            ((Map)headers).put("key", MobPushConfig.appkey);
            RequestBody body = RequestBody.create(JSON_MEDIA, data);
            Request request = (new Request.Builder()).url(url).headers(Headers.of((Map)headers)).post(body).build();
            response = callAndGet(CLIENT.newCall(request), data, error);
            return response;
        } catch (Exception var5) {
            error.add(var5.getMessage());
            throw new ApiException(var5);
        } finally {
            saveRpcLog(url, headers, data, response, error);
        }
    }

    public static <T> Result<T> get(String url, Map<String, String> headers, String data, Class<T> cls) {
        ArrayList<String> error = new ArrayList<>();
        if (headers == null) {
            headers = new HashMap();
        }

        ((Map)headers).put("sign", serverSign(data, MobPushConfig.appSecret));
        ((Map)headers).put("key", MobPushConfig.appkey);
        Request request = (new Request.Builder()).url(url).headers(Headers.of((Map)headers)).get().build();
        Result response = callAndGet(CLIENT.newCall(request), cls, (String)null, error);
        saveRpcLog(url, headers, data, response, error);
        return response;
    }

    public static StringResult get(String url, Map<String, String> headers) {
        ArrayList<String> error = new ArrayList<>();
        StringResult response = null;
        if (headers == null) {
            headers = new HashMap();
        }

        ((Map)headers).put("sign", serverSign("", MobPushConfig.appSecret));
        ((Map)headers).put("key", MobPushConfig.appkey);
        Request request = (new Request.Builder()).url(url).headers(Headers.of((Map)headers)).get().build();
        response = callAndGet(CLIENT.newCall(request), (String)null, error);
        saveRpcLog(url, headers, null, response, error);
        return response;
    }

    private static StringResult callAndGet(Call call, final String data, ArrayList<String> error) {
        final CountDownLatch latch = new CountDownLatch(1);
        final StringResult res = new StringResult();
        call.enqueue(new Callback() {
            public void onFailure(Call call, IOException e) {
                try {
                    res.setRes(JSON.toJSONString(Result.newServerError("网络请求错误")));
                    res.setResponseCode(500);
                    error.add(res.toString());
                    latch.countDown();
                } finally {
                    latch.countDown();
                }

            }

            public void onResponse(Call call, Response response) {
                try {
                    res.setRes((String)SafeUtil.nullExecRes(response.body(), ResponseBody::string, Http.WEB_ERROR));
                    res.setResponseCode(response.code());
                    Http.log.info("MobRequest response: {}, request: {}", res, data);
                } catch (Exception var7) {
                    res.setRes(JSON.toJSONString(Result.newError(500, "请求结果处理异常")));
                    res.setResponseCode(500);
                    error.add(res + var7.getMessage());
                    throw var7;
                } finally {
                    latch.countDown();
                }

            }
        });
        SafeUtil.safeExec(latch::await);
        return res;
    }

    public static String serverSign(String decodeData, String appSecret) {
        return MD5.hash(decodeData + appSecret, MobHelper.CHARSET_UTF8);
    }

    public static <R> Result<R> getResult(Map<String, Object> params, R res, String url) {
        params.put("appkey", MobPushConfig.appkey);
        Result<R> result = post(MobPushConfig.baseUrl + url, (Map)null, JSON.toJSONString(params), res.getClass());
        if (!result.success()) {
            throw new ApiException(result);
        } else {
            return result;
        }
    }

    static {
        CLIENT = (new OkHttpClient.Builder()).connectTimeout(10L, TimeUnit.SECONDS).writeTimeout(10L, TimeUnit.SECONDS).readTimeout(10L, TimeUnit.SECONDS).connectionPool(new ConnectionPool(32, 5L, TimeUnit.SECONDS)).build();
        Result<?> webError = new Result();
        webError.setStatus(Result.ERROR);
        webError.setError("网络请求异常");
        WEB_ERROR = JSON.toJSONString(webError);
    }
    private static Pair<String, String> getCallee() {
        StackTraceElement[] stacks = Arrays.stream(Thread.currentThread().getStackTrace()).skip(2).toArray(StackTraceElement[]::new);//跳过 Thread、getCallee
        String packageName = Http.class.getPackage().getName().substring(0, Http.class.getPackage().getName().lastIndexOf('.'));//getClass().getPackage().getName();
        for(StackTraceElement stack : stacks) {
            if(!stack.getClassName().contains(packageName)) {
                return Pair.of(stack.getClassName(), stack.getMethodName());
            }
        }
        return Pair.of(null, null);
    }

    private static Pair<String, String> getCaller() {
        StackTraceElement[] stacks = Arrays.stream(Thread.currentThread().getStackTrace()).skip(3).toArray(StackTraceElement[]::new);//跳过 Thread、getCaller和Http被调用函数
        Pair<String, String> result = null;
        String thisPackageName = "mob.push.api.http";
        String minersPackageName = "cn.sphd.miners";
        for(StackTraceElement stack : stacks) {
            if(result == null && !stack.getClassName().contains(thisPackageName)) {
                result = Pair.of(stack.getClassName(), stack.getMethodName());
            }
            if(stack.getClassName().contains(minersPackageName)) {
                return Pair.of(stack.getClassName(), stack.getMethodName());
            }
        }
        return result == null ? Pair.of(null, null) : result;
    }
    private static void saveRpcLog(String url, Map<String, String> headers, String body, Object response, ArrayList<String> error) {
        RpcLogService rpcLogService = RpcLogService.getRpcLogService();
        if(rpcLogService!=null) {
            Map<String, Object> requestMap = new HashMap<>();
            if(ObjectUtils.isNotEmpty(headers)) {
                requestMap.put("head", headers);
            }
            if(StringUtils.isNotBlank(body)) {
                requestMap.put("body", body);
            }
            String host = null;
            try {
                host = new URI(url).getHost();
            } catch (URISyntaxException e) {
                error.add("mob.push.api.http saveRpcLog getHost error" + e.getMessage());
                log.warn("getHost error", e);
            } finally {
                rpcLogService.saveRpcLog(new RpcLog(
                        SaveRpcLog.Direction.send.getIndex(),
                        getCaller(),
                        getCallee(),
                        host,
                        GetLocalIPUtils.getServerIp(),
                        url,
                        JSON.toJSONString(requestMap),
                        JSON.toJSONString(response),
                        error.stream().collect(Collectors.joining("\n"))));
            }
        }
    }
}