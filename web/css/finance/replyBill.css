*{ font-family: "Microsoft Yahei";   }
td,th{
	vertical-align:middle !important;
	overflow:hidden;white-space:normal; word-break:break-all;text-overflow:ellipsis;
	
 }
.clr{ clear:both;  }
.Con_head{
	font-size: 18px;
	font-weight: 700;

}
.Con_continer{
	padding-top: 20px;
}
.Con_bar{
	padding: 10px 0 10px 5px;
	font-weight: 700;
	

}
.Con_bar>span{
	cursor: pointer;
	padding:0 0 8px 0;
	/*border-bottom: 4px solid #36c6d3;*/
}
.Con_bar>span:hover{
	cursor: pointer;
	padding:0 0 8px 0;
	border-bottom: 4px solid #a8e8ee;
}
.Con_bar>span:nth-child(2){
	margin-left: 12px;
	/*border-bottom: 4px solid #fff;*/
}
.bg{
	background-color: #f1f1f1;
	font-weight: 400;
	font-size: 14px;
}
.bg_bar{
	padding: 8px !important;
	border: 1px solid #e4e4e4 !important;
	width: 140px;
}
.Table_bar{
	margin-top: 30px;
}
.Table_butten>span{
	cursor: pointer;
}
.Table_butten>span:nth-child(2){
	margin:0 10px 0 10px;
}
.Con_bottom{
	padding-left: 50px;
}
.instruction{
	padding: 25px 0;
	width: 500px;
	color: #000;
	margin-left: 10px;
}
.bot_instruction{
	padding: 25px 0;
	border: 1px solid #999;
	width: 400px;
	font-size: 12px;
	color: #333;
}
.bonceContainer{
	width: 600px;
}
.bonceHead>span{
	font-weight: 700;
}
.check_See,.check_bg{
	padding: 5px 0;
}
.check_bg{
	position: relative;
}
/*.check_Seebg,.check_Seebg>input,.check_SeeType{
	background-color: #e4e4e4;
}*/
.check_Seebg,.check_SeeType{
	position: relative;
	z-index: 1;
}
.check_See>span,.check_Seebg>span{
	width: 26.5%;
	display: inline-block;
	text-align: right;
}
.check_SeeType>span:nth-child(1){
	width: 26.5%;
	display: inline-block;
	text-align: right;
}
.check_SeeType{
	padding: 5px 0;
}
.check_SeeType>span:nth-child(2),.check_SeeType>span:nth-child(4),.check_SeeType>span:nth-child(6){
	margin-left: 20px;
}
/*.check_SeeType>input{
	width: 5.5%;
}*/
.check_See>input{
	margin-left: 8px;
	padding: 0 0 0 10px;
	width: 60%;
	height: 35px!important;
	background-color: #fff;
}
.check_Seebg>input{
	margin-left: 8px;
	padding: 0 0 0 10px;
	width: 60%;
	height: 35px!important;
}
.check_See>select{
	margin-left: 8px;
	padding: 0 0 0 6px;
	width: 60.5%;
	height: 35px!important;
	border: 1px solid #ccc;
}
.check_SeeHide,.accept_hide{
	display: none;
}
.bank_Con>span:nth-child(2){
	color: red;
	width: 10px;
	font-size: 16px;
}
.deal_active{
	border-bottom: 4px solid #36c6d3;
}
.deal_Nav{
	border-bottom: 4px solid #fff;
}

/*radioBtn style*/
span.radioBtn{ padding:0 4px; display:inline-block; width:20px; min-width:20px; height:20px;
	border-radius:10px!important; top:0px; background:#fff; position:relative; border:1px solid #ccc;    }
span.radioBtn>.val{ display:none;    }
span.valShow{ width:150px; min-width:150px; border:none; padding:0 5px; line-height:35px;    }
span.radioBtn>.radioShow{ display:inline-block; background:#fff; border-radius:5px!important; width:10px; height:10px;position: relative;
	top: -1px; }
span.radioActive>.radioShow{  background:#9d9d9d; box-shadow:0 0 2px #666;  }
.dataMark{ width: 100%; height:35px; background:rgba(0,0,0,0.2);position: absolute;z-index:1; top:5px; }


.hd{
	display:none;
}

#turnToTip_1 , #turnToTip_2{ text-align: center; color: red; font-size:14px;  }
.addpayDetails{
	max-height: 500px;
	overflow-y:auto;
}
.collectBillDetail:before,.collectBillDetail:after,.chequeDetail:before,.chequeDetail:after{clear:both;content:''; display:block; width:0; height:0; visibility:hidden;}
.collectBillDetail li,.chequeDetail li{float:left;width:45%;padding:10px;}
.subTitle{display: inline-block;min-width:100px;}
.btBlock{margin: 16px 0 36px 0;}
.align-left .ty-table  td{text-align: left;}
.paddT{padding-top:24px;}
.tTtl{padding-left: 14px;}
.ty-btn-size{padding: 0 24px;width: 130px;height: 32px;line-height: 32px;text-align: center;}
.smallSize{margin: auto;width: 80%;}
.upForm ul li {float: left;position: relative;margin-top: 10px;width: 45%;}
.upForm ul li:nth-child(2n+1) {margin-right: 54px;}
.upForm ul li p {padding-left: 10px;}
.bonceCon .upForm ul li input[disabled] {background: #d7d7d7;}
.clearInputVal {position: absolute;  top: 30px;  right: 4px;  color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
input:focus + i.clearInputVal{  display: block;  }
.modItem-s{width: 160px;}
.line{ border-bottom: 1px solid #bfbfbf; width: 100%; margin: 32px auto 20px; }
.upForm ul li.longSize{width:100%;}
.more-size{margin-top: 50px;width: 700px;}
.moreList li{margin-bottom: 20px;}
.moreList li span:nth-child(1){margin-right: 50px;width: 162px;}
.moreList li span:nth-child(2){margin-right: 20px;display: inline-block;width: 150px;}
.moreList li span:nth-child(3){margin-right: 10px;display: inline-block;min-width: 160px;}
.moreY {padding-top: 30px;}
.moreY span:nth-child(1){margin-right: 100px;width: 162px;}
.moreY input{margin-right: 158px;display: inline-block;width: 200px;}
.link-blue{color: #0070c0; font-weight: 700;}
.moreBtn{margin-right: 20px;font-size: 16px;}
.mainCon {margin-left: 100px;margin-right: 60px;display: none;}
.backPage{padding-bottom: 30px;}

