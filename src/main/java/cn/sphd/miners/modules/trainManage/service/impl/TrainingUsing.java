package cn.sphd.miners.modules.trainManage.service.impl;

import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBankAttachment;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBankAttachmentHistory;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBankHistory;
import cn.sphd.miners.modules.trainManage.service.TrainService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import org.springframework.web.context.ContextLoader;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

/**
 * @ClassName TrainingUsing
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/5/13 10:44
 * @Version 1.0
 */
public class TrainingUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;

    @Override
    public boolean checkUsing(String filename) {
        if (StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            TrainService service = ac.getBean(TrainService.class, "trainService");
            switch (entityClass) {
                case "TrainingQuestionBankAttachment":
                    TrainingQuestionBankAttachment attachment;
                    if ((attachment = service.getTrainingQuestionBankAttachment(id)) != null
                            && service.getTrainingQuestionBank(attachment.getBank()) != null
                    ) {
                        return filename.equals(attachment.getPath());
                    }
                    break;
                case "TrainingQuestionBankAttachmentHistory":
                    TrainingQuestionBankAttachmentHistory history = service.getTrainingQuestionBankAttachmentHistory(id);
                    TrainingQuestionBankHistory trainingQuestionBankHistory=service.getTrainingQuestionBankHistory(history.getBankHistory());
                    if (history != null && trainingQuestionBankHistory !=null) {
                        return filename.equals(history.getPath());
                    }
                    break;
            }
        }
        return false;
    }

        @Override
        public String getKey () {
            /* 由于本回调类是单实体使用，只需要使用id；
             * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
            return id + entityClass;
        }

    public TrainingUsing(Integer id, Class entityClass) {
            this.id = id;
            String className = entityClass.getName();
            //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
            this.entityClass = className.substring(className.lastIndexOf('.') + 1);
        }

    public TrainingUsing() {
        }

        public String getEntityClass () {
            return entityClass;
        }

        public void setEntityClass (String entityClass){
            this.entityClass = entityClass;
        }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}