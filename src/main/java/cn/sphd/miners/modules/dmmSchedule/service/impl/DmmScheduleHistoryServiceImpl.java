package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmScheduleHistoryDao;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmScheduleImageHistoryDao;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleHistory;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleImage;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleImageHistory;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleHistoryService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/13.
 */
@Service("dmmScheduleHistoryService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DmmScheduleHistoryServiceImpl implements DmmScheduleHistoryService {
    @Autowired
    DmmScheduleHistoryDao dmmScheduleHistoryDao;
    @Autowired
    DmmScheduleImageHistoryDao dmmScheduleImageHistoryDao;
    @Autowired
    UploadService uploadService;

    @Override
    public List<DmmScheduleHistory> getDmmScheduleHistories(Integer id, PageInfo pageInfo) {
        String hql=" from DmmScheduleHistory where scheduleId=:scheduleId";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",id);
        List<DmmScheduleHistory> DmmScheduleHistoryList=dmmScheduleHistoryDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return DmmScheduleHistoryList;
    }

    @Override
    public DmmScheduleHistory getNewDmmScheduleHistory(Integer id) {
        String hql=" from DmmScheduleHistory where scheduleId=:scheduleId order by id desc";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",id);
        DmmScheduleHistory dmmScheduleHistory= (DmmScheduleHistory) dmmScheduleHistoryDao.getByHQLWithNamedParams(hql,params);
        return dmmScheduleHistory;
    }

    @Override
    public void saveDmmScheduleHistory(DmmSchedule dmmSchedule, AuthAcc acc, Boolean delFileUsing) {
        DmmScheduleHistory scheduleHistory= this.getNewDmmScheduleHistory(dmmSchedule.getId());//最新历史记录

        DmmScheduleHistory dmmScheduleHistory=new DmmScheduleHistory();
        BeanUtils.copyProperties(dmmSchedule,dmmScheduleHistory);
        if (scheduleHistory==null){
            dmmScheduleHistory.setVersionNo(1);//此次为第一次的历史记录
        }else {
            dmmScheduleHistory.setVersionNo(scheduleHistory.getVersionNo()+1);//版本号递增
            dmmScheduleHistory.setPreviousId(scheduleHistory.getId());//前一次记录id
        }
        dmmScheduleHistory.setSchedule(dmmSchedule);
        dmmScheduleHistoryDao.save(dmmScheduleHistory);

        for (DmmScheduleImage dmmScheduleImage:dmmSchedule.getDmmScheduleImageHashSet()){
            DmmScheduleImageHistory dmmScheduleImageHistory=new DmmScheduleImageHistory();
            BeanUtils.copyProperties(dmmScheduleImage,dmmScheduleImageHistory);
            dmmScheduleImageHistory.setUpdateDate(new Date());
            dmmScheduleImageHistory.setVersionNo(dmmScheduleHistory.getVersionNo());//版本号递增
            dmmScheduleImageHistory.setPreviousId(dmmScheduleHistory.getPreviousId());//前一次记录id
            dmmScheduleImageHistory.setScheduleId(dmmSchedule.getId());// 日程id
            dmmScheduleImageHistory.setScheduleHistory(dmmScheduleHistory);
            dmmScheduleImageHistoryDao.save(dmmScheduleImageHistory);

            uploadService.addFileUsing(new DmmUsing(dmmScheduleImageHistory.getId(), dmmScheduleImageHistory.getClass()), dmmScheduleImageHistory.getNormal(),
                    dmmSchedule.getTitle() + "(进历史)", acc , "备忘与日程"); // 添加历史表的附件引用
            if (delFileUsing) {
                uploadService.delFileUsing(new DmmUsing(dmmScheduleImage.getId(), dmmScheduleImage.getClass()), dmmScheduleImage.getNormal(),acc); // 删除主表的附件 引用
            }
        }
    }

    @Override
    public DmmScheduleHistory getDmmScheduleHistoryById(Integer id) {
        DmmScheduleHistory dmmScheduleHistory= dmmScheduleHistoryDao.get(id);
        return dmmScheduleHistory;
    }

    @Override
    public DmmScheduleImageHistory getDmmScheduleImageHistoryById(Integer id) {
        return dmmScheduleImageHistoryDao.get(id);
    }
}
