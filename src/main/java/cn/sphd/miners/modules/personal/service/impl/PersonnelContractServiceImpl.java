package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.personal.dao.PersonnelContractDao;
import cn.sphd.miners.modules.personal.dao.PersonnelContractHistoryDao;
import cn.sphd.miners.modules.personal.dao.PersonnelContractImageDao;
import cn.sphd.miners.modules.personal.dao.PersonnelContractImageHistoryDao;
import cn.sphd.miners.modules.personal.dto.PerConMes;
import cn.sphd.miners.modules.personal.entity.PersonnelContract;
import cn.sphd.miners.modules.personal.entity.PersonnelContractHistory;
import cn.sphd.miners.modules.personal.entity.PersonnelContractImage;
import cn.sphd.miners.modules.personal.entity.PersonnelContractImageHistory;
import cn.sphd.miners.modules.personal.service.PersonnelContractService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service("personnelContractService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class PersonnelContractServiceImpl implements PersonnelContractService {

    @Autowired
    PersonnelContractDao personnelContractDao;
    @Autowired
    PersonnelContractHistoryDao personnelContractHistoryDao;
    @Autowired
    PersonnelContractImageDao personnelContractImageDao;
    @Autowired
    PersonnelContractImageHistoryDao personnelContractImageHistoryDao;

    @Autowired
    UploadService uploadService;
    @Autowired
    UserService userService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Override
    public PersonnelContract insertPersonnelController(User user, PersonnelContract personnelContract, String contractSignTime, String contractStartTime, String contractEndTime, String contractBaseImages) {
        PersonnelContract perCon = this.addPersonnelContract(user, personnelContract, contractSignTime, contractStartTime, contractEndTime, 0);
        //新增改
        PersonnelContractHistory perConHis = this.addPerConHis(perCon, PerContractOperation.update.getIndex());
        if (StringUtils.isNotBlank(perCon.getFilePath())) {
            PersonnelContarctUsing cu = new PersonnelContarctUsing(Integer.valueOf(perCon.getId().toString()), PersonnelContract.class);
            uploadService.addFileUsing(cu, perCon.getFilePath(), perCon.getFileName(), user, "职工档案");
            PersonnelContarctUsing cuh = new PersonnelContarctUsing(Integer.valueOf(perConHis.getId().toString()), PersonnelContractHistory.class);
            uploadService.addFileUsing(cuh, perConHis.getFilePath(), perConHis.getFileName(), user, "职工档案");
        }
        this.addPerConImage(contractBaseImages, user, perCon, perConHis);
        return perCon;
    }

    @Override
    public Integer upPerCon(User user, PersonnelContract personnelContract, String contractSignTime, String contractStartTime, String contractEndTime, String contractBaseImages) {
        PersonnelContract oldCon = this.perConSingle(personnelContract.getId());
        Integer state = 1;
        PersonnelContract newCon = this.getNewPerCon(oldCon.getUser());
        if (oldCon.getId().equals(newCon.getId())) {
            PersonnelContract previousCon = this.getPerConByPreviousOrLast(oldCon.getUser(), oldCon.getLevel(), "1");
            Date validStart = NewDateUtils.dateFromString(contractStartTime, "yyyy-MM-dd");
            Date validEnd = NewDateUtils.dateFromString(contractEndTime, "yyyy-MM-dd");
            if (previousCon != null) {
                if (validStart.getTime() > previousCon.getValidEnd().getTime()) {
                    oldCon.setValidStart(validStart);
                    oldCon.setValidEnd(validEnd);
                } else {
                    state = 3;
                }
            } else {
                oldCon.setValidStart(validStart);
                oldCon.setValidEnd(validEnd);
            }
            if (state.equals(1)) {
                if (StringUtils.isNotBlank(personnelContract.getSn())) {
                    oldCon.setSn(personnelContract.getSn());
                }
                if (StringUtils.isNotBlank(contractSignTime)) {
                    oldCon.setSignTime(NewDateUtils.dateFromString(contractSignTime, "yyyy-MM-dd"));
                }
                if (StringUtils.isNotBlank(personnelContract.getRemark())) {
                    oldCon.setRemark(personnelContract.getRemark());
                }
                PersonnelContarctUsing cu = new PersonnelContarctUsing(Integer.valueOf(oldCon.getId().toString()), PersonnelContract.class);
                if (StringUtils.isNotBlank(personnelContract.getFilePath()) && StringUtils.isNotBlank(personnelContract.getFileName())) {
                    if (StringUtils.isNotBlank(oldCon.getFilePath())) {
                        if (!oldCon.getFilePath().equals(personnelContract.getFilePath())) {
                            uploadService.delFileUsing(cu,oldCon.getFilePath(),user);
                            oldCon.setFilePath(personnelContract.getFilePath());
                            oldCon.setFileName(personnelContract.getFileName());
                            uploadService.addFileUsing(cu, oldCon.getFilePath(), oldCon.getFileName(), user, "职工档案");
                        }
                    } else {
                        oldCon.setFilePath(personnelContract.getFilePath());
                        oldCon.setFileName(personnelContract.getFileName());
                        uploadService.addFileUsing(cu, oldCon.getFilePath(), oldCon.getFileName(), user, "职工档案");
                    }
                } else {
                    if (StringUtils.isNotBlank(oldCon.getFilePath())) {
                        uploadService.delFileUsing(cu,oldCon.getFilePath(),user);
                    }
                    oldCon.setFilePath(null);
                    oldCon.setFileName(null);
                }
                oldCon.setUpdator(user.getUserID());
                oldCon.setUpdateName(user.getUserName());
                oldCon.setUpdateDate(new Date());
                oldCon.setVersionNo(oldCon.getVersionNo()+1);
                PersonnelContractHistory perConHis = this.addPerConHis(oldCon, PerContractOperation.update.getIndex());
                if (StringUtils.isNotBlank(perConHis.getFilePath()) && StringUtils.isNotBlank(perConHis.getFileName())) {
                    PersonnelContarctUsing cuh = new PersonnelContarctUsing(Integer.valueOf(perConHis.getId().toString()), PersonnelContractHistory.class);
                    uploadService.addFileUsing(cuh, perConHis.getFilePath(), perConHis.getFileName(), user, "职工档案");
                }
                if (StringUtils.isNotBlank(contractBaseImages)) {
                    this.addPerConImage(contractBaseImages, user, oldCon, perConHis);
                }
            }
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public Integer renewalPerCon(User user, PersonnelContract personnelContract, String contractSignTime, String contractStartTime, String contractEndTime, String contractBaseImages) {
        PersonnelContract oldCon = this.perConSingle(personnelContract.getId());
        Integer state = 1;
        PersonnelContract newCon = this.getNewPerCon(oldCon.getUser());
        if (oldCon.getId().equals(newCon.getId())) {
            Date validStart = NewDateUtils.dateFromString(contractStartTime, "yyyy-MM-dd");
            Date validEnd = NewDateUtils.dateFromString(contractEndTime, "yyyy-MM-dd");
            if (oldCon.getValidEnd().getTime() > validStart.getTime()) {
                state = 3;
            } else {
                oldCon.setIsCurrent(false);
                personnelContract.setId(null);
                PersonnelContract addCon = this.addPersonnelContract(user, personnelContract, contractSignTime, contractStartTime, contractEndTime, oldCon.getLevel() + 1);
                PersonnelContractHistory addConHis = this.addPerConHis(addCon, PerContractOperation.update.getIndex());
                if (StringUtils.isNotBlank(addCon.getFilePath())) {
                    PersonnelContarctUsing cu = new PersonnelContarctUsing(Integer.valueOf(addCon.getId().toString()), PersonnelContract.class);
                    uploadService.addFileUsing(cu, addCon.getFilePath(), addCon.getFileName(), user, "职工档案");
                    PersonnelContarctUsing cuh = new PersonnelContarctUsing(Integer.valueOf(addConHis.getId().toString()), PersonnelContractHistory.class);
                    uploadService.addFileUsing(cuh, addConHis.getFilePath(), addConHis.getFileName(), user, "职工档案");
                }
                if (StringUtils.isNotBlank(contractBaseImages)) {
                    this.addPerConImage(contractBaseImages, user, addCon, addConHis);
                }
            }
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public Integer terminatePerCon(Integer userID) {
        List<PersonnelContract> list = this.getPerConSignRecord(userID);
        Integer state = 0;
        if (!list.isEmpty()) {
            StringBuffer hql = new StringBuffer("update PersonnelContract set status = :status where user = :user");
            Map<String,Object> param = new HashMap<>();
            param.put("status", PerConStatus.suspend.getIndex());
            param.put("user", userID);
            state = personnelContractDao.queryHQLWithNamedParams(hql.toString(),param);
            PersonnelContract newCon = this.getNewPerCon(userID);
            this.addPerConHis(newCon, PerContractOperation.original.getIndex());
        }
        return state;
    }

    @Override
    public List<PersonnelContract> getPerConSignRecord(Integer userID) {
        StringBuffer hql = new StringBuffer(" from PersonnelContract where user = :user order by level asc");
        Map<String, Object> param = new HashMap(1) {
            {
                put("user", userID);
            }
        };
        List<PersonnelContract> list = personnelContractDao.getListByHQLWithNamedParams(hql.toString(), param);
        return list;
    }

    @Override
    public Map perConMes(Long id) {
        PersonnelContract perCon = this.perConSingle(id);
        StringBuffer hql = new StringBuffer(" from PersonnelContractImage where contract = :contract and operation != :operation order by id asc");
        Map<String, Object> param = new HashMap(2) {{
            put("contract", id);
            put("operation", PerContractOperation.del.getIndex());
        }};
        List<PersonnelContractImage> listImage = personnelContractImageDao.getListByHQLWithNamedParams(hql.toString(), param);
        Map<String, Object> map = new HashMap<>();
        map.put("listImage", listImage);
        map.put("perCon", perCon);
        return map;
    }

    @Override
    public List<PersonnelContractHistory> getListPerConHis(Long id) {
        StringBuffer hql = new StringBuffer(" from PersonnelContractHistory where contract = :contract and operation = :operation order by id asc");
        Map<String, Object> param = new HashMap(2) {
            {
                put("contract", id);
                put("operation", PerContractOperation.update.getIndex());
            }
        };
        List<PersonnelContractHistory> list = personnelContractHistoryDao.getListByHQLWithNamedParams(hql.toString(), param);
        return list;
    }

    @Override
    public Map perConHisMes(Long contractHisId) {
        PersonnelContractHistory conHis = this.perConHisSingle(contractHisId);
        StringBuffer hql = new StringBuffer(" from PersonnelContractImageHistory where contractHistory = :contractHistory order by id asc");
        Map<String, Object> paramImage = new HashMap(1) {{
            put("contractHistory", contractHisId);
        }};
        List<PersonnelContractImageHistory> listHisImage = personnelContractImageHistoryDao.getListByHQLWithNamedParams(hql.toString(), paramImage);
        HashMap<String, Object> map = new HashMap<>();
        map.put("contracthistory", conHis);
        map.put("listHisImage", listHisImage);
        return map;
    }

    @Override
    public PersonnelContract perConSingle(Long id) {
        PersonnelContract perSon = personnelContractDao.get(id);
        return perSon;
    }

    @Override
    public PersonnelContractHistory perConHisSingle(Long id) {
        PersonnelContractHistory perSonHis = personnelContractHistoryDao.get(id);
        return perSonHis;
    }

    @Override
    public PersonnelContractImage perConImage(Long id) {
        PersonnelContractImage perSonImage = personnelContractImageDao.get(id);
        return perSonImage;
    }

    @Override
    public PersonnelContractImageHistory perConImageHis(Long id) {
        PersonnelContractImageHistory perSonImageHis = personnelContractImageHistoryDao.get(id);
        return perSonImageHis;
    }


    @Override
    public List<PersonnelContract> getPersonnelContractsByUserIds(List<Integer> userIdList, Byte status) {
        String hql=" from PersonnelContract p where id in ( select max(id) from PersonnelContract p where p.user in (:userIdList) and p.validStart <= :validStart and p.status = :status group by p.user)";
        Map<String,Object> map=new HashMap<>();
        map.put("userIdList",userIdList);
        Date date = NewDateUtils.today();
        map.put("validStart",date);
        map.put("status",status);
        List<PersonnelContract> personnelContractList=personnelContractDao.getListByHQLWithNamedParams(hql,map);
        Set<Integer> set = new HashSet<>();
        List<Integer> listDelRepUser = new ArrayList<>(set);
        if (!personnelContractList.isEmpty()) {
            for (PersonnelContract p : personnelContractList) {
                set.add(p.getUser());
            }
        }
        if (!userIdList.isEmpty()) {
            for (Integer user : userIdList) {
                if (set.add(user)) {
                    listDelRepUser.add(user);
                }
            }
        }
        StringBuffer hqlDelRep = new StringBuffer(" from PersonnelContract p where id in ( select min(id) from PersonnelContract p where p.user in (:listDelRepUser) and p.validEnd > :validEnd and p.status = :status group by p.user)");
        Map<String,Object> mapDelRep = new HashMap<>();
        mapDelRep.put("listDelRepUser",listDelRepUser);
        mapDelRep.put("validEnd",date);
        mapDelRep.put("status",status);
        List<PersonnelContract> personnelContractmapDelRepList = personnelContractDao.getListByHQLWithNamedParams(hqlDelRep.toString(),mapDelRep);
        if (!personnelContractmapDelRepList.isEmpty()) {
            personnelContractList.addAll(personnelContractmapDelRepList);
        }
        return personnelContractList;
    }

    @Override
    public void personnelContractEveryDayTask() {
        final Date callTime = NewDateUtils.joinDateTimeString(NewDateUtils.today(),"10:00");
        Date today = NewDateUtils.today();
        Date thirtyDay = NewDateUtils.changeDay(today,30);
        Date twentyThreeDay = NewDateUtils.changeDay(today,23);
        Date sixteenDay = NewDateUtils.changeDay(today,16);
        Date nineDay = NewDateUtils.changeDay(today,9);
        Date twoDay = NewDateUtils.changeDay(today,2);
        List<Date> validEndList = new ArrayList<>(){{
            add(today);
            add(thirtyDay);
            add(twentyThreeDay);
            add(sixteenDay);
            add(nineDay);
            add(twoDay);
        }};
        String hql=" select new cn.sphd.miners.modules.personal.dto.PerConMes(p.org, p.user, p.username, p.validEnd) from PersonnelContract p inner join OrgPopedom o on p.org = o.org where o.organization.state!=null and o.organization.state!='1' and o.mid=:mid and p.validEnd in (:validEndList) and p.status = :status and p.isCurrent = :isCurrent order by p.org";
        Map<String,Object> map=new HashMap<>(){{
            put("mid", "kb");
            put("validEndList", validEndList);
            put("status", PerConStatus.efficient.getIndex());
            put("isCurrent", true);
        }};
        List<PerConMes> list=personnelContractDao.getListByHQLWithNamedParams(hql,map);
        if (list != null && list.size() > 0) {
            Integer org = list.get(0).getOrg();
            User generalUser= userService.getUserByRoleCode(org,"general");
            for (PerConMes p : list) {
                if (!(org.equals(p.getOrg()))) {
                    org = p.getOrg();
                    generalUser= userService.getUserByRoleCode(org,"general");
                }
                if (generalUser!=null) {
                    String time = NewDateUtils.dateToString(p.getValidEnd(), "yyyy-MM-dd");
                    String memo = p.getUsername() + "的劳动合同将于" + time + "过期，请及时采取措施！";
                    PersonnelContractRemind personnelContractRemind = new PersonnelContractRemind(generalUser.getUserID(),memo);
                    clusterMessageSendingOperations.delayCall(callTime,personnelContractRemind);
                }
            }
        }
    }

    @Override
    public void sendPerConMes(Integer userId, String memo) {
        String content = "操作时间 系统 " + NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss");
        userSuspendMsgService.saveUserSuspendMsg(1, memo, content, userId, null, null);
    }


    //新增劳动合同
    private PersonnelContract addPersonnelContract(User user, PersonnelContract personnelContract, String contractSignTime, String contractStartTime, String contractEndTime, Integer level){
        personnelContract.setSignTime(NewDateUtils.dateFromString(contractSignTime, "yyyy-MM-dd"));
        personnelContract.setValidStart(NewDateUtils.dateFromString(contractStartTime, "yyyy-MM-dd"));
        personnelContract.setValidEnd(NewDateUtils.dateFromString(contractEndTime, "yyyy-MM-dd"));
        if (StringUtils.isBlank(personnelContract.getFilePath())) {
            personnelContract.setFileName(null);
            personnelContract.setFilePath(null);
        }
        personnelContract.setOrg(user.getOid());
        personnelContract.setCreator(user.getUserID());
        personnelContract.setCreateDate(new Date());
        personnelContract.setCreateName(user.getUserName());
        personnelContract.setOperation(PerContractOperation.add.getIndex());
        personnelContract.setLevel(level);
        personnelContract.setIsCurrent(true);
        personnelContract.setVersionNo(0);
        personnelContract.setStatus(PerConStatus.efficient.getIndex());
        personnelContractDao.save(personnelContract);
        return personnelContract;
    }

    //新增劳动合同历史
    private PersonnelContractHistory addPerConHis(PersonnelContract perCon, Byte operation){
        PersonnelContractHistory perConHis = new PersonnelContractHistory();
        BeanUtils.copyPropertiesIgnoreNull(perCon, perConHis);
        perConHis.setId(null);
        perConHis.setContract(perCon.getId());
        perConHis.setOperation(operation);
        if (PerContractOperation.original.getIndex().equals(operation)) {
            perConHis.setFilePath(null);
            perConHis.setFileName(null);
        }
        personnelContractHistoryDao.save(perConHis);
        return perConHis;
    }

    //新增劳动合同的图片
    private void addPerConImage(String contractBaseImages, User user, PersonnelContract perSon, PersonnelContractHistory perSonHis){
        List<PersonnelContractImage> list = JSON.parseArray(contractBaseImages, PersonnelContractImage.class);
        for (PersonnelContractImage i : list) {
            if (PerContractOperation.add.getIndex().equals(i.getOperation())) {
                i.setOrg(user.getOid());
                i.setContract(perSon.getId());
                i.setOrders(0);
                i.setCreator(user.getUserID());
                i.setCreateDate(new Date());
                i.setUpdateDate(i.getCreateDate());
                i.setCreateName(user.getUserName());
                i.setOperation(i.getOperation());
                personnelContractImageDao.save(i);
                PersonnelContarctUsing cui = new PersonnelContarctUsing(Integer.valueOf(i.getId().toString()), PersonnelContractImage.class);
                uploadService.addFileUsing(cui, i.getUplaodPath(), i.getTitle(), user, "职工档案");
                this.addPerConImageHis(user, i, perSonHis.getId());
            } else {
                PersonnelContractImage image = this.perConImage(i.getId());
                if (!(PerContractOperation.original.getIndex().equals(i.getOperation()))) {
                    PersonnelContarctUsing cui = new PersonnelContarctUsing(Integer.valueOf(i.getId().toString()), PersonnelContractImage.class);
                    uploadService.delFileUsing(cui, image.getUplaodPath(), user);
                    if (PerContractOperation.update.getIndex().equals(i.getOperation())) {
                        image.setTitle(i.getTitle());
                        image.setType(i.getType());
                        image.setUplaodPath(i.getUplaodPath());
                        image.setOperation(i.getOperation());
                        image.setUpdateDate(new Date());
                        uploadService.addFileUsing(cui, image.getUplaodPath(), image.getTitle(), user, "职工档案");
                        this.addPerConImageHis(user,image, perSonHis.getId());
                    } else {
                        image.setUplaodPath(null);
                        image.setUpdateDate(new Date());
                        image.setOperation(i.getOperation());
                    }
                } else {
                    this.addPerConImageHis(user, image, perSonHis.getId());
                }
            }
        }
    }

    //新增劳动合同图片的历史
    private void addPerConImageHis(User user, PersonnelContractImage i, Long hisId) {
        PersonnelContractImageHistory his = new PersonnelContractImageHistory();
        BeanUtils.copyPropertiesIgnoreNull(i,his);
        his.setId(null);
        his.setImage(i.getId());
        his.setContractHistory(hisId);
        personnelContractImageHistoryDao.save(his);
        PersonnelContarctUsing cuih = new PersonnelContarctUsing(Integer.valueOf(his.getId().toString()), PersonnelContractImageHistory.class);
        uploadService.addFileUsing(cuih, his.getUplaodPath(), his.getTitle(), user, "职工档案");
    }

    private PersonnelContract getPerConByPreviousOrLast(Integer userID, Integer level, String type){
        StringBuffer hql = new StringBuffer(" from PersonnelContract where user = :user and level = :level");
        Map<String, Object> param = new HashMap<>();
        param.put("user", userID);
        if ("1".equals(type)) {
            level = level - 1;
        } else {
            level = level + 1;
        }
        param.put("level", level);
        PersonnelContract con = null;
        if (!level.equals(0)) {
            con = (PersonnelContract) personnelContractDao.getByHQLWithNamedParams(hql.toString(),param);
        }
        return con;
    }

    //根据条件获取子合同
    private PersonnelContract getNewPerCon(Integer userID) {
        StringBuffer hql = new StringBuffer(" from PersonnelContract where user = :user order by id desc");
        Map<String, Object> param = new HashMap<>();
        param.put("user", userID);
        PersonnelContract con = (PersonnelContract) personnelContractDao.getByHQLWithNamedParams(hql.toString(),param);
        return con;
    }

}
