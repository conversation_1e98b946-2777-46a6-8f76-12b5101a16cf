<template>
  <div id="purchaseQueryPer">
    <TY_NavTop title="查询"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询</div>
            <div class="queryContent" v-if="paramQuery.supplierId !== ''">{{paramQuery.supplierName}}供应的</div>
            <div class="queryContent" v-if="paramQuery.dateType === '1'">入库申请的提出时间（报检时间）为</div>
            <div class="queryContent" v-if="paramQuery.dateType === '2'">检验时间为</div>
            <div class="queryContent" v-if="paramQuery.dateType === '3'">让步申请的提出时间为</div>
            <div class="queryContent" v-if="paramQuery.dateType === '4'">让步审批的时间为</div>
            <div class="queryContent" v-if="paramQuery.dateType === '5'">库管员的入库处理时间为</div>
            <div class="queryContent">{{queryData.beginDate | formatDay('YYYY年MM月DD日')}}-{{queryData.endDate | formatDay('YYYY年MM月DD日')}}期间<span v-if="paramQuery.dateType === '1'">的订单</span></div>
            <div v-if="paramQuery.dateType === '2'">
              <div v-if="paramQuery.resultType === '1'">
                <div v-if="paramQuery.state === ''">的全部订单</div>
                <div v-if="paramQuery.state === '3'">物料全部合格的订单</div>
                <div v-if="paramQuery.state === '4'">含有不合格物料的订单</div>
              </div>
              <div v-if="paramQuery.resultType === '2'">
                <div v-if="paramQuery.state === ''">的全部物料</div>
                <div v-if="paramQuery.state === '1'">数量无误的物料</div>
                <div v-if="paramQuery.state === '0'">数量有问题的物料</div>
              </div>
            </div>
            <div v-if="paramQuery.dateType === '3' || paramQuery.dateType === '4'">
              <div v-if="paramQuery.state === ''">的全部物料</div>
              <div v-if="paramQuery.state === '6'">让步审批通过的物料</div>
              <div v-if="paramQuery.state === '7'">让步审批未通过的物料</div>
            </div>
            <div v-if="paramQuery.dateType === '5'">
              <div v-if="paramQuery.state === ''">的全部物料</div>
              <div v-if="paramQuery.state === '1'">数量无误的物料</div>
              <div v-if="paramQuery.state === '0'">数量有问题的物料</div>
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的数据共如下{{queryData.data.length}}条
          </div>
        </div>
      </div>
      <div>
        <div v-if="paramQuery.dateType === '1' || (paramQuery.dateType === '2' && paramQuery.resultType === '1')">
          <div class="ui-cells">
            <a class="ui-cell" v-for="(item, index) in queryData.data" v-bind:key="index">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>{{item.name}}</div>
                  <div>采购人 {{item.create_name}} {{item.create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            </a>
          </div>
        </div>
        <div v-else>
          <div class="ui-cells">
            <a class="ui-cell" v-on:click="jump(item.mai_id)" v-for="(item, index) in queryData.data" v-bind:key="index">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>{{item.code}}</div>
                  <div>{{item.name}}</div>
                  <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'purchaseQueryPer',
  data () {
    return {
      loading: true,
      paramQuery: {},
      queryData: {
        data: [],
        beginDate: '',
        endDate: ''
      }
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    let that = this
    this.$http.post('../../../inStock/queryListByCondition', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body
      console.log('queryData' + JSON.stringify(data))
      if (data) {
        if (data.data === null) {
          data.data = []
        }
        that.queryData = data
        if (that.paramQuery.dateSection === '3') {
          that.queryData.beginDate += ' 00:00:00'
          that.queryData.endDate += ' 00:00:00'
        }
      } else {
        that.$kiko_message('加载失败')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    jump: function (id) {
      this.$store.dispatch('setMaterialParam', {
        user: 'query',
        tab: ''
      })
      this.$router.push({
        path: `/materialDetail/${id}`
      })
    }
  }
}
</script>
