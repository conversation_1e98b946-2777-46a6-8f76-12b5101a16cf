package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;

import cn.sphd.miners.modules.material.entity.SrmSupplierContactSocial;
import cn.sphd.miners.modules.material.entity.SrmSupplierContactSocialHistory;


import java.io.Serializable;

public interface SrmSupplierContactSocialHistoryDao extends IBaseDao<SrmSupplierContactSocialHistory, Serializable> {
    //cchId 联系人历史Id
    void insert(SrmSupplierContactSocial contactSocial, Integer cchId);
}
