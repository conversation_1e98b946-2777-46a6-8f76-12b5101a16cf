<template>
	<!-- 提成政策 -->
	<div class="commission">
		<Pageheader title="小程序分享" :showBack="true"/>
    <div>
      <div class="live-panel yellow">
        您将“主播助手”分享后，接收者如在15日内通过您的分享成功注册，您将获得一笔一次性的提成，金额为该次成交最终实收金额的的51%。
      </div>
      <div class="live-panel live-tr">
        <div>
          提成按月核算，某月提成将于下月16日发放。
        </div>
      </div>
      <div class="live-panel">
        <div class="live-tr">”主播助手“将按流程发放提成。</div>
        <div class="live-tr">发放提成时，”主播助手“平台将代扣代缴您的个人所得税。</div>
        <div class="live-tr">此外根据有关规定，您年度收入申报时，需计入上述提成。</div>
      </div>
      <div class="live-panel">
        <div class="live-tr">重要提示！</div>
        <div class="live-tr"><span class="orageTxt">对于您的分享，”主播助手“平台不会向您索要任何钱财，</span>只会向您发放提成！<span class="orageTxt">请提高警惕，小心骗子！</span></div>
      </div>
    </div>
	</div>
</template>
<script setup>
import { ref } from "vue";
import { ElMessage } from 'element-plus'
import { useRouter,useRoute  } from 'vue-router';

const router = useRouter();  //用router.options.routes  渲染菜单


</script>
<style lang="scss" scoped>
.yellow{
  background: #ececb7;
}
.orageTxt{
  color: #f57f0e;
}
</style>
