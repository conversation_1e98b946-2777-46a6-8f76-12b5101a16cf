package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxHistoryEntity;

import java.io.Serializable;
import java.util.List;

public interface AcctTaxHistoryDao extends IBaseDao<AcctTaxHistoryEntity,Serializable> {
    List<AcctTaxHistoryEntity> taxUpdateRecord(Integer org, int taxId);

    Integer getTaxHisId(Integer org, Integer taxId);

    void deleteByTaxId(Integer org,Integer id);
}
