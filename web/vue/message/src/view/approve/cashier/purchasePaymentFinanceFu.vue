<template>
  <div id="purchasePaymentFinanceFu"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="采购部门的付款"></TY_NavTop>
    <div class="detailInfo">
      <!--票据-->
      <div v-if="pund === 0">
        <div @click="jumpDetail(0)" >
          <span><span class="ttl1">申请付款金额 </span><span> {{detailInfo.amount && detailInfo.amount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
          <!-- &nbsp;<span class="btncat" @click="jumpDetail(0)">查看详情</span> -->
        </div>
        <div>
          <span class="ttl1">收款单位</span><span>  {{details.fullName }}</span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailInfo.payNum || 0 }}笔，共{{(detailInfo.paidAmount && detailInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.numApplication || 0 }}笔，共{{(detailInfo.lockedAmount && detailInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
      <!--预付款-->
      <div v-if="pund === 1">
        <div @click="jumpDetail(1)" >
          <span><span class="ttl1">申请付款金额 </span><span>  {{detailInfo.planAmount && detailInfo.planAmount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
          <!-- &nbsp;<span class="btncat" @click="jumpDetail(0)">查看详情</span> -->
        </div>
        <div>
          <span class="ttl1">收款单位</span><span> {{details.srmSupplier.fullName }} </span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailInfo.payNum || 0 }}笔，共{{(detailInfo.paidAmount && detailInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailInfo.lockedAmount && detailInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">计划付款金额</span>{{detailInfo.planAmount && detailInfo.planAmount.toFixed(2)}}元
        </div>
        <div>
          <span class="ttl1">计划付款时间</span>{{detailInfo.planDate |formatDay('YYYY-MM-DD')}}
          <span class="redTxt"><span class="ttl1">计划付款方式</span>{{isture}}</span>
        </div>
        <div class="martop10">
          <span class="ttl1">付款事由</span>采购的预付款
        </div>
      </div>
    </div>
    <div>
      <el-form :label-position="left" :rules="rules" label-width="110px" :model="square" ref="square" style="margin-top:10px;">
        <el-form-item label="拟付款方式" prop="method">
          <el-select v-model="square.method" @change="getAccountKinds()" placeholder="请选择（必填）" size="small"
                     style="padding-right: 116px;border: none; width: 247px;">
            <el-option label="现金" value="1"></el-option>
            <el-option label="银行转账" value="5"></el-option>
            <el-option label="收到的其他公司的承兑汇票" value="4"></el-option>
            <el-option label="收到的其他公司的转账支票" value="3"></el-option>
            <el-option label="本公司的转账支票" value="6"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="square.method === '1'">
          <el-form-item label="现金金额" prop="financeAccountId" >
            <span>{{balance.toFixed(2)}}</span>元
          </el-form-item>
          <el-form-item label="实际付款金额" prop="factMoney" >
            <el-input type="input" v-model="square.factMoney" style="width:100px; " placeholder="请填写"></el-input>元
          </el-form-item>
          <p style="text-align:right;">
            <span class="linkBtn" @click="payAll()">应支付的全部金额</span>
          </p>
          <el-form-item label="实际付款日期" prop="factDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.factDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="摘要" prop="summary">
            <el-input type="textarea" v-model="square.summary" style="width:250px; " placeholder="请输入摘要"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <el-form :label-position="left" :rules="rules" label-width="140px" :model="square" ref="square" style="margin-top:10px;">
        <el-form-item label="银行账户" prop="accountId" v-if="square.method === '5'">
          <el-select v-model="square.accountId" placeholder="请选择银行账户" size="small" @change="setFin">
            <el-option
              v-for="item in financeAccounts"
              :key="item.id"
              :label="(item.isPublic == '1' ? '对公户 ': item.name)+ ' ' + item.bankName + ' ' + item.account"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <div v-if="square.method === '5' && square.accountId">
          <el-form-item label="账户余额"><span>{{selectFin.balance.toFixed(2)}}</span></el-form-item>
          <el-form-item label="账户类型"><span>{{selectFin.isPublic == '1' ? '对公户 ': '非对公户' }}</span></el-form-item>
          <el-form-item label="开户行"><span>{{selectFin.bankName}}</span></el-form-item>
          <el-form-item label="账号"><span>{{selectFin.account}}</span></el-form-item>
          <el-form-item class="marbtm20" :label="paymentAudit === 0 ? '付款金额':'拟付款金额'" prop="factMoney" v-if="square.method === '5'">
            <el-input type="input" v-model="square.factMoney" style="width:100px; " placeholder="请填写"></el-input>元
          </el-form-item>
          <p style="text-align:right;">
            <span class="linkBtn" @click="payAll()">应支付的全部金额</span>
          </p>
          <el-form-item class="marbtm20" style="margin-top:16px;" :label="paymentAudit === 0 ? '实际付款日期' : '拟付款日期'" prop="planDate" v-if="square.method === '5'">
            <el-date-picker type="date" placeholder="请选择" v-model="square.planDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item class="marbtm20" label="摘要" prop="summary" v-if="paymentAudit === 0">
            <el-input type="textarea" v-model="square.summary" style="width:250px; " placeholder="请输入摘要"></el-input>
          </el-form-item>
        </div>
        <div v-if="square.method === '3' && selectOk === 1">
          <el-form-item label="支票号"><span>{{ selectInvoice.selectItem.returnNo }}</span></el-form-item>
          <el-form-item label="所开具发票或收据的金额" class="limitHt"><span></span></el-form-item><!--{{ selectInvoice.selectItem.billAmount.toFixed(2) }}-->
          <el-form-item label="到期日"><span>{{ selectInvoice.selectItem.expireDate | formatDay('YYYY-MM-DD')}}</span></el-form-item>
          <el-form-item label="出具的银行"><span>{{ selectInvoice.selectItem.bankName }}</span></el-form-item>
          <p><span style="margin-left:222px; font-size:14px;" class="linkBtn" @click="getpurchaseInvoiceInfo">查看支票</span></p>
          <el-form-item class="marbtm20" label="收款单位接收日期" prop="factDate" v-if="paymentAudit === 0">
            <el-date-picker type="date" placeholder="请选择" v-model="square.factDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item class="marbtm20" label="收款单位经手人" prop="operatorName" v-if="paymentAudit === 0">
            <el-input v-model="square.operatorName" style="width:250px; " placeholder="请输入收款经手人"></el-input>
          </el-form-item>
          <el-form-item class="marbtm20" label="摘要" prop="summary" v-if="paymentAudit === 0">
            <el-input type="textarea" v-model="square.summary" style="width:250px; " placeholder="请输入摘要"></el-input>
          </el-form-item>
        </div>
        <div v-if="square.method === '4' && selectOk === 1">
          <el-form-item label="汇票号"><span>{{ selectInvoice.selectItem.returnNo }}</span></el-form-item>
          <el-form-item label="所开具发票或收据的金额" class="limitHt"><span>{{ selectInvoice.selectItem.billAmount.toFixed(2) }}</span></el-form-item>
          <el-form-item label="到期日"><span>{{selectInvoice.selectItem.expireDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>
          <el-form-item label="出具的银行"><span>{{ selectInvoice.selectItem.bankName }}</span></el-form-item>
          <p><span style="margin-left:222px; font-size:14px;" class="linkBtn" @click="getpurchaseInvoiceInfo">查看汇票</span></p>
          <el-form-item class="marbtm20" label="收款单位接收日期" prop="factDate" v-if="paymentAudit === 0">
            <el-date-picker type="date" placeholder="请选择" v-model="square.factDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item class="marbtm20" label="收款单位经手人" prop="operatorName" v-if="paymentAudit === 0">
            <el-input v-model="square.operatorName" style="width:250px; " placeholder="请输入收款经手人"></el-input>
          </el-form-item>
          <el-form-item class="marbtm20" label="摘要" prop="summary" v-if="paymentAudit === 0">
            <el-input type="textarea" v-model="square.summary" style="width:250px; " placeholder="请输入摘要"></el-input>
          </el-form-item>
        </div>
        <div v-if="square.method === '6' && selectOk === 1">
          <el-form-item label="转帐支票的开户行"><span>{{ selectInvoice.selectItem.bankName }}</span></el-form-item>
          <el-form-item label="转帐支票的支票号"><span>{{ selectInvoice.financeInvoiceItem.chequeNo }}</span></el-form-item>
          <el-form-item class="marbtm20" label="支票金额" prop="factMoney">
            <el-input type="input" v-model="square.factMoney" style="width:100px; " placeholder="请填写"></el-input> 元
          </el-form-item>
          <p style="text-align:right;">
            <span class="linkBtn" @click="payAll()">应支付的全部金额</span>
          </p>
          <el-form-item class="marbtm20" label="到期日" prop="endDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item class="marbtm20" label="收款单位接收日期" prop="factDate" v-if="paymentAudit === 0">
            <el-date-picker type="date" placeholder="请选择" v-model="square.factDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item class="marbtm20" label="收款单位经手人" prop="receiveName" v-if="paymentAudit === 0">
            <el-input v-model="square.receiveName" style="width:250px; " placeholder="请输入"></el-input>
          </el-form-item>

          <el-form-item class="marbtm20" label="摘要" prop="summary" v-if="paymentAudit === 0">
            <el-input type="textarea" v-model="square.summary" style="width:250px; " placeholder="请输入摘要"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div class="handle_button" style="text-align: center;" v-if="square.method > 0">
        <span style="width:80%; margin-top:20px;" class="ui-btn ui-btn_info" @click="approve(nowApproveItem)">确 定</span>
      </div>
    </div>
  </div>
</template>
<style lang="less">
.limitHt .el-form-item__label{line-height: inherit;}
.limitHt label{line-height: inherit;}
</style>
<style lang="less" scoped>
.redTxt{ color: red; }
.martop10{ margin-top:10px;}
.el-form-item__content{
  text-align: right!important;
  margin-right: 20px!important;
}
#purchasePaymentFinanceFu{
  .marbtm20.el-form-item{
   margin-bottom: 20px;
  }
  .el-form-item__content{
    text-align: right!important;
    margin-right: 20px!important;
  }
  .el-icon-arrow-right{ float: right; font-size: 16px; }
  .el-form-item__error{ position: relative }
  background: #fff;
  overflow: auto!important;
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  .detailInfo{
    padding:10px 20px 0px 20px;
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
    line-height: 26px;
    .ttl1{
      display: inline-block;
      width: 86px;
      text-align: right;
      margin-right: 10px;
    }
  }
  .mar30{ margin-right:30px;  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  .flexC{
    display: flex;margin-bottom:15px ;
    &>span{ display:block; flex: 1;  }
  }
  /deep/ .el-input__icon{
    color: #68717a;
  }
  /deep/.el-select__caret .el-input__icon .el-icon-arrow-up{
    -webkit-transform: rotateZ(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotateZ(-90deg);
    transition: transform 0.3s ease-in-out;
  }
  /deep/ .el-select__caret .el-input__icon .el-icon-arrow-up .is-reverse{
    -webkit-transform: rotateZ(0deg);
    -ms-transform: rotate(0deg);
    transform: rotateZ(0deg);
  }
}
</style>
<script>
export default {
  name: 'purchasePaymentFinanceFu',
  data () {
    return {
      loading: true,
      toggle: -1,
      balance: 0,
      paymentAudit: 2,
      selectOk: '',
      isSpecialBill: 0,
      isPublic: 0,
      selectFin: {},
      detailInfo:{},
      details: {
        financePayment: {},
        poPaymentApplication: {
          amount: ''
        },
        srmSupplier: {},
        poOrdersPrepayment: {},
        poOrders: {
          amount: ''
        },
        approvalProcessList: {}
      },
      selectInvoice: {
        selectItem: {}
      }, // 选择好的 票据信息
      personnelReimburse: {},
      nowApproveItem: {},
      approvalProcessList: {},
      financeAccounts: {},
      isNowApprover: false,
      square: {
        'method': '',
        'planDate': '',
        'factDate': '',
        'accountId': '',
        'receiveDate': '',
        'endDate': '',
        'payName': '',
        'summary': '',
        'operatorName': '',
        'receiveName': '',
        'factMoney': ''
      },
      rules: {
        method: [
          { required: true, message: '该项不能为空！' }
        ],
        factMoney: [
          { required: true, message: '该项不能为空！' }
        ],
        planDate: [
          { required: true, message: '该项不能为空！' }
        ],
        accountId: [
          { required: true, message: '该项不能为空！' }
        ],
        factDate: [
          { required: true, message: '该项不能为空！' }
        ],
        summary: [
          { required: true, message: '该项不能为空！' }
        ],
        operatorName: [
          { required: true, message: '该项不能为空！' }
        ],
        endDate: [
          { required: true, message: '该项不能为空！' }
        ],
        receiveDate: [
          { required: true, message: '该项不能为空！' }
        ],
        receiveName: [
          { required: true, message: '该项不能为空！' }
        ]

      },
      pund: 0,
      code: 0,
      lastAllPay: 0
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    let parampun = Number(this.$route.params.pun)
    let paramcho = Number(this.$route.params.choose)
    that.choose = paramcho
    that.code = Number(paramCode.substring(0, 2)) // 24-待复核; 25-待付款
    that.invoiceID = paramCode.substring(2, paramCode.length)
    that.pund = Number(parampun)
    that.updatePay = this.$route.params.updatePay
    console.log('that.updatePay ', that.updatePay )
    that.selectOk = Number(this.$route.params.selectOk)
    if (that.selectOk === 1) {
      this.getCurAuth()
      that.selectInvoice = this.$store.getters.getPurchaseInvoice
      console.log('selectInvoice.selectItem', this.selectInvoice.selectItem)
      let type = Number(that.selectInvoice.type)
      if (type === 2 || type === 3) {
        this.square.method = String(++type)
      } else if (type === 6) {
        this.square.method = '6'
      }
    }
    that.getDetail()

  },
  destroyed: function () {
  },
  methods: {
    getAlreadyPayDetails () {
      console.log('getAlreadyPayDetails ')
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceAlready/${this.invoiceID}`
      })
    },
    getCheckDetails () {
      console.log('getCheckDetails ')
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceCheck/${this.invoiceID}`
      })
    },
    payAll: function () {
      this.square.factMoney = this.lastAllPay
    },
    getDetail: function () {
      let that = this
      that.loading = false
      if (that.pund === 0) { // 票款
        let params = {'applicationId': that.invoiceID}
        if(that.updatePay == 1){ // 修改的
          params.financePaymentId = localStorage.payid
        }
        this.axios.post('../../../purchaseInvoice/getPurchaseInvoiceDetail.do', params)
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailInfo = res.poPaymentApplication
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            localStorage.setItem('accessToken', that.nowApproveItem.id)
            let appInfo = that.detailInfo
            let allsum = appInfo.amount || 0
            let already = appInfo.paidAmount || 0
            let lockMoney = appInfo.lockedAmount || 0
            console.log(allsum, already, lockMoney)
            let lastMoney = allsum - already - lockMoney
            if(that.updatePay == 1){ // 修改的
              let finnanceAmount = res.financePayment.planAmount
              lastMoney += Number(finnanceAmount)
            }
            that.lastAllPay = lastMoney.toFixed(2)
          })
          .catch(function (error) {
            console.log(error)
          })
      } else if (that.pund === 1) { // 预付款
        let params2 = {'orderPrepaymentId': that.invoiceID}
        if(that.updatePay == 1){ // 修改的
          params2.financePaymentId = localStorage.payid
        }
        this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do', params2)
          .then(function (response) {
            let res = response.data.data
            that.detailInfo = res.poOrdersPrepayment
            that.details = res
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            localStorage.setItem('accessToken', that.nowApproveItem.id)
            let appInfo = that.detailInfo
            let allsum = appInfo.planAmount || 0
            let already = appInfo.paidAmount || 0
            let lockMoney = appInfo.lockedAmount || 0
            console.log(allsum, already, lockMoney)
            let lastMoney = allsum - already - lockMoney
            if(that.updatePay == 1){ // 修改的
              let finnanceAmount = res.financePayment.planAmount
              lastMoney += Number(finnanceAmount)
            }
            that.lastAllPay = lastMoney.toFixed(2)
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    getAccountKinds: function (bool) {
      let that = this
      // bool 只在选完票据跳回来的时候 true
      let json = {
        oid: this.sphdSocket.user.oid,
        isPublic: '',
        isBasic: '',
        accountType: this.square.method,
        accountStatus: 1
      }
      if (Number(json.accountType) === 3 || Number(json.accountType) === 4) {
        if (bool) {

        } else {
          let type = Number(json.accountType) - 1
          let saveData = {
            'id': this.$route.params.id,
            'updatePay': this.$route.params.updatePay,
            'selectOk': this.$route.params.selectOk,
            'type': type,
            'amount': Number(this.detailInfo.amount),
            'selectItem': this.selectInvoice.selectItem
          }
          this.$store.dispatch('setPurchaseInvoice', saveData)
          let planmount = that.detailInfo.planAmount
          let planmount2 = that.detailInfo.billAmount
          let pund = that.pund
          if (pund === 0) {
            this.$router.push({
              path: `/purchasePaymentMethod/${type}/${pund}/${planmount2}`
            })
          } else {
            this.$router.push({
              path: `/purchasePaymentMethod/${type}/${pund}/${planmount}`
            })
          }
        }
      } else if (Number(json.accountType) === 6) { // 内部转账支票
        if (bool) {

        } else {
          let saveData = {
            'id': this.$route.params.id,
            'updatePay': this.$route.params.updatePay,
            'selectOk': this.$route.params.selectOk,
            'type': Number(json.accountType),
            'amount': Number(this.detailInfo.amount),
            'selectItem': this.selectInvoice.selectItem
          }
          this.$store.dispatch('setPurchaseInvoice', saveData)
          let planmount = that.detailInfo.planAmount
          let pund = that.pund
          this.$router.push({
            path: `/purchasePaymentMethod/6/${pund}/${planmount}`
          })
        }
      } else if (Number(json.accountType) === 1 || Number(json.accountType) === 5) { // 1-现金 ； 2-银行转账
        let accountType = 1
        if (Number(json.accountType) === 5) {
          accountType = 2
        }
        this.axios.post('../../../account/getAccountKinds.do', { accountType: accountType, accountStatus: 1 })
          .then(response => {
            let that = this
            let res = response.data
            that.loading = false
            that.financeAccounts = res['data']['financeAccounts']
            console.log('that.financeAccounts ', that.financeAccounts)
            if (that.financeAccounts.length > 0) {
              if (that.square.method === '1') {
                that.balance = that.financeAccounts[0]['balance']
                // var accountId = that.financeAccounts[0]['id']
              } else {}
            }
          }).catch(function (error) {
            console.log(error)
          })
        this.getCurAuth()
      }
    },
    getCurAuth () {
      let that = this
      var type1 = ''
      // Integer businessId：需查询的业务id
      // Integer type：1-报销  2-采购申请的票据处理 3-采购申请的预付款
      // String code：paymentApproval-付款审批 付款复核-paymentAudit
      // 获取现在付款复核是否需要审批
      if (that.pund === 1) {
        type1 = 3
      } else {
        type1 = 2
      }
      let itemd = {
        'code': 'paymentAudit',
        'type': type1,
        'businessId': that.invoiceID

      }
      this.axios.post('../../../popedom/getCurrentItem.do', itemd).then(function (response) {
        let res = response.data
        that.paymentAudit = Number(res.data.status)
        console.log(that.paymentAudit === 0)
      }).catch(function (error) {
        console.log(error)
      })
    },
    setFin: function () {
      let that = this
      let accountId = that.square.accountId
      that.financeAccounts.forEach(function (item) {
        if (item.id === accountId) {
          that.selectFin = item
          that.isPublic = item.isPublic
        }
      })
    },
    getpurchaseInvoiceInfo: function () {
      let item = this.selectInvoice.selectItem
      item.type = this.selectInvoice.type
      item.pund = this.pund
      console.log(item)
      localStorage.setItem('invoiceTicketInfo', JSON.stringify(item))
      this.$router.push({
        path: `/purchasePaymentInvoiceInfo`
      })
    },
    jump: function () {
      let that = this
      that.$router.push({
        path: `/financeReimburse`
      })
    },
    jumpDetail: function (pun) {
      let that = this
      that.$router.push({
        path: `/purchaseInvoiceDetails3/34${that.invoiceID}/${pun}/0`
      })
    },
    approve: function (nowApproveItem) {
      let that = this
      // let lastApprove = this.nowApproveItem // 付款复核是否需要审批
      // let sessionid = this.sphdSocket.sessionid
      // let userId = this.sphdSocket.user.userID
      // let reimburseId = lastApprove['reimburse_'] || ''
      let pen = nowApproveItem.id
      let approvalProcessId = pen
      let method = this.square.method
      let planDate = this.square.planDate // 这个属性是在选择银行转账时才会填写的值
      let factDate = this.square.factDate
      let factMoney = this.square.factMoney
      let summary = this.square.summary // 摘要
      let operatorName = this.square.operatorName
      let accountId = this.square.accountId
      let endDate = this.square.endDate
      let receiveDate = this.square.receiveDate
      let receiveName = this.square.receiveName
      let isOk = true
      let allFuamont = this.lastAllPay // 剩余应付的钱

      let msg = '请将必填项补充完整！'
      if (method === '1') {
        accountId = that.financeAccounts[0]['id']
        if (factDate === '') {
          isOk = false
        }
        if (summary === '') {
          isOk = false
        }
        if (Number(that.balance) < Number(that.square.factMoney)) {
          isOk = false
          msg = '系统中现金的余额不足！请确认！'
        } else if (Number(allFuamont) < Number(that.square.factMoney)) {
          isOk = false
          msg = '需支付的金额没这么多啦！请确认！'
        }
      }
      else if (method === '5') {
        if (planDate === '') {
          isOk = false
        }
        if (accountId === '') {
          isOk = false
        }
        if (Number(that.selectFin.balance) < Number(that.square.factMoney)) {
          isOk = false
          msg = '系统中该账户余额不足！请确认'
        }
        if (that.paymentAudit === 0) {
        }
        if (that.updatePay === '1' || that.updatePay === '2') {
        }
        if (Number(factMoney) > Number(allFuamont)) {
          isOk = false
          msg = '需支付的金额没这么多啦！请确认！'
        }
      }
      else if (method === '3' || method === '4' || method === '6') {
        if (that.selectOk === 1) {
          if (method === '6') {
            if (endDate === '' || factMoney === '') {
              isOk = false
              msg = '请将必填项补充完整！'
            }
            if (Number(that.selectInvoice.selectItem.balance) < Number(that.square.factMoney)) {
              isOk = false
              msg = '系统中该账户余额不足！请确认！'
            } else if (Number(allFuamont) < Number(that.square.factMoney)) {
              console.log('oooo', allFuamont, that.square.factMoney)
              isOk = false
              msg = '需支付的金额没这么多啦！请确认！'
            }
          }
        } else {
          isOk = false
          msg = '请先选择票据！'
        }
      }
      if (isOk) {
        let data = {
          'approvalProcessId': approvalProcessId,
          'method': method
        }
        let confirmStr = ''
        let successStr = ''
        let havConform = false
        if (method === '1' || method === '5') {
          data.planDate = planDate
          data.factDate = factDate
          data.factAmount = factMoney
          data.summary = summary
          data.accountId = accountId
          successStr = '操作成功！页面将跳至“可付款”！'
          if (method === '5') {
            successStr = '操作成功，已提交至“待复核”！页面将跳至“可付款”！'
          }
          if (Number(factMoney) !== Number(allFuamont)) {
            havConform = true
            confirmStr = method === '1' ? '实际付款金额低于需支付金额! 确定无误吗？' : '拟付款金额低于需支付金额！如确定无误，将提交至“待复核”。'
            successStr = `本条申请将停留在“可付款”，因为还有${(Number(allFuamont) - Number(factMoney)).toFixed(2)}元需支付！`
            console.log('havConform  55555', havConform, confirmStr, successStr)
          } else {

          }
        } else if (method === '3' || method === '4') {
          if (that.paymentAudit === 0) {
            data.factDate = factDate
            data.summary = summary
            data.operatorName = operatorName
          }
          data.invoiceId = that.selectInvoice.selectItem.id
          data.oppositeCorp = '' //that.selectInvoice.selectItem.operatorName,不是这个值
          data.receiveDate = this.$options.filters['formatDay'](that.selectInvoice.selectItem.receiveDate, 'YYYY-MM-DD')
          data.expireDate = this.$options.filters['formatDay'](that.selectInvoice.selectItem.expireDate, 'YYYY-MM-DD')
          factMoney = this.selectInvoice.selectItem.billAmount.toFixed(2)
          data.factAmount = factMoney

          successStr = '操作成功，已提交至“待复核”！页面将跳至“可付款”！'
          if (Number(factMoney) !== Number(allFuamont)) {
            havConform = true
            if (Number(factMoney) > Number(allFuamont)) { //
              let cha1 = Number(factMoney) - Number(allFuamont)
              confirmStr = `将多支付${cha1.toFixed(2)}元！如“确定”，将提交至“待复核”。`
              successStr = `多支付了${cha1.toFixed(2)}元！该差额将进入“借款管理”模块。请在该模块管理该差额！`
            } else {
              let cha2 = Number(allFuamont) - Number(factMoney)
              confirmStr = `还有${cha2.toFixed(2)}元需支付！如“确定”，将提交至“待复核”。`
              successStr = `本条申请将停留在“可付款”，因为还有${cha2.toFixed(2)}元需支付！`
            }
            console.log('havConform  55555', havConform, confirmStr, successStr)
          }
        } else if (method === '6') {
          if (that.paymentAudit === 0) {
            data.summary = summary
            //data.receiveDate = this.$options.filters['formatDay'](receiveDate, 'YYYY-MM-DD')
            data.factDate = factDate
            data.receiver = receiveName
          }
          data.factAmount = factMoney
          data.expireDate = this.$options.filters['formatDay'](endDate, 'YYYY-MM-DD')
          data.accountId = that.selectInvoice.financeInvoiceItem.accountId_
          data.invoiceId = that.selectInvoice.financeInvoiceItem.id
          successStr = '操作成功！页面将跳至“可付款”！'
          if (method === '5') {
            successStr = '操作成功，已提交至“待复核”！页面将跳至“可付款”！'
          }
          if (Number(factMoney) !== Number(allFuamont)) {
            havConform = true
            confirmStr = `还有${(Number(allFuamont) - Number(factMoney)).toFixed(2)}元需支付！如“确定”，将提交至“待复核`
            successStr = `本条申请将停留在“可付款”，因为还有${(Number(allFuamont) - Number(factMoney)).toFixed(2)}元需支付！`
            console.log('havConform  55555', havConform, confirmStr, successStr)
          }
        }
        let senUrl = '../../../purchaseInvoice/payableFinanceApproval.do'
        console.log('that.updatePay ', that.updatePay)
        if (that.updatePay === '1' || that.updatePay === '2') {
          if (that.code === 24) {
            data.type = 1 // 1-待复核修改的 2-待付款修改的
          } else if (that.code === 25) {
            data.type = 2
          } else if (that.code === 63) { // 待复核
            data.type = 1
          } else if (that.code === 64) { // 待付款
            data.type = 2
          }
          senUrl = '../../../purchaseInvoice/getUpdateFinancePayment.do'
        }
        console.log('approve havConform', havConform)
        console.log('approve havConform', confirmStr)

        if (havConform) {
          this.$confirm(confirmStr, '提示', {
            confirmButtonText: '确  定',
            cancelButtonText: '返回重录',
            type: 'warning'
          }).then(() => {
            that.sendUrlFun(senUrl, data, successStr, allFuamont)
          }).catch(() => {
            if (method === '1') {
              // this.square.factDate = ''
              // this.square.factMoney = ''
              // this.square.summary = '' // 摘要
            } else if (method === '5') {
              // this.square.planDate = ''
              // this.square.factMoney = ''
            }
          })
        } else {
          that.sendUrlFun(senUrl, data, successStr, allFuamont)
        }
      } else {
        that.$kiko_message(msg)
      }
    },
    sendUrlFun: function (senUrl, data, successStr, allFuamont) {
      let that = this
      that.loading = true
      this.axios.post(senUrl, data)
        .then(function (response) {
          that.loading = false
          let res = response.data.data
          that.$kiko_message(res.content)
          if (res.content === '操作成功' || res.content === '修改成功') {
            that.$kiko_message(successStr)
            localStorage.navNum = 3
            if (allFuamont - data.factAmount > 0) {
              localStorage.navNum = 2
            }
            that.$router.push({
              path: `/purchasePaymentFinanceApproval`
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  },
  computed: {
    // 正确的应该是1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
    isture: function () {
      let one = Number(this.detailInfo.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  }
}
</script>
