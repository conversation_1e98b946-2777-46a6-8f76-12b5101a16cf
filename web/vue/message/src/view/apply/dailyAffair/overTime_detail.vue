<template>
  <div id="dailyAffairs" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="ttl"></TY_NavTop>
    <div class="container">
      <div class="bg">
        <div class="tip" v-if="detail.approveStatus === '2' && detail.actualType === '0' && submitOvertimeRules.upperLimit !== -1">按公司规定，您本次实际加班的数据需在{{submitOvertimeRules.endDate}}前申报，否则将被记为“实际未加班”！</div>
        <div v-if="detail.approveStatus === '2' && detail.actualType === '0'">
          <el-form :label-position="left" :rules="rules" label-width="110px" :model="addDeclareOverTime" ref="addDeclareOverTime">
            <el-form-item label="实际开始时间" prop="beginTime1">
              <el-select v-model="addDeclareOverTime.beginTime1" placeholder="请选择(必填)" size="small" style="width: 100%;" @change="setEndTimeLine">
                <el-option
                  v-for="item in beginTimeLine"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                 >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="实际结束时间" prop="endTime1">
              <el-select v-model="addDeclareOverTime.endTime1" placeholder="请选择(必填)" size="small" style="width: 100%;">
                <el-option
                  v-for="item in endTimeLine"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="申报时长">
              <span>{{duration}}</span>
            </el-form-item>
            <el-form-item label="加班事由" prop="actualReason">
              <el-input v-model="addDeclareOverTime.actualReason" size="small"></el-input>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_info" value="提交" @click="submitForm(1)" v-loading.fullscreen.lock="loading">
            <input type="submit" class="ui-btn" value="实际未加班" @click="submitForm(0)">
          </div>
        </div>
        <div class="item">
          <div class="item_title">{{detail.userName}}</div>
          <div class="item_content"></div>
        </div>

        <div>
          <div class="item">
            <div class="item_title">加班所属日期</div>
            <div class="item_content">{{detail.beginTime | formatDay('YYYY-MM-DD dddd')}}</div>
          </div>
          <div class="kj-panel" v-if="(processList1.length > 1 && detail.actualType === '1') || detail.approveDuration !== null">
            <div class="item">
              <div class="item_title">批准时长</div>
              <div class="item_content">{{detail.approveDuration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">说明</div>
              <div class="item_content">{{detail.approveExplain}}</div>
            </div>
            <div v-if="td.approveStatus === '3'" v-for="(td, index) in processList1" v-bind:key="index">
              <div class="item color-red">
                加班申报被{{td.userName}}驳回！
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{td.reason}}</div>
              </div>
            </div>
            <div class="processList">
              <div class="process">
                <div v-for="(item, index) in processList1" v-bind:key="index">
                  <div v-if="item.approveStatus !== '1'">审批人 <span class="processName">{{item.userName}}</span> {{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="kj-panel applyDetail" v-if="detail.actualType === '1'">
            <div class="item">
              <div class="item_title">申报时长</div>
              <div class="item_content">{{detail.actualDuration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">申报起止时间</div>
              <div class="item_content">{{detail.actualBeginTime | formatDay('HH:mm')}} - {{detail.actualEndTime | formatDay('HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">加班事由</div>
              <div class="item_content">{{detail.actualReason}}</div>
            </div>
            <div class="processList">
              <div class="process">
                <div>申请人 <span class="processName">{{detail.userName}}</span> {{detail.actualApplyTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
          </div>
          <div class="kj-panel noOverTime" v-if="detail.actualType === '2'">
            <div class="item">
              <div class="item_title color-red">实际未加班！</div>
              <div class="item_content color-red" v-if="detail.finalResult === '6'">规定时间内未提交实际加班的数据</div>
              <div class="item_content" v-else></div>
            </div>
            <div class="processList">
              <div class="process">
                <div>
                  申请人
                  <span class="processName" v-if="detail.finalResult === '6'">系统</span>
                  <span class="processName" v-else>{{detail.userName}}</span>
                  {{detail.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}
                </div>
              </div>
            </div>
          </div>
          <div class="kj-panel noNeedAppy" v-if="detail.actualType === '4'">
            <div class="item">
              <div class="item_content">
                由于考勤管理员已成功修改了{{detail.userName}}{{detail.beginTime | formatDay('YYYY-MM-DD dddd')}}的考勤，故此次加班的时长无需再申报。
              </div>
            </div>
            <div class="item">
              <div class="item_content">
                如有异议，请与考勤管理员确认。
              </div>
            </div>
          </div>
          <div class="kj-panel planDetail" v-if="detail.kind !== 2">
            <div class="item">
              <div class="item_title">计划时长</div>
              <div class="item_content">{{detail.duration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">计划起止时间</div>
              <div class="item_content">{{detail.beginTime | formatDay('HH:mm')}} - {{detail.endTime | formatDay('HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">加班事由</div>
              <div class="item_content">{{detail.reason}}</div>
            </div>
            <div v-if="is.approveStatus === '3'" v-for="(is, index) in processList" v-bind:key="index">
              <div class="item">
                <div class="color-red">加班申请被{{is.userName}}驳回！</div>
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{is.reason}}</div>
              </div>
            </div>
            <div class="processList">
              <div class="process">
                <div>申请人 <span class="processName">{{detail.userName}}</span> {{detail.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                <div v-if="it.approveStatus !== '1'" v-for="(it, index) in processList" v-bind:key="index">
                  <div>审批人 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            </div>
            <div class="handle_button" v-if="processList[0]!=null && processList[0].approveStatus==='1' && processList[0].level===1">
              <input type="submit" class="ui-btn ui-btn_info" value="撤销申请" @click="revokeApply()">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .bg{
    background: #fff;
    padding: 4px 16px;
  }
  .item{
    display: flex;
    padding: 4px 0;
    line-height: 18px;
    .item_title{
      width: 90px;
      flex: none;
      color: #666;
      line-height: 2rem;
      padding: 0;
    }
    .item_content{
      flex: auto;
      line-height: 2rem;
      text-align: left;
    }
  }
  .kj-panel{
    border-top:1px solid #ddd;
  }
  .process{
    font-size: 12px;
    color: #666;
    margin-top: 8px;
    margin-bottom: 8px;
    margin-left: 90px;
    .processName{
      display: inline-block;
      width: 52px;
      margin-left: 4px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
      text-align: left;
    }
  }
  .item .item_title.color-red{
    color: red;
  }
</style>
<script>
import auth from '@/sys/auth'
import * as moment from 'moment'
import 'moment/locale/zh-cn'
import { gohistory } from '@/js/common'
export default {
  // duration（计划时长）
  // beginTime（计划开始时间）
  // endTime（计划截止时间）
  // reason（计划原因）
  // actualDuration（申报时长）
  // actualBeginTime（申报起始时间）
  // actualEndTime（申报截止时间）
  // actualReason（申报原因）
  // approveDuration（批准时长）
  // actualApplyTime（申报申请时间）
  // approveExplain（批准说明）
  name: 'applyOverTimeDetail',
  computed: {
    duration: function () {
      let beginTime = this.addDeclareOverTime.beginTime1
      let endTime = this.addDeclareOverTime.endTime1
      if (beginTime !== '' && endTime !== '') {
        let du = (endTime.split(':')[1] - beginTime.split(':')[1]) / 60 + (endTime.split(':')[0] - beginTime.split(':')[0])
        return du
      }
    }
  },
  data () {
    return {
      ttl: "加班申请",
      detail: {},
      processList: [],
      processList1: [],
      addDeclareOverTime: { // 实际加班传参
        session: '', // sessionID
        overtimeId: '', // 加班id
        beginTime1: '', // 开始时间
        userId: '', // 用户id
        endTime1: '', // 结束时间
        actualDuration: 0, // 申报时长
        actualReason: '' // 申报的加班事由
      },
      deletePersonnelOvertime: { // 实际未加班传参
        session: '', // sessionID
        outTimeId: '' // 加班id
      },
      applyDate: '',
      beginTimeLine: [],
      endTimeLine: [],
      workTimeJson: [],
      loading: false,
      rules: {
        beginTime1: [
          { required: true, message: '请输入必填项' }
        ],
        endTime1: [
          { required: true, message: '请输入必填项' }
        ],
        actualReason: [
          { required: true, message: '请输入必填项' }
        ]
      },
      listenersUid: [],
      submitOvertimeRules: {
        upperLimit: -1,
        endDate: ''
      },
      // tabValue: 2
    }
  },
  created () {
    let userId = auth.getUserID()
    let session = auth.getSessionid()
    let outTimeId = this.$route.params.outTimeId
    // this.tabValue = this.$route.params.tabValue
    // 给时间下拉列表赋值
    this.addDeclareOverTime.session = session
    this.addDeclareOverTime.overtimeId = outTimeId
    this.addDeclareOverTime.userId = userId

    this.deletePersonnelOvertime.session = session
    this.deletePersonnelOvertime.outTimeId = outTimeId

    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('notice', function (data) {
        that.loading = false
        let info = JSON.parse(data)
        console.log('notice', data, info)
        if (typeof (info) === 'number') { // 实际未加班
          let datas = Number(data)
          console.log('notice1', datas)
          if (datas === 1) {
            that.$kiko_message('操作成功')
            gohistory(that)
            // that.$router.push({
            //   path: '/applyOutTime',
            //   name: 'applyOutTime',
            //   params: {
            //     tabValue: that.tabValue
            //   }
            // })
          } else if (datas === 2) {
            that.$kiko_message('已进行申报加班，不能进行第二次申报申请')
          } else if (datas === 4) {
            that.$kiko_message('操作失败，因为该条申请已被审批')
            gohistory(that)
          } else {
            that.$kiko_message('您申请中的时间有些问题，请重新申请！')
          }
        } else { // 申报时长
          let state = Number(info['status'])
          if (state === 1) {
            that.$kiko_message('操作成功')
            gohistory(that)
            // that.$router.push({
            //   path: '/applyOutTime',
            //   name: 'applyOutTime',
            //   params: {
            //     tabValue: 2
            //   }
            // })
          } else if (state === 2) {
            that.$kiko_message('已进行申报加班，不能进行第二次申报申请')
          } else if (state === 3) {
            let duration = info['duration']
            that.$kiko_message('公司仅允许提交不超过' + duration + '小时的加班申请。请重新提交')
          } else if (state === 4) {
            that.$kiko_message('操作失败，因为该条申请已被审批')
            gohistory(that)
            // that.$router.push({
            //   path: '/applyOutTime',
            //   name: 'applyOutTime',
            //   params: {
            //     tabValue: 1
            //   }
            // })
          } else if(state === 7) {
            that.loading = false
            that.$kiko_message('操作失败，因为您选择的时间已有加班！')
          } else {
            that.$kiko_message('您申请中的时间有些问题，请重新申请！')
          }
        }
        console.log('返回的是：', info)
      })
    ]
    this.getOverTimeDetail()

  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getOverTimeDetail: function () {
      this.loading = true
      let that = this
      let params = {
        outTimeId: this.$route.params.outTimeId,
        session: ''
      }
      this.$http.post('../../../leaveAndOutTime/approverOverTimeDetails.do', { json: JSON.stringify(params) }, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data
        that.detail = reqData.personnelOvertime // 加班详情
        that.processList = reqData.processList // 计划加班审批流程
        that.processList1 = reqData.processList1 // 申报加班和批准加班的审批流程
        let beginTime = moment(reqData.personnelOvertime.beginTime).format('YYYY-MM-DD')
        that.applyDate = beginTime
        // 获取考勤设置的起始时间和午休时间，并初始化各个选择框
        let data = {
          oid: that.sphdSocket.user.oid,
          attendanceDate: moment(that.detail.beginTime).format("YYYY-MM-DD"), // 请假日期
          userId: that.sphdSocket.user.userID
        }
        if(that.detail.approveStatus === '2' && that.detail.actualType === '0') that.ttl = '实际时长申报页'
        let _this = that
        that.$http.post('../../../workAttendance/getWorkTime.do', data, {
          emulateJSON: true
        }).then((response) => {
          let data = response.body.data
          _this.workTimeJson = data
          _this.initTimeLine(data)

          _this.loading = false
        }).catch(function () {
          _this.loading = false
          _this.$message({
            type: 'error',
            message: '系统错误，请重试1！'
          })
        })
        that.$http.post('../../../popedom/getItemSupplementary.do', { code: 'submitOvertimeRules' }, {
          emulateJSON: true
        }).then((response) => {
          let reqData = response.body.data
          let approvalItem = reqData.approvalItem
          let endDate = moment(_this.detail.beginTime).add(approvalItem.upperLimit, 'days')
          _this.submitOvertimeRules = reqData.approvalItem // 加班详情
          _this.submitOvertimeRules.endDate = moment(endDate).format("YYYY-MM-DD") + ' 24:00'
        }).catch(function () {
          _this.loading = false
          _this.$message({
            type: 'error',
            message: '系统错误，请重试3！'
          })
        })
      }).catch(function () {
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试2！'
        })
      })
    },
    jump: function (path) {
      this.$router.push({
        path: '/' + path,
        name: path
      })
    },
    submitForm: function (type) {
      let that = this
      if (type === 0) {
        this.$confirm('您确定实际未加班吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.loading = true
          that.sphdSocket.send('deletePersonnelOvertime', that.deletePersonnelOvertime)
        })
      } else if (type === 1) {
        let declareOverTime = JSON.stringify(this.addDeclareOverTime)
        declareOverTime = JSON.parse(declareOverTime)
        declareOverTime.beginTime1 = this.applyDate + ' ' + this.addDeclareOverTime.beginTime1 + ':00'
        declareOverTime.endTime1 = this.applyDate + ' ' + this.addDeclareOverTime.endTime1 + ':00'
        declareOverTime.actualDuration = this.duration
        if (Number(this.duration) > 0) {
          this.$refs['addDeclareOverTime'].validate((valid) => {
            if (valid) {
              that.loading = true
              this.sphdSocket.send('addDeclareOverTime', declareOverTime)
              console.log('addDeclareOverTime had touched' + JSON.stringify(declareOverTime))
            } else {
              this.$kiko_message('请填必填项')
              return false
            }
          })
        } else {
          this.$kiko_message('请选择正确的结束时间！')
        }
      }
    },
    revokeApply: function () {
      this.$confirm('确定撤销本条申请吗?', '！提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let that = this
        // 逐级审批加班申请 progressiveApprovalLeave 所需参数：approvalProcessId
        let data = {
          approvalStatus: '9',
          approvalProcessId: this.processList[0].id
        }
        that.sphdSocket.send('progressiveApprovalOutTime', data)
        that.loading = true
      })
    },
    initTimeLine: function (data) {
      let beginTime = data.beginTime
      let endTime = data.endTime
      let breakBegin = data.breakBegin
      let breakEnd = data.breakEnd
      if ( data.workOrNo === '2' ) {
        let canChooseTimeArr = ['00:00-' + beginTime, breakBegin + '-' + breakEnd, endTime + '-24:00']

        this.beginTimeLine = this.getTimeLine(canChooseTimeArr)
        this.endTimeLine = this.getTimeLine(canChooseTimeArr)

      } else {
        this.beginTimeLine = this.getTimeLine()
        this.endTimeLine = this.getTimeLine(['00:30-24:00'])
      }
      var detail = JSON.stringify(this.detail)
      var detail1 = JSON.parse(detail)
      this.addDeclareOverTime.beginTime1 = moment(detail1.beginTime).format("HH:mm")
      this.addDeclareOverTime.endTime1 = moment(detail1.endTime).format("HH:mm")
    },
    // 获取时间范围数据（适用到下拉框）
    getTimeLine: function (range) {
      // range 示例 ['00:00-09:00', '10:00-11:00'] 需要显示的区间（剩下的禁用）
      if (!range) {
        range = ['00:00-24:00']
      }
      let data = []
      for (var i = 0; i <= 24; i++) {
        var textMM = i < 10 ? '0' + i : i
        data.push({value: textMM + ':' + '00', label: textMM + ':' + '00', disabled: true})
        if (i !== 24) {
          data.push({value: textMM + ':' + '30', label: textMM + ':' + '30', disabled: true})
        }
      }
      let that = this
      range.forEach((elem, index) => {
        let start = elem.split("-")[0]
        let end = elem.split("-")[1]

        let startNum = that.chargeToNum(start)
        let endNum = that.chargeToNum(end)
        for (let j = startNum; j <= endNum; j++) {
          data[j].disabled = false
        }
      })
      return data
    },
    // 时间转换位数字好计算
    chargeToNum: function (time) {
      let timeMM = Number(time.split(":")[0]);
      let timeSS = time.split(":")[1];
      timeSS = timeSS === '30'?  0.5: 0
      return Number((- ( -timeMM - timeSS)) * 2 )
    },
    setEndTimeLine: function (value) {
      // 根据选择的时间生成止时间的区间(先查看属于哪个区间，再重新生成当前时间之后到止时间的区间)
      let canChooseTimeArr = []
      if (this.workTimeJson.workOrNo === '2') {
        canChooseTimeArr = ['00:00-' + this.workTimeJson.beginTime, this.workTimeJson.breakBegin + '-' + this.workTimeJson.breakEnd, this.workTimeJson.endTime + '-24:00']
      } else {
        canChooseTimeArr = ['00:00-24:00']
      }

      console.log('canChooseTimeArr', canChooseTimeArr)
      let endTimePeriod = this.getEndTimePeriod(value, canChooseTimeArr)
      // 生成代码
      this.endTimeLine = this.getTimeLine(endTimePeriod)

      // 选择到当前时间半小时后的止时间，如果没有则不显示
      let endTime = this.getNextHalfHourTime(value)
      this.addDeclareOverTime.endTime1 = endTime
    },
    getNextHalfHourTime: function (time) {
      let a = time.split(":")
      let z = Number(a[0])
      let x = a[1]
      if (x === '00') {
        x = '30'
      } else {
        z++
        x = '00'
      }
      return ( z > 9 ? z : '0' + z ) + ':' + x
    },
    getEndTimePeriod: function (tim, timeArr, isNeedOtherAfterTimeArr) {
      let time = this.getNextHalfHourTime(tim)
      let newRange = '', sliceIndex = -1, newTimeArr = []
      let thisTimeNum = this.chargeToNum(time)
      let endTime = ''
      timeArr.forEach((elem, index) => {
        console.log(elem, index);
        var start = elem.split("-")[0]
        var end = elem.split("-")[1]

        var startNum = this.chargeToNum(start)
        var endNum = this.chargeToNum(end)
        if (thisTimeNum >= startNum && thisTimeNum <= endNum) {
          sliceIndex = index
          endTime = end
          newRange = time + '-' + endTime
          newTimeArr.push(newRange)
        }
        if (timeArr[index + 1]) {
          let start1 = timeArr[index + 1].split("-")[0]
          let startNum1 = this.chargeToNum(start1)
          if (thisTimeNum > endNum && thisTimeNum <= startNum1) {
            sliceIndex = index
          }
        }

      })

      if (isNeedOtherAfterTimeArr) {
        if (sliceIndex < 0) {
          newTimeArr = []
        } else {
          newTimeArr.push(...timeArr.slice(sliceIndex+1))
        }
      }

      return newTimeArr
    }
  }
}
</script>
