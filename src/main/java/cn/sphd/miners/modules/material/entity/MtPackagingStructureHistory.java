package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "t_mt_packaging_structure_history")
public class MtPackagingStructureHistory {
    private Long id;
    private Integer org;
    private Long packaging;
    private Long packagingStruct;
    private Integer material;
    private Integer level;
    private Long parent;
    private String path;
    private Integer isLeaf;
    private Integer orders;
    private Integer materialCount;
    private Integer packagingCount;
    private Integer ancillaryCount;
    private String manner;
    private BigDecimal netWeight;
    private Integer netUnitId;
    private String netUnit;
    private BigDecimal grossWeight;
    private Integer grossUnitId;
    private String grossUnit;
    private BigDecimal totalWeight;
    private Integer totalUnitId;
    private String totalUnit;
    private Integer enabled;
    private Date enabledTime;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private Integer operation;
    private Integer previousId;
    private Integer versionNo;

    //上层包装信息
    private MtPackagingStructureHistory parentStructure;
    //主要包装物
    private MtPackagingItemHistory zyPackaging;
    //辅助包装物
    private List<MtPackagingItemHistory> itemList;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "packaging")
    public Long getPackaging() {
        return packaging;
    }

    public void setPackaging(Long packaging) {
        this.packaging = packaging;
    }

    @Basic
    @Column(name = "packaging_struct")
    public Long getPackagingStruct() {
        return packagingStruct;
    }

    public void setPackagingStruct(Long packagingStruct) {
        this.packagingStruct = packagingStruct;
    }

    @Basic
    @Column(name = "material")
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "level")
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    @Basic
    @Column(name = "parent")
    public Long getParent() {
        return parent;
    }

    public void setParent(Long parent) {
        this.parent = parent;
    }

    @Basic
    @Column(name = "path")
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Basic
    @Column(name = "is_leaf")
    public Integer getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "material_count")
    public Integer getMaterialCount() {
        return materialCount;
    }

    public void setMaterialCount(Integer materialCount) {
        this.materialCount = materialCount;
    }

    @Basic
    @Column(name = "packaging_count")
    public Integer getPackagingCount() {
        return packagingCount;
    }

    public void setPackagingCount(Integer packagingCount) {
        this.packagingCount = packagingCount;
    }

    @Basic
    @Column(name = "ancillary_count")
    public Integer getAncillaryCount() {
        return ancillaryCount;
    }

    public void setAncillaryCount(Integer ancillaryCount) {
        this.ancillaryCount = ancillaryCount;
    }

    @Basic
    @Column(name = "manner")
    public String getManner() {
        return manner;
    }

    public void setManner(String manner) {
        this.manner = manner;
    }

    @Basic
    @Column(name = "net_weight")
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    @Basic
    @Column(name = "net_unit_id")
    public Integer getNetUnitId() {
        return netUnitId;
    }

    public void setNetUnitId(Integer netUnitId) {
        this.netUnitId = netUnitId;
    }

    @Basic
    @Column(name = "net_unit")
    public String getNetUnit() {
        return netUnit;
    }

    public void setNetUnit(String netUnit) {
        this.netUnit = netUnit;
    }

    @Basic
    @Column(name = "gross_weight")
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    @Basic
    @Column(name = "gross_unit_id")
    public Integer getGrossUnitId() {
        return grossUnitId;
    }

    public void setGrossUnitId(Integer grossUnitId) {
        this.grossUnitId = grossUnitId;
    }

    @Basic
    @Column(name = "gross_unit")
    public String getGrossUnit() {
        return grossUnit;
    }

    public void setGrossUnit(String grossUnit) {
        this.grossUnit = grossUnit;
    }

    @Basic
    @Column(name = "total_weight")
    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    @Basic
    @Column(name = "total_unit_id")
    public Integer getTotalUnitId() {
        return totalUnitId;
    }

    public void setTotalUnitId(Integer totalUnitId) {
        this.totalUnitId = totalUnitId;
    }

    @Basic
    @Column(name = "total_unit")
    public String getTotalUnit() {
        return totalUnit;
    }

    public void setTotalUnit(String totalUnit) {
        this.totalUnit = totalUnit;
    }

    @Basic
    @Column(name = "enabled")
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time")
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }


    @Transient
    public MtPackagingStructureHistory getParentStructure() {
        return parentStructure;
    }

    public void setParentStructure(MtPackagingStructureHistory parentStructure) {
        this.parentStructure = parentStructure;
    }
    @Transient
    public MtPackagingItemHistory getZyPackaging() {
        return zyPackaging;
    }

    public void setZyPackaging(MtPackagingItemHistory zyPackaging) {
        this.zyPackaging = zyPackaging;
    }
    @Transient
    public List<MtPackagingItemHistory> getItemList() {
        return itemList;
    }

    public void setItemList(List<MtPackagingItemHistory> itemList) {
        this.itemList = itemList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MtPackagingStructureHistory that = (MtPackagingStructureHistory) o;
        return id == that.id && Objects.equals(org, that.org) && Objects.equals(packaging, that.packaging) && Objects.equals(packagingStruct, that.packagingStruct) && Objects.equals(material, that.material) && Objects.equals(level, that.level) && Objects.equals(parent, that.parent) && Objects.equals(path, that.path) && Objects.equals(isLeaf, that.isLeaf) && Objects.equals(orders, that.orders) && Objects.equals(materialCount, that.materialCount) && Objects.equals(packagingCount, that.packagingCount) && Objects.equals(ancillaryCount, that.ancillaryCount) && Objects.equals(manner, that.manner) && Objects.equals(netWeight, that.netWeight) && Objects.equals(netUnitId, that.netUnitId) && Objects.equals(netUnit, that.netUnit) && Objects.equals(grossWeight, that.grossWeight) && Objects.equals(grossUnitId, that.grossUnitId) && Objects.equals(grossUnit, that.grossUnit) && Objects.equals(totalWeight, that.totalWeight) && Objects.equals(totalUnitId, that.totalUnitId) && Objects.equals(totalUnit, that.totalUnit) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(memo, that.memo) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, packaging, packagingStruct, material, level, parent, path, isLeaf, orders, materialCount, packagingCount, ancillaryCount, manner, netWeight, netUnitId, netUnit, grossWeight, grossUnitId, grossUnit, totalWeight, totalUnitId, totalUnit, enabled, enabledTime, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
