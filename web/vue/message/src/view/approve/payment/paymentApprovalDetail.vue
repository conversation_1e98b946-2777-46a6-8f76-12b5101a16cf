<template>
  <div id="paymentApprovalDetail"  v-loading.fullscreen.lock="loading">
    <TY_NavTop :title=title ></TY_NavTop>
      <div class="panel-title">
        {{personnelReimburse.createName}} 申请报销 {{personnelReimburse.amount}} 元  <div class="right">{{personnelReimburse.transactionType | isSaler}}</div>
      </div>
      <div class="panel-content">
        <div class="item-header">报销事由</div>
        <div>事件的发生日期：{{personnelReimburse.beginDate | formatDay('YYYY年MM月DD日')}} - {{personnelReimburse.endDate | formatDay('DD日')}}</div>
        <div>
          {{personnelReimburse.purpose}}
        </div>
        <div class="panel-content">
          <div class="text-right" v-on:click="toggle = -toggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
          <div v-show="toggle > 0">
            <div class="item-content">
              <div class="processItem">
                <div>提交报销申请</div>
                <div>申请人</div>
                <div>{{personnelReimburse.createName | stringSplit(4)}} {{personnelReimburse.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-for="it in approvalProcessList" v-bind:key="it.id">
                <div v-if="it.approveStatus === '2' && it.businessType == 32" class="processItem">
                  <div>在线审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && !it.businessType" class="processItem" >
                  <div>在线审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="code !== '35'">
                  <div v-if="it.approveStatus === '2' && it.businessType == 33" class="processItem">
                    <div>线下审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 17" class="processItem">
                    <div>付款审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 21" class="processItem">
                    <div>付款方式确认</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 20" class="processItem">
                    <div>付款复核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 22" class="processItem">
                    <div>付款方式修改</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '5'" class="processItem">
                    <div>
                      <span v-if="financeAccount.accountType == '2'">报销款已转账</span>
                      <span v-if="financeAccount.accountType == '1'">现金付讫</span>
                    </div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
              </div>
              <div v-if="code !== '35'">
                <!-- 最后一条审批记录展示 -->
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===32">
                  <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                  <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{  nowApproveItem.reasonStr }}</div></div>
                  <div class="processItem">
                    <div></div>
                    <div>出纳员</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && !nowApproveItem.businessType">
                  <div style="margin-top:15px;">报销申请被驳回！</div>
                  <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                  <div class="processItem">
                    <div></div>
                    <div>审批人</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===33">
                  <div style="margin-top:15px;">  线下审核未通过！</div>
                  <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                  <div class="processItem">
                    <div></div>
                    <div>出纳员</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="nowApproveItem.approveStatus === '8'">
                  <div style="margin-top:15px;">被驳回！</div>
                  <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                  <div class="processItem">
                    <div></div>
                    <div>出纳员</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-blue">
                  <!--<span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && !nowApproveItem.businessType"><b>{{nowApproveItem.userName}}</b>为下一个审批人</span>-->
                  <span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===32">等待出纳在线审核。</span>
                  <span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===33">
                请将实际票据交出纳，由出纳进行线下审核。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                  <span v-if="nowApproveItem.approveStatus === '1' && isNowApprover && nowApproveItem.businessType===33">
                请等待报销者提交实际票据。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                  <span v-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
      <div class="panel-title">
        本次报销共有票据 {{personnelReimburse.billQuantity}} 张，合计 {{personnelReimburse.billAmount}} 元
      </div>
      <div class="panel-content">
        <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
          <el-tab-pane label="按票据查看" name="bill">
            <div class="ui-cells_none">
              <a class="ui-cell" @click="goBillInfo(item)" v-for="item in listMapBillCat" v-bind:key="item.billCat">
                <div class="ui-cell__bd">
                  <div class="ui-cell_con">
                    <el-row>
                      <el-col :span="12">{{item.billCatName}}</el-col>
                      <el-col :span="6">{{item.num}}张</el-col>
                      <el-col :span="6">{{item.totalAmount}} 元</el-col>
                    </el-row>
                  </div>
                  <div class="ui-cell__ft"></div>
                </div>
              </a>
            </div>
          </el-tab-pane>
          <el-tab-pane label="按费用查看" name="feeCat">
            <a class="ui-cell" @click="goFeeInfo(item)"  v-for="item in listMapFeeCat" v-bind:key="item.feeCat">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <el-row>
                    <el-col :span="12">{{item.feeCatName}}</el-col>
                    <el-col :span="6">{{item.secondFeeCatName | chargeNull}}</el-col>
                    <el-col :span="6">{{item.totalAmount}} 元</el-col>
                  </el-row>
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-if="lastState == 17">
        <div class="ui-cells_none">
          <p class="chk">
            <el-checkbox label="同意付款！" name="type" @change="handleChange"></el-checkbox>
            </p>
          <div slot="footer" class="dialog-footer">
            <div class="handle_button">
              <input type="submit" v-bind:style="{ 'background': handleBgColor, 'color': handleColor }" class="ui-btn" value="确定" @click="approve()">
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<style lang="less">
  #app #paymentApprovalDetail{
    .processItem{
      display: flex;
      font-size: 11px;
      margin-left:-5px;
      div:nth-child(1){ text-align: left; flex: 1 }
      div:nth-child(2){ text-align: center; flex: 1 }
      div:nth-child(3) { text-align: right; flex: 2 }
    }
    overflow-y: auto;
    .chk{ padding:50px 0 0 20px; }
    .handle_button{ text-align: center;  }
    .ui-btn{
      padding: 6px 52px;
      font-size: 12px;
      line-height: 1;
      height: inherit;
      border-radius: 3px;
      width: auto;
    }
    .tipRed{ color: #ff220f; }
    .tipCon {
      padding:15px;
      opacity: 0.8;
      p:nth-child(2),p:nth-child(3){
      text-indent: 2em;
      }
    }
  }

</style>

<script>
import { isSaler, chargeNull } from '../../../js/common'
import rejectPng from '../../../images/reject.png'
export default {
  name: 'paymentApprovalDetail',
  data () {
    return {
      isReject: false, // 判断是否驳回来显示驳回图片
      isNowApprover: false,
      title: '个人报销付款审批',
      rejectPng: rejectPng, // 驳回图片
      twoApproval: false,
      handleBgColor: '#C7C7C7',
      handleColor: '#787878',
      tips: false,
      tipContent: '',
      loading: true,
      toggle: -1,
      code: '',
      approveStatus: '0',
      personnelReimburse: {},
      financeAccount: {},
      nowApproveItem: {
        'approveStatus': '0'
      },
      approvalProcessList: {},
      listMapBillCat: {},
      listMapFeeCat: {},
      lastState: '0',
      num: 0,
      financeReimburseBills: [],
      paymentReviewHandle: [],
      paymentReviewApproval: [],
      isSpecialBill: 0,
      isPublic: '',
      square: {},
      listenersUid: [],
      activeName: 'bill'
    }
  },
  filters: {
    isSaler,
    chargeNull,
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    // let billCat = this.$route.params.billCat
    console.log(this.$route.params)
    let reimburseId = paramCode.substring(2, paramCode.length)
    let code = paramCode.substring(0, 2)
    this.code = code
    if (code === 'AA') {
      this.title = '增票认证'
    }
    this.listenersUid = [
      this.sphdSocket.subscribe('cashierHandle', function (data) {
        console.log('cashierHandle session Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.cashierHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('authInvoiceDetail', function (data) {
        that.loading = false
        let authInvoiceDetail = JSON.parse(data)
        // 报销申请信息
        that.num = authInvoiceDetail.num
        that.financeReimburseBills = authInvoiceDetail.financeReimburseBills
        console.log('authInvoiceDetail session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('paymentHandleApproval', function (data) {
        console.log('Socket recieved OK:' + data)
        that.loading = false
        let res = JSON.parse(data)
        let state = res['status']
        let content = res['content']
        that.$kiko_message(content)
        if (state === 1 || state === 2) {
          that.$router.push({
            path: `/personalReimburseApproval`
          })
        }
      })
    ]
    let session = this.sphdSocket.sessionid
    // this.sphdSocket.send('getReimburseDetail', { reimburseId: reimburseId, session: session })
    this.getDetails(reimburseId)
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getDetails: function (reimburseId) {
      let that = this
      let params = {
        reimburseId:reimburseId
      }
      this.axios.post('../../../reimburseWindow/getReimburseDetail.do', params)
        .then(function (response) {
          console.log('详情=', response)
          let data = response.data.data
          console.log('详情 data=', data)

          that.loading = false
          let ReimburseDetail = data

          // 报销申请信息
          that.personnelReimburse = ReimburseDetail.personnelReimburse
          that.financeAccount = ReimburseDetail.financeAccount
          that.square.summary = that.personnelReimburse.summary
          // 审批列表
          that.approvalProcessList = ReimburseDetail.approvalProcessList
          if (ReimburseDetail.approvalProcessList.length > 0) {
            that.nowApproveItem = that.approvalProcessList[that.approvalProcessList.length - 1]
            let lastToUser = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].toUser
            that.lastState = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].businessType
            if (!lastToUser || lastToUser === that.sphdSocket.user.userID) {
              that.isNowApprover = true
            }
          }
          that.approveStatus = that.personnelReimburse.approveStatus
          // 按照票据种类列表
          that.listMapBillCat = ReimburseDetail.listMapBillCat
          for (var i in ReimburseDetail.listMapBillCat) {
            let billCatName = ReimburseDetail.listMapBillCat[i].billCatName
            if (billCatName === '增值税专用发票') {
              that.isSpecialBill = 1
            } else if (billCatName === '收据') {
              that.isSpecialBill = 2
            }
          }
          // 按费用类别列表
          that.listMapFeeCat = ReimburseDetail.listMapFeeCat



        })
        .catch(function (error) {
          console.log(error)
        })
    },

    handleChange: function () {
      if (this.handleBgColor === '#C7C7C7') {
        this.handleColor = '#fff'
        this.handleBgColor = '#409EFF'
      } else {
        this.handleBgColor = '#C7C7C7'
        this.handleColor = '#787878'
      }
    },
    goBillInfo: function (item) {
      let params = {
        'name': 'approve',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': item.billCat,
        'feeCat': '',
        'secondFeeCat': '',
        'billCatName': item.billCatName,
        'feeCatName': '',
        'secondFeeCatName': '',
        'totalAmount': item.totalAmount,
        'num': item.num,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFinanceBillInfo: function (id) {
      let params = {
        id: id
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFeeInfo: function (item) {
      let params = {
        'name': 'approve',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': '',
        'feeCat': item.feeCat,
        'secondFeeCat': item.secondFeeCat,
        'billCatName': '',
        'feeCatName': item.feeCatName,
        'secondFeeCatName': item.secondFeeCatName,
        'totalAmount': item.totalAmount,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    approve: function () {
      if (this.handleBgColor === '#409EFF') {
        let approveInfo = {
          userId: this.sphdSocket.user.userID,
          approvalProcessId: this.approvalProcessList[this.approvalProcessList.length - 1].id,
          session: this.sphdSocket.sessionid
        }
        this.loading = true
        this.sphdSocket.send('paymentHandleApproval', approveInfo)
      } else {
        this.$kiko_message('确定实际票据是否与本申请一致！')
      }
    }
  }
}
</script>
