.modInfo [disabled]{ background: #d1d1d1;cursor: not-allowed;}
.modInfo .ty-inputText,.modInfo .ty-inputSelect{width: 100%;}
.modItem-m{position: relative;float:left;margin-top: 12px;width: 400px;}
.modItem-m:nth-child(odd){margin-right: 80px;}
.modInfo{padding-bottom: 20px;margin-bottom: 20px;}
.rt-arrow{float: right;font-family:"宋体"; color: #000001; font-size: 15px;font-weight: bold;}
.narrW{padding: 0 50px;}
.topTip{margin: 30px 0;font-size: 15px;}
.itemBox{margin-top: 30px;}
.itemBox .modItem {float:left;width: 400px;}
.itemBox .modItem > p{padding-right: 20px;}
.itemBox>div {margin-bottom: 36px;}
.itemBox>div .modItem:nth-child(2n){margin-right: 70px;}
.bw-alert{width: 100%;padding: 8px 0;box-sizing: border-box;border-radius: 2px;position: relative;font-size: 13px;overflow: hidden;opacity: 1;display: flex;align-items: center;
    transition: opacity .2s;margin-bottom: 8px;justify-content: space-between;}
.btnSize .ty-btn{width: 152px;text-align: center;}
.search{ float:right;  }
.search>input{ margin-left: 10px; height:30px; padding:0 5px; border: 1px solid #ccc; border-right:none;border-radius:4px 0 0 4px; width:280px;  }
.search>span{ display:inline-block; width:80px;height:30px; float:right; text-align: center;border: 1px solid #ccc; cursor: default;background: #5d9beb; color:#fff; line-height:25px;
    padding:0 10px; border-radius:0 4px 4px 0; }
.careful{color: #0070bf;}
.orgCare{padding-right: 20px;color: #c55911;}
.orgLightCare{color: #ec7d31;}
.mainCon:not(.mainCon1){display: none;}
.clearInputVal,.clearVal {position: absolute;  bottom: 0;  right: 4px; width: 10px;
    cursor: pointer; color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
input:focus + i.clearInputVal,input:focus + i.clearVal{  display: block;  }
.modItem-m .clearInputVal{top: 30px;}
.customersSearch{ position: relative; }
.cusSearchItems{ position: absolute; display: none; width: 100%;background: #fff; border: 1px solid #ddd; max-height: 130px; overflow-y: auto;z-index: 1000;  }
.cusSearchItems>option{ padding:0 15px; }
.cusSearchItems>option:hover{ background: #b9d2df; }
.addContractForm{ padding-left: 60px;padding-right: 58px;}
.bonceCon .addContractForm input:disabled{background-color: #d8d8d8;}
.low-line{border-bottom: 1px solid #bfbfbf;}
.selectBtns span{ margin-left: 20px;font-size: 15px;color: #bebebe;}
.selectBtns span.linkBtn{color: #0b94ea;}
.linkBtn{ color: #0070bf; cursor: pointer; font-weight: bold;}
.narrowBody{margin: auto;width: 88%;}
.changeDot .fa-dot-circle-o{color:#5d9cec ;}
.changeDot{padding: 8px 10px;}
.changeDot i{margin-right: 10px;}
.dotItem .changeDot{float: left; width:150px; }
.dotItem{margin-top: 18px;}
.gapTp{margin-top: 40px;}
.parts,.priceTp{margin-top: 30px;}
.priceForm input{width: 100%;}
.origin{margin-top: 10px;}
.origin select,.origin input{width: 124px;}
.origin select[disabled]{ background: #d9d9d9 !important;  }
.gapRt{display: inline-block;width: 180px;}
.gapRt0{display: inline-block;width: 306px;}
.setItem>.fa{ color:#5d9cec; margin-right:15px;   }
.fa{ color:#5d9cec;  }
.gray-box{min-height: 30px; padding: 4px 8px;border: 1px solid #dcdfe6;background: #d8d8d8;}
.priceItem+ input{display: none;width: 100px;}
.surplusTip{margin-bottom: 10px;}
.productCon{white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.xing:before {  content: "*";  width: 5px;  height: 5px;  display: inline-block;color: red;  font-weight: bolder;  position: relative;  }
.linkUploadify .uploadify-button { margin: 0; background-color: inherit; border: none;  line-height: 12px;  padding: 3px 6px;  font-size: 12px;  color: #1685ea;  cursor: pointer;  text-decoration: none;
    font-weight: normal;  display: inline-block;  vertical-align: middle;  }
.linkUploadify .uploadify-button:active{border: none;}
.flex-box{display: flex;justify-content:space-between;flex-wrap: wrap;}
.flex-box>div{margin-bottom: 12px;}
.flex-box .fieldSm{width:150px;}
.flex-box .fieldMid span{  margin-right: 20px;  }
.fileCon{ margin-bottom: 10px; border: 1px solid #ddd; height: 30px; border-radius: 2px; background: #fff; line-height: 30px; padding: 0 9px; }
.fileCon .fileIm{ margin:0;  }
.fileCon .fileIm .fa{ margin:0 8px; color:#5d9cec;   }
.red{ color: #ed5565; }
.s_contact1 .modItem-m.priceSet,.modItem-m.add_modeSetted,.modItem-m.add_modeSetted {margin-right: 0;}
.elemFlex{  display: flex;justify-content: space-between;align-items: center;padding-bottom: 10px;}
#seeServiceNoCycle .manyTime > div{margin-bottom: 12px;}
.sm-ttl{display: inline-block;width: 100px;}
.sm-con{display: inline-block;width: 130px;}
.rws-con{display: inline-block;width: 250px;}
.rtCon span{ float: right; }
.goods .linkBtn{margin-right: 20px; }
.gapLn{margin-top: 20px;padding-top: 20px;border-top: 1px solid #CCCCCC;}
.noByCycleSet{margin-top: 20px;}
.noByCycleSet .manyTime select.entry{width: 184px;}
.flexBox{width: 100%;padding: 8px 0;box-sizing: border-box;border-radius: 2px;position: relative;font-size: 13px;overflow: hidden;opacity: 1;display: flex;align-items: center;
    justify-content: space-between;transition: opacity .2s;margin-bottom: 8px;}
.withBold{font-weight: bold;}
.def-btn {color: #0070bf;padding: 0 5px;}
.del-btn {color: red;padding: 0 5px;}
.price-box{margin-left: 100px;margin-top: 10px;}
.price-box .del-btn{top: 5px;right: -34px;position: absolute;font-weight: bold;}
.price-box select,.price-box input{width: 124px;}
.parts,.priceTp{margin-top: 30px;}
.price-box input.middleSize{width: 390px;}
.bonceCon .conBody input.priceInput{display: none}
.bonceCon input[disabled]{ background: #d8d8d8!important  }
.narrowA{margin: auto;width: 350px;}
.itemC{margin-bottom: 30px}
.deliveryTime{white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.midPay{margin-top: 10px;}
.bounce_Fixed4 {  position: fixed;  display: none;  background: rgba(100, 100, 100, 0.5);  width: 100%;  padding-bottom: 200px; z-index: 10006; }
.delSite {position: relative;}

