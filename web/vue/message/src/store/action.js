import axios from 'axios'

function setNewMenu (context, obj) { // 调用：this.$store.dispatch('setNewMenu'，obj)
  console.log('action.js setNewMenu', obj, context)
  context.commit('SET_MENU', obj)
}
function setNewVersion (context, obj) { // 调用：this.$store.dispatch('setNewMenu'，obj)
  context.commit('SET_VERSION', obj)
}
function setPurchaseFinanceInfo(context, obj) {
  context.commit('SET_PURCHASEFINANCEINFO', obj)
}
function setNewMsgCount (context, num) {
  context.commit('SET_MsgCount', num)
}
function setMsgDisappear (context, bool) {
  context.commit('SET_MSGDISAPPEAR', bool)
}
function setNewHomeNav (context, num) {
  context.commit('SET_HOMENAV', num)
}
function setOverTimeNav (context, num) {
  context.commit('SET_OVERTIMENAV', num)
}
function setLeaveNav (context, num) {
  context.commit('SET_LEAVENAV', num)
}
function setNewInvoiceNav (context, num) {
  context.commit('SET_INVOICENAV', num)
}
function setNewInvoiceFinNav (context, num) {
  context.commit('SET_INVOICEFINNAV', num)
}
function setNewFileNav (context, num) {
  context.commit('SET_FILENAV', num)
}
function setBillParam (context, obj) {
  context.commit('SET_BillPARAM', obj)
}
function setReimburseNav (context, num) {
  context.commit('SET_REIMBURSENAV', num)
}
function setFinReimburseNav (context, num) {
  context.commit('SET_FINREIMBURSENAV', num)
}
function setPayReimburseNav (context, num) {
  context.commit('SET_PAYREIMBURSENAV', num)
}
function setMaterialParam (context, obj) {
  context.commit('SET_MATERIALPARAM', obj)
}
function setApprovalSettingSearchInfo (context, obj) {
  context.commit('SET_APPROVALSETTINGSEARCHINFO', obj)
}
function setCusorg (context, obj) {
  context.commit('SET_CUSORG', obj)
}
function setBeRecoveredInfo(context, obj) {
  context.commit('SETBERECOVEREDINFO', obj)
}
// 获取开票申请的列表
function setNewInvoiceList ({ commit }) {
  return new Promise((resolve, reject) => {
    axios.post('../../../sale/applicationList.do').then(data => {
      let _this = this
      _this.applyList2 = []
      _this.applyList3 = []
      _this.applyList4 = []
      _this.applyList5 = []
      let list = data['data']['data']
      if (list && list.length > 0) {
        list.forEach(function (item) {
          let sta = Number(item['state'])
          switch (sta) {
            case 2 :
            case 3 :
            case 4 :
            case 5 :
              _this['applyList' + sta].push(item)
              break
            default :
          }
        })
      }
      let listArr = {
        'applyList2': _this.applyList2,
        'applyList3': _this.applyList3,
        'list3' : JSON.parse(JSON.stringify(_this.applyList3)),
        'applyList4': _this.applyList4,
        'list5':  JSON.parse(JSON.stringify(_this.applyList5)),
        'applyList5': _this.applyList5
      }
      console.log('listArr',listArr)
      commit('SET_INVOICELIST', listArr)
      resolve(data.data)
    }).catch((err) => {
      console.log('获取数据错误了！', err)
    })
  })
}
// 获取文件审批的列表
function setNewFileList (context, num) {
  context.commit('SET_FILELIST', num)
}
// creator:hxz 2019-06-04 获取投诉的列表
function setNewComplaintList (context, params) {
  return new Promise((resolve, reject) => {
    axios.get('../../../complaint/getHandingComplaint.do', { params: params }).then(data => {
      let list = data.data.data
      context.commit('SET_COMPLAINTLIST', list)
      resolve(data.data)
    }).catch((err) => {
      console.log('获取数据错误了！', err)
      reject(err)
    })
  })
}
// creator:hxz 2019-06-05 获取登录投诉的人员身份-是不是核心人物
function setNewComplaintHe (context, num) {
  context.commit('SET_COMPLAINTHE', num)
}
function setNewComplaintNav (context, num) {
  context.commit('SET_COMPLAINTNAV', num)
}
function setPotentialNav (context, num) {
  context.commit('SET_POTENTIALNAV', num)
}
function setSaleManageNav (context, num) {
  context.commit('SET_SALEMANAGENAV', num)
}
function setPurchaseNav (context, num) {
  context.commit('SET_PURCHASENAV', num)
}
function setPurchaseInvoice (context, obj) {
  context.commit('SET_PURCHASEINVOICE', obj)
}
function setAttendanceLeaveParam (context, obj) {
  context.commit('SET_ATTENDANCELEAVEPARAM', obj)
}

/*
  async setNewInvoiceList ({ commit }) {
    let res = await fetch('../../../sale/applicationList.do')
    let list = await res.json()
    console.log('异步返回的数据：', list)
    let applyList2 = []
    let applyList3 = []
    let applyList4 = []
    let applyList5 = []
    if (list && list.length > 0) {
      list.forEach(function (item) {
        let sta = Number(item['state'])
        switch (sta) {
          case 2 :
          case 3 :
          case 4 :
          case 5 :
          case 7 :
            if (sta === 7) { sta = 5 }
            ('applyList' + sta).push(item)
            // let sub = _this.sphdSocket.subscribe('changeStateById', _this.changeCallBack, null, 'custom', item['id'])
            // let sub2 = _this.sphdSocket.subscribe('invoiceHandle', _this.handleCallback, null, 'custom', item['id'])
            // _this.listeners.push(sub)
            // _this.listeners.push(sub2)
            break
          default :
        }
      })
    }
    let listArr = {
      'applyList2': applyList2,
      'applyList3': applyList3,
      'applyList4': applyList4,
      'applyList5': applyList5
    }
    commit('SET_INVOICELIST', { listArr })
  }
*/

export default {
  setNewMenu,
  setNewVersion,
  setNewMsgCount,
  setMsgDisappear,
  setNewHomeNav,
  setOverTimeNav,
  setLeaveNav,
  setNewInvoiceNav,
  setNewInvoiceFinNav,
  setNewFileNav,
  setNewFileList,
  setNewInvoiceList,
  setNewComplaintList,
  setNewComplaintHe,
  setNewComplaintNav,
  setBillParam,
  setReimburseNav,
  setFinReimburseNav,
  setPayReimburseNav,
  setPotentialNav,
  setSaleManageNav,
  setMaterialParam,
  setApprovalSettingSearchInfo,
  setPurchaseNav,
  setPurchaseInvoice,
  setAttendanceLeaveParam,
  setCusorg,
  setPurchaseFinanceInfo,
  setBeRecoveredInfo
}
