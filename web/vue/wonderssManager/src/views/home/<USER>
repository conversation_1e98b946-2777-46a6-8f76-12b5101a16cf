<template>
  <div>
    <el-row class="card_avatar_avatar">
      <el-col :span="6" v-for="(item) in enableButtons" class="card_avatar">
        <div @click="jump(item.url)">
          <div class="icon_avatar">
            <div :class="'icon icon_' + item.iconData"></div>
          </div>
          <div class="icon_name">{{item.name}}</div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import JSONBig from "json-bigint";
export default {
  props: ['homePageAndWorks'],
  data() {
    return {
      enableButtons:[],
    }
  },
  components: {},
  watch: {
    homePageAndWorks: {
      immediate: true,
      deep: true,
      handler(newVal) {
        let popedomList = newVal.popedomList ?? []
        // console.log('gongzuo watch popedomList', popedomList)
        console.log(JSONBig.stringify(popedomList))
        this.mergeButtons(popedomList)
      }
    }
  },
  created() {
  },
  methods: {
    mergeButtons(homePageList) {
      const readybuttons = [
        {iconData: 'zhongshuguankong', url: 'managerManage', mid: 'mb'},//高管管理
        {iconData: 'zhongshuguankong', url: 'centralControl', mid: 'mc'},//中枢管控
        // {iconData: '', url: '', mid: 'ba'},//公司总览
        // {iconData: '', url: '', mid: 'ca'},//手机端工作
        // {iconData: '', url: '', mid: 'ea'},//权限管理
        // {iconData: '', url: '', mid: 'ia'},//启用管理
        // {iconData: '', url: '', mid: 'ja'},//备忘与日程
        // {iconData: '', url: '', mid: 'km'},//工作记录点评
        // {iconData: '', url: '', mid: 'md'},//冻结账号
        // {iconData: '', url: '', mid: 'ra'},//文件与资料
        // {iconData: '', url: '', mid: 'ma'},//个人中心
        // {iconData: '', url: '', mid: 'na'},//参考资料
        // {iconData: '', url: '', mid: 'db'},//日常事务
        // {iconData: '', url: '', mid: 'gb'},//回款录入
        // {iconData: '', url: '', mid: 'gc'},//潜在客户
        // {iconData: '', url: '', mid: 'gd'},//访谈记录
        // {iconData: '', url: '', mid: 'dj'},//跨机构访问
        // {iconData: '', url: '', mid: 'cb'},//浏览管理
        // {iconData: '', url: '', mid: 'ce'},//会议管理
        // {iconData: '', url: '', mid: 'cg'},//资源管理
        // {iconData: '', url: '', mid: 'cm'},//考核成绩公示板
        // {iconData: '', url: '', mid: 'cn'},//公共信息管理
        // {iconData: '', url: '', mid: 'co'},//公共信息
        // {iconData: '', url: '', mid: 'cw'},//考勤打卡
        // {iconData: '', url: '', mid: 'ed'},//当前权限
        // {iconData: '', url: '', mid: 'ee'},//审批查看
        // {iconData: '', url: '', mid: 'eg'},//修改记录
        // {iconData: '', url: '', mid: 'jc'},//事件管理
        // {iconData: '', url: '', mid: 'mf'},//系统设置
        // {iconData: '', url: '', mid: 'mi'},//登录设置
        // {iconData: '', url: '', mid: 'nb'},//类别设置
        // {iconData: '', url: '', mid: 'nc'},//文件管理
        // {iconData: '', url: '', mid: 'rc'},//文件管理
      ]
      let buttonList = JSONBig.parse(JSONBig.stringify(readybuttons))
      this.enableButtons = buttonList.filter(button => {
        const popedom = homePageList.find(item => item.mid === button.mid);
        if (popedom) {
          return Object.assign(button, popedom)
        }
        return false
      })
      console.log('mergeButtons', this.enableButtons)
    },
    jump: function (url) {
      this.$router.push({name: url})
    }
  }
}

</script>
<style lang="scss" scoped>
.card_avatar_avatar{
  .card_avatar{
    text-align: center;
    padding: 16px;
    &:nth-child(even) {
      background: #fafafa;
    }
    .icon_avatar{
      display:inline-block;
      width:48px;
      height:48px;
      position:relative;
      text-align: center;
      background-size: 48px;
      margin-top: 8px;
      border-radius: 8px;
      background: rgba($tyBounceColorGreen, .1);
      margin-bottom: 4px;
    }
    .icon{
      width:48px;
      height:48px;
    }
    .icon_name{
      color: #666;
      text-decoration: none;
      margin-bottom: 8px;
      font-size: 14px;
    }
  }
}
.icon_zhongshuguankong{   background-image: url("@/assets/img/manage.png"); }
</style>
