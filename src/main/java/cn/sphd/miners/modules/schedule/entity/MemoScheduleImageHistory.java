package cn.sphd.miners.modules.schedule.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2019/8/13.
 * 日程附件历史表
 */
@Entity
@Table(name = "t_memo_schedule_image_history")
public class MemoScheduleImageHistory implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="memo_schedule_history"  ,nullable=true , unique=false, insertable=false, updatable=false)
    private Integer scheduleHistoryId;//日程历史表id

    @Column(name="memo_schedule"  ,nullable=true , unique=false)
    private Integer scheduleId;//日程主表id

    @Column(name="schedule_uid"  , length=36 , nullable=true , unique=false)
    private String scheduleUid;   //日程的唯一标识符。 此值用于标识分布式提醒

    @Column(name="title"  , length=255 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="description"  ,length=255 , nullable=true , unique=false)
    private String description;   //详细描述

    @Column(name="normal"  ,length=255 , nullable=true , unique=false)
    private String normal;   //正常图标

    @Column(name="large"  ,length=255 , nullable=true , unique=false)
    private String large;   //大图标

    @Column(name="medium"  ,length=255 , nullable=true , unique=false)
    private String medium;   //中图标

    @Column(name="thumbnail"  ,length=255 , nullable=true , unique=false)
    private String thumbnail;   //缩略图

    @Column(name="orders"  ,nullable=true , unique=false)
    private Integer orders;   //排序

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation; //操作:1-增,2-删,3-改,4-修改图片,5-修改补充资料内容,6-修改补充资料图片',

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录id

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="memo_schedule_history", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MemoScheduleHistory scheduleHistory;//日程历史表集联


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleHistoryId() {
        return scheduleHistoryId;
    }

    public void setScheduleHistoryId(Integer scheduleHistoryId) {
        this.scheduleHistoryId = scheduleHistoryId;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getScheduleUid() {
        return scheduleUid;
    }

    public void setScheduleUid(String scheduleUid) {
        this.scheduleUid = scheduleUid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNormal() {
        return normal;
    }

    public void setNormal(String normal) {
        this.normal = normal;
    }

    public String getLarge() {
        return large;
    }

    public void setLarge(String large) {
        this.large = large;
    }

    public String getMedium() {
        return medium;
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public MemoScheduleHistory getScheduleHistory() {
        return scheduleHistory;
    }

    public void setScheduleHistory(MemoScheduleHistory scheduleHistory) {
        this.scheduleHistory = scheduleHistory;
    }
}
