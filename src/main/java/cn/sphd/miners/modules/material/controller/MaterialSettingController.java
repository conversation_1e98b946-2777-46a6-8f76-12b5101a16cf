package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.modules.iws.entity.TIwsMaterialLocation;
import cn.sphd.miners.modules.iws.service.ITIwsMaterialLocationService;
import cn.sphd.miners.modules.iws.service.impl.TIwsMaterialLocationServiceImpl;
import cn.sphd.miners.modules.material.dao.MtStockInfoDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtStockInfo;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.dao.LocalSettingDao;
import cn.sphd.miners.modules.system.entity.LocalSetting;
import cn.sphd.miners.modules.system.entity.User;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/material/setting")
public class MaterialSettingController {

  @Autowired LocalSettingDao localSettingDao;

  @Autowired
  MtService mtService;

  @Autowired
  ITIwsMaterialLocationService materialLocationService;
  @Autowired
  MtStockInfoDao stockInfoDao;
  @RequestMapping("state")
  @ResponseBody
  public LocalSetting state(LocalSetting localSetting, User user) {
    List<LocalSetting> ls = this.list(localSetting, user);

    LocalSetting localSetting1 =
        localSettingDao.getByHQL(
            "from LocalSetting where value=?0 and category=?1 and org=?2", "1", "2", user.getOid());

    return localSetting1;
  }

  @RequestMapping("list")
  @ResponseBody
  public List<LocalSetting> list(LocalSetting localSetting, User user) {
    String hql = "from LocalSetting where 1=1 and org=:org";
    Map<String, Object> params = new HashMap<>();
    if (localSetting.getKey() != null) {
      hql += " and key = :key";
      params.put("key", localSetting.getKey());
    }
    if (localSetting.getValue() != null) {
      hql += " and value = :value";
      params.put("value", localSetting.getValue());
    }
    params.put("org", user.getOid());
    if (localSetting.getCategory() != null) {
      hql += " and category = :category";
      params.put("category", localSetting.getCategory());
    }
    List<LocalSetting> listByHQLWithNamedParams =
        localSettingDao.getListByHQLWithNamedParams(hql, params);
    if (listByHQLWithNamedParams == null|| listByHQLWithNamedParams.isEmpty()) {
      // 初始化setting
      this.initMaterialSetting(user);
      listByHQLWithNamedParams = localSettingDao.getListByHQLWithNamedParams(hql, params);
    }

    return listByHQLWithNamedParams;
  }

  @RequestMapping("updateSet")
  @ResponseBody
  public AjaxResult set(LocalSetting localSetting, User user) {
    // 更换形式，将其他形式设置为0
    Integer oid = localSetting.getOrg()==null?user.getOid():localSetting.getOrg();
    // 获取当前value为1的设置
    LocalSetting localSetting1 =
        localSettingDao.getByHQL(
            "from LocalSetting where value=1 and category=2 and org=?0", oid);

    if(localSetting1.getId().equals(localSetting.getId())){
      return AjaxResult.warn("请勿重复操作");
    }
    if (localSetting1 != null) {
      localSetting1.setValue("0");
      localSettingDao.update(localSetting1);
    }
    LocalSetting s = localSettingDao.get(localSetting.getId());
    if (s != null) {
      s.setValue("1");
      localSettingDao.update(s);
    }
    return AjaxResult.success();
  }

  //供应商，包装都不区分
  @RequestMapping("initialStock22")
  @ResponseBody
  public AjaxResult initialStock22(User user, Integer material, BigDecimal initialStock,String previous) {
    if(StringUtils.isNotBlank(previous)){
      mtService.deleteMaterialLocation(material);
    }
    mtService.initialStock22(user,material, initialStock,0,0);
    return AjaxResult.success();
  }

  //区分供应商但不区分包装
  @RequestMapping("initialStock12")
  @ResponseBody
  public AjaxResult initialStock12(User user, String json,String previous) throws JsonProcessingException {
    ObjectMapper objectMapper = new ObjectMapper();
    List<TIwsMaterialLocation> materialLocations = objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, TIwsMaterialLocation.class));

    if(StringUtils.isNotBlank(previous)){
      for (TIwsMaterialLocation materialLocation : materialLocations) {
        mtService.deleteMaterialLocation(materialLocation.getMaterial());
      }
    }
    mtService.initialStock12(user, materialLocations,1,0);
    return AjaxResult.success();
  }

  //不区分供应商但是区分包装
  @RequestMapping("initialStock21")
  @ResponseBody
  public AjaxResult initialStock21(User user,String json,String previous) throws JsonProcessingException {
    ObjectMapper objectMapper = new ObjectMapper();
    List<TIwsMaterialLocation> materialLocations = objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, TIwsMaterialLocation.class));

    if(StringUtils.isNotBlank(previous)){
      for (TIwsMaterialLocation materialLocation : materialLocations) {
        mtService.deleteMaterialLocation(materialLocation.getMaterial());
      }
    }
    mtService.initialStock21(user, materialLocations,0,1);
    return AjaxResult.success();
  }

  //区分供应商且区分包装
  @RequestMapping("initialStock11")
  @ResponseBody
  public AjaxResult initialStock11(User user,String json,String previous) throws JsonProcessingException {

    ObjectMapper objectMapper = new ObjectMapper();
    List<TIwsMaterialLocation> materialLocations = objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, TIwsMaterialLocation.class));

    if(StringUtils.isNotBlank(previous)){
      for (TIwsMaterialLocation materialLocation : materialLocations) {
        mtService.deleteMaterialLocation(materialLocation.getMaterial());
      }
    }
    mtService.initialStock21(user, materialLocations,1,1);
    return AjaxResult.success();
  }

  @RequestMapping("currentStock")
  @ResponseBody
  public AjaxResult currentStock(Integer material) {
    var resultMap = new HashMap();
    MtBase mtBase = mtService.getMtById(material);
    List<Map<String, Object>> map = mtService.getStocksDetailByMtId(material);

    if(mtBase.getDripSupplier()==null&&mtBase.getDripPackaging()==null){
      resultMap.put("state", "00");
    }
    else if(mtBase.getDripSupplier()==0&&mtBase.getDripPackaging()==0){
      resultMap.put("state", "22");


    }else if(mtBase.getDripSupplier()==0&&mtBase.getDripPackaging()==1){
      resultMap.put("state", "21");
    }else if(mtBase.getDripSupplier()==1&&mtBase.getDripPackaging()==0){
      resultMap.put("state", "12");
    }else if(mtBase.getDripSupplier()==1&&mtBase.getDripPackaging()==1){
      resultMap.put("state", "11");
    }
    resultMap.put("map", map);

    MtStockInfo stockInfo = stockInfoDao.getByHQL(" from MtStockInfo o where o.material_=?0", material);
    if (stockInfo != null) {
      resultMap.put("currentStock", stockInfo.getCurrentStock());
      resultMap.put("initialStock", stockInfo.getInitialStock());
    }
    return AjaxResult.success(resultMap);
  }

  @RequestMapping("currentStockDetail")
  @ResponseBody
  public AjaxResult currentStockDetail(TIwsMaterialLocation tIwsMaterialLocation) {

    List<Map<String, Object>> map = mtService.getStocksDetailByMtSupplierMaterial(tIwsMaterialLocation);
    return AjaxResult.success(map);
  }

  public void initMaterialSetting(User user) {

    LocalSetting localSetting = new LocalSetting();
    localSetting.setCategory("2");
    localSetting.setKey("00型");
    localSetting.setValue("1");
    localSetting.setMemo("“是否区分供应商”与“是否区分包装”都需要选择");
    localSetting.setOrg(user.getOid());
    localSetting.setEnabled(true);

    localSettingDao.save(localSetting);

    LocalSetting localSetting1 = new LocalSetting();
    localSetting1.setCategory("2");
    localSetting1.setKey("11型");
    localSetting1.setValue("0");
    localSetting1.setMemo("“是否区分供应商”与“是否区分包装”都默认为“区分”");
    localSetting1.setOrg(user.getOid());
    localSetting1.setEnabled(true);

    localSettingDao.save(localSetting1);

    LocalSetting localSetting2 = new LocalSetting();
    localSetting2.setCategory("2");
    localSetting2.setKey("12型");
    localSetting2.setValue("0");
    localSetting2.setMemo("“是否区分供应商”默认为“区分”，“是否区分包装”默认为“不区分”");
    localSetting2.setOrg(user.getOid());
    localSetting2.setEnabled(true);

    localSettingDao.save(localSetting2);

    LocalSetting localSetting3 = new LocalSetting();
    localSetting3.setCategory("2");
    localSetting3.setKey("21型");
    localSetting3.setValue("0");
    localSetting3.setMemo("“是否区分供应商”默认为“不区分”，“是否区分包装”默认为“区分”");
    localSetting3.setOrg(user.getOid());
    localSetting3.setEnabled(true);

    localSettingDao.save(localSetting3);

    LocalSetting localSetting4 = new LocalSetting();
    localSetting4.setCategory("2");
    localSetting4.setKey("22型");
    localSetting4.setValue("0");
    localSetting4.setMemo("“是否区分供应商”与“是否区分包装”都默认为“不区分”");
    localSetting4.setOrg(user.getOid());
    localSetting4.setEnabled(true);

    localSettingDao.save(localSetting4);
  }
}
