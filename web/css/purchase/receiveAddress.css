.mainCon{margin-left: 120px;margin-right: 120px;}
.gapR{margin-right: 40px;}
.panel-box{padding-top: 30px;padding-bottom: 130px;}
.flexBox{width: 100%;box-sizing: border-box;border-radius: 2px;position: relative;font-size: 13px;overflow: hidden;opacity: 1;display: flex;align-items: center;
    justify-content: space-between;transition: opacity .2s;margin-bottom: 8px;}
.careful{color: #0070bf;}
.linkBtn{ color: #0070bf; cursor: pointer; font-weight: bold;}
.blueBg thead td{border-color: #dceaf6;background: #dceaf6}
.sale_gap{margin-right:40px;}
.otherContact{overflow-y: hidden; }
.otherContact li{float:left;margin-bottom:10px;width:330px;}
.otherContact .sale_gap{margin-right: 6px;}
#newContectData .addMore{margin-left: 8px;margin-right: 16px;}
#chooseCusContact .fa{ color: #50a9ea;    }
.sale-con{padding-left:10px;margin-right:20px;min-width: 142px;display: inline-block;line-height:32px;}
.see_otherContact {margin: 20px auto;}
.sale_ttl1{margin-right: 10px;display: inline-block;min-width: 90px;text-align: right;line-height: 32px;vertical-align: top;}
.sale_con1>input{width: 220px;}
.chooseCusCon{margin-right: 16px;}
.chooseCusBtn{ background: #ddd; width: 40px; display:inline-block; text-align: center; line-height: 30px; height: 30px; margin-left: -5px;  }
.linkUploadify .uploadify-button { margin: 0; background-color: inherit; border: none;  line-height: 12px;  padding: 3px 6px;  font-size: 12px;  color: #1685ea;  cursor: pointer;  text-decoration: none;
    font-weight: normal;  display: inline-block;  vertical-align: middle;  }
.linkUploadify .uploadify-button:active{border: none;}
#newContectInfo .uploadify-button{line-height: 28px;}
.uploadify-button:visited{text-decoration: none}
#uploadCard{display:inline-block;}
#ReceiveName,#receiveAreaName {height: 30px;width: 181px;line-height: 30px;}
.bussnessCard{display: inline-block;}
.bussnessCard .filePic{display:inline-block;width: 260px; height: 150px; background-size: contain;  background-repeat: no-repeat;}
.cusList { margin:20px auto; width:90%;   }
.cusList>li{ list-style: none; background:#fff; line-height:30px; padding:0 15px;  }
.cusList>li:nth-child(even){ background:#f5f5f5;  }
.areaA{margin: auto;width: 85%;}
.areaForm {margin: auto;width: 80%;}
.areaForm p span:first-child{width: 180px;text-align: left;}
.contactflex{display:flex;padding-left: 30px;}
.clearVal{position: relative;}
input:focus + i.clearInputVal{  display: block;  }
.clearVal .clearInputVal{position: absolute;  display: none;right: 8px;font-style: normal;color: #7a7b7e;top: -1px;cursor: pointer;}
.xing{margin-right: 4px;}
.xing:before {content: "*";width: 5px;height: 5px;display: inline-block;color: red;font-weight: bolder;position: relative;top: 2px;}
#addMoreContact option[disabled]{background: #ddd;cursor: pointer;}
