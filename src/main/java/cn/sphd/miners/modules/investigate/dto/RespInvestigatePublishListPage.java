package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.investigate.entity.InvestigatePublish;

import java.util.List;

public class RespInvestigatePublishListPage {
    private Integer sum;
    private String orgName;
    private PageInfo pageInfo;
    private List<InvestigatePublish> investigatePublishList;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<InvestigatePublish> getInvestigatePublishList() {
        return investigatePublishList;
    }

    public void setInvestigatePublishList(List<InvestigatePublish> investigatePublishList) {
        this.investigatePublishList = investigatePublishList;
    }
}
