package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquFixedAssetsHistory;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface ITEquFixedAssetsHistoryService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquFixedAssetsHistory selectTEquFixedAssetsHistoryById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquFixedAssetsHistory 
     * @return 集合
     */
    public List<TEquFixedAssetsHistory> selectTEquFixedAssetsHistoryList(TEquFixedAssetsHistory tEquFixedAssetsHistory);

    /**
     * 新增
     * 
     * @param tEquFixedAssetsHistory 
     * @return 结果
     */
    public int insertTEquFixedAssetsHistory(TEquFixedAssetsHistory tEquFixedAssetsHistory);

    /**
     * 修改
     * 
     * @param tEquFixedAssetsHistory 
     * @return 结果
     */
    public int updateTEquFixedAssetsHistory(TEquFixedAssetsHistory tEquFixedAssetsHistory);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTEquFixedAssetsHistoryByIds(String ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquFixedAssetsHistoryById(Long id);
}
