package cn.sphd.miners.modules.thirdPlatform.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.HttpClientUtils;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.thirdPlatform.entity.TpApp;
import cn.sphd.miners.modules.thirdPlatform.entity.TpMember;
import cn.sphd.miners.modules.thirdPlatform.entity.TpMerchant;
import cn.sphd.miners.modules.thirdPlatform.entity.TpPlatform;
import cn.sphd.miners.modules.thirdPlatform.service.TgService;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022年06月07日 16:44
 **/
@Service("ttTgService")
public class TtTgServiceImpl implements TgService {
    @Autowired
    ThirdPlatformService platformService;
    @Autowired
    DlmService dlmService;
    @Override
    public void getTpAccessTokenToRedis(TpPlatform platform) {
        Logger.getLogger(getClass()).warn("TT getTpAccessTokenToRedis not implement yet");
    }

    @Override
    public void getAppAccessTokenToRedis(TpApp app) {
        //"https://developer.toutiao.com/api/apps/v2/token?grant_type=client_credential&appid=&secret="
        String url = "https://developer.toutiao.com/api/apps/v2/token";
        Map<String, String> headers = new HashMap<>(1);
        headers.put("content", "application/json");
        Map<String, String> bodyMap = new HashMap<>(3);
        bodyMap.put("grant_type", "client_credential");
        bodyMap.put("appid", app.getAppId());
        bodyMap.put("secret", app.getAppSecret());
        HttpClientUtils client = new HttpClientUtils(url, true);
        Map<String, Object> response = client.jsonResponseToT(client.doPost(null, url, headers, null, JSON.toJSONString(bodyMap)),Map.class);
        Map<String, Object> tokenMap = new HashMap<>(2);
        String accessToken;
        Long expiresIn;
        Map<String, Object> data;
        if (null != response
                && null != (data = (Map<String, Object>) response.getOrDefault("data",null))
                && StringUtils.isNotEmpty(accessToken = String.valueOf(data.getOrDefault("access_token",null)))
                && 0L < (expiresIn = Long.valueOf(String.valueOf(data.getOrDefault("expires_in",0L))))) {
            tokenMap.put("accessToken",accessToken);
            tokenMap.put("expiresIn", expiresIn);
            platformService.saveAppAccessTokenToRedis(app, tokenMap);
        } else { //出错处理
            StringBuffer msg = new StringBuffer("ttTgService.getAppAccessToken");
            if(response == null) {
                msg.append("获取token失败  appid：" + app.getAppId() + "请检查是否是公众号白名单的问题。");
            } else {
                msg.append("获取token失败  appid：" + app.getAppId() + " err_no:{} err_tips:{} \n" + JSON.toJSONString(response) + "\n请检查是否是白名单的问题。");
            }
            Logger.getLogger(getClass()).error(msg.append("\n是不是服务器ip地址变了，检查公众号白名单，AppId="+app.getAppId()).toString());
        }
    }

    @Override
    public void receiveAuthEvent(String postData, String signature, String timeStamp, String nonce, String encryptType, String msgSignature) {
        Logger.getLogger(getClass()).warn("TT receiveAuthEvent not implement yet");
    }

    @Override
    public void savePayNotify(Map<String, Object> params) {
        Logger.getLogger(getClass()).warn("TT receivePayNotify not implement yet");
    }

    @Override
    public Pair<TpMember, MyException> code2Member(TpApp app, String code, Map<String, String> userInfo) {
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName() + "|" + app.getTgCode() + "|" + code;
        while ((lockKey=dlmService.getLock(methodName, TimeUnit.MINUTES.toMillis(1)))== null) {//wyu:防止重复调用，加锁1分钟（HttpClientUtils超时时间是1分钟）
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                Logger.getLogger(getClass()).info("TT Thread.sleep error", e);
                Thread.currentThread().interrupt();
                return null;
            }
        }
        Map<String, Object> dataMap;
        TpMember member;
        Long memberId;
        Pair<TpMember, MyException> result;
        //从缓存和数据库读取
        if (ObjectUtils.isNotEmpty(dataMap = platformService.getMemberSessionKeyRedis(app, code))
            && (memberId = (Long) dataMap.get("memberId"))!=null
            && (member = platformService.getMemberById(memberId)) != null ) {
            platformService.fillMemberUserInfoWithMap(member, userInfo);
            result = Pair.of(member, null);
        } else {//第三方接口调用
            String url = "https://developer.toutiao.com/api/apps/v2/jscode2session";
            Map<String, String> headers = new HashMap<>(1);
            headers.put("content", "application/json");
            Map<String, String> bodyMap = new HashMap<>(4);
            bodyMap.put("appid", app.getAppId());
            bodyMap.put("secret", app.getAppSecret());
            bodyMap.put("code", code);
            bodyMap.put("anonymous_code", "");
            HttpClientUtils client = new HttpClientUtils(url, true);
            Map<String, Object> response = client.jsonResponseToT(client.doPost(null, url, headers, null, JSON.toJSONString(bodyMap)), Map.class);
            dataMap = new HashMap<>(4);
            String sessionKey, openId, unionId;
            Map<String, Object> data;
            if (null != response
                    && null != (data = (Map<String, Object>) response.get("data"))) {
                if (StringUtils.isNotEmpty(sessionKey = (String) data.get("session_key"))
                        && StringUtils.isNotEmpty(openId = (String) data.get("openid"))
                        && StringUtils.isNotEmpty(unionId = (String) data.get("unionid"))) {
                    dataMap.put("session_key", sessionKey);
                    dataMap.put("openid", openId);
                    dataMap.put("unionid", unionId);
                    member = platformService.getByAppUnionId(app, unionId, openId);
                    if (member != null && member.getId() != null) {
                        platformService.fillMemberUserInfoWithMap(member, userInfo);
                        dataMap.put("memberId", member.getId());
                        //保存session_key到redis
                        platformService.saveMemberSessionKeyRedis(app, code, dataMap);
                        System.out.println("code2SessionMember member:" + JSON.toJSONString(member));
                        result = Pair.of(member, null);
                    } else {//返回错误信息
                        Logger.getLogger(getClass()).error("生成TpMember失败：" + JSON.toJSONString(response));
                        result = Pair.of(null, new MyException("5", "创建对象失败, 可能是数据库问题"));
                    }
                } else {//返回错误信息
                    Logger.getLogger(getClass()).error("获取code2Session失败，第三方服务器返回错误信息：" + JSON.toJSONString(response));
                    result = Pair.of(null, new MyException("4", "login error, 可能是授权或者code问题，建议重试tt.login"));
                }
            } else { //网络出错处理
                StringBuffer msg = new StringBuffer("ttTgService.code2SessionMember");
                if (response == null) {
                    msg.append("获取code2Session失败  appid：" + app.getAppId() + "请检查是否是公众号白名单的问题。");
                } else {
                    msg.append("获取code2Session失败  appid：" + app.getAppId() + ", code=" + code + " err_no:{} err_tips:{} \n" + JSON.toJSONString(response) + "\n请检查是否是白名单的问题。");
                }
                Logger.getLogger(getClass()).error(msg.append("\n是不是服务器ip地址变了"));
                result = Pair.of(null, new MyException("5", msg.insert(0, "可能是小程序后台配置问题, 错误信息：").toString()));
            }
        }
        dlmService.releaseLock(methodName, lockKey);
        return result;
    }

    @Override
    public void initMerchantService(TpMerchant merchant) {
    }

    @Override
    public JsonResult preOrder(HttpServletRequest request, EcOrder order) {
        JsonResult result = new JsonResult();
        result.setError(new MyException("-128", "字节跳动支付未实现，请联系开发人员检查代码和数据库配置！"));
        return result;
    }

    @Override
    public JsonResult refundOrder(HttpServletRequest request, EcOrder order) {
        JsonResult result = new JsonResult();
        result.setError(new MyException("-128", "字节跳动支付退款未实现，请联系开发人员检查代码和数据库配置！"));
        return result;
    }
}