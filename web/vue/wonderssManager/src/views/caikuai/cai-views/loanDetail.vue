
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>

    <div v-if="info">
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li>
          <span>{{ info.loanDetail.createName }}
            {{ new Date(info.loanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}创建
          </span>
          <span class="ty-linkBtn" @click="editLog"> 修改记录 </span>
        </li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>本金金额</span><span>{{ formatCurrency(info.loanDetail.principalAmount) }}</span></li>
        <div v-if="info.loanDetail.loanType === '1'">
          <li><span>出资方</span><span>{{ (info.loanDetail.lender) }}</span></li>
          <li><span>本金型式</span><span>{{ info.loanDetail.incomeMethod === '3' ? (info.loanDetail.withinOrAbroad === '1' ? '内部':'外部' ): '' }}{{ chargePayMethod(info.loanDetail.incomeMethod) }}</span></li>
          <li><span>收款日期</span><span>{{ new Date(info.loanDetail.paymentDate).format('yyyy-MM-dd') }}</span></li>
          <li><span>收款经手人</span><span>{{ (info.loanDetail.partnerName) }}</span></li>
        </div>
        <div v-else>
          <li><span>收款方</span><span>{{ (info.loanDetail.borrower) }}</span></li>
          <li><span>本金型式</span><span>{{ info.loanDetail.incomeMethod === '3' ? (info.loanDetail.withinOrAbroad === '1' ? '内部':'外部' ): '' }}{{ chargePayMethod(info.loanDetail.incomeMethod) }}</span></li>
          <div v-if="info.loanDetail.incomeMethod === '3'">
            <li><span>银行账户</span><span>{{ info.loanDetail.account  }}</span></li>
            <li><span>支票号</span><span>{{ info.loanDetail.billNo  }}</span></li>
            <li><span>支票到期日</span><span>{{ new Date(info.loanDetail.billEndDate).format('yyyy-MM-dd') }}</span></li>
          </div>
          <li><span>付款日期</span><span>{{ new Date(info.loanDetail.paymentDate).format('yyyy-MM-dd') }}</span></li>
          <div v-if="info.loanDetail.incomeMethod === '5'">
            <li><span>付款银行</span><span>{{ info.loanDetail.receiveBank }}</span></li>
          </div>
          <li><span>付款经手人</span><span>{{ (info.loanDetail.operatorName) }}</span></li>
          <div v-if="info.loanDetail.incomeMethod === '5'">
            <li><span>借款方账号名称</span><span>{{ info.loanDetail.oppositeAccount }}</span></li>
            <li><span>开户行</span><span>{{ info.loanDetail.oppositeBankno }}</span></li>
            <li><span>账号</span><span>{{ info.loanDetail.oppositeBankcode }}</span></li>
          </div>
          <li v-if="info.loanDetail.partnerName"><span>收款经手人</span><span>{{ (info.loanDetail.partnerName) }}</span></li>
        </div>

        <li><span>应归还本金的日期</span><span>{{ info.loanDetail.repaymentDate ? (new Date(info.loanDetail.repaymentDate).format('yyyy-MM-dd') )  : '未约定具体日期'  }}</span></li>
        <li><span>名义利率</span><span>{{ (info.loanDetail.nominalRate *100) }}%</span></li>
        <li><span>利息的支付方式</span><span>{{ chargeInterestMethod(info.loanDetail.interestMethod ) }}</span></li>
        <div v-if="info.loanDetail.interestMethod != 0">
          <li><span>开始计息日期</span><span>{{ new Date(info.loanDetail.interestAccrualDate).format('yyyy-MM-dd') }}</span></li>
          <li><span>每月还款日</span><span>{{ (info.loanDetail.periodRepaymentDate ) }}</span></li>
          <li><span>每次应付款金额</span><span>{{ (info.loanDetail.periodRepaymentAmount ) }}</span></li>
        </div>
        <li><span>备注</span><span class="turnHang">{{ (info.loanDetail.memo ) }}</span></li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>已付款金额 {{ formatCurrency(info.sumRepaymentAmount || 0 ) }} 元</span><span class="ty-linkBtn" @click="payLog">付款记录</span></li>
      </ul>
      <div class="hr_gray"></div>

    </div>



  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "loanDetail",
  data() {
    return {
      info: null,
    }
  },
  mixins: [finance],
  mounted() {
    let loanDetail = localStorage.getItem('loanDetail')
    loanDetail = JSON.parse(loanDetail)
    this.info = loanDetail
  },
  methods: {
    payLog(){
      let repaymentList = this.info.repaymentList || []
      if(repaymentList.length === 0){
        this.$message.info('暂无付款记录')
      }else{
        localStorage.setItem('loanRepayList', JSON.stringify(repaymentList))
        this.$router.push("loanRepayList")
      }
    },
    editLog(){
      var borrorModList = this.info.borrorModList || []
      if(borrorModList.length === 0){
        this.$message.info('暂无修改记录')
      }else{
        localStorage.setItem('loanEditList', JSON.stringify(borrorModList))
        this.$router.push("loanEditList")
      }
    },

  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.turnHang{
  display: inline-block;  white-space:normal; line-height: 20px;
  max-width: 60%;
}
.loan{
  .fa-angle-right{ font-size: 20px;  }

}
</style>