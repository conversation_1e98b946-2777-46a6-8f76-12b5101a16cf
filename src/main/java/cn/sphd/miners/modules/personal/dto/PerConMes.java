package cn.sphd.miners.modules.personal.dto;

import java.io.Serializable;
import java.util.Date;

public class PerConMes implements Serializable {

    protected final long serialVersionUID = 1L;

    private Integer org;
    private Integer user;
    private String username;
    private Date validEnd;

    public long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Date getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(Date validEnd) {
        this.validEnd = validEnd;
    }

    public PerConMes(){

    }

    public PerConMes(Integer org, Integer user, String username, Date validEnd) {
        this.org = org;
        this.user = user;
        this.username = username;
        this.validEnd = validEnd;
    }
}
