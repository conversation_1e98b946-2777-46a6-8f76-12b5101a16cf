package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdCommodityMedia;
import cn.sphd.miners.modules.commodity.entity.PdCommodityMediaHistory;

import java.io.Serializable;

public interface PdCommodityMediaHistoryDao extends IBaseDao<PdCommodityMediaHistory, Serializable> {
    //phId 商品历史id
    PdCommodityMediaHistory insert(PdCommodityMedia pdCommodityMedia,Integer phId);
}
