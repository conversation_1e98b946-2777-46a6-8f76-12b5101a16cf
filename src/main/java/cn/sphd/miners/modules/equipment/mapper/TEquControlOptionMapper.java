package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquControlOption;

import java.util.List;

/**
 * 装备管理_控制选项
 * 20240407 1.292机器设备之初始设置 新增Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
public interface TEquControlOptionMapper {
    /**
     * 查询装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param id 装备管理_控制选项
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     */
    public TEquControlOption selectTEquControlOptionById(Long id);

    /**
     * 查询装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增列表
     *
     * @param tEquControlOption 装备管理_控制选项
     *                          20240407 1.292机器设备之初始设置 新增
     * @return 装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增集合
     */
    public List<TEquControlOption> selectTEquControlOptionList(TEquControlOption tEquControlOption);

    /**
     * 新增装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlOption 装备管理_控制选项
     *                          20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    public int insertTEquControlOption(TEquControlOption tEquControlOption);

    /**
     * 修改装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlOption 装备管理_控制选项
     *                          20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    public int updateTEquControlOption(TEquControlOption tEquControlOption);

    /**
     * 删除装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param id 装备管理_控制选项
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 结果
     */
    public int deleteTEquControlOptionById(Long id);

    /**
     * 批量删除装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquControlOptionByIds(String[] ids);

    TEquControlOption selectTEquControlOptionByNameAndOrg(TEquControlOption tEquControlOption);
}
