package cn.sphd.miners.modules.material.entity;


import java.sql.*;
import java.util.Date;

import java.io.Serializable;
import javax.persistence.*;


@Entity (name="MtMaterialApply")
@Table (name="t_mt_material_apply")
//物料_入库申请单
public class MtMaterialApply implements Serializable {

    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="applicant"   , nullable=true , unique=false)
    private Integer applicant;

    @Column(name="oid"   , nullable=true , unique=false)
    private Integer oid;

    @Column(name="applicant_name"  , length=255 , nullable=true , unique=false)
    private String applicantName; 

    @Column(name="instance"   , nullable=true , unique=false)
    private Integer instance; 

    @Column(name="parent"   , nullable=true , unique=false)
    private Integer parent; 

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type; 

    @Column(name="reason"  , length=255 , nullable=true , unique=false)
    private String reason; 

    @Column(name="action"  , length=255 , nullable=true , unique=false)
    private String action; 

    @Column(name="apply_date"   , nullable=true , unique=false)
    private Date applyDate; 

    @Column(name="apply_type"  , length=1 , nullable=true , unique=false)
    private String applyType; 

    @Column(name="quantity_plan"   , nullable=true , unique=false)
    private Long quantityPlan; 

    @Column(name="quantity_fact"   , nullable=true , unique=false)
    private Long quantityFact; 

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state; 

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo; 

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description; 

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator; 

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName; 

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator; 

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName; 

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;





    public Integer getOid() {
        return oid;
    }

    public void setOid(Integer oid) {
        this.oid = oid;
    }

    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    

    public Integer getApplicant() {
        return applicant;
    }
	
    public void setApplicant (Integer applicant) {
        this.applicant =  applicant;
    }
	

    public String getApplicantName() {
        return applicantName;
    }
	
    public void setApplicantName (String applicantName) {
        this.applicantName =  applicantName;
    }
	

    public Integer getInstance() {
        return instance;
    }
	
    public void setInstance (Integer instance) {
        this.instance =  instance;
    }
	

    public Integer getParent() {
        return parent;
    }
	
    public void setParent (Integer parent) {
        this.parent =  parent;
    }
	

    public String getType() {
        return type;
    }
	
    public void setType (String type) {
        this.type =  type;
    }
	

    public String getReason() {
        return reason;
    }
	
    public void setReason (String reason) {
        this.reason =  reason;
    }
	

    public String getAction() {
        return action;
    }
	
    public void setAction (String action) {
        this.action =  action;
    }
	

    public Date getApplyDate() {
        return applyDate;
    }
	
    public void setApplyDate (Date applyDate) {
        this.applyDate =  applyDate;
    }
	

    public String getApplyType() {
        return applyType;
    }
	
    public void setApplyType (String applyType) {
        this.applyType =  applyType;
    }
	

    public Long getQuantityPlan() {
        return quantityPlan;
    }
	
    public void setQuantityPlan (Long quantityPlan) {
        this.quantityPlan =  quantityPlan;
    }
	

    public Long getQuantityFact() {
        return quantityFact;
    }
	
    public void setQuantityFact (Long quantityFact) {
        this.quantityFact =  quantityFact;
    }
	

    public String getState() {
        return state;
    }
	
    public void setState (String state) {
        this.state =  state;
    }
	

    public String getMemo() {
        return memo;
    }
	
    public void setMemo (String memo) {
        this.memo =  memo;
    }
	

    public String getDescription() {
        return description;
    }
	
    public void setDescription (String description) {
        this.description =  description;
    }
	

    public Integer getCreator() {
        return creator;
    }
	
    public void setCreator (Integer creator) {
        this.creator =  creator;
    }
	

    public String getCreateName() {
        return createName;
    }
	
    public void setCreateName (String createName) {
        this.createName =  createName;
    }
	

	

    public Integer getUpdator() {
        return updator;
    }
	
    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }

    public String getUpdateName() {
        return updateName;
    }
	
    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }


    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
