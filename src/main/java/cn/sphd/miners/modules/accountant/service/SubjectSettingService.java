package cn.sphd.miners.modules.accountant.service;

import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.List;

/**
 * Created by root on 17-1-4.
 */
public interface SubjectSettingService {

    List idesBySubjectCategory(String parent);

    List<TAccountantSubjectEntity> listAccountantSubject(HashMap param);

    String firstSubject(String subject, Integer oid);

    JSONObject newAccountantSubject(String parent, Integer oid, String name, String measureUnit,
                                    Integer staffId, String staffName, String newsubject, String quantityAssistingAccounting,
                                    String relevanceType, Integer relevanceItem);

    TAccountantSubjectEntity getOneAccountSubject(String subject, Integer oid);

    HashMap subjectMessage(List subjectes, Integer oid, String subject);

    Integer updateSubjectMessagw(User user,TAccountantSubjectEntity param);

    String subjectState(List subjectes, Integer oid, String state,User user);

    List<TAccountantSubjectEntity> listChildSubject(String subject, Integer oid);

    //得到资产负债表
    HashMap listByTBalanceSheet(Integer oid, String beginDate, String peroid, Integer code,String state);

    //资产负债的计算过程
    List<TBalanceSheet> balanceSheetComputationalProcess(Integer oid, Integer code, String beginDate, String endDate,
                                                         String period, String state);

    //得到利润表
    HashMap maoByProfitStatement(Integer oid, String beginDate, String peroid, Integer code,String state);

    //利润表的计算过程
    List<ProfitStatement> profitStatementComputationalProcess(Integer oid, Integer code, String beginDate,
                                                              String endDate, String period, String state);

    //试算
    Integer tentativeConculationstatus(User user);

    //结账
    Integer closeAccountsStatus(User user, String state);

    //反结账
    Integer backCloseAccountsStatus(User user);

    //查询结账表此时状态
    TAccountantSettle settingStatus(Integer oid);

    //结转损益时做的工作
    boolean carryOver(User user);

    int deleteSubject(int oid, String subject);

    int checkPerentState(String subject, String state, int oid);

    //会计模块初始化
    int accountInitialization(Integer org,Integer userId,String userName);

    //得到指定的科目信息（1413和********）
    List<TAccountantSubjectEntity> getSpecifySubjectInfo(int oid);

    //根据名称检查是否重复
    String checkNameDuplicate(int oid, String name,String parent);

    //生成物料相关科目
    int generateSubjects(User user, MaterialSubjectParam param);

    //检查是否显示报税按钮
    int isDisplayTaxBtn(int oid);

    //检查某科目是否重复
    String checkRepeat(String subject, String name,Integer id);

    /*
    * 返回低值易耗品1413和固定资产1601下的所有科目
    * */
    List<String> getEquipmentToolsSubjectNames(User user);


    TAccountantSetting initKJinfo(User user);
}
