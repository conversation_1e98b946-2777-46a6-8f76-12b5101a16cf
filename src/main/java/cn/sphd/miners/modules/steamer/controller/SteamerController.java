package cn.sphd.miners.modules.steamer.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.lyric.entity.CmwPlaylist;
import cn.sphd.miners.modules.lyric.entity.CmwSongs;
import cn.sphd.miners.modules.lyric.service.LyricService;
import cn.sphd.miners.modules.steamer.dto.PlaylistSongsDto;
import cn.sphd.miners.modules.steamer.service.SteamerService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

@Controller
@RequestMapping("/steamer")
public class SteamerController {

    @Autowired
    SteamerService steamerService;
    @Autowired
    UserService userService;
    @Autowired
    LyricService lyricService;

    //字幕精灵新增歌曲
    @ResponseBody
    @RequestMapping("/steamerAddSongs.do")
    public JsonResult steamerAddSongs(User user, CmwSongs cmwSongs, String module){
        lyricService.addSongsBySteamer(user,cmwSongs,module);
        Map<String, Object> map = new HashMap<>();
        map.put("state", 1);
        return new JsonResult(1, map);
    }

    //获取歌曲
    @ResponseBody
    @RequestMapping("/steamerGetSongs.do")
    public JsonResult steamerGetSongs(User user, Long id){
        List<CmwSongs> listAllSongs = steamerService.getPlayListSongs(user,id);
        Map<String, Object> map = new HashMap<>();
        map.put("listAllSongs", listAllSongs);
        return new JsonResult(1, map);
    }

    //初始页面
    @ResponseBody
    @RequestMapping("/minePlayList.do")
    public JsonResult minePlayList(User user, String time){
        Map<String, Object> map = steamerService.getAllPlaylist(user, time);
        return new JsonResult(1, map);
    }

    //点击节目单获取节目单信息
    @ResponseBody
    @RequestMapping("/getPlayListMes.do")
    public JsonResult getPlayListMes(User user, Long id){
        Map<String, Object> map = steamerService.getPlaylistMes(user, id);
        return new JsonResult(1, map);
    }

    //点击歌单歌曲列表更多按钮获取歌曲
    @ResponseBody
    @RequestMapping("/getMoreSongs.do")
    public JsonResult getMoreSongs(User user, Long id, String name, PageInfo pageInfo){
        List<PlaylistSongsDto> list = steamerService.moreSongs(user, id, name, pageInfo);
        Map<String, Object> map = new HashMap<>();
        map.put("listSongs", list);
        map.put("pageInfo", pageInfo);
        return new JsonResult(1, map);
    }

    //新增歌单
    @ResponseBody
    @RequestMapping("/addPlayList.do")
    public JsonResult addPlayList(User user, String liveDate, String songIds){
        Integer state = steamerService.addPlayList(user, liveDate, songIds);
        Map<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //修改歌单
    @ResponseBody
    @RequestMapping("/upPlayList.do")
    public JsonResult upPlayList(User user, Long id, String songIds, String liveDate){
        Integer state = steamerService.upPlayList(user, id, songIds, liveDate);
        Map<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //删除歌单
    @ResponseBody
    @RequestMapping("/delPlayList.do")
    public JsonResult delPlayList(Long id){
        Integer state = steamerService.delPlayList(id);
        Map<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //移除歌曲
    @ResponseBody
    @RequestMapping("/removePlayListSong.do")
    public JsonResult removePlayListSong(Long id, Integer playListNum){
        Integer state = steamerService.removePlayListSong(id, playListNum);
        Map<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }





    @ResponseBody
    @RequestMapping("/test.do")
    public JsonResult test(User user, Long id){

        JSONObject json1 = new JSONObject();
        json1.put("songs", Long.valueOf("1"));
        JSONObject json2 = new JSONObject();
        json2.put("songs", Long.valueOf("2"));
        JSONObject json3 = new JSONObject();
        json3.put("songs", Long.valueOf("3"));
        JSONArray jsonArray = new JSONArray();
        jsonArray.put(json1);
        jsonArray.put(json2);
        jsonArray.put(json3);
        String json = jsonArray.toString();
        //steamerService.test(json);
        return null;
    }



}
