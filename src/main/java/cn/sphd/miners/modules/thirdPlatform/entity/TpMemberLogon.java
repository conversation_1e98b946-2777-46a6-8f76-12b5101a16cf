package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table ( name ="t_tp_member_logon" )
public class TpMemberLogon implements Serializable {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    /**
     * 应用ID
     */
    @Column(name = "app", nullable=true , unique=false )
    private Long app;

    /**
     * 子机构ID
     */
    @Column(name = "sub_org", nullable=true , unique=false )
    private Long subOrg;

    /**
     * 第三方应用帐号
     */
    @Column(name = "app_acc", length=128, nullable=true , unique=false )
    private String appAcc;

    /**
     * 会员ID
     */
    @Column(name = "member", nullable=true , unique=false )
    private Long member;

    /**
     * 会员union_id
     */
    @Column(name = "union_id", length=128, nullable=true , unique=false )
    private String unionId;

    /**
     * 会员union_id
     */
    @Column(name = "open_id", length=128, nullable=true , unique=false )
    private String openId;

    /**
     * 手机号
     */
    @Column(name = "mobile", length=50, nullable=true , unique=false )
    private String mobile;

    /**
     * 昵称
     */
    @Column(name = "nick_name", length=50, nullable=true , unique=false )
    private String nick_name;

    /**
     * 用户访问令牌(Token)
     */
    @Column(name = "token", length=128, nullable=true , unique=false )
    private String token;

    /**
     * session_key
     */
    @Column(name = "session_key", length=128, nullable=true , unique=false )
    private String sessionKey;

    /**
     * 访问设备UA
     */
    @Column(name = "ua", length=128, nullable=true , unique=false )
    private String ua;

    /**
     * 访问设备uuid
     */
    @Column(name = "device_uuid", length=128, nullable=true , unique=false )
    private String deviceUuid;

    /**
     * 火星经度
     */
    @Column(name = "x_wgs84", nullable=true , unique=false )
    private Long xWgs84;

    /**
     * 火星纬度
     */
    @Column(name = "y_wgs84", nullable=true , unique=false )
    private Long yWgs84;

    /**
     * 机构ID
     */
    @Column(name = "org", nullable=true , unique=false )
    private Integer org;

    /**
     * 创建人id
     */
    @Column(name = "creator", nullable=true , unique=false )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name", length=100, nullable=true , unique=false )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable=true , unique=false )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator", nullable=true , unique=false )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name", length=100, nullable=true , unique=false )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time", nullable=true , unique=false )
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApp() {
        return app;
    }

    public void setApp(Long app) {
        this.app = app;
    }

    public String getAppAcc() {
        return appAcc;
    }

    public void setAppAcc(String appAcc) {
        this.appAcc = appAcc;
    }

    public Long getMember() {
        return member;
    }

    public void setMember(Long member) {
        this.member = member;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNick_name() {
        return nick_name;
    }

    public void setNick_name(String nick_name) {
        this.nick_name = nick_name;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getDeviceUuid() {
        return deviceUuid;
    }

    public void setDeviceUuid(String deviceUuid) {
        this.deviceUuid = deviceUuid;
    }

    public Long getxWgs84() {
        return xWgs84;
    }

    public void setxWgs84(Long xWgs84) {
        this.xWgs84 = xWgs84;
    }

    public Long getyWgs84() {
        return yWgs84;
    }

    public void setyWgs84(Long yWgs84) {
        this.yWgs84 = yWgs84;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getSubOrg() {
        return subOrg;
    }

    public void setSubOrg(Long subOrg) {
        this.subOrg = subOrg;
    }
}
