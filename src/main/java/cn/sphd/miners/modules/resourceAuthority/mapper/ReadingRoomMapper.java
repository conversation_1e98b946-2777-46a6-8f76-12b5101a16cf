package cn.sphd.miners.modules.resourceAuthority.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.resourceAuthority.entity.ReadingRoomEntity;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2020/10/23.
 */
@Component
public interface ReadingRoomMapper extends BaseMapper<ReadingRoomEntity> {

    /*借阅文件*/

    //获取待审批和已批准的借阅文件
    List<ReadingRoomEntity> getApplyReadFile(ReadingRoomEntity readingRoomEntity);

    //分页获取待审批和已批准的借阅文件（用于查询）
    List<ReadingRoomEntity> getReadFileByApproveStatuslistPage(QueryData query);

    //获取未看的借阅文件的数量
    Integer countUnreadFile(ReadingRoomEntity readingRoomEntity);

    //获取审批人“我的借阅申请”中的待审批和已批准
    List<ReadingRoomEntity> getApproveReadFile(QueryData query);

    Integer countApproveReadFile(Integer userId);

    //分页取审批人“我的借阅申请”中的待审批和已批准（用于查询）
    List<ReadingRoomEntity> getApproveReadFilelistPage(QueryData query);

    //获取“文件借阅审批”（终审借阅文件）
    List<ReadingRoomEntity> getLastApproveReadFile(QueryData query);

    Integer countLastApproveReadFile(QueryData query);

    //分页获取“文件借阅审批”（终审借阅文件）
    List<ReadingRoomEntity> getLastApproveReadFilelistPage(QueryData query);

    //根据id获取一条用于推送借阅文件
    ReadingRoomEntity readingRoomSingle(Integer id);

    //获取30天之前且没有阅览的文件
    List<ReadingRoomEntity> getDaysAgoReadFile(Date timeBegin);

    //获取某文件夹下被借阅的文件
    List<ReadingRoomEntity> getReadRoomByCategoryPath(HashMap<String, Object> map);

    //获取某个文下被借阅的文件
    List<ReadingRoomEntity> getReadRoomByFileId(HashMap<String, Object> map);

    //获取某个历史文件被借阅的记录
    List<ReadingRoomEntity> getReadRoomByHisFileId(Integer hisFileId);

    /*借阅讨论*/

    //获取30天之前且没有阅览的讨论
    List<ReadingRoomEntity> getDaysAgoReadForum(Date timeBegin);

    //根据id获取一条用于推送借阅讨论
    ReadingRoomEntity readingRoomSingleByForum(Integer id);

    //获取待审批和已批准的借阅讨论
    List<ReadingRoomEntity> getApplyReadForum(ReadingRoomEntity readingRoomEntity);

    //分页获取待审批和已批准的借阅讨论（用于查询）
    List<ReadingRoomEntity> getReadForumByApproveStatuslistPage(QueryData query);

    //获取未看的借阅讨论的数量
    Integer countUnreadForum(ReadingRoomEntity readingRoomEntity);

    //获取审批人"讨论借阅"中的待审批和已批准
    List<ReadingRoomEntity> getApproveReadForum(QueryData query);

    Integer countApproveReadForum(Integer userId);

    //分页取审批人“讨论借阅”中的待审批和已批准（用于查询）
    List<ReadingRoomEntity> getApproveReadForumlistPage(QueryData query);

    //获取“讨论借阅审批”（终审借阅讨论）
    List<ReadingRoomEntity> getLastApproveReadForum(QueryData query);

    Integer countLastApproveReadForum(HashMap map);

    //分页获取“讨论借阅审批”（终审借阅文件）
    List<ReadingRoomEntity> getLastApproveReadForumlistPage(QueryData query);

}
