package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.AmountRecoveredService;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.loan.dao.LoanBizDao;
import cn.sphd.miners.modules.loan.entity.LoanBiz;
import cn.sphd.miners.modules.loan.service.LoanBizService;
import cn.sphd.miners.modules.loan.service.OverpaymentService;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.purchaseInvoice.dao.PoPaymentApplicationDao;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentApplication;
import cn.sphd.miners.modules.purchaseInvoice.service.PurchaseInvoiceService;
import cn.sphd.miners.modules.sales.dao.PoOrdersPrepaymentDao;
import cn.sphd.miners.modules.sales.model.PoOrdersPrepayment;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Administrator on 2022/12/30
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class AmountRecoveredServiceImpl implements AmountRecoveredService {

    @Autowired
    PurchaseInvoiceService purchaseInvoiceService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    AccountService accountService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    LoanBizService loanBizService;
    @Autowired
    OverpaymentService overpaymentService;
    @Autowired
    RoleService roleService;

    @Autowired
    PoLoanDao poLoanDao;
    @Autowired
    PoOrdersPrepaymentDao poOrdersPrepaymentDao;
    @Autowired
    PoPaymentApplicationDao poPaymentApplicationDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    FinanceReceiptDao financeReceiptDao;
    @Autowired
    LoanBizDao loanBizDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    FinanceChequeDetailDao financeChequeDetailDao;
    @Autowired
    FinanceAccountDao financeAccountDao;

    @Override
    public List<Map<String,Object>> getPoLoanList(Integer org,Integer toUser,Integer fromUser,String approveStatus,Integer business,Integer businessType) {
        Map<String,Object> map = new HashMap<>();
        List<Map<String, Object>> loanBizList = new ArrayList<>();

        for (int i=0;i<2;i++) {
            String hql = "";
            if (i==0) {
                //除了支付多付款之外的，因为客户不一样
//                hql = "select pl.id,pl.amount,pl.createName,pl.createDate,ss.name,ss.codeName from PoLoan pl,SrmSupplier ss where" +
//                        " pl.finishTime is null and pl.supplier=ss.id and pl.id in (select business from ApprovalProcess where org=:org";
                 hql = "select lb.id,lb.amount,lb.createName,lb.createDate,lb.supplierName,ss.codeName from LoanBiz lb,SrmSupplier ss where" +
                    " lb.finishTime is null and lb.supplier=ss.id and lb.type!=5 and lb.id in (select business from ApprovalProcess where org=:org";
            }else {
                //这个是5-支付多付款
                hql = "select lb.id,lb.amount,lb.createName,lb.createDate,lb.supplierName,sc.code from LoanBiz lb,SlCustomer sc where" +
                        " lb.finishTime is null and lb.supplier=sc.id and lb.type=5 and lb.id in (select business from ApprovalProcess where org=:org";
            }
//        String hql = "from LoanBiz where" +
//                " finishTime is null and id in (select business from ApprovalProcess where org=:org";
            map.put("org", org);
            if (toUser != null) {
                hql += " and toUser=:toUser";
                map.put("toUser", toUser);
            }
            if (fromUser != null) {
                hql += " and fromUser=:fromUser";
                map.put("fromUser", fromUser);
            }
            if (StringUtils.isNotEmpty(approveStatus)) {
                hql += " and approveStatus=:approveStatus";
                map.put("approveStatus", approveStatus);
            }
            if (business != null) {
                hql += " and business=:business";
                map.put("business", business);
            }
            if (businessType != null) {
                hql += " and businessType=:businessType";
                map.put("businessType", businessType);
            }
            hql += ")";
            List<Object[]> objects = loanBizDao.getListByHQLWithNamedParams(hql, map);
            for (Object[] ob : objects) {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", ob[0]);  //id
                map1.put("amount", ob[1]);  //金额
                map1.put("createName", ob[2]);  //创建人
                map1.put("createDate", ob[3]);  //创建时间
                map1.put("name", ob[4]);  //供应商名称
                map1.put("codeName", ob[5]);  //供应商序号
                loanBizList.add(map1);
            }
        }
        return loanBizList;
    }

    @Override
    public Map<String, Object> getPoLoanDetail(Integer poLoanId) {
        Map<String,Object> map = new HashMap<>();
//        PoLoan poLoan = poLoanDao.get(poLoanId);
        LoanBiz loanBiz = loanBizDao.get(poLoanId);
        PoPaymentApplication poPaymentApplication = new PoPaymentApplication();  //票款处理
        PoOrdersPrepayment poOrdersPrepayment = new PoOrdersPrepayment();   //预付款的
        if (1==loanBiz.getType()){
            poPaymentApplication = poPaymentApplicationDao.get(loanBiz.getPaymentApply());
        }else if (2==loanBiz.getType()){
            poOrdersPrepayment = poOrdersPrepaymentDao.get(loanBiz.getOrdersPrepayment().longValue());
        }else if (5==loanBiz.getType()){ //需收回的款
            String tracePath = loanBiz.getTracePath();
            if (StringUtils.isNotEmpty(tracePath)) {
                String[] tp = tracePath.split(",");
                String overId = tp[tp.length-2]; //取上一个多收来的款id
                LoanBiz overLoanBiz = loanBizDao.get(Integer.parseInt(overId));
                map.put("overLoanBiz",overLoanBiz);  //需收回的款详情中展示的多收来的款信息
            }

        }
        SrmSupplier srmSupplier = purchaseInvoiceService.getSupplier(loanBiz.getOrders());  //供应商信息
        map.put("poLoan",loanBiz);  //需收回的款信息
        map.put("poPaymentApplication",poPaymentApplication);  //票据处理信息
        map.put("poOrdersPrepayment",poOrdersPrepayment);  //预付款信息
        map.put("srmSupplier",srmSupplier);  //供应商信息
        return map;
    }

    @Override
    public Map<String, Object> amountRecoveredApproval(User user, Integer poLoanId, String method, String money, Integer financeAccountId, String oppositeCorp, String receiveAccountDate, String receiveDate, String expireDate, String originalCorp, String bankName, String returnNo) {
        Map<String,Object> map = new HashMap<>();
//        PoLoan poLoan = poLoanDao.get(poLoanId);
        LoanBiz loanBiz = loanBizDao.get(poLoanId);
        if (loanBiz.getFinishTime()!=null){  //已经进行操作了
            map.put("status", 3);//不可重复操作
            map.put("content", "不可重复操作");
        }else {
            Integer finishType = 1; //1-未完结 2-正好完结 3-多付
//            BigDecimal lockedAmount = loanBiz.getLockedAmount() != null ? loanBiz.getLockedAmount().add(money) : money; //锁定(正在申批中)金额
            BigDecimal paidAmount = loanBiz.getPaidAmount() != null ? loanBiz.getPaidAmount().add(new BigDecimal(money)) : new BigDecimal(money); //已付金额
            BigDecimal difference = paidAmount.subtract(loanBiz.getAmount());  //差额(负数代表少付)

            if (difference.compareTo(new BigDecimal(0))>=0&&(method.equals("1")||method.equals("5"))){
                //现金或银行转账的不可多支付
                map.put("status", 4);//不可重复操作
                map.put("content", "收回的金额可能有误！请重新录入！");
            }else {

                loanBiz.setPaidAmount(paidAmount);
                loanBiz.setPaidTimes(loanBiz.getPaidTimes() != null ? loanBiz.getPaidTimes() + 1 : 1);
                loanBiz.setBalance(difference);
                if (difference.compareTo(new BigDecimal(0)) >= 0) {
                    if (difference.compareTo(new BigDecimal(0)) == 0) {
                        finishType = 2;
                    } else if (difference.compareTo(new BigDecimal(0)) > 0) {
                        finishType = 3;
                    }
                    loanBiz.setFinishTime(new Date());
                }
                loanBiz.setUpdateDate(new Date());
                loanBiz.setUpdateName(user.getUserName());
                loanBiz.setUpdator(user.getUserID());
                loanBizDao.update(loanBiz);

                String source = "8"; //报销时的
                String businessType = "po_loan";
                if (loanBiz.getBizType().compareTo(LoanBizService.LoanBizBizType.payment.getIndex()) == 0) {
                    businessType = "payment";
                    source = "A";  //支付--需收回的款
                } else if (loanBiz.getBizType().compareTo(LoanBizService.LoanBizBizType.sale.getIndex()) == 0) {
                    businessType = "sale";
                    source = "9";
                }
                //添加收款表的数据
                FinanceReceipt financeReceipt = new FinanceReceipt();
                financeReceipt.setOrg(user.getOid());
                financeReceipt.setBusinessType(businessType); //业务类型:sale-销售,po_loan-差额借款,payment-支付
                financeReceipt.setBusiness(poLoanId);
                financeReceipt.setAmount(new BigDecimal(money));
                financeReceipt.setMethod(method);
                financeReceipt.setFactDate(new Date());
                financeReceipt.setAccountId(financeAccountId);
                financeReceipt.setCreateDate(new Date());
                financeReceipt.setCreateName(user.getUserName());
                financeReceipt.setCreator(user.getUserID());
                financeReceiptDao.save(financeReceipt);

                ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessByBusToUser(poLoanId, 69, null, "1");
                approvalProcess.setBusinessGroup(financeReceipt.getId().toString());
                approvalProcess.setApproveStatus("2");
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setUserName(user.getUserName());
                approvalProcess.setToUser(user.getUserID());
                approvalProcessDao.update(approvalProcess);

                if (finishType == 1) { //不完结的，重新录入审批流程
                    ApprovalProcess process1 = new ApprovalProcess();
                    BeanUtils.copyPropertiesIgnoreNull(approvalProcess, process1);
                    process1.setCreateDate(new Date());
                    process1.setId(null);
                    process1.setBusinessGroup(null);
                    process1.setApproveStatus("1");  //待处理
                    process1.setHandleTime(new Date());
                    approvalProcessDao.save(process1);
                }

                SrmSupplier srmSupplier = null;
                String summary = "";  //摘要(在此用途和摘要一样)
                if (StringUtils.isEmpty(oppositeCorp)) {
                    if (loanBiz.getOrders() != null && StringUtils.isEmpty(loanBiz.getSupplierName())) {
                        srmSupplier = purchaseInvoiceService.getSupplier(loanBiz.getOrders());  //供应商信息
                        summary = "回收的" + srmSupplier.getName() + "的款";
                        oppositeCorp = srmSupplier.getName();
                    } else {
                        summary = "回收的" + loanBiz.getSupplierName() + "的款";
                        oppositeCorp = loanBiz.getSupplierName();
                    }
                }

                if ("1".equals(method)) {  //现金
                    map = accountService.getCreditCash(user, money, money, method, null, summary, summary, user.getUserName(), oppositeCorp, null, source, null, NewDateUtils.dateFromString(receiveDate, "yyyy-MM-dd"), new Date(), financeReceipt.getId(), new Date(), new Date());
                    Integer accountDetailId = (Integer) map.get("accountDetailId");
                    financeReceipt.setAccountDetail(accountDetailId);
                    Integer accountId = (Integer) map.get("financeAccountId");
                    financeReceipt.setAccountId(accountId);
                } else if ("3".equals(method) || "4".equals(method)) {  //承兑汇票和外部转账支票
                    map = accountService.getCreditReturn(user, money, money, method, null, bankName, summary, summary, user.getUserName(), NewDateUtils.dateFromString(expireDate, "yyyy-MM-dd"), originalCorp, oppositeCorp, NewDateUtils.dateFromString(receiveDate, "yyyy-MM-dd"), null, source, null, returnNo, new Date(), financeReceipt.getId(), new Date(), new Date());
                    Integer accountBill = (Integer) map.get("accountBill");
                    financeReceipt.setAccountBill(accountBill);
                } else if ("5".equals(method)) {  //银行转账
                    map = accountService.getCreditBank(user, financeAccountId, money, money, method, null, summary, summary, user.getUserName(), oppositeCorp, NewDateUtils.dateFromString(receiveAccountDate, "yyyy-MM-dd"), null, source, null, new Date(), financeReceipt.getId(), new Date(), new Date());
                    Integer accountDetailId = (Integer) map.get("accountDetailId");
                    financeReceipt.setAccountDetail(accountDetailId);
                }
                financeReceiptDao.update(financeReceipt);

                if (finishType == 3) {
                    Integer origBiz = loanBiz.getOrigBiz();
                    if (loanBiz.getOrigBiz() == null) {
                        origBiz = loanBiz.getId();
                    }
                    //生成借款信息(多收来的款)
                    Integer reviewInstance = null;
                    ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "paymentAudit");  //查当前的复核流程
                    if (approvalItem != null) {
                        reviewInstance = approvalItem.getId();
                    }
                    LoanBiz loanBiz1 = loanBizService.addLoanBiz(user, loanBiz.getId(), null, null, LoanBizService.LoanBizBizType.sale.getIndex(), LoanBizService.LoanBizType.overpayment.getIndex(), difference, loanBiz.getSupplier(), loanBiz.getSupplierName(), null, origBiz, loanBiz.getTracePath(), reviewInstance);
                    //财务出纳-可付款的流程
                    ApprovalProcess process = new ApprovalProcess();
                    process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回
                    process.setLevel(1);
                    process.setToUserName("财务处理者");
                    process.setToMid("lp");  //报销受理模块
                    process.setCreateDate(new Date());
                    process.setOrg(user.getOid());
                    process.setUserName("财务处理者");
                    process.setFromUser(user.getUserID());
                    process.setAskName(user.getUserName());
                    process.setHandleTime(new Date());
                    process.setBusiness(loanBiz1.getId());
                    process.setBusinessType(70);
                    approvalProcessDao.save(process);

                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //多收来的款-可付款推送，+1
                        overpaymentService.overpaymentRejectSend(1, 1, loanBiz1, u.getUserId(), "/overpaymentPayable", "overreceivedFundsApproval");
                    }
                }

                Integer status = (Integer) map.get("status");
                if (1 == status && finishType != 1) {  //成功后推送
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务出纳为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //需收回的款 审批人待处理-1
                        purchaseInvoiceService.loanBizRejectSend(-1, -1, loanBiz, u.getUserId(), "/poLoanHandle", null, null, "needRecoveredAmount");
                    }
                }
            }
        }
        return map;
    }

    @Override
    public List<Map<String,Object>> getRecoveredList(Integer org,String businessType,Integer business) {
        Map<String,Object> map = new HashMap<>();
        //除现金外的其他付款方式
        String contition = "select fr.id,fr.amount,fr.method,fr.accountBill,fr.createDate,fr.createName,b.cheque_,b.returnBill from FinanceReceipt fr,FinanceAccountBill b where (fr.accountBill=b.id or fr.method in ('1','5')) and fr.org=:org";
        map.put("org",org);
        if (business!=null){
            contition+=" and fr.business=:business";
            map.put("business",business);
        }
        if (StringUtils.isNotEmpty(businessType)){
            contition+=" and fr.businessType=:businessType";
            map.put("businessType",businessType);
        }
        contition+=" group by fr.id";
        List<Object[]> objects = financeReceiptDao.getListByHQLWithNamedParams(contition,map);
        List<Map<String,Object>> detail = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("id",ob[0]);  //id
            map1.put("amount",ob[1]);  //金额
            map1.put("method",ob[2]);  //支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            map1.put("accountBill",ob[3]);  //票据id
            map1.put("createDate",ob[4]);  //完成时间
            map1.put("createName",ob[5]);  //完成人
            map1.put("cheque",ob[6]);  //内部支出票据
            map1.put("returnBill",ob[7]);  //外部的
            detail.add(map1);
        }
        return detail;
    }

    @Override
    public Map<String, Object> getRecoveredDetail(Integer recoveredId) {
        Map<String,Object> map = new HashMap<>();
        FinanceAccount financeAccount = new FinanceAccount();
        FinanceChequeDetail financeChequeDetail = new FinanceChequeDetail();
        FinanceReturn financeReturn = new FinanceReturn();
        Integer invoiceType = 1;  //主要来区分是内部转账支票还是外部转账支票
        FinanceReceipt financeReceipt = financeReceiptDao.get(recoveredId);
        if (financeReceipt!=null){
            if (financeReceipt.getAccountId()!=null){
                financeAccount = financeAccountDao.get(financeReceipt.getAccountId());
            }

            if ("3".equals(financeReceipt.getMethod()) || "4".equals(financeReceipt.getMethod())) {
                FinanceAccountBill financeAccountBill = financeAccountBillDao.get(financeReceipt.getAccountBill());
                if (financeAccountBill.getCheque_() != null) {
                    invoiceType = 2;  //内部票据
                    financeChequeDetail = financeChequeDetailDao.get(financeAccountBill.getCheque_());
                } else if (financeAccountBill.getFinanceReturn().getId() != null) {
                    invoiceType = 3;  //外部转账票据
                    financeReturn = financeAccountBill.getFinanceReturn();
                }
            }
        }

        map.put("financeReceipt",financeReceipt); //还款信息
        map.put("financeAccount",financeAccount);  //账户信息
        map.put("invoiceType",invoiceType);  // 1-其他的付款方式  2-内部转账票据  3-外部转账票据
        map.put("financeChequeDetail",financeChequeDetail);  //内部转账支票信息
        map.put("financeReturn",financeReturn);   //外部转账支票或者承兑汇票信息
        return map;
    }
}
