select.month,#year1,#year2{ width:80px; }
select, input{ border: 1px solid #ccc; display: inline-block; height:30px; padding:0 5px; width:150px;  }
.form-control{ display: inline-block; width:150px;  }
.ty-tr{ line-height: 35px;  }
.sta1{ display: inline-block; }
.sta1{ display: none; }
table thead td{ background:#eee;  }
hr{ border: 1px solid #ccc; }
.contact { display:flex;width:300px;  }
.contact>span:nth-child(1){ text-align:left ; display: inline-block; width: 100px;overflow: hidden;  }
.contact>span:nth-child(2){ text-align:left; display: inline-block; width: 175px;overflow: hidden; margin-left: 25px;  }
.page-header-fixed .page-container{ margin-top: 26px; }
#year2::-webkit-input-placeholder{
    color:#4d6b8a;
}
#year2::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color:#4d6b8a;
}
#year2:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color:#4d6b8a;
}
#year2:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color:#4d6b8a;
}







