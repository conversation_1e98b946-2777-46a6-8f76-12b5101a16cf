<template>
  <div id="purchasePaymentFinanceApprovalDetail"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="付款复核"></TY_NavTop>
    <div class="detailInfo">
       <!--票据-->
       <div v-if="pund === 0">
        <div @click="jumpDetail(0)" >
          <span><span class="ttl1">申请付款金额 </span><span> {{detailsInfo.amount && detailsInfo.amount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span>  {{details.fullName }}</span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailsInfo.payNum || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.numApplication || 0 }}笔，共{{(detailsInfo.lockedAmount && detailsInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
      <!--预付款-->
      <div v-if="pund === 1">
        <div @click="jumpDetail(1)" >
          <span><span class="ttl1">申请付款金额 </span><span>  {{detailsInfo.planAmount && detailsInfo.planAmount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span> {{details.srmSupplier.fullName }} </span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailsInfo.payNum || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailsInfo.lockedAmount && detailsInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">计划付款金额</span>{{detailsInfo.planAmount && detailsInfo.planAmount.toFixed(2)}}元
        </div>
        <div>
          <span class="ttl1">计划付款时间</span>{{detailsInfo.planDate |formatDay('YYYY-MM-DD')}}
          <span class="redTxt"><span class="ttl1">计划付款方式</span>{{isture}}</span>
        </div>
        <div class="martop10">
          <span class="ttl1">付款事由</span>采购的预付款
        </div>
      </div>
      <!--多收来的款-->
      <div v-if="pund === 3">
        <div>
          <span class="ttl1">原始的付款方</span><span> {{detailsInfo.supplierName }} </span>
        </div>
        <div>
          <span><span class="ttl1">多收来的金额 </span><span>  {{detailsInfo.amount && detailsInfo.amount.toFixed(2)}} 元 </span></span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailsInfo.paidTimes || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailsInfo.lockedAmount && detailsInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
    </div>
    <div v-if="detailsInfo">
      <div class="handle_button">
        <span @click="jumpFu3()" class="btncat" style="margin-right:50px; font-size:1.2em; ">原定的付款方式</span>
      </div>
      <el-form :label-position="left" label-width="180px">
        <div v-if="details.financePayment.method === '5'" >
          <el-form-item label="拟付款方式"><span>银行转账</span></el-form-item>
<!--          <el-form-item label="计划付款日期"><span>{{ details.financePayment.planDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>-->
          <el-form-item label="银行账户"><span>{{ details.financeAccount.name }}</span></el-form-item>
          <el-form-item label="账户类型"><span>{{ details.financeAccount.isPublic == '1' ? '对公户 ': '非对公户' }} </span></el-form-item>
          <el-form-item label="开户行"><span>{{details.financeAccount.bankName}}</span></el-form-item>
          <el-form-item label="账号"><span>{{details.financeAccount.account}}</span></el-form-item>
          <!-- <el-form-item label="账户余额"><span>{{details.financeAccount.balance}}</span></el-form-item> -->
          <el-form-item label="拟付款金额"><span>{{details.financePayment.factAmount}}</span></el-form-item>
          <el-form-item label="拟付款日期"><span>{{ details.financePayment.planDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>
          <div v-if="code == '51' || code == 71">
            <p class="orangeTip" style="text-align: center;">！与银行转账有关的手机卡、U盾、口令卡或其他安全设备由出纳以外的专人管理，出纳转账时需复核无误后得到授权，可降低财务风险</p>
            <p style="text-align: center;margin-top:10px;">
              <el-checkbox label="经复核，上述付款信息无误，已向出纳授权。" name="type" @change="handleChange()"></el-checkbox>
            </p>
            <div class="handle_button">
              <span style="width:80%; position: relative; left: -34px; " class="ui-btn ui-btn_info"  @click="approve()">确 定</span>
            </div>
          </div>
        </div>
        <div v-if="details.financePayment.method === '4'">
          <el-form-item label="拟付款方式"><span>承兑汇票（外）</span></el-form-item>
          <el-form-item label="汇票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
          <el-form-item label="所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount }}</span></el-form-item>
          <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
          <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName }}</span></el-form-item>
          <div class="handle_button">
            <span @click="seeticket()" class="btncat" style="margin-right:50px; font-size:15px; ">查看汇票</span>
          </div>
          <div v-if="code == '51' || code == 71">
            <p class="orangeTip" v-if="Number(detailsInfo.balanceAmount) > 0">！本次将多支付{{ detailsInfo.balanceAmount && detailsInfo.balanceAmount.toFixed(2) }}元！该差额将进入“借款管理”模块，请跟踪管理。</p>
            <p class="orangeTip">！票据与印鉴由不同人的管理，出纳转账时需复核无误后得到授权，可降低财务风险。</p>
            <p style="text-align: center;margin-top:10px;">
              <el-checkbox label="经复核，上述付款信息无误，已向出纳授权。" name="type" @change="handleChange()"></el-checkbox>
            </p>
            <div class="handle_button" style="text-align: center;">
              <span style="width:80%; " class="ui-btn ui-btn_info"  @click="approve()">确 定</span>
            </div>
          </div>
        </div>
        <div v-if="details.financePayment.method === '3'">
          <div v-if="details.invoiceType == '2'">  <!--(1-其他付款方式  2-内部转账支票 3-外部转账支票)-->
            <el-form-item label="拟付款方式"><span>转账支票（内）</span></el-form-item>
            <el-form-item label="转账支票的开户行"><span>{{ details.financeAccount.bankName  }}</span></el-form-item>
            <el-form-item label="转账支票的支票号"><span>{{ details.financeChequeDetail.chequeNo }}</span></el-form-item>
            <el-form-item label="支票金额"><span>{{ details.financeChequeDetail.billAmount }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeChequeDetail.expireDate | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
<!--            <el-form-item label="接收日期"><span>{{details.financeChequeDetail.receiveDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>-->
<!--            <el-form-item label="接收经手人"><span>{{details.financeChequeDetail.receiver }}</span></el-form-item>-->
<!--            <el-form-item label="支付经手人"><span>{{details.financeChequeDetail.operator }}</span></el-form-item>-->
            <div v-if="code == '51' || code == '71'">
              <p class="orangeTip">！票据与印鉴由不同人的管理，出纳转账时需复核无误后得到授权，可降低财务风险。</p>
              <p style="text-align: center;margin-top:20px;">
                <el-checkbox label="经复核，上述付款信息无误，已向出纳授权。" name="type" @change="handleChange()"></el-checkbox>
              </p>
              <div class="handle_button" style="text-align: center;">
                <span style="width:80%; " class="ui-btn ui-btn_info"  @click="approve()">确 定</span>
              </div>
            </div>
          </div>
          <div v-if="details.invoiceType == '3'">
            <el-form-item label="拟付款方式"><span>转账支票（外）</span></el-form-item>
            <el-form-item label="支票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
            <el-form-item label="所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
            <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName}}</span></el-form-item>
            <div class="handle_button">
              <span @click="seeticket()" class="btncat" style="margin-right:50px; font-size:15px; ">查看支票</span>
            </div>
            <div v-if="code == '51' || code == '71'">
              <p class="orangeTip" v-if="Number(detailsInfo.balanceAmount) > 0">！本次将多支付{{ detailsInfo.balanceAmount && detailsInfo.balanceAmount.toFixed(2) }}元！该差额将进入“借款管理”模块，请跟踪管理。</p>
              <p class="orangeTip">！票据与印鉴由不同人的管理，出纳转账时需复核无误后得到授权，可降低财务风险。</p>
              <p style="text-align: center; margin-top:20px;">
                <el-checkbox label="经复核，上述付款信息无误，已向出纳授权。" name="type" @change="handleChange()"></el-checkbox>
              </p>
              <div class="handle_button" style="text-align: center;">
                <span style="width:80%; " class="ui-btn ui-btn_info"  @click="approve()">确 定</span>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<style lang="less">
#purchasePaymentFinanceApprovalDetail{
  .el-icon-arrow-right{ float: right; font-size: 16px; }
  .el-form-item{ width:364px; }
  .el-textarea{ width: 232px!important; margin-top: 5px; }
  .el-input{ width: 232px!important; }
  //.el-form-item__label{ width: 120px!important; }
  background: #fff;
  overflow: auto!important;
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .orangeTip{
    margin-top: 20px;
    color: #ff9304;
    font-size: 12px;
    padding: 0 15px;
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  .detailInfo{
    padding:10px 20px 0px 20px;
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
    line-height: 26px;
    .ttl1{
      display: inline-block;
      width: 86px;
      text-align: right;
      margin-right: 10px;
    }
  }
  .mar30{ margin-right:30px;  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  .flexC{
    display: flex;margin-bottom:15px ;
    &>span{ display:block; flex: 1;  }
  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
  .oInfo{ padding:5px 20px; }
}
</style>
<script>
var that
export default {
  name: 'purchasePaymentFinanceApprovalDetail',
  data () {
    return {
      loading: true,
      toggle: -1,
      isSpecialBill: 0,
      detailsInfo:{},
      details: {
        listMapBillCat: [],
        poPaymentApplication: {
          amount: ''
        },
        financeChequeDetail: {},
        financeAccount: {
          balance: ''
        },
        financeReturn: {},
        listMapFeeCat: [],
        srmSupplier: {},
        poOrdersPrepayment: {},
        poOrders: {
          amount: ''
        },
        approvalProcessList: {},
        financePaymentHistory: {},
        financePayment: {},
        financeReturnHistory: {}
      },
      updatePay: false,
      nowApproveItem: {},
      isNowApprover: false,
      pund: 0,
      busyid: '',
      code: 0
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    that = this
    let paramCode = this.$route.params.id
    this.busyid = this.$route.params.busyid
    let parampun = Number(this.$route.params.pun)
    that.code = Number(paramCode.substring(0, 2)) // 51-待处理; 52-已批准
    console.log('code:', that.code)
    that.invoiceID = paramCode.substring(2, paramCode.length)
    that.pund = Number(parampun)
    that.getDetail()
  },
  destroyed: function () {
  },
  methods: {
    getAlreadyPayDetails () {
      console.log('getAlreadyPayDetails ')
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceAlready/${this.invoiceID}`
      })
    },
    getCheckDetails () {
      console.log('getCheckDetails ')
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceCheck/${this.invoiceID}`
      })
    },
    seeticket: function () {
      let that = this
      let item = {}
      item = this.details.financeReturn
      item.pund = that.pund
      if (this.details.financePayment.method === '4') {
        item.type = 3
      } else if (this.details.financePayment.method === '3') {
        item.type = 2
      }
      console.log(item)
      localStorage.setItem('invoiceTicketInfo', JSON.stringify(item))
      that.$router.push({
        path: `/purchasePaymentInvoiceInfo`
      })
    },
    jumpDetail: function (pun) {
      let that = this
      that.$router.push({
        path: `/purchaseInvoiceDetails3/34${that.invoiceID}/${pun}/${that.busyid}`
      })
    },
    handleChange: function () {
      let that = this
      that.updatePay = !that.updatePay
    },
    getDetail: function () {
      let that = this
      if (that.pund === 0) {
        this.axios.post('../../../purchaseInvoice/getPurchaseInvoiceDetail.do', { 'applicationId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailsInfo = res.poPaymentApplication
            console.log('that.detailsInfo =', that.detailsInfo )
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
          })
          .catch(function (error) {
            console.log(error)
          })
      } else if (that.pund === 3) {
        this.axios.post('../../../overpayment/getOverpaymentDetail.do', { 'loanBizId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailsInfo = res.loanBiz
            that.detailsInfo.balanceAmount = res.loanBiz.balance
            console.log('that.detailsInfo =', that.detailsInfo )
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do', { 'orderPrepaymentId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailsInfo = res.poOrdersPrepayment
            console.log('that.detailsInfo =', that.detailsInfo )
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            let bills = res.listMapBillCat || []
            bills.forEach(function (item2) {
              if (item2.invoiceCategory === '1') {
                that.isSpecialBill = 1
              }
            })
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    jumpFu3: function () {
      let that = this
      if (Number(that.details.status) === 0) {
        that.$kiko_message('原来计划的付款方式没有修改过！')
        return false
      }
      this.$router.push({
        path: `/purchasePaymentFinanceFu3/${this.$route.params.id}/${this.$route.params.pun}/${that.busyid}`
      })
    },
    approve: function () {
      let that = this
      if (that.updatePay) {
        let lastApprove = this.nowApproveItem
        let approvalProcessId = lastApprove['id']
        // this.details.approvalProcessList.forEach(function (apItem) {
        //   if (apItem.approveStatus === '1' && apItem.businessType === 51) {
        //     approvalProcessId = apItem.id
        //   }
        // })
        let url = '../../../purchaseInvoice/reviewedApprovalApproval.do'
        if (that.pund === 3 || that.pund === '3') {
          url = `../../../overpayment/overpaymentReviewedApproval.do`
        }
        this.axios.post(url, { 'approvalProcessId': approvalProcessId })
          .then(function (response) {
            let res = response.data.data
            that.$kiko_message(res.content)
            that.loading = false
            if (res.content === '操作成功') {
              if (Number(that.pund) === 3) {
                localStorage.setItem('navNum', '1')
                that.$router.push({
                  path: `/overreceivedFundsFinance`
                })
              } else {
                localStorage.setItem('navNum', '2')
                that.$router.push({
                  path: `/purchaseFinanceReimburse`
                })
              }
            }
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        that.$kiko_message('需要先勾选确认！')
      }
    }
  },
  computed: {
    isture: function () {
      let one = Number(this.detailsInfo.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  }
}
</script>
