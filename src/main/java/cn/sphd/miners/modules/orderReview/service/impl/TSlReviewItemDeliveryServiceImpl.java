package cn.sphd.miners.modules.orderReview.service.impl;

import java.util.List;

import cn.sphd.miners.modules.orderReview.entity.TSlReviewItemDelivery;
import cn.sphd.miners.modules.orderReview.mapper.TSlReviewItemDeliveryMapper;
import cn.sphd.miners.modules.orderReview.service.ITSlReviewItemDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
@Transactional
public class TSlReviewItemDeliveryServiceImpl implements ITSlReviewItemDeliveryService
{
    @Autowired
    private TSlReviewItemDeliveryMapper tSlReviewItemDeliveryMapper;

    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TSlReviewItemDelivery selectTSlReviewItemDeliveryById(Long id)
    {
        return tSlReviewItemDeliveryMapper.selectTSlReviewItemDeliveryById(id);
    }

    /**
     * 查询列表
     * 
     * @param tSlReviewItemDelivery 
     * @return 
     */
    @Override
    public List<TSlReviewItemDelivery> selectTSlReviewItemDeliveryList(TSlReviewItemDelivery tSlReviewItemDelivery)
    {
        return tSlReviewItemDeliveryMapper.selectTSlReviewItemDeliveryList(tSlReviewItemDelivery);
    }

    /**
     * 新增
     * 
     * @param tSlReviewItemDelivery 
     * @return 结果
     */
    @Override
    public int insertTSlReviewItemDelivery(TSlReviewItemDelivery tSlReviewItemDelivery)
    {
        return tSlReviewItemDeliveryMapper.insertTSlReviewItemDelivery(tSlReviewItemDelivery);
    }

    /**
     * 修改
     * 
     * @param tSlReviewItemDelivery 
     * @return 结果
     */
    @Override
    public int updateTSlReviewItemDelivery(TSlReviewItemDelivery tSlReviewItemDelivery)
    {
        return tSlReviewItemDeliveryMapper.updateTSlReviewItemDelivery(tSlReviewItemDelivery);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTSlReviewItemDeliveryByIds(Long[] ids)
    {
        return tSlReviewItemDeliveryMapper.deleteTSlReviewItemDeliveryByIds(ids);
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTSlReviewItemDeliveryById(Long id)
    {
        return tSlReviewItemDeliveryMapper.deleteTSlReviewItemDeliveryById(id);
    }
}
