package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import java.util.concurrent.TimeUnit;
import cn.sphd.miners.modules.schedule.dao.MemoJobDao;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleDao;
import cn.sphd.miners.modules.schedule.entity.MemoJob;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.schedule.service.MemoJobService;
import cn.sphd.miners.modules.system.entity.User;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by Administrator on 2019/1/24.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoJobServiceImpl implements MemoJobService {
    @Autowired
    MemoJobDao memoJobDao;
    @Autowired
    MemoScheduleDao memoScheduleDao;

    @Override
    public void updateMemoJob(MemoJob memoJob) {
        memoJobDao.update(memoJob);
    }

    @Override
    public MemoJob getLastMemoJobBySid(Integer sId) {
        String hql=" from MemoJob where scheduleId=:sId order by createDate desc";
        Map<String,Object> params=new HashMap<>();
        params.put("sId",sId);
        MemoJob memoJob= (MemoJob) memoJobDao.getByHQLWithNamedParams(hql,params);
        return memoJob;
    }

    @Override
    public MemoJob addMemoJobByUserSchedule(MemoSchedule memoSchedule) {
        MemoJob memoJob=new MemoJob();
        addMemoJobByUserScheduleTask(memoSchedule, memoJob, memoSchedule.getNextRunTime().getTime(), System.currentTimeMillis());
        return memoJob;
    }

//    @Override
//    public MemoJob addMemoJobByUserSchedule(MemoSchedule memoSchedule) {
//        MemoJob memoJob=new MemoJob();
//        memoJob.setSchedule(memoSchedule);
//        memoJob.setCreator(memoSchedule.getUser());
//        memoJob.setCreateDate(new Date());
//        memoJob.setUser(memoSchedule.getUser());
//        memoJob.setCreateName(memoSchedule.getCreateName());
//        memoJob.setName(memoSchedule.getName());
//        memoJob.setTitle(memoSchedule.getTitle());
//        memoJob.setType(1);//约定提醒 -1
//        memoJob.setRunTime(memoSchedule.getNextRunTime());//运行时间
//        memoJob.setUpdateDate(memoSchedule.getNextRunTime());// 存成这条提醒的的初次运行时间
//        memoJob.setEnabled(1);//需要提醒
////        date= NewDateUtils.changeDay(NewDateUtils.getLastTimeOfMonth(new Date()),-memoSchedule.getSpecialInterval());//每月倒数提醒日
//
////        Calendar calendar = Calendar.getInstance();
////        calendar.setTime(NewDateUtils.changeMonth(memoSchedule.getNextRunTime(),1));
////        int maxday = calendar.get(Calendar.DAY_OF_MONTH);
////        new Date(memoSchedule.getNextRunTime().getTime()+TimeUnit.DAYS.toMillis(maxday));
//
//        //计算 下次提醒时间
//        Date runTime=memoSchedule.getNextRunTime();
//        if(memoSchedule.getFreqType()>1) {//是重复日程
//            if (1==memoSchedule.getFreqType()){
//                memoSchedule.setNextRunTime(null);//一次性日程，下次提醒时间清空
//            }else if (4==memoSchedule.getFreqType()) { //每天重复
//                Date nextRunTime = new Date(runTime.getTime()+TimeUnit.DAYS.toMillis(1));//下一天
//                memoSchedule.setNextRunTime(nextRunTime);//新的下次提醒时间
//
//            }else if (8==memoSchedule.getFreqType()){ // 每周重复
//                Date nextRunTime = new Date(runTime.getTime()+TimeUnit.DAYS.toMillis(7));//下一周
//                memoSchedule.setNextRunTime(nextRunTime);//新的下次提醒时间
//
//            }else if (16==memoSchedule.getFreqType()){//每月重复
//                if (memoSchedule.isSpecial()) {// 是特殊时间提醒（每月倒数第几日）
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.setTime(NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(runTime,1)));
//                    int days=calendar.get(Calendar.DAY_OF_MONTH);//调取当月的天数
//                    Date nextRunTime = new Date(runTime.getTime() + TimeUnit.DAYS.toMillis(days));
//
//                    memoSchedule.setNextRunTime(nextRunTime);//新的下次提醒时间
//                }else {
//                    Calendar calendar = Calendar.getInstance();
//                    Date nextRunTime;
//                    int i=1;
//                    do{
//                        calendar.setTime(runTime);
//                        calendar.add(Calendar.MONTH, i); // 月份再调至下个月；
//                        nextRunTime=calendar.getTime();
//                        i++;
//                    }while (!NewDateUtils.getDate(runTime).equals(NewDateUtils.getDate(nextRunTime)));// 这月提醒 日期那天 不等于 下月提醒日期那天
//                    memoSchedule.setNextRunTime(nextRunTime);
//                }
//            }else if (32==memoSchedule.getFreqType()){//每年重复
//                Calendar calendar = Calendar.getInstance();
//                Date nextRunTime;
//                int i=1;
//                do {
//                    calendar.setTime(runTime);
//                    calendar.add(Calendar.YEAR,i);//年调至下一年
//                    nextRunTime=calendar.getTime();
//                    i++;
//                }while (!NewDateUtils.getDate(runTime).equals(NewDateUtils.getDate(nextRunTime)));// 这年提醒 月和日 不等于 下一年 月和日
//                memoSchedule.setNextRunTime(nextRunTime);
//            }
//        }
//        memoJob.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
//        memoJob.setDescription(memoSchedule.getDescription());
//        memoJob.setPlace(memoSchedule.getPlace());
//        memoJobDao.save(memoJob);
//        return memoJob;
//    }

    @Override
    public Long addMemoJobByUserScheduleTask(MemoSchedule memoSchedule, MemoJob memoJob, Long runTime, Long now) {
        memoJob.setSchedule(memoSchedule);
        memoJob.setCreator(memoSchedule.getUser());
        memoJob.setCreateDate(new Date(now));
        memoJob.setUser(memoSchedule.getUser());
        memoJob.setCreateName(memoSchedule.getCreateName());
        memoJob.setName(memoSchedule.getName());
        memoJob.setTitle(memoSchedule.getTitle());
        memoJob.setType(1);//约定提醒 -1
        memoJob.setRunTime(new Date(runTime));//运行时间
        memoJob.setUpdateDate(new Date(runTime));// 存成这条提醒的的初次运行时间
        if(runTime>now) {
            memoJob.setEnabled(1);//需要提醒
            memoJob.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        } else {
            memoJob.setEnabled(0);//需要提醒
            memoJob.setState(4);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        }

        //计算 下次提醒时间
        if(memoSchedule.getFreqType()>1) {//是重复日程
            Date begin,end;
            switch (memoSchedule.getFreqType()) {
                case 4: //每天重复
                    runTime += TimeUnit.DAYS.toMillis(1);//下一天
                    break;
                case 8: // 每周重复
                    runTime += TimeUnit.DAYS.toMillis(7);//下一周
                    break;
                case 16: //每月重复
                if (memoSchedule.isSpecial()) {// 是特殊时间提醒（每月倒数第几日）
                    begin = NewDateUtils.changeMonth(new Date(runTime),1);
                    end = NewDateUtils.changeMonth(new Date(runTime),2);
                    runTime += end.getTime()-begin.getTime();
                }else {
                    begin = NewDateUtils.changeMonth(new Date(runTime), 0);
                    end = NewDateUtils.changeMonth(new Date(runTime), 1);
                    runTime += end.getTime() - begin.getTime();
                }
                break;
                case 32: //每年重复
                    begin = NewDateUtils.getNewYearsDay(new Date(runTime));
                    end = NewDateUtils.changeYear(new Date(runTime),1);
                    runTime += end.getTime()-begin.getTime();
                    break;
            }
                memoSchedule.setNextRunTime(new Date(runTime));//新的下次提醒时间
        } else {
            memoSchedule.setNextRunTime(null);//一次性日程，下次提醒时间清空
            runTime = Long.MAX_VALUE;
        }

        memoJob.setDescription(memoSchedule.getDescription());
        memoJob.setPlace(memoSchedule.getPlace());
        memoJobDao.save(memoJob);
        System.out.println("addMemoJobByUserScheduleTask.memoJob.getRunTime:"+memoJob.getRunTime());
        return runTime;
    }

    @Override
    public List<MemoJob> getMemoJobListBySid(Integer sId) {
        String hql=" from MemoJob where scheduleId=:sId and isValid=true order by runTime asc";
        Map<String,Object> params=new HashMap<>();
        params.put("sId",sId);
        List<MemoJob> memoJobList=  memoJobDao.getListByHQLWithNamedParams(hql,params);
        return memoJobList;
    }

    @Override
    public MemoJob getMemoJobById(Integer id) {
        return memoJobDao.get(id);
    }

    @Override
    public void deleteMemoJob(MemoJob memoJob) {
        memoJobDao.delete(memoJob);
    }

    @Override
    public List<MemoJob> getMemoJobListByDate(Integer userId, Date beginDate, Date endDate, PageInfo pageInfo) {
        String hql=" from MemoJob where isValid=true and schedule.user=:userId and :beginDate<runTime and runTime<:endDate order by runTime asc";

        Map<String,Object> params=new HashMap<>();
        params.put("userId",userId);
        params.put("beginDate",beginDate);
        params.put("endDate",endDate);
        List<MemoJob> memoJobList= memoJobDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return memoJobList;
    }

    @Override
    public List<MemoJob> getMemoJobListByUserId(Integer userId,String keyword,PageInfo pageInfo) {
        String hql=" from MemoJob where isValid=true and user=:userId";
        Map<String,Object> params=new HashMap<>();
        params.put("userId", userId);
        if (!StringUtil.isNullOrEmpty(keyword)){
            hql+=" and (title like:keyword or description like:keyword or supplement like:keyword)";
            params.put("keyword","%"+keyword+"%");
        }
        List<MemoJob> memoJobList=memoJobDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return memoJobList;
    }
}
