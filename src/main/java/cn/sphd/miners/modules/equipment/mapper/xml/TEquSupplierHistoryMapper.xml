<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquSupplierHistoryMapper">
    
    <resultMap type="TEquSupplierHistory" id="TEquSupplierHistoryResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="supplier"    column="supplier"    />
        <result property="name"    column="name"    />
        <result property="fullName"    column="full_name"    />
        <result property="code"    column="code"    />
        <result property="keywords"    column="keywords"    />
        <result property="supplyCount"    column="supply_count"    />
        <result property="exclusiveCount"    column="exclusive_count"    />
        <result property="cutCount"    column="cut_count"    />
        <result property="enabled"    column="enabled"    />
        <result property="enabledTime"    column="enabled_time"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTEquSupplierHistoryVo">
        select id, org, supplier, name, full_name, code, keywords, supply_count, exclusive_count, cut_count, enabled, enabled_time, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_equ_supplier_history
    </sql>

    <select id="selectTEquSupplierHistoryList" parameterType="TEquSupplierHistory" resultMap="TEquSupplierHistoryResult">
        <include refid="selectTEquSupplierHistoryVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="supplier != null "> and supplier = #{supplier}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="fullName != null  and fullName != ''"> and full_name like concat('%', #{fullName}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="keywords != null  and keywords != ''"> and keywords = #{keywords}</if>
            <if test="supplyCount != null "> and supply_count = #{supplyCount}</if>
            <if test="exclusiveCount != null "> and exclusive_count = #{exclusiveCount}</if>
            <if test="cutCount != null "> and cut_count = #{cutCount}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="enabledTime != null "> and enabled_time = #{enabledTime}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTEquSupplierHistoryById" parameterType="Long" resultMap="TEquSupplierHistoryResult">
        <include refid="selectTEquSupplierHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTEquSupplierHistory" parameterType="TEquSupplierHistory" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_supplier_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="supplier != null">supplier,</if>
            <if test="name != null">name,</if>
            <if test="fullName != null">full_name,</if>
            <if test="code != null">code,</if>
            <if test="keywords != null">keywords,</if>
            <if test="supplyCount != null">supply_count,</if>
            <if test="exclusiveCount != null">exclusive_count,</if>
            <if test="cutCount != null">cut_count,</if>
            <if test="enabled != null">enabled,</if>
            <if test="enabledTime != null">enabled_time,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="name != null">#{name},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="code != null">#{code},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="supplyCount != null">#{supplyCount},</if>
            <if test="exclusiveCount != null">#{exclusiveCount},</if>
            <if test="cutCount != null">#{cutCount},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="enabledTime != null">#{enabledTime},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTEquSupplierHistory" parameterType="TEquSupplierHistory">
        update t_equ_supplier_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="name != null">name = #{name},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="code != null">code = #{code},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="supplyCount != null">supply_count = #{supplyCount},</if>
            <if test="exclusiveCount != null">exclusive_count = #{exclusiveCount},</if>
            <if test="cutCount != null">cut_count = #{cutCount},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="enabledTime != null">enabled_time = #{enabledTime},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquSupplierHistoryById" parameterType="Long">
        delete from t_equ_supplier_history where id = #{id}
    </delete>

    <delete id="deleteTEquSupplierHistoryByIds" parameterType="String">
        delete from t_equ_supplier_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>