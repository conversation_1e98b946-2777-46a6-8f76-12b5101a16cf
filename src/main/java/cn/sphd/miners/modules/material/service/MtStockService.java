package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * Created by Administrator on 2017/8/2.
 */
public interface MtStockService extends BadgeNumberCallback {

    void saveMtStock(MtStock mtStock);

    List<MtStock> getByMtMaterialApply(Integer oid,Integer applyId);

    MtStock getByIdAndOid(Integer ID,Integer oid);

    MtStock getById(Integer ID);

    void updateMtStock(MtStock mtStock);

    List<PdMerchandise> getOuterSnByLike(Integer oid, String outerSn,Integer state);

    PdBase getPdBaseById(Integer id);

    List<MtStock> getMtStockByQualified(Integer oid,String qualified);

    List<MtStock> getMtStockByQualified1(Integer oid,String qualified);

    List<MtStock> getMtStockByQualified2(Integer oid,String qualified);

    List<MtStock> getMtStockByQualifiedByConcession(Integer oid,String qualified);//让不但

    List<MtStock> getMtStockAcceptance(Integer oid,String qualified,String state);

    List<MtStock> getMtStockAcceptanceByConcession(Integer oid,String qualified,String state);

    Map getMtStocksByStateAndType(Integer state,Integer type,Integer qualified,Integer oid,Integer per);

    List<MtStock> getMtStockAcceptance1(Integer oid,String state);

    List<MtStock> getMtStockAcceptance1ByConcession(Integer oid,String state);//让不但

    List<MtStock> getMtStockAcceptanceByConcessionApplication (Integer oid);//让不但

    Map getMtStocksByState(Integer state,Integer qualified,Integer oid,Integer per);

    MtStockInfo getMtStockInfoByMId(Integer material);

    void updateMtStockInfo(MtStockInfo stockInfo);

    MtStockInfo getMtStockInfoByProduct(Integer product);

    Map getMtStockInfoIdByItemId(Integer itemId);

    MtStockInfo getMtStockInfoById(Integer mtStockInfoId);

    void saveMtStockInfo(MtStockInfo mtStockInfo);

    List<MtStock> getMtStocksByOidAndProductId(Integer oid, Integer id);

    void saveInMtApplication(MtInApplication inApplication);//入库申请

    void saveInMtApplicationItem(MtInApplicationItem inApplicationItem);

    Map getInApplicationList(Integer oid,String state);

    Map getInListDetailByInApplicationId(Integer id);

    MtInApplicationItem getInApplicationItemById(Integer itemId);

    void saveInApplicationItemCheck(MtInApplicationItemCheck inApplicationItemCheck);

    MtInApplication getMtInApplicationById(Integer instance);

    void saveOrUpdateInApplication(MtInApplication mtInApplication);

    void updateInApplicationItem(MtInApplicationItem inApplicationItem);

    List<MtInApplicationItem> getInApplicationItemsByApplicationId(Integer instance);

    Map getQualitySituation(Integer applicationId);

    Map getMtList(Integer oid, String state, String approvalType,String user,Integer id);

    Map getCountList(Integer applicationId, String state);

    Map getListByCondition(Integer oid,Integer supplierId, String beginDate, String endDate,String dateSection, String dateType, String resultType,String state,String quantityType);

    Map getItemChecksByItemId(Integer id);

    Integer getStockCount(Integer oid, String state, String notState, Integer userId, String approvalType);

    MtStock getByApplicationItemId(Integer id);

    Integer updateInitialStock(User user, Integer id, BigDecimal oldStock, BigDecimal newStock);

    List<MtInApplicationItemCheck> getItemChecks(Integer itemId);

    Map getInList(User user,Integer pageNum,Integer per);

    Map getStockChangeRecord(Integer id, String type, String startTime, String endTime, String year,Integer pageNum, Integer per);

    void saveOrUpdateStockInfo(MtStockInfo stockInfo);

    List<MtStockInfo> getMtStockInfoByOrg(Integer oid);
}
