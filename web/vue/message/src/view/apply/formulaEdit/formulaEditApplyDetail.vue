<template>
  <div id="formulaEditApplyDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="配方修改"></TY_NavTop>
    <div v-if="formulaInfo.before">
      <div class="detailInfo">
        <div style="padding: 20px 0 0 20px">
          <div class="">
          <span>代号为{{formulaInfo.before.code}}配方的修改
            <span class="red" v-if="formulaInfo.before.code != formulaInfo.after.code">（新代号为{{formulaInfo.after.code}}）</span>
          </span>
          </div>
        </div>
        <div class="panel-content">
          <div class="panel-content">
            <div class="text-right" v-on:click="toggle = -toggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
            <div v-show="toggle > 0">
              <div class="item-content">
                <div class="processItem">
                  <div></div>
                  <div>申请人</div>
                  <div>{{formulaInfo.after.updateName }} {{formulaInfo.after.updateDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div>
                  <div v-if="formulaInfo.approval.approveStatus == '1'"  >
                    <p class="blue">{{ formulaInfo.approval.toUserName | stringSplit(4)}}为本申请的下一个审批人</p>
                  </div>
                  <div v-else-if="formulaInfo.approval.approveStatus == '2'" class="processItem" >
                    <div>审批通过</div>
                    <div>审批人</div>
                    <div>{{ formulaInfo.approval.toUserName | stringSplit(4)}} {{ formulaInfo.approval.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-else-if="formulaInfo.approval.approveStatus == '3'" class="processItem" >
                    <div>审批驳回</div>
                    <div>审批人</div>
                    <div>{{ formulaInfo.approval.toUserName | stringSplit(4)}} {{ formulaInfo.approval.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="formulaInfo.approval.approveStatus == '3'" class="red" >
                    驳回理由：{{formulaInfo.approval.reason}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="ttl3">
          <div v-bind:style="{ 'border-bottom-color': nextColor }" @click="toggle2(1)">修改后</div>
          <div v-bind:style="{ 'border-bottom-color': preColor }" @click="toggle2(0)">修改前</div>
        </div>
        <div class="con">
          <div>
            <p><span class="p1">配方代号</span> <span :class="{ red: formulaInfo.before.code != formulaInfo.after.code }">{{ baaInfo.code }}</span></p>
            <p><span class="p1">配方名称</span> <span :class="{ red: formulaInfo.before.name != formulaInfo.after.name }">{{ baaInfo.name }}</span></p>
            <p><span class="p1">材料名称</span> <span :class="{ red: formulaInfo.before.materialName != formulaInfo.after.materialName }">{{ baaInfo.materialName }}</span></p>
          </div>
          <div class="bluettl">配方中主料有如下
            <span :class="{ red: formulaInfo.before.zlList.length != formulaInfo.after.zlList.length }">{{baaInfo.zlList.length}}</span>
            种</div>
          <div>
            <a class="ui-cell" v-if="baaInfo.zlList.length > 0" v-on:click="jump(item,'1')" v-for="(item, index) in baaInfo.zlList" v-bind:key="index">
              <div class="Item" :class="{ red: item.isRed }">
                <span>
                   {{item.name}}
                  <span style="width:30px; "></span>
                  {{item.code}}
                </span>
                <span class="rt" :class="{ red: item.isRedAmount }">{{item.amount}} 份</span>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
          <div  class="bluettl">配方中辅料有如下
            <span :class="{ red: formulaInfo.before.flList.length != formulaInfo.after.flList.length }">{{baaInfo.flList.length}}</span>
            种</div>
          <div>
            <a class="ui-cell" v-if="baaInfo.flList.length > 0" v-on:click="jump(item,'2')" v-for="(item, index) in baaInfo.flList" v-bind:key="index">
              <div class="Item" :class="{ red: item.isRed }">
                <span>
                   {{item.name}}
                  <span style="width:30px; "></span>
                  {{item.code}}
                </span>
                <span class="rt" :class="{ red: item.isRedAmount }">{{item.amount}} 份</span>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #formulaEditApplyDetail{
    .red{ color: #ec6b55;  }
    .blue{ color: #0a9bee; text-align: center; margin-top: 10px;  }
    .bluettl{ line-height:30px; padding-left:20px; background:#cbe2f2;color:#333;     }
    .p1{ margin:0 20px; line-height:30px;   }
    height:100%;
    overflow: auto!important;
    .processItem{
      display: flex;
      font-size: 11px;
      margin-left:-5px;
      div:nth-child(1),div:nth-child(2){ text-align: left; flex: 1 }
      div:nth-child(3) { text-align: right; flex: 2 }
    }
    .detailInfo{
      background:#fff; font-size:14px;
      border-bottom:1px solid #ddd ;
    }
    .ttl3{
      display: flex;
      cursor:default ;
      div{ flex:1; border-bottom: 2px solid #ddd; text-align:center; line-height:40px; height:40px;     }
    }
    .Item{
      width:100% ;
      .rt{ float: right;  }
    }
  }
</style>
<script>
var that
export default {
  name: 'formulaEditApplyDetail',
  data () {
    return {
      loading: true,
      id: -1,
      toggle: -1,
      listenersUid: [],
      preColor: '#ddd',
      baaInfo: null,
      nextColor: 'rgb(54, 198, 211)',
      formulaInfo: {
        'before': null,
        'after': null,
        'approval': null
      }
    }
  },
  created () {
    that = this
    that.id = this.$route.params.id
    that.getDetails()
    this.listenersUid = [
      this.sphdSocket.subscribe('formulaEditApplyList', function (data) {
        console.log('formulaEditApplyList 返回值user:')
        let res = JSON.parse(data)
        console.log(res)
        let formula = res.ap
        let operate = Number(res.operate)
        if (operate > 0) {
        } else if (operate < 0) {
          let fid = formula.id
          if (that.approvalId === fid) {
            this.$kiko_message('该配方修改已完成审批！')
            that.getDetails()
          }
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    that.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    getDetails: function () {
      let json = { 'approvalId': that.id }
      this.axios.post('../../../formula/formulaEditDetails.do', json).then(function (response) {
        let res = response.data
        console.log('配方详情')
        console.log(res)
        that.loading = false
        that.formulaInfo = res
        that.baaInfo = that.formulaInfo.after
        let before = that.formulaInfo.before
        let after = that.formulaInfo.after
        that.compareMt(before.flList, after.flList)
        that.compareMt(before.zlList, after.zlList)
      }).catch(function (error) {
        console.log(error)
      })
    },
    jump: function (item, type) {
      let formulaInItem = {
        'fCode': that.baaInfo.code,
        'fName': that.baaInfo.name,
        'fType': type,
        'item': item
      }
      localStorage.setItem('formulaInfo', JSON.stringify(formulaInItem))
      that.$router.push({
        path: `/formulaInfo`
      })
    },
    compareMt: function (before, after) {
      for (let i = 0; i < before.length; i++) {
        let isSet = false
        let beforeItem = before[i]
        for (let j = 0; j < after.length; j++) {
          let afterItem = after[j]
          if (beforeItem.code === afterItem.code) {
            let _bool = afterItem.amount !== beforeItem.amount
            beforeItem['isRedAmount'] = _bool
            afterItem['isRedAmount'] = _bool
            isSet = true
            beforeItem['isRed'] = false
            afterItem['isRed'] = false
          }
        }
        if (!isSet) {
          beforeItem['isRed'] = true
        }
      }
      for (let k = 0; k < after.length; k++) {
        let afterItem = after[k]
        if (afterItem['isRed'] === undefined) {
          afterItem['isRed'] = true
        }
      }
      console.log(before)
      console.log(after)
    },
    toggle2: function (num) {
      if (num === 1) {
        this.preColor = '#ddd'
        this.nextColor = 'rgb(54, 198, 211)'
        that.baaInfo = that.formulaInfo.after
      } else {
        this.nextColor = '#ddd'
        this.preColor = 'rgb(54, 198, 211)'
        that.baaInfo = that.formulaInfo.before
      }
    }
  }
}
</script>
