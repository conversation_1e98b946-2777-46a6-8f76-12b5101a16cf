<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true :showBack=true @mycloseFun="handleCustomClose">
      </PageHeader>
    </div>
    <div class="sticky ty-fff" v-if="list">
      <p class="padding-0-15">{{ new
      Date(list.beginDate).format('yyyy-MM-dd') }}至{{ new
      Date(list.endDate).format('yyyy-MM-dd') }}期间收到的外部转账支票</p>
      <p class="padding-0-15">又付出去的共{{ list.numAll ? list.numAll : 0
        }}张，{{ list.amountAll == null ? 0 : this.formatCurrency(list.amountAll) }}元</p>
      <p class="padding-0-15">收款方按所收金额排列如下：</p>
    </div>
    <div class="re margin_top_16">
      <div class="spaceBetween padding-0-15 ty-fff" v-for="(item,index) in list.listMap" :key="index"
           :class="{ 'f4': index % 2 !== 0 ,'ty-fff':index.id % 2 === 0}">
        <div>
          <p>
            {{ item.oppositeCorp }}
          </p>
          <p>共给支票{{ item.num }}张，总额{{ item.amount }}</p>
        </div>
        <div class="ic" @click="rou(item)">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getReturnStatistics} from '@/api/financeBill'
import {formatCurrency} from '@/utils/utils'

export default {
  name: "cmoPayStatisticsList",
  data() {
    return {
      tit: "财会",
      list: {},
      formatCurrency: formatCurrency,
    }
  },
  methods: {
    rou(item) {
      let payStatisticsDetail = {
        oppositeCorp: item.oppositeCorp,
        beginTime: new Date(this.list.beginDate).format('yyyy-MM-dd'),
        returnId: item.id
      }
      localStorage.setItem('payStatisticsDetail', JSON.stringify(payStatisticsDetail))
      this.$router.push({path: "/cmoPayStatisticsDetail"})
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getList(payStatisticsList) {
      const params = {
        type: 1,
        beginTime: new Date(payStatisticsList.beginTime).format('yyyy-MM-dd')
      }
      getReturnStatistics(params).then(res1 => {
        this.list = res1.data.data
      })
    }
  },
  mounted() {
    let payStatisticsList = localStorage.getItem('payStatisticsList');
    this.payStatisticsList = JSON.parse(payStatisticsList);
    this.getList(this.payStatisticsList)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.sticky {
  padding: 10px 0 10px 0;
  line-height: 20px;
}

.re {
  position: relative;
  top: 40px;

  .spaceBetween {
    > div {
      padding: 10px 0;
      line-height: 20px;
    }
  }
}
</style>