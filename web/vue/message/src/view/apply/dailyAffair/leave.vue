<template>
  <div id="overTimeApply">
    <TY_NavTop title="请假申请" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in applyHandle" v-bind:key="index">
            <div class="ui-cell__hd">
              <span class="leave_type">{{item.leaveTypeName}}</span>
            </div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con_s">
                <div>开始时间：{{item.beginTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                <div>结束时间：{{item.endTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.actualState === '1'">提</div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false' :msgCount="applyApprovalCount">
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in applyApproval" v-bind:key="index">
            <div class="ui-cell__hd">
              <span class="leave_type">{{item.leaveTypeName}}</span>
            </div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con_s">
                <div>开始时间：{{item.beginTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
                <div>结束时间：{{item.endTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.actualState === '1'">提</div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>
export default {
  name: 'applyLeave',
  data () {
    return {
      tabValue: '1',
      applyHandle: [],
      applyApproval: [],
      listenersUid: []
    }
  },
  computed: {
    applyApprovalCount: function () {
      return this.applyApproval.length
    }
  },
  created () {
    this.tabValue = this.$route.params.tabValue || '1'
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('applyLeaveHandle', function (data) {
        that.applyHandle = JSON.parse(data)
        console.log('applyLeaveHandle session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('applyLeaveHandle', function (data) {
        let getData = JSON.parse(data)
        let personnelLeave = getData.personnelLeave
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.applyHandle.unshift(personnelLeave)
        } else if (operate < 0) {
          let leaveId = personnelLeave.id
          let _index = -1
          that.applyHandle.forEach(function (item, index) {
            if (item.id === leaveId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.applyHandle.splice(_index, 1)
          }
        }
        console.log('applyLeaveHandle user Socket received OK:' + data)
      }, function () {
        console.log('applyLeaveHandle user Socket Error:')
      }, 'user'),
      this.sphdSocket.subscribe('applyLeaveApproval', function (data) {
        that.applyApproval = JSON.parse(data)
        console.log('applyLeaveApproval session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('applyLeaveApproval', function (data) {
        let getData = JSON.parse(data)
        let personnelLeave = getData.personnelLeave
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.applyApproval.unshift(personnelLeave)
        } else if (operate < 0) {
          let leaveId = personnelLeave.id
          let _index = -1
          console.log(that.applyApproval)
          that.applyApproval.forEach(function (item, index) {
            if (item.id === leaveId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.applyApproval.splice(_index, 1)
          }
        }
        console.log('applyLeaveApproval user Socket received OK:' + data)
      }, function () {
        console.log('applyLeaveApproval user Socket Error:')
      }, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('applyLeave', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    doSomeThing: function () {
      // console.log("Do something!");
    },
    jump: function (id) {
      let that = this
      this.$router.push({
        path: `/applyLeaveDetail/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/applyLeaveQuery`
      })
    }
  }
}
</script>
