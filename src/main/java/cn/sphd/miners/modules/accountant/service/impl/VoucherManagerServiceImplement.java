package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucher;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucherSubject;
import cn.sphd.miners.modules.accountant.mapper.TAccountSubjectMapper;
import cn.sphd.miners.modules.accountant.mapper.VoucherMapper;
import cn.sphd.miners.modules.accountant.mapper.VoucherSubjectMapper;
import cn.sphd.miners.modules.accountant.service.VoucherManagerService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by root on 17-1-4.
 */
@Service
@Transactional(readOnly=false)
public class VoucherManagerServiceImplement implements VoucherManagerService {

    @Autowired
    VoucherMapper voucherMapper;

    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;

    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;

    @Override
    public JSONArray arrVoucherManager(Integer oid ) {
        JSONArray jsonArry = new JSONArray();
        JSONObject object = null;
        TAccountantVoucher v = new TAccountantVoucher();
        v.setOrg(oid);
        List<TAccountantVoucher> listVoucer = voucherMapper.getAllVoucher(v);
        for (TAccountantVoucher voucher : listVoucer) {
            object = new JSONObject();
            object.put("check_no",voucher.getCheckNo());
            object.put("summary",voucher.getSummary());
            object.put("operator_name",voucher.getOperatorName());
            object.put("operator_finance",voucher.getOperatorFinance());
            object.put("create_name" ,voucher.getCreateName());
            object.put("create_date",voucher.getCreateDate());
            object.put("update_name",voucher.getUpdateName());
            if (voucher.getBillDate() != null)
                object.put("bill_date",voucher.getBillDate());
            TAccountantVoucherSubject vs = new TAccountantVoucherSubject();
            vs.setVoucherId(voucher.getId());
            vs.setDirection("1");
            List<TAccountantVoucherSubject> listBorrow = voucherSubjectMapper.listByVoucherDirection(vs) ;
            if(listBorrow.size() > 0)
            {
                for (TAccountantVoucherSubject avs : listBorrow ) {
                    avs.setSubjectName(getSubjectName(avs));
                }
                object.put("listBorrow",listBorrow);
            }
            vs.setDirection("2");
            List<TAccountantVoucherSubject> listLoan = voucherSubjectMapper.listByVoucherDirection(vs) ;
            if(listLoan.size() > 0)
            {
                for (TAccountantVoucherSubject avs : listLoan ) {
                    avs.setSubjectName(getSubjectName(avs));
                }
                object.put("listLoan",listLoan);
            }
            jsonArry.put(object);
        }
        return jsonArry;
    }

    public String  getSubjectName(TAccountantVoucherSubject v){
        TAccountantSubjectEntity as = new TAccountantSubjectEntity();
        as.setOrg(v.getOrg());
        as.setSubject(v.getSubject());
        TAccountantSubjectEntity s = tAccountSubjectMapper.subjectAllMessage(as);
        String subjectName = s.getName();
        if (s.getParent() != null) {
            as.setSubject(s.getParent());
            TAccountantSubjectEntity ss = tAccountSubjectMapper.subjectAllMessage(as);
            subjectName = ss.getName() + "-" +subjectName;
            if (ss.getParent() != null) {
                as.setSubject(ss.getParent());
                TAccountantSubjectEntity sss = tAccountSubjectMapper.subjectAllMessage(as);
                subjectName = sss.getName() + "-" +subjectName;
            }
        }
        return subjectName;
    };
}
