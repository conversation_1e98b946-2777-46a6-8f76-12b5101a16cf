import {request, noloadingHttp} from "@/utils/request";

/**
 * <AUTHOR>
 * @description 登录，先调用游客token清除之前token信息，然后调用登录
 * @date Create at 2023/9/15 08:04
 * @method login
 * @param logonName 帐号
 * @param logonPwd 密码
 * @returns {*}
 */
export function login(logonName, logonPwd) {
    return request({
        url: '/sys/login.do',
        data: {
            logonName: logonName,
            logonPwd: logonPwd
        }
    })
}
export function getServerTail() {
    return noloadingHttp({
        url: '/sys/getServerTail.do'
    })
}

// 
/*
 * creator: 2022-8-16  hxz
 *  领地/机构内都可用 获取头像图片展示的跟路径
 * params:  
*/
export function getRootPathFile() {
    return request({
        url: '/uploads/getRootPath.do',
    })
}

/*
 * creator: 2022-8-16  hxz
 *   机构列表 
 * params:  
*/
export function organizationList(data) {
    return request({
        url: '/sys/getOrgListPageInfo.do',
        data: data
    })
}

/*
 * creator: 2024-4-14  hxz
 *   更换图片
 * params:
*/
export function updateUserimgByUserID(data) {
    return request({
        url: '/general/updateUserimgByUserID.do',
        data: data
    })
}

/*
 * creator: 2023-10-24 hxz
 * 调用Wonderss完成微信等第三方登陆
*/
export function mpCodeLogin (data) {
    return request({
        url: '/tp/mpCodeLogin.do',
        data: data
    })
}

/*
 * creator: 2023-10-24 hxz
 * 注册页 发送短信验证码
 * params: phone 手机号
 * 返回值：data 中： 1-成功 0-失败，3-手机号格式不对，5 验证码已经发过还未过时
*/
export function sendMobileCode (data) {
    return request({
        url: '/auth/loginVerificationCode.do',
        data: data
    })
}
/*
 * creator: 2023-10-24 hxz
 * 登陆 校验 验证码
 * params: mobile 手机号 code 验证码
*/
export function verificationCode (data) {
    return request({
        url: '/auth/verificationCodeAndLogin.do',
        data: data
    })
}

/*
 * creator: 2024-10-31 hxz
 * 第89天 第一次登录， 判断新账号是否存在（存在 给原型选项，不存在 就是 下一步掉激活账号 unlockAcc.do）
 * 返回值： success 1 操作成功，返回 id / success 0 抛出失败信息 ”新账号不存在”
 {"page":null,"success":1,"data":"25","error":null,"others":null}
*/
export function unlockGetNewAccId () {
    return request({
        url: '/auth/unlockGetNewAccId.do',
    })
}

/*
 * creator: 2024-10-31 hxz
 * 第89天 第一次登录， 选择 使用新领地
 * 返回值： success 1 操作成功， / success 0 抛出失败信息 ”新账号不存在”
 * 返回值实例： {"page":null,"success":1,"data":"操作成功","error":null,"others":null}
 */
export function selectNewAcc () {
    return request({
        url: '/auth/selectNewAcc.do',
    })
}

/*
 * creator: 2024-10-31 hxz
*
 * 第89天 第一次登录， 选择 使用被冻结的原领地
 * 返回值： success 1 操作成功， / success 0 抛出失败信息 ”新账号不存在”
 * 返回值实例： {"page":null,"success":1,"data":"操作成功","error":null,"others":null}
 */
export function selectOldAcc () {
    return request({
        url: '/auth/selectOldAcc.do',
    })
}

export function verificationCodeStart (data) {
    return request({
        url: '/sendMessage/checkVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2023-10-24 hxz
 * 登陆 验证码方式
 * params: mobile 手机号 code 验证码
*/
export function codeLogin (data) {
    return request({
        url: '/sys/codeLogin.do',
        data: data
    })
}

/*
 * creator: 2024-09-04 hxz
 * 自定义的安全问题 不含答案 （账号验证时用）
 * params:
 * 备注： 如果 返回值 自定义的安全问题不存在，需调用 文档中默认安全问题第一题 接口 defaultQuestionBankOne.do
*/
export function ownSecurityQuestion () {
    return request({
        url: '/authQuestion/ownSecurityQuestion.do',
    })
}
/*
 * creator: 2024-09-04 hxz
 * 默认生成安全问题 第一题 （账号验证时用）
 * params:  phone 手机号
 * 备注： 按需求 随机给出19各干扰项， 1个正确机构名
*/
export function defaultQuestionBankOne (data) {
    return request({
        url: '/authQuestion/defaultQuestionBankOne.do',
        data
    })
}
/*
 * creator: 2024-09-04 hxz
 * 回答自定义的问题答案验证 （账号验证时用）
 * 传入参数： answers（传值字段名）= [{id 问题id ，answer 回答内容},{id 问题id ，answer 回答内容}] (格式为 json 转String的字符串)
    返回值： success 1 认证通过 / success 0 验证失败 抛出失败信息 “对不起，您的答案不对”。
*/
export function answerSecurityQuestion (data) {
    return request({
        url: '/authQuestion/answerSecurityQuestion.do',
        data
    })
}

/*
 * creator: 2024-09-26 hxz
 *  激活账号 第76日-第88日间的第一次登录，方式为获取登录者一次短信验证码，且无需改密码，
 * 传入参数： 无
    返回值： success 1 操作成功 / success 0 抛出失败信息 ”您已操作完成，无需再操作了！”
*/
export function unlockAcc (data) {
    return request({
        url: '/auth/unlockAcc.do',
        data
    })
}

/*
 * creator: 2024-09-04 hxz
 * 验证 默认第一题 回答是否正确 （账号验证时用）
 * 传入参数：  phone 手机号 ，orgName 选择的机构
 * 返回值： success 1 回答正确 data返回人员数量 /
 *        success 0 验证失败 抛出失败信息 :
 *              “对不起，您的答案不对！再选错"+number+"次，您私人领地内的数据将无法再访问！”，
 *              “对不起，您没能答对安全问题，您私人领地内的数据已无法再访问！您依旧可登录系统，但需请公司总务协助操作！”。
 * 备注： 如果验证成功 给予默认第二题
*/
export function verifyQuestionBankOne (data) {
    return request({
        url: '/authQuestion/verifyQuestionBankOne.do',
        data
    })
}

/*
 * creator: 2024-09-04 hxz
 * 验证 默认第二题 回答是否正确 （账号验证时用）
 * 传入参数： phone 手机号 ，orgName 选择的机构， userNames 输入的人名 （传值格式 userNames=姓名1,姓名2，姓名3 ）
 * 返回值： success 1 本次认证通过 / success 0 验证失败
 * 抛出失败信息 “对不起，您的答案不对！再选错"+number+"次，您私人领地内的数据将无法再访问！”，
 *            “对不起，您没能答对安全问题，您私人领地内的数据已无法再访问！您依旧可登录系统，但需请公司总务协助操作！”。
*/
export function verifyQuestionBankTwo (data) {
    return request({
        url: '/authQuestion/verifyQuestionBankTwo.do',
        data
    })
}
/*
 * creator: 2024-09-04 hxz
 * 微信扫码登陆成功后 76-88 日 或者 >89日 采用其他手机号作为账号 发送 短信验证码
 * 传入参数： phone 手机号
 * 返回值：  success 1 发送成功 / success 0 失败 抛出失败信息 “手机号长度不对”，“手机号格式不对”， “验证码未过时”。
*/
export function otherMobileVerificationCode (data) {
    return request({
        url: '/auth/otherMobileVerificationCode.do',
        data
    })
}
/*
 * creator: 2024-09-05 hxz
 * 微信扫码登陆成功后 76-88 日 或者 >89日 采用其他手机号作为账号 验证 短信验证码
 * 传入参数： phone 手机号， code 验证码
 * 返回值：success 1 验证通过 / success 0 验证失败 抛出失败信息 ”验证码错误或者超时！“
*/
export function checkOtherMobileVerificationCode (data) {
    return request({
        url: '/auth/checkOtherMobileVerificationCode.do',
        data
    })
}


/*
 * creator: 2022-8-16  hxz
 *   登录某个工作室 
 * params:   mobile， oid
 * res: user{"userName"姓名 ,"roleCode",}
*/
export function sureLogin(data) {
    data = data || {}
    return request({
        url: '/sys/sureLogin.do',
        data: data
    })
}
/*
 * creator: 2024-1-24 hxz
 *   获取 用户菜单列表
*/
export function getAllMenu(data) {
    data = data || {}
    return request({
        url: '/sys/getAllMenu.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 *   获取 关于 菜单
*/
export function getAboutInitialDirectory() {
    return request({
        url: '/about/getAboutInitialDirectory.do',
    })
}

/*
 * creator: 2024-5-27
 * 修改密码 - 修改密码提交接口(首页）
*/
export function submit_changePassword_start(data) {
    data = data || {}
    return request({
        url: '/auth/changePassword.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 *   机构内 修改手机号 给旧手机号 发送验证码
*/
export function sendCode_oldPhone(data) {
    data = data || {}
    return request({
        url: '/userInfo/orgEditPhoneVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2024-5-24 zxb
 * 修改用户账号 - 验证 机构内 修改手机号 给旧手机号 发送验证码
*/
export function checkCode_oldPhone(data) {
    data = data || {}
    return request({
        url: '/userInfo/checkOrgEditVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 *   机构内 修改手机号 给旧手机号 发送验证码
*/
export function sendCode_newPhone(data) {
    data = data || {}
    return request({
        url: '/userInfo/orgNewPhoneVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2024-5-27 zxb
 * 修改用户账号 - 验证 机构内 修改手机号 给新手机号 发送验证码
*/
export function checkCode_newPhone(data) {
    data = data || {}
    return request({
        url: '/userInfo/checkOrgNewPhoneVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 * 修改用户账号 - 最后的修改密码（跟修改密码功能的修改密码非同一接口）
*/
export function changePassword_changePhone(data) {
    data = data || {}
    return request({
        url: '/userInfo/updateUserPhonePassWord.do',
        data: data
    })
}

/*
 * creator: 2024-5-17 zxb
 * 修改密码 - 发送验证码 - 直接修改密码部分，验证手机号
*/
export function sendCode_changePassword(data) {
    data = data || {}
    return request({
        url: '/auth/editPasswordVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 * 修改密码 - 验证 修改密码 验证码
*/
export function checkCode_changePassword(data) {
    data = data || {}
    return request({
        url: '/auth/checkPasswordVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 * 修改密码 - 修改密码提交接口
*/
export function resetLoginPassWord(data) {
    data = data || {}
    return request({
        url: '/auth/editPassword.do',
        data: data
    })
}

/**
 * 校验密码
 * <AUTHOR>
 */
export function checkPassword(data) {
    data = data || {}
    return request({
        url: '/sys/checkPassword.do',
        dataType: "text",
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 *   登出系统
*/
export function logout() {
    return request({
        url: '/sys/logout.do',
    })
}

/*
 * creator: 2024-1-30 hxz
 *   获取领导信息
*/
export function getLeaderUserName(data) {
    data = data || {}
    return request({
        url: '/userInfo/getLeaderUserName.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 *   输入手机号 查重接口
*/
export function mobileCheckRepeat(data) {
    data = data || {}
    return request({
        url: '/userInfo/mobileCheckRepeat.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 *  打开页面首次获取讨论区和旧消息角标数量
*/
export function getMessageNum(data) {
    data = data || {}
    return request({
        url: '/message/getMessageNum.do',
        data: data
    })
}

/*
 * creator: 2024-1-30 hxz
 *  password
*/
export function refreshOperatortime(data) {
    data = data || {}
    return noloadingHttp({
        url: '/sys/refreshOperatortime.do',
        data: data,
    })
}

/*
 * creator: 2024-1-30 hxz
 *  这是一个 demo 方法
*/
export function getDemoList(data) {
    data = data || {}
    return request({
        url: '/user/getSystemSetStatus.do',
        data: data,
    })
}

/*
* creator: sy 2024-04-19    获取装备器具数据
* */
export function initCractern(data) {
    data = data || {}
    return request({
        url:"/equipment/list",
        method: 'post',
        data: data,
    })
}

/*
*   creator: sy 2024-05-21  点击新增装备名称弹窗中的“确定”按钮
 */
export function addnamer(data){
    data = data || {};
    return request({
        url:'/equipment/add',
        data: data
    })
}

/*
*   creator: sy 2024-05-28  点击修改装备器具名称弹窗中的“确定”按钮
 */
export function upmodif(data){
    data = data || {};
    return request({
        url:'/equipment/edit',
        data: data
    })
}

/*
*   creator: sy 2024-05-28  点击‘名称修改记录’按钮在弹窗中展示相应内容
 */
export function upname(data){
    data = data || {};
    return request({
        url:"/equipment/history/details",
        data: data
    })
}

/*
*   creator: sy 2024-05-29  点击单行的数量数字跳转并获取相应型号数据
 */
export function quantity(data){
    data = data || {};
    return request({
        url:"/equipment/model/groupList",
        method: 'get',
        data:data
    })
}

/*
*   creator: sy 2024-06-03  点击‘直接查看全部’获取数据
 */
export function getlooker(data){
    data = data || {};
    return request({
        url:"/equipment/model/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-07  获取类别管理表格数据
 */
export function getcatmsg(data){
    data = data || {};
    return request({
        url:"/equipment/category/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-07  获取被停用的类别数据
 */
export function stopnusern(data){
    data = data || {};
    return request({
        url:"/equipment/category/list",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2024-06-18  点击确定按钮进行类别新增
 */
export function addcamadn(data){
    data = data || {};
    return request({
        url:"/equipment/category/add",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2024-06-19  获取一级直属的子类别
 */
export function getchildcat(data){
    data = data || {};
    return request({
        url:"/equipment/category/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-20  获取修改记录
 */
export function catrecordn(data){
    data = data || {};
    return request({
        url:"/equipment/category/history/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-20  修改类别信息
 */
export function upcatmsgsure(data){
    data = data || {};
    return request({
        url:"/equipment/category/edit",
        method:"post",
        data: data
    })
}

/*
*   creator: sy 2024-06-24  停用类别数据
 */
export function stoppened(data){
    data = data || {};
    return request({
        url:"/equipment/category/edit",
        method:"post",
        data: data
    })
}

/*
*   creator: sy 2024-06-26  删除类别数据
 */
export function detcatet(data){
    data = data || {};
    return request({
        url:"/equipment/category/remove",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-26  恢复选中的数据
 */
export function restorecho(data){
    data = data || {};
    return request({
        url:"/equipment/category/edit",
        method: "post",
        data: data
    })
}


/*
*   creator: sy 2024-07-17  创建子工序选项
 */
export function createProcess2(data){
    data = data || {};
    return request({
        url:"/processSettings/createActivity.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-17  获取类别管理列表或是被停用类别数据
 */
export function getcatorylist(data){
    data = data || {};
    return request({
        url:"/processSettings/getCategoryList.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-18  新增类别
 */
export function addcatCory(data){
    data = data || {};
    return request({
        url:"/processSettings/createCategory.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-18  类别的停用/复用
 */
export function catuseory(data){
    data = data || {};
    return request({
        url:"/processSettings/updateCategoryEnabled.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-22  获取选项管理的统计数据
 */
export function getalloption(data){
    data = data || {};
    return request({
        url:"/processSettings/getOptionManagements.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-24  展示去管理获取的列表
 */
export function getToManageList(data){
    data = data || {};
    return request({
        url: '/processSettings/getToManageList.do',
        method: 'post',
        data: data
    })
}

/*
*   creator: hxz 2024-11-20  75天 激活后 设置密码
 */
export function otherChangePassword(data){
    data = data || {};
    return request({
        url: '/auth/otherChangePassword.do',
        data
    })
}


/*
*   creator: hxz 2024-12-8  提交注册机构申请
 */
export function registeredOrgApply(data){
    data = data || {};
    return request({
        url: '/special/registeredOrgApply.do',
        data
    })
}
/*
*   creator: lyt 2025-02-09  某机构初始化有多步时，各步的顺序按1权限2会计3财务装备器具…采购（含原辅材料库）得顺序
 */
export function judgeAccountingRes(data){
    data = data || {};
    return request({
        url: '/initialize/judgeAccountingEnd.do',
        data
    })
}



