package cn.sphd.miners.modules.dmmSchedule.service;

import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleImage;

import java.util.List;

/**
 * Created by Administrator on 2019/8/13.
 */
public interface DmmScheduleImageService {

    void deleteDmmScheduleImage(DmmScheduleImage dmmScheduleImage);

    void  deleteAllDmmScheduleImage(Integer scheduleId);

    List<DmmScheduleImage> getDmmScheduleImageListBySid(Integer scheduleId);

    DmmScheduleImage getDmmScheduleImageById(Integer id);
}
