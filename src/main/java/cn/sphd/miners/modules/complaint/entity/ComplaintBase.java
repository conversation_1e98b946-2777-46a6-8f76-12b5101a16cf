package cn.sphd.miners.modules.complaint.entity;

import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-01-12 15:23
 * @description
 **/
@Entity (name="ComplaintBase")
@Table(name = "t_complaint_base")
public class ComplaintBase implements Serializable{
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "org")
    private Integer org;

    @Column(name = "peroid")
    private Integer peroid;

    @Column(name = "customer")
    private Integer customer;

    @Column(name = "contact_name")
    private String contactName;

    @Column(name = "contact")
    private String contact;
    @Column(name = "contact_phone")
    private String contactPhone;

    @Column(name = "post")
    private String post;
    @Column(name = "receipt_time")
    private Date receiptTime;
    @Column(name = "type")
    private String type;


    @Column(name = "content")
    private String content;
    @Column(name = "state", length=1)
    private String state;

    @Column(name = "terminate_state", length=1)
    private String terminateState;

    @Column(name = "register_approve_time")
    private Date registerApproveTime;
    @Column(name = "register_approver")
    private Integer registerApprover;
    @Column(name = "register_reject_reason")
    private String registerRejectReason;
    @Column(name = "processor")
    private Integer processor;
    @Column(name = "settle_type", length=1)
    private String settleType;
    @Column(name = "settle_opinion")
    private String settleOpinion;
    @Column(name = "settler")
    private Integer settler;
    @Column(name = "settle_time")
    private Date settleTime;
    @Column(name = "settle_reject_reason")
    private String settleRejectReason;
    @Column(name = "settle_approver")
    private Integer settleApprover;
    @Column(name = "settle_approve_time")
    private Date settleApproveTime;
    @Column(name = "memo")
    private String memo;
    @Column(name = "initial_receipter")
    private Integer initialReceipter;
    @Column(name = "initial_handler")
    private Integer initialHandler;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_time")
    private Date updateTime;

    /*@OrderBy("id desc")
    @JsonIgnore
    @OneToMany(targetEntity=ComplaintSupplement.class, fetch= FetchType.EAGER, mappedBy="baseSupplement", cascade= CascadeType.REMOVE)
    private Set<ComplaintSupplement> supplementSet = new HashSet<ComplaintSupplement>();

    @JsonIgnore
    @OneToMany(targetEntity=ComplaintBaseHistory.class, fetch= FetchType.EAGER, mappedBy="historyComplaintBase", cascade= CascadeType.REMOVE)
    private Set<ComplaintBaseHistory> complaintBaseHistorySet = new HashSet<ComplaintBaseHistory>();*/

/*    @JsonIgnore
    @OneToMany(targetEntity=ComplaintDetail.class, fetch= FetchType.LAZY, mappedBy="detailComplaintBase", cascade= CascadeType.REMOVE)
    private Set<ComplaintDetail> complaintDetailSet = new HashSet<ComplaintDetail>();*/

    /*@JsonIgnore
    @OneToMany(targetEntity=ComplaintContact.class, fetch= FetchType.LAZY, mappedBy="contactComplaintBase", cascade= CascadeType.REMOVE)
    private Set<ComplaintContact> complaintContactSet = new HashSet<>();*/

    @Transient
    private List<ApprovalProcess> approvalProcessList;    //审批流程的list

    @Transient
    private Long countNum;//客户查询中统计的投诉数

    @Transient
    private String customName;//客户名字
    @Transient
    private String customCode;//客户编号

    @Transient
    private int coreCode;//权限   1-核心人物

    @Transient
    private String settlerName;//结案处理者名字

    @Transient
    private String processorName;//处理人名字

    public int getCoreCode() {
        return coreCode;
    }

    public void setCoreCode(int coreCode) {
        this.coreCode = coreCode;
    }


    public String getSettlerName() {
        return settlerName;
    }

    public void setSettlerName(String settlerName) {
        this.settlerName = settlerName;
    }

    public List<ApprovalProcess> getApprovalProcessList() {
        return approvalProcessList;
    }

    public void setApprovalProcessList(List<ApprovalProcess> approvalProcessList) {
        this.approvalProcessList = approvalProcessList;
    }

    public String getProcessorName() {
        return processorName;
    }

    public void setProcessorName(String processorName) {
        this.processorName = processorName;
    }

    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public String getCustomCode() {
        return customCode;
    }

    public void setCustomCode(String customCode) {
        this.customCode = customCode;
    }

    public Long getCountNum() {
        return countNum;
    }

    public void setCountNum(Long countNum) {
        this.countNum = countNum;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public Integer getCustomer() {
        return customer;
    }

    public void setCustomer(Integer customer) {
        this.customer = customer;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getReceiptTime() {
        return receiptTime;
    }

    public void setReceiptTime(Date receiptTime) {
        this.receiptTime = receiptTime;
    }

    public Date getRegisterApproveTime() {
        return registerApproveTime;
    }

    public void setRegisterApproveTime(Date registerApproveTime) {
        this.registerApproveTime = registerApproveTime;
    }

    public Integer getRegisterApprover() {
        return registerApprover;
    }

    public void setRegisterApprover(Integer registerApprover) {
        this.registerApprover = registerApprover;
    }

    public String getRegisterRejectReason() {
        return registerRejectReason;
    }

    public void setRegisterRejectReason(String registerRejectReason) {
        this.registerRejectReason = registerRejectReason;
    }

    public Integer getProcessor() {
        return processor;
    }

    public void setProcessor(Integer processor) {
        this.processor = processor;
    }

    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public String getSettleOpinion() {
        return settleOpinion;
    }

    public void setSettleOpinion(String settleOpinion) {
        this.settleOpinion = settleOpinion;
    }

    public Integer getSettler() {
        return settler;
    }

    public void setSettler(Integer settler) {
        this.settler = settler;
    }

    public Date getSettleTime() {
        return settleTime;
    }

    public void setSettleTime(Date settleTime) {
        this.settleTime = settleTime;
    }

    public String getSettleRejectReason() {
        return settleRejectReason;
    }

    public void setSettleRejectReason(String settleRejectReason) {
        this.settleRejectReason = settleRejectReason;
    }

    public Integer getSettleApprover() {
        return settleApprover;
    }

    public void setSettleApprover(Integer settleApprover) {
        this.settleApprover = settleApprover;
    }

    public Date getSettleApproveTime() {
        return settleApproveTime;
    }

    public void setSettleApproveTime(Date settleApproveTime) {
        this.settleApproveTime = settleApproveTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getInitialReceipter() {
        return initialReceipter;
    }

    public void setInitialReceipter(Integer initialReceipter) {
        this.initialReceipter = initialReceipter;
    }

    public Integer getInitialHandler() {
        return initialHandler;
    }

    public void setInitialHandler(Integer initialHandler) {
        this.initialHandler = initialHandler;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTerminateState() {
        return terminateState;
    }

    public void setTerminateState(String terminateState) {
        this.terminateState = terminateState;
    }

    public Integer getPeroid() {
        return peroid;
    }

    public void setPeroid(Integer peroid) {
        this.peroid = peroid;
    }
}
