<template>
	<!-- 已移出团队的成员 -->
	<div class="uni-container">
		<Pageheader title="团队成员管理" :showBack="true"/>

    <div class="live-panel">
      <div class="live-tr litF" @click="getMsg">
        <span>已移出您团队的成员现共{{ list.length }}名</span>
      </div>
      <div class="officeList">
        <component :is="LiveTable" :columns="columns" :list="list" @longPress="longPress"></component>
      </div>
    </div>
    <BottomDialog v-show="isShown">
      <div class="live-actionSheet" @click="rebackTeam">
        重新移入团队
      </div>
      <div class="live-actionSheet" @click="hideDialog">
        取 消
      </div>
    </BottomDialog>


	</div>
</template>
<!--

<script>

	export default {
		data() {
			return {
				columns: [{
						title: "抖音名",
						key: "name",
						flex:1
					},
					{
						title: '加入时间',
						key: 'time',
						flex:2
					},
					{
						title: '手机号(账号)',
						key: 'mobile',
						flex:1
					}
				],
				list:[],
				// list: [{
				// 			name: '<PERSON>',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",

				// 		},{
				// 			name: 'John Brown',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",

				// 		},{
				// 			name: 'John Brown',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",

				// 		},{
				// 			name: 'John Brown',
				// 			time: "2020-02-02 12:12:12",
				// 			mobile: '15202255153',
				// 			id: "1",

				// 		},
				// 	],
			}
		},
		onLoad(){
			this.getOutMembers();
		},
		methods: {
			// longPress:function(item){
			// 	console.log('backUserId=' , item)
			// 	uni.showActionSheet({
			// 		itemList: ['重新移入团队', '取 消'],
			// 		success: function (res) {
			// 			if(res.tapIndex === 0){
			// 				// 重新移入团队
			// 				memberBackTeam({ 'backUserId': item.id }).then(res => {
			// 					console.log('memberBackTeam=' , res)
			// 					if(res.data){
			// 						uni.showToast({
			// 							title: '操作成功',
			// 							duration: 1000,
			// 							icon:'none'
			// 						});
			// 						that.list.splice(index,1);
      //
			// 					}else{
			// 						uni.showToast({
			// 							title: '操作失败',
			// 							duration: 1000,
			// 							icon:'none'
			// 						});
      //
			// 					}
      //
			// 				})
			//
			// 			}else{}
			// 		},
			// 		fail: function (res) {
			// 			console.log(res.errMsg);
			// 		}
			// 	});
			//
			// },
			// getOutMembers: function(){
			// 	getOutTeamMembers().then(res => {
			// 		console.log('getOutTeamMembers = ', res.data)
			// 		let members = res.data.data;
      //
			// 		if(members.length > 0){
			// 			members.forEach(item => {
			// 				if(item.submitState == 0){
			// 					item.mobile = '已设为不展示'
			// 				}
			// 				this.list.push({
			// 					name: item.userName,
			// 					time: "2020-02-02 12:12:12",
			// 					mobile: item.mobile,
			// 					id: item.userID,
			// 				})
			// 			});
			//
			// 		}
			// 	})
			// },
		}
	}
</script>
-->

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from 'element-plus'
import { getOutTeamMembers, memberBackTeam } from '@/api/api.js'

import LiveTable from '../../components/liveTable.vue'
import BottomDialog from '../../components/BottomDialog.vue'
let columns2 = [
  {
    title: "抖音名",
    key: "name",
    flex:1
  },
  {
    title: '加入时间',
    key: 'time',
    flex:2
  },
  {
    title: '手机号(账号)',
    key: 'mobile',
    flex:1
  }
]
const list = reactive([])
const columns = columns2
const isShown = ref(false)
let editUser = {}

onMounted(()=>{
  getOutMembers()
})

const getOutMembers = function(){
  getOutTeamMembers().then(res => {
    console.log('getOutTeamMembers response', res )
    let members = res.data;
    if(members.length > 0){
      members.forEach(item => {
        if(item.submitState == 0){
          item.mobile = '已设为不展示'
        }
        list.push({
          name: item.userName,
          time: "2020-02-02 12:12:12",
          mobile: item.mobile,
          id: item.userID,
        })
      });
    }
  }).catch(res =>{
    console.log('txt error', res)
  });
}

const longPress = (item, index) => {
  console.log('index =', index);
  console.log(item)
  item.index = index
  editUser = item
  isShown.value = true

}
const hideDialog = function(){
  isShown.value = false

}
const rebackTeam = function (){
  console.log('editUser', editUser)
  memberBackTeam({ backUserId: editUser.id }).then((res)=>{
    console.log('memberBackTeam res:', res)
    isShown.value = false

    if(res){
      ElMessage.success({
        message: '操作成功',
        type: 'success',
      })
      list.splice(editUser.index,1);

    }else{
      ElMessage.error('操作失败')
    }
  })

}


</script>

<style lang="scss" scoped>
.litF{ font-size: 0.9em; }
</style>
