package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillDao;
import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillItemDao;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBill;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillItem;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/12/5.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceReimburseBillServiceImpl implements FinanceReimburseBillService {

    @Autowired
    FinanceReimburseBillDao financeReimburseBillDao;
    @Autowired
    FinanceReimburseBillItemDao financeReimburseBillItemDao;


    @Override
    public void saveFinanceReimburseBill(FinanceReimburseBill financeReimburseBill) {
        financeReimburseBillDao.save(financeReimburseBill);
    }

    @Override
    public FinanceReimburseBill getFinanceReimburseBillById(Integer id) {
        return financeReimburseBillDao.get(id);
    }

    @Override
    public void updateFinanceReimburseBill(FinanceReimburseBill financeReimburseBill) {
        financeReimburseBillDao.update(financeReimburseBill);
    }

    @Override
    public void saveFinanceReimburseBillItem(FinanceReimburseBillItem financeReimburseBillItem) {
        financeReimburseBillItemDao.save(financeReimburseBillItem);
    }

    @Override
    public List<FinanceReimburseBill> getFinanceReimburseBillByCertificationState(Integer oid, String certificationState) {
        String hql=" and o.reimburse.approveStatus='2' and o.reimburse.billCatName='增值税专用发票' and o.reimburse.user.oid="+oid+" and (o.certificationState='"+certificationState+"' or o.certificationState is null)";
        Map<String,String> orderBy=new HashMap<>();
        orderBy.put("o.id","desc");
        List<FinanceReimburseBill> financeReimburseBills=financeReimburseBillDao.findCollectionByConditionNoPage(hql,null,orderBy);
        return financeReimburseBills;
    }

    @Override
    public List<FinanceReimburseBill> getFinanceReimburseBillByPid(Integer pid) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceReimburseBill where primaryBill =: pid or id=:id";
        map.put("pid",pid);
        map.put("id",pid);
        return financeReimburseBillDao.getListByHQLWithNamedParams(hql,map);
    }
}

