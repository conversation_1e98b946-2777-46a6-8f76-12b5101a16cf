package cn.sphd.miners.modules.finance.dto;

import java.util.Date;
import java.util.List;

/**
 * @ClassName SalaryByUser
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/21 8:33
 * @Version 1.0
 */
public class PayPeriodDto {
    private Integer id;
    private Integer org;
    private String period; //所属月份
    private Integer salaryState; //工资状态:0-未录入(默认),1-需要继续录入,2-已录完
    private Integer incomeTaxState; //所得税状态:0-未录入(默认),1-需要继续录入,2-已录完
    private Integer socialSecurityState; //社保状态:0-未录入(默认),1-需要继续录入,2-已录完
    private Integer accumulationFundState; //公积金状态:0-未录入(默认),1-需要继续录入,2-已录完
    private Date dateSalary;  //工资修改的时间(页面显示的录入后的时间)
    private Date dateIncomeTax;  //所得税修改的时间(页面显示的录入后的时间)
    private Date dateSocialSecurity;  //社保修改的时间(页面显示的录入后的时间)
    private Date dateAccumulation;  //公积金修改的时间(页面显示的录入后的时间)

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Integer getSalaryState() {
        return salaryState;
    }

    public void setSalaryState(Integer salaryState) {
        this.salaryState = salaryState;
    }

    public Integer getIncomeTaxState() {
        return incomeTaxState;
    }

    public void setIncomeTaxState(Integer incomeTaxState) {
        this.incomeTaxState = incomeTaxState;
    }

    public Integer getSocialSecurityState() {
        return socialSecurityState;
    }

    public void setSocialSecurityState(Integer socialSecurityState) {
        this.socialSecurityState = socialSecurityState;
    }

    public Integer getAccumulationFundState() {
        return accumulationFundState;
    }

    public void setAccumulationFundState(Integer accumulationFundState) {
        this.accumulationFundState = accumulationFundState;
    }


    public Date getDateSalary() {
        return dateSalary;
    }

    public void setDateSalary(Date dateSalary) {
        this.dateSalary = dateSalary;
    }

    public Date getDateIncomeTax() {
        return dateIncomeTax;
    }

    public void setDateIncomeTax(Date dateIncomeTax) {
        this.dateIncomeTax = dateIncomeTax;
    }

    public Date getDateSocialSecurity() {
        return dateSocialSecurity;
    }

    public void setDateSocialSecurity(Date dateSocialSecurity) {
        this.dateSocialSecurity = dateSocialSecurity;
    }

    public Date getDateAccumulation() {
        return dateAccumulation;
    }

    public void setDateAccumulation(Date dateAccumulation) {
        this.dateAccumulation = dateAccumulation;
    }
}
