<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="approvalSettingsSearchDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="ttl" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ttl11">
        <p>您要查询：</p>
        <!--<p v-if="applyTime === 1 || applyTime === 2">{{ applyTime | chargeTime}}</p>-->
        <p>{{ beginTime |  formatDay('YYYY年MM月DD日')}} - {{ endTime |  formatDay('YYYY年MM月DD日')}}期间</p>
        <p>被{{chargeResult | isChargeResult}}的审批权限修改申请</p>
      </div>
      <div class="ttl1">符合查询条件的数据共如下{{list.length}}条。</div>
      <div>
        <a class="ui-cell" @click="applyDetail(item.id)" v-for='(item, index) in list' v-bind:key='index'>
          <div class="">
            <p>{{item.description}}</p>
            <p class="right"><span>{{item.askName}}</span> <span>{{item.createDateItem | formatDay('YYYY-MM-DD HH:mm:ss')}}</span></p>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #approvalSettingsSearchDetail{
    .ttl1{ background:#DEEBF6; padding:10px;     }
    .ttl11{ background: #edf3c6; padding:10px;     }
    .clr2{clear: both;  }
    .btn{ border:1px solid #ccc;padding:5px 15px; border-radius:4px;   }
    .active{ border-color:#0da394; color: #0da394;  }
    .right{
      text-align:right; color:#aaa; font-size:0.7em; width:370px ;
      span{ margin-right: 20px;  }
    }
  }
</style>
<script>
export default {
  name: 'approvalSettingsSearchDetail',
  data () {
    return {
      searchInfo: this.$store.getters.getApprovalSettingSearchInfo,
      loading: true,
      beginTime: '',
      endTime: '',
      chargeResult: '',
      searchType: Number(this.$route.params.id),
      applyTime: '',
      ttl: '审批设置的修改申请',
      list: []
    }
  },
  filters: {
    chargeTime: function (num, begin, end) {
      console.log('事件类型', num)
      switch (Number(num)) {
        case 1: return '近七日'
        case 2: return '本月'
        default:
      }
    },
    isChargeResult: function (num) {
      switch (Number(num)) {
        case 2: return '批准'
        case 3: return '驳回'
        default:
      }
    }
  },
  created: function () {
    let _this = this
    _this.beginTime = _this.searchInfo.beginTime
    _this.endTime = _this.searchInfo.endTime
    _this.chargeResult = _this.searchInfo.chargeResult
    _this.applyTime = _this.searchInfo.applyTime
    _this.getList()
    console.log(_this.searchType === 2)
    if (_this.searchType === 2) {
      console.log('进来了')
      _this.ttl = '审批设置的修改审批'
    }
  },
  methods: {
    getList: function () {
      let _this = this
      let url = '../../../popedom/getAllItemApply.do'
      if (Number(_this.searchType) === 2) {
        url = '../../../popedom/getAllItemApproval.do'
      }
      let data = {
        'userId': _this.sphdSocket.user.userID,
        'type': _this.applyTime,
        'approvalStatus': _this.chargeResult,
        'beginTime': _this.beginTime,
        'endTime': _this.endTime
      }
      _this.axios.post(url, {
        'json': JSON.stringify(data)
      }).then(function (response) {
        let data = response.data.data
        _this.list = data['approvalProcessList']
        _this.beginTime = data['beginTime']
        _this.endTime = data['endTime']
        console.log('查询列表', _this.list)
        _this.loading = false
      }).catch(function (error) {
        console.log(error)
        _this.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    applyDetail: function (itemID) {
      let _this = this
      this.$router.push({
        path: '/approvalSettingsSearchDetails/' + itemID + '/' + _this.searchType
      })
    }
  }
}
</script>
