<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showBack=true :showClose=true
                  @mycloseFun="handleCustomClose"
      ></PageHeader>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14 " v-if="bmPayChangesDetails.itemIndex>0">
      <p>以下为第{{ bmPayChangesDetails.itemIndex}}次修改后数据，其中本次修改的被标为了<span style="color:#ed5565;">红色</span></p>
      <p>{{ bmPayChangesDetails.createName }}<span class="padding-0-10">{{ new
      Date(bmPayChangesDetails.createDate).format('yyyy-MM-dd  hh:mm:ss') }}</span>修改</p>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14" v-else>
      <p>以下为原始信息</p>
      <p>{{ bmPayChangesDetails.createName }}<span class="padding-0-10">{{ new
      Date(bmPayChangesDetails.createDate).format('yyyy-MM-dd  hh:mm:ss') }}</span>创建</p>
    </div>
    <div class="padding-0-15 ty-fff margin_top_16 re" v-if="info&&bmPayChangesDetails">
      <p>
        <span>收款金额</span>
        <span v-html="info.repaymentAmount"></span>
      </p>
      <p>
        <span>收款日期</span>
        <span v-html="info.repaymentTime"></span>
      </p>
      <p>
        <span>支付方式</span>
        <span v-html="info.repaymentMethod"></span>
      </p>
      <p v-if="repaymentMethod==='1'">
        <span>收款经手人</span>
        <span v-html="info.receiver"></span>
      </p>
      <div v-else-if="repaymentMethod==='5'">
        <p>
          <span>到账日期</span>
          <span v-html="info.arriveDate"></span>
        </p>
        <p>
          <span>收款银行</span>
          <span v-html="info.receiveBank"></span>
        </p>
      </div>
      <div v-else-if="repaymentMethod==='4'||repaymentMethod==='3'">
        <p>
          <span>{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票号</span>
          <span v-html="info.returnNo"></span>
        </p>
        <p>
          <span>票据金额</span>
          <span v-html="info.repaymentAmount"></span>
        </p>
        <p>
          <span>收到{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票的日期</span>
          <span v-html="info.receiveDate"></span>
        </p>
        <p>
          <span>{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票到期日</span>
          <span v-html="info.expireDate"></span>
        </p>
        <p>
          <span>出具{{repaymentMethod == 4 ? '汇' : repaymentMethod == 3 ? '支' : 'err'}}票银行</span>
          <span style="width: 70%;text-align: end" class="hH" v-html="info.bankName"></span>
        </p>
        <p>
          <span>{{repaymentMethod == 4 ? '原始出具汇票' : repaymentMethod == 3 ? '出具支票' : 'err'}}单位</span>
          <span style="width: 60%;text-align: end" class="hH" v-html="info.originalCorp"></span>
        </p>
      </div>

    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'


export default {
  name: "bmPayChangesDetail",
  mixins: [finance],
  data() {
    return {
      tit: "财会",
      bmPayChangesDetails: {},
      info: null,
      repaymentMethod: '',
    }
  },
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    compareWithUnit(before, after, unit = '') {
      const formattedBefore = this.formatCurrency(before) + unit
      const formattedAfter = this.formatCurrency(after) + unit
      return this.compareTxt(formattedBefore, formattedAfter)
    },
    getOrdRecordModHistoryDetail(id) {
      let params = {id: id}
      this.financeApi.ordRecordModHistoryDetail(params).then(res1 => {
        let after = res1.data.data.mapParam
        let before = res1.data.data.mapBefore
        console.log('after', after)
        console.log('before', before)
        let newInfo = {}
        if (Object.keys(before).length > 0) {
          newInfo = {
            repaymentAmount: this.compareWithUnit(before.repaymentAmount, after.repaymentAmount, '元'),
            repaymentTime: this.compareTxt((new Date(before.repaymentTime).format('yyyy-MM-dd')), (new Date(after.repaymentTime).format('yyyy-MM-dd'))),
            repaymentMethod: this.chargePayMethod(after.repaymentMethod),
            receiver: this.compareTxt(before.receiver, after.receiver),
            arriveDate: this.compareTxt((new Date(before.arriveDate).format('yyyy-MM-dd')), (new Date(after.arriveDate).format('yyyy-MM-dd'))),
            receiveBank: this.compareTxt(before.receiveBank, after.receiveBank),
            returnNo: this.compareTxt(before.returnNo, after.returnNo),
            receiveDate: this.compareTxt((new Date(before.receiveDate).format('yyyy-MM-dd')), (new Date(after.receiveDate).format('yyyy-MM-dd'))),
            expireDate: this.compareTxt((new Date(before.expireDate).format('yyyy-MM-dd')), (new Date(after.expireDate).format('yyyy-MM-dd'))),
            originalCorp: this.compareTxt(before.originalCorp, after.originalCorp),
            bankName: this.compareTxt(before.bankName, after.bankName),
          }
        } else {
          newInfo = {
            repaymentAmount: `${this.formatCurrency(after.repaymentAmount)}元`,
            repaymentTime: new Date(after.repaymentTime).format('yyyy-MM-dd'),
            arriveDate: new Date(after.arriveDate).format('yyyy-MM-dd'),
            repaymentMethod: this.chargePayMethod(after.repaymentMethod),
            receiver: after.receiver,
            receiveBank: after.receiveBank,
            returnNo: after.returnNo,
            receiveDate: new Date(after.receiveDate).format('yyyy-MM-dd'),
            expireDate: new Date(after.expireDate).format('yyyy-MM-dd'),
            originalCorp: after.originalCorp,
            bankName: after.bankName,
          }
        }
        this.info = newInfo
        this.repaymentMethod = after.repaymentMethod
        console.log(this.info, 'this.info')

      }).catch(err => {
        console.log('err=', err)
        this.$message.error('getOrdRecordModHistoryDetail链接失败，请重试！')
      })
    }
  },
  mounted() {
    let bmPayChangesDetails = localStorage.getItem('bmPayChangesDetails')
    this.bmPayChangesDetails = JSON.parse(bmPayChangesDetails)
    console.log('bmPayChangesDetails=', this.bmPayChangesDetails)
    this.getOrdRecordModHistoryDetail(this.bmPayChangesDetails.id)
  },
}
</script>


<style scoped lang="scss">
@import "src/style/loans.scss";


.box {
  .re {
    line-height: 30px;

    > p, > div > p {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
      align-items: center;
    }
  }
}
</style>