package cn.sphd.miners.modules.purchaseInvoice.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购_票款处理发票明细表
 * 2021/6/16  lyx
 * 1.169采购的票款处理
 */
@Entity
@Table(name = "t_po_payment_invoice_item")
public class PoPaymentInvoiceItem implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"  , nullable=true, unique=false)
    private Integer org;   //机构ID

    @Column(name="application"  , nullable=true , unique=false)
    private Integer application;   //申请ID

    @Column(name="invoice"  , nullable=true , unique=false)
    private Integer invoice;   //发票ID

    @Column(name="orders"  , nullable=true , unique=false)
    private Integer orders;   //订单ID

    @Column(name="orders_item"  , nullable=true , unique=false,insertable = true)
    private Integer ordersItem;   //订单明细项ID

    @Column(name="customer"  , nullable=true , unique=false)
    private Integer customer;   //顾客ID

    @Column(name="item_no"  , nullable=true , unique=false)
    private Integer itemNo;   //行号

    @Column(name="item_name"  , length=255 , nullable=true , unique=false)
    private String itemName;  //明细项名称(货物或应税劳务、服务名称)

    @Column(name="model"  , length=255 , nullable=true , unique=false)
    private String model;  //规格型号

    @Column(name="item_quantity"   , nullable=true , unique=false)
    private Double itemQuantity;   //数量(注意double是为了凑税额)

    @Column(name="unit"  , length=100 , nullable=true , unique=false)
    private String unit ;   //单位

    @Column(name="unit_price"   , nullable=true , unique=false)
    private Double unitPrice;   //单价(注意double是为了凑税额)

    @Column(name="price"   , nullable=true , unique=false)
    private BigDecimal price;   //价格金额

    @Column(name="tax_rate"  , nullable=true , unique=false)
    private Integer taxRate;   //税率

    @Column(name="tax_amount"   , nullable=true , unique=false)
    private BigDecimal taxAmount;   //税额

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;   //价税合计

    @Column(name="subject"  , length=16 , nullable=true , unique=false)
    private String subject ;   //科目代码

    @Column(name="subject_name"  , length=50 , nullable=true , unique=false)
    private String subjectName ;   //科目名称

    @Column(name="finance_reason"  , length=1 , nullable=true , unique=false)
    private String financeReason ;   //财务修改原因:1-仅系手误,原发票号码不作废,2-发票弄丢,原发票号码不作废,3-原发票号码作废

    @Column(name="finance_memo"  , length=255 , nullable=true , unique=false)
    private String financeMemo ;   //财务修改备注

    @Column(name="finance_time"  , length=255 , nullable=true , unique=false)
    private Date financeTime ;   //财务修改时间

    @Column(name="check_result"  , nullable=true , unique=false)
    private String checkResult ;   //发票检查结果:0-不一致,1-一致

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo ;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;   //操作:1-增,2-删,3-改

    @Column(name = "previous_id")
    private Integer previousId;   //修改前记录ID

    @Column(name = "version_no")
    private Integer versionNo;   //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getApplication() {
        return application;
    }

    public void setApplication(Integer application) {
        this.application = application;
    }

    public Integer getInvoice() {
        return invoice;
    }

    public void setInvoice(Integer invoice) {
        this.invoice = invoice;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getOrdersItem() {
        return ordersItem;
    }

    public void setOrdersItem(Integer ordersItem) {
        this.ordersItem = ordersItem;
    }

    public Integer getCustomer() {
        return customer;
    }

    public void setCustomer(Integer customer) {
        this.customer = customer;
    }

    public Integer getItemNo() {
        return itemNo;
    }

    public void setItemNo(Integer itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Double getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(Double itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(Integer taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getFinanceReason() {
        return financeReason;
    }

    public void setFinanceReason(String financeReason) {
        this.financeReason = financeReason;
    }

    public String getFinanceMemo() {
        return financeMemo;
    }

    public void setFinanceMemo(String financeMemo) {
        this.financeMemo = financeMemo;
    }

    public Date getFinanceTime() {
        return financeTime;
    }

    public void setFinanceTime(Date financeTime) {
        this.financeTime = financeTime;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
