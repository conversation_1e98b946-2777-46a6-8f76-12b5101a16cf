package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.TaccountantSubjectChange;
import cn.sphd.miners.modules.accountant.entity.TaccountantSubjectChangeDto;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TaccountantSubjectChangeMapper extends BaseMapper<TaccountantSubjectChange>{

    List<TaccountantSubjectChange> getChangeSubjects(TaccountantSubjectChange subjectChange);

    void copySubjectToChangeTable(TaccountantSubjectChangeDto param2);

    void updateParent(TaccountantSubjectChange parentChange);

    void updateSubjectInfo(TaccountantSubjectChange change);

    void updateState(TaccountantSubjectChange change);
}