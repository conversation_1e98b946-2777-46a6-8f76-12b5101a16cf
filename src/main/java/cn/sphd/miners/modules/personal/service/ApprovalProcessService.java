package cn.sphd.miners.modules.personal.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.Offer;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2016/12/22.
 * 李旭
 */
public interface ApprovalProcessService extends BadgeNumberCallback {

    void  saveApprovalProcess(ApprovalProcess approvalProcess);

//    List<ApprovalProcess> getApprovalProcessByApprovalStatus(Integer toUserId, String approvalStatus);

    ApprovalProcess getApprovalProcessById(Integer id);

    void updateApprovalProcess(ApprovalProcess approvalProcess);

    List<ApprovalProcess> getApprovalProcessByPersonnelReimburseId(Integer id);

    List<ApprovalProcess> getApprovalProcessByBusiness(Integer business,Integer businessType,String approvalStatus);

    List<ApprovalProcess> getApprovalProcessByDescription(String Description,Integer businessType,String approvalStatus);

    List<ApprovalProcess> getApprovalProcessByBusinessType(Integer toUserId, String approvalStatus, Integer businessType, Date beginTime, Date endTime, PageInfo page,String sort);

    List<ApprovalProcess> getApprovalProcessByFromUser(Integer userId, String approvalStatus,Integer businessType,Date beginTime, Date endTime,String sort);
    List<ApprovalProcess> getApprovalProcessByFromUser(Integer oid, Integer userId, String approvalStatus,Integer businessType,Date beginTime, Date endTime,String sort);

    List<ApprovalProcess> getApprovalProcessByMid(String mid, String approvalStatus,Integer oid);//拥有特殊权限才可审批列表

    List<ApprovalProcess> getApprovalProcessListByToUserId(Integer toUserId, String approvalStatus,PageInfo pageInfo);

    List<ApprovalProcess> getApprovalProcessListByToUserIdCreateDate(Integer toUserId,String approvalStatus,String beginDate,String endDate,Integer feeCat,Integer billCat,String userName);

    List<ApprovalProcess> getApprovalProcessOutTime(Integer toUserId, String approvalStatus, Integer businessType, String beginTime, String endTime, PageInfo page);  //加班查询(已批准与待处理、驳回等显示的顺序和内容不同)

    List<ApprovalProcess> getApprovalProcessByBusinessType(Integer oid, String approvalStatus, Integer businessType);

    List<ApprovalProcess> getApprovalProcessByBusinessDesc(Integer business,Integer businessType,String approvalStatus);

    void updateApprovalProcessListByToUser(Integer oldUserId,Integer newUserId);

    //此方法只能用于申请时新增审批流程  user为申请人信息  businessType-审批过程的类型  和下面的区别是，描述字段是方法内拼接的，逐步换成下面的接口
    Integer addApplyApprovalProcess(Integer business, Integer businessType,User user,String description,ApprovalItem approvalItem);
    //此方法只能用于申请时新增审批流程  user为申请人信息  businessType-审批过程的类型 和上面的区别是，描述字段直接存
    ApprovalProcess addApplyApprovalProcess(Integer business, Integer businessType,String description,Integer level,Integer fromUser,String askName,User user,ApprovalItem approvalItem);

    ApprovalProcess addApprovalProcess(Integer business, Integer businessType,String description,Integer level,User applyUser,User approvalUser,String handleName);

    Integer getApprovalProcessCounts(Integer toUserId,Integer... businessType);

    Integer getApprovalProcessCountsMid(Integer oid,String mid, Integer... businessType);

    List<UserHonePageDto> getUserHonePageDtoListByApprovalId(Integer userId,Integer businessType);//获取类型下，userId审批过的申请人列表

    Integer approvalCompleted(Integer oid,Integer financeAccountId);  //是否有未完成的审批申请

    List<UserHonePageDto> getUserHonePageDtoListByToUserId(Integer toUserId,String userName,Integer businessType,Integer oid);// 查询审批人报销审批过得申请人列表，且按汉字排序

    List<ApprovalProcess> getApprovalProcessByLeaveItem(Integer business,Integer businessType);

    void  choiceInterviewers(Offer offer, Integer handleId,Date handleDate,User loginUser);// 添加应聘者面试官流程

    List<Integer> getUserIdsByBusiness(Integer business,Integer businessType);

    void deleteApprovalProcess(ApprovalProcess approvalProcess);

    ApprovalProcess getApprovalProcessByBusToUser(Integer business,Integer businessType,Integer toUser);
    ApprovalProcess getApprovalProcessByBusToUser(Integer business,Integer businessType,Integer toUser,String approveStatus);

    List<ApprovalProcess> getApprovalProcessByUserId(Integer oid, Integer toUserId, String approvalStatus,String approvalStatusItem, Integer businessType,Date beginTime,Date endTime,  String sort);  //审批人已批准获取数据

    List<ApprovalProcess> getApprovalProcessByFromUserId(Integer oid, Integer toUserId, String approvalStatus,String approvalStatusItem, Integer businessType,Date beginTime,Date endTime, String sort);  //审批人已批准获取数据

    List<Integer> getBusinessByToUserBusinessType(Integer toUser,Integer businessType);

    List<ApprovalProcess> getApprovalProcessAll(Integer toUserId,Integer business,Integer orderby, Integer... businessType);
    List<ApprovalProcess> getApprovalProcesses(Integer toUserId,Integer business,Integer businessGroup,Integer orderby, Integer... businessType);

    List<ApprovalProcess> getApprovalProcessCounts1(Integer org,Integer toUserId,  String mid, Integer... businessType);

    List<ApprovalProcess> getApprovalProcessByGroup(Integer toUser,Integer business, String businessGroup, String approveStatus, Integer... businessType);

    List<ApprovalProcess> getApprovalProcessByOidBusinessType(Integer oid,Integer businessType,List<Integer> business);  // 根据 oid和 类型，查出待处理列表

}
