package cn.sphd.miners.modules.publicInformation.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.publicInformation.dao.PiInfoDao;
import cn.sphd.miners.modules.publicInformation.dao.PiInfoHistoryDao;
import cn.sphd.miners.modules.publicInformation.dto.PiFilePageMes;
import cn.sphd.miners.modules.publicInformation.dto.PiRecord;
import cn.sphd.miners.modules.publicInformation.entity.PiCategory;
import cn.sphd.miners.modules.publicInformation.entity.PiInfo;
import cn.sphd.miners.modules.publicInformation.entity.PiInfoHistory;
import cn.sphd.miners.modules.publicInformation.service.PiAclService;
import cn.sphd.miners.modules.publicInformation.service.PiCatService;
import cn.sphd.miners.modules.publicInformation.service.PiInfoServies;
import cn.sphd.miners.modules.resourceAuthority.service.impl.ResUsing;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("piInfoServies")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class PiInfoServiesImpl implements PiInfoServies {

    @Autowired
    PiInfoDao piInfoDao;
    @Autowired
    PiInfoHistoryDao piInfoHistoryDao;


    @Autowired
    PiCatService piCatService;
    @Autowired
    UploadService uploadService;
    @Autowired
    PiAclService piAclService;




    @Override
    public PiInfo piInfoSingle(Integer id) {
        PiInfo piInfo = piInfoDao.get(id);
        return piInfo;
    }

    @Override
    public PiInfoHistory piInfoHistorySingle(Integer id) {
        PiInfoHistory piInfoHistory = piInfoHistoryDao.get(id);
        return piInfoHistory;
    }

    @Override
    public Long getPiFileNum(Integer org, Integer categoryId) {
        String hql = "select count(id) from PiInfo where org = :org";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        if (categoryId != null) {
            hql = hql + " and category = :category";
            param.put("category", categoryId);
        }
        Long num = (Long) piInfoDao.getByHQLWithNamedParams(hql,param);
        return num;
    }

    @Override
    public Long getCatSize(Integer org, Integer categoryId) {
        String hql = "select sum(size) from PiInfo where org = :org";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        if (categoryId != null) {
            hql = hql + " and category = :category";
            param.put("category", categoryId);
        }
        Long size = (Long) piInfoDao.getByHQLWithNamedParams(hql,param);
        if (size == null) {
            size = Long.valueOf(0);
        }
        return size;
    }

    @Override
    public HashMap<String, Object> getCatFile(User user, Integer categoryId, PageInfo pageInfo) {
        String hql = "from PiInfo where org = :org ";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        if (categoryId != null) {
            hql = hql + " and category = :category";
            param.put("category", categoryId);
        } else {
            hql = hql + " and isTrash = 0";
        }
        hql = hql + " and valid = 1 order by createDate";
        List<PiInfo> listPiInfo = piInfoDao.getListByHQLWithNamedParams(hql, param, pageInfo);
        if (!listPiInfo.isEmpty()) {
            for (PiInfo p : listPiInfo) {
                Long num = piAclService.piAclNumByFile(p.getId());
                p.setAclNum(num);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("pageInfo", pageInfo);
        map.put("list", listPiInfo);
        return map;
    }

    @Override
    public HashMap<String, Object> getUserFile(User user, PageInfo pageInfo) {
        String hql = "select new cn.sphd.miners.modules.publicInformation.dto.PiFilePageMes(p.id, p.name, p.version, p.changeNum, p.createName, p.createDate, p.updateName, p.updateDate, p.type, p.path) from PiInfo p , PiInfoAcl pa where p.id = pa.file and p.org = :org and pa.user = :user and p.isTrash = 0 and p.valid = 1 order by p.createDate";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("user", user.getUserID());
        List<PiFilePageMes> listPiInfo = piInfoDao.getListByHQLWithNamedParams(hql, param, pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("pageInfo", pageInfo);
        map.put("list", listPiInfo);
        return map;
    }

    @Override
    public HashMap<String, Object> addPiFile(User user, Integer categoryId, String name, String fileSn,
                                           String content, String type, String isSystem, String path, String size,
                                           String version, String companyName, String taxIdNumber,
                                           String address, String bankName, String bankNo, String memo,
                                           String module) {
        PiCategory piCategory = piCatService.piCategorySingle(categoryId);
        HashMap<String, Object> map = new HashMap<>();
        Integer state = 1;
        if (piCategory.getValid()) {
            String fileSnStatus = this.checkPiFileSn(user, fileSn);
            if ("1".equals(fileSnStatus)) {
                Date date = new Date();
                PiInfo pi = this.addPiInfo(user,piCategory,name,fileSn,content,type,isSystem,
                        path,size,version,companyName,taxIdNumber,address,bankName,bankNo,memo,date,
                        module);
                this.addPiHisVersion(user,piCategory,pi.getId(),Long.valueOf(0),name,fileSn,content,null,type,
                        path,size,version,companyName,taxIdNumber,address,bankName,bankNo,memo,
                        user.getUserID(),user.getUserName(),date,null,null,
                        null,module);
                this.addPiHisMove(user,pi.getId(),piCategory,null,date);
                this.addPiHisName(user,pi.getId(),piCategory,name,date);
                this.addPiHisSn(user,pi.getId(),piCategory,fileSn,date);
                map.put("newFile", pi);
            } else {
                state = 2;
            }
        }else {
            state = 3;
        }
        map.put("state", state);
        return map;
    }

    @Override
    public HashMap<String, Object> upPiFileVersion(User user, Integer fileId, String reason, String type,
                                                   String path, String size, String version,
                                                   String companyName, String taxIdNumber,
                                                   String address, String bankName, String bankNo,
                                                   String memo, String module) {
        PiInfo piInfo = this.piInfoSingle(fileId);
        Date date = new Date();
        if (path != null && size != null && version != null) {
            PiInfoUsing callback = new PiInfoUsing(piInfo.getId(), piInfo.getClass());
            if (piInfo.getPath() != null) {
                uploadService.delFileUsing(callback, piInfo.getPath(), user);
            }
            piInfo.setPath(path);
            piInfo.setSize(Long.parseLong(size));
            piInfo.setVersion(version);
            uploadService.addFileUsing(callback, piInfo.getPath(), piInfo.getName(), user, module);
        }
        piInfo.setChangeNum(piInfo.getChangeNum()+1);
        PiCategory cat = piCatService.piCategorySingle(piInfo.getCategory());
        this.addPiHisVersion(user,cat,piInfo.getId(),piInfo.getChangeNum(),piInfo.getName(),
                piInfo.getFileSn(),piInfo.getContent(),reason,type,path,size, version,companyName,
                taxIdNumber,address,bankName,bankNo,memo,piInfo.getCreator(),piInfo.getCreateName(),
                piInfo.getCreateDate(),user.getUserID(),user.getUserName(),date,module);
        if ("2".equals(type)) {
            piInfo.setCompanyName(companyName);
            piInfo.setTaxIdNumber(taxIdNumber);
            piInfo.setAddress(address);
            piInfo.setBankName(bankName);
            piInfo.setBankNo(bankNo);
        }else if ("3".equals(type)) {
            piInfo.setCompanyName(companyName);
            piInfo.setTaxIdNumber(taxIdNumber);
        }else if("4".equals(type)){
            piInfo.setCompanyName(companyName);
            piInfo.setBankName(bankName);
            piInfo.setBankNo(bankNo);
            piInfo.setMemo(memo);
        }
        piInfo.setReason(reason);
        piInfo.setUpdator(user.getUserID());
        piInfo.setUpdateName(user.getUserName());
        piInfo.setUpdateDate(date);
        HashMap<String, Object> map = new HashMap<>();
        map.put("piFile",piInfo);
        return map;
    }

    @Override
    public String checkPiFileSn(User user, String fileSn) {
        String hql = "select count(id) from PiInfo where fileSn = :fileSn and org = :org";
        HashMap<String,Object> param = new HashMap<>();
        param.put("fileSn",fileSn);
        param.put("org", user.getOid());
        Long num = (Long) piInfoDao.getByHQLWithNamedParams(hql,param);
        String status = "1";
        if (num > 0) {
            status = "2";
        }
        return status;
    }

    @Override
    public HashMap<String, Object> getListPiHis(Integer fileId, PageInfo pageInfo) {
        PiInfo piInfo = this.piInfoSingle(fileId);
        String hql = "from PiInfoHistory where file = :file and changeNum != :changeNum and operation = 4";
        HashMap<String,Object> param = new HashMap<>();
        param.put("file",fileId);
        param.put("changeNum",piInfo.getChangeNum());
        List<PiInfoHistory> list = piInfoHistoryDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("pageInfo", pageInfo);
        return map;
    }

    @Override
    public HashMap<String, Object> getPiInfoMes(Integer fileId) {
        PiInfo piInfo = this.piInfoSingle(fileId);
        PiCategory cat = piCatService.piCategorySingle(piInfo.getCategory());
        String hql = "select count(case when operation = 5 then 1 end) as m , count(case when operation = 6 then 1 end) as n, count(case when operation = 7 then 1 end) as s from PiInfoHistory where file = :file";
        HashMap<String, Object> param = new HashMap<>();
        param.put("file", fileId);
        Object[] obj = (Object[]) piInfoHistoryDao.getByHQLWithNamedParams(hql,param);
        HashMap<String, Object> map = new HashMap<>();
        map.put("piFile", piInfo);
        map.put("categoryName", cat.getName());
        map.put("moveNum", Long.valueOf(obj[0].toString())-1);
        map.put("upNameNum", Long.valueOf(obj[1].toString())-1);
        map.put("upFileSnNum", Long.valueOf(obj[2].toString())-1);
        return map;
    }

    @Override
    public Integer disablePifileHis(Integer fileHisId) {
        PiInfoHistory his = this.piInfoHistorySingle(fileHisId);
        Integer state = 1;
        if (his.getEnabled()) {
            his.setEnabled(false);
            his.setEnabledTime(new Date());
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public Integer upPiInfoName(User user, Integer fileId, String name) {
        PiInfo piInfo = this.piInfoSingle(fileId);
        Integer state = 1;
        if (piInfo.getValid()) {
            piInfo.setName(name);
            PiCategory cat = piCatService.piCategorySingle(piInfo.getCategory());
            this.addPiHisName(user,fileId,cat,name,new Date());
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public Integer upPiInfoFileSn(User user, Integer fileId, String fileSn) {
        String checkFileSn = this.checkPiFileSn(user, fileSn);
        Integer state = 1;
        if ("1".equals(checkFileSn)) {
            PiInfo pi = this.piInfoSingle(fileId);
            if (pi.getValid()) {
                pi.setFileSn(fileSn);
                PiCategory cat = piCatService.piCategorySingle(pi.getCategory());
                this.addPiHisSn(user,fileId, cat, fileSn, new Date());
            }else {
                state =3;
            }
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public Integer upPiInfoCategory(User user, Integer fileId, Integer categoryId) {
        PiInfo pi = this.piInfoSingle(fileId);
        Integer state = 1;
        if(pi.getTrash()){
            state = 2;  //已经是垃圾文件
        } else if (!pi.getValid()) {
            state = 3;  //已经是删除的文件
        } else {
            PiCategory catOld = piCatService.piCategorySingle(pi.getCategory());
            if (catOld.getTrash() || !catOld.getValid()) {
                state = 5;   //老文件夹已经是垃圾文件夹或者是已经删除的文件夹，实际操作中应该不会有这种情况
            } else {
                PiCategory catNew = piCatService.piCategorySingle(categoryId);
                if (catNew.getValid()) {
                    pi.setCategory(categoryId);
                    if (catNew.getTrash()) {
                        pi.setTrash(true);
                        this.addPiHisMove(user,fileId,catNew,"1",new Date());
                        this.updatePiInfoHisCategory(fileId, categoryId, true);
                    }else {
                        this.addPiHisMove(user,fileId,catNew,null,new Date());
                        this.updatePiInfoHisCategory(fileId, categoryId, false);
                    }
                } else {
                   state = 4;  //要移动到的文件夹已经被删除了
                }
            }
        }
        return state;
    }

    @Override
    public Integer restorePiInfo(User user, Integer fileId) {
        PiInfo pi = this.piInfoSingle(fileId);
        Integer state = 1;
        if (pi.getValid()) {
            PiCategory nowCat = piCatService.piCategorySingle(pi.getCategory());
            if (Byte.valueOf("9").equals(nowCat.getType())) {
                String hqlMove = " from PiInfoHistory where file = :file and operation = :operation order by id desc";
                HashMap<String,Object> paramMove = new HashMap<>();
                paramMove.put("file",fileId);
                paramMove.put("operation",Byte.valueOf("5"));
                List<PiInfoHistory> listMove = piInfoHistoryDao.getListByHQLWithNamedParams(hqlMove,paramMove);
                PiCategory catOld = null;
                for (PiInfoHistory ph : listMove) {
                    if (!nowCat.getId().equals(ph.getCategory())) {
                        catOld = piCatService.piCategorySingle(ph.getCategory());
                        if (!catOld.getValid()) {
                            state = 4;
                        }
                        break;
                    }
                }
                pi.setCategory(catOld.getId());
                pi.setTrash(false);
                this.addPiHisMove(user,fileId,catOld,null,new Date());
                this.updatePiInfoHisCategory(fileId, catOld.getId(), false);
            }else {
                state = 3;  //已经不在垃圾文件夹下
            }
        }else {
            state = 2;  //文件已经被删除
        }
        return state;
    }

    @Override
    public Integer delPiInfo(Integer fileId) {
        PiInfo pi = this.piInfoSingle(fileId);
        Integer state = 1;
        if (pi.getValid()) {
            PiCategory cat = piCatService.piCategorySingle(pi.getCategory());
            if (Byte.valueOf("9").equals(cat.getType())) {
                String hqlMove = "from PiInfoHistory where operation = :operation and file = :file order by id desc";
                PageInfo pageInfo = new PageInfo();
                pageInfo.setPageSize(1);
                HashMap<String,Object> paramMove = new HashMap<>();
                paramMove.put("operation",Byte.valueOf("5"));
                paramMove.put("file",fileId);
                List<PiInfoHistory> list = piInfoHistoryDao.getListByHQLWithNamedParams(hqlMove,paramMove,pageInfo);
                PiInfoHistory piInfoHistory = null;
                for (PiInfoHistory ph : list) {
                    piInfoHistory = ph;
                }
                Date fiveDaysAgo = NewDateUtils.changeDay(new Date(),-4);
                if (piInfoHistory.getCreateDate().getTime() < fiveDaysAgo.getTime()) {
                    pi.setPath("");
                    pi.setValid(false);
                    String hql = "update PiInfoHistory set path = :path, valid = :valid where file = :file and operation = 4";
                    HashMap<String,Object> param = new HashMap<>();
                    param.put("path","");
                    param.put("valid",false);
                    param.put("file",fileId);
                    Integer delState = piInfoHistoryDao.queryHQLWithNamedParams(hql,param);
                } else {
                    state = 4;
                }
            } else {
                state = 3;
            }
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public List<PiRecord> listPiInfoHisByOperation(String operation, Integer fileId) {
        String hql = "from PiInfoHistory where file = :file and operation = :operation";
        HashMap<String,Object> param = new HashMap<>();
        param.put("file",fileId);
        param.put("operation",Byte.valueOf(operation));
        List<PiInfoHistory> list = piInfoHistoryDao.getListByHQLWithNamedParams(hql,param);
        List<PiRecord> listRecord = new ArrayList<>();
        for(int i = 1; i < list.size(); i++){
            PiRecord Record = new PiRecord();
            if(operation.equals("5")){
                Record.setNameBefore(list.get(i-1).getCategoryName());
                Record.setNameAfter(list.get(i).getCategoryName());
                Record.setCreateName(list.get(i).getCreateName());
                Record.setCreateTime(list.get(i).getCreateDate());
            } else if(operation.equals("6")){
                Record.setNameBefore(list.get(i-1).getName());
                Record.setNameAfter(list.get(i).getName());
                Record.setCreateName(list.get(i).getCreateName());
                Record.setCreateTime(list.get(i).getCreateDate());
            }else {
                Record.setNameBefore(list.get(i-1).getFileSn());
                Record.setNameAfter(list.get(i).getFileSn());
                Record.setCreateName(list.get(i).getCreateName());
                Record.setCreateTime(list.get(i).getCreateDate());
            }
            listRecord.add(Record);
        }
        return listRecord;
    }

    @Override
    public HashMap<String, Object> generalSearchPiInfo(User user, String name, PageInfo pageInfo) {
        String hql = "from PiInfo where org = :org and (name like :name or fileSn like :fileSn)";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("name", "%"+name+"%");
        param.put("fileSn", "%"+name+"%");
        List<PiInfo> list = piInfoDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("pageInfo", pageInfo);
        return map;
    }

    //新增文件
    private PiInfo addPiInfo(User user, PiCategory piCategory, String name, String fileSn,
                             String content, String type, String isSystem, String path, String size,
                             String version, String companyName, String taxIdNumber, String address,
                             String bankName, String bankNo, String memo, Date date, String module){
        PiInfo pi = new PiInfo();
        pi.setName(name);
        pi.setFileSn(fileSn);
        if (content != null) {
            pi.setContent(content);
        }
        if (path != null && size != null && version != null) {
            pi.setPath(path);
            pi.setSize(Long.parseLong(size));
            pi.setVersion(version);
        }
        if ("2".equals(type)) {
            pi.setCompanyName(companyName);
            pi.setTaxIdNumber(taxIdNumber);
            pi.setAddress(address);
            pi.setBankName(bankName);
            pi.setBankNo(bankNo);
        }else if ("3".equals(type)) {
            pi.setCompanyName(companyName);
            pi.setTaxIdNumber(taxIdNumber);
        }else if("4".equals(type)){
            pi.setCompanyName(companyName);
            pi.setBankName(bankName);
            pi.setBankNo(bankNo);
            pi.setMemo(memo);
        }
        pi.setType(Byte.valueOf(type));
        pi.setOrg(user.getOid());
        pi.setCategory(piCategory.getId());
        if (isSystem != null) {
            pi.setIsSystem(Byte.valueOf(isSystem));
        } else {
            pi.setIsSystem(Byte.valueOf("2"));
        }

        pi.setEnabled(true);
        pi.setChangeNum(0);
        pi.setTrash(false);
        pi.setDeleted(false);
        pi.setValid(true);
        pi.setCreator(user.getUserID());
        pi.setCreateName(user.getUserName());
        pi.setCreateDate(date);
        piInfoDao.save(pi);
        if (path != null && size != null && version != null) {
            PiInfoUsing callback = new PiInfoUsing(pi.getId(), pi.getClass());
            uploadService.addFileUsing(callback, pi.getPath(), pi.getName(), user, module);
        }
        return pi;
    }

    //新增历史版本
    private void addPiHisVersion(User user, PiCategory piCategory, Integer fileId, Long changeNum,
                                 String name, String fileSn, String content, String reason,
                                 String type, String path, String size, String version,
                                 String companyName, String taxIdNumber, String address,
                                 String bankName, String bankNo, String memo, Integer creator,
                                 String createName, Date createDate, Integer updator, String updateName,
                                 Date updateDate, String module){
        PiInfoHistory his = new PiInfoHistory();
        his.setName(name);
        his.setFileSn(fileSn);
        if (content != null) {
            his.setContent(content);
        }
        if(reason != null){
            his.setReason(reason);
        }
        if (path != null && size != null && version != null) {
            his.setPath(path);
            his.setSize(Long.parseLong(size));
            his.setVersion(version);
        }
        if ("2".equals(type)) {
            his.setCompanyName(companyName);
            his.setTaxIdNumber(taxIdNumber);
            his.setAddress(address);
            his.setBankName(bankName);
            his.setBankNo(bankNo);
        }else if ("3".equals(type)) {
            his.setCompanyName(companyName);
            his.setTaxIdNumber(taxIdNumber);
        }else if("4".equals(type)){
            his.setCompanyName(companyName);
            his.setBankName(bankName);
            his.setBankNo(bankNo);
            his.setMemo(memo);
        }
        his.setType(Byte.valueOf(type));
        his.setFile(fileId);
        his.setCategory(piCategory.getId());
        his.setEnabled(true);
        his.setChangeNum(changeNum);
        his.setTrash(false);
        his.setDeleted(false);
        his.setValid(true);
        his.setOperation(Byte.valueOf("4"));
        his.setOrg(user.getOid());
        his.setCreator(creator);
        his.setCreateName(createName);
        his.setCreateDate(createDate);
        if (updator != null) {
            his.setUpdator(updator);
        }
        if (updateName != null) {
            his.setUpdateName(updateName);
        }
        if (updateDate != null) {
            his.setUpdateDate(updateDate);
        }
        piInfoHistoryDao.save(his);
        if (path != null && size != null && version != null) {
            PiInfoUsing callback = new PiInfoUsing(his.getId(), his.getClass());
            uploadService.addFileUsing(callback, his.getPath(), his.getName(), user, module);
        }
    }

    //新增移动版本
    private void addPiHisMove(User user, Integer fileId, PiCategory piCategory, String trash, Date date){
        PiInfoHistory his = new PiInfoHistory();
        his.setFile(fileId);
        his.setCategory(piCategory.getId());
        his.setCategoryName(piCategory.getName());
        his.setOperation(Byte.valueOf("5"));
        his.setCreator(user.getUserID());
        his.setCreateName(user.getUserName());
        his.setCreateDate(date);
        if (trash != null) {
            his.setTrash(true);
        }
        piInfoHistoryDao.save(his);
    }

    //新增名字版本
    private void addPiHisName(User user, Integer fileId, PiCategory piCategory, String name, Date date){
        PiInfoHistory his = new PiInfoHistory();
        his.setFile(fileId);
        his.setCategory(piCategory.getId());
        his.setName(name);
        his.setOperation(Byte.valueOf("6"));
        his.setCreator(user.getUserID());
        his.setCreateName(user.getUserName());
        his.setCreateDate(date);
        piInfoHistoryDao.save(his);
    }

    //新增编号版本
    private void addPiHisSn(User user, Integer fileId, PiCategory piCategory, String fileSn, Date date){
        PiInfoHistory his = new PiInfoHistory();
        his.setFile(fileId);
        his.setCategory(piCategory.getId());
        his.setFileSn(fileSn);
        his.setOperation(Byte.valueOf("7"));
        his.setCreator(user.getUserID());
        his.setCreateName(user.getUserName());
        his.setCreateDate(date);
        piInfoHistoryDao.save(his);
    }

    //修改所有历史版本的路径
    private void updatePiInfoHisCategory(Integer fileId, Integer categoryId, Boolean isTrash){
        String hql = "update PiInfoHistory set category = :category, isTrash = :isTrash where file = :file and operation = 4";
        HashMap<String,Object> param = new HashMap<>();
        param.put("category",categoryId);
        param.put("isTrash", isTrash);
        param.put("file",fileId);
        Integer del = piInfoHistoryDao.queryHQLWithNamedParams(hql, param);
    }

}
