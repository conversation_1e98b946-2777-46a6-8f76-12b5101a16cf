// import SockJS from '../static/websockes/sockjs.min.js'
const SockJS = window.SockJS
// import Stomp from '../static/websockes/stomp.min.js'
const Stomp = window.Stomp
import auth from '@/sys/auth'
import J<PERSON>NBig from 'json-bigint'
import {nanoid} from 'nanoid'

const sphdSocket = {
  start () {
    this.level = {
      acc: 1,
      user: 2,
      page: 3
    }
    // console.log('sphd Websock constructor')
    // console.log('sphd auth', auth)
    // console.log('sphd auth', auth.webRoot)
    this.url = auth.webRoot + '/socket'
    // console.log('sphdSocket.url', this.url)
    this.socket = new SockJS(this.url)
    // console.log('SockJS',this.socket)
    this.stomp = typeof Stomp.over == "undefined" ? Stomp.Stomp : Stomp
    // console.log('Stomp',this.stomp)
    this.stompClient = this.stomp.over(this.socket)
    // console.log('stompClient',this.stompClient)
    this.isConnect = false
    this.stompClient.debug = null // wyu：不输出调试信息；如果需要输出stopjs调试信息的时候注释掉本行即可。
    this.listeners = []
    this.errorcount = 0
    this.errorlimit = 32
    this.reflushAuth()
    this.connect()
  },
  /**
   * @deprecated：仅用于兼容旧小窗/vue/message代码，更新对象
   */
  reflushAuth() {
    /**
     * @deprecated：仅用于兼容旧小窗/vue/message代码，建议直接auth.getUser()
     * 另外，当前账号手机号建议用 auth.getAcc().mobile 获取，在账号密码登录后有效。
     */
    this.user = auth.getUser()
    /**
     * @deprecated：仅用于兼容旧小窗/vue/message代码，建议直接auth.getOrg()
     */
    this.org = auth.getOrg()
    /**
     * @deprecated：仅用于兼容旧小窗/vue/message代码，建议直接auth.getSessionid()
     */
    this.sessionid = auth.getSessionid()
    console.log('sphdSocketvue', this.user, this.org, this.sessionid)
  },
  connect (force = false) {
    let that = this
    // console.log('before call connect')
    that.stompClient.connect({}, function () {
      console.log('connected: ')
      that.isConnect = true
      that.errorcount = 0
      that.resubscribe(force)
    }, function () {
      that.isConnect = false
      if (that.errorcount++ < that.errorlimit) {
        // console.log('vue connect error,retry connect.')
        that.stompClient.disconnect(function () {
          that.socket.close()
          setTimeout(function () {
            that.reconnect()
            console.log('Vue disconnected, retry connect.', that.errorcount)
          }, 100 * Math.pow(that.errorcount, 2))
        })
      } else {
        console.log('无法连接服务器，本窗口将被关闭！')
      }
    })
    console.log('after called connect')
  },
  reconnect () {
    let that = this
    that.socket = new SockJS(that.url)
    that.stompClient = that.stomp.over(that.socket)
    that.stompClient.debug = null
    that.connect(true)
  },
  resubscribe (force = false) {
    let that = this
    // 订阅服务器发送来的消息
    that.listeners.forEach(function (listener) {
      if (!listener.subscribed || force) {
        listener.subscribed = true
        let path = '/push/'
        switch (listener.type) {
          case 'acc':
            path+=auth.getAccId()+'/'
            break
          case 'user':
            path+=auth.getByName('userID')+'/'
            break
          case 'session':
            path+=auth.getSessionid()+'/'
            break
          case 'logged':
            path+=auth.getLoggedKey()+'/'
            // console.log('subscribe logged path = ', path)
            break
          case 'custom':
            if (typeof listener.param !== 'undefined' && listener.param.toString().length > 0) {
              path += listener.param + '/'
            }
            // default: // boardcast
        }
        path += listener.url
        console.log('subscribe path:' + path)
        that.stompClient.subscribe(path,
            function (event) {
              console.log('callback lister path: ' + path + ' event: ' + event)
              if (typeof listener.callback === 'function') {
                console.log('callback start.')
                listener.callback(event.body)
                console.log('callback finish.')
              }
            },
            function () {
              console.log('resubscribe failure')
              if (typeof listener.errorcallback === 'function') {
                listener.errorcallback()
              }
            })
      }
    })
  },
  subscribe (url, callback, errorcallback, type = 'session', param = '') { // wyu：type=广播：boardcast,用户：userid,会话：session,自定义：custom(param 可以穿oid之类)
    this.subscribeV2 (this.level.user, url, callback, errorcallback, type, param)
  },
  subscribeV2 (level, url, callback, errorcallback, type = 'session', param = '') { // wyu：type=广播：boardcast,用户：userid,会话：session,自定义：custom(param 可以穿oid之类)
    let that = this
    let listener = {
      uuid: that.guid(),
      level: level,
      url: url,
      param: param,
      type: type,
      callback: callback,
      errorcallback: errorcallback,
      subscribed: false
    }
    // console.log('param', listener.param)
    this.listeners.push(listener)
    let interval = setInterval(function () {
      if (that.isConnect) {
        that.resubscribe()
        clearInterval(interval)
      }
    }, 1)
    return listener.uuid
  },
  unsubscribe (uuid) {
    let that = this
    for(let i=this.listeners.length-1; i>=0; i--) {
      let listener = this.listeners.at(i)
      if (listener.uuid === uuid) {
        if (listener.subscribed && listener.subscribed.id) {
          that.stompClient.unsubscribe(listener.subscribed.id)
        }
        that.listeners.splice(index, 1)
      }
    }
  },
  unsubscribeAll (level) {
    let that = this
    this.listeners.forEach(listener => {
      if (listener.level >= level) {
        if (listener.subscribed && listener.subscribed.id) {
          that.stompClient.unsubscribe(listener.subscribed.id)
        }
      }
    })
    this.listeners = this.listeners.filter(listener => {
      return listener.level < level
    })
  },
  guid () {
    return nanoid();
    // return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    //   let v, r
    //   r = Math.random() * 16 | 0
    //   v = c === 'x' ? r : (r & 0x3 | 0x8)
    //   return v.toString(16)
    // })
  },
  // 发送信息
  send (url, message) {
    console.log('sphd.js send new', '/send/' + url, JSONBig.stringify(message))
    let that = this
    let interval = setInterval(function () {
      if (that.isConnect) {
        that.stompClient.send('/send/' + url, {
          token: auth.getToken()
        }, JSONBig.stringify(message))
        // console.log('send:'+JSONBig.stringify(message))
        clearInterval(interval)
      }
    }, 1)
  },
  getCookie (name) { // 获取cookie，仅在同域名iframe中可用
    let arr = document.cookie.match(new RegExp('(^| )' + name + '=([^]*)(|$)'))
    if (arr != null) {
      return decodeURIComponent(arr[2])
    }
    return null
  }
}
export default sphdSocket