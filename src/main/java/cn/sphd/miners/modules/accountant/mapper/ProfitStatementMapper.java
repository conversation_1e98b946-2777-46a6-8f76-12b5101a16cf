package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.modules.accountant.entity.ProfitStatement;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface ProfitStatementMapper extends BaseMapper<ProfitStatement> {

    List<ProfitStatement> listByAllProfitStatement (ProfitStatement ps);

    ProfitStatement getProfitStatem(ProfitStatement ps);

    //根据期数清除利润数据
    int delProfitStatem(ProfitStatement ps);
}