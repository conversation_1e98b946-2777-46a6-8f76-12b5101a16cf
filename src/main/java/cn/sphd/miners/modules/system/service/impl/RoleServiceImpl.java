package cn.sphd.miners.modules.system.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.sales.dao.PdModelSettingsDao;
import cn.sphd.miners.modules.sales.entity.PdModelSettings;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.*;
import cn.sphd.miners.modules.system.dto.PopedomDto;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by 赵应 on 2016-06-20.
 */
@Service("roleService")
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class RoleServiceImpl extends BaseServiceImpl implements RoleService {

    @Autowired
    PopedomDao popedomDao;
    @Autowired
    ApprovalItemDao approvalItemDao;
    @Autowired
    OrgDao orgDao;
    @Autowired
    UserDao userDao;
    @Autowired
    ApprovalFlowDao approvalFlowDao;
    @Autowired
    RoleDao roleDao;
    @Autowired
    RolePopedomDao rolePopedomDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;

    @Autowired
    ApprovalItemHistoryDao approvalItemHistoryDao;
    @Autowired
    ApprovalFlowHistoryDao approvalFlowHistoryDao;
    @Autowired
    PdModelSettingsDao pdModelSettingsDao;

    @Autowired
    OvertimeService overtimeService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    UserService userService;

    @Override
    public void saveRole(Popedom popedom) {
        //1：获取角色ID、获取权限的code（pid_mid）数组
        //角色ID
        Integer roleID = popedom.getRoleID();
        //权限code
        String[] selectopers = popedom.getSelectoper();
        //获取用户ID的数组
        Integer[] selectusers = popedom.getSelectuser();
        //一：保存角色权限关联表（没有使用hibernate操作）
        this.saveRolePopedom(roleID, selectopers);
        //二：保存用户角色关联表（使用hibernate操作）
        this.saveUserRole(roleID, selectusers);
    }

    //获取审批列表
    @Override
    public List<ApprovalItem> getAllApprovalItem(Integer company) {
        Map<String, Object> map = new HashMap<>();
        String itemHql = " from ApprovalItem where belongTo =:oid";
        map.put("oid", company);
        List<ApprovalItem> approvalItems = approvalItemDao.getListByHQLWithNamedParams(itemHql, map);
        return approvalItems;
    }

    private List<ApprovalFlow> getApprovalFlowList(Integer itemId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalFlow where item=:itemId order by id asc";
        map.put("itemId", itemId);
        return approvalFlowDao.getListByHQLWithNamedParams(hql, map);
    }


    /**
     * @Name: findPopedomList
     * @Description: 查询所有的权限信息
     * @Author: 赵应
     * @Version: 1000
     * @Create
     * @parameters: 无
     * @Return: List<Popedom>：权限的PO集合
     */
    public List<Popedom> findPopedomList() {
        Map<String, String> orderby = new LinkedHashMap<String, String>();
        orderby.put("o.mid", "asc");//显示序号按照编号的升序排列
        List<Popedom> list = popedomDao.findCollectionByConditionNoPage("", null, orderby);
        return list;
    }

    /**
     * @Name: findPopedomListByRoleID
     * @Description: 使用角色ID，获取当前角色具有的权限
     * @Author: 赵应
     * @Version: 1000
     * @Create
     * @parameters: 无
     * @Return: List<Popedom>：权限的PO集合
     */
    public List<Popedom> findPopedomListByRoleID(Integer roleID) {
        //1：查询系统中所有的功能权限，返回List<Popedom>，页面要遍历所有的功能权限
        List<Popedom> popedomList = this.findPopedomList();
        //2：获取角色ID，使用角色ID作为条件，查询角色权限关联表，返回当前角色具有的功能权限，返回List<ElecRolePopedom>
        String condition = " and o.roleID=?";
        Object[] params = {roleID};
        //3：遍历List<RolePopedom>，获取权限的code，将权限的code，组织成一个字符串，中间用一个特殊符号@分开，格式aa@ab@ac@ad@ae
        StringBuffer buffer = new StringBuffer("");

        //存放当前角色具有的权限
        String popedom = buffer.toString();
        /**
         * 4：遍历List<Popedom>，获取权限的code，判断字符串是否包含当前的权限code
         如果包含，设置flag=1；
         如果不包含，设置flag=2；
         */
        if (popedomList != null && popedomList.size() > 0) {
            for (Popedom elecPopedom : popedomList) {
                //说明当前角色具有该权限
                if (popedom.contains(elecPopedom.getMid())) {
                    elecPopedom.setFlag("1");
                } else {
                    elecPopedom.setFlag("2");
                }
            }
        }
        return popedomList;
    }


    /**
     * @Name: findUserListByRoleID
     * @Description: 使用角色ID，获取当前角色具有的用户
     * @Author: 赵应
     * @Version: 1000
     * @Create
     * @parameters: 无
     * @Return: List<User>：用户的PO集合
     */
    public List<User> findUserListByRoleID(Integer roleID) {
        //1：查询系统中所有的用户，返回List<ElecUser>，页面要遍历所有的用户
        String condition = " and o.isDuty = ?0";
        Object[] params = {"1"};
        Map<String, String> orderby = new LinkedHashMap<String, String>();
        orderby.put("o.onDutyDate", "asc");
        List<User> userList = userDao.findCollectionByConditionNoPage(condition, params, orderby);
        //2：获取角色ID，使用角色ID作为条件，查询用户角色关联表，返回当前角色具有的用户，返回Set<ElecUser>
        //使用角色ID，查询角色表
//        Set<User> users = role.getUsers();
        //3：遍历Set<ElecUser>，获取用户ID，将用户ID，组织成一个List<String>，格式存放用户ID。sfsdfskdfjslkdfjksdljflksd
        //存放当前角色具有的用户集合
        List<Integer> list = new ArrayList<Integer>();
//        if(users!=null && users.size()>0){
//            for(User user:users){
//                list.add(user.getUserID());
//            }
//        }
        /**
         * 4：遍历List<ElecUser>，获取用户ID，判断List<String>是否包含当前的用户ID
         如果包含，设置flag=1；
         如果不包含，设置flag=2；
         */
        if (userList != null && userList.size() > 0) {
            for (User user : userList) {
                //说明页面用户的复选框被选中
                if (list.contains(user.getUserID())) {
                    user.setFlag("1");
                }
                //说明页面用户的复选框不被选中
                else {
                    user.setFlag("2");
                }
            }
        }
        return userList;
    }

    //获取单个审批项目详情
    @Override
    public ApprovalItem getApprovalItemById(Integer id) {
        return approvalItemDao.get(id);
    }

    @Override
    public ApprovalItem getApprovalItemByPreviousItem(Integer previousItem) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalItem where previousItem=:previousItem";
        map.put("previousItem", previousItem);
        return (ApprovalItem) approvalItemDao.getByHQLWithNamedParams(hql, map);
    }

    //更新审批项目
    @Override
    public void updateApprovalItem(ApprovalItem approvalItem) {
        approvalItemDao.update(approvalItem);
    }


    /**
     * 一：保存角色权限关联表（没有使用hibernate操作）
     */
    private void saveRolePopedom(Integer roleID, String[] selectopers) {
        //2：使用角色ID，查询角色权限关联表，返回List<ElecRolePopedom> list
        String condition = " and o.roleID=?";
        Object[] params = {roleID};

    }

    /**
     * 二：保存用户角色关联表（使用hibernate操作）
     */
    private void saveUserRole(Integer roleID, Integer[] selectusers) {
        //2：解除之前用户和角色的关联关系，重新建立用户和角色的关联关系
        //使用角色ID，获取角色对象
        /**方案一*/
        //获取当前角色具有的用户的Set集合
//        Set<User> elecUsers = role.getUsers();
        //删除之前的值
//        elecUsers.clear();
        //重新建立关联关系，放置到Set集合中，遍历selectusers用户的数组
        if (selectusers != null && selectusers.length > 0) {
            for (Integer userID : selectusers) {
                User user = new User();
                user.setUserID(userID);
//                elecUsers.add(user);
            }
        }
    }

    @Override
    public void saveRole(Role role) {
        roleDao.save(role);
    }

    @Override
    public List<Role> getRoleList() {
        String hql = " from Role";
        List<Role> roleList = roleDao.getListByHQL(hql);
        return roleList;
    }

    @Override
    public ArrayList<PopedomDto> getAllPopedomList() {
        String hql = "select new cn.sphd.miners.modules.system.dto.PopedomDto(mid, pid, name, url) from Popedom where isMenu=:menu and pid!=:pid and url is not null order by orders,mid";
        Map<String, Object> map = new HashMap<>();
        map.put("menu", true);
        map.put("pid", "0");
        ArrayList<PopedomDto> result = (ArrayList) popedomDao.getListByHQLWithNamedParams(hql, map);

        return result;
    }

    @Override
    public Role getRoleById(Integer id) {
        return roleDao.get(id);
    }

    @Override
    public void deleteAllRolePopedom(Set<RolePopedom> rolePopedoms) {
        rolePopedomDao.deleteAll(rolePopedoms);
    }

    @Override
    public void saveRolePopedom(RolePopedom rolePopedom) {
        rolePopedomDao.save(rolePopedom);
    }

    //获取当前的使用的审批流程
    @Override
    public ApprovalItem getCurrentItem(Integer oid, String code) {
        Map<String, Object> map = new HashMap<>();
        ApprovalItem approvalItem = new ApprovalItem();
        if (code.equals("supplementaryOvertime") || code.equals("supplementaryLeave") || code.equals("overtimeRule") || code.equals("leaveRule") || code.equals("submitOvertimeRules")) {
            String itemHql = " from ApprovalItem where belongTo =:oid and code=:code and approveStatus='2'";
            map.put("oid", oid);
            map.put("code", code);
            approvalItem = (ApprovalItem) approvalItemDao.getByHQLWithNamedParams(itemHql, map);  //补报加班、请假的,加班/请假提前时间的
        } else {
            String itemHql = " from ApprovalItem where belongTo =:oid and code=:code and openDate<=:openDate and approveStatus='2' order by id desc";
            map.put("oid", oid);
            map.put("code", code);
            map.put("openDate", new Date());
            approvalItem = (ApprovalItem) approvalItemDao.getByHQLWithNamedParams(itemHql, map);  //加班/付款/请假审批/报销审批/订单评审/材料入库检验/成品入库检验/商品和产品关联/仓库模式修改
        }
        return approvalItem;
    }

    //获取当前使用的审批流程
    private List<ApprovalItem> getCurrentItems(Integer oid, String code, Integer transmitType) {
        Map<String, Object> map = new HashMap<>();
        List<ApprovalItem> approvalItems = new ArrayList<>();
        String itemHql = "select id from ApprovalItem where belongTo=:oid and openDate<=:openDate and approveStatus='2'";
        map.put("oid", oid);
        if (transmitType != null) {
            if (5 == transmitType) {
                itemHql += " and code in ('ordersReview','materialInCheck','productInCheck','commodityProduct','paymentAudit')";
            }
        }
        if (StringUtils.isNotEmpty(code)) {
            itemHql += " and code=:code";
            map.put("code", code);
        }
        map.put("openDate", new Date());
        itemHql += " order by id desc";
        List<Integer> ids = approvalItemDao.getListByHQLWithNamedParams(itemHql, map);
        if (ids.size() > 0) {
            map.clear();
            String item = "from ApprovalItem where id in (:ids) group by code order by orders asc";
            map.put("ids", ids);
            approvalItems = approvalItemDao.getListByHQLWithNamedParams(item, map);  //只有加班
        }
        return approvalItems;
    }

    @Override
    public List<ApprovalItem> getApprovalItemOverTime(Integer oid, String code) {
        Map<String, Object> map = new HashMap<>();
        List<ApprovalItem> approvalItemList = new ArrayList<>();
        ApprovalItem approvalItem = this.getCurrentItem(oid, "overTimeApply");  //当前使用的加班
        ApprovalItem approvalItem2 = this.getCurrentItem(oid, "leaveApply");  //请假审批
        ApprovalItem approvalItem3 = this.getCurrentItem(oid, "reimburseApply");  //当前使用的报销
        ApprovalItem approvalItem1 = this.getCurrentItem(oid, "paymentApproval");  //当前使用的付款
        if (approvalItem != null) {
            approvalItemList.add(approvalItem);
        }
        if (approvalItem2 != null) {
            approvalItemList.add(approvalItem2);
        }
        if (approvalItem3 != null) {
            approvalItemList.add(approvalItem3);
        }
        if (MyStrings.nulltoempty(code).isEmpty()) {  //多数是PC端用的
            if (approvalItem1 != null) {
                approvalItemList.add(approvalItem1);
            }

            String itemHql1 = " from ApprovalItem where belongTo =:oid and code in ('archivesApply','postApply','itemApply','workAttendanceApply') order by orders";
            map.put("oid", oid);
            List<ApprovalItem> approvalItems1 = approvalItemDao.getListByHQLWithNamedParams(itemHql1, map);  //除加班外，其他的审批项
            for (int i = 0; i < approvalItems1.size(); i++) {
                approvalItemList.add(approvalItems1.get(i));
                if (i == 2) {
                    ApprovalItem approvalItem5 = this.getCurrentItem(oid, "materialInCheck");  //当前使用的材料入库检验
                    ApprovalItem approvalItem6 = this.getCurrentItem(oid, "productInCheck");  //当前使用的成品入库检验
                    ApprovalItem approvalItem8 = this.getCurrentItem(oid, "purchaseApprovalSettings");  //当前使用的采购设置审批
                    ApprovalItem approvalItem10 = this.getCurrentItem(oid, "inStockCheckerSet");  //当前使用的仓库模式更改审批
                    ApprovalItem approvalItem12 = this.getCurrentItem(oid, "finishedProductCheck");  // 成品库(1.326销售两种模式)
//                    approvalItemList.add(approvalItem4);
                    approvalItemList.add(approvalItem5);
                    approvalItemList.add(approvalItem6);

                    if(approvalItem8!=null){
                        approvalItemList.add(approvalItem8);
                    }

                    if (approvalItem10!=null){
                        approvalItemList.add(approvalItem10);
                    }

                    //(1.326销售两种模式)  成品库 未启用是省事了  ， 属于简易模式  不加里面四项，     1 是启用了， 启用了流程麻烦，要加这四项设置。
                    if (approvalItem12!=null) {
                        approvalItemList.add(approvalItem12);
                    }
                    if (approvalItem12==null||approvalItem12.getStatus()==1){
                        ApprovalItem approvalItem4 = this.getCurrentItem(oid, "ordersReview");  //当前使用的订单评审  （1.326  改名 销售订单的评审）
                        approvalItemList.add(approvalItem4);

                        ApprovalItem approvalItem7 = this.getCurrentItem(oid, "commodityProduct");  //当前使用的商品和产品关联(产品基本信息的创建模式)
                        if (approvalItem7!=null){
                            PdModelSettings pdModelSettings = this.getPdModelSettings(approvalItem7.getId());
                            String des1 = "与专属商品相关的产品为模式1，";
                            String des2 = "与通用型商品相关的产品为模式1";
                            if (pdModelSettings!=null) {
                                if (2 == pdModelSettings.getDedicatedModel()) {
                                    des1 = "与专属商品相关的产品为模式2，";
                                }
                                if (2 == pdModelSettings.getGeneralModel()) {
                                    des2 = "与通用型商品相关的产品为模式2";
                                }
                                String des = des1 + des2;
                                approvalItem7.setCommodityProductDes(des);  //产品基本信息的创建模式  的状态描述
                            }
                            approvalItemList.add(approvalItem7);
                        }
                        ApprovalItem approvalItem9 = this.getCurrentItem(oid, "stockModeChange");  //当前使用的仓库模式更改审批
                        if(approvalItem9!=null){
                            approvalItemList.add(approvalItem9);
                        }
                        ApprovalItem approvalItem11 = this.getCurrentItem(oid, "productLogisticsCheck");  // 成品出库时物流人员的复核(1.326销售两种模式)
                        if(approvalItem11!=null){
                            approvalItemList.add(approvalItem11);
                        }
                    }

                }
            }

        }
        return approvalItemList;
    }

//    private void sort(List<ApprovalItem> approvalItemList){
//        Collections.sort(approvalItemList, new Comparator<ApprovalItem>() {
//            @Override
//            public int compare(ApprovalItem o1, ApprovalItem o2) {
//                return o1.getOrders()-o2.getOrders();
//            }
//
//        });
//    }

    @Override
    public List<ApprovalFlow> getApprovalFlowByItemId(Integer itemId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalFlow where item_=:itemId";
        map.put("itemId", itemId);
        List<ApprovalFlow> approvalFlows = approvalFlowDao.getListByHQLWithNamedParams(hql, map);
        for (ApprovalFlow approvalFlow : approvalFlows) {
            if (approvalFlow.getToUserId() != null && approvalFlow.getToUserId() != 0) {
                User user = userDao.get(approvalFlow.getToUserId());
                approvalFlow.setMobile(user.getMobile());
                approvalFlow.setRoleCode(user.getRoleCode());
                approvalFlow.setUserName(user.getUserName());
            }
        }
        return approvalFlows;
    }

    //    @Override
    private List<ApprovalItem> getItemList(Integer oid, String code, String approveStatus) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalItem where belongTo=:oid and code=:code";
        map.put("oid", oid);
        map.put("code", code);
        if (!MyStrings.nulltoempty(approveStatus).isEmpty()) {
            hql += " and approveStatus=:approveStatus";
            map.put("approveStatus", approveStatus);
        }
        hql += " order by openDate asc,createDate asc";
        List<ApprovalItem> approvalItems = approvalItemDao.getListByHQLWithNamedParams(hql, map);
        if ("2".equals(approveStatus) && approvalItems.size() == 1) {  //没有进行修改的（第一次的信息）移除，不返回
            approvalItems.remove(approvalItems.get(0));
        }
        return approvalItems;
    }

    /**
     *
     *

     * @return
     */
    /**
     * 修改加班审批设置
     * @param userId            登录人id
     * @param itemId            审批设置id
     * @param openDate          执行日期
     * @param state             状态  0-不需要(自动关联),1-需要审批(检验、评审、手动关联)
     * @param approvalFlows     审批设置的次级
     * @param paymentAuditState 付款复核的状态 0-不需要 1-需要
     * @param generalModel  通用模式1-模式1(默认),2-模式2
     * @param dedicatedModel 专用模式1-模式1(默认),2-模式2
     * @param reviseModel 修改人模式1-由有权限创建商品的职工修改(默认),2-由有产品操作权限的职工修改
     * @return
     */
    @Override
    public Map<String, Object> updateItemApply(User user, Integer itemId, Date openDate, Integer state, String approvalFlows, Integer paymentAuditState,
        Integer generalModel,Integer dedicatedModel,Integer reviseModel) {
        Map<String, Object> map = new HashMap<>();
        if (itemId != null && openDate != null && state != null) {
//            User user = userDao.get(userId);
            ApprovalItem approvalItem = approvalItemDao.get(itemId);  //原来的审批设置
            List<ApprovalItem> approvalItems = getItemList(user.getOid(), approvalItem.getCode(), "1");  //是否有未完成的申请
            if (approvalItems.size() > 0) {
                map.put("state", 2);  //有未审批完的某审批设置的审批流程修改的申请
                map.put("content", "此修改申请正在申请中，不能再次提交！");
            } else {
                approvalItem.setUpdator(user.getUserID());
                approvalItem.setUpdateDate(new Date());
                approvalItem.setUpdateName(user.getUserName());

                ApprovalItem newApprovalItem = new ApprovalItem();
                newApprovalItem.setDescription(approvalItem.getDescription());
                newApprovalItem.setType(approvalItem.getType());
                newApprovalItem.setName(approvalItem.getName());
                newApprovalItem.setBelongTo(user.getOid());
                newApprovalItem.setCode(approvalItem.getCode());
                newApprovalItem.setStatus(state);
                newApprovalItem.setCreator(user.getUserID());
                newApprovalItem.setCreateName(user.getUserName());
                newApprovalItem.setCreateDate(new Date());
                newApprovalItem.setOpenDate(NewDateUtils.today(openDate));
                newApprovalItem.setApproveStatus("1");
                newApprovalItem.setUpdator(user.getUserID());
                newApprovalItem.setUpdateDate(new Date());
                newApprovalItem.setUpdateName(user.getUserName());
                newApprovalItem.setOrders(approvalItem.getOrders());
                newApprovalItem.setUpperLimit(approvalItem.getUpperLimit());
                approvalItemDao.save(newApprovalItem);

                if ("paymentApproval".equals(approvalItem.getCode())) {   //付款审批的修改带着付款复核的[这个是添加付款复核的审批信息]
                    ApprovalItem approvalItem1 = getApprovalItemByPreviousItem(approvalItem.getId());
                    ApprovalItem newApprovalItem1 = new ApprovalItem();
                    newApprovalItem1.setDescription(approvalItem1.getDescription());
                    newApprovalItem1.setType(approvalItem1.getType());
                    newApprovalItem1.setName(approvalItem1.getName());
                    newApprovalItem1.setBelongTo(approvalItem1.getBelongTo());
                    newApprovalItem1.setCode(approvalItem1.getCode());
                    newApprovalItem1.setStatus(paymentAuditState);  // 0-不需要 1-需要
                    newApprovalItem1.setCreator(user.getUserID());
                    newApprovalItem1.setCreateName(user.getUserName());
                    newApprovalItem1.setCreateDate(newApprovalItem.getCreateDate());
                    newApprovalItem1.setOpenDate(newApprovalItem.getOpenDate());
                    newApprovalItem1.setApproveStatus("1");
                    newApprovalItem1.setUpdator(user.getUserID());
                    newApprovalItem1.setUpdateDate(newApprovalItem.getUpdateDate());
                    newApprovalItem1.setUpdateName(user.getUserName());
                    newApprovalItem1.setOrders(approvalItem1.getOrders());
                    newApprovalItem1.setUpperLimit(approvalItem1.getUpperLimit());
                    newApprovalItem1.setPreviousItem(newApprovalItem.getId());
                    newApprovalItem1.setLevel(1);
                    approvalItemDao.save(newApprovalItem1);
                }

                //商品和产品关联(产品基本信息的创建模式)-commodityProduct 除外，暂时没有这里的审批定义
                if (!"commodityProduct".equals(approvalItem.getCode())&&state == 1 && !MyStrings.nulltoempty(approvalFlows).isEmpty()) {
                    JSONArray jsonArray = JSONArray.fromObject(approvalFlows);
                    if (jsonArray.size() != 0) {
                        newApprovalItem.setLevel(jsonArray.size());  //审批次级
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject approvalFlow = jsonArray.getJSONObject(i);
                            ApprovalFlow newApprovalFlow = new ApprovalFlow();
                            Integer toUserId = approvalFlow.getInt("toUserId");
                            if (toUserId == 0) {
                                newApprovalFlow.setToUser("直接上级");
                            } else {
                                newApprovalFlow.setToUser("指定审批人");
                                User user1 = userDao.get(toUserId);
                                newApprovalFlow.setUserName(user1.getUserName());
                            }
                            newApprovalFlow.setToUserId(toUserId);
                            newApprovalFlow.setType(1);
                            newApprovalFlow.setItem(newApprovalItem);
                            String amountCeiling = approvalFlow.getString("amountCeiling"); //上限
                            if (!MyStrings.nulltoempty(amountCeiling).isEmpty()) {
                                newApprovalFlow.setAmountCeiling(approvalFlow.getDouble("amountCeiling"));
                                if ("overTimeApply".equals(approvalItem.getCode()) || "leaveApply".equals(approvalItem.getCode())) {
                                    newApprovalFlow.setLimitQuantity(approvalFlow.getDouble("amountCeiling"));
                                } else if ("reimburseApply".equals(approvalItem.getCode()) || "paymentApproval".equals(approvalItem.getCode())) {
                                    newApprovalFlow.setLimitAmount(new BigDecimal(approvalFlow.getDouble("amountCeiling")));
                                } else {
                                    newApprovalFlow.setLimitQuantity(approvalFlow.getDouble("amountCeiling"));
                                    newApprovalFlow.setLimitAmount(new BigDecimal(approvalFlow.getDouble("amountCeiling")));
                                }

                            }
                            newApprovalFlow.setLevel(approvalFlow.getInt("level"));
                            approvalFlowDao.save(newApprovalFlow);
                            if (i == jsonArray.size() - 1 && StringUtils.isNotEmpty(amountCeiling)) {
                                newApprovalItem.setUpperLimit(new BigDecimal(approvalFlow.getDouble("amountCeiling")));  //上限
                            }
                        }
                    }
                } else {
                    if ("commodityProduct".equals(approvalItem.getCode())){  //产品基本信息的创建模式，有其他的审批
                        newApprovalItem.setLevel(1);  //审批次级

                        //产品_模式设置表
                        PdModelSettings pdModelSettings = new PdModelSettings();
                        pdModelSettings.setOrg(user.getOid());
                        pdModelSettings.setInstance(newApprovalItem.getId());
                        pdModelSettings.setApprovalStatus(ApprovalStatus.applicationSubmission.getIndex());//审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-撤回
                        pdModelSettings.setCorrelationModel(RoleService.CorrelationModel.manualAssociation.getIndex());//关联模式:0-自动关联(默认，不需要审批),1-手工关联(需要审批)
//                        pdModelSettings.setCorrelationModel(null);//关联模式:0-自动关联(默认),1-手工关联
                        pdModelSettings.setGeneralModel(RoleService.GeneralModel.model1.getIndex());//通用模式:1-模式1(默认),2-模式2
                        if (generalModel!=null&&2==generalModel){
                            pdModelSettings.setGeneralModel(RoleService.GeneralModel.model2.getIndex());//通用模式:1-模式1(默认),2-模式2
                        }
                        pdModelSettings.setDedicatedModel(RoleService.DedicatedModel.model1.getIndex());//专用模式:1-模式1(默认),2-模式2
                        if (dedicatedModel!=null&&2==dedicatedModel){
                            pdModelSettings.setDedicatedModel(RoleService.DedicatedModel.model2.getIndex());//专用模式:1-模式1(默认),2-模式2
                        }
                        pdModelSettings.setReviseModel(RoleService.ReviseModel.model1.getIndex());//修改人模式:1-由有权限创建商品的职工修改(默认),2-由有产品操作权限的职工修改
                        if (reviseModel!=null&&2==reviseModel){
                            pdModelSettings.setReviseModel(RoleService.ReviseModel.model2.getIndex());//修改人模式:1-由有权限创建商品的职工修改(默认),2-由有产品操作权限的职工修改
                        }
                        pdModelSettings.setEnabledTime(newApprovalItem.getOpenDate());
                        pdModelSettings.setCreator(user.getUserID());
                        pdModelSettings.setCreateName(user.getUserName());
                        pdModelSettings.setCreateTime(new Date());
                        pdModelSettingsDao.save(pdModelSettings);
                    }else {
                        newApprovalItem.setLevel(0);  //审批次级
                    }
                }

                Map<String, Object> contentMap = getContent(null, approvalItem);  //返回需要拼接的描述
                ApprovalItem approvalItem1 = approvalService.getApprovalItemByOidAndCode(user.getOid(), "itemApply");
                if (1 == approvalItem1.getStatus()) {   //需要审批
                    String handleName = "";//审批人总称
                    String userName = "";//审批人名字
                    Integer uid = null;
                    for (ApprovalFlow f : approvalItem1.getApprovalFlowHashSet()) {
                        if (f.getLevel() == 1) {
                            uid = f.getToUserId();
                            handleName = f.getToUser();
                            userName = f.getUserName();
                        }
                    }
                    ApprovalProcess approvalProcess = new ApprovalProcess();
                    approvalProcess.setLevel(1);
                    approvalProcess.setApproveStatus("1");
                    approvalProcess.setToUser(uid);
                    approvalProcess.setToUserName(handleName);//审批人总称
                    approvalProcess.setUserName(userName);//审批人名称
                    approvalProcess.setCreateDate(newApprovalItem.getCreateDate());
                    approvalProcess.setBusiness(newApprovalItem.getId());
                    approvalProcess.setBusinessType(10);
                    approvalProcess.setOldId(itemId);
                    approvalProcess.setNewId(newApprovalItem.getId());
                    approvalProcess.setFromUser(user.getUserID());
                    approvalProcess.setAskName(user.getUserName());
                    approvalProcess.setDescription((String) contentMap.get("description"));
                    approvalProcess.setMessage(false);
                    approvalProcess.setOrg(user.getOid());
                    approvalProcessDao.save(approvalProcess);

                    //给申请人的待处理发送
                    this.updateRejectSend(0, 1, approvalProcess, approvalProcess.getFromUser(), "/handleApply", null, null, "approvalSettingsApply");

                    //给审批人的待处理发送
                    this.updateRejectSend(1, 1, approvalProcess, approvalProcess.getToUser(), "/handleApproval", "有一条申请待审批", (String) contentMap.get("content"), "approvalSettingsApproval");

                    OverturnPopedom overturnPopedom = new OverturnPopedom(newApprovalItem.getId());
                    clusterMessageSendingOperations.delayCall(newApprovalItem.getOpenDate(), overturnPopedom);
                } else {  //原来的审批流程修改不需要审批
                    newApprovalItem.setApproveStatus("2");
                }
                map.put("state", 1);  //修改成功
                map.put("content", "提交成功");
            }
        } else {
            map.put("state", 0);  //修改失败
            map.put("content", "提交失败");
        }
        return map;
    }

    @Override
    @Transactional
    public String updatePurchaseItemApply(User user, com.alibaba.fastjson.JSONObject after, com.alibaba.fastjson.JSONObject before) {



        com.alibaba.fastjson.JSONObject object = new com.alibaba.fastjson.JSONObject();
        object.put("after", after);
        object.put("before", before);


        ApprovalItem applyItem = approvalService.getApprovalItemByOidAndCode(user.getOid(), "itemApply");
       //获取第一个审批人
        ApprovalFlow applyFlow = approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), 1);

        if (applyFlow == null) {

            return "没有审批人，无法修改";
        }


        ApprovalProcess approvalProcess = new ApprovalProcess();
        approvalProcess.setLevel(1);
        approvalProcess.setApproveStatus("1");
        approvalProcess.setToUser(applyFlow.getToUserId());
        approvalProcess.setToUserName(applyFlow.getUserName());//审批人总称
        approvalProcess.setUserName(user.getUserName());//审批人名称
        approvalProcess.setCreateDate(new Date());
        approvalProcess.setBusiness(after.getInteger("id"));
        approvalProcess.setBusinessType(10);
//            approvalProcess.setOldId(itemId);
        approvalProcess.setNewId(after.getInteger("id"));
        approvalProcess.setFromUser(user.getUserID());
        approvalProcess.setAskName(user.getUserName());
        approvalProcess.setDescription("采购审批流程的修改审批");
        approvalProcess.setMessage(false);
        approvalProcess.setOrg(user.getOid());
        approvalProcess.setApproveData(object.toJSONString());
        approvalProcess.setApproveMemo(approvalProcess.getCreateDate().getTime()+"");
        //approvalProcess.setCreateDateItem(new Date());
        approvalProcessDao.save(approvalProcess);

        //给申请人的待处理发送
        this.updateRejectSend(0, 1, approvalProcess, approvalProcess.getFromUser(), "/handleApply", null, null, "approvalSettingsApply");
        //给审批人的待处理发送
        this.updateRejectSend(1, 1, approvalProcess, approvalProcess.getToUser(), "/handleApproval", "有一条申请待审批", "采购审批流程的修改审批", "approvalSettingsApproval");

        OverturnPopedom overturnPopedom = new OverturnPopedom(after.getInteger("id"));
        String date=after.getString("openDate");

        clusterMessageSendingOperations.delayCall(DateUtil.fomatDate(date), overturnPopedom);
        return "success";


    }


    @Override
    public String purchaseApprovalApproval(ApprovalProcess approvalProcess) {

        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("ap",approvalProcess);
        hashMap.put("status",1);
        User user=userService.getUserByID(approvalProcess.getFromUser());



        //2批准 3驳回
        if ("2".equals(approvalProcess.getApproveStatus())) {

            approvalProcess.setApproveStatus("2");
            approvalProcess.setHandleTime(new Date());
            approvalProcessDao.update(approvalProcess);

            //查下下一级审批人
            ApprovalItem applyItem = approvalService.getApprovalItemByOidAndCode(user.getOid(), "itemApply");
            //获取下一个审批人
            ApprovalFlow applyFlow = approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), approvalProcess.getLevel()+1);
            if(applyFlow!=null){
                ApprovalProcess approvalProcess1 = new ApprovalProcess();
                approvalProcess1.setLevel(approvalProcess.getLevel() + 1);
                approvalProcess1.setApproveStatus("1");
                approvalProcess1.setToUser(applyFlow.getToUserId());
                approvalProcess1.setToUserName(applyFlow.getUserName());//审批人总称
                approvalProcess1.setUserName(approvalProcess.getUserName());//审批人名称
                approvalProcess1.setCreateDate(new Date());
                approvalProcess1.setBusiness(approvalProcess.getBusiness());
                approvalProcess1.setBusinessType(10);

                approvalProcess1.setNewId(approvalProcess.getBusiness());
                approvalProcess1.setFromUser(user.getUserID());
                approvalProcess1.setAskName(user.getUserName());
                approvalProcess1.setDescription("采购审批流程的修改审批");
                approvalProcess1.setMessage(false);
                approvalProcess1.setOrg(user.getOid());
                approvalProcess1.setApproveData(approvalProcess.getApproveData());
                approvalProcess1.setApproveMemo(approvalProcess.getApproveMemo());
                //approvalProcess1.setCreateDateItem(approvalProcess1.getCreateDateItem());
                approvalProcessDao.save(approvalProcess1);

                //给下级审批人的待处理发送
                this.updateRejectSend(1, 1, approvalProcess1, approvalProcess1.getToUser(), "/handleApproval", "有一条申请待审批", "采购审批流程的修改审批", "approvalSettingsApproval");

                //给当前审批人的已批准发送
                this.updateRejectSend(0, 1, approvalProcess, approvalProcess.getToUser(), "/approvalApproval", null, null, "approvalSettingsApproval");


            }else{
                updatePurchaseApproval(approvalProcess);



                //给申请人的待处理发送
                this.updateRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/handleApply", null, null, "approvalSettingsApply");

                List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(), 10, "2");
                for (ApprovalProcess a : approvalProcesses) {  //最终审批结束，将所有审批人的已批准数据减掉
                    //给审批人的已批准发送
                    this.updateRejectSend(0, -1, a, a.getToUser(), "/handleApproval", null, null, "approvalSettingsApproval");
                }

                //给申请人的详情页发送(状态已变更)
                this.updateRejectSend(0, 0, approvalProcess, approvalProcess.getFromUser(), "/itemDetail", null, null, "approvalSettingsApply");


                // 批准给申请人发消息
                userSuspendMsgService.saveUserSuspendMsg(1, "申请被批准","采购审批流程的修改审批", "审批时间 " + user.getUserName() + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "applyItemDetail", approvalProcess.getId());  //给前端要查看详情的链接
            }
            //给审批人的待处理发送
            this.updateRejectSend(-1, -1, approvalProcess, approvalProcess.getToUser(), "/handleApproval", "有一条申请待审批", "采购审批流程的修改审批", "approvalSettingsApproval");

        }else{
            approvalProcess.setApproveStatus("3");
            approvalProcess.setHandleTime(new Date());



            //给申请人的待处理发送
            this.updateRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/handleApply", null, null, "approvalSettingsApply");

            //给审批人的待处理发送
            this.updateRejectSend(-1, -1, approvalProcess, approvalProcess.getToUser(), "/handleApproval", null, null, "approvalSettingsApproval");

            List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(), 10, "2");
            for (ApprovalProcess a : approvalProcesses) {  //最终审批结束，将所有审批人的已批准数据减掉
                //
                if(approvalProcess.getApproveMemo().equals(a.getApproveMemo())){
                    a.setApproveStatus("3");
                    //给审批人的已批准发送
                    this.updateRejectSend(0, -1, a, a.getToUser(), "/handleApproval", null, null, "approvalSettingsApproval");
                }
            }

            //给申请人的详情页发送(状态已变更)
            this.updateRejectSend(0, 0, approvalProcess, approvalProcess.getFromUser(), "/itemDetail", null, null, "approvalSettingsApply");
            // 驳回给申请人发消息
            userSuspendMsgService.saveUserSuspendMsg(1, "申请被驳回", "采购审批流程的修改审批", "审批时间 " + user.getUserName() + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "applyItemDetail", approvalProcess.getId());  //给前端要查看详情的链接
        }
        approvalProcessDao.update(approvalProcess);


        return "success";
    }

    private void updatePurchaseApproval(ApprovalProcess approvalProcess){
        JSONObject jsonObject=JSONObject.fromObject(approvalProcess.getApproveData());
        //获取修改后数据
        JSONObject after=jsonObject.getJSONObject("after");

        Integer status= Integer.valueOf(after.getString("status"));
        Integer level=0;
        if(StringUtils.isNotEmpty(after.getString("level"))){
             level= Integer.valueOf(after.getString("level"));
        }

        Integer id= Integer.valueOf(after.getString("id"));
        Date openDate= DateUtil.fomatDate(after.getString("openDate"));

        //保存历史
        ApprovalItem approvalItem=approvalItemDao.get(id);
//        approvalItem.setStatus(status);
//        approvalItem.setLevel(level);
//        approvalItem.setOpenDate(openDate);
//        approvalItem.setUpdateDate(new Date());
//        approvalItem.setUpdateName(approvalProcess.getAskName());
//        approvalItem.setUpdator(approvalProcess.getFromUser());


        ApprovalItem caigou=new ApprovalItem();
        caigou.setBelongTo(approvalItem.getBelongTo());
        caigou.setLevel(level);
        caigou.setName("采购审批设置");
        caigou.setDescription("采购审批设置审批");
        caigou.setType("Update");
        caigou.setStatus(status);
        caigou.setOrders(100);
        caigou.setCode("purchaseApprovalSettings");
        caigou.setCreateName(approvalItem.getCreateName());
        caigou.setCreateDate(approvalItem.getCreateDate());
        caigou.setUpdateDate(new Date());
        caigou.setUpdateName(approvalProcess.getAskName());
        caigou.setUpdator(approvalProcess.getFromUser());
        caigou.setOpenDate(openDate);
        caigou.setApproveStatus("2");
        caigou.setAuditDate(NewDateUtils.today(new Date()));
        caigou.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(caigou);

        ApprovalItemHistory approvalItemHistory= approvalItemHistoryDao.insert(caigou);

        //删除所有flow
//        List<ApprovalFlow> list=approvalService.approvalFlowList(id);
//        approvalFlowDao.deleteAll(list);

        JSONArray array=after.getJSONArray("userList");

        for (int i = 0; i < array.size(); i++) {
            JSONObject obj=array.getJSONObject(i);

            ApprovalFlow jiabanFlow=new ApprovalFlow();
            jiabanFlow.setToUser(obj.getString("toUser"));
            jiabanFlow.setToUserId(Integer.valueOf(obj.getString("toUserId")));
            jiabanFlow.setType(1);
            jiabanFlow.setItem(caigou);
            jiabanFlow.setAmountCeiling(24.0);
            jiabanFlow.setLimitQuantity(24.0);
            jiabanFlow.setLevel(Integer.valueOf(obj.getString("level")));
            jiabanFlow.setUserName(obj.getString("userName"));
            approvalFlowDao.save(jiabanFlow);

            approvalFlowHistoryDao.insert(jiabanFlow,approvalItemHistory.getId());
        }
    }

    //财务修改 长连接推送   pass 通道  superscript 角标
    public void updateRejectSend(int loginNum, int operate, ApprovalProcess approvalProcess, Integer toUserId, String pass, String title, String content, String code) {
        System.out.println("财务修改推送开始:" + new Date());
        System.out.println("审批流程id：" + approvalProcess.getId() + " userId: " + toUserId);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("approvalProcess", approvalProcess);
        User approvalUser = userDao.get(toUserId); //推送人
        swMessageService.rejectSend(loginNum, operate, hashMap, toUserId.toString(), pass, title, content, approvalUser, code);
        System.out.println("财务修改推送结束:" + new Date());

    }

    /**
     * 审批设置加班/付款修改的审批(暂时只有加班、付款、请假审批、报销审批、订单评审、材料入库检验、成品入库检验、商品和产品关联)
     *
     * @param user              登录人
     * @param approvalProcessId 审批流程id
     * @param approveStatus     1-批准 0-驳回
     * @param reason            驳回理由
     * @return
     */
    @Override
    public Map<String, Object> updateOutTimeApproval(User user, Integer approvalProcessId, Integer approveStatus, String reason) {
        Map<String, Object> map = new HashMap<>();
        if (approvalProcessId != null && approveStatus != null) {
//            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            ApprovalItem approvalItem = approvalItemDao.get(approvalProcess.getBusiness());
            Map<String, Object> contentMap = getContent(approveStatus, approvalItem);  //返回需要拼接的描述/content/contentMessage

            if (user.getUserID().equals(approvalProcess.getToUser())) {   //判断审批人和登陆人是否为同一人
                if ("1".equals(approvalProcess.getApproveStatus())) {
                    List<ApprovalItem> approvalItems = getItemList(user.getOid(), approvalItem.getCode(), "1");  //是否有未完成的申请
                    if (approvalItems.size() <= 1) { //待处理的size为1时，则为本身
                        if (approveStatus == 1) {  //批准
                            approvalProcess.setApproveStatus("2");
                            approvalProcess.setHandleTime(new Date());

                            ApprovalItem approvalItem1 = approvalService.getApprovalItemByOidAndCode(user.getOid(), "itemApply");
                            if (approvalProcess.getLevel() < approvalItem1.getApprovalFlowHashSet().size()) {  //如果是最后审批人则直接结束，如不是，则添加下一级审批人
                                String handleName = "";//审批人总称
                                String userName = "";//审批人名字
                                Integer uid = null;
                                for (ApprovalFlow f : approvalItem1.getApprovalFlowHashSet()) {
                                    if (f.getLevel() == approvalProcess.getLevel() + 1) {
                                        uid = f.getToUserId();
                                        handleName = f.getToUser();
                                        userName = f.getUserName();
                                    }
                                }
                                ApprovalProcess approvalProcess1 = new ApprovalProcess();
                                approvalProcess1.setLevel(approvalProcess.getLevel() + 1);
                                approvalProcess1.setApproveStatus("1");
                                approvalProcess1.setToUser(uid);
                                approvalProcess1.setToUserName(handleName);//审批人总称
                                approvalProcess1.setUserName(userName);//审批人名称
                                approvalProcess1.setCreateDate(new Date());
                                approvalProcess1.setBusiness(approvalItem.getId());
                                approvalProcess1.setBusinessType(10);
                                approvalProcess1.setNewId(approvalProcess.getBusiness());
                                approvalProcess1.setFromUser(approvalProcess.getFromUser());
                                approvalProcess1.setAskName(approvalProcess.getAskName());
                                approvalProcess1.setDescription(approvalProcess.getDescription());
                                approvalProcess1.setMessage(false);
                                approvalProcess1.setOrg(user.getOid());
                                approvalProcessDao.save(approvalProcess1);

                                //给下级审批人的待处理发送
                                this.updateRejectSend(1, 1, approvalProcess1, approvalProcess1.getToUser(), "/handleApproval", "有一条申请待审批", (String) contentMap.get("content"), "approvalSettingsApproval");

                                //给当前审批人的已批准发送
                                this.updateRejectSend(0, 1, approvalProcess, approvalProcess.getToUser(), "/approvalApproval", null, null, "approvalSettingsApproval");

                            } else {  //最终审批结束给，申请人发送消息
                                approvalItem.setApproveStatus("2");
                                approvalItem.setAuditDate(new Date());
                                approvalItem.setAuditorName(user.getUserName());
                                approvalItem.setAuditor(user.getUserID());
                                approvalItemDao.update(approvalItem);

                                if ("paymentApproval".equals(approvalItem.getCode())) {  //付款审批的要修改付款复核的内容
                                    ApprovalItem approvalItem2 = getApprovalItemByPreviousItem(approvalItem.getId());
                                    approvalItem2.setApproveStatus("2");
                                    approvalItem2.setAuditDate(approvalItem.getAuditDate());
                                    approvalItem2.setAuditorName(user.getUserName());
                                    approvalItem2.setAuditor(user.getUserID());
                                    approvalItemDao.update(approvalItem2);
                                }

                                if ("commodityProduct".equals(approvalItem.getCode())){
                                    PdModelSettings pdModelSettings = this.getPdModelSettings(approvalItem.getId());
                                    if (pdModelSettings!=null) {
                                        pdModelSettings.setApprovalStatus(ApprovalStatus.applicationPassed.getIndex()); //审批通过的
                                        pdModelSettings.setUpdateName(user.getUserName());
                                        pdModelSettings.setUpdateTime(new Date());
                                        pdModelSettings.setUpdator(user.getUserID());
                                        pdModelSettingsDao.update(pdModelSettings);
                                    }
                                }

                                if ("finishedProductCheck".equals(approvalItem.getCode())){
                                    ApprovalItem productLogisticsItem = new ApprovalItem();
                                    productLogisticsItem.setBelongTo(approvalItem.getBelongTo());
                                    productLogisticsItem.setDescription("成品出库时物流人员的复核");
                                    productLogisticsItem.setLevel(1);
                                    productLogisticsItem.setName("成品出库时物流人员的复核");
                                    productLogisticsItem.setStatus(0); // 0不需物流复核，  1需
                                    productLogisticsItem.setType("Check");  //检验
                                    productLogisticsItem.setOrders(187);
                                    productLogisticsItem.setCode("productLogisticsCheck");
                                    productLogisticsItem.setCreateName("系统");
                                    productLogisticsItem.setCreateDate(new Date());
                                    productLogisticsItem.setOpenDate(approvalItem.getOpenDate());
                                    productLogisticsItem.setApproveStatus("2");
                                    productLogisticsItem.setAuditDate(new Date());
                                    productLogisticsItem.setUpperLimit(new BigDecimal(-1));
                                    approvalItemDao.save(productLogisticsItem);

                                    ApprovalItem customerOrder = new ApprovalItem();
                                    customerOrder.setBelongTo(approvalItem.getBelongTo());
                                    customerOrder.setDescription("销售订单的评审");
                                    customerOrder.setLevel(1);
                                    customerOrder.setName("销售订单的评审");
                                    customerOrder.setStatus(approvalItem.getStatus());  //1-需要评审 0-不需要
                                    customerOrder.setType("Review");   //评审
                                    customerOrder.setOrders(175);
                                    customerOrder.setCode("ordersReview");
                                    customerOrder.setCreateName("系统");
                                    customerOrder.setCreateDate(new Date());
                                    customerOrder.setOpenDate(approvalItem.getOpenDate());
                                    customerOrder.setApproveStatus("2");
                                    customerOrder.setAuditDate(new Date());
                                    customerOrder.setUpperLimit(new BigDecimal(-1));
                                    approvalItemDao.save(customerOrder);

                                    ApprovalItem commodityProduct = new ApprovalItem();
                                    commodityProduct.setBelongTo(approvalItem.getBelongTo());
                                    commodityProduct.setDescription("产品基本信息的创建模式");
                                    commodityProduct.setLevel(1);
                                    commodityProduct.setName("产品基本信息的创建模式");
                                    commodityProduct.setStatus(approvalItem.getStatus());  //0-自动关联(默认，不需要审批),1-手工关联(需要审批)
                                    commodityProduct.setType("Correlation");  //关联
                                    commodityProduct.setOrders(190);
                                    commodityProduct.setCode("commodityProduct");
                                    commodityProduct.setCreateName("系统");
                                    commodityProduct.setCreateDate(new Date());
                                    commodityProduct.setOpenDate(approvalItem.getOpenDate());
                                    commodityProduct.setApproveStatus("2");
                                    commodityProduct.setAuditDate(new Date());
                                    commodityProduct.setUpperLimit(new BigDecimal(-1));
                                    approvalItemDao.save(commodityProduct);

                                    ApprovalItem stockMode = new ApprovalItem();
                                    stockMode.setBelongTo(approvalItem.getBelongTo());
                                    stockMode.setLevel(1);
                                    stockMode.setName("仓库的模式");
                                    stockMode.setDescription("更改仓库模式");
                                    stockMode.setType("Update");
                                    stockMode.setStatus(approvalItem.getStatus());   //1 智能库    0-非智能
                                    stockMode.setOrders(220);
                                    stockMode.setCode("stockModeChange");
                                    stockMode.setCreateName("系统");
                                    stockMode.setCreateDate(new Date());
                                    stockMode.setOpenDate(approvalItem.getOpenDate());
                                    stockMode.setApproveStatus("2");
                                    stockMode.setAuditDate(new Date());
                                    stockMode.setUpperLimit(new BigDecimal(-1));
                                    approvalItemDao.save(stockMode);
                                }

                                //给申请人的待处理发送
                                this.updateRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/handleApply", null, null, "approvalSettingsApply");

                                List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(), 10, "2");
                                for (ApprovalProcess a : approvalProcesses) {  //最终审批结束，将所有审批人的已批准数据减掉
                                    //给审批人的已批准发送
                                    this.updateRejectSend(0, -1, a, a.getToUser(), "/approvalApproval", null, null, "approvalSettingsApproval");
                                }

                                // 批准给申请人发消息
                                userSuspendMsgService.saveUserSuspendMsg(1, "申请被批准", (String) contentMap.get("contentMessage"), "审批时间 " + user.getUserName() + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "applyItemDetail", approvalProcess.getId());  //给前端要查看详情的链接
                            }

                            //给审批人的待处理发送
                            this.updateRejectSend(-1, -1, approvalProcess, approvalProcess.getToUser(), "/handleApproval", "有一条申请待审批", (String) contentMap.get("content"), "approvalSettingsApproval");
                        } else {   //驳回
                            approvalProcess.setApproveStatus("3");
                            approvalProcess.setHandleTime(new Date());
                            approvalProcess.setReason(reason);
                            approvalItem.setApproveStatus("3");
                            approvalItem.setAuditDate(new Date());
                            approvalItem.setAuditorName(user.getUserName());
                            approvalItem.setAuditor(user.getUserID());

                            if ("paymentApproval".equals(approvalItem.getCode())) {  //付款审批的要修改付款复核的内容
                                ApprovalItem approvalItem2 = getApprovalItemByPreviousItem(approvalItem.getId());
                                approvalItem2.setApproveStatus("3");
                                approvalItem2.setAuditDate(approvalItem.getAuditDate());
                                approvalItem2.setAuditorName(user.getUserName());
                                approvalItem2.setAuditor(user.getUserID());
                                approvalItemDao.update(approvalItem2);
                            }

                            if ("commodityProduct".equals(approvalItem.getCode())){
                                PdModelSettings pdModelSettings = this.getPdModelSettings(approvalItem.getId());
                                if (pdModelSettings!=null) {
                                    pdModelSettings.setApprovalStatus(ApprovalStatus.applicationPassed.getIndex()); //审批通过的
                                    pdModelSettings.setUpdateName(user.getUserName());
                                    pdModelSettings.setUpdateTime(new Date());
                                    pdModelSettings.setUpdator(user.getUserID());
                                    pdModelSettingsDao.update(pdModelSettings);
                                }
                            }

                            //给申请人的待处理发送
                            this.updateRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/handleApply", null, null, "approvalSettingsApply");

                            //给审批人的待处理发送
                            this.updateRejectSend(-1, -1, approvalProcess, approvalProcess.getToUser(), "/handleApproval", null, null, "approvalSettingsApproval");

                            List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(), 10, "2");
                            for (ApprovalProcess a : approvalProcesses) {  //最终审批结束，将所有审批人的已批准数据减掉
                                //给审批人的已批准发送
                                this.updateRejectSend(0, -1, a, a.getToUser(), "/approvalApproval", null, null, "approvalSettingsApproval");
                            }
                            // 驳回给申请人发消息
                            userSuspendMsgService.saveUserSuspendMsg(1, "申请被驳回", (String) contentMap.get("contentMessage"), "审批时间 " + user.getUserName() + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "applyItemDetail", approvalProcess.getId());  //给前端要查看详情的链接
                        }

                        //给申请人的详情页发送(状态已变更)
                        this.updateRejectSend(0, 0, approvalProcess, approvalProcess.getFromUser(), "/itemDetail", null, null, "approvalSettingsApply");
                        map.put("state", 1);
                        map.put("content", "操作成功");
                    } else {
                        map.put("state", 2);  //有未审批完的加班审批流程修改的申请
                        map.put("content", "此修改申请正在申请中，不能再次操作！");
                    }
                } else {
                    map.put("state", 3);  //已在其他地方进行审批了，不可再次操作
                    map.put("content", "此申请已审批，不可重复操作");
                }
            } else {
                map.put("state", 4);  //审批人和申请人不是同一个人
                map.put("content", "不是对应审批人，不可操作");
            }
        } else {
            map.put("state", 0);  //修改失败
            map.put("content", "操作失败");
        }
        return map;
    }

    /**
     * @param approveStatus 1-批准 0-驳回
     * @param approvalItem
     * @return
     */
    private Map<String, Object> getContent(Integer approveStatus, ApprovalItem approvalItem) {
        Map<String, Object> contentMap = new HashMap<>();
        String common = "";  //公共部分
        String description = "";  ////审批中的描述
        String content = "";  // //推送中的content
        String contentMessage = "";  //推送申请人消息的content
        if ("overTimeApply".equals(approvalItem.getCode())) {  //加班
            common = "加班审批流程";
//            description = "加班审批流程的修改申请";  //审批中的描述
//            content = "加班审批流程的修改申请";  //推送中的content
//            if (approveStatus!=null) {
//                if (1 == approveStatus) {
//                    contentMessage = "加班审批流程的修改申请已经被批准了！";
//                } else if (0 == approveStatus) {
//                    contentMessage = "加班审批流程的修改申请被驳回了！";
//                }
//            }
        } else if ("paymentApproval".equals(approvalItem.getCode())) {  //付款审批
            common = "付款审批流程";
//            description = "付款审批流程的修改申请";  //审批中的描述
//            content = "付款审批流程的修改申请";  //推送中的content
//            if (approveStatus!=null) {
//                if (1 == approveStatus) {
//                    contentMessage = "关于付款审批流程的修改申请已被批准！";
//                } else if (0 == approveStatus) {
//                    contentMessage = "关于付款审批流程的修改申请被驳回了！";
//                }
//            }
        } else if ("leaveApply".equals(approvalItem.getCode())) {  //付款审批
            common = "请假审批流程";
//            description = "请假审批流程的修改申请";  //审批中的描述
//            content = "请假审批流程的修改申请";  //推送中的content
//            if (approveStatus!=null) {
//                if (1 == approveStatus) {
//                    contentMessage = " 请假审批流程的修改申请已经被批准了！";
//                } else if (0 == approveStatus) {
//                    contentMessage = "请假审批流程的修改申请被驳回了！";
//                }
//            }
        } else if ("reimburseApply".equals(approvalItem.getCode())) {  //报销审批
            common = "报销审批流程";
//            description = "报销审批流程的修改申请";  //审批中的描述
//            content = "报销审批流程的修改申请";  //推送中的content
//            if (approveStatus!=null) {
//                if (1 == approveStatus) {
//                    contentMessage = "报销审批流程的修改申请已经被批准了！";
//                } else if (0 == approveStatus) {
//                    contentMessage = " 报销审批流程的修改申请被驳回了！";
//                }
//            }
        } else if ("ordersReview".equals(approvalItem.getCode())) {  //来自客户订单的评审
            common = "客户订单评审流程";
        } else if ("materialInCheck".equals(approvalItem.getCode())) {  //材料入库检验
            common = "外购材料入库检验流程";
        } else if ("productInCheck".equals(approvalItem.getCode())) {  //成品入库检验
            common = "成品入库检验流程";
        } else if ("commodityProduct".equals(approvalItem.getCode())) {  //产品基本信息的创建模式
//            common = "商品与产品关联方式";
            common = "通用型商品与产品名称代号关系";
        }
        else if("stockModeChange".equals(approvalItem.getCode())) {
            common="仓库模式";
        }
        else if("inStockCheckerSet".equals(approvalItem.getCode())) {
            common="外购物料入库时，包装完好情况的检查者";
        }
        else if("productLogisticsCheck".equals(approvalItem.getCode())) {
            common="成品出库时物流人员的复核";  //(1.326销售两种模式)
        }
        else if("finishedProductCheck".equals(approvalItem.getCode())) {
            common="成品库";  //(1.326销售两种模式)
        }
        description = common + "的修改申请";  //审批中的描述
        content = common + "的修改申请";  //推送中的content
        if (approveStatus != null) {
            if (1 == approveStatus) {
                contentMessage = common + "的修改申请已经被批准了！";
            } else if (0 == approveStatus) {
                contentMessage = common + "的修改申请被驳回了！";
            }
        }
        contentMap.put("description", description);
        contentMap.put("content", content);
        contentMap.put("contentMessage", contentMessage);
        return contentMap;
    }

    @Override
    public List<ApprovalItem> getApprovalItems(Integer oid, String code) {
        List<ApprovalItem> approvalItems = this.getApprovalItemOverTime(oid, code);  //暂时只有加班
        for (ApprovalItem approvalItem : approvalItems) {
            if (approvalItem.getStatus() == 1) {  //需要审批
                this.getApprovalUser(approvalItem);
            }
        }
        return approvalItems;
    }

    public ApprovalItem getApprovalUser(ApprovalItem approvalItem) {
        for (ApprovalFlow approvalFlow : approvalItem.getApprovalFlowHashSet()) {
            String nameMobile;
            Integer toUserId = approvalFlow.getToUserId();
            if (toUserId == 0) {
                nameMobile = "直接上级";
            } else {
                User user = userDao.get(approvalFlow.getToUserId());
                nameMobile = user.getUserName() + " " + user.getMobile();
            }
            if (1 == approvalFlow.getLevel()) {
                approvalItem.setFirstUserName(nameMobile);
            }
            if (2 == approvalFlow.getLevel()) {
                approvalItem.setSecondUserName(nameMobile);
            }
            if (approvalFlow.getLevel() == approvalItem.getApprovalFlowHashSet().size()) {
                approvalItem.setFinalUserName(nameMobile);
            }
        }
        return approvalItem;
    }

    /**
     * 审批设置修改申请自动驳回
     *
     * @param id 加班审批设置id
     */
    @Override
    public void overturn(Integer id) {
        ApprovalItem approvalItem = approvalItemDao.get(id);
        if ("1".equals(approvalItem.getApproveStatus())) {
            approvalItem.setApproveStatus("3"); //驳回
            approvalItem.setAuditDate(approvalItem.getOpenDate());
            approvalItem.setAuditorName("系统");
            approvalItemDao.update(approvalItem);
            if ("paymentApproval".equals(approvalItem.getCode())) {  //付款审批的要修改付款复核的内容
                ApprovalItem approvalItem2 = getApprovalItemByPreviousItem(approvalItem.getId());
                approvalItem2.setApproveStatus("3");
                approvalItem2.setAuditDate(approvalItem.getAuditDate());
                approvalItem2.setAuditorName("系统");
//                approvalItem2.setAuditor(user.getUserID());
                approvalItemDao.update(approvalItem2);
            }

            Map<String, Object> contentMap = getContent(0, approvalItem);  //返回推送的content
            List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(id, 10, null);
            for (ApprovalProcess approvalProcess : approvalProcesses) {
                if ("1".equals(approvalProcess.getApproveStatus())) {
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setHandleTime(approvalItem.getOpenDate());
                    approvalProcess.setReason("系统自动驳回");
                    approvalProcess.setUserName("系统");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    //给申请人的待处理发送
                    this.updateRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/handleApply", null, null, "approvalSettingsApply");

                    //给审批人的待处理发送
                    this.updateRejectSend(-1, -1, approvalProcess, approvalProcess.getToUser(), "/handleApproval", null, null, "approvalSettingsApproval");

                    // 批准给申请人发消息
                    userSuspendMsgService.saveUserSuspendMsg(1, "申请被驳回", (String) contentMap.get("contentMessage"), "审批时间 " + approvalProcess.getUserName() + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(), "applyItemDetail", approvalProcess.getId());  //给前端要查看详情的链接
                } else if ("2".equals(approvalProcess.getApproveStatus())) {
                    //给审批人的已批准发送
                    this.updateRejectSend(0, -1, approvalProcess, approvalProcess.getToUser(), "/approvalApproval", null, null, "approvalSettingsApproval");
                }
            }
        }
    }

    @Override
    public Map<String, Object> getRecordList(Integer oid, String code) {
        Map<String, Object> map = new HashMap<>();
        List<ApprovalItem> approvalItems = this.getItemList(oid, code, "2");  //第一条数据是原来的
        ApprovalItem approvalItem = this.getCurrentItem(oid, code);  //当前使用的审批设置
        Integer num = 0;
        Integer status = 0;
        List<Map<String, Object>> listMap = new ArrayList<>();  //修改记录的列表
        if (approvalItems.size() > 0) {
            Date openDatePrevious = NewDateUtils.today(approvalItems.get(0).getOpenDate());  //上一个的启用日期
            Date openDateCurrent = NewDateUtils.today(approvalItem.getOpenDate());  //当前使用的加班审批流程的启用日期
            for (int i = 0; i < approvalItems.size(); i++) {
                Map<String, Object> map1 = new HashMap<>();
                String recordState = "原始信息";  //资料状态
                String state = "有效，正在执行";  // 已失效   有效，正在执行    有效，尚未执行
                Date openDate = approvalItems.get(i).getOpenDate();  //开始执行日期

                if (i == 0 && NewDateUtils.today(approvalItems.get(0).getOpenDate()).getTime() < openDateCurrent.getTime()) {  //最初的数据启用日期小于当前使用的启用日期(当等于的时候，状态就是“有效，正在执行”，没有大于)
                    state = "已失效";
                } else if (i != 0) {  //除原始信息
                    recordState = "第" + i + "次修改后";
                    //若openDatePrevious和openDate、openDateCurrent这三个时间相同的话，openDateCurrent为最后一个，所以按照下面第一个判断走
                    if (openDatePrevious.getTime() == openDate.getTime()) {  //上一个启用日期和此启用日期相同
                        listMap.get(listMap.size() - 1).put("state", "已失效");  //将上一个的状态改为“已失效”
                        listMap.get(listMap.size() - 1).put("openDate", null);  //将上一个的执行日期改为null
                    }
                    if (openDate.getTime() < openDateCurrent.getTime()) {  //此启用日期小于当前使用的启用日期(若两者相等，则是“有效，正在执行”的默认)
                        state = "已失效";
                    }
                    if (openDate.getTime() > openDateCurrent.getTime()) {  //此启用日期大于当前使用的启用日期(若两者相等，则是“有效，正在执行”的默认)
                        state = "有效，尚未执行";
                    }
                }

                //此为判断当前生效的加班是第几次修改的
                if (status != 1) {
                    if (approvalItems.get(i).getId().equals(approvalItem.getId())) {
                        if (approvalItems.get(i).getApprovalFlowHashSet().size() > 0) {
                            map.put("approvalNameCurrent", approvalItems.get(i).getApprovalFlowHashSet().get(0).getUserName());  //当期使用付款审批的审批人（付款审批暂时默认为一级审批）
                        } else {
                            map.put("approvalNameCurrent", "");
                        }
                        status = 1;
                    } else {
                        num++;
                    }
                }
                map1.put("numOrder", i);  //主要是为了后面得到排序，前端暂不使用
                map1.put("recordState", recordState);  //资料状态
                map1.put("state", state);  //状态 已失效   有效，正在执行    有效，尚未执行
                map1.put("openDate", openDate);  //正在执行日期
                map1.put("statusResult", approvalItems.get(i).getStatus());  //状态   0-不需要(自动关联),1-需要审批(检验、评审、手动关联)
                map1.put("createDate", approvalItems.get(i).getCreateDate());  //创建或修改时间
                map1.put("createName", approvalItems.get(i).getCreateName());  //创建人或修改人
                map1.put("collectId", approvalItems.get(i).getId());  //审批流程id

                if ("paymentApproval".equals(approvalItem.getCode())) {
                    if (1 == approvalItems.get(i).getStatus() && approvalItems.get(i).getApprovalFlowHashSet() != null) {  //需要付款审批的
                        User user2 = userDao.get(approvalItems.get(i).getApprovalFlowHashSet().get(0).getToUserId());
                        map1.put("approvalName", user2.getUserName() + " " + user2.getMobile());  //付款审批的审批人（付款审批暂时默认为一级审批）
                    } else {
                        map1.put("approvalName", "");  //付款审批的审批人
                    }

                    ApprovalItem approvalItem1 = getApprovalItemByPreviousItem(approvalItems.get(i).getId());  //查付款复核
                    if (approvalItem1 != null) {
                        if (1 == approvalItem1.getStatus()) {    //需要复核[付款复核的内容]
                            User user1 = userService.getUserByRoleCode(oid, "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                            map1.put("paymentAuditName", user1.getUserName() + " " + user1.getMobile());  //付款复核的审批人（付款复核暂时默认为一级审批）
                        } else {
                            map1.put("paymentAuditName", "");  //付款复核的审批人（付款复核暂时默认为一级审批）
                        }
                        map1.put("paymentAuditStatus", approvalItem1.getStatus());  //复核的状态   0-不需要(自动关联),1-需要审批(检验、评审、手动关联)
                    }

                }
                listMap.add(map1);
                openDatePrevious = openDate;  //将当前的启用日期更新为下一个遍历的上一个启用日期
            }
            //paymentApproval-付款审批 订单评审-ordersReview 材料入库检验-materialInCheck 成品入库检验-productInCheck 商品和产品关联-commodityProduct  这些都是倒叙
            if ("paymentApproval".equals(code) || "ordersReview".equals(code) || "materialInCheck".equals(code) || "productInCheck".equals(code) || "commodityProduct".equals(code)) {
                this.sort(listMap);  //按序号倒叙
            }
        }
        map.put("approvalItems", listMap);  //修改记录的列表
        map.put("approvalItem", approvalItem);  //当前生效的
        map.put("num", num); //当前是第几次修改的
        return map;
    }

    private void sort(List<Map<String, Object>> listMap) {
        Collections.sort(listMap, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                Integer num1 = (Integer) o1.get("numOrder");
                Integer num2 = (Integer) o2.get("numOrder");
                return num2 - num1;
            }
        });
    }

    @Override
    public Map<String, Object> updateItem(User user, Integer itemId, Double ruleTime, Boolean enableType) {
        Map<String, Object> map = new HashMap<>();
        ApprovalItem approvalItem = approvalItemDao.get(itemId);
        Integer updateType = 0; //判断ruleTime和enableType是否有改
        if ((ruleTime != null && !new BigDecimal(ruleTime).equals(approvalItem.getUpperLimit()))) {
            approvalItem.setUpperLimit(new BigDecimal(ruleTime));
            updateType = 1;
        }
        if (enableType != null && !enableType.equals(approvalItem.isEnabled())) {
            approvalItem.setEnabled(enableType);
            updateType = 1;
        }

        if (1 == updateType) {
            approvalItem.setUpdateDate(new Date());
            approvalItem.setUpdateName(user.getUserName());
            approvalItem.setUpdator(user.getUserID());
            approvalItemDao.update(approvalItem);
            map.put("status", 1);
            map.put("content", "修改成功");
        } else {
            map.put("status", 2);
            map.put("content", "值未改动");
        }

        return map;
    }

    //暂时是只使用的补报加班-supplementaryOvertime,补报请假-supplementaryLeave
    @Override
    public boolean getItemState(Integer oid, String code) {
        Map<String, Object> map = new HashMap<>();
        String itemHql = " from ApprovalItem where belongTo =:oid and code=:code and approveStatus='2'";
        map.put("oid", oid);
        map.put("code", code);
        ApprovalItem approvalItem = (ApprovalItem) approvalItemDao.getByHQLWithNamedParams(itemHql, map);  //补报加班、请假的
        return approvalItem.isEnabled();
    }

    @Override
    public PdModelSettings getPdModelSettings(Integer instance) {
        Map<String, Object> map = new HashMap<>();
        String hql = " from PdModelSettings where instance=:instance";
        map.put("instance", instance);
        PdModelSettings pdModelSettings = (PdModelSettings) pdModelSettingsDao.getByHQLWithNamedParams(hql, map);
        return pdModelSettings;
    }
}
