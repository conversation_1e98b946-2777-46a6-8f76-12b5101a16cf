package cn.sphd.miners.modules.dmmSchedule.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleSupplementImage;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by Administrator on 2019/1/24.
 */
public interface DmmScheduleService {

    Integer saveDmmSchedule(DmmSchedule dmmSchedule, AuthAcc acc, String source, String[] imagePaths);

    void updateDmmSchedule(DmmSchedule dmmSchedule);

    DmmSchedule getDmmScheduleById(Integer id);

    List<DmmSchedule> getDmmScheduleListByUserId(Long accId, PageInfo pageInfo, String keyWord);

    void addDmmScheduleRemind(Integer id);

    void overdueDmmJobSchedule(Integer id, Date updateDate);

    List<DmmSchedule> getDmmScheduleListByDate(Long accId, Date begin, Date end);

    List<DmmSchedule> keywordQuerySchedule(Long accId, String keyword, String description, String supplement, String place, PageInfo pageInfo);

    void scheduleNoRemind(Integer id);

    void handleSchedule(DmmSchedule dmmSchedule, String type, Date remindDate);

    void scheduleSettleDay(String code);

    Integer getDmmScheduleCounts(Long accId);

    List<DmmSchedule> getDmmListByUserId(Long accId, PageInfo pageInfo);// 获取不需要提醒的日程列表

    void  updateDmmSchedule(DmmSchedule dmmSchedule, AuthAcc acc, String[] imagePaths);//修改日程带附件

    List<DmmSchedule> getDmmScheduleListByKeyword(Long accId, String keyword, PageInfo pageInfo);

    void deleteDmmSchedule(DmmSchedule dmmSchedule);

    void deleteDmmSchedule(Integer id, AuthAcc acc);

    void delDmmScheduleSupplementImagFileUsing(Set<DmmScheduleSupplementImage> dmmScheduleSupplementImageHashSet, AuthAcc acc);

}
