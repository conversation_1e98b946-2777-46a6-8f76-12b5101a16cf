<template>
  <div>
    <PageHeader title="机构选择" :showBack=true>
    </PageHeader>
    <div class="container orgList">
      <ul class="autoY">
        <li class="orgItem" data-name="login" v-for="(item, index2) in orgList" :key="index2">
          <div @click="goInOrg(item)">
            <span class="fa fa-wordpress"></span>
            <span class="message_corner" v-if="item.msgCount">({{ item.msgCount }})</span>
            <span class="mobile">{{ item.name }}</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {organizationList, sureLogin} from "@/api/api";
import sphdSocket from '@/sys/sphd'


export default {
  name: "switchOrg",
  data() {
    return {
      orgList: [],
      orgPage: true,
      orgPageInfo: null
    };
  },
  created() {
    // document.title = "机构列表";
    let height = Math.max(document.documentElement.clientHeight, document.body.clientHeight);
    console.log(height);
    this.heightClent = height;
    sphdSocket.unsubscribeAll(sphdSocket.level.user)
    this.getOrgList()

    // if(auth.isLogInOrg()) {
    //   this.goInOrg({
    //     userID: auth.getUserID(),
    //     oid: auth.getOrg().id
    //   })
    // } else {
    //   console.log('before getOrgList', auth.getAccId())
    //   this.getOrgList()
    // }
  },

  methods: {
    getOrgList() {
      let data = {};
      this.organizationListFun(data);
    },

    async organizationListFun(data) {
      this.orgPage = false;
      let listRes = await organizationList(data);
      console.log("listRes =", listRes);
      let listData = listRes.data.organizationList || [];
      console.log("organizationList", listData);
      let list = [];
      let defaultOrg = null;
      listData.forEach((org) => {
        if (org.code === "liveHelper") {
        } else {
          if (org.isDefault === "1") {
            defaultOrg = org;
          }
          list.push(org);
        }
      });
      console.log(list)
      this.orgList = list;

      // if (list.length === 0) {
      //   console.log("没有机构，跳转私人领地");
      //   this.$router.push("mine");
      // } else if (list.length === 1) {
      //   console.log("只有一个机构，直接登陆进入机构");
      //   this.goInOrg(list[0]);
      // } else {
      //   if (defaultOrg) {
      //     console.log("defaultOrg", defaultOrg);
      //     this.goInOrg(defaultOrg);
      //   } else {
      //     this.orgList = list;
      //   }
      // }
    },
    async goInOrg(item) {
      let params = {
        userID: item.userID,
        oid: item.id,
      }
      let slRes = await sureLogin(params)
      //更新sphdSocket中的对象，用于兼容message小窗的sphdSocket.user .org代码
      sphdSocket.reflushAuth()
      let slData = slRes.data
      // console.log('sureLogin', slData)
      if (slData.error != undefined) {
        this.$alert(slData.error, '提示', {
          confirmButtonText: '确定',
        })
      } else {
        localStorage.setItem('orgInfo', JSON.stringify(item))
        this.$router.push('homeMy')
      }
    },

  }
};
</script>

<style lang="scss" scoped>
.orgList {
  height: calc(100vh - 44px);
  overflow-y: auto;

  .pageTtl {
    position: relative;
    color: #fff;
    font-size: 16px;
    height: 50px;
    margin-bottom: 16px;
    line-height: 50px;
    background-color: $tyColorGreen;
    text-align: center;

    .left {
      position: absolute;
      left: 10px;
      font-size: 20px;
      top: 15px;
    }
  }

  .orgCc {
    overflow-y: auto;
    height: 500px;
  }

  .autoY {
    height: 1000px;

    .orgItem {
      line-height: 40px;
      border-bottom: 1px solid #eee;
      padding: 0 20px;

      .fa-wordpress {
        color: $tyColorGreen;
        margin-right: 10px;
      }
    }
  }

}
</style>
