package cn.sphd.miners.modules.inStockCheck.service.impl;

import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory;
import cn.sphd.miners.modules.inStockCheck.mapper.TMtStockFlowSettingHistoryMapper;
import cn.sphd.miners.modules.inStockCheck.mapper.TMtStockFlowSettingMapper;
import cn.sphd.miners.modules.inStockCheck.service.TMtStockFlowSettingService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting;

import java.util.Date;
import java.util.List;

@Service
public class TMtStockFlowSettingServiceImpl implements TMtStockFlowSettingService {

  @Autowired private TMtStockFlowSettingMapper tMtStockFlowSettingMapper;

  @Autowired private TMtStockFlowSettingHistoryMapper tMtStockFlowSettingHistoryMapper;

  @Override
  public int deleteByPrimaryKey(Integer id) {
    return tMtStockFlowSettingMapper.deleteByPrimaryKey(id);
  }

  @Override
  public int insert(TMtStockFlowSetting record) {
    return tMtStockFlowSettingMapper.insert(record);
  }

  @Override
  public int insertSelective(TMtStockFlowSetting record) {
    return tMtStockFlowSettingMapper.insertSelective(record);
  }

  @Override
  public TMtStockFlowSetting selectByPrimaryKey(Integer id) {
    return tMtStockFlowSettingMapper.selectByPrimaryKey(id);
  }

  @Override
  public int updateByPrimaryKeySelective(TMtStockFlowSetting record, User user) {
    // 获取当前设置 如果为空 则初始化出一条
    TMtStockFlowSetting tMtStockFlowSetting =
        tMtStockFlowSettingMapper.selectByValue(Integer.valueOf(record.getValue()));
    if (tMtStockFlowSetting == null) {
      record.setId(null);
      record.setOrg(user.getOid());
      record.setVersionNo(1);
      record.setCreator(record.getUpdator());
      record.setCreateDate(record.getUpdateDate());
      record.setCreateName(record.getUpdateName());
      record.setEffectState((byte) 1);
      record.setEnable(true);
      record.setEffectDate(new Date());
      record.setKey((byte) 0);
      record.setValue(record.getValue());
      tMtStockFlowSettingMapper.insertSelective(record);
      // 初始化时保存为初始记录
      TMtStockFlowSettingHistory history = new TMtStockFlowSettingHistory();

      BeanUtils.copyProperties(record, history);

      history.setId(null);
      history.setStockFlowSetting(record.getId());
      history.setCreateDate(new Date());
      history.setCreateName("系统默认");
      history.setVersionNo(0);
      history.setPreviousId(0);

      tMtStockFlowSettingHistoryMapper.insert(history);

    }
    tMtStockFlowSettingMapper.selectByValue(Integer.valueOf(record.getValue()));

    tMtStockFlowSetting.setKey(record.getKey());
    tMtStockFlowSetting.setEffectDate(record.getEffectDate());
    tMtStockFlowSettingMapper.updateByPrimaryKeySelective(tMtStockFlowSetting);

    List<TMtStockFlowSettingHistory> histories =
        tMtStockFlowSettingHistoryMapper.selectBySettingId(tMtStockFlowSetting.getId());
    // 更新后保存为历史
    TMtStockFlowSettingHistory history = new TMtStockFlowSettingHistory();
    BeanUtils.copyProperties(tMtStockFlowSetting, history);
    history.setId(null);
    history.setStockFlowSetting(record.getId());
    history.setCreateDate(new Date());
    history.setCreateName(user.getUserName());
    history.setCreator(user.getUserID());
    history.setVersionNo(histories.size() + 1);
    history.setPreviousId(histories.get(histories.size() - 1).getId());
    tMtStockFlowSettingHistoryMapper.insert(history);

    return 1;
  }

  @Override
  public int updateByPrimaryKey(TMtStockFlowSetting record) {
    return tMtStockFlowSettingMapper.updateByPrimaryKey(record);
  }
}
