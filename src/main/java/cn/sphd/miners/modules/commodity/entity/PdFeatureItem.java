package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 产品_特性项目
20240508 1.296产品特性新增对象 t_pd_feature_item
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Entity(name="PdFeatureItem")
@Table(name="t_pd_feature_item")
public class PdFeatureItem
{
    private static final long serialVersionUID = 1L;

    /** ID */

    private Long id;

    /** 机构ID */
    private Integer org;

    /** 产品ID */
    private Integer product;

    /** 特性ID */
    private Long feature;

    /** 类别:1-尺寸;2-类似尺寸,有基准值,并有上限的计量型特性;3-有基准值与上限,但无下限的计量型特性;4-有基准值与下限,但无上限的计量型特性;5-没有基准值,仅有上限与下限的计量型特性;6-没有基准值与上限,仅有下限的计量型特性;7-
             没有基准值与下限,仅有上限的计量型特性;8-需在“合格”与“不合格”中选择的计数型特性;9-需在自定义的两个或多个选项中选择的计数型特性;10-
             需录入为纯文本的数据的计数型特性;11-需录入百分比（百分号系统自带）的计数型特性;12-需在日历中选择某日期的计数型特性;13-需在日历中选择某月份的计数型特性;14-需在日历中选择某年份的计数型特性 */
    private Integer category;

    /** 特性名称 */
    private String name;

    /** 尺寸单位:1-mm,2-cm,3-dm,5-m */
    private Integer sizeUnit;

    /** 单位ID */
    private Integer unitId;

    private String unit;
    /** 项数 */
    private BigDecimal baseSize;

    /** 上偏差 */
    private BigDecimal ecartSuperieur;

    /** 下偏差 */
    private BigDecimal ecartInferieur;

    /** 等级 */
    private Integer rank;

    /** 等级名称 */
    @Transient
    private String rankName;

    /** 等级符号 */
    @Transient
    private String rankSymbol;

    /** 上限合格否 */
    private Integer upperQualified;

    /** 下限合格否 */
    private Integer lowerQualified;

    /** 检测设备/仪器 种数 */
    private Integer deviceCount;

    /** 量具/检具或检验工具 种数 */
    private Integer instrumentCount;

    /** 设备/工具以外的方法 种数 */
    private Integer controlCount;

    /** 控制的内容 */
    private String controlContent;

    /** 文本的字数上限 */
    private Integer textLimit;

    /** 小数的位数上限 */
    private Integer decimalsLimit;

    /** 排序 */
    private Integer orders;

    /** 启用标志:true-启用,false-停用 */
    private Integer enabled;

    /** 备注 */
    private String memo;

    /** 创建人id */
    private Integer creator;

    /** 创建人 */
    private String createName;

    /** 创建时间 */
    private Date createDate;

    /** 修改人id */
    private Integer updator;

    /** 修改人 */
    private String updateName;

    /** 修改时间 */
    private Date updateDate;

    /** 操作:1-增,2-删,3-改,4-暂停,5-恢复 */
    private Integer operation;

    /** 修改前记录ID */
    private Integer previousId;

    /** 版本号,每次修改+1 */
    private Integer versionNo;

    //特性选项列表

    private String featureOptionJson;

    private List<PdFeatureOption> featureOptionList;
    //特性检查列表

    private String itemCheckJson1;
    //检测设备/仪器 列表

    private List<PdItemCheck> itemCheckList1;

    private String itemCheckJson2;
    //量具/检具或检验工具 列表

    private List<PdItemCheck> itemCheckList2;

    private String itemCheckJson3;
    //设备/工具以外的方法

    private List<PdItemCheck> itemCheckList3;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "product")
    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    @Basic
    @Column(name = "feature")
    public Long getFeature() {
        return feature;
    }

    public void setFeature(Long feature) {
        this.feature = feature;
    }

    @Basic
    @Column(name = "category")
    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "size_unit")
    public Integer getSizeUnit() {
        return sizeUnit;
    }

    public void setSizeUnit(Integer sizeUnit) {
        this.sizeUnit = sizeUnit;
    }

    @Basic
    @Column(name = "unit_id")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Basic
    @Column(name = "base_size")
    public BigDecimal getBaseSize() {
        return baseSize;
    }

    public void setBaseSize(BigDecimal baseSize) {
        this.baseSize = baseSize;
    }

    @Basic
    @Column(name = "ecart_superieur")
    public BigDecimal getEcartSuperieur() {
        return ecartSuperieur;
    }

    public void setEcartSuperieur(BigDecimal ecartSuperieur) {
        this.ecartSuperieur = ecartSuperieur;
    }

    @Basic
    @Column(name = "ecart_inferieur")
    public BigDecimal getEcartInferieur() {
        return ecartInferieur;
    }

    public void setEcartInferieur(BigDecimal ecartInferieur) {
        this.ecartInferieur = ecartInferieur;
    }

    @Basic
    @Column(name = "rank")
    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    @Basic
    @Column(name = "upper_qualified")
    public Integer getUpperQualified() {
        return upperQualified;
    }

    public void setUpperQualified(Integer upperQualified) {
        this.upperQualified = upperQualified;
    }

    @Basic
    @Column(name = "lower_qualified")
    public Integer getLowerQualified() {
        return lowerQualified;
    }

    public void setLowerQualified(Integer lowerQualified) {
        this.lowerQualified = lowerQualified;
    }

    @Basic
    @Column(name = "device_count")
    public Integer getDeviceCount() {
        return deviceCount;
    }

    public void setDeviceCount(Integer deviceCount) {
        this.deviceCount = deviceCount;
    }

    @Basic
    @Column(name = "instrument_count")
    public Integer getInstrumentCount() {
        return instrumentCount;
    }

    public void setInstrumentCount(Integer instrumentCount) {
        this.instrumentCount = instrumentCount;
    }

    @Basic
    @Column(name = "control_count")
    public Integer getControlCount() {
        return controlCount;
    }

    public void setControlCount(Integer controlCount) {
        this.controlCount = controlCount;
    }

    @Basic
    @Column(name = "control_content")
    public String getControlContent() {
        return controlContent;
    }

    public void setControlContent(String controlContent) {
        this.controlContent = controlContent;
    }

    @Basic
    @Column(name = "text_limit")
    public Integer getTextLimit() {
        return textLimit;
    }

    public void setTextLimit(Integer textLimit) {
        this.textLimit = textLimit;
    }

    @Basic
    @Column(name = "decimals_limit")
    public Integer getDecimalsLimit() {
        return decimalsLimit;
    }

    public void setDecimalsLimit(Integer decimalsLimit) {
        this.decimalsLimit = decimalsLimit;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "enabled")
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
    @Transient
    public String getFeatureOptionJson() {
        return featureOptionJson;
    }

    public void setFeatureOptionJson(String featureOptionJson) {
        this.featureOptionJson = featureOptionJson;
    }
    @Transient
    public List<PdFeatureOption> getFeatureOptionList() {
        return featureOptionList;
    }

    public void setFeatureOptionList(List<PdFeatureOption> featureOptionList) {
        this.featureOptionList = featureOptionList;
    }
    @Transient
    public String getItemCheckJson1() {
        return itemCheckJson1;
    }

    public void setItemCheckJson1(String itemCheckJson1) {
        this.itemCheckJson1 = itemCheckJson1;
    }
    @Transient
    public List<PdItemCheck> getItemCheckList1() {
        return itemCheckList1;
    }

    public void setItemCheckList1(List<PdItemCheck> itemCheckList1) {
        this.itemCheckList1 = itemCheckList1;
    }
    @Transient
    public String getItemCheckJson2() {
        return itemCheckJson2;
    }

    public void setItemCheckJson2(String itemCheckJson2) {
        this.itemCheckJson2 = itemCheckJson2;
    }
    @Transient
    public List<PdItemCheck> getItemCheckList2() {
        return itemCheckList2;
    }

    public void setItemCheckList2(List<PdItemCheck> itemCheckList2) {
        this.itemCheckList2 = itemCheckList2;
    }
    @Transient
    public String getItemCheckJson3() {
        return itemCheckJson3;
    }

    public void setItemCheckJson3(String itemCheckJson3) {
        this.itemCheckJson3 = itemCheckJson3;
    }
    @Transient
    public List<PdItemCheck> getItemCheckList3() {
        return itemCheckList3;
    }

    public void setItemCheckList3(List<PdItemCheck> itemCheckList3) {
        this.itemCheckList3 = itemCheckList3;
    }
    @Transient
    public String getRankName() {
        return rankName;
    }

    public void setRankName(String rankName) {
        this.rankName = rankName;
    }
    @Transient
    public String getRankSymbol() {
        return rankSymbol;
    }

    public void setRankSymbol(String rankSymbol) {
        this.rankSymbol = rankSymbol;
    }

    @Transient
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
