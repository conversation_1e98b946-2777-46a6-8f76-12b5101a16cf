package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfo;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfoHistory;
import cn.sphd.miners.modules.commodity.entity.PdPackagingStructure;
import cn.sphd.miners.modules.commodity.service.PdPackagingService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.system.entity.User;


import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

@Controller
@RequestMapping("/packing")
public class PackagingController {

    @Autowired
    private PdPackagingService pdPackagingService;

    @Autowired
    private MtService mtService;

    @Autowired
    private ProductService productService;


    @RequestMapping("/packIndex.do")
    public String packIndex() {
        return "/technology/packManage";
    }


    /**
     * 获取已设置列表
     */
    @RequestMapping("/getPackagingList.do")
    @ResponseBody
    public JsonResult getPackagingList(User user, String param, PageInfo pageInfo) {
        List<Map<String, Object>> list = pdPackagingService.getPackagingList(user.getOid(), param, 1, pageInfo);
        for (Map<String, Object> m:list
             ) {
            Long num= (Long) m.get("num");
            if(num==0){
                m.put("gs","无需包装");
            }else{
                m.put("gs",num+"种包装方式");
            }
        }
        return new JsonResult(0, list, pageInfo);
    }

    /**
     * 获取未设置数量
     */
    @RequestMapping("/getWszNum.do")
    @ResponseBody
    public JsonResult getWszNum(User user) {

        Object num = pdPackagingService.getWszNum(user.getOid());
        return new JsonResult(0, num);
    }


    /**
     * 未设置列表
     */
    @RequestMapping("/getWszPackagingList.do")
    @ResponseBody
    public JsonResult getWszPackagingList(User user, String param, PageInfo pageInfo) {

        List<Map<String, Object>> list = pdPackagingService.getPackagingList(user.getOid(), param, 0, pageInfo);
        return new JsonResult(0, list, pageInfo);
    }

    /**
     * 新增包装物
     */
    @RequestMapping("/addBzw.do")
    @ResponseBody
    public JsonResult addBzw(User user, MtBase mtBase) {
        Integer typeId = pdPackagingService.getBzTypeId(user.getOid());

        mtBase.setCategory_(typeId);
        mtBase.setIsPurchased("0");
        mtBase.setEnabled("1");
        mtBase.setOrigin("5");
        Map map = mtService.operation(mtBase, "1", user.getUserID());

        int code = (int) map.get("code");
        if (code != 1) {
            return new JsonResult(0, "", "400", (String) map.get("message"));
        }
        return new JsonResult(0, null);
    }

    /**
     * 获取包装物列表
     */
    @RequestMapping("/getBzwList.do")
    @ResponseBody
    public JsonResult getBzwList(User user) {
        Integer typeId = pdPackagingService.getBzTypeId(user.getOid());
        List<Map<String, Object>> list = pdPackagingService.getMtBaseByType(user.getOid(), typeId);
        return new JsonResult(0, list);
    }


    /**
     * 新增包装
     */
    @RequestMapping("/addPackaging.do")
    @ResponseBody
    public JsonResult addPackaging(User user, PdPackagingInfo info) {

        info.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));

        String res = pdPackagingService.addPackaging(info, user);
        if ("success".equals(res)) {
            return new JsonResult(1, info.getProductId());
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /**
     * 包装查看
     */
    @RequestMapping("/getPackagingDetail.do")
    @ResponseBody
    public JsonResult getPackagingDetail(User user, Integer productId) {
        Map<String, Object> map = new HashMap<>();

        //先查询是否无需包装
        List<PdPackagingInfo> wx = pdPackagingService.getWxPackagingList(productId);

        if(wx.size()>0){
            map.put("isPacking","0");
            map.put("list", new ArrayList<>());


            //暂停的包装信息
            List<PdPackagingInfoHistory> suspendList = new ArrayList<>();

            List<PdPackagingInfoHistory> allList = pdPackagingService.getPackagingList(productId);

            for (PdPackagingInfoHistory info : allList) {
                if(info.getLayersCount().intValue()!=0){
                    if (info.getEnabled() == 0 ) {

                        suspendList.add(info);
                    }
                }
            }
            map.put("suspendList", suspendList);
            return new JsonResult(1, map);
        }
        //正常的包装信息
        List<PdPackagingInfoHistory> list = new ArrayList<>();
        //暂停的包装信息
        List<PdPackagingInfoHistory> suspendList = new ArrayList<>();

        List<PdPackagingInfoHistory> allList = pdPackagingService.getPackagingList(productId);

        for (PdPackagingInfoHistory info : allList) {
            if(info.getLayersCount().intValue()!=0){
                if (info.getEnabled() == 1) {

                    list.add(info);
                } else {
                    suspendList.add(info);
                }
            }
        }
        if(allList.size()==1){
            if(allList.get(0).getLayersCount().intValue()==0){
                map.put("isPacking","0");
            }else{
                map.put("isPacking","1");
            }
        }else{
            map.put("isPacking","1");
        }


        map.put("list", list);
        map.put("suspendList", suspendList);
        return new JsonResult(1, map);
    }


    /**
     * 包装修改
     */
    @RequestMapping("/updatePackaging.do")
    @ResponseBody
    public JsonResult updatePackaging(User user, PdPackagingInfo info) {

        PdPackagingInfoHistory pdPackagingInfoHistory=pdPackagingService.getPackagingRecordDetail(info.getId());

        PdPackagingInfo pdPackagingInfo = pdPackagingService.getPackaging(pdPackagingInfoHistory.getPackaging());

        pdPackagingInfo.setMerchandise(info.getProductId());
        List<PdPackagingStructure> pdPackagingStructureList = JSONObject.parseArray(info.getList(), PdPackagingStructure.class);


        pdPackagingInfo.setOperation(3);
        pdPackagingInfo.setEnabledTime(new Date());
        pdPackagingInfo.setEnabled(1);
        pdPackagingInfo.setUpdateDate(new Date());
        pdPackagingInfo.setUpdateName(user.getUserName());
        pdPackagingInfo.setUpdator(user.getUserID());
        pdPackagingInfo.setOuterLength(info.getOuterLength());
        pdPackagingInfo.setOuterWidth(info.getOuterWidth());
        pdPackagingInfo.setOuterHeight(info.getOuterHeight());
        pdPackagingInfo.setLengthUnitId(info.getLengthUnitId());
        pdPackagingInfo.setLengthUnit(info.getLengthUnit());
        pdPackagingInfo.setWidthUnit(info.getWidthUnit());
        pdPackagingInfo.setWidthUnitId(info.getWidthUnitId());
        pdPackagingInfo.setHeightUnit(info.getHeightUnit());
        pdPackagingInfo.setHeightUnitId(info.getHeightUnitId());
        pdPackagingInfo.setOuterShape(info.getOuterShape());

        if(info.getEffectTime()==null){
            pdPackagingInfo.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));
        }else{
            pdPackagingInfo.setEffectTime(info.getEffectTime());
        }

        String res = pdPackagingService.updatePackaging(pdPackagingInfo, pdPackagingStructureList, user);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }


    /**
     * 暂停或启动包装
     */
    @RequestMapping("/stopOrStartPackaging.do")
    @ResponseBody
    public JsonResult stopOrStartPackaging(User user, Integer id, int enabled) {
        PdPackagingInfoHistory pdPackagingInfoHistory=pdPackagingService.getPackagingRecordDetail(id);
        PdPackagingInfo info = pdPackagingService.getPackagingDetail(pdPackagingInfoHistory.getPackaging());
        info.setEnabled(enabled);
        info.setEnabledTime(new Date());
        info.setUpdateDate(new Date());
        info.setUpdateName(user.getUserName());
        info.setUpdator(user.getUserID());
        info.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));
        if (enabled == 1) {
            info.setOperation(4);
        } else {
            info.setOperation(5);
        }
        List<PdPackagingStructure> pdPackagingStructureList = info.getStructureList();

        String res = pdPackagingService.updatePackaging(info, pdPackagingStructureList, user);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /**
     * 确定设置
     */
    @RequestMapping("/submitSettings.do")
    @ResponseBody
    public JsonResult submitSettings(User user, Integer productId) {


        PdMerchandise merchandise = productService.getByPdCusId(productId);
        merchandise.setPackagingState(2);
        productService.updatePdMerchandise(merchandise);
        return new JsonResult(1, null);
    }

    /**
     * 获取操作记录
     */
    @RequestMapping("/getPackagingRecordList.do")
    @ResponseBody
    public JsonResult getPackagingRecordList(User user, Integer id) {
        PdPackagingInfoHistory pdPackagingInfoHistory=pdPackagingService.getPackagingRecordDetail(id);
        List<PdPackagingInfoHistory> list = pdPackagingService.getPackagingRecordList(pdPackagingInfoHistory.getPackaging());
        return new JsonResult(1, list);
    }

    /**
     * 获取操作记录详情
     */
    @RequestMapping("/getPackagingRecordDetail.do")
    @ResponseBody
    public JsonResult getPackagingRecordDetail(User user, Integer recordId) {
        PdPackagingInfoHistory infoHistory = pdPackagingService.getPackagingRecordDetail(recordId);
        return new JsonResult(1, infoHistory);
    }

    /**
     * 包装删除
     */
    @RequestMapping("/deletePackaging.do")
    @ResponseBody
    public JsonResult deletePackaging(User user, Integer id) {
        PdPackagingInfoHistory pdPackagingInfoHistory=pdPackagingService.getPackagingRecordDetail(id);
        pdPackagingService.deletePackaging(pdPackagingInfoHistory.getPackaging());
        return new JsonResult(1, null);
    }
}
