package cn.sphd.miners.modules.orderReview.service;

import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReview;
import cn.sphd.miners.modules.system.entity.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ITSlOrdersItemReviewService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlOrdersItemReview selectTSlOrdersItemReviewById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlOrdersItemReview 
     * @return 集合
     */
    public List<TSlOrdersItemReview> selectTSlOrdersItemReviewList(TSlOrdersItemReview tSlOrdersItemReview);

    /**
     * 新增
     * 
     * @param tSlOrdersItemReview 
     * @return 结果
     */
    public int insertTSlOrdersItemReview(TSlOrdersItemReview tSlOrdersItemReview);

    /**
     * 修改
     * 
     * @param tSlOrdersItemReview 
     * @return 结果
     */
    public int updateTSlOrdersItemReview(TSlOrdersItemReview tSlOrdersItemReview);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewByIds(Long[] ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewById(Long id);

    Map getReviewDetails(Integer orderId, User user);

    boolean orderSplit(HttpServletRequest request, HttpServletResponse response,Integer orderId, User user);
}
