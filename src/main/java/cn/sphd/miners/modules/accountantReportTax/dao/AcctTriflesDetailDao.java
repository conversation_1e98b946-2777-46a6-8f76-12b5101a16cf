package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesDetailEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesEntity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AcctTriflesDetailDao extends IBaseDao<AcctTriflesDetailEntity,Serializable> {
    Map<String,Object> getTriflesReportRecord(AcctTriflesDetailEntity triflesDetailEntity);
}
