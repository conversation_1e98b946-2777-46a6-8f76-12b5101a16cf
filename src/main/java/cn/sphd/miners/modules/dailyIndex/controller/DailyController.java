package cn.sphd.miners.modules.dailyIndex.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.initializer.SaveRpcLog;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.HttpClientUtils;
import cn.sphd.miners.modules.dailyIndex.entity.DailyOrg;
import cn.sphd.miners.modules.dailyIndex.service.DailyOrgService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by HIAPAD on 2022/6/15.
 * 活跃指数
 */
@Controller
@RequestMapping("/daily")
public class DailyController {

    @Autowired
    DailyOrgService dailyOrgService;
    @Autowired
    PdCustomerService pdCustomerService;


    /**
     * <AUTHOR>
     * @Date 2022/6/15
     * 跳转活跃指数1页面
     */
    @RequestMapping("/toActivityIndex1.do")
    public String toActivityIndex1(){
        return "/activityIndex/activity1";
    }

    /**
     * <AUTHOR>
     * @Date 2022/6/15
     * 跳转活跃指数2页面
     */
    @RequestMapping("/toActivityIndex2.do")
    public String toActivityIndex2(){
        return "/activityIndex/activity2";
    }

    /**
     * <AUTHOR>
     * @Date 2022/6/15
     * 跳转活跃指数3页面
     */
    @RequestMapping("/toActivityIndex3.do")
    public String toActivityIndex3(){
        return "/activityIndex/activity3";
    }



    /**
     * <AUTHOR>
     * @Date 2022/6/28
     * 获取指定oids的机构指数列表  给管理平台提供的接口
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/getDailyOrgList.do")
    public JsonResult getDailyOrgList(String oids,Integer yearMonthDay,Integer type){
        List<Integer> oidList= JSON.parseArray(oids,Integer.class);
        List<Map<String,Object>> mapList =dailyOrgService.getDailyOrgCounts(oidList,yearMonthDay,type);
        return new JsonResult(1,mapList);
    }

    /**
     * <AUTHOR>
     * @Date 2022/6/30
     * 获取指定oid 指定日期 的机构指数  给管理平台提供的接口
     */
    @SaveRpcLog
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/getDailyOrgsByYearMonth.do")
    public JsonResult getDailyOrgsByYearMonth(Integer oid,Integer yearMonth){
        List<DailyOrg> dailyOrgList=dailyOrgService.getDailyOrgInfo(oid,yearMonth);
        return new JsonResult(1,dailyOrgList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/2/4
     * 活跃指数1 创建不足90天没交费的机构列表
     */
    @ResponseBody
    @RequestMapping("/dailyIndex1.do")
    public JsonResult dailyIndex1(User user,PageInfo pageInfo,String name){
        Boolean button=false;
        if (user.getRoleCode().equals("super")||user.getRoleCode().equals("sale")){
            button=true;
        }
        // 从管理平台访问
        String url=System.getProperty("miners.glptApiRoot")+"/daily/dailyIndex1.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("pageSize", String.valueOf(pageInfo.getPageSize()));
        map.put("currentPageNo", String.valueOf(pageInfo.getCurrentPageNo()));
        map.put("name", name);
        map.put("button", button.toString());
        if (!button) {// 是获得权限的普通员工，只能查询 其负责客户的机构
            List<Integer>  gTenantIds =pdCustomerService.getGTenantIdsByPrincipal(user.getOid(),user.getUserID());
            map.put("tenantIds", JSONObject.toJSONString(gTenantIds));
        }
//        JsonResult jsonResult = MinersHttpClientUtils.getJson(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }

    /**
     * <AUTHOR>
     * @Date 2023/2/4
     * 活跃指数1 指标设置列表接口
     */
    @ResponseBody
    @RequestMapping("/getIndexList1.do")
    public JsonResult getIndexList1(){
        // 从管理平台访问
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexList1.do";
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }

    /**
     * <AUTHOR>
     * @Date 2023/2/4
     * 活跃指数1 2 3 通用 查看某指标设置详情
     */
    @ResponseBody
    @RequestMapping("/getIndexInfo.do")
    public JsonResult getIndexInfo(Integer id){
        // 从管理平台访问
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexInfo.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("id", id.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }

    /**
     * <AUTHOR>
     * @Date 2023/2/5
     * 活跃指数1 2 3通用  修改指数设置保存
     */
    @ResponseBody
    @RequestMapping("/updateDailyIndex.do")
    public JsonResult updateDailyIndex(String json, User user){
        // 从管理平台访问
        String url=System.getProperty("miners.glptApiRoot")+"/daily/updateDailyIndex.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("json", json);
        map.put("tykjUserId", user.getUserID().toString());
        map.put("tykjUserName", user.getUserName());
        JsonResult result = client.jsonResponseToT(client.doPost(null, url, null,null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }

    /**
     * <AUTHOR>
     * @Date 2023/2/5
     * 活跃指数1 权重设置列表
     */
    @ResponseBody
    @RequestMapping("/getWeightList1.do")
    public JsonResult getWeightList1(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightList1.do";
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }

    /**
     * <AUTHOR>
     * @Date 2023/2/6
     * 活跃指数1 2 3通用 权重修改确定提交接口
     */
    @ResponseBody
    @RequestMapping("/updateWeightIndex.do")
    public JsonResult updateWeightIndex(String json, User user){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/updateWeightIndex.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("json", json);
        map.put("tykjUserId", user.getUserID().toString());
        map.put("tykjUserName", user.getUserName());
        JsonResult result = client.jsonResponseToT(client.doPost(null, url, null,null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }

    /**
     * <AUTHOR>
     * @Date 2023/2/6
     * 活跃指数2 创建已满90天但尚未收过费机构的wonderss月使用指数
     */
    @ResponseBody
    @RequestMapping("/dailyIndex2.do")
    public JsonResult dailyIndex2(User user,PageInfo pageInfo,Integer yearMonth,String name){
        Boolean button=false;
        if (user.getRoleCode().equals("super")||user.getRoleCode().equals("sale")){
            button=true;
        }

        String url=System.getProperty("miners.glptApiRoot")+"/daily/dailyIndex2.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("pageSize", String.valueOf(pageInfo.getPageSize()));
        map.put("currentPageNo", String.valueOf(pageInfo.getCurrentPageNo()));
        map.put("name", name);
        map.put("button", button.toString());
        if (!button) {// 是获得权限的普通员工，只能查询 其负责客户的机构
            List<Integer>  gTenantIds =pdCustomerService.getGTenantIdsByPrincipal(user.getOid(),user.getUserID());
            map.put("tenantIds", JSONObject.toJSONString(gTenantIds));
        }
        if (yearMonth!=null)
            map.put("yearMonth",yearMonth.toString());
//        JsonResult jsonResult = MinersHttpClientUtils.getJson(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }


    /**
     * <AUTHOR>
     * @Date 2023/2/7
     * 活跃指数2 指标设置列表接口
     */
    @ResponseBody
    @RequestMapping("/getIndexList2.do")
    public JsonResult getIndexList2(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexList2.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


    /**
     * <AUTHOR>
     * @Date 2023/2/7
     * 活跃指数2 权重设置列表
     */
    @ResponseBody
    @RequestMapping("/getWeightList2.do")
    public JsonResult getWeightList2(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightList2.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


    /**
     * <AUTHOR>
     * @Date 2023/2/7
     * 活跃指数3 缴费的机构列表  现在没有缴费功能，查不出缴费机构列表
     */
    @ResponseBody
    @RequestMapping("/dailyIndex3.do")
    public JsonResult dailyIndex3(User user,PageInfo pageInfo,String name){
        Boolean button=false;
        if (user.getRoleCode().equals("super")||user.getRoleCode().equals("sale")){
            button=true;
        }

        String url=System.getProperty("miners.glptApiRoot")+"/daily/dailyIndex3.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("pageSize", String.valueOf(pageInfo.getPageSize()));
        map.put("currentPageNo", String.valueOf(pageInfo.getCurrentPageNo()));
        map.put("name", name);
        map.put("button", button.toString());
//        JsonResult jsonResult = MinersHttpClientUtils.getJson(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }


    /**
     * <AUTHOR>
     * @Date 2023/2/8
     * 活跃指数3 指标设置列表接口
     */
    @ResponseBody
    @RequestMapping("/getIndexList3.do")
    public JsonResult getIndexList3(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexList3.do";
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


    /**
     * <AUTHOR>
     * @Date 2023/2/8
     * 活跃指数3 权重设置列表
     */
    @ResponseBody
    @RequestMapping("/getWeightList3.do")
    public JsonResult getWeightList3(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightList3.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


    /**
     * <AUTHOR>
     * @Date 2023/2/9
     * 公用 图表1 内部用户数 横轴为天，纵轴为每日零时在册职工与浏览者人数
     *  每日实际登录人数c  在册职工与浏览者人数d
     *  c/d
     */
    @ResponseBody
    @RequestMapping("/getIndexTable1.do")
    public JsonResult getIndexTable1(Integer oid,Integer yearMonth){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexTable1.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("oid", oid.toString());
        map.put("yearMonth", yearMonth.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


    /**
     * <AUTHOR>
     * @Date 2023/2/9
     * 公用 图表2 外部用户数 横轴为天，纵轴为实际的外部用户数
     *  期初 为 当月第一天外部浏览人数
     *  每天人数
     *  每天人数和期初变化数
     */
    @ResponseBody
    @RequestMapping("/getIndexTable2.do")
    public JsonResult getIndexTable2(Integer oid,Integer yearMonth){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexTable2.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("oid", oid.toString());
        map.put("yearMonth", yearMonth.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/22
     * 公用 图表3 内部用户登录次数 横轴为天，纵轴为每日登录总次数
     * 日人均登录次数=登录总次数/期间内部用户的和/期间天数
     * 登录者日人均登录次数=登录总次数/期间内部用户中登录者的和/期间天数
     */
    @ResponseBody
    @RequestMapping("/getIndexTable3.do")
    public JsonResult getIndexTable3(Integer oid,Integer yearMonth){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexTable3.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("oid", oid.toString());
        map.put("yearMonth", yearMonth.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/23
     * 公用 图表4 内部用户登录时长（时长按小时h算）
     * 横轴为天，纵轴为每日登录总时长
     * 日人均登录时长=登录总时长/期间内部用户的和/期间天数
     * 登录者日人均登录时长=登录总时长/期间内部用户中登录者的和/期间天数
     */
    @ResponseBody
    @RequestMapping("/getIndexTable4.do")
    public JsonResult getIndexTable4(Integer oid,Integer yearMonth){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexTable4.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("oid", oid.toString());
        map.put("yearMonth", yearMonth.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/24
     * 公用 图表5 外部用户登录次数
     * 横轴为天，纵轴为每日登录总次数
     */
    @ResponseBody
    @RequestMapping("/getIndexTable5.do")
    public JsonResult getIndexTable5(Integer oid,Integer yearMonth){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexTable5.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("oid", oid.toString());
        map.put("yearMonth", yearMonth.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/24
     * 公用 图表6
     * 外部用户登录时长（时长按小时h算）
     * 横轴为天，纵轴为每日登录总时长
     */
    @ResponseBody
    @RequestMapping("/getIndexTable6.do")
    public JsonResult getIndexTable6(Integer oid,Integer yearMonth){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexTable6.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("oid", oid.toString());
        map.put("yearMonth", yearMonth.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/19
     * 特殊机构-活跃指数1-指标设置记录列表
     */
    @ResponseBody
    @RequestMapping("/getIndexHistoryList1.do")
    public JsonResult getIndexHistoryList1(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexHistoryList1.do";
        HttpClientUtils client = new HttpClientUtils(url);
//        Map<String,String> map=new HashMap<String,String>();
//        map.put("id", id.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/20
     * 特殊机构-活跃指数1- 指标设置记录  详情
     */
    @ResponseBody
    @RequestMapping("/getIndexHistoryInfo1.do")
    public JsonResult getIndexHistoryInfo1(Integer versionNo){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexHistoryInfo1.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/20
     * 特殊机构-活跃指数1 权重设置 修改历史记录
     */
    @ResponseBody
    @RequestMapping("/getWeightHistoryList1.do")
    public JsonResult getWeightHistoryList1(Integer versionNo){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightHistoryList1.do";
        HttpClientUtils client = new HttpClientUtils(url);
//        Map<String,String> map=new HashMap<String,String>();
//        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/20
     * 特殊机构-活跃指数1- 权重设置记录 单一详情
     */
    @ResponseBody
    @RequestMapping("/getWeightHistoryInfo1.do")
    public JsonResult getWeightHistoryInfo1(Integer versionNo){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightHistoryInfo1.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/21
     * 特殊机构-活跃指数2   指标设置记录
     */
    @ResponseBody
    @RequestMapping("/getIndexHistoryList2.do")
    public JsonResult getIndexHistoryList2(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexHistoryList2.do";
        HttpClientUtils client = new HttpClientUtils(url);
//        Map<String,String> map=new HashMap<String,String>();
//        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/21
     * 特殊机构-活跃指数2   指标设置记录  详情
     */
    @ResponseBody
    @RequestMapping("/getIndexHistoryInfo2.do")
    public JsonResult getIndexHistoryInfo2(Integer versionNo){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexHistoryInfo2.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/21
     * 特殊机构-活跃指数2 权重设置 修改历史记录
     */
    @ResponseBody
    @RequestMapping("/getWeightHistoryList2.do")
    public JsonResult getWeightHistoryList2(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightHistoryList2.do";
        HttpClientUtils client = new HttpClientUtils(url);
//        Map<String,String> map=new HashMap<String,String>();
//        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/22
     * 特殊机构-活跃指数2   权重设置 历史记录  详情
     */
    @ResponseBody
    @RequestMapping("/getWeightHistoryInfo2.do")
    public JsonResult getWeightHistoryInfo2(Integer versionNo){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightHistoryInfo2.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/22
     * 特殊机构-活跃指数3   指标设置记录
     */
    @ResponseBody
    @RequestMapping("/getIndexHistoryList3.do")
    public JsonResult getIndexHistoryList3(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexHistoryList3.do";
        HttpClientUtils client = new HttpClientUtils(url);
//        Map<String,String> map=new HashMap<String,String>();
//        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/23
     * 特殊机构-活跃指数3   指标设置记录  详情
     */
    @ResponseBody
    @RequestMapping("/getIndexHistoryInfo3.do")
    public JsonResult getIndexHistoryInfo3(Integer versionNo){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getIndexHistoryInfo3.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/23
     * 特殊机构-活跃指数3 权重设置 修改历史记录
     */
    @ResponseBody
    @RequestMapping("/getWeightHistoryList3.do")
    public JsonResult getWeightHistoryList3(){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightHistoryList3.do";
        HttpClientUtils client = new HttpClientUtils(url);
//        Map<String,String> map=new HashMap<String,String>();
//        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2025/3/24
     * 特殊机构-活跃指数3   权重设置 历史记录  详情
     */
    @ResponseBody
    @RequestMapping("/getWeightHistoryInfo3.do")
    public JsonResult getWeightHistoryInfo3(Integer versionNo){
        String url=System.getProperty("miners.glptApiRoot")+"/daily/getWeightHistoryInfo3.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<String,String>();
        map.put("versionNo", versionNo.toString());
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result;
    }

}
