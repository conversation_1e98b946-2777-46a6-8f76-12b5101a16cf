$(function(){
			$("#allFile").hide();
	$("#histype").hide();
	$(".back").hide();
	$(".backid").hide();
	$("#category_id").hide();
	$("#category_idd").hide();
	$("#fi_amount").hide();
	
	getFirstType();
});

//获取历史第一层列表
function getFirstType(){
		$.ajax({
		url:"${pageContext.request.contextPath}/res_getFirstType.action",
		data:{},
		Type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			var typeSize=data["typeSize"];
			$("#typesize").append(typeSize);
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else{
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid' >"+id+"</td><td onclick='getsecondtype($(this))' class='tz'>"+name+"</td></tr>";
					$("#filetype").append(str);
				}
			}
		
		},
		error:function(){
			alert("连接错误，请稍后重试！");
		}
	});
}

//获取历史第二三层列表
function getsecondtype(obj){
	var id=obj.parent().children(":eq(0)").html();
	$("#filetype").children(":gt(0)").remove();
	$("#typesize").html("");
	$("#category_id").html("");
	$("#type_name").html("");
	$(".back").show();
	$(".cs_name").show();
	$("#intfirst").hide();
	$("#intsecond").show();
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getHistory.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			var typeSize=data["typeSize"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==2){
				$("#filetype").hide();
				$("#allFile").hide();
				$("#histype").show();
				var typename=data["typename"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var file=list[i]["file"];
					var name=list[i]["name"];
					var id_num=list[i]["id_num"];
					var str="<tr onclick='getthirdtype($(this))'><td class='fileid'>"+ file +"</td><td  class='tz'>"+ name +"</td><td>"+id_num+"</td></tr>"
					$("#histype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}else if(status==3){
				$("#filetype").hide();
				$("#allFile").hide();
				$("#histype").show();
				var parentid=data["backid"];
				var typename=data["typename"];
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}else{
				var typename=data["typename"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getsecondtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}
		},
		error:function(){
		}
	});
}
//获取第四层列表
function getthirdtype(obj){
	var id=obj.children(":eq(0)").html();
	$("#histype").children(":gt(0)").remove();
	$("#typesize").html("");
	$("#fisize").html("");
	$("#category_id").html("");
	$("#category_idd").html("");
	$("#type_name").html("");
	$(".back").hide();
	$(".backid").show();
	$(".cs_name").show();
	$("#intsecond").hide();
	$("#ty_amount").hide();
	$("#intthird").show();
	$("#fi_amount").show();
	$.ajax({
		url:"${pageContext.request.contextPath}/res_lastHis.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else{
				$("#histype").hide();
				$("#allFile").show();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var file_sn=list[i]["file_sn"];
					var type=list[i]["type"];
					var change_num=list[i]["change_num"];
					if(change_num==0){
						change_num="--";
					}else{
						change_num="G"+change_num;
					}
					var size=list[i]["size"];
					var operator_name=list[i]["opertator_name"];
					var create_date=list[i]["create_date"];
					var path=list[i]["path"];
					var str="<tr>" +
						"<td class='re_id'>"+id+"</td>" +
						"<td>"+file_sn+"</td>" +
						"<td>"+name+"</td>" +
						"<td>"+type+"</td>" +
						"<td>"+change_num+"</td>" +
						"<td>"+size+"</td>" +
						"<td>"+operator_name+"</td>" +
						"<td>"+create_date+"</td>" +
						"<td>" +
						"<a class='ty-color-blue' href ='#addOpinion2' onclick='ddown($(this))'>下载</a>" +
						"<label style='display:none;'>"+list[i]["path"]+"</label>" +
						"<a class='ty-color-blue' onclick='see_allMessage($(this))'>查看详情</a></td></tr>";
					$("#allFile").append(str);
				}
				$("#category_idd").append(parentid);
				$("#type_name").append(typename);
				$("#fisize").append(typeSize);
			}
		},
		error:function(){
		}
	}); 
}
//第四层点击返回上一页
function goback1(){
	var id=$("#category_idd").html();
	$("#allFile").children(":gt(0)").remove();
	$("#typesize").html("");
	$("#fisize").html("");
	$(".back").show();
	$(".backid").hide();
	$(".cs_name").show();
	$("#category_id").html("");
	$("#type_name").html("");
	$("#ty_amount").show();
	$("#fi_amount").hide();
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getHistory.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==2){
				$("#allFile").hide();
				$("#histype").show();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var file=list[i]["file"];
					var name=list[i]["name"];
					var id_num=list[i]["id_num"];
					var str="<tr onclick='getthirdtype($(this))'><td class='fileid'>"+ file +"</td><td  class='tz'>"+ name +"</td><td>"+id_num+"</td></tr>"
					$("#histype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}else if(status==3){
				var parentid=data["backid"];
				var typename=data["typename"];
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
			}else{
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getsecondtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}
		},
		error:function(){
		}
	});
}
//返回上一页
function goback(){
	var id=$("#category_id").html();
	$("#typesize").html("");
	$("#category_id").html("");
	$("#type_name").html("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getFileBack.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				$("#intsecond").hide();
				$("#intfirst").show();
				$(".back").hide();
				$("#filetype").children(":gt(0)").remove();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getsecondtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}else if(status==2){
				$("#intsecond").hide();
				$(".back").hide();
				$("#histype").hide();
				$("#intfirst").show();
				$("#allFile").hide();
				$("#filetype").show();
				$("#histype").children(":gt(0)").remove();
				$("#allFile").children(":gt(0)").remove();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getsecondtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}else{
				$("#intsecond").show();
				$("#intthird").hide();
				$("#allFile").hide();
				$("#histype").hide();
				$("#filetype").show();
				$("#histype").children(":gt(0)").remove();
				$("#allFile").children(":gt(0)").remove();
				$("#filetype").children(":gt(0)").remove();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getsecondtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}
		},
		error:function(){
		}
	});
}	
//查看详情
function see_allMessage(obj){
	bounce.show($("#addOsee"));
	var id=obj.parent().parent().children(":eq(0)").html();
	$("#Osee_createdate").html("");
	$("#Osee_filesn").html("");
	$("#Osee_name").html("");
	$("#Osee_size").html("");
	$("#Osee_type").html("");
	$("#Osee_change").html("");
	$("#Osee_updatedate").html("");
	$("#Osee_reason").html("");
	$("#Osee_content").html("");
	$("#Osee_opertatorname").html("");
	$("#Osee_operatedate").html("");
	$("#Osee_verifiername").html("");
	$("#Osee_verifydate").html("");
	$("#Osee_approvername").html("");
	$("#Osee_approvedate").html("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_hisFileMessage.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var list=data["list"];
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else{
				var reason = list[0]["reason"];
				if(reason == null){
					reason ="";
				}
				$("#Osee_createdate").html(list[0]["create_date"]);
				$("#Osee_filesn").html(list[0]["file_sn"]);
				$("#Osee_name").html(list[0]["name"]);
				$("#Osee_size").html(list[0]["size"]);
				$("#Osee_type").html(list[0]["type"]);
				var ee_cha=list[0]["change_num"];
				if(ee_cha==0){
					$("#Osee_change").html("--");
				}else{
					$("#Osee_change").html("G"+ee_cha);
				}
				$("#Osee_updatedate").html(list[0]["update_date"]);
				$("#Osee_reason").html(reason);
				$("#Osee_content").html(list[0]["content"]);
				$("#Osee_opertatorname").html(list[0]["opertator_name"]);
				var date_operate=list[0]["operate_date"];
				if(date_operate=='1900-01-01 00:00:00'){
					date_operate=null;
				}
				$("#Osee_operatedate").html(date_operate);
				$("#Osee_verifiername").html(list[0]["verifier_name"]);
				var date_verity=list[0]["verify_date"];
				if(date_verity=='1900-01-01 00:00:00'){
					date_verity=null;
				}
				$("#Osee_verifydate").html(date_verity);
				$("#Osee_approvername").html(list[0]["approver_name"]);
				var date_approve= list[0]["approve_date"];
				if(date_approve=='1900-01-01 00:00:00'){
					date_approve=null;
				}
				$("#Osee_approvedate").html(date_approve);
			}
		},
		error:function(){
		}
	}); 
}

//下载文件
function ddown(obj){
	var name = obj.siblings("label").html();
	if (name == "" || name == undefined || name == null || name == "undefined" || name == "null") {
		alert("文件不存在！");
		return false;
	}
	obj.attr("download","").attr("href",$.uploadUrl+path);
}
 