<template>
  <div id="discussionBorrowQueryPer">
    <TY_NavTop :title="paramQuery.title"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.timeBegin|formatDay('YYYY年MM月DD日')}} - {{queryData.timeEnd|formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent" v-if="paramQuery.eventType === 1">
              <span v-if="paramQuery.approveStatus === '2'">审批通过</span><span v-if="paramQuery.approveStatus === '3'">被驳回</span>的讨论组查阅申请
            </div>
            <div class="queryContent" v-else>
              <span v-if="!paramQuery.fromUser">您经手且<span v-if="paramQuery.approveStatus === '2'">审批通过</span><span v-if="paramQuery.approveStatus === '3'">审批驳回</span>的讨论组查阅申请</span>
              <span v-if="paramQuery.fromUser">{{paramQuery.fromUserName}}被<span v-if="paramQuery.approveStatus === '2'">审批通过</span><span v-if="paramQuery.approveStatus === '3'">审批驳回</span>的讨论组查阅申请</span>
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的数据共如下{{pageInfo.totalResult}}条
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells discussBorrow">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in queryData.list" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div><span class="participantsNum">{{item.participantsNum}}人</span>{{item.title}}</div>
                <div class="create">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
          <div class="page">
            <el-pagination
              layout="prev, pager, next"
              @current-change = "getReadFileByType"
              :page-count="pageInfo.totalPage">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'discussionBorrowQueryPer',
  data () {
    return {
      loading: true,
      paramQuery: {},
      queryData: {
        beginDate: 1560408078000,
        endDate: 1560408078000,
        approveStatus: 2
      },
      state: '',
      pageInfo: {
        totalPage: 1,
        pageSize: 8
      }
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    this.getReadFileByType(1)
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/discussionBorrowDetail/${id}/query${this.paramQuery.eventType}`
      })
    },
    getReadFileByType: function (val) {
      this.paramQuery.currentPageNo = val
      this.paramQuery.pageSize = this.pageInfo.pageSize
      let that = this
      this.$http.post('../../../read/findReadForumByType.do', this.paramQuery, {
        emulateJSON: true
      }).then((response) => {
        this.loading = false
        let res = response.body
        if (res) {
          let data = res.data
          console.log(res)
          that.queryData = data
          that.pageInfo = data.pageInfo
        } else {
          that.$kiko_message('加载失败')
        }
      }).catch(function () {
        // this.jumpError('系统错误，请重试！')
      })
    }
  }
}
</script>
