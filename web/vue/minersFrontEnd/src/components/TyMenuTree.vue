<!--
菜单分级显示
----desc：该子组件不是公用组件，
----pages（用到的页面）：[ customerManage ]
-->
<template>
 <div class="TyMenuTree">
   <div>
      <div >
        <table class="ty-table">
          <tbody>
          <tr>
            <td class="bd">一级菜单</td>
            <td colspan="2" class="tdDiv">
              <table class="ty-table tbl2">
                <tbody>
                <tr>
                  <td class="bd">二级菜单</td>
                  <td class="tdDiv"><div class="brd bd">三级菜单</div></td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr v-for="(menuItem1, menuIndex1) in newLevelList" :key="menuIndex1">
            <td @click="tdClick( menuItem1, 1 )">{{ menuItem1.name }}</td>
            <td colspan="2" class="tdDiv">
              <table class="ty-table tbl2">
                <tbody>
                <tr v-if="menuItem1.subPopdoms.length > 0" class="flexDv" v-for="(menuItem2, menuIndex2) in menuItem1.subPopdoms" :key="menuIndex2">
                  <td @click="tdClick( menuItem2, 2 )">{{ menuItem2.name }}</td>
                  <td class="tdDiv">
                    <div class="brd" v-if="menuItem2.subPopdoms.length > 0"
                         v-for="(menuItem3, menuIndex3) in menuItem2.subPopdoms" :key="menuIndex3"
                         @click="tdClick( menuItem3, 3 )"
                    >
                      {{  menuItem3.name }}
                    </div>
                    <div v-else>
                    </div>
                  </td>
                </tr>
                <tr v-else>
                  <td></td>
                  <td class="tdDiv"><div class="brd"></div></td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          </tbody>
        </table>

      </div>
   </div>
 </div>
</template>
<script>

export default {
  name: "TyMenuTree",
  data() {
    return {
      newLevelList:[],
    };
  },
  props:{
    list: { 'type':[Array], 'default':[] },
    tdClickFun: { 'type':Function, 'default': null },
  },
  mounted() {
    console.log('Menu list=', this.list)
    this.newLevelList = this.setNewLevelList(this.list)
  },
  methods: {
    tdClick(menuItem, level){
      // menuItem 菜单详情， level：等级
      if(this.tdClickFun){
        this.tdClickFun(menuItem, level)
      }

    },
    setNewLevelList(originData){
      let newData = []
      for (let item of originData) {
        for (let it of originData) {
          if (it.pid === item.mid) {
            item.subPopdoms.push(it)
          }
        }
        if (item.pid === '0') {
          newData.push(item)
        }
      }
      console.log('newData= ', newData)
      return newData
    },


  },
}
</script>

<style lang="scss" scoped>
.TyMenuTree{
  .tdDiv{ padding: 0;  }
  .brd{ line-height: 40px; border-bottom: 1px solid #ccc; }
  .brd:last-child{ border-bottom: none; }
  .ty-table tbody td{ width: 160px; }
  .tbl2 tbody td{ border-left: none; }
  .tbl2 tbody td:last-child{ border-right: none; }
  .tbl2 tbody tr:first-child td{ border-top: none; }
  .tbl2 tbody tr:last-child td{ border-bottom: none; }
  .bd{ font-weight: bold; font-size:14px; color: #666;  }
}
</style>