package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.utils.MyStrings;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class PersonnelAttendanceUserDto implements Serializable {

    //职工基本信息
    private Integer userID;//人员id
    private String userName; //人员名称
    private Integer deptId; //部门id
    private String departName; //部门名

    //今天的考勤字段
    private Integer idToday;  //今天考勤id
    private String beginStateToday;//今天上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班
    private Date amAttendanceToday; //今天上午考勤时间

    //昨天的考勤字段
    private Integer idYesterday;  //昨天考勤id
    private String beginStateYesterday;//昨天上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班
    private String endStateYesterday;//昨天状态:0-,未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班
    private Date amAttendanceYesterday; //昨天上午考勤时间
    private Date pmAttendanceYesterday; //昨天下午考勤时间

    private String workBeginTime;  //开始上班时间

    private Map<String,Object> attendanceAll;  //月考勤的所有情况

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public Integer getIdToday() {
        return idToday;
    }

    public void setIdToday(Integer idToday) {
        this.idToday = idToday;
    }

    public String getBeginStateToday() {
        return beginStateToday;
    }

    public void setBeginStateToday(String beginStateToday) {
        this.beginStateToday = beginStateToday;
    }

    public Date getAmAttendanceToday() {
        return amAttendanceToday;
    }

    public void setAmAttendanceToday(Date amAttendanceToday) {
        this.amAttendanceToday = amAttendanceToday;
    }

    public Integer getIdYesterday() {
        return idYesterday;
    }

    public void setIdYesterday(Integer idYesterday) {
        this.idYesterday = idYesterday;
    }

    public String getBeginStateYesterday() {
        return beginStateYesterday;
    }

    public void setBeginStateYesterday(String beginStateYesterday) {
        this.beginStateYesterday = beginStateYesterday;
    }

    public String getEndStateYesterday() {
        return endStateYesterday;
    }

    public void setEndStateYesterday(String endStateYesterday) {
        this.endStateYesterday = endStateYesterday;
    }

    public Date getAmAttendanceYesterday() {
        return amAttendanceYesterday;
    }

    public void setAmAttendanceYesterday(Date amAttendanceYesterday) {
        this.amAttendanceYesterday = amAttendanceYesterday;
    }

    public Date getPmAttendanceYesterday() {
        return pmAttendanceYesterday;
    }

    public void setPmAttendanceYesterday(Date pmAttendanceYesterday) {
        this.pmAttendanceYesterday = pmAttendanceYesterday;
    }

    public Map<String, Object> getAttendanceAll() {
        return attendanceAll;
    }

    public void setAttendanceAll(Map<String, Object> attendanceAll) {
        this.attendanceAll = attendanceAll;
    }

    public String getWorkBeginTime() {
        return workBeginTime;
    }

    public void setWorkBeginTime(String workBeginTime) {
        this.workBeginTime = workBeginTime;
    }

    public PersonnelAttendanceUserDto() {
    }

    public PersonnelAttendanceUserDto(Integer userID, String userName, String departId, String departName, Integer idToday, String beginStateToday, Date amAttendanceToday, Integer idYesterday, String beginStateYesterday, String endStateYesterday, Date amAttendanceYesterday, Date pmAttendanceYesterday) {
        this.userID = userID;
        this.userName = userName;
        if(!MyStrings.nulltoempty(departId).isEmpty()) {
            this.deptId = Integer.valueOf(departId);
            this.departName = departName;
        } else {
            this.deptId = 0;
            this.departName = "";
        }
        this.idToday = idToday;
        this.beginStateToday = beginStateToday;
        this.amAttendanceToday = amAttendanceToday;
        this.idYesterday = idYesterday;
        this.beginStateYesterday = beginStateYesterday;
        this.endStateYesterday = endStateYesterday;
        this.amAttendanceYesterday = amAttendanceYesterday;
        this.pmAttendanceYesterday = pmAttendanceYesterday;
    }
    public PersonnelAttendanceUserDto(Integer userID, String userName, String departId, String departName) {
        this.userID = userID;
        this.userName = userName;
        if(!MyStrings.nulltoempty(departId).isEmpty()) {
            this.deptId = Integer.valueOf(departId);
            this.departName = departName;
        } else {
            this.deptId = 0;
            this.departName = "";
        }
    }
}
