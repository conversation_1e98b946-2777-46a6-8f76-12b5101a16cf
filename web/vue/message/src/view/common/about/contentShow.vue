<template>
  <div id="contentShow" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="内容" :isBackHome=false></TY_NavTop>
    <div class="container">
      <div class="place">
        <div class="placeContainer">
          <pre>{{aboutFile.content}}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  pre{
    white-space: normal;
    font-family: "Microsoft Yahei";
  }
</style>

<script>
  export default {
    name: 'contentShow',
    data () {
      return {
        loading: true,
        aboutFile: {}
      }
    },
    created: function () {
      let that = this
      let fileId = this.$route.params.id
      this.axios.post('../../../about/fileHistorySingleByAbout.do', {
        id: fileId
      })
        .then(function (response) {
          let data = response.data.data
          that.loading = false
          console.log(response)
          that.aboutFile = data.aboutFileHistory
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  }
</script>
