<template>
  <div id="ticketAuthSearch" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="增票认证" isButton="true" @toggleSubmit="submitBtn"></TY_NavTop>
    <div class="container">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">  您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <br>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <hr>
            <div>
              <h4 class="item-header">认证结果</h4>
              <div class="el-form-item" id="staList">
                <el-radio-group v-model="queryForm.approvalStatus" size="small">
                  <el-radio label="1" border>认证已通过的发票</el-radio><br/><br>
                  <el-radio label="2" border>认证未通过、不用来抵扣，只用作报销的发票</el-radio><br/><br>
                  <el-radio label="3" border>认证未通过、换了新发票的发票</el-radio>
                </el-radio-group>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#ticketAuthSearch{
  #staList .el-radio{ width: 300px; margin-bottom: 15px;}
  hr{ color:#fff; }
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  .el-radio{  display: inline-block;}
  #resonCon{
    .el-radio{
      display: inline-block; width: 150px; margin-right: 10px; padding-left: 0; padding-right: 0; text-align: center; margin-top: 5px;
      .el-radio__label{ padding-left: 0; }
    }
  }

  .el-radio__input, #reimburseApplyQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
  .buttonGroupRight .nav_btn:last-child { padding:0 30px; }
}
</style>
<script>
export default {
  name: 'ticketAuthSearch',
  data () {
    return {
      loading: false,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approvalStatus: '1'
      },
      type: 0
    }
  },
  created: function () {
  },
  methods: {
    submitBtn: function () {
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        approvalStatus: this.queryForm.approvalStatus,
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        type: this.queryForm.type
      }
      if (queryParam.type === '3') {
        if (queryParam.beginDate.length > 5 && queryParam.endDate.length > 5) {
        } else {
          this.$kiko_message('请选择开始时间和结束时间！')
          return false
        }
      }
      console.log('传过去的值')
      console.log(queryParam)
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/ticketAuthSearchList'
      })
    }
  }
}
</script>
