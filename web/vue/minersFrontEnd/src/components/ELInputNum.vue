<template>
  <!--  加强版的element输入框，可以自动补位，限制数字-->
  <el-input
      v-model="value"
      placeholder="请录入正整数"
      clearable
      @blur="blur_judgePlaceNum($event)"
      @input="handleInput"
  >
    <template v-for="(value, name) in $slots" #[name]>
      <slot :name="name" />
    </template>
  </el-input>
</template>

<script>
export default {
  props: {
    modelValue: {}
  },
  emits: ['update:modelValue', 'inputEvent'],
  computed: {
    value: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  methods: {
    handleInput () {
      this.$emit('inputEvent')
    },
    blur_judgePlaceNum (event) {
      let input = event.target.value
      // if (!/^-?\d*(\.\d{0,4})?$/.test(input)) {
      //   this.$message({
      //     type: 'error',
      //     message: '请输入一个有效的数字，小数部分最多四位数！'
      //   })
      //   this.$emit('update:modelValue', '')
      // }
      if (input && !/^[1-9]\d*$/.test(input)) {
        this.$message({
          type: 'error',
          message: '请输入正整数！'
        })
        this.$emit('update:modelValue', '')
      }
    }
  }
}
</script>