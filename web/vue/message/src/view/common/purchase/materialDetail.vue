<template>
  <div id="materialDetail">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-cells_none">
        <div class="ui-cell" v-if="param.user === 'xb' || ( param.user === 'zbi' && param.tab > 1 ) || ( param.user === 'zbu' && param.tab > 2 )">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">待入库物料名称</el-col>
                <el-col :span="12">{{materialDetail.name}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-else>
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">待检验物料名称</el-col>
                <el-col :span="12">{{materialDetail.name}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-if="param.user === 'xb' || ( param.user === 'zbi' && param.tab > 1 ) || ( param.user === 'zbu' && param.tab > 2 )">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">待入库物料编号</el-col>
                <el-col :span="12">{{materialDetail.code}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-else>
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">待检验物料编号</el-col>
                <el-col :span="12">{{materialDetail.code}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">供应商</el-col>
                <el-col :span="12">{{materialDetail.supplier_name}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">规格</el-col>
                <el-col :span="12">{{materialDetail.specifications}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">型号</el-col>
                <el-col :span="12">{{materialDetail.model}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">申请入库的数量</el-col>
                <el-col :span="12">{{materialDetail.quantity_plan}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12">到货日期</el-col>
                <el-col :span="12">{{materialDetail.arrive_date | formatDay('YYYY-MM-DD')}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-if="materialDetail.unqualified_info">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12"><span class="red">不合格品的信息</span></el-col>
                <el-col :span="12">{{materialDetail.unqualified_info}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-if="materialDetail.unqualified_handle === 'A'">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12"><span class="red">不合格品处理方案</span></el-col>
                <el-col :span="12">由于紧急需要，建议特采，提出让步申请</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-if="materialDetail.state === '6' || materialDetail.state === '7'">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12"><span class="red">让步审批的结果</span></el-col>
                <el-col :span="12" v-if="materialDetail.state === '6'">审批通过，允许入库</el-col>
                <el-col :span="12" v-if="materialDetail.state === '7'">审批未通过，不允许入库</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-if="materialDetail.state === '7'">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12"><span class="red">理由</span></el-col>
                <el-col :span="12">{{materialDetail.reject_reason}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-if="materialDetail.reject_reason">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12"><span class="red">让步审批的结果</span></el-col>
                <el-col :span="12">{{materialDetail.reject_reason}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="ui-cell" v-for="(item, index) in checks" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <el-row>
                <el-col :span="12"><span class="red">仓库所清点的数量</span></el-col>
                <el-col :span="12">{{item.check_quantity}}{{item.unit}} {{item.create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="panel-content text-right">
          <div>采  购：{{materialDetail.create_name}} {{materialDetail.create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div v-if="materialDetail.check_name">检  验：{{materialDetail.check_name}} {{materialDetail.check_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div v-if="materialDetail.reject_name">让步申请：{{materialDetail.reject_name}} {{materialDetail.reject_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div v-if="materialDetail.reject_approval_name">让步审批：{{materialDetail.reject_approval_name}} {{materialDetail.reject_approval_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div v-if="materialDetail.stock_date">仓库：{{materialDetail.stock_name}} {{materialDetail.stock_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
      </div>
      <!--以下为不同角色不同状态的不同处理方式-->
      <!--1.来源为检验待处理-->
      <div class="panel-content" v-show="param.user === 'vbc' && param.tab === 1">
        <div class="ui-row">
          <div>请检验，并请判定本批物料质量是否合格。</div>
          <el-form :label-position="left" :model="authApproval" :rules="rules" label-width="154px" ref="authApproval">
            <el-radio-group v-model="authApproval.type" style="margin-left: 30px">
              <el-radio label="1">合格</el-radio>
              <el-radio label="0">不合格</el-radio>
            </el-radio-group>
            <div class="ui-row" v-show="authApproval.type === '0'">
              <el-input type="textarea" :rows="2" v-model="authApproval.memo" size="small" placeHolder="请在此录入不合格品的信息"></el-input>
            </div>
          </el-form>
        </div>
        <div class="handle_button">
          <button class="ui-btn ui-btn_info" @click="authConfirm(1)">确定</button>
        </div>
      </div>
      <!--2.来源为仓库待入库-->
      <div class="panel-content" v-show="param.user === 'xb' && param.tab === 1">
        <div class="ui-row">
          <div>请确认本批物料实际数量与申请入库的数量是否一致。</div>
          <el-form :label-position="left" :model="authApproval" :rules="rules" label-width="154px" ref="authApproval">
            <el-radio-group v-model="authApproval.type" style="margin-left: 30px">
              <el-radio label="1">数量一致，入库</el-radio>
              <el-radio label="2">数量不一致</el-radio>
            </el-radio-group>
            <div class="ui-row" v-show="authApproval.type === '2'">
              录入您所清点的数量后请点击确定，以便采购知晓及处置。
              <el-input-number v-model="authApproval.num" controls-position="right" :min="0" size="small"></el-input-number>
            </div>
          </el-form>
        </div>
        <div class="handle_button">
          <button class="ui-btn ui-btn_info" @click="authConfirm(2)">确定</button>
        </div>
      </div>
      <!--3.来源为仓库数量异议待处理-->
      <div class="panel-content" v-show="param.user === 'xb' && param.tab === 2">
        <div class="ui-row">
          <div class="ui-row">
            输入框内可录入您重新所清点后的数量。
            <el-input-number v-model="authApproval.num" controls-position="right" :min="0" size="small"></el-input-number>
          </div>
        </div>
        <div class="handle_button">
          <button class="ui-btn ui-btn_info" @click="authConfirm(3)">确定</button>
        </div>
      </div>
      <!--4.来源为技术-->
      <div class="panel-content" v-show="param.user === 'wb'">
        <div class="ui-row">
          <el-form :label-position="left" :model="authApproval" :rules="rules" label-width="154px" ref="authApproval">
            <el-radio-group v-model="authApproval.type" style="margin-left: 30px">
              <el-radio label="5">审批通过，允许该物料入库</el-radio>
              <el-radio label="6">审批未通过，不允许该物料入库</el-radio>
            </el-radio-group>
            <div class="ui-row" v-show="authApproval.type === '6'">
              <el-input type="textarea" :rows="2" v-model="authApproval.memo" size="small" placeHolder="请在此录入不可让步接受的理由"></el-input>
            </div>
          </el-form>
        </div>
        <div class="handle_button">
          <button class="ui-btn ui-btn_info" @click="authConfirm(4)">确定</button>
        </div>
      </div>
      <!--5.来源为采购数量异议待处理-->
      <div class="panel-content" v-show="param.user === 'zbi' && param.tab === 3">
        <div class="ui-row">
          <div class="red">！提示</div>
          <div class="red">仓库在系统中可修改所清点的数量。</div>
          <div>请与仓库沟通，或共同清点数量，确认无误后再在本页操作</div>
        </div>
        <div class="ui-row">
          <el-radio-group v-model="authApproval.type">
            <el-radio label="4">经确认，仓库所清点的数量是正确的。</el-radio>
          </el-radio-group>
        </div>
        <div class="handle_button">
          <button class="ui-btn ui-btn_info" @click="authConfirm(5)">确定</button>
        </div>
      </div>
      <!--6.来源为采购不合格物品待处理-->
      <div class="panel-content" v-show="param.user === 'zbu' && param.tab === 1">
        <div class="handle_button">
          <button class="ui-btn ui-btn_info" @click="orderQuality">该批订单质量情况</button>
          <button class="ui-btn ui-btn_info" @click="handleUnqualified">处理</button>
        </div>
      </div>
    </div>
    <el-dialog
      title="请对该不合格物料给予处理"
      :visible.sync="handleDialog"
      width="98%"
    >
      <el-radio-group v-model="zbu_handleUd">
        <el-radio label="禁用" disabled>退货</el-radio>
        <el-radio label="禁用" disabled>通知供应商，由其到现场挑选</el-radio>
        <el-radio label="禁用" disabled>通知供应商，由其到现场返工</el-radio>
        <el-radio label="禁用" disabled>代供应商挑选</el-radio>
        <el-radio label="禁用" disabled>代供应商返工</el-radio>
        <el-radio label="1">由于紧急需要，建议特采，提出让步申请</el-radio>
      </el-radio-group>
      <div slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value=" 取消" @click="handleDialog = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="authConfirm(6)">
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style>
  .panel-content{
    background: #fff;
    overflow: hidden;
    padding: 0.8rem 1rem;
  }
</style>

<script>
import { gohistory } from '../../../js/common'
export default {
  name: 'materialDetail',
  data () {
    return {
      title: '外购物料入库申请',
      application_id: 0,
      listenersUid: [],
      loading: true,
      handleDialog: false,
      materialDetail: {},
      checks: [],
      zbi_handleUd: '',
      zbu_handleUd: '',
      authApproval: {
        type: '',
        num: '',
        memo: ''
      },
      rules: {
        type: [
          { required: true }
        ]
      },
      param: {}
    }
  },
  created () {
    let that = this
    let materialId = this.$route.params.id
    let storeParam = this.$store.getters.getMaterialParam
    this.param = storeParam
    console.log(storeParam)
    // vb-检验 zb-采购 wb-技术 xb-仓库
    switch (storeParam.user) {
      case 'zbc': // 来源于 检采购角色-外购物料入库申请（待检验 检验通过 检验不合格）
      case 'zbi': // 来源于 检采购角色-外购物料入库申请（待检验 待入库 数量异议待处理）
        this.title = '外购物料入库申请'
        break
      case 'vbc': // 来源于 检验角色-外购物料检验（待检验 检验通过 检验不合格）
        this.title = '外购物料检验'
        break
      case 'xb': // 来源于 仓库-外购物料入库（待入库 数量异议待确认）
        this.title = '外购物料入库'
        break
      case 'zbu': // 来源于 采购-不合格品处理（待处理 待让步审批 待入库 数量异议处理）
      case 'zbuOQ': // 来源于 检验角色-不合格品处理 该批订单质量情况（共 通过 不合格）
      case 'wb': // 来源于 技术-不合格品处理（无切换标签）
        this.title = '不合格品处理'
        break
      case 'query': // 来源于 查询
        this.title = '查询'
        break
      case 'msg': // 来源于 消息
        this.title = '我的消息'
        break
    }
    this.axios.post('../../../inStock/listDetail', {
      id: materialId
    }).then(function (response) {
      that.loading = false
      let detail = response.data.data
      let checks = response.data.checks
      console.log('detail', detail)
      if (detail) {
        that.materialDetail = detail[0]
      } else {
        that.$kiko_message('数据返回错误')
      }
      if (checks) {
        that.checks = checks
      }
    })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    authConfirm: function (state) {
      let that = this
      let param = {}
      if (state === 1) {
        if (this.authApproval.type === '') {
          this.$kiko_message('请选择判定结果！')
          return false
        }
        if (this.authApproval.type === '0' && this.authApproval.memo === '') {
          this.$kiko_message('请录入不合格的信息！')
          return false
        }
        this.loading = true
        param = {
          oid: this.sphdSocket.user.oid,
          itemId: this.$route.params.id, // 明细id
          userId: this.sphdSocket.user.userID, // 用户id
          resultType: this.authApproval.type, // 审批结果:1-通过,0-否决,为空时说明正在审批中',
          state: this.authApproval.type === '1' ? 3 : 4, // 审批结果:1-通过,0-否决,为空时说明正在审批中',
          unqualifiedInfo: this.authApproval.memo // 不合格信息
        }
        this.$http.post('../../../inStock/checks', param, {
          emulateJSON: true
        }).then((response) => {
          that.loading = false
          console.log('code', response.body.code)
          let code = response.body.code
          if (code === '200') {
            that.$kiko_message('审批成功')
            that.$router.go(-1)
          }
        }).catch(function () {
          // this.jumpError('系统错误，请重试！')
        })
      } else {
        switch (state) {
          // 2.来源为仓库待入库（审批数一致或不一致）
          case 2:
            if (this.authApproval.type === '') {
              this.$kiko_message('请选择审批结果！')
              return false
            }
            if (this.authApproval.type === '2' && (this.authApproval.num === '' || typeof this.authApproval.num === 'undefined')) {
              this.$kiko_message('请输入您清点后的数量！')
              return false
            }
            param = {
              operate: this.authApproval.type, // 1-数量一致正常入库  2-数量不一致输入数量 3：再次清点数量 4：数量确认 5：审批通过允许入库  6：审批不通过不允许入库 7:提出让步申请
              quantityType: this.authApproval.type === '1' ? 1 : 0, // '数量类型:1-数量一致,0-数量不致'
              approvalType: 1, // 1:正常审批 2：让步审批
              checkQuantity: this.authApproval.type === '1' ? '' : this.authApproval.num, // 实际清点数量
              state: this.authApproval.type === '1' ? 8 : 9 // 状态:1-采购录入,2-采购提交，3-检验审批通过 4，检验审批否决 5-让步申请 6-让步审批通过 7-让步审批否决 8仓库确认 9-仓库数量异议 A-采购确认'
            }
            break
          // 3.来源为仓库数量异议待处理（仓库清点数量，可多次提交）
          case 3:
            if (this.authApproval.num === '' || typeof this.authApproval.num === 'undefined') {
              this.$kiko_message('请输入实际清点数量！')
              return false
            }
            param = {
              operate: 3, // 1-数量一致正常入库 2-数量不一致输入数量 3：再次清点数量 4：数量确认 5：审批通过允许入库  6：审批不通过不允许入库 7:提出让步申请
              checkQuantity: this.authApproval.num, // 实际清点d数量
              state: 9 // 9:仓库数量异议
            }
            break
          // 4.来源为技术 （让步审批）
          case 4:
            if (this.authApproval.type === '') {
              this.$kiko_message('请选择审批结果！')
              return false
            }
            if (this.authApproval.type === '6' && this.authApproval.memo === '') {
              this.$kiko_message('请录入不可让步接受的理由！')
              return false
            }
            param = {
              operate: this.authApproval.type, // 5：审批通过允许入库 6：审批不通过不允许入库
              approvalType: 2, // 1:正常审批 2：让步审批
              state: this.authApproval.type === '5' ? 6 : 7, // 审批结果:6-让步审批通过,7-让步审批否决,为空时说明正在审批中'
              rejectReason: this.authApproval.memo // 不允许让步理由
            }
            break
          // 5.来源为采购数量异议待处理（对应3，清点数量采购确认）
          case 5:
            if (this.authApproval.type === '') {
              this.$kiko_message('请选择处理结果！')
              return false
            }
            param = {
              operate: 4, // 1-数量一致正常入库  2-数量不一致输入数量 3：再次清点数量 4：数量确认 5：审批通过允许入库  6：审批不通过不允许入库 7:提出让步申请
              approvalType: 1, // 1:正常审批 2：让步审批
              state: 'A' // 审批结果:A:采购确认'
            }
            break
          // 6.来源为采购不合格处理（提出让步申请）
          case 6:
            if (this.zbu_handleUd === '') {
              this.$kiko_message('请选择处理结果！')
              return false
            }
            param = {
              operate: 7, // 1-数量一致正常入库  2-数量不一致输入数量 3：再次清点数量 4：数量确认 5：审批通过允许入库  6：审批不通过不允许入库 7:提出让步申请
              state: 5, // 审批结果:5:让步申请'
              unqualified_handle: 'A' // 不合格品处理意见 需提出让步时传参。传递参数： A
            }
            break
        }
        this.loading = true
        this.approve(param)
      }
    },
    orderQuality: function () {
      var id = this.$route.params.id
      this.$router.push({
        path: `/orderQuality/${id}`
      })
    },
    handleUnqualified: function () {
      this.handleDialog = true
    },
    approve: function (param) {
      let params = param
      params.oid = this.sphdSocket.user.oid
      params.userId = this.sphdSocket.user.userID
      params.itemId = this.$route.params.id // 明细id
      console.log('approveParam', JSON.stringify(params))
      let that = this
      this.axios.post('../../../inStock/inStock', { json: JSON.stringify(param) }).then(function (response) {
        that.loading = false
        let code = response.data.code
        if (code === '200') {
          that.$kiko_message('操作成功')
          gohistory(that)
        }
        console.log('data', code)
      })
    }
  }
}
</script>
