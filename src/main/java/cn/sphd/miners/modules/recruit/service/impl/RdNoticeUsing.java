package cn.sphd.miners.modules.recruit.service.impl;

import cn.sphd.miners.modules.recruit.entity.RdNotice;
import cn.sphd.miners.modules.recruit.entity.RdNoticeHistory;
import cn.sphd.miners.modules.recruit.entity.RdResume;
import cn.sphd.miners.modules.recruit.service.RdNoticeService;
import cn.sphd.miners.modules.recruit.service.RdResumeService;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleImage;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleImageHistory;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleSupplementImage;
import cn.sphd.miners.modules.schedule.service.MemoScheduleHistoryService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleImageService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleSupplementService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class RdNoticeUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Long id;
    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);

            RdNoticeService rdNoticeService= ac.getBean(RdNoticeService.class, "rdNoticeService");
            switch (entityClass) {
                case "RdNotice":
                    RdNotice rdNotice = rdNoticeService.getRdNoticeById(id);
                    if (rdNotice != null) {
                        return filename.equals(rdNotice.getImagePath());
                    }
                    break;
                case "RdNoticeHistory":
                    RdNoticeHistory rdNoticeHistory=rdNoticeService.getRdNoticeHistoryById(id);
                    if(rdNoticeHistory != null) {
                        return  filename.equals(rdNoticeHistory.getImagePath());
                    }
                    break;
                case "RdResume":
                    RdResumeService rdResumeService=ac.getBean(RdResumeService.class, "rdResumeService");
                    RdResume rdResume=rdResumeService.getRdResumeById(id);
                    if(rdResume != null) {
                        return  filename.equals(rdResume.getFilePath());
                    }
                    break;
            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public RdNoticeUsing(Long id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }

    public RdNoticeUsing(){

    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }
}
