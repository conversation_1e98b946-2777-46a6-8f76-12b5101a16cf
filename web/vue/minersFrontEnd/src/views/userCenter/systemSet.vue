<template>
  <div class="systemSet">

    <table class="ty-table">
      <tr>
        <td>功能</td>
        <td>功能描述</td>
        <td>操作</td>
      </tr>
      <tr>
        <td>财务是否可以使用手机端</td>
        <td>启用意味着财务人员可使用手机端进行数据录入、编辑及查看等 </td>
        <td>
          <el-select v-model="switchOr" placeholder="请选择" @change="changeSwitch">
            <el-option label="启用"  :value="1" ></el-option>
            <el-option label="禁用"  :value="0" ></el-option>
          </el-select>
        </td>
      </tr>
    </table>

    <TyDialog v-if="switchVisible" :dialogTitle="'确认信息'" color="orange" :dialogHide="hideU" >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideU">取消</el-button>
        <el-button class="bounce-ok" @click="switchOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <span>确定要改变所有财务人员的登录状态？</span>
        </div>
      </template>
    </TyDialog>


  </div>
</template>
<script>
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import { getSystemSetStatus, updateloginstatus } from '@/api/userInfo.js'


export default {
  data() {
    return {
      pageName: 'systemSet',
      switchOr: 0 ,
      switchVisible:false,
    }
  },
  beforeRouteLeave(to, from, next) {
      beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'mf', name: '系统设置', pageName: this.pageName}, this.getThisSta, this)
  },
  destroyed () {
    console.log('beforeDestroy data', this.$data)
  },
  methods: {
    switchOk(){
      let data = { state: this.switchOr }
      updateloginstatus(data).then(res => {
        let data = res.data.data
        console.log('updateloginstatus res data=', data)
        this.switchVisible = false
        if(data == 1){
          this.$message.success('操作成功!')
        }
      }).catch(err => {
        console.log('err=', err)
        // this.$message.error('密码不能为空!')
        if(this.switchOr == 0){
          this.switchOr = 1
        }else{
          this.switchOr = 0
        }
      })

    },
    hideU(){
      this.switchVisible = false
      if(this.switchOr == 0){
        this.switchOr = 1
      }else{
        this.switchOr = 0
      }
    },
    getThisSta(){
      getSystemSetStatus().then(res => {
        let data = res.data.data
        console.log('getSystemSetStatus =', data)
        let financeLoginStatus = data.financeLoginStatus  // 财务登录状态
        let saleLoginStatus = data.saleLoginStatus // 销售登录状态
        this.switchOr = financeLoginStatus === 0 ? 0 : 1
        console.log('this.switchOr=', this.switchOr)

      }).catch(err => {

      })
    },
    changeSwitch(){
      console.log('changeSwitch=', this.switchOr)
      this.switchVisible = true

    }

  }
}

</script>

<style lang="scss" scoped>
.systemSet{
  padding: 50px;

}

</style>
