package cn.sphd.miners.modules.uploads.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.dto.*;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import cn.sphd.miners.modules.uploads.utils.UploadRootPath;
import com.alibaba.fastjson.JSON;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Created by 朱思旭 on 2021/1/23.
 */
@Controller
@RequestMapping("/uploads")
public class UploadFileController {
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    UploadService uploadService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    AuthService authService;

    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/getRootPath.do")
    public String getRootPath(HttpServletRequest request, String sessionid, User user, Integer userId, AuthAcc acc) {
        user = getUser(sessionid, user, userId);
        if(user == null && !AuthAcc.realAcc(acc)) {
            throw new IllegalArgumentException("用户不能为空!");
        }
        String webRoot = System.getProperty("BaseUrl");
        if (StringUtils.isEmpty(webRoot)) {
            webRoot = GetLocalIPUtils.getRootPath(request);
        }
        Map<String, String> result = new HashMap<>();
        result.put("webRoot", webRoot);//站点跟目录前缀
        String ow365url = (String) request.getAttribute("ow365url");
        String uploadUrl = (String) request.getAttribute("uploadUrl");
        String fileUrl = (String) request.getAttribute("fileUrl");
        Organization organization;
        UploadRootPath path = UploadRootPath.getInstance();
        if(user!=null) {
            if ((StringUtils.isEmpty(ow365url) || StringUtils.isEmpty(uploadUrl) || StringUtils.isEmpty(fileUrl))
                    && (organization = orgService.getByOid(user.getOid(),true,false)) != null
                    && StringUtils.isNotEmpty(organization.getUploadStorageType())) {
                if (StringUtils.isEmpty(ow365url)) {
                    ow365url = path.getOw365Path(request, organization);
                }
                if (StringUtils.isEmpty(uploadUrl)) {
                    uploadUrl = path.getUploadPath(request, organization);
                }
                if (StringUtils.isEmpty(fileUrl)) {
                    fileUrl = path.getFilePath(request, organization);
                }
            }
            result.put("ow365url", ow365url);//ow365预览前缀
            result.put("uploadUrl", uploadUrl);//同域下载前缀
            result.put("fileUrl", fileUrl);//文件服务器下载前缀
        } else if(AuthAcc.realAcc(acc)) {
            if ((StringUtils.isEmpty(ow365url) || StringUtils.isEmpty(uploadUrl) || StringUtils.isEmpty(fileUrl))) {
                if (StringUtils.isEmpty(ow365url)) {
                    ow365url = path.getOw365Path(request, acc);
                }
                if (StringUtils.isEmpty(uploadUrl)) {
                    uploadUrl = path.getUploadPath(request, acc);
                }
                if (StringUtils.isEmpty(fileUrl)) {
                    fileUrl = path.getFilePath(request, acc);
                }
            }
            result.put("ow365url", ow365url);//ow365预览前缀
            result.put("uploadUrl", uploadUrl);//同域下载前缀
            result.put("fileUrl", fileUrl);//文件服务器下载前缀
        }
        return JSON.toJSONString(result);
    }

    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/uploadfyByFile.do")
    public String uploadfyByFile(HttpServletRequest request, String sessionid, String token, AuthAcc acc, User user, @RequestParam("file") MultipartFile file, String module, Integer userId, String uploadUid, String groupUuid) {
        if (StringUtils.isEmpty(module)) {
            throw new IllegalArgumentException("module must not be null(模块不能为空)!");
        }
        if (file != null || file.getSize() > 0) {
            user = getUser(sessionid, user, userId);
            ResPathDto pathDto = new ResPathDto(request, user, acc);
            pathDto.mkdir();
            ResFileDto fileDto = new ResFileDto(file.getOriginalFilename());
            File toFile = new File(pathDto.getFullPath() + fileDto.getFileName());
            try {
                FileUtils.copyInputStreamToFile(file.getInputStream(), toFile);//如果扩展名属于允许上传的类型，则创建文件
            } catch (IOException e) {
                Logger.getLogger(getClass()).error("/upload/uploadfyByFile.do IOException：" + pathDto.getFullPath() + fileDto.getFileName(), e);
            }
            UploadFile uploadFile = uploadService.createOrUpdateUploadFile(fileDto, pathDto, toFile, module, groupUuid, user, acc, null);
            uploadFile.setFileUid(StringUtils.isNotEmpty(uploadUid) ? uploadUid : UUID.randomUUID().toString().replace("-",""));
            LogDto logDto = new LogDto(request, "普通上传");
            uploadService.saveUploadFile(uploadFile, user, acc, logDto);
            redisTemplate.opsForValue().set("miners:uploadfyByFile:fileUid:"+sessionid+uploadFile.getFileUid(), uploadFile.getFilename(), 3, TimeUnit.DAYS);
            addGroupUUID(uploadFile, null, sessionid);
            String result = JSON.toJSONString(uploadFile);
            System.out.println("uploadfyByFile.do\n"+JSON.toJSONString(uploadFile));
            return result;
        }
        throw new IllegalArgumentException("文件大小不能为零");
    }

    /**
     * <AUTHOR>
     * @description kendo chunks 上传后端。
     * @since 2021/4/25 9:42
     * @method uploadfyBykendo
     * @param request
     * @param sessionid
     * @param user
     * @param file
     * @param metadata
     * @param module
     * @param userId
     * @param groupUuid
     * @return: java.lang.String
     **/
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/uploadfyByKendo.do")
    public String uploadfyByKendo(HttpServletRequest request, HttpServletResponse response, String sessionid, AuthAcc acc, User user, @RequestParam("file") MultipartFile file, String metadata, String module, Integer userId, String groupUuid) {
        //wyu：20200404 选择文件上传后，按目前需求，前端25分钟定时请求"/sys/refreshLogoutTime.do"，无须此处再延时。
//        //wyu：延长无操作注销用户时间，差5分钟（25分钟）重设
//        if((long)redisTemplate.opsForValue().get("miners:logout:"+session.getId())+TimeUnit.MINUTES.toMillis(25) < System.currentTimeMillis()) {
//            redisTemplate.opsForValue().set("miners:logout:" + session.getId(), System.currentTimeMillis(), 35, TimeUnit.MINUTES);
//        }
        if (StringUtils.isEmpty(module)) {
            throw new IllegalArgumentException("module must not be null(模块不能为空)!");
        }
        MetaDataDto metaDto = MetaDataDto.parse(metadata);
        String pathDtoStr;
        ResPathDto pathDto;
        String pathDtoKey = "miners:pathDto:" + sessionid + metaDto.getUploadUid();
        if (StringUtils.isNotEmpty(pathDtoStr = (String) redisTemplate.opsForValue().get(pathDtoKey))) {
            pathDto = JSON.parseObject(pathDtoStr, ResPathDto.class);
        } else {
            user = getUser(sessionid, user, userId);
            pathDto = new ResPathDto(request, user, acc);
            redisTemplate.opsForValue().set(pathDtoKey, JSON.toJSONString(pathDto), 7, TimeUnit.DAYS);//ResPathDto缓存7天，单文件上传时间不应超过7天
        }
        ChunkUploadDto chunk = new ChunkUploadDto(pathDto, metaDto, file, sessionid, redisTemplate);
        ResFileDto fileDto = chunk.saveChunk();
//        System.out.println("/uploadfyByKendo.do chunk.saveChunk() ResFileDto: " + JSON.toJSONString(fileDto));
        UploadFile uploadFile = new UploadFile();
        uploadFile.setFileUid(chunk.getMetaDto().getUploadUid());
        uploadFile.setMinMissIndex(fileDto.getMinMissIndex());
        if (uploadFile.getMinMissIndex() >= -1L) {//wyu：上传未完成
            uploadFile.setUploaded(Boolean.FALSE);
        } else {//wyu：上传完成，返回文件路径和扩展名。
            redisTemplate.delete(pathDtoKey);//上传完成，删除ResPathDto缓存
            uploadFile.setUploaded(Boolean.TRUE);
            user = getUser(sessionid, user, userId);
            uploadFile = uploadService.createOrUpdateUploadFile(fileDto, pathDto, new File(pathDto.getFullPath() + fileDto.getFileName()), module, groupUuid, user, acc, uploadFile);
            uploadFile.setRelativePath(fileDto.getRelativePath());
            uploadFile.setOriginalRelativePath(chunk.getMetaDto().getRelativePath());
            LogDto logDto = new LogDto(request, "Kendo上传");
            uploadService.saveUploadFile(uploadFile, user, acc, logDto);
            addGroupUUID(uploadFile, chunk.getMetaDto(), sessionid);
        }
        if(StringUtils.isNotEmpty(response.getHeader("token"))) {
            uploadFile.setToken(response.getHeader("token"));
        }
        return JSON.toJSONString(uploadFile);
    }

    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/removeByKendo.do")
    public void removeByKendo(HttpServletRequest request, String sessionid, User user, AuthAcc acc, String fileNames, Integer userId) {
        user = getUser(sessionid, user, userId);
        String rootPath = ResPathDto.getRootPath(request);
        LogDto logDto = new LogDto(request, "Kendo上传页面点击删除");
        delFileKendo(fileNames, user, acc, rootPath, sessionid, logDto);
    }

    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/removeFilesByKendo.do")
    public void removeFilesBykendo(HttpServletRequest request, String sessionid, User user, AuthAcc acc, String fileNamesList, Integer userId) {
        List<String> fileNames = JSON.parseArray(fileNamesList, String.class);
        LogDto logDto = new LogDto(request, "Kendo上传页面未提交刷新批量删除");
        user = getUser(sessionid, user, userId);
        String rootPath = ResPathDto.getRootPath(request);
        for (String filename : fileNames) {
            delFileKendo(filename,user, acc,rootPath,sessionid,logDto);
        }
    }
    private void delFileKendo(String filename,User user, AuthAcc acc,String rootPath,String sessionId,LogDto logDto) {
        filename = ChunkUploadDto.removeUpload(sessionId, filename, redisTemplate);
        if(StringUtils.isNotEmpty(filename)) {
            filename = filename.replace(rootPath, "").replace(ResPathDto.separator, "/");
            UploadFile uploadFile = uploadService.getUploadFile(filename);
            if(uploadFile!=null) {
                uploadService.delUploadFile(uploadFile, user, acc, logDto);    //删除数据库中的数据
            }
        }
    }

    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/removeFilesByGroup.do")
    public void removeFilesByGroup(HttpServletRequest request, String sessionid, User user, AuthAcc acc, String groupUuid, Integer userId) {
        if(StringUtils.isNotEmpty(groupUuid)) {
            LogDto logDto = new LogDto(request, "多文件/文件夹上传页面未提交按groupUuid批量删除");
            user = getUser(sessionid, user, userId);
            String key = "miners:upload:group:" + groupUuid;
            List list = redisTemplate.opsForList().range(key, 0, -1);
            redisTemplate.delete(key);
            String rootPath = ResPathDto.getRootPath(request);
            for (Object value : list) {
                String fileUid = (String) value;
                if (fileUid.indexOf('\0') != -1) {
                    delFileKendo(fileUid, user, acc, rootPath, sessionid, logDto);
                } else {
                    delByFileuid(fileUid, user, acc, rootPath, sessionid, logDto);
                }
            }
        }
    }

    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/removeByFile.do")
    public void removeByFile(HttpServletRequest request, String sessionid, User user, AuthAcc acc, String fileUid, Integer userId) {
        if (StringUtils.isNotEmpty(fileUid)) {
            user = getUser(sessionid, user, userId);
            LogDto logDto = new LogDto(request, "普通上传页面点击删除");
            String rootPath = ResPathDto.getRootPath(request);
            delByFileuid(fileUid, user, acc, rootPath, sessionid, logDto);
        }
    }

    private final User getUser(String sessionid, User user, Integer userId) {
        if (user == null && userId != null && sessionid!=null) {
            user = userService.getUserByID(userId);
        }
        return user;
    }

    private void delByFileuid(String fileUid,User user, AuthAcc acc,String rootPath, String sessionid ,LogDto logDto) {
        String filename = (String) redisTemplate.opsForValue().get("miners:uploadfyByFile:fileUid:"+sessionid+fileUid);
        if(StringUtils.isNotEmpty(filename)) {
            UploadFile uploadFile = uploadService.getUploadFile(filename);
            if(uploadFile!=null
                && uploadFile.getCreateDate().getTime()+TimeUnit.DAYS.toMillis(4)>System.currentTimeMillis()//四天内上传，未进垃圾站不能删除
                && (uploadFile.getUserId()!=null&&uploadFile.getUserId().equals(user.getUserID())//机构本人上传
                    || uploadFile.getAccId()!=null && uploadFile.getAccId().equals(acc.getId()))//领地本人上传
                && !uploadService.checkUploadFileByFilename(filename)//文件还未被使用
            ) {
                String path = rootPath + uploadFile.getRealPath();
                new File(path).delete();
                uploadService.delUploadFile(uploadFile, user, acc, logDto);    //删除数据库中的数据
            }
        }
    }

    private void addGroupUUID(UploadFile uploadFile, MetaDataDto metaDto, String keyid) {
        if (StringUtils.isNotEmpty(uploadFile.getGroupUuid())) {
            String key = "miners:upload:group:" + uploadFile.getGroupUuid();
            if (metaDto == null) {
                redisTemplate.opsForList().leftPush(key, uploadFile.getFileUid());
            } else {
                redisTemplate.opsForList().leftPush(key, metaDto.getRelativePath() + '\0' + metaDto.getUploadUid());
            }
            redisTemplate.expire(key, 3, TimeUnit.DAYS);
            List list = redisTemplate.opsForList().range(key, 0, -1);
            for (Object value : list) {
                String filename = (String) value;
                String[] keys = filename.split("\0");
                if (keys.length == 2) {
                    key = "miners:chunkUpload:uploadPathByRelativePath:" + keyid + ":" + keys[0];
                    redisTemplate.expire(key, 3, TimeUnit.DAYS);
                    key = "miners:chunkUpload:uploadPathByUploadUid:" + keyid + ":" + keys[1];
                    redisTemplate.expire(key, 3, TimeUnit.DAYS);
                } else {
                    key = "miners:uploadfyByFile:fileUid:"+keyid+filename;
                    redisTemplate.expire(key, 3, TimeUnit.DAYS);
                }
            }
            uploadService.updateCreateDateByGroupUuid(uploadFile.getGroupUuid(), uploadFile.getCreateDate());
//            redisTemplate.execute(new SessionCallback() {
//                @Nullable
//                @Override
//                public Object execute(RedisOperations operations) throws DataAccessException {
//                    Long now = uploadFile.getCreateDate().getTime();
//                    String grouptimeKey = "miners:upload:grouptime:" + uploadFile.getGroupUuid();
//                    Long createDate = (Long) operations.opsForValue().get(grouptimeKey);
//                    if (createDate == null) {//第一个文件
//                        operations.opsForValue().set(grouptimeKey, now, 3 ,TimeUnit.DAYS);
//                    } else if (now - createDate > TimeUnit.DAYS.toMillis(1)) {//最早的文件上传完成已经超过1天
//                        uploadService.updateCreateDateByGroupUuid(uploadFile.getGroupUuid(), new Date(now));
//                        operations.opsForValue().set(grouptimeKey, now, 3 ,TimeUnit.DAYS);
//                    }
//                    return null;
//                }
//            });
        }
    }
}