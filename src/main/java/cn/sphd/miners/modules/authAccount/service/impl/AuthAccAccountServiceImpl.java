package cn.sphd.miners.modules.authAccount.service.impl;


import cn.sphd.miners.common.initializer.DsEcSku;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.authAccount.dao.AuthAccAccountDao;
import cn.sphd.miners.modules.authAccount.dao.AuthAccAccountHistoryDao;
import cn.sphd.miners.modules.authAccount.dao.AuthAccMdseDao;
import cn.sphd.miners.modules.authAccount.dao.AuthAccValidPeriodDao;
import cn.sphd.miners.modules.authAccount.entity.AuthAccAccount;
import cn.sphd.miners.modules.authAccount.entity.AuthAccAccountHistory;
import cn.sphd.miners.modules.authAccount.entity.AuthAccMdse;
import cn.sphd.miners.modules.authAccount.entity.AuthAccValidPeriod;
import cn.sphd.miners.modules.authAccount.service.AuthAccAccountService;
import cn.sphd.miners.modules.ec.dao.EcOrderDao;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.ec.entity.EcOrderDetail;
import cn.sphd.miners.modules.ec.entity.EcSku;
import cn.sphd.miners.modules.ec.service.DsService;
import cn.sphd.miners.modules.ec.service.EcService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("authAccAccountService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class AuthAccAccountServiceImpl implements AuthAccAccountService, DsService {

    @Autowired
    AuthAccAccountDao authAccAccountDao;
    @Autowired
    AuthAccAccountHistoryDao authAccAccountHistoryDao;
    @Autowired
    AuthAccMdseDao authAccMdseDao;
    @Autowired
    AuthAccValidPeriodDao authAccValidPeriodDao;
    @Autowired
    EcOrderDao ecOrderDao;

    @Autowired
    EcService ecService;
    @Autowired
    AuthService authService;

    @Override
    public AuthAccMdse getAuthAccMdse() {
        StringBuffer hql = new StringBuffer(" from AuthAccMdse");
        AuthAccMdse authAccMdse = (AuthAccMdse) authAccMdseDao.getByHQLWithNamedParams(hql.toString(),null);
        return authAccMdse;
    }

    @Override
    @DsEcSku(serviceName = serviceName)
    public AuthAccMdse addAuthAccMdse(User user, AuthAccMdse authAccMdse) {
        authAccMdse = new AuthAccMdse();
        authAccMdse.setName("私人领地套餐");
        authAccMdse.setPrice(BigDecimal.valueOf(1));
        authAccMdse.setUnit(Byte.valueOf("1"));
        authAccMdse.setAmount(Long.valueOf("9223372036854775807"));
        authAccMdse.setCreator(user.getUserID());
        authAccMdse.setCreateName(user.getUserName());
        authAccMdse.setCreateTime(new Date());
        authAccMdseDao.save(authAccMdse);
        return authAccMdse;
    }


    @Override
    public AuthAccMdse upAuthAccMdse(AuthAccMdse authAccMdse) {
        AuthAccMdse mdse = authAccMdseDao.get(authAccMdse.getId());
        mdse.setAmount(authAccMdse.getAmount());
        mdse.setUnit(authAccMdse.getUnit());
        mdse.setPrice(authAccMdse.getPrice());
        mdse.setExpiredTime(authAccMdse.getExpiredTime());
        return mdse;
    }

    @Override
    public Map<String, Object> getOpenAccountMes(AuthInfoDto authInfo) {
        AuthAccMdse mdse = this.getAuthAccMdse();
        Integer state = 0;
        Map<String, Object> map = new HashMap<>();
        if (mdse != null) {
            AuthAccAccount account = this.getAccountSingle(authInfo.getAccId());
            if (account != null) {
                this.accountFillEcId(account, mdse);
                if (account.getSkuId() != null && account.getExp() != null) {
                    state = this.accountFillMes(authInfo, account, account.getExp(), "2");
                } else {
                    state = this.accountFillMes(authInfo, account,null, "1");
                }
            } else {
                state = 1;
                Date trialEndDate = NewDateUtils.changeDay(NewDateUtils.today(), 15);
                account = new AuthAccAccount();
                account.setTrailEndDate(trialEndDate);
                account.setAnnualFee(mdse.getPrice());
                account.setSpaceFee(BigDecimal.valueOf(1));
                account.setSpaceAllocation(Long.valueOf(10));
            }
            account.setTrailDays(15);
            map.put("account", account);
        }
        map.put("state", state);
        return map;
    }

    @Override
    public AuthAccAccount addAuthAccAccount(AuthInfoDto authInfo) {
        AuthAccMdse mdse = this.getAuthAccMdse();
        AuthAccAccount account = this.getAccountSingle(authInfo.getAccId());
        if (account == null) {
            account = new AuthAccAccount();
            account.setAccId(authInfo.getAccId());
            account.setAnnualFee(mdse.getPrice());
            account.setSpaceFee(BigDecimal.valueOf(1));
            account.setSpaceAllocation(Long.valueOf(10));
            account.setApplyTime(new Date());
            account.setCreateName(authInfo.getName());
            account.setCreateDate(account.getApplyTime());
            authAccAccountDao.save(account);
            this.accountFillEcId(account, mdse);
            AuthAccAccountHistory authAccAccountHistory = new AuthAccAccountHistory();
            BeanUtils.copyPropertiesIgnoreNull(account, authAccAccountHistory);
            authAccAccountHistory.setAccAccount(account.getId());
            authAccAccountHistory.setId(null);
            authAccAccountHistoryDao.save(authAccAccountHistory);
        }
        return account;
    }

    @Override
    public AuthAccAccount countFactFee(AuthInfoDto authInfo) {
        AuthAccMdse mdse = this.getAuthAccMdse();
        AuthAccAccount account = this.getAccountSingle(authInfo.getAccId());
        this.accountFillEcId(account, mdse);
        this.accountFillMes(authInfo, account, null, "1");
        account.setFactFee(BigDecimal.valueOf(10));
        account.setDiscount(BigDecimal.valueOf(9));
        account.setDiscountTime(new Date());
        return account;
    }

    @Override
    public List<AuthAccValidPeriod> getAuthAccValidPeriodList(AuthInfoDto authInfo) {
        StringBuffer hql = new StringBuffer(" from AuthAccValidPeriod where accId =:accId");
        Map<String, Object> params = new HashMap<>(1){{
            put("accId", authInfo.getAccId());
        }};
        List<AuthAccValidPeriod> list = authAccValidPeriodDao.getListByHQLWithNamedParams(hql.toString(),params);
        if (!list.isEmpty()) {
            AuthAccAccount account = this.getAccountSingle(authInfo.getAccId());
            for (AuthAccValidPeriod p : list) {
                EcOrder ecOrder = ecOrderDao.get(p.getOrderDetailId());
                p.setFactFee(ecOrder.getPaid());
                p.setAnnualFee(ecOrder.getGoodsAmount());
                p.setSpaceFee(account.getSpaceFee());
                p.setSpaceAllocation(account.getSpaceAllocation());
                p.setDiscount(BigDecimal.valueOf(9));
            }
        }
        return list;
    }

    @Override
    public AuthAccAccount getAccountSingle(Long accId) {
        StringBuffer hql = new StringBuffer(" from AuthAccAccount where accId = :accId");
        Map<String, Object> param = new HashMap(){{
            put("accId", accId);
        }};
        AuthAccAccount account = (AuthAccAccount) authAccAccountDao.getByHQLWithNamedParams(hql.toString(),param);
        return account;
    }

    @Override
    public Date getAuthExp(AuthInfoDto authInfo, Date now) {
        if(!authInfo.logged()) {
            return null;
        }
        String first = "select 1";
        String second = " from AuthAccValidPeriod where accId =:accId and ";
        String third = "validFrom <= :now and validThru > :now";
        StringBuffer hql = new StringBuffer(first).append(second).append(third);
        Map<String, Object> params = new HashMap<>();
        params.put("accId", authInfo.getAccId());
        params.put("now", now);
        if (authAccValidPeriodDao.getByHQLWithNamedParams(hql.toString(), params) != null) {
            String newFirst = "select max(validThru)";
            hql.replace(0, first.length(), newFirst).replace(newFirst.length() + second.length(), newFirst.length() + second.length() + third.length(), "validThru >= :now");
            return  (Date) authAccValidPeriodDao.getByHQLWithNamedParams(hql.toString(), params);
        } else {
            return null;
        }
    }



    @Override
    public void SaveToEc(String mathodName, Map<String, Object> params, Object result) {
        User user = getCurrentUser(params);
        AuthAccMdse record = (AuthAccMdse) result;
        EcSku oldSku = ecService.getSkuByDs(serviceName, record.getId().toString());
        Integer sMaxMun = 100;
        EcSku newSku=new EcSku(null, "私人领地", null, null, defaultMchid, EcService.SkuType.virtual, EcService.SkuStatus.online, record.getPrice(), Long.valueOf(record.getAmount()), null, 0L, record.getExpiredTime(), Short.valueOf(record.getUnit()), 1, sMaxMun, module, serviceName, record.getId().toString(), user);
        ecService.addOrUpdateSkuAndSpu(oldSku,newSku);
    }

    @Override
    public void lockStockByEc(EcSku sku, Long value) {

    }

    @Override
    public void soldByEc(EcSku sku, EcOrderDetail orderDetail, Long value, AuthInfoDto authInfo) {
        if (0L != value) {
            AuthAccMdse authAccMdse = this.deducteStock(sku, value);
            this.createValidPeriod(authAccMdse, sku, orderDetail, value, authInfo);
            ecService.updateOrderDetailShipStatus(orderDetail, EcService.ShippingStatus.received);
        }
    }

    @Override
    public void returnByEc(EcSku sku, EcOrderDetail orderDetail, Long value, AuthInfoDto authInfo) {
        if(0L != value) {
            this.returnStock(sku);
        }
    }

    @Override
    public boolean checkRefund(EcOrderDetail detail, AuthInfoDto authInfo, Date now) {
        boolean result = false;
        authInfo = SerializationUtils.clone(authInfo);
        authInfo.setSessionId(null);
        if (authInfo != null && ecService.getAuthInfoByOrder(detail.getOrder()).compareTo(authInfo) == 0) {
            return this.checkAccValidPeriod(detail, now) !=null;
        }
        return result;
    }

    //扣减商品表的库存
    private AuthAccMdse deducteStock(EcSku sku, Long value) {
        AuthAccMdse authAccMdse = this.getAuthAccMdseSingle(Long.valueOf(sku.getDsKey()));
        Map<String, Object> params = new HashMap<String, Object>(2) {{
            put("amount", 0L + value);
        }};
        return authAccMdseDao.incAttr(authAccMdse, params);
    }

    AuthAccMdse getAuthAccMdseSingle(Long id) {
        AuthAccMdse authAccMdse = authAccMdseDao.get(id);
        return authAccMdse;
    }

    private void createValidPeriod(AuthAccMdse authAccMdsev, EcSku sku, EcOrderDetail orderDetail, Long value, AuthInfoDto authInfo) {
        Integer num = authAccMdsev.getExpiredTime() == null || value == null ? 1 : (int)(authAccMdsev.getExpiredTime()*value);
        EcService.CTpye ctype = EcService.CTpye.getByIndex(Short.valueOf(authAccMdsev.getUnit()));
        Date now = new Date(System.currentTimeMillis());
        Date oldExp;
        if ((oldExp = this.getAuthExp(authInfo, now)) == null) {
            oldExp = now;
        }
        Date newExp = EcService.CTpye.getExp(oldExp, num, ctype);
        AuthAccValidPeriod authAccValidPeriod = new AuthAccValidPeriod(orderDetail.getId(), authInfo, authAccMdsev, num, ctype, oldExp, newExp, (byte)1);
        authAccValidPeriodDao.save(authAccValidPeriod);
    }

    private AuthAccMdse returnStock(EcSku sku) {
        AuthAccMdse authAccMdse = this.getAuthAccMdseSingle(sku.getId());
        return authAccMdse;
    }

    private AuthAccValidPeriod checkAccValidPeriod(EcOrderDetail orderDetail, Date now){
        StringBuffer hql = new StringBuffer(" from AuthAccValidPeriod where orderDetailId=:orderDetailId and validFrom>:now and status=:status");
        Map<String, Object> params = new HashMap<String, Object>(3){{
            put("orderDetailId", orderDetail.getId());
            put("now", now);
            put("status", (byte)1);
        }};
        AuthAccValidPeriod authAccValidPeriod = (AuthAccValidPeriod) authAccValidPeriodDao.getByHQLWithNamedParams(hql.toString(), params);
        return authAccValidPeriod;
    }

    //填入skuid
    private void accountFillEcId(AuthAccAccount account, AuthAccMdse mdse){
        EcSku sku = ecService.getSkuByDs(serviceName, mdse.getId().toString());
        if (sku != null) {
            account.setSkuId(sku.getId());
            this.accountFillValid(account, mdse);
        }
    }

    //填有效期
    private void accountFillValid(AuthAccAccount account , AuthAccMdse mdse){
        AuthInfoDto authInfo = authService.getAuthInfoFromToken(authService.getTokenFromDefaultResponseRequest());
        if (authInfo != null) {
            account.setExp(this.getAuthExp(authInfo, new Date(System.currentTimeMillis())));
        }
    }

    //填试用结束以及一些其它内同
    private Integer accountFillMes(AuthInfoDto authInfo, AuthAccAccount account, Date exp, String type){
        Integer state = 0;
        Date today = NewDateUtils.today();
        Date trialEndDate = NewDateUtils.changeDay(account.getApplyTime(), 15);
        Date validFrom = null;
        Date validThru = null;
        if ("1".equals(type)) {
            account.setTrailEndDate(trialEndDate);
            if (today.compareTo(trialEndDate) > 0) {
                state = 3;                              //未付费且超过试用期
                validFrom = today;
            } else {
                state = 2;                              //未付费但是没有超过试用期
                validFrom = NewDateUtils.changeDay(account.getApplyTime(), 16);
            }
            validThru = NewDateUtils.changeDay(validFrom,365);
        } else {
            if (today.compareTo(exp) > 0) {
                state = 4;                              //使用时间以过期
                validFrom = today;
                validThru = NewDateUtils.changeDay(validFrom,365);
            } else {
                Date threeDaysAgo = NewDateUtils.changeDay(exp, -3);
                if (today.compareTo(threeDaysAgo) > 0) {
                    state = 5;                         //还有3天过期
                    validFrom = NewDateUtils.changeDay(exp, 1);
                    validThru = NewDateUtils.changeDay(validFrom,365);
                } else {
                    state = 6;                         //在使用期间内
                    AuthAccValidPeriod authAccValidPeriod = this.getauthAccValidPeriodByEXp(authInfo,exp);
                    validFrom = authAccValidPeriod.getValidFrom();
                    validThru = authAccValidPeriod.getValidThru();
                    account.setPayTime(authAccValidPeriod.getCreateTime());
                    account.setFactFee(authAccValidPeriod.getFactFee());
                    account.setNextYearValidFrom(NewDateUtils.changeDay(validThru, 1));
                    account.setNextYearvalidThru(NewDateUtils.changeDay(validThru, 366));
                }
            }
        }
        account.setValidFrom(validFrom);
        account.setValidThru(validThru);
        return state;
    }

    //根据exp和accI获取有效期记录
    private AuthAccValidPeriod getauthAccValidPeriodByEXp(AuthInfoDto authInfo, Date exp){
        StringBuffer hql = new StringBuffer(" from AuthAccValidPeriod where accId =:accId and validThru = :exp");
        Map<String, Object> params = new HashMap<>();
        params.put("accId", authInfo.getAccId());
        params.put("exp", exp);
        AuthAccValidPeriod authAccValidPeriod = (AuthAccValidPeriod) authAccValidPeriodDao.getByHQLWithNamedParams(hql.toString(), params);
        return authAccValidPeriod;
    }
}
