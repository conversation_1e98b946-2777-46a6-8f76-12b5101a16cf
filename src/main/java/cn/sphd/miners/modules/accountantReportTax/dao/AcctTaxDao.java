package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDetailDto;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.util.List;

public interface AcctTaxDao extends IBaseDao<AcctTaxEntity,Serializable> {
    void setDisenabled(User user,Integer reportId);

    void updateHisTaxName(int taxId, String name);

    AcctTaxDetailDto getTaxDetailDto(User user, int taxId,int reportId);

    boolean checkTaxName(String name, Integer org);

    boolean isExsitSystemTax(Integer org);

    List<AcctTaxEntity> getListByOrg(Integer org);

    Integer getTaxId(byte type, Integer org);
}
