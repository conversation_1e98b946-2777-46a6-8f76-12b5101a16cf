import {
    addcamadn,
    catrecordn,
    detcatet,
    getcatmsg,
    getchildcat,
    restorecho,
    stopnusern,
    stoppened,
    upcatmsgsure
} from "@/api/equipMent";
import {ref} from "vue";
import {ElMessage} from "element-plus";

export default {
    name:'',
    components:{},
    setup() {
        // const jsonObj = {};
        // let counter = 0;
        // const t = ref(1); // 假设这是你的条件变量，你可以根据实际需要修改它的值
        // function addProperty() {
        //     if (t.value === 1) {
        //         counter++;
        //         const newPropName = `a${counter}`;
        //         jsonObj[newPropName] = `Value of ${newPropName}`;
        //     }
        // }
        return {
            // jsonObj,
            // addProperty,
            // text1: ref(''),
            // text2: ref(''),
            // text3: ref(''),
            // text4: ref(''),
            // input1: ref(''),
            // input2: ref(''),
        }
    },
    data() {
        return {
            catgory: 0,
            listc1: [],
            listc2: [],
            listbox1: {},
            listbox2: {},
            tableData1: [],
            tableData2: [],
            tableData3: [],
            tableData4: [],
            delVisiblenc1: null,
            delVisiblenc2: null,
            delVisiblenc3: null,
            delVisiblenc4: null,
            delVisiblenc5: null,
            delVisiblenc6: null,
            delVisiblenc7: null,
            delVisiblenc8: null,
            delVisiblenc9: null,
            delVisiblenc10: null,
            updent: 0,//针对一级类别
            updent2: 0,//针对子类别
            stoptent: 0,
            prestrt: 0,
            detprt: 0,
            hfpt: 0,
            mid1: null,
            mid2: null,
            kept: 0,
            detpont: 0,
            catlnk: 0,
            catad: null,
            catname: '',
            text1: '',
            text2: '',
            text3: '',
            text4: '',
            text5: '',
            text6: '',
            text7: '',
            text8: '',
            input1: '',
            input2: '',
            input3: '',
            input4: '',
            parentid: '',
            meen: '',
            idnamlink: [],//用于存储获取到的id和name
            bclast: '',//区分是回到首页还是回到上一页
            //fabox: {},//父级id+子级id+父级的name
            //lastname: '',//上一级的名字
            //counter: 0,
            //jsonObj: {}
            // nextname: ''
        }
    },
    created(){},
    //页面刷新后立即执行的函数
    mounted(){
        const json1 = { 'enabled': 1,'parent': 0};
        this.getCategon(json1);
    },
    computed(){},
    methods: {
        pageClick(pageItem){
            let data1 = {
                pageSize: 20,
                currentPageNo: pageItem.page
            };
            this.initCreatedaCat(data1);
        },
        initCreatedaCat(){
            let params = { 'currentPageNo':1,'pageSize':20,'totalPage':15 };
            let data2 = {
                'enabled': 1,
                'parent': 0
            };
            this.getCategon(data2);
        },

        hideFunc1(){
            this.delVisiblenc1 = false;
        },
        hideFunc2(){
            this.delVisiblenc2 = false;
        },
        hideFunc3(){
            this.delVisiblenc3 = false;
        },
        hideFunc4(){
            this.delVisiblenc4 = false;
        },
        hideFunc5(){
            this.delVisiblenc5 = false;
        },
        hideFunc6(){
            this.delVisiblenc6 = false;
        },
        hideFunc7(){
            this.delVisiblenc7 = false;
        },
        hideFunc8(){
            this.delVisiblenc8 = false;
        },
        hideFunc9(){
            this.delVisiblenc9 = false;
        },
        hideFunc10(){
            this.delVisiblenc10 = false;
        },
        getCategon(data2){
            getcatmsg(data2).then(res => {
                console.log('res现有的数据',res);
                const lank1 = res.data;
                console.log('lank1现有的数据',lank1);
                this.listc1 = lank1;
                this.tableData1 = lank1;
            })
            //、、this.delVisiblenc9 = false;
        },
        gotunback2(typn2){
            this.catgory = 0;
            const json1 = {'enabled': 1,'parent': 0};
            this.getCategon(json1);
        },
        gotunback(typn){
            if(typn === 1){
                //debugger;
                if(this.idnamlink.length === 1){
                    this.catgory = 0;
                    //需要调用获取首页列表数据的函数
                    const json1 = {
                        'enabled':1,
                        'parent': 0
                    };
                    this.getCategon(json1);//parent=0时是回到首页。
                    this.idnamlink = [];
                }else if(this.idnamlink.length > 1){
                    //console.log(this.idnamlink);
                    let json1 = {'enabled': 1,'parent': ''};
                    this.idnamlink.forEach((item,index) => {
                        if(index === this.idnamlink.length-2){
                            json1.parent = item.id;
                            this.listc2 = [];
                            getchildcat(json1).then(res => {
                                const link1 = res.data;
                                for(let t1 = 0;t1<link1.length;t1++){
                                    link1[t1].childrens = link1[t1].childrens+'个';
                                }
                                this.tableData3 = link1;
                                const jsonc1 = {'catname': item.name,'longer': res.data.length};
                                this.listc2.push(jsonc1);
                            })
                            this.idnamlink.splice(this.idnamlink.length-1,1);
                            console.log('idnamlink-',this.idnamlink);
                        }
                    })
                }
                // if(this.fabox.fb === 0){
                //     this.catgory = 0;
                //     //需要调用获取首页列表数据的函数
                //     const json1 = {
                //         'enabled':1,
                //         'parent': 0
                //     };
                //     this.getCategon(json1);//parent=0时是回到首页。
                // }else{
                //     console.log(this.fabox);
                //     const json1 = {
                //         'enabled': 1,
                //         'parent': this.fabox.fb
                //     };
                //     this.listc2 = [];
                //     getchildcat(json1).then(res => {
                //         this.catname = this.fabox.fname;
                //         const link1 = res.data;
                //         for(let t1 = 0;t1<link1.length;t1++){
                //             link1[t1].childrens = link1[t1].childrens+'个';
                //         }
                //         this.tableData3 = link1;
                //         // const pd = this.fabox.n;
                //         // const fb = this.fabox.pd;
                //         // const cb = this.fabox.fb;
                //         // debugger;
                //         //const nextname = null;
                //         // const fname = this.fabox.lname;//3.1
                //         // const lname = this.fabox.nextname;//2.1
                //         // this.fabox.n = 0;this.fabox.pd = pd;this.fabox.fb = fb;
                //         // this.fabox.cb = cb;this.fabox.fname = fname;
                //         // this.fabox.lname = lname;
                //         // const jsonc1 = {
                //         //     'catname': this.fabox.fname,
                //         //     'longer': res.data.length
                //         // };
                //         // this.listc2.push(jsonc1);
                //         //fname-3.1
                //         // this.fabox = {
                //         //     n:0,pd:pd,fb:fb,cb:cb,fname:this.fabox.lname,
                //         // };
                //     })
                // }
                //console.log(this.fabox);
                // if(this.fabox.pd === 0){
                //     if(this.fabox.fname === undefined){
                //         this.catgory = 0;
                //         //需要调用获取首页列表数据的函数
                //         const json1 = {
                //             'enabled':1,
                //             'parent': 0
                //         };
                //         this.getCategon(json1);//parent=0时是回到首页。
                //     }else{
                //         const json1 = {
                //             'enabled': 1,
                //             'parent': this.fabox.fd
                //         };
                //         this.listc2 = [];
                //         getchildcat(json1).then(res1 => {
                //             console.log('res1现在的数据',res1);
                //             const jsonc1 = {
                //                 'catname': this.fabox.fname,
                //                 'longer': res1.data.length
                //             };
                //             this.listc2.push(jsonc1);
                //             console.log('this.listc2现在的值',this.listc2);
                //             this.catname = this.fabox.fname;
                //             const link1 = res1.data;
                //             for(let t1 = 0;t1<link1.length;t1++){
                //                 link1[t1].childrens = link1[t1].childrens+'个';
                //             }
                //             this.tableData3 = link1;
                //             debugger;
                //             console.log(this.fabox);
                //         })
                //     }
                // }else{}
                // if(this.fabox.fb === 0){
                //     this.catgory = 0;
                //     //需要调用获取首页列表数据的函数
                //     const json1 = {
                //         'enabled':1,
                //         'parent': 0
                //     };
                //     this.getCategon(json1);//parent=0时是回到首页。
                // }else{//此时就是从后面的页面跳回有子类数据的第一页了，此时fb!==0
                //     debugger;
                //     console.log(this.fabox);
                //     const jsond = {
                //         'enabled': 1,'parent': this.fabox.fb
                //     };
                //     this.listc2 = [];
                //     getchildcat(jsond).then(res => {
                //         const jsonc1 = {
                //             'catname': this.fabox.fname,
                //             'longer': res.data.length
                //         };
                //         this.listc2.push(jsonc1);
                //         this.catname = this.fabox.fname;
                //         const link1 = res.data;
                //         for(let t1 = 0;t1<link1.length;t1++){
                //             link1[t1].childrens = link1[t1].childrens+'个';
                //         }
                //         this.tableData3 = link1;
                //     })
                //     // debugger;
                //     // if(this.fabox.fname === undefined){
                //     //     this.catgory = 0;
                //     //     //需要调用获取首页列表数据的函数
                //     //     const json1 = {
                //     //         'enabled':1,
                //     //         'parent': 0
                //     //     };
                //     //     this.getCategon(json1);//parent=0时是回到首页。
                //     // }else{
                //     //     const json1 = {
                //     //         'enablled':1,
                //     //         'parent': this.fabox.fb
                //     //     };
                //     //     this.bclast = 2;
                //     //     this.listc2 = [];
                //     //     getchildcat(json1).then(res1 => {
                //     //         console.log('res1现在的数据',res1);
                //     //         const jsonc1 = {
                //     //             'catname': this.lastname,
                //     //             'longer': res1.data.length
                //     //         };
                //     //         this.listc2.push(jsonc1);
                //     //         console.log('this.listc2现在的值',this.listc2);
                //     //         this.catname = this.fabox.fname;
                //     //         const link1 = res1.data;
                //     //         for(let t1 = 0;t1<link1.length;t1++){
                //     //             link1[t1].childrens = link1[t1].childrens+'个';
                //     //         }
                //     //         this.tableData3 = link1;
                //             // debugger;
                //             // console.log(this.fabox);
                //             // console.log(this.meen);
                //             // this.tableData3.forEach(item => {
                //             //     if(item.parent === 0){this.lastname = undefined;}
                //             // })
                //             // if(this.tableData3.parent === 0){this.lastname = undefined;}
                //             // this.fabox = {
                //             //     fb: this.tableData3.parent,cb: this.tableData3.id,fname: this.lastname
                //             // };
                //     //     })
                //     // }
                // }
            }
        },
        lookchilden(meen,cak){
            this.catgory = 2;
            this.meen = meen;
            let parid = meen.id;//数字按钮所在的那行数据的id
            //debugger;
            if(cak){
                const jsont = {id: parid,name: meen.name};
                this.idnamlink.push(jsont);
            }else{}
            console.log('idnamlink+',this.idnamlink);
            // if(cak === 1){//从首页跳转到第一页
            //     this.bclast = 1;
            //     //此时是从表格中点击的数字按钮
            //     //parid = meen.id;//获取到的按钮对应的一级类别id
            //     //meen.oldpatid = parid;//1
            //     // this.fabox = {
            //     //     fb: meen.parent,cb: meen.id
            //     // };
            //     //this.lastname = meen.name;
            // }else if(cak === 2){//从第一页跳到第二页
            //     this.bclast = 2;
            //     //parid = meen.oldpatid;
            //     //parid = meen.parent;//获取到上一级的id
            //     //meen.oldpatid = meen.parent;
            //     // let newData = { a: this.fabox.n || 0};
            //     // this.fabox.a = newData.a;
            //     //debugger;
            //     //this.fabox.nextname = this.fabox.lname;
            //     //this.counter++;
            //     //const newPropName = 'a'+this.counter;
            //     //this.$set(this.jsonObj, newPropName, `Value of ${newPropName}`);
            //     //console.log(jsonObj);
            //     // let newData3 = { newPropName: this.fabox.lname};
            //     // this.fabox.newPropName = newData3.newPropName;
            //     // let newData2 = { lname: this.lastname };
            //     // this.fabox.lname = newData2.lname;
            //     // const n = this.fabox.pd || 0;
            //     // const pnd = this.fabox.fb;
            //     // this.fabox.n = n;
            //     // this.fabox.pd = pnd;
            //     // this.fabox.fb = meen.parent;
            //     // this.fabox.cb = meen.id;
            //     //this.fabox.lname = newData2.lname;
            //     // this.fabox.fname = meen.name;
            //     // this.lastname = meen.name;
            // }else{}
            const json1 = {
                'enabled': 1,
                'parent': parid
            };
            this.listc2 = [];
            getchildcat(json1).then(res1 => {
                //console.log('res1现在的数据',res1);
                const jsonc1 = {
                    'catname': meen.name,
                    'longer': res1.data.length
                };
                this.listc2.push(jsonc1);
                //console.log('this.listc2现在的值',this.listc2);
                this.catname = meen.catname;
                const link1 = res1.data;
                for(let t1 = 0;t1<link1.length;t1++){
                    link1[t1].childrens = link1[t1].childrens+'个';
                }
                this.tableData3 = link1;
            })
        },
        morecatlook(){
            this.delVisiblenc2 = true;
        },
        openonecat(){
            this.delVisiblenc1 = true;
            this.text1 = "";
            this.text2 = "";
        },
        restoredat(){
            const catid = this.catad;
            const jsonc = {
                'id': catid,
                'enabled': 1
            };
            restorecho(jsonc).then(res => {
                console.log('res现在的数据7.3',res);
                const cnst = res.data.msg;
                if(cnst == "操作成功"){
                    this.delVisiblenc6 = false;
                    this.stopcatfi();
                }
            })
        },
        restoreuse(bckcom){
            this.catad = bckcom.id;
            this.delVisiblenc6 = true;
            this.prestrt = 1;
            this.hfpt = 1;
            this.detprt = 0;
        },
        stopcatfi(){
            this.catgory = 1;
            const json1 = {
                'enabled': 0
            };
            stopnusern(json1).then(res => {
                console.log('res.data现在的数据',res.data);
                const lank2 = res.data;
                for(let t = 0;t<lank2.length;t++){
                    const enabled = lank2[t].enabledTime;
                    if(enabled == null){
                        lank2[t].enabledTime = "";
                    }
                }
                this.tableData2 = lank2;
            })
            //this.delVisiblenc9 = false;
        },
        upbscination(memg){
            this.delVisiblenc3 = true;
            this.updent = 1;
            const catname = memg.name;//类别名称
            const content = memg.content;//当前所含内容/使用的主要场合
            const catid = memg.id;//类别Id
            this.mid1 = catid;
            this.input1 = catname;
            this.input2 = content;
            this.text3 = "";
            this.text4 = "";
        },
        upcatlank(upt){
            if(upt === 1){
                this.delVisiblenc4 = true;
            }else if(upt === 2){
                this.delVisiblenc10 = true;
            }
            const catenid = this.mid1;
            console.log('catenid有值么',catenid);
            const jsonu = {
                'category': catenid
            };
            catrecordn(jsonu).then(res => {
                console.log('res的值',res);
                const beflist = res.data;
                for(let b = 0;b<beflist.length;b++){
                    const versionNo = beflist[b].versionNo;
                    if(versionNo == 0){
                        beflist[b].msgmade = "数据的创建";
                    }else{
                        beflist[b].msgmade = "第"+beflist[b].versionNo+'次修改后';
                    }
                }
                this.tableData4 = beflist;
            })
        },
        uponemadsun(ked){
            // if(ked === 1){
            // }else if(ked === 2){}
            let upowname = '';//修改后的名称
            let upowmesg = '';//修改后的内容
            let catid = '';//类别id
            let befename = '';//修改前的名称
            let befemesg = '';//修改前的内容
            if(ked === 1) {
                upowmesg = this.text4;
                upowname = this.text3;
                catid = this.mid1;
                befename = this.input1;
                befemesg = this.input2;
            }else if(ked === 2){
                upowname = this.text7;
                upowmesg = this.text8;
                catid = this.mid2;
                befename = this.input3;
                befemesg = this.input4;
            }
            const jsont = {
                "id": "",
                "name": "",
                "content": ""
            };
            jsont.id = catid;
            if(upowname == ""){
                jsont.name = befename;
            }else{
                jsont.name = upowname;
            }
            if(upowmesg == ""){
                jsont.content = befemesg;
            }else{
                jsont.content = upowmesg;
            }
            upcatmsgsure(jsont).then(res => {
                console.log('res现在的值',res);
                const msgn = res.data.msg;
                if(msgn == "操作成功"){
                    if(ked === 1){
                        this.delVisiblenc3 = false;
                        const jsonmn = {
                            'enabled': 1,
                            'parent': 0
                        };
                        this.getCategon(jsonmn);
                    }else if(ked === 2){
                        this.delVisiblenc8 = false;
                        this.lookchilden(this.meen,3);
                    }
                }
            })
        },
        //点击新增同级子类别按钮展示弹窗
        addchidcat(){
            this.delVisiblenc7 = true;
            this.catname = this.meen.catname;
            this.parentid = this.meen.id;
            this.text5 = '';
            this.text6 = '';
            // this.listc2.forEach(item => {
            //     this.catname = item.catname;
            //     this.parentid = item.id;//父级id
            // })
        },
        detsure(kek){
            //this.delVisiblenc9 = false;//为何弹窗就关闭失败呢？？？？？？？？
            debugger;
            if(kek === 1){
                const detlink = this.listbox2;
                console.log('this.listbox2的数据',detlink);
                if(detlink.childrens !== 0){
                    //console.log('有来过这里么？');
                    ElMessage.error("操作失败，该类别不可删除！");
                    return false;
                }else if(detlink.revisable === 0){
                    ElMessage.error("操作失败，该类别不可删除！");
                    return false;
                }
                const catid = detlink.id;
                const jsont = {
                    'ids': catid
                };
                debugger
                detcatet(jsont).then(res => {
                    //debugger;
                    console.log('res现在的样子',res);
                    const kelank = res.data;
                    if(kelank.code === 500){
                        ElMessage.error("操作失败，该类别不可删除！");
                        return false;
                    }else if(kelank.code === 0){
                        //debugger;
                        this.delVisiblenc5 = false;
                        //需要区分下是启用中的数据还是停用中的数据
                        //console.log('走到这里了么');
                        debugger;
                        const cat = this.catlnk;//这个位置连赋值都没赋值，就是默认的0，很显然不对
                        if(cat === 1){//启用部分
                            const json1 = {
                                'enabled': 1,
                                'parent': 0
                            };
                            this.getCategon(json1);
                        }else if(cat === 2){//停用部分
                            this.stopcatfi();
                        }
                    }
                })
            }else if(kek === 2){}
        },
        detsure2(kek2){
            debugger;
            if(kek2 === 1){
                const detlink = this.listbox2;
                if(detlink.childrens !== '0个'){
                    ElMessage.error("操作失败，该类别不可删除！");
                    return false;
                }else if(detlink.revisable === 0){
                    ElMessage.error("操作失败，该类别不可删除！");
                    return false;
                }
                const catid = detlink.id;
                const jsont = {
                    'ids': catid
                };
                detcatet(jsont).then(res => {
                    //debugger;
                    const kelank = res.data;
                    if(kelank.code === 500){
                        ElMessage.error("操作失败，该类别不可删除！");
                        return false;
                    }else if(kelank.code === 0) {
                        //需要区分下是启用中的数据还是停用中的数据
                        const cat = this.catlnk;
                        if(cat === 1) {//启用部分
                            this.lookchilden(this.meen);
                        }else if(cat === 2){//停用部分
                        }
                    }
                })
            }
        },
        dettlen(det,dif){
            //debugger;
            const catmink = det;
            this.listbox2 = catmink;
            console.log('catmink现在的值',catmink);
            if(catmink.isSystem === 1){
                ElMessage.error("操作失败，该类别不可删除！");
                return false;
            }else if(catmink.equipmentCount !== null && catmink.equipmentCount > 0){
                ElMessage.error("操作失败，因为该类别下面尚有装备器具！");
                return false;
            }
            if(dif === 1){
                // const catmink = det;
                // this.listbox2 = catmink;
                // console.log('catmink现在的值',catmink);
                // if(catmink.isSystem === 1){
                //   ElMessage.error("操作失败，该类别不可删除！");
                //   return false;
                // }else if(catmink.equipmentCount !== null && catmink.equipmentCount > 0){
                //   ElMessage.error("操作失败，因为该类别下面尚有装备器具！");
                //   return false;
                // }
                this.delVisiblenc5 = true;
                this.kept = 2;//代表是删除
                this.detpont = 1;//代表是一级类别进行删除
                this.stoptent = 0;//将停用相关的按钮隐藏
                this.catlnk = 1;
            }else if(dif === 2){
                this.delVisiblenc6 = true;
                this.detprt = 1;//代表是一级类别的数据进行删除
                this.hfpt = 2;//代表是删除类别
                this.prestrt = 0;//将停用按钮隐藏
                this.catlnk = 2;
            }
        },
        tipOkc1(){
            const content1 = this.text1;//一级类别名称输入框中填写的数据
            const content2 = this.text2;//所含内容/使用的主要场合输入框中填写的数据
            const lanken = this.tableData1;//类别管理列表中的数据
            console.log('lanken现在的数据',lanken);
            let e = 0;
            for(var b = 0;b<lanken.length;b++){
                const unname = lanken[b].name;
                const uncontent = lanken[b].content;
                if(content1 === unname){
                    e = 1;
                    ElMessage.error('操作失败，因为系统里已有这个名称了');
                    return false;
                }else if(content2 === uncontent){
                    e = 1;
                    ElMessage.error('操作失败，因为系统里已有这个内容了');
                    return false;
                }else if(content1.length === 0 || content1.length < 0){
                    e = 1;
                    ElMessage.error('操作失败，请填写一级类别名称内容。');
                    return false;
                }else if(content2.length === 0 || content2.length < 0){
                    e = 1;
                    ElMessage.error('操作失败，请填写 所含内容/使用的主要场合内容。');
                    return false;
                }else{
                    e = 0;
                }
            }
            const json1 = {
                'name': content1,
                'content': content2,
                'parent': 0
            };
            if(e === 0){
                addcamadn(json1).then(res => {
                    console.log('res现在的数据',res);
                    this.delVisiblenc1 = false;
                    const jsonaga = {
                        'enabled': 1,
                        'parent': 0
                    };
                    this.getCategon(jsonaga);
                })
            }
        },
        tipOkc2(){
            const content5 = this.text5;//子类别名称输入框中填写的数据
            const content6 = this.text6;//所含内容/使用的主要场合输入框中填写的数据
            const tab3 = this.tableData3;
            let t = 0;
            for(var c = 0;c>tab3.length;c++){
                const chiname = tab3[c].name;
                const chicontent = tab3[c].content;
                if(content5 === chiname){
                    t = 1;
                    ElMessage.error('操作失败，因为系统里已有这个名称了');
                    return false;
                }else if(content6 === chicontent){
                    t = 1;
                    ElMessage.error('操作失败，因为系统里已有这个内容了');
                    return false;
                }else if(content5.length === 0 || content5.length < 0){
                    t = 1;
                    ElMessage.error('操作失败，请填写一段子类别名称内容。');
                    return false;
                }else if(content6.length === 0 || content6.length < 0){
                    t = 1;
                    ElMessage.error('操作失败，请填写所含内容/使用的主要场合内容。');
                    return false;
                }else{
                    t = 0;
                }
            }
            const json2 = {
                'name': content5,
                'content': content6,
                'parent': this.parentid
            };
            if(t === 0){
                addcamadn(json2).then(res => {
                    this.delVisiblenc7 = false;
                    this.lookchilden(this.meen);
                })
            }
        },
        stopchose(stopmmg){
            this.listbox1 = stopmmg;
            const stid = stopmmg.id;//停用按钮对应行的类别id
            if(stopmmg.isSystem == 1){
                ElMessage.error('操作失败，该类别不可停用！');
                return false;
            }else if(stopmmg.equipmentCount != null && stopmmg.equipmentCount > 0){
                ElMessage.error('操作失败，因为该类别下尚有装备器具！');
                return false;
            }
            this.delVisiblenc5 = true;
            this.stoptent = 1;//代表是一级类别进行停用
            this.kept = 1;//代表是停用
            this.detpont = 0;//将删除功能使用的按钮隐藏
        },
        stopcat(){
            const stolink = this.listbox1;
            if(stolink.childrens != 0){
                ElMessage.error("操作失败，该类别不可停用！");
                return false;
            }
            const jsonst = {
                'id': stolink.id,
                'enabled': 0
            };
            stoppened(jsonst).then(res =>{
                console.log('res现在的数据',res);
                const over = res.data.msg;
                if(over == "操作成功"){
                    this.delVisiblenc5 = false;
                    const json5 = {
                        'enabled': 1,
                        'parent': 0
                    };
                    this.getCategon(json5);
                }
            })
        },
        stopcat2(){
            debugger;
            const stolink = this.listbox1;
            if(stolink.childrens != '0个'){
                ElMessage.error("操作失败，该类别不可停用！");
                return false;
            }
            const jsonst = {
                'id': stolink.id,
                'enabled': 0
            };
            stoppened(jsonst).then(res => {
                const over = res.data.msg;
                if(over == "操作成功") {
                    this.delVisiblenc5 = false;
                    this.lookchilden(this.meen);
                }
            })
        },
        upfoundation(memg){
            debugger;
            this.delVisiblenc8 = true;
            this.updent2 = 2;
            const catname = memg.name;//类别名称
            const content = memg.content;//当前所含内容/使用的主要场合
            const catid = memg.id;//类别id
            this.mid2 = catid;
            this.input3 = catname;
            this.input4 = content;
            this.text7 = "";
            this.text8 = "";
        },
        stopuse(stopmmg){
            debugger;
            this.listbox1 = stopmmg;
            const stid = stopmmg.id;//停用按钮对应行的类别id
            if(stopmmg.isSystem == 1){
                ElMessage.error('操作失败，该类别不可停用！');
                return false;
            }else if(stopmmg.equipmentCount != null && stopmmg.equipmentCount > 0){
                ElMessage.error('操作失败，因为该类别下尚有装备器具！');
                return false;
            }
            this.delVisiblenc9 = true;
            this.stoptent = 2;//代表是子类别进行停用
            this.kept = 1;//代表是停用
            this.detpont = 0;
        },
        detaleune(det,dif){
            debugger;
            const catmink = det;
            this.listbox2 = catmink;
            if(catmink.isSystem === 1){
                ElMessage.error("操作失败，该类别不可删除！");
                return false;
            }else if(catmink.equipmentCount !== null && catmink.equipmentCount > 0){
                ElMessage.error("操作失败，因为该类别下面尚有装备器具！");
                return false;
            }
            if(dif === 1){
                this.delVisiblenc9 = true;
                this.kept = 2;//代表是删除
                this.detpont = 2;//代表是子级类别进行删除
                this.stoptent = 0;//将停用相关的按钮隐藏
            }else if(dif === 2){
                this.delVisiblenc6 = true;
                this.detprt = 2;//代表是子级类别的数据进行删除
                this.hfpt = 2;//代表是删除类别
                this.prestrt = 0;//讲停用按钮隐藏
            }
        }
    }
}



/*补录结束*/
