<template>
	<div >
		<Pageheader title="注册主播助手" :showBack="true">
		</Pageheader>

		<div class="zhuceing">正在注册 ...... </div>
    <div class="live-container">
      <span @click="nextPay" class="primary live-button">下一步</span>
    </div>
  </div>
</template>
<script setup>
import { registerSonOrg  } from '@/api/api.js'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'
import { ElMessage } from 'element-plus'
import auth from '@/sys/auth'

const router = useRouter();

onMounted(()=>{
  nextPay()

})

const nextPay = () => {
  let user2= auth.getAcc()
  console.log(' user2= ', user2)
  let phone = user2.mobile;
  let username = user2.name;
  registerSonOrg({ phone: phone, userName: username })
      .then((res)=>{
        console.log('registerSonOrg', res)
        let result = res.data ;
        // true 成功, false 失败 (加了一个限制，
        // 手机号如已存在 一个作为董事长的直播机构，则返回 false，给原型中的“这个手机号已被注销了，换个手机号吧”提示)
        if(result){
          router.push({name: 'officeList'});

        }else{
          ElMessage.error('这个手机号已被注销了，换个手机号吧')
          router.push({name: 'register2'});
        }
      })
}

</script>

<style> 
.martop20{
	margin-top: 15px;
}
.tab1{
	padding-left: 15px;
	font-size: 13px;
}
.zhuceing{
	display: block;
	margin:100px auto;
	text-align: center;
}
</style>
