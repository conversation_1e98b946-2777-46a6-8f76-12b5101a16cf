package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.ProService;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.sales.service.ContractBaseService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 商品相关
 */
@Controller
@RequestMapping("/product")
@Component
public class ProductController {
    @Autowired
    ProService productService;
    @Autowired
    MaterielService materielService;
    @Autowired
    PdCustomerService pdCustomerService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserSuspendMsgService suspendMsgService;
    @Autowired
    ContractBaseService contractBaseService;

    /**
     * 通用性--录入商品
     * contractId 合同id
     */
    @ResponseBody
    @RequestMapping("/addTYProduct.do")
    public Map<String, Object> addTYProduct(User user, PdMerchandise product, String commodityMediaList, String isSaled) {
        Map<String, Object> map = new HashMap<String, Object>();

        if (StringUtils.isEmpty(product.getOuterSn())) {
            map.put("code", 400);
            map.put("msg", "请填写商品代号");
            return map;
        }
        if (StringUtils.isEmpty(product.getOuterName())) {
            map.put("code", 400);
            map.put("msg", "请填写商品名称");
            return map;
        }
        if (StringUtils.isEmpty(product.getUnit())) {
            map.put("code", 400);
            map.put("msg", "请填写计量单位");
            return map;
        }
        if (product.getMinimumStock() == null) {
            map.put("code", 400);
            map.put("msg", "请填写最低库存");
            return map;
        }
        product.setOrg(user.getOid());


        product.setType("1");
        //未销售
        if ("0".equals(isSaled)) {
            product.setIsSaled("0");
            product.setInitialStock(new BigDecimal(0));
        } else {
            product.setIsSaled("1");
        }
        String res = productService.addProduct(product, commodityMediaList, user);
        if ("success".equals(res)) {
            map.put("code", 200);
        } else {
            map.put("code", 400);
            map.put("msg", "该商品代号已存在!");
        }
        return map;
    }

    /**
     * 专属商品--录入商品
     */
    @ResponseBody
    @RequestMapping("/addZSProduct.do")
    public Map<String, Object> addZSProduct(User user, PdMerchandise product, String isSaled, String commodityMediaList) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isEmpty(product.getOuterSn())) {
            map.put("code", 400);
            map.put("msg", "请填写商品代号");
            return map;
        }
        if (StringUtils.isEmpty(product.getOuterName())) {
            map.put("code", 400);
            map.put("msg", "请填写商品名称");
            return map;
        }
        if (StringUtils.isEmpty(product.getUnit())) {
            map.put("code", 400);
            map.put("msg", "请填写计量单位");
            return map;
        }
        if (product.getMinimumStock() == null) {
            map.put("code", 400);
            map.put("msg", "请填写最低库存");
            return map;
        }
        product.setOrg(user.getOid());
        product.setType("2");
        if (product.getCustomer() == null) {
            map.put("code", 400);
            map.put("msg", "请先选择客户");
            return map;
        }
        if (product.getCustomer().getId() == null) {
            map.put("code", 400);
            map.put("msg", "请先选择客户");
            return map;
        }
        if (product.getCustomer().getId() == 0) {
            map.put("code", 400);
            map.put("msg", "请先选择客户");
            return map;
        }
        //未销售
        if ("0".equals(isSaled)) {
            product.setIsSaled("0");
            product.setInitialStock(new BigDecimal(0));
        } else {
            product.setIsSaled("1");
        }
        if (product.getUnitPrice() == null) {
            if (product.getUnitPriceReference() != null) {
                product.setUnitPrice(product.getUnitPriceReference());
            }
            if (product.getUnitPriceNotax() != null) {
                if (product.getTaxRate() != null) {
                    product.setUnitPrice(product.getUnitPriceNotax().add(product.getUnitPriceNotax().multiply(product.getTaxRate().divide(new BigDecimal(100)))));
                } else {
                    product.setUnitPrice(product.getUnitPriceNotax());
                }

            }
            if (product.getUnitPriceInvoice() != null) {
                product.setUnitPrice(product.getUnitPriceInvoice());
            }
            if (product.getUnitPriceNoinvoice() != null) {
                product.setUnitPrice(product.getUnitPriceNoinvoice());
            }
        }
        String res = productService.addProduct(product, commodityMediaList, user);
        if ("success".equals(res)) {
            map.put("code", 200);
        } else {
            map.put("code", 400);
            map.put("msg", "该商品代号已存在!");
        }
        return map;
    }

    /**
     * 通用商品--根据类别获取商品列表
     */
    @ResponseBody
    @RequestMapping("/getTYProductListByCategory.do")
    public Map<String, Object> getTYProductListByCategory(User user, Integer currPage, Integer pageSize, String category, String param) {
        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }
        PageInfo pageInfo=new PageInfo();
        pageInfo.setCurrentPageNo(currPage);
        pageInfo.setPageSize(pageSize);


        Map<String, Object> map = productService.getTyProductListByCategory(user.getOid(), category, param,pageInfo, "1", 0, user.getUserID());
        List<Map<String, Object>> mtCategories = materielService.getMtCategoryByOidAndName(user.getOid(), param);
        map.put("mtCategories", mtCategories);
        //暂停销售数量
        List<Map<String, Object>> list2 = productService.suspendProductListForList(user.getOid(), param, "1");
        map.put("suspendProductNum", list2.size());
        map.put("currPage", currPage);
        return map;
    }

    /**
     * 专属商品--根据类别获取商品列表
     */
    @ResponseBody
    @RequestMapping("/getZSProductListByCategory.do")
    public Map<String, Object> getZSProductListByCategory(User user, Integer currPage, Integer pageSize, String customerId, String param) {

        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        PageInfo pageInfo=new PageInfo();
        pageInfo.setCurrentPageNo(currPage);
        pageInfo.setPageSize(pageSize);


        Map<String, Object> map =new HashMap<>();
        //如果是财务查出所有客户
        if("finance".equals(user.getManagerCode())){
            map=productService.getZSProductListByCustomer(user.getOid(), customerId, param, pageInfo, null);
            List<Map<String,Object>> list= (List<Map<String, Object>>) map.get("data");
            //如果是财务，特殊处理
            if(list!=null){
                for (Map<String,Object> m:list) {
                    m.put("type","1");
                }
            }
        }else{
            map=productService.getZSProductListByCustomer(user.getOid(), customerId, param, pageInfo, user.getUserID());
        }

        List<Map<String, Object>> customers = new ArrayList<>();

        if ("0".equals(customerId)) {
            customers = materielService.getMtCategoryByOidAndName(user.getOid(), param);
        } else {
            //如果是财务查出所有客户
            if("finance".equals(user.getManagerCode())){
                customers = pdCustomerService.getListByPrincipal(null, customerId, user.getOid(), param);
            }else{
                customers = pdCustomerService.getListByPrincipal(user.getUserID(), customerId, user.getOid(), param);
            }

        }
        map.put("customers", customers);
        //暂停销售数量
        List<Map<String, Object>> list2 = productService.suspendProductListForList(user.getOid(), param, "2");
        map.put("suspendProductNum", list2.size());
        map.put("currPage", currPage);
        return map;
    }

    /**
     * 公司总览--获取所有商品
     */
    @ResponseBody
    @RequestMapping("/getAllProductList.do")
    public Map<String, Object> getAllProductList(User user, Integer currPage, Integer pageSize, String customerId, String param) {

        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        PageInfo pageInfo=new PageInfo();
        pageInfo.setCurrentPageNo(currPage);
        pageInfo.setPageSize(pageSize);

        Map<String, Object> map = productService.getZSProductListByCustomer(user.getOid(), customerId, param, pageInfo, null);
        List<Map<String, Object>> customers = new ArrayList<>();
        if ("0".equals(customerId)) {
            customers = materielService.getMtCategoryByOidAndName(user.getOid(), param);
        } else {
            customers = pdCustomerService.getListByPrincipal(null, customerId, user.getOid(), param);
        }
        map.put("customers", customers);
        //暂停销售数量
        List<Map<String, Object>> list2 = productService.suspendProductListForList(user.getOid(), param, null);
        map.put("suspendProductNum", list2.size());
        map.put("currPage", currPage);
        return map;
    }

    /**
     * 根据客户id查看合法的合同列表
     */
    @ResponseBody
    @RequestMapping("/getContractBaseListByCustomer.do")
    public Map<String, Object> getContractBaseListByCustomer(Integer customerId) {
        Map<String, Object> map = new HashMap<String, Object>();
        //获取合同信息
        List<Map<String, Object>> contractBaseList = contractBaseService.getContactBaseList(customerId);
        map.put("contractBaseList", contractBaseList);
        return map;
    }

    /**
     * 查看商品基本信息
     */
    @ResponseBody
    @RequestMapping("/getProductOne.do")
    public Map<String, Object> getProductOne(Integer productId) {
        Map<String, Object> map = new HashMap<String, Object>();
        PdMerchandise product = productService.getProductOne(productId);
        PdMerchandiseVo productVo = new PdMerchandiseVo();
        BeanUtils.copyProperties(product, productVo);
//        product.setCustomer(null);
//        product.setProduct(null);
//        product.setSlOrdersItemHashSet(null);
        if ("2".equals(productVo.getType())) {
            if (productVo.getUnitPriceNoinvoice() != null) {
                productVo.setUnitPrice(null);
            }

            if (productVo.getUnitPriceInvoice() != null) {
                productVo.setUnitPrice(null);
            }

        }
        productVo.setAddressList(productService.getProductAddressList(productId));

        productVo.setNameInvoice(productService.getPdMerchandiseInvoice(productId, 1));
        productVo.setGlInvoice(productService.getPdMerchandiseInvoice(productId, 2));
        //获取暂停时间
        PdMerchandiseHistory m1 = productService.pdMerchandiseHistory(productId, "7", "0");
        if (m1 != null) {
            productVo.setZtDate(m1.getUpdateDate());
            productVo.setZtName(m1.getUpdateName());
        }
        //获取回复时间
        PdMerchandiseHistory m2 = productService.pdMerchandiseHistory(productId, "7", "1");
        if (m2 != null) {
            productVo.setHfDate(m2.getUpdateDate());
            productVo.setHfName(m2.getUpdateName());
        }
        //获取恢复时间
        map.put("data", productVo);
        map.put("code", 200);
        return map;
    }

    /**
     * 暂停商品销售
     */
    @ResponseBody
    @RequestMapping("/suspendProduct.do")
    public Map<String, Object> suspendProduct(User user, Integer productId, String state) {
        Map<String, Object> map = new HashMap<String, Object>();

        if (state == null || "".equals(state)) {
            map.put("code", 400);
            map.put("msg", "参数错误");
            return map;
        }
        String res = productService.suspendProduct(productId, state, user);
        if ("success".equals(res)) {
            map.put("code", 200);
            return map;
        }
        map.put("code", 400);
        map.put("msg", res);
        return map;
    }

    /**
     * 暂停商品销售判断
     */
    @ResponseBody
    @RequestMapping("/suspendProductJudge.do")
    public Map<String, Object> suspendProductJudge(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        String s = request.getParameter("productId");
        Integer productId = Integer.valueOf(s);
        String state = request.getParameter("state");
        if (state == null || "".equals(state)) {
            map.put("code", 400);
            map.put("msg", "参数错误");
            return map;
        }
        String res = productService.suspendProductJudge(productId, state);

        map.put("code", res);
        return map;
    }

    /**
     * 删除商品
     */
    @ResponseBody
    @RequestMapping("/deleteProduct.do")
    public Map<String, Object> deleteProduct(Integer productId) {
        Map<String, Object> map = new HashMap<String, Object>();
        String res = productService.deleteProduct(productId);
        if ("success".equals(res)) {
            map.put("code", 200);
            return map;
        }
        map.put("code", 400);
        map.put("msg", "删除失败");
        return map;

    }

    /**
     * 删除商品判断
     */
    @ResponseBody
    @RequestMapping("/deleteProductJudge.do")
    public Map<String, Object> deleteProductJudge(Integer productId) {
        Map<String, Object> map = new HashMap<String, Object>();


        String res = productService.deleteProductJudge(productId);

        map.put("code", res);
        return map;
    }


    /**
     * 所有暂停销售列表
     */
    @ResponseBody
    @RequestMapping("/suspendAllProductList.do")
    public Map<String, Object> suspendAllProductList(User user, String param, Integer currPage, Integer pageSize) {


        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        PageInfo pageInfo=new PageInfo();
        pageInfo.setCurrentPageNo(currPage);
        pageInfo.setPageSize(pageSize);

        Map<String, Object> map = productService.suspendProductList(user.getOid(), param,pageInfo, null);

        Integer num = 0;
        if (map.get("totalRows") != null) {
            num = (Integer) map.get("totalRows");
        }
        map.put("suspendProductNum", num);

        map.put("currPage", currPage);
        return map;
    }

    /**
     * 通用商品--暂停销售列表
     */
    @ResponseBody
    @RequestMapping("/suspendTYProductList.do")
    public Map<String, Object> suspendTYProductList(User user, String param, Integer currPage, Integer pageSize) {


        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        PageInfo pageInfo=new PageInfo();
        pageInfo.setCurrentPageNo(currPage);
        pageInfo.setPageSize(pageSize);

        Map<String, Object> map = productService.suspendProductList(user.getOid(), param,pageInfo, "1");
        List<Map<String, Object>> mtCategories = materielService.getMtCategoryByOidAndName(user.getOid(), param);
        map.put("mtCategories", mtCategories);
        Integer num = 0;
        if (map.get("totalRows") != null) {
            num = (Integer) map.get("totalRows");
        }
        map.put("suspendProductNum", num);

        map.put("currPage", currPage);
        return map;
    }

    /**
     * 专属商品--暂停销售列表
     */
    @ResponseBody
    @RequestMapping("/suspendZSProductList.do")
    public Map<String, Object> suspendZSProductList(User user, String param, Integer currPage, Integer pageSize) {


        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        PageInfo pageInfo=new PageInfo();
        pageInfo.setCurrentPageNo(currPage);
        pageInfo.setPageSize(pageSize);

        Map<String, Object> map = productService.suspendProductList(user.getOid(), param, pageInfo, "2");
        List<Map<String, Object>> customers = pdCustomerService.getListByPrincipal(user.getUserID(), null, user.getOid(), param);
        map.put("customers", customers);

        Integer num = 0;
        if (map.get("totalRows") != null) {
            num = (Integer) map.get("totalRows");
        }
        map.put("suspendProductNum", num);
        map.put("currPage", currPage);
        return map;
    }

    /**
     * 基本信息修改
     * 未完结订单中的数据是否一并修改？ 1 修改 2不修改
     */
    @ResponseBody
    @RequestMapping("/updateProductBase.do")
    public Map<String, Object> updateProductBase(User user, String commodityMediaList, PdMerchandise product) {
        Map<String, Object> map = new HashMap<String, Object>();

        String res = productService.updateProductBase(product, commodityMediaList, user, "jb");
        if ("success".equals(res)) {
            map.put("code", res);
            return map;
        }
        map.put("code", 400);
        map.put("msg", res);
        return map;
    }

    /**
     * 其他信息修改
     * 未完结订单中的数据是否一并修改？ 1 修改 2不修改
     */
    @ResponseBody
    @RequestMapping("/updateProductOtherBase.do")
    public Map<String, Object> updateProductOtherBase(User user,String commodityMediaList, PdMerchandise product) {

        Map<String, Object> map = new HashMap<String, Object>();


        String res = productService.updateProductBase(product, commodityMediaList, user, "qt");
        if ("success".equals(res)) {
            map.put("code", res);
            return map;
        }

        map.put("code", 400);
        map.put("msg", res);
        return map;
    }


    /**
     * 关联管理--获取待关联列表
     */
    @ResponseBody
    @RequestMapping("/getWaitRelevanceProList.do")
    public Map<String, Object> getWaitRelevanceProList(User user, Integer currPage, Integer pageSize) {

        Integer oid = user.getOid();

        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        Map<String, Object> map = productService.getWaitRelevanceProList(oid, currPage, pageSize);


        map.put("currPage", currPage);


        return map;
    }

    /**
     * 关联管理--获取已关联列表
     */
    @ResponseBody
    @RequestMapping("/getAlreadyProList.do")
    public Map<String, Object> getAlreadyProList(User user, Integer currPage, Integer pageSize, String param) {
        Integer oid = user.getOid();
        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }
        Map<String, Object> map = productService.getAlreadyProList(oid, param, currPage, pageSize);
        map.put("currPage", currPage);
        return map;
    }

    /**
     * 关联管理--产品查看列表(去选择产品)
     */
    @ResponseBody
    @RequestMapping("/getWaitPdPerList.do")
    public Map<String, Object> getWaitPdPerList(User user, Integer currPage, Integer pageSize, String param) {
        Integer oid = user.getOid();
        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }
        Map<String, Object> map = productService.getWaitPdPerList(oid, param, currPage, pageSize);
        map.put("currPage", currPage);
        return map;
    }


    /**
     * 关联管理--根据产品查询关联的商品
     */
    @ResponseBody
    @RequestMapping("/getProductListByPdBase.do")
    public Map<String, Object> getProductListByPdBase(Integer pdBaseId) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Map<String, Object>> List = productService.getProductListByPdBase(pdBaseId);
        map.put("list", List);
        map.put("code", 200);
        return map;
    }

    /**
     * 关联管理--确认关联
     */
    @ResponseBody
    @RequestMapping("/confirmAssociation.do")
    public Map<String, Object> confirmAssociation(User user, Integer pdBaseId, Integer productId) {
        Map<String, Object> map = new HashMap<String, Object>();
        String res = productService.confirmAssociation(pdBaseId, productId, user);
        if ("success".equals(res)) {
            map.put("code", 200);
        } else {
            map.put("code", 400);
            map.put("msg", res);
        }

        return map;
    }


    /**
     * 关联管理--关联查看
     */
    @ResponseBody
    @RequestMapping("/getProductCorrelation.do")
    public Map<String, Object> getProductCorrelation(Integer productId) {
        Map<String, Object> map = new HashMap<String, Object>();

        Map<String, Object> res = productService.getProductCorrelation(productId);


        map.put("code", 200);
        map.put("msg", res);
        return map;
    }

    /**
     * 产品档案--产品列表
     */
    @ResponseBody
    @RequestMapping("/getPdBaseList.do")
    public Map<String, Object> getPdBaseList(User user, Integer currPage, Integer pageSize, String category, String param, String process, String composition) {

        Integer oid = user.getOid();

        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        Map<String, Object> map = productService.getPdBaseList(oid, category, process, composition, param, "1", currPage, pageSize);

        Map<String, Object> type = new HashMap<>();
        type.put("id", 0);
        type.put("value", "待分类");

        Integer num = 0;
        if (map.get("totalRows") != null) {
            num = (Integer) map.get("totalRows");
        }
        type.put("num", num);

        List list = new ArrayList();
        list.add(type);
        Map<String, Object> stopList = productService.suspendPdBaseList(oid, null, null, "", "1", currPage, pageSize);

        Integer suspendNum = 0;
        if (stopList.get("totalRows") != null) {
            suspendNum = (Integer) stopList.get("totalRows");
        }
        map.put("currPage", currPage);
        map.put("countNum", num);
        map.put("suspendNum", suspendNum);
        map.put("mtCategories", list);
        map.put("code", 200);


        return map;
    }


    /**
     * 通用性- 获取通用商品列表
     * contractId 合同id
     */
    @ResponseBody
    @RequestMapping("/getTYProductList.do")
    public Map<String, Object> getTYProductList(User user) {
        Map<String, Object> map = new HashMap<String, Object>();

        List<Map<String, Object>> list = productService.getProductList(user.getOid(), "1", null);
        map.put("code", 200);
        map.put("data", list);
        return map;
    }

    /**
     * 专属商品--录入商品
     */
    @ResponseBody
    @RequestMapping("/getZsProductList.do")
    public Map<String, Object> getZsProductList(User user, Integer customerId) {
        Map<String, Object> map = new HashMap<String, Object>();

        List<Map<String, Object>> list = productService.getProductList(user.getOid(), "2", customerId);
        map.put("code", 200);
        map.put("data", list);
        return map;

    }

    /**
     * 获取所有客户
     */
    @ResponseBody
    @RequestMapping("/getAllCustomer.do")
    public Map<String, Object> getAllCustomer(User user) {
        Map<String, Object> map = new HashMap<String, Object>();

        List<Map<String, Object>> list = pdCustomerService.getCustomerByPrincipal(user.getOid(), null);
        map.put("code", 200);
        map.put("data", list);
        return map;

    }

    /**
     * 产品档案--产品录入
     */
    @ResponseBody
    @RequestMapping("/addPdBase.do")
    public Map<String, Object> addPdBase(User user, PdBase pdBase, String zsProductList, String tYProductId, String resourceListString, String cptzImageString, String xhImageString) {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();

        if (StringUtils.isEmpty(pdBase.getInnerSn())) {
            map.put("code", 400);
            map.put("msg", "请填写产品图号");
            return map;
        }
        if (StringUtils.isEmpty(pdBase.getName())) {
            map.put("code", 400);
            map.put("msg", "请填写产品名称");
            return map;
        }
        if (StringUtils.isEmpty(pdBase.getUnit())) {
            map.put("code", 400);
            map.put("msg", "请填写计量单位");
            return map;
        }

        if (StringUtils.isEmpty(pdBase.getPhrase())) {
            map.put("code", 400);
            map.put("msg", "请填写所属阶段");
            return map;
        }
        if (pdBase.getProcessDept() == null) {
            map.put("code", 400);
            map.put("msg", "请填写加工单位");
            return map;
        }
        pdBase.setOid(oid);
        pdBase.setType("1");
        pdBase.setState("1");
        String res = productService.addPdBase(pdBase, zsProductList, tYProductId, resourceListString, cptzImageString, xhImageString, user);


        if ("success".equals(res)) {
            map.put("code", 200);
        } else {
            map.put("code", 400);
            map.put("msg", res);
        }

        return map;
    }


    /**
     * 查看产品信息
     */
    @ResponseBody
    @RequestMapping("/getPdBaseOne.do")
    public Map<String, Object> getPdBaseOne(Integer pdBaseId) {
        Map<String, Object> map = new HashMap<String, Object>();
        PdBase pdBase = productService.getPdBaseOne(pdBaseId);
        pdBase.setTPdBaseHistoryPdBaseViaProduct(null);
        pdBase.setTMtBaseViaProduct(null);
        pdBase.setTPdCompositionViaParent(null);
        pdBase.setTPdMerchandisePdBaseViaProduct(null);
        pdBase.setTPdCompositionPdBaseViaProduct(null);
        pdBase.setTPdProcessPdBaseViaProduct(null);
        map.put("data", pdBase);
        map.put("code", 200);

        return map;
    }

    /**
     * 修改产品信息
     */
    @ResponseBody
    @RequestMapping("/updatePdBase.do")
    public Map<String, Object> updatePdBase(User user, PdBase pdBase, String flag) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isEmpty(pdBase.getInnerSn())) {
            map.put("code", 400);
            map.put("msg", "请填写产品图号");
            return map;
        }
        if (StringUtils.isEmpty(pdBase.getName())) {
            map.put("code", 400);
            map.put("msg", "请填写产品名称");
            return map;
        }
        if (StringUtils.isEmpty(pdBase.getUnit())) {
            map.put("code", 400);
            map.put("msg", "请填写计量单位");
            return map;
        }

        if (StringUtils.isEmpty(pdBase.getPhrase())) {
            map.put("code", 400);
            map.put("msg", "请填写所属阶段");
            return map;
        }
        if (pdBase.getProcessDeptName() == null) {
            map.put("code", 400);
            map.put("msg", "请填写加工单位");
            return map;
        }

        String res = productService.updatePdBase(pdBase, user, flag);


        if ("success".equals(res)) {
            map.put("code", 200);
        } else {
            map.put("code", 400);
            map.put("code", res);
        }


        return map;
    }


    /**
     * 产品档案--暂停/恢复生产
     */
    @ResponseBody
    @RequestMapping("/suspensionOfPdBase.do")
    public Map<String, Object> suspensionOfPdBase(User user, Integer pdBaseId, String state) {
        Map<String, Object> map = new HashMap<String, Object>();

        if (state == null || "".equals(state)) {
            map.put("code", 400);
            map.put("msg", "参数错误");
            return map;
        }
        String res = productService.suspensionOfPdBase(pdBaseId, state, user);


        if ("success".equals(res)) {
            map.put("code", 200);
            return map;
        }

        map.put("code", 400);
        map.put("msg", res);
        return map;
    }

    /**
     * 产品档案--暂停/恢复生产判断
     */
    @ResponseBody
    @RequestMapping("/suspensionOfPdBaseJudge.do")
    public Map<String, Object> suspensionOfPdBaseJudge(Integer pdBaseId, String state) {
        Map<String, Object> map = new HashMap<String, Object>();


        if (state == null || "".equals(state)) {
            map.put("code", 400);
            map.put("msg", "参数错误");
            return map;
        }
        String res = productService.suspensionOfPdBaseJudge(pdBaseId, state);
        map.put("code", res);
        return map;

    }

    /**
     * 产品档案--删除产品
     */
    @ResponseBody
    @RequestMapping("/deletePdBase.do")
    public Map<String, Object> deletePdBase(Integer pdBaseId) {
        Map<String, Object> map = new HashMap<String, Object>();


        String res = productService.deletePdBase(pdBaseId);


        if ("success".equals(res)) {
            map.put("code", 200);
            return map;
        } else {
            map.put("code", 400);
            map.put("msg", "0");
        }


        return map;
    }

    /**
     * 产品档案--删除产品判断
     */
    @ResponseBody
    @RequestMapping("/deletePdBaseJudge.do")
    public Map<String, Object> deletePdBaseJudge(Integer pdBaseId) {
        Map<String, Object> map = new HashMap<String, Object>();


        String res = productService.deletePdBaseJudge(pdBaseId);


        map.put("code", res);
        return map;
    }


    /**
     * 产品档案--暂停产品列表
     */
    @ResponseBody
    @RequestMapping("/suspendPdBaseList.do")
    public Map<String, Object> suspendPdBaseList(User user, String source, String composition, String param, Integer currPage, Integer pageSize) {
        Integer oid = user.getOid();

        if (currPage == null || currPage == 0) {
            currPage = 1;//默认当前页
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 20;
        }

        Map<String, Object> map = productService.suspendPdBaseList(oid, source, composition, param, "1", currPage, pageSize);
        map.put("currPage", currPage);

        Integer suspendNum = 0;
        if (map.get("totalRows") != null) {
            suspendNum = (Integer) map.get("totalRows");
        }

        List<Map<String, Object>> countList = productService.getPdBaseList(oid);
        map.put("countNum", countList.size());
        map.put("suspendNum", suspendNum);
        return map;
    }

    /*
     **
     *<AUTHOR>
     *获取基本信息修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getProductRecordBaseList.do")
    public JsonResult getProductRecordBaseList(Integer id) {


        List<PdMerchandiseHistory> list = productService.getPdMerchandisePriceRecordList(id,"3");


        Map<String, Object> map = new HashMap<>();
        if (list.size() == 1) {
            map.put("createName", list.get(0).getCreateName());
            map.put("createDate", list.get(0).getCreateDate());
            map.put("updateName", list.get(0).getUpdateName());
            map.put("updateDate", list.get(0).getUpdateDate());
            map.put("list", new ArrayList<>());
        } else if (list.size() > 1) {
            map.put("createName", list.get(list.size() - 1).getCreateName());
            map.put("createDate", list.get(list.size() - 1).getCreateDate());
            map.put("updateName", list.get(list.size() - 1).getUpdateName());
            map.put("updateDate", list.get(list.size() - 1).getUpdateDate());
            for (int i = 0; i <list.size()-1 ; i++) {
                list.get(i).setEndDate(list.get(i+1).getUpdateDate());
            }
            map.put("list", list);
        } else {
            map.put("createName", null);
            map.put("createDate", null);
            map.put("updateName", null);
            map.put("updateDate", null);
            map.put("priceReason", null);
            map.put("basicReason", null);
            map.put("financerName", null);
            map.put("financerTime", null);
            map.put("list", list);
        }
        return new JsonResult(1, map);

    }


    /**
     * 查看商品基本信息修改记录
     */
    @ResponseBody
    @RequestMapping("/getProductRecordBaseDetails.do")
    public Map<String, Object> getProductRecordBaseDetails(Integer id) {
        Map<String, Object> map = new HashMap<String, Object>();
        PdMerchandiseHistory product = productService.getProductRecordBaseDetails(id);
        product.setCustomer(null);
        product.setProduct(null);

        if ("2".equals(product.getType())) {
            if (product.getUnitPriceNoinvoice() != null) {
                product.setUnitPrice(null);
            }
            if (product.getUnitPriceInvoice() != null) {
                product.setUnitPrice(null);
            }
        }
        map.put("data", product);
        map.put("code", 200);
        return map;
    }

    /*
     **
     *<AUTHOR>
     *获取其他信息修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getProductRecordOtherList.do")
    public JsonResult getProductRecordOtherList(Integer id) {

        List<Map<String, Object>> list = productService.getProductRecordBaseList(id, "B");

        Map<String, Object> map = this.getRecord(list);

        return new JsonResult(1, map);
    }

    /*
     **
     *<AUTHOR>
     *获取其他信息修改记录详情
     */
    @ResponseBody
    @RequestMapping("/getProductRecordOtherDetails.do")
    public Map<String, Object> getProductRecordOtherDetails(Integer id) {

        Map<String, Object> map = new HashMap<String, Object>();
        PdMerchandiseHistory product = productService.getProductRecordOtherDetails(id);
        product.setCustomer(null);
        product.setProduct(null);

        if ("2".equals(product.getType())) {
            if (product.getUnitPriceNoinvoice() != null) {
                product.setUnitPrice(null);
            }
            if (product.getUnitPriceInvoice() != null) {
                product.setUnitPrice(null);
            }
        }
        map.put("data", product);
        map.put("code", 200);
        return map;
    }

    /*
     **
     *<AUTHOR>
     *获取产品关联记录
     */
    @ResponseBody
    @RequestMapping("/getPdBaseGuanLianList.do")
    public Map<String, Object> getPdBaseGuanLianList(User user, String param, String source, String composition, Integer currPage, Integer pageSize) {

        Map<String, Object> map = productService.getPdBaseGuanLianList(user.getOid(), param, source, composition, currPage, pageSize);
        return map;
    }

    //    /*
//     **
//     *<AUTHOR>
//     *获取产品关联记录
//     */
//    @ResponseBody
//    @RequestMapping("/getProductListByPdBase.do")
//    public Map<String, Object> getProductListByPdBase( User user,Integer pdBaseId) {
//        Map<String, Object> map = new HashMap<String, Object>();
//        List< Map<String,Object> > list = productService. getProductListByPdBase(pdBaseId);
//        map.put("data", list);
//        map.put("code", 200);
//        return map;
//    }
//
    /*
     **
     *<AUTHOR>
     *获取商品关联记录
     */
    @ResponseBody
    @RequestMapping("/getProductGuanLianList.do")
    public JsonResult getProductGuanLianList(Integer id) {
        List<Map<String, Object>> list = productService.getProductGuanLianList(id);
        return new JsonResult(1, list);
    }


    /*
     **
     *<AUTHOR>
     *产品启停记录
     */
    @ResponseBody
    @RequestMapping("/getProductStopAndStartRecord.do")
    public JsonResult getProductStopAndStartRecord(Integer id) {
        List<Map<String, Object>> list = productService.getProductStopAndStartRecord(id);

        Map<String, Object> map = new HashMap<>();
        map.put("list", list);

        return new JsonResult(1, map);
    }


    /*
     **
     *<AUTHOR>
     *根据代号获取使用过该代号的商品
     */
    @ResponseBody
    @RequestMapping("/getProductHistoryByCode.do")
    public JsonResult getProductHistoryByCode(User user, String code) {
        List<Map<String, Object>> list = productService.getProductHistoryByCode(user.getOid(), code);

        for (Map<String, Object> m : list) {
            String ud = (String) m.get("updateDate");
            if (ud != null) {
                String[] s = ud.split(",");
                if (s.length > 1) {
                    m.put("updateDate", s[0]);
                    m.put("updateName", s[1]);
                }
            }

        }
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("oldNum", productService.getProductByCode(user.getOid(), code).size());

        return new JsonResult(1, map);
    }

    /*
     **
     *<AUTHOR>
     *编辑开票信息
     */
    @ResponseBody
    @RequestMapping("/editPdMerchandiseInvoice.do")
    public JsonResult editPdMerchandiseInvoice(User user, PdMerchandiseInvoice merchandiseInvoice) {
        merchandiseInvoice.setOrg(user.getOid());
        merchandiseInvoice.setCreateName(user.getUserName());
        merchandiseInvoice.setCreator(user.getUserID());
        merchandiseInvoice.setCreateDate(new Date());
        merchandiseInvoice.setUpdateName(user.getUserName());
        merchandiseInvoice.setUpdator(user.getUserID());
        merchandiseInvoice.setUpdateDate(new Date());
        String res = productService.editPdMerchandiseInvoice(merchandiseInvoice);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }

    }

    /*
     **
     *<AUTHOR>
     *获取开票信息
     */
    @ResponseBody
    @RequestMapping("/getPdMerchandiseInvoice.do")
    public JsonResult getPdMerchandiseInvoice(User user, Integer merchandise, Integer type) {

        PdMerchandiseInvoice data = productService.getPdMerchandiseInvoice(merchandise, type);
        return new JsonResult(1, data);
    }

    /*
     **
     *<AUTHOR>
     *获取开票信息修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getPdMerchandiseInvoiceRecordList.do")
    public JsonResult getPdMerchandiseInvoiceRecordList(Integer merchandise, Integer type) {

        List<PdMerchandiseInvoiceHistory> list = productService.getPdMerchandiseInvoiceRecordList(merchandise, type);
        List<Map<String, Object>> l2 = new ArrayList<>();
        for (PdMerchandiseInvoiceHistory pi : list) {
            Map m = new HashMap();
            m.put("id", pi.getId());
            l2.add(m);
        }

        Map map = new HashMap();
        if (list.size() == 1) {
            map.put("createName", list.get(0).getCreateName());
            map.put("createDate", list.get(0).getCreateDate());
            map.put("updateName", list.get(0).getUpdateName());
            map.put("updateDate", list.get(0).getUpdateDate());
            map.put("list", list);
        } else if (list.size() > 1) {
            map.put("createName", list.get(list.size() - 1).getCreateName());
            map.put("createDate", list.get(list.size() - 1).getCreateDate());
            map.put("updateName", list.get(list.size() - 1).getUpdateName());
            map.put("updateDate", list.get(list.size() - 1).getUpdateDate());
            map.put("list", list);
        } else {
            map.put("createName", null);
            map.put("createDate", null);
            map.put("updateName", null);
            map.put("updateDate", null);
            map.put("list", list);
        }


        return new JsonResult(1, map);
    }


    /**
     * 查看商品基本信息修改记录
     */
    @ResponseBody
    @RequestMapping("/getPdMerchandiseInvoiceRecordDetails.do")
    public JsonResult getPdMerchandiseInvoiceRecordDetails(Integer id) {

        PdMerchandiseInvoiceHistory history = productService.getPdMerchandiseInvoiceRecordDetails(id);
        return new JsonResult(1, history);
    }

    /*
     **
     *<AUTHOR>
     *修改价格元素
     */
    @ResponseBody
    @RequestMapping("/updatePdMerchandisePrice.do")
    public JsonResult updatePdMerchandisePrice(User user, PdMerchandise pdMerchandise) {


        if(StringUtils.isEmpty(pdMerchandise.getEffectiveOption())){
            return new JsonResult(0,"","400","请选择生效日期");

        }


        if("2".equals(pdMerchandise.getEffectiveOption())&&pdMerchandise.getEffectiveDate()==null){
            return new JsonResult(0,"","400","请选择生效时间");
        }
        PdMerchandise m = productService.getProductOne(pdMerchandise.getId());

        //历史对象
        PdMerchandiseHistory pcph=new PdMerchandiseHistory();
        BeanUtils.copyProperties(m,pcph);
        pcph.setId(null);
        pcph.setProductCustomer(m.getId());
        if(m.getCustomer()!=null){
            pcph.setCustomer(m.getCustomer().getId());
        }

        m.setUpdateDate(new Date());
        m.setUpdateName(user.getUserName());
        m.setUpdator(user.getUserID());
         //如果立即生效
        if("1".equals(pdMerchandise.getEffectiveOption())){
            m.setEffectiveDate(new Date());
            //m.setEffectiveDate(pdMerchandise.getEffectiveDate());
            m.setEffectiveOption(pdMerchandise.getEffectiveOption());
            m.setInvoiceCategory(pdMerchandise.getInvoiceCategory());
            m.setTaxRate(pdMerchandise.getTaxRate());
            m.setUnitPrice(pdMerchandise.getUnitPrice());
            m.setUnitPriceNotax(pdMerchandise.getUnitPriceNotax());
            m.setUnitPriceInvoice(pdMerchandise.getUnitPriceInvoice());
            m.setUnitPriceNoinvoice(pdMerchandise.getUnitPriceNoinvoice());
            m.setUnitPriceReference(pdMerchandise.getUnitPriceReference());

            pcph.setEffectiveDate(new Date());
            //m.setEffectiveDate(pdMerchandise.getEffectiveDate());
            pcph.setEffectiveOption(pdMerchandise.getEffectiveOption());
            pcph.setInvoiceCategory(pdMerchandise.getInvoiceCategory());
            pcph.setTaxRate(pdMerchandise.getTaxRate());
            pcph.setUnitPrice(pdMerchandise.getUnitPrice());
            pcph.setUnitPriceNotax(pdMerchandise.getUnitPriceNotax());
            pcph.setUnitPriceInvoice(pdMerchandise.getUnitPriceInvoice());
            pcph.setUnitPriceNoinvoice(pdMerchandise.getUnitPriceNoinvoice());
            pcph.setUnitPriceReference(pdMerchandise.getUnitPriceReference());

            if ("2".equals(m.getType())) {
                if (m.getUnitPrice() == null) {
                    if (m.getUnitPriceReference() != null) {
                        m.setUnitPrice(m.getUnitPriceReference());
                    }
                    if (m.getUnitPriceNotax() != null) {
                        if (m.getUnitPriceNotax() != null) {
                            if (m.getTaxRate() != null) {
                                m.setUnitPrice(m.getUnitPriceNotax().add(m.getUnitPriceNotax().multiply(m.getTaxRate().divide(new BigDecimal(100)))));
                            } else {
                                m.setUnitPrice(m.getUnitPriceNotax());
                            }

                        }
                    }
                    if (m.getUnitPriceInvoice() != null) {
                        m.setUnitPrice(m.getUnitPriceInvoice());
                    }
                    if (m.getUnitPriceNoinvoice() != null) {
                        m.setUnitPrice(m.getUnitPriceNoinvoice());
                    }
                }
            }

            if (m.getUnitPrice() != null) {
                m.setHasInvoice("1");
            }

            if (m.getUnitPriceReference() != null) {
                m.setHasInvoice("0");
            }
            if (m.getUnitPriceNoinvoice() != null) {
                m.setHasInvoice("0");
            }
            if (m.getUnitPriceNotax() != null) {
                m.setHasInvoice("1");
            }
            if (m.getUnitPriceInvoice() != null) {
                m.setHasInvoice("1");
            }
        }else{
            //如果是择期生效
            pcph.setEffectiveDate(pdMerchandise.getEffectiveDate());
            pcph.setEffectiveOption(pdMerchandise.getEffectiveOption());
            pcph.setInvoiceCategory(pdMerchandise.getInvoiceCategory());
            pcph.setTaxRate(pdMerchandise.getTaxRate());
            pcph.setUnitPrice(pdMerchandise.getUnitPrice());
            pcph.setUnitPriceNotax(pdMerchandise.getUnitPriceNotax());
            pcph.setUnitPriceInvoice(pdMerchandise.getUnitPriceInvoice());
            pcph.setUnitPriceNoinvoice(pdMerchandise.getUnitPriceNoinvoice());
            pcph.setUnitPriceReference(pdMerchandise.getUnitPriceReference());

            if ("2".equals(pcph.getType())) {
                if (pcph.getUnitPrice() == null) {
                    if (pcph.getUnitPriceReference() != null) {
                        pcph.setUnitPrice(pcph.getUnitPriceReference());
                    }
                    if (pcph.getUnitPriceNotax() != null) {
                        if (pcph.getUnitPriceNotax() != null) {
                            if (pcph.getTaxRate() != null) {
                                pcph.setUnitPrice(pcph.getUnitPriceNotax().add(pcph.getUnitPriceNotax().multiply(pcph.getTaxRate().divide(new BigDecimal(100)))));
                            } else {
                                pcph.setUnitPrice(pcph.getUnitPriceNotax());
                            }

                        }
                    }
                    if (pcph.getUnitPriceInvoice() != null) {
                        pcph.setUnitPrice(pcph.getUnitPriceInvoice());
                    }
                    if (pcph.getUnitPriceNoinvoice() != null) {
                        pcph.setUnitPrice(pcph.getUnitPriceNoinvoice());
                    }
                }
            }

            if (pcph.getUnitPrice() != null) {
                pcph.setHasInvoice("1");
            }

            if (pcph.getUnitPriceReference() != null) {
                pcph.setHasInvoice("0");
            }
            if (pcph.getUnitPriceNoinvoice() != null) {
                pcph.setHasInvoice("0");
            }
            if (pcph.getUnitPriceNotax() != null) {
                pcph.setHasInvoice("1");
            }
            if (pcph.getUnitPriceInvoice() != null) {
                pcph.setHasInvoice("1");
            }
            pcph.setOperation("D");
            pcph.setBasicReason(pdMerchandise.getBasicReason());
            if(StringUtils.isNotEmpty(pdMerchandise.getMemo())){
                pcph.setMemo(pdMerchandise.getMemo());
            }
            if(StringUtils.isNotEmpty(pdMerchandise.getPriceDesc())){
                pcph.setPriceDesc(pdMerchandise.getPriceDesc());
            }
        }

        m.setBasicReason(pdMerchandise.getBasicReason());
        if(StringUtils.isNotEmpty(pdMerchandise.getMemo())){
            m.setMemo(pdMerchandise.getMemo());
        }
        if(StringUtils.isNotEmpty(pdMerchandise.getPriceDesc())){
            m.setPriceDesc(pdMerchandise.getPriceDesc());
        }

        m.setOperation("D");




        productService.updatePdMerchandisePrice(m,pcph);
        return new JsonResult(1, null);
    }

    /*
     **
     *<AUTHOR>
     *获取其他价格信息
     */
    @ResponseBody
    @RequestMapping("/getOtherPrice.do")
    public JsonResult getOtherPrice(User user, Integer productId) {

        Map<String,Object> map=new HashMap<>();
        PdMerchandise m = productService.getProductOne(productId);
        PdMerchandiseHistory history= productService.pdMerchandiseHistory(productId,"D","1");
        if(history==null){
            map.put("isShow",false);
        }else{
            //如果立即生效，说明没有价格
            if("1".equals(history.getEffectiveOption())){
                map.put("isShow",false);
            }else{
                if(new Date().getTime()>history.getEffectiveDate().getTime()){
                    map.put("isShow",false);
                }else{
                    map.put("isShow",true);
                }
            }
        }

        map.put("data",history);
        return new JsonResult(1, map);
    }
    /*
     **
     *<AUTHOR>
     *获取开票信息修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getPdMerchandisePriceRecordList.do")
    public JsonResult getPdMerchandisePriceRecordList(Integer id) {

        List<PdMerchandiseHistory> list = productService.getPdMerchandisePriceRecordList(id,"D");


        Map<String, Object> map = new HashMap<>();
        if (list.size() == 1) {
            map.put("createName", list.get(0).getCreateName());
            map.put("createDate", list.get(0).getCreateDate());
            map.put("updateName", list.get(0).getUpdateName());
            map.put("updateDate", list.get(0).getUpdateDate());
            map.put("list", new ArrayList<>());
        } else if (list.size() > 1) {
            map.put("createName", list.get(list.size() - 1).getCreateName());
            map.put("createDate", list.get(list.size() - 1).getCreateDate());
            map.put("updateName", list.get(list.size() - 1).getUpdateName());
            map.put("updateDate", list.get(list.size() - 1).getUpdateDate());
            for (int i = 0; i <list.size()-1 ; i++) {
                list.get(i).setEndDate(list.get(i+1).getUpdateDate());
            }
            map.put("list", list);
        } else {
            map.put("createName", null);
            map.put("createDate", null);
            map.put("updateName", null);
            map.put("updateDate", null);
            map.put("priceReason", null);
            map.put("basicReason", null);
            map.put("financerName", null);
            map.put("financerTime", null);
            map.put("list", list);
        }
        return new JsonResult(1, map);
    }

    /**
     * 查看商品基本信息修改详情
     */
    @ResponseBody
    @RequestMapping("/getPdMerchandisePriceRecordDetails.do")
    public JsonResult getPdMerchandisePriceRecordDetails(Integer id) {

        PdMerchandiseHistory history = productService.getPdMerchandisePriceRecordDetails(id);
        return new JsonResult(1, history);
    }


    /*
     **
     *<AUTHOR>
     *移除，删除产品图纸
     */
    @ResponseBody
    @RequestMapping("/removePdBaseImage.do")
    public JsonResult removePdBaseImage(User user,Integer product, Integer category) {
        String res = productService.removePdBaseImage(product, category,user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     *<AUTHOR>
     *上传，更换产品图纸
     */
    @ResponseBody
    @RequestMapping("/saveOrUpdatePdBaseImage.do")
    public JsonResult saveOrUpdatePdBaseImage(PdImage pdImage, User user) {
        String res = productService.saveOrUpdatePdBaseImage(pdImage, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     *<AUTHOR>
     *获取图纸操作记录
     */
    @ResponseBody
    @RequestMapping("/getPdBaseImageRecord.do")
    public JsonResult getPdBaseImageRecord(Integer product, Integer category) {
        List<PdImageHistory> list = productService.getPdBaseImageRecord(product, category);
        return new JsonResult(1, list);
    }

    /*
     **
     *<AUTHOR>
     *资源文件关联
     */
    @ResponseBody
    @RequestMapping("/saveOrUpdatePdResource.do")
    public JsonResult saveOrUpdatePdResource(PdResource pdResource, User user) {
        String res = productService.saveOrUpdatePdResource(pdResource, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }


    /*
     **
     *<AUTHOR>
     *解除文关联
     */
    @ResponseBody
    @RequestMapping("/securePdResource.do")
    public JsonResult securePdResource(Integer product, Integer resourceId, User user) {
        String res = productService.securePdResource(product, resourceId, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     *<AUTHOR>
     *获取资源文件操作记录
     */
    @ResponseBody
    @RequestMapping("/getPdResourceRecord.do")
    public JsonResult getPdResourceRecord(Integer product) {
        List<PdResourceHistory> list = productService.getPdResourceRecord(product);
        return new JsonResult(1, list);
    }





    private Map<String, Object> getRecord(List<Map<String, Object>> list) {
        Map map = new HashMap();
        if (list.size() == 1) {
            map.put("createName", list.get(0).get("createName"));
            map.put("createDate", list.get(0).get("createDate"));
            map.put("updateName", list.get(0).get("updateName"));
            map.put("updateDate", list.get(0).get("updateDate"));
            map.put("priceReason", list.get(0).get("priceReason"));
            map.put("basicReason", list.get(0).get("basicReason"));
            map.put("financerName", list.get(0).get("financerName"));
            map.put("financerTime", list.get(0).get("financerTime"));
            map.put("memo", list.get(0).get("memo"));
            map.put("list", new ArrayList<>());
        } else if (list.size() > 1) {
            map.put("createName", list.get(list.size() - 1).get("createName"));
            map.put("createDate", list.get(list.size() - 1).get("createDate"));
            map.put("updateName", list.get(list.size() - 1).get("updateName"));
            map.put("updateDate", list.get(list.size() - 1).get("updateDate"));
            map.put("priceReason", list.get(list.size() - 1).get("priceReason"));
            map.put("basicReason", list.get(list.size() - 1).get("basicReason"));
            map.put("financerName", list.get(list.size() - 1).get("financerName"));
            map.put("financerTime", list.get(list.size() - 1).get("financerTime"));
            map.put("memo", list.get(list.size() - 1).get("memo"));
            map.put("list", list);
        } else {
            map.put("createName", null);
            map.put("createDate", null);
            map.put("updateName", null);
            map.put("updateDate", null);
            map.put("priceReason", null);
            map.put("basicReason", null);
            map.put("financerName", null);
            map.put("financerTime", null);
            map.put("memo", null);
            map.put("list", list);
        }

        return map;
    }

    @Scheduled(cron = "0 0 10 * * ?")
    public void taskCycle() {
        List<Map<String, Object>> orgList = productService.getOrg();
        for (Map<String, Object> o : orgList) {
            //拥有成品库权限的用户
            List<UserDto> xbList = userPopedomService.getUserByOidMid((Integer) o.get("id"), "xb");
            //拥有关联管理权限的用户
            List<UserDto> wcList = userPopedomService.getUserByOidMid((Integer) o.get("id"), "wc");
            List<Map<String, Object>> tj = productService.zrtj((Integer) o.get("id"));
            for (Map<String, Object> m : tj) {
                String s = m.get("userName") + "于" + m.get("date") + "录入了" + m.get("sum") + "种商品,您需及时在系统中录入初始库存。";
                for (UserDto u : xbList) {
                    List<UserSuspendMsg> l = suspendMsgService.getByContentAndToday(s, u.getUserID());
                    if (l.size() == 0) {
                        suspendMsgService.saveUserSuspendMsg(1, s, "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), u.getUserID(), null, 0);
                    }

                }
            }

            for (Map<String, Object> m : tj) {
                String s = m.get("userName") + "于" + m.get("date") + "录入了" + m.get("sum") + "种商品,请及时在“关联管理”模块中将商品与产品关联。";
                for (UserDto u : wcList) {
                    List<UserSuspendMsg> l = suspendMsgService.getByContentAndToday(s, u.getUserID());
                    if (l.size() == 0) {
                        suspendMsgService.saveUserSuspendMsg(1, s, "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), u.getUserID(), null, 0);
                    }

                }
            }
        }

    }
}
