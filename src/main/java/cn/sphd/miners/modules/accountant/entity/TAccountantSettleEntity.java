package cn.sphd.miners.modules.accountant.entity;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by root on 17-2-8.
 */
@Entity
@Table(name = "t_accountant_settle", schema = "miners", catalog = "")
public class TAccountantSettleEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private int id;
    private String settletime;
    private String addtime;
    private Integer oid;

    @Id
    @Column(name = "id", nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


    @Basic
    @Column(name = "settletime", nullable = true)
    public String getSettletime() {
        return settletime;
    }

    public void setSettletime(String settletime) {
        this.settletime = settletime;
    }

    @Basic
    @Column(name = "addtime", nullable = true)
    public String getAddtime() {
        return addtime;
    }

    public void setAddtime(String addtime) {
        this.addtime = addtime;
    }


    @Basic
    @Column(name = "oid", nullable = true)
    public Integer getOid() {
        return oid;
    }

    public void setOid(Integer oid) {
        this.oid = oid;
    }
}
