package cn.sphd.miners.modules.inStockCheck.service;

import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory;

import java.util.List;

public interface TMtStockFlowSettingHistoryService{

    int deleteByPrimaryKey(Integer id);

    int insert(TMtStockFlowSettingHistory record);

    int insertSelective(TMtStockFlowSettingHistory record);

    TMtStockFlowSettingHistory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TMtStockFlowSettingHistory record);

    int updateByPrimaryKey(TMtStockFlowSettingHistory record);

    List<TMtStockFlowSettingHistory> getHistoriesByFlowId(Integer id);

}
