package cn.sphd.miners.modules.investigate.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-08-01 
 */

@Entity ( name ="InvestigateQuestion" )
@Table ( name ="t_investigate_question" )
public class InvestigateQuestion  implements Serializable {

	private static final long serialVersionUID =  499095064700057334L;

	/**
	 * ID
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 问卷ID
	 */
   	@Column(name = "subject" )
	private Integer subject;

	/**
	 * 类型:1-问答题,2-判断题,3-单选题,4-多选题,9特殊题
	 */
   	@Column(name = "type" )
	private String type;

	/**
	 * 父试题ID
	 */
   	@Column(name = "parent_question" )
	private Integer parentQuestion;

	/**
	 * 父试题应答
	 */
   	@Column(name = "parent_key" )
	private Integer parentKey;

	/**
	 * 类型;1-普通,2-条件
	 */
   	@Column(name = "category" )
	private String category;

	/**
	 * 标签ID
	 */
   	@Column(name = "tag" )
	private Integer tag;

	/**
	 * 代码
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 是否基本信息(与基本信息列在一起)
	 */
   	@Column(name = "is_essential" )
	private Integer isEssential;

	/**
	 * 是否必填
	 */
   	@Column(name = "is_required" )
	private Integer isRequired;

	/**
	 * 是否展示
	 */
   	@Column(name = "is_show" )
	private Integer isShow;
	/**
	 * 是否多输入框:0-否,n-最大选项数
	 */
	@Column(name = "is_multiple" )
	private Integer isMultiple;
	/**
	 * 是否多个附件:0-否,n-最大上传个数
	 */
	@Column(name = "is_attachment" )
	private Integer isAttachment;
	/**
	 * 内容
	 */
	@Column(name = "attach_tab" )
	private String attachTab;
	/**
	 * 内容
	 */
	@Column(name = "special_model" )
	private String specialModel;
	/**
	 * 内容
	 */
	@Column(name = "special_tab" )
	private String specialTab;
	/**
	 * 内容
	 */
	@Column(name = "reg_exp" )
	private String regExp;

	/**
	 * 是否启用
	 */
   	@Column(name = "enabled" )
	private Integer enabled;

	/**
	 * 启/停用时间
	 */
   	@Column(name = "enabled_time" )
	private Date enabledTime;

	/**
	 * 内容
	 */
   	@Column(name = "content" )
	private String content;

	/**
	 * 排序
	 */
   	@Column(name = "orders" )
	private Integer orders;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getSubject() {
		return this.subject;
	}

	public void setSubject(Integer subject) {
		this.subject = subject;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Integer getParentQuestion() {
		return this.parentQuestion;
	}

	public void setParentQuestion(Integer parentQuestion) {
		this.parentQuestion = parentQuestion;
	}

	public Integer getParentKey() {
		return this.parentKey;
	}

	public void setParentKey(Integer parentKey) {
		this.parentKey = parentKey;
	}

	public String getCategory() {
		return this.category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Integer getTag() {
		return this.tag;
	}

	public void setTag(Integer tag) {
		this.tag = tag;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getIsEssential() {
		return this.isEssential;
	}

	public void setIsEssential(Integer isEssential) {
		this.isEssential = isEssential;
	}

	public Integer getIsRequired() {
		return this.isRequired;
	}

	public void setIsRequired(Integer isRequired) {
		this.isRequired = isRequired;
	}

	public Integer getIsShow() {
		return this.isShow;
	}

	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}

	public Integer getIsMultiple() {
		return isMultiple;
	}

	public void setIsMultiple(Integer isMultiple) {
		this.isMultiple = isMultiple;
	}

	public Integer getIsAttachment() {
		return isAttachment;
	}

	public void setIsAttachment(Integer isAttachment) {
		this.isAttachment = isAttachment;
	}

	public String getAttachTab() {
		return attachTab;
	}

	public void setAttachTab(String attachTab) {
		this.attachTab = attachTab;
	}

	public String getSpecialModel() {
		return specialModel;
	}

	public void setSpecialModel(String specialModel) {
		this.specialModel = specialModel;
	}

	public String getSpecialTab() {
		return specialTab;
	}

	public void setSpecialTab(String specialTab) {
		this.specialTab = specialTab;
	}

	public String getRegExp() {
		return regExp;
	}

	public void setRegExp(String regExp) {
		this.regExp = regExp;
	}

	public Integer getEnabled() {
		return this.enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Date getEnabledTime() {
		return this.enabledTime;
	}

	public void setEnabledTime(Date enabledTime) {
		this.enabledTime = enabledTime;
	}

	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Integer getOrders() {
		return this.orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
