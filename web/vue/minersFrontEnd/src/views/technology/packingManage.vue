<template>
  <div id="packingManage">
    <div v-if="mainNum === 1">
      <div class="unSection">
        <div class="ty-left">待设置包装信息的商品</div>
        <span>共{{wszNum}}种</span>
        <el-link type="primary" @click="initWszPage(1)">去设置</el-link>
      </div>
      <div class="lineQuery ty-clear">
        <div class="ty-left queryTip">以下为包装信息已设置的商品</div>
        <div class="ty-right searchSect">
          <div class="ty-left keywordSearch">
            <span class="searchIcon" :style="{backgroundImage:'url('+ searchIcon +')'}"></span>
            <input placeholder="请输入要查找商品的代号或名称" id="searchKeyBase1" v-model="searchKeyHome" />
          </div>
          <span class="ty-left ty-btn ty-btn-blue ty-btn-big searchBtn" @click="initPackingHome">确 定</span>
        </div>
      </div>
      <div>
        <table class="ty-table ty-table-control bg-yellow" id="packList">
          <tbody>
          <tr>
            <td>商品图号</td>
            <td>商品名称/规格/型号</td>
            <td>计量单位</td>
            <td>所属客户</td>
            <td>包装概述</td>
            <td>操作</td>
          </tr>
          <tr  v-for="(item, index) in goodsList" v-bind:key="index">
            <td>{{ item.outerSn }}</td>
            <td>{{ item.outerName }}/{{ item.specifications || '' }}/{{ item.model || '' }}</td>
            <td>{{ item.unit || '' }}</td>
            <td>{{ item.customerName || '' }}</td>
            <td>{{ item.gs}}</td>
            <td class="ty-td-control">
              <span @click="scanPackage(item)" class="ty-color-blue">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
        <TyPage v-if="goodsList.length > 0"
                :curPage="mainNum1Page.currentPageNo" :pageSize="mainNum1Page.pageSize"
                :allPage="mainNum1Page.totalPage" :pageClickFun="mainNum1PageFun"></TyPage>
      </div>
    </div>
    <div v-if="mainNum === 2">
      <div class="backPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(1);">返 回</span>
      </div>
      <div class="lineQuery ty-clear">
        <div class="ty-left queryTip">以下为包装信息有待设置的商品</div>
        <div class="ty-right searchSect">
          <div class="ty-left keywordSearch">
            <span class="searchIcon" :style="{backgroundImage:'url('+ searchIcon +')'}"></span>
            <input placeholder="请输入要查找商品的代号或名称" v-model="searchKeyWsz" />
          </div>
          <span class="ty-left ty-btn ty-btn-blue ty-btn-big searchBtn" @click="initWszPage(1)">确 定</span>
        </div>
      </div>
      <table class="ty-table ty-table-control bg-yellow" id="commodityListN">
        <tbody>
        <tr>
          <td width="14%">商品图号</td>
          <td width="18%">商品名称/规格/型号</td>
          <td width="8%">计量单位</td>
          <td width="8%">所属客户</td>
          <td width="18">创建</td>
          <td width="10%">操作</td>
        </tr>
        <tr v-for="(item, index) in wszGoodsList" v-bind:key="index">
          <td>{{ item.outerSn }}</td>
          <td>{{ item.outerName || '' }}/{{ item.specifications || '' }}/{{ item.model || '' }}</td>
          <td>{{ item.unit || '' }}</td>
          <td>{{ item.customerName || '' }}</td>
          <td>{{ item.createName	}} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')	}}</td>
          <td class="ty-td-control">
            <span @click="packingSetting(item)" class="ty-color-blue">设置</span>
          </td>
        </tr>
        </tbody>
      </table>
      <TyPage v-if="wszGoodsList.length > 0"
              :curPage="mainNum2Page.currentPageNo" :pageSize="mainNum2Page.pageSize"
              :allPage="mainNum2Page.totalPage" :pageClickFun="mainNum2PageFun"></TyPage>
    </div>
    <div v-if="mainNum === 3">
      <div class="backPage ty-clear">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(1);">返 回</span>
      </div>
      <div class="topName">{{ commodityListInfo.outerName }}/{{ commodityListInfo.outerSn }}/{{ commodityListInfo.specifications || '' }}/{{ commodityListInfo.model || ''}}/{{ commodityListInfo.unit || '' }}</div>
      <div class="topName ty-clear">
        <span class="sc_mode1" v-if="Number(this.packagingState) === 1">本商品已创建了<span class="packageKinds">{{ cmPackingList.length }}</span>种包装方式。</span>
        <span class="sc_mode2" v-if="Number(this.packagingState) === 2">本商品包装方式中，<span v-if="usingAble">在用的有{{ cmPackingList.length }}种，</span>已停用的有<span class="susKinds">{{ suspendList.length }}</span>种。</span>
        <div class="ty-right">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 susPackingList gapRt" v-if="Number(this.packagingState) === 2" @click="susPackingList()">已停用的数据</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapRt" @click="updatePackingTip(1)">改为无需包装</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="addPacking()">新增包装方式</span>
        </div>
      </div>
      <div class="mainOnlyRow" data-id="" v-if="Number(isPacking) === 1 && cmPackingList.length > 0">
        <div class="linkWrap gapBt">
          以下为<span class="createInfo">{{cmPackingList[0].createName}} {{new Date(cmPackingList[0].createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>创建的包装方式。
          <span class="linkBtn gap" @click="updatePackage(0)">编辑</span>
          <span class="redLinkBtn susPackage" @click="updatePackingTip(3, 0)">停用</span>
          <span class="linkBtn updateRecord" @click="getUpdateRecord(cmPackingList[0], 0)">操作记录</span>
          <span class="ty-right linkBtn" @click="scanMorePacking" id="scanMore">查看更多的包装方式</span>
        </div>
        <table class="ty-table ty-table-control">
          <tbody>
          <tr>
            <td>长宽高</td>
            <td>形状</td>
            <td>装有本材料</td>
            <td>净重</td>
            <td>毛重</td>
            <td>包装方式共几层</td>
          </tr>
          <tr>
            <td>{{cmPackingList[0].outerLength || ''}}* {{cmPackingList[0].outerWidth || ''}}*{{cmPackingList[0].outerHeight || ''}}</td>
            <td>{{charge(cmPackingList[0].outerShape, 'outerShape')}}</td>
            <td>{{cmPackingList[0].structureList[cmPackingList[0].structureList.length-1].modNum}}{{ commodityListInfo.unit }}</td>
            <td>{{cmPackingList[0].structureList[cmPackingList[0].structureList.length-1].modNetWeight}}
              {{cmPackingList[0].structureList[cmPackingList[0].structureList.length-1].modWeightUnit}}</td>
            <td>{{Number(cmPackingList[0].structureList[cmPackingList[0].structureList.length-1].modGrossWeight).toFixed(2)}}
              {{cmPackingList[0].structureList[cmPackingList[0].structureList.length-1].modWeightUnit}}</td>
            <td>{{cmPackingList[0].structureList.length}}层 </td>
          </tr>
          </tbody>
        </table>
        <div id="pckLevels">
          <div class="pckRow" v-for="(structure, keys) in cmPackingList[0].structureList" v-bind:key="keys">
            <span class="gapTtl" v-if="cmPackingList[0].structureList.length > 1 && keys === 0">最小包装</span>
            <span class="gapTtl" v-else-if="keys === cmPackingList[0].structureList.length - 1">最外层包装</span>
            <span class="gapTtl" v-else>最小包装的上{{chargeLevel(keys)}}层包</span>
            主要使用{{structure.zyPackaging.name}}包装，辅助包装物共{{structure.itemList.length}}种
            <span class="ty-right linkBtn" @click="scanItemPack(structure, 0, keys)">查看</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="mainNum === 4">
      <div class="backPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(1);">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(3);">返回上一页</span>
      </div>
      <div class="topName">{{ commodityListInfo.outerName }}/{{ commodityListInfo.outerSn }}/{{ commodityListInfo.specifications || '' }}/{{ commodityListInfo.model || ''}}/{{ commodityListInfo.unit || '' }}</div>
      <div class="topName ty-clear">
        <span class="sc_mode1" v-if="Number(this.packagingState) === 1">本商品已创建了<span class="packageKinds">{{ cmPackingList.length }}</span>种包装方式，您可对各种包装方式进行编辑或删除。</span>
        <span class="sc_mode2" v-else>本商品包装方式中，<span class="usingCon" v-if="usingAble">在用的有<span>{{ cmPackingList.length }}</span>种，</span>已停用的有<span class="susKinds">{{ suspendList.length }}</span>种。</span>
      </div>
      <div class="mainRows" id="scanMoreCon">
        <div class="packageScanItem" data-id="{{item.id}}" v-for="(item, index) in scanMoreList" v-bind:key="index">
          <div class="linkWrap clear gapBt">
            以下为<span>{{item.createName}} {{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>创建的包装方式。
            <div class="ty-right">
              <span class="linkBtn gap" @click="updatePackage(index)">编辑</span>
              <span class="redLinkBtn susPackage" @click="updatePackingTip(3, index)" data-type="{{usingAble? 0: 1}}">{{usingAble? '停用': '启用'}}</span>
              <span class="linkBtn updateRecord" @click="getUpdateRecord(item, index)">操作记录</span>
            </div>
          </div>
          <table class="ty-table ty-table-control">
            <tbody>
            <tr>
              <td>长宽高</td>
              <td>形状</td>
              <td>装有本材料</td>
              <td>净重</td>
              <td>毛重</td>
              <td>包装方式共几层</td>
            </tr>
            <tr>
              <td>{{item.outerLength || ''}}*{{item.outerWidth || ''}}* {{item.outerHeight || ''}}</td>
              <td>{{charge(item.outerShape, 'outerShape')}}</td>
              <td>{{item.structureList[item.structureList.length-1].modNum}}{{item.unit}}</td>
              <td>{{item.structureList[item.structureList.length-1].modNetWeight}}{{item.structureList[item.structureList.length-1].modWeightUnit}}</td>
              <td>{{Number(item.structureList[item.structureList.length-1].modGrossWeight).toFixed(2)}}{{item.structureList[item.structureList.length-1].modGrossUnit}}</td>
              <td>{{item.structureList.length}}层</td>
            </tr>
            </tbody>
          </table>
          <div class="pckRow" v-for="(structure, keys) in item.structureList" v-bind:key="keys">
            <span class="gapTtl" v-if="item.structureList.length > 1 && keys === 0">最小包装</span>
            <span class="gapTtl" v-else-if="keys === item.structureList.length - 1">最外层包装</span>
            <span class="gapTtl" v-else>最小包装的上{{chargeLevel(keys)}}层</span>
            主要使用{{structure.zyPackaging.name}}包装，辅助包装物共{{structure.itemList.length}}种
            <span class="ty-right linkBtn" @click="scanItemPack(structure, index, keys)">查看</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="mainNum === 5">
      <div class="backPage">
        <input type="hidden" v-model="backNum" />
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(1);">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(8);">返回上一页</span>
      </div>
      <div class="limitWid"><p>包装方式的操作记录</p></div>
      <div class="limitWid">
        <span>商品：</span>{{ commodityListInfo.outerName }}/{{ commodityListInfo.outerSn }}/{{ commodityListInfo.specifications || '' }}/{{ commodityListInfo.model || ''}}/{{ commodityListInfo.unit || '' }}
      </div>
      <div class="limitWid">
        <span>客户：</span><span class="sc_commodityCus">{{ commodityListInfo.customerName || '' }}</span>
      </div>
      <div>
        <table class="ty-table ty-table-control" id="recordList">
          <tbody>
          <tr>
            <td>属性</td>
            <td>操作者及时间</td>
            <td>生效日期</td>
            <td>详情查看</td>
          </tr>
          <tr v-for="(item, index) in recordList" v-bind:key="index">
            <td>{{operation[item.operation]}}</td>
            <td>{{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</td>
            <td>{{ new Date(item.effectTime).format('yyyy-MM-dd') }}</td>
            <td>
              <span class="ty-color-blue" @click="updateRecordScan(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div v-if="mainNum === 6">
      <div class="backPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(1);">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(5);">返回上一页</span>
      </div>
      <div class="mainRows recordScan">
        <table class="ty-table ty-table-control">
          <tbody>
          <tr>
            <td>长宽高</td>
            <td>形状</td>
            <td>装有本材料</td>
            <td>净重</td>
            <td>毛重</td>
            <td>包装方式共几层</td>
          </tr>
          <tr>
            <td>{{recordScanItem.outerLength || ''}}*{{recordScanItem.outerWidth || ''}}*{{recordScanItem.outerHeight || ''}}</td>
            <td>{{charge(recordScanItem.outerShape, 'outerShape')}}</td>
            <td>{{recordScanItem.structureList[recordScanItem.structureList.length-1].modNum}}{{commodityListInfo.unit}}</td>
            <td>{{recordScanItem.structureList[recordScanItem.structureList.length-1].modNetWeight}}
              {{recordScanItem.structureList[recordScanItem.structureList.length-1].modWeightUnit}}</td>
            <td>{{Number(recordScanItem.structureList[recordScanItem.structureList.length-1].modGrossWeight).toFixed(2)}}
              {{recordScanItem.structureList[recordScanItem.structureList.length-1].modGrossUnit}}</td>
            <td>{{recordScanItem.structureList.length}}层</td>
          </tr>
          </tbody>
        </table>
        <div class="pckRow" v-for="(structure, keys) in recordScanItem.structureList" v-bind:key="keys">
          <span class="gapTtl" v-if="recordScanItem.structureList.length > 1 && keys === 0">最小包装</span>
          <span class="gapTtl" v-else-if="keys === recordScanItem.structureList.length - 1">最外层包装</span>
          <span class="gapTtl" v-else>最小包装的上{{chargeLevel(keys) }}层</span>
          主要使用{{structure.zyPackaging.name}}包装，辅助包装物共{{structure.itemList.length}}种
        </div>
      </div>
    </div>
    <div v-if="mainNum === 7">
      <div class="backPage ty-clear">
        <input type="hidden" v-model="backPreNum" />
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(1);">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="backPre(9);">返回上一页</span>
        <el-button class="ty-right" type="primary" :disabled="packagingState===0 && modelPattern === 0" @click="commodityPackingOk">确 定</el-button>
      </div>
      <div v-if="packagingState===0">
        <div class="modeSelect sectCon">
          <span class="spTtl">本商品是否需要包装？<span class="red">*</span></span>
          <span class="modelPattern" @click="togglePattern(1)"><span :class="{'fa': true,' fa-circle': Number(modelPattern) === 1, ' fa-circle-o': modelPattern !== 1}"></span>需要</span>
          <span class="modelPattern" @click="togglePattern(2)"><span :class="{'fa': true,' fa-circle': Number(modelPattern) === 2, ' fa-circle-o': modelPattern !== 2}"></span>不需要</span>
        </div>
        <div class="hrLine"></div>
      </div>
      <div v-if="packagingState===0 && Number(modelPattern) === 1 || packagingState===2">
        <div class="ty-clear sectCon">
          <span>请先编辑最小包装，之后如必要，可“增加一层包装”，直至最外层的包装。</span>
          <span class="linkBtn ty-right" @click="addMoreLayer">增加一层包装</span>
        </div>
        <div class="hrLine"></div>
        <div class="sectCon">
          <table class="ty-table ty-table-control bg-yellow" id="packList">
            <tbody>
            <tr>
              <td>包装级别</td>
              <td>主包装物</td>
              <td>辅助包装物</td>
              <td>包含上一层小包装的个数</td>
              <td>单个包装内商品的数量</td>
              <td>单个包装的净重</td>
              <td>单个包装的毛重</td>
              <td>操作</td>
            </tr>
            <tr  v-for="(item, index) in cmPackingStructureList" v-bind:key="index">
              <td>
                <span v-if="index === 0">最小包装</span>
                <span v-else-if="index > 0 && index === cmPackingStructureList.length - 1">最外层包装</span>
                <span v-else>最小包装的上{{chargeLevel(index) }}层</span>
              </td>
              <td>
                <span>
                  {{item.zyPackaging.code? item.zyPackaging.code+'/': ''}}
                  {{item.zyPackaging.name? item.zyPackaging.name+'/': ''}}
                  {{item.zyPackaging.model? item.zyPackaging.model+'/': ''}}
                  {{item.zyPackaging.specifications? item.zyPackaging.specifications+'/': ''}}
                  {{item.zyPackaging.unit || ''}}
                </span>
              </td>
              <td class="ty-td-control">
                <span class="ty-color-blue" v-if="item.itemList.length > 0" @click="scanSubPackList(index)">
                  {{  item.itemList.length }}种
                </span>
              </td>
              <td> {{ index === 0 ? '—': item.productCount }}</td>
              <td>{{ index === 0 ? item.productCount: item.modNum }}</td>
              <td>{{ item.modNetWeight }}{{ item.modWeightUnit }}</td>
              <td>{{ item.modGrossWeight }}{{ item.modGrossUnit }}</td>
              <td>
                <span class="ty-color-blue" @click="editPackingInfo(item,index)">编辑</span>
                <span class="ty-color-red" @click="delThisLayer(index)" v-if="index > 0 && cmPackingStructureList.length-1 === index">删除本层</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <!-- 1=改为无需包装,2=删除、3=停用   -->
    <TyDialog v-if="tipVisible" width="800" dialogTitle="提示" :dialogHide="hideFun" :dialogName="'tipVisible'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('tipVisible')">取消</el-button>
        <el-button class="bounce-ok" @click="withoutPackageOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <div class="ty-center" v-if="Number(updatePackingKind) === 1">
            <span v-if="Number(packagingState) === 2">确定后，在用的包装方式均将停用！</span>
            <span v-else>确定后，已编辑的包装方式均将消失不见！</span>
          </div>
          <div class="ty-center" v-else-if="Number(updatePackingKind) === 2">确定删除本种包装方式吗？</div>
          <div class="ty-center" v-else-if="Number(updatePackingKind) === 3">
            <span v-if="usingAble">确定停用这种包装方式吗？</span>
            <span v-else>确定启用这种包装方式吗？</span>
          </div>
        </div>
      </template>
    </TyDialog>
    <!--  包装设置  -->
    <TyDialog v-if="settingVisible" width="760" dialogTitle="本层包装的信息" :dialogHide="hideFun" :dialogName="'settingVisible'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('settingVisible')">取消</el-button>
        <el-button class="bounce-ok" @click="structureSure()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="wrapLab">
          <el-form
              ref="structureFrom"
              :model="structureFrom"
              :rules="structureFromrules"
              label-width="200px"
              label-position="left"
              style="width: 500px"
          >
            <el-form-item label="主要包装物" prop="zyPackaging">
              <el-input v-model="structureFrom.zyPackaging" type="hidden" />
              <el-col :span="16">{{structureFrom.zyPackaging === ''?'尚未选择': structureFrom.zyPackaging.code +'/'+structureFrom.zyPackaging.name +'/'+(structureFrom.zyPackaging.model||'') +'/'+
                  (structureFrom.zyPackaging.specifications||'') +'/'+structureFrom.zyPackaging.unit}}</el-col>
              <el-col :span="8" class="txtRight"><el-link type="primary" :underline="false" @click="selectPackage(1,'update')">选择</el-link></el-col>
            </el-form-item>
            <el-form-item label="辅助包装物">
              <el-input v-model="structureFrom.itemList" type="hidden" />
              <el-col :span="14">{{ structureFrom.itemList.length === 0 ?'尚未选择': '已选'+ structureFrom.itemList.length +'种'}}</el-col>
              <el-col :span="6" class="txtRight"><el-link type="primary" :underline="false" @click="scanSubPackList()">查看/管理</el-link></el-col>
              <el-col :span="4" class="txtRight"><el-link type="primary" :underline="false" @click="selectPackage(2,'add')">增加</el-link></el-col>
            </el-form-item>
            <el-form-item :label="level > 0?'包含几个上一层的小包装':'单个包装内商品的数量'" prop="productCount" class="unitPos">
              <el-input v-model="structureFrom.productCount" placeholder="请录入数字"  @input="limitNum('productCount', 2)" clearable/>
              <span class="matUnit">{{ level > 0? '个' : commodityListInfo.unit }}</span>
            </el-form-item>
            <el-form-item v-if="level > 0" label="单个包装内商品的数量" prop="productCount" class="unitPos">
              <el-input v-model="structureFrom.modNum" :disabled="level > 0"/>
              <span class="matUnit">{{commodityListInfo.unit}}</span>
            </el-form-item>
            <el-form-item label="单个包装的净重" class="unitPos">
              <el-input v-model="structureFrom.modNetWeight" disabled />
              <span class="matUnit">{{ structureFrom.modWeightUnit }}</span>
            </el-form-item>
            <el-form-item label="单个包装的毛重" class="unitPos">
              <el-input v-model="structureFrom.modGrossWeight" disabled />
              <span class="matUnit">{{ structureFrom.modGrossUnit}}</span>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <!--  商品最外层包装的信息   -->
    <TyDialog v-if="outerBaseLog" width="680" dialogTitle="商品最外层包装的信息" :dialogHide="hideFun" :dialogName="'outerBaseLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('outerBaseLog')">取消</el-button>
        <el-button class="bounce-ok" @click="outerBaseOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="fmWrapper">
          <div>请录入本商品最外层包装的外廓信息</div>
          <div class="bTip">注：各尺寸的单位均默认为m，如不适用，请更换！</div>
          <el-form ref="outerBaseForm" :model="outerBaseForm">
            <ul class="outer_form">
              <li>
                <div>摆放于货架之后长度方向的尺寸
                  <span class="ty-color-gray ty-right">查看图例</span>
                </div>
                <input class="ty-inputText" @input="limitNum('outerLength', 4)" placeholder="请录入数字" v-model="outerBaseForm.outerLength" require />
                <select v-model="outerBaseForm.lengthUnitId" require>
                  <option value="3">m</option>
                  <option value="2">cm</option>
                  <option value="1">mm</option>
                </select>
              </li>
              <li>
                <div>
                  摆放于货架之后宽度方向的尺寸
                  <span class="ty-color-gray ty-right">查看图例</span>
                </div>
                <input class="ty-inputText" @input="limitNum('outerWidth', 4)" placeholder="请录入数字" v-model="outerBaseForm.outerWidth" require/>
                <select v-model="outerBaseForm.widthUnitId" require>
                  <option value="3">m</option>
                  <option value="2">cm</option>
                  <option value="1">mm</option>
                </select>
              </li>
              <li>
                <div>
                  摆放于货架之后高度方向的尺寸
                  <span class="ty-color-gray ty-right">查看图例</span>
                </div>
                <input class="ty-inputText" @input="limitNum('outerHeight', 4)" placeholder="请录入数字" v-model="outerBaseForm.outerHeight" require />
                <select v-model="outerBaseForm.heightUnitId" require>
                  <option value="3">m</option>
                  <option value="2">cm</option>
                  <option value="1">mm</option>
                </select>
              </li>
            </ul>
            <div class="shapeCon">
              <p>本商品最外层包装是什么形状？请选择：</p>
              <div class="outerShape">
                <div><input type="radio" v-model="outerBaseForm.outerShape" value="1" @click="outerShapeTogg"/>长方体</div>
                <div><input type="radio" v-model="outerBaseForm.outerShape" value="2" @click="outerShapeTogg"/>圆柱体</div>
                <div><input type="radio" v-model="outerBaseForm.outerShape" value="3" @click="outerShapeTogg"/>其他形状</div>
              </div>
            </div>
            <div class="effectTimeCon" v-if="packagingState === 2 && pEditType === 'update'">
              <p>本此次修改从何时开始生效？{{outerBaseForm.effectTime}}</p>
              <el-date-picker v-model="outerBaseForm.effectTime" type="date" placeholder="选择日期" id="outerEffect" size="large"
                              :disabled-date="disabledDate"
                              format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </div>
          </el-form>
        </div>
      </template>
    </TyDialog>

    <!--  查看/管理本层包装的辅助包装物  -->
    <TyDialog v-if="scanSubLog" width="1000" dialogTitle="查看/管理本层包装的辅助包装物" :dialogHide="hideFun" :dialogName="'scanSubLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('scanSubLog')">关  闭</el-button>
      </template>
      <template #dialogBody>
        <el-row>
          <el-col :span="12">本层包装的辅助包装物已有如下<span class="subPackLen">{{structureFrom.itemList.length}}</span>种</el-col>
          <el-col :span="12" class="txtRight">
            <el-link type="primary" :underline="false" class="gapRt" @click="selectPackage(3, 'add')">增加辅助包装物</el-link>
          </el-col>
        </el-row>
        <table class="ty-table ty-table-control" id="scanSubPackList">
          <tbody>
          <tr>
            <td width="30%">包装物</td>
            <td width="15%">使用时的理论用量</td>
            <td width="20%">本层所用本包装物总重</td>
            <td width="15%">出材量</td>
            <td width="20%">操作</td>
          </tr>
          <tr  v-for="(item, index) in structureFrom.itemList" v-bind:key="index">
            <td>{{item.code}}/{{item.name}}/{{item.specifications ||''}}/{{item.model ||''}}/{{item.unit ||''}}</td>
            <td>{{ item.ratedAmout }}</td>
            <td>{{ Number(item.weightReference).toFixed(2) }}{{ charge(item.weightReferenceUnitId, 'weightUnit') }}</td>
            <td>{{ item.volum || ""}}</td>
            <td>
              <span class="ty-color-blue" @click="selectPackage(3, 'update', index)" data-source="3" data-type="update">修改</span>
              <span class="ty-color-red" @click="delTip( index)">删除</span>
            </td>
          </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>
    <!--  选择/更换本层包装的主要包装物   -->
    <TyDialog v-show="selectPackageLog" width="980" :dialogTitle="selectDialogTtl" :dialogHide="hideFun" :dialogName="'selectPackageLog'" >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('selectPackageLog')">取消</el-button>
        <el-button class="bounce-ok" @click="selectPackageSure">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="packingSelectForm" :model="packingSelectForm"  label-width="880px">
          <div class="modInput">
            <table class="pannelTab">
              <tr>
                <td>
                  <div class="tbTtl">
                    包装物代号<i class="red">*</i>
                    <span class="linkBtn ty-right" @click="addPackage">增加包装物</span>
                  </div>
                  <select name="material" v-model="packingSelectForm.material" @change="changePackageName()" class="ty-inputSelect"
                          :disabled="selectPackageSource.source === 3 && selectPackageSource.type === 'update' ? true: false"
                  >
                    <option value="">请选择</option>
                    <option
                        v-for="item in packageNameList"
                        :key="item.id"
                        :value="item.id"
                    >{{item.code}}
                    </option>
                  </select>
                </td>
                <td>
                  <div class="tbTtl">
                    包装物名称<i class="red">*</i>
                  </div>
                  <input class="ty-inputText" value="" v-model="packingSelectForm.name" name="name" disabled />
                </td>
              </tr>
              <tr>
                <td>
                  <div class="tbTtl">规格</div>
                  <input class="ty-inputText" value="" v-model="packingSelectForm.specifications" name="specifications" disabled/>
                </td>
                <td>
                  <div class="tbTtl">型号</div>
                  <input class="ty-inputText" value="" v-model="packingSelectForm.model" name="model" disabled/>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="tbTtl">计量单位1（进货时）</div>
                  <input class="ty-inputText" value="" v-model="packingSelectForm.unit" name="unit" disabled/>
                </td>
                <td>
                  <div class="tbTtl">备注</div>
                  <input class="ty-inputText" value="" v-model="packingSelectForm.memo" name="memo" disabled/>
                </td>
              </tr>
            </table>
            <div class="line tipCon">
              <div class="ty-left">
                <div>以下各数据均特指进行本层包装且使用本包装物时的情景，请逐项填写。如遇难理解之处，可参看“操作指南”。</div>
                <div class="ty-color-blue">注 以下有些数据可用于计算包装物的利用率，有些数据用于系统测算出发货时的货物总重，故各非必填项也建议填写！</div>
              </div>
              <span class="ty-right linkBtn" @click="manualBookLog=true">操作指南</span>
            </div>
            <table class="pannelTab" id="otherBase">
              <tr>
                <td>
                  <div class="tbTtl">
                    计量单位2<i class="red">*</i>（使用时)
                    <span class="linkBtn ty-right" @click="addUnit" data-targ="slct_unit">新增</span>
                  </div>
                  <select class="ty-inputSelect" name="usageUnitId" v-model="packingSelectForm.usageUnitId" @change="changeUnitName()">
                    <option value="">请选择</option>
                    <option
                        v-for="item in unitList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </option>
                  </select>
                </td>
                <td>
                  <div class="areaCon">
                    <div class="tbTtl">理论用量<i class="red">*</i></div>
                    <input class="ty-inputText" value="" placeholder="请录入" v-model="packingSelectForm.ratedAmout" @input="weightCount('ratedAmout')" name="ratedAmout"  />
                    <span class="usageUnitName">{{packingSelectForm.usageUnit}}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="tbTtl">单重1</div>
                  <input class="weightText" v-model="packingSelectForm.stockWeight" placeholder="请录入，如需要，可更换重量单位" name="stockWeight" @input="weightCount('stockWeight')" />
                  <select class="weightSelect" v-model="packingSelectForm.stockWeightUnitId" type="text" id="weightUnit1" name="stockWeightUnitId" require @change='weightCount'>
                    <option value="1">毫克（mg）</option>
                    <option value="2">克（g）</option>
                    <option value="3">千克（kg）</option>
                    <option value="4">吨（T）</option>
                  </select>
                </td>
                <td>
                  <div class="areaCon">
                    <div class="tbTtl">单重1合计</div>
                    <input class="ty-inputText" v-model="packingSelectForm.weightReference" value="" placeholder="请录入" name="weightReference" disabled id="weightCount"/>
                    <span class="weightReferenceUnit unitVal">{{packingSelectForm.weightReferenceUnit}}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="tbTtl">单重2</div>
                  <input class="weightText" v-model="packingSelectForm.usageWeight" type="text" name="usageWeight" id="weight2" placeholder="请录入，如需要，可更换重量单位" @input="volumCount(true)">
                  <select class="weightSelect" v-model="packingSelectForm.usageWeightUnitId" id="weightUnit2" type="text" name="usageWeightUnitId" @change="volumCount()">
                    <option value="1">毫克（mg）</option>
                    <option value="2">克（g）</option>
                    <option value="3">千克（kg）</option>
                    <option value="4">吨（T）</option>
                  </select>
                </td>
                <td>
                  <div class="tbTtl">出材量</div>
                  <input class="ty-inputText" v-model="packingSelectForm.volum" value="20" placeholder="请录入" id="volumCon" name="volum" disabled/>
                </td>
              </tr>
            </table>
          </div>
        </el-form>
      </template>
    </TyDialog>
    <!-- 操作指南   -->
    <TyDialog v-if="manualBookLog" width="800" dialogTitle="操作指南" :dialogHide="hideFun" :dialogName="'manualBookLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('manualBookLog')">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="manualEct">
          <p>1 名词解释</p>
          <p>
            <span>1.1 计量单位2</span>
            系指使用本包装物进行本层包装时，本包装物的计量单位。
          </p>
          <p>
            <span>1.2 理论用量</span>
            系指使用本包装物进行本层包装时，本包装物的理论用量。
          </p>
          <p>
            <span>1.3 单重1</span>
            系指每计量单位2本包装物的重量。
          </p>
          <p>
            <span>1.4 单重1合计</span>
            系指本层包装所使用本包装物的总重量，算法为理论用量乘以单重1。
          </p>
          <p>
            <span>1.5 单重2</span>
            系指每计量单位1本包装物的重量。
          </p>
          <p>
            <span>1.6 出材量</span>
            系指每计量单位1的本包装物能出来多少计量单位2的本包装物。
          </p>
          <p>2 页面上的操作</p>
          <p>2.1 总皮重与出材量系由系统算出，无需录入，其他各项则需录入。</p>
          <p>2.2 单重1不是必填项，但如不录入，系统无法算出“单重1合计”与“出材量”的值。</p>
          <p>2.3 单重2不是必填项，但如不录入，系统无法算出“出材量”的值。</p>
        </div>
      </template>
    </TyDialog>

    <!--增加包装物-->
    <TyDialog v-show="addPackageLog" width="600" dialogTitle="增加包装物" :dialogHide="hideFun" :dialogName="'addPackageLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('addPackageLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addPackageOk">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="addPackageForm" :rules="packageRules" :model="addPackageForm" label-width="80px" label-position="top">
          <div class="fmWrapper">
            <p>所增加的包装物将进入材料—“商品的包装物”清单</p>
            <ul class="pack_form">
              <li>
                <el-form-item label="包装物代号" prop="code">
                  <el-input v-model="addPackageForm.code" clearable></el-input>
                </el-form-item>
              </li>
              <li>
                <el-form-item label="包装物名称" prop="name">
                  <el-input v-model="addPackageForm.name" clearable></el-input>
                </el-form-item>
              </li>
              <li>
                <el-form-item label="规格" prop="specifications">
                  <el-input v-model="addPackageForm.specifications" clearable></el-input>
                </el-form-item>
              </li>
              <li>
                <el-form-item label="型号" prop="model">
                  <el-input v-model="addPackageForm.model" clearable></el-input>
                </el-form-item>
              </li>
              <li>
                <span class="linkBtn ty-right" @click="addUnit">新增</span>
                <el-form-item label="进货时的计量单位" prop="unitId">
                  <el-select v-model="addPackageForm.unitId" placeholder="请选择" id="buyingUnit">
                    <el-option value="">请选择</el-option>
                    <el-option
                        v-for="item in unitList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </li>
              <li>
                <el-form-item label="备注（录入内容可包含但不限于包装物的材质）" prop="memo">
                  <el-input v-model="addPackageForm.memo" clearable></el-input>
                </el-form-item>
              </li>
            </ul>
          </div>
        </el-form>
      </template>
    </TyDialog>
    <!--  查看本层包装的包装物   -->
    <TyDialog v-if="scanMajLog" width="1000" dialogTitle="查看本层包装的包装物" :dialogHide="hideFun" :dialogName="'scanMajLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('scanMajLog')">关  闭</el-button>
      </template>
      <template #dialogBody>
        <p class="gapT">本层包装为<span id="packName">{{spanPackage.ttl}}</span>，包装内商品净重<span id="packItemWeight">{{spanPackage.modNetWeight}}{{spanPackage.modWeightUnit}}</span>，
          包装后的毛重<span id="packItemGrossWeight">{{spanPackage.modGrossWeight}} {{spanPackage.modGrossUnit}}</span>。</p>
        <p class="gapT">本层包装的单个包装内装有
          <span v-if="spanPackage.level === 0">商品{{spanPackage.productCount}}{{commodityListInfo.unit}}。</span>
          <span v-else-if="spanPackage.level > 0">{{spanPackage.packagingCount}}个上一层的小包装，共包装有商品{{spanPackage.modNum}}{{commodityListInfo.unit}}。</span>
        </p>
        <hr/>
        <table class="ty-table ty-table-control" id="scanItemPackList">
          <tbody>
          <tr>
            <td width="15%">包装物类别</td>
            <td width="30%">包装物信息</td>
            <td width="15%">使用时的理论用量</td>
            <td width="20%">本层所用本包装物总重</td>
            <td width="20%">本包装物的出材量</td>
          </tr>
          <tr>
            <td>主要包装物</td>
            <td>{{spanPackage.zyPackaging.code}}/{{spanPackage.zyPackaging.name}}/{{spanPackage.zyPackaging.specifications ||''}}/{{spanPackage.zyPackaging.model ||''}}/{{spanPackage.zyPackaging.unit ||''}}</td>
            <td>{{spanPackage.zyPackaging.ratedAmout}}{{spanPackage.zyPackaging.usageUnit}}</td>
            <td>{{Number(spanPackage.zyPackaging.weightReference).toFixed(2)}}{{spanPackage.zyPackaging.weightReferenceUnit}}</td>
            <td>{{spanPackage.zyPackaging.volum}}</td>
          </tr>
          <tr v-for="(item, index) in spanPackage.itemList" :key="index">
            <td>辅助包装物</td>
            <td>{{item.code}}/{{item.name}}/{{item.specifications ||''}}/{{item.model ||''}}/{{item.unit ||''}}</td>
            <td>{{item.ratedAmout}}{{item.usageUnit}}</td>
            <td>{{Number(item.weightReference).toFixed(2)}}{{item.weightReferenceUnit}}</td>
            <td>{{item.volum}}</td>
          </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>
    <!--  新增计量单位   -->
    <TyDialog v-if="addUnitLog" width="500" dialogTitle="新增计量单位" color="blue" :dialogHide="hideFun" :dialogName="'addUnitLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('addUnitLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addUnitOkBtn(8)">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="unitForm" :model="unitForm" label-width="80px" label-position="top">
          <el-form-item label="计量单位">
            <el-input type="text" v-model="unitForm.name"  placeholder="请录入计量单位的名称"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </TyDialog>
    <!--  删除本层提示  -->
    <TyDialog v-if="delVisible" width="580" dialogTitle="！提示" color="blue" :dialogHide="hideFun" :dialogName="'delVisible'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('delVisible')">取消</el-button>
        <el-button class="bounce-ok" @click="delThisLayerOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <p>确定删除本层包装吗？</p>
        </div>
      </template>
    </TyDialog>
    <!--  删除提示  -->
    <TyDialog v-if="delSubPackLog" width="580" dialogTitle="！提示" color="blue" :dialogHide="hideFun" :dialogName="'scanSubPackLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('delSubPackLog')">取消</el-button>
        <el-button class="bounce-ok" @click="delScanPackage">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定删除本材料吗？
        </div>
      </template>
    </TyDialog>
  </div>
</template>
<script>
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import { getPackList, getPackagingDetail, getWszNum, getUnpackList, getUnitList, addPackageDone, getPackageList, noNeedPackingOk,
  outerBaseSure, updatePackingTipOk, getRecord, getRecordDetail, addUnitOk
} from "@/api/technology"
import searchImg from '@/assets/img/search.png'
export default {
  data() {
    return {
      pageName:'packingManage',
      mainNum: 1,
      backNum: '',
      backPreNum: '',
      wszNum: 0,
      searchKeyHome: '',
      searchKeyWsz: '',
      goodsList: [],
      packagingState: '', //0未设置，1设置中 2设置完成
      isPacking: 0,
      kind: 0,
      cmPackingList: [],
      suspendList: [],
      commodityListInfo: {}, //商品信息
      structureFrom: {}, //编辑本层包装from
      wszGoodsList: [],
      mainNum1Page: {},
      mainNum2Page: {},
      dialogTitle: '',
      modelPattern: 0,
      settingType: 'edit',
      finishAble: false,
      selectDialogTtl: '选择/更换本层包装的主要包装物',
      scanSubLog: false,
      editSub: false,
      settingVisible: false,
      selectPackageLog: false,
      //主要/辅助包装物
      packingSelectForm: {
        "material":'',
        "name":'',
        "specifications":'',
        "model":'',
        "unit":'',
        "memo":''
      },
      addPackageForm: {
        "code":'',
        "name":'',
        "specifications":'',
        "model":'',
        "unit":'',
        "unitId":'',
        "memo":''
      },
      addPackageLog: false,
      unitList: [],
      packageNameList: [],
      selectPackageSource: {},
      scanSubPackLog: false,
      cmPackingStructureList: [{ //新建包装
        "productCount": '',
        "zyPackaging": '',
        "itemList": [],
        "modNum": '',
        "modNetWeight": '',
        "modGrossWeight": ''
      }],
      level: 0,
      order: 0,
      outerBaseForm: {},
      manualBookLog: false,
      usingAble: true,
      scanMoreList: [],
      updatePackingKind: 0,
      recordList: [],
      operation: ['','创建','删','修改','启用','停用'],
      recordScanItem: {"structureList":[]},
      outerBaseLog: false,
      spanPackage: {},
      pEditType: '',
      addUnitLog: false,
      scanMajLog: false,
      delVisible: false,
      delSubPackLog: false,
      tipVisible: false,
      unitForm:{}, //新增计量单位
      searchIcon: searchImg,
      packageRules:{
        code: [
          { required: true, message: '还有必填项尚未填写', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '还有必填项尚未填写', trigger: 'blur' }
        ],
        unitId: [
          { required: true, message: '还有必填项尚未填写', trigger: 'blur' }
        ]
      },
      structureFromrules:{
        zyPackaging: [
          { required: true, message: '', trigger: 'blur' }
        ],
        productCount: [
          { required: true, message: '', trigger: 'blur' }
        ]
      }
    }
  },
  watch:{
    'structureFrom.productCount': function(){
      this.grossComputed(2)
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'wg', name: '包装', pageName: this.pageName}, this.initPackingHome, this)
    getUnitList({ 'module': 8 }).then(res => {
      var list = res.data['list'] || [];
      this.unitList = list
    })
  },
  methods: {
    disabledDate(time) {
      return new Date(time).getTime() <= new Date().getTime()
    },
    initPackingHome(){
      let that = this
      that.mainNum = 1
      that.goodsList = []
      getWszNum().then(res => {
        that.wszNum = res.data.data
      }).catch(err => {
        console.log('err=QQQQQQQQQ', err)
      })
      this.indexGoodsList(1)
    },
    mainNum1PageFun(pageInfo){
      this.indexGoodsList(pageInfo.page)
    },
    indexGoodsList(cur){
      let param = {
        pageSize:20,
        currentPageNo: cur
      }
      this.goodsList = []
      getPackList(param).then(data => {
        let list = data.data.data || []
        this.mainNum1Page = data.data.page
        if (list) {
          for (let i=0;i<list.length;i++){
            if (this.searchKeyHome === '' || list[i].outerSn.includes(this.searchKeyHome) || list[i].outerName.includes(this.searchKeyHome)){
              this.goodsList.push(list[i])
            }
          }
        }
      })
    },
    scanPackage(info){
      this.commodityListInfo = info
      this.packagingState = info.packagingState
      this.mainNum = 3
      this.usingAble = true
      this.initCmPackingDetail();
    },
    initWszPage(cur){
      this.mainNum = 2
      this.wszGoodsList = []
      let param = {
        pageSize:20,
        currentPageNo: cur
      }
      getUnpackList(param).then(res => {
        let list = res.data.data || []
        this.mainNum2Page = res.data.page
        if (list.length > 0) {
          for (let i=0;i<list.length;i++){
            if (this.searchKeyWsz === '' || list[i].outerSn.includes(this.searchKeyWsz) || list[i].outerName.includes(this.searchKeyWsz)){
              this.wszGoodsList.push(list[i])
            }
          }
        }
      })
    },
    mainNum2PageFun(pageInfo){
      this.initWszPage(pageInfo.page)
    },
    initCmPackingDetail(){
      let param = { "productId": this.commodityListInfo.id }
      getPackagingDetail(param).then(res => {
        let isPacking = res.data.data.isPacking;//isPacking=0  无需包装
        let list = res.data.data.list || [];
        let susList =  res.data.data.suspendList || [];
        let arr = [];
        arr.push(...susList,...list);
        this.isPacking = isPacking
        this.cmPackingList = list
        this.suspendList = susList
        if (arr && arr.length > 0) {
          for (let n=0;n<arr.length;n++) {
            let info =  arr[n] || {};
            let structureList = info.structureList || [];
            structureList.forEach((item, index)=>{
              let netW=``, netU=``,grossW=``, grossU=``, grossAuto=``, netAuto=``, total=0;
              item.zyPackaging.volum = ""
              item.zyPackaging.weightReference = ""
              if (this.handleNull(item.zyPackaging.ratedAmout) !== "" && this.handleNull(item.zyPackaging.stockWeight) !== "") {
                let step = item.zyPackaging.ratedAmout * item.zyPackaging.stockWeight;
                let refer = this.autoWeight(step, item.zyPackaging.stockWeightUnitId);
                item.zyPackaging.weightReference = refer.val;
                item.zyPackaging.weightReferenceUnit = this.charge(refer.type,'weightUnit');
                item.zyPackaging.weightReferenceUnitId = refer.type;
                let num1 = this.chargeWeight(step, item.zyPackaging.stockWeightUnitId);
                total += num1;
              }
              if (this.handleNull(item.zyPackaging.weightReference) !== "" && this.handleNull(item.zyPackaging.stockWeightUnitId) !== "" && this.handleNull(item.zyPackaging.usageWeightUnit) !== "" && this.handleNull(item.zyPackaging.usageWeight) !== "") {
                let num1 = this.chargeWeight(item.zyPackaging.weightReference, item.zyPackaging.weightReferenceUnitId);
                let num2 = this.chargeWeight(item.zyPackaging.usageWeight, item.zyPackaging.usageWeightUnitId);
                let count = Math.floor(num2 / num1);
                item.zyPackaging.volum = count;
              }
              for(let i=0;i<item.itemList.length;i++){
                item.itemList[i].order = i+1;
                item.itemList[i].volum = ""
                item.itemList[i].weightReference = ""
                if (this.handleNull(item.itemList[i].ratedAmout) !== "" && this.handleNull(item.itemList[i].stockWeight) !== "") {
                  let step = item.itemList[i].ratedAmout * item.itemList[i].stockWeight;
                  let refer = this.autoWeight(step, item.itemList[i].stockWeightUnitId);
                  let num1 = this.chargeWeight(step, item.itemList[i].stockWeightUnitId);
                  item.itemList[i].weightReference = refer.val;
                  item.itemList[i].weightReferenceUnit = this.charge(refer.type,'weightUnit');
                  item.itemList[i].weightReferenceUnitId = refer.type;
                  total += num1;
                }
                if (this.handleNull(item.weightReference) !== "" && this.handleNull(item.stockWeightUnitId) !== "" && this.handleNull(item.usageWeightUnit) !== "" && this.handleNull(item.usageWeight) !== "") {
                  let num1 = this.chargeWeight(item.itemList[i].weightReference, item.itemList[i].weightReferenceUnitId);
                  let num2 = this.chargeWeight(item.itemList[i].usageWeight, item.itemList[i].usageWeightUnitId);
                  let count = Math.floor(num2 / num1);
                  item.itemList[i].volum = count;
                }
              }
              if (index === 0) {
                netW = this.commodityListInfo.netWeight * item.productCount;
                netU = this.commodityListInfo.weightUnit;
                grossW = netW
                grossU = netU
                item.modNum = item.productCount;
              } else {
                netW = structureList[index-1].netWeight * item.productCount;
                netU = structureList[index-1].modWeightUnitId;
                grossW = structureList[index-1].modGrossWeight * item.productCount
                grossU = structureList[index-1].modWeightUnitId
                item.modNum = item.productCount * structureList[index-1].modNum;
              }
              let initW = this.chargeWeight(grossW, grossU);//转为毫克
              if (initW) {
                grossAuto = this.autoWeight((initW + Number(total)).toFixed(2), 1);
              } else {
                grossAuto = this.autoWeight(Number(total), 1);
              }
              netAuto = this.autoWeight(netW, netU);
              item.modNetWeight = netAuto.val;
              item.modWeightUnit = this.charge(netAuto.type, 'weightUnit');
              item.modWeightUnitId = netAuto.type;
              item.modGrossWeight = grossAuto.val;
              item.modGrossUnit = this.charge(grossAuto.type, 'weightUnit');
              item.modGrossUnitId = grossAuto.type;
            })
          }
          susList.findIndex(value => Number(value.enabled) === 0)
          list.findIndex(value => Number(value.enabled) === 1)
        }
      })
    },
    packingSetting(item){
      this.mainNum = 7
      this.backPreNum = 2
      this.modelPattern = 0
      this.pEditType = 'add'
      this.commodityListInfo = item
      this.packagingState = 0
      this.initCmPacking()
    },
    editPackingInfo(item, level ){
      this.level = level
      const info = JSON.stringify(item)
      this.structureFrom = JSON.parse(info)
      this.settingType = 'edit'
      this.settingVisible = true
    },
    selectPackage(source, editType, subOrder){
      let ttl = ``;
      if (source === 1) {
        ttl = `选择/更换本层包装的主要包装物`;
        editType = !this.structureFrom.zyPackaging || this.structureFrom.zyPackaging === ''? 'add':'update'
      } else if (source === 2) {
        ttl = `增加本层包装的辅助包装物`;
      } else if (source === 3) {
        if (editType === 'add') {
          ttl = `增加本层包装的辅助包装物`;
        } else {
          ttl = `选择/更换本层包装的主要包装物`;
        }
      }
      this.selectDialogTtl = ttl;
      this.selectPackageSource = {source,"type": editType}
      this.selectPackageLog = true
      this.packingSelectForm = {}
      this.packingSelectForm.stockWeightUnitId = 2
      this.packingSelectForm.usageWeightUnitId = 2
      this.packingSelectForm.stockWeightUnit = '克'
      this.packingSelectForm.usageWeightUnit = '克'
      if (editType === 'update') {
        if (source === 1) {
          const info = JSON.stringify(this.structureFrom.zyPackaging)
          const data = JSON.parse(info)
          this.packingSelectForm = data
        } else {
          const info = JSON.stringify(this.structureFrom.itemList[subOrder])
          const data = JSON.parse(info)
          this.order = subOrder
          this.packingSelectForm = data
        }
      }
      if (this.packageNameList.length === 0) {
        getPackageList().then(res => {
          var list = res.data.data || [];
          this.packageNameList = list
        })
      }
    },
    addPackage(){ //增加包装物
      this.addPackageForm = {
        "code":'',
        "name":'',
        "specifications":'',
        "model":'',
        "unit":'',
        "unitId":'',
        "memo":''
      }
      this.addPackageLog = true
    },
    addPackageOk(){ //增加包装物-确定
      let unit = this.unitList.find(item => Number(item.id) === Number(this.addPackageForm.unitId))
      if (unit) this.addPackageForm.unit = unit.name
      addPackageDone( this.addPackageForm).then(() => {
        this.addPackageLog = false
        getPackageList().then(res => {
          var list = res.data.data || [];
          this.packageNameList = list
        })
      })
    },
    changePackageName(){//更换包装物
      if (this.packingSelectForm.material === "") {
        this.packingSelectForm.name = ""
        this.packingSelectForm.specifications = ""
        this.packingSelectForm.model = ""
        this.packingSelectForm.unit = ""
        this.packingSelectForm.memo = ""
      } else {
        let data = this.packageNameList.find(item => Number(item.id) === Number(this.packingSelectForm.material))
        if (data) {
          this.packingSelectForm.name = data.name
          this.packingSelectForm.specifications = data.specifications
          this.packingSelectForm.model = data.model
          this.packingSelectForm.unit = data.unit
          this.packingSelectForm.memo = data.memo
        }
      }
    },
    selectPackageSure(){//选择主要包装物-确定
      if (this.packingSelectForm.material !== "" && this.packingSelectForm.usageUnitId !== "" && this.packingSelectForm.ratedAmout !== ""
      && this.packingSelectForm.material !== undefined && this.packingSelectForm.usageUnitId !== undefined && this.packingSelectForm.ratedAmout !== undefined
      ) {
        let type = this.selectPackageSource.type
        let source = this.selectPackageSource.source
        let packInfo = this.packageNameList.find(item => Number(item.id) === Number(this.packingSelectForm.material))
        if ( this.packingSelectForm.stockWeightUnitId !== "") {
          this.packingSelectForm.stockWeightUnit = this.charge(this.packingSelectForm.stockWeightUnitId, 'weightUnit')
        }
        if (this.packingSelectForm.usageWeightUnitId !== "") {
          this.packingSelectForm.usageWeightUnit = this.charge(this.packingSelectForm.usageWeightUnitId, 'weightUnit')
        }
        if (packInfo) this.packingSelectForm.code = packInfo.code
        if (source === 1) {
          this.structureFrom.zyPackaging = this.packingSelectForm
        } else if (source === 2) {
          this.packingSelectForm.order = this.structureFrom.itemList.length + 1
          this.structureFrom.itemList.push(this.packingSelectForm)
        } else if (source === 3) { //查看/管理本层包装的辅助包装物
          if (type === 'add') {
            this.packingSelectForm.order = this.structureFrom.itemList.length + 1
            this.structureFrom.itemList.push(this.packingSelectForm)
          } else if (type === 'update') {
            this.structureFrom.itemList[this.order] = this.packingSelectForm
          }
          this.scanSubPackLog = true
        }
        this.grossComputed(1)
        this.selectPackageLog = false
        if (source === 3 && this.editSub) {
          this.cmPackingStructureList[this.level] = this.structureFrom
          this.grossNextComputed()
        }
      } else {
        this.$message({
          'type':'error',
          'offset':'30',
          'message': "您还有必填项未完成！"
        })
        return false
      }
    },
    changeUnitName(){
      if (this.packingSelectForm.usageUnitId !== "") {
        let info = this.unitList.find(item => Number(this.packingSelectForm.usageUnitId) === Number(item.id))
        if (info) this.packingSelectForm.usageUnit = info.name
      } else {
        this.packingSelectForm.usageUnit = '';
      }
    },
    scanSubPackList(level){
      this.editSub = false
      if (level === 0 || level) {
        this.level = level
        this.editSub = true
        const info = JSON.stringify(this.cmPackingStructureList[level])
        this.structureFrom = JSON.parse(info)
      }
      this.scanSubLog = true
    },
    structureSure(){
      this.$refs['structureFrom'].validate((valid) => {
        if (valid) {
          this.settingVisible = false
          if (this.settingType === 'add') {
            this.cmPackingStructureList.push(this.structureFrom)
          } else {
            this.cmPackingStructureList[this.level] = this.structureFrom
            this.grossNextComputed()
          }
        } else {
          this.$message({
            'type':'error',
            'offset':'30',
            'message': "还有必填项未填！"
          })
          return false;
        }
      });
    },
    commodityPackingOk(){
      let type = this.pEditType
      if (Number(this.packagingState === 0) && this.modelPattern === 2) {
        this.noNeedPacking()
      } else if ((Number(this.packagingState) === 0 && this.modelPattern === 1 || this.packagingState === 2) && this.cmPackingStructureList.length > 0) {
        let able = true;
        this.cmPackingStructureList.forEach((item)=>{
          if (item.zyPackaging === "" || item.productCount === "") {
            able = false
            return false;
          }
        })
        if (able) {
          this.outerBaseForm = {}
          this.outerBaseForm.lengthUnitId = 3
          this.outerBaseForm.widthUnitId = 3
          this.outerBaseForm.heightUnitId = 3
          this.outerBaseForm.outerShape = ''
          this.settingVisible = false
          this.outerBaseLog = true
          if (type === 'update'){
            let info = this.cmPackingList[this.kind]
            this.outerBaseForm = info
          }
          this.outerBaseForm.effectTime = ''
        } else {
          this.$message({
            'type':'error',
            'offset':'30',
            'message': "操作失败！点击“确定”时，页面最下方一行的包装需编辑完！"
          })
        }
      }
    },
    noNeedPacking(){
      let info = this.commodityListInfo;
      let data = {"productId": info.id, 'list': '[]'}
      noNeedPackingOk(data).then(() => {
        if (!this.packagingState || this.packagingState === 0){
          this.initWszPage(1)
        } else {
          this.initCmPackingDetail()
          this.mainNum = 3
          this.usingAble = true
        }
      })
    },
    outerBaseOk(){
      let type = this.pEditType
      let info = this.commodityListInfo
      let url = `../packing/addPackaging.do`;
      let outer = JSON.stringify(this.outerBaseForm)
      let deep = JSON.parse(outer)
      let json = {
        "productId": info.id,
        "outerLength": deep.outerLength,
        "lengthUnitId": deep.lengthUnitId,
        "outerWidth": deep.outerWidth,
        "widthUnitId": deep.widthUnitId,
        "outerHeight": deep.outerHeight,
        "heightUnitId": deep.heightUnitId,
        "outerShape": deep.outerShape
      }
      if (json.outerLength === "") delete json.outerLength
      if (json.outerWidth === "") delete json.outerWidth
      if (json.outerHeight === "") delete json.outerHeight
      json.lengthUnit =  this.charge(json.lengthUnitId, 'weightUnit')
      json.widthUnit =  this.charge(json.widthUnitId, 'weightUnit')
      json.heightUnit =  this.charge(json.heightUnitId, 'weightUnit')
      if (this.packagingState === 1 || this.packagingState === 2 || !this.packagingState  && this.modelPattern === 1) {
        this.cmPackingStructureList.forEach((item,index) => {
          item.product = info.id
          item.level = index + 1
        })
        json.list = JSON.stringify(this.cmPackingStructureList);
      }
      if (type === 'update') {
        if (info.packagingState === 2) {
          let time = this.outerBaseForm.effectTime
          if (!time || time === ""){
            this.$message({
              'offset':'30',
              'type':'error',
              'message':'请选择日期！'
            })
            return false;
          }
          json.effectTime = time
        }
        json.id = this.cmPackingList[this.kind].id
        url = `../packing/updatePackaging.do`;
      }
      outerBaseSure(json, url).then(() => {
        console.log("lllll:" + info.packagingState + '，kkkkkk:' + (info.packagingState === 0))
        if (!info.packagingState || info.packagingState === 0) {
          this.initWszPage(1)
        } else if (info.packagingState === 2) {
          this.mainNum = 3
          this.usingAble = true
          info.packagingState = info.packagingState || 1
          this.initCmPackingDetail()
        }
        this.outerBaseLog = false
      })
    },
    scanMorePacking(){
      this.mainNum = 4
      this.usingAble = true
      this.scanMoreList = this.cmPackingList
    },
    susPackingList(){ //已停用的数据
      this.mainNum = 4
      this.scanMoreList = this.suspendList
      this.usingAble = false
    },
    delTip(index){
      this.order = index
      this.delSubPackLog = true
    },
    outerShapeTogg(){
      this.outerBaseForm.outerShape && this.outerBaseForm.outerShape !== ""? this.outerBaseForm.outerShape = "": ""
    },
    togglePattern(code){
      if (this.modelPattern === code) {
        this.modelPattern = 0
      } else {
        this.modelPattern = code
      }
    },
    addMoreLayer(){
      this.settingType = 'add'
      if (this.cmPackingStructureList.length < 8) {
        let able = false
        this.cmPackingStructureList.forEach((item)=>{
          if (item.zyPackaging === "" || item.productCount === "") {
            able = true
            return false;
          }
        })
        if (able) {
          this.$message({
            'type':'error',
            'offset':'30',
            'message': "操作失败！点击“增加一层包装”时，页面最下方一行的包装需编辑完！"
          })
          return false;
        }
        this.structureFrom = {
          "productCount": '',
          "zyPackaging": '',
          "itemList": [],
          "modNum": '',
          "modNetWeight": '',
          "modWeightUnit": '',
          "modGrossWeight": '',
          "modGrossUnit": '',
          "unit": this.commodityListInfo.unit
        }
        this.level = this.cmPackingStructureList.length
        this.settingVisible = true
      } else {
        this.$message({
          'type':'error',
          'offset':'30',
          'message':'包装层数最多八层'
        })
      }
    },
    //查看
    scanItemPack(info, kind, level){
      let ttl = ``
      if (this.cmPackingStructureList.length > 1 && kind === 0) {
        ttl = '最小包装';
      } else if (kind === this.cmPackingStructureList.length - 1){
        ttl = '最外层包装';
      }else{
        ttl = `最小包装的上${this.chargeLevel(kind)}层包装`;
      }
      info.ttl = ttl
      info.level = level
      if (info.zyPackaging && info.zyPackaging.id){
        if (this.handleNull(info.zyPackaging.ratedAmout) !== "" && this.handleNull(info.zyPackaging.stockWeight) !== "") {
          let refer = this.autoWeight(info.zyPackaging.ratedAmout * info.zyPackaging.stockWeight, info.zyPackaging.stockWeightUnitId)
          info.zyPackaging.weightReference = refer.val;
          info.zyPackaging.weightReferenceUnit =  this.charge(refer.type,'weightUnit')
          info.zyPackaging.weightReferenceUnitId = refer.type
        }
        if (this.handleNull(info.zyPackaging.weightReference) !== "" && this.handleNull(info.zyPackaging.stockWeightUnitId) !== "" &&
            this.handleNull(info.zyPackaging.usageWeightUnit) !== "" && this.handleNull(info.zyPackaging.usageWeight) !== "") {
          let num1 = this.chargeWeight(info.zyPackaging.weightReference, info.zyPackaging.weightReferenceUnitId);
          let num2 = this.chargeWeight(info.zyPackaging.usageWeight, info.zyPackaging.usageWeightUnitId);
          let count = Math.floor(num2 / num1);
          info.zyPackaging.volum = count;
        }
      }
      info.itemList.forEach((item)=>{
        if (this.handleNull(item.ratedAmout) !== "" && this.handleNull(item.stockWeight) !== "") {
          let refer = this.autoWeight(item.ratedAmout * item.stockWeight, item.stockWeightUnitId)
          item.weightReference = refer.val;
          item.weightReferenceUnit = this.charge(refer.type,'weightUnit')
          item.weightReferenceUnitId = refer.type
        }
        if (this.handleNull(item.weightReference) !== "" && this.handleNull(item.stockWeightUnitId) !== "" &&
            this.handleNull(item.usageWeightUnit) !== "" && this.handleNull(item.usageWeight) !== "") {
          let num1 = this.chargeWeight(item.weightReference, item.weightReferenceUnitId);
          let num2 = this.chargeWeight(item.usageWeight, item.usageWeightUnitId);
          let count = Math.floor(num2 / num1);
          item.volum = count;
        }
      })
      this.spanPackage = info
      this.scanMajLog = true
    },
    updatePackingTip(source, num){ //source:1=改为无需包装,2=删除、3=停用
      this.updatePackingKind = source
      if (source === 3) {
        this.kind = num
      }
      this.tipVisible = true
    },
    withoutPackageOk(){ // 改为无需包装确定
      let url = ``, data = {};
      let source = this.updatePackingKind;
      if (source === 1) {
        url = `../packing/addPackaging.do`;
        data ={"productId": this.commodityListInfo.id, "list": '[]'}
      } else if (source === 3) {
        url = `../packing/stopOrStartPackaging.do`;
        if (this.usingAble) {
          data = {"id": this.cmPackingList[this.kind].id, "enabled": 0}
        } else {
          data = {"id": this.suspendList[this.kind].id, "enabled": 1}
        }
      }
      updatePackingTipOk(data, url).then(() => {
        this.tipVisible = false
        if (source === 3 && this.usingAble) {
          this.cmPackingList.splice(this.kind, 1)
        } else if (source === 3 && data.enabled === 1) {
          this.suspendList.splice(this.kind, 1)
        }
        this.initCmPackingDetail()
      })
    },
    //新增包装方式
    addPacking(){
      this.pEditType = 'add'
      this.backPreNum = this.mainNum
      this.initCmPacking()
      this.mainNum = 7

    },
    initCmPacking (){
      this.cmPackingStructureList = [{
        "productCount": '',
        "zyPackaging": '',
        "itemList": [],
        "modNum": '',
        "modNetWeight": '',
        "modWeightUnit": '',
        "modWeightUnitId": '',
        "modGrossWeight": '',
        "modGrossUnit": '',
        "unit": this.commodityListInfo.unit
      }]
    },
    //操作记录
    getUpdateRecord(info){
      let json = {"id": info.id}
      this.backNum =  this.mainNum
      this.mainNum = 5
      getRecord(json).then(res => {
        this.recordList = res.data.data || []
      })
    },
    updateRecordScan(info){//操作记录-查看
      let data =  {"recordId": info.id}
      getRecordDetail(data).then(res => {
        let data = res.data.data || {};
        data.structureList.forEach((pack, index) => {
          let netW=``, netU=``,grossW=``, grossU=``, grossAuto=``, netAuto=``, total=0;
          pack.zyPackaging.volum = ""
          pack.zyPackaging.weightReference = ""
          if (this.handleNull(pack.zyPackaging.ratedAmout) !== "" && this.handleNull(pack.zyPackaging.stockWeight) !== "") {
            let step = pack.zyPackaging.ratedAmout * pack.zyPackaging.stockWeight;
            let refer = this.autoWeight(step, pack.zyPackaging.stockWeightUnitId)
            pack.zyPackaging.weightReference = refer.val;
            pack.zyPackaging.weightReferenceUnit = this.charge(refer.type,'weightUnit')
            pack.zyPackaging.weightReferenceUnitId = refer.type
            let num1 = this.chargeWeight(step, pack.zyPackaging.stockWeightUnitId);
            total += num1;
          }
          if (this.handleNull(pack.zyPackaging.weightReference) !== "" && this.handleNull(pack.zyPackaging.stockWeightUnitId) !== "" &&
              this.handleNull(pack.zyPackaging.usageWeightUnit) !== "" && this.handleNull(pack.zyPackaging.usageWeight) !== "") {
            let num1 = this.chargeWeight(pack.zyPackaging.weightReference, pack.zyPackaging.weightReferenceUnitId);
            let num2 = this.chargeWeight(pack.zyPackaging.usageWeight, pack.zyPackaging.usageWeightUnitId);
            let count = Math.floor(num2 / num1);
            pack.zyPackaging.volum = count;
          }
          for(let i=0;i<pack.itemList.length;i++){
            pack.itemList[i].order = i+1;
            pack.itemList[i].volum = ""
            pack.itemList[i].weightReference = ""
            if (this.handleNull(pack.itemList[i].ratedAmout) !== "" && this.handleNull(pack.itemList[i].stockWeight) !== "") {
              let step = pack.itemList[i].ratedAmout * pack.itemList[i].stockWeight;
              let refer = this.autoWeight(step, pack.itemList[i].stockWeightUnitId)
              pack.itemList[i].weightReference = refer.val;
              pack.itemList[i].weightReferenceUnit = this.charge(refer.type,'weightUnit')
              pack.itemList[i].weightReferenceUnitId = refer.type
              let num1 = this.chargeWeight(step, pack.itemList[i].stockWeightUnitId);
              total += num1;
            }
            if (this.handleNull(pack.weightReference) !== "" && this.handleNull(pack.stockWeightUnitId) !== "" &&
                this.handleNull(pack.usageWeightUnit) !== "" && this.handleNull(pack.usageWeight) !== "") {
              let num1 = this.chargeWeight(pack.itemList[i].weightReference, pack.itemList[i].weightReferenceUnitId);
              let num2 = this.chargeWeight(pack.itemList[i].usageWeight, pack.itemList[i].usageWeightUnitId);
              let count = Math.floor(num2 / num1);
              pack.itemList[i].volum = count;
            }
          }
          if (index === 0) {
            netW = pack.productCount * this.commodityListInfo.netWeight;
            netU = this.commodityListInfo.weightUnit;
            grossW = netW;
            grossU = netU;
            pack.modNum = pack.productCount;
          } else {
            netW = pack.productCount * data.structureList[index-1].modNetWeight;
            netU = data.structureList[index-1].modWeightUnitId;
            pack.modNum = pack.productCount * data.structureList[index-1].modNum;
            grossW = pack.productCount * data.structureList[index-1].modGrossWeight;
            grossU = data.structureList[index-1].modGrossUnitId;
          }
          let grossVal = this.chargeWeight(grossW, grossU);//转为毫克
          if (grossVal) {
            grossAuto = this.autoWeight((grossVal + Number(total)).toFixed(2), 1);
          } else {
            grossAuto = this.autoWeight(Number(total), 1);
          }
          netAuto = this.autoWeight(netW, netU);
          pack.modNetWeight = netAuto.val || '';
          pack.modWeightUnit = this.charge(netAuto.type, 'weightUnit');
          pack.modWeightUnitId = netAuto.type;
          pack.modGrossWeight = grossAuto.val || '';
          pack.modGrossUnit = this.charge(grossAuto.type, 'weightUnit');
          pack.modGrossUnitId = grossAuto.type;
        })
        this.recordScanItem = data
        this.mainNum = 6
      })
    },
    updatePackage(level){//编辑包装方式
      let info = {}
      if(this.usingAble){
        info = this.cmPackingList[level]
      } else{
        info = this.suspendList[level]
      }
      const kinds = JSON.stringify(info)
      this.level = level
      this.cmPackingStructureList = JSON.parse(kinds).structureList
      this.pEditType = 'update'
      this.modelPattern = 0
      this.packagingState = 2
      this.backPreNum = this.mainNum
      this.mainNum = 7
    },
    addUnit() {
      this.unitForm.name = ""
      this.addUnitLog = true
    },
    addUnitOkBtn(module) {
      var name = this.unitForm.name;
      addUnitOk({module,name}).then(res => {
        var status = res.data['status'] ;
        if(status ==1){
          this.$message({
            'type':'success',
            'offset':'30',
            'message':'新增成功！'
          })
          this.addUnitLog = false
          getUnitList({ 'module': module }).then(res => {
            var list = res.data['list'] || [];
            this.unitList = list
          })
        }else {
          var tipStr = '' ;
          if(status == 0){
            tipStr = '新增失败！系统中已经有这个计量单位了。' ;
          }else if(status == 2){
            tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
          }
          this.$message({
            'type':'error',
            'offset':'30',
            'message': tipStr
          })
        }
      })
    },
    delThisLayer(level){ //删除本层
      this.level = level
      this.delVisible = true
    },
    delThisLayerOk(){
      this.cmPackingStructureList.splice(this.level, 1)
      this.delVisible = false
    },
    delScanPackage(){
      if (this.editSub) {
        this.cmPackingStructureList[this.level].itemList.splice(this.order, 1)
        this.grossNextComputed()

      }
      this.structureFrom.itemList.splice(this.order, 1)
      this.delSubPackLog = false
    },
    backPre(num){
      if (num === 9) {
        num = this.backPreNum
      } else if (num === 8){
        num = this.backNum
      }
      if (num === 1 || num === '1'){
        this.initPackingHome()
      } else if (num === 2 || num === '2'){
        this.initWszPage(1)
      } else if (num === 3 || num === '3'){
        this.usingAble = true
      }
      this.mainNum = num
    },
    volumCount(able){ //计算出材量
      if ( able ) this.limitNum('usageWeight',2)
      if (this.packingSelectForm.weightReference === "" || this.packingSelectForm.usageWeight === "" || this.packingSelectForm.usageWeightUnitId === ""
          || !this.packingSelectForm.weightReference || !this.packingSelectForm.usageWeight || !this.packingSelectForm.usageWeightUnitId) {
        this.packingSelectForm.volum = ""
      } else {
        let num1 = this.chargeWeight(this.packingSelectForm.weightReference, this.packingSelectForm.weightReferenceUnitId) //单重1合计
        let num2 = this.chargeWeight(this.packingSelectForm.usageWeight, this.packingSelectForm.usageWeightUnitId)
        this.packingSelectForm.volum = Math.floor(num2 / num1);//出材量
      }
    },
    weightCount(str) {
      let count = 0;
      if (str) {
        this.limitNum(str, 2)
      }
      if (this.packingSelectForm.stockWeight === "" || this.packingSelectForm.ratedAmout === "" || this.packingSelectForm.stockWeightUnitId === "" ||
          !this.packingSelectForm.stockWeight || !this.packingSelectForm.ratedAmout || !this.packingSelectForm.stockWeightUnitId) {
        this.packingSelectForm.weightReference = ""
        this.packingSelectForm.weightReferenceUnit = ""
        this.packingSelectForm.weightReferenceUnitId = ""
      } else {
        let unit1 = this.packingSelectForm.stockWeightUnitId
        count = this.packingSelectForm.ratedAmout * this.packingSelectForm.stockWeight
        let weight = this.autoWeight(count, unit1);
        this.packingSelectForm.weightReference = Number(weight.val).toFixed(2)
        this.packingSelectForm.weightReferenceUnit = this.charge(weight.type, 'weightUnit')
        this.packingSelectForm.weightReferenceUnitId = weight.type
      }
      this.volumCount(false)
    },
    charge(val , type) { //翻译键值
      var str = ""
      switch (type){
        case 'outerShape':
          if(val == "1"){ str = "长方体"; }
          if(val == "2"){ str = "圆柱体"; }
          if(val == "3"){ str = "其它形状"; }
          break;
        case 'weightUnit':
          if(val == "1"){ str = "毫克"; }
          if(val == "2"){ str = "克"; }
          if(val == "3"){ str = "千克"; }
          if(val == "4"){ str = "吨"; }
          break;
      }
      return str
    },
    chargeWeight(val , type) { //转为毫克
      var str = 0;
      if (this.handleNull(val) !== "" && this.handleNull(type) !== "") str = val * Math.pow(1000, type-1);
      return str;
    },
    autoWeight(val , type) { //重量自动换算
      let num = Number(type);
      if (val < 1000 || num === 4){
        console.log(num)
        return {'val':val, 'type': num};
      }
      ++num;
      return this.autoWeight(val/ 1000,num);
    },
    limitNum(name, num) {
      let value = ``
      if (name === 'productCount') {
        value = this.structureFrom.productCount
      } else if (name === 'ratedAmout') {
        value = this.packingSelectForm.ratedAmout
      } else if (name === 'stockWeight') {
        value = this.packingSelectForm.stockWeight
      } else if (name === 'outerLength'){
        value = this.outerBaseForm.outerLength
      } else if (name === 'outerWidth'){
        value = this.outerBaseForm.outerWidth
      } else if (name === 'outerHeight'){
        value = this.outerBaseForm.outerHeight
      }
      let val = this.chargeNum(value, num)
      if (name === 'productCount') {
        this.structureFrom.productCount = val
      } else if (name === 'ratedAmout') {
        this.packingSelectForm.ratedAmout = val
      } else if (name === 'stockWeight') {
        this.packingSelectForm.stockWeight = val
      } else if (name === 'outerLength'){
        this.outerBaseForm.outerLength = val
      } else if (name === 'outerWidth'){
        value = this.outerBaseForm.outerWidth = val
      } else if (name === 'outerHeight'){
        value = this.outerBaseForm.outerHeight = val
      }
    },
    grossComputed() { // =1:主要、辅助包装物‘单重1合计’变化  =2:改变productCount的值
      let count = this.structureFrom.productCount
      let grossW = {'val': '', 'type':''}, initNet = {},grossVal = 0, total = 0, netTotal = 0;
      let major = this.structureFrom.zyPackaging
      let subList = this.structureFrom.itemList || []
      if (major !== "") {
        total += this.chargeWeight(major.weightReference, major.weightReferenceUnitId);
      }
      subList.forEach((item) => {
        if (item.weightReference !== "") {
          total += this.chargeWeight(item.weightReference, item.weightReferenceUnitId);
        }
      })
      if (this.level > 0){
        const modInfo = JSON.stringify(this.cmPackingStructureList[this.level-1])
        const modJson = JSON.parse(modInfo)
        initNet = this.autoWeight(count * modJson.modNetWeight, modJson.modWeightUnitId)
        grossVal = this.chargeWeight(count * modJson.modGrossWeight, modJson.modGrossUnitId)
        let modNum = count * modJson.modNum
        this.structureFrom.modNum = modNum ? count * modJson.modNum : ''
      } else {
        if (this.commodityListInfo.netWeight) {
          initNet = this.autoWeight(count * this.commodityListInfo.netWeight, this.commodityListInfo.weightUnit)
          grossVal = this.chargeWeight(count * this.commodityListInfo.netWeight, this.commodityListInfo.weightUnit)
        }
        this.structureFrom.modNum = count
      }
      if (grossVal) {
        grossW = this.autoWeight((grossVal + Number(total)).toFixed(2), 1);
      } else {
        grossW = this.autoWeight(Number(total), 1);
      }
      this.structureFrom.modNetWeight    = initNet.val || ''
      this.structureFrom.modWeightUnitId = initNet.type
      this.structureFrom.modWeightUnit   = this.charge(initNet.type, 'weightUnit')
      this.structureFrom.modGrossWeight  = grossW.val || ''
      this.structureFrom.modGrossUnitId  = grossW.type
      this.structureFrom.modGrossUnit    = this.charge(grossW.type, 'weightUnit')
    },
    grossNextComputed() { //重新计算当前层包装后边的毛重
      let level = this.level
      if (level < this.cmPackingStructureList.length -1) {
        for(let i= level + 1;i<this.cmPackingStructureList.length;i++) {
          let grossW = "", total = 0;
          let packItem = this.cmPackingStructureList[i]
          let major = packItem.zyPackaging
          let count = packItem.productCount
          if (major && major !== ''){
            let subList = packItem.itemList || []
            if (major !== "") {
              total += this.chargeWeight(major.weightReference, major.weightReferenceUnitId);
            }
            subList.forEach((item) => {
              if (item.weightReference !== "") {
                total += this.chargeWeight(item.weightReference, item.weightReferenceUnitId);
              }
            })
            const modInfo = JSON.stringify(this.cmPackingStructureList[i-1])
            const modJson = JSON.parse(modInfo)
            let initNet = this.autoWeight(count * modJson.modNetWeight, modJson.modWeightUnitId)
            let grossVal = this.chargeWeight(count * modJson.modGrossWeight, modJson.modGrossUnitId)
            let modNum = count * modJson.modNum
            this.structureFrom.modNum = modNum ? count * modJson.modNum : ''
            if (grossVal) {
              grossW = this.autoWeight((grossVal + Number(total)).toFixed(2), 1);
            } else {
              grossW = this.autoWeight(Number(total), 1);
            }
            this.cmPackingStructureList[i].modNetWeight    = initNet.val || ''
            this.cmPackingStructureList[i].modWeightUnitId = initNet.type
            this.cmPackingStructureList[i].modWeightUnit   = this.charge(initNet.type, 'weightUnit')
            this.cmPackingStructureList[i].modNum = modNum
            this.cmPackingStructureList[i].modGrossWeight = grossW.val || ''
            this.cmPackingStructureList[i].modGrossUnitId= grossW.type
            this.cmPackingStructureList[i].modGrossUnit = this.charge(grossW.type, 'weightUnit')
          }
        }
      }
    },
    chargeNum(value){
      value = value.replace(/[^\d.]/g, "");  //清除除了“数字”和“.”以外的字符
      value = value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
      value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      value = value.replace(/^(\\-)*(\\d+)\\.(\\d{'+num+'}).*$/, '$1$2.$3');//只能输入3个小数
      if (value.indexOf(".") < 0 && value != "") {
        //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        value = parseFloat(value);
      }
      return value
    },
    chargeLevel(num){
      const chars = ['零','一','二','三','四','五','六','七','八','九']
      return chars[num]
    },
    hideFun(str){
      switch (str) {
        case 'settingVisible':
          this.settingVisible = false
          break
        case 'scanSubLog':
          this.scanSubLog = false
          break
        case 'selectPackageLog':
          this.selectPackageLog = false
          break
        case 'addPackageLog':
          this.addPackageLog = false
          break
        case 'scanSubPackLog':
          this.scanSubPackLog = false
          break
        case 'addUnitLog':
          this.addUnitLog = false
          break
        case 'scanMajLog':
          this.scanMajLog = false
          break
        case 'delVisible':
          this.delVisible = false
          break
        case 'delSubPackLog':
          this.delSubPackLog = false
          break
        case 'tipVisible':
          this.tipVisible = false
          break
        case 'manualBookLog':
          this.manualBookLog = false
          break
        case 'outerBaseLog':
          this.outerBaseLog = false
          break
      }
    },
    handleNull(str) {
      var result = str === 'null' || str === null || str === undefined ? '':str;
      return result;
    },
  }
}

</script>

<style lang="scss" scoped>
#packingManage{
  padding: 30px 100px;
  font-size: 14px;
  .ty-color-gray {color: #aaa;}
  .unSection{margin-bottom:  40px;}
  .lineQuery{margin-bottom:  20px;}
  .unSection > span:nth-child(2){ margin-left:  200px;}
  .unSection .el-link{ margin-left:  100px;}
  .linkBtn {  color: #036bc0;cursor: default;}
  .redLinkBtn { margin-right: 50px; color: #f80202;cursor: default;}
  .tipCon{overflow: hidden;padding-top:20px;font-size: 12px;}
  .spTtl{display: inline-block; width: 200px;}
  .backPage{margin-bottom:  20px;}
  .wrapLab{margin: 20px auto;width: 510px;}
  .modeSelect .fa{margin-right: 10px;}
  .modelPattern{margin-left: 100px;padding: 10px;}
  .sectCon{padding: 28px 0;}
  .hrLine, .packageItem{border-top:  1px solid #d7d7d7;}
  .packageItem{padding-top: 24px;}
  .packageItem > div{padding-bottom: 24px;overflow: hidden;}
  .red {color: #f80202;}
  .fmWrapper{margin: auto;width: 400px;}
  .pack_form { padding-top: 20px;}
  .pack_form li{ padding: 6px 0;position: relative;}
  .pack_form .ty-inputText,.pack_form .ty-inputSelect{ width: 100%;}
  .pannelTab td{ padding:10px 40px; width: 362px;  }
  .pannelTab td .tbTtl{ padding-left:2px;padding-bottom:10px;  }
  .pannelTab{ width: 924px; margin: 10px auto;}
  .line{ border-top: 1px solid #bfbfbf; width: 830px; margin: 0 auto; }
  .modInput .weightText{width: 280px;}
  .modInput .weightSelect{width: 98px;}
  .manualEct{margin: auto;width: 600px;}
  .manualEct p{margin-bottom: 20px;}
  .manualEct p > span{display: inline-block; width: 136px;}
  .areaCon{position: relative;}
  .usageUnitName,.unitVal{position: absolute;  right: 4px;  line-height: 30px;  font-size: 14px;}
  .bTip{color: #366091}
  .shapeCon{margin-top: 20px;padding-top: 20px;border-top: 1px solid #c2c1c1;}
  .shapeCon p{margin-bottom: 10px;}
  .topName{padding: 20px 0;border-bottom: 1px solid #c2c1c1;}
  .gapTtl{display: inline-block; width: 200px;}
  .linkWrap .gap{margin: 0 50px;}
  .pckRow{padding: 20px 0 0 20px;}
  .outer_form li{ padding: 14px 0;}
  .outer_form li input{ width: 336px;}
  .outer_form li >div{ margin-bottom: 6px;}
  .outerShape{display: flex;justify-content: space-between;}
  .outerShape input{margin-right: 14px;}
  .mainRows,.mainOnlyRow{padding-top: 20px;}
  .gapL{margin-left: 50px;}
  .gapLt{margin-left: 50px;}
  .gapBt{margin-bottom: 16px;}
  .bonceCon hr{border-bottom: 1px solid #d7d4d4;margin-bottom: 20px;margin-top: 20px;border-top: none;}
  .packageScanItem{padding: 20px 0 30px 0;margin-bottom: 20px;border-bottom: 1px solid #b1b1b1;}
  .packageScanItem:last-child{border-bottom: none;}
  .keywordSearch input,.bonceCon .keywordSearch input {padding: 0 10px;font-size: 12px;min-width: 200px;height: 28px;line-height: 28px;border: 1px solid #b6b6b6;}
  .limitWid{padding-bottom: 20px;}
  .limitWid span:first-child{display: inline-block; width: 60px;}
  .effectTimeCon{margin-top: 20px;padding-top: 20px;border-top: 1px solid #c1c1c1;}
  .effectTimeCon p{margin-bottom: 10px;}
  .searchIcon{float: left;background-position: 0 7px;background-repeat: no-repeat;background-size: 20px;
    height: 30px;width: 30px;cursor: pointer;}
  .searchSect .searchBtn{padding: 0 22px;height: 30px;}
  .ty-clear,.clear{overflow: hidden}
  .midBox{width: 360px;}
  .tyDialog input:not([type='radio']):not([type='checkbox']),.tyDialog select,.modInput input,.modInput select{ border:1px solid #dcdfe6; border-radius:2px;
    background-color: #fff;display:inline-block;  height:30px;line-height:30px;  background-image: none;  box-sizing: border-box;  color: #606266;  font-size: inherit;
    outline: none;  padding: 0 8px;transition: border-color .2s cubic-bezier(.645,.045,.355,1); }
  .tyDialog input:disabled,.tyDialog select:disabled,.modInput input:disabled,.modInput select:disabled{  background-color: #dddddd!important;  }
  .tyDialog input:focus,.tyDialog select:focus,.modInput input:focus,.modInput select:focus{  border:1px solid #48cfad; }
  .modInput .ty-inputText,.modInput .ty-inputSelect,.effectTimeCon .el-input{  width: 100%; }
  .outer_form select{margin-left: 4px;width: 60px;}
  .tyDialog #packageAll input{width: 120px;}
  .backPage span{margin-right: 10px;}
  .txtRight{text-align: right;}
  .unitPos{position: relative;}
  .unitPos .matUnit{position: absolute;right: -34px;display: inline-block;width: 32px;}
  .gapRt{margin-right: 10px;}
  .gapT{margin-bottom: 10px;}
  .pack_form .el-form-item{margin-bottom: 0;}
  .queryTip{margin-top: 10px;}
}

</style>
