<template>
  <div class="billScan">
    <div class="mainCon" v-show="mainPage === 1">
      <tySecondTab class="marBtm22" :namelist="namelist" :changeTabFun="changeTab" ></tySecondTab>
      <div class="opinionCon" v-if="tabNo === 0">
        <p class="gapBt ty-left">以下为所收到且尚在手中的外部承兑汇票，共<span id="total_bill">{{tabIndex0Data.amount.toFixed(2)}} </span>张，
          总计<span id="amount_bill">{{tabIndex0Data.total}} </span>元</p>
        <el-link class="ty-right moreBtn" type="primary" @click="moreData(2)">更多数据</el-link>
        <table class="ty-table ty-table-control">
          <thead>
          <td width="10%">汇票号后六位</td>
          <td width="15%">到期日</td>
          <td width="10%">金额</td>
          <td width="10%">付款单位</td>
          <td width="15%">出具的银行</td>
          <td width="20%">创建</td>
          <td width="10%">汇票是否修改过</td>
          <td width="10%">操作</td>
          </thead>
          <tbody id="accept_tbody">
          <tr v-for="(item, index) in tabIndex0Data.financeReturns" :key="index">
            <td>{{item.returnNo}}</td>
            <td>{{new Date(item["expireDate"]).format('yyyy-MM-dd')}}</td>
            <td>{{parseFloat(item["amount"]).toFixed(2)}}</td>
            <td>{{item.payer}}</td>
            <td>{{item.bankName}}</td>
            <td>{{item.createName}} {{new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
            <td>{{(item["operation"] === 3 || item["operation"] === '3'? '是':'否')}}</td>
            <td>
              <span class="ty-color-blue" @click="accept_see(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="opinionCon" v-if="tabNo === 1">
        <p class="ty-left gapBt">以下为所收到且尚在手中的外部转账支票，共<span id="total">{{tabIndex1Data.total}} </span>张，
          总计<span id="amount">{{tabIndex1Data.amount.toFixed(2)}} </span>元</p>
        <el-link class="ty-right moreBtn" type="primary" @click="moreData(1)">更多数据</el-link>
        <table class="ty-table ty-table-control">
          <thead>
          <td width="10%">支票号后六位</td>
          <td width="15%">到期日</td>
          <td width="10%">金额</td>
          <td width="10%">付款单位</td>
          <td width="15%">出具的银行</td>
          <td width="20%">创建</td>
          <td width="10%">支票是否修改过</td>
          <td width="10%">操作</td>
          </thead>
          <tbody id="bill_tbody">
          <tr v-for="(item, index) in tabIndex1Data.financeReturns" :key="index">
            <td>{{item.returnNo}}</td>
            <td>{{new Date(item["expireDate"]).format('yyyy-MM-dd')}}</td>
            <td>{{parseFloat(item["amount"]).toFixed(2)}}</td>
            <td>{{item.payer}}</td>
            <td>{{item.bankName}}</td>
            <td>{{item.createName}} {{new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
            <td>{{(item["operation"] === 3 || item["operation"] === '3'? '是':'否')}}</td>
            <td>
              <span class="ty-color-blue" @click="accept_see(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

<!--      <TyPage v-if="pageShow"
              :curPage="pageInfo.currentPageNo" :pageSize="pageInfo.pageSize"
              :allPage="pageInfo.totalPage" :pageClickFun="pageClick"></TyPage>-->

    </div>
    <div class="mainCon" v-if="mainPage === 2">
      <div class="backPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="showMainCon(1)">返  回</span>
      </div>
      <div class="more-size">
        <ul class="moreList">
          <li>
            <span>今年收到{{tabNo === 1?'转账支票':'承兑汇票'}}</span>
            <span id="thisYearNum">{{ moreYear.yearTime.num || 0 }}张</span>
            <span id="thisYearAmount">{{ (moreYear.yearTime.amount || 0).toFixed(2) }}元</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="periodScan(1)">查  看</span>
          </li>
          <li>
            <span>去年收到{{tabNo === 1?'转账支票':'承兑汇票'}}</span>
            <span id="lastYearNum">{{ moreYear.lastYearTime.num || 0 }}张</span>
            <span id="lastYearAmount">{{ (moreYear.lastYearTime.amount || 0).toFixed(2) }}元</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="periodScan(2)">查  看</span>
          </li>
          <li>
            <span>前年收到{{tabNo === 1?'转账支票':'承兑汇票'}}</span>
            <span id="agoYearNum">{{ moreYear.beforeYearTime.num || 0 }}张</span>
            <span id="agoYearAmount">{{ (moreYear.beforeYearTime.amount || 0).toFixed(2) }}元</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="periodScan(3)">查  看</span>
          </li>
        </ul>
        <div class="moreY">
          <span>更多年份的数据</span>
          <el-date-picker v-model="moreYSearch"  placeholder="请选择年份" id="outerEffect"
                          type="year"
                          format="YYYY"
                          value-format="YYYY"
          >
          </el-date-picker>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapX" @click="periodScan(4)">确  定</span>
        </div>
      </div>
    </div>
    <div class="mainCon" v-if="mainPage === 3">
      <div class="backPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapRt" @click="showMainCon(1);">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="showMainCon(2);">返回上一页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 ty-right" id="draftCountBtn" @click="draftCount(1);">统  计</span>
      </div>
      <div>
        <p class="periodDetail gapBt">
          {{new Date(moreYearData.beginDate).format('yyyy-MM-dd')}}至
          {{new Date(moreYearData.endDate).format('yyyy-MM-dd')}}期间收到的外部{{tabNo === 1?'转帐支票':'承兑汇票'}}共{{moreYearData.numAmount.num}}张，{{(moreYearData.numAmount.amount || 0).toFixed(2)}}元
        </p>
        <table class="ty-table ty-table-control">
          <thead>
          <td width="10%"><span class="cateTtl">{{tabNo === 1?'支票':'汇票'}}</span>号后六位</td>
          <td width="15%">到期日</td>
          <td width="10%">金额</td>
          <td width="10%">付款单位</td>
          <td width="15%">出具的银行</td>
          <td width="20%">创建</td>
          <td width="10%"><span class="cateTtl">{{tabNo === 1?'支票':'汇票'}}</span>是否修改过</td>
          <td width="10%">操作</td>
          </thead>
          <tbody id="billPeriod">
          <tr v-for="(item, index) in moreYearData.financeReturns" :key="index">
            <td>{{item.returnNo}}</td>
            <td>{{new Date(item["expireDate"]).format('yyyy-MM-dd')}}</td>
            <td>{{parseFloat(item["amount"]).toFixed(2)}}</td>
            <td>{{(item.payer || '')}}</td>
            <td>{{item.bankName}}</td>
            <td>{{item.createName}} {{new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
            <td>{{(Number(item["operation"]) === 3 ? '是':'否')}}</td>
            <td>
              <span class="ty-color-blue" @click="accept_see(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="mainCon" v-if="mainPage === 4">
      <div class="backPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapRt" @click="showMainCon(1);">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="showMainCon(3);">返回上一页</span>
      </div>
      <div class="more-size">
        <p class="periodDetail gapBt">
          {{new Date(moreYearData.beginDate).format('yyyy-MM-dd')}}至
          {{new Date(moreYearData.endDate).format('yyyy-MM-dd')}}期间收到的外部{{tabNo === 1?'转帐支票':'承兑汇票'}}共{{moreYearData.numAmount.num}}张，{{(moreYearData.numAmount.amount || 0).toFixed(2)}}元
        </p>
        <ul class="moreList">
          <li>
            <span>还在手中的</span>
            <span id="stillNum">{{ (countData.available.num || 0) }}张</span>
            <span id="stillAmount">{{countData.available.amount}}元</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="countScan(1)">查  看</span>
          </li>
          <li>
            <span>存入银行的</span>
            <span id="bankNum">{{ (countData.saveBanks.num || 0) }}张</span>
            <span id="bankAmount">{{ countData.saveBanks.amount }}元</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="countScan(2)">查  看</span>
          </li>
          <li>
            <span>又付出去的</span>
            <span id="payNum">{{ (countData.payReturns.num || 0) }}张</span>
            <span id="payAmount">{{ countData.payReturns.amount }}元</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="countScan(4)">查  看</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="mainCon" v-if="mainPage === 5">
      <div class="backPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapRt" @click="showMainCon(1);">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="showMainCon(4);">返回上一页</span>
      </div>
      <div>
        <p class="byCountPeriod">
          {{new Date(byMethodData.beginDate).format('yyyy-MM-dd')}}至
          {{new Date(byMethodData.endDate).format('yyyy-MM-dd')}}期间收到的外部{{tabNo === 1?'转帐支票':'承兑汇票'}}中，
            <span v-if="countScanType === 1">还在手中的共{{byMethodData.numAmount.num}}张</span>
            <span v-if="countScanType === 2">存入银行的共{{byMethodData.numAmount.num}}张</span>
            <span v-if="countScanType === 4">又付出去的共{{byMethodData.numAmount.num}}张</span>
          ,{{byMethodData.numAmount.amount}}元
        </p>
        <div>
          <table class="ty-table ty-table-control byCurrently" v-if="countScanType === 1">
            <thead>
            <td width="10%"><span class="cateTtl">{{tabNo === 1?'支票':'汇票'}}</span>号后六位</td>
            <td width="14%">到期日</td>
            <td width="10%">金额</td>
            <td width="10%">付款单位</td>
            <td width="14%">出具的银行</td>
            <td width="20%">创建</td>
            <td width="10%"><span class="cateTtl">{{tabNo === 1?'支票':'汇票'}}</span>是否修改过</td>
            <td width="12%">操作</td>
            </thead>
            <tbody>
            <tr v-for="(item, index) in byMethodData.financeReturns" :key="index">
              <td>{{item.returnNo}}</td>
              <td>{{new Date(item["expireDate"]).format('yyyy-MM-dd')}}</td>
              <td>{{parseFloat(item["amount"]).toFixed(2)}}</td>
              <td>{{item.payer || ''}}</td>
              <td>{{item.bankName}}</td>
              <td>{{item.createName}} {{new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
              <td>{{(item["operation"] === 3 || item["operation"] === '3'? '是':'否')}}</td>
              <td>
                <span class="ty-color-blue" @click="accept_see(item)">查看</span>
              </td>
            </tr>
            </tbody>
          </table>
          <table class="ty-table ty-table-control byBank" v-if="countScanType === 2">
            <thead>
            <td width="10%"><span class="cateTtl">{{tabNo === 1?'支票':'汇票'}}</span>号后六位</td>
            <td width="10%">金额</td>
            <td width="10%">付款单位</td>
            <td width="22%">到期日/存入银行的日期/到账日期</td>
            <td width="18%">所存入的银行账户</td>
            <td width="20%">在系统内操作“存入银行”者</td>
            <td width="10%">操作</td>
            </thead>
            <tbody>
            <tr v-for="(item, index) in byMethodData.financeReturns" :key="index">
              <td>{{item.returnNo}}</td>
              <td>{{parseFloat(item["amount"]).toFixed(2)}}</td>
              <td>{{item.payer}}</td>
              <td>{{new Date(item["expireDate"]).format('yyyy-MM-dd')}}/
                {{new Date(item.depositDate).format('yyyy-MM-dd')}}/
                {{ new Date(item.receiveAccountDate).format('yyyy-MM-dd')}}</td>
              <td>{{item.accout}}</td>
              <td>{{item.updateName}} {{new Date(item["updateDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
              <td>
                <span class="ty-color-blue" @click="accept_see(item)">查看</span>
              </td>
            </tr>
            </tbody>
          </table>
          <table class="ty-table ty-table-control byPayOut" v-if="countScanType === 4">
            <thead>
            <td width="10%"><span class="cateTtl">{{tabNo === 1?'支票':'汇票'}}</span>号后六位</td>
            <td width="15%">到期日</td>
            <td width="10%">金额</td>
            <td width="10%">付款单位</td>
            <td width="14%">接收单位</td>
            <td width="14%">接收日期</td>
            <td width="18%">在系统内操作“又付出去”者</td>
            <td width="14%">操作</td>
            </thead>
            <tbody>
            <tr v-for="(item, index) in byMethodData.financeReturns" :key="index">
              <td>{{item.returnNo}}</td>
              <td>{{new Date(item["expireDate"]).format('yyyy-MM-dd')}}</td>
              <td>{{parseFloat(item["amount"]).toFixed(2)}}</td>
              <td>{{item.payer}}</td>
              <td>{{(item["oppositeCorp"] || '')}}</td>
              <td>{{new Date(item.receiveAccountDate).format('yyyy-MM-dd')}}</td>
              <td>{{item.updateName}} {{new Date(item["updateDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
              <td>
                <span class="ty-color-blue" @click="accept_see(item)">查看</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <!--  弹窗的高低顺序 是按照代码的前后顺序   -->
    <TyDialog v-if="detailLog" width="800" :dialogTitle="tabNo === 1?'转账支票查看':'承兑汇票查看'" color="blue" :dialogHide="hideFun" :dialogName="'detailLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideFun('detailLog')">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="align-left wrapSize">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="baseRecord()">修改记录</span>
          <div v-if="(mainPage === 3 || mainPage === 5) && [2,'2',4,'4'].indexOf(billDetails.financeReturn.state) > -1">
            <div class="paddT ty-clear">
              <p class="ty-left tTtl operateCon">{{ billDetails.financeReturn.state === '2'?'存入银行的信息':'付款信息' }}</p>
              <p class="ty-right">操作 <span class="operateDate">{{billDetails.financeReturn.updateName + ' ' + new Date(billDetails.financeReturn.updateDate).format('yyyy-MM-dd hh:mm:ss')}}</span></p>
            </div>
            <div>
              <table  class="ty-table saveStore2" v-if="billDetails.financeReturn.state === '2'">
                <tbody>
                <tr>
                  <td width="40%">存入银行账户</td>
                  <td id="saveBank">{{billDetails.financeReturn.saveBankName}} {{billDetails.financeReturn.accout}}</td>
                </tr>
                <tr>
                  <td>存入时间</td>
                  <td id="saveTime">{{new Date(billDetails.financeReturn.depositDate).format('yyyy-MM-dd')}}</td>
                </tr>
                <tr>
                  <td>存入经手人</td>
                  <td id="saveHandler">{{billDetails.financeReturn.depositorName}}</td>
                </tr>
                <tr>
                  <td>到账时间</td>
                  <td id="saveArriveDate">{{new Date(billDetails.financeReturn.receiveAccountDate).format('yyyy-MM-dd')}}</td>
                </tr>
                </tbody>
              </table>
              <table  class="ty-table saveStore4" v-if="billDetails.financeReturn.state === '4'">
                <tbody>
                <tr>
                  <td width="40%">收款单位</td>
                  <td id="payeeName">{{billDetails.financeReturn.oppositeCorp || ''}}</td>
                </tr>
                <tr>
                  <td>实际付款日期</td>
                  <td id="payDate">{{new Date(billDetails.financeReturn.factDate).format('yyyy-MM-dd')}}</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="paddT ty-clear">
            <p class="ty-left tTtl">票面信息</p>
            <p class="ty-right">创建 <span class="createInfo">{{(billDetails.financeReturn["createName"] + ' ' +  new Date(billDetails.financeReturn["createDate"]).format('yyyy-MM-dd hh:mm:ss'))}}</span></p>
          </div>
          <table  class="ty-table">
            <tbody>
            <tr>
              <td width="40%" class="snTtl">{{ tabNo === 1? '支票号':'汇票号' }}</td>
              <td colspan="3" id="billSn">{{billDetails.financeReturn.returnNo}}</td>
            </tr>
            <tr>
              <td>金额</td>
              <td id="billAmount">{{billDetails.financeReturn.amount}}</td>
            </tr>
            <tr>
              <td>到期日</td>
              <td id="billDueDate">{{new Date(billDetails.financeReturn.expireDate).format('yyyy-MM-dd')}}</td>
            </tr>
            <tr>
              <td class="unitTtl">{{ tabNo === 1? '出具的单位':'最初出具的单位' }}</td>
              <td id="billUnit">{{billDetails.financeReturn.originalCorp}}</td>
            </tr>
            <tr>
              <td>出具的银行</td>
              <td id="billBank">{{billDetails.financeReturn.bankName}}</td>
            </tr>
            <tr>
              <td>付款单位</td>
              <td id="acceptSee_payer">{{billDetails.financeReturn["payer"]}}</td>
            </tr>
            </tbody>
          </table>
          <p class="paddT tTtl">其他信息</p>
          <table  class="ty-table">
            <tbody>
            <tr>
              <td width="40%">数据来源</td>
              <td id="acceptSee_item" v-if="billDetails.source === 'A' || Number(billDetails.source) === 3">{{ billDetails.source === 'A' ? '需回收的款': '回款录入' }}</td>
              <td id="acceptSee_item" v-else>数据录入－收入－{{ chargeCateroy( billDetails.financeReturn["category"] ) }}</td>
            </tr>
            <tr>
              <td>用途</td>
              <td id="acceptSee_use">{{billDetails.financeReturn.purpose}}</td>
            </tr>
            <tr>
              <td>摘要</td>
              <td id="acceptSee_abstract">{{billDetails.financeReturn.summary}}</td>
            </tr>
            <tr>
              <td>收到日期</td>
              <td id="acceptSee_getbilldata">{{new Date(billDetails.financeReturn["receiveDate"]).format('yyyy-MM-dd')}}</td>
            </tr>
            <tr class="sourceIncome" v-if="billDetails.source === '1' || billDetails.source === 1">
              <td>所开具发票或收据的金额</td>
              <td id="acceptSee_billMoney">{{formatMoney(billDetails.financeReturn.billAmount)}}</td>
            </tr>
            <tr class="sourceIncome" v-if="billDetails.source === '1' || billDetails.source === 1">
              <td>经手人</td>
              <td id="acceptSee_guy">{{billDetails.financeReturn.operatorName}}</td>
            </tr>
            <tr class="sourceIncome" v-if="billDetails.source === '1' || billDetails.source === 1">
              <td>备注</td>
              <td id="acceptSee_remark">{{billDetails.financeReturn.memo}}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="historyLog" width="500" dialogTitle="修改记录" color="blue" :dialogHide="hideFun" :dialogName="'historyLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('historyLog')">取消</el-button>
      </template>
      <template #dialogBody>
        <table class="ty-table ty-table-control">
          <tbody>
          <tr>
            <td>资料状态</td>
            <td>修改的结果</td>
            <td>创建人/修改人</td>
          </tr>
          <tr v-for="(item, index) in financeHistoryList" :key="index">
            <td>{{ index === financeHistoryList.length -1 ? '原始信息' : '第'+ (financeHistoryList.length - 1 - index)+'次修改后' }}</td>
            <td class="ty-td-control"><span @click="logDetails(item)" class="ty-color-blue">查看</span></td>
            <td>{{ item.createName }} {{ index === financeHistoryList.length -1 ? new Date(item.createDate ).format("yyyy-MM-dd hh:mm:ss"): new Date(item.updateDate ).format("yyyy-MM-dd hh:mm:ss")  }} </td>
          </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>
    <TyDialog v-if="recordDetailLog" width="800" :dialogTitle="tabNo === 1?'转账支票查看':'承兑汇票查看'" color="blue" :dialogHide="hideFun" :dialogName="'recordDetailLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('recordDetailLog')">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div class="align-left">
          <div class="paddT ty-clear">
            <p class="ty-left tTtl">票面信息</p>
          </div>
          <table  class="ty-table">
            <tbody>
            <tr>
              <td width="40%" class="snTtl">{{ tabNo === 1? '支票号':'汇票号' }}</td>
              <td colspan="3" id="billSn">{{recordDetails.returnNo}}</td>
            </tr>
            <tr>
              <td>金额</td>
              <td id="billAmount">{{recordDetails.amount}}</td>
            </tr>
            <tr>
              <td>到期日</td>
              <td id="billDueDate">{{new Date(recordDetails.expireDate).format('yyyy-MM-dd')}}</td>
            </tr>
            <tr>
              <td class="unitTtl">{{ tabNo === 1? '出具的单位':'最初出具的单位' }}</td>
              <td id="billUnit">{{recordDetails.originalCorp}}</td>
            </tr>
            <tr>
              <td>出具的银行</td>
              <td id="billBank">{{recordDetails.bankName}}</td>
            </tr>
            <tr>
              <td>付款单位</td>
              <td id="acceptSee_payer">{{recordDetails["payer"]}}</td>
            </tr>
            </tbody>
          </table>
          <p class="paddT tTtl">其他信息</p>
          <table  class="ty-table">
            <tbody>
            <tr>
              <td width="40%">数据来源</td>
              <td id="acceptSee_item" v-if="financeHistorySource.source === 'A' || Number(financeHistorySource.source) === 3">{{ financeHistorySource.source === 'A' ? '需回收的款': '回款录入' }}</td>
              <td id="acceptSee_item" v-else>数据录入－收入－{{ chargeCateroy( recordDetails["category"] ) }}</td>
            </tr>
            <tr>
              <td>用途</td>
              <td id="acceptSee_use">{{recordDetails.purpose}}</td>
            </tr>
            <tr>
              <td>摘要</td>
              <td id="acceptSee_abstract">{{recordDetails.summary}}</td>
            </tr>
            <tr>
              <td>收到日期</td>
              <td id="acceptSee_getbilldata">{{new Date(recordDetails["receiveDate"]).format('yyyy-MM-dd')}}</td>
            </tr>
            <tr class="sourceIncome" v-if="!(financeHistorySource.source === 'A' && Number(financeHistorySource.source) === 3)">
              <td>所开具发票或收据的金额</td>
              <td id="acceptSee_billMoney">{{formatMoney(recordDetails.billAmount)}}</td>
            </tr>
            <tr class="sourceIncome" v-if="!(financeHistorySource.source === 'A' && Number(financeHistorySource.source) === 3)">
              <td>经手人</td>
              <td id="acceptSee_guy">{{recordDetails.operatorName}}</td>
            </tr>
            <tr class="sourceIncome" v-if="!(financeHistorySource.source === 'A' && Number(financeHistorySource.source) === 3)">
              <td>备注</td>
              <td id="acceptSee_remark">{{recordDetails.memo}}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
  </div>
</template>
<script>
import * as api from "@/api/companyOverview";
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import tySecondTab from "@/components/tySecondTab.vue";
import { formatMoney } from'@/utils/formTest'

export default {
  data() {
    return {
      pageName:'overviewReplyBill',
      namelist: ['承兑汇票台账','转账支票台账'],
      tabIndex0Data: {amount: 0},
      tabIndex1Data: {amount: 0},
      mainPage: 1,
      tabNo: 0,
      countScanType: 1,
      periodRes: '',
      countData: {},
      billDetails: {},
      recordDetails: {},
      financeHistoryList: [],
      financeHistorySource: {},
      moreYear: {},
      moreYSearch: '',
      moreYearData: {
        numAmount:{
          num: 0
        }
      },
      byMethodData: {},
      detailLog: false,
      historyLog: false,
      recordDetailLog: false,
    }
  },
  components: {
    tySecondTab,
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'bgb', name: '票据', pageName: this.pageName}, this.accept, this)
  },
  mounted() {
  },
  methods: {
    formatMoney,
    accept(){
      let json = { "type":1, 	"state":1 	}
      if (this.tabNo === 0) {
        json.type = 2
      }
      api.getAccept(json).then(res => {
        let data = res.data.data
        if (this.tabNo === 0) {
          this.tabIndex0Data = data;
        } else {
          this.tabIndex1Data = data;
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    changeTab(tabNum) {
      this.tabNo = tabNum
      this.accept()
    },
    accept_see(info){
      var returnId = info.id;
      if ( returnId == undefined || returnId == "") {
        this.$message.error('系统错误，刷新重试！')
        return false;
      }
      let json = { "returnId" : returnId   }
      api.getAcceptDetail(json).then(res => {
        let data = res.data.data;
        //operation：操作/汇票是否修改过:1-增,2-删,3-修改(在存入银行之前的修改，3是修改，其他都是否)
        //source：数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
        //         5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8报销时多付出去的款---需收回的款(1.231差额处理) 9多收来的款(1.233差额处理)
        //         10-多收来的款-需收回的款(1.233差额处理)
        data.financeReturn.amount = formatMoney(data.financeReturn.amount)
        this.billDetails = data
        this.detailLog = true
      }).catch(err => {
        console.log('err=', err)
      })
    },
    baseRecord(){
      let resData = {
        "returnId": this.billDetails.financeReturn.id
      }
      api.getReturnRecords(resData).then(res => {
        let data = res.data.data.financeReturnHistories || [];
        this.financeHistoryList = data
        this.financeHistorySource = res.data.data
        this.historyLog = true
      }).catch(err => {
        console.log('err=', err)
      })
    },
    logDetails(info){
      const detail = JSON.parse(JSON.stringify(info))
      detail.amount = formatMoney(detail.amount)
      this.recordDetails = detail
      this.recordDetailLog = true
    },
    moreData(num){
      //type：1-转帐支票，2-承兑汇票
      let json = { "type": num}
      api.getMoreData(json).then(res => {
        let data = res.data.data;
        this.moreYear = data
        this.mainPage = 2
      }).catch(err => {
        console.log('err=', err)
      })
    },
    periodScan(num){
      let today = new Date();
      let resDate = ``;
      let kind = this.tabNo === 1 ? 1: 2
      if (num === 1) {
        resDate = today.format('yyyy-MM-dd');
      } else if (num === 2) {
        let time = new Date(today.getFullYear() - 1, 2, 1)
        resDate = time.format('yyyy-MM-dd');
      } else if (num === 3) {
        let time = new Date(today.getFullYear() - 2, 2, 1)
        resDate = time.format('yyyy-MM-dd');
      } else if (num === 4){
        let year = this.moreYSearch
        if (year === '') {
          this.$message.error("请选择年份！")
          return false;
        }
        let time = year + '-01-01';
        resDate = time;
      }
      this.periodRes = resDate;
      this.mainPage = 3
      let json = {
        "type": kind, //type：1-转帐支票，2-承兑汇票
        //"state": 1, //state: 1-有效,2-存入银行,3-作废，4-已支出
        "beginTime": resDate
      }
      api.getMoreDataList(json).then(res => {
        let data = res.data.data;
        this.moreYearData = data
      }).catch(err => {
        console.log('err=', err)
      })
    },
    draftCount(){
      let kind = this.tabNo === 1 ? 1: 2
      let json = {
        "type": kind, //type：1-转帐支票，2-承兑汇票
        "beginTime": this.periodRes
      }
      api.getCountData(json).then(res => {
        let data = res.data.data;
        data.available.amount = (data.available.amount || 0).toFixed(2)
        data.saveBanks.amount = (data.saveBanks.amount || 0).toFixed(2)
        data.payReturns.amount = (data.payReturns.amount || 0).toFixed(2)
        this.countData = data
        this.showMainCon(4)
      }).catch(err => {
        console.log('err=', err)
      })
    },
    countScan(type){
      let kind = this.tabNo === 1 ? 1: 2
      let json = {
        "type": kind, //type：1-转帐支票，2-承兑汇票
        "state": type, //state: 1-有效,2-存入银行,3-作废，4-已支出
        "beginTime": this.periodRes
      }
      this.countScanType = type
      api.getByMethod(json).then(res => {
        let data = res.data.data;
        this.byMethodData = data
        this.showMainCon(5);
      }).catch(err => {
        console.log('err=', err)
      })
    },
    showMainCon(num){
      this.mainPage = num
    },
    chargeCateroy(num){
      switch (Number( num )){
        case 1:
          return "货款"
          break
        case 2 : return "借款" ; break ;
        case 3 : return "投资款" ; break ;
        case 4 : return "废品" ; break ;
        case 5 : return "其他" ; break ;
        default : break ;
      }
    },
    hideFun(str){
      switch (str) {
        case 'recordDetailLog':
          this.recordDetailLog = false
          break
        case 'historyLog':
          this.historyLog = false
          break
        case 'detailLog':
          this.detailLog = false
          break
      }
    },

  }
}

</script>

<style lang="scss" scoped>
.billScan{
  padding-top: 20px;
  font-size: 14px;
  .mainCon{ margin: 0 100px;}
  .gapBt{ margin-bottom: 10px; }
  .gapRt{ margin-right: 10px; }
  .marBtm22{ margin-bottom: 20px; }
  .gapX{margin-left: 134px;}
  .moreList li{margin-bottom: 20px;}
  .moreList li span:nth-child(1){margin-right: 50px;display: inline-block;width: 162px;}
  .moreList li span:nth-child(2){margin-right: 20px;display: inline-block;width: 150px;}
  .moreList li span:nth-child(3){margin-right: 10px;display: inline-block;min-width: 160px;}
  .moreY {padding-top: 30px;}
  .moreY span:nth-child(1){margin-right: 100px;width: 162px;}
  .moreY input{margin-right: 150px;display: inline-block;width: 200px;padding: 3px 2px;}
  .link-blue{color: #0070c0; font-weight: 700;}
  .moreBtn{margin-right: 20px;font-size: 16px;color: #0070c0;font-weight: 700;}
  .backPage{padding-bottom: 40px;}
  .paddT{padding:24px 0 12px 0;}
  .tTtl{padding-left: 14px;}
  .align-left .ty-table  td{text-align: left;}
  .ty-clear {overflow: hidden;}
  .wrapSize{margin: auto;width: 80%;}
}

</style>
