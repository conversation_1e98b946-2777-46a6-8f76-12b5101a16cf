<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="myAttendanceUpdateDetails" class="attendanceCon" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="placeContainer">
        <div class="ttl1">{{approvalProcess.description}}</div>
        <div class="ttl1">修改理由 {{attendanceUserHistory.updateDesc}}</div>
        <div class="ttl2">
          <div class="rightSide">
            <span v-on:click="recordToggle" class="processRecord color-blue">审批记录 <i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i></span>
            <div class="processContent" v-show="toggle > 0">
              <div >
                <span class="process_desName">申请人</span>
                <span class="process_name">{{attendanceUserHistory.createName}}</span>
                <span>{{ attendanceUserHistory.createDate | formatDay('YYYY-MM-DD HH:mm:ss') }}</span>
              </div>
              <p class="color-blue" v-if="approvalProcess.approveStatus === '1' && params.mark !== '2'">{{approvalProcess.userName}}为本申请的下一个审批人</p>
              <div >
                <div v-if="approvalProcess.approveStatus === '2'">
                  <div>
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{approvalProcess.userName}}</span>
                    <span>{{approvalProcess.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                </div>
                <div v-if="approvalProcess.approveStatus === '3'">
                  <div>
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{approvalProcess.userName}}</span>
                    <span>{{approvalProcess.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red" style="text-align: left; position: relative; top:-18px;">
                    <div>被驳回</div>
                    <div style="word-break: break-all;"><span>驳回理由：</span>{{approvalProcess.reason}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="clr2"></div>
        </div>
        <div>
          <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
            <el-tab-pane label="修改后" name="after">
              <table class="table">
                <tbody>
                <tr>
                  <th class="th">上班</th>
                  <td>{{attendanceUserHistory.beginState | formatState}}</td>
                </tr>
                <tr>
                  <th class="th">下班</th>
                  <td>{{attendanceUserHistory.endState | formatState}}</td>
                </tr>
                <template v-for="(item, index) in  attendanceUsesrDetailHistorys">
                  <tr v-if="item.type === '7'" v-bind:key="index">
                    <th class="th">旷工</th>
                    <td>
                      {{item.beginTime | formatDay('HH:mm')}} - {{item.endTime | formatDay('HH:mm')}}
                    </td>
                  </tr>
                  <tr v-if="item.type === '5'" v-bind:key="index">
                    <th class="th">请假 - {{item.leaveTypeName}}</th>
                    <td>
                      <a class="ui-cell" @click="jumpAttendanceAffairsDetail(item, 'leave', 'after')">
                        <div class="ui-cell__bd">
                          <div class="ui-cell_con">
                            <span class="smallText">{{item.beginTime | formatDay('YYYY-MM-DD HH:mm')}} - {{item.endTime | formatDay('YYYY-MM-DD HH:mm')}}</span>
                          </div>
                          <div class="ui-cell__ft"></div>
                        </div>
                      </a>
                    </td>
                  </tr>
                  <tr v-if="item.type === '8'" v-bind:key="index">
                    <th class="th">加班</th>
                    <td>
                      <a class="ui-cell" @click="jumpAttendanceAffairsDetail(item, 'overTime', 'after')">
                        <div class="ui-cell__bd">
                          <div class="ui-cell_con">
                            加班时长：{{item.duration}}h
                          </div>
                          <div class="ui-cell__ft"></div>
                        </div>
                      </a>
                    </td>
                  </tr>
                </template>
                </tbody>
              </table>
            </el-tab-pane>
            <el-tab-pane label="修改前" name="before">
              <div class="text-center" v-if="noNeedType === 1">
                本日无需考勤
              </div>
              <table class="table" v-else>
                <tbody>
                <tr>
                  <th class="th">上班</th>
                  <td>{{previousAttendanceUserHistory.beginState | formatState}}</td>
                </tr>
                <tr>
                  <th class="th">下班</th>
                  <td>{{previousAttendanceUserHistory.endState | formatState}}</td>
                </tr>
                <template v-for="(item, index) in previousAttendanceUserDetailHistorys">
                  <tr v-if="item.type === '7'" v-bind:key="index">
                    <th class="th">旷工</th>
                    <td>
                      {{item.beginTime | formatDay('HH:mm')}} - {{item.endTime | formatDay('HH:mm')}}
                    </td>
                  </tr>
                  <tr v-if="item.type === '5'" v-bind:key="index">
                    <th class="th">请假 - {{item.leaveTypeName}}</th>
                    <td>
                      <a class="ui-cell" @click="jumpAttendanceAffairsDetail(item, 'leave', 'before')">
                        <div class="ui-cell__bd">
                          <div class="ui-cell_con">
                            <span class="smallText">{{item.beginTime | formatDay('YYYY-MM-DD HH:mm')}} - {{item.endTime | formatDay('YYYY-MM-DD HH:mm')}}</span>
                          </div>
                          <div class="ui-cell__ft"></div>
                        </div>
                      </a>
                    </td>
                  </tr>
                  <tr v-if="item.type === '8'" v-bind:key="index">
                    <th class="th">加班</th>
                    <td>
                      <a class="ui-cell" @click="jumpAttendanceAffairsDetail(item, 'overTime', 'before')">
                        <div class="ui-cell__bd">
                          <div class="ui-cell_con">
                            加班时长：{{item.duration}}h
                          </div>
                          <div class="ui-cell__ft"></div>
                        </div>
                      </a>
                    </td>
                  </tr>
                </template>
                </tbody>
              </table>
            </el-tab-pane>
          </el-tabs>
          <div class="handle_button ttl1" v-if="params.mark === '2'">
            <button class="ui-btn ui-btn_error" @click="approve(3)" style="margin-right: 24px">驳回</button>
            <button class="ui-btn ui-btn_info" @click="approve(2)">批准</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .attendanceCon{
    .placeContainer{
      padding: 0.5rem 1rem;  background-color: #ffffff;
    }
    .ttl1{ padding-top: 10px;  font-size: 1.2em; }
    .ttl2{  padding:5px 0; }
    .ttl3{  display: flex;
      div{ flex:1; border-bottom: 2px solid #ddd; text-align:center; line-height:40px; height:40px;     }
    }
    .right{ text-align:right;  }
    .clr2{clear: both;  }
    .center{text-align: center;  }
    .con .el-row {  border-bottom: 1px solid #ccc;
      .el-col {padding:10px 2.5rem;}
      .el-col:last-child{ padding-left: 30px; border-left: 1px solid #ccc;}
    }
    .table{
      .ui-cell{
        padding: 0;
        background: inherit;
      }
    }
    .th{
      width: 80px;
      text-align: left;
      padding: 0 .8em;
    }
    .rightSide{
      text-align:right;
      padding-left: 0;
      font-size: 1.2em;
      color: #909399;
      .process_name{
        display: inline-block;
        width: 52px;
        margin-left: 4px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
        text-align: left;
      }
      .process_desName{
        display: inline-block;
        width: 55px;
        vertical-align: top;
      }
    }
    .smallText{
      font-size: 1.0rem;
    }
    .processRecord{
      cursor: pointer;
      &:hover{
        color: #3676bd;
      }
      i{
        font-weight: bold;
      }
      font-weight: bold;
    }
    .processContent{
      padding: 0.8rem 1.2rem;
      font-size: 1rem;
      background: #fafafa;
    }
  }
</style>
<script>
import { gohistory } from '../../../js/common'
export default {
  name: 'myAttendanceUpdateDetail',
  data () {
    return {
      attendanceDetail: {
        oldValue: [{}],
        newValue: [{}]
      },
      approvalProcess: [],
      attendanceUserHistory: {},
      attendanceUsesrDetailHistorys: {},
      previousAttendanceUserHistory: {},
      previousAttendanceUserDetailHistorys: {},
      toggle: -1,
      title: '职工考勤修改',
      loading: true,
      isBackHome: false,
      listeners: [],
      noNeedType: '',
      activeName: 'after'
    }
  },
  created: function () {
    this.params = this.$route.params
    let _this = this
    switch (Number(this.params.type)) {
      case 1:
        this.title = '我的考勤修改'
        break
      case 2:
        this.title = '考勤员提交的考勤修改'
        break
      case 3:
        this.title = '个人提交的考勤修改'
        break
      default:
    }
    _this.applyDetail()
  },
  filters: {
    formatState: function (value) {
      var str = ''
      switch (value) {
        case '1':
          str = '正常'
          break
        case '2':
          str = '迟到'
          break
        case '3':
          str = '早退'
          break
        case '4':
          str = '外出'
          break
        case '5':
          str = '请假'
          break
        case '6':
          str = '出差'
          break
        case '7':
          str = '旷工'
          break
        case '8':
          str = '加班'
          break
        default:
          str = '其他'
      }
      return str
    }
  },
  methods: {
    applyDetail: function () {
      let that = this
      this.$http.post('../../../workAttendance/updateAttendanceDetail.do', { aUHistoryId: this.params.id }, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data
        that.attendanceUserHistory = reqData.attendanceUserHistory // 修改后的职工考勤
        that.previousAttendanceUserHistory = reqData.previousAttendanceUserHistory // 修改前的职工考勤
        that.attendanceUsesrDetailHistorys = reqData.attendanceUserDetailHistorys   // 修改后的职工考勤明细
        that.previousAttendanceUserDetailHistorys = reqData.previousAttendanceUserDetailHistorys   // 修改前的职工考勤明细
        that.approvalProcess = reqData.approvalProcess || []   // 审批流程
        that.noNeedType = reqData.noNeedType // 1-无需考勤的修改前无数据的  0-包括无需考勤修改前有数据和正常考勤的有数据

        console.log(reqData)
        console.log('修改后', that.attendanceUsesrDetailHistorys)
        that.loading = false
      }).catch(function (res) {
        console.log('系统错误', res)
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    recordToggle: function () {
      let _this = this
      _this.toggle = -_this.toggle
    },
    jumpAttendanceAffairsDetail: function (item, name, changeType) {
      let that = this

      let id = item.source === '1' ? item.business : item.id
      item.userName = item.createName
      let params = {
        id: id,
        source: Number(item.source),
        title: that.title,
        name: name,
        changeType: changeType,
        info: item
      }
      this.$store.dispatch('setAttendanceLeaveParam', params) ;
      this.$router.push({
        path: '/myAttendanceChangeAffairsDetail'
      })
    },
    approve: function (approveStatus) {
      // 文管审批
      let paramsData = {
        approvalProcessId: this.approvalProcess.id ,
        userId: this.sphdSocket.user.userID ,
        approveStatus: approveStatus
      }
      let that = this
      if (approveStatus === 2) {
        this.$confirm('确定批准此条申请吗？', '!提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.loading = true
          that.handleChange(paramsData)
        })
      } else {
        this.$prompt('请输入驳回理由', '!提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '最多可录入50字'
        }).then(({ value }) => {
          that.loading = true
          paramsData.approveMemo = value || ''
          if (paramsData.approveMemo.length <= 50) {
            that.handleChange(paramsData)
          } else {
            that.loading = false
            that.$kiko_message('最多可录入50字')
          }
        })
      }
    },
    handleChange: function (fileData) {
      let that = this
      let url = '../../../workAttendance/handleApprovalInstance.do'
      if (Number(this.params.type) === 3) {
        url = '../../../workAttendance/approvalAttendance.do'
      }
      console.log(fileData)
      this.$http.post(url, fileData, {
        emulateJSON: true
      }).then((response) => {
        that.loading = false
        let data = response.body
        console.log('审批返回值', data)
        let status = data.data.status
        that.$kiko_message(data.data.content)
        if (status === 1) {
          gohistory(that)
        }
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    }
  },
  destroyed: function () {
    let _this = this
    console.log('destroyed')
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  }
}
</script>
