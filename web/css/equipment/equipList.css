/*装备器具 - 装备清单 css*/
.mainCon{
    display: none;
    margin-left: 100px;
    padding-right: 50px;
}
.linkBtn{
    color: #0b94ea;
    display: inline-block;
    text-decoration: none;
    cursor: pointer;
}
.linkBtn:hover{
    text-decoration: underline;
}
.filter i{
    font-size: 25px;
    color: #0b94ea;
    position: absolute;
    right: 7px;
    top: 5px;
}
.filter{
    text-align: right;
    float: right;
    position: relative;
}
#filterCat{
    width: 200px;
    display: inline-block;
}
.allWidth{
    width: 100%;
    margin-top: 30px;
    margin-bottom: 30px;
}
.hr{
    border-bottom: 1px solid #ccc;
    margin:30px 0;
}
#eqScan td>div:nth-child(2), #eqLogScan td>div:nth-child(2){
    color: #0b94ea;
    padding-left:15px;
    height: 30px;
    background: #fff;
    line-height: 30px;
    border-radius: 4px;
}
.weiTip{
    display: none;
    padding: 20px 10px;
}
.tdRela i{
    position: absolute;
    left: -40px;
    font-size: 22px;
    color: #5d9cec;
}
#catPath{
    font-size: 16px;
    color: #5d9cec;
}
.tdRela{
    position: relative;
}
#selectTab{
    width: 95%;
    margin-left: 5%;
}

/*=============*/

.maintip{
    width: 500px;
    margin:100px 0 0 100px;
}
.maintip>p:nth-child(3){
    margin-top: 40px;
}
.maintip>p:nth-child(3)>span:nth-child(2){
    margin-left: 100px;
}
.eqContent{
    width: 600px;
}
.eqContent table{
    width: 100%;
}
.eqContent td{
    padding:6px 10px;
    position: relative;
}

.litInput{
    padding-right: 28px!important;
}
.litUnit{
    position: absolute;
    right: 10px;
    bottom: 5px;
}
.textarea{
    width: 100% !important;
}
.limtRole{
    float: right;
}

.exportStep {
    padding-left: 40px;
}
.stepItem {
    margin-bottom: 20px;
}
.flexRow {
    margin-left: 50px;
    display: flex;
    justify-content: space-between;
    width: 356px;
}
#uploadAdd{
    width: 550px;
}

#catContainer{
    max-height: 300px;
    overflow: auto;
    border: 1px solid #eee;
}
#catContainer li{
    line-height: 25px;
}
#catContainer .catName{
    cursor: pointer;
    padding:0 10px;
}
#catContainer .catName:hover{
    background: #b7d4e7;
}
#catContainer .catName.selectedCat{
    background:#0b94ea ;
    color:#fff;
}
#catContainer ul ul{
    margin-left: 30px;
    width: 300px;
    border-left: 1px solid #0b94ea;
}
.pa40{
    padding:40px;
}
.catItem{
    padding:5px 0 5px 20px;
    position: relative;
}
.catItem:nth-child(odd){
    background: #eee;
}
.catItem>.fa{
    position: absolute;
    right: 16px;
}
#batchClassCon .fa{
    color: #0b94ea;
    font-size: 18px;
    width: 18px;
    font-weight: bold;
}
.catItemName .fa{
    position: relative;
    top: 2px;
}
#batchClassCon .catItemName{
    cursor: pointer;
}
.gapLt{
    margin-left: 50px;
}
.gapR{
    margin-right: 50px;
}
.gapB{
    margin-bottom: 50px;
}
.ty-table tbody td.grayBg{
    width: 100px;
    font-size: 24px;
    background: #dfdddd;
}
.ty-table tbody td.grayBg .fa{
    vertical-align: top;
    line-height: 28px;
}
.setTip{
    margin-right: 50px;
    line-height: 32px;
}
.catBody{
    margin: auto;
    width: 460px;
}
#uniformCatName{
    margin-top: 30px;
    width: 380px;
    height: 34px;
}
.downBtn{
    display: inline-block;
    width: 70px;
    background: #d8d8d8;
    text-align: center;
    height: 34px;
    line-height: 31px;
    font-size: 20px;
    margin-left: -5px;
}
#selectCatPath{
    margin-top: 20px;
}
.eqDetails >table{
    margin-bottom: 30px;
}
.eqDetails >div span:not(:last-child){
    margin-right: 30px;
}
.bonceCon .infoEdit input{
    max-width: 120px;
}
.moreBd{
    border-bottom: 1px solid #ddd;
    margin: 40px 0;
}
.singleSect span {
    margin-bottom: 10px;
    padding: 10px 2px 6px 2px;
    width: 120px;
    display: inline-block;
    border-bottom: 3px solid #ccc;
}
.equipEdit{
    padding-top: 20px;
}
.equipEdit hr{
    border-bottom: 1px solid #ccc;
}
.gapTp{
    margin-top: 50px;
}
.limitPos {
    position: relative;
}
.subLogScan{
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #ccc;
}
.cateCon{
    max-width: 300px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.eqDetails .memo{
    max-width: 260px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}























