<template>
  <div class="questionBank">
    <el-dialog
        v-model="dialog_visible_editQuestionBank"
        title="新增题库"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        <el-form
            label-position="top"
            ref="editQuestionBankForm"
            class="editQuestionBankForm"
            :model="form_editQuestionBank"
        >
          <el-form-item label="题库名称（请选取与题库主题有关的名字）" prop="name">
            <el-input v-model="form_editQuestionBank.name" placeholder="请录入" clearable maxlength="200" show-word-limit/>
          </el-form-item>
          <el-form-item label="题库代号（此代号由系统生成，不可修改）" prop="name">
            <el-input v-model="form_editQuestionBank.code" readonly disabled/>
          </el-form-item>
          <el-form-item label="将与题库有关的附件增加于此，管理更便捷！如有多个附件，请逐一增加。 " prop="name">
            <el-upload
                class="upload_contract-img"
                v-model:file-list="form_editQuestionBank.fileList"
                :headers="imgUpload.uploadHeaders"
                :action="imgUpload.uploadAction"
                :accept="imgUpload.uploadLyc"
                :data="imgUpload.postData"
                ref="upload"
                :limit="1"
            >
              <template #trigger>
                <el-button type="primary" size="small">增加附件</el-button>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editQuestionBank = false">取消</el-button>
          <el-button type="primary" @click="editQuestionBank_submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_editMt"
        title="新增素材"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <el-text>
          素材的内容需与试题相关。系统支持使用素材生成某些类型的试题。
        </el-text>
        <el-row>
          某素材下可有多道不同类型的试题，但某次考核只会选取某素材下的一道题。
        </el-row>
        <el-form
            label-position="top"
            ref="editMtForm"
            class="editMtForm"
            :model="form_editMt"
            style="margin-top: 16px"
        >
          <el-form-item label="请将素材录入或复制到下面的空格里。" prop="sourceTxt">
            <el-input
                v-model="form_editMt.content"
                maxlength="80"
                placeholder="请录入"
                show-word-limit
                type="textarea"
                :rows="5"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editMt = false">取消</el-button>
          <el-button type="primary" @click="editMt_submit">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_editTQ"
        title="新增试题"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <el-form
            label-position="left"
            ref="editMtForm"
            class="editMtForm"
            :model="form_editTQ"
            style="margin-top: 16px"
            label-width="220"
        >
          <div v-show="form_editTQ.origin === 'seeMt'">
            <div class="row">
              素材 {{form_editTQ.material.code}}
            </div>
            <div class="row" style="margin-top: 16px">
              {{form_editTQ.material.content}}
            </div>
            <div class="ty-hr"></div>
          </div>
          <div v-show="form_editTQ.origin === 'free'">
            <el-form-item label="要增加的试题是否基于某素材？" prop="sourceTxt">
              <el-radio-group v-model="form_editTQ.isBaseMaterial" @change="form_editTQ.material={}; form_editTQ.isCorrectBaseMaterial = ''">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="素材">
              <el-input
                  placeholder="尚未选择"
                  readonly
                  v-model="form_editTQ.material.code"
              >
                <template #append>
                  <el-button @click="chooseMaterial()" :disabled="form_editTQ.isBaseMaterial === 2">去选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </div>
          <el-form-item label="要增加哪种试题？" prop="sourceTxt" @change=" form_editTQ.isCorrectBaseMaterial = ''">
            <el-radio-group v-model="form_editTQ.TQType">
              <el-radio :value="1">单选题</el-radio>
              <el-radio :value="2">多选题</el-radio>
              <el-radio :value="3">判断题</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label-width="380" v-show="form_editTQ.TQType && form_editTQ.isBaseMaterial === 1">
            <template #label>
              <span v-show="form_editTQ.TQType === 3">标准答案为“正确”的试题是否直接使用素材原文？</span>
              <span v-show="form_editTQ.TQType !== 3">是否采用正确项直接取材于素材原文的WONDERSS模式？</span>
            </template>
            <el-radio-group v-model="form_editTQ.isCorrectBaseMaterial">
              <el-radio :value="1">是</el-radio>
              <el-radio :value="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-dialog
            v-model="dialog_visible_chooseMaterial"
            title="选择素材"
            width="800"
            :close-on-click-modal="false"
            append-to-body
            class="setShelfNo_edit"
            :draggable="true"

        >
          <div class="mainCon" style="width: 700px; margin: 0 auto">
            <el-table
                ref="multipleTable"
                :data="form_editTQ.form_chooseMaterial.canChooseMaterials"
                border
                @row-click="handleSelectionChange"
                highlight-current-row
            >
              <el-table-column
                  label=""
                  width="55"
                  align="center"
              >
                <template #default="scope">
                  <el-radio
                      v-model="form_editTQ.form_chooseMaterial.choseMaterial.id"
                      :value="scope.row.id"
                  >
                  </el-radio>
                </template>
              </el-table-column>
              <el-table-column label="素材代号" prop="code" :sortable="true"></el-table-column>
              <el-table-column
                  label="素材内容"
                  prop="content"
                  width="250"
                  show-overflow-tooltip
              ></el-table-column>
              <el-table-column label="创建" :sortable="true">
                <template #default="scope">
                  {{scope.row.createName}} {{scope.row.createDate}}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="dialog_visible_chooseMaterial = false">取消</el-button>
              <el-button type="primary" @click="chooseMaterial_submit">确定</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editTQ = false">取消</el-button>
          <el-button type="primary" @click="editTQ_submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
<!--    单选题：SCQ(Single Choice Question); -->
<!--    多选题：MCQ(Multiple Choice Questions);-->
<!--    判断题：TFQ(True or False Question)-->
    <el-dialog
        v-model="dialog_visible_editSCQ"
        title="制作单选题"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <el-form
            label-position="top"
            ref="editMtForm"
            class="editMtForm"
            :model="form_editSCQ"
            style="margin-top: 16px"
            label-width="220"
        >
          系统生成的考核试卷中，单选题仅有一种模式，即1个正确项、3个干扰项的四选一模式。
          <div class="ty-hr"></div>
          <div v-if="form_editTQ.isBaseMaterial === 1">

            <el-form-item prop="sourceTxt">
              <template #label>
                素材sc202400001-0003的原文
              </template>
              <el-input
                  v-model="form_editSCQ.material.content"
                  type="textarea"
                  :rows="2"
                  readonly
                  @select="getSelectedText"
              />
            </el-form-item>
            <div class="ty-hr"></div>
          </div>
          <div class="auto" v-if="form_editTQ.isCorrectBaseMaterial === 1">
            <el-form-item prop="right">
              <template #label>
                <div>正确项</div>
                <el-text type="primary" size="small">注 操作方法为在素材原文中点击两次，先点击之处为正确项的起点，后点击之处为正确项的终点。</el-text>
              </template>
              <el-input
                  v-model="form_editSCQ.right"
                  readonly
              />
            </el-form-item>
            <el-form-item prop="title">
              <template #label>
                <div>题干</div>
                <el-text type="primary" size="small">注 无需操作，题干将与正确项同时生成。</el-text>
              </template>
              <el-input
                  v-model="form_editSCQ.title"
                  readonly
              />
            </el-form-item>
          </div>
          <div class="hand" v-if="form_editTQ.isCorrectBaseMaterial !== 1">
            <el-form-item prop="title">
              <template #label>
                <div>题干</div>
                <el-text type="primary" size="small">注 题干需手动录入。</el-text>
              </template>
              <el-input
                  v-model="form_editSCQ.title"
              />
            </el-form-item>
            <el-form-item prop="right">
              <template #label>
                <div>正确项</div>
                <el-text type="primary" size="small">注 正确项需手动录入。</el-text>
              </template>
              <el-input
                  v-model="form_editSCQ.title"
              />
            </el-form-item>
          </div>
          <div class="row">
            干扰项
            <div class="rightBtn">
              <el-button type="primary" link @click="addDistracterInput('SCQ')">增加一个干扰项录入框</el-button>
            </div>
          </div>
          <div class="row" style="margin-bottom: 16px">
            <el-text type="primary" size="small">注 干扰项需手动需录入，不可少于3个，不可多于7个。</el-text>
          </div>
          <el-form-item prop="right" v-for="(distractor, index) in form_editSCQ.distractors" :key="index">
            <template #label>
              干扰项 {{Number(index) + 1}}
            </template>
            <el-input
                v-model="distractor.content"
                maxlength="80"
                placeholder="请录入"
                show-word-limit
            >
              <template #append v-if="index > 2">
                <el-button @click="deleteDistracter_noInterface('SCQ', index)">删除</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editSCQ = false">取消</el-button>
          <el-button type="primary" @click="editSCQ_submit">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_editMCQ"
        title="制作多选题"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <el-form
            label-position="top"
            ref="editMtForm"
            class="editMtForm"
            :model="form_editMCQ"
            style="margin-top: 16px"
            label-width="220"
        >
          <div v-if="form_editTQ.isBaseMaterial === 1">
            <el-form-item prop="sourceTxt">
              <template #label>
                素材sc202400001-0003的原文
              </template>
              <el-input
                  v-model="form_editMCQ.material.content"
                  type="textarea"
                  :rows="2"
                  readonly
              />
            </el-form-item>
            <div class="ty-hr"></div>
          </div>
          <el-form-item prop="right">
            <template #label>
              <div>题干</div>
              <el-text type="primary" size="small">注 题干需手动录入。</el-text >
            </template>
            <el-input
                v-model="form_editMCQ.title"
                maxlength="200"
                placeholder="请录入"
                show-word-limit
                type="textarea"
            />
          </el-form-item>
          <div class="row" style="margin-bottom: 16px">
            <div>
              <div>正确项</div>
              <div><el-text type="primary" size="small">注1 各正确项需手动录入。</el-text></div>
              <div><el-text type="primary" size="small">注2 系统要求正确项不得少于2个，以及干扰项与正确项合计不可少于4个，不可多于10个。</el-text></div>
            </div>
            <div class="rightBtn">
              <el-button type="primary" link>增加一个正确项录入框</el-button>
            </div>
          </div>
          <el-form-item prop="right">
            <template #label>
              <div>正确项1</div>
            </template>
            <el-input
                v-model="form_editMCQ.right"
                maxlength="80"
                placeholder="请录入"
                show-word-limit
            />
          </el-form-item>
          <el-form-item prop="right">
            <template #label>
              <div>正确项2</div>
            </template>
            <el-input
                v-model="form_editMCQ.right2"
                maxlength="80"
                placeholder="请录入"
                show-word-limit
            />
          </el-form-item>
          <div class="ty-hr"></div>
          <div class="row">
            干扰项
            <div class="rightBtn">
              <el-button type="primary" link>增加一个干扰项录入框</el-button>
            </div>
          </div>
          <div class="row" style="margin-bottom: 16px">
            <el-text type="primary" size="small">注 干扰项需手动需录入，可录入多个，以及干扰项与正确项合计不可少于4个，不可多于10个。</el-text>
          </div>
          <el-form-item prop="right" v-for="(distractor, index) in form_editSCQ.distractors" :key="index">
            <template #label>
              干扰项 {{Number(index) + 1}}
            </template>
            <el-input
                v-model="distractor.content"
                maxlength="80"
                placeholder="请录入"
                show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editMCQ = false">取消</el-button>
          <el-button type="primary" @click="editMCQ_submit">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_editTFQ"
        title="制作判断题"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <el-form
            label-position="top"
            ref="editTFQForm"
            class="editTFQForm"
            :model="form_editTFQ"
            style="margin-top: 16px"
            label-width="220"
        >
          <div v-if="form_editTQ.isBaseMaterial === 1">
            <el-form-item prop="sourceTxt">
              <template #label>
                素材{{form_editTFQ.material.code}}的原文
              </template>
              <el-input
                  v-model="form_editTFQ.material.content"
                  type="textarea"
                  :rows="2"
                  readonly
              />
            </el-form-item>
            <div class="ty-hr"></div>
          </div>
          <div v-show="form_editTQ.isCorrectBaseMaterial === 1">
            <div>标准答案为“正确”的判断题</div>
            <el-text type="primary" size="small">注 无需操作，此题由系统生成，内容就是素材原文。</el-text>
            <div class="ty-hr"></div>
            <div>标准答案为“错误”的备选题</div>
            <el-text type="primary" size="small">注1 录入框中带有素材原文，以便改动。点击“X”可清空全部。</el-text>
            <br>
            <el-text type="primary" size="small">注2 录入的内容只要与素材原文有不同之处，就能生成有效的试题。</el-text>
            <el-form-item prop="`options[${index}].content`" v-for="(distract, index) in form_editTFQ.options" :key="index" style="margin-top:16px">
              <template #label>
                备选题 {{Number(index) + 1}}
              </template>
              <el-input
                  v-model="form_editTFQ.options[index].content"
                  maxlength="80"
                  placeholder="请录入内容"
                  show-word-limit
                  clearable
              />
            </el-form-item>
          </div>
          <div v-show="form_editTQ.isCorrectBaseMaterial === 2">
            <div class="row">
              请根据需要录入判断题的内容，之后确定这道题的标准答案。
              <div class="rightBtn">
                <el-button type="primary" link>增加一道</el-button>
              </div>
            </div>
            <div v-for="(distract, index) in form_editTFQ.options" :key="index" style="margin-top: 18px">
              <el-form-item prop="right" style="margin-bottom: 0">
                <template #label>
                  判断题 {{Number(index) + 1}}
                </template>
                <el-input
                    v-model="distract.content"
                    maxlength="80"
                    placeholder="请录入内容"
                    show-word-limit
                />
              </el-form-item>
              <div class="row">
                标准答案：
                <el-radio-group v-model="distract.isKey">
                  <el-radio :value="1">是</el-radio>
                  <el-radio :value="2">否</el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editTFQ = false">取消</el-button>
          <el-button type="primary" @click="editTFQ_submit">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeMt"
        title="素材查看"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <div class="row">
          素材 {{table_seeMt.mtInfo.code}}
        </div>
        <div class="row" style="margin-top: 16px">
          {{table_seeMt.mtInfo.content}}
        </div>
        <div class="ty-hr"></div>
        <div class="row">
          <div class="rightBtn">
            <el-button type="primary" link @click="editTQ('seeMt')"><b>新增试题</b></el-button>
            <el-button type="primary" link @click="seeStopTQ"><b>查看已停用的试题</b></el-button>
          </div>
        </div>
        <div class="row">
          本素材的在用试题中，单选题{{table_seeMt.mtInfo.choiceNum}}道，多选题{{table_seeMt.mtInfo.tfNum}}，判断题{{table_seeMt.mtInfo.tfNum}}道，具体如下：
        </div>
        <el-table :data="table_seeMt.tqList" border style="width: 100%; margin-top: 8px">
          <el-table-column label="题型">
            <template #default="scope">
              {{filter_QTType(scope.row.type)}}
            </template>
          </el-table-column>
          <el-table-column label="创建">
            <template #default="scope">
              {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link @click="seeQT(scope.row)"><b>查看</b></el-button>
              <el-button type="primary" link><b>停用</b></el-button>
              <el-button type="danger" link><b>删除</b></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeMt = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeStopTQByMt"
        title="已停用的试题"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <div class="row">
          素材 {{table_seeMt.mtInfo.code}}
        </div>
        <div class="row" style="margin-top: 16px">
          {{table_seeMt.mtInfo.content}}
        </div>
        <div class="ty-hr"></div>
        <el-table :data="table_seeMt.stopTQList" border style="width: 100%; margin-top: 8px">
          <el-table-column label="题型" prop="type" :sortable="true"></el-table-column>
          <el-table-column label="创建">
            <template #default="scope">
              {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link><b>查看</b></el-button>
              <el-button type="primary" link><b>启用</b></el-button>
              <el-button type="danger" link><b>删除</b></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeStopTQByMt = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeSCQ"
        title="单选题查看"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <div class="row">
          如需要，可在本页面进行相关操作。
        </div>
        <el-text type="primary" size="small">注 操作后须保持干扰项不少于3个，不多于7个！</el-text>
        <div class="row">
          <div class="rightBtn">
            <el-button type="primary" link @click="editDistractors"><b>新增干扰项</b></el-button>
            <el-button type="primary" link @click="seeStopOption"><b>查看已停用的选项</b></el-button>
          </div>
        </div>
        <div class="ty-hr"></div>
        <div class="row">
          题目  {{table_seeMt.table_seeTQ.question.code}}
        </div>
        <div class="row" style="margin-top: 16px">
          {{table_seeMt.table_seeTQ.question.content}}
        </div>
        <el-table :data="table_seeMt.table_seeTQ.options" border style="width: 100%; margin-top: 8px">
          <el-table-column label="选项类别" prop="type">
            <template #default="scope">
              {{scope.row.isKey === 0?'干扰项':'正确项'}}
            </template>
          </el-table-column>
          <el-table-column label="内容" prop="content"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <div v-if="scope.row.isKey === 0">
                <el-button type="primary" link @click="stateDistracter(scope.row.id, 0)"><b>停用</b></el-button>
                <el-button type="danger" link @click="deleteDistracter(scope.row.id)"><b>删除</b></el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeSCQ = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeMCQ"
        title="多选题查看"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <el-form
            label-position="top"
            ref="editMtForm"
            class="editMtForm"
            :model="form_editMCQ"
            style="margin-top: 16px"
            label-width="220"
        >
          <el-form-item prop="sourceTxt">
            <template #label>
              素材sc202400001-0003的原文
            </template>
            <el-input
                v-model="form_editMCQ.material.content"
                type="textarea"
                :rows="2"
                readonly
            />
          </el-form-item>
          <div class="row">
            <div>
              <div>正确项</div>
              <div><el-text type="primary" size="small">注1 操作方法为在素材原文中点击两次，先点击之处为正确项的起点，后点击之处为正确项的终点。</el-text></div>
              <div><el-text type="primary" size="small">注2 系统要求正确项不得少于2个，以及干扰项与正确项合计不可少于4个，不可多于10个。</el-text></div>
            </div>
            <div class="rightBtn">
              <el-button type="primary" link>增加一个正确项录入框</el-button>
            </div>
          </div>
          <el-form-item prop="right">
            <template #label>
              <div>正确项1</div>
            </template>
            <el-input
                v-model="form_editSCQ.right"
                placeholder="请按注1的操作方法操作！"
                readonly
            />
          </el-form-item>
          <el-form-item prop="right">
            <template #label>
              <div>正确项2</div>
            </template>
            <el-input
                v-model="form_editSCQ.right2"
                placeholder="请按注1的操作方法操作！"
                readonly
            />
          </el-form-item>
          <div class="ty-hr"></div>
          <el-form-item prop="right">
            <template #label>
              <div>题干</div>
              <el-text type="primary" size="small">注 无需操作，题干将与正确项同时生成。</el-text>
            </template>
            <el-input
                v-model="form_editSCQ.title"
                readonly
            />
          </el-form-item>
          <div class="row">
            干扰项
            <div class="rightBtn">
              <el-button type="primary" link>增加一个干扰项录入框</el-button>
            </div>
          </div>
          <div class="row" style="margin-bottom: 16px">
            <el-text type="primary" size="small">注 干扰项需手动需录入，数量不限，但干扰项与正确项合计不可少于4个，不可多于10个。</el-text>
          </div>
          <el-form-item prop="right" v-for="(distractor, index) in form_editSCQ.distractors" :key="index">
            <template #label>
              干扰项 {{Number(index) + 1}}
            </template>
            <el-input
                v-model="distractor.content"
                maxlength="80"
                placeholder="请录入"
                show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeMCQ = false">取消</el-button>
          <el-button type="primary" @click="editTQ_submit">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeTFQ"
        title="判断题查看"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <div class="row">
          题目  {{table_seeMt.table_seeTQ.question.code}}
        </div>
        <div class="row" style="margin-top: 16px">
          {{table_seeMt.table_seeTQ.question.content}}
        </div>
        <div class="ty-hr"></div>
        <div class="row" style="margin-top: 16px">
          标准答案为“{{table_seeMt.table_seeTFQ.question.content}}”的。
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeTFQ = false">取消</el-button>
          <el-button type="primary" @click="editTQ_submit">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_editDistracter"
        title="新增干扰项"
        width="800"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 700px; margin: 0 auto">
        <div class="row">
          <b>题目</b> {{table_seeMt.mtInfo.code}}
        </div>
        <div class="row" style="margin-top: 16px">
          {{table_seeMt.mtInfo.content}}
        </div>
        <div class="ty-hr"></div>
        <div class="row">
          干扰项
        </div>
        <el-input
            v-model="form_editTFQ.material.content"
            type="textarea"
            placeholder="请录入干扰项内容，字数上限为80个。"
            maxlength="80"
            show-word-limit
            :rows="2"
            style="margin-top: 16px"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editDistracter = false">关闭本页</el-button>
          <el-button type="primary" @click="editDistracter_submit">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <div class="ty-container" id="home">
      <div class="page">
        <section v-show="page === 'main'">
          <div class="row head" style="display: flex; align-items: center">
            <el-button type="success" @click="editQuestionBank()">新增题库</el-button>

            <div class="rightBtn">
              <el-text>查找</el-text>
              <el-input
                  style="width: 300px; margin-left: 4px"
                  placeholder="请输入题库代号或名称中的关键字"
              >
                <template #prefix>
                  <el-icon class="el-input__icon"><search /></el-icon>
                </template>
                <template #append>
                  <el-button type="primary">确定</el-button>
                </template>
              </el-input>
              <el-button type="default" style="margin-left: 16px" @click="step_main_jumpStopTQ">已停用的题库</el-button>
            </div>
          </div>
          <el-table
              :data="questionBank.list"
              border
              @sort-change="questionBank_sort"
              style="width: 100%; margin-top: 16px">
            <el-table-column label="题库代号" prop="code" sortable="custom" ></el-table-column>
            <el-table-column label="题库名称" prop="name" ></el-table-column>
            <el-table-column label="可生成试题">
              <template #default="scope">
                {{scope.row.materialNum}}
              </template>
            </el-table-column>
            <el-table-column label="可生成的试题中">
              <el-table-column label="在用素材的数量">
                <template #default="scope">
                  <el-button type="primary" link @click="step_main_jumpUsingMt(scope.row)">{{scope.row.materialNum}} 个</el-button>

                </template>
              </el-table-column>
              <el-table-column label="没有素材的试题">
                <template #default="scope">
                  <el-button type="primary" link @click="step_main_jumpNoMt(scope.row)">{{scope.row.materialNum}} 道</el-button>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="创建" prop="createDate" sortable="custom">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format($filter.format(scope.row.createDate))}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="seeQBank"><b>进入题库</b></el-button>
                <el-button type="primary" link @click="stateQBank(scope.row.id, 0)"><b>停用</b></el-button>
                <el-button type="danger" link @click="deleteQBank(scope.row.id)"><b>删除</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
                layout="prev, pager, next"
                :page-count="questionBank.pageInfo.totalPage"
                background
                hide-on-single-page
                @current-change="pagination_questionBank_change"
                style="justify-content: flex-end"
            />
          </div>
        </section>
        <section v-show="page === 'stopTQ'">
          <div class="row head">
            <el-button type="default" @click="page = 'main'">返回</el-button>
          </div>
          <div class="row" style="display: flex; align-items: center">
            以下是已停用的题库。
            <div class="rightBtn">
              <el-text>查找</el-text>
              <el-input
                  style="width: 300px; margin-left: 4px"
                  placeholder="请输入题库代号或名称中的关键字"
              >
                <template #prefix>
                  <el-icon class="el-input__icon"><search /></el-icon>
                </template>
                <template #append>
                  <el-button type="primary">确定</el-button>
                </template>
              </el-input>
            </div>
          </div>
          <el-table :data="stopQuestionBank.list" border style="width: 100%; margin-top: 8px">
            <el-table-column label="题目" prop="code" :sortable="true"></el-table-column>
            <el-table-column label="题型" prop="name" ></el-table-column>
            <el-table-column label="创建" prop="createDate" sortable="custom">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format($filter.format(scope.row.createDate))}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="seeMtByQuestionBank(scope.row)"><b>查看</b></el-button>
                <el-button type="primary" link @click="stateQBank(scope.row.id, 1)"><b>启用</b></el-button>
                <el-button type="danger" link @click="deleteQBank(scope.row.id)"><b>删除</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination layout="prev, pager, next" :total="stopQuestionBank.pageInfo.totalPage" background hide-on-single-page/>
          </div>
        </section>
        <section v-show="page === 'usingMt'">
          <div class="row head">
            <el-button type="default" @click="page = 'main'">返回</el-button>
            <div class="rightBtn">
              <el-button type="primary" @click="editTQ('free')">新增试题</el-button>
              <el-button type="primary" @click="editMt">新增素材</el-button>
              <el-button type="primary" @click="stoppedMt">已停用的素材</el-button>
              <el-button disabled>操作指南</el-button>
            </div>
          </div>
          <h3>{{form_editUsingMt.questionBank.code}} / {{form_editUsingMt.questionBank.name}}</h3>
          <div class="row" style="display: flex; align-items: center">
            本题库中，在用素材共以下{{form_editUsingMt.list.length}}个
            <div class="rightBtn">
              <el-text>查找</el-text>
              <el-input
                  style="width: 320px; margin-left: 4px"
                  placeholder="请输入素材代号或内容中的关键字"
              >
                <template #prefix>
                  <el-icon class="el-input__icon"><search /></el-icon>
                </template>
                <template #append>
                  <el-button type="primary">确定</el-button>
                </template>
              </el-input>
            </div>
          </div>
          <el-table :data="form_editUsingMt.list" border style="width: 100%; margin-top: 8px">
            <el-table-column label="素材代号" prop="code" :sortable="true"></el-table-column>
            <el-table-column
                label="素材内容"
                prop="content"
                width="400"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="创建" :sortable="true" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="在用试题数量" :sortable="true" width="300">
              <template #default="scope">
                单选题{{scope.row.choiceNum}}道，判断题{{scope.row.tfNum}}道
              </template>
            </el-table-column>
            <el-table-column label="已停用的试题">
              <template #default="scope">
                {{scope.row.disabledNum}}道
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="seeMtByQuestionBank(scope.row)"><b>查看</b></el-button>
                <el-button type="primary" link @click="stateMt(scope.row.id, 0)"><b>停用</b></el-button>
                <el-button type="danger" link @click="deleteMt(scope.row.id)"><b>删除</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination layout="prev, pager, next" :total="questionBank.pageInfo.totalPage" background hide-on-single-page/>
          </div>
        </section>
        <section v-show="page === 'noMt'">
          <div class="row head">
            <el-button type="default" @click="page = 'main'">返回</el-button>
            <div class="rightBtn">
              <el-button type="primary">新增试题</el-button>
              <el-button type="primary">已停用的素材</el-button>
              <el-button>操作指南</el-button>
            </div>
          </div>
          <h3>{{form_editNoMt.questionBank.code}} / {{form_editNoMt.questionBank.name}}</h3>
          <div class="row" style="display: flex; align-items: center">
            本题库中，没有素材的试题共以下{{form_editNoMt.list.length}}道
            <div class="rightBtn">
              <el-text>查找</el-text>
              <el-input
                  style="width: 300px; margin-left: 4px"
                  placeholder="请输入试题题目中的关键字"
              >
                <template #prefix>
                  <el-icon class="el-input__icon"><search /></el-icon>
                </template>
                <template #append>
                  <el-button type="primary">确定</el-button>
                </template>
              </el-input>
            </div>
          </div>
          <el-table :data="questionBank.list" border style="width: 100%; margin-top: 8px">
            <el-table-column label="题目" prop="code" :sortable="true"></el-table-column>
            <el-table-column label="题型" prop="name" ></el-table-column>
            <el-table-column label="创建" :sortable="true">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="seeMtByQuestionBank(scope.row)"><b>查看</b></el-button>
                <el-button type="primary" link><b>停用</b></el-button>
                <el-button type="danger" link><b>删除</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination layout="prev, pager, next" :total="questionBank.pageInfo.totalPage" background hide-on-single-page/>
          </div>
        </section>
        <section v-show="page === 'stoppedMt'">
          <div class="row head">
            <el-button type="default" @click="page = 'usingMt'">返回</el-button>
          </div>
          <div class="row" style="display: flex; align-items: center">
            以下是已停用的素材。
            <div class="rightBtn">
              <el-text>查找</el-text>
              <el-input
                  style="width: 300px; margin-left: 4px"
                  placeholder="请输入题库代号或名称中的关键字"
              >
                <template #prefix>
                  <el-icon class="el-input__icon"><search /></el-icon>
                </template>
                <template #append>
                  <el-button type="primary">确定</el-button>
                </template>
              </el-input>
            </div>
          </div>
          <el-table :data="form_stoppedMt.list" border style="width: 100%; margin-top: 8px">
            <el-table-column label="素材代号" prop="code" :sortable="true"></el-table-column>
            <el-table-column
                label="素材内容"
                prop="content"
                width="200"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="创建" :sortable="true">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="在用试题数量" :sortable="true">
              <template #default="scope">
                单选题{{scope.row.choiceNum}}道，判断题{{scope.row.tfNum}}道
              </template>
            </el-table-column>
            <el-table-column label="已停用的试题">
              <template #default="scope">
                {{scope.row.disabledNum}}道
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="seeMtByQuestionBank(scope.row)"><b>查看</b></el-button>
                <el-button type="primary" link @click="stateMt(scope.row.id, 1)"><b>启用</b></el-button>
                <el-button type="danger" link @click="deleteMt(scope.row.id)"><b>删除</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination layout="prev, pager, next" :total="questionBank.pageInfo.totalPage" background hide-on-single-page/>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.pagination{
  text-align: right;
  margin-top: 16px;
}
.ty-container{
  margin-top:35px; padding:10px;
}
.ty-page-header{
  display: flex;
  line-height: 24px;
  padding: 0 0 0 70px;
  color: #5d9cec;
  margin-top: 16px;
  .page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 16px;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      background-color: #dcdfe6;
    }
    .icon-back {
      font-size: 18px;
      margin-right: 6px;
      align-self: center;
      position: relative;
      top: 1px;
    }
  }
}
.page{
  padding: 8px 0;
  position: relative;
  margin: 0 70px;
  .panel-box{
    border-top: 1px solid #ddd;
    padding-top: 8px;
    &:first-child{
      border: none
    }
  }
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.effect_avatar{
  background: rgb(64, 158, 255);
  padding: 24px 64px;
  border-radius: 3px;
  .effect_title{
    color: #fff;
  }
  .effect_main_avatar{
    .effect_main{
      background: #fff;
    }
  }
}
.effect_main_avatar{
  margin-top: 16px;
  margin-bottom: 4px;
  .effect_main{
    display: flex;
    background: #f5f5f5;
    padding: 16px;
    text-align: center;
    border-radius: 5px;
    font-size: 24px;
    align-items: center;
    .effect_code{
      flex: auto;
    }
    .effect_shelfNum{
      flex: auto;
    }
    .effect_floorNum{
      flex: auto;
    }
    .effect_qrCode{
      width: 100px;
      flex: none;
      height: 48px;
    }
    .effect_des{
      color: #666;
    }
    h2{
      font-family: Helvetica;
    }
  }
}
.label_avatar{
  margin-top: 16px;
  .label{
    width: 139px;
    height: 36px;
    background: #efefef;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    border: 1px solid #d9d9d9;
    margin-left: -1px;
    margin-top: -1px;
    &.active{
      background: $ty-color-orange;
      color: #fff;
      border-color: #d97610;
    }
    .el-icon{
      vertical-align: middle;
    }
  }
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
:deep(el-form--label-top){
  .el-form-item__label{
    padding: 0;
  }
}
.text-center{
  text-align: center;
}
.des{
  color: #333;
}
.setShelfNo_edit{
  p{
    margin-bottom: 8px;
  }
}
div.question{
  margin-top: 8px;
}
.valign-middle{
  vertical-align: middle;
}
.see_shelfNo_box{
  padding: 8px 4px;
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import auth from '@/sys/auth'
import * as api from "@/api/train.js"
import ElInputPlus from "@/components/shelfWh/ElInputPlus.vue"
export default {
  data() {
    return {
      imgUpload: {
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.png,.jpeg,.jpg,.gif',
        postData: {
          module: '仓库布置图', userId: auth.getUserID()
        },
      },
      pageName: 'train_questionBank',
      pageShow: false,
      pageInfo:{ 'currentPageNo': 0, 'pageSize': 20 , 'totalPage': 0  },
      page: 'main',
      questionBank: {
        type: 1, // type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
        state: 1, // status 1启用状态的，0停用状态的
        list: [],
        pageInfo: {
          currentPageNo: 1,
          pageSize: 20,
          totalPage: 1
        }
      },
      stopQuestionBank: {
        type: 1, // type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
        state: 0, // status 1启用状态的，0停用状态的
        list: [],
        pageInfo: {
          currentPageNo: 1,
          pageSize: 20,
          totalPage: 1
        }
      },
      form_editQuestionBank: {
        name: '',
        code: '',
        module: '培训管理',
        trainingQuestionBankAttachment: ''
      },
      form_editMt: {
        content: ''
      },
      form_editTQ: {
        origin: 'free',
        isBaseMaterial: '',
        material: {id: 0},
        TQType: '',
        isCorrectBaseMaterial: '',
        form_chooseMaterial: {
          canChooseMaterials: [],
          choseMaterial: {id: 0}
        }
      },
      form_editUsingMt: {
        questionBank: {
          code: '',
          name: ''
        },
        list: [],
        pageInfo: {}
      },
      form_editNoMt: {
        questionBank: {
          code: '',
          name: ''
        },
        list: []
      },
      form_stoppedMt: {
        questionBank: {
          code: '',
          name: ''
        },
        list: []
      },
      form_editSCQ: {
        material: {},
        right: '',
        title: '',
        distractors: [{content: ''},{content: ''},{content: ''}]
      },
      form_editMCQ: {
        material: {},
        right: '',
        title: '',
        distractors: [{content: ''},{content: ''}]
      },
      form_editTFQ: {
        material: {},
        right: '',
        right2: '',
        title: '',
        options: []
      },
      table_seeMt: {
        mtInfo: {},
        tqList: [],
        stopTQList: [],
        table_seeTQ: {
          material: {},
          question: {},
          options: []
        }
      },
      questionBankListPage: {},
      stepList: [],
      step3_shelfImgUrl: '',
      dialog_visible_editQuestionBank: false,
      dialog_visible_editMt: false,
      dialog_visible_seeMt: false,
      dialog_visible_editTQ: false,
      dialog_visible_chooseMaterial: false,
      dialog_visible_editSCQ: false,
      dialog_visible_editMCQ: false,
      dialog_visible_editTFQ: false,
      dialog_visible_seeStopTQByMt: false,
      dialog_visible_seeSCQ: false,
      dialog_visible_seeMCQ: false,
      dialog_visible_seeTFQ: false,
      dialog_visible_editDistracter: false,

      radios: {

      },
      newShelfRules: {
        shelfName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        shelfCode: [
          { required: true, message: '请输入代号', trigger: 'blur' }
        ],
        layers: [
          { required: true, message: '请输入层数', trigger: 'blur' }
        ],
        length: [
          { required: true, message: '请输入长', trigger: 'blur' }
        ],
        width: [
          { required: true, message: '请输入宽', trigger: 'blur' }
        ],
        height: [
          { required: true, message: '请输入高', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请选择单位', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ]
      },
      table_labelSet: []
    }
  },
  components: {
    ElInputPlus
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'nh', name: '试题库', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      this.getQuestionBankList()
    },
    getQuestionBankList(option = []) {
      // 获取试题库列表
      let defaultOption = {
        type: 1, // type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
        status: 1, // status 1启用状态的，0停用状态的
        currentPageNo: 1,
        pageSize: 20,
      }
      if (this.page === 'main') {
        defaultOption.status = 1
      } else if (this.page === 'stopTQ') {
        defaultOption.status = 0
      }
      let params = {...defaultOption, ...option}
      api.getQuestionBankList(params)
          .then(res => {
            let data = res.data
            let list = data.trainingQuestionBankList
            let pageInfo = data.pageInfo
            if (params.status === 0) {
              this.stopQuestionBank.list = list
              this.stopQuestionBank.pageInfo = pageInfo
            } else {
              this.questionBank.list = list
              this.questionBank.pageInfo = pageInfo
            }
            console.log('questionBank.pageInfo.totalPage', this.questionBank.pageInfo.totalPage)
          })
    },
    seeQBank() {
      // 查看试题库
      this.page = 'seeTQ'
    },
    stateQBank(id, state) {
      // 停用/启用试题库
      let tips = state === 0?`确定后，生成试卷时将不再选择该题库中的试题。<br>确定停用该题库吗？`:`确定后，生成试卷时本题库内的试题将重新可能被选择。<br>确定启用本题库吗？`
      this.$messageBox.confirm(
          tips,
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      )
          .then(() => {
            let data = {
              id: id,
              enabled: state
            }
            api.stateQBank(data)
                .then(res => {
                  let status = res.data.status
                  if (status === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    this.getQuestionBankList()
                  } else {
                    this.$message({
                      type: 'error',
                      message: '操作失败！'
                    })
                  }
                })
          })
    },
    deleteQBank(id) {
      // 删除试题库
      api.deleteJudgeQBank(id)
          .then(res => {
            let state = res.data.state
            if (state === 1) {
              this.$messageBox.confirm(
                  '确定删除该题库吗？',
                  '！提示',
                  {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                  }
              ).then(() => {
                api.deleteQBank(id).then(res => {
                  let state = res.data.state
                  if (state === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    this.get
                  } else {
                    this.$message({
                      type: 'error',
                      message: '操作失败！'
                    })
                  }
                })
              })
            } else {
              this.$message({
                type: 'error',
                message: '删除失败！因为该题库中含有已使用过的试题'
              })
            }
          })
    },
    editQuestionBank () {
      // 新增试题库
      this.dialog_visible_editQuestionBank = true
      api.getQuestionBankNewCode() // 服务器获取试题库代号
      .then(res => {
        this.form_editQuestionBank.code = res.data.code
      })
    },
    editQuestionBank_submit() {
      // 新增试题库 - 提交
      api.sureNewQuestionBank(this.form_editQuestionBank)
      .then(res => {
        let status = res.data.status
        this.dialog_visible_editQuestionBank = false
        this.msg(res)
        if (status === 1) {
          this.getQuestionBankList()
        }
      })
    },
    editMt() {
      // 新增素材
      this.dialog_visible_editMt = true
    },
    editMt_submit() {
      // 新增素材 - 提交
      let bankId = this.form_editUsingMt.questionBank.id
      let data = {
        bank: bankId,
        content: this.form_editMt.content
      }
      api.saveMt(data)
      .then(res => {
        let status = res.data.status
        this.dialog_visible_editMt = false
        this.msg(res)
        if (status === 1) {
          this.getMtListByBank({id: this.form_editUsingMt.questionBank.id, status: 1})
        }
      })

    },
    editTQ(origin) {
      // 新增试题
      this.dialog_visible_editTQ = true
      this.form_editTQ.origin = origin
      if (origin === 'seeMt') {
        this.form_editTQ.material = this.table_seeMt.mtInfo
      }
    },
    editTQ_submit() {
      // 新增试题 - 下一步（根据选择的种类不同跳转到 制作 单选题/多选题/判断题）
      let questionTypeArr = ['SCQ', 'MCQ', 'TFQ']
      let questionType = questionTypeArr[this.form_editTQ.TQType - 1]
      console.log(this.form_editTQ.TQType)
      console.log(questionType)
      this['dialog_visible_edit' + questionType] = true
      this['form_edit' + questionType].material = this.form_editTQ.material
      this.initEditTQ(questionType)
    },
    initEditTQ(questionType){
      let isBaseMaterial = this.form_editTQ.isBaseMaterial
      let isCorrectBaseMaterial = this.form_editTQ.isCorrectBaseMaterial
      switch (questionType) {
        case 'SCQ':
          break;
        case 'MCQ':
          break;
        case 'TFQ':
          if (isCorrectBaseMaterial === 1) {
            const defaultContent = this.form_editTFQ.material.content
            // this.form_editTFQ.options = Array(3).fill({content: defaultContent, isKey: 0})  // fill陷阱，fill对象时是引用，会导致联动
            this.form_editTFQ.options =  Array.from({ length: 3 }, () => ({ ...{content: defaultContent, isKey: 0} }));
          } else {
            this.form_editTFQ.options = [{content: '', isKey: ''}]
          }
          break;
      }
    },
    seeStopTQ() {
      // 查看已停用的试题
      api.seeMt({id: this.table_seeMt.mtInfo.id, status: 0})
        .then(res => {
          this.dialog_visible_seeStopTQByMt = true
          let data =res.data
          let tqList = data.trainingQuestionList || []
          this.table_seeMt.stopTQList = tqList
        })
    },
    editSCQ_submit() {
      // 新增单选题 - 提交
      let distractors = JSON.parse(JSON.stringify(this.form_editSCQ.distractors))
      distractors.map(item => item.isKey = 0)
      let right = { content: this.form_editSCQ.right, isKey: 1}
      distractors.push(right)
      let data = {
        material: this.form_editTQ.material.id,
        content: this.form_editSCQ.title,
        optionList: JSON.stringify(distractors)
      }
      console.log(distractors)
      console.log(data)
      api.editSCQ(data)
      .then(res => {
        let status = res.data.status
        if (status === 1) {
          this.$message({
            type: 'success',
            message: '操作成功！'
          })
          this['dialog_visible_editSCQ'] = false
        } else if (status === -2) {
          this.$message({
            type: 'error',
            message: '选项有重复无法新增！'
          })
        } else {
          this.$message({
            type: 'error',
            message: '操作失败！'
          })
        }
      })
    },
    editMCQ_submit() {
      // 新增单选题 - 提交
      let distractors = JSON.parse(JSON.stringify(this.form_editSCQ.distractors))
      distractors.map(item => item.isKey = 0)
      let right = { content: this.form_editSCQ.right, isKey: 1}
      distractors.push(right)
      let data = {
        material: this.form_editTQ.material.id,
        content: this.form_editSCQ.title,
        optionList: JSON.stringify(distractors)
      }
      console.log(distractors)
      console.log(data)
      api.editSCQ(data)
          .then(res => {
            let status = res.data.status
            if (status === 1) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this['dialog_visible_editSCQ'] = false
            } else if (status === -2) {
              this.$message({
                type: 'error',
                message: '选项有重复无法新增！'
              })
            } else {
              this.$message({
                type: 'error',
                message: '操作失败！'
              })
            }
          })
    },
    editTFQ_submit() {
      // 新增判断题 - 提交
      let options = JSON.parse(JSON.stringify(this.form_editTFQ.options))
      options = [{ content: this.form_editTQ.material.content, isKey: 1}, ...options];
      let data = {
        material: this.form_editTQ.material.id,
        contentList: JSON.stringify(options)
      }
      console.log(data)
      api.editTFQ(data)
          .then(res => {
            let status = res.data.status
            if (status === 1) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this['dialog_visible_editSCQ'] = false
            } else if (status === -1) {
              this.$message({
                type: 'error',
                message: '已达到可新增选择题上限！'
              })
            } else {
              this.$message({
                type: 'error',
                message: '操作失败！'
              })
            }
          })
    },
    addDistracterInput(type) {
      // 新增干扰项输入框（无接口）
      let form = this['form_edit' + type]
      if (form.distractors.length < 7) {
        this['form_edit' + type].distractors.push({content: ''})
      } else {
        this.$message({
          type: 'error',
          message: '干扰项不能多余7个！'
        })
      }
    },
    deleteDistracter_noInterface(type, index) {
      // 删除干扰项（无接口）
      this['form_edit' + type].distractors.splice(index, 1)
    },
    stoppedMt() {
      // 已停用的素材
      this.page = 'stoppedMt'
      this.getMtListByBank({id: this.form_editUsingMt.questionBank.id, status: 0})
    },
    seeMtByQuestionBank (mtItem) {
      // 查看某素材库的素材
      this.table_seeMt.mtInfo = mtItem
      let data = {
        id: mtItem.id,
        status: 1
      }
      api.seeMt(data)
      .then(res => {
        this.dialog_visible_seeMt = true
        let data =res.data
        let tqList = data.trainingQuestionList || []
        let mtInfo = data.trainingMaterial || []
        this.table_seeMt.mtInfo = mtInfo
        this.table_seeMt.tqList = tqList
      })

    },
    stateMt(id, state) {
      // 停用/启动素材
      let tips = state === 0?`确定后，生成试卷时将不再选择该素材下的试题。<br>确定停用该素材吗？`:`确定后，生成试卷时本素材下的试题将重新可能被选择。<br>确定启用本素材吗？`
      this.$messageBox.confirm(
          tips,
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      )
          .then(() => {
            let data = {
              id: id,
              enabled: state
            }
            api.stateMt(data)
                .then(res => {
                  let status = res.data.status
                  if (status === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    this.getMtListByBank({id: this.form_editUsingMt.questionBank.id, status: 1-state})
                  } else {
                    this.$message({
                      type: 'error',
                      message: '操作成功！'
                    })
                  }
                })
          })
    },
    deleteMt(id) {
      // 删除素材
      api.deleteJudgeMt(id)
      .then(res => {
        let state = res.data.status
        if (state === 1) {
          this.$messageBox.confirm(
              '确定删除该素材吗？',
              '！提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
          ).then(() => {
            api.deleteMt(id).then(res => {
              let state = res.data.status
              if (state === 1) {
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                })
                let status = this.page === 'stoppedMt'?0: 1
                this.getMtListByBank({id: this.form_editUsingMt.questionBank.id, status: status})
              } else {
                this.$message({
                  type: 'error',
                  message: '操作失败！'
                })
              }
            })
          })
        }
      })

    },
    chooseMaterial() {
      // 新增试题 - 选择素材
      this.dialog_visible_chooseMaterial = true
      this.form_editTQ.form_chooseMaterial.canChooseMaterials = this.form_editUsingMt.list
      this.form_editTQ.form_chooseMaterial.choseMaterial = this.form_editTQ.material
    },
    handleSelectionChange(row) {
      // 选择素材勾选赋值
      this.form_editTQ.form_chooseMaterial.choseMaterial = row
      console.log(row)
    },
    chooseMaterial_submit() {
      // 新增试题 - 选择素材 - 提交
      this.dialog_visible_chooseMaterial = false
      this.form_editTQ.material = this.form_editTQ.form_chooseMaterial.choseMaterial
    },
    getSelectedText(event) {
      // 新增试题 - 选中赋值
      let start = event.target.selectionStart
      let end = event.target.selectionEnd
      let value = event.target.value
      if (value && end !== 0) {
        let chooseTxt = value.slice(start, end)
        let rest = value.replace(chooseTxt, '')

        this.form_editSCQ.right = chooseTxt
        this.form_editSCQ.title = rest
      }
    },
    seeQT(item) {
      // 查看试题
      this.dialog_visible_seeSCQ = true
      let data = {
        id: item.id,
        status: 1
      }
      api.getQuestionDetail(data)
      .then(res => {
        let data = res.data
        console.log('table_seeMt', this.table_seeMt)
        let question = data.trainingQuestion
        let options = data.trainingQuestionKeyList // 选项（包括正确项、干扰项）
        this.table_seeMt.table_seeTQ.question = question
        this.table_seeMt.table_seeTQ.options = options
      })
    },
    editDistractors() {
      // 编辑干扰项
      this.dialog_visible_editDistracter = true
    },
    editDistracter_submit() {
      // 编辑干扰项
      let data = {
        question: question,
        disableKey: disableKey
      }
      api.addDistracter(data)
      .then(res => {

      })
    },
    seeStopOption() {
      // 查看已停用的选项

    },
    stateDistracter(id, state) {
      // 停用干扰项
      let tips = state === 0?`确定后，该干扰项将从系统中彻底消失。<br>确定删除该干扰项吗？`:`确定后，生成试卷时本素材下的试题将重新可能被选择。<br>确定启用本素材吗？`
      this.$messageBox.confirm(
          tips,
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      )
          .then(() => {
            let data = {
              id: id,
              enabled: state
            }
            api.stateDistracter(data)
                .then(res => {
                  let status = res.data.status
                  if (status === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    this.getMtListByBank({id: this.form_editUsingMt.questionBank.id, status: 1-state})
                  } else {
                    this.$message({
                      type: 'error',
                      message: '操作失败！'
                    })
                  }
                })
          })
    },
    deleteDistracter (id) {
      // 删除干扰项
      api.deleteDistracter(id)
          .then(res => {
            let state = res.data.status
            if (state === 1) {
              this.$messageBox.confirm(
                  '确定后，该干扰项将从系统中彻底消失。<br>' +
                  '确定删除该干扰项吗？',
                  '！提示',
                  {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                  }
              ).then(() => {
                api.deleteMt(id).then(res => {
                  let state = res.data.status
                  if (state === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    let status = this.page === 'stoppedMt'?0: 1
                    this.getMtListByBank({id: this.form_editUsingMt.questionBank.id, status: status})
                  } else {
                    this.$message({
                      type: 'error',
                      message: '操作失败！'
                    })
                  }
                })
              })
            }
          })
    },
    step_main_jumpUsingMt(item) {
      this.page = 'usingMt'
      this.form_editUsingMt.questionBank = item
      this.getMtListByBank({id: item.id})
    },
    step_main_jumpNoMt(item) {
      this.page = 'noMt'
      this.form_editNoMt.questionBank = item
      this.getMtListByBank({id: item.id})
    },
    step_main_jumpStopTQ() {
      this.page = 'stopTQ'
      this.getQuestionBankList(0)
    },
    pagination_questionBank_change(currentPageNo) {
      this.getQuestionBankList({currentPageNo: currentPageNo})

    },
    getMtListByBank(option) {
      let defaultOption = {
        // id: id,
        type: 1, // type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
        status: 1, // status 1启用状态的，0停用状态的
        currentPageNo: 1,
        pageSize: 20,
        keyword: ''
      }
      let data = {...defaultOption, ...option}
      api.getMtListByBank(data)
      .then(res => {
        let data = res.data
        let list = data.trainingMaterialList
        let pageInfo = data.pageInfo
        if (this.page === 'usingMt') {
          this.form_editUsingMt.list = list
          this.form_editUsingMt.pageInfo = pageInfo
        } else if (this.page === 'noMt') {
          this.form_editNoMt.list = list
          this.form_editNoMt.pageInfo = pageInfo
        } else if (this.page === 'stoppedMt') {
          this.form_stoppedMt.list = list
          this.form_stoppedMt.pageInfo = pageInfo
        }
      })
    },
    prevStep () {
      --this.page
    },
    msg(res) {
      let status = res.data.status
      if (status === 1) {
        this.$message({
          type: 'success',
          message: '操作成功'
        })
      } else {
        this.$message({
          type: 'error',
          message: '操作失败'
        })
      }
    },
    realPath (path) {
      return auth.webRoot + '/upload/' + path
    },
    questionBank_sort(column) {
      // 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
      let prop = column.prop
      let order = column.order
      let sort = 1
      if (order === null) {
        sort = 1
      } else {
        let sortArr = ['codeD', 'codeA', 'createDateD', 'createDateA','numD', 'numA']
        let orderS = order === 'descending'?'D':'A'
        sort = sortArr.findIndex(item => item === (prop + orderS)) + 1
      }
      this.getQuestionBankList({type: sort})
      console.log('sort', sort)
    },
    filter_QTType(value) {
      const arr = ['单选题', '判断题', '多选题']
      return arr[value-1]
    }
  },
  directives: {
    // 在模板中启用 v-focus
    onlyInteger: (el) => {
      // 这会在 `mounted` 和 `updated` 时都调用
      let ele = el.querySelector('input')
      ele.addEventListener('input', () => {
        // 获取输入框的当前值
        let value = ele.value;
        // 使用正则表达式匹配非正整数的部分，并将其替换为空字符串
        value = value.replace(/[^\d]/g, '');
        // 如果值为0，则直接设置为空字符串，因为0不是正整数
        if (value === '0') {
          value = '';
        }
        // 更新输入框的值
        ele.value = value;
      });
    }
  }
}


</script>