package cn.sphd.miners.modules.uploads.utils;

import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.system.entity.Organization;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Map;

public class UploadRootPath {
    private static UploadRootPath instance;
    private String defaultValue = "34400";

    public static UploadRootPath getInstance() {
        if(instance == null) {
            instance = new UploadRootPath();
        }
        return instance;
    }

    public String getUploadPath(HttpServletRequest request, Organization organization) {
        switch(organization.getUploadStorageType()) {
            case "NFS":
                return GetLocalIPUtils.getRootPath(request)+"/upload/";
        }
        return null;
    }
    public String getUploadPath(HttpServletRequest request, AuthAcc acc) {
        return GetLocalIPUtils.getRootPath(request)+"/upload/";
    }
    public String getFilePath(HttpServletRequest request, Organization organization) {
        String share_file_url;
        if("NFS".equalsIgnoreCase(organization.getUploadStorageType())&& StringUtils.isNotEmpty(share_file_url = System.getProperty("miners.share_file_url"))) {
            return share_file_url;
        } else {
            return getUploadPath(request, organization);
        }
    }
    public String getFilePath(HttpServletRequest request, AuthAcc acc) {
        String share_file_url;
        if(StringUtils.isNotEmpty(share_file_url = System.getProperty("miners.share_file_url"))) {
            return share_file_url;
        } else {
            return getUploadPath(request, acc);
        }
    }
    public String getOw365Path(HttpServletRequest request, Organization organization){
        String filePath = getFilePath(request, organization);
        if(StringUtils.isNotEmpty(filePath)) {
            return "https://ow365.cn/?i=" + getOw365Id(filePath) + "&ssl=1&furl=" + filePath;
        } else {
            return null;
        }
    }
    public String getOw365Path(HttpServletRequest request, AuthAcc acc){
        String filePath = getFilePath(request, acc);
        if(StringUtils.isNotEmpty(filePath)) {
            return "https://ow365.cn/?i=" + getOw365Id(filePath) + "&ssl=1&furl=" + filePath;
        } else {
            return null;
        }
    }
    public String getOw365Id(HttpServletRequest request){
        String domain = request.getServerName();
        return getOw365IdByDomain(domain);
    }
    public String getOw365Id(String urlString){
        try {
            URL url = new URL(urlString);
            String domain = url.getHost();
            return getOw365IdByDomain(domain);
        } catch (MalformedURLException e) {
            Logger.getLogger(getClass()).warn("getOw365Id error: ", e);
            return defaultValue;
        }
    }
    public String getOw365IdByDomain(String domain){
        String ow365ids=System.getProperty("miners.ow365id");
        Map<String,String> map = (Map) JSON.parse(ow365ids);
        String ow365id = map.getOrDefault(domain,defaultValue);
        return ow365id;
    }
}
