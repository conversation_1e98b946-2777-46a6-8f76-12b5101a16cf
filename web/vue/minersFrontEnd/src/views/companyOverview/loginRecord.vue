<template>
  <div class="loginRecordContainer">
    <div class="ty-container">
      <el-row>
        <el-col :span="12">
          <div class="ty-left">今天是 <span id="nowDate">{{ currentDate }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="ty-right flagTab" v-if="folderFlag === -2 && sort === 0">
            <el-popover placement="bottom" :width="300" trigger="manual" :visible="defineSearch.visible">
              <template #reference>
                <span class="ty-btn ty-btn-big ty-circle-5 loginQuery" :class="{'ty-btn-blue': flag === 3}" @click="defaultSearchBtn">自定义查询</span>
              </template>
              <div>
                <el-form-item label="起时间：">
                  <el-date-picker v-model="defineSearch.queryBeginTime" :teleported="false" type="date" placeholder="选择日期" value-format="YYYY-MM-DD">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="止时间：">
                  <el-date-picker v-model="defineSearch.queryEndTime" :teleported="false" type="date" placeholder="选择日期" value-format="YYYY-MM-DD">
                  </el-date-picker>
                </el-form-item>
                <div style="text-align: right;">
                  <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3 gapRt" @click="defineSearch.visible = false">取消</span>
                  <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="sureLoginQuery(3)">查询</span>
                </div>
              </div>
            </el-popover>
            <el-button-group class="ty-btn-group gapLt">
              <el-button
                  v-for="(btn, index) in stateButtons"
                  :key="index"
                  :type="btn.active && flag < 3 ? 'primary' : ''"
                  @click="changeState(btn.value)"
              >
                {{ btn.label }}
              </el-button>
            </el-button-group>
          </div>
          <div class="ty-right goBack" v-if="folderFlag > -2">
            <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="goBack()">返回</button>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="ty-mainData">
      <!--展示模块-->
      <div class="mainDataNav firstWrapper">
        <div class="loginPersonInfo">
          <div>
            <div class="LPI_time ty-inline-block">{{loginCount.date}}</div>
            <div class="LPI_name ty-inline-block">{{loginCount.userName}}</div>
          </div>
          <div>
            <div class="total_log">{{loginCount.duration}}</div>
            <div class="ttl_sign">登录总时长</div>
          </div>
        </div>
        <div class="totalLogin">
          <div class="ty-inline-block">
            <div class="loginSum">{{loginCount.total}}</div>
            <div class="loginSumDesc">登录总次数</div>
          </div>
        </div>

        <div class="mobileLogin">
          <i class="fa fa-mobile"></i>
          <div class="ty-inline-block">
            <div class="mobileNum">{{loginCount.mobileNum}}</div>
            <div class="mobileNumDesc">手机端</div>
          </div>

        </div>
        <div class="pcLogin">
          <i class="fa fa-desktop"></i>
          <div class="ty-inline-block">
            <div class="pcNum">{{loginCount.pcNum}}</div>
            <div class="pcNumDesc">电脑端</div>
          </div>

        </div>
      </div>
      <!--主展示区域-->
      <div class="dataNav">
        <!--时间姓名切换-->
        <div class="sortTab" style="margin-top: 10px" v-if="folderFlag < -1">
          <div class="ty-btn-group">
            <span class="ty-btn ty-btn-big ty-circle-5" :class="{'ty-btn-active-blue': tabIndex === 0}" @click="sortTab(0)">按时间排列</span>
            <span class="ty-btn ty-btn-big ty-circle-5" :class="{'ty-btn-active-blue': tabIndex === 1}" @click="sortTab(1)">按姓名排列</span>
          </div>
        </div>
        <!--所有表格-->
        <div class="dataList">
          <!-- 本日时间排序 -->
          <div id="dayInfo" v-if="folderFlag < -1 && flag === 0 && sort === 0 || folderFlag === 2 && folderSign === 0">
            <table class="ty-table ty-table-control">
              <thead><tr>
                <td width="15%">姓名</td>
                <td width="15%">部门名称</td>
                <td width="10%">职位</td>
                <td width="15%">登录时间</td>
                <td width="15%">登录时长</td>
                <td width="15%">登录端口</td>
                <td width="15%">登录地点</td>
              </tr></thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.userName}}</td>
                <td>{{item.departmentName || ''}}</td>
                <td>{{item.postName || ''}}</td>
                <td>{{new Date(item.operatetime).format('yyyy-MM-dd hh:mm:ss')}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.typeStr}}</td>
                <td>{{item.ip || ''}}</td>
              </tr>
              </tbody>
            </table>
            <div id="ye-dayInfo"></div>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 本日按姓名排列 -->
          <div id="dayName" v-if="folderFlag < -1 && flag === 0 && sort === 1">
            <table class="ty-table ty-table-control">
              <thead><tr>
                <td width="10%">姓名</td>
                <td width="10%">部门名称</td>
                <td width="10%">职位</td>
                <td width="10%">登录总时长</td>
                <td width="15%">最后登录时间</td>
                <td width="10%">登录总次数</td>
                <td width="10%">电脑登录总次数</td>
                <td width="10%">手机登录总次数</td>
                <td width="15%">更多</td>
              </tr></thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.userName || ''}}</td>
                <td>{{item.departmentName || ''}}</td>
                <td>{{item.postName || ''}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{new Date(item.operatetime).format('yyyy-MM-dd hh:mm:ss')}}</td>
                <td>{{item.count || ''}}</td>
                <td>{{item.com || ''}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                                    :curPage="loginPageInfo.currPage" :pageSize="20"
                                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>

          <!-- 本月按时间排序 -->
          <div id="monthInfo"  v-if="folderFlag < -1 && flag === 1 && sort === 0">
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>登录日期</td>
                <td>登录人数</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.loginYear || ''}}</td>
                <td>{{item.sum || ''}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.count || ''}}</td>
                <td>{{item.com || ''}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 本月按姓名排序 -->
          <div id="monthName" v-if="folderFlag < -1 && flag === 1 && sort === 1">
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>姓名</td>
                <td>部门名称</td>
                <td>职位</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.userName || ''}}</td>
                <td>{{item.departmentName || ''}}</td>
                <td>{{item.postName || ''}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.count || ''}}</td>
                <td>{{item.com || ''}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 本月按姓名排序-查看详情 -->
          <div id="monthDetail" v-if="folderFlag === 2 && folderSign === 1">
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>姓名</td>
                <td>部门名称</td>
                <td>职位</td>
                <td>登录日期</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.userName || ''}}</td>
                <td>{{item.departmentName || ''}}</td>
                <td>{{item.postName || ''}}</td>
                <td>{{item.loginYear || ''}}</td>
                <td>{{item.durationTime || '--'}}</td>
                <td>{{item.count || '--'}}</td>
                <td>{{item.com || '--'}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 本年按时间排序 -->
          <div id="yearInfo" v-if="Number(flag) === 2 && Number(sort) === 0 && folderFlag === -2" >
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>登录月份</td>
                <td>登录人数</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.loginYear || ''}}</td>
                <td>{{item.sum || ''}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.count || ''}}</td>
                <td>{{item.com || ''}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 本年按姓名排序 -->
          <div id="yearName" v-if="flag === 2 && sort === 1 && folderFlag === -2">
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>姓名</td>
                <td>部门名称</td>
                <td>职位</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.userName || ''}}</td>
                <td>{{item.departmentName || ''}}</td>
                <td>{{item.postName || ''}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.count || ''}}</td>
                <td>{{item.com || ''}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 本年按时间排序-查看详情 -->
          <div id="yearInfoDetail" v-if="folderFlag === 3 && folderSign === 1 || folderFlag === 4 && folderSign === 1" >
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>{{ yearInfoDetailTtl }}</td>
                <td>登录人数</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{folderFlag === 3 ? new Date(item.operatetime).format('yyyy-MM-dd'): item.loginYear}}</td>
                <td>{{item.sum || '--'}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.count || '--'}}</td>
                <td>{{item.com || '--'}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 本年按姓名排序-查看详情 -->
          <div id="yearDetail" v-if="folderFlag === 3 && folderSign === 2 || folderFlag === 4 && folderSign === 2">
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>姓名</td>
                <td>部门名称</td>
                <td>职位</td>
                <td>{{ yearDetailTtl }}</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.userName || ''}}</td>
                <td>{{item.departmentName || ''}}</td>
                <td>{{item.postName || ''}}</td>
                <td>{{item.loginYear || '--'}}</td>
                <td>{{item.durationTime || '--'}}</td>
                <td>{{item.count || '--'}}</td>
                <td>{{item.com || '--'}}</td>
                <td>{{chargeApp(item.app) }}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage id="ye-yearDetail" v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>

          <!-- 自定义时间排序 -->
          <div id="defineInfo"  v-if="folderFlag === -2 && flag === 3 && sort === 0">
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>{{ defineInfoTtl }}</td>
                <td>登录人数</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody id="diySearch">
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.loginYear}}</td>
                <td>{{item.sum || '--'}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.count || '--'}}</td>
                <td>{{item.com || '--'}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
          <!-- 自定义姓名排序 -->
          <div id="defineName"  v-if="folderFlag === -2 && flag === 3 && sort === 1">
            <table class="ty-table ty-table-control">
              <thead>
              <tr>
                <td>姓名</td>
                <td>部门名称</td>
                <td>职位</td>
                <td>登录总时长</td>
                <td>登录总次数</td>
                <td>电脑端登录总次数</td>
                <td>手机端登录总次数</td>
                <td>更多</td>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(item, index) in loginRecords" :key="index">
                <td>{{item.userName || ''}}</td>
                <td>{{item.departmentName || ''}}</td>
                <td>{{item.postName || ''}}</td>
                <td>{{item.durationTime || ''}}</td>
                <td>{{item.count || ''}}</td>
                <td>{{item.com || ''}}</td>
                <td>{{chargeApp(item.app)}}</td>
                <td><span class="ty-color-blue" @click="seeLoginRecordListDetailBtn(item)">登录查询</span></td>
              </tr>
              </tbody>
            </table>
            <TyPage v-if="loginPageInfo"
                    :curPage="loginPageInfo.currPage" :pageSize="20"
                    :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
          </div>
        </div>
      </div>
      <!--额外按姓名最后一级的显示区域-->
      <div class="moreList" v-if="folderFlag === 1">
        <div class="signTab" style="margin-top: 10px">
          <div class="ty-btn-group">
            <span class="ty-btn ty-btn-big ty-circle-5" :class="{'ty-btn-active-blue': signActive === 0}" @click="signTab(0)">电脑端登录记录</span>
            <span class="ty-btn ty-btn-big ty-circle-5" :class="{'ty-btn-active-blue': signActive === 1}" @click="signTab(1)">手机端登录记录</span>
          </div>
        </div>
        <div class="dataList">
          <!--本日-->
          <div class="dataContainer">
            <!--按姓名电脑端登录记录-->
            <div id="pcInfo">
              <table class="ty-table ty-table-control">
                <thead><tr>
                  <td width="25%">登录日期</td>
                  <td width="25%">登录时间</td>
                  <td width="25%">登录时长</td>
                  <td width="25%">登录地点</td>
                </tr></thead>
                <tbody>
                <tr v-for="(item, index) in loginRecords" :key="index">
                  <td>{{new Date(item.operatetime).format('yyyy-MM-dd')}}</td>
                  <td>{{new Date(item.operatetime).format('yyyy-MM-dd hh:mm:ss')}}</td>
                  <td>{{item.durationTime}}</td>
                  <td>{{item.ip || ''}}</td>
                </tr>
                </tbody>
              </table>
              <TyPage v-if="loginPageInfo"
                      :curPage="loginPageInfo.currPage" :pageSize="20"
                      :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
            </div>
            <!-- 按姓名手机端登录记录-->
            <div id="mbInfo" style="display:none;">
              <table class="ty-table ty-table-control">
                <thead><tr>
                  <td width="25%">登录日期</td>
                  <td width="25%">登录时间</td>
                  <td width="25%">登录时长</td>
                  <td width="25%">登录地点</td>
                </tr></thead>
                <tbody>
                <tr v-for="(item, index) in loginRecords" :key="index">
                  <td>{{new Date(item.operatetime).format('yyyy-MM-dd')}}</td>
                  <td>{{new Date(item.operatetime).format('yyyy-MM-dd hh:mm:ss')}}</td>
                  <td>{{item.durationTime}}</td>
                  <td>{{item.ip || ''}}</td>
                </tr>
                </tbody>
              </table>
              <TyPage v-if="loginPageInfo"
                      :curPage="loginPageInfo.currPage" :pageSize="20"
                      :allPage="loginPageInfo.totalPage" :pageClickFun="loginRecordsPage"></TyPage>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as recordApi from "@/api/companyOverview";
import {beforeRouteLeave, initNav} from "@/utils/routeChange";
import myLoginRecord from "@/mixins/loginRecord";

export default {
  mixins: [myLoginRecord],
  data() {
    return {}
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  methods: {
    getLoginRecordList(curr, totalPage, flag, sort) {
      const recordParam = {
        currPage: curr,
        pageSize: totalPage,
        flag: flag,
        sort: sort,
      };
      let beginTime= this.defineBeginTime
      let endTime = this.defineEndTime
      if (flag === 3) {
        recordParam.beginTime = beginTime;
        recordParam.endTime = endTime
      }
      recordApi.loginrecord(recordParam).then(response => {
        let userLogList =  response.data.userLogList
        let date = response.data.date || new Date().format('yyyy-MM-dd')
        let data = response.data
        this.currentDate = date;
        //this.loginHistory.push({ flag: flag, sort: sort });
        if(sort===0){
          switch (flag){
              //按本日时间排序 flag === 0 && sort === 0
            case 0 :
              userLogList.map(item => item.typeStr = this.chargePort(item.type))
              this.loginCount = {"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              break;
              //按本月时间排序 flag === 1 && sort === 0
            case 1 :
              //表头展示
              this.loginCount = {"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(var i=0;i<userLogList.length;i++){
                let nextJson = {
                  "flag" : 2,
                  "sort" : -1,
                  "sign" : 0,
                  "mdate" : userLogList[i].loginYear,
                  "loginYear" : userLogList[i].loginYear
                };
                userLogList[i].nextJson = nextJson
              }
              break;
              //按本年时间排序 flag === 2 && sort === 0
            case 2 :
              //表头展示
              this.loginCount = {"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++){
                let nextJson = {
                  "flag" : 3,
                  "sort" : -1,
                  "sign" : 1,
                  "mdate" : userLogList[i].loginYear
                };
                userLogList[i].nextJson = nextJson
              }
              break;

              //按自定义时间排序 flag === 3 && sort === 0
            case 3 :
              //表头展示
              this.loginCount = {"total":data.total,"date":data.beginTime+'~'+endTime,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++){
                let beginYear = beginTime.split("-")[0];
                let beginMonth = beginTime.split("-")[1];
                let endYear = endTime.split("-")[0];
                let endMonth = endTime.split("-")[1];
                let mdate;
                let edate;
                let nextJson = {};
                if(beginYear !== endYear){//跨年
                  mdate = userLogList[i].loginYear+"-01-01";
                  if(mdate<data.beginTime)
                    mdate=data.beginTime;
                  edate = userLogList[i].loginYear+"-12-31";
                  if(edate>endTime)
                    edate=endTime;
                  this.defineInfoDetailTtl = '登录年份'
                  nextJson = {
                    "flag" : 4,
                    "sort" : -1,
                    "sign" : 1,
                    "mdate" : mdate,
                    "edate" : edate,
                    "loginYear" : userLogList[i].loginYear
                  };
                }else if(beginMonth !== endMonth){//垮月
                  mdate = userLogList[i].loginYear+"-01";
                  if(mdate<data.beginTime)
                    mdate=data.beginTime;
                  var thisDate= new Date(userLogList[i].loginYear.split("-")[0],userLogList[i].loginYear.split("-")[1],0);
                  var days  = thisDate.getDate();
                  edate = userLogList[i].loginYear+"-"+days;
                  if(edate>endTime)
                    edate=endTime;
                  this.defineInfoDetailTtl = '登录月份'
                  nextJson = {
                    "flag" : 3,
                    "sort" : -1,
                    "sign" : 1,
                    "mdate" : mdate,
                    "edate" : edate,
                    "loginYear" : userLogList[i].loginYear
                  };
                }else{
                  mdate = userLogList[i].loginYear;
                  edate = userLogList[i].loginYear;
                  this.defineInfoDetailTtl = '登录日期'
                  nextJson = {
                    "flag" : 2,
                    "sort" : -1,
                    "sign" : 0,
                    "mdate" : mdate,
                    "edate" : edate,
                    "loginYear" : userLogList[i].loginYear
                  };
                }
                userLogList[i].nextJson = nextJson
              }
              break;
          }
        }else if(sort===1){
          switch (flag){
              //按本日姓名排序 flag === 0 && sort === 1
            case 0 :
              //表头展示
              this.loginCount = {"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}

              for(let i=0;i<userLogList.length;i++){
                let nextJson = {
                  "flag" : 1,
                  "sort" : 0,
                  "sign" : -1,
                  "userID" : userLogList[i].user_,
                  "mdate" : new Date(userLogList[i].operatetime).format('yyyy-MM-dd')
                };
                userLogList[i].nextJson = nextJson
              }
              break;

              //按本月姓名排序 flag === 1 && sort === 1
            case 1 :
              //表头展示
              this.loginCount = {"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++){
                var nextJson = {
                  "flag" : 2,
                  "sort" : -1,
                  "sign" : 1,
                  "userID" : userLogList[i].user_,
                  "mdate" : data.firstdate,
                  "edate" : date
                };
                userLogList[i].nextJson = nextJson
              }
              break;

              //按本年姓名排序 flag === 2 && sort === 1
            case 2 :
              //表头展示
              this.loginCount = {"total":data.total,"date":data.firstdate+'~'+date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++){
                let nextJson = {
                  "flag" : 3,
                  "sort" : -1,
                  "sign" : 2,
                  "userID" : userLogList[i].user_,
                  "mdate" : data.firstdate,
                  "edate" : date
                };
                userLogList[i].nextJson = nextJson
              }
              break;

              //按自定义姓名排序 flag === 3 && sort === 1
            case 3 :
              //表头展示
              this.loginCount = {"total":data.total,"date":data.beginTime+'~'+data.endTime,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++){
                let nextJson = {
                  "flag" : 4,
                  "sort" : -1,
                  "sign" : 2,
                  "userID" : userLogList[i].user_,
                  "mdate" : data.beginTime,
                  "edate" : data.endTime
                };
                userLogList[i].nextJson = nextJson
              }
              break;
          }
        }
        this.loginRecords = userLogList
        this.loginPageInfo = data
      }).catch(err => {
        console.log('err=', err)
      })
    },
    sortTab(tabIndex) {
      tabIndex = Number(tabIndex)
      //获取对应数据
      let thisFlag = this.flag
      let thisSort = tabIndex //0,1
      let json = {}
      this.sort = thisSort

      if(thisFlag === 3){
        //如果按钮组没有选中则为自定义
        //获取自定义数据
        let beginTime = this.defineBeginTime
        let endTime = this.defineEndTime
        if (!beginTime || !endTime) {
          return false
        }
        json = {
          "type" : 1,
          "flag" : thisFlag,
          "sort" : thisSort,
          "beginTime" : beginTime,
          "endTime" : endTime
        };
      }else{
        //获取其他状态数据
        json = {
          "type" : 1,
          "flag" : thisFlag,
          "sort" : thisSort
        };
      }
      if(this.tabIndex != tabIndex) {
        this.tabIndex = tabIndex
      }
      this.getLoginRecordList(1,20,thisFlag,thisSort);
      //设置状态追踪
      this.setLoginState(json);
    },
    seeLoginRecordListDetail(curr,totalPage,param) {
      //folderFlag=flag=1本日,flag=2本月,flag=3:sign=1本年里按时间排列,sign=2本年里按姓名排列,sign=3本年列表按姓名查看里的查看,flag=4:sign=1按时间排列\sign=2按姓名排列
      this.folderFlag = Number(param.flag)
      this.folderSign = Number(param.sign)
      this.detailStatus = true
      param["type"] = 2;

      let info = {
        "flag":param.flag,
        "currPage":curr,
        "pageSize":totalPage
      };
      let flag = param.flag;
      let sort = param.sort;
      let sign = param.sign;
      let mdate = param.mdate;
      let edate = param.edate;

      let userID = param.userID;
      if(userID === undefined){
        userID = param.userId;
      }
      let loginYear = param.loginYear;
      if(param.flag != 1) { this.signActive = 0 }
      if(mdate !== undefined && mdate !== -1){info["mdate"] = mdate;}
      if(edate !== undefined && edate !== -1){info["edate"] = edate;}
      if(userID !== undefined && userID !== -1){info["userID"] = userID;}
      if(sort !== undefined && sort !== -1){info["sort"] = sort;}
      if(sign !== undefined && sign !== -1){info["sign"] = sign;}
      if(loginYear !== undefined && loginYear !== -1){info["loginYear"] = loginYear;}
      recordApi.loginrecordDetail(info).then(response => {
        let userLogList = response.data.userLogList;
        let date = response.data.date;
        let data = response.data
        let nextJson = {}
        switch (flag) {
          case 1:
            this.loginCount = {"total":data.total,"date":data.date,"pcNum":data.computer,"mobileNum":data.app,"postName":data.postName,
              "departmentName":data.departmentName,"userName":data.userName, "duration": data.duration}
            break;
          case 2:
            if (sign === 0) {
              userLogList.map(item => item.typeStr = this.chargePort(item.type))
              this.loginCount = {"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
            }else if (sign === 1) {
              for(let i=0;i<userLogList.length;i++) {
                nextJson = {
                  "flag": 1,
                  "sort": 0,
                  "sign": -1,
                  "userID": userLogList[i].user_,
                  "mdate": userLogList[i].loginYear
                }
                userLogList[i].nextJson = nextJson
              }
              this.loginCount = {"total":data.total,"date":data.firstdate +'~' +date,"pcNum":data.computer,"mobileNum":data.app,
                "postName":userLogList[0].postName,"departmentName":userLogList[0].departmentName,"userName":userLogList[0].userName, "duration": data.duration}
            }
            break;
          case 3:
            if (sign === 1) {
              this.loginCount = {"total":data.total,"date":date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++) {
                nextJson = {
                  "flag": 2,
                  "sort": -1,
                  "sign": 0,
                  "mdate": new Date(userLogList[i].operatetime).format('yyyy-MM-dd'),
                  "loginYear": new Date(userLogList[i].operatetime).format('yyyy-MM-dd')
                }
                userLogList[i].nextJson = nextJson
              }
            } else  if (sign === 2) {
              this.loginCount = {"total":data.total,"date":data.firstdate +'~' +date,"pcNum":data.computer,"mobileNum":data.app,
                "postName":userLogList[0].postName,"departmentName":userLogList[0].departmentName,"userName":userLogList[0].userName, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++) {
                let thisDate = new Date(userLogList[i].loginYear.split("-")[0], userLogList[i].loginYear.split("-")[1], 0);
                let days = thisDate.getDate();
                let mdate = userLogList[i].loginYear + "-01";
                if (mdate < data.firstdate) mdate = data.firstdate
                let edate = userLogList[i].loginYear + "-" + days;
                if (edate > date) edate = date;
                  nextJson = {
                    "flag": 2,
                    "sort": -1,
                    "sign": 1,
                    "mdate": mdate,
                    "edate": edate,
                    "userID": userLogList[i].user_
                  };
                  userLogList[i].nextJson = nextJson
              }
            }
            break;
          case 4:
            if (sign === 1) {
              this.loginCount = {"total":data.total,"date":data.firstdate +'~' +date,"pcNum":data.computer,"mobileNum":data.app, "duration": data.duration}
              for(let i=0;i<userLogList.length;i++) {
                let arr = userLogList[i].loginYear.split('-');
                flag = 5 - arr.length;
                let mdate;
                let edate;
                let thisDate = new Date(arr[0], arr[1], 0);
                let days = thisDate.getDate();
                mdate = arr[0] + "-" + arr[1] + "-01";
                edate = arr[0] + "-" + arr[1] + "-" + days;
                switch (flag) {
                  case 3:
                    sign = 1;
                    break;
                  case 2:
                    sign = 0;
                    break;
                  case 1:
                    mdate = userLogList[i].loginYear;
                    edate = userLogList[i].loginYear;
                    break;
                }
                mdate = mdate > data.firstdate ? mdate : data.firstdate;
                edate = edate < date ? edate : date;
                nextJson = {
                  "flag": flag,
                  "sort": -1,
                  "sign": sign,
                  "mdate": mdate,
                  "edate": edate,
                  "loginYear": userLogList[i].loginYear
                }
                userLogList[i].nextJson = nextJson
              }
            } else if (sign === 2) {
              this.loginCount = {
                "total": data.total,
                "date": data.firstdate + '~' + date,
                "pcNum": data.computer,
                "mobileNum": data.app,
                "postName": userLogList[0].postName,
                "departmentName": userLogList[0].departmentName,
                "userName": userLogList[0].userName,
                "duration": data.duration
              }
              for (let i = 0; i < userLogList.length; i++) {
                let arr = userLogList[i].loginYear.split('-');
                flag = 4 - arr.length;
                let mdate;
                let edate;
                let thisDate = new Date(arr[0], arr[1], 0);
                let days = thisDate.getDate();
                switch (flag) {
                  case 3:
                    mdate = arr[0] + "-01-01";
                    edate = arr[0] + "-12-31";
                    break;
                  case 2:
                    mdate = arr[0] + "-" + arr[1] + "-01";
                    edate = arr[0] + "-" + arr[1] + "-" + days;
                    sign = 1;
                    break;
                  case 1:
                    mdate = userLogList[i].loginYear;
                    edate = userLogList[i].loginYear;
                    break;
                }
                mdate = mdate > data.firstdate ? mdate : data.firstdate;
                edate = edate < date ? edate : date;
                nextJson = {
                  "flag": flag,
                  "sort": 0,
                  "sign": sign,
                  "mdate": mdate,
                  "edate": edate,
                  "loginYear": userLogList[i].loginYear,
                  "userID": userLogList[i].user_
                }
                userLogList[i].nextJson = nextJson
              }
            }
        }
        this.loginRecords = userLogList
        this.loginPageInfo = data
      }).catch(err => {
        console.log('err=', err)
      })
    },
  },
  mounted() {
    initNav({mid: 'bea', name: '登录记录', pageName: this.pageName}, this.setLoginRecordIndex(), this)
  },
};
</script>

<style scoped lang="scss">
@use "@/style/loginRecord.scss";
</style>
