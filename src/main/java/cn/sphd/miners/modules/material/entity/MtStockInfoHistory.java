/**
	* Copyright (c) minuteproject, <EMAIL>
	* All rights reserved.
	* 
	* Licensed under the Apache License, Version 2.0 (the "License")
	* you may not use this file except in compliance with the License.
	* You may obtain a copy of the License at
	* 
	* http://www.apache.org/licenses/LICENSE-2.0
	* 
	* Unless required by applicable law or agreed to in writing, software
	* distributed under the License is distributed on an "AS IS" BASIS,
	* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
	* See the License for the specific language governing permissions and
	* limitations under the License.
	* 
	* More information on minuteproject:
	* twitter @minuteproject
	* wiki http://minuteproject.wikispaces.com 
	* blog http://minuteproject.blogspot.net
	* 
*/
/**
	* template reference : 
	* - Minuteproject version : 0.9.5
	* - name      : DomainEntityJPA2Annotation
	* - file name : DomainEntityJPA2Annotation.vm
	* - time      : 2018/01/20 ��Ԫ at 09:35:43 CST
*/
package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;
import java.util.Date;

@Entity (name="MtStockInfoHistory")
@Table (name="t_mt_stock_info_history")
public class MtStockInfoHistory {

	
    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @XmlElement (name="id")
    private Integer id;

    @Column(name="stock_info"   , nullable=true , unique=true)
    @XmlElement (name="stock-info")
    private Integer stockInfo; 

    @Column(name="material"   , nullable=true , unique=false)
    @XmlElement (name="material")
    private Integer material; 

    @Column(name="initial_stock"   , nullable=true , unique=false)
    @XmlElement (name="initial-stock")
    private BigDecimal initialStock;

    @Column(name="current_stock"   , nullable=true , unique=false)
    @XmlElement (name="current-stock")
    private BigDecimal currentStock;

    @Column(name="available_stock"   , nullable=true , unique=false)
    @XmlElement (name="available-stock")
    private BigDecimal availableStock;

    @Column(name="minimum_stock"   , nullable=true , unique=false)
    @XmlElement (name="minimum-stock")
    private BigDecimal minimumStock;

    @Column(name="stock_position"  , length=100 , nullable=true , unique=false)
    @XmlElement (name="stock-position")
    private String stockPosition; 

    @Column(name="stock_requirements"  , length=255 , nullable=true , unique=false)
    @XmlElement (name="stock-requirements")
    private String stockRequirements; 

    @Column(name="last_inventory_time"   , nullable=true , unique=false)
    @XmlElement (name="last-inventory-time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastInventoryTime;

    //供应商 名称
   @Column(name="provider_name"  , length=255 , nullable=true , unique=false)
    @XmlElement (name="provider_name")
    private String providerName;

    @Column(name="creator", nullable=true , unique=false)
    @XmlElement (name="creator")
    private Integer creator; 

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    @XmlElement (name="create-name")
    private String createName; 

    @Column(name="create_date"   , nullable=true , unique=false)
    @XmlElement (name="create-date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    @XmlElement (name="updator")
    private Integer updator; 

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    @XmlElement (name="update-name")
    private String updateName; 

    @Column(name="update_date"   , nullable=true , unique=false)
    @XmlElement (name="update-date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @Column(name="after_initial_stock"   , nullable=true , unique=false)
    @XmlElement (name="after_initial_stock")
    private BigDecimal afterInitialStock;

    @Column(name="after_minimum_stock"   , nullable=true , unique=false)
    @XmlElement (name="after_minimum_stock")
    private BigDecimal afterMinimumStock;

    @Column(name="supplier"   , nullable=true , unique=false)
    @XmlElement (name="supplier")
    private Integer supplier;

    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    

    public Integer getStockInfo() {
        return stockInfo;
    }
	
    public void setStockInfo (Integer stockInfo) {
        this.stockInfo =  stockInfo;
    }

    public Integer getMaterial() {
        return material;
    }
	
    public void setMaterial (Integer material) {
        this.material =  material;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(BigDecimal availableStock) {
        this.availableStock = availableStock;
    }

    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public String getStockPosition() {
        return stockPosition;
    }
	
    public void setStockPosition (String stockPosition) {
        this.stockPosition =  stockPosition;
    }

    public String getStockRequirements() {
        return stockRequirements;
    }
	
    public void setStockRequirements (String stockRequirements) {
        this.stockRequirements =  stockRequirements;
    }

    public Date getLastInventoryTime() {
        return lastInventoryTime;
    }
	
    public void setLastInventoryTime (Date lastInventoryTime) {
        this.lastInventoryTime =  lastInventoryTime;
    }

    public Integer getCreator() {
        return creator;
    }
	
    public void setCreator (Integer creator) {
        this.creator =  creator;
    }

    public String getCreateName() {
        return createName;
    }
	
    public void setCreateName (String createName) {
        this.createName =  createName;
    }

    public Date getCreateDate() {
        return createDate;
    }
	
    public void setCreateDate (Date createDate) {
        this.createDate =  createDate;
    }

    public Integer getUpdator() {
        return updator;
    }
	
    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }

    public String getUpdateName() {
        return updateName;
    }
	
    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }
	
    public void setUpdateDate (Date updateDate) {
        this.updateDate =  updateDate;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public BigDecimal getAfterInitialStock() {
        return afterInitialStock;
    }

    public void setAfterInitialStock(BigDecimal afterInitialStock) {
        this.afterInitialStock = afterInitialStock;
    }

    public BigDecimal getAfterMinimumStock() {
        return afterMinimumStock;
    }

    public void setAfterMinimumStock(BigDecimal afterMinimumStock) {
        this.afterMinimumStock = afterMinimumStock;
    }

    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }
}
