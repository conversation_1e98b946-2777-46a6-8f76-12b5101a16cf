package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_pd_image")
public class PdImage {
    private Long id;
    private Integer org;
    private Integer product;
    private Integer category;
    private String title;
    private Integer type;
    private String uplaodPath;
    private Integer orders;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "product")
    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    @Basic
    @Column(name = "category")
    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    @Basic
    @Column(name = "title")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Basic
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Basic
    @Column(name = "uplaod_path")
    public String getUplaodPath() {
        return uplaodPath;
    }

    public void setUplaodPath(String uplaodPath) {
        this.uplaodPath = uplaodPath;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PdImage pdImage = (PdImage) o;
        return id == pdImage.id &&
                Objects.equals(org, pdImage.org) &&
                Objects.equals(product, pdImage.product) &&
                Objects.equals(category, pdImage.category) &&
                Objects.equals(title, pdImage.title) &&
                Objects.equals(type, pdImage.type) &&
                Objects.equals(uplaodPath, pdImage.uplaodPath) &&
                Objects.equals(orders, pdImage.orders) &&
                Objects.equals(memo, pdImage.memo) &&
                Objects.equals(creator, pdImage.creator) &&
                Objects.equals(createName, pdImage.createName) &&
                Objects.equals(createDate, pdImage.createDate) &&
                Objects.equals(updator, pdImage.updator) &&
                Objects.equals(updateName, pdImage.updateName) &&
                Objects.equals(updateDate, pdImage.updateDate) &&
                Objects.equals(operation, pdImage.operation) &&
                Objects.equals(previousId, pdImage.previousId) &&
                Objects.equals(versionNo, pdImage.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, product, category, title, type, uplaodPath, orders, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
