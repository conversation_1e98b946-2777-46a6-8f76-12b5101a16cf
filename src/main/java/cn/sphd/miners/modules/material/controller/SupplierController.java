package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.PoContractBaseService;
import cn.sphd.miners.modules.material.service.SrmSupplierService;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson2.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

@Controller
@RequestMapping("/supplier")
public class SupplierController {

    @Autowired
    private SrmSupplierService supplierService;
    @Autowired
    private PoContractBaseService poContractBaseService;

    //-update by zy
    //1.247：共用供应商模块，以type 区分 1-普通供应商 2-装备供应商
    //部分接口增加传参 type进行区分
    //原重载方法搜索供应商 getByOid 更名为 searchByOid

    //1.259删除合同
    @ResponseBody
    @PostMapping("/removeContract.do")
    public JsonResult removeContract(Integer id,User user){
        String res = poContractBaseService.removeContract(id,user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    //1.259-删除邮寄信息
    @ResponseBody
    @PostMapping("/removeAddress.do")
    public JsonResult removeAddress(User user, Integer addressId){
        String res = supplierService.removeAddress(user,addressId);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    //进入供应商首页
    @RequestMapping("/index.do")
    public String index(User user, @RequestParam(defaultValue = "1") Integer type) {
        List<SrmSupplier> suppliers = supplierService.getByOid(user.getOid(), 1, null, null, type);

        if (suppliers != null && suppliers.size() > 0) {
            for (SrmSupplier s : suppliers) {
                List<MtSupplierMaterial> supplierMaterials1 = supplierService.getSupplierMaterialsByState(s.getId(), 1);
                if (supplierMaterials1 != null && supplierMaterials1.size() > 0) {
                    if (s.getSupplyCount() == null)
                        s.setSupplyCount(supplierMaterials1.size());//设置供应中的数量

                    //计算独家供应的数量
                    Integer count = 0;

                    for (MtSupplierMaterial supplierMaterial : supplierMaterials1) {
                        List<MtSupplierMaterial> supplierMaterials = supplierService.getSupplierMaterialsByMaterial(supplierMaterial.getMaterial().getId());
                        if (supplierMaterials != null && supplierMaterials.size() <= 1) {
                            count += 1;
                        } else {
                            s.setCutCount(0);
                        }
                    }

                    s.setExclusiveCount(count);
                }
                List<MtSupplierMaterial> supplierMaterials2 = supplierService.getSupplierMaterialsByState(s.getId(), 0);
                if (supplierMaterials2 != null && supplierMaterials2.size() > 0) {
                    s.setCutCount(supplierMaterials2.size());//设置不再供应的数量
                }

            }
        }
        return "/supplierproduct/suppliecontract";
    }


    /**
     * 获取供应商列表
     */
    @ResponseBody
    @RequestMapping("/getSupplierList.do")
    public JsonResult getSupplierList(User user, @RequestParam(defaultValue = "1") Integer type, PageInfo pageInfo) throws IOException {

        Map<String, Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<SrmSupplier> list = supplierService.searchByOid(oid, 1, pageInfo, null, type);
        map.put("pageInfo", pageInfo);
        map.put("list", list);
        return new JsonResult(1, map);
    }

    /**
     * 搜素供应商
     */
    @ResponseBody
    @RequestMapping("/searchSupplier.do")
    public JsonResult searchSupplier(User user, @RequestParam(defaultValue = "1") Integer type, PageInfo pageInfo, String keyword) throws IOException {

        Integer oid = user.getOid();
        List<SrmSupplier> list = supplierService.searchByOid(oid, 1, pageInfo, keyword, type);
        Map<String, Object> map = new HashMap<>();
        map.put("pageInfo", pageInfo);
        map.put("list", list);
        return new JsonResult(1, map);
    }

    /**
     * 获取已暂停采购的供应商
     */
    @ResponseBody
    @RequestMapping("/getSuspendSupplierList.do")
    public JsonResult getSuspendSupplierList(User user, PageInfo pageInfo,  @RequestParam(defaultValue = "1") Integer type) throws IOException {
        Integer oid = user.getOid();
        List<SrmSupplier> list = supplierService.searchByOid(oid, 0, pageInfo, null, type);
        Map<String, Object> map = new HashMap<>();
        map.put("pageInfo", pageInfo);
        map.put("list", list);
        return new JsonResult(1, map);
    }

    /**
     * 新增供应商
     */
    @ResponseBody
    @RequestMapping("/addSupplier.do")
    public JsonResult addSupplier(User user, SrmSupplierVo srmSupplierVo, HttpServletRequest request) throws IOException {
        Map<String, Object> params = new HashMap<>();
        params.put("yjAddressList", request.getParameter("yjAddressList"));
        params.put("contactsList", request.getParameter("contactsList"));
        params.put("qImages", request.getParameter("qImages"));
        ;
        params.put("contractBaseList", request.getParameter("contractBaseList"));
        SrmSupplier supplier = new SrmSupplier();
        BeanUtils.copyProperties(srmSupplierVo, supplier);

        if(supplier.getType()==null)
            supplier.setType(1);
        //1.265:初始化 设置新增的供应商operation为 0  初始化之后改成1
        supplier.setOperation("0");
        String res = supplierService.addSupplier(supplier, params, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }

    }

    /**
     * 供应商查看
     */
    @ResponseBody
    @RequestMapping("/getSrmSupplierOne.do")
    public JsonResult getSrmSupplierOne(User user, Integer id) throws IOException {

        SrmSupplier supplier = supplierService.getSrmSupplierOne(id);
        return new JsonResult(1, supplier);

    }

    /**
     * 供应商删除
     */
    @ResponseBody
    @RequestMapping("/deleteSrmSupplier.do")
    public JsonResult deleteSrmSupplier(User user, Integer id) throws IOException {

        SrmSupplier supplier = supplierService.getById(id);
        if (supplier != null) {
            if ((supplier.getSupplyCount() != null && supplier.getSupplyCount().intValue() != 0) || (supplier.getExclusiveCount() != null && supplier.getExclusiveCount().intValue() != 0) || (supplier.getCutCount() != null && supplier.getCutCount().intValue() != 0)) {
                return new JsonResult(0, "", "400", "该供应已被使用，不能删除");
            }
        } else {
            return new JsonResult(0, "", "400", "该供应商不存在");
        }
        supplierService.deleteSrmSupplier(id);
        return new JsonResult(1, null);
    }

    /**
     * 供应商暂停
     */
    @ResponseBody
    @RequestMapping("/stopSupplier.do")
    public JsonResult stopSupplier(User user, Integer id) throws IOException {

        SrmSupplier supplier = supplierService.getById(id);
        if (supplier != null) {
            if ((supplier.getSupplyCount() != null && supplier.getSupplyCount().intValue() != 0) || (supplier.getExclusiveCount() != null && supplier.getExclusiveCount().intValue() != 0) || (supplier.getCutCount() != null && supplier.getCutCount().intValue() != 0)) {
                return new JsonResult(0, "", "400", "该供应已被使用，不能暂停");
            }
        } else {
            return new JsonResult(0, "", "400", "该供应商不存在");
        }
        supplierService.recoveryOrStopSrmSupplier(id, 0, user);


        return new JsonResult(1, null);
    }

    /**
     * 供应商恢复
     */
    @ResponseBody
    @RequestMapping("/recoverySrmSupplier.do")
    public JsonResult recoverySrmSupplier(User user, Integer id) throws IOException {

        supplierService.recoveryOrStopSrmSupplier(id, 1, user);

        return new JsonResult(1, null);
    }

    /**
     * 供应商暂停，启用记录
     */
    @ResponseBody
    @RequestMapping("/getStartAndStopList.do")
    public JsonResult getStartAndStopList(User user, Integer id) throws IOException {


        List<Map<String, Object>> list = supplierService.getStartAndStopList(id);

        return new JsonResult(1, list);
    }

    /**
     * 基本信息查看
     */
    @ResponseBody
    @RequestMapping("/getSupplierBase.do")
    public JsonResult getSupplierBase(User user, Integer id) throws IOException {

        SrmSupplier supplier = supplierService.getSrmSupplierOne(id);

        return new JsonResult(1, supplier);
    }

    /*
     **
     * 获取邮寄地址信息
     */
    @ResponseBody
    @RequestMapping("/getAddressData.do")

    public JsonResult getAddressData(Integer addressId) {

        //根据获取客户所有地址
        Map<String, Object> data = supplierService.getAddress(addressId);

        return new JsonResult(1, data);
    }

    /**
     * 基本信息修改
     */
    @ResponseBody
    @RequestMapping("/updateSupplierBase.do")
    public JsonResult updateSupplierBase(User user, SrmSupplier srmSupplier) throws IOException {

        if(StringUtils.isNotEmpty(srmSupplier.getImagesString())){
            JSONArray array=JSONArray.parse(srmSupplier.getImagesString());
            List<Map<String, Object>> list=new ArrayList();
            for (int i = 0; i < array.size(); i++) {
                Map<String,Object> map=new HashMap<>();
                map.put("normal",array.getJSONObject(i).get("normal"));
                list.add(map);
            }
            srmSupplier.setqImages(list);
        }
        supplierService.updateSrmSupplierBase(srmSupplier, user);

        return new JsonResult(1, null);
    }



    /*
     **
     *<AUTHOR>
     * 基本信息修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getRecordBaseList.do")
    public JsonResult getRecordBaseList(Integer id) {

        List<Map<String, Object>> list = supplierService.getRecordBaseList(id);

        Map<String, Object> map = this.getRecord(list);

        return new JsonResult(1, map);
    }


    /*
     **
     *<AUTHOR>
     * 基本修改记录信息
     */
    @ResponseBody
    @RequestMapping("/getRecordBaseDetails.do")
    public JsonResult getRecordBaseDetails(Integer id, Integer front) {


        Map<String, Object> data = supplierService.getRecordBaseDetails(id, front);

        return new JsonResult(1, data);
    }


    /*
     **
     *<AUTHOR>
     * 开票信息查看
     */
    @ResponseBody
    @RequestMapping("/getSupplierInvoice.do")
    public JsonResult getSupplierInvoice(Integer id) {


        SrmSupplier supplier = supplierService.getSrmSupplier(id);

        SrmSupplierInvoice srmSupplierInvoice = supplierService.getSrmSupplierInvoice(supplier.getId());
        if (srmSupplierInvoice != null) {
            supplier.setTaxRate(srmSupplierInvoice.getTaxRate());
        }
        if (supplier.getVatsCount() == null) {
            supplier.setVatsCount(0);
        }

        if (supplier.getOtherInvoiceCount() == null) {
            supplier.setOtherInvoiceCount(0);
        }

        if (supplier.getNotInvoiceCount() == null) {
            supplier.setNotInvoiceCount(0);
        }

        return new JsonResult(1, supplier);
    }

    @ResponseBody
    @RequestMapping("/getSupplierInvoices.do")
    public JsonResult getSupplierInvoices(Integer id) {
        List<SrmSupplierInvoice> invoices = supplierService.getSrmSuppliers(id);

        return new JsonResult(1, invoices);

    }

    @RequestMapping(value = "/addSupplierInvoice.do", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult addSupplierInvoice(SrmSupplierInvoice invoice, User user) {
        return supplierService.addSupplierInvoice(invoice, user);
    }

    @RequestMapping(value = "/deleteSupplierInvoice.do", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult deleteSupplierInvoice(SrmSupplierInvoice invoice, User user) {
        return supplierService.deleteSupplierInvoice(invoice, user);
    }

    @RequestMapping(value = "/materialAddress.do", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult materialAddress(Integer id, User user) {
        return supplierService.materialAddress(id);
    }

    /*
     **
     *<AUTHOR>
     * 开票信息修改
     */
    @ResponseBody
    @RequestMapping("/updateSupplierInvoice.do")
    public JsonResult updateSupplierInvoice(User user, SrmSupplier supplier) {

        supplierService.updateSupplierInvoice(supplier, user);

        return new JsonResult(1, null);
    }


    /*
     **
     *<AUTHOR>
     *获取供应商开票记录列表
     */
    @ResponseBody
    @RequestMapping("/getRecordInvoiceList.do")
    public JsonResult getRecordInvoiceList(Integer id) {


        List<Map<String, Object>> list = supplierService.getRecordInvoiceList(id);
        Map<String, Object> map = this.getRecord(list);

        return new JsonResult(1, map);
    }


    /*
     **
     *<AUTHOR>
     *获取供应商开票记录详情
     */
    @ResponseBody
    @RequestMapping("/getRecordInvoiceDetails.do")
    public JsonResult getRecordInvoiceDetails(Integer id, Integer front) {


        Map<String, Object> data = supplierService.getRecordInvoiceDetails(id, front);

        return new JsonResult(1, data);
    }

    /**
     * 孙文
     * 供应撒的合同列表
     */
    @ResponseBody
    @RequestMapping("/getContractBaseListBySupplier.do")
    public JsonResult getContractBaseListBySupplier(Integer id) {

        SrmSupplier supplier = supplierService.getSrmSupplier(id);
        if (supplier == null) {
            return new JsonResult(0, "", "400", "供应商不存在");
        }
        //获取合同信息
        List<Map<String, Object>> contractBaseList = poContractBaseService.getContractBaseListBySupplier(id);
        Map<String, Object> map = new HashMap<>();
        map.put("name", supplier.getName());
        map.put("fullName", supplier.getFullName());
        map.put("codeName", supplier.getCodeName());
        map.put("list", contractBaseList);
        return new JsonResult(1, map);
    }

    /*
     **
     * 获取供应商的合同列表
     */
    @ResponseBody
    @RequestMapping("/getContractList.do")

    public JsonResult getContractList(Integer supplierId) {
        List<Map<String, Object>> mtList = poContractBaseService.getContractBaseListBySupplier(supplierId);
        return new JsonResult(1, mtList);
    }

    /*
     **
     * 获取没有合同的材料列表
     */
    @ResponseBody
    @RequestMapping("/getContractMaterialList.do")

    public JsonResult getContractMaterialList(User user, Integer id) {
        List<Map<String, Object>> mtList = poContractBaseService.getNoContractMaterialList(user.getOid(), id);
        return new JsonResult(1, mtList);
    }

    /*
     **
     * 供应商——新增合同
     */
    @ResponseBody
    @RequestMapping("/addContractBase.do")

    public JsonResult addContractBase(User user, String contractBase, Integer supplierId) {

        String res = poContractBaseService.addContractBase(contractBase, supplierId, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     * 销售——客户管理——修改合同
     */
    @ResponseBody
    @RequestMapping("/updateContractBase.do")

    public JsonResult updateContractBase(User user, String contractBase) {
        String res = poContractBaseService.updateContractBase(contractBase, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     * 销售——客户管理——续约合同
     */
    @ResponseBody
    @RequestMapping("/renewalContractBase.do")

    public JsonResult renewalContractBase(User user, String contractBase) {


        String res = null;
        try {
            res = poContractBaseService.renewalContractBase(contractBase, user);
        } catch (Exception e) {
            e.printStackTrace();
            return new JsonResult(0, "", "400", e.getMessage());
        }
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     * 销售——客户管理——查看合同
     */
    @ResponseBody
    @RequestMapping("/getContractBase.do")

    public JsonResult getContractBase(Integer id) {
        PoContractBase res = poContractBaseService.getContractBase(id);
        return new JsonResult(1, res);
    }

    /*
     **
     * 销售——客户管理——暂停/启用合同
     */
    @ResponseBody
    @RequestMapping("/suspendOrStartContractBase.do")

    public JsonResult suspendOrStartContractBase(Integer id, Integer enabled, User user) {
        String res = poContractBaseService.suspendOrStartContractBase(id, enabled, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }


    /**
     * 孙文
     * 客户管理>暂停合同列表
     */
    @ResponseBody
    @RequestMapping("/getSuspendContractBaseList.do")
    public JsonResult getSuspendContractBaseList(Integer id) {

        List<Map<String, Object>> result = poContractBaseService.getSuspendContractBaseList(id);

        return new JsonResult(1, result);
    }

    /**
     * 孙文
     * 客户管理>合约到期合同列表
     */
    @ResponseBody
    @RequestMapping("/getContractBaseEndList.do")
    public JsonResult getContractBaseEndList(Integer id) {
        List<Map<String, Object>> result = poContractBaseService.getContractBaseEndList(id);
        return new JsonResult(1, result);
    }

    /*
     **
     * 根据合同获取商品列表
     */
    @ResponseBody
    @RequestMapping("/getMtListByContract.do")

    public JsonResult getMtListByContract(Integer id) {

        List<Map<String, Object>> productList = poContractBaseService.getMtListByContract(id);
        return new JsonResult(1, productList);
    }

    /*
     **
     * 获取联系人列表
     */
    @ResponseBody
    @RequestMapping("/getContactsList.do")

    public JsonResult getContactsList(Integer supplierId) {

        List<Map<String, Object>> list = supplierService.getContactsList(supplierId, 1);

        return new JsonResult(1, list);
    }

    /*
     **
     * 获取删除的联系人列表
     */
    @ResponseBody
    @RequestMapping("/getDeleteContactsList.do")

    public JsonResult getDeleteContactsList(Integer id) {


        List<Map<String, Object>> list = supplierService.getContactsList(id, 0);

        return new JsonResult(1, list);
    }

    /*
     **
     * 添加邮寄地址
     */
    @ResponseBody
    @RequestMapping("/addSupplierAddress.do")
    public JsonResult addSupplierAddress(User user, Integer supplierId, String address) {

        String res = supplierService.addCustomerAddress(user, supplierId, address);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     * 修改地址
     */
    @ResponseBody
    @RequestMapping("/updateSupplierAddress.do")

    public JsonResult updateSupplierAddress(User user, SrmSupplierPost post) {
        String res = supplierService.updateSupplierAddress(user, post);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     * 获取暂停地址
     */
    @ResponseBody
    @RequestMapping("/getSuspendAddress.do")

    public JsonResult getSuspendAddress(Integer supplierId) {

        //根据获取客户所有地址
        List<Map<String, Object>> list = supplierService.getAllAddress(supplierId, "0");
        return new JsonResult(1, list);
    }

    /*
     **
     * 联系人删除
     */
    @ResponseBody
    @RequestMapping("/deleteCustomerContact.do")

    public JsonResult deleteCustomerContact(User user, Integer contactId) {
        String res = supplierService.deleteCustomerContact(contactId, user);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     *查看联系人社交号
     */
    @ResponseBody
    @RequestMapping("/getContactsSocial.do")
    public JsonResult getContactsSocial(Integer contactId) {


        Map<String, Object> data = supplierService.getContactsSocial(contactId);

        return new JsonResult(1, data);
    }


    /*
     **
     * 销售——客户管理——修改社交号与名片
     */
    @ResponseBody
    @RequestMapping("/updateContactsSocialAndCard.do")
    public JsonResult getContactCard(User user, Integer contactId, HttpServletRequest request) {
        String res = supplierService.updateContactsSocialAndCard(user, contactId, request);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }


    /*
     **
     * 销售——客户管理——新增联系人
     */
    @ResponseBody
    @RequestMapping("/addContact.do")

    public JsonResult addContact(User user, HttpServletRequest request, Integer supplierId) {
        Map res = supplierService.addCustomerContact(user, supplierId, request);

//        return new JsonResult(1, res);
        if ("success".equals(res.get("code"))) {
            return new JsonResult(1, String.valueOf(res.get("res")));
        } else {
            return new JsonResult(0, "", "400", String.valueOf(res.get("res")));
        }
    }

    /*
     **
     *<AUTHOR>
     * 获取联系人修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getRecordContactList.do")
    public JsonResult getRecordContactList(Integer id) {


        List<Map<String, Object>> list = supplierService.getRecordContactList(id);
        Map<String, Object> map = this.getRecord(list);
        return new JsonResult(1, map);
    }

    /*
     **
     *<AUTHOR>
     * 获取联系人修改记录详情
     */
    @ResponseBody
    @RequestMapping("/getRecordContactDetails.do")
    public JsonResult getRecordContactDetails(Integer id, Integer front) {
        Map<String, Object> data = supplierService.getRecordContactDetails(id, front);
        return new JsonResult(1, data);
    }

    /*
     **
     *<AUTHOR>
     * 销售——客户管理——获取收货地址修改记录详细信息
     */
    @ResponseBody
    @RequestMapping("/getRecordShAddressDetails.do")
    public JsonResult getRecordShAddressDetails(Integer id, Integer front) {
        Map<String, Object> data = supplierService.getRecordShAddressDetails(id, front);
        return new JsonResult(1, data);
    }

    /*
     **
     *<AUTHOR>
     * 销售——客户管理——获取收货,发票地址修改记录列表
     */
    @ResponseBody
    @RequestMapping("/getRecordAddressList.do")
    public JsonResult getRecordAddressList(Integer id) {


        List<Map<String, Object>> list = supplierService.getRecordAddressList(id);
        Map<String, Object> map = this.getRecord(list);

        return new JsonResult(1, map);
    }


    /*
     **
     * 销售——客户管理——停用与启用地址
     */
    @ResponseBody
    @RequestMapping("/startOrStopAddress.do")

    public JsonResult startOrStopAddress(User user, Integer addressId, int enabled) {


        String res = supplierService.startOrStopAddress(addressId, user, enabled);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /*
     **
     * 销售——客户管理——停用与启用地址
     */
    @ResponseBody
    @RequestMapping("/ttt.do")

    public JsonResult ttt(User user) {

        System.out.println("计算到期的合同开始");


        List<PoContractBase> list2 = supplierService.getEndList();


        for (PoContractBase po : list2) {
            po.setIsExpired(1);
        }

        supplierService.batchUpdateSlContractBase(list2);
        System.out.println("计算到期的合同结束");


        return new JsonResult(1, null);

    }


    private Map<String, Object> getRecord(List<Map<String, Object>> list) {
        Map map = new HashMap();
        if (list.size() == 1) {
            map.put("createName", list.get(0).get("createName"));
            map.put("createDate", list.get(0).get("createDate"));
            map.put("updateName", list.get(0).get("updateName"));
            map.put("updateDate", list.get(0).get("updateDate"));
            map.put("list", new ArrayList<>());
        } else if (list.size() > 1) {
            map.put("createName", list.get(list.size() - 1).get("createName"));
            map.put("createDate", list.get(list.size() - 1).get("createDate"));
            map.put("updateName", list.get(list.size() - 1).get("updateName"));
            map.put("updateDate", list.get(list.size() - 1).get("updateDate"));
            map.put("list", list);
        } else {
            map.put("createName", null);
            map.put("createDate", null);
            map.put("updateName", null);
            map.put("updateDate", null);
            map.put("list", list);
        }

        return map;
    }
}
