package cn.sphd.miners.modules.uploads.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.DigestUtils;
import cn.sphd.miners.common.utils.MinersHttpClientUtils;
import cn.sphd.miners.modules.uploads.dto.HeadMessage;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by 朱思旭 on 2019/12/10.
 */
//@Controller
@RequestMapping("/upload")
public class ResSeafileController {
    /**
     * seafile上传，超过2M时采用前端断点续传。
     * <AUTHOR>
     * @since 2019/12/26 16:34
     */
    HashMap<String,String> urls = new HashMap<>();
    /*@ResponseBody
    @RequestMapping("/uploadfyBykendo1.do")
    public String uploadfyBykendo(HttpSession session, @RequestParam("file") MultipartFile file, String metadata)throws Exception{
        String url= "http://seafile.btransm.com/api2/repos/fc61c1d9-6eb9-477d-8cae-ac7aada600b7/upload-link/?p=/文件";
        String urlnew = null;
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        JSONObject jsonObject = JSON.parseObject(metadata);
        String filename = jsonObject.getString("fileName");
        long filesize = jsonObject.getLong("totalFileSize");
        if(jsonObject.getLong("totalChunks")>1) {
            final long unitsize = 1024 * 1024 * 2;
            long begin = unitsize * jsonObject.getLong("chunkIndex");
            long end = Math.min(begin + unitsize, filesize)-1;
            head.addHeader("Content-Range"," bytes " + begin + "-" + end + "/" + filesize);
            head.addHeader("Content-Disposition", " attachment; filename=\"" + java.net.URLEncoder.encode(filename, "UTF8") + "\"");
            urlnew = urls.get(session.getId()+filename+filesize);//分块上传时不在重新申请上传url
        }
        if(urlnew==null) {
            String data = MinersHttpClientUtils.getSslJson(url, head);
            Pattern p1 = Pattern.compile("\"(.*?)\"");
            Matcher m = p1.matcher(data);
            while (m.find()) {
                urlnew = m.group().trim().replace("\"", "");
                if(jsonObject.getLong("totalChunks")>1) {
                    urls.put(session.getId() + filename + filesize, urlnew);
                }
            }
        }
        InputStream inputStream = file.getInputStream();
        MinersHttpClientUtils.uploadSsl(urlnew, inputStream, head, filename);
        Boolean uploadeds = jsonObject.getLong("totalChunks") - 1 <= jsonObject.getLong("chunkIndex");
        ObjectMapper mapper = new ObjectMapper();
        ChunkResultDto fileBlob = new ChunkResultDto();
        fileBlob.setUploaded(uploadeds);
        fileBlob.setFileUid(jsonObject.getString("uploadUid"));
        return mapper.writeValueAsString(fileBlob);
    }*/

    private HashMap splitFile(String path) throws IOException {
        FileInputStream fis = new FileInputStream(path);
        HashMap<String, Object> result = new HashMap<>();
        List<byte[]> bufs = new ArrayList<>();
        byte[] buf = new byte[1024 *1024 * 2];
        byte[] b;
        int len;
        long filesize = 0;
        while ((len = fis.read(buf)) > 0) {
            filesize+=len;
            b = new byte[len];
            System.arraycopy(buf,0,b,0,len);
            bufs.add(b);
        }
        fis.close();
        result.put("filesize", filesize);
        result.put("bufs",bufs);
        return  result;
    }

    /**
     * 测试多线程上传seafile
     * 例子：http://127.0.0.1:8080/res/uploadfy.do?filename=Raydrive%E9%80%9A%E8%AE%AF%E7%BB%84%E4%BB%B6%E6%95%B0%E6%8D%AE%E4%BA%A4%E4%BA%92%E8%A7%86%E5%9B%BE18.rar&path=F%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E6%A1%A3%5C%E6%96%AF%E6%99%AE%E4%BA%92%E5%8A%A8%E6%96%87%E6%A1%A3%5C%E5%B7%A5%E4%BD%9C%E6%96%87%E6%A1%A3
     * <AUTHOR>
     * @since 2019/12/26 16:22
     * @param path 本地文件路径（不带最后的斜杠），文件路径特殊符号转义地址 http://www.jsons.cn/urlencode/
     * @param filename 文件名
     * @return cn.sphd.miners.common.persistence.JsonResult
     */
    //多线程执行语句
    public Boolean uploadfyChunksFinished;
    @ResponseBody
    @RequestMapping("/uploadfy.do")
    public JsonResult uploadfy(String path, String filename) throws IOException {
        path= "D:";
        filename = "中文版.pdf";
        String result;
        String parent = "/文件";
        String url = getUploadUrl(filename, parent);
        HashMap splited= splitFile(path+File.separator+filename);
        long filesize = (long) splited.get("filesize");
        List<byte[]> bufs = (List<byte[]>) splited.get("bufs");
        System.out.println("splited count:"+bufs.size());
        if(bufs.size()>1) {
            //多线程执行语句
            ExecutorService es = Executors.newFixedThreadPool(Math.min(bufs.size(),5));//限制最多5个线程上传
            StringBuffer r = new StringBuffer();
            long begin, end=-1;
            uploadfyChunksFinished = false;
            for (int i = 0; i < bufs.size(); i++) {
                begin = end+1;
                byte[] b = bufs.get(i);
                end += b.length;
                //单线程轮流执行
//                r.append(uploadfyChunks(url, b,filename, parent+"/",begin,end, filesize));
                //多线程执行语句
                es.execute(new UpChunksThread(url, b, filename, parent + "/", begin, end, filesize, r, this));
            }
            //Begin 多线程执行语句
            es.shutdown();
            while (!uploadfyChunksFinished && !es.isTerminated()) {//上传完成或者上传线程全部退出。
                try {
                    Thread.sleep(TimeUnit.SECONDS.toMillis(3));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return null;
                }
            }
            //Endof 多线程执行语句
            result = r.toString();
        } else {
//            result = uploadfyChunks(url, bufs.get(0), filename, parent+"/", 0, filesize-1, filesize);
            result = uploadfy(url, bufs.get(0), filename, parent+"/");
        }
        return new JsonResult(result);
    }
    class UpChunksThread implements Runnable {
        String url, filename, parent;
        long begin, end, filesize;
        byte[] b;
        StringBuffer r;
        ResSeafileController controller;
        public UpChunksThread(String url, byte[] b, String filename, String parent, long begin, long end, long filesize, StringBuffer r, ResSeafileController controller) {
            this.url = url;
            this.b = b;
            this.filename = filename;
            this.parent = parent;
            this.begin = begin;
            this.end = end;
            this.filesize = filesize;
            this.r = r;
            this.controller = controller;
        }

        @Override
        public void run() {
            String result = this.controller.uploadfyChunks(url, b,filename, parent+"/",begin,end, filesize);
            r.append(result);
            if(DigestUtils.isSha1(result)) {//上传完成，返回文件id，40位字母数字字符串
                this.controller.uploadfyChunksFinished = true;
                System.out.println("上传完成");
            }
        }
    }

    public String getUploadUrl(String filename, String parent) {
        String url= "http://seafile.btransm.com/api2/repos/fc61c1d9-6eb9-477d-8cae-ac7aada600b7/upload-link/?p="+parent;
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        String data = MinersHttpClientUtils.getSslJson(url, head);
        Pattern p1 = Pattern.compile("\"(.*?)\"");
        Matcher m = p1.matcher(data);
        String urlnew = null;
        while(m.find()){
            urlnew = m.group().trim().replace("\"","");
        }
        return urlnew;
    }
    public String uploadfyGetBytes(String filename,String parent) {
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
//        head.setAccept("Accept: application/json; charset=utf-8; indent=4");
        String url= "http://seafile.btransm.com//api/v2.1/repos/fc61c1d9-6eb9-477d-8cae-ac7aada600b7/file-uploaded-bytes/?parent_dir="+parent+"&file_name="+filename;
        return MinersHttpClientUtils.getSslJson(url, head);
    }
    public String uploadfyChunks(String url, byte[] buf,String filename, String parent, long begin, long end, long filesize){
        HeadMessage head = new HeadMessage();
        head.setToken(" Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        head.addHeader("Content-Range"," bytes "+begin+"-"+end+"/"+filesize);
        try {
            head.addHeader("Content-Disposition", " attachment; filename=\""+java.net.URLEncoder.encode(filename, "UTF8")+"\"");
        } catch (UnsupportedEncodingException e) {
            head.addHeader("Content-Disposition", " attachment; filename=\""+filename+"\"");
            e.printStackTrace();
        }
        return MinersHttpClientUtils.uploadSslNew(url, buf, head, filename, parent);
    }
    public String uploadfy(String url, byte[] buf,String filename, String parent) {
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        return MinersHttpClientUtils.uploadSslNew(url, buf, head, filename,parent);
    }
    /** wyu:upload-blks方法服务器端返回错误{"error": "Internal error."}
     @ResponseBody
     @RequestMapping("/uploadfyblk.do")
     public JsonResult uploadfyblk(String path, String filename) throws IOException {
     String result;
     String parent = "/文件";
     HashMap splited= splitFile(path+File.separator+filename);
     int filesize = (int) splited.get("filesize");
     List<byte[]> bufs = (List<byte[]>) splited.get("bufs");
     if(bufs.size()>1) {
     ArrayList<String> blklist = new ArrayList();
     for (int i = 0; i < bufs.size(); i++) {
     blklist.add(UUID.randomUUID().toString().replaceAll("-", ""));
     }
     HashMap<String, Object> params = uploadfyBlkOne(String.join(",", blklist), parent);
     uploadfyBlkTwo((String) params.get("rawblksurl"), (List) params.get("blklist"), bufs);
     result = uploadfyBlkThree((String) params.get("commiturl"), filename, filesize, (List) params.get("blklist"), parent);
     } else {
     String url = getUploadUrl(filename, parent);
     result = uploadfy(url, bufs.get(0), filename, parent);
     }
     return new JsonResult(result);
     }
    public HashMap uploadfyBlkOne(String blklist, String parent) {
        String url= "http://seafile.btransm.com/api2/repos/fc61c1d9-6eb9-477d-8cae-ac7aada600b7/upload-blks-link/?p="+parent;
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        HashMap<String, String> map = new HashMap<>();
        map.put("blklist", blklist);
        String jsonObject = MinersHttpClientUtils.postSslJson(url, map, head);
        JSONObject json = JSON.parseObject(jsonObject);
        System.out.printf(jsonObject);
        HashMap<String, Object> result = new HashMap<>();
        result.put("rawblksurl",json.getString("rawblksurl"));
        result.put("commiturl",json.getString("commiturl"));
        result.put("blklist",json.getJSONArray("blklist").toJavaList(String.class));
        return result;
    }
    public String uploadfyBlkTwo(String url, List<String> blklist, List<byte[]> bufs){
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        int total = Math.min(blklist.size(),bufs.size());
        for (int i=0;i<total;i++) {
            MinersHttpClientUtils.uploadSslblk(url, bufs.get(i), head, blklist.get(i));
        }
        return null;
    }
    public String uploadfyBlkThree(String url, String filename, int filesize, List blklist, String parent){
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        head.setAccept("Accept: application/json; charset=utf-8; indent=4");
        HashMap<String, String> map = new HashMap<>();
        map.put("parent_dir", parent);
        map.put("file_name", filename);
        map.put("file_size", String.valueOf(filesize));
        map.put("replace", "1");
        map.put("blockids", JSON.toJSONString(blklist));
        return MinersHttpClientUtils.postSslJson(url, map, head);
    }
     **/

    @ResponseBody
    @RequestMapping("/getSeafileupload.do")
    public String getSeafileupload()throws Exception {
        String url= "http://seafile.btransm.com/api2/repos/fc61c1d9-6eb9-477d-8cae-ac7aada600b7/upload-link/?p=/文件";
        HeadMessage head = new HeadMessage();
        head.setToken("Token 7d0e92056df613e668aa54f1c3e4ce97452cdd21");
        String data = MinersHttpClientUtils.getSslJson(url, head);
        Pattern p1 = Pattern.compile("\"(.*?)\"");
        Matcher m = p1.matcher(data);
        String urlnew = null;
        while(m.find()){
            urlnew = m.group().trim().replace("\"","");
            System.out.println(urlnew);
        }
        return null;
    }



}
