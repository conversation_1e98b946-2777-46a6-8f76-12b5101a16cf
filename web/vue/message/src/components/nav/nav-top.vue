<template>
  <div class="navigation_wrapper" id="navigationBar">
    <div class="navigation_handle">
      <div class="buttonGroupLeft">
        <div class="nav_btn" v-on:click="reBack"><i class="arrow arrow-left"></i></div>
        <div class="nav_btn" v-on:click="backHome" v-if="isBackHome"><i class="close"></i></div>
        <slot name="left"></slot>
      </div>
      <div class="navigation_title">{{title}}</div>
      <div class="buttonGroupRight">
        <div class="nav_btn" v-on:click="toggleList" v-if="isSearch === 'true'"><img src="../../images/search.png"></div>
        <div class="nav_btn" v-on:click="toggleUpdate" v-if="isUpdate"><img src="../../images/edit.png"></div>
        <div class="nav_btn" v-on:click="toggleRecord" v-if="isRecord"><img src="../../images/record.png"></div>
        <div class="nav_btn" v-on:click="toggleSubmit" v-if="isButton">确定</div>
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  @green-blue   : #36c6d3;
  .navigation_wrapper{
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    display: block;
    height: 48px;
    line-height: 48px;
    background-color: @green-blue;
    text-align: center;
    z-index:999
  }
  .nav_btn {
    width: 32px;
    height: 100%;
    background-color: @green-blue;
    color: #fff;
    cursor: pointer;
  }
  .nav_btn:hover {
    background-color: darken(@green-blue, 5%);
  }
  .nav_btn img {
    width: 16px;
    margin-bottom: -3px;
  }
  .navigation_title{
    display: inline-block;
    font-size: 14px;
    color: #fff;
  }
  .navigation_handle{
    display: flex;
  }
  .navigation_handle .navigation_title{
    max-width: 180px;
  }
  .navigation_handle .buttonGroupLeft{
    flex: 1;
    display: flex;
    flex-direction: row;
  }
  .navigation_handle .buttonGroupRight{
    flex: 1;
    display: flex;
    flex-direction: row-reverse;
    .nav_btn{ margin-right:16px;  }
  }
</style>

<script>
import { gohistory } from '../../js/common'
export default {
  name: 'navigationBar',
  props: {
    title: {
      type: String,
      default: '提示'
    },
    isReBack: {
      type: Boolean,
      default: false
    },
    isSearch: {
      type: String,
      default: 'false'
    },
    isUpdate: {
      type: Boolean,
      default: false
    },
    isBackHome: {
      type: Boolean,
      default: true
    },
    isButton: {
      type: Boolean,
      default: false
    },
    isRecord: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    reBack: function () {
      console.log('isReBack', this.isReBack)
      if (!this.isReBack) {
        gohistory(this)
      } else {
        console.log('reback')
        this.$emit('reback')
      }
    },
    backHome: function () {
      this.$router.push({
        path: '/'
      })
      this.$store.dispatch('setMsgDisappear', false)
    },
    toggleList: function () {
      console.log(123)
      this.$emit('toggleList')
    },
    toggleUpdate: function () {
      this.$emit('toggleUpdate')
    },
    toggleSubmit: function () {
      this.$emit('toggleSubmit')
    },
    toggleRecord: function () {
      this.$emit('toggleRecord')
    }
  }
}
</script>
