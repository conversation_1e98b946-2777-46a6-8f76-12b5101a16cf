.bounce_Fixed2 {position: fixed;z-index: 10002;display: none; background: rgba(100, 100, 100, 0.5); width: 100%; padding-bottom: 200px; z-index: 10002;  }
.ty-table td.sec-ttl{text-align:left;}
.check-sign:hover{color:#fff; background-color:#5d9cec; transition: all .2s;border-radius: 3px;}
#invoiceCheck ul{margin-top:30px;}
#invoiceCheck ul:before,#invoiceCheck ul:after{display:block;  clear:both;  content:"";  visibility:hidden;  height:0}
#invoiceCheck ul li{float:left;width:32%;margin-right:2%;}
#invoiceCheck ul li:last-child{margin-right:0;}
.choosed{color:#fff; background-color:#5d9cec;border-radius:3px;}
.allElect p{float:left;width:50%;}
.bonceCon input[type="checkbox"]{padding-top:0;height:auto;line-height:inherit;}
#issueRegist{max-height:550px;overflow-y:auto;}
#issueRegist label{font-weight:100;}
#issueRegist table,#issueRegistUpdate table{margin-bottom:30px;}
.rowCl:before,.rowCl:after,.clear:before,.clear:after{display:block;clear:both;content:"";visibility:hidden;height:0;}
.rowTtl{float:left;margin-right:10px;min-width:150px;height:36px;line-height:36px;}
.rowTtl_d{float:left;margin-right:10px;min-width:56px;height:36px;line-height:36px;}
.rowCon select{width:120px;}
.rowCon{float:left;height:36px;line-height:36px;}
.rowSun{float: left;width:50%;}
.deliveType{float:left;width:500px;}
.long1{width:120px;}
.long3{width:152px;}
.long1,.long2{margin-right:20px;}
.long4{width:90%;}
.long4 input{width:100%;}
#issueRegist .fa,#issueRegistUpdate .fa,#signRegisterUpdate .fa,#signRegister .fa,#signedUpdateReason .fa{
    color: #48cfad;margin-right: 5px;font-size: 14px;display:inline-block;width:16px;}
.deliveRadio,.type-express{height:36px;line-height:34px;}
.deliveType span{width:240px;display:inline-block;}
.rowLine{padding:8px 0;}
.item-ttl{min-width:80px;margin-right:12px;display:inline-block;}
.rowInr{float:left;width:50%;}
.rowInr1{float:left;width: 33%;}
.rowLine:before,.rowLine:after{display:block;clear:both;content:"";visibility:hidden;height:0;}
.busNote{min-width:100px;margin-right:20px;}
.numNote{min-width:180px;margin-right:20px;}
.dateNote{min-width:180px;margin-right:20px;}
.notice{min-width:685px;}
.lookDetailTb{margin-top:20px;width:700px;}
.lookDetailTb tr td{padding:4px;text-align:center;line-height:30px;border:1px solid #d7d7d7;}
.ty-table .bg-flag td{background:red;}
.signOperate span{display: inline-block;width:200px;}
.bonceCon .otherInfo{width:80%;}
#invoicesCheck.ty-table thead td{border-color: #d7d7d7;}
#noSignUpdateRecord .flag,#signedUpdateRecordSee .flag,.resultInit td.flag{color:red;}
#noSignUpdateRecord .flagBg,#signedUpdateRecordSee .flagBg td,#signedUpdateRecordSee .flagBg{background:red;}
.signInforEntry{margin-top:20px;}
.posTop{vertical-align: top;}
.cells{border:1px solid #ddd;padding: 0 10px;display: inline-block;line-height: 28px;background: #fff;}
.editTogetherInfo:before,.editTogetherInfo:after,.togetherInfo:before,.togetherInfo:after{display:block;clear:both;content:"";visibility:hidden;height:0;}
.conCenter{text-align: center;}
#editOperate{padding:6px;color:#ccc;background-color: #e8f0f5;cursor:not-allowed;}
#editOperate .fa{color:#ccc;}
#noSigned thead td,#signRecord thead td{border: 1px solid #d7d7d7; }


