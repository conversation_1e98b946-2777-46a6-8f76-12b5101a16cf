import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
//export default defineConfig({
//  plugins: [
//    vue(),
//  ],
//  resolve: {
//    alias: {
//      '@': fileURLToPath(new URL('./src', import.meta.url))
//    }
//  }
//})
export default ({command, mode}) => {
  const envConfig = loadEnv(mode, './', ['VITE_', 'VUE_'])
  console.log('envConfig',envConfig)
  let config = {
    plugins: [
        vue(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    define: {
      'process.env': envConfig
    },
    base: './',
    publicPath: './',
    build: {
      chunkSizeWarningLimit: 65536,
      target: [ 'es2022' ]
    },
    performance: {
      hints: 'warning',
      maxEntrypointSize: 1048576,
      maxAssetSize: 33554432
    }
  }
  return defineConfig(config)
}
