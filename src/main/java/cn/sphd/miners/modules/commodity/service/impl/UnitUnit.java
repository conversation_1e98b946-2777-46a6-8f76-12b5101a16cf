package cn.sphd.miners.modules.commodity.service.impl;

import java.math.BigDecimal;

public class UnitUnit {

    //转毫克
    public  static  BigDecimal  toMilligram(BigDecimal amount,String type){

        if("1".equals(type)){
            return amount;
        }
        if("2".equals(type)){
            return amount.multiply(new BigDecimal(1000));
        }
        if("3".equals(type)){
            return amount.multiply(new BigDecimal(1000)).multiply(new BigDecimal(1000));
        }
        if("4".equals(type)){
            return amount.multiply(new BigDecimal(1000)).multiply(new BigDecimal(1000)).multiply(new BigDecimal(1000));
        }
        if(amount==null){
            return new BigDecimal(0);
        }
        return amount;
    }

    //转最优数量
    public  static  BigDecimal  milligramToBest(BigDecimal amount){

        if(amount.longValue()<1000){
            return amount;
        }
        if(amount.longValue()<1000000){
            return amount.divide(new BigDecimal(1000));
        }
        if(amount.longValue()<1000000000){
            return amount.divide(new BigDecimal(1000000));
        }
        return amount.divide(new BigDecimal(1000000000));
    }

    //获取最优单位
    public  static  String  milligramToUnit(BigDecimal amount){

        if(amount.longValue()<1000){
            return "1";
        }
        if(amount.longValue()<1000000){
            return "2";
        }
        if(amount.longValue()<1000000000){
            return "3";
        }
        return "4";
    }

    public  static  String  milligramToUnitName(BigDecimal amount){

        if(amount.longValue()<1000){
            return "毫克";
        }
        if(amount.longValue()<1000000){
            return "克";
        }
        if(amount.longValue()<1000000000){
            return "千克";
        }
        return "吨";
    }
}
