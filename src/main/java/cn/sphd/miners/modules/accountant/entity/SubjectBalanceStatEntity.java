package cn.sphd.miners.modules.accountant.entity;

import java.io.Serializable;
import java.math.BigDecimal;

/*
* 该类用于存放科目余额表的统计结果
* */
public class SubjectBalanceStatEntity implements Serializable {

    private BigDecimal sumBeginningBalanceCredit = BigDecimal.valueOf(0);//年初借
    private BigDecimal sumBeginningBalanceDebit = BigDecimal.valueOf(0);//年初贷
    private BigDecimal sumPreviousBalanceCredit = BigDecimal.valueOf(0);//期初借
    private BigDecimal sumPreviousBalanceDebit = BigDecimal.valueOf(0);//期初贷
    private BigDecimal sumCredit = BigDecimal.valueOf(0);//本期借
    private BigDecimal sumDebit = BigDecimal.valueOf(0);//本期贷
    private BigDecimal sumAccumulativeCredit = BigDecimal.valueOf(0);//本年累计借
    private BigDecimal sumAccumulativeDebit = BigDecimal.valueOf(0);//本年累计贷
    private BigDecimal sumBalanceCredit = BigDecimal.valueOf(0);//期末借
    private BigDecimal sumBalanceDebit = BigDecimal.valueOf(0);//期末贷

    public BigDecimal getSumBeginningBalanceCredit() {
        return sumBeginningBalanceCredit;
    }

    public void setSumBeginningBalanceCredit(BigDecimal sumBeginningBalanceCredit) {
        this.sumBeginningBalanceCredit = sumBeginningBalanceCredit;
    }

    public BigDecimal getSumBeginningBalanceDebit() {
        return sumBeginningBalanceDebit;
    }

    public void setSumBeginningBalanceDebit(BigDecimal sumBeginningBalanceDebit) {
        this.sumBeginningBalanceDebit = sumBeginningBalanceDebit;
    }

    public BigDecimal getSumPreviousBalanceCredit() {
        return sumPreviousBalanceCredit;
    }

    public void setSumPreviousBalanceCredit(BigDecimal sumPreviousBalanceCredit) {
        this.sumPreviousBalanceCredit = sumPreviousBalanceCredit;
    }

    public BigDecimal getSumPreviousBalanceDebit() {
        return sumPreviousBalanceDebit;
    }

    public void setSumPreviousBalanceDebit(BigDecimal sumPreviousBalanceDebit) {
        this.sumPreviousBalanceDebit = sumPreviousBalanceDebit;
    }

    public BigDecimal getSumCredit() {
        return sumCredit;
    }

    public void setSumCredit(BigDecimal sumCredit) {
        this.sumCredit = sumCredit;
    }

    public BigDecimal getSumDebit() {
        return sumDebit;
    }

    public void setSumDebit(BigDecimal sumDebit) {
        this.sumDebit = sumDebit;
    }

    public BigDecimal getSumAccumulativeCredit() {
        return sumAccumulativeCredit;
    }

    public void setSumAccumulativeCredit(BigDecimal sumAccumulativeCredit) {
        this.sumAccumulativeCredit = sumAccumulativeCredit;
    }

    public BigDecimal getSumAccumulativeDebit() {
        return sumAccumulativeDebit;
    }

    public void setSumAccumulativeDebit(BigDecimal sumAccumulativeDebit) {
        this.sumAccumulativeDebit = sumAccumulativeDebit;
    }

    public BigDecimal getSumBalanceCredit() {
        return sumBalanceCredit;
    }

    public void setSumBalanceCredit(BigDecimal sumBalanceCredit) {
        this.sumBalanceCredit = sumBalanceCredit;
    }

    public BigDecimal getSumBalanceDebit() {
        return sumBalanceDebit;
    }

    public void setSumBalanceDebit(BigDecimal sumBalanceDebit) {
        this.sumBalanceDebit = sumBalanceDebit;
    }
}
