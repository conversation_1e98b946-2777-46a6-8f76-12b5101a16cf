<template>
  <div>
    <div class="head">
      <div>
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>
    <div>
      <h3>修改记录</h3>
      <ul class="xiangUlB padding1015">
        <li v-if="list.length > 0" >当前资料为第{{ list.length - 1 }}次修改后的结果。</li>
        <li v-if="list.length > 0" >修改人 {{ listLast.createName }} {{ new Date(listLast.createDate).format('yyyy-MM-dd hh:mm:ss') }}</li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="repItem "  v-if="list.length > 0" v-for="(item,index) in list" :key="index">
        <li @click="goEditLogDetails(item,index)"><span class="greenTxt">{{ index === 0 ? '原始信息' : '第'+ index + '修改后'  }}</span> </li>
        <li class="endLi">{{ index === 0 ? '创建人' : '修改人'  }} {{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</li>
      </ul>
      <div class="hr_gray"></div>
    </div>

  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "loanEditList",
  data() {
    return {
      list: [],
      listLast: [],
    }
  },
  mixins: [finance],
  mounted() {
    let loanEditList = JSON.parse(localStorage.getItem('loanEditList'))
    console.log('loanEditList=', loanEditList)
    this.list = loanEditList
    this.listLast = loanEditList[loanEditList.length - 1]
  },
  methods: {
    goEditLogDetails(item, index){
      item.indexCC = index
      localStorage.setItem('loanEditDetail', JSON.stringify(item))
      this.$router.push("loanEditDetail")
    },

  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
h3{
  text-align: center; margin:20px auto;
}

</style>