package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdRankHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdRank;
import cn.sphd.miners.modules.commodity.entity.PdRankHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PdRankHistoryDaoImpl  extends BaseDao<PdRankHistory,Serializable> implements PdRankHistoryDao {
    @Override
    public PdRankHistory insert(PdRank rank) {
        PdRankHistory history=new PdRankHistory();
        BeanUtils.copyProperties(rank,history);
        history.setId(null);
        history.setRank(rank.getId());
        save(history);

        getSession().flush();

        return history;
    }
}
