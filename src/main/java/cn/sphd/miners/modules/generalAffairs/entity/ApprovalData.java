package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-01-09 
 */

@Entity ( name ="ApprovalData" )
@Table ( name ="t_sys_approval_data" )
public class ApprovalData  implements Serializable {

	private static final long serialVersionUID =  1734311596753980716L;

	@Id
	@Column(name="id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	/**
	 * org
	 */
	@Column(name = "org" )
	private Integer org;

	/**
	 * 审批实例ID
	 */
   	@Column(name = "instance" )
	private Integer instance;

	/**
	 * 申请标题
	 */
   	@Column(name = "title" )
	private String title;

	/**
	 * 申批项目
	 */
   	@Column(name = "item" )
	private Integer item;

	/**
	 * 表代号
	 */
	@Column(name = "table_code" )
	private String tableCode;

	/**
	 * 表名
	 */
   	@Column(name = "table_name" )
	private String tableName;

	/**
	 * 所在行的ID(用于处理多行数据)
	 */
   	@Column(name = "row_id" )
	private Integer rowId;

	/**
	 * 行名
	 */
   	@Column(name = "row_name" )
	private String rowName;
	/**
	 * 列代号
	 */
	@Column(name = "col_code" )
	private String colCode;

	/**
	 * 列名
	 */
   	@Column(name = "col_name" )
	private String colName;

	/**
	 * 数据类型:1-数字,2-字符,3-日期,4-时间,5-货币,6-json
	 */
   	@Column(name = "data_type" )
	private String dataType;

	/**
	 * 原值
	 */
   	@Column(name = "old_value" )
	private String oldValue;

	/**
	 * 新值
	 */
   	@Column(name = "new_value" )
	private String newValue;

	/**
	 * 所进行的操作:1-增,2-改,3-删
	 */
   	@Column(name = "type" )
	private String type;

	/**
	 * 描述
	 */
   	@Column(name = "description" )
	private String description;
	/**
	 * 备注
	 */
	@Column(name = "memo" )
	private String memo;

	/**
	 * 审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核,4-待处理
	 */
   	@Column(name = "approve_status" )
	private String approveStatus;

	/**
	 * 申请备注
	 */
   	@Column(name = "approval_memo" )
	private String approvalMemo;

	/**
	 * 审批备注
	 */
   	@Column(name = "approve_memo" )
	private String approveMemo;

	/**
	 * 对应消息表的id
	 */
   	@Column(name = "message_id" )
	private Integer messageId;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	public Integer getId() {
		return this.id;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public String getTableCode() {
		return tableCode;
	}

	public void setTableCode(String tableCode) {
		this.tableCode = tableCode;
	}

	public String getColCode() {
		return colCode;
	}

	public void setColCode(String colCode) {
		this.colCode = colCode;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getInstance() {
		return this.instance;
	}

	public void setInstance(Integer instance) {
		this.instance = instance;
	}

	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getItem() {
		return this.item;
	}

	public void setItem(Integer item) {
		this.item = item;
	}

	public String getTableName() {
		return this.tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public Integer getRowId() {
		return this.rowId;
	}

	public void setRowId(Integer rowId) {
		this.rowId = rowId;
	}

	public String getRowName() {
		return this.rowName;
	}

	public void setRowName(String rowName) {
		this.rowName = rowName;
	}

	public String getColName() {
		return this.colName;
	}

	public void setColName(String colName) {
		this.colName = colName;
	}

	public String getDataType() {
		return this.dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getOldValue() {
		return this.oldValue;
	}

	public void setOldValue(String oldValue) {
		this.oldValue = oldValue;
	}

	public String getNewValue() {
		return this.newValue;
	}

	public void setNewValue(String newValue) {
		this.newValue = newValue;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDescription() {
		return this.description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getApproveStatus() {
		return this.approveStatus;
	}

	public void setApproveStatus(String approveStatus) {
		this.approveStatus = approveStatus;
	}

	public String getApprovalMemo() {
		return this.approvalMemo;
	}

	public void setApprovalMemo(String approvalMemo) {
		this.approvalMemo = approvalMemo;
	}

	public String getApproveMemo() {
		return this.approveMemo;
	}

	public void setApproveMemo(String approveMemo) {
		this.approveMemo = approveMemo;
	}

	public Integer getMessageId() {
		return this.messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
