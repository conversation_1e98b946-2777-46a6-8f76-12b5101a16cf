package cn.sphd.miners.modules.inStockCheck.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class TMtStockFlowSetting {
    /**
    * ID
    */
    private Integer id;

    /**
    * 机构ID
    */
    private Integer org;

    /**
    * 类别:in-入库
    */
    private String category;

    /**
    * 键值:0-原有流程,1-库管检查,2-检验检查,3-两者都要检查,4-不需要检查
    */
    private Byte key;

    /**
    * 对应审批项目(t_sys_approval_item的ID)
    */
    private String value;

    /**
    * 是否有效,false-无效
    */
    private Boolean enable;

    /**
    * 生效日期
    */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectDate;

    /**
    * 执行状态:0-未执行,1-已执行
    */
    private Byte effectState;

    /**
    * 备注
    */
    private String memo;

    /**
    * 创建人id
    */
    private Integer creator;

    /**
    * 创建人
    */
    private String createName;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 修改人id
    */
    private Integer updator;

    /**
    * 修改人
    */
    private String updateName;

    /**
    * 修改时间
    */
    private Date updateDate;

    /**
    * 操作:1-增,2-删,3-修改
    */
    private Byte operation;

    /**
    * 修改前记录ID
    */
    private Integer previousId;

    /**
    * 版本号,每次修改+1
    */
    private Integer versionNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Byte getKey() {
        return key;
    }

    public void setKey(Byte key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Date getEffectDate() {
        return effectDate;
    }

    public void setEffectDate(Date effectDate) {
        this.effectDate = effectDate;
    }

    public Byte getEffectState() {
        return effectState;
    }

    public void setEffectState(Byte effectState) {
        this.effectState = effectState;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}