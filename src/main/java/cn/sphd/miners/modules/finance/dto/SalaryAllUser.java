package cn.sphd.miners.modules.finance.dto;

import java.util.List;

/**
 * @ClassName SalaryAllUser
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/21 8:34
 * @Version 1.0
 */
public class SalaryAllUser {
    private Integer resignationSum;//辞职人员总数
    private Integer userSum;//在职人员总数
    private List<SalaryByUser> userList;//在职人员列表
    private List<SalaryByUser> resignationList;//离职人员列表

    public Integer getResignationSum() {
        return resignationSum;
    }

    public void setResignationSum(Integer resignationSum) {
        this.resignationSum = resignationSum;
    }

    public Integer getUserSum() {
        return userSum;
    }

    public void setUserSum(Integer userSum) {
        this.userSum = userSum;
    }

    public List<SalaryByUser> getUserList() {
        return userList;
    }

    public void setUserList(List<SalaryByUser> userList) {
        this.userList = userList;
    }

    public List<SalaryByUser> getResignationList() {
        return resignationList;
    }

    public void setResignationList(List<SalaryByUser> resignationList) {
        this.resignationList = resignationList;
    }
}
