<template id="login">
  <div class="login">
    <van-nav-bar
      title="登录"
      left-arrow
      @click-left="onClickLeft"
    />
    <div class="login_container">
      <div class="login_icon">
        <van-image
          width="60px"
          height="60px"
          fit="cover"
          position="center"
          :src="img"
        />
      </div>
      <div class="login_title">
        登录Wonderss账号
      </div>
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-model="username"
            name="用户名"
            label="用户名"
            placeholder="请输入用户名"
            :rules="[{ required: true, message: '请输入用户名' }]"
          />
          <van-field
            v-model="password"
            type="password"
            name="密码"
            label="密码"
            placeholder="密码"
            :rules="[{ required: true, message: '请填写密码' }]"
          />
        </van-cell-group>
        <div style="margin: 16px;">
          <van-button block type="primary" native-type="submit" color="#3cb0a7">
            登录
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<style>
html{
  background: #f2f2f2;
  height: 100%;
}
body{
  padding:0;
  margin: 0;
}
.login_title{
  margin: 20px;
  text-align: center;
}
.login_icon{
  text-align: center;
  margin: 32px 0 20px 0;
}
.agreeXieyi{
  font-size: 14px;
  margin: 8px 0;
}
.agreeXieyi .color-blue{
  color: #1989fa;
}
.van-checkbox__icon .van-icon {
  background: #fff;
}
</style>

<script>
// wyu：引相关的库
import img from '../assets/wonderss_logo.png'
export default {
  name: 'resumeRecord',
  data () {
    return {
      username: '',
      password: '',
      img: img,
      checked: false
    }
  },
  methods: {
    onSubmit () {
      this.$router.push({
        path: 'home',
        name: 'home'
      })
    },
    onClickLeft () {
      this.$router.go(-1)
    }
  }
}
</script>
