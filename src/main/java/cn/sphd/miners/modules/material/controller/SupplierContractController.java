package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.material.dto.MaterialContractDto;
import cn.sphd.miners.modules.material.dto.SupplierContractDto;
import cn.sphd.miners.modules.material.entity.PoContractBase;
import cn.sphd.miners.modules.material.entity.PoContractBaseHistory;
import cn.sphd.miners.modules.material.service.PoContractNewService;
import cn.sphd.miners.modules.system.entity.LocalSetting;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/supplier")
public class SupplierContractController {

    @Autowired
    PoContractNewService poContractNewService;

    //新增合同
    @ResponseBody
    @RequestMapping("/insertSupplierContract.do")
    public JsonResult insertSupplierContract(User user, PoContractBase poContractBase, String contractSignTime, String contractStartTime,
                                             String contractEndTime, String contractBaseImages, String mtList) {
        Map<String, Object> map = new HashMap<String, Object>();
        PoContractBase contract = poContractNewService.insertSupplierCon(user,poContractBase,contractSignTime,contractStartTime,contractEndTime,contractBaseImages,mtList);
        map.put("state", 1);
        map.put("contract", contract);
        return new JsonResult(1,map);
    }

    //修改合同
    @ResponseBody
    @RequestMapping("/updateSupplierContract.do")
    public JsonResult updateSupplierContract(User user, PoContractBase poContractBase, String contractSignTime,
                                             String contractStartTime, String contractEndTime, String contractBaseImages,
                                             String mtList){
        Map<String, Object> map = new HashMap<>();
        Integer state = poContractNewService.upSupplierCon(user,poContractBase,contractSignTime,contractStartTime,contractEndTime,contractBaseImages,mtList);
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //续约合同
    @ResponseBody
    @RequestMapping("/renewalSupplierContract.do")
    public JsonResult renewalSupplierContract(User user, PoContractBase poContractBase, String contractSignTime,
                                              String contractStartTime, String contractEndTime, String contractBaseImages,
                                              String mtList, String type){
        Map<String, Object> map = new HashMap<>();
        Integer state = poContractNewService.renewalPoContract(user,poContractBase,contractSignTime,contractStartTime,contractEndTime,contractBaseImages,mtList,type);
        map.put("state", state);
        return new JsonResult(1, map);
    }


    //终止合同
    @ResponseBody
    @RequestMapping("/terminateContract.do")
    public JsonResult terminateContract(Integer id) {
        Map<String, Object> map = new HashMap<>();
        Integer state = poContractNewService.terminatePoContract(id);
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //启用合同时判断合同的状态
    @ResponseBody
    @RequestMapping("/checkReStartPoContractState.do")
    public JsonResult checkReStartPoContractState(Integer id) {
        Map<String, Object> map = poContractNewService.checkReStartPoContractState(id);
        return new JsonResult(1, map);
    }

    //恢复终止合同 1-恢复到正在用的 2-恢复到过期合同
    @ResponseBody
    @RequestMapping("/reStartPoContract.do")
    public JsonResult reStartPoContract(Integer id, String type) {
        Map<String, Object> map = new HashMap<>();
        Integer state = poContractNewService.reStartPoContract(id, type);
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //添加材料列表
    @ResponseBody
    @RequestMapping("/getContractMt.do")
    public JsonResult getContractMt(User user, Integer supplier) {
        List<MaterialContractDto> list = poContractNewService.getMaterialContract(user, "1", supplier, null);
        HashMap<String, Object> map = new HashMap<>();
        map.put("mtList",list);
        return new JsonResult(1, map);
    }

    //获取三种合同 1-正常 2-终止 3-到期
    @ResponseBody
    @RequestMapping("/listContractBase.do")
    public JsonResult getPolistContract(Integer supplierId, String type){
        Map<String, Object> map = new HashMap<>();
        List<PoContractBase> list = poContractNewService.listPoContract(supplierId,type,null,null);
        map.put("contractBaseList", list);
        return new JsonResult(1, map);
    }

    //获取合同的修改记录
    @ResponseBody
    @RequestMapping("/poContractBaseHistory.do")
    public JsonResult poContractBaseHistory(Integer id) {
        List<PoContractBaseHistory> list = poContractNewService.getListPoContractHisByoperation(id, "3");
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //获取某个合同的全部签约记录
    @ResponseBody
    @RequestMapping("/poContractSignRecord.do")
    public JsonResult poContractSignRecord(Integer primaryId) {
        List<PoContractBase> list = poContractNewService.getPoContractSignRecord(primaryId);
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //获取合同详情
    @ResponseBody
    @RequestMapping("/poContractBaseMes.do")
    public JsonResult poContractBaseMes(User user, Integer id) {
        Map<String, Object> map = poContractNewService.getPoContractMes(user, id);
        return new JsonResult(1, map);
    }

    //获取历史合同详情
    @ResponseBody
    @RequestMapping("/poContractBaseHisMes.do")
    public JsonResult poContractBaseHisMes(User user, Integer contractHisId) {
        Map<String, Object> map = poContractNewService.getPoContractHisMes(user, contractHisId);
        return new JsonResult(1, map);
    }

    //获取全部供应商
    @ResponseBody
    @RequestMapping("/getSupplierByAddContract.do")
    public JsonResult getSupplierByAddContract(User user) {
        List<SupplierContractDto> list = poContractNewService.getAllSupplierForContract(user);
        Map<String, Object> map = new HashMap<>();
        map.put("list",list);
        return new JsonResult(1, map);
    }

    //获取采购合同
    @ResponseBody
    @RequestMapping("/getProcurementContractList.do")
    public JsonResult getProcurementContractList(User user) {
        List<SupplierContractDto> list = poContractNewService.procurementContractList(user);
        Map<String, Object> map = new HashMap<>();
        map.put("list",list);
        return new JsonResult(1, map);
    }

    //获取全部合同
    @ResponseBody
    @RequestMapping("/getListByAllSupplierContract.do")
    public JsonResult getListByAllSupplierContract(User user, String type) {
        List<PoContractBase> list = poContractNewService.listAllPoContract(user, type);
        Map<String, Object> map = new HashMap<>();
        map.put("list",list);
        return new JsonResult(1, map);
    }

    //新增合同的提示（1-采购 2-销售）
    @ResponseBody
    @RequestMapping("/getContractRemind.do")
    public JsonResult getContractRemind(User user, String type) {
        LocalSetting localSetting = poContractNewService.getContractRemindByType(user,type);
        Map<String, Object> map = new HashMap<>();
        if (localSetting.isEnabled()) {
            map.put("state", 1);   //需要提醒
        }else {
            map.put("state", 2);  //不需提醒
        }
        return new JsonResult(1, map);
    }

    //修改新增合同的提示（1-采购 2-销售）
    @ResponseBody
    @RequestMapping("/updateContractRemind.do")
    public JsonResult updateContractRemind(User user, String type) {
        poContractNewService.upContractRemind(user,type);
        Map<String, Object> map = new HashMap<>();
        map.put("state", 1);   //成功
        return new JsonResult(1, map);
    }

}
