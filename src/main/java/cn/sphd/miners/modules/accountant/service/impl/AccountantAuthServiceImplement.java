package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.service.AccountantAuthService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.RoleService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by root on 17-1-4.
 */
@Service
@Transactional(readOnly=false)
public class AccountantAuthServiceImplement implements AccountantAuthService {

    @Autowired
    RoleService roleService;

    @Override
    public JSONObject auth(User user, String nameJsp) {
//        JSONArray roleArr = GetPersonalPrivateAuthUtil.getPersonalPrivateAuth(session,roleService);
        JSONObject auth = new JSONObject();
//        auth.append("auth" ,roleArr ) ;
        switch (nameJsp){
            case "subjectSet":
                auth.append("type" ,"subjectSet" ) ;
                break;
            case "subjectChoose":
                auth.append("type" ,"subjectChoose" ) ;
                break;
            case "voucherManage":
                auth.append("type" ,"voucherManage" ) ;
                break;
            case "accountBook":
                auth.append("type" ,"accountBook" ) ;
                break;
            case "contraryReports":
                auth.append("type" ,"contraryReports" ) ;
                break;
            case "accountantImport":
                auth.append("type" ,"accountantImport" ) ;
                break;
            case "authority":
                auth.append("type" ,"authority" ) ;
                break;
        }

        return auth;
    }
}
