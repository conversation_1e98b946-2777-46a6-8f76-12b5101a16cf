package cn.sphd.miners.modules.orderReview.service.impl;

import java.util.List;
import java.util.Map;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReview;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersReview;
import cn.sphd.miners.modules.orderReview.mapper.TSlOrdersReviewMapper;
import cn.sphd.miners.modules.orderReview.service.ITSlOrdersReviewService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
@Transactional
public class TSlOrdersReviewServiceImpl implements ITSlOrdersReviewService
{
    @Autowired
    private TSlOrdersReviewMapper tSlOrdersReviewMapper;

    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TSlOrdersReview selectTSlOrdersReviewById(Long id)
    {
        return tSlOrdersReviewMapper.selectTSlOrdersReviewById(id);
    }

    /**
     * 查询列表
     * 
     * @param tSlOrdersReview 
     * @return 
     */
    @Override
    public List<TSlOrdersReview> selectTSlOrdersReviewList(TSlOrdersReview tSlOrdersReview)
    {
        return tSlOrdersReviewMapper.selectTSlOrdersReviewList(tSlOrdersReview);
    }

    /**
     * 新增
     * 
     * @param tSlOrdersReview 
     * @return 结果
     */
    @Override
    public int insertTSlOrdersReview(TSlOrdersReview tSlOrdersReview)
    {
        return tSlOrdersReviewMapper.insertTSlOrdersReview(tSlOrdersReview);
    }

    /**
     * 修改
     * 
     * @param tSlOrdersReview 
     * @return 结果
     */
    @Override
    public int updateTSlOrdersReview(TSlOrdersReview tSlOrdersReview)
    {
        return tSlOrdersReviewMapper.updateTSlOrdersReview(tSlOrdersReview);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTSlOrdersReviewByIds(Long[] ids)
    {
        return tSlOrdersReviewMapper.deleteTSlOrdersReviewByIds(ids);
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTSlOrdersReviewById(Long id)
    {
        return tSlOrdersReviewMapper.deleteTSlOrdersReviewById(id);
    }

    @Override
    public List<Map<String, Object>> selectTSlOrdersReviewListByCondition(TSlOrdersItemReview ordersItemReview, PageInfo pageInfo) {
        return tSlOrdersReviewMapper.selectTSlOrdersReviewListByCondition(ordersItemReview);
    }

    @Override
    public TSlOrdersReview selectTSlOrdersReviewByOrderId(Long orders) {
        return tSlOrdersReviewMapper.selectTSlOrdersReviewByOrderId(orders);
    }
}
