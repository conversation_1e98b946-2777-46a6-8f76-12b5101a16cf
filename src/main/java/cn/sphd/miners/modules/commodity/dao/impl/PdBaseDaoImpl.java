package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.service.impl.UnitUnit;
import org.hibernate.Criteria;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/10/12.
 */
@Repository
public class PdBaseDaoImpl extends BaseDao<PdBase,Serializable> implements PdBaseDao{

    @Override
    public void updateUnitAndProcess(Integer id, BigDecimal netWeight, String weightUnit, String process) {
        Map<String, Object> map = new HashMap<>();
        String hql="update PdBase set process=:process,state='2',stage='2' ";
        if(netWeight!=null&&weightUnit!=null&&!"null".equals(weightUnit)){
            BigDecimal a=UnitUnit.toMilligram(netWeight,weightUnit);
            hql=hql+"  ,netWeight=:netWeight ";
            map.put("netWeight",UnitUnit.milligramToBest(a));

            hql=hql+" ,weightUnit=:weightUnit ";
            map.put("weightUnit",UnitUnit.milligramToUnit(a));
        }

         hql=hql+" where id=:id";

        map.put("process",process);
        map.put("id",id);
        this.queryHQLWithNamedParams(hql,map);
    }

    @Override
    public void updateUnitAndComposition(Integer id, BigDecimal netWeight, String weightUnit, String composition,String stage) {
        Map<String, Object> map = new HashMap<>();
        String split="0";
        String hql="update PdBase set composition=:composition,stage=:stage,split=:split ";
        if("3".equals(stage)){
            split="1";
        }
        if(netWeight!=null&&weightUnit!=null&&!"null".equals(weightUnit)){
            BigDecimal a=UnitUnit.toMilligram(netWeight,weightUnit);
            hql=hql+"  ,netWeight=:netWeight ";
            map.put("netWeight", UnitUnit.milligramToBest(a));

            hql=hql+" ,weightUnit=:weightUnit ";
            map.put("weightUnit",UnitUnit.milligramToUnit(a));
        }


         hql=hql+" where id=:id";

        map.put("composition",composition);
        map.put("stage",stage);
        map.put("split",split);
        map.put("id",id);
        this.queryHQLWithNamedParams(hql,map);
    }

    @Override
    public Long directComposition(Integer oid,Integer pdBaseId, String type) {
        String hql="SELECT count(p.id)  from  PdBase p where p.oid=:oid and  p.type=:type  and p.id in ( SELECT b.id  from  PdBase b where b.json is not null and  b.path like CONCAT('%1:',"+pdBaseId+",'%') )";
        Map<String, Object> map = new HashMap<>();
        map.put("oid",oid);
        map.put("type",type);
        return (Long) getByHQLWithNamedParams(hql,map);
    }

    @Override
    public Long indirectPartProduct(Integer oid, Integer pdBaseId) {
        String hql="SELECT count(p.id)  from  PdBase p where p.oid=:oid and " +
                "p.type='1' and p.id in ( SELECT b.id  from  PdBase b where b.json is not null and b.path like CONCAT('%:',"+pdBaseId+",'%') ) AND " +
                "p.id not in ( SELECT b.id  from  PdBase b where  b.path like CONCAT('%1:',"+pdBaseId+",'%') )";
        Map<String, Object> map = new HashMap<>();
        map.put("oid",oid);
        return (Long) getByHQLWithNamedParams(hql,map);
    }

    @Override
    public Long formerdirectPartComposition(Integer oid, Integer pdBaseId, String type) {
        String sql = "select * from (SELECT  " +
                "p.product as product,"+
                "p.inner_sn as innerSn," +
                "p.name AS name," +
                "p.model AS model," +
                "p.specifications AS specifications," +
                "p.json as json " +
                "FROM\n" +
                "t_pd_base_history p \n" +
                "WHERE\n" +
                "p.type="+type+" and  p.json is not null and p.path like CONCAT('%1:'," + pdBaseId + ",'%') order by p.id desc ) s group by s.product ";

        List<Map<String, Object>> list=getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
        return Long.valueOf(list.size());
    }

    @Override
    public Long formerIndirectPartProduct(Integer oid, Integer pdBaseId) {
        String sql = "select * from (SELECT  "+
                "p.product as product,"+
                "p.inner_sn as innerSn," +
                "p.name AS name," +
                "p.model AS model," +
                "p.specifications AS specifications," +
                "p.json as json "+
                "FROM\n" +
                "t_pd_base_history p\n"+
                "WHERE\n" +
                "p.type='1' and   p.json is not null and p.path like CONCAT('%:',"+pdBaseId+",'%')  AND " +
                "p.product not in ( SELECT b.product  from  t_pd_base_history b where  b.path like CONCAT('%1:',"+pdBaseId+",'%') )) s group by s.product";
        Map<String, Object> map = new HashMap<>();
        map.put("type","1");
        List<Map<String, Object>> list=  getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
        return Long.valueOf(list.size());
    }


}
