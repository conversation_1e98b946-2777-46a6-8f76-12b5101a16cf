package cn.sphd.miners.modules.accountantReportTax.dto;

import java.io.Serializable;
import java.util.Date;

public class PayTaxDto implements Serializable {
    private static final long serialVersionUID = -8896243397559706L;

    private Date payTime;//实际缴纳日期
    private Date editTime;//编辑日期
    private String editorName;//编辑人

    public PayTaxDto(){}

    public PayTaxDto(Date payTime,Date updateDate,String updateName){
        this.payTime = payTime;
        this.editTime = updateDate;
        this.editorName = updateName;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getEditTime() {
        return editTime;
    }

    public void setEditTime(Date editTime) {
        this.editTime = editTime;
    }

    public String getEditorName() {
        return editorName;
    }

    public void setEditorName(String editorName) {
        this.editorName = editorName;
    }
}
