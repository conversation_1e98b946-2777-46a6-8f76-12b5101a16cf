package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.schedule.entity.MemoJob;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleSupplementImage;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by Administrator on 2019/1/24.
 */
public interface MemoScheduleService {

    Integer saveMemoSchedule(MemoSchedule memoSchedule, User user, String source, String[] imagePaths);

    void updateMemoSchedule(MemoSchedule memoSchedule);

    MemoSchedule getMemoScheduleById(Integer id);

    List<MemoSchedule> getMemoScheduleListByUserId(Integer userId, PageInfo pageInfo,String keyWord);

    void addMemoScheduleRemind(Integer id);

    void overdueMemoJobSchedule(Integer id,Date updateDate);

    List<MemoSchedule> getMemoScheduleListByDate(Integer userId,Date begin,Date end);

    List<MemoSchedule> keywordQuerySchedule(Integer userId,String keyword,String description,String supplement,String place,PageInfo pageInfo);

    void scheduleNoRemind(Integer id);

    void handleSchedule(MemoSchedule memoSchedule,String type,Date remindDate);

    void scheduleSettleDay(String code);

    Integer getMemoScheduleCounts(Integer userId);

    List<MemoSchedule> getMemoListByUserId(Integer userId,PageInfo pageInfo);// 获取不需要提醒的日程列表

    void  updateMemoSchedule(MemoSchedule memoSchedule,User user,String[] imagePaths);//修改日程带附件

    List<MemoSchedule> getMemoScheduleListByKeyword(Integer userId,String keyword,PageInfo pageInfo);

    void deleteMemoSchedule(MemoSchedule memoSchedule);

    void deleteMemoSchedule(Integer id,User user);

    void delMemoScheduleSupplementImagFileUsing(Set<MemoScheduleSupplementImage> memoScheduleSupplementImageHashSet, User user);

}
