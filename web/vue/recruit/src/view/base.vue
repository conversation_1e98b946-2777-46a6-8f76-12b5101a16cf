<template id="base">
  <div class="base">
    <van-loading size="24px" vertical>加载中...</van-loading>
  </div>
</template>
<script>
// wyu：引相关的库
import JsEncrypt from 'jsencrypt/bin/jsencrypt'
import sha1 from 'js-sha1'
export default {
  created () {
    // this.$toast.loading({
    //   message: 'Loading...',
    //   forbidClick: true,
    //   loadingType: 'spinner'
    // })
    if (typeof (this.$route.params.item) === 'undefined') {
      this.jumpError('二维码错误，没有参数！')
    }
    this.$http({}).then((response) => { // wyu: 获取服务器时间
      this.checkParams(new Date(response.headers.get('Date')))
    }, (response) => {
      console.log(response)
      this.jumpError('服务器无法访问！')
    })
  },
  methods: {
    jumpError (message) {
      this.$toast.clear()
      this.$router.replace({name: 'error', params: {message: message}})
    },
    checkParams (now) {
      let data = this.decrypt(decodeURIComponent(this.$route.params.item))
      let items = JSON.parse(data)
      try {
        let salt = sha1('cRDZsFXS' + items.userId + items.oid + items.expire_date)
        console.log(salt)
        console.log(items.salt)
        if (salt !== items.salt) {
          this.jumpError('二维码错误，参数不正确！')
        } else if (now >= new Date(items.expire_date)) {
          this.jumpError('二维码已过期！')
        } else {
          // console.log('userId', items.userId)
          // console.log('oid', items.oid)
          this.jumpNext(items)
        }
      } catch (e) {
        this.jumpError('二维码错误，参数错误！')
      }
    },
    jumpNext (items) {
      console.log('window.parent.$', window.parent.$)
      // wyu：设置系统路径
      // if (window.location.href.indexOf('/vue/') > 0) {
      //   if (typeof window.parent.$ === 'undefined') {
      //     window.parent.$ = {}
      //   }
      //   window.parent.$.webRoot = window.location.href.substring(0, window.location.href.indexOf('/vue/'))
      //   this.$http.get(window.parent.$.webRoot + '/uploads/getRootPath.do', {params: {userId: userId}}).then((response) => {
      //     window.parent.$.webRoot = response.data.webRoot
      //     window.parent.$.fileUrl = response.data.fileUrl
      //     window.parent.$.uploadUrl = response.data.uploadUrl
      //     window.parent.$.ow365url = response.data.ow365url
      //     this.$toast.clear()
      //     this.$router.replace({name: 'home', params: {userId: userId, oid: oid}})
      //   }, (response) => {
      //     console.log('请求失败！', response)
      //     this.jumpError('服务器请求失败！')
      //   })
      // } else {
      //   this.jumpError('服务器路径错误！')
      // }
      this.$http.get(window.parent.$.webRoot + '/uploads/getRootPath.do', {params: {userId: items.userId}}).then((response) => {
        window.parent.$.webRoot = response.data.webRoot
        window.parent.$.fileUrl = response.data.fileUrl
        window.parent.$.uploadUrl = response.data.uploadUrl
        window.parent.$.ow365url = response.data.ow365url
        this.$toast.clear()
        let noticeId = items.noticeId // 区分是二维码还是分享
        if (noticeId) {
          this.$router.replace({name: `wxShare/${noticeId}`, params: {userId: items.userId, oid: items.oid}})
        } else {
          let recruit = this.$store.getters.getRecruit
          recruit.userId = items.userId
          recruit.oid = items.oid
          this.$store.dispatch('setRecruit', recruit)
          this.$router.replace({name: 'home'})
        }
      }, (response) => {
        console.log('请求失败！', response)
        this.jumpError('服务器请求失败！')
      })
      console.log('new window.parent.$', window.parent.$)
    },
    decrypt (data) {
      let jse = new JsEncrypt()
      jse.setPrivateKey(`**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)
      return jse.decrypt(data)
    },
    encrypt (data) {
      let jse = new JsEncrypt()
      jse.setPublicKey(`-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAvySpSYm/34kJikFY7MqL
/sGWSbhUvKug94zeAQzvMEvJSkqRLHL1mYt1kluCD0xyqO/2opIiODuzZuskZokB
/VRyAkL8G5Ntgsv48cxvnpnL2fP+TP+PLdZYan7p+Ygs/aKdm01mT94L26kex41M
lkvghzncBfHtYhTH5lWhtmXi7mHPpUKrFVyDjPA3JrTJzZzeQvAdrT8UflHf2/Ea
hQo4EDwZczIiVYVosjiWKBoX4QKPXAJhIVt88Y39/mDaLYeOkDNE/5/jNrGZm3Pl
Wb/xB+mWRf1cH4UZl3qIqI/a9eyhjMFpX5FP3DB97TVg1s49AzLL0exBFjd2jK+Z
LLFShpLVpiN+tJAMJT3t5cvh5Ctf5kuLPp4Bh8+YD+GnuSmfdvK+cpt25atRkdbn
S76KY3u17VdKxRi2kklOl2T7HZQzAK9FPg9e0v99DHYN/4t1R33pm+u5cANF8uAl
INKpipDP2+eDKiYbasGeSe9henPyO1rcu9JFHF0fbZJuup8nhMZLpvmj5B3jHhBs
j36pdwwFGPAbxsv1E7FIaIBb0mf1+xyFWIJnkDOf7UIeDDKHqYEe/2u82D3I1xV8
wWdml7K33E7fpqTi8ShWGV89STAnUTLO1N4yy7A4p90jobYmHte1Amoy1HbvsQJg
x0o+z7lqYGkzb/yE+a67xf8CAwEAAQ==
-----END PUBLIC KEY-----`)
      return jse.encrypt(data)
    }
  }
}
</script>
