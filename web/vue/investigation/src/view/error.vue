<template>
  <div id="error">
    <img :src="errorImg" alt="提交失败了"/>
    <div class="round" v-html="message"></div>
    <el-button style="margin:100px auto; display: block;" type="primary" @click="close()">关闭</el-button>
  </div>
</template>

<style>
  #error{ margin-top: 30px; position: relative}
  #error h5{text-align: center}
  #error .round{ margin-top:200px;  color: #b93a09; font-size:20px;  text-align: center;   }
  #error img{ display:block; width:60%; margin:10px auto;  }
  .close{ position: absolute; top:150px; }
  .el-button--primary {
    margin: 100px auto!important;
    display: block!important;
  }
</style>

<script>
import errorImg from '../assets/error.png'
export default {
  data () {
    return {
      errorImg: errorImg,
      message: '出错啦!'
    }
  },
  mounted () {
    let message = this.$route.params.message || '出错啦!'
    if (typeof (message) !== 'undefined') {
      this.message = message
    } else {
      this.message = '出错啦!'
    }
  },
  methods: {
    close () {
      if (typeof (window.WeixinJSBridge) !== 'undefined') {
        window.WeixinJSBridge.call('closeWindow')
      } else if (navigator.userAgent.indexOf('MSIE') > 0) {
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
          window.opener = null
          window.close()
        } else {
          window.open('', '_top')
          window.top.close()
        }
      } else {
        try {
          this.focus()
          self.opener = null
          self.close()
        } catch (e) {
        }
        try {
          window.opener = null
          window.open('', '_self', '')
          window.close()
        } catch (e) {
        }
        try {
          window.open(window.location, '_self').close()
        } catch (e) {
        }
        window.location.replace('about:blank')
      }
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  h1, h2 {
    font-weight: normal;
  }
  .error{
    background-color: #f1f1f2;
    border-radius: 4px;
    text-align: center;
    padding: 20px;
  }
  .error i{
    color: #F56C6C;
  }
  .tip{
    margin-left: 8px;
  }
</style>
