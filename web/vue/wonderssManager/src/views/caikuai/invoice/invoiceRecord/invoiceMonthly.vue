<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" @mycloseFun="handleCustomClose" :showClose=true :showBack=true>
      </PageHeader>
    </div>
    <div class="padding-10-15 ty-fff sticky" v-if="list.specialInvoice&&list.ordinaryInvoice&&list.otherInvoice">
      <p>{{ new
      Date(invoiceType.beginDate).format('yyyy-MM-dd') }}至{{ new
      Date(invoiceType.endDate).format('yyyy-MM-dd') }}期间</p>
      <p>开具{{invoiceName}}{{invoiceNum}}张,金额{{invoiceAmount}}元</p>
    </div>
    <div class="re">
      <p class="h35f14 f4 margin_top_16">
        <span>按月份展示如下：</span>
      </p>
      <ul>
        <li v-for="(item,index) in list2" :key="item.id" class="flex"
            :class="{ 'f4': index % 2 !== 0 ,'ty-fff':index%2===0}" @click="rou(item)">
          <p>{{ item.monthYear }}</p>
          <p>
            <span>{{ item.allInvoice.num }}张</span>
            <span>{{ item.allInvoice.totalAmount }}元</span>
          </p>
          <div>
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {getInvoiceStatistics, getInvoiceType} from '@/api/financeBill'

export default {
  name: "invoiceMonthly",
  data() {
    return {
      tit: "财会",
      invoiceType: {},
      invoiceList: {},
      list: {},
      list2: [],
      invoiceName: '',
      invoiceNum: '',
      invoiceAmount: ''
    }
  },
  computed: {},
  mounted() {
    let invoiceType = localStorage.getItem('invoiceType');
    this.invoiceType = JSON.parse(invoiceType);
    console.log(invoiceType, 'invoiceTypeeeeeeeeeeeeeeeeeeeeee')
    let invoiceList = localStorage.getItem('invoice');
    this.invoiceList = JSON.parse(invoiceList);
    this.getList(this.invoiceList, this.invoiceType)
  },
  methods: {
    getList(invoiceList, invoiceType) {
      if (invoiceList.index == 0) {
        this.beginTime = new Date(new Date().getFullYear(), 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 1) {
        this.beginTime = new Date(new Date().getFullYear() - 1, 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 2) {
        this.beginTime = new Date(new Date().getFullYear() - 2, 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 3) {
        this.beginTime = new Date(this.invoiceList.result, 0, 1).format('yyyy-MM-dd');
      }

      const params = {beginDate: this.beginTime, typeMonth: invoiceList.typeMonth, dateType: invoiceList.dateType}
      getInvoiceStatistics(params).then(res1 => {
        this.list = res1.data.data
        if (invoiceType.type == 1) {
          this.invoiceName = '增值税专用发票'
          this.invoiceNum = res1.data.data.specialInvoice.num
          this.invoiceAmount = res1.data.data.specialInvoice.totalAmount == null ? 0 : res1.data.data.specialInvoice.totalAmount
        } else if (invoiceType.type == 2) {
          this.invoiceName = '增值税普通发票'
          this.invoiceNum = res1.data.data.ordinaryInvoice.num
          this.invoiceAmount = res1.data.data.ordinaryInvoice.totalAmount == null ? 0 : res1.data.data.ordinaryInvoice.totalAmount
        } else if (invoiceType.type == 3) {
          this.invoiceName = '其他发票'
          this.invoiceNum = res1.data.data.otherInvoice.num
          this.invoiceAmount = res1.data.data.otherInvoice.totalAmount == null ? 0 : res1.data.data.otherInvoice.totalAmount
        }
        console.log(res1, 'res1.data.data')
      }).catch(error => {
        console.error('Error:', error);
      });
      const params2 = {
        type: invoiceType.type,
        beginDate: new Date(invoiceType.beginDate).format('yyyy-MM-dd'),
        endDate: new Date(invoiceType.endDate).format('yyyy-MM-dd')
      }
      getInvoiceType(params2).then(res1 => {
        this.list2 = res1.data.data.monthList
      }).catch(error => {
        console.error('Error:', error);
      });

    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    rou(item) {
      const [year, month] = item.monthYear.split("-");
      const beginDate = new Date(year, month - 1, 1).format('yyyy-MM-dd');
      const endDate = new Date(year, month, 0).format('yyyy-MM-dd');
      let invoiceDetailsMonthly = {
        type: this.invoiceType.type,
        beginDate,
        endDate
      }
      console.log(invoiceDetailsMonthly, 'invoiceDetailsListttttttttttttttttttt')
      localStorage.setItem('invoiceDetailsMonthly', JSON.stringify(invoiceDetailsMonthly))
      this.$router.push("/invoiceDetailsList")
    },
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.sticky {
  line-height: 22px;
}

.re {
  position: relative;
  top: 40px;
}

ul {
  > li {
    display: flex;
    line-height: 22px;
    padding: 8px 15px;
    justify-content: space-between;

    > :nth-child(1) {
      width: 25%;
    }

    > :nth-child(2) {
      width: 50%;
      text-align: left;
      display: flex;
      justify-content: space-between;

      > span {
        width: 48%;
        text-align: end;
        word-break: break-all;
        white-space: normal;
      }
    }

    > div:last-child {
      width: 10%;
      display: flex;
      justify-content: end;
    }
  }
}

.h35f14 {
  padding: 2px 15px;
}
</style>