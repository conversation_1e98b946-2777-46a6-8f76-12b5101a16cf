<template>
  <div id="potentialApply" class="potential">
    <TY_NavTop title="潜在客户受理" isUpdate="true" @toggleUpdate="jumpEdit"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div v-if="tabValue === '1'">
        <div class="panel-title">如有必要推进与本客户的合作，则请您指定销售负责人</div>
        <div class="panel-content">
          <div class="clear">
            <div class="put-right handleSect">
              <el-link type="primary" @click="bookVisible = true">指定销售负责人</el-link>
              <el-link type="danger" @click="stopDialog = true">终止接触</el-link>
            </div>
          </div>
        </div>
      </div>
      <div v-if="tabValue === '2'">
        <div class="panel-content">
          <div class="clear">
            <div class="put-left">
              <span>本客户销售负责人：{{details. principalName}}</span>
            </div>
            <div class="put-right handleSect">
              <el-link type="primary" @click="bookVisible = true">更换</el-link>
              <el-link type="primary" @click="salerRecord">更换记录</el-link>
              <el-link type="danger" @click="stopDialog = true">终止接触</el-link>
            </div>
          </div>
        </div>
      </div>
      <div v-if="tabValue === '3'">
        <div class="panel-title">  实际工作有进展时，请在本页面进行操作。</div>
        <div class="panel-content">
          <div class="clear">
            <div class="put-right handleSect">
              <el-link type="primary" @click="transferToRegularConfirm">转为正式客户</el-link>
              <el-link type="danger" @click="stopDialog = true">终止接触</el-link>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-title">
        基本信息 <div class="right" v-on:click="recordToggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
      </div>
      <div class="panel-content">
        <div v-show="toggle > 0">
          <div class="recorditem" v-for="(record, index) in handleRecord" :key="index">
            <div class="recordDetail">
              <span v-if="record.approveStatus === '1'">申请人： {{record.askName}} {{record.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</span>
              <span v-if="record.approveStatus === '2'">审批人： {{record.userName}} {{record.handleTime | formatDate('yyyy/MM/dd hh:mm:ss')}}</span>
              <span v-if="record.approveStatus === '3'">销售负责人： {{record.userName}} {{record.handleTime | formatDate('yyyy/MM/dd hh:mm:ss')}}</span>
            </div>
          </div>
        </div>
        <el-row>
          <el-col :span="6">客户名称</el-col>
          <el-col :span="18">{{details.fullName}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">地    址</el-col>
          <el-col :span="18">{{details.address}}</el-col>
        </el-row>
        <div v-if="sphdSocket.user.oid === 0">
          <el-row>
            <el-col :span="6">最高负责人</el-col>
            <el-col :span="18">{{details.supervisorName}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="6">手 机</el-col>
            <el-col :span="18">{{details.supervisorMobile}}</el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="6">全景图片</el-col>
          <el-col :span="18">
            <div class="col-flex">
              <div class="block" v-for="(img, index) in details.qImages" :key="index">
                <el-image
                  style="width: 80px; height: 80px"
                  :src="fileUrl + img.normal"
                  fit="contain" @click="preview(1, img.normal)"></el-image>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">产品图片</el-col>
          <el-col :span="18">
            <div class="col-flex">
              <div class="block" v-for="(img, index) in details.pImages" :key="index">
                <el-image
                  style="width: 80px; height: 80px"
                  :src="fileUrl + img.normal"
                  fit="contain" @click="preview(2, img.normal)"></el-image>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触日期</el-col>
          <el-col :span="18">{{details.firstContactTime | formatDate('yyyy/MM/dd')}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触地点</el-col>
          <el-col :span="18">{{details.firstContactAddress}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">信息获取渠道</el-col>
          <el-col :span="18">{{details.infoSource}}</el-col>
        </el-row>
      </div>
      <div class="panel-title">
        该客户的需求等情况
      </div>
      <div>
        <div class="ui-cells_none">
          <a class="ui-cell" >
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                {{ details.functionOptTxt }}
              </div>
            </div>
          </a>
          <a class="ui-cell" >
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                机构向其客户 {{ details.saleOpt }}
              </div>
            </div>
          </a>
          <a class="ui-cell" >
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                {{ details.commodityOpt === '有自己生产的产品'? '机构' : '机构的商品' + ' ' + (details.commodityOpt || '')  }}
              </div>
            </div>
          </a>
          <a class="ui-cell" >
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <span v-if="details.warehouseOpt === '本机构不使用仓库功能'">本机构不使用仓库功能</span>
                <span v-if="details.warehouseOpt === '智能仓库模式'">使用本系统后，机构采用智能仓库模式 </span>
                <span v-if="details.warehouseOpt === '非智能仓库模式'">使用本系统后，机构不采用智能仓库模式 </span>
              </div>
            </div>
          </a>

        </div>
      </div>
      <div class="panel-title">
        联系人<div class="right" v-on:click="newContact"><el-link type="primary">新增</el-link></div>
      </div>
      <div>
        <div class="ui-cells_none">
            <a class="ui-cell" @click="jumpContact(contact.id)" v-for="(contact, index) in details.contactsList" :key="index">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <el-row>
                    <el-col :span="6">{{contact.name}}</el-col>
                    <el-col :span="6">{{contact.post}}</el-col>
                    <el-col :span="12">{{contact.mobile}}</el-col>
                  </el-row>
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
      </div>
      <div class="panel-title">
        访谈记录<div class="right" v-on:click="newInteview"><el-link type="primary">新增</el-link></div>
      </div>
      <div>
        <div class="ui-cells_none">
          <a class="ui-col" @click="jumpInteview(view.id)" v-for="(view, index) in details.interviewList" :key="index">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div class="clearBoth">
                  <div class="viewLt" :title="view.interviewerNames | handleNull | formatName">访谈对象 共{{view.interviewer | countNum}}人：{{view.interviewerNames | handleNull | formatName}}</div>
                  <div class="viewRt">访谈日期 {{view.interviewDate | formatDate('yyyy-MM-dd')}}</div>
                </div>
                <div>访谈目标:{{view.mbContent | handleNull}}
                </div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="stopDialog"
      width="70%" class="def-danger">
      <p class="st-center">确定不再与该客户继续接触吗？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="stopDialog = false">取消</a>
          <a class="fc-btn ui-btn_info" ref="operate" @click="endContact">确定</a>
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="transferDialog"
      width="70%" class="def-danger">
      <p class="st-center">确定将该客户转为正式客户吗？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="transferDialog = false">取消</a>
          <a class="fc-btn ui-btn_info" ref="operate" @click="transferToRegular">确定</a>
        </div>
      </span>
    </el-dialog>
    <el-dialog
      :title="imgTitle"
      :visible.sync="previewDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="previewDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="previewPath" :download="previewPath" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="selectSaler"></TY_AddressBook>
  </div>
</template>
<style lang="less">
  .clearBoth:before, .clearBoth:after {
    display: block;
    clear: both;
    content: "";
    visibility: hidden;
    height: 0;
  }
  .viewLt{
    float: left;
    width: 56%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .viewRt {
    float: right;
    width: 42%;
  }
  .wideSize .ui-col_con{
    width: 330px;
  }
</style>
<script>
import { formatDate, gohistory, countNum, handleNull, formatName } from '@/js/common'
export default {
  name: 'potentialApproveDetail',
  filters: {
    formatDate,
    gohistory,
    countNum,
    handleNull,
    formatName
  },
  data () {
    return {
      loading: true,
      toggle: -1,
      details: '',
      tabValue: '',
      roleList: [],
      saler: {},
      handleRecord: [],
      transferDialog: false,
      stopDialog: false,
      bookVisible: false,
      browser: 1,
      imgTitle: '',
      previewPath: '',
      previewDialog: false,
      isSpecial: false,
      fileUrl: window.parent.$.fileUrl
    }
  },
  created () {
    let _this = this
    let detailId = _this.$route.params.id
    _this.tabValue = _this.$route.params.type
    this.axios.post('../../../sale/getPotentialCustomer.do', {
      id: detailId
    }).then(function (response) {
      _this.loading = false
      _this.details = response.data.data
      let functionOptList =  JSON.parse(_this.details.functionOpt)
      let opStr = ['总务']
      functionOptList.forEach(opI=>{
        if(opI === '无需开通其他功能'){
          _this.details.functionOptTxt = '需开通总务'
        }
        else {
          switch (opI){
            case '销售功能': opStr.push('销售'); break;
            case '会计功能': opStr.push('会计'); break;
            case '财务功能': opStr.push('财务'); break;
          }
        }
      })
      if(opStr.length > 1){
        let stt = opStr.join('、')
        stt = stt.replace(/(.*)、/,'$1与')
        _this.details.functionOptTxt = '需开通' + stt
      }

    }).catch(function (error) {
      console.log(error)
    })
    this.axios.post('../../../sales/getSalesmanList.do'
    ).then(function (response) {
      _this.loading = false
      _this.roleList = response.data.salesmanList
    }).catch(function (error) {
      console.log(error)
    })
    if (this.sphdSocket.user.oid === 0) {
      this.isSpecial = true
    }
  },
  methods: {
    jumpEdit: function () {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialEdit/${detailId}`
      })
    },
    jumpContact: function (id) {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialContact/${detailId}/${id}`
      })
    },
    jumpInteview: function (id) {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialInteview/${detailId}/${id}/${1}`
      })
    },
    newContact: function () {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/contactDetailUpdate/${detailId}/0/${'new'}`
      })
    },
    newInteview: function () {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/viewDetailUpdate/${detailId}/0/${'new'}`
      })
    },
    recordToggle: function () {
      let _this = this
      _this.toggle = -_this.toggle
      if (_this.toggle > 0) {
        this.axios.post('../../../sale/getCustomerApprovalRecordList.do', {
          id: _this.$route.params.id
        }).then(function (response) {
          _this.handleRecord = response.data.data
        }).catch(function (error) {
          console.log(error)
        })
      }
    },
    selectSaler: function (data) {
      let _this = this
      _this.loading = true
      let json = {
        id: this.$route.params.id,
        principal: data.userID
      }
      this.axios.post('../../../sale/updatePrincipal.do', json).then(function (response) {
        let state = response.data.status
        _this.loading = false
        if (state === 1) {
          if (_this.tabValue === '1') {
            gohistory(_this)
          } else {
            _this.$message({
              type: 'success',
              message: '更换成功！'
            })
            _this.details.principalName = data.userName
          }
        } else {
          _this.$message({
            type: 'error',
            message: '操作失败！'
          })
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    endContact: function () {
      let _this = this
      _this.loading = true
      this.axios.post('../../../sale/stopContact.do', {
        id: _this.$route.params.id
      }).then(function () {
        _this.loading = false
        gohistory(_this)
      }).catch(function (error) {
        console.log(error)
      })
    },
    transferToRegularConfirm: function () {
      if (this.isSpecial) {
        this.transferToRegular()
      } else {
        this.transferDialog = true
      }
    },
    transferToRegular: function () {
      if (this.isSpecial) {
        let detailId = this.$route.params.id
        this.$router.push({
          path: `/potentialToFormal/${detailId}`,
          name: 'potentialToFormal',
          params: {
            data: {
              fullName: this.details.fullName,
              address: this.details.address,
              code: '',
              supervisorMobile: this.details.supervisorMobile,
              supervisorName: this.details.supervisorName
            }
          }
        })
      } else {
        let _this = this
        this.axios.post('../../../sale/turnFormalCustomer.do', {
          id: _this.$route.params.id
        }).then(function () {
          gohistory(_this)
        }).catch(function (error) {
          console.log(error)
        })
      }
    },
    salerRecord: function () {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialChangeRecord/${detailId}`
      })
    },
    preview: function (tab, path) {
      this.previewDialog = true
      if (tab === 1) {
        this.imgTitle = '全景图片'
      } else {
        this.imgTitle = '产品图片'
      }
      this.previewPath = path
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    }
  }
}
</script>
