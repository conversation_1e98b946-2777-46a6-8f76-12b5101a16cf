package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectDetail;
import org.springframework.stereotype.Component;

@Component
public interface DetailMapper extends BaseMapper<TAccountantSubjectDetail> {

    /*得到科目详情表*/
    TAccountantSubjectDetail getSubjectDetail(TAccountantSubjectDetail param);

    /*删除原详情表*/
    int deleteDetail(TAccountantSubjectDetail param);

}