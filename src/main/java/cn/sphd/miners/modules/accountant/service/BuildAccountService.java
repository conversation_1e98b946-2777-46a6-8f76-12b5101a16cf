package cn.sphd.miners.modules.accountant.service;

import cn.sphd.miners.modules.accountant.entity.TAccountantCash;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-06-14 18:52
 * @description
 **/
public interface BuildAccountService {

    JSONObject getBaseSubjectsByOrg(int org);

    String getBuildAccountState(int org);

    int setBuildAccountState(User user, String state);

    JSONObject amountVerification(int org, String listSubjectsParam, String cashFlow);

    int saveBuildAccount(int org, String listSubjects, String cashFlow);

    JSONObject reportVerification(User user);

    int buildFinish(User user);

    int rebuild(int org,User user);

    int checkRebuildFlag(int org);

    void initialKJdata(int oid, User user);
}
