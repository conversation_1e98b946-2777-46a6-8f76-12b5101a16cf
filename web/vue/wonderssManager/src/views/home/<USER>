<template>
  <div class="homeMy">
    <div class="my-flex-panel my-flex-item" v-if="user">
      <div class="img33">
        <el-image class="userimg" :src="user.imgPath">
          <template #error>
            <img :src="userImgErr" class="userimg"/>
          </template>
        </el-image>
      </div>
      <div class="user">
        <div class="officeName">{{ org.name || '----' }}</div>
        <div class="userInfo">
          <div class="txt">{{ user.userName || '---' }}</div>
          <div>
            <span class="txt">手机号：{{ user.mobile || '10000000001' }}</span>
            <el-icon class="live-right relaRight">
              <arrow-right-bold color="#53B5A8" class="houseSty8"/>
            </el-icon>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="ty-panel">
        <div class="ty-p" @click="goPage('mine')">
          <i class="icon icon-privateArea"></i> 私人领地
          <i class="fa fa-angle-right"></i>
        </div>
      </div>
      <div class="ty-panel">
        <div class="ty-p" @click="goPage('switchOrg')">
          <i class="icon icon-changeOffice"></i> 切换机构
          <i class="fa fa-angle-right"></i>
        </div>
      </div>
      <div class="ty-panel">
        <div class="ty-p" @click="tip">
          <i class="icon icon-cancellation"></i> 解绑退出
          <i class="fa fa-angle-right"></i>
        </div>
        <div class="ty-p" @click="exit">
          <i class="icon icon-cancellation"></i> 退出
          <i class="fa fa-angle-right"></i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import userImgErr from "@/assets/commonImg/userImgErr.png"
import auth from '@/sys/auth'
import wx from 'weixin-js-sdk'

export default {
  data() {
    return {
      org: '',
      user: '',
      userImgErr: userImgErr,
    }
  },
  mounted() {
    this.user = auth.getUser()
    this.user.imgPath = auth.webRoot + '/upload/' + this.user.imgPath
    this.org = auth.getOrg()
    console.log('user=', this.user)
    console.log('org=', this.org)
    console.log('imgPath=', this.user.imgPath)
  },
  watch: {
    homePageAndWorks: {
      immediate: true,
      deep: true,
      handler(newVal) {
        console.log('homePageAndWorks', newVal)
      }
    }
  },
  destroyed() {
  },
  methods: {
    tip() {
      this.$message.info('功能尚未开发！')
    },
    exit() {
      wx.miniProgram.navigateTo({url: '/pages/exit/index'})
    },
    imgOnError(e) {
      let img = e.srcElement;
      img.src = userImgErr
      img.onerror = null; //防止闪图
    },
    goPage(urlStr) {
      this.$router.push(urlStr)
    },
  }
}

</script>

<style lang="scss" scoped>
$imgWid: 80px;
$imgWid2: 40px;
$bgColor: #fafafa;
.homeMy {
  .my-flex-panel {
    display: flex;
    padding: 20px;
    background-color: $bgColor;
    margin-bottom: 20px;

    .img33 {
      flex: 1;
      display: inline-block;
      overflow: hidden;
      background-color: #eee;
      border-radius: $imgWid2;
      width: $imgWid;
      height: $imgWid;
      position: relative;
      top: 20px;
      text-align: center;

      img {
        width: 84px;
        height: 84px;
        display: inline-block;
      }
    }

    .user {
      flex: 3;
      padding-left: 15px;

      .officeName {
        line-height: 50px;
      }

      .userInfo {
        line-height: 30px;
        color: #555;
        font-size: 0.9em;
      }
    }
  }

  .icon {
    width: 20px;
    height: 20px;
    background-color: #fafafa;
    background-repeat: no-repeat;
    background-size: 16px !important;
    display: inline-block;
    position: relative;
    top: 6px;
    margin-right: 0;
  }

  .icon-privateArea {
    background-image: url("@/assets/wonderss/privateArea.png");
  }

  .icon-cancellation {
    background-image: url("@/assets/wonderss/cancellation.png");
  }

  .icon-changeOffice {
    background-image: url("@/assets/wonderss/changeOffice.png");
  }


}

.ty-panel {
  background-color: #fafafa;
  line-height: 40px;
  margin: 20px 0;
  padding: 4px 20px;

  .ty-p:not(:last-child) {
    border-bottom: 1px solid #f8f8f8;
  }

  .fa-angle-right {
    float: right;
    font-size: 25px;
    color: #ddd;
    margin-top: 6px;
  }
}


</style>

<style lang="scss">
.el-image {
  overflow: visible;
}
</style>
