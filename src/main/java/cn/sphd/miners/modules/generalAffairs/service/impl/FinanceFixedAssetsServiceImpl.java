package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.modules.generalAffairs.dao.FinanceFixedAssetsDao;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceFixedAssets;
import cn.sphd.miners.modules.generalAffairs.service.FinanceFixedAssetsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2017/7/11.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceFixedAssetsServiceImpl implements FinanceFixedAssetsService {

    @Autowired
    FinanceFixedAssetsDao financeFixedAssetsDao;


    @Override
    public void addFinanceFixedAssets(FinanceFixedAssets financeFixedAssets) {
        financeFixedAssetsDao.save(financeFixedAssets);
    }

    @Override
    public List<FinanceFixedAssets> getAllSnNameList(Integer org) {
        String hql="from FinanceFixedAssets o where o.org="+org+" order by o.createDate desc";
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceFixedAssets> getFinanceFixedAssetsByOrg(Integer org, String useState) {
       String hql="from FinanceFixedAssets o where o.org="+org+" and o.useState="+useState+" order by o.createDate desc";
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceFixedAssets> getFinanceFixedAssetsByOrg1(Integer org, String useState) {
        String hql="from FinanceFixedAssets o where o.org="+org+" and o.useState="+useState+" order by o.name desc";
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceFixedAssets> getFinanceFixedAssetsByOrg2(Integer org, String useState) {
        String hql="from FinanceFixedAssets o where o.org="+org+" and o.useState="+useState+" order by o.name asc";
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceFixedAssets> getFinanceFixedAssetsByOrg3(Integer org, String useState) {
        String hql="from FinanceFixedAssets o where o.org="+org+" and o.useState="+useState+" order by o.storageDate desc";
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceFixedAssets> getFinanceFixedAssetsByOrg4(Integer org, String useState) {
        String hql="from FinanceFixedAssets o where o.org="+org+" and o.useState="+useState+" order by o.storageDate asc";
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceFixedAssets> getFinanceFixedAssetsReceByOrg(Integer org, String receiveState) {
        String hql="from FinanceFixedAssets o where o.org="+org+" and o.receiveState="+receiveState+" order by o.createDate desc";
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceFixedAssets> findAll(Integer org) {
        String hql="from FinanceFixedAssets o where o.org="+org;
        return financeFixedAssetsDao.getListByHQL(hql);
    }

    @Override
    public FinanceFixedAssets getFinanceFixedAssets(Integer org, Integer id) {
        String hql="from FinanceFixedAssets o where o.org="+org+" and o.id="+id;
        return financeFixedAssetsDao.getByHQL(hql);
    }

    @Override
    public void updateFinanceFixedAssets(FinanceFixedAssets financeFixedAssets) {
        financeFixedAssetsDao.update(financeFixedAssets);
    }


    @Override
    public List<FinanceFixedAssets> getSnNameList(Integer org, String useState, String bhxh) {
        String hql="from FinanceFixedAssets o where o.org="+org+" and o.useState="+useState+" and o.snName='"+bhxh+"'";
        return financeFixedAssetsDao.getListByHQL(hql);
    }



}
