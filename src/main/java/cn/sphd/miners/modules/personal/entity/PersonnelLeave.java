package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

/**
 * Created by Administrator on 2016/10/25.
 * 请假表
 */
@Entity
@Table(name = "t_personal_leave")
public class PersonnelLeave implements Serializable{
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;

    @Column(name="days"   , nullable=true , unique=false)
    private Integer days;

    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;   //开始时间

    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;   //结束时间

    @Column(name="duration"   , nullable=true , unique=false)
    private Double duration;//时长

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type; //1-事假,2-病假,3-婚假,4-产假,5-产假（1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他）

    @Column(name="kind"   , nullable=true , unique=false)
    private Byte kind;  //类型:1-普通(默认),2-补报

    @Column(name="reason"  , length=255 , nullable=true , unique=false)
    private String reason;   //原因

    @Column(name = "actual_state",length = 1,nullable = true,unique = false)
    private String actualState;  //实际请假状态:0/null-按计划完成,1-提前消假',

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;

    @Column(name="depart"   , nullable=true , unique=false)
    private Integer depart;

    @Column(name="departName"   , nullable=true , unique=false)
    private String departName;

    @Column(name="postID"   , nullable=true , unique=false)
    private Integer postID;

    @Column(name="postName"   , nullable=true , unique=false)
    private String postName;

    @Column(name="leader"   , nullable=true , unique=false)
    private Integer leader;

    @Column(name="comment"  , length=255 , nullable=true , unique=false)
    private String comment;   //审批意见

    @Column(name="approval_days"   , nullable=true , unique=false)
    private Integer approvalDays;

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;// 是否进查询  T- 进  仅在 已批准 进查询时使用

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date")
    @CreationTimestamp
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , unique=false)
    @UpdateTimestamp
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;//1-待处理 2-批准 3-驳回 9-撤回

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;  //最后的审批日期[如果有提前结束请假，则为提前结束请假批准/驳回的时间]

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;//申请备注(驳回原因)

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;  //审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="actual_type"  , length=1 , nullable=true , unique=false)
    private String actualType; //1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-丧假'

    @Column(name="actual_duration"   , nullable=true , unique=false)
    private Double actualDuration;//实际时长

    @Column(name="actual_begin_time"   , nullable=true , unique=false)
    private Date actualBeginTime;//实际起始时间

    @Column(name="actual_end_time"   , nullable=true , unique=false)
    private Date actualEndTime;//实际截止时间

    @Column(name="actual_reason"  , length=255 , nullable=true , unique=false)
    private String actualReason;//实际原因

    @Column(name="approve_duration"   , nullable=true , unique=false)
    private Double approveDuration;//批准时长

    @Column(name="approve_explain"  , length=255 , nullable=true , unique=false)
    private String approveExplain;//批准说明

    @Column(name="department"   , nullable=true , unique=false)
    private Integer department;//部门id

    @Column(name="leave_type"   , nullable=true , unique=false)
    private Integer leaveType;//请假类型id

    @ManyToOne(fetch= FetchType.EAGER )
    @JsonIgnore@JSONField(serialize = false)
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=true, updatable=true)
    private User user;

    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer user_;

    //请假明细表
    @JsonIgnore@JSONField(serialize = false)
    @OneToMany(targetEntity=PersonnelLeaveItem.class, fetch= FetchType.LAZY, mappedBy="leave", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<PersonnelLeaveItem> approvalProcessHashSet = new HashSet<PersonnelLeaveItem>();

    //处理过程
//    @OneToMany(targetEntity=ApprovalProcess.class, fetch= FetchType.LAZY, mappedBy="leave")//, cascade=CascadeType.ALL)
//    @Where(clause="business_type=3")
//    private Set<ApprovalProcess> approvalProcessSet = new HashSet<ApprovalProcess>();


    @Transient
    private String leaderName;  //直属上级

    @Transient
    private String userName;  //请假人

    @Transient
    private Integer buttonStatus;  //控制显示提前结束请假的按钮 1-不显示

    @Transient
    private String leaveTypeName;  //请假类型名称

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getDepart() {
        return depart;
    }

    public void setDepart(Integer depart) {
        this.depart = depart;
    }

    public Integer getLeader() {
        return leader;
    }

    public void setLeader(Integer leader) {
        this.leader = leader;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getApprovalDays() {
        return approvalDays;
    }

    public void setApprovalDays(Integer approvalDays) {
        this.approvalDays = approvalDays;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public Integer getPostID() {
        return postID;
    }

    public void setPostID(Integer postID) {
        this.postID = postID;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public Integer getUser_() {
        return user_;
    }

    public void setUser_(Integer user_) {
        this.user_ = user_;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    public Set<PersonnelLeaveItem> getApprovalProcessHashSet() {
        return approvalProcessHashSet;
    }

    public void setApprovalProcessHashSet(Set<PersonnelLeaveItem> approvalProcessHashSet) {
        this.approvalProcessHashSet = approvalProcessHashSet;
    }

    public String getActualState() {
        return actualState;
    }

    public void setActualState(String actualState) {
        this.actualState = actualState;
    }

//    public Set<ApprovalProcess> getApprovalProcessSet() {
//        return approvalProcessSet;
//    }
//
//    public void setApprovalProcessSet(Set<ApprovalProcess> approvalProcessSet) {
//        this.approvalProcessSet = approvalProcessSet;
//    }


    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getButtonStatus() {
        return buttonStatus;
    }

    public void setButtonStatus(Integer buttonStatus) {
        this.buttonStatus = buttonStatus;
    }

    public String getActualType() {
        return actualType;
    }

    public void setActualType(String actualType) {
        this.actualType = actualType;
    }

    public Double getActualDuration() {
        return actualDuration;
    }

    public void setActualDuration(Double actualDuration) {
        this.actualDuration = actualDuration;
    }

    public Date getActualBeginTime() {
        return actualBeginTime;
    }

    public void setActualBeginTime(Date actualBeginTime) {
        this.actualBeginTime = actualBeginTime;
    }

    public Date getActualEndTime() {
        return actualEndTime;
    }

    public void setActualEndTime(Date actualEndTime) {
        this.actualEndTime = actualEndTime;
    }

    public String getActualReason() {
        return actualReason;
    }

    public void setActualReason(String actualReason) {
        this.actualReason = actualReason;
    }

    public Double getApproveDuration() {
        return approveDuration;
    }

    public void setApproveDuration(Double approveDuration) {
        this.approveDuration = approveDuration;
    }

    public String getApproveExplain() {
        return approveExplain;
    }

    public void setApproveExplain(String approveExplain) {
        this.approveExplain = approveExplain;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public Integer getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(Integer leaveType) {
        this.leaveType = leaveType;
    }

    public String getLeaveTypeName() {
        return leaveTypeName;
    }

    public void setLeaveTypeName(String leaveTypeName) {
        this.leaveTypeName = leaveTypeName;
    }

    public Byte getKind() {
        return kind;
    }

    public void setKind(Byte kind) {
        this.kind = kind;
    }

    public Double calcDuration(List<Pair<Date, Date>> workHoursList) {
        return workHoursList.stream().mapToDouble(item -> {
            if(item.getLeft().getTime()<actualEndTime.getTime() && item.getRight().getTime()>actualBeginTime.getTime()) {
                return Math.min(item.getRight().getTime(), actualEndTime.getTime()) - Math.max(item.getLeft().getTime(), actualBeginTime.getTime());
            } else {
                return Double.valueOf(0.0F);
            }
        }).sum();
    }
    public void sliceDuration(Pair<Map<Long,Long>, Map<Long, Long>> durationSlices) {
        Map<Long, Long> newItems = new HashMap<>();
        Iterator<Map.Entry<Long, Long>> iterator = durationSlices.getRight().entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, Long> set = iterator.next();
            if (set.getKey() < actualEndTime.getTime() && set.getValue() > actualBeginTime.getTime()) {//工时时间片段与实际请假时间相交
                if(set.getKey() >= actualBeginTime.getTime() && set.getValue()  <= actualEndTime.getTime()) {//工时片段被包含
                    durationSlices.getLeft().put(set.getKey(), set.getValue());//添加时长片
                    iterator.remove();//删除工时片
//                    removeKeys.add(set.getKey());//删除工时片
                } else if(set.getKey() >= actualBeginTime.getTime()) {//工时片段左侧相同或被包含，右侧超出请假时间
                    durationSlices.getLeft().put(set.getKey(), actualEndTime.getTime());//添加时长片段
                    newItems.put(actualEndTime.getTime(), set.getValue());//重新添加右侧超出请假时间的工时片段
                    iterator.remove();//删除原工时片
                } else if(set.getValue() <= actualEndTime.getTime()) {//工时片段右侧相同或被包含，左侧超出请假时间
                    durationSlices.getLeft().put(actualBeginTime.getTime(), set.getValue());//添加右侧相交部分时长片段
                    set.setValue(actualEndTime.getTime());//将工时片段右侧改为请假开始时间
                } else {//请假被时间片包含
                    durationSlices.getLeft().put(actualBeginTime.getTime(), actualEndTime.getTime());//添加请假时间为时长片段
                    newItems.put(actualEndTime.getTime(), set.getValue());//新加请假结束到工时片段结束为新的工时片段
                    set.setValue(actualBeginTime.getTime());//将工时片段右侧改为请假开始时间
                }
            }
        };
        durationSlices.getRight().putAll(newItems);
    }
}