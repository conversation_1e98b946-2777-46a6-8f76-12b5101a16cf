<template>
  <div id="overTimeApply">
    <TY_NavTop title="加班审批" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="approvalHandleCount">
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, '1')" v-for="(item, index) in approvalHandle" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.beginTime |formatDay('YYYY年MM月DD日 dddd')}}</div>
                <div v-if="item.actualType !== '1'">计划时长  {{item.duration}}h</div>
                <div v-if="item.actualType === '1'">申报时长  {{item.actualDuration}}h</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, '2')" v-for="(item, index) in approvalApproval" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
                <div>计划时长  {{item.duration}}h</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>
export default {
  name: 'approveOverTime',
  data () {
    return {
      approvalHandle: [],
      approvalApproval: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  computed: {
    approvalHandleCount: function () {
      return this.approvalHandle.length
    }
  },
  created () {
    this.tabValue = this.$store.getters.getOverTimeNav
    console.log(this.$store.getters.getOverTimeNav)
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('notice', function (data) {
        console.log('notice Socket received OK:' + data)
        if (data === '1') {
          that.$router.push({
            path: '/approvalOutTime'
          })
        }
      }),
      this.sphdSocket.subscribe('approvalOutTimeHandle', function (data) {
        that.approvalHandle = JSON.parse(data)
        console.log('approvalOutTimeHandle session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('approvalOutTimeHandle', function (data) {
        let getData = JSON.parse(data)
        let personnelOvertime = getData.personnelOvertime
        let operate = Number(getData.operate)
        if (operate > 0) {
          if (personnelOvertime.actualType === '1') {
            that.approvalHandle.push(personnelOvertime)
          } else {
            that.approvalHandle.unshift(personnelOvertime)
          }
        } else if (operate < 0) {
          let overTimeId = personnelOvertime.id
          let _index = -1
          console.log(that.approvalHandle)
          that.approvalHandle.forEach(function (item, index) {
            if (item.id === overTimeId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.approvalHandle.splice(_index, 1)
          }
        }
        console.log('approvalOutTimeHandle user Socket received OK:' + data)
      }, function () {
        console.log('approvalOutTimeHandle user Socket Error:')
      }, 'user'),
      this.sphdSocket.subscribe('approvalOutTimeApproval', function (data) {
        that.approvalApproval = JSON.parse(data)
        // console.log('approvalOutTimeApproval session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('approvalOutTimeApproval', function (data) {
        let getData = JSON.parse(data)
        let personnelOvertime = getData.personnelOvertime
        let operate = Number(getData.operate)
        if (operate > 0) {
          if (personnelOvertime.actualType === '1') {
            that.approvalApproval.push(personnelOvertime)
          } else {
            that.approvalApproval.unshift(personnelOvertime)
          }
        } else if (operate < 0) {
          let overTimeId = personnelOvertime.id
          let _index = -1
          that.approvalApproval.forEach(function (item, index) {
            if (item.id === overTimeId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.approvalApproval.splice(_index, 1)
          }
        }
        console.log('approvalOutTimeApproval user Socket received OK:' + data)
      }, function () {
        console.log('approvalOutTimeApproval user Socket Error:')
      }, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    console.log('userId', this.auth.getUser().userID, this.sphdSocket.user.userID,  this.auth.getUser().userID === this.sphdSocket.user.userID)
    console.log('session', this.auth.getSessionid(), this.sphdSocket.sessionid, this.auth.getSessionid() === this.sphdSocket.sessionid)
    this.sphdSocket.send('approvalOutTime', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
      console.log(item)
    })
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setOverTimeNav', tab)
      this.$router.push({
        path: `/approvalOutTimeDetail/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/approvalOverTimeQuery`
      })
    }
  }
}
</script>
