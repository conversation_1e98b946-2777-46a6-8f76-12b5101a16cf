package cn.sphd.miners.modules.resourceAuthority.entity;

import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;
import org.apache.ibatis.type.Alias;

/**
 * Created by 朱思旭 on 2018/5/11.
 */
@Alias("resVisit")
public class ResVisit {
    private Integer id;
    private Integer file;
    private String type;
    private String memo;
    private Integer creator;
    private String createName;
    private String createDate;
    private Integer updator;
    private String updateName;
    private String updateDate;

    private Integer num;
    private String dateName;

    private String everyBeginTime;
    private String everyEndTime;
    private PostUserMussage postUserMussage;
    private ResEntity fileMessage;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFile() {
        return file;
    }

    public void setFile(Integer file) {
        this.file = file;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getDateName() {
        return dateName;
    }

    public void setDateName(String dateName) {
        this.dateName = dateName;
    }

    public String getEveryBeginTime() {
        return everyBeginTime;
    }

    public void setEveryBeginTime(String everyBeginTime) {
        this.everyBeginTime = everyBeginTime;
    }

    public String getEveryEndTime() {
        return everyEndTime;
    }

    public void setEveryEndTime(String everyEndTime) {
        this.everyEndTime = everyEndTime;
    }

    public PostUserMussage getPostUserMussage() {
        return postUserMussage;
    }

    public void setPostUserMussage(PostUserMussage postUserMussage) {
        this.postUserMussage = postUserMussage;
    }

    public ResEntity getFileMessage() {
        return fileMessage;
    }

    public void setFileMessage(ResEntity fileMessage) {
        this.fileMessage = fileMessage;
    }
}
