<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="reviewApproveFinanceSearchList" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="付款复核" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ttl11">
        <p>您要查询：</p>
        <p>{{ beginTimeShow |  formatDay('YYYY年MM月DD日')}} - {{ endTimeShow |  formatDay('YYYY年MM月DD日')}}期间</p>
        <p>经您复核的{{applyUserName}}个人报销付款</p>
      </div>
      <div class="ttl1">符合查询条件的数据共如下{{list.length}}条。</div>
      <div>

        <a class="ui-cell" v-on:click="applyDetail('21' + item.id, item.billCat)" v-for="(item, index) in list" v-bind:key="index">
          <div class="ui-cell__hd">{{item.createName | stringSplit(4)}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>申请报销 {{item.amount}} 元</div>
              <div>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</div>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #reviewApproveFinanceSearchList{
    .ttl1{ background:#DEEBF6; padding:10px;     }
    .ttl11{ background: #edf3c6; padding:10px;     }
    .clr2{clear: both;  }
    .btn{ border:1px solid #ccc;padding:5px 15px; border-radius:4px;   }
    .active{ border-color:#0da394; color: #0da394;  }
  }
</style>
<script>
export default {
  name: 'reviewApproveFinanceSearchList',
  data () {
    return {
      searchInfo: this.$store.getters.getApprovalSettingSearchInfo,
      loading: true,
      beginTime: '',
      endTime: '',
      chargeResult: '',
      searchType: Number(this.$route.params.id),
      applyTime: '',
      list: []
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created: function () {
    let _this = this
    console.log('searchInfo', this.searchInfo)
    _this.beginTime = _this.searchInfo.beginDate + ' 00:00:00'
    _this.endTime = _this.searchInfo.endDate + ' 23:59:59'
    _this.type = _this.searchInfo.type
    _this.applyId = _this.searchInfo.applyId || ''
    _this.applyUserName = _this.searchInfo.applyUserName
    if (!_this.applyId) {
      _this.applyUserName = '全部'
    }
    _this.getList()
  },
  methods: {
    getList: function () {
      let _this = this
      let url = '../../../paymentApproval/getPaymentReview.do'
      let data = {
        'userId': _this.sphdSocket.user.userID,
        'type': _this.type,
        'applyUserId': _this.applyId,
        'beginDate': _this.beginTime,
        'endDate': _this.endTime
      }
      _this.axios.post(url, {
        ...data
      }).then(function (response) {
        let data = response.data.data
        _this.list = data['paymentReviews']
        _this.beginTimeShow = data['beginTime']
        _this.endTimeShow = data['endTime']
        console.log('查询结果', data)
        _this.loading = false
      }).catch(function (error) {
        console.log(error)
        _this.loading = false
        _this.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    applyDetail: function (itemID, billCat) {
      this.$router.push({
        path: '/paymentApprovalDetail/' + itemID + '/' + billCat
      })
    }
  }
}
</script>
