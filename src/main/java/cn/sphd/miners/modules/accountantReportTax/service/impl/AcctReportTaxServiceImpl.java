package cn.sphd.miners.modules.accountantReportTax.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.*;
import cn.sphd.miners.modules.accountantReportTax.dto.*;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.finance.entity.FinanceAccountBillImage;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional(propagation= Propagation.REQUIRED)
public class AcctReportTaxServiceImpl implements AcctReportTaxService {

    @Autowired
    AcctReportDao acctReportDao;        //报表dao
    @Autowired
    AcctTaxDao acctTaxDao;        //税种dao
    @Autowired
    AcctTaxPeriodDao acctTaxPeriodDao;  //时间范围dao
    @Autowired
    AcctTaxReportDao taxReportDao;  //报表税种关联dao
    @Autowired
    AcctTaxReportHistoryDao taxReportHistoryDao;    //
    @Autowired
    AcctTaxDetailDao taxDetailDao;//detail dao
    @Autowired
    AcctTaxDetailHistoryDao taxDetailHistoryDao;// tax detail history dao
    @Autowired
    AcctReportHistoryDao reportHistoryDao;
    @Autowired
    AcctTaxPeriodHistoryDao periodHistoryDao;
    @Autowired
    AcctTaxHistoryDao taxHistoryDao;
    @Autowired
    AcctTriflesDao triflesDao;
    @Autowired
    AcctTriflesHistoryDao triflesHistoryDao;
    @Autowired
    AcctTriflesDetailDao triflesDetailDao;
    @Autowired
    AcctTriflesDetailHistoryDao triflesDetailHistoryDao;
    @Autowired
    AcctTriflesPeriodDao triflesPeriodDao;
    @Autowired
    AcctTriflesPeriodHistoryDao triflesPeriodHistoryDao;
    @Autowired
    DataService dataService;

    /**
     * 新增报表
     * 频次:0,-随机,1-日,2-周,3-旬,4-半月,5-月,6-季,7-半年,8-年
     * 操作:1-增,2-删,3-改,4-改错别字,5-改名字,6-改日期范围
     * 新增 report报表、period时间范围、taxreport关联表、detail表
     * 最后要新增历史表
     * 1、写报表原表，version初始0，作为修改次数统计
     * 2、根据频次写period表和periodHistory表
     * 3、写taxReport表，tax字段空缺，detail里需要taxReport表ID
     * 4、写报表历史表
     * 5、写detail表，作为报税记录表
     */
    @Override
    public JsonResult addReport(User user, AcctReportEntity reportEntity) {
        Date curDate = new Date();
        Integer creator = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();

        String name = reportEntity.getName();
        //判断是否重名
        boolean nameFlag = acctReportDao.checkReportName(name,org);//true-没有重名，false-有重名
        if (!nameFlag) {
            JsonResult result = new JsonResult();
            result.setSuccess(0);
            result.setData("操作失败！因为系统不支持同名报表。请输入其他名称！");
            return result;
        }

        String periodArray = reportEntity.getPeriodArray();//json格式的时间范围数组
        reportEntity.setOrg(org);
        reportEntity.setCreator(creator);
        reportEntity.setCreateName(userName);
        reportEntity.setCreateDate(curDate);
        reportEntity.setOperation("1");
        reportEntity.setEnabled(1);
        reportEntity.setEnabledTime(curDate);
        reportEntity.setVersionNo(0);
        acctReportDao.save(reportEntity);//新增报表
        Integer reportId = reportEntity.getId();
        List<AcctTaxPeriodEntity> periodList = addPeriodOfReport(periodArray,user,reportId);//新增时间范围

        //新增report历史表
        AcctReportHistoryEntity reportHistoryEntity = new AcctReportHistoryEntity();
        reportHistoryEntity.setReport(reportId);
        String rname = reportEntity.getName();
        reportHistoryEntity.setName(rname);
        reportHistoryEntity.setOldName(rname);
        reportHistoryEntity.setOrg(org);
        reportHistoryEntity.setCreateDate(curDate);
        reportHistoryEntity.setCreator(creator);
        reportHistoryEntity.setCreateName(userName);
        reportHistoryEntity.setEnabled(1);
        reportHistoryEntity.setFrequency(reportEntity.getFrequency());
        reportHistoryEntity.setVersionNo(0);
        reportHistoryDao.save(reportHistoryEntity);//新增report历史表

        AcctTaxReportEntity taxReportEntity = new AcctTaxReportEntity();
        taxReportEntity.setReport(reportId);
        taxReportEntity.setReportHistory(reportHistoryEntity.getId());
        taxReportEntity.setOperation("1");
        taxReportEntity.setEnabled(1);
        taxReportEntity.setCreator(creator);
        taxReportEntity.setEnabledTime(curDate);
        taxReportEntity.setOrg(org);
        taxReportEntity.setCreateDate(curDate);
        taxReportEntity.setCreateName(userName);
        taxReportEntity.setVersionNo(0);
        taxReportDao.save(taxReportEntity);//新增taxReport

        //新增taxReportHistory
        AcctTaxReportHistoryEntity taxReportHistoryEntity = new AcctTaxReportHistoryEntity();
        taxReportHistoryEntity.setOrg(org);
        taxReportHistoryEntity.setTaxReport(taxReportEntity.getId());
        taxReportHistoryEntity.setReport(reportId);
        taxReportHistoryEntity.setReportHistory(reportHistoryEntity.getId());
        taxReportHistoryEntity.setCreateDate(new Date());
        taxReportHistoryEntity.setCreator(creator);
        taxReportHistoryEntity.setCreateName(userName);
//        taxReportHistoryEntity.setTaxPeriod();//这个可以放report的版本号，以便查询绑定的是哪个版本下的report
        taxReportHistoryDao.save(taxReportHistoryEntity);//新增taxReportHistory

        AcctTaxDetailEntity taxDetailEntity = new AcctTaxDetailEntity();
        Integer taxReportId = taxReportEntity.getId();
        taxDetailEntity.setTaxReport(taxReportId);
        taxDetailEntity.setReportHistory(reportHistoryEntity.getId());
        taxDetailEntity.setCreateDate(curDate);
        taxDetailEntity.setCreator(creator);
        taxDetailEntity.setReport(reportId);
        taxDetailEntity.setCreateName(userName);
        taxDetailEntity.setOrg(org);
        taxDetailEntity.setOperation("1");
        taxDetailEntity.setState("0");//状态:0-未报,1-已报
        taxDetailDao.save(taxDetailEntity);//新增detail

        //新增period历史表
        for (AcctTaxPeriodEntity periodEntity : periodList) {
            AcctTaxPeriodHistoryEntity periodHistoryEntity = new AcctTaxPeriodHistoryEntity();
            periodHistoryEntity.setTaxPeriod(periodEntity.getId());
            periodHistoryEntity.setReport(reportId);
            periodHistoryEntity.setReportHistory(reportHistoryEntity.getId());
            periodHistoryEntity.setBeginMonth(periodEntity.getBeginMonth());
            periodHistoryEntity.setBeginDate(periodEntity.getBeginDate());
            periodHistoryEntity.setEndMonth(periodEntity.getEndMonth());
            periodHistoryEntity.setEndDate(periodEntity.getEndDate());
            periodHistoryEntity.setEnabled(periodEntity.getEnabled());
            periodHistoryEntity.setOrg(periodEntity.getOrg());
            periodHistoryEntity.setVersionNo(0);
            periodHistoryEntity.setCreator(creator);
            periodHistoryEntity.setCreateName(userName);
            periodHistoryEntity.setCreateDate(curDate);
            periodHistoryDao.save(periodHistoryEntity);
        }

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 新增时间范围
     * {"data":[{"beginDate":"9月1日","endDate":"9月10日"},{"beginDate":"9月1日","endDate":"9月10日"}]}
     * */
    private List<AcctTaxPeriodEntity> addPeriodOfReport(String periodArray,User user,Integer reportId) {
        List<AcctTaxPeriodEntity> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject(periodArray);
        JSONArray data = jsonObject.getJSONArray("data");
        Integer org = user.getOid();
        String userName = user.getUserName();
        Integer userId = user.getUserID();

        for (int i=0;i<data.length();i++) {
            JSONObject item = data.getJSONObject(i);
            AcctTaxPeriodEntity periodEntity = new AcctTaxPeriodEntity();
            periodEntity.setReport(reportId);
            periodEntity.setOrg(org);
            String beginDate = item.optString("beginDate","");
            String endDate = item.optString("endDate","");
            if (beginDate.length() > 0) {
                int yindex = beginDate.indexOf("月");
                int rindex = beginDate.indexOf("日");
                String month = beginDate.substring(0,yindex);
                String day = beginDate.substring(yindex + 1,rindex);
                periodEntity.setBeginMonth(Integer.parseInt(month));
                periodEntity.setBeginDate(Integer.parseInt(day));
            }
            else {}

            if (endDate.length() > 0) {
                int yindex = endDate.indexOf("月");
                int rindex = endDate.indexOf("日");
                String month = endDate.substring(0,yindex);
                String day = endDate.substring(yindex + 1,rindex);
                periodEntity.setEndMonth(Integer.parseInt(month));
                periodEntity.setEndDate(Integer.parseInt(day));
            }
            else {}

            periodEntity.setEnabled(1);
            periodEntity.setEnabledTime(new Date());
            periodEntity.setCreateDate(new Date());
            periodEntity.setCreator(userId);
            periodEntity.setCreateName(userName);
            periodEntity.setOperation("1");
            periodEntity.setVersionNo(0);
            acctTaxPeriodDao.save(periodEntity);//新增时间范围
            list.add(periodEntity);


        }
        return list;
    }

    /**
     * 新增税种
     * 频次:0,-随机,1-日,2-周,3-旬,4-半月,5-月,6-季,7-半年,8-年
     * 操作:1-增,2-删,3-改,4-改错别字,5-改名字,6-改日期范围
     * 新增 tax报表、taxreport关联表、detail表
     * 1、新增tax
     * 2、新增taxHistory
     * 3、新增taxreport关联表
     * 4、新增detail
     */
    @Override
    public JsonResult addTax(User user, AcctTaxEntity taxEntity) {
        Date curDate = new Date();
        Integer creator = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();
        Integer reportId = taxEntity.getReport();
        Integer taxId = taxEntity.getId();
        Integer taxHisId = taxHistoryDao.getTaxHisId(org,taxId);

        //根据report获取报表历史中的ID数据
        AcctReportHistoryEntity reportHistory = reportHistoryDao.getLastByReportId(reportId);
        Integer reportHisId = reportHistory.getId();

        AcctTaxReportEntity taxReportEntity = new AcctTaxReportEntity();
        taxReportEntity.setTax(taxId);
        taxReportEntity.setReport(reportId);
        taxReportEntity.setReportHistory(reportHisId);
        taxReportEntity.setTaxHistory(taxHisId);
        taxReportEntity.setOperation("1");
        taxReportEntity.setEnabled(1);
        taxReportEntity.setCreator(creator);
        taxReportEntity.setEnabledTime(curDate);
        taxReportEntity.setOrg(org);
        taxReportEntity.setCreateDate(curDate);
        taxReportEntity.setCreateName(userName);
        taxReportEntity.setVersionNo(0);
        taxReportDao.save(taxReportEntity);//新增taxReport

        //新增taxReportHistory
        AcctTaxReportHistoryEntity taxReportHistoryEntity = new AcctTaxReportHistoryEntity();
        taxReportHistoryEntity.setOrg(org);
        taxReportHistoryEntity.setTaxReport(taxReportEntity.getId());
        taxReportHistoryEntity.setTax(taxId);
        taxReportHistoryEntity.setReport(reportId);
        taxReportHistoryEntity.setReportHistory(reportHisId);
        taxReportHistoryEntity.setTaxHistory(taxHisId);
        taxReportHistoryEntity.setCreateDate(new Date());
        taxReportHistoryEntity.setCreator(creator);
        taxReportHistoryEntity.setCreateName(userName);
//        taxReportHistoryEntity.setTaxPeriod();//这个可以放report的版本号，以便查询绑定的是哪个版本下的report
        taxReportHistoryDao.save(taxReportHistoryEntity);//新增taxReportHistory

        AcctTaxDetailEntity taxDetailEntity = new AcctTaxDetailEntity();
        Integer taxReportId = taxReportEntity.getId();
        taxDetailEntity.setTaxReport(taxReportId);
        taxDetailEntity.setReport(reportId);
        taxDetailEntity.setTax(taxId);
        taxDetailEntity.setTaxHistory(taxHisId);
        taxDetailEntity.setReportHistory(reportHisId);
        taxDetailEntity.setCreateDate(curDate);
        taxDetailEntity.setCreator(creator);
        taxDetailEntity.setReport(reportId);
        taxDetailEntity.setCreateName(userName);
        taxDetailEntity.setOrg(org);
        taxDetailEntity.setOperation("1");
        taxDetailEntity.setState("0");//状态:0-未报,1-已报
        taxDetailDao.save(taxDetailEntity);//新增detail

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 返回当前机构可用的报表列表
     * 包含了详情数据
     * */
    @Override
    public JsonResult getReportList(User user) {
        //在period表中查找起始月份是所选月的数据,应报报表
        List<AcctReportEntity> list = acctTaxPeriodDao.getReportList(user);
//        List<AcctReportHistoryEntity> list = reportHistoryDao.getReportListByMonth(user,year,month);
//        List<AcctReportHistoryEntity> list = taxDetailDao.getReportListByMonth(user,year,month);
        List<AcctReportEntity> enableList = new ArrayList<>();
        List<AcctReportEntity> disenableList = new ArrayList<>();
        List<AcctTaxEntity> enableTaxList = new ArrayList<>();
        List<AcctTaxEntity> disenableTaxList = new ArrayList<>();
        for (AcctReportEntity item : list) {
            Integer enable = item.getEnabled();
            if (enable.compareTo(1) == 0) {//可用的list
                enableList.add(item);
            }
            else {//不再申报的list
                disenableList.add(item);
            }
            List<AcctTaxEntity> taxlist = item.getTaxList();
            for (AcctTaxEntity tax : taxlist) {
                tax.setReportName(item.getName());
                tax.setReport(item.getId());
                tax.setFrequency(item.getFrequency());
                tax.setPeriodList(item.getPeriodList());
//                tax.setReportEntity(item);
                Integer taxEnabled = tax.getEnabled();
                if (taxEnabled.compareTo(1) == 0) {//可用的tax
                    enableTaxList.add(tax);
                }
                else {//不再申报的list
                    disenableTaxList.add(tax);
                }
            }
        }

        JsonResult result = new JsonResult();
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("enableList",enableList);
        map.put("disenableList",disenableList);
        map.put("enableTaxList",enableTaxList);
        map.put("disenableTaxList",disenableTaxList);
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
     * 返回税种详情
     * */
    @Override
    public JsonResult getTaxDetial(User user, int taxId,int reportId) {
        AcctTaxDetailDto taxDetailDto = acctTaxDao.getTaxDetailDto(user,taxId,reportId);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(taxDetailDto);
        return result;
    }

    /**
     * 返回报表详情
     * */
    @Override
    public JsonResult getReportDetial(User user, int reportId) {
        AcctReportEntity reportEntity = acctReportDao.getReportDetial(user,reportId);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(reportEntity);
        return result;
    }

    @Override
    public JsonResult delTestData(User user) {

        Integer org = user.getOid();
        Integer taxId = 12;
        Integer report = 16;
        Integer updator = 7054;
        Integer accountDetail = 1000;
        BigDecimal factAmount = BigDecimal.valueOf(1000);
        String updateName = "总务";
        String period = "2021-10";
        Date updateDate = new Date();
        Date payTime = new Date();

        AcctTaxDetailEntity detailEntityParam = new AcctTaxDetailEntity();
        detailEntityParam.setFactAmount(factAmount);
        detailEntityParam.setOrg(org);
        detailEntityParam.setTax(taxId);
        detailEntityParam.setReport(report);
        detailEntityParam.setAccountDetail(accountDetail);
        detailEntityParam.setPayTime(payTime);
        detailEntityParam.setUpdator(updator);
        detailEntityParam.setUpdateName(updateName);
        detailEntityParam.setUpdateDate(updateDate);
        detailEntityParam.setPeriod(period);

        int res = setTaxFactAmount(detailEntityParam);


        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(res);
        return result;
    }

    /**
     * 返回杂项详情
     * */
    @Override
    public JsonResult getTriflesDetial(User user, int triflesId) {
        AcctTriflesEntity triflesEntity = triflesDao.getTriflesDetial(user,triflesId);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(triflesEntity);
        return result;
    }

    /**
     * 机构初系统自带的税种
     * */
    @Override
    public void initialSystemTax(Integer org,Integer userId,String userName) {
        //先判断是否已经有了系统自带的税种，有的话不新建，没有的话才新增
        boolean res = acctTaxDao.isExsitSystemTax(org);
        if (!res) {
            /*
            * 系统自带选项暂为
            * “增值税”、
            * “城市维护建设税”、
            * “教育附加”、
            * “企业所得税”、
            * “个人所得税”、
            * “城镇土地使用税”、
            * “房产税”、
            * “印花税”、
            * “土地增值税”、
            * “车船使用税”、
            * “车辆购置税”、
            * “关税”、
            * “社保”、
            * “公积金”
            * */
            Date date = new Date();

            AcctTaxEntity taxEntityZZS = new AcctTaxEntity();
            taxEntityZZS.setName("增值税");
            taxEntityZZS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityZZS.setOrg(org);
            taxEntityZZS.setCreator(userId);
            taxEntityZZS.setCreateName(userName);
            taxEntityZZS.setCreateDate(date);
            taxEntityZZS.setOperation("1");
            taxEntityZZS.setEnabled(1);
            taxEntityZZS.setEnabledTime(date);
            taxEntityZZS.setIsSystem(1);
            taxEntityZZS.setVersionNo(0);
            acctTaxDao.save(taxEntityZZS);
            saveTaxHistory(taxEntityZZS);

            AcctTaxEntity taxEntityCSWHJSS = new AcctTaxEntity();
            taxEntityCSWHJSS.setName("城市维护建设税");
            taxEntityCSWHJSS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityCSWHJSS.setOrg(org);
            taxEntityCSWHJSS.setCreator(userId);
            taxEntityCSWHJSS.setCreateName(userName);
            taxEntityCSWHJSS.setCreateDate(date);
            taxEntityCSWHJSS.setOperation("1");
            taxEntityCSWHJSS.setEnabled(1);
            taxEntityCSWHJSS.setEnabledTime(date);
            taxEntityCSWHJSS.setIsSystem(1);
            taxEntityCSWHJSS.setVersionNo(0);
            acctTaxDao.save(taxEntityCSWHJSS);
            saveTaxHistory(taxEntityCSWHJSS);

            AcctTaxEntity taxEntityJYFJ = new AcctTaxEntity();
            taxEntityJYFJ.setName("教育附加");
            taxEntityJYFJ.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityJYFJ.setOrg(org);
            taxEntityJYFJ.setCreator(userId);
            taxEntityJYFJ.setCreateName(userName);
            taxEntityJYFJ.setCreateDate(date);
            taxEntityJYFJ.setOperation("1");
            taxEntityJYFJ.setEnabled(1);
            taxEntityJYFJ.setEnabledTime(date);
            taxEntityJYFJ.setIsSystem(1);
            taxEntityJYFJ.setVersionNo(0);
            acctTaxDao.save(taxEntityJYFJ);
            saveTaxHistory(taxEntityJYFJ);

            AcctTaxEntity taxEntityQYSDS = new AcctTaxEntity();
            taxEntityQYSDS.setName("企业所得税");
            taxEntityQYSDS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityQYSDS.setOrg(org);
            taxEntityQYSDS.setCreator(userId);
            taxEntityQYSDS.setCreateName(userName);
            taxEntityQYSDS.setCreateDate(date);
            taxEntityQYSDS.setOperation("1");
            taxEntityQYSDS.setEnabled(1);
            taxEntityQYSDS.setEnabledTime(date);
            taxEntityQYSDS.setIsSystem(1);
            taxEntityQYSDS.setVersionNo(0);
            acctTaxDao.save(taxEntityQYSDS);
            saveTaxHistory(taxEntityQYSDS);

            AcctTaxEntity taxEntityGRSDS = new AcctTaxEntity();
            taxEntityGRSDS.setName("个人所得税");
            taxEntityGRSDS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityGRSDS.setOrg(org);
            taxEntityGRSDS.setCreator(userId);
            taxEntityGRSDS.setCreateName(userName);
            taxEntityGRSDS.setCreateDate(date);
            taxEntityGRSDS.setOperation("1");
            taxEntityGRSDS.setEnabled(1);
            taxEntityGRSDS.setEnabledTime(date);
            taxEntityGRSDS.setIsSystem(1);
            taxEntityGRSDS.setVersionNo(0);
            acctTaxDao.save(taxEntityGRSDS);
            saveTaxHistory(taxEntityGRSDS);

            AcctTaxEntity taxEntityCZTDSYS = new AcctTaxEntity();
            taxEntityCZTDSYS.setName("城镇土地使用税");
            taxEntityCZTDSYS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityCZTDSYS.setOrg(org);
            taxEntityCZTDSYS.setCreator(userId);
            taxEntityCZTDSYS.setCreateName(userName);
            taxEntityCZTDSYS.setCreateDate(date);
            taxEntityCZTDSYS.setOperation("1");
            taxEntityCZTDSYS.setEnabled(1);
            taxEntityCZTDSYS.setEnabledTime(date);
            taxEntityCZTDSYS.setIsSystem(1);
            taxEntityCZTDSYS.setVersionNo(0);
            acctTaxDao.save(taxEntityCZTDSYS);
            saveTaxHistory(taxEntityCZTDSYS);

            AcctTaxEntity taxEntityFCS = new AcctTaxEntity();
            taxEntityFCS.setName("房产税");
            taxEntityFCS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityFCS.setOrg(org);
            taxEntityFCS.setCreator(userId);
            taxEntityFCS.setCreateName(userName);
            taxEntityFCS.setCreateDate(date);
            taxEntityFCS.setOperation("1");
            taxEntityFCS.setEnabled(1);
            taxEntityFCS.setEnabledTime(date);
            taxEntityFCS.setIsSystem(1);
            taxEntityFCS.setVersionNo(0);
            acctTaxDao.save(taxEntityFCS);
            saveTaxHistory(taxEntityFCS);

            AcctTaxEntity taxEntityYHS = new AcctTaxEntity();
            taxEntityYHS.setName("印花税");
            taxEntityYHS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityYHS.setOrg(org);
            taxEntityYHS.setCreator(userId);
            taxEntityYHS.setCreateName(userName);
            taxEntityYHS.setCreateDate(date);
            taxEntityYHS.setOperation("1");
            taxEntityYHS.setEnabled(1);
            taxEntityYHS.setEnabledTime(date);
            taxEntityYHS.setIsSystem(1);
            taxEntityYHS.setVersionNo(0);
            acctTaxDao.save(taxEntityYHS);
            saveTaxHistory(taxEntityYHS);

            AcctTaxEntity taxEntityTDZZS = new AcctTaxEntity();
            taxEntityTDZZS.setName("土地增值税");
            taxEntityTDZZS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityTDZZS.setOrg(org);
            taxEntityTDZZS.setCreator(userId);
            taxEntityTDZZS.setCreateName(userName);
            taxEntityTDZZS.setCreateDate(date);
            taxEntityTDZZS.setOperation("1");
            taxEntityTDZZS.setEnabled(1);
            taxEntityTDZZS.setEnabledTime(date);
            taxEntityTDZZS.setIsSystem(1);
            taxEntityTDZZS.setVersionNo(0);
            acctTaxDao.save(taxEntityTDZZS);
            saveTaxHistory(taxEntityTDZZS);

            AcctTaxEntity taxEntityCCS = new AcctTaxEntity();
            taxEntityCCS.setName("车船使用税");
            taxEntityCCS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityCCS.setOrg(org);
            taxEntityCCS.setCreator(userId);
            taxEntityCCS.setCreateName(userName);
            taxEntityCCS.setCreateDate(date);
            taxEntityCCS.setOperation("1");
            taxEntityCCS.setEnabled(1);
            taxEntityCCS.setEnabledTime(date);
            taxEntityCCS.setIsSystem(1);
            taxEntityCCS.setVersionNo(0);
            acctTaxDao.save(taxEntityCCS);
            saveTaxHistory(taxEntityCCS);

            AcctTaxEntity taxEntityGZS = new AcctTaxEntity();
            taxEntityGZS.setName("车辆购置税");
            taxEntityGZS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityGZS.setOrg(org);
            taxEntityGZS.setCreator(userId);
            taxEntityGZS.setCreateName(userName);
            taxEntityGZS.setCreateDate(date);
            taxEntityGZS.setOperation("1");
            taxEntityGZS.setEnabled(1);
            taxEntityGZS.setEnabledTime(date);
            taxEntityGZS.setIsSystem(1);
            taxEntityGZS.setVersionNo(0);
            acctTaxDao.save(taxEntityGZS);
            saveTaxHistory(taxEntityGZS);

            AcctTaxEntity taxEntityGS = new AcctTaxEntity();
            taxEntityGS.setName("关税");
            taxEntityGS.setCategory(1);//类别:1-税,2-社保,3-公积金
            taxEntityGS.setOrg(org);
            taxEntityGS.setCreator(userId);
            taxEntityGS.setCreateName(userName);
            taxEntityGS.setCreateDate(date);
            taxEntityGS.setOperation("1");
            taxEntityGS.setEnabled(1);
            taxEntityGS.setEnabledTime(date);
            taxEntityGS.setIsSystem(1);
            taxEntityGS.setVersionNo(0);
            acctTaxDao.save(taxEntityGS);
            saveTaxHistory(taxEntityGS);

            AcctTaxEntity taxEntitySB = new AcctTaxEntity();
            taxEntitySB.setName("社保");
            taxEntitySB.setCategory(2);//类别:1-税,2-社保,3-公积金
            taxEntitySB.setOrg(org);
            taxEntitySB.setCreator(userId);
            taxEntitySB.setCreateName(userName);
            taxEntitySB.setCreateDate(date);
            taxEntitySB.setOperation("1");
            taxEntitySB.setEnabled(1);
            taxEntitySB.setEnabledTime(date);
            taxEntitySB.setIsSystem(1);
            taxEntitySB.setVersionNo(0);
            acctTaxDao.save(taxEntitySB);
            saveTaxHistory(taxEntitySB);

            AcctTaxEntity taxEntityGJJ = new AcctTaxEntity();
            taxEntityGJJ.setName("公积金");
            taxEntityGJJ.setCategory(3);//类别:1-税,2-社保,3-公积金
            taxEntityGJJ.setOrg(org);
            taxEntityGJJ.setCreator(userId);
            taxEntityGJJ.setCreateName(userName);
            taxEntityGJJ.setCreateDate(date);
            taxEntityGJJ.setOperation("1");
            taxEntityGJJ.setEnabled(1);
            taxEntityGJJ.setEnabledTime(date);
            taxEntityGJJ.setIsSystem(1);
            taxEntityGJJ.setVersionNo(0);
            acctTaxDao.save(taxEntityGJJ);
            saveTaxHistory(taxEntityGJJ);
        }
    }

    private void saveTaxHistory(AcctTaxEntity taxEntity) {
        //新增历史
        AcctTaxHistoryEntity taxHistoryEntity = new AcctTaxHistoryEntity();
        taxHistoryEntity.setTax(taxEntity.getId());
        taxHistoryEntity.setOrg(taxEntity.getOrg());
        taxHistoryEntity.setName(taxEntity.getName());
        taxHistoryEntity.setOldName(taxEntity.getName());
        taxHistoryEntity.setCategory(taxEntity.getCategory());
        taxHistoryEntity.setVersionNo(0);
        taxHistoryEntity.setOperation(taxEntity.getOperation());
        taxHistoryEntity.setCreateDate(new Date());
        taxHistoryEntity.setCreator(taxEntity.getCreator());
        taxHistoryEntity.setCreateName(taxEntity.getCreateName());
        taxHistoryEntity.setEnabled(taxEntity.getEnabled());
        taxHistoryEntity.setEnabledTime(taxEntity.getEnabledTime());
        taxHistoryEntity.setIsSystem(taxEntity.getIsSystem());
        taxHistoryDao.save(taxHistoryEntity);//新增tax历史
    }

    /**
     * PC端录入税款后需要给税种的实际金额赋值
     * org
     * taxId            税种ID
     * report           报表ID
     * factAmount       实际金额
     * period           报表需申报的月份，格式yyyy-MM  PC端选择的某条报税记录中的起日期
     * payTime          缴款时间
     * accountDetail    财务明细账ID
     * updator          修改人ID
     * updateName       修改人名称
     * updateDate       修改日期
     * */
    @Override
    public Integer setTaxFactAmount(AcctTaxDetailEntity detailEntity) {
        Integer res = taxDetailDao.setTaxFactAmount(detailEntity);//保存税款信息到detail表，factAmount，accountDetail，payTime
        return res;
    }

    /**
     * 返回李娅星用到的税款方面的数据
     * id   setTaxFactAmount 这个方法返回的ID
     * */
    @Override
    public Map<String,Object> getById(Integer id) {
        Map<String,Object> res = new HashMap<>();
        AcctTaxDetailHistoryEntity detailHistoryEntity = taxDetailHistoryDao.get(id);
        Integer taxHistory = detailHistoryEntity.getTaxHistory();
        Integer report = detailHistoryEntity.getReport();
        String periodBegin = detailHistoryEntity.getPeriodBegin();
        String periodEnd = detailHistoryEntity.getPeriodEnd();
        String taxName = "";
        if (taxHistory != null) {
            AcctTaxHistoryEntity taxHistoryEntity = taxHistoryDao.get(taxHistory);
            taxName = taxHistoryEntity.getName();
        }
        else {
            AcctTaxEntity taxEntity = acctTaxDao.get(detailHistoryEntity.getTax());
            taxName = taxEntity.getName();
        }
        AcctTaxPeriodEntity periodEntity = new AcctTaxPeriodEntity();
        periodEntity.setReport(report);
        periodEntity.setOrg(detailHistoryEntity.getOrg());
        periodEntity.setBeginMonth(Integer.parseInt(periodBegin.substring(4,6)));
        periodEntity.setBeginDate(Integer.parseInt(periodBegin.substring(6,8)));
        periodEntity.setEndMonth(Integer.parseInt(periodEnd.substring(4,6)));
        periodEntity.setEndDate(Integer.parseInt(periodEnd.substring(6,8)));
        periodEntity = acctTaxPeriodDao.getByDate(periodEntity);
        if (periodEntity != null) {
            res.put("periodId",periodEntity.getId());
        }
        else
            res.put("periodId",null);
        res.put("taxName",taxName);
        res.put("report",report);
        res.put("periodBegin",periodBegin);
        res.put("periodEnd",periodEnd);
        return res;
    }

    /**
     * 新增税种，只录名称
     */
    @Override
    public JsonResult insertTax(User user, AcctTaxEntity taxEntity) {
        Date date = new Date();
        Integer creator = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();
        String name = taxEntity.getName();

        boolean nameFlag = acctTaxDao.checkTaxName(name,org);//true-没有重名，false-有重名
        if (!nameFlag) {
            JsonResult result = new JsonResult();
            result.setSuccess(0);
            result.setData("操作失败！因为系统不支持同名税种。请输入其他名称！");
            return result;
        }

        taxEntity.setOrg(user.getOid());
        taxEntity.setCreator(user.getUserID());
        taxEntity.setCreateName(user.getUserName());
        taxEntity.setCreateDate(date);
        taxEntity.setOperation("1");
        taxEntity.setEnabled(1);
        taxEntity.setEnabledTime(date);
        taxEntity.setVersionNo(0);
        acctTaxDao.save(taxEntity);//新增税种
        Integer taxId = taxEntity.getId();

        //新增历史
        AcctTaxHistoryEntity taxHistoryEntity = new AcctTaxHistoryEntity();
        taxHistoryEntity.setTax(taxId);
        taxHistoryEntity.setOrg(org);
        taxHistoryEntity.setName(name);
        taxHistoryEntity.setOldName(name);
        taxHistoryEntity.setVersionNo(0);
        taxHistoryEntity.setCreateDate(new Date());
        taxHistoryEntity.setCreator(creator);
        taxHistoryEntity.setCreateName(userName);
        taxHistoryDao.save(taxHistoryEntity);//新增tax历史

//        Map<String,Object> res = new HashMap<String,Object>();
//        res.put("id",taxId);
//        res.put("taxHisId",taxHistoryEntity.getId());
        JsonResult result = new JsonResult();
        result.setSuccess(1);
//        result.setData(res);
        return result;
    }

    /**
     * 返回税种列表
     * PC端不返回社保和公积金，手机端全返回
     * type     pc端不用传，手机端传1
     */
    @Override
    public JsonResult taxList(User user,Integer type) {
        Integer org = user.getOid();
        //如果该机构没有系统自带的税种，需要先生成
        initialSystemTax(org,user.getUserID(),user.getUserName());
        List<AcctTaxEntity> list = acctTaxDao.getListByOrg(org);
        if (type == null) {
            //PC端不返回社保和公积金
            list.removeIf(p -> "社保".equals(p.getName()) || "公积金".equals(p.getName()));
        }
        JsonResult result = new JsonResult();
        result.setSuccess(0);
        result.setData(list);
        return result;
    }

    /**
     * 删除税种
     * 系统自带的不能删，已绑定报表的不能删
     */
    @Override
    public JsonResult delTax(User user, Integer id) {
        JsonResult result = new JsonResult();
        Integer org = user.getOid();
        AcctTaxEntity taxEntity = acctTaxDao.get(id);
        Integer isSystem = taxEntity.getIsSystem();
        if (isSystem != null) {
            result.setSuccess(0);
            result.setData("因为系统自带的税种不能删除！");
            return result;
        }
        List<AcctTaxReportEntity> taxReportList = taxReportDao.getTaxReportByTaxId(org,id);//检查税种是否已经绑定了报表
        if (taxReportList.size() > 0) {
            result.setSuccess(0);
            result.setData("因为已有报税数据的税种不能删除！");
            return result;
        }
        taxHistoryDao.deleteByTaxId(org,id);
        acctTaxDao.deleteById(id);
        result.setSuccess(1);
        return result;
    }

    /**
     * 返回PC端需要的申报记录
     * startDate    开始日期
     * endDate      截止日期
     * taxId        税种ID
     */
    @Override
    public JsonResult getApplyTaxList(User user,Integer taxId, Date startDate, Date endDate) {
        JsonResult result = new JsonResult();
        Integer org = user.getOid();
        if (startDate == null || endDate == null) {
            result.setSuccess(0);
            result.setData("开始日期和截止日期不能为空");
            return result;
        }

        if (endDate.getTime() < startDate.getTime()) {
            result.setSuccess(0);
            result.setData("截止日期不能小于开始日期");
            return result;
        }

        List<AcctTaxReportEntity> taxReportList = taxReportDao.getTaxReportByTaxId(org,taxId);
        if (taxReportList.size() > 0) {
            Integer reportId = taxReportList.get(0).getReport();
            List<BSBean> list = new ArrayList<>();
            Integer startMonth = NewDateUtils.getMonth(startDate);
            String yearMonth = NewDateUtils.dateToString(startDate,"yyyy-MM");

            BSBean dateBean = new BSBean();
            dateBean.setMonth(startMonth);
            dateBean.setYearMonthDay(yearMonth + "-01");
            dateBean.setYearMonth(yearMonth);
            list.add(dateBean);
            String endDateStr = NewDateUtils.dateToString(endDate,"yyyy-MM");
            Integer smonth = NewDateUtils.getMonth(startDate);
            Integer emonth = NewDateUtils.getMonth(endDate);
            Integer syear = NewDateUtils.getYear(startDate);
            Integer eyear = NewDateUtils.getYear(endDate);
            if (smonth.compareTo(emonth) < 0 || syear.compareTo(eyear) < 0) {
                while (true) {
                    Date nextMonthDate = NewDateUtils.changeMonth(startDate,1);
                    Integer nextMonth = NewDateUtils.getMonth(nextMonthDate);
                    BSBean dateBeanNext = new BSBean();
                    dateBeanNext.setMonth(nextMonth);
                    String ym = NewDateUtils.dateToString(nextMonthDate,"yyyy-MM");
                    dateBeanNext.setYearMonthDay(ym + "-01");
                    dateBeanNext.setYearMonth(ym);
                    list.add(dateBeanNext);
                    startDate = nextMonthDate;
                    if (endDateStr.startsWith(ym)) {
                        break;
                    }
                }
            }


            AcctTaxPeriodEntity periodEntityParam = new AcctTaxPeriodEntity();
            periodEntityParam.setOrg(org);
            periodEntityParam.setReport(reportId);

            List<BSBean> resultList = new ArrayList<>();

            for (BSBean bean : list) {
                String ymd = bean.getYearMonthDay();
                String ym = bean.getYearMonth();
                periodEntityParam.setBeginMonth(bean.getMonth());
                periodEntityParam.setCreateDate(NewDateUtils.dateFromString(ymd,"yyyy-MM-dd"));
                List<AcctTaxPeriodEntity> periodList = acctTaxPeriodDao.getByBeginMonth(periodEntityParam);
                if (periodList.size() > 0) {
                    AcctTaxPeriodEntity periodEntity = periodList.get(0);
                    Integer beginDay = periodEntity.getBeginDate();
                    Integer endDay = periodEntity.getEndDate();
                    bean.setStartDate(ym + "-" + String.format("%02d",beginDay));
                    bean.setEndDate(ym + "-" + String.format("%02d",endDay));
                    bean.setId(periodEntity.getId());
                    bean.setReport(periodEntity.getReport());
                    resultList.add(bean);
                }
            }

            result.setSuccess(1);
            result.setData(resultList);
            return result;
        }
        else {
            result.setSuccess(1);
            result.setData("");
            return result;
        }

    }

    /**
     * 返回某年月内所有已缴税列表
     * period   yyyy-MM
     */
    @Override
    public JsonResult getTaxedList(User user, String period) {
        List<AlreadyDeclaredTaxDto> list = taxDetailDao.getTaxedList(user,period);
        for (AlreadyDeclaredTaxDto dto : list) {
            Integer accountDetail = dto.getAccountDetail();
            /*
            * map.put("method",method);  //支出方式
              map.put("financeAccountBillImages",financeAccountBillImages);  //图片信息
              * method:0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                financeAccountBillImages:图片信息{
                    id：id
                    org：机构ID
                    accountDetail：帐户明细ID
                    uplaodPath：文件上传路径
                }
            * */
            Map<String,Object> map = dataService.getTaxMethod(user,accountDetail);
            String method = (String) map.get("method");
            List<FinanceAccountBillImage> images = (List<FinanceAccountBillImage>) map.get("financeAccountBillImages");
            dto.setMethod(method);
            dto.setImages(images);

        }
        Map<String,Object> map = new HashMap<>();
        map.put("today",new Date());
        map.put("list",list);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
    * 返回已缴税款的编辑记录
    * */
    @Override
    public JsonResult getPayTaxList(User user, Integer detailId) {
        List<PayTaxDto> list = taxDetailHistoryDao.getPayTaxList(user,detailId);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(list);
        return result;
    }

    /**
     * 返回税种ID
     * type;  类型:1-工资,2-所得税,3-社保,4-公积金
     *
     * */
    @Override
    public Integer getTaxId(byte type, Integer org) {
        Integer taxId = acctTaxDao.getTaxId(type,org);
        return taxId;
    }

    /**
     * 释放某条申报记录
     * 税款修改后如果审批驳回的话需要释放之前选的申报记录
     * id   要释放锁定状态的申报记录ID
     * */
    @Override
    public int releasePeriodById(Integer id) {
        AcctTaxPeriodEntity periodEntity = acctTaxPeriodDao.get(id);
        if (periodEntity != null) {
            periodEntity.setEnabled(1);
            return 1;
        }
        return 0;
    }

    /**
     * 获取报税事务报表列表
     * 未来时间：查period，月份匹配，name用原表；已报为0
     * 历史时间： detail里的：detail里period字段 = 查询年月；
     *          period里的：创建时间小于等于查询时间；是全的，要去掉detail里重复的
     * */
    @Override
    public JsonResult getReportByDate(User user, int year, int month) {
//        String str = "" + year + "-" + String.format("%02d",month);
        Integer org = user.getOid();
        //如果该机构没有系统自带的税种，需要先生成
        initialSystemTax(org,user.getUserID(),user.getUserName());
//        List<AcctReportDetailDto> reportList = acctReportDao.getByPeriod(org,year,month);//全的，包含了所有的应报和已报
        Map<String,Object> reportListMap = acctReportDao.getByPeriod(user,year,month);//全的，包含了所有的应报和已报
        List<AcctReportDetailDto> detailReportList = acctReportDao.getListFromDetail(user,year,month);//已报列表
        List<AcctReportDetailDto> reportList = (List<AcctReportDetailDto>) reportListMap.get("list");
        int reportNum = reportList.size();//应报报表数
        int reportedNum = detailReportList.size();//已报报表数
        int taxNum = 0;//应报税种数
        int taxedNum = 0;//已报税种数
        int switchState = 0;
        List<AcctTaxDto> allTaxList = new ArrayList<>();//所有税种集合
        for (AcctReportDetailDto reportEntity : reportList) {//把已报的一些属性放到应报里面去
            Integer reportId = reportEntity.getReportId();
//            if (reportId.compareTo(39) == 0) {
//                System.out.println(11);
//            }
            for (AcctReportDetailDto detailReportEntity : detailReportList) {
                Integer detailReportId = detailReportEntity.getReportId();
                List<AcctTaxDto> taxDtoList = detailReportEntity.getTaxList();
                if (switchState == 0) {
                    taxedNum += taxDtoList.size();
                }
                if (detailReportId.compareTo(reportId) == 0) {
                    reportEntity.setCreateName(detailReportEntity.getCreateName());
                    reportEntity.setCreateDate(detailReportEntity.getCreateDate());
                    reportEntity.setFactReportDate(detailReportEntity.getFactReportDate());
                    reportEntity.setReportName(detailReportEntity.getReportName());
                    reportEntity.setStartPeriod(detailReportEntity.getStartPeriod());
                    reportEntity.setEndPeriod(detailReportEntity.getEndPeriod());
                    reportEntity.setLatestDeclare(detailReportEntity.getLatestDeclare());
                    reportEntity.setTaxList(taxDtoList);
                }
            }
            switchState = 1;
            List<AcctTaxDto> taxList = reportEntity.getTaxList();
            taxNum += taxList.size();
            allTaxList.addAll(taxList);
        }

        BigDecimal totalFactAmount = (BigDecimal) reportListMap.get("totalFactAmount");
        JsonResult result = new JsonResult();
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("reportNum",reportNum);//应报报表数
        map.put("reportedNum",reportedNum);//已报报表数
        map.put("taxNum",taxNum);//应报税种数
        map.put("taxedNum",taxedNum);//已报税种数
        map.put("allReportList",reportList);//所有报表
        map.put("alltaxList",allTaxList);//所有税种
        map.put("totalFactAmount",totalFactAmount);//查询月份的缴税总额
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
     * 报表修改记录
     * */
    @Override
    public JsonResult reportUpdateRecord(User user, int reportId) {
        Integer org = user.getOid();
        List<AcctReportHistoryEntity> list = reportHistoryDao.reportUpdateRecord(org,reportId);
        JsonResult result = new JsonResult();
//        Map<String,Object> map = new HashMap<String,Object>();
//        map.put("reportNum",reportNum);//应报报表数
//        map.put("reportedNum",reportedNum);//已报报表数
//        map.put("taxNum",taxNum);//应报税种数
//        map.put("taxedNum",taxedNum);//已报税种数
//        map.put("allReportList",reportList);//所有报表
//        map.put("alltaxList",allTaxList);//所有税种
        result.setSuccess(1);
        result.setData(list);
        return result;
    }

    /**
     * 获取税种的修改记录
     * */
    @Override
    public JsonResult taxUpdateRecord(User user, int taxId) {
        Integer org = user.getOid();
        List<AcctTaxHistoryEntity> list = taxHistoryDao.taxUpdateRecord(org,taxId);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(list);
        return result;
    }

    /**
     * 报表状态改成不再申报
     * */
    @Override
    public JsonResult setReportDisenabled(User user, Integer reportId) {
        AcctReportEntity reportEntity = acctReportDao.get(reportId);
        reportEntity.setEnabled(0);
        reportEntity.setEnabledTime(new Date());
        reportEntity.setUpdator(user.getCreator());
        reportEntity.setUpdateName(user.getUserName());
        reportEntity.setUpdateDate(new Date());
        acctTaxDao.setDisenabled(user,reportId);
        //目前不再申报后不能再恢复，所以可以找到最后一条数据进行更新
//        reportHistoryDao.setDisenabled(reportId);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 报表状态改成不再申报
     * */
    @Override
    public JsonResult setTaxDisenabled(User user, Integer taxId) {
        JsonResult result = new JsonResult();
        AcctTaxEntity taxEntity = acctTaxDao.get(taxId);
        if (taxEntity != null) {
            taxEntity.setEnabled(0);
            taxEntity.setEnabledTime(new Date());
            taxEntity.setUpdator(user.getCreator());
            taxEntity.setUpdateName(user.getUserName());
            taxEntity.setUpdateDate(new Date());
            result.setSuccess(1);
        }
        else {
            result.setSuccess(0);
        }
        return result;
    }

    /**
     * 报税
     * taxArray:{"data":[{"id":1,"taxId":1,"fact_amount":1000}]}
     * @param taxDate     实际申报的日期
     * @param detailId 有值说明已报过税
     * @param reportHisId 历史报表ID
     * 1、更新report表中的latestDeclare字段值
     * 2、判断taxdetail表中是否已经有了在给定年月的报税数据，没有的话新增，有的话不再新增
     * 3、新增编辑记录，在taxdetail和taxreport的历史表里
     * */
    @Override
    public JsonResult setReportTax(Integer detailId,int reportId, int reportHisId,String taxArray, String taxDate,User user) {
        Date date = NewDateUtils.dateFromString(taxDate,"yyyy-MM-dd");
        Integer userID = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();
        Date curDate = new Date();
        String str = taxDate.replace("-","");
        String period = taxDate.substring(0,7);//yyyy-MM

        String latestDeclare = NewDateUtils.dateToString(curDate,"yyyyMMdd");
        reportHistoryDao.get(reportHisId).setLatestDeclare(latestDeclare);
        acctReportDao.get(reportId).setLatestDeclare(latestDeclare);

//        taxDetailDao.updateTaxLatestDeclare();//更新税种的LatestDeclare字段，"不再申报"按钮点击时如果改字段有值需要给再次确认的提示
        JSONObject jsonObject = new JSONObject(taxArray);
        JSONArray data = jsonObject.getJSONArray("data");
        List<AcctTaxDetailEntity> taxList = new ArrayList<>();//税种集合
        for (int i=0;i<data.length();i++) {
            JSONObject jObject = data.getJSONObject(i);
            AcctTaxDetailEntity detailEntity = new AcctTaxDetailEntity();
            Integer taxHisId = jObject.optInt("taxHisId",0);//税种历史ID
            Integer taxId = jObject.optInt("taxId",0);
            detailEntity.setTax(taxId);
            detailEntity.setTaxHistory(taxHisId);
            detailEntity.setReport(reportId);
            detailEntity.setReportHistory(reportHisId);
            BigDecimal planAmount = jObject.optBigDecimal("planAmount",null);
//            detailEntity.setFactAmount(factAmount);
            detailEntity.setPlanAmount(planAmount);
            detailEntity.setDelareTime(date);//实际报税时间
            detailEntity.setPeriod(period);
            detailEntity.setCreateDate(curDate);
            detailEntity.setOrg(org);
            detailEntity.setCreateName(userName);
            detailEntity.setState("1");
            detailEntity.setCreator(userID);
            detailEntity.setOperation("1");
            taxList.add(detailEntity);
        }

        //判断taxdetail表中是否已经有了在给定年月的报税数据，没有的话新增，有的话不再新增
        if (detailId != null) {//已经有了该年月的报税数据，不再新增，只写编辑记录，需要把已经有的报税数据更新成最新的数据
            AcctTaxDetailEntity taxDetailEntity = taxDetailDao.get(detailId);
            taxDetailEntity.setDelareTime(date);
            taxDetailEntity.setState("1");
            taxDetailEntity.setOperation("3");
            taxDetailEntity.setCreateDate(curDate);
            taxDetailEntity.setCreator(userID);
            taxDetailEntity.setCreateName(userName);
            taxDetailEntity.setUpdator(userID);
            taxDetailEntity.setUpdateName(userName);
            taxDetailEntity.setUpdateDate(curDate);
            for (AcctTaxDetailEntity detailParam : taxList) {
                detailParam.setPeriod(str.substring(0,6));
                taxDetailDao.updateByTaxId(detailParam);//更新taxHistory和factAmount
            }
            //写编辑记录
            List<AcctTaxDetailEntity> list = taxDetailDao.getListByDeclareDate(reportId,period);
            insertEditRecord(list,user);

        }
        else {
            //先新增再写编辑记录
            List<AcctTaxDetailEntity> listTaxDetail = new ArrayList<>();
            AcctTaxReportEntity taxReportEntity = new AcctTaxReportEntity();
            taxReportEntity.setReport(reportId);
            String hql = "from AcctTaxReportEntity where report=?0 and tax=null";
            taxReportEntity = taxReportDao.getByHQL(hql,new Object[]{reportId});
            Integer taxReportId = taxReportEntity.getId();
            AcctTaxDetailEntity taxDetailEntity = new AcctTaxDetailEntity();
            taxDetailEntity.setOrg(org);
            taxDetailEntity.setReport(reportId);
            taxDetailEntity.setReportHistory(reportHisId);
            taxDetailEntity.setDelareTime(date);
            taxDetailEntity.setTaxReport(taxReportId);
            taxDetailEntity.setState("1");
            taxDetailEntity.setPeriod(period);
            taxDetailEntity.setCreator(userID);
            taxDetailEntity.setCreateName(userName);
            taxDetailEntity.setCreateDate(curDate);
            taxDetailEntity.setOperation("1");
            taxDetailDao.save(taxDetailEntity);
            listTaxDetail.add(taxDetailEntity);
            for (AcctTaxDetailEntity taxEntity : taxList) {
                Integer taxId = taxEntity.getTax();
                String hql2 = "from AcctTaxReportEntity where report=?0 and tax=?1";
                taxReportEntity = taxReportDao.getByHQL(hql2,new Object[]{reportId,taxId});
                Integer taxReportId2 = taxReportEntity.getId();
                taxEntity.setTaxReport(taxReportId2);
                taxDetailDao.save(taxEntity);
                listTaxDetail.add(taxEntity);
                acctTaxDao.get(taxId).setLatestDeclare(str);
            }

            //写编辑记录
            insertEditRecord(listTaxDetail,user);

        }

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 获取编辑记录
     * reportId     报表ID
     * period       yyyy-MM
     * */
    @Override
    public JsonResult getEditRecord(User user, int reportHistory,int reportId, String period) {
        // TODO: 2021/9/24 编辑记录的数据都是来自t_accountant_tax_detail_history表，
        Integer org = user.getOid();
        AcctTaxDetailHistoryEntity param = new AcctTaxDetailHistoryEntity();
        param.setPeriod(period);
        param.setReport(reportId);
        param.setOrg(org);
        param.setReportHistory(reportHistory);
        param.setCreator(user.getUserID());
        Map<String,Object> map = taxDetailHistoryDao.getEditRecord(param);

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
     * 报税记录
     * @type    1-报表的报税记录，2-税种的报税记录
     * */
    @Override
    public JsonResult getReportTaxRecord(User user, Integer reportId, Integer taxId,String year,int type) {
        // TODO: 2021/9/24 报税记录数据主要来自t_accountant_tax_detail
        if (year == null) {
            Calendar c = Calendar.getInstance();
            int yearInt = c.get(Calendar.YEAR);
            year = "" + yearInt;
        }

        AcctTaxDetailEntity taxDetailEntity = new AcctTaxDetailEntity();
        taxDetailEntity.setPeriod(year);
        taxDetailEntity.setOrg(user.getOid());
        taxDetailEntity.setState("1");//已报
        taxDetailEntity.setReport(reportId);
//        if (type == 1) {//报表的报税记录
//        }
        if (type == 2) {//税种的报税记录
            taxDetailEntity.setTax(taxId);
        }
        Map<String,Object> map = taxDetailDao.getReportTaxRecord(taxDetailEntity,type);//报税记录
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
     * 修改报表名称
     * @param reportId     报表ID
     * @param type         1-改错别字，2-改新名称，3-改日期范围
     * @param name         新名称
     * @param periodArray {"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}
     * 改错别字需要改历史数据，改新名称不需要改历史
     */
    @Override
    public JsonResult updateReportName(User user, int reportId, String name,String type,String periodArray) {
        Date curDate = new Date();
        Integer creator = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();
        Integer reportHisId = null;
        AcctReportEntity reportEntity = acctReportDao.get(reportId);

        //改完名称需要写修改记录
        AcctReportHistoryEntity reportHistoryEntity = new AcctReportHistoryEntity();
        reportHistoryEntity.setReport(reportId);
        reportHistoryEntity.setName(name);
        reportHistoryEntity.setOldName(name);
        reportHistoryEntity.setOrg(org);
        reportHistoryEntity.setCreateDate(curDate);
        reportHistoryEntity.setCreator(creator);
        reportHistoryEntity.setCreateName(userName);
        reportHistoryEntity.setEnabled(reportEntity.getEnabled());
        reportHistoryEntity.setLatestDeclare(reportEntity.getLatestDeclare());
        reportHistoryEntity.setFrequency(reportEntity.getFrequency());
        reportHistoryEntity.setVersionNo(0);
        reportHistoryDao.save(reportHistoryEntity);//新增report历史表
        reportHisId = reportHistoryEntity.getId();

        switch (type) {
            case "1":
                //改错别字需要改历史数据
                if ("1".equals(type)) {
                    acctReportDao.queryHql("update AcctReportHistoryEntity set name=?0 where report=?1",
                            new Object[]{name,reportId});
//                    reportEntity.setName(name);
                }
            case "2":
//                acctReportDao.queryHql("update AcctReportEntity set name=?0,versionNo=versionNo+1 where id=?1",
//                        new Object[]{name,reportId});
                reportEntity.setName(name);
                reportEntity.setUpdateName(userName);
                reportEntity.setUpdateDate(curDate);
                reportEntity.setUpdator(creator);
                Integer version = reportEntity.getVersionNo();
                reportEntity.setVersionNo(version + 1);

                break;
            case "3"://改日期范围,需要更新period原表数据
                JSONObject jsonObject = new JSONObject(periodArray);
                JSONArray data = jsonObject.getJSONArray("data");
                for (int i=0;i<data.length();i++) {
                    JSONObject pObject = data.getJSONObject(i);
                    //{"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}
                    int periodId = pObject.optInt("periodId",0);
                    int beginMonth = pObject.optInt("beginMonth",0);
                    int beginDate = pObject.optInt("beginDate",0);
                    int endMonth = pObject.optInt("endMonth",0);
                    int endDate = pObject.optInt("endDate",0);
                    AcctTaxPeriodEntity periodEntity = acctTaxPeriodDao.get(periodId);
                    periodEntity.setBeginMonth(beginMonth);
                    periodEntity.setBeginDate(beginDate);
                    periodEntity.setEndMonth(endMonth);
                    periodEntity.setEndDate(endDate);

                }

        }
        //写period history 表
        JSONObject jsonObject = new JSONObject(periodArray);
        JSONArray data = jsonObject.getJSONArray("data");
        for (int i=0;i<data.length();i++) {
            JSONObject pObject = data.getJSONObject(i);
            //{"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}
            int periodId = pObject.optInt("periodId",0);
            int beginMonth = pObject.optInt("beginMonth",0);
            int beginDate = pObject.optInt("beginDate",0);
            int endMonth = pObject.optInt("endMonth",0);
            int endDate = pObject.optInt("endDate",0);
            AcctTaxPeriodHistoryEntity periodHistoryEntity = new AcctTaxPeriodHistoryEntity();
            periodHistoryEntity.setTaxPeriod(periodId);
            periodHistoryEntity.setReport(reportId);
            periodHistoryEntity.setReportHistory(reportHisId);
            periodHistoryEntity.setBeginMonth(beginMonth);
            periodHistoryEntity.setBeginDate(beginDate);
            periodHistoryEntity.setEndMonth(endMonth);
            periodHistoryEntity.setEndDate(endDate);
            periodHistoryEntity.setOrg(org);
            periodHistoryEntity.setVersionNo(0);
            periodHistoryEntity.setCreator(creator);
            periodHistoryEntity.setCreateName(userName);
            periodHistoryEntity.setCreateDate(curDate);
            periodHistoryDao.save(periodHistoryEntity);
        }

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 修改税种
     * @param taxId         税种ID
     * @param name          新名称
     * @param type          1-改错别字，2-改新名称,3-变更报表
     * @param curReportId   当前税种已绑定的报表ID
     * @param newReportId   新绑定的报表ID
     * 改错别字需要改历史数据，改新名称不需要改历史
     */
    @Override
    public JsonResult updateTax(User user, int taxId, String name, String type, Integer curReportId,Integer newReportId) {
        Date curDate = new Date();
        Integer creator = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();
        AcctTaxEntity taxEntity = acctTaxDao.get(taxId);
        AcctTaxHistoryEntity taxHistoryEntity = new AcctTaxHistoryEntity();//税种历史
        taxHistoryEntity.setTax(taxId);
        taxHistoryEntity.setName(name);
        taxHistoryEntity.setOldName(name);
        taxHistoryEntity.setOrg(org);
        taxHistoryEntity.setCreator(creator);
        taxHistoryEntity.setCreateName(userName);
        taxHistoryEntity.setCreateDate(curDate);
        taxHistoryDao.save(taxHistoryEntity);
        AcctTaxReportEntity taxReportEntity = taxReportDao.getByTaxAndReport(taxId,curReportId);
        //新增taxReportHistory
        AcctTaxReportHistoryEntity taxReportHistoryEntity = new AcctTaxReportHistoryEntity();
        taxReportHistoryEntity.setOrg(org);
        taxReportHistoryEntity.setTaxReport(taxReportEntity.getId());
        taxReportHistoryEntity.setTax(taxId);
        Integer newReportHisId = null;
        if (newReportId != null) {
            taxReportHistoryEntity.setReport(newReportId);
            AcctReportHistoryEntity reportHistory = reportHistoryDao.getLastByReportId(newReportId);
            if (reportHistory != null) {
                newReportHisId = reportHistory.getId();
            }
        }
        else {
            taxReportHistoryEntity.setReport(curReportId);
        }
        if (newReportHisId != null) {
            taxReportHistoryEntity.setReportHistory(newReportHisId);
        }
        else {
            taxReportHistoryEntity.setReportHistory(taxReportEntity.getReportHistory());
        }
        taxReportHistoryEntity.setTaxHistory(taxHistoryEntity.getId());
        taxReportHistoryEntity.setCreator(creator);
        taxReportHistoryEntity.setCreateName(userName);
        taxReportHistoryEntity.setCreateDate(curDate);
        taxReportHistoryDao.save(taxReportHistoryEntity);//新增taxReportHistory

        switch (type) {
            case "1"://改错别字,需要改历史数据
                if ("1".equals(type)) {//错别字
                    acctTaxDao.updateHisTaxName(taxId,name);//修改tax历史表的name
                }
            case "2"://改新名称,只改原表
                taxEntity.setName(name);
                taxEntity.setUpdateName(userName);
                taxEntity.setUpdateDate(curDate);
                taxEntity.setUpdator(creator);
                Integer version = taxEntity.getVersionNo();
                taxEntity.setVersionNo(version + 1);
                break;
            case "3"://变更报表
                taxReportEntity.setReport(newReportId);
                break;

        }

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**新增编辑记录*/
    private void insertEditRecord(List<AcctTaxDetailEntity> taxDetailList,User user) {
        // TODO: 2021/9/23 往tax_detail_history表里写数据，这个表里的数据作为编辑记录显示
        Integer org = user.getOid();
        Integer userID = user.getUserID();
        String userName = user.getUserName();
        Date curDate = new Date();

        for (AcctTaxDetailEntity taxDetailEntity : taxDetailList) {
            AcctTaxDetailHistoryEntity taxDetailHistoryEntity = new AcctTaxDetailHistoryEntity();
            Integer reportId = taxDetailEntity.getReport();
            Date date = taxDetailEntity.getDelareTime();
            Integer taxId = taxDetailEntity.getTax();
            BigDecimal factAmount = taxDetailEntity.getFactAmount();
            Integer taxReport = taxDetailEntity.getTaxReport();
            Integer id = taxDetailEntity.getId();
            String period = taxDetailEntity.getPeriod();
            Integer reportHisId = taxDetailEntity.getReportHistory();
            Integer taxHisId = taxDetailEntity.getTaxHistory();

            taxDetailHistoryEntity.setReportHistory(reportHisId);
            taxDetailHistoryEntity.setTaxHistory(taxHisId);
            taxDetailHistoryEntity.setPeriod(period);
            taxDetailHistoryEntity.setTaxDetail(id);
            taxDetailHistoryEntity.setOrg(org);
            taxDetailHistoryEntity.setReport(reportId);
            taxDetailHistoryEntity.setDelareTime(date);
            taxDetailHistoryEntity.setState("1");
            taxDetailHistoryEntity.setCreator(userID);
            taxDetailHistoryEntity.setCreateName(userName);
            taxDetailHistoryEntity.setCreateDate(curDate);
            taxDetailHistoryEntity.setOperation("1");
            taxDetailHistoryEntity.setTaxReport(taxReport);
            taxDetailHistoryEntity.setTax(taxId);
            taxDetailHistoryEntity.setFactAmount(factAmount);
            taxDetailHistoryDao.save(taxDetailHistoryEntity);
        }

    }

    /**
     * 新增杂项
     * @param name          杂项名
     * @param frequency     频次
     * @param periodArray   {"data":[{"beginDate":"9月1日","endDate":"9月10日"},{"beginDate":"9月1日","endDate":"9月10日"}]}
     */
    @Override
    public JsonResult addTrifles(User user, String name, String frequency,String periodArray) {
        Integer org = user.getOid();
        Date curDate = new Date();
        Integer creator = user.getUserID();
        String userName = user.getUserName();
        AcctTriflesEntity triflesEntity = new AcctTriflesEntity();
        triflesEntity.setOrg(org);
        triflesEntity.setName(name);
        triflesEntity.setFrequency(frequency);
        triflesEntity.setCreator(creator);
        triflesEntity.setCreateDate(curDate);
        triflesEntity.setCreateName(userName);
        triflesEntity.setVersionNo(0);
        triflesEntity.setEnabled(1);
        triflesEntity.setEnabledTime(curDate);
        triflesDao.save(triflesEntity);//新增杂项
        Integer triflesId = triflesEntity.getId();
        AcctTriflesHistoryEntity triflesHistoryEntity = new AcctTriflesHistoryEntity();
        triflesHistoryEntity.setOrg(org);
        triflesHistoryEntity.setTrifles(triflesId);
        triflesHistoryEntity.setFrequency(triflesEntity.getFrequency());
        triflesHistoryEntity.setName(name);
        triflesHistoryEntity.setOldName(name);
        triflesHistoryEntity.setEnabled(triflesEntity.getEnabled());
        triflesHistoryEntity.setEnabledTime(triflesEntity.getEnabledTime());
        triflesHistoryEntity.setCreator(creator);
        triflesHistoryEntity.setCreateDate(curDate);
        triflesHistoryEntity.setCreateName(userName);
        triflesHistoryDao.save(triflesHistoryEntity);//新增杂项历史

        JSONObject jsonObject = new JSONObject(periodArray);
        JSONArray data = jsonObject.getJSONArray("data");
        for (int i=0;i<data.length();i++) {
            JSONObject item = data.getJSONObject(i);
            AcctTriflesPeriodEntity triflesPeriodEntity = new AcctTriflesPeriodEntity();
            triflesPeriodEntity.setOrg(org);
            triflesPeriodEntity.setTrifles(triflesId);
            triflesPeriodEntity.setCreator(creator);
            triflesPeriodEntity.setCreateName(userName);
            triflesPeriodEntity.setCreateDate(curDate);
            triflesPeriodEntity.setEnabled(1);
            triflesPeriodEntity.setEnabledTime(curDate);
            String beginDate = item.optString("beginDate","");
            String endDate = item.optString("endDate","");
            if (beginDate.length() > 0) {
                int yindex = beginDate.indexOf("月");
                int rindex = beginDate.indexOf("日");
                String month = beginDate.substring(0,yindex);
                String day = beginDate.substring(yindex + 1,rindex);
                triflesPeriodEntity.setBeginMonth(Integer.parseInt(month));
                triflesPeriodEntity.setBeginDate(Integer.parseInt(day));
            }
            else {}

            if (endDate.length() > 0) {
                int yindex = endDate.indexOf("月");
                int rindex = endDate.indexOf("日");
                String month = endDate.substring(0,yindex);
                String day = endDate.substring(yindex + 1,rindex);
                triflesPeriodEntity.setEndMonth(Integer.parseInt(month));
                triflesPeriodEntity.setEndDate(Integer.parseInt(day));
            }
            else {}

            triflesPeriodDao.save(triflesPeriodEntity);//新增杂项时限
            Integer periodId = triflesPeriodEntity.getId();
            AcctTriflesPeriodHistoryEntity triflesPeriodHistoryEntity = new AcctTriflesPeriodHistoryEntity();
            triflesPeriodHistoryEntity.setOrg(org);
            triflesPeriodHistoryEntity.setTrifles(triflesId);
            triflesPeriodHistoryEntity.setTriflesPeriod(periodId);
            triflesPeriodHistoryEntity.setTriflesHistory(triflesHistoryEntity.getId());
            triflesPeriodHistoryEntity.setBeginMonth(triflesPeriodEntity.getBeginMonth());
            triflesPeriodHistoryEntity.setBeginDate(triflesPeriodEntity.getBeginDate());
            triflesPeriodHistoryEntity.setEndMonth(triflesPeriodEntity.getEndMonth());
            triflesPeriodHistoryEntity.setEndDate(triflesPeriodEntity.getEndDate());
            triflesPeriodHistoryEntity.setCreator(creator);
            triflesPeriodHistoryEntity.setCreateDate(curDate);
            triflesPeriodHistoryEntity.setCreateName(userName);
            triflesPeriodHistoryDao.save(triflesPeriodHistoryEntity);//新增时限历史
        }

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 返回机构所有杂项
     */
    @Override
    public JsonResult getTriflesList(User user) {
        List<AcctTriflesEntity> list = triflesDao.getTriflesList(user);//全的，包含了需办理的和不再办理的
        List<AcctTriflesEntity> listEnable = new ArrayList<>();//需办理的
        List<AcctTriflesEntity> listDisenable = new ArrayList<>();//无需办理的
        for (AcctTriflesEntity triflesEntity : list) {
            Integer enabled = triflesEntity.getEnabled();
            if (enabled == 1) {//需办理的
                listEnable.add(triflesEntity);
            }
            else {//无需办理的
                listDisenable.add(triflesEntity);
            }
        }
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("enableList",listEnable);
        map.put("disEnableList",listDisenable);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
     * 杂项状态改成不再办理
     */
    @Override
    public JsonResult setTriflesDisenabled(User user, Integer triflesId) {
        Date date = new Date();
        Integer updator = user.getUserID();
        String updateName = user.getUserName();
        AcctTriflesEntity triflesEntity = triflesDao.get(triflesId);
        triflesEntity.setEnabled(0);
        triflesEntity.setEnabledTime(date);
        triflesEntity.setUpdator(updator);
        triflesEntity.setUpdateDate(date);
        triflesEntity.setUpdateName(updateName);
        List<AcctTriflesPeriodEntity> periodList = triflesPeriodDao.getTriflesPeriodList(triflesEntity.getId());
        for (AcctTriflesPeriodEntity triflesPeriodEntity : periodList) {
            triflesPeriodEntity.setEnabled(0);
            triflesPeriodEntity.setEnabledTime(date);
            triflesPeriodEntity.setUpdator(updator);
            triflesPeriodEntity.setUpdateDate(date);
            triflesPeriodEntity.setUpdateName(updateName);
        }
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 修改杂项
     * @param triflesId     杂项ID
     * @param name          新名称
     * @param type          1-改错别字，2-改新名称,3-修改日期范围
     * @param periodArray   {"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}  时限数组
     * 改错别字需要改历史数据，改新名称不需要改历史
     */
    @Override
    public JsonResult updateTrifles(User user, int triflesId, String name, String type, String periodArray) {
        Date curDate = new Date();
        Integer creator = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();
        Integer triflesHisId = null;
        AcctTriflesEntity triflesEntity = triflesDao.get(triflesId);

        //改完名称需要写修改记录
        AcctTriflesHistoryEntity triflesHistoryEntity = new AcctTriflesHistoryEntity();
        triflesHistoryEntity.setTrifles(triflesId);
        triflesHistoryEntity.setName(name);
        triflesHistoryEntity.setOldName(name);
        triflesHistoryEntity.setOrg(org);
        triflesHistoryEntity.setCreateDate(curDate);
        triflesHistoryEntity.setCreator(creator);
        triflesHistoryEntity.setCreateName(userName);
        triflesHistoryEntity.setEnabled(triflesEntity.getEnabled());
        triflesHistoryEntity.setLatestDeclare(triflesEntity.getLatestDeclare());
        triflesHistoryEntity.setFrequency(triflesEntity.getFrequency());
        triflesHistoryEntity.setVersionNo(0);
        triflesHistoryDao.save(triflesHistoryEntity);//新增杂项历史表
        triflesHisId = triflesHistoryEntity.getId();

        switch (type) {
            case "1":
                //改错别字需要改历史数据
                if ("1".equals(type)) {
                    triflesHistoryDao.updateName(name,triflesId);
                }
            case "2":
                triflesEntity.setName(name);
                triflesEntity.setUpdateName(userName);
                triflesEntity.setUpdateDate(curDate);
                triflesEntity.setUpdator(creator);
                Integer version = triflesEntity.getVersionNo();
                triflesEntity.setVersionNo(version + 1);

                break;
            case "3"://改日期范围,需要更新period原表数据
                JSONObject jsonObject = new JSONObject(periodArray);
                JSONArray data = jsonObject.getJSONArray("data");
                for (int i=0;i<data.length();i++) {
                    JSONObject pObject = data.getJSONObject(i);
                    //{"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}
                    int periodId = pObject.optInt("periodId",0);
                    int beginMonth = pObject.optInt("beginMonth",0);
                    int beginDate = pObject.optInt("beginDate",0);
                    int endMonth = pObject.optInt("endMonth",0);
                    int endDate = pObject.optInt("endDate",0);
                    AcctTriflesPeriodEntity periodEntity = triflesPeriodDao.get(periodId);
                    periodEntity.setBeginMonth(beginMonth);
                    periodEntity.setBeginDate(beginDate);
                    periodEntity.setEndMonth(endMonth);
                    periodEntity.setEndDate(endDate);

                }

        }
        //写period history 表
        JSONObject jsonObject = new JSONObject(periodArray);
        JSONArray data = jsonObject.getJSONArray("data");
        for (int i=0;i<data.length();i++) {
            JSONObject pObject = data.getJSONObject(i);
            //{"data":[{"periodId":1,"beginMonth":"9","beginDate":"1","endMonth":"9","endDate":"10"}]}
            int periodId = pObject.optInt("periodId",0);
            int beginMonth = pObject.optInt("beginMonth",0);
            int beginDate = pObject.optInt("beginDate",0);
            int endMonth = pObject.optInt("endMonth",0);
            int endDate = pObject.optInt("endDate",0);
            AcctTriflesPeriodHistoryEntity periodHistoryEntity = new AcctTriflesPeriodHistoryEntity();
            periodHistoryEntity.setTriflesPeriod(periodId);
            periodHistoryEntity.setTrifles(triflesId);
            periodHistoryEntity.setTriflesHistory(triflesHisId);
            periodHistoryEntity.setBeginMonth(beginMonth);
            periodHistoryEntity.setBeginDate(beginDate);
            periodHistoryEntity.setEndMonth(endMonth);
            periodHistoryEntity.setEndDate(endDate);
            periodHistoryEntity.setOrg(org);
            periodHistoryEntity.setVersionNo(0);
            periodHistoryEntity.setCreator(creator);
            periodHistoryEntity.setCreateName(userName);
            periodHistoryEntity.setCreateDate(curDate);
            triflesPeriodHistoryDao.save(periodHistoryEntity);
        }

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 杂项修改记录
     * @param triflesId     杂项ID
     */
    @Override
    public JsonResult triflesUpdateRecord(User user, int triflesId) {
        Integer org = user.getOid();
        List<AcctTriflesHistoryEntity> list = triflesHistoryDao.triflesUpdateRecord(org,triflesId);
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(list);
        return result;
    }

    /**
     * 根据年月查询杂项
     * @param year
     * @param month
     */
    @Override
    public JsonResult getTriflesByDate(User user, int year, int month) {
        List<AcctTriflesDto> triflesList = triflesDao.getByPeriod(user,year,month);//全的，包括已报的和应报的
        List<AcctTriflesDto> triflesedList = triflesDao.getListFromDetail(user,year,month);//已报列表
        int triflesNum = triflesList.size();//应报数
        int triflesedNum = triflesedList.size();//已报数
        for (AcctTriflesDto triflesDto : triflesList) {
            Integer triflesId = triflesDto.getTriflesId();
            for (AcctTriflesDto detailDto : triflesedList) {
                Integer detailId = detailDto.getTriflesId();
                if (detailId.compareTo(triflesId) == 0) {
                    triflesDto.setName(detailDto.getName());
                    triflesDto.setFactReportDate(detailDto.getFactReportDate());
                    triflesDto.setCreateName(detailDto.getCreateName());
                    triflesDto.setLatestDeclare(detailDto.getLatestDeclare());
                }
            }

        }

        JsonResult result = new JsonResult();
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("triflesNum",triflesNum);//应报数
        map.put("triflesedNum",triflesedNum);//已报数
        map.put("allTriflesList",triflesList);//所有杂项
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
     * 杂项报税
     * @param handleDate 申报日期,YYYY-MM-DD
     * @param detailId 报税ID，列表里返回的字段
     * @param triflesId 杂项ID
     * @param triflesHisId 历史杂项ID
     */
    @Override
    public JsonResult handleTrifles(Integer detailId, int triflesId, int triflesHisId, String handleDate, User user) {
        Date date = NewDateUtils.dateFromString(handleDate,"yyyy-MM-dd");
        Integer userID = user.getUserID();
        String userName = user.getUserName();
        Integer org = user.getOid();
        Date curDate = new Date();
//        String str = handleDate.replace("-","");

        String latestDeclare = NewDateUtils.dateToString(curDate,"yyyyMMdd");
        triflesDao.get(triflesId).setLatestDeclare(latestDeclare);
        triflesHistoryDao.get(triflesHisId).setLatestDeclare(latestDeclare);

        //判断detail表中是否已经有了在给定年月的报税数据，没有的话新增，有的话不再新增
        if (detailId != null) {//已经有了该年月的报税数据，不再新增，只写编辑记录，需要把已经有的报税数据更新成最新的数据
            AcctTriflesDetailEntity triflesDetailEntity = triflesDetailDao.get(detailId);
            triflesDetailEntity.setDelareTime(date);
            triflesDetailEntity.setPeriod(handleDate.substring(0,7));
            triflesDetailEntity.setState("1");
            triflesDetailEntity.setOperation("3");
            triflesDetailEntity.setCreateDate(curDate);
            triflesDetailEntity.setCreator(userID);
            triflesDetailEntity.setCreateName(userName);
            triflesDetailEntity.setUpdator(userID);
            triflesDetailEntity.setUpdateName(userName);
            triflesDetailEntity.setUpdateDate(curDate);
            //写编辑记录
            insertTriflesEditRecord(triflesDetailEntity,user);

        }
        else {//先新增再写编辑记录
            AcctTriflesDetailEntity triflesDetailEntity = new AcctTriflesDetailEntity();
            triflesDetailEntity.setOrg(org);
            triflesDetailEntity.setTrifles(triflesId);
            triflesDetailEntity.setTriflesHistory(triflesHisId);
            triflesDetailEntity.setDelareTime(date);
            triflesDetailEntity.setState("1");
            triflesDetailEntity.setPeriod(handleDate.substring(0,7));
            triflesDetailEntity.setCreator(userID);
            triflesDetailEntity.setCreateName(userName);
            triflesDetailEntity.setCreateDate(curDate);
            triflesDetailEntity.setOperation("1");
            triflesDetailDao.save(triflesDetailEntity);
            //写编辑记录
            insertTriflesEditRecord(triflesDetailEntity,user);

        }

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        return result;
    }

    /**
     * 写杂项的编辑记录
     * */
    private void insertTriflesEditRecord(AcctTriflesDetailEntity triflesDetailEntity, User user) {
        AcctTriflesDetailHistoryEntity historyEntity = new AcctTriflesDetailHistoryEntity();
        historyEntity.setOrg(triflesDetailEntity.getOrg());
        historyEntity.setTriflesDetail(triflesDetailEntity.getId());
        historyEntity.setTrifles(triflesDetailEntity.getTrifles());
        historyEntity.setTriflesHistory(triflesDetailEntity.getTriflesHistory());
        historyEntity.setPeriod(triflesDetailEntity.getPeriod());
        historyEntity.setDelareTime(triflesDetailEntity.getDelareTime());
        historyEntity.setState(triflesDetailEntity.getState());
        historyEntity.setCreator(user.getUserID());
        historyEntity.setCreateDate(new Date());
        historyEntity.setCreateName(user.getUserName());
        triflesDetailHistoryDao.save(historyEntity);
    }

    /**
     * 杂项的报税记录
     * @param   triflesId     报表ID
     * @param   year
     */
    @Override
    public JsonResult getTriflesReportRecord(User user, Integer triflesId, String year) {
        if (year == null) {
            Calendar c = Calendar.getInstance();
            int yearInt = c.get(Calendar.YEAR);
            year = "" + yearInt;
        }

        AcctTriflesDetailEntity triflesDetailEntity = new AcctTriflesDetailEntity();
        triflesDetailEntity.setPeriod(year);
        triflesDetailEntity.setOrg(user.getOid());
        triflesDetailEntity.setState("1");//已报
        triflesDetailEntity.setTrifles(triflesId);

//        List<AcctTriflesDetailEntity> list = triflesDetailDao.getTriflesReportRecord(triflesDetailEntity);//报税记录
        Map<String,Object> map = triflesDetailDao.getTriflesReportRecord(triflesDetailEntity);//报税记录
        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(map);
        return result;
    }

    /**
     * 杂项的编辑记录
     * triflesId     报表ID
     * period       yyyy-MM
     */
    @Override
    public JsonResult getTriflesEditRecord(User user, int triflesId, String period) {
        Integer org = user.getOid();
        AcctTriflesDetailHistoryEntity param = new AcctTriflesDetailHistoryEntity();
        String periodYM = period.substring(0,7);//年月
        param.setPeriod(periodYM);
        if (period.length() > 7) {
            param.setDelareTime(NewDateUtils.dateFromString(period,"yyyy-MM-dd"));
        }
        param.setTrifles(triflesId);
        param.setOrg(org);
        List<AcctTriflesDetailHistoryEntity> list = triflesDetailHistoryDao.getTriflesEditRecord(param);

        JsonResult result = new JsonResult();
        result.setSuccess(1);
        result.setData(list);
        return result;
    }
}
