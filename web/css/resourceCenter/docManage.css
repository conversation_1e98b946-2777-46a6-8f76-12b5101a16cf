.clear:before,.clear:after{clear:both;content:''; display:block; width:0; height:0; visibility:hidden;}
.ty-panel{  background: none;    }
.ty-colFileTree{ float: left ; max-height:700px; width:310px;    }
.mar{ margin-left: 330px;  }
.fa-folder+span{ width:100px; display:inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
#scanSet { width:760px;  }
#allRight .ty-gray , #nowRight .ty-gray { color:#aaa; }

/* creator: 李玉婷，2019-06-28 08:29:40，文件夹管理 */
.headPanel h3 {display: block;  height:35px; line-height: 35px; font-size: 18px;  font-weight: 500;  color: #36ab60;  min-width:250px; margin: 0; flex: none}
.headPanel .operableBtn { line-height: 35px; border-top:2px solid #36ab60;}
.icon-de{font-size: 9px;}
.headPanel{margin-right:30px;color: #36ab60;}
.nowFolder{overflow: hidden;margin-bottom: 70px; display: flex}
.docLastIntrol{flex: auto}
.def-table{width: 100%; max-width: 100%; margin-bottom: 20px;}
.def-table thead tr td{ color:#777;border-bottom: 1px solid #ddd;}
.def-table tr td{padding: 8px; line-height: 1.42857;  vertical-align: top;text-align: center; color: #333;}
.spaceGap{margin-right: 26px;cursor: pointer;}
.bounce-changeClass input::-webkit-input-placeholder { /* WebKit browsers */  color: #999;  }
.bounce-changeClass input::-moz-placeholder { /* Mozilla Firefox 19+ */ color: #999;  }
.bounce-changeClass input:-moz-placeholder { /* Mozilla Firefox 19+ */ color: #999;  }
.bounce-changeClass input:-ms-input-placeholder { /* Internet Explorer 10+ */  color: #999;  }
.folderNameSee{color: #548b69;}
#nameEditRecord .bonceCon{max-height: 560px;overflow-y: auto;}
.ty-colFileTree li > ul:nth-child(2){  margin-left: 20px;  }
.normalName{  font-size: 18px;  font-weight: 500;  color: #36ab60;  width: 200px;}
.setBgHeight{min-height:613px;}

.btnLink{ color:#0b94ea;cursor:default;     }
.btnLink:hover{ text-decoration:underline;   }
#scanSet .departTree{ width:720px;    }
#scanSet .ctrl{ float:right; line-height:30px; color:#0b94ea; cursor: default;  }
#scanSet .ctrl:hover{ text-decoration:underline;   }
.txtR{ text-align: right; }

#scanSet .departTree > ul, #scanSet .departTree>form{ height:250px;  }
#scanSet  .departTree .arrow { line-height:270px  }
#scanSetLogScan .bonceCon .users{ border:1px solid #ccc; overflow-y: auto; max-height: 150px;  }
#scanSetLogScan .bonceCon .users p{ padding:5px 20px; margin:0;  }
#scanSetLogScan .bonceCon .users p:nth-child(even){ background:#eaeaea; }
#scanSetLog .searchBar,#scanSetLog_accurate .searchBar{
    display: flex;
    justify-content: space-between;
    margin: 4px 0 8px 0
}
#scanSetLog .searchBar .inputNameOrSn, #scanSetLog_accurate .searchBar .inputNameOrSn{
    width: 300px;
}
#scanSetLog .handleResult, #scanSetLog_accurate .handleResult{
    width: 140px;
    word-wrap: break-word;
    display: inline-block;
}
.item{  margin: 8px 0;}
.moveSetCon{width: 600px;margin: 0 auto;}
.ty-hr{border-bottom: 1px solid #dae6f0;margin: 12px 0;}
.roleList{height: 76px;background: #fff;padding: 2px;overflow-y: auto;}
.user_avatar{display: inline-block;background-color: #eee;padding: 4px 8px;border-radius: 3px;margin: 4px; cursor: pointer}
.user_avatar i.delUser {color: #ed5565;margin-left: 4px;}
.user_avatar i.addUser {color: #36ab60;margin-left: 4px;}
#scanSetLog .bonceCon input:not([type="radio"]):not([type="checkbox"]), #scanSetLog .bonceCon select, #scanSetLog_accurate .bonceCon input:not([type="radio"]):not([type="checkbox"]){
    height: 32px;
    color: #666;
    padding:0 8px;
    min-width: 167px;
    border-color: #5d9cec;
    border-radius: 0;
}
.log_row{
    margin-bottom: 16px;
}
.log_item{
    display: inline-block;
    vertical-align: top;
}
.log_item_title{
    color: #666;
    display: inline-block;
    width: 85px;
    vertical-align: top;
}
.log_item .log_item_content{
    color: #333;
    display: inline-block;
    width: 170px;
}
.log_item_long .log_item_content{
    color: #333;
    display: inline-block;
}
.nodata{
    width: 100%;
    text-align: center;
    height: 60px;
    line-height: 60px;
}

.indent2{
    text-indent: 28px;
}



