package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquModelImport;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
public interface ITEquModelImportService
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquModelImport selectTEquModelImportById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquModelImport 
     * @return 集合
     */
    public List<TEquModelImport> selectTEquModelImportList(TEquModelImport tEquModelImport);

    /**
     * 新增
     * 
     * @param tEquModelImport 
     * @return 结果
     */
    public int insertTEquModelImport(TEquModelImport tEquModelImport);

    /**
     * 修改
     *
     * @param tEquModelImport 
     * @param user
     * @return 结果
     */
    public int updateTEquModelImport(TEquModelImport tEquModelImport, User user);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTEquModelImportByIds(String ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquModelImportById(Long id);

    AjaxResult importData(String ids, User user);

    List<TEquModelImport> check(TEquModelImport modelImport,  User user);
}
