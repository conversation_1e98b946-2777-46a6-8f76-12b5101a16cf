<template>
  <div id="reimburseBillVerification">
    <TY_NavTop title="个人报销的票据审核" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待在线审核" name="1" closable='false' :msgCount="onlineAuditHandle.length">
        <div class="tip">
          <p>以下票据有待报销，请在线审核！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('31' + item.id, 0, '')" v-for="(item, index) in onlineAuditHandle" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="在线审核ok" name="2" closable='false'>
        <div>
          <a class="ui-cell" v-on:click="jump('32' + item.id, 0, '')" v-for="(item, index) in onlineAuditOK" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待线下审核" name="3" closable='false' :msgCount="offlineAuditHandle.length">
        <div class="tip">
          <p> 以下票据有待线下审核。请核对实际票据与系统中的电子票据是否一致。一致的，方可申请付款！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('33' + item.id, 0 , 3 , 1)" v-for="(item, index) in offlineAuditHandle" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
  #reimburseBillVerification{
    overflow: auto;
    .ui-cell{ display: block; }
    .item_fn{
      display: flex;
      div:nth-child(1){ flex:1;  }
      div:nth-child(2){ flex:3;  }
    }
    .tabCount{
      top:0;
      right:0 ;
      left:auto;
    }
    .tabs-tab{
      width:33%;
    }
    .txt_right{
      text-align: right;
      font-size:11px;
      padding-right:15px;
    }
    .tip{
       padding:5px 12px;
       background-color: #E2EFDA;
       display: block;
     }
  }
</style>
<script>
export default {
  name: 'reimburseBillVerification',
  data () {
    return {
      onlineAuditHandle: [],
      onlineAuditOK: [],
      offlineAuditHandle: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    this.tabValue = this.$store.getters.getFinReimburseNav
    let that = this
    this.listenersUid = [
      // 待在线审核
      this.sphdSocket.subscribe('onlineAuditHandle', function (data) {
        // 待票据审核
        that.cashierHandle = JSON.parse(data)
        console.log('onlineAuditHandle OK:' + JSON.parse(data))
        console.log(JSON.parse(data))
        that.onlineAuditHandle = JSON.parse(data)
      }),
      this.sphdSocket.subscribe('onlineAuditHandle', function (data) {
        console.log('onlineAuditHandle user OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.onlineAuditHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 在线审核ok
      this.sphdSocket.subscribe('onlineAuditOK', function (data) {
        // 待付款审批
        console.log('onlineAuditOK :' + JSON.parse(data))
        console.log(JSON.parse(data))
        that.onlineAuditOK = JSON.parse(data)
      }),
      this.sphdSocket.subscribe('onlineAuditOK', function (data) {
        console.log('onlineAuditOK user OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditOK.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.onlineAuditOK.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditOK.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待线下审核
      this.sphdSocket.subscribe('offlineAuditHandle', function (data) {
        // 可付款
        console.log('offlineAuditHandle OK:' + data)
        console.log(JSON.parse(data))
        that.offlineAuditHandle = JSON.parse(data)
      }),
      this.sphdSocket.subscribe('offlineAuditHandle', function (data) {
        console.log('offlineAuditHandle user OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.offlineAuditHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.offlineAuditHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.offlineAuditHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),

      this.sphdSocket.subscribe('notice', function (data) {
        console.log('notice Socket received OK:' + data)
        if (data === '1') {
          that.$router.push({
            path: '/approveReimburse'
          })
        }
      })
    ]
    let oid = this.sphdSocket.user.oid
    // let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('verificationBills', { oid: oid, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id, param, tab, num) {
      this.$store.dispatch('setFinReimburseNav', tab)
      this.$router.push({
        path: `/approveReimburseDetail/${id}/${param}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/reimburseVerificationBillsQuery`
      })
    }
  }
}
</script>
