<template>
  <div class="lyricTxtSave">
    <Pageheader :title="txtTitle" :showBack="true" :showClose="true" :runMyBackFun="true" @mycloseFun="cancelAdd" @myBackFun="goback"/>
    <div class="">
      <div class="live-container">
        <el-form label-position="top" :model="formData">
            <div>
              本歌曲的节奏已配完！
            </div>
            <el-radio-group v-model="formData.submitType" size="large">
              <div>
                <el-radio label="1">保存</el-radio>
              </div>
              <div>
                <el-radio label="2">重新配节奏</el-radio>
              </div>
               <div>
                 <el-radio label="3">退出，已上传的歌词也舍弃！</el-radio>
               </div>
            </el-radio-group>
        </el-form>
        <span @click="goNext()" class="primary live-button">确 定</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from "vue-router";
import { ElMessage } from 'element-plus'
import { insertSongs, insertRhythm, getSongLock} from '@/api/music';
import JSONBig from 'json-bigint'
import auth from '@/sys/auth'

const router = useRouter();
const formData = reactive({
  submitType:''
})
var lockData =  { lock:'' }
const txtTitle = ref('')
onMounted(() => {
  let lyricInfo = JSONBig.parse(localStorage.getItem('newlycdata'))
  console.log('lyricInfo.from ===', lyricInfo.from)
  let from = lyricInfo && lyricInfo.from
  if(from === 'lycTaskHandel'){
    getLockFun()
  }
  txtTitle.value = from === 'newLyricXiQu' ? '戏词制作' : '歌词制作'
});

// 取消新增
const cancelAdd = () => {
  let lyricInfo = JSONBig.parse(localStorage.getItem('newlycdata'))
  console.log('lyricInfo=', lyricInfo)
  console.log('lyricInfo.from ===', lyricInfo.from)
  let from = lyricInfo && lyricInfo.from

  localStorage.removeItem('newlycdata')
  localStorage.removeItem('patData')
  localStorage.removeItem('agreeObj')
  localStorage.removeItem('lycTxtObj')
  localStorage.removeItem('assignTasks')
  localStorage.removeItem('staffList')

  if(from === 'lycTaskHandel'){
    router.push({ name: 'lycTaskHandel' });
  }else if(from === 'newLyric'){
    router.push({ name: 'lyricTask', });
  }else if(from === 'newLyricXiQu'){
    router.push({ name: 'lyricTaskXiQu', });
  }else{
    router.push({ name: 'homeManage', });
  }
}
const goback = () => {
  router.push({
    name: 'lyricTxtPat',
  });
}
// 获取lock
const getLockFun = () => {
  let formData2 = localStorage.getItem('newlycdata')
  formData2 = JSONBig.parse(formData2)
  console.log('formData = ：', formData2)
  let data = { 'id': formData2.id }
  getSongLock(data).then(res => {
    console.log('getSongLock res=', res)
    lockData = res.data

  }).catch(err=>{
    console.log('err=', err)
  })
}

const goNext = () => {
  switch (formData.submitType){
    case '1': // 保存
      let formData2 = localStorage.getItem('newlycdata')
      formData2 = JSONBig.parse(formData2)
      if(formData2.from === 'newLyric' || formData2.from === 'newLyricXiQu' ){// 主播直接新增的
          submit()
        }else{ // 主播或者成员 上传的 节奏
        submitLyric()
        }
      break;
    case '2': // 重新配节奏
      localStorage.removeItem('patData')
      router.push({
        name: 'lyricTxtPat',
      });
      break;
    case '3': // 退出，已上传的歌词也舍弃！
      cancelAdd()
      break;
    default:
      ElMessage.info('请选择处理方式！');
      return false
      break;
  }
}
const submitLyric = ()=> {
  let data = {}
  let formData2 = localStorage.getItem('newlycdata')
  formData2 = JSONBig.parse(formData2)
  let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  console.log('jsonData=', jsonData)
  const userInfoSys = auth.getUser()
  data.type = userInfoSys.roleCode === "staff" ? 2 : 1
  data.id = formData2.id
  let lycTxtObj = localStorage.getItem('lycTxtObj')
  lycTxtObj = JSONBig.parse(lycTxtObj)
  data.rhythmOption = lycTxtObj.rhythmOption
  data.rhythm = `WEBVTT\n\n` ; // 节奏歌词文本(含时间标签)
  let patData = JSONBig.parse(localStorage.getItem('patData'))
  data.lyrics = patData.txtPat  ; // 歌词文本 从剪贴板获取的歌词文本
  let beginTime = '00:00:00.000'
  // 歌词 开头点击的情况
  patData.lyricArr.forEach((itemLyc, index, arr) => {
    console.log('arr', arr)
    let nextTimeObj = arr[index+1]
    if(nextTimeObj){
      data.rhythm += `${ itemLyc.timeStr } --> ${ nextTimeObj.timeStr }\n ${ itemLyc.txt }\n\n`
    }else{
      data.rhythm += `${ itemLyc.timeStr } --> ${ '99:99.999' }\n ${ itemLyc.txt }\n\n`
    }
  })
  data.approveStatus = 2
  data.selected = data.type === 1 ? 1 : 0
  insertRhythm(data).then(res => {
    console.log('insertRhythm res=', res)
    let state = res.data && res.data.state
    if(state === 0){
      ElMessage.success('节奏已选完！');
    }else if(state === 1){
      ElMessage.success('保存成功！');
      router.push("lyricDetails2")
    }else if(state === 2){
      ElMessage.success('不要重复处理!');
    }
  }).catch(err => {
    console.log('insertRhythm err=', err)
  })
}
const submit = ()=> {
  let formData2 = localStorage.getItem('newlycdata')
  formData2 = JSONBig.parse(formData2)
  console.log('formData = 这是什么', formData2)
  let lycTxtObj = localStorage.getItem('lycTxtObj')
  lycTxtObj = JSONBig.parse(lycTxtObj)
  let data = {}
  if(formData2.id){
    data = { 'id': formData2.id }
  }
  data.lock = lockData.lock
  data.name = formData2.name  // 歌曲名称
  data.type = formData2.from === 'newLyricXiQu' ? 2 : 1 ;// 类型:1-歌曲,2-戏曲 戏曲的先不做
  data.captionRequired = formData2.addWord   // 是否需要加字幕 0-否 1-是 传0的时候5以下就都不用传了
  if(data.type === 2){
    data.selections = formData2.nameXuanDuan
    data.genres = formData2.nameLiu
  }

  if(formData2.addWord === '1'){
    let songDuration = formData2.banTime.min + '分' + formData2.banTime.ss + '秒'
    data.trackDuration = (formData2.orlLyc.duration || 0 ) * 1000  ; // 歌曲时长 上个接口返回的 duration 字段
    data.trackDuration = parseInt(data.trackDuration)
    data.bgmDuration = (formData2.banLyc.duration || 0) * 1000 ; // 伴奏时长 上个接口返回的duration字段
    data.bgmDuration = parseInt(data.bgmDuration)
    data.publishType = formData2.uploadType ; // 发布方式:1-自行上传,2-发布任务,团队成员上传
    data.rhythmOption = lycTxtObj.rhythmOption ; // 节奏选项:1-原唱,2-伴奏   这个值传的是否正确会对接口产生影响
    data.originalPath = formData2.orlLyc.filePath ; // 原唱存储路径 上个接口返回的filePath字段
    data.bgmPath = formData2.banLyc.filePath ; // 伴唱存储路径 上个接口返回的filePath字段
    data.songDuration = songDuration; //  版本号 传0
    data.versionNo = 0 ; //  版本号 传0
    data.selected = 1  ; // 是否被选择 1-被选择 0-未选择 这里固定传1
    data.rhythm = `WEBVTT\n\n` ; // 节奏歌词文本(含时间标签)
    let patData = JSONBig.parse(localStorage.getItem('patData'))
    data.lyrics = patData.txtPat  ; // 歌词文本 从剪贴板获取的歌词文本
    let beginTime = '00:00:00.000'
    // 歌词末尾点击的情况
    // patData.lyricArr.forEach(itemLyc => {
    //   data.rhythm += `${ beginTime } --> ${ itemLyc.timeStr }\n ${ itemLyc.txt }\n\n`
    //   beginTime = itemLyc.timeStr
    // })

    // 歌词 开头点击的情况
    let lastTimeStr = patData.lastTimeStr
    patData.lyricArr.forEach((itemLyc, index, arr) => {
      console.log('arr', arr)
      let nextTimeObj = arr[index+1]
      if(nextTimeObj){
        data.rhythm += `${ itemLyc.timeStr } --> ${ nextTimeObj.timeStr }\n ${ itemLyc.txt }\n\n`
      }else{
        data.rhythm += `${ itemLyc.timeStr } --> ${ lastTimeStr }\n ${ itemLyc.txt }\n\n`
      }
    })

  }

  // approveStatus:  captionRequired=0时传2；captionRequired=2且publishType=1时传2；captionRequired=2且publishType=2时传1
  if(data.captionRequired === '0' || (data.captionRequired === '1' && data.publishType === '1')){
    data.approveStatus = 2
  }
  if(data.captionRequired === '1' && data.publishType === '2'){
    data.approveStatus = 1
  }
  console.log('insertSongs data=', data)
  insertSongs(data).then(res =>{
    console.log('insertSongs', res)
    let success = res.success
    if(success === 1){
      ElMessage.success('新增成功！');
      let song = res.data.cmwLyric
      song.seetype = 1; // 1-主播 2- 成员
      song.fromAdd = 1
      song.id = song.song
      let lyricInfo = JSONBig.parse(localStorage.getItem('newlycdata'))
      let from = lyricInfo && lyricInfo.from
      song.type = from === 'newLyricXiQu' ? 2 : 1
      localStorage.setItem('lyricDetailsItem', JSONBig.stringify(song))
      router.push("lyricDetails")
    }else{
      ElMessage.error('新增失败！请重试');

    }
  })

}


</script>
<style lang="scss">
.lyricTxtSave{
  .el-radio-group{
    display: block!important;
  }
}
</style>
