package cn.sphd.miners.modules.finance.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/22.
 */
public interface FinanceUpdateService {

    Map<String,Object> updateCash(Integer userId, Integer detailId, String genre,String categoryDesc, String summary, BigDecimal credit,
                                  BigDecimal debit, String purpose, String auditorName, String oppositeCorp, String memo, Date receiveAccountDate,
                                  Integer accountId, Integer billQuantity,BigDecimal billAmount,String stakeholderCategory,Integer stakeholder,
                                  Date billDate,Date factDate,Integer imgType,String imgPaths,Integer taxId,String taxCategoryName,Integer report,Integer businessPeriodBegin,
                                  Integer businessPeriodEnd,String periodBegin,String periodEnd,Integer taxPeriodIdInsert,Integer taxPeriodIdUpdate);

    Map<String,Object> updateTransferAccounts(Integer userId,Integer detailId,BigDecimal credit,BigDecimal debit,Date auditDate,Integer chequeId,String memo);

    Map<String,Object> updateReturn(Integer userId,Integer returnId,Integer accountId,Date depositDate,String depositorName,Date receiveAccountDate,Integer imgType,String imgPaths,Integer updateType,Integer returnIdNew);

    Map<String,Object> updateBankTransfer(Integer userId,Integer chequeDetailId,Integer accountId,String summary,
                                          Integer billQuantity,BigDecimal amount,BigDecimal billAmount,String purpose,String financialHandling,String type,
                                          String chequeNo,Date expireDate,String receiveCorp,Date receiveDate,String receiver,String operator,
                                          String memo,Integer chequeId,String stakeholderCategory,Integer stakeholder, Date billDate,Date factDate,Integer imgType,String imgPaths);

    Map<String,Object> approvalCash(Integer userId,Integer approvalProcessId,Integer state,String reason);

    Map<String,Object> approvalReturn(Integer userId,Integer approvalProcessId,Integer state,String reason);

    Map<String,Object> approvalBankTransfer(Integer userId,Integer approvalProcessId,Integer state,String reason);

    Map<String,Object> approvalTransferAccounts(Integer userId,Integer approvalProcessId,Integer state,String reason);
}
