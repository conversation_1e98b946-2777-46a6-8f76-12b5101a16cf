package cn.sphd.miners.modules.message.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.personal.entity.UserMessage;

import java.util.List;

/**
 * Created by Administrator on 2017/3/27.
 */
public interface MessageService {

    List<UserMessage> getMessageByUidAndState(Integer status,Integer uid);
    List<UserMessage> getMessageByUidAndState(Integer status, Integer uid, PageInfo pageInfo);

    List<UserMessage> getAllMessage(Integer status,Integer uid,String beginDate,String endDate);

    List<UserMessage> getMessageByPersonnelReimburseId(Integer pid,Integer status,Integer uid,Integer messageType);

    List<UserMessage> getMessageByMessageId(Integer messageId);

    void updateMessagesByUidMessageType(Integer uid,String messageType);
}
