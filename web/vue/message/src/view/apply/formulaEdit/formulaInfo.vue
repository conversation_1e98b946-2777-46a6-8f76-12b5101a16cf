<template>
  <div id="formulaInfo">
    <TY_NavTop title="配方修改"></TY_NavTop>
    <div class="detailInfo" >
      <div style="padding:0 20px; line-height: 40px;">
        <div class="">
          <span>代号为{{formulaInfo.fCode}}配方中的{{formulaInfo.fType | formatType}} {{formulaInfo.item.name}}</span>
        </div>
      </div>
    </div>
    <div>
      <div style="height:20px;"></div>
      <div class="con">
        <div>
          <div class="Item">原料代号 <span class="rt">{{ formulaInfo.item.code }}</span></div>
          <div class="Item">原料名称 <span class="rt">{{ formulaInfo.item.name }}</span></div>
          <div class="Item">型号 <span class="rt">{{ formulaInfo.item.model }}</span></div>
          <div class="Item">规格 <span class="rt">{{ formulaInfo.item.specifications }}</span></div>
          <div class="Item">计量单位 <span class="rt">{{ formulaInfo.item.unit }}</span></div>
          <div class="Item">份数 <span class="rt">{{ formulaInfo.item.amount }}份</span></div>
          <div class="Item">备注 <span class="rt">{{ formulaInfo.item.memo }}</span></div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #formulaInfo{
    height:100%;
    overflow: auto!important;
    .detailInfo{
      background:#fff; font-size:14px;
      border-bottom:1px solid #ddd ;
    }
    .Item{
      padding:0 30px ;
      font-size:15px ;
      line-height: 30px;
      background: #fff;
      .rt{ float: right;  }
    }
  }
</style>
<script>
var that
export default {
  name: 'formulaInfo',
  data () {
    return {
      formulaInfo: null
    }
  },
  created () {
    that = this
    that.formulaInfo = JSON.parse(localStorage.getItem('formulaInfo'))
    if (!that.formulaInfo) {

    }
  },
  filters: {
    formatType: function (num) {
      if (num === '1') {
        return '主料'
      } else if (num === '2') {
        return '辅料'
      }
    }
  },
  methods: {
  }
}
</script>
