package cn.sphd.miners.modules.resourceAuthority.entity;

import org.apache.ibatis.type.Alias;

import javax.persistence.Transient;
import java.math.BigInteger;
import java.util.Date;

/**
 * Created by 朱思旭 on 2017/10/30.
 */

@Alias("resource")
public class ResEntity {
  private Integer id;
  private Integer org;
  private Integer category;
  private String categoryPath;
  private Boolean enabled;
  private String enabledTime;
  private String name;
  private String fileSn;
  private String content;
  private String reason;
  private String path;
  private Long size;
  private Integer changeNum;
  private Integer downloadNum;
  private Integer viewNum;
  private Integer moveNum;
  private Integer modifyNum;
  private Byte isTrash;
  private Boolean isDeleted;
  private Date deleteTime;
  private String version;
  private String keyword;
  private Integer valid;
  private String type;
  private Integer isStick;
  private Integer operator;
  private String opertatorName;
  private String operateDate;
  private Integer verifier;
  private String verifierName;
  private String verifyDate;
  private Integer auditor;
  private String auditName;
  private String auditDate;
  private String approveStatus;
  private Integer approver;
  private String approverName;
  private String approveDate;
  private String operation;
  private String applyMemo;
  private String approveMemo;
  private String memo;
  private Integer creator;
  private String createName;
  private String createDate;
  private Integer updator;
  private String updateName;
  private String updateDate;
  private Integer messageId;
  private BigInteger totalSize;
  private String teminateState;
  private Date teminateTime;
  private String teminateReason;
  private Integer teminater;
  private String teminaterName;
  private String lockUuid;
  private String batchUuid;

  private Integer user;
  private String listChooseOrgAndUser;
  private String createDateBegin;
  private String createDateEnd;
  private String updateDateBegin;
  private String updateDateEnd;
  @Transient
  private String operationTag;   //增减标签
  private String fileValidTime; //有效期
  private String userName;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getCategory() {
    return category;
  }

  public void setCategory(Integer category) {
    this.category = category;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getFileSn() {
    return fileSn;
  }

  public void setFileSn(String fileSn) {
    this.fileSn = fileSn;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getReason() {
    return reason;
  }

  public void setReason(String reason) {
    this.reason = reason;
  }

  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public Long getSize() {
    return size;
  }

  public void setSize(Long size) {
    this.size = size;
  }

  public Integer getChangeNum() {
    return changeNum;
  }

  public void setChangeNum(Integer changeNum) {
    this.changeNum = changeNum;
  }

  public Integer getDownloadNum() {
    return downloadNum;
  }

  public void setDownloadNum(Integer downloadNum) {
    this.downloadNum = downloadNum;
  }

  public Integer getViewNum() {
    return viewNum;
  }

  public void setViewNum(Integer viewNum) {
    this.viewNum = viewNum;
  }

  public Integer getMoveNum() {
    return moveNum;
  }

  public void setMoveNum(Integer moveNum) {
    this.moveNum = moveNum;
  }

  public Integer getModifyNum() {
    return modifyNum;
  }

  public void setModifyNum(Integer modifyNum) {
    this.modifyNum = modifyNum;
  }

  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }

  public String getKeyword() {
    return keyword;
  }

  public void setKeyword(String keyword) {
    this.keyword = keyword;
  }

  public Integer getValid() {
    return valid;
  }

  public void setValid(Integer valid) {
    this.valid = valid;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public Integer getIsStick() {
    return isStick;
  }

  public void setIsStick(Integer isStick) {
    this.isStick = isStick;
  }

  public Integer getOperator() {
    return operator;
  }

  public void setOperator(Integer operator) {
    this.operator = operator;
  }

  public String getOpertatorName() {
    return opertatorName;
  }

  public void setOpertatorName(String opertatorName) {
    this.opertatorName = opertatorName;
  }

  public String getOperateDate() {
    return operateDate;
  }

  public void setOperateDate(String operateDate) {
    this.operateDate = operateDate;
  }

  public Integer getVerifier() {
    return verifier;
  }

  public void setVerifier(Integer verifier) {
    this.verifier = verifier;
  }

  public String getVerifierName() {
    return verifierName;
  }

  public void setVerifierName(String verifierName) {
    this.verifierName = verifierName;
  }

  public String getVerifyDate() {
    return verifyDate;
  }

  public void setVerifyDate(String verifyDate) {
    this.verifyDate = verifyDate;
  }

  public Integer getAuditor() {
    return auditor;
  }

  public void setAuditor(Integer auditor) {
    this.auditor = auditor;
  }

  public String getAuditName() {
    return auditName;
  }

  public void setAuditName(String auditName) {
    this.auditName = auditName;
  }

  public String getAuditDate() {
    return auditDate;
  }

  public void setAuditDate(String auditDate) {
    this.auditDate = auditDate;
  }

  public String getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(String approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApprover() {
    return approver;
  }

  public void setApprover(Integer approver) {
    this.approver = approver;
  }

  public String getApproverName() {
    return approverName;
  }

  public void setApproverName(String approverName) {
    this.approverName = approverName;
  }

  public String getApproveDate() {
    return approveDate;
  }

  public void setApproveDate(String approveDate) {
    this.approveDate = approveDate;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public String getCreateDate() {
    return createDate;
  }

  public void setCreateDate(String createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public String getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(String updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public BigInteger getTotalSize() {
    return totalSize;
  }

  public void setTotalSize(BigInteger totalSize) {
    this.totalSize = totalSize;
  }

  public Integer getUser() {
    return user;
  }

  public void setUser(Integer user) {
    this.user = user;
  }

  public String getListChooseOrgAndUser() {
    return listChooseOrgAndUser;
  }

  public void setListChooseOrgAndUser(String listChooseOrgAndUser) {
    this.listChooseOrgAndUser = listChooseOrgAndUser;
  }

  public String getCreateDateBegin() {
    return createDateBegin;
  }

  public void setCreateDateBegin(String createDateBegin) {
    this.createDateBegin = createDateBegin;
  }

  public String getCreateDateEnd() {
    return createDateEnd;
  }

  public void setCreateDateEnd(String createDateEnd) {
    this.createDateEnd = createDateEnd;
  }

  public String getUpdateDateBegin() {
    return updateDateBegin;
  }

  public void setUpdateDateBegin(String updateDateBegin) {
    this.updateDateBegin = updateDateBegin;
  }

  public String getUpdateDateEnd() {
    return updateDateEnd;
  }

  public void setUpdateDateEnd(String updateDateEnd) {
    this.updateDateEnd = updateDateEnd;
  }

  public ResEntity() {
  }

  public ResEntity(Integer id) {
    this.id = id;
  }

  public String getOperationTag() {
    return operationTag;
  }

  public void setOperationTag(String operationTag) {
    this.operationTag = operationTag;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public String getCategoryPath() {
    return categoryPath;
  }

  public void setCategoryPath(String categoryPath) {
    this.categoryPath = categoryPath;
  }

  public Byte getIsTrash() {
    return isTrash;
  }

  public void setIsTrash(Byte isTrash) {
    this.isTrash = isTrash;
  }

  public String getFileValidTime() {
    return fileValidTime;
  }

  public void setFileValidTime(String fileValidTime) {
    this.fileValidTime = fileValidTime;
  }

  public void setIsDeleted(Boolean isDeleted) {
    this.isDeleted = isDeleted;

  }

  public Boolean getIsDeleted() {
    return isDeleted;
  }

  public Boolean getEnabled() {
    return enabled;
  }

  public void setEnabled(Boolean enabled) {
    this.enabled = enabled;
  }

  public String getEnabledTime() {
    return enabledTime;
  }

  public void setEnabledTime(String enabledTime) {
    this.enabledTime = enabledTime;
  }

  public Date getDeleteTime() {
    return deleteTime;
  }

  public void setDeleteTime(Date deleteTime) {
    this.deleteTime = deleteTime;
  }

  public String getTeminateState() {
    return teminateState;
  }

  public void setTeminateState(String teminateState) {
    this.teminateState = teminateState;
  }

  public Date getTeminateTime() {
    return teminateTime;
  }

  public void setTeminateTime(Date teminateTime) {
    this.teminateTime = teminateTime;
  }

  public String getTeminateReason() {
    return teminateReason;
  }

  public void setTeminateReason(String teminateReason) {
    this.teminateReason = teminateReason;
  }

  public Integer getTeminater() {
    return teminater;
  }

  public void setTeminater(Integer teminater) {
    this.teminater = teminater;
  }

  public String getTeminaterName() {
    return teminaterName;
  }

  public void setTeminaterName(String teminaterName) {
    this.teminaterName = teminaterName;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public String getLockUuid() {
    return lockUuid;
  }

  public void setLockUuid(String lockUuid) {
    this.lockUuid = lockUuid;
  }

  public String getBatchUuid() {
    return batchUuid;
  }

  public void setBatchUuid(String batchUuid) {
    this.batchUuid = batchUuid;
  }
}
