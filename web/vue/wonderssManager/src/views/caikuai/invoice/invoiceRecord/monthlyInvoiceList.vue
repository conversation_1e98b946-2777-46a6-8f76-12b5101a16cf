<template>
  <p class="f4 margin_top_16 spaceBetween">
    <span>所开的发票具体如下：</span>
    <span @click="Hide()" style="color:#337ab7;">切换为按发票类别展示</span>
  </p>
  <ul class="ty-fff" v-if="list.financeInvoiceDetails">
    <li v-for="(item,index) in list.financeInvoiceDetails" :key="index">
      <div>
          <span>
            <span class="padding-r-5">发票号码</span>
            {{ item.invoiceNo }}
          </span>
        <span>
            <span class="padding-r-5">金额</span>
            {{ item.amount }}元
          </span>
        <span>
            <span class="padding-r-5">发票日期</span>
            {{ new
        Date(item.operateDate).format('yyyy-MM-dd')}}
          </span>
        <span>
            <span class="padding-r-5">开给</span>
            {{ item.receiveCorp }}
          </span>
        <p>
            <span class="padding-r-5">
              开票申请
            </span>
          <span class="padding-r-5">{{ item.applicantName }}</span><span>{{ new
        Date(item.applicationTime).format('yyyy-MM-dd hh:mm:ss') }}</span>
        </p>
      </div>
      <div @click="rou(item.id)" class="ic">
        <el-icon>
          <arrow-right/>
        </el-icon>
      </div>
    </li>
  </ul>

</template>

<script>
import {getInvoiceListMonth} from '@/api/financeBill'

export default {
  name: "monthlyInvoiceList",
  data() {
    return {
      list: []
    }
  },
  methods: {
    Hide() {
      this.$router.push({path: "/categoryB"})
    },
    rou(id) {
      this.$router.push({path: '/invoiceDetails', query: {id: id}})
    },
    getList(invoiceDetailsList) {
      // const [year, month] = invoiceDetailsList.monthYear.split("-");
      // const beginDate = new Date(year, month - 1, 1).format('yyyy-MM-dd');
      // const endDate = new Date(year, month, 0).format('yyyy-MM-dd');
      const params = {
        type: invoiceDetailsList.type,
        beginDate: invoiceDetailsList.beginDate,
        endDate: invoiceDetailsList.endDate
      }
      getInvoiceListMonth(params).then(res1 => {
        this.list = res1.data.data
        console.log(res1.data.data, 'res1.data.data')
      }).catch(error => {
        console.error('Error:', error);
      })
    },

  },
  mounted() {
    let invoiceDetailsList = localStorage.getItem('invoiceDetailsList');
    this.invoiceDetailsList = JSON.parse(invoiceDetailsList);
    console.log(invoiceDetailsList, 'invoiceDetailsList')
    this.getList(this.invoiceDetailsList)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

ul {
  li {
    padding: 10px 15px;
    display: flex;
    align-items: center;

    > div:first-child {
      width: 90%;
      display: flex;
      flex-wrap: wrap;
      line-height: 25px;

      > span {
        width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    > div:last-child {
      width: 10%;
      height: 100%;
      display: flex;
      justify-content: end;
    }
  }

  > li:nth-of-type(even) {
    background-color: $tyBounceColorGray;
  }
}

.spaceBetween {
  padding: 2px 15px;
  line-height: 35px;
}
</style>