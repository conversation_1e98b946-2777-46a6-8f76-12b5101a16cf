<template>
  <div id="approvalReimburse">
    <TY_NavTop title="报销审批" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="toHandleCount">
        <div class="nowTime">今天是{{nowTime | formatDay('YYYY-MM-DD dddd')}}</div>
        <div>
          <a class="ui-cell" v-on:click="jump('11' + item.id, '1')" v-for="(item, index) in toHandle" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName | stringSplit(4)}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>申请报销 {{item.amount}} 元</div>
                <div>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div class="nowTime">今天是{{nowTime | formatDay('YYYY-MM-DD dddd')}}</div>
        <div>
          <a class="ui-cell" v-on:click="jump('12' + item.id, '2')" v-for="(item, index) in approved" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName | stringSplit(4)}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>申请报销 {{item.amount}} 元</div>
                <div>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>
export default {
  name: 'approvalReimburse',
  data () {
    return {
      toHandle: [],
      approved: [],
      listenersUid: [],
      nowTime: 0,
      tabValue: '1'
    }
  },
  computed: {
    toHandleCount: function () {
      return this.toHandle.length
    }
  },
  created () {
    this.tabValue = this.$store.getters.getReimburseNav
    let that = this
    this.axios.get('../../../reimburseWindow/getCurrentTime.do')
      .then(function (response) {
        let time = response.data.time
        that.nowTime = time
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('reimburseHandle', function (data) {
        that.toHandle = JSON.parse(data)
        console.log('reimburseHandle session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('reimbursesApproval', function (data) {
        that.approved = JSON.parse(data)
        console.log('reimbursesApproval session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('reimburseHandle', function (data) {
        console.log('reimburseHandle user Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.toHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('reimbursesApproval', function (data) {
        console.log('reimburseHandle user Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.approved.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.approved.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.approved.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    console.log(userId)
    console.log(session)
    this.sphdSocket.send('getApprovalList', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setReimburseNav', tab)
      this.$router.push({
        path: `/approveReimburseDetail2/${id}/0`
      })
    },
    search: function () {
      this.$router.push({
        path: `/approveReimburseQuery/1`
      })
    }
  }
}
</script>
