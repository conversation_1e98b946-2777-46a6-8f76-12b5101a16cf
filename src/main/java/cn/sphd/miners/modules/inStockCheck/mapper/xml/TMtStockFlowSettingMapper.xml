<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.inStockCheck.mapper.TMtStockFlowSettingMapper">
  <resultMap id="BaseResultMap" type="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting">
    <!--@mbg.generated-->
    <!--@Table t_mt_stock_flow_setting-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org" jdbcType="INTEGER" property="org" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="_key" jdbcType="TINYINT" property="key" />
    <result column="_value" jdbcType="VARCHAR" property="value" />
    <result column="enable" jdbcType="BOOLEAN" property="enable" />
    <result column="effect_date" jdbcType="DATE" property="effectDate" />
    <result column="effect_state" jdbcType="TINYINT" property="effectState" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="updator" jdbcType="INTEGER" property="updator" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="operation" jdbcType="TINYINT" property="operation" />
    <result column="previous_id" jdbcType="INTEGER" property="previousId" />
    <result column="version_no" jdbcType="INTEGER" property="versionNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org, category, _key, _value, `enable`, effect_date, effect_state, memo, creator, 
    create_name, create_date, updator, update_name, update_date, `operation`, previous_id, 
    version_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_mt_stock_flow_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_mt_stock_flow_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting (org, category, _key, 
      _value, `enable`, effect_date, 
      effect_state, memo, creator, 
      create_name, create_date, updator, 
      update_name, update_date, `operation`, 
      previous_id, version_no)
    values (#{org,jdbcType=INTEGER}, #{category,jdbcType=VARCHAR}, #{key,jdbcType=TINYINT}, 
      #{value,jdbcType=VARCHAR}, #{enable,jdbcType=BOOLEAN}, #{effectDate,jdbcType=DATE}, 
      #{effectState,jdbcType=TINYINT}, #{memo,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{updator,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{operation,jdbcType=TINYINT}, 
      #{previousId,jdbcType=INTEGER}, #{versionNo,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="org != null">
        org,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="key != null">
        _key,
      </if>
      <if test="value != null">
        _value,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="effectDate != null">
        effect_date,
      </if>
      <if test="effectState != null">
        effect_state,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="previousId != null">
        previous_id,
      </if>
      <if test="versionNo != null">
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="org != null">
        #{org,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BOOLEAN},
      </if>
      <if test="effectDate != null">
        #{effectDate,jdbcType=DATE},
      </if>
      <if test="effectState != null">
        #{effectState,jdbcType=TINYINT},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=TINYINT},
      </if>
      <if test="previousId != null">
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting">
    <!--@mbg.generated-->
    update t_mt_stock_flow_setting
    <set>
      <if test="org != null">
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        _key = #{key,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        _value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BOOLEAN},
      </if>
      <if test="effectDate != null">
        effect_date = #{effectDate,jdbcType=DATE},
      </if>
      <if test="effectState != null">
        effect_state = #{effectState,jdbcType=TINYINT},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=TINYINT},
      </if>
      <if test="previousId != null">
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting">
    <!--@mbg.generated-->
    update t_mt_stock_flow_setting
    set org = #{org,jdbcType=INTEGER},
      category = #{category,jdbcType=VARCHAR},
      _key = #{key,jdbcType=TINYINT},
      _value = #{value,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BOOLEAN},
      effect_date = #{effectDate,jdbcType=DATE},
      effect_state = #{effectState,jdbcType=TINYINT},
      memo = #{memo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      `operation` = #{operation,jdbcType=TINYINT},
      previous_id = #{previousId,jdbcType=INTEGER},
      version_no = #{versionNo,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByValue" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_mt_stock_flow_setting
    where _value = #{id,jdbcType=INTEGER}
  </select>
</mapper>