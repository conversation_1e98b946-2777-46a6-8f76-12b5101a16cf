<template>
  <div class="newLyric">
    <Pageheader title="新增戏曲" :showBack="true" :showClose="true" :runMyBackFun="true" @mycloseFun="cancelAdd" @myBackFun="cancelAdd">
      <div class="submitBtn" v-if="formData.addWord !== '1'" @click="submit">确定</div>
    </Pageheader>
    <div class="mainc">
      <el-form label-position="top" label-width="280px" :rules="rules" :model="formData">
        <el-form-item label="剧目名称" prop="name">
          <el-input v-model="formData.name"></el-input>
        </el-form-item>
        <el-form-item label="选段名称" prop="nameXuanDuan">
          <el-input v-model="formData.nameXuanDuan"></el-input>
        </el-form-item>
        <el-form-item label="流派">
          <el-input v-model="formData.nameLiu"></el-input>
          <p class="blueTip">注：新增的剧目可供制作节目单时选用</p>
        </el-form-item>

        <el-form-item label="直播演唱本曲目时，是否需要加字幕？" prop="addWord">
          <el-radio-group v-model="formData.addWord" size="large">
            <el-radio border label="1">需要</el-radio>
            <el-radio border label="0">不需要</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="formData.addWord === '1'">
          <div class="form-item">
            <div class="flexc">
              <div>原唱</div>
              <div>{{ formData.orlLyc.filePath ? '已选择': '尚未上传' }}</div>
              <div>
                <el-upload v-if="uploadBtn1_txt != '正在上传...'"
                           class="avatar-uploader"
                           :headers="uploadHeaders"
                           :action="uploadAction"
                           ref="upload"
                           :limit="fileLimit"
                           :accept="uploadLyc" :data="postData"
                           :multiple="false" :show-file-list="false"
                           :on-remove="uploadRemove"
                           :on-success="handleSuccess"
                           :before-upload="beforeUpload"
                >
                  <el-link type="primary">{{ uploadBtn1_txt }}</el-link>
                </el-upload>

                <el-link v-else type="primary">{{ uploadBtn1_txt }}</el-link>
                <el-link class="playB" v-if="uploadBtn1_txt==='更换'" @click="playMusic(formData.orlLyc)" type="primary">播放</el-link>
              </div>
            </div>
            <div class="blueTip">注 原唱上传后，为歌词配节奏时更便捷！</div>
          </div>
          <div class="form-item">
            <div class="flexc">
              <div>伴奏</div>
              <div>{{ formData.banLyc.filePath ? '已选择': '尚未上传' }}</div>
              <div>
                <el-upload v-if="uploadBtn2_txt != '正在上传...'"
                           class="avatar-uploader"
                           :headers="uploadHeaders"
                           :action="uploadAction"
                           ref="upload"
                           :limit="fileLimit"
                           :accept="uploadLyc" :data="postData"
                           :multiple="false" :show-file-list="false"
                           :on-remove="uploadRemove"
                           :on-success="handleSuccess2"
                           :before-upload="beforeUpload2"
                >
                  <el-link type="primary">{{ uploadBtn2_txt }}</el-link>
                </el-upload>
                <el-link v-else type="primary">{{ uploadBtn2_txt }}</el-link>
                <el-link class="playB" v-if="uploadBtn2_txt==='更换'" @click="playMusic(formData.banLyc)" type="primary">播放</el-link>
              </div>
            </div>
            <div class="blueTip">注1 选择的伴奏可供直播时与字幕一键使用。</div>
            <div class="blueTip">注2 伴奏仅您自己可见，团队成员见不到。</div>
          </div>
          <el-form-item label="伴奏时长">
            <el-input class="litInput" v-model="formData.banTime.min" :disabled="banEditable"></el-input>&nbsp;分
            &nbsp;&nbsp;
            <el-input class="litInput" v-model="formData.banTime.ss" :disabled="banEditable"></el-input>&nbsp;秒
          </el-form-item>
          <el-form-item label="歌词由谁上传">
            <el-radio v-model="formData.uploadType" label="1">自行上传</el-radio>
            <el-radio v-model="formData.uploadType" label="2">发布任务，团队成员上传</el-radio>
          </el-form-item>
          <div v-if="formData.uploadType === '1'" class="form-item">
            <div class="flexc">
              <div>本曲目的歌词</div>
              <div>{{ false ? '已编辑': '尚未编辑' }}</div>
              <div>
                <el-link type="primary" @click="goLycTip">编辑</el-link>
              </div>
            </div>
          </div>
          <div v-if="formData.uploadType === '2'" class="form-item">
            <span @click="goAssignTasks" class="primary live-button">下一步</span>
          </div>
        </div>
      </el-form>


      <AudioPlayer v-if="playLyc" :playName="playName" :fileUrl="playUrl" @closePlayLyc="closePlayLyc"></AudioPlayer>

    </div>
  </div>
</template>
<script setup>
import { getAudioFileDuratuin, insertSongs } from '@/api/music';
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'
import auth from '@/sys/auth.js'

const router = useRouter();
const formData = reactive({
  from:'newLyricXiQu',
  name:'',
  nameXuanDuan:'',
  nameLiu:'',
  addWord:'',
  trackDuration:'',
  bgmDuration:'',
  publishType:'',
  rhythmOption:'',
  originalPath:'',
  bgmPath:'',
  inllustration:'',
  firstSentence:'',
  lastSentence:'',
  approveStatus:'',
  rhythm:'',
  lyrics:'',
  user:'',
  duration:'',// 歌曲时长
  banTime: { min:'', ss:'' },
  oralTime: { min:'', ss:'' },
  uploadType: '',
  orlLyc:{ duration:'', durationTime:'', filePath:'', state:'' },
  banLyc:{ duration:'', durationTime:'', filePath:'', state:'' },
})
const rules = ref({
  name: [
    { required: true, message: '请输入歌曲名称', trigger: 'blur' },
    {
      min: 3,
      max: 5,
      message: '长度在 3 到 5 个字符',
      trigger: 'blur',
    },
  ],
  nameXuanDuan:[
    { required: true, message: '请输入选段名称', trigger: 'blur' },
  ],
  addWord:[
    { required: true, message: '请选择是否需要加字幕' },
  ],
  banTime:[
    { required: true, message: '请选择伴奏时长' },
  ],
  uploadType:[
    { required: true, message: '请选择歌词由谁上传' },
  ]
})
const uploadUrl = localStorage.getItem('uploadUrl');
const webRoot = localStorage.getItem('webRoot');
const playLyc = ref(false);
const playUrl = ref('');
const playName = ref('');
const banEditable = ref(false);
const uploadBtn1_txt = ref('选择');
const uploadBtn2_txt = ref('选择');
const uploadLyc = '.mp3,.cda,.wav,.aif,.aiff,.mid,.wma,.ra,.vqf,.ape'
const fileLimit = ''
const postData = reactive({
  module: '字幕精灵',
  userId: auth.getUserID()
})
const uploadHeaders = { 'Token': auth.getToken() }
const uploadAction = webRoot + '/uploads/uploadfyByFile.do'

console.log('uploadAction=', uploadAction)

onMounted(() => {
  let obsNewLyc = localStorage.getItem('obsNewLyc')
  if(obsNewLyc){
    banEditable.value = true
    obsNewLyc = JSON.parse(obsNewLyc)
    formData.id = obsNewLyc.id
    formData.name = obsNewLyc.name
    formData.addWord = '1'
    formData.orlLyc = { duration:obsNewLyc.trackDuration, durationTime:obsNewLyc.trackDuration, filePath:obsNewLyc.originalPath, state:'' }
    let oralTimeObj = formatTime(obsNewLyc.trackDuration)
    formData.orlLyc.durationTime = oralTimeObj.min + '分' + oralTimeObj.ss + '秒'
    formData.banTime.min = oralTimeObj.min
    formData.banTime.ss = oralTimeObj.ss
    if(obsNewLyc.bgmPath){
      formData.banLyc = { duration: obsNewLyc.bgmDuration, durationTime:obsNewLyc.bgmDuration, filePath:obsNewLyc.bgmPath, state:'' }
      let banTimeObj = formatTime(obsNewLyc.bgmDuration)
      formData.orlLyc.durationTime = banTimeObj.min + '分' + banTimeObj.ss + '秒'
      formData.banTime.min = banTimeObj.min
      formData.banTime.ss = banTimeObj.ss
    }

  }else{
    banEditable.value = false
    // getAudioFileDuratuinFun()
  }

});
watch(formData,(newValue,oldValue)=>{
  // console.log("新值是");
  // console.log(newValue);
  // console.log( "旧址是");
  // console.log(oldValue);
})
const uploadRemove = (file) => {
  console.log('uploadRemove = ', file)
}

const handleSuccess2 = (res,file) => { // 伴唱
  uploadBtn2_txt.value = '更换'
  banEditable.value = true
  console.log('handleSuccess =', res)
  formData.banLyc.filePath = res.filename
  formData.banLyc.fileUid = res.fileUid
  const audio = new Audio()
  audio.src = uploadUrl + res.filename
  countAudioTime(audio, 'banLyc')
}
const handleSuccess = (res,file) => { // 原唱
  uploadBtn1_txt.value = '更换'
  banEditable.value = true
  console.log('handleSuccess =', res)
  formData.orlLyc.filePath = res.filename
  formData.orlLyc.fileUid = res.fileUid
  const audio = new Audio()
  audio.src = uploadUrl + res.filename
  countAudioTime(audio, 'orlLyc')
}
const beforeUpload2 = (file) => {
  uploadBtn2_txt.value = '正在上传...'
  console.log(' beforeUpload file=', file)
  formData.banLyc = { 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' }
  formData.banLyc.filename = file.name
}
const beforeUpload = (file) => {
  uploadBtn1_txt.value = '正在上传...'
  console.log(' beforeUpload file=', file)
  formData.orlLyc = { 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' }
  formData.orlLyc.filename = file.name
}
const  countAudioTime = async (audio , type) => {
  while (isNaN(audio.duration) || audio.duration === Infinity) {
    // 延迟一会 不然网页都卡死
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  let dur0 = audio.duration ; // 单位是 秒
  let dain = String(dur0).split('.')[1]
  let dur = String(dur0).split('.')[0]
  console.log('音频的总时长:',dur)
  formData[type].duration = dur0 // 秒
  let min = parseInt(dur/60)
  let ss = dur%60
  formData[type].durationTime = `${min}:${ss}.${dain}` // 12:12.909090
  formData[type].durationTime2 = `${min}:${ss}` // 12:12.909090
  console.log('min=', min)
  console.log('ss=', ss)
  if(type === 'banLyc'){
    if(formData.oralTime.min){

    }else{
      formData.banTime.min = min
      formData.banTime.ss = ss
    }
  }else if(type === 'orlLyc'){
    formData.oralTime = { 'min':min, 'ss': ss}
    formData.banTime.min = min
    formData.banTime.ss = ss
  }
}
const formatTime = (timeKey) => {
  let str = ''
  timeKey = timeKey> 0 ? timeKey : 0
  let dur0 = timeKey/1000 // 单位 秒
  let dain = String(dur0).split('.')[1]
  let dur = String(dur0).split('.')[0]
  console.log('音频的总时长:',dur)
  let min = parseInt(dur/60)
  let ss = dur%60
  // str = min + '分' + ss + '秒'
  str = { 'min':min , 'ss':ss }
  return str
}
const getAudioFileDuratuinFun = ()=> {
  console.log('获取')
  getAudioFileDuratuin().then( res => {
    console.log(res)
    console.log('formData.value', formData)
    console.log('formData.value', formData.banLyc)

    formData.banLyc = res.data
    formData.orlLyc = res.data
    console.log('orlLyc 的值已经变化了')
    let durStr = formData.orlLyc.durationTime
    let fenArr = durStr.split('分')
    formData.banTime.min = fenArr[0]
    formData.banTime.ss = fenArr[1].split('秒')[0]
    banEditable.value = true
  })
}
const closePlayLyc = (val)=> {
  playLyc.value = val
}
const goAssignTasks = ()=> {
  console.log('formData = ：', formData)
  let formData2 = (JSONBig.stringify(formData))
  if(formData.name.length == 0){
    ElMessage.info('请先输入歌曲名称！');
    return false
  }
  if(uploadBtn1_txt.value === '正在上传...'){
    ElMessage.info('原唱正在上传，请稍等');
    return false
  }else if(uploadBtn2_txt.value === '正在上传...'){
    ElMessage.info('伴唱正在上传，请稍等');
    return false
  }
  if(formData.banTime.min.length == "" || formData.banTime.ss.length == ""){
    ElMessage.info('请先输入伴奏时长！');
    return false
  }
  localStorage.setItem('newlycdata', formData2)
  router.push({
    name: 'assignTasks',
  });
}
// creator：hxz 播放音乐
const playMusic = (lyc)=> {
  console.log('playMusic lyc=', lyc)
  playLyc.value = true
  playName.value = formData.name
  playUrl.value = uploadUrl + lyc.filePath
  console.log('playUrl.value=', playUrl)
  console.log(' playName.value=',  playName)
}
const goLycTip = () => {
  console.log('formData = ：', formData)
  formData.type = 2
  let formData2 = (JSONBig.stringify(formData))
  if(formData.name.length == 0){
    ElMessage.info('请先输入戏曲名称！');
    return false
  }
  if(uploadBtn1_txt.value === '正在上传...'){
    ElMessage.info('原唱正在上传，请稍等');
    return false
  }else if(uploadBtn2_txt.value === '正在上传...'){
    ElMessage.info('伴唱正在上传，请稍等');
    return false
  }
  if(formData.banTime.min.length == "" || formData.banTime.ss.length == ""){
    ElMessage.info('请先输入伴奏时长！');
    return false
  }

  localStorage.setItem('newlycdata', formData2)
  router.push({
    name: 'lyricTipXiQu',
    params: {
    },
  });
}
const submit = ()=> {
  console.log('formData = ：', formData)
  let formData2 = JSONBig.parse(JSONBig.stringify(formData))

  // 12 inllustration 说明 publishType=2时非空就传
  // 13 firstSentence 第一句 publishType=2时非空就传
  // 14 lastSentence 最后一句 publishType=2时非空就传
  // 15 approveStatus 审批状态 1-申请提交 2-审合通过 3-申请撤销 captionRequired=0时传2；captionRequired=2且publishType=1时传2；captionRequired=2且publishType=2时传1
  // 16 versionNo 版本号 传0
  // 17 rhythm 节奏歌词文本(含时间标签)
  // 18 lyrics 歌词文本 从剪贴板获取的歌词文本
  // 19 user string 一个user的接送字符串 应该是这样 [{"user":"1"}，{"user":"2"}] captionRequired=2且publishType=2才传这个值
  // 20 isSelected 是否被选择 1-被选择 0-未选择 这里固定传1
  console.log('formData2', formData2)
  let data = {
    'name':formData2.name, // 歌曲名称
    'type':2, // 类型:1-歌曲,2-戏曲 戏曲的先不做
    'captionRequired':formData2.addWord, // 是否需要加字幕 0-否 1-是 传0的时候5以下就都不用传了
  }
  data.selections = formData2.nameXuanDuan
  data.genres = formData2.nameLiu
  if(data.name.length == 0 ){
    ElMessage.info('请先输入歌曲名称！');
    return false
  }
  if( data.selections.length == 0){
    ElMessage.info('请先输入选段名称！');
    return false
  }
  if( data.captionRequired.length == 0 ){
    ElMessage.info('请选择是否需要加字幕！');
    return false
  }

  if(formData2.addWord === 1){
    data.trackDuration = formData2.trackDuration ; // 歌曲时长 上个接口返回的 duration 字段
    data.bgmDuration = formData2.bgmDuration ; // 伴奏时长 上个接口返回的duration字段
    data.publishType = formData2.publishType ; // 发布方式:1-自行上传,2-发布任务,团队成员上传
    data.rhythmOption = formData2.rhythmOption ; // 节奏选项:1-原唱,2-伴奏  这个值可能有点问题我换了新东西 看看能否接收到
    data.originalPath = formData2.originalPath ; // 原唱存储路径 上个接口返回的filePath字段

    data.bgmPath = formData2.bgmPath ; // 伴唱存储路径 上个接口返回的filePath字段
    data.inllustration = formData2.inllustration ; // 说明 publishType=2时非空就传
    data.firstSentence = formData2.firstSentence ; // 第一句 publishType=2时非空就传
    data.lastSentence = formData2.lastSentence ; // 最后一句 publishType=2时非空就传
    data.versionNo = 0 ; //  版本号 传0
    data.rhythm = formData2.rhythm  ; // 节奏歌词文本(含时间标签)
    data.lyrics = formData2.lyrics  ; // 歌词文本 从剪贴板获取的歌词文本
    data.user = formData2.user  ; // 一个user的接送字符串 应该是这样 [{"user":"1"}，{"user":"2"}] captionRequired=2且publishType=2才传这个值
    data.isSelected = 1  ; // 是否被选择 1-被选择 0-未选择 这里固定传1
  }

  // approveStatus:  captionRequired=0时传2；captionRequired=2且publishType=1时传2；captionRequired=2且publishType=2时传1
  if(data.captionRequired === 0 || (data.captionRequired === 1 && data.publishType === 1)){
    data.approveStatus = 2
  }
  if(data.captionRequired === 1 && data.publishType === 2){
    data.approveStatus = 1
  }
  insertSongs(data).then(res =>{
    console.log('insertSongs', res)
    let success = res.success
    if(success === 1){
      ElMessage.success('新增成功！');
      router.push("lyricTaskXiQu")
    }else{
      ElMessage.error('新增失败！');

    }
  })

}
// 取消新增
const cancelAdd = () => {
  console.log('cancelAdd')
  localStorage.removeItem('newlycdata')
  localStorage.removeItem('patData')
  localStorage.removeItem('agreeObj')
  localStorage.removeItem('lycTxtObj')
  localStorage.removeItem('assignTasks')
  localStorage.removeItem('staffList')
  router.push({
    name: 'lyricTaskXiQu',
  });
}

</script>
<style lang="scss" scoped>
@import "../../style/common.scss";
.newLyric{
  .avatar-uploader{
    display: inline-block;
    margin-right: 10px;
    position: relative;
  }
  .playB{
    position: relative;
    top: -2px;
    font-size: 0.9em;
  }
  .submitBtn{
    width: 100%;
    height: 100%;
  }
  .mainc{
    color:#333;
    padding:20px 16px;
  }
  .litInput{
    width: 100px;
  }
  .blueTip{
    color: #0b94ea;
    font-size: 0.8em;
    line-height: 25px;
  }
  .flexc{
    display: flex;
    >div{
      flex: 1;
    }
  }
  .form-item{
    margin-bottom: 10px;
    font-size:0.9em ;
    color: #606266;
  }
  .marl20{
    margin-left: 20px;
  }
}

.el-button--text {
  color: #FFF;
  background-color:$livePageHeadBg ;
  border-color: $livePageHeadBg;
  height: 34px;  line-height: 34px;  padding: 0 20px;
}
.el-button--text:focus,.el-button--text:hover {
  color: #FFF;
  background-color: $livePageHeadBg;  border-color: $livePageHeadBg ;
  height: 34px;  line-height: 34px;  padding: 0 20px;
}
.el-button--text:active {
  color: #FFF;
  background-color:$livePageHeadBg ;
  border-color:$livePageHeadBg ;
  height: 34px;
  line-height: 34px;  padding: 0 20px;
}
</style>
