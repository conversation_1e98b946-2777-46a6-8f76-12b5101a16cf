package cn.sphd.miners.modules.accountant.service;

import cn.sphd.miners.modules.system.entity.User;

/**
 * <AUTHOR>
 * @create 2018-09-20 16:24
 * @description
 **/
public interface FinanceRelevanceService {

    //会计模块内部调用的接口，用于关联产生财务相关科目，即1002的子科目，由财务新增对公账户时触发
    int generateFinanceSubjects(int oid,int financeID,String accountName,String accountNO,User user);

    //财务模块调用的接口，用于关联产生财务相关科目，和上面接口的区别是该接口需要检测会计模块是否开启，开启后才会关联产生科目
    int generateSubjectsByFinance(int oid,int financeID,String accountName,String accountNO,User user);

    //关联修改财务对公户
    int financeRelevanceModify(int oid,int financeID,String accountName,String accountNO,User user);

    /*检测机构是否开启了会计模块*/
    boolean enableAccountantModule(Integer oid);

}
