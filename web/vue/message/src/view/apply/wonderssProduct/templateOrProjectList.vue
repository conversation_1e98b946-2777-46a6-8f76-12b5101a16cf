<!-- 模板管理 -->
<template>
  <div id="templateOrProjectList" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <p class="tip">{{ tipStr }}</p>
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="jump(item)" v-for="(item, index) in list" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_title">
              <span class="approve_name" v-html="item.description"></span>
              <p class="txt-right">{{item.createName }} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #templateOrProjectList{
  .tip{ padding:5px 15px; background:rgba(199, 212, 228, 0.8); margin-top:0;  }
  .txt-right{ padding:5px 15px; text-align: right; font-size: 0.8em; color:#999; }
  }
</style>
<script>
export default {
  name: 'templateOrProjectList',
  data () {
    return {
      code: '',
      title: '',
      tipStr: '',
      loading: true,
      list:[],
      listenersUid: []
    }
  },
  created: function () {
    this.code = this.$route.params.code
    this.getList()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let that = this
      let data = { 'userID': that.sphdSocket.user.userID }
      let url = ''
      let subscribeUrl = ''
      let subscribeID = ''
      switch (this.code){
        case 'templateFoundApply':
          this.title = '模板创建'
          this.tipStr = '以下模板创建申请有待审批'
          url = '../../../productApproval/templateFoundApplyList.do'
          subscribeUrl = 'templateFoundApplyList'
          break;
        case 'templateFoundApproval':
          this.title = '模板创建'
          this.tipStr = '以下模板创建申请有待审批'
          url = '../../../productApproval/templateFoundApprovalList.do'
          subscribeUrl = 'templateFoundApprovalList'
          break
        case 'templateEditApply':
          this.title = '模板修改'
          this.tipStr = '以下模板提交了修改申请，有待审批'
          url = '../../../productApproval/templateEditApplyList.do'
          subscribeUrl = 'templateEditApplyList'
          break
        case 'templateEditApproval':
          this.title = '模板修改'
          this.tipStr = '以下模板提交了修改申请，有待审批'
          url = '../../../productApproval/templateEditApprovalList.do'
          subscribeUrl = 'templateEditApprovalList'
          break
        case 'productFoundApply':
          this.title = '产品创建'
          this.tipStr = '以下产品创建申请有待审批'
          url = '../../../productApproval/productFoundApplyList.do'
          subscribeUrl = 'productFoundApplyList'
          break
        case 'productFoundApproval':
          this.title = '产品创建'
          this.tipStr = '以下产品创建申请有待审批'
          url = '../../../productApproval/productFoundApprovalList.do'
          subscribeUrl = 'productFoundApprovalList'
          break
        case 'productEditApply':
          this.title = '产品修改'
          this.tipStr = '以下产品提交了修改申请，有待审批'
          url = '../../../productApproval/productEditApplyList.do'
          subscribeUrl = 'productEditApplyList'
          break
        case 'productEditApproval':
          this.title = '产品修改'
          this.tipStr = '以下产品提交了修改申请，有待审批'
          url = '../../../productApproval/productEditApprovalList.do'
          subscribeUrl = 'productEditApprovalList'
          break
        case 'productRecoveryApply':
          this.title = '产品恢复使用'
          this.tipStr = '以下产品提交了恢复使用的申请，有待审批'
          url = '../../../productApproval/productRecoveryApplyList.do'
          subscribeUrl = 'productRecoveryApplyList'
          break
        case 'productRecoveryApproval':
          this.title = '产品恢复使用'
          this.tipStr = '以下产品提交了恢复使用的申请，有待审批'
          url = '../../../productApproval/productRecoveryApprovalList.do'
          subscribeUrl = 'productRecoveryApprovalList'
          break
        default:
          console.log("没有匹配到类型")
      }
      if(url === ''){
        that.$message({
          type: 'error',
          message: '链接错误啦，返回重试！'
        })
        this.$router.back(-1)
      }
      subscribeID = this.sphdSocket.subscribe(subscribeUrl, function (res) {
        let data = JSON.parse(res)
        console.log('获取的返回值:' , data)
        let ap = data.ap
        let operate = Number(data.operate)
        if (operate > 0) {
          that.list.unshift(ap)
        } else if (operate < 0) {
          let _index = -1
          that.list.forEach(function (item, index) {
            if (item.id === ap.id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.list.splice(_index, 1)
          }
        }
      }, null, 'user')
      that.listenersUid.push(subscribeID)
      console.log(url,data)
      that.axios.post(url, {
        ...data
      }).then(function (response) {
        that.loading = false
        that.list = response.data.data || []
        console.log('列表', data)
      }).catch(function (error) {
        console.log(error)
        that.loading = false
        that.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    jump: function (item) {
      let that = this
      this.$router.push({
        path: `/templateOrProjectDetails/${that.code}/${item.id}/0`
      })
    },
    search: function () {
      let that = this
      this.$router.push({
        path: `/templateOrProjectSearch/${that.code}`
      })
    }
  }
}
</script>
