<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="reimburseVerificationBillsQueryPer" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="个人报销的票据审核" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ttl11">
        <p>您要查询：</p>
        <p>{{ beginTimeShow |  formatDay('YYYY年MM月DD日')}} - {{ endTimeShow |  formatDay('YYYY年MM月DD日')}}期间</p>
        <p>您经手且{{ approveStatus| filterSta }}<span v-if="applyUserName">{{applyUserName}}</span>的个人报销申请</p>
      </div>
      <div class="ttl1">符合查询条件的数据共如下{{list.length}}条。</div>
      <div>
        <a class="cellc" v-on:click="jump('34' + item.id, 0 , 3 , 1)" v-for="(item, index) in list" v-bind:key="index">
          <div class="item_fn">
            <div>{{item.createName | stringSplit(4)}}</div>
            <div>
              <p>申请报销 {{item.amount}} 元</p>
              <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
            </div>
          </div>
          <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #reimburseVerificationBillsQueryPer{
    .cellc{ display: block; padding: 10px 15px;  font-size: 14px; }
    .cellc:nth-child(odd){ background:#fff;  }
    .ttl1{ background:#DEEBF6; padding:10px;     }
    .ttl11{ background: #edf3c6; padding:10px;     }
    .clr2{clear: both;  }
    .btn{ border:1px solid #ccc;padding:5px 15px; border-radius:4px;   }
    .active{ border-color:#0da394; color: #0da394;  }
    .txt_right{ text-align: right;  }
    .item_fn{
      display: flex;
      div:nth-child(1){ flex:1;  }
      div:nth-child(2){ flex:3;  }
    }
  }
</style>
<script>
export default {
  name: 'reimburseVerificationBillsQueryPer',
  data () {
    return {
      searchInfo: {},
      loading: true,
      beginTime: '',
      endTime: '',
      chargeResult: '',
      searchType: Number(this.$route.params.id),
      applyTime: '',
      list: []
    }
  },
  filters: {
    filterSta: function (num) {
      switch (num) {
        case '1':
          return '票据在线审批未通过'
        case '2':
          return '审批流程被驳回'
        case '3':
          return '线下票据不一致'
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created: function () {
    let _this = this
    this.searchInfo = JSON.parse(localStorage.getItem('query'))
    console.log('searchInfo', this.searchInfo)
    _this.beginTime = _this.searchInfo.beginDate + ' 00:00:00'
    _this.endTime = _this.searchInfo.endDate + ' 23:59:59'
    _this.type = _this.searchInfo.type
    _this.oid = _this.sphdSocket.user.oid
    _this.approveStatus = String(_this.searchInfo.approveStatus)
    _this.applyId = _this.searchInfo.applyId || ''
    _this.applyUserName = _this.searchInfo.applyUserName
    if (!_this.applyId) {
      _this.applyUserName = ''
    }
    _this.getList()
  },
  methods: {
    getList: function () {
      let _this = this
      let url = '../../../reimburseWindow/verificationBillsQuery.do'
      let data = {
        'userId': _this.sphdSocket.user.userID,
        'type': _this.type,
        'oid': _this.oid,
        'approveStatus': _this.approveStatus,
        'applyId': _this.applyId,
        'beginDate': _this.beginTime,
        'endDate': _this.endTime
      }
      _this.axios.post(url, {
        ...data
      }).then(function (response) {
        let data = response.data.data
        _this.list = data['personnelReimburseList'] || []
        _this.beginTimeShow = data['beginDate']
        _this.endTimeShow = data['endDate']
        console.log('查询结果', data)
        _this.loading = false
      }).catch(function (error) {
        console.log(error)
        _this.loading = false
        _this.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    jump: function (id, param) {
      console.log('param : ' + param)
      this.$router.push({
        path: `/approveReimburseDetail/${id}/${param}`
      })
    }
  }
}
</script>
