package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.generalAffairs.dto.PersonnelAttendanceUserDto;
import cn.sphd.miners.modules.generalAffairs.dto.PunchDto;
import cn.sphd.miners.modules.generalAffairs.dto.WorkOffBlockDto;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.iot.entity.IotTerminal;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.ApplicationContext;
import org.springframework.ui.Model;
import org.springframework.util.Assert;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * WorkAttendanceService_in_generalAffairs
 * @author: wuyu
 * @since: 3.9
 * @date: 2025-04-21 13:42:39
 **/
public interface WorkAttendanceService {
    //间隔时间，超过这个时间的考勤事件才被处理
    long operateInteval = TimeUnit.MINUTES.toMillis(10);
    String methodName = "runAttendanceTask";
    String attendanceUserEventKey = "miners:AttendanceUserEvent:";
    //到岗类型: 类型:1-假/离岗,2-班/到岗
    enum WorkdayType {
        DayOff("1", "2", (byte)1),//假/离岗
        Workday("2", "1", (byte)2),//班/到岗
        Multiple("3", "3", (byte)3);//请假全天，既是到岗又是离岗
        private String name;
        private String state;
        private Byte index;
        WorkdayType(String name, String state, Byte index) {
            this.name = name;
            this.state = state;
            this.index = index;
        }
        public String getName() {
            return name;
        }

        public String getState() {
            return state;
        }

        public Byte getIndex() {
            return index;
        }
        //无需考勤
        public Boolean noNeed() {return DayOff.equals(this) || Multiple.equals(this);}
        //是否在岗
        public boolean onWork() {return Workday.equals(this) || Multiple.equals(this);}
        //用数字找到枚举对象
        public static WorkdayType getByIndex(Byte index) {
            return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().orElse(null);
        }
        //用字符串找到枚举对象
        public static WorkdayType getByName(String name) {
            return Arrays.stream(values()).filter(e -> e.getName().equals(name)).findAny().orElse(null);
        }
        //用state字符串找到枚举对象
        public static WorkdayType getByState(String state) {
            return Arrays.stream(values()).filter(e -> e.getState().equals(state)).findAny().orElse(null);
        }
        public static boolean checkMultipleState(WorkdayType source, WorkdayType type) {
            return source!=null && (source.equals(type) || source.equals(WorkdayType.Multiple));
        }
        public static List<WorkdayType> getMultipleStates(WorkdayType source) {
            return Arrays.asList(source, WorkdayType.Multiple);
        }
    }
    //考勤设置表:操作:0-正常,1-增,2-删,3-改,4-修改录入日期,5-修改迟到早退和请假期设置
    enum AttendanceConfigOperation {
        normal("normal", (byte)0),
        create("create", (byte)1),
        delete("delete", (byte)2),
        modify("modify", (byte)3),
        modifyEntryDate("modifyEntryDate", (byte)4),
        modifyLateLeave("modifyLateLeave", (byte)5);
        private String name;
        private Byte index;
        AttendanceConfigOperation(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //考勤设置表:考勤模式:1-考勤宝,2-手工录入
    enum ConfigAttendancePattern {
        attendanceTreasure("attendanceTreasure", "考勤宝", (byte)1, 2),
        manualEntry("manualEntry", "手工录入", (byte)2,1);
        private String name;
        private String info;
        private Byte index;
        private Integer source;
        ConfigAttendancePattern(String name, String info, Byte index, Integer source) {
            this.name = name;
            this.info = info;
            this.index = index;
            this.source = source;
        }
        public String getName() {
            return name;
        }

        public String getInfo() {
            return info;
        }

        public Byte getIndex() {
            return index;
        }

        public Integer getSource() {return source;}

        public static ConfigAttendancePattern getByIndex(Byte index) {
            return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().orElse(null);
        }
        public static ConfigAttendancePattern getBySource(Integer source) {
            return Arrays.stream(values()).filter(e -> e.getSource().equals(source)).findAny().orElse(null);
        }

        /**
         * toString 重写方法
         * @apiNote 被 WorkAttendanceController.getAttendancePattern.do 使用
         * <AUTHOR>
         * @since 3.8
         * @return String
         * @date 2025-04-24 13:14:38
         **/
        @Override
        public String toString() {
            return "{index:"+getIndex()+",name:'"+getName()+"',info:'"+getInfo()+"',source:'"+getSource()+"'}";
        }
    }

    //人事-员工打卡表:打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
    enum PunchType {
        goToWork("上班", WorkdayType.Workday, (byte)1),
        offWork("下班", WorkdayType.DayOff, (byte)2),
        forenoon("午前", WorkdayType.DayOff, (byte)3),
        afternoon("午后", WorkdayType.Workday, (byte)4),
        leavePost("离岗", WorkdayType.DayOff, (byte)5),
        returningToWork("返岗", WorkdayType.Workday, (byte)6),
        beforeOvertime("加班前", WorkdayType.Workday, (byte)7),
        afterOvertime("加班后", WorkdayType.DayOff, (byte)8),
        other("待定", WorkdayType.Multiple, (byte)0);
        private String name;
        private WorkdayType workdayType;
        private Byte index;
        PunchType(String name, WorkdayType workdayType, Byte index) {
            this.name = name;
            this.workdayType = workdayType;
            this.index = index;
        }
        public String getName() {
            return name;
        }

        public WorkdayType getWorkdayType() {
            return workdayType;
        }

        public Byte getIndex() {
            return index;
        }
        public static PunchType getByIndex(Byte index) {
            return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().orElse(null);
        }
    }

    //员工考勤明细打卡对照表: 类型:1-开始,2-结束
    enum DetailPunchType {
        begin("begin", (byte)1),
        end("end", (byte)2);
        private String name;
        private Byte index;
        DetailPunchType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //考勤类型:0-未考勤/待定，1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,9-其他,-1 无需考勤, -2 修改考勤作息表（班改假或假改班）, -3 打卡
    enum AttendanceType {
        punch("-3", "打卡", null, (byte)-3),
        workdayLieu("-2", "作息表修改法定调休", null, (byte)-2),//仅用于考勤修改作息时间表的过去和当日的值被修改
        noNeed("-1", "无需考勤", null, (byte)-1),
        pending("0", "未考勤/待定", null, (byte)0),
        normal("1", "正常", null, (byte)1),
        late("2", "迟到", null, (byte)2),
        leaveEarly("3", "早退", null, (byte)3),
        goOut("4", "外出", null, (byte)4),
        leave("5", "请假", "leaveService", (byte)5),
        travel("6", "出差", null, (byte)6),
        absenteeism("7", "旷工", null, (byte)7),
        overtime("8", "加班", "overtimeService", (byte)8),
        other("9", "其他", null, (byte)9);
        private String name;
        private String description;
        private String businessService;
        private Byte index;
        AttendanceType(String name, String description, String businessService, Byte index) {
            this.name = name;
            this.description = description;
            this.businessService = businessService;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public String getDescription() {return description;}

        public String getBusinessService() {
            return businessService;
        }

        public Byte getIndex() {
            return index;
        }
        public static AttendanceType getByIndex(Byte index) {
            return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().orElse(null);
        }
        public static AttendanceType getByName(String name) {
            return Arrays.stream(values()).filter(e -> e.getName().equals(name)).findAny().orElse(null);
        }
        public static AttendanceType getByDescription(String description) {
            return Arrays.stream(values()).filter(e -> e.getDescription().equals(description)).findAny().orElse(null);
        }
        public static AttendanceType getByBusinessService(String businessService) {
            Assert.notNull(businessService, "businessService must not be null!");
            return Arrays.stream(values()).filter(e -> businessService.equals(e.getBusinessService())).findAny().orElse(null);
        }
        //获取考勤事件的types
        public static List<AttendanceType> getEventTypes() {
            return Arrays.stream(values()).filter(e-> !Arrays.asList(noNeed, pending, normal, late, leaveEarly, absenteeism, other).contains(e)).toList();
        }
        //获取考勤审批的types，有从对应的申请表获取数据
        public static List<AttendanceType> getApprovalAttendanceEventTypes() {
            return Arrays.stream(values()).filter(e-> !Arrays.asList(punch, workdayLieu,noNeed, pending, normal, late, leaveEarly, absenteeism, other).contains(e)).toList();
        }
        //前端列表用
        public static List<AttendanceType> getShowList() {
            return Arrays.stream(values()).filter(e-> !Arrays.asList(punch, workdayLieu,noNeed, pending, normal).contains(e)).toList();
        }
        //不同的话，多重
        public static List<AttendanceType> getMultiple() {
            return Arrays.stream(values()).filter(e-> !Arrays.asList(punch, workdayLieu,noNeed, pending, normal, overtime).contains(e)).toList();
        }
    }

    //人事_打卡设备表: 类型:1-设备,2-wechat
    enum AttendanceTerminaType {
        device("device", (byte)1),
        wechat("wechat", (byte)2);
        private String name;
        private Byte index;
        AttendanceTerminaType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
        public static AttendanceTerminaType getByIndex(Byte index) {
            return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().orElse(null);
        }
    }
    /**
     * getAttendanceEventService 按照名称获取AttendanceEventService
     * <AUTHOR>
     * @since 4.0
     * @param serviceName
     * @return AttendanceEventSerivce
     * @date 2025-05-12 13:28:40
     **/
    default AttendanceEventSerivce getAttendanceEventService(String serviceName) {
        ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
        return ac.getBean(serviceName, AttendanceEventSerivce.class);
    }
    default void asyncUpdateAttendanceUserEvents () {
        String lockKey;
        if((lockKey = getDlmtLock()) != null) {
            updateAttendanceUserEvents();
            releaseDlmLock(lockKey);
        }
    }
    String getDlmtLock();
    String getDlmtLock(int times);//升级专用，解决首次运行超时问题
    void releaseDlmLock(String lockKey);
    void runAttendanceTask();
    void debugRunAttendanceTask(Date now, Integer oid);
    void runAttendance(Date now);

    void updateAttendanceUserEvents();//无加锁接口，内部使用，外部调用请使用asyncUpdateAttendanceUserEvents

    void setAttendanceUserEvent(User user, Date operateTime, AttendanceType type);
    void setAttendanceUserEvent(Pair<Integer, WorkOffBlockDto> eventMap);
    //获取系统启用时间
    Date getStartUsingSystemTime(Integer oid);
    //判断是否已经设置班/假（考勤设置例外表）
    boolean hasPersonnelAttendanceExceptionByMonth(Integer oid,Date beginTime,Date endTime);
    Date getPreWorkDate(Date currendDate,Integer oid);
    void clearWorkDateCaches(Integer oid);
    //获取机构指定日期的考勤录入时间
    Date getInputTime(Integer oid, Date openDate);
    //获取机构指定所有考勤生效时间，按时间倒排，有缓存，请勿修改参数和返回值
    List<Date> getPersonnelAttendanceConfigsOpenDates(Integer oid);
    //获取机构指定生效时间的全部考勤部门和考勤设置，有缓存，请勿修改参数和返回值
    Pair<Map<Integer, Integer>, Map<Integer,PersonnelAttendanceConfig>> getPersonnelAttendanceConfigsByExactOpenDate(Integer oid, Date openDate);
    //根据机构查找考勤设置
    PersonnelAttendanceConfig getOnePersonnelAttendanceConfigByOpenDate(Integer oid, Date openDate);
    //根据部门查找考勤设置
    PersonnelAttendanceConfig getPersonnelAttendanceConfigByDept(Integer oid, Integer deptId, Date openDate);
    //按时间段获取机构作息时间表
    List<PersonnelAttendanceException> getPersonnelAttendanceExceptionByMonth(Integer oid, Date beginTime, Date endTime);
    //获取机构指定日期的作息时间表
    PersonnelAttendanceException getPersonnelAttendanceException(Integer oid, Date exceptionDate);
    //设置考勤设置
    void attendanceTimeSetting(String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,String personnelAttendanceConfigs,User user);
    //修改/设置考勤设置
    PersonnelAttendanceConfig addPersonnelAttendanceConfig(User user,Date inputTime,Date beginTime,Date endTime,Date openDate,String type,Boolean isBreak, Date breakBegin,Date breakEnd,Byte operation,Byte attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,Integer recordId,String departmentIds);
    //人工录入考勤
    void addOrUpdatePersonnelAttendanceManual(PersonnelAttendanceUser personnelAttendanceUser, Date date, Integer dtype, String kuangGong, User operator);  //考勤录入的处理接口
    JsonResult clock(User user, WorkdayType type, Date punchTime, String deviceUuid, AttendanceTerminaType terminaType, String terminalUuid, String brand, String model, PunchType punchType, Integer leaveId, Integer overTimeId, String image);
    List<PunchDto> getPersonnelAttendancePunches(Date start, NewDateUtils.Period period, Date end, PunchDto search, User user, PageInfo pageInfo);
    //获取各种类型的考勤信息
    Map<String,Object> getAllTypeAttendance(Integer oid,Integer deptId,Integer userId,Date currentTime);

    //调整考勤页面的处理项
    void attendance(Model model, User user);

    List<PersonnelAttendanceRecord> getPersonnelAttendanceRecordByOid(Integer oid,String operation,Date effectDate,PageInfo page);

    List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigList(Integer oid,String type,Date openDate);

    Map<String,Object> setTimeTable(User user,List timeTable);

    Map<String,Object> getTimeTable(Integer oid,Date timeTable);
    //获取当前使用的考勤规则时间
    Date getCurrentsingSystemTime(Integer oid);

    List<PersonnelAttendanceConfig> attendanceDept(Date openDate,Integer oid,String type);

    JsonResult getAttendanceTime(User user,String type);

    List<PersonnelAttendanceUserDto> getAttendanceList(Integer oid, Date today, Date yesterday, PageInfo pageInfo);

    Date getBeginTime(Integer oid, Integer deptId, Date openDate);

//    void setDefaultWorkAttendance(Integer oid);

    Map<String, Object> returnTime(Integer oid, Integer deptId, Date openDate, Map<String, Object> map);

    Long countPersonnelAttendanceUserDetailBykg(Integer attendanceId, Integer userId, Date begin, String source);  //不取系统默认的旷工，且没有进行下班录入的

    PersonnelAttendanceUser getPersonnelAttendanceUserByUserIdAndId(Integer id,Integer userId,Date time,String beginState);

    void defaultOverTime(PersonnelAttendanceUser personnelAttendanceUser, User attendanceUser, Integer deptId, Date start1, Integer type, Date workBegin, Date workEnd,Date breakBegin, Date breakEnd,Integer source);

    PersonnelAttendanceUser addPersonnelAttendanceUser1(Integer oid,Integer deptId,User user,String beginState,String endState,String type,Integer userId,Date openDate,Date workBegin,Date workEnd,Date breakBegin, Date breakEnd,Integer source);
    //将旷工、请假、加班等等的状态考勤，保存到员工考勤明细表中
    PersonnelAttendanceUserDetail addPersonnelAttendanceUserDetail(Date beginDate,Date endDate,String type,String source,String businessType,Integer leaveType,Integer business,String reason,PersonnelAttendanceUser p,User user,String overDuration,String overMemo,String state,Date createDate);

    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByBusiness(Integer attendanceId,String type,String source,Integer business,String state);

    void addDefaultClockRecord(PersonnelOvertime outTime, Integer type, PersonnelAttendanceUser personnelAttendanceUser, Integer detailId);  //默认打卡得

    PersonnelAttendanceUserDetail getPersonnelAttendanceUserDetail(Integer user,Date attendanceDate,String type,Integer business,String state);

    void overPunchTurn(Integer overUserId, Date punchTime, Byte punchType, Byte detailPunchType, Integer detailId, Integer overtimeId,Byte type);

    PersonnelAttendanceUserDetailPunch getUserDetailPunch(Integer org,Integer detailId, Byte detailPunchType,Date punchTime, String createName);  //获取员工考勤明细打卡对照信息

    PersonnelAttendanceException getPersonnelAttendanceExceptionByExceptionDate(Integer oid, Date exceptionDate);

    void initializeWorkAttendanceApply(int org);  //初始化考勤修改审批设置

    Map<String,Object> getAttendanceMonthOrDay(Integer ttType,String type,Date beginDay,Integer userId);

    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByType(Integer attendanceId,Date begin,String source);

    List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigByOpenDate(Integer oid, Integer type, Date openDate);

    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByMonth(Integer userId,Integer attendanceId,Date begin,Date end,String type);

    void addPersonnelAttendanceIotTerminal(IotTerminal iotTerminal, IotTerminal iotTerminalOld, AuthInfoDto authInfo,Byte operation);

    PersonnelAttendanceIotTerminal getIotTerminalHistoryLast(Integer iotTerminal);



}