package cn.sphd.miners.modules.generalAffairs.entity;

/**
 * @ClassName UserLock
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/4 9:30
 * @Version 1.0
 */
public class UserLock extends  SysUserLock {
    private String substituteName;//代理人名字
    private String substituteMobile;//代理人手机号
    private String submitState;//是否已提交（user表中字段）
    private String gender;//性别
    private String postName;//职位
    private String departName;//部门

    public String getSubmitState() {
        return submitState;
    }

    public void setSubmitState(String submitState) {
        this.submitState = submitState;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getSubstituteMobile() {
        return substituteMobile;
    }

    public void setSubstituteMobile(String substituteMobile) {
        this.substituteMobile = substituteMobile;
    }

    public String getSubstituteName() {
        return substituteName;
    }

    public void setSubstituteName(String substituteName) {
        this.substituteName = substituteName;
    }
}
