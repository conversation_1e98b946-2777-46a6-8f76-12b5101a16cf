package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.commodity.entity.PdFormula;
import cn.sphd.miners.modules.commodity.entity.PdFormulaHistory;
import cn.sphd.miners.modules.commodity.service.FormulaService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


@Controller
@RequestMapping("/formula")
public class FormulaController {
    @Autowired
    private MaterielService materielService;

    @Autowired
    private FormulaService formulaService;

    @Autowired
    private MtService mtService;

    @Autowired
    private ApprovalProcessService approvalProcessService;

    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Autowired
    SWMessageService swMessageService;
    /**
     * 配方页面跳转
     */
    @RequestMapping("/formula.do")
    public String formula(){

        return "/formulaManage/formula" ;
    }


    /**
     * 材料页面跳转
     */
    @RequestMapping("/materialsInFormula.do")
    public String materialsInFormula(){

        return "/formulaManage/materialsInFormula" ;
    }


    /**
     * 获取首页数据
     */
    @ResponseBody
    @RequestMapping("/getHomeData.do")
    public Map<String,Object> getHomeData(User user, String keyword, Integer currPage, Integer pageSize) {
        Integer oid=user.getOid();

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        //配方列表
        Map<String,Object> map=formulaService.getFormulaList(oid,"1",keyword, currPage,  pageSize);

        //待处理产品
        Map<String,Object> r=formulaService.getPdFoBaseList(oid,1,1);

        if("200".equals(r.get("code"))){
            map.put("pdNum",r.get("totalRows"));
        }else{
            map.put("pdNum",0);
        }


        //已停止
        Map<String,Object> stop=formulaService.getFormulaList(oid,"0",null, 1,  1);
        if("200".equals(stop.get("code"))){
            map.put("stopNum",stop.get("totalRows"));
        }else{
            map.put("stopNum",0);
        }

        map.put("currPage",currPage);
        return map;
    }



    /**
     *录入配方
     * @return
     */
    @RequestMapping("/addOrUpdateFormula.do")
    @ResponseBody
    public Map<String,Object> addCompositionMaterial(User user, String formula){
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid=user.getOid();
        



        String res="";
        try {
            res=formulaService.addOrUpdateFormula(oid,user,formula);
        }catch (RuntimeException e){
            e.getStackTrace();
            res=e.getMessage();
        }



        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }



        return map ;
    }


    /**
     * 获取待选择产品零件列表
     */
    @RequestMapping("/getPdFoBaseList.do")
    @ResponseBody
    public Map<String,Object> getPdFoBaseList(User user,Integer currPage, Integer pageSize){

        Integer oid=user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }
        Map<String,Object> r=formulaService.getPdFoBaseList(oid,currPage,pageSize);

        String code= (String) r.get("code");
        if ("400".equals(code)){
            r.put("totalRows",0);
        }
        return r;
    }

    /**
     * 获取待选择产品列表(无分页)
     */
    @RequestMapping("/getPdFoBaseListNoPage.do")
    @ResponseBody
    public Map<String,Object> getPdFoBaseListNoPage(User user){

        Integer oid=user.getOid();

        List<Map<String,Object>> list=formulaService.getPdFoBaseList(oid);

        return returnAllListMap(list);
    }

    private Map<String,Object> returnAllListMap(List list){
        Map<String,Object> map=new HashMap<>();
        map.put("data",list);
        map.put("totalRows",list.size());
        map.put("code",200);


        return map;
    }

    /**
     * 获取配方列表
     */
    @RequestMapping("/getFormulaList.do")
    @ResponseBody
    public Map<String,Object> getFormulaList(User user,Integer currPage, Integer pageSize){

        Integer oid=user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=formulaService.getFormulaList(oid,"1",null, currPage,  pageSize);

        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        map.put("currPage",currPage);
        return map;
    }


    /**
     * 获取配方列表，无分页
     */
    @RequestMapping("/getFormulaListNoPage.do")
    @ResponseBody
    public Map<String,Object> getFormulaListNoPage(User user){

        Map<String,Object> map=new HashMap<>();
        Integer oid=user.getOid();

        List<Map<String,Object>> list=formulaService.getFormulaList(oid,"1");

        map.put("list",list);
        map.put("code",200);
        return map;
    }

    /**
     * 根据配方获取材料信息
     */
    @RequestMapping("/getFormulaDetails.do")
    @ResponseBody
    public Map<String,Object> getFormulaDetails(Integer id){

        Map<String,Object> map=new HashMap<>();

        PdFormula data=formulaService.getPdFormula(id);
        map.put("data",data);
        map.put("code",200);
        return map;
    }

    /**
     * 配方与产品绑定   产品多  配方一
     */
    @RequestMapping("/bindOperation.do")
    @ResponseBody
    public Map<String,Object> bindOperation(User user,String baseList,Integer formula){
        Map<String,Object> map=new HashMap<>();
        
        String res=formulaService.bindOperation(baseList,formula,user);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }


        return map ;
    }


    /**
     * id 配方id
     *  配方删除
     */
    @RequestMapping("/deleteFormula.do")
    @ResponseBody
    public Map<String,Object> deleteMt(User user,Integer id){
        Map<String,Object> map=new HashMap<>();
        Integer oid=user.getOid();
        String res=formulaService.deleteFormula(id,oid);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }

        return map ;
    }


    /**
     * id 配方id
     *  配方停用/启动
     */
    @RequestMapping("/startStopFormula.do")
    @ResponseBody
    public Map<String,Object> startStopMt(User user,Integer id,String state){
        Map<String,Object> map=new HashMap<>();
        
        String res=formulaService.startStopFormula(id,state,user);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }

        return map ;
    }




    /**
     * 获取已停用配方列表
     */
    @RequestMapping("/getStopFormulaList.do")
    @ResponseBody
    public Map<String,Object> getStopFormulaList(User user,Integer currPage, Integer pageSize){

        Integer oid=user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=formulaService.getStopFormulaList(oid, currPage,  pageSize);

        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        map.put("currPage",currPage);
        return map;

    }


    /**
     * id 配方id
     * 根据配方获取已绑产品列表
     */
    @RequestMapping("/getBindingPdList.do")
    @ResponseBody
    public Map<String,Object> getBindingPdList(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=formulaService.getBindingPdList(id, currPage,  pageSize);

        Map<String,Object> before=formulaService.getBeforeBindingPdList(id, 1,  1);

        if("400".equals(before.get("code"))){
            map.put("beforeNum",0);
        }else{
            map.put("beforeNum",before.get("totalRows"));
        }
        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        map.put("currPage",currPage);
        return map;
    }


    /**
     * id 配方id
     * 根据已停用配方获取曾经绑定的产品
     */
    @RequestMapping("/getBeforeBindingPdList.do")
    @ResponseBody
    public Map<String,Object> getBeforeBindingPdList(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=formulaService.getBeforeBindingPdList(id, currPage,  pageSize);

        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        map.put("currPage",currPage);
        return map;
    }


    /**
     * id 材料id
     * 根据产品获取材料更换记录
     */
    @RequestMapping("/getFormulaListByPdBase.do")
    @ResponseBody
    public Map<String,Object> getFormulaListByPdBase(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=formulaService.getFormulaListByPdBase(id, currPage,  pageSize);
        map.put("currPage",currPage);
        return map;
    }


    /**
     * 获取材料列表
     */
    @RequestMapping("/getMtListForFormula.do")
    @ResponseBody
    public Map<String,Object> getMtListForFormula(User user,String keyword,Integer currPage, Integer pageSize){

        Integer oid=user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        List<Integer> codes=new ArrayList<>();
        for (MtCategory m:mtCategories) {
            if("构成商品的原辅材料".equals(m.getName())){

                List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                for (MtCategory m2:l) {
                    codes.add(m2.getId());
                }

            }
        }
        Map<String,Object> map=mtService.getMtListForFormula("1",keyword,codes, currPage,  pageSize);

        //已停止
        Map<String,Object> stop=mtService.getStopMtBaseListForFormula(codes,null, 1,  1);
        if("200".equals(stop.get("code"))){
            map.put("stopNum",stop.get("totalRows"));
        }else{
            map.put("stopNum",0);
        }

        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        return map;
    }

    /**
     * 获取已停用材料列表
     */
    @RequestMapping("/getStopMtBaseListForFormula.do")
    @ResponseBody
    public Map<String,Object> getStopMtBaseListForFormula(User user,Integer currPage, Integer pageSize,String keyword){

        Integer oid=user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        List<Integer> codes=new ArrayList<>();
        for (MtCategory m:mtCategories) {
            if("构成商品的原辅材料".equals(m.getName())){

                List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                for (MtCategory m2:l) {
                    codes.add(m2.getId());
                }

            }
        }
        Map<String,Object> map=mtService.getStopMtBaseListForFormula(codes,keyword, currPage,  pageSize);

        return map;
    }


    /**
     * id 材料id
     * 根据材料获取已绑产配方列表
     */
    @RequestMapping("/getBindingFormulaList.do")
    @ResponseBody
    public Map<String,Object> getBindingFormulaList(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=formulaService.getBindingFormulaList(id, currPage,  pageSize);

        Map<String,Object> before=formulaService.getBeforeBindingFormulaList(id, 1,  1);

        if("400".equals(before.get("code"))){
            map.put("beforeNum",0);
        }else{
            map.put("beforeNum",before.get("totalRows"));
        }
        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        return map;
    }



    /**
     * id 材料id
     * 根据已停用材料获取曾经绑定的配方
     */
    @RequestMapping("/getBeforeBindingFormulaList.do")
    @ResponseBody
    public Map<String,Object> getBeforeBindingFormulaList(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=formulaService.getBeforeBindingFormulaList(id, currPage,  pageSize);

        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        return map;
    }


    /**
     * 获取尚未选择的材料
     */
    @RequestMapping("/selectMtBaseList.do")
    @ResponseBody
    public Map<String,Object> selectMtBaseList(User user){

        Map<String,Object> map=new HashMap<>();
        Integer oid=user.getOid();

        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        List<Integer> codes=new ArrayList<>();
        for (MtCategory m:mtCategories) {
            if("构成商品的原辅材料".equals(m.getName())){

                List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                for (MtCategory m2:l) {
                    codes.add(m2.getId());
                }

            }
        }
        List list=mtService.getMtList("1",codes);



        map.put("list",list);
        map.put("totalRows",0);
        map.put("code",200);

        return map;
    }


    /**
     * 配方审批
     */
    @RequestMapping("/formulaApproval.do")
    @MessageMapping("/formulaApproval")
    @ResponseBody
    public JsonResult formulaApproval(String json){
        JSONObject jsonObject = JSON.parseObject(json);
        Integer approvalId=jsonObject.getInteger("approvalId"); //机构id
        String approveStatus=jsonObject.getString("approveStatus"); //机构id
        String reason=jsonObject.getString("reason"); //机构id
        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Map<String,Object> map=new HashMap<>();

        ApprovalProcess approvalProcess= approvalProcessService.getApprovalProcessById(approvalId);

        if (approvalProcess==null){
            map.put("code",400);
            map.put("msg","信息错误");
        }
        if(!"1".equals(approvalProcess.getApproveStatus())){
            map.put("code",400);
            map.put("msg","该条记录已被审批");
            return new JsonResult(1,map);
        }
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setReason(reason);


        String res="";
        try {
            res=formulaService.formulaApproval(approvalProcess,sessionid);
        }catch (RuntimeException e){
            res=e.getMessage();
        }

        if("success".equals(res)){
            map.put("status",1);
        }else{

            map.put("status",1);
            map.put("msg",res);
        }


//        //审批人
//        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/formulaEditApprovalList",null,null,null,null, JSON.toJSONString(map));
//
//        //申请人
//        clusterMessageSendingOperations.convertAndSendToUser(approvalProcess.getFromUser()+"","/formulaEditApplyList",null,null,null,null, JSON.toJSONString(map));
//
        System.out.println("执行完毕");
        return new JsonResult(1,map);
    }

    /**
     * 获取配方修改申请列表
     */
    @RequestMapping("/formulaEditApplyList.do")
    @MessageMapping("/formulaEditApplyList")
    @ResponseBody
    public JsonResult formulaEditApplyList(String json){
        JSONObject jsonObject = JSON.parseObject(json);
        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Integer userId=jsonObject.getInteger("userId"); //机构id

        List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByFromUser(userId,"1",30,null,null,"desc");

        Map<String,Object> map = new HashMap<>();
        map.put("list",list);
        map.put("num",list.size());
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/formulaEditApplyList",null,null,null,null, JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     * 获取配方修改审批列表
     */
    @RequestMapping("/formulaEditApprovalList.do")
    @MessageMapping("/formulaEditApprovalList")
    @ResponseBody
    public JsonResult formulaEditApprovalList(String json){

        JSONObject jsonObject = JSON.parseObject(json);
        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Integer userId=jsonObject.getInteger("userId"); //机构id
        List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(userId,"1",30,null,null,null,"desc");
        Map<String,Object> map=new HashMap<>();
        map.put("list",list);
        map.put("num",list.size());
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/formulaEditApprovalList",null,null,null,null, JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     * 配方修改详情查看
     * id审批id
     */
    @RequestMapping("/formulaEditDetails.do")
    @ResponseBody
    public Map<String,Object> formulaEditDetails(Integer approvalId){
        Map<String,Object> map=new HashMap<>();
        ApprovalProcess approvalProcess= approvalProcessService.getApprovalProcessById(approvalId);



        PdFormulaHistory after=formulaService.getPdFormulaHistoryById(approvalProcess.getBusiness());

        //如果还未审批，查询当前最新的
        if("1".equals(approvalProcess.getApproveStatus())){
            PdFormula    pdFormula=formulaService.getByInstance(approvalId);
            map.put("before",pdFormula);
        }else{
            if(after.getPreviousId()==null||"0".equals(after.getPreviousId())){

                PdFormula    pdFormula=formulaService.getLikeByApproval(approvalId);
                map.put("before",pdFormula);
            }else{
                PdFormulaHistory before=formulaService.getPdFormulaHistoryById(after.getPreviousId());
                map.put("before",before);
            }
        }




        map.put("after",after);

        map.put("approval",approvalProcess);
        map.put("code",200);
        return map ;
    }

    /**
     * 配方审批查询
     */
    @RequestMapping("/selectFormulaEditList.do")
    @ResponseBody
    public Map<String,Object> selectFormulaEditList(User user, HttpServletRequest request) throws ParseException {


        
        Map<String,Object> map = new HashMap<>();
        String date=request.getParameter("date");
        String state=request.getParameter("state");
        String startTime=request.getParameter("startTime");
        String endTime=request.getParameter("endTime");
        String type=request.getParameter("type");
        Date st=null;
        Date ed=null;
        SimpleDateFormat sim1=new SimpleDateFormat("yyyy-MM-dd");

        SimpleDateFormat sim2=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleDateFormat sim3=new SimpleDateFormat("yyyy-MM");
        //7天内
        if("1".equals(date)){
            String nowDate=sim1.format(new Date());

            st=new Date((sim1.parse(nowDate).getTime()-3600*24*7*1000));

            ed=new Date(sim2.parse(nowDate+" 23:59:59").getTime());
            //当月
        }else if("2".equals(date)){
            //获取当前月第一天：
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
            String first = sim1.format(c.getTime());
            System.out.println("===============first:"+first);

            //获取当前月最后一天
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            String last = sim1.format(ca.getTime());
            System.out.println("===============last:"+last);

            st=new Date(sim2.parse(first+" 00:00:00").getTime());

            ed=new Date(sim2.parse(last+" 23:59:59").getTime());
        }else{

            st=new Date(sim2.parse(startTime+" 00:00:00").getTime());

            ed=new Date(sim2.parse(endTime+" 23:59:59").getTime());
        }

        //申请人
        if("1".equals(type)){

            List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByFromUser(user.getUserID(),state,30,st,ed,"desc");
            map.put("list",list);
        }else{
            List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),state,30,st,ed,null,"desc");
            map.put("list",list);
        }

        map.put("startTime",st);
        map.put("endTime",ed);
        map.put("code",200);
        return map ;
    }


}
