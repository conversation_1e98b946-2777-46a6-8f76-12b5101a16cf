<template>
  <div class="assignTasks">
    <Pageheader title="任务创建" :showBack="true"/>
    <div class="live-container">
      <div class="txtCenter">
        <div class="ttl" v-if="playFrom ==='newLyric'">{{ playName }}</div>
        <div class="ttl" v-if="playFrom ==='newLyricXiQu'">{{ playName }} / {{ playXuanduan }} / {{ playLiuPai }}</div>
        <div>{{ playTime }}</div>
      </div>
      <el-form label-position="top" label-width="280px" :rules="rules" :model="formData">
        <div class="form-item">
          <div class="flexc">
            <div>任务的接收对象</div>
            <div>已选定{{ staffNum }}人</div>
            <div>
              <el-link  type="primary" @click="goSelectUser">选择</el-link>
            </div>
          </div>
          <div class="flexc">
            <div>原唱</div>
            <div>{{ oralShow ? '已选择' : '未选择' }}</div>
            <div v-if="oralShow">
              <el-link  @click="playMusic" type="primary">播放</el-link>
            </div>

          </div>
          <div class="blueTip"> 注 原唱选择后，为歌词配节奏时更便捷！</div>
        </div>
        <el-form-item label="说明">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows:5}" v-model="formData.inllustration" placeholder="如必要，可录入关于歌曲原唱的版本等各方面信息"></el-input>
          <div class="numDiv txtRight" >{{ formData.inllustration.length  }}/20</div>
        </el-form-item>
        <el-form-item label="第一句">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows:5}" v-model="formData.firstSentence" placeholder="如必要，可录入关于歌曲原唱的版本等各方面信息"></el-input>
          <div class="numDiv txtRight">{{ formData.firstSentence.length  }}/20</div>
        </el-form-item>

        <el-form-item label="最后一句">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows:5}" v-model="formData.lastSentence" placeholder="如必要，可录入关于歌曲原唱的版本等各方面信息"></el-input>
          <div class="numDiv txtRight" >{{ formData.lastSentence.length  }}/20</div>
        </el-form-item>

      </el-form>
      <div class="form-item">
        <span @click="publishTask" class="primary live-button">任务发布</span>
      </div>
    </div>

    <AudioPlayer v-if="playLyc" :playName="playName" :fileUrl="fileUrl" @closePlayLyc="closePlayLyc"></AudioPlayer>

  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import { insertSongs } from "@/api/music.js";
import JSONBig from 'json-bigint'



const router = useRouter();
const playFrom = ref('')
const playName = ref('')
const playXuanduan = ref('')
const playLiuPai = ref('')
const playLyc = ref(false)
const playTime = ref('')
const fileUrl = ref('')
const oralShow = ref('')
const staffNum = ref(0)
const formData = reactive({
  inllustration:'',
  firstSentence:'',
  lastSentence:'',
})

onMounted(() => {
  let newlycdata = JSONBig.parse(localStorage.getItem('newlycdata'));
  let uploadUrl = localStorage.getItem('uploadUrl');
  console.log('newlycdata ===', newlycdata)
  playFrom.value = newlycdata.from
  playName.value = newlycdata.name
  playXuanduan.value = newlycdata.nameXuanDuan
  playLiuPai.value = newlycdata.nameLiu

  let banTime = newlycdata.banTime
  playTime.value = banTime.min + '分' + banTime.ss + '秒'
  let staffList = JSONBig.parse(localStorage.getItem('staffList') || '[]')
  staffNum.value = staffList.length
  oralShow.value =  newlycdata.orlLyc && newlycdata.orlLyc.filePath
  if(oralShow.value){
    fileUrl.value = uploadUrl + newlycdata.orlLyc.filePath
  }
});
const closePlayLyc = (val)=> {
  playLyc.value = val
}
// creator：hxz 播放音乐
const playMusic = ()=> {
  playLyc.value = true
}
// creator：hxz 选择用户
const goSelectUser = ()=> {
  let formData2 = JSONBig.stringify(formData.value)
  localStorage.setItem('assignTasks', formData2)
  router.push({
    name: 'selectUser',
  });
}
const publishTask = ()=> {
  let formData2 = localStorage.getItem('newlycdata')
  formData2 = JSONBig.parse(formData2)
  console.log('formData = ：', formData2)
  let lycTxtObj = localStorage.getItem('lycTxtObj')
  lycTxtObj = JSONBig.parse(lycTxtObj)

  console.log('formData2', formData2)
  let type = formData2.from === 'newLyricXiQu' ? 2 : 1 ;
  let data = {
    'name':formData2.name, // 歌曲名称
    'type': type, // 类型:1-歌曲,2-戏曲 戏曲的先不做
    'captionRequired':formData2.addWord, // 是否需要加字幕 0-否 1-是 传0的时候5以下就都不用传了
  }
  if(data.type === 2){
    data.selections = formData2.nameXuanDuan
    data.genres = formData2.nameLiu
  }
  if(formData2.id){
    data.id = formData2.id
  }

  if(formData2.addWord === '1'){
    let songDuration = formData2.banTime.min + '分' + formData2.banTime.ss + '秒'
    data.songDuration = songDuration; // 伴奏时长
    data.trackDuration = (formData2.orlLyc.duration || 0 ) * 1000   ; // 歌曲时长 上个接口返回的 duration 字段
    data.trackDuration = parseInt(data.trackDuration)
    data.bgmDuration = (formData2.banLyc.duration || 0) * 1000 ; // 伴奏时长 上个接口返回的duration字段
    data.bgmDuration = parseInt(data.bgmDuration)
    data.publishType = formData2.uploadType ; // 发布方式:1-自行上传,2-发布任务,团队成员上传
    // data.rhythmOption = lycTxtObj.rhythmOption ; // 节奏选项:1-原唱,2-伴奏  这个值可能有点问题我换了新东西 看看能否接收到
    data.originalPath = formData2.orlLyc.filePath ; // 原唱存储路径 上个接口返回的filePath字段
    data.bgmPath = formData2.banLyc.filePath ; // 伴唱存储路径 上个接口返回的filePath字段
    if(data.publishType === '2'){
      data.inllustration = formData.inllustration ; // 说明 publishType=2时非空就传
      data.firstSentence = formData.firstSentence ; // 第一句 publishType=2时非空就传
      data.lastSentence = formData.lastSentence ; // 最后一句 publishType=2时非空就传
      let userList = localStorage.getItem('selectUser')
      let staffList = JSONBig.parse(localStorage.getItem('staffList') || '[]')
      if(staffList.length === 0){
        ElMessage.info('请先选择任务的接受对象！');
        return false
      }
      data.listTaskAssign = [] ; // 一个user的接送字符串 应该是这样 [{"user":"1"}，{"user":"2"}] captionRequired=1且publishType=2才传这个值
      staffList.forEach( u => {
        data.listTaskAssign.push(u.id )
      })
      data.listTaskAssign =  data.listTaskAssign.toString()
    }
    data.versionNo = 0 ; //  版本号 传0
    // let patData = JSONBig.parse(localStorage.getItem('patData'))
    // data.rhythm = `` ; // 节奏歌词文本(含时间标签)
    // data.lyrics = patData.txtPat  ; // 歌词文本 从剪贴板获取的歌词文本
    data.isSelected = 1  ; // 是否被选择 1-被选择 0-未选择 这里固定传1
    // patData.lyricArr.forEach(itemLyc => {
    //   data.rhythm += `[${ itemLyc.time }]${ itemLyc.txt }\n`
    // })
  }

  // approveStatus:  captionRequired=0时传2；captionRequired=2且publishType=1时传2；captionRequired=2且publishType=2时传1
  if(data.captionRequired === '0' || (data.captionRequired === '1' && data.publishType === '1')){
    data.approveStatus = 2
  }
  if(data.captionRequired === '1' && data.publishType === '2'){
    data.approveStatus = 1
  }
  console.log('insertSongs data=', data)
  insertSongs(data).then(res =>{
    console.log('insertSongs', res)
    let success = res.success
    if(success === 1){
      ElMessage.success('新增成功！');
      localStorage.removeItem('newlycdata')
      localStorage.removeItem('patData')
      localStorage.removeItem('agreeObj')
      localStorage.removeItem('lycTxtObj')
      localStorage.removeItem('assignTasks')
      localStorage.removeItem('staffList')
      if(playFrom ==='newLyric'){
        router.push("lyricTask")
      }else{
        router.push("lyricTaskXiQu")
      }
    }else{
      ElMessage.error('新增失败！');

    }
  })

}
</script>
<style lang="scss" scoped>
.assignTasks{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
  }
  .numDiv{
    display: block;
    width: 100%;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .txtRight{
    text-align: right;
  }

  .blueTip{
    color: #0b94ea;
    font-size: 0.8em;
    line-height: 25px;
  }
  .flexc{
    display: flex;
    line-height: 30px;
    >div{
      flex: 1;
      text-align: center;
    }
    >div:nth-child(1){
      text-align: left;
    }
  }
  .form-item{
    margin-bottom: 10px;
    font-size:0.9em ;
    color: #606266;
  }

}
</style>
