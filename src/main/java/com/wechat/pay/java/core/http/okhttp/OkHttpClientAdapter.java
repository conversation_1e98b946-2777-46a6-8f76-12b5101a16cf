//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.wechat.pay.java.core.http.okhttp;

import cn.sphd.miners.common.initializer.SaveRpcLog;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.rpcLog.entity.RpcLog;
import cn.sphd.miners.modules.rpcLog.service.RpcLogService;
import com.wechat.pay.java.core.auth.Credential;
import com.wechat.pay.java.core.auth.Validator;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.http.*;
import com.wechat.pay.java.core.util.GsonUtil;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.*;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
@Service
public final class OkHttpClientAdapter extends AbstractHttpClient {
    private static final Logger logger = LoggerFactory.getLogger(OkHttpClientAdapter.class);
    private static final String META_NAME = "meta";
    private static final String FILE_NAME = "file";
    private final OkHttpClient okHttpClient;
    @Autowired
    public RpcLogService rpcLogService;

    public OkHttpClientAdapter(Credential credential, Validator validator, OkHttpClient client) {
        super(credential, validator);
        this.okHttpClient = (OkHttpClient)Objects.requireNonNull(client);
    }

    protected String getHttpClientInfo() {
        return "okhttp3/" + this.okHttpClient.getClass().getPackage().getImplementationVersion();
    }

//    @DsEcSku(serviceName = "wxResService")
    public OriginalResponse innerExecute(HttpRequest wechatPayRequest) {
        ArrayList<String> error = new ArrayList<>();
        String response = null;
        Request okHttpRequest = this.buildOkHttpRequest(wechatPayRequest);

        try {
            Response okHttpResponse = this.okHttpClient.newCall(okHttpRequest).execute();
            Throwable var4 = null;

            OriginalResponse var5;
            try {
                var5 = this.assembleOriginalResponse(wechatPayRequest, okHttpResponse);
            } catch (Throwable var15) {
                var4 = var15;
                throw var15;
            } finally {
                if (okHttpResponse != null) {
                    if (var4 != null) {
                        try {
                            okHttpResponse.close();
                        } catch (Throwable var14) {
                            var4.addSuppressed(var14);
                            error.add(GsonUtil.toJson(var14));
                        }
                    } else {
                        okHttpResponse.close();
                    }
                }

            }
            response = GsonUtil.toJson(var5);
            return var5;
        } catch (IOException var17) {
            error.add(GsonUtil.toJson(var17));
            throw new HttpException(wechatPayRequest, var17);
        } finally {
            RpcLogService rpcLogService = RpcLogService.getRpcLogService();
            if(rpcLogService!=null) {
                rpcLogService.saveRpcLog(new RpcLog(
                        SaveRpcLog.Direction.send.getIndex(),
                        getCaller(),
                        getCallee(),
                        wechatPayRequest.getUri().getHost(),
                        GetLocalIPUtils.getServerIp(),
                        wechatPayRequest.getUri().toString(),
                        GsonUtil.toJson(wechatPayRequest),
                        response,
                        error.stream().collect(Collectors.joining("\n"))));
            } else {
                WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
                ServletContext sc = context==null ? null : context.getServletContext();
                ApplicationContext ac = sc==null ? null : WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
                RpcLogService result = ac==null ? null : ac.getBean(RpcLogService.class, "rpcLogService");
                if(result==null) {
                    org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(RpcLogService.class);
                    logger.error("OkHttpClientAdapter 1 获取 rpcLogService 错误，" + (context==null ? " context is null " : "") + (sc==null ? " sc is null " : "") + (ac==null ? " ac is null " : "") + (result==null ? " rpcLogService is null " : ""));
                    Pair<String, String> caller = getCaller();
                    if(caller!=null && caller.getLeft()!=null && caller.getRight()!=null) {
                        logger.error("调用者： "+caller.getLeft()+" . "+caller.getRight());
                    }
                }
                logger.error("OkHttpClientAdapter 1.1 Autowired" + this.rpcLogService);
            }
        }
    }

    private Request buildOkHttpRequest(HttpRequest wechatPayRequest) {
        Request.Builder okHttpRequestBuilder = (new Request.Builder()).url(wechatPayRequest.getUrl());
        Map<String, String> headers = wechatPayRequest.getHeaders().getHeaders();
        headers.forEach(okHttpRequestBuilder::addHeader);
        String method = wechatPayRequest.getHttpMethod().name();
        RequestBody okHttpRequestBody = this.buildOkHttpRequestBody(wechatPayRequest.getBody());
        okHttpRequestBuilder.method(method, okHttpRequestBody);
        return okHttpRequestBuilder.build();
    }

    private RequestBody buildOkHttpRequestBody(com.wechat.pay.java.core.http.RequestBody wechatPayRequestBody) {
        if (wechatPayRequestBody == null) {
            return null;
        } else if (wechatPayRequestBody instanceof JsonRequestBody) {
            return this.createOkHttpRequestBody(wechatPayRequestBody);
        } else if (wechatPayRequestBody instanceof FileRequestBody) {
            return this.createOkHttpMultipartRequestBody(wechatPayRequestBody);
        } else {
            logger.error("When an http request is sent and the okhttp request body is constructed, the requestBody parameter type cannot be found,requestBody class name[{}]", wechatPayRequestBody.getClass().getName());
            return null;
        }
    }

    private RequestBody createRequestBody(String content, MediaType mediaType) {
        return RequestBody.create(mediaType, content);
    }

    private RequestBody createRequestBody(byte[] content, MediaType mediaType) {
        return RequestBody.create(mediaType, content);
    }

    private RequestBody createOkHttpRequestBody(com.wechat.pay.java.core.http.RequestBody wechatPayRequestBody) {
        return this.createRequestBody(((JsonRequestBody)wechatPayRequestBody).getBody(), MediaType.parse(wechatPayRequestBody.getContentType()));
    }

    private RequestBody createOkHttpMultipartRequestBody(com.wechat.pay.java.core.http.RequestBody wechatPayRequestBody) {
        FileRequestBody fileRequestBody = (FileRequestBody)wechatPayRequestBody;
        RequestBody okHttpFileBody = this.createRequestBody(fileRequestBody.getFile(), MediaType.parse(fileRequestBody.getContentType()));
        return (new MultipartBody.Builder()).setType(MultipartBody.FORM).addFormDataPart("meta", fileRequestBody.getMeta()).addFormDataPart("file", fileRequestBody.getFileName(), okHttpFileBody).build();
    }

    private Map<String, String> assembleResponseHeader(Response okHttpResponse) {
        Map<String, String> responseHeaders = new ConcurrentHashMap();
        int headerSize = okHttpResponse.headers().size();

        for(int i = 0; i < headerSize; ++i) {
            responseHeaders.put(okHttpResponse.headers().name(i), okHttpResponse.headers().value(i));
        }

        return responseHeaders;
    }

    private OriginalResponse assembleOriginalResponse(HttpRequest wechatPayRequest, Response okHttpResponse) {
        Map<String, String> responseHeaders = this.assembleResponseHeader(okHttpResponse);

        try {
            return (new OriginalResponse.Builder()).request(wechatPayRequest).headers(responseHeaders).statusCode(okHttpResponse.code()).contentType(okHttpResponse.body() != null && okHttpResponse.body().contentType() != null ? okHttpResponse.body().contentType().toString() : null).body(okHttpResponse.body().string()).build();
        } catch (IOException var5) {
            throw new MalformedMessageException(String.format("Assemble OriginalResponse,get responseBody failed.%nHttpRequest[%s]", wechatPayRequest));
        }
    }

    protected InputStream innerDownload(HttpRequest httpRequest) {
        System.out.println("wonderss OkHttpClientAdapter innerDownload in");
        String response = null;
        ArrayList<String> error = new ArrayList<>();
        Request okHttpRequest = this.buildOkHttpRequest(httpRequest);

        try {
            Response okHttpResponse = this.okHttpClient.newCall(okHttpRequest).execute();
            if (this.isInvalidHttpCode(okHttpResponse.code())) {
                throw new ServiceException(httpRequest, okHttpResponse.code(), "");
            } else {
                InputStream responseBodyStream = null;
                if (okHttpResponse.body() != null) {
                    responseBodyStream = okHttpResponse.body().byteStream();
                }
                System.out.println("wonderss OkHttpClientAdapter innerDownload return OK" + GsonUtil.toJson(okHttpResponse));
                response = GsonUtil.toJson(okHttpResponse);
                return responseBodyStream;
            }
        } catch (IOException var5) {
            error.add(GsonUtil.toJson(var5));
            throw new HttpException(httpRequest, var5);
        } finally {
            RpcLogService rpcLogService = RpcLogService.getRpcLogService();
            if(rpcLogService!=null) {
                rpcLogService.saveRpcLog(new RpcLog(
                        SaveRpcLog.Direction.send.getIndex(),
                        getCaller(),
                        getCallee(),
                        httpRequest.getUri().getHost(),
                        GetLocalIPUtils.getServerIp(),
                        httpRequest.getUri().toString(),
                        GsonUtil.toJson(okHttpRequest),
                        response,
                        error.stream().collect(Collectors.joining("\n"))));
            } else {
                WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
                ServletContext sc = context==null ? null : context.getServletContext();
                ApplicationContext ac = sc==null ? null : WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
                RpcLogService result = ac==null ? null : ac.getBean(RpcLogService.class, "rpcLogService");
                if(result==null) {
                    org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(RpcLogService.class);
                    logger.error("OkHttpClientAdapter 2 获取 rpcLogService 错误，" + (context==null ? " context is null " : "") + (sc==null ? " sc is null " : "") + (ac==null ? " ac is null " : "") + (result==null ? " rpcLogService is null " : ""));
                    Pair<String, String> caller = getCaller();
                    if(caller!=null && caller.getLeft()!=null && caller.getRight()!=null) {
                        logger.error("调用者： "+caller.getLeft()+" . "+caller.getRight());
                    }
                }
                logger.error("OkHttpClientAdapter 2.1 Autowired" + this.rpcLogService);
            }
        }
    }

    private Pair<String, String> getCallee() {
        StackTraceElement[] stacks = Arrays.stream(Thread.currentThread().getStackTrace()).skip(2).toArray(StackTraceElement[]::new);//跳过 Thread、getCallee
        String packageName = getClass().getPackage().getName().substring(0, getClass().getPackage().getName().lastIndexOf('.'));//getClass().getPackage().getName();
        for(StackTraceElement stack : stacks) {
            if(!stack.getClassName().contains(packageName)) {
                return Pair.of(stack.getClassName(), stack.getMethodName());
            }
        }
        return Pair.of(null, null);
    }

    private Pair<String, String> getCaller() {
        StackTraceElement[] stacks = Arrays.stream(Thread.currentThread().getStackTrace()).skip(3).toArray(StackTraceElement[]::new);//跳过 Thread、getCaller和OkHttpClientAdapter被调用函数
        Pair<String, String> result = null;
        String thisPackageName = "com.wechat.pay.java.core.http";
        String minersPackageName = "cn.sphd.miners";
        for(StackTraceElement stack : stacks) {
            if(result == null && !stack.getClassName().contains(thisPackageName)) {
                result = Pair.of(stack.getClassName(), stack.getMethodName());
            }
            if(stack.getClassName().contains(minersPackageName)) {
                return Pair.of(stack.getClassName(), stack.getMethodName());
            }
        }
        return result == null ? Pair.of(null, null) : result;
    }
}