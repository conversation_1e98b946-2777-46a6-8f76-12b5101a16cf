package cn.sphd.miners.modules.accountant.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.accountant.entity.SubjectChooseParams;
import cn.sphd.miners.modules.accountant.entity.TAccountantSettle;
import cn.sphd.miners.modules.accountant.entity.TaccountantSettingHistoryEntity;
import cn.sphd.miners.modules.accountant.entity.UpdateVoucherParams;
import cn.sphd.miners.modules.car.util.Result;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

/**
 * Created by root on 17-1-4.
 */
public interface SubjectSelectService {
    JSONObject queryFinanceData(int oid);

//    JSONObject queryFinanceDataByID(int id,String type);

    JsonResult insertVoucher(User user, SubjectChooseParams params);


    JSONArray queryApprove(int oid, int approve_status);

//    JSONArray queryFinanceModifyData(int oid);

    int setApprove(int id, int approve_status, int voucher_id, String cashjson, User user);

    int insertVoucherHistory(UpdateVoucherParams params);

    TAccountantSettle judgeSettled(int belong_peroid, int oid);

    int setRelation(User user,String state);

    int getRelation(User user);

}
