package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.commodity.service.FinishedService;
import cn.sphd.miners.modules.commodity.service.ProService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.inv.service.InvService;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.stock.service.PdOutApplicationService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 成品库
 */
@Controller
@RequestMapping("/finished")
public class FinishedProductCtrl {

    @Autowired
    FinishedService finishedService;

    @Autowired
    InvService invService;
    @Autowired
    MtStockService mtStockService;
    @Autowired
    PdOutApplicationService pdOutApplicationService;
    @Autowired
    ProductService productService;
    @Autowired
    ProService proService;
    @Autowired
    MaterielService materielService;

    @Autowired
    PdCustomerService pdCustomerService;

    /**
     * 获取主页数据
     */
    @ResponseBody
    @RequestMapping("/getHomeData.do")
    public Map<String,Object> getHomeData(User user){
        Integer oid= user.getOid();
        Map<String,Object> map=finishedService.totalWarehouse(oid);
        //待录入
        map.put("waitingFor",finishedService.getNotEnteredList(oid).size());
        //已设置
        int alreadySet=finishedService.getPdProductList(oid).size();
        map.put("alreadySet",alreadySet);
        //种类
        map.put("zl",alreadySet);
        //待入库
        map.put("dairuku",mtStockService.getMtStockAcceptance(oid, "1", "2").size());
        //待出库
        map.put("daichuku",pdOutApplicationService.getPdOutApplicationListByInOutState(2,oid).size());
        return map ;
    }

    /**
     * 获取待录入初始库存列表
     */
    @ResponseBody
    @RequestMapping("/getWaitingForLocation.do")
    public Map<String,Object> getWaitingForLocation(User user, Integer currPage, Integer pageSize){

        Integer oid= user.getOid();

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=finishedService.getNotEnteredList(oid,currPage,pageSize);
        map.put("currPage",currPage);

        return map;
    }


    /**
     * 录入初始库存库位
     */
    @ResponseBody
    @RequestMapping("/addInitialStock.do")
    public Map<String,Object> addInitialStock(User user, HttpServletRequest request){
        Map<String,Object> map=new HashMap<String,Object>();
        

        Integer id = Integer.valueOf(request.getParameter("id"));
        String locationList = request.getParameter("locationList");


        String res=finishedService.addInitialStock(id,locationList,user);

        map.put("code",200);

        return map;
    }

    /**
     * 获取库位列表
     */
    @ResponseBody
    @RequestMapping("/getLocationList.do")
    public Map<String,Object> getLocationList(User user){

        Integer oid= user.getOid();


        Map<String,Object> map=finishedService.getLocationList(oid);


        return map;
    }

    /**
     * 库位情况查看
     */
    @ResponseBody
    @RequestMapping("/getLocationDetail.do")
    public Map<String,Object> getLocationDetail(Integer locationId){
        Map<String,Object> map=new HashMap<String,Object>();


        Map<String,Object> data=finishedService.getLocationDetail(locationId);

        map.put("data",data);
        map.put("code",200);
        return map;
    }


    /**
     * 成品列表
     */
    @ResponseBody
    @RequestMapping("/getPdProductList.do")
    public Map<String,Object> getPdProductList(User user, Integer currPage, Integer pageSize,String type,Integer id,String param){

        Integer oid= user.getOid();

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }
        
        Map<String,Object> map=finishedService.getPdProductList(oid,type,id, param,currPage,pageSize);
        List< Map<String,Object>> mtCategories=finishedService.getMtCategory(id,user.getOid());
        map.put("currPage",currPage);
        map.put("mtCategories",mtCategories);

        //暂停销售数量
        List<Map<String, Object>> list2 = proService.suspendProductListForList(user.getOid(),null, null);
        map.put("suspendNum", list2.size());
        return map;
    }

    /**
     * 获取暂停成品列表
     */
    @ResponseBody
    @RequestMapping("/getSuspendPdProductList.do")
    public Map<String,Object> getSuspendPdProductList(User user, Integer currPage, Integer pageSize){

        Integer oid= user.getOid();

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=finishedService.getSuspendPdProductList(oid,currPage,pageSize);

        map.put("currPage",currPage);

        return map;
    }

    /**
     * 获取初始库存修改记录
     */
    @ResponseBody
    @RequestMapping("/getInitialStockRecord.do")
    public Map<String,Object> getInitialStockRecord(Integer id){
        Map<String,Object> map=new HashMap<String,Object>();

        List<Map<String,Object> > data=finishedService.getInitialStockRecord(id);

        PdMerchandise product=productService.getByPdCusId(id);

        String customerName="通用商品";
        if(product.getCustomer_()!=null){
            SlCustomer customer=pdCustomerService.getPdCustomerById(product.getCustomer_());
            if(customer!=null){
                customerName=customer.getFullName();
            }
        }
        if(product!=null){
            map.put("unit",product.getUnit());
            map.put("currentStock",product.getCurrentStock());
            map.put("initialStock",product.getInitialStock());

            for (Map<String,Object> m:data) {
                m.put("customerName",customerName);
            }
        }
        map.put("list",data);
        map.put("code",200);
        return map;
    }

    /**
     * 获取最低库存修改记录
     */
    @ResponseBody
    @RequestMapping("/getMinimumStockRecord.do")
    public Map<String,Object> getMinimumStockRecord(Integer id){
        Map<String,Object> map=new HashMap<String,Object>();

        List<Map<String,Object> > data=finishedService.getMinimumStockRecord(id);

        PdMerchandise product=productService.getByPdCusId(id);
        if(product!=null){
            map.put("unit",product.getUnit());
            map.put("currentStock",product.getCurrentStock());
            map.put("initialStock",product.getInitialStock());
        }
        for (Map<String,Object> m:data) {
            m.put("customerName",product.getCustomerName());
        }
        map.put("list",data);
        map.put("code",200);
        return map;
    }


    /**
     * 修改初始库存
     */
    @ResponseBody
    @RequestMapping("/updateInitialStock.do")
    public Map<String,Object> updateInitialStock(User user, Integer id, BigDecimal num){
        Map<String,Object> map=new HashMap<String,Object>();
        
        String res=finishedService.updateInitialStock(id,num,user);
        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }
        return map;
    }


    /**
     * 占用库位查看
     */
    @ResponseBody
    @RequestMapping("/getLocationListByProduct.do")
    public Map<String,Object> getLocationListByProduct( Integer id){
        Map<String,Object> map=new HashMap<String,Object>();
        ;
        Map<String,Object> data=finishedService.getLocationListByProduct(id);
        map.put("data",data);
        map.put("code",200);
        return map;
    }

    /**
     * 修改库位
     */
    @ResponseBody
    @RequestMapping("/updateLocation.do")
    public Map<String,Object> updateLocation(User user, Integer id, String locationList){
        Map<String,Object> map=new HashMap<String,Object>();
        
        String res=finishedService.updateLocation(id,locationList,user);

        map.put("code",200);

        return map;
    }


    /**
     * 成品库搜索
     */
    @ResponseBody
    @RequestMapping("/search.do")
    public Map<String,Object> search(User user, String param, Integer currPage, Integer pageSize){
        Integer oid= user.getOid();

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=finishedService.search(oid,param,currPage,pageSize);
        map.put("currPage",currPage);
        return map;
    }
}
