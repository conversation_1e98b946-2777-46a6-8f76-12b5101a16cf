<template>
  <div id="openInvoice2">
    <TY_NavTop :title="title" isReBack="true" @reback="isReBackFun"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="sendContainer">
          <div><span>{{info.name}}</span></div>
          <div class="ritxt">需要发票数量
            <span>{{info.invoice_count}}
              <span v-if="info.invoice_count !== '--'">张</span>
          </span></div>
          <div class="ritxt">申请开票金额 <span>{{info.invoice_amount | numberFilter(2)}} </span></div>

          <div class="flexc">
            <div>

            </div>
            <div>
              <div><span class="namec">申请人：{{info.create_name}}</span><span> {{info.create_date | formatDate}}</span></div>
              <div v-if="info.update_name"><span class="namec">审批人：{{info.update_name}}</span><span> {{info.update_date | formatDate}}</span></div>
              <div v-if="info.auditor_name"><span class="namec">开票人：{{info.auditor_name}}</span><span>{{info.audit_date | formatDate}}</span></div>
            </div>
          </div>

        </div>
      </div>
      <p class="H1"></p>
      <table class="table">
        <thead>
        <td>金额</td>
        <td>发票种类</td>
        <td>发票号码</td>
        <td>开票日期</td>
        <td>行数</td>
        <td>操作</td>
        </thead>
        <tbody>
        <tr v-for="(item, index) in list" v-bind:key="index">
          <td>{{item.amount| numberFilter(2)}}</td>
          <td>{{item.invoice_category | filterInvoiceType}}</td>
          <td>{{item.invoice_no}}</td>
          <td>{{item.operate_date | formatDate('yyyy/MM/dd')}}</td>
          <td>{{item.line}}</td>
          <td v-if="!item.check_result" class="link" @click="toInfo(item)"><span v-if="info.teminate_state !== 1">{{item.invoice_no | chargeControlBtn}}</span></td>
          <td v-if="item.check_result" class="linkGray"><span v-if="info.teminate_state !== 1">{{item.invoice_no | chargeControlBtn}}</span></td>
        </tr>
        </tbody>
      </table>
      <p class="H"></p>
      <div class="centerBtn" v-if="info.state != 5 && info.state != 7 ">
        <input type="submit" :disabled="isOk" class="ui-btn ui-btn_info" value="确定" @click="registerOk()">
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #openInvoice2{
    .ritxt{
      font-size: 0.9em;
      text-align: right;
      &>span{ width: 80px; text-align: left; display: inline-block;  }
    }
    .namec{ display: inline-block; width:96px; text-align: left; overflow: hidden;white-space: nowrap;text-overflow: ellipsis; position: relative;
      top: 5px;}
    .flexc{
      display: flex;
      .linkbtn{ color:#0a9bee; cursor: default; font-size: 15px; }
      .linkbtn:hover{ text-decoration: underline; }
      &>div:nth-child(1){ flex:1;  }
      &>div:nth-child(2){ flex:5; font-size: 0.7em; text-align: right;  }
    }
    .H1{ height:20px;  }
    .H{ height:40px;  }
    .link{ color:#37bc9b!important; cursor: default;  }
    .linkGray{ color:#aaa!important; cursor: default;  }
    .link:hover{ color:#48cfad;  }
    .wid{ display: inline-block; width: 30px; }
    .centerBtn{
      text-align: center;
      .ui-btn{
        display: inline-block;
        border-radius: 15px;
        width: 70%;
        height: auto;
        line-height:1;
        vertical-align: middle;
        padding:8px 0 ;
      }
    }
  }

</style>
<script>
import { formatDate, filterInvoiceType, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceRegisterDetail',
  data () {
    return {
      dialogVisible: false,
      loading: true,
      apIds: '',
      title: '开票待审批',
      listeners: [],
      isOk: false,
      reason: '',
      list: [],
      info: {}
    }
  },
  filters: {
    formatDate,
    numberFilter,
    filterInvoiceType,
    chargeControlBtn: function (no) {
      if (no) {
        return '修改'
      } else {
        return '选择'
      }
    }
  },
  computed: {},
  created () {
    let _this = this
    _this.applicationId = this.$route.params.id
    // 获取申请详情
    _this.getDetails()
    _this.listeners = [
      // 已批准待开票
      _this.sphdSocket.subscribe('applyInvoice', _this.subscribeCallBack, null, 'user'),
      // 已开票未登记
      _this.sphdSocket.subscribe('invoiceRegister', _this.subscribeCallBack, null, 'user'),
      // 填完发票号的回调
      _this.sphdSocket.subscribe('invoiceHandle', _this.handleCallback, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    isReBackFun:function () {
      this.$router.push({
        path: "/invoiceRegister"
      })
    },
    chargeTitle: function (sta) {
      let str = '开票待审批'
      switch (sta) {
        case 3 :
          str = '已批准待开票'
          break
        case 4 :
          str = '暂缓开票'
          break
        case 5 :
        case 7 :
          str = '已开票未登记'
          break
        default :
      }
      return str
    },
    getDetails: function () {
      let _this = this
      _this.$http.post('../../../sale/getApplicationItemListByApplicationId.do', {
        'applicationId': _this.applicationId
      }, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        _this.list = res['data']
        let aps = ''
        if (_this.list && _this.list.length > 0) {
          for (let i = 0; i < _this.list.length; i++) {
            aps += _this.list[i]['id'] + ','
            if (_this.list[i]['invoice_no']) {
              _this.list[i]['operate_date'] = formatDate(_this.list[i]['operate_date'], 'yyyy-MM-dd')
            } else {
              _this.isOk = true
              _this.list[i]['operate_date'] = ''
            }
          }
          _this.apIds = aps.substr(0, aps.length - 1)
        }
        _this.info = res.base[0]
        _this.info['invoice_amount'] = 0
        _this.list.forEach(function (item) {
          _this.info['invoice_amount'] += Number(item['amount'])
        })
        _this.loading = false
        _this.title = _this.chargeTitle(Number(_this.info.state))
      }).catch(function () {
        _this.loading = false
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
        this.$router.push({
          path: `/invoiceRegister`
        })
      })
    },
    toInfo: function (item) {
      let _this = this
      let param =  {
                    'id': _this.applicationId,
                    'invoiceId': item['id'],
                    'division_way': _this.info.division_way,
                    'invoiceCategory': item['invoice_category']
                    }
      if ((_this.info.division_way === 2 || _this.info.division_way === '2') && item.fid && item.fid !== undefined) { param.fid = item.fid}
      console.log("paramparam:" + JSON.stringify(param))
      _this.$router.push({
        name: `invoiceRegisterDetailInfo`,
        params: param
      })
    },
    // 确定开票登记
    registerOk: function () {
      let _this = this
      let userId = this.sphdSocket.user.userID
      let sessionid = this.sphdSocket.sessionid
      _this.loading = true
      let data = {
        'userId': userId,
        'sessionid': sessionid,
        'applicationItemIds': _this.apIds,
        'applicationState': 5,
        'state': 2,
        'sure': 1
      }
      console.log('外面 确定 的传值：', data)
      _this.sphdSocket.send('invoiceApplicationHandle', data)
    },
    // 填完发票号的回调
    handleCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('开票登记回调', res)
      if (res['amount']) { // 后面选发票号的
        _this.$message({
          type: 'success',
          message: '其他财务人员已填写了一张发票的发票号'
        })
        _this.getDetails()
      } else if (res['state'] === '5') { // 前面确定开票
        let userId = this.sphdSocket.user.userID
        if (res['operatorId'] === userId) { // 自己操作的回调
          _this.$message({
            type: 'success',
            message: '操作成功'
          })
          _this.$router.push({
            path: `/invoiceRegister/invoiceRegister2`
          })
        } else {
          _this.$message({
            type: 'success',
            message: '其他财务人员已对此申请开票登记'
          })
          _this.isOk = true // 按钮不能点了
        }
      }
    },
    subscribeCallBack: function (res) {
      console.log('回调详情：', res)
    }
  }
}
</script>
