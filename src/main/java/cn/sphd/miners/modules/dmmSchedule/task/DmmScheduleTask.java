package cn.sphd.miners.modules.dmmSchedule.task;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dmmSchedule.service.DmmJobService;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by Administrator on 2023/3/11
 */
public class DmmScheduleTask {

    @Autowired
    DmmScheduleService dmmScheduleService;
    @Autowired
    DmmJobService dmmJobService;
    @Autowired
    UserService userService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    DlmService dlmService;

    public void dmmScheduleSettleDay(){
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("领地日程日任务开始：" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy/MM/dd-HH:mm:ss:SSS"));
//            List<User> userList = userService.getAllUser();
//            for (User u : userList) {
//                if ("1".equals(u.getIsDuty())) {
            dmmScheduleService.scheduleSettleDay(methodName);
//                }
//            }
            System.out.println("领地日程日任务结束："  + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy/MM/dd-HH:mm:ss:SSS"));
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
