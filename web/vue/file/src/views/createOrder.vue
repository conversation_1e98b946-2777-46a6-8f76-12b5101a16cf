<template>
  <div class="wheater">
    <loading v-show="isLoad" />
    <div class="logo">
      <span>Endress + Hauser</span>
      <img src="../assets/logo.png" class="logoimg" />
    </div>
    <div class="bobo" v-for="(item, i) in details">
      <div class="item">
        <span class="title">{{ item.skuName }}</span>
        <el-input-number v-model="details[i].count" @change="(currentValue, oldValue,)=>handleChange(currentValue, oldValue, details[i])" :min="1" :max="item.sMaxNum??100" label=""></el-input-number>
        <br>
        <span class='unitPrice'>{{ item.unitPrice }}元/{{item.ctypeId==-1?'':item.ctypeExpires}}{{ctype[item.ctypeId]}}</span>
        <span class='detailTotal'>{{ item.orderTotal }}元</span>
      </div>
    </div>
  </div>
  <div class='pay'>
    <span class='title'>{{ order.orderTotal }}元</span>
    <span class="paybtn" @click="goPay">去支付</span>
  </div>
</template>

<style lang="scss" scoped>
.wheater {
  text-align: left;
  padding: 30px 16px;
  min-height: 100%;
  img,
  video {
    width: 100%;
  }
  .logo {
    text-align: right;
    height: 66px;
    line-height: 66px;
    padding: 0 20px;
    span {
      font-size: 24px;
    }
    .logoimg {
      display: inline-block;
      width: 44px;
    }
  }
  h1 {
    font-size: 14px;
    margin-top: 75px;
    text-align: center;
  }
}
.title{
  font-size: medium;
  display: inline-block;
  text-align: left;
  width: 70%;
}
.bobo {
  border-left: 5px solid #61aad9;
  margin-top: 10px;
  box-shadow: 0 2px 2px #eee;
  .item {
    border: 1px solid #eee;
    text-align: left;
    padding: 1em;
    position: relative;
    .el-input-number {
      max-width: 30%;
    }
    .unitPrice {
      font-size: small;
      display: inline-block;
      width: 30%;
    }
    .detailTotal {
      font-size: small;
      text-align: right;
      display: inline-block;
      width: 70%;
    }
  }
}
.pay{
  position: absolute;
  bottom: 1em;
  left: 0;
  width: 100%;
  text-align: right;
  height: 1.5em;
  line-height: 1.5em;
  padding: 0 2em;
  z-index: 99;
  .paybtn {
    display: inline-block;
    right: 1em;
    background-color: darkgreen;
    color: white;
    border-radius: 0.5em;
    padding: 0.5em 1em;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
#app {
  position: static;
  min-height: 100vh;
}
</style>

<script>
import loading from '@/components/loading.vue'
import { newOrder, createOrder }  from '@/api/api'
import { isMiniBrowserOrWebview } from '@/utils/browerUtils'
import JSONBig from 'json-bigint'
import wx from 'weixin-js-sdk'
export default {
  data() {
    return {
      info: {},
      isLoad: true,
      order:{},
      details:[],
      ctype: {
        1: '年',
        2: '季度',
        3: '月',
        4: '日',
        5: '小时',
        6: '分钟',
        '-1': '不限'
      }
    };
  },
  components: {
    loading,
  },
  mounted() {
    let params = localStorage.getItem('createOrder') ? JSONBig.parse(localStorage.getItem('createOrder')) : this.$route.params
    console.log('mounted', params)
    if (params.skuIds) {
      this.newOrder(params.skuIds)
    }
  },
  methods: {
    newOrder(skuIds) {
      this.isLoad = true
      console.log('wonderss.module', wonderss.module)
      console.log('skuIds', skuIds)
      newOrder(skuIds, wonderss.module).then((data) => {
        console.log('newOrder', typeof data, data)
        this.isLoad = false
        if (data.success === 0) {
          console.log(data.error.errorMsg)
          this.$message({type: 'error', message: data.error.errorMsg})
        }
        console.log('newOrder order', data.data)
        this.setOrder(data.data)
      })
    },
    handleChange(currentValue, oldValue, detail) {
      let oldTotal = detail.orderTotal
      console.log('handleChange', currentValue, oldValue, detail)
      detail.orderTotal = detail.count * detail.unitPrice
      console.log('handleChange1', detail.count, detail.unitPrice, detail.orderTotal, oldTotal)
      this.order.orderTotal += detail.orderTotal - oldTotal
    },
    goPay() {
      if(!this.isLoad) {
        this.isLoad = true
        createOrder(JSONBig.stringify(this.order)).then((data) => {
          console.log('createOrder', typeof data, data)
          this.isLoad = false
          console.log('goPay', data.data)
          console.log('goPay paydata', data.data?.payData)
          if (data.success === 0) {
            console.log(data.error.message)
            this.$message({type: 'error', message: data.error.message})
          } else if(isMiniBrowserOrWebview()){
            localStorage.removeItem('webViewParams')
            data.data.url = 'folderOrFile'
            wx.miniProgram.postMessage({ data: data.data })
            wx.miniProgram.navigateTo({url: '/pages/pay/index?data='+encodeURIComponent(JSONBig.stringify(data.data))})
          } else {
            this.$message({type: 'error', message: '仅限微信浏览器访问，请在微信下访问小程序页面'})
          }
          this.setOrder(data.data?.order)
        })
      }
    },
    setOrder(order) {
      if(order) {
        this.order = order
        this.details = order?.details
        console.log('order', order)
        console.log('details', order?.details)
      }
    }
  }
};
</script>