package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-12-21 
 */

@Entity ( name ="TpAppProfile" )
@Table ( name ="t_tp_app_profile" )
public class TpAppProfile  implements Serializable {

	private static final long serialVersionUID =  7888719582507066594L;

	/**
	 * ID
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 应用ID
	 */
   	@Column(name = "app" )
	private Integer app;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 公众号ID
	 */
   	@Column(name = "of_accounts" )
	private String ofAccounts;

	/**
	 * 第三方应用帐号
	 */
   	@Column(name = "app_accounts" )
	private String appAccounts;

	/**
	 * 应用环境:1-开发,2-测试,3-生产
	 */
   	@Column(name = "app_evn" )
	private Integer appEvn;

	/**
	 * 应用版本
	 */
   	@Column(name = "app_version" )
	private String appVersion;

	/**
	 * 隐私设置
	 */
   	@Column(name = "status" )
	private Integer status;

	/**
	 * 应用名称
	 */
   	@Column(name = "nick_name" )
	private String nickName;

	/**
	 * 头像地址
	 */
   	@Column(name = "head_img_media_id" )
	private String headImgMediaId;

	/**
	 * 功能介绍
	 */
   	@Column(name = "signature" )
	private String signature;

	/**
	 * 组织机构代码证或营业执照 mediaid
	 */
   	@Column(name = "license" )
	private String license;

	/**
	 * 第三方平台接口调用令牌(Token)
	 */
   	@Column(name = "authorizer_access_token" )
	private String authorizerAccessToken;

	/**
	 *  刷新令牌
	 */
   	@Column(name = "authorizer_refresh_token" )
	private String authorizerRefreshToken;

	/**
	 * 二维码地址
	 */
   	@Column(name = "qr_code" )
	private String qrCode;

	/**
	 * 管理员名称
	 */
   	@Column(name = "sa_name" )
	private String saName;

	/**
	 * 联系方式
	 */
   	@Column(name = "contact" )
	private String contact;

	/**
	 * 类型:1-第三方小程序,2-独立小程序
	 */
   	@Column(name = "type" )
	private Integer type;

	/**
	 * 当前阶段:1-注册,2-绑定,3-提交草稿,4-提交模板,5--提交小程序,6-提审/发布
	 */
   	@Column(name = "current_phase" )
	private Integer currentPhase;

	/**
	 * 当前状态:0-草稿,1-提交申请,2-审批通过,3-审批否决
	 */
   	@Column(name = "approve_status" )
	private Integer approveStatus;

	/**
	 * 当前审批描述:记录驳回原因等信息
	 */
   	@Column(name = "approve_desc" )
	private String approveDesc;

	/**
	 * 当前批准时间
	 */
   	@Column(name = "approve_time" )
	private Date approveTime;

	/**
	 * 绑定状态:0-未绑定,1-待微信审核,2-待设置基础信息,3-待选择模板,4-绑定完成
	 */
   	@Column(name = "bind_status" )
	private Integer bindStatus;

	/**
	 * 绑定/解绑时间
	 */
   	@Column(name = "bind_time" )
	private Date bindTime;

	/**
	 * 用户数
	 */
   	@Column(name = "member_count" )
	private Integer memberCount;

	/**
	 * 模块:资源-resouce
             如以后定义好模块后再关联
	 */
   	@Column(name = "module" )
	private String module;

	/**
	 * 发布者ID
	 */
   	@Column(name = "publisher" )
	private Integer publisher;

	/**
	 * 发布者姓名
	 */
   	@Column(name = "publisher_name" )
	private String publisherName;

	/**
	 * 发布时间
	 */
   	@Column(name = "publish_time" )
	private Date publishTime;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApp() {
		return this.app;
	}

	public void setApp(Integer app) {
		this.app = app;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public String getOfAccounts() {
		return this.ofAccounts;
	}

	public void setOfAccounts(String ofAccounts) {
		this.ofAccounts = ofAccounts;
	}

	public String getAppAccounts() {
		return this.appAccounts;
	}

	public void setAppAccounts(String appAccounts) {
		this.appAccounts = appAccounts;
	}

	public Integer getAppEvn() {
		return this.appEvn;
	}

	public void setAppEvn(Integer appEvn) {
		this.appEvn = appEvn;
	}

	public String getAppVersion() {
		return this.appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getNickName() {
		return this.nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getHeadImgMediaId() {
		return this.headImgMediaId;
	}

	public void setHeadImgMediaId(String headImgMediaId) {
		this.headImgMediaId = headImgMediaId;
	}

	public String getSignature() {
		return this.signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getLicense() {
		return this.license;
	}

	public void setLicense(String license) {
		this.license = license;
	}

	public String getAuthorizerAccessToken() {
		return this.authorizerAccessToken;
	}

	public void setAuthorizerAccessToken(String authorizerAccessToken) {
		this.authorizerAccessToken = authorizerAccessToken;
	}

	public String getAuthorizerRefreshToken() {
		return this.authorizerRefreshToken;
	}

	public void setAuthorizerRefreshToken(String authorizerRefreshToken) {
		this.authorizerRefreshToken = authorizerRefreshToken;
	}

	public String getQrCode() {
		return this.qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getSaName() {
		return this.saName;
	}

	public void setSaName(String saName) {
		this.saName = saName;
	}

	public String getContact() {
		return this.contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public Integer getType() {
		return this.type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getCurrentPhase() {
		return this.currentPhase;
	}

	public void setCurrentPhase(Integer currentPhase) {
		this.currentPhase = currentPhase;
	}

	public Integer getApproveStatus() {
		return this.approveStatus;
	}

	public void setApproveStatus(Integer approveStatus) {
		this.approveStatus = approveStatus;
	}

	public String getApproveDesc() {
		return this.approveDesc;
	}

	public void setApproveDesc(String approveDesc) {
		this.approveDesc = approveDesc;
	}

	public Date getApproveTime() {
		return this.approveTime;
	}

	public void setApproveTime(Date approveTime) {
		this.approveTime = approveTime;
	}

	public Integer getBindStatus() {
		return this.bindStatus;
	}

	public void setBindStatus(Integer bindStatus) {
		this.bindStatus = bindStatus;
	}

	public Date getBindTime() {
		return this.bindTime;
	}

	public void setBindTime(Date bindTime) {
		this.bindTime = bindTime;
	}

	public Integer getMemberCount() {
		return this.memberCount;
	}

	public void setMemberCount(Integer memberCount) {
		this.memberCount = memberCount;
	}

	public String getModule() {
		return this.module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public Integer getPublisher() {
		return this.publisher;
	}

	public void setPublisher(Integer publisher) {
		this.publisher = publisher;
	}

	public String getPublisherName() {
		return this.publisherName;
	}

	public void setPublisherName(String publisherName) {
		this.publisherName = publisherName;
	}

	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
