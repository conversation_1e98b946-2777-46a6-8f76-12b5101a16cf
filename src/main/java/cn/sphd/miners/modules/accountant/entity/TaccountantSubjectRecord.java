package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-04-22 
 */
@Entity ( name ="TaccountantSubjectRecord" )
@Table ( name ="t_accountant_subject_record" )
@Alias("TaccountantSubjectRecord")
public class TaccountantSubjectRecord implements Serializable {

	private static final long serialVersionUID =  7675884414599401232L;

	/**
	 * ID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
   	@Column(name = "id" )
	private Integer id;

	/**
	 * 科目ID
	 */
   	@Column(name = "subject_id" )
	private Integer subjectId;

	/**
	 * 科目代码
	 */
   	@Column(name = "subject" )
	private String subject;

	/**
	 * 父节点
	 */
   	@Column(name = "parent" )
	private String parent;

	/**
	 * 所属机构
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 叶子科目类型
	 */
   	@Column(name = "category" )
	private Integer category;

	/**
	 * 层级
	 */
   	@Column(name = "level" )
	private Integer level;

	/**
	 * 排序
	 */
   	@Column(name = "orders" )
	private Integer orders;

	/**
	 * 和原表的state意义不同
	 * 状态,true-返回页面需要显示的数据,false-不需要显示，存储科目修改前的数据
	 */
   	@Column(name = "state" )
	private Integer state;

	/**
	 * 最大子科目数(便于生成子科目编码)
	 */
   	@Column(name = "max_child_subjects" )
	private Integer maxChildSubjects;

	/**
	 * 建账/重新建账月份
	 */
   	@Column(name = "account_month" )
	private String accountMonth;

	/**
	 * 初始金额
	 */
   	@Column(name = "initial_amount" )
	private Double initialAmount;

	/**
	 * 初始数量
	 */
   	@Column(name = "initial_quantity" )
	private Double initialQuantity;

	/**
	 * 年初余额
	 */
   	@Column(name = "beginning_balance" )
	private Double beginningBalance;

	/**
	 * 上期余额
	 */
   	@Column(name = "previous_balance" )
	private Double previousBalance;

	/**
	 * 年初数量
	 */
   	@Column(name = "beginning_quantity" )
	private Double beginningQuantity;

	/**
	 * 上期余额
	 */
   	@Column(name = "previous_quantity" )
	private Double previousQuantity;

	/**
	 * 最近结转日期
	 */
   	@Column(name = "carried_forward_date" )
	private Date carriedForwardDate;

	/**
	 * 期末余额
	 */
   	@Column(name = "balance" )
	private Double balance;

	/**
	 * 期末数量
	 */
   	@Column(name = "quantity" )
	private Double quantity;

	/**
	 * 摘要
	 */
   	@Column(name = "summary" )
	private String summary;

	/**
	 * 本期借
	 */
   	@Column(name = "credit" )
	private Double credit;

	/**
	 * 本期贷
	 */
   	@Column(name = "debit" )
	private Double debit;

	/**
	 * 本年累计借
	 */
   	@Column(name = "credit_accumulative" )
	private Double creditAccumulative;

	/**
	 * 本年累计借数量
	 */
   	@Column(name = "credit_accumulative_quantity" )
	private Double creditAccumulativeQuantity;

	/**
	 * 本年累计贷
	 */
   	@Column(name = "debit_accumulative" )
	private Double debitAccumulative;

	/**
	 * 本年累计贷数量
	 */
   	@Column(name = "debit_accumulative_quantity" )
	private Double debitAccumulativeQuantity;

	/**
	 * 年初余额方向:1-借,2-贷,3-平
	 */
   	@Column(name = "beginning_direction" )
	private String beginningDirection;

	/**
	 * 期初余额方向:1-借,2-贷,3-平
	 */
   	@Column(name = "previous_direction" )
	private String previousDirection;

	/**
	 * 期末余额方向:1-借,2-贷,3-平
	 */
   	@Column(name = "end_direction" )
	private String endDirection;

	/**
	 * 余额方向:1-借,2-贷,3-平
	 */
   	@Column(name = "balance_direction" )
	private String balanceDirection;

	/**
	 * 计量单位
	 */
   	@Column(name = "measure_unit" )
	private String measureUnit;

	/**
	 * 数量辅助核算
	 */
   	@Column(name = "quantity_assisting_accounting" )
	private String quantityAssistingAccounting;

	/**
	 * 标识是否为初始录入:1-初始录入
	 */
   	@Column(name = "label" )
	private String label;

	/**
	 * 说明
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 关联类型:1-商品，2-物料
	 */
   	@Column(name = "relevance_type" )
	private String relevanceType;

	/**
	 * 关联项的id
             根据代码去不同的表中查,商品去找商品表找id，物料去找物料表id
             
	 */
   	@Column(name = "relevance_item" )
	private Integer relevanceItem;

	/**
	 * 建账ID
	 */
   	@Column(name = "establish" )
	private Integer establish;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd")
   	@Column(name = "create_date" )
	private Date createDate;

	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date changeDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 申批项目
	 */
   	@Column(name = "approve_Item" )
	private Integer approveItem;

	/**
	 * 审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
	 */
   	@Column(name = "approve_status" )
	private String approveStatus;

	/**
	 * 审批次级
	 */
   	@Column(name = "approve_level" )
	private Integer approveLevel;

	/**
	 * 审批者ID
	 */
   	@Column(name = "auditor" )
	private Integer auditor;

	/**
	 * 审批者
	 */
   	@Column(name = "auditor_name" )
	private String auditorName;

	/**
	 * 审批日期
	 */
   	@Column(name = "audit_date" )
	private Date auditDate;

	/**
	 * 操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 申请备注
	 */
   	@Column(name = "apply_memo" )
	private String applyMemo;

	/**
	 * 审批备注
	 */
   	@Column(name = "approve_memo" )
	private String approveMemo;

	/**
	 * 消息ID
	 */
   	@Column(name = "message_id" )
	private Integer messageId;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Date getChangeDate() {
		return changeDate;
	}

	public void setChangeDate(Date changeDate) {
		this.changeDate = changeDate;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getSubjectId() {
		return this.subjectId;
	}

	public void setSubjectId(Integer subjectId) {
		this.subjectId = subjectId;
	}

	public String getSubject() {
		return this.subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getParent() {
		return this.parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getCategory() {
		return this.category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public Integer getLevel() {
		return this.level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public Integer getOrders() {
		return this.orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public Integer getState() {
		return this.state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getMaxChildSubjects() {
		return this.maxChildSubjects;
	}

	public void setMaxChildSubjects(Integer maxChildSubjects) {
		this.maxChildSubjects = maxChildSubjects;
	}

	public String getAccountMonth() {
		return this.accountMonth;
	}

	public void setAccountMonth(String accountMonth) {
		this.accountMonth = accountMonth;
	}

	public Double getInitialAmount() {
		return this.initialAmount;
	}

	public void setInitialAmount(Double initialAmount) {
		this.initialAmount = initialAmount;
	}

	public Double getInitialQuantity() {
		return this.initialQuantity;
	}

	public void setInitialQuantity(Double initialQuantity) {
		this.initialQuantity = initialQuantity;
	}

	public Double getBeginningBalance() {
		return this.beginningBalance;
	}

	public void setBeginningBalance(Double beginningBalance) {
		this.beginningBalance = beginningBalance;
	}

	public Double getPreviousBalance() {
		return this.previousBalance;
	}

	public void setPreviousBalance(Double previousBalance) {
		this.previousBalance = previousBalance;
	}

	public Double getBeginningQuantity() {
		return this.beginningQuantity;
	}

	public void setBeginningQuantity(Double beginningQuantity) {
		this.beginningQuantity = beginningQuantity;
	}

	public Double getPreviousQuantity() {
		return this.previousQuantity;
	}

	public void setPreviousQuantity(Double previousQuantity) {
		this.previousQuantity = previousQuantity;
	}

	public Date getCarriedForwardDate() {
		return this.carriedForwardDate;
	}

	public void setCarriedForwardDate(Date carriedForwardDate) {
		this.carriedForwardDate = carriedForwardDate;
	}

	public Double getBalance() {
		return this.balance;
	}

	public void setBalance(Double balance) {
		this.balance = balance;
	}

	public Double getQuantity() {
		return this.quantity;
	}

	public void setQuantity(Double quantity) {
		this.quantity = quantity;
	}

	public String getSummary() {
		return this.summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public Double getCredit() {
		return this.credit;
	}

	public void setCredit(Double credit) {
		this.credit = credit;
	}

	public Double getDebit() {
		return this.debit;
	}

	public void setDebit(Double debit) {
		this.debit = debit;
	}

	public Double getCreditAccumulative() {
		return this.creditAccumulative;
	}

	public void setCreditAccumulative(Double creditAccumulative) {
		this.creditAccumulative = creditAccumulative;
	}

	public Double getCreditAccumulativeQuantity() {
		return this.creditAccumulativeQuantity;
	}

	public void setCreditAccumulativeQuantity(Double creditAccumulativeQuantity) {
		this.creditAccumulativeQuantity = creditAccumulativeQuantity;
	}

	public Double getDebitAccumulative() {
		return this.debitAccumulative;
	}

	public void setDebitAccumulative(Double debitAccumulative) {
		this.debitAccumulative = debitAccumulative;
	}

	public Double getDebitAccumulativeQuantity() {
		return this.debitAccumulativeQuantity;
	}

	public void setDebitAccumulativeQuantity(Double debitAccumulativeQuantity) {
		this.debitAccumulativeQuantity = debitAccumulativeQuantity;
	}

	public String getBeginningDirection() {
		return this.beginningDirection;
	}

	public void setBeginningDirection(String beginningDirection) {
		this.beginningDirection = beginningDirection;
	}

	public String getPreviousDirection() {
		return this.previousDirection;
	}

	public void setPreviousDirection(String previousDirection) {
		this.previousDirection = previousDirection;
	}

	public String getEndDirection() {
		return this.endDirection;
	}

	public void setEndDirection(String endDirection) {
		this.endDirection = endDirection;
	}

	public String getBalanceDirection() {
		return this.balanceDirection;
	}

	public void setBalanceDirection(String balanceDirection) {
		this.balanceDirection = balanceDirection;
	}

	public String getMeasureUnit() {
		return this.measureUnit;
	}

	public void setMeasureUnit(String measureUnit) {
		this.measureUnit = measureUnit;
	}

	public String getQuantityAssistingAccounting() {
		return this.quantityAssistingAccounting;
	}

	public void setQuantityAssistingAccounting(String quantityAssistingAccounting) {
		this.quantityAssistingAccounting = quantityAssistingAccounting;
	}

	public String getLabel() {
		return this.label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRelevanceType() {
		return this.relevanceType;
	}

	public void setRelevanceType(String relevanceType) {
		this.relevanceType = relevanceType;
	}

	public Integer getRelevanceItem() {
		return this.relevanceItem;
	}

	public void setRelevanceItem(Integer relevanceItem) {
		this.relevanceItem = relevanceItem;
	}

	public Integer getEstablish() {
		return this.establish;
	}

	public void setEstablish(Integer establish) {
		this.establish = establish;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getApproveItem() {
		return this.approveItem;
	}

	public void setApproveItem(Integer approveItem) {
		this.approveItem = approveItem;
	}

	public String getApproveStatus() {
		return this.approveStatus;
	}

	public void setApproveStatus(String approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Integer getApproveLevel() {
		return this.approveLevel;
	}

	public void setApproveLevel(Integer approveLevel) {
		this.approveLevel = approveLevel;
	}

	public Integer getAuditor() {
		return this.auditor;
	}

	public void setAuditor(Integer auditor) {
		this.auditor = auditor;
	}

	public String getAuditorName() {
		return this.auditorName;
	}

	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}

	public Date getAuditDate() {
		return this.auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public String getApplyMemo() {
		return this.applyMemo;
	}

	public void setApplyMemo(String applyMemo) {
		this.applyMemo = applyMemo;
	}

	public String getApproveMemo() {
		return this.approveMemo;
	}

	public void setApproveMemo(String approveMemo) {
		this.approveMemo = approveMemo;
	}

	public Integer getMessageId() {
		return this.messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
