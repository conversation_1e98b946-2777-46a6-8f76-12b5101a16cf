package cn.sphd.miners.modules.resourceAuthority.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.forumArea.entity.ForumPost;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.resourceAuthority.entity.ReadingRoomEntity;
import cn.sphd.miners.modules.resourceAuthority.service.ReadRoomService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCategoryService;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2020/10/21.
 */
@Controller
@RequestMapping("/read")
public class ReadingRoomController {

    @Autowired
    ReadRoomService readRoomService;
    @Autowired
    UserService userService;
    @Autowired
    ResCategoryService resCategoryService;
    @Autowired
    ResService resService;
    @Autowired
    ForumService forumService;

    //阅览室获取初始文件夹
    @ResponseBody
    @RequestMapping("/initialFolderForRead.do")
    public JsonResult initialFolderForRead(User user){
        Integer oid = user.getOid();
        User userManager = userService.getUserByRoleCode(oid, "general");
        HashMap<String, Object> query = null;
        if (userManager != null) {
            query = resCategoryService.getFirstTypeByManager(userManager);
            query.put("state", 1);
        } else {
            query = new HashMap<>();
            query.put("state", 2);
        }
        return new JsonResult(1,query);
    }

    //阅览室获取下级文件夹
    @ResponseBody
    @RequestMapping("/getFolderFatherAndChildForRead.do")
    public JsonResult getFolderFatherAndChildForRead(User user, Integer categoryId){
        Integer oid = user.getOid();
        User userManager = userService.getUserByRoleCode(oid, "general");
        QueryData query = resCategoryService.getCategoryAndChildCategoryManager(userManager, categoryId);
        return new JsonResult(1,query);
    }

    //阅览室获取文件
    @ResponseBody
    @RequestMapping("/getFileForRead.do")
    public JsonResult getFileForRead(User user, Integer categoryId, String type, PageInfo pageInfo){
        Integer oid = user.getOid();
        User userManager = userService.getUserByRoleCode(oid, "general");
        QueryData query = resService.getFileByManager(userManager, categoryId, pageInfo, type, null);
        return new JsonResult(1,query);
    }

    //新增借阅文件
    @ResponseBody
    @RequestMapping("/insertBorrowingFile.do")
    public JsonResult insertBorrowingFile(User user, Integer fileId, String type, Integer auditor, String auditorName, String idType){
        Integer state = readRoomService.applyBorrowingFile(user,fileId,type,auditor,auditorName,idType);
        return new JsonResult(1,state);
    }

    //审批借阅文件
    @ResponseBody
    @RequestMapping("/handleBorrowingFile.do")
    public JsonResult handleBorrowingFile(User user, Integer borrowId, Integer processId,
                                          String approveStatus, String approveMemo, Integer toUser, String toUserName,
                                          String type){
        String status = readRoomService.handleBorrowFile(user,borrowId,processId,approveStatus,approveMemo,toUser,toUserName,type);
        HashMap<String,Object> map =new HashMap<>();
        map.put("status",status);
        return new JsonResult(1,map);
    }

    //申请人获取文件借阅申请 待审批、已批准
    @ResponseBody
    @RequestMapping("/getApplyReadFile.do")
    public JsonResult getApplyReadFile(User user){
        Integer userID = user.getUserID();
        QueryData query = readRoomService.getApplyReadFile(userID);
        return new JsonResult(1, query);
    }

    //中间审批人获取文件借阅申请 待审批、已批准
    @ResponseBody
    @RequestMapping("/getApproveReadFile.do")
    public JsonResult getApproveReadFile(User user){
        Integer userID = user.getUserID();
        QueryData query = readRoomService.getApproveReadFile(userID);
        return new JsonResult(1, query);
    }

    //大小总务获取终审借阅文件
    @ResponseBody
    @RequestMapping("/getLastApproveReadFile.do")
    public JsonResult getLastApproveReadFile(User user){
        Integer oid = user.getOid();
        List<ReadingRoomEntity> list = readRoomService.getLastReadFile(oid);
        QueryData query = new QueryData();
        query.put("lastApproveReadFile", list);
        return new JsonResult(1,query);
    }

    /**
     * 阅览室查询文件接口
     * @param eventType 查询事件类型 1申请中查找 2是中间审批中查找 3是终极审批中查找
     * @param type 查询时间类型 1是近7日 2是月 3是自定义
     * @param timeBegin 查询时间 查月时传“2019-01”自定义时传“2019-01-01” 进7日不用传
     * @param timeEnd 只有当自定义时传 “20198-03-02”
     * @param approveStatus 1是已批准 2是已驳回
     * @return
     * @throws IOException
     * @throws ParseException
     */
    @ResponseBody
    @RequestMapping("/findReadFileByType.do")
    public JsonResult findReadFileByType(User user, Integer oid, String eventType, String type, String timeBegin, String timeEnd, String approveStatus, Integer fromUser, PageInfo pageInfo, String fromUserName){
        Integer userID = user.getUserID();
        QueryData query = new QueryData();
        QueryData queryData = new QueryData();
        this.handTimeForFind(query,queryData,userID,approveStatus,fromUser,fromUserName,pageInfo,timeBegin,timeEnd,oid,type,eventType);
        List<ReadingRoomEntity> list = readRoomService.findReadFileByPage(query, eventType);
        queryData.put("list", list);
        queryData.put("pageInfo", pageInfo);
        return new JsonResult(1, queryData);
    }

    //获取借阅文件详情
    @ResponseBody
    @RequestMapping("/getReadFileMessage.do")
    public JsonResult getReadFileMessage(User user, Integer borrowId){
        QueryData query = readRoomService.readFileMes(borrowId, user);
        return new JsonResult(1,query);
    }

    //获取文件保存位置
    @ResponseBody
    @RequestMapping("/getReadFilePlace.do")
    public JsonResult getReadFilePlace(Integer fileId){
        String  allCategoryName = readRoomService.getNewFilePlace(fileId);
        QueryData query = new QueryData();
        query.put("allCategoryName", allCategoryName);
        return new JsonResult(1,query);
    }

/*    @Scheduled(cron = "5 * * * * ?")
    public void taskRead(){
        System.out.println("hello");
    }
    @Scheduled(cron = "5 * * * * ?")
    public void taskReadtwo(){
        System.out.println("world");
    }*/

    //获取可以借阅的讨论列表
    @ResponseBody
    @RequestMapping("/getOpenForumPost.do")
    public JsonResult getOpenForumPost(User user, String title, PageInfo pageInfo){
        Integer oid = user.getOid();
        List<ForumPost> list = forumService.listOpenForumPost(oid,title,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("openList", list);
        map.put("pageInfo", pageInfo);
        return new JsonResult(1,map);
    }

    //新增借阅讨论
    @ResponseBody
    @RequestMapping("/insertBorrowingForum.do")
    public JsonResult insertBorrowingForum(User user, Integer postId, String type, Integer auditor, String auditorName){
        readRoomService.applyBorrowingForum(user,postId,type,auditor,auditorName);
        return new JsonResult(1,"成功");
    }

    //审批借阅讨论
    @ResponseBody
    @RequestMapping("/handleBorrowingForum.do")
    public JsonResult handleBorrowingForum(User user, Integer borrowId, Integer processId,
                                           String approveStatus, String approveMemo, Integer toUser, String toUserName,
                                           String type){
        String status = readRoomService.handleBorrowForum(user, borrowId, processId, approveStatus, approveMemo, toUser, toUserName, type);
        HashMap<String,Object> map =new HashMap<>();
        map.put("status",status);
        return new JsonResult(1,map);
    }

    //申请人获取讨论借阅申请 待审批、已批准
    @ResponseBody
    @RequestMapping("/getApplyReadForum.do")
    public JsonResult getApplyReadForum(User user){
        Integer userID = user.getUserID();
        QueryData query = readRoomService.getApplyReadForum(userID);
        return new JsonResult(1, query);
    }

    //中间审批人获取文件借阅申请 待审批、已批准
    @ResponseBody
    @RequestMapping("/getApproveReadForum.do")
    public JsonResult getApproveReadForum(User user){
        Integer userID = user.getUserID();
        QueryData query = readRoomService.getApproveReadForum(userID);
        return new JsonResult(1, query);
    }

    //大小总务获取终审借阅文件
    @ResponseBody
    @RequestMapping("/getLastApproveReadForum.do")
    public JsonResult getLastApproveReadForum(User user){
        Integer oid = user.getOid();
        List<ReadingRoomEntity> list = readRoomService.getLastReadForum(oid);
        QueryData query = new QueryData();
        query.put("listApproveReadFile", list);
        return new JsonResult(1,query);
    }

    /**
     * 阅览室查询讨论接口
     * @param eventType 查询事件类型 1申请中查找 2是中间审批中查找 3是终极审批中查找
     * @param type 查询时间类型 1是近7日 2是月 3是自定义
     * @param timeBegin 查询时间 查月时传“2019-01”自定义时传“2019-01-01” 进7日不用传
     * @param timeEnd 只有当自定义时传 “20198-03-02”
     * @param approveStatus 1是已批准 2是已驳回
     * @return
     * @throws IOException
     * @throws ParseException
     */
    @ResponseBody
    @RequestMapping("/findReadForumByType.do")
    public JsonResult findReadForumByType(User user, Integer oid, String eventType, String type, String timeBegin, String timeEnd, String approveStatus, Integer fromUser, PageInfo pageInfo, String fromUserName){
        Integer userID = user.getUserID();
        QueryData query = new QueryData();
        QueryData queryData = new QueryData();
        this.handTimeForFind(query,queryData,userID,approveStatus,fromUser,fromUserName,pageInfo,timeBegin,timeEnd,oid,type,eventType);
        List<ReadingRoomEntity> list = readRoomService.findReadForumByPage(query, eventType);
        queryData.put("list", list);
        queryData.put("pageInfo", pageInfo);
        return new JsonResult(1, queryData);
    }

    //获取借阅文件详情
    @ResponseBody
    @RequestMapping("/getReadForumPostMessage.do")
    public JsonResult getReadForumPostMessage(User user, Integer borrowId){
        QueryData query = readRoomService.readForumPostMes(borrowId,user);
        return new JsonResult(1,query);
    }



    private void handTimeForFind(QueryData query, QueryData queryData, Integer userID, String approveStatus,
                                 Integer fromUser, String fromUserName, PageInfo pageInfo, String timeBegin,
                                 String timeEnd, Integer oid, String type, String eventType){
        Date dateBegin = null;
        Date dateEnd = null;
        query.put("toUser", userID);
        query.put("approveStatus", approveStatus);
        if (fromUser != null) {
            query.put("creator", fromUser);
            queryData.put("fromUserName", fromUserName);
        }
        query.put("pageInfo", pageInfo);
        if (type.equals("1")) {
            dateEnd = NewDateUtils.tomorrow();
            dateBegin = NewDateUtils.changeDay(dateEnd, -7);
            query.put("timeBegin", dateBegin);
            query.put("timeEnd", dateEnd);
            dateEnd = new Date();
        } else if (type.equals("2")) {
            dateBegin = NewDateUtils.changeMonth(new Date(),0);
            dateEnd = NewDateUtils.getLastTimeOfMonth(dateBegin);
            query.put("timeBegin", dateBegin);
            query.put("timeEnd", dateEnd);
        } else if (type.equals("3")) {
            dateBegin = NewDateUtils.dateFromString(timeBegin + " 00:00:00","yyyy-MM-dd HH:mm:ss");
            dateEnd = NewDateUtils.dateFromString(timeEnd+ " 23:59:59","yyyy-MM-dd HH:mm:ss");
            query.put("timeBegin", dateBegin);
            query.put("timeEnd", dateEnd);
        }
        if ("3".equals(eventType)) {
            query.put("toMid", "AuthApproval");
            query.put("fromOrg", oid);
        }
        queryData.put("timeBegin", dateBegin);
        queryData.put("timeEnd", dateEnd);
    }

    /**
     * 跳转页面的接口
     */
    @RequestMapping("/readingRoom.do")
    public String readingRoom(User user, Model model ){
        model.addAttribute("user",user);
        return "/readingRoom/resourceBorrow";
    }

    /**
     * 跳转页面的接口
     */
    @RequestMapping("/archivedForum.do")
    public String readingRoomForumpost(User user, Model model ){
        model.addAttribute("user",user);
        return "/readingRoom/discussionBorrow";
    }
}
