<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquControlImageMapper">
    
    <resultMap type="TEquControlImage" id="TEquControlImageResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="controlPoint"    column="control_point"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="normalPath"    column="normal_path"    />
        <result property="thumbnailPath"    column="thumbnail_path"    />
        <result property="orders"    column="orders"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTEquControlImageVo">
        select id, org, control_point, title, content, normal_path, thumbnail_path, orders, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_equ_control_image
    </sql>

    <select id="selectTEquControlImageList" parameterType="TEquControlImage" resultMap="TEquControlImageResult">
        <include refid="selectTEquControlImageVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="controlPoint != null "> and control_point = #{controlPoint}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="normalPath != null  and normalPath != ''"> and normal_path = #{normalPath}</if>
            <if test="thumbnailPath != null  and thumbnailPath != ''"> and thumbnail_path = #{thumbnailPath}</if>
            <if test="orders != null "> and orders = #{orders}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTEquControlImageById" parameterType="Long" resultMap="TEquControlImageResult">
        <include refid="selectTEquControlImageVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTEquControlImage" parameterType="TEquControlImage" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_control_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="controlPoint != null">control_point,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="normalPath != null">normal_path,</if>
            <if test="thumbnailPath != null">thumbnail_path,</if>
            <if test="orders != null">orders,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="controlPoint != null">#{controlPoint},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="normalPath != null">#{normalPath},</if>
            <if test="thumbnailPath != null">#{thumbnailPath},</if>
            <if test="orders != null">#{orders},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTEquControlImage" parameterType="TEquControlImage">
        update t_equ_control_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="controlPoint != null">control_point = #{controlPoint},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="normalPath != null">normal_path = #{normalPath},</if>
            <if test="thumbnailPath != null">thumbnail_path = #{thumbnailPath},</if>
            <if test="orders != null">orders = #{orders},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquControlImageById" parameterType="Long">
        delete from t_equ_control_image where id = #{id}
    </delete>

    <delete id="deleteTEquControlImageByIds" parameterType="String">
        delete from t_equ_control_image where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>