<template>
  <div class="lycTaskHandel">
    <Pageheader title="歌词任务处理" :showBack="true" :runMyBackFun="true" @myBackFun="goBack"/>
    <div class="handleC">
      <div @click="tabNum(1)" :class="activeIndex === 1 ? 'activeIndex' :''">待上传（X）</div>
      <div @click="tabNum(2)" :class="activeIndex === 2 ? 'activeIndex' :''">已上传（X）</div>
    </div>
    <div class="mar30">
      <div v-show="activeIndex === 1">
        <div class="lyricList">
          <div class="lyItem" v-for="(item, index) in list" :key="index">
            <div @click="goToHandel(item)" class="lyItemC">
              <span class="name"> {{ item.name }}</span>
              <span class="time" v-if="item.captionRequired">{{ item.durationTime  }}
          </span>
              <arrow-right class="arrR" />
            </div>
          </div>
        </div>
      </div>
      <div v-show="activeIndex === 2">
        <div class="live-panel live-tr">
          <div class="txtCenter">
            年份:
            <el-date-picker v-model="year" type="year" value-format="YYYY" placeholder="选择年份" @change="getUploadedLyricByYearFun(year)"></el-date-picker>
          </div>
        </div>
        <div class="live-panel live-tr">
          {{ year }} 年共上传 {{ listNum}} 首
        </div>
        <div class="live-panel live-pad">
          <div class="live-tr" v-for="(item, index) in list" :key="index"  >
            <div @click="goPage(item)" :class="list.length-1 === index ? '':' live-tr-line'" >
              <span>{{ item.time }}</span>
              <span class="live-right ">
                {{ item.num }}首
                <el-icon class="relaRight" >
                  <arrow-right-bold color="#53B5A8" class="houseSty8" />
                </el-icon>
            </span>

            </div>
          </div>
          <div class="live-tr" style="color:#aaa;" v-if="list.length === 0">
            暂无数据
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import JSONBig from 'json-bigint'
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { getLyricTask, getUploadedLyricByYear,  } from '@/api/music.js'
import auth from '@/sys/auth'

const activeIndex = ref(1)
const listNum = ref(0)
const year = ref('')
const list = ref('')
const router = useRouter();


onMounted(() => {
  let activeNum = Number(localStorage.getItem('activeIndex'))
  tabNum(activeNum)
  year.value = (new Date()).format("yyyy")
})

const goBack = () => {
  router.push({ name: 'homeHandle' });
}

const tabNum = (num) => {
  activeIndex.value = num
  localStorage.setItem('activeIndex',num)
  const userInfoSys = auth.getUser()
  let data = { }
  if(num === 1){ // 待上传
    if(userInfoSys.roleCode === "staff"){ // 成员
      data.type = 2
      data.approveStatus = 1
    }else{ // 主播
      data.type = 1
      data.approveStatus = 1
    }
    getLyricTask(data).then(res=>{
      console.log('getLyricTask 返回值', res)
      list.value = res.data.listSongs

    }).catch(err => {
      console.log('有错误', err)
    })
  }else if(num === 2){ // 已上传
    getUploadedLyricByYearFun(year.value)
  }

}
const getUploadedLyricByYearFun = (yearSend) => {
  let data = {  }
  if(yearSend){
    data.year = yearSend
  }
  getUploadedLyricByYear(data).then(res => {
    console.log('getUploadedLyricByYear res =', res)
    let ll = res.data.timeRecord
    list.value = ll
    let nn = 0
    ll.forEach( item => {
      nn += Number(item.num || 0)
    })
    listNum.value = nn


  }).catch(err => {
    console.log('err=', err)
  })
}
const goPage = (item) => {
  console.log('要去的详情', item)
  if(item.num && item.num>0){
    localStorage.setItem('lyricTaskAlreadyYear', JSONBig.stringify(item) )
    router.push({ name:'lyricTaskAlready'});
  }

}
const goToHandel = (item) => {
  console.log('要去的详情', item)
  let songDuration = item.songDuration
  let min = songDuration.split('分')[0]
  let ssStr = songDuration.split('分')[1]
  let ss = ssStr.split('秒')[0]

  let dur0 = item.trackDuration / 1000 ; // 单位是 秒
  let dain = String(dur0).split('.')[1]
  let dur = String(dur0).split('.')[0]
  let min0 = parseInt(dur/60)
  let ss0 = dur%60
  let durationTime0 = `${min0}:${ss0}.${dain}` // 12:12.909090
  let durationTime00 = `${min0}:${ss0}` // 12:12.909090


  let dur01 = item.bgmDuration / 1000 ; // 单位是 秒
  let dain1 = String(dur01).split('.')[1]
  let dur1 = String(dur01).split('.')[0]
  let min01 = parseInt(dur1/60)
  let ss01 = dur1%60
  let durationTime01 = `${min01}:${ss01}.${dain1}` // 12:12.909090
  let durationTime011 = `${min01}:${ss01}` // 12:12.909090


  const formData = {
    id: item.id,
    from:'lycTaskHandel',
    name: item.name,
    addWord:'1',
    trackDuration: item.trackDuration,
    bgmDuration:item.bgmDuration,
    publishType:item.publishType,
    rhythmOption:item.rhythmOption,
    originalPath:item.originalPath,
    bgmPath:item.bgmPath,
    inllustration:item.inllustration,
    firstSentence:item.firstSentence,
    lastSentence:item.lastSentence,
    approveStatus:item.approveStatus,
    rhythm:'',
    lyrics:'',
    user:'',
    duration:'',// 歌曲时长
    banTime: { min:min, ss:ss },
    uploadType: '',
    orlLyc:{ duration:dur0, durationTime:durationTime0, durationTime2:durationTime00, filePath:item.originalPath, state:'' },
    banLyc:{ duration:dur01, durationTime:durationTime01, durationTime2:durationTime011, filePath:item.bgmPath, state:'' },
  }
  let formData2 = (JSONBig.stringify(formData))
  localStorage.setItem('newlycdata', formData2)
  router.push({ name:'lyricTip'});
}
</script>
<style lang="scss" scoped>
.arrR{
  width: 16px;
  display: inline-block;
}
.mar30{ margin-top: 30px; }
.handleC{
  display: flex;
  padding-top: 10px;
  background-color: #fff;
  >div{
    flex: 1;
    line-height: 30px;
    border-bottom: 2px solid #fff;
    text-align: center;
    &.activeIndex{
      border-bottom-color: $livePageHeadBg;
    }
  }
}
.lyricList{
  margin-top: 20px;
  background: #fff;
  .lyItem:nth-child(odd){
    background-color: #eee;
  }
  .lyItemC{
    display: flex;
    padding:0 10px;
    .name{
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 40px;
      flex: 3;
    }
    .arrR{
      width: 16px;
      display: inline-block;
    }
    .time{
      display: inline-block;
      line-height: 43px;
      text-align: right;
      flex: 1;

    }
  }
}
.houseSty8{
  position: relative;
  top:0px;
}
</style>
