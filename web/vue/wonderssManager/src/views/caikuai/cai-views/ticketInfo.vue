<!--日流水的具体详情-->
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>
    <div v-if=" info && info.source">
      <ul class="xiangUlB margin-10-15">
        <li v-if="info.financeReturn.type == '2'"><span>承兑汇票详情</span>
          <span class="ty-color-orange" >此汇票已存入银行</span>
        </li>
        <li v-if="info.financeReturn.type == '1'"><span>转账支票详情</span>
          <span class="ty-color-orange" >此支票已存入银行</span>
        </li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="tkInfo margin-10-15">
        <li><span>{{ info.financeReturn.category === '4' ? '汇' : '支' }}票号</span><span>{{ info.financeReturn.returnNo }}</span></li>
        <li><span>票据金额</span><span>{{ info.financeReturn.amount }}</span></li>
        <li><span>{{ info.financeReturn.category === '4' ? '汇' : '支' }}票到期日</span><span>{{ new Date(info.financeReturn.expireDate).format('yyyy-MM-dd') }}</span></li>
        <li><span>出具{{ info.financeReturn.category === '4' ? '汇' : '支' }}票银行</span><span>{{ info.financeReturn.originalCorp }}</span></li>
        <li><span>付款单位</span><span>{{ info.financeReturn.payer }}</span></li>
        <li><span>{{ info.financeReturn.category === '4' ? '原始出具汇票单位' : '出具支票单位' }}</span><span>{{ info.financeReturn.originalCorp }}</span></li>
        <li><span style=" vertical-align: top;">备注</span>
          <span style=" white-space: break-spaces; width:184px; line-height: 20px; ">{{ info.financeReturn.memo }}</span></li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="tkInfo margin-10-15">
        <li><span>收到{{ info.financeReturn.category === '4' ? '汇' : '支' }}票日期</span><span>{{ new Date(info.financeReturn.receiveDate).format('yyyy-MM-dd') }}</span></li>
        <li class="rightTxt">录入者 {{ info.financeReturn.createName }} {{ new Date(info.financeReturn.createDate).format('yyyy-MM-dd hh:mm:ss') }}</li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="tkInfo margin-10-15">
        <li><span>存入银行时间</span><span>{{ new Date(info.financeReturn.depositDate).format('yyyy-MM-dd')  }}</span></li>
        <li><span style="width:100px;">所存入的账户</span><span style=" ">{{ info.financeReturn.saveBankName }} {{ info.financeReturn.accout }}</span></li>
        <li><span>到账时间</span><span>{{ new Date(info.financeReturn.receiveAccountDate).format('yyyy-MM-dd') }}</span></li>
        <li><span>存入经手人</span><span>{{ info.financeReturn.depositorName }}</span></li>
        <li class="rightTxt">录入者 {{ info.financeReturn.updateName }} {{ new Date(info.financeReturn.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</li>
      </ul>
      <div class="hr_gray"></div>


    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  data() {
    return {
      info: null,
    }
  },
  mixins: [finance],
  mounted() {
    this.ticketInfo()
  },
  methods: {
    ticketInfo(){
      let id =  this.$route.params.id
      console.log('id=', id)
      this.financeApi.getReturnDetail({ returnId : id }).then(res1=>{
        let res = res1.data.data
        this.info = res

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
.tkInfo{
  line-height: 30px; font-size: 15px;
  li>span:nth-child(1){
    width: 140px; margin-right: 20px; display: inline-block;
  }
  li>span:nth-child(2){
     display: inline-block;
  }
  .rightTxt{
    text-align: right;
  }
}

</style>