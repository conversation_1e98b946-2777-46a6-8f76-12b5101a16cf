<template>
  <Pageheader title="核心人物管控" :showBack="true"></Pageheader>
  <div class="container_main">
    <div v-if="type === 'set'">
      <el-row style="margin-top: 16px">
        <el-col :span="20" :offset="2">
          您已选择 <b>{{temporaryAdmin.userName}}</b> 操作职工导入。<br>
          手机号： <b>{{temporaryAdmin.mobile}}</b>。
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20" :offset="2">您如需亲自导入，需到电脑端操作。</el-col>
      </el-row>
      <el-row>
        <el-col :span="20" :offset="2">
          换手机号，或换为另一人，请点击修改。<br>
          <el-text type="primary">
            注：修改后，原手机号将无法继续操作，该号码已导入的数据也将被清除！
          </el-text>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20" :offset="2" style="text-align: center">
          <el-button type="primary" size="large" style="padding-left: 32px; padding-right: 32px" @click="changeTemporaryAdmin">修改</el-button>
        </el-col>
      </el-row>
    </div>
    <div v-if="type === 'reset'">
      <el-row style="margin-top: 16px">
        <el-col :span="20" :offset="2">
          您已在电脑端选择亲自导入。亲自导入，需到电脑端操作。
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20" :offset="2">如需找一位职工作为临时管理员来导入，请点击修改。</el-col>
      </el-row>
      <el-row>
        <el-col :span="20" :offset="2">
          修改后，您电脑端已导入的数据将被清除。
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20" :offset="2" style="text-align: center">
          <el-button size="large" type="primary" style="padding-left: 32px; padding-right: 32px" @click="resetTemporaryAdmin">修改</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import * as api from "@/api/centralControl";
export default {
  data() {
    return {
      type: 'set',
      temporaryAdmin: {
        userID: 0,
        userName: '',
        mobile: ''
      }
    }
  },
  components: {},
  created() {
    let type = this.$route.params.type
    console.log('type:', type)
    this.type = type
    api.getTemporaryAdmin()
    .then(res => {
      let data = res.data.data
      this.temporaryAdmin = data.user
    })
  },
  methods: {
    jump: function (path) {
      this.$router.push(path)
    },
    changeTemporaryAdmin() {
      this.$router.push({
        path: `/coreInput/change`,
        query: {
          userId: this.temporaryAdmin.userID
        }
      })
    },
    resetTemporaryAdmin() {
      this.$router.push({
        path: `/coreInput/reset`,
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 16px;
}
.el-row:last-child {
  margin-bottom: 0;
}
</style>
