package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.accountant.service.ReimburseRelevanceService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.entity.AccountDetail;
import cn.sphd.miners.modules.finance.entity.AccountPeriod;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBill;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/12/9.
 * 报销受理
 * 李旭
 */
@Controller
@RequestMapping("/reimburse")
public class ReimburseController {

    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    CodeService codeService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    AccountService accountService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    FinanceReimburseBillService financeReimburseBillService;
    @Autowired
    ReimburseRelevanceService reimburseRelevanceService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;


    /**
     * <AUTHOR>
     * @Date 2016/12/9 15:34
     * 打开报销受理类别设置主页面
     */
    @ResponseBody
    @RequestMapping("/toCode.do")
    public void toCode(User user, HttpServletResponse response) throws IOException {
        Integer oid= user.getOid();
        CodeCategory codeCategory=codeService.getCodeCategoryByNameOid(oid,"费用类别");
        Map<String,Object> map=new HashMap<String,Object>();
        List<Code> codeList=codeService.getCodesByCategory(codeCategory.getId());
        map.put("codeList",codeList);
        ObjectToJson.objectToJson1(map,new String[]{"parent","category"},response);
    }


    /**
     * <AUTHOR>
     * @Date 2016/12/9 15:55
     * 新增费用类别
     */
    @ResponseBody
    @RequestMapping("/addCode.do")
    public void addCode(User user, Code code, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (code!=null) {
            Integer oid = user.getOid();
            CodeCategory codeCategory = codeService.getCodeCategoryByNameOid(oid, "费用类别");

            Code c=codeService.getCodeByName(oid,code.getName());
            if (c!=null){
                map.put("status", 2);//此类别已经存在，臭不要脸
            }else {
                code.setCategory(codeCategory);
                code.setEnabled(1);
                code.setOrders(160);
                codeService.addCode(code);
                if (reimburseRelevanceService.judgeRelevance(oid)){ //判断是否设置会计关联
                    User accounting = userService.getUserByRoleCode(oid, "accounting");//会计
                    if (accounting == null) {
                        accounting = userService.getUserByRoleCode(oid, "agentAccounting");//代理会计
                    }
                    userSuspendMsgService.saveUserSuspendMsg(1, "财务人员新增了名为“"+code.getName()+"”的一级费用类别！", "新增时间：" + new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date()), accounting.getUserID(),"subjectMessage",code.getId());

                }
                map.put("code", code);
                map.put("status", 1);
            }
        }else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map,new String[]{"parent","category"},response);
    }

    /**
     * <AUTHOR>
     * @Date 2016/12/9 16:48
     * 暂停使用费用类别
     */
    @ResponseBody
    @RequestMapping("/updateCode.do")
    public void updateCode(Code code,HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();

        if (code.getId()!=null&&code.getEnabled()!=null) {
            Code c = codeService.getCodeById(code.getId());
            c.setEnabled(code.getEnabled());
            codeService.updateCode(c);
            map.put("code", code);
            map.put("status", 1);

        }else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map,new String[]{"parent","category"},response);

    }


    /**
     * <AUTHOR>
     * @Date 2016/12/23 14:55
     * 财务报销受理页面接口
     */
    @ResponseBody
    @RequestMapping("/getReimbursementAcceptance.do")
    public void getReimbursementAcceptance(User user, String approvalStatus, HttpServletResponse response) throws IOException {
//        List<ApprovalProcess> apps=approvalProcessService.getApprovalProcessByApprovalStatus(user.getUserID(),approvalStatus);//1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
        List<ApprovalProcess> apps=approvalProcessService.getApprovalProcessByMid("lp",approvalStatus,user.getOid());
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("approvalProcess",apps);
        ObjectToJson.objectToJson1(map,new String[]{"approvalFlow","approvalProcessHashSet","user"},response);
    }


    /**
     * <AUTHOR>
     * @Date 2016/12/26 11:38
     * 财务报销受理接收or驳回
     */
    @ResponseBody
    @RequestMapping("/financeReceiveReimbursement.do")
    public void financeReceiveReimbursement(User user, Integer approvalProcessId, Integer approvalStatus,String approveMemo, HttpServletResponse response) throws IOException {

        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid= user.getOid();
        if (approvalProcessId!=null){
            ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessById(approvalProcessId);
            PersonnelReimburse personnelReimburse=personnelReimburseService.personnelReimburseById(approvalProcess.getReimburse().getId());
            personnelReimburse.setApproveLevel(approvalProcess.getLevel());
            List<UserMessage> userMessageList = userMessageService.getUserMessageListByMessageId(approvalProcessId,"4");
            //approvalStatus等于1为批准，否则都是驳回
            if (approvalStatus==1){
                if (approvalProcess.getApproveStatus().equals("7")){
                    map.put("status",3);//此报销申请已接收，不可重复接收
                }else {
                    approvalProcess.setApproveStatus("7");//批准接收

                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcess.setToUser(user.getUserID());
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    ApprovalProcess process = new ApprovalProcess();
                    process.setApproveStatus("4");// 待两讫
                    process.setLevel(approvalProcess.getLevel());
                    process.setToUser(approvalProcess.getToUser());
                    process.setToUserName("财务两讫者");
                    process.setToMid(approvalProcess.getToMid());
                    process.setReimburse(approvalProcess.getReimburse());
                    process.setCreateDate(new Date());
                    approvalProcessService.saveApprovalProcess(process);

                    //批准后给财务两讫的消息
                    List<UserPopedom> userPopedomList = userPopedomService.getUserPopedomByMid(oid, "lp");
                    for (UserPopedom u : userPopedomList) {
                        if (!u.getUser().getRoleCode().equals("")) {
                            UserMessage userMessage = new UserMessage();
                            userMessage.setUser(personnelReimburse.getUser());
                            userMessage.setApprovalStatus(4);
                            userMessage.setMessageType("4");
                            userMessage.setHandleId(u.getUserId().toString());
                            userMessage.setMessageId(process.getId());
                            userMessage.setEventType("报销申请");
                            userMessage.setIllustrate(personnelReimburse.getUser().getUserName() + "的报销申请");
                            userMessage.setPersonnelReimburId(personnelReimburse.getId());
                            userMessage.setState(1);
                            userMessage.setReceiveUserId(u.getUserId());  //接收消息人
                            userMessage.setCreateDate(new Date());
                            userMessageService.addUserMassage(userMessage);
                        }
                    }
                }
            }else {
                approvalProcess.setApproveStatus("8");
                approvalProcess.setUserName(user.getUserName());
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setApproveMemo(approveMemo);
                personnelReimburse.setApproveStatus("3");
                personnelReimburse.setApproveMemo(approveMemo);
                approvalProcessService.updateApprovalProcess(approvalProcess);


            }
            for (UserMessage userMessage1:userMessageList) {
                if (approvalStatus==1){
                    userMessage1.setApprovalStatus(7); //消息中的审批状态为批准
                }else {
                    userMessage1.setApprovalStatus(3); //消息中的审批状态为驳回
                }
                userMessage1.setState(2);
                userMessage1.setHandleId(user.getUserID().toString());
                userMessage1.setHandleName(user.getUserName());
                userMessage1.setHandleReply(approveMemo);
                userMessage1.setHandleTime(new Date());
                userMessageService.updateUserMassage(userMessage1);
            }

            personnelReimburseService.updatePersonnelReimburse(personnelReimburse);

            //   批准/驳回后给申请者的消息
            UserMessage userMessage = new UserMessage();
            userMessage.setUser(personnelReimburse.getUser());
            if (approvalStatus==1){
                userMessage.setApprovalStatus(4);
            }else {
                userMessage.setApprovalStatus(3);
            }
            userMessage.setHandleId(user.getUserID().toString());
            userMessage.setMessageId(approvalProcess.getId());
            userMessage.setEventType("报销结果");
            userMessage.setIllustrate(personnelReimburse.getUser().getUserName()+"的报销申请");
            userMessage.setPersonnelReimburId(personnelReimburse.getId());
            userMessage.setMessageType("4");
            userMessage.setState(1);
            userMessage.setReceiveUserId(personnelReimburse.getUser().getUserID());  //接收消息人
            userMessage.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
            userMessage.setCreateDate(new Date());
            userMessageService.addUserMassage(userMessage);

            map.put("status",1);//批准成功
        }else {
            map.put("status",0);//批准失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }


    /**
     * <AUTHOR>
     * @Date 2016/12/26 14:49
     * 财务报销受理两讫
     * summary：摘要
     */
    @ResponseBody
    @RequestMapping("/financeAccountBalance.do")
    public void financeAccountBalance(User user, String payMethod,String summary, Integer financeAccountId, Integer approvalProcessId, Integer approvalStatus, String approveMemo,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid= user.getOid();
        int fa=0;//发送消息 0-不发(余额不足，不发消息) 1-发
        if (approvalProcessId!=null){
            ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessById(approvalProcessId);
            PersonnelReimburse personnelReimburse=personnelReimburseService.personnelReimburseById(approvalProcess.getReimburse().getId());
            personnelReimburse.setApproveLevel(approvalProcess.getLevel());
            List<UserMessage> userMessageList = userMessageService.getUserMessageListByMessageId(approvalProcessId,"4");

            //approvalStatus等于1为批准，否则都是驳回
            if (approvalStatus==1){

                FinanceAccount f= new FinanceAccount();
                if (financeAccountId!=null&&financeAccountId!=0) {
                    f = accountService.getFinanceAccountById(financeAccountId);
                }else {
                    f = accountService.getFinanceAccountByOidAndType(oid,1);//备用金
                }
                Double jieguo=f.getBalance().subtract(personnelReimburse.getAmount()).doubleValue();

                if (jieguo<0){
                    map.put("status",2);//余额不足无法批准
                }else {
                    if (personnelReimburse.getApproveStatus().equals("2")){
                        map.put("status",3);//  该报销申请已两讫，不能再次两讫
                    }else {
                        fa = 1;//发消息
                        approvalProcess.setApproveStatus("5");//批准报销

                        approvalProcess.setHandleTime(new Date());
                        approvalProcess.setUserName(user.getUserName());
                        approvalProcessService.updateApprovalProcess(approvalProcess);


//                    approvalProcess.setApproveStatus("5");
//                    approvalProcess.setHandleTime(new Date());
//                    approvalProcess.setUserName(user.getUserName());
//                    approvalProcessService.updateApprovalProcess(approvalProcess);

                        personnelReimburse.setAccount(f.getAccount());
                        personnelReimburse.setPayMethod(payMethod);
                        personnelReimburse.setApproveStatus("2");
                        personnelReimburse.setAuditDate(new Date());
                        personnelReimburse.setAuditor(user.getUserID());
                        personnelReimburse.setAuditorName(user.getUserName());
                        personnelReimburse.setSummary(summary);  //摘要

                        //生成财务明细
                        AccountDetail accountDetail = new AccountDetail();
                        accountDetail.setDebit(personnelReimburse.getAmount());//合计报销金额 录入支出
                        accountDetail.setBalance(f.getBalance().subtract(personnelReimburse.getAmount()));
                        accountDetail.setCreator(user.getUserID());
                        accountDetail.setCreateName(user.getUserName());
                        accountDetail.setCreateDate(new Date());
                        accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                        accountDetail.setOrg(orgService.getByOid(oid));
                        accountDetail.setSummary(personnelReimburse.getSummary());
                        accountDetail.setPurpose(personnelReimburse.getPurpose());
                        accountDetail.setMemo(personnelReimburse.getMemo());
                        accountDetail.setAuditorName(user.getUserName());
                        accountDetail.setPersonnelReimburse(personnelReimburse);
                        accountDetail.setBillAmount(personnelReimburse.getBillAmount());
                        if ("1".equals(payMethod)) {
                            accountDetail.setMethod("1");//现金
                        } else if ("3".equals(payMethod)) {
                            accountDetail.setMethod("5");//银行转账
                        }
                        accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择

                        //更新账户
                        f.setDebit(f.getDebit().add(personnelReimburse.getAmount()));//总支出加上
                        f.setBalance(f.getBalance().subtract(personnelReimburse.getAmount()));//在余额中减去
                        accountService.updateFinanceAccount(f);

                        //月结
                        AccountPeriod yue = accountService.getPeroidByType(oid, f.getAccountType(), f.getId(), 1);
                        yue.setDebit(yue.getDebit().add(personnelReimburse.getAmount()));
                        yue.setBalance(yue.getBalance().subtract(personnelReimburse.getAmount()));
                        accountService.updateAccountPeroid(yue);

                        //日结
                        AccountPeriod ri = accountService.getPeroidByType(oid, f.getAccountType(), f.getId(), 2);
                        ri.setDebit(ri.getDebit().add(personnelReimburse.getAmount()));
                        ri.setBalance(ri.getBalance().subtract(personnelReimburse.getAmount()));
                        accountService.updateAccountPeroid(ri);

                        accountDetail.setFid(f.getId().toString());
                        accountDetail.setAccountId(f);
                        accountDetail.setAccount(ri.getId().toString());
                        accountDetail.setSource("1");
                        accountService.saveAccountDetail(accountDetail);

                        personnelReimburseService.updatePersonnelReimburse(personnelReimburse);
                        map.put("status", 1);//批准成功
                    }
                }
            }else {
                approvalProcess.setApproveStatus("8");
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setApproveMemo(approveMemo);
                approvalProcess.setUserName(user.getUserName());
                personnelReimburse.setApproveStatus("3");
                personnelReimburse.setApproveMemo(approveMemo);
                approvalProcessService.updateApprovalProcess(approvalProcess);
                personnelReimburseService.updatePersonnelReimburse(personnelReimburse);
                map.put("status",1);//成功
                fa=1;//发消息

            }
            for (UserMessage userMessage1:userMessageList) {
                if (approvalStatus == 1) {
                    userMessage1.setApprovalStatus(5); //消息中的审批状态为批准报销
                } else {
                    userMessage1.setApprovalStatus(8); //消息中的审批状态为驳回
                }
                userMessage1.setState(2);
                userMessage1.setHandleId(user.getUserID().toString());
                userMessage1.setHandleName(user.getUserName());
                userMessage1.setHandleReply(approveMemo);
                userMessage1.setHandleTime(new Date());
                if (fa == 1) {
                    userMessageService.updateUserMassage(userMessage1);
                }
            }
            //   批准/驳回后给申请者的消息
            UserMessage userMessage = new UserMessage();
            userMessage.setUser(personnelReimburse.getUser());
            if (approvalStatus==1){
                userMessage.setApprovalStatus(5);
            }else {
                userMessage.setApprovalStatus(8);
            }
            userMessage.setHandleId(user.getUserID().toString());
            userMessage.setMessageId(approvalProcess.getId());
            userMessage.setEventType("报销结果");
            userMessage.setIllustrate(personnelReimburse.getUser().getUserName()+"的报销申请");
            userMessage.setPersonnelReimburId(personnelReimburse.getId());
            userMessage.setMessageType("4");
            userMessage.setState(1);
            userMessage.setReceiveUserId(personnelReimburse.getUser().getUserID());  //接收消息人
            userMessage.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
            userMessage.setCreateDate(new Date());
            if (fa==1) {
                userMessageService.addUserMassage(userMessage);
            }

            //向申请人直系上级发消息
//            if (fa==1) {
//                User zhixi = userService.getUserByID(personnelReimburse.getUser().getUserID());
//                while (zhixi.getLeader() != null && !"0".equals(zhixi.getLeader())) {
//                    zhixi = userService.getUserByID(Integer.valueOf(zhixi.getLeader()));
//
//                    UserMessage zhixiMessage = new UserMessage();
//                    zhixiMessage.setUser(personnelReimburse.getUser());
//                    if (approvalStatus==1){
//                        zhixiMessage.setApprovalStatus(5);
//                    }else {
//                        zhixiMessage.setApprovalStatus(3);
//                    }
//                    zhixiMessage.setHandleId(approvalProcess.getToUser().toString());
//                    zhixiMessage.setMessageId(approvalProcess.getId());
//                    zhixiMessage.setEventType("报销结果");
//                    zhixiMessage.setIllustrate(personnelReimburse.getUser().getUserName()+"的报销申请");
//                    zhixiMessage.setPersonnelReimburId(personnelReimburse.getId());
//                    zhixiMessage.setMessageType("4");
//                    zhixiMessage.setState(1);
//                    zhixiMessage.setReceiveUserId(zhixi.getUserID());  //接收消息人
//                    zhixiMessage.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
//                    zhixiMessage.setCreateDate(new Date());
//                    zhixiMessage.setZhiXi("1");
//
//                    userMessageService.addUserMassage(zhixiMessage);
//                }
//            }
        }else {
            map.put("status",0);//批准失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

    }


    /**
    * <AUTHOR>
    * @Date 2018/3/7 10:09
    * 会计 科目选择  报销增值税
    */
    @ResponseBody
    @RequestMapping("/getReimburseBillByCState.do")
    public void getReimburseBillByCState(User user,HttpServletResponse response) throws IOException {
        Integer oid= user.getOid();
        List<FinanceReimburseBill> financeReimburseBillList=financeReimburseBillService.getFinanceReimburseBillByCertificationState(oid,"0");
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("reimburseBillList",financeReimburseBillList);
        ObjectToJson.objectToJson1(map,new String[]{"user","personnelReimbursetAttachmentHashSet","approvalProcessHashSet","financeReimburseBillHashSet","financeReimburseBillItemHashSet"},response);

    }

    /**
    * <AUTHOR>
    * @Date 2018/3/8 11:31
    * 会计 报销增值税票据详情
    */
    @ResponseBody
    @RequestMapping("/getReimburseBillInfo.do")
    public void  getReimburseBillInfo(Integer id,HttpServletResponse response) throws IOException {
        FinanceReimburseBill bill = financeReimburseBillService.getFinanceReimburseBillById(id);
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("bill",bill);
        ObjectToJson.objectToJson1(map,new String[]{"user","approvalProcessHashSet","financeReimburseBillHashSet","reimburseBill"},response);

    }

    /**
    * <AUTHOR>
    * @Date 2018/3/7 16:49
    * 会计 报销增值税认证
    */
    @ResponseBody
    @RequestMapping("/reimburseBillCertificationState.do")
    public void  reimburseBillCertificationState(Integer id,String state,HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (id!=null&&state!=null&&!"".equals(state)) {
            FinanceReimburseBill bill = financeReimburseBillService.getFinanceReimburseBillById(id);
            bill.setCertificationState(state);
            financeReimburseBillService.updateFinanceReimburseBill(bill);
            map.put("status", 1);//操作成功
        }else {
            map.put("status", 0);//操作失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    @ResponseBody
    @RequestMapping("/test.do")
    public void test(HttpServletResponse response) throws IOException {
        List<Code> codeList= codeService.getFirstCostCategoryList(410);
        ObjectToJson.objectToJson(codeList,new  String[]{"category"},response);
    }

}
