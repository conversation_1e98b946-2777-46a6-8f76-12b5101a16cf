package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.material.dao.MtStockInfoHistoryDao;
import cn.sphd.miners.modules.material.entity.MtStockInfoHistory;
import cn.sphd.miners.modules.material.service.MtStockInfoHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2018-01-20.
        */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MtStockInfoHistoryServiceImpl implements MtStockInfoHistoryService {
    @Autowired
    MtStockInfoHistoryDao mtStockInfoHistoryDao;
    @Override
    public void saveMtStockInfoHistory(MtStockInfoHistory mtStockInfoHistory) {
        mtStockInfoHistoryDao.save(mtStockInfoHistory);
    }

    public MtStockInfoHistory  get(MtStockInfoHistory m){
      return mtStockInfoHistoryDao.getByHQL("from MtStockInfoHistory m where m.initialStock="+m.getInitialStock()+" and m.afterInitialStock=" +m.getAfterInitialStock()+
                "  and m.initialStock="+m.getMinimumStock()+"  and m.afterMinimumStock="+m.getAfterMinimumStock()+" and m.material="+m.getMaterial());
    }

    @Override
    public List<MtStockInfoHistory> getMtStockInfoHistoryListByMaterial(Integer material) {

        return   mtStockInfoHistoryDao.getListByHQL("From MtStockInfoHistory m where m.material=?0",material);
    }




    @Override
    public List<Map> getMtSIHListByStockInfoIdForMap(Integer material,String state,Integer supplier) {

        String sql="SELECT s.id,s.material,s.after_initial_stock as afterInitialStock,s.initial_stock as initialStock,s.minimum_stock as minimumStock,s.after_minimum_stock as afterMinimumStock,s.create_name as createName,DATE_FORMAT(s.create_date,'%Y-%m-%d %H:%i:%S') as createDate,t.`name`,s.supplier\n" +
                "FROM t_mt_stock_info_history s\n" +
                "LEFT JOIN t_srm_supplier t\n" +
                "ON s.supplier=t.id\n" +
                "WHERE s.material="+material;
        if("2".equals(state)){
            sql+="  and s.after_initial_stock <> IFNULL(s.initial_stock,0)";
        }else if("1".equals(state)){
            sql+="  and IFNULL(s.minimum_stock,0) <> after_minimum_stock";
        }
        if(supplier!=null)
            sql+= " and t.id="+supplier;
        return mtStockInfoHistoryDao.getMtSIHListByStockInfoIdForMap(sql) ;
    }



}
