<template>
  <div id="willCleanFile">
    <TY_NavTop title="即将消失的文件"></TY_NavTop>
    <div class="container">
      <div class="nowTime">
        以下文件即将从系统中消失。<br>
        认为有用的文件，可在消失前还原。
      </div>
      <div class="container">
        <a class="ui-cell" @click="jump(item.id)" v-for="(item ,index) in toApprove" :key="index">
          <div class="fileLists">
            <div class="fileType file_xls"></div>
            <div class="fileInfo">
              <div class="fileName" v-text="item.name" :title="item.name"></div>
              <div class="fileDetail color-blue">{{item.fileValidTime}}</div>
            </div>
            <div class="fileFormat">
              <span class="fileVersion">G{{item.changeNum}}</span>
              <span class="fileNo">编号：{{item.fileSn}}</span>
            </div>
            <div class="blue">{{item.expriationTime}}</div>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .participantsNum{
    display: inline-block;
    width: 30px;
    height: 16px;
    vertical-align: top;
    margin-top: 2px;
    position: relative;
    text-align: center;
    background-color: #5badff;
    color: #fff;
    font-size: 12px;
    border-radius: 1px;
    margin-right: 6px;
    line-height:16px;
  }
  .blue{
    font-size: 1rem;
    color: #5badff;
    margin-left: 37px;
  }
</style>

<script>
  export default {
    name: 'forumApproval',
    data () {
      return {
        toApprove: [],
        listenersUid: []
      }
    },
    created () {
      let that = this
      this.axios.post('../../../res/getDelTrashFile.do')
        .then(function (response) {
          console.log(response)
          let getData = response.data.data
          that.toApprove = getData.list
        })
        .catch(function (error) {
          console.log(error)
        })
      this.listenersUid = [
        this.sphdSocket.subscribe('soonDisappearFile', function (data) {
          console.log('soonDisappearFile user:' + data)
          let getData = JSON.parse(data)
          let personnelReimburse = getData.res
          let operate = Number(getData.operate)
          if (operate > 0) {
            that.toApprove.unshift(personnelReimburse)
          } else if (operate < 0) {
            let reimburseId = personnelReimburse.id
            let _index = -1
            that.toApprove.forEach(function (item, index) {
              if (item.id === reimburseId) {
                _index = index
              }
            })
            if (_index > -1) {
              that.toApprove.splice(_index, 1)
            }
          }
        }, null, 'user')
      ]
      console.log(that.sphdSocket.org.id)
    },
    destroyed: function () {
      let _this = this
      this.listenersUid.forEach(function (item) {
        _this.sphdSocket.unsubscribe(item)
      })
    },
    methods: {
      jump: function (id) {
        this.$router.push({
          path: `/willCleanFileDetail/${id}`
        })
      }
    }
  }
</script>
