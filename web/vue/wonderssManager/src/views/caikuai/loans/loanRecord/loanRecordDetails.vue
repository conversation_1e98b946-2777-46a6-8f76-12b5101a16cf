<template>
  <!--  贷款修改记录详情-->
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true @mycloseFun="closeFun" :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14 " v-if="loanEditDetail.itemIndex>0">
      <p>以下为第{{ loanEditDetail.itemIndex }}次修改后数据，其中本次修改的被标为了<span style="color:#ed5565;">红色</span></p>
      <p> {{ loanEditDetail.createName }} <span class="padding-0-10">{{ new
      Date(loanEditDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>修改</p>
    </div>
    <div class="sticky ty-fff padding-0-15 h35f14" v-else>
      <p>以下为原始信息</p>
      <p> {{ loanEditDetail.createName }} <span class="padding-0-10">{{ new
      Date(loanEditDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>创建</p>
    </div>
    <div class="re margin_top_16" v-if="info">
      <div class="padding-0-15 ty-fff">
        <p>
          <span>本金金额</span>
          <span v-html="info.principalAmount" class="hH"></span>
        </p>
        <p>
          <span>出资方</span>
          <span v-html="info.loaner" class="hH"></span>
        </p>
        <p>
          <span>本金型式</span>
          <span v-html="info.incomeMethod" class="hH"></span>
        </p>
        <div v-if="info.incomeMethod==='现金'">
          <p>
            <span>收款日期</span>
            <span v-html="info.paymentDate" class="hH"></span>
          </p>
          <p>
            <span>收款经手人</span>
            <span v-html="info.partnerName" class="hH"></span>
          </p>
        </div>
        <div v-else-if="info.incomeMethod==='银行转账'">
          <p>
            <span>到账日期</span>
            <span class="hH" v-html="info.paymentDate"></span>
          </p>
          <p>
            <span>收款银行</span>
            <span class="hH" v-html="info.receiveBank"></span>
          </p>
        </div>
        <div v-else-if="info.incomeMethod==='转账支票'||info.incomeMethod==='承兑汇票'">
          <p>
            <span>收到{{info.incomeMethod === '转账支票' ? '支' : info.incomeMethod === '承兑汇票' ? '汇' : 'err'}}票日期</span>
            <span class="hH" v-html="info.receiveDate"></span>
          </p>
          <p>
            <span>{{info.incomeMethod === '转账支票' ? '支' : info.incomeMethod === '承兑汇票' ? '汇' : 'err'}}票号</span>
            <span class="hH" v-html="info.returnNo"></span>
          </p>
          <p>
            <span>票据金额</span>
            <span v-html="info.principalAmount" class="hH"></span>
          </p>
          <p>
            <span>{{info.incomeMethod === '转账支票' ? '支' : info.incomeMethod === '承兑汇票' ? '汇' : 'err'}}票到期日</span>
            <span v-html="info.expireDate" class="hH"></span>

          </p>
          <p>
            <span>{{info.incomeMethod === '转账支票' ? '出具支' : info.incomeMethod === '承兑汇票' ? '原始出具汇' : 'err'}}票单位</span>
            <span v-html="info.originalCorp" style="width: 60%;" class="hH"></span>

          </p>
          <p>
            <span>出具{{info.incomeMethod === '转账支票' ? '支' : info.incomeMethod === '承兑汇票' ? '汇' : 'err'}}票银行</span>
            <span v-html="info.bankName" style="width: 60%;" class="hH"></span>
          </p>
        </div>
        <p>
          <span>应归还本金的日期</span>
          <span v-html="info.repaymentDate"></span>
        </p>
        <p>
          <span>名义利率</span>
          <span v-html="info.nominalRate"></span>
        </p>
        <p>
          <span>利息的支付方式</span>
          <span v-html="info.interestMethodStr"></span>
        </p>
        <div v-if="info.interestMethod !== '0'">
          <p>
            <span>开始计息日期</span>
            <span v-html="info.interestAccrualDate"></span>
          </p>
          <p>
            <span>每{{ info.interestMethod === '1' ? '日' : info.interestMethod === '2' ? '月' : info.interestMethod === '3' ? '年' : '错误'
              }}还款日</span>
            <span v-html="info.periodRepaymentDate "></span>
          </p>
          <p>
            <span>每次应付款金额</span>
            <span v-html="info.periodRepaymentAmount"></span>
          </p>
        </div>
        <p>
          <span>备注</span>
          <span v-html="info.memo" class="hH" style="width: 70%"></span>
        </p>
      </div>
    </div>

  </div>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "loanRecordDetails",
  mixins: [finance],
  data() {
    return {
      tit: "财会",
      list: {},
      loanDetail: {},
      info: null,
      loanHistoryDetailCompare: {},
      loanEditDetail: {}
    }
  },
  computed: {
    itemIndex() {
      return this.$route.query.itemIndex
    },
  },
  mounted() {
    let loanEditDetail = localStorage.getItem('loanEditDetail')
    this.loanEditDetail = JSON.parse(loanEditDetail)
    console.log('loanEditDetail=', this.loanEditDetail)
    this.getEditHisDetails(this.loanEditDetail.id)
  },
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    compareWithUnit(before, after, unit = '') {
      const formattedBefore = this.formatCurrency(before) + unit
      const formattedAfter = this.formatCurrency(after) + unit
      return this.compareTxt(formattedBefore, formattedAfter)
    },
    getLoansDetails() {
      let params = {id: this.id, sourceType: 2}
      this.financeApi.ordLoanDetail(params).then(res1 => {
        this.loanDetail = res1.data.data.loanDetail
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('getLoansDetails链接失败，请重试！')
      })
    },
    getEditHisDetails(ID) {
      let params = {tBorrowCommonHistoryId: ID}
      this.financeApi.getLoanHistoryDetailCompare(params).then(res1 => {
        let res = res1.data.data
        let loanHistoryDetailCompare = res.loanHistoryDetailCompare
        let after = loanHistoryDetailCompare.after
        let before = loanHistoryDetailCompare.before
        console.log('after', after)
        console.log('before', before)
        let newInfo = {}
        if (before) {
          newInfo = {
            originalCorp: this.compareTxt(before.originalCorp, after.originalCorp),
            bankName: this.compareTxt(before.bankName, after.bankName),
            expireDate: this.compareTxt((new Date(before.expireDate).format('yyyy-MM-dd')), (new Date(after.expireDate).format('yyyy-MM-dd'))),
            returnNo: this.compareTxt(before.returnNo, after.returnNo),
            receiveDate: this.compareTxt((new Date(before.receiveDate).format('yyyy-MM-dd')), (new Date(after.receiveDate).format('yyyy-MM-dd'))),
            receiveBank: this.compareTxt(before.receiveBank, after.receiveBank),
            principalAmount: this.compareWithUnit(before.principalAmount, after.principalAmount, '元'),
            loaner: this.compareTxt(before.loaner, after.loaner),
            incomeMethod: this.chargePayMethod(after.incomeMethod),
            paymentDate: this.compareTxt((new Date(before.paymentDate).format('yyyy-MM-dd')), (new Date(after.paymentDate).format('yyyy-MM-dd'))),
            partnerName: this.compareTxt(before.partnerName, after.partnerName),
            repaymentDate: this.compareTxt((before.repaymentDate ? (new Date(before.repaymentDate).format('yyyy-MM-dd')) : '未约定具体日期'), (after.repaymentDate ? (new Date(after.repaymentDate).format('yyyy-MM-dd')) : '未约定具体日期')),
            nominalRate: this.compareWithUnit(((before.nominalRate * 100).toFixed(2)), ((after.nominalRate * 100).toFixed(2)), '%'),
            interestMethodStr: this.compareTxt(this.chargeInterestMethod(before.interestMethod), this.chargeInterestMethod(after.interestMethod)),
            interestMethod: after.interestMethod,
            memo: this.compareTxt(before.memo, after.memo),
            interestAccrualDate: this.compareTxt(new Date(before.interestAccrualDate).format('yyyy-MM-dd'), new Date(after.interestAccrualDate).format('yyyy-MM-dd')),
            periodRepaymentDate: this.compareTxt((before.periodRepaymentDate), (after.periodRepaymentDate)),
            periodRepaymentAmount: this.compareWithUnit(before.periodRepaymentAmount, after.periodRepaymentAmount, '元'),
          }
        } else {
          newInfo = {
            bankName: after.bankName,
            originalCorp: after.originalCorp,
            expireDate: (new Date(after.expireDate).format('yyyy-MM-dd')),
            returnNo: after.returnNo,
            receiveDate: (new Date(after.receiveDate).format('yyyy-MM-dd')),
            receiveBank: after.receiveBank,
            principalAmount: `${this.formatCurrency(after.principalAmount)}元`,
            loaner: after.loaner,
            borrower: after.borrower,
            incomeMethod: this.chargePayMethod(after.incomeMethod),
            paymentDate: new Date(after.paymentDate).format('yyyy-MM-dd'),
            partnerName: after.partnerName,
            repaymentDate: after.repaymentDate ? (new Date(after.repaymentDate).format('yyyy-MM-dd')) : '未约定具体日期',
            nominalRate: (after.nominalRate * 100).toFixed(2) + '%',
            interestMethodStr: this.chargeInterestMethod(after.interestMethod),
            interestMethod: after.interestMethod,
            memo: after.memo,
            lender: after.lender,
            interestAccrualDate: new Date(after.interestAccrualDate).format('yyyy-MM-dd'),
            periodRepaymentDate: after.periodRepaymentDate,
            periodRepaymentAmount: `${this.formatCurrency(after.periodRepaymentAmount)}元`,
          }
        }
        this.info = newInfo
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('getEditHisDetails链接失败，请重试！')
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";


.turnHang {
  display: inline-block;
  white-space: normal;
  line-height: 20px;
  max-width: 60%;
}

.loan {
  .fa-angle-right {
    font-size: 20px;
  }
}

.re {
  position: relative;
  top: 40px;
}

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.re {
  position: relative;
  top: 40px;

  > div:nth-child(1) {
    line-height: 30px;

    > p {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
    }

    > div {
      > p {
        display: flex;
        justify-content: space-between;
        padding: 2px 0;
      }
    }
  }
}

.box {
  background-color: $tyColorBg;
}

.hH {
  width: 70%;
  text-align: end;
}
</style>
