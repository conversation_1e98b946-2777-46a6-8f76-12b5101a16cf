<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showBack=true :showClose=true
                  @mycloseFun="handleCustomClose"
      ></PageHeader>
    </div>
    <div class="sticky ty-fff spaceBetween padding-0-15 h35f14">
      <p>
        <span>{{ loanDetail.createName }}</span>
        <span class="padding-0-10">{{ new
        Date(loanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>创建
      </p>
      <span style="color:#337ab7;" @click="rou()">修改记录</span>
    </div>
    <div class="re margin_top_16">
      <div class="padding-0-15 ty-fff">
        <p>
          <span>本金金额</span>
          <span class="hH">{{ this.formatCurrency(loanDetail.principalAmount) }}元</span>
        </p>
        <p>
          <span>收款方</span>
          <span class="hH">{{ loanDetail.borrower }}</span>
        </p>
        <p>
          <span>本金型式</span>
          <span v-if="loanDetail.incomeMethod==='1'">现金</span>
          <span v-else-if="loanDetail.incomeMethod === '3' && loanDetail.withinOrAbroad==='1'">内部转账支票</span>
          <span v-else-if="loanDetail.incomeMethod === '3' && loanDetail.withinOrAbroad==='0'">外部转账支票</span>
          <span v-else-if="loanDetail.incomeMethod === '4'">承兑汇票</span>
          <span v-else-if="loanDetail.incomeMethod === '5'">银行转账</span>
        </p>
        <p v-if="loanDetail.incomeMethod==='1'|| loanDetail.incomeMethod==='5'">
          <span>付款日期</span>
          <span class="hH">{{ new
          Date(loanDetail.paymentDate).format('yyyy-MM-dd') }}</span>
        </p>
        <div v-if="loanDetail.incomeMethod==='5'">
          <p>
            <span>付款银行</span>
            <span class="hH">{{ loanDetail.receiveBank }}</span>
          </p>
          <p>
            <span>付款经手人</span>
            <span class="hH">{{ loanDetail.operatorName }}</span>
          </p>
          <p>
            <span>借款方账号名称</span>
            <span>{{ loanDetail.oppositeAccount }}</span>
          </p>
          <p>
            <span>开户行</span>
            <span>{{ loanDetail.oppositeBankno }}</span>
          </p>
          <p>
            <span>账号</span>
            <span>{{ loanDetail.oppositeBankcode }}</span>
          </p>
        </div>
        <div v-else-if="loanDetail.incomeMethod === '3'|| loanDetail.incomeMethod==='4'">
          <p v-if="loanDetail.withinOrAbroad==='1'">
            <span>银行账户</span>
            <span>{{ loanDetail.account }}</span>
          </p>
          <p>
            <span>{{loanDetail.incomeMethod === '3' ? '支' : loanDetail.incomeMethod === '4' ? '汇' : 'err'}}票号</span>
            <span>{{ loanDetail.billNo }}</span>
          </p>
          <p v-if="loanDetail.withinOrAbroad==='1'">
            <span>支票到期日</span>
            <span>{{ loanDetail.billEndDate }}</span>
          </p>
          <p>
            <span>付款日期</span>
            <span class="hH">{{ new
            Date(loanDetail.paymentDate).format('yyyy-MM-dd') }}</span>
          </p>
        </div>
        <div v-if="loanDetail.incomeMethod!=='5'">
          <p>
            <span>付款经手人</span>
            <span class="hH">{{ loanDetail.operatorName }}</span>
          </p>
          <p>
            <span>收款经手人</span>
            <span class="hH">{{ loanDetail.partnerName }}</span>
          </p>
        </div>
        <p>
          <span>应归还本金的日期</span>
          <span v-if="loanDetail.repaymentMethod==='1'">未约定具体日期</span>
          <span v-if="loanDetail.repaymentMethod==='2'">{{ new
          Date(loanDetail.repaymentDate).format('yyyy-MM-dd') }}</span>
        </p>
        <p>
          <span>名义利率</span>
          <span>{{ ((loanDetail.nominalRate) * 100).toFixed(2) }}%</span>
        </p>
        <p>
          <span>利息的支付方式</span>
          <span v-if="loanDetail.interestMethod==='0'">无利息</span>
          <span
              v-if="loanDetail.interestMethod!=='0'">按{{ loanDetail.interestMethod === '1' ? '日' : loanDetail.interestMethod === '2' ? '月' : loanDetail.interestMethod === '3' ? '年' : '错误'
            }}付</span>
        </p>
        <div v-if="loanDetail.interestMethod!=='0'">
          <p>
            <span>开始计息日期</span>
            <span>{{ new
            Date(loanDetail.interestAccrualDate).format('yyyy-MM-dd') }}</span>
          </p>
          <p>
            <span>每{{ loanDetail.interestMethod === '1' ? '日' : loanDetail.interestMethod === '2' ? '月' : loanDetail.interestMethod === '3' ? '年' : '错误'
              }}还款日</span>
            <span>{{ loanDetail.periodRepaymentDate }}</span>
          </p>
          <p>
            <span>每次应付款金额</span>
            <span>{{ this.formatCurrency(loanDetail.periodRepaymentAmount) }}元</span>
          </p>
        </div>
        <p>
          <span>备注</span>
          <span class="hH" style="width: 70%">{{ loanDetail.memo }}</span>
        </p>
      </div>
      <p class="ty-fff spaceBetween padding-0-15 h35f14 margin_top_16">
        <span>已收款金额</span>
        <span>{{ sumRepaymentAmount }}元</span>
        <span style="color:#337ab7;" @click="pay()">收款记录</span>
      </p>
      <div class="loansBottom">
        <div v-for="(item,index) in recordList" :key="index">
          <div class="hr_gray"></div>
          <p class="ty-fff recordList">{{ item.createName }}于
            {{ new
            Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}
            将本条数据{{ item.state === true ? "由“已完结”恢复至“尚需继续付款”。" : item.state === false ? "操作为“已完结”。" : 'err'}}</p>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import {defineComponent} from "vue";
import finance from '@/mixins/finance.js'

export default defineComponent({
  name: "borrowMoneyDetails",
  mixins: [finance],
  computed: {
    id() {
      return this.$route.query.id
    },
  },
  data() {
    return {
      tit: "财会",
      loanDetail: {},
      recordList: [],
    }
  },
  mounted() {
    this.getLoansDetails()
  },
  methods: {
    rou() {
      this.$router.push({
        path: '/bmRecordList',
        query: {id: this.id}

      })
    },
    getLoansDetails() {
      let params = {id: this.id, sourceType: 2}
      this.financeApi.ordLoanDetail(params).then(res1 => {
        this.loanDetail = res1.data.data.loanDetail
        this.recordList = res1.data.data.recordList
        this.sumRepaymentAmount = this.formatCurrency(res1.data.data.sumRepaymentAmount)
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },

    pay() {
      if (this.sumRepaymentAmount === 0) {
        this.$message({
          message: '暂无收款记录！',
          offset: 300 // 设置偏移量为300px
        });
      } else {
        this.$router.push({path: '/bmPayRecordList',query:{id:this.id}})
      }
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    }
  },
})
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";


.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.hH {
  width: 60%;
  text-align: end;
  line-height: 22px;
}

.re {
  position: relative;
  top: 40px;

  > div:nth-child(1) {
    line-height: 30px;

    > p, > div > p {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
      align-items: center;
    }
  }
}

.recordList {
  padding: 6px 15px;
  word-break: break-all;
  white-space: normal;
  line-height: 25px;
}

.hr_gray {
  width: 100%;
  height: 15px;
  background-color: #eee;
}
</style>