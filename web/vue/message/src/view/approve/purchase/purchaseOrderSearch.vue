<template>
  <div id="purchaseOrderSearch" >
    <TY_NavTop :title="title" isButton="true" @toggleSubmit="submit"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">  您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">供应商</h4>
              <div class="el-form-item">
                <span :class="queryForm.applier === 0?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <div>
              <h4 class="item-header">时间选项</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <hr>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approvalStatus" size="small">
                  <el-radio label="2" border>已批准</el-radio>
                  <el-radio label="3" border>已驳回</el-radio>
                  <el-radio v-if="searchType == 0" label="4" border>已撤回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <hr>
            <div id="resonCon" v-if="queryForm.approvalStatus == 3">
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.rejectReasion" size="small">
                  <el-radio label="" border style="float: right">全部</el-radio>
                  <h4 class="item-header">驳回理由</h4>
                  <div>
                    <el-radio label="1" border>供应商价格贵</el-radio>
                    <el-radio label="2" border>供应商质量不稳定</el-radio><br/>
                    <el-radio label="3" border>供应商服务态度不好</el-radio>
                    <el-radio label="4" border>供应商无法确保准时交货</el-radio><br/>
                    <el-radio label="5" border>付款方式不合理</el-radio>
                    <el-radio label="6" border>暂无需采购</el-radio><br/>
                    <el-radio label="7" border>发票原因</el-radio>
                    <el-radio label="8" border>其他原因</el-radio><br/>
                  </div>
                </el-radio-group>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>
<style lang="less">
#purchaseOrderSearch{
  hr{ color:#fff; }
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  .el-radio{  display: inline-block; width: 75px;  text-align: center;}
  #resonCon{
    .el-radio{
      display: inline-block; width: 150px; margin-right: 10px; padding-left: 0; padding-right: 0; text-align: center; margin-top: 5px;
      .el-radio__label{ padding-left: 0; }
    }
  }

  .el-radio__input, #reimburseApplyQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
}
</style>

<script>
export default {
  name: 'purchaseOrderSearch',
  data () {
    return {
      loading: true,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approvalStatus: '2',
        rejectReasion: '',
        applier: 0,
        applyName: '请选择'
      },
      billCat: [],
      secondFeeCatList: [],
      roleList: [],
      bookVisible: false,
      title: '采购申请',
      searchType: 0,
      type: 0
    }
  },
  created: function () {
    let that = this
    that.searchType = this.$route.params.type // 0-申请者；1-审批者
    that.axios.post('../../../purchaseOrderApproval/getPurchaseSuppliers.do', { oid: this.sphdSocket.org.id }, {
      emulateJSON: true
    }).then((response) => {
      console.log('供应商返回值：' + response)
      that.loading = false
      let data = response.data
      let supList = data.data
      that.roleList = []
      supList.forEach(function (item) {
        let supI = { 'userName': item.supplierName, 'mobile': '', 'userId': item.supplier }
        that.roleList.push(supI)
      })
      console.log(that.roleList)
    })
  },
  methods: {
    submit: function () {
      console.log(JSON.stringify(this.queryForm))
      let approveStatus = Number(this.queryForm.approvalStatus)
      let queryParam = {
        approvalStatus: approveStatus,
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        type: this.queryForm.type,
        supplier: this.queryForm.applier,
        supplierName: this.queryForm.applyName,
        rejectReasion: this.queryForm.rejectReasion,
        searchType: this.searchType
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/purchaseOrderSearchList'
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.applier = 0
        this.queryForm.applyName = '请选择'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.applier = data.userId
      this.queryForm.applyName = data.userName
    }
  }
}
</script>
