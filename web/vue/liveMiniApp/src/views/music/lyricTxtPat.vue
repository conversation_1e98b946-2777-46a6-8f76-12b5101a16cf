<template>
  <div class="lyricTxtPat">
    <Pageheader :title="txtTitle" :showBack="true" :showClose="true" :runMyBackFun="true" @mycloseFun="cancelAdd" @myBackFun="goback"/>
    <div class="live-container">
      <div>
        <AudioC :fileUrl="fileUrl" ref="Lyc" :playName="playName"></AudioC>
      </div>
      <div class="txtPatC" v-if="!setTxt">
        <div class="marbtm30">
          <el-button type="text" class="">操作说明</el-button>
          <el-button type="text" class="live-right" @click="clearTxt">清空</el-button>
          <el-button type="text" class="live-right" @click="clipboard">获取剪切板</el-button>
        </div>
        <el-input type="textarea" :autosize="{ minRows: 8, maxRows:20}" placeholder="请输入内容" v-model="txtPat"></el-input>
        <div class="marTop">
          <span @click="goPat" class="primary live-button">配节奏</span>
        </div>
      </div>
      <div class="txtPatC" v-if="setTxt">
        <div class="kuangCon" ref="kuangCon">
          <div class="alltxtCon" ref="alltxtCon" :style="{ top: topStr  }">
            <div ref="blueTxt" v-html="blueTxtStr" class="blueTxt"></div>
            <div ref="grayTxt" v-html="grayTxtStr" class="grayTxt"></div>
          </div>
        </div>
        <div class="marTop live-bottomDiv">
          <span @click="startPat" v-if="!pating" class="primary live-button">开始</span>
          <span @click="pat" v-if="pating" class="primary live-button">敲击</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox } from 'element-plus';
import { ElMessage } from 'element-plus';
import JSONBig from 'json-bigint'

const router = useRouter();
const playName = ref('')
const fileUrl = ref('')
const txtPat = ref('')
const txtTitle = ref('')
const blueTxtStr = ref('')
const grayTxtStr = ref('')
const topStr = ref(0)
const setTxt = ref(false)
const pating = ref(false)
const isSing = ref(false)
const lyricArr = ref([])

const Lyc = ref(null)
const kuangCon = ref(null)
const blueTxt = ref(null)
const grayTxt = ref(null)

onMounted(() => {
  let tipPage = localStorage.getItem('tipPage')
  txtTitle.value = tipPage === 'xiqu' ? '戏词制作' : '歌词制作'
  let newlycdata = JSONBig.parse(localStorage.getItem('newlycdata'));
  let agreeObj = JSONBig.parse(localStorage.getItem('agreeObj'));
  let lycTxtObj = JSONBig.parse(localStorage.getItem('lycTxtObj'));
  let uploadUrl = localStorage.getItem('uploadUrl');
  console.log('newlycdata ===', newlycdata)
  playName.value = newlycdata.name
  if(lycTxtObj.rhythmOption === '1'){
    fileUrl.value = uploadUrl + newlycdata.orlLyc.filePath
  }else if(lycTxtObj.rhythmOption === '2'){
    fileUrl.value = uploadUrl + newlycdata.banLyc.filePath
  }else{
    fileUrl.value = uploadUrl + (newlycdata.banLyc.filePath || newlycdata.orlLyc.filePath)
  }
  console.log(' fileUrl.value ==',  fileUrl.value )
  console.log(' agreeObj==',  agreeObj )
  if(agreeObj.agreeOrNot === '1'){ // 同意从剪切板弄的
    clipboard()
  }

});

// 取消新增
const cancelAdd = () => {
  console.log('cancelAdd')
  let lyricInfo = JSONBig.parse(localStorage.getItem('newlycdata'));
  let from = lyricInfo.from
  localStorage.removeItem('newlycdata')
  localStorage.removeItem('patData')
  localStorage.removeItem('agreeObj')
  localStorage.removeItem('lycTxtObj')
  localStorage.removeItem('assignTasks')
  localStorage.removeItem('staffList')
  if(from === 'lycTaskHandel'){
    router.push({ name: 'lycTaskHandel' });
  }else{
    router.push({ name: 'lyricTask', });
  }
}
const goback = () => {
  router.push({
    name: 'lyricTip',
  });
}
const goPat = () => {
  setTxt.value = true
  let showTxt =  txtPat.value
  let showTxtP = ''
  let showTxtArr = showTxt.split(/[\n\r]/g)
  console.log('showTxtArr=', showTxtArr)
  showTxtArr.forEach( item => {
    item = item.trim()
    if(item.length != 0){
      showTxtP += `<p>${ item }</p>`
    }
  })
  console.log('showTxtP=', showTxtP)
  grayTxtStr.value = showTxtP
  blueTxtStr.value = ''

}
const startPat = () => {
  pating.value = true
  isSing.value = !isSing.value
  console.log('调用 Lyc.audioPla 传参= ', isSing.value)
  console.log('Lyc=', Lyc)
  Lyc._rawValue.audioPlay(isSing.value );
  lyricArr.value = []
}
const pat = () => {
  let cur = Lyc._rawValue.getCurrent(true)
  let curStr = Lyc._rawValue.getCurrent()
  let curP = grayTxt._value.firstChild
  if(curP){
    console.log('curP = ', curP)
    let curPTxt = curP.innerHTML
    let curPTxt2 = curP.innerHTML
    blueTxtStr.value += `<p>${ curPTxt }</p>`
    curP.remove()
    autoMove()
    // 记录数据
    lyricArr.value.push({
      time: cur,
      timeStr: curStr,
      txt:curPTxt
    })
    // 判断是否完成，完成了就跳转下一页
    curP = grayTxt._value.firstChild
    if(!curP){ // 歌词配完了，跳页
      compliteLyc()
    }
  } else{
    compliteLyc()
  }
}

const compliteLyc = () => {
  ElMessage({
    type: 'info',
    message: '全部歌词已配完！',
  });
  let lastTimeStr = Lyc._rawValue.getDuration()
  let patData = {
    lastTimeStr:lastTimeStr,
    lyricArr: lyricArr.value,
    txtPat: txtPat.value,
    endType:'1',
    pageType: localStorage.getItem('tipPage')
  }
  console.log('patData=', patData)
  localStorage.setItem('patData', JSONBig.stringify(patData))
  router.push({
    name: 'lyricTxtPatSave',
  });
}

const autoMove = () => {
  const kua = kuangCon._value.offsetTop
  const grayH = grayTxt._value.offsetTop
  // console.log('kuangCon = ', kuangCon)
  // console.log('grayTxt = ', grayTxt)
  const kuaJ = kua + 100 ;
  // console.log('kua=', kua)
  console.log('grayH=', grayH)
  console.log('kuaJ=', kuaJ)
  let cha = ''
  if(grayH > kuaJ){
    cha = grayH - kuaJ
    topStr.value = `-${ cha }px`
  }
  console.log('topStr.value=', topStr.value)
}
const clearTxt = () => {
  txtPat.value = "";
}
const clipboard = () => {
  navigator.clipboard
      .readText()
      .then((v) => {
        console.log("获取剪贴板成功：", v);
        // this.$confirm('监测到剪切板的内容, 是否填入歌词数据?', '提示', {
        ElMessageBox.confirm('是否将读取到的剪切板内容填入歌词?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
            .then(() => {
              txtPat.value = v;
              // ElMessage({
              //   type: 'success',
              //   message: '删除成功!',
              // });
            })
            .catch(() => {
              // ElMessage({
              //   type: 'info',
              //   message: '已取消删除',
              // });
            });

      })
      .catch((v) => {
        console.log("获取剪贴板失败: ", v);
      });

}
</script>
<style lang="scss">
.lyricTxtPat{
  .txtPatC{
    margin:40px 0;
  }
  .marbtm30{
    margin-bottom: 15px;
  }
  .marTop{
    margin:20px 0;
    padding-bottom: 30px;
  }
  .kuangCon{
    height: 400px;
    overflow: auto;
    border: 1px solid #ccc;
    position: relative;
    padding: 0 16px;
    border-radius: 4px;
    .alltxtCon{
      position: absolute;
    }
    .blueTxt{
      color:$livePageHeadBg ;
    }
    .grayTxt{
    }
  }
}
</style>
