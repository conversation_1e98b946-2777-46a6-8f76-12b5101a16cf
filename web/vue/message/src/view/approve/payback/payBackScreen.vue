<template>
  <div id="applyFileQuery">
    <TY_NavTop title="回款处置"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tip">您可查询到一年内的回款记录，请确定查询条件。</div>
          <el-form :model="queryForm" ref="queryForm" label-width="70px" size="mini">
            <el-form-item label="录入时间">
              <el-select v-model="queryForm.inputVal" placeholder="—— ——" @change="clearOther('acceptTime')">
                <el-option
                  v-for="item in inputTime"
                  :key="item"
                  :label="item.inputLabel"
                  :value="item.inputVal">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="受理时间">
              <el-select v-model="queryForm.acceptVal" placeholder="—— ——" @change="clearOther('inputTime')">
                <el-option
                  v-for="item in inputTime"
                  :key="item"
                  :label="item.inputLabel"
                  :value="item.inputVal">
                </el-option>
              </el-select>
            </el-form-item>
            <p class="hrGap"></p> <!--1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐'-->
            <el-form-item label="收入方式">
              <el-select v-model="income" placeholder="全部">
                <el-option label="全部" value=""></el-option>
                <el-option label="现金" value="1"></el-option>
                <el-option label="银行转账" value="5"></el-option>
                <el-option label="转账支票" value="3"></el-option>
                <el-option label="承兑汇票" value="4"></el-option>
              </el-select>
            </el-form-item>
            <p class="hrGap"></p>
            <el-form-item label="录入者">
              <el-select v-model="queryForm.inputerVal" placeholder="全部">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="it in inputer"
                           :key="it"
                           :label="it.userName"
                           :value="it.userID">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="fc-btn ui-btn_info" @click="screenCancel" value="取消">
            <input type="submit" :class="{'fc-btn ui-btn_info': !btnControl, 'fd-btn ui-btn_disabled': btnControl}" :disabled="btnControl" value="确定" @click="screenEnd()">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, getMonthList } from '../../../js/common'
export default {
  name: 'payApproveScreen',
  filters: {
    formatDate
  },
  data () {
    return {
      income: '',
      ssTitle: '',
      queryForm: {
        inputVal: '',
        acceptVal: '',
        inputerVal: ''
      },
      inputer: [],
      inputTime: [],
      loading: false,
      endParams: {}
    }
  },
  computed: {
    btnControl () {
      return this.queryForm.inputVal === '' && this.queryForm.acceptVal === ''
    }
  },
  created () {
    let that = this
    that.inputTime = getMonthList(12)
    that.queryForm.inputVal = formatDate(new Date(), 'yyyy-MM')
    this.$http.post('../../../salesBack/getEntryUser.do', {}, {
      emulateJSON: true
    }).then((response) => {
      this.inputer = response.body.list
    }).catch(function () {
      this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  },
  methods: {
    clearOther: function (type) {
      if (type === 'inputTime') {
        this.queryForm.inputVal = ''
      } else if (type === 'acceptTime') {
        this.queryForm.acceptVal = ''
      }
    },
    screenEnd: function () {
      let param = {
        'type': '3',
        'applyDate': this.queryForm.inputVal,
        'method': this.income,
        'approvalDate': this.queryForm.acceptVal,
        'applyUserId': this.queryForm.inputerVal,
        'applyUserName': ''
      }
      if (this.queryForm.inputerVal !== '') {
        for (let t in this.inputer) {
          if (this.queryForm.inputerVal === this.inputer[t].userID) {
            param.applyUserName = this.inputer[t].userName
          }
        }
      }
      param = JSON.stringify(param)
      localStorage.setItem('collectParams', param)
      this.$router.push({
        path: 'collectShList'
      })
    },
    screenCancel: function () {
      let floatingUrl = window.localStorage.getItem('floating')
      if (floatingUrl) {
        floatingUrl = floatingUrl.split('-')
        if (floatingUrl.length > 0) {
          floatingUrl.pop()
          if (floatingUrl.length > 0) {
            this.$router.push({
              path: floatingUrl[floatingUrl.length - 1]
            })
          } else {
            this.$router.push({
              path: '/'
            })
          }
          floatingUrl.pop()
          window.localStorage.setItem('floating', floatingUrl.join('-'))
        } else {
          this.$router.push({
            path: '/'
          })
        }
      }
    }
  }
}
</script>
