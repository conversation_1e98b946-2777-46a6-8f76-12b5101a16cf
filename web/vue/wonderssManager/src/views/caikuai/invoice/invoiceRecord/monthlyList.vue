<template>
  <div>
    <p class="h35f14 f4 margin_top_16">
      <span>按月份展示如下：</span>
      <span @click="Hide()" style="color:#337ab7;">切换为按发票类别展示</span>
    </p>
    <ul class="ty-fff">
      <li v-for="(item,index) in list.monthList" :key="index" class="flex" :class="{ 'f4': index % 2 !== 0 }"
          @click="rou(item)">
        <p>{{ item.monthYear }}</p>
        <p>
          <span>{{ item.allInvoice.num }}张</span>
          <span>{{ item.allInvoice.totalAmount }}元</span>
        </p>
        <div>
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </li>
    </ul>

  </div>
</template>

<script>
import {getInvoiceStatistics} from '@/api/financeBill'

export default {
  name: "monthlyList",
  data() {
    return {
      list: [],
    }
  },
  methods: {
    rou(item) {
      const [year, month] = item.monthYear.split("-");
      const beginDate = new Date(year, month - 1, 1).format('yyyy-MM-dd');
      const endDate = new Date(year, month, 0).format('yyyy-MM-dd');
      let invoiceDetailsList = {
        type: '',
        beginDate: beginDate, endDate: endDate
      }
      localStorage.setItem('invoiceDetailsList', JSON.stringify(invoiceDetailsList))
      this.$router.push('/monthlyInvoiceList')
    },
    Hide() {
      this.$router.push('/category')
    },
    getList(invoiceList) {
      if (invoiceList.index == 0) {
        this.beginTime = new Date(new Date().getFullYear(), 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 1) {
        this.beginTime = new Date(new Date().getFullYear() - 1, 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 2) {
        this.beginTime = new Date(new Date().getFullYear() - 2, 0, 1).format('yyyy-MM-dd');
      } else if (invoiceList.index == 3) {
        this.beginTime = new Date(this.invoiceList.result, 0, 1).format('yyyy-MM-dd');
      }
      const params = {beginDate: this.beginTime, typeMonth: invoiceList.typeMonth, dateType: invoiceList.dateType}

      getInvoiceStatistics(params).then(res1 => {
        this.list = res1.data.data
        console.log(res1, 'res1.data.data')
      }).catch(error => {
        console.error('Error:', error);
      });
    }
  },
  mounted() {
    let invoiceList = localStorage.getItem('invoice');
    this.invoiceList = JSON.parse(invoiceList);
    console.log(this.invoiceList,'this.invoiceList')
    this.getList(this.invoiceList)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

ul {
  > li {
    display: flex;
    line-height: 22px;
    padding: 8px 15px;
    justify-content: space-between;

    > :nth-child(1) {
      width: 25%;
    }

    > :nth-child(2) {
      width: 50%;
      text-align: left;
      display: flex;
      justify-content: space-between;

      > span {
        width: 48%;
        text-align: end;
        word-break: break-all;
        white-space: normal;
      }
    }

    > div:last-child {
      width: 10%;
      display: flex;
      justify-content: end;
    }
  }
}

.h35f14 {
  display: flex;
  justify-content: space-between;
  padding: 2px 15px;
}
</style>