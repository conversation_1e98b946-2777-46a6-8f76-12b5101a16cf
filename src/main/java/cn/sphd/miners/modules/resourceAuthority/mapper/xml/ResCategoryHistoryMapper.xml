<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResCategoryHistoryMapper">

    <select id="getSingle" parameterType="resCatHis" resultType="resCatHis">
        select
        id, resource_category AS resourceCategory, org, org_name AS orgName, parent, type, category, valid,
        max_child_categories AS maxChildCategories, children, descendant, leafs, path, size,
        name, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor,  auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category_history
        where id = #{id}
    </select>

    <select id="listPage"  parameterType="hashmap" resultType="resCatHis" >
        select
        id, resource_category AS resourceCategory, org, org_name AS orgName, parent, type, category, valid,
        max_child_categories AS maxChildCategories, children, descendant, leafs, path, size,
        name, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor,  auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category_history
    </select>

    <delete id="delete" parameterType="resCatHis" >
        delete from t_resource_category_history
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="resCatHis" >
        insert into t_resource_category_history (id, resource_category, org,
        org_name, parent, type,
        category, valid, max_child_categories,
        children, descendant, leafs, path,
        size, name, memo, orders,
        creator, create_name, create_date,
        updator, update_name, update_date,
        approve_item, approve_status, approve_level,
        auditor, auditor_name, audit_date,
        operation, apply_memo, approve_memo,
        message_id, previous_id, version_no)
        values (#{id}, #{resourceCategory}, #{org},
        #{orgName}, #{parent}, #{type},
        #{category}, #{valid}, #{maxChildCategories},
        #{children}, #{descendant}, #{leafs}, #{path},
        #{size}, #{name}, #{memo}, #{orders},
        #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate},
        #{approveItem}, #{approveStatus}, #{approveLevel},
        #{auditor}, #{auditorName}, #{auditDate},
        #{operation}, #{applyMemo}, #{approveMemo},
        #{messageId}, #{previousId}, #{versionNo})
    </insert>

    <update id="update" useGeneratedKeys="true" keyProperty="id" parameterType="resCatHis" >
        update t_resource_category_history
        <set >
            <if test="resourceCategory != null" >
                resource_category = #{resourceCategory},
            </if>
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="orgName != null" >
                org_name = #{orgName},
            </if>
            <if test="parent != null" >
                parent = #{parent},
            </if>
            <if test="type != null" >
                type = #{type},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="valid != null" >
                valid = #{valid},
            </if>
            <if test="maxChildCategories != null" >
                max_child_categories = #{maxChildCategories},
            </if>
            <if test="children != null" >
                children = #{children},
            </if>
            <if test="descendant != null" >
                descendant = #{descendant},
            </if>
            <if test="leafs != null" >
                leafs = #{leafs},
            </if>
            <if test="size != null" >
                size = #{size},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="orders != null" >
                orders = #{orders},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="previousId != null" >
                previous_id = #{previousId},
            </if>
            <if test="versionNo != null" >
                version_no = #{versionNo},
            </if>
            <if test="path != null" >
                path = #{path},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="categoryHisList" resultType="resCatHis">
        select
        id, resource_category AS resourceCategory, org, org_name AS orgName, parent, type, category, valid,
        max_child_categories AS maxChildCategories, children, descendant, leafs, path, size,
        name, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor,  auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category_history
        where  resource_category = #{id}
    </select>

</mapper>