package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUserDetail;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.AttendanceType;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.WorkdayType;
import org.apache.commons.lang3.SerializationUtils;

import java.util.*;

/**
 * WorkOffBlockDto_in_generalAffairs 考勤时间块
 * @author: wuyu
 * @since: 4.0
 * @date: 2025-04-27 15:56:03
 **/
public class WorkOffBlockDto extends BaseEntity {
    private WorkdayType type;
    private AttendanceType attendanceType;
    private Integer detailId;
    private String businessService;
    private Integer business;
    private Date begin;
    private Date end;
    private Date updateDate;

    public WorkdayType getType() {
        return type;
    }

    public void setType(WorkdayType type) {
        this.type = type;
    }

    public AttendanceType getAttendanceType() {
        return attendanceType;
    }

    public void setAttendanceType(AttendanceType attendanceType) {
        this.attendanceType = attendanceType;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getBusinessService() {
        return businessService;
    }

    public void setBusinessService(String businessService) {
        this.businessService = businessService;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public Date getBegin() {
        return begin;
    }

    public void setBegin(Date begin) {
        if(end!=null && begin!=null && end.before(begin)){
           this.begin = end;
           this.end = begin;
        } else {
            this.begin = begin;
        }
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        if(begin!=null && end!=null && begin.after(end)){
            this.end = begin;
            begin = end;
        } else {
            this.end = end;
        }
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public WorkOffBlockDto() {
    }

    public WorkOffBlockDto(PersonnelAttendanceUserDetail detail) {
        this.type = WorkdayType.getByIndex(detail.getWorkdayType());
        this.attendanceType = AttendanceType.getByName(detail.getType());
        this.detailId = detail.getId();
        this.begin = detail.getBeginTime();
        this.end = detail.getEndTime();
        this.updateDate = detail.getUpdateDate();
    }
    public WorkOffBlockDto(WorkdayType type, String businessService, Integer business, Date begin, Date end, Date updateDate) {
        this.type = type;
        this.attendanceType = AttendanceType.getByBusinessService(businessService);
        this.businessService = businessService;
        this.business = business;
        this.begin = begin;
        this.end = end;
        this.updateDate = updateDate;
    }
    public Date GetBegin() {
        return begin != null ? begin : NewDateUtils.getMaxDate();
    }
    public Date GetEnd() {
        return end != null ? end : NewDateUtils.getMinDate();
    }

    public static List<WorkOffBlockDto> getListFromDetailSet(Set<PersonnelAttendanceUserDetail> detailSet, List<AttendanceType> filterTypes, List<WorkdayType> workdayTypes) {
        List<String> types = filterTypes.stream().map(AttendanceType::getName).toList();
        List<Byte> workdays = workdayTypes.stream().map(WorkdayType::getIndex).toList();
        return detailSet.stream().filter(detail -> workdays.contains(detail.getWorkdayType()) && types.contains(detail.getType())).map(WorkOffBlockDto::new).toList();
    }

    public static List<Integer> merge(List<WorkOffBlockDto> list) {
        list = new ArrayList<>(list.stream().sorted(Comparator.comparing(WorkOffBlockDto::GetBegin)).toList());
        WorkOffBlockDto current, next;
        Iterator<WorkOffBlockDto> iterator = list.iterator();
        List<Integer> removeDetailIds = new ArrayList<>();
        if(iterator.hasNext()) {
            current = iterator.next();
            while (iterator.hasNext()) {
                next = iterator.next();
                if (!current.GetEnd().before(next.GetBegin())) {
                    if (next.GetEnd().after(current.GetEnd())) {
                        current.setEnd(next.GetEnd());
                    }
                    if(current.detailId==null){
                        current.setDetailId(next.detailId);
                    } else if(next.detailId!=null){
                        removeDetailIds.add(next.detailId);
                    }
                    iterator.remove();
                }
            }
        }
        return removeDetailIds;
    }
    public static List<Integer> mergeWithLimit(List<WorkOffBlockDto> list, Date begin, Date end) {
        list.forEach(item->{
            if(item.GetBegin().before(begin)) {
                item.setBegin(begin);
            }
            if(item.GetEnd().after(end)) {
                item.setEnd(end);
            }
        });
        return merge(list);
    }
    public static List<Integer> mergeWithLimit(List<WorkOffBlockDto> list, List<WorkOffBlockDto> highLevelBlocks, Date begin, Date end) {
        list.forEach(item->{
            if(item.GetBegin().before(begin)) {
                item.setBegin(begin);
            }
            if(item.GetEnd().after(end)) {
                item.setEnd(end);
            }
        });
        slice(list, highLevelBlocks);
        return merge(list);
    }
    public static void slice(List<WorkOffBlockDto> list, List<WorkOffBlockDto> highList) {
        Iterator<WorkOffBlockDto> iterator = highList.iterator();
        while (iterator.hasNext()) {
            WorkOffBlockDto high = iterator.next();
            List<WorkOffBlockDto> newList = new ArrayList<>();
            Iterator<WorkOffBlockDto> it = list.iterator();
            while (it.hasNext()) {
                WorkOffBlockDto current = it.next();
                if (high.GetBegin().before(current.GetEnd()) && high.GetEnd().after(current.GetBegin())) {//工时时间片段与实际请假时间相交
                    if(!high.GetBegin().after(current.GetBegin()) && !high.GetEnd().before(current.GetEnd())) {//工时片段被包含
                        it.remove();//删除工时片
                    } else if(!high.GetBegin().after(current.GetBegin())) {//工时片段左侧相同或被包含，右侧超出请假时间
                        current.setBegin(high.GetEnd());//将工时片段左侧改为请假结束时间
                    } else if(!high.GetEnd().before(current.GetEnd())) {//工时片段右侧相同或被包含，左侧超出请假时间
                        current.setEnd(high.GetBegin());//将工时片段右侧改为请假开始时间
                    } else {//请假被时间片包含
                        WorkOffBlockDto newItem = SerializationUtils.clone(current);//新建一个新工时片
                        current.setEnd(high.GetBegin());//将工时片段右侧改为请假开始时间
                        newItem.setBegin(high.GetEnd());//将新工时片段左侧改为请假结束时间
                        newList.add(newItem);
                    }
                }
            }
            list.addAll(newList);
        }
    }

//    private Double getDurationFromDetails(PersonnelAttendanceUser personnelAttendanceUser, List<PersonnelAttendanceUserDetail> details) {
////        //简单求和
////        List<Pair<Date, Date>> workHoursList = personnelAttendanceUser.GetWorkHoursList();
////        return personnelLeaves.stream().mapToDouble(item -> item.calcDuration(workHoursList)).sum() / TimeUnit.HOURS.toMillis(1);
//        //wyu：去重求和
//        Map<Long, Long>  workHoursSlices = personnelAttendanceUser.GetSliceWorkHoursList();
//        Pair<Map<Long,Long>, Map<Long, Long>> durationSlices = Pair.of(new HashMap<>(), workHoursSlices);
//        details.stream().forEach(item ->  item.sliceDuration(durationSlices));
//        return Double.valueOf(durationSlices.getLeft().entrySet().stream().mapToLong(item -> item.getValue()-item.getKey()).sum())/ TimeUnit.HOURS.toMillis(1);
//    }
//    public List<Pair<Date, Date>> GetWorkHoursList() {
//        List<Pair<Date, Date>> result = new ArrayList<>();
//        if(beginTime != null && endTime != null && attendanceDate!=null) {
//            if(breakBegin != null && breakEnd != null) {
//                result.add(Pair.of(NewDateUtils.joinDateTime(attendanceDate, beginTime), NewDateUtils.joinDateTime(attendanceDate, breakBegin)));
//                result.add(Pair.of(NewDateUtils.joinDateTime(attendanceDate, breakEnd), NewDateUtils.joinDateTime(attendanceDate, endTime)));
//            } else {
//                result.add(Pair.of(NewDateUtils.joinDateTime(attendanceDate, beginTime), NewDateUtils.joinDateTime(attendanceDate, endTime)));
//            }
//        }
//        return result;
//    }
//    public Map<Long, Long> GetSliceWorkHoursList() {
//        List<Pair<Date, Date>> wholes = GetWorkHoursList();
//        return wholes.stream().collect(Collectors.toMap(item->item.getLeft().getTime(), item -> item.getRight().getTime(), (key1, key2) -> key1));
//    }
//        Map<Long, Long> newItems = new HashMap<>();
//        Iterator<Map.Entry<Long, Long>> iterator = durationSlices.getRight().entrySet().iterator();
//        while (iterator.hasNext()) {
//            Map.Entry<Long, Long> set = iterator.next();
//            if (set.getKey() < actualEndTime.getTime() && set.getValue() > actualBeginTime.getTime()) {//工时时间片段与实际请假时间相交
//                if(set.getKey() >= actualBeginTime.getTime() && set.getValue()  <= actualEndTime.getTime()) {//工时片段被包含
//                    durationSlices.getLeft().put(set.getKey(), set.getValue());//添加时长片
//                    iterator.remove();//删除工时片
////                    removeKeys.add(set.getKey());//删除工时片
//                } else if(set.getKey() >= actualBeginTime.getTime()) {//工时片段左侧相同或被包含，右侧超出请假时间
//                    durationSlices.getLeft().put(set.getKey(), actualEndTime.getTime());//添加时长片段
//                    newItems.put(actualEndTime.getTime(), set.getValue());//重新添加右侧超出请假时间的工时片段
//                    iterator.remove();//删除原工时片
//                } else if(set.getValue() <= actualEndTime.getTime()) {//工时片段右侧相同或被包含，左侧超出请假时间
//                    durationSlices.getLeft().put(actualBeginTime.getTime(), set.getValue());//添加右侧相交部分时长片段
//                    set.setValue(actualEndTime.getTime());//将工时片段右侧改为请假开始时间
//                } else {//请假被时间片包含
//                    durationSlices.getLeft().put(actualBeginTime.getTime(), actualEndTime.getTime());//添加请假时间为时长片段
//                    newItems.put(actualEndTime.getTime(), set.getValue());//新加请假结束到工时片段结束为新的工时片段
//                    set.setValue(actualBeginTime.getTime());//将工时片段右侧改为请假开始时间
//                }
//            }
//        };
//        durationSlices.getRight().putAll(newItems);
}