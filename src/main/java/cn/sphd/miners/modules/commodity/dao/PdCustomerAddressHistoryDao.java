package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdCustomerAddress;
import cn.sphd.miners.modules.commodity.entity.PdCustomerAddressHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;

public interface PdCustomerAddressHistoryDao extends IBaseDao<PdCustomerAddressHistory,Serializable> {

    //添加历史记录
    void insert(PdCustomerAddress pdCustomerAddress);

    //添加历史记录
    PdCustomerAddressHistory insertNoCustomerAddress(PdCustomerAddress pdCustomerAddress);
}
