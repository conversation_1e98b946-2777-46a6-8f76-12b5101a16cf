package cn.sphd.miners.modules.forumArea.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;
import cn.sphd.miners.modules.forumArea.entity.*;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2018/2/2.
 */
public interface ForumService extends BadgeNumberCallback {

    enum Type { //发布方式:1-自行上传,2-发布任务,团队成员上传
        picture("图片", (byte)1),
        file("文件", (byte)2),
        resource("资源", (byte)3);
        private String name;
        private Byte index;
        Type(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }

        public Byte getIndex() {
            return index;
        }
    }

    //新增一个讨论
    void insertForumStatus(ForumPost forumPost, User user);

    //为一个讨论新增附件
    void insertForumattachment(ForumPostAttachment forumPostAttachment, User user);

    //保存一个讨论的参与人员
    void insertFotrumUser(ForumPostUser forumPostUser);

    //在主题审批中获取讨论的信息
    ForumPost forumPostMessageByhead(Integer id);

    //获取一个讨论的详细信息
    HashMap forumPostMessage(Integer id, Integer userID);

    //获取回帖的全部信息
    HashMap forumReplyMessage(Integer postId, Integer userID, Integer replyId, String type, String unreadNum, Integer parentReplyId);

    //根据条件获取讨论的全部信息
    HashMap allReplyByType(User user, Integer postId, String time, Integer findUser, String mesType, Date startTime, Date endTime, PageInfo pageInfo, String type);

    //获取某天的回帖或回复
    List<ForumReply> getReplyByDay(Integer postId, Date startDay, Date endDay, PageInfo pageInfo);

    //根据type查找附属文件或附属图片
    List<ForumReplyAttachment> getSubsidiaryFile(Integer postId, String type, String attType, String place, PageInfo pageInfo);

    //删除讨论区消息提示的消息数
    int delMessageForForumPost(Integer id, Integer userID);

    //批准或驳回讨论
    Integer approveStatus(User user, ForumPost forumPost);

    //修改讨论主题
    ForumPost upForumPost(ForumPost forumPost);

    //修改讨论的状态
    void upPostStatus(ForumPost forumPost, String title, String content, Integer compere, String compereName, Integer participantsNum, Date date);

    /*//修改参与人数
    void upForumPostParticipantsNum(ForumPost forumPost);
*/
    //获取最初页面的已通过的讨论
    List<ForumPost> getForumPost(Integer userID, String title, Integer enabled, String updateDate, PageInfo pageInfo);

    //新增一条回帖
    void insertForumReply(ForumReply forumReply);

    //修改一条回帖dereplyNum字段
    ForumReply upForumReply(ForumReply forumReply);

    //根据id得到回帖
    ForumReply oneForumReply(Integer id);

    //获取某个讨论的最后一条消息
    ForumReply getLastOneForumReply(Integer postId);

    //删除回帖的附件并删除引用
    void delReplyAttachment(Integer replyId, User user);

    //新增回帖或回复的人
    void insertReplyUser(ForumReplyUser forumReplyUser);

    //获取所有的参与人
    List<ForumPostUser> listPostUser(Integer id);

    //获取某个讨论的参与人
    ForumPostUser getOnePostUser(Integer postId, Integer userId);

    //删除某个讨论的参与人
    void delPostUser(Integer post, Integer userId, ForumPostUser forumPostUser);

    //新增某个讨论的参与人
    String addPostUser(Integer post, Integer userId);

    //获取用户分析表数据的某条数据
    ForumReplyUser getReplyUser(Integer replyId, Integer userID);

    //删除某个回帖的参与人
    Integer delReplyUser(Integer id);

    //删除回复用户表某个post的某个人的数据
    void delReplyUserByPostAndUser(Integer id, Integer userID);

    //删除讨论_回复用户统计表（t_forum_reply_user_stat）某回复的全部数据
    void delReplyUserState(Integer postId, Integer replyId, Integer userID);

    //发送消息方法
    boolean sendForumPostMessage(Integer type, ForumPost forumPost, Integer userId);

    //新增讨论详情页面的“M信息”和“回复”处的人员用以查出数字
    void insertReplyUserStat(ForumReplyUserStat forumReplyUserStat);

    //把人员的一些信息封装到dto中
    PostUserMussage backUser(User user);

    //新增点对点讨论当存在时改为新增一条消息
    void twoUserForumPost(Integer org,List<ForumPostUser> listPostUser, User user, ForumPost forumPost);

    //公用循环参与人来推送主题，新消息和删除新增人的方法
    void pushNewReplyByType(List<ForumPostUser> forumPostUserList, String type, Integer userID, Integer postId,
                            Integer replyId, Integer reply, Integer creator, String listUser,
                            HashMap<String, Object> map, ForumPost forumPost, String forumAndroidId, String operationType);

    //新增一条系统消息
    ForumReply insertSystemReply(Integer userID, Integer postId, String creatName, String type, String allName, String title, String content, Integer compere);

    //查看当前讨论组是否有讨论，若是没有就新增一版
    Integer checkeForumPostHistory(Integer postId, ForumPost forumPost, List<ForumPostUser> listPostUser, User user);

    //新增一版ForumPost的历史
    void insertForumPostHistory(ForumPost forumPost, Integer versionNo, List<ForumPostUser> listPostUser, User user);

    //获取某讨论全部的历史记录
    List<ForumPostHistory> listForumPostHistory(Integer postId);

    //获取某历史版本的参与人
    List<ForumPostUserHistory> listForumPostHistoryUser(Integer postId, Integer postHistoryId);

    //停用讨论组
    void stopUsingForumPost();

    //清除讨论组
    void removeForumPost();

    //开放或不开放讨论组
    Integer forumPostByIsOpen(Integer postId, Integer isOpen);

    //获取某机构开放状态的讨论组
    List<ForumPost> listOpenForumPost(Integer oid, String title, PageInfo pageInfo);

    //删除讨论组
    HashMap delForumPost(User user, Integer postId);

    //获取管理的讨论组列表
    List<ForumPost> getManagerPostList(Integer userID);

    //还原删除的讨论
    Integer restoreForum(Integer postId);

    //获取主题附件表single
    ForumPostAttachment getSingleByPostAtt(Integer id);

    //获取讨论主题的全部附件
    List<ForumPostAttachment> listPostAtt(Integer postId);

    //获取讨论回复附件表single
    ForumReplyAttachment getSingleByReplyAtt(Integer id);

    //获取讨论实体
    ForumPost getSingleByForum(Integer id);

    //新增讨论的审批流程
    void addApprovalProcessForForum(Integer postId, User user, Integer level, Integer toUser,
                                    String toUserName, Date createDate, String toMid);

    //中间审批和终极审批讨论
    String handlerForumPost(User user, Integer postId, Integer processId, String approveStatus, String approveMemo,
                            Integer toUser, String toUserName, String type);

    //申请人获取讨论
    List<ForumPost> getApplyForum(Integer userID);

    //中间人获取讨论
    HashMap<String, Object> getApproveForum(Integer userID);

    //终审人获取讨论
    List<ForumPost> getLastForum(Integer oid);

    //三种条件查询讨论
    List<ForumPost> findForumByPage(HashMap param, String eventType, Integer fromUser);

    //获取审批流程
    List<ApprovalProcess> getlistApprovalProcess(Integer id, Integer bussinessType);

    //根据回复的附件path字段获取某条附件
    ForumReplyAttachment forumReplyAttSingle(String path);

    //获取回复附件表图片和文件的个数
    Object[] getReplyAttNumByType(Integer post);

    //内部文件换版时发送新消息方法
    void changeResVersionSendNewMes(Integer fileId,String name, String fileSn, Integer changeNum);

    //外部文件换版时发送新消息方法
    void changeAttVersionSend(User user, Integer replyId, String type, String path, String size, String module);

    //封装下发送信息的方法用于新增消息和新增讨论时发附件消息调用
    ForumReply addReplyForPub(User user, Integer postId, Integer reply, String content, String parentContent,
                              String listforumReplyAttachment, String listUser, String forumAndroidId);

    //新增一条消息
    HashMap insertOneReply(User user, Integer postId, Integer reply, String content, String parentContent,
                           String listforumReplyAttachment, String listUser, String forumAndroidId, String type,
                           String attType, Integer attId, Integer resId, Integer resVersion ,Integer resHisId,
                           Integer replyIdByAtt, String isUploaded);

    ForumReplyAttHis getSingleByReplyAttHis(Integer id);

    List<ForumReplyAttHis> listReplyAttHis(Integer replyId, PageInfo pageInfo);

    //删除文件时要把内部文件转为外部文件
    void changeResFileIntoAtt(Integer fileId, String path, String fileName, String version, Integer size);
}
