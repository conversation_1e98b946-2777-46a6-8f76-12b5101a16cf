package cn.sphd.miners.modules.dailyAffairs.service;


import cn.sphd.miners.modules.dailyAffairs.entity.PersonnelReimbursePayment;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/12/27.
 */
public interface PaymentApprovalService{

    List<PersonnelReimburse> getPaymentHandle(Integer toUser, Integer fromUser, String approvalStatusReimburse, String approvalStatus, Integer businessType, Date beginDate, Date endDate);  //付款审批待处理

    List<PersonnelReimburse> paymentReviewApprover(Integer toUser,Integer fromUser,String approvalStatusReimburse, String approvalStatus,Date beginDate,Date endDate,Integer... businessType);

    Map<String,Object> paymentHandleApproval(Integer userId,Integer approvalProcessId); //付款审批的审批

    List<PersonnelReimburse> cashierPaymentHandle(Integer oid,String approveStatus,Integer businessType);  //财务-出纳-待付款审批列表

    //------1.103付款复核
    //可付款的审批
    Map<String,Object> payableApproval(Integer userId,Integer approvalProcessId,String method, Date planDate, Date factDate, Integer accountId,Integer reimburseId,String summary,Map<String,Object> map);

    Map<String,Object> reviewApproval(Integer userId,Integer approvalProcessId);

    Map<String,Object> getPaymentMethod(Integer reimburseId);  //查询原定付款方式

    PersonnelReimbursePayment getCurrentPaymentMethod(Integer reimburseId);   //查看当前的付款方式

    Map<String,Object> updatePaymentMethod(Integer userId,Integer approvalProcessId,String method,Date planDate,Date factDate,Integer accountId,Integer type,String summary);   //修改原定付款方式
}
