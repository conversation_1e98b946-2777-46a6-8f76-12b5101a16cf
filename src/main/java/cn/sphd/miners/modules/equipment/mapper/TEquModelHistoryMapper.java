package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquModelHistory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 装备管理_型号历史
 * 20230209 1.247装备器具新增
 * Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Repository
public interface TEquModelHistoryMapper {
    /**
     * 查询装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_型号历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 装备管理_型号历史
     * 20230209 1.247装备器具新增
     */
    public TEquModelHistory selectTEquModelHistoryById(Long id);

    /**
     * 查询装备管理_型号历史
     * 20230209 1.247装备器具新增
     * 列表
     *
     * @param tEquModelHistory 装备管理_型号历史
     *                         20230209 1.247装备器具新增
     * @return 装备管理_型号历史
     * 20230209 1.247装备器具新增
     * 集合
     */
    public List<TEquModelHistory> selectTEquModelHistoryList(TEquModelHistory tEquModelHistory);

    /**
     * 新增装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquModelHistory 装备管理_型号历史
     *                         20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquModelHistory(TEquModelHistory tEquModelHistory);

    /**
     * 修改装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquModelHistory 装备管理_型号历史
     *                         20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquModelHistory(TEquModelHistory tEquModelHistory);

    /**
     * 删除装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_型号历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 结果
     */
    public int deleteTEquModelHistoryById(Long id);

    /**
     * 批量删除装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquModelHistoryByIds(String[] ids);
}
