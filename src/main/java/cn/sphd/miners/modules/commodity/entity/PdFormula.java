package cn.sphd.miners.modules.commodity.entity;

import cn.sphd.miners.modules.material.entity.MtBase;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Entity
@Table(name = "t_pd_formula")
public class PdFormula {
    private Integer id;
    private Integer org;
    private String code;
    private String name;
    private String materialName;
    private Integer majorIngredient;
    private Integer minorIngredient;
    private Integer products;
    private String enabled;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;  //1增  2修改 3删除 4停用
    private Integer previousId;
    private Integer versionNo;
    private List<Map<String,Object>> zlList;
    private List<Map<String,Object>> flList;
    private Integer product;
    private Integer instance;
    private String instanceChain;
    private String approveStatus;
    private String rejectReasionDesc;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "material_name")
    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    @Basic
    @Column(name = "major_ingredient")
    public Integer getMajorIngredient() {
        return majorIngredient;
    }

    public void setMajorIngredient(Integer majorIngredient) {
        this.majorIngredient = majorIngredient;
    }

    @Basic
    @Column(name = "minor_ingredient")
    public Integer getMinorIngredient() {
        return minorIngredient;
    }

    public void setMinorIngredient(Integer minorIngredient) {
        this.minorIngredient = minorIngredient;
    }

    @Basic
    @Column(name = "products")
    public Integer getProducts() {
        return products;
    }

    public void setProducts(Integer products) {
        this.products = products;
    }

    @Basic
    @Column(name = "enabled")
    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }
    @Transient
    public List<Map<String,Object>> getZlList() {
        return zlList;
    }

    public void setZlList(List<Map<String,Object>> zlList) {
        this.zlList = zlList;
    }
    @Transient
    public List<Map<String,Object>> getFlList() {
        return flList;
    }

    public void setFlList(List<Map<String,Object>> flList) {
        this.flList = flList;
    }

    @Transient
    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Basic
    @Column(name = "instance")
    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }
    @Basic
    @Column(name = "instance_chain")
    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    @Basic
    @Column(name = "approve_status")
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Basic
    @Column(name = "reject_reasion_desc")
    public String getRejectReasionDesc() {
        return rejectReasionDesc;
    }

    public void setRejectReasionDesc(String rejectReasionDesc) {
        this.rejectReasionDesc = rejectReasionDesc;
    }
}
