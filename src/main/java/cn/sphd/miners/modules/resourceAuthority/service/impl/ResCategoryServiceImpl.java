package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.resourceAuthority.dto.ResNumDto;
import cn.sphd.miners.modules.resourceAuthority.entity.*;
import cn.sphd.miners.modules.resourceAuthority.mapper.*;
import cn.sphd.miners.modules.resourceAuthority.service.ReadRoomService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCategoryService;
import cn.sphd.miners.modules.resourceAuthority.service.ResNoticeService;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;

/**
 * Created by 朱思旭 on 2017/10/31.
 */
@Service
@Transactional(readOnly=false)
public class ResCategoryServiceImpl implements ResCategoryService {

    @Autowired
    ResCategoryMapper resCategoryMapper;
    @Autowired
    ResCategoryAclMapper resCategoryAclMapper;
    @Autowired
    ResMapper resMapper;
    @Autowired
    ResAclMapper resAclMapper;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    ResService resService;
    @Autowired
    ResCategoryHistoryMapper resCategoryHistoryMapper;
    @Autowired
    ResAclLogMapper resAclLogMapper;
    @Autowired
    ResCategoryService resCategoryService;
    @Autowired
    ResHistoryMapper resHistoryMapper;
    @Autowired
    ReadingRoomMapper readingRoomMapper;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ReadRoomService readRoomService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    ResNoticeService resNoticeService;

    @Override
    public QueryData getFirstType(User user, String type) {
        ResCategory rc = new ResCategory();
        rc.setOrg(user.getOid());
        rc.setUser(user.getUserID());
        rc.setType(type);
        List<ResCategory> listFirstCategory = resCategoryMapper.getCategoryByParent(rc);    //获取第一层全部文件夹
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(1);
        QueryData param = new QueryData();
        param.put("pageInfo", pageInfo);
        param.put("user", user.getUserID());
        param.put("type",type);
        Integer authResult = 0;
        if (!listFirstCategory.isEmpty()) {
            for (ResCategory r : listFirstCategory) {
                param.put("parent",r.getId());
                //获取到一级文件夹下是否有子文件夹，若是有就给childStatus属性填入1.
                ResCategory listChild = resCategoryMapper.getOneCategoryByParentAuthlistPage(param);
                if (listChild != null) {
                    r.setChildStatus("1");
                }
                ResEntity listFile = resMapper.getOneFileByCategorylistPage(param);
                if (listFile != null) {
                    r.setFileStatus("1");
                }
                if (authResult.equals(0)) {
                    Integer authIds = resCategoryAclMapper.countAuth(r.getId());
                    if (authIds > 0) {
                        authResult = 1;
                    }
                }
            }
        }
        QueryData query = new QueryData();
        query.put("listFirstFolder", listFirstCategory);
        query.put("authFolder", authResult);
        return query;
    }

    @Override
    public HashMap<String, Object> getFirstTypeByManager(User user) {
        List<ResCategory> listFirstCategory = this.listFirstCategory(user);     //获取第一层全部文件夹
        Integer folderNum = listFirstCategory.size();//第一层文件夹的个数
        HashMap<String, Object> paramFirst = new HashMap<>();
        paramFirst.put("org", user.getOid());
        ResCategory allCat = resMapper.getFileNum(paramFirst);
        Integer fileNum = allCat.getLeafs();   //某机构有效文件的个数
        ResCategory catRecycleBin = new ResCategory();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(1);
        QueryData param = new QueryData();
        param.put("pageInfo", pageInfo);
        Integer authResult = 0;
        Integer haveStatus = 0;
        Integer noHaveStatus = 0;
        for (ResCategory r : listFirstCategory) {
            param.put("categoryPath", r.getId());
            param.put("parent", r.getId());
            //获取到一级文件夹下是否有子文件夹，若是有就给childStatus属性填入1.
            ResCategory cat = resMapper.getFileNum(param);
            if (cat.getLeafs()>0) {
                r.setLeafs(cat.getLeafs());
            }
            Integer childrenNum = resCategoryMapper.getAllChildCategoryNumByParent(r.getId());
            if (childrenNum > 0) {
                r.setChildStatus("1");
                r.setChildren(childrenNum);
            }
            ResEntity oneFile = resMapper.getOneFileByCategorylistPage(param);
            if (oneFile != null) {
                r.setFileStatus("1");
            }
            //判断此文件夹是否是特殊文件夹
            if (Byte.valueOf("1").equals(r.getIsTrash())) {
                catRecycleBin = r;
            }
            Integer authIds = resCategoryAclMapper.countAuth(r.getId());
            if (authIds > 0) {
                haveStatus = 1;
            } else {
                noHaveStatus = 1;
            }
        }
        if (haveStatus.equals(1)) {
            if (noHaveStatus.equals(0)) {
                authResult = 2;
            }else {
                authResult = 1;
            }
        }
        List<ResCategory> firstCategtyList = new ArrayList<>();
        if (catRecycleBin.getId() != null) {
            listFirstCategory.remove(catRecycleBin);
            firstCategtyList.addAll(listFirstCategory);
            firstCategtyList.add(catRecycleBin);
        }else {
            firstCategtyList.addAll(listFirstCategory);
            catRecycleBin.setOrg(user.getOid());
            catRecycleBin.setType("1");
            catRecycleBin.setIsTrash(Byte.valueOf("1"));
            catRecycleBin.setName("暂时不用的文件");
            catRecycleBin.setCreator(user.getUserID());
            catRecycleBin.setCreateName(user.getUserName());
            catRecycleBin.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            catRecycleBin.setCategory("1");
            catRecycleBin.setValid(true);
            catRecycleBin.setChildren(0);
            catRecycleBin.setLeafs(0);
            catRecycleBin.setSize(BigInteger.valueOf(0));
            catRecycleBin.setVersionNo(0);
            int insertRecycleBin = resCategoryMapper.insert(catRecycleBin);
            catRecycleBin.setPath(catRecycleBin.getId().toString());
            int updateRecycleBin = resCategoryMapper.update(catRecycleBin);
            firstCategtyList.add(catRecycleBin);
        }
        HashMap<String, Object> query = new HashMap<>();
        query.put("listFirstFolder", firstCategtyList);
        query.put("authFolder", authResult);
        query.put("folderNum", folderNum);
        query.put("fileNum", fileNum);
        return query;
    }

    @Override
    public QueryData getCategoryAndChildCategory(User user, String type, Integer categoryId) {
        ResCategory rc = new ResCategory();
        rc.setId(categoryId);
        ResCategory categoryMessage = resCategoryMapper.getSingle(rc);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(20);
        QueryData query = resService.getFile(user,categoryId,pageInfo,"1", null);
        rc.setType(type);
        rc.setUser(user.getUserID());
        rc.setOrg(user.getOid());
        rc.setParent(categoryId);
        List<ResCategory> listChildCategory = resCategoryMapper.getChildCategory(rc);
        pageInfo = new PageInfo();
        pageInfo.setPageSize(1);
        QueryData param = new QueryData();
        param.put("pageInfo", pageInfo);
        param.put("user", user.getUserID());
        param.put("type",type);
        for (ResCategory r : listChildCategory) {
            param.put("parent", r.getId());
            //获取到一级文件夹下是否有子文件夹，若是有就给childStatus属性填入1.
            ResCategory catChild = resCategoryMapper.getOneCategoryByParentAuthlistPage(param);
            if (catChild != null) {
                r.setChildStatus("1");
            }
            ResEntity oneFile = resMapper.getOneFileByCategorylistPage(param);
            if (oneFile != null) {
                r.setFileStatus("1");
            }
        }
        query.put("parentFolder", categoryMessage);
        query.put("childFolder", listChildCategory);
        return query;
    }

    @Override
    public QueryData getCategoryAndChildCategoryManager(User user, Integer categoryId) {
        ResCategory rc = new ResCategory();
        rc.setId(categoryId);
        ResCategory categoryMessage = resCategoryMapper.getSingle(rc);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(20);
        QueryData query = resService.getFileByManager(user,categoryId,pageInfo,"1", null);
        List<ResCategory> listChildCategory = resCategoryMapper.getAllChildCategoryByParent(categoryId);
        categoryMessage.setChildren(listChildCategory.size());  //填入新的文件夹数量
        if (listChildCategory.size() > 0) {
            categoryMessage.setChildStatus("1");
        }
        HashMap<String, Object> paramParent = new HashMap<>();
        if (categoryMessage.getPath() != null) {
            paramParent.put("categoryPath", categoryMessage.getPath());
        }else {
            paramParent.put("categoryPath",categoryMessage.getId());
        }
        ResCategory allCat = resMapper.getFileNum(paramParent);
        categoryMessage.setLeafs(allCat.getLeafs());   //填入新的文件数量
        if (categoryMessage.getLeafs() > 0) {
            categoryMessage.setFileStatus("1");
        }
        pageInfo = new PageInfo();
        pageInfo.setPageSize(1);
        QueryData param = new QueryData();
        param.put("pageInfo", pageInfo);
        for (ResCategory r : listChildCategory) {
            param.put("parent", r.getId());
            param.put("categoryPath", r.getPath());
            //获取到一级文件夹下是否有子文件夹，若是有就给childStatus属性填入1.
            ResCategory cat = resMapper.getFileNum(param);
            if (cat.getLeafs()>0) {
                r.setLeafs(cat.getLeafs());
            }
            Integer childrenNum = resCategoryMapper.getAllChildCategoryNumByParent(r.getId());
            if (childrenNum > 0) {
                r.setChildStatus("1");
                r.setChildren(childrenNum);
            }
            ResEntity oneFile = resMapper.getOneFileByCategorylistPage(param);
            if (oneFile != null) {
                r.setFileStatus("1");
            }
        }
        query.put("parentFolder", categoryMessage);
        query.put("childFolder", listChildCategory);
        return query;
    }

    @Override
    public List<ResDepartAndEmployee> getDepartAndEmployee(int oid) {
        List<ResDepartAndEmployee> users = resCategoryMapper.getDepartAndEmployee(oid);
        return users;
    }

    @Override
    public JSONObject getDepartment(int oid, int pid) {
        JSONObject result = new JSONObject();
        //查询该pid下的一级子部门
        List<ResDepartAndEmployee> departs = resCategoryMapper.getDepartment(pid);
        //查询该pid下的人员
        List<ResDepartAndEmployee> employee = resCategoryMapper.getEmployee(pid);
        //查询每个部门下面是否有子部门或人员
        for (ResDepartAndEmployee depart : departs)
        {
            List<ResDepartAndEmployee> childDeparts = resCategoryMapper.getDepartment(depart.getDepartid());
            if (childDeparts.size() > 0)
                depart.setArrow(true);
            else
            {
                List<ResDepartAndEmployee> childEmployee = resCategoryMapper.getEmployee(depart.getDepartid());
                if (childEmployee.size() > 0)
                {
                    depart.setArrow(true);
                }
            }
        }
        result.put("departs",departs);
        result.put("employee",employee);
        return result;
    }

    @Override
    public ResCategory addSameCategoryByResource(User user, Integer parent, String categoryName, String type, List<Integer> listUserIDs) {
        ResCategory rc = new ResCategory();
        rc.setOrg(user.getOid());
        rc.setName(categoryName);
        rc.setType(type);
        rc.setChildren(0);
        rc.setLeafs(0);
        rc.setSize(BigInteger.valueOf(0));
        rc.setVersionNo(0);
        rc.setCreateName(user.getUserName());
        rc.setCreator(user.getUserID());
        rc.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        rc.setValid(true);
        rc.setIsTrash(Byte.valueOf("0"));
        ResCategory upCatPath = new ResCategory();

        if (parent == null) {
            Integer addCategory = resCategoryMapper.insert(rc);
            upCatPath.setPath(rc.getId().toString());
            //      resCategoryService.insertResAclLog(rc.getId(), null, listUserIDs, new ArrayList<Integer>(), user, new Date(), categoryName, user.getOid(), "1");
            listUserIDs = this.addCategoryAuth("1", user, rc.getId(), parent, listUserIDs);
        } else {
            rc.setParent(parent);
            Integer addCategory = resCategoryMapper.insert(rc);
            String path = resService.allParentCategoryName(parent, null, rc.getId().toString(), "2");  //获取路径至本级 例子“1,2,3”
            categoryName = resService.allParentCategoryName(parent, categoryName, rc.getId().toString(), "1");
            upCatPath.setPath(path);
            listUserIDs = this.addCategoryAuth("1", user, rc.getId(), parent, listUserIDs);
        }
        resCategoryService.insertResAclLog(rc.getId(), null, listUserIDs, new ArrayList<Integer>(), user, new Date(), categoryName, user.getOid(), "1");
        upCatPath.setId(rc.getId());
        resCategoryMapper.update(upCatPath);   //新增后要给文件夹路径字段赋值
        return rc;
    }

    @Override
    public QueryData addSameCategoryByData(User user, Integer parent, String categoryName, String type) {
        ResCategory rc = new ResCategory();
        rc.setCreator(user.getUserID());
        rc.setName(categoryName);
        rc.setVersionNo(0);
        ResCategory category = null;
        if (parent == null) {
            category = resCategoryMapper.getCategoryByNameAndOidAndType(rc);
        } else {
            rc.setParent(parent);
            category = resCategoryMapper.getCategoryByNameAndOidAndParent(rc);
        }
        int state = 0;
        ResCategory newCategory = null;
        if (category == null) {
            state = 1;
            rc.setCreateName(user.getUserName());
            rc.setOrg(user.getOid());
            rc.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            rc.setType(type);
            rc.setValid(true);
            if (parent == null) {
                Integer addCategory = resCategoryMapper.insert(rc);
                this.addCategoryAuth("2",user,rc.getId(),parent,null);
            } else {
                rc.setParent(parent);
                Integer addCategory = resCategoryMapper.insert(rc);
                this.addCategoryAuth("2",user,rc.getId(),parent, null);
            }
            newCategory = resCategoryMapper.getSingle(rc);
        }
        QueryData query = new QueryData();
        query.put("state", state);
        query.put("newCategory", newCategory);
        return query;
    }

    @Override
    public ResCategory getSingle(Integer categoryId) {
        return resCategoryMapper.getSingle( new ResCategory(categoryId) );
    }

    @Override
    public List<OrganizationDto> getAuthOrgWithUserTreeByOid(Integer oid, Integer categoryId, String type) {
        List<OrganizationDto> list = null;
        if ("1".equals(type)) {
            if(categoryId.equals(0)){
                list = orgService.getOrgWithAuthUserTreeLocking(oid,new ArrayList<Integer>());
            }else {
                ResCategory category = this.getSingle(categoryId);
                List<Integer> userIds = resCategoryAclMapper.getAllUserIdsByCategory(categoryId);
                list = orgService.getOrgWithAuthUserTreeByResLocking(oid,userIds,null,type);
            }
        } else {
            if(categoryId.equals(0)){
                list = orgService.getOrgWithAuthUserTreeLocking(oid,new ArrayList<Integer>());
            }else {
                ResCategory category = this.getSingle(categoryId);
                List<Integer> userIds = resCategoryAclMapper.getAllUserIdsByCategory(categoryId);
                Integer parent = category.getParent();
                if (parent != null){
                    List<Integer> userParentIds = resCategoryAclMapper.getAllUserIdsByCategory(parent);
                    list = orgService.getOrgWithAuthUserTreeByResLocking(oid,userParentIds,userIds,type);
                }else {
                    list = orgService.getOrgWithAuthUserTreeLocking(oid,userIds);
                }
            }
        }
        return list;
    }

    @Override
    public String updateFolderAuth(Integer categoryId, List<Integer> userID, User user, String operateType, String allCategoryName, Long leafs) {
        List<ResCategory> listOridnaryCat = null;
        if (categoryId == null) {
            listOridnaryCat = resCategoryMapper.getOrdinaryFirstCategory(user.getOid());
        }else {
            listOridnaryCat = new ArrayList<>();
            ResCategory cat = this.getSingle(categoryId);
            listOridnaryCat.add(cat);
        }
        String status = "2";
        if (listOridnaryCat != null) {
            for(ResCategory c : listOridnaryCat){
                List<ResCategoryAcl> oldAcls = resCategoryAclMapper.getAllAuthByCategory(c.getId());
                if (oldAcls.isEmpty()) {
                    this.confrimUpAuth(oldAcls,userID,c,user,operateType, allCategoryName, leafs);
                    status = "1";
                }else {
                    if(categoryId != null){
                        this.confrimUpAuth(oldAcls,userID,c,user,operateType, allCategoryName, leafs);
                        status = "1";
                    }
                }
            }
        }
        String state = "1";
        if(!"1".equals(status)){
            state = "2";
        }
        return state;
    }
    private void confrimUpAuth(List<ResCategoryAcl> oldAcls, List<Integer> userID, ResCategory c, User user, String operateType, String allCategoryName, Long leafs){
        List<Integer> addUsers = new ArrayList<>();
        addUsers.addAll(userID);
        ArrayList<Integer> oldUserIds = new ArrayList<>();
        ArrayList<Integer> delUsers = new ArrayList<>();
        for(ResCategoryAcl acl : oldAcls){
            if(addUsers.contains(acl.getUser())) {
                oldUserIds.add(acl.getUser());
            } else {
                delUsers.add(acl.getUser());
                resCategoryAclMapper.delete(acl);
            }
        }
        addUsers.removeAll(oldUserIds);
        for (Integer userId : addUsers){
            resCategoryAclMapper.insert( new ResCategoryAcl(c.getId(), userId) );
        }
        this.updateSubAuth(c.getId(),addUsers,delUsers);
        if (!addUsers.isEmpty()) {
            resService.sendCategory(c, addUsers, user, "2", "1");    //新增文件夹
            if (leafs == null) {
                QueryData param = new QueryData();
                param.put("categoryPath", c.getPath());
                ResCategory catLeafs = resMapper.getFileNum(param);
                leafs = Long.valueOf(catLeafs.getLeafs());
            }
            if (!leafs.equals(Long.valueOf(0))) {
                resNoticeService.sendResFileNotice(user, null, null, null,null,null, leafs, c.getId(), "1", addUsers,null,null);
            }
        }
        if ("2".equals(operateType)) {
            allCategoryName = resService.allParentCategoryName(c.getParent(), c.getName(), null, "1");
        }
        this.insertResAclLog(c.getId(), null, addUsers, delUsers, user, new Date(), allCategoryName, user.getOid(), operateType);
    }
    protected void updateSubAuth(Integer categoryId, List<Integer> addUsers, List<Integer> delUsers){
        List<Integer> subFolders = new ArrayList<>();
        List<Integer> subFiles = new ArrayList<>();
        List<Integer> fileIds = resMapper.getFileIdsByCategory(categoryId);
        subFiles.addAll(fileIds);
        this.fillSubFolderFiles(categoryId, subFolders, subFiles);
        for(Integer id : subFolders){
            for(Integer userID : delUsers){
                resCategoryAclMapper.delAuthByUserAndCategory( new ResCategoryAcl(id, userID) );
            }
            for(Integer userID : addUsers){
                ResCategoryAcl resCategoryAcl = new ResCategoryAcl(id, userID);
                if(resCategoryAclMapper.count(resCategoryAcl).intValue()<=0) {
                    resCategoryAclMapper.insert(new ResCategoryAcl(id, userID));
                }
            }
        }
        for(Integer id : subFiles){
            for(Integer userID : delUsers){
                resAclMapper.delAuthByFileAndUser( new ResAcl(id, userID) );
            }
            for(Integer userID : addUsers){
                ResAcl resAcl = new ResAcl(id, userID);
                if(resAclMapper.count(resAcl).intValue()<=0) {
                    resAclMapper.insert(resAcl);
                }
            }
        }
    }
    protected void fillSubFolderFiles(Integer categoryId, List<Integer> subFolders, List<Integer> subFiles){
        List<Integer> folderIds = resCategoryMapper.getChildCategoryIds(categoryId);
        subFolders.addAll(folderIds);
        List<Integer> fileIds = resMapper.getFileIdsByCategory(categoryId);
        subFiles.addAll(fileIds);
        for( Integer forderId : folderIds){
            fillSubFolderFiles(forderId, subFolders, subFiles);
        }
    }
    //wyu: End of Forder Auth

    @Override
    public QueryData UpdateCategoryName(User user, ResCategory resCategory) {
        ResCategory rcOld = resCategoryMapper.getSingle(resCategory);
        ResCategory r = new ResCategory();
        r.setOrg(user.getOid());
        r.setName(resCategory.getName());
        r.setType(resCategory.getType());
        ResCategory category = null;
        if (r.getParent() == null) {
            category = resCategoryMapper.getCategoryByNameAndOid(r);
        }else{
            r.setParent(rcOld.getParent());
            category = resCategoryMapper.getCategoryByNameAndOidAndParent(r);
        }
        Integer state = 0;
        Integer upState = 0;
        if (category == null) {
            r.setId(resCategory.getId());
            r.setUpdator(user.getUserID());
            r.setUpdateName(user.getUserName());
            r.setUpdateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            if ("1".equals(resCategory.getType())) {
                if (rcOld.getVersionNo().equals(0)) {
                    insertCategoryHistory(rcOld.getId(),rcOld.getName(),rcOld.getCreator(), rcOld.getCreateName(),rcOld.getCreateDate(),rcOld.getVersionNo());
                }
                r.setVersionNo(rcOld.getVersionNo() + 1);
            }
            upState = resCategoryMapper.update(r);
            if ("1".equals(resCategory.getType())) {
                insertCategoryHistory(r.getId(), r.getName(), r.getUpdator(), r.getUpdateName(), r.getUpdateDate(), r.getVersionNo());
            }
            state = 1;
        }
        ResCategory rcNew = resCategoryMapper.getSingle(resCategory);
        if (state == 1) {
            List<Integer> listUserIDs = this.getAllUserIdsByCategory(rcNew.getId());
            resNoticeService.addSpecialAuthUserId(listUserIDs, user);  //填入特殊权限
            resNoticeService.sendResFileNotice(user,null,null,null,null,null,null,null,"15",listUserIDs,rcOld.getName(),rcNew.getName());
        }
        QueryData query = new QueryData();
        query.put("state", state);
        query.put("upState", upState);
        query.put("category", rcNew);
        return query;
    }

    @Override
    public void updateResCatUuid(Integer categoryId) {
        ResCategory r = new ResCategory();
        r.setId(categoryId);
        String uu = UUID.randomUUID().toString();
        String uuid = uu.replaceAll("-", "");
        r.setLockUuid(uuid);
        resCategoryMapper.update(r);
    }

    @Override
    public String deleteCategory(Integer categoryId) {
        ResCategoryAcl rca = new ResCategoryAcl();
        rca.setCategory(categoryId);
        Integer delAuth = resCategoryAclMapper.delAllAuthByCategory(categoryId);
        ResCategory r = new ResCategory();
        r.setId(categoryId);
        ResCategory category = resCategoryMapper.getSingle(r);
        r.setValid(false);
        Integer delCategory = resCategoryMapper.update(r);
        return "1";
    }

    @Override
    public QueryData getCategoryAuthUser(Integer categoryId) {
        ResCategory rc = new ResCategory();
        rc.setId(categoryId);
        ResCategory categoryMessage = resCategoryMapper.getSingle(rc);
        List<ResCategoryAcl> list = resCategoryAclMapper.getAllAuthByCategory(categoryId);
        List<PostUserMussage> allAuth = new ArrayList<>();
        if(!list.isEmpty()){
            for (ResCategoryAcl r : list) {
                User user = userService.getUserByID(r.getUser());
                PostUserMussage p = new PostUserMussage();
                p.setUserID(user.getUserID());
                p.setUserName(user.getUserName());
                p.setDepartName(user.getDepartName());
                p.setMobile(user.getMobile());
                p.setImgPath(user.getImgPath());
                p.setIsDuty(user.getIsDuty());
                allAuth.add(p);
            }
        }
        QueryData query = new QueryData();
        query.put("category",categoryMessage);
        query.put("authNum", list.size());
        query.put("allAuth", allAuth);
        return query;
    }

    //循环给文件夹的文件个数和大小进行累加或递减
    @Override
    public void sumCategoryLeafsAndSize(ResCategory category, BigInteger size, Integer leafs){
        List<String> list = new ArrayList<>();
        if(category.getPath() != null){
            list = Arrays.asList(category.getPath().split(","));
        } else {
            list.add(category.getId().toString());
        }
        for(String s : list){
            ResCategory cat = new ResCategory();
            cat.setId(Integer.parseInt(s));
            cat = resCategoryMapper.getSingle(cat);
            cat.setLeafs(cat.getLeafs() + leafs);
            cat.setSize(cat.getSize().add(size));
            resCategoryMapper.update(cat);
        }

    }

    @Override
    public List<ResCategoryHistory> listCategoryHistory(Integer categoryId) {
        List<ResCategoryHistory> list = resCategoryHistoryMapper.categoryHisList(categoryId);
        return list;
    }

    @Override
    public ResCategory checkFolder(User user, Integer parent, String categoryName, String type) {
        ResCategory rc = new ResCategory();
        rc.setOrg(user.getOid());
        rc.setName(categoryName);
        rc.setType(type);
        ResCategory category = null;
        if (parent == null) {
            category = resCategoryMapper.getCategoryByNameAndOid(rc);
        } else {
            rc.setParent(parent);
            category = resCategoryMapper.getCategoryByNameAndOidAndParent(rc);
        }
        return category;
    }

    @Override
    public void insertResAclLog(Integer categoryId, Integer fileId, List<Integer> newUsers, List<Integer> delUsers,
                                User user, Date now, String categoryName, Integer oid, String operateType) {
        ResAclLog ral = new ResAclLog();
        if (categoryId != null) {
            ral.setCategory(categoryId);
        }
        if (fileId !=null) {
            ral.setResource(fileId);
        }
        ral.setOrg(oid);
        ral.setOperateType(operateType);
        if (newUsers != null) {
            StringBuffer increasedUsers = new StringBuffer();
            for (Integer nu : newUsers) {
                increasedUsers.append(nu + ",");
            }
            ral.setIncreasedUsers(increasedUsers.toString());
            ral.setIncreasedNum(newUsers.size());
        }
        if (delUsers != null) {
            StringBuffer decreasedUsers = new StringBuffer();
            for (Integer du : delUsers) {
                decreasedUsers.append(du + ",");
            }
            ral.setDecreasedUsers(decreasedUsers.toString());
            ral.setDecreasedNum(delUsers.size());
        }
        ral.setCreator(user.getUserID());
        ral.setCreateName(user.getUserName());
        ral.setCreateDate(now);
        ral.setPath(categoryName);
        resAclLogMapper.insert(ral);
    }

    @Override
    public List<ResAclLog> listResAclLog(Integer oid, String year, String month, String name, String type, PageInfo pageInfo) {
        QueryData query = new QueryData();
        query.put("pageInfo", pageInfo);
        query.put("org", oid);
        Date dateBegin = null;
        Date dateEnd = null;
        if (year != "") {
            String time = year;
            if (month != "") {
                if (month.length()==1) {
                    time = year + "-0" + month + "-01";
                }else {
                    time = year + "-" + month + "-01";
                }
                dateBegin = NewDateUtils.dateFromString(time, "yyyy-MM-dd");
                dateEnd = NewDateUtils.getLastTimeOfMonth(dateBegin);
            } else {
                time = year + "-01-01";
                dateBegin = NewDateUtils.dateFromString(time, "yyyy-MM-dd");
                dateEnd = NewDateUtils.getLastTimeOfYear(dateBegin);
            }
        } else {
            Date yearFirstDay = NewDateUtils.getNewYearsDay(new Date());
            if (month != "") {
                dateBegin = NewDateUtils.changeMonth(yearFirstDay, Integer.valueOf(month)-1);
                dateEnd = NewDateUtils.getLastTimeOfMonth(dateBegin);
            } else {
                dateBegin = yearFirstDay;
                dateEnd = NewDateUtils.getLastTimeOfYear(dateBegin);
            }
        }
        query.put("dateBegin",dateBegin);
        query.put("dateEnd",dateEnd);
        if (name != "") {
            query.put("name",name);
            if (type != "") {
                query.put("type", type);  //type 1-文件夹名称搜索 2-文件名称和文件编号搜索 3-all搜索
            } else {
                query.put("type", "3");
            }
        }
        List<ResAclLog> list = resAclLogMapper.resAclLoglistPage(query);
        if (!list.isEmpty()) {
            for (ResAclLog ral : list) {
                String allName = null;
                if (ral.getCategory() != null) {
                    ResCategory cat = this.getSingle(ral.getCategory());
                    allName = cat.getName();
                }
                if (ral.getResource() != null) {
                    ResEntity res = resService.getSingle(ral.getResource());
                    allName = res.getFileSn() + "/" + res.getName();
                }
                ral.setName(allName);
            }
        }
        return list;
    }

    @Override
    public QueryData resAclLogMessage(Integer aclId, String type) {
        QueryData query = new QueryData();
        ResAclLog r = new ResAclLog();
        r.setId(aclId);
        ResAclLog resAclLog = resAclLogMapper.getSingle(r);
        String name = null;
        if (resAclLog.getCategory() != null) {
            ResCategory cat = this.getSingle(resAclLog.getCategory());
            name = cat.getName();
        }
        if (resAclLog.getResource() != null) {
            ResEntity res = resService.getSingle(resAclLog.getResource());
            name = res.getFileSn() + "/" + res.getName();
        }
        resAclLog.setName(name);
        List<UserHonePageDto> addUser = new ArrayList<>();
        List<UserHonePageDto> delUser = new ArrayList<>();
        if ("1".equals(type)) {
            String increasedUsers = resAclLog.getIncreasedUsers();
            List<String> listIncreasedUsers = Arrays.asList(increasedUsers.split(","));
            for (String si : listIncreasedUsers) {
                UserHonePageDto userDto = userService.getUserHonePageDtoByUserId(Integer.valueOf(si));
                addUser.add(userDto);
            }
            query.put("addUser", addUser);
        }else if ("2".equals(type)) {
            String decreasedUsers = resAclLog.getDecreasedUsers();
            List<String> listDecreasedUsers = Arrays.asList(decreasedUsers.split(","));
            for (String sd : listDecreasedUsers) {
                UserHonePageDto userDto = userService.getUserHonePageDtoByUserId(Integer.valueOf(sd));
                delUser.add(userDto);
            }
            query.put("delUser", delUser);
        }
        query.put("resAclLog", resAclLog);
        return query;
    }

    @Override
    public List<Integer> getAllUserIdsByCategory(Integer categoryId) {
        List<Integer> userIDs = resCategoryAclMapper.getAllUserIdsByCategory(categoryId);
        return userIDs;
    }

    @Override
    public List<Integer> getAllUserIdsByFile(Integer resId) {
        List<Integer> listUserIDs =  resAclMapper.getAllUserIdsByFileId(resId);
        return listUserIDs;
    }

    @Override
    public Integer changeCategory(User user, Integer oldCategoryId, Integer newCategoryId, String resLockUuid, String userID) {
        ResCategory catOld = this.getSingle(oldCategoryId);
        Integer state = 1;
        if (catOld.getLockUuid() != null) {
            if (!(resLockUuid.equals(catOld.getLockUuid()))) {
                state = 2;
            } else {
                this.updateResCatUuid(oldCategoryId);
            }
        }else {
            this.updateResCatUuid(oldCategoryId);
        }
        if (state == 1) {
            QueryData param = new QueryData();
            param.put("categoryPath", catOld.getPath());
            ResCategory catLeafs = resMapper.getFileNum(param);
            ResCategory newCategory = this.getSingle(newCategoryId);
            String newName = resService.allParentCategoryName(newCategory.getParent(), newCategory.getName(), null, "1");
            ResCategory cat = this.getSingle(catOld.getParent());
            String oldName = resService.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");
            String allCategoryName = oldName + "转移到" + newName;
            List<Integer> userIds = resCategoryAclMapper.getAllUserIdsByCategory(oldCategoryId);
            resNoticeService.addSpecialAuthUserId(userIds, user);  //填入特殊权限
            if (Byte.valueOf("1").equals(newCategory.getIsTrash())) {
                Integer status = this.handleBorrowedFileByCategoryPath(catOld,"2",user);
                this.updateEveryFolderCategoryPath(catOld.getId(), newCategoryId,newCategory.getPath(),oldCategoryId.toString(),user, "1");
                resNoticeService.sendResFileNotice(user,null,null,null,null,null,null,oldCategoryId,"7",userIds,null,null);
            }else {
                this.updateEveryFolderCategoryPath(catOld.getId(),newCategoryId,newCategory.getPath(),oldCategoryId.toString(),user, "2");
                resNoticeService.sendResFileNotice(user,null,null,null,null,null,null,oldCategoryId,"13",userIds,oldName,newName);
            }
            /*
             List<Integer>  listUserIDs = new ArrayList<>();
            List<ResCategoryAcl> getParentAuth = resCategoryAclMapper.getAllAuthByCategory(newCategoryId);
            if(!getParentAuth.isEmpty()){
                for (ResCategoryAcl rc : getParentAuth) {
                    listUserIDs.add(rc.getUser());
                }
            }*/

            List<Integer> listUserID = JSON.parseArray(userID, Integer.class);
            this.updateFolderAuth(oldCategoryId,listUserID,user,"4", allCategoryName, Long.valueOf(catLeafs.getLeafs()));
        }
        return state;
    }

    @Override
    public HashMap<String, Object> judgementMove(User user, Integer oldCategoryId, Integer fileId, Integer newCategoryId, String type) {
        Integer state = 1;
        ResCategory newCategory = this.getSingle(newCategoryId);
        HashMap<String, Object> map = new HashMap<>();
        if("1".equals(type)){
            ResEntity resEntity = resService.getSingle(fileId);
            if (newCategoryId.equals(resEntity.getCategory())) {
                state = 2;
            } else {
                map.put("resLockUuid", resEntity.getLockUuid());
                if (Byte.valueOf("1").equals(newCategory.getIsTrash())) {
                    if ("1".equals(resEntity.getTeminateState()) || "3".equals(resEntity.getOperation())) {
                        state = 4;
                    }else {
                        state = resService.handleOrGetBorrowedFile(resEntity,"1",user);
                    }
                } else {
                    List<ResCategoryAcl> listCatAclNew = resCategoryAclMapper.getAllAuthByCategory(newCategoryId);
                    for (ResCategoryAcl newR : listCatAclNew) {
                        UserHonePageDto userNew = userService.getUserHonePageDtoByUserId(newR.getUser());
                        newR.setUserName(userNew.getUserName());
                    }
                    List<ResAcl> listCatAclOld = resAclMapper.getFileAuth(fileId);
                    if (!listCatAclOld.isEmpty()) {
                        for (ResAcl oldR : listCatAclOld) {
                            UserHonePageDto userOld = userService.getUserHonePageDtoByUserId(oldR.getUser());
                            oldR.setUserName(userOld.getUserName());
                        }
                    }
                    map.put("listCatAclNew", listCatAclNew);
                    map.put("listCatAclOld", listCatAclOld);
                }
            }
        } else {
            ResCategory catOld = this.getSingle(oldCategoryId);
            if(catOld.getParent()==null && newCategory.getParent() == null){
                state = 2;
            }else {
                if (newCategoryId.equals(catOld.getParent())) {
                    state = 2;
                } else {
                    map.put("resLockUuid", catOld.getLockUuid());
                    if (Byte.valueOf("1").equals(newCategory.getIsTrash())) {
                        ResNumDto resNumDto = resMapper.getResNumDtoByCategoryPath(catOld.getPath());
                        if (resNumDto.getChangeNum() != 0 || resNumDto.getOperationNum() != 0 || resNumDto.getTeminateNum() != 0) {
                            state = 4;
                        }else {
                            state = this.handleBorrowedFileByCategoryPath(catOld,"1",user);
                        }
                    } else {
                        List<ResCategoryAcl> listCatAclNew = resCategoryAclMapper.getAllAuthByCategory(newCategoryId);
                        for (ResCategoryAcl newR : listCatAclNew) {
                            UserHonePageDto userNew = userService.getUserHonePageDtoByUserId(newR.getUser());
                            newR.setUserName(userNew.getUserName());
                        }
                        List<ResCategoryAcl> listCatAclOld = resCategoryAclMapper.getAllAuthByCategory(oldCategoryId);
                        if (!listCatAclOld.isEmpty()) {
                            for (ResCategoryAcl oldR : listCatAclOld) {
                                UserHonePageDto userOld = userService.getUserHonePageDtoByUserId(oldR.getUser());
                                oldR.setUserName(userOld.getUserName());
                            }
                        }
                        map.put("listCatAclNew", listCatAclNew);
                        map.put("listCatAclOld", listCatAclOld);
                    }
                }
            }
        }
        map.put("state", state);
        return map;
    }

    //递归修改子级的文件夹，文件和历史文件的categoryPath
    private void updateEveryFolderCategoryPath(Integer categoryId, Integer parent, String parentPath, String oldCategoryId, User user, String type) {
        ResCategory c = this.getSingle(categoryId);
        ResCategory moveCat = new ResCategory();
        moveCat.setId(c.getId());
        String movePath = parentPath + "," + c.getPath().substring(c.getPath().indexOf(oldCategoryId));
        moveCat.setPath(movePath);
        if ("1".equals(type)) {
            if (parent != null) {
                moveCat.setParent(parent);
            }
            moveCat.setIsTrash(Byte.valueOf("1"));
        }else {
            if (parent != null) {
                moveCat.setParent(parent);
            }
        }
        resCategoryMapper.update(moveCat);
        List<ResEntity> listFile = resMapper.getFileBycategory(categoryId);
        if (!listFile.isEmpty()) {
            for (ResEntity r : listFile) {
                resService.removeStatusByCentre(r.getId(), categoryId, user, r, null,"2");
                /*int delAlloldAuth = resAclMapper.delAllAuthByFile(r.getId());
                Integer insertNewAuthByFile = resAclMapper.insertAuthByCategoryAuth(r);*/
                // resService.changeAuthByMoveFile(r.getId(), categoryId, user, r);
            }
        }
        List<ResCategory> listChild = resCategoryMapper.getAllChildCategoryByParent(categoryId);
        for(ResCategory r : listChild){
            categoryId = r.getId();
            updateEveryFolderCategoryPath(categoryId,null,parentPath,oldCategoryId,user,type);
        }
    }


   /* //递归修改子级的文件夹，文件和历史文件的categoryPath
    private void updateEveryFolderCategoryPath(Integer categoryId, String parentPath, String oldCategoryId, String type) {
        List<ResCategory> listChild = resCategoryMapper.getAllChildCategoryByParent(categoryId);
        for(ResCategory r : listChild){
            ResCategory cat = new ResCategory();
            cat.setId(r.getId());
            String movePath = parentPath + "," + r.getPath().substring(r.getPath().indexOf(oldCategoryId));
            cat.setPath(movePath);
            ResEntity res = new ResEntity();
            res.setCategoryPath(movePath);
            res.setCategory(r.getId());
            HashMap<String, Object> param = new HashMap<>();
            param.put("categoryPath", movePath);
            param.put("categoryPathOld", r.getPath());
            if ("1".equals(type)) {
                cat.setIsTrash(true);
                res.setIsTrash(true);
                param.put("isTrash", true);
            }
            resCategoryMapper.update(cat);
            resMapper.updateResourceCategoryPath(res);
            resHistoryMapper.updateHisResCategoryPath(param);
            categoryId = r.getId();
            updateEveryFolderCategoryPath(categoryId,parentPath,oldCategoryId,type);
        }
    }*/


    //当有资源中心权限是初始资源中心的5个文件夹
    private Integer insertNewFolderForOrg(Integer oid, Integer userId, String userName) {
        ResCategory catBook = new ResCategory();
        catBook.setOrg(oid);
        catBook.setType("1");
        catBook.setName("图书");
        catBook.setCreator(userId);
        catBook.setCreateName(userName);
        catBook.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        catBook.setCategory("1");
        catBook.setValid(true);
        catBook.setChildren(0);
        catBook.setLeafs(0);
        catBook.setSize(BigInteger.valueOf(0));
        catBook.setVersionNo(0);
        catBook.setIsTrash(Byte.valueOf("0"));
        int insertBookCategory = resCategoryMapper.insert(catBook);
        catBook.setPath(catBook.getId().toString());
        int updateBookCategory = resCategoryMapper.update(catBook);

        ResCategory catMange = new ResCategory();
        catMange.setOrg(oid);
        catMange.setType("1");
        catMange.setName("管理性文件");
        catMange.setCreator(userId);
        catMange.setCreateName(userName);
        catMange.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        catMange.setCategory("1");
        catMange.setValid(true);
        catMange.setChildren(0);
        catMange.setLeafs(0);
        catMange.setSize(BigInteger.valueOf(0));
        catMange.setVersionNo(0);
        catMange.setIsTrash(Byte.valueOf("0"));
        int insertMangeCategory = resCategoryMapper.insert(catMange);
        catMange.setPath(catMange.getId().toString());
        int updateMangeCategory = resCategoryMapper.update(catMange);

        ResCategory catNotice = new ResCategory();
        catNotice.setOrg(oid);
        catNotice.setType("1");
        catNotice.setName("通知");
        catNotice.setCreator(userId);
        catNotice.setCreateName(userName);
        catNotice.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        catNotice.setCategory("1");
        catNotice.setValid(true);
        catNotice.setChildren(0);
        catNotice.setLeafs(0);
        catNotice.setSize(BigInteger.valueOf(0));
        catNotice.setVersionNo(0);
        catNotice.setIsTrash(Byte.valueOf("0"));
        int insertNoticeCategory = resCategoryMapper.insert(catNotice);
        catNotice.setPath(catNotice.getId().toString());
        int updateNoticeCategory = resCategoryMapper.update(catNotice);

        ResCategory catBlank = new ResCategory();
        catBlank.setOrg(oid);
        catBlank.setType("1");
        catBlank.setName("空白表格");
        catBlank.setCreator(userId);
        catBlank.setCreateName(userName);
        catBlank.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        catBlank.setCategory("1");
        catBlank.setValid(true);
        catBlank.setChildren(0);
        catBlank.setLeafs(0);
        catBlank.setSize(BigInteger.valueOf(0));
        catBlank.setVersionNo(0);
        catBlank.setIsTrash(Byte.valueOf("0"));
        int insertBlankCategory = resCategoryMapper.insert(catBlank);
        catBlank.setPath(catBlank.getId().toString());
        int updateBlankCategory = resCategoryMapper.update(catBlank);


        ResCategory catRecord = new ResCategory();
        catRecord.setOrg(oid);
        catRecord.setType("1");
        catRecord.setName("表格填写记录");
        catRecord.setCreator(userId);
        catRecord.setCreateName(userName);
        catRecord.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        catRecord.setCategory("1");
        catRecord.setValid(true);
        catRecord.setChildren(0);
        catRecord.setLeafs(0);
        catRecord.setSize(BigInteger.valueOf(0));
        catRecord.setVersionNo(0);
        catRecord.setIsTrash(Byte.valueOf("0"));
        int insertRecordCategory = resCategoryMapper.insert(catRecord);
        catRecord.setPath(catRecord.getId().toString());
        int updateRecordCategory = resCategoryMapper.update(catRecord);

        ResCategory catRecycleBin = new ResCategory();
        catRecycleBin.setOrg(oid);
        catRecycleBin.setType("1");
        catRecycleBin.setIsTrash(Byte.valueOf("1"));
        catRecycleBin.setName("暂时不用的文件");
        catRecycleBin.setCreator(userId);
        catRecycleBin.setCreateName(userName);
        catRecycleBin.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        catRecycleBin.setCategory("1");
        catRecycleBin.setValid(true);
        catRecycleBin.setChildren(0);
        catRecycleBin.setLeafs(0);
        catRecycleBin.setSize(BigInteger.valueOf(0));
        catRecycleBin.setVersionNo(0);
        int insertRecycleBin = resCategoryMapper.insert(catRecycleBin);
        catRecycleBin.setPath(catRecycleBin.getId().toString());
        int updateRecycleBin = resCategoryMapper.update(catRecycleBin);

        return 1;
    }

    //资源中心新增名字的历史记录
    private void insertCategoryHistory(Integer categoryId, String name, Integer creator, String createName, String createDate, Integer versionNo){
        ResCategoryHistory his = new ResCategoryHistory();
        his.setResourceCategory(categoryId);
        his.setName(name);
        his.setCreator(creator);
        his.setCreateName(createName);
        his.setCreateDate(createDate);
        his.setVersionNo(versionNo);
        resCategoryHistoryMapper.insert(his);
    }





    //wyu:Deprecated unused Forder Auth
//    //获取文件夹的权限并放在ResDepartAndEmployee类中
//    private ArrayList<ResDepartAndEmployee> getFolderAuth(Integer categoryId){
//        List<ResCategoryAcl> listAuths = resCategoryAclMapper.getAllAuthByCategory(categoryId);
//        ArrayList<ResDepartAndEmployee> allusers = new ArrayList<ResDepartAndEmployee>();
//        for(ResCategoryAcl rc : listAuths){
//            ResDepartAndEmployee rdae = new ResDepartAndEmployee();
//            rdae.setUid(rc.getUser());
//            allusers.add(rdae);
//        }
//        return allusers;
//    }
//
//    //获取某文件的全部权限放在ResDepartAndEmployee类中
//    private ArrayList<ResDepartAndEmployee> getFileAuth(Integer FileId){
//        List<ResAcl> listAuths = resAclMapper.getFileAuth(FileId);
//        ArrayList<ResDepartAndEmployee> allusers = new ArrayList<ResDepartAndEmployee>();
//        for (ResAcl ra : listAuths) {
//            ResDepartAndEmployee rdae = new ResDepartAndEmployee();
//            rdae.setUid(ra.getUser());
//            allusers.add(rdae);
//        }
//        return allusers;
//    }

//    //此方法是用于获取全部部门和人员的递归操作
//    private List<ResDepartAndEmployee> getAllByOid(Integer pid, List<ResDepartAndEmployee> allusers){
//        List<ResDepartAndEmployee> departs = resCategoryMapper.getDepartment(pid);
//        if (departs.isEmpty()) {
//            List<ResDepartAndEmployee> newUsers = resCategoryMapper.getEmployee(pid);;
//            return newUsers;
//        } else {
//            for (ResDepartAndEmployee r : departs) {
//                ArrayList<ResDepartAndEmployee> allusersnew = new ArrayList<>();
//                List<ResDepartAndEmployee>  res = getAllByOid(r.getDepartid(), allusersnew);
//                if (res == null) {
//                    if (!allusersnew.isEmpty()) {
//                        ResDepartAndEmployee resDepart = (ResDepartAndEmployee) SerializationUtils.clone(r);
//                        List<ResDepartAndEmployee> newListt = resCategoryMapper.getEmployee(r.getDepartid());
//                        for (ResDepartAndEmployee li : newListt) {
//                            allusersnew.add(li);
//                        }
//                        resDepart.setResDepartAndEmployees(allusersnew);
//                        allusers.add(resDepart);
//                    }
//                } else {
//                    if(!res.isEmpty()){
//                        r.setResDepartAndEmployees((ArrayList<ResDepartAndEmployee>) res);
//                        allusers.add(r);
//                    } else {
//                        allusers.add(r);
//                    }
//                }
//            }
//        }
//        return null;
//    }
//
//    //此方法是用于获取上级文件夹权限时的递归操作
//    private List<ResDepartAndEmployee> getUserID (Integer pid, List<ResDepartAndEmployee> allusers, List<ResDepartAndEmployee> newList) {
//        List<ResDepartAndEmployee> departs = resCategoryMapper.getDepartment(pid);
//        if (departs.isEmpty()) {
//            List<ResDepartAndEmployee> newUserss = new ArrayList<ResDepartAndEmployee>();
//            List<ResDepartAndEmployee> newUsers = removeRepartUser(pid,allusers,newUserss);
//            return newUsers;
//        } else {
//            for (ResDepartAndEmployee r : departs) {
//                ArrayList<ResDepartAndEmployee> newList2 = new ArrayList<>();
//                List<ResDepartAndEmployee>  res = getUserID(r.getDepartid(), allusers, newList2);
//                if (res == null) {
//                    if (!newList2.isEmpty()) {
//                        ResDepartAndEmployee resDepart = (ResDepartAndEmployee) SerializationUtils.clone(r);
//                        List<ResDepartAndEmployee> newListt = removeRepartUser(r.getDepartid(),allusers,newList2);
//                        resDepart.setResDepartAndEmployees( (ArrayList<ResDepartAndEmployee>) newListt);
//                        newList.add(resDepart);
//                    }
//                } else {
//                    if(!res.isEmpty()){
//                        r.setResDepartAndEmployees((ArrayList<ResDepartAndEmployee>) res);
//                        newList.add(r);
//                    }
//                }
//            }
//
//        }
//        return null;
//    }
//
//    //此方法是用于获取上级文件夹权限时的递归操作中的去除重复人员操作
//    private List<ResDepartAndEmployee> removeRepartUser(Integer departID,List<ResDepartAndEmployee> allusers,List<ResDepartAndEmployee> newList){
//        List<ResDepartAndEmployee> users = resCategoryMapper.getEmployee(departID);
//        if (!users.isEmpty()) {
//            List<ResDepartAndEmployee> listAll = new ArrayList<>();
//            listAll.addAll(allusers);
//            listAll.addAll(users);
//            for(int i = 0; i < listAll.size() - 1; i++){
//                for (int j = listAll.size() - 1; j > i; j--) {
//                    if (listAll.get(i).getUid() == listAll.get(j).getUid()) {
//                        newList.add(listAll.get(j));
//                        listAll.remove(j);
//                    }
//                }
//            }
//        }
//        return newList;
//    }

    //"其他"部门的权限去重
//    private ArrayList<ResDepartAndEmployee> removeRepartUserByOther(List<ResDepartAndEmployee> listAuthUser, List<ResDepartAndEmployee> listallUser){
//        List<ResDepartAndEmployee> listAll = new ArrayList<ResDepartAndEmployee>();
//        ArrayList<ResDepartAndEmployee> listAuth = new ArrayList<ResDepartAndEmployee>();
//        listAll.addAll(listAuthUser);
//        listAll.addAll(listallUser);
//        for(int i = 0; i < listAll.size() - 1; i++){
//            for (int j = listAll.size() - 1; j > i; j--) {
//                if (listAll.get(i).getUid() == listAll.get(j).getUid()) {
//                    listAuth.add(listAll.get(j));
//                    listAll.remove(j);
//                }
//            }
//        }
//        return listAuth;
//    }

    /**
     *  此方法是一个用于新增文件夹权限的方法
     * @param addType 新增的类型 1是资源中心新增文件夹 2是资料管理新增文件夹权限
     * @param user  当前的登录人
     * @param categoryId 新增的文件夹ID
     * @param parent 当listUserIDs是空的时候才会使用
     * @param listUserIDs 存储的文件夹权限可以是空
     */
    private List<Integer> addCategoryAuth(String addType, User user, Integer categoryId, Integer parent,List<Integer> listUserIDs){
        List<ResCategoryAcl> listCategoryAuth = new ArrayList();
        ResCategoryAcl rca;
        if ("1".equals(addType)) {
            if (listUserIDs == null){
                listUserIDs = new ArrayList<>();
                List<ResCategoryAcl> getParentAuth = resCategoryAclMapper.getAllAuthByCategory(parent);
                if(!getParentAuth.isEmpty()){
                    for (ResCategoryAcl rc : getParentAuth) {
                        rc.setCategory(categoryId);
                        listCategoryAuth.add(rc);
                        listUserIDs.add(rc.getUser());
                    }
                }
            }else {
                for (Integer i : listUserIDs) {
                    ResCategoryAcl resCategoryAcl = new ResCategoryAcl();
                    resCategoryAcl.setCategory(categoryId);
                    resCategoryAcl.setUser(i);
                    listCategoryAuth.add(resCategoryAcl);
                }
            }
        } else {
            rca = new ResCategoryAcl();
            rca.setUser(user.getUserID());
            rca.setCategory(categoryId);
            rca.setCreator(user.getUserID());
            listCategoryAuth.add(rca);
        }
        if(!listCategoryAuth.isEmpty()){
            for (ResCategoryAcl r : listCategoryAuth) {
                Integer status = resCategoryAclMapper.insert(r);
            }
        }
        return listUserIDs;
    }

    //给子文件夹个数，文件个数和文件夹大小这三个字段赋值
    private void addCategoryThreeMessage(ResCategory r, Integer children, String type){
        if(("1").equals(type)){
            if (r.getChildren() == null && r.getLeafs() == null && r.getSize() == null) {
                CategoryTool categoryTool = new CategoryTool();
                categoryTool.updateCategoryMessage(r.getId(), resCategoryMapper, resMapper);
                r.setChildren(children);
                r.setLeafs(categoryTool.getLeafs());
                r.setSize(categoryTool.getSize());
                if(r.getParent() != null){
                    String path = resService.allParentCategoryName(r.getParent(), null, r.getId().toString(), "2");
                    r.setPath(path);
                }
                r.setVersionNo(0);
                Integer state = resCategoryMapper.update(r);
            }
        }
    }

    private List<ResCategory> listFirstCategory(User user){
        //获取文件夹，若是没有文件夹，则进行初始化。
        List<ResCategory> listFirstCategory = resCategoryMapper.getAllCategoryByOidAndTypeForFirst(user.getOid());
        if(listFirstCategory.isEmpty()){
            this.insertNewFolderForOrg(user.getOid(), user.getUserID(), user.getUserName());
            listFirstCategory = resCategoryMapper.getAllCategoryByOidAndTypeForFirst(user.getOid());     //获取第一层全部文件夹
        }
        return listFirstCategory;
    }

    //获取某文件夹下被借阅的文件并对文件进行操作 type=1是查看是否有借阅文件type=2是对借阅文件进行处理
    private Integer handleBorrowedFileByCategoryPath(ResCategory oldCat, String type, User user){
        Integer state = 1;
        HashMap<String, Object> param = new HashMap<>();
        param.put("categoryPath", oldCat.getPath());
        Date timeBegin = NewDateUtils.changeDay(new Date(), -29);
        param.put("timeBegin", timeBegin);
        param.put("isTrash", false);
        List<ReadingRoomEntity> list = readingRoomMapper.getReadRoomByCategoryPath(param);
        if (!list.isEmpty()) {
            if ("1".equals(type)) {
                state = 3;
            } else {
                List<Integer> listUser = new ArrayList<>();
                Set<String> set = new HashSet<>();
                for (ReadingRoomEntity room : list) {
                    if ("1".equals(room.getApproveStatus())) {
                        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(room.getId(), 29, "");
                        ApprovalProcess ap = listAp.get(listAp.size()-1);
                        if (ap.getToMid() != null) {
                            readRoomService.handleBorrowFile(user,room.getId(),ap.getId(),"3",null,null,null,"3");
                        } else {
                            readRoomService.handleBorrowFile(user,room.getId(),ap.getId(),"3",null,null,null,"2");
                        }
                    } else {
                        if(set.add(room.getCreator().toString())){
                            listUser.add(room.getCreator());
                        }
                    }
                }
                if (!listUser.isEmpty()) {
                    for (Integer u : listUser) {
                        String taskbar = "总务人员" +user.getUserName()+ "已改变了" + oldCat.getName() +"的保存位置,故您所借阅的有关文件已无法继续查看。";
                        userSuspendMsgService.saveUserSuspendMsg(1, taskbar, "操作时间 "+user.getUserName()+" "+NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"), u, null, null);
                    }
                }
            }
        }
        return state;
    }

}
