package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.modules.generalAffairs.entity.ApprovalData;
import cn.sphd.miners.modules.generalAffairs.entity.ApprovalInstance;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;

import java.util.List;

/**
 * @ClassName ApprovalInstanceDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/10 11:47
 * @Version 1.0
 */
public class ApprovalInstanceDto extends ApprovalInstance {
    private List<ApprovalProcess> approvalProcessList;
    private List<ApprovalData> approvalDataList;

    public List<ApprovalData> getApprovalDataList() {
        return approvalDataList;
    }

    public void setApprovalDataList(List<ApprovalData> approvalDataList) {
        this.approvalDataList = approvalDataList;
    }

    public List<ApprovalProcess> getApprovalProcessList() {
        return approvalProcessList;
    }

    public void setApprovalProcessList(List<ApprovalProcess> approvalProcessList) {
        this.approvalProcessList = approvalProcessList;
    }
}
