<template>
  <div id="applyReimburseQueryPer">
    <TY_NavTop title="报销申请"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.beginDate | formatDay('YYYY年MM月DD日')}}-{{queryData.endDate | formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent">
              <span v-if="queryData.approveStatus === 2">审批通过</span><span v-if="queryData.approveStatus === 3">被驳回</span>的报销申请
            </div>
            <div class="queryContent" v-if="queryData.feeCatName && !queryData.billCatName">
              申请中需含有费用类别为“{{queryData.feeCatName}}”的票据
            </div>
            <div class="queryContent" v-if="!queryData.feeCatName && queryData.billCatName">
              申请中需含有{{queryData.billCatName}}
            </div>
            <div class="queryContent" v-if="queryData.feeCatName && queryData.billCatName">
              申请中需含有费用类别为“{{queryData.feeCatName}}”的{{queryData.billCatName}}
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的报销申请共如下{{queryData.number}}条 <span v-if="queryData.approveStatus !== 3 && queryData.approveStatus !== 8">，共{{queryData.money}}元。</span>
          </div>
        </div>
      </div>
      <div>
        <div v-if="state == 0">
          <el-table
            :data="queryData.monthlyList"
            fit
            border
            >
            <el-table-column
              prop="applyDate"
              label="月份"
              width="120"
            >
            </el-table-column>
            <el-table-column
              prop="number"
              label="票据数量"
            >
            </el-table-column>
            <el-table-column
              prop="money"
              label="金额"
            >
            </el-table-column>
          </el-table>
        </div>
        <div v-if="state == 1">
          <div class="ui-cells">
            <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in queryData.personnelReimburseList" v-bind:key="index">
              <div class="ui-cell__hd">{{item.createName | stringSplit(4)}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>申请报销 {{item.amount}} 元</div>
                  <div>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'applyReimburseQueryPer',
  data () {
    return {
      loading: true,
      paramQuery: {},
      queryData: {
        beginDate: 1560408078000,
        endDate: 1560408078000,
        approveStatus: 2,
        number: 0,
        money: 0,
        personnelReimburseList: [],
        monthlyList: []
      },
      state: ''
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    let that = this
    this.$http.post('../../../reimburseWindow/myReimburseQuery.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body
      if (data) {
        let data = response.data.data
        console.log(response.data)
        let newMonthlyList = []
        for (let i in data.monthlyList) {
          if (data.monthlyList[i].number) {
            newMonthlyList.push(data.monthlyList[i])
          }
        }
        data.monthlyList = newMonthlyList
        that.state = data.state // 1-有明细 0-没有明细
        that.queryData = data
      } else {
        that.$kiko_message('加载失败')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/applyReimburseDetail/${id}`
      })
    }
  }
}
</script>
