package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.PoContractMaterial;
import cn.sphd.miners.modules.material.entity.PoContractMaterialHistory;

import java.io.Serializable;

public interface PoContractMaterialHistoryDao extends IBaseDao<PoContractMaterialHistory, Serializable> {

    PoContractMaterialHistory insert(PoContractMaterial poContractMaterial,Integer chId,Integer mtHisId);
    void deleteByCommodity(Integer contractMaterial);
}
