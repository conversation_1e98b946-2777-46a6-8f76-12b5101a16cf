<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2360121" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe69e;</span>
                <div class="name">主页</div>
                <div class="code-name">&amp;#xe69e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">后退</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeac4;</span>
                <div class="name">手机</div>
                <div class="code-name">&amp;#xeac4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeaff;</span>
                <div class="name">图文</div>
                <div class="code-name">&amp;#xeaff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeabd;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xeabd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeabf;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xeabf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeac0;</span>
                <div class="name">导入</div>
                <div class="code-name">&amp;#xeac0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeac3;</span>
                <div class="name">锁定</div>
                <div class="code-name">&amp;#xeac3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeac6;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xeac6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeaf1;</span>
                <div class="name">对号</div>
                <div class="code-name">&amp;#xeaf1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeaf2;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xeaf2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeaf3;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xeaf3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeafb;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xeafb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb00;</span>
                <div class="name">Android更多</div>
                <div class="code-name">&amp;#xeb00;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb03;</span>
                <div class="name">箭头_列表向右</div>
                <div class="code-name">&amp;#xeb03;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb04;</span>
                <div class="name">箭头_列表收起</div>
                <div class="code-name">&amp;#xeb04;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb05;</span>
                <div class="name">箭头_列表向左</div>
                <div class="code-name">&amp;#xeb05;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb06;</span>
                <div class="name">箭头_切换向下</div>
                <div class="code-name">&amp;#xeb06;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb07;</span>
                <div class="name">箭头_列表展开</div>
                <div class="code-name">&amp;#xeb07;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb08;</span>
                <div class="name">箭头_切换向上</div>
                <div class="code-name">&amp;#xeb08;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb23;</span>
                <div class="name">电话</div>
                <div class="code-name">&amp;#xeb23;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb24;</span>
                <div class="name">短信</div>
                <div class="code-name">&amp;#xeb24;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb25;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xeb25;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb27;</span>
                <div class="name">喜爱</div>
                <div class="code-name">&amp;#xeb27;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb42;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xeb42;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb43;</span>
                <div class="name">通知中心</div>
                <div class="code-name">&amp;#xeb43;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb44;</span>
                <div class="name">邮件</div>
                <div class="code-name">&amp;#xeb44;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb45;</span>
                <div class="name">登出</div>
                <div class="code-name">&amp;#xeb45;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb46;</span>
                <div class="name">个人信息</div>
                <div class="code-name">&amp;#xeb46;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">通知中心禁用</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1676252580038') format('woff2'),
       url('iconfont.woff?t=1676252580038') format('woff'),
       url('iconfont.ttf?t=1676252580038') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-zhuye"></span>
            <div class="name">
              主页
            </div>
            <div class="code-name">.icon-zhuye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-houtui"></span>
            <div class="name">
              后退
            </div>
            <div class="code-name">.icon-houtui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouji"></span>
            <div class="name">
              手机
            </div>
            <div class="code-name">.icon-shouji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuwen"></span>
            <div class="name">
              图文
            </div>
            <div class="code-name">.icon-tuwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daochu"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.icon-daochu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daoru"></span>
            <div class="name">
              导入
            </div>
            <div class="code-name">.icon-daoru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suoding"></span>
            <div class="name">
              锁定
            </div>
            <div class="code-name">.icon-suoding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.icon-wenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duihao"></span>
            <div class="name">
              对号
            </div>
            <div class="code-name">.icon-duihao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caidan"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.icon-caidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Androidgengduo"></span>
            <div class="name">
              Android更多
            </div>
            <div class="code-name">.icon-Androidgengduo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou_liebiaoxiangyou"></span>
            <div class="name">
              箭头_列表向右
            </div>
            <div class="code-name">.icon-jiantou_liebiaoxiangyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou_liebiaoshouqi"></span>
            <div class="name">
              箭头_列表收起
            </div>
            <div class="code-name">.icon-jiantou_liebiaoshouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou_liebiaoxiangzuo"></span>
            <div class="name">
              箭头_列表向左
            </div>
            <div class="code-name">.icon-jiantou_liebiaoxiangzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou_qiehuanxiangxia"></span>
            <div class="name">
              箭头_切换向下
            </div>
            <div class="code-name">.icon-jiantou_qiehuanxiangxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou_liebiaozhankai"></span>
            <div class="name">
              箭头_列表展开
            </div>
            <div class="code-name">.icon-jiantou_liebiaozhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou_qiehuanxiangshang"></span>
            <div class="name">
              箭头_切换向上
            </div>
            <div class="code-name">.icon-jiantou_qiehuanxiangshang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianhua"></span>
            <div class="name">
              电话
            </div>
            <div class="code-name">.icon-dianhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duanxin"></span>
            <div class="name">
              短信
            </div>
            <div class="code-name">.icon-duanxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.icon-fenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiai"></span>
            <div class="name">
              喜爱
            </div>
            <div class="code-name">.icon-xiai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.icon-xiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhizhongxin"></span>
            <div class="name">
              通知中心
            </div>
            <div class="code-name">.icon-tongzhizhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youjian"></span>
            <div class="name">
              邮件
            </div>
            <div class="code-name">.icon-youjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dengchu"></span>
            <div class="name">
              登出
            </div>
            <div class="code-name">.icon-dengchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenxinxi"></span>
            <div class="name">
              个人信息
            </div>
            <div class="code-name">.icon-gerenxinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhizhongxinjinyong"></span>
            <div class="name">
              通知中心禁用
            </div>
            <div class="code-name">.icon-tongzhizhongxinjinyong
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuye"></use>
                </svg>
                <div class="name">主页</div>
                <div class="code-name">#icon-zhuye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-houtui"></use>
                </svg>
                <div class="name">后退</div>
                <div class="code-name">#icon-houtui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouji"></use>
                </svg>
                <div class="name">手机</div>
                <div class="code-name">#icon-shouji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuwen"></use>
                </svg>
                <div class="name">图文</div>
                <div class="code-name">#icon-tuwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daochu"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#icon-daochu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daoru"></use>
                </svg>
                <div class="name">导入</div>
                <div class="code-name">#icon-daoru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suoding"></use>
                </svg>
                <div class="name">锁定</div>
                <div class="code-name">#icon-suoding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#icon-wenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duihao"></use>
                </svg>
                <div class="name">对号</div>
                <div class="code-name">#icon-duihao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#icon-caidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Androidgengduo"></use>
                </svg>
                <div class="name">Android更多</div>
                <div class="code-name">#icon-Androidgengduo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou_liebiaoxiangyou"></use>
                </svg>
                <div class="name">箭头_列表向右</div>
                <div class="code-name">#icon-jiantou_liebiaoxiangyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou_liebiaoshouqi"></use>
                </svg>
                <div class="name">箭头_列表收起</div>
                <div class="code-name">#icon-jiantou_liebiaoshouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou_liebiaoxiangzuo"></use>
                </svg>
                <div class="name">箭头_列表向左</div>
                <div class="code-name">#icon-jiantou_liebiaoxiangzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou_qiehuanxiangxia"></use>
                </svg>
                <div class="name">箭头_切换向下</div>
                <div class="code-name">#icon-jiantou_qiehuanxiangxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou_liebiaozhankai"></use>
                </svg>
                <div class="name">箭头_列表展开</div>
                <div class="code-name">#icon-jiantou_liebiaozhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou_qiehuanxiangshang"></use>
                </svg>
                <div class="name">箭头_切换向上</div>
                <div class="code-name">#icon-jiantou_qiehuanxiangshang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianhua"></use>
                </svg>
                <div class="name">电话</div>
                <div class="code-name">#icon-dianhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duanxin"></use>
                </svg>
                <div class="name">短信</div>
                <div class="code-name">#icon-duanxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#icon-fenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiai"></use>
                </svg>
                <div class="name">喜爱</div>
                <div class="code-name">#icon-xiai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#icon-xiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhizhongxin"></use>
                </svg>
                <div class="name">通知中心</div>
                <div class="code-name">#icon-tongzhizhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youjian"></use>
                </svg>
                <div class="name">邮件</div>
                <div class="code-name">#icon-youjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dengchu"></use>
                </svg>
                <div class="name">登出</div>
                <div class="code-name">#icon-dengchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenxinxi"></use>
                </svg>
                <div class="name">个人信息</div>
                <div class="code-name">#icon-gerenxinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhizhongxinjinyong"></use>
                </svg>
                <div class="name">通知中心禁用</div>
                <div class="code-name">#icon-tongzhizhongxinjinyong</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
