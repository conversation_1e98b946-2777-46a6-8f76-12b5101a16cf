package cn.sphd.miners.modules.dmmSchedule.service;

import cn.sphd.miners.modules.dmmSchedule.entity.DmmJobMsg;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.List;

/**
 * Created by Administrator on 2020/1/2.
 */
public interface DmmJobMsgService{

    DmmJobMsg getDmmJobMsgById(Integer id);

    DmmJobMsg saveDmmJobMsg(Integer superscript, String title, String content, String memo, Long toUserId, Integer jobId, Integer scheduleId);

    void updateDmmJobMsg(DmmJobMsg DmmJobMsg, String state, Long toUserId);//提醒 单条减角标

    List<DmmJobMsg> getDmmJobMsgByJobId(Integer jobId, String state);

    List<DmmJobMsg> getDmmJobMsgByScheduleId(Integer scheduleId, String state);

    List<DmmJobMsg> getDmmJobMsgBuUserId(Long accId, String state);

    void  deleteAllDmmJobMsg(List<DmmJobMsg> DmmJobMsgList);


}
