<template>
  <div class="investigationContainer">
    <div class="mainCon11" v-if="mainpage===11">
      <div class="clear line" style="text-align: right">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="goStop" >已终止的调查</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL20" @click="createInv" >发起新的调查</span>
        <br>
        <div class="ty-right searchSect searchSect2">
          <el-input placeholder="请输入关键字或素材代号" v-model="mainCon11Data.searchKey">
            <template #prepend>查找</template>
            <template #append><span @click="searchBtn1">确定</span></template>
          </el-input>
        </div>
      </div>
      <div>
        <p class="marTop20">进行中的调查共以下 <span class="num1">{{ mainCon11Data.num1 }}</span> 个</p>
        <table class="ty-table ty-table-control widthTab">
          <tr>
            <td>所用问卷</td>
            <td>调查发起时间</td>
            <td>问卷提交截止日期</td>
            <td>已回收的问卷</td>
            <td>其他操作</td>
          </tr>
          <tr v-for="(invst, index) in mainCon11Data.list" :key="index">
            <td v-html="invst.name"></td>
            <td>{{ invst.createName }} {{ new Date(invst.createDate ).format("yyyy-MM-dd hh:mm:ss") }}</td>
            <td>{{ new Date(invst.endTime).format("yyyy-MM-dd")}}</td>
            <td class="ty-td-control ">
              <span>{{ invst.feedbackNum }}份</span>
              <span class="ty-color-blue" @click="scanAns(invst)">查看全部</span>
              <span class="ty-color-blue" @click="analysisAns(invst, 11)">调查分析</span>
              <span class="ty-color-blue" @click="share(invst)">分享</span>
            </td>
            <td>
              <span class="ty-color-blue funBtn" @click="manageInv(invst)">管理 </span>
            </td>
          </tr>
        </table>
        <div id="page11">
          <TyPage v-if="mainCon11Data.pageShow"
                  :curPage="mainCon11Data.pageInfo.currentPageNo" :pageSize="mainCon11Data.pageInfo.pageSize"
                  :allPage="mainCon11Data.pageInfo.totalPage" :pageClickFun="pageClick11"></TyPage>

        </div>
      </div>
    </div>
    <!--  搜索 1-->
    <div class="mainCon22" v-if="mainpage===22">
      <div>
        <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" @click="back22">返回</span>
      </div>
      <div>
        <p class="marTop20">进行中的调查共以下 <span class="num1">{{ mainCon22Data.num1 }}</span> 个</p>
        <table class="ty-table ty-table-control widthTab">
          <tr>
            <td>所用问卷</td>
            <td>调查发起时间</td>
            <td>问卷提交截止日期</td>
            <td>已回收的问卷</td>
            <td>其他操作</td>
          </tr>
          <tr v-for="(invst, index22) in mainCon22Data.list" :key="index22">
            <td>{{ invst.name }}</td>
            <td>{{ invst.createName }} {{ new Date(invst.createDate ).format("yyyy-MM-dd hh:mm:ss") }}</td>
            <td>{{ new Date(invst.endTime ).format("yyyy-MM-dd") }}</td>
            <td class="ty-td-control">
              <span> {{ invst.feedbackNum }}份</span>
              <span class="ty-color-blue funBtn" @click="scanAns(invst)">查看全部</span>
              <span class="ty-color-blue funBtn" @click="analysisAns(invst,22)">调查分析</span>
            </td>
            <td>
              <span class="ty-color-blue funBtn" @click="manageInv(invst)">管理 </span>
            </td>
          </tr>

        </table>
        <div id="page1Search">
          <TyPage v-if="mainCon22Data.pageShow"
                  :curPage="mainCon22Data.pageInfo.currentPageNo" :pageSize="mainCon22Data.pageInfo.pageSize"
                  :allPage="mainCon22Data.pageInfo.totalPage" :pageClickFun="pageClick22"></TyPage>

        </div>

      </div>
    </div>
    <!--  已终止的调查-->
    <div class="mainCon55" v-if="mainpage===55">
      <div class="ty-right searchSect">
        <div class="ty-left keywordSearch"><span class="ty-left">其他年份</span>
          <div class="inputBox ty-right">
            <el-date-picker v-model="mainCon55Data.searchYear" type="year"
                            format="YYYY" value-format="YYYY"
                            placeholder="请选择">
            </el-date-picker>
          </div>
        </div>
        <button class="ty-left ty-btn ty-btn-blue ty-btn-big" @click="searchBtn2">确定</button>

      </div>
      <p>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="back55">返回</span>
      </p>
      <p class="marTop20"><span class="year">{{ mainCon55Data.year }}</span>年已终止的调查共以下<span class="num">{{ mainCon55Data.num }}</span>份 </p>
      <table class="ty-table ty-table-control" >
        <tr>
          <td>月份</td>
          <td>已终止的调查</td>
        </tr>
        <tr v-for="(item, index55) in mainCon55Data.list" :key="index55">
          <td>{{ item.month  }}</td>
          <td>
            <span> {{ item.sum }} 份</span>
            <span class="ty-color-blue funBtn" @click="invStopList(item)">查看</span>
          </td>
        </tr>
      </table>

    </div>
    <!--  已终止的调查 列表 -->
    <div class="mainCon66" v-if="mainpage===66">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="back66">返回</span>
      </div>
      <div>
        <p class="marTop20"><span class="month">{{ mainCon66Data.month }}</span>已终止的调查共以下<span class="num6">{{ mainCon66Data.num6 }}</span>个。</p>
        <table class="ty-table ty-table-control widthTab">
          <tr>
            <td>所用问卷</td>
            <td>调查发起时间</td>
            <td>问卷提交截止日期</td>
            <td>已回收的问卷</td>
            <td>其他操作</td>
          </tr>
          <tr v-for="(invst, index) in mainCon66Data.list" :key="index">
            <td v-html="invst.name"></td>
            <td>{{ invst.createName }} {{ new Date(invst.createDate ).format("yyyy-MM-dd hh:mm:ss") }}</td>
            <td>{{ new Date(invst.endTime).format("yyyy-MM-dd") }}</td>
            <td class="ty-td-control">
              <span>{{ invst.feedbackNum }}份</span>
              <span class="ty-color-blue" @click="scanAns(invst)">查看全部</span>
              <span class="ty-color-blue" @click="analysisAns(invst,66)">调查分析</span>
              <span class="ty-color-blue" @click="share(invst)">分享</span>
            </td>
            <td>
              <span class="ty-color-blue funBtn" @click="manageInv(invst)">管理 </span>
            </td>
          </tr>
        </table>
        <div id="page66">
          <TyPage v-if="mainCon66Data.pageShow"
                  :curPage="mainCon66Data.pageInfo.currentPageNo" :pageSize="mainCon66Data.pageInfo.pageSize"
                  :allPage="mainCon66Data.pageInfo.totalPage" :pageClickFun="pageClick11"></TyPage>
        </div>
      </div>

    </div>
    <!--  查看问卷-->
    <div class="mainCon77" v-if="mainpage===77">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back77" @click="back77">返回</span>
      </div>
      <div class="marTop20">
        <div class="investigation">
          <p class="memo"></p>
          <h3 class="ttl">{{ mainCon77Data.ttl }}</h3>
          <div class="preface">{{ mainCon77Data.preface }}</div>
          <div class="main">
            <div class="cat" v-for="cat in mainCon77Data.list ">
              <h4>{{ cat.name || "" }}</h4>
              <div class="ques" v-for="q in cat.investigateQuestionList">
                <p>{{  q.orders }}、{{ q.content }}</p>
                <div class="ops">
                  <span v-for="ans in q.investigateAnswerList">{{ ans.content }}</span><br/>
                </div>
              </div>
            </div>
          </div>

        </div>

      </div>
    </div>
    <!--  查看答卷列表 -->
    <div class="mainCon88" v-if="mainpage===88">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back88" v-if="mainCon88Data.back88Show" @click="back88">返回</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back881" v-if="mainCon88Data.back88S1how" @click="back88">返回主页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 back881 marL20" v-if="mainCon88Data.back88S1how" @click="back881">返回上一页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right marLeft100 exportDataSet" v-if="mainCon88Data.exportDataSet" @click="exportDataSetBtn">原始数据导出</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right marLeft100"  @click="dataShowSet">本页展示设置</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right notJiData" v-if="mainCon88Data.notJiData" @click="notJiData">未纳入统计的数据</span>
      </div>
      <div class="marTop20">
        <p>截至 <span class="time">{{ mainCon88DataTime }}</span>，问卷已回收<span class="num">{{ mainCon88Data.num }}</span>份，具体如下：</p>
        <div style="width: 100%; overflow: auto; padding:0 0 20px 0;">
          <table class="ty-table ty-table-control" id="tab88" :style="'width:' + this.mainCon88Data.tab88Width + 'px' " >
            <tr>
              <td><span class="lineDot" >提交时间</span></td>
              <td v-for="(tag, indexTag) in mainCon88Data.tags" :key="indexTag" :title="tag.content">
                <span class="lineDot">{{ tag.content }}</span></td>
              <td>操作</td>
            </tr>
            <tr v-for="(item, index8) in mainCon88Data.list" :key="index8">
              <td>{{ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss") }}</td>
              <td v-for=" tagC in item.tds">{{ tagC }}</td>
              <td>
                <span class="ty-color-blue funBtn" @click="scanAnsInfo(item)">查看</span>
                <span v-if="mainCon88Data.typeStr === '未纳入统计'" class="ty-color-blue funBtn" @click="notJiBtn(item,1)">恢复纳入统计</span>
                <span v-else class="ty-color-blue funBtn" @click="notJiBtn(item,0)">不纳入统计</span>

              </td>
            </tr>
          </table>
        </div>
        <div id="page88">
          <TyPage v-if="mainCon88Data.pageShow"
                  :curPage="mainCon88Data.pageInfo.currentPageNo" :pageSize="mainCon88Data.pageInfo.pageSize"
                  :allPage="mainCon88Data.pageInfo.totalPage" :pageClickFun="pageClick88"></TyPage>

        </div>
      </div>
    </div>
    <!--  调查分析 -->
    <div class="mainCon99" v-if="mainpage===99">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="back99">返回</span>
      </div>
      <div class="marTop20">
        <div class="line30">
          <p class="df12">  本次调查共有<span class="qMun">{{ mainCon99Data.qMun }}</span>道题，
            截至<span class="endTime">{{ mainCon99DataEndTime }}</span> ，
            共回收<span class="ansNum">{{ mainCon99Data.ansNum }}</span>份</p>
          <p>点击查看，可查看相应题型的数据</p>
        </div>
        <table class="ty-table ty-table-control" id="analysisAns">
          <tr>
            <td>题型</td>
            <td>数量</td>
            <td>操作</td>
          </tr>
          <tr>
            <td>问答题（含常规问答题与特殊型式的问答题）<br>
              年、月或日期的题<br>
              国内省、市、地区或地址的题</td>
            <td class="q1Num">{{ mainCon99Data.q1Num || 0 }}</td>
            <td><span class="ty-color-blue funBtn" @click="getQAnsysList(1)">查看</span></td>
          </tr>
          <tr>
            <td>正误判断题、常规单选题</td>
            <td class="q2Num">{{ mainCon99Data.q2Num || 0 }}</td>
            <td><span class="ty-color-blue funBtn" @click="getQAnsysList(2)">查看</span></td>
          </tr>
          <tr>
            <td>常规多选题</td>
            <td class="q3Num">{{ mainCon99Data.q3Num || 0 }}</td>
            <td><span class="ty-color-blue funBtn" @click="getQAnsysList(3)">查看</span></td>
          </tr>
        </table>
      </div>
    </div>
    <!--  各题型 问题查看-->
    <div class="mainCon1010" v-if="mainpage===1010">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="back1010">返回</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 marL20" @click="exportBtn(1)">导出</span>
      </div>
      <div class="marTop20">
        <p class="df1"></p>
        <div class="line30">
          <div class="type1" v-if="mainCon1010Data.type === 1">
            常规问答题与系统中特殊型式的问答题共<span class="q1Num2"></span>道，情况如下
            <div class="type1List" style="padding-left: 32px;">
              <div class="line" v-for="(item, index1010_1) in mainCon1010Data.list" :key="index1010_1">
                <p>{{ item.orders }}、{{ item.content }}</p>
                <p>
                  <span>回收的问卷中，{{ item.answerSum }}份回答了本题 </span>
                  <span class="linkBtn seeThisAnsBtn" @click="seeThisAns(item)">查看本题的全部回答</span>
                </p>
              </div>
            </div>
            <div class="pagetype1">
              <TyPage v-if="mainCon1010Data.pageShow"
                      :curPage="mainCon1010Data.pageInfo.currentPageNo" :pageSize="mainCon1010Data.pageInfo.pageSize"
                      :allPage="mainCon1010Data.pageInfo.totalPage" :pageClickFun="pageClick1010"></TyPage>

            </div>
          </div>
          <div class="type2" v-if="mainCon1010Data.type === 2">
            常规单选题与正误判断题共<span class="q2Num2"></span>道，情况如下
            <div class="type2List" style="padding-left: 32px;">
              <div class="line" v-for="(item, index1010_2) in mainCon1010Data.list" :key="index1010_2">
                <p>{{item.orders }}、 {{ item.content }}</p>
                <p>回收的问卷中，{{ item.answerSum }}份回答了本题 ，情况如下：</p>
                <table class="ty-table">
                  <tr><td class="oneLine">选项</td><td width="150">选择该项的份数</td><td width="150">占比</td></tr>
                  <tr v-for="(option, indexOption) in item.respInvestigateQuestionKeyList" :key="indexOption">
                    <td class="oneLine">{{ option.content }}</td>
                    <td>{{ option.sum }}</td>
                    <td>{{ (option.percentage *100).toFixed(2) }}%</td>
                  </tr>
                </table>
              </div>
            </div>
            <div class="pagetype2">
              <TyPage v-if="mainCon1010Data.pageShow"
                      :curPage="mainCon1010Data.pageInfo.currentPageNo" :pageSize="mainCon1010Data.pageInfo.pageSize"
                      :allPage="mainCon1010Data.pageInfo.totalPage" :pageClickFun="pageClick1010"></TyPage>

            </div>
          </div>
          <div class="type3" v-if="mainCon1010Data.type === 3">
            <span class="df123">常规多选题共<span class="q3Num2"></span>道，各题各个选项被选择的情况统计如下</span>
            <div class="type3List" style="padding-left: 32px;">
              <div class="line" v-for="(item, index1010_2) in mainCon1010Data.list" :key="index1010_2">
                <p>{{item.orders }}、 {{ item.content }}</p>
                <p>回收的问卷中，{{ item.answerSum }}份回答了本题 ，情况如下：
                  <span class="linkBtn" style="margin-left: 200px;" @click="changeToNext(item)">切换至答卷者选择结果的统计</span></p>
                <table class="ty-table">
                  <tr><td class="oneLine">选项</td><td width="150">选择该项的份数</td><td width="150">占比</td></tr>
                  <tr v-for="(option, indexOption) in item.respInvestigateQuestionKeyList" :key="indexOption">
                    <td class="oneLine">{{ option.content }}</td>
                    <td>{{ option.sum }}</td>
                    <td>{{ (option.percentage *100).toFixed(2) }}%</td>
                  </tr>
                </table>
              </div>

            </div>
            <div class="pagetype3">
              <TyPage v-if="mainCon1010Data.pageShow"
                      :curPage="mainCon1010Data.pageInfo.currentPageNo" :pageSize="mainCon1010Data.pageInfo.pageSize"
                      :allPage="mainCon1010Data.pageInfo.totalPage" :pageClickFun="pageClick1010"></TyPage>

            </div>
          </div>
        </div>
      </div>
    </div>
    <!--  问答题的 答案 详情 -->
    <div class="mainCon1111" v-if="mainpage===1111">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right allDataBtn" v-if="mainCon1111Data.allDataBtn" @click="allDataBtn">全部数据</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="back1111">返回</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 marL20" @click="exportBtn(2)" >导出</span>
      </div>
      <div class="marTop20">
        <p class="qInfo">{{ mainCon1111Data.qInfo }}</p>
        <p><span class="qwe line30">{{ mainCon1111Data.qwe }}</span></p>
        <div class="qAnsList">

          <table class="ty-table" v-if="mainCon1111Data.case == 1">
            <tr>
              <td>提交时间</td>
              <td v-for="(ttl, indexTtl) in mainCon1111Data.ttlList" :key="indexTtl">
                {{ ttl.content }}
              </td>
            </tr>
            <tr v-for="(item, indexTtlitem) in mainCon1111Data.list " :key="indexTtlitem">
              <td>{{ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")  }}</td>
              <td v-for="(ansI, indexAnsI) in item.respInvestigateAnswerList" :key="indexAnsI">
                {{ ansI.content || '' }}
              </td>
            </tr>
          </table>

          <!--    4-加具体地址的， 3-省市区-->
          <table class="ty-table" v-if="mainCon1111Data.case == 2">
            <tr><td>省</td><td>市</td><td>地区</td></tr>
            <tr v-for="(trItem, indexTrItem) in  mainCon1111Data.newTrList" :key="indexAreaI">
              <td class="ty-td-control">{{ trItem.td1.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(trItem.td1)">{{ trItem.td1.sum }}位</span></td>
              <td class="ty-td-control">
                <div class="tdSty" v-for="(cityItem, indexCityItem) in trItem.td2" :key="indexCityItem" :style="{ 'line-height' : (cityItem.td2Num * 40) + 'px'}">
                   {{ cityItem.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(cityItem)">{{ cityItem.sum }}位</span>
                </div>
              </td>
              <td class="ty-td-control">
                <div class="tdSty" v-for="(quItem, indexQuItem) in trItem.td3" :key="indexQuItem" style="line-height: 40px;">
                  {{ quItem.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(quItem)">{{ quItem.sum }}位</span>
                </div>
              </td>
            </tr>
          </table>

          <table class="ty-table" v-if="mainCon1111Data.case == 3">
            <tr><td>省</td></tr>
            <tr v-for="(trItem, indexTrItem) in  mainCon1111Data.newTrList" :key="indexAreaI">
              <td class="ty-td-control">{{ trItem.td1.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(trItem.td1)">{{ trItem.td1.sum }}位</span></td>
            </tr>
          </table>
          <table class="ty-table" v-if="mainCon1111Data.case == 4">
            <tr><td>省</td><td>市</td></tr>
            <tr v-for="(trItem, indexTrItem) in  mainCon1111Data.newTrList" :key="indexAreaI">
              <td class="ty-td-control">{{ trItem.td1.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(trItem.td1)">{{ trItem.td1.sum }}位</span></td>
              <td class="ty-td-control">
                <div v-for="(cityItem, indexCityItem) in trItem.td2" :key="indexCityItem" :style="{ 'line-height' : cityItem.td2Num+ 'px'}">
                  {{ cityItem.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(cityItem)">{{ cityItem.sum }}位</span>
                </div>
              </td>
            </tr>
          </table>
          <table class="ty-table" v-if="mainCon1111Data.case == 5">
            <tr><td>年</td></tr>
            <tr v-for="(trItem, indexTrItem) in  mainCon1111Data.newTrList" :key="indexAreaI">
              <td class="ty-td-control">{{ trItem.td1.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(trItem.td1)">{{ trItem.td1.sum }}位</span></td>
            </tr>
          </table>
          <table class="ty-table" v-if="mainCon1111Data.case == 6">
            <tr><td>年</td><td>月</td></tr>
            <tr v-for="(trItem, indexTrItem) in  mainCon1111Data.newTrList" :key="indexAreaI">
              <td class="ty-td-control">{{ trItem.td1.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(trItem.td1)">{{ trItem.td1.sum }}位</span></td>
              <td class="ty-td-control">
                <div class="tdSty" v-for="(cityItem, indexCityItem) in trItem.td2" :key="indexCityItem" style="line-height:40px;">
                  {{ cityItem.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(cityItem)">{{ cityItem.sum }}位</span>
                </div>
              </td>
            </tr>
          </table>
          <table class="ty-table" v-if="mainCon1111Data.case == 7">
            <tr><td>年</td><td>月</td><td>日</td></tr>
            <tr v-for="(trItem, indexTrItem) in  mainCon1111Data.newTrList" :key="indexAreaI">
              <td class="ty-td-control">{{ trItem.td1.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(trItem.td1)">{{ trItem.td1.sum }}位</span></td>
              <td class="ty-td-control">
                <div v-for="(cityItem, indexCityItem) in trItem.td2" :key="indexCityItem" :style="{ 'line-height' : cityItem.td2Num+ 'px'}">
                  <span>{{ cityItem.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(cityItem)">{{ cityItem.sum }}位</span></span>
                </div>
              </td>
              <td class="ty-td-control">
                <div v-for="(quItem, indexQuItem) in trItem.td3" :key="indexQuItem">
                  <span>{{ quItem.name }} <span class="ty-color-blue" @click="getYearAreaDetailsBtn(quItem)">{{ quItem.sum }}位</span></span>
                </div>
              </td>
            </tr>
          </table>

        </div>
        <div class="page11">

        </div>
      </div>
    </div>
    <!--  多选题的 切换至答卷者选择结果的统计报表 -->
    <div class="mainCon1212" v-if="mainpage===1212">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="back1111">返回</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 marL20" @click="exportBtn(3)">导出</span>
      </div>
      <div class="marTop20">
        <div>
          <div class="type3" style="padding-left: 32px; ">
            <span class="chang1"></span>
            <p class="questionInfo">{{ mainCon1212Data.questionInfo }}</p>
            <table class="ty-table optionTab">
              <tr><td>选项</td></tr>
              <tr v-for="(op, indexOp) in mainCon1212Data.ops" :key="indexOp">
                <td><span class="circleOrd">{{ op.orders }}</span>{{ op.content }}</td>
              </tr>
            </table>
            <div class="marTop20">问卷共回收 <span class="pall">{{ mainCon1212Data.pall }}</span>份，其中 <span class="pans">{{ mainCon1212Data.pans }}</span>份回答了本题，回答者按选择结果统计如下：</div>
            <table class="ty-table ansTab">
              <tr><td>选择结果</td><td>选择该项的数量</td><td>占比</td></tr>
              <tr v-for="(ans, indexAns) in mainCon1212Data.ansList" :key="indexAns">
                <td><span class="circleOrd">{{ formatOption(ans.answer, mainCon1212Data.ops)  }}</span></td>
                <td>{{ ans.sum }}</td>
                <td>{{ (ans.percentage *100).toFixed(2)  }}%</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    <!--  年月日 省市区的 详情 和  全部数据 -->
    <div class="mainCon1313" v-if="mainpage===1313">
      <div class="clear line">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="back1313">返回</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 marL20" @click="exportBtn(4)">导出</span>
      </div>
      <div class="marTop20">
        <p class="qInfo13">{{ mainCon1313Data.qInfo13 }}</p>
        <p><span class="qwe13">{{ mainCon1313Data.qwe13 }}</span></p>
        <div class="qAnsList13">
          <table class="ty-table">
            <tr><td>提交时间</td>
              <td v-for="(ttl, indexTtl) in mainCon1313Data.ttlList" :key="indexTtl">{{ ttl.content }}</td>
            </tr>
            <tr v-for="(item, indexItem) in mainCon1313Data.list" :key="indexItem">
              <td>{{ new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss")  }}</td>
              <td v-for="(ansI, indexAns) in item.respInvestigateAnswerList" :key="indexAns">
                {{ ansI.content || '' }}
              </td>
            </tr>
          </table>
        </div>
        <div class="page13">
          <TyPage v-if="mainCon1313Data.pageShow"
                  :curPage="mainCon1313Data.pageInfo.currentPageNo" :pageSize="mainCon1313Data.pageInfo.pageSize"
                  :allPage="mainCon1313Data.pageInfo.totalPage" :pageClickFun="pageClick1313"></TyPage>

        </div>
      </div>
    </div>

    <div class="bonceC">

      <!--  调查管理  -->
      <TyDialog id="manageInv" v-if="manageInvData.visible" width="460" dialogTitle="调查管理" color="blue" :dialogHide="hideManageInv">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideManageInv">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="manageInvOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="manageInvBC" style="line-height: 30px;">
            <div>
              使用Wonderss的手机端，即可将所发起的调查问卷分享到微信！<br>
              <span class="blueTip">注：Wonderss手机端里，该功能在“工作”下的“调查管理”中</span>
              <div style="text-align: right">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="shareView">分享到微信后的效果</span>
              </div>
            </div>
            <div>
              问卷提交后，多长时间内可修改？请设置 <br>
              <el-select class="wid100" v-model="manageInvData.manage_upTim" placeholder="请选择" @change="showNext">
                <el-option label="1分钟" value="1"></el-option>
                <el-option label="10分钟" value="10"></el-option>
                <el-option label="20分钟" value="20"></el-option>
                <el-option label="30分钟" value="30"></el-option>
              </el-select>
            </div>
            <div class="endDateFm">
              <el-radio v-model="manageInvData.endType" :label="1">修改问卷提交的截止日期</el-radio>
              <span class="ty-btn ty-btn-blue ty-right ty-btn-big ty-circle-5" @click="endtimLog">截止日期履历</span>
            </div>
            <div class="timCC" v-if="manageInvData.endType === 1">
              <el-date-picker style="width: 100%" v-model="manageInvData.upEndDate" type="date" placeholder="请选择 截止日期">
              </el-date-picker>
            </div>
            <div class="endTestCC">
              <el-radio v-model="manageInvData.endType" :label="2">立即终止本次调查</el-radio>
            </div>
          </div>
        </template>
      </TyDialog>

      <!--  调查管理2  -->
      <TyDialog id="manageInv2" v-if="manageInv2Data.visible" width="588" dialogTitle="调查管理" color="blue" :dialogHide="hideCreateInv">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideManageInv2">关闭</span>
        </template>
        <template #dialogBody>
          <div class="manageInvBC" style="line-height: 30px;">
            <div class="flexcc">
              <div>
                <p>本次调查问卷提交的截止日期已过！</p>
                <p>该截止日期：<span class="endtim">{{ manageInv2Data.endtim }}</span></p>
              </div>
              <div>
                <span class="ty-btn ty-btn-blue ty-right ty-btn-big ty-circle-5" @click="endtimLog">截止日期履历</span>
              </div>
            </div>
            <div>
              使用Wonderss的手机端，即可将所发起的调查问卷分享到微信！<br>
              <span class="blueTip">注：Wonderss手机端里，该功能在“工作”下的“调查管理”中</span>
              <div style="text-align: right">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="shareView">分享到微信后的效果</span>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>

      <!--   新建调查  -->
      <TyDialog id="createInv" v-if="createInvData.visible" width="450" dialogTitle="调查管理" color="green" :dialogHide="hideCreateInv">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideCreateInv">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="createInvOk">确定</span>
        </template>
        <template #dialogBody>
          <div class="manageInvBC" style="line-height: 30px;">
            <div>
              <p>请选择本次要调查的问卷 <i class="ty-color-red">*</i>
                <span class="linkBtn ty-right" data-fun="prevScan">预 览</span>
              </p>
              <el-select class="wid100" v-model="createInvData.preID" placeholder="请选择" @change="showNext">
                <el-option v-for="(op, indexItem) in createInvData.bankList" :key="indexItem"
                     :label="op.title" :value="op.id" >
                </el-option>
              </el-select>
            </div>
            <div class="marTp16px" v-if="createInvData.marTp16px">
              <div class="flexcc">
                <div>
                  <p>查看或更换封面图片，请点击“封面图片”	</p>
                  <p class="blueTip">注：封面图片指分享至微信的链接封面上的图片。</p>
                </div>
                <div class="fengm" @click="openUploadFImg">
                  <div class="fImg">
                    <img :src="createInvData.defaultImg" alt="封面图片">
                  </div>
                </div>
              </div>
              <div style="display: none;">
                <div id="uploadCC">
                </div>
              </div>
              <div>
                <p> 问卷提交后，多长时间内可修改？请设置 </p>
                <el-select class="wid100" v-model="createInvData.add_min" placeholder="请选择" @change="showNext">
                  <el-option label="1分钟" value="1"></el-option>
                  <el-option label="10分钟" value="10"></el-option>
                  <el-option label="20分钟" value="20"></el-option>
                  <el-option label="30分钟" value="30"></el-option>
                </el-select>

              </div>
              <div>
                <p>请选择问卷提交的截至日期<i class="ty-color-red">*</i></p>
                <el-date-picker style="width: 100%" v-model="createInvData.endDate" type="date" placeholder="选择日期">
                </el-date-picker>
                <p class="blueTip">注：所选日期24：00后，问卷将无法再提交。</p>
              </div>
              <div>
                <p style="font-size: 14px;">使用Wonderss的手机端，即可将所创建的调查问卷分享到微信！<br>
                  <span class="blueTip">注：Wonderss手机端里，该功能在“工作”下的“调查管理”中</span>
                </p>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>

      <!--   封面图片  -->
      <TyDialog v-if="uploadFImgData.visible" width="500" dialogTitle="封面图片" color="blue" :dialogHide="hidePloadFImg">
        <template #dialogBody>
          <div class="line30">
            <p>微信发布的链接封面上将展示此图片。如需要，可更换。</p>
            <div class="imgCC" style="text-align: center">
              <div class="fImg">
                <img :src="createInvData.defaultImg" alt="封面图片">
              </div>
            </div>
            <p style="margin-top: 20px"></p>
            <uploadFile ref="uploadFile" class="ty-right"
                        module="调查管理"
                        committed.async="false"
                        :showSelect="false"
                        :showFileList="false"
                        :autoUpload="true"
                        ext='.gif,.png,.jpg,.jpeg'
                        extMsg='只能上传图片'
                        :successFun="handleSuccess"
            >
              <template #btnArea>
                <div>
                  <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 " @click="uploadFImg">更换</span>
                </div>
              </template>
            </uploadFile>
          </div>
        </template>
      </TyDialog>

      <!--   数据展示设置  -->
      <TyDialog v-if="showSetData.visible" width="800" dialogTitle="展示设置" color="blue" :dialogHide="hideShowSet">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideShowSet">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="updateShowQuestionFun">确定</span>
        </template>
        <template #dialogBody>
          <div>
            <span class="ty-right">
                <span class="ty-linkBtn funBtn" @click="allSelectBtn">全选</span>
                <span class="ty-linkBtn funBtn" @click="allNoSelectBtn">全不选</span>
            </span>
            <p>页面上要展示哪些问题？请进行设置。</p>
            <table class="ty-table ty-table-control">
              <tr>
                <td width="10%">题号</td>
                <td width="60%">问题</td>
                <td width="10%">状态</td>
                <td width="20%">选为展示对象</td>
              </tr>
              <tr v-for="item in showSetData.list">
                <td>{{ item.orders }}</td>
                <td>{{ item.content }}</td>
                <td>{{ item.isShow === 1 ? '已选中' : '- -'}}</td>
                <td>
                  <span @click="toggleSelect(item)" :class="item.isShow === 1 ? 'ty-color-red' : 'ty-color-blue'">{{ item.isShow === 1 ? '放弃' : '选择' }}</span>
                </td>
              </tr>

            </table>
          </div>
        </template>
      </TyDialog>

      <!--   导出设置  -->
      <TyDialog v-if="exportSetData.visible" width="800" dialogTitle="原始数据导出" color="blue" :dialogHide="hideShowSet">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideExportSet">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="exportData">确定</span>
        </template>
        <template #dialogBody>
          <div>
            <span class="ty-right">
                <span class="ty-linkBtn funBtn" @click="allSelectBtn2">全选</span>
                <span class="ty-linkBtn funBtn" @click="allNoSelectBtn2">全不选</span>
            </span>
            <p>请选择要导出哪些问题的原始数据。</p>
            <table class="ty-table ty-table-control">
              <tr>
                <td width="10%">题号</td>
                <td width="60%">问题</td>
                <td width="10%">状态</td>
                <td width="20%">选为展示对象</td>
              </tr>
              <tr v-for="(exportI, indexExportI) in exportSetData.list" :key="indexExportI">
                <td>{{ exportI.orders }}</td>
                <td>{{ exportI.content }}</td>
                <td>{{ exportI.isShow === 1 ? '已选中' : '- -' }}</td>
                <td>
                  <span @click="toggleSelectExport(exportI)" :class="exportI.isShow === 1 ? 'ty-color-red' : 'ty-color-blue'">{{ exportI.isShow === 1 ? '放弃' : '选择' }}</span>
                </td>
              </tr>

            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 截止日期履历  -->
      <TyDialog v-if="endtimLogData.visible" id="endtimLog" width="800" dialogTitle="截止日期履历" color="blue" :dialogHide="hideEndtimLog">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideEndtimLog">关闭</span>
        </template>
        <template #dialogBody>
          <div class="line30">
            <p>本次调查问卷提交的截止日期：<span class="endTime">{{ endtimLogData.endTime }}</span></p>
            <p>之前设置过的截止日期如下： </p>
            <table class="ty-table" id="endtimLogTab">
              <tr><td>截止日期</td><td>创建/修改的操作</td></tr>
              <tr v-for="item in endtimLogData.list">
                <td>{{  new Date(item.qrDeadline ).format('yyyy-MM-dd')  }}</td>
                <td>{{ item.createName }} {{ new Date( item.createDate ).format('yyyy-MM-dd')  }}</td>
              </tr>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 分享到微信后的效果  -->
      <TyDialog v-if="shareViewData.visible" id="endtimLog" width="300" :headerShow="false" color="blue" :dialogHide="hideShareView">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div>
            <div class="shareViewbg" >
              <div class="back" @click="hideShareView"><i class="backBtn  fa fa-angle-left"></i>微信好友或群名</div>
              <div class="txtCon">
                <div class="ttl">{{ shareViewData.ttl }}</div>
                <div style="display: flex;">
                  <div class="txt" v-html="shareViewData.txt ">
                  </div>
                  <div class="fim">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>



    </div>
  </div>

</template>

<script>

import auth from '@/sys/auth'
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import { selectPublishList, selectPublishStopByMonthList,selectPublishByKeywordList,
  completeSubjectList, investigateQuestionStatistics, investigateAnswerStatistics,
  answerListByMultipleChoiceQuestion, investigateAnswerListByQuestion, areaListByQuestion,
  selectObjectById, selectQuestionOptionsList, updateShowQuestion, getQRLink,
  selectSubjectOptionsList,addPublish, managePublish, selectInvestigateQr,
  updateObjectSubsumable,
} from "@/api/investigationManage.js"
import uploadFile from "@/components/uploadFile.vue"
import fimg from '@/assets/img/fimg.png'
export default {
  name: "investigation",
  data() {
    return {
      pageName:'investigation',
      mainpage:11,
      selectPublishListPrams:{},
      shareViewData:{
        visible:false,
        ttl: '',
        txt: '',
      },
      endtimLogData:{
        visible : false,
        endTime: '' ,
        list : []
      },
      manageInv2Data:{
        visible: false,
        endtim: '',
      },
      manageInvData:{
        visible:'',
        manage_upTim: '',
        upEndDate: '',
        endType: '',
      },
      createInvData:{
        type:'',
        visible:false,
        marTp16px:false,
        preID:'',
        add_min:'',
        endDate:'',
        defaultImg:fimg,
        path:'../assets/img/fimg.png' ,
        originalFilename: '' ,
        bankList:[],
      },
      uploadFImgData:{
        visible:false,

      },
      mainCon88DataTime:'',
      mainCon99DataEndTime:'',
      editInvest: { }, // 当前 操作的 调查表
      mainCon11Data:{
        num1:0,
        searchKey:'',
        pageShow:false,
        list:[],
        pageInfo: {},
        res:{},
      },
      mainCon22Data:{
        num1:'',
        list: [],
        pageShow:false,
        pageInfo:null
      },
      mainCon55Data:{
        year:'',
        searchYear:'',
        num:'',
        list:[]
      },
      mainCon66Data:{
        num6:0,
        month:0,
        pageShow:false,
        list:[],
        pageInfo: {},
      },
      mainCon77Data:{
        memo:'',
        ttl:'',
        preface:'',
        backto: 0,
      },
      mainCon88Data:{
        back88S1how: true,
        back88Show: true,
        notJiData: true,
        exportDataSet: true,
        isStop: '',
        time: '',
        num: '',
        tags: '',
        list: '',
        pageInfo: '',
        tab88Width: '',
        typeStr: '',
      },
      mainCon99Data:{
        ansNum:'',
        endTime:'',
        q1Num:'',
        q2Num:'',
        q3Num:'',
        qMun:'',
      },
      mainCon1010Data:{
        type:'',
        q1Num2:'',
        q2Num2:'',
        q3Num2:'',
        list:[],
        pageInfo: { },
        pageShow: false
      },
      mainCon1111Data:{
        qwe:'',
        qInfo:'',
        question:'',
        newType:'',
        tipAtr:'',
        allDataBtn: false,
        case:0,
        res:null,
      },
      mainCon1212Data:{
        question: { },
        ops:[],
        ansList:[],
        pall: 0,
        pans: 0,
      },
      mainCon1313Data:{
        info:null,
        qInfo13: '',
        qwe13: '',
        ttlList: [],
        list: [],
        pageInfo: null,
        pageShow: false
      },
      showSetData:{
        visible: false ,
        list:[]
      },
      exportSetData:{
        visible: false ,
        list:[]
      }
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  components: {
    uploadFile,
  },
  created(){
    console.log('auth=', auth)
    initNav({mid: 'kz', name: '调查管理', pageName: this.pageName}, this.mountedFun, this)
  },
  methods:{
    createInvOk(){
      let d = this.createInvData.endDate
      if(d.length == 0){
        this.$message.error("请录入有效期的到期日")
        return false;
      }
      let min = this.createInvData.add_min
      if(min === ''){
        this.$message.error("问卷提交后，多长时间内可修改 需要设置")
        return false;
      }
      let opID = this.createInvData.preID
      let coverImage = this.createInvData.path
      let data = {
        "expireDate": new Date(d).getTime() ,
        "id":opID ,
        "coverImage":coverImage ,
        "modifyLimit": min // 允许修改时间
      }
      addPublish(data).then(res1=>{
        let res = res1.data
        let status = res.status
        this.createInvData.visible = false
        if(status === 1){
          this.$message.success("新的调查已生成")
          this.backTo11()
        }else{
          this.$message.success("新的调查生成失败")
        }

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    handleSuccess(file, files, ofile, totalFiles){
      console.log('成功了', file)
      this.createInvData.path = file.filename
      this.createInvData.originalFilename = file.originalFilename
      this.uploadFImgData.visible = false
      let rootPath = JSON.parse(localStorage.getItem('rootPath'))
      let path = rootPath.fileUrl + file.filename
      this.createInvData.defaultImg = path

    },
    hidePloadFImg(){
      this.uploadFImgData.visible = false
    },
    openUploadFImg(){
      this.uploadFImgData.visible = true
    },
    showNext(){
      console.log('this.createInvData.preID =', this.createInvData.preID )
      if(this.createInvData.preID > 0){
        this.createInvData.marTp16px = true
      }else{
        this.createInvData.marTp16px = false
      }
    },
    createInv(){
      this.createInvData.visible = true
      this.createInvData.marTp16px = false
      this.createInvData.type = 'add'
      this.createInvData.endDate = ''
      this.createInvData.add_min = ''
      this.createInvData.preID = ''
      this.createInvData.defaultImg = fimg
      this.createInvData.path = '../assets/img/fimg.png'
      selectSubjectOptionsList().then(res1=>{
        let list = res1.data || []
        this.createInvData.bankList = list
      }).catch(err=>{
        console.log('err=', err)
      })
    },
    hideExportSet(){
      this.exportSetData.visible = false
    },
    exportData(){
      let updateQuestionJson = []
      console.log('exportSetData.list.=', this.exportSetData.list)
      let list = JSON.parse(JSON.stringify(this.exportSetData.list))
      list.forEach(item=>{
        if(item.isShow === 1){
          updateQuestionJson.push({'question': item.id})
        }
      })
      if(updateQuestionJson.length === 0){
        this.$message.error('请最少选择一项！')
        return false
      }
      let info = this.editInvest

      var xhr = new XMLHttpRequest();
      var url = auth.webRoot + "/investigationPublish/exportExcel.do";
      var data = JSON.stringify({ id: info.id, updateQuestionJson: JSON.stringify(updateQuestionJson) }); // JSON数据

      xhr.open("POST", url, true);
      xhr.setRequestHeader("Content-Type", "application/json");
      xhr.responseType = "blob";
      xhr.onload = function (oEvent) {
        var content = xhr.response;
        var elink = document.createElement('a');
        elink.download = `${info.name}.xls`;
        elink.style.display = 'none';
        var blob = new Blob([content]);
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        document.body.removeChild(elink);
        this.exportSetData.visible = false
      };

      xhr.send(data);

    },
    exportDataSetBtn(){
      let info = this.editInvest
      selectQuestionOptionsList({ 'id': info.id }).then(res1=>{
        let res = res1.data
        this.exportSetData.list = res
        this.exportSetData.visible = true

      }).catch(err=>{
        console.log('err=',err)
      })

    },
    toggleSelectExport(item){
      if(item.isShow === 1){
        item.isShow = 0
      }else{
        item.isShow = 1
      }
    },
    toggleSelect(item){
      if(item.isShow === 1){
        item.isShow = 0
      }else{
        item.isShow = 1
      }
    },
    updateShowQuestionFun(){
      let updateQuestionJson = [], info = this.editInvest
      this.showSetData.list.forEach(item=>{
        if(item.isShow === 1){
          updateQuestionJson.push({'question': item.id})
        }
      })
      let params = { 'id': info.id , 'updateQuestionJson': JSON.stringify(updateQuestionJson)}
      updateShowQuestion(params).then(res1=>{
        let res = res1.data
        if(res.status === 1){
          this.$message.success("设置成功！")
          this.showSetData.visible = false
          this.getCompleteSubjectList(info.id , 1, this.mainCon88Data.typeStr)
        }else{
          this.$message.error("设置失败！")
        }

      }).catch(err=>{
        console.log('err=', err)
      })

    },
    allSelectBtn2(){
      this.exportSetData.list.forEach(item=>{
        item.isShow = 1
      })
    },
    allSelectBtn(){
      this.showSetData.list.forEach(item=>{
        item.isShow = 1
      })
    },
    allNoSelectBtn2(){
      this.exportSetData.list.forEach(item=>{
        item.isShow = 0
      })
    },
    allNoSelectBtn(){
      this.showSetData.list.forEach(item=>{
        item.isShow = 0
      })
    },
    hideShowSet(){
      this.showSetData.visible = false
    },
    dataShowSet(){
      let info = this.editInvest
      selectQuestionOptionsList({ 'id': info.id }).then(res1=>{
        let res = res1.data
        this.showSetData.list = res
        this.showSetData.visible = true

      }).catch(err=>{
        console.log('err=', err)
      })

    },
    back77(){
      let backto = this.mainCon77Data.backto
      this.mainpage = backto
    },
    scanAnsInfo(info){
      selectObjectById({ "id": info.id }).then(res1=>{
        let res = res1.data
        let list = res.respInvestigateSubjectTagList;
        let header = res.header || ""
        let title = res.title || ""
        this.mainCon77Data.list = list
        this.mainCon77Data.ttl = title
        this.mainCon77Data.preface = header
        this.mainCon77Data.backto = 88
        this.mainpage = 77

      }).catch(err=>{
        console.log('err=', err)
      })

    },
    notJiBtn(info, type){
      // type:0-不纳入统计，1-恢复纳入统计
      let data = {
        "id": info.id, type: type
      }
      updateObjectSubsumable(data).then(res1=>{
        let res = res1.data
        // status 1成功，0失败
        let status = res.status
        if(status == 1){
          let tip = '本条数据已进入“未纳入统计的数据”！'
          if(type == 1){
            tip = '本条数据已恢复纳入统计！'
          }
          this.$message.success(tip)
          let typeStr =''
          if(type === 1){
            typeStr ='未纳入统计'
          }
          this.getCompleteSubjectList(this.editInvest.id, 1, typeStr)
        }else{
          this.$message.error('操作失败！')
        }

      }).catch(err=>{
        console.log('err=', err)
      })

    },
    seeThisAns(item){
      this.mainpage = 1111
      let txt = `回收的问卷中，${ item.answerSum }份回答了本题 `
      this.mainCon1111Data.qwe = txt + '，具体如下：'
      this.mainCon1111Data.qInfo = `${ item.orders }、 ${ item.content }`
      this.mainCon1111Data.question = item
      this.getQAnsList(1)
    },
    getQAnsList(cur){
      let question =  this.mainCon1111Data.question
      let publish = this.editInvest
      let newType = ''
      if(question.type === '1'){ // 填空
        newType = 1
      }else if(question.type === '9'){ // 特殊题型
        if(question.isMultiple == '8'){ newType = 1 }
        else if(question.specialModel === '1'){ newType = 1 }
        else if(question.specialModel === '2'){ newType = 1 }
        else if(question.specialModel === '3'){ newType = 3 }
        else if(question.specialModel === '4'){ newType = 4 }
      }
      this.mainCon1111Data.newType = newType
      if(newType === 1){
        this.mainCon1111Data.allDataBtn = false
      }else{
        this.mainCon1111Data.allDataBtn = true
      }
      let params = {
        "publish": publish.id ,
        "question": question.id, "type": newType,
        "currentPageNo": cur, "pageSize": 40
      }
      investigateAnswerListByQuestion(params).then(res1=>{
        let res = res1.data
        let answerSum = res.answerSum || 0
        let objSum = res.objSum || 0
        let tipAtr = `${ objSum || '' }份问卷中，${ answerSum }份回答了本题，具体如下：`

        this.mainCon11Data.res = res
        let list = []
        this.mainCon1111Data.case = 1
        this.mainCon1111Data.allDataBtn = false
        this.mainCon1111Data.ttlList = res.investigatePublishShowList
        this.mainCon1111Data.list = res.respInvestigateObjectList
        console.log('this.mainCon1111Data=', this.mainCon1111Data)

        if (question.type == '3'|| question.type === '2') { // 单选， 判断

        }else if(question.type == '4'){ // 多选

        }else if(question.type == '1'){ // 填空

        }
        else if(question.type == '9') { // 特殊题型
          if (question.isMultiple == 8) { // 可以多次输入的题

          } else if (question.specialModel == '1') { // 录入必须为电子邮箱格式的问答题

          } else if (question.specialModel == '2') { // 录入必须为11位的手机号的问答题


          } else if (question.specialModel == '3') { // 省、市、地区或地址的题-
            this.mainCon1111Data.allDataBtn = true
            tipAtr = `回收的问卷中，${ answerSum }份回答了本题，统计如下：`
            list = res.investigateAreaList || []
            if (question.specialTab === '4' || question.specialTab === '3') { //  4-加具体地址的， 3-省市区
              this.mainCon1111Data.case = 2
              let newTrList = []
              list.forEach(areaI => { // // 省
                areaI.contentStr = areaI.name
                let td2 = [], td3= []
                let cityList = areaI.investigateAreaList // 市
                cityList.forEach(cityItem=>{
                  cityItem.contentStr = areaI.name + ' / ' + cityItem.name
                  let quList = cityItem.investigateAreaList || []
                  cityItem.td2Num =  quList.length
                  td2.push(cityItem)

                  if(quList.length > 0){
                    quList.forEach((quItem)=>{ // 区
                      quItem.contentStr = cityItem.contentStr + ' / ' + quItem.name
                      td3.push(quItem)
                    })
                  }
                })
                let trItem = { 'td1':areaI , 'td2': td2, 'td3': td3 }
                newTrList.push(trItem)
              })
              this.mainCon1111Data.newTrList = newTrList
            }
            else if(question.specialTab ==='1'){ // 省
              this.mainCon1111Data.case = 3
              let newTrList = []
              list.forEach(areaI =>{
                areaI.contentStr = areaI.name
                let trItem = { 'td1':areaI }
                newTrList.push(trItem)
              })
              this.mainCon1111Data.newTrList = newTrList
            }
            else if(question.specialTab ==='2'){ // 市
              this.mainCon1111Data.case = 4
              let newTrList = []
              list.forEach(areaI =>{
                areaI.contentStr = areaI.name
                let td2 = []
                let cityList = areaI.investigateAreaList || []
                cityList.forEach((cityItem, cityIndex)=>{
                  cityItem.contentStr = areaI.name + ' / ' + cityItem.name
                  td2.push(cityItem)
                })
                let trItem = { 'td1':areaI , 'td2': td2 }
                newTrList.push(trItem)
              })
              this.mainCon1111Data.newTrList = newTrList

            }
          }
          else if (question.specialModel == '4') { // 年、月或日期的题
            tipAtr = `回收的问卷中，${ answerSum }份回答了本题，统计如下： `
            list = res.investigateAreaList || []
            this.mainCon1111Data.allDataBtn = true
            if (question.specialTab == '1') { // 选项为年历
              this.mainCon1111Data.case = 5
              let newTrList = []
              list.forEach(areaI =>{
                areaI.contentStr = areaI.name
                let trItem = { 'td1':areaI }
                newTrList.push(trItem)
              })
              this.mainCon1111Data.newTrList = newTrList
            }
            else if (question.specialTab == '2') { // 选项为月历
              this.mainCon1111Data.case = 6
              let newTrList = []
              list.forEach(areaI =>{
                areaI.contentStr = areaI.name
                let td2 = []
                let cityList = areaI.investigateAreaList || []
                cityList.forEach((cityItem, cityIndex)=>{
                  cityItem.contentStr = areaI.name + cityItem.name
                  td2.push(cityItem)
                })
                let trItem = { 'td1':areaI , 'td2': td2 }
                newTrList.push(trItem)
              })
              this.mainCon1111Data.newTrList = newTrList
            }
            else if (question.specialTab == '3') { // 选项为日历
              this.mainCon1111Data.case = 7
              let newTrList = []
              list.forEach(areaI => { //  省
                areaI.contentStr = areaI.name
                let td2 = [], td3= []
                let cityList = areaI.investigateAreaList // 市
                cityList.forEach(cityItem=>{
                  cityItem.contentStr = areaI.name + cityItem.name
                  let quList = cityItem.investigateAreaList || []
                  cityItem.td2Num =  quList.length
                  td2.push(cityItem)

                  if(quList.length > 0){
                    quList.forEach((quItem)=>{ // 区
                      quItem.contentStr = cityItem.contentStr + quItem.name
                      td3.push(quItem)
                    })
                  }
                })
                let trItem = { 'td1':areaI , 'td2': td2, 'td3': td3 }
                newTrList.push(trItem)
              })
              this.mainCon1111Data.newTrList = newTrList
            }
          }
        }
        this.mainCon1111Data.qwe = tipAtr

      }).catch(err=>{
        console.log('err=',err)
      })
    },
    getYearAreaDetailsBtn(info){
      this.mainpage = 1313
      this.mainCon1313Data.info = info
      this.getYearAreaDetails(1)
    },
    getYearAreaDetails(cur){
      let info = this.mainCon1313Data.info
      let question =  this.mainCon1111Data.question
      let publish = this.editInvest
      let newType = this.mainCon1111Data.newType

      let content = info.contentStr
      let data = {
        publish: publish.id,
        question:  question.id,
        type: newType,
        content: content,
        pageSize:20, currentPageNo:cur
      }
      areaListByQuestion(data).then(res1=>{
        let res = res1.data
        let tipStr
        if(data.content){
          this.mainCon1313Data.qInfo13 = ''
          tipStr = `该${ res.answerSum }位问卷的提交者信息如下：`
        }else{
          tipStr = `回收的问卷中，${ res.answerSum }份回答了本题，具体如下：`
          this.mainCon1313Data.qInfo13 = question.orders + '、' + question.content
        }
        this.mainCon1313Data.qwe13 = tipStr
        let ttlList = res.investigatePublishShowList || []
        this.mainCon1313Data.ttlList = res.investigatePublishShowList || []
        let list = res.respInvestigateObjectList || []
        list.forEach(ii => {
          let tdList = ii.respInvestigateAnswerList
          if(tdList.length !== ttlList.length){
            let newArr = []
            for(let i=tdList.length; i<ttlList.length; i++){
              newArr.push({ content: '' })
            }
            ii.respInvestigateAnswerList.push(...newArr)
          }
        })
        this.mainCon1313Data.list = list
        let pageInfo = res.pageInfo ;
        this.mainCon1313Data.pageInfo = pageInfo


      }).catch(err=>{
        console.log('err=', err)
      })
    },
    pageClick1313(pageItem){
      this.getYearAreaDetails(pageItem.page)
    },
    exportBtn(place){
      let data = { "publish": this.editInvest.id }
      let queryStr = `publish=${ this.editInvest.id }`
      let type = this.mainCon1010Data.type
      let question = this.mainCon1212Data.question
      let url = auth.webRoot
      if(place == 1){
        data['type'] = type
        url += '/investigationPublish/exportExcelQuestionStatistics.do'
        queryStr += `&type=${type}`
      }
      else if(place == 2){
        queryStr += `&question=${question.id}`
        data['question'] = question.id
        url += '/investigationPublish/exportExcelEssayQuestion.do'

        if(question.type == '1'){
          queryStr += `&type=1`
          data['type'] = 1
        }else if(question.type == '9'){
          if(question.isMultiple == 8){ // 可以多次输入的题
            data['type'] = 1
            queryStr += `&type=1`
          }else if(question.specialModel == '1'){ // 录入必须为电子邮箱格式的问答题
            data['type'] = 1
            queryStr += `&type=1`
          }else if(question.specialModel == '2'){ // 录入必须为11位的手机号的问答题
            data['type'] = 1
            queryStr += `&type=1`
          }else if(question.specialModel == '3'){ // 省、市、地区或地址的题-
            data['type'] = 3
            queryStr += `&type=3`
          }else if(question.specialModel == '4'){ // 年、月或日期的题
            data['type'] = 4
            queryStr += `&type=4`
          }
        }
      }
      else if(place == 3){
        queryStr += `&question=${question.id}`
        data['question'] = question.id
        url += '/investigationPublish/exportExcelMultipleChoiceQuestion.do'
      }
      else if(place == 4){
        queryStr += `&question=${question.id}`
        data['question'] = question.id
        url +=  '/investigationPublish/exportExcelAreaListByQuestion.do'
        let info = this.mainCon1313Data.info
        let newType = this.mainCon1111Data.newType
        let content = info.contentStr
        if(content){
          queryStr += `&type=${ newType }&content=${ content }`
        }else{
          queryStr += `&type=${ newType }`
        }
        data.type = newType
        data.content = content
      }

      var oReq = new XMLHttpRequest();
      oReq.open("GET", `${ url }?${ queryStr }`, true);
      oReq.responseType = "blob";
      oReq.onload = function (oEvent) {
        var content = oReq.response;
        var elink = document.createElement('a');
        elink.download = `调查分析.xls`;
        elink.style.display = 'none';
        var blob = new Blob([content]);
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        document.body.removeChild(elink);
      };
      oReq.send();
    },
    searchBtn2(){
      this.getStopByMonth(this.mainCon55Data.searchYear)
    },
    back55(){
      this.backTo11()
    },
    backTo11(){
      this.mainpage = 11
      let pageInfo = this.selectPublishListPrams
      this.getList(pageInfo.status, pageInfo.currentPageNo ,pageInfo.month)
    },
    mountedFun(){
      this.getList(1,1,'')

    },
    goStop(){
      this.mainpage = 55
      this.getStopByMonth()
    },
    getStopByMonth(year){
      selectPublishStopByMonthList({ 'year':year }).then(res1=>{
        let res = res1.data
        let yearC= res.year
        let list = res.respInvestigateStopByMonthList || []
        list.forEach(item=>{
          item.status = 0
        })
        this.mainCon55Data.num = res.num
        this.mainCon55Data.year = yearC
        this.mainCon55Data.list = list


      }).catch(err=>{
        console.log('err=',err)
      })
    },
    back22(){
      this.mainpage = 11
    },
    back66(){
      this.mainpage = 55
    },
    invStopList(item){
      let month = item.month
      this.getList(0,1,month)
      this.mainpage = 66
    },
    searchBtn1(){
      this.getListBySearch(this.mainCon11Data.searchKey,1 )
    },
    getListBySearch(searchKey,currentPageNo ){
      let params={
        "keyword" : searchKey,
        "currentPageNo" : currentPageNo,
        "pageSize" : 20,
      }
      selectPublishByKeywordList(params).then(res1=>{
        let res = res1.data
        let list = res.investigatePublishList || []
        let sumPage = res.pageInfo.totalPage || 0
        let currentPageNo2 = res.pageInfo.currentPageNo || currentPageNo
        let sum = res.sum || 0
        list.forEach(invst=>{
          invst.isStop = false
        })
        this.mainpage = 22
        this.mainCon22Data.num1 = sum
        this.mainCon22Data.list = list
        this.mainCon22Data.pageInfo = res.pageInfo
        this.mainCon22Data.pageShow = false
        this.mainCon22Data.pageShow = true

      }).catch(err=>{
        console.log('err=',err)
      })

    },
    getList(status, currentPageNo , month){
      let params = {
        "status" : status,
        "currentPageNo" : currentPageNo,
        "month" : month ,
        "pageSize" : 20,
      }
      selectPublishList(params).then(res1=>{
        let res = res1.data
        let list = res.investigatePublishList || []
        let pageInfo = res.pageInfo
        let sumPage = res.pageInfo.totalPage || 0
        let currentPageNo2 = res.pageInfo.currentPageNo || currentPageNo
        let sum = res.sum || 0
        this.selectPublishListPrams = params
        list.forEach(invst=>{
          invst.isStop = false
          invst.status = status
          if(status===0){ invst.isStop = true }
        })
        if(status===0){
          this.mainCon66Data.num6 = sum
          this.mainCon66Data.month = month
          this.mainCon66Data.pageInfo = pageInfo
          this.mainCon66Data.list = list
          this.mainCon66Data.pageShow = true

        }else{
          this.mainCon11Data.num1 = sum
          this.mainCon11Data.pageInfo = pageInfo
          this.mainCon11Data.list = list
          this.mainCon11Data.pageShow = true
        }

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    pageClick11(pageItem){
      let month = this.selectPublishListPrams.month
      let status = this.selectPublishListPrams.status
      let currentPageNo = pageItem.page
      this.getList(status, currentPageNo , month)
    },
    hideCreateInv(){
      this.createInvData.visible = false
    },
    scanAns(invst){
      this.editInvest = invst
      this.mainpage = 88
      this.mainCon88Data.back88Show = true
      this.mainCon88Data.isStop = invst.isStop
      this.mainCon88Data.notJiData = true
      this.mainCon88Data.exportDataSet = true
      this.mainCon88Data.back88S1how = false

      this.getCompleteSubjectList(invst.id,1)
    },
    getCompleteSubjectList(id , currentPageNo, typeStr ){
      let type = typeStr ? 0 : 1 ; // type 为1时，纳入统计，为0时，未纳入统计
      let params = {
        "id": id , "pageSize": 20 , "currentPageNo": currentPageNo, 'type': type
      }
      completeSubjectList(params).then(res1=>{
        console.log('completeSubjectList res1=', res1)
        let res = res1.data
        let tags = res.investigatePublishShowList || []
        let list = res.respInvestigateObjectList || []
        let totalPage = res.pageInfo.totalPage || 0
        let totalResult = res.pageInfo.totalResult || 0
        let currentPageNo2 = res.pageInfo.currentPageNo || currentPageNo
        this.getHostTimeFun('mainCon88DataTime')
        // console.log('timer=', timer)
        list.forEach(item => {
          let respInvestigateAnswerList = item.respInvestigateAnswerList || []
          item.tds = []
          respInvestigateAnswerList.forEach( QaA => {
            tags.forEach(tag => {
              if(QaA.question === tag.question){
                item.tds.push(QaA.content || '- -')
              }
            })
          })

        })
        this.mainpage = 88
        this.mainCon88Data.num = totalResult
        this.mainCon88Data.tags = tags
        this.mainCon88Data.list = list
        this.mainCon88Data.pageInfo = res.pageInfo
        this.mainCon88Data.tab88Width = 200 * tags.length + 400
        this.mainCon88Data.typeStr = typeStr

      }).catch(err=>{
        console.log('err=',err)
      })
    },
    setTD(item, tags){
      let respInvestigateAnswerList = item.respInvestigateAnswerList || []

      let str = ``
      respInvestigateAnswerList.forEach( QaA => {
        tags.forEach(tag => {
          if(QaA.question === tag.question){
            str += `<td style="width: 200px;">${ QaA.content || '- -' }</td>`
          }
        })
      })
      return str
    },
    back88(){
      if(this.mainCon88Data.isStop){  // 停用的
        this.mainpage = 66
      }else{ // 进行中的
        this.mainpage = 11
      }
    },
    back881(){
      this.mainCon88Data.back88S1how = false
      this.mainCon88Data.back88Show = true
      this.mainCon88Data.notJiData = true
      this.mainCon88Data.exportDataSet = true
      this.getCompleteSubjectList(this.editInvest.id, 1)
    },
    notJiData(){
      this.mainCon88Data.back88S1how = true
      this.mainCon88Data.back88Show = false
      this.mainCon88Data.notJiData = false
      this.mainCon88Data.exportDataSet = false
      this.getCompleteSubjectList(this.editInvest.id, 1, '未纳入统计')
    },
    analysisAns(invst,pageNo){
      // 调查分析
      this.editInvest = invst
      this.mainpage = 99
      console.log('pageNo=', pageNo)
      this.mainCon99Data.pageNo = pageNo
      this.mainCon99Data.ansNum = invst.feedbackNum
      let params = { "publish": invst.id }
      investigateAnswerStatistics(params).then(res1=>{
        let res = res1.data
        this.getHostTimeFun('mainCon99DataEndTime')
        // this.mainCon99Data.endTime = (new Date(timer).format("yyyy-MM-dd hh:mm:ss"))
        this.mainCon99Data.ansNum = res.objectCount
        this.mainCon99Data.q1Num = res.essayQuestionCount
        this.mainCon99Data.q2Num = res.singleChoiceCount
        this.mainCon99Data.q3Num = res.multipleChoiceCount
        let qMun = Number(res.essayQuestionCount) + Number(res.singleChoiceCount) + Number(res.multipleChoiceCount) ;
        this.mainCon99Data.qMun = qMun

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    getQAnsysList(type){
      // 调查分析 -  查看题型
      let publish = this.editInvest.id
      this.mainpage = 1010
      this.mainCon1010Data.type = type
      this.getgetQAnsysListByType(publish, type, 1)
    },
    back1010(){
      this.mainpage = 99
    },
    back1111(){
      this.mainpage = 1010
    },
    back1313(){
      this.mainpage = 1111
    },
    getgetQAnsysListByType(publish, type, cur){
      // 调查分析 -  查看题型 列表
      let params = { "publish": publish, "type": type, "currentPageNo": cur, "pageSize": 20}
      investigateQuestionStatistics(params).then(res1=>{
        let res = res1.data
        let list = res.respInvestigateQuestionList || [];
        this.mainCon1010Data.q1Num2 = res.essayQuestionCount || 0
        this.mainCon1010Data.q2Num2 = res.singleChoiceCount || 0
        this.mainCon1010Data.q3Num2 = res.multipleChoiceCount || 0
        this.mainCon1010Data.list = list
        this.mainCon1010Data.pageInfo = res.pageInfo
        this.mainCon1010Data.pageShow = true

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    getHostTimeFun(key){
      auth.getHostTime('/favicon.ico', hosttime => { // 获取服务器当前时间
        console.log('哈哈哈:', new Date(hosttime));
        this[key] = (new Date(hosttime).format("yyyy-MM-dd hh:mm:ss"))
      });
    },
    changeToNext(question){
      this.mainpage = 1212
      this.mainCon1212Data.question = question
      this.mainCon1212Data.questionInfo = `${ question.orders }、 ${ question.content }`

      let params = { "question": question.id , "publish": this.editInvest.id }
      answerListByMultipleChoiceQuestion(params).then(res1=>{
        let res = res1.data
        let objectSum = res.objectSum ; // 总回收数
        let sum = res.sum  ; // 回答本题人数
        let ques = res.respInvestigateQuestion || [] ; // 题目详情
        let ops = ques.investigateQuestionKeyList || [] ; // options
        let ansList = res.respAnswerCountList || [] ; // ans

        this.mainCon1212Data.pall = objectSum
        this.mainCon1212Data.pans = sum
        this.mainCon1212Data.ops = ops
        this.mainCon1212Data.ansList = ansList

      }).catch(err=>{
        console.log('err=', err)
      })

    },
    formatOption(answer,options) {
      let answerArr = answer.split(',')
      let ansStr = ``
      answerArr.forEach(function (ansID) {
        options.forEach(function (op) {
          if(ansID == op.id){
            ansStr += `${ op.orders }`
          }
        })
      })
      return ansStr
    },
    share(invst){
      this.$message.success('友情提示：分享需使用手机端！')
      this.editInvest = invst
      getQRLink({ "expireDate": invst.qrDeadline , 'id': invst.id }).then(res1=>{
        let res = res1.data
        console.log(res.data)
      }).catch(err=>{
        console.log('err=',err)
      })

    },
    back99(){
      console.log('this.mainCon99Data.pageNo=', this.mainCon99Data.pageNo)
      this.mainpage = this.mainCon99Data.pageNo
    },
    hideManageInv(){
      this.manageInvData.visible = false
    },
    hideManageInv2(){
      this.manageInv2Data.visible = false
    },
    manageInv(invst){
      this.editInvest = invst
      let endTime = new Date(invst.endTime).format('yyyy-MM-dd')
      if(invst.status===0){
        this.manageInv2Data.visible = true
        this.manageInv2Data.endtim = endTime
      }else{
        this.manageInvData.visible = true
        this.manageInvData.endType = ''
        this.manageInvData.manage_upTim = invst.modifyLimit
        this.manageInvData.upEndDate = endTime
      }
    },
    manageInvOk(){
      let info =  this.editInvest
      let data = { id: info.id }
      data.type = 1  // type为1只修改允许修改时间，为2有截止日期修改，为3立即终止
      data.expireDate = ''   // 截止日期
      data.modifyLimit = this.manageInvData.manage_upTim  // 允许修改时间
      let endDateFm = this.manageInvData.endType
      if(endDateFm === 1){ // // 选择了  修改问卷提交的截止日期
        let d = this.manageInvData.upEndDate
        console.log('data.expireDate=', d)
        if(d){}else{
          this.$message.error('请输入截止日期！')
          return false
        }
        data.expireDate = new Date(d).getTime()
        data.type = 2

      }else if(endDateFm === 2){
        data.type = 3
      }else{
        this.$message.error('请选择截止情况！')
        return false
      }
      managePublish(data).then(res1=>{
        let res = res1.data
        if(res.status === 1){
          this.$message.success('操作成功！')
          this.manageInvData.visible = false
          if(this.mainpage === 11 || this.mainpage === 66){
            let params = this.selectPublishListPrams
            let month = params.month
            let status = params.status
            let currentPageNo = params.currentPageNo
            this.getList(status, currentPageNo , month)
          }else if(this.mainpage === 22){
            let cur = this.mainCon22Data.pageInfo.currentPageNo
            this.getListBySearch(this.mainCon11Data.searchKey,cur )
          }
        }else{
          this.$message.error('操作失败！')
        }

      }).catch(err=>{
        console.log('err=',err)
      })


    },
    hideEndtimLog(){
      this.endtimLogData.visible = false
    },
    endtimLog(){
      let info = this.editInvest
      selectInvestigateQr({ 'id': info.id }).then(res1=>{
        let res = res1.data
        let endTime = res.endTime || "";
        let list = res.investigateQrs || []
        this.endtimLogData.visible = true
        this.endtimLogData.list = list
        this.endtimLogData.endTime = endTime && (new Date(endTime).format("yyyy-MM-dd"))


      }).catch(err=>{
        console.log('err=', err)
      })
    },
    shareView(){
      let info = this.editInvest
      this.shareViewData.visible = true
      this.shareViewData.ttl = info.name
      let org = auth.getOrg()
      let orgName = org.name
      this.shareViewData.txt = `问卷提交截止日期:${ new Date(info.endTime).format('yyyy-MM-dd') } <br> ${ orgName }`
    }

  }
}
</script>

<style scoped lang="scss">
@use '@/style/investigation.scss';
.investigationContainer{
  padding: 20px; font-size: 15px;
  .marL20{ margin-left: 20px; }
  .line30{ line-height: 30px; }
  .oneLine { text-overflow: ellipsis; overflow: hidden; white-space: nowrap; word-break: break-all !important; text-align: left !important;}
  .tdSty{
    border-bottom: 1px solid #ccc; margin:0 -15px;
  }
  .ty-color-blue{ line-height: 20px; }
  .tdSty:last-child{ border-bottom: none; }
  .wid100{ width: 100%; }
}
</style>