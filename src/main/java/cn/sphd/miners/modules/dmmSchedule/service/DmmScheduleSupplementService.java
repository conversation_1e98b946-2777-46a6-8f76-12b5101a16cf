package cn.sphd.miners.modules.dmmSchedule.service;

import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleSupplement;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleSupplementImage;

import java.util.List;

/**
 * Created by Administrator on 2019/8/14.
 */
public interface DmmScheduleSupplementService {

    List<DmmScheduleSupplement>  getDmmScheduleSupplementListBySid(Integer scheduleId);

    boolean addDmmScheduleSupplement(AuthAcc acc, Integer dmmJobId, String description, String[] imagePaths);

    boolean deleteDmmScheduleSupplement(Integer supplementId);

    List<DmmScheduleSupplement>  getDmmScheduleSupplementListByJobId(Integer dmmJobId);

    DmmScheduleSupplement getDmmScheduleSupplementById(Integer id);

    DmmScheduleSupplementImage getDmmScheduleSupplementImageById(Integer id);
}
