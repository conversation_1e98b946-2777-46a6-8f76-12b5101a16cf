package cn.sphd.miners.modules.finance.dto;

import cn.sphd.miners.modules.finance.entity.PersonnelSalary;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName MonthSalaryByOrg
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/7 8:34
 * @Version 1.0
 */
public class MonthSalary {
    private String reportingPeriod;//所属月份
    private Date salaryTime;//个税时间
    private Date taxTime;//个税时间
    private Date premiumTime;//社保时间
    private Date fundTime;//公积金时间
    private String salaryName;//个税
    private String taxName;//
    private String premiumName;//社保
    private String fundName;//公积金
    private Integer number;//总人数
    private Integer salaryStatus;
    private Integer taxesStatus;
    private Integer insuranceStatus;
    private Integer accumulationFundStatus;
    private Double factSalary;//工资
    private Double tax;//个税
    private Double insurance;//保险
    private Double accumulationFund;//公积金
    private List<RespPersonnelSalary> personnelSalaryList;
    private String createName;//创建人
    private Date createDate;//创建时间
    private String updateName;//修改人
    private Date updateDate;//修改时间

    public String getSalaryName() {
        return salaryName;
    }

    public void setSalaryName(String salaryName) {
        this.salaryName = salaryName;
    }

    public String getTaxName() {
        return taxName;
    }

    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    public String getPremiumName() {
        return premiumName;
    }

    public void setPremiumName(String premiumName) {
        this.premiumName = premiumName;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getSalaryTime() {
        return salaryTime;
    }

    public void setSalaryTime(Date salaryTime) {
        this.salaryTime = salaryTime;
    }

    public Date getTaxTime() {
        return taxTime;
    }

    public void setTaxTime(Date taxTime) {
        this.taxTime = taxTime;
    }

    public Date getPremiumTime() {
        return premiumTime;
    }

    public void setPremiumTime(Date premiumTime) {
        this.premiumTime = premiumTime;
    }

    public Date getFundTime() {
        return fundTime;
    }

    public void setFundTime(Date fundTime) {
        this.fundTime = fundTime;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getSalaryStatus() {
        return salaryStatus;
    }

    public void setSalaryStatus(Integer salaryStatus) {
        this.salaryStatus = salaryStatus;
    }

    public Integer getTaxesStatus() {
        return taxesStatus;
    }

    public void setTaxesStatus(Integer taxesStatus) {
        this.taxesStatus = taxesStatus;
    }

    public Integer getInsuranceStatus() {
        return insuranceStatus;
    }

    public void setInsuranceStatus(Integer insuranceStatus) {
        this.insuranceStatus = insuranceStatus;
    }


    public Double getFactSalary() {
        return factSalary;
    }

    public void setFactSalary(Double factSalary) {
        this.factSalary = factSalary;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Double getInsurance() {
        return insurance;
    }

    public void setInsurance(Double insurance) {
        this.insurance = insurance;
    }

    public Integer getAccumulationFundStatus() {
        return accumulationFundStatus;
    }

    public void setAccumulationFundStatus(Integer accumulationFundStatus) {
        this.accumulationFundStatus = accumulationFundStatus;
    }

    public Double getAccumulationFund() {
        return accumulationFund;
    }

    public void setAccumulationFund(Double accumulationFund) {
        this.accumulationFund = accumulationFund;
    }

    public List<RespPersonnelSalary> getPersonnelSalaryList() {
        return personnelSalaryList;
    }

    public void setPersonnelSalaryList(List<RespPersonnelSalary> personnelSalaryList) {
        this.personnelSalaryList = personnelSalaryList;
    }

    public String getReportingPeriod() {
        return reportingPeriod;
    }

    public void setReportingPeriod(String reportingPeriod) {
        this.reportingPeriod = reportingPeriod;
    }


}
