
/*
*  共用文件
* 目前用到的 页面如下：
*   初始化-销售1 web/vue/minersFrontEnd/src/views/init/saleInit.vue、
*   销售管理-专属商品 、
*   销售管理-通用商品 、
*
* */

import {addUnitOk, getUnitList} from "@/api/technology";
import * as initApi from "@/api/init";
import * as api from "@/api/init";

export default {
    name:"saleGoodsJs",
    data() {
        return {
            currNavList: [],
            isSaled: 1,
            categoryId: 1,
            inSales: {
                pageShow: {}
            },
            addUniversalPt:{
                visible: false,
                info: {}
            },
            addUniversalPtForm: {
                taxRate: '',
                unitPriceNotax: '',
                unitPrice: '',
                unitPriceInvoice: '',
                unitPriceNoinvoice: '',
                unitPriceReference: '',
                unitId: '',
                priceDesc: '',
                minimumStock: '',
                memo: '',
                commodityMediaList:[],
                firstTime: '',
                initialPeriod: '',
                chanNum: 0,
                chanImg: [],
                vedio: [],
                vedioNum: 0
            },
            goodsAddNotice: false,
            unitList: [],
            invoiceTaxRateList: [],
            unitForm:{}, //新增计量单位
            addUnitLog: false,
            addPtrules: {
                outerSn: [
                    {required: true, message: '请录入', trigger: 'blur'}
                ],
                outerName: [
                    {required: true, message: '请录入', trigger: 'blur'}
                ],
                unitId: [
                    {required: true, message: '请选择', trigger: 'blur'}
                ],
                minimumStock: [
                    {required: true, message: '请录入', trigger: 'blur'}
                ],
            },
            vedioPlay: false,
            inSalesPtList: [],
            kindsTree: [],
            searchKeyBase: '',
            indexInputState: 0,
            betweenHeight: 20
        }
    },
    computed:{
        firstLevelAmount(){
            let count = 0
            let category = this.kindsTree
            for(let m=0; m<category.length; m++){
                count += Number(category[m].num)
            }
            return count
        },
        betweenHeightFun(){
            // let count = this.$refs.bigContainerRef.offsetHeight
            // return count
        },
    },
    mounted() {
        //let count = this.$refs.bigContainerRef.offsetHeight
        //this.betweenHeight = count
    },
    destroyed () {
    },
    methods: {
        getUniversalPtList(){ //获取
            initApi.getUniversalPt().then(res => {
                let list = res.data.data || []
                this.kindsTree = res.data.mtCategories || []
                this.inSalesPtList = list
                if (list.length > 0) {
                    this.saleNum = 4
                    let count = this.$refs.bigContainerRef.offsetHeight
                    this.betweenHeight = count

                    //this.betweenHeightFun()
                } else {
                    this.saleNum = 3
                }
            })
        },
        setFirstTimeNGoods(){
            if(this.addUniversalPtForm.initialPeriod){
                this.addUniversalPtForm.firstTimeN = false
            }
        },
        toggefirstTimeNGoods(type){
            if(type === 'manage'){ // 管理客户
                this.manageBaseData.firstTimeN = !this.manageBaseData.firstTimeN
                if(this.manageBaseData.firstTimeN){
                    this.manageBaseData.initialPeriod = ''
                }
            }else{ // 新增客户
                this.addUniversalPtForm.firstTimeN = !this.addUniversalPtForm.firstTimeN
                if(this.addUniversalPtForm.firstTimeN){
                    this.addUniversalPtForm.initialPeriod = ''
                }
            }
        },
        toggefirstTimeGoods(val, type){
            if(type === 'manage'){// 客户管理
                if(this.manageBaseData.firstTime === val){
                    this.manageBaseData.firstTime = ''
                }else{
                    this.manageBaseData.firstTime = val
                    this.manageBaseData.initialPeriod = ''
                    this.manageBaseData.firstTimeN = false
                }
            }else{ // 新增客户
                if(this.addUniversalPtForm.firstTime === val){
                    this.addUniversalPtForm.firstTime = ''
                }else{
                    this.addUniversalPtForm.firstTime = val
                    this.addUniversalPtForm.initialPeriod = ''
                    this.addUniversalPtForm.firstTimeN = false
                }
            }

        },
        newSalesCommodity(){
            this.addUniversalPt.visible = true
            this.$refs['addUniversalPtRef'].resetFields();
            this.getUnitListFun(1)
            this.getIndexInvoiceList(1)
        },
        addCommoditySure() { //通用商品录入
            let reqTrue = 0;
            let addRate = this.addUniversalPtForm.taxRate
            let unitPriceNotax = this.addUniversalPtForm.unitPriceNotax
            let unitPrice_add = this.addUniversalPtForm.unitPrice
            if(addRate || unitPriceNotax || unitPrice_add){ // 有一个填了,三个都是必填项
                let msg = ''
                if(addRate) {
                    if (unitPriceNotax == "" || unitPrice_add == '') {
                        reqTrue++;
                        msg = '请录入含税单价或者不含税单价'
                    }
                }
                if(unitPriceNotax){
                    if(addRate == "" || unitPrice_add == ''){
                        reqTrue++;
                        msg = '请录入税率'
                    }
                }
                if(unitPrice_add){
                    if(addRate == "" || unitPriceNotax == ''){
                        reqTrue++;
                        msg = '请录入税率'
                    }
                }
                if (reqTrue > 0) {
                    this.$message({
                        'type':'error',
                        'offset':'30',
                        'message': msg
                    })
                    return false;
                }
            }
            this.$refs['addUniversalPtRef'].validate((valid) => {
                if (valid) {
                    let goodsData = {
                        'isSaled': this.isSaled,
                        'category': this.categoryId,
                        ...this.addUniversalPtForm
                    }
                    let unitItem = this.unitList.find(item => Number(goodsData.unitId) === Number(item.id))
                    if (unitItem) {
                        goodsData['unit'] = unitItem.name;
                    }
                    //图片 视频
                    let media = this.addUniversalPtForm.chanImg || [];
                    if (this.addUniversalPtForm.vedio.length > 0) {
                        media.push(this.addUniversalPtForm.vedio[0])
                    }
                    goodsData.commodityMediaList = JSON.stringify(media);

                    goodsData.exclusiveTime = this.addUniversalPtForm.firstTime
                    goodsData.exclusiveMonth = this.addUniversalPtForm.initialPeriod
                    initApi.addTYProductSure(goodsData).then(res => {
                        if (res.data.data.code == 200){
                            //getCommodityList(1,20,"","全部","");
                            this.addUniversalPt.visible = false
                            // this.addUniversalPt.visible = false
                        } else {
                            let msg = res.data.data.msg;
                            this.$message({
                                'type':'error',
                                'offset':'30',
                                'message': msg
                            })
                        }
                    })
                } else {
                    this.$message({
                        'type':'error',
                        'offset':'30',
                        'message': '还有必填项尚未填写!'
                    })
                    return false;
                }
            })

        },
        goodsAddNoticeBtn(){
            this.goodsAddNotice = true
        },
        goodsAddNoticeHide(){
            this.goodsAddNotice = false
        },
        addUnitLogHide(){
            this.addUnitLog = false
        },
        addUnit() {
            this.unitForm.name = ""
            this.addUnitLog = true
        },
        addUnitOkBtn(module) {
            var name = this.unitForm.name;
            addUnitOk({module,name}).then(res => {
                var status = res.data['status'] ;
                if(status ==1){
                    this.$message({
                        'type':'success',
                        'offset':'30',
                        'message':'新增成功！'
                    })
                    this.addUnitLog = false
                    this.getUnitListFun(module)
                }else {
                    var tipStr = '' ;
                    if(status == 0){
                        tipStr = '新增失败！系统中已经有这个计量单位了。' ;
                    }else if(status == 2){
                        tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                    }
                    this.$message({
                        'type':'error',
                        'offset':'30',
                        'message': tipStr
                    })
                }
            })
        },
        getUnitListFun(module){
            let that = this
            getUnitList({ 'module': module }).then(res => {
                var list = res.data['list'] || [];
                that.unitList = list
            })
        },
        getIndexInvoiceList(){
            api.getInvoiceList()
                .then(res => {
                    let rates = []
                    let list = res.data["financeInvoiceSettings"] || [];
                    for (let r = 0; r < list.length; r++) {
                        if (list[r]["category"] == '1'){
                            let rate = list[r]["enableTaxTate"];
                            rates = rate.split(",");
                            this.invoiceTaxRateList = rates
                        }
                    }
                })
        },
        gs_beforeUpload2(file){
            if(this.addUniversalPtForm.chanNum >= 9){
                return false
            }else{
                this.addUniversalPtForm.chanNum++
            }
        },
        gs_uploadFile2(){
            this.addUniversalPtForm.chanNum = this.addUniversalPtForm.chanImg.length
        },
        gs_fileSuccess2(file, files, ofile, totalFiles){
            let imgItem = {
                uplaodPath: this.rootPath.fileUrl + file.filename,
                title: file.name,
                type: 1
            }
            this.addUniversalPtForm.chanImg.push(imgItem)
        },
        gs_delImg(index, imgType, type){
            if(type === 'manage'){
                this.manageBaseData[imgType].splice(index,1)
            }else{
                this.addUniversalPtForm[imgType].splice(index,1)
            }
        },
        gs_beforeUpload(file){
            if(this.addUniversalPtForm.vedio >= 1){
                return false
            }else{
                this.addUniversalPtForm.vedioNum++
            }
        },
        gs_uploadFile(){
            this.addUniversalPtForm.vedioNum = this.addUniversalPtForm.vedio.length
        },
        gs_fileSuccess(file, files, ofile, totalFiles){
            let imgItem = {
                uplaodPath: this.rootPath.fileUrl + file.filename,
                title: file.name,
                type: 2
            }
            this.addUniversalPtForm.vedio.push(imgItem)
        },
        gs_delVedio(index, imgType, type){
            if(type === 'manage'){
                this.manageBaseData[imgType].splice(index,1)
            }else{
                this.addUniversalPtForm[imgType].splice(index,1)
            }
        },
        vedioPlayBtn(){
            this.vedioPlay = true
        },



        chargeXhr(){
            this.addUniversalPt.visible = false
        }

    }
}
