package cn.sphd.miners.modules.processManagement.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by lyx on 2023/5/9
 * 生产_活动周期表  1.251工序1
 */
@Entity
@Table(name="t_ppm_activity_cyclicity")
public class PpmActivityCyclicity implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   //ID

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="process"   , nullable=true , unique=false)
    private Long process;   //工序ID

    @Column(name="activity"   , nullable=true , unique=false)
    private Long activity;   //活动ID

    @Column(name="type"  , nullable=true , unique=false)
    private Byte type;   //类型:2-子工序,3-操作,4-套餐

    @Column(name="pattern"  , nullable=true , unique=false)
    private Byte pattern;   //型式:1-一定时间内需进行一次或数次,2-一定次数的操作后需进行一次或数次,3-一个或几个班次后需进行一次或数次

    @Column(name="frequency"   , nullable=true , unique=false)
    private Integer frequency;   //时长/频次

    @Column(name="time_unit"  , nullable=true , unique=false)
    private Byte timeUnit;   //时间单位:1-秒,2-分钟,3-小时,4-天

    @Column(name="action"   , nullable=true , unique=false)
    private Integer action;   //操作(预留)

    @Column(name="execute_times"   , nullable=true , unique=false)
    private Integer executeTimes;   //执行次数

    @Column(name="enabled")
    private Boolean enabled; //启用标志:true-启用(默认),false-停用

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;   //创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;  //创建人

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;   //创建时间

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;   //修改人id

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;   //修改人

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;   //修改时间

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private Byte operation;  //操作:1-增,2-删,3-改,4-暂停,5-恢复

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Long previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getActivity() {
        return activity;
    }

    public void setActivity(Long activity) {
        this.activity = activity;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Byte getPattern() {
        return pattern;
    }

    public void setPattern(Byte pattern) {
        this.pattern = pattern;
    }

    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    public Byte getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(Byte timeUnit) {
        this.timeUnit = timeUnit;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public Integer getExecuteTimes() {
        return executeTimes;
    }

    public void setExecuteTimes(Integer executeTimes) {
        this.executeTimes = executeTimes;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Long getProcess() {
        return process;
    }

    public void setProcess(Long process) {
        this.process = process;
    }
}
