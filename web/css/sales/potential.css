input:not([type="radio"]), select {
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    background-color: #fff;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    background-image: none;
    box-sizing: border-box;
    color: #606266;
    font-size: inherit;
    outline: none;
    padding: 0 8px;
    transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
}

.sale_ttl1 {
    width: 100px;
}

.inputMain {
    padding-left: 50px;
}

.addOtherInfo {
    margin: 20px 0 0;
}

.dataList {
    width:1000px;
}

.par_ttl {
    display: inline-block;
    width: 126px;
}

#newContectInfo .uploadify-button {
    line-height: 28px;
}

.sale_r {
    display: flex;
    justify-content: space-between;
}
.tjBtn{
    margin: 50px 0 0 84px;
}
.row_item{
    margin-bottom: 10px;
}
.row_ttl{
    padding: 0 20px;
}
/*#addInterview #interviewer{
    padding: 0 20px;
    width: 156px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #ccc;
    display: inline-block;
    background: #fff;
}*/
.dotBtn{
    font-weight: 500;
    cursor: pointer;
}
#choosePotContact .fa{ color: #50a9ea;    }
#contactSource{margin: 0 0 10px 76px;}
.see_otherContact li:last-child {  clear: inherit!important;  }
.see_otherContact li span:first-child {  margin-right: 20px;  }
.inputSize .form-control{width: 100%;}
.saleUploadBtn .uploadify-button{ display: inline-block;}
/*1.235*/
.txtTip{margin-right: 10px;}
.addInterview hr, #scan hr{border-top:1px solid #bebebe;}
.careBlue{color: #0070bf;}
.uploadify-queue,.headTip{display: none;}
.posRt{text-align: right;}
.wrapBody{margin: auto;width: 90%;}
.aBody{margin: auto;width: 70%;}
.narrowA{margin-left: 10px;width: 90%;}
.padLt{margin-left: 15px;}
.padBt{margin-bottom: 12px;}
.itemC{margin-bottom: 12px;}
.itemC p{margin-bottom: 4px;line-height: 32px;}
.entryCon textarea, .textMax{width:430px;}
#addOtherTogether .otherContact input{width:350px;}
#addOtherTogether .otherContact li{width:100%;}
#chooseFellowUn ul li{overflow: hidden;}
.redLinkBtn{color: red;display: inline-block;vertical-align: middle;font-weight: bold;}
.gapR{margin-right: 50px;}
#participant{float: right;width: 300px;}
.widBt{margin-bottom: 22px;line-height: 30px;}
.interviewerCon{width: 500px;display: inline-block;}
.businessCard .filePic {display: inline-block;width: 260px;height: 150px;background-size: contain;background-repeat: no-repeat;}
.recordForm table{width: 100%;}
.recordForm table .purposeCon{display: inline-block;}
.addInterview .flexBox{display: flex;justify-content: space-between;}
.addInterview .flexBox .linkBtn{white-space:nowrap;}
.recordForm table tr td:first-child{width: 142px;padding-right: 0;}
.recordForm table td {padding: 5px 15px;vertical-align: top;line-height: 30px;}
.viewsList { margin:20px auto;   }
.viewsList>li{ padding: 0 10px;list-style: none; background:#fff; line-height:30px;  }
.viewsList>li:nth-child(even){ background:#f5f5f5;  }
.viewsList .fa{ padding-right: 10px;color: #50a9ea;  }
.xing:before {content: "*";width: 5px;height: 5px;display: inline-block;color: red;font-weight: bolder;position: relative;}
.interviewList .ty-table{table-layout: fixed;}
.interviewList .ty-table tbody tr td:first-child{white-space:nowrap;text-overflow: ellipsis;overflow: hidden;}
