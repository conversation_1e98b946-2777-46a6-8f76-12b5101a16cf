package cn.sphd.miners.modules.authAccount.entity;

import cn.sphd.miners.common.persistence.BaseEntity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = " t_auth_acc_mdse")
public class AuthAccMdse extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;                                           //`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "name")
    private String name;                                       //'中文名称',
    @Column(name = "code")
    private String code;                                       //'号码',
    @Column(name = "orders")
    private Integer orders;                                    //'排序',
    @Column(name = "amount")
    private Long amount;                                    //'数量',
    @Column(name = "unit")
    private Byte unit;                                         //'单位:0-不限,1-年,2-季,3-月,4-日,5-小时,6-分钟',
    @Column(name = "price")
    private BigDecimal price;                                  //'价格',
    @Column(name = "enabled")
    private Byte enabled;                                      //'状态：0-不启用,1-启用',
    @Column(name = "enabled_time")
    private Date enabledTime;                                  //'启停用时间',
    @Column(name = "expired_time")
    private Integer expiredTime;                               //'斯限',
    @Column(name = "memo")
    private String memo;                                       //'备注',
    @Column(name = "creator", nullable = true)
    private Integer creator;
    @Column(name = "create_name", nullable = true, length = 100)
    private String createName;
    @Column(name = "create_time", nullable = true)
    private Date createTime;
    @Column(name = "updator", nullable = true)
    private Integer updator;
    @Column(name = "update_name", nullable = true, length = 100)
    private String updateName;
    @Column(name = "update_time", nullable = true)
    private Date updateTime;
    @Column(name = "operation", nullable = true, length = 1)
    private String operation;
    @Column(name = "previous_id", nullable = true)
    private Integer previousId;
    @Column(name = "version_no", nullable = true)
    private Integer versionNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Byte getUnit() {
        return unit;
    }

    public void setUnit(Byte unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Byte getEnabled() {
        return enabled;
    }

    public void setEnabled(Byte enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(Integer expiredTime) {
        this.expiredTime = expiredTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
