<template>
  <div id="potentialContact" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="6">{{details.name}}</el-col>
          <el-col :span="18">{{details.post}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="createDetail">录入者：{{details.createName}}  {{details.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</el-col>
        </el-row>
      </div>
      <div class="panel-content">
        <div class="contactList">
          <el-row v-if="details.mobile !== ''">
            <el-col class="icon-mobile" :span="24">{{details.mobile}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.mobile" v-bind:key="index">
            <el-col :span="24" :class="details.mobile === '' && index === 0?'icon-mobile':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.qq" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-qq':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.email" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-email':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.weixin" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-weixin':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.weibo" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-weibo':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.defined" v-bind:key="index">
            <el-col :span="24" class="icon-defined">{{social.name}}   {{social.code}}</el-col>
          </el-row>
        </div>
      </div>
      <div class="cus_visitCard" v-if="details.visitCard !== null && details.visitCard !== ''">
        <el-image
          style="width: 300px; height: 200px"
          :src="fileUrl + details.visitCard"
          fit="contain" @click="preview(details.visitCard)">
          <div slot="error" class="image-slot">
            <img :src="fileUrl + details.visitCard" />
          </div>
        </el-image>
      </div>
    </div>
    <el-dialog
      title=""
      :visible.sync="previewDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="previewDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="previewPath" :download="previewPath" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style>
  .contactList .el-row{
    border-bottom: 1px solid #ddd;
    line-height: 32px;
    padding: 6px 0;
  }
  .contactList .el-row .el-col{
    padding-left: 50px
  }
  .contactList .el-row .icon-mobile{
    background: url('../../../images/mobile.png') no-repeat 2%;
    background-size: 18px;
  }
  .contactList .el-row .icon-qq{
    background: url('../../../images/QQ.png') no-repeat 2%;
    background-size: 28px;
  }
  .contactList .el-row .icon-weixin{
    background: url('../../../images/weixin.png') no-repeat 2%;
    background-size: 28px;
  }
  .contactList .el-row .icon-weibo{
    background: url('../../../images/weibo.png') no-repeat 2%;
    background-size: 28px;
  }
  .contactList .el-row .icon-email{
    background: url('../../../images/email.png') no-repeat 2%;
    background-size: 28px;
  }
  .contactList .el-row .icon-defined{
    padding-left: 0;
  }
</style>

<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'potentialContact',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      details: {},
      socialList: {},
      browser: 1,
      imgTitle: '',
      previewPath: '',
      previewDialog: false,
      fileUrl: window.parent.$.fileUrl,
      webRoot: window.parent.$.webRoot
    }
  },
  created () {
    let _this = this
    this.axios.post('../../../sale/gettPotentialContactsSocial.do', {
      customerId: _this.$route.params.detailId,
      contactId: _this.$route.params.contactId
    }).then(function (response) {
      _this.details = response.data.data
      let list = response.data.data.socialList
      let socialList = {
        mobile: [],
        qq: [],
        email: [],
        weixin: [],
        weibo: [],
        defined: []
      }
      for (let i in list) {
        let type = list[i].type
        switch (type) {
          case '1':
            socialList.mobile.push(list[i])
            break
          case '2':
            socialList.qq.push(list[i])
            break
          case '3':
            socialList.email.push(list[i])
            break
          case '4':
            socialList.weixin.push(list[i])
            break
          case '5':
            socialList.weibo.push(list[i])
            break
          case '9':
            socialList.defined.push(list[i])
            break
        }
      }
      _this.socialList = socialList
      _this.loading = false
    }).catch(function (error) {
      console.log(error)
    })
  },
  methods: {
    preview: function (path) {
      this.previewDialog = true
      this.previewPath = path
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    }
  }
}
</script>
