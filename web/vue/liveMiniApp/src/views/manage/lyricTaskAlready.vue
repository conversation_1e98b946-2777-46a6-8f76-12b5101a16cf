<template>
	<div class="lyricTaskAlready">
    <Pageheader title="歌词任务处理" :showBack="true" :runMyBackFun="true" @myBackFun="goBack" />
    <div class="live-panel live-tr">
      {{ timeStr }}年共上传{{ list.length }}首
    </div>

    <div class="">
      <div class="lyricList">
        <div class="lyItem" v-for="(item, index) in list" :key="index">
          <div @click="goDetails(item)" class="lyItemC">
            <span class="name">{{ item.songName }}</span>
            <span class="time">{{ new Date(item.createTime).format('yyyy-MM-dd hh:mm:ss')   }}
            <arrow-right class="arrR" />
            </span>
          </div>
        </div>
      </div>
    </div>
	</div>
</template>
<script setup>
import JSONBig from 'json-bigint'
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { getUploadedLyricByMonth } from '@/api/music.js'


const router = useRouter();
const list = ref([]);
const timeStr = ref('');

onMounted(() => {
  getList()
})
// 跳转歌曲详情
const goDetails = (item) => {
  localStorage.setItem('lyricTaskAlreadyItem', JSONBig.stringify(item))
  router.push({ name: 'lyricTaskAlreadyInfo' });
}

const getList = () => {
  console.log('dayin')
  let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  console.log('jsonData=', jsonData)
  let lyricTaskAlreadyYear = JSONBig.parse(localStorage.getItem('lyricTaskAlreadyYear'))
  console.log('lyricTaskAlreadyYear=', lyricTaskAlreadyYear)
  let time = lyricTaskAlreadyYear.time
  timeStr.value = time
  let month = time.split('年')[0]
  let dayStr = time.split('年')[1]
  let day = dayStr.split('月')[0]
  day = Number(day) > 10 ? day : `0${ day }`
  let data = {
    month: month + '-' + day
  }
  getUploadedLyricByMonth(data).then( res => {
    console.log('getUploadedLyricByMonth res=', res)
    list.value = res.data.listRhythm
  }).catch(err=>{
    console.log('err=', err)
  })


}
const goBack = () => {
  router.push({ name: 'lycTaskHandel' });

}
</script>
<style lang="scss" scoped>
.lyricList{
  margin-top: 20px;
  background: #fff;
  .lyItem:nth-child(odd){
    background-color: #eee;
  }
  .lyItemC{
    display: flex;
    padding:0 10px;
    .name{
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 40px;
      flex: 3;
    }
    .arrR{
      width: 16px;
      display: inline-block;
      position: relative;
      top:2px;
    }
    .time{
      display: inline-block;
      float: right;
      line-height: 40px;
    }
  }
}


</style>
