<template>
  <div id="app22">
    <router-view/>
  </div>

</template>

<style lang="scss">
[class^="el-icon-fa"], [class*=" el-icon-fa"] {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome!important;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@import '../node_modules/font-awesome/css/font-awesome.css';
$fa-css-prefix: el-icon-fa;


#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  box-sizing: border-box;
  //background-color: #fefefe;
}

*{
  padding: 0; margin:0;
  font-family: "Helvetica Neue",Helvetica,Arial,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}

</style>
<script setup>
// import auth from '@/sys/auth'
// import { ElMessage } from 'element-plus'
// import initStorage from '@/sys/initStorage'
// import sphdSocket from '@/sys/sphd'
// import { onMounted } from "vue";
// import { useRouter } from "vue-router";
// import JSONBig from 'json-bigint'
// import { sureLogin, callLogin, tpCodeBindAcc   } from '@/api/api.js'
//
// let localUrl22 = window.location.href
// if(localUrl22.indexOf('?u=') > -1) {
//   let allu22 = localUrl22.split('#/')[0]
//   let uStr33 = allu22.split('?u=')[1]
//   uStr33 = decodeURIComponent(uStr33)
//   let aa = JSONBig.parse(uStr33)
//
//   localStorage.clear()
//
// }
//
//
// const router2 = useRouter();
//
// auth.init({isGuest: true})
// initStorage({})
// sphdSocket.start()
// let acceptData = ''
//
//
// onMounted(() => {
//
//   let localUrl = window.location.href
//   let jsonData = {}
//   let urlJson = {}
//   if(localUrl.indexOf('?u=') > -1){
//     let allu = localUrl.split('#/')[0]
//     let newUrl=allu.split('?u=')[0]
//     let uStr = allu.split('?u=')[1]
//     uStr = decodeURIComponent(uStr)
//     console.log('uStr=', uStr)
//     urlJson = JSONBig.parse(uStr)
//     console.log('接到的所有参数 urlJson=', urlJson)
//     acceptData = urlJson
//     jsonData = urlJson
//     localStorage.removeItem('jsonData')
//     localStorage.removeItem('org')
//     localStorage.removeItem('user')
//
//     console.log('jsonData=', jsonData)
//     console.log('这里是刚进来存储的' )
//
//
//   }
//
// // 正常使用的
//   let localJsonData = jsonData
//   login(localJsonData)
//
//
//
//
// })
//
// const login = (localJsonData) =>{
//   console.log('登陆 login 传进来的 ', localJsonData)
//   let data = {
//     // tgCode:'byteDance',
//     tgCode:'byteDance',
//     code: localJsonData.code,
//     userInfo: JSONBig.stringify(localJsonData.userInfo)
//   }
//   callLogin(acceptData).then(resAll => {
//     let res = resAll.data
//     console.log(' 登陆返回值=', res)
//     localJsonData.loginRes = res
//     // 登陆接口的处理
//     if(res.success>0) { // 已经有私人领域的账号了,需要选择机构
//       localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
//       router2.push({ name: 'orgList' });
//     }
//     else { // 还没有 私人领域的 账号
//       let error = res.error
//       console.log('error.code=', error.code)
//       switch (error.code) {
//         case '2': //需要短信验证激活
//           ElMessage.error('需要短信验证激活！');
//           break
//         case '3': //需要注册，调用auth/sendMessageVerificationCodeRegister.do注册通用框架账号
//           // ElMessage.error('需要注册');
//           localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
//           router2.push({ name: 'Start' });
//           break
//         case '4': //可能是授权或者code问题，建议重试tt.login
//           ElMessage.error('可能是授权或者code问题，建议重试wx.login！');
//
//           break
//         case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
//           ElMessage.error('服务器问题，比如wonderss服务器访问微信/字节服务器异常');
//           console.log('服务器问题，比如wonderss服务器访问微信/字节服务器异常')
//           break
//       }
//
//     }
//   }).catch((err)=>{
//     console.log('callLogin err', err)
//   })
//
// }
//
//
//
//
//
</script>