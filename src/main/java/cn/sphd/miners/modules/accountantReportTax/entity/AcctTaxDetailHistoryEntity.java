package cn.sphd.miners.modules.accountantReportTax.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-09-06 
 */

@Entity ( name ="AcctTaxDetailHistoryEntity" )
@Table ( name ="t_accountant_tax_detail_history" )
public class AcctTaxDetailHistoryEntity implements Serializable {

	private static final long serialVersionUID =  8602287286460828717L;

	/**
	 * ID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
   	@Column(name = "id" )
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 申报明细ID
	 */
   	@Column(name = "tax_detail" )
	private Integer taxDetail;

	/**
	 * 税种报表ID
	 */
   	@Column(name = "tax_report" )
	private Integer taxReport;

	/**
	 * 税种报表历史ID
	 */
   	@Column(name = "tax_report_history" )
	private Integer taxReportHistory;

	/**
	 * 税种ID
	 */
   	@Column(name = "tax" )
	private Integer tax;

	/**
	 * 报表ID
	 */
   	@Column(name = "report" )
	private Integer report;

	/**
	 * 报表历史ID
	 */
	@Column(name = "report_history" )
	private Integer reportHistory;

	/**
	 * 税种历史ID
	 */
	@Column(name = "tax_history" )
	private Integer taxHistory;

	/**
	 * 申报期YYYYMMDD
	 */
   	@Column(name = "period" )
	private String period;

	/**
	 * 申报期起始YYYYMMDD
	 */
	@Column(name = "period_begin" )
	private String periodBegin;

	/**
	 * 申报期截止YYYYMMDD
	 */
	@Column(name = "period_end" )
	private String periodEnd;

	/**
	 * 税款所属开始时期YYYYMMDD
	 */
	@Column(name = "belongs_begin" )
	private String belongsBegin;

	/**
	 * 税款所属姐追时期YYYYMMDD
	 */
	@Column(name = "belongs_end" )
	private String belongsEnd;

	/**
	 * 财务明细账ID
	 */
	@Column(name = "account_detail" )
	private Integer accountDetail;

	/**
	 * 状态:0-未报,1-已报
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 申报时间
	 */
   	@Column(name = "delare_time" )
	private Date delareTime;

	/**
	 * 应缴金额
	 */
   	@Column(name = "plan_amount" )
	private BigDecimal planAmount;

	/**
	 * 实缴金额
	 */
   	@Column(name = "fact_amount" )
	private BigDecimal factAmount;

	/**
	 * 缴款时间
	 */
   	@Column(name = "pay_time" )
	private Date payTime;

	/**
	 * 赁证路径
	 */
   	@Column(name = "document_path" )
	private String documentPath;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public String getBelongsBegin() {
		return belongsBegin;
	}

	public void setBelongsBegin(String belongsBegin) {
		this.belongsBegin = belongsBegin;
	}

	public String getBelongsEnd() {
		return belongsEnd;
	}

	public void setBelongsEnd(String belongsEnd) {
		this.belongsEnd = belongsEnd;
	}

	public String getPeriodBegin() {
		return periodBegin;
	}

	public void setPeriodBegin(String periodBegin) {
		this.periodBegin = periodBegin;
	}

	public String getPeriodEnd() {
		return periodEnd;
	}

	public void setPeriodEnd(String periodEnd) {
		this.periodEnd = periodEnd;
	}

	public Integer getAccountDetail() {
		return accountDetail;
	}

	public void setAccountDetail(Integer accountDetail) {
		this.accountDetail = accountDetail;
	}

	public Integer getReportHistory() {
		return reportHistory;
	}

	public void setReportHistory(Integer reportHistory) {
		this.reportHistory = reportHistory;
	}

	public Integer getTaxHistory() {
		return taxHistory;
	}

	public void setTaxHistory(Integer taxHistory) {
		this.taxHistory = taxHistory;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getTaxDetail() {
		return this.taxDetail;
	}

	public void setTaxDetail(Integer taxDetail) {
		this.taxDetail = taxDetail;
	}

	public Integer getTaxReport() {
		return this.taxReport;
	}

	public void setTaxReport(Integer taxReport) {
		this.taxReport = taxReport;
	}

	public Integer getTaxReportHistory() {
		return this.taxReportHistory;
	}

	public void setTaxReportHistory(Integer taxReportHistory) {
		this.taxReportHistory = taxReportHistory;
	}

	public Integer getTax() {
		return this.tax;
	}

	public void setTax(Integer tax) {
		this.tax = tax;
	}

	public Integer getReport() {
		return this.report;
	}

	public void setReport(Integer report) {
		this.report = report;
	}

	public String getPeriod() {
		return this.period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Date getDelareTime() {
		return this.delareTime;
	}

	public void setDelareTime(Date delareTime) {
		this.delareTime = delareTime;
	}

	public BigDecimal getPlanAmount() {
		return this.planAmount;
	}

	public void setPlanAmount(BigDecimal planAmount) {
		this.planAmount = planAmount;
	}

	public BigDecimal getFactAmount() {
		return this.factAmount;
	}

	public void setFactAmount(BigDecimal factAmount) {
		this.factAmount = factAmount;
	}

	public Date getPayTime() {
		return this.payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public String getDocumentPath() {
		return this.documentPath;
	}

	public void setDocumentPath(String documentPath) {
		this.documentPath = documentPath;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
