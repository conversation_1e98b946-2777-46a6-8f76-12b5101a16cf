<template>
  <div id="interview_addContact" v-show="show">
    <TY_NavTop title="潜在客户申请" :isReBack="true" @reback="closeShow"></TY_NavTop>
    <div class="container">
      <div class="panel-content sect-con">
        <el-row>
          <el-col :span="6">姓名<span class="red" v-if="editType.source === 3">*</span></el-col>
          <el-col :span="18"><el-input size="mini" placeholder="请输入内容" v-model="details.name"></el-input></el-col>
        </el-row>
        <el-row v-if="editType.source === 3">
          <el-col :span="6">所在公司或组织</el-col>
          <el-col :span="18"><el-input size="mini" placeholder="请输入内容" v-model="details.company"></el-input></el-col>
        </el-row>
        <el-row>
          <el-col :span="6">职位</el-col>
          <el-col :span="18"><el-input size="mini" placeholder="请输入内容" v-model="details.post"></el-input></el-col>
        </el-row>
        <el-row>
          <el-col :span="6">手机</el-col>
          <el-col :span="18"><el-input size="mini" placeholder="请输入内容" v-model="details.mobile"></el-input></el-col>
        </el-row>
        <div v-for="(it, i) in contactTypeList" :key="i">
          <el-row class="socialList" v-for="(social, index) in details.socialList[it.eName]" :key="index">
            <el-col :span="6" v-if="social.type === '9' || social.type === 9">{{social.name}}</el-col>
            <el-col :span="6" v-else>{{it.name}}</el-col>
            <el-col :span="12">{{social.code}}</el-col>
            <el-col :span="6"><el-button type="danger" size="mini" @click="delContact(it.eName, index)">删除</el-button></el-col>
          </el-row>
        </div>
      </div>
      <div class="panel-content gap">
        <el-collapse v-model="activeNames" accordion>
          <el-collapse-item title="添加更多联系方式" name="1" class="primary">
            <el-row>
              <el-col :span="6">
                <el-select class="spc-select" v-model="contactType" placeholder="请选择" size="mini">
                  <el-option value="1" label="手机"></el-option>
                  <el-option value="2" label="QQ"></el-option>
                  <el-option value="3" label="邮箱"></el-option>
                  <el-option value="4" label="微信"></el-option>
                  <el-option value="5" label="微博"></el-option>
                  <el-option value="9" label="自定义"></el-option>
                </el-select>
              </el-col>
              <el-col :span="18" v-if="contactType !== ''" class="definedArea">
                <el-input placeholder="请输入内容" v-model="contactValue" class="input-with-select" size="mini">
                  <el-input class="definedInput" v-if="contactType === '9'" v-model="definedTitle" slot="prepend" placeholder="请录入自定义标签" size="mini"></el-input>
                </el-input>
              </el-col>
            </el-row>
            <el-button type="primary" size="small" @click="addOtherContact" :disabled="contactType === '' && contactValue === ''">添加</el-button>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="panel-content">
        <el-row>
          <el-col :span="24">名片照片</el-col>
        </el-row>
        <el-row>
          <el-col :span="20" class="handle-center editUploadFile">
            <el-upload
              class="avatar-uploader"
              :action="webRoot+'/uploads/uploadfyByFile.do'"
              :data = "uploadData"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
            >
              <img v-if="details.visitCard"
                   :src="fileUrl + details.visitCard"
                   class="avatar row-bg" type="flex" justify="space-between"
              >
              <i class="el-icon-plus avatar-uploader-icon" v-else></i>
            </el-upload>
          </el-col>
          <el-col :span="4" v-if="details.visitCard">
            <el-button type="text" @click="handleRemove">删除</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="handle_button handle-center">
        <el-button type="primary" @click="contactUpdateSure" size="medium">提交</el-button>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #interview_addContact{
    position: fixed;
    top: 0;
    left: 0;
    z-index:1000;
    width: 100%;
    height: 100%;
    background: #fff;
  }
  #contactList .el-button {
    margin-left: 10px;
  }
</style>
<script>
import { gohistory, handleNull } from '../../../js/common'
export default {
  name: 'interview_addContact',
  props: {
    details: Object,
    editType: Object,
    potentialId: String,
    show: Boolean
  },
  filters: {
    gohistory,
    handleNull
  },
  data () {
    return {
      loading: true,
      activeNames: '',
      contactTypeList: [
        {
          type: 1,
          name: '手机',
          eName: 'mobile'
        }, {
          type: 2,
          name: 'QQ',
          eName: 'qq'
        }, {
          type: 3,
          name: '邮箱',
          eName: 'email'
        }, {
          type: 4,
          name: '微信',
          eName: 'weixin'
        }, {
          type: 5,
          name: '微博',
          eName: 'weibo'
        }, {
          type: 9,
          name: '自定义',
          eName: 'defined'
        }
      ],
      contactType: '',
      contactValue: '',
      definedTitle: '',
      fileUrl: window.parent.$.fileUrl,
      webRoot: window.parent.$.webRoot,
      uploadData: {
        module: '潜在客户',
        userId: this.sphdSocket.user.userID
      }
    }
  },
  created () {
    let _this = this
    _this.loading = false
  },
  methods: {
    addOtherContact: function () {
      let type = this.contactType
      let value = this.contactValue
      let item = this.contactTypeList.find(item => Number(item.type) === Number(type))
      if (type === '9' || type === 9) {
        let definedLable = this.definedTitle
        if (item) {
          this.details.socialList[item.eName].push({
            code: value,
            type: item.type,
            name: definedLable
          })
        }
      } else {
        if (item) {
          this.details.socialList[item.eName].push({
            code: value,
            type: item.type,
            name: item.name
          })
        }
      }
      this.contactType = ''
      this.contactValue = ''
      this.activeNames = ''
    },
    delContact: function (eName, index) {
      this.details.socialList[eName].splice(index, 1)
    },
    handleAvatarSuccess (file) {
      // 删除之前的照片
      let option = {type: 'fileId', fileId: this.details.visitCardUid}
      window.parent.cancelFileDel(option, true)
      // 赋值记录新的照片
      this.details.cardPath = file.filename
      this.details.visitCard = file.filename
      this.details.visitCardUid = file.fileUid
      let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
      fileDelArr.push({'type':'fileId', 'fileId': file.fileUid})
      window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    },
    handleRemove () {
      let option = {type: 'fileId', fileId: this.details.visitCardUid}
      window.parent.cancelFileDel(option, true)
      this.details.cardPath = ''
      this.details.visitCard = ''
      this.details.visitCardUid = ''
    },
    contactUpdateSure: function () {
      let _this = this
      if (_this.editType.source === 3 && _this.details.name === '') {
        this.$kiko_message('请录入姓名！')
        return false
      }
      _this.loading = true
      let social = []
      let socialList = _this.details.socialList
      for (var a in socialList.mobile) {
        social.push(socialList.mobile[a])
      }
      for (var b in socialList.qq) {
        social.push(socialList.qq[b])
      }
      for (var c in socialList.email) {
        social.push(socialList.email[c])
      }
      for (var d in socialList.weixin) {
        social.push(socialList.weixin[d])
      }
      for (var e in socialList.weibo) {
        social.push(socialList.weibo[e])
      }
      for (var f in socialList.defined) {
        social.push(socialList.defined[f])
      }
      social = JSON.stringify(social)
      let json = {
        name: _this.details.name,
        memo: _this.details.name,
        post: _this.details.post,
        mobile: _this.details.mobile,
        socialList: social,
        visitCard: _this.details.visitCard,
        customer: _this.potentialId
      }
      if (_this.editType.source === 1) {
        let url = '../../../sales/addCustomerContact.do'
        _this.axios.post(url, json).then(function (res) {
          let option = {type: 'fileId', fileId: _this.details.visitCardUid}
          json.id = res.data.id
          json.participator = res.data.id
          window.parent.cancelFileDel(option)
          _this.loading = false
          _this.$emit('add_contact', json)
        }).catch(function (error) {
          console.log(error)
        })
      } else {
        if (_this.editType.source === 3) {
          json.cardPath = _this.details.cardPath
        }
        if (_this.editType.type === 1) {
          json.id = Math.random() + ''
          json.participator = json.id
        }
        _this.$emit('add_contact', json)
      }
      _this.closeShow()
    },
    closeShow: function () {
      this.$emit('update:show', false)
    }
  },
  destroyed: function () {
    window.parent.cancelFileDel({type: 'fileId', fileId: this.details.visitCardUid}, true)
  }
}
</script>
