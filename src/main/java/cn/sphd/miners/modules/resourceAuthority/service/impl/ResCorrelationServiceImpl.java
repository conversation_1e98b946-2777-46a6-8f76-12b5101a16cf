package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.resourceAuthority.dao.ResCorrelationDao;
import cn.sphd.miners.modules.resourceAuthority.dao.ResCorrelationHisDao;
import cn.sphd.miners.modules.resourceAuthority.dto.ResAndResAttNameDto;
import cn.sphd.miners.modules.resourceAuthority.entity.*;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResAclMapper;
import cn.sphd.miners.modules.resourceAuthority.service.ResAttService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCorrelationService;
import cn.sphd.miners.modules.resourceAuthority.service.ResNoticeService;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2022/1/28.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class ResCorrelationServiceImpl implements ResCorrelationService {

    @Autowired
    ResCorrelationDao resCorrelationDao;
    @Autowired
    ResCorrelationHisDao resCorrelationHisDao;


    @Autowired
    ResAttService resAttService;
    @Autowired
    ResService resService;
    @Autowired
    ResNoticeService resNoticeService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserService userService;


    @Autowired
    ResAclMapper resAclMapper;


    @Override
    public ResCorrelation addCorrliation(User user, Integer resource, Integer attId, String operation, Date createDate) {
        ResCorrelation resCorrelation = new ResCorrelation();
        resCorrelation.setAttachment(attId);
        resCorrelation.setResource(resource);
        resCorrelation.setOperation(operation);
        resCorrelation.setOrg(user.getOid());
        resCorrelation.setCreator(user.getUserID());
        resCorrelation.setCreateName(user.getUserName());
        resCorrelation.setCreateDate(createDate);
        resCorrelationDao.save(resCorrelation);
        this.addResCorrelationHis(user,resource,attId,resCorrelation.getId(),operation,createDate);
        return resCorrelation;

    }

    @Override
    public ResCorrelationHis addResCorrelationHis(User user, Integer resource, Integer attId,  Integer corrId, String operation, Date createDate) {
        ResCorrelationHis resCorrelationHis = new ResCorrelationHis();
        resCorrelationHis.setCorrelation(corrId);
        resCorrelationHis.setAttachment(attId);
        resCorrelationHis.setResource(resource);
        resCorrelationHis.setOperation(operation);
        resCorrelationHis.setCreator(user.getUserID());
        resCorrelationHis.setCreateName(user.getUserName());
        resCorrelationHis.setCreateDate(createDate);
        resCorrelationHisDao.save(resCorrelationHis);
        return resCorrelationHis;
    }

    @Override
    public Integer checkCorrelation(Integer attId, String operation) {
        String hql = "select count(id) from ResCorrelation where attachment = :attachment";
        HashMap<String,Object> param = new HashMap<>();
        param.put("attachment",attId);
        if (operation != null) {
            hql = hql + " and operation = :operation";
            param.put("operation",operation);
        }
        Long num = (Long) resCorrelationDao.getByHQLWithNamedParams(hql,param);
        Integer state = 1;
        if (num > 0) {
            state = 2;
        }
        return state;
    }

    @Override
    public HashMap<String, Object> threeformAddCorrelation(User user, Integer id, Integer resource) {
        ResCorrelation resCorrelation = this.getResCorrelationByIdAndRes(id,resource);
        Integer state = 1;
        HashMap<String,Object> map = new HashMap<>();
        ResAtt resAtt = resAttService.getResAttSingle(id);
        if (resCorrelation != null) {
            if ("5".equals(resCorrelation.getOperation())){
                resCorrelation.setOperation("4");
                resCorrelation.setCreator(user.getUserID());
                resCorrelation.setCreateName(user.getUserName());
                resCorrelation.setCreateDate(new Date());
                this.addResCorrelationHis(user,resource,id,resCorrelation.getId(),"4",resCorrelation.getCreateDate());
                if(!resAtt.getEnabled()){
                    resAtt.setEnabled(true);
                }
                map.put("resAtt", resAtt);
            } else {
                state = 2;
            }
        } else {
            resCorrelation = this.addCorrliation(user,resource,id,"4", new Date());
            if ("0".equals(resAtt.getState())) {
                resAtt.setState("1");
            }
            if(!resAtt.getEnabled()){
                resAtt.setEnabled(true);
            }
            map.put("resAtt", resAtt);
        }
        if (state == 1) {
            List<Integer> listUserids =  resAclMapper.getAllUserIdsByFileId(resource);
            resNoticeService.addSpecialAuthUserId(listUserids, user);
            resNoticeService.sendResFileNotice(user, resource, resAtt, resCorrelation.getId(), null, null, null, null, "10", listUserids, null, null);
        }
        map.put("state", state);
        return map;
    }

    @Override
    public HashMap<String, Object>  removeCorrelation(User user, Integer id, Integer resource) {
        ResCorrelation r = this.getResCorrelationByIdAndRes(id,resource);
        Integer state = 1;
        if ("5".equals(r.getOperation())){
            state = 2;
        } else {
            r.setOperation("5");
            r.setUpdator(user.getUserID());
            r.setUpdateName(user.getUserName());
            r.setUpdateDate(new Date());
            this.addResCorrelationHis(user,resource,id,r.getId(),"5",r.getUpdateDate());
            Integer correlationState = this.checkCorrelation(id, "4");
            ResAtt resAtt = resAttService.getResAttSingle(id);
            if (correlationState == 1) {
                resAtt.setEnabled(false);
            }
            List<Integer> listUserids =  resAclMapper.getAllUserIdsByFileId(resource);
            resNoticeService.addSpecialAuthUserId(listUserids, user);
            resNoticeService.sendResFileNotice(user, resource, resAtt, r.getId(), null, null, null, null, "11", listUserids, null, null);
        }
        HashMap<String,Object> map = new HashMap<>();
        map.put("state", state);
        return map;
    }

    @Override
    public HashMap<String, Object> getRemoveCorrelationState(Integer id, Integer resource) {
        ResCorrelation r = this.getResCorrelationByIdAndRes(id,resource);
        Integer state = 1;
        HashMap<String,Object> map = new HashMap<>();
        if ("5".equals(r.getOperation())){
            state = 3;
        } else {
            String hql = " from ResCorrelation where operation = :operation and attachment = :attachment order by createDate desc";
            HashMap<String,Object> param = new HashMap<>();
            param.put("operation","4");
            param.put("attachment",id);
            List<ResCorrelation> list = resCorrelationDao.getListByHQLWithNamedParams(hql,param);
            if (!list.isEmpty()) {
                if (list.size() == 1) {
                    if (list.get(0).getResource().equals(resource)) {
                        state = 2;
                    } else {
                        state = 3;
                    }
                } else {
                    List<ResCorrelation> listRes = new ArrayList<>();
                    for (ResCorrelation rc : list) {
                        if (!rc.getResource().equals(resource)) {
                            ResEntity res = resService.getSingle(rc.getResource());
                            rc.setResEntity(res);
                            listRes.add(rc);
                        }
                    }
                    map.put("list", listRes);
                }
            } else {
                state = 3;
            }
        }
        map.put("state", state);
        return map;
    }

    @Override
    public List<ResCorrelation> getCorrealtionFile(Integer attId, Integer fileId, String type, PageInfo pageInfo) {
        String hql = " from ResCorrelation where operation = :operation ";
        HashMap<String,Object> param = new HashMap<>();
        if ("3".equals(type)) {
            hql = hql + " and resource = :resource";
            param.put("operation","4");
            param.put("resource",fileId);
        }else {
            if ("1".equals(type)) {
                param.put("operation","4");
            } else {
                param.put("operation","5");
            }
            hql = hql + " and attachment = :attachment";
            param.put("attachment",attId);
        }
        hql = hql + " order by createDate desc";
        List<ResCorrelation> list = resCorrelationDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        if (!list.isEmpty()) {
            for (ResCorrelation rc : list) {
                if (attId != null) {
                    ResEntity r = resService.getSingle(rc.getResource());
                    rc.setResEntity(r);
                }
                if (fileId != null) {
                    ResAtt resAtt = resAttService.getResAttSingle(rc.getAttachment());
                    rc.setResAtt(resAtt);
                }
            }
        }
        return list;
    }

    @Override
    public List<ResCorrelation> getCorrFile(Integer id) {
        String hql = " from ResCorrelation where attachment = :attachment and operation = :operation order by createDate desc";
        HashMap<String,Object> param = new HashMap<>();
        param.put("attachment",id);
        param.put("operation","4");
        List<ResCorrelation> list = resCorrelationDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public List<ResCorrelationHis> getCorrelationHistory(Integer attId, Integer fileId, PageInfo pageInfo) {
        String hql = " from ResCorrelationHis ";
        HashMap<String,Object> param = new HashMap<>();
        if (attId != null) {
            hql = hql + " where attachment = :attachment";
            param.put("attachment",attId);
        }
        if (fileId != null) {
            hql = hql + " where resource = :resource";
            param.put("resource",fileId);
        }
        List<ResCorrelationHis> list = resCorrelationHisDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        if (!list.isEmpty()) {
            for (ResCorrelationHis rch : list) {
                if (attId != null) {
                    ResEntity r = resService.getSingle(rch.getResource());
                    rch.setResEntity(r);
                }
                if (fileId != null) {
                    ResAtt resAtt = resAttService.getResAttSingle(rch.getAttachment());
                    rch.setResAtt(resAtt);
                }
            }
        }
        return list;
    }

    @Override
    public List<ResCorrelation> getCorrelationResAtt(Integer fileId, PageInfo pageInfo) {
        String hql = " from ResCorrelation where resource = :resource and operation = :operation order by createDate desc";
        HashMap<String,Object> param = new HashMap<>();
        param.put("resource",fileId);
        param.put("operation","4");
        List<ResCorrelation> list = resCorrelationDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        if (!list.isEmpty()) {
            for (ResCorrelation rc : list) {
                ResAtt resAtt = resAttService.getResAttSingle(rc.getAttachment());
                rc.setResAtt(resAtt);
            }
        }
        return list;
    }

    @Override
    public void batchRemoveCorrelation(Integer fileId, User user) {
        String hql = " from ResCorrelation where operation = :operation and resource = :resource";
        HashMap<String,Object> param = new HashMap<>();
        param.put("operation", "4");
        param.put("resource",fileId);
        List<ResCorrelation> listCorrelation = resCorrelationDao.getListByHQLWithNamedParams(hql, param);
        String status = "1";
        if (!listCorrelation.isEmpty()) {
            for (ResCorrelation r : listCorrelation) {
                this.removeCorrelation(user,r.getAttachment(),fileId);
            }
        }
    }

    @Override
    public HashMap<String,Object> getCorrelationForAbolishFile(Integer fileId) {
        String hql = " from ResCorrelation where resource = :resource and operation = :operation order by createDate desc";
        HashMap<String,Object> param = new HashMap<>();
        param.put("resource",fileId);
        param.put("operation","4");
        List<ResCorrelation> list = resCorrelationDao.getListByHQLWithNamedParams(hql,param);
        List<ResAndResAttNameDto> listResAtt = new ArrayList<>();
        List<ResAndResAttNameDto> listResAndResAtt = new ArrayList<>();
        if (!list.isEmpty()) {
            for (ResCorrelation rc : list) {
                String hqlAtt = " from ResCorrelation where attachment = :attachment and operation = :operation order by createDate desc";
                HashMap<String,Object> paramAtt = new HashMap<>();
                paramAtt.put("attachment",rc.getAttachment());
                paramAtt.put("operation","4");
                List<ResCorrelation> listAtt = resCorrelationDao.getListByHQLWithNamedParams(hqlAtt,paramAtt);
                if (!listAtt.isEmpty()) {
                    ResAtt resAtt = resAttService.getResAttSingle(rc.getAttachment());
                    ResAndResAttNameDto resAndResAttNameDto = new ResAndResAttNameDto();
                    resAndResAttNameDto.setResAttNameAndSn(resAtt.getSn() + "/" + resAtt.getName());
                    if (listAtt.size() == 1) {
                        listResAtt.add(resAndResAttNameDto);
                    } else {
                        List<String> listname = new ArrayList<>();
                        for (ResCorrelation r : listAtt) {
                            if (!(fileId.equals(r.getResource()))) {
                                ResEntity resEntity = resService.getSingle(r.getResource());
                                listname.add(resEntity.getFileSn() + "/" + resEntity.getName());
                            }
                        }
                        resAndResAttNameDto.setResNameAndSn(listname);
                        listResAndResAtt.add(resAndResAttNameDto);
                    }
                }
            }
        }
        HashMap<String,Object> map = new HashMap<>();
        map.put("listResAtt", listResAtt);
        map.put("listResAndResAtt", listResAndResAtt);
        return map;
    }

    @Override
    public ResCorrelation getSingleResCorrelation(Integer id) {
        ResCorrelation resCorrelation = resCorrelationDao.get(id);
        return resCorrelation;
    }

    //根据附件id和文件id获取其关联关系
    private ResCorrelation getResCorrelationByIdAndRes(Integer id, Integer resource){
        String hql = " from ResCorrelation where attachment = :attachment and resource = :resource";
        HashMap<String,Object> param = new HashMap<>();
        param.put("attachment",id);
        param.put("resource",resource);
        ResCorrelation r = (ResCorrelation) resCorrelationDao.getByHQLWithNamedParams(hql,param);
        return r;
    }

}
