package cn.sphd.miners.modules.personal.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.common.utils.UploadImages;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillService;
import cn.sphd.miners.modules.message.service.MessageService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.entity.PersonnelReimbursetAttachment;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.personal.service.PersonnelReimbursetAttachmentService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.CodeService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * Created by Administrator on 2016/12/12.
 */
@Controller
@RequestMapping("/expense")
public class ExpenseController {
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    PersonnelReimbursetAttachmentService personnelReimbursetAttachmentService;
    @Autowired
    CodeService codeService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    MessageService messageService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    FinanceReimburseBillService financeReimburseBillService;



    //======================  报销申请  ==============================

    /*
    **
    *<AUTHOR>
    *@Date 2016/12/12 15:30
    *查询全部费用类别
    */
    @ResponseBody
    @RequestMapping("/querySysCode.do")
    public void QuerySysCode(User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        CodeCategory codeCategory=codeService.getCodeCategoryByNameOid(user.getOid(),"费用类别");
        if(codeCategory!=null){

            List<Code> feeCats = codeService.getCodesByCategory(codeCategory.getId());
            List list=new ArrayList();
            for(Code code:feeCats){
                if(code.getEnabled()==1){
                    list.add(code);
                }

            }

            map.put("feeCats", list);
        }else {
            map.put("result",0);//"数据库‘字典分类表’中不存在可用‘费用类别！’"
        }
        ObjectToJson.objectToJson1(map, new String[]{"org","codeHashSet","parent","category"}, response);
    }


    /*
    **
    *<AUTHOR>
    *@Date 2016/12/12 15:32
    * 查询全部票据种类
    */
    @ResponseBody
    @RequestMapping("/queryCodeCategory.do")
    public void QueryCodeCategory(User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        CodeCategory codeCategory=codeService.getCodeCategoryByNameOid(user.getOid(),"票据种类");
        if(codeCategory!=null){

            List<Code> billCats = codeService.getCodesByCategory(codeCategory.getId());
            map.put("billCats", billCats);
            ObjectToJson.objectToJson1(map, new String[]{"org","codeHashSet","parent","category"}, response);

        }else {
            map.put("result",0);//"数据库‘字典分类表’中不存在可用‘票据种类！’"
            ObjectToJson.objectToJson1(map, new String[]{"org","codeHashSet","parent","category"}, response);

        }

    }



    /*
   **
   *<AUTHOR>
   *@Date 2016/12/9 14:08
   * 打开报销申请页面
   */
    @RequestMapping("/toPersonnelReimburse.do")
    public String toPersonnelReimburse( Model model){
        return "/expense/toPersonnelReimburse";
    }


//=========================  报销申请  ===========================


    /**
     * <AUTHOR>
     * @Date 2016/12/22 11:18
     * 查看报销申请处理详情
     */
    @ResponseBody
    @RequestMapping("/getPersonnelReimburseInfo.do")
    public void getPersonnelReimburseInfo(Integer approvalProcessId,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        if (approvalProcessId!=null){
            ApprovalProcess a=approvalProcessService.getApprovalProcessById(approvalProcessId);
            PersonnelReimburse p=personnelReimburseService.personnelReimburseById(a.getReimburse().getId());
            map.put("attachmentList",p.getPersonnelReimbursetAttachmentHashSet());//附件列表
            List<ApprovalProcess> processList=approvalProcessService.getApprovalProcessByPersonnelReimburseId(p.getId());
            map.put("processList",processList);//审批流程列表
            map.put("personnelReimburse",p);
            map.put("approvalProcessId",approvalProcessId);
            map.put("status",1);
        }else {
            map.put("status",0);//获取失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"reimburseBill","reimburse","accountDetail","user","personnelReimbursetAttachmentHashSet","approvalProcessHashSet","reimburset","approvalFlow","reimburse"},response);
    }


    //跳转未知页面
    @RequestMapping("/chargeSubmitSale.do")
    public String chargeSubmitSale(){
        return "/user/chargeSubmitSale";
    }



    @ResponseBody
    @RequestMapping("/uploadImages.do")
    public void uploadImages(HttpServletRequest request,HttpServletResponse response,@RequestParam(value = "imgFile",required = false) MultipartFile[] files) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        String personnelReimburseId=request.getParameter("personnelReimburseId");
        if (files!=null){
            for (MultipartFile f : files) {
                PersonnelReimburse personnelReimburse=personnelReimburseService.personnelReimburseById(Integer.valueOf(personnelReimburseId));
                String path=UploadImages.uploadImages(request,f);//存图片的路径
                PersonnelReimbursetAttachment personnelReimbursetAttachment = new PersonnelReimbursetAttachment();
                personnelReimbursetAttachment.setPath(path);
                personnelReimbursetAttachment.setReimburset(personnelReimburse);//报销ID
                personnelReimbursetAttachmentService.addPersonnelReimbursetAttachment(personnelReimbursetAttachment);

            }
            map.put("status", 1);//成功

        }else {
            map.put("status", 0);//失败

        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

    }

}
