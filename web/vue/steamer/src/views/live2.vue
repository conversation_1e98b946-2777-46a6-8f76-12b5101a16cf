<template>
  <div id='player' v-html="lyricTxt"></div>
<!--  <div id='hidden'>-->
<!--    <video id='video' ref="video" hidden preload crossorigin='anonymous' playsinline='' webkit-playsinline='' x5-video-player-type='h5' ></video>-->
<!--  </div>-->
</template>
<script lang="js">
import qs from 'qs'
import { lyricLive } from '@/api/api.js'

export default {
  name: "live2",
  data() {
    return {
      lyricTxt:'',
      playingTime:0
    };
  },
  components: {
  },
  created(){
    let params = qs.parse(location.hash.substring(location.hash.lastIndexOf('?')), { ignoreQueryPrefix: true })
    console.log(params.accId,params)
    let that = this
    setInterval(function () {
      that.getLyricLive(params.accId);
    }, 250)
  },
  methods: {
    getLyricLive(accId){
      let video = this.$refs.video
      lyricLive(accId).then(res => {
        let live = res.data.data
        // console.log('live', live.lyric, live.playing, live)
        this.lyricTxt = live.lyric
        if(live.playing) {
          this.playingTime = new Date().getTime()
          // let currentTime = live.timeMillis/1000
          // if(Math.abs(currentTime - video.currentTime)>1) {
          //   if(typeof video.fastSeek ==  'function') {
          //     video.fastSeek(currentTime)
          //   } else {
          //     video.currentTime = currentTime
          //   }
          //   // console.log('seeked', video.currentTime)
          // }
          // console.log(video.paused)
          // if(video.paused) {
          //   video.play()
          // }
        } else {
          // video.pause()
          // console.log(video.currentTime, typeof video.fastSeek, typeof video.fastSeek ==  'function')
          if((new Date().getTime() - this.playingTime)>5000) {//5秒
            this.lyricTxt = '字幕精灵'
          }
        }
      })
    },
  },
}
</script>
<style type='text/css'>
#app{
  display: flex;
  justify-content: center;
}
/*#hidden{*/
/*  position:absolute;*/
/*  z-index:-10000;*/
/*  width:0;*/
/*  height:0;*/
/*  display:inline;*/
/*}*/
#player{
  width:100%;
  height:100%;
  color: yellow;
  font-size: 3em;
  line-height: 3em;
  background-color: rgba(0, 0, 0, 0) !important;
}
</style>