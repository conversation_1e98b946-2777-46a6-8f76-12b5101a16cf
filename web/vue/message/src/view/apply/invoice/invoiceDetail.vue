<template>
  <div id="openInvoice">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="sendContainer">
          <div v-if="info.state == 4">
            <div><span class="red">！重要提示</span></div>
            <p> 下列发票暂缓开具，<span class="red">请择机提醒领导</span>，以免漏开。</p>
            <p style="height: 10px;"></p>
          </div>
          <span v-if="info.teminate_state==1" class="end1">终止</span>
          <div class="tickCus">
            <p><span>{{info.name}}</span> <span class="blueBtn tiRight" @click="getCusInvice">客户的开票资料</span></p>
            <div class="litFt">本次申请开票{{info.invoice_amount | numberFilter(2)}}元，需发票 {{info.invoice_count || '--'}}张</div>
          </div>
          <div class="yard gapTop" v-if="info.create_name"><span>申请人：{{info.create_name}}</span><span></span><span>{{info.create_date | formatDate}}</span></div>
          <div class="yard" v-if="info.update_name"><span>审批人：{{info.update_name}}</span><span></span><span>{{info.update_date | formatDate}}</span></div>
          <div class="yard" v-if="info.state==5 || info.state == 7"><span>开票人：{{info.auditor_name}}</span><span>{{info.audit_date | formatDate}}</span></div>
          <div v-if="info.teminate_state==1" class="hr">
            <span>终止操作人：{{info.teminater_name}}</span><span class="wid"></span><span>{{info.teminate_time | formatDate}}</span><br>
            <span>终止原因：</span><span>{{info.teminate_reason}}</span>
          </div>
        </div>
      </div>
      <p style="height: 15px"></p>
      <div class="ticketList">
        <div class="ticketItem" v-for="(item, index) in list" v-bind:key="index">
          <div class="line"></div>
          <div >
            需求{{ index+1 }} {{ filterInvoiceType(item.invoice_category) }}
            <span class="blueBtn tiRight" @click="toInfo(item.id, applicationId, item.fid)">查看</span>
            <br>发票内货物共{{item.line}}行，金额{{item.amount | numberFilter(2)}}元
            <span v-if="info.state == 3 || info.state == 5 || info.state == 7"><br>发票号码：{{item.invoice_no}}，开票日期：{{item.operate_date | formatDate('yyyy/MM/dd')}}</span>
          </div>
        </div>
        <div class="ticketItem">
          <div class="line"></div>
        </div>


      </div>
      <p style="height: 20px;"></p>
      <div class="centerBtn" v-if="info.teminate_state!=1 && info.state != 7 && info.canEnd" >
        <input type="submit" class="ui-btn ui-btn_info" value="终止本次开票申请" @click="endApplyBtn()">
      </div>
    </div>
    <el-dialog title="终止本次开票申请" :rules="rules" :visible.sync="dialogVisible" width="97%" :before-close="handleClose">
      <p class="em2">请输入本次开票申请的终止原因</p>
      <el-form :label-position="left" label-width="70px">
          <el-input type="textarea" v-model="reason" :size="medium"></el-input>
      </el-form>
      <br>
      <p class="em2">点击确定后，您在被终止的开票申请中还可查看到本申请中的相关信息。</p>
      <p class="em2">确定终止本次开票申请吗？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value="取消" @click="dialogVisible = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定"  @click="endApply()">
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
  .em2{ text-indent: 2em; }
  .red{ color:#ed5565;  }
  .hr{ border-top: 1px solid #333; padding:0.5rem 1rem; word-wrap:break-word }
  .wid{ display: inline-block; width: 30px; }
  .gapTop{padding-top: 10px;  }
  .yard{
    text-align: right;
    span:first-child{
      display: inline-block;
      width: 120px;
    }
  }
  .end1 {
    position: absolute; top:3rem; right:2rem;
    display: inline-block;
    border:3px solid #B87237;
    background:#F79646;
    border-radius:50%;
    color: #fff;
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }
  .centerBtn{
    text-align: center;
    .ui-btn{
      display: inline-block;
      border-radius: 15px;
      width: 70%;
      height: auto;
      line-height:1;
      vertical-align: middle;
      padding:8px 0 ;
    }
  }
  .tickCus{
    .litFt{ font-size: 0.8em}
  }
  .ticketItem{ background: #fff;padding:5px 15px; }
  .blueBtn{ color: #0b94ea; cursor: pointer; padding:5px 10px }
  .tiRight{
    float: right;
  }
  .line{ width: 60%; border-top:1px solid #ccc; margin: 10px auto; }
</style>
<script>
import { formatDate, numberFilter } from '../../../js/common'
export default {
  name: 'applyInvoiceDetail',
  data () {
    return {
      dialogVisible: false,
      loading: true,
      reason: '',
      title: '开票待审批',
      list: [],
      listeners: [],
      rules: {
        reason: [
          { required: true }
        ]
      },
      info: {}
    }
  },
  filters: {
    formatDate,
    numberFilter
  },
  computed: {},
  created () {
    let _this = this
    this.applicationId = this.$route.params.id
    _this.listeners = [
      // 开票待审批
      _this.sphdSocket.subscribe('invoiceApply', _this.subscribeCallBack, null, 'user'),
      // 暂缓开票
      _this.sphdSocket.subscribe('reprieve', _this.subscribeCallBack, null, 'user'),
      // 已批准待开票
      _this.sphdSocket.subscribe('applyInvoice', _this.subscribeCallBack, null, 'user'),
      // 已终止
      _this.sphdSocket.subscribe('endInvoice', _this.subscribeCallBack, null, 'user'),
      // 已开票未登记
      _this.sphdSocket.subscribe('invoiceRegister', _this.subscribeCallBack, null, 'user'),
      // 填完发票号的回调
      _this.sphdSocket.subscribe('invoiceHandle', _this.handleCallback, null, 'user')
    ]
    // 获取申请详情
    this.getDetails()
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    filterInvoiceType:function (value) {
      let type = ''
      switch (value) {
        case '1':
          type = '增值税专用发票'
          break
        case '2':
          type = '增值税普通票/其他普通票'
          break
        case '3':
          type = '增值税普通票/其他普通票'
          break
        default:
          type = ''
      }
      return type
    },
    chargeTitle: function (sta) {
      let str = '开票待审批'
      switch (sta) {
        case 3 :
          str = '已批准待开票'
          break
        case 4 :
          str = '暂缓开票'
          break
        case 5 :
        case 7 :
          str = '已开票未登记'
          break
        default :
      }
      return str
    },
    getDetails: function () {
      let _this = this
      _this.$http.post('../../../sale/getApplicationItemListByApplicationId.do', {
        'applicationId': _this.applicationId
      }, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        _this.list = res.data
        _this.info = res.base[0]
        _this.loading = false
        _this.title = _this.chargeTitle(Number(_this.info.state))
        _this.info['invoice_amount'] = 0
        let canEnd = true
        _this.list.forEach(function (item) {
          _this.info['invoice_amount'] += Number(item['amount'])
          if (item['idi_create_name']) {
            canEnd = false
          }
        })
        _this.info.canEnd = canEnd
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
        this.$router.push({
          path: `/invoiceApply`
        })
      })
    },
    toInfo: function (invoiceId, apId, fid) {
    // :id/:invoiceId/:way/:fid',
      let data = {
        'id': apId,
        'invoiceId':invoiceId,
        'way':this.info.division_way,
        'fid':fid,
      }
      let setIframeStyle = {
        'height': '400px',
        'margin-top': '200px',
        'border': 'none'
      }
      window.parent.floatToPage('../../../sale/ticketDetails', data, setIframeStyle)

      // let _this = this
      // console.log("fffffiiiiddd:" + fid)
      // this.$router.push({
      //   "path": `/applyInvoiceDetailInfo/${apId}/${invoiceId}/${_this.info.division_way}/${fid}`
      // })
    },
    getCusInvice: function () {
      let setIframeStyle = {
        'height': '380px',
        'margin-top': '200px',
        'width': '620px',
        'border': 'none'
      }
      let data = {
        'customerID': this.info.customer_id
      }
      window.parent.floatToPage('../../../sale/toCustomer', data, setIframeStyle)
    },
    // 终止本次开票申请
    endApplyBtn: function () {
      this.dialogVisible = true
    },
    endApply: function () {
      this.loading = true
      let userId = this.sphdSocket.user.userID
      this.sphdSocket.send('changeStateById', {
        'userId': userId,
        'id': this.applicationId,
        'oid': this.sphdSocket.org.id,
        'state': this.info.state,
        'terminateState': 1,
        'terminateReason': this.reason
      })
    },
    subscribeCallBack: function (res) {
      console.log('回调详情：', res)
      let data = JSON.parse(res)
      let state = data['state'] // 4 - 暂缓 ， 3 批准
      console.log(state)
      let opreator = data['updator']
      let curUserID = this.sphdSocket.user.userID
      if (curUserID === opreator) {
        if (state === 3 || state === 4) {
          this.$message({
            type: 'success',
            message: '操作成功！'
          })
          this.$router.push({
            path: `/invoiceApproval`
          })
        } else {
          this.dialogVisible = false
          this.$message({
            type: 'error',
            message: '操作失败！'
          })
        }
      } else {
        if (state === 3) {
          this.$message({
            type: 'success',
            message: '该操作已被批准！'
          })
        } else if (state === 4) {
          this.$message({
            type: 'error',
            message: '该操作已被暂缓开票！'
          })
        } else {
          this.dialogVisible = false
        }
        this.getDetails()
      }
    },
    // 开票登记回调
    handleCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('开票登记回调', res)
      if (res['state'] === '5') { // 前面确定开票
        _this.$message({
          type: 'success',
          message: '财务人员开票登记了此申请！'
        })
      }
      _this.getDetails()
    }
  }
}
</script>
