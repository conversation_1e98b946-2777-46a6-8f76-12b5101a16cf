package cn.sphd.miners.modules.recruit.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.entity.PersonalEducation;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelOccupation;
import cn.sphd.miners.modules.personal.entity.PersonnelFolks;
import cn.sphd.miners.modules.personal.entity.PersonnelInterview;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelInterviewService;
import cn.sphd.miners.modules.recruit.entity.*;
import cn.sphd.miners.modules.recruit.service.RdNoticeService;
import cn.sphd.miners.modules.recruit.service.RdPostService;
import cn.sphd.miners.modules.recruit.service.RdResumeService;
import cn.sphd.miners.modules.system.entity.Offer;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OfferService;
import cn.sphd.miners.modules.system.service.OrgService;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

/**
 * Created by Administrator on 2023/5/11
 * 新版岗位管理
 */
@Controller
@RequestMapping("/resume")
public class ResumeController {

    @Autowired
    RdResumeService rdResumeService;
    @Autowired
    RdPostService rdPostService;
    @Autowired
    OfferService offerService;
    @Autowired
    RdNoticeService rdNoticeService;
    @Autowired
    OrgService orgService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    PersonnelInterviewService personnelInterviewService;


    /**
     * <AUTHOR>
     * @Date 2023/5/11
     * 新版  招聘管理详情
     */
    @ResponseBody
    @RequestMapping("/getResumeInfo.do")
    public JsonResult getResumeInfo(User user, Integer offerId){
        Map<String,Object> map=new HashMap<>();
        Offer offer=offerService.getOfferById(offerId); //面试者详情
        List<PersonalEducation> peList = offerService.getPersonalEducationListByOfferId(offerId);
        List<PersonnelOccupation> poList = offerService.getPersonnelOccupationListByOfferId(offerId);
        List<PersonnelFolks> pfList = offerService.getPersonnelFolksListByOfferId(offerId);
        List<PersonnelInterview> piList = offerService.getPersonnelInterviewByOfferId(offerId);
        offer.setPersonnelInterviews(piList);//面试意见
        offer.setPersonalEducations(peList);//教育经历
        offer.setPersonnelOccupations(poList);//工作经历
        offer.setPersonnelFolkses(pfList);//家庭成员
        RdResume rdResume=rdResumeService.getRdResumeByOfferId(offerId); //面试者简历
        List<RdInterviewNotification> rdInterviewNotificationList=rdResumeService.getRdInterviewNotificationsByResumeId(rdResume.getId()); //面试通知列表
//            List<RdInterviewer> rdInterviewerList=rdResumeService.getRdInterviewersByResumeId(rdResume.getId()); //面试官列表
        map.put("notificationCount",rdInterviewNotificationList.size());
        List<Integer> userIds=approvalProcessService.getUserIdsByBusiness(offerId,16);//已经给此应聘者选择过的面试官id
        map.put("interviewerCount",userIds.size()>0?userIds.size()-1:0);
//        List<PersonnelInterview> personnelInterviewList = offerService.getPersonnelInterviewByOfferId(offerId); //面试意见
        Integer number=personnelInterviewService.getNumberBySuggestion(offerId,"1");// 建议入职 的面试官人数
        map.put("agreeCount",number);
        map.put("offer",offer);
        map.put("rdResume",rdResume);
        map.put("personnelInterviewList",piList);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/11
     * 新版  录入面试通知
     */
    @ResponseBody
    @RequestMapping("/addInterviewNotification.do")
    public JsonResult addInterviewNotification(User user,String noticeTime,Integer noticeMethod,String memo, Long resumeId){
        RdResume rdResume=rdResumeService.getRdResumeById(resumeId); //面试者简历
        RdInterviewNotification rdInterviewNotification=new RdInterviewNotification(user.getOid(), NewDateUtils.dateFromString(noticeTime,"yyyy-MM-dd"),noticeMethod,memo,user.getUserID(),user.getUserName());
        rdInterviewNotification.setRdResume(rdResume);
        rdResumeService.saveRdInterviewNotification(rdInterviewNotification);
        rdResume.setState(2); //已通知
        rdResumeService.updateRdResume(rdResume);
        return new JsonResult(1,1);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/11
     * 新版  查看面试通知记录
     */
    @ResponseBody
    @RequestMapping("/getInterviewNotificationList.do")
    public JsonResult getInterviewNotificationList(User user,Long resumeId){
        List<RdInterviewNotification> rdInterviewNotificationList=rdResumeService.getRdInterviewNotificationsByResumeId(resumeId); //面试通知列表
        return new JsonResult(1,rdInterviewNotificationList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/8/16
     * 新版  查看面试通知详情
     */
    @ResponseBody
    @RequestMapping("/getNotificationInfo.do")
    public JsonResult getNotificationInfo(User user,Long id){
        RdInterviewNotification rdInterviewNotification=rdResumeService.getRdInterviewNotificationById(id); //面试通知详情
        return new JsonResult(1,rdInterviewNotification);
    }

    /**
     * <AUTHOR>
     * @Date 2023/8/16
     * 新版  修改面试通知
     */
    @ResponseBody
    @RequestMapping("/updateNotificationInfo.do")
    public JsonResult updateNotificationInfo(User user,Long id,String noticeTime,Integer noticeMethod,String memo){
        Integer state=0;
        if (id!=null&&StringUtils.isNotEmpty(noticeTime)&&noticeMethod!=null&&StringUtils.isNotEmpty(memo)){
            RdInterviewNotification rdInterviewNotification=rdResumeService.getRdInterviewNotificationById(id); //面试通知详情
            rdInterviewNotification.setNoticeTime(NewDateUtils.dateFromString(noticeTime,"yyyy-MM-dd"));
            rdInterviewNotification.setNoticeMethod(noticeMethod);
            rdInterviewNotification.setMemo(memo);
            rdInterviewNotification.setUpdator(user.getUserID());
            rdInterviewNotification.setUpdateDate(new Date());
            rdInterviewNotification.setUpdateName(user.getUserName());

            List<Map<String,Object>> mapList=new ArrayList<>();
            if (StringUtils.isNotEmpty(rdInterviewNotification.getModifyRecord())) {
                mapList = (List<Map<String, Object>>) JSONObject.parse(rdInterviewNotification.getModifyRecord());
            }
            Map<String,Object> map=new HashMap<>();
            map.put("dateName","修改时间");
            map.put("userName",user.getUserName());
            map.put("dateTime",new Date());
            mapList.add(map);
            rdInterviewNotification.setModifyRecord(JSONObject.toJSONString(mapList));
            rdResumeService.updateRdInterviewNotification(rdInterviewNotification);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/11
     * 新版  查看已选面试官列表   用之前 RecruitController  里  面试官choiceInterviewers.do 系列接口， 新表不用
     */
    @ResponseBody
    @RequestMapping("/getResumeInterviewerList.do")
    public JsonResult getResumeInterviewerList(User user,Long resumeId){
        List<RdInterviewer> rdInterviewerList=rdResumeService.getRdInterviewersByResumeId(resumeId); //面试官列表
        return new JsonResult(1,rdInterviewerList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/12
     * 新版  招聘启示管理列表
     */
    @ResponseBody
    @RequestMapping("/getRdNoticeList.do")
    public JsonResult getRdNoticeList(User user){
        List<RdNotice> rdNoticeList=rdNoticeService.getRdNoticeListByOid(user.getOid(),true,null,null);
        List<RdNotice> rdNotices=new ArrayList<>();
        for (RdNotice rdNotice:rdNoticeList){
            RdNotice sum=rdResumeService.getSumRdResumeByNoticeId(rdNotice.getId());
            rdNotice.setRecievedCount(sum.getRecievedCount());
            rdNotice.setNoticedCount(sum.getNoticedCount());
            rdNotice.setInterviewedCount(sum.getInterviewedCount());
            rdNotice.setEnteredCount(sum.getEnteredCount());
            rdNotice.setRdNoticePostList(new ArrayList<>(rdNotice.getRdNoticePostHashSet()));
            if (rdNotice.getEndTime().getTime()<=new Date().getTime()){
                rdNotice.setEnabled(false);
                rdNotice.setEnabledTime(new Date());
                rdNotice.setUpdateDate(new Date());
                rdNoticeService.updateRdNotice(rdNotice,user);
            }else {
                rdNotices.add(rdNotice);
            }
        }
        return new JsonResult(1,rdNotices);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/13
     * 分享到微信的效果， 招聘启示详情
     */
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/getRdnoticeInfo.do")
    public JsonResult getRdnoticeInfo(User user,Long id){
        RdNotice rdNotice=rdNoticeService.getRdNoticeById(id);
        if (rdNotice.getEnabled()) {
            List<RdNoticeItem> rdNoticeItemList = rdNoticeService.getRdNoticeItemsByNoticeId(rdNotice.getId(), 1);
            List<RdNoticeAdditional> rdNoticeAdditionalList = rdNoticeService.getRdNoticeAdditionalsByNoticeId(rdNotice.getId());
            List<RdNoticePost> rdNoticePostList = rdNoticeService.getRdNoticePostsByNoticeId(rdNotice.getId());
            Organization organization = orgService.getByOid(rdNotice.getOrg(), true, false);
            rdNotice.setOrgName(organization.getName());
            Map<String, Object> map = new HashMap<>();
            map.put("rdNotice", rdNotice);
            map.put("rdNoticeItemList", rdNoticeItemList);
            map.put("rdNoticeAdditionalList", rdNoticeAdditionalList);
            map.put("rdNoticePostList", rdNoticePostList);
            return new JsonResult(1,map);
        }
        return new JsonResult(new MyException("-1", "链接已失效"));
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/13
     * 分享到微信的效果， 招聘启示岗位详情
     */
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/getRdNoticePostInfo.do")
    public JsonResult getRdNoticePostInfo(Long id){
        RdNoticePost rdNoticePost=rdNoticeService.getRdNoticePostById(id);
        if (rdNoticePost.getRdNotice().getEnabled()) {
            RdPost rdPost = rdPostService.getRdPostById(rdNoticePost.getPostId());
            List<RdNoticeItem> rdNoticeItemList = rdNoticeService.getRdNoticeItemsByNoticePostId(id);
            Map<String, Object> map = new HashMap<>();
            map.put("rdNoticePost", rdNoticePost); //招聘岗位信息
            map.put("rdPost", rdPost); //岗位信息
            map.put("rdNoticeItemList", rdNoticeItemList); //岗位信息
            return new JsonResult(1, map);
        }
        return new JsonResult(new MyException("-1", "链接已失效"));
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/13
     * 新版  失效停用的 招聘启示列表
     */
    @ResponseBody
    @RequestMapping("/getDeaNoticeList.do")
    public JsonResult getDeaNoticeList(User user){
        Date beginDate=NewDateUtils.changeMonth(new Date(),0);
        Date endDate=NewDateUtils.getLastTimeOfMonth(beginDate);
        List<RdNotice> rdNoticeList=rdNoticeService.getRdNoticeListByOid(user.getOid(),false,beginDate,endDate);
        for (RdNotice rdNotice:rdNoticeList){
            rdNotice.setRdNoticePostList(new ArrayList<>(rdNotice.getRdNoticePostHashSet()));
        }
        return new JsonResult(1,rdNoticeList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/14
     * 新版  失效停用的 按年月条件筛选 招聘启示列表
     * yearMonth  2023-05 格式
     */
    @ResponseBody
    @RequestMapping("/getDeaNoticesByYearMonth.do")
    public JsonResult getDeaNoticesByYearMonth(User user,String yearMonth){
        Date beginDate=NewDateUtils.dateFromString(yearMonth+"-01","yyyy-MM-dd");  //某月初
        Date endDate=NewDateUtils.getLastTimeOfMonth(beginDate); //某月末
        List<RdNotice> rdNoticeList=rdNoticeService.getRdNoticeListByOid(user.getOid(),false,beginDate,endDate);
        for (RdNotice rdNotice:rdNoticeList){
            rdNotice.setRdNoticePostList(new ArrayList<>(rdNotice.getRdNoticePostHashSet()));
        }
        return new JsonResult(1,rdNoticeList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/14
     * 新版   招聘启示信息  截至日期履历
     */
    @ResponseBody
    @RequestMapping("/getDeaNoticeHistories.do")
    public JsonResult getDeaNoticeHistories(User user,Long id){
        List<RdNoticeHistory> rdNoticeHistories=rdNoticeService.getRdNoticeHistoriesByNoticeId(id,new Integer[]{1,7});
        if (rdNoticeHistories.size()>1) {
            return new JsonResult(1, rdNoticeHistories);
        }else {
            rdNoticeHistories=new ArrayList<>();
            return new JsonResult(1,rdNoticeHistories);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/14
     * 新版   招聘管理  已收简历XX份
     */
    @ResponseBody
    @RequestMapping("/getRdResumeList.do")
    public JsonResult getRdResumeList(User user,Long id){
        List<RdResume> rdResumeList=rdResumeService.getRdResumesByNoticeId(id,null);
        return new JsonResult(1,rdResumeList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/14
     * 新版   招聘管理  通知面试XX份
     */
    @ResponseBody
    @RequestMapping("/getNotificationList.do")
    public JsonResult getNotificationList(User user,Long id){
        List<RdResume> rdResumeList=rdResumeService.getRdResumesByNoticeId(id,2);
        return new JsonResult(1,rdResumeList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/15
     * 新版   招聘管理  已面试XX份
     */
    @ResponseBody
    @RequestMapping("/getInterviewList.do")
    public JsonResult getInterviewList(User user,Long id){
        List<RdResume> rdResumeList=rdResumeService.getRdResumesByNoticeId(id,3);
        return new JsonResult(1,rdResumeList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/15
     * 新版   招聘管理  已入职XX份
     */
    @ResponseBody
    @RequestMapping("/getEntryList.do")
    public JsonResult getEntryList(User user,Long id){
        List<RdResume> rdResumeList=rdResumeService.getRdResumesByNoticeId(id,4);
        return new JsonResult(1,rdResumeList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/16
     * 新版   招聘管理  修改简历提交截止日期接口（产生历史记录）
     */
    @ResponseBody
    @RequestMapping("/updateRdNoticeEndTime.do")
    public JsonResult updateRdNoticeEndTime(User user,Long id,String endTime){
        Integer state=0;
        if (id!=null&& StringUtils.isNotEmpty(endTime)) {
            RdNotice rdNotice = rdNoticeService.getRdNoticeById(id);
            rdNotice.setEndTime(NewDateUtils.dateFromString(endTime, "yyyy-MM-dd"));
            rdNoticeService.updateRdNotice(rdNotice,  user ,7);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/16
     * 新版   招聘管理  已发出去的招聘启事有问题，需立即作废
     */
    @ResponseBody
    @RequestMapping("/rdNoticeCancel.do")
    public JsonResult rdNoticeCancel(User user,Long id){
        Integer state=0;
        if (id!=null) {
            RdNotice rdNotice = rdNoticeService.getRdNoticeById(id);
            rdNotice.setTeminateReason(1); //中止原因:1-有问题,作废;2-正常结束,3-招满
            rdNotice.setEnabled(false);
            rdNotice.setEnabledTime(new Date());
            rdNoticeService.updateRdNotice(rdNotice, user,8);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/16
     * 新版   招聘管理  修改简历提交 简历提交后，多长时间内可修改
     */
    @ResponseBody
    @RequestMapping("/updateRdNoticeIntervals.do")
    public JsonResult updateRdNoticeIntervals(User user,Long id,Integer intervals){
        Integer state=0;
        if (id!=null&& intervals!=null) {
            RdNotice rdNotice = rdNoticeService.getRdNoticeById(id);
            rdNotice.setIntervals(intervals);
            rdNoticeService.updateRdNotice(rdNotice,  user);
            state=1;
        }
        return new JsonResult(1,state);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/16
     * 新版   招聘管理  创建1默认启示项列表接口
     */
    @ResponseBody
    @RequestMapping("/getNoticeItemList.do")
    public JsonResult getNoticeItemList(User user){
        List<RdNoticeItemTmpl> rdNoticeItemTmplList=rdNoticeService.getRdNoticeItemTmplList(1,user.getOid());
        return new JsonResult(1,rdNoticeItemTmplList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/17
     * 新版   招聘管理  创建1默认岗位项列表接口
     */
    @ResponseBody
    @RequestMapping("/getNoticePostItemList.do")
    public JsonResult getNoticePostItemList(User user){
        List<RdNoticeItemTmpl> rdNoticeItemTmplList=rdNoticeService.getRdNoticeItemTmplList(2,user.getOid());
        return new JsonResult(1,rdNoticeItemTmplList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/17
     * 新版   招聘管理  新增招聘启事  操作完毕保存接口
     */
    @ResponseBody
    @RequestMapping("/addRdNotice.do")
    public JsonResult addRdNotice(User user,String json){
        rdNoticeService.addRdNotice(user,json);
        return new JsonResult(1,1);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/19
     * 新版   招聘管理  扫二维码 查看简历提交记录
     * hashKey 前端生成的游客标识，不清缓存一直有效，用于再次扫码 做历史数据查询
     */
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/getResumeSubmitHistories.do")
    public JsonResult getResumeSubmitHistories(Long noticeId,Long noticePostId,String hashKey){
        RdResume rdResume=rdResumeService.getRdResume(noticeId,noticePostId,hashKey);
        RdNotice rdNotice=rdNoticeService.getRdNoticeById(noticeId);
        Map<String,Object> map=new HashMap<>();
        map.put("rdResume",rdResume);
        map.put("rdNotice",rdNotice);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/19
     * 新版   招聘管理  扫二维码 查看简历提交记录 后  其他简历提交记录
     * hashKey 前端生成的游客标识，不清缓存一直有效，用于再次扫码 做历史数据查询
     */
    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/getResumeOtherHistories.do")
    public JsonResult getResumeOtherHistories(String hashKey,Long rdResumeId){
        RdResume rdResume=rdResumeService.getRdResumeById(rdResumeId);
        List<RdResume> rdResumes=rdResumeService.getRdResumesByHashKey(hashKey);
        rdResumes.remove(rdResume);
        return new JsonResult(1,rdResumes);
    }


    /**
     * <AUTHOR>
     * @Date 2023/6/14
     * 新版   招聘管理  按需求更改
     * 复制产生下一个 时调用的详情
     */
    @ResponseBody
    @RequestMapping("/getNoticeCopyInfo.do")
    public JsonResult getNoticeCopyInfo(Long id){
        List<RdNoticeItem> noticeItemList=rdNoticeService.getRdNoticeItemsByNoticeId(id,1); //1招聘启示项目模板，  2招聘岗位项目模板
        List<RdNoticeItem> noticePostList=rdNoticeService.getRdNoticeItemsByNoticeId(id,2); //1招聘启示项目模板，  2招聘岗位项目模板
        Map<String,Object> map=new HashMap<>();
        map.put("noticeItemList",noticeItemList);
        map.put("noticePostList",noticePostList);
        return new JsonResult(1,map);
    }

}
