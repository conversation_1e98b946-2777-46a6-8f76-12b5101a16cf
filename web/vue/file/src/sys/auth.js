import { isMiniBrowserOrWebview } from '@/utils/browerUtils'
import packageJson from '@/../package.json'
import { Base64 } from 'js-base64'
import axios from 'axios'
import Cookies from 'js-cookie'
import qs from 'qs'
import J<PERSON><PERSON><PERSON><PERSON> from 'json-bigint'
const minTokenLen = 147
const maxTokenLen = 1024
const auth = {
  getToken () {
    let token = localStorage.getItem('token')
    if (this.verifyToken(token)) {
      return token
    } else if ((token = this.getCookie('token')) && this.verifyToken(token)) {
      return token
    } else {
      return null
    }
  },
  saveToken (token) {
    let verify = this.verifyToken(token)
    if (verify === 1) {
      localStorage.setItem('token', token)
      axios.defaults.headers.common['token'] = token
      if (typeof this.http !== 'undefined') {
        this.http.headers.common['token'] = token
      }
      let acc
      if ((acc = this.getAcc()) !== null && acc.id >= 0) {
        this.setCookie('accId', acc.id)
        this.setCookie('mobile', acc.mobile)
      }
      this.getUser(true)
      console.log('token saved')
    } else if (verify === -1) {
      this.cleanToken()
    }
  },
  cleanToken () {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('org')
    this.removeCookie('token')
    this.removeCookie('accId')
    this.removeCookie('mobile')
    this.removeCookie('userID')
    this.removeCookie('oid')
    delete axios.defaults.headers.common['token']
    if (typeof this.http !== 'undefined') {
      delete this.http.headers.common['token']
    }
    console.log('token cleand')
  },
  getGuestToken (force) {
    if(force || !this.isLogged()) {
      let result = this.getUrl(this.webRoot + '/auth/getGuestToken.do')
      if (typeof result === 'string' && result.length > 0) {
        this.saveToken(result)
        return result
      } else {
        return null
      }
    } else {
      return this.getToken()
    }
  },
  getDevToken ({accId, userId, memberId}) {
    let url = this.webRoot + '/auth/getDevToken.do'
    let params = []
    if (typeof accId !== 'undefined') {
      params.push('accId=' + accId)
    }
    if (typeof userId !== 'undefined') {
      params.push('userId=' + userId)
    }
    if (typeof memberId !== 'undefined') {
      params.push('memberId=' + memberId)
    }
    if (params.length > 0) {
      url += '?' + params.join('&')
    }
    let result = this.getUrl(url)
    if (typeof result === 'string' && result.length > 0) {
      this.saveToken(result)
      if (this.isLogInOrg()) {
        setTimeout(function () { auth.getUser() }, 0)
      }
      return result
    } else {
      return null
    }
  },
  refreshCurrentUserOrg () {
    let result = this.getUrl(this.webRoot + '/sys/refreshCurrentUserOrg.do')
    console.log(result)
    if (typeof result === 'string' && result.length > 0) {
      let obj = JSONBig.parse(result)
      let data = obj.data
      let user = data.user
      if (user != null) {
        this.setCookie('userID', JSONBig.parse(user).userID)
        localStorage.setItem('user', user)
      }
      let org = data.org
      if (org != null) {
        this.setCookie('oid', JSONBig.parse(org).id)
        localStorage.setItem('org', org)
      }
    }
  },
  getTokenPayload (token) {
    if (typeof token !== 'string') {
      token = this.getToken()
    }
    if (typeof token === 'string' && token.length > minTokenLen && token.length < maxTokenLen) {
      let tokenArr = token.split('.')
      if (typeof tokenArr === 'object' && tokenArr instanceof Array && tokenArr.length === 3) {
        return Base64.decode(tokenArr[1])
      }
    }
    return null
  },
  getByName (name, token) {
    let tokenStr = this.getTokenPayload(token)
    if (typeof tokenStr === 'string') {
      let tokenObj = JSONBig.parse(tokenStr)
      if (typeof tokenObj === 'object' && typeof tokenObj[name] !== 'undefined') {
        return tokenObj[name]
      }
    }
    return null
  },
  getExp (token) {
    return this.getByName('exp', token)
  },
  getSessionid () {
    return this.getByName('sessionid')
  },
  getTpMemberId () {
    return this.getByName('tpMemberId')
  },
  getAccId () {
    let acc = this.getAcc()
    if (typeof acc === 'object') {
      return acc.id
    } else {
      return null
    }
  },
  getAcc () {
    return this.getByName('acc')
  },
  getUserID () {
    return this.getByName('userID')
  },
  isLogged () {
    if(this.verifyToken(this.getToken()) > 0
        && (this.getAccId() > 0n || this.getTpMemberId() > 0n)) {
      return true
    } else {
      return false
    }
  },
  getLoggedKey () {
    if(this.verifyToken(this.getToken()) > 0
        && (this.getAccId() > 0n || this.getTpMemberId() > 0n)) {
      let result = {}
      console.log('getLogged accId', this.getAccId(), this.getAccId() > 0n)
      if(this.getAccId() > 0n) {
        result.accId = this.getAccId()
      }
      console.log('getLogged tpMemberId', this.getTpMemberId(), this.getTpMemberId() > 0n)
      if(this.getTpMemberId() > 0n) {
        result.tpMemberId = this.getTpMemberId()
      }
      console.log('getLogged userId', this.getUserID(), this.getUserID() > 0)
      if(this.getUserID() > 0) {
        result.userId = this.getUserID()
      }
      console.log('loggedKey key 升序？ result', JSONBig.stringify(result), encodeURIComponent(JSONBig.stringify(result)))
      return encodeURIComponent(JSONBig.stringify(result))
    } else {
      return ''
    }
  },
  getUser (force) {
    let user
    if ((localStorage.getItem('user') != null && this.getUserID() === (user = JSONBig.parse(localStorage.getItem('user'))).userID) && !force) {
      return user
    } else if (this.getUserID() != null) {
      this.refreshCurrentUserOrg()
      if (localStorage.getItem('user') != null) {
        return JSONBig.parse(localStorage.getItem('user'))
      }
    } else {
      localStorage.removeItem('user')
      localStorage.removeItem('org')
    }
    return null
  },
  getOrg () {
    if (localStorage.getItem('org') != null) {
      return JSONBig.parse(localStorage.getItem('org'))
    } else if (this.getByName('userID') != null) {
      this.refreshCurrentUserOrg()
      if (localStorage.getItem('org') != null) {
        return JSONBig.parse(localStorage.getItem('org'))
      }
    }
    return null
  },
  getPackageName () {
    return packageJson.name??''
  },
  getCookie (name) { // 获取cookie，仅在同域名iframe中可用
    return Cookies.get(name)
    // var arr = document.cookie.match(new RegExp('(^| )' + name + '=([^;]*)(;|$)'))
    // if (arr != null) {
    //   return decodeURIComponent(arr[2])
    // } else {
    //   return null
    // }
  },
  setCookie (name, value) { // 设置特定的过期时间和路径，给EFK采集数据用
    Cookies.set(name, value, {expires: 1 / 6, path: '/'})
  },
  removeCookie (name) {
    Cookies.remove(name, {path: '/'})
  },
  getUrl (url) {
    console.log('getUrl', url)
    let xmlhttp = new XMLHttpRequest()
    xmlhttp.open('GET', url, false)
    xmlhttp.setRequestHeader('Wndrss-Prj', this.getPackageName())
    let token
    if ((token = this.getToken()) !== null) {
      xmlhttp.setRequestHeader('token', token)
    }
    xmlhttp.send(null)
    return xmlhttp.responseText
  },
  getDomain (url) {
    let index
    if (typeof url !== 'string' || url.length <= 0) {
      url = location.href
    }
    if ((index = url.indexOf('//')) >= 0) {
      url = url.substring(index + 2)
    }
    if ((index = url.indexOf('/vue/')) > 0) {
      return url.substring(0, index)
    } else if ((index = url.indexOf('/')) > 0) {
      return url.substring(0, index)
    } else {
      return url
    }
  },
  getPureDomain (url) {
    let domain = this.getDomain(url)
    if (typeof domain === 'string') {
      let index
      if ((index = domain.indexOf(':')) > 0) {
        return domain.substring(0, index)
      }
    }
    return domain
  },
  getPath (url) {
    let index
    if (typeof url !== 'string' || url.length <= 0) {
      url = location.href
    }
    if ((index = url.indexOf('//')) >= 0) {
      url = url.substring(index + 2)
      if ((index = url.indexOf('/')) > 0) {
        url = url.substring(index)
      } else {
        return '/'
      }
    }
    if (url.length <= 1) {
      return '/'
    } else if ((index = url.indexOf('/vue/')) > 0) {
      return url.substring(index)
    } else if ((index = url.indexOf('/')) > 0) {
      return url.substring(index + 1)
    } else {
      return url
    }
  },
  verifyToken (token) {
    typeof token === 'string' && console.log('verifyToken', typeof token === 'string' && token.length > minTokenLen && token.length < maxTokenLen && token.split('.').length === 3, this.getExp(token) * 1000, Date.now().valueOf(), token.split('.').length)
    if (typeof token === 'string' && token.length > minTokenLen && token.length < maxTokenLen && token.split('.').length === 3) {
      if (this.getExp(token) * 1000 > Date.now().valueOf()) {
        return 1
      } else {
        return -1
      }
    } else {
      return 0
    }
  },
  isDevDomain (domain) {
    const dev_domains = ['wonderss.hongbeibz.com', 'dvm01.btransmission.com', 'dvm02.btransmission.com', 'dvm03.btransmission.com', 'dvm04.btransmission.com', 'dvm05.btransmission.com']
    let patternIpPort = /^(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)(:([1-9][0-9]{0,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553)){0,1}$/
    return dev_domains.indexOf(domain) > -1 || domain.toLowerCase().includes('frp.btransmission.com') || domain.toLowerCase().includes('localhost') || patternIpPort.test(domain)
  },
  isDevEnv () {
    let params = qs.parse(location.search, {ignoreQueryPrefix: true})
    if(params.code && params.tgCode && params.appId) {
      return false
    }
    let webRoot = process.env.VUE_APP_WEB_ROOT
    if(process.env.NODE_ENV === 'development' && typeof webRoot === 'string' && webRoot.length > 0 && this.isDevDomain(this.getDomain(webRoot)) && !isMiniBrowserOrWebview()) {
      return true
    } else {
      return false
    }
  },
  init ({Vue, isGuest}) {
    // if(this.isInitialized !== true) {
      console.log('auth.init')
      if (typeof Vue !== 'undefined') {
        console.log('axios!==Vue.$http', axios !== Vue.$http, axios, Vue.$http, axios !== Vue.http, Vue.http)
        if (typeof Vue.$http !== 'undefined' && axios !== Vue.$http) {
          this.http = Vue.$http
        } else if (typeof Vue.http !== 'undefined' && axios !== Vue.http) {
          this.http = Vue.http
        }
      }
      // console.log('process.env', process.env)
      let webRoot = process.env.VUE_APP_WEB_ROOT
      if (typeof webRoot === 'string' && webRoot.length > 0) {
        this.webRoot = webRoot
        console.log('location.search.length', location.search, location.search.length)
        if (this.isDevEnv() && !this.isLogged()) {
          let accId = process.env.VUE_APP_ACC_ID
          let userId = process.env.VUE_APP_USER_ID
          let memberId = process.env.VUE_APP_MEMBER_ID
          // DEBUG
          this.cleanToken()
          // ENDOF DEBUG
          !this.getToken() && this.getDevToken({accId: accId, userId: userId, memberId: memberId})
        }
      } else if (location.href.indexOf('/vue/') > 0) {
        this.webRoot = location.href.substring(0, location.href.indexOf('/vue/'))
      } else {
        this.webRoot = ''
      }
      !this.isLogged() && isGuest && this.getGuestToken()
      let token = this.getToken()
      console.log('token', this.verifyToken(token), token)

      if (typeof axios !== 'undefined') {
        console.log('axios.interceptors', axios.interceptors.length)
        axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8'
        axios.defaults.headers['Wndrss-Prj'] = this.getPackageName()
        axios.defaults.withCredentials = true
        axios.defaults.timeout = 30000
        axios.defaults.transformRequest = [function (data) {
          return qs.stringify(data, { indices: false })
        }]
        axios.defaults.transformResponse = [function (data) {
          try {
            return JSONBig.parse(data)
          } catch (err) {
            console.log('axios JSONBig.parse error', err)
            return data
          }
        }]
        axios.defaults.baseURL = this.webRoot
        axios.interceptors.request.use(config=>{
          // console.log("请求拦截器")
          config.headers.token = this.getToken()
          return config
        })
        axios.interceptors.response.use(response => {
          // console.log("auth 响应拦截器", response)
          if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.token === 'string' && response.headers.token.length > 0) {
            this.saveToken(response.headers.token)
          }
          return response
        }, error => {
          console.log('error', error)
          let response = error?.response ?? error?.message
          console.log('response', response)
          if (response?.status === 500 && response?.data.indexOf('IllegalAccessException: 未登陆或者登陆状态已过期！') >= 0) { // token过期
            console.log('interceptors 500')
            this.cleanToken()
          }
          return Promise.reject(error)
        })
      }
      if (typeof this.http !== 'undefined') {
        this.http.interceptors.push((request, next) => {
          request.credentials = true
          next((response) => {
            if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.get('token') === 'string' && response.headers.get('token').length > 0) {
              this.saveToken(response.headers.get('token'))
            }
            return response
          })
        })
      }
      // console.log('auth.init OK')
      // this.isInitialized = true
    }
  // }
}
export default auth