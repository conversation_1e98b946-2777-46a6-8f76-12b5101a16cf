package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.modules.generalAffairs.entity.ApprovalInstance;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ApprovalDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/10 11:55
 * @Version 1.0
 */
public class ApprovalDto {
    private List<ApprovalInstance> approvalInstanceList;
    private Integer count;
    private Date startTime;
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<ApprovalInstance> getApprovalInstanceList() {
        return approvalInstanceList;
    }

    public void setApprovalInstanceList(List<ApprovalInstance> approvalInstanceList) {
        this.approvalInstanceList = approvalInstanceList;
    }
}
