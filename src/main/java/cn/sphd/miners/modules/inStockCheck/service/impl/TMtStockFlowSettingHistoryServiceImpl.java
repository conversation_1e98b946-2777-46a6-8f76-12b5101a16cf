package cn.sphd.miners.modules.inStockCheck.service.impl;

import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting;
import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory;
import cn.sphd.miners.modules.inStockCheck.mapper.TMtStockFlowSettingHistoryMapper;
import cn.sphd.miners.modules.inStockCheck.service.TMtStockFlowSettingHistoryService;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class TMtStockFlowSettingHistoryServiceImpl implements TMtStockFlowSettingHistoryService {

    @Autowired
    private TMtStockFlowSettingHistoryMapper tMtStockFlowSettingHistoryMapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return tMtStockFlowSettingHistoryMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(TMtStockFlowSettingHistory record) {
        return tMtStockFlowSettingHistoryMapper.insert(record);
    }

    @Override
    public int insertSelective(TMtStockFlowSettingHistory record) {
        return tMtStockFlowSettingHistoryMapper.insertSelective(record);
    }

    @Override
    public TMtStockFlowSettingHistory selectByPrimaryKey(Integer id) {
        return tMtStockFlowSettingHistoryMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(TMtStockFlowSettingHistory record) {
        return tMtStockFlowSettingHistoryMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(TMtStockFlowSettingHistory record) {
        return tMtStockFlowSettingHistoryMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<TMtStockFlowSettingHistory> getHistoriesByFlowId(Integer id) {
        //由审批id查询审批设置id
        TMtStockFlowSetting tMtStockFlowSetting=tMtStockFlowSettingHistoryMapper.selectByValue(id);
        if (tMtStockFlowSetting != null) {
            return tMtStockFlowSettingHistoryMapper.selectBySettingId(tMtStockFlowSetting.getId());
        }

       return null;
    }

}
