<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.ProfitStatementMapper">

    <select id="getSingle" resultType="profit" parameterType="profit" >
        select
        id, org, period, type, begin_date, end_date, cell_code, accumulative, amount, memo,
        creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from t_accountant_profit_statement
        where id = #{id}
    </select>

    <select id="listPage" resultType="profit" parameterType="hashmap" >
        select
        id, org, period, type, begin_date, end_date, cell_code, accumulative, amount, memo,
        creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from t_accountant_profit_statement
    </select>

    <select id="listByAllProfitStatement" resultType="profit">
        select
        id, org, period, type, begin_date, end_date, cell_code AS cellCode, accumulative, amount, memo,
        creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from t_accountant_profit_statement
        WHERE
        org = #{org} AND period = #{period} AND begin_date = #{beginDate} AND end_date = #{endDate}
    </select>
    <select id="getProfitStatem" resultType="profit">
        select
        id, org, period, type, begin_date, end_date, cell_code AS cellCode, accumulative, amount, memo,
        creator, create_name, create_date, updator, update_name, update_date, approve_Item,
        approve_status, approve_level, auditor, auditor_name, audit_date, operation, apply_memo,
        approve_memo, message_id,state
        from t_accountant_profit_statement
        WHERE
        org = #{org} AND period = #{period} AND cell_code = #{cellCode}
    </select>

    <insert id="insert" parameterType="profit" useGeneratedKeys="true" keyProperty="id" >
        insert into t_accountant_profit_statement (org, period,
        type, begin_date, end_date, cell_code,
        accumulative, amount, memo,
        creator, create_name, create_date,
        updator, update_name, update_date,
        approve_Item, approve_status, approve_level,
        auditor, auditor_name, audit_date,
        operation, apply_memo, approve_memo,
        message_id,state)
        values (#{org}, #{period},
        #{type}, #{beginDate}, #{endDate}, #{cellCode},
        #{accumulative}, #{amount}, #{memo},
        #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate},
        #{approveItem}, #{approveStatus}, #{approveLevel},
        #{auditor}, #{auditorName}, #{auditDate},
        #{operation}, #{applyMemo}, #{approveMemo},
        #{messageId},#{state})
    </insert>

    <update id="update" parameterType="profit" >
        update t_accountant_profit_statement
        <set >
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="period != null" >
                period = #{period},
            </if>
            <if test="type != null" >
                type = #{type},
            </if>
            <if test="beginDate != null" >
                begin_date = #{beginDate},
            </if>
            <if test="endDate != null" >
                end_date = #{endDate},
            </if>
            <if test="cellCode != null" >
                cell_code = #{cellCode},
            </if>
            <if test="accumulative != null" >
                accumulative = #{accumulative},
            </if>
            <if test="amount != null" >
                amount = #{amount},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="state != null" >
                state = #{state},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="profit" >
        delete from t_accountant_profit_statement
        where id = #{id}
    </delete>
    <delete id="delProfitStatem">
        delete from t_accountant_profit_statement
        where org = #{org} AND period = #{period}
    </delete>
</mapper>