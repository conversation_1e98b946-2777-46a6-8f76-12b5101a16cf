package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillAttachmentDao;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillAttachment;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/5/13.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceReimburseBillAttachmentServiceImpl implements FinanceReimburseBillAttachmentService {

    @Autowired
    FinanceReimburseBillAttachmentDao financeReimburseBillAttachmentDao;

    /**
     *
     * @param billId
     * @param type 1-查询报销详情的图片(某一行综合的所有图片)  2-查看某张票具体的图片
     * @return
     */
    @Override
    public List<FinanceReimburseBillAttachment> getFinanceReimburseBillAttachmentListByBillId(Integer billId,Integer type) {
        List<FinanceReimburseBillAttachment> financeReimburseBillItemList = new ArrayList<>();
        if (1==type){
            String hql = " from FinanceReimburseBillAttachment where reimburseBillId in (select id from FinanceReimburseBill where id=:billId or primaryBill=:billId)";
            Map<String, Object> map = new HashMap<>();
            map.put("billId", billId);
            financeReimburseBillItemList = financeReimburseBillAttachmentDao.getListByHQLWithNamedParams(hql, map);
        }else if (2==type) {
            String hql = " from FinanceReimburseBillAttachment where reimburseBillId=:billId";
            Map<String, Object> map = new HashMap<>();
            map.put("billId", billId);
            financeReimburseBillItemList = financeReimburseBillAttachmentDao.getListByHQLWithNamedParams(hql, map);
        }
        return financeReimburseBillItemList;
    }
}
