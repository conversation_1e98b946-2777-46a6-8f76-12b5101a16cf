package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdPackagingItem;
import cn.sphd.miners.modules.commodity.entity.PdPackagingItemHistory;

import java.io.Serializable;

public interface PdPackagingItemHistoryDao extends IBaseDao<PdPackagingItemHistory, Serializable> {

    //添加历史记录
    PdPackagingItemHistory insert(PdPackagingItem pdPackagingItem,Integer structureHistoryId,Integer mtBaseHistoryId);
}
