<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.VoucherHistoryMapper" >

  <select id="getSingle"  parameterType="voucherHistory" resultType="voucherHistory">
    select 
    id, voucher_id AS voucherId, org, voucher, belong_peroid AS belongPeroid, book_date AS bookDate, mode, type, category, sn,
    settlement, check_no AS checkNo, is_settled AS isSettled, is_account AS isAccount, reason, bill_detail AS billDetail, summary, operator,
    operator_name AS operatorName, operator_finance AS operatorFinance, memo, creator, create_name AS createName, create_date AS createDate, updator,
    update_name AS updateName, update_date AS  updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod,bill_date as billDate, detail_id AS detailId, ftype, kind, addtime
    from t_accountant_voucher_history
    where id = #{id}
  </select>

  <select id="listPage"  parameterType="hashmap" resultType="voucherHistory">
    select
    id, voucher_id AS voucherId, org, voucher, belong_peroid AS belongPeroid, book_date AS bookDate, mode, type, category, sn,
    settlement, check_no AS checkNo, is_settled AS isSettled, is_account AS isAccount, reason, bill_detail AS billDetail, summary, operator,
    operator_name AS operatorName, operator_finance AS operatorFinance, memo, creator, create_name AS createName, create_date AS createDate, updator,
    update_name AS updateName, update_date AS  updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod, bill_date as billDate,detail_id AS detailId, ftype, kind, addtime
    from t_accountant_voucher_history
  </select>

  <!-- 从凭证历史表得到凭证 -->
  <select id="getVouchersFormHistory" parameterType="voucherHistory" resultType="voucherHistory">
    SELECT
    voucher_id AS voucherId,
    id,org,
    DATE_FORMAT(create_date,'%Y年%m月%d日') AS createDate,
    summary,
    bill_detail AS  billDetail,
    pricetype,
    purpose,
    bill_quantity AS  billQuantity,
    bill_period AS billPeriod,
    bill_date as billDate,
    belong_peroid AS belongPeroid,
    create_name AS createName,
    memo,
    type,
    source,
    kind,reason,
    DATE_FORMAT(addtime,'%Y年%m月%d日') AS addtime,
    operator_name as operatorName,is_settled AS isSettled
    FROM `t_accountant_voucher_history`
    WHERE org=#{org} AND approve_status=#{approveStatus} order by id desc
  </select>

  <select id="getVoucherHistoryByVoucherID"
          resultType="voucherHistory">

    SELECT id,approve_status AS approveStatus FROM `t_accountant_voucher_history` WHERE voucher_id=#{voucherId} ORDER BY addtime desc limit 1
  </select>

  <select id="getHistorySubject"
          resultType="voucherSubjectHistory">
    SELECT id FROM t_accountant_voucher_subject_history  where direction=#{direction} and voucher_id =#{voucherId}
  </select>
    <select id="getOrgHistoryVouchers"
            resultType="voucherHistory">
      select
    id, voucher_id AS voucherId, org, voucher
    from t_accountant_voucher_history WHERE  org=#{org}
    </select>

    <delete id="delete" parameterType="voucherHistory" >
    delete from t_accountant_voucher_history
    where id = #{id}
  </delete>

  <delete id="deleteHistorySubjectDetail">
    DELETE  FROM `t_accountant_subject_detail_history` WHERE subject_id=#{borrowID} or subject_id=#{loanID}
  </delete>

  <delete id="deleteHistoryVoucherSubject">
    DELETE  FROM `t_accountant_voucher_subject_history` WHERE voucher_id=#{voucherID}
  </delete>

  <delete id="deleteHistoryVoucherByVoucherID">
    DELETE FROM t_accountant_voucher_history WHERE voucher_id=#{voucherID}
  </delete>

  <insert id="insert" parameterType="voucherHistory" useGeneratedKeys="true" keyProperty="id">
    insert into t_accountant_voucher_history ( voucher_id, org,
    voucher, belong_peroid, book_date,
    mode, type, category, sn,
    settlement, check_no, is_settled,
    is_account, reason, bill_detail,
    summary, operator, operator_name,
    operator_finance, memo, creator,
    create_name, create_date, updator,
    update_name, update_date, approve_Item,
    approve_status, approve_level, auditor,
    auditor_name, audit_date, operation,
    apply_memo, approve_memo, message_id,
    source, pricetype, purpose,
    bill_quantity, bill_period,bill_date, detail_id,
    ftype, kind, addtime)
    values ( #{voucherId}, #{org},
    #{voucher}, #{belongPeroid}, #{bookDate},
    #{mode}, #{type}, #{category}, #{sn},
    #{settlement}, #{checkNo}, #{isSettled},
    #{isAccount}, #{reason}, #{billDetail},
    #{summary}, #{operator}, #{operatorName},
    #{operatorFinance}, #{memo}, #{creator},
    #{createName}, #{createDate}, #{updator},
    #{updateName}, #{updateDate}, #{approveItem},
    #{approveStatus}, #{approveLevel}, #{auditor},
    #{auditorName}, #{auditDate}, #{operation},
    #{applyMemo}, #{approveMemo}, #{messageId},
    #{source}, #{pricetype}, #{purpose},
    #{billQuantity}, #{billPeriod}, #{billDate},#{detailId},
    #{ftype}, #{kind}, #{addtime})
  </insert>

  <update id="update" parameterType="voucherHistory" >
    update t_accountant_voucher_history
    <set >
      <if test="voucherId != null" >
        voucher_id = #{voucherId},
      </if>
      <if test="org != null" >
        org = #{org},
      </if>
      <if test="voucher != null" >
        voucher = #{voucher},
      </if>
      <if test="belongPeroid != null" >
        belong_peroid = #{belongPeroid},
      </if>
      <if test="bookDate != null" >
        book_date = #{bookDate},
      </if>
      <if test="mode != null" >
        mode = #{mode},
      </if>
      <if test="type != null" >
        type = #{type},
      </if>
      <if test="category != null" >
        category = #{category},
      </if>
      <if test="sn != null" >
        sn = #{sn},
      </if>
      <if test="settlement != null" >
        settlement = #{settlement},
      </if>
      <if test="checkNo != null" >
        check_no = #{checkNo},
      </if>
      <if test="isSettled != null" >
        is_settled = #{isSettled},
      </if>
      <if test="isAccount != null" >
        is_account = #{isAccount},
      </if>
      <if test="reason != null" >
        reason = #{reason},
      </if>
      <if test="billDetail != null" >
        bill_detail = #{billDetail},
      </if>
      <if test="summary != null" >
        summary = #{summary},
      </if>
      <if test="operator != null" >
        operator = #{operator},
      </if>
      <if test="operatorName != null" >
        operator_name = #{operatorName},
      </if>
      <if test="operatorFinance != null" >
        operator_finance = #{operatorFinance},
      </if>
      <if test="memo != null" >
        memo = #{memo},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
      <if test="source != null" >
        source = #{source},
      </if>
      <if test="pricetype != null" >
        pricetype = #{pricetype},
      </if>
      <if test="purpose != null" >
        purpose = #{purpose},
      </if>
      <if test="billQuantity != null" >
        bill_quantity = #{billQuantity},
      </if>
      <if test="billPeriod != null" >
        bill_period = #{billPeriod},
      </if>
      <if test="detailId != null" >
        detail_id = #{detailId},
      </if>
      <if test="ftype != null" >
        ftype = #{ftype},
      </if>
      <if test="kind != null" >
        kind = #{kind},
      </if>
      <if test="addtime != null" >
        addtime = #{addtime},
      </if>
    </set>
    where id = #{id}
  </update>

  <!-- 更新凭证历史表的审批状态 -->
  <update id="updateVouchersHistoryApproveStatus" parameterType="voucherHistory">
    UPDATE `t_accountant_voucher_history` SET approve_status=#{approveStatus} WHERE voucher_id=#{voucherId}
  </update>

  <update id="updateHistorySettle">
    UPDATE t_accountant_voucher_history SET is_settled=#{isSettled}  where create_date=#{createDate} AND org=#{org}
  </update>

  <update id="updateHistoryByDate">
    UPDATE `t_accountant_voucher_history` SET is_settled=0 where is_settled=1 and create_date=#{createDate} AND org=#{org}
  </update>


</mapper>