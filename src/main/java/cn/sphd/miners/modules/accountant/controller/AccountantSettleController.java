package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.service.AccountantSettleService;
import cn.sphd.miners.modules.accountant.util.Result;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Created by tao on 17-2-8.
 * 结帐controller，结帐之后上月的
 */
@Controller
@RequestMapping("/accountant")
public class AccountantSettleController {

    private static final String SUCCESS = "1";
    private static final String ERROR = "0";

    @Autowired
    AccountantSettleService settleService;

    /*页面上点击结帐按钮时触发*/
    @RequestMapping("/settleAccount.do")
    public void settleAccount(User user, HttpServletResponse response) {
//        User user = (User) session.getAttribute("user");
        int res = settleService.changeVoucherSettleState(user,1);
        WriteJSONUtil.writeObject(res,"res",response);

    }

    /*页面上点击反结帐按钮时触发*/
    @RequestMapping("/cancelSettleAccount.do")
    public void cancelSettleAccount(User user, HttpServletResponse response) {
//        User user = (User) session.getAttribute("user");
//        Integer org = (Integer) session.getAttribute("oid");
        Integer org = user.getOid();
        int res = settleService.canselsSettleAccount(org);
        WriteJSONUtil.writeObject(res,"res",response);
    }


//    @ResponseBody
//    @RequestMapping("/settleTest.do")
//    public Object test(HttpSession session, HttpServletResponse response) {
//        JSONObject result = doScheduled();
//        return new Result(SUCCESS,"",null);
//    }

    /*点击报税按钮后触发的接口*/
    @ResponseBody
    @RequestMapping("/clickTaxBtn.do")
    public Object clickTaxBtn(User user,String taxDate) {
//        User user = (User) session.getAttribute("user");
        int res = settleService.clickTaxBtn(user,taxDate);
        Result result = new Result();
        if (res > 0) {
            result.setCode(SUCCESS);
            result.setMsg("");
            result.setData(res);
        }
        else {
            result.setCode(ERROR);
            result.setMsg("");
            result.setData(res);
        }

        return result;
    }

    /*
    * 把消息变成已处理
    * mid   消息id
    * */
    @RequestMapping("/updateMessage.do")
    @ResponseBody
    public void updateMessage(Integer mid,HttpServletResponse response) throws IOException {

        int res = settleService.updateMessage(mid);
        WriteJSONUtil.writeObject(res,"res",response);
    }

    /*
    * 测试得到科目实时科目余额
    * */
    @RequestMapping("/testSubjectBalance.do")
    @ResponseBody
    public void getSubjectBalanceRealTime(User user,HttpServletResponse response) throws IOException {
//        User user = (User) session.getAttribute("user");
//        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        Integer org = user.getOid();
        com.alibaba.fastjson.JSONObject res = settleService.getSubjectBalanceRealTime(org);
        WriteJSONUtil.writeObject(res.toString(),"res",response);
    }

}
