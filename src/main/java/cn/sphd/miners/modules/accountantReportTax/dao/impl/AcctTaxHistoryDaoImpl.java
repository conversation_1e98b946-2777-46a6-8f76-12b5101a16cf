package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctReportHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxDetailHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTaxHistoryDaoImpl extends BaseDao<AcctTaxHistoryEntity, Serializable> implements AcctTaxHistoryDao {

    @Autowired
    AcctTaxHistoryDao taxHistoryDao;
    @Autowired
    AcctReportHistoryDao reportHistoryDao;
    @Autowired
    AcctTaxPeriodHistoryDao periodHistoryDao;

    @Override
    public List<AcctTaxHistoryEntity> taxUpdateRecord(Integer org, int taxId) {
        String hql = "from AcctTaxHistoryEntity where tax=:tax";
        HashMap<String, Object> param = new HashMap<>();
//        param.put("org", org);
        param.put("tax", taxId);
        List<AcctTaxHistoryEntity> list = taxHistoryDao.getListByHQLWithNamedParams(hql,param);
        for (AcctTaxHistoryEntity taxHistoryEntity : list) {
            String getReportHistoryHql = "select rh from AcctTaxReportHistoryEntity trh,AcctReportHistoryEntity rh where " +
                    "trh.reportHistory=rh.id and trh.taxHistory=:taxHistory";
            HashMap<String, Object> paramRH = new HashMap<>();
            paramRH.put("taxHistory", taxHistoryEntity.getId());
            List<AcctReportHistoryEntity> listRH = reportHistoryDao.getListByHQLWithNamedParams(getReportHistoryHql,paramRH);
            for (AcctReportHistoryEntity reportHistoryEntity : listRH) {
                String periodListHql = "from AcctTaxPeriodHistoryEntity where reportHistory=" + reportHistoryEntity.getId();
                List<AcctTaxPeriodHistoryEntity> periodHistoryEntityList = periodHistoryDao.getListByHQLWithNamedParams(periodListHql,null);
                reportHistoryEntity.setPeriodHistoryList(periodHistoryEntityList);
                taxHistoryEntity.setReportHistoryEntity(reportHistoryEntity);
            }

        }
        return list;
    }

    @Override
    public Integer getTaxHisId(Integer org, Integer taxId) {
        String hql = " from AcctTaxHistoryEntity where tax=:tax and org=:org order by id desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("tax", taxId);
        param.put("org", org);
        Query query = taxHistoryDao.getSession().createQuery(hql);
        taxHistoryDao.setParams(query, param);
        query.setMaxResults(1);
        List<AcctTaxHistoryEntity> list = query.list();
        Integer id = list.get(0).getId();
        return id;
    }

    @Override
    public void deleteByTaxId(Integer org,Integer id) {
        Integer hisId = getTaxHisId(org,id);
        taxHistoryDao.deleteById(hisId);
    }

}
