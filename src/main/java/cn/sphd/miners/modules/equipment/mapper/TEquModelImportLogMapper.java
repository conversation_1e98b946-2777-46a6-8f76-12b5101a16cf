package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquModelImportLog;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@Component
public interface TEquModelImportLogMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquModelImportLog selectTEquModelImportLogById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquModelImportLog 
     * @return 集合
     */
    public List<TEquModelImportLog> selectTEquModelImportLogList(TEquModelImportLog tEquModelImportLog);

    /**
     * 新增
     * 
     * @param tEquModelImportLog 
     * @return 结果
     */
    public int insertTEquModelImportLog(TEquModelImportLog tEquModelImportLog);

    /**
     * 修改
     * 
     * @param tEquModelImportLog 
     * @return 结果
     */
    public int updateTEquModelImportLog(TEquModelImportLog tEquModelImportLog);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquModelImportLogById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquModelImportLogByIds(String[] ids);
}
