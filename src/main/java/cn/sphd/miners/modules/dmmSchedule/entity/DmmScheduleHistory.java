package cn.sphd.miners.modules.dmmSchedule.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2019/8/13.
 * 备忘日程历史表
 */
@Entity
@Table(name = "t_dmm_schedule_history")
public class DmmScheduleHistory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="memo_schedule"  ,nullable=true , unique=false, insertable=false, updatable=false)
    private Integer scheduleId;//日程主表id

    @Column(name="schedule_uid"  , length=36 , nullable=true , unique=false)
    private String scheduleUid;   //日程的唯一标识符。 此值用于标识分布式提醒

    @Column(name="`acc_id`"  , nullable=true , unique=false)
    private Long accId;//账号id

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;   //名称

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="share_state"  ,length = 1 ,nullable=true , unique=false)
    private String shareState="0";//分享状态:0-未分享,1-已分享
    @Column(name="share_source"  ,nullable=true , unique=false)
    private Integer shareSource=0; //来源:0-原生,其它-分享ID

    @Column(name="enabled"  ,length = 1 ,nullable=true , unique=false)
    private Integer enabled=1;   //状态：0-不启用,1-启用 如果未启用计划，则不会运行该计划中的任何作业

    @Column(name="enabled_time"   , nullable=true , unique=false)
    private Date enabledTime;//启停用时间

    @Column(name="is_valid"   ,nullable=true , unique=false)
    private boolean isValid=true;   //逻辑删除标志,false-无效,true-有效

    @Column(name="category"  ,nullable=true , unique=false)
    private Integer category;   //所属类别

    @Column(name="place"  , length=255 , nullable=true , unique=false)
    private String place;   //地点

    @Column(name="description"  , nullable=true , unique=false)
    private String description;   //详细描述

    @Column(name="parent"  ,nullable=true , unique=false)
    private Integer parent;   //父日程ID(复制时记录)

    @Column(name="source"  , length=1 , nullable=true , unique=false)
    private String source;   //来源：1-新增,2-修改原任务而来,3-设置新的提醒时间复制而来

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //'备注'

    @Column(name="is_notify"   ,nullable=true , unique=false)
    private boolean notify;   //是否需要通知,false-不,true-是

    @Column(name="state"  ,nullable=true , unique=false)
    private Integer state;   //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒

    @Column(name="freq_type"  ,nullable=true , unique=false)
    private Integer freqType;   //此日程的频率。1 = 仅一次,4 = 每日,8 = 每周,16 =每月,32 =每年,64 =相对于 = 每月

    @Column(name="freq_interval"  ,nullable=true , unique=false)
    private Integer freqInterval;   //提醒的间隔天数。 值取决于freq_type。 默认值是0，这指示freq_interval未使用

    @Column(name="is_special"   ,nullable=true , unique=false)
    private boolean special=false;   //是否为特殊时间(如月末倒数),true-是,false-否

    @Column(name="special_interval"  ,nullable=true , unique=false)
    private Integer specialInterval;   //倒数第某天(用于定义月末特定日期)

    @Column(name="active_start_date"   , nullable=true , unique=false)
    private Date activeStartDate;//可以开始执行提醒的日期

    @Column(name="active_start_time"   , nullable=true , unique=false)
    private Date activeStartTime;//在时间和日期之间active_start_date和active_end_date该提醒将会开始执行

    @Column(name="active_end_date"   , nullable=true , unique=false)
    private Date activeEndDate;//可以停止执行提醒的日期

    @Column(name="active_end_time"   , nullable=true , unique=false)
    private Date activeEndTime;//在时间和日期之间active_start_date和active_end_date该提醒将停止执行

    @Column(name = "supplement", nullable=true , unique=false)
    private String supplement;//补充说明

    @Column(name="run_number"  ,nullable=true , unique=false)
    private Integer runNumber=0;   //约定提醒运行次数(不含再次提醒)

    @Column(name="notice_number"  ,nullable=true , unique=false)
    private Integer noticeNumber=0;   //提醒运行次数(含再次提醒)

    @Column(name="last_run_time"   , nullable=true , unique=false)
    private Date lastRunTime;//最后提醒时间(每次再次提醒的不修改此值)

    @Column(name="next_run_time"   , nullable=true , unique=false)
    private Date nextRunTime;//下次提醒时间(每次再次提醒的不修改此值)

    @Column(name="version_number"  ,nullable=true , unique=false)
    private Integer versionNumber;   //日程的当前版本号. 例如,如果日程已被修改了10次，version_number为 10

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation; //操作:1-增,2-删,3-改,4-修改图片,5-修改补充资料内容,6-修改补充资料图片',

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录id

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo=1; //版本号,每次修改+1

    private boolean modifiable=true;// 是否可修改

    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="memo_schedule", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private DmmSchedule schedule;//日程表

    //日程附件历史表
//    @JsonIgnore
    @OrderBy("id ASC")
    @OneToMany(targetEntity= DmmScheduleImageHistory.class, fetch= FetchType.LAZY, mappedBy="scheduleHistory", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<DmmScheduleImageHistory> memoScheduleImageHistoryHashSet = new HashSet<DmmScheduleImageHistory>();


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getScheduleUid() {
        return scheduleUid;
    }

    public void setScheduleUid(String scheduleUid) {
        this.scheduleUid = scheduleUid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getParent() {
        return parent;
    }

    public void setParent(Integer parent) {
        this.parent = parent;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public boolean isNotify() {
        return notify;
    }

    public void setNotify(boolean notify) {
        this.notify = notify;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getFreqType() {
        return freqType;
    }

    public void setFreqType(Integer freqType) {
        this.freqType = freqType;
    }

    public Integer getFreqInterval() {
        return freqInterval;
    }

    public void setFreqInterval(Integer freqInterval) {
        this.freqInterval = freqInterval;
    }

    public boolean isSpecial() {
        return special;
    }

    public void setSpecial(boolean special) {
        this.special = special;
    }

    public Integer getSpecialInterval() {
        return specialInterval;
    }

    public void setSpecialInterval(Integer specialInterval) {
        this.specialInterval = specialInterval;
    }

    public Date getActiveStartDate() {
        return activeStartDate;
    }

    public void setActiveStartDate(Date activeStartDate) {
        this.activeStartDate = activeStartDate;
    }

    public Date getActiveStartTime() {
        return activeStartTime;
    }

    public void setActiveStartTime(Date activeStartTime) {
        this.activeStartTime = activeStartTime;
    }

    public Date getActiveEndDate() {
        return activeEndDate;
    }

    public void setActiveEndDate(Date activeEndDate) {
        this.activeEndDate = activeEndDate;
    }

    public Date getActiveEndTime() {
        return activeEndTime;
    }

    public void setActiveEndTime(Date activeEndTime) {
        this.activeEndTime = activeEndTime;
    }

    public String getSupplement() {
        return supplement;
    }

    public void setSupplement(String supplement) {
        this.supplement = supplement;
    }

    public Integer getRunNumber() {
        return runNumber;
    }

    public void setRunNumber(Integer runNumber) {
        this.runNumber = runNumber;
    }

    public Integer getNoticeNumber() {
        return noticeNumber;
    }

    public void setNoticeNumber(Integer noticeNumber) {
        this.noticeNumber = noticeNumber;
    }

    public Date getLastRunTime() {
        return lastRunTime;
    }

    public void setLastRunTime(Date lastRunTime) {
        this.lastRunTime = lastRunTime;
    }

    public Date getNextRunTime() {
        return nextRunTime;
    }

    public void setNextRunTime(Date nextRunTime) {
        this.nextRunTime = nextRunTime;
    }

    public Integer getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public DmmSchedule getSchedule() {
        return schedule;
    }

    public void setSchedule(DmmSchedule schedule) {
        this.schedule = schedule;
    }

    public Set<DmmScheduleImageHistory> getMemoScheduleImageHistoryHashSet() {
        return memoScheduleImageHistoryHashSet;
    }

    public void setMemoScheduleImageHistoryHashSet(Set<DmmScheduleImageHistory> memoScheduleImageHistoryHashSet) {
        this.memoScheduleImageHistoryHashSet = memoScheduleImageHistoryHashSet;
    }

    public boolean isModifiable() {
        return modifiable;
    }

    public void setModifiable(boolean modifiable) {
        this.modifiable = modifiable;
    }

    public String getShareState() {
        return shareState;
    }

    public void setShareState(String shareState) {
        this.shareState = shareState;
    }

    public Integer getShareSource() {
        return shareSource;
    }

    public void setShareSource(Integer shareSource) {
        this.shareSource = shareSource;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }
}
