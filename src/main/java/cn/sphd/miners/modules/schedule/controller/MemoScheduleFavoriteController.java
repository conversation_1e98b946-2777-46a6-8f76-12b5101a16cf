package cn.sphd.miners.modules.schedule.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.schedule.entity.MemoFavorite;
import cn.sphd.miners.modules.schedule.entity.MemoFavoriteSchedule;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.schedule.service.MemoFavoriteService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

@Controller
@RequestMapping("/scheduleFavorite")
public class MemoScheduleFavoriteController {

    @Autowired
    MemoScheduleService memoScheduleService;
    @Autowired
    MemoFavoriteService memoFavoriteService;
    @Autowired
    UserService userService;

    /**
     * <AUTHOR>
     * @Date 2021/1/14 11:06
     * 新建 一级收藏夹  或 子级收藏夹
     */
    @ResponseBody
    @RequestMapping("/addScheduleFavorite.do")
    public JsonResult addScheduleFavorite(User user,Integer parentId, String name){
        Map<String,Object> map=new HashMap<>();
        if (user!=null&&name!=null&&!name.isEmpty()) {
            user = userService.getUserByID(user.getUserID());
            MemoFavorite memoFavorite = new MemoFavorite();
            List<MemoFavorite> memoFavoriteList;
            if (parentId!=null&&parentId!=0){
                MemoFavorite parentFavorite=memoFavoriteService.getMemoFavoriteById(parentId);
                if (parentFavorite.getMemoFavoriteScheduleHashSet().size()>0){
                    map.put("state",3); // 父菜单下有 日程关联，不能新增子级收藏夹
                    return new JsonResult(1,map);
//                    return 3;// 父菜单下有 日程关联，不能新增子级收藏夹
                }
                memoFavoriteList=memoFavoriteService.getFavoritesByPid(parentId);
                memoFavorite.setLevel(parentFavorite.getLevel()+1);
                memoFavorite.setPath(parentFavorite.getPath()+parentId);
                memoFavorite.setFavorite(parentFavorite);
            }else {
                memoFavorite.setLevel(1);
                memoFavorite.setPath("/");
                memoFavoriteList=memoFavoriteService.getFirstFavorites(user.getUserID());
            }

            for (MemoFavorite m:memoFavoriteList){
                if (m.getName().equals(name)){
                    map.put("state",2); // 同层文件夹不能重名
                    return new JsonResult(1,map);
//                    return 2; // 同层文件夹不能重名
                }
            }

            memoFavorite.setUserId(user.getUserID());
            memoFavorite.setName(name);
            memoFavorite.setCreator(user.getUserID());
            memoFavorite.setCreateName(user.getUserName());
            memoFavorite.setCreateTime(new Date());
            memoFavorite.setVersionNo(0);
            memoFavorite.setScheduleNum(0);
            memoFavoriteService.saveMemoFavorite(memoFavorite);
//            return 1;
            map.put("state",1);
            map.put("memoFavorite",memoFavorite);
            return new JsonResult(1,map);
        }
        map.put("state",0);
        return new JsonResult(1,map);
//        return 0;
    }

    /**
     * <AUTHOR>
     * @Date 2021/1/14 17:06
     * 修改收藏夹名称
     */
    @ResponseBody
    @RequestMapping("/updateScheduleFavorite.do")
    public Integer updateScheduleFavorite(User user,Integer id, String name){
//        Map<String,Object> map=new HashMap<>();
        if (user!=null&&name!=null&&!name.isEmpty()&&id!=null) {
            MemoFavorite memoFavorite=memoFavoriteService.getMemoFavoriteById(id);
            List<MemoFavorite> memoFavoriteList;
            if (memoFavorite.getParentId()!=null){
                memoFavoriteList=memoFavoriteService.getFavoritesByPid(memoFavorite.getParentId());
            }else {
                memoFavoriteList=memoFavoriteService.getFirstFavorites(user.getUserID());
            }
            for (MemoFavorite m:memoFavoriteList){
                if (m.getName().equals(name)&&!m.getId().equals(id)){
//                    map.put("state",2); // 同层文件夹不能重名
//                    return new JsonResult(1,new JsonResult());
                    return 2;// 同层文件夹不能重名
                }
            }
            memoFavorite.setName(name);
            memoFavorite.setUpdator(user.getUserID());
            memoFavorite.setUpdateName(user.getUserName());
            memoFavorite.setUpdateTime(new Date());
            memoFavorite.setVersionNo(memoFavorite.getVersionNo()+1);
            memoFavoriteService.updateMemoFavorite(memoFavorite);
            return 1;//成功
        }
        return 0;
    }


    /**
     * <AUTHOR>
     * @Date 2021/1/15 9:40
     * 删除收藏夹（有备忘不能删除，有子文件夹可以删）
     */
    @ResponseBody
    @RequestMapping("/deleteScheduleFavorite.do")
    public Integer deleteScheduleFavorite(Integer id){
        if (id!=null) {
            MemoFavorite memoFavorite = memoFavoriteService.getMemoFavoriteById(id);
            List<Integer> favoriteIds=memoFavoriteService.getFavoriteIdsByPath("/"+id+"/"); // 子收藏夹 ids
            favoriteIds.add(id);//本收藏夹
            Integer counts= memoFavoriteService.getCountsByFavoriteIds(favoriteIds);//关联的 备忘数量
            if (counts>0){
                return 2; //收藏夹下 有日程 不能删除
            }
            memoFavoriteService.deleteMemoFavorite(memoFavorite);
            return 1;// 成功
        }
        return 0;

    }

    /**
     * <AUTHOR>
     * @Date 2021/1/15 11:57
     * 收藏功能获取收藏夹列表接口
     */
    @ResponseBody
    @RequestMapping("/getFirstFavorites.do")
    public JsonResult getFirstFavorites(User user){
        List<MemoFavorite> memoFavoriteList = new ArrayList<>();
        if (user!=null) {
            memoFavoriteList = memoFavoriteService.getFirstFavorites(user.getUserID());
            for (MemoFavorite memoFavorite:memoFavoriteList){
                Integer sum= scheduleSum(memoFavorite.getMemoFavoriteHashSet(),memoFavorite.getMemoFavoriteScheduleHashSet().size());
                memoFavorite.setScheduleNum(sum);
            }

        }
        return new JsonResult(1,memoFavoriteList);
    }

    private Integer scheduleSum(Set<MemoFavorite> memoFavoriteList,Integer sum){
//        Integer a=0;
        for (MemoFavorite memoFavorite:memoFavoriteList){
           if (memoFavorite.getMemoFavoriteHashSet().size()>0){
               sum+=scheduleSum(memoFavorite.getMemoFavoriteHashSet(),0);
//               memoFavorite.setScheduleNum(sum);
           }else {
               memoFavorite.setScheduleNum(memoFavorite.getMemoFavoriteScheduleHashSet().size());
               sum+=memoFavorite.getScheduleNum();
           }
        }
        return sum;
    }

    /**
     * <AUTHOR>
     * @Date 2021/1/15 13:13
     * 点击收藏夹 获取子收藏夹列表
     */
    @ResponseBody
    @RequestMapping("/getSonFavorites.do")
    public JsonResult getSonFavorites(User user,Integer id){
        List<MemoFavorite> memoFavoriteList = new ArrayList<>();
        if (user!=null) {//验证登陆过
            memoFavoriteList = memoFavoriteService.getFavoritesByPid(id);

            for (MemoFavorite memoFavorite:memoFavoriteList){
                Integer sum= scheduleSum(memoFavorite.getMemoFavoriteHashSet(),memoFavorite.getMemoFavoriteScheduleHashSet().size());
                memoFavorite.setScheduleNum(sum);
            }
        }
        return new JsonResult(1,memoFavoriteList);
    }

    /**
     * <AUTHOR>
     * @Date 2021/1/18 8:52
     * 点击收藏夹 获取备忘列表
     */
    @ResponseBody
    @RequestMapping("/getMemoFavoriteSchedules.do")
    public JsonResult getMemoFavoriteSchedules(User user,Integer id){
        List<MemoSchedule> memoFavoriteList = new ArrayList<>();
        if (user!=null&&id!=null) {//验证登陆过
            memoFavoriteList = memoFavoriteService.getMemoSchedulesByFavoriteId(user.getUserID(),id);
        }
        return new JsonResult(1,memoFavoriteList);
    }

    /**
     * <AUTHOR>
     * @Date 2021/1/18 9:10
     * 转移至其他收藏夹
     */
    @ResponseBody
    @RequestMapping("/moveToOtherFavorite.do")
    public Integer moveToOtherFavorite(User user,Integer oldFavoriteId,Integer newFavoriteId,Integer scheduleId){
        if (user!=null&&oldFavoriteId!=null&&newFavoriteId!=null&&scheduleId!=null) {//验证登陆过
            MemoFavoriteSchedule memoFavoriteSchedule = memoFavoriteService.getMemoFavoriteScheduleByScheduleId(user.getUserID(),oldFavoriteId,scheduleId);
            if (memoFavoriteSchedule==null){
                return 2;// 该日程在本收藏夹中不存在 无法移动
            }
            MemoFavorite newMemoFavorite=memoFavoriteService.getMemoFavoriteById(newFavoriteId);
            if (newMemoFavorite==null){
                return 3;// 新收藏夹并不存在，无法移动
            }

            if (newMemoFavorite.getMemoFavoriteHashSet().size()>0){
                return 4;// 新收藏夹下有子收藏夹，无法复制
            }

            memoFavoriteSchedule.setFavorite(newMemoFavorite);
            memoFavoriteSchedule.setUpdator(user.getUserID());
            memoFavoriteSchedule.setUpdateTime(new Date());
            memoFavoriteSchedule.setUpdateName(user.getUserName());
            memoFavoriteService.updateMemoFavoriteSchedule(memoFavoriteSchedule);
            return 1;// 移动成功
        }
        return 0;//失败 缺少传值
    }

    /**
     * <AUTHOR>
     * @Date 2021/1/18 15:20
     * 收藏至其他收藏夹
     */
    @ResponseBody
    @RequestMapping("/collectToOtherFavorite.do")
    public Integer collectToOtherFavorite(User user,Integer newFavoriteId,Integer scheduleId) {
        if (user != null && newFavoriteId != null && scheduleId != null) {//验证登陆过

            MemoFavoriteSchedule memoFavoriteSchedule = memoFavoriteService.getMemoFavoriteScheduleByScheduleId(user.getUserID(), newFavoriteId, scheduleId);
            if (memoFavoriteSchedule != null) {
                return 2; // 该收藏夹中已存在，无法再次收藏
            }
            user = userService.getUserByID(user.getUserID());
            MemoFavorite memoFavorite = memoFavoriteService.getMemoFavoriteById(newFavoriteId);
            if (memoFavorite==null){
                return 3;// 新收藏夹并不存在，无法复制
            }

            if (memoFavorite.getMemoFavoriteHashSet().size()>0){
                return 4;// 新收藏夹下有子收藏夹，无法复制
            }

            MemoSchedule memoSchedule = memoScheduleService.getMemoScheduleById(scheduleId);
            memoFavoriteSchedule = new MemoFavoriteSchedule();
            memoFavoriteSchedule.setCreateTime(new Date());
            memoFavoriteSchedule.setCreateName(user.getUserName());
            memoFavoriteSchedule.setCreator(user.getUserID());
            memoFavoriteSchedule.setFavorite(memoFavorite);
            memoFavoriteSchedule.setSchedule(memoSchedule);
            memoFavoriteSchedule.setTitle(memoSchedule.getTitle());
            memoFavoriteService.saveMemoFavoriteSchedule(memoFavoriteSchedule);
            return 1;//复制成功
        }
        return 0;//失败 缺少传值
    }

    /**
     * <AUTHOR>
     * @Date 2021/1/18 15:20
     * 仅从本收藏夹中移除（删掉本条关联）
     */
    @ResponseBody
    @RequestMapping("/deleteMemoFavoriteSchedule.do")
    public Integer deleteMemoFavoriteSchedule(User user,Integer favoriteId,Integer scheduleId){
        if (user != null && favoriteId != null && scheduleId != null) {//验证登陆过
            MemoFavoriteSchedule memoFavoriteSchedule = memoFavoriteService.getMemoFavoriteScheduleByScheduleId(user.getUserID(), favoriteId, scheduleId);
            if(memoFavoriteSchedule!=null){
                memoFavoriteService.deleteMemoFavoriteSchedule(memoFavoriteSchedule);
                return 1;
            }
            return 2;// 要移除的不存在
        }
        return 0;//失败 缺少传值
    }

    /**
     * <AUTHOR>
     * @Date 2021/1/20 10:03
     * 从全部收藏夹中移除（删掉全部关联）
     */
    @ResponseBody
    @RequestMapping("/deleteAllFavoriteSchedule.do")
    public Integer deleteAllFavoriteSchedule(User user,Integer scheduleId){
        if (user != null && scheduleId != null) {//验证登陆过
            List<MemoFavoriteSchedule> memoFavoriteScheduleList=memoFavoriteService.getMemoFavoriteSchedulesByScheduleId(scheduleId);
            if (memoFavoriteScheduleList.size()>0) {
                memoFavoriteService.deleteAllMemoFavoriteSchedule(memoFavoriteScheduleList);
            }
            return 1;
        }
        return 0;//失败 缺少传值
    }


}
