<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sphd.miners.modules.accountant.mapper.SubjectSelectMapper">

    <update id="setRelation">
        UPDATE `t_accountant_setting` SET value_=#{value_} WHERE org=#{org} AND key_='kmgl';
    </update>

    <insert id="addInitializationSetting">
        INSERT INTO `t_accountant_setting` (org,key_,value_) SELECT #{org},key_,value_ FROM t_accountant_setting_tmpl;
    </insert>

    <select id="getRelation" resultType="link">
        SELECT value_ FROM `t_accountant_setting` WHERE org=#{org} and key_='kmgl';
    </select>

    <select id="checkOrgSettingData" resultType="link">
        select id from  `t_accountant_setting` where org=#{org} limit 1
    </select>

</mapper>