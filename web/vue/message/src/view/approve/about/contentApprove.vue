<template>
  <div id="contentApprove">
    <TY_NavTop title="内容审批" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <TY_Tabs :value="tabValue" v-loading.fullscreen.lock="loading">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="approvalHandleCount">
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="approveTo(item.id, 'approve')" v-for="(item, index) in listApply" :key="index">
            <div class="fileLists">
              <div class="fileType file_txt"></div>
              <div class="fileInfo">
                <div class="fileName" v-text="item.name" :title="item.name"></div>
                <div class="fileDetail">{{item.updateName || item.createName}} {{(item.updateTime || item.createTime) |formatDay('YYYY-MM-DD HH:mm:ss') }}</div>
              </div>
              <div class="fileFormat">
                <span class="fileVersion">G{{item.versionNo}}</span>
                <span class="fileNo">编号：{{item.fileSn}}</span>
              </div>
            </div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="approveTo(item.id, 'approve2')" v-for="(item, index) in listApprove" :key="index">
            <div class="fileLists">
              <div class="fileType file_txt"></div>
              <div class="fileInfo">
                <div class="fileName" v-text="item.name" :title="item.name"></div>
                <div class="fileDetail">{{item.updateName || item.createName}} {{(item.updateTime || item.createTime) |formatDay('YYYY-MM-DD HH:mm:ss') }}</div>
              </div>
              <div class="fileFormat">
                <span class="fileVersion">G{{item.versionNo}}</span>
                <span class="fileNo">编号：{{item.fileSn}}</span>
              </div>
            </div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>
  import * as moment from 'moment'
  import 'moment/locale/zh-cn'
  export default {
    name: 'contentApprove',
    data () {
      return {
        loading: true,
        isBackHome: true,
        weekDay: '',
        date_today: moment(new Date()).format('YYYY/MM/DD'),
        listApply: [],
        listApprove: [],
        listenersUid: []
      }
    },
    created: function () {
      let today = new Date()
      let week = today.getDay()
      let weekArr = ['日', '一', '二', '三', '四', '五', '六']
      this.weekDay = '周' + weekArr[week]
      let _this = this
      _this.$http.post('../../../about/aboutApplyFile.do', {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data
        _this.listApply = reqData.listAboutFileByApply
        _this.loading = false
      }).catch(function () {
        _this.loading = false
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
      _this.$http.post('../../../about/aboutAppoveFile.do', {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data
        _this.listApprove = reqData.listAboutFileByApprove
        _this.loading = false
      }).catch(function () {
        _this.loading = false
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
      this.listenersUid = [
        this.sphdSocket.subscribe('contentApproval', function (data) { // 终审时更新数据
          console.log('内容中间审批待审批订阅:' + data)
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            console.log('增加一条')
            newDate = reqData.aboutFileHis
            console.log('this.listApply', _this.listApply)
            console.log('newDate', newDate)
            _this.listApply.push(newDate)
          } else if (update < 0) { // 减少一条
            console.log('减少一条')
            let deletId = reqData.aboutFileHis.id
            _this.listApply.forEach(function (item, index) {
              if (deletId === item.id) {
                _this.listApply.splice(index, 1)
              }
            })
          }
        }, null, 'user'),
        this.sphdSocket.subscribe('approvedByContent', function (data) { // 终审时更新数据
          console.log('内容中间审批已批准订阅:' + data)
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            console.log('增加一条')
            newDate = reqData.aboutFileHis
            console.log(_this.listApprove)
            console.log(newDate)
            _this.listApprove.push(newDate)
          } else if (update < 0) { // 减少一条
            console.log('减少一条')
            let deletId = reqData.aboutFileHis.id
            _this.listApprove.forEach(function (item, index) {
              if (deletId === item.id) {
                _this.listApprove.splice(index, 1)
              }
            })
          }
        }, null, 'user')
      ]
    },
    destroyed: function () {
      let _this = this
      this.listenersUid.forEach(function (item) {
        _this.sphdSocket.unsubscribe(item)
      })
    },
    methods: {
      approveTo: function (id, type) {
        this.$router.push({
          path: `/contentDetail/${id}/${type}`
        })
      },
      screen: function () {
        this.$router.push({
          path: '/contentQuery/approve'
        })
      }
    }
  }
</script>
