/* creator:张旭博 date:2023/5/19 招聘管理 */
#qrCode{
    text-align: center;
    margin-top: 10px;
}
.btn-group{flex: auto;}
.page{padding: 8px 0 8px 70px;max-width: 1000px;}
.page .page-content-avatar {margin-top: 8px;}
.page .page-header{margin-top: 8px;padding: 8px 0;color: #76787d;}
.part{padding: 8px 0;margin-top: 8px;}
.panel-box{border-bottom: 1px solid #ddd;padding-top: 8px;}
.panel-box:last-child{border: none}
.panel-box .alert{color: #686b71}
.part_addBranches .panel-box{border-bottom:none;}
.kj-hr{border-bottom: 1px solid #eee;margin: 8px 0;}
.ty-alert{font-size: 14px;}
.btn-group{flex: auto;}
.functionCon {width: 500px; margin: 16px auto 0}
.functionCon div {margin-bottom: 8px;}
.manageCon , .addCon{
    width: 400px;
    margin: 8px auto 0;
}
.page{
    display: none;
    position: relative;
}
.manageCon .item{
    margin: 24px 0;
    line-height: 24px;
}
.ty-page-header{
    display: flex;
    line-height: 24px;
    padding: 0 0 0 70px;
    color: #5d9cec;
    margin-top: 16px;
}
.page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
}
.page-header__left::after {
    content: "";
    position: absolute;
    width: 1px;
    height: 16px;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #dcdfe6;
}
.page-header__left .icon-back {
    font-size: 18px;
    margin-right: 6px;
    align-self: center;
    position: relative;
    top: 1px;
}
.item{
    margin: 8px 0;
    line-height: 24px;
}
.topRightBtn{
    position: absolute;
    top: -28px;
    right: 0;
}
.toggleItem{
    display: none;
}
.textMax{
    font-size: 12px;
    color: #aaa;
}
/* creator:李玉婷 date:2017/11/6 */
.overflow{overflow:hidden;}
.sect{padding:10px 20px;}
.small_ttl{margin-bottom:12px;display: inline-block;font-size:18px;color:#48cfad;}
.masterInfo{width:84%;}
.con_part li{line-height: 36px}
.tx{margin-left:2%;width:18%;}
.con_part{padding-left:80px;font-size:14px;}
.viewerTx{width:130px;height:160px;text-align:center;margin:auto}
.viewerTx img{width:100%;}
.opinionCon{padding: 12px;}
.opinionCon input{border:1px solid rgb(204, 204, 204);}
.opinionCon ul li{padding:10px 0;}
.select2-container {  z-index: 99;  }
.bonceCon input, .bonceCon select, .bonceCon textarea {border: 1px solid #dee8f0;}
span.ty-color-red{margin-left:4px;margin-right:4px;}
.field{display:inline-block;width:120px;text-align:right;margin-right: 5px;vertical-align: top;  line-height:30px;}
.shu11 select{width:170px;}
.shu11 input{width:170px;padding-left:8px;margin-left: 4px;}
.jobInfor .con_part{width:84%;}


.us_field{display: inline-block;width:120px;}
.select_normal{  border: 1px solid #dee8f0;  background-color: #fff;  width: 152px;  text-align: left;  padding: 0;  color: #3f3f3f;  }
.us_input .sect input{  border: 1px solid #dee8f0;  background-color: #fff;  text-indent: 8px;  height:30px;  }
.select2-container--default .select2-selection--single .select2-selection__rendered{  line-height:30px;  }
.select2-container--default .select2-selection--single .select2-selection__arrow {height: 30px;}
.select2-container .select2-selection--single{height:30px;}
.importIntro{
    padding:20px 80px;
    color: #646c74;
}
.employeeQuery .searchCon{
    padding: 20px;
}
.employeeQuery input{
    border:1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    width: 100%;
    text-align: left;
    padding: 0 8px;
    color: #3f3f3f;
    height: 36px;
}
.employeeQuery input:focus{
    border:1px solid #5d9cec;
}
.eq_item{
    width: 420px;
    margin-bottom: 10px;
    border-bottom: 1px solid #e2e2e2;
    padding-bottom: 10px;
}
.eq_item:last-child{
    border-bottom: none;
}
.eq_item .eq_l{
    float: left;
    width: 20%;
    line-height:2em;
    text-align: right;
    padding-right:20px;
}
.eq_item .eq_r{
    float: left;
    width: 80%;
    line-height:2em;
    white-space: normal;
}
.laydate_body .laydate_y .laydate_yms ul{
    width: 120px;
}
.oprateBtns{padding:10px 0;margin-left:108px;}
.department_panel{position:relative}
.department_panel .option_con{position:absolute;top:30px;border: 1px solid #ccc;  font-size: 12px;
    width: 500px;  margin: -1px 0 0;background: #eaeaea; padding: 15px;z-index: 100;}
.gap{
    display: inline-block;
    width: 30px;
    height: 2px;
    background-color: #5d9cec;
}
.conFiled li{
    float: left;
    width:50%;
    margin-bottom: 10px;
}
.conFiled li span:first-child{
    min-width:100px;
    display: inline-block;
    text-align: right;
}
.conFiled li select{
    width:162px;
}
.userBaseInfo{
    padding:4px 30px;
    margin-bottom: 20px;
    background: #595959;
    overflow: hidden;
}
.userImg{
    float:left;
    width:100px;
}
.userImg img{
    width:100%;
    border-radius: 50%;
}
.userCon{
    float:left;
    min-width:600px;
    color:#eee;
}
.baseDetail span{
    margin-left: 10px;
    margin-right: 10px;
    font-size:12px;
}
.baseDetail span:first-child{
    margin-left: 0;
}
.contectList li{
    float: left;
    width:33%;
}
.otherInfo{
    border-bottom:1px solid #ccc;
}
.otherInfo h5{
    color: #00a278;
}
.charact{
    margin-left:110px;
    overflow: hidden;
}
.mmTtl{
    float:left;
    width:20%;
    min-width:100px;
}
.mmCon,.timeSlot{
    float:left;
    width:70%;
}
.mmCon span:first-child{
    margin-right:10px;
}
.timeSlot i{
    margin:0 10px;
}
.clear:before,.clear:after{
    display:block;
    clear:both;
    content:"";
    visibility:hidden;
    height:0
}
.btnSect{
    margin-top:10px;
    margin-bottom:10px;
}
.opertionList{
    padding-top:30px;
    margin-left:90px;
    width:70%;
}
.opertionList .ty-table td{
    text-align: left;
}
.opertionList tr td.tct{
    text-align: center;
}
.agreenTip {
    margin-right:20px;
    cursor:pointer;
}
.agreenTip i{
    margin-right:20px;
}
.panelTtl{
    margin-right:100px;
    color: #00a278;
    font-size: 16px;
    line-height: 32px;
}
.imgShow{
    width: 100%;
}
.imgShow img{
    width: 100%;
}
#qrCode{
    text-align: center;
    margin-top: 10px;
}
.offerFeedBack{
    display: none;
}
.us_field{display: inline-block;width:120px;}
.qrCode{
    text-align: center;
    padding:16px;
    color: #999;
    background-color: #fff;
    font-size: 12px;
}
#qrCode_small{
    width: 168px;
    height: 154px;
}
.recruit{
    width: 1000px;
    margin: auto;
}
.declare_avatar{
    padding: 32px 64px;
    background: #f0f0f0;
    display: flex;
    justify-content: space-between;
    color: #333;
}
.declare_avatar p{
    margin-bottom: 6px;
}
.declare_avatar h4{
    display: inline-block;
    padding-left: 8px;
    font-size: 16px;
    color: #666;
    font-weight: bold;
    border-left: 4px solid #5d9cec;
    margin-left: -12px;
    margin-bottom: 16px;
}
.row-flex{
    display: flex;
    flex-direction: row;
}
.phone_avatar{
    width: 360px;
    height: 560px;
    background: #f7f7f7;
    flex: none;
    margin-right: 32px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 5px #cacaca;
    position: relative;
}
.nav_bar{
    height: 48px;
    line-height: 48px;
    background-color: #36c6d3;
    text-align: center;
    z-index: 99;
    flex: none;
    display: flex;
    color: #fff;
}
.nav_left_btnGroup{
    flex: 1;
    text-align: left;
}
.nav_left_btnGroup i{
    margin-left: 8px;
}
.nav_title{
    text-align: center;
    max-width: 180px;
}
.nav_right_btnGroup{
    flex: 1;
    text-align: right;
}
.nav_right_btnGroup i{
    margin-right: 8px;
}
.phone_container{
    flex: auto;
    overflow: auto;
}
.company{
    margin: 8px 16px;
    overflow: hidden;
    color: #515151;
    font-size: 14px;
    line-height: 21px;
    text-indent: 28px;
}
.phone_container .item-row{
    display: flex;
    line-height: 24px;
    padding: 6px 8px;
    border-bottom: 1px solid #f0f0f0;
}
.item-column{
    display: flex;
    flex-direction: column;
    line-height: 24px;
    padding: 6px 8px;
    border-bottom: 1px solid #f0f0f0;
}
.item-row .item-title, .item-column .item-title{
    width: 130px;
    flex: none;
    text-align: left;
    color: #606266;
    padding: 0 8px;
}

.item-row .item-content, .item-column .item-content{
    flex: auto;
    text-align: right;
    padding: 0 8px;
    color: #333;
}
.item-column .item-content{
    text-align: left;
    margin-top: 4px;
}
.item .item-row{
    position: relative;
}
.item-row label{
    position: absolute;
    top: 0;
    display: block;
    width: 30px;
    height: 30px;
    text-align: center;
    color: #aaa;
    background: #f5f5f5;
    left: -29px;
    border: 1px solid #dcdfe6;
    font-weight: normal;
    line-height: 28px;
}
.postInfo .item-content{
    background: #fff;
    padding: 4px 8px;
    border: 1px solid #dee8f0;
    min-height: 30px;
    font-size: 13px;
    color: #606b75;
}
.bonceConMain{
    width: 350px;
    margin: 0 auto;
}
.input_choose{
    display: flex;
    position: relative;
    border: 1px solid #dcdfe6;
    min-height: 32px;
    width: 350px;
    background-color: #fff;
    padding: 4px 8px;
    color: #666;
}
.input_choose input.search{
    border: none;
    width: 20px;
}
.search{
    min-width: 80px;
}
.selected_item{
    display: inline-block;
    height: 26px;
    line-height: 26px;
    padding: 0 12px;
    background-color: #eef1f2;
    border-radius: 13px;
    font-size: 14px;
}
.input_choose_list {
    width: 350px;
    display: flex;
    flex-direction: column;
    background: #fff;
    display: none;
    max-height: 160px;
    overflow: auto;
    box-shadow: 0 1px 2px #ddd;
}
.input_choose_list.dir{
    display: flex;
    max-height: 300px
}
.input_choose_item{
    display: flex;
    padding: 8px 10px;
    cursor: default;
}
.input_choose_item .icon_avatar{
    width: 36px;
    height: 36px;
    margin-right: 8px;
    border-radius: 18px;
    background-color: #dcdfe6;
}
.input_choose_item_info{
    display: flex;
    flex-direction:column;
}
.input_choose_item_post{
    color: #aaa;
    font-size: 12px;
}
.input_choose_item.selected{
    opacity:0.4;
    filter:alpha(opacity=40);
}
.input_choose_item:not(.selected):hover{
    background-color: #e6f0f7;
}
.input_del {
    color: #ef5161;
    position: absolute;
    top: 0;
    display: block;
    right: -20px;
    line-height: 30px;
    cursor: pointer;

}
.input_del:hover{
    color: #ed5565;
}
#addPost .kj-input{
    width: 100%;
}

.delRole, .delFile{
    transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    display: inline-block;
    font-size: 18px;
    width: 18px;
    height: 18px;
    text-align: center;
    vertical-align: middle;
    line-height: 18px;
    cursor: pointer;
}
.msgItem{
    padding:0 8px;
    display: flex;
    flex-direction: row;
    /*justify-content: flex-end;*/
    margin-bottom: 8px;
}
.msgItem.selfMsg{
    flex-direction: row-reverse;
}
.msgItem.reviseMsg{
    flex-direction: row;
}
.msgItem.system{
    justify-content: center;
}
.msgItem .msgItemCon{
    margin: 0 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.msgItem.system .msgItemCon{
    align-items: center;
}
.msgItem.selfMsg .msgItemCon{
    align-items: flex-end;
}
.msgItemCon .applyTime{
    color: #999;
    font-size: 12px;
    margin-right: 8px;
}
.msgItemCon .userName{
    color: #666;
    font-size: 12px;
}
.msgItem .userInfo{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
}
.msgItem.selfMsg .userInfo{
    flex-direction: row-reverse;
}
.msgItem.reviseMsg .userInfo{
    flex-direction: row;
}
.msgItem.selfMsg .msgReply{
    color: #d3e6ff;
}
.msgItem p,.replyItem p{
    margin: 0;
}
.bubble_avatar{
    background: #fff;
    color: #807a7a;
    border-radius: 4px;
    padding: 8px 12px;
    position: relative;
    box-shadow: 0 0 6px #eee;
}
.avatar{
    width: 36px;
    height: 36px;
    cursor: pointer;
    border-radius: 5px;
    flex-shrink: 0;
    background: #36c6d3;
    text-align: center;
    line-height: 30px;
    color: #fff;
    font-size: 26px;
}
.avatar.small{
    width: 24px;
    height: 24px;
}
.foot_bar{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 42px;
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
}
.share_main{
    display: flex;
}
.share_main .share_des{
    font-size: 12px;
    margin-right: 4px;
}
.share_main .share_des .share_des_title{
    font-weight: bold;
    font-size: 14px;
}
.share_main .share_img{
    width: 96px;
    height: 50px;
}
.share_main .share_img img{
    width: 96px;
    height: 50px;
}
.app_info{
    font-size: 12px;
    margin-top: 4px;
    border-top: 1px solid #f0f0f0;
}
.app_info img{
    width: 10px;
    height: 10px;
}