<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.VoucherSubjectMapper" >

  <select id="getSingle"  parameterType="voucherSubject" resultType="voucherSubject">
    select 
    id, org, voucher_id AS voucherId, acount_date AS acountDate, subject, direction, amount,
    subject_detail AS subjectDetail, creator, create_name AS createName, create_date AS createDate,
    updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
    message_id AS messageId, subject_names AS subjectNames,reimburse_bill AS reimburseBill,reimburse_bill_item AS reimburseBillItem,memo
    from t_accountant_voucher_subject
    where id = #{id}
  </select>

  <select id="listPage"  parameterType="voucherSubject" resultType="voucherSubject">
    select
        id, org, voucher_id AS voucherId, acount_date AS acountDate, subject, direction, amount, subject_detail AS subjectDetail, creator,
    create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
     approve_Item AS approveItem, approve_status AS approveStatus,
    approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo,
    approve_memo AS approveMemo, message_id AS messageId, subject_names AS subjectNames,reimburse_bill AS reimburseBill,reimburse_bill_item AS reimburseBillItem,memo
    from t_accountant_voucher_subject
  </select>

    <select id="listByProfitSubject" resultType="voucherSubject">
        SELECT
        a.id, a.org, a.subject, a.direction, a.amount
        FROM
        t_accountant_voucher_subject a,
        t_accountant_voucher b
        WHERE
        a.voucher_id = b.id AND a.subject = #{subject} AND (b.approve_status = 2 OR b.approve_status = 7 OR b.source = 1 or b.source = 2)
        AND b.org = #{org} AND b.create_date = #{createDate}
    </select>


  <!-- 从凭证科目表得到金额 -->
  <select id="getSubjectByDirection" resultType="voucherSubject">
    SELECT id,org,amount,voucher_id AS voucherId,acount_date AS acountDate,subject,direction,subject_detail AS subjectDetail,
    creator,create_name as createName,create_date AS createDate,approve_status AS approveStatus
    FROM `t_accountant_voucher_subject` WHERE voucher_id=#{voucherId} AND direction=#{direction}
  </select>

  <delete id="delete" parameterType="voucherSubject" >
    delete from t_accountant_voucher_subject
    where id = #{id}
  </delete>

  <select id="getSubjectInfo" resultType="subjectTemp" parameterType="hashmap">
    SELECT v.id,v.subject AS  subject,s.name AS name,v.amount AS amount, v.subject_names AS subjectNames,v.reimburse_bill AS reimburseBill,
    v.reimburse_bill_item AS reimburseBillItem,v.memo,
    d.credit_quantity AS creditQuantity,d.debit_quantity AS debitQuantity,d.unit_price AS unitPrice,s.measure_unit AS measureUnit,
    s.quantity_assisting_accounting AS quantityAssistingAccounting
    FROM `t_accountant_voucher_subject` v,
    t_accountant_subject s,
    t_accountant_subject_detail d
      WHERE v.voucher_id=#{id} AND v.direction=#{direction} AND v.subject = s.subject AND s.org=#{org} and d.subject_id=v.id
  </select>
  <select id="listByVoucherSubject" resultType="voucherSubject">
    SELECT
    id, org, subject, amount
    FROM
    t_accountant_voucher_subject
    WHERE
    subject = #{subject} AND org = #{org}
  </select>

  <select id="listByVoucherSubjectes" resultType="voucherSubject">
    SELECT
    id, org, subject, amount
    FROM
    t_accountant_voucher_subject
    WHERE
    org = #{org} AND subject IN
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="getSubjectByVoucher"
          resultType="voucherSubject">
    SELECT id,org,amount,voucher_id AS voucherId,acount_date AS acountDate,subject,direction,subject_detail AS subjectDetail,
    creator,create_name as createName,create_date AS createDate,approve_status AS approveStatus, subject_names AS subjectNames,
    reimburse_bill AS reimburseBill,reimburse_bill_item AS reimburseBillItem,memo
    FROM `t_accountant_voucher_subject` WHERE voucher_id = #{voucherId}

  </select>

  <select id="listByVoucherDirection" resultType="voucherSubject">
    SELECT
    id, org, voucher_id AS voucherId, acount_date AS acountDate, subject, direction, amount,
    subject_detail AS subjectDetail, creator, create_name AS createName, create_date AS createDate,
    updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
    message_id AS messageId, subject_names AS subjectNames
    FROM  t_accountant_voucher_subject
    WHERE voucher_id= #{voucherId} AND direction= #{direction}
  </select>

  <select id="listByVoucherProfit" resultType="voucherSubject">
    SELECT
    a.id, a.org, a.subject, a.direction, a.amount
    FROM
    t_accountant_voucher_subject a,
    t_accountant_voucher b
    WHERE
    a.voucher_id = b.id AND b.org = #{org} AND b.create_date = #{createDate}
  </select>

    <select id="getSubjectByAmount" resultType="voucherSubject">
      SELECT
        s.voucher_id AS voucherId,
        s.amount,v.source
      FROM   t_accountant_voucher_subject s ,t_accountant_voucher v
      <where>
        s.org = #{org} AND s.amount = #{amount} and s.`voucher_id`=v.id  AND Date_format(s.acount_date, '%Y-%m') = #{acountDate}
        <if test="source!='3'.toString() and source!=null">
          and v.source=#{source}
        </if>
        <if test="source=='3'.toString()">
          and (v.source = '3' or v.source = '2')
        </if>

      </where>

    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="voucherSubject">
    insert into t_accountant_voucher_subject ( org, voucher_id,
    acount_date, subject, direction,
    amount, subject_detail, creator,
    create_name, create_date, updator,
    update_name, update_date, approve_Item,
    approve_status, approve_level, auditor,
    auditor_name, audit_date, operation,
    apply_memo, approve_memo, message_id, subject_names,reimburse_bill,reimburse_bill_item,memo)
    values ( #{org}, #{voucherId},
    #{acountDate}, #{subject}, #{direction},
    #{amount}, #{subjectDetail}, #{creator},
    #{createName}, now(), #{updator},
    #{updateName}, #{updateDate}, #{approveItem},
    #{approveStatus}, #{approveLevel}, #{auditor},
    #{auditorName}, #{auditDate}, #{operation},
    #{applyMemo}, #{approveMemo}, #{messageId}, #{subjectNames},#{reimburseBill},#{reimburseBillItem},#{memo})
  </insert>

  <update id="update" parameterType="voucherSubject" >
    update t_accountant_voucher_subject
    <set >
      <if test="org != null" >
        org = #{org},
      </if>
      <if test="voucherId != null" >
        voucher_id = #{voucherId},
      </if>
      <if test="acountDate != null" >
        acount_date = #{acountDate},
      </if>
      <if test="subject != null" >
        subject = #{subject},
      </if>
      <if test="direction != null" >
        direction = #{direction},
      </if>
      <if test="amount != null" >
        amount = #{amount},
      </if>
      <if test="subjectDetail != null" >
        subject_detail = #{subjectDetail},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
      <if test="subjectNames != null">
        subject_names = #{subjectNames}
      </if>
    </set>
    where id = #{id}
  </update>

  <update id="updateSubjectNo">
    UPDATE `t_accountant_voucher_subject` SET subject=#{subject} WHERE voucher_id=#{voucherId} AND direction=#{direction}
  </update>

  <delete id="deleteSubject">
    DELETE FROM t_accountant_voucher_subject WHERE voucher_id=#{voucherId}
  </delete>

</mapper>