
import {request} from "@/utils/request";
//供应商-MCI

/*
*   create: sy 2025-04-29   获取指标列表
 */
export function getMCIList(data){
    data = data || {};
    return request({
        url: '../eis/list',
        method: 'post',
        data: data
    })
}

/*
*   create: sy 2025-04-30   点击确定提交新修改后的权重数据
 */
// export function uppurchae(data){
//     data = data || {};
//     return request({
//         url: "../eis/edit",
//         method: 'post',
//         data: JSON.stringify(data),
//         //processData: false,
//         //application/json
//         contentType: 'application/json; charset=utf-8'
//     })
// }

/*
 *  creator: sy 2025-05-06  获取权重调整记录列表
 */
export function getweigList(data){
    data = data || {};
    return request({
        url:"../eis/history/histories",
        method: 'get',
        data: data
    })
}

/*
 *  creator: sy 2025-05-06  获取权重调整记录列表查看列表
 */
export function getweisetList(data){
    data = data || {};
    return request({
        url: '../eis/history/list',
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2025-05-14  根据算法类型获取算法
 */
export function getAecbyCat(data){
    data = data || {};
    return request({
        url:"../eis/aec/list",
        method: 'post',
        data: data
    })
}