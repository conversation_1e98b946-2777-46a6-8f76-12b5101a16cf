package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("link")
public class TAccountantSetting {
  private int id;
  private Integer org;
  private String key_;
  private String value_;
  private String memo;
  private Integer creator;
  private String createName;
  private Integer updator;
  private String updateName;
  private Date updateDate;

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public String getKey_() {
    return key_;
  }

  public void setKey_(String key_) {
    this.key_ = key_;
  }

  public String getValue_() {
    return value_;
  }

  public void setValue_(String value_) {
    this.value_ = value_;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }
}
