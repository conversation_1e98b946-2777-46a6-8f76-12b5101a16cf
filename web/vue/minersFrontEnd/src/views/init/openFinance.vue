<template>
  <div id="openFinance">
    <div v-if="mainNum === 1">
      <div v-show="!invoiceState">
        <div class="rowGap">
          <el-row>
            <el-col :span="16">欢迎启用“财务”功能！</el-col>
            <el-col :span="8">
              <div class="txtRight">
                <span class='fa fa-blue gapRt' :class="{'fa-dot-circle-o': nextPattern, ' fa-circle-o': !nextPattern}" @click="checkNextStep"></span>
                <span class="gapRL">本页项目已设置完毕！</span>
                <el-button class="ty-right" type="primary" @click="nextStep(1)">下一步</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="hrLine"></div>
      </div>
      <el-row class="rowGap">
        <el-col :span="16" v-show="!invoiceState">公司向客户开具哪几种发票？请进行“发票设置”。设置完后请进入“下一步”！</el-col>
        <el-col :span="16" v-show="invoiceState">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 2">返 回</span>
        </el-col>
        <el-col :span="8">
          <el-button class="ty-right" type="primary" @click="invoiceSetting()">发票设置</el-button>
        </el-col>
      </el-row>
      <table class="ty-table ty-table-control">
        <tbody>
        <tr>
          <td>本公司可开的发票种类</td>
          <td>有无附页</td>
          <td>行数</td>
          <td>税率</td>
          <td>金额上限</td>
          <td>操作</td>
        </tr>
        <tr v-for="(item, index) in indexInvoiceList" v-bind:key="index">
          <td>{{ categoryCharge(item["category"]) }}</td>
          <td>{{ (item["hasAttachment"] ? "有" : "无") }}</td>
          <td>{{ (item["linesLimited"]||"--") }}</td>
          <td>{{ item["enableTaxTate"] ? item["enableTaxTate"].replace(/,/g, "%, ") + '% ' : '--'}}</td>
          <td>{{ item["category"] == 1? "不含税": "" }}{{ item["category"] == 2? "含税": "" }}{{item.amountLimited}}元</td>
          <td class="ty-td-control">
            <span class='ty-color-blue' @click="editInvoice(item)">修改</span>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div v-if="mainNum === 2">
      <div class="rowGap">
        <el-row>
          <el-col :span="14">欢迎启用“财务”功能！</el-col>
          <el-col :span="10">
            <div class="txtRight">
              <span class='fa gapRt fa-blue' :class="{'fa-circle': initState, ' fa-circle-o': !initState}" @click="checkInitState"></span>
              <span class="gapRL">本页各项中必须进行的编辑均已完成，启用“财务”功能！</span>
              <el-button class="ty-right" type="primary" @click="nextStep(3)">确 定</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="hrLine"></div>
      <div class="gapBr">下列各项与财务数据的初始化有关，必须进行的编辑完成后，请勾选右上方的选项并点击“确定”，以完成财务的初始化工作。</div>
      <table class="ty-table ty-table-control">
        <tbody>
        <tr>
          <td>需编辑的项</td>
          <td>系统的要求</td>
          <td>状态</td>
          <td>操作</td>
        </tr>
        <tr>
          <td class="txt-left">公司向客户开具哪几种发票？</td>
          <td>必须编辑</td>
          <td>{{!invoiceState?'尚未编辑':'已编辑'}}</td>
          <td class="ty-td-control">
            <span class='ty-color-blue' @click="editInvoiceList()">编辑</span>
          </td>
        </tr>
        <tr>
          <td class="txt-left">银行账户及现金/备用金的信息</td>
          <td>必须编辑</td>
          <td>{{!accountState?'尚未编辑':'已编辑'}}</td>
          <td class="ty-td-control">
            <span class='ty-color-blue' @click="getAccountList()">编辑</span>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div v-if="mainNum === 3">
      <div class="rowGap">
        <el-row>
          <el-col :span="14">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 2">返 回</span>
          </el-col>
          <el-col :span="10">
            <div class="txtRight">
              <span class='fa gapRt fa-blue' :class="{'fa-circle': finishState, ' fa-circle-o': !finishState}" @click="accountsFinish"></span>
              <span class="gapRL">本页内容已编辑完成！</span>
              <el-button class="ty-right" type="primary" @click="nextStep(2)">确 定</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="hrLine"></div>
      <div class="rowGap tipBule">
        <div>下表内容经确认无误后，请勾选右上角的选项并点击确定，如需要，请进行相关操作。</div>
        <div class="blueTxt">注1 备用金/现金账户的当前余额与实际如不相符，请修改；</div>
        <div class="ty-clear">
          <span class="blueTxt">注2 列表中的银行账户如不全，请点击“新增银行账户”并完成后续操作。</span>
          <div class="ty-right">
            下表的银行账户中，对公户共{{accountsListNum.count}}个，非对公户共{{accountsListNum.noCount}}个
            <el-link type="primary" class="btnGap" @click="addAccount()">新增银行账户</el-link>
          </div>
        </div>
      </div>
      <table class="ty-table ty-table-control">
        <tbody>
        <tr>
          <td> 账户名称</td>
          <td> 开户行 </td>
          <td> 账号 </td>
          <td> 账户类型 </td>
          <td> 当前余额 </td>
          <td> 当前状态 </td>
          <td width="220px"> 操作 </td>
        </tr>
        <tr v-for="(item, index) in accountsList" v-bind:key="index">
          <td>{{ item.name}}</td>
          <td>{{ item.accountType === 1?'--': item.bankName }}</td>
          <td>{{ item.accountType === 1?'--': item.account }}</td>
          <td>{{ item.accountType === 1?'--': item.operation}}</td>
          <td>{{ (item.balance || 0).toFixed(2)}}</td>
          <td>{{ item.accountType === 1?'--': scanState(item.accountStatus)}}</td>
          <td class="ty-td-control txt-left">
            <el-button type="primary" :disabled="item.accountType === 1 && buildState == 4" @click="editAccount(item)" plain>修改</el-button>
            <el-button type="danger" v-if="item.accountType !== 1" @click="delAccount(item)" plain>删除</el-button>
<!--            <span class='ty-color-blue' @click="editAccount(item)">修改</span>-->
<!--            <span v-if="item.accountType !== 1" class='ty-color-blue gapLt' @click="delAccount(item)">删除</span>-->
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <!--  发票设置  -->
    <TyDialog v-if="invoiceSettingLog" width="650" :dialogTitle="invoiceTtl" :dialogHide="hideFun" :dialogName="'invoiceSettingLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('invoiceSettingLog')">取消</el-button>
        <el-button :class="{'bounce-disable': !returnCharge, 'bounce-ok': returnCharge}" :disabled="!returnCharge" @click="addInvoiceOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div id="invoiceSet">
          <div class="gapBt" v-if="invoiceEditType === 'edit'">
            请修改
            <span v-if="Number(invoiceInfo.category) === 1">增值税专用票</span>
            <span v-if="Number(invoiceInfo.category) === 2">增值税普通票</span>
            <span v-if="Number(invoiceInfo.category) === 3">其他普通票</span>
            的发票设置
          </div>
          <div id="setCatoryCon" v-if="invoiceEditType === 'add'">
            <div class="gapBt">请选择发票种类</div>
            <span class="item_i" id="setCatory1" >
                    <span class="mask" v-if="invoiceSet1"></span>
                    <span @click="setCatory(1)">
                        <i class="fa" :class="{'fa-circle': Number(invoiceInfo.category) === 1,'fa-circle-o': Number(invoiceInfo.category) !== 1}"></i> 增值税专用发票
                    </span>
                </span>
            <span class="item_i" id="setCatory2">
                    <span class="mask" v-if="invoiceSet2"></span>
                    <span @click="setCatory(2)">
                        <i class="fa" :class="{'fa-circle': Number(invoiceInfo.category) === 2,'fa-circle-o': Number(invoiceInfo.category) !== 2}"></i> 增值税普通发票
                    </span>
                </span>
            <span class="item_i" id="setCatory3">
                    <span class="mask" v-if="invoiceSet3"></span>
                    <span @click="setCatory(3)">
                        <i class="fa" :class="{'fa-circle': Number(invoiceInfo.category) === 3,'fa-circle-o': Number(invoiceInfo.category) !== 3}"></i> 其他普通发票
                    </span>
                </span>
          </div>
          <div v-if="[1,2,3,'1','2','3'].indexOf(invoiceInfo.category) > -1">
            <span class="ttl">发票有无附页</span>
            <span class="item_i" id="setFu1" @click="setFu(1)">
              <i class="fa" :class="{'fa-circle-o': !invoiceInfo.hasAttachment || invoiceInfo.hasAttachment === '','fa-dot-circle-o': invoiceInfo.hasAttachment}"></i>
              有</span>
            <span class="item_i" id="setFu0" @click="setFu(0)">
              <i class="fa" :class="{'fa-dot-circle-o': !invoiceInfo.hasAttachment && invoiceInfo.hasAttachment !== '','fa-circle-o': invoiceInfo.hasAttachment || invoiceInfo.hasAttachment === ''}"></i> 无</span>
          </div>
          <div v-if="invoiceInfo.hasAttachment !== '' && !invoiceInfo.hasAttachment">
            <span class="ttl">每张发票可开的行数上限 <i>*</i></span>
            <el-input type="text" id="hangNum" v-model="invoiceInfo.linesLimited" maxlength="9" @input="chargeNum('linesLimited')" /> 行
          </div>
          <div v-if="invoiceInfo.category === 1">
            <span class="ttl">同一张发票上是否允许选择不同税率的商品</span>
            <span class="item_i" id="setDiffLv1" @click="setDiffLv(1)">
              <i class="fa" :class="{'fa-dot-circle-o': invoiceInfo.multipleRate,'fa-circle-o': !invoiceInfo.multipleRate}"></i> 是</span>
            <span class="item_i" id="setDiffLv0" @click="setDiffLv(0)">
              <i class="fa" :class="{'fa-dot-circle-o': !invoiceInfo.multipleRate,'fa-circle-o': invoiceInfo.multipleRate}"></i> 否</span>
          </div>
          <div v-if="invoiceInfo.category === 1">
            <span class="ttl">请输入可供选择的税率 <i>*</i></span>
            <a id="addLv" @click="addRate()"> + 增加新税率 </a>
            <span id="stopLv" @click="scanStopLvs()" v-if="invoiceEditType ==='edit'">已停用的税率</span>
            <div id="lvs" v-show="rateList.length > 0">
            <span class="lv_item" :class="{'lv_gray': item.type === 3}" v-for="(item, index) in rateList" v-bind:key="index">
              <span>{{ item.lv }}%</span>
              <a v-if="item.type === 1" class='lv_control' @click='deleLv(index)'>删除</a>
              <a v-if="item.type === 2" class='lv_control' @click='changeLv(item, index)'>还原</a>
              <a v-if="item.type === 0" class='lv_control' @click='changeLv(item, index)'>停用</a>
              <a v-if="item.type === 3" class='lv_control' @click='changeLv(item, index)'>还原</a>
          </span>
            </div>
          </div>
          <div v-if="invoiceInfo.category === 1 || invoiceInfo.category === 2 || invoiceInfo.category === 3">
            <span class="ttl" v-show="invoiceInfo.category === 1">每张发票可开的金额上限（不含税）<i>*</i></span>
            <span class="ttl" v-show="invoiceInfo.category === 2">每张发票可开的含税金额上限<i>*</i></span>
            <span class="ttl" v-show="invoiceInfo.category === 3">每张发票可开的金额上限<i>*</i></span>
            <el-input type="text" v-model="invoiceInfo.amountLimited" maxlength="9" max="999999999" @input="chargeNum('amountLimited')" /> 元
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addLvConLog" width="500" dialogTitle="新增税率" color="blue" :dialogHide="hideFun" :dialogName="'addLvConLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('addLvConLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addLvOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <el-input type="text" placeholder="请输入税率" id="add_lv" v-model="rateCon" maxlength="9" @input="formatBasicVal('rateCon')" style="width: 200px" /> %
        </div>
      </template>
    </TyDialog>
    <!-- 新增银行账户 -->
    <TyDialog v-if="addAccountLog" width="500" dialogTitle="新增银行账户" color="green" :dialogHide="hideFun" :dialogName="'addAccountLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('addAccountLog')">取消</el-button>
        <el-button class="bounce-ok" @click="sureNewAccount">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="addAccountFormRef"
                 :model="addAccountForm"
                 label-position="left"
                 label-width="128"
                 style="max-width: 400px"
                 show-message="false"
        >
        <table class="ty-table ty-table-none" id="add_account">
          <tbody>
          <tr v-if="addAccountType === 'add'">
            <td class="form-title">
              <div class="linkGap">
                <span class="ty-color-red" style="margin-left: -3px;">* </span><span class="typeTtl">账户类型：</span>
              </div>
            </td>
            <td class="form-con">
              <div class="radioBox linkGap" id="operationRadio">
                <span class="ty-radio radioDisabled" value="对公户">
                  <i class="fa fa-circle-o"></i>
                  <span>对公户</span>
                </span>
                <span class="ty-radio" value="非对公户" @click="operationRadio(0)">
                  <i class="fa" :class="{'fa-circle-o': addAccountInfo.isPublic === '' || Number(addAccountInfo.isPublic) !== 0,'fa-dot-circle-o': addAccountInfo.isPublic !== '' && Number(addAccountInfo.isPublic) === 0}"></i>
                  <span>非对公户</span>
                </span>
              </div>
            </td>
          </tr>
          <tr v-if="addAccountType === 'edit'">
            <td class="form-title">
              <div class="linkGap">
                <span class="ty-color-red" style="margin-left: -1px;">* </span><span class="tytpeTtl">账户类型：</span>
              </div>
            </td>
            <td>
              <div class="linkGap">
              {{Number(addAccountInfo.isPublic) === 1 ? '对公户':'非对公户'}}
              </div>
            </td>
          </tr>
          <tr id="baseAccountSet" v-if="addAccountType === 'edit' && addAccountInfo.isPublic == 1">
            <td class="form-title"></td>
            <td class="form-con">
              <div class="radioBox linkGap" :class="{'radioDisabled': !isBasicAble}">
                <span class="ty-radio" value="基本户" id="baseAccount" @click="publicOption(1)">
                  <i class="fa" :class="{'fa-circle-o': Number(addAccountInfo.isBasic) !== 1,'fa-dot-circle-o': addAccountInfo.isBasic === 1}"></i>
                  <span>基本户</span>
                </span>
                <span class="ty-radio" value="非基本户" @click="publicOption(0)">
                  <i class="fa" :class="{'fa-circle-o': Number(addAccountInfo.isBasic) !== 0,'fa-dot-circle-o': addAccountInfo.isBasic === 0}"></i>
                  <span>非基本户</span>
                </span>
              </div>
            </td>
          </tr>
          <tr class="entrySect">
            <td colspan="2">
              <el-form-item label="账户名称：" required>
                <el-input v-model="addAccountInfo.name" />
              </el-form-item>
              <el-form-item label="开户行：" required>
                <el-input v-model="addAccountInfo.bankName" />
              </el-form-item>
              <el-form-item label="账号：" required>
                <el-input v-model="addAccountInfo.account" @input="chargeNum('account')" />
              </el-form-item>
              <el-form-item label="初始资金：" required>
                <el-input v-model="addAccountInfo.balance" maxlength="9" @input="formatBasicVal('balance')" /><!--onkeyup="clearNoNumO(this)"-->
              </el-form-item>
              <el-form-item label="可否取现：">
                <div class="radioBox radioDisabled" id="select2Radio">
                  <span class="ty-radio" value="1">
                    <i class="fa" :class="{'fa-circle-o': Number(addAccountInfo.cashable) !== 1,'fa-dot-circle-o': Number(addAccountInfo.cashable) === 1}"></i>
                    <span>可取现</span>
                  </span>
                  <span class="ty-radio" value="2">
                    <i class="fa" :class="{'fa-circle-o': Number(addAccountInfo.cashable) !== 2,'fa-dot-circle-o': Number(addAccountInfo.cashable) === 2}"></i>
                    <span>不可取现</span>
                  </span>
                </div>
              </el-form-item>
              <el-form-item label="备注：">
                <el-input v-model="addAccountInfo.memo" />
              </el-form-item>
            </td>
          </tr>
          </tbody>
        </table>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-if="scanStopLvsLog" width="930" dialogTitle="已停用的税率" color="blue" :dialogHide="hideFun" :dialogName="'scanStopLvsLog'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div id="scanStopLvs">
          <table class="ty-table ty-table-control">
            <tbody>
            <tr>
              <td>税率</td>
              <td>停用时间</td>
              <td>停用者</td>
              <td>启用时间</td>
              <td>启用者</td>
              <td>创建时间</td>
              <td>创建者</td>
              <td>操作</td>
            </tr>
            <tr v-for="(item, index) in rateHistories" :key="index">
              <td>{{item["rate"]}}%</td>
              <td>
                <div v-for="(hisItem, indexH) in item.financeInvoiceRateHistorys" :key="indexH">
                  <span v-if="hisItem.enabled"></span>
                  <span v-else>{{new Date(hisItem["updateDate"]).format('yyyy-MM-dd hh:mm:ss')}}</span>
                </div>
              </td>
              <td>
                <div v-for="(hisItem, indexH) in item.financeInvoiceRateHistorys" :key="indexH">
                  <span v-if="hisItem.enabled"></span>
                  <span v-else>{{hisItem.updateName}}</span>
                </div>
              </td>
              <td>
                <div v-for="(hisItem, indexH) in item.financeInvoiceRateHistorys" :key="indexH">
                  <span v-if="hisItem.enabled">{{hisItem.createDate? '':new Date(hisItem["updateDate"]).format('yyyy-MM-dd hh:mm:ss')}}</span>
                  <span v-else></span>
                </div>
              </td>
              <td>
                <div v-for="(hisItem, indexH) in item.financeInvoiceRateHistorys" :key="indexH">
                  <span v-if="hisItem.enabled">{{hisItem.createDate? '':hisItem.updateName}}</span>
                  <span v-else></span>
                </div>
              </td>
              <td>
                <div v-for="(hisItem, indexH) in item.financeInvoiceRateHistorys" :key="indexH">
                  <span v-if="hisItem.enabled">{{hisItem.createDate? new Date(hisItem["createDate"]).format('yyyy-MM-dd hh:mm:ss'):''}}</span>
                  <span v-else></span>
                </div>
              </td>
              <td>
                <div v-for="(hisItem, indexH) in item.financeInvoiceRateHistorys" :key="indexH">
                  <span v-if="hisItem.enabled">{{hisItem.createDate? hisItem.createName:''}}</span>
                  <span v-else></span>
                </div>
              </td>
              <td><span class="ty-color-blue" @click="startBtn(item)">启用</span></td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="startTipLog" width="500" dialogTitle="温馨提示：" color="blue" :dialogHide="hideFun" :dialogName="'startTipLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('startTipLog')">取消</el-button>
        <el-button class="bounce-ok" @click="startLv()">确定</el-button>
      </template>
      <template #dialogBody>
        <p class="ty-center"> 您确定要启用税率 <span class="ty-color-blue" id="stLv">{{rateForm.rate}}%</span> 吗？</p>
      </template>
    </TyDialog>
    <TyDialog v-if="accountEditLog" width="500" dialogTitle="修改" color="blue" :dialogHide="hideFun" :dialogName="'accountEditLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('accountEditLog')">取消</el-button>
        <el-button class="bounce-ok" @click="updateAccountSure()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <span class="form-title"><span class="ty-color-red">* </span>初始资金</span>
          <el-input id="updateBasic" type="text" v-model="basicVal" maxlength="9" @input="formatBasicVal('basicVal')" />
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="iKnowLog" width="500" dialogTitle="！提示" color="red" :dialogHide="hideIKnowLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideIKnowLog">我知道了</el-button>
      </template>
      <template #dialogBody>
        <div class="iKnowWord">
          <p>上步初始化尚未完成，故本模块初始化暂无法开始！</p>
          <p>上步初始化：{{judgeMessage.previousInit}}</p>
          <p>负责人：{{judgeMessage.userName}}</p>
        </div>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import * as api from "@/api/init";
import {initNav} from "@/utils/routeChange";
import auth from '@/sys/auth'
import {judgeAccountingRes, logout} from "@/api/api";

export default {
  data() {
    return {
      pageName:'openFinance',
      mainNum: 1,
      invoiceEditType: 'add',
      rateCon: '',
      basicVal: '',
      invoiceTtl: '发票设置',
      rateList: [],
      rateOrgList: [],
      indexInvoiceList: [],
      accountsList: [],
      stepList: [],
      rateHistories: [],
      accountsListNum: {
        count: 0,
        noCount: 0,
      },
      buildState: 0,
      accountType: '',
      finishState: false,
      isBasicAble: true,
      addAccountFormRef: {},
      addAccountForm: {},
      rateForm: {},
      nextPattern: false,
      initState: false,
      accountState: false,
      invoiceState: false,
      invoiceSettingLog: false,
      addLvConLog: false,
      addAccountLog: false,
      scanStopLvsLog: false,
      startTipLog: false,
      accountEditLog: false,
      iKnowLog: false,
      judgeMessage: {},
      invoiceInfo: {
        "linesLimited": '',
        "amountLimited": '',
        "category": '',
        "hasAttachment": '',
        "multipleRate": '',
        "enableTaxTate": ''
      },
      addAccountInfo:{
        'isPublic': '',
        'isBasic': '',
        'name': '',
        'bankName': '',
        'account': '',
        'balance': '',
        'cashable': '',
        'memo': ''
      }
    }
  },
  computed:{
    invoiceSet1(){
      let index = this.indexInvoiceList.findIndex(item => item.category === '1')
      return index > -1
    },
    invoiceSet2(){
      let index = this.indexInvoiceList.findIndex(item => item.category === '2')
      return index > -1
    },
    invoiceSet3(){
      let index = this.indexInvoiceList.findIndex(item => item.category === '3')
      return index > -1
    },
    returnCharge() {
      let able = true
      let type = Number(this.invoiceInfo.category) // 发票类型
      let amountNum = this.invoiceInfo.amountLimited
      if (type === "" || amountNum === "" || this.invoiceInfo.hasAttachment === "") {
        able = false
      }
      if (!this.invoiceInfo.hasAttachment) {
        let hangNum = this.invoiceInfo.linesLimited
        if (hangNum === "") {
          able = false
        }
      }
      if (Number(type) === 1) {
        var lvLen = this.rateList.length;
        if (lvLen === 0) {
          able = false
        }
      }
      return able;
    }
  },
  watch:{
  },
  mounted() {
    initNav({mid: 'vh', name: '财务', pageName: this.pageName}, this.getInitState, this)
  },
  methods: {
    getIndexInvoiceList(){
      api.getInvoiceList()
          .then(res => {
            let list = res.data["financeInvoiceSettings"] || [];
            this.indexInvoiceList = list
          })
    },
    invoiceSetting() {
      judgeAccountingRes()
          .then(res => {
            let state = res.data.data.operate //operate ture 是可以编辑， false 不能编辑
            if (state) {
              this.invoiceSettingLog = true
              this.invoiceEditType = 'add'
              this.invoiceTtl = '发票设置'
              this.initBounce()
              this.invoiceInfo.category = ''
            } else {
              this.judgeMessage = res.data.data
              this.iKnowLog = true
            }
          })
    },
    hideIKnowLog(){
      this.iKnowLog = false
    },
    editInvoice(info) {
      let detail = JSON.parse(JSON.stringify(info))
      this.invoiceSettingLog = true
      this.invoiceEditType = 'edit'
      this.invoiceTtl = '修改发票设置'
      this.initBounce()
      let lvsList = detail["enableTaxTate"].split(",");
      let arr = []
      for (var i = 0; i < lvsList.length; i++) {
        let ifo = {"type": 0, "lv": lvsList[i]};
        arr.push(ifo)
      }
      this.rateList = arr
      detail.category = detail.category * 1
      this.invoiceInfo = detail
    },
    addInvoiceOk(){
      var type = this.invoiceEditType
      var lvs = "";
      var addTaxRate = ""; // 新添加的税率
      var startTaxRate = ""; // 启用的税率
      var stopTaxRate = ""; // 要停用的税率

      var url = "../invoiceSetting/addInvoiceSetting.do";
      var data = {
        "linesLimited": this.invoiceInfo.linesLimited,
        "amountLimited": this.invoiceInfo.amountLimited,
        "category": this.invoiceInfo.category,
        "hasAttachment": this.invoiceInfo.hasAttachment,
        "multipleRate": this.invoiceInfo.multipleRate
      }
      if (data.category == 1) {
        this.rateList.forEach(function (info) {
          var lv = info["lv"];
          var tp = info["type"]; // 0 - 原来就有的 1 -新增的 2 - 启用的 3 - 停用的
          if (tp != 3) {
            lvs += lv + ",";
          }
          if (type == "edit") {
            if (tp == 1) {
              addTaxRate += lv + ",";
            } else if (tp == 2) {
              startTaxRate += lv + ",";
            } else if (tp == 3) {
              stopTaxRate += lv + ",";
            }
          }
        });
        lvs = lvs.slice(0, lvs.length - 1);
        stopTaxRate = stopTaxRate.slice(0, stopTaxRate.length - 1);
        addTaxRate = addTaxRate.slice(0, addTaxRate.length - 1);
        startTaxRate = startTaxRate.slice(0, startTaxRate.length - 1);
      }
      data.enableTaxTate = lvs
      if (type == "edit") {
        url = "../invoiceSetting/updateInvoiceSetting.do";
        if (data.category == 1) {
          data.addTaxRate = addTaxRate // 新添加的税率,多税率以,分隔;
          data.stopTaxRate = stopTaxRate // 要停用的税率,多税率以,分隔;
          data.startTaxRate = startTaxRate // 要启用的税率,多税率以,分隔；
        }
        data.id = this.invoiceInfo.id  // 发票设置id
      }
      if (data.multipleRate	=== '') data.multipleRate	= false
      api.addInvoiceTj(data, url)
          .then(res => {
            let tip = ``, idea = `error`
            if (type == "edit") {
              let state = res.data["status"];
              // 0-修改失败 1-系统中有相同的税率  2-修改成功
              if (state == 1) {
                tip = `系统中有相同的税率`
              } else if (state == 2) {
                idea = `success`
                tip = `修改成功`
                this.getIndexInvoiceList()
              } else if (state == 0) {
                tip = `操作失败`
              } else {
                tip = `未知原因，操作失败！`
              }
            } else {
              let state = res.data["state"];
              // state： 0-添加失败  1-添加成功  2-系统中已存在此发票类型
              if (state == 1) {
                idea = `success`
                tip = `操作成功`
                this.getIndexInvoiceList()
              } else if (state == 2) {
                tip = `系统中已存在此发票类型`
              } else if (state == 0) {
                tip = `操作失败`
              } else {
                tip = `未知原因，操作失败！`
              }
            }
            this.$message({
              type: idea,
              message: tip
            })
            this.invoiceSettingLog =  false
          })
     // clearInterval(formValid);
    },
    setCatory(type){
      this.initBounce();
      if(this.invoiceInfo.category == type) {
        this.invoiceInfo.category = ''
      } else {
        this.invoiceInfo.category = type
      }
    },
    initBounce(){
      this.invoiceInfo.linesLimited = ''
      this.invoiceInfo.amountLimited = ''
      this.invoiceInfo.hasAttachment = ''
      this.invoiceInfo.multipleRate = ''
      this.invoiceInfo.enableTaxTate = ''
      this.invoiceInfo.disableTaxRate = ''
    },
    setFu(type) {// creator : 李玉婷 2024-09-10   发票设置 - 选择 发票有无附页
      this.invoiceInfo.linesLimited = ''
      if(this.invoiceInfo.hasAttachment === Boolean(Number(type))) {
        this.invoiceInfo.hasAttachment = ''
      } else {
        this.invoiceInfo.hasAttachment = Boolean(Number(type))
      }
    },
    setDiffLv(type) {
      if(this.invoiceInfo.multipleRate === type) {
        this.invoiceInfo.multipleRate = ''
      } else {
        this.invoiceInfo.multipleRate = Boolean(Number(type))
      }
    },
    addRate(){//creator : 李玉婷 2024-09-10    发票设置 - 增加可供选择的税率
      this.rateCon = ''
      this.addLvConLog = true
    },
    addLvOk() {
      var lv = this.rateCon
      if (lv.trim() == "") {
        this.$message({
          type: 'error',
          message: '请输入税率'
        })
        return false;
      }
      lv = parseFloat(lv);
      if (lv > 100) {
        this.$message({
          type: 'error',
          message: '税率不合法'
        })
        return false;
      }
      let icon = this.rateList.findIndex(item => item.lv == lv)
      if (icon > -1) {
        this.$message({
          type: 'error',
          message: '系统中已存在此税率，不能重复'
        })
        return;
      }
      if(this.invoiceInfo.disableTaxRate.indexOf(lv) != -1) {
        this.$message({
          type: 'error',
          message: "该税率已被停用。您可在“已停用的税率”中将其重新启用"
        })
        return;
      }
      var info = {
        "type": 1, // 0 - 原来就有的税率 1-新增的税率 ， 2 - 启用的税率
        "lv": lv
      };
      this.rateList.push(info)
      this.addLvConLog = false
    },
    deleLv(num){
      this.rateList.splice(num, 1)
    },
    changeLv(info, num){
      let detail = JSON.parse(JSON.stringify(info));
      var type = detail["type"]; // 0 - 原来就有的 1 -新增的 2 - 启用的 3 - 停用的
      if (type == 0) { // 当前原有启用 - 改为 停用
        type = 3;
      } else if (type == 2) { // 原来停用的改为启用（当前）的后 - 再改为 停用
        this.rateList.splice(num, 1)
        return false
      } else if (type == 3) { // 原来启用的改为停用（当前）后 - 再改为 启用
        type = 0;
      }
      detail["type"] = type;
      this.rateList[num] = detail
    },
    //creator : 李玉婷 2024-09-10    发票设置 - 查看 停用的税率
    scanStopLvs(type){
      let settingId = this.invoiceInfo.id
      let settingHistoryId = ""
      if (type === 1) {
        //settingId =  $("#logSetID").val() ;
        //settingHistoryId = $("#logID").val() ;
      }
      let param = {"settingId": settingId , "settingHistoryId": settingHistoryId }
      api.getStopLvs(param).then(res => {
        let list = res.data["rateHistories"] || []
        const arr = []
        list.forEach(value => {
          let vIndex = this.rateList.findIndex(item => item.lv === value.rate)
          if (vIndex === -1) arr.push(value)
        })
        this.rateHistories = arr
      }).catch(err => {
        console.log('err=', err)
      })
      this.scanStopLvsLog = true
    },
    startBtn(info){
      let detail = JSON.parse(JSON.stringify(info))
      this.rateForm = detail
      this.startTipLog = true
    },
    startLv(){
      let lvJson = {"type": 2, "lv": this.rateForm.rate}
      this.startTipLog = false
      this.rateList.push(lvJson)
      let icon = this.rateHistories.findIndex(item => Number(item.rate) === Number(this.rateForm.rate))
      if (icon > -1) {
        this.rateHistories.splice(icon, 1)
      }
    },
    checkNextStep(){
      if(this.indexInvoiceList.length > 0){
        this.nextPattern = !this.nextPattern
      }
    },
    nextStep(step){
      let param = {
        id: this.stepList[0].id
      }
      if (step === 1) {
        if(!this.nextPattern){
          return false
        }
      } else if (step === 2) {
        if(!this.finishState){
          return false
        }
        param.id = this.stepList[3].id
      } else if (step === 3) {
        if(!this.initState){
          return false
        }
        param.id = this.stepList[1].id
      }
      api.financeSaveStep(param).then(() => {
        this.mainNum = 2
        if (step === 1) {
          this.invoiceState = true
          this.accountState = false
        } else if (step === 2){
          this.accountState = true
        } else if (step === 3){
          this.$message({
            showClose: true,
            type: 'success',
            message: "财务的初始化已完成！请及时登录并使用Wonderss系统！"
          })
          //this.logout()
          logout().then(res => {
            console.log('logOut res=', res)
            let status = res.status
            if(status === 200){
              //this.$router.replace('Start')
              location.reload(true)
            }else{
              this.$message.error('退出失败！')
            }
          }).catch(err => {
            console.log('logOut err=', err)
            this.$message.error('退出失败！')
          })

        }
      })
    },
    checkInitState(){
      if(this.invoiceState && this.accountState){
        this.initState = !this.initState
      } else {
        this.$message({
          showClose: true,
          type: 'error',
          message: "操作失败！本页必须编辑的项都编辑完后，才可勾选本选项！"
        })
      }
    },
    editInvoiceList(){
      this.mainNum = 1
      this.getIndexInvoiceList()
    },
    getAccountList(){
      let params = {'oid': auth.oid}
      this.mainNum = 3
      this.finishState = false
      let countA = 0,countB = 0
      api.getAccounts(params).then(res => {
        let accountsList = res.data["financeAccountList"] || []
        let accountFromState = res.data["res"] * 1
        for(var i=0;i<accountsList.length;i++){
          var operation = '--';
          if (accountsList[i].accountType !== 1) {
            if (accountsList[i].isPublic == "0") {
              operation = '非对公户';
              countA++
            } else {
              if (accountsList[i].isBasic == "1") {
                operation = '对公户 基本户';
              } else {
                operation = '对公户 非基本户';
              }
              countB++
            }
            accountsList[i].operation = operation
          }
        }
        this.accountsListNum.noCount = countA
        this.accountsListNum.count = countB
        this.accountsList = accountsList
        this.buildState = accountFromState
      }).catch(err => {
        console.log('err=', err)
      })
    },
    addAccount(){
      //初始化
      this.addAccountInfo = {
        'isPublic': 0,
        'isBasic': 0,
        'name': '',
        'bankName': '',
        'account': '',
        'balance': '',
        'cashable': 1,
        'memo': ''
      }
      this.addAccountLog = true
      this.addAccountType = 'add'
    },
    operationRadio(type){
      if (this.addAccountInfo.isPublic === '') {
        this.addAccountInfo.isPublic = type
        if (type === 0) {
          this.addAccountInfo.cashable = 1
        }
      } else {
        this.addAccountInfo.isPublic = ''
      }
    },
    publicOption(type){
      if (this.isBasicAble) {
        if (this.addAccountInfo.isBasic === type) {
          this.addAccountInfo.isBasic = ''
        } else {
          if (type === 1) {
            this.addAccountInfo.cashable = 1
          } else {
            this.addAccountInfo.cashable = 2
          }
          this.addAccountInfo.isBasic = type
        }
      }
    },
    sureNewAccount(){
      if (this.addAccountInfo.isPublic === ""){
        this.$message({
          type: 'error',
          message: "请选择账户类型!"
        })
      }else if (this.addAccountInfo.name == ""){
        this.$message({
          type: 'error',
          message: "请填写账户名称!"
        })
      }else if(this.addAccountInfo.bankName == ""){
        this.$message({
          type: 'error',
          message: "请填写开户行!"
        })
      }else if(this.addAccountInfo.account == ""){
        this.$message({
          type: 'error',
          message: "请填写账号!"
        })
      }else if(this.addAccountInfo.balance == ""){
        this.$message({
          type: 'error',
          message: "请填写初始金额!"
        })
      }else{
        let url = '/account/addAccount.do'
        const param = {
          'isPublic': 0,
          'isBasic': 0,
          'accountType': 3,
          'name': this.addAccountInfo.name,
          'bankName': this.addAccountInfo.bankName,
          'account': this.addAccountInfo.account,
          'initialAmount': this.addAccountInfo.balance,
          'cashable': this.addAccountInfo.cashable,
          'memo': this.addAccountInfo.memo
        }
        if (this.addAccountType === 'edit') {
          param.accountId = this.addAccountInfo.id
          url = '../account/updateAccount.do'
        }
        api.saveBankSure(url, param).then(res => {
          if (this.addAccountType === 'edit') {
            var status = res.data["status"];
            if(status == "1"){
              this.addAccountLog = false
              this.getAccountList()
            }else if(status == "2"){
              this.$message({
                type: 'error',
                message: "账户中的余额比初始金额少，不够冲账."
              })
            }
          } else {
            this.addAccountLog = false
            this.getAccountList()
          }
        })
      }
    },
    updateAccountSure(){
      var amount = this.basicVal;
      if(amount == ""){
        this.$message({
          type: 'success',
          message: "请填写初始金额!"
        })
        return false;
      }
      let data ={
        "accountId" : this.addAccountInfo.id,
        "initialAmount": this.basicVal
      }
      let url = '../account/updateAccount.do'
      api.saveBankSure(url, data).then(res => {
        if (this.addAccountType === 'edit') {
          var status = res.data["status"];
          if(status == "1"){
            this.accountEditLog = false
            this.getAccountList()
          }else if(status == "2"){
            this.$message({
              type: 'error',
              message: "账户中的余额比初始金额少，不够冲账."
            })
          }
        }
      })
    },
    editAccount(info){
      const detail = JSON.parse(JSON.stringify(info))
      this.addAccountType = 'edit'
      if (detail.accountType === 1) {
        if (this.buildState == 4){
          this.$message({
            type: 'error',
            message: "建账完成不能修改余额."
          })
          return false
        } else {
          this.basicVal = detail.balance
          this.accountEditLog = true
        }
      } else {
        this.addAccountLog = true
        if (Number(detail.isBasic) === 1) {
          this.isBasicAble = true
        } else {
          this.isBasicAble = false
        }
      }
      this.addAccountInfo = detail
    },
    delAccount(info, num){
      let param = {"id": info.id}
      this.accountsList.splice(num, 1)
      api.delBankSure(param).then(() => {
        this.getAccountList()
      })
    },
    accountsFinish(){
      this.finishState = !this.finishState
    },
    getInitState(){
      api.initStartSure('finance').then(res => {
        let stepList = res.data.data
        this.stepList = stepList
        stepList.forEach((item,index) => {
          if (index === 0) {
            if (item.state === 1){
              this.invoiceState = true
              this.mainNum = 2
            } else {
              this.getIndexInvoiceList()
            }
          }else if (index === 3 && item.state === 1) {
            this.accountState = true
          }
        })
      })
    },
    categoryCharge(type) {
      if (type == 1) {
        return "增值税专用票";
      } else if (type == 2) {
        return "增值税普通票";
      } else if (type == 3) {
        return "其他普通票";
      } else {
        return "";
      }
    },
    scanState(val){
      if( val == true){ return "启用" };
      if( val == false){ return "关闭" };
      return "";
    },
    limitNum(newVal){
      let value = ``
      if (newVal && newVal !== '') {
        value = newVal.replace(/[^\d]/g, '');
        if (value === '0' || value === 0) {
          value = '';
        }
      }
      return value
    },
    chargeNum(str){
      let newVal = ''
      switch (str) {
        case 'amountLimited':
        case 'linesLimited':
          newVal = this.invoiceInfo[str]
          break;
        case 'account':
          newVal = this.addAccountInfo[str]
          break;
      }
      let val = this.limitNum(newVal)
      switch (str) {
        case 'amountLimited':
        case 'linesLimited':
          this.invoiceInfo[str]= val
          break;
        case 'account':
          this.addAccountInfo[str]= val
          break;
      }
    },
    formatBasicVal(str) {
      let newVal = ''
      switch (str) {
        case 'rateCon':
        case 'basicVal':
          newVal = this[str]
          break;
        case 'balance':
          newVal = this.addAccountInfo[str]
          break;
      }
      let value = newVal.replace(/^0[0-9]+/, val => val[1])
          .replace(/^(\.)+/, "")
          .replace(/[^\d.]/g, "")
          .replace(/\.+/, ".")
          .replace(/^-*(\d+)\.(\d\d).*$/, "$1$2.$3");
      //(`^-?\\d*((\\.\\d{0,${decimalPlaces}})?)$`)
      switch (str) {
        case 'rateCon':
        case 'basicVal':
          this[str] = value
          break;
        case 'balance':
          this.addAccountInfo[str] = value
          break;
      }
    },
    hideFun(str){
      switch (str) {
        case 'invoiceSettingLog':
          this.invoiceSettingLog = false
          break
        case 'addLvConLog':
          this.addLvConLog = false
          break
        case 'addAccountLog':
          this.addAccountLog = false
          break
        case 'scanStopLvsLog':
          this.scanStopLvsLog = false
          break
        case 'startTipLog':
          this.startTipLog = false
          break
        case 'accountEditLog':
          this.accountEditLog = false
          break
      }
    }
  }
}
</script>
<style scoped lang="scss">
#openFinance{
  padding: 0 100px;
  font-size: 14px;
  .hrLine{border-top:  1px solid #d7d7d7;}
  .rowGap{padding: 24px 0;}
  .tipBule > div{margin-bottom: 6px;}
  .tipBule .blueTxt{color: #006fbf;}
  .el-row span{display: inline-block;line-height: 30px;}
  .el-row .el-col {line-height: 30px;}
  .gapRt{margin-right: 12px;}
  .gapRL{margin-right: 36px;}
  .gapBt{margin-bottom: 10px;}
  .gapLt{margin-bottom: 20px;}
  .gapBr{margin: 30px 0 10px 0;}
  .btnGap{margin-left: 100px;}
  input:not([type="radio"]):not([type="checkbox"]), select {
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    background-color: #fff;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    background-image: none;
    box-sizing: border-box;
    color: #606266;
    font-size: inherit;
    outline: none;
    padding: 0 8px;
    transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  }
  #invoiceSet i {
    color: #ed5565;
    font-style: inherit;
  }
  #invoiceSet span.item_i {
    margin-right: 20px;
  }
  #invoiceSet i.fa {
    color: #48cfad;
    margin-right: 5px;
  }
  #invoiceSet > div {
    padding: 10px 40px;
  }
  #invoiceSet > div:hover {
    background: #eaeaea;
  }
  #invoiceSet > div .ttl {
    display: inline-block;
    width: 305px;
  }
  #lvs {
    padding: 10px;
  }
  .lv_item {
    border: 1px solid #48cfad;
    line-height: 25px;
    display: inline-block;
    padding: 2px 5px;
    cursor: default;
    margin: 10px 10px 0 0;
  }
  .lv_gray {
    border: 1px solid #ccc;
  }
  .lv_control {
    float: right;
    width: 50px;
    text-align: center;
    cursor: pointer;
    color: #337ab7;
  }
  #addLv {
    color: #337ab7;
    cursor: default;
    border: 1px solid #f0f8ff;
  }
  #addLv:hover {
    border: 1px solid #219a54;
  }
  #stopLv , #stopLv2 {
    color: #337ab7;
    text-decoration: underline;
    cursor: pointer;
    display: inline-block;
    float: right;
  }
  #stopLv:hover , #stopLv2:hover{
    color: #3785c7;
  }
  #scanStopLvs td > div, #scanStopLvs2 td > div {
    height: 30px;
    margin: 0 -15px;
    border-bottom: 1px solid #ccc;
    line-height: 30px;
  }
  #scanStopLvs td > div:last-child, #scanStopLvs2 td > div:last-child {
    border-bottom: none;
  }
  #scanStopLvs , #scanStopLvs2{
    width: 900px;
  }
  #setCatoryCon , #setCatory1, #setCatory2, #setCatory3 {
    position: relative;
  }
  .txt-left{text-align: left;}
  .mask {
    position: absolute;
    z-index: 1;
    background: rgba(100, 100, 100, 0.2);
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  #setCatory1, #setCatory2, #setCatory3 {
    display: inline-block; width: 150px; height:20px;
  }
  #add_account {
    margin-left: 22px;
  }
  #add_account .ty-radio{
    cursor: pointer;
    margin-right:10px;
  }
  .radioBox{
    width: 220px;
    padding:5px 5px;
    background-color: #e8f0f5;
    line-height: 20px;
  }
  .radioDisabled,.radioDisabled .ty-radio{
    color: #ccc;
    cursor: not-allowed;
  }
  .pack{
    position:relative;
    width:220px;
  }
  .fa-blue{
    color: #409eff;
  }
  .bonceFoot .bounce-disable {  background: #ccc;  color: #666; margin:0 10px;  }
  .linkGap{margin-bottom: 18px;}
  .tytpeTtl{color: #67696d;}
  .form-title {width: 32%;text-align: left;}
  .el-input{width: 200px;}
  #add_account .el-input{width: 230px;}
  .txtRight{text-align: right;}
  .iKnowWord{
    width: 80%;
    margin: 0 auto;
    p{
      margin-bottom: 14px;
    }
  }
}
</style>