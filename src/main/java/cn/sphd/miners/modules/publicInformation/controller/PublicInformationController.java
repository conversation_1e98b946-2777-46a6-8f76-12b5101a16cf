package cn.sphd.miners.modules.publicInformation.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.publicInformation.dto.PiRecord;
import cn.sphd.miners.modules.publicInformation.dto.PiYearAclLog;
import cn.sphd.miners.modules.publicInformation.entity.*;
import cn.sphd.miners.modules.publicInformation.service.PiAclService;
import cn.sphd.miners.modules.publicInformation.service.PiCatService;
import cn.sphd.miners.modules.publicInformation.service.PiInfoServies;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Controller
@RequestMapping("/pi")
public class PublicInformationController {

    @Autowired
    PiCatService piCatService;
    @Autowired
    PiInfoServies piInfoServies;
    @Autowired
    PiAclService piAclService;


    //关于获取初始目录
    @ResponseBody
    @RequestMapping("/getPiDir.do")
    public JsonResult getPiDir(User user){
        HashMap<String, Object> map = piCatService.getPiInitialDir(user);
        return new JsonResult(1,map);
    }

    //获取目录下文件
    @ResponseBody
    @RequestMapping("/getPiCatFile.do")
    public JsonResult getPiCatFile(User user, Integer categoryId, PageInfo pageInfo){
        HashMap<String, Object> map = piInfoServies.getCatFile(user, categoryId, pageInfo);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //公共信息页获取带权限的文件
    @ResponseBody
    @RequestMapping("/getPiCatFileByUser.do")
    public JsonResult getPiCatFileByUser(User user, PageInfo pageInfo){
        HashMap<String, Object> map;
        if ("super".equals(user.getRoleCode()) || "smallSuper".equals(user.getRoleCode())) {
            map = piInfoServies.getCatFile(user, null, pageInfo);
        }else {
            map = piInfoServies.getUserFile(user, pageInfo);
        }
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //新增目录
    @ResponseBody
    @RequestMapping("/addPiDir.do")
    public JsonResult addPiDir(User user, String name){
        HashMap<String, Object> map = piCatService.addPiCat(user, name);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //修改目录名称
    @ResponseBody
    @RequestMapping("/upPiDirName.do")
    public JsonResult upPiDirName(User user, Integer categoryId, String name){
        HashMap<String, Object> map = piCatService.upPiCat(user, categoryId, name);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //目录修改记录
    @ResponseBody
    @RequestMapping("/getPiDirHis.do")
    public JsonResult getPiDirHis(Integer categoryId){
        List<PiCategoryHistory> list = piCatService.listPiCatHis(categoryId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list",list);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //删除目录
    @ResponseBody
    @RequestMapping("/delPiDir.do")
    public JsonResult delPiDir(User user, Integer categoryId){
        Integer state = piCatService.delPitCat(user, categoryId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //上传文件
    @ResponseBody
    @RequestMapping("/affirdPiFile.do")
    public JsonResult affirdPiFile(User user, Integer categoryId, String name, String fileSn,
                                 String content, String type, String path, String size,
                                 String version, String module){
        HashMap<String, Object> map = piInfoServies.addPiFile(user,categoryId,name,fileSn,content,type,null,
                path,size,version,null,null,null,null,null,null,module);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //换版文件
    @ResponseBody
    @RequestMapping("/changePiFileVersion.do")
    public JsonResult changePiFileVersion(User user, Integer fileId, String reason, String type,
                                        String path, String size, String version,
                                        String companyName, String taxIdNumber,
                                        String address, String bankName, String bankNo,
                                        String memo, String module){
        HashMap<String, Object> map = piInfoServies.upPiFileVersion(user,fileId,reason,type,path,size,
                version,companyName,taxIdNumber,address,bankName,bankNo,memo,module);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //获取文件换版记录
    @ResponseBody
    @RequestMapping("/getPiFileHistory.do")
    private JsonResult getPiFileHistory(Integer fileId, PageInfo pageInfo){
        HashMap<String, Object> map = piInfoServies.getListPiHis(fileId,pageInfo);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //获取文件基本信息
    @ResponseBody
    @RequestMapping("/getPiFileMes.do")
    public JsonResult getPiFileMes(Integer fileId) {
        HashMap<String, Object> map = piInfoServies.getPiInfoMes(fileId);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //获取历史文件的基本信息
    @ResponseBody
    @RequestMapping("/getPiHisFileMes.do")
    public JsonResult getPiHisFileMes(Integer fileHisId) {
        PiInfoHistory piInfoHistory = piInfoServies.piInfoHistorySingle(fileHisId);
        PiCategory cat = piCatService.piCategorySingle(piInfoHistory.getCategory());
        HashMap<String, Object> map = new HashMap<>();
        map.put("piHisFile", piInfoHistory);
        map.put("categoryName", cat.getName());
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //禁用某一历史版本
    @ResponseBody
    @RequestMapping("/disablePiFileHis.do")
    public JsonResult disablePiFileHis(Integer fileHisId) {
        Integer state = piInfoServies.disablePifileHis(fileHisId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //修改文件名字
    @ResponseBody
    @RequestMapping("/updatePiFileName.do")
    public JsonResult updatePiFileName(User user, String name, Integer fileId){
        Integer state = piInfoServies.upPiInfoName(user,fileId,name);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //修改文件编号
    @ResponseBody
    @RequestMapping("/updatePiFileSn.do")
    public JsonResult updatePiFileSn(User user, String fileSn, Integer fileId){
        Integer state = piInfoServies.upPiInfoFileSn(user,fileId,fileSn);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //移动文件
    @ResponseBody
    @RequestMapping("/changePiFilePlace.do")
    public JsonResult changePiFilePlace(User user, Integer fileId, Integer categoryId) {
        Integer state = piInfoServies.upPiInfoCategory(user,fileId,categoryId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //还原文件
    @ResponseBody
    @RequestMapping("/trashPiFileRestore.do")
    public JsonResult trashPiFileRestore(User user, Integer fileId) {
        Integer state = piInfoServies.restorePiInfo(user, fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //删除垃圾文件
    @ResponseBody
    @RequestMapping("/delTrashFile.do")
    public JsonResult delTrashFile(Integer fileId) {
        Integer state = piInfoServies.delPiInfo(fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //查看移动，修改名称和修改编号的记录
    @ResponseBody
    @RequestMapping("/getPiUpFileRecord.do")
    private JsonResult getPiUpFileRecord(String operation, Integer fileId){
        List<PiRecord> list = piInfoServies.listPiInfoHisByOperation(operation,fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //根据名字和编号搜索文件(普通检索)
    @ResponseBody
    @RequestMapping("/generalFindPiFile.do")
    public JsonResult generalFindPiFile(User user, String name, PageInfo pageInfo){
        HashMap<String, Object> map = piInfoServies.generalSearchPiInfo(user,name,pageInfo);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //获取文件的权限树
    @ResponseBody
    @RequestMapping("/getPiFileAuthTree.do")
    public JsonResult getPiFileAuthTree(User user, Integer fileId) {
        List<OrganizationDto> list = piAclService.getPiInfoAuthTree(user, fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //批量新增文件的权限
    @ResponseBody
    @RequestMapping("/addPiFileAuth.do")
    public JsonResult addPiFileAuth(User user, Integer fileId, String listPiFileUser) {
        List<PiInfoAcl> listPiAcl = JSON.parseArray(listPiFileUser,PiInfoAcl.class);
        piAclService.addPiInfoAuth(user,fileId,listPiAcl);
        return new JsonResult(1,1);
    }

    //批量删除文件的权限
    @ResponseBody
    @RequestMapping("/delPiFileAuth.do")
    public JsonResult delPiFileAuth(User user, Integer fileId, String listPiFileUser) {
        List<PiInfoAcl> listPiAcl = JSON.parseArray(listPiFileUser,PiInfoAcl.class);
        piAclService.delPiInfoAuth(user,fileId,listPiAcl);
        return new JsonResult(1,1);
    }

    //获取某机构被赋予过权限的人
    @ResponseBody
    @RequestMapping("/getPiAuthAllUser.do")
    public JsonResult getPiAuthAllUser(User user) {
        List<UserHonePageDto> list = piAclService.listAuthAllUser(user);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

     //获取某年被赋予和删除的权限记录
    @ResponseBody
    @RequestMapping("/getYearPiAuthLog.do")
    public JsonResult getYearPiAuthLog(User user, String year) {
        Date begin = null;
        if (year == null) {
            begin = NewDateUtils.getNewYearsDay();
        } else {
            begin = NewDateUtils.dateFromString(year+"-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        List<PiYearAclLog> listYearLog = new ArrayList<>();
        Date end = NewDateUtils.getLastTimeOfYear(begin);
        List<PiAclLog>  list = piAclService.listAclLog(user,begin,end);
        List<PiAclLog> listMonth = new ArrayList<>();
        if (!list.isEmpty()) {
            Date time = NewDateUtils.changeMonth(list.get(0).getCreateDate(),0);
            for (int i = 0; i<list.size();i++) {
                if (list.get(i).getCreateDate().getTime() >= time.getTime()) {
                     listMonth.add(list.get(i));
                }else {
                    PiYearAclLog piYearAclLog = new PiYearAclLog();
                    piYearAclLog.setTime(time);
                    piYearAclLog.setListAclLog(listMonth);
                    listYearLog.add(piYearAclLog);
                    time = NewDateUtils.changeMonth(list.get(i).getCreateDate(),0);
                    listMonth = new ArrayList<>();
                    listMonth.add(list.get(i));
                }
                if (i == list.size()-1) {
                    PiYearAclLog piYearAclLog = new PiYearAclLog();
                    piYearAclLog.setTime(time);
                    piYearAclLog.setListAclLog(listMonth);
                    listYearLog.add(piYearAclLog);
                }
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("listYearLog", listYearLog);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //获取某次权限记录的详情
    @ResponseBody
    @RequestMapping("/getPiAuthLogDetail.do")
    public JsonResult getPiAuthLogDetail(Integer aclLog) {
        List<PiAclDetail> list = piAclService.listAclDetail(null, aclLog);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //获取某人权限的赋予记录
    @ResponseBody
    @RequestMapping("/getUserPiAuthDetail.do")
    public JsonResult getUserPiAuthDetail(Integer userId) {
        List<PiAclDetail> list = piAclService.listAclDetail(userId, null);
        List<PiYearAclLog> listYearLog = new ArrayList<>();
        if (!list.isEmpty()) {
            List<PiAclDetail> listMonth = new ArrayList<>();
            Date time = NewDateUtils.changeMonth(list.get(0).getCreateDate(),0);
            for (int i = 0; i<list.size();i++) {
                if (list.get(i).getCreateDate().getTime() >= time.getTime()) {
                    listMonth.add(list.get(i));
                }else {
                    PiYearAclLog piYearAclLog = new PiYearAclLog();
                    piYearAclLog.setTime(time);
                    piYearAclLog.setLsitAclDetail(listMonth);
                    listYearLog.add(piYearAclLog);
                    time = NewDateUtils.changeMonth(list.get(i).getCreateDate(),0);
                    listMonth = new ArrayList<>();
                    listMonth.add(list.get(i));
                }
                if (i == list.size()-1) {
                    PiYearAclLog piYearAclLog = new PiYearAclLog();
                    piYearAclLog.setTime(time);
                    piYearAclLog.setLsitAclDetail(listMonth);
                    listYearLog.add(piYearAclLog);
                }
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("listYearLog", listYearLog);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //根据人员获取当前的权限文件或根据文件获取可见人员
    @ResponseBody
    @RequestMapping("/getPiFileAcl.do")
    public JsonResult getPiFileAcl(Integer userId, Integer fileId) {
        List<PiInfoAcl> list = piAclService.listPiAcl(userId, fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        JsonResult result = new JsonResult(1,map);
        return result;
    }


    /*
    页面跳转
     */

    //目录
    @RequestMapping("/publicDirection.do")
    public String publicDirection(User user, Model model){
        model.addAttribute("user",user);
        return "/publicInfo/publicMenu";
    }

    //内容管理
    @RequestMapping("/publicInformation.do")
    public String publicInformation(User user, Model model){
        model.addAttribute("user",user);
        return "/publicInfo/publicInfo";
    }


}
