<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.inStockCheck.mapper.TMtBoundWarehouseMapper">
  <resultMap id="BaseResultMap" type="cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse">
    <!--@mbg.generated-->
    <!--@Table t_mt_bound_warehouse-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="location" jdbcType="BIGINT" property="location" />
    <result column="supplier_material" jdbcType="INTEGER" property="supplierMaterial" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="locked_amount" jdbcType="DECIMAL" property="lockedAmount" />
    <result column="packaging_count" jdbcType="INTEGER" property="packagingCount" />
    <result column="unit" jdbcType="CHAR" property="unit" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="updator" jdbcType="INTEGER" property="updator" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="operation" jdbcType="CHAR" property="operation" />
    <result column="previous_id" jdbcType="INTEGER" property="previousId" />
    <result column="version_no" jdbcType="INTEGER" property="versionNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `location`, supplier_material, amount, locked_amount, packaging_count, unit, 
    memo, creator, create_name, create_date, updator, update_name, update_date, `operation`, 
    previous_id, version_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_mt_bound_warehouse
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_mt_bound_warehouse
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_bound_warehouse (`location`, supplier_material, amount, 
      locked_amount, packaging_count, unit, 
      memo, creator, create_name, 
      create_date, updator, update_name, 
      update_date, `operation`, previous_id, 
      version_no)
    values (#{location,jdbcType=BIGINT}, #{supplierMaterial,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, 
      #{lockedAmount,jdbcType=DECIMAL}, #{packagingCount,jdbcType=INTEGER}, #{unit,jdbcType=CHAR}, 
      #{memo,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{updator,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{operation,jdbcType=CHAR}, #{previousId,jdbcType=INTEGER}, 
      #{versionNo,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_bound_warehouse
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="location != null">
        `location`,
      </if>
      <if test="supplierMaterial != null">
        supplier_material,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="lockedAmount != null">
        locked_amount,
      </if>
      <if test="packagingCount != null">
        packaging_count,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="previousId != null">
        previous_id,
      </if>
      <if test="versionNo != null">
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="location != null">
        #{location,jdbcType=BIGINT},
      </if>
      <if test="supplierMaterial != null">
        #{supplierMaterial,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="lockedAmount != null">
        #{lockedAmount,jdbcType=DECIMAL},
      </if>
      <if test="packagingCount != null">
        #{packagingCount,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=CHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=CHAR},
      </if>
      <if test="previousId != null">
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse">
    <!--@mbg.generated-->
    update t_mt_bound_warehouse
    <set>
      <if test="location != null">
        `location` = #{location,jdbcType=BIGINT},
      </if>
      <if test="supplierMaterial != null">
        supplier_material = #{supplierMaterial,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="lockedAmount != null">
        locked_amount = #{lockedAmount,jdbcType=DECIMAL},
      </if>
      <if test="packagingCount != null">
        packaging_count = #{packagingCount,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=CHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=CHAR},
      </if>
      <if test="previousId != null">
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse">
    <!--@mbg.generated-->
    update t_mt_bound_warehouse
    set `location` = #{location,jdbcType=BIGINT},
      supplier_material = #{supplierMaterial,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      locked_amount = #{lockedAmount,jdbcType=DECIMAL},
      packaging_count = #{packagingCount,jdbcType=INTEGER},
      unit = #{unit,jdbcType=CHAR},
      memo = #{memo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      `operation` = #{operation,jdbcType=CHAR},
      previous_id = #{previousId,jdbcType=INTEGER},
      version_no = #{versionNo,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_mt_bound_warehouse
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`location` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.location,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="supplier_material = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.supplierMaterial,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.amount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="locked_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lockedAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="packaging_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.packagingCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.unit,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.operation,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="previous_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.previousId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="version_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.versionNo,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_mt_bound_warehouse
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`location` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.location != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.location,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="supplier_material = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.supplierMaterial != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.supplierMaterial,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.amount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="locked_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lockedAmount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.lockedAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="packaging_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packagingCount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.packagingCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.unit,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.memo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updator != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operation != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.operation,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="previous_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.previousId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.previousId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="version_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.versionNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.versionNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_bound_warehouse
    (`location`, supplier_material, amount, locked_amount, packaging_count, unit, memo, 
      creator, create_name, create_date, updator, update_name, update_date, `operation`, 
      previous_id, version_no)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.location,jdbcType=BIGINT}, #{item.supplierMaterial,jdbcType=INTEGER}, #{item.amount,jdbcType=DECIMAL}, 
        #{item.lockedAmount,jdbcType=DECIMAL}, #{item.packagingCount,jdbcType=INTEGER}, 
        #{item.unit,jdbcType=CHAR}, #{item.memo,jdbcType=VARCHAR}, #{item.creator,jdbcType=INTEGER}, 
        #{item.createName,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.updator,jdbcType=INTEGER}, 
        #{item.updateName,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP}, #{item.operation,jdbcType=CHAR}, 
        #{item.previousId,jdbcType=INTEGER}, #{item.versionNo,jdbcType=INTEGER})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from t_mt_bound_warehouse where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <update id="updateBatchUseMultiQuery" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update t_mt_bound_warehouse
      <set>
        <if test="item.location != null">
          `location` = #{item.location,jdbcType=BIGINT},
        </if>
        <if test="item.supplierMaterial != null">
          supplier_material = #{item.supplierMaterial,jdbcType=INTEGER},
        </if>
        <if test="item.amount != null">
          amount = #{item.amount,jdbcType=DECIMAL},
        </if>
        <if test="item.lockedAmount != null">
          locked_amount = #{item.lockedAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.packagingCount != null">
          packaging_count = #{item.packagingCount,jdbcType=INTEGER},
        </if>
        <if test="item.unit != null">
          unit = #{item.unit,jdbcType=CHAR},
        </if>
        <if test="item.memo != null">
          memo = #{item.memo,jdbcType=VARCHAR},
        </if>
        <if test="item.creator != null">
          creator = #{item.creator,jdbcType=INTEGER},
        </if>
        <if test="item.createName != null">
          create_name = #{item.createName,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null">
          create_date = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updator != null">
          updator = #{item.updator,jdbcType=INTEGER},
        </if>
        <if test="item.updateName != null">
          update_name = #{item.updateName,jdbcType=VARCHAR},
        </if>
        <if test="item.updateDate != null">
          update_date = #{item.updateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.operation != null">
          `operation` = #{item.operation,jdbcType=CHAR},
        </if>
        <if test="item.previousId != null">
          previous_id = #{item.previousId,jdbcType=INTEGER},
        </if>
        <if test="item.versionNo != null">
          version_no = #{item.versionNo,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_bound_warehouse
    (`location`, supplier_material, amount, locked_amount, packaging_count, unit, memo, 
      creator, create_name, create_date, updator, update_name, update_date, `operation`, 
      previous_id, version_no)
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.location != null">
          #{item.location,jdbcType=BIGINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.supplierMaterial != null">
          #{item.supplierMaterial,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.amount != null">
          #{item.amount,jdbcType=DECIMAL},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.lockedAmount != null">
          #{item.lockedAmount,jdbcType=DECIMAL},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.packagingCount != null">
          #{item.packagingCount,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.unit != null">
          #{item.unit,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.memo != null">
          #{item.memo,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.creator != null">
          #{item.creator,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createName != null">
          #{item.createName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createDate != null">
          #{item.createDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updator != null">
          #{item.updator,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateName != null">
          #{item.updateName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateDate != null">
          #{item.updateDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.operation != null">
          #{item.operation,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.previousId != null">
          #{item.previousId,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.versionNo != null">
          #{item.versionNo,jdbcType=INTEGER}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_bound_warehouse
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      `location`,
      supplier_material,
      amount,
      locked_amount,
      packaging_count,
      unit,
      memo,
      creator,
      create_name,
      create_date,
      updator,
      update_name,
      update_date,
      `operation`,
      previous_id,
      version_no,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{location,jdbcType=BIGINT},
      #{supplierMaterial,jdbcType=INTEGER},
      #{amount,jdbcType=DECIMAL},
      #{lockedAmount,jdbcType=DECIMAL},
      #{packagingCount,jdbcType=INTEGER},
      #{unit,jdbcType=CHAR},
      #{memo,jdbcType=VARCHAR},
      #{creator,jdbcType=INTEGER},
      #{createName,jdbcType=VARCHAR},
      #{createDate,jdbcType=TIMESTAMP},
      #{updator,jdbcType=INTEGER},
      #{updateName,jdbcType=VARCHAR},
      #{updateDate,jdbcType=TIMESTAMP},
      #{operation,jdbcType=CHAR},
      #{previousId,jdbcType=INTEGER},
      #{versionNo,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      `location` = #{location,jdbcType=BIGINT},
      supplier_material = #{supplierMaterial,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      locked_amount = #{lockedAmount,jdbcType=DECIMAL},
      packaging_count = #{packagingCount,jdbcType=INTEGER},
      unit = #{unit,jdbcType=CHAR},
      memo = #{memo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      `operation` = #{operation,jdbcType=CHAR},
      previous_id = #{previousId,jdbcType=INTEGER},
      version_no = #{versionNo,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_bound_warehouse
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="location != null">
        `location`,
      </if>
      <if test="supplierMaterial != null">
        supplier_material,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="lockedAmount != null">
        locked_amount,
      </if>
      <if test="packagingCount != null">
        packaging_count,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="previousId != null">
        previous_id,
      </if>
      <if test="versionNo != null">
        version_no,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="location != null">
        #{location,jdbcType=BIGINT},
      </if>
      <if test="supplierMaterial != null">
        #{supplierMaterial,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="lockedAmount != null">
        #{lockedAmount,jdbcType=DECIMAL},
      </if>
      <if test="packagingCount != null">
        #{packagingCount,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=CHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=CHAR},
      </if>
      <if test="previousId != null">
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="location != null">
        `location` = #{location,jdbcType=BIGINT},
      </if>
      <if test="supplierMaterial != null">
        supplier_material = #{supplierMaterial,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="lockedAmount != null">
        locked_amount = #{lockedAmount,jdbcType=DECIMAL},
      </if>
      <if test="packagingCount != null">
        packaging_count = #{packagingCount,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=CHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=CHAR},
      </if>
      <if test="previousId != null">
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>