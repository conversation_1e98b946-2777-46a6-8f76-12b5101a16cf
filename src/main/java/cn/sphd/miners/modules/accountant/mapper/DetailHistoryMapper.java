package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectDetailHistory;
import org.springframework.stereotype.Component;

@Component
public interface DetailHistoryMapper extends BaseMapper<TAccountantSubjectDetailHistory> {

    TAccountantSubjectDetailHistory getSubjectDetailHistory(TAccountantSubjectDetailHistory param);

    /*删除历史详情*/
    int deleteHistoryDetail(TAccountantSubjectDetailHistory param);

    /*将原详情数据写到历史表去*/
    int insertDetailHistorySwap(TAccountantSubjectDetailHistory param);

    TAccountantSubjectDetailHistory getSubjectDetail(TAccountantSubjectDetailHistory param);

    int deleteDetail(TAccountantSubjectDetailHistory param);

}