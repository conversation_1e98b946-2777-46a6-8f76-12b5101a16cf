package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesHistoryEntity;

import java.io.Serializable;
import java.util.List;

public interface AcctTriflesHistoryDao extends IBaseDao<AcctTriflesHistoryEntity,Serializable> {
    void updateName(String name, int triflesId);

    List<AcctTriflesHistoryEntity> triflesUpdateRecord(Integer org, int triflesId);

    AcctTriflesHistoryEntity getLastByReportId(Integer triflesId);
}
