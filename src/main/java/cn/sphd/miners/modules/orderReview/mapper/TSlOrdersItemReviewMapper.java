package cn.sphd.miners.modules.orderReview.mapper;

import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReview;

import java.util.List;
import java.util.Map;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface TSlOrdersItemReviewMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlOrdersItemReview selectTSlOrdersItemReviewById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlOrdersItemReview 
     * @return 集合
     */
    public List<TSlOrdersItemReview> selectTSlOrdersItemReviewList(TSlOrdersItemReview tSlOrdersItemReview);

    /**
     * 新增
     * 
     * @param tSlOrdersItemReview 
     * @return 结果
     */
    public int insertTSlOrdersItemReview(TSlOrdersItemReview tSlOrdersItemReview);

    /**
     * 修改
     * 
     * @param tSlOrdersItemReview 
     * @return 结果
     */
    public int updateTSlOrdersItemReview(TSlOrdersItemReview tSlOrdersItemReview);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewByIds(Long[] ids);

    List<Map<String, Object>> getPreReviewsByItemId(Integer itemId);
}
