<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResMapper">

    <select id="getSingle"  parameterType="resource" resultType="resource">
        select
            id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
            version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
            DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
            DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
            DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
            approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
            DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
            updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash, enabled,
            teminate_state AS teminateState, DATE_FORMAT(teminate_time,'%Y-%m-%d') AS teminateTime, teminater,
            teminater_name AS teminaterName, lock_uuid AS lockUuid, batch_uuid AS batchUuid
        from t_resource
        where id = #{id}
    </select>

    <select id="listPage"  parameterType="hashmap" resultType="resource">
        select
            id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
            version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
            DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
            DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
            DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
            approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
            DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
            updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
    </select>

    <select id="getFileByCategoryAndUserId" resultType="resource">
        select
            r.id, r.category, r.name, r.org, r.category_path AS categoryPath, r.file_sn AS fileSn, r.reason, r.path, r.size, r.change_num AS changeNum,
            r.download_num AS downloadNum,  r.view_num AS viewNum,  r.move_num AS moveNum,
            r.modify_num AS modifyNum, r.version, r.keyword, r.valid, r.is_stick AS isStick, r.operator,
            r.opertator_name AS opertatorName, DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
            r.verifier, r.verifier_name AS verifierName,
            DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate, r.auditor, r.audit_name AS auditName,
            DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, r.approve_status AS approveStatus,
            r.approver, r.approver_name AS approverName,
            DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate, r.operation, r.apply_memo AS applyMemo,
            r.approve_memo AS approveMemo, r.memo, r.creator, r.create_name AS createName,
            DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
            r.updator, r.update_name AS updateName,
            DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,r.message_id AS messageId, r.content,
            r.total_size AS totalSize, r.is_trash AS isTrash
        from t_resource r, t_resource_acl ra
        where r.id = ra.resource and r.category = #{category} and ra.user = #{user}
    </select>

    <select id="getFileBycategory" resultType="resource">
        select
            id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
            download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
            version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
            DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
            DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
            DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
            approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
            operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
            DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
            updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
            message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
        where category = #{category} and valid = 1
    </select>

    <select id="getOneFileByCategorylistPage" resultType="resource">
        select id
        from t_resource
        where category = #{parent} and valid = 1
    </select>

    <select id="getFileIdsByCategory" resultType="Integer">
        select id
        from t_resource
        where category = #{category} and valid = 1
    </select>

    <select id="filelistPage" parameterType="hashmap" resultType="resource">
        select
        r.id, r.category, r.name, r.org, r.category_path AS categoryPath, r.file_sn AS fileSn, r.reason, r.path, r.size, r.change_num AS changeNum,
        r.download_num AS downloadNum,  r.view_num AS viewNum,  r.move_num AS moveNum,
        r.modify_num AS modifyNum, r.version, r.keyword, r.valid, r.is_stick AS isStick, r.operator,
        r.opertator_name AS opertatorName, DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
        r.verifier, r.verifier_name AS verifierName,
        DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate, r.auditor, r.audit_name AS auditName,
        DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, r.approve_status AS approveStatus,
        r.approver, r.approver_name AS approverName,
        DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate, r.operation, r.apply_memo AS applyMemo,
        r.approve_memo AS approveMemo, r.memo, r.creator, r.create_name AS createName,
        DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        r.updator, r.update_name AS updateName,
        DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,r.message_id AS messageId, r.content,
        r.total_size AS totalSize, r.is_trash AS isTrash, r.teminate_state AS teminateState,
        DATE_FORMAT(r.teminate_time,'%Y-%m-%d') AS teminateTime, r.teminater, r.teminater_name AS teminaterName
        from t_resource r, t_resource_acl ra
        where r.id = ra.resource and r.category = #{category} and ra.user = #{user} and r.approve_status = 2
        and r.valid = 1
        <if test="teminateState != null" >
            and r.teminate_state = #{teminateState}
        </if>
        order by
        <if test='type == "1"' >
            r.audit_date desc
        </if>
        <if test='type == "2"' >
            r.audit_date asc
        </if>
        <if test='type == "3"' >
            r.file_sn asc
        </if>
        <if test='type == "4"' >
            r.file_sn desc
        </if>
    </select>

    <select id="getFileByCategoryAndUserIdAndapproveStatus" resultType="resource">
        select
            r.id, r.category, r.name, r.org, r.category_path AS categoryPath, r.file_sn AS fileSn, r.reason, r.path, r.size, r.change_num AS changeNum,
            r.download_num AS downloadNum,  r.view_num AS viewNum,  r.move_num AS moveNum,
            r.modify_num AS modifyNum, r.version, r.keyword, r.valid, r.is_stick AS isStick, r.operator,
            r.opertator_name AS opertatorName, DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
            r.verifier, r.verifier_name AS verifierName,
            DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate, r.auditor, r.audit_name AS auditName,
            DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, r.approve_status AS approveStatus,
            r.approver, r.approver_name AS approverName,
            DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate, r.operation, r.apply_memo AS applyMemo,
            r.approve_memo AS approveMemo, r.memo, r.creator, r.create_name AS createName,
            DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
            r.updator, r.update_name AS updateName,
            DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,r.message_id AS messageId, r.content,
            r.total_size AS totalSize, r.is_trash AS isTrash
        from t_resource r, t_resource_acl ra
        where r.id = ra.resource and r.category = #{category} and ra.user = #{user} and r.approve_status = 2
    </select>

    <select id="getAllFileByListCategory" resultType="resource">
        select
        id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
        version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
        DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
        DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
        approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
        where category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid = 1
    </select>

    <select id="fileByManagerlistPage" parameterType="hashmap" resultType="resource">
        select
        id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
        version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
        DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
        DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
        approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash,
        teminate_state AS teminateState, DATE_FORMAT(teminate_time,'%Y-%m-%d') AS teminateTime, teminater,
        teminater_name AS teminaterName
        from t_resource
        where category = #{category} and approve_status = 2 and valid = 1
        <if test="teminateState != null" >
            and teminate_state = #{teminateState}
        </if>
        order by
        <if test='type == "1"' >
            audit_date desc
        </if>
        <if test='type == "2"' >
            audit_date asc
        </if>
        <if test='type == "3"' >
            file_sn asc
        </if>
        <if test='type == "4"' >
            file_sn desc
        </if>
    </select>

    <select id="searchfilelistPage" resultType="resource">
        select
        r.id, r.category, r.name, r.org, r.category_path AS categoryPath, r.file_sn AS fileSn, r.reason, r.path, r.size, r.change_num AS changeNum,
        r.download_num AS downloadNum,  r.view_num AS viewNum,  r.move_num AS moveNum,
        r.modify_num AS modifyNum, r.version, r.keyword, r.valid, r.is_stick AS isStick, r.operator,
        r.opertator_name AS opertatorName, DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
        r.verifier, r.verifier_name AS verifierName,
        DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate, r.auditor, r.audit_name AS auditName,
        DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, r.approve_status AS approveStatus,
        r.approver, r.approver_name AS approverName,
        DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate, r.operation, r.apply_memo AS applyMemo,
        r.approve_memo AS approveMemo, r.memo, r.creator, r.create_name AS createName,
        DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        r.updator, r.update_name AS updateName,
        DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,r.message_id AS messageId, r.content,
        r.total_size AS totalSize, r.is_trash AS isTrash
        from t_resource r, t_resource_acl ra
        where r.id = ra.resource and ra.user = #{user} and r.approve_status = 2 and r.valid = 1 and r.teminate_state != 1
        and (r.name like CONCAT('%',#{name},'%') or r.file_sn like CONCAT('%',#{name},'%')) and r.category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="creator != null" >
            and r.creator = #{creator}
        </if>
        order by
        <if test='type == "1"' >
            locate( #{name},r.name)>0 DESC
        </if>
        <if test='type == "2"' >
            r.audit_date asc
        </if>
        <if test='type == "3"' >
            r.audit_date desc
        </if>
    </select>

    <select id="searchFilelistPage" resultType= "resource">
        select
        r.id, r.category, r.name, r.org, r.category_path AS categoryPath, r.file_sn AS fileSn, r.reason, r.path, r.size, r.change_num AS changeNum,
        r.download_num AS downloadNum,  r.view_num AS viewNum,  r.move_num AS moveNum,
        r.modify_num AS modifyNum, r.version, r.keyword, r.valid, r.is_stick AS isStick, r.operator,
        r.opertator_name AS opertatorName, DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
        r.verifier, r.verifier_name AS verifierName,
        DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate, r.auditor, r.audit_name AS auditName,
        DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, r.approve_status AS approveStatus,
        r.approver, r.approver_name AS approverName,
        DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate, r.operation, r.apply_memo AS applyMemo,
        r.approve_memo AS approveMemo, r.memo, r.creator, r.create_name AS createName,
        DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        r.updator, r.update_name AS updateName,
        DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,r.message_id AS messageId, r.content,
        r.total_size AS totalSize, r.is_trash AS isTrash
        from t_resource r, t_resource_acl ra
        where r.id = ra.resource and ra.user = #{user} and r.approve_status = 2 and r.valid = 1 and r.teminate_state != 1
        <if test="name != null" >
            and r.name like CONCAT('%',#{name},'%')
        </if>
        <if test="fileSn != null" >
            and r.file_sn like CONCAT('%',#{fileSn},'%')
        </if>
        <if test="version != null" >
            and r.version = #{version}
        </if>
        <if test="createName != null" >
            and r.create_name like CONCAT('%',#{createName},'%')
        </if>
        <if test="createDate != null" >
            and r.create_date between #{createDateBegin} and #{createDateEnd}
        </if>
        <if test="updateName != null" >
            and r.update_name like CONCAT('%',#{updateName},'%')
        </if>
        <if test="updateDate != null" >
            and r.update_date between #{updateDateBegin} and #{updateDateEnd}
        </if>
        and r.category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by locate( #{name}, r.name)>0 DESC
    </select>

    <select id="searchfileByAuthlistPage" resultType="resource">
        select
        id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
        version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
        DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
        DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
        approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
        where approve_status = 2 and (name like CONCAT('%',#{name},'%') or file_sn like
        CONCAT('%',#{name},'%')) and valid = 1 and teminate_state != 1
        <if test="creator != null" >
            and creator = #{creator}
        </if>
        and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by
        <if test='type == "1"' >
            locate( #{name},`name`)>0 DESC
        </if>
        <if test='type == "2"' >
            audit_date asc
        </if>
        <if test='type == "3"' >
            audit_date desc
        </if>
    </select>

    <select id="searchFileByAuthlistPage" resultType="resource">
        select
        id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
        version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
        DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
        DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
        approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
        where approve_status = 2 and valid = 1 and teminate_state != 1
        <if test="name != null" >
            and name like CONCAT('%',#{name},'%')
        </if>
        <if test="fileSn != null" >
            and file_sn like CONCAT('%',#{fileSn},'%')
        </if>
        <if test="version != null" >
            and version = #{version}
        </if>
        <if test="createName != null" >
            and create_name like CONCAT('%',#{createName},'%')
        </if>
        <if test="createDate != null" >
            and create_date between #{createDateBegin} and #{createDateEnd}
        </if>
        <if test="updateName != null" >
            and update_name like CONCAT('%',#{updateName},'%')
        </if>
        <if test="updateDate != null" >
            and update_date between #{updateDateBegin} and #{updateDateEnd}
        </if>
        and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by locate( #{name},`name`)>0 DESC
    </select>

    <select id="searchFileByHeadNum" resultType="resource">
        select
        id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
        version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
        DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
        DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
        approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
        where approve_status = 2 and file_sn like CONCAT(#{FileSn},'%') and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid = 1
        order by
        audit_date desc
    </select>

    <select id="searchfile" resultType="resource">
        select
        id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
        version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
        DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
        DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
        approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
        where approve_status = 2 and (name like CONCAT('%',#{name},'%') or file_sn like
        CONCAT('%',#{name},'%')) and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid = 1
    </select>

    <select id="searchfileByAuth" resultType="resource">
        select
        r.id, r.category, r.name, r.org, r.category_path AS categoryPath, r.file_sn AS fileSn, r.reason, r.path, r.size, r.change_num AS changeNum,
        r.download_num AS downloadNum,  r.view_num AS viewNum,  r.move_num AS moveNum,
        r.modify_num AS modifyNum, r.version, r.keyword, r.valid, r.is_stick AS isStick, r.operator,
        r.opertator_name AS opertatorName, DATE_FORMAT(r.operate_date,'%Y-%m-%d') AS operateDate,
        r.verifier, r.verifier_name AS verifierName,
        DATE_FORMAT(r.verify_date,'%Y-%m-%d') AS verifyDate, r.auditor, r.audit_name AS auditName,
        DATE_FORMAT(r.audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, r.approve_status AS approveStatus,
        r.approver, r.approver_name AS approverName,
        DATE_FORMAT(r.approve_date,'%Y-%m-%d') AS approveDate, r.operation, r.apply_memo AS applyMemo,
        r.approve_memo AS approveMemo, r.memo, r.creator, r.create_name AS createName,
        DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        r.updator, r.update_name AS updateName,
        DATE_FORMAT(r.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,r.message_id AS messageId, r.content,
        r.total_size AS totalSize, r.is_trash AS isTrash
        from t_resource r, t_resource_acl ra
        where r.id = ra.resource and ra.user = #{user} and r.approve_status = 2 and r.valid = 1
        and (r.name like CONCAT('%',#{name},'%') or r.file_sn like CONCAT('%',#{name},'%')) and r.category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="checckFileSnByFile" resultType="java.lang.Integer">
        select
        COUNT(id) num
        from t_resource
        where file_sn = #{fileSn} and is_deleted = 0 and category IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getFileNum" resultType="resCat">
        select
        COUNT(id) leafs
        from t_resource
        where
        <if test="org != null" >
            org = #{org}
        </if>
        <if test="categoryPath != null" >
            category_path like CONCAT(#{categoryPath},'%')
        </if>
        and valid = 1
    </select>

    <select id="getFileSize"  resultType="java.lang.Long">
        select
        SUM(size) size
        from t_resource
        where
        <if test="org != null" >
            org = #{org}
        </if>
        <if test="categoryPath != null" >
            category_path like CONCAT(#{categoryPath},'%')
        </if>
        and valid = 1
    </select>

    <select id="getFileByValid" resultType="resource">
        select
        id, category, name, org, category_path AS categoryPath, file_sn AS fileSn, reason, path, size, change_num AS changeNum,
        download_num AS downloadNum, view_num AS viewNum,  move_num AS moveNum,  modify_num AS modifyNum,
        version, keyword, valid, is_stick AS isStick, operator, opertator_name AS opertatorName,
        DATE_FORMAT(operate_date,'%Y-%m-%d') AS operateDate, verifier, verifier_name AS verifierName,
        DATE_FORMAT(verify_date,'%Y-%m-%d') AS verifyDate, auditor, audit_name AS auditName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, approve_status AS approveStatus, approver,
        approver_name AS approverName, DATE_FORMAT(approve_date,'%Y-%m-%d') AS approveDate,
        operation, apply_memo AS applyMemo, approve_memo AS approveMemo, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        updator, update_name AS updateName, DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        message_id AS messageId, content, total_size AS totalSize, is_trash AS isTrash
        from t_resource
        where valid = 0 and is_deleted = 0
        <if test="org != null" >
            and org = #{org}
        </if>
        <if test="deleteTime != null" >
            and delete_time &lt; #{deleteTime}
        </if>
    </select>

    <select id="getFileByValidNum" resultType="java.lang.Integer">
        select COUNT(id) num
        from t_resource
        where valid = 0 and is_deleted = 0 and org = #{org}
    </select>

    <select id="getResNumDtoByCategoryPath"  resultType="cn.sphd.miners.modules.resourceAuthority.dto.ResNumDto">
        SELECT
            count(case when  change_num != 0 then 1 end) as  changeNum,
            count(case when  operation = 3 then 1 end) as operationNum,
            count(case when  teminate_state = 1 then 1 end) as teminateNum
        FROM
            t_resource
        WHERE
            category_path LIKE CONCAT(#{categoryPath},'%')
    </select>

    <delete id="delete" parameterType="resource" >
        delete from t_resource
        where id = #{id}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="resource" >
        insert into t_resource (category, name,
                                org, category_path,
                                file_sn, reason, path,
                                size, change_num, download_num,
                                view_num,  move_num,  modify_num,
                                version, keyword, is_trash, valid,
                                is_stick, operator, opertator_name,
                                operate_date, verifier, verifier_name,
                                verify_date, auditor, audit_name, audit_date,
                                approve_status, approver, approver_name,
                                approve_date, operation, apply_memo,
                                approve_memo, memo, creator,
                                create_name, create_date, updator,
                                update_name, update_date, message_id,
                                content, total_size)
        values (#{category}, #{name}, #{org}, #{categoryPath}, #{fileSn}, #{reason}, #{path}, #{size}, #{changeNum}, #{downloadNum},
                #{viewNum}, #{moveNum}, #{modifyNum}, #{version}, #{keyword}, #{isTrash}, #{valid}, #{isStick}, #{operator},
                #{opertatorName}, #{operateDate}, #{verifier}, #{verifierName}, #{verifyDate}, #{auditor},
                #{auditName}, #{auditDate}, #{approveStatus}, #{approver}, #{approverName}, #{approveDate},
                #{operation}, #{applyMemo}, #{approveMemo}, #{memo}, #{creator}, #{createName}, #{createDate},
                #{updator}, #{updateName}, #{updateDate}, #{messageId}, #{content}, #{totalSize})
    </insert>

    <update id="update" parameterType="resource" >
        update t_resource
        <set >
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="categoryPath != null" >
                category_path = #{categoryPath},
            </if>
            <if test="fileSn != null" >
                file_sn = #{fileSn},
            </if>
            <if test="reason != null" >
                reason = #{reason},
            </if>
            <if test="path != null" >
                path = #{path},
            </if>
            <if test="size != null" >
                size = #{size},
            </if>
            <if test="changeNum != null" >
                change_num = #{changeNum},
            </if>
            <if test="downloadNum != null" >
                download_num = #{downloadNum},
            </if>
            <if test="viewNum != null" >
                view_num = #{viewNum},
            </if>
            <if test="moveNum != null" >
                move_num = #{moveNum},
            </if>
            <if test="modifyNum != null" >
                modify_num = #{modifyNum},
            </if>
            <if test="isTrash != null" >
                is_trash = #{isTrash},
            </if>
            <if test="isDeleted != null" >
                is_deleted = #{isDeleted},
            </if>
            <if test="deleteTime != null" >
                delete_time = #{deleteTime},
            </if>
            <if test="version != null" >
                version = #{version},
            </if>
            <if test="keyword != null" >
                keyword = #{keyword},
            </if>
            <if test="valid != null" >
                valid = #{valid},
            </if>
            <if test="isStick != null" >
                is_stick = #{isStick},
            </if>
            <if test="operator != null" >
                operator = #{operator},
            </if>
            <if test="opertatorName != null" >
                opertator_name = #{opertatorName},
            </if>
            <if test="operateDate != null" >
                operate_date = #{operateDate},
            </if>
            <if test="verifier != null" >
                verifier = #{verifier},
            </if>
            <if test="verifierName != null" >
                verifier_name = #{verifierName},
            </if>
            <if test="verifyDate != null" >
                verify_date = #{verifyDate},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditName != null" >
                audit_name = #{auditName},
            </if>
            <if test="auditDate != null"          >
                audit_date = #{auditDate},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approver != null" >
                approver = #{approver},
            </if>
            <if test="approverName != null" >
                approver_name = #{approverName},
            </if>
            <if test="approveDate != null" >
                approve_date = #{approveDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="content != null" >
                content = #{content},
            </if>
            <if test="totalSize != null" >
                total_size = #{totalSize},
            </if>
            <if test="teminateState != null" >
                teminate_state = #{teminateState},
            </if>
            <if test="teminateTime != null" >
                teminate_time = #{teminateTime},
            </if>
            <if test="teminater != null" >
                teminater = #{teminater},
            </if>
            <if test="teminaterName != null" >
                teminater_name = #{teminaterName},
            </if>
            <if test="lockUuid != null" >
                lock_uuid = #{lockUuid},
            </if>
            <if test="batchUuid != null" >
                batch_uuid = #{batchUuid},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateResourceCategoryPath" parameterType="resource" >
        update t_resource
        <set >
            <if test="categoryPath != null" >
                category_path = #{categoryPath},
            </if>
            <if test="isTrash != null" >
                is_trash = #{isTrash},
            </if>
        </set>
        where category = #{category}
    </update>

    <update id="reuserFile">
        update
            t_resource
        set
            teminate_state = 0,
            teminate_time = null,
            teminater = null,
            teminater_name = null,
            updator = #{updator},
            update_name = #{updateName},
            update_date = #{updateDate},
            audit_date = #{auditDate}
        where
            id = #{id}
    </update>

</mapper>