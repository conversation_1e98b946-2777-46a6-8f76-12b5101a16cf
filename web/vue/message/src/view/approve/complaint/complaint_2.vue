<!-- 待结案 -->
<template>
  <div class="ui-cells">
    <p v-if="he === 1"> 目前有以下{{ list2.length }}个待结案的投诉</p>
    <p v-if="he === 2"> 目前您负责的投诉中，以下{{ list2.length }}个尚未提交结案申请</p>
    <a class="ui-cell" @click="jump(item.id)" v-for="(item, index) in list2" v-bind:key="index">
      <div class="ui-cell__bd">
        <div class="ui-cell_con">
          <div class="cusName">{{item.customName}}</div>
          <div><span class="info">接到投诉的日期</span><span class="info">{{item.receiptTime | formatDate('yyyy-MM-dd')}}</span></div>
          <div><span class="info">录入者</span><span class="info">{{item.createName}} {{item.createTime | formatDate}}</span></div>
        </div>
      </div>
      <div class="ui-cell__ft"></div>
    </a>
  </div>
</template>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'complaintApproval2',
  filters: {
    formatDate
  },
  data () {
    return {
      list: this.$store.getters.getComplaintList,
      he: this.$store.getters.getComplaintHe, // 1 - 核心人物 2 - 非核心人物
      listeners: [],
      list2: [] // 待审批
    }
  },
  created () {
    let _this = this
    if (_this.list) {
      _this.list2 = _this.list['listPendingComplaintEnd'] || []
    }
    _this.$store.dispatch('setNewComplaintNav', 2)
    _this.listeners.push(_this.sphdSocket.subscribe('pendingComplaintEnd', _this.callBack, null, 'user'))
  },
  methods: {
    jump: function (id) {
      let he = this.$store.getters.getComplaintHe
      let t = he === 1 ? 3 : 4
      window.parent.floatToPage('../../../complaint/complaintInfo.do', { 'id': id, 't': t })
    },
    callBack: function (res) {
      let _this = this
      console.log('带结案：')
      let curUserID = _this.sphdSocket.user.userID
      let he = this.$store.getters.getComplaintHe
      _this.$store.dispatch('setNewComplaintList', { 'userID': curUserID, 'role': he }).then((result) => {
        _this.loading = false
      })
    }
  },
  watch: {
    '$store.state.complaintList': {
      immediate: true,
      deep: true,
      handler (complaintList) {
        if (complaintList) {
          this.list = complaintList
          this.list2 = this.list['listPendingComplaintEnd'] || []
        }
      }
    }
  }
}
</script>
