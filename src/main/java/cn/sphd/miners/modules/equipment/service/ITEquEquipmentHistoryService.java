package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquEquipmentHistory;

import java.util.List;
import java.util.Map;

/**
 * 装备管理_装备器具历史
 * 20230209 1.247装备器具新增
 * Service接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public interface ITEquEquipmentHistoryService {
    /**
     * 查询装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_装备器具历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     */
    public TEquEquipmentHistory selectTEquEquipmentHistoryById(Long id);

    /**
     * 查询装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     * 列表
     *
     * @param tEquEquipmentHistory 装备管理_装备器具历史
     *                             20230209 1.247装备器具新增
     * @return 装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     * 集合
     */
    public List<TEquEquipmentHistory> selectTEquEquipmentHistoryList(TEquEquipmentHistory tEquEquipmentHistory);

    /**
     * 新增装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipmentHistory 装备管理_装备器具历史
     *                             20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquEquipmentHistory(TEquEquipmentHistory tEquEquipmentHistory);

    /**
     * 修改装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipmentHistory 装备管理_装备器具历史
     *                             20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquEquipmentHistory(TEquEquipmentHistory tEquEquipmentHistory);

    /**
     * 批量删除装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_装备器具历史
     *            20230209 1.247装备器具新增
     *            主键集合
     * @return 结果
     */
    public int deleteTEquEquipmentHistoryByIds(String... ids);

    /**
     * 删除装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     * 信息
     *
     * @param id 装备管理_装备器具历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 结果
     */
    public int deleteTEquEquipmentHistoryById(Long id);

    List<Map<String, Object>> history(Long id);

}
