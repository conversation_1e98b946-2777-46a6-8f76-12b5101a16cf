package cn.sphd.miners.modules.resourceAuthority.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResDepartAndEmployee;
import org.springframework.stereotype.Component;

import java.util.List;

import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2017/10/31.
 */
@Component
public interface ResCategoryMapper extends BaseMapper<ResCategory> {

    //获取parent是null的文件夹(带权限)
    List<ResCategory> getCategoryByParent(ResCategory rc);

    //获取某机构一级文件夹下全部子级文件夹信息
    List<ResCategory> getFirstCategoryByAllChild(HashMap map);

    //获取某文件夹的子级文件夹(带权限)
    List<ResCategory> getChildCategory(ResCategory rc);

    //获取某文件夹的子级文件夹ids
    List<Integer> getChildCategoryIds(Integer parent);

    //根据oid和名字查询一级文件夹
    ResCategory getCategoryByNameAndOid(ResCategory rc);

    //根据creator、type和名字查询一级文件夹
    ResCategory getCategoryByNameAndOidAndType(ResCategory rc);

    //根据oid和名字查询父级文件夹不是null的文件夹
    ResCategory getCategoryByNameAndOidAndParent(ResCategory rc);

    //根据creator、type和名字查询父级文件夹不是null的文件夹
    ResCategory getCategoryByNameAndOidAndParentAndType(ResCategory rc);

    //根据oid获取type为1的全部文件夹
    List<ResCategory> getAllCategoryByOidAndType(Integer oid);

    //根据oid获取type是1的第一级的文件夹
    List<ResCategory> getAllCategoryByOidAndTypeForFirst(Integer oid);

    //根据oid获取type是1的第一级的文件夹但是不包括回收站
    List<ResCategory> getOrdinaryFirstCategory(Integer oid);

    //根据oid获取type是1的全部文件夹
    List<ResCategory> getAllCategory(Integer oid);

    //根据oid获取type是1的回收站文件夹
    List<ResCategory> getTrashCategory(Integer oid);

    //根据oid获取type为1的全部文件夹
    List<ResCategory> getAllFirstCategoryByOid(ResCategory rc);

    //不要权限只根据parent获取子文件夹
    List<ResCategory> getAllChildCategoryByParent(Integer parent);

    //不要权限只根据parent返回子文件夹的个数
    Integer getAllChildCategoryNumByParent(Integer parent);

    //根据parent获取其中一个子文件夹
    ResCategory getOneCategoryByParentlistPage(QueryData query);

    //根据parent等信息获取其中一个子文件夹(带权限)
    ResCategory getOneCategoryByParentAuthlistPage(QueryData query);

    //根据名字模糊查找文件夹
    List<ResCategory> searchFolderlistPage(QueryData query);

    //根据名字模糊查找文件夹（有权限）
    List<ResCategory> searchFolderByAuthlistPage(QueryData query);

    List<ResDepartAndEmployee> getDepartAndEmployee(int oid);

    ResDepartAndEmployee getDepart(int oid);

    List<ResDepartAndEmployee> getDepartment(int pid);

    List<ResDepartAndEmployee> getEmployee(int departid);

    List<ResDepartAndEmployee> getEmployeeOther(int oid);
}

