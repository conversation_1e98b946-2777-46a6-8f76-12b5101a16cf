package cn.sphd.miners.modules.investigate.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-08-01 
 */

@Entity ( name ="InvestigateSubjectHistory" )
@Table ( name ="t_investigate_subject_history" )
public class InvestigateSubjectHistory  implements Serializable {

	private static final long serialVersionUID =  1534810706711533889L;

	/**
	 * ID
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 问卷ID
	 */
   	@Column(name = "subject" )
	private Integer subject;

	/**
	 * 调查类型:自定义
	 */
   	@Column(name = "category" )
	private String category;

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 代码
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 页眉
	 */
   	@Column(name = "header" )
	private String header;

	/**
	 * 标题
	 */
   	@Column(name = "title" )
	private String title;

	/**
	 * 复制源
	 */
   	@Column(name = "source" )
	private Integer source;

	/**
	 * 问题数
	 */
   	@Column(name = "questions_num" )
	private Integer questionsNum;

	/**
	 * 必填题数
	 */
   	@Column(name = "required_num" )
	private Integer requiredNum;

	/**
	 * 是否启用
	 */
   	@Column(name = "enabled" )
	private Integer enabled;

	/**
	 * 启/停用时间
	 */
   	@Column(name = "enabled_time" )
	private Date enabledTime;

	/**
	 * 排序
	 */
   	@Column(name = "orders" )
	private Integer orders;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getSubject() {
		return this.subject;
	}

	public void setSubject(Integer subject) {
		this.subject = subject;
	}

	public String getCategory() {
		return this.category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getHeader() {
		return this.header;
	}

	public void setHeader(String header) {
		this.header = header;
	}

	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getSource() {
		return this.source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public Integer getQuestionsNum() {
		return this.questionsNum;
	}

	public void setQuestionsNum(Integer questionsNum) {
		this.questionsNum = questionsNum;
	}

	public Integer getRequiredNum() {
		return this.requiredNum;
	}

	public void setRequiredNum(Integer requiredNum) {
		this.requiredNum = requiredNum;
	}

	public Integer getEnabled() {
		return this.enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Date getEnabledTime() {
		return this.enabledTime;
	}

	public void setEnabledTime(Date enabledTime) {
		this.enabledTime = enabledTime;
	}

	public Integer getOrders() {
		return this.orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
