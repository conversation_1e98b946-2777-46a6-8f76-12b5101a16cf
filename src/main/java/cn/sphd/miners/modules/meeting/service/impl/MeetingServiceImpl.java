package cn.sphd.miners.modules.meeting.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.meeting.dao.*;
import cn.sphd.miners.modules.meeting.entity.*;
import cn.sphd.miners.modules.meeting.service.MeetingService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson2.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service("meetingService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MeetingServiceImpl implements MeetingService {


    @Autowired
    MeetingInfoDao meetingInfoDao;
    @Autowired
    MeetingImageDao meetingImageDao;
    @Autowired
    MeetingParticipantDao meetingParticipantDao;
    @Autowired
    MeetingQuestionDao meetingQuestionDao;
    @Autowired
    MeetingInfoHistoryDao meetingInfoHistoryDao;
    @Autowired
    MeetingQuestionHistoryDao meetingQuestionHistoryDao;
    @Autowired
    MeetingParticipantHistoryDao meetingParticipantHistoryDao;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    UploadService uploadService;
    @Autowired
    MeetingResolutionDao meetingResolutionDao;
    @Autowired
    MeetingSpeakDao meetingSpeakDao;

    @Override
    public List<MeetingInfo> getMeetingInfoListByUserId(Integer userId) {
        String hql="from MeetingInfo where state in(0,1) and (creator=:userId or id in(select meetingId from MeetingParticipant where user=:userId)) and planTime>:beginDate";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("beginDate",NewDateUtils.changeDay(new Date(),0));
        List<MeetingInfo> meetingInfos=meetingInfoDao.getListByHQLWithNamedParams(hql+" order by planTime desc",map);
        return meetingInfos;
    }

    @Override
    public void handleMeetingExpire(Integer oid) {
        String hql="from MeetingInfo where state in(0,1) and org=:oid and planTime<=:beginDate";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("beginDate",NewDateUtils.changeDay(new Date(),0));
        List<MeetingInfo> meetingInfos=meetingInfoDao.getListByHQLWithNamedParams(hql,map);
        if (meetingInfos.size()>0) {
            for (MeetingInfo meetingInfo : meetingInfos) {
                meetingInfo.setState(3);  // //状态:0-未开始,1-进行中,2-已结束,3-已过期 , 4-取消会议，  -1 修改中  ,5-实际未召开
//            meetingInfo.setFinishTime(new Date());  //过期时， 结束时间存过期时间
                meetingInfoDao.update(meetingInfo);
            }

            hql = " from MeetingInfoHistory where state in(0,1) and org=:oid and planTime<=:beginDate and id in(select business from ApprovalProcess where approveStatus='1' and org=:oid and businessType=:businessType)";
            map.put("businessType", 76);
            List<MeetingInfoHistory> meetingInfoHistoryList = meetingInfoHistoryDao.getListByHQLWithNamedParams(hql, map);
            Map<Integer, MeetingInfoHistory> infoMap = new HashMap<>();
            List<Integer> idList=new ArrayList<>();
            for (MeetingInfoHistory meetingInfoHistory : meetingInfoHistoryList) {
                infoMap.put(meetingInfoHistory.getId().intValue(), meetingInfoHistory);
                idList.add(meetingInfoHistory.getId().intValue());
            }

            Map<Integer, User> userMap = new HashMap<>();
            List<User> userList = userService.getPresentUsers(oid);
            for (User u : userList) {
                userMap.put(u.getUserID(), u);
            }

            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByOidBusinessType(oid, 76,idList);
            for (ApprovalProcess approvalProcess : approvalProcessList) {
                approvalProcess.setApproveStatus("3");
                approvalProcessService.updateApprovalProcess(approvalProcess);
                if(userMap.get(approvalProcess.getToUser())!=null){
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("ap", approvalProcess);
                    hashMap.put("meetingInfo", infoMap.get(approvalProcess.getBusiness()));
                    swMessageService.rejectSend(-1, -1, hashMap, approvalProcess.getToUser().toString(), "/meetingSignApprovalList", null, null, userMap.get(approvalProcess.getToUser()), "meetingSignApproval");
                }
            }
        }
    }

    @Override
    public void saveMeetingInfo(User user, Integer[] attendees, List<String> questions, Integer recorderUserId,String topic, Integer planDuration, String planTime, Integer pattern, String matters) {
        MeetingInfo meetingInfo=new MeetingInfo();
        meetingInfo.setTopic(topic);
        meetingInfo.setPlanDuration(planDuration);
        meetingInfo.setPattern(pattern);
        meetingInfo.setMatters(matters);
        meetingInfo.setCreateDate(new Date());
        meetingInfo.setCreateName(user.getUserName());
        meetingInfo.setCreator(user.getUserID());
        meetingInfo.setAuditName(user.getUserName());// 文官姓名
        meetingInfo.setOrg(user.getOid());
        meetingInfo.setAuditor(user.getUserID());// 文官id
        meetingInfo.setPlanTime(NewDateUtils.dateFromString(planTime,"yyyy-MM-dd HH:mm:ss"));
        meetingInfo.setQuestionNum(questions.size()); //问题数
        meetingInfoDao.save(meetingInfo);

        for (Integer userId:attendees){  //参会者
            saveMeetingParticipant(meetingInfo,userId,1);
        }

        saveMeetingParticipant(meetingInfo,recorderUserId,2); // 记录者

        for (String question:questions){
            saveMeetingQuestion(meetingInfo,question);
        }

        // 历史记录
        MeetingInfoHistory meetingInfoHistory= saveMeetingInfoHistory(meetingInfo); // 会议历史
        for (Integer userId:attendees){  //参会者
            saveMeetingParticipantHistory(meetingInfoHistory,userId,1);
        }
        saveMeetingParticipantHistory(meetingInfoHistory,recorderUserId,2); // 记录者

        for (String question:questions){
            saveMeetingQuestionHistory(meetingInfoHistory,question);
        }
    }

    MeetingParticipant saveMeetingParticipant(MeetingInfo meetingInfo,Integer userId,Integer type){
        User attUser=userService.getUserByID(userId);
        MeetingParticipant meetingParticipant=new MeetingParticipant();
        BeanUtils.copyProperties(meetingInfo, meetingParticipant);
        meetingParticipant.setUser(attUser.getUserID());
        meetingParticipant.setUserName(attUser.getUserName());
        meetingParticipant.setType(type);
        meetingParticipant.setMeetingInfo(meetingInfo);
        meetingParticipant.setCreateDate(new Date());
        meetingParticipantDao.save(meetingParticipant);
        return meetingParticipant;
    }

    MeetingQuestion saveMeetingQuestion(MeetingInfo meetingInfo,String question){
        MeetingQuestion meetingQuestion=new MeetingQuestion();
        BeanUtils.copyProperties(meetingInfo, meetingQuestion);
        meetingQuestion.setContent(question);
        meetingQuestion.setMeetingInfo(meetingInfo);
        meetingQuestionDao.save(meetingQuestion);
        return meetingQuestion;
    }

    MeetingInfoHistory saveMeetingInfoHistory(MeetingInfo meetingInfo){
        MeetingInfoHistory meetingInfoHistory=new MeetingInfoHistory();
        BeanUtils.copyProperties(meetingInfo, meetingInfoHistory);
        meetingInfoHistory.setMeetingInfo(meetingInfo);
        meetingInfoHistoryDao.save(meetingInfoHistory);
        return meetingInfoHistory;
    }

    MeetingParticipantHistory saveMeetingParticipantHistory(MeetingInfoHistory meetingInfoHistory, Integer userId, Integer type){
        User attUser=userService.getUserByID(userId);
        MeetingParticipantHistory meetingParticipantHistory=new MeetingParticipantHistory();
        BeanUtils.copyProperties(meetingInfoHistory, meetingParticipantHistory);
        meetingParticipantHistory.setUser(attUser.getUserID());
        meetingParticipantHistory.setUserName(attUser.getUserName());
        meetingParticipantHistory.setType(type);
        meetingParticipantHistory.setMeetingInfoHistory(meetingInfoHistory);
        meetingParticipantHistoryDao.save(meetingParticipantHistory);
        return meetingParticipantHistory;
    }

    MeetingQuestionHistory saveMeetingQuestionHistory(MeetingInfoHistory meetingInfoHistory,String question){
        MeetingQuestionHistory meetingQuestionHistory=new MeetingQuestionHistory();
        BeanUtils.copyProperties(meetingInfoHistory, meetingQuestionHistory);
        meetingQuestionHistory.setContent(question);
        meetingQuestionHistory.setMeetingInfoHistory(meetingInfoHistory);
        meetingQuestionHistoryDao.save(meetingQuestionHistory);
        return meetingQuestionHistory;
    }

    @Override
    public void saveMeetingInfoHistory(User user, Integer[] attendees, List<String> questions, Integer recorderUserId, Integer approver, String topic, Integer planDuration, String planTime, Integer pattern, String matters, Long meetingId, String memo, String address) {

        MeetingInfoHistory meetingInfoHistory=new MeetingInfoHistory();

        if (meetingId!=null){
            MeetingInfo meetingInfo=meetingInfoDao.get(meetingId);
            meetingInfoHistory.setMeetingInfo(meetingInfo); // 如果是修改 直接有主会议id
            meetingInfo.setState(-1); // -1修改中
            meetingInfoDao.update(meetingInfo);
        }
        meetingInfoHistory.setTopic(topic);
        meetingInfoHistory.setPlanDuration(planDuration);
        meetingInfoHistory.setPattern(pattern);
        meetingInfoHistory.setMatters(matters);
        meetingInfoHistory.setMemo(memo);
        meetingInfoHistory.setAddress(address);
        meetingInfoHistory.setCreateDate(new Date());
        meetingInfoHistory.setCreateName(user.getUserName());
        meetingInfoHistory.setCreator(user.getUserID());
        meetingInfoHistory.setOrg(user.getOid());
        meetingInfoHistory.setParticipantNum(attendees.length);  //参与人数
        meetingInfoHistory.setQuestionNum(questions.size()); //问题数
        meetingInfoHistory.setPlanTime(NewDateUtils.dateFromString(planTime,"yyyy-MM-dd HH:mm:ss"));

        //审批信息
        Integer businessType=74;
        String destination="/meetingMiddleApprovalList";
        String tCode="meetingMiddleApproval";
        User approverUser=new User();
        if (approver==null){  //没有选择审批者，选择了直接提交给文官
            approverUser=userService.getUserByRoleCode(user.getOid(),"general");
            meetingInfoHistory.setAuditName(user.getUserName());// 文官姓名
            meetingInfoHistory.setAuditor(user.getUserID());// 文官id
            businessType=75;
            destination="/meetingFinalApprovalList";
            tCode="meetingFinalApproval";
        }else {
            approverUser=userService.getUserByID(approver);
        }
        meetingInfoHistory.setApprover(approver);
        meetingInfoHistory.setApproverName(approverUser.getUpdateName());
        meetingInfoHistoryDao.save(meetingInfoHistory);

        for (Integer userId:attendees){  //参会者
            if (userId.equals(recorderUserId)){
                saveMeetingParticipantHistory(meetingInfoHistory,recorderUserId,2); // 记录者
            }else {
                saveMeetingParticipantHistory(meetingInfoHistory, userId, 1);
            }
        }

        for (String question:questions){
            saveMeetingQuestionHistory(meetingInfoHistory,question);
        }

        String des=user.getUserName()+"的会议发起申请";
        ApprovalProcess approvalProcess= approvalProcessService.addApprovalProcess(meetingInfoHistory.getId().intValue(),businessType,des,1,user,approverUser,approverUser.getUserName());

        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("ap",approvalProcess);
        hashMap.put("meetingInfo",meetingInfoHistory);
        hashMap.put("status",1);
        swMessageService.rejectSend(1,1,hashMap,approverUser.getUserID().toString(),destination,des,des,approverUser,tCode);
    }

    @Override
    public List<MeetingInfoHistory> getMeetingInfoHistoriesByCreator(Integer creatorId) {
        String hql="from MeetingInfoHistory where approveStatus=1 and creator=:creatorId";
        Map<String,Object> map=new HashMap<>();
        map.put("creatorId",creatorId);
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingInfoHistoryDao.getListByHQLWithNamedParams(hql+" order by planTime asc",map);
        return meetingInfoHistoryList;
    }

    @Override
    public MeetingInfo getMeetingInfoById(Long id) {
        return meetingInfoDao.get(id);
    }

    @Override
    public MeetingInfoHistory getMeetingInfoHistoryById(Long id) {
        return meetingInfoHistoryDao.get(id);
    }

    @Override
    public MeetingInfoHistory getMeetingInfoHistoryByMeetingId(Long meetingId) {
        String hql="from MeetingInfoHistory where approveStatus=2 and meetingId=:meetingId order by id desc";
        Map<String,Object> map=new HashMap<>();
        map.put("meetingId",meetingId);
        MeetingInfoHistory meetingInfoHistory= (MeetingInfoHistory) meetingInfoHistoryDao.getByHQLWithNamedParams(hql,map);
        return meetingInfoHistory;
    }

    @Override
    public List<MeetingInfoHistory> getMeetingInfoHistoriesByMeetingId(Long meetingId,Integer approveStatus) {
        String hql="from MeetingInfoHistory where approveStatus=:approveStatus and meetingId=:meetingId";
        Map<String,Object> map=new HashMap<>();
        map.put("meetingId",meetingId);
        map.put("approveStatus",approveStatus);
        if (approveStatus==1){
            hql+=" order by planTime desc";
        }
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingInfoHistoryDao.getListByHQLWithNamedParams(hql,map);
        return meetingInfoHistoryList;
    }

    @Override
    public void withdrawMeetingApply(MeetingInfoHistory meetingInfoHistory, User loginUser) {
        meetingInfoHistory.setApproveStatus(5); //撤回
        meetingInfoHistory.setUpdateDate(new Date());
        meetingInfoHistory.setUpdator(loginUser.getUserID());
        meetingInfoHistory.setUpdateName(loginUser.getUserName());
        meetingInfoHistoryDao.update(meetingInfoHistory);

        List<ApprovalProcess> approvalProcessList=approvalProcessService.getApprovalProcessByBusiness(meetingInfoHistory.getId().intValue(),74,"1");
        for (ApprovalProcess approvalProcess:approvalProcessList){
            approvalProcess.setApproveStatus("4");
            String des=loginUser.getUserName()+"的会议申请被撤回";
            approvalProcess.setApproveMemo(des);
            approvalProcessService.updateApprovalProcess(approvalProcess);

            HashMap<String,Object> hashMap=new HashMap<>();
            hashMap.put("ap",approvalProcess);
            hashMap.put("meetingInfo",meetingInfoHistory);
            User approverUser=userService.getUserByID(approvalProcess.getToUser());

            swMessageService.rejectSend(-1,-1,hashMap,approverUser.getUserID().toString(),"/meetingMiddleApprovalList",des,des,approverUser,"meetingMiddleApproval");

        }
    }

    @Override
    public List<MeetingInfoHistory> getMeetingInfoHistoriesInIds(List<Integer> ids) {
        String hql="from MeetingInfoHistory where id in(:ids)";
        Map<String,Object> map=new HashMap<>();
        List<Long>  longList= JSONArray.parseArray(ids.toString(),Long.class);
        map.put("ids",longList);
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingInfoHistoryDao.getListByHQLWithNamedParams(hql+" order by planTime asc",map);
        return meetingInfoHistoryList;
    }

    @Override
    public JsonResult approvalMeetingMiddle(User user, Long id, Integer approvalStatus, Integer approvalUserId,String approveMemo) {
        ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessByBusToUser(id.intValue(),74,user.getUserID());
        MeetingInfoHistory meetingInfoHistory=meetingInfoHistoryDao.get(id);
        if ("1".equals(approvalProcess.getApproveStatus())){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            if (approvalStatus==1) {
                approvalProcess.setApproveStatus("2");

                User approverUser;
                Integer businessType = 74;
                String tCode="meetingMiddleApproval";
                String destination="meetingMiddleApprovalList";
                if (approvalUserId != null) {  // 选人了就是中间审批往下传
                    approverUser = userService.getUserByID(approvalUserId);
                } else {  //没选人就是 直接提交给文官
                    approverUser = userService.getUserByRoleCode(user.getOid(), "general");
                    businessType = 75;
                    tCode="meetingFinalApproval";
                    destination="meetingFinalApprovalList";
                }
                ApprovalProcess nextApprovalProcess = approvalProcessService.addApprovalProcess(id.intValue(), businessType, approvalProcess.getDescription(), approvalProcess.getLevel() + 1, user, approverUser, approverUser.getUserName());
                HashMap<String,Object> hashMap=new HashMap<>();
                hashMap.put("ap",nextApprovalProcess);
                hashMap.put("meetingInfo",meetingInfoHistory);
                swMessageService.rejectSend(1,1,hashMap,approverUser.getUserID().toString(),destination,approvalProcess.getDescription(),approvalProcess.getDescription(),approverUser,tCode);

            }else {
                approvalProcess.setApproveStatus("3");
                approvalProcess.setApproveMemo(approveMemo);
                approvalProcess.setReason(approveMemo);

//                MeetingInfoHistory meetingInfoHistory=meetingInfoHistoryDao.get(id);
                meetingInfoHistory.setApproveDate(new Date());
                meetingInfoHistory.setApproverName(user.getUserName());
                meetingInfoHistory.setApprover(user.getUserID());
                meetingInfoHistory.setApproveMemo(approveMemo);
                meetingInfoHistory.setUpdator(user.getUserID());
                meetingInfoHistory.setAuditDate(new Date());
                meetingInfoHistory.setApproveStatus(3);
                meetingInfoHistory.setTeminateReason(approveMemo);
                meetingInfoHistoryDao.update(meetingInfoHistory);

                userSuspendMsgService.saveUserSuspendMsg(1,"申请被驳回","您的会议发起申请被驳回了！","操作时间  "+user.getUserName()+" "+ sdf.format(approvalProcess.getHandleTime()),meetingInfoHistory.getCreator(),"meetingApply",meetingInfoHistory.getId().intValue());//推送我的消息

            }
            approvalProcess.setHandleTime(new Date());
            approvalProcessService.updateApprovalProcess(approvalProcess);

            HashMap<String,Object> hashMap=new HashMap<>();
            hashMap.put("ap",approvalProcess);
            hashMap.put("meetingInfo",meetingInfoHistory);
            swMessageService.rejectSend(-1,-1,hashMap,user.getUserID().toString(),"/meetingMiddleApprovalList",null,null,user,"meetingMiddleApproval");
            return new JsonResult(1,1);
        }else {
            return new JsonResult(new MyException("已处理 无需重复处理"));
        }
    }


    @Override
    public JsonResult approvalMeetingFinal(User user, Long id, Integer approvalStatus,String approveMemo) {
        ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessByBusToUser(id.intValue(),75,user.getUserID());
        if ("1".equals(approvalProcess.getApproveStatus())){

            MeetingInfoHistory meetingInfoHistory=meetingInfoHistoryDao.get(id);
            meetingInfoHistory.setApproveDate(new Date());
            meetingInfoHistory.setApproverName(user.getUserName());
            meetingInfoHistory.setApprover(user.getUserID());
            meetingInfoHistory.setUpdator(user.getUserID());
            meetingInfoHistory.setAuditDate(new Date());

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            if (approvalStatus==1) {

                MeetingInfo meetingInfo=meetingInfoHistory.getMeetingInfo();

                if (meetingInfo!=null){  //会议修改
                    meetingInfo.setState(0);// 改回会议未开始状态
//                    BeanUtils.copyProperties(meetingInfoHistory, meetingInfo);
                    meetingInfo.setTopic(meetingInfoHistory.getTopic());
                    meetingInfo.setPlanDuration(meetingInfoHistory.getPlanDuration());
                    meetingInfo.setPattern(meetingInfoHistory.getPattern());
                    meetingInfo.setMatters(meetingInfoHistory.getMatters());
                    meetingInfo.setPlanTime(meetingInfoHistory.getPlanTime());
                    meetingInfoDao.update(meetingInfo);

                    meetingParticipantDao.deleteAll(meetingInfo.getMeetingParticipantHashSet());
                    meetingQuestionDao.deleteAll(meetingInfo.getMeetingQuestionHashSet());

                    MeetingInfoHistory oldInfo=getMeetingInfoHistoryByMeetingId(meetingInfoHistory.getMeetingId());

                    for (MeetingParticipantHistory participantHistory:oldInfo.getMeetingParticipantHashSet()){
                        ApprovalProcess ap=approvalProcessService.getApprovalProcessByBusToUser(oldInfo.getId().intValue(),76,participantHistory.getUser());

                        if ("1".equals(ap.getApproveStatus())) {
                            String des="会议被修改，旧会议待签收变为无效";

                            ap.setApproveStatus("6"); //6-修改 原信息未处理的变无效
                            ap.setReason(des);
                            ap.setHandleTime(new Date());
                            approvalProcessService.updateApprovalProcess(approvalProcess);

                            HashMap<String,Object> hashMap=new HashMap<>();
                            hashMap.put("ap",approvalProcess);
                            hashMap.put("meetingInfo",meetingInfoHistory);
                            swMessageService.rejectSend(-1,-1,hashMap,participantHistory.getUser().toString(),"/meetingSignApprovalList",des,des,user,"meetingSignApproval");
                        }
                    }


                }else {                //会议新增 产生实际的会议
                    meetingInfo = new MeetingInfo();
                    BeanUtils.copyProperties(meetingInfoHistory, meetingInfo);
//                meetingInfo.setCreateDate(new Date()); //实际创建时间
                    meetingInfoDao.save(meetingInfo);
                    meetingInfoHistory.setMeetingInfo(meetingInfo);
                }

                for (MeetingQuestionHistory meetingQuestionHistory:meetingInfoHistory.getMeetingQuestionHashSet()){
                    saveMeetingQuestion(meetingInfo,meetingQuestionHistory.getContent());
                }

                for (MeetingParticipantHistory participantHistory:meetingInfoHistory.getMeetingParticipantHashSet()){

                    saveMeetingParticipant(meetingInfo,participantHistory.getUser(),participantHistory.getType());

                    User approverUser=userService.getUserByID(participantHistory.getUser());
                    String des="您有新会议待签收";
                    ApprovalProcess nextApprovalProcess = approvalProcessService.addApprovalProcess(id.intValue(), 76, des, 1, user, approverUser, approverUser.getUserName());
                    HashMap<String,Object> hashMap=new HashMap<>();
                    hashMap.put("ap",nextApprovalProcess);
                    hashMap.put("meetingInfo",meetingInfoHistory);
                    swMessageService.rejectSend(1,1,hashMap,approverUser.getUserID().toString(),"/meetingSignApprovalList",des,des,approverUser,"meetingSignApproval");

                }

                userSuspendMsgService.saveUserSuspendMsg(1,"申请被批准","您的会议发起申请被批准了！","操作时间  "+user.getUserName()+" "+ sdf.format(approvalProcess.getHandleTime()),meetingInfoHistory.getCreator(),"meetingApply",meetingInfoHistory.getId().intValue());//推送我的消息

                approvalProcess.setApproveStatus("2");
                meetingInfoHistory.setApproveStatus(2);
            }else {
                approvalProcess.setApproveStatus("3");
                approvalProcess.setApproveMemo(approveMemo);
                approvalProcess.setReason(approveMemo);
                meetingInfoHistory.setApproveStatus(3);
                meetingInfoHistory.setApproveMemo(approveMemo);

                MeetingInfo meetingInfo=meetingInfoHistory.getMeetingInfo();
                if (meetingInfo!=null) {  //会议修改 被驳回   ，原会议要回到列表中
                    meetingInfo.setState(0);// 改回会议未开始状态
                    meetingInfoDao.update(meetingInfo);
                }

                userSuspendMsgService.saveUserSuspendMsg(1,"申请被驳回","您的会议发起申请被驳回了！","操作时间  "+user.getUserName()+" "+ sdf.format(approvalProcess.getHandleTime()),meetingInfoHistory.getCreator(),"meetingApply",meetingInfoHistory.getId().intValue());//推送我的消息
            }

            meetingInfoHistoryDao.update(meetingInfoHistory);

            approvalProcess.setHandleTime(new Date());
            approvalProcessService.updateApprovalProcess(approvalProcess);

            HashMap<String,Object> hashMap=new HashMap<>();
            hashMap.put("ap",approvalProcess);
            hashMap.put("meetingInfo",meetingInfoHistory);
            swMessageService.rejectSend(-1,-1,hashMap,user.getUserID().toString(),"/meetingFinalApprovalList",null,null,user,"meetingFinalApproval");
            return new JsonResult(1,1);
        }else {
            return new JsonResult(new MyException("已处理 无需重复处理"));
        }
    }

    @Override
    public JsonResult approvalMeetingSign(User user, Long id, Integer approvalStatus, Integer signType) {
        MeetingInfoHistory meetingInfoHistory;
        Long meetingId;
        if (1!=signType) { // 不是审批里的签收
            meetingInfoHistory=getMeetingInfoHistoryByMeetingId(id);
            meetingId=id;
        }else {
            meetingInfoHistory=meetingInfoHistoryDao.get(id);
            meetingId=meetingInfoHistory.getMeetingId();
        }
        ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessByBusToUser(meetingInfoHistory.getId().intValue(),76,user.getUserID());
        if ("1".equals(approvalProcess.getApproveStatus())){
            Integer userId=user.getUserID();

            MeetingParticipant meetingParticipant=getMeetingParticipant(userId,meetingId);
            MeetingParticipantHistory meetingParticipantHistory=getMeetingParticipantHistory(userId,meetingInfoHistory.getId());

            if (approvalStatus==1) {
                approvalProcess.setApproveStatus("2");

                meetingParticipant.setSigned(true); //签收
                meetingParticipant.setPresent(true); //出席

                meetingParticipantHistory.setSigned(true); //签收
                meetingParticipantHistory.setPresent(true); //不出席

            }else {
                approvalProcess.setApproveStatus("3");

                meetingParticipant.setSigned(true); //签收
                meetingParticipant.setPresent(false); //出席

                meetingParticipantHistory.setSigned(true); //签收
                meetingParticipantHistory.setPresent(false); // 不出席
            }

            meetingParticipant.setSignTime(new Date()); //实际签收时间
            meetingParticipant.setUpdateDate(new Date());
            meetingParticipant.setUpdator(userId);
            meetingParticipant.setUserName(user.getUserName());
            meetingParticipantDao.update(meetingParticipant);

            meetingParticipantHistory.setSignTime(new Date()); //实际签收时间
            meetingParticipantHistory.setUpdateDate(new Date());
            meetingParticipantHistory.setUpdator(userId);
            meetingParticipantHistory.setUserName(user.getUserName());
            meetingParticipantHistoryDao.update(meetingParticipantHistory);

            approvalProcess.setHandleTime(new Date());
            approvalProcessService.updateApprovalProcess(approvalProcess);

            HashMap<String,Object> hashMap=new HashMap<>();
            hashMap.put("ap",approvalProcess);
            hashMap.put("meetingInfo",meetingInfoHistory);
            swMessageService.rejectSend(-1,-1,hashMap,userId.toString(),"/meetingSignApprovalList",null,null,user,"meetingSignApproval");
            return new JsonResult(1,1);
        }else {
            return new JsonResult(new MyException("已处理 无需重复处理"));
        }
    }

    @Override
    public MeetingParticipant getMeetingParticipant(Integer userId,Long meetingId){
        String hql="from MeetingParticipant where user=:userId and meetingId=:meetingId";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("meetingId",meetingId);
        MeetingParticipant meetingParticipant= (MeetingParticipant) meetingParticipantDao.getByHQLWithNamedParams(hql,map);
        return meetingParticipant;
    }

    @Override
    public MeetingParticipantHistory getMeetingParticipantHistory(Integer userId,Long meetingHistoryId){
        String hql="from MeetingParticipantHistory where user=:userId and meetingHistoryId=:meetingHistoryId";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("meetingHistoryId",meetingHistoryId);
        MeetingParticipantHistory meetingParticipantHistory= (MeetingParticipantHistory) meetingParticipantHistoryDao.getByHQLWithNamedParams(hql,map);
        return meetingParticipantHistory;
    }

    List<MeetingParticipantHistory> getMeetingParticipantHistoriesByMeetingId(Long meetingId){
        String hql="from MeetingParticipantHistory where meetingId=:meetingId";
        Map<String,Object> map=new HashMap<>();
        map.put("meetingId",meetingId);
        List<MeetingParticipantHistory> meetingParticipantHistoryList= meetingParticipantHistoryDao.getListByHQLWithNamedParams(hql,map);
        return meetingParticipantHistoryList;
    }

    @Override
    public JsonResult cancelMeeting(User user, Long id) {
        MeetingInfo meetingInfo=meetingInfoDao.get(id);
        if (user.getUserID().equals(meetingInfo.getCreator())) {
            meetingInfo.setState(4); //取消会议
            meetingInfo.setTeminateState(1);
            meetingInfo.setTeminateTime(new Date());
            meetingInfo.setTeminater(user.getUserID());
            meetingInfo.setTeminaterName(user.getUserName());
            meetingInfo.setTeminateReason("取消");
            meetingInfoDao.update(meetingInfo);

            MeetingInfoHistory meetingInfoHistory=getMeetingInfoHistoryByMeetingId(id);
            meetingInfoHistory.setState(meetingInfo.getState()); //取消会议
            meetingInfoHistory.setTeminateState(meetingInfo.getTeminateState());
            meetingInfoHistory.setTeminateTime(meetingInfo.getTeminateTime());
            meetingInfoHistory.setTeminater(meetingInfo.getTeminater());
            meetingInfoHistory.setTeminaterName(meetingInfo.getTeminaterName());
            meetingInfoHistory.setTeminateReason(meetingInfo.getTeminateReason());
            meetingInfoHistoryDao.update(meetingInfoHistory);

            List<MeetingParticipantHistory> meetingParticipantHistoryList = getMeetingParticipantHistoriesByMeetingId(id);
            for (MeetingParticipantHistory meetingParticipantHistory : meetingParticipantHistoryList) {
                User appUser = userService.getUserByID(meetingParticipantHistory.getUser());
                ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessByBusToUser(meetingParticipantHistory.getMeetingHistoryId().intValue(), 76, meetingParticipantHistory.getUser());
                if ("1".equals(approvalProcess.getApproveStatus())) {
                    approvalProcess.setApproveStatus("5");
                    approvalProcess.setApproveMemo("取消");
                    approvalProcess.setHandleTime(new Date());
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("ap", approvalProcess);
                    hashMap.put("meetingInfo",meetingInfoHistory);
                    swMessageService.rejectSend(-1, -1, hashMap, appUser.toString(), "/meetingSignApprovalList", null, null, appUser, "meetingSignApproval");

                }
            }
            return new JsonResult(1, 1);
        }
        return new JsonResult(new MyException("登陆人不是会议发起人，无法取消"));
    }

    @Override
    public void updateMeetingInfo(MeetingInfo meetingInfo) {
        meetingInfoDao.update(meetingInfo);
    }

    @Override
    public MeetingImage getMeetingImageById(Long id) {
        return meetingImageDao.get(id);
    }

    @Override
    public void addMeetingImage(User user, Long id, String[] uploadPaths, String memo,Integer type) {

        MeetingInfo meetingInfo = meetingInfoDao.get(id);

        String hql="from MeetingImage where creator=:userId and meetingId=:meetingId order by versionNo desc";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",user.getUserID());
        map.put("meetingId",id);
        MeetingImage lastMeetingImage= (MeetingImage) meetingImageDao.getByHQLWithNamedParams(hql,map);

        Integer orders=1;
        for (String path:uploadPaths) {
            MeetingImage meetingImage=new MeetingImage();
            meetingImage.setCreator(user.getUserID());
            meetingImage.setCreateDate(new Date());
            meetingImage.setCreateName(user.getUserName());
            meetingImage.setMeetingInfo(meetingInfo);
            meetingImage.setOrg(user.getOid());
            meetingImage.setType(type);
            meetingImage.setUploadPath(path);
            meetingImage.setMemo(memo);
            if (lastMeetingImage!=null){
                meetingImage.setVersionNo(lastMeetingImage.getVersionNo()+1);
            }else {
                meetingImage.setVersionNo(1);
            }
            meetingImageDao.save(meetingImage);

            MeetingUsing callback = new MeetingUsing(meetingImage.getId(), meetingImage.getClass());
            String name = meetingInfo.getTopic();
            if (uploadPaths.length > 1) { //多个文件
                name = name + orders;
            }
            uploadService.addFileUsing(callback, path, name, user, "会议");
        }
    }

    @Override
    public JsonResult enterMeeting(User user, Long id) {
        MeetingInfo meetingInfo=meetingInfoDao.get(id);
        if (1==meetingInfo.getState()){
            MeetingParticipant meetingParticipant = getMeetingParticipant(user.getUserID(), id);
            if (meetingParticipant.getPresent()&&meetingParticipant.getPresentTime()!=null) {
                return new JsonResult(new MyException("已签到,不能重复签到"));
            } else {
                meetingParticipant.setPresent(true);
                meetingParticipant.setPresentTime(new Date());
                meetingParticipantDao.update(meetingParticipant);
                return new JsonResult(1, 1);

            }
        }else {
            return new JsonResult(new MyException("发起人还没点击“会议开始”呢！"));
        }
    }

    @Override
    public List<Map<String,Object>> getMeetingImagesByMeetingIdAndUserId(Long meetingId,Integer userId,Integer type) {
        String hql="from MeetingImage where meetingId=:meetingId ";
        Map<String,Object> map=new HashMap<>();
        map.put("meetingId",meetingId);

        if (type==1){ //类型:1-随手拍,2-现场
            hql+=" and creator=:userId and type=1";
            map.put("userId",userId);
        }else {
            hql+=" and type=2";
        }
        List<MeetingImage> meetingImageList = meetingImageDao.getListByHQLWithNamedParams(hql,map);

        Map<Integer,List<MeetingImage>> meetingImageMap=new HashMap<>();
        Map<Integer,String> meetingMemoMap=new HashMap<>();

        for (MeetingImage meetingImage:meetingImageList){
            List<MeetingImage> meetingImages= meetingImageMap.get(meetingImage.getVersionNo());
            if (meetingImages==null){
               meetingImages=new ArrayList<>();
            }
            meetingImages.add(meetingImage);
            meetingImageMap.put(meetingImage.getVersionNo(),meetingImages); // 同一次 随手拍的 图片列表
            meetingMemoMap.put(meetingImage.getVersionNo(),meetingImage.getMemo()); // 同一次 随手拍的 备注说明
        }

        Set<Integer> versionNos=meetingImageMap.keySet();
        List<Map<String,Object>> resultList=new ArrayList<>();
        for (Integer versionNo:versionNos){
            Map<String,Object> resultMap=new HashMap<>();
            resultMap.put("versionNo",versionNo);  //同一次随手拍 版本号
            resultMap.put("meetingImages", meetingImageMap.get(versionNo)); //// 同一次 随手拍的 图片列表
            resultMap.put("memo", meetingMemoMap.get(versionNo));  // 同一次 随手拍的 备注说明
            resultList.add(resultMap);
        }
        return resultList;
    }

    @Override
    public void updateMeetingImage(User user, Long id, String[] uploadPaths, String memo, Integer versionNo) {
        MeetingInfo meetingInfo=meetingInfoDao.get(id);

        String hql="from MeetingImage where creator=:userId and meetingId=:meetingId and versionNo=:versionNo";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",user.getUserID());
        map.put("meetingId",id);
        map.put("versionNo",versionNo);
        List<MeetingImage> meetingImageList = meetingImageDao.getListByHQLWithNamedParams(hql,map);

        for (MeetingImage meetingImage:meetingImageList){
            MeetingUsing callback = new MeetingUsing(meetingImage.getId(), meetingImage.getClass());
            uploadService.delFileUsing(callback, meetingImage.getUploadPath(), user);
        }
        meetingImageDao.deleteAll(meetingImageList);

        Integer orders=1;
        for (String path:uploadPaths) {
            MeetingImage meetingImage=new MeetingImage();
            meetingImage.setCreator(user.getUserID());
            meetingImage.setCreateDate(new Date());
            meetingImage.setCreateName(user.getUserName());
            meetingImage.setMeetingInfo(meetingInfo);
            meetingImage.setOrg(user.getOid());
            meetingImage.setType(1);
            meetingImage.setUploadPath(path);
            meetingImage.setMemo(memo);
            meetingImage.setVersionNo(versionNo);
            meetingImageDao.save(meetingImage);

            MeetingUsing callback = new MeetingUsing(meetingImage.getId(), meetingImage.getClass());
            String name = meetingInfo.getTopic();
            if (uploadPaths.length > 1) { //多个文件
                name = name + orders;
            }
            uploadService.addFileUsing(callback, path, name, user, "会议");
        }
    }

    @Override
    public MeetingParticipant getMeetingRecorder(Long meetingId) {
        String hql="from MeetingParticipant where type=:type and meetingId=:meetingId";
        Map<String,Object> map=new HashMap<>();
        map.put("type",2);
        map.put("meetingId",meetingId);
        MeetingParticipant meetingParticipant= (MeetingParticipant) meetingParticipantDao.getByHQLWithNamedParams(hql,map);
        return meetingParticipant;
    }

    @Override
    public MeetingParticipantHistory getMeetingRecorderHistory(Long meetingHistoryId) {
        String hql="from MeetingParticipantHistory where type=:type and meetingHistoryId=:meetingHistoryId";
        Map<String,Object> map=new HashMap<>();
        map.put("type",2);
        map.put("meetingHistoryId",meetingHistoryId);
        MeetingParticipantHistory meetingParticipantHistory= (MeetingParticipantHistory) meetingParticipantHistoryDao.getByHQLWithNamedParams(hql,map);
        return meetingParticipantHistory;
    }

    @Override
    public List<MeetingInfo> recorderMeetingMakeList(Integer userId,Integer approveStatus) {
        String hql="from MeetingInfo where state=2 and approveStatus=:approveStatus and id in(select meetingId from MeetingParticipant where user=:userId and type=2)";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus);
        List<MeetingInfo> meetingInfos=meetingInfoDao.getListByHQLWithNamedParams(hql+" order by planTime desc",map);
        return meetingInfos;
    }

    @Override
    public List<MeetingInfo> founderMeetingMakeList(Integer userId,Integer approveStatus) {
        String hql="from MeetingInfo where state=2 and creator=:userId and approveStatus=:approveStatus";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus);
        List<MeetingInfo> meetingInfos=meetingInfoDao.getListByHQLWithNamedParams(hql+" order by planTime desc",map);
        return meetingInfos;
    }

    @Override
    public List<MeetingParticipant> getMeetingParticipantsByMeetingId(Long meetingId, Boolean sign) {
        String hql="from MeetingParticipant where meetingId=:meetingId and signed=:signed";
        Map<String,Object> map=new HashMap<>();
        map.put("signed",sign);
        map.put("meetingId",meetingId);
        List<MeetingParticipant> meetingParticipantList=meetingParticipantDao.getListByHQLWithNamedParams(hql,map);
        return meetingParticipantList;
    }

    @Override
    public List<MeetingQuestion> getMeetingQuestionByMeetingId(Long meetingId) {
        String hql="from MeetingQuestion where meetingId=:meetingId";
        Map<String,Object> map=new HashMap<>();
        map.put("meetingId",meetingId);
        List<MeetingQuestion> meetingQuestionList=meetingQuestionDao.getListByHQLWithNamedParams(hql,map);
        return meetingQuestionList;
    }

    @Override
    public List<MeetingImage> getMeetingImagesByMeetingId(Long meetingId,Integer type,Integer userId) {
        String hql="from MeetingImage where meetingId=:meetingId and type=:type";
        Map<String,Object> map=new HashMap<>();
        map.put("meetingId",meetingId);
        map.put("type",type);

        if (userId!=null){
            hql+=" and creator=:userId";
            map.put("userId",userId);
        }
        List<MeetingImage> meetingImageList=meetingImageDao.getListByHQLWithNamedParams(hql,map);
        return meetingImageList;
    }

    @Override
    public List<MeetingInfo> participantsMeetingReceiptList(Integer userId) {
        String hql="from MeetingInfo where state=2 and approveStatus=:approveStatus and id in(select meetingId from MeetingParticipant where receipt is null and user=:userId)";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",2);
        List<MeetingInfo> meetingInfos=meetingInfoDao.getListByHQLWithNamedParams(hql+" order by planTime desc",map);
        return meetingInfos;
    }

    @Override
    public JsonResult meetingRecordReceipt(User user, Long meetingId) {
        Integer userId=user.getUserID();
        MeetingInfo meetingInfo=meetingInfoDao.get(meetingId);
        MeetingParticipant meetingParticipant=getMeetingParticipant(userId,meetingId);

        if (meetingParticipant!=null&&meetingParticipant.getReceipt()==null){
            meetingParticipant.setReceipt(true); //会议记录签收
            meetingParticipant.setReceiptTime(new Date()); //会议记录签收时间
            meetingParticipant.setUpdateDate(new Date());
            meetingParticipant.setUpdator(userId);
            meetingParticipant.setUserName(user.getUserName());
            meetingParticipantDao.update(meetingParticipant);

            HashMap<String,Object> hashMap=new HashMap<>();
            hashMap.put("meetingInfo",meetingInfo);
            swMessageService.rejectSend(-1,-1,hashMap,userId.toString(),"/participantsMeetingReceiptList",null,null,user,"meetingRecordSign");
            return new JsonResult(1,1);
        }else {
            return new JsonResult(new MyException("已处理 无需重复处理"));
        }
    }

    @Override
    public List<MeetingInfo> getHistoryMeetingListByUserId(Integer userId, Date beginDate,Date endDate) {
        String hql="from MeetingInfo where state>1 and (creator=:userId or id in(select meetingId from MeetingParticipant where user=:userId))";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        if (beginDate!=null&&endDate!=null){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }

        List<MeetingInfo> meetingInfos=meetingInfoDao.getListByHQLWithNamedParams(hql+" order by planTime desc",map);
        return meetingInfos;
    }

    @Override
    public List<MeetingInfo> screenHistoryMeetingList(Integer userId, Integer promoterId, Integer recorderId, Integer state, Integer approveStatus) {
        String hql="from MeetingInfo where state>1 and (creator=:userId or id in(select meetingId from MeetingParticipant where user=:userId ";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);

        if (recorderId!=null){ // 记录者
            hql+=" and type=2 and user=:recorderId";
            map.put("recorderId",recorderId);
        }
        hql+="))";
        if (promoterId!=null){
            hql+=" and creator=:promoterId";
            map.put("promoterId",promoterId);
        }
        if (state!=null){
            if (state==2){
                hql+=" and state in(2,4,5)"; // 已结束 有三种情况  完成2，会议取消4，实际未召开5
            }else if (state==3){
                hql+=" and state=3"; // 已过期
            }
        }
        if (approveStatus!=null){
            if (approveStatus==2){
                hql+=" and approveStatus=2";   //会议记录制作完成
            }else {
                hql+=" and approveStatus!=2";  //会议记录制作未完成
            }
        }
        List<MeetingInfo> meetingInfos=meetingInfoDao.getListByHQLWithNamedParams(hql+" order by planTime desc",map);
        return meetingInfos;
    }

    @Override
    public void addMeetingRecordApply(User user, Long id, Integer conveneState, String factTime, String finishTime, Integer[] otherAttendees, String questionJsonArray, String resolutionJsonArray, String speakJsonArray, String[] sitePhotos) {
        MeetingInfo meetingInfo=getMeetingInfoById(id);
        if (conveneState==1){
            meetingInfo.setFactTime(NewDateUtils.dateFromString(factTime,"yyyy-MM-dd HH:mm:ss"));
            meetingInfo.setFinishTime(NewDateUtils.dateFromString(finishTime,"yyyy-MM-dd HH:mm:ss"));

            List<Integer> noUserIdList=new ArrayList<>();
            for (MeetingParticipant meetingParticipant:meetingInfo.getMeetingParticipantHashSet()){
                noUserIdList.add(meetingParticipant.getUser());
            }

            for (Integer userId:otherAttendees){  //其他参会者
                if (!noUserIdList.contains(userId)) {
                    saveMeetingParticipant(meetingInfo, userId, 1);
                }
            }
            net.sf.json.JSONArray questionArray = net.sf.json.JSONArray.fromObject(questionJsonArray); /// 问题
            List questionList= net.sf.json.JSONArray.toList(questionArray);
            for (int i=0;i<questionList.size();i++) {
                JSONObject questionJson = JSONObject.fromObject(questionList.get(i));
                MeetingQuestion meetingQuestion=meetingQuestionDao.get(questionJson.getLong("id"));
                meetingQuestion.setResult(questionJson.getInt("result")); // 0-未解决，1-解决
                meetingQuestion.setSupplement(questionJson.getString("supplement")); //补充说明
                meetingQuestion.setUpdateDate(new Date());
                meetingQuestion.setUpdator(user.getUserID());
                meetingQuestion.setUpdateName(user.getUserName());
                meetingQuestion.setMeetingInfo(meetingInfo);
                meetingQuestionDao.update(meetingQuestion);
            }

            net.sf.json.JSONArray resolutionArray = net.sf.json.JSONArray.fromObject(resolutionJsonArray); /// 决议
            List resolutionList= net.sf.json.JSONArray.toList(resolutionArray);
            for (int i=0;i<resolutionList.size();i++) {
                String res=resolutionList.get(i).toString();
                MeetingResolution meetingResolution=new MeetingResolution();
                meetingResolution.setCreateDate(new Date());
                meetingResolution.setCreator(user.getUserID());
                meetingResolution.setCreateName(user.getUserName());
                meetingResolution.setResolution(res);
                meetingResolution.setMeetingInfo(meetingInfo);
                meetingResolutionDao.save(meetingResolution);
            }

            net.sf.json.JSONArray speakArray = net.sf.json.JSONArray.fromObject(speakJsonArray); /// 发言
            List speakList= net.sf.json.JSONArray.toList(speakArray);
            for (int i=0;i<speakList.size();i++) {
                JSONObject speakJson = JSONObject.fromObject(speakList.get(i));
                MeetingSpeak meetingSpeak=new MeetingSpeak();
                meetingSpeak.setUser(speakJson.getInt("speakUserId")); // 发言人
                User speakUser=userService.getUserByID(meetingSpeak.getUser());
                meetingSpeak.setUserName(speakUser.getUserName());
                meetingSpeak.setSummary(speakJson.getString("summary")); //内容
                meetingSpeak.setCreateDate(new Date());
                meetingSpeak.setCreator(user.getUserID());
                meetingSpeak.setCreateName(user.getUserName());
                meetingSpeak.setMeetingInfo(meetingInfo);
                meetingSpeakDao.save(meetingSpeak);
            }

            addMeetingImage(user, id, sitePhotos, null,2); // 现场照片
        }else {
            meetingInfo.setState(5);
        }

        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("meetingInfo",meetingInfo);
        User creUser=userService.getUserByID(meetingInfo.getCreator()); //发起人

        if (user.getUserID().equals(meetingInfo.getCreator())){  //登陆人是 会议发起人 直接
            meetingInfo.setApproveStatus(2);

            for (MeetingParticipant meetingParticipant:meetingInfo.getMeetingParticipantHashSet()){
                User u=userService.getUserByID(meetingParticipant.getUser());
                swMessageService.rejectSend(1,1,hashMap,u.getUserID().toString(),"/meetingRecordSignList","您有一条会议记录需要签收","您有一条会议记录需要签收",u,"meetingRecordSign");
            }
        }else {
            meetingInfo.setApproveStatus(1);

            swMessageService.rejectSend(1,1,hashMap,creUser.getUserID().toString(),"/meetingRecordApprovalList","您有一条会议记录需要审批","您有一条会议记录需要审批",creUser,"meetingRecordApproval");
        }
        meetingInfo.setRecordEditor(user.getUserID());
        meetingInfo.setRecordEditorName(user.getUserName());
        meetingInfo.setRecordEditTime(new Date());
        meetingInfoDao.update(meetingInfo);

        //给 会议记录者 发  频道四 会议记录 制作 待制作 角标-1
        MeetingParticipant meetingParticipant=getMeetingRecorder(id);
        User recUser=userService.getUserByID(meetingParticipant.getUser());
        swMessageService.rejectSend(-1,-1,hashMap,meetingParticipant.getUser().toString(),"/recorderMeetingMakeList",null,null,recUser,"meetingRecordMake");

        //给 会议发起人发  频道五 会记录 审批 待制作-1
        swMessageService.rejectSend(-1,-1,hashMap,creUser.getUserID().toString(),"/founderMeetingMakeList",null,null,creUser,"meetingRecordApproval");

    }

    @Override
    public void approvalMeetingRecord(User user, Long id, Integer approval,String approveMemo) {
        MeetingInfo meetingInfo = getMeetingInfoById(id);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("meetingInfo",meetingInfo);
        if (approval == 1) {
            meetingInfo.setApproveStatus(2);

            for (MeetingParticipant meetingParticipant:meetingInfo.getMeetingParticipantHashSet()){
                User u=userService.getUserByID(meetingParticipant.getUser());
                swMessageService.rejectSend(1,1,hashMap,u.getUserID().toString(),"/meetingRecordSignList","您有一条会议记录需要签收","您有一条会议记录需要签收",u,"meetingRecordSign");
            }
        } else {
            meetingInfo.setApproveStatus(3);
            meetingInfo.setApproveMemo(approveMemo);

            //给 记录者  已驳回发送 +1
            User u=userService.getUserByID(meetingInfo.getRecordEditor());
            swMessageService.rejectSend(1,1,hashMap,meetingInfo.getRecordEditor().toString(),"/recorderMeetingRejectList","您的会议记录申请被驳回了","您的会议记录申请被驳回了",u,"meetingRecordMake");

            userSuspendMsgService.saveUserSuspendMsg(1,"申请被驳回","您的会议记录申请被驳回了！","操作时间  "+user.getUserName()+" "+ meetingInfo.getApproveDate(),meetingInfo.getRecordEditor(),"meetingRecordApply",meetingInfo.getId().intValue());//推送我的消息

        }
        meetingInfo.setApproveDate(new Date());
        meetingInfo.setApproverName(user.getUserName());
        updateMeetingInfo(meetingInfo);

        swMessageService.rejectSend(-1,-1,hashMap,user.getUserID().toString(),"/meetingRecordApprovalList",null,null,user,"meetingRecordApproval");


    }


    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code) {
            case "meetingRecordMake": //会议记录——制作
                //待制作 + 已驳回
                result = getRecorderMeetingMakeNumber(user.getUserID(),new Integer[]{0,3});
                break;
            case "meetingRecordApproval":   //会议记录——审批
                //带制作 + 待审批
                result = getFounderMeetingMakeNumber(user.getUserID(),new Integer[]{0,1});
                break;
            case "meetingRecordSign":   //会议记录——签收
                result = participantsMeetingReceiptList(user.getUserID()).size();
                break;
        }
        return result;
    }


    @Override
    public Integer getRecorderMeetingMakeNumber(Integer userId, Integer[] approveStatus) {
        String hql="select count(*) from  MeetingInfo where state=2 and approveStatus in(:approveStatus) and id in(select meetingId from MeetingParticipant where user=:userId and type=2)";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus);
        Long result = (Long) meetingInfoDao.getByHQLWithNamedParams(hql, map);
        return result.intValue();
    }

    @Override
    public Integer getFounderMeetingMakeNumber(Integer userId, Integer[] approveStatus) {
        String hql="select count(*) from MeetingInfo where state=2 and creator=:userId and approveStatus in(:approveStatus)";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus);
        Long result = (Long) meetingInfoDao.getByHQLWithNamedParams(hql, map);
        return result.intValue();
    }
}
