package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquModelProduct;
import cn.sphd.miners.modules.equipment.mapper.TEquModelProductMapper;
import cn.sphd.miners.modules.equipment.service.ITEquModelProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_型号产品参与Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquModelProductServiceImpl implements ITEquModelProductService
{
    @Autowired
    private TEquModelProductMapper tEquModelProductMapper;

    /**
     * 查询装备管理_型号产品参与
     * 
     * @param id 装备管理_型号产品参与主键
     * @return 装备管理_型号产品参与
     */
    @Override
    public TEquModelProduct selectTEquModelProductById(Long id)
    {
        return tEquModelProductMapper.selectTEquModelProductById(id);
    }

    /**
     * 查询装备管理_型号产品参与列表
     * 
     * @param tEquModelProduct 装备管理_型号产品参与
     * @return 装备管理_型号产品参与
     */
    @Override
    public List<TEquModelProduct> selectTEquModelProductList(TEquModelProduct tEquModelProduct)
    {
        return tEquModelProductMapper.selectTEquModelProductList(tEquModelProduct);
    }

    /**
     * 新增装备管理_型号产品参与
     * 
     * @param tEquModelProduct 装备管理_型号产品参与
     * @return 结果
     */
    @Override
    public int insertTEquModelProduct(TEquModelProduct tEquModelProduct)
    {
        return tEquModelProductMapper.insertTEquModelProduct(tEquModelProduct);
    }

    /**
     * 修改装备管理_型号产品参与
     * 
     * @param tEquModelProduct 装备管理_型号产品参与
     * @return 结果
     */
    @Override
    public int updateTEquModelProduct(TEquModelProduct tEquModelProduct)
    {
        return tEquModelProductMapper.updateTEquModelProduct(tEquModelProduct);
    }

    /**
     * 批量删除装备管理_型号产品参与
     * 
     * @param ids 需要删除的装备管理_型号产品参与主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelProductByIds(String... ids)
    {
        return tEquModelProductMapper.deleteTEquModelProductByIds(ids);
    }

    /**
     * 删除装备管理_型号产品参与信息
     * 
     * @param id 装备管理_型号产品参与主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelProductById(Long id)
    {
        return tEquModelProductMapper.deleteTEquModelProductById(id);
    }
}
