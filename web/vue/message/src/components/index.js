// creator: 张旭博，2018-09-20 10:24:13，公用组件全局注册
import Vue from 'vue'
import dialog from './dialog/dialog'
import navTop from './nav/nav-top'
import tab from './tab/tabs'
import tabPane from './tab/tab-pane'
import tabBar from './tab/tab-bar'
import Message from './dialog/message'
import Loading from './dialog/loading'
import confirm from './dialog/confirm'
import ToolTip from './dialog/tip'
import AddressBook from './addressBook/address'
import Edit from './addressBook/edit'

const LoadingConstructor = Vue.extend(Loading)

Vue.directive('kiko-loading', {
  update: function (el, binding) {
    if (binding.oldValue !== binding.value) {
      const options = {}
      options.fullScreen = binding.modifiers.fullscreen
      if (options.fullScreen) {
        options.top = 0
        options.left = 0
        options.width = '100%'
        options.height = '100%'
      } else {
        ['top', 'left'].forEach(function (property) {
          var scroll = property === 'top' ? 'scrollTop' : 'scrollLeft'
          options[property] = el.getBoundingClientRect()[property] +
            document.body[scroll] +
            document.documentElement[scroll] +
            'px'
        });
        ['height', 'width'].forEach(function (property) {
          options[property] = el.getBoundingClientRect()[property] + 'px'
        })
      }
      let component = new LoadingConstructor({
        data: options
      }).$mount()
      let node = document.querySelector('.kiko-loading')
      if (node && node.parentNode) {
        node.parentNode.removeChild(node)
      }
      if (binding.value === true) {
        document.querySelector('body').appendChild(component.$el)
      } else {
        if (node && node.parentNode) {
          node.parentNode.removeChild(node)
        }
      }
    }
  }
})

function plugin (vue) {
  let Vue = vue
  if (plugin.installed) {
    return
  }
  Vue.component('TY_Alert', alert)
  Vue.component('TY_NavTop', navTop)
  Vue.component('TY_Tabs', tab)
  Vue.component('TY_TabPane', tabPane)
  Vue.component('TY_TabBar', tabBar)
  Vue.component('TY_Dialog', dialog)
  Vue.component('TY_Message', Message)
  Vue.component('TY_Loading', Loading)
  Vue.component('TY_Confirm', confirm)
  Vue.component('TY_Tip', ToolTip)
  Vue.component('TY_AddressBook', AddressBook)
  Vue.component('TY_Edit', Edit)

  Vue.prototype.$kiko_tooltip = function (event, opt) {
    let options = opt
    let rect = {};
    ['top', 'left'].forEach(function (property) {
      let scroll = property === 'top' ? 'scrollTop' : 'scrollLeft'
      rect[property] = event.target.getBoundingClientRect()[property] +
        document.body[scroll] +
        document.documentElement[scroll]
    });
    ['height', 'width'].forEach(function (property) {
      rect[property] = event.target.getBoundingClientRect()[property]
    })
    options.rect = rect
    let ToolTips = Vue.extend(ToolTip)

    let component = new ToolTips({
      data: options
    }).$mount()
    event.target.appendChild(component.$el)
    // document.querySelector('body').appendChild(component.$el)
  }

  Vue.prototype.$kiko_message = function (option) {
    let options = option
    if (options === undefined || options === null) {
      options = {
        message: ''
      }
    } else if (typeof options === 'string' || typeof options === 'number') {
      options = {
        message: options
      }
    }
    let Messages = Vue.extend(Message)

    let component = new Messages({
      data: options
    }).$mount()
    document.querySelector('body').appendChild(component.$el)
  }
}

export default plugin
