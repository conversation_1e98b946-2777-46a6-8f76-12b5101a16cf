<template>
  <div id="potentialApply" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content sect-con">
        <el-row>
          <el-col :span="6">客户名称</el-col>
          <el-col :span="16"><el-input size="mini" placeholder="请输入内容" v-model="details.fullName"></el-input></el-col>
        </el-row>
        <el-row>
          <el-col :span="6">地    址</el-col>
          <el-col :span="16"><el-input size="mini" placeholder="请输入内容" v-model="details.address"></el-input></el-col>
        </el-row>
        <div v-if="sphdSocket.user.oid === 0">
          <el-row>
            <el-col :span="6">最高负责人</el-col>
            <el-col :span="16"><el-input size="mini" placeholder="请输入内容" v-model="details.supervisorName"></el-input></el-col>
          </el-row>
          <el-row>
            <el-col :span="6">手 机</el-col>
            <el-col :span="16"><el-input size="mini" placeholder="请输入内容" v-model="details.supervisorMobile"></el-input></el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="6">全景图片</el-col>
          <el-col :span="16" class="col-flex uploadBaseImg">
            <el-upload
              :action="webRoot+'/uploads/uploadfyByFile.do'"
              :data = "uploadData"
              list-type="picture-card"
              :file-list="details.qImages"
              :show-file-list="true"
              :on-success="handleQimagesSuccess"
              :on-remove="handleRemoveQj">
              <i class="el-icon-plus"></i>
            </el-upload>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">产品图片</el-col>
          <el-col :span="18" class="col-flex uploadBaseImg">
            <el-upload
              :action="webRoot+'/uploads/uploadfyByFile.do'"
              :data = "uploadData"
              list-type="picture-card"
              :file-list="details.pImages"
              :show-file-list="true"
              :on-success="handlePimagesSuccess"
              :on-remove="handleRemoveCp">
              <i class="el-icon-plus"></i>
            </el-upload>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触日期</el-col>
          <el-col :span="18">
            <el-date-picker
              v-model="details.firstContactTime"
              type="date"
              placeholder="选择日期"
              format="yyyy/MM/dd"
              value-format="yyyy/MM/dd"
              size="mini">
            </el-date-picker>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触地点</el-col>
          <el-col :span="16"><el-input size="mini" placeholder="请输入内容" v-model="details.firstContactAddress"></el-input></el-col>
        </el-row>
        <el-row>
          <el-col :span="6">信息获取渠道</el-col>
          <el-col :span="16"><el-input size="mini" placeholder="请输入内容" v-model="details.infoSource"></el-input></el-col>
        </el-row>
      </div>
      <div class="handle_button handle-center">
        <el-button type="primary" :disabled="editControl" @click="baseUpdateSure" size="medium">提交</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { formatDate, gohistory } from '../../../js/common'
export default {
  name: 'baseDetailUpdate',
  filters: {
    formatDate,
    gohistory
  },
  data () {
    return {
      loading: true,
      urls: [],
      details: {
        fullName: '',
        address: '',
        supervisorName: '',
        supervisorMobile: '',
        qImages: [],
        pImages: [],
        firstContactTime: '',
        firstContactAddress: '',
        infoSource: ''
      },
      orgDetails: {
        qImages: [],
        pImages: []
      },
      disabled: false,
      dialogImageUrl: '',
      dialogVisible: false,
      fileUrl: window.parent.$.fileUrl,
      webRoot: window.parent.$.webRoot,
      uploadData: {
        module: '潜在客户',
        userId: this.sphdSocket.user.userID
      }
    }
  },
  computed: {
    editControl () {
      let state = 0
      for (let item in this.details) {
        if (item === 'firstContactTime') {
          if (formatDate(new Date(this.details[item]), 'yyyy/MM/dd') !== formatDate(new Date(this.orgDetails[item]), 'yyyy/MM/dd')) {
            state++
          }
        } else if (item === 'qImages' || item === 'pImages') {
          if (this.details[item].length !== this.orgDetails[item].length) {
            state++
          } else {
            for (let t in this.details[item]) {
              let indx = this.orgDetails[item].findIndex(value => value.normal === this.details[item][t].normal)
              if (indx === undefined) {
                state++
              }
            }
          }
        } else {
          if (this.details[item] !== this.orgDetails[item]) {
            state++
          }
        }
      }
      if (this.details.fullName !== '' && state > 0) {
        return false
      } else {
        return true
      }
    }
  },
  created () {
    let _this = this
    this.axios.post('../../../sale/getPotentialCustomer.do', {
      id: _this.$route.params.id
    }).then(function (response) {
      _this.loading = false
      let details = response.data.data
      for (let i in details.qImages) {
        details.qImages[i].name = ''
        details.qImages[i].url = _this.fileUrl + details.qImages[i].normal
      }
      for (let a in details.pImages) {
        details.pImages[a].name = ''
        details.pImages[a].url = _this.fileUrl + details.pImages[a].normal
      }

      if (details.firstContactTime !== null) {
        details.firstContactTime = new Date(details.firstContactTime)
      } else {
        details.firstContactTime = ''
      }
      _this.details = details
      _this.orgDetails = JSON.parse(JSON.stringify(details))
    }).catch(function (error) {
      console.log(error)
    })
  },
  methods: {
    baseUpdateSure: function () {
      let _this = this
      let details = _this.details
      let json = {
        id: _this.orgDetails.id,
        fullName: details.fullName,
        supervisorName: details.supervisorName,
        supervisorMobile: details.supervisorMobile,
        address: details.address,
        firstContactAddress: details.firstContactAddress,
        infoSource: details.infoSource,
        qImages: JSON.stringify(details.qImages),
        pImages: JSON.stringify(details.pImages)
      }
      if (details.firstContactTime !== null && details.firstContactTime !== '') {
        json.firstContactTime = details.firstContactTime
      }
      this.axios.post('../../../sales/updatePdCustomerBase.do', json
      ).then(function (response) {
        _this.loading = true
        console.log('response:' + response)
        let list = []
        for (let i in _this.details.qImages) {
          if (_this.details.qImages[i].fileUid) {
            list.push({
              type: 'fileId',
              fileId: _this.details.qImages[i].fileUid
            })
          }
        }
        for (let a in _this.details.pImages) {
          if (_this.details.pImages[a].fileUid) {
            list.push({
              type: 'fileId',
              fileId: _this.details.pImages[a].fileUid
            })
          }
        }
        window.parent.cancelFileDel(list)
        gohistory(_this)
      }).catch(function (error) {
        console.log(error)
      })
    },
    handleRemoveQj (file) {
      let indx = this.details.qImages.findIndex(item => item.normal === file.normal)
      let option = {type: 'fileId', fileId: this.details.qImages[indx].fileUid}
      this.details.qImages.splice(indx, 1)
      window.parent.cancelFileDel(option, true)
    },
    handleRemoveCp (file) {
      let indx = this.details.pImages.findIndex(item => item.name === file.name)
      let option = {type: 'fileId', fileId: this.details.qImages[indx].fileUid}
      this.details.pImages.splice(indx, 1)
      window.parent.cancelFileDel(option, true)
    },
    handleQimagesSuccess (file) {
      let fileData = {
        name: '',
        url: this.fileUrl + file.filename,
        normal: file.filename,
        fileUid: file.fileUid
      }
      this.details.qImages.push(fileData)
      let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
      fileDelArr.push({'type':'fileId', 'fileId': file.fileUid})
      window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    },
    handlePimagesSuccess (file) {
      let fileData = {
        name: '',
        url: this.fileUrl + file.filename,
        normal: file.filename,
        fileUid: file.fileUid
      }
      this.details.pImages.push(fileData)
      let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
      fileDelArr.push({'type':'fileId', 'fileId': file.fileUid})
      window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    }
  },
  destroyed: function () {
    let list = []
    for (let i in this.details.qImages) {
      if (this.details.qImages[i].fileUid) {
        list.push({
          type: 'fileId',
          fileId: this.details.qImages[i].fileUid
        })
      }
    }
    for (let a in this.details.pImages) {
      if (this.details.pImages[a].fileUid) {
        list.push({
          type: 'fileId',
          fileId: this.details.pImages[a].fileUid
        })
      }
    }
    window.parent.cancelFileDel(list, true)
  }
}
</script>
