<template>
  <div id="trialApplyQuery" class="trialApplyQuery">
    <TY_NavTop title="试用申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approvalStatus" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div class="handle_button" style="margin-top: 20px">
              <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit" v-loading.fullscreen.lock="loading">
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  .trialApplyQuery .el-radio__input, .trialApplyQuery .el-radio__input {
    display: none;
  }
  .trialApplyQuery .el-form-item{
    margin-bottom: 8px;
  }
  .trialApplyQuery .el-radio-group .el-radio {
    display: inline-block;
    margin: 0 5px 5px 0;
  }
  .el-radio.is-bordered + .el-radio.is-bordered
  .trialApplyQuery .el-checkbox{
    margin-bottom: 8px;
  }
  .trialApplyQuery .el-radio:first-child{
    margin-left: 10px;
  }
</style>

<script>
export default {
  name: 'trialApplyQuery',
  data () {
    return {
      loading: false,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approvalStatus: '2'
      }
    }
  },
  created: function () { },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approvalStatus 2- 批准 3- 驳回
      // feeCat 费用类别id
      // feeCatName 费用类别名称
      // billCat 票据种类 id
      // billCatName 票据种类名称
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        approvalStatus: this.queryForm.approvalStatus,
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        type: this.queryForm.type,
        userId: this.sphdSocket.user.userID
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/trialApplyQueryPer',
        name: 'trialApplyQueryPer',
        params: {
          data: queryParam
        }
      })
    }
  }
}
</script>
