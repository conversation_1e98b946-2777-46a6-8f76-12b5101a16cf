package cn.sphd.miners.modules.mpWeChatRes.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021-12-21 
 */

@Entity
@Table ( name ="t_wx_resource_category" )
public class WxResourceCategory  implements Serializable {

	/**
	 * ID
	 */
	@Id
	@Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 微信应用账号ID
	 */
	@Column(name = "app", nullable=true , unique=false )
	private Integer app;

	/**
	 * 微信应用账号ID
	 */
	@Column(name = "app_id", length=128, nullable=true , unique=false )
	private String appId;

	/**
	 * 机构ID
	 */
	@Column(name = "org", nullable=true , unique=false )
	private Integer org;

	/**
	 * 父结点ID
	 */
	@Column(name = "parent", nullable=true , unique=false )
	private Integer parent;

	/**
	 * 类型：1-资源中心,2-参考资料
	 */
	@Column(name = "type", length=1, nullable=true , unique=false )
	private String type;

	/**
	 * 分类：1-系统类别,2-自定义类别
	 */
	@Column(name = "category", length=1, nullable=true , unique=false )
	private String category;

	/**
	 * 是否有效
	 */
	@Column(name = "valid", nullable=true , unique=false )
	private Boolean valid;

	/**
	 * 显示有子级文件夹
	 */
	@Column(name = "max_child_categories", nullable=true , unique=false )
	private Integer maxChildCategories;

	/**
	 * 儿子的个数
	 */
	@Column(name = "children", nullable=true , unique=false )
	private Integer children;

	/**
	 * 子孙文件夹个数
	 */
	@Column(name = "descendant", nullable=true , unique=false )
	private Long descendant;

	/**
	 * 文件个数
	 */
	@Column(name = "leafs", nullable=true , unique=false )
	private Integer leafs;

	/**
	 * 路径,存各级祖先节点，以逗号分隔
	 */
	@Column(name = "path", nullable=true , unique=false )
	private String path;

	/**
	 * 存储文件大小
	 */
	@Column(name = "size", nullable=true , unique=false )
	private Long size;

	/**
	 * 名称
	 */
	@Column(name = "name", nullable=true , unique=false )
	private String name;

	/**
	 * 是否垃圾
	 */
	@Column(name = "is_trash", nullable=true , unique=false )
	private Boolean isTrash;

	/**
	 * 描述
	 */
	@Column(name = "memo", length=255, nullable=true , unique=false )
	private String memo;

	/**
	 * 排序
	 */
	@Column(name = "orders", nullable=true , unique=false )
	private Integer orders;

	/**
	 * 创建人id
	 */
	@Column(name = "creator", nullable=true , unique=false )
	private Integer creator;

	/**
	 * 创建人
	 */
	@Column(name = "create_name", length=100, nullable=true , unique=false )
	private String createName;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date", nullable=true , unique=false )
	private Date createDate;

	/**
	 * 修改人id
	 */
	@Column(name = "updator", nullable=true , unique=false )
	private Integer updator;

	/**
	 * 修改人
	 */
	@Column(name = "update_name", length=100, nullable=true , unique=false )
	private String updateName;

	/**
	 * 修改时间
	 */
	@Column(name = "update_date", nullable=true , unique=false )
	private Date updateDate;

	/**
	 * 申批项目
	 */
	@Column(name = "approve_item", nullable=true , unique=false )
	private Integer approveItem;

	/**
	 * 审批状态:0-未申请 ,1-申请提交,2-通过审核,3-否决审核
	 */
	@Column(name = "approve_status", length=1, nullable=true , unique=false )
	private String approveStatus;

	/**
	 * 审批次级
	 */
	@Column(name = "approve_level", nullable=true , unique=false )
	private Integer approveLevel;

	/**
	 * 审批者ID
	 */
	@Column(name = "auditor", nullable=true , unique=false )
	private Integer auditor;

	/**
	 * 审批者
	 */
	@Column(name = "auditor_name", length=100, nullable=true , unique=false )
	private String auditorName;

	/**
	 * 审批日期
	 */
	@Column(name = "audit_date", nullable=true , unique=false )
	private Date auditDate;

	/**
	 * 操作:1-增,2-删,3-改,4-改名
	 */
	@Column(name = "operation", length=1, nullable=true , unique=false )
	private String operation;

	/**
	 * 申请备注
	 */
	@Column(name = "apply_memo", length=255, nullable=true , unique=false )
	private String applyMemo;

	/**
	 * 审批备注
	 */
	@Column(name = "approve_memo", length=255, nullable=true , unique=false )
	private String approveMemo;

	/**
	 * 对应消息表的id
	 */
	@Column(name = "message_id", nullable=true , unique=false )
	private Integer messageId;

	/**
	 * 修改前记录ID
	 */
	@Column(name = "previous_id", nullable=true , unique=false )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
	@Column(name = "version_no", nullable=true , unique=false )
	private Integer versionNo;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApp() {
		return app;
	}

	public void setApp(Integer app) {
		this.app = app;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getParent() {
		return parent;
	}

	public void setParent(Integer parent) {
		this.parent = parent;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Boolean getValid() {
		return valid;
	}

	public void setValid(Boolean valid) {
		this.valid = valid;
	}

	public Integer getMaxChildCategories() {
		return maxChildCategories;
	}

	public void setMaxChildCategories(Integer maxChildCategories) {
		this.maxChildCategories = maxChildCategories;
	}

	public Integer getChildren() {
		return children;
	}

	public void setChildren(Integer children) {
		this.children = children;
	}

	public Long getDescendant() {
		return descendant;
	}

	public void setDescendant(Long descendant) {
		this.descendant = descendant;
	}

	public Integer getLeafs() {
		return leafs;
	}

	public void setLeafs(Integer leafs) {
		this.leafs = leafs;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Long getSize() {
		return size;
	}

	public void setSize(Long size) {
		this.size = size;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Boolean getTrash() {
		return isTrash;
	}

	public void setTrash(Boolean trash) {
		isTrash = trash;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getOrders() {
		return orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getApproveItem() {
		return approveItem;
	}

	public void setApproveItem(Integer approveItem) {
		this.approveItem = approveItem;
	}

	public String getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(String approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Integer getApproveLevel() {
		return approveLevel;
	}

	public void setApproveLevel(Integer approveLevel) {
		this.approveLevel = approveLevel;
	}

	public Integer getAuditor() {
		return auditor;
	}

	public void setAuditor(Integer auditor) {
		this.auditor = auditor;
	}

	public String getAuditorName() {
		return auditorName;
	}

	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}

	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public String getApplyMemo() {
		return applyMemo;
	}

	public void setApplyMemo(String applyMemo) {
		this.applyMemo = applyMemo;
	}

	public String getApproveMemo() {
		return approveMemo;
	}

	public void setApproveMemo(String approveMemo) {
		this.approveMemo = approveMemo;
	}

	public Integer getMessageId() {
		return messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}
}
