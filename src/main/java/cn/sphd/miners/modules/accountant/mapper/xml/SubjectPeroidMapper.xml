<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sphd.miners.modules.accountant.mapper.SubjectPeroidMapper" >

  <select id="listPage" parameterType="hashmap" resultType="subjectPeroid" >
    select 
       id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state
    from t_accountant_subject_peroid
  </select>

  <select id="getSingle" parameterType="subjectPeroid" resultType="subjectPeroid">
     select
       id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state
    from t_accountant_subject_peroid
    where id = #{id}
  </select>

  <select id="judgeSubjectPeroid"
          resultType="subjectPeroid">

         select
       id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state
    from t_accountant_subject_peroid
    where subject_id = #{subjectId} and period=#{period}

  </select>

  <select id="judgeAnnualReport" resultType="subjectPeroid">

  SELECT        id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state,quantity,credit_quantity AS creditQuantity,
    debit_quantity AS debitQuantity,unit_price AS unitPrice
     FROM t_accountant_subject_peroid WHERE subject_id=#{subjectId} AND period=#{period} order by id desc limit 1

  </select>

  <select id="getPeroidList" parameterType="hashmap" resultType="subjectPeroid">

    select
    id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit, credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    memo,beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state,quantity,credit_quantity AS creditQuantity,
    debit_quantity AS debitQuantity,unit_price AS unitPrice
    from t_accountant_subject_peroid

    <where>
      CHAR_LENGTH(period)=7 and state &gt; 1 and state!='4' and subject_id=#{subjectId}
      <if test="period!=null"> and period  &lt;= DATE_FORMAT(#{period},'%Y-%m') and period like CONCAT(YEAR(#{period}),'%')</if>
      <if test="period==null"> and period like CONCAT(YEAR(#{createDate}),'%') </if>

    </where>


    ORDER BY period
  </select>

  <select id="getDetailPeroidList" parameterType="hashmap"
          resultType="subjectPeroid">


    select
    id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state,credit_quantity AS creditQuantity,
    debit_quantity AS debitQuantity,unit_price AS unitPrice,quantity
    from t_accountant_subject_peroid

    <where>
      CHAR_LENGTH(period)=10 and state &gt; 1 and state!='4' and subject_id IN
      <foreach item="item" index="index" collection="peroidlist" open="(" separator="," close=")">
        #{item}
      </foreach>

      <if test="period!=null"> and DATE_FORMAT(period,'%Y-%m') &lt;= DATE_FORMAT(#{period},'%Y-%m') and period like CONCAT(YEAR(#{period}),'%')</if>
      <if test="period==null"> and period like CONCAT(YEAR(#{curdate}),'%') </if>

    </where>
    ORDER BY period

  </select>


  <select id="getAnnual" resultType="subjectPeroid">

                 select
       id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state
    from t_accountant_subject_peroid
    where subject_id = #{subjectId} and period=#{period}

  </select>
  <select id="getAnnualperid" resultType="subjectPeroid">
    select
       a.id, a.subject_id AS subjectId, a.subject, a.period, a.balance,
       a.previous_balance as previousBalance, a.summary, a.credit, a.debit,
       a.balance_direction AS balanceDirection,a.beginning_balance AS beginningBalance,a.beginning_direction AS beginningDirection
    from t_accountant_subject_peroid a, t_accountant_subject b
    where a.subject_id = b.id AND a.subject = #{subject} AND a.period = #{period} AND b.org = #{org}

  </select>

  <select id="subjectPeriod" resultType="subjectPeroid">
    select
       a.id, a.subject, a.period, a.balance,
       a.previous_balance as previousBalance, a.summary, a.credit, a.debit,
       a.balance_direction AS balanceDirection
    from t_accountant_subject_peroid a, t_accountant_subject b
    where a.subject = b.subject AND a.period = #{period} AND b.org = #{org}
  </select>

  <select id="getBeginningData" resultType="subjectPeroid">

  SELECT

   id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
    summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
    beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state


  FROM `t_accountant_subject_peroid` WHERE subject_id=#{subjectId} AND CHAR_LENGTH(period)=7 AND (period LIKE CONCAT(YEAR(#{period}),'%'))

    AND
    (state='2' or state='3' or state='4')

   ORDER BY period LIMIT 1;

  </select>
    <select id="getBeginningQuantity"
            resultType="subjectPeroid">

        SELECT


	credit_quantity AS creditQuantity,
	debit_quantity AS debitQuantity,
	quantity


  FROM `t_accountant_subject_peroid` WHERE subject_id=#{subjectId} AND CHAR_LENGTH(period)=7 AND (period LIKE CONCAT(YEAR(#{period}),'%'))

    AND
    (state='2' or state='3')
    AND
    period &lt;= DATE_FORMAT(#{period},'%Y-%m')


   ORDER BY period LIMIT 1;

    </select>

    <select id="getPeroidListBySubjectID" resultType="subjectPeroid">
      SELECT
       id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
        summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
        beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state
      FROM `t_accountant_subject_peroid` WHERE subject_id=#{subjectId}
    </select>
    <select id="getLastPeroid" resultType="subjectPeroid">
      SELECT
       id, subject_id AS  subjectId, subject, period, begin_date AS  beginDate, end_date AS endDate, previous_balance AS  previousBalance, balance,
        summary, credit, debit,credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
        beginning_direction AS beginningDirection,beginning_balance AS beginningBalance ,previous_direction AS previousDirection,balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,state
      FROM `t_accountant_subject_peroid` WHERE subject_id=#{subjectId} and state='3' and CHAR_LENGTH(period) = 7 order by period desc limit 1
    </select>


    <delete id="delete" parameterType="subjectPeroid" >
    delete from t_accountant_subject_peroid
    where id = #{id}
  </delete>

  <delete id="delSubjectPeriod">
    DELETE FROM t_accountant_subject_peroid
    WHERE period like CONCAT('%',#{period},'%') and CHAR_LENGTH(period) &gt;= 7 AND (subject_id in (SELECT id FROM t_accountant_subject WHERE org=#{org}))
  </delete>
  <delete id="delTestData">
    DELETE FROM t_accountant_subject_peroid where id=36917 or id=36950
  </delete>

  <insert id="insert" parameterType="subjectPeroid" useGeneratedKeys="true" keyProperty="id">
    insert into t_accountant_subject_peroid (subject_id, subject,
      period, begin_date, end_date, 
      previous_balance, balance, summary, 
      credit, debit,beginning_direction,previous_direction,balance_direction, beginning_balance ,memo,
      creator, create_name, create_date, 
      updator, update_name, update_date, 
      approve_Item, approve_status, approve_level, 
      auditor, auditor_name, audit_date, 
      operation, apply_memo, approve_memo, 
      message_id,state,credit_accumulative, debit_accumulative,credit_quantity,debit_quantity,unit_price,quantity)
    values ( #{subjectId}, #{subject},
      #{period}, #{beginDate}, #{endDate},
      #{previousBalance}, #{balance}, #{summary},
      #{credit}, #{debit},#{beginningDirection},#{previousDirection},#{balanceDirection},#{beginningBalance}, #{memo},
      #{creator}, #{createName}, #{createDate},
      #{updator}, #{updateName}, #{updateDate},
      #{approveItem}, #{approveStatus}, #{approveLevel},
      #{auditor}, #{auditorName}, #{auditDate},
      #{operation}, #{applyMemo}, #{approveMemo},
      #{messageId},#{state},#{creditAccumulative},#{debitAccumulative},#{creditQuantity},#{debitQuantity},#{unitPrice},#{quantity})
  </insert>

  <insert id="insertImportantData">

    INSERT INTO `t_accountant_subject_peroid` (
    subject_id,
    subject,
    period,
    previous_balance,
    previous_direction,
    balance,
    balance_direction,
    credit,
    debit,
    beginning_balance,
    beginning_direction,
    create_date,
    state,
    summary
    ) SELECT
    subject_id,
    subject,
    #{term},
    previous_balance,
    previous_direction,
    balance,
    end_direction,
    0,
    0,
    beginning_balance,
    beginning_direction,
    #{now},
    '1',
    '本月合计'

    FROM
    t_accountant_subject_history
    WHERE
    org = #{org}
    AND (
    credit_accumulative > 0
    OR debit_accumulative > 0
    OR beginning_balance > 0
    OR previous_balance > 0
    )
    AND subject_id NOT IN

    <foreach item="item" index="index" collection="subidList" open="(" separator="," close=")">
      #{item}
    </foreach>

  </insert>

  <update id="update" parameterType="subjectPeroid" >
    update t_accountant_subject_peroid
    <set >
      <if test="subjectId != null" >
        subject_id = #{subjectId},
      </if>
      <if test="subject != null" >
        subject = #{subject},
      </if>
      <if test="period != null" >
        period = #{period},
      </if>
      <if test="beginDate != null" >
        begin_date = #{beginDate},
      </if>
      <if test="endDate != null" >
        end_date = #{endDate},
      </if>
      <if test="previousBalance != null" >
        previous_balance = #{previousBalance},
      </if>
      <if test="balance != null" >
        balance = #{balance},
      </if>
      <if test="summary != null" >
        summary = #{summary},
      </if>
      <if test="credit != null" >
        credit = #{credit},
      </if>
      <if test="debit != null" >
        debit = #{debit},
      </if>

      <if test="creditAccumulative != null" >
        credit_accumulative = #{creditAccumulative},
      </if>
      <if test="debitAccumulative != null" >
        debit_accumulative = #{debitAccumulative},
      </if>

      <if test="beginningDirection != null" >
        beginning_direction = #{beginningDirection},
      </if>

      <if test="beginningBalance != null" >
        beginning_balance = #{beginningBalance},
      </if>

      <if test="previousDirection != null" >
        previous_direction = #{previousDirection},
      </if>

      <if test="balanceDirection != null" >
        balance_direction = #{balanceDirection},
      </if>

      <if test="memo != null" >
        memo = #{memo},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
      <if test="state != null" >
        state = #{state},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updatePeriodState">
    UPDATE
    t_accountant_subject_peroid a,
    t_accountant_subject b
    SET
    a.state = #{state}
    WHERE
    a.subject_id = b.id AND b.org = #{org} AND a.period like CONCAT('%',#{period},'%') AND CHAR_LENGTH(a.period) &gt;= 7
  </update>

  <update id="updateAccumulative">

    UPDATE `t_accountant_subject_peroid` p,
     t_accountant_subject_history h
    SET p.credit_accumulative = h.credit_accumulative,
     p.debit_accumulative = h.debit_accumulative
    WHERE
        h.org =#{org}
    AND p.period LIKE CONCAT(#{peroid},'%')
    AND p.subject_id = h.subject_id;
  </update>

</mapper>