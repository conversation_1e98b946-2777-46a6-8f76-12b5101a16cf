package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_srm_supplier_post_history")
public class SrmSupplierPostHistory {
    private Integer id;
    private Integer org;
    private Integer supplier;
    private Integer supplierPost;
    private Integer supplierContact;
    private String type;
    private String contact;
    private String mobile;
    private String telephone;
    private String email;
    private String address;
    private String postcode;
    private Integer isDefault;
    private Integer orders;
    private String memo;
    private String keywords;
    private Integer enabled;
    private Date enabledTime;
    private String operation;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private Integer previousId;
    private Integer versionNo;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "supplier")
    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    @Basic
    @Column(name = "supplier_contact")
    public Integer getSupplierContact() {
        return supplierContact;
    }

    public void setSupplierContact(Integer supplierContact) {
        this.supplierContact = supplierContact;
    }

    @Basic
    @Column(name = "type")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Basic
    @Column(name = "contact")
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Basic
    @Column(name = "mobile")
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Basic
    @Column(name = "telephone")
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    @Basic
    @Column(name = "email")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Basic
    @Column(name = "address")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Basic
    @Column(name = "postcode")
    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    @Basic
    @Column(name = "is_default")
    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "keywords")
    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    @Basic
    @Column(name = "enabled")
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time")
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
    @Basic
    @Column(name = "supplier_post")
    public Integer getSupplierPost() {
        return supplierPost;
    }

    public void setSupplierPost(Integer supplierPost) {
        this.supplierPost = supplierPost;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SrmSupplierPostHistory that = (SrmSupplierPostHistory) o;
        return id == that.id && Objects.equals(org, that.org) && Objects.equals(supplier, that.supplier) && Objects.equals(type, that.type) && Objects.equals(contact, that.contact) && Objects.equals(mobile, that.mobile) && Objects.equals(telephone, that.telephone) && Objects.equals(email, that.email) && Objects.equals(address, that.address) && Objects.equals(postcode, that.postcode) && Objects.equals(isDefault, that.isDefault) && Objects.equals(orders, that.orders) && Objects.equals(memo, that.memo) && Objects.equals(keywords, that.keywords) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(operation, that.operation) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, supplier, type, contact, mobile, telephone, email, address, postcode, isDefault, orders, memo, keywords, enabled, enabledTime, operation, creator, createName, createDate, updator, updateName, updateDate, previousId, versionNo);
    }
}
