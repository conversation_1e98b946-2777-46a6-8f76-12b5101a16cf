package cn.sphd.miners.modules.equipment.controller;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquControlImage;
import cn.sphd.miners.modules.equipment.service.ITEquControlImageService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * Controller
 * 
 * <AUTHOR>
 * @date 2024-04-13
 */
@Controller
@RequestMapping("/equ/control/image")
public class TEquControlImageController
{
    private String prefix = "system/image";

    @Autowired
    private ITEquControlImageService tEquControlImageService;

    @GetMapping()
    public String image()
    {
        return prefix + "/image";
    }

    /**
     * 查询列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TEquControlImage tEquControlImage)
    {
        List<TEquControlImage> list = tEquControlImageService.selectTEquControlImageList(tEquControlImage);
        return list;
    }


    /**
     * 新增
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquControlImage tEquControlImage)
    {
        return toAjax(tEquControlImageService.insertTEquControlImage(tEquControlImage));
    }

    /**
     * 修改
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        TEquControlImage tEquControlImage = tEquControlImageService.selectTEquControlImageById(id);
        mmap.put("tEquControlImage", tEquControlImage);
        return prefix + "/edit";
    }

    /**
     * 修改保存
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquControlImage tEquControlImage)
    {
        return toAjax(tEquControlImageService.updateTEquControlImage(tEquControlImage));
    }

    /**
     * 删除
     */

    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(tEquControlImageService.deleteTEquControlImageByIds(ids));
    }
}
