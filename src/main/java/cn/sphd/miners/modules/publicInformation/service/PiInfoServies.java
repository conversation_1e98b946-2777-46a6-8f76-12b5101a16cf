package cn.sphd.miners.modules.publicInformation.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.publicInformation.dto.PiRecord;
import cn.sphd.miners.modules.publicInformation.entity.PiInfo;
import cn.sphd.miners.modules.publicInformation.entity.PiInfoHistory;
import cn.sphd.miners.modules.system.entity.User;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.HashMap;
import java.util.List;

public interface PiInfoServies {

    //获取piInfo实体
    PiInfo piInfoSingle(Integer id);

    //获取piInfoHis实体
    PiInfoHistory piInfoHistorySingle(Integer id);

    //获取机构下所有文件或某文件夹下文件个数
    Long getPiFileNum(Integer org, Integer categoryId);

    //获取目录大小活某目录大小
    Long getCatSize(Integer org, Integer categoryId);

    //获取1级详情和目录下文件
    HashMap<String, Object> getCatFile(User user, Integer categoryId, PageInfo pageInfo);

    //获取某人可见的文件
    HashMap<String, Object> getUserFile(User user, PageInfo pageInfo);

    //上传文件
    HashMap<String, Object> addPiFile(User user, Integer categoryId, String name, String fileSn,
                                    String content, String type, String isSystem, String path, String size,
                                    String version, String companyName, String taxIdNumber,
                                    String address, String bankName, String bankNo, String memo,
                                    String module);

    //换版文件
    HashMap<String, Object> upPiFileVersion(User user, Integer fileId, String reason, String type,
                                            String path, String size, String version,
                                            String companyName, String taxIdNumber,
                                            String address, String bankName, String bankNo,
                                            String memo, String module);

    //检查文件编号是否重复
    String checkPiFileSn(User user, String fileSn);

    //获取文件历史记录
    HashMap<String, Object> getListPiHis(Integer fileId, PageInfo pageInfo);

    //获取文件的详情（需要返回一些记录的次数）
    HashMap<String, Object> getPiInfoMes(Integer fileId);

    //禁用历史文件版本
    Integer disablePifileHis(Integer fileHisId);

    //修改文件名字
    Integer upPiInfoName(User user, Integer fileId, String name);

    //修改文件编号
    Integer upPiInfoFileSn(User user, Integer fileId, String fileSn);

    //移动文件
    Integer upPiInfoCategory(User user, Integer fileId, Integer categoryId);

    //还原文件
    Integer restorePiInfo(User user, Integer fileId);

    //删除文件
    Integer delPiInfo(Integer fileId);

    //获取移动、修改名字或编号的记录
    List<PiRecord> listPiInfoHisByOperation(String operation, Integer fileId);

    //普通搜索文件
    HashMap<String, Object> generalSearchPiInfo(User user, String name, PageInfo pageInfo);
}
