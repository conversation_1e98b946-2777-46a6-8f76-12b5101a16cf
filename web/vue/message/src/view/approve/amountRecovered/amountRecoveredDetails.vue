<template>
  <div id="amountRecoveredDetails"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="需收回的款"></TY_NavTop>
    <div class="detailInfo">
      <div>
        <div class="detail">
          <div v-if="details.srmSupplier && details.poLoan.amount">
          <span>
            <span class="ttl1">供应商：</span><span> {{details.srmSupplier.fullName }}</span><span class="fr">{{ details.srmSupplier.codeName}}</span>
          </span><br/>
            <span><span class="ttl1">需收回的金额</span><span class="fr"> {{details.poLoan.amount && details.poLoan.amount.toFixed(2)}} 元 </span></span>
          </div>
          <div v-if="details.overLoanBiz && details.poLoan.amount">
            <div>
              <span class="ttl1">原始的付款方：</span><span> {{details.overLoanBiz.supplierName }}</span>
            </div>
            <div>
              <span class="ttl1">需收回的金额</span><span class="fr"> {{details.poLoan.amount && details.poLoan.amount.toFixed(2)}} 元 </span>
            </div>
          </div>
          <div @click="getRecoveredPayDetails" class="payOver">
            <span class="el-icon-arrow-right fr"></span>
            <span class="ttl1">已收回</span>
            <span class="fr"> {{details.poLoan.paidTimes || 0 }}笔，共{{(details.poLoan.paidAmount || 0) && details.poLoan.paidAmount.toFixed(2) }}元 </span>
          </div>
        </div>
        <div class="payC" v-if="detailsInfo.id">
          <p>供参考的数据</p>
          <div @click="jumpDetail(0)">
            <span class="el-icon-arrow-right fr"></span>
            <span class="ttl1">申请付款金额</span>
            <span class="fr" v-if="pund === 1"> {{detailsInfo.planAmount && detailsInfo.planAmount.toFixed(2) }}元 </span>
            <span class="fr" v-else> {{detailsInfo.amount && detailsInfo.amount.toFixed(2) }}元 </span>
          </div>
          <div @click="getAlreadyPayDetails">
            <span class="el-icon-arrow-right fr"></span>
            <span class="ttl1">已付款</span>
            <span class="fr" v-if="pund === 3"> {{detailsInfo.paidTimes || 0 }}笔，共{{detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2) }}元 </span>
            <span class="fr" v-else> {{detailsInfo.payNum || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) }}元 </span>
          </div>
        </div>
      </div>
    </div>
    <div>
      <p style="padding:15px 16px 0 16px; ">
        请向供应商索要款项，要到后请及时录入！
      </p>
      <el-form :label-position="left" :rules="rules" label-width="160px" :model="square" ref="square" style="margin-top:10px; ">
        <el-form-item label="所收到款项的型式" prop="method">
          <el-select v-model="square.method" @change="getAccountKinds()" placeholder="请选择（必填）" size="small">
            <el-option label="现金" value="1"></el-option>
            <el-option label="银行转账" value="5"></el-option>
            <el-option label="承兑汇票" value="4"></el-option>
            <el-option label="转账支票" value="3"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="square.method === '1'">
          <el-form-item label="金额" prop="money" >
            <el-input v-model="square.money" placeholder="金额"></el-input><span class="yuan">元</span>
          </el-form-item>
          <el-form-item label="收到日期" prop="receiveDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.receiveDate" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </div>
        <div v-if="square.method === '5'">
          <el-form-item label="金额" prop="money" >
            <el-input v-model="square.money" placeholder="金额"></el-input><span class="yuan">元</span>
          </el-form-item>
          <el-form-item label="到账日期" prop="receiveAccountDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.receiveAccountDate" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="收款银行" prop="financeAccountId">
            <el-select v-model="square.financeAccountId" placeholder="收款银行" size="small">
              <el-option
                v-for="item in financeAccounts"
                :key="item.id"
                :label="(item.isPublic == '1' ? '对公户 ': item.name)+ ' ' + item.bankName + ' ' + item.account"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

        </div>
        <div v-if="square.method === '3'"> <!--  转账支票 -->
          <el-form-item label="到期日" prop="expireDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.expireDate" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="收到日期" prop="receiveDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.receiveDate" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="出具的银行" prop="bankName" >
            <el-input v-model="square.bankName" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item label="出具的单位" prop="originalCorp" >
            <el-input v-model="square.originalCorp" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item label="支票号" prop="returnNo" >
            <el-input v-model="square.returnNo" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item label="金额" prop="money" >
            <el-input v-model="square.money" placeholder="请录入"></el-input><span class="yuan">元</span>
          </el-form-item>
        </div>
        <div v-if="square.method === '4'"> <!--  承兑汇票 -->
          <el-form-item label="到期日" prop="expireDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.expireDate" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="收到日期" prop="receiveDate">
            <el-date-picker type="date" placeholder="请选择" v-model="square.receiveDate" size="small" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="出具的银行" prop="bankName" >
            <el-input v-model="square.bankName" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item label="最初出具的单位" prop="originalCorp" >
            <el-input v-model="square.originalCorp" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item label="汇票号" prop="returnNo" >
            <el-input v-model="square.returnNo" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item label="金额" prop="money" >
            <el-input v-model="square.money" placeholder="请录入"></el-input><span class="yuan">元</span>
          </el-form-item>
        </div>
      </el-form>
      <div class="handle_button" style="text-align: center;" v-if="square.method > 0">
        <span style="width:80%; margin-top:20px;" class="ui-btn ui-btn_info" @click="approve(nowApproveItem)">确 定</span>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#amountRecoveredDetails{
  .detailInfo .detail .payOver{
    line-height: 40px;
  }
  .el-form-item__content{
    text-align: right!important;
    margin-right: 20px!important;
    width: 180px;
  }
  .el-date-editor.el-input {
    width: 182px;
  }
  .el-form-item {
    margin-bottom: 20px!important;
    position: relative;
    .yuan{
      position: absolute;
      right: -16px;
    }
  }
}

</style>
<style lang="less" scoped>
.fr{
  float: right;
}
.detailInfo{
  background-color: #eee;
  padding-bottom: 1px;
  .detail,.payC{
    background: #fff;
    padding: 0 20px;
    margin-bottom: 16px;
    line-height: 30px;
    .ee1{
      border-top: 1px solid #ddd;
    }
  }
  .payC>div{
    line-height: 40px;
    cursor: pointer;
  }
}
.redTxt{ color: red; }
.martop10{ margin-top:10px;}

#amountRecoveredDetails{
  background: #fff;
  overflow-x: hidden!important;
  overflow-y: auto;
  width: 100%;

  .el-icon-arrow-right{ float: right; font-size: 16px;     position: relative;
    top: 11px;}
  .el-form-item__error{ position: relative }

  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  .mar30{ margin-right:30px;  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  .flexC{
    display: flex;margin-bottom:15px ;
    &>span{ display:block; flex: 1;  }
  }
  /deep/ .el-input__icon{
    color: #68717a;
  }
  /deep/.el-select__caret .el-input__icon .el-icon-arrow-up{
    -webkit-transform: rotateZ(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotateZ(-90deg);
    transition: transform 0.3s ease-in-out;
  }
  /deep/ .el-select__caret .el-input__icon .el-icon-arrow-up .is-reverse{
    -webkit-transform: rotateZ(0deg);
    -ms-transform: rotate(0deg);
    transform: rotateZ(0deg);
  }
}
</style>
<script>
export default {
  name: 'amountRecoveredDetails',
  data () {
    return {
      poLoanId: '',
      invoiceID: '',
      pund: '',
      loading: true,
      details: {
        poLoan: {}
      },
      detailsInfo: {
      },
      financeAccounts: [],
      square: {
        'method': '',
        'money': '',
        'receiveDate': '',
        'receiveAccountDate': '',
        'financeAccountId': '',
        'expireDate': '',
        'bankName': '',
        'originalCorp': '',
        'returnNo': ''
      },
      rules: {
        method: [
          { required: true, message: '该项为必填项' }
        ],
        money: [
          { required: true, message: '该项为必填项' }
        ],
        receiveDate: [
          { required: true, message: '该项为必填项' }
        ],
        receiveAccountDate: [
          { required: true, message: '该项为必填项' }
        ],
        financeAccountId: [
          { required: true, message: '该项为必填项' }
        ],
        expireDate: [
          { required: true, message: '该项为必填项' }
        ],
        bankName: [
          { required: true, message: '该项为必填项' }
        ],
        originalCorp: [
          { required: true, message: '该项为必填项' }
        ],
        returnNo: [
          { required: true, message: '该项为必填项' }
        ]

      }
    }
  },
  created () {
    let that = this
    this.poLoanId = this.$route.params.id
    that.getDetail()
  },
  destroyed: function () {
  },
  methods: {
    getAlreadyPayDetails () {
      this.details.pund = this.pund
      this.details.fullName  = this.details.poLoan.supplierName
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceAlready/${this.invoiceID}`
      })
    },
    getRecoveredPayDetails () {
      this.details.pund = this.pund
      this.details.fullName  = this.details.poLoan.supplierName
      this.$store.dispatch('setBeRecoveredInfo', this.details)
      this.$router.push({
        path: `/needRecoveredAmountOk/${this.poLoanId}`
      })
    },
    jumpDetail: function () {
      let that = this
      if (that.pund === 3) {
        that.$router.push({
          path: `/purchaseInvoiceDetails3/34${that.invoiceID}/3/${that.details.poLoan.financePayment}`
        })
      } else {
        that.$router.push({
          path: `/purchaseInvoiceDetails3/34${that.invoiceID}/${that.pund}/0`
        })
      }
    },
    getDetail: function () {
      let that = this
      that.loading = false
      this.axios.post('../../../amountRecovered/getPoLoanDetail.do', { 'poLoanId': that.poLoanId })
        .then(function (response) {
          let res = response.data.data
          console.log('res=', res)
          that.details = res
          // poLoan.type 类型:1-货款(票据处理的),2-预付款
          if(res.poLoan.type == 1){
            that.pund = 0
            that.detailsInfo = res.poPaymentApplication
          }else if(res.poLoan.type == 5){
            that.pund = 3 //多收来的款
            that.detailsInfo = res.overLoanBiz
          }else{
            that.pund = 1
            that.detailsInfo = res.poOrdersPrepayment
          }
          that.invoiceID = that.detailsInfo.id
          console.log('that.detailsInfo' + JSON.stringify(that.detailsInfo))
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    getAccountKinds: function (bool) {
      let that = this
      // bool 只在选完票据跳回来的时候 true
      let json = {
        oid: this.sphdSocket.user.oid,
        isPublic: '',
        isBasic: '',
        accountType: this.square.method,
        accountStatus: 1
      }
      if (Number(json.accountType) === 5) { // 1-现金 ； 2-银行转账
        let accountType = 2
        this.axios.post('../../../account/getAccountKinds.do', { accountType: accountType, accountStatus: 1 })
          .then(response => {
            let res = response.data
            that.loading = false
            that.financeAccounts = res['data']['financeAccounts'] || []
            console.log('that.financeAccounts ', that.financeAccounts)
          }).catch(function (error) {
            console.log(error)
          })
      }
    },
    approve: function () {
      let that = this
      let method = this.square.method
      let money = this.square.money
      let receiveDate = this.square.receiveDate
      let receiveAccountDate = this.square.receiveAccountDate
      let financeAccountId = this.square.financeAccountId
      let expireDate = this.square.expireDate
      let bankName = this.square.bankName
      let originalCorp = this.square.originalCorp
      let returnNo = this.square.returnNo
      let paidAmount = that.details.poLoan.paidAmount || 0
      let isOk = true
      let havConform = false
      let confirmStr = ''
      let successStr = ''
      let msg = '请将必填项补充完整！'
      if (method === '1' || method === '5') {
        if (money === '') {
          isOk = false
          msg = '请输入金额'
        }
        if (method === '1' && receiveDate === '') {
          isOk = false
          msg = '请输入收到日期'
        }
        if (method === '5' && financeAccountId === '') {
          isOk = false
          msg = '请选择收款银行'
        }
        if (method === '5' && receiveAccountDate === '') {
          isOk = false
          msg = '请录入到账日期'
        }
        if (Number(money) > Number(that.details.poLoan.amount) - paidAmount) {
          isOk = false
          msg = '收回的金额可能有误！\n请重新录入！'
        }
      } else if (method === '3' || method === '4') {
        let kee = method === '3' ? '支' : '汇'
        if (money === '') {
          isOk = false
          msg = '请输入金额'
        }
        if (receiveDate === '') {
          isOk = false
          msg = `请输入收到${kee}票的日期`
        }
        if (expireDate === '') {
          isOk = false
          msg = `请输入${kee}票的到期日`
        }
        if (bankName === '') {
          isOk = false
          msg = `请输入出具${kee}票的银行`
        }
        if (originalCorp === '') {
          isOk = false
          msg = method === '3' ? '出具的单位' : '最初出具的单位'
        }
        if (returnNo === '') {
          isOk = false
          msg = `请输入${kee}票号`
        }
        if (Number(money) > Number(that.details.poLoan.amount) - paidAmount) {
          havConform = true
          let cha1 = Number(money) - Number(that.details.poLoan.amount) + Number(paidAmount)
          confirmStr = `将多收${cha1}元？\n“确定”后，该差额将计入常规借款，返还事宜由财务负责。`
        }
      }
      if (isOk) {
        let data = {
          'poLoanId': that.poLoanId,
          'method': method
        }
        data.money = money
        data.receiveDate = receiveDate
        let senUrl = '../../../amountRecovered/amountRecoveredApproval.do'
        if (method === '5') {
          delete data.receiveDate
          data.financeAccountId = financeAccountId
          data.receiveAccountDate = receiveAccountDate
        } else if (method === '3' || method === '4') {
          data.expireDate = expireDate
          data.bankName = bankName
          data.originalCorp = originalCorp
          data.returnNo = returnNo
        }
        if (havConform) {
          this.$confirm(confirmStr, '提示', {
            confirmButtonText: '确  定',
            cancelButtonText: '返回重录',
            type: 'warning'
          }).then(() => {
            that.sendUrlFun(senUrl, data)
          }).catch(() => {
          })
        } else {
          that.sendUrlFun(senUrl, data)
        }
      } else {
        that.$kiko_message(msg)
      }
    },
    sendUrlFun: function (senUrl, data, successStr, allFuamont) {
      let that = this
      that.loading = true
      this.axios.post(senUrl, data)
        .then(function (response) {
          that.loading = false
          let res = response.data.data
          that.$kiko_message(res.content)
          if (res.content === '操作成功' || res.content === '修改成功') {
            that.$kiko_message(res.content)
            that.$router.push({
              path: `/needRecoveredAmount`
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  }
}
</script>
