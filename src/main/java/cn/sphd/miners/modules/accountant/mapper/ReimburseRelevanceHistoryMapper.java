package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectRelevance;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectRelevanceHistory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface ReimburseRelevanceHistoryMapper extends BaseMapper<TAccountantSubjectRelevanceHistory> {


    List<TAccountantSubjectRelevanceHistory> getCategoryRelevanceHistory(TAccountantSubjectRelevanceHistory param);

    void deleteByOrg(int org);
}