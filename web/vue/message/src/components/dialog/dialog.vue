<template>
  <div class="bounce" v-show="showMask">
    <div class="bounceContainer">
      <div class="bounceHead">{{title}}<div class="close-btn" @click="closeMask"><i class="iconfont icon-close"></i></div></div>
      <div class="bounceCon"><slot></slot></div>
      <div class="bounceFoot"><slot name="foot"></slot></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    value: {},
    // 类型包括 defalut 默认， danger 危险， confirm 确认，
    type: {
      type: String,
      default: 'default'
    },
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      showMask: false
    }
  },
  methods: {
    closeMask () {
      this.showMask = false
    },
    closeBtn () {
      this.$emit('cancel')
      this.closeMask()
    },
    dangerBtn () {
      this.$emit('danger')
      this.closeMask()
    },
    confirmBtn () {
      this.$emit('confirm')
      this.closeMask()
    }
  },
  mounted () {
    this.showMask = this.value
  },
  watch: {
    value (newVal, oldVal) {
      this.showMask = newVal
    },
    showMask (val) {
      this.$emit('input', val)
    }
  }
}
</script>
