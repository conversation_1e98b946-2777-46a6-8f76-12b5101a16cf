package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.entity.SubjectChooseParams;
import cn.sphd.miners.modules.accountant.entity.UpdateVoucherParams;
import cn.sphd.miners.modules.accountant.mapper.SettleMapper;
import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 17-1-4.
 */
@Controller
@RequestMapping("/accountant")
public class SubjectSelectController {

    @Autowired
    SubjectSelectService subjectSelectService;
    @Autowired
    DataService dataService;
    @Autowired
    SettleMapper settleMapper;

    private static  final String SUCCESS="1";
    private static  final String ERROR="0";

//    @Autowired
//    LoadingCacheService loadingCacheService;

//    @RequestMapping("/test.do")
//    public void test(HttpSession session, HttpServletResponse response) throws Exception{
//        HttpClient client = new DefaultHttpClient();
//        HttpPost method = new HttpPost("http://wonderss.mntfp.com:8888/about/response.do");
//        List<NameValuePair> list = new ArrayList<NameValuePair>();
//        BasicNameValuePair param = new BasicNameValuePair("hi","i am tykj");
//        list.add(new BasicNameValuePair("hi","i am tykj"));
//        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list,"utf-8");
//        method.setEntity(entity);
//        HttpResponse respons = client.execute(method);
//        HttpEntity entity2 = respons.getEntity();
//        String retStr = EntityUtils.toString(entity2);
//        System.out.println(retStr);
//        WriteJSONUtil.writeJSON(retStr,response);
//    }

    /*
    * 财务数据(正常待选择、修改待选择)进行科目选择的时候
    * summary               摘要
    * belong_period         凭证日期 1-本月 2-非本月
    * mode                  1-一借一贷
    * price                 发生金额
    * category              凭证字 1-转
    * subjectBorrow         上传的借方的json数据，[{subject:1001,price:100,subjectNames}]
    * subjectLoan           上传的贷方的json数据，[{subject:5001,price:100,subjectNames}]
    * is_account            是否下帐 0-不予下帐 1-下帐
    * reason                不予下帐理由
    * bill_detail           票据详情id
    * memo                  备注
    * bill_period           票据所属月份 1-本月 2-非本月
    * source                凭证来源 0-财务 1-会计录入 2-结转损益 3-个人报销
    * approve_status        凭证状态
    * pricetype             0-收入 1-支出
    * purpose               目的
    * bill_quantity         票据数量
    * kind
    * operatorName          经手人
    * operatorFinance       财务经手人
    * billDate              票据日期
    * detailId              财务明细ID
    * cashjson              现金流量的json数组，包含cashitem和cashes
    * */
    @RequestMapping("/selectSubject.do")
    @ResponseBody
    public JsonResult selectSubject(User user, SubjectChooseParams params) {
        JsonResult result = subjectSelectService.insertVoucher(user,params);
        return result;
    }

    /*得到待审批、已批准、已驳回的数据*/
    /*approve_status 1-正常待审批        5-不予下帐待审批     4-修改待审批
                     2-正常审批通过      7-财务修改审批通过   9-不予下帐审批通过
                     6-不予下帐已驳回    8-财务修改驳回       3-正常已驳回
                     */
    @RequestMapping("/getApprove.do")
    public void getApprove(User user, HttpServletResponse response, int approve_status) {
//        User user = (User) session.getAttribute("user");
//        Integer oid = (Integer) session.getAttribute("oid");
        int oid = user.getOid();
        JSONArray list = subjectSelectService.queryApprove(oid,approve_status);
        JSONObject result = new JSONObject();
        result.put("approveList",list);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    /*
    * 已批准和已驳回中的修改接口
    * id 凭证id
    * voucher_id 历史表的id，存在则说明修改的是历史表的数据
    * approve_status
    * belong_peroid
    * subjectBorrow
    * subjectLoan
    * reason                如果选择的不予下帐，则需要填写理由
    * cashjson              现金流量的json数组，包含cashitem和cashes
    * price         票据金额
    * */
    @RequestMapping("/updateVoucher.do")
    public void updateVoucher(
                              HttpServletResponse response,
                              UpdateVoucherParams params) {

//        int belong_peroid = params.getBelong_peroid();
//        User user = (User) session.getAttribute("user");
//        int oid = user.getOid();
//        TAccountantSettle selected = subjectSelectService.judgeSettled(belong_peroid,oid);
//        String state = "1";
//        if (selected != null)
//            state = selected.getState();
        String subjectBorrow = params.getSubjectBorrow();
//        if(!"2".equals(state))
//        {
            if (subjectBorrow == null || "".equals(subjectBorrow))
            {
                WriteJSONUtil.writeObject(-1,"res",response);
            }
            else
            {
                int res = subjectSelectService.insertVoucherHistory(params);
                WriteJSONUtil.writeObject(res,"res",response);
            }

//        }
//        else
//        {
//            WriteJSONUtil.writeObject(-2,"res",response);
//        }

    }

    /*
    * 审批时访问的接口，因为待审批的数据不仅来自凭证表也来自凭证历史表
    * 所以得传一个参数表明要审批的数据是来自哪个表的
    * voucher_id，如果该值大于0表示来自历史表
    * cashjson              现金流量的json数组，包含cashitem和cashes
    * */
    @RequestMapping("/setApprove.do")
    public void setApprove(User user, HttpServletResponse response, SubjectChooseParams params, int voucher_id) {
//        User user = (User) session.getAttribute("user");
        int id = params.getId();
        int approve_status = params.getApprove_status();
        String cashjson = params.getCashjson();
        int res = subjectSelectService.setApprove(id,approve_status,voucher_id,cashjson,user);
        WriteJSONUtil.writeObject(res,"res",response);
    }

    /*
    * 点击科目设置页中的启用科目名称关联功能时触发
    * 在t_accountant_setting表里增加一条数据，说明已和物料、商品等模块建立联系
    * 科目会随着商品等的新建而新建
    * res   1   关联成功   0   关联失败
    * state Y-开启关联，N-关闭关联
    * */
    @RequestMapping("/setRelation.do")
    public void setRelation(User user, HttpServletResponse response,String state) {
//        User user = (User) session.getAttribute("user");
        int res = subjectSelectService.setRelation(user,state);
        WriteJSONUtil.writeObject(res,"res",response);
    }


    /*
    * 获取该登录机构的t_accountant_setting里面的值
    * 如果有值的话需要在科目设置页里显示“启用科目名称关联功能”
    * 否则不显示
    * res   1`已关联   0   未关联
    * */
    @RequestMapping("/getRelation.do")
    public void getRelation(User user, HttpServletResponse response) {
//        User user = (User) session.getAttribute("user");
        int res = subjectSelectService.getRelation(user);
        WriteJSONUtil.writeObject(res,"res",response);
    }


}
