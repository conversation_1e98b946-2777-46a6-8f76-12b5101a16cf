<!--修改记录-->
<template>
  <div class="head">
    <div>
      <PageHeader title="修改记录" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <div class="changeBox">
    <p><span>{{ tableData.length === 1 ? '原始信息' : '当前资料为第'+ (tableData.length - 1) +'次修改后的结果' }}  </span></p>
    <p>
      <span>
      <span>创建人/修改人</span>&nbsp;&nbsp;&nbsp;
      <span>{{ lastItem.updateName }} {{ new Date(lastItem.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</span></span>
    </p>
  </div>
  <div class="emTableBox">
    <table class="emTable">
      <thead>
      <th width="30%">资料状态</th><th width="20%">操作</th><th width="50%">创建人/修改人</th>
      </thead>
      <tbody>
      <tr v-for="(item, index) in tableData" :key="index">
        <td><span>{{ index === 0 ? '原始信息' :'第' + index + '次修改后' }}</span></td>
        <td @click="logDetail(item, index)" class="tdC"><span class="ty-linkBtn-green">查看</span> </td>
        <td>{{ item.updateName }} {{ new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "changeLog",
  data() {
    return {
      lastItem: {},
      tableData: []
    }
  },
  mixins: [finance],
  created() {
    let changeLog = localStorage.getItem('changeLog')
    if(changeLog){
      let params = JSON.parse(changeLog)
      this.getRecordsFun(params)
    }else{
      this.$message.error('获取数据失败！')
      this.$router.go(-1)
    }
  },
  methods: {
    getRecordsFun(params){
      this.financeApi.getRecords(params).then(res1=>{
        let res = res1.data.data
        let list = res.personnelPayHistories || []
        this.tableData = list
        this.lastItem = list[list.length -1]

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('获取数据失败！')
        this.$router.go(-1)
      })
    },
    logDetail(item, index){
      item.index00 = index
      localStorage.setItem('changeLogRaw', JSON.stringify(item))
      this.$router.push("/changeLogRaw")
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/account.scss";
@import "src/style/common.scss";

.emTable {
  .tdC{
    position: relative;
    .ty-linkBtn-green{
      position: relative; top: 6px;
    }
  }
}
</style>