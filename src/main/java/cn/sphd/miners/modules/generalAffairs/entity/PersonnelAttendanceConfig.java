package cn.sphd.miners.modules.generalAffairs.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

/**
 * Created by Administrator on 2018/4/9.
 * 考勤设置表
 */
@Entity
@Table(name = "t_personnel_attendance_config")
public class PersonnelAttendanceConfig implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构id

    @Column(name="record"   , nullable=true , unique=false)
    private Integer record;//修改记录id 【若此值为空，那么说明是系统第一次添加的考勤设置规则】

    @Column(name="rule_code"  , length=10 , nullable=true , unique=false)
    private String ruleCode;//规则代码

    @Column(name="rule_name"  , length=50 , nullable=true , unique=false)
    private String ruleName;//规则名称

    @Column(name="rule_desc"  , length=255 , nullable=true , unique=false)
    private String ruleDesc;//规则描述

    @Column(name="enabled"  ,  nullable=true , unique=false)
    private Boolean enabled;//是否有效,true(1)-有效,false(0)-无效

    @Column(name="open_date"   , nullable=true , unique=false)
    private Date openDate;//规则启用日期

    @Column(name="close_date"   , nullable=true , unique=false)
    private Date closeDate;//规则截止日期

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//类型:1-正常班,2-倒班

    @Column(name="input_time"   , nullable=true , unique=false)
    private Date inputTime;//每日考勤录入时间(如未按时录入,则为旷工)

    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;//上班时间

    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;//下班时间

    @Column(name="middle_break"  ,  nullable=true , unique=false)
    private Boolean middleBreak;//是否中午考勤,true-是

    /**
     * breakBegin和breakEnd不为空，统计出勤或旷工的时候扣除午休时间，不受middleBreak的值限制，如果无须统计，请至少设置一个时间值为空值。
     * 目前只可以设置一个餐食休息时间，如果后续有多个休息时间的需求，可以把设置表和日表的breakTime设置成json。
     * <AUTHOR>
     * @since 4.0
     * @date 2025-06-10 11:39:00
     **/
    @Column(name="break_begin"   , nullable=true , unique=false)
    private Date breakBegin;//午休开始时间

    @Column(name="break_end"   , nullable=true , unique=false)
    private Date breakEnd;//午休结束时间

    @Column(name="effect_date"   , nullable=true , unique=false)
    private Date effectDate;//'修改录入时间生效日期'

    @Column(name="old_create_time"   , nullable=true , unique=false)
    private Date oldCreateTime;//'原先创建时间'（即是原来的考勤录入时间）

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name = "operation", nullable=true , unique=false)
    private Byte operation;//操作:0-正常,1-增,2-删,3-改,4-修改录入日期,5-修改迟到早退和请假期设置

    @Column(name="attendance_pattern")
    private Byte attendancePattern;  //考勤模式:1-考勤宝,2-手工录入(默认)

    @Column(name="late_limit")
    private Short lateLimit;  //`late_limit` SMALLINT DEFAULT NULL COMMENT '迟到时限(分钟)'

    @Column(name="early_limit")
    private Short earlyLimit;  //`early_limit` SMALLINT DEFAULT NULL COMMENT '早退时限(分钟)'

    @Column(name="leave_work"   , nullable=true , unique=false)
    private Boolean leaveWork;  //请假到岗是否使用考勤宝:0-不使用(默认),1-使用

    @Column(name="p1_before")
    private Short p1Before;  //`p1_before` SMALLINT DEFAULT NULL COMMENT '上班赦免(分钟),null全天可用'

    @Column(name="p1_after")
    private Short p1After;  //`p1_after` SMALLINT DEFAULT NULL COMMENT '下班赦免(分钟),null全天可用'

    @Column(name="p2_before")
    private Short p2Before;  //`p2_before` SMALLINT DEFAULT NULL COMMENT '请假离岗赦免(分钟),null全天可用'

    @Column(name="p2_after")
    private Short p2After;  //`p2_after` SMALLINT DEFAULT NULL COMMENT '请假返岗赦免(分钟),null全天可用'

    @Column(name="omit_before",columnDefinition = "59999")
    private Integer omitBefore;  //`omit_before` INTEGER DEFAULT 59999 COMMENT '到岗忽略（毫秒）,null没有忽略时间'

    @Column(name="omit_after",columnDefinition = "0")
    private Integer omitAfter;  //`omit_after` INTEGER DEFAULT 59999 COMMENT '离岗忽略（毫秒）,null没有忽略时间'

    @Column(name="previous_id"  , length=100 , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    //与考勤部门设置表
    @OrderBy
    @OneToMany(targetEntity=PersonnelAttendanceDepartmentConfig.class, fetch= FetchType.LAZY, mappedBy="rule", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<PersonnelAttendanceDepartmentConfig> personnelAttendanceDepartmentConfigHashSet = new HashSet<>();

    //与考勤部门设置表
    @JsonIgnore@JSONField(serialize = false)
    @OneToMany(targetEntity=PersonnelAttendanceConfigHistory.class, fetch= FetchType.LAZY, mappedBy="rule", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<PersonnelAttendanceConfigHistory> personnelAttendanceConfigHistoryHashSet = new HashSet<PersonnelAttendanceConfigHistory>();

    //与考勤设置例外表
//    @OneToMany(targetEntity=PersonnelAttendanceException.class, fetch= FetchType.LAZY, mappedBy="rule", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
//    private Set<PersonnelAttendanceException> personnelAttendanceExceptionHashSet = new HashSet<PersonnelAttendanceException>();

    @Transient
    private String inputTimeString;//每日考勤录入时间(如未按时录入,则为旷工)

    @Transient
    private String beginTimeString;//上班时间

    @Transient
    private String endTimeString;//下班时间

    @Transient
    private String breakBeginString;//午休开始时间

    @Transient
    private String breakEndString;//午休结束时间

    @Transient
    private List<PersonnelAttendanceDepartmentConfig> personnelAttendanceDepartmentConfigList;

    @Transient
    private String departmentIds;//部门id的集合

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getOpenDate() {
        return openDate;
    }

    public void setOpenDate(Date openDate) {
        this.openDate = openDate;
    }

    public Date getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(Date closeDate) {
        this.closeDate = closeDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getInputTime() {
        return inputTime;
    }

    public void setInputTime(Date inputTime) {
        this.inputTime = inputTime;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getMiddleBreak() {
        return middleBreak;
    }

    public void setMiddleBreak(Boolean middleBreak) {
        this.middleBreak = middleBreak;
    }

    public Date getBreakBegin() {
        return breakBegin;
    }

    public void setBreakBegin(Date breakBegin) {
        this.breakBegin = breakBegin;
    }

    public Date getBreakEnd() {
        return breakEnd;
    }

    public void setBreakEnd(Date breakEnd) {
        this.breakEnd = breakEnd;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Set<PersonnelAttendanceDepartmentConfig> getPersonnelAttendanceDepartmentConfigHashSet() {
        return personnelAttendanceDepartmentConfigHashSet;
    }

    public void setPersonnelAttendanceDepartmentConfigHashSet(Set<PersonnelAttendanceDepartmentConfig> personnelAttendanceDepartmentConfigHashSet) {
        this.personnelAttendanceDepartmentConfigHashSet = personnelAttendanceDepartmentConfigHashSet;
    }

    public Set<PersonnelAttendanceConfigHistory> getPersonnelAttendanceConfigHistoryHashSet() {
        return personnelAttendanceConfigHistoryHashSet;
    }

    public void setPersonnelAttendanceConfigHistoryHashSet(Set<PersonnelAttendanceConfigHistory> personnelAttendanceConfigHistoryHashSet) {
        this.personnelAttendanceConfigHistoryHashSet = personnelAttendanceConfigHistoryHashSet;
    }

    //    public Set<PersonnelAttendanceException> getPersonnelAttendanceExceptionHashSet() {
//        return personnelAttendanceExceptionHashSet;
//    }
//
//    public void setPersonnelAttendanceExceptionHashSet(Set<PersonnelAttendanceException> personnelAttendanceExceptionHashSet) {
//        this.personnelAttendanceExceptionHashSet = personnelAttendanceExceptionHashSet;
//    }


    public String getInputTimeString() {
        return inputTimeString;
    }

    public void setInputTimeString(String inputTimeString) {
        this.inputTimeString = inputTimeString;
    }

    public String getBeginTimeString() {
        return beginTimeString;
    }

    public void setBeginTimeString(String beginTimeString) {
        this.beginTimeString = beginTimeString;
    }

    public String getEndTimeString() {
        return endTimeString;
    }

    public void setEndTimeString(String endTimeString) {
        this.endTimeString = endTimeString;
    }

    public String getBreakBeginString() {
        return breakBeginString;
    }

    public void setBreakBeginString(String breakBeginString) {
        this.breakBeginString = breakBeginString;
    }

    public String getBreakEndString() {
        return breakEndString;
    }

    public void setBreakEndString(String breakEndString) {
        this.breakEndString = breakEndString;
    }

    public Integer getRecord() {
        return record;
    }

    public void setRecord(Integer record) {
        this.record = record;
    }

    public Date getEffectDate() {
        return effectDate;
    }

    public void setEffectDate(Date effectDate) {
        this.effectDate = effectDate;
    }

    public Date getOldCreateTime() {
        return oldCreateTime;
    }

    public void setOldCreateTime(Date oldCreateTime) {
        this.oldCreateTime = oldCreateTime;
    }

    public List<PersonnelAttendanceDepartmentConfig> getPersonnelAttendanceDepartmentConfigList() {
        return personnelAttendanceDepartmentConfigList;
    }

    public void setPersonnelAttendanceDepartmentConfigList(List<PersonnelAttendanceDepartmentConfig> personnelAttendanceDepartmentConfigList) {
        this.personnelAttendanceDepartmentConfigList = personnelAttendanceDepartmentConfigList;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Byte getAttendancePattern() {
        return attendancePattern;
    }

    public void setAttendancePattern(Byte attendancePattern) {
        this.attendancePattern = attendancePattern;
    }

    public Short getLateLimit() {
        return lateLimit;
    }

    public void setLateLimit(Short lateLimit) {
        this.lateLimit = lateLimit;
    }

    public Short getEarlyLimit() {
        return earlyLimit;
    }

    public void setEarlyLimit(Short earlyLimit) {
        this.earlyLimit = earlyLimit;
    }

    public Boolean getLeaveWork() {
        return leaveWork;
    }

    public void setLeaveWork(Boolean leaveWork) {
        this.leaveWork = leaveWork;
    }

    public Short getP1Before() {
        return p1Before;
    }

    public void setP1Before(Short p1Before) {
        this.p1Before = p1Before;
    }

    public Short getP1After() {
        return p1After;
    }

    public void setP1After(Short p1After) {
        this.p1After = p1After;
    }

    public Short getP2Before() {
        return p2Before;
    }

    public void setP2Before(Short p2Before) {
        this.p2Before = p2Before;
    }

    public Short getP2After() {
        return p2After;
    }

    public void setP2After(Short p2After) {
        this.p2After = p2After;
    }

    public Integer getOmitBefore() {
        return omitBefore;
    }

    public void setOmitBefore(Integer omitBefore) {
        this.omitBefore = omitBefore;
    }

    public Integer getOmitAfter() {
        return omitAfter;
    }

    public void setOmitAfter(Integer omitAfter) {
        this.omitAfter = omitAfter;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(String departmentIds) {
        this.departmentIds = departmentIds;
    }
}
