package cn.sphd.miners.modules.project.controller;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.project.entity.ProjectAttachment;
import cn.sphd.miners.modules.project.entity.ProjectBase;
import cn.sphd.miners.modules.project.service.ProjectAttachmentService;
import cn.sphd.miners.modules.project.service.ProjectBaseService;
import cn.sphd.miners.modules.project.service.impl.ProjectUsing;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserRoleService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2018/5/31.
 * 新版项目管理
 */
@Controller
@RequestMapping("/project")
public class ProjectController {

    @Autowired
    ProjectAttachmentService projectAttachmentService;
    @Autowired
    ProjectBaseService projectBaseService;
    @Autowired
    UserService userService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    UploadService uploadService;

    /**
    * <AUTHOR>
    * @Date 2018/6/1 11:33
    *跳转综合管理页面
    */
    @RequestMapping("/toProjectBaseManage.do")
    public String projectBaseManage(HttpServletRequest request,Model model){

        return "/projectsManage/baseManage";
    }

    /**
    * <AUTHOR>
    * @Date 2018/6/1 11:34
    * 跳转 立案模块页面
    */
    @RequestMapping("/toProjectBaseIndex.do")
    public String projectBaseIndex(HttpServletRequest request,Model model){
        return "/projectsManage/buildProject";
    }

    /**
    * <AUTHOR>
    * @Date 2018/6/1 11:35
    * 项目 状态列表
     * state 状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
    */
    @ResponseBody
    @RequestMapping("/projectBaseList.do")
    public void projectBaseList(User user, String state, PageInfo pageInfo, HttpServletResponse response) throws IOException {
        List<ProjectBase> pList;
        User approvalUser=userService.getUserByCoreCode(user.getOid(),"projectCore");
        if (approvalUser.getUserID().equals(user.getUserID())) {
            pList = projectBaseService.getProjectBaseByState(user.getOid(), state,null,null);
        }else {
            pList=projectBaseService.getProjectBaseByState(user.getOid(),state,user.getUserID(),null);
        }
        for (ProjectBase p:pList){
            if (p.getInitialReceipter()!=null)
                p.setRegisterName(userService.getUserByID(p.getInitialReceipter()).getUserName());
            if (p.getRegisterApprover()!=null)
                p.setRegisterApproveName(userService.getUserByID(p.getRegisterApprover()).getUserName());
            if (p.getSettler()!=null)
                p.setSettleName(userService.getUserByID(p.getSettler()).getUserName());
            if (p.getSettleApprover()!=null)
                p.setSettleApproveName(userService.getUserByID(p.getSettleApprover()).getUserName());
        }
        ObjectToJson.objectToJson(pList,new String[]{"projectBase","projectAttachmentHashSet"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/6/1 11:54
    * 新增项目立案
    */
    @ResponseBody
    @RequestMapping("/addProjectBase.do")
    public void addProjectBase(User user, ProjectBase projectBase, String pathList, HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        projectBase.setOrg(user.getOid());
        projectBase.setCreateDate(new Date());
        projectBase.setCreator(user.getUserID());
        projectBase.setUpdateName(user.getUserName());
        projectBase.setCreateName(user.getUserName());
        projectBase.setUpdateDate(new Date());
        projectBase.setUpdator(user.getUserID());
        if (DateUtils.getYear(projectBase.getBeginTime())==1970){
            projectBase.setBeginTime(null);
        }
        if (DateUtils.getYear(projectBase.getEndTime())==1970){
            projectBase.setEndTime(null);
        }
        User approvalUser=userService.getUserByCoreCode(user.getOid(),"projectCore");
        if (approvalUser.getUserID().equals(user.getUserID())){
            projectBase.setState("3");//申请人是 核心人物，直接通过 无需审批
        }else {
            projectBase.setState("1");
        }
        projectBase.setRegisterApproveTime(new Date());//立案批准时间
        projectBase.setRegisterApprover(approvalUser.getUserID());//立案批准人ID
        projectBase.setProcessor(approvalUser.getUserID());//处理人
        projectBase.setInitialHandler(approvalUser.getUserID());//初始处理人
        projectBase.setInitialReceipter(user.getUserID());//立案人
        projectBaseService.addProjectBase(projectBase);

        if (projectBase.getState().equals("1")){
            ApprovalProcess approvalProcess=new ApprovalProcess();
            approvalProcess.setCreateDate(new Date());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setApproveStatus("1");
            approvalProcess.setBusiness(projectBase.getId());//业务id
            approvalProcess.setBusinessType(12);
            approvalProcess.setToUser(approvalUser.getUserID());
            approvalProcess.setToUserName(approvalUser.getUserName());
            approvalProcess.setUserName(approvalUser.getUserName());
            approvalProcess.setAskName(user.getUserName());
            approvalProcess.setLevel(1);
            approvalProcess.setType(1);//1-项目立案 2-项目结案
            approvalProcess.setDescription("立案申请");
            approvalProcessService.saveApprovalProcess(approvalProcess);

//            UserMessage userMessage = new UserMessage();
//            userMessage.setUser(user);
//            userMessage.setApprovalStatus(1);
//            userMessage.setHandleId(approvalUser.getUserID().toString());
//            userMessage.setMessageId(approvalProcess.getId());
//            SimpleDateFormat sdf=new SimpleDateFormat("yyyy年MM月dd日");
//            userMessage.setEventType("项目管理");
//            userMessage.setIllustrate(sdf.format(new Date())+"有新的项目管理立案申请");
//            userMessage.setMessageType("14");
//            userMessage.setState(1);
//            userMessage.setReceiveUserId(approvalUser.getUserID());  //接收消息人
//            userMessage.setCreateDate(new Date());
//            userMessage.setPersonnelReimburId(projectBase.getId());
//            userMessage.setType(1);//1-立案 2-结案
//            userMessageService.addUserMassage(userMessage);

        }else {
            ApprovalProcess approvalProcess=new ApprovalProcess();
            approvalProcess.setCreateDate(new Date());
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setApproveStatus("2");
            approvalProcess.setBusiness(projectBase.getId());//业务id
            approvalProcess.setBusinessType(12);
            approvalProcess.setToUser(approvalUser.getUserID());
            approvalProcess.setToUserName(approvalUser.getUserName());
            approvalProcess.setUserName(approvalUser.getUserName());
            approvalProcess.setAskName(user.getUserName());
            approvalProcess.setLevel(1);
            approvalProcess.setType(1);//1-项目立案 2-项目结案
            approvalProcess.setDescription("立案申请");
            approvalProcessService.saveApprovalProcess(approvalProcess);
        }
        if (pathList!=null) {
            JSONArray jsonArray = JSONArray.fromObject(pathList);
            List list=JSONArray.toList(jsonArray);
            for (int i=0;i<list.size();i++) {
                JSONObject jo = JSONObject.fromObject(list.get(i));

                ProjectAttachment projectAttachment = new ProjectAttachment();
                projectAttachment.setCreator(user.getUserID());
                projectAttachment.setCreateDate(new Date());
                projectAttachment.setCreateName(user.getUserName());
                projectAttachment.setType("2");
                projectAttachment.setPath(jo.getString("path"));
                projectAttachment.setTitle(jo.getString("fileName"));
                projectAttachment.setOrders(100);
                projectAttachment.setProjectBase(projectBase);
                projectAttachmentService.saveProjectAttachment(projectAttachment);
                uploadService.addFileUsing(new ProjectUsing(projectAttachment.getId()), projectAttachment.getPath(), projectBase.getProjectName(), user, "项目管理");

            }
        }
        map.put("status",1);//成功
        ObjectToJson.objectToJson1(map,new String[]{""},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/6/1 14:27
    * 公用上传附件接口
    */
    @ResponseBody
    @RequestMapping("/uploadFiles.do")
    public void uploadFiles(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "file",required = false) MultipartFile[] files) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (files!=null){
            List<Map<String,String>> pathList=new ArrayList<>();
            for (MultipartFile f : files) {
                Map<String,String> imgMap=new HashMap<>();
                String path = request.getServletContext().getRealPath("/upload");//项目路径
                String datepath = new SimpleDateFormat("/yyyy/MM/dd/").format(new Date());//当前时间路径

                String imgName = f.getOriginalFilename();
                if (!imgName.equals("")) {
                    File dir = new File(path + datepath);
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }

                    try {
                        FileUtils.copyInputStreamToFile(f.getInputStream(), new File(path + datepath + imgName));//如果扩展名属于允许上传的类型，则创建文件
                    } catch (IOException e) {
                        System.out.println("项目管理附件上传：upload" + datepath + imgName);
                        e.printStackTrace();
                    }
                    imgMap.put("path","upload" + datepath + imgName);
                    imgMap.put("fileName",imgName);
                    pathList.add(imgMap);
                }else {
                    map.put("status", 2);//无文件名
                }

            }
            map.put("status", 1);//成功
            map.put("pathList",pathList);
        }else {
            map.put("status", 0);//失败

        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

    }
    
    /**
    * <AUTHOR>
    * @Date 2018/6/1 14:48
    * 查询立案详情
    */
    @ResponseBody
    @RequestMapping("/getProjectBaseInfo.do")
    public void getProjectBaseInfo(User user,Integer id,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (id!=null){
            ProjectBase p=projectBaseService.getProjectBaseById(id);
            if (p.getInitialReceipter()!=null)
            p.setRegisterName(userService.getUserByID(p.getInitialReceipter()).getUserName());
            if (p.getRegisterApprover()!=null)
            p.setRegisterApproveName(userService.getUserByID(p.getRegisterApprover()).getUserName());
            if (p.getSettler()!=null)
            p.setSettleName(userService.getUserByID(p.getSettler()).getUserName());
            if (p.getSettleApprover()!=null)
            p.setSettleApproveName(userService.getUserByID(p.getSettleApprover()).getUserName());
            List<ApprovalProcess> approvalProcessList=approvalProcessService.getApprovalProcessByBusinessDesc(id,12,null);

            User approvalUser = userService.getUserByCoreCode(user.getOid(), "projectCore");
//            if (!approvalUser.getUserID().equals(user.getUserID())) {
//                List<UserMessage> userMessages = messageService.getMessageByPersonnelReimburseId(p.getId(),1,user.getUserID(),14);//消息类型  1-财务  2-加班  3-请假   4-报销
//                if (userMessages.size()>0){
//                    for (UserMessage u:userMessages) {
//                        u.setState(2);  //对消息的处理  1-未处理 2-已处理
//                        userMessageService.updateUserMassage(u);  //更新消息提醒的状态
//                    }
//                }
//            }
            map.put("status", 1);//成功
            map.put("project",p);
            map.put("approvalProcessList",approvalProcessList);
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"projectBase","approvalFlow","reimburse","personnelLeave","personnelOvertime"},response);

    }

    /**
    * <AUTHOR>
    * @Date 2018/6/1 15:06
    * 获取立案者人员列表
     * type 1-投诉管理核心人物 2-投诉管理立案者 3-投诉管理处理者 4-投诉管理查看者
     * 5-项目管理核心人物  6-项目管理立案者 7-持续改进核心人物  8-持续改进立案者
    */
    @ResponseBody
    @RequestMapping("/getFilingUsers.do")
    public void getFilingUsers(User user,Integer type,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (type!=null){
            Integer oid= user.getOid();
            String code=userRoleService.getUserRoleCodeByType(type);//
            List<User> codeUsers = userService.getUserListByCoreCodeLocking(oid,code);//
            map.put("status", 1);//成功
            map.put("codeUsers",codeUsers);
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","user","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","handleTime","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/6/1 15:24
    * 变更立案人接口
    */
    @ResponseBody
    @RequestMapping("/updateInitialReceipter.do")
    public void updateInitialReceipter(User user,Integer id,Integer userId,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if(id!=null&&userId!=null){
            ProjectBase p=projectBaseService.getProjectBaseById(id);
//            messageService.updateMessagesByUidMessageType(p.getInitialReceipter(),"14");//把历史立案者未处理消息变为已处理

            p.setInitialReceipter(userId);//变更可操作立案者id
            p.setUpdator(user.getUserID());
            p.setUpdateName(userService.getUserByID(userId).getUserName());
            projectBaseService.updateProjectBase(p);
            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }



    /**
    * <AUTHOR>
    * @Date 2018/6/4 9:16
    * 批准驳回  立案申请
    */
    @ResponseBody
    @RequestMapping("/approvalProjectCase.do")
    public void approvalProjectCase(User user,Integer id,String approvalStatus,String registerRejectReason,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        if (id!=null&&!"".equals(approvalStatus)&&approvalStatus!=null) {
            ProjectBase pb = projectBaseService.getProjectBaseById(id);
            List<ApprovalProcess> aps = approvalProcessService.getApprovalProcessByBusiness(id, 12, null);
            ApprovalProcess  app= aps.get(aps.size()-1);
//            List<UserMessage> userMessageList = userMessageService.getUserMessageListByMessageId(app.getId(),"14");

            pb.setRegisterApproveTime(new Date());//立案批准时间
            pb.setRegisterApprover(user.getUserID());//立案批准人
            pb.setRegisterRejectReason(registerRejectReason);//立案驳回原因

            app.setHandleTime(new Date());
            app.setUserName(user.getUserName());
            app.setToUserName(user.getUserName());
            app.setReason(registerRejectReason);
            if ("1".equals(approvalStatus)) {
                pb.setState("3");//状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过

                projectBaseService.updateProjectBase(pb);

                app.setApproveStatus("2");//1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                approvalProcessService.updateApprovalProcess(app);

            }else {
                pb.setState("2");//状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过

                projectBaseService.updateProjectBase(pb);

                app.setApproveStatus("3");//1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                approvalProcessService.updateApprovalProcess(app);
            }
//            for (UserMessage u:userMessageList){
//                if ("1".equals(approvalStatus)) {
//                    u.setApprovalStatus(2); //消息中的审批状态为批准
//                }else {
//                    u.setApprovalStatus(3); //消息中的审批状态为驳回
//                }
//                u.setState(2);
//                u.setHandleId(user.getUserID().toString());
//                u.setHandleName(user.getUserName());
//                u.setHandleReply(registerRejectReason);
//                u.setHandleTime(new Date());
//                userMessageService.updateUserMassage(u);
//            }

//            User shenqing=userService.getUserByID(pb.getInitialReceipter());
            //   批准/驳回后给申请者的消息
//            UserMessage userMessage = new UserMessage();
//            userMessage.setUser(shenqing);
//            String neirong="";
//            if ("1".equals(approvalStatus)){
//                userMessage.setApprovalStatus(2);
//                neirong="已立案";
//            }else {
//                userMessage.setApprovalStatus(3);
//                neirong="被驳回";
//            }
//            userMessage.setHandleId(user.getUserID().toString());
//            userMessage.setMessageId(app.getId());
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
//            userMessage.setEventType("项目管理");
//            userMessage.setIllustrate(sdf.format(new Date()) + "的项目管理立案申请"+neirong);
//            userMessage.setPersonnelReimburId(pb.getId());
//            userMessage.setMessageType("14");
//            userMessage.setState(1);
//            userMessage.setType(1);//1-立案 2-结案
//            userMessage.setReceiveUserId(shenqing.getUserID());  //接收消息人
//            userMessage.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
//            userMessage.setCreateDate(new Date());
//            userMessageService.addUserMassage(userMessage);
            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }


    /**
    * <AUTHOR>
    * @Date 2018/6/4 10:04
    * 结案申请 接口
    */
    @ResponseBody
    @RequestMapping("/projectSettle.do")
    public void projectSettle(User user ,Integer id,String settleOpinion,Date settleTimeFact,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (id!=null) {
            ProjectBase projectBase = projectBaseService.getProjectBaseById(id);
            projectBase.setOrg(user.getOid());
            projectBase.setUpdateName(user.getUserName());
            projectBase.setUpdateDate(new Date());
            projectBase.setUpdator(user.getUserID());
            User approvalUser = userService.getUserByCoreCode(user.getOid(), "projectCore");
            if (approvalUser.getUserID().equals(user.getUserID())) {
                projectBase.setState("6");//申请人是 核心人物，直接通过 无需审批
                projectBase.setSettleType("1");//结案类型:1-通过,2- 驳回
            } else {
                projectBase.setState("4");
            }
            projectBase.setProcessor(approvalUser.getUserID());//处理人
            projectBase.setSettler(user.getUserID());
            projectBase.setSettleTime(new Date());
            projectBase.setSettleOpinion(settleOpinion);//结案意见
            projectBase.setSettleTimeFact(settleTimeFact);//实际结案时间
            projectBase.setSettleApproveTime(new Date());//结案批准时间
            projectBase.setSettleApprover(user.getUserID());//结案批准人
            projectBaseService.updateProjectBase(projectBase);

            if (projectBase.getState().equals("4")) {
                ApprovalProcess approvalProcess = new ApprovalProcess();
                approvalProcess.setCreateDate(new Date());
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setApproveStatus("1");
                approvalProcess.setBusiness(projectBase.getId());//业务id
                approvalProcess.setBusinessType(12);
                approvalProcess.setToUser(approvalUser.getUserID());
                approvalProcess.setToUserName(approvalUser.getUserName());
                approvalProcess.setUserName(approvalUser.getUserName());
                approvalProcess.setAskName(user.getUserName());
                approvalProcess.setLevel(1);
                approvalProcess.setType(2);//1-项目立案 2-项目结案
                approvalProcess.setDescription("结案申请");
                approvalProcessService.saveApprovalProcess(approvalProcess);

//                UserMessage userMessage = new UserMessage();
//                userMessage.setUser(user);
//                userMessage.setApprovalStatus(1);
//                userMessage.setHandleId(approvalUser.getUserID().toString());
//                userMessage.setMessageId(approvalProcess.getId());
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
//                userMessage.setEventType("项目管理");
//                userMessage.setIllustrate(sdf.format(new Date()) + "有新的项目管理结案申请");
//                userMessage.setMessageType("14");
//                userMessage.setState(1);
//                userMessage.setReceiveUserId(approvalUser.getUserID());  //接收消息人
//                userMessage.setCreateDate(new Date());
//                userMessage.setPersonnelReimburId(projectBase.getId());
//                userMessage.setType(2);//1-立案 2-结案
//
//                userMessageService.addUserMassage(userMessage);

            }else {
                ApprovalProcess approvalProcess = new ApprovalProcess();
                approvalProcess.setCreateDate(new Date());
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setApproveStatus("2");
                approvalProcess.setBusiness(projectBase.getId());//业务id
                approvalProcess.setBusinessType(12);
                approvalProcess.setToUser(approvalUser.getUserID());
                approvalProcess.setToUserName(approvalUser.getUserName());
                approvalProcess.setUserName(approvalUser.getUserName());
                approvalProcess.setAskName(user.getUserName());
                approvalProcess.setLevel(1);
                approvalProcess.setType(2);//1-项目立案 2-项目结案
                approvalProcess.setDescription("结案申请");
                approvalProcessService.saveApprovalProcess(approvalProcess);
            }
            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/6/4 10:25
    * 批准驳回 结案申请
    */
    @ResponseBody
    @RequestMapping("/approvalProjectSettle.do")
    public void approvalProjectSettle(User user,Integer id,String approvalStatus,String settleRejectReason,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        if (id!=null&&!"".equals(approvalStatus)&&approvalStatus!=null) {
            ProjectBase pb = projectBaseService.getProjectBaseById(id);
            List<ApprovalProcess> aps = approvalProcessService.getApprovalProcessByBusiness(id, 12, "1");
            ApprovalProcess  app= aps.get(aps.size()-1);
//            List<UserMessage> userMessageList = userMessageService.getUserMessageListByMessageId(app.getId(),"14");

            pb.setSettleApproveTime(new Date());//结案批准时间
            pb.setSettleApprover(user.getUserID());//结案批准人
            pb.setSettleRejectReason(settleRejectReason);//结案驳回原因

            app.setHandleTime(new Date());
            app.setUserName(user.getUserName());
            app.setToUserName(user.getUserName());
            app.setReason(settleRejectReason);
            if ("1".equals(approvalStatus)) {
                pb.setState("6");//状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
                pb.setSettleType("1");//结案类型:1-通过,2- 驳回
                projectBaseService.updateProjectBase(pb);

                app.setApproveStatus("2");//1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                approvalProcessService.updateApprovalProcess(app);

            }else {
                pb.setState("5");//状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过
                pb.setSettleType("2");//结案类型:1-通过,2- 驳回

                projectBaseService.updateProjectBase(pb);

                app.setApproveStatus("3");//1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                approvalProcessService.updateApprovalProcess(app);
            }
//            for (UserMessage u:userMessageList){
//                if ("1".equals(approvalStatus)) {
//                    u.setApprovalStatus(2); //消息中的审批状态为批准
//                }else {
//                    u.setApprovalStatus(3); //消息中的审批状态为驳回
//                }
//                u.setState(2);
//                u.setHandleId(user.getUserID().toString());
//                u.setHandleName(user.getUserName());
//                u.setHandleReply(settleRejectReason);
//                u.setHandleTime(new Date());
//                userMessageService.updateUserMassage(u);
//            }

//            User shenqing=userService.getUserByID(pb.getInitialReceipter());
//            //   批准/驳回后给申请者的消息
//            UserMessage userMessage = new UserMessage();
//            userMessage.setUser(shenqing);
//            String neirong="";
//            if ("1".equals(approvalStatus)){
//                userMessage.setApprovalStatus(2);
//                neirong="已结案";
//            }else {
//                userMessage.setApprovalStatus(3);
//                neirong="被驳回";
//            }
//            userMessage.setHandleId(user.getUserID().toString());
//            userMessage.setMessageId(app.getId());
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
//            userMessage.setEventType("项目管理");
//            userMessage.setIllustrate(sdf.format(new Date()) + "的项目管理结案申请"+neirong);
//            userMessage.setPersonnelReimburId(pb.getId());
//            userMessage.setMessageType("14");
//            userMessage.setState(1);
//            userMessage.setReceiveUserId(shenqing.getUserID());  //接收消息人
//            userMessage.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
//            userMessage.setCreateDate(new Date());
//            userMessage.setType(2);//1-立案 2-结案
//
//            userMessageService.addUserMassage(userMessage);
            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }
    
    /**
    * <AUTHOR>
    * @Date 2018/6/4 10:41
    * 结案通过 查询
     * type 1- 本年 2-去年 3-前年 4-自定义
    */
    @ResponseBody
    @RequestMapping("/projectSettleList.do")
    public void projectSettleList(User user,Integer type,String beginDate,String endDate,PageInfo pageInfo,HttpServletResponse response) throws ParseException, IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        if (type!=null) {
            User approvalUser = userService.getUserByCoreCode(user.getOid(), "projectCore");
            Date begin = new Date();
            Date end = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (type == 1) {
                begin = DateUtils.aYearFirstDay(new Date());
                end = DateUtils.aYearEndDate(begin);
            } else if (type == 2) {
                begin = DateUtils.aYearFirstDay(new Date());
                begin = NewDateUtils.changeYear(begin, 1);//去年
                end = DateUtils.aYearEndDate(begin);
            } else if (type == 3) {
                begin = DateUtils.aYearFirstDay(new Date());
                begin = NewDateUtils.changeYear(begin, 2);//前年
                end = DateUtils.aYearEndDate(begin);
            } else if (type == 4) {
                begin = sdf.parse(beginDate+" 00:00:00");
                end = sdf.parse(endDate+" 23:59:59");
            }
            String b=sdf.format(begin);
            String e=sdf.format(end);

            List<ProjectBase> pList;
            if (approvalUser.getUserID().equals(user.getUserID())) {
                pList = projectBaseService.getProjectBaseByDate(user.getOid(), "6", null, begin, end, pageInfo);
            } else {
                pList = projectBaseService.getProjectBaseByDate(user.getOid(), "6", user.getUserID(), begin, end, pageInfo);
            }
            for (ProjectBase p:pList){
                if (p.getInitialReceipter()!=null)
                    p.setRegisterName(userService.getUserByID(p.getInitialReceipter()).getUserName());
                if (p.getRegisterApprover()!=null)
                    p.setRegisterApproveName(userService.getUserByID(p.getRegisterApprover()).getUserName());
                if (p.getSettler()!=null)
                    p.setSettleName(userService.getUserByID(p.getSettler()).getUserName());
                if (p.getSettleApprover()!=null)
                    p.setSettleApproveName(userService.getUserByID(p.getSettleApprover()).getUserName());
            }
            map.put("status", 1);//成功
            map.put("projectBaseList",pList);
            map.put("pageInfo",pageInfo);
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"projectAttachmentHashSet"},response);
    }
}
