package cn.sphd.miners.modules.processManagement.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.processManagement.service.ProcessSettingsService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * Created by lyx on 2023/5/9
 * 1.251工序1
 */
@Controller
@RequestMapping("/processView")
public class ProcessViewController {

    @Autowired
    ProcessSettingsService processSettingsService;

    /**
    *@Description 跳转工序查看
    *@auther 李娅星
    *@date 2023/5/10
    *@param
    */
    @RequestMapping("/toProcessView.do")
    public String toProcessView(){
        return "/processmanagement/processView";
    }

    /**
     *@Description 工序查看，获取产品列表
     *@auther 孙文
     *@date 2023/5/17
     */
    @ResponseBody
    @RequestMapping("/getPdBaseProcessList.do")
    public JsonResult getPdBaseProcessList(User user){
        List<Map<String, Object>> list=processSettingsService.getPdBaseProcessList(user);
        return new JsonResult(1, list);
    }

}
