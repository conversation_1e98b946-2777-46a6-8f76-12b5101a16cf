<template>
  <div id="applyReimburseQueryPer">
    <TY_NavTop :title="paramQuery.title"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.beginDate|formatDay('YYYY年MM月DD日')}} - {{queryData.endDate|formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent">
              <span v-if="paramQuery.approveStatus === '2' || paramQuery.approveStatus === 2">审批通过</span><span v-if="paramQuery.approveStatus === '3' || paramQuery.approveStatus === 3">被驳回</span>的修改考勤申请
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的数据共如下{{queryData.approvalInstanceList.length}}条。
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells">
          <a class="ui-cell" v-for="(item, index) in queryData.approvalInstanceList" v-on:click="jump(item.business, 3)" :key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div class="cusName">{{item.description}}</div>
                <p class="text-right"><span>{{item.askName}}</span> <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span></p>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'applyReimburseQueryPer',
  data () {
    return {
      loading: true,
      paramQuery: {},
      queryData: {
        beginDate: 1560408078000,
        endDate: 1560408078000,
        approvalInstanceList: []
      }
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    this.getAttendanceChange()
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/attendanceChangeDetail/${id}/3/3`
      })
    },
    getAttendanceChange: function () {
      let that = this
      let url = '../../../workAttendance/approvalInstanceByCriterionList.do'
      let title = that.paramQuery.title
      let sendData = this.paramQuery
      console.log(this.paramQuery)
      this.$http.post(url, sendData, {
        emulateJSON: true
      }).then((response) => {
        this.loading = false
        let res = response.body.data
        if (res) {
          that.queryData = res
          that.queryData.approvalInstanceList = that.queryData.approvalProcesses || []

        } else {
          that.$kiko_message('加载失败')
        }
      }).catch(function () {
        // this.jumpError('系统错误，请重试！')
      })
    }
  }
}
</script>
