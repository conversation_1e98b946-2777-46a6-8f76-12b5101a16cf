.labourSet, .proScan, .addPro, .endSuccess , .endProLiCon{ display: none;  }
.labourSetP{ padding-top: 15px; }
#addLabourSet{ width: 450px; }
.endSuccessCon{ padding:20px 0;  }
#changeState{ position: relative;  }
/* 结案自定义查询*/
.ty-btn-group{ border:1px solid #bbb; border-radius:4px;   }
ul.searchCon:before{  border-bottom: 7px solid rgba(0,0,0,0.2);  border-left: 7px solid transparent;  border-right: 7px solid transparent; content: "";
    display: inline-block !important; position: absolute;  right: 58px;  top: -7px;      }
ul .trigle{  border-bottom: 5px solid #fff;  border-left: 5px solid transparent;  border-right: 5px solid transparent;  content: "";
    display: inline-block !important; position: absolute;  right: 60px; top: -5px;       }
.searchCon{ left:43px; width:350px; border:1px solid #ccc; box-shadow:3px 3px 5px #666;top:40px;     }
.searchCon .ttl{ display:inline-block; width:100px; text-align:right;    }
.searchCon input{ display:inline-block; width:215px; height:35px; line-height:35px;padding:0 5px;      }
.searchCon li{margin-top:10px; }
.searchCon .ctl{ text-align:right; padding-right:30px; margin :30px 0 15px 0 ;     }
/*新增项目*/
.xing{ color:red; line-height: 15px; }
#addproCon{ padding-bottom: 200px;  }
.proTr{ margin:10px 0; }
#add_fu:after{ display: block; clear: both; }
.proTr .ttl {
    width: 95px;
    text-align: right;
    display: inline-block;
    margin: 0 15px;
}
.proTr .ttlBig{ width:100px; text-align: right; display: inline-block;    }
.proTr input, .proTr textarea, .proTr .panel{ border:1px solid #ddd; vertical-align:middle; padding:0 5px; }

#peoLi {
    width: 200px;
}
.proTr textarea, .proTr .panel {
    width: 1015px;
    display: inline-block;
}

.proTr input[type="text"] {
    width: 451px;
}

.proTr input {
    height: 35px;
    width: 401px;
}
.h300{ height: 175px!important; }
.h200{ height: 118px!important; }
.h100{ height: 62px!important; }

.proTr .w800 {
    width: 1015px !important;
}
.mar{ margin-left: 20px; }
.ty-center{ text-align: center;  }
/* 查看项目*/
.proScan{ padding-bottom: 200px; }

.proTr .con {
    border: 1px solid #ddd;
    line-height: 23px;
    padding: 5px;
    display: inline-block;
    width: 450px;
    height: 35px;
    vertical-align: middle;
    background: #fff;
    word-wrap: break-word;
}

.proTr .textarea {
    width: 1015px !important;
}
.proTr span.lit{ width: 350px!important; }

.chargeCon {
    margin: 20px 105px;
    background: #fff;
    width: 1015px;
}
.chargeCon article{ margin-left:50px;   }
.chargeCon>p{ background:#eee;padding:10px;    }
.chargeCon>p i{ color: #48cfad;display: inline-block; margin-left: 15px; font-size:16px; font-weight: bold;   }
.ty-process-item>article>p>span:nth-child(2){ display: inline-block; width: 80px; padding-left:20px;  }
/* 分工设置 */
.ty-colFileTree>ul ul{display: none ; padding-left:15px }
.ty-colFileTree .ty-treeItemActive>i.fa-file{  color: #fff;}
/* 结案 */
#endProDate , #endProComment, #registerRejectReason , #endProApplyReason{ width:400px;  }
#endProComment, #registerRejectReason, #endProApplyReason{ height:120px }
/* 上传附件 */
.ty-fileType {
    width: 60px;
    height: 100px;
    margin: 5px;
    position: relative;
    margin-right: 10px;
    background-size: 60px 80px !important;
    overflow: hidden;
}
.ty-fileType:hover{  box-shadow:0 0 5px #aaa ;     }
.ty-fileType .fName {
    position: absolute;
    top: 83px;
    left: 0;
    font-size: 10px;
    display: inline-block;
    width: 60px;
    height: 100px;
    line-height: 13px;
    color: #777;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.ty-fileType .fa {
    position: absolute;
    right: 0;
    top: -1px;
    display: none;
    box-shadow: 0 0 2px #000;
    color: #48cfad;
}
.ty-fileType .fa:hover {
    color: #ed5565;
}
.inline {
    display: inline-block;
}

.ty-fileType a.control {
    display: none;
    cursor: pointer;
    position: absolute;
    background: #6cc1f7;
    color: #fff;
    border-radius: 4px;
    padding: 0 5px;
    left: 10px;
}

.ty-fileType a.control:hover {
    background: #0b94ea
}

.ty-fileType a.control:nth-child(2) {
    top: 21px;
}

.ty-fileType a.control:nth-child(3) {
    top: 45px;
}

/*.laydate_btn{ display: none;  }*/
.ty-process-item > article > p > span:first-child {
    display: inline-block;
    width: 80px;
}

.ty-process-item > article > p > i:nth-child(2) {
    display: inline-block;
    text-align: left;
    font-style: normal;
}
.file_item {
    width: 80px;
    height: 80px;
    position: relative;
}
.file_item .fileType {
    width: 36px;
    height: 40px;
    margin: auto;
    background-size: 36px;
    background-repeat: no-repeat;
}
.fileShowList {
    display: flex;
    margin-top: 4px;
}
.file_item .file_name {
    font-size: 12px;
    display: inline-block;
    width: 100%;
    overflow: hidden;
    height: 50px;
    margin-top: 4px;
    text-align: center;
}
.uploadify-button, .uploadDeleteBtn {
    display: inline-block;
    border: none;
    background-color: #e7e7e7;
    height: 24px;
    border-radius: 3px;
    padding: 0 12px;
    font-size: 12px;
    font-weight: 400;
    color: #535353;
    cursor: pointer;
    text-decoration: none;
    flex-shrink: 0;
    line-height: 24px;
    margin: 4px;
}
.delRole, .delFile {
    transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    display: inline-block;
    font-size: 18px;
    width: 18px;
    height: 18px;
    text-align: center;
    vertical-align: middle;
    line-height: 18px;
    cursor: pointer;
}
.delFile {
    position: absolute;
    right: 5px;
    top: -5px;
}


