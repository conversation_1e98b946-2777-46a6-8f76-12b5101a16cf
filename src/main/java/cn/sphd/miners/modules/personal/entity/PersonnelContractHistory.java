package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_personnel_contract_history")
public class PersonnelContractHistory extends BaseEntity{
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//id                            bigint not null auto_increment  comment 'ID',
    @Column(name = "org")
    private Integer org;                           //int  comment '机构ID',
    @Column(name = "contract")
    private Long contract;                         //bigint  comment '合同ID',
    @Column(name = "user")
    private Integer user;                          //int  comment '用户ID',
    @Column(name = "username",length = 100)
    private String username;                       //varchar(100)  comment '用户名',
    @Column(name = "prim_contract")
    private Long primContract;                     //bigint  comment '主合同ID',
    @Column(name = "is_current")
    private Boolean isCurrent;                     //boolean default FALSE  comment '当前使用标识',
    @Column(name = "sn",length = 100)
    private String sn;                             //varchar(100)  comment '合同编号',
    @Column(name = "sign_time")
    private Date signTime;                         //datetime  comment '签署时间',
    @Column(name = "valid_start")
    private Date validStart;                       //datetime  comment '有效起始时间',
    @Column(name = "valid_end")
    private Date validEnd;                         //datetime  comment '有效截止时间',
    @Column(name = "is_expired")
    private Boolean isExpired;                     //boolean default FALSE  comment '是否到期',
    @Column(name = "suspend_time")
    private Date suspendTime;                      //datetime  comment '暂停履约时间',
    @Column(name = "status")
    private Byte status;                           //tinyint default 1  comment '状况:0-终止,1-正常,2-续约',
    @Column(name = "file_name")
    private String fileName;                       //varchar(50)  comment '合同文件名称',
    @Column(name = "file_path")
    private String filePath;                       //varchar(255)  comment '合同文件路径',
    @Column(name = "level")
    private Integer level;                         //int  comment '层级(续约次数)',
    @Column(name = "trace_path")
    private String tracePath;                      //text  comment '续约ID追踪,历史ID以逗号分隔',
    @Column(name = "remark")
    private String remark;                         //varchar(255)  comment '备注',
    @Column(name = "keywords",length = 100)
    private String keywords;                       //varchar(100)  comment '关键字',
    @Column(name = "enabled")
    private Boolean enabled;                       //tinyint default 1  comment '状态:0-不启用,1-启用',
    @Column(name = "enabled_time")
    private Date enabledTime;                      //datetime  comment '启停用时间',
    @Column(name = "creator")
    private Integer creator;                       //int  comment '创建人id',
    @Column(name = "create_name",length = 100)
    private String createName;                   //varchar(100)  comment '创建人',
    @Column(name = "create_date")
    @CreationTimestamp
    private Date createDate;                   //datetime(3) default null  comment '创建时间',
    @Column(name = "updator")
    private Integer updator;                       //int  comment '修改人id',
    @Column(name = "update_name",length = 100)
    private String updateName;                     //varchar(100)  comment '修改人',
    @Column(name = "update_date")
    @UpdateTimestamp
    private Date updateDate;                   //datetime(3) default null  comment '修改时间',
    @Column(name = "operation")
    private Byte operation;                     //tinyint  comment '操作:1-增,2-删,3-改,4-续约,5-终止,6-更改文档,7-更改媒体'
    @Column(name = "previous_id")
    private Integer previousId;                   //int  comment '修改前记录ID',
    @Column(name = "version_no")
    private  Integer versionNo;                    //int  comment '版本号,每次修改+1',

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getContract() {
        return contract;
    }

    public void setContract(Long contract) {
        this.contract = contract;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getPrimContract() {
        return primContract;
    }

    public void setPrimContract(Long primContract) {
        this.primContract = primContract;
    }

    public Boolean getIsCurrent(){
        return isCurrent;
    }

    public void setIsCurrent(Boolean isCurrent){
        this.isCurrent = isCurrent;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getValidStart() {
        return validStart;
    }

    public void setValidStart(Date validStart) {
        this.validStart = validStart;
    }

    public Date getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(Date validEnd) {
        this.validEnd = validEnd;
    }

    public Boolean getIsExpired() {

        return isExpired;
    }

    public void setIsExpired(Boolean isExpired) {
        this.isExpired = isExpired;
    }

    public Date getSuspendTime() {
        return suspendTime;
    }

    public void setSuspendTime(Date suspendTime) {
        this.suspendTime = suspendTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getTracePath() {
        return tracePath;
    }

    public void setTracePath(String tracePath) {
        this.tracePath = tracePath;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
