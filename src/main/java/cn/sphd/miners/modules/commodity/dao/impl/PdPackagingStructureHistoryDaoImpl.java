package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdPackagingStructureHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdPackagingStructure;
import cn.sphd.miners.modules.commodity.entity.PdPackagingStructureHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class PdPackagingStructureHistoryDaoImpl extends BaseDao<PdPackagingStructureHistory, Serializable> implements PdPackagingStructureHistoryDao {
    @Override
    public PdPackagingStructureHistory insert(PdPackagingStructure pdPackagingStructure,Integer infoHistoryId) {
        PdPackagingStructureHistory history=new PdPackagingStructureHistory();

        BeanUtils.copyProperties(pdPackagingStructure,history);
        history.setPackaging(infoHistoryId);
        history.setPackagingStruct(pdPackagingStructure.getId());
        history.setId(null);
        save(history);
        return history;
    }
}
