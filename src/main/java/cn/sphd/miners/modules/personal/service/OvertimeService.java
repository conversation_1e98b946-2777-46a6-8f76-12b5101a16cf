package cn.sphd.miners.modules.personal.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.generalAffairs.service.AttendanceEventSerivce;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/5/8.
 * 请假加班方法
 */
public interface OvertimeService extends BadgeNumberCallback, LeaveOutTimeUtilsService, AttendanceEventSerivce {

    //Kind 1-普通(默认),2-补报
    enum Kind {
        ordinary("ordinary", (byte)1),
        supplementary("supplementary", (byte)2);
        private String name;
        private Byte index;
        Kind(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //回复意见:1-同意,2-不同意
    enum replyOpinion {
        agree("agree", (byte)1),
        disagree("disagree", (byte)2);
        private String name;
        private Byte index;
        replyOpinion(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    void  overturn(Integer id,Integer type);

    List<PersonnelOvertime>  getPersonnelOvertimeListByUser(Integer oid,Integer userId,Integer loginUserId, Integer approvalStatus,  String actualType,Integer type);

    Double getDuration(String begin, String end);

    Double getOverTimeHours(Integer userId, String approvalStatus, Date beginTime, Date endTime, String actualState);  //state(1-加班总时长 2-平时加班时长 3-周六日加班总时长)【1-工作日加班2-周末假日加班3-法定节假日加班】

    List<PersonnelOvertime>  getPersonnelOvertimeListByTime(Integer userId, Integer approvalStatus, Date beginTime, Date endTime);

    //sortType 排序(1-倒序 2-顺序)  userId(申请人id)  approverId（审批人id）
    List<PersonnelOvertime>  getPersonnelOvertimeListByBeginTime(Integer userId, Integer approvalStatus, Date beginTime, Date endTime, String actualType, Integer sortType,Integer approverId);

    List<PersonnelOvertime> getPersonnelOvertimeByApproverId(Integer toUserId, String approvalStatus, Integer businessType, String beginTime, String endTime, PageInfo page);   //根据加班开始时间和审批人id，查询审批人审批的所有完成的加班

    //以下为长链接时，修改的加班、请假service   type 1-职工中的(所有人的)   2-我的团队（本人及其下属）
    List<PersonnelOvertime> getPersonnelOvertimeByHandle(Integer toUserId,Integer oid, String approvalStatus, Integer businessType,Integer type);  //待处理

    List<PersonnelOvertime> getPersonnelOvertimeByApproval(Integer toUserId,Integer oid,Integer type);  //根据审批人id、状态等查询加班 type 1-职工中的(所有人的) 2-我的团队（本人及其下属）

    public void outTimeRejectSend(int loginNum,int operate,PersonnelOvertime personnelOvertime,Integer toUserId,String pass,String title, String content,String code);

    Integer getOvertimeCounts(Integer userId,String approveStatus,String actualType);

    HashMap<String,Object> myOutTimeInfoById(Integer userId,Integer id,HashMap<String,Object> map);

    PersonnelOvertime addPersonnelOvertime(User userId,PersonnelOvertime personnelOvertime,Double actualDuration,String actualReason,String beginTime1, String endTime1,String tjtype) throws ParseException;

    PersonnelOvertime getPersonnelOvertimeById(Integer id);

    void approvalOutTimeApproval(ApprovalProcess approvalProcess, User user, PersonnelOvertime outTime, Integer type, Integer id, Double approveDuration, String approveExplain);   //审批加班批准

    void approvalOutTimeDisapproval(ApprovalProcess approvalProcess,User user,PersonnelOvertime outTime,String reason);   //审批加班驳回

    void approvalOutTime(User user,Integer id,Integer type,Double approveDuration,String approveExplain,String reason, Integer approvalProcessId,String approvalStatus) throws ParseException;   //审批加班

    Integer deletePersonnelOvertime(Integer outTimeId,Integer type);  //实际未加班接口

    Map<String,Object> getApplicantOutTimes(Integer userId,Integer approverId,Integer state,String beginDate,String endDate,Map<String, Object> map) throws ParseException;  //申请人加班查询

    Map<String,Object> getApproverOutTimes(Integer userId,Integer state,String beginDate,String endDate,Map<String,Object> map) throws ParseException;  //审批人加班查询

    Map<String,Object> getOverTimeDetails(Integer outTimeId,Map<String,Object> map);

    Map<String,Object> addPlanOverTime(User user,PersonnelOvertime personnelOvertime, String beginTime1, String endTime1,Double ruleTime,Map<String,Object> map) throws ParseException;  //提交计划加班申请   (tjtype 1-计划加班申请 2-申报加班申请)

   Map<String,Object> addDeclareOverTime(String sessionId,User user,Integer overtimeId, String beginTime1, String endTime1,Double actualDuration,String actualReason,Map<String, Object> map) throws ParseException;  //提交申报加班申请

    List<PersonnelOvertime> getPersonnelOvertimeListQuery(Integer userId,Integer type,Date beginDate,Date endDate);

    HashMap<String,Object> getOvertimeStatistics(List<PersonnelOvertime> personnelOvertimeList);

    HashMap<String,Object> getOverTimeSum(Integer userId,Integer type,Date beginDate,Date endDate,Integer applyId,boolean isApproval);

    List<PersonnelOvertime> getApprovalOverTimeListQuery(Integer userId, Integer type, Date beginDate, Date endDate,Integer applyUserId);

    Integer getPersonnelOvertimeCountsByOid(Integer oid);

    List<PersonnelOvertime> getPersonnelOvertimeHandleByTime(Integer userId,Date beginDate,Date endDate);  //获取某人某天的计划加班或者申报未完成的加班

    Map<String,Object> supplementaryOvertime(User user,String beginTime, String endTime,Double durationSub,String reason);  //补报加班

    Map<String,Object> assignOvertime(User user,String beginTime, String endTime,Double durationSub,String reason,String overtimeUserIds);  //指派加班

    List<PersonnelOvertime> assignOvertimeList(Integer userId,String approveStatus,Date beginDate,Date endDate);  //获取指派加班列表

    Map<String,Object> approvalAssignOutTime(User user,Integer assignOvertimeId,String approveStatus,String reason);  //指派加班审批

    JsonResult progressiveApprovalOutTime(Integer approvalProcessId, Integer id, Integer type, Double approveDuration, String approveExplain, String reason, String approvalStatus, String sessionid, User user);
}
