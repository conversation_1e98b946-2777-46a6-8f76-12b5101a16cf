
@charset "utf-8";

@import "variable";
@import "normalize";


html{
}
body{
}
 li{   list-style: none;  }
//input,select,textarea,select{ outline: 0 }
//textarea {  resize: none; }



.ty-right{
  float: right;
}
.ty-center{
  text-align: center;
}
.ty-clear{
  clear: both;
}
.ty-linkBtn-red{
  color: $tyColorRed; font-size:15px; display: inline-block; padding:0 10px;
  cursor:pointer; text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
}

.ty-linkBtn-green{
  color: $tyBounceColorGreen; font-size:15px; display: inline-block; padding:0 10px;
  cursor:pointer; text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
}

.ty-linkBtn, .ty-linkBtn-blue{
  color: $tyBounceColorBlue; font-size:15px; display: inline-block; padding:0 10px; cursor: pointer; text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
  &:last-child{
    padding-right: 0;
  }
}


.ty-btn{ text-decoration: none;  padding:0 10px;cursor: pointer; background-color:#fff;color:#101010; height:24px; line-height:24px; display:inline-block; border:none;       }
.ty-btn-big{ padding:0 24px;height:32px; line-height:32px;        }

.ty-btn-green{ background-color:$tyBounceColorGreen;  color: #fff; }
.ty-btn-blue{ background-color:$tyBounceColorBlue;  color: #fff; }
.ty-btn-blue:hover{ background-color:$tyBounceColorBlueHover;  color: #fff; }

.ty-btn-gray{ background-color:$tyBounceColorGray;  color: #fff; }
.ty-btn-blue:hover{ background-color:$tyColorGrayHover;  color: #fff; }

.ty-table-control .ty-color-blue, .ty-td-control .ty-color-blue{
  color: $tyBounceColorBlue;
}

.ty-color-blue{  color: $tyBounceColorBlue; }
.ty-color-green{  color: $tyBounceColorGreen; }
.ty-color-red{  color: $tyColorRed; }
.ty-color-orange{  color: $tyColorOrange; }
.ty-tight{ float: right; }
.ty-right{ float: right; }
.text-center{ text-align: center; }

.ty-panel{

}
.ty-p { }
.ty-p { }
.container{
  font-size: 16px;
  overflow-y: auto;
}
.container_main{
  font-size: 14px;
  height: calc(100vh - 76px);
  overflow-y: auto;
  padding: 16px;
}
.el-button{
  font-size: 14px;
  padding-left: 32px; padding-right: 32px
}
