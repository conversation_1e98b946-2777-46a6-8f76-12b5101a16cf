package cn.sphd.miners.modules.personal.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.personal.service.PersonnelReimbursetAttachmentService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.*;

/**
 * Created by Administrator on 2016/7/12.
 */
@Controller
@RequestMapping("/user")
public class UserController {
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    AccountService accountService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    PersonnelReimbursetAttachmentService personnelReimbursetAttachmentService;
    @Autowired
    OrgService orgService;


    @RequestMapping("/systemSet.do")
    public String systemSet(User user, Model model){
//        Integer oid= (Integer) session.getAttribute("oid");
        Integer loginStatus=null;
        Integer loginStatus1=null;
        User finance=userService.getUserByRoleCode(user.getOid(),"finance");
        if (finance!=null){
            loginStatus=finance.getLoginStatus();
        }else {
            loginStatus=0;
        }
        User sale=userService.getUserByRoleCode(user.getOid(),"sale");
        if (sale!=null){
            loginStatus1=sale.getLoginStatus();
        }else {
            loginStatus1=0;
        }
        model.addAttribute("loginStatus",loginStatus);
        model.addAttribute("loginStatus1",loginStatus1);
        return "/user/systemSet";
    }

    @ResponseBody
    @RequestMapping("/getSystemSetStatus.do")
    public JsonResult getSystemSetStatus(User user){
//        Integer oid= (Integer) session.getAttribute("oid");
        Integer financeLoginStatus=null;
        Integer saleLoginStatus=null;
        User finance=userService.getUserByRoleCode(user.getOid(),"finance");
        if (finance!=null){
            financeLoginStatus=finance.getLoginStatus();
        }else {
            financeLoginStatus=0;
        }
        User sale=userService.getUserByRoleCode(user.getOid(),"sale");
        if (sale!=null){
            saleLoginStatus=sale.getLoginStatus();
        }else {
            saleLoginStatus=0;
        }
        Map<String,Object> map=new HashMap<>();
        map.put("financeLoginStatus",financeLoginStatus);
        map.put("saleLoginStatus",saleLoginStatus);
        return new JsonResult(1,map);
    }

    @ResponseBody
    @RequestMapping("/updateLoginStatus.do")
    public JsonResult updateLoginStatus(User user, Integer loginStatus){
//        Integer oid= (Integer) session.getAttribute("oid");
        List<User> users=userService.getFinanceAndManageUserByOrgId(user.getOid());
        for (User u:users){
            if (u.getRoleCode().equals("finance")){
                u.setLoginStatus(loginStatus);
                userService.updateUser(u);
            }
        }
//        return "redirect:/user/systemSet.do";
        return new JsonResult(1,1);

    }

    @RequestMapping("/updateLoginStatus1.do")
    public String updateLoginStatus1(User user, Integer loginStatus){
//        Integer oid= (Integer) session.getAttribute("oid");
        List<User> users=userService.getFinanceAndManageUserByOrgId(user.getOid());
        for (User u:users){
            if (u.getRoleCode().equals("sale")){
                u.setLoginStatus(loginStatus);
                userService.updateUser(u);
            }
        }
        return "redirect:/user/systemSet.do";
    }



    @RequestMapping("/yanZheng.do")
    public String yanZheng(){
        return "user/yanzheng";
    }


    //通讯录
    @RequestMapping("/toAddressList.do")
    public String addressList(User user,Integer sonOid, Model model, String userName) throws IOException {
        Integer oid=user.getOid();
        if (sonOid!=null){
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        List<User> users=userService.getUserByCurrentOidAndNameLocking(oid,userName);
        model.addAttribute("users",users);
        return "/home/<USER>";
    }

}
