<template>
  <div id="purchasePaymentFinanceApproval" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="采购部门的付款" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待付款审批" name="1" closable='false'>
        <div class="tip">
          以下付款申请系采购部门提交，尚未通过领导审批！
        </div>
        <div>   <!--上面一条是票据数据，下面一条是预付款数据-->
          <a class="ui-cell" v-on:click="jump('22' + item.poPaymentApplication.id,0,0)" v-for="(item, index) in cashierPaymentHandle" v-bind:key="index" v-if="item.poPaymentApplication !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount && item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
          <a class="ui-cell" v-on:click="jump('61' + item.poOrderPrepayment.id, 0,1)" v-for="(item, index) in cashierPaymentHandle" v-bind:key="index" v-if="item.poOrderPrepayment !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planAmount && item.poOrderPrepayment.planAmount.toFixed(2)}}&nbsp;元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planDate |formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="可付款" name="2" closable='false' :msgCount="financePayable.length">
        <div class="tip">
          <p> 下列付款申请已获审批通过，有待付款。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('23' + item.poPaymentApplication.id, 1,0, item)" v-for="(item, index) in financePayable" v-bind:key="index" v-if="item.poPaymentApplication !== null">
            <div class="item_fn">
              <div class="supName">供应商：{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
          <a class="ui-cell" v-on:click="jump('62' + item.poOrderPrepayment.id, 1,1,item)" v-for="(item, index) in financePayable" v-bind:key="index" v-if="item.poOrderPrepayment !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planAmount && item.poOrderPrepayment.planAmount.toFixed(2)}}&nbsp;元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planDate |formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待复核" name="3" closable='false'>
        <div class="tip">
          <p>下列待付款项有待复核。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('24' + item.poPaymentApplication.id, 2,0, item)" v-for="(item, index) in financeReview" v-bind:key="index" v-if="item.poPaymentApplication !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
          <a class="ui-cell" v-on:click="jump('63' + item.poOrderPrepayment.id, 2,1, item)" v-for="(item, index) in financeReview" v-bind:key="index" v-if="item.poOrderPrepayment !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planAmount && item.poOrderPrepayment.planAmount.toFixed(2)}}&nbsp;元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planDate |formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待付款" name="4" closable='false' :msgCount="cashierTwoSettled.length">
        <div class="tip">
          <p>下列待付款项复核通过，请予付款。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('25' + item.poPaymentApplication.id, 2,0, item)" v-for="(item, index) in cashierTwoSettled" v-bind:key="index" v-if="item.poPaymentApplication !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
          <a class="ui-cell" v-on:click="jump('64' + item.poOrderPrepayment.id, 2,1, item)" v-for="(item, index) in cashierTwoSettled" v-bind:key="index" v-if="item.poOrderPrepayment !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planAmount && item.poOrderPrepayment.planAmount.toFixed(2)}}&nbsp;元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment && item.poOrderPrepayment.planDate |formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#purchasePaymentFinanceApproval{
  .right{ float: right;  }
  .ui-cell{ display: block;  }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
  .tabCount{
    top:0;
    right:0 ;
    left:auto;
  }
  .tabs-tab{
    width:25%;
    font-size:12px ;
  }
  .tip{
    padding:5px 12px;
    background-color: #E2EFDA;
    display: block;
  }
}
</style>
<script>
export default {
  name: 'purchasePaymentFinanceApproval',
  data () {
    return {
      cashierHandle: [],
      cashierTwoSettled: [], // 待付款
      cashierPaymentHandle: [], // 待付款审批
      financePayable: [], // 可付款
      financeReview: [], // 待复核
      listenersUid: [],
      tabValue: '1',
      loading: true,
      nowTime: 0
    }
  },
  filters: {
    formatType: function (num, amount) {
      let str = ''
      switch (Number(num)) {
        case 1:
          str = '本次仅提交票据，不付款'
          break
        case 2:
          str = `申请付款${amount.toFixed(2)}元`
          break
        case 3:
          str = `申请付款${amount.toFixed(2)}元`
          break
      }
      return str
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    that.getList()
    that.tabValue = localStorage.navNum || '1'
    console.log(that.tabValue)
    this.listenersUid = [
      // 待付款审批
      this.sphdSocket.subscribe('payApprovalFinanceHandle', function (data) {
        let getData = JSON.parse(data)
        console.log('待付款审批 订阅通道', getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierPaymentHandle.unshift(getData)
          console.log('cashierPaymentHandle', that.cashierPaymentHandle)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.cashierPaymentHandle.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierPaymentHandle.splice(_index, 1)
          }
          console.log('cashierPaymentHandle', that.cashierPaymentHandle)
        }
      }, null, 'user'),
      // 可付款
      this.sphdSocket.subscribe('payableFinanceHandle', function (data) {
        let getData = JSON.parse(data)
        console.log('可付款 订阅通道', getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financePayable.unshift(getData)
          console.log('financePayable', that.financePayable)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.financePayable.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.financePayable.splice(_index, 1)
          }
          console.log('financePayable', that.financePayable)
        }
      }, null, 'user'),
      // 待复核
      this.sphdSocket.subscribe('reviewedFinanceHandle', function (data) {
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financeReview.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.financeReview.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.financeReview.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待付款
      this.sphdSocket.subscribe('payFinanceHandle', function (data) {
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierTwoSettled.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.cashierTwoSettled.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierTwoSettled.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../purchaseInvoice/getFinancePaymentApply.do')
        .then(function (response) {
          let res = response.data.data
          console.log('列表：', res)
          that.loading = false

          that.cashierPaymentHandle = res.payApprovalFinanceHandle || [] // 待付款审批
          that.financePayable = res.payableFinanceHandle || [] // 可付款
          that.financeReview = res.reviewedFinanceHandle || [] // 待复核
          that.cashierTwoSettled = res.payFinanceHandle || [] // 待付款
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id, num, pun, item) { // pun：是票据还是预付款
      console.log('item:', item)
      let businessGroup = Number(item && item.businessGroup || 0)
      if (num === 1) { // 可付款
        this.$router.push({
          path: `/purchasePaymentFinanceFu/${id}/0/0/${pun}/2`
        })
      } else if (num === 2) {
        this.$router.push({
          path: `/purchasePaymentFinanceFu2/${id}/${pun}/1/${businessGroup}`
        })
      } else { // 待付款审批
        this.$router.push({
          path: `/purchaseInvoiceDetails3/${id}/${pun}/${businessGroup}`
        })
      }
    },
    search: function () {
      this.$router.push({
        path: `/approveReimburseQuery/2`
      })
    }
  }
}
</script>
