package cn.sphd.miners.modules.commodity.entity;

import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "t_pd_packaging_info_history")
public class PdPackagingInfoHistory {
    private Integer id;
    private Integer org;
    private Integer packaging;
    private Integer merchandise;
    private String outerSn;
    private Integer orders;
    private Integer packagingCount;
    private Integer productCount;
    private BigDecimal productAmount;
    private Integer materialCount;
    private Integer layersCount;
    private String manner;
    private BigDecimal netWeight;
    private Integer netUnitId;
    private String netUnit;
    private BigDecimal grossWeight;
    private Integer grossUnitId;
    private String grossUnit;
    private BigDecimal totalWeight;
    private Integer totalUnitId;
    private String totalUnit;
    private BigDecimal outerLength;
    private Integer lengthUnitId;
    private String lengthUnit;
    private BigDecimal outerWidth;
    private Integer widthUnitId;
    private String widthUnit;
    private BigDecimal outerHeight;
    private Integer heightUnitId;
    private String heightUnit;
    private Integer outerShape;
    private Integer miniPrincipal;
    private String miniPrincipalName;
    private Integer miniAuxNum;
    private Integer penultimatePrincipal;
    private String penultimatePrincipalName;
    private Integer penultimateAuxNum;
    private Integer antepenultPrincipal;
    private String antepenultPrincipalName;
    private Integer antepenultAuxNum;
    private Integer outerPrincipal;
    private String outerPrincipalName;
    private Integer outerAuxNum;
    private Integer enabled;
    private Date enabledTime;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date effectTime;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private Integer operation;
    private Integer previousId;
    private Integer versionNo;

    private List<PdPackagingStructureHistory> structureList;

    private String unit;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "packaging")
    public Integer getPackaging() {
        return packaging;
    }

    public void setPackaging(Integer packaging) {
        this.packaging = packaging;
    }



    @Basic
    @Column(name = "merchandise")
    public Integer getMerchandise() {
        return merchandise;
    }

    public void setMerchandise(Integer merchandise) {
        this.merchandise = merchandise;
    }

    @Basic
    @Column(name = "outer_sn")
    public String getOuterSn() {
        return outerSn;
    }

    public void setOuterSn(String outerSn) {
        this.outerSn = outerSn;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "packaging_count")
    public Integer getPackagingCount() {
        return packagingCount;
    }

    public void setPackagingCount(Integer packagingCount) {
        this.packagingCount = packagingCount;
    }

    @Basic
    @Column(name = "product_count")
    public Integer getProductCount() {
        return productCount;
    }

    public void setProductCount(Integer productCount) {
        this.productCount = productCount;
    }

    @Basic
    @Column(name = "product_amount")
    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    @Basic
    @Column(name = "material_count")
    public Integer getMaterialCount() {
        return materialCount;
    }

    public void setMaterialCount(Integer materialCount) {
        this.materialCount = materialCount;
    }

    @Basic
    @Column(name = "layers_count")
    public Integer getLayersCount() {
        return layersCount;
    }

    public void setLayersCount(Integer layersCount) {
        this.layersCount = layersCount;
    }

    @Basic
    @Column(name = "manner")
    public String getManner() {
        return manner;
    }

    public void setManner(String manner) {
        this.manner = manner;
    }

    @Basic
    @Column(name = "net_weight")
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    @Basic
    @Column(name = "net_unit_id")
    public Integer getNetUnitId() {
        return netUnitId;
    }

    public void setNetUnitId(Integer netUnitId) {
        this.netUnitId = netUnitId;
    }

    @Basic
    @Column(name = "net_unit")
    public String getNetUnit() {
        return netUnit;
    }

    public void setNetUnit(String netUnit) {
        this.netUnit = netUnit;
    }

    @Basic
    @Column(name = "gross_weight")
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    @Basic
    @Column(name = "gross_unit_id")
    public Integer getGrossUnitId() {
        return grossUnitId;
    }

    public void setGrossUnitId(Integer grossUnitId) {
        this.grossUnitId = grossUnitId;
    }

    @Basic
    @Column(name = "gross_unit")
    public String getGrossUnit() {
        return grossUnit;
    }

    public void setGrossUnit(String grossUnit) {
        this.grossUnit = grossUnit;
    }

    @Basic
    @Column(name = "total_weight")
    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    @Basic
    @Column(name = "total_unit_id")
    public Integer getTotalUnitId() {
        return totalUnitId;
    }

    public void setTotalUnitId(Integer totalUnitId) {
        this.totalUnitId = totalUnitId;
    }

    @Basic
    @Column(name = "total_unit")
    public String getTotalUnit() {
        return totalUnit;
    }

    public void setTotalUnit(String totalUnit) {
        this.totalUnit = totalUnit;
    }

    @Basic
    @Column(name = "outer_length")
    public BigDecimal getOuterLength() {
        return outerLength;
    }

    public void setOuterLength(BigDecimal outerLength) {
        this.outerLength = outerLength;
    }

    @Basic
    @Column(name = "length_unit_id")
    public Integer getLengthUnitId() {
        return lengthUnitId;
    }

    public void setLengthUnitId(Integer lengthUnitId) {
        this.lengthUnitId = lengthUnitId;
    }

    @Basic
    @Column(name = "length_unit")
    public String getLengthUnit() {
        return lengthUnit;
    }

    public void setLengthUnit(String lengthUnit) {
        this.lengthUnit = lengthUnit;
    }

    @Basic
    @Column(name = "outer_width")
    public BigDecimal getOuterWidth() {
        return outerWidth;
    }

    public void setOuterWidth(BigDecimal outerWidth) {
        this.outerWidth = outerWidth;
    }

    @Basic
    @Column(name = "width_unit_id")
    public Integer getWidthUnitId() {
        return widthUnitId;
    }

    public void setWidthUnitId(Integer widthUnitId) {
        this.widthUnitId = widthUnitId;
    }

    @Basic
    @Column(name = "width_unit")
    public String getWidthUnit() {
        return widthUnit;
    }

    public void setWidthUnit(String widthUnit) {
        this.widthUnit = widthUnit;
    }

    @Basic
    @Column(name = "outer_height")
    public BigDecimal getOuterHeight() {
        return outerHeight;
    }

    public void setOuterHeight(BigDecimal outerHeight) {
        this.outerHeight = outerHeight;
    }

    @Basic
    @Column(name = "height_unit_id")
    public Integer getHeightUnitId() {
        return heightUnitId;
    }

    public void setHeightUnitId(Integer heightUnitId) {
        this.heightUnitId = heightUnitId;
    }

    @Basic
    @Column(name = "height_unit")
    public String getHeightUnit() {
        return heightUnit;
    }

    public void setHeightUnit(String heightUnit) {
        this.heightUnit = heightUnit;
    }

    @Basic
    @Column(name = "outer_shape")
    public Integer getOuterShape() {
        return outerShape;
    }

    public void setOuterShape(Integer outerShape) {
        this.outerShape = outerShape;
    }

    @Basic
    @Column(name = "mini_principal")
    public Integer getMiniPrincipal() {
        return miniPrincipal;
    }

    public void setMiniPrincipal(Integer miniPrincipal) {
        this.miniPrincipal = miniPrincipal;
    }

    @Basic
    @Column(name = "mini_principal_name")
    public String getMiniPrincipalName() {
        return miniPrincipalName;
    }

    public void setMiniPrincipalName(String miniPrincipalName) {
        this.miniPrincipalName = miniPrincipalName;
    }

    @Basic
    @Column(name = "mini_aux_num")
    public Integer getMiniAuxNum() {
        return miniAuxNum;
    }

    public void setMiniAuxNum(Integer miniAuxNum) {
        this.miniAuxNum = miniAuxNum;
    }

    @Basic
    @Column(name = "penultimate_principal")
    public Integer getPenultimatePrincipal() {
        return penultimatePrincipal;
    }

    public void setPenultimatePrincipal(Integer penultimatePrincipal) {
        this.penultimatePrincipal = penultimatePrincipal;
    }

    @Basic
    @Column(name = "penultimate_principal_name")
    public String getPenultimatePrincipalName() {
        return penultimatePrincipalName;
    }

    public void setPenultimatePrincipalName(String penultimatePrincipalName) {
        this.penultimatePrincipalName = penultimatePrincipalName;
    }

    @Basic
    @Column(name = "penultimate_aux_num")
    public Integer getPenultimateAuxNum() {
        return penultimateAuxNum;
    }

    public void setPenultimateAuxNum(Integer penultimateAuxNum) {
        this.penultimateAuxNum = penultimateAuxNum;
    }

    @Basic
    @Column(name = "antepenult_principal")
    public Integer getAntepenultPrincipal() {
        return antepenultPrincipal;
    }

    public void setAntepenultPrincipal(Integer antepenultPrincipal) {
        this.antepenultPrincipal = antepenultPrincipal;
    }

    @Basic
    @Column(name = "antepenult_principal_name")
    public String getAntepenultPrincipalName() {
        return antepenultPrincipalName;
    }

    public void setAntepenultPrincipalName(String antepenultPrincipalName) {
        this.antepenultPrincipalName = antepenultPrincipalName;
    }

    @Basic
    @Column(name = "antepenult_aux_num")
    public Integer getAntepenultAuxNum() {
        return antepenultAuxNum;
    }

    public void setAntepenultAuxNum(Integer antepenultAuxNum) {
        this.antepenultAuxNum = antepenultAuxNum;
    }

    @Basic
    @Column(name = "outer_principal")
    public Integer getOuterPrincipal() {
        return outerPrincipal;
    }

    public void setOuterPrincipal(Integer outerPrincipal) {
        this.outerPrincipal = outerPrincipal;
    }

    @Basic
    @Column(name = "outer_principal_name")
    public String getOuterPrincipalName() {
        return outerPrincipalName;
    }

    public void setOuterPrincipalName(String outerPrincipalName) {
        this.outerPrincipalName = outerPrincipalName;
    }

    @Basic
    @Column(name = "outer_aux_num")
    public Integer getOuterAuxNum() {
        return outerAuxNum;
    }

    public void setOuterAuxNum(Integer outerAuxNum) {
        this.outerAuxNum = outerAuxNum;
    }

    @Basic
    @Column(name = "enabled")
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time")
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "effect_time")
    public Date getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Transient
    public List<PdPackagingStructureHistory> getStructureList() {
        return structureList;
    }

    public void setStructureList(List<PdPackagingStructureHistory> structureList) {
        this.structureList = structureList;
    }

    @Transient
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
