<template>
  <div id="needRecoveredAmountOk" style="overflow-y: auto;" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="已收回"></TY_NavTop>
    <div class="detailInfo">
      <div class="detail" v-if="details.srmSupplier && details.poLoan.amount">
          <span>
            <span class="ttl1">供应商：</span><span> {{details.srmSupplier.fullName }}</span><span class="fr">{{ details.srmSupplier.codeName}}</span>
          </span><br/>
        <span><span class="ttl1">需收回的金额</span><span class="fr"> {{details.poLoan.amount && details.poLoan.amount.toFixed(2)}} 元 </span></span>
      </div>
      <div class="detail" v-if="details.overLoanBiz && details.poLoan.amount">
        <div>
           <span>
            <span class="ttl1">原始的付款方：</span><span> {{details.overLoanBiz.supplierName }}</span>
          </span>
        </div>
        <div>
          <span><span class="ttl1">需收回的金额</span><span class="fr"> {{details.poLoan.amount && details.poLoan.amount.toFixed(2)}} 元 </span></span>
        </div>
      </div>
      <div class="payC">
        <span class="ttl1">已收回</span>
        <span class="fr"> {{details.poLoan.paidTimes || 0 }}笔，共{{(details.poLoan.paidAmount || 0) && details.poLoan.paidAmount.toFixed(2) }}元 </span>
      </div>
    </div>
    <div class="marT20">
      <el-table :data="list" class="tbddd"  stripe  style="margin:0 auto;">
        <el-table-column
          prop="date"
          label="收款完成时间">
        </el-table-column>
        <el-table-column
          label="收款方式及金额">
          <template slot-scope="scope">
            <span class="tdCtrl" @click="getPayDetails(scope.row)">
              {{ scope.row.amount }}
               <i class="el-icon-arrow-right marL"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<style lang="less" scoped>
.tdCtrl{
  display: block;
  cursor: pointer;
  position: relative;
}
.tbddd .el-table__header-wrapper{
  position: relative;
  left: 18px;
}
.marT20{
  margin-top: 20px;
}
.detailInfo{
  padding:10px 20px 10px 20px;
  background:#fff; font-size:14px;
  border-bottom:1px solid #ddd ;
  line-height: 26px;
  .ttl1{
    display: inline-block;
    width: 100px;
    margin-right: 10px;
  }
}
</style>
<script>
import * as moment from 'moment'
export default {
  name: 'needRecoveredAmountOk',
  data () {
    return {
      poLoanId: '',
      pund: '',
      loading: true,
      toggle: -1,
      detailsInfo: {},
      details: {
      },
      list: []
    }
  },
  created () {
    this.poLoanId = this.$route.params.poLoanId
    this.details = this.$store.getters.getBeRecoveredInfo
    console.log('getBeRecoveredInfo', this.details)
    let type = Number(this.details.pund)
    if(isNaN(type)){
      this.$router.back(-1)
      return false
    }
    this.pund = type
    if (type === 1) {
      this.detailsInfo = this.details.poOrdersPrepayment
    } else  if (type === 3) {
      this.detailsInfo = this.details.overLoanBiz
    } else {
      this.detailsInfo = this.details.poPaymentApplication
    }
    this.getList(type)
  },
  methods: {
    getPayDetails:function (itemTr) {
      console.log('点击tr:', itemTr)
      this.$router.push({
        path: `/needRecoveredAmountOkInfo/${itemTr.id}`
      })
    },
    formatMethod (item) {
      let str = ''
      switch (Number(item.method)) {
        case 1:
          str = '现金'; break
        case 5:
          str = '银行转账'; break
        case 4:
          str = '承兑汇票（外）'; break
        case 3:
          if (Number(item.cheque) > 0) { // cheque & returnBill 为 支票ID
            str = '转账支票（内）'; break
          } else if (Number(item.returnBill) > 0) {
            str = '转账支票（外）'; break
          } else {
            str = `未识别支付方式`
          }
          break
        default:
          str = `未识别支付方式:${item.method}`
      }
      return str
    },
    getList: function (type) {
      let that = this
      that.loading = false
      let params = { business:  that.poLoanId }
      let businessType = this.details.poLoan.type
      if (Number(businessType) === 1 || Number(businessType) === 2) { // 预付款
        params.businessType = 'po_loan'
      } else if (Number(businessType) === 4) {
        params.businessType = 'sale'
      } else if (Number(businessType) === 5) {
        params.businessType = 'payment'
      }
      this.axios.post('../../../amountRecovered/getRecoveredList.do', params)
        .then((res) => {
          let arr = res.data.data.recoveredList || []
          arr.forEach(item => {
            let methodStr = this.formatMethod(item)
            that.list.push({
              id: item.id ,
              date: moment(item.updateDate).format('YYYY-MM-DD HH:mm:ss'),
              amount: `${methodStr} - ${item.amount && item.amount.toFixed(2)}元`
            })
          })
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  },
  computed: {
  }
}
</script>
