<template>
  <div class="purchaseContractIndex">
    <div class="mainCon" v-if="page === 'main'">
      <el-row>
        <el-col :span="12">本公司的采购合同统计如下：</el-col>
        <el-col :span="12" class="txtRight">
          <el-button type="primary" @click="addPurContractBtn">新增合同</el-button>
          <el-button type="primary" @click="seeAllContract">直接查看全部合同</el-button>
        </el-col>
      </el-row>
      <el-table :data="supplierList" align="center" border :cell-style="tableCellStyle" :header-cell-style="tableCellStyle" fit style="width: 100%">
        <el-table-column label="供应商" prop="fullName"></el-table-column>
        <el-table-column label="材料">
          <template #default="scope">
            <span>{{ scope.row.numM }}种</span>
          </template>
        </el-table-column>
        <el-table-column label="有效期内的合同">
          <template #default="scope">
            <span>{{ scope.row.contractNum }}种</span>
          </template>
        </el-table-column>
        <el-table-column label="所涉材料">
          <template #default="scope">
            <span>{{ scope.row.inNum }}种</span>
          </template>
        </el-table-column>
        <el-table-column label="已不再执行的合同">
          <template #default="scope">
            <span>{{ scope.row.otherContractNum }}种</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" link @click="seeSupplier(scope.row)"><b>查看</b></el-button>
          </template>
        </el-table-column>
      </el-table>
<!--      <TyPage v-if="indexPageShow"
              :curPage="pageInfo.currentPageNo" :pageSize="pageInfo.pageSize"
              :allPage="pageInfo.totalPage" :pageClickFun="pageClick"></TyPage>-->

    </div>
    <section v-if="page === 'seeContractBySup'">
      <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
        <div>
          <el-button type="default" @click="page = 'main'">返回</el-button>
          <el-button type="primary" @click="addPurContractBtn">新增合同</el-button>
        </div>
        <div class="rightBtn">
          <el-button type="primary" @click="seeExpireContract()">已到期的合同</el-button>
          <el-button type="primary" @click="seeStopContract()">已暂停/终止的合同</el-button>
        </div>
      </div>
      <div class="row gapRow" style="margin-top: 16px">
        <el-text size="large">
          {{seeContractBySup.info.fullName}}
        </el-text>
      </div>
      <div class="row gapRow" style="margin-top: 16px">
        <el-text>
          处于有效期内的采购合同共{{seeContractBySup.list1.length}}个，具体如下
        </el-text>
      </div>
      <el-table :data="seeContractBySup.list1" border style="width: 100%; margin-top: 16px">
        <el-table-column label="合同编号">
          <template #default="scope">
            {{scope.row.sn}}
          </template>
        </el-table-column>
        <el-table-column label="所涉材料" width="100">
          <template #default="scope">
            {{scope.row.materialCount}}种
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="250">
          <template #default="scope">
            {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="签署日期" width="150">
          <template #default="scope">
            {{$filter.format(scope.row.signTime, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="scope">
            {{scope.row.memo}}
          </template>
        </el-table-column>
        <el-table-column label="本版本合同的创建" width="250">
          <template #default="scope">
            {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="manage_mtList(scope.row)">本合同下的材料</el-dropdown-item>
                  <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                  <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                  <el-dropdown-item @click="manage_change(scope.row)">修改合同信息</el-dropdown-item>
                  <el-dropdown-item @click="manage_renew(scope.row, 1)">续约</el-dropdown-item>
                  <el-dropdown-item @click="manage_stop(scope.row)">暂停履约/合同终止</el-dropdown-item>
                  <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                  <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <section v-if="page === 'seeExpireContractBySup'">
      <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
        <div>
          <el-button type="default" @click="page = 'seeContractBySup'">返回</el-button>
        </div>
      </div>
      <div class="row gapRow" style="margin-top: 16px">
        <el-text size="large">
          {{seeContractBySup.info.fullName}}
        </el-text>
      </div>
      <div class="row gapRow" style="margin-top: 16px">
        <el-text>
          已到期的采购合同{{seeContractBySup.list3.length}}个，具体如下
        </el-text>
      </div>
      <el-table :data="seeContractBySup.list3" border style="width: 100%; margin-top: 16px">
        <el-table-column label="合同编号">
          <template #default="scope">
            {{scope.row.sn}}
          </template>
        </el-table-column>
        <el-table-column label="所涉材料" width="100">
          <template #default="scope">
            {{scope.row.materialCount}}种
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="250">
          <template #default="scope">
            {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="签署日期" width="150">
          <template #default="scope">
            {{$filter.format(scope.row.signTime, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="scope">
            {{scope.row.memo}}
          </template>
        </el-table-column>
        <el-table-column label="本版本合同的创建" width="250">
          <template #default="scope">
            {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="manage_mtList(scope.row)">本合同下的材料</el-dropdown-item>
                  <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                  <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                  <el-dropdown-item @click="manage_renew(scope.row, 3)">续约</el-dropdown-item>
                  <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                  <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <section v-if="page === 'seeStopContractBySup'">
      <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
        <div>
          <el-button type="default" @click="page = 'seeContractBySup'">返回</el-button>
        </div>
      </div>
      <div class="row" style="margin-top: 16px">
        <el-text size="large">
          {{seeContractBySup.info.fullName}}
        </el-text>
      </div>
      <div class="row" style="margin-top: 16px">
        <el-text>
          已暂停履约/终止的采购合同共{{seeContractBySup.list2.length}}个，具体如下
        </el-text>
      </div>
      <el-table :data="seeContractBySup.list2" border style="width: 100%; margin-top: 16px">
        <el-table-column label="合同编号">
          <template #default="scope">
            {{scope.row.sn}}
          </template>
        </el-table-column>
        <el-table-column label="所涉材料" width="100">
          <template #default="scope">
            {{scope.row.materialCount}}种
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="250">
          <template #default="scope">
            {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="签署日期" width="150">
          <template #default="scope">
            {{$filter.format(scope.row.signTime, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="scope">
            {{scope.row.memo}}
          </template>
        </el-table-column>
        <el-table-column label="本版本合同的创建" width="200">
          <template #default="scope">
            {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
          </template>
        </el-table-column>
        <el-table-column label="暂停履约/终止的操作" width="250">
          <template #default="scope">
            {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="manage_mtList(scope.row)">本合同下的材料</el-dropdown-item>
                  <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                  <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                  <el-dropdown-item @click="manage_recovery(scope.row)">恢复履约/重启合作</el-dropdown-item>
                  <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                  <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <section v-if="page === 'seeContractByAll'">
      <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
        <div>
          <el-button type="default" @click="prevStep">返回</el-button>
          <el-button type="primary" @click="addPurContractBtn">新增合同</el-button>
        </div>
        <div class="rightBtn">
          <el-button type="primary" @click="seeExpireContractByAll()">已到期的合同</el-button>
          <el-button type="primary" @click="seeStopContractByAll()">已暂停/终止的合同</el-button>
        </div>
      </div>
      <div class="row" style="margin-top: 16px">
        <el-text>
          处于有效期内的采购合同共{{seeContractBySup.list1.length}}个，具体如下
        </el-text>
      </div>
      <el-table :data="seeAllContractList.list1" border style="width: 100%; margin-top: 16px">
        <el-table-column label="合同编号">
          <template #default="scope">
            {{scope.row.sn}}
          </template>
        </el-table-column>
        <el-table-column label="所属供应商">
          <template #default="scope">
            {{scope.row.supplierName}}
          </template>
        </el-table-column>
        <el-table-column label="所涉材料" width="150">
          <template #default="scope">
            {{scope.row.materialCount}}种
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="250">
          <template #default="scope">
            {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="签署日期" width="150">
          <template #default="scope">
            {{$filter.format(scope.row.signTime, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="scope">
            {{scope.row.memo}}
          </template>
        </el-table-column>
        <el-table-column label="本版本合同的创建" width="220">
          <template #default="scope">
            {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="manage_mtList(scope.row)">本合同下的材料</el-dropdown-item>
                  <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                  <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                  <el-dropdown-item @click="manage_change(scope.row)">修改合同信息</el-dropdown-item>
                  <el-dropdown-item @click="manage_renew(scope.row, 1)">续约</el-dropdown-item>
                  <el-dropdown-item @click="manage_stop(scope.row)">暂停履约/合同终止</el-dropdown-item>
                  <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                  <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <section v-if="page === 'seeExpireContractByAll'">
      <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
        <div>
          <el-button type="default" @click="page = 'seeContractByAll'">返回</el-button>
        </div>
      </div>
      <div class="row" style="margin-top: 16px">
        <el-text>
          已到期的采购合同共{{seeAllContractList.list3.length}}个，具体如下
        </el-text>
      </div>
      <el-table :data="seeAllContractList.list3" border style="width: 100%; margin-top: 16px">
        <el-table-column label="合同编号">
          <template #default="scope">
            {{scope.row.sn}}
          </template>
        </el-table-column>
        <el-table-column label="所属供应商">
          <template #default="scope">
            {{scope.row.supplierName}}
          </template>
        </el-table-column>
        <el-table-column label="所涉材料" width="150">
          <template #default="scope">
            {{scope.row.materialCount}}种
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="250">
          <template #default="scope">
            {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="签署日期" width="150">
          <template #default="scope">
            {{$filter.format(scope.row.signTime, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="scope">
            {{scope.row.memo}}
          </template>
        </el-table-column>
        <el-table-column label="本版本合同的创建" width="220">
          <template #default="scope">
            {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="manage_mtList(scope.row)">本合同下的材料</el-dropdown-item>
                  <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                  <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                  <el-dropdown-item @click="manage_renew(scope.row, 3)">续约</el-dropdown-item>
                  <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                  <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <section v-if="page === 'seeStopContractByAll'">
      <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
        <div>
          <el-button type="default" @click="page = 'seeContractByAll'">返回</el-button>
        </div>
      </div>
      <div class="row" style="margin-top: 16px">
        <el-text>
          已暂停履约/终止的采购合同共{{seeAllContractList.list2.length}}个，具体如下
        </el-text>
      </div>
      <el-table :data="seeAllContractList.list2" border style="width: 100%; margin-top: 16px">
        <el-table-column label="合同编号">
          <template #default="scope">
            {{scope.row.sn}}
          </template>
        </el-table-column>
        <el-table-column label="所属供应商">
          <template #default="scope">
            {{scope.row.supplierName}}
          </template>
        </el-table-column>
        <el-table-column label="所涉材料" width="150">
          <template #default="scope">
            {{scope.row.materialCount}}种
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="250">
          <template #default="scope">
            {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="签署日期" width="150">
          <template #default="scope">
            {{$filter.format(scope.row.signTime, 'day')}}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="scope">
            {{scope.row.memo}}
          </template>
        </el-table-column>
        <el-table-column label="本版本合同的创建" width="220">
          <template #default="scope">
            {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="manage_mtList(scope.row)">本合同下的材料</el-dropdown-item>
                  <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                  <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                  <el-dropdown-item @click="manage_recovery(scope.row)">恢复履约/重启合作</el-dropdown-item>
                  <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                  <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <!--  弹窗的高低顺序 是按照代码的前后顺序   -->
    <TyDialog v-show="addPurContractLog" width="600" :dialogTitle="editContractTitle" :dialogHide="hideFun" :dialogName="'addPurContractLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('addPurContractLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addPurContractSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form
              :rules="editContractRules"
              label-position="top"
              ref="editContractForm"
              class="editContract"
              :model="form_editContract"
              label-width="110"
          >
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="供应商名称" prop="supplier" v-if="(page === 'seeContractByAll' || page === 'main') && form_editContract.editType === 1">
                  <el-select v-model="form_editContract.supplier" placeholder="请选择" @change="change_supplier">
                    <el-option
                        v-for="item in option_suppliers"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="供应商名称" prop="fullName" v-else>
                  <el-input v-model="form_editContract.supInfo.fullName" placeholder="请录入" readonly disabled/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="合同编号" prop="sn">
                  <el-input v-model="form_editContract.sn" placeholder="请录入"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="签署日期" prop="signTime">
                  <el-date-picker
                      v-model="form_editContract.signTime"
                      type="date"
                      placeholder="请录入"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="合同的有效期" prop="validTime">
                  <el-date-picker
                      v-model="form_editContract.validTime"
                      type="daterange"
                      range-separator="到"
                      start-placeholder="请选择"
                      end-placeholder="请选择"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :unlink-panels="true"
                      :disabled-date="disabledDate"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="合同的扫描件或照片(共可上传9张)" prop="contractBaseImages" class="widthLimit">
              <template #label>
                合同的扫描件或照片(共可上传9张)
                <div style="float: right">
                  <el-button type="primary" link @click="$refs.uploadImgBtn.click()">上传</el-button>
                </div>
              </template>
              <div class="fileBox">
                <el-upload
                    v-model:file-list="form_editContract.contractBaseImages"
                    :headers="imgUpload.uploadHeaders"
                    :action="imgUpload.uploadAction"
                    :accept="imgUpload.uploadLyc"
                    :data="imgUpload.postData"
                    :limit="9"
                    :multiple="true"
                    :on-success="editContract_imgSuccess"
                    :on-preview="editContract_imgPreview"
                    :on-remove="editContract_imgRemove"
                    :on-exceed="editContract_imgHandleExceed"
                    list-type="picture-card"
                >
                  <span style="display: none" ref="uploadImgBtn">上传</span>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="合同的可编辑版" prop="fileList" class="widthLimit">
              <template #label>
                合同的可编辑版
                <div style="float: right">
                  <el-button type="primary" link @click="$refs.uploadFileBtn.click()">上传</el-button>
                </div>
              </template>
              <div class="fileBox">
                <el-upload
                    class="upload_contract-file"
                    v-model:file-list="form_editContract.fileList"
                    :headers="fileUpload.uploadHeaders"
                    :action="fileUpload.uploadAction"
                    :accept="fileUpload.uploadLyc"
                    :data="fileUpload.postData"
                    ref="upload"
                    :limit="1"
                    :on-exceed="editContract_fileHandleExceed"
                    :on-success="editContract_fileSuccess"
                >
                  <span style="display: none" ref="uploadFileBtn">上传</span>
                </el-upload>
              </div>
            </el-form-item>
            <div>
              <el-form-item prop="listMt"  v-show="form_editContract.supplier || form_editContract.supInfo.id" class="widthLimit">
              <template #label>
                本合同下的材料
                <el-button type="primary" link @click="manage_mtList(form_editContract)">{{form_editContract.listMt.length}}</el-button>
                种
                <div style="float: right">
                  <el-button type="primary" link @click="editContract_removeMt()">移出材料</el-button>
                  <el-button type="primary" link @click="editContract_addMt">添加材料</el-button>
                </div>
              </template>
              <el-input :value="filter_good()" readonly disabled/>
            </el-form-item>
            </div>
            <el-form-item label="备注" prop="memo">
              <el-input v-model="form_editContract.memo" placeholder="请录入" show-word-limit maxlength="255"/>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <!-- 增加删除材料）-->
    <TyDialog v-if="contractMtLog" width="800" :dialogTitle="form_changeGood.title" :dialogHide="hideFun" :dialogName="'contractMtLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('contractMtLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addMtSure">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="row gapRow">
          <div><span class="supplerName">{{supplerNameInfo.fullName}}</span>的材料共有以下<span class="tip">{{form_changeGood.list.length}}</span>种</div>
          <div class="rightBtn">
            已选 {{form_changeGood.select.length}} 种
          </div>
        </div>
        <el-table
            align="center"
            :cell-style="tableCellStyle"
            :header-cell-style="tableCellStyle"
            ref="table_changeGood"
            :data="form_changeGood.list"
            border
            @selection-change="editContract_changeGood_handleCheck"
            fit style="width: 100%">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="code" label="材料代号"/>
          <el-table-column prop="name" label="材料名称"/>
          <el-table-column prop="model" label="型号"/>
          <el-table-column prop="specifications" label="规格"/>
          <el-table-column prop="unit" label="计量单位"/>
          <el-table-column label="已包含的合同">
            <template #default="scope">
              <el-button type="primary" link @click="getAllContractByGood(scope.row)"><b>{{scope.row.contractNum || 0}}个</b></el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <el-dialog v-model="dialog_visible_imgShow">
      <div class="text-center">
        <img w-full :src="imgShow" alt="Preview Image" style="width: 100%"/>
      </div>
    </el-dialog>
    <TyDialog v-if="dialog_visible_manage_img" width="700" dialogTitle="合同的扫描件或照片" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_img'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_img')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-image
              v-for="(item, index) in list_seeContractImg.list"
              :key= index
              style="width: 80px; height: 80px"
              :src="item.url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="list_seeContractImg.preview"
              :initial-index="index"
              fit="cover"
          />
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_recoveryExpire" width="500" dialogTitle="！提示" :dialogHide="hideFun" :dialogName="'dialog_visible_recoveryExpire'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_recoveryExpire')">关闭</el-button>
        <el-button type="primary" @click="manage_recoveryExpire_submit()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 400px; margin: 0 auto">
          本合同已于{{recoveryExpire.validEnd}}到期。请确定具体要进行哪项操作：
          <el-radio-group v-model="recoveryExpire.radio" style="flex-direction: column; align-items: flex-start; margin: 16px 0; padding-left: 46px">
            <el-radio :value="1">不再执行，转入“已到期的合同”</el-radio>
            <el-radio :value="2">续约</el-radio>
          </el-radio-group>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_manage_renewHis" width="700" dialogTitle="本合同的续约记录" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_renewHis'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_renewHis')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-table :data="seeContractRenewHis.list" border style="width: 100%; margin-top: 16px">
            <el-table-column label="版本" width="200">
              <template #default="scope">
                {{scope.$index === 0?'第1版（原始版本）':'第' + (scope.$index + 1) + '版（第' + scope.$index + '次续约后）'}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'renew')"><b>查看</b></el-button>
              </template>
            </el-table-column>
            <el-table-column label="创建（时间为各版本合同的续约时间）" width="300">
              <template #default="scope">
                {{scope.row.updateName || scope.row.createName}}
                {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_manage_changeHis" width="700" dialogTitle="本版本合同的修改记录" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_changeHis'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_changeHis')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <div class="row">
            当前数据为本版本合同第{{seeContractChangeHis.list.length - 1}}次修改后的结果。
            <div class="rightBtn">
              修改时间：{{$filter.format(seeContractChangeHis.list.at(-1)?.updateDate || seeContractChangeHis.list.at(-1)?.createDate)}}
            </div>
          </div>
          <el-table :data="seeContractChangeHis.list" border style="width: 100%; margin-top: 16px">
            <el-table-column label="记录">
              <template #default="scope">
                {{scope.$index === 0?'本版本合同的原始信息':'第' +scope.$index+ '次修改后'}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'change')"><b>查看</b></el-button>
              </template>
            </el-table-column>
            <el-table-column label="创建者/修改者" width="250">
              <template #default="scope">
                {{scope.row.updateName || scope.row.createName}}
                {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_seeContract" width="700" dialogTitle="查看合同" :dialogHide="hideFun" :dialogName="'dialog_visible_seeContract'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_seeContract')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-descriptions
              class="margin-top"
              :column="1"
              border
          >
            <el-descriptions-item label="合同编号" width="200">{{seeContractDetail.info.sn}}</el-descriptions-item>
            <el-descriptions-item label="签署日期">{{seeContractDetail.info.signTime}}</el-descriptions-item>
            <el-descriptions-item label="合同的有效期">
              {{seeContractDetail.info.validStart}} 至 {{seeContractDetail.info.validEnd}}
            </el-descriptions-item>
            <el-descriptions-item label="合同的扫描件或照片">
              <el-image
                  v-for="(item, index) in seeContractDetail.info.imgList"
                  :key= index
                  style="width: 40px; height: 40px"
                  :src="item.url"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="seeContractDetail.info.previewList"
                  :initial-index="index"
                  fit="cover"
                  :preview-teleported="true"
              />
            </el-descriptions-item>
            <el-descriptions-item label="合同的可编辑版">
              <el-button type="primary" link @click="preview_File(seeContractDetail.info.filePath)" v-if="seeContractDetail.info.filePath"><b>查看</b></el-button>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                本合同下的材料（共{{seeContractDetail.info.listMt?.length}}种）
              </template>
              <el-button type="primary" link @click="manage_mtList(seeContractDetail.info)"><b>查看</b></el-button>
            </el-descriptions-item>
            <el-descriptions-item label="备注">{{seeContractDetail.info.memo}}</el-descriptions-item>
            <el-descriptions-item label="本版本合同的修改记录" v-show="seeContractDetail.info.origin === 'renew'">
              <el-button type="primary" link @click="manage_changeHis(seeContractDetail.info)"><b>查看</b></el-button>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </template>
    </TyDialog>
    <!-- 查看材料-->
    <TyDialog v-if="scanContractMtLog" width="800" dialogTitle="本合同下的材料" :dialogHide="hideFun" :dialogName="'scanContractMtLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('scanContractMtLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="row gapRow">
          <div>本合同下的材料共有以下{{ form_seeGood.list.length }}种</div>
        </div>
        <el-table
            align="center"
            :data="form_seeGood.list"
            border
            fit style="width: 100%">
          <el-table-column prop="code" label="材料代号"/>
          <el-table-column prop="name" label="材料名称"/>
          <el-table-column prop="model" label="型号"/>
          <el-table-column prop="specifications" label="规格"/>
          <el-table-column prop="unit" label="计量单位"/>
          <el-table-column label="已包含的合同">
            <template #default="scope">
              <el-button type="primary" link @click="getAllContractByGood(scope.row)"><b>{{scope.row.contractNum || 0}}个</b></el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_seeContractByGood" width="900" dialogTitle="已包含某材料的采购合同" :dialogHide="hideFun" :dialogName="'dialog_visible_seeContractByGood'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_seeContractByGood')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 800px; margin: 0 auto">
          <div class="row" style="margin-top: 16px">
            <el-text size="large">
              {{form_seeContractByGood.goodStr}}
            </el-text>
          </div>
          <el-table
              :data="form_seeContractByGood.list" border style="width: 100%; margin-top: 16px">
            <el-table-column label="合同编号" prop="sn"></el-table-column>
            <el-table-column label="所涉材料">
              <template #default="scope">
                {{scope.row.materialCount}} 种
              </template>
            </el-table-column>
            <el-table-column label="有效期" width="220">
              <template #default="scope">
                {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="签署日期" width="120">
              <template #default="scope">
                {{$filter.format(scope.row.signTime, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="本版本合同的创建" width="220">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
  </div>
</template>
<script>
import { initNav } from "@/utils/routeChange"
import * as api from "@/api/supplier"
import auth from "@/sys/auth";
import {getRootPathFile} from "@/api/api";
import contractMixin from "@/mixins/equipManage/contractMixin";

export default {
  mixins: [contractMixin],
  data() {
    return {
      pageName:'purchaseContract',
      page: 'main',
      rootPath: { fileUrl :'',  ow365url :'',  uploadUrl :'',  webRoot :'' },
      supplierList: [],
      iconAble: false,
      indexPageShow: false,
      pageInfo:{ 'currentPageNo': 3, 'pageSize': 10 , 'totalPage': 15  },
      contractMtLog: false,
      seeAllContractList: {
        type: 1,
        list1: [],
        list2: [],
        list3: [],
      },
      form_changeGood: {
        title: '',
        list: [],
        select: []
      },
      form_seeGood: {
        title: '',
        list: [],
        select: []
      },
      form_seeContractByGood: {
        cusInfo: {},
        goodInfo: {},
        goodStr: '',
        list: []
      },
      scanContractMtLog: false,
      dialog_visible_seeContractByGood: false,
    }
  },
  components: {
  },
  created(){
    initNav({mid: 'demo', name: '采购合同', pageName: this.pageName}, this.getSupplierListFun(1), this)
  },
  mounted() {

  },
  methods: {
    getSupplierListFun(){
      //let json = {"currentPageNo":currentPageNo, "pageSize": 20}
      api.getSupplierList({})
          .then(res => {
            this.supplierList = res.data.data.list || []
            this.pageInfo = res.data.data.pageInfo
          })
      getRootPathFile()
          .then(res => {
            console.log('RootPath=', res.data)
            this.rootPath = res.data
          })
    },
    addPurContractBtn(){
      if (this.page === 'seeContractByAll' || this.page === 'main') {
        this.form_editContract.supInfo = {}
        api.getSupplierNameList({})
            .then(res => {
              let list = res.data.data.list || []
              this.option_suppliers = list.map(item => {
                return {
                  label: item.fullName,
                  value: item.id
                }
              })
            })
      } else {
        this.form_editContract.supInfo = this.seeContractBySup.info
      }
      this.initContract(1)
    },
    tableCellStyle() {
      return {'border-color': '#c9c6c6'};
    },
    addMtSure() {
      const handleType = this.form_changeGood.handleType
      const select = this.form_changeGood.select
      if (handleType === 'add') {
        this.form_editContract.listMt = select
      }
      if (handleType === 'remove') {
        const nowSelect = this.form_editContract.listMt
        this.form_editContract.listMt = nowSelect.filter(item1 =>
            !select.some(item2 => item2.id === item1.id)
        )
      }
      this.contractMtLog = false
    },
    initPage() {
      let thisPage = this.page
      switch (thisPage) {
        case 'main':
          this.getSupplierListFun(1, 20)
          break
        case 'seeContractBySup':
        case 'seeExpireContractBySup':
        case 'seeStopContractBySup':
          this.getContractList()
          break
        case 'seeContractByAll':
        case 'seeExpireContractByAll':
        case 'seeStopContractByAll':
          this.getAllContractList()
          break
      }
    },
    getAllContractByGood(item) {
      this.dialog_visible_seeContractByGood = true
      this.form_seeContractByGood.list = item.listPoContract || []
    },
    editContract_changeGood_handleCheck(val) {
      this.form_changeGood.select = val
    },

    manage_mtList(item) {
      if (item.listMt) {
        this.form_seeGood.list = item.listMt || []
      } else {
        if (item.id) {
          api.getContractBase(item.id)
              .then(res => {
                let data = res.data.data
                console.log(data)
                this.form_seeGood.list = data.listMt || []
              })
        } else {
          this.form_seeGood.list = item.listMt || []
        }
      }
      this.scanContractMtLog = true
    },
    editContract_removeMt() {
      this.contractMtLog = true
      this.form_changeGood.select = []
      this.form_changeGood.list = this.form_editContract.listMt
      this.form_changeGood.handleType = 'remove'
      this.form_changeGood.title = '从本合同移出材料'
    },
    editContract_addMt() {
      this.contractMtLog = true
      this.supplerNameInfo = this.form_editContract.supInfo
      const productTY = this.form_editContract.canChoosePurchaseMt
      this.form_changeGood.select = this.form_editContract.listMt
      this.form_changeGood.list = productTY
      this.form_changeGood.handleType = 'add'
      this.form_changeGood.title = '向本合同添加材料'
      const select = this.form_changeGood.select
      let that = this
      this.$nextTick(() => {
        select.forEach(item => {
          this.form_changeGood.list.forEach(i => {
            if (item.id === i.id) {
              that.$refs['table_changeGood'].toggleRowSelection(i, true)
            }
          })
        })
      })
    },
    filter_good (data) {
      let nameArr = []
      if (data) {
        nameArr = data.map(item => item.name)
      } else {
        nameArr = this.form_editContract.listMt.map(item => item.name)
      }
      return nameArr.join('、')
    },
    seeSupplier(item) {
      this.seeContractBySup.info = item
      this.page = 'seeContractBySup'
    },
    seeExpireContract() {
      this.page = 'seeExpireContractBySup'
    },
    seeStopContract() {
      this.page = 'seeStopContractBySup'
    },
    getContractList() {
      let typeArr = ['seeContractBySup', 'seeStopContractBySup', 'seeExpireContractBySup']
      let type = typeArr.findIndex(item => item === this.page) + 1
      let id = this.seeContractBySup.info.id
      api.getContractBaseList(type, id)
          .then(res => {
            console.log(res)
            let data = res.data.data.contractBaseList
            this.seeContractBySup['list' + type] = data
          })
    },
    seeAllContract() {
      this.page = 'seeContractByAll'
    },
    seeExpireContractByAll() {
      this.page = 'seeExpireContractByAll'
    },
    seeStopContractByAll() {
      this.page = 'seeStopContractByAll'
    },
    getAllContractList() {
      let typeArr = ['seeContractByAll', 'seeStopContractByAll', 'seeExpireContractByAll']
      let type = typeArr.findIndex(item => item === this.page) + 1
      api.getAllContractList(type)
          .then(res => {
            console.log(res)
            let data = res.data.data.list || []
            this.seeAllContractList['list' + type] = data
          })
    },
    manage_file(item) {
      // 合同的可编辑版
      api.getContractBase(item.id)
          .then(res => {
            let data = res.data.data
            console.log(data)
            let path = data.contractBase.filePath
            if (path) {
              this.preview_File(path)
            } else {
              this.$message({
                type: 'error',
                message: '合同的可编辑版未上传！'
              })
            }
          })

    },
    manage_img(item) {
      // 合同的扫描件或照片
      api.getContractBase(item.id)
          .then(res => {
            let data = res.data.data
            console.log(data)
            let listImage = data.listImage
            listImage.map(item => {
              item.url = auth.webRoot + '/upload/' + item.uplaodPath
              item.normalPath = item.uplaodPath
              item.name = item.title
            })
            this.list_seeContractImg.list = listImage
            this.list_seeContractImg.preview = listImage.map(item => item.url)
            this.dialog_visible_manage_img = true
          })
    },
    prevStep () {
      this.page = 'main'
    },
    msg(res) {
      let code = res.data.code
      let msg = res.data.msg
      let type = ''
      switch (code) {
        case 0:
          type = 'success'
          break
        case 301:
          type = 'warning'
          break
        case 500:
          type = 'error'
          break
      }
      this.$message({
        type: type,
        message: msg,
        plain: true
      })
    },
  },
  computed: {
    editContractTitle() {
      let arr = ['新增合同', '修改合同', '续约合同']
      return arr[this.form_editContract.editType - 1]
    },
  },
  watch: {
    // 每当 question 改变时，这个函数就会执行
    page(newPage) {
      //this.page = newPage
      this.initPage()
    }
  },
  directives: {
    // 在模板中启用 v-focus
    onlyInteger: (el) => {
      // 这会在 `mounted` 和 `updated` 时都调用
      let ele = el.querySelector('input')
      ele.addEventListener('input', () => {
        // 获取输入框的当前值
        let value = ele.value;
        // 使用正则表达式匹配非正整数的部分，并将其替换为空字符串
        value = value.replace(/[^\d]/g, '');
        // 如果值为0，则直接设置为空字符串，因为0不是正整数
        if (value === '0') {
          value = '';
        }
        // 更新输入框的值
        ele.value = value;
      });
    }
  }
}

</script>

<style lang="scss" scoped>
@use "@/style/contrct.scss";
.purchaseContractIndex{
  padding: 50px 100px;
  font-size: 14px;
  .mainCon .el-row{margin-bottom: 18px;}
  .whiteBg{padding: 2px 10px;background-color: #fff;height: 26px;line-height: 26px;}
  .grayBg{padding: 2px 10px;background-color: #ddd;height: 26px;line-height: 26px;}
  .bodySize{ margin: auto; width: 440px }
  .upTip{color: #ccc;}
  .txtPos{display: inline-block;}
  .rowGap {margin-bottom: 18px;line-height: 26px;}
  .gapBt{margin-bottom: 10px;}
  .txtRight{text-align: right;}
  .gapRt{margin-right: 10px;}
  .el-icon{color: #5d9cec;font-size: 12px;}
  .ty-container{
    margin-top:35px; padding:10px;
  }
  .ty-page-header{
    display: flex;
    line-height: 24px;
    padding: 0 0 0 70px;
    color: #5d9cec;
    margin-top: 16px;
    .page-header__left{
      display: flex;
      cursor: pointer;
      margin-right: 40px;
      position: relative;
      &::after {
        content: "";
        position: absolute;
        width: 1px;
        height: 16px;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        background-color: #dcdfe6;
      }
      .icon-back {
        font-size: 18px;
        margin-right: 6px;
        align-self: center;
        position: relative;
        top: 1px;
      }
    }
  }
  .page{
    padding: 8px 0;
    position: relative;
    margin: 0 70px;
    .panel-box{
      border-top: 1px solid #ddd;
      padding-top: 8px;
      &:first-child{
        border: none
      }
    }
  }
  .ty-hr{
    margin: 16px 0;
    width: 100%;
    height: 1px;
    background: #eee;
  }
  .row{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .rightBtn{
      flex: auto;
      text-align: right;
    }
    &.head{
      margin-bottom: 16px;
    }
  }
  .text-center{
    text-align: center;
  }
  .widthLimit .el-form-item__label{
    width: 100%;
    display: block;
  }
  .gapRow{ margin-bottom: 16px;}
  :deep(.el-upload){
    display: none;
  }
}

</style>
