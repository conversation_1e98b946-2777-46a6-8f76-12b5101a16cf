
import 'jquery'
import { myBrowser } from '@/utils/browerUtils'
export function previewOrDownloadFun(obj, type, scopeThis){
    let thatss = scopeThis
    console.log('读取文件obj=', obj)

    var path = obj.getAttribute('path')
    var filename = obj.getAttribute('download')

    var pathArr = path.split(".");

    var url = thatss.rootPath.uploadUrl + path;
    var suffix = pathArr[pathArr.length - 1] ;

    console.log('thatss.rootPath=', thatss.rootPath)

    // const link = document.createElement('a'); //创建下载a标签
    // link.href = URL.createObjectURL(blob);
    // link.download = downloadName; //下载后文件名
    // link.style.display = 'none'; //默认隐藏元素
    // document.body.appendChild(link); // body中添加元素
    // link.click(); // 执行点击事件
    // URL.revokeObjectURL(link.href); //释放Blob对象
    // document.body.removeChild(link);//下载完成后移除元素

    var eleLink = document.createElement('a');
    eleLink.style.display = 'none';
    eleLink.target = '_blank';

    if (type === 1) {
        switch (suffix){
            case 'doc':case 'docx':
            case 'xls':case 'xlsx':
            case 'ppt':
                url = 'https://view.officeapps.live.com/op/view.aspx?src='+thatss.rootPath.fileUrl+path
                break;
            case 'png':case 'jpg':case 'jpeg':case 'gif':
            case "pdf":
            case "txt":
                url = thatss.rootPath.fileUrl+path
                break;
            case "md":
                url = thatss.rootPath.webRoot+'/assets/md/index.html?src=/upload/'+path
                break;
            default:
                // case "rar":case "zip":
                // case "pdf": // wyu：ow365有文件大小(5M)限制，改为直接用浏览器预览
                // case 'et':
                url = thatss.rootPath.ow365url+path
        }
    } else {
        eleLink.download = filename;
        if (['png', 'jpg', 'jpeg', 'gif'].indexOf(suffix) > -1) {
            // if ($.inArray(suffix,['png', 'jpg', 'jpeg', 'gif'])) {
            var broweer = myBrowser();
            console.log('broweer=', broweer)
            if (broweer == 'IE'){
                downloadIEFile(filename, url);
                return false;
            }
        }
    }
    eleLink.href = url;
    document.body.appendChild(eleLink);
    eleLink.click();
    document.body.removeChild(eleLink);
}
export function previewFile(rootPath, path) {
    console.log('rootPath：', rootPath)
    console.log('path：', path)

    let pathArr = path.split('.');

    let url =rootPath.uploadUrl + path;
    let suffix = pathArr[pathArr.length - 1] ;

    let eleLink = document.createElement('a');
    eleLink.style.display = 'none';
    eleLink.target = '_blank';

    switch (suffix){
        case 'doc':case 'docx':
        case 'xls':case 'xlsx':
        case 'ppt':
            url = 'https://view.officeapps.live.com/op/view.aspx?src='+rootPath.fileUrl+path
            break;
        case 'png':case 'jpg':case 'jpeg':case 'gif':
        case "pdf":
        case "txt":
            url = rootPath.fileUrl+path
            break;
        case "md":
            url = rootPath.webRoot+'/assets/md/index.html?src=/upload/'+path
            break;
        default:
            url = rootPath.ow365url+path
    }
    eleLink.href = url;
    document.body.appendChild(eleLink);
    eleLink.click();
    document.body.removeChild(eleLink);
}
export function downloadFile() {

}


//支持IE11
function downloadIEFile(fileName, url, contentType) {
    console.log('downloadIEFile start');
    getDataUrlBySrc(url).then(function (b64) {
        console.log(b64);
        var blob = base64Img2Blob(b64);
        window.navigator.msSaveOrOpenBlob(blob, fileName);
        console.log('downloadIEFile finished');
    });

    console.log('downloadIEFile end');
}
function base64Img2Blob(code){
    var parts = code.split(';base64,');
    var contentType = parts[0].split(':')[1];
    var raw = window.atob(parts[1]);
    var rawLength = raw.length;
    var uInt8Array = new Uint8Array(rawLength);
    for (var i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array], {type: contentType});
}
function getDataUrlBySrc(src) {
    var def = $.Deferred();
    console.log('def=', def)
    const xmlHTTP = new XMLHttpRequest();
    xmlHTTP.open("GET", src, true);
    xmlHTTP.timeout = 20000;
    xmlHTTP.crossDomain = true;
    // 以 ArrayBuffer 的形式返回数据
    xmlHTTP.responseType = "arraybuffer";
    xmlHTTP.onload = function (e) {
        // 1. 将返回的数据存储在一个 8 位无符号整数值的类型化数组里面
        const arr = new Uint8Array(xmlHTTP.response);
        // 2. 转为 charCode 字符串
        const raw = Array.prototype.map
            .call(arr, function (charCode) {
                return String.fromCharCode(charCode);
            })
            .join("");
        // 3. 将二进制字符串转为 base64 编码的字符串
        const b64 = btoa(raw);
        const dataURL = "data:image/jpeg;base64," + b64;
        def.resolve(dataURL);
    };
    xmlHTTP.onerror = function (err) {
        console.log(err);
    };
    xmlHTTP.send();
    return def.promise(); //就在这里调用
}


























