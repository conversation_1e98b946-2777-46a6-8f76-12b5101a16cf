package cn.sphd.miners.modules.publicInformation.dto;

import java.io.Serializable;
import java.util.Date;

public class PiRecord implements Serializable {

    private String nameBefore;
    private String nameAfter;
    private String createName;
    private Date createTime;

    public String getNameBefore() {
        return nameBefore;
    }

    public void setNameBefore(String nameBefore) {
        this.nameBefore = nameBefore;
    }

    public String getNameAfter() {
        return nameAfter;
    }

    public void setNameAfter(String nameAfter) {
        this.nameAfter = nameAfter;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public PiRecord(){

    }

    public PiRecord(String nameBefore, String nameAfter, String createName, Date createTime) {
        this.nameBefore = nameBefore;
        this.nameAfter = nameAfter;
        this.createName = createName;
        this.createTime = createTime;
    }

}
