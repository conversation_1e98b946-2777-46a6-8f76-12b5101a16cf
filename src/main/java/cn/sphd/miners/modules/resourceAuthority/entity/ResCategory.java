package cn.sphd.miners.modules.resourceAuthority.entity;

import org.apache.ibatis.type.Alias;

import javax.persistence.Transient;
import java.math.BigInteger;

/**
 * Created by 朱思旭 on 2017/10/30.
 */

@Alias("resCat")
public class ResCategory {
  private Integer id;
  private Integer org;
  private String orgName;
  private Integer parent;
  private String type;
  private String category;
  private Boolean valid;
  private Integer maxChildCategories;
  private Integer children;
  private Integer descendant;
  private Integer leafs;
  private String path;
  private BigInteger size;
  private String name;
  private String memo;
  private Integer orders;
  private Integer creator;
  private String createName;
  private String createDate;
  private Integer updator;
  private String updateName;
  private String updateDate;
  private Integer approveItem;
  private String approveStatus;
  private Integer approveLevel;
  private Integer auditor;
  private String auditorName;
  private String auditDate;
  private String operation;
  private String applyMemo;
  private String approveMemo;
  private Integer messageId;
  private Integer previousId;
  private Integer versionNo;
  private Byte isTrash;
  private String lockUuid;
  private String batchUuid;

  private Integer user;
  private String childStatus;
  private String fileStatus;
  private String listChooseOrgAndUser;
  private String allParentCategory;

  @Transient
  private String operationTag;   //增减标签


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public String getOrgName() {
    return orgName;
  }

  public void setOrgName(String orgName) {
    this.orgName = orgName;
  }

  public Integer getParent() {
    return parent;
  }

  public void setParent(Integer parent) {
    this.parent = parent;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getCategory() {
    return category;
  }

  public void setCategory(String category) {
    this.category = category;
  }

  public Boolean getValid() {
    return valid;
  }

  public void setValid(Boolean valid) {
    this.valid = valid;
  }

  public Integer getMaxChildCategories() {
    return maxChildCategories;
  }

  public void setMaxChildCategories(Integer maxChildCategories) {
    this.maxChildCategories = maxChildCategories;
  }

  public Integer getChildren() {
    return children;
  }

  public void setChildren(Integer children) {
    this.children = children;
  }

  public Integer getDescendant() {
    return descendant;
  }

  public void setDescendant(Integer descendant) {
    this.descendant = descendant;
  }

  public Integer getLeafs() {
    return leafs;
  }

  public void setLeafs(Integer leafs) {
    this.leafs = leafs;
  }

  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public BigInteger getSize() {
    return size;
  }

  public void setSize(BigInteger size) {
    this.size = size;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getOrders() {
    return orders;
  }

  public void setOrders(Integer orders) {
    this.orders = orders;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public String getCreateDate() {
    return createDate;
  }

  public void setCreateDate(String createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public String getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(String updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getApproveItem() {
    return approveItem;
  }

  public void setApproveItem(Integer approveItem) {
    this.approveItem = approveItem;
  }

  public String getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(String approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApproveLevel() {
    return approveLevel;
  }

  public void setApproveLevel(Integer approveLevel) {
    this.approveLevel = approveLevel;
  }

  public Integer getAuditor() {
    return auditor;
  }

  public void setAuditor(Integer auditor) {
    this.auditor = auditor;
  }

  public String getAuditorName() {
    return auditorName;
  }

  public void setAuditorName(String auditorName) {
    this.auditorName = auditorName;
  }

  public String getAuditDate() {
    return auditDate;
  }

  public void setAuditDate(String auditDate) {
    this.auditDate = auditDate;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public Integer getPreviousId() {
    return previousId;
  }

  public void setPreviousId(Integer previousId) {
    this.previousId = previousId;
  }

  public Integer getVersionNo() {
    return versionNo;
  }

  public void setVersionNo(Integer versionNo) {
    this.versionNo = versionNo;
  }

  public Integer getUser() {
    return user;
  }

  public void setUser(Integer user) {
    this.user = user;
  }

  public String getChildStatus() {
    return childStatus;
  }

  public void setChildStatus(String childStatus) {
    this.childStatus = childStatus;
  }

  public String getFileStatus() {
    return fileStatus;
  }

  public void setFileStatus(String fileStatus) {
    this.fileStatus = fileStatus;
  }

  public String getListChooseOrgAndUser() {
    return listChooseOrgAndUser;
  }

  public void setListChooseOrgAndUser(String listChooseOrgAndUser) {
    this.listChooseOrgAndUser = listChooseOrgAndUser;
  }

  public String getAllParentCategory() {
    return allParentCategory;
  }

  public void setAllParentCategory(String allParentCategory) {
    this.allParentCategory = allParentCategory;
  }

  public ResCategory() {
  }

  public ResCategory(Integer id) {
    this.id = id;
  }

  public String getOperationTag() {
    return operationTag;
  }

  public void setOperationTag(String operationTag) {
    this.operationTag = operationTag;
  }

  public Byte getIsTrash() {
    return isTrash;
  }

  public void setIsTrash(Byte isTrash) {
    this.isTrash = isTrash;
  }

  public String getLockUuid() {
    return lockUuid;
  }

  public void setLockUuid(String lockUuid) {
    this.lockUuid = lockUuid;
  }

  public String getBatchUuid() {
    return batchUuid;
  }

  public void setBatchUuid(String batchUuid) {
    this.batchUuid = batchUuid;
  }
}
