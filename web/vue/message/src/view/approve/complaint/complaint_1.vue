<!-- 立案待审批 -->
<template>
  <div class="ui-cells">
    <p>目前有如下{{ list1.length }}个有待您审批的投诉立案申请</p>
    <a class="ui-cell" @click="jump(item.id)" v-for="(item, index) in list1" v-bind:key="index">
      <div class="ui-cell__bd">
        <div class="ui-cell_con">
          <div class="cusName">{{item.customName}}</div>
          <div><span class="info">接到投诉的日期</span><span class="info">{{item.receiptTime | formatDate('yyyy-MM-dd')}}</span></div>
          <div><span class="info">录入者</span><span class="info">{{item.createName}} {{item.createTime | formatDate}}</span></div>
        </div>
      </div>
      <div class="ui-cell__ft"></div>
    </a>
  </div>
</template>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'complaintApproval1',
  filters: {
    formatDate
  },
  data () {
    return {
      list: this.$store.getters.getComplaintList,
      list1: [], // 待审批
      listeners: []
    }
  },
  created () {
    let _this = this
    _this.$store.dispatch('setNewComplaintNav', 1)
    if (_this.list) {
      _this.list1 = _this.list['listApproveComplaint'] || []
    }
    _this.listeners.push(_this.sphdSocket.subscribe('pendingApproveComplaint', _this.callBack, null, 'user'))
  },
  methods: {
    jump: function (id) {
      window.parent.floatToPage('../../../complaint/complaintInfo.do', { 'id': id, 't': 2 })
    },
    callBack: function (res) {
      let _this = this
      console.log('产生变化通道的返回值')
      let curUserID = _this.sphdSocket.user.userID
      let he = this.$store.getters.getComplaintHe
      _this.$store.dispatch('setNewComplaintList', { 'userID': curUserID, 'role': he }).then((result) => {
        _this.loading = false
      })
    }
  },
  watch: {
    '$store.state.complaintList': {
      immediate: true,
      deep: true,
      handler (complaintList) {
        if (complaintList) {
          this.list = complaintList
          this.list1 = this.list['listApproveComplaint'] || []
        }
      }
    }
  }
}
</script>
