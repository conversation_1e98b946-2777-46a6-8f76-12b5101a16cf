package cn.sphd.miners.modules.personal.dto;

import cn.sphd.miners.modules.personal.entity.PersonnelLeave;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Administrator on 2018/11/7.
 * 请假查询
 */
public class LeaveQuery implements Serializable {
    private Integer id;
    private String name;
    private Long count;
    private List<PersonnelLeave> personnelLeaves;

    public LeaveQuery(Integer id, String name) {
        this.id = id;
        this.name = name;
        this.count = 0L;
        this.personnelLeaves=new ArrayList<>();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public List<PersonnelLeave> getPersonnelLeaves() {
        return personnelLeaves;
    }

    public void setPersonnelLeaves(List<PersonnelLeave> personnelLeaves) {
        this.personnelLeaves = personnelLeaves;
    }

    public void addPersonnelLeaves(PersonnelLeave personnelLeave) {
        this.personnelLeaves.add(personnelLeave);
    }
    public void SetCount() {
        this.count = Long.valueOf(this.personnelLeaves.size());
    }
}
