@charset "utf-8";

.editContract{margin: 0 20px;}
.fileBox{
  border-radius: 3px;
  width: 100%;
  border: 1px solid rgb(228, 231, 237);
  min-height: 14px;
  padding: 8px;
  background: #fff;
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
.gapRow{ margin-bottom: 16px;}

:deep(.el-upload--picture-card){
  width: 64px;
  height: 64px;
  &>i{
    font-size: 16px;
  }
}
:deep(.el-upload-list--picture-card .el-upload-list__item){
  width: 64px;
  height: 64px;
}
:root{
  --el-upload-list-picture-card-size: 48px
}
.widthLimit :deep(.el-form-item__label)  {
  width: 100%;
  display: block;
}