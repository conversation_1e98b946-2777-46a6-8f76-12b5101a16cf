package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtPackagingInfoHistoryDao;
import cn.sphd.miners.modules.material.entity.MtPackagingInfo;
import cn.sphd.miners.modules.material.entity.MtPackagingInfoHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class MtPackagingInfoHistoryDaoImpl extends BaseDao<MtPackagingInfoHistory, Serializable> implements MtPackagingInfoHistoryDao {

    @Override
    public MtPackagingInfoHistory insert(MtPackagingInfo info) {
        MtPackagingInfoHistory history=new MtPackagingInfoHistory();

        BeanUtils.copyProperties(info,history);
        history.setPackaging(info.getId());
        history.setId(null);
        save(history);
        return history;
    }
}
