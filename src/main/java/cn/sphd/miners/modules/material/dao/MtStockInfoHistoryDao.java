package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.MtStockInfoHistory;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2018-01-20.
 */
public interface MtStockInfoHistoryDao extends IBaseDao<MtStockInfoHistory,Serializable> {

    List<Map> getMtSIHListByStockInfoIdForMap(String sql);
}
