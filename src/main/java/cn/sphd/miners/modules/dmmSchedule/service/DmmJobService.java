package cn.sphd.miners.modules.dmmSchedule.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmJob;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;

import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2019/1/24.
 */
public interface DmmJobService {

    void  updateDmmJob(DmmJob dmmJob);

    DmmJob getLastDmmJobBySid(Integer sId);

    DmmJob addDmmJobByUserSchedule(DmmSchedule dmmSchedule);
    Long  addDmmJobByUserScheduleTask(DmmSchedule dmmSchedule, DmmJob dmmJob, Long runTime, Long now);

    List<DmmJob> getDmmJobListBySid(Integer sId);

    DmmJob getDmmJobById(Integer id);

    void  deleteDmmJob(DmmJob dmmJob);

    List<DmmJob> getDmmJobListByDate(Long accId, Date beginDate, Date endDate, PageInfo pageInfo);

    List<DmmJob> getDmmJobListByUserId(Long accId, String keyword, PageInfo pageInfo);


}
