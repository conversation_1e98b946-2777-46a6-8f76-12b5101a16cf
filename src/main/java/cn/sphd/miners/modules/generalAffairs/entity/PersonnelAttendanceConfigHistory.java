package cn.sphd.miners.modules.generalAffairs.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

/**
 * Created by Administrator on 2018/5/5.
 */
@Entity
@Table(name = "t_personnel_attendance_config_history")
public class PersonnelAttendanceConfigHistory implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构id

    @Column(name="record"   , nullable=true , unique=false)
    private Integer record;//修改记录id

    @Column(name="previous_rule"   , nullable=true , unique=false)
    private Integer previousRule;//修改前规则ID[老的规则id]

    @Column(name="rule_code"  , length=10 , nullable=true , unique=false)
    private String ruleCode;//规则代码

    @Column(name="rule_name"  , length=50 , nullable=true , unique=false)
    private String ruleName;//规则名称

    @Column(name="rule_desc"  , length=255 , nullable=true , unique=false)
    private String ruleDesc;//规则描述

    @Column(name="enabled"  ,  nullable=true , unique=false)
    private Boolean enabled;//是否有效,true-有效,false-无效[1-有效 0-失效]

    @Column(name="open_date"   , nullable=true , unique=false)
    private Date openDate;//规则启用日期

    @Column(name="close_date"   , nullable=true , unique=false)
    private Date closeDate;//规则截止日期

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//类型:1-正常班,2-倒班

    @Column(name="effect_date"   , nullable=true , unique=false)
    private Date effectDate;//'修改录入时间生效日期'

    @Column(name="input_time"   , nullable=true , unique=false)
    private Date inputTime;//每日考勤录入时间(如未按时录入,则为旷工)

    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;//上班时间

    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;//下班时间

    @Column(name="is_break"  ,  nullable=true , unique=false)
    private boolean isBreak;//是否中间休息(是否中午考勤),true-是

    @Column(name="break_begin"   , nullable=true , unique=false)
    private Date breakBegin;//午休开始时间

    @Column(name="break_end"   , nullable=true , unique=false)
    private Date breakEnd;//午休结束时间

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name = "operation", nullable=true , unique=false)
    private Byte operation;//操作:0-正常,1-增,2-删,3-改,4-修改录入日期,5-修改迟到早退和请假期设置

    @Column(name="attendance_pattern"   , nullable=true , unique=false)
    private Byte attendancePattern;  //考勤模式:1-考勤宝,2-手工录入

    @Column(name="late_limit"  , length=100 , nullable=true , unique=false)
    private Integer lateLimit;  //迟到时限(分钟)

    @Column(name="early_limit"   , nullable=true , unique=false)
    private Integer earlyLimit;  //早退时限(分钟)

    @Column(name="leave_work"   , nullable=true , unique=false)
    private Boolean leaveWork;  //请假到岗是否使用考勤宝:0-不使用(默认),1-使用

    @Column(name="previous_id"  , length=100 , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="rule", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelAttendanceConfig rule;

    @Column(name="rule"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer ruleId;//新的规则id

    //与考勤部门设置历史表
    @OneToMany(targetEntity=PersonnelAttendanceDepartmentConfigHistory.class, fetch= FetchType.LAZY, mappedBy="rule", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<PersonnelAttendanceDepartmentConfigHistory> personnelAttendanceDepartmentConfigHistoryHashSet = new HashSet<>();

    @Transient
    private List<PersonnelAttendanceDepartmentConfigHistory> personnelAttendanceDepartmentConfigHistoryList;

    @Transient
    private String beginTimeString;//上班时间

    @Transient
    private String endTimeString;//下班时间

    @Transient
    private String breakBeginString;//午休开始时间

    @Transient
    private String breakEndString;//午休结束时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getOpenDate() {
        return openDate;
    }

    public void setOpenDate(Date openDate) {
        this.openDate = openDate;
    }

    public Date getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(Date closeDate) {
        this.closeDate = closeDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }



    public void setInputTime(Date inputTime) {
        this.inputTime = inputTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public void setBreakBegin(Date breakBegin) {
        this.breakBegin = breakBegin;
    }

    public void setBreakEnd(Date breakEnd) {
        this.breakEnd = breakEnd;
    }

    public boolean isBreak() {
        return isBreak;
    }

    public void setBreak(boolean aBreak) {
        isBreak = aBreak;
    }


    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public PersonnelAttendanceConfig getRule() {
        return rule;
    }

    public void setRule(PersonnelAttendanceConfig rule) {
        this.rule = rule;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public Date getInputTime() {
        return inputTime;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public Date getBreakBegin() {
        return breakBegin;
    }

    public Date getBreakEnd() {
        return breakEnd;
    }

    public Set<PersonnelAttendanceDepartmentConfigHistory> getPersonnelAttendanceDepartmentConfigHistoryHashSet() {
        return personnelAttendanceDepartmentConfigHistoryHashSet;
    }

    public void setPersonnelAttendanceDepartmentConfigHistoryHashSet(Set<PersonnelAttendanceDepartmentConfigHistory> personnelAttendanceDepartmentConfigHistoryHashSet) {
        this.personnelAttendanceDepartmentConfigHistoryHashSet = personnelAttendanceDepartmentConfigHistoryHashSet;
    }

    public Date getEffectDate() {
        return effectDate;
    }

    public void setEffectDate(Date effectDate) {
        this.effectDate = effectDate;
    }

    public Integer getRecord() {
        return record;
    }

    public void setRecord(Integer record) {
        this.record = record;
    }

    public Integer getPreviousRule() {
        return previousRule;
    }

    public void setPreviousRule(Integer previousRule) {
        this.previousRule = previousRule;
    }

    public List<PersonnelAttendanceDepartmentConfigHistory> getPersonnelAttendanceDepartmentConfigHistoryList() {
        return personnelAttendanceDepartmentConfigHistoryList;
    }

    public void setPersonnelAttendanceDepartmentConfigHistoryList(List<PersonnelAttendanceDepartmentConfigHistory> personnelAttendanceDepartmentConfigHistoryList) {
        this.personnelAttendanceDepartmentConfigHistoryList = personnelAttendanceDepartmentConfigHistoryList;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Byte getAttendancePattern() {
        return attendancePattern;
    }

    public void setAttendancePattern(Byte attendancePattern) {
        this.attendancePattern = attendancePattern;
    }

    public Integer getLateLimit() {
        return lateLimit;
    }

    public void setLateLimit(Integer lateLimit) {
        this.lateLimit = lateLimit;
    }

    public Integer getEarlyLimit() {
        return earlyLimit;
    }

    public void setEarlyLimit(Integer earlyLimit) {
        this.earlyLimit = earlyLimit;
    }

    public Boolean getLeaveWork() {
        return leaveWork;
    }

    public void setLeaveWork(Boolean leaveWork) {
        this.leaveWork = leaveWork;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getBeginTimeString() {
        return beginTimeString;
    }

    public void setBeginTimeString(String beginTimeString) {
        this.beginTimeString = beginTimeString;
    }

    public String getEndTimeString() {
        return endTimeString;
    }

    public void setEndTimeString(String endTimeString) {
        this.endTimeString = endTimeString;
    }

    public String getBreakBeginString() {
        return breakBeginString;
    }

    public void setBreakBeginString(String breakBeginString) {
        this.breakBeginString = breakBeginString;
    }

    public String getBreakEndString() {
        return breakEndString;
    }

    public void setBreakEndString(String breakEndString) {
        this.breakEndString = breakEndString;
    }
}
