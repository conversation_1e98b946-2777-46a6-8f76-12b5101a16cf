package cn.sphd.miners.modules.mpLiveAssistant.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.HttpClientUtils;
import cn.sphd.miners.modules.about.dto.AboutFileHistory;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.mpLiveAssistant.dto.ShareAppMes;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/mpa")
public class AssistantController {

    //获取5种分享形式的信息
    @AuthPassport(tpMember = true)
    @ResponseBody
    @RequestMapping("/getTtShareMes.do")
    public JsonResult getTtShareMes(AuthInfoDto authInfo){
        ShareAppMes shareAppMes = new ShareAppMes();
        shareAppMes.setTitle("字幕精灵");
        shareAppMes.setDesc("字幕精灵");
        shareAppMes.setShareName(authInfo.getName());
        shareAppMes.setShareTime(new Date());
        HashMap<String, Object> map = new HashMap<>();
        map.put("shareTtAppMes", shareAppMes);
        return new JsonResult(1,map);
    }

}
