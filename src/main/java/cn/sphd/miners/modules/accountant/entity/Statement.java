package cn.sphd.miners.modules.accountant.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Id;
import java.sql.Timestamp;

/**
 * Created by khmsoft on 2017/5/16.
 */
public class Statement {
    private int id;
    private Integer settle;
    private Integer code;
    private Integer org;
    private String cellCode;
    private String formula;
    private String balanceIn;
    private String balanceOut;
    private String memo;
    private Integer creator;
    private String createName;
    private Timestamp createDate;
    private Integer updator;
    private String updateName;
    private Timestamp updateDate;
    private Integer approveItem;
    private String approveStatus;
    private Integer approveLevel;
    private Integer auditor;
    private String auditorName;
    private Timestamp auditDate;
    private String operation;
    private String applyMemo;
    private String approveMemo;
    private Integer messageId;

    @Id
    @Column(name = "id", nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "settle", nullable = true)
    public Integer getSettle() {
        return settle;
    }

    public void setSettle(Integer settle) {
        this.settle = settle;
    }

    @Basic
    @Column(name = "code", nullable = true)
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Basic
    @Column(name = "org", nullable = true)
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "cell_code", nullable = true, length = 20)
    public String getCellCode() {
        return cellCode;
    }

    public void setCellCode(String cellCode) {
        this.cellCode = cellCode;
    }

    @Basic
    @Column(name = "formula", nullable = true, length = 100)
    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    @Basic
    @Column(name = "balance_in", nullable = true, length = 100)
    public String getBalanceIn() {
        return balanceIn;
    }

    public void setBalanceIn(String balanceIn) {
        this.balanceIn = balanceIn;
    }

    @Basic
    @Column(name = "balance_out", nullable = true, length = 100)
    public String getBalanceOut() {
        return balanceOut;
    }

    public void setBalanceOut(String balanceOut) {
        this.balanceOut = balanceOut;
    }

    @Basic
    @Column(name = "memo", nullable = true, length = 255)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator", nullable = true)
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name", nullable = true, length = 100)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date", nullable = true)
    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator", nullable = true)
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name", nullable = true, length = 100)
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date", nullable = true)
    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "approve_Item", nullable = true)
    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    @Basic
    @Column(name = "approve_status", nullable = true, length = 1)
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Basic
    @Column(name = "approve_level", nullable = true)
    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    @Basic
    @Column(name = "auditor", nullable = true)
    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    @Basic
    @Column(name = "auditor_name", nullable = true, length = 100)
    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    @Basic
    @Column(name = "audit_date", nullable = true)
    public Timestamp getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Timestamp auditDate) {
        this.auditDate = auditDate;
    }

    @Basic
    @Column(name = "operation", nullable = true, length = 1)
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "apply_memo", nullable = true, length = 255)
    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    @Basic
    @Column(name = "approve_memo", nullable = true, length = 255)
    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    @Basic
    @Column(name = "message_id", nullable = true)
    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Statement statement = (Statement) o;

        if (id != statement.id) return false;
        if (settle != null ? !settle.equals(statement.settle) : statement.settle != null) return false;
        if (code != null ? !code.equals(statement.code) : statement.code != null) return false;
        if (org != null ? !org.equals(statement.org) : statement.org != null) return false;
        if (cellCode != null ? !cellCode.equals(statement.cellCode) : statement.cellCode != null) return false;
        if (formula != null ? !formula.equals(statement.formula) : statement.formula != null) return false;
        if (balanceIn != null ? !balanceIn.equals(statement.balanceIn) : statement.balanceIn != null) return false;
        if (balanceOut != null ? !balanceOut.equals(statement.balanceOut) : statement.balanceOut != null) return false;
        if (memo != null ? !memo.equals(statement.memo) : statement.memo != null) return false;
        if (creator != null ? !creator.equals(statement.creator) : statement.creator != null) return false;
        if (createName != null ? !createName.equals(statement.createName) : statement.createName != null) return false;
        if (createDate != null ? !createDate.equals(statement.createDate) : statement.createDate != null) return false;
        if (updator != null ? !updator.equals(statement.updator) : statement.updator != null) return false;
        if (updateName != null ? !updateName.equals(statement.updateName) : statement.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(statement.updateDate) : statement.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(statement.approveItem) : statement.approveItem != null)
            return false;
        if (approveStatus != null ? !approveStatus.equals(statement.approveStatus) : statement.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(statement.approveLevel) : statement.approveLevel != null)
            return false;
        if (auditor != null ? !auditor.equals(statement.auditor) : statement.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(statement.auditorName) : statement.auditorName != null)
            return false;
        if (auditDate != null ? !auditDate.equals(statement.auditDate) : statement.auditDate != null) return false;
        if (operation != null ? !operation.equals(statement.operation) : statement.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(statement.applyMemo) : statement.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(statement.approveMemo) : statement.approveMemo != null)
            return false;
        if (messageId != null ? !messageId.equals(statement.messageId) : statement.messageId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (settle != null ? settle.hashCode() : 0);
        result = 31 * result + (code != null ? code.hashCode() : 0);
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (cellCode != null ? cellCode.hashCode() : 0);
        result = 31 * result + (formula != null ? formula.hashCode() : 0);
        result = 31 * result + (balanceIn != null ? balanceIn.hashCode() : 0);
        result = 31 * result + (balanceOut != null ? balanceOut.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        return result;
    }
}
