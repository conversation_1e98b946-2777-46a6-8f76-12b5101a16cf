package cn.sphd.miners.modules.accountantReportTax.entity;

/**
* 返回PC端页面需要的报税记录列表
* */
public class BSBean {

    Integer id;
    Integer report;
    Integer month;//PC端选择的开始日期到截止日期之间的月份
    String yearMonth;//PC端选择的开始日期到截止日期之间的年月,yyyy-MM
    String yearMonthDay;//yyyy-MM-dd
    String startDate;//需报税的开始日期
    String endDate;//需报税的截止日期

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReport() {
        return report;
    }

    public void setReport(Integer report) {
        this.report = report;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getYearMonthDay() {
        return yearMonthDay;
    }

    public void setYearMonthDay(String yearMonthDay) {
        this.yearMonthDay = yearMonthDay;
    }

}
