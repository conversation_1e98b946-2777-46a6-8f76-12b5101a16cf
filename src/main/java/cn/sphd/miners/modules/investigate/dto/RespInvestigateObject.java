package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.modules.investigate.entity.InvestigateObject;

import java.util.List;

public class RespInvestigateObject extends InvestigateObject {
    private String orgName;

    private List<RespInvestigateAnswer> respInvestigateAnswerList;

    private String showValue1;
    private String showValue2;
    private String showValue3;
    private String showValue4;
    private String showValue5;
    private String showValue6;
    private String showValue7;
    private String showValue8;
    private String showValue9;
    private String showValue10;

    public List<RespInvestigateAnswer> getRespInvestigateAnswerList() {
        return respInvestigateAnswerList;
    }

    public void setRespInvestigateAnswerList(List<RespInvestigateAnswer> respInvestigateAnswerList) {
        this.respInvestigateAnswerList = respInvestigateAnswerList;
    }
    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getShowValue1() {
        return showValue1;
    }

    public void setShowValue1(String showValue1) {
        this.showValue1 = showValue1;
    }

    public String getShowValue2() {
        return showValue2;
    }

    public void setShowValue2(String showValue2) {
        this.showValue2 = showValue2;
    }

    public String getShowValue3() {
        return showValue3;
    }

    public void setShowValue3(String showValue3) {
        this.showValue3 = showValue3;
    }

    public String getShowValue4() {
        return showValue4;
    }

    public void setShowValue4(String showValue4) {
        this.showValue4 = showValue4;
    }

    public String getShowValue5() {
        return showValue5;
    }

    public void setShowValue5(String showValue5) {
        this.showValue5 = showValue5;
    }

    public String getShowValue6() {
        return showValue6;
    }

    public void setShowValue6(String showValue6) {
        this.showValue6 = showValue6;
    }

    public String getShowValue7() {
        return showValue7;
    }

    public void setShowValue7(String showValue7) {
        this.showValue7 = showValue7;
    }

    public String getShowValue8() {
        return showValue8;
    }

    public void setShowValue8(String showValue8) {
        this.showValue8 = showValue8;
    }

    public String getShowValue9() {
        return showValue9;
    }

    public void setShowValue9(String showValue9) {
        this.showValue9 = showValue9;
    }

    public String getShowValue10() {
        return showValue10;
    }

    public void setShowValue10(String showValue10) {
        this.showValue10 = showValue10;
    }
}
