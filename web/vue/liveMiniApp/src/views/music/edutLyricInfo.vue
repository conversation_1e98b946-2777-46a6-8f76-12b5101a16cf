<template>
  <div class="edutLyricInfo">
    <Pageheader :title="'修改' +  pageType + '基本信息'" :showBack="true" :showClose="true"
                @mycloseFun="cancelEdit" >
      <div class="submitBtn" @click="submit">确定</div>
    </Pageheader>
    <div class="mainc">
      <el-form label-position="top" label-width="280px" :rules="rules" :model="formData">
        <div v-if="quType == 2">
          <el-form-item label="剧目名称" prop="name">
            <el-input v-model="formData.name"></el-input>
          </el-form-item>
          <el-form-item label="选段名称" prop="nameXuanDuan">
            <el-input v-model="formData.nameXuanDuan"></el-input>
          </el-form-item>
          <el-form-item label="流派">
            <el-input v-model="formData.nameLiu"></el-input>
            <p class="blueTip">注：新增的剧目可供制作节目单时选用</p>
          </el-form-item>

        </div>
        <div v-else>
          <el-form-item label="歌曲名称" >
            <el-input v-model="formData.name"></el-input>
            <p class="blueTip">注：新增的歌曲可供制作节目单时选用</p>
          </el-form-item>
        </div>

        <div v-if="songDetails && songDetails.captionRequired">
          <el-form-item label="伴奏时长"><span class="live-right">{{ playTime }}</span></el-form-item>

          <el-form-item label="说明">
            <el-input type="textarea" v-model="formData.inllustration" :placeholder="'如必要，可录入关于' +   pageType +'原唱的版本等各方面信息'"></el-input>
            <div class="numDiv txtRight" >{{ formData.inllustration.length  }}/20</div>
          </el-form-item>

          <el-form-item label="第一句">
            <el-input type="textarea" v-model="formData.firstSentence" :placeholder="'如必要，可录入关于' +   pageType +'原唱的版本等各方面信息'"></el-input>
            <div class="numDiv txtRight">{{ formData.firstSentence.length  }}/20</div>
          </el-form-item>

          <el-form-item label="最后一句">
            <el-input type="textarea" v-model="formData.lastSentence" :placeholder="'如必要，可录入关于' +   pageType +'原唱的版本等各方面信息'"></el-input>
            <div class="numDiv txtRight" >{{ formData.lastSentence.length  }}/20</div>
          </el-form-item>
        </div>
      </el-form>

    </div>
  </div>
</template>
<script setup>
import { updateSongMessage } from '@/api/music';
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'

const router = useRouter();
//
let data = JSONBig.parse(localStorage.getItem('songMessage'));
const songDetails = data.song
console.log('songDetails==', songDetails)
const quType = localStorage.getItem('quType')
const playTime = songDetails.durationTime
const pageType = quType == 2 ? '戏曲' : '歌曲'

const formData = reactive({
  name:'',
  nameXuanDuan:'',
  nameLiu:'',
  inllustration:'',
  firstSentence:'',
  lastSentence:'',
  approveStatus:'',
  user:''
})

const rules = ref({
  name: [
    { required: true, message: '请输入歌曲名称', trigger: 'blur' },
    {
      min: 3,
      max: 5,
      message: '长度在 3 到 5 个字符',
      trigger: 'blur',
    },
  ],
})

onMounted(() => {
  getDetails()
});


const getDetails = () => {
  formData.name = songDetails.name;
  if(quType == 2){
    formData.nameXuanDuan = songDetails.selections;
    formData.nameLiu = songDetails.genres;
  }
  formData.inllustration = songDetails.inllustration;
  formData.firstSentence = songDetails.firstSentence;
  formData.lastSentence = songDetails.lastSentence;
  console.log('formData=', formData )
  console.log('songDetails=', songDetails )
  console.log('songDetails captionRequired=', songDetails.captionRequired )

}

const submit = () => {
  let params = {
    id: songDetails.id ,
    name: formData.name,
    inllustration:formData.inllustration,
    firstSentence:formData.firstSentence,
    lastSentence: formData.lastSentence,
    selections: formData.nameXuanDuan,
    genres: formData.nameLiu,
  }
  updateSongMessage(params).then(res1=>{
    let res = res1.data
    console.log('red=', res )
    if(res.state == 1){
      ElMessage.success('修改成功');
      cancelEdit()
    }else{
      ElMessage.error('修改失败');
    }


  }).catch(err=>{
    console.log('err=', err)
    ElMessage.error('链接错误');
  })
}
const cancelEdit = () => {
  console.log('cancelAdd')
  localStorage.removeItem('newlycdata')
  localStorage.removeItem('patData')
  localStorage.removeItem('agreeObj')
  localStorage.removeItem('lycTxtObj')
  localStorage.removeItem('assignTasks')
  localStorage.removeItem('staffList')
  localStorage.removeItem('songMessage')
  if(quType == 2){
    router.push({
      name: 'lyricTaskXiQu',
    });
  }else{
    router.push({
      name: 'lyricTask',
    });
  }

}

</script>
<style lang="scss" scoped>
@import "../../style/common.scss";
.edutLyricInfo{
  .submitBtn{
    width: 100%;
    height: 100%;
  }
  .mainc{
    color:#333;
    padding:20px 16px;
  }
  .litInput{
    width: 100px;
  }
  .blueTip{
    color: #0b94ea;
    font-size: 0.8em;
    line-height: 25px;
  }
  .flexc{
    display: flex;
    >div{
      flex: 1;
    }
  }
  .form-item{
    margin-bottom: 10px;
    font-size:0.9em ;
    color: #606266;
  }
  .marl20{
    margin-left: 20px;
  }

  .numDiv{
    display: block;
    width: 100%;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .txtRight{
    text-align: right;
  }

}

//.el-button--text {
//  color: #FFF;
//  background-color:$livePageHeadBg ;
//  border-color: $livePageHeadBg;
//  height: 34px;  line-height: 34px;  padding: 0 20px;
//}
//.el-button--text:focus,.el-button--text:hover {
//  color: #FFF;
//  background-color: $livePageHeadBg;  border-color: $livePageHeadBg ;
//  height: 34px;  line-height: 34px;  padding: 0 20px;
//}
//.el-button--text:active {
//  color: #FFF;
//  background-color:$livePageHeadBg ;
//  border-color:$livePageHeadBg ;
//  height: 34px;
//  line-height: 34px;  padding: 0 20px;
//}
</style>
