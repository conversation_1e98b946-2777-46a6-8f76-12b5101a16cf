<template id="home">
  <div class="home">
    <div class="page_home" v-show="!isResume">
      <div class="nav-bar">
        <div class="nav-bar__left">
          <span class="van-nav-bar__text" @click="lastPage">上一页</span>
        </div>
        <div class="nav-bar__title">个人情况表</div>
        <div class="nav-bar__right">
          <span class="van-nav-bar__text" v-show="active < 3" @click="nextPage">下一页</span>
          <span class="van-nav-bar__text" v-show="active === 3" @click="preview">预览</span>
        </div>
      </div>
      <van-overlay :show="loading">
        <div class="wrapper">
          <van-loading />
        </div>
      </van-overlay>
      <div class="main_container">
        <van-tabs v-model="active" swipeable>
          <van-tab title="基本信息">
            <van-popup v-model="show_address" round position="bottom">
              <van-cascader
                v-model="cascaderValue"
                title="请选择所在地区"
                :options="options"
                @close="show_workDate = false"
                @finish="onFinish"
              />
            </van-popup>
            <van-cell-group>
              <div class="van-cell">
                <div class="van-cell__title van-field__label"><span>照片</span></div>
                <van-uploader
                  v-model="imgList"
                  multiple
                  :max-count="1"
                  :after-read="onRead"
                  :before-delete="onDelete"
                  :max-size="50 * 1024 * 1024 * 8"
                  @oversize="onOversize"
                />
              </div>
              <van-field
                v-model="recruitForm.userName"
                required
                clearable
                label="姓名"
                placeholder="请输入姓名"
                name="userName"
                v-validate="'required|max:20'"
                :error="errors.has('userName')"
                :error-message="errors.first('userName')"
              />
              <van-field
                readonly
                clickable
                label="性别"
                :value="recruitForm.genderName"
                placeholder="请选择性别"
                @click="openPicker('gender')"
                is-link
              />
              <van-field
                v-model="recruitForm.date"
                type="tel"
                label="出生日期"
                placeholder="请选择出生日期"
                readonly
                @click="showDatePicker('birth')"
                is-link
              />
              <van-field
                readonly
                clickable
                label="婚姻状况"
                :value="recruitForm.marryName"
                placeholder="请选择婚姻状况"
                @click="openPicker('marry')"
                is-link
              />
              <van-field
                v-model="recruitForm.nation"
                label="民族"
                placeholder="请输入民族"
              />
              <van-field
                v-model="recruitForm.politicalStatus"
                label="政治面貌"
                placeholder="请输入政治面貌"
              />
              <van-field
                readonly
                clickable
                label="最高学历"
                :value="recruitForm.degreeName"
                placeholder="请选择最高学历"
                @click="openPicker('degree')"
                is-link
              />
              <van-field
                v-model="recruitForm.address"
                label="出生地"
                placeholder="请选择出生地"
                is-link
                readonly
                @click="chooseAddress('address')"
              />

              <van-field
                v-model="recruitForm.addressDetail"
                label="详细地址"
                placeholder="请输入出生地详细地址"
              />
              <van-field
                v-model="recruitForm.actualAddress"
                label="现居地"
                placeholder="请选择现居地"
                is-link
                readonly
                @click="chooseAddress('actualAddress')"
              />
              <van-field
                v-model="recruitForm.actualAddressDetail"
                label="详细地址"
                placeholder="请输入现居地详细地址"
              />
              <van-field
                v-model="recruitForm.idCard"
                label="身份证号"
                pattern="^([0-9]){7,18}(x|X)?$"
                maxlength="18"
                placeholder="请输入身份证号"
              />
              <van-field
                v-model="recruitForm.offerSalaty"
                type="number"
                label="期望薪资"
                placeholder="请输入期望薪资"
                required
                name="offerSalaty"
                v-validate="'required|numeric'"
                :error="errors.has('offerSalaty')"
                :error-message="errors.first('offerSalaty')"
              />
              <van-field
                v-model="recruitForm.offerPost"
                label="应聘职位"
                placeholder="请输入应聘职位"
                :disabled="offerPostDisabled"
              />
              <van-field
                v-model="recruitForm.interesting"
                label="特长"
                placeholder="请输入特长"
              />
              <van-field
                v-model="recruitForm.speciality"
                label="爱好"
                placeholder="请输入爱好"
              />
              <van-field
                v-model="recruitForm.firstLanguage"
                label="第一外语语种"
                placeholder="请输入第一外语语种"
              />
              <van-field
                v-model="recruitForm.firstForeignLevel"
                label="水平或证书"
                placeholder="请输入水平或证书"
              />
              <van-field
                v-model="recruitForm.secondLanguage"
                label="第二外语语种"
                placeholder="请输入第二外语语种"
              />
              <van-field
                v-model="recruitForm.secondForeignLevel"
                label="水平或证书"
                placeholder="请输入水平或证书"
              />
              <van-field
                v-model="recruitForm.computerLevel"
                label="计算机水平证书"
                placeholder="请输入计算机水平证书"
              />
              <van-field
                v-model="recruitForm.otherSkills"
                label="其他技能描述"
                placeholder="请输入其他技能描述"
              />
            </van-cell-group>
          </van-tab>
          <van-tab title="联系方式">
            <van-field
              v-model="recruitForm.mobile"
              type="tel"
              pattern="^(13[0-9]|14[5|7]|15[0|1|2|3|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$"
              label="联系电话"
              placeholder="请输入联系电话"
              required
              name="mobile"
              v-validate="'required|mobile'"
              :error="errors.has('mobile')"
              :error-message="errors.first('mobile')"
            />
            <van-field
              v-model="recruitForm.qq"
              label="QQ"
              placeholder="请输入QQ"
              type="number"
              pattern="[1-9][0-9]{4,14}"
            />
            <van-field
              v-model="recruitForm.email"
              type="email"
              label="E-mail"
              placeholder="请输入E-mail"
              name="email"
              v-validate="'email'"
              :error="errors.has('email')"
              :error-message="errors.first('email')"
            />
            <van-cell>
              如必要，可上传您个人版本的简历！
              <van-uploader
                class="ty-right"
                accept=".doc,.docx,.xls,.xlsx,.pdf,.html,.txt"
                max-count="1"
                :after-read="afterRead"
                v-model="resumeList"
                :before-delete="beforeDelete"
                :max-size="50 * 1024 * 1024 * 8"
                @oversize="onOversize"
              >
                <template #default>
                  <span class="ty-right color-blue">上传</span>
                </template>
              </van-uploader>
            </van-cell>

          </van-tab>
          <van-tab title="工作经历">
            <div class="box" v-for="(occupation, index) in recruitForm.occupations" v-bind:key="occupation.key">
              <div class="right" v-if="index !== 0"><van-button type="danger" size="small" @click="delWork(index)">删除</van-button></div>
              <van-field
                v-model="occupation.corpName"
                label="公司名称"
                placeholder="请输入公司名称"
              />
              <van-field
                label="服务时间"
              >
                <template #input>
                  <input type="tel" placeholder="请选择开始时间" class="van-field__control" v-model="occupation.beginTime" readonly @click="showDatePicker('workBeginTime', index)" is-link>
                  <span style="color: #ccc">-</span>
                  <input type="tel" placeholder="请选择结束时间" class="van-field__control" v-model="occupation.endTime" readonly @click="showDatePicker('workEndTime', index)" is-link style="margin-left: 20px">
                </template>
              </van-field>
              <van-field
                v-model="occupation.salary"
                label="薪资水平"
                type="number"
                placeholder="请输入薪资水平"
                required
                name="salary"
                v-validate="'required|numeric'"
                :error="errors.has('salary')"
                :error-message="errors.first('salary')"
              />
              <van-field
                v-model="occupation.corpSize"
                label="公司规模"
                placeholder="请输入公司规模"
              />
              <van-field
                v-model="occupation.corpNature"
                label="公司性质"
                placeholder="请输入公司性质"
              />
              <van-field
                v-model="occupation.corpDepartment"
                label="所在部门"
                placeholder="请输入所在部门"
              />
              <van-field
                v-model="occupation.post"
                label="职位"
                placeholder="请输入职位"
              />
              <van-field
                v-model="occupation.memo"
                label="未继续工作原因"
                placeholder="请输入未继续工作原因"
              />
              <van-field
                v-model="occupation.jobDesc"
                label="工作描述"
                placeholder="请输入工作描述"
              />
              <van-field
                v-model="occupation.operatingDuty"
                label="工作职责"
                placeholder="请输入工作职责"
              />
            </div>
            <van-button type="info" icon="plus" block @click="addWork">工作经历</van-button>
          </van-tab>
          <van-tab title="教育经历">
            <div class="box" v-for="(educations, index) in recruitForm.educations" v-bind:key="educations.key">
              <div class="right" v-if="index !== 0"><van-button type="danger" size="small" @click="delEducation(index)">删除</van-button></div>
              <van-field
                v-model="educations.collegeName"
                label="毕业院校"
                placeholder="请输入毕业院校"
              />
              <van-field
                label="学习时间"
              >
                <template #input>
                  <input type="tel" placeholder="请选择开始时间" class="van-field__control" v-model="educations.beginTime" readonly @click="showDatePicker('eduBeginTime', index)">
                  <span style="color: #ccc">-</span>
                  <input type="tel" placeholder="请选择结束时间" class="van-field__control" v-model="educations.endTime" readonly @click="showDatePicker('eduEndTime', index)" style="margin-left: 20px">
                </template>
              </van-field>
              <van-field
                v-model="educations.departmentName"
                label="院系"
                placeholder="请输入院系"
              />
              <van-field
                v-model="educations.major"
                label="专业"
                placeholder="请输入专业"
              />
              <van-field
                v-model="educations.degreeDesc"
                label="学历/学位"
                placeholder="请输入学历/学位"
              />
              <van-field
                v-model="educations.majorDesc"
                label="专业描述"
                placeholder="请输入专业描述"
              />
              <van-field
                v-model="educations.memo"
                label="补充说明"
                placeholder="请输入补充说明"
              />
            </div>
            <van-button type="info" icon="plus" block @click="addEducation">增加教育经历</van-button>
          </van-tab>
        </van-tabs>
      </div>
      <van-popup v-model="showPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="columns"
          @cancel="showPicker = false"
          @confirm="onConfirm"
        />
      </van-popup>
      <van-popup v-model="show_workDate" position="bottom" ref="picker">
        <van-datetime-picker
          title="选择年月日"
          type="date"
          v-model="currentDate"
          :min-date="workMinDate"
          :max-date="workMaxDate"
          @cancel="show_workDate = false"
          @confirm="confirmDate"
        />
      </van-popup>
    </div>
    <div class="page_resume" v-show="isResume">
      <van-nav-bar title="个人情况表" />
      <van-overlay :show="loading">
        <div class="wrapper">
          <van-loading />
        </div>
      </van-overlay>
      <div class="main_container">
        <div class="tip">以下内容经本人检查，均真实无误！</div>
        <div>
          <van-cell-group>
            <div class="van-cell head">
              <van-image
                width="70"
                height="70"
                :src="recruitForm.realPath"
              >
                <template v-slot:error>未设置</template>
              </van-image>
              <div class="user_simple">
                <span class="userName">{{recruitForm.userName}}</span>
                <div class="user_simple_gray">{{recruitForm.mobile}}</div>
                <div class="user_simple_gray" v-if="recruitForm.offerPost">应聘职位： {{recruitForm.offerPost}}</div>
              </div>
            </div>
            <div class="van-cell" v-if="line1 !== ''">{{line1}}</div>
            <div class="van-cell" v-if="line2 !== ''">{{line2}}</div>
            <van-cell title="出生地" :value="(recruitForm.address + ' ' + recruitForm.addressDetail)" v-if="recruitForm.address || recruitForm.addressDetail"/>
            <van-cell title="现居地：" :value="(recruitForm.actualAddress + ' ' + recruitForm.actualAddressDetail)" v-if="recruitForm.actualAddress || recruitForm.actualAddressDetail"/>
            <van-cell title="身份证号" :value="recruitForm.idCard" v-if="recruitForm.idCard"/>
            <van-cell title="特长：" :value="recruitForm.interesting" v-if="recruitForm.interesting"/>
            <van-cell title="爱好：" :value="recruitForm.speciality" v-if="recruitForm.speciality"/>
            <van-cell title="期望薪资：" :value="recruitForm.offerSalaty" v-if="recruitForm.offerSalaty"/>
            <van-cell title="QQ： " :value="recruitForm.qq" v-if="recruitForm.qq "/>
            <van-cell title="E-mail： " :value="recruitForm.email" v-if="recruitForm.email"/>
            <van-cell title="个人版本的简历： " :value="recruitForm.realFilePath" v-if="recruitForm.realFilePath">
              <template #default>
                <a class="color-blue" :href="recruitForm.realFilePath" :title="recruitForm.fileName" target="_blank">查看</a>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
        <div class="panel">
          <div class="panel_title">个人技能</div>
          <div class="panel_content">
            <van-cell-group>
              <div class="van-cell" v-if="recruitForm.firstLanguage">第一外语语种：{{recruitForm.firstLanguage}} {{recruitForm.firstForeignLevel}}</div>
              <div class="van-cell" v-if="recruitForm.secondLanguage">第二外语语种：{{recruitForm.secondLanguage}} {{recruitForm.secondForeignLevel}}</div>
              <van-cell title="计算机：" :value="recruitForm.computerLevel" v-if="recruitForm.computerLevel"/>
              <van-cell title="其他技能描述：" :value="recruitForm.otherSkills" v-if="recruitForm.otherSkills"/>
            </van-cell-group>
          </div>
        </div>
        <div class="panel">
          <div class="panel_title">教育经历</div>
          <div class="panel_content">
            <div v-for="(education, index) in recruitForm.educations" v-bind:key="index">
              <div class="van-cell" v-if="education.beginTime">{{education.beginTime | formatDay('YYYY-MM-DD')}} - {{education.endTime | formatDay('YYYY-MM-DD')}}</div>
              <div class="van-cell" v-if="education.collegeName">{{education.collegeName}}</div>
              <div class="van-cell" v-if="education.collegeName || education.major || education.degreeDesc">
                <span v-if="education.collegeName">{{education.departmentName}}</span>
                <span v-if="education.major"> | {{education.major}}</span>
                <span v-if="education.degreeDesc"> | {{education.degreeDesc}}</span>
              </div>
              <div class="van-cell" v-if="education.majorDesc">专业描述 <br> {{education.majorDesc}} </div>
              <div class="van-cell" v-if="education.memo">补充说明 <br> {{education.memo}} </div>
            </div>
          </div>
        </div>
        <div class="panel">
          <div class="panel_title">工作经历</div>
          <div class="panel_content">
            <div v-for="(occupation, index) in recruitForm.occupations" v-bind:key="index">
              <div class="van-cell" v-if="occupation.beginTime">{{occupation.beginTime | formatDay('YYYY-MM-DD')}} - {{occupation.endTime | formatDay('YYYY-MM-DD')}}</div>
              <div class="van-cell" v-if="occupation.corpName">{{occupation.corpName}}</div>
              <div class="van-cell" v-if="occupation.jobDesc || occupation.corpSize">
                <span v-if="occupation.corpNature">{{occupation.corpNature}}</span>
                <span v-if="occupation.corpSize"> | {{occupation.corpSize}}</span>
              </div>
              <div class="van-cell" v-if="occupation.corpDepartment || occupation.post || occupation.salary">
                {{[occupation.corpDepartment, occupation.post, occupation.salary].filter(Boolean).join(' | ')}}
              </div>
              <div class="van-cell" v-if="occupation.memo"><span class="color-red">未继续工作原因：</span><br>{{occupation.memo}}</div>
              <div class="van-cell" v-if="occupation.jobDesc">工作描述 <br>{{occupation.jobDesc}}</div>
              <div class="van-cell" v-if="occupation.operatingDuty">工作职责 <br>{{occupation.operatingDuty}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="footerFixed">
        <van-row gutter="8">
          <van-col span="12">
            <van-button type="default" block @click="quitPreview">退出预览
            </van-button>
          </van-col>
          <van-col span="12">
            <van-button type="info" block @click="submit">
              提交
            </van-button>
          </van-col>
        </van-row>
        <div class="tips" v-if="recruitType !== 'qrcode'"><small class="color-blue">注1：提交后，再次点击链接时，还可查看到所提交内容。</small></div>
        <div class="tips" v-if="intervals > 0"><small class="color-blue">注2：提交后，{{intervals}}分钟内还可修改！</small></div>
        <div class="tips" v-if="intervals === 0"><small class="color-blue">注2：提交后不可修改，请谨慎填写！</small></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .ty-right{
    float: right;
  }
  body{
    padding:0;
    margin: 0;
  }

  .box{
    padding:8px 0;
    border-bottom: 8px solid #eee;
  }
  .right{
    text-align: right;
  }
  #app .van-button{
    margin: 4px 0;
  }
  .nav-bar{
    position: relative;
    height: 46px;
    line-height: 46px;
    text-align: center;
    background-color: #fff;
    -webkit-user-select: none;
    user-select: none;
  }
  .nav-bar__left{
    position: absolute;
    left: 16px;
    bottom: 0
  }
  .nav-bar__title{
    max-width: 60%;
    margin: 0 auto;
    color: #323233;
    font-weight: 500;
    font-size: 16px;
  }
  .nav-bar__right{
    position: absolute;
    bottom: 0;
    right: 16px;
  }

  .page_home .main_container {
    height: calc(100vh - 66px);
  }
  .page_resume .main_container {
    height: calc(100vh - 168px);
  }
  .head{
    display: flex;
  }
  .head .userName{
    font-size: 16px;
    font-weight: bold;
  }
  .user_simple{
    margin-left: 12px;
  }
  .break{
    margin: 0 4px;
  }
  .color-blue{
    color: #5d9cec
  }
  .footerFixed{
    position: fixed;
    bottom: 0;
    padding: 4px 8px;
    background: #f0f0f0;
    border-top: 1px solid #eee;
    width: calc(100% - 16px);
    height: 94px;
  }
  .tips{
    padding: 0 8px;
    color: #5d9cec;
    line-height: 1.5;
  }
  .user_simple_gray{
    color: #969799;
    font-size: 13px;
  }
  .wrapper{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .color-red{
    color: #ff0000;
  }
</style>

<script>
import { ref } from 'vue'
import { useCascaderAreaData } from '@vant/area-data'
// wyu：引相关的库
export default {
  name: 'home',
  data () {
    return {
      show_address: false,
      addressName: '',
      currentDate: new Date(),
      show_workDate: false,
      datePickIndex: 0,
      datePickModelName: '',
      options: useCascaderAreaData(),
      cascaderValue: '',
      workMinDate: new Date(1900, 0, 1),
      workMaxDate: new Date(),
      params: {},
      maxDate: '',
      active: 0,
      imgList: [],
      resumeList: [],
      showPicker: false,
      columns: [],
      pickerName: '',
      recruitForm: {
        imgPath: '',
        filePath: '',
        fileName: '',
        oid: 0,
        userName: '',
        gender: '',
        genderName: '',
        date: '',
        nation: '',
        marry: '',
        marryName: '',
        politicalStatus: '',
        degree: '',
        degreeName: '',
        address: '',
        addressDetail: '',
        actualAddress: '',
        actualAddressDetail: '',
        idCard: '',
        offerSalaty: '',
        offerPost: '',
        interesting: '',
        speciality: '',
        firstLanguage: '',
        firstForeignLevel: '',
        secondLanguage: '',
        secondForeignLevel: '',
        computerLevel: '',
        otherSkills: '',
        mobile: '',
        qq: '',
        email: '',
        occupations: [{
          corpName: '',
          beginTimePicker: '',
          beginTime: '',
          endTimePicker: '',
          endTime: '',
          salary: '',
          corpSize: '',
          corpNature: '',
          corpDepartment: '',
          post: '',
          memo: '',
          jobDesc: '',
          operatingDuty: '',
          showWorkBeginTime: false,
          showWorkEndTime: false
        }],
        educations: [{
          collegeName: '',
          beginTime: '',
          endTime: '',
          departmentName: '',
          major: '',
          degreeDesc: '',
          majorDesc: '',
          memo: ''
        }]
      },
      isResume: false,
      offerPostDisabled: false,
      changeId: 0,
      loading: false,
      recruitType: 'qrcode', // qrcode: 二维码 shareNew: 分享新增 shareChange: 分享修改
      intervals: 0
    }
  },
  created () {
    let params = this.$route.params
    this.params = params
    let recruit = this.$store.getters.getRecruit
    this.intervals = recruit.intervals || 0
    console.log('recruitData.......：', recruit)
    if (recruit.postName) {
      this.recruitForm.offerPost = recruit.postName
      this.offerPostDisabled = true
    } else {
      this.offerPostDisabled = false
    }
    if (recruit.postId) {
      this.recruitType = 'other'
    } else {
      this.recruitType = 'qrcode'
    }
    console.log('recruit', recruit)
    console.log('window.parent.$', window.parent.$)

    // 简历提交记录来的简历id，用来更改已提交过的简历
    if (recruit.offerId) {
      let that = this
      this.changeId = recruit.offerId
      this.loading = true
      this.$http.post(window.parent.$.webRoot + '/recruit/offerInfo.do', { id: recruit.offerId }, { emulateJSON: true })
        .then((response) => {
          let data = response.body.data
          if (data) {
            let offer = data.offer
            let recruitForm = that.recruitForm
            for (let key in recruitForm) {
              if (offer[key]) {
                recruitForm[key] = offer[key]
              }
            }
            recruitForm.date = offer.birthday ? this.$moment(offer.birthday).format('YYYY-MM-DD') : ''
            recruitForm.genderName = that.chargeRecruit('gender', offer.gender)
            recruitForm.marryName = that.chargeRecruit('marry', offer.marry)
            recruitForm.degreeName = that.chargeRecruit('degree', offer.degree)
            recruitForm.filePath = data.filePath
            recruitForm.realFilePath = window.parent.$.webRoot + '/upload/' + data.filePath
            recruitForm.realPath = window.parent.$.webRoot + '/upload/' + offer.imgPath
            recruitForm.fileName = '个人简历'
            let addressArr = that.getCharactersAroundFirstSpace(offer.address)
            let actualAddressArr = that.getCharactersAroundFirstSpace(offer.actualAddress)
            if (addressArr.length === 2) {
              recruitForm.address = addressArr[0]
              recruitForm.addressDetail = addressArr[1]
            }
            if (actualAddressArr.length === 2) {
              recruitForm.actualAddress = actualAddressArr[0]
              recruitForm.actualAddressDetail = actualAddressArr[1]
            }
            that.imgList = offer.imgPath ? [{ url: (window.parent.$.webRoot + '/upload/' + offer.imgPath), isImage: true }] : []
            that.resumeList = data.filePath ? [{ url: (window.parent.$.webRoot + '/upload/' + data.filePath), isImage: false }] : []
            recruitForm.educations = offer.personalEducations
            recruitForm.educations.forEach(item => {
              if (item.beginTime) {
                item.beginTime = this.$moment(item.beginTime).format('YYYY-MM-DD')
                item.endTime = this.$moment(item.endTime).format('YYYY-MM-DD')
              } else {
                item.beginTime = ''
                item.endTime = ''
              }
            })
            recruitForm.occupations = offer.personnelOccupations
            recruitForm.occupations.forEach(item => {
              if (item.beginTime) {
                item.beginTime = this.$moment(item.beginTime).format('YYYY-MM-DD')
                item.endTime = this.$moment(item.endTime).format('YYYY-MM-DD')
              } else {
                item.beginTime = ''
                item.endTime = ''
              }
            })
            if (recruit.postName) {
              recruitForm.offerPost = recruit.postName
              that.offerPostDisabled = true
            } else {
              that.offerPostDisabled = false
            }
            that.loading = false
            console.log(that.recruitForm)
          } else {
            that.$toast('获取失败')
          }
        }).catch(function () {
          that.$toast('系统错误，请重试！')
        })
    }
  },
  methods: {
    addWork () {
      this.recruitForm.occupations.push({
        corpName: '',
        beginTime: '',
        endTime: '',
        salary: '',
        corpSize: '',
        corpNature: '',
        corpDepartment: '',
        post: '',
        memo: '',
        jobDesc: '',
        operatingDuty: ''
      })
    },
    delWork (index) {
      this.recruitForm.occupations.splice(index, 1)
    },
    addEducation () {
      this.recruitForm.educations.push({
        collegeName: '',
        beginTime: '',
        endTime: '',
        departmentName: '',
        major: '',
        degreeDesc: '',
        majorDesc: '',
        memo: ''
      })
    },
    delEducation (index) {
      this.recruitForm.educations.splice(index, 1)
    },
    openPicker (name) {
      try {
        this.showPicker = true
        this.pickerName = name
        switch (name) {
          case 'gender':
            this.columns = ['男', '女']
            break
          case 'marry':
            this.columns = ['未婚', '已婚']
            break
          case 'degree':
            this.columns = ['研究生', '本科', '大专', '中专或高中', '其它']
            break
        }
      } catch (err) {
        console.log('出问题了' + err)
      }
    },
    lastPage () {
      if (this.active > 0) {
        this.active--
      } else {
        this.$router.go(-1)
      }
    },
    nextPage () {
      this.active++
    },
    preview () {
      let that = this
      this.$validator.validateAll().then((result) => {
        console.log(result)
        if (result) {
          that.isResume = true
          console.log('recruitForm', that.recruitForm)
        } else {
          that.$toast('请输入必填项！')
        }
      })
    },

    onRead (file) {
      file.status = 'uploading'
      file.message = '上传中...'
      let recruit = this.$store.getters.getRecruit
      let params = new FormData()
      params.append('file', file.file)
      params.append('module', '招聘管理')
      if (recruit.userId) {
        params.append('userId', recruit.userId)
      }
      console.log('Params', params)
      let url = window.parent.$.webRoot + '/uploads/uploadfyByFile.do'
      this.$http.post(url, params).then(res => {
        console.log(res)
        let path = res.body.filename
        if (path) {
          file.status = 'done'
          file.message = ''
          this.recruitForm.imgPath = path
          this.recruitForm.realPath = window.parent.$.webRoot + '/upload/' + path
          this.recruitForm.imgUid = res.body.fileUid
          let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
          fileDelArr.push({'type': 'fileId', 'fileId': res.body.fileUid})
          window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
        } else {
          file.status = 'failed'
          file.message = '返回路径失败'
        }
      }).catch(err => {
        console.log('err', err)
        file.status = 'done'
        file.message = '上传失败'
      })
    },
    afterRead (file) {
      file.status = 'uploading'
      file.message = '上传中...'
      let recruit = this.$store.getters.getRecruit
      let params = new FormData()
      params.append('file', file.file)
      params.append('module', '招聘管理')
      params.append('userId', recruit.userId)
      let url = window.parent.$.webRoot + '/uploads/uploadfyByFile.do'
      this.$http.post(url, params).then(res => {
        console.log(res)
        let path = res.body.filename
        let name = res.body.originalFilename
        if (path) {
          file.status = 'done'
          file.message = ''
          this.recruitForm.filePath = path
          this.recruitForm.fileName = name
          this.recruitForm.realFilePath = window.parent.$.webRoot + '/upload/' + path
          this.recruitForm.fileUid = res.body.fileUid
          let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
          fileDelArr.push({'type': 'fileId', 'fileId': res.body.fileUid})
          window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
        } else {
          file.status = 'failed'
          file.message = '返回路径失败'
        }
        console.log('this.recruitForm', this.recruitForm)
      }).catch(err => {
        console.log('err', err)
        file.status = 'done'
        file.message = '上传失败'
      })
    },
    onDelete () {
      this.cancelFileDel({
        type: 'fileId',
        fileId: this.recruitForm.imgUid
      }, true)
      this.imgList = []
    },
    beforeDelete () {
      console.log('this.recruitForm.fileUid', this.recruitForm.fileUid)
      this.cancelFileDel({
        type: 'fileId',
        fileId: this.recruitForm.fileUid
      }, true)
      this.resumeList = []
    },
    onOversize (file) {
      console.log(file)
      this.$toast('文件大小不能超过50MB')
    },
    onConfirm (value, index) {
      this.recruitForm[this.pickerName] = Number(index) + 1
      this.recruitForm[this.pickerName + 'Name'] = value
      this.showPicker = false
    },
    quitPreview () {
      this.isResume = false
    },
    submit () {
      this.loading = true
      let that = this

      // 处理数据
      if (this.recruitForm.gender === 2) {
        this.recruitForm.gender = 0
      }
      if (this.recruitForm.marry === 2) {
        this.recruitForm.marry = 0
      }
      // 表单深拷贝后再处理要提交的数据
      const recruit = JSON.parse(JSON.stringify(this.recruitForm))
      recruit.address = recruit.address + ' ' + recruit.addressDetail
      recruit.actualAddress = recruit.actualAddress + ' ' + recruit.actualAddressDetail
      recruit.occupations = JSON.stringify(recruit.occupations)
      recruit.educations = JSON.stringify(recruit.educations)
      // 判断有没有启事id,此id在分享初始页存储，如果不是分享过来的，则简历提交不传任何特殊值

      let recruitData = this.$store.getters.getRecruit

      let paramData = {}
      if (recruitData.postId) {
        let owxrecord = localStorage.getItem('wxrecord')
        if (!owxrecord) {
          let uid = this.getGuid()
          recruitData.hashKey = uid
        } else {
          owxrecord = JSON.parse(owxrecord)
          recruitData.hashKey = owxrecord.hash
        }
        recruitData.noticePostId = recruitData.postId
      }
      // 二维码中有userid和oid 应聘和修改 有noticeId postId hashkey 修改多传一个resumeId
      paramData = {...recruit, ...recruitData}

      console.log('paramData', paramData)
      console.log('recruit', JSON.stringify(recruit))
      console.log('recruitData', JSON.stringify(recruitData))
      this.$http.post(window.parent.$.webRoot + '/recruit/addRecruitUser.do', paramData, {
        emulateJSON: true
      }).then((response) => {
        console.log(response.body)
        let data = response.body
        if (data.data === 1) {
          let data = {
            nid: recruitData.noticeId,
            pid: recruitData.noticePostId,
            create: that.$moment().format('YYYY-MM-DD HH:mm:ss')
          }
          let owxrecord = localStorage.getItem('wxrecord')
          if (owxrecord) {
            owxrecord = JSON.parse(owxrecord)
            owxrecord.data.push(data)
            localStorage.setItem('wxrecord', JSON.stringify(owxrecord))
          } else {
            let wxrecord = {
              hash: recruitData.hashKey,
              data: [{
                nid: recruitData.noticeId,
                pid: recruitData.noticePostId,
                create: that.$moment().format('YYYY-MM-DD HH:mm:ss')
              }]
            }
            localStorage.setItem('wxrecord', JSON.stringify(wxrecord))
          }
          this.$store.dispatch('setRecruit', {})
          that.$toast.success('提交成功！')
          that.cancelFileDel({
            type: 'fileId',
            fileId: this.recruitForm.imgUid
          })
          that.cancelFileDel({
            type: 'fileId',
            fileId: this.recruitForm.fileUid
          })
          that.loading = false
          setTimeout(function () {
            that.close()
          }, 2000)
        } else {
          that.$toast.fail('提交失败！')
        }
      }).catch(function (res) {
        console.log('res', res)
        that.loading = false
        that.$toast.fail('系统错误，请重试！')
      })
    },
    getGuid () {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16 | 0
        let v = c === 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      })
    },
    chargeRecruit (name, data) {
      if (name === 'gender') {
        if (data === '0' || data === '2') {
          return '女'
        } else if (data === '1') {
          return '男'
        } else {
          return ''
        }
      }
      if (name === 'marry') {
        if (data === '0' || data === '2') {
          return '已婚'
        } else if (data === '1') {
          return '未婚'
        } else {
          return ''
        }
      }
      if (name === 'degree') {
        data = Number(data)
        let arr = ['研究生', '本科', '大专', '中专或高中', '其它']
        return arr[data - 1] || ''
      }
      if (name === 'age') {
        if (data) {
          let birthDay = new Date(data)
          let nowDate = new Date()
          let year1 = nowDate.getFullYear()
          let year2 = birthDay.getFullYear()
          return (year1 - year2) + '岁'
        } else {
          return ''
        }
      }
    },
    close () {
      if (typeof (window.WeixinJSBridge) !== 'undefined') {
        window.WeixinJSBridge.call('closeWindow')
      } else if (navigator.userAgent.indexOf('MSIE') > 0) {
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
          window.opener = null
          window.close()
        } else {
          window.open('', '_top')
          window.top.close()
        }
      } else {
        try {
          this.focus()
          self.opener = null
          self.close()
        } catch (e) {
        }
        try {
          window.opener = null
          window.open('', '_self', '')
          window.close()
        } catch (e) {
        }
        try {
          window.open(window.location, '_self').close()
        } catch (e) {
        }
        window.location.replace('about:blank')
      }
    },
    cancelFileDel (option, isDel) {
      let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
      let n = -1
      fileDelArr.forEach(function (item, index) {
        if (option.type === item.type && option[option.type] === item[item.type]) {
          n = index
          fileDelArr.splice(index, 1)
        }
      })
      if (n === -1) {
        console.log('可能面临风险')
        console.log('没匹配到：', option)
      }
      // 如果传了此字段为true，那么也同时会调用删除文件接口
      if (isDel) {
        let type = option.type
        let url = window.parent.$.webRoot + '/uploads/removeByFile.do'
        let data = {
          fileUid: option[type],
          userId: this.$route.params.userId
        }
        this.$http.post(url, data)
      }
      window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    },
    chooseAddress (name) {
      this.addressName = name
      this.show_address = true
      this.cascaderValue = ''
    },
    onFinish ({ selectedOptions }) {
      this.show_address = false
      this.recruitForm[this.addressName] = selectedOptions.map((option) => option.text).join('/')
    },
    confirmDate (value) {
      console.log(value)
      console.log('this.datePickIndex', this.datePickIndex)
      console.log('this.datePickModelName', this.datePickModelName)
      let date = this.$moment(value).format('YYYY-MM-DD')
      let model = this.datePickModelName
      if (model === 'birth') {
        this.recruitForm.date = date
      } else if (model === 'workBeginTime') {
        this.recruitForm.occupations[this.datePickIndex].beginTime = date
      } else if (model === 'workEndTime') {
        this.recruitForm.occupations[this.datePickIndex].endTime = date
      } else if (model === 'eduBeginTime') {
        this.recruitForm.educations[this.datePickIndex].beginTime = date
      } else if (model === 'eduEndTime') {
        this.recruitForm.educations[this.datePickIndex].endTime = date
      }
      this.show_workDate = false
    },
    showDatePicker (name, index) {
      // 显示picker
      this.show_workDate = true
      this.currentDate = new Date()
      if (typeof index !== 'undefined') {
        this.datePickIndex = index
      }
      this.datePickModelName = name
    },
    getCharactersAroundFirstSpace (str) {
      const parts = str.split(' ', 2) // 分割字符串至第一个空格，并限制为2个结果
      if (parts.length === 2) {
        return [parts[0], parts[1].trim()] // 返回第一部分和第二部分的前缀，去除两端空白字符
      }
      return [] // 如果没有空格，返回空数组
    }
  },
  computed: {
    line1 () {
      let form = this.recruitForm
      let gender = form.gender !== null && form.gender !== '' ? this.chargeRecruit('gender', form.gender) : ''
      let date = form.date ? ('（' + form.date + '）') : ''
      let age = this.chargeRecruit('age', form.date)
      let marry = form.marry !== null && form.marry !== '' ? this.chargeRecruit('marry', form.marry) : ''
      let arr = [gender, age, date, marry]
      return arr.filter(Boolean).join(' | ')
    },
    line2 () {
      let form = this.recruitForm
      let nation = form.nation || ''
      let politicalStatus = form.politicalStatus || ''
      let marry = form.degree !== null && form.degree !== '' ? this.chargeRecruit('degree', form.degree) : ''
      let arr = [nation, politicalStatus, marry]
      return arr.filter(Boolean).join(' | ')
    }
  },
  destroyed: function () {
    window.parent.cancelFileDel({
      type: 'fileId',
      fileId: this.recruitForm.imgUid
    }, true)
    window.parent.cancelFileDel({
      type: 'fileId',
      fileId: this.recruitForm.fileUid
    }, true)
  }
}
</script>
