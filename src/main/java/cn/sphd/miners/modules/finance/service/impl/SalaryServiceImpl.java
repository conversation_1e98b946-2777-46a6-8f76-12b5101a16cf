package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.FinanceUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxDetailHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.BSBean;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.dto.*;
import cn.sphd.miners.modules.finance.dto.SalaryIndex;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.finance.service.SalaryService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.system.dao.OrgDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.ParseException;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @ClassName SalaryServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/7 8:18
 * @Version 1.0
 */

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class SalaryServiceImpl implements SalaryService {
    @Autowired
    PersonnelSalaryDao personnelSalaryDao;
    @Autowired
    UserDao userDao;
    @Autowired
    PersonnelSalaryHistoryDao personnelSalaryHistoryDao;
    @Autowired
    PersonnelPayPeroidDao personnelPayPeroidDao;
    @Autowired
    PersonnelPayHistoryDao personnelPayHistoryDao;
    @Autowired
    PersonnelPayDao personnelPayDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    OrgDao orgDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    AcctTaxDetailHistoryDao taxDetailHistoryDao;

    @Autowired
    AccountService accountService;
    @Autowired
    AcctReportTaxService acctReportTaxService;
    @Autowired
    DataService dataService;

    @Override
    public void salaryIndexInitialization(int org, String year) {
        Integer month;

        if(year==null){
            month=NewDateUtils.getMonth(new Date());
            year=Integer.toString(NewDateUtils.getYear(new Date()));
        }
        else
            month=12;
        for(int i=1;i<=month;i++){
            String reportingPeriod=year+"-";
            if(i<10)
                reportingPeriod=reportingPeriod+"0"+i;
            else
                reportingPeriod=reportingPeriod+i;
            String hql="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+reportingPeriod+"'";
            List<PersonnelSalary> personnelSalaryList = personnelSalaryDao.getListByHQLWithNamedParams(hql,null);
            if(personnelSalaryList.isEmpty())
            {
                String hql1 = "from User where oid = "+ org +" and roleCode in ('staff','agent','super') and isDuty in ('1','9')";
                List<User> list = userDao.getListByHQLWithNamedParams(hql1,null);
                for(User user:list)
                {
                    PersonnelSalary personnelSalary=new PersonnelSalary();
                    personnelSalary.setOrg(org);
                    personnelSalary.setUser(user.getUserID());
                    personnelSalary.setReportingPeriod(reportingPeriod);
                    personnelSalaryDao.save(personnelSalary);
                }
            }
        }
        if(month==0){
           String year1=Integer.toString((NewDateUtils.getYear(new Date())-1));
            String reportingPeriod=year1+"-12";
            String hql="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+reportingPeriod+"'";
            List<PersonnelSalary> personnelSalaryList = personnelSalaryDao.getListByHQLWithNamedParams(hql,null);
            if(personnelSalaryList.isEmpty())
            {
                String hql1 = "from User where oid = "+ org +" and roleCode in ('staff','agent','super') and isDuty in ('1','9')";
                List<User> list = userDao.getListByHQLWithNamedParams(hql1,null);
                for(User user:list)
                {
                    PersonnelSalary personnelSalary=new PersonnelSalary();
                    personnelSalary.setOrg(org);
                    personnelSalary.setUser(user.getUserID());
                    personnelSalary.setReportingPeriod(reportingPeriod);
                    personnelSalaryDao.save(personnelSalary);
                }
            }
        }
    }

    @Override
    public void salaryByYearInitialization(int org, String year) {
        int month=12;
        int status=0;
        for(int i=1;i<=month;i++){
            String reportingPeriod=year+"-";
            if(i<10)
                reportingPeriod=reportingPeriod+"0"+i;
            else
                reportingPeriod=reportingPeriod+i;
            String hql="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+reportingPeriod+"'";
            List<PersonnelSalary> personnelSalaryList = personnelSalaryDao.getListByHQLWithNamedParams(hql,null);
            if(personnelSalaryList.isEmpty())
            {
                if(status==1){
                    String hql1 = "from User where oid = "+ org +" and roleCode in ('staff','agent','super') and isDuty in ('1','9')";
                    List<User> list = userDao.getListByHQLWithNamedParams(hql1,null);
                    for(User user:list)
                    {
                        PersonnelSalary personnelSalary=new PersonnelSalary();
                        personnelSalary.setOrg(org);
                        personnelSalary.setUser(user.getUserID());
                        personnelSalary.setReportingPeriod(reportingPeriod);
                        personnelSalary.setCreateDate(new Date());
                        personnelSalaryDao.save(personnelSalary);
                    }
                }
            }else{
                status=1;
            }
        }
    }

    @Override
    public SalaryIndex annualSalary(int org, String year) {
        SalaryIndex salaryIndex=new SalaryIndex();
        Integer month=NewDateUtils.getYear(new Date());
        AnnualSalary annualSalary=new AnnualSalary();
        annualSalary.setYear(year);
        Double sum= Double.valueOf(0);
        String sql="SELECT reporting_period,count(*) AS number,IFNULL(sum(fact_salary),0) AS factSalary,IFNULL(sum(income_tax),0) AS tax,IFNULL(sum(insurance_premium),0) AS insurance,IFNULL(sum(accumulation_fund),0) AS housingFund,DATE_FORMAT(create_date, '%Y-%m-%d %T'),DATE_FORMAT(salary_time, '%Y-%m-%d %T'),DATE_FORMAT(tax_time, '%Y-%m-%d %T'),DATE_FORMAT(premium_time, '%Y-%m-%d %T'),DATE_FORMAT(fund_time, '%Y-%m-%d %T') FROM `t_personnel_salary` WHERE org="+org+" and LEFT(reporting_period,4)='"+year+"'  GROUP BY reporting_period ORDER BY reporting_period desc ";
        List<Object[]> personnelSalarylist= personnelSalaryDao.getObjectListBySQL(sql);
        List<MonthSalary> monthSalaryList=new ArrayList<>();
        if (personnelSalarylist.size() > 0 && !personnelSalarylist.isEmpty()) {
            for (int i = 0; i <personnelSalarylist.size(); i++) {
                MonthSalary monthSalary=new MonthSalary();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(personnelSalarylist.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                monthSalary.setReportingPeriod(objects.getString(0));
                monthSalary.setNumber(Integer.valueOf(objects.getString(1)));
                monthSalary.setFactSalary(Double.valueOf(objects.getString(2)));
                monthSalary.setTax(Double.valueOf(objects.getString(3)));
                monthSalary.setInsurance(Double.valueOf(objects.getString(4)));
                monthSalary.setAccumulationFund(Double.valueOf(objects.getString(5)));

                String formatter = "yyyy-MM-dd HH:mm:ss";
                String date=objects.getString(6);
                String salaryTime=objects.getString(7);//个税时间
                String taxTime=objects.getString(8);//创建时间
                String premiumTime=objects.getString(9);//社保时间
                String fundTime=objects.getString(10);//公积金时间
                if(date!=null) {
                    Date cDate=NewDateUtils.dateFromString(date,formatter);
                    monthSalary.setCreateDate(cDate);
                }
                if(salaryTime!=null) {
                    Date cDate=NewDateUtils.dateFromString(salaryTime,formatter);
                    monthSalary.setSalaryTime(cDate);
                }if(taxTime!=null) {
                    Date cDate=NewDateUtils.dateFromString(taxTime,formatter);
                    monthSalary.setTaxTime(cDate);
                }if(premiumTime!=null) {
                    Date cDate=NewDateUtils.dateFromString(premiumTime,formatter);
                    monthSalary.setPremiumTime(cDate);
                }if(fundTime!=null) {
                    Date cDate=NewDateUtils.dateFromString(fundTime,formatter);
                    monthSalary.setFundTime(cDate);
                }
                sum=monthSalary.getFactSalary()+sum;
                String hqlSalary="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+monthSalary.getReportingPeriod()+"' and factSalary is not null";
                List<PersonnelSalary> personnelSalaryList = personnelSalaryDao.getListByHQLWithNamedParams(hqlSalary,null);
                if(personnelSalaryList.size()==0) {
                    monthSalary.setSalaryStatus(0);
                }else if(personnelSalaryList.size()>0 &&personnelSalaryList.size()<monthSalary.getNumber()) {
                    monthSalary.setSalaryStatus(1);
                }else if(personnelSalaryList.size()==monthSalary.getNumber()){
                    monthSalary.setSalaryStatus(2);
                }
                String hqlTax="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+monthSalary.getReportingPeriod()+"' and incomeTax is not null";
                List<PersonnelSalary> personnelTaxList = personnelSalaryDao.getListByHQLWithNamedParams(hqlTax,null);
                if(personnelTaxList.size()==0) {
                    monthSalary.setTaxesStatus(0);
                }else if(personnelTaxList.size()>0 &&personnelTaxList.size()<monthSalary.getNumber()) {
                    monthSalary.setTaxesStatus(1);
                }else if(personnelTaxList.size()==monthSalary.getNumber()){
                    monthSalary.setTaxesStatus(2);
                }
                String hqlInsurance="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+monthSalary.getReportingPeriod()+"' and insurancePremium is not null";
                List<PersonnelSalary> personnelhqlInsuranceList = personnelSalaryDao.getListByHQLWithNamedParams(hqlInsurance,null);
                if(personnelhqlInsuranceList.size()==0) {
                    monthSalary.setInsuranceStatus(0);
                }else if(personnelhqlInsuranceList.size()>0 &&personnelhqlInsuranceList.size()<monthSalary.getNumber()) {
                    monthSalary.setInsuranceStatus(1);
                }else if(personnelhqlInsuranceList.size()==monthSalary.getNumber()){
                    monthSalary.setInsuranceStatus(2);
                }
                String hqlHousingFund="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+monthSalary.getReportingPeriod()+"' and accumulationFund is not null";
                List<PersonnelSalary> personnelhqlHousingFundList = personnelSalaryDao.getListByHQLWithNamedParams(hqlHousingFund,null);
                if(personnelhqlHousingFundList.size()==0) {
                    monthSalary.setAccumulationFundStatus(0);
                }else if(personnelhqlHousingFundList.size()>0 &&personnelhqlHousingFundList.size()<monthSalary.getNumber()) {
                    monthSalary.setAccumulationFundStatus(1);
                }else if(personnelhqlHousingFundList.size()==monthSalary.getNumber()){
                    monthSalary.setAccumulationFundStatus(2);
                }
                monthSalaryList.add(monthSalary);
            }
        }
        annualSalary.setSumSalary(sum);
        annualSalary.setMonthSalaryList(monthSalaryList);
        salaryIndex.setAnnualSalary(annualSalary);
        return salaryIndex;
    }

    @Override
    public MonthSalary monthSalaryInfo(int org, String month) {
        MonthSalary monthSalary=new MonthSalary();
        Double factSalary=Double.valueOf(0);//工资
        Double tax=Double.valueOf(0);//个税
        Double insurance=Double.valueOf(0);//保险
        Double accumulationFund=Double.valueOf(0);//公积金
        String hqlSalary="from PersonnelSalary where org ="+ org +" and reportingPeriod='"+month+"'";
        List<PersonnelSalary> list = personnelSalaryDao.getListByHQLWithNamedParams(hqlSalary,null);
        List<RespPersonnelSalary> personnelSalaryList=new ArrayList<>();
        for(PersonnelSalary personnelSalary:list){
            RespPersonnelSalary respPersonnelSalary=new RespPersonnelSalary();
            BeanUtils.copyPropertiesIgnoreNull(personnelSalary,respPersonnelSalary);
            User user=userDao.get(personnelSalary.getUser());
            if("agent".equals(user.getRoleCode()))
            {
                respPersonnelSalary.setUserName(user.getRoleName().substring(1, user.getRoleName().length()));
            }
           else
                respPersonnelSalary.setUserName(user.getUserName());
            respPersonnelSalary.setPhone(user.getMobile());
            respPersonnelSalary.setDepartName(user.getDepartName());
            respPersonnelSalary.setPostName(user.getPostName());
            if(personnelSalary.getFactSalary()!=null)
            factSalary=factSalary+personnelSalary.getFactSalary();
            if(personnelSalary.getIncomeTax()!=null)
            tax=tax+personnelSalary.getIncomeTax();
            if(personnelSalary.getInsurancePremium()!=null)
            insurance=insurance+personnelSalary.getInsurancePremium();
            if(personnelSalary.getAccumulationFund()!=null)
            accumulationFund=accumulationFund+personnelSalary.getAccumulationFund();
            personnelSalaryList.add(respPersonnelSalary);
        }
        monthSalary.setPersonnelSalaryList(personnelSalaryList);
        monthSalary.setNumber(personnelSalaryList.size());
        monthSalary.setAccumulationFund(accumulationFund);
        monthSalary.setFactSalary(factSalary);
        monthSalary.setInsurance(insurance);
        monthSalary.setTax(tax);
        if(personnelSalaryList.size()>0) {
            if(personnelSalaryList.get(0).getCreateDate()!=null){
                monthSalary.setCreateDate(personnelSalaryList.get(0).getCreateDate());
                monthSalary.setCreateName(personnelSalaryList.get(0).getCreateName());
            }if(personnelSalaryList.get(0).getSalaryTime()!=null){
                monthSalary.setSalaryTime(personnelSalaryList.get(0).getSalaryTime());
                monthSalary.setSalaryName(personnelSalaryList.get(0).getSalaryName());
                monthSalary.setCreateDate(personnelSalaryList.get(0).getSalaryTime());
                monthSalary.setCreateName(personnelSalaryList.get(0).getSalaryName());
            }if(personnelSalaryList.get(0).getTaxTime()!=null){
                monthSalary.setTaxTime(personnelSalaryList.get(0).getTaxTime());
                monthSalary.setTaxName(personnelSalaryList.get(0).getTaxName());
            }if(personnelSalaryList.get(0).getPremiumTime()!=null){
                monthSalary.setPremiumTime(personnelSalaryList.get(0).getPremiumTime());
                monthSalary.setPremiumName(personnelSalaryList.get(0).getPremiumName());
            }if(personnelSalaryList.get(0).getFundTime()!=null){
                monthSalary.setFundTime(personnelSalaryList.get(0).getFundTime());
                monthSalary.setFundName(personnelSalaryList.get(0).getFundName());
            }
        }
        return monthSalary;
    }


    @Override
    public MonthSalary updateMonthSalaryDetails(User user, String month, int type,int updateNo) {
        int operation=type+3;
        MonthSalary monthSalary=new MonthSalary();
        Double factSalary=Double.valueOf(0);//工资
        Double tax=Double.valueOf(0);//个税
        Double insurance=Double.valueOf(0);//保险
        Double accumulationFund=Double.valueOf(0);//公积金
        String hqlSalary="from PersonnelSalary where org ="+ user.getOid() +" and reportingPeriod='"+month+"'";
        List<PersonnelSalary> list = personnelSalaryDao.getListByHQLWithNamedParams(hqlSalary,null);
        List<RespPersonnelSalary> personnelSalaryList=new ArrayList<>();
        for(PersonnelSalary personnelSalary:list){
            RespPersonnelSalary respPersonnelSalary=new RespPersonnelSalary();
            BeanUtils.copyPropertiesIgnoreNull(personnelSalary,respPersonnelSalary);
            User user1=userDao.get(personnelSalary.getUser());
            if("agent".equals(user1.getRoleCode()))
            {
                respPersonnelSalary.setUserName(user1.getRoleName().substring(1, user1.getRoleName().length()));
            }
            else
                respPersonnelSalary.setUserName(user1.getUserName());
            respPersonnelSalary.setPhone(user1.getMobile());
            respPersonnelSalary.setDepartName(user1.getDepartName());
            respPersonnelSalary.setPostName(user1.getPostName());
            String hql = "from PersonnelSalaryHistory where personnelSalary="+personnelSalary.getId()+" and operation='"+operation+"'  and  subVersionNo >="+updateNo;
            List<PersonnelSalaryHistory> salaryHistoryList = personnelSalaryHistoryDao.getListByHQLWithNamedParams(hql, null);
            if(salaryHistoryList.size()>0)
            {
                if(type==1){
                    respPersonnelSalary.setFactSalary(salaryHistoryList.get(0).getFactSalary());
                }else if(type==2){
                    respPersonnelSalary.setIncomeTax(salaryHistoryList.get(0).getIncomeTax());
                }else if(type==3){
                    respPersonnelSalary.setInsurancePremium(salaryHistoryList.get(0).getInsurancePremium());
                }else if(type==4){
                    respPersonnelSalary.setAccumulationFund(salaryHistoryList.get(0).getAccumulationFund());
                }
            }
            String hql1 = "from PersonnelSalaryHistory where personnelSalary="+personnelSalary.getId()+" and operation='"+operation+"'  and  subVersionNo ="+(updateNo-1);
            List<PersonnelSalaryHistory> salaryHistoryList1 = personnelSalaryHistoryDao.getListByHQLWithNamedParams(hql1, null);
            if(salaryHistoryList1.size()>0)
                respPersonnelSalary.setType(1);
            if(respPersonnelSalary.getType()==null)
                respPersonnelSalary.setType(0);

            if(respPersonnelSalary.getFactSalary()!=null)
                factSalary=factSalary+respPersonnelSalary.getFactSalary();
            if(respPersonnelSalary.getIncomeTax()!=null)
                tax=tax+respPersonnelSalary.getIncomeTax();
            if(respPersonnelSalary.getInsurancePremium()!=null)
                insurance=insurance+respPersonnelSalary.getInsurancePremium();
            if(respPersonnelSalary.getAccumulationFund()!=null)
                accumulationFund=accumulationFund+respPersonnelSalary.getAccumulationFund();
            personnelSalaryList.add(respPersonnelSalary);
        }
        String hql1 = "from PersonnelSalaryHistory where org ="+user.getOid() +" and reportingPeriod='"+month+"' and operation='"+operation+"' and subVersionNo= ";
        if(updateNo==0)
            hql1=hql1+updateNo;
            else
            hql1=hql1+(updateNo-1);
        List<PersonnelSalaryHistory> salaryHistoryList1 = personnelSalaryHistoryDao.getListByHQLWithNamedParams(hql1, null);

        if(salaryHistoryList1.size()>0)
        {
            monthSalary.setUpdateName(salaryHistoryList1.get(0).getUpdateName());
            monthSalary.setUpdateDate(salaryHistoryList1.get(0).getUpdateDate());
            if(updateNo==0){
                if(type==1){
                    monthSalary.setCreateName(salaryHistoryList1.get(0).getSalaryName());
                    monthSalary.setCreateDate(salaryHistoryList1.get(0).getSalaryTime());
                }else if(type==2){
                    monthSalary.setCreateName(salaryHistoryList1.get(0).getTaxName());
                    monthSalary.setCreateDate(salaryHistoryList1.get(0).getTaxTime());
                }else if(type==3){
                    monthSalary.setCreateName(salaryHistoryList1.get(0).getPremiumName());
                    monthSalary.setCreateDate(salaryHistoryList1.get(0).getPremiumTime());
                }else if(type==4){
                    monthSalary.setCreateName(salaryHistoryList1.get(0).getFundName());
                    monthSalary.setCreateDate(salaryHistoryList1.get(0).getFundTime());
                }
            }
        }
        monthSalary.setPersonnelSalaryList(personnelSalaryList);
        monthSalary.setNumber(personnelSalaryList.size());
        monthSalary.setAccumulationFund(accumulationFund);
        monthSalary.setFactSalary(factSalary);
        monthSalary.setInsurance(insurance);
        monthSalary.setTax(tax);

        return monthSalary;
    }

    @Override
    public List<SalaryTotalByYear> salaryTotalGroupYear(User user) {
        List<SalaryTotalByYear> list=new ArrayList<>();
        String sql="SELECT LEFT (reporting_period,4) AS `year`,IFNULL(sum(fact_salary),0) AS `salary` FROM `t_personnel_salary` WHERE org="+user.getOid()+" GROUP BY (LEFT (reporting_period,4)) order by (LEFT (reporting_period,4)) desc";
        List<Object[]> personnelSalarylist = personnelSalaryDao.getObjectListBySQL(sql);
        if (personnelSalarylist.size() > 0 && !personnelSalarylist.isEmpty()) {
            for (int i = 0; i <personnelSalarylist.size(); i++) {
                SalaryTotalByYear salaryTotalByYear=new SalaryTotalByYear();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(personnelSalarylist.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                salaryTotalByYear.setYear(objects.getString(0));
                salaryTotalByYear.setSalary(Double.valueOf(objects.getString(1)));
                Double average= Double.valueOf(0);
                String averageSalarySql="SELECT reporting_period,IFNULL(sum(fact_salary),0) AS `salary`,count(*) AS count FROM `t_personnel_salary` WHERE org="+user.getOid()+" AND LEFT (reporting_period,4)='"+salaryTotalByYear.getYear()+"' GROUP BY reporting_period";
                List<Object[]> averageSalary = personnelSalaryDao.getObjectListBySQL(averageSalarySql);
                for (int j = 0; j <averageSalary.size(); j++) {
                    //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                    String str = JSON.toJSONString(averageSalary.get(j));
                    JSONArray object = JSONArray.parseArray(str);
                    Double salary=Double.valueOf(object.getString(1));
                    Double count=Double.valueOf(object.getString(2));
                    if(count.intValue()!=0)
                    average=average+salary/count;
                }
                BigDecimal bg = new BigDecimal(average);
                double average1 = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                salaryTotalByYear.setAverageSalary(average1);
                list.add(salaryTotalByYear);
            }
        }
        return list;
    }




    @Override
    public int addMonthSalary(User user, List<PersonnelSalary> personnelSalaryList,int type) {
        Date newDate=new Date();
        for(PersonnelSalary p:personnelSalaryList)
        {
           PersonnelSalary personnelSalary=personnelSalaryDao.get(p.getId());
           if(type==1){
               personnelSalary.setFactSalary(p.getSalary());
               personnelSalary.setSalaryName(user.getUserName());
               personnelSalary.setSalaryUser(user.getUserID());
               personnelSalary.setSalaryTime(newDate);

               personnelSalary.setCreator(user.getUserID());
               personnelSalary.setCreateName(user.getUserName());
               personnelSalary.setCreateDate(newDate);
           }else if(type==2){
               personnelSalary.setIncomeTax(p.getSalary());
               personnelSalary.setTaxName(user.getUserName());
               personnelSalary.setTaxUser(user.getUserID());
               personnelSalary.setTaxTime(newDate);
           }else if(type==3){
               personnelSalary.setInsurancePremium(p.getSalary());
               personnelSalary.setPremiumName(user.getUserName());
               personnelSalary.setPremiumUser(user.getUserID());
               personnelSalary.setPremiumTime(newDate);
           }else if(type==4){
               personnelSalary.setAccumulationFund(p.getSalary());
               personnelSalary.setFundName(user.getUserName());
               personnelSalary.setFundUser(user.getUserID());
               personnelSalary.setFundTime(newDate);
           }
            personnelSalary.setAdjustDate(newDate);
            personnelSalary.setOperateTime(newDate);
            personnelSalary.setOperator(user.getUserID());
            personnelSalary.setOperation("1");
            personnelSalary.setVersionNo(0);
            personnelSalary.setSubVersionNo(0);
            personnelSalaryDao.update(personnelSalary);
        }
        return 1;
    }

    @Override
    public int updateMonthSalary(User user, List<PersonnelSalary> personnelSalaryList, int type) {
        int version=0;
        if(personnelSalaryList.size()>0) {
            int operation=type+3;
            PersonnelSalary pSalary = personnelSalaryDao.get(personnelSalaryList.get(0).getId());
            String hqlSalary = "from PersonnelSalaryHistory where org =" + pSalary.getOrg() + " and reportingPeriod='" + pSalary.getReportingPeriod() + "' and operation='"+operation+"'";
            List<PersonnelSalaryHistory> list = personnelSalaryHistoryDao.getListByHQLWithNamedParams(hqlSalary, null);
            if (list.size()>0)
                version=list.get(list.size()-1).getSubVersionNo()+1;
        for(PersonnelSalary p:personnelSalaryList) {
            PersonnelSalary personnelSalary = personnelSalaryDao.get(p.getId());
            PersonnelSalaryHistory personnelSalaryHistory=new PersonnelSalaryHistory();
            BeanUtils.copyPropertiesIgnoreNull(personnelSalary,personnelSalaryHistory);
            personnelSalaryHistory.setPersonnelSalary(personnelSalary.getId());
            personnelSalaryHistory.setId(null);
            personnelSalaryHistory.setUpdator(user.getUserID());
            personnelSalaryHistory.setUpdateName(user.getUserName());
            personnelSalaryHistory.setUpdateDate(new Date());
            if(type==1){
                personnelSalaryHistory.setOperation("4");
                personnelSalary.setFactSalary(p.getSalary());
            }else if(type==2){
                personnelSalaryHistory.setOperation("5");
                personnelSalary.setIncomeTax(p.getSalary());
            }else if(type==3){
                personnelSalaryHistory.setOperation("6");
                personnelSalary.setInsurancePremium(p.getSalary());
            }else if(type==4){
                personnelSalaryHistory.setOperation("7");
                personnelSalary.setAccumulationFund(p.getSalary());
            }
            personnelSalaryHistory.setSubVersionNo(version);
            personnelSalaryHistoryDao.save(personnelSalaryHistory);

            personnelSalary.setAdjustDate(new Date());
            personnelSalary.setOperateTime(new Date());
            personnelSalary.setOperator(user.getUserID());
            personnelSalary.setUpdator(user.getUserID());
            personnelSalary.setUpdateName(user.getUserName());
            personnelSalary.setUpdateDate(new Date());
            personnelSalary.setOperation("3");
            personnelSalary.setSubVersionNo(personnelSalary.getVersionNo()+1);
            personnelSalaryDao.update(personnelSalary);
        }
        String hql="from PersonnelSalary where org ="+ user.getOid() +" and reportingPeriod='"+pSalary.getReportingPeriod()+"'";
        List<PersonnelSalary> list1 = personnelSalaryDao.getListByHQLWithNamedParams(hql,null);
        for(PersonnelSalary personnelSalary:list1)
            {
                personnelSalary.setVersionNo(personnelSalary.getVersionNo()+1);
                personnelSalaryDao.update(personnelSalary);
            }
        }
        return 1;
    }

    @Override
    public UpdateSalaryInfo updateMonthSalaryInfo(User user, String month, int type) {
        UpdateSalaryInfo updateSalaryInfo=new UpdateSalaryInfo();
        int x=0;
        String hqlSalary="from PersonnelSalary where org ="+ user.getOid() +" and reportingPeriod='"+month+"'";
        List<PersonnelSalary> list = personnelSalaryDao.getListByHQLWithNamedParams(hqlSalary,null);
        if(list.size()>0) {
                if(type==1&&list.get(0).getSalaryTime()!=null) {
                    updateSalaryInfo.setCreateDate(list.get(0).getSalaryTime());
                    updateSalaryInfo.setCreateName(list.get(0).getSalaryName());
                }if(type==2&list.get(0).getTaxTime()!=null) {
                    updateSalaryInfo.setCreateDate(list.get(0).getTaxTime());
                    updateSalaryInfo.setCreateName(list.get(0).getTaxName());
                }if(type==3&list.get(0).getPremiumTime()!=null) {
                    updateSalaryInfo.setCreateDate(list.get(0).getPremiumTime());
                    updateSalaryInfo.setCreateName(list.get(0).getPremiumName());
                }if(type==4&list.get(0).getFundTime()!=null){
                    updateSalaryInfo.setCreateDate(list.get(0).getFundTime());
                    updateSalaryInfo.setCreateName(list.get(0).getFundName());
                    }

        }
        int operation=type+3;
        String hql = "from PersonnelSalaryHistory where org =" +user.getOid() + " and reportingPeriod='" + month + "' and operation='"+operation+"' group by subVersionNo";
        List<PersonnelSalaryHistory> personnelSalaryHistoryList = personnelSalaryHistoryDao.getListByHQLWithNamedParams(hql, null);
        if(personnelSalaryHistoryList.size()>0)
        {
            updateSalaryInfo.setCreateDate(personnelSalaryHistoryList.get(personnelSalaryHistoryList.size() - 1).getUpdateDate());
            updateSalaryInfo.setCreateName(personnelSalaryHistoryList.get(personnelSalaryHistoryList.size() - 1).getUpdateName());
        }
        updateSalaryInfo.setUpdateNumber(personnelSalaryHistoryList.size());
        List<UpdateSalaryInfo> updateSalaryDetails=new ArrayList<>();
        for(PersonnelSalaryHistory personnelSalaryHistory:personnelSalaryHistoryList)
        {
            if(personnelSalaryHistory.getSubVersionNo()==0) {
                UpdateSalaryInfo detail=new UpdateSalaryInfo();
                detail.setUpdateNumber(personnelSalaryHistory.getSubVersionNo());
                if(type==1&&list.get(0).getSalaryTime()!=null) {
                    detail.setCreateDate(personnelSalaryHistory.getSalaryTime());
                    detail.setCreateName(personnelSalaryHistory.getSalaryName());
                }if(type==2&list.get(0).getTaxTime()!=null) {
                    detail.setCreateDate(personnelSalaryHistory.getTaxTime());
                    detail.setCreateName(personnelSalaryHistory.getTaxName());
                }if(type==3&list.get(0).getPremiumTime()!=null) {
                    detail.setCreateDate(personnelSalaryHistory.getPremiumTime());
                    detail.setCreateName(personnelSalaryHistory.getPremiumName());
                }if(type==4&list.get(0).getFundTime()!=null){
                    detail.setCreateDate(personnelSalaryHistory.getFundTime());
                    detail.setCreateName(personnelSalaryHistory.getFundName());
                }
                updateSalaryDetails.add(detail);
            }
            UpdateSalaryInfo detail=new UpdateSalaryInfo();
            detail.setUpdateNumber(personnelSalaryHistory.getSubVersionNo()+1);
            detail.setCreateDate(personnelSalaryHistory.getUpdateDate());
            detail.setCreateName(personnelSalaryHistory.getUpdateName());
            detail.setCreator(personnelSalaryHistory.getUpdator());
            updateSalaryDetails.add(detail);
        }
        updateSalaryInfo.setUpdateSalaryDetails(updateSalaryDetails);
        return updateSalaryInfo;
    }



    @Override
    public List<String> selectYearListByOrg(int org,String year) {
        List<String> list = new ArrayList<>();
        String sql = "select LEFT(reporting_period,4) AS `year`,sum(fact_salary) from `t_personnel_salary` where org="+org+" and LEFT(reporting_period,4)!='"+year+"' group by (LEFT(reporting_period,4))";
        List<Object[]> personnelSalarylist = personnelSalaryDao.getObjectListBySQL(sql);
        if (personnelSalarylist.size() > 0 && !personnelSalarylist.isEmpty()) {
            Integer yearNow = NewDateUtils.getYear(new Date());
            int num= 12;  //
            if (yearNow.toString().equals(year)){
                num = NewDateUtils.getMonth(new Date());
            }
            for (int i = 0; i <personnelSalarylist.size(); i++) {
                if (i<num) {
                    //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                    String s = JSON.toJSONString(personnelSalarylist.get(i));
                    JSONArray objects = JSONArray.parseArray(s);
                    String year1 = objects.getString(0);
                    list.add(year1);
                }else {
                    break;
                }
            }
        }
        return list;
    }
    @Override
    public SalaryAllUser selectSalaryAllUser(Integer org) {
        SalaryAllUser salaryAllUser=new SalaryAllUser();
        String sql = "SELECT `user`,ifnull(sum(fact_salary),0) FROM `t_personnel_salary` WHERE org="+org+" group by `user`";
        List<Object[]> personnelSalarylist = personnelSalaryDao.getObjectListBySQL(sql);
        List<SalaryByUser> userList=new ArrayList<>();
        if (personnelSalarylist.size() > 0 && !personnelSalarylist.isEmpty()) {
            for (int i = 0; i <personnelSalarylist.size(); i++) {
                SalaryByUser salaryByUser=new SalaryByUser();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(personnelSalarylist.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                Integer userId= Integer.valueOf(objects.getString(0));
                Double salary= Double.valueOf(objects.getString(1));
                User user=userDao.get(userId);
                if("agent".equals(user.getRoleCode()))
                {
                    salaryByUser.setUserName(user.getRoleName().substring(1, user.getRoleName().length()));
                }
                else
                    salaryByUser.setUserName(user.getUserName());
                salaryByUser.setPhone(user.getMobile());
                salaryByUser.setUserId(userId);
                salaryByUser.setSalary(salary);
                userList.add(salaryByUser);
            }
        }
        salaryAllUser.setUserList(userList);
        salaryAllUser.setUserSum(userList.size());
        salaryAllUser.setResignationSum(0);//离职员工不存在，目前不处理
        return salaryAllUser;
    }

    @Override
    public SalaryByUser selectSalaryByUser(Integer userId,String year) {
        SalaryByUser salaryByUser=new SalaryByUser();
        User user=userDao.get(userId);
        if("agent".equals(user.getRoleCode()))
        {
            salaryByUser.setUserName(user.getRoleName().substring(1, user.getRoleName().length()));
        }
        else
            salaryByUser.setUserName(user.getUserName());
        salaryByUser.setPhone(user.getMobile());
        salaryByUser.setUserId(userId);
        salaryByUser.setYear(year);
        List<SalaryByTime> salaryByTimeList=new ArrayList<>();
        String sql;
        List<Object[]> personnelSalarylist;
        if(year==null||"".equals(year)) {
            sql = "SELECT LEFT(reporting_period,4),IFNULL(sum(fact_salary),0) FROM `t_personnel_salary` WHERE `user`=" + userId + " group by  LEFT(reporting_period,4) order by LEFT(reporting_period,4) desc";
        }else {
            sql = "SELECT reporting_period,IFNULL(fact_salary,0) FROM `t_personnel_salary` WHERE `user`=" + userId + " and LEFT(reporting_period,4)='"+ year +"' order by reporting_period desc";
        }
        personnelSalarylist = personnelSalaryDao.getObjectListBySQL(sql);
            if (personnelSalarylist.size() > 0 && !personnelSalarylist.isEmpty()) {
                for (int i = 0; i < personnelSalarylist.size(); i++) {
                    SalaryByTime salaryByTime = new SalaryByTime();
                    //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                    String s = JSON.toJSONString(personnelSalarylist.get(i));
                    JSONArray objects = JSONArray.parseArray(s);
                    String time = objects.getString(0);
                    Double salary = Double.valueOf(objects.getString(1));
                    salaryByTime.setTime(time);
                    salaryByTime.setSalary(salary);
                    salaryByTimeList.add(salaryByTime);
                }
            }
            salaryByUser.setSalaryByTimeList(salaryByTimeList);
        return salaryByUser;
    }

    @Override
    public List<PersonnelSalary> selectOrgBySalary() {
        String hql="from PersonnelSalary group by org";
        List<PersonnelSalary> personnelSalaryList = personnelSalaryDao.getListByHQLWithNamedParams(hql, null);
        return personnelSalaryList;
    }

    @Override
    public void addPersonnelPayPeroid(User user){
        Integer yearMonth = NewDateUtils.getYearMonth(new Date());  //获取当前时间的年月
        Organization organization = orgDao.get(user.getOid());
        for (int i=1;i<5;i++){    //类型:1-工资,2-所得税,3-社保,4-公积金;
            PersonnelPayPeroid personnelPayPeroid = getPeriod(user.getOid(),null,(byte) i,2);
            if (personnelPayPeroid!=null){
                Integer peroid = Integer.parseInt(personnelPayPeroid.getPeriod());  //数据保存的最后一月
                while (yearMonth>peroid){  //保存的时间小于当前时间的，需要添加
                    Date bean = NewDateUtils.dateFromString(peroid.toString()+"01","yyyyMMdd");  //转化为某月的一号，才可以转化为日期
                    Integer newPeroid = NewDateUtils.getYearMonth(NewDateUtils.changeMonth(bean,1)); //获取新的年月【这里的月份+1】
                    PersonnelPayPeroid personnelPayPeroidNew = new PersonnelPayPeroid();
                    personnelPayPeroidNew.setOrg(user.getOid());
                    personnelPayPeroidNew.setPeriod(newPeroid.toString());
                    personnelPayPeroidNew.setType(personnelPayPeroid.getType());
                    personnelPayPeroidNew.setState(0);  //状态:0-未录入(默认),1-需要继续录入,2-已录完
                    personnelPayPeroidNew.setVersionNo(0);
                    personnelPayPeroidNew.setCreator(user.getUserID());
                    personnelPayPeroidNew.setCreateName(user.getUserName());
                    personnelPayPeroidNew.setCreateDate(new Date());
                    personnelPayPeroidDao.save(personnelPayPeroidNew);

                    peroid=newPeroid;
                }
            }else {
                Date begin = organization.getCreateDate()!=null?organization.getCreateDate():new Date();
                Date beginDate = NewDateUtils.getNewYearsDay(begin);  //起始年月取机构创建那年的1月份
                Integer peroid = NewDateUtils.getYearMonth(beginDate);  //起始年月取机构创建那年的1月份
                while (yearMonth>=peroid){  //yearMonth:当前的年月
                    PersonnelPayPeroid personnelPayPeroidNew = new PersonnelPayPeroid();
                    personnelPayPeroidNew.setOrg(user.getOid());
                    personnelPayPeroidNew.setPeriod(peroid.toString());
                    personnelPayPeroidNew.setType((byte) i);  //类型:1-工资,2-所得税,3-社保,4-公积金
                    personnelPayPeroidNew.setState(0);  //状态:0-未录入(默认),1-需要继续录入,2-已录完
                    personnelPayPeroidNew.setVersionNo(0);
                    personnelPayPeroidNew.setCreator(user.getUserID());
                    personnelPayPeroidNew.setCreateName(user.getUserName());
                    personnelPayPeroidNew.setCreateDate(new Date());
                    personnelPayPeroidDao.save(personnelPayPeroidNew);

                    beginDate = NewDateUtils.changeMonth(beginDate,1);  //获取新的年月【这里的月份+1】
                    peroid=NewDateUtils.getYearMonth(beginDate);  //转化为年月
                }
            }
        }
    }

    //type 类型:1-工资,2-所得税,3-社保,4-公积金      某年的     sort 1-按月份倒序
    public List<PersonnelPayPeroid> getPeroidsByPeriodYear(Integer oid,Integer yearDate,Integer type,Integer sort){
        Map<String,Object> map = new HashMap<>();
        String hql = " from PersonnelPayPeroid where org=:org";
        map.put("org",oid);
        if (yearDate!=null){
            Integer yearNew = NewDateUtils.getYear(new Date());  //现在年份
            String beginDate = yearDate+"01";  //开始日期
            String endDate = yearDate+"12";  //结束日期
            if (yearNew==yearDate){
                endDate = NewDateUtils.getYearMonth(new Date()).toString();
            }
            hql+=" and period>=:beginDate and period<=:endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);  //前四位年份一样的
        }
        if(sort!=null){
            if (sort==1){
                hql+=" order by period desc";
            }
        }

        List<PersonnelPayPeroid> personnelPayPeroids = personnelPayPeroidDao.getListByHQLWithNamedParams(hql,map);
        return personnelPayPeroids;
    }

    //type 类型:1-工资,2-所得税,3-社保,4-公积金     某月的
    public List<PersonnelPayPeroid> getPeroidsByPeriodMonth(Integer oid,String period,Byte type){
        Map<String,Object> map = new HashMap<>();
        String hql = " from PersonnelPayPeroid where org=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and period = :period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        List<PersonnelPayPeroid> personnelPayPeroids = personnelPayPeroidDao.getListByHQLWithNamedParams(hql,map);
        return personnelPayPeroids;
    }

    @Override    //typeSource 1-pc 2-手机端
    public List<PersonnelPayPeroid> getgetSalaryList(User user,Integer yearDate,Integer sort,Integer typeSource) {
        this.addPersonnelPayPeroid(user);  //添加到当前月份
        if (typeSource!=null&&1==typeSource) {
            List<PersonnelPayPeroid> personnelPayPeroids = getPeroidsByPeriodYear(user.getOid(), yearDate, null, sort);
            return personnelPayPeroids;
        }else {
            return null;
        }
    }

    public PersonnelPay getPersonnelPay(Integer oid,String period,Byte type){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PersonnelPay where org=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and period = :period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        hql+=" order by versionNo desc";
        PersonnelPay personnelPay = (PersonnelPay) personnelPayDao.getByHQLWithNamedParams(hql,map);
        return personnelPay;
    }

    @Override
    public Map<String, Object> manualEntry(User user,String userList, Date payTime, byte payWay, BigDecimal pay, byte type,String period,Integer accountId,String summary,Date periodDate) {
        Map<String,Object> map = new HashMap<>();
        Date createDate = new Date();
        FinanceAccount financeAccount = financeAccountDao.get(accountId); //获取账户id
        AccountPeriod accountPeriod = accountService.getAccountPeriodByMonth( financeAccount.getId(), new Date());  //月结

        //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
        //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
        BigDecimal me = new BigDecimal(pay.toString());
        BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
        BigDecimal jieguo = ba.subtract(me);

        if (jieguo.doubleValue() < 0) {
            map.put("status", 3);//余额不足
            map.put("content","该账户余额不足");
        } else if (0 == financeAccount.getAccountStatus()) {
            map.put("status", 2);//此账户已被冻结，不允许操作
            map.put("content","此账户已被冻结，不允许操作");
        } else {

            List<Integer> userQuit = new ArrayList<>(); //离职人员id
            net.sf.json.JSONArray jsonValues = net.sf.json.JSONArray.fromObject(userList, new JsonConfig());
            List users = (List) net.sf.json.JSONArray.toCollection(jsonValues);

            Integer versionNo = 1;
            PersonnelPay personnelPay1 = getPersonnelPay(user.getOid(),period,type);  //获取系统中最新的那个录入版本
            if (type==1&&personnelPay1!=null){  //只有职工工资可以多次进行录入
                versionNo = personnelPay1.getVersionNo()+1; //系统中最新的那个录入版本+1为录入的新版本
            }

            List<PersonnelPay> personnelPays = new ArrayList<>(); //更新里面的accountDetail用
            List<PersonnelPayHistory> personnelPayHistorys = new ArrayList<>(); //更新里面的accountDetail用
            Integer businessHistory = null;
            for (int i=0;i<users.size();i++){
                JSONObject userManual = JSONObject.fromObject(users.get(i));  //要录入的职工
                Integer userIdManual = userManual.getInt("userId");  //职工id
                PersonnelPay personnelPay = new PersonnelPay();
                personnelPay.setOrg(user.getOid());
                personnelPay.setUser(userIdManual);  //职工id
                personnelPay.setPeriod(period);  //所属月份
                personnelPay.setType(type);  //类型
                personnelPay.setPay(pay);  //实发总额
                personnelPay.setFactPay(new BigDecimal(userManual.getDouble("factPay")));  //实发金额
                personnelPay.setPayTime(payTime);
                personnelPay.setPayWay(payWay);
//                personnelPay.setAccountDetail(accountDetail.getId());
                personnelPay.setFinanceAccount(financeAccount.getId());
                personnelPay.setPayAccount(financeAccount.getAccount());
                personnelPay.setCreateDate(createDate);
                personnelPay.setCreateName(user.getUserName());
                personnelPay.setCreator(user.getUserID());
                personnelPay.setOperation("1");
                personnelPay.setVersionNo(versionNo);
                personnelPayDao.save(personnelPay);
                personnelPays.add(personnelPay);

                PersonnelPayHistory personnelPayHistory = this.addPayHistory(user,personnelPay,0,createDate);
                personnelPayHistorys.add(personnelPayHistory);
                if (businessHistory==null){
                    businessHistory = personnelPayHistory.getId();
                }

                String isduty = userManual.getString("isduty");  //1-在职 2-离职
                if (StringUtils.isNotEmpty(isduty)&&"2".equals(isduty)){
                    userQuit.add(userIdManual);
                }
            }
            this.updatePeroidState(user,userQuit,period,type);  //修改状况信息【需继续录入的均是离职的职工】

            if (accountPeriod == null) {
                try {
                    FinanceUtils.addPeroid(financeAccount.getId(), user, accountService);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());//再查一遍
            AccountPeriod ap = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结
            if (accountPeriod.getBuildState() != null) {
                if (accountPeriod.getBuildState().equals("已生成"))
                    map.put("status", 3);//已生成
            }

            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setDebit(pay);
            accountDetail.setCreateDate(createDate);
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setAccount(accountPeriod.getId().toString());
            accountDetail.setAuditDate(createDate);
            accountDetail.setBillAmount(pay);  //票面金额
            accountDetail.setMethod("1"); //0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
            if (2 == payWay) {
                accountDetail.setMethod("5");
            }
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(summary);//用途
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            if (financeAccount.getAccount() != null) {
                accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            } else {
                accountDetail.setAccountBank(financeAccount.getBankName());
            }
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setSource("6"); //数据来源: 6-工资管理(1.206工资之支付)
            accountDetail.setBillDate(payTime);  //票据日期
            accountDetail.setFactDate(payTime);  //实际(付款/支出)日期'
            accountDetail.setBusinessHistory(businessHistory);

            BigDecimal debit = accountPeriod.getDebit().add(accountDetail.getDebit());
            BigDecimal balance = accountPeriod.getBalance().subtract(accountDetail.getDebit());
            financeAccount.setDebit(financeAccount.getDebit().add(accountDetail.getDebit()));//计入账户支出
            financeAccount.setBalance(financeAccount.getBalance().subtract(accountDetail.getDebit()));//从余额减去支出
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriod.setDebit(debit);
            accountPeriod.setBalance(balance);

            debit = ap.getDebit().add(accountDetail.getDebit());
            balance = ap.getBalance().subtract(accountDetail.getDebit());
            ap.setDebit(debit);
            ap.setBalance(balance);
            accountService.updateAccountPeroid(accountPeriod);//月结
            accountService.updateAccountPeroid(ap);//日结
            accountService.updateFinanceAccount(financeAccount);//账户
            accountService.saveAccountDetail(accountDetail);//明细

            //1.207财务之税种--2022/07/27新增
            if (type!=1) {  //添加进入报税的   2-所得税,3-社保,4-公积金
                this.salaryToTax(user,type,periodDate,accountDetail,pay,period);
            }
            for (PersonnelPay pp:personnelPays) {
                if (pp.getAccountDetail()==null) {
                    pp.setAccountDetail(accountDetail.getId());
                    personnelPayDao.update(pp);
                }
            }
            for (PersonnelPayHistory pph:personnelPayHistorys) {
                pph.setAccountDetail(accountDetail.getId());
                personnelPayHistoryDao.update(pph);
            }

            map.put("status",1); //成功
            map.put("content","操作成功");
        }

        return map;
    }

    private void updatePeroidState(User user,List<Integer> userQuit,String period,Byte type){

        PersonnelPayPeroid personnelPayPeroid = getPeriod(user.getOid(),period,type,null); //获取当前类型的状况信息
        personnelPayPeroid.setState(2);  //状态:0-未录入(默认),1-需要继续录入,2-已录完
        personnelPayPeroid.setUpdateDate(new Date());
        personnelPayPeroid.setUpdateName(user.getUserName());
        personnelPayPeroid.setUpdator(user.getUserID());

        List<PersonnelPayPeroid> personnelPayPeroids = getPeroidsByPeriodMonth(user.getOid(),period,null);
        personnelPayPeroids.remove(personnelPayPeroid);
        for (PersonnelPayPeroid personnelPayPeroid1:personnelPayPeroids) {
            if (personnelPayPeroid1.getState()!=0){
                Integer groupType = null;
                if (personnelPayPeroid1.getType()==1){
                    groupType = 2;  //工资时，查询以user分组
                }
                List<PersonnelPay> personnelPays1 = getPersonnelPays(user.getOid(),personnelPayPeroid1.getType(),period,null,null,groupType,userQuit,1); //其他类型中是否有此次没有录入的离职人员
                if (personnelPays1.size()>0){
                    if (1==personnelPayPeroid.getType()) {    //只有工资的有这种情况
                        List<Integer> userQuit1 = new ArrayList<>();  //此次的添加没有，但是已添加的是否有同样的离职人员
                        for (PersonnelPay p : personnelPays1) {
                            userQuit1.add(p.getUser());
                        }
                        //判断是否有相同的离职职工
                        List<PersonnelPay> personnelPays2 = getPersonnelPays(user.getOid(), personnelPayPeroid.getType(), period, null, null, groupType, userQuit1, null);
                        if (personnelPays2.size() < userQuit.size()) {  //其他类型录入的离职人员少于现在录入的离职人员
                            personnelPayPeroid.setState(1);
                        }

                    }else {
                        personnelPayPeroid.setState(1);  //状态:0-未录入(默认),1-需要继续录入,2-已录完
                    }
                }

                List<PersonnelPay> personnelPays = new ArrayList<>();
                if (userQuit.size()>0) {
                    personnelPays = getPersonnelPays(user.getOid(), personnelPayPeroid1.getType(), period, null, null, groupType, userQuit, null);  //其他类型中是否与录入的离职人员一致
                    if (personnelPays.size()<userQuit.size()){  //其他类型录入的离职人员少于现在录入的离职人员
                        personnelPayPeroid1.setState(1);
                    }
                }
                personnelPayPeroidDao.update(personnelPayPeroid1);
            }
        }
        personnelPayPeroidDao.update(personnelPayPeroid);

    }

    @Override  //sort 排序 1-顺序 2-倒序
    public PersonnelPayPeroid getPeriod(Integer oid,String yearMonth,Byte type,Integer sort){
        //type 类型:1-工资,2-所得税,3-社保,4-公积金
        Map<String,Object> map = new HashMap<>();
        String hql = " from PersonnelPayPeroid where org=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(yearMonth)){
            hql+=" and period = :period";
            map.put("period",yearMonth);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);  //前四位年份一样的
        }
        if (sort!=null){
            if (2==sort) {
                hql += " order by period desc";
            }
        }
        PersonnelPayPeroid personnelPayPeroid = (PersonnelPayPeroid) personnelPayPeroidDao.getByHQLWithNamedParams(hql,map);
        return personnelPayPeroid;
    }

    @Override  //userQuit-录入的离职人员id  typeQuit 1-除userQuit里的外，其他离职人员  userId-要查询的职工
    public List<PersonnelPay> getPersonnelPays(Integer oid, Byte type, String period,Integer userId,Integer versionNo,Integer groupType,List<Integer> userQuit,Integer typeQuit) {
        Map<String,Object> map = new HashMap<>();
        String hql = " from PersonnelPay where org=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and period =:period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (userId!=null){
            hql+=" and user=:user";
            map.put("user",userId);
        }
        if (versionNo!=null){
            hql+=" and versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }
        if (userQuit!=null&&userQuit.size()>0){
            if (typeQuit!=null&&typeQuit==1) { //去除传的userId，查询其他的离职人员
                hql+=" and user not in (:userQuit)";
            }else {
                hql+=" and user in (:userQuit)";
            }
            map.put("userQuit", userQuit);
        }
        if (typeQuit!=null){
            hql+=" and user in (select userID from User where roleCode='staff' and oid=:oid and isDuty='2')";
            map.put("oid",oid);
        }
        if (groupType!=null){
            if (1==groupType){   //以版本号分组
                hql+=" group by versionNo";
            }else if (2==groupType){
                hql+=" group by user";  //以user分组
            }
        }
        List<PersonnelPay> personnelPays = personnelPayDao.getListByHQLWithNamedParams(hql,map);
        return personnelPays;
    }

    @Override   //groupType 1-按versionNo分组  2-1-按subVersionNo分组
    public List<PersonnelPayHistory> getRecords(Integer oid,Byte type,String period,Integer versionNo,Integer subVersionNo,Integer groupType){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PersonnelPayHistory where org=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and period = :period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (versionNo!=null){
            hql+=" and versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }
        if (subVersionNo!=null){
            hql+=" and subVersionNo=:subVersionNo";
            map.put("subVersionNo",subVersionNo);
        }
        if (groupType!=null) {
            if (1==groupType) {
                hql += " group by versionNo";
            }else if (2==groupType){
                hql += " group by subVersionNo";
            }
        }
        return personnelPayHistoryDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override    //detailType 判断accountDetail的(accountDetail无值，说明是只进行覆盖，没有冲账的操作) 1-排除accountDetail无值的
    public PersonnelPayHistory getPersonnelPayHistory(Integer oid,Integer payHistoryId,Byte type,String period,Integer versionNo,Integer payId,Integer detailType){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PersonnelPayHistory where org=:org";
        map.put("org",oid);
        if (payHistoryId!=null){
            hql+=" and id = :id";
            map.put("id",payHistoryId);
        }
        if (StringUtils.isNotEmpty(period)){
            hql+=" and period = :period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (versionNo!=null){
            hql+=" and versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }
        if (payId!=null){
            hql+=" and personnelPay=:personnelPay";
            map.put("personnelPay",payId);
        }
        if (detailType!=null){
            if (1==detailType){
                hql+=" and accountDetail is not null";
            }
        }
        hql+=" order by subVersionNo desc";
        return (PersonnelPayHistory) personnelPayHistoryDao.getByHQLWithNamedParams(hql,map);
    }

    private PersonnelPayHistory addPayHistory(User user,PersonnelPay personnelPay,Integer subVersionNo,Date createDate){
        PersonnelPayHistory personnelPayHistory = new PersonnelPayHistory();
        BeanUtils.copyPropertiesIgnoreNull(personnelPay,personnelPayHistory);
        personnelPayHistory.setId(null);
        personnelPayHistory.setPersonnelPay(personnelPay.getId());
        personnelPayHistory.setCreator(user.getUserID());
        personnelPayHistory.setCreateName(user.getUserName());
        personnelPayHistory.setCreateDate(createDate);
        personnelPayHistory.setUpdator(user.getUserID());
        personnelPayHistory.setUpdateName(user.getUserName());
        personnelPayHistory.setUpdateDate(createDate);
        personnelPayHistory.setSubVersionNo(subVersionNo);
        personnelPayHistory.setOperation("3"); //操作:1-增,2-删,3-改,4-调财务数据
        personnelPayHistoryDao.save(personnelPayHistory);
        return personnelPayHistory;
    }

    @Override
    public Map<String, Object> updateManual(User user, String userList, Date payTime, Integer payWay, BigDecimal pay, Integer type, String period, Integer accountId,Integer versionNo,String operation) {
        Map<String,Object> map = new HashMap<>();
        FinanceAccount financeAccountNew = financeAccountDao.get(accountId);  //新賬戶
        Byte typeByte = this.IntegerToByte(type,1);
        List<PersonnelPay> personnelPays = getPersonnelPays(user.getOid(),typeByte,period,null,versionNo,null,null,null);  //获取某月的发放信息
        Integer accountIdOld = personnelPays.get(0).getFinanceAccount();  //原来的账户id
        //取一条判断是否有历史数据[accountDetail不为空，只找入账的。accountDetail为空是修改直接覆盖的]
        PersonnelPayHistory personnelPayHistory = getPersonnelPayHistory(user.getOid(),null,typeByte,period,versionNo,personnelPays.get(0).getId(),1);
        AccountDetail accountDetailOld = accountDetailDao.get(personnelPayHistory.getAccountDetail());
        BigDecimal payOld = personnelPays.get(0).getPay();

        Integer businessHistory1 = personnelPayHistory.getId();  //原本的历史id
        Integer businessHistory2 = null;  //新的历史id

        Date updateDate = new Date();
        List<Integer> userQuit = new ArrayList<>();  //离职的人员

        List<PersonnelPay> personnelPaysManual = new ArrayList<>();  //离职职工新添加的userId
        net.sf.json.JSONArray jsonValues = net.sf.json.JSONArray.fromObject(userList, new JsonConfig());
        List users = (List) net.sf.json.JSONArray.toCollection(jsonValues);
        for (PersonnelPay p:personnelPays) {   //两遍的循环，修改或添加本表数据
            Integer having = 0;  //是否p.id 在下面的修改中 0-不在 1-在
            for (int i=0;i<users.size();i++){
                JSONObject userUpdate = JSONObject.fromObject(users.get(i));  //要录入的职工
                PersonnelPay personnelPay = new PersonnelPay();
                String payIdString = (String) userUpdate.getString("payId");
                Integer payId =StringUtils.isNotEmpty(payIdString)&&!payIdString.equals("null")?Integer.parseInt(payIdString):null; //职工工资id
                if (payId!=null&&payId.equals(p.getId())) {
                    personnelPay = personnelPayDao.get(payId);
                    personnelPay.setPay(pay);  //实发总额
                    personnelPay.setFactPay(new BigDecimal(userUpdate.getDouble("factPay")));  //实发金额
                    personnelPay.setPayTime(payTime);
                    personnelPay.setPayWay(this.IntegerToByte(payWay, 2));
                    personnelPay.setFinanceAccount(accountId);
                    personnelPay.setPayAccount(financeAccountNew.getAccount());
                    personnelPay.setUpdateDate(updateDate);
                    personnelPay.setUpdateName(user.getUserName());
                    personnelPay.setUpdator(user.getUserID());
                    personnelPay.setOperation(operation); //操作:1-增,2-删,3-改,4-调财务数据,5-修改发放信息,6-修改工资数,7-修改支出信息,8-修改职工项
                    personnelPayDao.update(personnelPay);

                    having = 1;
                    break;
                }else {
                    String userIdString = (String) userUpdate.getString("userId");
                    Integer userIdManual =StringUtils.isNotEmpty(userIdString)&&!userIdString.equals("null")?Integer.parseInt(userIdString):null; //职工id
                    if (userIdManual!=null) {
                        if (userQuit.size()==0||!userQuit.contains(userIdManual)) {  //qu chong
                            personnelPay.setOrg(user.getOid());
                            personnelPay.setUser(userIdManual);  //职工id
                            personnelPay.setPeriod(period);  //所属月份
                            personnelPay.setType(typeByte);  //类型
                            personnelPay.setPay(pay);  //实发总额
                            personnelPay.setFactPay(new BigDecimal(userUpdate.getDouble("factPay")));  //实发金额
                            personnelPay.setPayTime(payTime);
                            personnelPay.setPayWay(this.IntegerToByte(payWay, 2));
                            personnelPay.setFinanceAccount(financeAccountNew.getId());
                            personnelPay.setPayAccount(financeAccountNew.getAccount());
                            personnelPay.setCreateDate(updateDate);
                            personnelPay.setCreateName(user.getUserName());
                            personnelPay.setCreator(user.getUserID());
                            personnelPay.setUpdateDate(updateDate);
                            personnelPay.setUpdateName(user.getUserName());
                            personnelPay.setUpdator(user.getUserID());
                            personnelPay.setOperation(operation);
                            personnelPay.setVersionNo(p.getVersionNo());
                            personnelPayDao.save(personnelPay);

                            personnelPaysManual.add(personnelPay);
                            userQuit.add(userIdManual);  //修改中添加的都是离职的人员
                            break;
                        }
                    }
                }
            }

            if (0==having){  //没有修改的直接更新和加记录
                p.setPay(pay);
                p.setOperation(operation);
                p.setUpdateDate(updateDate);
                p.setUpdateName(user.getUserName());
                p.setUpdator(user.getUserID());
            }
        }

        if (personnelPaysManual.size()>0){
            personnelPays.addAll(personnelPaysManual);
        }

        //取最新的一条判断是否有历史数据[accountDetail为不为空均算，取最新的，用于subVersionNo值]
        PersonnelPayHistory personnelPayHistory1 = getPersonnelPayHistory(user.getOid(),null,typeByte,period,versionNo,personnelPays.get(0).getId(),null);
        Integer subVersionNo = personnelPayHistory1.getSubVersionNo()+1;
        List<PersonnelPayHistory> personnelPayHistories = new ArrayList<>();  //历史里面添加上新的accountDetail值
        for (PersonnelPay p:personnelPays) {  //添加历史记录
            PersonnelPayHistory personnelPayHistory2 = this.addPayHistory(user, p, subVersionNo, updateDate);  //添加歷史數據
            personnelPayHistories.add(personnelPayHistory2);
            if (businessHistory2 == null) {
                businessHistory2 = personnelPayHistory2.getId();
            }
        }

        this.updatePeroidState(user,userQuit,period,typeByte);  //修改状况信息【需继续录入的均是离职的职工】

        if (accountDetailOld!=null&&accountDetailOld.getBusinessHistory()==null){
            accountDetailOld.setBusinessHistory(businessHistory1);  //如果原来的里面没有就添加上
        }

        if (!accountId.equals(accountIdOld)||pay.compareTo(payOld)!=0){  //需要沖賬的
            FinanceAccount financeAccountOld = financeAccountDao.get(accountIdOld);  //修改前的银行账户

            AccountPeriod accountPeriod = new AccountPeriod();  //修改前账户的月结
            AccountPeriod accountPeriod1 = new AccountPeriod();  //修改后账户的月结
            AccountPeriod accountPeriodDay = new AccountPeriod();  //修改前账户的日结
            AccountPeriod accountPeriodDay1 = new AccountPeriod();  //修改后账户的日结
            AccountDetail accountDetail1 = new AccountDetail();//批准后，账务明细表里添加一条新数据(正确)
            AccountDetail accountDetail2 = new AccountDetail();//批准后，账务明细表里添加一条新数据(金额为负)
            Double newBanlance = 0.0; //账户修改前新余额
            Double newBanlance1 = 0.0 ; //账户修改后的账户新余额

            if (financeAccountNew==financeAccountOld){   //没有修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(financeAccountOld.getId(), new Date());  //账户的月结
                accountPeriodDay = accountService.getAccountPeriodByDay(financeAccountOld.getId(),new Date());  //账户的日结

                //支出修改 新余额=现在账户余额+修改前的支出-修改后的新支出
                newBanlance = financeAccountOld.getBalance().add(accountDetailOld.getDebit()).subtract(pay).doubleValue();
            }else {    //修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(financeAccountOld.getId(), new Date());  //修改前账户的月结
                accountPeriod1 = accountService.getAccountPeriodByMonth(financeAccountNew.getId(), new Date());  //修改后账户的账户月结
                accountPeriodDay = accountService.getAccountPeriodByDay(financeAccountOld .getId(), new Date());   //修改前账户的日结
                accountPeriodDay1 = accountService.getAccountPeriodByDay(financeAccountNew.getId(), new Date());  //修改后账户的日结

                //支出修改 新余额=现在账户余额+修改前的支出-修改后的新支出
                newBanlance = financeAccountOld.getBalance().add(accountDetailOld.getDebit()).doubleValue();  //修改前的账户的余额
                newBanlance1 = financeAccountNew.getBalance().subtract(pay).doubleValue(); //修改后的账户余额
            }

            //如果金额修改了或者是银行账户修改了
            if ((newBanlance >= 0 && newBanlance1>=0) || !financeAccountNew.getId().equals(financeAccountOld.getId())) {


                //批准后，账务明细表里添加一条新数据(正确)
                BeanUtils.copyPropertiesIgnoreNull(accountDetailOld,accountDetail1);
                accountDetail1.setId(null);
                accountDetail1.setPreviousId(accountDetailOld.getId());
                accountDetail1.setCreateDate(new Date());
                accountDetail1.setCreateName(user.getUserName());
                accountDetail1.setCreator(user.getUserID());
                if (accountPeriod1.getId() != null) {
                    accountDetail1.setAccount(accountPeriod1.getId().toString());
                } else {
                    accountDetail1.setAccount(accountPeriod.getId().toString());
                }
                accountDetail1.setAuditDate(new Date());
                accountDetail1.setDebit(pay);
                accountDetail1.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                accountDetail1.setFid(financeAccountNew.getId().toString());
                accountDetail1.setAccountId(financeAccountNew);//账户外键
                accountDetail1.setBillDate(payTime);  //票据日期
                accountDetail1.setFactDate(payTime);   //实际(付款/支出)日期')
                accountDetail1.setBalance(BigDecimal.valueOf(newBanlance1)); //余额
                accountDetail1.setVoucher(null);
                accountDetail1.setaIsAccount(null);
                accountDetail1.setAccountDetailHistories(null);
                accountDetail1.setModify(true);
                accountDetail1.setBusinessHistory(businessHistory2); //修改后的

                //批准后，账务明细表里添加一条新数据(金额为负)[冲账的businessHistory和老数据的一样]
                BeanUtils.copyPropertiesIgnoreNull(accountDetailOld,accountDetail2);
                accountDetail2.setId(null);
                accountDetail2.setPreviousId(accountDetailOld.getId());
                accountDetail2.setCreateDate(new Date());
                accountDetail2.setCreateName(user.getUserName());
                accountDetail2.setCreator(user.getUserID());
                accountDetail2.setAuditDate(new Date());
                accountDetail2.setDebit(accountDetailOld.getDebit().multiply(new BigDecimal(-1)));
                accountDetail2.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                accountDetail2.setBalance(BigDecimal.valueOf(newBanlance)); //余额
                accountDetail2.setModityStatus("2");
                accountDetail2.setVoucher(null);
                accountDetail2.setaIsAccount(null);
                accountDetail2.setAccountDetailHistories(null);
                accountDetail2.setModify(true);

                //账户是否改变
                if (accountPeriod1.getId() != null) {   //账户修改
                    accountDetail1.setBalance(BigDecimal.valueOf(newBanlance1)); //正确的新余额

                    //修改前账户的月结
                    accountPeriod.setDebit(accountPeriod.getDebit().subtract(accountDetailOld.getDebit()));
                    accountPeriod.setBalance(BigDecimal.valueOf(newBanlance));

                    //修改后账户的月结
                    accountPeriod1.setDebit(accountPeriod1.getDebit().add(pay));
                    accountPeriod1.setBalance(BigDecimal.valueOf(newBanlance1));

                    //修改前的日结
                    accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(accountDetailOld.getDebit()));
                    accountPeriodDay.setBalance(BigDecimal.valueOf(newBanlance));

                    //修改后的日结
                    accountPeriodDay1.setDebit(accountPeriodDay1.getDebit().add(pay));
                    accountPeriodDay1.setBalance(BigDecimal.valueOf(newBanlance1));

                    //修改前账户余额
                    financeAccountOld.setDebit(financeAccountOld.getDebit().subtract(accountDetailOld.getDebit()));
                    financeAccountOld.setBalance(BigDecimal.valueOf(newBanlance));

                    //修改后账户余额
                    financeAccountNew.setDebit(financeAccountNew.getDebit().add(pay));
                    financeAccountNew.setBalance(BigDecimal.valueOf(newBanlance1));

                } else {  //账户未修改
                    accountDetail1.setBalance(BigDecimal.valueOf(newBanlance)); //正确的新余额

                    //修改后的月结
                    accountPeriod.setDebit(accountPeriod.getDebit().subtract(accountDetailOld.getDebit()).add(pay));
                    accountPeriod.setBalance(BigDecimal.valueOf(newBanlance));

                    //修改后的日结
                    accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(accountDetailOld.getDebit()).add(pay));
                    accountPeriodDay.setBalance(BigDecimal.valueOf(newBanlance));

                    //修改后的账户余额
                    financeAccountOld.setDebit(financeAccountOld.getDebit().subtract(accountDetailOld.getDebit()).add(pay));
                    financeAccountOld.setBalance(BigDecimal.valueOf(newBanlance));
                }

            }
            accountDetailOld.setModify(true);
            accountDetailOld.setModityStatus("2");  //数据已进行冲账，不可以再修改

            if (newBanlance >= 0 && newBanlance1 >= 0) {
                if (accountPeriod1.getId() != null) {  //修改账户
                    accountPeroidDao.update(accountPeriod1);
                    accountPeroidDao.update(accountPeriodDay1);
                    financeAccountDao.update(financeAccountNew);
                    accountPeroidDao.update(accountPeriod);  //修改前账户额月结
                    accountPeroidDao.update(accountPeriodDay);  //修改前账户的日结
                    financeAccountDao.update(financeAccountOld);  //没有修改账户，但是账户金额发生变化
                    accountDetailDao.save(accountDetail2);//负数明细
                    accountDetailDao.save(accountDetail1);//正确的新明细
                    accountDetailDao.update(accountDetailOld);//老数据

                    for (PersonnelPayHistory pph:personnelPayHistories) {
                        pph.setAccountDetail(accountDetail1.getId());
                        personnelPayHistoryDao.update(pph);
                    }

                } else {  //未修改账户
                    if (accountDetail1.getDebit() != null || accountDetail1.getCredit() != null) {  //判断是否只修改了金额
                        accountPeroidDao.update(accountPeriod);  //修改前账户额月结
                        accountPeroidDao.update(accountPeriodDay);  //修改前账户的日结
                        financeAccountDao.update(financeAccountOld);  //没有修改账户，但是账户金额发生变化
                        accountDetailDao.save(accountDetail2);//负数明细
                        accountDetailDao.save(accountDetail1);//正确的新明细
                        accountDetailDao.update(accountDetailOld);//老数据

                        for (PersonnelPayHistory pph:personnelPayHistories) {
                            pph.setAccountDetail(accountDetail1.getId());
                            personnelPayHistoryDao.update(pph);
                        }
                    }
                }
                //1.207财务之税种--2022/07/27新增
                if (type!=1) {  //添加进入报税的   2-所得税,3-社保,4-公积金
                    this.salaryToTax(user,typeByte,NewDateUtils.dateFromString(period+"01","yyyyMMdd"),accountDetail1,pay,period);
                }
                map.put("state", 1);//操作成功
                map.put("content","操作成功");
            } else {
                map.put("state", 2);  //余额不足
                map.put("content","余额不足");
            }

        }else {
            accountDetailOld.setBillDate(payTime);  //票据日期
            accountDetailOld.setFactDate(payTime);   //实际(付款/支出)日期')
            accountDetailDao.update(accountDetailOld);

            if (type!=1) {  //添加进入报税的   2-所得税,3-社保,4-公积金
                AcctTaxDetailHistoryEntity acctTaxDetailHistoryEntity = taxDetailHistoryDao.get(accountDetailOld.getBusiness());
                acctTaxDetailHistoryEntity.setPayTime(payTime);
                taxDetailHistoryDao.update(acctTaxDetailHistoryEntity);
            }

            map.put("state", 1);//操作成功
            map.put("content","操作成功");
        }
        return map;
    }

    @Override   //groupType 1-按user分组
    public Map<String, Object> getPayByUserList(User user, String period, Byte type,Integer versionNo,Integer groupType) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select pp.id,pp.user,u.userName,u.mobile,u.departName,u.postName,sum(pp.factPay) from PersonnelPay pp, User u where pp.org=:org and pp.user=u.userID";
        map.put("org",user.getOid());
        if (StringUtils.isNotEmpty(period)){
            hql+=" and pp.period=:period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and pp.type=:type";
            map.put("type",type);
        }
        if (versionNo!=null){
            hql+=" and pp.versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }
        if (groupType!=null) {
            hql += " group by pp.user";
        }
        List<Object[]> userPay = personnelPayDao.getListByHQLWithNamedParams(hql,map);

        List<Map<String,Object>> mapList = new ArrayList<>();
        Integer userNum = 0;
        BigDecimal pay = new BigDecimal(0);  //实发总额
        for (Object[] object:userPay) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("payId",object[0]);  //id
            map1.put("userId",object[1]);  //职工id
            map1.put("userName",object[2]);  //名称
            map1.put("mobile",object[3]);  //手机号
            map1.put("departName",object[4]);  //部门名
            map1.put("postName",object[5]); //岗位
            map1.put("factPay",object[6]);  //实发金额
            mapList.add(map1);

            BigDecimal factPay = (BigDecimal) object[6];
            pay=pay.add(factPay);
            userNum++;
        }
        map.put("userNum",userNum);  //人数
        map.put("pay",pay);  //实发总额
        map.put("mapList",mapList);  //列表信息
        return map;
    }


    @Override   //Integer转化为byte     type-要转化的值   kind 1-类型 2-支出方式
    public Byte IntegerToByte(Integer type, Integer kind) {
        byte typeByte;
        if (1==kind){
            typeByte = SalaryService.type.salary.getIndex(); //1-工资
            if (2==type){
                typeByte = SalaryService.type.incomeTax.getIndex(); //2-所得税
            }else if (3==type){
                typeByte = SalaryService.type.socialSecurity.getIndex(); //3-社保
            }else if (4==type){
                typeByte = SalaryService.type.accumulationFund.getIndex(); //4-公积金
            }
        }else {
            typeByte = SalaryService.payWay.cash.getIndex(); //1-现金
            if (2==type){
                typeByte = SalaryService.payWay.bankTransfer.getIndex(); //2-银行转账
            }
        }
        return typeByte;
    }

    @Override  //groupType 1-按版本分组
    public Map<String, Object> getPersonnelPayMonth(Integer oid,Integer userId, Byte type, String period,Integer groupType) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select ";
        if (userId!=null){
            hql+="pp.id,";
        }else {
            hql+="count(pp.user),";
        }
        hql+="pp.payTime,pp.payWay,pp.pay,pp.factPay,pp.createDate,pp.createName,pp.versionNo,f.id,f.account,f.bankName,f.name,f.accountType,f.isPublic from PersonnelPay pp,FinanceAccount f where pp.org=:org and pp.financeAccount=f.id";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and pp.period=:period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and pp.type=:type";
            map.put("type",type);
        }
        if (userId!=null){
            hql+=" and pp.user=:user";
            map.put("user",userId);
        }
        if (groupType!=null) {
            hql += " group by pp.versionNo";
        }
        List<Object[]> personnelPayMonth = personnelPayDao.getListByHQLWithNamedParams(hql,map);
        List<Map<String,Object>> mapList = new ArrayList<>();
        BigDecimal pay = new BigDecimal(0);
        for (Object[] ob:personnelPayMonth) {
            Map<String,Object> map1 = new HashMap<>();
            if (userId!=null) {
                map1.put("payId", ob[0]);  //id
            }else {
                map1.put("userNum", ob[0]);  //人数
            }
            map1.put("payTime",ob[1]);  //发放时间
            map1.put("payWay",ob[2]);  //发放方式 1-现金 2-银行
            map1.put("pay",ob[3]);  //实发总金额【某次录入的总额,getPersonnelPayMonth接口用】
            map1.put("factPay",ob[4]);  //实发金额【某职工某月某次录入的金额，getPayByUserMonthList接口用】
            map1.put("createDate",ob[5]);
            map1.put("createName",ob[6]);
            map1.put("versionNo",ob[7]);
            map1.put("financeAccount",ob[8]);  //账户id
            map1.put("account",ob[9]); //账户
            map1.put("bankName",ob[10]);  //银行名称
            map1.put("name",ob[11]);  //账户名称
            map1.put("accountType",ob[12]);  //1-现金，2-银行
            map1.put("isPublic",ob[13]); //是否为对公帐号:1-是 0-否
            mapList.add(map1);
            BigDecimal factPay = (BigDecimal) ob[4];
            pay = pay.add(factPay);
        }
        map.put("personnelPayMonth",mapList);
        map.put("period",period);
        map.put("payUser",pay);  //【某职工某月某次录入的金额，getPayByUserMonthList接口用】实发总额
        if (userId!=null){
            User user = userDao.get(userId);
            map.put("user",user);
        }
        return map;
    }

    @Override   //只查询某条即可
    public PersonnelPay getPersonnelPay(Integer oid, String period, Byte typeByte, Integer versionNo) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PersonnelPay where org=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and period = :period";
            map.put("period",period);
        }
        if (typeByte!=null){
            hql+=" and type=:type";
            map.put("type",typeByte);
        }
        if (versionNo!=null){
            hql+=" and versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }

        return (PersonnelPay) personnelPayDao.getByHQLWithNamedParams(hql,map);
    }

    @Override   //groupType 1-按versionNo分组  2-1-按subVersionNo分组
    public Map<String,Object> getRecordDetails(Integer oid,Byte type,String period,Integer versionNo,Integer subVersionNo){
        Map<String,Object> map = new HashMap<>();
        String hql = "select pp.id,pp.user,u.userName,u.mobile,u.departName,u.postName,pp.factPay,pp.operation,pp.pay,pp.payTime,pp.payWay,pp.financeAccount,pp.createDate,pp.createName from PersonnelPayHistory pp, User u where pp.org=:org and pp.user=u.userID";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and pp.period = :period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and pp.type=:type";
            map.put("type",type);
        }
        if (versionNo!=null){
            hql+=" and pp.versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }
        if (subVersionNo!=null){
            hql+=" and pp.subVersionNo=:subVersionNo";
            map.put("subVersionNo",subVersionNo);
        }
        List<Object[]> userPay = personnelPayHistoryDao.getListByHQLWithNamedParams(hql,map);

        map.clear();
        List<Map<String,Object>> mapList = new ArrayList<>();
        BigDecimal pay = null;  //实发总额
        Date payTime = null;  //发放日期
        Byte payWay = null;  //发放方式:1-现金,2-转帐
        Integer financeAccountId = null; //账户id
        String operation = null;
        Date createDate = null;  //修改时间(和update的是一样的)
        String createName = null;  //修改人(和update的是一样的)
        for (Object[] object:userPay) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("payHistoryId",object[0]);  //历史id
            map1.put("userId",object[1]);  //职工id
            map1.put("userName",object[2]);  //名称
            map1.put("mobile",object[3]);  //手机号
            map1.put("departName",object[4]);  //部门名
            map1.put("postName",object[5]); //岗位
            map1.put("factPay",object[6]);  //实发金额
            mapList.add(map1);

            operation = (String) object[7]; //操作:1-增,2-删,3-改,4-调财务数据,5-修改发放信息,6-修改工资数,7-修改支出信息,8-修改职工项
            pay = (BigDecimal) object[8];
            payTime = (Date) object[9];
            payWay = (Byte) object[10];
            financeAccountId = (Integer) object[11];
            createDate = (Date) object[12];
            createName = (String) object[13];
        }
        FinanceAccount financeAccount = new FinanceAccount();
        if (financeAccountId!=null){
            financeAccount = financeAccountDao.get(financeAccountId);
        }
        map.put("type",type);  //类型
        map.put("pay",pay);  //实发总额
        map.put("payTime",payTime);  //发放日期
        map.put("payWay",payWay);  //发放方式:1-现金,2-转帐
        map.put("operation",operation);  //操作:1-增,2-删,3-改,4-调财务数据,5-修改发放信息,6-修改工资数,7-修改支出信息,8-修改职工项
        map.put("mapList",mapList);  //列表信息
        map.put("financeAccount",financeAccount);  //账户信息
        map.put("createDate",createDate);
        map.put("createName",createName);
        if (mapList.size()>0){
            map.put("userNum",mapList.size());  //发放人数
        }else {
            map.put("userNum",0);  //发放人数
        }

        PersonnelPay personnelPay = getPersonnelPay(oid,period,type,versionNo);
        if (personnelPay!=null){
            map.put("createDate0",personnelPay.getCreateDate());  //创建时间
            map.put("createName0",personnelPay.getCreateName());   //创建人
        }else {
            map.put("createDate0",null); //创建时间
            map.put("createName0",null); //创建人
        }
        return map;
    }

    @Override   //groupType 1-按user分组
    public Map<String, Object> getPayHistoryBySubversionNo(User user,String period,Integer versionNo,Integer subVersionNo,Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select pp.id,pp.user,u.userName,u.mobile,u.departName,u.postName,pp.factPay from PersonnelPayHistory pp, User u where pp.org=:org and pp.user=u.userID";
        map.put("org",user.getOid());
        if (StringUtils.isNotEmpty(period)){
            hql+=" and pp.period = :period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and pp.type=:type";
            map.put("type",type);
        }
        if (versionNo!=null){
            hql+=" and pp.versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }
        if (subVersionNo!=null){
            hql+=" and pp.subVersionNo=:subVersionNo";
            map.put("subVersionNo",subVersionNo);
        }
        List<Object[]> userPay = personnelPayDao.getListByHQLWithNamedParams(hql,map);

        List<Map<String,Object>> mapList = new ArrayList<>();
        Integer userNum = 0;
        BigDecimal pay = new BigDecimal(0);  //实发总额
        for (Object[] object:userPay) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("payId",object[0]);  //id
            map1.put("userId",object[1]);  //职工id
            map1.put("userName",object[2]);  //名称
            map1.put("mobile",object[3]);  //手机号
            map1.put("departName",object[4]);  //部门名
            map1.put("postName",object[5]); //岗位
            map1.put("factPay",object[6]);  //实发金额
            mapList.add(map1);

            BigDecimal factPay = (BigDecimal) object[6];
            pay=pay.add(factPay);
            userNum++;
        }
        map.put("userNum",userNum);  //人数
        map.put("pay",pay);  //实发总额
        map.put("mapList",mapList);  //列表信息
        return map;
    }

    //工资管理中的所得税、社保、公积金进入报税的内容
    private void salaryToTax(User user,byte type,Date periodDate,AccountDetail accountDetail,BigDecimal pay,String period) {
        Integer taxId = acctReportTaxService.getTaxId(type,user.getOid());  //获取税种id
        //获取报表id、申领记录的起止时间,可能为空
        JsonResult result = acctReportTaxService.getApplyTaxList(user,taxId,NewDateUtils.today(periodDate),NewDateUtils.today(NewDateUtils.getLastTimeOfMonth(periodDate)));
        String data = (String) result.getData();
        if (StringUtils.isNotEmpty(data)) {
            List dataList = (List) Arrays.asList(data);
            BSBean dateBean = (BSBean) dataList.get(0);
            if (dateBean.getId()!=null) {
                accountDetail.setBusinessKey(taxId);  //税种id
                Integer businessPeriodBegin = Integer.parseInt(NewDateUtils.dateToString(periodDate, "yyyyMMdd"));  //所属月份的1号
                Integer businessPeriodEnd = Integer.parseInt(NewDateUtils.dateToString(NewDateUtils.getLastTimeOfMonth(periodDate), "yyyyMMdd"));  //所属月份的最后一天
                accountDetail.setBusinessPeriodBegin(businessPeriodBegin);  //所属日期开始时间
                accountDetail.setBusinessPeriodEnd(businessPeriodEnd);  //所属日期结束时间

                String startDate = dateBean.getStartDate();  //需申报的起日期
                String endDate = dateBean.getEndDate();   //需申报的止日期
                Integer taxPeriodIdInsert = dateBean.getId();  //申报记录id
                Integer acctTaxDetailId = dataService.setTaxFactAmount(user, taxId,dateBean.getId(), pay, period, accountDetail.getFactDate(), accountDetail.getId(), businessPeriodBegin, businessPeriodEnd, startDate, endDate,taxPeriodIdInsert,null);
                accountDetail.setBusiness(acctTaxDetailId);//报表详情id
                accountDetailDao.update(accountDetail);
            }
        }
    }

    @Override
    public Map<String, Object> getSalaryList(Integer org, String beginDate, String endDate, Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "SELECT pp.period,p.pay,p.userNum FROM t_personnel_pay_peroid pp LEFT JOIN (SELECT period, pay,COUNT(`user`) AS userNum FROM t_personnel_pay WHERE type="+type+" AND org="+org+" AND period BETWEEN "+beginDate+" AND "+endDate+" GROUP BY period) p ON pp.period=p.period WHERE pp.type="+type+" AND pp.org="+org+" AND pp.period BETWEEN "+beginDate+" AND "+endDate+" GROUP BY pp.period ";
        List<Object[]> objects = personnelPayPeroidDao.getObjectListBySQL(hql);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("period",ob[0]); //年月
            map1.put("pay",ob[1]);  //金额
            map1.put("userNum",ob[2]);  //职工人数
            mapList.add(map1);
        }
        map.clear();
        map.put("mapList",mapList);
        return map;
    }

    @Override
    public Map<String, Object> getSalaryByUser(Integer org, Byte type,Integer isDuty) {
        Map<String,Object> map = new HashMap<>();
        String hql = "SELECT u.userID,u.userName,u.mobile,SUM(p.fact_pay) FROM t_sys_user u LEFT JOIN t_personnel_pay p ON u.userID=p.`user` AND p.type="+type+" WHERE u.role_code='staff'";
        if (1==isDuty){
            hql+=" AND u.isDuty IN(1,9) ";  //在职
        }else if (2==isDuty){
            hql+=" AND u.isDuty=2 "; //离职
        }
        hql+=" AND u.oid="+org+" GROUP BY u.userID ";
        List<Object[]> objects = personnelPayPeroidDao.getObjectListBySQL(hql);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("userID",ob[0]); //userid
            map1.put("userName",ob[1]);  //名称
            map1.put("mobile",ob[2]);  //手机号
            map1.put("pay",ob[3]);  //金额
            mapList.add(map1);
        }
        map.clear();
        map.put("mapList",mapList);
        map.put("userNum",mapList.size());
        return map;
    }

    @Override
    public Map<String, Object> getSalaryByYear(Integer org,Integer userId, Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "SELECT SUBSTRING(period,1,4) AS yearDate,SUM(fact_pay) AS pay ,COUNT(`user`) AS userNum FROM t_personnel_pay WHERE org="+org+" AND type="+type+"";
        if (userId!=null){
            hql+=" and `user`="+userId+"";
        }
        hql+=" GROUP BY SUBSTRING(period,1,4)";
        List<Object[]> objects = personnelPayPeroidDao.getObjectListBySQL(hql);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("yearDate",ob[0]); //年
            map1.put("pay",ob[1]);  //金额
            map1.put("userNum",ob[2]);  //人数
            BigDecimal pay = (BigDecimal) ob[1];
            BigInteger userNum = (BigInteger) ob[2];
            if (userId==null && (userNum!=null&&!userNum.equals(BigInteger.ZERO))){   //
                BigDecimal capitaPay = pay.divide(new BigDecimal(userNum),2,BigDecimal.ROUND_HALF_UP);
                map1.put("capitaPay",capitaPay);  //人均
            }
            mapList.add(map1);
        }
        map.clear();
        map.put("mapList",mapList);
        if (userId!=null) {
            User user = userDao.get(userId);
            map.put("userName",user.getUserName());
            map.put("phone",user.getMobile());
        }
        return map;
    }

    @Override
    public Map<String, Object> getSalaryByUserYear(Integer org, Integer userId, Integer yearDate, Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "SELECT period,SUM(fact_pay) AS pay FROM t_personnel_pay WHERE org="+org+" AND type="+type+"";
        if (userId!=null){
            hql+=" and `user`="+userId+"";
        }
        if (yearDate!=null){
            Integer yearNew = NewDateUtils.getYear(new Date());  //现在年份
            String beginDate = yearDate+"01";  //开始日期,某年的1月份
            String endDate = yearDate+"12";  //结束日期,某年的12月份
            if (yearNew==yearDate){
                endDate = NewDateUtils.getYearMonth(new Date()).toString();
            }
            hql+=" and period between "+beginDate+" and "+endDate+"";
        }
        hql+=" GROUP BY period";
        List<Object[]> objects = personnelPayPeroidDao.getObjectListBySQL(hql);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("period",ob[0]); //年月
            map1.put("pay",ob[1]);  //金额
            mapList.add(map1);
        }
        map.clear();
        map.put("mapList",mapList);
        map.put("yearDate",yearDate);
        if (userId!=null) {
            User user = userDao.get(userId);
            map.put("userName",user.getUserName());
            map.put("phone",user.getMobile());
        }
        return map;
    }

    @Override   //groupType 1-按versionNo分组  2-1-按subVersionNo分组
    public Map<String,Object> getRecordDetailList(Integer oid,Byte type,String period,Integer versionNo,Integer subVersionNo){
        Map<String,Object> map = new HashMap<>();
        String hql = "select pp.id,pp.user,u.userName,u.mobile,pp.factPay from PersonnelPayHistory pp, User u where pp.org=:org and pp.user=u.userID";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(period)){
            hql+=" and pp.period = :period";
            map.put("period",period);
        }
        if (type!=null){
            hql+=" and pp.type=:type";
            map.put("type",type);
        }
        if (versionNo!=null){
            hql+=" and pp.versionNo=:versionNo";
            map.put("versionNo",versionNo);
        }
        if (subVersionNo!=null){
            hql+=" and pp.subVersionNo=:subVersionNo";
            map.put("subVersionNo",subVersionNo);
        }
        List<Object[]> userPay = personnelPayHistoryDao.getListByHQLWithNamedParams(hql,map);

        map.clear();
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] object:userPay) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("payHistoryId",object[0]);  //历史id
            map1.put("userId",object[1]);  //职工id
            map1.put("userName",object[2]);  //名称
            map1.put("mobile",object[3]);  //手机号
            map1.put("factPay",object[4]);  //实发金额
            mapList.add(map1);
        }
        map.put("mapList",mapList);  //列表信息
        return map;
    }
}
