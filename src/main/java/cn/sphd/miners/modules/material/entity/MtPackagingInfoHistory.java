package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "t_mt_packaging_info_history")
public class MtPackagingInfoHistory {
    private Long id;
    private Integer org;
    private Long packaging;
    private Integer material;
    private Integer orders;
    private Integer supplierMaterial;
    private int supplier;
    private Integer packagingCount;
    private Integer materialCount;
    private Integer layersCount;
    private String manner;
    private BigDecimal netWeight;
    private Integer netUnitId;
    private String netUnit;
    private BigDecimal grossWeight;
    private Integer grossUnitId;
    private String grossUnit;
    private BigDecimal totalWeight;
    private Integer totalUnitId;
    private String totalUnit;
    private BigDecimal outerLength;
    private Integer lengthUnitId;
    private String lengthUnit;
    private BigDecimal outerWidth;
    private Integer widthUnitId;
    private String widthUnit;
    private BigDecimal outerHeight;
    private Integer heightUnitId;
    private String heightUnit;
    private Integer outerShape;
    private Integer enabled;
    private Date enabledTime;
    private Date effectTime;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private Integer operation;
    private Integer previousId;
    private Integer versionNo;


    private List<MtPackagingStructureHistory> structureList;
    private String unit;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "packaging")
    public Long getPackaging() {
        return packaging;
    }

    public void setPackaging(Long packaging) {
        this.packaging = packaging;
    }

    @Basic
    @Column(name = "material")
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "supplier_material")
    public Integer getSupplierMaterial() {
        return supplierMaterial;
    }

    public void setSupplierMaterial(Integer supplierMaterial) {
        this.supplierMaterial = supplierMaterial;
    }

    @Basic
    @Column(name = "supplier")
    public int getSupplier() {
        return supplier;
    }

    public void setSupplier(int supplier) {
        this.supplier = supplier;
    }

    @Basic
    @Column(name = "packaging_count")
    public Integer getPackagingCount() {
        return packagingCount;
    }

    public void setPackagingCount(Integer packagingCount) {
        this.packagingCount = packagingCount;
    }

    @Basic
    @Column(name = "material_count")
    public Integer getMaterialCount() {
        return materialCount;
    }

    public void setMaterialCount(Integer materialCount) {
        this.materialCount = materialCount;
    }

    @Basic
    @Column(name = "layers_count")
    public Integer getLayersCount() {
        return layersCount;
    }

    public void setLayersCount(Integer layersCount) {
        this.layersCount = layersCount;
    }

    @Basic
    @Column(name = "manner")
    public String getManner() {
        return manner;
    }

    public void setManner(String manner) {
        this.manner = manner;
    }

    @Basic
    @Column(name = "net_weight")
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    @Basic
    @Column(name = "net_unit_id")
    public Integer getNetUnitId() {
        return netUnitId;
    }

    public void setNetUnitId(Integer netUnitId) {
        this.netUnitId = netUnitId;
    }

    @Basic
    @Column(name = "net_unit")
    public String getNetUnit() {
        return netUnit;
    }

    public void setNetUnit(String netUnit) {
        this.netUnit = netUnit;
    }

    @Basic
    @Column(name = "gross_weight")
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    @Basic
    @Column(name = "gross_unit_id")
    public Integer getGrossUnitId() {
        return grossUnitId;
    }

    public void setGrossUnitId(Integer grossUnitId) {
        this.grossUnitId = grossUnitId;
    }

    @Basic
    @Column(name = "gross_unit")
    public String getGrossUnit() {
        return grossUnit;
    }

    public void setGrossUnit(String grossUnit) {
        this.grossUnit = grossUnit;
    }

    @Basic
    @Column(name = "total_weight")
    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    @Basic
    @Column(name = "total_unit_id")
    public Integer getTotalUnitId() {
        return totalUnitId;
    }

    public void setTotalUnitId(Integer totalUnitId) {
        this.totalUnitId = totalUnitId;
    }

    @Basic
    @Column(name = "total_unit")
    public String getTotalUnit() {
        return totalUnit;
    }

    public void setTotalUnit(String totalUnit) {
        this.totalUnit = totalUnit;
    }

    @Basic
    @Column(name = "outer_length")
    public BigDecimal getOuterLength() {
        return outerLength;
    }

    public void setOuterLength(BigDecimal outerLength) {
        this.outerLength = outerLength;
    }

    @Basic
    @Column(name = "length_unit_id")
    public Integer getLengthUnitId() {
        return lengthUnitId;
    }

    public void setLengthUnitId(Integer lengthUnitId) {
        this.lengthUnitId = lengthUnitId;
    }

    @Basic
    @Column(name = "length_unit")
    public String getLengthUnit() {
        return lengthUnit;
    }

    public void setLengthUnit(String lengthUnit) {
        this.lengthUnit = lengthUnit;
    }

    @Basic
    @Column(name = "outer_width")
    public BigDecimal getOuterWidth() {
        return outerWidth;
    }

    public void setOuterWidth(BigDecimal outerWidth) {
        this.outerWidth = outerWidth;
    }

    @Basic
    @Column(name = "width_unit_id")
    public Integer getWidthUnitId() {
        return widthUnitId;
    }

    public void setWidthUnitId(Integer widthUnitId) {
        this.widthUnitId = widthUnitId;
    }

    @Basic
    @Column(name = "width_unit")
    public String getWidthUnit() {
        return widthUnit;
    }

    public void setWidthUnit(String widthUnit) {
        this.widthUnit = widthUnit;
    }

    @Basic
    @Column(name = "outer_height")
    public BigDecimal getOuterHeight() {
        return outerHeight;
    }

    public void setOuterHeight(BigDecimal outerHeight) {
        this.outerHeight = outerHeight;
    }

    @Basic
    @Column(name = "height_unit_id")
    public Integer getHeightUnitId() {
        return heightUnitId;
    }

    public void setHeightUnitId(Integer heightUnitId) {
        this.heightUnitId = heightUnitId;
    }

    @Basic
    @Column(name = "height_unit")
    public String getHeightUnit() {
        return heightUnit;
    }

    public void setHeightUnit(String heightUnit) {
        this.heightUnit = heightUnit;
    }

    @Basic
    @Column(name = "outer_shape")
    public Integer getOuterShape() {
        return outerShape;
    }

    public void setOuterShape(Integer outerShape) {
        this.outerShape = outerShape;
    }

    @Basic
    @Column(name = "enabled")
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time")
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "effect_time")
    public Date getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Transient
    public List<MtPackagingStructureHistory> getStructureList() {
        return structureList;
    }

    public void setStructureList(List<MtPackagingStructureHistory> structureList) {
        this.structureList = structureList;
    }

    @Transient
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MtPackagingInfoHistory that = (MtPackagingInfoHistory) o;
        return id == that.id && supplier == that.supplier && Objects.equals(org, that.org) && Objects.equals(packaging, that.packaging) && Objects.equals(material, that.material) && Objects.equals(orders, that.orders) && Objects.equals(supplierMaterial, that.supplierMaterial) && Objects.equals(packagingCount, that.packagingCount) && Objects.equals(materialCount, that.materialCount) && Objects.equals(layersCount, that.layersCount) && Objects.equals(manner, that.manner) && Objects.equals(netWeight, that.netWeight) && Objects.equals(netUnitId, that.netUnitId) && Objects.equals(netUnit, that.netUnit) && Objects.equals(grossWeight, that.grossWeight) && Objects.equals(grossUnitId, that.grossUnitId) && Objects.equals(grossUnit, that.grossUnit) && Objects.equals(totalWeight, that.totalWeight) && Objects.equals(totalUnitId, that.totalUnitId) && Objects.equals(totalUnit, that.totalUnit) && Objects.equals(outerLength, that.outerLength) && Objects.equals(lengthUnitId, that.lengthUnitId) && Objects.equals(lengthUnit, that.lengthUnit) && Objects.equals(outerWidth, that.outerWidth) && Objects.equals(widthUnitId, that.widthUnitId) && Objects.equals(widthUnit, that.widthUnit) && Objects.equals(outerHeight, that.outerHeight) && Objects.equals(heightUnitId, that.heightUnitId) && Objects.equals(heightUnit, that.heightUnit) && Objects.equals(outerShape, that.outerShape) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(effectTime, that.effectTime) && Objects.equals(memo, that.memo) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, packaging, material, orders, supplierMaterial, supplier, packagingCount, materialCount, layersCount, manner, netWeight, netUnitId, netUnit, grossWeight, grossUnitId, grossUnit, totalWeight, totalUnitId, totalUnit, outerLength, lengthUnitId, lengthUnit, outerWidth, widthUnitId, widthUnit, outerHeight, heightUnitId, heightUnit, outerShape, enabled, enabledTime, effectTime, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
