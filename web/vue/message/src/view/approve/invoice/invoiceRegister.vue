<template>
  <div id="invoiceRegister" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="开票登记" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="tabs">
      <div class="tabs-bar">
        <router-link :to="{name: 'invoiceRegister1'}" tag="div">
          <TY_TabBar label="已批准待开票" :msgCount="applyList3.length" :bool="$store.getters.getInvoiceFinNav === 1">
          </TY_TabBar>
        </router-link>
        <router-link :to="{name: 'invoiceRegister2'}" tag="div">
          <TY_TabBar label="已开票未登记" :bool="$store.getters.getInvoiceFinNav === 2">
          </TY_TabBar>
        </router-link>
      </div>
      <div class="tabs-content">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>
<style>
  #invoiceRegister .tabs-tab{
    width:50%;
  }
  #invoiceRegister .info {
    display: inline-block;
    width: 49%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size:1rem;
  }

</style>
<script>
import { formatDate, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceRegister',
  filters: {
    formatDate,
    numberFilter
  },
  data () {
    return {
      list: this.$store.getters.getInvoiceList,
      applyList2: [], // 待审批
      applyList3: [], // 待开票
      list3: [], // 待开票
      applyList4: [], // 暂缓开票
      applyList5: [], // 未登记
      loading: true,
      listeners: []
    }
  },
  created () {
    let _this = this
    this.$store.dispatch('setNewInvoiceList')
    _this.listeners = [
      // 登记
      _this.sphdSocket.subscribe('signFor', _this.signForCallback, null, 'user'),
      // 签收
      _this.sphdSocket.subscribe('saleRegistration', _this.saleRegistrationCallback, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    setLisenter: function () {
      let _this = this
      _this.loading = false
      console.log(_this.list)
      if (_this.list && _this.list.applyList4) {
        _this.applyList2 = _this.list.applyList2
        _this.applyList3 = _this.list.applyList3
        _this.list3 = _this.list.list3
        _this.applyList4 = _this.list.applyList4
        _this.applyList5 = _this.list.applyList5
      }
    },
    search: function () {
      this.$router.push({
        name: `invoiceEnd`
      })
    },
    signForCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('签收回调', res)
      this.$store.dispatch('setNewInvoiceList')
    },
    saleRegistrationCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('送达回调', res)
      this.$store.dispatch('setNewInvoiceList')
    }
  },
  watch: {
    '$store.state.invoiceList': {
      deep: true,
      immediate: true,
      handler (invoiceList) {
        console.log('0 重获列表：', invoiceList)
        this.list = invoiceList
        if (this.list && this.list.applyList2) {
          this.setLisenter()
        }
      }
    }
  }
}
</script>
