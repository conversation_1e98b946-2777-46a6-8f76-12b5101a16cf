<template>
  <div class="home">
    <PageHeader showBack="true" title="登录"><span class="fa fa-close" @click="close"></span></PageHeader>
    <div class="contentHome">
      <div v-if="twoArea ===1">
        <div class="line30">
          您当前有两个领地，请选择您想要的领地！ <br>
          为了数据安全，继续登录需回答以下安全问题：
        </div>
        <div>
          <div class="radioC">
            <el-radio v-model="radio1" label="1"> 使用被冻结的原领地 </el-radio>
            <br>
            <br>
            <el-radio v-model="radio1" label="2"> 使用新领地 </el-radio>
          </div>
          <div class="ty-center" style="padding: 30px 0;">
            <span class="ty-btn ty-btn-green ty-circle-3 ty-btn-big" @click="okBtn">确定</span>
          </div>
        </div>
      </div>
      <div v-if="twoArea === 2" class="endTip">
        <div>您已选定“被冻结的原领地”，账号的密码为您之前被冻结领地的密码。</div>
        <div>请关闭本页面！</div>

      </div>
    </div>

  </div>
</template>
<script>
import { dupAcc, selectOldAccByActiveCode, selectNewAccByActiveCode,  } from "@/api/actAcc";
import PageHeader from '@/components/PageHeader.vue'

export default {
  data() {
    return {
      radio1:'',
      twoArea: 0,
    }
  },
  mounted() {
    this.getSta()
  },
  components: {
    PageHeader
  },
  methods: {
    getSta(){
      dupAcc().then(res1=>{
        let res = res1.data
        let data = res.data
        if(data){
          this.twoArea = 1

        }else{
          this.twoArea = 2
        }
      }).catch(err=>{
        console.log('err=', err)
      })

    },
    okBtn(){
      if(this.radio1 == 1){ // 原领地
        selectOldAccByActiveCode().then(res1=>{
          let res = res1.data
          let data = res.data
          let success = res.success
          if(success == 1){
            this.twoArea = 2
          }else{
            this.$message.error(data)
          }
        }).catch(err=>{
          console.log('err=', err)
        })

      } else if(this.radio1 == 2){ // 新领地

      }else {
        this.$message.error('请选择使用的领地！')
      }

    },
    close(){
      window.close()
    }
  },
}
</script>

<style lang="scss" scoped>
.home{
  .contentHome{
    padding:20px 20px 55px 20px;
    .line30{ line-height: 30px; font-size: 15px; }
    .radioC{ line-height: 20px; margin-top: 40px; }
  }
  .endTip{
    width: 70%; margin:100px auto;
  }

}
</style>
