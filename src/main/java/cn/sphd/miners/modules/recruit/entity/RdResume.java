package cn.sphd.miners.modules.recruit.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 招聘简历表
 * <AUTHOR>
 * @date 20230506
 **/
@Entity
@Table(name = "t_rd_resume")
public class RdResume {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    @Column
    private Integer org;//机构ID

    @Column(name = "org_name",length = 100)
    private String orgName;//机构ID

    @Column(name = "notice_post",nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticePostId;//招聘岗位ID

    @Column(name = "notice")
    private Long noticeId;//启示D

    @Column
    private Integer offer;//招聘ID

    @Column(name = "user_name",length = 20)
    private String userName; //姓名

    @Column(length = 1)
    private String gender;		//性别  1-男 0-女

    @Column
    private Integer age;        //年龄

    @Column(name = "post_name",length = 50)
    private String postName; //岗位名

    @Column(length = 30)
    private String mobile; //手机

    @Column(name = "acc_id")
    private Long accId;//账号accId

    @Column(name = "send_time")
    private Date sendTime;//通知面试时间

    @Column(name = "notification_num")
    private Integer notificationNum;//通知次数

    @Column(name = "interviewer_num")
    private Integer interviewerNum;//面试官人数

    @Column(name = "pass_num")
    private Integer passNum;//通过人数

    @Column(name = "hash_key" )
    private String hashKey;  // 游客提交标识， 用于区分 再次扫码二维码获取历史数据

    @Column
    private Integer state=1;//状态:1-已收,2-已通知,3-已面试,4-已中止,5-已入职

    @Column(name = "file_path" )
    private String filePath;  // 上传文件存储路径 个人简历文件上传

    @Column
    private Integer result;//结果:1-录用,0-终止

    @Column(length = 255)
    private String memo; //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"   , nullable=true , unique=false)
    private Integer operation; //操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1


    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice_post", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNoticePost rdNoticePost ;//招聘岗位表

    //招聘面试官表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= RdInterviewer.class, fetch= FetchType.EAGER, mappedBy="rdResume", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<RdInterviewer> rdInterviewerHashSet = new HashSet<RdInterviewer>();


    //招聘面试通知表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= RdInterviewNotification.class, fetch= FetchType.EAGER, mappedBy="rdResume", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<RdInterviewNotification> rdInterviewNotificationHashSet = new HashSet<RdInterviewNotification>();


    @Transient
    private String imgPath;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }


    public Integer getOffer() {
        return offer;
    }

    public void setOffer(Integer offer) {
        this.offer = offer;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getNotificationNum() {
        return notificationNum;
    }

    public void setNotificationNum(Integer notificationNum) {
        this.notificationNum = notificationNum;
    }

    public Integer getInterviewerNum() {
        return interviewerNum;
    }

    public void setInterviewerNum(Integer interviewerNum) {
        this.interviewerNum = interviewerNum;
    }

    public Integer getPassNum() {
        return passNum;
    }

    public void setPassNum(Integer passNum) {
        this.passNum = passNum;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }



    public Set<RdInterviewer> getRdInterviewerHashSet() {
        return rdInterviewerHashSet;
    }

    public void setRdInterviewerHashSet(Set<RdInterviewer> rdInterviewerHashSet) {
        this.rdInterviewerHashSet = rdInterviewerHashSet;
    }

    public Set<RdInterviewNotification> getRdInterviewNotificationHashSet() {
        return rdInterviewNotificationHashSet;
    }

    public void setRdInterviewNotificationHashSet(Set<RdInterviewNotification> rdInterviewNotificationHashSet) {
        this.rdInterviewNotificationHashSet = rdInterviewNotificationHashSet;
    }

    public Long getNoticePostId() {
        return noticePostId;
    }

    public void setNoticePostId(Long noticePostId) {
        this.noticePostId = noticePostId;
    }

    public RdNoticePost getRdNoticePost() {
        return rdNoticePost;
    }

    public void setRdNoticePost(RdNoticePost rdNoticePost) {
        this.rdNoticePost = rdNoticePost;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }


    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public String getHashKey() {
        return hashKey;
    }

    public void setHashKey(String hashKey) {
        this.hashKey = hashKey;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
