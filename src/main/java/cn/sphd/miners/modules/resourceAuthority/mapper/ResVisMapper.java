package cn.sphd.miners.modules.resourceAuthority.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.resourceAuthority.entity.ResVisit;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
@Component
public interface ResVisMapper extends BaseMapper<ResVisit>{

    //根据type和fileId获取数据
    List<ResVisit> getResVisit(ResVisit rv);

    //获取某日某机构人员的浏览或下载记录的次数
    List<ResVisit> getVisitByDatelistPage(QueryData query);

    //获取某段时间内的浏览或下载记录
    ResVisit getRecordNum(QueryData query);

    //获取某人某日浏览或下载的详细记录
    List<ResVisit> getDetailRecordByPersonlistPage(QueryData query);

    //获取某文件所有权限人员的浏览或下载的次数
    List<ResVisit> getAllPersonVisNumByFilelistPage(QueryData query);

    //获取某人所有文件的浏览或下载的次数
    List<ResVisit> getAllPersonVisNumByCreatorlistPage(QueryData query);

    //获取某文件浏览或下载的详细记录
    List<ResVisit> getDetailRecordByFilelistPage(QueryData query);

    //根据人和文件返回浏览或下载的一个月份列表
    List<String> getMonthForVisit(QueryData query);
}