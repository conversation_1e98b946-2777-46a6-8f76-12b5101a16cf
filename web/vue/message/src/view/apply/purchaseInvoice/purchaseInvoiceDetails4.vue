<template>
  <div id="purchaseInvoiceDetails4">
    <TY_NavTop :title="title" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div class="con1">
        <div class="oInfo" v-if="toHandle.poOrdersPrepayment">  <!--左侧v-if=""用于进行数组的校验-->
          <p style="margin-left: -21px;">
            计划付款金额&nbsp;{{ toHandle.poOrdersPrepayment.planAmount && toHandle.poOrdersPrepayment.planAmount.toFixed(2) }} 元
          </p>
          <p>
            <span style="margin-left: -21px;">
              计划付款时间&nbsp;{{ toHandle.poOrdersPrepayment.planDate |formatDay('YYYY-MM-DD')}}
            </span>
            <span style="margin-left: 16px;">计划付款方式&nbsp;{{isture}}</span>
          </p>
        </div>
        <p class="txt_right" v-if="toHandle.poOrders">
          申请人：{{ toHandle.poOrders.createName  }} {{toHandle.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}
        </p>
      </div>
      <div class="panel-content" style="margin-bottom: 12px;">
        <div>
          <table style="width:100%;"> <!--左侧同理进行数组的校验-->
            <tr>
              <td>供应商：{{ toHandle.srmSupplier.fullName  }}</td>
              <td style="text-align: right;">{{ toHandle.srmSupplier.codeName }}</td>
            </tr>
            <tr>
              <td>订单总额：{{ toHandle.poOrders.amount && toHandle.poOrders.amount.toFixed(2) }}元</td>
              <td style="text-align: right;">订单号：{{ toHandle.poOrders.sn }} </td>
            </tr>
            <tr>
              <td>订单日期：{{ toHandle.poOrders.createDate | formatDay('YYYY-MM-DD') }}</td>
              <td></td>
            </tr>
          </table>
        </div>
        <div class="panel-content">
          <div class="text-right">
            <el-link style="color: #409eff; margin-right: 20px;" @click="orderProgressRade()">订单查看
            </el-link>
            <el-link v-on:click="toggle = -toggle">审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link>
          </div>
          <div v-show="toggle > 0">
            <div class="item-content">
              <div class="processItem" v-if="toHandle.poOrders">
                <div>提交报销申请</div>
                <div>申请人</div>
                <div>{{toHandle.poOrders.createName | stringSplit(4)}} {{ toHandle.poOrders.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-for="it in toHandle.approvalProcessList" v-bind:key="it.id">
                <div v-if="it.approveStatus === '2' && it.businessType == 23" class="processItem"> <!--采购订单审批-->
                  <div>采购审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 63" class="processItem">
                  <div>付款审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 64" class="processItem">
                  <div>付款方式确认</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 65" class="processItem">
                  <div>付款复核通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 66" class="processItem">
                  <div v-if="toHandle.financePayment.method === '1'">现金付讫</div>
                  <div v-if="toHandle.financePayment.method === '3'">报销款已转支票</div>
                  <div v-if="toHandle.financePayment.method === '4'">报销款已转承兑汇票</div>
                  <div v-if="toHandle.financePayment.method === '5'">报销款已转账</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 67" class="processItem">
                  <div>付款方式修改</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <!-- 最后一条审批记录展示 -->
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType ===46">
                <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ nowApproveItem.reason }}</div></div>
                <br>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 45">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 47">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue">
                <span v-if="nowApproveItem.approveStatus === '2'">本次报销已完结！</span>
                <span v-else-if="nowApproveItem.approveStatus === '1' && nowApproveItem.userName !== 0 && nowApproveItem.userName !== null && sq == 0">{{nowApproveItem.userName}}为下一个审批人</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-content">
        <div style="background: #fff; padding:10px 15px;">
          <span>付款事由：</span><span style="color:red">采购的预付款</span>
        </div>
      </div>
      <div class="panel-content" v-if="code === 21">
        <div style="margin-top: 50px;">
          <el-radio-group v-model="approveStaPayment" size="small" style="margin-left: 20px;">
            <el-radio label="2">同意付款！</el-radio><br/>
          </el-radio-group>
          <div style="color:#0a9bee; font-size: 12px; line-height: 30px; margin-left: 29px;">注：对付款有异议，需进行调查时可暂不操作。</div>
        </div>
        <div class="handle_button" style="text-align: center;">
          <input type="submit" style="background: #0a9bee; color:#fff; width: 100px; text-align: center;" class="ui-btn" value="确定" @click="approvePayment">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import rejectPng from '../../../images/reject.png';

export default {
  name: 'purchaseInvoiceDetails4',
  data(){
    return {
      isReject: false, // 判断是否驳回来显示驳回图片
      rejectPng: rejectPng, // 驳回图片
      loading: true,
      toggle: -1, // 审批记录控制显隐
      isNowApprover: false,
      title: '采购预付款的申请',
      approveStatus: '',
      nowApproveItem: {},
      approveStaPayment:'',
      code:0,
      toHandle:{
        poOrders:{},
        srmSupplier:{},
        poOrdersPrepayment:{},
        approvalProcessList:[]
      }
    }
  },
  created() {
    let that = this;
    let paramCode = this.$route.params.id;
    let paramsq = this.$route.params.sq;
    console.log('判断是否是预付款的申请',paramsq);
    that.sq = paramsq;
    that.code = Number(paramCode.substring(0, 2))
    that.invoiceID = paramCode.substring(2, paramCode.length);
    if(that.code === 21){
      this.title='采购的预付款';
    }

    that.getDetail();
  },
  methods:{
    getDetail:function(){
      let that = this;
      console.log('invoiceID', that.invoiceID);
      this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do',{'orderPrepaymentId':that.invoiceID})
        .then(function (response) {
          let res = response.data.data;
          that.toHandle = res;
          that.loading = false;
          let ap = res.approvalProcessList || [];
          that.nowApproveItem = ap[ap.length - 1];
        })
        .catch(function (error) {
          console.log(error);
        })
    },
    orderProgressRade(){
      let id = this.toHandle.poOrders.id;
      this.$router.push({
        path: `/purchaseInvoiceMore/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    },
    approvePayment:function () {
      let that = this;
      let approveInfo = {
        approvalProcessId: that.toHandle.approvalProcessList[0].id  //目前id获取不到
      }
      console.log(approveInfo);
      that.loading = true;
      this.axios.post('../../../purchaseInvoice/advancePaymentPayApproved.do', approveInfo )
        .then((response) =>{
          let res = response.data.data;
          //     that.$kiko_message(res.content)
          //     that.loading =false;
          if(res.state === 1){//state的值是数字，content的值才是描述
            this.$router.push({  //若成功则跳转到哪个页面
              path: `/purchaseAdvancePayApproval`   //这个位置现在跳转有问题，不是同一个页面
            })
          }
        })
        .catch((error) =>{
          this.$router.push({
            path:`/purchaseInvoiceDetails4`
          })
          console.log(error);
        })
    },
  },
  computed:{
    isture: function () {
      let one = Number(this.toHandle.poOrdersPrepayment.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  }
}
</script>

<style lang="less">
#purchaseInvoiceDetails4{
  .right{ float: right;  }
  .ui-cell{ display: block;  }
  .rejectPng{
    position: absolute;
    top: calc( 50% - 195px);
    left: calc( 50% - 74px);
    z-index: 50;
    filter:alpha(opacity=60);
    opacity:0.6;
    -moz-opacity:0.6;
    -khtml-opacity: 0.6
  }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
}
</style>
