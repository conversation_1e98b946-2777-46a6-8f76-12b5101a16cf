
/*审批流程的样式*/
.ok-icon { background: url(../user/icon/ok.png) no-repeat center center;  }
.no-icon { background: url(../user/icon/no.png) no-repeat center center;  }
.wait-icon { background: url(../user/icon/wait.png) no-repeat center center;  }
.ok-icon , .no-icon , .wait-icon { background-size:25px 25px; display:inline-block; width:25px; height:25px;margin-right:15px;    }
.processTr{ padding-top:15px;      }
.touserInfo{ margin-right:30px;    }
.touserInfo , .timeInfo{ position:relative; top:-8px;    }
.memoInfo{  padding:5px 0 5px 50px;   }
.noProcess{ color:#E7505A;  }
.wipe_deal{ font-size:20px; padding: 0 0 20px ;   }
.applyYes{ background: rgb(224, 235, 249);   }
.applyNo{ background: rgb(254, 231, 232);  }
.dealSee_continer{ margin:20px 0 ; padding:20px; color:#3598dc;   }
.dealSee_continer .ttl { font-size:20px;     }


#info_img>img{ height:50px;      }
/*// 大图的展示*/
#info_img>img{ height:50px ; max-width: 200px   }
#bigImagcon{ background: #fff ; margin:0 auto; position: relative ;   }
#imgController{ background: #101010; z-index:1;  font-size: 23px; width: 400px; height:45px; position: absolute;  bottom: 100px;  text-align: center ; padding:5px 0 ;    }
#imgController .rotate , #bigImagcon .closeImg{  color:#ddd; margin-right: 25px;  }
#imgController .closeImg{ font-size: 25px;   }
#imgController .closeImg:hover{ color:#ed5565;  }
#imgController .rotate:hover { color:#fff;   }
.bigImagC{ position: relative;  }
#bigImag{  display:block ; box-shadow:0 0 5px #6e6c6c; margin: 0 auto ;   }
.bounce_Fixed{ flex-direction:column; justify-content:center; }
#amountTip{ color:red; font-size:11px;  }




/* 报销的审批流程 */
.processStep{ position:relative;margin-top:20px;   }
.line{ position: relative; display: inline-block; width:100%;overflow: hidden;height:30px;  }
.line>div{ border-top:3px dotted #ccc;  position: absolute; width: 100%;top:21px; }
.line>div:before{ display: inline-block; width:10px; height:10px; background: #ccc; transform: rotate(45deg); position: absolute;content:""    }
.line>div>div:after{ display: inline-block; width: 16px; height: 16px; background: #ccc; transform: rotate(45deg); position: absolute; content: "";}
.line>div:before{ left: -5px; top: -7px;  }
.line>div>div{ overflow: hidden; width: 24px; height: 30px; right:-12px; top: -14px; display: inline-block; position: absolute;}
.line>div>div:after{     right: 15px; top: 2px; }
.steps{ display: flex; margin-top:-36px;  }
.stepItem{ font-size:16px; flex: 1 }
.stepItem small{ font-size:10px;   }
.litCircle{    border: 2px solid #0F4C75; background: #fff; padding: 2px; border-radius: 25px; position: relative; display: inline-block; color: #fff;}
.litCircle>div{text-align: center; background:#0F4C75; width:40px; height:40px; border-radius:20px; padding-top:8px; font-size:20px;  }
.bigCircle{ text-align: left; color: #275b7d; height: 90px; border-left: 2px solid #0F4C75; position: relative; padding-top: 14px; margin-top: 25px; margin-left:20px; padding-left: 10px; line-height: 15px; font-size: 10px;  }
.bigCircle:before{ width: 8px;height: 8px;border-radius: 5px;background: #0f4c75;content: "";display: inline-block;position: absolute;top: -18px;left:-5px;  }


