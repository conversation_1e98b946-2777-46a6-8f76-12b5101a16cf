<template>
  <div class="MCI">
    <div class="mclhome" v-show="mcleto === 0">
      <div>
        <div class="main rolor">
          <el-row>
            <el-col :span="15">
              <div>
                <!--            <div class="backBtn">-->
                <!--              <el-button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" @click="" v-show="mcleto === 1">返回</el-button>-->
                <!--            </div>-->
                <div>
                  材料采购指数（MCI）为系统对某供应商供应某种材料的评估分值，可用以解决材料下采购订单时，哪个供应商排在上方的问题。<br>
                  系统有默认的MCI算法，如觉必要可查看算法管理并重新设置，或调整权重。
                </div>
              </div>
            </el-col>
            <el-col :span="9">
              <div>
                <el-row>
                  <el-col :span="10"><div></div></el-col>
                  <el-col :span="14">
                    <div>
                      <el-row>
                        <el-col :span="4"><div></div></el-col>
                        <el-col :span="20">
                          <div>
                            <el-button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" @click="weightAdjustmentBtn()" style="margin-left: 5px;">调整权重</el-button>
                            <el-button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" @click="weightAdjustmentRecordBtn()" style="margin-left: 5px;">权重调整记录</el-button>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
          <el-table :data="catpurchase" class="ty-table-control">
            <el-table-column prop="indexName" label="指标" width="" align="center"></el-table-column>
            <el-table-column prop="fullMark" label="满分" width="" align="center"></el-table-column>
            <el-table-column prop="weight" label="权重" width="" align="center"></el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="algorithmManage(scope.row)">算法及管理</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="mclqzset" v-show="mcleto === 1">
      <div>
        <div class="main rolor">
          <el-row>
            <el-col :span="16">
              <div>
                <el-button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" @click="back(1)">返回</el-button>
              </div>
            </el-col>
            <el-col :span="8">
              <div>
                <el-row>
                  <el-col :span="11"><div></div></el-col>
                  <el-col :span="13">
                    <div>
                      <el-row>
                        <el-col :span="1"><div></div></el-col>
                        <el-col :span="23">
                          <div>
                            <el-button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 ty-right" @click="sureAdjustWeight">确定</el-button>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
          <el-table :data="newpurchase" class="ty-table-control">
            <el-table-column prop="indexName" label="指标的类别" width="" align="center"></el-table-column>
            <el-table-column prop="weight" label="原权重" width="" align="center"></el-table-column>
            <el-table-column label="新权重" width="" align="center">
              <template #default="scope">
                <el-input v-model="scope.row.weight2" placeholder="请输入内容" style="width: 120px;height: 30px;"></el-input>%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="mclqzlst" v-show="mcleto === 2">
      <div>
        <div class="main rolor">
          <el-row>
            <el-col :span="16">
              <div>
                <el-button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" @click="back(1)">返回</el-button>
              </div>
            </el-col>
            <el-col :span="8"><div></div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24"><div class="grid-content bg-purple-dark text-center">
              <h4>权重调整记录</h4>
            </div></el-col>
          </el-row>
          <el-row>
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <div class="tips">当前数据系统第{{ setice }}次修改后的结果</div>
            </div></el-col>
          </el-row>
          <el-table :data="setpurchase" class="ty-table-control">
            <el-table-column prop="menners" width="" align="center"></el-table-column>
            <el-table-column prop="settime" width="" align="center"></el-table-column>
            <el-table-column width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="seeRecord(scope.row.versionNo)">查看</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="mclqzlok" v-show="mcleto === 3">
      <div>
        <div class="main rolor">
          <el-row>
            <el-col :span="16">
              <div>
                <el-button class="ty-btn ty-btn-big ty-btn-blue ty-circle-3" @click="back(2)">返回</el-button>
              </div>
            </el-col>
            <el-col :span="8"><div></div></el-col>
          </el-row>
          <el-table :data="lokpurchase" class="ty-table-control">
            <el-table-column prop="indexName" label="指标的类别" width="" align="center"></el-table-column>
            <el-table-column prop="weight" label="权重" width="" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <TyDialog v-show="alormanent" width="580" dialogTitle="算法及管理" color="blue" :dialogHide="almaCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="almaCancel">关闭</el-button>
      </template>
      <template #dialogBody>
        <div v-show="alor === 1" class="1-1">
          <el-row style="margin-bottom: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="font-size: 14px;margin-top: 10px;
              margin-bottom: 10px;color: inherit;font-weight: bold;">{{ alok.almodule }}的算法</div></el-col>
          </el-row>
          <el-row style="margin-bottom: 20px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="font-size: 13px;color: #909399;">
              某材料本指标的算法按下表。本指标的算法不可修改。
            </div></el-col>
          </el-row>
          <el-table :data="alopurchase" class="ty-table-control">
            <el-table-column prop="value" label="实际情况" width="" align="center"></el-table-column>
            <el-table-column prop="mark" label="本项指标实际得分" width="" align="center">
              <template #default="scope">
                <span style="background-color: #f9e491;padding: .2em;">{{ scope.row.mark }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-row style="margin-top: 15px;" v-for="(item1,index1) in alopurchase" :key="index1">
            <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
            <el-col :span="12"><div class="grid-content bg-purple-right">
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple"></div></el-col>
                <el-col :span="23"><div class="grid-content bg-purple-right" v-show="index1 === 0" style="font-size: 13px;color: #909399;">
                  当前算法的生效时间：{{ item1.enabledTime }}
                </div> </el-col>
              </el-row>
            </div></el-col>
          </el-row>
        </div>
        <div v-show="alor === 2" class="1-5">
          <el-row style="margin-bottom: 20px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="font-size: 14px;margin-top: 10px;
              margin-bottom: 10px;color: inherit;font-weight: bold;">{{ alok.almodule }}的算法</div></el-col>
          </el-row>
          <el-row>
            <el-col :span="14">
              <div class="grid-content bg-purple-dark">
                <el-row>
                  <el-col :span="24" style="margin-bottom: 5px;">
                    <div class="grid-content bg-purple-dark" style="color: #909399;">本指标现采用如下的分段式计值法。</div> </el-col>
                  <el-col :span="24" style="margin-bottom: 5px;">
                    <div class="grid-content bg-purple-dark" style="color: #909399;">您可"自定义参数"，也可"更换为其他算法"。</div> </el-col>
                  <el-col :span="24"><div class="grid-content bg-purple-dark" style="color: #909399;">
                    两种操作均将生成修改记录。" </div> </el-col>
                </el-row>
              </div> </el-col>
            <el-col :span="10"><div class="grid-content bg-purple-right"></div> </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <el-row>
                <el-col :span="11"><div class="grid-content bg-purple"></div></el-col>
                <el-col :span="13"><div class="grid-content bg-purple">
                  <span class="ty-linkBtn" style="font-weight: bold;">自定义参数</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">更换为其他算法</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">修改记录</span>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-table :data="alopurchase2" class="ty-table-control" style="margin-top: 10px;">
            <el-table-column prop="" label="实际情况" width="" align="center"></el-table-column>
            <el-table-column prop="" label="本项指标实际得分" width="" align="center"></el-table-column>
          </el-table>
          <el-row style="margin-top: 10px;">
            <el-col :span="20">
              <div class="grid-content bg-purple">
                <el-row>
                  <el-col :span="24" style="margin-bottom: 5px;">
                    <div class="grid-content bg-purple-dark" style="color: #909399;">本指标现采用如下的步进式算法。</div>
                  </el-col>
                  <el-col :span="24" style="margin-bottom: 5px;">
                    <div class="grid-content bg-purple-dark" style="color: #909399;">您可“更换为其他算法”，也可“修改当前算法中的参数”。</div>
                  </el-col>
                  <el-col :span="24" style="margin-bottom: 5px;">
                    <div class="grid-content bg-purple-dark" style="color: #909399;">两种操作均将生成修改记录。" </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col :span="4"><div class="grid-content bg-purple-right"></div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <el-row>
                <el-col :span="11"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="13"><div class="grid-content bg-purple">
                  <span class="ty-linkBtn" style="font-weight: bold;">自定义参数</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">更换为其他算法</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">修改记录</span>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="color: #909399;">设系统中某供应商挂账天数的数据为A，步长为S，本项指标实际得分为K，则 </div> </el-col>
          </el-row>
          <el-row>
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="text-align: center;margin-top: 10px;
              font-size: 18px;">K=A/S</div> </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="margin-bottom: 5px;">
              <div class="grid-content bg-purple-dark">其中</div>
            </el-col>
            <el-col :span="24" style="margin-bottom: 5px;">
              <div class="grid-content bg-purple-dark">S单位为天，当前设置为X。</div>
            </el-col>
            <el-col :span="24" style="margin-bottom: 5px;">
              <div class="grid-content bg-purple-dark">K最大值限制为100，即达到100后不再随A增加而变化。</div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 15px;">
            <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
            <el-col :span="12"><div class="grid-content bg-pruple-right">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="16"><div class="grid-content bg-purple-right" style="font-size: 13px;color: #909399;">
                  当前算法的生效时间：XX
                </div> </el-col>
              </el-row>
            </div></el-col>
          </el-row>
        </div>
        <div v-show="alor === 3" class="2-4">
          <el-row style="margin-bottom: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="font-size: 14px;margin-top: 10px;
              margin-bottom: 10px;color: inherit;">X的算法</div> </el-col>
          </el-row>
          <el-row style="margin-bottom: 20px;">
            <el-col :spn="24"><div class="grid-content bg-purple-dark">
              本指标算法如下。
            </div> </el-col>
            <el-col :span="24"><div class="grid-content bg-purple-dark">您可”自定义参数“，操作后将生成修改记录。</div> </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <el-row>
                <el-col :span="11"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="13"><div class="grid-content bg-purple">
                  <span class="ty-linkBtn" style="font-weight: bold;">自定义参数</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">修改记录</span>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-table :data="alopurchase3" class="ty-table-control" style="margin-top: 10px;">
            <el-table-column prop="" label="实际情况" width="" align="center"></el-table-column>
            <el-table-column prop="" label="本项指标实际得分" width="" align="center"></el-table-column>
          </el-table>
          <el-row style="margin-top: 15px;">
            <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
            <el-col :span="12"><div class="grid-content bg-pruple-right">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="16"><div class="grid-content bg-purple-right" style="font-size: 13px;color: #909399;">
                  当前算法的生效时间：XX
                </div></el-col>
              </el-row>
            </div></el-col>
          </el-row>
        </div>
        <div v-show="alor === 4" class="2-9">
          <el-row style="margin-bottom: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="font-size: 14px;margin-top: 10px;
              margin-bottom: 10px;color: inherit;">X的算法</div> </el-col>
          </el-row>
          <el-row style="margin-bottom: 20px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">本指标算法如下：</div> </el-col>
            <el-col :span="24"><div class="grid-content bg-purple-dark">您可“自定义”参数，操作后将生成修改记录。</div> </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple=dark">
              <el-row>
                <el-col :span="11"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="13"><div class="grid-content bg-purple">
                  <span class="ty-linkBtn" style="font-weight: bold;">自定义参数</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">修改记录</span>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-table :data="alopurchase4" class="ty-table-control" style="margin-top: 10px;">
            <el-table-column prop="value" label="实际情况" width="" align="center"></el-table-column>
            <el-table-column prop="mark" label="本想指标实际得分" width="" align="center"></el-table-column>
          </el-table>
          <el-row style="margin-top: 15px;">
            <el-col :span="12"><div class="grid-content bg-purple"></div> </el-col>
            <el-col :span="12"><div class="grid-content bg-purple-right">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="16"><div class="grid-content bg-purple-right" style="font-size: 13px;color: #909399;">
                  当前算法的生效时间：{{ alo29.enabledTime }}
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-row style="margin-top: 15px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              开票属性相同的材料价格可比较，不同的则不好比较。
            </div> </el-col>
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              价格补偿系数，一下简称系数，用以不同开票属性价格比较时，修正价格间差异。
            </div> </el-col>
            <el-col :span="24"><div class="grid-content bg-pruple-dark">下表中为系统当前的值。</div> </el-col>
            <el-col :span="24"><div class="grid-content bg-purple-dark">您可“修改价格补偿系数”，操作后将生成修记录。</div> </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <el-row>
                <el-col :span="11"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="13"><div class="grid-content bg-purple">
                  <span class="ty-linkBtn" style="font-weight: bold;">修改价格补偿系数</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">修改记录</span>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-table :data="alopurchase5" class="ty-table-control" style="margin-top: 10px;">
            <el-table-column prop="name" label="场景" width="" align="center"></el-table-column>
            <el-table-column prop="value" label="系数当前的值" width="" align="center"></el-table-column>
          </el-table>
          <el-row style="margin-top: 15px;" v-for="(item,index)  in alopurchase5" :key="index">
            <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
            <el-col :span="12"><div class="grid-content bg-pruple-right">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="16"><div class="grid-content bg-purple-right" v-show="index === 0">
                  当前数据的生效时间：{{ item.enabledTime }}
                </div></el-col>
              </el-row>
            </div></el-col>
          </el-row>
        </div>
        <div v-show="alor === 5" class="2-14">
          <el-row style="margin-bottom: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="font-size: 14px;margin-top: 10px;
              margin-bottom: 10px;color: inherit;">X的算法</div> </el-col>
          </el-row>
          <el-row style="margin-bottom: 20px;">
            <el-col :spn="24"><div class="grid-content bg-purple-dark">
              本指标算法如下。
            </div> </el-col>
            <el-col :span="24"><div class="grid-content bg-purple-dark">您可”自定义参数“，操作后将生成修改记录。</div> </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <el-row>
                <el-col :span="11"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="13"><div class="grid-content bg-purple">
                  <span class="ty-linkBtn" style="font-weight: bold;">自定义参数</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">修改记录</span>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-table :data="alopurchase6" class="ty-table-control" style="margin-top: 10px;">
            <el-table-column prop="" label="采购周期的天数" width="" align="center"></el-table-column>
            <el-table-column prop="" label="本项指标实际得分" width="" align="center"></el-table-column>
          </el-table>
          <el-row style="margin-top: 15px;">
            <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
            <el-col :span="12"><div class="grid-content bg-pruple-right">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="16"><div class="grid-content bg-purple-right" style="font-size: 13px;color: #909399;">
                  当前算法的生效时间：XX
                </div></el-col>
              </el-row>
            </div></el-col>
          </el-row>
        </div>
        <div v-show="alor === 6" class="2-15">
          <el-row style="margin-bottom: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark" style="font-size: 14px;margin-top: 10px;
              margin-bottom: 10px;color: inherit;">X的算法</div> </el-col>
          </el-row>
          <el-row style="margin-bottom: 20px;">
            <el-col :spn="24"><div class="grid-content bg-purple-dark">
              本指标算法如下。
            </div> </el-col>
            <el-col :span="24"><div class="grid-content bg-purple-dark">您可”自定义参数“，操作后将生成修改记录。</div> </el-col>
            <el-col :span="24"><div class="grid-content bg-purple-dark ty-color-blue">注：材料情况差异很大，本指标仅能对部分材料起到作用！</div> </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <el-row>
                <el-col :span="11"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="13"><div class="grid-content bg-purple">
                  <span class="ty-linkBtn" style="font-weight: bold;">自定义参数</span>
                  <span class="ty-linkBtn" style="font-weight: bold;">修改记录</span>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <el-table :data="alopurchase7" class="ty-table-control" style="margin-top: 10px;">
            <el-table-column prop="" label="最低采购量的数值（计量单位无法示出）" width="" align="center"></el-table-column>
            <el-table-column prop="" label="本项指标实际得分" width="" align="center"></el-table-column>
          </el-table>
          <el-row style="margin-top: 15px;">
            <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
            <el-col :span="12"><div class="grid-content bg-pruple-right">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="16"><div class="grid-content bg-purple-right" style="font-size: 13px;color: #909399;">
                  当前算法的生效时间：XX
                </div></el-col>
              </el-row>
            </div></el-col>
          </el-row>
        </div>
      </template>
    </TyDialog>
    <TyDialog  v-show="alupsuaxi" width="" dialogTitle="修改价格补偿系数" color="blue" :dialogHide="alupsuCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="alupsuCancel">取消</el-button>
        <el-button class="bounce-ok">确定</el-button>
      </template>
      <template #dialogBody>
        <el-row><el-col :span="24"><div class="grid-content bg-purple-dark">请在空格内录入价格补偿系数</div> </el-col></el-row>
        <el-table :data="alopurchase11" class="ty-table-control" style="margin-top: 10px;">
          <el-table-column prop="" label="场景" width="" align="center"></el-table-column>
          <el-table-column prop="" label="系数" width="" align="center"></el-table-column>
        </el-table>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import {beforeRouteLeave, initNav} from "../../utils/routeChange";
import {getAecbyCat, getMCIList, getweigList, getweisetList} from "../../api/MCI";
import axios from "axios";
//import TyDialog from "../../components/TyDialog";

export default {
  //components: {TyDialog},
  //name: "MCI",
  data(){
    return{
      pageName: 'MCI',
      mcleto: 0,//切换不同页面内容
      //mclpro: 0,//切换不同情况
      catpurchase: [],//采购指数数据
      newpurchase: [],//权重调整数据
      setpurchase: [],//权重调整记录数据
      setice: '',//权重系统数据修改后的第？次结果
      lokpurchase: [],//权重调整记录查看
      //Algorithm and Management
      alormanent: '',//用于判断‘算法及管理’弹窗是否展示的
      alopurchase: [],//算法与管理列表数据-页面：1-1
      alopurchase2: [],//算法与管理列表数据-页面：1-5
      alopurchase4: [],//算法与管理列表 数据：2-9
      alopurchase5: [],
      alopurchase11: [],
      alor: '',//1-代表第一种类型,2-代表第二种类型，3-代表第三种类型
      alok: {
        almodule: '',//算法名称
      },
      alo29: {
        enabledTime: '',//算法时间
      },
      alupsuaxi: false,
    }
  },
  mounted(){//页面刷新后立即执行的函数
    this.getMCIList(2);
  },
  beforeRouteLeave(to, from, next){
    beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'mc',name: 'MCI',pageName: this.pageName}, this.mountedFun, this)
  },
  methods:{
    //获取指标列表
    getMCIList(category){
      let data = {category: category};
      getMCIList(data).then(res => {
        console.log('4.29-9:29',res.data);
        let catlist = res.data;
        catlist.sort((a,b) => {
          return a.module.slice(2,a.module.length) - b.module.slice(2,b.module.length)
        })
        console.log('4.29-10:00',catlist);
        catlist.forEach(item1 => {
          item1.weight = item1.weight+ '%';
        })
        this.catpurchase = catlist;
      })
    },
    //调整权重
    weightAdjustmentBtn(){
      this.mcleto = 1;
      let data = {category: 2};
      getMCIList(data).then(res => {
        let catlist = res.data;
        catlist.sort((a,b) => {
          return a.module.slice(2,a.module.length) - b.module.slice(2,b.module.length)
        })
        catlist.forEach(itemc => {
          itemc.weight2 = itemc.weight;
        })
        this.newpurchase = catlist;
      })
    },
    //返回上一页
    back(nep){
      if(nep === 1){
        this.mcleto = 0;
        this.getMCIList(2);
      }else if(nep === 2){
        this.mcleto = 2;
        this.weightAdjustmentRecordBtn();
      }
    },
    //权重调整-确定
    sureAdjustWeight(){
      let newweit = [];
      let countt = 0;
      console.log('4.30-11:06',this.newpurchase);
      let newt = this.newpurchase;
      newt.forEach(item1 => {
        let json1 = {id: item1.id, weight: Number(item1.weight2)};
        newweit.push(json1);
        countt += Number(item1.weight2);
      })
      console.log("4.30-11:10",newweit);//它是个数组
      console.log("4.30-11:11",countt);
      //将countt转换成整数（没有小数）的格式，并根据数值进行判断
      if(Number(countt.toFixed()) !== 100){
        this.$message.error('请检查数据，各指标所占权重之和需等于100%！');
        return false;
      }
      const newstr = JSON.stringify(newweit);
      console.log('5.21-9:02',newweit);
      console.log('5.21-9:03',newstr);
      console.log('类型判断:', typeof newstr);
      try {
        const parsed = JSON.parse(newstr);
        console.log('反向解析成功，确实是JSON字符串:', parsed);
      } catch (e) {
        console.error('字符串格式错误:', e);
      }
      axios.interceptors.request.use(config => {
        console.log('拦截器修改前的数据:', config.data);
        return config;
      });
      console.log('5.21-9:04',newstr);
      //newstr = JSON.stringify(newstr);
      console.log('Axios 配置:', axios.defaults);
      //console.log('5.21-9:54',eisstr);
      // const encodedStr = encodeURIComponent(newstr); // 关键步骤：URL 编码
      // // ✅ 打印编码后的字符串（应包含 %22、%3A 等百分号编码）
      // console.log('URL 编码后的字符串:', encodedStr);
      // // ✅ 反向验证：解码后应与原字符串一致
      // const decodedStr = decodeURIComponent(encodedStr);
      // console.log('解码后的字符串:', decodedStr);
      // console.log('反向解析成功:', JSON.parse(decodedStr));
      //debugger;
      //const json = {eisIndicesStr: newstr};
      //JSON.stringify({ data: str }
      const headers = {'Content-Type': 'application/json;charset=utf-8'};
      axios.post("../eis/edit",{eisIndicesStr: newstr}).then(response => {
        let code = response.code;
        if(code === 0){
          this.$message.success(response.msg);
          this.back();
        }
      }).catch(error => {
        this.$message.error('操作失败');
      })
      // uppurchae(newweit).then(res => {
      //   let code = res.code;
      //   if(code === 0){
      //     this.$message.success(res.msg);
      //     this.back();
      //   }else{
      //     this.$message.error('操作失败');
      //   }
      // })
    },
    //权重调整记录
    weightAdjustmentRecordBtn(){
      this.mcleto = 2;
      let json = {category: 2};
      getweigList(json).then(res => {
        console.log('5.6-10:34',res);
        let weislik = res.data;
        weislik.forEach((itemw,indexw) => {
          if(weislik.length < 2){
            this.setice = '权重尚未经修改';
          }else{
            this.setice = this.setpurchase.length - 1;
          }
          if(indexw === 0){
            itemw.menners = '初始权重'
            itemw.settime = '创建时间：'+itemw.createDate;
          }else{
            itemw.menners = '第'+indexw+'次修改后';
            itemw.settime = ' 修改时间：'+itemw.updateDate;
          }
        })
        this.setpurchase = weislik;
      })
    },
    //点击权重调用记录列表中的查看按钮
    seeRecord(versionNo){
      this.mcleto = 3;
      let json = {versionNo: versionNo,category: 2};
      getweisetList(json).then(res => {
        let list = res.data.result;
        list.sort((a,b) => {
          return a.module.slice(2, a.module.length) - b.module.slice(2, b.module.length)
        })
        list.forEach(items => {
          items.weight = items.weight + '%';
        })
        this.lokpurchase = list;
      })
    },
    //算法及管理
    algorithmManage(lank){
      this.alormanent = true;
      this.alor = 1;
      this.alok.almodule = lank.indexName;
      console.log('5.14-13:54',lank);
      const id = lank.id;
      const json = {eisIndex: id};
      getAecbyCat(json).then(res => {
        console.log('5.14-14:11',res);
        const eisinfo = res.data.eis;//算法数据
        const algorithmCat = eisinfo.algorithmCat;
        const indexName = eisinfo.indexName;
        const param = res.data.param;
        const lanst = res.data.data;
        if(algorithmCat === 1){
          if(eisinfo === '2-9'){
            const enumerate = lanst;
            enumerate.forEach((item,index)=> {
              if(Number(index) === 0){
                this.alo29.enabledTime = item.enabledTime;
              }
            })
            this.alopurchase4 = enumerate;
            console.log('5.19-9:44',param);
            // param.forEach((item,index) => {
            //   if(index === 0){
            //     const ebtime = item.enabledTime;
            //   }
            // })
            this.alopurchase5 = param;//因为此时 param=[]所以看不出来效果 。
          }else{
            const enumerate = lanst;
            this.alopurchase = enumerate;
          }
        }else if(algorithmCat === 2){
          if(eisinfo === '1-5'){

          }
        }
      })
    },
    almaCancel(){
      this.alormanent = false;
    },
    alupsuCancel(){
      this.alupsuaxi = false;
    }
  }
}
</script>

<style scoped lang="scss">
.mclhome, .mclqzset, .mclqzlst, .mclqzlok{
  .rolor{
    margin: 57px 96px 0 96px;
  }
}
.mclqzlst{
  .rolor{
    .text-center{
      text-align: center;
      h4{font-size: 18px;}
    }
    .tips{color: #909399;font-size: 13px;}
  }
}
</style>