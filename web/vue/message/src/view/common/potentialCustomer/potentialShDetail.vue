<template>
  <div id="potentialApply" class="potential">
    <TY_NavTop :title="searchTtl" isRecord="true" @toggleRecord="jumpRecord"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-title">
        基本信息 <div class="right" v-on:click="recordToggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
      </div>
      <div class="panel-content">
        <div v-show="toggle > 0">
          <div class="recorditem" v-for="(record, index) in handleRecord" :key="index">
            <div class="recordDetail">
              <span v-if="record.approveStatus === '1'">申请人： {{record.userName}} {{record.handleTime | formatDate('yyyy/MM/dd hh:mm:ss')}}</span>
              <span v-if="record.approveStatus === '2'">审批人： {{record.userName}} {{record.handleTime | formatDate('yyyy/MM/dd hh:mm:ss')}}</span>
              <span v-if="record.approveStatus === '3'">销售负责人： {{record.userName}} {{record.handleTime | formatDate('yyyy/MM/dd hh:mm:ss')}}</span>
            </div>
          </div>
        </div>
        <el-row>
          <el-col :span="6">客户名称</el-col>
          <el-col :span="18">{{details.fullName}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">地    址</el-col>
          <el-col :span="18">{{details.address}}</el-col>
        </el-row>
        <div v-if="sphdSocket.user.oid === 0">
          <el-row>
            <el-col :span="6">最高负责人</el-col>
            <el-col :span="18">{{details.supervisorName}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="6">手 机</el-col>
            <el-col :span="18">{{details.supervisorMobile}}</el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="6">全景图片</el-col>
          <el-col :span="18">
            <div class="col-flex">
              <div class="block" v-for="(img, index) in details.qImages" :key="index">
                <el-image
                  style="width: 80px; height: 80px"
                  :src="fileUrl + img.normal"
                  fit="contain" @click="preview(1, img.normal)"></el-image>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">产品图片</el-col>
          <el-col :span="18">
            <div class="col-flex">
              <div class="block" v-for="(img, index) in details.pImages" :key="index">
                <el-image
                  style="width: 80px; height: 80px"
                  :src="fileUrl + img.normal"
                  fit="contain" @click="preview(2, img.normal)"></el-image>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触日期</el-col>
          <el-col :span="18">{{details.firstContactTime | formatDate('yyyy/MM/dd')}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触地点</el-col>
          <el-col :span="18">{{details.firstContactAddress}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">信息获取渠道</el-col>
          <el-col :span="18">{{details.infoSource}}</el-col>
        </el-row>
      </div>
      <div class="panel-title">
        联系人
      </div>
      <div>
        <div class="ui-cells_none">
          <a class="ui-cell" @click="jumpContact(contact.id)" v-for="(contact, index) in details.contactsList" :key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <el-row>
                  <el-col :span="8">{{contact.name}}</el-col>
                  <el-col :span="8">{{contact.post}}</el-col>
                  <el-col :span="8">{{contact.mobile}}</el-col>
                </el-row>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
      <div class="panel-title">
        访谈记录
      </div>
      <div>
        <div class="ui-cells_none">
          <a class="ui-col" @click="jumpInteview(view.id)" v-for="(view, index) in details.interviewList" :key="index">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div class="clearBoth">
                  <div class="viewLt">访谈对象 共{{view.interviewer | countNum}}人：{{view.interviewer}}</div>
                  <div class="viewRt">访谈日期 {{view.interviewDate | formatDate('yyyy-MM-dd')}}</div>
                </div>
                <div>访谈目标:{{view.content}}
                </div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
            <div class="ui-col__ft">
              <div class="createDetail">录入者： {{view.createName}} {{view.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</div>
              <div class="interviewCon"></div>
            </div>
          </a>
        </div>
      </div>
    </div>
    <el-dialog
      :title="imgTitle"
      :visible.sync="previewDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="previewDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="previewPath" :download="previewPath" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="less">
  .clearBoth:before, .clearBoth:after {
    display: block;
    clear: both;
    content: "";
    visibility: hidden;
    height: 0;
  }
  .viewLt{
    float: left;
    width: 56%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .viewRt {
    float: right;
    width: 42%;
  }
  .wideSize .ui-col_con{
    width: 330px;
  }
</style>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'potentialQueryDetail',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      searchTtl: '',
      details: '',
      toggle: -1,
      handleRecord: [],
      browser: 1,
      imgTitle: '',
      previewPath: '',
      previewDialog: false,
      fileUrl: window.parent.$.fileUrl
    }
  },
  created () {
    let _this = this
    let source = _this.$route.params.source
    let detailId = _this.$route.params.id
    console.log('搜索详情1： ' + source)
    console.log('搜索详情2： ' + detailId)
    if (source === '1') {
      _this.searchTtl = '潜在客户申请'
    } else {
      _this.searchTtl = '潜在客户受理'
    }
    this.axios.post('../../../sale/getSelectPotentialOne.do', {
      id: detailId
    }).then(function (response) {
      _this.loading = false
      _this.details = response.data.data
    }).catch(function (error) {
      console.log(error)
    })
  },
  methods: {
    jumpRecord: function () {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialMsgRecord/${detailId}`
      })
    },
    jumpContact: function (id) {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialContact/${detailId}/${id}`
      })
    },
    jumpInteview: function (id) {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialInteview/${detailId}/${id}/${2}`
      })
    },
    recordToggle: function () {
      let _this = this
      _this.toggle = -_this.toggle
      if (_this.toggle > 0) {
        this.axios.post('../../../sale/getCustomerApprovalRecordList.do', {
          id: _this.$route.params.id
        }).then(function (response) {
          _this.handleRecord = response.data.data
        }).catch(function (error) {
          console.log(error)
        })
      }
    },
    preview: function (tab, path) {
      this.previewDialog = true
      if (tab === 1) {
        this.imgTitle = '全景图片'
      } else {
        this.imgTitle = '产品图片'
      }
      this.previewPath = path
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    }
  }
}
</script>
