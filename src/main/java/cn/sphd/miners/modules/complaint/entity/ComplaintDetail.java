package cn.sphd.miners.modules.complaint.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity(name="ComplaintDetail")
@Table(name = "t_complaint_detail")
public class ComplaintDetail implements Serializable {

  /**
   *
   */
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(name = "peroid")
  private Integer peroid;

  @Column(name = "complaint")
  private Integer complaint;

  @Column(name = "problem")
  private String problem;

  @Column(name = "consequence")
  private String consequence;

  @Column(name = "appeal")
  private String appeal;

  @Column(name = "orders")
  private Integer orders;

  @Column(name = "customer_product")
  private Integer customerProduct;

  @Column(name = "outer_sn")
  private String outerSn;

  @Column(name = "amount")
  private BigDecimal amount;

  @Column(name = "percent")
  private BigDecimal percent;

  @Column(name = "memo")
  private String memo;

  @Column(name = "creator")
  private Integer creator;

  @Column(name = "create_name")
  private String createName;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "updator")
  private Integer updator;

  @Column(name = "update_name")
  private String updateName;

  @Column(name = "update_time")
  private Date updateTime;

  @Transient
  private String listAttachment;   //string类型图片附件数组
  @Transient
  private String listProductOutput;          //String类型产品出库数组
  @Transient
  private String innerSn;      //内部图号
  @Transient
  private String innerName;    //内部名称
  @Transient
  private String outersn;     //外部图号
  @Transient
  private String outerName;   //外部名称


  @JsonIgnore
  @OneToMany(targetEntity=ComplaintAttachment.class, fetch= FetchType.LAZY, mappedBy="detailAttachment", cascade= CascadeType.REMOVE)
  private Set<ComplaintAttachment> detailComplaintAttachmentHashSer = new HashSet<>();

  @JsonIgnore
  @OneToMany(targetEntity=ComplaintProductOutput.class, fetch= FetchType.LAZY, mappedBy="detailComplaintProductOutput", cascade= CascadeType.REMOVE)
  private Set<ComplaintProductOutput> detailComplaintProductOutputHashSer = new HashSet<>();

  //waijian


  public Set<ComplaintAttachment> getDetailComplaintAttachmentHashSer() {
    return detailComplaintAttachmentHashSer;
  }

  public void setDetailComplaintAttachmentHashSer(Set<ComplaintAttachment> detailComplaintAttachmentHashSer) {
    this.detailComplaintAttachmentHashSer = detailComplaintAttachmentHashSer;
  }

  public Set<ComplaintProductOutput> getDetailComplaintProductOutputHashSer() {
    return detailComplaintProductOutputHashSer;
  }

  public void setDetailComplaintProductOutputHashSer(Set<ComplaintProductOutput> detailComplaintProductOutputHashSer) {
    this.detailComplaintProductOutputHashSer = detailComplaintProductOutputHashSer;
  }

  //fuzhu
  public String getListAttachment() {
    return listAttachment;
  }

  public void setListAttachment(String listAttachment) {
    this.listAttachment = listAttachment;
  }

  public String getListProductOutput() {
    return listProductOutput;
  }

  public void setListProductOutput(String listProductOutput) {
    this.listProductOutput = listProductOutput;
  }

  public String getInnerSn() {
    return innerSn;
  }

  public void setInnerSn(String innerSn) {
    this.innerSn = innerSn;
  }

  public String getInnerName() {
    return innerName;
  }

  public void setInnerName(String innerName) {
    this.innerName = innerName;
  }

  public String getOutersn() {
    return outersn;
  }

  public void setOutersn(String outersn) {
    this.outersn = outersn;
  }

  public String getOuterName() {
    return outerName;
  }

  public void setOuterName(String outerName) {
    this.outerName = outerName;
  }


  //shujuku

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getComplaint() {
    return complaint;
  }

  public void setComplaint(Integer complaint) {
    this.complaint = complaint;
  }

  public String getProblem() {
    return problem;
  }

  public void setProblem(String problem) {
    this.problem = problem;
  }

  public String getConsequence() {
    return consequence;
  }

  public void setConsequence(String consequence) {
    this.consequence = consequence;
  }

  public String getAppeal() {
    return appeal;
  }

  public void setAppeal(String appeal) {
    this.appeal = appeal;
  }

  public Integer getOrders() {
    return orders;
  }

  public void setOrders(Integer orders) {
    this.orders = orders;
  }

  public Integer getCustomerProduct() {
    return customerProduct;
  }

  public void setCustomerProduct(Integer customerProduct) {
    this.customerProduct = customerProduct;
  }

  public String getOuterSn() {
    return outerSn;
  }

  public void setOuterSn(String outerSn) {
    this.outerSn = outerSn;
  }

  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public BigDecimal getPercent() {
    return percent;
  }

  public void setPercent(BigDecimal percent) {
    this.percent = percent;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Integer getPeroid() {
    return peroid;
  }

  public void setPeroid(Integer peroid) {
    this.peroid = peroid;
  }
}
