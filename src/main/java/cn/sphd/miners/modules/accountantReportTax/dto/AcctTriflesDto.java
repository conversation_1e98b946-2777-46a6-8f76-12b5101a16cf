package cn.sphd.miners.modules.accountantReportTax.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 杂项DTO
 * */
public class AcctTriflesDto implements Serializable {

    private Integer triflesId;
    private Integer triflesHisId;
    private Integer detailId;//报税ID
    private String name;
    private String startPeriod;//申报的开始日期
    private String endPeriod;//申报的结束日期
    private Date latestDeclare;//最后编辑的日期
    private Date factReportDate;//实际申报的日期
    private Date createDate;//数据产生时间
    private String createName;//创建人

    public AcctTriflesDto(){}

    public AcctTriflesDto(Integer triflesId,String name,String startPeriod,String endPeriod,Date latestDeclare,Date factReportDate){
        this.triflesId = triflesId;
        this.name = name;
        this.startPeriod = startPeriod;
        this.endPeriod = endPeriod;
        this.latestDeclare = latestDeclare;
        this.factReportDate = factReportDate;

    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Integer getTriflesHisId() {
        return triflesHisId;
    }

    public void setTriflesHisId(Integer triflesHisId) {
        this.triflesHisId = triflesHisId;
    }

    public Integer getTriflesId() {
        return triflesId;
    }

    public void setTriflesId(Integer triflesId) {
        this.triflesId = triflesId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public Date getLatestDeclare() {
        return latestDeclare;
    }

    public void setLatestDeclare(Date latestDeclare) {
        this.latestDeclare = latestDeclare;
    }

    public Date getFactReportDate() {
        return factReportDate;
    }

    public void setFactReportDate(Date factReportDate) {
        this.factReportDate = factReportDate;
    }
}
