package cn.sphd.miners.modules.complaint.dto;

import java.math.BigDecimal;

/**
 * Created by 朱思旭 on 2021/10/8.
 */
public class ComplaintTH {

    private BigDecimal numComplaint;
    private String huanbiComplaint;
    private String tongbiComplaint;

    public BigDecimal getNumComplaint() {
        return numComplaint;
    }

    public void setNumComplaint(BigDecimal numComplaint) {
        this.numComplaint = numComplaint;
    }

    public String getHuanbiComplaint() {
        return huanbiComplaint;
    }

    public void setHuanbiComplaint(String huanbiComplaint) {
        this.huanbiComplaint = huanbiComplaint;
    }

    public String getTongbiComplaint() {
        return tongbiComplaint;
    }

    public void setTongbiComplaint(String tongbiComplaint) {
        this.tongbiComplaint = tongbiComplaint;
    }

    public ComplaintTH(){

    }

    public ComplaintTH(BigDecimal numComplaint, String huanbiComplaint, String tongbiComplaint) {
        this.numComplaint = numComplaint;
        this.huanbiComplaint = huanbiComplaint;
        this.tongbiComplaint = tongbiComplaint;
    }
}
