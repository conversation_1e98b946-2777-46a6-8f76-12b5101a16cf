<template>
  <div id="potentialApplyList" v-loading.fullscreen.lock="loading" class="potential">
    <TY_NavTop title="潜在客户申请" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false'>
        <div class="ui-cells">
          <a class="ui-col" v-on:click="jumpDetail(item.id, 1)" v-for="(item, index) in untreatList" v-bind:key="index">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div>{{item.fullName}}</div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
            <div class="ui-col__ft">
              <div class="createDetail">录入者 {{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</div>
            </div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待正式合作" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-col" v-for="(item,index) in finishList" :key="index" @click="jumpDetail(item.id, 2)">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div>{{item.fullName}}</div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
            <div class="ui-col__ft">
              <div class="createDetail">录入者 {{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</div>
            </div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<script>
import { formatDate } from '@/js/common'
export default {
  name: 'potentialCustomerApply',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      tabValue: '1',
      toHandleCount: '',
      untreatList: [],
      finishList: [],
      listenersUid: []
    }
  },
  created () {
    let oid = this.sphdSocket.user.oid
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    let _this = this
    _this.tabValue = this.$store.getters.potentialNav
    this.listenersUid = [
      this.sphdSocket.subscribe('potentialHandleList', function (data) {
        console.log('我的审批potentialHandleList：' + data)
        let resData = JSON.parse(data)
        this.toHandleCount = resData.list.length
        _this.untreatList = resData.list
        _this.loading = false
      }),
      this.sphdSocket.subscribe('potentialAcceptListForCreator', function (data) {
        console.log('我的审批111potentialAcceptListForCreator：' + data)
        let resData = JSON.parse(data)
        _this.finishList = resData.list
      }),
      this.sphdSocket.subscribe('potentialHandleList', function (data) {
        console.log('待处理增加一条减少一条:' + data)
        let reqData = JSON.parse(data)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          console.log('增加一条')
          newDate = reqData.customer
          _this.untreatList.push(newDate)
        } else if (update < 0) { // 减少一条
          console.log('减少一条')
          let deletId = reqData.customer.id
          let index = _this.untreatList.findIndex(item => Number(deletId) === Number(item.id))
          if (index !== undefined) {
            _this.untreatList.splice(index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('potentialAcceptListForCreator', function (data) {
        console.log('待正式合作增加一条减少一条:' + data)
        let reqData = JSON.parse(data)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          console.log('增加一条')
          newDate = reqData.customer
          _this.finishList.push(newDate)
        } else if (update < 0) { // 减少一条
          console.log('减少一条')
          let deletId = reqData.customer.id
          let indx = _this.finishList.findIndex(item => Number(deletId) === Number(item.id))
          if (indx !== undefined) {
            _this.finishList.splice(indx, 1)
          }
        }
      }, null, 'user')
    ]
    this.sphdSocket.send('potentialHandleList', { 'oid': oid, userId: userId, session: session })
    this.sphdSocket.send('potentialAcceptListForCreator', { 'oid': oid, userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jumpDetail: function (id, tab) {
      this.$store.dispatch('setPotentialNav', tab)
      this.$router.push({
        path: `/potentialDetail/${id}`
      })
    },
    screen: function () {
      this.$router.push({
        path: `/applyPotentialQuery/${1}`
      })
    }
  }
}
</script>
