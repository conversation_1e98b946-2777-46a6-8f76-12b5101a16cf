package cn.sphd.miners.modules.system.service;

import cn.sphd.miners.modules.system.entity.OrgPopedom;
import cn.sphd.miners.modules.system.entity.OrgPopedomHistory;
import cn.sphd.miners.modules.system.entity.Organization;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by Administrator on 2017/10/26.
 */
public interface OrgPopedomService {

    void saveOrgPopedom(OrgPopedom orgPopedom);

    void deleteOrgPopedom(OrgPopedom orgPopedom);

    void saveOrgPopedom(List<OrgPopedom> orgPopedomList,Integer oid,String updateUserName);

    void deleteOrgPopedomAllByOid(Set<OrgPopedom> orgPopedoms);

    boolean hasSalarytreasure(Integer oid);

    List<Organization> getOrgPopedomByType(String type);//1- 薪资宝 2-会计

    boolean getOrgPopedomByMid(Integer oid,String mid);

    OrgPopedom getOrgPopedomByOidMid(Integer oid,String mid);

    List<Organization> getOrgPopedomByMid(String mid);
    Organization getOrgPopedomByMid(String mid, Integer index);

    List<OrgPopedom> getOrgPopedomListByOid(Integer oid);

    List<OrgPopedom> getOrgPopedomListNotInCode(Integer oid,String code);

    List<String> getOrgPopedomListInCode(Integer oid,String code);

    List<String> getOrgMidListByOid(Integer oid);

    void  updateOrgPopedom(Integer oid,String midRenames,String userName);

    void  saveOrgPopedomHistory(OrgPopedom orgPopedom,Integer versionNo,String userName,String type,Integer oid);

    List<OrgPopedomHistory> getOrgPopedomHistoriesByOid(Integer oid);

    List<OrgPopedomHistory> getOrgPopedomHistoriesByOidMid(Integer oid,String mid);

    List<OrgPopedom> getOrgPopedomNewNameListByOid(Integer oid);
}
