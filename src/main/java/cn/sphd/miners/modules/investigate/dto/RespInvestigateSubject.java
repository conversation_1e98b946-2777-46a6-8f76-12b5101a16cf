package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.modules.investigate.entity.InvestigateSubject;

import java.util.List;

public class RespInvestigateSubject extends InvestigateSubject {
    private Integer publishEnabled;
    private Integer publishId;
    private Integer objective;
    private Integer status;
    private String orgName;

    private Integer modifyLimit;

    public Integer getModifyLimit() {
        return modifyLimit;
    }

    public void setModifyLimit(Integer modifyLimit) {
        this.modifyLimit = modifyLimit;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    private List<RespInvestigateSubjectTag> respInvestigateSubjectTagList;

    public Integer getPublishEnabled() {
        return publishEnabled;
    }

    public void setPublishEnabled(Integer publishEnabled) {
        this.publishEnabled = publishEnabled;
    }

    public Integer getPublishId() {
        return publishId;
    }

    public void setPublishId(Integer publishId) {
        this.publishId = publishId;
    }

    public Integer getObjective() {
        return objective;
    }

    public void setObjective(Integer objective) {
        this.objective = objective;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<RespInvestigateSubjectTag> getRespInvestigateSubjectTagList() {
        return respInvestigateSubjectTagList;
    }

    public void setRespInvestigateSubjectTagList(List<RespInvestigateSubjectTag> respInvestigateSubjectTagList) {
        this.respInvestigateSubjectTagList = respInvestigateSubjectTagList;
    }
}
