<template>
	<div class="uni-container">
			<div class="wating icon-life">  </div>
			<div class="watTxt">敬请期待</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
		onLoad() {

		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>

<style lang="scss">
.icon-life{ background: url("../../../../../liveMiniApp/src/assets/img/tabbar/life.png");   background-size: 200px; }
	
</style>
