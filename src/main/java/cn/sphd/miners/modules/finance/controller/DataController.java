package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.FinanceUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.accountant.service.ReimburseRelevanceService;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.*;
import cn.sphd.miners.modules.finance.service.impl.FinanceBillUsing;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import io.netty.util.internal.StringUtil;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/12/16.
 *数据查看、数据录入、加入与会计模块相关的两个方法
 */
@Controller
@RequestMapping("/data")
public class DataController {
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    DataService dataService;
    @Autowired
    OrgService orgService;
    @Autowired
    AccountService accountService;
    @Autowired
    FinanceReturnService financeReturnService;
    @Autowired
    FinanceChequeService financeChequeService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    ReimburseRelevanceService reimburseRelevanceService;
    @Autowired
    UploadService uploadService;
    @Autowired
    AcctReportTaxService acctReportTaxService;
    /**
     * <AUTHOR>
     * @date 2016                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           /12/21 16:40
     * 查看某一条数据的详细信息(查看功能，与下面getDataDetail.do的接口相同，就返回值不同，此接口以后少用)
     * 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     */
    @ResponseBody
    @RequestMapping("/getOneDetail.do")
    public void getOneDetail(HttpServletResponse response, Integer detailId) throws IOException {
        Map<String, Object> map = new HashMap<>();
         AccountDetail accountDetail = dataService.getOneDetail(detailId);
        if (accountDetail != null) {
            if ("1".equals(accountDetail.getMethod()) || "5".equals(accountDetail.getMethod())) {  //为现金或者银行转账时
                if (accountDetail.getPersonnelReimburse() != null) {
                    PersonnelReimburse personnelReimburse = accountDetail.getPersonnelReimburse();  // 报销数据
                    personnelReimburse.setDetailId(detailId);
                    personnelReimburseService.updatePersonnelReimburse(personnelReimburse);
                    map.put("kind", 1);
                    map.put("content", personnelReimburse); // 来源于报销数据的现金/银行转账信息
                } else {
                    if ("3".equals(accountDetail.getType())) {
                        map.put("kind", 2);
                        map.put("content", accountDetail);  // 项目是收入的现金/银行转账信息
                    } else if ("4".equals(accountDetail.getType())) {
                        FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                        if (financeAccountBill != null) {
//                            accountDetail.setBillCat(financeAccountBill.getBillCat());
//                            accountDetail.setBillPeriod(financeAccountBill.getBillPeriod());
                            accountDetail.setBillQuantity(financeAccountBill.getBillQuantity());
                        }
                        if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())) {  //税款的数据
                            Map<String, Object> map1 = acctReportTaxService.getById(accountDetail.getBusiness());
                            accountDetail.setTaxName((String) map1.get("taxName"));  //税种名称
                            accountDetail.setReport((Integer) map1.get("report"));  //报表id
                            accountDetail.setTaxPeriodId((Integer) map1.get("periodId"));  //申报记录id
                            accountDetail.setBeginDate(NewDateUtils.dateFromString((String) map1.get("periodBegin"),"yyyyMMdd") ); //税款的申报记录开始时间
                            accountDetail.setEndDate(NewDateUtils.dateFromString((String) map1.get("periodEnd"),"yyyyMMdd")); //税款的申报记录结束时间
                        }
                        map.put("kind", 3);
                        map.put("content", accountDetail);  // 项目是支出的现金/银行转账信息
                    }
                }
                List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(accountDetail.getOrg_(),accountDetail.getId(),null);
                map.put("financeAccountBillImages",financeAccountBillImages);  //图片信息
            } else if ("2".equals(accountDetail.getMethod()) || "3".equals(accountDetail.getMethod()) || "4".equals(accountDetail.getMethod())) { //2-现金支票 3-转账支票  4-承兑汇票
                FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                if (financeAccountBill!=null){
                    if (financeAccountBill.getCheque() != null) {
                        FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                        FinanceAccount financeAccount = financeChequeDetail.getAccountId();
                        if (financeAccount != null && "基本户".equals(financeAccount.getOperation())) {
                            financeChequeDetail.setIsBasic(financeAccount.getOperation());
                        }
                        financeChequeDetail.setDateType(accountDetail.getType()); //1-初始金额冲减,2-转帐交易,3-收入,4-支出
//                        financeChequeDetail.setBillCat(financeAccountBill.getBillCat());
//                        financeChequeDetail.setBillPeriod(financeAccountBill.getBillPeriod());
                        financeChequeDetail.setBillQuantity(financeAccountBill.getBillQuantity());
                        financeChequeDetail.setBillAmount(financeAccountBill.getBillAmount());  //票面金额
                        financeChequeDetail.setSubType(financeAccountBill.getSubType());
                        financeChequeDetail.setBillDate(financeAccountBill.getBillDate());
//                        financeChequeDetail.setFactDate(financeAccountBill.getFactDate());
                        map.put("kind", 4);
                        map.put("content", financeChequeDetail); // 现金支票/内部支票的信息
                    } else {
                        FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();
                        financeReturn.setBillType(financeAccountBill.getType());
                        financeReturn.setSaveBankId(financeReturn.getAccountId().toString());
                        financeReturn.setBillAmount(financeAccountBill.getBillAmount());
                        financeReturn.setBillQuantity(financeAccountBill.getBillQuantity());
                        financeReturn.setSubType(financeAccountBill.getSubType());
                        financeReturn.setBillDate(financeAccountBill.getBillDate());
//                        financeReturn.setFactDate(financeAccountBill.getFactDate());
                        map.put("kind", 5);
                        map.put("content", financeReturn);  //承兑汇票/外部支票的信息
                    }

                    List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(accountDetail.getOrg_(),null,financeAccountBill.getId());
                    map.put("financeAccountBillImages",financeAccountBillImages);  //图片信息
                }
                map.put("financeAccountBill", financeAccountBill); // 现金支票/内部支票和承兑汇票/外部支票的信息，还有factDate【收款单位的信息】

            } else if ("7".equals(accountDetail.getMethod())) {
                FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                if (financeAccountBill != null) {
                    FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                    if (financeChequeDetail != null) {
                        accountDetail.setChequeId(financeChequeDetail.getId());
                        accountDetail.setChequeNo(financeChequeDetail.getChequeNo());
                        FinanceAccount financeAccount = financeChequeDetail.getAccountId();
                        if (financeAccount != null) {
                            accountDetail.setBankBase(financeAccount.getOperation());
                        }
                        map.put("kind", 8);
                        map.put("content", accountDetail);  // 项目是内部非支出性转账的取现金中的基本户
                    }
                }else {
                    map.put("kind", 7);
                    map.put("content", accountDetail);  // 项目是内部非支出性转账的取现金中的非基本户
                }
            } else {
                map.put("kind", 6);
                map.put("content", accountDetail);  // 项目是内部非支出性转账的除去取现金的数据
            }
        }
        ObjectToJson.objectToJson1(map, new String[]{"org", "accountId", "accountDetailHistories", "billDetail", "personnelReimburse",
                "user", "reimburset", "cheque", "financeReturn",
                "financeAccountBillHistoryHashSet", "accountDetailHashSet",
                "chequeReg", "financeAccountBillHashSet", "approvalFlow", "reimburse"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           /12/21 16:40
     * 查看某一条数据的详细信息(查看功能,与上面getOneDetail.do的接口相同，就返回值不同，以后多用此接口)
     * 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     */
    @ResponseBody
    @RequestMapping("/getDataDetail.do")
    public JsonResult getOneDetail(Integer detailId) {
        Map<String, Object> map = new HashMap<>();
        AccountDetail accountDetail = dataService.getOneDetail(detailId);
        if (accountDetail != null) {
            if ("1".equals(accountDetail.getMethod()) || "5".equals(accountDetail.getMethod())) {  //为现金或者银行转账时
                if (accountDetail.getPersonnelReimburse() != null) {
                    PersonnelReimburse personnelReimburse = accountDetail.getPersonnelReimburse();  // 报销数据
                    personnelReimburse.setDetailId(detailId);
                    personnelReimburseService.updatePersonnelReimburse(personnelReimburse);
                    map.put("kind", 1);
                    map.put("content", personnelReimburse); // 来源于报销数据的现金/银行转账信息
                } else {
                    if ("3".equals(accountDetail.getType())) {
                        map.put("kind", 2);
                        map.put("content", accountDetail);  // 项目是收入的现金/银行转账信息
                    } else if ("4".equals(accountDetail.getType())) {
                        FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                        if (financeAccountBill != null) {
//                            accountDetail.setBillCat(financeAccountBill.getBillCat());
//                            accountDetail.setBillPeriod(financeAccountBill.getBillPeriod());
                            accountDetail.setBillQuantity(financeAccountBill.getBillQuantity());
                        }
                        if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())) {  //税款的数据
                            Map<String, Object> map1 = acctReportTaxService.getById(accountDetail.getBusiness());
                            accountDetail.setTaxName((String) map1.get("taxName"));  //税种名称
                            accountDetail.setReport((Integer) map1.get("report"));  //报表id
                            accountDetail.setTaxPeriodId((Integer) map1.get("periodId"));  //申报记录id
                            accountDetail.setBeginDate(NewDateUtils.dateFromString((String) map1.get("periodBegin"),"yyyyMMdd") ); //税款的申报记录开始时间
                            accountDetail.setEndDate(NewDateUtils.dateFromString((String) map1.get("periodEnd"),"yyyyMMdd")); //税款的申报记录结束时间
                        }
                        map.put("kind", 3);
                        map.put("content", accountDetail);  // 项目是支出的现金/银行转账信息
                    }
                }
                List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(accountDetail.getOrg_(),accountDetail.getId(),null);
                map.put("financeAccountBillImages",financeAccountBillImages);  //图片信息
            } else if ("2".equals(accountDetail.getMethod()) || "3".equals(accountDetail.getMethod()) || "4".equals(accountDetail.getMethod())) { //2-现金支票 3-转账支票  4-承兑汇票
                FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                if (financeAccountBill!=null){
                    if (financeAccountBill.getCheque() != null) {
                        FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                        FinanceAccount financeAccount = financeChequeDetail.getAccountId();
                        if (financeAccount != null && "基本户".equals(financeAccount.getOperation())) {
                            financeChequeDetail.setIsBasic(financeAccount.getOperation());
                        }
                        financeChequeDetail.setDateType(accountDetail.getType()); //1-初始金额冲减,2-转帐交易,3-收入,4-支出
//                        financeChequeDetail.setBillCat(financeAccountBill.getBillCat());
//                        financeChequeDetail.setBillPeriod(financeAccountBill.getBillPeriod());
                        financeChequeDetail.setBillQuantity(financeAccountBill.getBillQuantity());
                        financeChequeDetail.setBillAmount(financeAccountBill.getBillAmount());  //票面金额
                        financeChequeDetail.setSubType(financeAccountBill.getSubType());
                        financeChequeDetail.setBillDate(financeAccountBill.getBillDate());
//                        financeChequeDetail.setFactDate(financeAccountBill.getFactDate());
                        map.put("kind", 4);
                        map.put("content", financeChequeDetail); // 现金支票/内部支票的信息
                    } else {
                        FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();
                        financeReturn.setBillType(financeAccountBill.getType());
                        financeReturn.setSaveBankId(financeReturn.getAccountId().toString());
                        financeReturn.setBillAmount(financeAccountBill.getBillAmount());
                        financeReturn.setBillQuantity(financeAccountBill.getBillQuantity());
                        financeReturn.setSubType(financeAccountBill.getSubType());
                        financeReturn.setBillDate(financeAccountBill.getBillDate());
//                        financeReturn.setFactDate(financeAccountBill.getFactDate());
                        map.put("kind", 5);
                        map.put("content", financeReturn);  //承兑汇票/外部支票的信息
                    }

                    List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(accountDetail.getOrg_(),null,financeAccountBill.getId());
                    map.put("financeAccountBillImages",financeAccountBillImages);  //图片信息
                }
                map.put("financeAccountBill", financeAccountBill); // 现金支票/内部支票和承兑汇票/外部支票的信息，还有factDate【收款单位的信息】

            } else if ("7".equals(accountDetail.getMethod())) {
                FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                if (financeAccountBill != null) {
                    FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                    if (financeChequeDetail != null) {
                        accountDetail.setChequeId(financeChequeDetail.getId());
                        accountDetail.setChequeNo(financeChequeDetail.getChequeNo());
                        FinanceAccount financeAccount = financeChequeDetail.getAccountId();
                        if (financeAccount != null) {
                            accountDetail.setBankBase(financeAccount.getOperation());
                        }
                        map.put("kind", 8);
                        map.put("content", accountDetail);  // 项目是内部非支出性转账的取现金中的基本户
                    }
                }else {
                    map.put("kind", 7);
                    map.put("content", accountDetail);  // 项目是内部非支出性转账的取现金中的非基本户
                }
            } else {
                map.put("kind", 6);
                map.put("content", accountDetail);  // 项目是内部非支出性转账的除去取现金的数据
            }
        }
//        ObjectToJson.objectToJson1(map, new String[]{"org", "accountId", "accountDetailHistories", "billDetail", "personnelReimburse",
//                "user", "reimburset", "cheque", "financeReturn",
//                "financeAccountBillHistoryHashSet", "accountDetailHashSet",
//                "chequeReg", "financeAccountBillHashSet", "approvalFlow", "reimburse"}, response);
        return new JsonResult(1,map);
    }
    //-----------------------------以下为数据录入------------------------------------

    /**
     * <AUTHOR>
     * @Date 2016/12/30 15:10
     * 收入
     */
    @ResponseBody
    @RequestMapping("/credit.do")
    public void credit(User user,String method, Double money,Double billAmount, Integer financeAccountId, String genre, String summary, String purpose, String auditorName, String oppositeCorp, String receiveAccountDate, String receiveDate, String checkNumber, String expireDate, String originalCorp, String bankName, String memo, String categoryDesc, HttpServletResponse response) throws ParseException, IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Organization organization = user.getOrganization();
        //Debug start ********
        Map<String, String> debug = new HashMap<>();
        debug.put("method", method);
        //Debug finish ********
        if ("1".equals(method)) {
            //单纯现金
            FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(organization.getId(), 1);//备用金
            AccountPeriod accountPeriod = accountService.getPeroidByType(financeAccount.getOrg().getId(), financeAccount.getAccountType(), financeAccount.getId(), 1);

            AccountDetail accountDetail = new AccountDetail();

            if (accountPeriod == null) {
                FinanceUtils.addPeroid(financeAccount.getId(),user, accountService);
            }
            accountPeriod = accountService.getPeroidByType(financeAccount.getOrg().getId(), financeAccount.getAccountType(), financeAccount.getId(), 1);//再查一遍

            AccountPeriod ap = accountService.getPeroidByType(financeAccount.getOrg().getId(), financeAccount.getAccountType(), financeAccount.getId(), 2);//找到日结
            //Debug start ********
            debug.put("financeAccount0", JSON.toJSONString(financeAccount));
            debug.put("accountPeriodDay0", JSON.toJSONString(ap));
            debug.put("accountPeriodMonth0", JSON.toJSONString(accountPeriod));
            //Debug finish ********
            accountDetail.setCredit(new BigDecimal(money));

            if ("0".equals(financeAccount.getAccountStatus()) || 0 == financeAccount.getAccountStatus()) {
                map.put("status", 2);//此账户已被冻结，不允许操作
            }

            if (accountPeriod.getBuildState() != null) {
                if (accountPeriod.getBuildState().equals("已生成"))
                    map.put("status", 3);//已生成
            }

            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(organization);
            accountDetail.setCategoryDesc(categoryDesc);
            accountDetail.setAccount(accountPeriod.getId().toString());
            accountDetail.setAuditDate(new Date());
            accountDetail.setAuditorName(auditorName);//经手人
            accountDetail.setMethod(method);//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
            accountDetail.setMemo(memo);
            accountDetail.setGenre(genre);
            accountDetail.setSummary(summary);
            accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
            accountDetail.setPurpose(purpose);//用途
            accountDetail.setOppositeCorp(oppositeCorp);//付款单位
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setSource("1");
            if (financeAccount.getAccount()!=null){
                accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            }else {
                accountDetail.setAccountBank(financeAccount.getBankName());
            }
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setBillDate(new Date());

//            BigDecimal credit = accountPeriod.getCredit().add(accountDetail.getCredit());
//            BigDecimal balance = accountPeriod.getBalance().add(accountDetail.getCredit());
            financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriod.setCredit(accountPeriod.getCredit().add(accountDetail.getCredit()));
            accountPeriod.setBalance(accountPeriod.getBalance().add(accountDetail.getCredit()));

            BigDecimal credit = ap.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : ap.getCredit().add(accountDetail.getCredit());
            BigDecimal balance = ap.getBalance().add(accountDetail.getCredit());
            ap.setCredit(credit);
            ap.setBalance(balance);

            accountService.updateAccountPeroid(accountPeriod);//月结
            accountService.updateAccountPeroid(ap);//日结
            accountService.updateFinanceAccount(financeAccount);//账户
            accountService.saveAccountDetail(accountDetail);//明细
            map.put("status", 1);//成功

            //Debug start ********
            debug.put("money", money.toString());
            debug.put("balance", balance.toString());
            debug.put("accountId", financeAccount.getId().toString());
            debug.put("financeAccount", JSON.toJSONString(financeAccount));
            debug.put("accountDetail", JSON.toJSONString(accountDetail));
            debug.put("accountPeriodDay", JSON.toJSONString(ap));
            debug.put("accountPeriodMonth", JSON.toJSONString(accountPeriod));
            //Debug finish ********
        } else if ("3".equals(method) || "4".equals(method)) {

            //回款票据 的转账支票
            FinanceReturn fr = new FinanceReturn();
            fr.setReturnNo(checkNumber);//支票号码
            if ("3".equals(method)) {
                fr.setType(1);//1-转账支票 2-承兑汇票
            } else {
                fr.setType(2);//1-转账支票 2-承兑汇票
            }
            fr.setCategory(genre);//1-贷款,2-借款,3-投资款,4-废品,5-其他
            fr.setCategoryDesc(categoryDesc);//其他时候的说明
            fr.setAmount(new BigDecimal(money));//金额
            fr.setBillAmount(new BigDecimal(billAmount));//票面金额
            fr.setSummary(summary);//摘要
            fr.setPurpose(purpose);//用途
            fr.setOperatorName(auditorName);//经手人
            fr.setPayer(oppositeCorp);//付款单位
            fr.setOriginalCorp(originalCorp);//出具票据单位
            fr.setBankName(bankName);//出具票据银行
            fr.setExpireDate(sdf.parse(expireDate));//发票到期日期
            fr.setReceiveDate(sdf.parse(receiveDate));//收到票据日期
            fr.setMemo(memo);//备注
            fr.setState("1");//1-有效,2-存入银行,3-作废
            fr.setCreateDate(new Date());
            fr.setCreateName(user.getUserName());
            fr.setCreator(user.getUserID());
            fr.setOrg(organization);
            dataService.saveFinanceReturn(fr);

            //支票明细
            FinanceAccountBill f = new FinanceAccountBill();
            f.setType("1");//1-收入，2-支出
            f.setAmount(new BigDecimal(money));//金额
            f.setBillAmount(new BigDecimal(billAmount));  //票面金额
            f.setSummary(summary);//摘要
            f.setPurpose(purpose);//用途
//            f.setBillQuantity();//票据数量
//            f.setBillPeriod();//是否本月票据
            f.setBillNo(checkNumber);//支票号码
            f.setFinanceReturn(fr);//回款票据外键
            f.setOrg(organization);//机构
            f.setOperatorName(auditorName);//经手人
            f.setMemo(memo);//备注
            f.setAccountantStatus("1");
            f.setSource("1");
            f.setBillDate(new Date());
            f.setCreateDate(new Date());
            f.setCreateName(user.getUserName());
            f.setCreator(user.getUserID());
            dataService.saveFinanceAccountBill(f);

            map.put("status", 1);//成功

            //Debug start ********
            debug.put("money", money.toString());
            debug.put("financeReturn", JSON.toJSONString(fr));
            debug.put("financeAccountBill", JSON.toJSONString(f));
            //Debug finish ********

        } else if ("5".equals(method)) {
            FinanceAccount financeAccount = accountService.getFinanceAccountById(financeAccountId);
            AccountPeriod accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());

            AccountDetail accountDetail = new AccountDetail();

            if (accountPeriod == null) {
                FinanceUtils.addPeroid(financeAccount.getId(),user, accountService);
            }
            accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());//再查一遍

            AccountPeriod ap = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

            //Debug start ********
            debug.put("financeAccount0", JSON.toJSONString(financeAccount));
            debug.put("accountPeriodDay0", JSON.toJSONString(ap));
            debug.put("accountPeriodMonth0", JSON.toJSONString(accountPeriod));
            //Debug finish ********

            accountDetail.setCredit(new BigDecimal(money));

            if ("0".equals(financeAccount.getAccountStatus()) || 0 == financeAccount.getAccountStatus()) {
                map.put("status", 2);//此账户已被冻结，不允许操作
            }

            if (accountPeriod.getBuildState() != null) {
                if (accountPeriod.getBuildState().equals("已生成"))
                    map.put("status", 3);//已生成
            }

            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(organization);
            accountDetail.setAccount(accountPeriod.getId().toString());
            accountDetail.setAuditDate(new Date());
            accountDetail.setCategoryDesc(categoryDesc);
            accountDetail.setAuditorName(auditorName);
            accountDetail.setGenre(genre);  //类别
            accountDetail.setMethod(method);
            accountDetail.setMemo(memo);
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(purpose);//用途
            accountDetail.setOppositeCorp(oppositeCorp);//收款单位
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setReceiveAccountDate(sdf.parse(receiveAccountDate));//到账日期
            accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
            accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setSource("1");
            accountDetail.setBillDate(new Date());

//            BigDecimal credit = accountPeriod.getCredit().add(accountDetail.getCredit());
//            BigDecimal balance = accountPeriod.getBalance().add(accountDetail.getCredit());
            financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriod.setCredit(accountPeriod.getCredit().add(accountDetail.getCredit()));
            accountPeriod.setBalance(accountPeriod.getBalance().add(accountDetail.getCredit()));

            BigDecimal credit = ap.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : ap.getCredit().add(accountDetail.getCredit());
            BigDecimal balance = ap.getBalance().add(accountDetail.getCredit());
            ap.setCredit(credit);
            ap.setBalance(balance);

            accountService.updateAccountPeroid(accountPeriod);//月结
            accountService.updateAccountPeroid(ap);//日结
            accountService.updateFinanceAccount(financeAccount);//账户
            accountService.saveAccountDetail(accountDetail);//明细

            map.put("status", 1);//成功

            //Debug start ********
            debug.put("money", money.toString());
            debug.put("balance", balance.toString());
            debug.put("accountId", financeAccountId.toString());
            debug.put("financeAccount", JSON.toJSONString(financeAccount));
            debug.put("accountDetail", JSON.toJSONString(accountDetail));
            debug.put("accountPeriodDay", JSON.toJSONString(ap));
            debug.put("accountPeriodMonth", JSON.toJSONString(accountPeriod));
            //Debug finish ********

        } else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    /**
     * <AUTHOR>
     * @Date 2017/1/3 13:17
     * 2021/11/24 1.179财务优化2101--lyx改
     * 支出
     * subType 帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
     * stakeholderCategory(类别:1-供应商,2-员工,3-自行录入) stakeholder(干系人ID) factUser(实际用户(同事userid)) billDate(票据日期) Date factDate(实际(付款/支出)日期'，实际缴纳日期(1.207加的))  imgPaths(图片路径，多个)
     * auditorName(经手人) oppositeCorp（收款单位）
     */
    @ResponseBody
    @RequestMapping("/debit.do")
    public JsonResult debit(User user,String method, Double money,Double billAmount, String withinOrAbroad, Integer checkId, Integer financeAccountId, String summary,
                      String purpose, String auditorName, String oppositeCorp, String receiveDate, String expireDate, String memo, String operator, String receiver,
                      Integer billQuantity, String subType,String stakeholderCategory,Integer stakeholder, String billDate,String factDate,String imgPaths) throws ParseException{
        Map<String, Object> map = new HashMap<String, Object>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Organization organization = user.getOrganization();
        if ("1".equals(method)) {
            //单纯现金
            FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(user.getOid(), 1);//备用金
            AccountPeriod accountPeriod = accountService.getAccountPeriodByMonth( financeAccount.getId(), new Date());

            //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
            //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
            BigDecimal me = new BigDecimal(money.toString());
            BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
            BigDecimal jieguo = ba.subtract(me);

            if (jieguo.doubleValue() < 0) {
                map.put("status", 4);//余额不足
            } else if (0 == financeAccount.getAccountStatus()) {
                map.put("status", 2);//此账户已被冻结，不允许操作
            } else {
                AccountDetail accountDetail = new AccountDetail();

                if (accountPeriod == null) {
                    FinanceUtils.addPeroid(financeAccount.getId(), user, accountService);
                }
                accountPeriod = accountService.getAccountPeriodByMonth( financeAccount.getId(), new Date());//再查一遍

                AccountPeriod ap = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

                accountDetail.setDebit(new BigDecimal(money));

                if (accountPeriod.getBuildState() != null) {
                    if (accountPeriod.getBuildState().equals("已生成"))
                        map.put("status", 3);//已生成
                }

                accountDetail.setCreateDate(new Date());
                accountDetail.setCreator(user.getUserID());
                accountDetail.setCreateName(user.getUserName());
                accountDetail.setOrg(organization);
                accountDetail.setAccount(accountPeriod.getId().toString());
                accountDetail.setAuditDate(new Date());
                accountDetail.setAuditorName(auditorName);//经手人
                accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
                accountDetail.setMethod(method);
                accountDetail.setMemo(memo);
                accountDetail.setSummary(summary);
                accountDetail.setPurpose(purpose);//用途
                accountDetail.setOppositeCorp(oppositeCorp);//收款单位
                accountDetail.setFid(financeAccount.getId().toString());
                accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
                accountDetail.setAccountId(financeAccount);//账户外键
                accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                if (financeAccount.getAccount()!=null){
                    accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
                }else {
                    accountDetail.setAccountBank(financeAccount.getBankName());
                }
                accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                accountDetail.setSource("1");
                accountDetail.setSubType(subType); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出
                accountDetail.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
                accountDetail.setStakeholder(stakeholder); //干系人ID(收款单位的id)
                accountDetail.setBillQuantity(billQuantity);  //票据数量
                accountDetail.setBillDate(NewDateUtils.dateFromString(billDate,"yyyy-MM-dd"));  //票据日期
                accountDetail.setFactDate(NewDateUtils.dateFromString(factDate,"yyyy-MM-dd"));  //实际(付款/支出)日期'

                BigDecimal debit = accountPeriod.getDebit().add(accountDetail.getDebit());
                BigDecimal balance = accountPeriod.getBalance().subtract(accountDetail.getDebit());
                financeAccount.setDebit(financeAccount.getDebit().add(accountDetail.getDebit()));//计入账户支出
                financeAccount.setBalance(financeAccount.getBalance().subtract(accountDetail.getDebit()));//从余额减去支出
                accountDetail.setBalance(financeAccount.getBalance());
                accountPeriod.setDebit(debit);
                accountPeriod.setBalance(balance);

//                debit = ap.getDebit().add(accountDetail.getDebit());
//                balance = ap.getBalance().subtract(accountDetail.getDebit());
                ap.setDebit(ap.getDebit().add(accountDetail.getDebit()));
                ap.setBalance(ap.getBalance().subtract(accountDetail.getDebit()));

                accountService.updateAccountPeroid(accountPeriod);//月结
                accountService.updateAccountPeroid(ap);//日结
                accountService.updateFinanceAccount(financeAccount);//账户
                accountService.saveAccountDetail(accountDetail);//明细

                if (StringUtils.isNotEmpty(imgPaths)){  //添加图片
                    JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                    List imgPathList = (List) JSONArray.toCollection(jsonValues);
                    for (int i=0;i<imgPathList.size();i++){
                        FinanceAccountBillImage financeAccountBillImage = dataService.addBillImage(user,accountDetail.getId(),null,"1",imgPathList.get(i).toString(),i+1);

                        FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImage.getId(),FinanceAccountBillImage.class);
                        uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表
                    }
                }
                map.put("status", 1);//成功
            }
        } else if ("3".equals(method)) {
            //3代表转账支票
            //内部or外部   1-内部，0-外部
            if ("1".equals(withinOrAbroad)) {
                //支出的内部银行转账支票
                FinanceAccount financeAccount = accountService.getFinanceAccountById(financeAccountId);

//                BigDecimal jieguo = financeAccount.getBalance().subtract(new BigDecimal(money));
                //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
                //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
                BigDecimal me = new BigDecimal(money.toString());
                BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
                BigDecimal jieguo = ba.subtract(me);

                if (jieguo.doubleValue() < 0) {
                    map.put("status", 4);//余额不足
                } else if (0 == financeAccount.getAccountStatus()) {
                    map.put("status", 2);//此账户已被冻结，不允许操作
                } else {
                    FinanceChequeDetail fc = financeChequeService.getChequeByDetailId(checkId);
                    fc.setSummary(summary);//摘要
                    fc.setPurpose(purpose);//用途
                    fc.setReceiver(receiver);//接收经手人
                    fc.setAmount(new BigDecimal(money));
                    fc.setBillAmount(new BigDecimal(billAmount));  //票面金额
                    fc.setOperator(operator);//支付经手人
                    fc.setFinancialHandling(auditorName);//财务经手人
                    fc.setExpireDate(NewDateUtils.today(sdf.parse(expireDate)));//支票到期日
                    fc.setReceiveDate(NewDateUtils.today(sdf.parse(receiveDate)));//接收日期
                    fc.setMemo(memo);
                    fc.setState("2");//1-未使用,2-已使用,3-作废
                    fc.setReceiveCorp(oppositeCorp);//收款单位
                    fc.setUpdateDate(new Date());
                    fc.setUpdateName(user.getUserName());
                    fc.setUpdator(user.getUserID());
                    financeChequeService.updateCashChequeState(fc);

                    //支票明细
                    FinanceAccountBill f = new FinanceAccountBill();
                    f.setOperatorName(auditorName);//经手人
                    f.setCreateDate(new Date());
                    f.setCreateName(user.getUserName());
                    f.setCreator(user.getUserID());
                    f.setType("2");//1-收入，2-支出
                    f.setAmount(fc.getAmount());//金额
                    f.setBillAmount(new BigDecimal(billAmount));  //票面金额
                    f.setSummary(summary);//摘要
                    f.setPurpose(purpose);//用途
                    f.setBillQuantity(billQuantity);//票据数量
//                    f.setBillPeriod(billPeriod);//是否本月票据
//                    f.setBillCat(billCatName);//票据种类
                    f.setOppositeCorp(oppositeCorp);//收款单位
                    f.setBillNo(fc.getChequeNo());//发票号码
                    f.setCheque(fc);//转账支票外键
                    f.setMemo(memo);
                    f.setOrg(organization);
                    f.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                    f.setSource("1");
                    f.setSubType(subType); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
                    f.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
                    f.setStakeholder(stakeholder); //干系人ID(收款单位的id)
                    f.setBillDate(NewDateUtils.dateFromString(billDate,"yyyy-MM-dd"));  //票据日期
                    f.setFactDate(NewDateUtils.dateFromString(factDate,"yyyy-MM-dd"));  //实际(付款/支出)日期'
                    dataService.saveFinanceAccountBill(f);

                    AccountPeriod accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());
                    AccountDetail accountDetail = new AccountDetail();
                    if (accountPeriod == null) {
                        FinanceUtils.addPeroid(financeAccount.getId(), user, accountService);
                    }
                    accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());//再查一遍
                    AccountPeriod ap = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

                    if (accountPeriod.getBuildState() != null) {
                        if (accountPeriod.getBuildState().equals("已生成"))
                            map.put("status", 3);//已生成
                    }
                    accountDetail.setDebit(new BigDecimal(money));
                    accountDetail.setCreateDate(new Date());
                    accountDetail.setCreator(user.getUserID());
                    accountDetail.setCreateName(user.getUserName());
                    accountDetail.setOrg(organization);
                    accountDetail.setAccount(accountPeriod.getId().toString());
                    accountDetail.setAuditDate(new Date());
                    accountDetail.setAuditorName(auditorName);//经手人
                    accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
                    accountDetail.setMethod(method);
                    accountDetail.setSummary(summary);
                    accountDetail.setPurpose(purpose);//用途
                    accountDetail.setOppositeCorp(oppositeCorp);//收款单位
                    accountDetail.setMemo(memo);
                    accountDetail.setFid(financeAccount.getId().toString());
                    accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
                    accountDetail.setAccountId(financeAccount);//账户外键
                    accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                    accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
                    accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                    accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                    accountDetail.setBillDetail(f);
                    accountDetail.setSource("1");
                    accountDetail.setSubType(subType); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
                    accountDetail.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
                    accountDetail.setStakeholder(stakeholder); //干系人ID(收款单位的id)
                    accountDetail.setBillQuantity(billQuantity);  //票据数量
                    if (billDate!=null) {
                        accountDetail.setBillDate(NewDateUtils.dateFromString(billDate, "yyyy-MM-dd"));  //票据日期
                    }else {
                        accountDetail.setBillDate(new Date());
                    }
                    accountDetail.setFactDate(NewDateUtils.dateFromString(factDate,"yyyy-MM-dd"));  //实际(付款/支出)日期'

                    BigDecimal debit = accountPeriod.getDebit().add(accountDetail.getDebit());
                    BigDecimal balance = accountPeriod.getBalance().subtract(accountDetail.getDebit());
                    financeAccount.setDebit(financeAccount.getDebit().add(accountDetail.getDebit()));//计入账户支出
                    financeAccount.setBalance(financeAccount.getBalance().subtract(accountDetail.getDebit()));//从余额减去支出
                    accountDetail.setBalance(financeAccount.getBalance());
                    accountPeriod.setDebit(debit);
                    accountPeriod.setBalance(balance);

//                    debit = ap.getDebit().add(accountDetail.getDebit());
//                    balance = ap.getBalance().subtract(accountDetail.getDebit());
                    ap.setDebit(ap.getDebit().add(accountDetail.getDebit()));
                    ap.setBalance(ap.getBalance().subtract(accountDetail.getDebit()));

                    accountService.updateAccountPeroid(accountPeriod);//月结
                    accountService.updateAccountPeroid(ap);//日结
                    accountService.updateFinanceAccount(financeAccount);//账户
                    accountService.saveAccountDetail(accountDetail);//明细

                    if (StringUtils.isNotEmpty(imgPaths)){  //添加图片
                        JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                        List imgPathList = (List) JSONArray.toCollection(jsonValues);
                        for (int i=0;i<imgPathList.size();i++){
                            FinanceAccountBillImage financeAccountBillImage = dataService.addBillImage(user,accountDetail.getId(),f.getId(),"1",imgPathList.get(i).toString(),i+1);

                            FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImage.getId(),FinanceAccountBillImage.class);
                            uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表

                        }
                    }

                    map.put("status", 1);//成功
                }
            } else {

                //支出的外部转账支票
                FinanceReturn fr = financeReturnService.getReturn(checkId);
                fr.setState("4");//1-有效,2-存入银行,3-作废,4-已使用
                fr.setUpdateName(user.getUserName());
                fr.setUpdator(user.getUserID());
                fr.setUpdateDate(new Date());
                dataService.updateFinanceReturn(fr);

                //支票明细
                FinanceAccountBill f = new FinanceAccountBill();
                f.setOperatorName(auditorName);//经手人
                f.setCreateDate(new Date());
                f.setCreateName(user.getUserName());
                f.setCreator(user.getUserID());
                f.setType("2");//1-收入，2-支出
                f.setAmount(BigDecimal.valueOf(money));//金额
                f.setBillAmount(new BigDecimal(billAmount));  //票面金额
                f.setSummary(summary);//摘要k
                f.setPurpose(purpose);//用途
                f.setBillQuantity(billQuantity);//票据数量
//                f.setBillPeriod(billPeriod);//是否本月票据
//                f.setBillCat(billCatName);//票据种类
                f.setOppositeCorp(oppositeCorp);//收款单位
                f.setBillNo(fr.getReturnNo());//发票号码
                f.setFinanceReturn(fr);//回款票据外键
                f.setMemo(memo);
                f.setOrg(organization);
                f.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                f.setSource("1");
                f.setSubType(subType); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
                f.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
                f.setStakeholder(stakeholder); //干系人ID(收款单位的id)
                f.setBillDate(NewDateUtils.dateFromString(billDate,"yyyy-MM-dd"));  //票据日期
                f.setFactDate(NewDateUtils.dateFromString(factDate,"yyyy-MM-dd"));  //实际(付款/支出)日期'
                dataService.saveFinanceAccountBill(f);

                if (StringUtils.isNotEmpty(imgPaths)){  //添加图片
                    JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                    List imgPathList = (List) JSONArray.toCollection(jsonValues);
                    for (int i=0;i<imgPathList.size();i++){
                        FinanceAccountBillImage financeAccountBillImage = dataService.addBillImage(user,null,f.getId(),"1",imgPathList.get(i).toString(),i+1);

                        FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImage.getId(),FinanceAccountBillImage.class);
                        uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表

                    }
                }

                map.put("status", 1);//成功
            }

        } else if ("4".equals(method)) {
            //4支出的承兑汇票
            FinanceReturn fr = financeReturnService.getReturn(checkId);
            fr.setState("4");//1-有效,2-存入银行,3-作废,4-已使用
            fr.setUpdateName(user.getUserName());
            fr.setUpdator(user.getUserID());
            fr.setUpdateDate(new Date());
            dataService.updateFinanceReturn(fr);

            //支票明细
            FinanceAccountBill f = new FinanceAccountBill();
            f.setOperatorName(auditorName);//经手人
            f.setCreateDate(new Date());
            f.setCreateName(user.getUserName());
            f.setCreator(user.getUserID());
            f.setType("2");//1-收入，2-支出
            f.setAmount(BigDecimal.valueOf(money));//金额
            f.setBillAmount(new BigDecimal(billAmount));  //票面金额
            f.setSummary(summary);//摘要
            f.setPurpose(purpose);//用途
            f.setBillQuantity(billQuantity);//票据数量
            f.setOppositeCorp(oppositeCorp);//收款单位
//            f.setBillPeriod(billPeriod);//是否本月票据
//            f.setBillCat(billCatName);//票据种类
            f.setBillNo(fr.getReturnNo());//发票号码
            f.setFinanceReturn(fr);//回款票据外键
            f.setMemo(memo);
            f.setOrg(organization);
            f.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            f.setSource("1");
            f.setSubType(subType); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
            f.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
            f.setStakeholder(stakeholder); //干系人ID(收款单位的id)
            if (billDate!=null) {
                f.setBillDate(NewDateUtils.dateFromString(billDate, "yyyy-MM-dd"));  //票据日期
            }else {
                f.setBillDate(new Date());
            }
            f.setFactDate(NewDateUtils.dateFromString(factDate,"yyyy-MM-dd"));  //实际(付款/支出)日期'
            dataService.saveFinanceAccountBill(f);

            if (StringUtils.isNotEmpty(imgPaths)){  //添加图片
                JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                List imgPathList = (List) JSONArray.toCollection(jsonValues);
                for (int i=0;i<imgPathList.size();i++){
                    FinanceAccountBillImage financeAccountBillImage = dataService.addBillImage(user,null,f.getId(),"1",imgPathList.get(i).toString(),i+1);

                    FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImage.getId(),FinanceAccountBillImage.class);
                    uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表
                }
            }
            map.put("status", 1);//成功

        } else if ("5".equals(method)) {
            //银行转账
            FinanceAccount financeAccount = accountService.getFinanceAccountById(financeAccountId);
            AccountPeriod accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());  //月结

            //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
            //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
            BigDecimal me = new BigDecimal(money.toString());
            BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
            BigDecimal jieguo = ba.subtract(me);

            if (jieguo.doubleValue() < 0) {
                map.put("status", 4);//余额不足
            } else if (0 == financeAccount.getAccountStatus()) {
                map.put("status", 2);//此账户已被冻结，不允许操作
                map.put("content","此账户已被冻结，不允许操作");
            }else {
                AccountDetail accountDetail = new AccountDetail();
                if (accountPeriod == null) {
                    FinanceUtils.addPeroid(financeAccount.getId(), user, accountService);
                }
                accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());//再查一遍
                AccountPeriod ap = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结
                accountDetail.setDebit(new BigDecimal(money));

                if (accountPeriod.getBuildState() != null) {
                    if (accountPeriod.getBuildState().equals("已生成"))
                    map.put("status", 3);//已生成
                    map.put("content","已生成");
                }

                accountDetail.setCreateDate(new Date());
                accountDetail.setCreator(user.getUserID());
                accountDetail.setCreateName(user.getUserName());
                accountDetail.setOrg(organization);
                accountDetail.setAccount(accountPeriod.getId().toString());
                accountDetail.setAuditDate(new Date());
                accountDetail.setAuditorName(auditorName);
                accountDetail.setMethod(method);
                accountDetail.setSummary(summary);
                accountDetail.setPurpose(purpose);//用途
                accountDetail.setOppositeCorp(oppositeCorp);//收款单位
                accountDetail.setMemo(memo);
                accountDetail.setFid(financeAccount.getId().toString());
                if (billAmount==null||billAmount==0){
                    billAmount = money;
                }
                accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
                accountDetail.setAccountId(financeAccount);//账户外键
                accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
                accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                accountDetail.setSource("1");
                accountDetail.setSubType(subType); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
                accountDetail.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
                accountDetail.setStakeholder(stakeholder); //干系人ID(收款单位的id)
                accountDetail.setBillQuantity(billQuantity);  //票据数量
                if (billDate!=null) {
                    accountDetail.setBillDate(NewDateUtils.dateFromString(billDate, "yyyy-MM-dd"));  //票据日期
                }else {
                    accountDetail.setBillDate(new Date());
                }
                accountDetail.setFactDate(NewDateUtils.dateFromString(factDate,"yyyy-MM-dd"));  //实际(付款/支出)日期'

                BigDecimal debit = accountPeriod.getDebit().add(accountDetail.getDebit());
                BigDecimal balance = accountPeriod.getBalance().subtract(accountDetail.getDebit());
                financeAccount.setDebit(financeAccount.getDebit().add(accountDetail.getDebit()));//计入账户支出
                financeAccount.setBalance(financeAccount.getBalance().subtract(accountDetail.getDebit()));//从余额减去支出
                accountDetail.setBalance(financeAccount.getBalance());
                accountPeriod.setDebit(debit);
                accountPeriod.setBalance(balance);

//                debit = ap.getDebit().add(accountDetail.getDebit());
//                balance = ap.getBalance().subtract(accountDetail.getDebit());
                ap.setDebit(ap.getDebit().add(accountDetail.getDebit()));
                ap.setBalance(ap.getBalance().subtract(accountDetail.getDebit()));

                accountService.updateAccountPeroid(accountPeriod);//月结
                accountService.updateAccountPeroid(ap);//日结
                accountService.updateFinanceAccount(financeAccount);//账户
                accountService.saveAccountDetail(accountDetail);//明细

                if (StringUtils.isNotEmpty(imgPaths)){  //添加图片
                    JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                    List imgPathList = (List) JSONArray.toCollection(jsonValues);
                    for (int i=0;i<imgPathList.size();i++){
                        FinanceAccountBillImage financeAccountBillImage = dataService.addBillImage(user,accountDetail.getId(),null,"1",imgPathList.get(i).toString(),i+1);

                        FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImage.getId(),FinanceAccountBillImage.class);
                        uploadService.addFileUsing(financeBillUsing, imgPathList.get(i).toString(),null, user, "数据录入");//新增引用表
                    }
                }

                map.put("status", 1);//成功
                map.put("content","操作成功");
            }
        } else {
            map.put("status", 0);//失败
            map.put("content","操作失败");
        }
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 1.207财务优化之税种---数据录入中支出的税种
     * @Date 2022/7/13
     * @param taxCategorys 中包含的字段内容{ taxId(税种id) taxCategoryName(税种名称) 税款所属时期(businessPeriodBegin-开始时间 businessPeriodEnd-结束时间,Integer 格式是yyyyMMdd) report(报表id)
     * periodBegin(需申报的开始日期 String yyyy-MM-dd) periodEnd(需申报的截止日期 String yyyy-MM-dd) String method(1-现金 5-银行转账), Double money(金额)
     * Integer financeAccountId(银行账户), String memo(备注), String factDate（实际缴纳日期）,String imgPaths(图片)，Integer taxPeriodIdInsert（申报记录id） }
     * @return
     **/
    @ResponseBody
    @RequestMapping("/debitTaxCategory.do")
    public JsonResult debitTaxCategory(User user,String taxCategorys){
        if (StringUtils.isNotEmpty(taxCategorys)){
            Map<String,Object> map = dataService.debitTaxCategory(user,taxCategorys);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }
    
    
    /**
     * <AUTHOR>
     * @Description 支出类型为-其他同事的公务支出（财务费用除外，需入库物品的报销除外）【报销的】
     * @Date 2022/2/16
     * @param 此接口与个人事务中的报销(submitReimburseApply.do)接口大体逻辑相同，个别细节不同
     * @param  Integer factUser-实际用户(同事userid)' 0-本人的公务支出（财务费用除外、需入库物品的报销除外）
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/debitReimburse.do")
    public JsonResult submitReimburseApply(HttpServletRequest request, User user,Integer factUser) throws Exception {
        String  reimburse=request.getParameter("reimburse");//报销
        String  commonBills=request.getParameter("commonBills");//公用的票据部分
        String content="申请失败";
        if (!StringUtil.isNullOrEmpty(reimburse)&&!StringUtil.isNullOrEmpty(commonBills)&&user!=null) {
            content = personnelReimburseService.reimburseApply(user.getUserID(), reimburse, commonBills,factUser);
        }
        Map<String,Object> map = new HashMap<>();
        map.put("content",content);
        return new JsonResult(1,map);

    }

    /**
     * <AUTHOR>
     * @Date 2017/1/4 15:07
     * 内部非支出性转账
     */
    @ResponseBody
    @RequestMapping("/transferAccounts.do")
    public void transferAccounts(User user, String memo, String occurrenceTime, String method, Integer fromId, Integer toId, Double money, Integer chequeDetailId, HttpServletResponse response) throws ParseException, IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Organization organization = user.getOrganization();
        if (fromId != null && toId != null) {
            //Debug start ********
            Map<String, String> debug = new HashMap<>();
            debug.put("method", method);
            debug.put("fromId", fromId.toString());
            debug.put("toId", toId.toString());
            debug.put("money", money.toString());
            //Debug finish ********

            FinanceAccount from = accountService.getFinanceAccountById(fromId);//转账账户
            FinanceAccount to = accountService.getFinanceAccountById(toId);//收款账户

//            BigDecimal jieguo = from.getBalance().subtract(new BigDecimal(money));
            //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
            //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
            BigDecimal me = new BigDecimal(money.toString());
            BigDecimal ba = new BigDecimal(from.getBalance().toString());
            BigDecimal jieguo = ba.subtract(me);

            AccountDetail fromDetail=new AccountDetail();

            if (fromId.equals(toId)) {
                map.put("status", 2);//失败 不能对自身转账
            } else if (from.getAccountStatus() == 0 || to.getAccountStatus() == 0) {
                map.put("status", 3);//账户已被冻结，不能再继续操作！
            } else if (jieguo.doubleValue() < 0) {
                map.put("status", 4);//余额不足啦！
            } else {
                AccountPeriod apFromDay = accountService.getAccountPeriodByDay( from.getId(), new Date());//账户日结
                AccountPeriod apToDay = accountService.getAccountPeriodByDay(to.getId(), new Date());

                AccountPeriod apFrom = accountService.getAccountPeriodByMonth(from.getId(),new Date());//转账账户最新一期
                AccountPeriod apTo = accountService.getAccountPeriodByMonth(to.getId(), new Date());//收款账户最新一期

                if (apFrom == null) {
                    FinanceUtils.addPeroid(from.getId(), user, accountService);
                }
                apFrom = accountService.getAccountPeriodByMonth(from.getId(), new Date());//转账账户最新一期
                if (apTo == null) {
                    FinanceUtils.addPeroid(to.getId(), user, accountService);
                }
                apTo = accountService.getAccountPeriodByMonth(to.getId(), new Date());//收款账户最新一期

                FinanceAccountBill financeAccountBill = new FinanceAccountBill();

                if (apFrom != null) {
                    //Debug start ********
                    debug.put("from0", JSON.toJSONString(from));
                    debug.put("apFrom0", JSON.toJSONString(apFrom));
                    debug.put("apFromDay0", JSON.toJSONString(apFromDay));
                    //Debug finish ********
                    AccountDetail accountDetail = new AccountDetail();
//                    accountDetail.setSummary("向:" + to.getBankName() + (to.getAccount() == null ? "转账" : "账号为:" + to.getAccount() + "转账"));
                    if ("6".equals(method)) {
                        accountDetail.setSummary("存现金");
                    }
                    if ("7".equals(method))
                        accountDetail.setSummary("取现金");

                    if (chequeDetailId != null && chequeDetailId != 0) {
                        FinanceChequeDetail financeChequeDetail = financeChequeService.getChequeByDetailId(chequeDetailId);
                        financeChequeDetail.setAmount(new BigDecimal(money));//金额
                        financeChequeDetail.setBillAmount(new BigDecimal(money));  //票面金额
                        financeChequeDetail.setOperateDate(NewDateUtils.today(sdf.parse(occurrenceTime)));//发生时间
                        financeChequeDetail.setOperator(user.getUserName());//支付经手人
                        financeChequeDetail.setFinancialHandling(user.getUserName());  //财务经手人
                        financeChequeDetail.setState("2");//已使用
                        financeChequeDetail.setUpdateDate(new Date());
                        financeChequeDetail.setUpdator(user.getUserID());
                        financeChequeDetail.setUpdateName(user.getUserName());
                        financeChequeService.updateCashChequeState(financeChequeDetail);

                        financeAccountBill.setOperatorName(user.getUserName());//经手人
                        financeAccountBill.setCreateDate(new Date());
                        financeAccountBill.setCreateName(user.getUserName());
                        financeAccountBill.setCreator(user.getUserID());
                        financeAccountBill.setType("2");//1-收入，2-支出
                        financeAccountBill.setAmount(new BigDecimal(money));//金额
                        financeAccountBill.setSummary("取现金");//摘要
//                         financeAccountBill.setPurpose(purpose);//用途
//                        financeAccountBill.setBillQuantity(billQuantity);//票据数量
//                        financeAccountBill.setBillPeriod(billPeriod);//是否本月票据
                        financeAccountBill.setBillCat("收据");//票据种类
                        financeAccountBill.setMemo(memo);
                        financeAccountBill.setBillPeriod("1");//1-本月票据，2-非本月票据
                        financeAccountBill.setAuditDate(NewDateUtils.today(sdf.parse(occurrenceTime)));//发生时间
                        financeAccountBill.setOrg(organization);
                        financeAccountBill.setCheque(financeChequeDetail);//现金支票外键
                        financeAccountBill.setAccountantStatus("0");//会计数据状态  1-未选择  2-已选择， 0-不选择
                        financeAccountBill.setBillAmount(new BigDecimal(money));  //票面金额
                        financeAccountBill.setSource("1");
                        financeAccountBill.setBillDate(new Date());
                        dataService.saveFinanceAccountBill(financeAccountBill);//新增为银行转账的票据明细

                        accountDetail.setBillDetail(financeAccountBill);//票据明细外键
                    }
                    if ("8".equals(method))
                        accountDetail.setSummary("向:" + to.getBankName() + (to.getAccount() == null ? "转账" : "账号为:" + to.getAccount() + "转账"));

                    accountDetail.setCreateDate(new Date());
                    accountDetail.setCreateName(user.getUserName());
                    accountDetail.setCreator(user.getUserID());
                    accountDetail.setOrg(organization);
                    accountDetail.setFid(String.valueOf(from.getId()));
                    accountDetail.setAccountId(from);//账户外键
                    accountDetail.setOppositeAccount(to.getName() + to.getAccount());//（收款时为付款账号，付款为收款账号）
                    accountDetail.setOppositeId(to.getId());//（收款时为付款账号id，付款为收款账号id）
                    accountDetail.setAccount(apFrom.getId().toString());
                    accountDetail.setDebit(BigDecimal.valueOf(money));
                    accountDetail.setBillAmount(BigDecimal.valueOf(money));  //票面金额
                    accountDetail.setMemo(memo);
                    accountDetail.setType("2");//转账类型为2
                    accountDetail.setAuditDate(NewDateUtils.today(sdf.parse(occurrenceTime)));//发生时间
                    accountDetail.setMethod(method);//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                    accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                    accountDetail.setSource("1");
                    accountDetail.setAccountBank(from.getBankName());
                    if (from.getOperation()!=null){
                        accountDetail.setAccountBank(accountDetail.getAccountBank()+"（"+from.getOperation()+"）");
                    }
                    if (from.getAccount()!=null){
                        accountDetail.setAccountBank(accountDetail.getAccountBank()+from.getAccount());
                    }

                    accountDetail.setOppositeAccount(to.getBankName());
                    if (to.getOperation()!=null){
                        accountDetail.setOppositeAccount(accountDetail.getOppositeAccount()+"（"+to.getOperation()+"）");
                    }
                    if (to.getAccount()!=null){
                        accountDetail.setOppositeAccount(accountDetail.getOppositeAccount()+to.getAccount());
                    }
                    accountDetail.setBillDate(new Date());
                    accountDetail.setAccountantStatus("1");


                    BigDecimal debit = apFrom.getDebit().add(accountDetail.getDebit());
                    BigDecimal balance = apFrom.getBalance().subtract(accountDetail.getDebit());

                    apFrom.setDebit(debit);
                    apFrom.setBalance(balance);
                    accountDetail.setBalance(balance);
                    apFromDay.setDebit(apFromDay.getDebit() == null ? new BigDecimal(0).add(accountDetail.getDebit()) : apFromDay.getDebit().add(accountDetail.getDebit()));//日结账
                    apFromDay.setBalance(apFromDay.getBalance() == null ? new BigDecimal(0).subtract(accountDetail.getDebit()) : apFromDay.getBalance().subtract(accountDetail.getDebit()));

                    from.setDebit(from.getDebit() == null ? new BigDecimal(0).add(accountDetail.getDebit()) : from.getDebit().add(accountDetail.getDebit()));//计入账户支出
                    from.setBalance(from.getBalance().subtract(accountDetail.getDebit()));//从余额减去支出

                    accountService.saveAccountDetail(accountDetail);
                    fromDetail=accountDetail;
                    //Debug start ********
                    debug.put("balanceFrom", balance.toString());
                    debug.put("from", JSON.toJSONString(from));
                    debug.put("accountDetailFrom", JSON.toJSONString(accountDetail));
                    debug.put("apFromDay", JSON.toJSONString(apFromDay));
                    //Debug finish ********
                }

                if (apTo != null) {
                    //Debug start ********
                    debug.put("to0", JSON.toJSONString(to));
                    debug.put("apTo0", JSON.toJSONString(apTo));
                    debug.put("apToDay0", JSON.toJSONString(apToDay));
                    //Debug finish ********
                    AccountDetail accountDetail = new AccountDetail();
//                    accountDetail.setSummary("向:" + to.getBankName() + (to.getAccount() == null ? "转账" : "账号为:" + to.getAccount() + "转账"));
                    if ("6".equals(method))
                        accountDetail.setSummary("存现金");
                    if ("7".equals(method))
                        accountDetail.setSummary("取现金");
                    accountDetail.setBillDetail(financeAccountBill);
                    if ("8".equals(method))
                        accountDetail.setSummary("来自" + from.getBankName() + (from.getAccount() == null ? "转账" : "账号为:" + from.getAccount() + "的转账"));

                    accountDetail.setCreateDate(new Date());
                    accountDetail.setCreateName(user.getUserName());
                    accountDetail.setCreator(user.getUserID());
                    accountDetail.setOrg(organization);
                    accountDetail.setFid(String.valueOf(to.getId()));
                    accountDetail.setAccountId(to);//账户外键
                    accountDetail.setOppositeAccount(from.getName()+"("+from.getOperation()+")" + from.getAccount());//（收款时为付款账号，付款为收款账号）
                    accountDetail.setOppositeId(from.getId());//（收款时为付款账号id，付款为收款账号id）
                    accountDetail.setAccount(apTo.getId().toString());
                    accountDetail.setCredit(BigDecimal.valueOf(money));   //收入
                    accountDetail.setBillAmount(BigDecimal.valueOf(money));  //票面金额
                    accountDetail.setMemo(memo);
                    accountDetail.setType("2");
                    accountDetail.setAuditDate(NewDateUtils.today(sdf.parse(occurrenceTime)));//发生时间
                    accountDetail.setMethod(method);//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                    accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                    accountDetail.setSource("1");
                    accountDetail.setAccountBank(to.getBankName());
                    if (to.getOperation()!=null){
                        accountDetail.setAccountBank(accountDetail.getAccountBank()+"（"+to.getOperation()+"）");
                    }
                    if (to.getAccount()!=null){
                        accountDetail.setAccountBank(accountDetail.getAccountBank()+to.getAccount());
                    }

                    accountDetail.setOppositeAccount(from.getBankName());
                    if (from.getOperation()!=null){
                        accountDetail.setOppositeAccount(accountDetail.getOppositeAccount()+"（"+from.getOperation()+"）");
                    }
                    if (from.getAccount()!=null){
                        accountDetail.setOppositeAccount(accountDetail.getOppositeAccount()+from.getAccount());
                    }
                    accountDetail.setBillDate(new Date());

                    BigDecimal credit = apTo.getCredit().add(accountDetail.getCredit());
                    BigDecimal balance = apTo.getBalance().add(accountDetail.getCredit());

                    apToDay.setCredit(apToDay.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : apToDay.getCredit().add(accountDetail.getCredit()));//日结账
                    apToDay.setBalance(apToDay.getBalance() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : apToDay.getBalance().add(accountDetail.getCredit()));

                    to.setCredit(to.getCredit().add(accountDetail.getCredit()));//计入账户收入
                    to.setBalance(to.getBalance().add(accountDetail.getCredit()));//从余额加上收入
                    apTo.setCredit(credit);
                    apTo.setBalance(balance);
                    accountDetail.setBalance(balance);
                    if (financeAccountBill.getId() == null) {
                        accountDetail.setBillDetail(null);
                    }
                    accountDetail.setDetailId(fromDetail.getId());//把付款账户的明细存入收款账户明细
                    accountService.saveAccountDetail(accountDetail);

                    fromDetail.setDetailId(accountDetail.getId());//把收款账户明细id更新到付款账户明细
                    accountService.updateAccountDetail(fromDetail);
                    //Debug start ********
                    debug.put("balanceTo", balance.toString());
                    debug.put("to", JSON.toJSONString(to));
                    debug.put("apTo", JSON.toJSONString(apTo));
                    debug.put("accountDetailTo", JSON.toJSONString(accountDetail));
                    debug.put("apToDay", JSON.toJSONString(apToDay));
                    //Debug finish ********
                }
                accountService.updateAccountPeroid(apFrom);
                accountService.updateAccountPeroid(apTo);
                accountService.updateFinanceAccount(from);
                accountService.updateFinanceAccount(to);
                accountService.updateAccountPeroid(apFromDay);
                accountService.updateAccountPeroid(apToDay);

                map.put("status", 1);

            }
        } else {
            map.put("status", 0); //失败

        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    /**
     * <AUTHOR>
     * @date 2017/1/9 14:15
     * 在内部非支出性转账中，根据转账种类不同，付款账户和收款账户的内容不同
     * type 1-存现金 2-取现金 3-其他银行转账
     */
    @ResponseBody
    @RequestMapping("/chooseAccountByType.do")
    public void chooseAccountByType(HttpServletResponse response, String type, User user) throws IOException {
        Map<String, Object> map = new HashMap<>();
        Integer oid = user.getOid();
        if (!"".equals(type)) {
            List<FinanceAccount> financeAccounts = financeAccountService.getAccountByAccountStatus(oid, 1,null); // 所有可用的银行账户
            FinanceAccount financeAccount = financeAccountService.getCashAccount(oid, 1); //查询现金/备用金账户
            if ("1".equals(type)) {
                map.put("financeAccount1", financeAccount); //付款账户  （查询现金/备用金账户）
                map.put("financeAccount2", financeAccounts); //收款账户  （所有可用的银行账户 ）
            } else if ("2".equals(type)) {
                List<FinanceAccount> financeAccountList = new ArrayList<>();
                for (FinanceAccount f : financeAccounts) {
                    if (f.getCashable()!=null && 1 == f.getCashable()) {
                        financeAccountList.add(f);
                    }
                }
                map.put("financeAccount1",financeAccountList); //付款账户 （所有可取现可用的银行账户）
                map.put("financeAccount2",financeAccount); //收款账户  （查询现金/备用金账户）
            }else {
                map.put("financeAccount1",financeAccounts); //付款账户 （所有可用的银行账户）
                map.put("financeAccount2",financeAccounts); //收款账户 （所有可用的银行账户）
            }
        }
        ObjectToJson.objectToJson1(map, new String[]{"org", "financeAccountHistories", "accountPeriodHashSet", "accountDetailHashSet", "financeChequeDetailHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2017/1/9 17:03
     * 在支出中根据支出方式不同，显示的内容不同
     * type 1-转账支票(1-内部支票 2-外部支票) 3-承兑汇票 4-银行转账
     */
    @ResponseBody
    @RequestMapping("/chooseCheque.do")
    public void chooseCheque(User user, HttpServletResponse response, String type) throws IOException {
        Map<String, Object> map = new HashMap<>();
        Integer oid = user.getOid();
        financeReturnService.chooseCheque(oid,type,map);
        ObjectToJson.objectToJson1(map, new String[]{"org", "financeAccountHistories", "accountPeriodHashSet", "accountDetailHashSet", "financeChequeDetailHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2017/1/9 17:03
     * 在支出中根据支出方式不同，显示的内容不同 【与上面的chooseCheque.do接口一样，只返回值不同,以后用此接口】
     * type 1-转账支票(1-内部支票 2-外部支票) 3-承兑汇票 4-银行转账
     */
    @ResponseBody
    @RequestMapping("/chooseReturn.do")
    public JsonResult chooseReturn(User user, String type) {
        Map<String, Object> map = new HashMap<>();
        Integer oid = user.getOid();
        financeReturnService.chooseCheque(oid,type,map);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @date 2017/1/11 11:06
     * 在支出中根据支出方式不同，显示的内容不同
     * 当支出方式为转账支票中的内部支票时(仅转账支票，没有现金支票)，获取支票号
     */
    @ResponseBody
    @RequestMapping("/getChequeByAccountId.do")
    public void getChequeByAccountId(HttpServletResponse response, Integer accountId) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (accountId != null) {
            List<FinanceChequeDetail> financeChequeDetails = financeChequeService.getChequeByAccountID(accountId);
            map.put("financeChequeDetails", financeChequeDetails);
        }
        ObjectToJson.objectToJson1(map, new String[]{"chequeReg", "accountId", "financeAccountBillHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2017/1/11 11:06
     * 在支出中根据支出方式不同，显示的内容不同【与getChequeByAccountId.do接口一样，只返回值不同,以后用此接口】
     * 当支出方式为转账支票中的内部支票时(仅转账支票，没有现金支票)，获取支票号
     */
    @ResponseBody
    @RequestMapping("/getChequeDetailByAccountId.do")
    public JsonResult getChequeDetailByAccountId(Integer accountId) {
        Map<String, Object> map = new HashMap<>();
        if (accountId != null) {
            List<FinanceChequeDetail> financeChequeDetails = financeChequeService.getChequeByAccountID(accountId);
            map.put("financeChequeDetails", financeChequeDetails);
        }
        return new JsonResult(1,map);
    }

    //--------------------------------------以下为会计模块中的方法（第一个接口作废）------------------------------------------------------//

    /**
     * <AUTHOR>
     * @date 2017/2/10 9:42
     * 会计-科目选择中待选择中的正常待选择的数据【此接口不用】
     * 不进入会计的财务数据：1.支出的票据类型选择为收据的；2.内部非支出性转账的，取现金与存现金中涉及到非对公户的，不进会计；其他内部转账，两方都是非对公户的，不进会计；3.报销的，票据类型为收据的，不分账户类型；4.数据修改的暂时不进会计【1.29版本确定的18/02/24】
     */
//    @ResponseBody
//    @RequestMapping("/getAccountingData.do")
//    public void getAccountingData(HttpServletResponse response,User user) throws IOException {
//        Map<String, Object> map = new HashMap<>();
//        Integer oid = user.getOid();
//        Code code = codeService.getCodeByName(oid,"收据");  //查询收据
//        Code code1 = codeService.getCodeByName(oid,"增值税专用发票");  //查询增值税专用发票
//        if (oid != null) {
//            List<FinanceAccountBill> financeAccountBills = dataService.getDataByOid(oid,"2");  //除“已选择”外的所有
//            List<FinanceAccountBill> financeAccountBillList = new ArrayList<>();
//            for (FinanceAccountBill financeAccountBill : financeAccountBills) {
//                if ("".equals(financeAccountBill.getAccountantStatus()) || "1".equals(financeAccountBill.getAccountantStatus()) || "0".equals(financeAccountBill.getAccountantStatus())) {      //数据修改的暂时不进会计
//                    if (!"收据".equals(financeAccountBill.getBillCat())){  //收入和支出中除收据外的信息
//                        AccountDetail a = dataService.getByBillId(financeAccountBill.getId());
//                        if (a!=null){
//                            if ("1".equals(a.getMethod()) || "3".equals(a.getMethod()) || "4".equals(a.getMethod()) || "5".equals(a.getMethod()) ){
//                                financeAccountBillList.add(financeAccountBill);  //支付方式为现金、银行转账、转账支票、承兑汇票的数据
//                            }else {
//                                FinanceAccount f1 = new FinanceAccount();
//                                FinanceAccount f2 = new FinanceAccount();
//                                if (a.getOppositeId()!=null){
//                                    f1 = financeAccountService.getAccountByIdAndIsPublic(a.getOppositeId(),"1");  //根据账户id查找是否为对公户
//                                }
//                                if (a.getAccountId()!=null){
//                                    f2 = financeAccountService.getAccountByIdAndIsPublic(a.getAccountId().getId(),"1");  //根据账户id查找是否为对公户
//                                }
//                                if (f1!=null && f2!=null){
//                                    financeAccountBillList.add(financeAccountBill);//类型是收据但报销账户是对公账户的数据
//                                }
//                            }
//                        }else {
//                            financeAccountBillList.add(financeAccountBill);  //收入与支出的转账支票或者承兑汇票
//                        }
//                    }else if (!"".equals(financeAccountBill.getType()) && "1".equals(financeAccountBill.getType())){
//                        financeAccountBillList.add(financeAccountBill);   //收入票据为收据的也进入会计
//                    }
//                }
//            }
//            List<AccountDetail> accountDetails = dataService.getAccountingByOid(oid);
//            List<AccountDetail> accountDetailList = new ArrayList<>();
//            for (AccountDetail accountDetail : accountDetails) {
//                if ("".equals(accountDetail.getAccountantStatus()) || accountDetail.getAccountantStatus()==null || "1".equals(accountDetail.getAccountantStatus())) {
//                    if (accountDetail.getPersonnelReimburse() != null) {
//                        PersonnelReimburse personnelReimburse = accountDetail.getPersonnelReimburse();
//                        List<FinanceReimburseBill> financeReimburseBills = personnelReimburseService.authInvoiceDetail(personnelReimburse.getId(),code1.getId(),"0");  //查询是否有未认证的增值税专用发票
//                        if (financeReimburseBills.size()==0){  //查询没有未认证的增值税专用发票，或者增值税专用发票已去不认证完成
//                            for (FinanceReimburseBill fb:personnelReimburse.getFinanceReimburseBillHashSet()){
//                                if (fb.getBillCat().equals(code.getId())){  //如果是收据，则根据账户判断是否进入会计
//                                    FinanceAccount financeAccount = accountDetail.getAccountId();
//                                    if (financeAccount!=null && "1".equals(financeAccount.getIsPublic())) {  //账户为对公户，则进入会计
//                                        accountDetail.setBillCat(fb.getBillCat().toString());
//                                        accountDetail.setBillQuantity(personnelReimburse.getBillQuantity());
//                                        accountDetail.setBillPeriod(personnelReimburse.getBillDate());
//                                        accountDetail.setBeginDate(personnelReimburse.getBeginDate());  //发生时间开始
//                                        accountDetail.setEndDate(personnelReimburse.getEndDate());   //发生时间结束
//                                        accountDetailList.add(accountDetail);  //报销两讫后收据中报销账户为对公户的
//                                    }
//                                    break;
//                                }else {  //票据种类中没有增值税专用发票，或者增值税专用发票全部都认证完成，都进入会计
//                                    accountDetail.setBillCat(fb.getBillCat().toString());
//                                    accountDetail.setBillQuantity(personnelReimburse.getBillQuantity());
//                                    accountDetail.setBillPeriod(personnelReimburse.getBillDate());
//                                    accountDetail.setBeginDate(personnelReimburse.getBeginDate());  //发生时间开始
//                                    accountDetail.setEndDate(personnelReimburse.getEndDate());  //发生时间结束
//                                    accountDetailList.add(accountDetail);
//                                    break;
//                                }
//                            }
//                        }
//                    } else if ("3".equals(accountDetail.getType())) {
//                        if ("1".equals(accountDetail.getMethod()) || "5".equals(accountDetail.getMethod())) {
//                            accountDetailList.add(accountDetail);  //收入中支付方式为现金、银行转账的数据
//                        }
//                    }else if ("2".equals(accountDetail.getType())){
//                        if ("6".equals(accountDetail.getMethod())|| "7".equals(accountDetail.getMethod()) || "8".equals(accountDetail.getMethod())){    //6-存现金，8-其他内部转账 在financeAccountBillList中没有这两种数据
//                            FinanceAccount f1 = new FinanceAccount();
//                            FinanceAccount f2 = new FinanceAccount();
//                            if (accountDetail.getOppositeId()!=null){
////                                f11 = financeAccountService.getAccountByIdAndIsPublic(accountDetail.getOppositeId(),"1");  //根据账户id查找是否为对公户
//                                f1 = financeAccountService.getAccountById(accountDetail.getOppositeId());
//                            }
//                            if (accountDetail.getAccountId()!=null){
////                                f22 = financeAccountService.getAccountByIdAndIsPublic(accountDetail.getAccountId().getId(),"1");  //根据账户id查找是否为对公户
//                                f2 = financeAccountService.getAccountById(accountDetail.getAccountId().getId());
//                            }
//                            if ("6".equals(accountDetail.getMethod()) || "7".equals(accountDetail.getMethod()) ){
//                                if (f1!=null && f2!=null){
//                                    if ("1".equals(f1.getIsPublic()) && "1".equals(f2.getIsPublic())){
//                                        if(accountDetail.getDebit()!=null){
//                                            accountDetailList.add(accountDetail);  //存现金与取现金，涉及到非对公户的，不进会计；支出的数据进入会计
//                                        }
//                                    }
//                                }
//                            }else {
//                                if ("1".equals(f1.getIsPublic()) && "1".equals(f2.getIsPublic())){
//                                    if (accountDetail.getDebit()!=null){
//                                        accountDetailList.add(accountDetail);  //非支出性转账中的其他内部转账，两方都是非对公户的，不进会计
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//            map.put("financeAccountBillList", financeAccountBillList);
//            map.put("accountDetailList", accountDetailList);
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"roles", "user", "area", "aId", "financeAccounts", "accountDetails", "accountPeriods",
//                "MtCategoryOrg", "financeReturnHashSet", "financeChequeHashSet",
//                "cheque", "org", "financeReturn", "financeAccountBillHistoryHashSet", "accountDetailHashSet",
//                "accountId", "accountDetailHistories", "billDetail", "personnelReimburse"}, response);
//    }

    /**
     * <AUTHOR>
     * @date 2017/2/13 14:42
     * 会计中正常待选择下查找每条信息的详情
     * kind 1-除收据外的报销两讫后的数据详情和收入中现金/银行转账的数据 2-收入所有和支出除收据外的数据详情
     */
    @ResponseBody
    @RequestMapping("/getAccountingDetail.do")
    public void getAccountingDetail(HttpServletResponse response, Integer detailId, String kind) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if ("1".equals(kind)) {
            AccountDetail accountDetail = dataService.getOneDetail(detailId);
            if (accountDetail != null) {
                PersonnelReimburse personnelReimburse = accountDetail.getPersonnelReimburse();
                if (personnelReimburse != null) {
                    map.put("kind", 1);
                    map.put("content", personnelReimburse);  //除去收据的报销两讫后的数据详情

                    Map debit = reimburseRelevanceService.getRelevanceDebitSubject(personnelReimburse.getUser().getOid(),personnelReimburse);
                    map.put("debitSubject", debit);  //返回会计相关联数据
                } else {
                    map.put("kind", 5);
                    map.put("content", accountDetail);  //收入为现金或者银行转账的数据
                }

            }
        } else {
            FinanceAccountBill financeAccountBill = dataService.getAccountBillByBillId(detailId);
            if (financeAccountBill != null) {
                if (financeAccountBill.getCheque() != null) {
                    FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                    financeChequeDetail.setDateType(financeAccountBill.getType());
                    financeChequeDetail.setBillCat(financeAccountBill.getBillCat());
                    financeChequeDetail.setBillQuantity(financeAccountBill.getBillQuantity());
                    financeChequeDetail.setBillPeriod(financeAccountBill.getBillPeriod());
                    financeChequeDetail.setBillAmount(financeAccountBill.getBillAmount());
                    map.put("kind", 2);
                    map.put("content", financeChequeDetail);  //现金支票或者内部支票的数据详情
                } else if (financeAccountBill.getFinanceReturn() != null) {
                    FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();
                    financeReturn.setBillType(financeAccountBill.getType());
                    financeReturn.setBillCat(financeAccountBill.getBillCat());
                    financeReturn.setBillPeriod(financeAccountBill.getBillPeriod());
                    financeReturn.setBillQuantity(financeAccountBill.getBillQuantity());
                    financeReturn.setOppositeCorp(financeAccountBill.getOppositeCorp());
                    financeReturn.setMemo(financeAccountBill.getMemo());
                    financeReturn.setAmount(financeAccountBill.getAmount());
                    financeReturn.setSummary(financeAccountBill.getSummary());
                    financeReturn.setOperatorName(financeAccountBill.getOperatorName());
                    financeReturn.setPurpose(financeAccountBill.getPurpose());
                    financeReturn.setBillAmount(financeAccountBill.getBillAmount());
                    map.put("kind", 3);
                    map.put("content", financeReturn);  //承兑汇票或者外部支票的数据详情
                } else {
                    AccountDetail accountDetail = dataService.getByBillId(detailId);
                    financeAccountBill.setMethod(accountDetail.getMethod());
                    financeAccountBill.setOppositeAccount(accountDetail.getAccountBank());
                    map.put("kind", 4);
                    map.put("content", financeAccountBill); //支出的现金或者银行转账的信息
                }
            }
        }
        ObjectToJson.objectToJson1(map, new String[]{"org", "accountId", "accountDetailHistories", "billDetail", "personnelReimburse",
                "user", "reimburset", "approvalFlow", "reimburse",
                "cheque", "financeReturn", "accountDetailHashSet", "financeAccountBillHistoryHashSet",
                "chequeReg", "financeAccountBillHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @Date 2017/2/17 17:06
     * 获取基本户现金支票列表
     */
    @ResponseBody
    @RequestMapping("/getCashChequeByAccountId.do")
    public void getCashChequeByAccountId(Integer financeAccountId, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (financeAccountId != null) {
            List<FinanceChequeDetail> chequeDetailList = new ArrayList<FinanceChequeDetail>();

            FinanceAccount f = financeAccountService.getAccountById(financeAccountId);
            List<FinanceChequeDetail> financeChequeDetailList=financeChequeService.getChequeListByFid(f.getId(),"1","2");
            if (financeChequeDetailList.size() > 0) {
                for (FinanceChequeDetail c : financeChequeDetailList) {
                    if ("1".equals(c.getState())) {
                        if ("2".equals(c.getType())){
                            chequeDetailList.add(c);//把未使用的现金支票放到结果集
                        }
                    }
                }
                if (chequeDetailList.size() > 0) {
                    map.put("chequeDetailList", chequeDetailList);
                    map.put("status", 1);
                } else {
                    map.put("status", 2);//没有可使用的现金支票
                }
            } else {
                map.put("status", 0);//账户不存在现金支票
            }
        } else {
            map.put("status", 0);

        }
        ObjectToJson.objectToJson1(map, new String[]{"chequeReg", "accountId", "financeAccountBillHashSet"}, response);
    }


//    ------------------------------------------通用1.6数据查看接口--------------------------------------------------------------

    /**
     * <AUTHOR>
     * @Date 2017/3/21 11:14
     * 获取所有账户本日、本月、上月、本年的，收支余额上期余额接口
     * state 1-本日，2-本月，3-上月，4-本年，5-自定义
     * 2022/8/1--lyx改 1.214多地点
     */
    @ResponseBody
    @RequestMapping("/getAllAccountData.do")
//    public void getAllAccountData(Integer state,Integer oid, User user, String beginDate, String endDate, HttpServletResponse response) throws IOException, ParseException {
    public JsonResult getAllAccountData(Integer state,Integer oid, User user, String beginDate, String endDate) throws ParseException, IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        map = accountService.getAllAccountData(oid,orgIntegerList,state,beginDate,endDate);
//        ObjectToJson.objectToJson1(map, new String[]{}, response);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2017/3/22 12:03
     * 获取所有账户本日、本月、上月、本年的，按时间查看接口
     * state 1-本日，2-本月，3-上月，4-本年，5-自定义
     * 1.214多地点---2022/08/02改
     */
    @ResponseBody
    @RequestMapping("/getAllAccountDataByTime.do")
//    public void getAllAccountDataByTime(Integer state,Integer oid,Integer fid,User user,String beginDate,String endDate,HttpServletResponse response) throws ParseException, IOException {
    public JsonResult getAllAccountDataByTime(Integer state,Integer oid,Integer fid,User user,String beginDate,String endDate) throws ParseException, IOException {
//        Integer oid= user.getOid();
        Map<String, Object> map = new HashMap<String, Object>();
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        map = accountService.getAllAccountDataByTime(oid,orgIntegerList,state,fid,beginDate,endDate);
//        ObjectToJson.objectToJson1(map,new String[]{"org","accountId","accountDetailHistories","billDetail","personnelReimburse"},response);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2017/3/23 15:13
     * 获取单账户或所有账户的，月列表或日列表
     * monthOrDay  1-年进月，2-月进日，3-日进明细 null/或不传-根据传的时间来获取数据去
     * 1.214多地点---2022/08/04改
     */
    @ResponseBody
    @RequestMapping("/getAccountMonthOrDay.do")
//    public void getAccountMonthOrDay(User user,Integer oid,String monthOrDay,String beginDate,String endDate,Integer fid,HttpServletResponse response) throws ParseException, IOException {
    public JsonResult getAccountMonthOrDay(User user,Integer oid,String monthOrDay,String beginDate,String endDate,Integer fid) throws ParseException, IOException {
//        Integer oid= user.getOid();
        Map<String,Object> map=new HashMap<String,Object>();
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        map = accountService.getAccountMonthOrDay(oid,orgIntegerList,monthOrDay,beginDate,endDate,fid);
//        ObjectToJson.objectToJson1(map,new String[]{"org","accountId","accountDetailHistories","billDetail","personnelReimburse"},response);
        return new JsonResult(1,map);
    }
    /**
     * <AUTHOR>
     * @date 2017/3/22 15:40
     * 获取所有账户本日/本月/上月/本年的按账户查看接口
     * state 1-本日，2-本月，3-上月，4-本年，5-自定义按时间
     * 1.214多地点---2022/08/04改
     */
    @ResponseBody
    @RequestMapping("/getDataByAccount.do")
    public JsonResult getDataByAccount(Integer state,Integer oid, String beginDate, String endDate, User user) throws IOException {
        Map<String, Object> map = new HashMap<>();
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        map = accountService.getDataByAccount(oid,orgIntegerList,state,beginDate,endDate,1);
        return new JsonResult(1,map);
    }

    //----------------------通用框架pc端1.72财务优化1--按月查看
    /**
     *<AUTHOR>
     *@date 2019/7/4 9:26
     *按月查看
     * 1.214多地点---2022/08/04改
    */
    @ResponseBody
    @RequestMapping("/getDetailByMonth.do")
    public JsonResult getDetailByMonth(PageInfo pageInfo,User user,Integer oid) throws IOException {
        Map<String,Object> map = new HashMap<>();
//        Integer oid = user.getOid();
        Date beginDate = NewDateUtils.changeMonth(new Date(),0);  //本月一号
        Date endDate = NewDateUtils.tomorrow();  //当前时间
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        List<AccountDetail> accountDetails = dataService.getDetailByMonth(oid,beginDate,endDate,orgIntegerList,pageInfo);  //按月流水
        for (AccountDetail accountDetail:accountDetails) {
            accountDetail.setOrgName(accountDetail.getOrg().getName());
        }
        map.put("accountDetails",accountDetails);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }

    //-----------------------------------------------1.209会计优化2202-------------------------------------
    /**
     * <AUTHOR>
     * @Description 目前系统中可供制作本月凭证的票据
     * @Date 2022/5/8
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/voucherList.do")
    public JsonResult voucherList(User user,String beginDate,String endDate){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(beginDate)&&StringUtils.isNotEmpty(endDate)) {
            map = dataService.voucherList(user.getOid(), NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd"), NewDateUtils.dateFromString(endDate,"yyyy-MM-dd"));
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
     * <AUTHOR>
     * @Description 尚未入会计帐的票据【这个是按照票据日期的，和需求确认过】
     * @Date 2022/5/8
     * @param type 1-按年查询  2-按年月查询    yearDate 年（yyyy,查某年中的所有月份的）
     * @return
     **/
    @ResponseBody
    @RequestMapping("/notRecordedList.do")
    public JsonResult notRecordedList(User user,Integer type,Integer yearDate){
        Map<String,Object> map = new HashMap<>();
        Date begin = null;
        Date end = null;
        if (2==type) {
            begin = NewDateUtils.getNewYearsDay(new Date()); //当年的1号
            end = new Date();
            if (yearDate!=null){
                Date beginDate = NewDateUtils.dateFromString(yearDate.toString()+"0101","yyyyMMdd");
                begin = NewDateUtils.changeMonth(beginDate, 0); //某月的1号
                Integer yearNow = NewDateUtils.getYear(new Date());
                if (!yearNow.equals(yearDate)){
                    end = NewDateUtils.getLastTimeOfYear(beginDate); //指定日期那年的最后1毫秒
                }
            }
        }
        map = dataService.notRecordedList(user.getOid(), begin, end,type);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 不予做账的票据【这个是按照票据日期的，和需求确认过】【也用在去年与今年的“不予下账”中的在A-1年1月至A年B-1月之间的票据统计】
     * @Date 2022/5/8
     * @param type 1-按年查询  2-按年月查询    yearDate 年（yyyy,查某年中的所有月份的。按年查询时，这个有值是年月开始时间）  endYear 结束年月(按年查询时，这个有值是年月结束时间)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/noAccountingList.do")
    public JsonResult noAccountingList(User user,Integer type,Integer beginYear,Integer endYear){
        Map<String,Object> map = new HashMap<>();
        Date begin = null;
        Date end = null;
        if (1==type){
            if (beginYear!=null&&endYear!=null) {   //主要用于查询去年与今年的“不予下账”中的在A-1年1月至A年B-1月之间的票据
                Date beginDate = NewDateUtils.dateFromString(beginYear.toString() + "01", "yyyyMMdd");  //指定某年的元旦
                Date endDate = NewDateUtils.dateFromString(endYear.toString() + "01", "yyyyMMdd");  //指定某年某月的1号
                begin = NewDateUtils.today(beginDate); //某月的1号
                end = NewDateUtils.getLastTimeOfMonth(endDate);  //某月的最后一毫秒
            }
        }else if (2==type) {
            begin = NewDateUtils.changeMonth(new Date(), 0); //当月的1号
            end = new Date();
            if (beginYear!=null){
                Date beginDate = NewDateUtils.dateFromString(beginYear.toString()+"0101","yyyyMMdd");
                begin = NewDateUtils.today(beginDate); //某月的1号
                Integer yearNow = NewDateUtils.getYear(new Date());
                if (!yearNow.equals(beginYear)){
                    end = NewDateUtils.getLastTimeOfYear(beginDate); //指定日期那年的最后1毫秒
                }
            }
        }
        map = dataService.noAccountingList(user.getOid(), begin, end,false,type);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 尚未入会计帐的票据详情列表【这个是按照票据日期的，和需求确认过】
     * @Date 2022/5/9
     * @param begin 某月中的某一天(年-月-日)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getNotRecordedDetailList.do")
    public JsonResult getNotRecordedDetailBillList(User user,String begin){
        Map<String,Object> map = new HashMap<>();
        Date beginDate = NewDateUtils.changeMonth(new Date(),0);  //当月的第一天
        Date endDate = new Date();
        if (StringUtils.isNotEmpty(begin)){
            Date begin1 = NewDateUtils.dateFromString(begin,"yyyy-MM-dd");
            beginDate = NewDateUtils.changeMonth(begin1,0);
            endDate = NewDateUtils.getLastTimeOfMonth(begin1);  //某月最后一秒
        }
        List<AccountDetail> accountDetails = dataService.getNotRecordedDetailList(user.getOid(),beginDate,endDate);
        List<FinanceAccountBill> financeAccountBills = dataService.getNotRecordedBillList(user.getOid(),beginDate,endDate);
        map.put("accountDetails",accountDetails);
        map.put("financeAccountBills",financeAccountBills);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 不予做账的票据详情列表【这个是按照票据日期的，和需求确认过】
     * @Date 2022/5/9
     * @param begin 某月中的某一天(年-月-日)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getNoAccountingDetailBillList.do")
    public JsonResult getNoAccountingDetailBillList(User user,String begin,String end){
        Map<String,Object> map = new HashMap<>();
        Date beginDate = NewDateUtils.changeMonth(new Date(),0);  //当月的第一天
        Date endDate = new Date();
        if (StringUtils.isNotEmpty(begin)&&StringUtils.isNotEmpty(end)){
            beginDate = NewDateUtils.today(NewDateUtils.dateFromString(begin,"yyyy-MM-dd"));
            endDate = NewDateUtils.getLastTimeOfDay(NewDateUtils.dateFromString(end,"yyyy-MM-dd"));
        }
        List<AccountDetail> accountDetails = dataService.getNoAccountingDetailList(user.getOid(),beginDate,endDate,false);
        List<FinanceAccountBill> financeAccountBills = dataService.getNoAccountingBillList(user.getOid(),beginDate,endDate,false);
        map.put("accountDetails",accountDetails);
        map.put("financeAccountBills",financeAccountBills);
        return new JsonResult(1,map);
    }

    @ResponseBody
    @RequestMapping("/test.do")
    public JsonResult test(User user){
        Map<String,Object> map = new HashMap<>();

        map = dataService.getTaxMethod(user,4812);
        return new JsonResult(1,map);

    }
}