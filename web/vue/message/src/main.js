import 'element-ui/lib/theme-chalk/index.css'
import TyComponent from '@/components/index'

import Vue from 'vue'
import ElementUI from 'element-ui'
import router from './router/index'
import VueResource from 'vue-resource'
import * as moment from 'moment'
import 'moment/locale/zh-cn'
import App from './App'
import axios from 'axios'
import Vuex from 'vuex'
import pyfl from 'pyfl'
import store from '@/store/index'
import auth from '@/sys/auth'
import initStorage from '@/sys/initStorage'
import sphdSocket from '@/sys/sphd'

axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'
axios.defaults.headers.get['Content-Type'] = 'application/x-www-form-urlencoded'
axios.defaults.transformRequest = [function (data) {
  let ret = ''
  for (let it in data) {
    ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
  }
  return ret.substr(0, ret.length - 1)
}]

Vue.prototype.auth = auth
Vue.prototype.axios = axios
Vue.prototype.pyfl = pyfl
Vue.config.productionTip = false
Vue.use(VueResource)
Vue.use(ElementUI)
Vue.use(TyComponent)
Vue.use(Vuex)

auth.init({Vue: Vue, isGuest: false})
initStorage({})
if(typeof window.parent === 'undefined' || typeof window.parent.sphdSocket === 'undefined') {
  console.log('sphdSocket1111')
  sphdSocket.start()
  Vue.prototype.sphdSocket = sphdSocket
  window.parent.sphdSocket = sphdSocket
} else {
  console.log('sphdSocket2222')
  // console.log('sphdSocket2222', window.parent.sphdSocket, window.parent.sphdSocket.user.userID, window.parent, window.parent.window, window.parent.window.sphdSocket.user.userID)
  Vue.prototype.sphdSocket = window.parent.sphdSocket
}

// 定义一个全局过滤器实现日期格式化
Vue.filter('formatDay', function (input, fmtstring='YYYY-MM-DD HH:mm:ss') {
  // 使用momentjs这个日期格式化类库实现日期的格式化功能
  return moment(input).format(fmtstring)
})

// 定义一个全局过滤器实现字节自送转换
Vue.filter('formatByte', function (size) {
  return  size < 102400 ? parseFloat(size / 1024).toFixed(2) + 'KB' : parseFloat(size / 1048576).toFixed(2) + 'MB'
})

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App),
  watch: {
    $route (from) {
      console.log('main.js from', from)
      let pathArr = []
      let pathStr = window.localStorage.getItem('floating')
      if (pathStr) {
        pathArr = pathStr.split('-')
      }
      pathArr.push(from.path)
      console.log('path', from)
      window.localStorage.setItem('floating', pathArr.join('-'))
    }
  }
})
