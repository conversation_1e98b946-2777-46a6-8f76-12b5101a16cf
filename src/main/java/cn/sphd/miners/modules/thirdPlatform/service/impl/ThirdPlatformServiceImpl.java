package cn.sphd.miners.modules.thirdPlatform.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.dao.AuthAccDao;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.system.dao.OrgDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.thirdPlatform.dao.*;
import cn.sphd.miners.modules.thirdPlatform.entity.*;
import cn.sphd.miners.modules.thirdPlatform.service.TgService;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ContextLoader;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Transactional(isolation=Isolation.DEFAULT,propagation=Propagation.REQUIRED,readOnly = false)
public class ThirdPlatformServiceImpl extends BaseServiceImpl implements ThirdPlatformService, InitializingBean {
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    TpAppDao tpAppDao;
    @Autowired
    TpPlatformDao tpPlatformDao;
    @Autowired
    @Qualifier("wxTgService")
    TgService wxTgService;
    @Autowired
    @Qualifier("ttTgService")
    TgService ttTgService;
    @Autowired
    AuthService authService;
    @Autowired
    TpMemberDao tpMemberDao;
    @Autowired
    TpAppShareDao tpAppShareDao;
    @Autowired
    TpAppMemberDao tpAppMemberDao;
    @Autowired
    TpAppShareMemberDao tpAppShareMemberDao;
    @Autowired
    TpMemberHistoryDao tpMemberHistoryDao;
    @Autowired
    TpMerchantDao tpMerchantDao;
    @Autowired
    TpPayNotifyDao tpPayNotifyDao;
    @Autowired
    TpAppVersionDao tpAppVersionDao;

    @Autowired
    UploadService uploadService;

    private final Map<Byte, TgService> tgServices;

    public ThirdPlatformServiceImpl() {
        tgServices = new HashMap<>(TgCode.values().length);
    }

    @Override
    public void afterPropertiesSet() {
        tgServices.put(TgCode.getInstance("weChat").getIndex(), wxTgService);
        tgServices.put(TgCode.getInstance("byteDance").getIndex(), ttTgService);
    }

    private List<TpPlatform> getTaskTpPlatform(Integer lastId, int limit) {
        String hql = "from TpPlatform where enabled=:enabled and tgCode is not null and id>:id order by id";
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled",Boolean.TRUE);
        params.put("id",lastId);
        return tpPlatformDao.getListByHQLWithNamedParamsLimit(hql,params,limit);
    }
    private List<TpApp> getTaskTpApp(Integer lastId, int limit) {
        String hql = "from TpApp where enabled=:enabled and tgCode is not null and id>:id order by id";
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled",true);
        params.put("id",lastId);
        return tpAppDao.getListByHQLWithNamedParamsLimit(hql,params,limit);
    }

    @Override
    public void tpAccessTokenTask() {
        final int limit = 100;
        Integer lastId = 0;
        Long expiresIn;
        List<TpPlatform> tpPlatforms = getTaskTpPlatform(lastId, limit);
        while (!tpPlatforms.isEmpty()) {
            for (TpPlatform platform : tpPlatforms) {
                if((expiresIn = getTpAccessTokenExp(platform))==null || expiresIn - System.currentTimeMillis() < TimeUnit.MINUTES.toMillis(90)) {//过期时间小于1.5小时重新获取
                    logger.warn("tpAccessTokenTask tp, " + TgCode.getByIndex(platform.getTgCode()).getName() + ", platform.appId=" + platform.getAppId());
                    TgService service = tgServices.get(platform.getTgCode());
                    service.getTpAccessTokenToRedis(platform);
                }
                lastId = platform.getId();
            }
            tpPlatforms = getTaskTpPlatform(lastId, limit);
        }
        lastId = 0;
        List<TpApp> apps = getTaskTpApp(lastId, limit);
        while (!apps.isEmpty()) {
            for (TpApp app : apps) {
                if ((expiresIn = getAppAccessTokenExp(app)) == null || expiresIn - System.currentTimeMillis() < TimeUnit.MINUTES.toMillis(90)) {//过期时间小于1.5小时重新获取
                    logger.warn("tpAccessTokenTask app, " + TgCode.getByIndex(app.getTgCode()).getName() + ", app.appId=" + app.getAppId());
                    TgService service = tgServices.get(app.getTgCode());
                    service.getAppAccessTokenToRedis(app);
                }
                lastId = app.getId();
            }
            apps = getTaskTpApp(lastId, limit);
        }
    }

    @Override
    public void saveTpAccessTokenToRedis(TpPlatform platform, Map<String, Object> tokenMap) {
        Long expiresIn = (Long) tokenMap.get("expiresIn");
        redisTemplate.opsForValue().set("miners:thirdPlatform:tp:token:" + ThirdPlatformService.TgCode.getByIndex(platform.getTgCode()).getAbbr() + ":" + platform.getAppId(), tokenMap, expiresIn, TimeUnit.SECONDS);
        Logger.getLogger(getClass()).info("saveTpAccessToRedis set OK, appId=" + platform.getAppId() + ", platformAccessToken=" + JSON.toJSONString(tokenMap) +", expires="+ NewDateUtils.dateToString(System.currentTimeMillis()+expiresIn*TimeUnit.SECONDS.toMillis(1), "yyyy-MM-dd HH:mm:ss.SSS"));
    }

    @Override
    public boolean saveTpVerifyTicketToRedis(TpPlatform platform, String ticket, Long timeStamp) {
        //ComponentVerifyTicket有效期为CreateTime后12小时
        Long expiresIn = timeStamp + TimeUnit.HOURS.toSeconds(12) - System.currentTimeMillis() / TimeUnit.SECONDS.toMillis(1);
        if(expiresIn > 0) {
            redisTemplate.opsForValue().set("miners:thirdPlatform:tp:ticket:" + ThirdPlatformService.TgCode.getByIndex(platform.getTgCode()).getAbbr() + ":" + platform.getAppId(), ticket, expiresIn, TimeUnit.SECONDS);
            Logger.getLogger(getClass()).info("saveTpVerifyTicketToRedis set OK, appId=" + platform.getAppId() + ", ticket(ComponentVerifyTicket)=" + ticket + ", expires=" + NewDateUtils.dateToString(System.currentTimeMillis() + expiresIn * TimeUnit.SECONDS.toMillis(1), "yyyy-MM-dd HH:mm:ss.SSS"));
            return true;
        } else {
            Logger.getLogger(getClass()).warn("saveTpVerifyTicketToRedis verify error, timeStamp = " + timeStamp + ", expiresIn = " + expiresIn);
            return false;
        }
    }

    @Override
    public void saveAppAccessTokenToRedis(TpApp app, Map<String, Object> tokenMap) {
        Long expiresIn = (Long) tokenMap.get("expiresIn");
        redisTemplate.opsForValue().set("miners:thirdPlatform:mp:token:" + ThirdPlatformService.TgCode.getByIndex(app.getTgCode()).getAbbr() + ":" + app.getAppId(), tokenMap, expiresIn, TimeUnit.SECONDS);
        Logger.getLogger(getClass()).info("saveAppAccessToRedis set OK, appId=" + app.getAppId() + ", appAccessToken=" + JSON.toJSONString(tokenMap) +", expires="+ NewDateUtils.dateToString(System.currentTimeMillis()+expiresIn*TimeUnit.SECONDS.toMillis(1), "yyyy-MM-dd HH:mm:ss.SSS"));

    }

    @Override
    public void tpMerchantTask() {
        final int limit = 100;
        Integer lastId = 0;
        List<TpMerchant> tpMerchants = getTaskTpMerchant(lastId, limit);
        while (!tpMerchants.isEmpty()) {
            for (TpMerchant tpMerchant : tpMerchants) {
                TgService service = tgServices.get(tpMerchant.getTgCode());
                service.initMerchantService(tpMerchant);
                lastId = tpMerchant.getId();
            }
            tpMerchants = getTaskTpMerchant(lastId, limit);
        }
    }
    private List<TpMerchant> getTaskTpMerchant(Integer lastId, int limit) {
        String hql = "from TpMerchant where enabled=:enabled and tgCode is not null and id>:id order by id";
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled",true);
        params.put("id",lastId);
        return tpAppDao.getListByHQLWithNamedParamsLimit(hql,params,limit);
    }

    @Override
    public void wxReceiveAuthEvent(String postData, String signature, String timeStamp, String nonce, String encryptType, String msgSignature) {
        wxTgService.receiveAuthEvent(postData, signature, timeStamp, nonce, encryptType, msgSignature);
    }

    @Override
    public void wxReceivePayNotify(Map<String, Object> params) {
        wxTgService.savePayNotify(params);
    }

    @Override
    public void savePayNotify(TpPayNotify notify) {
        tpPayNotifyDao.save(notify);
    }

    @Override
    public void test() {
        wxTgService.savePayNotify(null);
    }

    @Override
    public String getTpVerifyTicket(TpPlatform platform) {
        String redisKey = "miners:thirdPlatform:tp:ticket:" + TgCode.getByIndex(platform.getTgCode()).getAbbr() + ":" + platform.getAppId();
        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public String getTpAccessToken(TpPlatform platform) {
        String result = null;
        Map<String, Object> tokenObj = getTpAccessTokenMap(platform);
        if (ObjectUtils.isNotEmpty(tokenObj)) {
            result = (String) tokenObj.get("accessToken");
        }
        return result;
    }

    private Long getTpAccessTokenExp(TpPlatform platform) {
        Long result = null;
        Map<String, Object> tokenObj = getTpAccessTokenMap(platform);
        if (ObjectUtils.isNotEmpty(tokenObj)) {
            result = (Long) tokenObj.get("expiresIn");
        }
        return result;
    }

    private Map<String, Object> getTpAccessTokenMap(TpPlatform platform) {
        String redisKey = "miners:thirdPlatform:tp:token:" + TgCode.getByIndex(platform.getTgCode()).getAbbr() + ":" + platform.getAppId();
        return  (Map<String, Object>) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public String getAppAccessToken(TpApp app) {
        String result = null;
        Map<String, Object> tokenObj = getAppAccessTokenMap(app);
        if (ObjectUtils.isNotEmpty(tokenObj)) {
            result = (String) tokenObj.get("accessToken");
        }
        return result;
    }

    private Long getAppAccessTokenExp(TpApp app) {
        Long result = null;
        Map<String, Object> tokenObj = getAppAccessTokenMap(app);
        if (ObjectUtils.isNotEmpty(tokenObj)) {
            result = (Long) tokenObj.get("expiresIn");
        }
        return result;
    }
    public Map<String, Object> getAppAccessTokenMap(TpApp app) {
        String redisKey = "miners:thirdPlatform:mp:token:" + TgCode.getByIndex(app.getTgCode()).getAbbr() + ":" + app.getAppId();
        return (Map<String, Object>) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public TpPlatform getPlatformByAppId(TgCode code, String appId, Boolean enabled) {
        StringBuffer hql = new StringBuffer("from TpPlatform where appId=:appId and tgCode=:tgCode");
        Map<String, Object> params = new HashMap<>();
        params.put("appId", appId);
        params.put("tgCode", code.getIndex());
        if(enabled!=null) {
            hql.append(" and enabled=:enabled");
            params.put("enabled", enabled);
        }
        return (TpPlatform) tpPlatformDao.getByHQLWithNamedParams(hql.toString(),params);
    }
    @Override
    public TpApp getTpApp(TgCode tgCode, String appId, Boolean enabled) {
        StringBuffer hql = new StringBuffer("from TpApp where appId=:appId and tgCode=:tgCode");
        Map<String, Object> params = new HashMap<>(2);
        params.put("appId",appId);
        params.put("tgCode",tgCode.getIndex());
        if(enabled!=null) {
            hql.append(" and enabled=:enabled");
            params.put("enabled", enabled);
        }
        return (TpApp) tpAppDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public List<TpApp> getOtherAppByGroup(TpApp app) {
        if(app == null || app.getGroupId() == null) {
            return new ArrayList<>();
        }
        String hql = "from TpApp where groupId=:groupId and id!=:id and enabled=:enabled";
        Map<String, Object> params = new HashMap<>(3);
        params.put("groupId", app.getGroupId());
        params.put("id", app.getId());
        params.put("enabled",true);
        return tpAppDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public TpMerchant getMerchantByMchid(String mchid) {
        String hql = "from TpMerchant where mchid=:mchid and enabled=:enabled";
        Map<String, Object> params = new HashMap<>(2);
        params.put("mchid", mchid);
        params.put("enabled",true);
        return (TpMerchant) tpMerchantDao.getByHQLWithNamedParams(hql, params);
    }

    @Override
    public JsonResult preOrder(HttpServletRequest request, EcOrder order) {
        TgService service = tgServices.get(order.getTgCode());
        return service.preOrder(request, order);
    }

    @Override
    public JsonResult refundOrder(HttpServletRequest request, EcOrder order) {
        TgService service = tgServices.get(order.getTgCode());
        return service.refundOrder(request, order);
    }

    @Override
    public List<TpPayNotify> getPayNotifyListByNotifyId(String notifyId, NotifyStatus notifyStatus) {
        if(StringUtils.isNotBlank(notifyId)) {
            StringBuffer hql = new StringBuffer("from TpPayNotify where tgNotifyId=:tgNotifyId");
            Map<String, Object> params = new HashMap<>();
            params.put("tgNotifyId", notifyId);
            if(notifyStatus!=null) {
                hql.append(" and notifyStatus=:notifyStatus");
                params.put("notifyStatus", notifyStatus.getIndex());
            }
            return tpPayNotifyDao.getListByHQLWithNamedParams(hql.toString(), params);
        } else {
            return new ArrayList<>(0);
        }
    }

    @Override
    public Triple<TpApp, TpMember, MyException> code2Member(String tgCodeString, String appId, String code, Map<String, String> userInfo) {
        if (StringUtils.isBlank(tgCodeString)) {
            return Triple.of(null, null, new MyException("5", "appId不能为空！"));
        }
        TgCode tgCode = TgCode.getInstance(tgCodeString);
        TpApp app = getTpApp(tgCode, appId, Boolean.TRUE);
        TgService service = tgServices.get(tgCode.getIndex());
        if(app==null || StringUtils.isEmpty(code) || ObjectUtils.isEmpty(userInfo)) {
            logger.warn("code2Member app==null || StringUtils.isEmpty(code) || ObjectUtils.isEmpty(userInfo)");
            logger.warn("app : " + app + " : " + JSON.toJSONString(app));
            logger.warn("code : " + code);
            logger.warn("userInfo : " + userInfo  + " : " + JSON.toJSONString(userInfo));
            logger.warn("tgCodeString : " + tgCodeString);
            logger.warn("appId : " + appId);
        }
        if(app == null) {
            return Triple.of(null, null, new MyException("5", "系统找不到该小程序配置，请检查该appId对应的小程序是否已经录入系统！"));
        } else {
            Pair<TpMember, MyException> result = service.code2Member(app, code, userInfo);
            return Triple.of(app, result.getLeft(), result.getRight());
        }
    }

    @Override
    public JsonResult code2SessionMember(HttpServletRequest request, HttpServletResponse response, String oldToken, String sessionid, String tgCodeString, String appId, String code, Map<String, String> info) {
        Triple<TpApp, TpMember, MyException> code2Member = code2Member(tgCodeString, appId, code, info);
        TpMember member;
        JsonResult result;
        if((member = code2Member.getMiddle()) != null) {
            //登录设置token
            //确认，用户绑定帐号id，重复手机号需要通过短信验证
            Pair<MyException,AuthAcc> pair = authService.setTokenByTpAppMember(request, response, oldToken, sessionid, code2Member.getLeft(), member, info);
            System.out.println("code2SessionMember "+JSON.toJSONString(pair));
            if (pair.getLeft() != null) {
                result = new JsonResult(pair.getLeft());
                if(AuthAcc.realAcc(pair.getRight()) || AuthAcc.lockedAcc(pair.getRight())) {
                    System.out.println("code2SessionMember setData "+JSON.toJSONString(pair.getRight()));
                    result.setData(pair.getRight());
                }
            } else { //登录成功
                result = new JsonResult(1, member);
            }
        } else {
            result = new JsonResult(code2Member.getRight());
        }
        return result;
    }

    @Override
    public JsonResult codeBindAcc(HttpServletResponse response, String tgCodeString, String appId, String code, Map<String, String> userInfo, AuthAcc acc) {
        Triple<TpApp, TpMember, MyException> result = code2Member(tgCodeString, appId, code, userInfo);
        final TpMember member;
        if((member = result.getMiddle()) != null) {
            if (checkBindAcc(member, acc, result.getLeft())) {
                return new JsonResult(new MyException("3", "该微信已绑定了其他Wonderss账号！"));
            } else {
                return bindAcc(member, acc, result.getLeft(),response);
            }
        }
        return new JsonResult(result.getRight());
    }
    private boolean checkBindAcc(TpMember member, AuthAcc acc, TpApp app) {
        Long memberAccId;
        if ((memberAccId = member.getAccId()) != null && !memberAccId.equals(acc.getId())) {//当前TpMember已经绑定acc其他acc
            return true;
        } else {
            //处理同组app
            String unionId;
            if (app.getGroupId() != null && StringUtils.isNotEmpty(unionId = member.getUnionId())) {
                System.out.println("codeBindAcc 新unionId");
                List<TpMember> otherMembers = getGroupOtherAppUnionId(app, unionId);
                for (TpMember otherMember : otherMembers) {
                    if ((memberAccId = otherMember.getAccId()) != null && !memberAccId.equals(acc.getId())) {//同组TpMember已经绑定acc其他acc
                        return true;
                    }
                }
            }
        }
        return false;
    }
    private JsonResult bindAcc(TpMember member, AuthAcc acc, TpApp app, HttpServletResponse response) {
        System.out.println("bindAcc accId = " + acc.getId() + ", member acc = " + member.getAccId() + ", memberId = " + member.getId());
        Set<Long> changedIds = new HashSet<>();
        //处理同组app
        String unionId;
        if(app.getGroupId() != null && StringUtils.isNotEmpty(unionId = member.getUnionId())) {
            System.out.println("codeBindAcc 新unionId");
            List<TpMember> otherMembers = getGroupOtherAppUnionId(app, unionId);
            for (TpMember otherMember : otherMembers) {
                System.out.println("codeBindAcc 同组unionId "+JSON.toJSONString(otherMember));
                otherMember.setAccId(acc.getId());
                changedIds.add(otherMember.getId());
                System.out.println("codeBindAcc 同组unionId changedIds "+JSON.toJSONString(changedIds));
            }
        }
        member.setAccId(acc.getId());
        changedIds.add(member.getId());
        //解绑acc绑定的其他member
        System.out.println("codeBindAcc getAllAndGroupByAcc changedIds "+JSON.toJSONString(changedIds));
        List<TpMember> sameAccOtherMembers = getAllAndGroupByAcc(app, acc, changedIds);
        System.out.println("bindAcc sameAccOtherMembers count = " + sameAccOtherMembers.size() + ",changedIds = " + JSON.toJSONString(changedIds));
        for (TpMember m : sameAccOtherMembers) {
            m.setAccId(null);
            tpMemberDao.update(m);
            System.out.println("getAllAndGroupByAcc end 解绑acc绑定的其他member:  id："+m.getId()+", accId==null："+(m.getAccId()==null)+", JSON： "+JSON.toJSONString(m));
        }
        //更新token
        Map<String, Object> authMap = authService.verifyToken(authService.getTokenFromDefaultResponseRequest(), false, response);
        AuthInfoDto authInfo = (AuthInfoDto) authMap.get("authInfo");
        if(authInfo!=null && (authInfo.logged() || (authInfo.getMemberId() != null && authInfo.getMemberId() > 0L))) {
//            System.out.println("authInfo!=null && authInfo.logged()" + authInfo!=null +" : "+ authInfo.logged());
            if(!authInfo.onlyTpMember()) {//不是第三方登录，token增加TpMember
//                System.out.println("不是第三方登录，token增加TpMember");
                if (authService.setTokenAddTpAppMember(response, authService.getTokenFromDefaultResponseRequest(), member, app)) { //绑定成功
                    TpMemberHistory tpMemberHistory = new TpMemberHistory();
                    BeanUtils.copyPropertiesIgnoreNull(member, tpMemberHistory);
                    tpMemberHistory.setOperation("1");
                    tpMemberHistoryDao.save(tpMemberHistory);
                    return new JsonResult(1, member);
                }
//                System.out.println("token增加TpMember异常");
            } else {//第三方登录但，没有acc，登录
//                System.out.println("第三方登录但，没有acc，登录");
                if (authService.checkAccVerificationDaysAndUpdateToken(response, acc, authMap) == null) {
                    return new JsonResult(new MyException("-3", "登录失败，检查账号是否异常"));//acc "need verification" or "need unlock"
                } else {
                    return new JsonResult(2, sameAccOtherMembers.size());
                }
            }
        }
//        System.out.println("authInfo!=null && authInfo.logged() error " + authInfo==null);
//        System.out.println("authInfo!=null && authInfo.logged() error1 " + JSON.toJSONString(authInfo));
        throw new IllegalArgumentException("token异常！");
    }
    private List<TpMember> getAllAndGroupByAcc(TpApp app, AuthAcc acc, Collection<Long> excludeIds) {
        List<TpMember> result = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(4);
        params.put("tgCode", app.getTgCode());
        params.put("appId", app.getAppId());
        params.put("accId", acc.getId());
        String hql;
        if (ObjectUtils.isNotEmpty(excludeIds)) {
            if (excludeIds.size() == 1) {
                hql = "from TpMember where tgCode=:tgCode and appId=:appId and accId=:accId and id!=:excludeId";
                params.put("excludeId", excludeIds.stream().findFirst().get());
            } else {
                hql = "from TpMember where tgCode=:tgCode and appId=:appId and accId=:accId and id not in (:excludeIds)";
                params.put("excludeIds", excludeIds);
            }
        } else {
            hql = "from TpMember where tgCode=:tgCode and appId=:appId and accId=:accId";
        }
        TpMember member = (TpMember) tpMemberDao.getByHQLWithNamedParams(hql, params);
        System.out.println("getAllAndGroupByAcc 1 " + (member!=null && (ObjectUtils.isEmpty(excludeIds) || ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId()))));
        System.out.println("getAllAndGroupByAcc 1.1 " + (member!=null));
        if(member!=null) {
            System.out.println("getAllAndGroupByAcc 1.2 " + (ObjectUtils.isEmpty(excludeIds) || ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId())));
            System.out.println("getAllAndGroupByAcc 1.3 " + ObjectUtils.isEmpty(excludeIds));
            System.out.println("getAllAndGroupByAcc 1.4 " + (ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId())));
            System.out.println("getAllAndGroupByAcc 1.5 " + ObjectUtils.isNotEmpty(excludeIds));
            System.out.println("getAllAndGroupByAcc 1.6 " + !excludeIds.contains(member.getId()));
            System.out.println("getAllAndGroupByAcc 1.7 " + member.getId());
            System.out.println("getAllAndGroupByAcc 1.8 " + JSON.toJSONString(excludeIds));
        }
        if(member!=null && (ObjectUtils.isEmpty(excludeIds) || ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId()))) {
            result.add(member);
        }
        //处理同组app
        if(app.getGroupId() != null) {
            List<TpApp> otherApps = getOtherAppByGroup(app);
            for (TpApp other : otherApps) {
                params.put("tgCode", other.getTgCode());
                params.put("appId", other.getAppId());
                member = (TpMember) tpMemberDao.getByHQLWithNamedParams(hql, params);
                System.out.println("getAllAndGroupByAcc 2 " + (member!=null && (ObjectUtils.isEmpty(excludeIds) || ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId()))));
                System.out.println("getAllAndGroupByAcc 2.1 " + (member!=null));
                if(member!=null) {
                    System.out.println("getAllAndGroupByAcc 2.2 " + (ObjectUtils.isEmpty(excludeIds) || ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId())));
                    System.out.println("getAllAndGroupByAcc 2.3 " + ObjectUtils.isEmpty(excludeIds));
                    System.out.println("getAllAndGroupByAcc 2.4 " + (ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId())));
                    System.out.println("getAllAndGroupByAcc 2.5 " + ObjectUtils.isNotEmpty(excludeIds));
                    System.out.println("getAllAndGroupByAcc 2.6 " + !excludeIds.contains(member.getId()));
                    System.out.println("getAllAndGroupByAcc 2.7 " + member.getId());
                    System.out.println("getAllAndGroupByAcc 2.8 " + JSON.toJSONString(excludeIds));
                }
                if(member!=null && (ObjectUtils.isEmpty(excludeIds) || ObjectUtils.isNotEmpty(excludeIds) && !excludeIds.contains(member.getId()))) {
                    result.add(member);
                }
            }
        }
        if(!result.isEmpty()) {
            System.out.println("getAllAndGroupByAcc: "+JSON.toJSONString(result));
        }
        return result;
    }


    @Override
    public JsonResult unBindAcc(HttpServletResponse response, String token, String tgCodeString, String appId, AuthAcc acc) {
        if (acc==null) {
            throw new IllegalArgumentException("账号不能为空！");
        }
        TgCode tgCode = TgCode.getInstance(tgCodeString);
        TpApp app = getTpApp(tgCode, appId, Boolean.TRUE);
        if (app==null) {
            throw new IllegalArgumentException("appId不对！");
        }
        TpMember member = getOrGroupByAcc(app, acc);
        if (member == null) {
            throw new IllegalArgumentException("这个账号没绑定过微信！");
        }
        member.setAccId(null);
        //处理同组app
        String unionId;
        if(app.getGroupId() != null && StringUtils.isNotEmpty(unionId = member.getUnionId())) {
            List<TpMember> otherMembers = getGroupOtherAppUnionId(app, unionId);
            for (TpMember otherMember : otherMembers) {
                otherMember.setAccId(null);
            }
        }
        authService.setTokenRemoveTpAppMember(response, token);
//        TpMemberHistory tpMemberHistory=new TpMemberHistory(member.getTgCode(),member.getAppId(),member.getUnionId(),member.getOpenId(),acc.getId(),member.getAvatar(),member.getMobile(),member.getNickName(),acc.getName(),"2");
        TpMemberHistory tpMemberHistory=new TpMemberHistory();
        BeanUtils.copyPropertiesIgnoreNull(member, tpMemberHistory);
        tpMemberHistory.setAccId(acc.getId());
        tpMemberHistory.setOperation("2");
        tpMemberHistoryDao.save(tpMemberHistory);
        return new JsonResult(1, "success");
    }
    @Override
    public JsonResult verificationCodeMpLogin(HttpServletRequest request, HttpServletResponse response, String sessionid, TpMember member, String phone, String code, String sendMethordName, AuthInfoDto authInfo) {
        String name = StringUtils.isNotBlank(member.getNickName()) ? member.getNickName() : phone;
        AuthAcc acc = authService.newEnabledAcc(phone, name, AuthService.Source.mp);
        TpApp app = getTpApp(TgCode.getByIndex(member.getTgCode()), member.getAppId(), Boolean.TRUE);
        if(app!=null) {
            if (checkBindAcc(member, acc, app)) {//是否已绑定
                return new JsonResult(new MyException("3", "该微信已绑定了其他Wonderss账号！"));
            }
            JsonResult result = bindAcc(member, acc, app, response);//绑定
            if(Integer.valueOf(2).equals(result.getSuccess())) {//登录成功
                boolean bound = ((Integer)result.getData()).intValue() > 0;
                //更新头像
                String avatarUrl;
                UploadFile avatar;
                if (StringUtils.isNotEmpty(avatarUrl = member.getAvatar()) && StringUtils.isEmpty(acc.getAvatar())
                        && (avatar = uploadService.copyFileFromUrl(request, avatarUrl, null, acc, null, null, "私人领地")) != null) {
                    authService.updateAuthAvatar(response, authService.getTokenFromDefaultResponseRequest(), acc, avatar.getFilename());//wyu：小程序注册账号，如果新建账号或者原账号领地头像为空，保存小程序第三方头像
                }
                if (bound) {
                    return new JsonResult(2, "换绑成功");//换绑成功
                } else {
                    return new JsonResult(1, "绑定成功");//登录成功
                }
            } else {
                return result;//返回绑定错误。
            }
        } else {
            return new JsonResult(new MyException("5", "系统找不到该小程序配置，请检查该appId对应的小程序是否已经录入系统！"));
        }
    }

    @Override
    public void fillMemberUserInfoWithMap(TpMember member, Map<String, String> userInfo) {
        if (ObjectUtils.isNotEmpty(userInfo)) {
            System.out.println("userInfoMobile 3 fillMemberUserInfoWithMap userInfo:\n" + JSON.toJSONString(userInfo) + "\nmember:\n" + JSON.toJSONString(member));
            String value;
            if (StringUtils.isNotEmpty(value = String.valueOf(userInfo.getOrDefault("nickName", null)))) {
                member.setNickName(value);
            }
            if (StringUtils.isNotEmpty(value = String.valueOf(userInfo.getOrDefault("avatarUrl", null)))) {
                member.setAvatar(value);
            }
            //phoneNumber无法从userInfo中获取，且tt和wx的获取方式不一致
            if (StringUtils.isNotEmpty(value = String.valueOf(userInfo.getOrDefault("phoneNumber", null)))) {
                member.setMobile(value);
            }
        }
    }

    @Override
    public TpMember getByAppUnionId(TpApp app, String unionId, String openId) {
        if(StringUtils.isBlank(unionId) && StringUtils.isBlank(openId)) {
            Logger.getLogger(getClass()).error("getByAppUnionId: unionId 和 openId 不能都为空！");
            throw new IllegalArgumentException("unionId 和 openId 不能都为空！");
        }
        StringBuffer hql = new StringBuffer("from TpMember where appId=:appId and tgCode=:tgCode");
        Map<String, Object> params = new HashMap<>(3);
        params.put("appId",app.getAppId());
        params.put("tgCode",app.getTgCode());
        if(StringUtils.isNotBlank(unionId)) {
            hql.append(" and unionId=:unionId");
            params.put("unionId", unionId);
        } else if(StringUtils.isNotBlank(openId)) {
            hql.append(" and openId=:openId");
            params.put("openId", unionId);
        }
        TpMember member = (TpMember) tpMemberDao.getByHQLWithNamedParams(hql.toString(), params);
        if(member == null && StringUtils.isNotBlank(unionId)) {
            Long accId;
            TpMember otherMember = null;
            //处理同组app
            if(app.getGroupId() != null) {
                List<TpMember> otherMembers = getGroupOtherAppUnionId(app, unionId);
                for (TpMember other : otherMembers) {
                    if((accId = other.getAccId()) != null) {
                        if(authService.getEnabledOrLockedAcc(accId) != null) {
                            otherMember = other;
                            break;
                        } else {//账号不存在或已关闭，清空
                            other.setAppId(null);
                            tpMemberDao.update(other);
                        }
                    }
                }
            }
            member = new TpMember();
            if (otherMember!=null) {
                BeanUtils.copyPropertiesIgnoreNull(otherMember, member);
                member.setId(null);
                member.setCreateTime(null);
                member.setUpdateTime(null);
            }
            member.setTgCode(app.getTgCode());
            member.setAppId(app.getAppId());
            member.setUnionId(unionId);
            member.setOpenId(openId);
            tpMemberDao.save(member);
        }
        if(StringUtils.isNotBlank(unionId) && unionId.equalsIgnoreCase(member.getUnionId())) {
            member.setUnionId(unionId);
        }
        if(StringUtils.isNotBlank(openId) && openId.equalsIgnoreCase(member.getOpenId())) {
            member.setOpenId(openId);
        }
        return member;
    }

    @Override
    public List<TpMember> getGroupOtherAppUnionId(TpApp app, String unionId) {
        List<TpApp> otherApps = getOtherAppByGroup(app);
        if(!otherApps.isEmpty() && StringUtils.isNotEmpty(unionId)) {
            ArrayList<String> hqls = new ArrayList<>(otherApps.size());
            Map<String, Object> params = new HashMap<>(otherApps.size()*3);
            for (TpApp otherApp: otherApps) {
                hqls.add(" unionId=:unionId"+otherApp.getId()+" and appId=:appId"+otherApp.getId()+" and tgCode=:tgCode"+otherApp.getId());
                params.put("unionId"+otherApp.getId(), unionId);
                params.put("appId"+otherApp.getId(), otherApp.getAppId());
                params.put("tgCode"+otherApp.getId(), otherApp.getTgCode());
            }
            String hql = "from TpMember where " + StringUtils.join(hqls, " or ");
            System.out.println("getGroupOtherAppUnionId "+hql);
            System.out.println("getGroupOtherAppUnionId "+JSON.toJSONString(params));
            return tpMemberDao.getListByHQLWithNamedParams(hql, params);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public void updateLoginTpMember(TpMember member) {
        Date now = new Date(System.currentTimeMillis());
        member.setLastVerify(now);
        tpMemberDao.update(member);
        //后续需求要求，可以在此处添加小程序登录历史，如写入 TpMemberLogon（t_tp_member_logon）
    }

    @Override
    public void saveMemberSessionKeyRedis(TpApp app, String code, Map<String, Object> dataMap) {
        TgCode tgCode;
        if(app.getTgCode()!=null && (tgCode = TgCode.getByIndex(app.getTgCode()))!=null && StringUtils.isNotEmpty(code)) {
            String redisKey = "miners:thirdPlatform:memberSessionKey:" +tgCode.getAbbr() + ":" + code;
            redisTemplate.opsForValue().set(redisKey, dataMap, 3, TimeUnit.DAYS);
        }
    }

    @Override
    public Map<String, Object> getMemberSessionKeyRedis(TpApp app, String code) {
        TgCode tgCode;
        if(app.getTgCode()!=null && (tgCode = TgCode.getByIndex(app.getTgCode()))!=null && StringUtils.isNotEmpty(code)) {
            String redisKey = "miners:thirdPlatform:memberSessionKey:" +tgCode.getAbbr() + ":" + code;
            return (Map<String, Object>) redisTemplate.opsForValue().get(redisKey);
        } else {
            return null;
        }
    }

    @Override
    public TpMember getMemberById(Long id) {
        return tpMemberDao.get(id);
    }

    @Override
    public TpMember getMemberByOpenId(TpApp app, String openId) {
        if(app==null || StringUtils.isBlank(openId)) {
            throw new IllegalArgumentException("appId、openId为空或者不存在！");
        }
        String hql = "from TpMember where tgCode=:tgCode and appId=:appId and openId=:openId";
        Map<String, Object> params = new HashMap<>(3);
        params.put("tgCode", app.getTgCode());
        params.put("appId", app.getAppId());
        params.put("openId", openId);
        return (TpMember) tpMemberDao.getByHQLWithNamedParams(hql, params);
    }

    @Override
    public TpMember getOrGroupByAcc(TpApp app, AuthAcc acc) {
        String hql = "from TpMember where tgCode=:tgCode and appId=:appId and accId=:accId";
        Map<String, Object> params = new HashMap<>(3);
        params.put("tgCode", app.getTgCode());
        params.put("appId", app.getAppId());
        params.put("accId", acc.getId());
        TpMember member = (TpMember) tpMemberDao.getByHQLWithNamedParams(hql, params);
        //处理同组app
        if(member == null && app.getGroupId() != null) {
            List<TpApp> otherApps = getOtherAppByGroup(app);
            for (TpApp other : otherApps) {
                params.put("tgCode", other.getTgCode());
                params.put("appId", other.getAppId());
                member = (TpMember) tpMemberDao.getByHQLWithNamedParams(hql, params);
                if(member!=null) {
                    break;
                }
            }
        }
        return member;
    }

    @Override
    public String getMpPath(String module) {
        File file = new File(getBaseMpPath()+"mpapps/"+module);
        if(file.isDirectory()) {
            return file.getAbsolutePath();
        } else {
            return null;
        }
    }

    @Override
    public String getMpUniappPath(String module, TgCode tgCode) {
        File file = new File(getBaseMpPath()+"mpapps/"+module+tgCode.getPath());
        if(file.isDirectory()) {
            return file.getAbsolutePath();
        } else {
            return null;
        }
    }

    private String getBaseMpPath() {
        ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        return sc.getRealPath("/WEB-INF/classes/miniprograms/");
    }

//    @Override
//    public TtCodeSessionDto callingTtcodeSession(String code, TpApp tpApp) {
////        String url = "https://developer.toutiao.com/api/apps/v2/jscode2session";
////        TtDto ttDto = new TtDto();
////        ttDto.setAppId(tpApp.getAppId());
////        ttDto.setSecret(tpApp.getAppSecret());
////        ttDto.setCode(code);
////        String jsonstr = JSON.toJSONString(ttDto);
////        JsonResult jsonResult = MinersHttpClientUtils.postSetBodyAndHead(url,jsonstr);
////        Map<String,Object> result = (Map<String, Object>) jsonResult.getData();
//        TtCodeSessionDto ttCodeSessionDto = new TtCodeSessionDto();
////        ttCodeSessionDto.setUnionid(result.get("unionid").toString());
////        ttCodeSessionDto.setOpenid(result.get("openid").toString());
////        ttCodeSessionDto.setSessionKey(result.get("session_key").toString());
//        return ttCodeSessionDto;
//    }

//    @Override
//    public TpApp getAppByModule(String module) {
//        String hql = " from TpApp where module = :module";
//        HashMap<String, Object> param = new HashMap<>();
//        param.put("module", module);
//        TpApp tpApp = (TpApp) tpAppDao.getByHQLWithNamedParams(hql,param);
//        return tpApp;
//    }

    @Override
    public TpAppShare getTpAppShareSingle(Long shareId) {
        TpAppShare tpAppShare = tpAppShareDao.get(shareId);
        return tpAppShare;
    }

//    @Override
//    public Long getMemberByThreeform(String unionId, String openId) {
//        String hql = "SELECT m.accId FROM TpMember m INNER JOIN TpAppMember am ON am.member = m.id INNER JOIN TpApp a ON am.app = a.id WHERE m.unionId = :unionId AND m.openId = :openId AND a.platformCode = :platformCode";
//        HashMap<String, Object> param = new HashMap<>();
//        param.put("unionId", unionId);
//        param.put("openId", openId);
//        param.put("platformCode", "zijie");
//        Long accId = (Long) tpMemberDao.getByHQLWithNamedParams(hql,param);
//        return accId;
//    }

//    @Override
//    public TpMember getMemberByUnionIdAndopenId(String unionId, String openId) {
//        String hql = "FROM TpMember WHERE unionId = :unionId AND openId = :openId";
//        HashMap<String, Object> param = new HashMap<>();
//        param.put("unionId", unionId);
//        param.put("openId", openId);
//        TpMember tpMember = (TpMember) tpMemberDao.getByHQLWithNamedParams(hql,param);
//        return tpMember;
//    }

//    @Override
//    public TpMember getMemberByAccId(Integer accId) {
//        String hql = "FROM TpMember WHERE accId = :accId";
//        HashMap<String, Object> param = new HashMap<>();
//        param.put("accId", Long.valueOf(accId));
//        TpMember tpMember = (TpMember) tpMemberDao.getByHQLWithNamedParams(hql,param);
//        return tpMember;
//    }

//    @Override
//    public void insertTtMember(String code, Long shareId, String module, Integer accId, String nickName, String avatar, Integer type) {
//        TpApp tpApp = this.getAppByModule(module);
//        TtCodeSessionDto ttCodeSessionDto = this.callingTtcodeSession(code, tpApp);
//        TpMember tpMember = this.getMemberByUnionIdAndopenId(ttCodeSessionDto.getUnionid(),ttCodeSessionDto.getOpenid());
//        if (tpMember != null) {
//            tpMember.setNickName(nickName);
//            tpMember.setAvatar(avatar);
//            tpMember.setAccId(Long.valueOf(accId));
//        }else {
//            tpMember = new TpMember();
//            tpMember.setUnionId(ttCodeSessionDto.getUnionid());
//            tpMember.setOpenId(ttCodeSessionDto.getOpenid());
//            tpMember.setNickName(nickName);
//            tpMember.setAvatar(avatar);
//            tpMember.setAccId(Long.valueOf(accId));
//            tpMemberDao.saveOrUpdate(tpMember);
//        }
//        TpAppMember tpAppMember = new TpAppMember();
////        tpAppMember.setApp(tpApp.getId());
//        tpAppMember.setMember(tpMember.getId());
//        tpAppMember.setRoomRole(type);
//        tpAppMemberDao.saveOrUpdate(tpAppMember);
//        if (shareId != null) {
//            TpAppShare tpAppShare = this.getTpAppShareSingle(shareId);
//            if ("1".equals(tpAppShare.getType())) {
//                TpAppShareMember tpAppShareMember = new TpAppShareMember();
//                tpAppShareMember.setShare(tpAppShare.getId());
//                tpAppShareMember.setMember(tpMember.getId());
//                tpAppShareMemberDao.saveOrUpdate(tpAppShareMember);
//            }
//        }
//    }

//    private void JsapiTicket(AccessToken accessToken, TpApp tpApp) {
//        String url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token="+accessToken.getToken()+"&type=jsapi";
//        HttpClientUtils client = new HttpClientUtils(url);
//        Map<String, String> result = client.jsonResponseToT(client.doGet(null, url, null, null),Map.class);
//        // 如果请求成功
//        if (null != result && null != result.getOrDefault("ticket",null)) {
//            try {
//                JsapiTicket jsapiTicket = new JsapiTicket();
//                jsapiTicket.setTicket(result.get("ticket"));
//                jsapiTicket.setExpiresIn(Long.valueOf(result.get("expires_in")));
//                // wyu：accessToken和jsapiTicket都获取成功才更新
////                tpApp.setAccessToken(accessToken);
////                tpApp.setJsapiTicket(jsapiTicket);
//            } catch (JSONException e) {
//                // 获取token失败
//                System.out.println("获取jsApiTicket失败 errcode:{} errmsg:{}");
//            }
//        }
//    }


    @Override
    public int cleanMembeAccId(AuthAcc acc) {
        String hql="from TpMember where accId=:accId";
        Map<String, Object> params = new HashMap<String, Object>(1) {{
           put("accId", acc.getId());
        }};
        List<TpMember> members = tpMemberDao.getListByHQLWithNamedParams(hql, params);
        for (TpMember member : members) {
            member.setAccId(null);
        }
        return members.size();
    }

    @Autowired
    AuthAccDao authAccDao;
    @Autowired
    UserDao userDao;
    @Autowired
    OrgDao orgDao;
    @Override
    public JsonResult debugRemoveAllTpMemberAndAccUserOrg(HttpServletRequest request) {
        if(GetLocalIPUtils.isDevServer(request)) {
            //List<String> appIds = tpAppDao.getListByHQLWithNamedParams("select appId from TpApp where tpType in (3,4) and enabled=true", null);
            List<TpMember> members = tpMemberDao.getListByHQLWithNamedParams("from TpMember where accId is not null", null);
            int accCount = 0, userCount=0, orgCount=0;
            if(ObjectUtils.isNotEmpty(members)) {
                Long[] accIds = ArrayUtils.toObject(members.stream()/*.filter(member -> !appIds.contains(member.getAppId()))*/.mapToLong(TpMember::getAccId).distinct().toArray());
                members.stream().forEach(member -> {
                    member.setAccId(null);
                });
                if (ObjectUtils.isNotEmpty(accIds)) {
                    Map<String, Object> params = new HashMap<String, Object>() {{
                        put("accIds", accIds);
                    }};
                    //跳过含有三个0的常用调试手机号
                    List<AuthAcc> accs = authAccDao.getListByHQLWithNamedParams("from AuthAcc where state=1 and id in (:accIds) and mobile not like '%000%'", params);
                    accCount = accs.size();
                    List<String> mobiles = accs.stream().map(AuthAcc::getMobile).collect(Collectors.toList());
                    accs.stream().forEach(acc -> acc.SetState(AuthService.State.closed));
                    params = new HashMap<String, Object>() {{
                        put("mobiles", mobiles);
                    }};
                    userCount = userDao.queryHQLWithNamedParams("update User set isDuty=2 where mobile in (:mobiles)", params);
                    List<Organization> orgs = orgDao.getListByHQLWithNamedParams("from Organization where phone in (:mobiles)", params);
                    if(!orgs.isEmpty()) {
                        orgCount = orgs.size();
                        Map<String, Object> oidParams = new HashMap<>(1);
                        for (Organization org : orgs) {
                            org.setState(2);
                            org.setEnabled(false);
                            oidParams.put("oid", org.getId());
                            userCount += userDao.queryHQLWithNamedParams("update User set isDuty=2 where oid =:oid", oidParams);
                        }
                    }
                }
            }
            System.out.println("debugRemoveAllTpMemberAndAccUserOrg 执行成功，修改TpMember表"+members.size()+"条，修改AuthAcc表"+accCount+"条，修改User表"+userCount+"条，修改Org表"+orgCount+"条数据。");
            return new JsonResult(1, "执行成功，修改TpMember表"+members.size()+"条，修改AuthAcc表"+accCount+"条，修改User表"+userCount+"条，修改Org表"+orgCount+"条数据。");
        }
        return new JsonResult(new MyException("-1", "执行失败，只能在主干或者交互环境执行"));
    }


    @Override
    public List<TpMemberHistory> getTpMemberHistories(Long accId) {
        String hql="from TpMemberHistory where accId=:accId";
        Map<String, Object> params = new HashMap<>();
        params.put("accId", accId);
        List<TpMemberHistory> tpMemberHistories=tpMemberHistoryDao.getListByHQLWithNamedParams(hql,params);
        return tpMemberHistories;
    }

    @Override
    public String appTokenKey(String appId, String secretKey, String appVersion, String envType) {
        if (getState(secretKey) && StringUtils.isNotBlank(appId)) {
            String hql = "from TpAppVersion where appId=:appId";
            TpAppVersion appV = (TpAppVersion) tpAppVersionDao.getByHQLWithNamedParams(hql, new HashMap<>(1) {{put("appId", appId);}});
            if (appV !=null) {
                if (StringUtils.isBlank(appV.getAppVersion())
                        || !appV.getAppVersion().equals(appVersion)) {
                    //没有发布，appVersion.appVersion为空或者不匹配
                    return this.generateRandom(10);
                } else {
                    logger.warn("tpAppGetToken.do appId = " + appId + ", secretKey = " + secretKey + ", appVersion = " + appVersion + ", envType = " + envType);
                    //发布中，appVersion匹配。
                    return this.generateRandom(16);
                }
            }
        }
        return this.generateRandom(14);// 调用不正确， 条件：secretKey不合法，appId为空或者找不到AppVersion
    }

    private boolean getState(String secretKey){
        final String wonderss = "wonderss";
        int index=0, found;
        for (int i=0; i< wonderss.length(); i++) {
            char a = wonderss.charAt(i);
            if((found = secretKey.indexOf(a, index)) >= index ) {
                index = found+1;
            } else {
                return false;
            }
        }
        return true;
//        Integer state = 1;
//        final String wonderss = "wonderss";
//        while (state <= wonderss.length()) {
//            Integer secreLenth = secretKey.length();
//            String won = wonderss.substring(state-1,state);
//            Integer al = secretKey.indexOf(won)+1;
//            if (al.compareTo(0) > 0 && al.compareTo(secreLenth) <= 0) {
//                secretKey = secretKey.substring(al);
//                state = state + 1;
//            }else {
//                break;
//            }
//        }
//        return state;
    }
    private String generateRandom(int digitNumber) {
        System.out.println("tpAppGetToken.do generateRandom digitNumber = " + digitNumber);
        return digitNumber <=0 || digitNumber > 32 ? "" : UUID.randomUUID().toString().replace("-", "").substring(32-digitNumber);
//        // 纯数字，String.valueOf(Long.MAX_VALUE).length()==19
//        return digitNumber <=0  && digitNumber > 19 ? "" : String.valueOf((long) (Math.random() * (Math.pow(10, digitNumber))));
//        if (digitNumber == 0) {
//            return "";
//        }
//        String randomStr = String.valueOf((long) ((Math.random() * 9.0 + 1) * (Math.pow(10, digitNumber - 1))));
//        return randomStr;
    }
}