package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.modules.investigate.entity.*;

import java.util.List;

public class ReqInvestigateObject {
    private Integer publish;
    private String hashKey;
    private Integer objective;
    private String investigateAnswerJson;//答案
    private List<InvestigateAnswer> investigateAnswerList;

    public Integer getPublish() {
        return publish;
    }

    public void setPublish(Integer publish) {
        this.publish = publish;
    }

    public String getHashKey() {
        return hashKey;
    }

    public void setHashKey(String hashKey) {
        this.hashKey = hashKey;
    }

    public Integer getObjective() {
        return objective;
    }

    public void setObjective(Integer objective) {
        this.objective = objective;
    }

    public String getInvestigateAnswerJson() {
        return investigateAnswerJson;
    }

    public void setInvestigateAnswerJson(String investigateAnswerJson) {
        this.investigateAnswerJson = investigateAnswerJson;
    }

    public List<InvestigateAnswer> getInvestigateAnswerList() {
        return investigateAnswerList;
    }

    public void setInvestigateAnswerList(List<InvestigateAnswer> investigateAnswerList) {
        this.investigateAnswerList = investigateAnswerList;
    }
}
