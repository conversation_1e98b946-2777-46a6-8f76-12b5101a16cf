package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.schedule.entity.MemoJob;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2019/1/24.
 */
public interface MemoJobService {

    void  updateMemoJob(MemoJob memoJob);

    MemoJob getLastMemoJobBySid(Integer sId);

    MemoJob  addMemoJobByUserSchedule(MemoSchedule memoSchedule);
    Long  addMemoJobByUserScheduleTask(MemoSchedule memoSchedule, MemoJob memoJob, Long runTime, Long now);

    List<MemoJob> getMemoJobListBySid(Integer sId);

    MemoJob getMemoJobById(Integer id);

    void  deleteMemoJob(MemoJob memoJob);

    List<MemoJob> getMemoJobListByDate(Integer userId, Date beginDate, Date endDate, PageInfo pageInfo);

    List<MemoJob> getMemoJobListByUserId(Integer userId,String keyword,PageInfo pageInfo);


}
