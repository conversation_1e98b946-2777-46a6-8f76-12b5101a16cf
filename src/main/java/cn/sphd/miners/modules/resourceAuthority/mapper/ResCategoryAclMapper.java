package cn.sphd.miners.modules.resourceAuthority.mapper;

import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategoryAcl;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by 朱思旭 on 2017/10/31.
 */
@Component
public interface ResCategoryAclMapper extends BaseMapper<ResCategoryAcl> {
    //得到某科目的全部权限
    List<ResCategoryAcl> getAllAuthByCategory(Integer categotyId);

    //删除某科目除creator为0时的人员权限
    Integer delAuthByCategory(Integer categoryId);

    //删除莫科目的权限
    int delAuthByUserAndCategory(ResCategoryAcl rca);

    //删除某科目的全部权限
    Integer delAllAuthByCategory(Integer categoryId);

    //得到某科目的全部UserId
    List<Integer> getAllUserIdsByCategory(Integer categotyId);
    //利用categoryId和userId查询权限是否存在。
    Integer count(ResCategoryAcl rca);
    //查询某文件夹下有权限人的数量
    Integer countAuth(Integer categoryId);
}
