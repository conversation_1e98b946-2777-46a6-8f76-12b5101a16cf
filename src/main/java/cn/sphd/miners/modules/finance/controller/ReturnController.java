package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.finance.service.FinanceReturnService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * Created by Administrator on 2016/12/15.
 * 回款票据
 * 1.156首页之财会3
 */
@Controller
@RequestMapping("/return")
public class ReturnController {
    @Autowired
    FinanceReturnService financeReturnService;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    DataService dataService;
    @Autowired
    PdCustomerService pdCustomerService;

    /**
    *<AUTHOR>
    *@date 2017/1/3 10:07
    *获取所有回款票据
    *type(转账=1  承兑=2)  state【(1-有效,2-存入银行,3-作废，4-已支出) 在此方法中state=1】
    *单位账务明细账票据表为主表，单位回款票据表为副表
    */
    @ResponseBody
    @RequestMapping("/getAllReturn.do")
    public JsonResult getAllReturn(User user, Integer type, String state) {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        int total = 0;
        double amount = 0.0; //总金额
        List<FinanceReturn> financeReturns = financeReturnService.getAllReturn(oid, type , state,null);
        for (FinanceReturn financeReturn:financeReturns) {
            amount = amount + financeReturn.getAmount().doubleValue();  //总金额
            int noLength = financeReturn.getReturnNo().length();
            financeReturn.setReturnNumber(financeReturn.getReturnNo());
            if (noLength>6){
                String returnNo = financeReturn.getReturnNo().substring(noLength-6,noLength);  //汇票号后六位(就列表的需要保留后六位)
                financeReturn.setReturnNumber(returnNo);
            }
            total = financeReturns.size(); //总数量
        }
        map.put("total",total);
        map.put("amount",amount);  //总金额
        map.put("financeReturns",financeReturns);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2016/12/15 15:26
     *获取某个票据的信息(查看详情，换成下面的接口)
     * 单位账务明细账票据表为副表，单位回款票据表为主表
     */
//    @ResponseBody
//    @RequestMapping("/getReturn.do")
//    public void getReturn(Integer returnId,HttpServletResponse response) throws IOException {
//        Map<String,Object> map = new HashMap<>();
//        if (returnId!=null){
//            FinanceReturn financeReturn = financeReturnService.getReturn(returnId);
//            if (financeReturn!=null){
//                FinanceAccountBill financeAccountBill = financeReturnService.getBill(returnId);
//                financeReturn.setBillType(financeAccountBill.getType());
//                financeReturn.setOppositeCorp(financeAccountBill.getOppositeCorp());
//
//                if ("3".equals(financeAccountBill.getSource())){   //来源为销售之回款
//                    SlCustomer slCustomer = pdCustomerService.getPdCustomerBycollectId(financeAccountBill.getBusiness());
//                    financeReturn.setCustomerName(slCustomer.getName());  //客户名称
//                }
//                map.put("source",financeAccountBill.getSource());
//            }else {
//                map.put("source","");
//            }
//            map.put("financeReturn1",financeReturn);
//
//        }
//        ObjectToJson.objectToJson1(map,new String[]{"org","accountDetailHashSet","cheque","financeReturn","financeAccountBillHistoryHashSet",},response);
//    }

    /**
     *<AUTHOR>
     *@date 2016/12/15 15:26
     *获取某个票据的信息(查看详情)【与上面的/getReturn.do表相同，就返回值格式不同，以后用此接口】
     * 单位账务明细账票据表为副表，单位回款票据表为主表
     */
    @ResponseBody
    @RequestMapping("/getReturnDetail.do")
    public JsonResult getReturnDetail(Integer returnId){
        Map<String,Object> map = new HashMap<>();
        FinanceReturn financeReturn = financeReturnService.getReturn(returnId);
        if (financeReturn!=null){
            FinanceAccountBill financeAccountBill = financeReturnService.getBill(returnId,null);
            financeReturn.setBillType(financeAccountBill.getType());
            financeReturn.setOppositeCorp(financeAccountBill.getOppositeCorp());
            financeReturn.setFactDate(financeAccountBill.getFactDate());

            if ("3".equals(financeAccountBill.getSource())){   //来源为销售之回款
                SlCustomer slCustomer = pdCustomerService.getPdCustomerBycollectId(financeAccountBill.getBusiness());
                financeReturn.setCustomerName(slCustomer.getName());  //客户名称
            }
            FinanceAccountBill financeAccountBill1 = financeReturnService.getBill(returnId,"1"); //获取收入得那个来源
            if (financeAccountBill1!=null){
                map.put("source",financeAccountBill1.getSource());
            }else {
                map.put("source","");
            }
        }else {
            map.put("source","");
        }
        map.put("financeReturn",financeReturn);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2016/12/15 15:48
     *存入银行
     * 单位账务明细账票据表为副表，单位回款票据表为主表
     */
    @ResponseBody
    @RequestMapping("/saveBank.do")
    public void saveBank(FinanceReturn financeReturn, Integer returnId , HttpServletResponse response, User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        financeReturnService.saveBank(financeReturn,returnId,user,map);
        ObjectToJson.objectToJson1(map,new String[]{"org","accountId","accountDetailHistories","financeAccountHistories",
                                                     "accountPeriodHashSet","accountDetailHashSet","financeChequeDetailHashSet"},response);
    }

    //-------------------1.156首页之财会3---------------------------------
    /**
     *<AUTHOR>
     *@date 2021/4/16 16:08
     *收到的承兑汇票/转账支票计数
     * type 1-转帐支票，2-承兑汇票
    */
    @ResponseBody
    @RequestMapping("/getReturnNum.do")
    public JsonResult getReturnNum(User user, Integer type){
        Map<String,Object> map = financeReturnService.getReturnNum(user.getOid(),type);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/4/19 16:09
     *查看承兑汇票/转账支票列表【查询/尚在手中/存入银行/又付出去的也用此接口】
     * type 1-转帐支票，2-承兑汇票   state 1-有效,2-存入银行,3-作废，4-已支出
     * beginTime 时间（yyyy-MM-dd）
    */
    @ResponseBody
    @RequestMapping("/getReturnList.do")
    public JsonResult getReturnList(User user, Integer type,String state,String beginTime){
        Map<String,Object> map = new HashMap<>();
        Date dateTime = null;
        Integer yearInteger = null;
        if (StringUtils.isNotEmpty(beginTime)) {
            dateTime = NewDateUtils.dateFromString(beginTime, "yyyy-MM-dd");
            yearInteger = NewDateUtils.getYear(dateTime);

            Date beginDate = NewDateUtils.getNewYearsDay(dateTime);
            Date endDate = NewDateUtils.today();
            if (!NewDateUtils.getYear(beginDate).equals(NewDateUtils.getYear(new Date()))) {  //传过来的年份不是今年
                endDate = NewDateUtils.getLastTimeOfYear(beginDate);
            }
            map.put("beginDate", beginDate);
            map.put("endDate", endDate);
        }
        Map<String,Object> map1 = financeReturnService.getReturnNumByConditional(user.getOid(),type,yearInteger,state);
        List<FinanceReturn> financeReturns = financeReturnService.getAllReturn(user.getOid(),type,state,dateTime);
        for (FinanceReturn financeReturn:financeReturns) {
            FinanceAccountBill financeAccountBill = financeReturnService.getBill(financeReturn.getId());
            if (financeAccountBill!=null){
                financeReturn.setOppositeCorp(financeAccountBill.getOppositeCorp());
                if (financeReturn.getReceiveAccountDate()==null&&financeAccountBill.getReceiveAccountDate()!=null){
                    financeReturn.setReceiveAccountDate(financeAccountBill.getReceiveAccountDate());
                }else if (financeReturn.getReceiveAccountDate()==null&&financeAccountBill.getBusinessDate()!=null){
                    financeReturn.setReceiveAccountDate(financeAccountBill.getBusinessDate());
                }
            }
            int noLength = financeReturn.getReturnNo().length();
            financeReturn.setReturnNumber(financeReturn.getReturnNo());
            if (noLength>6){
                String returnNo = financeReturn.getReturnNo().substring(noLength-6,noLength);  //汇票号后六位(就列表的需要保留后六位)
                financeReturn.setReturnNumber(returnNo);
            }
        }
        map.put("financeReturns",financeReturns);
        map.put("numAmount",map1);  //总金额和总张数
        return new JsonResult(1,map);
    }
    
    /**
     *<AUTHOR>
     *@date 2021/4/21 15:40
     *承兑汇票/转账支票的统计【按收款方统计】
     * String state 1-有效,2-存入银行,3-作废，4-已支出
     * type 1-转帐支票，2-承兑汇票
     * beginTime 时间（yyyy-MM-dd）
    */
    @ResponseBody
    @RequestMapping("/getReturnStatistics.do")
    public JsonResult getReturnStatistics(User user,Integer type,String beginTime){
        Map<String,Object> map = new HashMap<>();
        Date dateTime = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
        map = financeReturnService.getReturnStatistics(user.getOid(),type,"4",dateTime);
        if (!MyStrings.nulltoempty(beginTime).isEmpty()) {
            Date beginDate = NewDateUtils.getNewYearsDay(dateTime);
            Date endDate = NewDateUtils.today();
            if (!NewDateUtils.getYear(beginDate).equals(NewDateUtils.getYear(new Date()))) {  //传过来的年份不是今年
                endDate = NewDateUtils.getLastTimeOfYear(beginDate);
            }
            map.put("beginDate", beginDate);
            map.put("endDate", endDate);
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/4/23 16:18
     *某收款方的票据列表
    */
    @ResponseBody
    @RequestMapping("/getReturnStatisticsList.do")
    public JsonResult getReturnStatisticsList(User user,Integer type,String beginTime,String oppositeCorp,Integer returnId){
        Map<String,Object> map = new HashMap<>();
        Date dateTime = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
        List<FinanceReturn> financeReturns = financeReturnService.getReturnStatisticsList(user.getOid(),type,"4",dateTime,oppositeCorp,returnId);
        map.put("financeReturns",financeReturns);
        if (!MyStrings.nulltoempty(beginTime).isEmpty()) {
            Date beginDate = NewDateUtils.getNewYearsDay(dateTime);
            Date endDate = NewDateUtils.today();
            if (!NewDateUtils.getYear(beginDate).equals(NewDateUtils.getYear(new Date()))) {  //传过来的年份不是今年
                endDate = NewDateUtils.getLastTimeOfYear(beginDate);
            }
            map.put("beginDate", beginDate);
            map.put("endDate", endDate);
        }
        return new JsonResult(1,map);
    }
    
    
    /**
     * <AUTHOR>
     * @Description 统计查询
     * @Date 2022/8/24
     * @param state（1-有效,2-存入银行,3-作废，4-已支出  在此暂时用不上此字段） type（1-转帐支票，2-承兑汇票） beginTime 时间（yyyy-MM-dd）
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/getReturnStatisticState.do")
    public JsonResult getReturnStatisticState(User user, Integer type,String beginTime){
        Map<String,Object> map = new HashMap<>();
        Date dateTime = null;
        if (!MyStrings.nulltoempty(beginTime).isEmpty()) {
            dateTime = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
            Date beginDate = NewDateUtils.getNewYearsDay(dateTime);
            Date endDate = NewDateUtils.today();
            if (!NewDateUtils.getYear(beginDate).equals(NewDateUtils.getYear(new Date()))) {  //传过来的年份不是今年
                endDate = NewDateUtils.getLastTimeOfYear(beginDate);
            }
            map.put("beginDate", beginDate);
            map.put("endDate", endDate);
        }
        map = financeReturnService.getReturnStatisticState(user.getOid(),type,dateTime);
        return new JsonResult(1,map);
    }

    //---------------------1.290公司总览之财务 部分接口，以上有公用的接口---------------------------
    /**
     *@Description 回款票据的修改
     *@auther 李娅星
     *@date 2024/5/20
     *@param id:票据id  category:种类/数据类别 1-贷款,2-借款,3-投资款,4-废品,5-其他 categoryDesc:其他时候的说明 summary:摘要 purpose:用途  billAmount：票面金额(所开具发票或收据的金额) receiveDate收到票据日期
     * operatorName:经手人 memo:备注 payer:付款单位  returnNo:支票号码  amount:金额 expireDate:到期日  bankName:出具的银行 originalCorp:原始出具的单位
    */
    @ResponseBody
    @RequestMapping("/updateReturnDetail.do")
    public JsonResult updateReturnDetail(User user,FinanceReturn financeReturn) {
        Map<String,Object> map = financeReturnService.updateReturnDetail(user,financeReturn);
        return new JsonResult(1,map);
    }

    /**
     *@Description 回款票据的修改记录(以及修改记录的详情)
     *@auther 李娅星
     *@date 2024/5/11
     *@param returnId票据id
    */
    @ResponseBody
    @RequestMapping("/updateReturnRecords.do")
    public JsonResult updateReturnRecords(Integer returnId) {
        Map<String,Object> map = new HashMap<>();
        List<FinanceReturnHistory> financeReturnHistories= financeReturnService.updateReturnRecords(returnId);
        FinanceAccountBill financeAccountBill = financeReturnService.getBill(returnId,null);
        if (financeAccountBill!=null){
            map.put("source",financeAccountBill.getSource()); //来源
        }else {
            map.put("source","");
        }
        map.put("financeReturnHistories",financeReturnHistories);
        return new JsonResult(1,map);
    }

    /**
     *@Description 统计中的存入银行/又付出去的票据的修改（此接口暂时不用，功能暂时不开发）
     *@auther 李娅星
     *@date 2024/7/22
     *@param id:票据id  category:种类/数据类别 1-贷款,2-借款,3-投资款,4-废品,5-其他 categoryDesc:其他时候的说明 summary:摘要 purpose:用途  billAmount：票面金额(所开具发票或收据的金额) receiveDate收到票据日期
     * operatorName:经手人 memo:备注 payer:付款单位  returnNo:支票号码  amount:金额 expireDate:到期日  bankName:出具的银行 originalCorp:原始出具的单位 accountId:存入银行账户id
     * accout：存入银行帐号 depositDate：存入银行日期 depositorName：存入经手人姓名  receiveAccountDate：到帐日期  oppositeCorp：收款单位  factDate：实际付款日期
     */
    @ResponseBody
    @RequestMapping("/updateBankAndPay.do")
    public JsonResult updateBankAndPay(User user,FinanceReturn financeReturn) {
        if (financeReturn.getId()!=null) {
            Map<String, Object> map = financeReturnService.updateBankAndPay(user, financeReturn);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }
}
