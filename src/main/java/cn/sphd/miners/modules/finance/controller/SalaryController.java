package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.entity.BSBean;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.finance.dto.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.SalaryService;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * @ClassName SalaryController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/5/27 7:53
 * @Version 1.0
 * @Update 1.206工作之交付 lyx--2022/4/19
 */
@Controller
@RequestMapping("/salary")
public class SalaryController {
    @Autowired
    SalaryService salaryService;
    @Autowired
    UserService userService;
    @Autowired
    AccountService accountService;
    @Autowired
    AcctReportTaxService acctReportTaxService;
    @Autowired
    OrgService orgService;

    //跳转到工资管理首页
    @RequestMapping("/goSalaryIndex.do")
    public String goSurveyAnswerList() {
        return "/finance/wageManage";
    }

    //工资管理首页
    //status 0为尚无数据，1为需要继续录入，2为完成录入
    @ResponseBody
    @RequestMapping("/salaryIndex.do")
    public SalaryIndex salaryIndex(User user) {
        salaryService.salaryIndexInitialization(user.getOid(),null);
        SalaryIndex salaryIndex=salaryService.annualSalary(user.getOid(),Integer.toString(NewDateUtils.getYear(new Date())));
        List<String> yearList=salaryService.selectYearListByOrg(user.getOid(),Integer.toString(NewDateUtils.getYear(new Date())));
        salaryIndex.setYearList(yearList);
        return salaryIndex;
    }
    //工资管理-按年查询
    @ResponseBody
    @RequestMapping("/salaryByYear.do")
    public SalaryIndex salaryByYear(User user,String year) {
        salaryService.salaryByYearInitialization(user.getOid(),year);
        SalaryIndex salaryIndex=salaryService.annualSalary(user.getOid(),year);
        return salaryIndex;
    }
    //工资管理-按月查询
    @ResponseBody
    @RequestMapping("/salaryByMonth.do")
    public MonthSalary salaryByMonth(User user,String month) {
        MonthSalary monthSalary=salaryService.monthSalaryInfo(user.getOid(),month);
        return monthSalary;
    }
    //工资管理-录入
    //传值，list[{id,salary}]
    //type 类型 1为薪资，2为所得税，3为社保，4公积金
    @ResponseBody
    @RequestMapping("/addMonthSalary.do")
    public RespStatus addMonthSalary(User user, String list,int type) {
        RespStatus respStatus=new RespStatus();
        List<PersonnelSalary> personnelSalaryList = JSONArray.parseArray(list,PersonnelSalary.class);
        int status =salaryService.addMonthSalary(user,personnelSalaryList,type);
        respStatus.setStatus(status);
        return respStatus;
    }
    //工资管理-修改
    //传值，list[{id,salary}]
    //type 类型 1为薪资，2为所得税，3为社保，4公积金
    @ResponseBody
    @RequestMapping("/updateMonthSalary.do")
    public RespStatus updateMonthSalary(User user, String list,int type) {
        RespStatus respStatus=new RespStatus();
        List<PersonnelSalary> personnelSalaryList = JSONArray.parseArray(list,PersonnelSalary.class);
        int status =salaryService.updateMonthSalary(user,personnelSalaryList,type);
        respStatus.setStatus(status);
        return respStatus;
    }
    //工资管理-修改记录
    //type 类型 1为薪资，2为所得税，3为社保，4公积金
    @ResponseBody
    @RequestMapping("/updateMonthSalaryInfo.do")
    public UpdateSalaryInfo updateMonthSalaryInfo(User user, String month,int type) {
        UpdateSalaryInfo updateSalaryInfo =salaryService.updateMonthSalaryInfo(user,month,type);
        return updateSalaryInfo;
    }
    //工资管理-修改记录详情
    @ResponseBody
    @RequestMapping("/updateMonthSalaryDetails.do")
    public MonthSalary updateMonthSalaryDetails(User user, String month,int type,int updateNo) {
        MonthSalary monthSalary =salaryService.updateMonthSalaryDetails(user,month,type,updateNo);
        return monthSalary;
    }
    //工资管理-历年所发工资
    @ResponseBody
    @RequestMapping("/salaryTotalGroupYear.do")
    public List<SalaryTotalByYear> salaryTotalGroupYear(User user) {
        List<SalaryTotalByYear> salaryTotalByYear =salaryService.salaryTotalGroupYear(user);
        return salaryTotalByYear;
    }
    //工资管理-按职工查看工资（全部）
    @ResponseBody
    @RequestMapping("/selectSalaryAllUser.do")
    public SalaryAllUser selectSalaryAllUser(User user) {
        Integer org= user.getOid();
        SalaryAllUser salaryAllUser =salaryService.selectSalaryAllUser(org);
        return salaryAllUser;
    }
    //工资管理-按职工查看（单个）
    @ResponseBody
    @RequestMapping("/selectSalaryByUser.do")
    public SalaryByUser selectSalaryByUser(Integer userId,String year) {
        SalaryByUser salaryByUser =salaryService.selectSalaryByUser(userId,year);
        return salaryByUser;
    }
    //工资管理-初始化年
    @ResponseBody
    @RequestMapping("/salaryInitialization.do")
    public void salaryInitialization(User user,String year) {
        Integer org= user.getOid();
        salaryService.salaryIndexInitialization(org,year);
    }

    //-------------------------以下为1.206工资之交付项目的接口----------------------------------------------

    /**
     * <AUTHOR>
     * @Description 获取工资管理主页列表
     * @Date 2022/4/20
     * @param yearDate 年份(格式 yyyy)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getSalaryList.do")
    public JsonResult getSalaryList(User user,Integer yearDate){
        Map<String,Object> map = new HashMap<>();
        if (yearDate==null){
            yearDate=NewDateUtils.getYear(new Date());
        }
        List<PersonnelPayPeroid> personnelPayPeroids = salaryService.getgetSalaryList(user,yearDate,1,1);
        List<PayPeriodDto> payPeriodDtos = new ArrayList<>();
        Integer yearNew = NewDateUtils.getYear(new Date());  //现在年份
        String beginDate = yearDate+"01";  //开始日期,某年的1月份
        String endDate = yearDate+"12";  //结束日期,某年的12月份
        if (yearNew.equals(yearDate)){
            endDate = NewDateUtils.getYearMonth(new Date()).toString();
        }
        if (personnelPayPeroids.size()>0) {
            while (Integer.parseInt(beginDate)<=Integer.parseInt(endDate)){
                PayPeriodDto payPeriodDto = new PayPeriodDto();
                for (PersonnelPayPeroid personnelPayPeroid : personnelPayPeroids) {
                    if (endDate.equals(personnelPayPeroid.getPeriod())) {
                        payPeriodDto.setOrg(personnelPayPeroid.getOrg());
                        payPeriodDto.setPeriod(personnelPayPeroid.getPeriod());
                        if (1 == personnelPayPeroid.getType().intValue()) {
                            payPeriodDto.setSalaryState(personnelPayPeroid.getState());
                            payPeriodDto.setDateSalary(personnelPayPeroid.getUpdateDate());
                        } else if (2 == personnelPayPeroid.getType().intValue()) {
                            payPeriodDto.setIncomeTaxState(personnelPayPeroid.getState());
                            payPeriodDto.setDateIncomeTax(personnelPayPeroid.getUpdateDate());
                        } else if (3 == personnelPayPeroid.getType().intValue()) {
                            payPeriodDto.setSocialSecurityState(personnelPayPeroid.getState());
                            payPeriodDto.setDateSocialSecurity(personnelPayPeroid.getUpdateDate());
                        } else if (4 == personnelPayPeroid.getType().intValue()) {
                            payPeriodDto.setAccumulationFundState(personnelPayPeroid.getState());
                            payPeriodDto.setDateAccumulation(personnelPayPeroid.getUpdateDate());
                        }

                    }
                }
                payPeriodDtos.add(payPeriodDto);

                Date bean = NewDateUtils.dateFromString(endDate + "01", "yyyyMMdd");  //转化为某月的一号，才可以转化为日期
                endDate = NewDateUtils.getYearMonth(NewDateUtils.changeMonth(bean, -1)).toString(); //获取新的年月【这里的月份+1】
            }
        }
        map.put("payPeriodDtos",payPeriodDtos);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 获取在职/离职职工
     * @Date 2022/4/23
     * @param  isDuty 1-在职 2-离职
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getUsers.do")
    public JsonResult getUsers(User user,String isDuty,String period) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
        List<Integer> orgIntegerList = new ArrayList<>();
        if (fatherOrg){
            orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
        }else {
            orgService.getOidByOrgSonOrg(user.getOid(),user.getOid());
        }
        List<UserDto> userDtos = userService.getUserListByOidIsduty(user.getOid(),orgIntegerList,isDuty,period);
        map.put("users",userDtos);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 手动录入和继续录入
     * @Date 2022/4/22
     * @param userList(包括：userId-职工id factPay-实发金额 isduty-1-在职 2-离职) payTime-发放日期(年月日) payWay(发放方式:1-现金,2-转帐)  pay-实发总额  type(类型1-工资,2-所得税,3-社保,4-公积金)
     * @param period-所属月份(yyyyMM)  accountId-账户id
     * @return
     **/
    @ResponseBody
    @RequestMapping("manualEntry.do")
    public JsonResult manualEntry(User user,String userList,String payTime,Integer payWay,Double pay,Integer type,String period,Integer accountId){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(userList)&&StringUtils.isNotEmpty(payTime)&&payWay!=null&&pay!=null&&type!=null&&StringUtils.isNotEmpty(period)&&accountId!=null){
            Date periodDate = NewDateUtils.dateFromString(period+"01","yyyyMMdd");  //所属月份的第一天
            Integer year = NewDateUtils.getYear(periodDate);
            Integer month = NewDateUtils.getMonth(periodDate);
            String yearmonth = year+"年";
            if (month<10){
                yearmonth=yearmonth+"0"+month+"月";
            }else {
                yearmonth=yearmonth+month+"月";
            }

            byte typeByte = SalaryService.type.salary.getIndex(); //1-工资
            String summary = yearmonth+"的工资";
            if (2==type){
                typeByte = SalaryService.type.incomeTax.getIndex(); //2-所得税
                summary = yearmonth+"的个人所得税";
            }else if (3==type){
                typeByte = SalaryService.type.socialSecurity.getIndex(); //3-社保
                summary = yearmonth+"的社保";
            }else if (4==type){
                typeByte = SalaryService.type.accumulationFund.getIndex(); //4-公积金
                summary = yearmonth+"的公积金";
            }

            Byte payWayByte = salaryService.IntegerToByte(payWay,2);

            map = salaryService.manualEntry(user,userList,NewDateUtils.dateFromString(payTime,"yyyy-MM-dd"),payWayByte,new BigDecimal(pay),typeByte,period,accountId,summary,periodDate);

            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400","操作失败，传值有误"));
        }
    }
    
    /**
     * <AUTHOR>
     * @Description 查看某月录入的工资/个人所得税/社保/公积金
     * @Date 2022/4/24
     * @param type 类型1-工资,2-所得税,3-社保,4-公积金    period-所属月份(yyyyMM)
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/getPersonnelPayMonth.do")
    public JsonResult getPersonnelPayMonth(User user,Integer type,String period) {
        Map<String, Object> map = new HashMap<>();
        if (type!=null&&StringUtils.isNotEmpty(period)) {
            Byte typeByte = salaryService.IntegerToByte(type, 1);
            if (1==type) {
                map = salaryService.getPersonnelPayMonth(user.getOid(), null, typeByte, period, 1);
            }else {
                map = salaryService.getPayByUserList(user,period,typeByte,null,1);
                PersonnelPay personnelPay = salaryService.getPersonnelPay(user.getOid(),period,typeByte,null);
                FinanceAccount financeAccount = accountService.getFinanceAccountById(personnelPay.getFinanceAccount());
                map.put("personnelPay",personnelPay);
                map.put("financeAccount",financeAccount);  //账户信息
            }
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400","操作失败，传值有误"));
        }
    }

    /**
     * <AUTHOR>
     * @Description 按职工查看统计列表
     * @Date 2022/4/26
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPayByUserList.do")
    public JsonResult getPayByUserList(User user,String period,Integer type){
        Byte typeByte = salaryService.IntegerToByte(type,1);
        Map<String,Object> map = salaryService.getPayByUserList(user,period,typeByte,null,1);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 按版本查询某月某次录入的详情列表(查看)
     * @Date 2022/4/26
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPayMonthList.do")
    public JsonResult getPayMonthList(User user,String period,Integer type,Integer versionNo){
        Byte typeByte = salaryService.IntegerToByte(type,1);
        Map<String,Object> map = salaryService.getPayByUserList(user,period,typeByte,versionNo,1);
        PersonnelPay personnelPay = salaryService.getPersonnelPay(user.getOid(),period,typeByte,versionNo);
        FinanceAccount financeAccount = new FinanceAccount();
        if (personnelPay!=null) {
            financeAccount = accountService.getFinanceAccountById(personnelPay.getFinanceAccount());
        }
        map.put("personnelPay",personnelPay);
        map.put("financeAccount",financeAccount);  //账户信息
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 按职工查询某月某次录入的详情列表(查看)
     * @Date 2022/4/29
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPayByUserMonthList.do")
    public JsonResult getPayByUserMonthList(User user,Integer userId,String period,Integer type){
        Byte typeByte = salaryService.IntegerToByte(type,1);
        Map<String,Object> map = salaryService.getPersonnelPayMonth(user.getOid(),userId,typeByte,period,null);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 某月的发放情况修改
     * @Date 2022/4/24
     * @param userList(包括 ：payId-薪资id factPay-实发金额 userId-职工id) payTime-发放日期(年月日) payWay(发放方式:1-现金,2-转帐)  pay-实发总额  type(类型1-工资,2-所得税,3-社保,4-公积金)
     * @param period-所属月份(yyyyMM)  accountId-账户id  versionNo-版本号   operation(5-修改发放信息,6-修改工资数,7-修改支出信息,8-修改职工项)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/updateManual.do")
    public JsonResult updateManual(User user,String userList,String payTime,Integer payWay,Double pay,Integer type,String period,Integer accountId,Integer versionNo,String operation){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(userList)&&StringUtils.isNotEmpty(payTime)&&payWay!=null&&pay!=null&&type!=null&&StringUtils.isNotEmpty(period)&&accountId!=null){
            map = salaryService.updateManual(user,userList,NewDateUtils.dateFromString(payTime,"yyyy-MM-dd"),payWay,BigDecimal.valueOf(pay),type,period,accountId,versionNo,operation);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400","操作失败，传值有误"));
        }
    }

    /**
     * <AUTHOR>
     * @Description 查询修改记录
     * @Date 2022/4/26
     * @param type 1-工资 2-所得税 3-社保 4-公积金
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getRecords.do")
    public JsonResult getRecords(User user,Integer type,String period,Integer versionNo){
        Map<String,Object> map = new HashMap<>();
        Byte typeByte = salaryService.IntegerToByte(type,1);
        List<PersonnelPayHistory> personnelPayHistories = salaryService.getRecords(user.getOid(),typeByte,period,versionNo,null,2);
        map.put("personnelPayHistories",personnelPayHistories);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 修改记录中点击查看某次详情列表
     * @Date 2022/4/27
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getRecordDetails.do")
    public JsonResult getRecordDetails(User user,Integer type,String period,Integer versionNo,Integer subVersionNo){
        Map<String,Object> map = new HashMap<>();
        if (type!=null&&StringUtils.isNotEmpty(period)&&versionNo!=null&&subVersionNo!=null) {
            Byte typeByte = salaryService.IntegerToByte(type, 1);
            Map<String,Object> personnelPayHistories = salaryService.getRecordDetails(user.getOid(), typeByte, period, versionNo, subVersionNo);
            Map<String,Object> personnelPayHistoriesOld = salaryService.getRecordDetails(user.getOid(), typeByte, period, versionNo, subVersionNo-1);
            map.put("personnelPayHistories", personnelPayHistories);
            map.put("personnelPayHistoriesOld", personnelPayHistoriesOld);
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400","操作失败，传值有误"));
        }
    }


    /**
     * <AUTHOR>
     * @Description 数据查看中来源为工资管理的查看详情
     * @Date 2022/6/17
     * @param businessHistory AccountDetail中的【工资的都有历史id查看】
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPayHistory.do")
    public JsonResult getPayHistory(User user,Integer businessHistory){
        PersonnelPayHistory personnelPayHistory = salaryService.getPersonnelPayHistory(user.getOid(),businessHistory,null,null,null,null,null);
        Map<String,Object> map = salaryService.getPayHistoryBySubversionNo(user,personnelPayHistory.getPeriod(),personnelPayHistory.getVersionNo(),personnelPayHistory.getSubVersionNo(),personnelPayHistory.getType());
        FinanceAccount financeAccount = accountService.getFinanceAccountById(personnelPayHistory.getFinanceAccount());
        AccountDetail accountDetail = accountService.getAccountDetailById(personnelPayHistory.getAccountDetail());
        if (accountDetail!=null){
            map.put("summary",accountDetail.getSummary());  //摘要
            map.put("purpose",accountDetail.getPurpose());  //用途
        }else {
            map.put("summary","");  //摘要
            map.put("purpose","");  //用途
        }
        map.put("personnelPayHistory",personnelPayHistory);
        map.put("financeAccount",financeAccount);  //账户信息
        return new JsonResult(1,map);
    }

    //-----------------------1.283首页之职工之工资优化2401 手机端的-----------------------------

    /**
     *@Description  工资主页
     *@auther 李娅星
     *@date 2024/2/29
     *@param yearDate(年份yyyy)
    */
    @ResponseBody
    @RequestMapping("/getSalaryMain.do")
    public JsonResult getSalaryMain(User user,Integer yearDate){
        Map<String,Object> map = new HashMap<>();
        if (yearDate==null){
            yearDate=NewDateUtils.getYear(new Date());
        }
        salaryService.getgetSalaryList(user,yearDate,1,2); //将没添加的添加上
        Integer yearNew = NewDateUtils.getYear(new Date());  //现在年份
        String beginDate = yearDate+"01";  //开始日期,某年的1月份
        String endDate = yearDate+"12";  //结束日期,某年的12月份
        if (yearNew.equals(yearDate)){
            endDate = NewDateUtils.getYearMonth(new Date()).toString();
        }
        map = salaryService.getSalaryList(user.getOid(),beginDate,endDate,SalaryService.type.salary.getIndex());
        return new JsonResult(1,map);
    }

    /**
     *@Description  按职工查看工资
     *@auther 李娅星
     *@date 2024/3/4
     *@param isDuty 1-在职 2-离职
     */
    @ResponseBody
    @RequestMapping("/getSalaryByUser.do")
    public JsonResult getSalaryByUser(User user,Integer isDuty){
        Map<String,Object> map = new HashMap<>();
        if (isDuty==null){
            isDuty = 1;
        }
        map = salaryService.getSalaryByUser(user.getOid(),SalaryService.type.salary.getIndex(),isDuty);
        return new JsonResult(1,map);
    }

    /**
     *@Description  历年工资/某人历年工资
     *@auther 李娅星
     *@date 2024/3/13
     *@param userId-职工id
     */
    @ResponseBody
    @RequestMapping("/getSalaryByYear.do")
    public JsonResult getSalaryByYear(User user,Integer userId){
        Map<String,Object> map = new HashMap<>();
        map = salaryService.getSalaryByYear(user.getOid(),userId,SalaryService.type.salary.getIndex());
        return new JsonResult(1,map);
    }

    /**
     *@Description  某人某年工资
     *@auther 李娅星
     *@date 2024/3/13
     *@param yearDate(年份yyyy) userId-职工id
     */
    @ResponseBody
    @RequestMapping("/getSalaryByUserYear.do")
    public JsonResult getSalaryByUserYear(User user,Integer userId,Integer yearDate){
        Map<String,Object> map = new HashMap<>();
        map = salaryService.getSalaryByUserYear(user.getOid(),userId,yearDate,SalaryService.type.salary.getIndex());
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 修改记录中点击查看详情列表(与上面getRecordDetails.do接口类似)
     * @Date 2022/4/27
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getRecordDetailList.do")
    public JsonResult getRecordDetailList(User user,Integer type,String period,Integer versionNo,Integer subVersionNo){
        Map<String,Object> map = new HashMap<>();
//        if (type!=null&&StringUtils.isNotEmpty(period)&&versionNo!=null&&subVersionNo!=null) {
        if (type!=null&&StringUtils.isNotEmpty(period)) {
            Byte typeByte = salaryService.IntegerToByte(type, 1);
            Map<String,Object> personnelPayHistories = salaryService.getRecordDetailList(user.getOid(), typeByte, period, versionNo, subVersionNo);
            if (subVersionNo!=null){
                subVersionNo = subVersionNo-1;
            }
            Map<String,Object> personnelPayHistoriesOld = salaryService.getRecordDetailList(user.getOid(), typeByte, period, versionNo, subVersionNo);
            map.put("personnelPayHistories", personnelPayHistories);
            map.put("personnelPayHistoriesOld", personnelPayHistoriesOld);
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400","操作失败，传值有误"));
        }
    }


    @ResponseBody
    @RequestMapping("test.do")
    public JsonResult test(User user){
        Map<String,Object> map = new HashMap<>();
        String period="202109";
        Date periodDate = NewDateUtils.dateFromString(period+"01","yyyyMMdd");
        Integer taxId = acctReportTaxService.getTaxId((byte) 2,user.getOid());  //获取税种id
        JsonResult result = acctReportTaxService.getApplyTaxList(user,12,NewDateUtils.today(periodDate),NewDateUtils.today(NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(periodDate,1))));
//        Map<String,Object> map1 = (Map<String, Object>) result.getData();
        List ja = (List) result.getData();
        if (ja.size()>0) {
            BSBean dateBean = (BSBean) ja.get(0);
//            accountDetail.setBusinessKey(taxId);  //税种id
            Integer businessPeriodBegin = Integer.parseInt(NewDateUtils.dateToString(periodDate,"yyyyMMdd"));  //所属月份的1号
            Integer businessPeriodEnd = Integer.parseInt(NewDateUtils.dateToString(NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(periodDate,1)),"yyyyMMdd"));  //所属月份的最后一天
//            accountDetail.setBusinessPeriodBegin(businessPeriodBegin);  //所属日期开始时间
//            accountDetail.setBusinessPeriodEnd(businessPeriodEnd);  //所属日期结束时间

            String startDate = dateBean.getStartDate();  //需申报的起日期
            String endDate = dateBean.getEndDate();   //需申报的止日期
            map.put("businessPeriodBegin",businessPeriodBegin);
            map.put("businessPeriodEnd",businessPeriodEnd);
            map.put("startDate",startDate);
            map.put("endDate",endDate);
        }
        return new JsonResult(1,map);
    }
}
