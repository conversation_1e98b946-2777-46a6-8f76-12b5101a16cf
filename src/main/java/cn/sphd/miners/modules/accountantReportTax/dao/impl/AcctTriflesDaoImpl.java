package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.*;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctReportDetailDto;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTriflesDto;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTriflesDaoImpl extends BaseDao<AcctTriflesEntity, Serializable> implements AcctTriflesDao {

    @Autowired
    AcctTriflesDao triflesDao;
    @Autowired
    AcctTriflesPeriodDao triflesPeriodDao;
    @Autowired
    AcctTriflesHistoryDao triflesHistoryDao;
    @Autowired
    AcctTriflesDetailDao triflesDetailDao;

    @Override
    public List<AcctTriflesEntity> getTriflesList(User user) {
        String hql = "from AcctTriflesEntity where org=:org";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        List<AcctTriflesEntity> list = triflesDao.getListByHQLWithNamedParams(hql,param);
        for (AcctTriflesEntity triflesEntity : list) {
            List<AcctTriflesPeriodEntity> periodList = triflesPeriodDao.getTriflesPeriodList(triflesEntity.getId());
            triflesEntity.setPeriodList(periodList);
        }
        return list;
    }

    @Override
    public List<AcctTriflesDto> getByPeriod(User user, int year, int month) {
        String str = "" + year + "-" + String.format("%02d",month);

        String hql = "select trifles from AcctTriflesPeriodEntity period,AcctTriflesEntity trifles where " +
                "period.org=:org and period.trifles=trifles.id and DATE_FORMAT(trifles.createDate,'%Y-%m')<=:period and period.beginMonth=:beginMonth group by trifles.id";

        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("period", str);
        param.put("beginMonth", month);
        List<AcctTriflesDto> result = new ArrayList<>();
        List<AcctTriflesEntity> list = triflesDao.getListByHQLWithNamedParams(hql,param);
        for (AcctTriflesEntity triflesEntity : list) {
            AcctTriflesDto triflesDto = new AcctTriflesDto();
            Integer triflesId = triflesEntity.getId();
            triflesDto.setTriflesId(triflesId);
            triflesDto.setName(triflesEntity.getName());
//            triflesDto.setLatestDeclare(triflesEntity.getLatestDeclare());
            AcctTriflesHistoryEntity historyEntity = triflesHistoryDao.getLastByReportId(triflesId);
            triflesDto.setTriflesHisId(historyEntity.getId());
            String periodHql = "from AcctTriflesPeriodEntity where trifles=:trifles and beginMonth=:beginMonth";
            HashMap<String, Object> periodParam = new HashMap<>();
            periodParam.put("trifles", triflesId);
            periodParam.put("beginMonth", month);
            List<AcctTriflesPeriodEntity> periodlist = triflesPeriodDao.getListByHQLWithNamedParams(periodHql,periodParam);
            String beginStr = "";
            String endStr = "";
            if (periodlist.size() > 0) {
                AcctTriflesPeriodEntity peiordEntity = periodlist.get(0);
                Integer beginMonth = peiordEntity.getBeginMonth();
                Integer beginDate = peiordEntity.getBeginDate();
                Integer endMonth = peiordEntity.getEndMonth();
                Integer endDate = peiordEntity.getEndDate();
                beginStr = "" + year + "-" + String.format("%02d",beginMonth) + "-" + String.format("%02d",beginDate);
                endStr = "" + year + "-" + String.format("%02d",endMonth) + "-" + String.format("%02d",endDate);
                triflesDto.setStartPeriod(beginStr);//应报开始时间
                triflesDto.setEndPeriod(endStr);//应该结束时间
            }
            result.add(triflesDto);
        }

        return result;
    }

    @Override
    public List<AcctTriflesDto> getListFromDetail(User user, int year, int month) {
        String str = "" + year + "-" + String.format("%02d",month);
        List<AcctTriflesDto> list = new ArrayList<>();
        String hql = "from AcctTriflesDetailEntity where org=:org and period=:period";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("period", str);
        List<AcctTriflesDetailEntity> detaillist = triflesDao.getListByHQLWithNamedParams(hql,param);//得到detail列表
        for (AcctTriflesDetailEntity detailEntity : detaillist) {
            AcctTriflesDto triflesDto = new AcctTriflesDto();
            triflesDto.setTriflesHisId(detailEntity.getTriflesHistory());
            triflesDto.setDetailId(detailEntity.getId());
            Integer triflesId = detailEntity.getTrifles();
            AcctTriflesEntity triflesEntity = triflesDao.get(triflesId);
            triflesDto.setTriflesId(triflesId);//报表ID
//            triflesDto.setFactReportDate(detailEntity.getCreateDate());//实际报税时间
            triflesDto.setFactReportDate(detailEntity.getDelareTime());//实际报税时间
            triflesDto.setCreateDate(detailEntity.getCreateDate());
            triflesDto.setCreateName(detailEntity.getCreateName());
//            reportDetailDto.setFrequency(reportEntity.getFrequency());//频次
//            triflesDto.setLatestDeclare(triflesEntity.getLatestDeclare());//最后编辑时间
            triflesDto.setLatestDeclare(detailEntity.getCreateDate());//最后编辑时间
            Integer hisId = detailEntity.getTriflesHistory();
            AcctTriflesHistoryEntity historyEntity =  triflesHistoryDao.get(hisId);
            if (historyEntity != null) {
                triflesDto.setName(historyEntity.getName());
            }
            list.add(triflesDto);
        }
        return list;
    }

    @Override
    public AcctTriflesEntity getTriflesDetial(User user, int triflesId) {
        AcctTriflesEntity result =  triflesDao.get(triflesId);
        List<AcctTriflesPeriodEntity> periodList = triflesPeriodDao.getTriflesPeriodList(result.getId());
        result.setPeriodList(periodList);
        return result;
    }


}
