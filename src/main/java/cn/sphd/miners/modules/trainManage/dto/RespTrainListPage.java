package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBank;

import java.util.List;

/**
 * @ClassName RespTrainListPage
 * @Description 题库列表
 * <AUTHOR>
 * @Date 2021/2/3 14:58
 * @Version 1.0
 */
public class RespTrainListPage {
    private List<TrainingQuestionBank> trainingQuestionBankList;
    private PageInfo pageInfo;
    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<TrainingQuestionBank> getTrainingQuestionBankList() {
        return trainingQuestionBankList;
    }

    public void setTrainingQuestionBankList(List<TrainingQuestionBank> trainingQuestionBankList) {
        this.trainingQuestionBankList = trainingQuestionBankList;
    }
}
