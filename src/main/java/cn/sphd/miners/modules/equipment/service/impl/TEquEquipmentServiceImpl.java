package cn.sphd.miners.modules.equipment.service.impl;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import cn.sphd.miners.modules.equipment.entity.TEquEquipmentHistory;
import cn.sphd.miners.modules.equipment.mapper.TEquEquipmentHistoryMapper;
import cn.sphd.miners.modules.equipment.mapper.TEquEquipmentMapper;
import cn.sphd.miners.modules.equipment.service.ITEquEquipmentService;
import cn.sphd.miners.modules.mci.entity.TEisIndexHistory;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_装备器具
 * 20230209 1.247装备器具新增
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquEquipmentServiceImpl implements ITEquEquipmentService {
    @Autowired
    private TEquEquipmentMapper tEquEquipmentMapper;

    @Autowired
    private TEquEquipmentHistoryMapper equipmentHistoryMapper;

    /**
     * 查询装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_装备器具
     *           20230209 1.247装备器具新增
     *           主键
     * @return 装备管理_装备器具
     * 20230209 1.247装备器具新增
     */
    @Override
    public TEquEquipment selectTEquEquipmentById(Long id) {
        return tEquEquipmentMapper.selectTEquEquipmentById(id);
    }

    /**
     * 查询装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 列表
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @return 装备管理_装备器具
     * 20230209 1.247装备器具新增
     */
    @Override
    public List<TEquEquipment> selectTEquEquipmentList(TEquEquipment tEquEquipment) {
        return tEquEquipmentMapper.selectTEquEquipmentList(tEquEquipment);
    }

    /**
     * 新增装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int insertTEquEquipment(TEquEquipment tEquEquipment) {

        if (this.checkRe(tEquEquipment))
            return tEquEquipmentMapper.insertTEquEquipment(tEquEquipment);
        else
            return 0;
    }

    public boolean checkRe(TEquEquipment tEquEquipment) {
        TEquEquipment selectedEquipment = new TEquEquipment();
        selectedEquipment.setOrg(tEquEquipment.getOrg());
        List<TEquEquipment> equipments = tEquEquipmentMapper.selectTEquEquipmentList(selectedEquipment);
        for (TEquEquipment e : equipments) {
            if (tEquEquipment.getFullName().equals(e.getFullName())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 修改装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @param user
     * @return 结果
     */
    @Override
    public int updateTEquEquipment(TEquEquipment tEquEquipment, User user) {
        tEquEquipment.setName(tEquEquipment.getFullName());
        TEquEquipment e = tEquEquipmentMapper.selectTEquEquipmentById(tEquEquipment.getId());
        Long version = 0L;
        if (this.checkRe(tEquEquipment)) {
            //判断历史
            TEquEquipmentHistory equipmentHistory = new TEquEquipmentHistory();
            equipmentHistory.setEquipment(tEquEquipment.getId());
            List<TEquEquipmentHistory> tEquEquipmentHistories = equipmentHistoryMapper.selectTEquEquipmentHistoryList(equipmentHistory);

            if (tEquEquipmentHistories == null || tEquEquipmentHistories.size() <= 0) {
                //增加原始信息
                TEquEquipmentHistory init = new TEquEquipmentHistory();
                BeanUtils.copyProperties(e, init);
                init.setEquipment(e.getId());
                init.setVersionNo(version);
                init.setPreviousId(version);
                init.setCreateDate(e.getCreateDate());
                init.setCreator(Long.valueOf(user.getUserID()));
                init.setCreateName(user.getUserName());
                equipmentHistoryMapper.insertTEquEquipmentHistory(init);
            }
            List<TEquEquipmentHistory> nowEquEquipmentHistories = equipmentHistoryMapper.selectTEquEquipmentHistoryList(equipmentHistory);

            TEquEquipmentHistory history = nowEquEquipmentHistories.stream().max(Comparator.comparing(TEquEquipmentHistory::getVersionNo)).get();
            version = history.getVersionNo() + 1;


            //增加修改后的历史


            tEquEquipmentMapper.updateTEquEquipment(tEquEquipment);


            TEquEquipment now = tEquEquipmentMapper.selectTEquEquipmentById(tEquEquipment.getId());
            TEquEquipmentHistory init = new TEquEquipmentHistory();
            BeanUtils.copyProperties(now, init);
            init.setEquipment(e.getId());
            init.setVersionNo(version);
            init.setCreateDate(new Date());
            init.setCreator(Long.valueOf(user.getUserID()));
            init.setCreateName(user.getUserName());
            equipmentHistoryMapper.insertTEquEquipmentHistory(init);

            return 1;

        } else return 0;
    }

    /**
     * 批量删除装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_装备器具
     *            20230209 1.247装备器具新增
     *            主键
     * @return 结果
     */
    @Override
    public int deleteTEquEquipmentByIds(String... ids) {
        return tEquEquipmentMapper.deleteTEquEquipmentByIds(ids);
    }

    /**
     * 删除装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 信息
     *
     * @param id 装备管理_装备器具
     *           20230209 1.247装备器具新增
     *           主键
     * @return 结果
     */
    @Override
    public int deleteTEquEquipmentById(Long id) {
        return tEquEquipmentMapper.deleteTEquEquipmentById(id);
    }


}
