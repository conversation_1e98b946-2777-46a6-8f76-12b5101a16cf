//wyu:自动化测试加id
const addidIsDevDomain = isDevDomain(getDomain(location.href))
let addidInterval = null
if(addidIsDevDomain && !addidInterval) {
    let root = document.body
    // 只能放在window.onload里延时修改，否则无法覆盖element-ui的id值
    window.onload = () => {
        setTimeout(() => {
            forDom(root, window.frameElement?.id ?? "", null)
            addidInterval = setInterval(() => forDom(root, window.frameElement?.id ?? "", null), 5000)
        }, 50)
    }
}
function forDom(item, pid, index) {
    if (item) {
        let id = pid + (index ?? '') + (item.nodeName ?? item.localName ?? 'i').substring(0,1)
        if (item.setAttribute && (!item.id || item.id.includes('el-'))) {//element-plus的默认id不固定，需要重设
            item.setAttribute('id', id)
        }
        if (item.childNodes && item.childNodes.length > 0) {
            let cIndex = 0
            item.childNodes.forEach(cNode => {
                if (cNode.nodeType==1) {
                    forDom(cNode, id, cIndex++)
                }
            })
        }
    }
}
function isDevDomain (domain) {
    const dev_domains = ['wonderss.hongbeibz.com', 'dvm01.btransmission.com', 'dvm02.btransmission.com', 'dvm03.btransmission.com', 'dvm04.btransmission.com', 'dvm05.btransmission.com', 'dlm02.btransmission.com', 'dlm03.btransmission.com']
    let patternIpPort = /^(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)(:([1-9][0-9]{0,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553)){0,1}$/
    return dev_domains.indexOf(domain) > -1 || domain.toLowerCase().includes('frp.btransmission.com') || domain.toLowerCase().includes('localhost') || patternIpPort.test(domain)
}
function getDomain (url) {
    let index
    if (typeof url !== 'string' || url.length <= 0) {
        url = location.href
    }
    if ((index = url.indexOf('//')) >= 0) {
        url = url.substring(index + 2)
    }
    if ((index = url.indexOf('/vue/')) > 0) {
        return url.substring(0, index)
    } else if ((index = url.indexOf('/')) > 0) {
        return url.substring(0, index)
    } else {
        return url
    }
}
// function getPureDomain (url) {
//     let domain = this.getDomain(url)
//     if (typeof domain === 'string') {
//         let index
//         if ((index = domain.indexOf(':')) > 0) {
//             return domain.substring(0, index)
//         }
//     }
//     return domain
// }