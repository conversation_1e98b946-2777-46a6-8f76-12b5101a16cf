package cn.sphd.miners.modules.resourceAuthority.service;

import cn.sphd.miners.modules.resourceAuthority.entity.ResAtt;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCorrelation;
import cn.sphd.miners.modules.resourceAuthority.entity.ResNotice;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.List;

public interface ResNoticeService extends BadgeNumberCallback {

    ResNotice insertResNotice(User user, Integer resId,  Integer resHisId, String bathUuid, Long batchNum, Integer corrId, Integer category, Integer userId, Byte type, String originalPosition, String newPosition);

    //根据type获取通知并根据不同type返回详情
    List<ResNotice> listResNoticeByType(User user, String type);

    //"知道了"或批量“知道了”通知中的文件
    Integer signedResourceByType(User user, Integer id, String type);

    //获取即将消失的通知
    List<ResNotice> listDisappearResNoticeByType(User user,String type);

    //给人员列表加入超管，总经理
    void addSpecialAuthUserId(List<Integer> listUserIDs, User user);

    //推送新增文件通知的方法
    void sendResFileNotice(User user, Integer resId, ResAtt resAtt, Integer corrId, Integer resHisId, String batchUuid,
                           Long batchNum, Integer category, String type, List<Integer> listUserIDs,
                           String originalPosition, String newPosition);

    //获取文件签收记录
    List<ResNotice> getListNotice(Integer userId, Integer resHisId, String batchUuid, String type, String isSigned);

    //获取文件的签收次数
    int resNoticeNumByType(Integer userId, Integer resHisId, String batchUuid, String type, String isSigned);

}
