package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table ( name ="t_tp_official_accounts" )
public class TpOfficialAccounts implements Serializable {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    /**
     * 平台ID
     */
    @Column(name = "platform", nullable=true , unique=false )
    private Long platform;

    /**
     * 机构ID
     */
    @Column(name = "org", nullable=true , unique=false )
    private Integer org;

    /**
     * 公众号
     */
    @Column(name = "official_accounts", length=128, nullable=true , unique=false )
    private String officialAccounts;

    /**
     * 名称
     */
    @Column(name = "name", length=50, nullable=true , unique=false )
    private String name;

    /**
     * 应用ID
     */
    @Column(name = "appid", length=128, nullable=true , unique=false )
    private String appid;

    /**
     * 应用秘钥
     */
    @Column(name = "appsecret", length=128, nullable=true , unique=false )
    private String appsecret;

    /**
     * 代码类型
     */
    @Column(name = "code_type", nullable=true , unique=false )
    private Integer codeType;

    /**
     * 代码
     */
    @Column(name = "code", length=30, nullable=true , unique=false )
    private String code;

    /**
     * 法人微信号
     */
    @Column(name = "legal_persona_wechat", length=50, nullable=true , unique=false )
    private String legalPersonaWechat;

    /**
     * 法人姓名（绑定银行卡)
     */
    @Column(name = "legal_persona_name", length=255, nullable=true , unique=false )
    private String legalPersonaName;

    /**
     * 第三方平台令牌
     */
    @Column(name = "component_access_token", length=255, nullable=true , unique=false )
    private String componentAccessToken;

    /**
     * 第三方联系电话
     */
    @Column(name = "component_phone", length=50, nullable=true , unique=false )
    private String componentPhone;

    /**
     * 二维码地址
     */
    @Column(name = "qr_code", length=255, nullable=true , unique=false )
    private String qrCode;

    /**
     * 头像地址
     */
    @Column(name = "avatar", length=255, nullable=true , unique=false )
    private String avatar;

    /**
     * 联系人
     */
    @Column(name = "contract", length=100, nullable=true , unique=false )
    private String contract;

    /**
     * 电邮
     */
    @Column(name = "email", length=100, nullable=true , unique=false )
    private String email;

    /**
     * 电话
     */
    @Column(name = "phone", length=100, nullable=true , unique=false )
    private String phone;

    /**
     * 关联应用个数
     */
    @Column(name = "app_count", nullable=true , unique=false )
    private Integer appCount;

    /**
     * 备注
     */
    @Column(name = "memo", length=255, nullable=true , unique=false )
    private String memo;

    /**
     * 创建人id
     */
    @Column(name = "creator", nullable=true , unique=false )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name", length=100, nullable=true , unique=false )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable=true , unique=false )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator", nullable=true , unique=false )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name", length=100, nullable=true , unique=false )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time", nullable=true , unique=false )
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlatform() {
        return platform;
    }

    public void setPlatform(Long platform) {
        this.platform = platform;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getOfficialAccounts() {
        return officialAccounts;
    }

    public void setOfficialAccounts(String officialAccounts) {
        this.officialAccounts = officialAccounts;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppsecret() {
        return appsecret;
    }

    public void setAppsecret(String appsecret) {
        this.appsecret = appsecret;
    }

    public Integer getCodeType() {
        return codeType;
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLegalPersonaWechat() {
        return legalPersonaWechat;
    }

    public void setLegalPersonaWechat(String legalPersonaWechat) {
        this.legalPersonaWechat = legalPersonaWechat;
    }

    public String getLegalPersonaName() {
        return legalPersonaName;
    }

    public void setLegalPersonaName(String legalPersonaName) {
        this.legalPersonaName = legalPersonaName;
    }

    public String getComponentAccessToken() {
        return componentAccessToken;
    }

    public void setComponentAccessToken(String componentAccessToken) {
        this.componentAccessToken = componentAccessToken;
    }

    public String getComponentPhone() {
        return componentPhone;
    }

    public void setComponentPhone(String componentPhone) {
        this.componentPhone = componentPhone;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getAppCount() {
        return appCount;
    }

    public void setAppCount(Integer appCount) {
        this.appCount = appCount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
