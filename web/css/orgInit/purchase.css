/*----------------------材料供应商录入-------------------------*/
.section_title{
    margin: 8px 0;
}
.section_title span.title{
    font-size: 14px;
    color: #666;
    font-weight: bold;
    padding: 0 8px;
    line-height: 1;
    border-left: 4px solid #bcdaff;
    display: inline-block;
    width: 200px;
}
/*材料录入*/
.rowTree_avatar{
    display: flex;
    flex-direction: row;
}
.tree_avatar{
    width: 300px;
    flex: none;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 180px);
}
.contentBox_avatar{
    flex: auto;
    padding: 8px 16px;
    background: #f9f9f9;
    margin-left: 8px;
    position: relative;
}
.tree_header{
    padding: 8px;
    background: #e8ebee;
    border-radius: 3px;
    color: #424a55;
}
.tree_footer{}
.tree_footer .handleBtn{
    padding: 8px;
    background: #e8ebee;
    border-radius: 3px;
    height: 36px;
}
.treeList_avatar{
    background: #f9f9f9;
    padding: 8px 0;
    flex: auto;
}
.treeList_avatar li{
    padding: 8px 16px;
    font-size: 13px;
    color: #4d555e;
    cursor: default;
    display: flex;
}
.treeList_avatar li:hover{
    background: #eee;
    color: #252a2f;
}
.treeList_avatar li .catName{
    width: 200px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    flex: none;
}
.treeList_avatar li .catNum{
    width: 20px;
    text-align: center;
    padding: 0;
    flex: none;
}
.treeList_avatar li .ctrlBtn{
    flex: auto;
    text-align: right;
}
.treeList_avatar li .ctrlBtn i{
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
    color: #aaa;
}
.treeList_avatar li:hover .ctrlBtn i.fa-pencil{
    color: #5d9cec;
}
.treeList_avatar li:hover .ctrlBtn i.fa-trash{
    color: #ed5565;
}
.searchBtn{
    height: 30px;
    line-height: 30px;
    background: #5d9cec;
    color: #fff;
    text-align: center;
    border: none;
    cursor: pointer;
    padding: 0 16px;
}
.btnCat{
    padding: 3px 8px;
    border-radius: 3px;
    margin-top: -2px;
    cursor: pointer;
    color: #5d9cec;
    font-size: 13px;
}
.bouncePanel{
    padding: 8px;
}
.bouncePanel-title{
    margin-bottom: 16px;
}
.bouncePanel-title .title{
    color: #333;
    line-height: 1;
    border-left: 4px solid #5d9cec;
    padding: 0 8px;
}
.bouncePanel-content{
    padding-left: 32px;
    color: #3c454d;
}
.item-flex{
    display: flex;
    line-height: 30px;
    margin-bottom: 4px;
    margin-top: 0;
}
.flex-end{
    display: flex;
    justify-content: flex-end;
}
.flex-end .ty-radio{
    margin-right: 0;
    margin-left: 4px;
}
.item-title{
    width: 120px;
    flex: none;
    text-align: left;
    color: #333a40;
    position: relative;
}
.item-title.require:before{
    content: '*';
    color: #ff0000;
    position: absolute;
    left: -8px;
}
.item-content{
    flex: auto;
    padding: 0 4px;
}
.item80{
    width: 80px;
}
.item100{
    width: 100px;
}
.item150{
    width: 150px;
}
.item200{
    width: 200px;
}
.item250{
    width: 200px;
}
.item300{
    width: 300px;
}
.item350{
    width: 350px;
}
.radioLabel{
    display: inline-block;

}
#editPointInfo .radioLabel{
    width: 100px;
    text-align: right;
}
#editMtSupplier .radioLabel{
    width: 150px;
    text-align: left;
}
#editMtSupplier .section_content{
    padding-left: 32px;
}
.btn-group{
    margin: 0;
}
table{
    margin: 0;
}
.hidePart{
    display: none;
}
#editContact .item-title{
    text-align: right;
    width: 110px;
}
#editContact .item-content{
    width: 240px;
    flex:none
}

/*----------------------材料录入-------------------------*/
.go2Cat:hover{ color:#0b94ea; text-decoration:underline; cursor: default;   }
.category{ display: inline-block; margin-right:15px;  }
.exportStep {  padding-left: 40px;  }
.flexRow{  margin-left: 50px;  display: flex;  justify-content:space-between;  width: 356px; align-items: center }
.fileFullName{  width: 278px; line-height:26px;background: #fff;text-align: center; flex: auto}
.ty-btn-middle{padding: 0px 22px;  height: 26px;  line-height: 26px;}
.left{  display: inline-block;  width: 80px;  }
.formItem{  position: relative; margin-top: 8px;  overflow: hidden;  clear: both;  }
.narrowLamp{width: 70%;min-width: 650px;}
.userForm{width: 300px;  margin: 0 auto 20px;}
.clearBtn{position: absolute;  right: 38px;  top: 4px;  width: 14px;  height: 30px;font-style: normal;  color: #b0b0b0;  cursor: pointer;}
.ty-active-blue{border-bottom: 2px solid #0070c0;  color: #0070c0;}
.ty-threeTab{border-bottom: 1px solid #ccc;  font-size: 14px;  line-height: 50px;  height: 50px;}
.ty-threeTab li.ty-active-blue {  color: #0070c0;  }
.ty-threeTab li {  list-style: none;  display: inline-block;  height: 50px;  padding: 0 20px;  cursor: pointer;  }
.gap-lt{margin-left: 50px;}
.gap-Tp{margin-top: 30px;}
.gap-bt{margin-bottom: 10px;}
.mainCon3 {padding-left: 80px;}
.select0, .select1,.select2{padding: 20px 50px;}
.select1,.select2, .noSave1{display: none;}
.bg-yellow thead td {  border-color: #fff0;  background-color: #fff2cc;  }
.noSaved {padding: 20px 40px;}
.btnDo{font-weight: bold;}

.editTip{ margin:10px;border:1px solid #ccc; border-radius:3px; padding:10px 15px; clear: both; margin-top:15px;   }
.narrowBody{margin: 0 auto;width: 80%;}
.changeDot{margin-bottom: 12px;}
.changeDot span{margin-right: 12px;}
.stepItem {  margin-bottom: 20px;  }
.viewBtn .uploadify-button{ padding: 0 22px; min-width: 80px;  margin: 0; border-radius: 0; height: 26px;  line-height: 26px;background-color: #5d9cec;  color: #fff;border: none;}
.viewBtn .uploadify-button:hover{ background: #5d9cec;}
#kindEditName{ width:300px;  }
.line{margin-bottom: 10px;}
.hideItem{
    /*margin-left: 32px;*/
}
.ty-inputText.short{
    width: 100px;
    min-width: inherit;
}
#addSupplier .ty-radio, #changeSupplierBase .ty-radio{
    width: 150px;
}
.flex-box{display: flex;justify-content:space-between;flex-wrap: wrap;}
.flex-box>div{margin-bottom: 12px;}
.flex-box .fieldSm{width:150px;}
.flex-box .fieldMid span{  margin-right: 20px;  }

.address_box{
    padding: 0 8px;
    height: 30px;
    line-height: 30px;
    border: 1px dashed #6a7b8a;
    background: #d8edff;
}
.pagePart{
    display: none;
}

.ty-null{
    text-align: center;
}
.ty-null img{
    width: 64px;
}
.ty-null p{
    color: #aaa;
    font-size: 13px;
}
.form_editRAAMt{
    line-height: 30px;
}
.form_editRAAMt input[type='text'], .form_editRAAMt select{
    width: 100%;
}
.form_editRAAMt .ty-radio input + label{
    top: 10px;
}
.textMax{
    font-size: 12px;
    color: #aaa;
}