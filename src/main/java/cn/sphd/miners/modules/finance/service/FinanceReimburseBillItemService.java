package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillItem;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2019/5/13.
 */
public interface FinanceReimburseBillItemService {

    List<FinanceReimburseBillItem> getFinanceReimburseBillItemListByBillId(Integer billId);

    List<PersonnelReimburse> myReimburseQueryList(Integer userId,Date begin, Date end, Integer approveStatus, Integer feeCat, Integer billCat, HashMap<String,Object> hashMap,Integer applyId);

    void myReimburseQuery(Integer userId,Date begin, Date end, Integer approveStatus, Integer feeCat, Integer billCat, HashMap<String,Object> hashMap,Integer applyId,Integer type);

    List<PersonnelReimburse> getCashierQuery(Integer userId,Date begin, Date end, Integer approveStatus, Integer feeCat, Integer billCat, HashMap<String,Object> hashMap,Integer applyId);
}
