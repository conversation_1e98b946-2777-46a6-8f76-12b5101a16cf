package cn.sphd.miners.modules.generalAffairs.entity;

import java.util.List;

/**
 * @ClassName ReqUserLock
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/3 14:41
 * @Version 1.0
 */
public class ReqUserLock {
    private Integer userId;//被冻结人
    private Integer substitute;//代办人
    private String userList;//manageId,passiveUserId  被冻结人高管id,代办人 ,组成的json串
    private List<SubstituteInfo> list;//转换后的userList

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getSubstitute() {
        return substitute;
    }

    public void setSubstitute(Integer substitute) {
        this.substitute = substitute;
    }

    public String getUserList() {
        return userList;
    }

    public void setUserList(String userList) {
        this.userList = userList;
    }

    public List<SubstituteInfo> getList() {
        return list;
    }

    public void setList(List<SubstituteInfo> list) {
        this.list = list;
    }
}
