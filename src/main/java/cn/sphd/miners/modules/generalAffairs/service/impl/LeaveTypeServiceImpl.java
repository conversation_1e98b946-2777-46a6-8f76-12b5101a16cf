package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.generalAffairs.dao.LeaveTypeDao;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelLeaveType;
import cn.sphd.miners.modules.generalAffairs.service.LeaveTypeService;
import cn.sphd.miners.modules.personal.dao.PersonnelLeaveDao;
import cn.sphd.miners.modules.personal.entity.PersonnelLeave;
import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by Administrator on 2021/4/29.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class LeaveTypeServiceImpl implements LeaveTypeService{

    @Autowired
    LeaveTypeDao leaveTypeDao;
    @Autowired
    UserDao userDao;
    @Autowired
    PersonnelLeaveDao personnelLeaveDao;

    @Autowired
    LeaveService leaveService;

    @Override
    public List<PersonnelLeaveType> getAllLeaveType(Integer oid,Integer enabled) {
        List<PersonnelLeaveType> personnelLeaveTypes = getDefaultLeaveTypes();  //系统默认的
        List<PersonnelLeaveType> personnelLeaveTypesCustomize = getLeaveTypes(oid,enabled);  //自定义的
        if (personnelLeaveTypesCustomize.size()>0){
            personnelLeaveTypes.addAll(personnelLeaveTypesCustomize);  //将自定义的添加到默认的后面
        }
        return personnelLeaveTypes;
    }

    private List<PersonnelLeaveType> getDefaultLeaveTypes() {
        List<PersonnelLeaveType> personnelLeaveTypes = new ArrayList<>();
        for (int i=1;i<=5;i++) {
            PersonnelLeaveType personnelLeaveType = new PersonnelLeaveType();
            String name = "事假";
            Integer id = -1;
            switch (i) {
//                case 1:
//                    name = "事假";
//                    id=-1;
//                    break;
                case 2:
                    name = "病假";
                    id=-2;
                    break;
                case 3:
                    name = "婚假";
                    id=-3;
                    break;
                case 4:
                    name = "丧假";
                    id=-4;
                    break;
                case 5:
                    name = "产假";
                    id=-5;
                    break;
            }
            personnelLeaveType.setName(name);
            personnelLeaveType.setId(id);
            personnelLeaveType.setEnabled(true);
            personnelLeaveType.setIsSystem(true);
            personnelLeaveTypes.add(personnelLeaveType);
        }
        return personnelLeaveTypes;
    }

    @Override
    public List<PersonnelLeaveType> getLeaveTypes(Integer oid,Integer enabled) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelLeaveType where org=:org";
        if (enabled!=null){
            if (1==enabled){
                hql+=" and enabled=true";
            }else if (0==enabled){
                hql+=" and enabled=false";
            }
        }
        map.put("org",oid);
        hql+=" order by enabled desc,createDate asc";
        return leaveTypeDao.getListByHQLWithNamedParams(hql,map);
    }


    @Override
    public Map<String, Object> pauseResumeUse(Integer leaveTypeId, Integer enabled,Integer userId) {
        Map<String,Object> map = new HashMap<>();
        if (leaveTypeId!=null&&enabled!=null){
            PersonnelLeaveType personnelLeaveType = leaveTypeDao.get(leaveTypeId);
            User user = userDao.get(userId);
            if (1==enabled){
                personnelLeaveType.setEnabled(true);
            }else if (0==enabled){
                personnelLeaveType.setEnabled(false);
            }
            personnelLeaveType.setEnabledTime(new Date());
            personnelLeaveType.setUpdateName(user.getUserName());
            personnelLeaveType.setUpdator(userId);
            personnelLeaveType.setUpdateDate(new Date());
            leaveTypeDao.update(personnelLeaveType);
            map.put("content","操作成功");
            map.put("status",1);
        }else {
            map.put("content","某个参数的参数值有误");
            map.put("status",0);
        }
        return map;
    }

    @Override
    public Map<String, Object> deleteLeaveType(Integer leaveTypeId) {
        Map<String, Object> map = new HashMap<>();
        PersonnelLeaveType personnelLeaveType = leaveTypeDao.get(leaveTypeId);
        List<PersonnelLeave> personnelLeaves = getLeaveByType(leaveTypeId);
        if (personnelLeaves.size()>0){
            map.put("content","因为该请假类型已被使用过。");//删除失败
            map.put("status",2);
        }else {
//            Map<String, Object> map1 = new HashMap<>();
//            String hql = "delete from PersonnelLeaveType where id=:id";
//            map1.put("id",leaveTypeId);
//            leaveTypeDao.queryHql(hql,map);
            leaveTypeDao.delete(personnelLeaveType);
            map.put("content","操作成功");//删除失败
            map.put("status",1);
        }
        return map;
    }

    //PersonnelLeave表中的leaveTypeId暂时还没有，等可以添加的时候要添加上
    private List<PersonnelLeave> getLeaveByType(Integer leaveTypeId){
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelLeave where leaveType=:leaveType";
        map.put("leaveType",leaveTypeId);
        return personnelLeaveDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override
    public Map<String, Object> addLeaveType(String name, Integer userId) {
        Map<String, Object> map = new HashMap<>();
        if (!MyStrings.nulltoempty(name).isEmpty()&&userId!=null){
            User user = userDao.get(userId);
            List<PersonnelLeaveType> personnelLeaveTypes = getLeaveByName(user.getOid(),name);
            if (name.equals("事假")||name.equals("病假")||name.equals("丧假")||name.equals("产假")||name.equals("婚假")){
                map.put("content","因为系统中已有该请假类型。");
                map.put("status",2);
            }else if (personnelLeaveTypes.size()>0){
                map.put("content","因为系统中已有该请假类型。");
                map.put("status",2);
            }else {
                PersonnelLeaveType personnelLeaveType = new PersonnelLeaveType();
                personnelLeaveType.setName(name);
                personnelLeaveType.setEnabled(true);
                personnelLeaveType.setIsSystem(false);
                personnelLeaveType.setCreateName(user.getUserName());
                personnelLeaveType.setCreator(userId);
                personnelLeaveType.setCreateDate(new Date());
                personnelLeaveType.setOrg(user.getOid());
                leaveTypeDao.save(personnelLeaveType);
                map.put("content", "操作成功");
                map.put("status",1);
            }
        }else {
            map.put("content","传参值为空");
            map.put("status",0);
        }
        return map;
    }

    private List<PersonnelLeaveType> getLeaveByName(Integer oid,String name){
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelLeaveType where org=:org";
        map.put("org",oid);
        if (!MyStrings.nulltoempty(name).isEmpty()){
            hql+=" and name=:name";
            map.put("name",name);
        }
        return personnelLeaveDao.getListByHQLWithNamedParams(hql,map);
    }
}
