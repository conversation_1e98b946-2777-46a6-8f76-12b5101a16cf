import { fileURLToPath, URL } from 'url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default ({command, mode}) => {
  const envConfig = loadEnv(mode, './', ['VITE_', 'VUE_'])
  console.log('envConfig',envConfig)
  let config = {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    define: {
      'process.env': envConfig,
      'wonderss': {
        'module': '1.202',
        'mchid': '1501736921'
      }
    },
    base: './',
    publicPath: './',
    build: {
      chunkSizeWarningLimit: 65536,
      target: [ 'es2022' ]
    }
  }
  return defineConfig(config)
}
