<template>
  <div id="invoiceRegisterDetailInfo">
    <TY_NavTop title="开票登记" @toggleList="search"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="flexc"><span class="blockspan em11">{{info.name}}</span><span class="blockspan em9">金额：{{info.allAmount | numberFilter(2)}}元</span></div>
          <div class="flexc"><span class="blockspan em9">发票种类：{{info.invoice_category | filterInvoiceType}}</span>
            <span class="blockspan em9" v-if="info.invoice_category=== 1"> 税额：{{ info.rateAmount| numberFilter(2) }}元</span></div>
          <div class="flexc">
            <div>
              <span class="linkbtn" @click="goInvoiceInfo(info.customer_id)">开票资料</span>
            </div>
            <div>
              <div><span class="namec">申请人：{{info.create_name}}</span><span> {{info.create_date | formatDate}}</span></div>
              <div v-if="info.update_name"><span class="namec">审批人：{{info.update_name}}</span><span> {{info.update_date | formatDate}}</span></div>
              <div v-if="info.auditor_name"><span class="namec">开票人：{{info.auditor_name}}</span><span>{{info.audit_date | formatDate}}</span></div>
            </div>
          </div>
        </div>
      </div>
      <p style="height: 15px;"></p>
      <table class="table">
        <thead>
        <td>商品</td>
        <td>单位</td>
        <td>数量</td>
        <td>单价</td>
        <td>金额</td>
        <td v-if="info.invoice_category==1">税率</td>
        <td v-if="info.invoice_category==1">税额</td>
        </thead>
        <tbody>
        <tr v-for="(item, index) in list" v-bind:key="index">
          <td>{{item.name}}</td>
          <td>{{item.unit}}</td>
          <td>{{item.item_quantity}}</td>
          <td>{{item.item_price}}</td>
          <td>{{item.item_amount | numberFilter(2)}}</td>
          <td v-if="info.invoice_category==1">{{item.tax_rate}}%</td>
          <td v-if="info.invoice_category==1">{{item.invoice_amount | numberFilter(2) }}</td>
        </tr>
        </tbody>
      </table>
      <p style="height:10px;"></p>
        <div class="ui-message">
          <div class="sendContainer">
            <el-form :label-position="left" :rules="rules" label-width="130px" ref="ticketInfo">
              <p>此处可对发票号码或开票日期进行修改</p>
              <el-form-item label="发票号码" prop="no1">
                <el-select v-model="ticketInfo.no" filterable @blur="selectBlur" placeholder="请选择" size="small" >
                  <el-option
                    v-for="item in tickets"
                    :key="item.id"
                    :label="item.invoiceNo"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="发票上的开票日期" prop="date1">
                <el-date-picker type="date" placeholder="请选择" value-format="yyyy-MM-dd" v-model="ticketInfo.date" size="small"></el-date-picker>
              </el-form-item>
              <p style="height:10px;"></p>
              <div v-if="type===0">
                <p v-if="oralNo === ticketInfo.no" >仅修改系统中“发票上的开票日期”时，点击“确定”后修改即生效</p>
                <el-form-item prop="radio" v-else label-width="0" class="wrapLab">
                  <el-radio-group v-model="ticketInfo.reason">
                    <el-radio :label="1">所开的{{info.invoice_no}}发票无问题，不作废，本次修改仅系手误</el-radio><br>
                    <el-radio :label="2">所开的{{info.invoice_no}}发票的发票联已丢失</el-radio><br>
                    <el-radio :label="3">所开的{{info.invoice_no}}发票有问题，需作废</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
            <p style="height:20px;"></p>
            <div class="centerBtn">
              <input type="submit" :disabled="isOk" class="ui-btn ui-btn_info" value="确定" @click="submitTicket()">
            </div>
          </div>
        </div>
    </div>
  </div>
</template>
<style lang="less">
  #invoiceRegisterDetailInfo{
    .namec{ display: inline-block; width:96px; text-align: left; overflow: hidden;white-space: nowrap;text-overflow: ellipsis; }
    .flexc{
      .em11{ font-size: 1.1em; }
      .em9{ font-size: 0.8em; }
      .blockspan{ display: block; flex: 1; line-height: 30px;  }
      &>.blockspan:nth-child(1){ flex:2;  }
      &>.blockspan:nth-child(2){ flex:1;  }

      display: flex;
      .linkbtn{ color:#0a9bee; cursor: default; font-size: 15px; }
      .linkbtn:hover{ text-decoration: underline; }
      &>div:nth-child(1){ flex:2;  }
      &>div:nth-child(2){ flex:4; font-size: 0.7em; text-align: right;  }
    }
    .wid{ display: inline-block; width: 30px; }
    .btndisabled{ color:red; }
    .centerBtn{
      text-align: center;
      .ui-btn{
        display: inline-block;
        border-radius: 15px;
        width: 70%;
        height: auto;
        line-height:1;
        vertical-align: middle;
        padding:8px 0 ;
      }
    }
    .wrapLab .el-radio__label{
      white-space: pre-wrap;
    }
  }
</style>
<script>
import { formatDate, filterInvoiceType, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceRegisterDetailInfo',
  data () {
    return {
      list: [],
      tickets: [],
      loading: true,
      info: {},
      listeners: [],
      type: 0,
      ticketInfo: { 'no': '', 'date': '' },
      rules: {
        no: [
          { required: true }
        ],
        date: [
          { required: true }
        ],
        reason: [
          { required: true }
        ]
      }
    }
  },
  filters: {
    formatDate,
    numberFilter,
    filterInvoiceType
  },
  created () {
    let _this = this
    _this.itemId = _this.$route.params.id
    _this.invoiceId = _this.$route.params.invoiceId
    _this.listeners = [
      // 已批准待开票
      _this.sphdSocket.subscribe('applyInvoice', _this.subscribeCallBack, null, 'user'),
      // 已开票未登记
      _this.sphdSocket.subscribe('invoiceRegister', _this.subscribeCallBack, null, 'user'),
      // 填完发票号的回调
      _this.sphdSocket.subscribe('invoiceHandle', _this.handleCallback, null, 'user')
    ]
    // 数据初始化
    _this.getTicketsList()
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    selectBlur:function (e) {
      console.log("selectBlur")
      let _this = this
      if(e.target.value){
        let isOk = false
        this.tickets.forEach(function (item) {
          if(item.invoiceNo === e.target.value){
            _this.ticketInfo.no = item.id
            isOk = true
            _this.chargeTick(_this.ticketInfo.no)
          }
        })
      }else{
        _this.ticketInfo.no = ""
      }
    },
    goInvoiceInfo: function (cusID) {
      let _this = this
      _this.$router.push({
        name: `invoiceRegisterDetailInfoInvoice`,
        params: {
          'id': cusID
        }
      })
    },
    // 获取发票详情
    getInfo: function () {
      let _this = this
      let f_param = {
                    'itemId': _this.invoiceId,
                    'applicationId': _this.itemId
                   }
                   console.log("opppppp:" + JSON.stringify(_this.$route.params))
      if ((_this.$route.params.division_way === 2 || _this.$route.params.division_way === '2') && _this.$route.params.fid && _this.$route.params.fid !== undefined) {
              f_param.fid = _this.$route.params.fid
            }
      // 获取详情
      _this.$http.post('../../../sale/getItemDetailsById.do',f_param , {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        _this.list = res.data
        _this.info = res.base[0]
        if(Number(_this.info.invoice_category) === 1){
          let sum = 0
          let rateSum = 0
          _this.list.forEach(function (im) {
            sum += (im.item_amount)
            rateSum += (im.invoice_amount)
          })
          _this.info.allAmount = sum
          _this.info.rateAmount = rateSum
          console.log('info', _this.info)

        }else{
          _this.info.allAmount = _this.info.invoice_amount
        }
        _this.oralNo = _this.info['invoice_id']
        _this.oralDate = _this.info['operate_date'] ? formatDate(_this.info['operate_date']) : ''
        _this.ticketInfo.no = _this.info['invoice_id'] ? _this.info['invoice_id'] : ''
        _this.ticketInfo.date = _this.info['operate_date'] ? formatDate(_this.info['operate_date']) : ''
        _this.type = _this.ticketInfo.no ? 0 : 1
        if (_this.info['invoice_id']) {
          _this.tickets.push({ 'id': _this.info['invoice_id'], 'invoiceNo': _this.info['invoice_no'] })
        }
        _this.loading = false
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
        _this.$router.push({
          path: `/invoiceRegisterDetail/${_this.itemId}`
        })
      })
    },
    // 获取发票列表 ticket
    getTicketsList: function () {
      let _this = this
      let invoiceCategory = Number(_this.$route.params.invoiceCategory)
      if(invoiceCategory == 2 || invoiceCategory == 3){
        invoiceCategory = 4
      }
      _this.$http.post('../../../invoice/getInvoices.do', {
        'type': invoiceCategory
      }, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        _this.tickets = res['invoiceDetails']
        _this.getInfo()
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '获取发票列表失败，请重试！'
        })
      })
    },
    // 提交选择的发票号
    submitTicket: function () {
      let _this = this
      let data = {}
      let userId = this.sphdSocket.user.userID
      let sessionid = this.sphdSocket.sessionid
      let send = true
      if (_this.ticketInfo.no) {} else { send = false }
      if (_this.ticketInfo.date) {} else { send = false }
      if (_this.type === 1) { // 新增
        data = {
          'saleId': _this.info.creator,
          'userId': userId,
          'sessionid': sessionid,
          'applicationItemIds': _this.invoiceId, // 申请单明细id
          'financeInvoiceId': _this.ticketInfo.no, // 发票id
          'date': _this.ticketInfo.date, // 开票日期 格式：yyyy-MM-dd
          'invoiceCategory': _this.info.invoice_category,
          'amount': _this.info.invoice_amount, // amount ：金额
          'line': _this.list.length, // 行数
          'state': 2 // '状态:1-空白,2-已使用,3-作废,4-启用'
        }
      } else { // 修改
        data = {
          'saleId': _this.info.creator,
          'userId': userId,
          'sessionid': sessionid,
          'applicationItemIds': _this.invoiceId, // 申请单明细id
          'financeInvoiceId': _this.ticketInfo.no, // 发票id
          'date': _this.ticketInfo.date, // 开票日期 格式：yyyy-MM-dd
          'amount': _this.info.invoice_amount // amount ：金额
        }
        if (_this.oralNo !== _this.ticketInfo.no) {
          console.log('reason=', _this.ticketInfo.reason)
          if (_this.ticketInfo.reason === 1) {
            data['operation'] = 5
            data['oldFid'] = _this.oralNo
            data['state'] = 1
            data['reason'] = `所开的${_this.info.invoice_no}发票无问题，不作废，本次修改仅系手误`
            data['losted'] = ''
          } else if (_this.ticketInfo.reason === 2) {
            data['operation'] = ''
            data['oldFid'] = _this.oralNo
            data['state'] = 2
            data['reason'] = `所开的${_this.info.invoice_no}发票的发票联已丢失`
            data['losted'] = 1
          } else if (_this.ticketInfo.reason === 3) {
            data['operation'] = 4
            data['oldFid'] = _this.oralNo
            data['state'] = 3
            data['reason'] = `所开的${_this.info.invoice_no}发票有问题，需作废`
            data['losted'] = ''
          } else {
            send = false
          }
        }
      }
      if (send) {
        if (_this.$route.params.division_way === '2') {
          data.amount = Number(_this.info.allAmount) + Number(_this.info.rateAmount)
        }
        _this.loading = true
        console.log('财务提交的数据：', data)
        _this.sphdSocket.send('invoiceApplicationHandle', data)
      } else {
        _this.$message({
          type: 'error',
          message: '请将各项内容填写完整再提交'
        })
      }
    },
    subscribeCallBack: function (res) {
      console.log('回调详情：', res)
    },
    // 订阅选择发票号通道的回调
    handleCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('选择发票号', res)
      let userId = this.sphdSocket.user.userID
      if (res['state'] === '5') { // 前面确定开票
        // 状态：0-暂存,1-录入,2-提交,3审批通过,4-暂缓开票,5-财务开票,6-待发出(核对),7-发出登记,8-货运,9-签收
        _this.$message({
          type: 'success',
          message: '其他财务人员已对此发票申请开票'
        })
      } else { // 后面选发票号的
        if (res['auditor'] === userId) { // 本人看到
          _this.$message({
            type: 'success',
            message: '操作成功！'
          })
          _this.$router.push({
            path: `/invoiceRegisterDetail/${_this.itemId}`
          })
        } else { // 看到其他财务操作了
          _this.$message({
            type: 'success',
            message: '其他财务人员已填写该发票的发票号'
          })
          _this.getInfo()
        }
      }
    },
    // 校验选择的发票是否合法
    chargeTick: function (ticketID) {
      console.log("校验选择的发票是否合法")
      let _this = this
      _this.tickets.forEach(function (item) {
        if (ticketID === item['id']) {
          if (item['teminateLabel'] && Number(item['state']) === 2) {
            let isOk = true
            // 核对行数
            if (_this.list.length !== item['line']) {
              isOk = false
            }
            // 核对金额
            if (_this.info.invoice_amount !== item['amount']) {
              isOk = false
            }
            // 接收单位
            if (_this.info.customer_id !== item['receiveCorpId']) {
              isOk = false
            }
            if (!isOk) {
              _this.ticketInfo.no = _this.info['invoice_id'] ? _this.info['invoice_id'] : ''
              _this.$message({
                type: 'error',
                message: '该发票为已终止申请使用过的，部分信息不一致，不能使用！'
              })
            }
          }
        }
      })
    }
  }
}
</script>
