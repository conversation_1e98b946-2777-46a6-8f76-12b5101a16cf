package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.generalAffairs.dto.AddressBookDto;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.socket.service.MqMobileDeviceService;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.UserHistoryService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.system.service.impl.UserHistoryUsing;
import cn.sphd.miners.modules.system.service.impl.UserUsing;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import io.netty.util.internal.StringUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;

/**
 * Created by Administrator on 2019/10/15.
 */
@Controller
@RequestMapping("/userInfo")
public class UserInfoController {
    @Autowired
    UserService userService;
    @Autowired
    PersonalService personalService;
    @Autowired
    UserHistoryService userHistoryService;
    @Autowired
    SendMessageService sendMessageService;
    @Autowired
    MqMobileDeviceService mqMobileDeviceService;
    @Autowired
    UploadService uploadService;
    @Autowired
    AuthService authService;
    @Autowired
    private SmsService smsService;
    
    /**
    * <AUTHOR>
    * @Date 2019/10/15 14:42
    * 个人信息 预览
    */
    @ResponseBody
    @RequestMapping("/userInfoPreview.do")
    public JsonResult userInfoPreview(User user,Integer passiveUserId){
        if (passiveUserId==null) {
            passiveUserId=user.getUserID();
        }
        User passiveUser = userService.getUserByID(passiveUserId); // id有值 是总务查看某人，id没值是当前登录人自己看自己

        List<UserContact> userContactList=personalService.getUserContactListByUserId(passiveUserId);
        List<PersonnelOccupation> personnelOccupations = personalService.getPersonnelOccupationByUserId(passiveUserId);
        List<PersonalEducation> personalEducations = personalService.getPersonalEducationByUserId(passiveUserId);
        Map<String,Object> map=new HashMap<>();
        if ("9".equals(passiveUser.getIsDuty())){
            passiveUser.setActivationButton(true);
        }else {
            passiveUser.setActivationButton(false);
        }
        map.put("user",passiveUser);
        map.put("userContacts",userContactList);
        map.put("personnelOccupations",personnelOccupations);
        map.put("personalEducations",personalEducations);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/15 15:23
    * 新增紧急联系人接口
    */
    @ResponseBody
    @RequestMapping("/addEmergencyContact.do")
    public JsonResult addEmergencyContact(User user,String emergencyName,String emergencyContact){
        Integer state=0;
//        if (!StringUtil.isNullOrEmpty(emergencyName)&&!StringUtil.isNullOrEmpty(emergencyContact)) {
//            String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$";
//            Pattern p = Pattern.compile(regex);
//            Matcher m = p.matcher(emergencyContact);
//            if (m.matches()) {//手机号格式正确
                user = userService.getUserByID(user.getUserID());
                user.setEmergencyName(emergencyName);
                user.setEmergencyContact(emergencyContact);
                userService.updateUser(user);
                state=1;//操作成功
//            }else {
//                state=2;//手机号格式不对
//            }
//        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/16 9:46
    * 编辑用户基本信息
    */
    @ResponseBody
    @RequestMapping("/updateUserBasicInfo.do")
    public JsonResult updateUserBasicInfo(User user, HttpServletRequest request){
        Integer state=  0;
//        User loginUser= (User) session.getAttribute("user");
//        loginUser=userService.getUserByID(loginUser.getUserID());//登录人
        String userInfo= request.getParameter("userInfo");//用户信息
        String contacts= request.getParameter("contacts");//联系方式
        if (!StringUtil.isNullOrEmpty(userInfo)) {
            JSONObject userInfoJson=JSONObject.fromObject(userInfo);
            User passiveUser=userService.getUserByID(userInfoJson.getInt("passiveUserId"));//被编辑人
            userHistoryService.addUserHistoryByUser(passiveUser,true,"0"); //查询是否存在记录，不存在补原始信息
            userService.updateUserBasicInfo(user,passiveUser, userInfoJson, contacts);
            userHistoryService.addUserHistoryByUser(passiveUser,false,"1");// 把新修改的内容 存到历史记录中    操作: 1- 普通更改（不改姓名、手机号、头像，只改其他信息） 2- 含姓名更改（不含 手机号，头像），3 只改手机号（手机号只能单独修改），4- 更换头像的历史记录


            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/16 11:37
    * 查看基本信息修改记录
    */
    @ResponseBody
    @RequestMapping("/getUserBasicInfoHistory.do")
    public JsonResult getUserBasicInfoHistory(User user,Integer passiveUserId, PageInfo pageInfo){
        user=userService.getUserByID(user.getUserID());
        if (passiveUserId==null){
            passiveUserId=user.getUserID();
        }
        user=userService.getUserByID(passiveUserId);

        List<UserHistory> userHistoryList= userHistoryService.getUserBasicInfoHistoryListByUserId(passiveUserId,null,pageInfo);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        if (pageInfo.getCurrentPageNo()>1){
            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
        }

        String dataState="";
        String updateName="";
        Date updateTime=new Date();
        for (UserHistory userHistory:userHistoryList){
            Map<String,Object> map=new HashMap<>();

            if (pageInfo.getCurrentPageNo()==1&&number==0){
                dataState="原始信息";
                updateName=userHistory.getCreateName();
                updateTime=userHistory.getCreateTime();

            }else {
                dataState="第"+number+"次修改后";
                updateName=userHistory.getUpdateName();
                updateTime=userHistory.getUpdateDate();
            }
            map.put("dataState",dataState);//资料状态
            map.put("id",userHistory.getId());
            map.put("updateName",updateName);//操作人名
            map.put("updateTime",updateTime);//修改时间
            mapList.add(map);
            number+=1;
        }

        Map<String,Object> map=new HashMap<>();
//        map.put("number",number==0?0:number-1);//最新的修改次数
        map.put("number",user.getVersionNo());//最新的修改次数
        map.put("updateName",updateName);//人
        map.put("updateTime",updateTime);//修改时间
        map.put("userRoleHistoryList",mapList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);

    }

    /**
    * <AUTHOR>
    * @Date 2019/11/8 11:22
    * 查看基本信息历史记录详情
    */
    @ResponseBody
    @RequestMapping("/getUserHistory.do")
    public JsonResult getUserHistory(Integer id){
        UserHistory userHistory=userHistoryService.getUserHistoryById(id);
        switch (userHistory.getManagerCode()){
            case "general": userHistory.setManageName("总务");
                break;
            case "finance": userHistory.setManageName("财务");
                break;
            case "sale": userHistory.setManageName("销售");
                break;
            case "accounting": userHistory.setManageName("会计");
                break;
        }
        if (RoleTmpl.isHighManager(userHistory.getRoleCode())||RoleTmpl.isSuper(userHistory.getRoleCode())){
            userHistory.setManageName("--");
        }

        List<UserContactHistory> userContactHistoryList=userHistoryService.getUserContactHistoryListByVersionNo(userHistory.getUserId(),userHistory.getVersionNo());
        Map<String,Object> map=new HashMap<>();
        map.put("user",userHistory);
        map.put("userContacts",userContactHistoryList);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/16 14:10
    * 新增工作经历
    */
    @ResponseBody
    @RequestMapping("/addPersonnelOccupation.do")
    public JsonResult addPersonnelOccupation(User user,Integer passiveUserId,String beginTime,String endTime,String corpName,String corpSize,String corpNature,String corpDepartment,String post,String jobDesc,String memo) throws ParseException {
        Integer state=0;
        if (user!=null) {
            if (passiveUserId==null){
                passiveUserId=user.getUserID();
            }
            User passiveUser=userService.getUserByID(passiveUserId);

            personalService.addOrUpdatePersonnelOccupation(passiveUser,user,null,beginTime,endTime,corpName,corpSize,corpNature,corpDepartment,post,jobDesc,memo);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2019/10/17 9:51
     * 编辑工作经历
     */
    @ResponseBody
    @RequestMapping("/updatePersonnelOccupation.do")
    public JsonResult updatePersonnelOccupation(User user,Integer passiveUserId,Integer id,String beginTime,String endTime,String corpName,String corpSize,String corpNature,String corpDepartment,String post,String jobDesc,String memo){
        Integer state=0;
        if (user!=null) {
            if (passiveUserId==null){
                passiveUserId=user.getUserID();
            }
            User passiveUser=userService.getUserByID(passiveUserId);
            personalService.addOrUpdatePersonnelOccupation(passiveUser,user,id,beginTime,endTime,corpName,corpSize,corpNature,corpDepartment,post,jobDesc,memo);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/16 14:57
    * 工作经历删除(逻辑删除)
    */
    @ResponseBody
    @RequestMapping("/markDeleteOccupation.do")
    public JsonResult markDeleteOccupation(Integer id){
        Integer state=0;
        if (id!=null) {
            PersonnelOccupation personnelOccupation = personalService.getPersonnelOccupationById(id);
            if ("1".equals(personnelOccupation.getUser().getSubmitState())) {//提交之后是逻辑删
                personalService.addPersonnelOccupationHistory(personnelOccupation, true);//查询用不用补原始信息，需要补就自动补
                personnelOccupation.setDeleted(true);
                personnelOccupation.setVersionNo(personnelOccupation.getVersionNo()!=null?personnelOccupation.getVersionNo()+1:1);
                personalService.updatePersonnelOccupation(personnelOccupation);
                personalService.addPersonnelOccupationHistory(personnelOccupation, false);//最新操作生成历史记录
            }else {
                personalService.deletePersonnelOccupation(personnelOccupation);
            }
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2019/10/16 13:36
     * 新增教育经历
     */
    @ResponseBody
    @RequestMapping("/addPersonalEducation.do")
    public JsonResult addPersonalEducation(User user,Integer passiveUserId,String beginTime,String endTime,String collegeName,String departmentName,String major,String degreeDesc,String majorDesc,String memo) throws ParseException {
        Integer state=0;
        if (user!=null) {
            if (passiveUserId==null){
                passiveUserId=user.getUserID();
            }
            User passiveUser=userService.getUserByID(passiveUserId);
            personalService.addOrUpdatePersonalEducation(passiveUser,user,null,beginTime,endTime,collegeName,departmentName,major,degreeDesc,majorDesc,memo);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/17 11:57
    * 编辑教育经历
    */
    @ResponseBody
    @RequestMapping("/updatePersonalEducation.do")
    public JsonResult updatePersonalEducation(User user,Integer passiveUserId,Integer id,String beginTime,String endTime,String collegeName,String departmentName,String major,String degreeDesc,String majorDesc,String memo) throws ParseException {
        Integer state=0;
        if (user!=null) {
            if (passiveUserId==null){
                passiveUserId=user.getUserID();
            }
            User passiveUser=userService.getUserByID(passiveUserId);
            personalService.addOrUpdatePersonalEducation(passiveUser,user,id,beginTime,endTime,collegeName,departmentName,major,degreeDesc,majorDesc,memo);
            state=1;
        }
        return new JsonResult(1, state);

    }

    /**
    * <AUTHOR>
    * @Date 2019/10/17 9:21
    * 教育经历删除（逻辑删除）
    */
    @ResponseBody
    @RequestMapping("/markDeleteEducation.do")
    public JsonResult markDeleteEducation(Integer id){
        Integer state=0;
        if (id!=null){
            PersonalEducation personalEducation=personalService.getPersonalEducationById(id);
            if ("1".equals(personalEducation.getUser().getSubmitState())) {//提交之后是逻辑删除
                personalService.addPersonnelEducationHistory(personalEducation, true);//查询用不用补原始信息，需要补就自动补
                personalEducation.setDeleted(true);
                personalEducation.setVersionNo(personalEducation.getVersionNo()!=null?personalEducation.getVersionNo()+1:1);
                personalService.updatePersonalEducation(personalEducation);
                personalService.addPersonnelEducationHistory(personalEducation, false);//最新操作生成历史记录
            }else {//否则是物理删除
                personalService.deletePersonalEducation(personalEducation);
            }
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/17 14:51
    * 查询工作经历修改记录
    */
    @ResponseBody
    @RequestMapping("/personnelOccupationHistories.do")
    public JsonResult personnelOccupationHistories(Integer id,PageInfo pageInfo){
        PersonnelOccupation personnelOccupation=personalService.getPersonnelOccupationById(id);
        List<PersonnelOccupationHistory> personnelOccupationHistories=personalService.getPersonnelOccupationHistoriesById(id,pageInfo);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        if (pageInfo.getCurrentPageNo()>1){
            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
        }
        for (PersonnelOccupationHistory p:personnelOccupationHistories){
            Map<String,Object> map=new HashMap<>();
            if (pageInfo.getCurrentPageNo()==1&&number==0){
                map.put("dataState","原始信息");
            }else if (p.isDeleted()){
                map.put("dataState","删除");
            }else {
                map.put("dataState","第"+number+"次修改后");
            }
            map.put("updateName",p.getCreateName());//人名
            map.put("updateDate",p.getUpdateDate());//修改时间
            map.put("id",p.getId());

            mapList.add(map);
            number+=1;
        }
        Map<String,Object> map=new HashMap<>();
        map.put("number",personnelOccupation.getVersionNo());//最新的修改次数
        map.put("personnelOccupation",personnelOccupation);//最新修改时间
        map.put("personnelOccupationHistoryList",mapList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/17 15:21
    * 查询单条工作经历修改记录详情
    */
    @ResponseBody
    @RequestMapping("/getPersonnelOccupationHistory.do")
    public JsonResult getPersonnelOccupationHistory(Integer id){
        PersonnelOccupationHistory p=personalService.getPersonnelOccupationHistoryById(id);
        return new JsonResult(1,p);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/17 15:13
    * 查询教育经历修改记录
    */
    @ResponseBody
    @RequestMapping("/personalEducationHistories.do")
    public JsonResult personalEducationHistories(Integer id,PageInfo pageInfo){
        PersonalEducation personalEducation=personalService.getPersonalEducationById(id);
        List<PersonnelEducationHistory> personnelEducationHistories=personalService.getPersonnelEducationHistoriesById(id,pageInfo);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        if (pageInfo.getCurrentPageNo()>1){
            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
        }
        for (PersonnelEducationHistory p:personnelEducationHistories){
            Map<String,Object> map=new HashMap<>();
            if (pageInfo.getCurrentPageNo()==1&&number==0){
                map.put("dataState","原始信息");
            }else if (p.isDeleted()){
                map.put("dataState","删除");
            }else {
                map.put("dataState","第"+number+"次修改后");
            }
            map.put("id",p.getId());
            map.put("updateName",p.getCreateName());//人名
            map.put("updateDate",p.getUpdateDate());//修改时间

            mapList.add(map);
            number+=1;
        }
        Map<String,Object> map=new HashMap<>();
        map.put("number",personalEducation.getVersionNo());//最新的修改次数
        map.put("personalEducation",personalEducation);//最新修改时间
        map.put("personnelEducationHistoryList",mapList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/17 15:27
     * 查询单条教育经历修改记录详情
     */
    @ResponseBody
    @RequestMapping("/getPersonalEducationHistory.do")
    public JsonResult getPersonalEducationHistory(Integer id){
        PersonnelEducationHistory p=personalService.getPersonnelEducationHistoryById(id);
        return new JsonResult(1,p);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/17 15:55
    * 资料填写已完成
    */
    @ResponseBody
    @RequestMapping("/submitUserInfo.do")
    public JsonResult submitUserInfo(User user){
        Integer state=0;
        if (user!=null) {
            user = userService.getUserByID(user.getUserID());
            user.setSubmitState("1");//提交状态:0-未提交,1-已提交
            userService.updateUser(user);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/18 11:42
    * 总务 查看单人档案管理详情
    */
    @ResponseBody
    @RequestMapping("/getUserArchives.do")
    public JsonResult  getUserArchives(Integer passiveUserId){
        User user=userService.getUserByID(passiveUserId);
        List<User> xiajiList = userService.getUsersByLeader(user.getOid(),passiveUserId.toString());
        String haveSubordinate="0";
        if (xiajiList.size()>0){
            haveSubordinate="1";
        }
        AuthAcc authAcc=authService.getLockedAcc(user.getAccId());
        if (authAcc!=null){
            user.setActivationButton(true);
        }else {
            user.setActivationButton(false);
        }
        user.setHaveSubordinate(haveSubordinate);//有下级 1 不能修改为普通员工     没有下级0
        return new JsonResult(1,user);
    }

    /**
    * <AUTHOR>
    * @Date 2019/10/18 11:49
    * 总务编辑职工身份证号
    */
    @ResponseBody
    @RequestMapping("/updateUserIdCard.do")
    public JsonResult updateUserIdCard(User user,Integer passiveUserId,String idCard,String address){
        Integer state=0;
        if (passiveUserId!=null&&!StringUtil.isNullOrEmpty(idCard)) {
            User passiveUser = userService.getUserByID(passiveUserId);//被编辑的人
            userService.addUserIdcard(passiveUser, user, idCard, address);//生成历史记录
            passiveUser.setIdCard(idCard);//身份证号
            passiveUser.setAddress(address);//身份证地址
            userService.updateUser(passiveUser);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
    * <AUTHOR>
    * @Date 2019/11/18 17:22
    * 总务查询身份证修改记录
    */
    @ResponseBody
    @RequestMapping("/getUserIdCardHistories.do")
    public JsonResult getUserIdCardHistories(Integer passiveUserId){
        List<UserIdcard> us=userService.getUserIdcardHistories(passiveUserId);
        Date updateDate=new Date();
        String updateName="";
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        for (UserIdcard userIdcard:us){
            Map<String,Object> map=new HashMap<>();
            if (number==0){
                map.put("dataState","原始信息");
            }else {
                map.put("dataState","第"+number+"次修改后");
            }
            map.put("id",userIdcard.getId());
            map.put("updateName", userIdcard.getUpdateName());//人名
            map.put("updateDate",userIdcard.getUpdateDate());//修改时间
            updateDate=userIdcard.getUpdateDate();
            updateName=userIdcard.getUpdateName();
            mapList.add(map);
            number+=1;
        }
        Map<String,Object> map=new HashMap<>();
        map.put("number",number==0?0:number-1);//最新的修改次数
        map.put("updateDate",updateDate);//最新修改时间
        map.put("updateName",updateName);//最新修改时间
        map.put("userIdcardHistoryList",mapList);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/11/18 17:29
    * 总务查看某次身份证信息修改详情
    */
    @ResponseBody
    @RequestMapping("/getUserIdCard.do")
    public JsonResult getUserIdCard(Integer id){
        UserIdcard userIdcard=userService.getUserIdCardById(id);
        return new JsonResult(1,userIdcard);
    }


    /**
     * <AUTHOR>
     * @Date 2020/5/12 16:19
     * 1.96 版 手机号查重功能
     */
    @AuthPassport(manor=true)
    @ResponseBody
    @RequestMapping("/mobileCheckRepeat.do")
    public JsonResult mobileCheckRepeat(User user,Integer userId,String mobile,Integer passiveUserId){
        if (userId!=null){
            user=userService.getUserByID(userId);
        }
        Integer oid=user.getOid();
        String cont="";
        Integer state=1;//1-查重通过  0-与在职相同  2- 与离职相同 3- 与修改记录相同
        User userByLogonName = userService.getUserByOidAndPhone(oid,mobile);
        if(userByLogonName!=null&& !userByLogonName.getUserID().equals(passiveUserId)) {
            cont="您录入的手机号与公司"+userByLogonName.getUserName()+"手机号相同。请确认！";
            state=0;
        }else {
            List<User> userList = userService.getUserByMobileAndIsDuty(oid, mobile, "2");  //离职人员
            if (userList.size() > 0) {
                cont = "您录入的手机号与公司已离职者" + userList.get(0).getUserName() + "手机号相同。确定该号码无误吗？";
                state = 2;
            }else {
                List<UserHistory> userHistoryList=userHistoryService.getUserHistoryListByOidMobile(oid,mobile);
                if (userHistoryList.size()>0){
                    cont="您录入的手机号与公司"+userHistoryList.get(0).getUserName()+"曾用过的手机号相同。确定该号码无误吗？";
                    state=3;
                }
            }
        }
        Map<String,Object> map=new HashMap<>();
        map.put("state",state);//查重结果
        map.put("cont",cont);//对应提示
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2020/5/12 17:30
     * 1.96 版 登录账号自行修改且不能收到短信验证码 时， 获取原型中的AABB具体人员
     */
    @ResponseBody
    @RequestMapping("/getLeaderUserName.do")
    public JsonResult getLeaderUserName(User user){
        String userName="";
        String leaderId="";
        if (RoleTmpl.isHighManager(user.getRoleCode())){//登陆人是高管 把董事长信息返回
            userName=user.getLeaderName();
            leaderId=user.getLeader();
        }else if(user.getRoleCode().equals("super")){
            userName="wonderss客服";
        }else if (user.getRoleCode().equals("smallSuper")){
            userName=user.getLeaderName();
            leaderId=user.getLeader();
        }else {//普通员工 把总务姓名返回
                User general=userService.getUserByRoleCode(user.getOid(),"general");
                userName=general.getUserName();
        }
        if (userName==null||userName.equals("")){
            User leaderUser=userService.getUserByID(Integer.valueOf(leaderId));
            userName=leaderUser.getUserName();
        }

        return new JsonResult(1,userName);

    }

    /**
     * <AUTHOR>
     * @Date 2024/5/18
     * 短信重构， 机构内 修改手机号  登陆人给自己 发送短信验证码
     */
    @ResponseBody
    @RequestMapping("/orgEditPhoneVerificationCode.do")
    public JsonResult orgEditPhoneVerificationCode(String phone, AuthInfoDto authInfo) {
        final List<String> verifyMethodNames = new ArrayList<String>(2) {{
            add("checkOrgEditVerificationCode");
            add("orgNewPhoneVerificationCode");   //第三步变成 给新手机号发送短信验证码
        }};
        return smsService.sendVerificationCodeForMe(verifyMethodNames, phone, authInfo, TemplateService.SmsContentTemplate.accLogin);
    }

    /**
     * <AUTHOR>
     * @Date 2024/5/18
     * 短信重构， 机构内 修改手机号   验证 登陆人给自己 的验证码
     */
    @ResponseBody
    @RequestMapping("/checkOrgEditVerificationCode.do")
    public JsonResult checkOrgEditVerificationCode(AuthInfoDto authInfo, String phone, String code, HttpServletResponse response){
        final String sendMethordName = "orgEditPhoneVerificationCode";
        if(smsService.verificationCode(response, authInfo,sendMethordName,phone,code)) {
            return new JsonResult(1, "验证通过");
        } else {
            return new JsonResult(new MyException("-1", "验证码错误或者超时！"));
        }
    }


    /**
     * <AUTHOR>
     * @Date 2024/5/27
     * 短信重构， 机构内 修改手机号  登陆人给新手机号 发送短信验证码
     */
    @AuthPassport(verifiedCode = "orgEditPhoneVerificationCode")
    @ResponseBody
    @RequestMapping("/orgNewPhoneVerificationCode.do")
    public JsonResult orgNewPhoneVerificationCode(String phone, AuthInfoDto authInfo) {
        final List<String> verifyMethodNames = new ArrayList<String>(2) {{
            add("checkOrgNewPhoneVerificationCode");
            add("updateUserPhonePassWord");
        }};
        return smsService.sendVerificationCodeForUserAcc(verifyMethodNames, phone, authInfo, TemplateService.SmsContentTemplate.accLogin);
    }

    /**
     * <AUTHOR>
     * @Date 2024/5/27
     * 短信重构， 机构内 修改手机号   验证 登陆人给新手机号 的验证码
     */
    @ResponseBody
    @RequestMapping("/checkOrgNewPhoneVerificationCode.do")
    public JsonResult checkOrgNewPhoneVerificationCode(AuthInfoDto authInfo, String phone, String code, HttpServletResponse response){
        final String sendMethordName = "orgNewPhoneVerificationCode";
        if(smsService.verificationCode(response, authInfo,sendMethordName,phone,code)) {
            return new JsonResult(1, "验证通过");
        } else {
            return new JsonResult(new MyException("-1", "验证码错误或者超时！"));
        }
    }

    /**
     * <AUTHOR>
     * @Date 2020/5/12 17:49
     * 1.96 版 新手机号和短信验证成功后， 更改登陆人手机号为新手机号 和 登录密码
     * password 明文
     */
    @AuthPassport(verifiedCode = "orgNewPhoneVerificationCode")
    @ResponseBody
    @RequestMapping("/updateUserPhonePassWord.do")
    public boolean updateUserPhonePassWord(AuthAcc acc, User user, String phone, String password,String code, HttpServletRequest request, HttpServletResponse response){
        if (phone.length()==11 && userService.getUserByMobileAndIsDuties(user.getOid(), phone, User.getIsDutyListCanLogin()).isEmpty()){
            Long now = System.currentTimeMillis();
            User masterUser = userService.getMasterUserByOidAndAcc(user.getOid(), acc);
            String name = StringUtils.isNotEmpty(masterUser.getUserName()) ? masterUser.getUserName() : acc.getName();
//            AuthAcc authAcc= authService.newEnabledAcc(request, response, phone, code, name, AuthService.Source.org, password);// 生成登陆账号
            // 短信重构修改 2024/5/21 李旭
            AuthAcc authAcc=authService.newEnabledAccInOrg(phone,masterUser.getUserName(),masterUser.getOrganization());
            if (authService.changePasswordByVerifiedCodeLogin(request, password)) {
                String oldMobile = acc.getMobile();
                List<User> users=userService.getUsersByMasterUserID(masterUser.getUserID());// 当前人员拥有的 身份列表
                for (User u : users) {
                    u.setMobile(phone);
//                u.setLogonName(phone);
//                clusterMessageSendingOperations.convertAndSendToUser(user.getMobile(),"/lockPasswordchange", "密码已修改","密码已被修改", null, null,"success");
                    u.setVersionNo(u.getVersionNo() == null ? 1 : u.getVersionNo() + 1);
                    u.setUpdateName(name);
                    u.setAccId(authAcc.getId());
                    u.setUpdateDate(new Date());
                    userService.updateUser(u);
                    if (u.getUserID().equals(masterUser.getUserID())) { //是主 员工 才产生修改记录  身份数据不记录 人员信息修改历史
                        userHistoryService.addUserHistoryByUser(u, "3", true);//保存历史记录
                        userService.followSuperToGlpt(u,phone);
                    }
                }
//                userService.updateUserPasswdByPhone(phone, password);
                if (StringUtils.isNotEmpty(oldMobile)) {
                    mqMobileDeviceService.forceLogoutDevices(oldMobile, "该账户的机构列表发生变动，请重新登陆。", null); // 强制下线 旧手机号
                }
                if(now > authAcc.getCreateTime().getTime()) { //新手机号账号非新建
                    mqMobileDeviceService.forceLogoutDevices(phone, "该账户的机构列表发生变动，请重新登陆。", null); // 强制下线 新手机号
                }
                return true;//成功
            }else {
                return false;
            }
        }
        return false;//失败，找不到用户
    }

    /**
     * <AUTHOR>
     * @Date 2020/5/27 15:49
     * 1.96 版 总务编辑用户手机号
     *
     */
    @ResponseBody
    @RequestMapping("/updateUserMobile.do")
    public boolean updateUserMobile(User user,Integer userId,String phone,HttpServletRequest request,AuthInfoDto authInfo){
        if (StringUtils.isNotEmpty(phone) && phone.length()==11 && userId!=null
//                && userService.getUserByMobileAndIsDuties(user.getOid(), phone, User.getIsDutyListCanLogin()).isEmpty()) {
                && userService.getUserByMobileAndIsDuties(user.getOid(), phone, new ArrayList<String>(Arrays.asList("1","2","3","4","5"))).isEmpty()) {
            Long now = System.currentTimeMillis();
            User passiveUser = userService.getUserByID(userId);
            AuthAcc oldAcc = authService.getEnabledOrDisabledAcc(passiveUser.getAccId());
            Organization org = user.getOrganization();
            User masterUser = userService.getMasterUserByOidAndAcc(org.getId(), oldAcc);
            String name = StringUtils.isNotEmpty(masterUser.getUserName()) ? masterUser.getUserName() : oldAcc.getName();
            AuthAcc authAcc = authService.newEnabledAccInOrg(phone, name, org);// 生成登陆账号
            if (authAcc != null) {
                String oldMobile = oldAcc.getMobile();
                List<User> users=userService.getUsersByMasterUserID(masterUser.getUserID());// 当前人员拥有的 身份列表
                for (User u : users) {
                    if (u.getUserID().equals(masterUser.getUserID())) {
                        userHistoryService.addUserHistoryByUser(u, "3", false);//保存历史记录
                    }
                    u.setMobile(phone);
//                u.setLogonName(phone);
//                u.setLogonPwd(userService.getLogonPwdByPhone(phone));
//                u.setPasswordTime(new Date(System.currentTimeMillis()));
                    u.setVersionNo(u.getVersionNo() == null ? 1 : u.getVersionNo() + 1);
                    u.setUpdateName(name);
                    u.setUpdateDate(new Date(System.currentTimeMillis()));
                    u.setAccId(authAcc.getId());
                    userService.updateUser(u);
                    if (u.getUserID().equals(masterUser.getUserID())) { //是主 员工 才产生修改记录  身份数据不记录 人员信息修改历史
                        userHistoryService.addUserHistoryByUser(u, "3", true);//保存历史记录
                    }
                }
                if (StringUtils.isNotEmpty(oldMobile)) {
                    mqMobileDeviceService.forceLogoutDevices(oldMobile, "该账户的机构列表发生变动，请重新登陆。", null);// 强制下线 旧手机号
                }
                if(now > authAcc.getCreateTime().getTime()) { //新手机号账号非新建
                    mqMobileDeviceService.forceLogoutDevices(phone, "该账户的机构列表发生变动，请重新登陆。", null); // 强制下线 新手机号
                }

//                String cont = "您登录wonderss系统" + org.getName() + "公司的账号已变为" + phone + "，如有疑问请速与" + user.getUserName() + "确认；您可下载手机端，或用电脑登录www.wonderss.com";
//                sendMessageService.sendMessage(user.getUserID(), org.getId(), oldMobile, cont);//发短信

                // 短信重构 2024/5/22 李旭更改
                String phoneNo=oldMobile;
                String url = GetLocalIPUtils.getRootPath(request);
                Map<String, String> params = new HashMap<String, String>(4) {{
                    put("phoneNo", phone);
                    put("url", url);
                    put("orgName", org.getName());
                    put("userName", user.getUserName());
                }};
                smsService.sendMessage(SmsService.SendType.sys, phoneNo, authInfo, TemplateService.SmsContentTemplate.changeMobile, params, null);

                return true;
            }
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Date 2020/6/5 15:49
     * 手机端2.61
     *
     */
    @ResponseBody
    @RequestMapping("/updateUserImage.do")
    public boolean updateUserImage(User user,String imagePath){
        if (user!=null&&imagePath!=null) {
            if (user.getImgPath()!=null&&!user.getImgPath().isEmpty()){
                UploadFile uploadFile=uploadService.getUploadFile(user.getImgPath());
                if (uploadFile!=null) {
                    uploadService.delFileUsing(new UserUsing(user.getUserID()), user.getImgPath(), user); // 如果 有原头像 则删除原头像 的文件引用
                }
            }
            user.setImgPath(imagePath);
            user.setUpdateDate(new Date());
            user.setUpdateName(user.getUserName());
            userService.updateUser(user);
            uploadService.addFileUsing(new UserUsing(user.getUserID()), user.getImgPath(), user.getUserName(), user, "职工头像");
            userHistoryService.addUserHistoryByUser(user,"3",false);//查有没有原始信息，么有就补充原始信息
            UserHistory userHistory=new UserHistory();
            userHistory.setImgPath(user.getImgPath());
            userHistory.setUserId(user.getUserID());
            userHistory.setUpdator(user.getUserID());
            userHistory.setUpdateName(user.getUserName());
            userHistory.setUpdateDate(new Date());
            userHistory.setCreator(user.getUserID());
            userHistory.setCreateTime(new Date());
            userHistory.setCreateName(user.getUserName());
            userHistory.setOperation("4");//操作: 1- 普通更改（不改姓名、手机号、头像，只改其他信息） 2- 含姓名更改（不含 手机号，头像），3 只改手机号（手机号只能单独修改），4- 更换头像的历史记录
            userHistoryService.saveUserHistory(userHistory);
            uploadService.addFileUsing(new UserHistoryUsing(userHistory.getId()), userHistory.getImgPath(), userHistory.getUserName()+"头像更换进历史", user, "职工头像");

            return true;
        }
        return false;
    }


    /**
     * <AUTHOR>
     * @Date 2021/5/24 11:37
     * 1.159私人领地奠基 查看员工姓名的修改记录
     */
    @ResponseBody
        @RequestMapping("/getUserNameHistories.do")
    public JsonResult getUserNameHistories(User user,Integer passiveUserId, PageInfo pageInfo){
        if (passiveUserId==null){
            passiveUserId=user.getUserID();
        }
        List<UserHistory> userHistoryList= userHistoryService.getUserBasicInfoHistoryListByUserId(passiveUserId,"2",pageInfo);
        if (userHistoryList.size()<=1){
            userHistoryList.clear(); //只有一条原始信息不返回
            pageInfo=new PageInfo();
        }
        List<Map<String,Object>> map=getUserHistories(userHistoryList,"userName",pageInfo);
        return new JsonResult(1,map,pageInfo);
    }


    private List<Map<String,Object>> getUserHistories(List<UserHistory> userHistoryList,String historyColumn,PageInfo pageInfo){
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        if (pageInfo.getCurrentPageNo()>1){
            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
        }
        String dataState="";
        String updateName="";
        Date updateTime=new Date();
        for (UserHistory userHistory:userHistoryList){
            Map<String,Object> map=new HashMap<>();

            if (pageInfo.getCurrentPageNo()==1&&number==0){
                dataState="原始信息";
                updateName=userHistory.getCreateName();
                updateTime=userHistory.getCreateTime();
                map.put("operationName","创建人");// 展示的操作人别名

            }else {
                dataState="第"+number+"次修改后";
                updateName=userHistory.getUpdateName();
                updateTime=userHistory.getUpdateDate();
                map.put("operationName","修改人");// 展示的操作人别名
            }
            map.put("dataState",dataState);//资料状态
            map.put("id",userHistory.getId());
            if (historyColumn.equals("userName")){
                map.put(historyColumn,userHistory.getUserName());// 历史姓名
            }
            if (historyColumn.equals("mobile")){
                map.put(historyColumn,userHistory.getMobile());// 历史手机号
            }
            map.put("updateName",updateName);//操作人名
            map.put("updateTime",updateTime);//修改时间
            mapList.add(map);
            number+=1;
        }

        Map<String,Object> map=new HashMap<>();
//        map.put("number",number==0?0:number-1);//最新的修改次数
        map.put("number",pageInfo.getTotalResult());//最新的修改次数 为总记录数
        map.put("updateName",updateName);//人
        map.put("updateTime",updateTime);//修改时间
        map.put("userHistoryList",mapList);
        map.put("pageInfo",pageInfo);
        return mapList;
    }

    /**
     * <AUTHOR>
     * @Date 2021/5/24 11:37
     * 1.159私人领地奠基 查看登陆账号的修改记录
     */
    @ResponseBody
    @RequestMapping("/getUserMobileHistories.do")
    public JsonResult getUserMobileHistories(User user,Integer passiveUserId, PageInfo pageInfo){
        if (passiveUserId==null){
            passiveUserId=user.getUserID();
        }
        List<UserHistory> userHistoryList= userHistoryService.getUserBasicInfoHistoryListByUserId(passiveUserId,"3",pageInfo);
        if (userHistoryList.size()<=1){
            userHistoryList.clear(); //只有一条原始信息不返回
            pageInfo=new PageInfo();
        }
        List<Map<String,Object>> mapList=getUserHistories(userHistoryList,"mobile",pageInfo);
        return new JsonResult(1,mapList,pageInfo);
    }


    /**
     * <AUTHOR>
     * @Date 2021/5/31 9:39
     * 1.159私人领地奠基 查看职工头像的修改记录
     */
    @ResponseBody
    @RequestMapping("/getUserImageHistories.do")
    public JsonResult getUserImageHistories(User user,Integer passiveUserId,PageInfo pageInfo){
        if (passiveUserId==null){
            passiveUserId=user.getUserID();
        }
        List<UserHistory> userHistoryList= userHistoryService.getUserBasicInfoHistoryListByUserId(passiveUserId,"4",pageInfo);
        List<Map<String,Object>> mapList=new ArrayList<>();
        for (UserHistory userHistory:userHistoryList){
            Map<String,Object> map=new HashMap<>();
            map.put("id",userHistory.getId());
            map.put("updateName",userHistory.getUpdateName());//人
            map.put("updateTime",userHistory.getUpdateDate());//修改时间
            map.put("imgPath",userHistory.getImgPath());// 图片路径
            mapList.add(map);
        }
        return new JsonResult(1,mapList,pageInfo);
    }

    /**
     * <AUTHOR>
     * @Date 2021/7/9 9:39
     *  通讯录接口翻新(原通讯录17年的，太老了)
     */
    @ResponseBody
    @RequestMapping("/getAddressBook.do")
    public JsonResult  getAddressBook(User user,String userName){
        List<AddressBookDto> addressBookDtoList=userService.getAddressBookDtoList(user.getOid(),userName);
        return  new JsonResult(1,addressBookDtoList);
    }


}
