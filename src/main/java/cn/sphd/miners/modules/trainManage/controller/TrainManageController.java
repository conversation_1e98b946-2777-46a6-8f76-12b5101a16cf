package cn.sphd.miners.modules.trainManage.controller;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.trainManage.dto.*;
import cn.sphd.miners.modules.trainManage.entity.TrainingMaterial;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBank;
import cn.sphd.miners.modules.trainManage.service.TrainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.List;

/**
 * @ClassName TrainManageController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/13 15:57
 * @Version 1.0
 */
@Controller
@RequestMapping("/trainManage")
public class TrainManageController {

    @Autowired
    TrainService trainService;

    @RequestMapping("/goAssessManage.do")
    public String assessManageIndex() {
        return "trainManage/assessManage";
    }

    @RequestMapping("/goQuestionBank.do")
    public String questionBankIndex() { return "trainManage/questionBank"; }

    //试题库首页接口
    //type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
    //status 1启用状态的，0停用状态的
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectQuestionBankList.do")
    public RespTrainListPage selectQuestionBankList(User user, Integer type, PageInfo pageInfo,Integer status) throws IOException {
        RespTrainListPage respTrainListPage =new RespTrainListPage();
        if(type==null)
            type=3;
        if(status==null)
            status=1;
        List<TrainingQuestionBank> list=trainService.selectTrainingQuestionBankList(user,type,pageInfo,status);
        respTrainListPage.setTrainingQuestionBankList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respTrainListPage.setPageInfo(pageInfo);
        return respTrainListPage;
    }
    //下一个题库代号查询
    @ResponseBody
    @RequestMapping("/selectQuestionBankCode.do")
    public RespTrainListPage selectQuestionBankCode(User user) throws IOException {
        RespTrainListPage respTrainListPage =new RespTrainListPage();
        Integer org = user.getOid();
        String code=trainService.selectQuestionBankCode(org);
        respTrainListPage.setCode(code);
        return respTrainListPage;
    }
    //新增题库
    //名称 name  应该有个上传文件的字段，之后再说。
    @ResponseBody
    @RequestMapping("/addQuestionBank.do")
    public RespStatus addQuestionBank(User user,TrainingQuestionBank trainingQuestionBank,String trainingQuestionBankAttachment,String module) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.addQuestionBank(user,trainingQuestionBank,trainingQuestionBankAttachment,module);
        respStatus.setStatus(status);
        return respStatus;
    }
    //查询题库
    //type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
    //pageSize 每页条数 currentPageNo 当前页数
    //status 1启用状态的，0停用状态的
    //keyword 关键字
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectQuestionBankListByKeyword.do")
    public RespTrainListPage selectQuestionBankListByKeyword(User user, Integer type, PageInfo pageInfo,String keyword,Integer status) throws IOException {
        RespTrainListPage respTrainListPage =new RespTrainListPage();
        if(status==null)
            status=1;
        if(type==null)
            type=3;
        List<TrainingQuestionBank> list=trainService.selectTrainingQuestionBankListByKeyword(user,type,pageInfo,keyword,status);
        respTrainListPage.setTrainingQuestionBankList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respTrainListPage.setPageInfo(pageInfo);
        return respTrainListPage;
    }
    //停用/启用题库
    //id
    //enabled 1启用，0停用
    @ResponseBody
    @RequestMapping("/updateQuestionBank.do")
    public RespStatus updateQuestionBank(User user,int id,int enabled) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.updateQuestionBank(user,id,enabled);
        respStatus.setStatus(status);
        return respStatus;
    }

    //删除题库判断
    //id
    //返回值，1可以删除，0含有已使用过的试题
    @ResponseBody
    @RequestMapping("/deleteQuestionBankJudge.do")
    public RespStatus deleteQuestionBankJudge(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteQuestionBankJudge(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除题库
    //id
    //返回值，1成功，0失败
    @ResponseBody
    @RequestMapping("/deleteQuestionBank.do")
    public RespStatus deleteQuestionBank(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteQuestionBank(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //题库查看
    //id
    //type  3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
    //status 1启用状态的，0停用状态的
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectQuestionBankDetail.do")
    public RespTrainMaterialListPage selectQuestionBankDetail( PageInfo pageInfo, Integer type,Integer status,int id) throws IOException {
        if(type==null)
            type=3;
        if(status==null)
            status=1;
        RespTrainMaterialListPage respTrainMaterialListPage=trainService.selectQuestionBankDetailById(pageInfo,id,type,status);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respTrainMaterialListPage.setPageInfo(pageInfo);
        return respTrainMaterialListPage;
    }
    //新增素材
    //content 内容
    //bank 题库id
    @ResponseBody
    @RequestMapping("/addTrainingMaterial.do")
    public RespStatus addTrainingMaterial(User user, TrainingMaterial trainingMaterial) throws IOException {
        RespStatus respStatus=trainService.addTrainingMaterial(user,trainingMaterial);
        return respStatus;
    }
    //停用、启用素材
    //id
    //enabled 1启用，0停用
    @ResponseBody
    @RequestMapping("/updateTrainingMaterial.do")
    public RespStatus updateTrainingMaterial(User user,int id,int enabled) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.updateTrainingMaterial(user,id,enabled);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除素材判断
    //id
    //返回值，1可以删除，0含有已使用过的试题
    @ResponseBody
    @RequestMapping("/deleteTrainingMaterialJudge.do")
    public RespStatus deleteTrainingMaterialJudge(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteTrainingMaterialJudge(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除素材
    //id
    //返回值，1成功，0失败
    @ResponseBody
    @RequestMapping("/deleteTrainingMaterial.do")
    public RespStatus deleteTrainingMaterial(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteTrainingMaterial(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //查询素材
    //id
    //type 排序规则 1代号降序排序，2代号升序排序，3创建时间降序排序，4创建时间升序排序，5素材数量降序排序，6素材数量升序排序
    //pageSize 每页条数 currentPageNo 当前页数
    //status 1启用状态的，0停用状态的
    //keyword 关键字
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectQuestionBankDetailByKeyword.do")
    public RespTrainMaterialListPage selectQuestionBankDetailByKeyword(int id, Integer type, PageInfo pageInfo,String keyword,Integer status) throws IOException {
        if(type==null)
            type=3;
        if(status==null)
            status=1;
        RespTrainMaterialListPage respTrainMaterialListPage=trainService.selectQuestionBankDetailByKeyword(pageInfo,id,type,status,keyword);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respTrainMaterialListPage.setPageInfo(pageInfo);
        return respTrainMaterialListPage;
    }
    //新增选择题
    //material 素材id
    //content 题干
    //optionList 选项组[{content:内容,isKey:1为正确项，0为错误项}] json字符串
    //返回值，1成功，0失败，-1已达到可新增选择题上限，-2选项有重复无法新增
    @ResponseBody
    @RequestMapping("/addChoiceQuestion.do")
    public RespStatus addChoiceQuestion(User user,int material,String content,String optionList) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.addChoiceQuestion(user,material,content,optionList);
        respStatus.setStatus(status);
        return respStatus;
    }
    //新增判断题
    //material 素材id
    //contentList 选项组[{content:内容,isKey:1为正确项，0为错误项}]  题干
    @ResponseBody
    @RequestMapping("/addTfQuestion.do")
    public RespStatus addTfQuestion(User user,int material,String contentList) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.addTfQuestion(user,material,contentList);
        respStatus.setStatus(status);
        return respStatus;
    }
    //新增判断接口
    //material 素材id
    //type 1选择，2判断
    //status 1可以新增，-1,已满4题，无法新增， 2为判断题已有且未满
    //yesNum;1为有正确的，0为没有正确的
    @ResponseBody
    @RequestMapping("/addQuestionJudge.do")
    public RespTrainQuestionAndKeyList addQuestionJudge(int material,int type) throws IOException {
        RespTrainQuestionAndKeyList respTrainQuestionList=trainService.addQuestionJudge(type,material);
        return respTrainQuestionList;
    }
    //素材查看
    //id 素材id
    //status 1启用状态的，0停用状态的
    @ResponseBody
    @RequestMapping("/selectTrainMaterialDetail.do")
    public RespTrainQuestionList selectTrainMaterialDetail(Integer status,int id) throws IOException {
        if(status==null)
            status=1;
        RespTrainQuestionList respTrainQuestionList=trainService.selectTrainQuestionDetailById(id,status);
        return respTrainQuestionList;
    }
    //停用、启用试题
    //id
    //enabled 1启用，0停用
    //返回值，1成功，0失败，-1选择题已经够4个，-2判断题已经够4个, -3 已经有判断题正确项不可启用
    @ResponseBody
    @RequestMapping("/updateTrainingQuestion.do")
    public RespStatus updateTrainingQuestion(User user,int id,int enabled) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.updateTrainingQuestion(user,id,enabled);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除试题判断
    //id
    //返回值，1可以删除，0含有已使用过的试题 -3判断题正确项不可删除
    @ResponseBody
    @RequestMapping("/deleteTrainingQuestionJudge.do")
    public RespStatus deleteTrainingQuestionJudge(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteTrainingQuestionJudge(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除试题
    //id
    //返回值，1成功，0失败 -3判断题正确项不可删除
    @ResponseBody
    @RequestMapping("/deleteTrainingQuestion.do")
    public RespStatus deleteTrainingQuestion(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteTrainingQuestion(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //试题查看
    //id 试题id
    //status 1启用状态的，0停用状态的
    @ResponseBody
    @RequestMapping("/selectTrainQuestionDetail.do")
    public RespTrainQuestionKeyList selectTrainQuestionDetail(Integer status,int id) throws IOException {
        if(status==null)
            status=1;
        RespTrainQuestionKeyList respTrainQuestionKeyList=trainService.selectTrainQuestionDetail(id,status);
        return respTrainQuestionKeyList;
    }
    //停用、启用干扰项
    //id
    //enabled 1启用，0停用
    //返回值，1成功，0失败，-1干扰项已经有5个了无法启用 ， -2干扰项只有3个了，无法停用
    @ResponseBody
    @RequestMapping("/updateTrainingQuestionKey.do")
    public RespStatus updateTrainingQuestionKey(User user,int id,int enabled) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.updateTrainingQuestionKey(user,id,enabled);
        respStatus.setStatus(status);
        return respStatus;
    }
    //新增干扰项
    //question 试题id
    //disableKey  错误项
    //返回值1成功，0失败，-1已达到上限无法新增
    @ResponseBody
    @RequestMapping("/addDisableKey.do")
    public RespStatus addDisableKey(User user,int question,String disableKey) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.addDisableKey(user,question,disableKey);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除干扰项判断
    //id
    //返回值，1可以删除，0含有已使用过的干扰项，-2干扰项不足不可删除
    @ResponseBody
    @RequestMapping("/deleteDisableKeyJudge.do")
    public RespStatus deleteDisableKeyJudge(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteDisableKeyJudge(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //删除干扰项
    //id
    //返回值，1成功，0失败，-2干扰项不足不可删除
    @ResponseBody
    @RequestMapping("/deleteDisableKey.do")
    public RespStatus deleteDisableKey(User user,int id) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.deleteDisableKey(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }
    //题库基本信息
    //id
    //返回值 content内容，code代号，createName 创建人createDate创建时间
    @ResponseBody
    @RequestMapping("/selectQuestionBankInfo.do")
    public RespTrainMaterialListPage selectQuestionBankInfo(int id) throws IOException {
        RespTrainMaterialListPage respTrainMaterialListPage=trainService.selectQuestionBankInfo(id);
        return respTrainMaterialListPage;
    }
    //题库修改接口
    //id content内容   deleteAttachmentList [{id:},{}]json字符串
    //后续会有新增附件的json串，但因为上传等内容，正在统一修改中，目前不做处理，后期进行处理。预留字段 addAttachmentList[]Json字符串
    //返回值 1成功 0失败
    @ResponseBody
    @RequestMapping("/updateTrainingQuestionBank.do")
    public RespStatus updateTrainingQuestionBank(User user,int id,String name,String deleteAttachmentList,String addAttachmentList,String module) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=trainService.updateTrainingQuestionBank(user,id,name,deleteAttachmentList,addAttachmentList,module);
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改记录接口
    //id
    //返回值 trainingQuestionBank 当前题库信息
    //trainingQuestionBankHistoryList 修改记录
    @ResponseBody
    @RequestMapping("/selectTrainBankHistoryList.do")
    public RespTrainBankHistoryList selectTrainBankHistoryList(int id) throws IOException {
        RespTrainBankHistoryList respTrainBankHistory=trainService.selectTrainBankHistoryList(id);
        return respTrainBankHistory;
    }
    //修改记录详情
    //id
    //返回值 trainingQuestionBankHistory 当前题库信息
    //trainingQuestionBankAttachmentHistoryList 修改记录
    @ResponseBody
    @RequestMapping("/selectTrainBankHistoryInfo.do")
    public RespTrainBankAttachmentHistoryList selectTrainBankHistoryInfo(int id) throws IOException {
        RespTrainBankAttachmentHistoryList respTrainBankAttachmentHistoryList=trainService.selectTrainBankHistoryInfo(id);
        return respTrainBankAttachmentHistoryList;
    }
}
