<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResVisMapper">

    <select id="getSingle"  parameterType="resVisit" resultType="resVisit" >
        select
        id, file, type, memo, creator, create_name AS createName, create_date AS createDate, updator,
        update_name AS updateName, update_date AS updateDate
        from t_resource_visit
        where id = #{id}
    </select>

    <select id="listPage"  parameterType="hashmap" resultType="resVisit" >
        select
        id, file, type, memo, creator, create_name AS createName, create_date AS createDate, updator,
        update_name AS updateName, update_date AS updateDate
        from t_resource_visit
    </select>

    <select id="getResVisit" resultType="resVisit">
        select
        id, file, type, memo, creator, create_name AS createName, create_date AS createDate, updator,
        update_name AS updateName, update_date AS updateDate
        from t_resource_visit
        where file = #{file} and type = #{type}
    </select>

    <select id="getVisitByDatelistPage" parameterType="hashmap" resultType="resVisit">
        select
        id, file, type, memo, creator, create_name AS createName,
        MAX(DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s')) AS createDate, updator,update_name AS updateName,
        update_date AS updateDate , count(*) num
        from t_resource_visit
        where type = #{type}
        <if test="endTime == null" >
            and DATE(create_date) = #{startTime}
        </if>
        <if test="endTime != null" >
            and DATE(create_date) between #{startTime} and #{endTime}
        </if>
        and file in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by
        <if test= 'eventType == "1"' >
            creator
        </if>
        <if test= 'eventType == "2"' >
            file
        </if>
        ORDER BY num DESC
    </select>

    <select id="getRecordNum" resultType="resVisit">
        select
        count(*) num, DATE_FORMAT(create_date,'%Y-%m-%d') AS createDate
        from t_resource_visit
        where type = #{type} and DATE(create_date) between #{startTime} and #{endTime}
        and file in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="creator != null" >
            and creator = #{creator}
        </if>
        <if test="file != null" >
            and file = #{file}
        </if>
    </select>

    <select id="getDetailRecordByPersonlistPage" resultType="resVisit">
        select
        id, file, type, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,update_name AS updateName,
        update_date AS updateDate
        from t_resource_visit
        where creator = #{creator} and type = #{type} and and DATE(create_date) = #{time}
    </select>

    <select id="getAllPersonVisNumByFilelistPage" resultType="resVisit">
        select
        id, file, type, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,update_name AS updateName,
        update_date AS updateDate , count(*) num
        from t_resource_visit
        where type = #{type} and file = #{file}
        <if test="date != null" >
            and DATE(create_date) = #{date}
        </if>
        group by creator
    </select>

    <select id="getAllPersonVisNumByCreatorlistPage" resultType="resVisit">
        select
        id, file, type, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,update_name AS updateName,
        update_date AS updateDate , count(*) num
        from t_resource_visit
        where type = #{type} and creator = #{creator}
        <if test="date != null" >
            and DATE(create_date) = #{date}
        </if>
        group by file
    </select>

    <select id="getDetailRecordByFilelistPage" resultType="resVisit">
        select
        id, file, type, memo, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator,update_name AS updateName,
        update_date AS updateDate
        from t_resource_visit
        where type = #{type} and creator = #{creator} and file = #{file}
        <if test="date != null" >
            and DATE(create_date) = #{date}
        </if>
        order by create_date desc
    </select>

    <select id="getMonthForVisit" resultType="java.lang.String">
        select
        <if test= 'type == "1"' >
            DATE_FORMAT(CEIL(create_date),'%c') AS m
        </if>
        <if test= 'type == "2"' >
            DATE_FORMAT(CEIL(create_date),'%Y') AS m
        </if>
        from t_resource_visit
        where type = #{type}
        <if test="creator != null" >
            and creator = #{creator}
        </if>
        <if test="file != null" >
            and file = #{file}
        </if>
        and DATE(create_date) between #{startTime} and #{endTime}
        group by m
        order by create_date asc
    </select>

    <delete id="delete" parameterType="resVisit" >
        delete from t_resource_visit
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="resVisit" >
        insert into t_resource_visit (file, type,
        memo, creator, create_name,
        create_date, updator, update_name,
        update_date)
        values (#{file}, #{type},
        #{memo}, #{creator}, #{createName},
        #{createDate}, #{updator}, #{updateName},
        #{updateDate})
    </insert>

    <update id="update" useGeneratedKeys="true" keyProperty="id" parameterType="resVisit" >
        update t_resource_visit
        <set >
            <if test="file != null" >
                file = #{file},
            </if>
            <if test="type != null" >
                type = #{type},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>