package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.modules.schedule.entity.MemoScheduleImage;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleImageHistory;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleSupplementImage;
import cn.sphd.miners.modules.schedule.service.MemoScheduleHistoryService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleImageService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleSupplementService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class MemoUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            switch (entityClass) {
                case "MemoScheduleImage":
                    MemoScheduleImageService memoScheduleImageService= ac.getBean(MemoScheduleImageService.class, "memoScheduleImageService");
                    MemoScheduleImage entity = memoScheduleImageService.getMemoScheduleImageById(id);
                    if (entity != null) {
                        return filename.equals(entity.getNormal());
                    }
                    break;
                case "MemoScheduleImageHistory":
                    MemoScheduleHistoryService memoScheduleHistoryService = ac.getBean(MemoScheduleHistoryService.class, "memoScheduleHistoryService");
                    MemoScheduleImageHistory memoScheduleImageHistory=memoScheduleHistoryService.getMemoScheduleImageHistoryById(id);
                    if(memoScheduleImageHistory != null) {
                        return  filename.equals(memoScheduleImageHistory.getNormal());
                    }
                    break;
                case "MemoScheduleSupplementImage":
                    MemoScheduleSupplementService memoScheduleSupplementService=ac.getBean(MemoScheduleSupplementService.class,"memoScheduleSupplementService");
                    MemoScheduleSupplementImage memoScheduleSupplementImage = memoScheduleSupplementService.getMemoScheduleSupplementImageById(id);
                    if(memoScheduleSupplementImage != null) {
                        return  filename.equals(memoScheduleSupplementImage.getNormal());
                    }
                    break;
            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public MemoUsing(Integer id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }

    public MemoUsing(){

    }

    public Integer getId() {
        return id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }
}
