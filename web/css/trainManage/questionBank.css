.mianCon{ display:none;  }
.mianCon.mainCon1{ display:block;  }
.search{  border: 1px solid #0b94ea; display: inline-block; border-radius: 20px; width:290px; padding: 4px 0; position: relative; float: right; margin-right: 30px;  }
.search span:nth-child(1){ border-radius: 20px 0 0 20px; background:#0b94ea;padding: 5px 10px; color: #fff;    }
.search span:nth-child(3){ text-align: right; width:44px; height:27px; border-radius:0 20px 20px 0;  padding: 5px 10px; position: absolute; right: 0; top: 0;   }
.search span:nth-child(3):hover{ background:#ecf6ff;    }
.search span:nth-child(3)>i{ display:inline-block; width:20px; height: 20px; background: url("../technology/img/search.png") no-repeat; background-size:20px 20px;    }
.search input{ border:none; margin: 0; display: inline-block; height: 27px; position: absolute; top: 0; padding: 0 5px; width:200px;    }
.search input:focus{ border:none;    }
td .fa,.bounce .fa{  color: #0b94ea; cursor: default; }
.linkBtn{ color: #0b94ea; cursor: default; line-height:40px;  }
.redLink{ color: #ea4c3e; cursor: default; line-height:40px;   }
.redLink:hover, .linkBtn:hover{ text-decoration: underline; font-weight: bold;  }
.scanFileList,.attachList{ max-height:200px; overflow-y: auto;  }
.attachList .fa { font-size:50px; color:#0b94ea; line-height:50px;    }
.attachItem{ font-size: 0.8em;display: flex; border: 1px dotted #ccc; padding: 10px; background: #eee; border-radius:3px; }
.attachItem>div{ flex:1;  }
.attachItem>div:nth-child(1),.attachItem>div:nth-child(3){ text-align: center; }
.attachItem>div:nth-child(2){ flex:4; }
.fileName{ width: 95%; word-break: keep-all; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.addAttachCon{ display: flex;  }
.addAttachCon>div:nth-child(1){ flex:6; }
.addAttachCon>div:nth-child(2){ flex:1; }
.pannel,.addAttachCon{ margin:10px 15px;  }
.bankName{ font-size:27px;  }
.bankCreate{ font-size: 13px; color: #666  }
#editSource{ width:700px;  }
#editQuestion,#questionScan, #stopQuestion{ width:670px;  }
#editQuestion .bonceCon, #editSource .bonceCon{ max-height:400px; overflow-y: auto;   }
.interfereOptionList .interfereOption textarea{ padding-right: 20px;width:490px;  }
#sourceTxt{ width:100%;height:74px;   }
body hr{ border-top:1px solid #ccc;  }
.questionType{ margin:0 0 10px 20px;   }
.editS{  background: #fff; padding: 10px; margin-top: 5px; height:auto;  }
.tip{ text-align: center; }
#editQuestion .qt2 textarea[disabled]{ background: #eee; }
.txtLeft{ text-align: left!important; }
.tfType i.fa{ color:#0b94ea;  }
.tfType{ margin-left: 30px; }
.tfType i{ margin-right: 10px; }
.bankCodeCss{padding: 6px 0;  height: 34px;display: inline-block;}
.keywordSearch input,.bonceCon .keywordSearch input {  padding: 0 10px 0 20px;  font-size: 12px;  min-width: 200px;  height: 31px;  line-height: 31px;}
.searchSect button {  border-top-right-radius: 5px;border-bottom-right-radius: 5px; }
.line{  padding-bottom: 12px;}
.searchSect{margin-right: 20px;}
.keywordSearch span:nth-child(1){line-height: 34px;  margin-right: 10px;}
.scanFileList .fa { font-size:50px; color:#0b94ea; line-height:50px;    }
.scanFileList .attachItem> div:nth-child(3){flex: 2;padding-top: 12px;}
.scanFileList .attachItem> div:nth-child(3) a:first-child{padding-right: 18px;}
.scanFileList .attachItem> div:nth-child(2) {line-height: 24px;  }
.clearInputVal {position: absolute;  top: 46px;  right: 20px;  color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
.interfereOption{position: relative;}
textarea:focus + i.clearInputVal{  display: block;  }
.clearArea,.clearInput{position: relative;}
.clearArea .clearAreaVal{position: absolute; top: 46px;  right: 20px;  color: #0070c0;  display: none;}
.clearInput .clearInputVal{position: absolute; top: 20px;  right: 10px;  color: #0070c0;  display: none;}
.clearArea textarea:focus + i.clearAreaVal{  display: block;  }
.clearInput input:focus + i.clearInputVal{  display: block;  }
.tfType[disabled="disabled"]{padding: 2px;background: #d1d1d1;cursor: not-allowed;}
.tfType[disabled="disabled"] i.fa{color: #aaa;}
.ty-table-control tbody tr td span.ty-color-blue,.ty-table-control tbody tr td span.ty-color-red{display: inline-block;min-width: 56px;}
