package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.SubjectBalanceSheet;

import java.math.BigDecimal;

/**
 * Created by root on 17-5-22.
 */
public class ReportsUtils {

    /*设置该科目的余额符号，如果余额方向和该科目方向一致则是正数，否则是负号*/
    public void settingSybol(SubjectBalanceSheet balanceSheet) {
        String subDirection = balanceSheet.getSubDirection();           //科目方向
        String beginDirection = balanceSheet.getBeginningDirection();   //年初方向
        String previousDirection = balanceSheet.getPreviousDirection(); //期初方向
        String balanceDirection = balanceSheet.getBalanceDirection();   //期末方向

        BigDecimal beginBalance = balanceSheet.getBeginningBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getBeginningBalance();   //年初余额
        BigDecimal previousBalance = balanceSheet.getPreviousBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getPreviousBalance(); //期初余额
        BigDecimal balance = balanceSheet.getBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getBalance();                 //期末余额
        String subject = balanceSheet.getSubject();
        if (!subDirection.equals(beginDirection) && (!"0".equals(beginDirection) && !"3".equals(beginDirection))) {
            //如果科目方向与年初方向相反，把年初余额变成负数
            beginBalance = beginBalance.negate();
            balanceSheet.setBeginningBalance(beginBalance);
        }
        if (!subDirection.equals(previousDirection) && (!"0".equals(previousDirection) && !"3".equals(previousDirection))) {
            //如果科目方向与期初方向相反，把期初余额变成负数
            previousBalance = previousBalance.negate();
            balanceSheet.setPreviousBalance(previousBalance);
        }
        if (!subDirection.equals(balanceDirection) && (!"0".equals(balanceDirection) && !"3".equals(balanceDirection))) {
            //如果科目方向与期末方向相反，把期末余额变成负数
            balance = balance.negate();
            balanceSheet.setBalance(balance);
        }

    }


    /*将余额累计在父科目上*/
    public void statisticsBalance(SubjectBalanceSheet parent, SubjectBalanceSheet child) {
        BigDecimal PbeginBalance = parent.getBeginningBalance() == null ? BigDecimal.valueOf(0) : parent.getBeginningBalance();   //父科目年初余额
        BigDecimal PpreviousBalance = parent.getPreviousBalance() == null ? BigDecimal.valueOf(0) : parent.getPreviousBalance(); //父科目期初余额
        BigDecimal Pbalance = parent.getBalance() == null ? BigDecimal.valueOf(0) : parent.getBalance();                 //父科目期末余额

        BigDecimal CbeginBalance = child.getBeginningBalance() == null ? BigDecimal.valueOf(0) : child.getBeginningBalance();   //子科目年初余额
        BigDecimal CpreviousBalance = child.getPreviousBalance() == null ? BigDecimal.valueOf(0) : child.getPreviousBalance(); //子科目期初余额
        BigDecimal Cbalance = child.getBalance() == null ? BigDecimal.valueOf(0) : child.getBalance();                 //子科目期末余额

        BigDecimal Pcredit = parent.getCredit() == null ? BigDecimal.valueOf(0) : parent.getCredit();                   //父科目的本期借方发生额
        BigDecimal Pdebit = parent.getDebit() == null ? BigDecimal.valueOf(0) : parent.getDebit();                     //父科目的本期贷方发生额
        BigDecimal PcreditAccumulative = parent.getCreditAccumulative() == null ? BigDecimal.valueOf(0) : parent.getCreditAccumulative();  //父科目的本年借方发生额
        BigDecimal PdebitAccumulative = parent.getDebitAccumulative() == null ? BigDecimal.valueOf(0) : parent.getDebitAccumulative();    //父科目的本年贷方发生额

        BigDecimal Ccredit = child.getCredit() == null ? BigDecimal.valueOf(0) : child.getCredit();                   //子科目的本期借方发生额
        BigDecimal Cdebit = child.getDebit() == null ? BigDecimal.valueOf(0) : child.getDebit();                     //子科目的本期贷方发生额
        BigDecimal CcreditAccumulative = child.getCreditAccumulative() == null ? BigDecimal.valueOf(0) : child.getCreditAccumulative();  //子科目的本年借方发生额
        BigDecimal CdebitAccumulative = child.getDebitAccumulative() == null ? BigDecimal.valueOf(0) : child.getDebitAccumulative();    //子科目的本年贷方发生额


        parent.setBeginningBalance(PbeginBalance.add(CbeginBalance));
        parent.setPreviousBalance(PpreviousBalance.add(CpreviousBalance));
        parent.setBalance(Pbalance.add(Cbalance));
        parent.setCredit(Pcredit.add(Ccredit));
        parent.setDebit(Pdebit.add(Cdebit));
        parent.setCreditAccumulative(PcreditAccumulative.add(CcreditAccumulative));
        parent.setDebitAccumulative(PdebitAccumulative.add(CdebitAccumulative));
    }

    /*设置该科目的余额的正负数*/
    public void settingSymbolAbs(SubjectBalanceSheet balanceSheet) {
        BigDecimal PbeginBalance = balanceSheet.getBeginningBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getBeginningBalance();   //父科目年初余额
        BigDecimal PpreviousBalance = balanceSheet.getPreviousBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getPreviousBalance(); //父科目期初余额
        BigDecimal Pbalance = balanceSheet.getBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getBalance();                 //父科目期末余额
        balanceSheet.setBeginningBalance(PbeginBalance.abs());
        balanceSheet.setPreviousBalance(PpreviousBalance.abs());
        balanceSheet.setBalance(Pbalance.abs());
//        balanceSheet.setBeginningBalance(PbeginBalance);
//        balanceSheet.setPreviousBalance(PpreviousBalance);
//        balanceSheet.setBalance(Pbalance);
    }

    /*确定余额统计后该科目的方向，根据余额的正负数判断，如果是正数，和科目本身方向相同，等于零是平，否则相反*/
    public void settingDirection(SubjectBalanceSheet balanceSheet) {
        BigDecimal PbeginBalance = balanceSheet.getBeginningBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getBeginningBalance();   //父科目年初余额
        BigDecimal PpreviousBalance = balanceSheet.getPreviousBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getPreviousBalance(); //父科目期初余额
        BigDecimal Pbalance = balanceSheet.getBalance() == null ? BigDecimal.valueOf(0) : balanceSheet.getBalance();                 //父科目期末余额
        String subcode = balanceSheet.getSubject();
        String subDirection = balanceSheet.getSubDirection();           //科目方向

        //设置期末余额的方向
        if(Pbalance.compareTo(BigDecimal.valueOf(0)) > 0)
        {
            //和科目本身方向相同
            balanceSheet.setBalanceDirection(subDirection);
        }
        if (Pbalance.compareTo(BigDecimal.valueOf(0)) < 0)
        {
            //和科目本身方向相反
            if("1".equals(subDirection))
            {
                balanceSheet.setBalanceDirection("2");
            }
            if("2".equals(subDirection))
            {
                balanceSheet.setBalanceDirection("1");
            }
        }
        if (Pbalance.compareTo(BigDecimal.valueOf(0)) == 0)
        {
            //平
            balanceSheet.setBalanceDirection("3");
        }

        //设置期初余额的方向
        if(PpreviousBalance.compareTo(BigDecimal.valueOf(0)) > 0)
        {
            //和科目本身方向相同
            balanceSheet.setPreviousDirection(subDirection);
        }
        if (PpreviousBalance.compareTo(BigDecimal.valueOf(0)) < 0)
        {
            //和科目本身方向相反
            if("1".equals(subDirection))
            {
                balanceSheet.setPreviousDirection("2");
            }
            if("2".equals(subDirection))
            {
                balanceSheet.setPreviousDirection("1");
            }
        }
        if (PpreviousBalance.compareTo(BigDecimal.valueOf(0)) == 0)
        {
            //平
            balanceSheet.setPreviousDirection("3");
        }

        //设置年初余额的方向
        if(PbeginBalance.compareTo(BigDecimal.valueOf(0)) > 0)
        {
            //和科目本身方向相同
            balanceSheet.setBeginningDirection(subDirection);
        }
        if (PbeginBalance.compareTo(BigDecimal.valueOf(0)) < 0)
        {
            //和科目本身方向相反
            if("1".equals(subDirection))
            {
                balanceSheet.setBeginningDirection("2");
            }
            if("2".equals(subDirection))
            {
                balanceSheet.setBeginningDirection("1");
            }
        }
        if (PbeginBalance.compareTo(BigDecimal.valueOf(0)) == 0)
        {
            //平
            balanceSheet.setBeginningDirection("3");
        }

    }


}
