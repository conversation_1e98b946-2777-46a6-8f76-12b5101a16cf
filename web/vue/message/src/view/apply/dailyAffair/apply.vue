<template>
  <div id="dailyAffairs">
    <TY_NavTop title="日常事务" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="applyTo(item)" v-if="item.pid == pid" v-for="(item, index) in $store.state.menu" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_title">
              <span class="approve_name" v-html="item.name"></span>
              <span :class="!item.corner? 'hd': 'message_count'" v-html="item.corner"></span>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'applyDailyAffairs',
  data () {
    return {
      pid: 0, // 父级id
      isBackHome: false
    }
  },
  created: function () {
    // 从路由获取父级菜单id
    this.pid = this.$route.params.pid
  },
  methods: {
    applyTo: function (item) {
      this.$router.push({
        path: '/' + item.code
      })
    }
  }
}
</script>
