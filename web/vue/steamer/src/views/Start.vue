<template>
  <div id="start" class="start">
    <div class="mainC">
      <el-container>
        <el-aside width="400px" class="left">
          <img class="img" src="../assets/img/za.jpeg" alt="宣发图"/>
        </el-aside>
        <el-main class="mainccc">
          <div class="formCont">
            <div class="navBar">
              <div :class="nav === 1 ? 'active' : ''" @click="nav = 1">
                密码登录
              </div>
              <div :class="nav === 2 ? 'active' : ''" @click="nav = 2">
                短信登录
              </div>
            </div>
            <div class="formcc" v-if="nav === 1">
              <el-form
                  class=""
                  label-position="right"
                  label-width="80px"
                  :model="formLabelAlign"
              >
                <el-form-item label="账号">
                  <el-input
                      v-model="formLabelAlign.name"
                      placeholder="请输入字幕精灵的账号（手机号）"
                  ></el-input>
                </el-form-item>
                <el-form-item label="密码">
                  <el-input
                      type="password"
                      v-model="formLabelAlign.pass"
                      placeholder="请输入密码"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button class="submitcc" type="primary" @click="loginBtn"
                  >登陆
                  </el-button
                  >
                </el-form-item>
                <el-form-item>
                  <el-checkbox
                      v-model="formLabelAlign.jiAcc"
                      label="记住账号"
                  ></el-checkbox>
                  <el-checkbox
                      v-model="formLabelAlign.jiPas"
                      label="记住密码"
                  ></el-checkbox>
                </el-form-item>
              </el-form>
            </div>
            <div class="formcc" v-if="nav === 2">
              <el-form
                  class=""
                  label-position="right"
                  label-width="80px"
                  :model="formLabelAlign"
              >
                <el-form-item label="账号">
                  <el-input
                      v-model="formLabelAlign.name"
                      placeholder="请输入字幕精灵的手机号"
                  ></el-input>
                </el-form-item>
                <el-form-item label="验证码">
                  <el-input
                      v-model="formLabelAlign.code"
                      placeholder="请输入验证码"
                      class="code"
                  ></el-input>
                  <el-button class="codeBtn" @click="sendCode" type="primary">发送验证码</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button class="submitcc" type="primary" @click="codeLoginBtn">登陆</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-main>
      </el-container>

      <div class="clr"></div>
    </div>
  </div>
</template>
<script lang="js">
import {login,sendMobileCode,verificationCode,codeLogin, organizationList, sureLogin} from "@/api/api";
import { ElMessage } from 'element-plus'
import sphdSocket from '@/sys/sphd'
export default {
  name: "Start",
  data() {
    return {
      // za: za,
      nav: 1,
      labelPosition: "right",
      formLabelAlign: {
        name: "",
        pass: "",
        code: "",
        // name: "152000000000",
        // pass: "aa123456",
        // code: "8888",
        jiAcc: "",
        jiPas: "",
      },
    };
  },
  mounted() {
    localStorage.removeItem('loginData')
    localStorage.removeItem('slData')
  },
  methods: {
    loginBtn: async function () {
      let logged = false
      let logonName = this.formLabelAlign.name
      let logonPwd = this.formLabelAlign.pass
      console.log('startvue', logonName, logonPwd)
      let loginRes = await login(logonName, logonPwd)
      let loginData = loginRes.data
      console.log('login', loginData)
      if (loginData.error != undefined) {
        this.$alert(loginData.error, '提示', {
          confirmButtonText: '确定',
        })

      } else if (loginData.user != undefined) {
        logged = true
        // localStorage.setItem('loginData', JSON.stringify(loginData))
        this.$router.push('mainPage')//只有一个机构，进入
      } else {
        let listRes = await organizationList({orgCode: 'liveHelper'}) //orgCode:'liveHelper'，只获取直播助手的机构,
        let listData = listRes.data.data //JsonResult 格式返回，多取一层data
        console.log('organizationList', listData)
        console.log(Array.isArray(listData))
        let user
        listData.forEach((item) => {
          if (item.roleCode == 'super') {//遍历机构（user），查找是超管的机构
            user = item
          }
        })
        if(user != undefined) {
          logged = true
          let slRes = await sureLogin(user)
          console.log('sureLogin 返回值', slRes)
          //更新sphdSocket中的对象，用于兼容message小窗的sphdSocket.user .org代码
          sphdSocket.reflushAuth()
          let slData = slRes.data
          if (slData.error != undefined) {
            console.log('登陆失败！！', slData.error)
            this.$message.error(slData.error)
            // this.$router.push('errPage')
          }else{
            this.$router.push('mainPage')
          }

        }
      }
      // //没有找到，跳转领地页？
      if(!logged) {
        // this.$router.push('errPage')//没有找到，跳转领地页还是报错提示？
      }
    },

    sendCode: function () {
      console.log('sendCode 开始')
      let data = {
        phone: this.formLabelAlign.name
      }
      if(data.phone.length === 11){
        sendMobileCode(data).then(res => {
          console.log('sendMobileCode res=',res)
          let data = res.data.data
          let success = res.data.success
          if (Number(success) === 1) {
            switch (Number(data)) {
              case 1:
                break;
              case 2:
              case 3:
              case 4:
                ElMessage.success('请输入正确的手机号！')
                break;
              default:
                ElMessage.success('验证码已发送！')
            }
          }else{
            ElMessage.error('操作失败！')
          }
        }).catch(err => {
          console.log('sendMobileCode err=', err)
          ElMessage.error('操作失败！')
        })
      }else{
        ElMessage.error('手机号码不正确！')
      }
    },
    codeLoginBtn:function () {
      let data = {
        mobile: this.formLabelAlign.name,
        code: this.formLabelAlign.code,
      }
      if(data.mobile.length === 11 && data.code.length === 4  ){
        verificationCode(data).then(res => {
          console.log('verificationCode res =', res)
          let data = res.data
          if (data) {
            this.codeLoginFun()

          } else {
            ElMessage.error('您输入的验证码有误！')
          }
        }).catch(err => {
          console.log('codeLogin err =', err)

        })
      }else{
        ElMessage.error('请录入正确的数据')
      }
    },
    codeLoginFun:function () {
      codeLogin().then(res => {
        console.log('codeLoginFun res=', res)
        let logged = false
        let user = res.data.user
        let error = res.data.error
        if(error != undefined){
          this.$alert(error, '提示', {
            confirmButtonText: '确定',
          })
        }
        else if(user){ // 只有一个机构，进入
          logged = true
          this.$router.push('mainPage')
        }
        else {
          organizationList({orgCode: 'liveHelper'}).then( res => {
            console.log('organizationList res=', res)
            let listData = res.data.data //JsonResult 格式返回，多取一层data
            let superUser
            listData.forEach((item) => {
              if (item.roleCode == 'super') {//遍历机构（user），查找是超管的机构
                superUser = item
              }
            })
            superUser && sureLogin(superUser).then(res => {
              console.log('sureLogin superUser res=', res)
              let slData = res.data
              sphdSocket.reflushAuth()
              if (slData) {
                logged = true
                this.$router.push('mainPage')
              }
            }).catch(err => {
              console.log('sureLogin superUser err=', err)
            })
          }).catch(err =>{
            console.log('organizationList err=', err)
          })
        }
        // //没有找到，跳转领地页？
        if(!logged) {
          this.$router.push('errPage')//没有找到，跳转领地页还是报错提示？
        }


      }).catch(err => {
        console.log('codeLoginFun err=', err)

      })

    }
  },
}
</script>
<style lang="scss">
#start {
  .mainC {
    width: 90%;
    margin: 100px auto 0;

    .img {
      display: block;
      width: 90%;
    }

    .mainccc {
      .navBar {
        display: flex;
        text-align: center;
        & > div {
          /* background-color: #eee; */
          font-size: 1.1em;
          font-weight: bold;
          padding: 20px;
          width: 50%;
          cursor: pointer;
          border-bottom: 1px solid #fff;

          &.active {
            color: #5927ef;
            border-bottom: 1px solid #5927ef;
          }
        }
      }

      .formCont {
        width: 500px;
        margin: 0 auto;
        background: linear-gradient(to bottom, #b190e9, #e1dffa);
        box-shadow: 2 2px 4px #ccc;
        border-radius: 10px;

        .formcc {
          padding: 40px;

          .code {
            width: 54%;
          }

          .codeBtn {
            width: 40%;
            margin-left: 20px;
          }

          .submitcc {
            display: block;
            width: 100%;
            margin: 20px auto 30px;
          }
        }
      }
    }

    .clr {
      clear: both;
    }
  }
}
</style>
