package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdCommodityMediaHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdCommodityMedia;
import cn.sphd.miners.modules.commodity.entity.PdCommodityMediaHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class PdCommodityMediaHistoryDaoImpl  extends BaseDao<PdCommodityMediaHistory, Serializable> implements PdCommodityMediaHistoryDao {
    @Override
    public PdCommodityMediaHistory insert(PdCommodityMedia pdCommodityMedia,Integer phId) {
        PdCommodityMediaHistory history=new PdCommodityMediaHistory();

        BeanUtils.copyProperties(pdCommodityMedia,history);
        history.setId(null);
        history.setCommodityMedia(pdCommodityMedia.getId());
        history.setCommodityHistory(phId);

        save(history);

        getSession().flush();

        return history;
    }
}
