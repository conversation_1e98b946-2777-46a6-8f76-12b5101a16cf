package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBankAttachmentHistory;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBankHistory;

import java.util.List;

/**
 * @ClassName RespTrainBankAttachmentHistoryList
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/19 11:24
 * @Version 1.0
 */
public class RespTrainBankAttachmentHistoryList {
    private TrainingQuestionBankHistory trainingQuestionBankHistory;
    private List<TrainingQuestionBankAttachmentHistory> trainingQuestionBankAttachmentHistoryList;

    public TrainingQuestionBankHistory getTrainingQuestionBankHistory() {
        return trainingQuestionBankHistory;
    }

    public void setTrainingQuestionBankHistory(TrainingQuestionBankHistory trainingQuestionBankHistory) {
        this.trainingQuestionBankHistory = trainingQuestionBankHistory;
    }

    public List<TrainingQuestionBankAttachmentHistory> getTrainingQuestionBankAttachmentHistoryList() {
        return trainingQuestionBankAttachmentHistoryList;
    }

    public void setTrainingQuestionBankAttachmentHistoryList(List<TrainingQuestionBankAttachmentHistory> trainingQuestionBankAttachmentHistoryList) {
        this.trainingQuestionBankAttachmentHistoryList = trainingQuestionBankAttachmentHistoryList;
    }
}
