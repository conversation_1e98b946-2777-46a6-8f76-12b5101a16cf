<template>
<!--  加强版的element输入框，可以自动补位，限制数字-->
  <el-input
      v-model="value"
      placeholder="请录入正整数"
      v-onlyInteger
      clearable
      @blur="blur_judgePlaceNum($event)"
      @input="handleInput"
  />
</template>

<script>
export default {
  props: {
    modelValue: {},
    placeNum: { // 位数
      type: Number,
      default: 3
    }
  },
  emits: ['update:modelValue', 'inputEvent'],
  computed: {
    value: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  methods: {
    handleInput () {
      this.$emit('inputEvent')
    },
    blur_judgePlaceNum (event) {
      let value = event.target.value
      if (value.length > this.placeNum) {
        this.$message({
          type: 'error',
          message: '超出最大位数！'
        })
        this.$emit('update:modelValue', '')
      } else {
        if (value !== '') {
          let newVal = value.padStart(this.placeNum, '0')
          this.$emit('update:modelValue', newVal)
        }
      }
    }
  },
  directives: {
    // 自定义指令（后面可写到公共部分，只能输入正整数）
    onlyInteger: (el) => {
      // 这会在 `mounted` 和 `updated` 时都调用
      let ele = el.querySelector('input')
      ele.addEventListener('input', () => {
        // 获取输入框的当前值
        let value = ele.value;
        // 使用正则表达式匹配非正整数的部分，并将其替换为空字符串
        value = value.replace(/[^\d]/g, '');
        // 如果值为0，则直接设置为空字符串，因为0不是正整数
        if (value === '0') {
          value = '';
        }
        // 更新输入框的值
        ele.value = value;
      });
    }
  },
  watch: {
    // 每当 placeNum （位数） 改变时，使用此组件的自动增删位数（现在只能增加位数，减少时清空）
    placeNum(newValue, oldValue) {
      if (this.modelValue !== '') {
        if (newValue > oldValue) {
          let newVal = this.modelValue.padStart(this.placeNum, '0')
          this.$emit('update:modelValue', newVal)
        } else {
          this.$emit('update:modelValue', '')
        }
      }
    }
  },
}
</script>