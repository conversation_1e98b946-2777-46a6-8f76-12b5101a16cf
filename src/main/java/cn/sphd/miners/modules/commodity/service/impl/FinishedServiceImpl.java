package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.PdMerchandiseDao;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseHistoryDao;
import cn.sphd.miners.modules.commodity.dao.PdLocationDao;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdLocation;
import cn.sphd.miners.modules.commodity.service.FinishedService;
import cn.sphd.miners.modules.inv.dao.InvWarehouseCategoryDao;
import cn.sphd.miners.modules.inv.dao.InvWarehouseLocationDao;
import cn.sphd.miners.modules.sales.dao.PdCustomerDao;
import cn.sphd.miners.modules.system.entity.User;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinishedServiceImpl implements FinishedService {
    @Autowired
    PdMerchandiseDao productDao;
    @Autowired
    PdLocationDao pdLocationDao;
    @Autowired
    PdMerchandiseHistoryDao productHistoryDao;
    @Autowired
    InvWarehouseLocationDao locationDao;
    @Autowired
    InvWarehouseCategoryDao invWarehouseCategoryDao;
    @Autowired
    PdCustomerDao pdCustomerDao;
    @Override
    public Map<String,Object> getNotEnteredList(Integer oid, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS innerSn,\n" +
                "p.outer_name as name,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate\n" +
                "FROM\n" +
                "t_pd_merchandise p\n" +
                "WHERE\n" +
                " p.org="+oid+" and p.initial_stock   is null\n" +

                "ORDER BY p.create_date";
        return productDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }


    @Override
    public List<Map<String,Object>>  getNotEnteredList(Integer oid) {
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS innerSn,\n" +
                "p.outer_name as name,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate\n" +
                "FROM\n" +
                "t_pd_merchandise p\n" +
                "WHERE\n" +
                " p.org='"+oid+"' and  p.initial_stock is null\n" +

                "ORDER BY p.create_date";

        List<Map<String,Object>> list= pdLocationDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
        return list;
    }
    @Override
    public String addInitialStock(Integer productId, String locationList,User user) {
        PdMerchandise product=productDao.get(productId);
        BigDecimal sum=new BigDecimal(0);
        if (locationList != null&&!"".equals(locationList)) {
            JSONArray list = JSONArray.fromObject(locationList);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject a = list.getJSONObject(i);
                    //库位id
                    String locationId=a.getString("locationId");
                    //数量
                    BigDecimal amount=new BigDecimal(a.getString("amount"));

                    sum=sum.add(amount);

                    PdLocation pdLocation=new PdLocation();


                    if(locationId!=null&&!"".equals(locationId)){
                        pdLocation.setLocation(Integer.valueOf(locationId));
                    }

                    pdLocation.setAmount(amount);
                    pdLocation.setProduct(productId);
                    pdLocation.setUnit(product.getUnit());
                    pdLocation.setCreator(user.getUserID());
                    pdLocation.setCreateDate(new Date());
                    pdLocation.setCreateName(user.getUserName());
                    pdLocation.setOperation("1");
                    pdLocation.setVersionNo(1);
                    pdLocationDao.save(pdLocation);

                }
            }

        }
        product.setInitialStock(sum);
        if(product.getCurrentStock()==null){
            product.setCurrentStock(sum);
        }
        if(product.getAvailableStock()==null){
            product.setAvailableStock(sum);
        }


        product.setOperation("4");
        if(product.getVersionNo()==null){
            product.setVersionNo(1);
        }else{
            product.setVersionNo(product.getVersionNo()+1);
        }
        productHistoryDao.insert(product);
        productDao.update(product);
        return "success";
    }

    @Override
    @Transactional
    public String updateInitialStock(Integer id, BigDecimal num,User user) {
        PdMerchandise product=productDao.getByHQL("from PdMerchandise where id="+id);
        if (product==null){
            return "商品信息不存在";
        }
        productDao.getSession().clear();
        if(product.getInitialStock()!=null){
            if(product.getInitialStock().compareTo(num)==-1){
                BigDecimal cha=num.subtract(product.getInitialStock());
                product.setInitialStock(num);
                if(product.getCurrentStock()==null){
                    product.setCurrentStock(cha);
                }else{
                    product.setCurrentStock(product.getCurrentStock().add(cha));
                }

                if(product.getAvailableStock()==null){
                    product.setAvailableStock(cha);
                }else{
                    product.setAvailableStock(product.getAvailableStock().add(cha));
                }
            }else if(product.getInitialStock().compareTo(num)==1){
                BigDecimal cha=product.getInitialStock().subtract(num);

                product.setInitialStock(num);
                if(product.getCurrentStock()==null){
                    product.setCurrentStock(cha);
                }else{
                    if((product.getCurrentStock().subtract(cha)).compareTo(new BigDecimal(0))==-1){
                        return "当前库存修改后为负数,不能修改";
                    }else{
                        product.setCurrentStock(product.getCurrentStock().subtract(cha));
                    }
                }

                if(product.getAvailableStock()==null){
                    product.setAvailableStock(cha);
                }else{
                    if((product.getAvailableStock().subtract(cha)).compareTo(new BigDecimal(0))==-1){
                        return "可用库存修改后为负数,不能修改";
                    }else{
                        product.setAvailableStock(product.getAvailableStock().subtract(cha));
                    }
                }
            }else{

                product.setInitialStock(num);
                if(product.getCurrentStock()==null){
                    product.setCurrentStock(num);
                }
                if(product.getAvailableStock()==null){
                    product.setAvailableStock(num);
                }
            }
        }

        product.setUpdateDate(new Date());
        product.setUpdateName(user.getUserName());
        product.setUpdator(user.getUserID());
        if(product.getVersionNo()==null){
            product.setVersionNo(1);
        }else{
            product.setVersionNo(product.getVersionNo()+1);
        }
        product.setOperation("4");
        productHistoryDao.insert(product);
        productDao.update(product);
        return "success";
    }

    @Override
    public Map<String, Object> getPdProductList(Integer oid, String type,Integer categoryId, String param,Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS innerSn,\n" +
                "p.outer_name as name,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.initial_stock as initialStock,\n" +
                "ifnull(p.current_stock,0 ) currentStock,\n" +
                "p.available_stock as availableStock,\n" +
                "p.minimum_stock as minimumiStock,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate,\n" +
                "ifnull((SELECT COUNT(b.id)  from t_pd_location b where b.product=p.id and b.location is not null),0)  num\n" +
                "FROM\n" +
                "t_pd_merchandise p\n" +
                "WHERE\n" +
                "p.org="+oid+" and p.enabled ='1'  and p.initial_stock is not null \n" ;

        if(StringUtils.isNotEmpty(param)){
            sql=sql+ "and (p.outer_sn like '%"+param+"%' or p.outer_name like '%"+param+"%')\n" ;
        }
        if(type!=null&&!"".equals(type)){
            if("1".equals(type)){
                sql=sql+" and p.type='1' ";
            }else{
                sql=sql+" and p.customer="+categoryId+" ";
            }
        }
               sql+= "GROUP BY p.id\n" +
                "ORDER BY p.create_date";

        return productDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getSuspendPdProductList(Integer oid, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS innerSn,\n" +
                "p.outer_name as name,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.initial_stock as initialStock,\n" +
                "ifnull(p.current_stock,0 ) currentStock,\n" +
                "p.available_stock as availableStock,\n" +
                "p.minimum_stock as minimumiStock,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate,\n" +
                "ifnull((SELECT COUNT(b.id)  from t_pd_location b where b.product=p.id and b.location is not null),0)  num\n" +
                "FROM\n" +
                "t_pd_merchandise p\n" +
                "WHERE\n" +
                "p.org="+oid+" and p.enabled ='0' and p.initial_stock is not null \n" ;

        sql+= "GROUP BY p.id\n" +
                "ORDER BY p.create_date";

        return productDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    public List<Map<String,Object>> getPdProductList(Integer oid) {

        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS innerSn,\n" +
                "p.outer_name as name,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.initial_stock as initialStock,\n" +
                "p.current_stock as currentStock,\n" +
                "p.available_stock as availableStock,\n" +
                "p.minimum_stock as minimumiStock,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate,\n" +
                "(SELECT COUNT(b.id)  from t_pd_location b where b.product=p.id and b.location is not null) num\n" +
                "FROM\n" +
                "t_pd_merchandise p\n" +
                "LEFT JOIN t_pd_location l ON p.id = l.product \n" +
                "WHERE\n" +
                "l.id is not null and\n"+
                "p.org="+oid+"\n" +
                "GROUP BY p.id\n" +
                "ORDER BY p.create_date";

        List<Map<String,Object>> list= pdLocationDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
        return list;
    }

    @Override
    public List<Map<String,Object> > getInitialStockRecord(Integer id) {
        String sql="SELECT\n" +
                "h.initial_stock AS initialStock,\n" +
                "h.update_name AS updateName,\n" +
                "h.update_date AS updateDate\n" +
                "FROM\n" +
                "t_pd_merchandise_history h\n" +
                "where h.operation ='4' and h.product="+id+
                "  order by h.version_no";

        List<Map<String,Object>> list= pdLocationDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

        if(list.size()<=1){
            return new ArrayList<>();

        }

        List<Map<String,Object>> mapList=new ArrayList<>();
        for (int i=1;i<list.size();i++){
            Map<String,Object> map=new HashMap();
            map.put("before",list.get(i-1).get("initialStock"));
            map.put("after",list.get(i).get("initialStock"));
            map.put("updateName",list.get(i).get("updateName"));
            map.put("updateDate",list.get(i).get("updateDate"));
            map.put("customerName","");
            mapList.add(map);
        }

        return mapList;
    }

    @Override
    public List<Map<String, Object>> getMinimumStockRecord(Integer id) {
        String sql="SELECT\n" +
                "h.mininum_stock AS mininumStock,\n" +
                "h.update_name AS updateName,\n" +
                "h.update_date AS updateDate\n" +
                "FROM\n" +
                "t_pd_merchandise_history h\n" +
                "where h.operation ='5' and h.product="+id+
                "  order by h.version_no";

        List<Map<String,Object>> list= pdLocationDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

        if(list.size()<=1){
            return new ArrayList<>();

        }

        List<Map<String,Object>> mapList=new ArrayList<>();
        for (int i=1;i<list.size();i++){
            Map<String,Object> map=new HashMap();
            map.put("before",list.get(i-1).get("mininumStock"));
            map.put("after",list.get(i).get("mininumStock"));
            map.put("updateName",list.get(i).get("updateName"));
            map.put("updateDate",list.get(i).get("updateDate"));
            map.put("customerName","");
            mapList.add(map);
        }

        return mapList;
    }

    @Override
    public Map<String, Object> getLocationList(Integer oid) {
        String sql = "SELECT iwl.id as locationId, iwl.location_name as locationName,location_code as locationCode from t_inv_warehouse_location iwl\n" +
                "where iwl.shelf in (SELECT id from t_inv_warehouse_shelf where region in ( select id from t_inv_warehouse_region where warehouse in ( select id from t_inv_warehouse_base where org=?0 and category='1')))";
        return locationDao.findMapByConditionNoPage(sql, new Object[]{oid});
    }

    @Override
    public Map<String, Object> getLocationDetail(Integer locationId) {
        String sql="SELECT new map(\n" +
                "iwl.id as locationId,\n" +
                "iwl.locationCode as locationCode,\n" +
                "iwb.warehouseCode as  warehouseCode,\n" +
                "iwe.regionCode as regionCode,\n" +
                "iwl.shelfCode as shelfCode,\n" +
                "iws.layer as layer,\n" +
                "iwl.createName as createName,\n" +
                "iwl.createDate as createDate)\n" +
                "FROM\n" +
                "InvWarehouseLocation iwl\n" +
                "LEFT JOIN \n" +
                "InvWarehouseShelf iws on iws.id=iwl.shelf\n" +
                "LEFT JOIN \n" +
                "InvWarehouseRegion iwe on iwe.id=iws.region\n" +
                "LEFT JOIN\n" +
                "InvWarehouseBase iwb on iwe.warehouse=iwb.id\n"+
                "where iwl.id="+locationId;

        Map<String,Object> param=new HashMap<>();

        Map<String,Object> data= (Map<String, Object>) locationDao.getByHQLWithNamedParams(sql,param);
        if(data!=null){
            String sql2="SELECT\n" +
                    "p.id AS id,\n" +
                    "p.outer_sn AS innerSn,\n" +
                    "p.outer_name as name,\n" +
                    "p.model as model,\n" +
                    "p.specifications as specifications,\n" +
                    "p.unit as unit,\n" +
                    "l.amount as amount\n" +
                    "FROM\n" +
                    "t_pd_location l\n" +
                    "LEFT JOIN t_pd_merchandise p ON p.id = l.product\n" +
                    "WHERE\n" +
                    "l.location="+locationId;
          List<Map<String,Object>> list= pdLocationDao.getSession().createSQLQuery(sql2).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

          data.put("list",list);

        }

        return data;
    }

    @Override
    public Map<String, Object> getLocationListByProduct(Integer id) {
        String sql="SELECT\n" +
                "new map(p.id AS id,\n" +
                "p.outerSn AS innerSn,\n" +
                "p.outerName as name,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "coalesce(p.currentStock,0)  as currentStock,\n"+
                "p.createName as createName,\n" +
                "p.createDate as createDate)\n" +
                "FROM\n" +
                "PdMerchandise p\n" +
                "WHERE\n" +
                "p.id =:id";
        Map<String,Object> param=new HashMap<>();
        param.put("id",id);
        Map<String,Object> data= (Map<String, Object>) locationDao.getByHQLWithNamedParams(sql,param);

        if(data!=null){
            String sql2="select l.id as id,l.location as locationId,iwl.location_name as locationName,iwl.location_code as locationCode,l.amount as amount from \n" +
                    "t_pd_location l\n" +
                    "LEFT JOIN t_inv_warehouse_location iwl on iwl.id=l.location\n" +
                    "where l.location is not null and l.product="+id ;
            List<Map<String,Object>> list= pdLocationDao.getSession().createSQLQuery(sql2).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
            data.put("num",list.size());
            data.put("list",list);

        }
        return data;
    }

    @Override
    public String updateLocation(Integer productId, String locationList, User user) {
        PdMerchandise product=productDao.get(productId);
        BigDecimal sum=new BigDecimal(0);

        //先删除
        Map<String,Object> params=new HashMap<>();
        params.put("product",productId);
        String hql = "delete from PdLocation where product=:product";
        pdLocationDao.queryHQLWithNamedParams(hql, params);

        if (locationList != null&&!"".equals(locationList)) {
            JSONArray list = JSONArray.fromObject(locationList);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject a = list.getJSONObject(i);
                    //库位id
                    String locationId=a.getString("locationId");
                    //数量
                    BigDecimal amount= new BigDecimal(a.getString("amount"));


                    sum=sum.add(amount);


                    PdLocation pdLocation=new PdLocation();

                    if(locationId!=null&&!"".equals(locationId)){
                        pdLocation.setLocation(Integer.valueOf(locationId));
                    }

                    pdLocation.setAmount(amount);
                    pdLocation.setProduct(productId);

                    pdLocation.setUnit(product.getUnit());
                    pdLocation.setCreator(user.getUserID());
                    pdLocation.setCreateDate(new Date());
                    pdLocation.setCreateName(user.getUserName());
                    pdLocation.setOperation("1");
                    pdLocation.setVersionNo(1);
                    pdLocationDao.save(pdLocation);



                }
            }

        }


        product.setCurrentStock(sum);

        product.setOperation("2");
        if(product.getVersionNo()==null){
            product.setVersionNo(1);
        }else{
            product.setVersionNo(product.getVersionNo()+1);
        }
        productHistoryDao.insert(product);
        productDao.update(product);
        return "success";
    }

    @Override
    public Map<String, Object> search(Integer oid, String param, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.outer_sn AS innerSn,\n" +
                "p.outer_name as name,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "IFNULL(p.initial_stock,0) as initialStock,\n" +
                "IFNULL(p.current_stock,0) as currentStock,\n" +
                "IFNULL(p.available_stock,0) as availableStock,\n" +
                "IFNULL(p.minimum_stock,0) as minimumiStock,\n" +
                "p.create_name as createName,\n" +
                "p.create_date as createDate,\n" +
                "(SELECT COUNT(b.id)  from t_pd_location b where b.product=p.id and b.location is not null)  num\n" +
                "FROM\n" +
                "t_pd_merchandise p\n" +
                "LEFT JOIN t_pd_location l ON p.id = l.product\n" +
                "WHERE\n" +
                "p.org="+oid+"\n";
                if(param!=null&&!"".equals(param)){
                    sql=sql+ "and (p.outer_sn like '%"+param+"%' or p.outer_name like '%"+param+"%')\n" ;
                }

                sql=sql+"GROUP BY p.id\n" +
                "ORDER BY p.create_date";

        return productDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    public List<Map<String, Object>> getMtCategory(Integer id, Integer oid) {
        String sql = "SELECT\n" +
                "	sc.id,\n" +
                "	'2' as type,\n" +
                "	ifnull(sc.full_name,'') name,\n" +
                "	(select count( distinct  p.id) from t_pd_merchandise p where p.initial_stock is not null and p.customer=sc.id and p.enabled='1') num\n" +
                "FROM\n" +
                "	t_sl_customer sc\n" +
                "WHERE\n" +
                "	sc.oid ="+oid+" and sc.type='1'";



        List< Map<String,Object>> list=new ArrayList<>();
        if (id==null){
            list=pdCustomerDao. getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();

            Map m=new HashMap();
            m.put("id",0);
            m.put("name","待分类");
            m.put("type","1");
            String sql2="select count(distinct p.id) from PdMerchandise p   where type='1'  and p.initialStock is not null and enabled='1' and p.org="+oid;

            m.put("num",pdCustomerDao.getDtoByHql(sql2));
            list.add(0,m);
        }else if(id==0){
            Map m=new HashMap();
            m.put("id",0);
            m.put("name","待分类");
            m.put("type","1");
            String sql2="select count(distinct p.id) from PdMerchandise p  where type='1'  and p.initialStock is not null and enabled='1' and p.org="+oid;

            m.put("num",pdCustomerDao.getDtoByHql(sql2));
            list.add(m);
        }else{
            Map m=new HashMap();
            m.put("id",id);
            m.put("name","待分类");
            m.put("type","2");
            String sql2="select count(distinct p.id) from PdMerchandise p   where p.org="+oid+" and p.type='2' and p.initialStock is not null and p.customer="+id+" and p.enabled='1' ";


            m.put("num",pdCustomerDao.getDtoByHql(sql2));
            list.add(m);
        }


        return list;
    }

    @Override
    public Map<String, Object> totalWarehouse(Integer oid) {
        //获取仓库信息
        Map<String, Object> map=new HashMap<>();
        String qySql="select COUNT(id) from InvWarehouseRegion where warehouse in (select id from  InvWarehouseBase where org="+oid+" and category='1')";
        String hjSql="SELECT COUNT(id) from InvWarehouseShelf  where region in (select id from InvWarehouseRegion where warehouse in (select id from  InvWarehouseBase where org="+oid+" and category='1') )";
        String kwSql="SELECT COUNT(id) from InvWarehouseLocation  where shelf in (select id from InvWarehouseShelf where region in (select id from InvWarehouseRegion where warehouse in (select id from  InvWarehouseBase where org="+oid+" and category='1') ))";
        String kSql="SELECT COUNT(iwl.id) from InvWarehouseLocation iwl LEFT JOIN PdLocation l on iwl=l.location where shelf in (select id from InvWarehouseShelf where region in (select id from InvWarehouseRegion where warehouse in (select id from  InvWarehouseBase where org="+oid+" and category='1') )) and  l.id is null";

        Long quyu=(Long)locationDao.getByHQLWithNamedParams(qySql,new HashMap<>());
        Long huojia= (Long) locationDao.getByHQLWithNamedParams(hjSql,new HashMap<>());
        Long kuwei=  (Long)locationDao.getByHQLWithNamedParams(kwSql,new HashMap<>());
        Long kongwei=(Long) locationDao.getByHQLWithNamedParams(kSql,new HashMap<>());
        map.put("quyu",quyu);
        map.put("huojia",huojia);
        map.put("kuwei",kuwei);
        map.put("kongwei",kongwei);

        String sql="select b.id,0 as zl,b.warehouse_name warehouseName,b.qy as quyu,b.hj as huojia,b.kw as kuwei,count(n.id) kongwei from (select b.id,b.warehouse_name,b.qy,b.hj,COUNT(l.id) kw from (select b.id,b.qy,b.warehouse_name,COUNT(s.id) hj from (select count(iwr.id) qy,iwb.id,iwb.warehouse_name from t_inv_warehouse_base iwb \n" +
                "LEFT JOIN t_inv_warehouse_region iwr on iwb.id=iwr.warehouse\n" +
                "where iwb.org="+oid+" and iwb.category='1' GROUP BY iwb.id ) b\n" +
                "LEFT JOIN (select iws.id,iwr.warehouse from t_inv_warehouse_shelf iws LEFT JOIN t_inv_warehouse_region iwr on iwr.id= iws.region) s on s.warehouse=b.id\n" +
                "GROUP BY b.id) b\n" +
                "LEFT JOIN (select iwl.id,iwr.warehouse from t_inv_warehouse_location iwl LEFT JOIN t_inv_warehouse_shelf iws on iws.id=iwl.shelf LEFT JOIN t_inv_warehouse_region \n" +
                "iwr on iwr.id=iws.region) l on b.id=l.warehouse\n" +
                "GROUP BY b.id) b\n" +
                "LEFT JOIN (SELECT  iwl.id,iwr.warehouse from t_inv_warehouse_location iwl LEFT JOIN t_pd_location l on iwl.id=l.location LEFT JOIN t_inv_warehouse_shelf iws on iws.id=iwl.shelf LEFT JOIN t_inv_warehouse_region iwr on iwr.id=iws.region where l.id is null ) n\n" +
                "on b.id=n.warehouse\n" +
                "GROUP BY b.id";
        List<Map<String,Object>> list= pdLocationDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

        String sql2="select DISTINCT l.product,iwr.warehouse from t_pd_location l\n" +
                "LEFT JOIN t_inv_warehouse_location iwl on l.location=iwl.id\n" +
                "LEFT JOIN t_inv_warehouse_shelf iws on iws.id=iwl.shelf\n" +
                "LEFT JOIN t_inv_warehouse_region iwr on iwr.id=iws.region\n" +
                "LEFT JOIN t_inv_warehouse_base iwb on iwb.id=iwr.warehouse\n" +
                "where iwr.org="+oid +" and iwb.category='1'";
        List<Map<String,Object>> list2= pdLocationDao.getSession().createSQLQuery(sql2).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

        for (Map<String,Object> m:list){
            int count=0;
            for (Map<String,Object> m2:list2) {

                if(m.get("id").equals(m2.get("warehouse"))){
                    count++;
                }
            }
            m.put("zl",count);
        }

        map.put("ckList",list);

        return map;
    }


}
