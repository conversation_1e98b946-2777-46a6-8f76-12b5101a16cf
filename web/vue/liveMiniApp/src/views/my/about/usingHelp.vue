<template>
  <div class="usingHelp">
    <Pageheader title="使用帮助" :showBack="true"/>
    <div class="live-contain">
      <div class="live-cells">
        <div class="live-cell">
          <el-input
              v-model="searchInput"
              placeholder="文件名称/文件编号"
          >
            <template #append>
              <el-icon @click="search()"><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="live-cell" v-for="(item, index) in childFolder" :key="item.id" @click="jumpNext(item)">
          <el-icon><Collection /></el-icon>
          <span class="folderName">{{item.name}}</span>
          <el-icon class="arrow-right"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAboutNextModuleList, getAboutSearchList } from "@/api/api.js";
import { reactive, ref, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";

let childFolder = ref([])
let searchInput = ref('')
const router = useRouter();
const route = useRoute();
console.log('params', route.params)


onMounted(() => {
  getNextModuleList()
})

watch(route, (val, from) => {
  getNextModuleList()
})

const getNextModuleList = () => {
  let categoryId = route.params.id
  getAboutNextModuleList(categoryId).then(res => {
    let child = res.data.childFolder
    childFolder.value = child
  })
}

const jumpNext = (item) => {
  let children = item.children
  if (children > 0) {
    router.push({ path: '/usingHelp/' + item.id });
  } else {
    router.push({ path: '/aboutFileList/' + item.id });
  }
}

const search = () => {
  let searchVal = searchInput.value
  if (searchVal) {
    router.push({ path: '/aboutSearchList/' + searchVal});
  } else {
    ElMessage('请输入搜索内容！')
  }
}
</script>

<style lang="scss" scoped>
.houseSty8{
  position: relative;
  top:5px
}
.folderName{
  margin-left: 8px;
}
</style>



