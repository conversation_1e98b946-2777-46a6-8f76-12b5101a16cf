package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_po_contract_base_history")
public class PoContractBaseHistory {
    private Integer id;
    private Integer supplier;
    private Integer contract;
    private Integer primaryCont;
    private String sn;
    private Date signTime;
    private Date validStart;
    private Date validEnd;
    private Integer isExpired;
    private String fileName;
    private String filePath;
    private Integer level;
    private String tracePath;
    private Integer materialCount;
    private String memo;
    private String keywords;
    private Integer enabled;
    private Date enabledTime;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;
    private Boolean isSuspend;                //暂停履约
    private Date suspendTime;               //收到时间

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "supplier", nullable = true)
    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    @Basic
    @Column(name = "primary_cont", nullable = true)
    public Integer getPrimaryCont() {
        return primaryCont;
    }

    public void setPrimaryCont(Integer primaryCont) {
        this.primaryCont = primaryCont;
    }

    @Basic
    @Column(name = "sn", nullable = true, length = 100)
    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    @Basic
    @Column(name = "sign_time", nullable = true)
    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    @Basic
    @Column(name = "valid_start", nullable = true)
    public Date getValidStart() {
        return validStart;
    }

    public void setValidStart(Date validStart) {
        this.validStart = validStart;
    }

    @Basic
    @Column(name = "valid_end", nullable = true)
    public Date getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(Date validEnd) {
        this.validEnd = validEnd;
    }

    @Basic
    @Column(name = "is_expired", nullable = true)
    public Integer getIsExpired() {
        return isExpired;
    }

    public void setIsExpired(Integer isExpired) {
        this.isExpired = isExpired;
    }

    @Basic
    @Column(name = "file_name", nullable = true, length = 50)
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Basic
    @Column(name = "file_path", nullable = true, length = 255)
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Basic
    @Column(name = "level", nullable = true)
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    @Basic
    @Column(name = "trace_path", nullable = true, length = -1)
    public String getTracePath() {
        return tracePath;
    }

    public void setTracePath(String tracePath) {
        this.tracePath = tracePath;
    }

    @Basic
    @Column(name = "material_count", nullable = true)
    public Integer getMaterialCount() {
        return materialCount;
    }

    public void setMaterialCount(Integer materialCount) {
        this.materialCount = materialCount;
    }

    @Basic
    @Column(name = "memo", nullable = true, length = 255)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "keywords", nullable = true, length = 100)
    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    @Basic
    @Column(name = "enabled", nullable = true)
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time", nullable = true)
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "creator", nullable = true)
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name", nullable = true, length = 100)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator", nullable = true)
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name", nullable = true, length = 100)
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date", nullable = true)
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation", nullable = true, length = 1)
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id", nullable = true)
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no", nullable = true)
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Basic
    @Column(name = "contract", nullable = true)
    public Integer getContract() {
        return contract;
    }

    public void setContract(Integer contract) {
        this.contract = contract;
    }

    @Basic
    @Column(name = "is_suspend", nullable = true)
    public Boolean getSuspend() { return isSuspend;}

    public void setSuspend(Boolean suspend) { isSuspend = suspend; }

    @Basic
    @Column(name = "suspend_time", nullable = true)
    public Date getSuspendTime() { return suspendTime; }

    public void setSuspendTime(Date suspendTime) { this.suspendTime = suspendTime; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PoContractBaseHistory that = (PoContractBaseHistory) o;
        return id == that.id && Objects.equals(supplier, that.supplier) && Objects.equals(sn, that.sn) && Objects.equals(signTime, that.signTime) && Objects.equals(validStart, that.validStart) && Objects.equals(validEnd, that.validEnd) && Objects.equals(isExpired, that.isExpired) && Objects.equals(fileName, that.fileName) && Objects.equals(filePath, that.filePath) && Objects.equals(level, that.level) && Objects.equals(tracePath, that.tracePath) && Objects.equals(materialCount, that.materialCount) && Objects.equals(memo, that.memo) && Objects.equals(keywords, that.keywords) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, supplier, sn, signTime, validStart, validEnd, isExpired, fileName, filePath, level, tracePath, materialCount, memo, keywords, enabled, enabledTime, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
