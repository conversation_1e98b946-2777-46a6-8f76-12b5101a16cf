package cn.sphd.miners.modules.commodity.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.math.BigDecimal;
import java.util.Map;

public class ConstituteUtil {

    /**
     * 根据单一条件获取节点位置
     * @param body            查询目标的主体内容
     * @param key            筛选匹配条件条件对应--key
     * @param value            筛选匹配条件对应--value
     * @return
     */
    public static JSONObject getNode(JSONArray body, String key, Object value) {
        JSONObject result=new JSONObject();
        if(body==null){
            return null;
        }
        for (int i = 0; i < body.size(); i++) {
            JSONObject jsonObject =body.getJSONObject(i);
            if (jsonObject.get(key).toString().equals(value.toString())) {
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    result.put(entry.getKey(), entry.getValue());
                }
                break;
            }else if(jsonObject.getJSONArray("children")!=null) {
                getNode(jsonObject.getJSONArray("children"), key, value,result);
            }
        }
        return result;
    }
    private static JSONObject getNode(JSONArray body, String key, Object value, JSONObject result) {
        for (int i = 0; i < body.size(); i++) {
            JSONObject jsonObject =body.getJSONObject(i);
            if (jsonObject.get(key).toString().equals(value.toString())) {
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    result.put(entry.getKey(), entry.getValue());
                }
                break;
            }else if(jsonObject.getJSONArray("children")!=null) {
                getNode(jsonObject.getJSONArray("children"), key, value,result);
            }
        }
        return result;
    }

    /**
     *
     * @param body            需要添加的目标树主体
     * @param key            筛选匹配条件对应的key
     * @param value            筛选匹配条件的值
     * @param index            需要插入的下标位置索引
     * @param node            插入的整体节点
     */
    public static void addNode(JSONArray body,String key,Object value,int index,JSONObject node,Integer level) {
        if(body.size()==0){
            node.put("level",level);
            body.add(index, node);
            return;
        }

        for (int i = 0; i < body.size(); i++) {
            if("id".equals(key)&&"0".equals(value.toString())) {
                node.put("level",level);
                body.add(index, node);
                break;
            }
            JSONObject jsonObject =body.getJSONObject(i);
            if (jsonObject.get(key).toString().equals(value.toString())) {

                JSONArray children= jsonObject.getJSONArray("children");
                if(children==null){
                    jsonObject.put("children",new JSONArray());
                }
                node.put("level",level);
                //删除原来的
                for (int k = 0; k < jsonObject.getJSONArray("children").size(); k++) {
                    JSONObject jo =jsonObject.getJSONArray("children").getJSONObject(k);
                    if(jo.getInteger("id").toString().equals(node.getInteger("id").toString())){
                        jsonObject.getJSONArray("children").remove(k);
                        node.put("children",jo.getJSONArray("children"));
                    }
                }

                jsonObject.getJSONArray("children").add(index, node);
                break;
            }else if(jsonObject.getJSONArray("children")!=null) {

                addNode(jsonObject.getJSONArray("children"), key, value,index,node,level++);
            }
        }
    }

    /**
     * 根据单一条件删除节点
     * @param body        需要删除的目标主体
     * @param key        筛选匹配条件对应的key
     * @param value        筛选匹配条件对应的value
     */
    public static void delNode(JSONArray body,String key,Object value) {
        for (int i = 0; i < body.size(); i++) {
            JSONObject jsonObject =body.getJSONObject(i);
            if (jsonObject.get(key).toString().equals(value.toString())) {
                body.remove(i);
                break;
            }else if(jsonObject.getJSONArray("children")!=null) {
                delNode(jsonObject.getJSONArray("children"), key, value);
            }
        }
    }

    /**
     * 根据单一条件修改节点
     * @param body        需要修改的目标主体
     * @param key        筛选匹配条件对应的key
     * @param value        筛选匹配条件对应的value
     * @param result    修改节点信息
     * @param isKeep    是否保留孩子节点
     */
    public static void updateNode(JSONArray body,String key,Object value,JSONObject result,boolean isKeep) {

//        JSONObject obj=getNode(body,"id",value);
//        if(obj!=null){
//            if(isKeep){
//                result
//            }
//
//        }
        for (int i = 0; i < body.size(); i++) {
            JSONObject jsonObject =body.getJSONObject(i);

            if (jsonObject.get(key).toString().equals(value.toString())) {
                if(isKeep){
                    result.put("children", jsonObject.getJSONArray("children"));
                }

                body.remove(i);
                body.set(0,jsonObject);
                break;
            }
        }
    }


    /**
     * 根据json生成path
     * @param body        需要生成的目标主体
     */
    public static String createPath(JSONArray body) {
        StringBuilder sb=new StringBuilder("");
        for (int i = 0; i < body.size(); i++) {
            JSONObject jsonObject =body.getJSONObject(i);
            String id=jsonObject.get("id").toString();
            String level=jsonObject.get("level").toString();
            if("".equals(sb.toString())){
                sb.append(level+":"+id);
            }else{
                sb.append(","+level+":"+id);
            }

            JSONArray array=jsonObject.getJSONArray("children");
            if(array!=null&&array.size()!=0){
                createPath(array,sb);
            }
        }


        return sb.toString();
    }

    /**
     * 根据json生成path
     * @param body        需要生成的目标主体
     */
    private static void createPath(JSONArray body,StringBuilder path) {
        for (int i = 0; i < body.size(); i++) {
            JSONObject jsonObject =body.getJSONObject(i);
            String id=jsonObject.get("id").toString();
            String level=jsonObject.get("level").toString();
            path.append(","+level+":"+id);
            JSONArray array=jsonObject.getJSONArray("children");
            if(array!=null&&array.size()!=0){
                createPath(array,path);
            }
        }
    }

    /**
     * 获取某一层级的id
     * @param body        需要生成的目标主体
     */
    public static String getIds(JSONArray body,int level) {
        StringBuilder sb=new StringBuilder();
        if(body==null){
            return null;
        }
        for (int i = 0; i < body.size(); i++) {

            JSONObject jsonObject =body.getJSONObject(i);
            Integer l=jsonObject.getInteger("level");
            if(l==level){
                sb.append(jsonObject.getString("id")+",");
            }else{
                JSONArray array=jsonObject.getJSONArray("children");
                if(array!=null&&array.size()!=0){
                    getIds(array,level,sb);
                }
            }
        }
        if(sb.length()==0){
            return "";
        }

        return sb.substring(0,sb.length() -1);
    }



    /**
     * 获取某一层级的id
     * @param body        需要生成的目标主体
     */
    private static void getIds(JSONArray body,int level,StringBuilder ids) {
        for (int i = 0; i < body.size(); i++) {

            JSONObject jsonObject =body.getJSONObject(i);
            Integer l=jsonObject.getInteger("level");
            if(l==level){
                ids.append(jsonObject.getString("id")+",");
            }else{
                JSONArray array=jsonObject.getJSONArray("children");
                if(array!=null&&array.size()!=0){
                    getIds(array,level,ids);
                }
            }
        }
    }

    /**
     * 获取全部id
     * @param body        需要生成的目标主体
     */
    public static String getIds(JSONArray body) {
        StringBuilder sb=new StringBuilder();
        for (int i = 0; i < body.size(); i++) {

            JSONObject jsonObject =body.getJSONObject(i);
            sb.append(jsonObject.getString("id")+",");
            JSONArray array=jsonObject.getJSONArray("children");
            if(array!=null&&array.size()!=0){
                getIds(array,sb);
            }
        }

        if(sb.length()>0){
            return sb.substring(0,sb.length()-1);
        }
        return sb.toString();
    }

    /**
     * 获取全部id
     * @param body        需要生成的目标主体
     */
    private static String getIds(JSONArray body,StringBuilder sb) {
        for (int i = 0; i < body.size(); i++) {

            JSONObject jsonObject =body.getJSONObject(i);
            sb.append(jsonObject.getString("id")+",");
            JSONArray array=jsonObject.getJSONArray("children");
            if(array!=null&&array.size()!=0){
                getIds(array,sb);
            }
        }

        return sb.toString();
    }


    /**
     * 获取金额
     * @param body        需要生成的目标主体
     */
    public static BigDecimal getAmount(JSONArray body) {
        BigDecimal sum=new BigDecimal(0);
        for (int i = 0; i < body.size(); i++) {

            JSONObject jsonObject =body.getJSONObject(i);
            Integer l=jsonObject.getInteger("level");
                BigDecimal amount=jsonObject.getBigDecimal("amount");
                BigDecimal num=jsonObject.getBigDecimal("num");
                sum=sum.add(amount.multiply(num));
        }

        return sum;
    }


    /**
     * 获取金额
     * @param JSONArray        需要生成的目标主体
     */
    public static JSONArray getAllObj(JSONArray array) {

        JSONArray a=new JSONArray();

        for (int i = 0; i <array.size() ; i++) {
            a.add(array.getJSONObject(i));
            if(array.getJSONObject(i).getJSONArray("children")!=null&&array.getJSONObject(i).getJSONArray("children").size()!=0){
                getAllObj(array.getJSONObject(i).getJSONArray("children"),a);
            }
        }
        return a;
    }

    private static void getAllObj(JSONArray array,JSONArray a) {
        for (int i = 0; i <array.size() ; i++) {
            a.add(array.getJSONObject(i));
            if(array.getJSONObject(i).getJSONArray("children")!=null&&array.getJSONObject(i).getJSONArray("children").size()!=0){
                getAllObj(array.getJSONObject(i).getJSONArray("children"),a);
            }
        }
    }
}
