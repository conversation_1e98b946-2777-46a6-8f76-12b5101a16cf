package cn.sphd.miners.modules.system.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Created by lixu on 2023/7/21
 * 李旭
 * 初始化   1.259初始化
 */

@Controller
@RequestMapping("/initialize")
public class InitializeController {

    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    OrgInitService orgInitService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    PopedomService popedomService;
    @Autowired
    UserService userService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    SmsService smsService;

    // 初始化 权限主页面
    @RequestMapping("/toAuthorityMain.do")
    public String toAuthorityMain(){
        return "/orgInit/authority";
    }

    //  初始化  采购主页面
    @RequestMapping("/toPurchaseMain.do")
    public String toPurchaseMain(){
        return "/orgInit/purchase";
    }

    //  初始化  原辅材料库
    @RequestMapping("/toRamtwhMain.do")
    public String toRamtwhMain(){
        return "/orgInit/RAMtWh";
    }


    /**
     * <AUTHOR>
     * @Date 2023/8/21
     * 普通机构-必须分配的权限列表
     */
    @ResponseBody
    @RequestMapping("/mustAllocationList.do")
    public JsonResult mustAllocationList(User user){
        List<String> midList=orgPopedomService.getOrgMidListByOid(user.getOid());
        List<InitTmpl> initTmplList=orgInitService.getInitTmplsByOrgMids(midList);
        for (InitTmpl initTmpl:initTmplList){
            UserPopedom userPopedom=userPopedomService.getUserPopedomByMid(initTmpl.getMid(),user.getOid());
            if (userPopedom!=null) {
                initTmpl.setUserId(userPopedom.getUserId());
                initTmpl.setUserName(userPopedom.getUser().getUserName());
                initTmpl.setMobile(userPopedom.getUser().getMobile());

                InitAllot initAllot=orgInitService.getLastInitAllotByCode(initTmpl.getCode(),user.getOid());
                if (initAllot!=null){
                    initTmpl.setState(initAllot.getState());
                }
            }
        }
        return new JsonResult(1,initTmplList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/8/21
     * 普通机构-必须分配的权限 编辑可选人员列表 用原来的接口 getUserList.do
     */



    /**
     * <AUTHOR>
     * @Date 2023/8/21
     * 普通机构-必须分配的权限 采购、库存 初始化人员 编辑生效
     */
    @ResponseBody
    @RequestMapping("/saveMustAllocation.do")
    public JsonResult saveMustAllocation(User user, String code, Integer passiveUserId, HttpServletRequest request, AuthInfoDto authInfo){
        Integer state=0;
        if (StringUtils.isNotEmpty(code)&&passiveUserId!=null) {
            InitTmpl initTmpl=orgInitService.getInitTmplByCode(code);
            String mid=initTmpl.getMid();
            UserPopedom userPopedom = userPopedomService.getUserPopedomByMid(mid,user.getOid());
            if (userPopedom != null) {
                userPopedomService.deleteUserPopedom(userPopedom);
            }
            userPopedomService.saveAllUserPopedom(mid, passiveUserId);

            UserInit userInit=orgInitService.getUserInitByMidOid(user.getOid(),mid);
            if (userInit!=null){
                orgInitService.deleteUserInit(userInit);
            }
            User passiveUser=userService.getUserByID(passiveUserId);
            orgInitService.saveUserInits(user,passiveUser,mid);

            userService.clearPopedomsCacheByUserId(passiveUserId); // 清理选中人员的权限缓存


//            String cont="我公司将使用Wonderss企业管理系统，"+initTmpl.getName()+"需由您进行。请使用您"+passiveUser.getMobile()+"手机号登录电脑端网址"+System.getProperty("BaseUrl")+"，之后按页面提示操作。\n" +
//                    "有疑问可拨打我手机"+user.getMobile()+"。";
//            sendMessageService.sendMessage(passiveUser.getUserID(),passiveUser.getOid(),passiveUser.getMobile(),cont);//发短信

            // 短信重构 2024/5/22 李旭更改
            String phoneNo=passiveUser.getMobile();
            String url = GetLocalIPUtils.getRootPath(request);
            Map<String, String> params = new HashMap<String, String>(4) {{
                put("phoneNo", phoneNo);
                put("url", url);
                put("initName", initTmpl.getName());
                put("mobile", user.getMobile());
            }};
            smsService.sendMessage(SmsService.SendType.general, phoneNo, authInfo, TemplateService.SmsContentTemplate.initItem, params, null);

            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2023/8/25
     * 普通机构-采购初始化步骤列表接口
     */
    @ResponseBody
    @RequestMapping("/getPurchaseInitList.do")
    public JsonResult getPurchaseInitList(User user){
        List<InitAllot> initAllotList=orgInitService.getInitAllotListByCode(user,"purchase",user.getOid());
        InitAllot overAllot=new InitAllot();
        for (InitAllot initAllot:initAllotList){
            if (initAllot.getPhase()==2){
                overAllot=initAllot;
            }
        }
        initAllotList.remove(overAllot);
        Date overUpdateDate=initAllotList.get(initAllotList.size()-1).getUpdateDate();
        String memo="已编辑完，但需重新编辑，因为";
        Boolean isEdit=false;
        if (overUpdateDate!=null){
            for (InitAllot initAllot:initAllotList){
                if (initAllot.getUpdateDate()!=null&&initAllot.getStage()!=2) {
                    if (initAllot.getUpdateDate().getTime() > overUpdateDate.getTime()) {
                        memo += initAllot.getEditName() + "+";
                        isEdit=true;
                    } else if (initAllot.getUpdateDate().getTime()== overUpdateDate.getTime()) {
                        if (initAllot.getState()!=0&&isEdit) {
                            initAllot.setState(2);
                            memo=StringUtils.substringBeforeLast(memo,"+"); //去除最后一个+
                            initAllot.setCont(memo+"的数据都有变化");
                        }
                    }
                }
            }
        }
        Map<String,Object> map=new HashMap<>();
        map.put("overAllot",overAllot);
        map.put("initAllotList",initAllotList);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2023/8/25
     * 普通机构-材料供应商全部录完的确定完成步骤接口   /普通机构-设备供应商全部录完的确定完成步骤接口 / 普通机构-其他原辅材料全部录完的确定完成步骤接口 /普通机构-原辅材料定点信息全部录完的确定完成步骤接口
     * 普通机构-原辅材料的初始库存已全部编辑完成，初始化结束！（采购类菜单权限生效，登陆可见） 2023/8/28
     * 普通机构-装备器具步骤确定完成  1.307
     * 普通机构-财务 会计 步骤 确定完成  1.307
     * 普通机构-销售初始化 个步骤 确定完成  1.343销售初始化  2025/7/15
     */
    @ResponseBody
    @RequestMapping("/completeAllot.do")
    public JsonResult completeAllot(User user,Integer id,Integer type){
        Integer state=0;
        if (id!=null) {
//            orgInitService.completeAllot(user,id);
            InitAllot initAllot = orgInitService.getInitAllotById(id);
            initAllot.setState(1);
            initAllot.setUpdateDate(new Date());
            initAllot.setUpdateName(user.getUserName());
            initAllot.setUpdator(user.getUserID());

            List<String> midList=orgInitService.getInitStepMIdsById(initAllot.getPreviousId());
            Set<String> mids=new HashSet<>();
            if (midList.size()>0){
                userPopedomService.deleteUserPopedomByOidMid(user.getOid(),midList);
                List<UserInit> userInitList=orgInitService.getUserInitListByMids(user.getOid(),midList);
                for (UserInit userInit:userInitList){
                    Popedom popedom = popedomService.getPopedomByMid(userInit.getMid());
                    if (popedom.getUrl() != null && !"".equals(popedom.getUrl())) {
                        mids.add(userInit.getMid());
                    }
                }
            }
            orgInitService.saveUserMids(user.getOid(),mids);

            InitStep initStep=orgInitService.getInitStepById(initAllot.getPreviousId());

            if (initStep!=null&&initStep.getInitTmpl().getCode().equals("material")&&initStep.getStage()==2&&initStep.getPhase()==2) {
                userPopedomService.deleteUserPopedomByOidMid(user.getOid(),new ArrayList<String>((Arrays.asList("ve","vf"))));
            }

            if (initStep!=null&&initStep.getInitTmpl().getCode().equals("equipmentTools")) {  //装备器具
                userPopedomService.deleteUserPopedomByOidMid(user.getOid(),new ArrayList<String>((Arrays.asList("vj"))));
            }



            List<User> userList=userService.getUserListByOrgId(user.getOid());
            for (User u:userList){

                if (initStep!=null&&initStep.getInitTmplId()==4&&initStep.getStage()==0){
                    if ("finance".equals(u.getRoleCode())){
                        userPopedomService.deleteUserPopedomByUserId(u.getUserID());
                        userPopedomService.saveUserPopedomByCode("finance",u);  //1.307初始化财务 ，需求说财务初始化完成后，财务高管才有正常菜单。  lixu 2024/10/23
                    }
                    if ("accounting".equals(u.getRoleCode())){
                        userPopedomService.deleteUserPopedomByUserId(u.getUserID());
                        userPopedomService.saveUserPopedomByCode("accounting",u);  //1.307初始化 会计，需求说财务初始化完成后，会计高管才有正常菜单。  lixu 2024/12/24
                    }
//                popedomTaskService.updateUserBadgeNumber(u);//重新计算角标
                }

                if (initStep!=null&&initStep.getInitTmpl().getCode().equals("saleInit1")&&initStep.getStage()==0){
                    if ("sale".equals(u.getRoleCode())){
                        userPopedomService.deleteUserPopedomByUserId(u.getUserID());
                        userPopedomService.saveUserPopedomByCode("sale",u);  //1.343初始化 销售1，需求说 初始化完成后，销售高管才有正常菜单。  lixu 2025/7/15
                    }
                }
                userService.clearPopedomsCacheByUserId(u.getUserID()); // 清理机构下每个人员的权限缓存
            }

            orgInitService.updateInitAllot(initAllot);
            state=1;
        }
        return new JsonResult(1,state);
    }

    /**
     * <AUTHOR>
     * @Date 2023/8/27
     *普通机构-原辅材料库初始化步骤列表接口
     */
    @ResponseBody
    @RequestMapping("/getMaterialInitList.do")
    public JsonResult getMaterialInitList(User user){
        List<InitAllot> initAllotList=orgInitService.getInitAllotListByCode(user,"material",user.getOid());
        return new JsonResult(1,initAllotList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/10/31
     *普通机构- 权限管理- 初始化 菜单   按需求后加
     */
    @RequestMapping("/toInitAuthority.do")
    public String toInitAuthority(){
        return "/authority/initAuthority";
    }


    /**
     * <AUTHOR>
     * @Date 2024/4/22
     *普通机构- 非特殊要求的 通用初始化步骤列表接口
     * code  初始化项 的code值      原辅材料库的初始化-material ,货架与库位的初始化-shelves   装备器具的初始化-equipmentTools
     *    finance	财务的初始化
     * accounting	会计的初始化
     * saleInit1	销售1的初始化
     */
    @ResponseBody
    @RequestMapping("/getInitListByCode.do")
    public JsonResult getInitListByCode(User user,String code){
        List<InitAllot> initAllotList=orgInitService.getInitAllotListByCode(user,code,user.getOid());
        return new JsonResult(1,initAllotList);
    }

    /**
     * <AUTHOR>
     * @Date 2024/6/5
     *普通机构- 非特殊要求的 通用  清理初始化步骤接口
     */
    @ResponseBody
    @RequestMapping("/unInitListByCode.do")
    public JsonResult unInitListByCode(User user,String code){
        if (StringUtils.isNotEmpty(code)){
            orgInitService.unInitListByCode(user,code);
            return new JsonResult(1, "清理成功");
        } else {
            return new JsonResult(new MyException("-1", "code值错误或不存在"));
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024/2/14
     *1.307 初始化    会计 、财务、  装备器具
     * 按需求 最新要求  机构有会计 初始化时  ，   会计未初始化完成前  财务 、装备器具 初始化均不能操作，
     * 此接口用于上述判断  会计初始化 完成 /  没有会计 初始化这事。 财务、装备器具也可以直接操作。
     */
    @ResponseBody
    @RequestMapping("/judgeAccountingEnd.do")
    public JsonResult judgeAccountingEnd(User user){
        Map<String,Object> map=new HashMap<>();
        map.put("operate",true);   //是否可以操作

        if (orgPopedomService.getOrgPopedomByMid(user.getOid(),"vi")){  // 机构拥有会计初始化权限
            if ("4".equals(user.getOrganization().getAccountState())&&"N".equals(user.getOrganization().getRebuildLabel())){
                return new JsonResult(1, map);
            }else {
                map.put("operate",false);   //是否可以操作
                map.put("message","上步初始化尚未完成，故本模块初始化暂无法开始！");  // 提示内容
                map.put("previousInit","初始化-会计");  // 上一步初始项
                User accountingUser=userService.getUserByRoleCode(user.getOid(),"accounting");
                map.put("userName",accountingUser!=null?accountingUser.getUserName():"");  // 负责人名
                map.put("mobile",accountingUser!=null?accountingUser.getMobile():"");  // 负责人手机号
                return new JsonResult(1, map);
            }
        }
        return new JsonResult(1, map);
    }



}
