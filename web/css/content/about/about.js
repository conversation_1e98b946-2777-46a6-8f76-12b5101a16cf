// 本地数据存储
var t_ststem_user = { role:"", username:"" };
var chargeCase = { chargeYes: 1 , chargeNo:2, chargeNot:0 }; 

/*
$(function(){
	// 获得弄登陆人员身份
	t_ststem_user.role = $.cookie("role");
	t_ststem_user.username = $.cookie("username");
});
*/



function getNull(str) {
	if(str==null || str == undefined || str=="" ) 
		return "";
	 else 
		 return str;
	 
}

var isIn_add = { is_add:false , addId:null }; // 标记是否正在进行上传
//删除已经放弃上传的记录
function deleteCancel(){
	if(isIn_add.is_add){
		$.ajax({
			url:"site_deleteIncomplete.action",
			data:{ "id": isIn_add.addId },
			type:"post",
			dataType:"json",
			success:function(data){
				console.log(data);
				var status = data["status"];
				if(status == 1){
					isIn_add = { is_add:false , addId:null };
				} 
			},
			error:function(){
				alert("新建错误，请稍后重试");
			}
		});
	}
	
}



















