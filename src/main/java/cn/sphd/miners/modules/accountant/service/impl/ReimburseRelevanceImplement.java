package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.FinanceRelevanceService;
import cn.sphd.miners.modules.accountant.service.ReimburseRelevanceService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.accountant.zEnum.CommonEnum;
import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillItemDao;
import cn.sphd.miners.modules.finance.entity.AccountDetail;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBill;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillItem;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.system.entity.Code;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.CodeService;
import cn.sphd.miners.modules.system.service.UserService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019-04-25 11:05
 * @description
 **/
@Service
@Transactional(propagation= Propagation.REQUIRED)
public class ReimburseRelevanceImplement implements ReimburseRelevanceService{

    @Autowired
    ReimburseRelevanceMapper reimburseRelevanceMapper;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    ReimburseRelevanceHistoryMapper reimburseRelevanceHistoryMapper;
    @Autowired
    CodeService codeService;
//    @Autowired
//    FinanceReimburseBillService billService;
    @Autowired
    FinanceReimburseBillItemDao billItemDao;
    @Autowired
    UserService userService;
    @Autowired
    SubjectSettingService settingService;
    @Autowired
    DataService dataService;
    @Autowired
    FinanceRelevanceService financeRelevanceService;
    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    DetailMapper detailMapper;
    @Autowired
    TAccountantSubjectCashMapper subjectCashMapper;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    VoucherUpdate voucherUpdate;
    @Autowired
    SettleMapper settleMapper;

    /*
    * 获取关联页面的数据
    * 由两部分数据组成，一是财务提供的所有一级费用类别，一是会计的关联表中的所有数据
    * 还要分别获取5601和5602的二级科目
    * */
    @Override
    public Map<String,Object> getRelevances(Integer org) {

        List<Code> codeList= codeService.getFirstCostCategoryList(org);//获得财务一级费用类别
        List<TAccountantSubjectRelevance> relevances = reimburseRelevanceMapper.getRelevances(org);//获得关联表数据
        Map<String,Object> result = new HashMap<String,Object>();
        result.put("categories",codeList);
        result.put("selectedSubjects",relevances);
        TAccountantSubjectEntity sub = new TAccountantSubjectEntity();
        sub.setOrg(org);
        sub.setSubject("5601");
        List<TAccountantSubjectEntity> listChildren5601 = tAccountSubjectMapper.childSubject(sub);
        result.put("5601",listChildren5601);
        sub.setSubject("5602");
        List<TAccountantSubjectEntity> listChildren5602 = tAccountSubjectMapper.childSubject(sub);
        result.put("5602",listChildren5602);
        return result;
    }

    /*
    * 保存关联设置
    * 现将上传的数据写到历史表，以便查询关联历史
    * 然后将数据写到关联表，以便在页面显示所有已关联的科目
    * */
    @Override
    public int setReimburseRelevance(User user,List<TAccountantSubjectRelevance> list) {
        Integer org = user.getOid();
        for (TAccountantSubjectRelevance subjectRelevance : list) {
            String username = user.getUserName();//修改人
            Integer userid = user.getUserID();//修改人id
            subjectRelevance.setOrg(org);
            //先写到历史表
            Integer enable = subjectRelevance.getEnabled();
            TAccountantSubjectRelevanceHistory history = new TAccountantSubjectRelevanceHistory();
            history.setUpdateName(username);
            history.setUpdator(userid);
            history.setUpdateDate(new Date());
            history.setCode(subjectRelevance.getCode());
            history.setEnabled(enable);
            history.setFeeCat(subjectRelevance.getFeeCat());
            history.setName(subjectRelevance.getName());
            history.setOrg(org);
            history.setSubject(subjectRelevance.getSubject());
            history.setSubjectId(subjectRelevance.getSubjectId());
            reimburseRelevanceHistoryMapper.insert(history);
            //写到原表（关联表）如果enable=1 表示要新增或修改，原表不存disable的数据,如果是disable将原表数据删除
            if (enable.compareTo(1) == 0) {
                //先判断类别id和高级科目是否已经存在，存在的话要update，否则insert
                TAccountantSubjectRelevance param = new TAccountantSubjectRelevance();
                param.setFeeCat(subjectRelevance.getFeeCat());
                param.setCode(subjectRelevance.getCode());
                param.setOrg(org);
                param = reimburseRelevanceMapper.getByFeeCat(param);
                if (param == null) {
                    reimburseRelevanceMapper.insert(subjectRelevance);
                }
                else {
                    reimburseRelevanceMapper.updateByFeeCat(subjectRelevance);
                }
            }
            else {
                //如果是disable需要把原表之前的数据删除，disable只在历史表存在
                reimburseRelevanceMapper.deleteDisable(subjectRelevance);
            }
        }
        return 1;
    }

    /*
    * 返回某类别设置的关联历史列表
    * */
    @Override
    public List<TAccountantSubjectRelevanceHistory> getCategoryRelevanceHistory(TAccountantSubjectRelevanceHistory param) {
        List<TAccountantSubjectRelevanceHistory> list = reimburseRelevanceHistoryMapper.getCategoryRelevanceHistory(param);
        return list;
    }

    /*
    * 当报销审批通过和认证处理后(需要当该报销中所有需要认证的票据都认证处理后才调)需要掉此方法来给报销自动匹配科目
    * 1、得到每个票据明细的费用类别
    * 2、判断类别是否关联了科目
    * 3、根据销售事务得到高级科目
    * 4、判断关联的科目和高级科目是否一致
    * 5、一致的话判断该票据明细是否生成了子科目，有的话直接用，否则自动生成
    * 6、如果不一致需要记下来表明该报销不会自动生成凭证
    * 7、如果报销中所有票据明细都自动选择完科目则需要自动生成正常已审批的凭证，否则需要将部分已自动选择科目的科目进行暂存
    * 以便后续会计手动选择科目
    * 8、如果有的票据认证通过需要生成进项税额科目，其发生额 = 所有认证通过的票据的税额合计
    * 9、如果不需要生成凭证只是暂存的话需要前端显示出进项税科目
    * */
    @Override
    public int autoMatchSubject(Integer oid, PersonnelReimburse personnelReimburse,FinanceAccount financeAccount) {
        boolean isEnableAccountant = financeRelevanceService.enableAccountantModule(oid);//判断机构是否开启了会计模块，true是开启，false是没开启
        if (!isEnableAccountant) return 0;
        TAccountantSubjectRelevance relevance = new TAccountantSubjectRelevance();
        relevance.setOrg(oid);
        String saleAffairs = personnelReimburse.getTransactionType();//事务类型:1-销售事务,其它-非销售事务
//        Set<FinanceReimburseBill> listReimburse = personnelReimburse.getFinanceReimburseBillHashSet();
        Integer reimburseId = personnelReimburse.getId();
        List<FinanceReimburseBill> listReimburse = personnelReimburseService.getReimburseBillByReimburseId(reimburseId,null);
        boolean voucherFlag = true;//是否要生成凭证，false-不生成，true-生成
        for (FinanceReimburseBill reimburseBill : listReimburse) {
            for (FinanceReimburseBillItem billItem : reimburseBill.getFinanceReimburseBillItemHashSet()) {
                //得到该票据明细的一级费用类别
                Integer feecat = billItem.getFeeCat();
                Code code = codeService.getFirstCodeByid(feecat);
                Integer firstLevelID = code.getId();
                //判断该类别是否关联了科目
                relevance.setFeeCat(firstLevelID);
                List<TAccountantSubjectRelevance> list = reimburseRelevanceMapper.getListByFeeCat(relevance);
                if (list.isEmpty()) {
                    voucherFlag = false;
                    continue;
                }
                else {
                    String superSubject = null;//高级科目5601或5602
                    String superSubjectName = null;//高级科目名称，销售费用或管理费用

                    if ("1".equals(saleAffairs)) {
                        //如果是销售事务
                        superSubject = "5601";
                        superSubjectName = "销售费用";
                    }
                    else {
                        superSubject = "5602";
                        superSubjectName = "管理费用";
                    }

                    if (list.size() == 1) {
                        //得到高级科目
                        String advancedSubject = list.get(0).getCode();
                        if (!advancedSubject.equals(superSubject)) {
                            //如果类别关联的科目和报销对应的高级科目不一致，也不能自动选择科目
                            voucherFlag = false;
                            continue;
                        }
                        else {
                            //自动选择科目
                            autoMatchSubject(oid,superSubject,superSubjectName,list,billItem);
                        }
                    }
                    else {
                        //如果该一级类别关联了两个高级科目的话需要自动选择科目
                        autoMatchSubject(oid,superSubject,superSubjectName,list,billItem);
                    }
                }
            }

        }

        /*
        * 如果每张票据都关联了科目则需要自动生成已批准的凭证
        * */
        if (voucherFlag && listReimburse.size() >0) {
            int res = autoCreateVoucher(oid,personnelReimburse,financeAccount);
            return res;
        }
        return 1;
    }

    /*
    * 判断某机构是否进行了报销和科目关联
    * 关联返回true ，否则返回false
    * */
    @Override
    public boolean judgeRelevance(Integer org) {
        List<TAccountantSubjectRelevance> relevances = reimburseRelevanceMapper.getRelevances(org);//获得关联表数据
        if (relevances.size() > 0)
            return true;
        else
            return false;
    }

    /*返回部分关联科目的报销数据的贷方科目*/
    @Override
    public Map getRelevanceDebitSubject(Integer oid,PersonnelReimburse personnelReimburse) {
//        Set<FinanceReimburseBill> billSet = personnelReimburse.getFinanceReimburseBillHashSet();
        Integer reimburseId = personnelReimburse.getId();
        List<FinanceReimburseBill> billSet = personnelReimburseService.getReimburseBillByReimburseId(reimburseId,null);

        BigDecimal taxAmountSum = BigDecimal.ZERO;//税额合计

        String subjectBorrow = null;
        String subjectLoan = null;
        JSONArray borrowArray = new JSONArray();
        JSONArray loanArray = new JSONArray();

        for (FinanceReimburseBill frb : billSet) {
            Set<FinanceReimburseBillItem> billItemSet = frb.getFinanceReimburseBillItemHashSet();
            String authFlag = frb.getCertificationState();//认证状态 0-未认证 1-认证通过，2-认证失败，3-无需认证
            for (FinanceReimburseBillItem billItem : billItemSet) {
                BigDecimal taxAmount = billItem.getTaxAmount();//税额
                if ("1".equals(authFlag)) {
                    //只有认证通过时才统计税额来作为进项税额的发生额
                    taxAmountSum = taxAmountSum.add(taxAmount);
                }
            }

        }
        //如果税额合计大于0，说明需要增加进项税额科目
        if (taxAmountSum.compareTo(BigDecimal.ZERO) > 0) {
            JSONObject borrow = new JSONObject();
            borrow.put("subject","222100010001");
            borrow.put("subjectNames","应交税费-应交增值税-进项税额");
            borrow.put("price",taxAmountSum);
            borrow.put("productID",0);
            borrowArray.put(borrow);
        }

        /*
        * 票据金额代表借方金额    实际金额代表贷方金额
        * 1、如果报销的票据金额 = 实际金额    支出方式如果是对公户 贷方科目选1002 下已生成的三级科目， 如果不是对公户贷方科目为1001
        * 2、如果报销的票据金额 > 实际金额    第二个贷方科目是2241下自动生成的子科目，发生额为差额，如果2241下没有自动生成子科目的话需要先生成
        * 3、如果报销的票据金额 < 实际金额    第二个借方科目是2241下自动生成的子科目，发生额为差额，如果2241下没有自动生成子科目的话需要先生成
        * */
        BigDecimal billAmount = personnelReimburse.getBillAmount();//借方金额
        BigDecimal realAmount = personnelReimburse.getAmount();//贷方金额
//        Integer reimburseId = personnelReimburse.getId();
        AccountDetail accountDetail = dataService.getDetailByReimburse(reimburseId);
        FinanceAccount financeAccount = accountDetail.getAccountId();
        String isPublic = financeAccount.getIsPublic();//1-是 0-否
        String account = financeAccount.getAccount();//对公户账号
        String bankName = financeAccount.getBankName();//开户行

        if (billAmount.compareTo(realAmount) > 0) {
            //第二个贷方科目是2241的二级科目，发生额是差额,先得判断2241下是否已经存在由姓名+手机号的子科目，没有的话需要先生成
            //返回2241下由报销申请者姓名+手机号命名的二级科目编号
            TAccountantSubjectEntity subjectChildOf2241 = getChildSubjectOf2241(oid,personnelReimburse);
            JSONObject loan = new JSONObject();
            loan.put("subject",subjectChildOf2241.getSubject());
            loan.put("subjectNames","其他应付款-" + subjectChildOf2241.getName());
            BigDecimal diff = billAmount.subtract(realAmount);
            loan.put("price",diff);
            loanArray.put(loan);
        }
        else if (billAmount.compareTo(realAmount) < 0) {
            //第二个借方科目是2241的二级科目，发生额是差额
            JSONObject borrow = new JSONObject();
            TAccountantSubjectEntity subjectChildOf2241 = getChildSubjectOf2241(oid,personnelReimburse);
            borrow.put("subject",subjectChildOf2241.getSubject());
            borrow.put("subjectNames","其他应付款-" + subjectChildOf2241.getName());
            BigDecimal diff = realAmount.subtract(billAmount);
            borrow.put("price",diff);
            borrowArray.put(borrow);
        }

        TAccountantSubjectEntity subjectParam = new TAccountantSubjectEntity();
        subjectParam.setName(account);
        subjectParam.setOrg(oid);

        if ("1".equals(isPublic)) {
            //如果支出方式是对公户，贷方科目需要有1002的三级子科目,根据对公户的账号来查
            subjectParam = tAccountSubjectMapper.getByName(subjectParam);
            JSONObject loan = new JSONObject();
            if (subjectParam != null) {
                loan.put("subject",subjectParam.getSubject());
                loan.put("subjectNames","银行存款-" + bankName + "-" + account);
                loan.put("price",realAmount);
            }
            else
            {
                //如果特殊情况下没有生成1002的三级科目，那就先用1002替代
                loan.put("subject","1002");
                loan.put("subjectNames","银行存款");
                loan.put("price",realAmount);
            }
            loanArray.put(loan);
        }
        else {
            //如果不是对公户，贷方科目取1001
            JSONObject loan = new JSONObject();
            loan.put("subject","1001");
            loan.put("subjectNames","库存现金");
            loan.put("price",realAmount);
            loanArray.put(loan);
        }

        JSONArray cashArray = new JSONArray();
        JSONObject cashObject = new JSONObject();
        //现金流量
        cashObject.put("cashitem","r9c4");
        cashObject.put("cashes",realAmount);
        cashArray.put(cashObject);
        String cash = cashArray.toString();

        subjectBorrow = borrowArray.toString();
        subjectLoan = loanArray.toString();

        Map result = new HashMap();
        result.put("borrow",subjectBorrow);
        result.put("loan",subjectLoan);
        result.put("cash",cash);
        return result;
    }

    /*
    * 自动生成凭证
    * 每个票据都有可能是经过认证操作的
    * 如果认证通过的话发生额取不含税金额，所有认证通过的票据的税额合计是进项税的发生额
    * 销售事务的作用是判断高级科目，然后生成子科目
    * 实际金额和票据金额的作用是生2241科目，发生额取差额
    * 是否为对公户作用是生成1001或1002及其现金流量金额
    * */
    private int autoCreateVoucher(Integer oid, PersonnelReimburse personnelReimburse,FinanceAccount financeAccount) {

//        Set<FinanceReimburseBill> billSet = personnelReimburse.getFinanceReimburseBillHashSet();
        //        Set<FinanceReimburseBill> listReimburse = personnelReimburse.getFinanceReimburseBillHashSet();
        Integer reimburseId = personnelReimburse.getId();
        List<FinanceReimburseBill> billSet = personnelReimburseService.getReimburseBillByReimburseId(reimburseId,null);

        BigDecimal taxAmountSum = BigDecimal.ZERO;//税额合计
        SubjectChooseParams voucherParams = new SubjectChooseParams();
        voucherParams.setSummary(personnelReimburse.getSummary());
        voucherParams.setOid(oid);
        /*
        * 如果报销中票据的开票日期都是非本月的话，凭证日期选择未结账月份的最后一天
        * 如果开票日期是本月的话，凭证只能是属于本月
        * */
        int belong_peroid = 2;//默认为非本月
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String curDateStr = NewDateUtils.dateToString(new Date(),"yyyy-MM");
//        String curDateStr = sdf.format(new Date());//当前日期
        Date issueDate = billSet.get(0).getIssueDate();//报销中票据的开票日期
        if (issueDate == null)
            issueDate = new Date();
        String issueDateStr = NewDateUtils.dateToString(issueDate,"yyyy-MM");
        if (curDateStr.equals(issueDateStr)) {
            belong_peroid = 1;
        }
        else {
            belong_peroid = 2;
            //查询该票据日期有没有结账，结账的话直接返回，不生成凭证。没结账的话看是否是上月票据，如果不是直接返回，
            //因为只有上月或本月票据才能生成凭证
            TAccountantSettle settle = new TAccountantSettle();
            settle.setOrg(oid);
            settle.setPeriod(issueDateStr);
            //得到上月的结账数据
            settle = settleMapper.getSettleDay(settle);
            if (settle != null && "3".equals(settle.getState())) {
                return 0;
            }
            if (settle == null) {//如果该票据日期未结账且不是上月票据，直接返回
                Date date = NewDateUtils.changeMonth(new Date(),-1);
                String str = NewDateUtils.dateToString(date,"yyyy-MM");
                if (!str.equals(issueDateStr)) {
                    return 0;
                }
            }
        }

        voucherParams.setBillDate(issueDate);
        voucherParams.setBelong_peroid(belong_peroid);
        voucherParams.setIs_account(1);
        voucherParams.setCategory(1);
//        voucherParams.setMemo("系统生成");
        voucherParams.setApprove_status(CommonEnum.VoucherApproveEnum.NORMAL_APPROVE.getIndex());//系统生成的凭证状态是2-正常已通过
        voucherParams.setSource(3);//来源于个人报销
//        voucherParams.setSource(0);//来源于个人报销
        voucherParams.setKind("1");//报销在此为1
        voucherParams.setPricetype(1);//报销在此默认1-支出
        voucherParams.setPurpose(personnelReimburse.getPurpose());
        voucherParams.setBill_quantity(personnelReimburse.getBillQuantity());

        String subjectBorrow = null;
        String subjectLoan = null;
        JSONArray borrowArray = new JSONArray();
        JSONArray loanArray = new JSONArray();
        BigDecimal borrowSum = BigDecimal.ZERO;//借方总金额
        BigDecimal loanSum = BigDecimal.ZERO;//贷方总金额

        for (FinanceReimburseBill frb : billSet) {
            Set<FinanceReimburseBillItem> billItemSet = frb.getFinanceReimburseBillItemHashSet();
            String authFlag = frb.getCertificationState();//认证状态 0-未认证 1-认证通过，2-认证失败，3-无需认证
            BigDecimal subjectAmount = BigDecimal.ZERO;//科目发生额
            for (FinanceReimburseBillItem billItem : billItemSet) {
                BigDecimal price = billItem.getPrice();//不含税金额
                BigDecimal amount = billItem.getAmount();//含税金额
                BigDecimal taxAmount = billItem.getTaxAmount();//税额
                if ("1".equals(authFlag)) {
                    //只有认证通过时才统计税额来作为进项税额的发生额
                    taxAmountSum = taxAmountSum.add(taxAmount);
                    subjectAmount = price;
                }
                else {
                    //如果没有认证通过科目发生额应取含税金额
                    subjectAmount = amount;
                }
                borrowSum = borrowSum.add(subjectAmount);
                JSONObject borrow = new JSONObject();
                borrow.put("subject",billItem.getSubject());
                borrow.put("subjectNames",billItem.getSubjectName());
                borrow.put("price",subjectAmount);
                borrow.put("productID",billItem.getId());
                borrowArray.put(borrow);
            }

        }
        //如果税额合计大于0，说明需要增加进项税额科目
        if (taxAmountSum.compareTo(BigDecimal.ZERO) > 0) {
            JSONObject borrow = new JSONObject();
            borrow.put("subject","222100010001");
            borrow.put("subjectNames","应交税费-应交增值税-进项税额");
            borrow.put("price",taxAmountSum);
            borrow.put("productID",0);
            borrowArray.put(borrow);
            borrowSum = borrowSum.add(taxAmountSum);
        }

        /*
        * 票据金额代表借方金额    实际金额代表贷方金额
        * 1、如果报销的票据金额 = 实际金额    支出方式如果是对公户 贷方科目选1002 下已生成的三级科目， 如果不是对公户贷方科目为1001
        * 2、如果报销的票据金额 > 实际金额    第二个贷方科目是2241下自动生成的子科目，发生额为差额，如果2241下没有自动生成子科目的话需要先生成
        * 3、如果报销的票据金额 < 实际金额    第二个借方科目是2241下自动生成的子科目，发生额为差额，如果2241下没有自动生成子科目的话需要先生成
        * */
        BigDecimal billAmount = personnelReimburse.getBillAmount();//借方金额
        BigDecimal realAmount = personnelReimburse.getAmount();//贷方金额
//        Integer reimburseId = personnelReimburse.getId();
        AccountDetail accountDetail = dataService.getDetailByReimburse(reimburseId);
        Integer detailId = accountDetail.getId();
//        voucherParams.setBill_detail(detailId);
        voucherParams.setDetailId(detailId);
        voucherParams.setOperatorName(accountDetail.getAuditorName());
        voucherParams.setOperatorFinance(personnelReimburse.getCreateName());
        String isPublic = financeAccount.getIsPublic();//1-是 0-否
        String account = financeAccount.getAccount();//对公户账号
        String bankName = financeAccount.getBankName();//开户行

        if (billAmount.compareTo(realAmount) > 0) {
            //第二个贷方科目是2241的二级科目，发生额是差额,先得判断2241下是否已经存在由姓名+手机号的子科目，没有的话需要先生成
            //返回2241下由报销申请者姓名+手机号命名的二级科目编号
            TAccountantSubjectEntity subjectChildOf2241 = getChildSubjectOf2241(oid,personnelReimburse);
            JSONObject loan = new JSONObject();
            loan.put("subject",subjectChildOf2241.getSubject());
            loan.put("subjectNames","其他应付款-" + subjectChildOf2241.getName());
            BigDecimal diff = billAmount.subtract(realAmount);
            loan.put("price",diff);
            loanArray.put(loan);
            loanSum = loanSum.add(diff);
        }
        else if (billAmount.compareTo(realAmount) < 0) {
            //第二个借方科目是2241的二级科目，发生额是差额
            JSONObject borrow = new JSONObject();
            TAccountantSubjectEntity subjectChildOf2241 = getChildSubjectOf2241(oid,personnelReimburse);
            borrow.put("subject",subjectChildOf2241.getSubject());
            borrow.put("subjectNames","其他应付款-" + subjectChildOf2241.getName());
            BigDecimal diff = realAmount.subtract(billAmount);
            borrow.put("price",diff);
            borrowArray.put(borrow);
            borrowSum = borrowSum.add(diff);
        }

        TAccountantSubjectEntity subjectParam = new TAccountantSubjectEntity();
        subjectParam.setName(account);
        subjectParam.setOrg(oid);

        if ("1".equals(isPublic)) {
            //如果支出方式是对公户，贷方科目需要有1002的三级子科目,根据对公户的账号来查
            subjectParam = tAccountSubjectMapper.getByName(subjectParam);
            JSONObject loan = new JSONObject();
            if (subjectParam != null) {
                loan.put("subject",subjectParam.getSubject());
                loan.put("subjectNames","银行存款-" + bankName + "-" + account);
                loan.put("price",realAmount);
            }
            else
            {
                //如果特殊情况下没有生成1002的三级科目，那就先用1002替代
                loan.put("subject","1002");
                loan.put("subjectNames","银行存款");
                loan.put("price",realAmount);
            }
            loanArray.put(loan);
        }
        else {
            //如果不是对公户，贷方科目取1001
            JSONObject loan = new JSONObject();
            loan.put("subject","1001");
            loan.put("subjectNames","库存现金");
            loan.put("price",realAmount);
            loanArray.put(loan);
        }
        loanSum = loanSum.add(realAmount);

        JSONArray cashArray = new JSONArray();
        JSONObject cashObject = new JSONObject();
        //现金流量
        cashObject.put("cashitem","r9c4");
        cashObject.put("cashes",realAmount);
        cashArray.put(cashObject);
        voucherParams.setCashjson(cashArray.toString());

        subjectBorrow = borrowArray.toString();
        voucherParams.setSubjectBorrow(subjectBorrow);
        subjectLoan = loanArray.toString();
        voucherParams.setSubjectLoan(subjectLoan);

        //判断借方和贷方总金额是否相等,只有当借方发生额 = 贷方发生额 时才能生成凭证
        if (borrowSum.compareTo(loanSum) == 0) {
            //找到机构大会计
            User accountant = null;
            accountant = userService.getUserByRoleCode(oid, "accounting");
            if (accountant == null) {
                accountant = userService.getUserByRoleCode(oid, "agentAccounting");
            }
            voucherParams.setUser(accountant);
            String voucherDate = voucherUpdate.getVoucherDate(belong_peroid);
            voucherParams.setVoucherDate(voucherDate);

            try {
//                String status = dataService.updateAccountantStatus(detailId,"1","2");
//                if ("1".equals(status)) {
//                    int voucherID = voucherUpdate.newVoucher(voucherParams,"1");
//                    if (voucherID > 0) return 1;
//                }
                int voucherID = voucherUpdate.newVoucher(voucherParams,"1");
                Integer res = dataService.updateVoucher(detailId,null,voucherID,true);//更新财务数据.是否下账,false-不予下账,true-下帐
                return res;
            }
            catch (Exception e) {
                e.printStackTrace();
//                dataService.updateAccountantStatus(detailId,"1","1");
            }

        }

        return 0;
    }

    /*
    * 根据报销申请人id获得该机构下由申请者姓名+手机号命名的2241二级子科目
    * 如果没有的话需要先生成
    * 最后返回2241下二级子科目的科目编号
    * */
    private TAccountantSubjectEntity getChildSubjectOf2241(Integer org, PersonnelReimburse personnelReimburse) {
        TAccountantSubjectEntity result = null;
        Integer creator = personnelReimburse.getCreator();
        User user = userService.getUserByID(creator);
        String userName = user.getUserName();
        String phone = user.getMobile();
        String name = userName + phone;
        TAccountantSubjectEntity subjectEntity = new TAccountantSubjectEntity();
        subjectEntity.setOrg(org);
        subjectEntity.setParent("2241");
        subjectEntity.setName(name);
        subjectEntity = tAccountSubjectMapper.getByPhone(subjectEntity);
        if (subjectEntity != null) return subjectEntity;
        else {
            //生成子科目
            String newSubjectJson = settingService.firstSubject("2241", org);
            net.sf.json.JSONArray jsonArray = net.sf.json.JSONArray.fromObject(newSubjectJson);
            if (jsonArray.size() > 0) {
                net.sf.json.JSONObject obj = jsonArray.getJSONObject(0);
                String newSubject = obj.optString("newSubject");
                net.sf.json.JSONObject newSubjectID = settingService.newAccountantSubject("2241",org,name,"",user.getUserID(),user.getUserName(),newSubject,"",null,null);
                int newid = newSubjectID.optInt("newSubjectID", 0);
                if (newid > 0) {
                    result = new TAccountantSubjectEntity();
                    result.setSubject(newSubject);
                    result.setName(name);
                }
            }
        }
        return result;
    }

    /*
    * 自动匹配科目，将关联的科目暂存在报销票据类里
    * 1、根据票据的创建人查找是否已经创建了相关的科目，如果没有的话需要新建科目
    * 2、如果已经存在科目的话则将该科目暂存在该票据里
    * 3、新建科目规则：姓名 + 手机号
    * superSubject                  高级科目5601或5602，根据报销中"是否属于销售事务"属性来定的
    * list                          该票据明细所关联的科目，最多能关联两个科目
    * FinanceReimburseBillItem      票据明细对象
    * superSubjectName              高级科目名称
    * */
    private void autoMatchSubject(Integer org,String superSubject,String superSubjectName, List<TAccountantSubjectRelevance> list, FinanceReimburseBillItem billItem) {
        Integer creator = billItem.getCreator();
        User user = userService.getUserByID(creator);
        String userName = user.getUserName();
        String phone = user.getMobile();
        //判断该机构是否存在以关联的二级科目为parent，以票据创建人名称 + 手机号为科目名称命名的科目
        for (TAccountantSubjectRelevance relevance : list) {
            String code = relevance.getCode();
            String relevanceSubject = relevance.getSubject();
            if (superSubject.equals(code)) {
                String name = userName + phone;
                TAccountantSubjectEntity subjectEntity = new TAccountantSubjectEntity();
                subjectEntity.setOrg(org);
                subjectEntity.setParent(relevanceSubject);
                subjectEntity.setName(name);
                subjectEntity = tAccountSubjectMapper.getByPhone(subjectEntity);
                String newName = superSubjectName + "-" + relevance.getName() + "-" + name;
                if (subjectEntity == null) {
                    //需要新建科目
                    String newSubjectJson = settingService.firstSubject(relevanceSubject, org);
                    net.sf.json.JSONArray jsonArray = net.sf.json.JSONArray.fromObject(newSubjectJson);
                    if (jsonArray.size() > 0) {
                        net.sf.json.JSONObject obj = jsonArray.getJSONObject(0);
                        String newSubject = obj.optString("newSubject");
                        net.sf.json.JSONObject newSubjectID = settingService.newAccountantSubject(relevanceSubject,org,name,"",user.getUserID(),user.getUserName(),newSubject,"",null,null);
                        int newid = newSubjectID.optInt("newSubjectID", 0);
                        if (newid > 0) {
                            newName += newSubject;
                            billItem.setSubject(newSubject);
                            billItem.setSubjectName(newName);
                            billItemDao.update(billItem);
                        }
                    }
                }
                else {
                    //如果自动生成的科目已经存在，则自动选择该科目
                    String subject = subjectEntity.getSubject();
                    billItem.setSubject(subject);
                    billItem.setSubjectName(newName);
                    billItemDao.update(billItem);
                }
            }
        }

    }


}
