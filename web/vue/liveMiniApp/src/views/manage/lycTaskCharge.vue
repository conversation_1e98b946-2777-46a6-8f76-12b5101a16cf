<template>
  <div class="lycTaskCharge">
    <Pageheader title="歌词任务审批" :showBack="true" />
    <div class="handleC">
      <div @click="tabNum(1)" :class="activeH === 1 ? 'activeH' :''">待确认（X）</div>
      <div @click="tabNum(2)" :class="activeH === 2 ? 'activeH' :''">未选用（X）</div>
    </div>
    <div class="mar30">
      <div class="listCC" v-show="activeH === 1">
        <div class="liItem" v-for="(item, index) in list" :key="index">
          <div class="flexC" @click="goDetail(item)">
            <div>{{ item.name }}</div>
            <div>{{ item.rhythmNum }}份</div>
            <div>{{ item.durationTime}}
              <el-icon class="live-right relaRight"><arrow-right-bold color="#53B5A8" class="houseSty8" /></el-icon>
            </div>
          </div>

        </div>

      </div>
      <div class="listCC" v-show="activeH === 2">
        <div class="liItem" v-for="(item, index) in list" :key="index">
          <div class="flexC" @click="goDetail(item)">
            <div>{{ item.name }}</div>
            <div>{{ item.rhythmNum }}份</div>
            <div>{{ item.durationTime}}
              <el-icon class="live-right relaRight"><arrow-right-bold color="#53B5A8" class="houseSty8" /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import JSONBig from 'json-bigint'
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { getLyricTask } from '@/api/music.js'

const activeH = ref(1)
const list = ref([])
const router = useRouter();


onMounted(() => {
  // let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  // console.log('jsonData=', jsonData)
  tabNum(1)
})

const tabNum = (num) => {
  activeH.value = num
  // localStorage.setItem('activeH', num)
  getList(num)
}
const goDetail = (item) => {
  localStorage.setItem('lycTaskChargeItem', JSONBig.stringify(item))
  router.push({ name: 'lycTaskChargeDetails' });
}
const getList = (num) => {
  let data = { type:1, approveStatus:1 }
  if(num === 2){
    data = { type:1, approveStatus:2 }
  }
  getLyricTask(data).then(res=>{
    console.log('getLyricTask 返回值', res)
    list.value = res.data.listSongs
  }).catch(err => {
    console.log('有错误', err)
  })
}

</script>
<style lang="scss" scoped>
.lycTaskCharge{
  .handleC{
    display: flex;
    padding-top: 10px;
    background-color: #fff;
    >div{
      flex: 1;
      line-height: 30px;
      border-bottom: 2px solid #fff;
      text-align: center;
      &.activeH{
        border-bottom-color: $livePageHeadBg;
      }
    }
  }
  .flexC{
    display: flex;
    >div{
      line-height: 40px;
    }
    >div:nth-child(1){
      flex: 2;
      padding-left:10px ;
    }
    >div:nth-child(2){
      flex: 1;
      text-align: center;
    }
    >div:nth-child(3){
      flex: 1;
      text-align: right;
      padding-right:10px ;
      .relaRight{
        position: relative;
        top:10px;
      }
    }
  }
  .listCC{
    background: #fff;
    padding-top: 10px;
    .liItem:nth-child(odd){
      background: #eee;
    }
  }
}

</style>
