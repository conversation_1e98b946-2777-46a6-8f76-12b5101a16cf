package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierInvoice;
import cn.sphd.miners.modules.material.entity.SrmSupplierInvoiceHistory;

import java.io.Serializable;

public interface SrmSupplierInvoiceHistoryDao extends IBaseDao<SrmSupplierInvoiceHistory, Serializable> {

   Integer saveHis(SrmSupplierInvoice srmSupplierInvoice,Integer hisId);
}
