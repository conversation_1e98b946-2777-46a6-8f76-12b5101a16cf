package cn.sphd.miners.modules.finance.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_finance_account_bill_history")
public class FinanceAccountBillHistory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="account_detail"   , nullable=true , unique=false)
    private Integer accountDetail;  //帐号

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;  //1-转帐支票(内部的)，2-现金汇票 3-承兑汇票(仅在此处用) 4-外部的转账支票(仅在此处用)

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;  //金额

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;  //票面金额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;  //摘要

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;  //用途

    @Column(name="has_bill"   , nullable=true , unique=false)
    private Boolean hasBill;  //是否有票据,true-有

    @Column(name="bill_cat"  , length=50 , nullable=true , unique=false)
    private String billCat;   //票据类型

    @Column(name="bill_quantity"   , nullable=true , unique=false)
    private Integer billQuantity;  //票据数量

    @Column(name="bill_period"  , length=1 , nullable=true , unique=false)
    private String billPeriod;  //1-本月票据,2-非本月票据

    @Column(name="bill_no"  , length=255 , nullable=true , unique=false)
    private String billNo;  //发票号码

    @Column(name="cheque"   , nullable=true , unique=false)
    private Integer cheque;  //支票ID(内部支票)

    @Column(name="return_bill"   , nullable=true , unique=false)
    private Integer returnBill;  //回款票据id

    @Column(name="opposite_corp"  , length=100 , nullable=true , unique=false)
    private String oppositeCorp;  //对方单位

    @Column(name="opposite_bankno"  , length=100 , nullable=true , unique=false)
    private String oppositeBankno;  //对方银行代码

    @Column(name="opposite_bankcode"  , length=100 , nullable=true , unique=false)
    private String oppositeBankcode;  //对方银行帐号

    @Column(name="receive_account_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date receiveAccountDate;   //到帐日期

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;  //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;  //审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核',

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;  //经手人

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;  //消息ID

    @Column(name="account_id"   , nullable=true , unique=false)
    private Integer accountId;  //(存入)银行账户的id

    @Column(name="accout"  , length=100 , nullable=true , unique=false)
    private String accout;  //(存入)账户

    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName;  // 银行名称

    @Column(name="save_bank_name"  , length=100 , nullable=true , unique=false)
    private String saveBankName;  // (存入)支票/汇票的银行名称

    @Column(name="depositor_name"  , length=100 , nullable=true , unique=false)
    private String depositorName;  //存入经手人

    @Column(name="deposit_date"   , nullable=true , unique=false)
    private Date depositDate;  //存入时间

    @Column(name="financial_handling"   , nullable=true , unique=false)
    private String financialHandling;//财务经手人

    @Column(name="receiver"  , length=100 , nullable=true , unique=false)
    private String receiver;  //接收经手人

    @Column(name="operator"  , length=100 , nullable=true , unique=false)
    private String operator;  //支付经手人

    @Column(name="expire_date"   , nullable=true , unique=false)
    private Date expireDate;  //支票到期日

    @Column(name="receive_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date receiveDate;  //接收日期

    @Column(name="receive_corp"  , length=100 , nullable=true , unique=false)
    private String receiveCorp;  //收款单位

    @Column(name = "business")
    private Integer business;//业务号

    @Column(name= "source",length = 1,nullable = true,unique = false)
    private String source;//数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)

    @Column(name= "state",length = 1,nullable = true,unique = false)
    private String state;//状态：0-未入账，1-重新入账，2-已入账

    @Column(name="occurrence_date"   , nullable=true , unique=false)
    private Date occurrenceDate;  //发生日期(借款付款录入中为付款日期)

    @Column(name = "previous_id")
    private Integer previousId;   //修改前记录ID

    @Column(name = "version_no")
    private Integer versionNo;   //版本号,每次修改+1

    @Column(name="business_date"   , nullable=true , unique=false)
    private Date businessDate;  //业务发生日期

    @Column(name= "business_type",nullable = true,unique = false)
    private String businessType;//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款

    @Column(name = "business_history")
    private Integer businessHistory;//历史表中的业务号

    @Column(name = "cheque_history")
    private Integer chequeHistory;//历史支票明细ID

    @Column(name = "return_bill_history")
    private Integer returnBillHistory;//历史回款票据ID

    @Column(name="sub_type"  , length=1 , nullable=true , unique=false)
    private String subType;   //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出

    @Column(name="stakeholder_category"  , length=1 , nullable=true , unique=false)
    private String stakeholderCategory;   //类别:1-供应商,2-员工,3-自行录入

    @Column(name = "stakeholder")
    private Integer stakeholder;//干系人ID

    @Column(name = "stakeholders_history")
    private Integer stakeholdersHistory;//干系人历史ID

    @Column(name = "fact_user")
    private Integer factUser;//实际用户(同事userid)',

    @Column(name="bill_date"   , nullable=true , unique=false)
    private Date billDate;  //票据日期

    @Column(name="fact_date"   , nullable=true , unique=false)
    private Date factDate;  //实际(付款/支出)日期'

    @Column(name = "voucher")
    private Integer voucher;//凭证ID,为空时-未入会计

    @Column(name="is_account"   , nullable=true , unique=false)
    private Boolean isAccount;  //是否下账,false-不予下账,true-下帐

    @Column(name="is_modify"   , nullable=true , unique=false)
    private Boolean isModify;  //是否修改,false-未修改(0),true-已修改(1)

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="bill_detail", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceAccountBill billDetail;

    @Column(name="bill_detail"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer billDetail_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Boolean getHasBill() {
        return hasBill;
    }

    public void setHasBill(Boolean hasBill) {
        this.hasBill = hasBill;
    }

    public String getBillCat() {
        return billCat;
    }

    public void setBillCat(String billCat) {
        this.billCat = billCat;
    }

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    public String getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(String billPeriod) {
        this.billPeriod = billPeriod;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Integer getCheque() {
        return cheque;
    }

    public void setCheque(Integer cheque) {
        this.cheque = cheque;
    }

    public Integer getReturnBill() {
        return returnBill;
    }

    public void setReturnBill(Integer returnBill) {
        this.returnBill = returnBill;
    }

    public String getOppositeCorp() {
        return oppositeCorp;
    }

    public void setOppositeCorp(String oppositeCorp) {
        this.oppositeCorp = oppositeCorp;
    }

    public String getOppositeBankno() {
        return oppositeBankno;
    }

    public void setOppositeBankno(String oppositeBankno) {
        this.oppositeBankno = oppositeBankno;
    }

    public String getOppositeBankcode() {
        return oppositeBankcode;
    }

    public void setOppositeBankcode(String oppositeBankcode) {
        this.oppositeBankcode = oppositeBankcode;
    }

    public Date getReceiveAccountDate() {
        return receiveAccountDate;
    }

    public void setReceiveAccountDate(Date receiveAccountDate) {
        this.receiveAccountDate = receiveAccountDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public FinanceAccountBill getBillDetail() {
        return billDetail;
    }

    public void setBillDetail(FinanceAccountBill billDetail) {
        this.billDetail = billDetail;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getAccout() {
        return accout;
    }

    public void setAccout(String accout) {
        this.accout = accout;
    }

    public String getSaveBankName() {
        return saveBankName;
    }

    public void setSaveBankName(String saveBankName) {
        this.saveBankName = saveBankName;
    }

    public String getDepositorName() {
        return depositorName;
    }

    public void setDepositorName(String depositorName) {
        this.depositorName = depositorName;
    }

    public Date getDepositDate() {
        return depositDate;
    }

    public void setDepositDate(Date depositDate) {
        this.depositDate = depositDate;
    }

    public String getFinancialHandling() {
        return financialHandling;
    }

    public void setFinancialHandling(String financialHandling) {
        this.financialHandling = financialHandling;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getReceiveCorp() {
        return receiveCorp;
    }

    public void setReceiveCorp(String receiveCorp) {
        this.receiveCorp = receiveCorp;
    }

    public Integer getBillDetail_() {
        return billDetail_;
    }

    public void setBillDetail_(Integer billDetail_) {
        this.billDetail_ = billDetail_;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getOccurrenceDate() {
        return occurrenceDate;
    }

    public void setOccurrenceDate(Date occurrenceDate) {
        this.occurrenceDate = occurrenceDate;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getBusinessHistory() {
        return businessHistory;
    }

    public void setBusinessHistory(Integer businessHistory) {
        this.businessHistory = businessHistory;
    }

    public Integer getChequeHistory() {
        return chequeHistory;
    }

    public void setChequeHistory(Integer chequeHistory) {
        this.chequeHistory = chequeHistory;
    }

    public Integer getReturnBillHistory() {
        return returnBillHistory;
    }

    public void setReturnBillHistory(Integer returnBillHistory) {
        this.returnBillHistory = returnBillHistory;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getStakeholderCategory() {
        return stakeholderCategory;
    }

    public void setStakeholderCategory(String stakeholderCategory) {
        this.stakeholderCategory = stakeholderCategory;
    }

    public Integer getStakeholder() {
        return stakeholder;
    }

    public void setStakeholder(Integer stakeholder) {
        this.stakeholder = stakeholder;
    }

    public Integer getStakeholdersHistory() {
        return stakeholdersHistory;
    }

    public void setStakeholdersHistory(Integer stakeholdersHistory) {
        this.stakeholdersHistory = stakeholdersHistory;
    }

    public Integer getFactUser() {
        return factUser;
    }

    public void setFactUser(Integer factUser) {
        this.factUser = factUser;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public Date getFactDate() {
        return factDate;
    }

    public void setFactDate(Date factDate) {
        this.factDate = factDate;
    }

    public Integer getVoucher() {
        return voucher;
    }

    public void setVoucher(Integer voucher) {
        this.voucher = voucher;
    }

    public Boolean getAccount() {
        return isAccount;
    }

    public void setAccount(Boolean account) {
        isAccount = account;
    }

    public Boolean getModify() {
        return isModify;
    }

    public void setModify(Boolean modify) {
        isModify = modify;
    }
}
