<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="approvalSettingsApplyDetails" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="审批设置的修改申请" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container approvalSettingsDetails">
      <div class="ttl1">{{tips}}</div>
<!--      <div class="ttl1" v-if="info.approvalItem && info.approvalItem.code!=='purchaseApprovalSettings'">计划于{{info.approvalItem.openDate | formatDay('YYYY/MM/DD')}}生效的{{info.approvalItem.code | toType}}{{info.state | toSate}}</div>-->
<!--      <div class="ttl1" v-if="info.approvalItem && info.approvalItem.code==='purchaseApprovalSettings'">采购审批流程已被修改，计划于{{info.after.openDate | formatDay('YYYY-MM-DD')}}生效，有待审批</div>-->
<!--      <div class="ttl1" v-if="info.approvalItem && info.approvalItem.code==='stockModeChange'">现提请修改仓库的模式，修改计划生效的日期：{{info.after.openDate | formatDay('YYYY-MM-DD')}}<br>请予审批！</div>-->
      <div class="ttl2" v-if="info.approvalItem.code!=='purchaseApprovalSettings'">
        <div v-if="info.approvalProcessList.length > 0">
          <p>审批记录</p>
          <p>申请人：{{info.approvalItem.createName || ''}} {{info.approvalItem.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div>
            <p v-if="item.approveStatus==2 || item.approveStatus==3" v-for='(item, index) in info.approvalProcessList' v-bind:key='index'>审批人：{{item.userName}} {{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
            <div class="reason clr2" v-if="item.approveStatus==3" v-for='(item, index) in info.approvalProcessList' v-bind:key='index'><span class="red">驳回理由：</span><div style="word-break: break-all;padding:15px;  ">{{item.reason}}</div> </div>
            <p v-if="item.approveStatus==1" v-for='(item, index) in info.approvalProcessList' v-bind:key='index'>等待 <span class="red">{{item.userName}}</span> 审批</p>
          </div>
          <div class="clr2"></div>
        </div>
      </div>
      <div class="ttl2" v-if="info.approvalItem.code==='purchaseApprovalSettings'">
        <p>审批记录</p>
        <p>申请人：{{info.createName || ''}} {{info.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
        <div>
          <p>等待 <span class="red">{{info.userName}}</span> 审批</p>
        </div>
        <div class="clr2"></div>
      </div>
      <div class="ttl3">
        <div :class="{active: seeType === 1}" @click="toggle(1)">修改后</div>
        <div :class="{active: seeType === 0}" @click="toggle(0)">修改前</div>
      </div>
      <div class="con">
        <!-- 修改后 -->
        <!--加班-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='overTimeApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p>不高于{{item.amountCeiling}}小时的加班，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--付款-->
        <div class="whiteBox fup" v-if="seeType && info.approvalItem.code==='paymentApproval'"  >
          <!--<p> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>-->
          <p>付款审批者：{{ info.approvalItem.status === 0 ? "无需审批" : `${ info.approvalFlows[0]["userName"]} ${ info.approvalFlows[0]["mobile"]}`}}</p>
          <p>付款复核者：{{ info.approvalItemAudit.status === 0 ? "不需要" : `${ info.paymentAuditUser.userName } ${ info.paymentAuditUser.mobile }`}}</p>
          <div class="clr2"></div>
        </div>
        <!--报销-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='reimburseApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p v-if="item.amountCeiling>0">不高于{{item.amountCeiling}}元的报销，需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level == 1">报销需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level != 1">高于{{info.top}}元的报销，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--请假-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='leaveApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p v-if="index === 0 && item.amountCeiling === -1">请假需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 0 && item.amountCeiling <= 12">当日不高于{{item.amountCeiling}}小时的请假，需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 12">不高于{{item.amountCeiling / 24}}天的请假，需{{item.level | Upper}}级审批</p>
          <p v-else>高于{{info.approvalFlows[index - 1].amountCeiling | changeDay}}的请假，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--来自客户订单的评审-->
        <div class="approveItem fatter" v-if="seeType && (info.approvalItem.code ==='ordersReview' || info.approvalItem.code ==='materialInCheck' || info.approvalItem.code ==='productInCheck' || info.approvalItem.code ==='productLogisticsCheck' || info.approvalItem.code ==='finishedProductCheck')">
          <p v-if="info.approvalItem.code ==='ordersReview' && info.approvalItem.status === 1">对于客户发来订单中的数量与交期，需要公司各部门评审</p>
          <p v-else-if="info.approvalItem.code ==='ordersReview' && info.approvalItem.status === 0">对于客户发来订单中的数量与交期，无需公司各部门评审</p>
          <p v-else-if="info.approvalItem.code ==='materialInCheck' && info.approvalItem.status === 0">对于采购来的材料，入库前不需要检验</p>
          <p v-else-if="info.approvalItem.code ==='materialInCheck' && info.approvalItem.status === 1">对于采购来的材料，入库前需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productInCheck' && info.approvalItem.status === 0">生产出的货物入成品库前不需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productInCheck' && info.approvalItem.status === 1">生产出的货物入成品库前需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productLogisticsCheck' && info.approvalItem.status === 0">成品出库时物流人员的复核的模式：无需物流复核</p>
          <p v-else-if="info.approvalItem.code ==='productLogisticsCheck' && info.approvalItem.status === 1">成品出库时物流人员的复核的模式：需物流复核</p>
          <p v-else-if="info.approvalItem.code ==='finishedProductCheck' && info.approvalItem.status === 0">成品库的模式：不启用成品库</p>
          <p v-else-if="info.approvalItem.code ==='finishedProductCheck' && info.approvalItem.status === 1">成品库的模式：启用成品库</p>
        </div>
        <!--采购审批流程的修改审批-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='purchaseApprovalSettings'">
          <p v-if="info.after.status === 0">采购无需审批</p>
          <p v-if="info.after.status === 1">采购需{{info.after.level | Upper}}级审批</p>
          <p>开始执行的时间: {{info.after.openDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div v-if="info.after.status === 1">
            <p v-for='(item, index) in info.after.userList' v-bind:key='index'>
              {{item.level | Upper}}级审批者： {{item.userName}}  {{item.mobile}}
            </p>
          </div>
        </div>
        <!--商品与产品名称代号关系的修改申请-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='commodityProduct'">
          <div class="ptMode">
            <div>当前产品图号与名称的创建模式：</div>
            <div>与专属商品相关的产品为模式{{info.pdModelSettingsNew.dedicatedModel}}</div>
            <div>与通用型商品相关的产品为模式{{info.pdModelSettingsNew.generalModel}}</div>
            <div v-if="info.pdModelSettingsNew.generalModel !== 1">产品信息{{info.pdModelSettingsNew.reviseModel === 1 ? `由有权限创建商品的职工修改`:`由有产品操作权限的职工修改`}}</div>
          </div>
        </div>
        <!--仓库的模式修改审批-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='stockModeChange'">
          <p v-if="info.approvalItem.status === 0">仓库的模式：非智能仓库</p>
          <p v-if="info.approvalItem.status === 1">仓库的模式：智能仓库</p>
        </div>
        <!--外购物料入库时，包装完好情况的检查者-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='inStockCheckerSet'">
          <p v-if="info.approvalItem.status === 0">外购物料入库时，需由库管检查包装的完好情况 </p>
          <p v-if="info.approvalItem.status === 1">外购物料入库时，需由检验员检查包装的完好情况</p>
          <p v-if="info.approvalItem.status === 2">外购物料入库时，库管与检验员均需检查包装的完好情况</p>
          <p v-if="info.approvalItem.status === 3">外购物料入库时，不需检查包装的完好情况</p>
        </div>
        <!-- 修改前 -->
        <!--加班-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='overTimeApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p>不高于{{item.amountCeiling}}小时的加班，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--付款-->
        <div class="whiteBox fup" v-if="!seeType && info.approvalItem.code==='paymentApproval'"  >
          <!--<p> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>-->
          <p>付款审批者：{{ info.approvalItemOld.status === 0 ? "无需审批" : `${ info.approvalFlowsOld[0]["userName"]} ${ info.approvalFlowsOld[0]["mobile"]}`}}</p>
          <p>付款复核者：{{ info.approvalItemOldAudit.status === 0 ? "不需要" : `${ info.paymentAuditUser.userName } ${ info.paymentAuditUser.mobile }`}}</p>
          <div class="clr2"></div>
        </div>
        <!--报销-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='reimburseApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p v-if="item.amountCeiling>0">不高于{{item.amountCeiling}}元的报销，需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level == 1">报销需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level != 1">高于{{info.topOld}}元的报销，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName|| item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--请假-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='leaveApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p v-if="index === 0 && item.amountCeiling === -1 || !item.amountCeiling">请假需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 0 && item.amountCeiling <= 12">当日不高于{{item.amountCeiling}}小时的请假，需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 12">不高于{{item.amountCeiling / 24}}天的请假，需{{item.level | Upper}}级审批</p>
          <p v-else>高于{{info.approvalFlows[index - 1].amountCeiling | changeDay}}的请假，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--来自客户订单的评审-->
        <div class="approveItem fatter" v-if="!seeType && (info.approvalItem.code ==='ordersReview' || info.approvalItem.code ==='materialInCheck' || info.approvalItem.code ==='productInCheck' || info.approvalItem.code ==='productLogisticsCheck' || info.approvalItem.code ==='finishedProductCheck')">
          <p v-if="info.approvalItemOld.code ==='ordersReview' && info.approvalItemOld.status === 1">对于客户发来订单中的数量与交期，需要公司各部门评审</p>
          <p v-else-if="info.approvalItemOld.code ==='ordersReview' && info.approvalItemOld.status === 0">对于客户发来订单中的数量与交期，无需公司各部门评审</p>
          <p v-else-if="info.approvalItemOld.code ==='materialInCheck' && info.approvalItemOld.status === 0">对于采购来的材料，入库前不需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='materialInCheck' && info.approvalItemOld.status === 1">对于采购来的材料，入库前需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productInCheck' && info.approvalItemOld.status === 0">生产出的货物入成品库前不需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productInCheck' && info.approvalItemOld.status === 1">生产出的货物入成品库前需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productLogisticsCheck' && info.approvalItemOld.status === 0">成品出库时物流人员的复核的模式：无需物流复核</p>
          <p v-else-if="info.approvalItemOld.code ==='productLogisticsCheck' && info.approvalItemOld.status === 1">成品出库时物流人员的复核的模式：需物流复核</p>
          <p v-else-if="info.approvalItemOld.code ==='finishedProductCheck' && info.approvalItemOld.status === 0">成品库的模式：不启用成品库</p>
          <p v-else-if="info.approvalItemOld.code ==='finishedProductCheck' && info.approvalItemOld.status === 1">成品库的模式：启用成品库</p>
        </div>
        <!--采购审批流程的修改审批-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='purchaseApprovalSettings'">
          <p v-if="info.before.status === 0">采购无需审批</p>
          <p v-if="info.before.status === 1">采购需{{info.before.level | Upper}}级审批</p>
          <p>开始执行的时间: {{info.before.openDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div v-if="info.before.status === 1">
            <p v-for='(item, index) in info.before.userList' v-bind:key='index'>
              {{item.level | Upper}}级审批者： {{item.userName}}  {{item.mobile}}
            </p>
          </div>
        </div>
        <!--商品与产品名称代号关系的修改申请-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='commodityProduct'">
          <div class="ptMode">
            <div>当前产品图号与名称的创建模式：</div>
            <div>与专属商品相关的产品为模式{{info.pdModelSettingsOld.dedicatedModel}}</div>
            <div>与通用型商品相关的产品为模式{{info.pdModelSettingsOld.generalModel}}</div>
            <div v-if="info.pdModelSettingsOld.generalModel !== 1">产品信息{{info.pdModelSettingsOld.reviseModel === 1 ? `由有权限创建商品的职工修改`:`由有产品操作权限的职工修改`}}</div>
          </div>
        </div>
        <!--仓库的模式修改审批-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='stockModeChange'">
          <p v-if="info.approvalItemOld.status === 0">仓库的模式：非智能仓库</p>
          <p v-if="info.approvalItemOld.status === 1">仓库的模式：智能仓库</p>
        </div>
        <!--外购物料入库时，包装完好情况的检查者-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='inStockCheckerSet'">
          <p v-if="info.approvalItemOld.status === 0">外购物料入库时，需由库管检查包装的完好情况 </p>
          <p v-if="info.approvalItemOld.status === 1">外购物料入库时，需由检验员检查包装的完好情况</p>
          <p v-if="info.approvalItemOld.status === 2">外购物料入库时，库管与检验员均需检查包装的完好情况</p>
          <p v-if="info.approvalItemOld.status === 3">外购物料入库时，不需检查包装的完好情况</p>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  .approvalSettingsDetails{
    .fup>p{ padding: 10px 15px; } .ttl1{ padding:8px;     }
    .ttl1 {white-space: pre-wrap}
    .ttl2{
      background: #fff; padding:8px;
      p{
        .red{ color: #f92111;   }
        &.reason{
          width: 96%;
        }
      }
    }
    .ttl3{
      display: flex;
      div{
        flex:1; border-bottom: 2px solid #ddd; text-align:center; line-height:40px; height:40px;cursor: pointer;
        &.active{
          border-bottom-color: #36c6d3;
          color: #36c6d3;
        }
      }
    }
    .clr2{clear: both;  }
    .right{ text-align:right;  }
    .con{
      background: #fff;
    }
    .con>.whiteBox{
      background:#fff ;
      span{display: inline-block; padding:10px 2.5rem; }
    }
    .con>.approveItem{
      padding:5px;
      &:nth-child(even){ background:#fff;  }
      .ptMode{padding:5px;}
    }
    .fatter p{padding: 4px;}
    .handle_button{
      background: #fff;
      padding: 4px 8px;
    }
  }
</style>
<script>
export default {
  name: 'approvalSettingsApplyDetails',
  data () {
    return {
      info: {
        state: 0, // 最终的状态
        approvalItem: {},
        approvalFlows: [],
        approvalFlowsOld: [],
        approvalProcessList: [],
        before: {
          userList: []
        },
        after: {
          userList: []
        },
        userName: '',
        createName: '',
        createDate: ''
      },
      seeType: 1,
      reason: '',
      showBtn: true,
      chargeType: 1,
      tipDialog1: false,
      tipDialog0: false,
      loading: true,
      approvalProcessId: 0,
      isBackHome: false,
      listeners: []
    }
  },
  filters: {
    Upper: function (num) {
      switch (Number(num)) {
        case 1: return '一'
        case 2: return '二'
        case 3: return '三'
        case 4: return '四'
        case 5: return '五'
        case 6: return '六'
        case 7: return '七'
        case 8: return '八'
        default:
      }
    },
    toSate: function (num) {
      switch (Number(num)) {
        case 1: return '有待审批'
        default: return ''
      }
    },
    toType: function (code) {
      switch (code) {
        case 'paymentApproval':return '新付款审批流程'
        case 'overTimeApply': return '新加班审批流程'
        case 'leaveApply': return '新请假审批流程'
        case 'reimburseApply': return '新报销审批流程'
        case 'ordersReview': return '客户订单评审新流程'
        case 'materialInCheck': return '外购材料检验新流程'
        case 'productInCheck': return '成品入库新流程'
        case 'commodityProduct': return '通用型商品与产品名称代号关系的修改申请'
        case 'stockModeChange': return '仓库的模式'
        default: return ''
      }
    },
    changeDay: function (hour) {
      var str = ''
      if (hour >= 24) {
        str = hour / 24 + '天'
      } else {
        str = hour + '小时'
      }
      return str
    }
  },
  created: function () {
    let _this = this
    this.approvalProcessId = this.$route.params.id
    console.log("approvalProcessId", this.$route.params.id)
    _this.applyDetail()
    _this.listeners = [
      // 订阅审批结果
      _this.sphdSocket.subscribe('handleApply', function (data) {
        console.log('获取订阅审批结果:' + data)
        let res = JSON.parse(data)
        console.log('res.business: ', res)
        console.log('res.business: ' + res.business)
        console.log('_this.info.approvalItem.id: ' + _this.info.approvalItem.id)
        if (res.approvalProcess.business === _this.info.approvalItem.id) {
          _this.applyDetail()
          _this.$message({
            type: 'info',
            message: '审批状态已变更'
          })
        }
      }, function () {
        console.log('获取订阅审批结果失败！')
      }, 'user')
    ]
  },
  methods: {
    applyDetail: function () {
      let _this = this
      _this.axios.post('../../../popedom/updateItemDetail.do', {
        'approvalProcessId': _this.approvalProcessId
      }).then(function (response) {
        console.log('详情', response.data.data)
        _this.info = response.data.data
        _this.loading = false
        if (_this.info.approvalItem.code === 'reimburseApply') {
          let top = ''
          let topOld = ''
          _this.info.approvalFlowsOld.forEach(function (item) {
            if (item.amountCeiling !== -1) {
              topOld = item.amountCeiling
            }
          })
          _this.info.approvalFlows.forEach(function (item) {
            if (item.amountCeiling !== -1) {
              top = item.amountCeiling
            }
          })
          _this.info['top'] = top
          _this.info['topOld'] = topOld
        }
        let process = _this.info.approvalProcessList
        if (process) {
          _this.info['state'] = process[process.length - 1]['approveStatus']
          console.log('详情后', _this.info)
        }
      }).catch(function (error) {
        console.log(error)
        _this.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    toggle: function (num) {
      this.seeType = num
    }
  },
  computed: {
    tips() {
      let RootFilters = this.$root.$options.filters
      let filters = this.$options.filters
      let code = this.info.approvalItem.code
      let tip = ''
      // <div className="ttl1" v-if="info.approvalItem && info.approvalItem.code!=='purchaseApprovalSettings'">计划于{{
      //   info
      //   .approvalItem.openDate | formatDay('YYYY/MM/DD')
      // }}生效的{{info.approvalItem.code | toType}}{{info.state | toSate}}</div>
      // <div className="ttl1"
      //      v-if="info.approvalItem && info.approvalItem.code==='purchaseApprovalSettings'">采购审批流程已被修改，计划于{{
      //   info
      //   .after.openDate | formatDay('YYYY-MM-DD')
      // }}生效，有待审批</div>
      // <div className="ttl1"
      //      v-if="info.approvalItem && info.approvalItem.code==='stockModeChange'">现提请修改仓库的模式，修改计划生效的日期：{{
      //   info
      //   .after.openDate | formatDay('YYYY-MM-DD')
      // }}<br>请予审批！</div>
      if (this.info.approvalItem) {
        if ( code==='purchaseApprovalSettings') {
          tip = `采购审批流程已被修改，计划于${RootFilters.formatDay(this.info.after.openDate, 'YYYY/MM/DD')}生效，有待审批`
        } else if (code === 'stockModeChange') {
          tip = `现提请修改仓库的模式，修改计划生效的日期：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY/MM/DD')}\n请予审批！`
        } else if (code === 'productLogisticsCheck') {
          tip = `现提请修改成品出库时物流人员的复核的模式，修改计划生效的日期：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY-MM-DD')}\n请予审批！`
        } else if (code === 'finishedProductCheck') {
          tip = `现提请修改成品库的模式，修改计划生效的日期：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY-MM-DD')}\n请予审批！`
        } else if (code === 'inStockCheckerSet') {
          tip = `现提请修改外购物料入库时包装完好情况的检查者，此项修改计划于：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY-MM-DD')}生效。请予审批！`
        } else {
          tip = `计划于${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY/MM/DD')}生效的${filters.toType(this.info.approvalItem.code)}${filters.toSate(this.info.state)}`
        }
      }
      return tip
    }
  },
  destroyed: function () {
    let _this = this
    console.log('destroyed')
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  }
}
</script>
