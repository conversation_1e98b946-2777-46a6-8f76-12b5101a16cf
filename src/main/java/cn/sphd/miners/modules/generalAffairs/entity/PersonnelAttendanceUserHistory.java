package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2018/4/11.
 * 员工考勤历史表
 */
@Entity
@Table(name = "t_personnel_attendance_user_history")
public class PersonnelAttendanceUserHistory implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="user"   , nullable=true , unique=false)
    private Integer user;//用户id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="dept"   , nullable=true , unique=false)
    private Integer dept;//部门id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="attendance_date"   , nullable=true , unique=false)
    private Date attendanceDate;//考勤日期

    @Column(name="type"  , length=10 , nullable=true , unique=false)
    private String type;//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他 10-由无需考勤改到考勤【若是10，则不展示改前信息，找本表查具体的类型】

    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;//上班时间

    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;//下班时间

    @Column(name="business"   , nullable=true , unique=false)
    private Integer business;//业务ID 加班和请假分别对应于t_personnel_overtime、t_personnel_leave的主键ID',

    @Column(name="break_begin"   , nullable=true , unique=false)
    private Date breakBegin;//午休开始时间(上午下班时间)

    @Column(name="break_end"   , nullable=true , unique=false)
    private Date breakEnd;//午休结束时间(下午上班时间)

    @Column(name="begin_state"  , length=1 ,columnDefinition = "0", nullable=true , unique=false)
    private String beginState;//上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他

    @Column(name="end_state"  , length=1 ,columnDefinition = "0", nullable=true , unique=false)
    private String endState;//下班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他

    @Column(name="break_begin_state"  , length=1 ,columnDefinition = "0", nullable=true , unique=false)
    private String breakBeginState;//上午下班状态:0-未考勤,1-已考勤

    @Column(name="break_end_state"  , length=1 ,columnDefinition = "0", nullable=true , unique=false)
    private String breakEndState;//下午上班状态:0-未考勤,1-已考勤

    @Column(name="am_attendance"   , nullable=true , unique=false)
    private Date amAttendance; //上午考勤时间

    @Column(name="pm_attendance"   , nullable=true , unique=false)
    private Date pmAttendance; //下午考勤时间

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name = "update_desc",length=255,nullable=true , unique=false)
    private String updateDesc;//修改理由

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;  //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作:1-增,2—删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    @Column(name="source"   , nullable=true , unique=false)
    private Integer source; //来源:1-手工录入(默认),2-打卡

    //与员工考勤表
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="attendance", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelAttendanceUser personnelAttendanceUser;

    @Column(name="attendance"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer attendanceId;//考勤id

    @Transient
    public String userName;  //职工名称

    @Transient
    public String noNeedtype; //无需考勤的状态

    @Transient
    public String upState ; //上班考勤具体状态

    @Transient
    public String downState ; //下班考勤具体状态

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getDept() {
        return dept;
    }

    public void setDept(Integer dept) {
        this.dept = dept;
    }

    public Date getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public Date getBreakBegin() {
        return breakBegin;
    }

    public void setBreakBegin(Date breakBegin) {
        this.breakBegin = breakBegin;
    }

    public Date getBreakEnd() {
        return breakEnd;
    }

    public void setBreakEnd(Date breakEnd) {
        this.breakEnd = breakEnd;
    }

    public String getBeginState() {
        return beginState;
    }

    public void setBeginState(String beginState) {
        this.beginState = beginState;
    }

    public String getEndState() {
        return endState;
    }

    public void setEndState(String endState) {
        this.endState = endState;
    }

    public String getBreakBeginState() {
        return breakBeginState;
    }

    public void setBreakBeginState(String breakBeginState) {
        this.breakBeginState = breakBeginState;
    }

    public String getBreakEndState() {
        return breakEndState;
    }

    public void setBreakEndState(String breakEndState) {
        this.breakEndState = breakEndState;
    }

    public Date getAmAttendance() {
        return amAttendance;
    }

    public void setAmAttendance(Date amAttendance) {
        this.amAttendance = amAttendance;
    }

    public Date getPmAttendance() {
        return pmAttendance;
    }

    public void setPmAttendance(Date pmAttendance) {
        this.pmAttendance = pmAttendance;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public PersonnelAttendanceUser getPersonnelAttendanceUser() {
        return personnelAttendanceUser;
    }

    public void setPersonnelAttendanceUser(PersonnelAttendanceUser personnelAttendanceUser) {
        this.personnelAttendanceUser = personnelAttendanceUser;
    }

    public Integer getAttendanceId() {
        return attendanceId;
    }

    public void setAttendanceId(Integer attendanceId) {
        this.attendanceId = attendanceId;
    }

    public String getUpdateDesc() {
        return updateDesc;
    }

    public void setUpdateDesc(String updateDesc) {
        this.updateDesc = updateDesc;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNoNeedtype() {
        return noNeedtype;
    }

    public void setNoNeedtype(String noNeedtype) {
        this.noNeedtype = noNeedtype;
    }

    public String getUpState() {
        return upState;
    }

    public void setUpState(String upState) {
        this.upState = upState;
    }

    public String getDownState() {
        return downState;
    }

    public void setDownState(String downState) {
        this.downState = downState;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }
}
