package cn.sphd.miners.modules.mpWeChatRes.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.ec.entity.EcOrderDetail;
import cn.sphd.miners.modules.ec.entity.EcSku;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceValidPeriod;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResource;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceAttachment;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceHistory;
import cn.sphd.miners.modules.thirdPlatform.entity.TpMember;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2021/12/21.
 */
public interface WxResService {
    String module = "1.202";
    String defaultMchid = "1501736921";
    String serviceName = "wxResService";

    //获取文件实体
    WxResource getWxResSingle(Integer file);

    //获取文件附件实体
    WxResourceAttachment getWxResAttSingle(Integer attId);

    //获取历史文件实体
    WxResourceHistory getWxResHisSingle(Integer hisFile);

    //获取文件的个数
    Long getWxFileNum(String categoryPath);

    //获取某个目录下的文件
    HashMap<String, Object> getWxFileByCategory(Integer categoryId, PageInfo pageInfo);

    //查看某目录下是否有文件
    String wxFileIsExistByCategory(Integer categoryId);

    //上传文章
    HashMap<String, Object> wxAddFile(User user, WxResource resource, String listResAtt);

    //修改文章
    HashMap<String, Object> wxUpFile(User user, WxResource resource, String listResAtt);

    //停用文章
    Integer wxStopFile(User user, Integer file);

    //还原文章
    Integer wxFileRestore(User user, Integer file);

    //获取文章操作记录
    List<WxResourceHistory> listWxfileRecord(Integer file, PageInfo pageInfo);

    //获取文章详情
    HashMap<String, Object> getWxResMessage(Integer file);

    //获取文章某次历史的详情
    HashMap<String, Object> getWxHisResMessage(Integer hisFile);

    //搜索文章
    HashMap<String , Object> findFile(String fileName, PageInfo pageInfo);

    List<WxResource> getWithSkuByIds(Integer[] ids);

    void createValidPeriod(WxResource resource, EcSku sku, EcOrderDetail orderDetail, Long value, AuthInfoDto authInfo);
    WxResourceValidPeriod checkValidPeriod(EcOrderDetail orderDetail, Date now);

    /**
     * 获取当前用户（AuthInfoDto）的当前有效的卡有效期列表。
     * @param authInfo
     * @param resource
     * @return
     */
    List<WxResourceValidPeriod> getAllAuthResourceValidPeriod(AuthInfoDto authInfo, WxResource resource, Date now);

    /**
     * 获取当前用户（AuthInfoDto）的当前有效的有效期列表中总的有效期，不存在返回null。
     * @param authInfo
     * @param resource
     * @return
     */
    Date getAuthResourceExp(AuthInfoDto authInfo, WxResource resource, Date now);
}
