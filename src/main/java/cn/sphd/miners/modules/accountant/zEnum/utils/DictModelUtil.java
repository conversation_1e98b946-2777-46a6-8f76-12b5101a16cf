package cn.sphd.miners.modules.accountant.zEnum.utils;

import cn.sphd.miners.modules.accountant.zEnum.bean.DictModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by 刘洪涛 on 17-3-17.
 */
public final class DictModelUtil {
	public static List<DictModel> convertEnumToDictModel(EnumValue[] arrays) {
		List<DictModel> list = new ArrayList<DictModel>();
		for (EnumValue status : arrays) {
			DictModel dictModel = new DictModel();
			dictModel.setDictCode(String.valueOf(status.getIndex()));
			dictModel.setDictName(status.getName());
			list.add(dictModel);
		}

		return list;
	}
}
