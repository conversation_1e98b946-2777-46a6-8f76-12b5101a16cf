package cn.sphd.miners.modules.publicInformation.dto;

import javax.persistence.Column;
import java.util.Date;

public class PiFilePageMes {

    private Integer id;
    private String name;
    private String version;
    private long changeNum;
    private String createName;
    private Date createDate;
    private String updateName;
    private Date updateDate;
    private Byte type;
    private String path;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public long getChangeNum() {
        return changeNum;
    }

    public void setChangeNum(long changeNum) {
        this.changeNum = changeNum;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public PiFilePageMes(){

    }

    public PiFilePageMes(Integer id, String name, String version, long changeNum, String createName, Date createDate, String updateName, Date updateDate, Byte type, String path) {
        this.id = id;
        this.name = name;
        this.version = version;
        this.changeNum = changeNum;
        this.createName = createName;
        this.createDate = createDate;
        this.updateName = updateName;
        this.updateDate = updateDate;
        this.type = type;
        this.path = path;
    }
}
