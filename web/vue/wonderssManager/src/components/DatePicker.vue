<!-- type='year' 只选择年份 -->
<!-- type='year-month' 选择年月 -->
<!-- type='date' 选择日期 -->
<!-- type='time' 选择时间 -->
<!-- type='time-second' 选择时间（秒） -->

<template>
  <div class="date-picker-overlay" @click.self="onCancel">
    <div class="date-picker-panel">
      <div class="picker-header">
        <span @click="onCancel">取消</span>
        <span class="title">选择日期</span>
        <span @click="onConfirm">确定</span>
      </div>
      <div class="picker-content">
        <div class="picker-indicator"></div>
        <div class="picker-columns">
          <ul v-if="showYearColumn"
              class="picker-column year-column"
              ref="yearList"
              @touchstart.prevent="startScroll('year', $event)"
              @touchmove.prevent="onScroll('year', $event)"
              @touchend.prevent="endScroll('year')"
              :style="yearScrollStyle"
          >
<!--        @touchstart.prevent="startScroll('year', $event)"
            @touchmove.prevent="onScroll('year', $event)"
            @touchend.prevent="endScroll('year')"
            -->

              <li v-for="n in 2" :key="`top-pad-year-${n}`" class="padding-item"></li>
              <li v-for="year in years" :key="year" :class="{ active: year === displayYear }">{{ year }}年</li>
              <li v-for="n in 2" :key="`bottom-pad-year-${n}`" class="padding-item"></li>

          </ul>
          <ul v-if="showMonthColumn"
              class="picker-column month-column"
              ref="monthList"
              @touchstart.prevent="startScroll('month', $event)"
              @touchmove.prevent="onScroll('month', $event)"
              @touchend.prevent="endScroll('month')"
              :style="monthScrollStyle"
          >
<!--        @touchstart.prevent="startScroll('month', $event)"
            @touchmove.prevent="onScroll('month', $event)"
            @touchend.prevent="endScroll('month')"-->

            <li v-for="n in 2" :key="`top-pad-month-${n}`" class="padding-item"></li>
            <li v-for="month in months" :key="month" :class="{ active: month === displayMonth }">
              {{ String(month).padStart(2, '0') }}月
            </li>
            <li v-for="n in 2" :key="`bottom-pad-month-${n}`" class="padding-item"></li>
          </ul>
          <ul v-if="showDayColumn"
              class="picker-column day-column"
              ref="dayList"
              @touchstart.prevent="startScroll('day', $event)"
              @touchmove.prevent="onScroll('day', $event)"
              @touchend.prevent="endScroll('day')"
              :style="dayScrollStyle"
          >
<!--        @touchstart.prevent="startScroll('day', $event)"
            @touchmove.prevent="onScroll('day', $event)"
            @touchend.prevent="endScroll('day')"
            -->

            <li v-for="n in 2" :key="`top-pad-day-${n}`" class="padding-item"></li>
            <li v-for="day in days" :key="day" :class="{ active: day === displayDay }">{{ String(day).padStart(2, '0')}}日
            </li>
            <li v-for="n in 2" :key="`bottom-pad-day-${n}`" class="padding-item"></li>
          </ul>
          <ul v-if="showHourColumn"
              class="picker-column hour-column"
              ref="hourList"
              @touchstart.prevent="startScroll('hour', $event)"
              @touchmove.prevent="onScroll('hour', $event)"
              @touchend.prevent="endScroll('hour')"
              :style="hourScrollStyle"
          >
            <li v-for="n in 2" :key="`top-pad-hour-${n}`" class="padding-item"></li>
            <li v-for="hour in hours" :key="hour" :class="{ active: hour === displayHour }">
              {{ String(hour).padStart(2, '0') }}时
            </li>
            <li v-for="n in 2" :key="`bottom-pad-hour-${n}`" class="padding-item"></li>
          </ul>
          <ul v-if="showMinuteColumn"
              class="picker-column minute-column"
              ref="minuteList"
              @touchstart.prevent="startScroll('minute', $event)"
              @touchmove.prevent="onScroll('minute', $event)"
              @touchend.prevent="endScroll('minute')"
              :style="minuteScrollStyle"
          >
            <li v-for="n in 2" :key="`top-pad-minute-${n}`" class="padding-item"></li>
            <li v-for="minute in minutes" :key="minute" :class="{ active: minute === displayMinute }">
              {{ String(minute).padStart(2, '0') }}分
            </li>
            <li v-for="n in 2" :key="`bottom-pad-minute-${n}`" class="padding-item"></li>
          </ul>
          <ul v-if="showSecondColumn"
              class="picker-column second-column"
              ref="secondList"
              @touchstart.prevent="startScroll('second', $event)"
              @touchmove.prevent="onScroll('second', $event)"
              @touchend.prevent="endScroll('second')"
              :style="secondScrollStyle"
          >
            <li v-for="n in 2" :key="`top-pad-second-${n}`" class="padding-item"></li>
            <li v-for="second in seconds" :key="second" :class="{ active: second === displaySecond }">
              {{ String(second).padStart(2, '0') }}秒
            </li>
            <li v-for="n in 2" :key="`bottom-pad-second-${n}`" class="padding-item"></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const ITEM_HEIGHT = 36;
const VISIBLE_ITEMS = 5;

export default {
  name: 'DatePicker',
  props: {
    initialYear: {
      type: Number,
      default: new Date().getFullYear()
    },
    initialMonth: {
      type: Number,
      default: new Date().getMonth() + 1
    },
    initialDay: {
      type: Number,
      default: new Date().getDate()
    },
    initialHour: {
      type: Number,
      default: new Date().getHours()
    },
    initialMinute: {
      type: Number,
      default: new Date().getMinutes()
    },
    initialSecond: {
      type: Number,
      default: new Date().getSeconds()
    },
    minYear: {
      type: Number,
      default: new Date().getFullYear() - 50
    },
    maxYear: {
      type: Number,
      default: new Date().getFullYear()
    },
    type: {
      type: String,
      default: 'year-month',
      validator: (value) => {
        return ['year', 'year-month', 'date', 'time', 'time-second'].includes(value);
      }
    }
  },
  emits: ['confirm', 'cancel'],
  data() {
    const years = [];
    for (let y = this.minYear; y <= this.maxYear; y++) {
      years.push(y);
    }

    const initialDate = new Date();

    const currentYear = this.initialYear ?? initialDate.getFullYear();
    const currentMonth = this.initialMonth ?? (initialDate.getMonth() + 1);
    const daysInInitialMonth = new Date(currentYear, currentMonth, 0).getDate();
    const currentDay = Math.min(this.initialDay ?? initialDate.getDate(), daysInInitialMonth);
    const currentHour = Math.max(0, Math.min(23, this.initialHour ?? initialDate.getHours()));
    const currentMinute = Math.max(0, Math.min(59, this.initialMinute ?? initialDate.getMinutes()));
    const currentSecond = Math.max(0, Math.min(59, this.initialSecond ?? initialDate.getSeconds()));

    return {
      years: years, // 年份数据列表 [minYear, ..., maxYear]
      months: Array.from({length: 12}, (_, i) => i + 1), // 月份数据列表 [1, ..., 12]
      hours: Array.from({length: 24}, (_, i) => i), // 小时数据列表 [0, ..., 23]
      minutes: Array.from({length: 60}, (_, i) => i), // 分钟数据列表 [0, ..., 59]
      seconds: Array.from({length: 60}, (_, i) => i), // 秒钟数据列表 [0, ..., 59]

      // --- 选中值状态  ---
      selectedYear: currentYear,
      selectedMonth: currentMonth,
      selectedDay: currentDay,
      selectedHour: currentHour,
      selectedMinute: currentMinute,
      selectedSecond: currentSecond,

      // --- 显示值状态  ---
      displayYear: currentYear,
      displayMonth: currentMonth,
      displayDay: currentDay,
      displayHour: currentHour,
      displayMinute: currentMinute,
      displaySecond: currentSecond,

      // --- 滚动相关状态 ---
      dragging: false, // 标记是否正在拖拽滚动
      startY: 0,       // 触摸开始时的 Y 坐标
      startTranslateY: {year: 0, month: 0, day: 0, hour: 0, minute: 0, second: 0},
      currentTranslateY: {year: 0, month: 0, day: 0, hour: 0, minute: 0, second: 0},
      currentScrollType: null,

      // --- 动画相关状态 ---
      isAnimating: {year: false, month: false, day: false, hour: false, minute: false, second: false},
    };
  },
  computed: {
    title() {
      switch (this.type) {
        case 'year':
          return '选择年份';
        case 'year-month':
          return '选择年月';
        case 'date':
          return '选择日期';
        case 'time':
          return '选择时间';
        case 'time-second':
          return '选择时间'; // time 和 time-second 可以用相同标题
        default:
          return '选择'; // 默认标题
      }
    },
    // --- 列显示控制 ---
    showYearColumn() {
      return ['year', 'year-month', 'date'].includes(this.type);
    },
    showMonthColumn() {
      return ['year-month', 'date'].includes(this.type);
    },
    showDayColumn() {
      return ['date'].includes(this.type);
    },
    showHourColumn() {
      return ['time', 'time-second'].includes(this.type);
    },
    showMinuteColumn() {
      return ['time', 'time-second'].includes(this.type);
    },
    showSecondColumn() {
      return ['time-second'].includes(this.type);
    },

    // --- 基础计算属性 ---
    itemHeight() {
      return ITEM_HEIGHT;
    },
    visibleHeight() {
      return this.itemHeight * VISIBLE_ITEMS;
    },

    // --- 滚动样式计算 ---
    yearScrollStyle() {
      return {
        transform: `translateY(${this.currentTranslateY.year}px)`,
        transition: this.isAnimating.year ? 'transform 0.3s ease-out' : 'none',
      };
    },
    monthScrollStyle() {
      return {
        transform: `translateY(${this.currentTranslateY.month}px)`,
        transition: this.isAnimating.month ? 'transform 0.3s ease-out' : 'none',
      };
    },
    dayScrollStyle() {
      return {
        transform: `translateY(${this.currentTranslateY.day}px)`,
        transition: this.isAnimating.day ? 'transform 0.3s ease-out' : 'none',
      };
    },
    hourScrollStyle() {
      return {
        transform: `translateY(${this.currentTranslateY.hour}px)`,
        transition: this.isAnimating.hour ? 'transform 0.3s ease-out' : 'none',
      };
    },
    minuteScrollStyle() {
      return {
        transform: `translateY(${this.currentTranslateY.minute}px)`,
        transition: this.isAnimating.minute ? 'transform 0.3s ease-out' : 'none',
      };
    },
    secondScrollStyle() {
      return {
        transform: `translateY(${this.currentTranslateY.second}px)`,
        transition: this.isAnimating.second ? 'transform 0.3s ease-out' : 'none',
      };
    },
    days() {
      // 看本月有多少天
      const year = this.displayYear;
      const month = this.displayMonth;
      const daysInMonth = new Date(year, month, 0).getDate();
      return Array.from({length: daysInMonth}, (_, i) => i + 1); // 生成 [1, ..., daysInMonth]
    },
  },
  watch: {
    // --- 监听 initial* prop 的变化 ---
    initialYear(newVal) {
      this.selectedYear = newVal;
      this.displayYear = newVal;
      if (this.showYearColumn) this.scrollToSelected('year', false);
    },
    initialMonth(newVal) {
      const validMonth = Math.max(1, Math.min(12, newVal)); // 保证月份在 1-12
      this.selectedMonth = validMonth;
      this.displayMonth = validMonth;
      if (this.showMonthColumn) this.scrollToSelected('month', false);
    },
    initialDay(newVal) {
      const daysInCurrentMonth = new Date(this.displayYear, this.displayMonth, 0).getDate();
      const validDay = Math.max(1, Math.min(daysInCurrentMonth, newVal));
      this.selectedDay = validDay;
      this.displayDay = validDay;
      if (this.showDayColumn) this.scrollToSelected('day', false);
    },
    initialHour(newVal) {
      const validHour = Math.max(0, Math.min(23, newVal));
      this.selectedHour = validHour;
      this.displayHour = validHour;
      if (this.showHourColumn) this.scrollToSelected('hour', false);
    },
    initialMinute(newVal) {
      const validMinute = Math.max(0, Math.min(59, newVal));
      this.selectedMinute = validMinute;
      this.displayMinute = validMinute;
      if (this.showMinuteColumn) this.scrollToSelected('minute', false);
    },
    initialSecond(newVal) {
      const validSecond = Math.max(0, Math.min(59, newVal));
      this.selectedSecond = validSecond;
      this.displaySecond = validSecond;
      if (this.showSecondColumn) this.scrollToSelected('second', false);
    },

    // --- 监听 display* 值的变化，用于联动 ---
    displayYear() {
      if (this.showDayColumn) this.$nextTick(this.adjustDay);
    },
    displayMonth() {
      if (this.showDayColumn) this.$nextTick(this.adjustDay);
    },

    type: {
      handler() {
        this.resetScrollStates(); // 重置所有列的滚动状态
        this.initializeValues(); // 根据新的 type 和 props 重新初始化 selected/display 值
        this.$nextTick(() => { // 等待 DOM 更新 (v-if 生效)
          this.initializeScrollPositions(); // 滚动到新的初始位置
        });
      },
      immediate: false // 不在组件挂载时立即执行 (mounted 中已处理)
    }
  },
  mounted() {
    // 执行初始化流程
    this.resetScrollStates();
    this.initializeValues();
    this.$nextTick(this.initializeScrollPositions);
  },
  methods: {
    resetScrollStates() {
      // 定义所有列的默认滚动和动画状态
      const defaultState = {year: 0, month: 0, day: 0, hour: 0, minute: 0, second: 0};
      const defaultAnimating = {year: false, month: false, day: false, hour: false, minute: false, second: false};
      // 使用对象扩展符创建新对象，避免引用问题
      this.startTranslateY = {...defaultState};
      this.currentTranslateY = {...defaultState};
      this.isAnimating = {...defaultAnimating};
    },

    initializeValues() {
      const initialDate = new Date(); // 获取当前时间作为备用默认值
      // 使用空值合并运算符 (??) 优先取 prop 值
      this.selectedYear = this.initialYear ?? initialDate.getFullYear();
      this.selectedMonth = this.initialMonth ?? (initialDate.getMonth() + 1);
      // 计算初始年月的天数，以验证初始日期
      const daysInInitialMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
      this.selectedDay = Math.min(this.initialDay ?? initialDate.getDate(), daysInInitialMonth);
      // 初始化时间，并确保在有效范围内
      this.selectedHour = Math.max(0, Math.min(23, this.initialHour ?? initialDate.getHours()));
      this.selectedMinute = Math.max(0, Math.min(59, this.initialMinute ?? initialDate.getMinutes()));
      this.selectedSecond = Math.max(0, Math.min(59, this.initialSecond ?? initialDate.getSeconds()));

      // 将初始化后的 selected* 值同步到 display* 值
      this.displayYear = this.selectedYear;
      this.displayMonth = this.selectedMonth;
      this.displayDay = this.selectedDay;
      this.displayHour = this.selectedHour;
      this.displayMinute = this.selectedMinute;
      this.displaySecond = this.selectedSecond;
    },

    initializeScrollPositions() {
      // 对每个可能显示的列，检查它是否应该显示 (show*Column) 并且对应的 DOM 元素已存在
      if (this.showYearColumn && this.$refs.yearList) this.scrollToSelected('year', false); // false 表示非动画滚动
      if (this.showMonthColumn && this.$refs.monthList) this.scrollToSelected('month', false);
      if (this.showDayColumn && this.$refs.dayList) this.scrollToSelected('day', false);
      if (this.showHourColumn && this.$refs.hourList) this.scrollToSelected('hour', false);
      if (this.showMinuteColumn && this.$refs.minuteList) this.scrollToSelected('minute', false);
      if (this.showSecondColumn && this.$refs.secondList) this.scrollToSelected('second', false);
    },

    onCancel() {
      this.$emit('cancel', false); // 触发 cancel 事件
    },
    onConfirm() {
      // 创建结果对象
      const result = {};
      // 根据当前显示的列，将 display* 的值赋给 selected*，并添加到 result 对象
      if (this.showYearColumn) {
        this.selectedYear = this.displayYear;
        result.year = this.selectedYear;
      }
      if (this.showMonthColumn) {
        this.selectedMonth = this.displayMonth;
        result.month = this.selectedMonth;
      }
      if (this.showDayColumn) {
        this.selectedDay = this.displayDay;
        result.day = this.selectedDay;
      }
      if (this.showHourColumn) {
        this.selectedHour = this.displayHour;
        result.hour = this.selectedHour;
      }
      if (this.showMinuteColumn) {
        this.selectedMinute = this.displayMinute;
        result.minute = this.selectedMinute;
      }
      if (this.showSecondColumn) {
        this.selectedSecond = this.displaySecond;
        result.second = this.selectedSecond;
      }
      this.$emit('confirm', result);
    },

    adjustDay() {
      // 此方法仅在日期列显示时有意义
      if (!this.showDayColumn) return;

      // 获取当前显示年月对应的最大天数 (依赖于 days 计算属性)
      const currentMaxDay = this.days.length;
      // 如果当前显示的日期大于该月的最大天数
      if (this.displayDay > currentMaxDay) {
        // 将显示日期调整为最大天数
        this.displayDay = currentMaxDay;
        // 使用 $nextTick 确保 days 计算属性和 DOM 更新完成后再滚动
        this.$nextTick(() => {
          // 滚动到调整后的有效日期 (带动画)
          this.scrollTo('day', this.displayDay, true);
        });
      } else {
        // 如果日期本身有效，但列表可能已变化（例如从2月切到3月），
        // 检查当前滚动位置是否正确对应 displayDay，如果不正确则进行校准滚动。
        // 这可以防止在月份切换时，日期列看起来"跳动"一下。
        this.$nextTick(() => {
          const list = this.getListByType('day');
          if (!list) return;
          const currentIndex = list.indexOf(this.displayDay);
          if (currentIndex !== -1) {
            const expectedTranslateY = -currentIndex * this.itemHeight;
            // 如果实际偏移量与预期偏移量差距较大，则进行校准滚动
            if (Math.abs(this.currentTranslateY.day - expectedTranslateY) > 1) { // 允许微小误差
              this.scrollTo('day', this.displayDay, true); // 使用动画校准
            }
          }
        });
      }
    },

    scrollTo(columnType, targetValue, animated = true) {
      const list = this.getListByType(columnType);
      if (!list) return;

      const index = list.indexOf(targetValue);
      if (index === -1) return;

      const targetTranslateY = -index * this.itemHeight;

      if (this.isAnimating[columnType] === undefined) this.isAnimating[columnType] = false;
      if (this.currentTranslateY[columnType] === undefined) this.currentTranslateY[columnType] = 0;

      this.isAnimating[columnType] = animated;
      this.currentTranslateY[columnType] = targetTranslateY;
      this.updateDisplayValue(columnType, targetValue);

      if (animated) {
        setTimeout(() => {
          this.isAnimating[columnType] = false;
        }, 300);
      } else {
        this.isAnimating[columnType] = false;
      }
    },

    scrollToSelected(columnType, animated = true) {
      const value = this.getSelectedValueByType(columnType); // 获取该列的选中值
      if (value !== null) { // 确保值有效
        this.scrollTo(columnType, value, animated);
      }
    },

    getListByType(columnType) {
      switch (columnType) {
        case 'year':
          return this.years;
        case 'month':
          return this.months;
        case 'day':
          return this.days; // 注意：返回的是 computed property
        case 'hour':
          return this.hours;
        case 'minute':
          return this.minutes;
        case 'second':
          return this.seconds;
        default:
          return null;
      }
    },

    getSelectedValueByType(columnType) {
      switch (columnType) {
        case 'year':
          return this.selectedYear;
        case 'month':
          return this.selectedMonth;
        case 'day':
          return this.selectedDay;
        case 'hour':
          return this.selectedHour;
        case 'minute':
          return this.selectedMinute;
        case 'second':
          return this.selectedSecond;
        default:
          return null;
      }
    },

    updateDisplayValue(columnType, value) {
      switch (columnType) {
        case 'year':
          this.displayYear = value;
          break;
        case 'month':
          this.displayMonth = value;
          break;
        case 'day':
          this.displayDay = value;
          break;
        case 'hour':
          this.displayHour = value;
          break;
        case 'minute':
          this.displayMinute = value;
          break;
        case 'second':
          this.displaySecond = value;
          break;
      }
    },
    startScroll(columnType, event) {

      // 如果当前列正在执行动画，则中断动画
      if (this.isAnimating[columnType]) {
        try {
          // 尝试获取当前元素的实时 transform 值作为动画中断点
          const listElement = this.$refs[`${columnType}List`];
          const matrix = window.getComputedStyle(listElement).transform;
          const translateY = matrix === 'none' ? 0 : parseFloat(matrix.split(',')[5]);
          this.currentTranslateY[columnType] = translateY;
          this.isAnimating[columnType] = false;
        } catch (e) {
          // 如果获取样式失败 (例如元素隐藏)，则使用 data 中的当前值
          console.warn(`Could not get computed style for ${columnType}, using currentTranslateY.`);
        }
        this.isAnimating[columnType] = false; // 清除动画标志
      }
      // 标记开始拖拽
      this.dragging = true;
      // 记录当前滚动的列类型
      this.currentScrollType = columnType;
      // 记录触摸起始点的 Y 坐标
      this.startY = event.touches ? event.touches[0].clientY : event.clientY;
      // 确保 startTranslateY 对象存在该列的键
      if (this.startTranslateY[columnType] === undefined) {
        this.startTranslateY[columnType] = 0;
      }
      // 记录触摸开始时该列的 translateY 值
      this.startTranslateY[columnType] = this.currentTranslateY[columnType];
    },

    onScroll(columnType, event) {
      if (!this.dragging || this.currentScrollType !== columnType) return;
      const clientY = event.touches ? event.touches[0].clientY : event.clientY;
      const deltaY = clientY - this.startY;
      let newTranslateY = this.startTranslateY[columnType] + deltaY;
      const maxTranslate = this.getMaxTranslateByType(columnType); // 获取上边界 (通常是 0)
      const minTranslate = this.getMinTranslateByType(columnType); // 获取下边界
      const buffer = this.itemHeight * 1.5; // 设置一个弹性缓冲区大小

      if (newTranslateY > maxTranslate + buffer) {
        newTranslateY = maxTranslate + buffer;
      } else if (newTranslateY < minTranslate - buffer) {
        newTranslateY = minTranslate - buffer;
      }
      this.currentTranslateY[columnType] = newTranslateY;
      const list = this.getListByType(columnType);
      if (!list) return;
      const maxIndex = list.length - 1;
      let activeIndex = Math.round(-newTranslateY / this.itemHeight);
      activeIndex = Math.max(0, Math.min(activeIndex, maxIndex));
      const currentValue = list[activeIndex];
      this.updateDisplayValue(columnType, currentValue);
    },

    endScroll(columnType) {
      // 如果未开始拖拽或当前滚动的不是此列，则忽略
      if (!this.dragging || this.currentScrollType !== columnType) return;
      this.dragging = false; // 标记拖拽结束

      const list = this.getListByType(columnType);
      if (!list) return;
      const maxIndex = list.length - 1;
      const currentTranslate = this.currentTranslateY[columnType]; // 获取当前的 translateY

      let targetIndex = Math.round(-currentTranslate / this.itemHeight);
      targetIndex = Math.max(0, Math.min(targetIndex, maxIndex));
      const targetValue = list[targetIndex]; // 获取目标索引对应的值

      // 使用 scrollTo 方法将列滚动到计算出的目标值 (带动画)
      this.scrollTo(columnType, targetValue, true);

      // 清除当前滚动类型记录
      this.currentScrollType = null;
    },

    // --- 滚动边界辅助方法 ---

    getMaxTranslateByType(columnType) {
      // 对所有列来说，上边界都是 0
      return 0;
    },

    getMinTranslateByType(columnType) {
      const list = this.getListByType(columnType);
      if (!list || list.length === 0) return 0; // 如果列表为空，则边界为 0
      // 最小偏移量 = -(列表长度 - 1) * 项目高度
      // 例如，列表有 10 项，索引是 0-9，最后一项居中时，索引 9 的项需要移动到中心，
      // 中心线偏移是 0，所以需要向上移动 9 * itemHeight 的距离。
      return -(list.length - 1) * this.itemHeight;
    }

  },
};
</script>

<style scoped lang="scss">
$item-height: 36px; // 每个滚动项的高度
$visible-items: 5; // 可见区域显示的项目数量 (奇数)
$picker-height: $item-height * $visible-items; // 选择器内容区域的总高度
$indicator-color: #e0e0e0; // 中间选中指示器的边框颜色
$active-text-color: #333; // 选中项的文字颜色
$normal-text-color: #999; // 非选中项的文字颜色
$highlight-text-color: #409eff; // 顶部操作按钮 (取消/确定) 的文字颜色
$border-color: #f0f0f0; // 面板内部分隔线的颜色

.date-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 9999;
  overflow: hidden;
  touch-action: none;
  -webkit-overflow-scrolling: auto; /* 禁用 iOS 弹性滚动 */
  overscroll-behavior: contain;
}

.date-picker-panel {
  width: 100%;
  background: #fff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid $border-color;
  font-size: 16px;

  span {
    color: $highlight-text-color;
  }

  .title {
    font-weight: bold;
    color: #333;
    font-size: 17px;
  }

  span:first-child {
    color: #666;
  }
}

.picker-content {
  position: relative;
  height: $picker-height;
  overflow: hidden;
  display: flex;
  justify-content: center;
  width: 100%;
}

.picker-indicator {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: $item-height;
  border-top: 1px solid $indicator-color;
  border-bottom: 1px solid $indicator-color;
  z-index: 998; /* 在列的下方 (列 z-index 为 2) */
  pointer-events: none;
}

.picker-columns {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 999; /* 在指示器上方 */
  //background-color: #00aae7;
}

.picker-column {
  height: 99999999px;
  list-style: none;
  margin: 0;
  padding: 0;
  text-align: center;
  font-size: 16px;
  flex: 1;
  max-width: 150px;
  position: relative; /* 由 static → relative */
  will-change: top; /* 提示浏览器优化 top 动画 */
  li {
    height: $item-height;
    line-height: $item-height;
    color: $normal-text-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.3s, font-size 0.3s, font-weight 0.3s;

    &.padding-item {
      height: $item-height;
    }
  }

  li.active {
    font-weight: bold;
    color: $active-text-color;
    font-size: 18px;
  }
}



</style>