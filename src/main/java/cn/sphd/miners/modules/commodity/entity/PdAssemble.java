package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_pd_assemble")
public class PdAssemble {
    private Integer id;
    private Integer org;
    private Integer product;
    private Long directPartProduct;
    private Long indirectPartProduct;
    private Long directPartComposition;
    private Long formerDirectPartProduct;
    private Long formerIndirectPartProduct;
    private Long formerdirectPartComposition;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }
    @Basic
    @Column(name = "product")
    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    @Basic
    @Column(name = "direct_part_product")
    public Long getDirectPartProduct() {
        return directPartProduct;
    }

    public void setDirectPartProduct(Long directPartProduct) {
        this.directPartProduct = directPartProduct;
    }

    @Basic
    @Column(name = "indirect_part_product")
    public Long getIndirectPartProduct() {
        return indirectPartProduct;
    }

    public void setIndirectPartProduct(Long indirectPartProduct) {
        this.indirectPartProduct = indirectPartProduct;
    }

    @Basic
    @Column(name = "direct_part_composition")
    public Long getDirectPartComposition() {
        return directPartComposition;
    }

    public void setDirectPartComposition(Long directPartComposition) {
        this.directPartComposition = directPartComposition;
    }

    @Basic
    @Column(name = "former_direct_part_product")
    public Long getFormerDirectPartProduct() {
        return formerDirectPartProduct;
    }

    public void setFormerDirectPartProduct(Long formerDirectPartProduct) {
        this.formerDirectPartProduct = formerDirectPartProduct;
    }

    @Basic
    @Column(name = "former_indirect_part_product")
    public Long getFormerIndirectPartProduct() {
        return formerIndirectPartProduct;
    }

    public void setFormerIndirectPartProduct(Long formerIndirectPartProduct) {
        this.formerIndirectPartProduct = formerIndirectPartProduct;
    }

    @Basic
    @Column(name = "formerdirect_part_composition")
    public Long getFormerdirectPartComposition() {
        return formerdirectPartComposition;
    }

    public void setFormerdirectPartComposition(Long formerdirectPartComposition) {
        this.formerdirectPartComposition = formerdirectPartComposition;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PdAssemble that = (PdAssemble) o;
        return id == that.id && Objects.equals(org, that.org) && Objects.equals(directPartProduct, that.directPartProduct) && Objects.equals(indirectPartProduct, that.indirectPartProduct) && Objects.equals(directPartComposition, that.directPartComposition) && Objects.equals(formerDirectPartProduct, that.formerDirectPartProduct) && Objects.equals(formerIndirectPartProduct, that.formerIndirectPartProduct) && Objects.equals(formerdirectPartComposition, that.formerdirectPartComposition) && Objects.equals(memo, that.memo) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, directPartProduct, indirectPartProduct, directPartComposition, formerDirectPartProduct, formerIndirectPartProduct, formerdirectPartComposition, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
