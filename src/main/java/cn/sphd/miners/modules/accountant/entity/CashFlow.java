package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;
import java.sql.Timestamp;

/**
 * Created by khmsoft on 2017/5/16.
 */
@Alias("cashflow")
public class CashFlow {
    private int id;
    private Integer org;
    private String period;
    private String type;
    private Date beginDate;
    private Date endDate;
    private String cellCode;
    private BigDecimal accumulative;
    private BigDecimal amount;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Timestamp updateDate;
    private Integer approveItem;
    private String approveStatus;
    private Integer approveLevel;
    private Integer auditor;
    private String auditorName;
    private Timestamp auditDate;
    private String operation;
    private String applyMemo;
    private String approveMemo;
    private Integer messageId;
    private String state;

    @Id
    @Column(name = "id", nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org", nullable = true)
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "period", nullable = true, length = 8)
    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    @Basic
    @Column(name = "type", nullable = true, length = 1)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Basic
    @Column(name = "begin_date", nullable = true)
    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    @Basic
    @Column(name = "end_date", nullable = true)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Basic
    @Column(name = "cell_code", nullable = true, length = 20)
    public String getCellCode() {
        return cellCode;
    }

    public void setCellCode(String cellCode) {
        this.cellCode = cellCode;
    }

    @Basic
    @Column(name = "accumulative", nullable = true, precision = 2)
    public BigDecimal getAccumulative() {
        return accumulative;
    }

    public void setAccumulative(BigDecimal accumulative) {
        this.accumulative = accumulative;
    }

    @Basic
    @Column(name = "amount", nullable = true, precision = 2)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Basic
    @Column(name = "memo", nullable = true, length = 255)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator", nullable = true)
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name", nullable = true, length = 100)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator", nullable = true)
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name", nullable = true, length = 100)
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date", nullable = true)
    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "approve_Item", nullable = true)
    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    @Basic
    @Column(name = "approve_status", nullable = true, length = 1)
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Basic
    @Column(name = "approve_level", nullable = true)
    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    @Basic
    @Column(name = "auditor", nullable = true)
    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    @Basic
    @Column(name = "auditor_name", nullable = true, length = 100)
    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    @Basic
    @Column(name = "audit_date", nullable = true)
    public Timestamp getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Timestamp auditDate) {
        this.auditDate = auditDate;
    }

    @Basic
    @Column(name = "operation", nullable = true, length = 1)
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "apply_memo", nullable = true, length = 255)
    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    @Basic
    @Column(name = "approve_memo", nullable = true, length = 255)
    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    @Basic
    @Column(name = "message_id", nullable = true)
    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    @Basic
    @Column(name = "state", nullable = true, length = 1)
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CashFlow cashFlow = (CashFlow) o;

        if (id != cashFlow.id) return false;
        if (org != null ? !org.equals(cashFlow.org) : cashFlow.org != null) return false;
        if (period != null ? !period.equals(cashFlow.period) : cashFlow.period != null) return false;
        if (type != null ? !type.equals(cashFlow.type) : cashFlow.type != null) return false;
        if (beginDate != null ? !beginDate.equals(cashFlow.beginDate) : cashFlow.beginDate != null) return false;
        if (endDate != null ? !endDate.equals(cashFlow.endDate) : cashFlow.endDate != null) return false;
        if (cellCode != null ? !cellCode.equals(cashFlow.cellCode) : cashFlow.cellCode != null) return false;
        if (accumulative != null ? !accumulative.equals(cashFlow.accumulative) : cashFlow.accumulative != null)
            return false;
        if (amount != null ? !amount.equals(cashFlow.amount) : cashFlow.amount != null) return false;
        if (memo != null ? !memo.equals(cashFlow.memo) : cashFlow.memo != null) return false;
        if (creator != null ? !creator.equals(cashFlow.creator) : cashFlow.creator != null) return false;
        if (createName != null ? !createName.equals(cashFlow.createName) : cashFlow.createName != null) return false;
        if (createDate != null ? !createDate.equals(cashFlow.createDate) : cashFlow.createDate != null) return false;
        if (updator != null ? !updator.equals(cashFlow.updator) : cashFlow.updator != null) return false;
        if (updateName != null ? !updateName.equals(cashFlow.updateName) : cashFlow.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(cashFlow.updateDate) : cashFlow.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(cashFlow.approveItem) : cashFlow.approveItem != null)
            return false;
        if (approveStatus != null ? !approveStatus.equals(cashFlow.approveStatus) : cashFlow.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(cashFlow.approveLevel) : cashFlow.approveLevel != null)
            return false;
        if (auditor != null ? !auditor.equals(cashFlow.auditor) : cashFlow.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(cashFlow.auditorName) : cashFlow.auditorName != null)
            return false;
        if (auditDate != null ? !auditDate.equals(cashFlow.auditDate) : cashFlow.auditDate != null) return false;
        if (operation != null ? !operation.equals(cashFlow.operation) : cashFlow.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(cashFlow.applyMemo) : cashFlow.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(cashFlow.approveMemo) : cashFlow.approveMemo != null)
            return false;
        if (messageId != null ? !messageId.equals(cashFlow.messageId) : cashFlow.messageId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (period != null ? period.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (beginDate != null ? beginDate.hashCode() : 0);
        result = 31 * result + (endDate != null ? endDate.hashCode() : 0);
        result = 31 * result + (cellCode != null ? cellCode.hashCode() : 0);
        result = 31 * result + (accumulative != null ? accumulative.hashCode() : 0);
        result = 31 * result + (amount != null ? amount.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        return result;
    }
}
