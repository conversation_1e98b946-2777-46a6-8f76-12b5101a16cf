
.red{ color: #ed5565!important;  }
.bluetip{ color: #5d9cec; font-size: 0.8em;
    display: block!important;
    margin: 0!important;
    width: 100%!important;
}
.tip{ text-align: center;  }
.tip, .preface{ color: #df7234; font-size: 14px; padding:15px; text-indent:2em; background: #fff;white-space: pre-wrap; }
.linkBtn{ color: #5d9cec; font-weight: bold; font-size: 0.9em; cursor: default; }
.linkBtn:hover{ text-decoration: underline;  }
.radioArea span{ vertical-align: top; display: inline-block; margin-right:20px;width: 46%; line-height: 26px;cursor: default;  }
.radioArea span .fa{ color:#5d9cec; }
.type1and2{ min-height: 50px; }
.typenum{
    border: 1px solid #ccc;
    border-left: none;
    border-right: none;
    padding: 5px 15px;
    margin: 5px;
}
.typenum>span{ width: 80%; }
.clearInput{ position: relative;  }
.clearInput input, .clearInput textarea { padding-right:35px;  }
.clearInputVal,.clearTextAreaVal{ position: absolute; right: 7px; bottom:20px; background: #ddd; width: 20px; color: #aaa; text-align: center; border-radius: 21px; height: 20px; line-height: 20px; padding: 0; }
.clearInput textarea{ width:100%!important; height:54px;   }
.clearTextAreaVal{ bottom:25px!important; }
.mainCon{ max-width: 1000px; min-width:600px;  }
hr{ border-bottom: 1px solid #ccc;  }
.mainCon1,.mainCon11{ display: block;  }

.lenTip{ display: inline-block; margin-right: 50px; }
.pannel,.ty-table{ margin-top: 20px; }

#editQuestion1{ width: 670px;  }
.marTop20{ margin-top: 20px; }
.marLeft100{ margin-left: 40px; }
.marRight20{ margin-right:20px; }
.pannel .form-control,.descItem .form-control{ margin-bottom: 15px; }
select.form-control { display: inline-block; width: 80px; margin: 0 10px;   }

.pannelAdd{ position: relative; width: 590px;  }
.pannelDel{ position: absolute; right: -42px; top: 36px;      }
.searchSect button {  border-top-right-radius: 5px;border-bottom-right-radius: 5px; }
.searchSect{margin-right: 20px;}
.keywordSearch span:nth-child(1){line-height: 34px;  margin-right: 10px;}
.keywordSearch input{  padding: 0 10px 0 20px;  font-size: 12px;  min-width: 200px;  height: 31px;  line-height: 31px;}
.inputBox{position: relative;}
.inputBox i{position: absolute;width:18px; height: 18px; background: url("../technology/img/search.png") no-repeat; background-size:20px 20px;top: 6px;
    left: 5px;}

.investigation h4{ font-size: 14px;font-weight: bold; line-height: 40px; }
.investigation .memo{
    text-align: center;
    font-size: 25px;
    font-weight: 100;
}
.investigation .ttl{ text-align: center;  }
.investigation .ops{ padding: 3px 20px; color: #666; line-height: 20px;  }
.investigation .ops>div{ margin-bottom:5px;  }
.investigation .ques{ margin-bottom: 20px;  }
.investigation{
    margin: 20px auto;
    width: 90%;
    border: 1px solid #ddd;
    padding: 20px;
    box-shadow: 0 0 3px #eee;
}




/* 调查管理 */
.declare_avatar{  padding: 32px 50px;  background: #f0f0f0;  display: flex;  justify-content: space-between;  color: #333;  }
.declare_avatar p{  margin-bottom:20px;  }
.declare_avatar h4{  display: inline-block;  padding-left: 8px;  font-size: 16px;  color: #666;  font-weight: bold;
    border-left: 4px solid #5d9cec;  margin-left: -12px;  margin-bottom: 16px;  line-height:27px ;  }
.qrCode{
    text-align: center;
    padding:16px;
    color: #999;
    background-color: #fff;
    font-size:16px;
}
#qrCode_small{
    width: 250px;
    height:230px;
    display: flex;
    justify-content:center;
    align-items:center;
}
.box{
    margin: 20px 0px;
    box-shadow: 0 0 3px #ddd;
    padding: 20px;
}
.typenum{ display: none; }
table.widthTab tr td{
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}
.line{
    margin-top:20px;
}
.seeThisAnsBtn{ margin-left: 50px; }
.circleOrd{
    display: inline-block; width: 24px; height: 24px;
    line-height: 24px; border-radius: 12px;
    border: 1px solid #666;
    margin: 0 4px;
}

.blueTip{
    color:#5d9cec;
    font-size: 0.8em;
}
.manageInvBC>div{ margin-top: 20px; margin-bottom: 20px; }
.manageInvBC .fa{ color: #0b94ea }
.manageInvBC select, .manageInvBC input{ width: 85%; }
.flexcc{ display: flex }
.flexcc>div:nth-child(1){ flex:3; }
.flexcc>div:nth-child(2){ flex:1; }
.fengm{
    display: block;

}
.marTp16px>div{
    margin: 16px 0;
}
#createInv .fImg{
    width: 60px;
    height: 60px;
    overflow: hidden;
    background: #eee;
    line-height: 60px;
    text-align: center;
    border-radius: 4px;
    box-shadow: 0 0 2px #ddd;
}
#createInv .fImg img{
    display: block;  height:60px ;
    border-radius: 4px;
}
#UploadFImg .fImg{
    max-height: 400px;
    overflow: hidden;
}
#UploadFImg .imgCC img{
    display: block; width: 100%;
    border-radius: 4px;
}
.searchSect2{
    margin-right: 0!important;
    margin-top: 16px;
}
.shareViewbg{ position: relative; width: 300px; height: 500px; background-image:url("../../assets/img/wxView2.png"); background-repeat: no-repeat; background-size: 100% auto; }
#shareView .back{
    text-align: center; height: 40px; line-height: 50px;border-bottom: 1px solid #ddd; margin-bottom: 20px;
}
#shareView .backBtn{ position: absolute;  top:17px; left: 10px; font-size: 20px;   }
#shareView .ttl{ max-height: 41px; overflow:hidden; text-overflow:ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;  }
#shareView .txtCon{  border-radius: 10px; padding: 5px; background: #fff; width: 220px; position: absolute; top:100px; left: 30px; }
#shareView .txt{ font-size: 0.8em; color: #666; flex: 4; }
#shareView .fim{ display: block; width: 40px; height:40px; background: #eee; flex: 1; text-align: center; color: #666;    }
#shareView .fim{ background-image:url("../../assets/img/investing.png") ; background-size: 100% 100%; background-repeat: no-repeat;   }

#preView {  border-radius: 4px; position: relative;      }
#preViewC{ padding:10px 5px; margin-top: 40px;  }
#preView .preTtl{ line-height: 40px; background: #0b94ea; color: #fff; text-align: center; position: absolute; width: 100%; top:0  }
#preView .preTtl i.preViewD { position: absolute; left: 10px; top: 10px; color: #fff;  }
#preView .ttl{ text-align: center; font-size: 20px;  }
#preView .preface{ background:none; padding: 1px;   }
#preView h4{ font-size: 16px;  }
#qNoFa{ color: #0b94ea;  }


.ty-linkBtn:hover{
    text-decoration: underline;
}
.ty-linkBtn{
    cursor: pointer;
    color: #0b94ea; display: inline-block; padding: 3px 10px;
}
.oneLine{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    word-break: break-all !important;
    text-align: left !important;
}
.lineDot{
    width: 100px;
    display: inline-block;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    word-break: break-all !important;
}
.qAnsList .ty-color-blue:hover{ background: #0b94ea; color: #fff; cursor: pointer;  }
.qAnsList .ty-color-blue{
    border-radius: 3px;
    display: inline-block; padding: 2px 10px; margin-left: 40px;
}





