
package cn.sphd.miners.modules.material.entity;



import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Entity (name="MtMaterialStock")
@Table (name="t_mt_material_stock")
//物料_库存对照表
public class MtMaterialStock implements Serializable {

	
    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="mtStockId"   , nullable=true , unique=false)
    private Integer mtStockId;

    @Column(name="material"   , nullable=false , unique=false)
    private Integer material; 

    @Column(name="storage"   , nullable=true , unique=false)
    private Integer storage; 

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo; 

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;


    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    

    public Integer getMaterial() {
        return material;
    }
	
    public void setMaterial (Integer material) {
        this.material =  material;
    }
	

    public Integer getStorage() {
        return storage;
    }
	
    public void setStorage (Integer storage) {
        this.storage =  storage;
    }


    public Integer getMtStockId() {
        return mtStockId;
    }


    public void setMtStockId(Integer mtStockId) {
        this.mtStockId = mtStockId;
    }

    public String getMemo() {
        return memo;
    }
	
    public void setMemo (String memo) {
        this.memo =  memo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
