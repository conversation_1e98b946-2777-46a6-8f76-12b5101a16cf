<template>
  <div id="reimburseApply">
    <TY_NavTop title="报销申请" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">今天是{{nowTime | formatDay('YYYY-MM-DD dddd')}}</div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toHandle" v-bind:key="index">
          <div class="ui-cell__hd">{{item.createName | stringSplit(4)}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>申请报销 {{item.amount}} 元</div>
              <div>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'applyReimburse',
  data () {
    return {
      toHandle: [],
      listenersUid: [],
      nowTime: 0
    }
  },
  computed: {
    toHandleCount: function () {
      return this.toHandle.length
    }
  },
  created () {
    let that = this
    this.axios.get('../../../reimburseWindow/getCurrentTime.do')
      .then(function (response) {
        let time = response.data.time
        that.nowTime = time
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('personnelReimbursePend', function (data) {
        that.toHandle = JSON.parse(data)
        console.log('personnelReimbursePend session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('personnelReimbursePend', function (data) {
        console.log('applyReimburseHandle user Socket Error:')
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.toHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toHandle.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    console.log(userId)
    console.log(session)
    this.sphdSocket.send('reimburseApply', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/applyReimburseDetail/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    }
  }
}
</script>
