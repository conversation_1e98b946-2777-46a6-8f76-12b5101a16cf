input, select{ padding:0; margin:0; }
.trItem{ padding:2px 0;  }
.ttl , .con , .conInput{ display:inline-block; line-height:35px;  height:35px;         }
.ttl{ width:25%; padding-right:10px;text-align:right;       }
.con{ width:69%;position:relative; background: #fff;border-radius: 4px;    }
.conInput{ width:80%; border:1px solid #ccc; position:absolute; top:10px; left: 5px;    }
span.conInput , input.conInput{ left: 2px; padding: 0 6px; text-align:left;     }
#method_2 , #method_3{ display:none;  }
.check_ok , .check_no { padding:10px; position: relative ;top:8px;     }
.check_ok { background: url("check_ok.png") no-repeat ;   }
.check_no { background: url("check_no.png") no-repeat ;    }
td div { float:left; width:24%; text-align:left; line-height:25px;   }
input[type="checkbox"]{   display:none;    }
.approvalLevel{ margin-top: 15px; position: relative; }
.mask{ height:35px; position: absolute; top:0; width: 430px ; z-index: 1; }
ul,li{
    margin: 0;
    padding:0;
    list-style: none;
}
.hd{ display: none; }
.btn{ border:1px solid #00a0e9 ; margin-top:15px;   }
.btn:hover{ background:#eaeaea; color:#444;  }
.ttl{width: 29%;vertical-align: top}
.con{vertical-align: top; height: auto;  cursor: pointer;}
.ttl2{width: 40%;  display: inline-block; height:35px;font-size:12px; float:left; border-right:1px solid #ccc;    }
.con2{ width: 58%; height:32px!important; display: inline-block; font-size:13px; position:relative; top:-1px;   }
.conInput{
    height: 38px;
    width: 80%;
    position: relative;
    top: 0;
    left: 0;
}
.trItem2{
    margin-top: 8px;
    margin-left: -1px;
    position: relative;
}
.addItem2{
    width: 99%;
    height: 38px;
    border:1px dashed #3399ff;
    text-align: center;
    margin-top: 8px;
    color: #3399ff;
    margin-left: 1px;
    cursor: pointer;
}
.deleteItem{  width: 33px; height: 33px; display:inline-block ; line-height:28px; border:1px solid #ccc;  position: absolute; right: -40px; cursor: pointer; color: #ccc;  }
.deleteItem:hover{ border-color: #ed6b75; color: #ed6b75; transition: all .3s;  }
.approvePList{ border:1px solid #ccc; text-align: left; margin-top:8px; height: 300px; overflow-y: scroll;  }
.approvePItem{ background-color: #f2f2f2; height: 30px; border-top: 1px solid #fff; line-height:30px; vertical-align: middle; padding:0 5px; cursor: default;  }
.approvePItem>i{
    vertical-align: top;
    color: #333;
    margin: 8px 3px 0 3px;
    width: 15px;
    height: 15px;
}
.approvePItem>input{
    display: inline;
    height: auto;
    margin:0 2px 0 2px;
}
.approvePList .level2,.approvePList .level3{
    margin-left:20px;
}
.ty-roleSearch{
    position: relative;
    height: 30px;
    width: 100%;

}
.ty-roleSearch>.ty-searchInput{
    width: 99%;
    height: 30px;
    border:none;
    background-color: #e8eff8;
    -webkit-border-radius:0;
    -moz-border-radius:0;
    border-radius:0;
}
.ty-roleSearch>.ty-searchInput:focus{
    border:1px solid #5d9cec;
}
.ty-roleSearch>.ty-searchBtn{
    width: 32px;
    background: #5d9cec url("../common/theme/icon/search.png") no-repeat;
    background-position: center center;
    background-size: 20px 20px;
    position: absolute;
    top: 0;
    right: 0;
    border: none;
    line-height: 30px;
    -webkit-border-radius:0;
    -moz-border-radius:0;
    border-radius:0;
}

#tipMs{ text-align:center;  }
#roleName{ display:block; margin:15px 0; }
#editTip{  font-size:11px; color:#ed5565 ;   }
.ty-select{ width:150px; }
.ty-mainData{ padding-bottom:250px;  }
.add-person{ background: url(img/u1501.png) no-repeat; background-size:16px 16px; background-position:97% ; padding-right:20px; white-space:nowrap;overflow:hidden;text-overflow:ellipsis;    }
.trItem2{ width:99%; border:1px solid #ccc; height:35px;   }

.ty-searchBtn{ cursor:pointer ;  }
.userList .fa, .apItem .fa{ color:#5d9cec ;  }
.userList{ padding:15px;   }
.userList p{ height:30px; line-height:30px; margin:0; padding:0 15px; }
.userList p:hover{background:#eee;    }
#myApply .bonceCon{ max-height: 300px; overflow-y: auto; }
#authLevelCon select{ display:inline-block; width:100%; }

#myApply {
    width: 545px;
}
#approvList{ padding-left:30px; }
#overTime .bonceCon{ padding:30px;   }
.itemLe{ padding:10px; }
.itemLe>p{ margin: 0 }
.itemLe>p>span{ padding-left:30px; }
.applyTip{ color: #0b94ea; }
.applyTip>p{padding-left:20px; margin: 0}
.overTimeEdit{ padding:10px 15px; cursor:pointer;  }
.overTimeEdit:hover{color: #0869a8 ; }
.apItem{width:300px; margin:0 auto;    }
.apItem .chargeName:hover{ cursor: pointer; background:#eee;   }
.apItem .chargeName{ width:150px; height: 22px; float: right; border:1px solid #ccc; border-radius:3px; text-align:center; background:#fff;        }

#startDate,#startDate2,.btns, #nextStep{ display: none; }
#nextStep{ padding:0 0 0 30px;}
#finNextLevel .fa, #nextStep .fa{ color:#0b94ea;  }
#flowList{ max-height: 210px; overflow-y: auto; }
#requestLog{ width:950px; }

.leTip{ text-align: left; padding:40px 0 0 40px; text-indent:2em;   }
.payEditBtn{ padding: 0 20px 10px 30px;  }
.border{ border:1px solid #0b94ea; padding:5px; border-radius:4px;      }
/*请假 lyt*/
.ft-intend{ text-indent:2em;   }
.approver{  width: 130px;  }
.limitReq{  padding-left:80px;  }
.limitReq>p{  margin-left:93px;  }
.higher{  display: inline-block;  width:72px;}
.limitReq select,.limitReq input{width: 60px;}
.wishDay{  margin-left:30px;}
.wish{  margin-left:48px;  }
#leaveNextStep{padding-left: 110px;}
.itemSp{float:right;margin-right: 50px;width: 120px;height: 22px;border:1px solid #ccc; border-radius:3px; text-align:center; background:#fff; }
.itemSp:hover{ cursor: pointer; background:#eee;   }
.leaveSeeTip{display: none;  font-size: 12px; color: #2f75b5; margin: 0}
.leaveFlowTip{  color: #2f75b5;  font-size: 12px;  }
.txtRight{ text-align:right;  }
.txtRight>span{ margin-right:20px;   }
#flowList4{ padding: 10px; margin: 15px; }

#reimburse .applyTip{ margin-left:135px; margin-bottom:15px;  }
#reimburse .applyTip>p{ margin:0; }

.selectUcon{ position: relative; display: inline-block;  }
#userInput{ display: inline-block; width: 250px; text-align: center; }
.selectU{ width: 250px; background: #fff;display: none; max-height:140px; overflow-y:auto; position: absolute; top: 30px; border:1px solid #ddd; }
.selectU option{ text-align: center; cursor: pointer; height: 25px; line-height: 25px; border-bottom: 1px solid #ddd; }
.selectU option.active{  background: rgba(55, 138, 214, 0.16);  }
.selectU option:last-child{ border-bottom: none;  }
.selectU option:hover{ background: rgba(55, 138, 214, 0.16);  }
.area-body{margin-left: 50px;}
.area-con>div{ padding: 10px 0;}

section{
    line-height: 2;
}
.p_row{
    display: flex;
    justify-content: space-between
}


.state_normal{
    background-color: #D58E65;
    padding: 2px 4px 1px;
    border-radius: 2px;
    color: #fff;
    font-size: 12px;
}
.state_green{
    background-color: #5cb85c;
    padding: 2px 4px 1px;
    border-radius: 2px;
    color: #fff;
    font-size: 12px;
}
.state_red{
    background-color: #d55a5a;
    padding: 2px 4px 1px;
    border-radius: 2px;
    color: #fff;
    font-size: 12px;
}

.item-flex{
    display: flex;
    line-height: 1.5;
    margin-bottom: 8px;
}
.item{
    line-height: 1.5;
    margin-bottom: 8px;
}
.item-fix{
    flex: none;
}
.item-auto{
    flex: auto;
}
.item-auto input, .item-auto select{
    width: 100%;
}

.fix200{
    width: 200px;
}
.fix120{
    width: 120px;
}
.item .item-title{
    line-height: 1.8;
    margin-bottom: 4px;
}
.item input, .item select{
    width: 100%;
}




