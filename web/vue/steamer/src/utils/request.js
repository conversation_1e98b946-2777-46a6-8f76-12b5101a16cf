import axios from "axios"

//可以添加多个不同的request
const requests = {
    default: axios.create({
        method: 'POST'
    }),
    // second:axios.create()
}
console.log('requests', requests)
//所有request都需要添加auth.init中设置的默认拦截器
Object.keys(requests).forEach((name) =>{
    requests[name].interceptors.request.handlers.push(...axios.interceptors.request.handlers)
    requests[name].interceptors.response.handlers.push(...axios.interceptors.response.handlers)
})
export const { default: request } = requests