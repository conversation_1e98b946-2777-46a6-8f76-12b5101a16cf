<template>
  <div id="question" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="试卷"></TY_NavTop>
    <div class="container">
      <div class="top">
        <p><span class="ttl">剩余答题时间</span>
          <span class="red">{{ leftTimeStr }}</span></p>
      </div>
      <div>
        <div class="title" style="padding: 10px 20px;"><span>{{ques.trainingQuestion.type == 1 ? '单选题' : '判断题' }} {{ques.orders}}</span><span style="margin-left:20px;">(本题{{ques.trainingExamQuestion.score}}分)</span></div>
        <div style="background: #fff;padding: 10px 20px;">
          <p style="margin-bottom: 20px; font-size: 14px;" v-html="ques.trainingQuestion.content"></p>
          <div v-if="ques.trainingQuestion.type == 1">
            <el-radio-group v-model="ans" size="small">
              <el-radio v-for="(item, index) in ques.trainingExamKeyList" v-bind:key="index" :label="item.id" ><span class="orderCon">{{ item.code }}</span> {{ item.content }}</el-radio>
            </el-radio-group>
          </div>
          <div v-if="ques.trainingQuestion.type == 2">
            <el-radio-group v-model="ans" size="small">
              <el-radio :label="1" >√</el-radio>
              <el-radio :label="0" >×</el-radio>
            </el-radio-group>
          </div>
          <div>
            <div class="subBtn" @click="quesTj">确 定</div>
            <div class="handle_button handle-center">
              <input type="submit" class="ui-btn ui-btn_info" value="上一题" @click="drawQues(1)">
              <input type="submit" class="ui-btn ui-btn_info" value="下一题" @click="drawQues(2)">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #question{
    color: #000001; overflow: auto;
    .ttl{font-size: 14px;}
    .red{ color:red; font-size:18px; display: inline-block; margin-left:40px;  }
    .top{ padding:10px 20px; background: #fff;   }
    .subBtn{ width:50%; margin:50px auto 20px;line-height:30px; text-align:center; color:#fff;background: #4797f1;  border-radius: 4px; }
    .handle_button .ui-btn{margin: 0; width: 88px; font-size:12px;}
    .el-radio {white-space: inherit;}
    .orderCon{ margin: 0 10px;}
  }
</style>
<script>
import { gohistory } from '../../../js/common'
export default {
  name: 'question',
  data () {
    return {
      ans: '',
      ques: {},
      info: {},
      contentStr: '',
      leftTimeStr: '',
      exam: '',
      timeRun: 1,
      loading: false,
      tureAble: false
    }
  },
  created () {
    let res = JSON.parse(localStorage.getItem('res'))
    this.info = res.info
    this.ques = res.ques
    let type = this.ques.trainingQuestion.type
    if (Number(type) === 1) {
      this.ans = Number(res.ques.trainingExamUserAnswer.examKey)
    } else {
      this.ans = Number(res.ques.trainingExamUserAnswer.memo)
    }
    this.setLeftTime()
  },
  destroyed: function () {
    let _this = this
    _this.timeRun = 0
  },
  methods: {
    drawQues: function (type) {
      let _this = this
      let quesNum = _this.ques.index
      let curr = quesNum
      let examKey = ``
      let quesType = Number(_this.info.respExamQuestionList[quesNum].trainingQuestion.type)
      _this.tureAble = false
      if (Number(quesType) === 1) {
        examKey = _this.info.respExamQuestionList[quesNum].trainingExamUserAnswer.examKey
      } else if (Number(quesType) === 2) {
        examKey = _this.info.respExamQuestionList[quesNum].trainingExamUserAnswer.memo
      }
      if (type === 1) {
        if (quesNum === 0) {
          _this.$kiko_message('没有上一题！')
          return false;
        }
        curr = quesNum - 1
      } else {
        if (quesNum === _this.info.respExamQuestionList.length - 1) {
          _this.$kiko_message('没有下一题！')
          return false;
        }
        curr = quesNum + 1
      }
      if (_this.ans !== 0 && !_this.ans || Number(examKey) === Number(_this.ans)){
        _this.ques = _this.info.respExamQuestionList[curr]
        let icon = Number(_this.ques.trainingQuestion.type)
        if (icon === 1) {
          _this.ans = Number(_this.ques.trainingExamUserAnswer.examKey)
        } else if (icon === 2) {
          _this.ans = Number(_this.ques.trainingExamUserAnswer.memo)
        }
      } else if (examKey !== _this.ans) {
        if (quesType === 1) {
          _this.info.respExamQuestionList[quesNum].trainingExamUserAnswer.examKey = _this.ans
        } else if (quesType === 2) {
          _this.info.respExamQuestionList[quesNum].trainingExamUserAnswer.memo = _this.ans
        }
        _this.loading = true
        _this.submit(curr)
      }
    },
    setLeftTime: function () {
      let that = this
      if (that.timeRun === 1) {
        let now = (new Date(that.info.date)).getTime();
        let end = (new Date(that.info.endTime)).getTime();
        let leftTime = end - now;
        let h, m, s;
        h = Math.floor(leftTime / 1000 / 60 / 60 % 24);
        m = Math.floor(leftTime / 1000 / 60);
        s = Math.floor(leftTime / 1000 % 60);
        if (m < 10) {
          m = '0' + m
        }
        if (s < 10) {
          s = '0' + s
        }
        if (leftTime > 1) {
          that.leftTimeStr = `${m}:${s}`
          now = now + 1000
          that.info.date = now
          setTimeout(that.setLeftTime, 1000);
        } else {
          that.leftTimeStr = `00:00`
          that.$kiko_message('答题时间已过')
          that.setPath()
        }
      }
    },
    quesTj: function () {
      let that = this
      that.tureAble = true
      let quesType = Number(that.ques.trainingQuestion.type)
      let key = ``
      if (quesType === 1) {
        key = that.ques.trainingExamUserAnswer.examKey
      } else {
        key = that.ques.trainingExamUserAnswer.memo
      }
      if (key !== '' && key !== that.ans) {
        that.submit()
      } else{
        gohistory(that)
      }
    },
    submit: function (curr) {
      let that = this
      if(!that.ans && that.ans !== 0){
        that.$kiko_message('请先选择答案！')
        return false
      }
      let url = '../../../examManage/addExamQuestionAnswer.do'
      let ansId = that.ques.trainingExamUserAnswer.id
      let data = { 'examQuestion':that.ques.trainingExamQuestion.id }
      if(ansId && ansId != 'undefined'){
        data['answerId'] = ansId
      }
      let type = that.ques.trainingQuestion.type
      if (type == 1){ // 单选题
        that.ques.trainingExamKeyList.forEach(function(item) {
          if(item.id === that.ans){
            data['memo'] = item.code
            data['examKey'] = item.id
          }
        })
      }else if(type == 2){ // 判断题
        data['memo'] = that.ans
      }
      that.axios.post(url, data).then((response) => {
        that.loading = false
        let res = response.data
        let status = res.status
        if(status == 1){
          if (that.tureAble) {
            gohistory(that)
          } else {
            let quesType = Number(that.info.respExamQuestionList[curr].trainingQuestion.type)
            that.ques = that.info.respExamQuestionList[curr]
            if (quesType === 1) {
              that.ans = Number(that.ques.trainingExamUserAnswer.examKey)
            } else {
              that.ans = Number(that.ques.trainingExamUserAnswer.memo)
            }
          }
        }else{
          that.$kiko_message('提交失败，请重试！')
        }

      }).catch(function (res) {
        console.log(res)
      })
    },
    setPath: function () {
      let floatingUrl = window.localStorage.getItem('floating')
      if (floatingUrl) {
        let pathArr = floatingUrl.split('-')
        let newArr = pathArr.slice(0, pathArr.length - 3)
        this.$router.push({
          path: `/examResult/${this.info.exam}`
        })
        window.localStorage.setItem('floating', newArr.join('-'))
      }
    }
  }
}
</script>
