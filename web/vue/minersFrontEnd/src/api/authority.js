import {request} from "@/utils/request";
/* ------------------权限设置------------------ */

// creator: 张旭博，2024-08-06 02:27:24， 获取客户合同列表
export function getUserList() {
    return request({
        url: '/popedom/getUserList.do'
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取一人权限
export function getOneMenu(manageId, userId) {
    return request({
        url: '/popedom/getExclusivePopedomsByManager.do',
        data: {
            manageId: manageId,
            userId: userId
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取多人权限
export function getAllMenu(manageId, userId) {
    return request({
        url: '/popedom/getPopedomListByManager.do',
        data: {
            manageId: manageId,
            userId: userId
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取多人权限
export function saveUserPopedom(userId, mid) {
    return request({
        url: '/popedom/saveUserPopedom.do',
        data: {
            userId: userId,
            mid: mid
        }
    })
}

// creator: 张旭博，2025-06-18 14:12:36， 当前权限
export function getCurrentSettingList() {
    return request({
        url: '../popedom/getOrgPopedomShow.do',
        data: {}
    })
}