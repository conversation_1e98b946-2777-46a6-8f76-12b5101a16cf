.orderItem{
    width:240px;
    font-size: 14px;
    float: left;
    margin: 0 30px;
    position: relative;
}
.hd1{
    display: none;
}
.orderItem>.orderItemTitle{
    color: #999;
    margin: 8px 0;
    text-align: left;
}
.orderItem>.orderItemCon{
    width: 240px;
    height: 32px;
    background-color: #fff;
    border-radius: 5px;
    line-height: 31px;
    text-align: center;
    color: #333;
}
.orderList{
    padding: 10px 30px 0 30px;
}
.orderList>span.ty-btn{
    margin:10px 0;
}
.orderItemSmall{
    width:160px;
    margin: 0 10px;
}
.orderItemMiddle{
    width:180px;
    margin: 0 10px;
}
.orderItemTiny{
    cursor:pointer;
    display:inline-block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    border-radius: 2px;
    margin-left: 5px;
    margin-top:8px;
    text-align: left;
}
.orderItemTiny i{
    font-size: 14px;
    margin-right:5px;
    width: 14px;
    height: 14px;
}
.orderItemTinyActive{
    background-color: #48cfad;
    color: #fff;
}
.orderItemSmall>.orderItemCon{
    width: 160px;
    height: 32px;
    background-color: #f0f8ff;
    border-radius: 5px;
    line-height: 31px;
    text-align: center;
    color: #333;
}
.radioChooseInfo,.selectChooseInfo{
    position: absolute;
    top: 0;
    left: 100%;
    width: 402px;
    background-color: #fff;
    padding: 10px 20px 20px 20px;
    border:1px solid #5d9cec;
    box-shadow: 0 0 5px #5da3f3;
    display: none;
}
.orderList td:hover .radioChooseInfo,.orderList td:hover .selectChooseInfo{
    display: block;
}
.chooseShow{
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    border: 1px solid #48cfad;
    width: 392px;
    text-align: center;
    color: #666;
    cursor: default;
    padding: 7px 0px 15px 15px;
    /*background-color:rgba(255,255,255,0.8)*/
    background-color: #fff;
}
.chooseShowBtn:hover .chooseShow{
    display: block;
}
.chooseShow{
    display: none;
    cursor: default;
}
.ty-optionCon{
    display: none;
    width: 182px;
    height: 150px;
    position: absolute;
    left: 0;
    z-index: 10;
    background-color: #fff;
    overflow-y: auto;
}
.ty-optionCon .ty-option{
    width: 100%;
    height: 30px;
    background-color: #f6f6f6;
    color: #666;
    padding-left: 8px;
    line-height: 30px;
    overflow: hidden;
}
.ty-optionCon .ty-option:hover{
    background-color: #48cfad;
    color: #fff;
}
.ty-optionCon .ty-optionActive{
    background-color: #A7F1DE;
    color: #fff;
}
.ty-inputSelect + .ty-optionCon{
    box-shadow: 0 3px 5px #cff0e7;
    background-color: #fefefe;
}
.bonceFoot .inputTip{
    color: #ed5565;
    text-align: left;
    margin: 0 10px;
}
.bonceFoot .inputTip>i{
    margin-right: 5px;
}
.ty-table tbody .newGoodItem td{
    background-color: #fffef4;
}

#roleCon , #roleCon_update{ width:455px;   }

.orderItemTiny i{ color:#48cfad ; }
.ty-dropdownCon>div:nth-child(2){ padding:0 0 5px 15px ; border-bottom:1px solid #eaeaea;    }
.ty-dropdownCon>div:nth-child(3){ padding: 0 0 15px 15px  ;  }
.ty-dropdownCon>div:nth-child(4){ text-align:right; padding: 0 15px 15px;  }

tr.throw td{ text-decoration:line-through ; color:#bbb;   }
.tipitemCon{ padding: 10px; display:inline-block;  }

/* 1.47 新加的内容 */
.ty-panel {
    padding: 10px;
}

#chart {
    width: 720px;
}

.warming > p > span {
    color: #ed5565;
    font-size: 16px;
}

table.ty-table td.bg_ea {
    background: #eaeaea;
}

table.ty-table td.bg_d {
    background: #ddd;
}

table.ty-table td.bg_c {
    background: #ccc;
}

#ordinfo {
    width: 1435px;
}

#ordTicket {
    width: 1100px;
}

#endOrdLog {
    width: 600px;
}

#endOrdLog .bg {
    height: 80px;
}

#goodsList {
    min-width: 1400px;
}

#ordTicket td {
    height: 30px;
}

.xing {
    color: #ed5565;
}

.linkOrds {
    min-width: 182px;
    text-align: center
}

.endArea, .ysGs {
    display: none;
}

.bounce_Fixed2,.bounce_Fixed3,.bounce_Fixed4,.bounce_Fixed5 {
    position: fixed;
    background: rgba(100, 100, 100, 0.5);
    padding-bottom: 200px;
}
.bounce_Fixed2 {
    z-index: 10002;
}
.bounce_Fixed4 {
    z-index: 10006;
}
.bounce_Fixed5 {
    z-index: 10007;
}

#linkOrd {
    width: 800px;
}

.tpl {
    width: 100%;
}

.mar {
    margin-right: 30px;
}

.marTop {
    margin-top: 30px;
}

.orderTable, .orderTableEnd {
    min-width: 1480px;
    margin: 0 0 30px 0;
}

.ri {
    position: absolute;
    right: 10px;
    top: 55px;
}

/* 自定义查询 */
.ty-btn-group {
    border: 1px solid #bbb;
    border-radius: 4px;
}

ul.searchCon:before {
    border-bottom: 7px solid rgba(0, 0, 0, 0.2);
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    content: "";
    display: inline-block !important;
    position: absolute;
    right: 35px;
    top: -7px;
}

ul .trigle {
    border-bottom: 5px solid #fff;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    content: "";
    display: inline-block !important;
    position: absolute;
    right: 37px;
    top: -5px;
}

.searchCon {
    left: -165px;
    width: 350px;
    border: 1px solid #ccc;
    box-shadow: 3px 3px 5px #666;
    top: 40px;
}

.searchCon .ttl {
    display: inline-block;
    width: 100px;
    text-align: right;
}

.searchCon input {
    display: inline-block;
    width: 215px;
    height: 35px;
    line-height: 35px;
    padding: 0 5px;
}

.searchCon li {
    margin-top: 10px;
}

.searchCon .ctl {
    text-align: right;
    padding-right: 30px;
    margin: 30px 0 15px 0;
}

.Search {
    display: inline-block;
    width: 250px;
    height: 35px;
    position: relative;
}

.Search input {
    display: inline-block;
    width: 100%;
    height: 100%;
    border: 1px solid #48cfad;
    color: #333;
    padding: 0 60px 0 10px;
    border-radius: 4px;
}

.Search .se {
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 0 4px 4px 0;
    display: inline-block;
    width: 50px;
    height: 100%;
    background: #48cfad;
    color: #fff;
    line-height: 35px;
    text-align: center;
    cursor: default;
}

.Search .se:hover {
    background: #37bc9b;
}

#end_reason {
    width: 100%;
    border: 1px solid #ed5565;
    box-shadow: 0 0 3px #ed5565;
}

select.customerName {
    width:200px;
}
#goodsList td div{ border-bottom:1px solid #ccc; margin: 0 -16px; line-height: 20px;   }
#goodsList td div:last-child{ border-bottom:none   }

#ticketTip, #ticketInfo {
    width: 680px;
}

#scanTickInfo {
    width: 888px;
}

#ticketInfoScan {
    width: 750px;
}

#scanGoodsInfo {
    width: 1325px;
}

.ticketOpen {
    display: none;
}

.i-blueCon {
    cursor: default;
}

.i-blueCon i {
    color: #0b94ea;
}
.newGood{
    margin-right: 20px;
}
.editMemo {
    display: none;
}
.red {
    color: #ff6431;
}
.linkBtn { color: #5a94ff; display: inline-block; padding:0 10px; cursor: default; }
.linkBtn:hover{text-decoration: underline; }

.flexcon{ display: flex; margin-top: 15px;  }
.flexcon>div{ flex:1; margin: 0 15px;  }
.flexcon input[ disabled="disabled"],#newGood input[ disabled="true"] { background: #eee!important;  }
.flexcon .form-control { width: 240px; }
#newGood .ty-inputText , #newGood .ty-inputSelect { width: 256px; }
#newGood .orderItemMiddle{ width:264px; }
.sale_con,.sale_con1,.sale_con2,.sale_con3,.sale_con4,.sale_seecon,.sale_seecon1{
    display: inline-block;
}
.sale_con,.sale_con1,.sale_con2,.sale_con3,.sale_con4,.sale_seecon,.sale_seecon1{
    display: inline-block;
}
.sale_con1>input {
    width: 220px;
}
#InvoiceMessage .sale_con1>span,.sale_con2>span,.sale_con>span{  position: relative; top:7px; width: 200px; display: inline-block; color:#7f95aa;   }
.sale_c label{
    margin-left:30px;
    font-weight: normal;
}
.sale_c input[type='radio']{
    margin-right:10px;
}
.sale_c{
    margin-left:20px;
    font-size: 13px;
    font-weight: 400;
    vertical-align: middle;
    padding: 4px 0;
    overflow: hidden;
    display: flex;
}
.sale_ttl1{ display: inline-block; min-width: 100px; text-align: right; line-height: 32px; vertical-align: top; }
.sale_gap{margin-right:30px;}
.otherContact{overflow-y: hidden; }
.otherContact li{float:left;margin-bottom:10px;width:328px;}
#newContectData .addMore{margin-left: 6px;margin-right: 12px;}
#uploadCard{display:inline-block;}
.bussnessCard .filePic{ display:inline-block;width: 260px; height: 150px; background-size: contain;  background-repeat: no-repeat;}
.chooseCusBtn{ background: #ddd; width: 40px; display:inline-block; text-align: center; line-height: 30px; height: 30px; margin-left: -5px;  }
.sale-con{padding-left:10px;min-width: 142px;line-height:32px;}
.contactflex{display:flex;padding-left: 30px;}
.see_otherContact,.record_otherContact{ margin: 20px auto;  }
.see_otherContact tr>td:nth-child(odd), .record_otherContact tr>td:nth-child(odd){ width:20%;   }
.see_otherContact tr>td:nth-child(even), .record_otherContact tr>td:nth-child(even){ width: 30%;   }
.cusList { margin:20px auto; width:90%;   }
.cusList>li{ list-style: none; background:#fff; line-height:30px; padding:0 15px;  }
.cusList>li:nth-child(even){ background:#f5f5f5;  }
#chooseCusContact .fa{ color: #50a9ea;    }
.setItem>.fa{ color:#5d9cec; margin-right:15px;   }
.invoiceTip { display: none!important;  }
#invoiceSet .tipsmall{ color:#5d9cec; font-size:0.8em;   }
.ty-inputText:disabled, .ty-inputSelect:disabled{ background: #eee!important; }

#cScan .leftTab .fa{ color: #5d9cec; }
#cScan .leftTab tbody tr td{  text-align:left;  }
#cScan .leftTab tbody tr td:first-child{  max-width:400px;  }
#cScan .fileImScan>span{ color: #5d9cec; font-size: 18px; padding: 0;}
#cScan .enabledList{ text-align: right; max-height: 100px; overflow: auto; margin: 10px 0; }
#cScan .enabledList .enName{ display: inline-block; width: 80px; }
#picShow{  position: fixed;  display: inline-flex;  justify-content: center;  border: 1px solid #aaa;  background: rgba(7, 7, 7, 0.64);  z-index: 100009;  width: 100%;
    height: 100%;  text-align: center;  vertical-align: middle;  top: 0;  left: 0;  align-items: center;  }
#picShow img{  margin-top:30px;  }

#tipcontractGoods .controlTd{ position: relative; overflow: initial; -webkit-user-select:none; -moz-user-select:none; -ms-user-select:none; user-select:none; }
#tipcontractGoods .controlTd .fa {  position: absolute;  left: -32px;  font-size: 21px;  color: #5d9cec;  }
#newOrder select[disabled],#newOrder input[disabled]{ background: #eee; }


.customersSearch{ position: relative; }
.cusSearchItems{ position: absolute; display: none; background: #fff; width: 236px; left: 5px; border: 1px solid #ddd; max-height: 130px; overflow-y: auto;  }
.cusSearchItems>option{ padding:0 15px; }
.cusSearchItems>option:hover{ background: #b9d2df; }
.area-body{margin: auto;width:400px;}
.area-elem{margin-top: 30px;}
.tfType {  margin-right: 30px;  }
.tfType i{  margin-right: 10px;  }
.tfType i.fa {  color: #0b94ea;  }
.reviewMsg{  margin: auto;width: 350px}
.changeDot .fa-dot-circle-o{color:#48cfad ;}
.changeDot{padding: 8px 10px;}
.changeDot i{margin-right: 20px;}
.checkSect .changeDot i{margin-right: 36px;}
.careful{color: #0070c0;}
.narrowBody{  margin: 0 auto;  width: 80%;  }
.lop{margin-top: 10px;}
.checkSect{padding: 30px 0 0 64px;width: 56%;}
.lop .changeDot{width: 160px;}
.backBtn {margin-bottom: 10px;}

#newGood .orderItemMiddle_l{width: 540px;margin: 0 10px;}
#newGood .orderItemMiddle_l select{width: 100%;}
.definedCon{position: relative;margin: auto;text-align: center;  width: 300px; }
.definedCon .ttl{font-weight:600;}
.definedCon input{padding-right:10px;width:100%;}
.clearLableText{position: absolute;  right: 8px;bottom: 6px;  cursor: pointer;  }
.flexBox{width: 100%;box-sizing: border-box;border-radius: 2px;position: relative;font-size: 13px;overflow: hidden;
    opacity: 1;display: flex;align-items: center;justify-content: space-between;transition: opacity .2s;}
.lineBtn{ color: #5a94ff; cursor: pointer;}
.receiveMethod > div {margin-top: 10px;padding: 20px 0;border-top: 1px solid #ccc;}
.receiveMethod > div:first-child {border-top: none;}
.checkItem .fa-dot-circle-o{color:#5d9cec ;}
.checkItem{margin: 30px 0;}
.checkItem i{margin-right: 10px;}
.wideBody{margin: auto;width: 80%;}
.goodsAddress.require-box {margin: 10px 0 0 30px;}
.blueCare{color: #0070bf;}
.intend{text-indent: 28px;}
.placeList {margin:20px auto;width:90%;}
.placeList>li{list-style: none;background:#fff;line-height:30px;padding:0 15px;}
.placeList>li:nth-child(even){background:#f5f5f5;}
.placeList .fa{margin-right: 20px;}

#ticketInfo{ width: 1000px; }
#ticketInfo hr{ border-bottom: 1px solid #ccc; }
.flexCC{ display: flex }
.flexCC>div{ flex:1;  }

.ty-invoiceTicket{ color: #d25f5f;  }
.ty-invoiceTicket .ty-ticketName{     display: inline-block;
    margin: 20px auto; border-style: double;  border-color: #d25f5f; border-top:none;border-left:none;border-right:none; }
.ty-ticketNameC{
    text-align: center;
}
.ty-ticketTab{
    width: 100%;  border-collapse: collapse;
}
.ty-ticketTab td.ty-ticket-txt-left{
    text-align: left;
}
.ty-ticketTab tr.ty-ticket-no-border td.ty-ticket-td-border{
    border-top: 1px solid #d25f5f ;
}
.ty-ticketTab tr.ty-ticket-no-border td{
    border-top: none;
    border-bottom: none;
}
.ty-ticketTab td.ty-ticket-txt-black{
    color: #666!important;
}
.ty-ticketTab td.ty-ticket-txt-blue{
    color: #0a9bee!important;
}
.ticketBeforeOpen .fa{
    color: #0a9bee!important;
}
.ty-ticketTab td{
    padding: 5px;
    border: 1px solid #d25f5f; text-align: center;
}

.ty-ticket-ttl>span>span:nth-child(1){
    display: inline-block;
    width: 150px;
}
.areaForm .sale_ttl1{
    width: 180px;
    text-align: left;
}
.areaForm {
    margin: auto;
    width: 85%;
}
.clearVal{
    position: relative;
}
input:focus + i.clearInputVal{
    display: block;
}
.clearVal .clearInputVal{
    position: absolute;
    display: none;
    right: 8px;
    font-style: normal;
    color: #7a7b7e;
    top: 4px;cursor: pointer;
}

#twoEditLog{ width: 800px }
#twoEdit .blueTip{ color: #0a9bee; font-size: 0.8em; }
#twoEdit .padC{ padding:5px 20px; }
#twoEdit .selectItems{ margin:10px }
#twoEdit .selectItem{ padding:5px 20px; }
#twoEdit .selectItem .fa{ color: #0070c0; }
#twoEdit hr{ border-bottom: 1px solid #ccc;  }
#twoEdit input:disabled{ background: #eaeaea; }
#twoEdit{width: 600px;}
.modelVal , .modelVal{padding-left: 30px;}
.otherContact .gap{margin-left: 4px;margin-right: 6px;}
#addMoreContact option[disabled]{background: #ddd;cursor: pointer;}
.uploadify-queue{display: none;}

#newGoodsTip .fa{ color: #0b94ea; margin-right: 10px; }
#newGoodsTip .ngTipR {
    display: inline-block; width: 200px;

}
.gap2{margin-bottom: 20px;}
/*#inProgressEndOrd .ty-radio input + label{left: 1px;}*/
.inProgressChart{border-bottom: 1px solid #CCCCCC;margin-bottom: 20px;}
