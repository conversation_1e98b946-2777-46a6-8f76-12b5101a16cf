package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.PoContractBase;
import cn.sphd.miners.modules.material.entity.PoContractBaseHistory;


import java.io.Serializable;

public interface PoContractBaseHistoryDao extends IBaseDao<PoContractBaseHistory, Serializable> {
    PoContractBaseHistory insert(PoContractBase slContractBase);
}
