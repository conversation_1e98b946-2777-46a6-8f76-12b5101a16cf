package cn.sphd.miners.modules.equipment.controller;

import java.util.Date;
import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquSupplier;
import cn.sphd.miners.modules.equipment.service.ITEquSupplierService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_供应商名录
 * 20230209 1.247装备器具新增Controller
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment/supplier")
public class EquSupplierController {
    private String prefix = "system/supplier";

    @Autowired
    private ITEquSupplierService tEquSupplierService;

    @GetMapping()
    public String supplier() {
        return prefix + "/supplier";
    }

    /**
     * 查询装备管理_供应商名录
     * 20230209 1.247装备器具新增列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TEquSupplier tEquSupplier,User user) {
        tEquSupplier.setOrg(Long.valueOf(user.getOid()));
        List<TEquSupplier> list = tEquSupplierService.selectTEquSupplierList(tEquSupplier);
        return list;
    }


    /**
     * 新增装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquSupplier tEquSupplier, User user) {
        tEquSupplier.setOrg(Long.valueOf(user.getOid()));
        tEquSupplier.setCreator(Long.valueOf(user.getUserID()));
        tEquSupplier.setCreateDate(new Date());
        tEquSupplier.setCreateName(user.getUserName());
        tEquSupplier.setEnabled(1L);
        return toAjax(tEquSupplierService.insertTEquSupplier(tEquSupplier));
    }

    /**
     * 修改装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */
    @GetMapping("/edit/{id}")
    @ResponseBody
    public TEquSupplier edit(@PathVariable("id") Long id) {
        TEquSupplier tEquSupplier = tEquSupplierService.selectTEquSupplierById(id);

        return tEquSupplier;
    }

    /**
     * 修改保存装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquSupplier tEquSupplier) {
        return toAjax(tEquSupplierService.updateTEquSupplier(tEquSupplier));
    }

    /**
     * 删除装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquSupplierService.deleteTEquSupplierByIds(ids));
    }
}
