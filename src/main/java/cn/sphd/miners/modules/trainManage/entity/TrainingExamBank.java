package cn.sphd.miners.modules.trainManage.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-02-03 
 */

@Entity ( name ="TrainingExamBank" )
@Table ( name ="t_training_exam_bank" )
public class TrainingExamBank  implements Serializable {

	private static final long serialVersionUID =  7168783161908683406L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 考核ID
	 */
   	@Column(name = "exam" )
	private Integer exam;

	/**
	 * 题库ID
	 */
   	@Column(name = "bank" )
	private Integer bank;

	/**
	 * 选择题数量
	 */
   	@Column(name = "choice_num" )
	private Integer choiceNum;

	/**
	 * 判断题数量
	 */
   	@Column(name = "tf_num" )
	private Integer tfNum;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getExam() {
		return this.exam;
	}

	public void setExam(Integer exam) {
		this.exam = exam;
	}

	public Integer getBank() {
		return this.bank;
	}

	public void setBank(Integer bank) {
		this.bank = bank;
	}

	public Integer getChoiceNum() {
		return this.choiceNum;
	}

	public void setChoiceNum(Integer choiceNum) {
		this.choiceNum = choiceNum;
	}

	public Integer getTfNum() {
		return this.tfNum;
	}

	public void setTfNum(Integer tfNum) {
		this.tfNum = tfNum;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
