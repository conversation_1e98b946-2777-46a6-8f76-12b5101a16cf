<template>
  <div class="overviewFund">
    <div v-if="mainNum !== 1">
      <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" id="goPrev" @click="goPrev()">返回</span>
    </div>
    <div>
      <div class="dataNav">
        <!-- 展示四个大数据 -->
        <div id="mainDataNav" v-if="mainNum === 1">
          <div class="navtab">
            <div class="left"><span id="betweenDate" >
              {{searchLevel === 1?'今天是'+ new Date( mainChart["beginDate"] ).format('yyyy-MM-dd'):
                new Date( mainChart["beginDate"] ).format('yyyy-MM-dd')+ " ~ " +new Date( mainChart["endDate"] ).format('yyyy-MM-dd')}}
            </span></div>
            <div class="right" style="position:relative; ">
                                    <span class="btnGroup" style="margin-right:200px; ">
                                        <span :class="{'btnnActive': searchLevel === 1}" class="btnn btnn-big" id="theDay" @click="searchByLevel(1)">本日</span>
                                        <span :class="{'btnnActive': searchLevel === 2}" class="btnn btnn-big" id="theMonth" @click="searchByLevel(2)">本月</span>
                                        <span :class="{'btnnActive': searchLevel === 4}" class="btnn btnn-big" id="theYear" @click="searchByLevel(4)">本年</span>
                                        <span class="clr"></span>
                                    </span>
              <el-popover :visible="popoverVisible" placement="bottom" :width="350" trigger="click">
                <template #reference>
                  <el-button :class="{'btnnActive': popoverVisible}" class="btnn btnn-big riBtn" @click="dropDownDIY">自定义查询</el-button>
                </template>
                <div class="searchCon">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <span class="ttl">时间范围：</span>
                      <el-date-picker
                        v-model="DIYStart"
                        type="date"
                        placeholder=""
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                    />
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <span class="ttl">到：</span>
                      <el-date-picker
                          v-model="DIYEnd"
                          type="date"
                          placeholder=""
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                      />
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="22" class="txtRight">
                      <el-button  @click="searchByDIY(5)" type="primary">查询</el-button>
                      <el-button  @click="popoverVisible = false">取消</el-button>
                    </el-col>
                    <el-col :span="2">
                    </el-col>
                  </el-row>
                </div>
              </el-popover>
            </div>
            <div class="clr"></div>
          </div>
          <div class="mainNav">
            <div class="bg_blue pal">
              <div class="visual">
                <i class="indexBg prevMonth"></i>
              </div>
              <div class="details">
                <div class="number">
                  <span data-counter="counterup" id="allPreviousBalance"> {{parseFloat(mainChart.allPreviousBalance).toFixed(2)}}</span>
                </div>
                <div class="desc" id="prevBalance"> {{prevBalanceTtl}} </div>
              </div>
            </div>
            <div class="bg_green pal">
              <div class="visual">
                <i class="indexBg theMonthGet"></i>
              </div>
              <div class="details">
                <div class="number">
                  <span data-counter="counterup" id="allCredit">{{parseFloat(mainChart.allCredit).toFixed(2)}}</span></div>
                <div class="desc" id="curGet"> {{curGetTtl}} </div>
              </div>
            </div>
            <div class="bg_red pal">
              <div class="visual">
                <i class="indexBg theMonthPay"></i>
              </div>
              <div class="details">
                <div class="number">
                  <span data-counter="counterup" id="allDebit">{{parseFloat(mainChart.allDebit).toFixed(2)}}</span>
                </div>
                <div class="desc" id="curOut"> {{curOutTtl}} </div>
              </div>
            </div>
            <div class="bg_purple pal ">
              <div class="visual">
                <i class="indexBg theMonthLast"></i>
              </div>
              <div class="details">
                <div class="number">
                  <span data-counter="counterup" id="allBalance" >{{parseFloat(mainChart.allBalance).toFixed(2)}}</span></div>
                <div class="desc" id="curBalance"> {{curBalanceTtl}} </div>
              </div>
            </div>
          </div>
          <div class="navtab">
            <div class="btnGroup searchType">
              <span :class="{'btnnActive': Number(searchMethod) === 2}" class="btnn btnn-big" id="searchByFlow" v-if="Number(searchLevel) === 2" @click="searchByMethod(2)">按月流水查看</span>
              <span :class="{'btnnActive': Number(searchMethod) === 0}" class="btnn btnn-big" id="searchByTime" @click="searchByMethod(0)">{{ Number(searchLevel) === 2 ? '按日期查看':'按时间查看' }}</span>
              <span :class="{'btnnActive': Number(searchMethod) === 1}" class="btnn btnn-big" id="searchByAccount" @click="searchByMethod(1)">按账户查看</span>
            </div>
          </div>
        </div>
        <!-- 查按时间段查询 按年显示某段时间的  -->
        <div id="duringLevel" v-if="mainNum === 2 && [1,2,3,''].indexOf(monthOrDayKind) > -1">
          <table class="ty-table">
            <thead>
            <td>日期</td>
            <td id="nva1">{{navTtl.nav1}}</td>
            <td id="nva2">{{navTtl.nav2}}</td>
            <td id="nva3">{{navTtl.nav3}}</td>
            <td id="nva4">{{navTtl.nav4}}</td>
            </thead>
            <tbody id="duringLevel_table">
            <tr>
              <td id="nav5">{{navTtl.nav5}}</td>
              <td id="nav6">{{navTtl.nav6}}</td>
              <td id="nav7">{{navTtl.nav7}}</td>
              <td id="nav8">{{navTtl.nav8}}</td>
              <td id="nav9">{{navTtl.nav9}}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="dataList">
        <!-- dataInfo -->
        <div id="dataInfo" v-if="(mainNum === 1 && searchMethod === 0 && searchLevel === 1) || (mainNum === 2 && monthOrDayKind === 3)">
          <table class="ty-table ty-table-control">
            <thead><tr>
              <td width="15%">时间</td>
              <td width="25%">摘要</td>
              <td class="belongOrg">所属机构</td>
              <td width="10%">收入</td>
              <td width="10%">支出</td>
              <td width="10%">经手人</td>
              <td width="15%">操作</td>
            </tr></thead>
            <tbody id="dataInfo_tbl">
            <tr v-for="(item, index) in dataByDayList" :key="index">
              <td>{{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</td>
              <td>{{item.summary}}</td>
              <td> {{ item.orgName || ''}} </td>
              <td>{{item.credit || '--'}}</td>
              <td>{{item.debit || '--'}}</td>
              <td>{{item.auditorName|| '--'}}</td>
              <td v-if="Number(item.type) !== 1">
                <span v-if="Number(item.source) === 2" class="ty-color-blue" @click="seeOrdLoanDetail(item)">查看</span>
                <span v-else-if="Number(item.source) === 3" class="ty-color-blue" @click="seeCollectDetail(item)">查看</span>
                <span v-else-if="Number(item.source) === 6" class="ty-color-blue" @click="seeWageDetail(item)">查看</span>
                <span v-else-if="Number(item.source) === 8" class="ty-color-gray">查看</span>
                <div v-else>
                  <span v-if="item.reimburseId" class='ty-color-blue' @click='getReimburseDetail(item.reimburseId)'>查看</span>
                  <span v-if="!item.reimburseId && (item.modityStatus == 2 || item.modityStatus == 3)" class='ty-color-gray'>查看</span>
                  <span v-if="!item.reimburseId && item.modityStatus != 2 && item.modityStatus != 3" class="ty-color-blue" @click="getDetails(item)">查看</span>
                </div>
              </td>
              <td v-else></td>
            </tr>
            </tbody>
          </table>
        </div>
        <!-- dataByLevel 按 月/年/时间段 查询 ， 展示列表 -->
        <div id="dataByLevel" v-if="mainNum === 2 && [1,2,''].indexOf(monthOrDayKind) > -1 || mainNum === 1 &&( searchMethod === 2 || searchMethod === 0 && (searchLevel === 2 || searchLevel === 3 || searchLevel === 4 ||
        (searchLevel === 5 && (DIYType === 'A' || DIYType === 'B' || DIYType === 'C'))))">
          <table class="ty-table ty-table-control">
            <thead>
            <td width="15%" id="td0">{{tdTtl.td0}}</td>
            <td width="15%" id="td1">{{tdTtl.td1}}</td>
            <td class="belongOrg">所属机构</td>
            <td width="15%" id="td2">{{tdTtl.td2}}</td>
            <td width="15%" id="td3">{{tdTtl.td3}}</td>
            <td width="15%" id="td4">{{tdTtl.td4}}</td>
            <td width="15%">操作</td>
            </thead>
            <tbody id="dataByLevel_tbl" v-if="searchMethod === 2">
            <tr v-for="(item, index) in dataByFlowList" :key="index">
              <td>{{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</td>
              <td>{{item.summary}}</td>
              <td> {{ item.orgName || ''}} </td>
              <td>{{item.credit || '--'}}</td>
              <td>{{item.debit || '--'}}</td>
              <td>{{item.auditorName|| '--'}}</td>
              <td v-if="Number(item.type) !== 1">
                <span v-if="Number(item.source) === 2" class="ty-color-blue" @click="seeOrdLoanDetail(item)">查看</span>
                <span v-else-if="Number(item.source) === 3" class="ty-color-blue" @click="seeCollectDetail(item)">查看</span>
                <span v-else-if="Number(item.source) === 6" class="ty-color-blue" @click="seeWageDetail(item)">查看</span>
                <span v-else-if="Number(item.source) === 8" class="ty-color-gray">查看</span>
                <div v-else>
                  <span v-if="item.reimburseId" class='ty-color-blue' @click='getReimburseDetail(item.reimburseId)'>查看</span>
                  <span v-if="!item.reimburseId && (item.modityStatus == 2 || item.modityStatus == 3)" class='ty-color-gray'>查看</span>
                  <span v-if="!item.reimburseId && item.modityStatus != 2 && item.modityStatus != 3" class="ty-color-blue" @click="getDetails(item)">查看</span>
                </div>
              </td>
              <td v-else></td>
            </tr>
            </tbody>
            <tbody v-if="(searchMethod === 0 || searchMethod === 1) && (searchLevel === 2 || searchLevel === 3 || searchLevel === 4 || (searchLevel === 5 && dataByTime.type !== 'D'))">
            <tr v-for="(item, index) in dataByTime.accountPeriodList" :key="index">
              <td>{{cutLen(item["beginDate"] , timeLenLimit)}} {{dataByTime.type === 'A' ? " ~ " +  cutLen( item["endDate"] , timeLenLimit ):''}}</td>
              <td>{{parseFloat(item["previousBalance"]).toFixed(2)}}</td>
              <td> {{ item.orgName || ''}} </td>
              <td>{{parseFloat(item["credit"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["debit"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["balance"]).toFixed(2)}}</td>
              <td>
                <span class="ty-color-blue" @click="showDetailList(item, monthOrDay, true)">查看</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <!-- 按账户查询 -->
        <div id="dataByAccount" v-if="mainNum === 1 && searchMethod === 1">
          <table class="ty-table ty-table-control">
            <thead>
            <td class='textLeft' width="25%" >账户</td>
            <td class="belongOrg">所属机构</td>
            <td id="ac1" width="12%">{{acTtl.td1}}</td>
            <td id="ac2" width="12%">{{acTtl.td2}}</td>
            <td id="ac3" width="12%">{{acTtl.td3}}</td>
            <td id="ac4" width="12%">{{acTtl.td4}}</td>
            <td width="16%">操作</td>
            </thead>
            <tbody id="dataByAccount_btl">
            <tr v-for="(item, index) in dataByAccount.accountPeriod1" :key="index">
              <td class='textLeft'>现金/备用金</td>
              <td> {{ item.orgName || ''}} </td>
              <td>{{parseFloat(item["previousBalance"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["credit"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["debit"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["balance"]).toFixed(2)}}</td>
              <td>
                <span class="ty-color-blue" @click="showDetailList(item, monthOrDay, true)">查看</span>
              </td>
            </tr>
            <tr v-if="!dataByAccount.accountPeriod1">
              <td class='textLeft'>现金/备用金</td>
              <td> 总机构</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>
                <span class="ty-color-blue" @click="showDetailList(item, monthOrDay, true)">查看</span>
              </td>
            </tr>
            <tr v-if="dataByAccount.accountPeriod2">
              <td class='textLeft'>银行账户汇总</td>
              <td> {{ dataByAccount.accountPeriod2.orgName || ''}} </td>
              <td>{{parseFloat(dataByAccount.accountPeriod2["previousBalance"]).toFixed(2)}}</td>
              <td>{{parseFloat(dataByAccount.accountPeriod2["credit"]).toFixed(2)}}</td>
              <td>{{parseFloat(dataByAccount.accountPeriod2["debit"]).toFixed(2)}}</td>
              <td>{{parseFloat(dataByAccount.accountPeriod2["balance"]).toFixed(2)}}</td>
              <td>
                <span class="ty-color-blue" @click="showDetailList(dataByAccount.accountPeriod2, monthOrDay, true)">查看</span>
              </td>
            </tr>
            <tr v-else>
              <td class='textLeft'>银行账户汇总</td>
              <td>总机构</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>0</td>
              <td>
                <span class="ty-color-blue" @click="showDetailList(dataByAccount.accountPeriod2, monthOrDay, true)">查看</span>
              </td>
            </tr>
            <tr v-for="(item, index) in dataByAccount.accountPeriod3" :key="index">
              <td class='ind2'>{{item.isPublic == 1 ? '': item.accountRealName }} {{formatAccount(item["accountName"])}} {{item["bankName"]}}</td>
              <td> {{ item.orgName || ''}} </td>
              <td>{{parseFloat(item["previousBalance"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["credit"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["debit"]).toFixed(2)}}</td>
              <td>{{parseFloat(item["balance"]).toFixed(2)}}</td>
              <td>
                <span class="ty-color-blue" @click="showDetailList(item, monthOrDay, true)">查看</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="dataInfo" v-if="mainNum === 3 || (searchLevel === 5 && DIYType === 'D')">
        <div>
          <!--   1-来源于报销数据的现金/银行转账信息  -->
          <div id="detailKind_1" v-if="fundDetail.kind === 1">
            <div class="bonceCon">
              <div class="approvCon applyYes" id="approvCon">
                <div class='processTr' v-for="(item, index) in fundDetail.content['approvalProcessHashSet']" :key="index" :class="{'noProcess': Number(item.approveStatus) === 3}">
                  <span v-if="[2,5].indexOf(Number(item.approveStatus)) > -1" class='ok-icon'></span>
                  <span v-if="Number(item.approveStatus) === 3" class='no-icon'></span>
                  <span v-if="[1,4].indexOf(Number(item.approveStatus)) > -1" class='wait-icon'></span>
                  <span class='touserInfo'>处理人 ：
                    <span v-show="Number(item.approveStatus) === 1">待处理</span>
                    <span v-show="Number(item.approveStatus) === 2">已批准</span>
                    <span v-show="Number(item.approveStatus) === 3">已驳回</span>
                    <span v-show="Number(item.approveStatus) === 4">待两讫</span>
                    <span v-show="Number(item.approveStatus) === 5">已报销</span>
                    / {{ item["toUserName"]}}</span>
                  <span v-if="[2,3,5].indexOf(Number(item.approveStatus)) > -1" class='timeInfo'>处理时间 ：{{item["handleTime"]}}</span>
                  <p v-show="Number(item.approveStatus) === 3" class='memoInfo'>回复内容 ： {{item["approveMemo"]}}</p>
                </div>
              </div>
              <!--同步传输的表单-->
              <table class="fundInfoShow table table-bordered " style="margin-top:30px; ">
                <div class="hd bigImag"></div>
                <tbody>
                <tr> <td width="30%" class="applyTtl">申请人</td><td width="70%" id="applyName">{{ fundDetail.content.createName}}</td> </tr>
                <tr> <td class="applyTtl">申请时间</td><td id="applyTime"> {{ new Date(fundDetail.content.createDate).format('yyyy-MM-dd hh:mm:ss')}}</td> </tr>
                <tr> <td class="applyTtl">费用类别</td><td id="applyKind">
                  <span class="scantd" id="scan_feeCatName_1">{{ fundDetail.content.feeCatName}}</span>
                </td> </tr>
                <tr> <td class="applyTtl">票据种类</td><td id="applyCat">
                  <span class="scantd" id="scan_billCatName_1" >{{ fundDetail.content.billCatName}}</span>
                </td>
                </tr>
                <tr> <td class="applyTtl">票据所属月份</td><td id="applyMonth">
                  <span class="scantd" id="scan_billDate_1">{{ chargeBillPeriod(fundDetail.content.billDate) }}</span>
                </td>
                </tr>
                <tr> <td class="applyTtl">摘要</td><td>
                  <span class="scantd" id="scan_summery_1" >{{ fundDetail.content.summary}}</span>
                </td> </tr>
                <tr> <td class="applyTtl">用途</td><td>
                  <span class="scantd" id="scan_purpose_1">{{ fundDetail.content.purpose}}</span>
                </td> </tr>
                <tr> <td class="applyTtl">票据数量</td><td >
                  <span class="scantd" id="scan_billAccount_1">{{ fundDetail.content.billQuantity}}</span>
                </td> </tr>
                <tr> <td class="applyTtl">实际金额</td><td>
                  <span class="scantd" id="scan_amount_1">{{ fundDetail.content.amount}}</span>
                </td> </tr>
                <tr> <td class="applyTtl">发票金额</td><td>
                  <span class="scantd" id="scan_billAmount_1">{{ fundDetail.content.billAmount}}</span>
                </td> </tr>
                <tr> <td class="applyTtl">备注</td><td>
                  <span class="scantd"  id="scan_memo_1">{{ fundDetail.content.memo}}</span>
                </td> </tr>
                <tr style="position:relative; " >
                  <td class="applyTtl">附件<div id="bigImagcon"><img id="bigImag" src=""></div></td>
                  <td>
                    <div id="info_images">
                      <div class="imgBox" v-for="(item, index) in fundDetail.content['personnelReimbursetAttachmentHashSet']" :key="index">
                        <a :href="rootPath.fileUrl+item.path" target="_blank"><img :src="rootPath.fileUrl+item.path"></a>
                      </div>
                    </div>
                  </td>
                </tr>
                </tbody>
              </table>
              <div class="itemTr">
                <span class="ttl"></span>
                <span class="con text-right">
                                    </span>
              </div>
            </div>
          </div>
          <!--   2-项目为收入的现金/银行转账信息      -->
          <div id="detailKind_2" v-if="fundDetail.kind === 2">
            <div class="bonceCon1">
              <div class="itemTr">
                <span class="ttl">项目</span>
                <span class="con">
                                        <span id="scan_type_2" class="scan">收入</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">类别</span>
                <span class="con">
                                        <span class="scan" id="scan_feeCat_2">{{chargeGenre(fundDetail.content.genre)}}</span>
                                    </span>
              </div>
              <div class="itemTr" id="genre_2">
                                    <span class="con">
                                        <span class="scan" id="scan_categoryDesc_2" v-if="Number(fundDetail.content.genre) === 5">
                                          {{fundDetail.content.categoryDesc}}
                                        </span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据种类</span>
                <span class="con ">
                                        <span class="mark"></span>
                                        <span class="scan" id="scan_billCat_2"></span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据所属月份</span>
                <span class="con" disabled >
                                        <span class="mark"></span>
                                        <span class="scan" id="scan_billMonth_2"></span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">摘要</span>
                <span class="con"><span class="scan" id="scan_summery_2">{{fundDetail.content.summary}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据数量</span>
                <span class="con" disabled >
                                        <span class="scan" id="scan_billAccount_2" style="background:rgba(0,0,0,0.15)"></span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">金额</span>
                <span class="con"><span class="scan" id="scan_amount_2">{{fundDetail.content.credit}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">所开具发票或收据的金额</span>
                <span class="con"><span class="scan" id="scan_billAmount_2">{{fundDetail.content.billAmount}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">用途</span>
                <span class="con"><span class="scan" id="scan_purpose_2">{{fundDetail.content.purpose}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">经手人</span>
                <span class="con"><span class="scan" id="scan_auditorName_2">{{fundDetail.content.auditorName}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">收入方式</span>
                <span class="con"><span class="scan" id="scan_method_2">{{ chargeMethod(fundDetail.content['method']) }}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">付款单位</span>
                <span class="con"><span class="scan" id="scan_oppositeCorp_2">{{ fundDetail.content['oppositeCorp'] }}</span>
                </span>
              </div>
              <div id="update_method5_2" v-if="Number(fundDetail.content['method']) === 5">
                <div class="itemTr">
                  <span class="ttl">到账时间</span>
                  <span class="con"><span class="scan" id="scan_receiveAccountDate_2">{{ new Date(fundDetail.content["receiveAccountDate"]).format('yyyy-MM-dd') }}</span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">收款银行</span>
                  <span class="con"><span class="scan" id="scan_accountBank_2">{{ fundDetail.content['accountBank'] }}</span>
                  </span>
                </div>
              </div>
              <div class="itemTr">
                <span class="ttl">备注</span>
                <span class="con"><span class="scan" id="scan_memo_2">{{ fundDetail.content['memo'] }}</span></span>
              </div>
              <div class="itemTr">
                <span class="ttl"></span>
                <span class="con text-right"></span>
              </div>
            </div>
          </div>
          <!-- 3-项目为支出的现金/银行转账信息 -->
          <div id="detailKind_3" v-if="Number(fundDetail.kind) === 3">
            <div class="bonceCon1" v-if="Number(fundDetail.content.subType) !== 5">
              <div class="itemTr">
                <span class="ttl">数据类别</span>
                <span class="con">
                                        <span id="scan_type_3" class="scan">支出</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">支出类别</span>
                <span class="con">
                                            <span class="scan" id="scan_exp_3">{{ fotmatExpType(fundDetail.content.subType) }}</span>
                                        </span>
              </div>
              <div class="oral1" v-if="Number(fundDetail.content.subType) !== 6">
                <div class="itemTr">
                  <span class="ttl">票据日期</span>
                  <span class="con">
                    <span class="scan" id="scan_billDate_3">{{new Date(fundDetail.content.billDate ).format("yyyy-MM-dd")}}</span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">用途</span>
                  <span class="con">
                    <span class="scan" id="scan_purpose_3">{{fundDetail.content.purpose}}</span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">摘要</span>
                  <span class="con">
                    <span class="scan" id="scan_summery_3">{{fundDetail.content.summary}}</span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">票据数量</span>
                  <span class="con">
                    <span class="scan" id="scan_billAccount_3">{{fundDetail.content.billQuantity}}</span>
                  </span>
                </div>
                <div id="scan_imgs_32">
                  <div class="cp_img_box" v-for="(item, index) in fundDetail['financeAccountBillImages']" :key="index">
                    <div class="fileType" :style="'background-image: url('+ rootPath.fileUrl + item.uplaodPath +')'">
                      <a class="ww" :path="item.uplaodPath" @click="seeOnline($event)">预览</a>
                    </div>
                  </div>
                </div>
                <div class="itemTr" id="billAmountTr" v-if="Number(fundDetail.content.subType) !== 7">
                  <span class="ttl">票据金额合计</span>
                  <span class="con">
                                        <span class="scan" id="scan_billAmount_3">{{fundDetail.content.billAmount}} </span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl" id="actMoney">{{ Number(fundDetail.content.subType) === 7?'支出金额':'实际金额合计' }}</span>
                  <span class="con">
                                        <span class="scan" id="scan_amount_3">{{fundDetail.content.debit }}</span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl" id="actPayDate">{{ Number(fundDetail.content.subType) === 7?'支出日期':'实际付款日期' }}</span>
                  <span class="con">
                                        <span class="scan" id="scan_factDate_3">{{new Date(fundDetail.content.factDate).format("yyyy-MM-dd") }}</span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">经手人</span>
                  <span class="con">
                                        <span class="scan" id="scan_auditorName_3">{{fundDetail.content.auditorName}} </span>
                                    </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">支出方式</span>
                  <span class="con">
                                                <span id="scan_method_3" class="scan"> {{chargeMethod(fundDetail.content.method)}}</span>
                                            </span>
                </div>
                <div id="update_method5_3" v-if="fundDetail.content['method'] == 5">
                  <div class="itemTr">
                    <span class="ttl">转账银行</span>
                    <span class="con">
                                                    <span class="scan" id="scan_accountBank_3">{{fundDetail.content.accountBank}}</span>
                                                </span>
                  </div>
                </div>
                <div class="itemTr stakeholderCategoryContsiner">
                  <span class="ttl">收款单位</span>
                  <span class="con poReL">
                                            <span class="scan stakeholderCategoryText" id="scan_stakeholderCategory_3">{{formatStakeholderCategory(fundDetail.content.stakeholderCategory)}}</span>
                                        </span>
                </div>
                <div class="itemTr " >
                  <span class="ttl"> </span>
                  <span class="con">
                                            <span class="scan" id="scan_oppositeCorp_3"> {{fundDetail.content.oppositeCorp}}</span>

                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">备注</span>
                  <span class="con">
                                                <span class="scan" id="scan_memo_3"> {{fundDetail.content.memo}}</span>
                                            </span>
                </div>
                <div class="itemTr">
                  <span class="ttl"></span>
                  <span class="con text-right">
                                            </span>
                </div>
              </div>
              <!-- 汇划费 的查看 -->
              <div class="oral2" v-if="Number(fundDetail.content.subType) === 6">
                <div class="itemTr">
                  <span class="ttl">支出日期</span>
                  <span class="con">
                                                <span class="scan" id="scan_paydate_3">{{new Date(fundDetail.content.factDate).format("yyyy-MM-dd")}} </span>
                                            </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">用途/摘要</span>
                  <span class="con">
                                                <span class="scan" id="scan_summaryPurpose_3">{{fundDetail.content.summary}} </span>
                                            </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">支出金额</span>
                  <span class="con">
                                                <span class="scan" id="scan_payAmount_3"> {{fundDetail.content.debit}}</span>
                                            </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">开户行</span>
                  <span class="con">
                                                <span class="scan" id="scan_payBank_3">{{fundDetail.content.accountBank}} </span>
                                            </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">凭证照片</span>
                  <span class="con">（最多可上传九张）</span>
                </div>
                <div class="itemTr">
                  <span class="ttl">&nbsp;</span>
                  <span class="con">
                    <div id="scan_imgs_3">
                      <div class="cp_img_box" v-for="(item, index) in fundDetail['financeAccountBillImages']" :key="index">
                        <div class="fileType" :style="'background-image: url('+ rootPath.fileUrl + item.uplaodPath +')'">
                          <a class="ww" :path="item.uplaodPath" @click="seeOnline($event)">预览</a>
                        </div>
                        </div>
                    </div>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">备注</span>
                  <span class="con">
                    <div class="scan" id="scan_payMemo_3">{{fundDetail.content.memo}} </div>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl"></span>
                  <span class="con text-right"></span>
                </div>
              </div>
            </div>
            <!--  税款录入-->
            <div id="rateEntryFrm" class="bonceCon2" v-if="Number(fundDetail.content.subType) === 5">
              <div class="scan000">
                <p><span class="ttl30">税种</span><span class="ratename">{{fundDetail.content.taxName}}</span></p>
                <p><span class="ttl30">税款所属时期</span><span class="ratenDur">{{fundDetail.content.businessPeriodBegin}} - {{fundDetail.content.businessPeriodEnd}}</span></p>
                <p><span class="ttl30">实际缴纳日期</span><span class="rateFactDate">{{new Date(fundDetail.content.factDate).format("yyyy-MM-dd")}}</span></p>
                <p><span class="ttl30">实缴金额</span><span class="rateAmount">{{fundDetail.content.debit}}</span></p>
                <p><span class="ttl30">支出方式</span><span class="rateMethod">{{fundDetail.content.method == 1 ? '现金支付' : '转账支付-'+fundDetail.content.accountBank}}</span></p>
                <p><span class="ttl30">缴税凭证照片</span><span class="ratePic">
                   <div class="cp_img_box" v-for="(item, index) in fundDetail['financeAccountBillImages']" :key="index">
                     <div class="fileType" :style="'background-image: url('+ rootPath.fileUrl + item.uplaodPath +')'">
                    <a class="ww" :path="item.uplaodPath" @click="seeOnline($event)">预览</a>
                    </div>
                        </div>
                </span></p>
                <p><span class="ttl30">申报记录</span><span class="rateReportLog">需申报的起止日期为{{new Date(fundDetail.content.beginDate).format("yyyy-MM-dd")}}至{{new Date(fundDetail.content.endDate).format("yyyy-MM-dd")}}的申报记录</span></p>
                <p><span class="ttl30">备注</span><span class="rateMemo">{{fundDetail.content.memo}}</span></p>
              </div>
            </div>

          </div>
          <!-- 4-现金支票/内部转账支票的信息  -->
          <div id="detailKind_3_" v-if="Number(fundDetail.kind) === 4">
            <div class="bonceCon1">
              <div class="itemTr">
                <span class="ttl">数据类别</span>
                <span class="con">
                                        <span id="scan_type_3_" class="scan">支出</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">支出类别</span>
                <span class="con ">
                  <span class="scan" id="scan_exp_3_">{{fotmatExpType(fundDetail.content.subType)}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据日期</span>
                <span class="con "  >
                                            <span class="scan" id="scan_billDate_3_">{{new Date(fundDetail.content.billDate ).format("yyyy-MM-dd")}}</span>
                                        </span>
              </div>
              <div class="itemTr">
                <span class="ttl">摘要</span>
                <span class="con">
                                        <span class="scan" id="scan_summery_3_">{{fundDetail.content.summary}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">用途</span>
                <span class="con">
                                        <span class="scan" id="scan_purpose_3_">{{fundDetail.content.purpose}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据数量</span>
                <span class="con">
                                        <span class="scan" id="scan_billAccount_3_">{{fundDetail.content.billQuantity}}</span>
                </span>
              </div>
              <div id="scan_imgs_3_">
                <div class="cp_img_box" v-for="(item, index) in fundDetail['financeAccountBillImages']" :key="index">
                  <div class="fileType" :style="'background-image: url('+ rootPath.fileUrl + item.uplaodPath +')'">
                    <a class="ww" :path="item.uplaodPath" @click="seeOnline($event)">预览</a>
                  </div>
                </div>
              </div>
              <div class="itemTr">
                <span class="ttl">票据金额合计</span>
                <span class="con">
                                        <span class="scan" id="scan_billAmount_3_">{{fundDetail.content.billAmount}} </span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">实际金额合计</span>
                <span class="con">
                                        <span class="scan" id="scan_amount_3_">{{fundDetail.content.amount}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">实际付款日期</span>
                <span class="con">
                                        <span class="scan" id="scan_factDate_3_"> {{new Date(fundDetail.financeAccountBill.factDate).format("yyyy-MM-dd")}}</span>
                </span>
              </div>

              <div class="itemTr">
                <span class="ttl">经手人</span>
                <span class="con">
                                        <span class="scan" id="scan_auditorName_3_">{{fundDetail.content.financialHandling}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">支出方式</span>
                <span class="con">
                                        <span id="scan_method_3_" class="scan">{{Number(fundDetail.content.type) === 1 ? '转账支票':'现金支票'}} </span>
                                    </span>
              </div>
              <div class="update_method_3_1">
                <div class="itemTr">
                  <span class="ttl"> </span>
                  <span class="con">
                                            <span id="scan_method_3_2" class="scan">内部支票 </span>
                                        </span>
                </div>
                <div class="update_method_3_1_1">
                  <div class="itemTr">
                    <span class="ttl">银行账户</span>
                    <span class="con">
                                                <span id="scan_bankAccount_3_" class="scan">{{fundDetail.content.bankName}} {{fundDetail.content.account}}</span>
                                            </span>
                  </div>
                  <div class="itemTr">
                    <span class="ttl">支票号</span>
                    <span class="con">
                                                <span id="scan_checkNo_3_" class="scan">{{fundDetail.content.chequeNo}} </span>
                                            </span>
                  </div>
                  <div class="itemTr">
                    <span class="ttl">支票到期日</span>
                    <span class="con">
                                                <span class="scan" id="scan_checkendDate_3_">{{new Date(fundDetail.content.expireDate).format('yyyy-MM-dd')}}</span>
                                            </span>
                  </div>
                  <div class="itemTr">
                    <span class="ttl">接收日期</span>
                    <span class="con">
                                                <span class="scan" id="scan_receiveDate_3_">{{new Date(fundDetail.content.receiveDate).format('yyyy-MM-dd')}} </span>
                                            </span>
                  </div>
                  <div class="itemTr">
                    <span class="ttl">接收经手人</span>
                    <span class="con">
                                                <span class="scan" id="scan_receivePerson_3_">{{fundDetail.content.receiver}} </span>
                                            </span>
                  </div>
                  <div class="itemTr">
                    <span class="ttl">支付经手人</span>
                    <span class="con">
                                                <span class="scan" id="scan_payPerson_3_">{{fundDetail.content.operator}} </span>
                                            </span>
                  </div>
                </div>
<!--                <div class="update_method_3_1_2">
                  <div class="itemTr">
                    <span class="ttl">支票号</span>
                    <span class="con">
                                            </span>
                  </div>
                </div>-->
              </div>
<!--              <div class="update_method_3_2" style="display:none">
                <div class="itemTr">
                  <span class="ttl">汇票号</span>
                  <span class="con">
                                    </span>
                </div>
              </div>-->

              <div class="itemTr stakeholderCategoryContsiner">
                <span class="ttl">收款单位</span>
                <span class="con poReL">
                                            <span class="scan stakeholderCategoryText" id="scan_stakeholderCategory_3_">{{formatStakeholderCategory(fundDetail.financeAccountBill.stakeholderCategory)}} </span>
                                        </span>
              </div>
              <div class="itemTr " >
                <span class="ttl"> </span>
                <span class="con">
                                            <span class="scan" id="scan_oppositeCorp_3_">{{fundDetail.financeAccountBill.oppositeCorp}} </span>

                                        </span>
              </div>

              <div class="itemTr">

                <span class="ttl">备注</span>
                <span class="con">
                                        <span class="scan" id="scan_memo_3_">{{fundDetail.content.memo}} </span>
                                    </span>
              </div>

              <div class="itemTr">
                <span class="ttl"></span>
                <span class="con text-right">
                                    </span>
              </div>
            </div>
          </div>
          <!-- 5- 承兑汇票/外部转账支票的信息 -->
          <div id="detailKind_5" v-if="Number(fundDetail.kind) === 5">
            <div class="bonceCon1">
              <div class="itemTr">
                <span class="ttl">项目</span>
                <span class="con">
                                        <span id="scan_type_5" class="scan">{{Number(fundDetail.content.billType) === 1?'收入':'支出'}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">类别</span>
                <span class="con">
                  <span id="scan_cat_5" class="scan">{{chargeGenre(fundDetail.content.category)}}</span>
                </span>
              </div>
              <div class="itemTr" v-if="Number(fundDetail.content.category) === 5">
                                    <span class="con">
                                        <span class="scan" id="scan_categoryDesc_5">{{fundDetail.content.categoryDesc}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据种类</span>
                <span class="con">
                                        <span id="scan_checkCat_5" class="scan" style="background:#eaeaea; "></span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据所属月份</span>
                <span class="con">
                                        <span id="scan_checkMonth_5" class="scan" style="background:#eaeaea; "></span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">摘要</span>
                <span class="con">
                                        <span class="scan" id="scan_summery_5">{{fundDetail.content.summary}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">票据数量</span>
                <span class="con">
                                        <span class="scan" id="scan_billAccount_5" style="background:#eaeaea; "> </span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">所开具发票或收据的金额</span>
                <span class="con">
                                        <span class="scan" id="scan_billAmount_5">{{fundDetail.content.billAmount}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">用途</span>
                <span class="con">
                                        <span class="scan" id="scan_purpose_5">{{fundDetail.content.purpose}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">经手人</span>
                <span class="con">
                                        <span class="scan" id="scan_auditorName_5">{{fundDetail.content.operatorName}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">收入方式</span>
                <span class="con">
                  <span id="scan_method_5" class="scan">{{Number(fundDetail.content.type) === 1?'转账支票':'承兑汇票'}}</span>
                </span>
              </div>
              <!--   转账支票   -->
              <div id="method_trans" v-if="Number(fundDetail.content.type) === 1">
                <div class="itemTr">
                  <span class="ttl">收到日期</span>
                  <span class="con">
                                            <span id="scan_recevDate_5" class="scan">{{new Date(fundDetail.content["receiveDate"]).format('yyyy-MM-dd')}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">付款单位</span>
                  <span class="con">
                                            <span id="scan_payCmp_5" class="scan">{{fundDetail.content.payer}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">支票号</span>
                  <span class="con">
                    <span id="scan_checkno_5" class="scan">{{fundDetail.content.returnNo}}</span>
                  </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">金额</span>
                  <span class="con">
                                        <span class="scan" id="scan_amount_5">{{fundDetail.content.amount}}</span>
                                    </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">到期日</span>
                  <span class="con">
                                            <span id="scan_endDate_5" class="scan">{{new Date(fundDetail.content["expireDate"]).format('yyyy-MM-dd')}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">出具的单位</span>
                  <span class="con">
                                            <span id="scan_origCmp_5" class="scan">{{fundDetail.content.originalCorp}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">出具的银行</span>
                  <span class="con">
                                            <span id="scan_origBank_5" class="scan">{{fundDetail.content.bankName}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">财务经手人</span>
                  <span class="con">
                                            <span id="scan_jingshou_5" class="scan">{{fundDetail.content.createName}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">录入时间</span>
                  <span class="con">
                                            <span id="scan_inputTime_5" class="scan">{{new Date(fundDetail.content["createDate"]).format('yyyy-MM-dd')}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">备注</span>
                  <span class="con">
                                            <span id="scan_memos_5" class="scan">{{fundDetail.content.memo}}</span>
                                        </span>
                </div>
              </div>
              <!-- 承兑汇票 -->
              <div id="method_receive" v-if="Number(fundDetail.content.type) === 2">
                <div class="itemTr">
                  <span class="ttl">收到日期</span>
                  <span class="con">
                                            <span id="scan_recevDate_5_1" class="scan">{{new Date(fundDetail.content["receiveDate"]).format('yyyy-MM-dd')}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">付款单位</span>
                  <span class="con">
                                            <span id="scan_payCmp_5_1" class="scan">{{fundDetail.content.payer}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">汇票号</span>
                  <span class="con">
                                            <span id="scan_checkno_5_1" class="scan">{{fundDetail.content.returnNo}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">金额</span>
                  <span class="con">
                                        <span class="scan" id="scan_amount_5_1">{{fundDetail.content.amount}}</span>
                                    </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">到期日</span>
                  <span class="con">
                                            <span id="scan_endDate_5_1" class="scan">{{new Date(fundDetail.content["expireDate"]).format('yyyy-MM-dd')}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">最初出具的单位</span>
                  <span class="con">
                                            <span id="scan_origCmp_5_1" class="scan">{{fundDetail.content.originalCorp}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">出具的银行</span>
                  <span class="con">
                                            <span id="scan_origBank_5_1" class="scan">{{fundDetail.content.bankName}}</span>
                                        </span>
                </div>
                <div class="itemTr">
                  <span class="ttl">备注</span>
                  <span class="con">
                                            <span id="scan_memos_5_1" class="scan">{{fundDetail.content.memo}}</span>
                                        </span>
                </div>
              </div>

              <div class="itemTr">
                <span class="ttl">存入银行</span>
                <span class="con">
                                        <span id="scan_cunBank_5" class="scan">{{fundDetail.content.saveBankName}} {{fundDetail.content.accout}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">存入时间</span>
                <span class="con">
                  <span id="scan_cunTime_5" class="scan">{{new Date(fundDetail.content["depositDate"]).format('yyyy-MM-dd')}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">存入经手人</span>
                <span class="con">
                                        <span id="scan_cunPerson_5" class="scan">{{fundDetail.content.depositorName}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">到账时间</span>
                <span class="con">
                                        <span id="scan_receTime_5" class="scan">{{new Date(fundDetail.content["receiveAccountDate"]).format('yyyy-MM-dd')}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl"></span>
                <span class="con text-right">
                                    </span>
              </div>
            </div>
          </div>
          <!-- 6-内部非支出性转账的三种-->
          <div id="detailKind_4" v-if="Number(fundDetail.kind) === 6 || Number(fundDetail.kind) === 7 || Number(fundDetail.kind) === 8">
            <div class="bonceCon1">
              <div class="itemTr">
                <span class="ttl">项目</span>
                <span class="con"><span class="scan">内部非支出性转账</span></span>
              </div>
              <div class="itemTr">
                <span class="ttl">转账类型</span>
                <span class="con"><span class="scan" id="scan_transKind_4">{{ chargeMethod(fundDetail.content.method) }}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl">付款账户</span>
                <span class="con">
                                        <span class="scan" id="scan_payAccount_4">{{Number(fundDetail.content.credit) === 0 ? fundDetail.content.accountBank:fundDetail.content.oppositeAccount}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">收款账户</span>
                <span class="con"><span class="scan" id="scan_receiveAccount_4">{{fundDetail.content.credit === ''? fundDetail.content.oppositeAccount:fundDetail.content.accountBank}}</span></span>
              </div>
              <div class="itemTr" id="checkCon" v-if="Number(fundDetail.kind) === 8">
                <span class="ttl">支票号</span>
                <span class="con">
                                        <span class="scan" id="scan_checkNo_4">{{fundDetail.content.chequeNo}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">转账金额</span>
                <span class="con">
                                        <span class="scan" id="scan_money_4">{{fundDetail.content.credit === ''? fundDetail.content.debit:fundDetail.content.credit}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">业务发生时期</span>
                <span class="con">
                                        <span class="scan" id="scan_auditDate_4">{{new Date(fundDetail.content.auditDate).format('yyyy-MM-dd')}}</span>
                                    </span>
              </div>
              <div class="itemTr">
                <span class="ttl">备注</span>
                <span class="con">
                  <span class="scan" id="scan_memo_4">{{fundDetail.content.memo}}</span>
                </span>
              </div>
              <div class="itemTr">
                <span class="ttl"></span>
                <span class="con text-right">
                                    </span>
              </div>
            </div>
          </div>
          <div id="detailKind_wage" v-if="wageShow" style=" width: 80%; margin: 30px auto;">
            <div>
              <div class="">
                <h3 class="ty-center" id="tt3">{{wageTtl.tt3}}</h3>
              </div>
              <div class="martop30">
                <div class="flexbox">
                  <div><span class="date_t">支出日期 </span><span class="date_i"></span>{{new Date(payHistoryDetail.personnelPayHistory.payTime).format("yyyy-MM-dd")}}</div>
                  <div><span class="number_t">职工人数 </span><span class="number_i">{{payHistoryDetail.mapList.length}}人</span></div>
                  <div style="flex: 2;"><span class="method_t">支出方式 </span>
                    <span class="method_i" v-if="Number(payHistoryDetail.personnelPayHistory.payWay) === 2">
                      {{ Number(payHistoryDetail.financeAccount.isPublic) === 1 ?
                        formatAccount(payHistoryDetail.financeAccount["account"])+ ' ' + payHistoryDetail.financeAccount["bankName"]:
                        bankNameStr = payHistoryDetail.financeAccount["name"] + ' ' + formatAccount(payHistoryDetail.financeAccount["account"])+ ' ' + payHistoryDetail.financeAccount["bankName"]
                      }}
                    </span>
                    <span v-else>现金支付</span>
                  </div>
                  <div><span class="amount_t">本月职工应缴个所税总额 </span><span class="amount_i">{{payHistoryDetail.personnelPayHistory.pay.toFixed(2)}}</span></div>
                </div>
                <div>
                  <span>创建：</span>
                  <span class="createD">{{payHistoryDetail.personnelPayHistory.createName}} {{ new Date(payHistoryDetail.personnelPayHistory.createDate).format("yyyy-MM-dd hh:mm:ss")}}</span>
                </div>
              </div>
              <table class="ty-table martop30 " id="scanSalary">
                <tbody>
                <tr>
                  <td width="12%">姓名</td>
                  <td width="15%">手机号</td>
                  <td width="30%">部门/岗位</td>
                  <td width="20%"><span class="placeTitle"></span>金额</td>
                </tr>
                <tr v-for="(item, index) in payHistoryDetail.mapList" :key="index">
                  <td>{{ item.userName }}</td>
                  <td>{{ item.mobile }}</td>
                  <td>{{ item.departName || '' }} {{ item.postName && item.departName ? '/' : ''  }} {{ item.postName || '' }}</td>
                  <td>{{ item.factPay.toFixed(2) }}</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <TyDialog v-if="historyLog" width="500" dialogTitle="修改记录" color="blue" :dialogHide="hideFun" :dialogName="'historyLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('historyLog')">取消</el-button>
      </template>
      <template #dialogBody>
        <table class="ty-table ty-table-control">
          <tbody>
          <tr>
            <td>资料状态</td>
            <td>修改的结果</td>
            <td>创建人/修改人</td>
          </tr>
          <tr v-for="(item, index) in financeHistoryList" :key="index">
            <td>{{ index === financeHistoryList.length -1 ? '原始信息' : '第'+ financeHistoryList.length - 1 - index+'次修改后' }}</td>
            <td class="ty-td-control"><span @click="logDetails(item)" class="ty-color-blue">查看</span></td>
            <td>{{ item.createName }} {{ index === financeHistoryList.length -1 ? new Date(item.createDate ).format("yyyy-MM-dd hh:mm:ss"): new Date(item.updateDate ).format("yyyy-MM-dd hh:mm:ss")  }} </td>
          </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>
    <TyDialog v-if="generalScan" width="1080" dialogTitle="综合查看" color="blue" :dialogHide="hideFun" :dialogName="'generalScan'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('generalScan')">取消</el-button>
      </template>
      <template #dialogBody>
        <div class="ordLoanDetail">
          <div class="item">
            <div class="col-md-4"><span class="item-title">出资方</span><span class="item-content lender">{{loanDetailData.loanDetail.lender}}</span></div>
            <div class="col-md-4"><span class="item-title">借款方</span><span class="item-content borrower">{{loanDetailData.loanDetail.borrower}}</span></div>
            <div class="col-md-4"><span class="item-title">本金金额</span><span class="item-content principalAmount">{{loanDetailData.loanDetail.principalAmount}}</span></div>
          </div>
          <div class="item">
                   <div class="col-md-4"><span class="item-title">名义利率</span><span class="item-content nominalRate">{{(loanDetailData.loanDetail.nominalRate * 100).toFixed(2) + '%'}}</span></div>
                   <div class="col-md-4">
                     <span class="item-title">本金型式</span><span class="item-content incomeMethod">{{chargeMethod(loanDetailData.loanDetail.incomeMethod)}}</span>
                   </div>
            <div v-if="loanDetailData.loanDetail.loanType == 2">
              <div class="col-md-4 tansCommon trans3" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 3">
                <span class="item-title">支票类别</span>
                <span class="item-content withinOrAbroad">{{chargeBillType(loanDetailData.loanDetail.withinOrAbroad)}}</span>
              </div>
              <div class="col-md-4 tansCommon trans4" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 4">
                <span class="item-title"><span>汇票号</span></span><span class=" item-content  billNo">{{loanDetailData.loanDetail.billNo}}</span> </div>
            </div>

                 </div>
          <div class="incomeMethodCon1 incomeMethodCon" v-if="loanDetailData.loanDetail.loanType == 2 || (loanDetailData.loanDetail.loanType ==1 && loanDetailData.loanDetail.incomeMethod == 1)">
<!--           转账支票/内部 -->
            <div class="inOrOut1 tansCommon " v-if="Number(loanDetailData.loanDetail.incomeMethod) === 3 && Number(loanDetailData.loanDetail.withinOrAbroad) === 1">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">银行账户</span> <span class=" item-content accountId"  >{{loanDetailData.loanDetail.accountId}}</span> </div>
                <div class="col-md-4"> <span class="item-title">支票号</span> <span class=" item-content billNo" >{{loanDetailData.loanDetail.billNo}}</span> </div>
                <div class="col-md-4"> <span class="item-title">支票到期日</span><span class=" item-content  billEndDate">{{new Date(loanDetailData.loanDetail.billEndDate).format('yyyy-MM-dd')}}</span> </div>
              </div>
            </div>
<!--            转账支票 /外部-->
            <div class="inOrOut0 tansCommon " v-if="Number(loanDetailData.loanDetail.incomeMethod) === 3 && Number(loanDetailData.loanDetail.withinOrAbroad) === 0">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">支票号</span> <span class="item-content billNo">{{loanDetailData.loanDetail.billNo}}</span> </div>
              </div>
            </div>
<!--            转账支票 /外部-->
            <div class="trans1 trans3 trans4 transFu item tansCommon trans" v-if="[1,3,4].indexOf(Number(loanDetailData.loanDetail.incomeMethod)) > -1">
              <div class="col-md-4 fu2" v-if="!(loanDetailData.loanDetail.loanType == 1 && Number(loanDetailData.loanDetail.incomeMethod) === 1)">
                <span class="item-title">付款日期</span>
                <span class=" item-content paymentDate">{{new Date(loanDetailData.loanDetail.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4 fu1" v-if="loanDetailData.loanDetail.loanType == 1 && Number(loanDetailData.loanDetail.incomeMethod) === 1">
                <span class="item-title">收款日期</span>
                <span class=" item-content paymentDate">{{new Date(loanDetailData.loanDetail.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4 fu2" v-if="!(loanDetailData.loanDetail.loanType == 1 && Number(loanDetailData.loanDetail.incomeMethod) === 1)">
                <span class="item-title">付款经手人</span>
                <span class=" item-content operatorName">{{loanDetailData.loanDetail.operatorName}}</span>
              </div>
              <div class="col-md-4 fu1 fu2">
                <span class="item-title">收款经手人</span>
                <span class=" item-content partnerName">{{loanDetailData.loanDetail.partnerName}}</span>

              </div>
            </div>
<!--            银行转账-->
            <div class="trans5 item tansCommon" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 5">
              <div class="col-md-4">
                <span class="item-title">付款日期</span>
                <span class=" item-content paymentDate">{{new Date(loanDetailData.loanDetail.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">付款银行</span>
                <span class=" item-content receiveBank">{{loanDetailData.loanDetail.receiveBank}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">付款经手人</span>
                <span class=" item-content operatorName">{{loanDetailData.loanDetail.operatorName}}</span>
              </div>
            </div>
<!--            银行转账-->
            <div class="trans21 item tansCommon" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 21">
              <div class="col-md-4">
                <span class="item-title">收款日期</span>
                <span class=" item-content paymentDate">{{new Date(loanDetailData.loanDetail.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">收款经手人</span>
                <span class=" item-content operatorName">{{loanDetailData.loanDetail.operatorName}}</span>
              </div>
            </div>
            <div class="trans5 item tansCommon" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 5">
              <div class="col-md-4">
                <span class="item-title">借款方账户名称</span>
                <span class=" item-content oppositeAccount">{{loanDetailData.loanDetail.oppositeAccount}}</span>

              </div>
              <div class="col-md-4">
                <span class="item-title">开户行</span>
                <span class=" item-content oppositeBankno">{{loanDetailData.loanDetail.oppositeBankno}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">账号</span>
                <span class=" item-content oppositeBankcode">{{loanDetailData.loanDetail.oppositeBankcode}}</span>
              </div>
            </div>
          </div>

          <div class="incomeMethodCon2 incomeMethodCon" v-if="Number(loanDetailData.loanDetail.loanType) === 1 && Number(loanDetailData.loanDetail.incomeMethod) !== 1">
            <div class="trans1 trans" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 1"></div>
            <!--转账支票/承兑汇票-->
            <div class="trans3 trans4" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 3 || Number(loanDetailData.loanDetail.incomeMethod) === 4">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">{{Number(loanDetailData.loanDetail.incomeMethod) === 3?'收到支票日期':'收到汇票日期'}}</span> <span class="item-content billReceiveDate">{{new Date(loanDetailData.loanDetail.billReceiveDate).format('yyyy-MM-dd')}}</span> </div>
                <div class="col-md-4"> <span class="item-title">{{Number(loanDetailData.loanDetail.incomeMethod) === 3?'支票号':'汇票号'}}</span> <span class="item-content billNo">{{loanDetailData.loanDetail.billNo}}</span> </div>
                <div class="col-md-4"> <span class="item-title">{{Number(loanDetailData.loanDetail.incomeMethod) === 3?'支票到期日':'汇票到期日'}}</span> <span class="item-content billEndDate">{{new Date(loanDetailData.loanDetail.billEndDate).format('yyyy-MM-dd')}}</span> </div>
              </div>
              <div class="item">
                <span class="col-md-4"> <span class="item-title">{{Number(loanDetailData.loanDetail.incomeMethod) === 3?'出具支票单位':'原始出具汇票单位'}}</span> <span class="item-content billSource">{{loanDetailData.loanDetail.billSource}}</span> </span>
                <span class="col-md-4"> <span class="item-title">{{Number(loanDetailData.loanDetail.incomeMethod) === 3?'出具支票银行':'出具汇票银行'}}</span> <span class="item-content billBank">{{loanDetailData.loanDetail.billBank}}</span> </span>
              </div>
            </div>
            <!--转账银行-->
            <div class="trans5" v-if="Number(loanDetailData.loanDetail.incomeMethod) === 5">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">到账日期</span> <span class="item-content arriveDate">{{new Date(loanDetailData.loanDetail.arriveDate).format('yyyy-MM-dd')}}</span> </div>
                <div class="col-md-8"> <span class="item-title">收款银行</span> <span style="width: 300px;" class="item-content receiveBank">{{loanDetailData.loanDetail.receiveBank}}</span></div>
              </div>
            </div>
          </div>

          <div class="item">
            <div class="col-md-4">
              <span class="item-title">归还本金的约定</span><span class="item-content repaymentDate">{{new Date(loanDetailData.loanDetail.repaymentDate).format('yyyy-MM-dd') === "" ?'未约定具体日期':new Date(loanDetailData.loanDetail.repaymentDate).format('yyyy-MM-dd')}}</span>
            </div>
          </div>

          <div class="item">
            <div class="col-md-8">
              <span class="item-title">利息的支付方式</span><span class="item-content interestMethod">{{chargeInterestMethod(loanDetailData.loanDetail.interestMethod)}}</span>
            </div>
          </div>
          <div>
            <div class="interest0 interest" v-if="Number(loanDetailData.loanDetail.interestMethod) === 0"></div>
            <div class="item interest2 interest3" v-if="[2,3].indexOf(Number(loanDetailData.loanDetail.interestMethod)) > -1">
              <div class="col-md-4"> <span class="item-title">开始计息日期</span><span class="item-content interestAccrualDate">{{new Date(loanDetailData.loanDetail.interestAccrualDate).format('yyyy-MM-dd')}}</span></div>
              <div class="col-md-4"> <span class="item-title">{{Number(loanDetailData.loanDetail.interestMethod) === 2?'每月还款日':'每年还款日'}}</span><span class="item-content periodRepaymentDate">{{loanDetailData.loanDetail.interestMethod}}</span></div>
              <div class="col-md-4"> <span class="item-title">每次应付金额</span><span class="item-content periodRepaymentAmount">{{loanDetailData.loanDetail.periodRepaymentAmount}}</span></div>
            </div>
          </div>
          <div class="item">
            <div class="col-md-12"><span class="item-title">备注</span><span class="item-content memo">{{loanDetailData.loanDetail.memo}}</span></div>
          </div>
        </div>
        <div class="recordInfo" style="margin-top: 8px">
          <div class="recordList">
            <div class="item">
            </div>
            <div class="item" v-for="(item, index) in loanDetailData.recordList" :key="index">
              <div v-if="index === 0" class="col-md-8"><span class="item-title">创建人</span><span style="width:300px; " class="item-content ">{{loanDetailData.recordList[0].createName}} {{new Date(loanDetailData.recordList[0].createDate).format('yyyy-MM-dd hh:mm:ss')}}</span></div>
              <div v-if="index === 0" class="col-md-4"><button :disabled="loanDetailData.borrorModList.length === 0" class="ty-btn ty-btn-big ty-btn-blue ty-circle-3 showHistory" @click="showHistory(1)">借款信息的修改记录</button></div>
              <p v-if="index >= 1" style="padding-left:88px;"> {{item.createName}}于{{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}
                {{item.state?'将本笔借款由“已完结”恢复至“尚需继续'+(loanDetailData.loanDetail.loanType === 2? '收款':'付款'):'将本笔借款操作为“已完结”。'}}</p>
            </div>
          </div>
        </div>
        <div class="repaymentList">
          <table class="ty-table ty-table-control">
            <thead>
            <tr>
              <td id="logttl0">已{{ Number(loanDetailData.loanDetail.loanType) === 2 ? '收款':'付款' }}金额</td>
              <td class="sumRepaymentAmount">{{loanDetailData.sumRepaymentAmount}}</td>
              <td ></td>
              <td>本金金额</td>
              <td class="principalAmount">{{loanDetailData.loanDetail.principalAmount}}</td>
            </tr>
            <tr>
              <td id="logttl1">{{ Number(loanDetailData.loanDetail.loanType) === 2 ? '收款':'付款' }}日期</td>
              <td id="logttl2">{{ Number(loanDetailData.loanDetail.loanType) === 2 ? '收款':'付款' }}金额</td>
              <td id="logttl3">{{ Number(loanDetailData.loanDetail.loanType) === 2 ? '收款':'支付' }}方式</td>
              <td>录入者</td>
              <td>操作</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item, index) in loanDetailData.repaymentList" :key="index">
              <td>{{new Date(item.repaymentTime).format('yyyy-MM-dd')}} </td>
              <td>{{item.repaymentAmount}}</td>
              <td>{{chargeMethod(item.repaymentMethod)}}</td>
              <td>{{item.createName}}  {{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</td>
              <td>
                <span class="ty-color-blue" @click="seeRepayBtn(item)">查看</span>
                <span class="ty-color-blue" @click="showHistory(2, item)">修改记录</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="collectDetailLog" width="660" dialogTitle="查看" color="blue" :dialogHide="hideFun" :dialogName="'collectDetailLog'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div id="collectDetailSee">
          <ul>
            <li>
              <p class="pTtl">客户名称</p>
              <p class="pCon" id="cltCustomer">{{collectDetailCus}}</p>
            </li>
            <li>
              <p class="pTtl">回款金额</p>
              <p class="pCon" id="cltAmount">{{formatMoney(collectDetail.amount)}}</p>
            </li>
          </ul>
          <div>
            <ul class="cltByCrah" v-if="Number(collectDetail.method) === 1"> <!--回款方式   1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐'-->
              <li>
                <p class="pTtl">收入方式</p>
                <p class="pCon">现金</p>
              </li>
              <li>
                <p class="pTtl">收到日期</p>
                <p class="pCon" id="cashRecive">{{new Date(collectDetail.receiveDate).format('yyyy/MM/dd')}}</p>
              </li>
            </ul>
            <ul class="cltByCheque" v-if="Number(collectDetail.method) === 3">
              <li>
                <p class="pTtl">收入方式</p>
                <p class="pCon">转账支票</p>
              </li>
              <li>
                <p class="pTtl">收到日期</p>
                <p class="pCon" id="cqRecive">{{new Date(collectDetail.receiveDate).format('yyyy/MM/dd')}}</p>
              </li>
              <li>
                <p class="pTtl">支票号</p>
                <p class="pCon" id="cqSn">{{collectDetail.returnNo}}</p>
              </li>
              <li>
                <p class="pTtl">到期日</p>
                <p class="pCon" id="cqDueDate">{{new Date(collectDetail.expireDate).format('yyyy/MM/dd')}}</p>
              </li>
              <li>
                <p class="pTtl">出具的单位</p>
                <p class="pCon" id="cqUnit">{{collectDetail.originalCorp}}</p>
              </li>
              <li>
                <p class="pTtl">出具的银行</p>
                <p class="pCon" id="cqBank">{{collectDetail.bankName}}</p>
              </li>
              <li>
                <p class="pTtl">存入银行</p>
                <p class="pCon" id="cqDepositBank">{{collectDetail.receiveBankName}}</p>
              </li>
              <li>
                <p class="pTtl">存入时间</p>
                <p class="pCon" id="cqDepositDate">{{new Date(collectDetail.depositDate).format('yyyy/MM/dd')}}</p>
              </li>
              <li>
                <p class="pTtl">存入经手人</p>
                <p class="pCon" id="cqDepositMan">{{collectDetail.depositorName}}</p>
              </li>
              <li>
                <p class="pTtl">到账时间</p>
                <p class="pCon" id="cqArriveTime">{{new Date(collectDetail.receiveAccountDate).format('yyyy/MM/dd')}}</p>
              </li>
            </ul>
            <ul class="cltByBill" v-if="Number(collectDetail.method) === 4">
              <li>
                <p class="pTtl">收入方式</p>
                <p class="pCon">承兑汇票</p>
              </li>
              <li>
                <p class="pTtl">收到日期</p>
                <p class="pCon" id="billRecive">{{new Date(collectDetail.receiveDate).format('yyyy/MM/dd')}}</p>
              </li>
              <li>
                <p class="pTtl">汇票号</p>
                <p class="pCon" id="billSn">{{collectDetail.returnNo}}</p>
              </li>
              <li>
                <p class="pTtl">到期日</p>
                <p class="pCon" id="billDueDate">{{new Date(collectDetail.expireDate).format('yyyy/MM/dd')}}</p>
              </li>
              <li>
                <p class="pTtl">最初出具的单位</p>
                <p class="pCon" id="billUnit">{{collectDetail.originalCorp}}</p>
              </li>
              <li>
                <p class="pTtl">出具的银行</p>
                <p class="pCon" id="billBank">{{collectDetail.bankName}}</p>
              </li>
              <li>
                <p class="pTtl">存入银行</p>
                <p class="pCon" id="billDepositBank">{{collectDetail.receiveBankName}}</p>
              </li>
              <li>
                <p class="pTtl">存入时间</p>
                <p class="pCon" id="billDepositDate">{{new Date(collectDetail.depositDate).format('yyyy/MM/dd')}}</p>
              </li>
              <li>
                <p class="pTtl">存入经手人</p>
                <p class="pCon" id="billDepositMan">{{collectDetail.depositorName}}</p>
              </li>
              <li>
                <p class="pTtl">到账时间</p>
                <p class="pCon" id="billArriveTime">{{new Date(collectDetail.receiveAccountDate).format('yyyy/MM/dd')}}</p>
              </li>
            </ul>
            <ul class="cltByBank" v-if="Number(collectDetail.method) === 5">
              <li>
                <p class="pTtl">收入方式</p>
                <p class="pCon">银行转账</p>
              </li>
              <li>
                <p class="pTtl">到账日期</p>
                <p class="pCon" id="bankRecive">{{new Date(collectDetail.expireDate).format('yyyy/MM/dd')}}</p>
              </li>
              <li>
                <p class="pTtl">收款银行</p>
                <p class="pCon" id="bankReciveBank">{{collectDetail.bankName}}</p>
              </li>
            </ul>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="reimburseLog" width="700" dialogTitle="报销查看" color="blue" :dialogHide="hideFun" :dialogName="'reimburseLog'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div class="processStep">
          <div class="line"><div><div></div></div></div>
          <div class="steps">
            <div class="stepItem">
              <div class="litCircle"><div>1</div></div>
              <div class="bigCircle">
                <p>提交报销申请</p>
                <p>申请人:{{reimbursePersonnel.createName}}</p>
                <p><small>{{(new Date(reimbursePersonnel.createDate).format("yyyy-MM-dd hh:mm:ss")) }}</small></p>
              </div>
            </div>
            <div class="stepItem" v-for="(item, index) in reimburseProcessList" :key="index">
              <div class="litCircle"><div>{{index * 1 + 2}}</div></div>
              <div class="bigCircle">
                <p v-if="Number(item.approveStatus) === 2">
                  <span v-if="Number(item.businessType) === 17">付款审批通过</span>
                  <span v-if="Number(item.businessType) === 20">付款复核通过</span>
                  <span v-if="Number(item.businessType) === 21">付款审批通过</span>
                  <span v-if="Number(item.businessType) === 22">付款方式修改</span>
                  <span v-else>审批通过</span>
                </p>
                <p v-if="Number(item.approveStatus) === 7">票据审批通过</p>
                <p v-if="Number(item.approveStatus) === 5">付款完成</p>
                <p v-if="Number(item.approveStatus) === 2">
                  <span v-if="Number(item.businessType) === 17 || Number(item.businessType) === 20 || Number(item.businessType) === 21">审批人：{{item.userName}}</span>
                  <span v-if="Number(item.businessType) === 22">出纳员：{{item.userName}}</span>
                  <span v-else>审批人：{{item.userName}}</span>
                </p>
                <p v-if="Number(item.approveStatus) === 7">出纳员：{{item.userName}}</p>
                <p v-if="Number(item.approveStatus) === 5">出纳员：{{item.userName}}</p>
                <p><small>{{new Date(item.handleTime).format("yyyy-MM-dd hh:mm:ss")}}</small></p>
              </div>
            </div>
          </div>
        </div>
        <div class="processList">
          <div class=""></div>
        </div>
        <div class="" style="margin-top: 12px">
          <div><div class="item-title"><b>摘要</b></div> <div class="item-content-big summary"></div></div>
          <div><div class="item-title"><b>用途</b></div> <div class="item-content-big purpose"></div>
          </div>
        </div>
        <div class="billDetail" style="margin-top: 12px">
          <table class="ty-table ty-table-control">
            <thead>
            <tr>
              <td>票据种类</td>
              <td>单张票据金额</td>
              <td>票据数量</td>
              <td>票据金额合计</td>
              <td>申请报销的金额</td>
              <td>查看</td>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td>增值税专用发票</td>
              <td>606.5</td>
              <td>1</td>
              <td>606.5</td>
              <td>606.5</td>
              <td>
                <span class="ty-color-blue">票据内容</span>
                <span class="ty-color-blue">票据图片</span>
              </td>
            </tr>
            <tr>
              <td>其他普通发票</td>
              <td>606.5</td>
              <td>1</td>
              <td>606.5</td>
              <td>606.5</td>
              <td>
                <span class="ty-color-blue">票据内容</span>
                <span class="ty-color-blue">票据图片</span>
              </td>
            </tr>
            <tr>
              <td>定额普通发票</td>
              <td>--</td>
              <td>1</td>
              <td>606.5</td>
              <td>606.5</td>
              <td>
                <span class="ty-color-blue">票据内容</span>
                <span class="ty-color-blue">票据图片</span>
              </td>
            </tr>
            <tr>
              <td>增值税普通发票</td>
              <td>606.5</td>
              <td>1</td>
              <td>606.5</td>
              <td>606.5</td>
              <td>
                <span class="ty-color-blue">票据内容</span>
                <span class="ty-color-blue">票据图片</span>
              </td>
            </tr>
            <tr>
              <td></td>
              <td></td>
              <td>81</td>
              <td>1615.6</td>
              <td>1600</td>
              <td></td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="payInfoLog" width="480" :dialogTitle="payInfoTtl" color="blue" :dialogHide="hideFun" :dialogName="'payInfoLog'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div>
          <div class="item">
            <span class="item-title payInfoTtl1">{{Number(this.loanDetailData.loanDetail.loanType) === 2? '收款金额':'付款金额'}}</span>
            <span class="item-content repaymentAmount">{{repaymentData.repaymentAmount}}</span>
          </div>
          <div class="item">
            <span class="item-title payInfoTtl2">{{Number(this.loanDetailData.loanDetail.loanType) === 2? '收款日期':'付款日期'}}</span>
            <span class="item-content repaymentTime">{{repaymentData.repaymentTime}}</span>
          </div>
          <div class="item">
            <span class="item-title payInfoTtl3">{{Number(this.loanDetailData.loanDetail.loanType) === 2? '收款方式':'支付方式'}}</span>
            <span class="item-content repaymentMethod">{{chargeMethod(repaymentData.repaymentMethod)}}</span>
          </div>

          <div class="optPay">
            <!-- 付款录入 转账支票 -->
            <!-- 转账支票 -->
            <div class="opt" v-if="Number(loanDetailData.loanDetail.loanType) !== 2 && Number(repaymentData['repaymentMethod']) !== 1">
              <div v-if="Number(repaymentData['repaymentMethod']) === 3">
                <div class="item opt3 opt31 opt32">
                  <span class="item-title"></span>
                  <span class="item-content billType">{{chargeBillType(repaymentData.billType)}}</span>
                </div>
                <!--内部支票 / 外部支票-->
                <div class="item opt31" v-if="repaymentData.billType === '1' || repaymentData.billType === '内部支票'">
                  <span class="item-title">银行账户</span>
                  <span class="item-content bank bankAccount" style="width:275px;">{{(repaymentData.accountBank || repaymentData.billBank) + ' ' + repaymentData.account}}</span>
                </div>
                <div class="item opt31" v-if="repaymentData.billType === '1' || repaymentData.billType === '内部支票'">
                  <span class="item-title">支票号</span>
                  <span class="item-content billNo">{{repaymentData.billNo}}</span>
                </div>
                <div class="item opt32" v-if="repaymentData.billType === '0' || repaymentData.billType === '外部支票'">
                  <span class="item-title chequeName">支票号</span>
                  <span class="item-content billNo">{{repaymentData.billNo}}</span>
                </div>
                <div class="item opt31" v-if="repaymentData.billType === '1' || repaymentData.billType === '内部支票'"><span class="item-title">支票到期日</span><span class="item-content billEndDate">{{repaymentData.billEndDate}}</span></div>
                <div class="item opt31" v-if="repaymentData.billType === '1' || repaymentData.billType === '内部支票'"><span class="item-title">接收日期</span><span class="item-content billReceiveDate">{{repaymentData.billReceiveDate}}</span></div>
                <div class="item opt31" v-if="repaymentData.billType === '1' || repaymentData.billType === '内部支票'"><span class="item-title">接收经手人</span><span class="item-content receiveOperatorName">{{repaymentData.receiveOperatorName}}</span></div>
                <div class="item opt31" v-if="repaymentData.billType === '1' || repaymentData.billType === '内部支票'"><span class="item-title">支付经手人</span><span class="item-content paymentOperatorName">{{repaymentData.paymentOperatorName}}</span></div>
              </div>
              <div class="item opt4" v-if="Number(repaymentData['repaymentMethod']) === 4">
                <span class="item-title chequeName">汇票号</span>
                <span class="item-content billNo">{{repaymentData.billNo}}</span>
              </div>
                <!-- 银行转账 -->
              <div class="item opt5" v-if="Number(repaymentData['repaymentMethod']) === 5">
                <span class="item-title">转账银行</span>
                <span class="item-content bank" style="width:275px">{{repaymentData.receiveBank}}</span>
              </div>
            </div>
            <!-- 收款录入 -->
            <div class="payx" v-if="Number(loanDetailData.loanDetail.loanType) === 2">
              <div class="payx1" v-if="Number(repaymentData.repaymentMethod) === 1"> <!--现金-->
                <div class="item"><span class="item-title" >收款经手人</span> <span class="item-content receiveOperatorName">{{repaymentData.receiveOperatorName}}</span></div>
              </div>
              <div class="payx3 payx4" v-if="[3,4].indexOf(Number(repaymentData.repaymentMethod)) > -1"> <!--转账支票 / 承兑汇票-->
                <div class="item"><span class="item-title pay1" >{{payTtl.pay1}}</span> <span class="item-content billReceiveDate" >{{repaymentData.billReceiveDate}}</span></div>
                <div class="item"><span class="item-title pay2" >{{payTtl.pay2}}</span> <span class="item-content billNo">{{repaymentData.billReceiveDate}}</span></div>
                <div class="item"><span class="item-title pay3" >{{payTtl.pay3}}</span> <span class="item-content billEndDate" >{{repaymentData.billEndDate}}</span></div>
                <div class="item"><span class="item-title pay4" >{{payTtl.pay4}}</span> <span class="item-content billSource">{{repaymentData.billSource}}</span></div>
                <div class="item"><span class="item-title pay5" >{{payTtl.pay5}}</span> <span class="item-content billBank">{{repaymentData.billBank}}</span></div>
              </div>
              <div class="payx5" v-if="Number(repaymentData.repaymentMethod) === 5">
                <div class="item"><span class="item-title" >到账日期</span> <span class="item-content arriveDate" >{{repaymentData.arriveDate}}</span></div>
                <div class="item"> <span class="item-title">收款银行</span> <span class="item-content receiveBank">{{repaymentData.receiveBank}}</span></div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="updateLog" width="580" dialogTitle="修改记录" color="blue" :dialogHide="hideFun" :dialogName="'updateLog'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <table class="ty-table ty-table-control">
          <thead>
          <td width="40%">修改时间</td>
          <td width="20%">修改人</td>
          <td width="20%">修改前</td>
          <td width="20%">修改后</td>
          </thead>
          <tbody>
          <tr v-for="(item, index) in updateRecordList" :key="index">
            <td>{{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</td>
            <td>{{item.createName}}</td>
            <td class="ty-td-control">
              <span class="hd">{{item.differents.join('-')}}</span>
              <span class="ty-color-blue btn" data-type="1" @click="showHistorySee(item, 1)">查看</span>
            </td>
            <td class="ty-td-control">
              <span class="hd">{{item.differents.join('-')}}</span>
              <span class="ty-color-blue btn" data-type="2" @click="showHistorySee(item, 2)">查看</span>
            </td>
            </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>
    <TyDialog v-if="borrowInfoLog" width="1080" :dialogTitle="borrowInfoTtl" color="blue" :dialogHide="hideFun" :dialogName="'borrowInfoLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('borrowInfoLog')">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div class="ordLoanDetail">
          <div class="item">
            <div class="col-md-4"><span class="item-title">出资方</span><span class="item-content lender" :class="{'redMark': markDis('lende')}">{{loanRecordCon.lender}}</span></div>
            <div class="col-md-4"><span class="item-title">借款方</span><span class="item-content borrower" :class="{'redMark': markDis('borrower')}">{{loanRecordCon.borrower}}</span></div>
            <div class="col-md-4"><span class="item-title">本金金额</span><span class="item-content principalAmount" :class="{'redMark': markDis('principalAmount')}">{{loanRecordCon.principalAmount}}</span></div>
          </div>
          <div class="item">
            <div class="col-md-4"><span class="item-title">名义利率</span><span class="item-content nominalRate" :class="{'redMark': markDis('nominalRate')}">{{(loanRecordCon.nominalRate * 100).toFixed(2)}}%</span></div>
            <div class="col-md-4">
              <span class="item-title">本金型式</span><span class="item-content incomeMethod" :class="{'redMark': markDis('incomeMethod')}">{{chargeMethod(loanRecordCon.incomeMethod)}}</span>
            </div>
            <div class="col-md-4 tansCommon trans3" v-if="(loanRecordCon.loanType == 2 || (loanRecordCon.loanType ==1 && loanRecordCon.incomeMethod == 1)) && [3, '3'].indexOf(loanRecordCon.incomeMethod) > -1">
              <span class="item-title">支票类别</span>
              <span class="item-content withinOrAbroad" :class="{'redMark': markDis('withinOrAbroad')}">{{loanRecordCon.withinOrAbroad}}</span>
            </div>
            <div class="col-md-4 tansCommon trans4" v-if="(loanRecordCon.loanType == 2 || (loanRecordCon.loanType ==1 && loanRecordCon.incomeMethod == 1)) && [4, '4'].indexOf(loanRecordCon.incomeMethod) > -1">
              <span class="item-title"><span>汇票号</span></span><span class=" item-content returnNo" :class="{'redMark': markDis('returnNo')}">{{loanRecordCon.returnNo}}</span> </div>
          </div>
          <!--借出的或者借入现金-->
          <div class="incomeMethodCon1 incomeMethodCon" v-if="loanRecordCon.loanType == 2 || (loanRecordCon.loanType ==1 && loanRecordCon.incomeMethod == 1)">
            <!--转账支票/内部 -->
            <div class="inOrOut1 tansCommon " v-if="loanRecordCon.withinOrAbroad == 1 && loanRecordCon.incomeMethod == 3">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">银行账户</span> <span class=" item-content accountBank" :class="{'redMark': markDis('accountBank')}"  >{{loanRecordCon.accountBank}}</span> </div>
                <div class="col-md-4"> <span class="item-title">支票号</span> <span class=" item-content returnNo"  :class="{'redMark': markDis('returnNo')}">{{loanRecordCon.returnNo}}</span> </div>
                <div class="col-md-4"> <span class="item-title">支票到期日</span><span class=" item-content expireDate" :class="{'redMark': markDis('expireDate')}">{{new Date(loanRecordCon.expireDate).format('yyyy-MM-dd')}}</span> </div>
              </div>
            </div>
            <!--转账支票 /外部 -->
            <div class="inOrOut0 tansCommon " v-if="loanRecordCon.withinOrAbroad == 0 && loanRecordCon.incomeMethod == 3">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">支票号</span> <span class="item-content returnNo" :class="{'redMark': markDis('returnNo')}"></span> </div>
              </div>
            </div>
            <!-- 付款信息 -->
            <div class="trans1 trans3 trans4 transFu item tansCommon trans" v-if="[1,'1',3,'3', 4, '4'].indexOf(loanRecordCon.incomeMethod) > -1">
              <div class="col-md-4 fu2" v-if="!(loanRecordCon.loanType ==1 && loanRecordCon.incomeMethod == 1)">
                <span class="item-title">付款日期</span>
                <span class=" item-content paymentDate" :class="{'redMark': markDis('paymentDate')}">{{new Date(loanRecordCon.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4 fu1" v-if="loanRecordCon.loanType ==1 && loanRecordCon.incomeMethod == 1">
                <span class="item-title">收款日期</span>
                <span class=" item-content paymentDate" :class="{'redMark': markDis('paymentDate')}">{{new Date(loanRecordCon.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4 fu2" v-if="!(loanRecordCon.loanType ==1 && loanRecordCon.incomeMethod == 1)">
                <span class="item-title">付款经手人</span>
                <span class=" item-content operatorName" :class="{'redMark': markDis('operatorName')}">{{new Date(loanRecordCon.operatorName).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4 fu1 fu2">
                <span class="item-title">收款经手人</span>
                <span class=" item-content partnerName" :class="{'redMark': markDis('partnerName')}">{{loanRecordCon.partnerName}}</span>

              </div>
            </div>
            <!--银行转账-->
            <div class="trans5 item tansCommon" v-if="Number(loanRecordCon.incomeMethod) === 5">
              <div class="col-md-4">
                <span class="item-title">付款日期</span>
                <span class=" item-content paymentDate" :class="{'redMark': markDis('paymentDate')}">{{new Date(loanRecordCon.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">付款银行</span>
                <span class=" item-content receiveBank" :class="{'redMark': markDis('receiveBank')}">{{loanRecordCon.receiveBank}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">付款经手人</span>
                <span class=" item-content operatorName" :class="{'redMark': markDis('operatorName')}">{{loanRecordCon.operatorName}}</span>
              </div>
            </div>
            <!--银行转账-->
            <div class="trans21 item tansCommon" v-if="Number(loanRecordCon.incomeMethod) === 21">
              <div class="col-md-4">
                <span class="item-title">收款日期</span>
                <span class=" item-content paymentDate" :class="{'redMark': markDis('paymentDate')}">{{new Date(loanRecordCon.paymentDate).format('yyyy-MM-dd')}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">收款经手人</span>
                <span class=" item-content receiveOperatorName" :class="{'redMark': markDis('receiveOperatorName')}">{{loanRecordCon.receiveOperatorName}}</span>
              </div>
            </div>
            <div class="trans5 item tansCommon" v-if="Number(loanRecordCon.incomeMethod) === 5">
              <div class="col-md-4">
                <span class="item-title">借款方账户名称</span>
                <span class=" item-content oppositeAccount" :class="{'redMark': markDis('oppositeAccount')}">{{loanRecordCon.oppositeAccount}}</span>

              </div>
              <div class="col-md-4">
                <span class="item-title">开户行</span>
                <span class=" item-content oppositeBankno" :class="{'redMark': markDis('oppositeBankno')}">{{loanRecordCon.oppositeBankno}}</span>
              </div>
              <div class="col-md-4">
                <span class="item-title">账号</span>
                <span class=" item-content oppositeBankcode" :class="{'redMark': markDis('oppositeBankcode')}">{{loanRecordCon.oppositeBankcode}}</span>
              </div>
            </div>
          </div>
          <!--// 借入的-->
          <div class="incomeMethodCon2 incomeMethodCon" v-if="Number(loanRecordCon.loanType) !== 2 && !(loanRecordCon.loanType ==1 && loanRecordCon.incomeMethod == 1)">
            <div class="trans1 trans" v-if="Number(loanRecordCon.incomeMethod) === 1"></div>
            <!--转账支票/承兑汇票-->
            <div class="trans3 trans4" v-if="[3,'3', 4, '4'].indexOf(loanRecordCon.incomeMethod) > -1">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">收到日期</span> <span class="item-content receiveDate" :class="{'redMark': markDis('receiveDate')}">{{new Date(loanRecordCon.receiveDate).format('yyyy-MM-dd')}}</span> </div>
                <div class="col-md-4"> <span class="item-title">{{Number(loanRecordCon.incomeMethod) === 3? '支票号': '汇票号'}}</span> <span class="item-content returnNo" :class="{'redMark': markDis('returnNo')}">{{loanRecordCon.returnNo}}</span> </div>
                <div class="col-md-4"> <span class="item-title">到期日</span> <span class="item-content expireDate" :class="{'redMark': markDis('expireDate')}">{{new Date(loanRecordCon.expireDate).format('yyyy-MM-dd')}}</span> </div>
              </div>
              <div class="item">
                <span class="col-md-4">
                  <span class="item-title">{{Number(loanRecordCon.incomeMethod) === 3? '出具的单位': '最初出具的单位'}}</span>
                  <span class="item-content originalCorp" :class="{'redMark': markDis('originalCorp')}">{{loanRecordCon.originalCorp}}</span>
                </span>
                <span class="col-md-4"> <span class="item-title">出具的银行</span> <span class="item-content bankName" :class="{'redMark': markDis('bankName')}">{{loanRecordCon.bankName}}</span> </span>
              </div>
            </div>
            <!--转账银行-->
            <div class="trans5" v-if="Number(loanRecordCon.incomeMethod) === 5">
              <div class="item">
                <div class="col-md-4"> <span class="item-title">到账日期</span> <span class="item-content arriveDate" :class="{'redMark': markDis('arriveDate')}">{{new Date(loanRecordCon.arriveDate).format('yyyy-MM-dd')}}</span> </div>
                <div class="col-md-8"> <span class="item-title">收款银行</span> <span style="width: 300px;" class="item-content receiveBank" :class="{'redMark': markDis('receiveBank')}">{{loanRecordCon.receiveBank}}</span></div>
              </div>
            </div>
          </div>

          <div class="item">
            <div class="col-md-4">
              <span class="item-title">归还本金的约定</span><span class="item-content repaymentDate" :class="{'redMark': markDis('repaymentDate')}">{{new Date(loanRecordCon.repaymentDate).format('yyyy-MM-dd') === "" ?'未约定具体日期':new Date(loanRecordCon.repaymentDate).format('yyyy-MM-dd')}}</span>
            </div>
          </div>
          <div class="item">
            <div class="col-md-8">
              <span class="item-title">利息的支付方式</span><span class="item-content interestMethod" :class="{'redMark': markDis('interestMethod')}">{{chargeInterestMethod(loanRecordCon.interestMethod)}}</span>
            </div>
          </div>
          <div>
            <div class="interest0 interest" v-if="Number(loanRecordCon.interestMethod) === 0"></div>
            <div class="item interest2 interest3" v-if="[3,'3', 2, '2'].indexOf(loanRecordCon.interestMethod) > -1">
              <div class="col-md-4"> <span class="item-title">开始计息日期</span><span class="item-content interestAccrualDate" :class="{'redMark': markDis('interestAccrualDate')}">{{new Date(loanRecordCon.interestAccrualDate).formate('yyyy-MM-dd')}}</span></div>
              <div class="col-md-4"> <span class="item-title">{{Number(loanRecordCon.interestMethod) === 2? '每月还款日': '每年还款日'}}</span><span class="item-content periodRepaymentDate" :class="{'redMark': markDis('periodRepaymentDate')}">{{loanRecordCon.periodRepaymentDate}}</span></div>
              <div class="col-md-4"> <span class="item-title">每次应付金额</span><span class="item-content periodRepaymentAmount" :class="{'redMark': markDis('periodRepaymentAmount')}">{{loanRecordCon.periodRepaymentAmount}}</span></div>
            </div>
          </div>
          <div class="item">
            <div class="col-md-12"><span class="item-title">备注</span><span class="item-content memo" :class="{'redMark': markDis('memo')}">{{loanRecordCon.memo}}</span></div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="payInfoUpdateLog" width="480" :dialogTitle="payLogTtl" color="blue" :dialogHide="hideFun" :dialogName="'payInfoUpdateLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideFun('payInfoUpdateLog')">关  闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <div class="item">
            <span class="item-title payInfoTtl1">{{Number(this.loanDetailData.loanDetail.loanType) === 2? '收款金额':'付款金额'}}</span>
            <span class="item-content repaymentAmount" :class="{'redMark': markRedFlag('repaymentAmount')}">{{loanRecordCon.repaymentAmount}}</span>
          </div>
          <div class="item">
            <span class="item-title payInfoTtl2">{{Number(this.loanDetailData.loanDetail.loanType) === 2? '收款日期':'付款日期'}}</span>
            <span class="item-content repaymentTime" :class="{'redMark': markRedFlag('repaymentTime')}">{{loanRecordCon.repaymentTime}}</span>
          </div>
          <div class="item">
            <span class="item-title payInfoTtl3">{{Number(this.loanDetailData.loanDetail.loanType) === 2? '收款方式':'支付方式'}}</span>
            <span class="item-content repaymentMethod" :class="{'redMark': markRedFlag('repaymentMethod')}">{{chargeMethod(loanRecordCon.repaymentMethod)}}</span>
          </div>

          <div class="optPay">
            <!-- 转账支票 -->
            <div class="opt" v-if="Number(loanDetailData.loanDetail.loanType) !== 2 && Number(loanRecordCon['repaymentMethod']) !== 1">
              <div class="item opt3 opt31 opt32" v-if="Number(loanRecordCon['repaymentMethod']) === 3">
                <span class="item-title"></span>
                <span class="item-content withinOrAbroad" :class="{'redMark': markRedFlag('withinOrAbroad')}">{{chargeBillType(loanRecordCon.withinOrAbroad)}}</span>
              </div>
              <!--内部支票 / 外部支票-->
              <div v-if="Number(loanRecordCon['repaymentMethod']) === 3 && (loanRecordCon.billType === '1' || loanRecordCon.withinOrAbroad === '内部支票')">
                <div class="item opt31">
                  <span class="item-title">银行账户</span>
                  <span class="item-content bank bankAccount" style="width:275px;" :class="{'redMark': markRedFlag('account')}">{{(loanRecordCon.accountBank || loanRecordCon.billBank) + ' ' + loanRecordCon.account}}</span>
                </div>
                <div class="item opt31">
                  <span class="item-title">支票号</span>
                  <span class="item-content returnNo" :class="{'redMark': markRedFlag('returnNo')}">{{loanRecordCon.returnNo}}</span>
                </div>
                <div class="item opt31"><span class="item-title">支票到期日</span><span class="item-content" :class="{'redMark': markRedFlag('expireDate')}">{{loanRecordCon.expireDate}}</span></div>
                <div class="item opt31"><span class="item-title">接收日期</span> <span class="item-content" :class="{'redMark': markRedFlag('receiveDate')}">{{loanRecordCon.receiveDate}}</span></div>
                <div class="item opt31"><span class="item-title">接收经手人</span><span class="item-content" :class="{'redMark': markRedFlag('receiver')}">{{loanRecordCon.receiver}}</span></div>
                <div class="item opt31"><span class="item-title">支付经手人</span><span class="item-content" :class="{'redMark': markRedFlag('operator')}">{{loanRecordCon.operator}}</span></div>
              </div>
              <div class="item opt32 opt4" v-if="loanRecordCon.billType === '0' || loanRecordCon.withinOrAbroad === '外部支票' || Number(loanRecordCon['repaymentMethod']) === 4">
                <span class="item-title chequeName">支票号</span>
                <span class="item-content returnNo" :class="{'redMark': markRedFlag('returnNo')}">{{loanRecordCon.returnNo}}</span>
              </div>
               <!-- 银行转账 -->
              <div class="item opt5" v-if="Number(loanRecordCon['repaymentMethod']) === 5">
                <span class="item-title">转账银行</span>
                <span class="item-content bank receiveBank" style="width:275px" :class="{'redMark': markRedFlag('receiveBank')}">{{loanRecordCon.receiveBank}}</span>
              </div>
            </div>
            <!-- 收款录入 -->
            <div class="payx" v-if="Number(loanDetailData.loanDetail.loanType) === 2">
              <div class="payx1" v-if="Number(loanRecordCon.repaymentMethod) === 1"> <!--现金-->
                <div class="item"><span class="item-title" >收款经手人</span> <span class="item-content receiveOperatorName" :class="{'redMark': markRedFlag('receiveOperatorName')}">{{loanRecordCon.receiveOperatorName}}</span></div>
              </div>
              <div class="payx3 payx4" v-if="[3,4].indexOf(Number(loanRecordCon.repaymentMethod)) > -1"> <!--转账支票 / 承兑汇票-->
                <div class="item"><span class="item-title pay1">{{payTtl.pay1}}</span> <span class="item-content" :class="{'redMark': markRedFlag('receiveDate')}">{{loanRecordCon.receiveDate}}</span></div>
                <div class="item"><span class="item-title pay2">{{payTtl.pay2}}</span> <span class="item-content" :class="{'redMark': markRedFlag('returnNo')}">{{loanRecordCon.returnNo}}</span></div>
                <div class="item"><span class="item-title pay3">{{payTtl.pay3}}</span> <span class="item-content" :class="{'redMark': markRedFlag('expireDate')}">{{loanRecordCon.expireDate}}</span></div>
                <div class="item"><span class="item-title pay4">{{payTtl.pay4}}</span> <span class="item-content" :class="{'redMark': markRedFlag('originalCorp')}">{{loanRecordCon.originalCorp}}</span></div>
                <div class="item"><span class="item-title pay5">{{payTtl.pay5}}</span> <span class="item-content" :class="{'redMark': markRedFlag('bankName')}">{{loanRecordCon.bankName}}</span></div>
              </div>
              <div class="payx5" v-if="Number(loanRecordCon.repaymentMethod) === 5">
                <div class="item"><span class="item-title" >到账日期</span> <span class="item-content arriveDate" :class="{'redMark': markRedFlag('arriveDate')}">{{loanRecordCon.arriveDate}}</span></div>
                <div class="item"> <span class="item-title">收款银行</span> <span class="item-content receiveBank" :class="{'redMark': markRedFlag('receiveBank')}">{{loanRecordCon.receiveBank}}</span></div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
  </div>
</template>
<script>
import * as api from "@/api/companyOverview"
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import auth from '@/sys/auth'
import { formatMoney } from "@/utils/formTest.js"
import {previewOrDownloadFun} from "@/utils/downloadFile";
import {getRootPathFile} from "@/api/api";

export default {
  data() {
    return {
      pageName: 'overviewIndexForAdmin',
      mainNum: 1,
      searchByOrg: '',
      searchLevel: 1, // 查询的日期：1-本日 ， 2-本月 ， 3-上月 ， 4-本年 ， 5-自定义查询
      searchMethod: 0,// 查询的方法：0-按时间查询 ， 1-按账户查询 , 2-按流水查询
      DIYStart: '',
      DIYEnd: '',
      DIYType: '',
      monthOrDay: 0,
      monthOrDayKind: 0,
      mainChart: {},
      dataByAccount: {},
      dataByTime: {},
      timeLenLimit: 0,
      dataByFlowList: [],
      dataByDayList: [],
      prevBalanceTtl: '',
      curGetTtl: '',
      curOutTtl: '',
      payInfoTtl: '收款查看',
      borrowInfoTtl: '查看',
      payLogTtl: '修改前',
      payInfoLog: false,
      borrowInfoLog: false,
      wageShow: false,
      payInfoUpdateLog: false,
      repaymentData:{},
      repaymentType:0, //0:查看收付款,1=修改记录的查看
      curBalanceTtl: '',
      fundDetail: {},
      collectDetail: {},
      payHistoryDetail: {},
      navTtl: {},
      collectDetailCus: {},
      loanRecordDetail: {},
      loanRecordCon: {},
      popoverVisible: false,
      searchOrgList: [],
      rootPath: { fileUrl :'',  ow365url :'',  uploadUrl :'',  webRoot :'' },
      tdTtl: {
        'td0': '日期',
        'td1': '昨日余额',
        'td2': '今日收入',
        'td3': '今日支出',
        'td4': '今日余额'
      },
      acTtl: {
        'td1': '上月余额',
        'td2': '本月收入',
        'td3': '本月支出',
        'td4': '本月余额'
      },
      wageTtl: {
        'date': '支出日期',
        'number': '职工人数',
        'method': '支出方式',
        'amount': '',
        'tt3': '',
        'placeTtl': ''
      },
      payTtl: {
        'pay1': '',
        'pay2': '',
        'pay3': '',
        'pay4': '',
        'pay5': ''
      },
      loanDetailData:{
        'borrorModList': [],// 借款修改列表，包含了修改前和修改后的数据，修改前的数据放在beforeInfo关键字里
        'loanDetail': {},// 借款详情
        'repaymentList': [],// 付款列表
        'recordList': [] // 借款启停用记录列表
      },
      reimburseProcessList:[],
      reimbursePersonnel:{},
      reimburseBillList:[],
      updateRecordList:[],
      navControlList:[],
      updateRecordKind: 1,
      useInfo: auth.getOrg(),
      historyLog: false,
      generalScan: false,
      collectDetailLog: false,
      reimburseLog: false,
      updateLog: false,
    }
  },
  components: {},
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  created() {
    initNav({mid: 'bga', name: '资金', pageName: this.pageName}, this.initCreate(), this)
  },
  mounted() {

  },
  methods: {
    formatMoney,
    initCreate(){
      this.searchByLevel(1)
      getRootPathFile()
          .then(res => {
            console.log('RootPath=', res.data)
            this.rootPath = res.data
          })
    },
    searchByLevel(level) {
      this.mainNum = 1
      this.searchLevel = level;
      if (level === '2' || level === 2) {
        this.searchMethod = 2;
      } else {
        this.searchMethod = 0;
      }
      this.showMainData();
    },
    searchByMethod(method){
      this.searchMethod = method ;
      this.showMainData();
    },
    searchByDIY(){
      if (this.DIYStart !== '' && this.DIYEnd !== '') {
        this.searchByLevel(5 );
      } else {
        this.$message("请输入时间范围")
      }
    },
    dropDownDIY(){
      this.popoverVisible = !this.popoverVisible
    },
    showMainData() {
      let oid = '';
      let level = this.searchLevel;
      let method = this.searchMethod;
      let searchOrg = this.searchByOrg
      // 执行数据查询  state 1-本日，2-本月，3-上月，-4-本年，5-自定义 （为5时多传这两个时间） beginDate 开始时间 endDate结束时间
      var json = {"state": level, "oid": oid};
      var beginDate = "", endDate = "";
      if (level === 5) {
        beginDate = this.DIYStart + " 00:00:00";
        endDate = this.DIYEnd + " 23:59:59";
        json = {"state": level, "beginDate": beginDate, "endDate": endDate, "oid": oid}
      }
      // 主页面统计的显示
      api.getAllAccountData(json).then(res => {
        let data = res.data.data;
        this.mainChart = data
        // data["allPreviousBalance"];           // 总上期余额
        // data["allCredit"];                               // 总收入
        // data["allDebit"];                                 // 总支出
        // data["allBalance"];                             // 总当前余额
      }).catch(err => {
        console.log('err=', err)
      })
      // 获取并显示明细数据
      if (method === 0) { // 按时间查询 ，
        api.getDataByTime(json).then(res => {
          let result = res.data.data;
          // type A-跨年，B-跨月，C-本月，D-本日 ， （本日的时候取 明细列表accountDetailList 其余都取accountPeriodList列表）
          let type = result["type"];
          let list = [];
          if (type == "D") {
            list = result["accountDetailList"];
          } else {
            list = result["accountPeriodList"];
          }
          // 显示页面
          switch (level) {
            case 1 :
              this.dataByLevel(level, method);
              this.dataByDayList = list
              break;
            case 2 :
            case 3 :
            case 4 :
              this.dataByLevel(level, method);
              this.dataByTime = result
              this.addList({"type": type, "level": level});
              break;
            case 5 :
              this.dataByLevel5(type, method);
              if (type == "D") {
                this.dataByDayList = list
              } else {
                this.dataByTime = result
                this.addList({"type": type, "level": level});
              }
              break;
            default :
              break;
          }
        }).catch(err => {
          console.log('err=', err)
        })
      } else if (method === 1) { // 按账户查询
        api.getDataByAccount(json).then(res => {
          let dataR = res.data.data
          let monthOrDay = 0;
          switch (level) {  // level : 1-本日，2-本月，3-上月，-4-本年，5-自定义    // // monthOrDay 1-年进月，2-月进日，3-日进明细
            case 1 :monthOrDay = 3;break;
            case 2 :monthOrDay = 2;
              break;
            case 3 :monthOrDay = 2;
              break;
            case 4 :monthOrDay = 1;
              break;
            case 5 :monthOrDay = "";
              break;
            default :
              break;
          }
          this.monthOrDay = monthOrDay
          //this.monthOrDayKind = monthOrDay
          let  beginDate = dataR["beginDate"];
          let  endDate = dataR["endDate"];
          let accountPeriod1 = dataR["accountPeriod1"]; // 现金账户的信息
          // {accountName:账号，bankName：银行名称，isBasic：是否为基本户，debit：支出，credit：收入，previousBalance:上期余额，balance：本期余额，accountId_：账户id}
          let accountPeriod2 = dataR["accountPeriod2"]; // 银行汇总账户的信息
          // {debit：支出，credit：收入，previousBalance:上期余额，balance：本期余额}；
          let accountPeriod3 = dataR["accountPeriod3"]; // 所有银行的信息
          // {accountName:账号，bankName：银行名称，isBasic：是否为基本户，debit：支出，credit：收入，previousBalance:上期余额，balance：本期余额，accountId_：账户id}
          if(accountPeriod1){
            for(let i =0;i<accountPeriod1.length;i++){
              accountPeriod1[i]["beginDate"] = beginDate ;
              accountPeriod1[i]["endDate"] = endDate ;
            }
          }
          if(accountPeriod2){
            accountPeriod2["beginDate"] = beginDate ;
            accountPeriod2["endDate"] = endDate ;
          }
          if(accountPeriod3){
            for(let i =0;i<accountPeriod3.length;i++){
              accountPeriod3[i]["beginDate"] = beginDate ;
              accountPeriod3[i]["endDate"] = endDate ;
            }
          }
          this.dataByAccount = dataR
          if (level == 5) {
            this.dataByLevel5(level, method);
          } else {
            this.dataByLevel(level, method);
          }
        }).catch(err => {
          console.log('err=', err)
        })
      } else if (method === 2) { // 按流水查询
        let resData = {"oid": oid}
        api.getDataByFlow(resData).then(res => {
          let flowList = res.data.data['accountDetails'] || [];
          this.dataByFlowList = flowList
          this.dataByLevel(level, method);
        }).catch(err => {
          console.log('err=', err)
        })
      }
      let scanStr = {level, method}
      this.navControlList[0] = scanStr
    },//展示主页面: 前四种
    dataByLevel(level, method) { // level 表示的是当前查询的等级
      this.mainNum = 1
      //this.monthOrDayKind = 0
      //$("#mainDataNav").show().siblings().hide();
      if (method == 0) { // 按时间查询
        switch (level) { // 1-本日 ， 2-本月 ， 3-上月 ， 4-本年
          case 1 :
            this.prevBalanceTtl = '昨日余额'
            this.curGetTtl = '今日收入'
            this.curOutTtl = '今日支出'
            this.curBalanceTtl = '当前余额'
            break;
          case 2 :
            this.prevBalanceTtl = '上月余额'
            this.curGetTtl = '本月收入'
            this.curOutTtl = '本月支出'
            this.curBalanceTtl = '本月余额'
            this.tdTtl = {
              'td0': '日期',
              'td1': '昨日余额',
              'td2': '今日收入',
              'td3': '今日支出',
              'td4': '今日余额'
            }
            break;
          case 3 :
            this.prevBalanceTtl = '上期余额'
            this.curGetTtl = '上月收入'
            this.curOutTtl = '上月支出'
            this.curBalanceTtl = '上月余额'
            this.tdTtl = {'td0': '日期', 'td1': '昨日余额', 'td2': '今日收入', 'td3': '今日支出', 'td4': '今日余额'}
            break;
          case 4 :
            this.prevBalanceTtl = '上期余额'
            this.curGetTtl = '本年收入'
            this.curOutTtl = '本年支出'
            this.curBalanceTtl = '本年余额'
            this.tdTtl = {'td0': '日期', 'td1': '上月余额', 'td2': '本月收入', 'td3': '本月支出', 'td4': '本月余额'}
            break;
          default :
            break;
        }
      } else if (method == 1) { // 按账户查询
        switch (level) { // 1-本日 ， 2-本月 ， 3-上月 ， 4-本年
          case 1 :
            this.acTtl = {'td1': '昨日余额', 'td2': '今日收入', 'td3': '今日支出', 'td4': '当前余额'}
            this.prevBalanceTtl = '昨日余额'
            this.curGetTtl = '今日收入'
            this.curOutTtl = '今日支出'
            this.curBalanceTtl = '当前余额'
            break;
          case 2 :
            this.acTtl = {'td1': '上月余额', 'td2': '本月收入', 'td3': '本月支出', 'td4': '本月余额'}
            this.prevBalanceTtl = '上月余额'
            this.curGetTtl = '本月收入'
            this.curOutTtl = '本月支出'
            this.curBalanceTtl = '本月余额'
            break;
          case 3 :
            this.acTtl = {'td1': '上期余额', 'td2': '上月收入', 'td3': '上月支出', 'td4': '上月余额'}
            this.prevBalanceTtl = '上期余额'
            this.curGetTtl = '上月收入'
            this.curOutTtl = '上月支出'
            this.curBalanceTtl = '上月余额'
            break;
          case 4 :
            this.acTtl = {'td1': '上期余额', 'td2': '本年收入', 'td3': '本年支出', 'td4': '本年余额'}
            this.prevBalanceTtl = '上期余额'
            this.curGetTtl = '本年收入'
            this.curOutTtl = '本年支出'
            this.curBalanceTtl = '本年余额'
            break;
          default :
            break;
        }
      } else if (method == 2) { // 按流水查询
        this.prevBalanceTtl = '上月余额'
        this.curGetTtl = '本月收入'
        this.curOutTtl = '本月支出'
        this.curBalanceTtl = '本月余额'
        this.tdTtl = {'td0': '时间', 'td1': '摘要', 'td2': '收入', 'td3': '支出', 'td4': '经手人'}
      }
    },
    dataByLevel5( type , method ){  // type : A-跨年，B-跨月，C-本月，D-本日 ， method : 1 按账户查询 ， 0 按时间查询
      this.DIYType = type
      if( method == 0 ){
        this.prevBalanceTtl = '上期余额'
        this.curGetTtl = '本期收入'
        this.curOutTtl = '本期支出'
        this.curBalanceTtl = '本期余额'
        switch( type ){
          case "C" :
          case "B" :
          case "A" :
            this.acTtl = {'td1': '上期余额', 'td2': '本期收入', 'td3': '本期支出', 'td4': '本期余额'}
            break ;
          default :
            break ;
        }
      }else if(method == 1){
        this.acTtl = {'td1': '上期余额', 'td2': '本期收入', 'td3': '本期支出', 'td4': '本期余额'}
      }

    },
    addList(obj) {
      let monthOrDay = obj.monthOrDay; // 1-年进月，2-月进日，3-日进明细
      var type = obj["type"];  // type A-跨年，B-跨月，C-本月，D-本日
      var level = obj["level"];  // 1-本日，2-本月，3-上月，-4-本年，5-自定义
      var timeLen = 4;

      if (monthOrDay) { // 有 monthOrDay
        switch (monthOrDay) {
          case 1 :
            timeLen = 7;
            monthOrDay = 2;
            break;
          case 2 :
            timeLen = 10;
            monthOrDay = 3;
            break;
          default:
            timeLen = 4;
            monthOrDay = 3;
            break;
        }
      } else {  // 首次点查看
        monthOrDay = 0;
        switch (level) {
          case 1 :
            timeLen = 19;
            break;
          case 2 :
            var method = obj["method"];
            if (method == 2) {
              timeLen = 19;
              monthOrDay = 0;
            } else {
              timeLen = 10;
              monthOrDay = 3;
            }
            break;
          case 3 :
            timeLen = 10;
            monthOrDay = 3;
            break;
          case 4 :
            timeLen = 7;
            monthOrDay = 2;
            break;
          default :
            if (type == "A") {
              timeLen = 10;
              monthOrDay = 1;
            } else if (type == "B") {
              timeLen = 7;
              monthOrDay = 2;
            } else if (type == "C") {
              timeLen = 10;
              monthOrDay = 3;
            } else if (type == "D") {
              timeLen = 19;
            }
            break;
        }
      }
      this.monthOrDay = monthOrDay
      this.timeLenLimit = timeLen
    },
    getDetails(info) {
      var disabled = info.disabled
      if (disabled) {
        return false;
      }
      this.mainNum = 3
      this.navControlList.push({'source': 'seeWageDetail'})
      if (!info.id || info.id == "") {
        this.$message("获取基本信息失败，请重试！")
        return false
      }
      let param = { "detailId" : info.id  }
      api.getDataDetail(param).then(res => {
        this.wageShow = false
        this.fundDetail = res.data
      }).catch(err => {
        console.log('err=', err)
      })
    },
    showDetailList(info, major, save){
      let oid = '';
      this.monthOrDayKind = major
      let data = {"oid": oid} ;  // monthOrDay 1-年进月，2-月进日，3-日进明细
      //var scanStr = obj.next(".hd").html() ;
      if(!info || info === "null"){
        this.$message.error('无明细可查看！')
        return false
      }
      var scanInfo = info ;
      data["monthOrDay"] = major ;
      data["beginDate"] = new Date(scanInfo["beginDate"]).format('yyyy-MM-dd hh:mm:ss') ;
      data["endDate"] = new Date(scanInfo["endDate"]).format('yyyy-MM-dd hh:mm:ss') ;
      if(this.searchMethod > 0){
        data["fid"] = scanInfo["accountId"] ;  // 账户id。（获取所有账户时不传，或传fid=null）
      }

      if( data["monthOrDay"] == "2" ){ data["endDate"] = this.getEndDate( data["beginDate"] , 2 ); }
      else if( data["monthOrDay"] == "3" ){ data["endDate"] = this.getEndDate( data["beginDate"] , 3 ); }
      if( !data["endDate"] ){ data["endDate"] = data["beginDate"] ;   }
      if (save) {
        let scanStr = {'info': info, 'monthOrDay': this.monthOrDayKind}
        this.navControlList.push(scanStr)
      }
      api.getAccountMonthOrDay(data).then(res => {
        let result = res.data.data;
        // type A-跨年，B-跨月，C-本月，D-本日 ， （本日的时候取 明细列表accountDetailList 其余都取accountPeriodList列表）
        // allBalance 总余额 allCredit总收入 allDebit总支出 allPreviousBalance总上期余额 beginDate开始 endDate结束
        var type = result["type"] ;
        var beginDate = result["beginDate"];
        var endDate = result["endDate"];
        var financeAccountId = result["financeAccountId"];
        var timeStr = "" ;
        this.mainNum = 2
        switch(major){
          case 1 : timeStr= this.cutLen(beginDate , 10) + " ~ " + this.cutLen(endDate , 10) ;  break ;
          case 2 : timeStr= this.cutLen(beginDate , 7);   break ;
          default:  timeStr= this.cutLen(beginDate , 10) ;  break ;
        }
        this.navTtl = {
          'nav5': timeStr,
          'nav6': (parseFloat(result["allPreviousBalance"])).toFixed(2),
          'nav7': (parseFloat(result["allCredit"])).toFixed(2),
          'nav8': (parseFloat(result["allDebit"])).toFixed(2),
          'nav9': (parseFloat(result["allBalance"])).toFixed(2)
        }
        var list = null ;
        if(!type && !major){
          list = result["accountPeriodList"];
          this.dataByTime = result
          this.addList({ "list" : list  , "type":type , "beginDate":beginDate , "endDate": endDate ,"financeAccountId": financeAccountId , "monthOrDay": major }) ;
        }else{
          if(type == "D"){
            list = result["accountDetailList"];
            this.dataByDayList = list
            this.monthOrDay = 3
            this.monthOrDayKind = 3
          }else{
            list = result["accountPeriodList"];
            this.dataByTime = result
            this.addList({ "list" : list , "type":type , "beginDate":beginDate , "endDate": endDate , "financeAccountId": financeAccountId , "monthOrDay": major , "fid": scanInfo["accountId_"]   }) ;
          }
          // 显示页面
          this.dataAndNavByLevel(  this.monthOrDayKind );
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    dataAndNavByLevel(monthOrDay ){ // 1-年进月，2-月进日，3-日进明细
      switch( Number(monthOrDay) ){
        case 3 :
          this.navTtl.nav1 = '昨日余额'
          this.navTtl.nav2 = '当日收入'
          this.navTtl.nav3 = '当日支出'
          this.navTtl.nav4 = '当前余额'
          //$("#dataInfo").show().siblings().hide();
          break ;
        case 2 :
          // $("#dataByLevel").show().siblings().hide();
          this.navTtl.nav1 = '上月余额'
          this.navTtl.nav2 = '本月收入'
          this.navTtl.nav3 = '本月支出'
          this.navTtl.nav4 = '本月余额'
          this.tdTtl = {'td0': '日期','td1': '昨日余额', 'td2': '今日收入', 'td3': '今日支出', 'td4': '今日余额'}
          break ;
        case 1 :
          // $("#dataByLevel").show().siblings().hide();
          this.navTtl.nav1 = '上期余额'
          this.navTtl.nav2 = '本年收入'
          this.navTtl.nav3 = '本年支出'
          this.navTtl.nav4 = '本年余额'
          this.tdTtl = {'td0': '日期','td1': '上月余额', 'td2': '本月收入', 'td3': '本月支出', 'td4': '本月余额'}
          break ;
        default: // 没有传monthOrDay ，目前只有按账户自定义查询会出现
          //$("#dataByLevel").show().siblings().hide();
          this.navTtl.nav1 = '上期余额'
          this.navTtl.nav2 = '本期收入'
          this.navTtl.nav3 = '本期支出'
          this.navTtl.nav4 = '本期余额'
          this.tdTtl = {'td0': '日期','td1': '上期余额', 'td2': '本期收入', 'td3': '本期支出', 'td4': '本期余额'}
          break ;
      }
    },
    seeOrdLoanDetail(info) {
      let param = {}
      if(info.businessType === "0101" || info.businessType === "0103"){
        param = { 'id' : info.business }
      }else {
        // 如果是支出的话
        param = { 'repaymentID' : info.business }
      }
      api.getOrdLoanDetail(param).then(res => {
        let data = res.data.data;
        this.loanDetailData = data
        this.generalScan = true
      }).catch(err => {
        console.log('err=', err)
      })
    },
    seeCollectDetail(info) {
      let param = {
        'collectId': info.business
      }
      api.getCollectDetail(param).then(res => {
        let detail = res.data.slCollectApplication;
        this.collectDetail = detail
        this.collectDetailCus = res.data.customerName
        this.collectDetailLog = true
      }).catch(err => {
        console.log('err=', err)
      })
    },
    seeWageDetail( info ){
      this.mainNum = 3
      this.navControlList.push({'source': 'seeWageDetail'})
      let param = { "businessHistory": info.businessHistory }
      api.getPayHistory(param).then(res => {
        let info = res.data.data.personnelPayHistory;
        let detail = res.data.data;
        let yearMonth = info.period.substr(0,4) + '年' + info.period.substr(4,2) + '月'
        this.payHistoryDetail = detail
        this.wageTtl.date = '支出日期'
        this.wageTtl.number = '职工人数'
        this.wageTtl.method = '支出方式'
        this.wageShow = true
        this.fundDetail = {}
        switch (info.type){
          case 1: // 工资
            this.wageTtl.date = '发放日期'
            this.wageTtl.number = '发放人数'
            this.wageTtl.method = '发放方式'
            this.wageTtl.amount = '本次实发总额'
            this.wageTtl.tt3 = '工资所属月份：' + yearMonth
            this.wageTtl.placeTtl = '实发'
            break;
          case 2: // 个人所得税
            this.wageTtl.amount = '本月职工应缴个所税总额'
            this.wageTtl.tt3 = yearMonth + '个人所得税明细表'
            this.wageTtl.placeTtl = '个人所得税'
            break;
          case 3: // 社保
            this.wageTtl.amount = '本月职工应缴社保总额'
            this.wageTtl.tt3 = yearMonth + '应缴社保明细表'
            this.wageTtl.placeTtl = '应缴社保'
            break;
          case 4: // 公积金
            this.wageTtl.amount = '本月职工应缴公积金总额'
            this.wageTtl.tt3 = yearMonth + '应缴公积金明细表'
            this.wageTtl.placeTtl = '应缴公积金'
            break;
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    getReimburseDetail(reimburseId) {
      this.reimburseLog = true
      api.getReimburseInfo({reimburseId : reimburseId }).then(res => {
        this.reimburseProcessList = res.data.processList
        this.reimbursePersonnel = res.data.personnelReimburse
        this.reimburseBillList = res.data.billList
      }).catch(err => {
        console.log('err=', err)
      })
    },
    seeRepayBtn(info) {
      this.payInfoTtl = '查看付款'
      this.payInfoLog = true
      api.getRepaymentDetail({id : info.id }).then(res => {
        this.repaymentType = 0
        let repaymentData = res.data.data
        if (repaymentData) {
          if(Number(this.loanDetailData.loanDetail.loanType) === 2) { // 收款
            this.payInfoTtl = '收款查看'
          }else{
            this.payInfoTtl = '付款查看'
          }
          this.repaymentData = repaymentData
          this.setRepayInfo22(0 , repaymentData , [])
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    setRepayInfo22(seeType , repaymentData , differents){
      let val = "";
      val = repaymentData['repaymentMethod'];
      repaymentData['repaymentTime'] = new Date(repaymentData['repaymentTime']).format('yyyy-MM-dd')
      repaymentData['receiveDate'] = new Date(repaymentData['receiveDate']).format('yyyy-MM-dd')
      repaymentData['billEndDate'] = new Date(repaymentData['billEndDate']).format('yyyy-MM-dd')
      repaymentData['billReceiveDate'] = new Date(repaymentData['billReceiveDate']).format('yyyy-MM-dd')
      repaymentData['expireDate'] = new Date(repaymentData['expireDate']).format('yyyy-MM-dd')
      repaymentData['arriveDate'] = new Date(repaymentData['arriveDate']).format('yyyy-MM-dd')
      if(Number(this.loanDetailData.loanDetail.loanType) === 2){ // 收款
        if(val == 4){
          this.payTtl.pay1 = "收到汇票日期"
          this.payTtl.pay2 = "汇票号"
          this.payTtl.pay3 = "汇票到期日"
          this.payTtl.pay4 = "原始出具汇票单位"
          this.payTtl.pay5 = "出具汇票银行"
        }else if(val == 3){
          this.payTtl.pay1 = "收到支票日期"
          this.payTtl.pay2 = "支票号"
          this.payTtl.pay3 = "支票到期日"
          this.payTtl.pay4 = "出具支票单位"
          this.payTtl.pay5 = "出具支票银行"
        }

        /*for (var key in differents) {
          if(differents[key]){
            $(seeCon +" ."+differents[key] + ":visible").html('<span class="ty-color-red">'+$(seeCon +" ."+differents[key]+ ":visible").html()+'</span>')
            if(differents[key] === 'receiveBank') {
              $(seeCon +" .receiveBank:visible").html('<span class="ty-color-red">'+$(seeCon +" .receiveBank:visible").html()+'</span>')
            }
          }
        }*/
      }else{ // 付款
      }
    },
    showHistory(status, info) {
      // status: 1- 借款信息修改 ； 2 - 付款记录修改记录
      this.updateLog = true
      this.updateRecordKind = status
      if (status === 1) {
        let id = this.loanDetailData.loanDetail.id
        api.ordLoanRecordModList({id : id }).then(res => {
          this.updateRecordList = res.data.data || []
        }).catch(err => {
          console.log('err=', err)
        })
      } else {
        api.ordRecordModList({id : info.id }).then(res => {
          this.updateRecordList = res.data.data || []
        }).catch(err => {
          console.log('err=', err)
        })
      }
    },
    showHistorySee(item, type){
      if(this.updateRecordKind === 1) {
        this.seeChanges(item, type)
      } else {
        this.getRepayHistoryDetail(item, type)
      }
    },
    seeChanges(loanDetail, type) {
      // type 1:修改前 2:修改后
      if(type === 1) {
        this.borrowInfoTtl = '修改前'
        this.loanRecordCon = loanDetail.before
      } else {
        this.borrowInfoTtl = '修改后'
        this.loanRecordCon = loanDetail.after
      }
      this.loanRecordDetail = loanDetail
      this.borrowInfoLog = true
    },
    getRepayHistoryDetail(repaymentData, type) {
      // type 1:修改前 2:修改后
      var differents = [] //el.prev().html().split('-')
      this.payInfoUpdateLog = true
      if(type === 1) {
        this.payLogTtl = '修改前'
        this.loanRecordCon = repaymentData.before
      } else {
        this.payLogTtl = '修改后'
        this.loanRecordCon = repaymentData.after
      }
      this.repaymentData = repaymentData
      this.setRepayInfo22(1, this.loanRecordCon , differents)
    },
    getEndDate( beginDate , monthOrDay){ // monthOrDay : 2-月进日 ， 3-日进明细
      var dateArr = new Date(beginDate) ;
      var year = dateArr.getFullYear() ;
      var month = dateArr.getMonth() ;
      var endDate = new Date(year,month+1,0).format('yyyy-MM-dd')  ;
      endDate += " 23:59:59";
      return endDate ;

    },
    formatAccount(account){
      let accountStr = `****`
      if(account.length >3){
        accountStr += account.substr(account.length-4,4)
      }else{
        let n = 4-account.length ;
        for(let i = 0 ; i < n; i++){
          accountStr += '*'
        }
        accountStr += account
      }
      return accountStr
    },
    cutLen(str, len) {
      if (str) {
        let format = `yyyy-MM-dd hh:mm:ss`;
        format = format.substr(0, len);
        return new Date(str).format(format);
      } else {
        return "";
      }
    },
    markDis(val){
      let able = this.loanRecordDetail.differents.indexOf(val) > -1
      return able
    },
    markRedFlag(val){
      let able = this.repaymentData.differents.indexOf(val) > -1
      return able
    },
    seeOnline(event, id) {
      let el = event.currentTarget
      previewOrDownloadFun(el, 1, this)
    },
    hideFun(str) {
      switch (str) {
        case 'historyLog':
          this.historyLog = false
          break
        case 'reimburseLog':
          this.reimburseLog = false
          break
        case 'generalScan':
          this.generalScan = false
          break
        case 'collectDetailLog':
          this.collectDetailLog = false
          break
        case 'payInfoLog':
          this.payInfoLog = false
          break
        case 'updateLog':
          this.updateLog = false
          break
        case 'borrowInfoLog':
          this.borrowInfoLog = false
          break
        case 'payInfoUpdateLog':
          this.payInfoUpdateLog = false
          break
      }
    },
    getOrgSonOrgsList(){
      api.getOrgSonOrgs().then(res => {
        var list = res.data.data || [];
        this.searchOrgList = list
        if (list > 1) {
          this.matchType = true
        } else {
          this.matchType = false
        }
      })
    },
    chargeBillPeriod(val) {
      switch (Number(val)) {
        case 1 :
          return "本月票据";
          break;
        case 2 :
          return "非本月票据";
          break;
        default :
          return "";
      }
    },
    chargeMethod(val) {
      switch (Number(val)) {
        case 1 :
          return "现金";
          break;
        case 2 :
          return "现金支票";
          break;
        case 3 :
          return "转账支票";
          break;
        case 4 :
          return "承兑汇票";
          break;
        case 5 :
          return "银行转账";
          break;
        case 6 :
          return "存现金";
          break;
        case 7 :
          return "取现金";
          break;
        case 8 :
          return "其他内部转账";
          break;
        default :
          return "";
      }
    },
    chargeBillType(type) {
      // 1-内部支票 0-外部支票
      switch (type) {
        case '1':
          return '内部支票';
          break;
        case '0':
          return '外部支票';
          break;
      }
    },
    chargeGenre(val) {
      switch (Number(val)) {
        case 1 :
          return "货款";
          break;
        case 2 :
          return "借款";
          break;
        case 3 :
          return "投资款";
          break;
        case 4 :
          return "废品";
          break;
        case 5 :
          return "其他";
          break;
        default :
          return "";
      }
    },
    fotmatExpType(type) {
      type = Number(type)
      let str = ''
      switch (type) {
        case 1:
          str = '本人的公务支出（财务费用除外、需入库物品的报销除外）'
          break;
        case 2:
          str = '其他同事的公务支出（财务费用除外，需入库物品的报销除外）'
          break;
        case 3:
          str = '需入库物品的报销'
          break;
        case 4:
          str = '社保/公积金'
          break;
        case 5:
          str = '税款'
          break;
        case 6:
          str = '汇划费'
          break;
        case 7:
          str = '其他财务费用的支出'
          break;
        case 8:
          str = '以上类别以外的支出'
          break;

      }
      return str
    },
    formatStakeholderCategory(cat) {
      cat = Number(cat);
      let str = ''
      switch (cat) {
        case 1 :
          str = '供应商';
          break;
        case 2 :
          str = '公司职工';
          break;
        case 3 :
          str = '财务自行录入的收款单位（含个人）';
          break;
      }
      return str
    },
    chargeInterestMethod (type) {
      //利息的支付方式：0-无利息,1-按日付,2-按月付,3-按年付
      switch (type) {
        case '0':
          return '无利息';
          break;
        case '1':
          return '按日付';
          break;
        case '2':
          return '按月付';
          break;
        case '3':
          return '按年付';
          break;
      }
    },
    goPrev() {
      let len = this.navControlList.length
      if (len <= 2) {
        this.mainNum = 1
        this.searchLevel = this.navControlList[0].level
        this.searchMethod = this.navControlList[0].method
        this.showMainData()
      } else {
        let json = this.navControlList[len-2]
        let major = json.monthOrDay
        this.showDetailList(json.info, major, false)
      }
      this.navControlList.splice(len-1, 1)
    }
  }
}

</script>

<style lang="scss" scoped>
.overviewFund {
  font-size: 14px;
  padding: 10px;
  line-height: 1.42857;
  .gapRt{
    margin-right: 10px;
  }
  .btnn {
    display: inline-block;
    line-height: 25px;
    padding: 0 10px;
    border: 1px solid #ccc;
    border-left-color: #ccc;
    margin: 0;
    border-radius: 5px;
  }

  .btnn-big {
    padding: 0 30px;
    line-height: 35px;
  }

  .btn-circle {
    border-radius: 5px;
  }

  .btnn:hover {
    background: #ececec;
    cursor: default;
  }

  .btnnActive {
    background: #eaeaea;
  }

  .control {
    color: #00a0e9;
  }

  .btnGroup {
    display: inline-block;
    border: 1px solid #ccc;
    border-radius: 5px;
    overflow: hidden;
  }

  .btnGroup .btnn:first-child {
    border-left: none;
  }

  .btnGroup .btnn {
    border-right: none;
    border-top: none;
    border-bottom: none;
    border-radius: 0px;
  }

  .em2 {
    padding: 2px 15px;
  }

  .left {
    float: left;
  }

  .right {
    float: right;
  }

  .clr {
    clear: both;
  }

  .navtab {
    padding: 10px 0;
  }

  .riBtn {
    position: absolute;
    right: 0;
    top: 0;
    height: 35px;
  }

  .dataNav table td.textLeft, .dataList table td.textLeft {
    text-align: left;
  }

  .dataNav table td.ind2, .dataList table td.ind2 {
    text-align: left;
    text-indent: 2em;
  }

  /*这是很重要的东西*/
  #betweenDate {
    font-size: 16px;
  }

  /* Four big data  */
  .indexBg {
    background: url('@/assets/commonImg/indexForAdmin.png') no-repeat;
    display: inline-block;
    height: 128px;
    width: 128px;
  }

  .theMonthPay {
    height: 80px;
    width: 80px;
    background-position: 0 0;
  }

  .prevMonth {
    height: 80px;
    width: 80px;
    background-position: -80px 0;
  }

  .theMonthGet {
    height: 80px;
    width: 80px;
    background-position: -160px 0;
  }

  .theMonthLast {
    height: 80px;
    width: 80px;
    background-position: -240px 0;
  }

  /* SearchContainer */
  ul.searchCon:before {
    border-bottom: 7px solid rgba(0, 0, 0, 0.2);
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    content: "";
    display: inline-block !important;
    position: absolute;
    right: 58px;
    top: -7px;
  }

  ul .trigle {
    border-bottom: 5px solid #fff;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    content: "";
    display: inline-block !important;
    position: absolute;
    right: 60px;
    top: -5px;
  }

  .searchCon {
    /*left: 100px;
    width: 350px;
    border: 1px solid #ccc;
    box-shadow: 3px 3px 5px #666;*/
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 14px;
    text-align: left;
  }

  /*四个的*/
  .mainNav {
    display: flex;
    justify-content: space-between
  }

  .pal {
    color: #fff;
    text-align: right;
    height: 100px;
    position: relative;
    overflow: hidden;
    width: 410px;
    margin-left: 8px;
  }

  .visual {
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .visual i {
    font-size: 100px;
    opacity: 0.1;
  }

  .number {
    font-size: 35px;
  }

  .details {
    padding: 10px;
  }

  .desc {
    font-size: 16px;
  }

  .bg_blue {
    background: #3598DC;
  }

  .bg_red {
    background: #E7505A;
  }

  .bg_green {
    background: #32C5D2;
  }

  .bg_purple {
    background: #8E44AD;
  }

  #duringLevel {
    margin-bottom: 50px;
  }

  /*radioBtn style*/
  span.radioBtn {
    padding: 0 4px;
    display: inline-block;
    width: 20px;
    min-width: 20px;
    height: 20px;
    border-radius: 10px !important;
    top: 3px;
    background: #fff;
    position: relative;
    border: 1px solid #ccc;
  }

  span.radioBtn > .val {
    display: none;
  }

  span.valShow {
    width: 150px;
    min-width: 150px;
    border: none;
    padding: 0 5px;
    line-height: 35px;
  }

  span.radioBtn > .radioShow {
    display: inline-block;
    background: #fff;
    border-radius: 5px !important;
    width: 10px;
    height: 10px;
    position: relative;
    top: -2px;
  }

  span.radioActive > .radioShow {
    background: #9d9d9d;
    box-shadow: 0 0 2px #666;
  }

  .con span.radioBtn > .radioShow {
    top: -7px;
  }

  /*详情页的基本展示*/
  .itemTr {
    padding: 5px 0;
    position: relative;
  }

  .itemTr > span.ttl {
    display: inline-block;
    width: 200px;
    text-align: right;
    padding-right: 10px;
    line-height: 30px;
    position: absolute;
    top: 5px;
  }

  .con {
    display: inline-block;
    line-height: 30px;
    width: 350px;
    position: relative;
    margin-left: 210px;
  }

  .scan {
    display: block;
  }

  .scan {
    width: 350px;
    border: 1px solid #ccc;
    padding-left: 10px;
    height: 30px;
    overflow: hidden;
  }

  .mark {
    background: rgba(0, 0, 0, 0.15);
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  /*动态展示图片*/
  #info_images > img {
    height: 50px;
  }

  #bigImagcon {
    position: absolute;
    bottom: 100px;
    left: 100px;
    display: none;
  }

  #bigImag {
    display: block;
    width: 650px;
    box-shadow: 0 0 2px #000;
  }

  .approvCon {
    padding: 20px;
  }

  /*回款*/
  #collectDetailSee ul {
    overflow: hidden;
  }

  #collectDetailSee ul li {
    float: left;
    width: 50%;
  }
  #collectDetailSee ul li p{
    margin-bottom: 10px;
  }

  .pTtl {
    float: left;
    width: 40%;
  }

  .pCon {
    float: left;
    width: 60%;
  }

  .laydate_btn {
    display: none;
  }

  .updateSubmit {
    width: auto;
    border: none;
    padding: 0 20px;
    overflow: hidden;
    background-color: #5d9cec;
    color: #fff;
    line-height: 30px;
    border-radius: 3px;
    cursor: pointer;
  }

  #info_images {
    overflow: hidden;
  }

  .imgBox {
    float: left;
    width: 180px;
    position: relative;
    border: 1px solid #efefef;
    margin-bottom: 5px;
  }

  .imgBox img {
    width: 180px;
  }

  .imgClose {
    display: none;
    position: absolute;
    top: 0px;
    right: 0px;
    width: 20px;
    height: 20px;
    line-height: 19px;
    color: #fff;
    background-color: #666666;
    border-radius: 15px;
    text-align: center;
  }

  .imgClose:hover {
    background-color: #ed5565;
  }

  .imgBox:hover .imgClose {
    display: block;
  }

  .item {
    overflow: hidden;
    clear: both;
    margin-bottom: 8px;
  }

  .item-title {
    display: inline-block;
    width: 126px;
    text-align: right;
    margin-right: 5px;
    vertical-align: top;
    line-height: 30px;
    color: #5a5e6c;
  }

  .item-title-big {
    display: inline-block;
    width: 300px;
    text-align: right;
    margin-right: 5px;
    vertical-align: top;
    line-height: 30px;
    color: #5a5e6c;
  }

  .item-content {
    display: inline-block;
    width: 158px;
    border-radius: 2px;
    color: #7e7e7e;
    padding: 6px 8px;
  }

  .item-content-big {
    display: inline-block;
    width: 700px;
    border-radius: 2px;
    color: #7e7e7e;
    line-height: 20px;
    padding: 6px 8px;
  }

  .repaymentList .ty-table thead td {
    border-color: #d7d7d7 #d7d7d7 #d7d7d7 #d7d7d7;
    background-color: #eee;
    font-weight: bold;
    color: #666;
  }

  #ordLoanAddEdit, #borrowInfo, #inOrOut {
    width: 1080px;
  }

  .memo {
    width: 866px;
    height: 60px;
    text-overflow: ellipsis;
    line-height: 20px;
    padding: 6px 8px;
  }

  #chooseC li {
    padding-left: 50px;
  }

  #chooseC li:hover {
    background: rgba(10, 158, 242, 0.05)
  }

  #oppositeCorpDiv ul {
  }

  #oppositeCorpDiv ul li i {
    margin-right: 15px;
    color: #5d9cec;
  }

  #oppositeCorpDiv ul li {
    list-style: none;
    line-height: 30px;
  }

  .stakeholderCategoryContsiner {
    position: relative;
  }

  .stakeholderCategory option:hover {
    background: #eee;
  }

  .stakeholderCategory option {
    border-bottom: 1px solid #ddd;
    padding: 0 20px;
    cursor: pointer;
  }

  .stakeholderCategory {
    position: absolute;
    top: 30px;
    background: #fff;
    z-index: 1;
    border: 1px solid #ddd;
    border-bottom: none;
    width: 100%;
    line-height: 28px;
    left: -1px;
  }

  .stakeholderCategoryText {
    display: inline-block;
    width: 100%;
    height: 100%;
  }

  .e3 > span {
    width: 100px;
    display: inline-block;
    text-align: right;
    margin-right: 10px;
  }

  .linkBtn {
    cursor: pointer;
    color: #5d9cec;
    padding: 3px 10px;
    border: none;
    width: auto;
    text-align: center;
    line-height: 30px;
  }

  .poReL {
    position: relative;
  }

  .cp_img_box {
    display: inline-block;
    width: 100px;
    position: relative;
    vertical-align: top;
  }

  .cp_img_box .fileType a {
    display: none;
    background-color: rgba(93, 156, 236, .9);
    color: #fff;
    height: 18px;
    line-height: 18px;
    width: 36px;
    margin: 0 auto 5px;
    font-size: 12px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
  }

  .cp_img_box .fa-times {
    position: absolute;
    right: 6px;
    top: 0px;
    color: #d64835;
  }

  .cp_img_box:hover .fileType a {
    display: block;
  }

  .cp_img_box .fileType span.fa {
    display: none;
  }

  .cp_img_box:hover .fileType span.fa {
    display: block;
  }

  .cp_img_name {
    font-size: 12px;
    color: #666;
    text-align: center;
    word-break: break-all;
    max-height: 70px;
  }


  #scan_imgs_32, #scan_imgs_3_ {
    border: none;
    margin-left: 190px;
    display: flex;
  }

  #scan_imgs_3 {
    border: none;
    /*margin-left: 180px;*/
    display: flex;
  }

  .fileType {
    width: 60px;
    height: 80px;
    margin-left: 20px;
    margin-bottom: 2px;
    background-position: center;
    background-color: #eee;
    background-size: 60px 80px;
  }

  .hd {
    display: none !important;
  }

  .flexbox {
    display: flex;
    text-align: right;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .flexbox > div {
    flex: 1;
    text-align: left;
    line-height: 30px;
  }

  .flexbox > div:last-child {
    text-align: right;
  }

  .martop30 {
    margin-top: 30px;
  }

  #detailKind_3 .bonceCon2 .scan000 .ttl30 {
    width: 130px;
    display: inline-block;
    text-align: right;
    margin-right: 15px;
  }

  #detailKind_3 .bonceCon2 .scan000 {
    padding: 16px;
    background: #eff6f7;
    margin-top: 30px;
  }

  #rateEntryFrm {
    width: 600px;
  }

  #rateEntryFrm .rateTtl {
    width: 100px;
    display: inline-block;
    text-align: right;
    margin-right: 10px;
  }

  #rateEntryFrm .tip {
    text-align: center;
    font-size: 0.8em;
    margin-top: -10px;
  }

  #rateEntryFrm .cc > div {
    margin-bottom: 10px;
  }

  #rateEntryFrm .sm.form-control {
    width: 190px;
  }

  #rateEntryFrm .bg.form-control {
    width: 400px;
  }

  #rateEntryFrm .bonceCon {
    background: #fff;
  }

  #rateEntryFrm .bonceFoot {
    background: #fff;
  }

  #ratePic {
    padding-left: 100px;
  }

  .screenArea {
    display: none;
  }

  .screenBody {
    margin-left: 130px;
    display: inline-block;
    overflow: hidden;
  }

  .screenBody span {
    margin-right: 12px;
    display: inline-block;
    line-height: 32px;
  }

  #searchOrg {
    padding: 0px 20px;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    line-height: 35px;
    height: 35px;
  }

  .updateFormWrap {
    padding-left: 50px;
  }

  .singleRow {
    margin-bottom: 30px;
  }

  .singleRow .fa {
    margin-right: 12px;
  }

  .toggleCon {
    margin-top: 20px;
  }

  #billNolist {
    width: 260px;
  }

  .check_See {
    padding: 5px 0;
  }

  .check_Seebg, .check_SeeType {
    position: relative;
    z-index: 1;
  }

  .check_See > span, .check_Seebg > span {
    width: 26.5%;
    display: inline-block;
    text-align: right;
  }

  .check_SeeType > span:nth-child(1) {
    width: 26.5%;
    display: inline-block;
    text-align: right;
  }

  .check_SeeType {
    padding: 5px 0;
  }

  .check_SeeType > span:nth-child(2), .check_SeeType > span:nth-child(4), .check_SeeType > span:nth-child(6) {
    margin-left: 20px;
  }

  .check_See > input {
    margin-left: 8px;
    padding: 0 0 0 10px;
    width: 60%;
    height: 35px !important;
    background-color: #fff;
  }

  .check_Seebg > input {
    margin-left: 8px;
    padding: 0 0 0 10px;
    width: 60%;
    height: 35px !important;
  }

  .check_See > select {
    margin-left: 8px;
    padding: 0 0 0 6px;
    width: 60.5%;
    height: 35px !important;
    border: 1px solid #ccc;
  }

  .check_SeeHide, .accept_hide {
    display: none;
  }

  .bank_Con > span:nth-child(2) {
    color: red;
    width: 10px;
    font-size: 16px;
  }
  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 14px;
    text-align: left;
    background-color: #fff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
    background-clip: padding-box;
  }
  .open > .dropdown-menu {
    display: block;
  }
  .col-md-4, .col-md-8  {
    float: left;
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .col-md-4{
    width: 30%;
  }
  .col-md-8 {
    width: 62%;
  }
  .applyYes{ background: rgb(224, 235, 249);   }
  .fundInfoShow tbody>tr>td{border: 1px solid #e7ecf1;}
  #detailKind_1 .bonceCon{padding: 10px 15px;background-color: #f0f8ff;max-height: 550px;overflow-y: auto;}
  #detailKind_1 .bonceCon table{width: 100%;max-width: 100%;margin-bottom: 20px;}
  #detailKind_1 .table-bordered tbody tr td {border: 1px solid #e7ecf1;}
  #detailKind_1 table tbody tr td {padding: 8px;line-height: 1.42857;vertical-align: top;border-top: 1px solid #e7ecf1;}
  .redMark{color: #ed5565;}
  #tt3{font-size: 24px;}
}
</style>
<style>
.el-popper .el-row {margin-bottom: 10px;}
.searchCon .txtRight {text-align: right;}
.searchCon .el-input {width: 200px;}
.searchCon .ttl {padding-right: 10px;display: inline-block;width: 90px;text-align: right;}
</style>
