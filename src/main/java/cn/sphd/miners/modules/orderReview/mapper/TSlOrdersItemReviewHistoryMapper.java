package cn.sphd.miners.modules.orderReview.mapper;

import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReviewHistory;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface TSlOrdersItemReviewHistoryMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlOrdersItemReviewHistory selectTSlOrdersItemReviewHistoryById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 集合
     */
    public List<TSlOrdersItemReviewHistory> selectTSlOrdersItemReviewHistoryList(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory);

    /**
     * 新增
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 结果
     */
    public int insertTSlOrdersItemReviewHistory(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory);

    /**
     * 修改
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 结果
     */
    public int updateTSlOrdersItemReviewHistory(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewHistoryById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTSlOrdersItemReviewHistoryByIds(Long[] ids);
}
