package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.inv.entity.InvWarehouseBase;
import cn.sphd.miners.modules.inv.service.InvService;
import cn.sphd.miners.modules.material.entity.MtInApplication;
import cn.sphd.miners.modules.material.entity.MtOutApplication;
import cn.sphd.miners.modules.material.service.MtGrantService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/5/10 14:33
 */
@Controller
@RequestMapping("/picking")
public class PickingController {

    @Autowired
    private UserService userService;
    @Autowired
    private MtGrantService mtGrantService;
    @Autowired
    private ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    InvService invService;

    @Autowired
    UserPopedomService userPopedomService;
    //进入领料分工页
    @RequestMapping("/index.do")
    public String index() {
        return "/materialDivision/materialDivision";
    }

    /**
     * @param: 获取领料者列表   coreCode pickingPerson
     * <AUTHOR>
     * @date: 2021/5/12 11:42
     */
    @ResponseBody
    @RequestMapping("/getCoreUser.do")
    public JsonResult getThreeUser( User user) throws IOException {

        Integer oid = user.getOid();
        List<User> codeUsers = userService.getUserListByCoreCodeLocking(oid,"pickingPerson");

        for (User u:codeUsers) {
            u.setRemark(mtGrantService.getListByUser(u.getUserID()).size()+"");
        }
        return new JsonResult(1,codeUsers);
    }


    /**
     * @param: 获取材料列表
     * <AUTHOR>
     * @date: 2021/5/12 11:42
     */
    @ResponseBody
    @RequestMapping("/getMtList.do")
    public JsonResult getMtList( User user,Integer userID) throws IOException {

        Integer oid = user.getOid();
        List<Map<String,Object>> mtList = mtGrantService.getMtList(oid,userID);

        return new JsonResult(1,mtList);
    }


    /**
     * @param: 材料授权 addIds添加授权  deleteIds删除授权
     * <AUTHOR>
     * @date: 2021/5/12 11:42
     */
    @ResponseBody
    @RequestMapping("/submitMtGrant.do")
    public JsonResult submitMtGrant( User user,String addIds,String deleteIds,Integer userID)  {


        mtGrantService.submitMtGrant(addIds,deleteIds,userID,user);

        return new JsonResult(1,null);
    }


    //获取可选人员
    @ResponseBody
    @RequestMapping("/getPickingUserList.do")
    public JsonResult getPickingUserList(User user,String code){
        Integer oid=user.getOid();
       List<UserDto>  list=mtGrantService.getPickingUserList(oid,code);

//        List<String> midList=new ArrayList<>();
//        midList.add(code);
//        List<UserHonePageDto> list=userPopedomService.getUserHonePageDtoByNotMid(user.getOid(),midList,null,null,"1");

        JsonResult result=new JsonResult(1,list);
        return result;
    }



    //获取可领取的材料树
    @ResponseBody
    @RequestMapping("/getUserMtListTree.do")
    public JsonResult getUserMtListTree( User user,String mtIds,Integer warehouseId) throws IOException {
        List<Map<String,Object>> mtList = mtGrantService.getUserMtListTree(user.getOid(),user.getUserID(),mtIds,warehouseId);
        //System.out.println("mtList："+mtList);
        return new JsonResult(1,mtList);
    }

    //获取所有可选材料
    @ResponseBody
    @RequestMapping("/getUserMtList.do")
    public JsonResult getUserMtList(  User user,String mtIds,Integer category,Integer warehouseId) throws IOException {
        List<Map<String,Object>> mtList = mtGrantService.getUserMtList(user.getOid(),user.getUserID(), mtIds,category,warehouseId);
        return new JsonResult(1,mtList);
    }

    //领料判断(判断是否满足领取条件)
    @ResponseBody
    @RequestMapping("/pickingMtJudge.do")
    public JsonResult pickingMtJudge(Integer id, BigDecimal num) throws IOException {

        String res="";
        try {
            res=mtGrantService.pickingMtJudge(id,num);
        }catch (Exception e){
            e.printStackTrace();
            res=e.getMessage();
        }
        if("success".equals(res)){
            return new JsonResult(1,null);
        }else{
            return new JsonResult(0,"","400",res);
        }
    }

    //领料申请(库房选择)
    @ResponseBody
    @RequestMapping("/selectWarehouse.do")
    public JsonResult selectWarehouse(User user, String mtIds) throws IOException {



        if(StringUtils.isEmpty(mtIds)){
            return new JsonResult(0,"","400","请选择至少一种材料");
        }

        List<Map<String,Object>> mapList=mtGrantService.selectWarehouse(mtIds);


        return new JsonResult(1,mapList);

    }



    /**
     * 领料申请
     * @param user
     * @param mtList
     * @param purpose
     * @param timeFact
     * @param durarionFact
     * @param warehouseId 库房id
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/pickingMtApply.do")
    public JsonResult pickingMtApply(User user, String mtList,String purpose,String timeFact,Long durarionFact,Integer warehouseId) throws IOException {


        if(StringUtils.isEmpty(purpose)){
            return new JsonResult(0,"","400","请选择材料用途");
        }
        if(StringUtils.isEmpty(timeFact)){
            return new JsonResult(0,"","400","请填写领料时间");
        }
        if(durarionFact==null){
            return new JsonResult(0,"","400","请填写计划时长");
        }
        String res="";
        try {
            res=mtGrantService.pickingMtApply(user,mtList,purpose,timeFact,durarionFact,warehouseId);
        }catch (Exception e){
            e.printStackTrace();
            res=e.getMessage();
        }
        if("success".equals(res)){
            return new JsonResult(1,null);
        }else{
            return new JsonResult(0,"","400",res);
        }
    }

    //获取其他领料计划
    @ResponseBody
    @RequestMapping("/getOtherPickingPlan.do")
    public JsonResult getOtherPickingPlan(User user,Integer materialWarehouse) throws IOException {
        List<MtOutApplication> list = mtGrantService.getOtherPickingPlan(user.getOid(),materialWarehouse);
        return new JsonResult(1,list);
    }
    //获取领料申请列表
    @ResponseBody
    @RequestMapping("/getPickingMtApplyList.do")
    @MessageMapping("/getPickingMtApplyList")
    public JsonResult getPickingMtApplyList( User user) throws IOException {
        List<InvWarehouseBase> warehouseBases=invService.getWarehouseList(user.getOid());
        List<MtOutApplication> list = mtGrantService.getMtOutApplicationList(user.getUserID(),user.getOid(),"2");
        for (MtOutApplication mo:list) {
            if(mo.getMaterialWarehouse()!=null){
                for ( InvWarehouseBase war:warehouseBases ){
                    if(mo.getMaterialWarehouse().intValue()==war.getId().intValue()){
                        mo.setWarehouseName(war.getWarehouseName());
                        break;
                    }
                }
            }
        }
        clusterMessageSendingOperations.convertAndSendToUser(user.getUserID()+"","/getPickingMtApplyList",null,null,null,null, JSON.toJSONString(list));
        return new JsonResult(1,list);
    }
    //根据材料获取库位列表
    @ResponseBody
    @RequestMapping("/getLocationList.do")
    public JsonResult getLocationList(Integer material,Integer warehouseId) throws IOException {

        List<Map<String,Object>> list=invService.getLocationListByMaterial(material,warehouseId);
        return new JsonResult(1,list);
    }
    //获取领料审批列表
    @ResponseBody
    @RequestMapping("/getPickingMtApprovalList.do")
    @MessageMapping("/getPickingMtApprovalList")
    public JsonResult getPickingMtApprovalList(  User user) throws IOException {
        List<MtOutApplication> list = mtGrantService.getMtOutApplicationList(null,user.getOid(),"2");
        List<InvWarehouseBase> warehouseBases=invService.getWarehouseList(user.getOid());
        for (MtOutApplication mo:list) {
            if(mo.getMaterialWarehouse()!=null){
                for ( InvWarehouseBase war:warehouseBases ){
                    if(mo.getMaterialWarehouse().intValue()==war.getId().intValue()){
                        mo.setWarehouseName(war.getWarehouseName());
                        break;
                    }
                }
            }
        }
        clusterMessageSendingOperations.convertAndSendToUser(user.getUserID()+"","/getPickingMtApprovalList",null,null,null,null, JSON.toJSONString(list));
        return new JsonResult(1,list);
    }

    /**
     * 领料详情查看
     * @param id 出库单id，领料单id
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/getPickingMtDetail.do")
    public JsonResult getPickingMtDetail( Integer id) throws IOException {
        Map<String,Object> map = mtGrantService.getPickingMtDetail(id);
        return new JsonResult(1,map);
    }
    /**
     * 领料审批
     * @param ，领料单id
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/pickingMtApproval.do")
    public JsonResult pickingMtApproval( Integer itemId,String json,User user) throws IOException {
        String res = mtGrantService.pickingMtApproval(itemId, json,user);

        if("success".equals(res)){
            return new JsonResult(1,null);
        }else{
            return new JsonResult(0,"","400",res);
        }
    }

    /**
     * 领料确认
     * @param ，领料单id
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/pickingMtConfirm.do")
    public JsonResult pickingMtConfirm( Integer itemId,User user)  {


        String res = mtGrantService.pickingMtConfirm(itemId,user);
        if("success".equals(res)){
            return new JsonResult(1,null);
        }else{
            return new JsonResult(0,"","400",res);
        }
    }


    /**
     * 领料申请查询
     */
    @RequestMapping("/selectPickingList.do")
    @ResponseBody
    public JsonResult selectPickingList(HttpServletRequest request) throws ParseException {


        Map<String,Object> map = new HashMap<>();

        Integer userId= Integer.valueOf(request.getParameter("userId"));
        String type=request.getParameter("type");
        String approvalStatus=request.getParameter("approvalStatus");
        String beginTime=request.getParameter("beginTime");
        String endTime=request.getParameter("endTime");
        String userType=request.getParameter("userType");
        Date st=null;
        Date ed=null;
        SimpleDateFormat sim1=new SimpleDateFormat("yyyy-MM-dd");

        SimpleDateFormat sim2=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleDateFormat sim3=new SimpleDateFormat("yyyy-MM");
        //7天内
        if("1".equals(type)){
            String nowDate=sim1.format(new Date());

            st=new Date((sim1.parse(nowDate).getTime()-3600*24*7*1000));

            ed=new Date(sim2.parse(nowDate+" 23:59:59").getTime());
            //当月
        }else if("2".equals(type)){
            //获取当前月第一天：
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, 0);
            c.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
            String first = sim1.format(c.getTime());
            System.out.println("===============first:"+first);

            //获取当前月最后一天
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            String last = sim1.format(ca.getTime());
            System.out.println("===============last:"+last);

            st=new Date(sim2.parse(first+" 00:00:00").getTime());

            ed=new Date(sim2.parse(last+" 23:59:59").getTime());
        }else{

            st=new Date(sim2.parse(beginTime+" 00:00:00").getTime());

            ed=new Date(sim2.parse(endTime+" 23:59:59").getTime());
        }
        List<MtOutApplication> list = mtGrantService.selectPickingList(userId,userType,approvalStatus,beginTime,endTime);
        map.put("beginTime",st );
        map.put("endTime",ed);
        map.put("list",list);
        return new JsonResult (1,map);
    }


    /**
     * 撤销申请
     * @param ，领料单id
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/pickingMtRevoke.do")
    public JsonResult pickingMtRevoke( Integer id,Integer userID) throws IOException {

        User loginUser=userService.getUserByID(userID);
        String res = mtGrantService.pickingMtRevoke(id,loginUser);
        if("success".equals(res)){
            return new JsonResult(1,null);
        }else{
            return new JsonResult(0,"","400",res);
        }

    }
    /**
     * 领料记录列表
     */
    @ResponseBody
    @RequestMapping("/getPickingRecordList.do")
    public Map<String,Object> getPickingRecordList(User user, Integer currPage, Integer pageSize){

        Integer oid= user.getOid();

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=mtGrantService.getPickingRecordList(oid,currPage,pageSize);

        map.put("currPage",currPage);
        return map;
    }


    /**
     * 手动入库
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/manualWarehousing.do")
    public JsonResult manualWarehousing(User user,MtInApplication mtInApplication,String mtList) throws IOException {

        if(mtInApplication.getArriveTime()==null){
            return new JsonResult(0,"","400","请选择入库时间");
        }

        if(mtInApplication.getCreator()==null){
            mtInApplication.setCreator(user.getUserID());
            mtInApplication.setCreateName(user.getUserName());
        }

        mtInApplication.setOrg(user.getOid());
        String res="";
        try {
            res=mtGrantService.manualWarehousing(mtInApplication,mtList);
        }catch (Exception e){
            e.printStackTrace();
            res=e.getMessage();
        }
        if("success".equals(res)){
            return new JsonResult(1,null);
        }else{
            return new JsonResult(0,"","400",res);
        }

    }


    /**
     * 手动领料
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/manualPicking.do")
    public JsonResult manualPicking(User user,MtOutApplication application,String mtList) throws IOException {


//        String res="";
//        try {
//            res=mtGrantService.manualWarehousing(mtInApplication,mtList);
//        }catch (Exception e){
//            e.printStackTrace();
//            res=e.getMessage();
//        }
//        if("success".equals(res)){
//            return new JsonResult(1,null);
//        }else{
//            return new JsonResult(0,"","400",res);
//        }

        return null;

    }

    /**
     * 获取库位列表
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/getLwLocationList.do")
    public JsonResult getLwLocationList(User user,Integer mtId) throws IOException {
        List<Map<String,Object>> mapList=mtGrantService.selectLocation(mtId);
        return new JsonResult(1,mapList);
    }


    /**
     * @param: 获取材料列表
     * <AUTHOR>
     * @date: 2021/5/12 11:42
     */
    @ResponseBody
    @RequestMapping("/getAllMtList.do")
    public JsonResult getAllMtList( User user) throws IOException {

        Integer oid = user.getOid();
        List<Map<String,Object>> mtList = mtGrantService.getAllMtList(oid);

        return new JsonResult(1,mtList);
    }

    /**
     * @param: 根据领料人，获取材料列表
     * <AUTHOR>
     * @date: 2021/5/12 11:42
     */
    @ResponseBody
    @RequestMapping("/getMtListByUser.do")
    public JsonResult getMtList(Integer userId) throws IOException {


        List<Map<String,Object>> mtList = mtGrantService.getMtList(null,userId);

        return new JsonResult(1,mtList);
    }



}
