package cn.sphd.miners.modules.spaceTraffic.service.impl;

import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import cn.sphd.miners.modules.spaceTraffic.service.SpaceTrafficService;
import org.springframework.context.ApplicationContext;

/**
 * Created by 朱思旭 on 2021/4/25.
 */
public class SpaceTrafficRemind implements DelayCallback {

    private Integer oid;

    private String memo;

    public Integer getOid() {
        return oid;
    }

    public String getMemo() {
        return memo;
    }

    public SpaceTrafficRemind(Integer oid, String memo) {
        this.oid = oid;
        this.memo = memo;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        SpaceTrafficService spaceTrafficService = ac.getBean(SpaceTrafficService.class);
        spaceTrafficService.sendSpaceTrafficMessage(oid,memo);
    }
}
