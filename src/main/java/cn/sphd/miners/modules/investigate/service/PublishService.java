package cn.sphd.miners.modules.investigate.service;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.investigate.dto.*;
import cn.sphd.miners.modules.investigate.entity.InvestigateObject;
import cn.sphd.miners.modules.investigate.entity.InvestigateObjectHistory;
import cn.sphd.miners.modules.investigate.entity.InvestigatePublish;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubject;
import cn.sphd.miners.modules.system.entity.User;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName PublishService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/3 8:13
 * @Version 1.0
 */

public interface PublishService {
    List<InvestigatePublish> selectInvestigatePublishList(User user, PageInfo pageInfo, Integer status, String month);
    int countInvestigatePublish(User user,Integer status,String month);
    RespInvestigateStopByMonthList selectSubjectStopByMonthList(User user, String year);
    List<InvestigatePublish> selectPublishByNameList(User user, PageInfo pageInfo, String keyword);
    List<InvestigateSubject> selectSubjectOptionsList(Integer org);

    InvestigatePublish selectInvestigatePublishByid(Integer id);
    int addPublish(Long expireDate, User user, Integer id, Integer modifyLimit,String coverImage);
    int updatePublish(Long expireDate, User user, Integer id);
    int updatePublishModifyLimit(Integer modifyLimit, User user, Integer id);
    int updatePublishCoverImage(String coverImage, User user, Integer id);
    int stopPublish(User user, Integer id);
    RespShowQuestion completeSubjectList(PageInfo pageInfo,Integer id,Integer type);
    RespShowQuestion exportExcelSubjectList(Integer id,String updateQuestionJson);
    RespShowQuestion investigateAnswerListByQuestion(int publish,int question,PageInfo pageInfo,Integer type);
    RespShowQuestion areaListByQuestion(int publish,int question,PageInfo pageInfo,Integer type,String content);
    List<RespInvestigateQuestion> selectQuestionOptionsList(Integer id);

    int updateShowQuestion(User user, Integer id,String updateQuestionJson);
    int updateObjectSubsumable(Integer id,Integer type);
    RespInvestigateSubject selectObjectById(Integer id);
    InvestigatePublish selectObjectIdByPublishId(Integer id);
    InvestigateObject addInvestigateObject(ReqInvestigateObject reqInvestigateObject);
    InvestigateObject updateInvestigateObject(ReqInvestigateObject reqInvestigateObject);
    List<InvestigateObjectHistory> investigateObjectHistoryList(Integer id);
    List<RespInvestigateObject> investigateObjectOtherList(Integer id,String hashKey);
    RespInvestigateSubject investigateAnswerHistoryDetails(Integer id);

    RespInvestigateQrList selectInvestigateQr(User user, Integer id);

    Integer selectHashKey(String hashKey,int id);
    void initializationQr(Integer id);

    void exportExcel(RespShowQuestion respShowQuestion, Integer id, HttpServletResponse response);
    void exportExcelQuestionStatistics(Integer piblish,Integer type,RespInvestigateQuestionStatistics respAnswerStatistics,HttpServletResponse response);
    void exportExcelEssayQuestion(Integer piblish,Integer question,Integer type,RespShowQuestion respShowQuestion,String content,HttpServletResponse response);
    void exportExcelMultipleChoiceQuestion(Integer piblish,Integer question,RespMultipleChoiceQuestion respMultipleChoiceQuestion,HttpServletResponse response);
    //tpye 1是增 0是减
    void investigateAnswerQuestionStat(int publish,int question,int type,int org);
    //tpye 1是增 0是减
    void investigateAnswerAnswerStat(int publish,int answer,int type,int org);

    RespAnswerStatistics investigateAnswerStatistics(int publish);
    void updateInvestigateAnswerStatistics(int publish);
    RespInvestigateQuestionStatistics investigateQuestionStatistics(int publish,int type,PageInfo pageInfo);

    RespMultipleChoiceQuestion answerListByMultipleChoiceQuestion(int publish,int question);
    void selectTask();
}
