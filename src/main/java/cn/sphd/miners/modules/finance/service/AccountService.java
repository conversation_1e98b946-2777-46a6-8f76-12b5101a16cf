package cn.sphd.miners.modules.finance.service;


import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2015-09-09.
 */
public interface AccountService {
    void addAccount(FinanceAccount financeAccount);

    List<FinanceAccount> getOrgFinanceAccounts(Integer id,List<Integer> orgIntegerList);

    List<AccountPeriod> getAccountPeroidByOrgIDAndFid(Integer id, Integer fid);

    List<AccountDetail> getPeroidDetailByOrgID(Date beginDate, Date endDate, Integer oid, Integer pid);

    List<AccountDetail> getPeroidDetailByOrgIDStr(String beginDate, String endDate, Integer oid, Integer pid);

    void saveAccountDetail(AccountDetail accountDetail);

    void deleteAccountDetail(AccountDetail accountDetail);

    void updateAccountDetail(AccountDetail accountDetail);

    AccountDetail getAccountDetailById(Integer id);

    AccountPeriod getAccountPeroidByID(Integer pid);

    AccountDetailHistory getAccountDetailHistoryById(Integer id);

    List<AccountDetailHistory> getAccountDetailHistoryByadId(Integer adId);

    void updateAccountPeroid(AccountPeriod accountPeriod);

    @Deprecated
    AccountPeriod getPeroidByType(Integer id, int i, Integer fid, Integer periodType);

    FinanceAccount getFinanceAccountById(Integer fid);

    List<AccountPeriod> getPeroidByOrgTime(Date beginDate, Date endDate, Integer fid);

    void updateFinanceAccount(FinanceAccount financeAccount);

    void addPeroid(AccountPeriod a);

    void deleteAccountById(Integer fid);

    List<FinanceAccount> getAllFinanceAccounts();

    List<Date> getPeriodMinAndMaxDate(Integer id);

    List<AccountPeriod> getAccountPeroidByOrgIDAndFidAndDate(Integer id,List<Integer> orgIntegerList, Integer fid, String beginDate, String endDate, Integer periodType);

    List<AccountPeriod> getAccountPeroidByOrgIDFidDate(Integer id, Integer fid, String beginDate, String endDate, Integer periodType);

    List<AccountDetail> getAccountDetailsByOrgIdAndFidAndDay(Integer id, Integer fid);

    List<AccountDetail> getAccountDetailsByOrgIdAndFid(Integer oid, Integer fid);

    List<AccountDetail> getAccountDetailsByOrgIdAndFidAndDate(Integer id, Integer fid, String beginDate, String endTime);

    List<AccountDetail> getPeroidDetailByOrgIDAndDate(Integer id, String beginDate, String endDate, Integer debitType);

    FinanceAccount getFinanceAccountByOidAndType(Integer id, int i);

    void removeNoDataPeriod();

    List<AccountDetail> getPeroidDetailByOrgIDAndDateAndFid(Integer company, String date, Integer fid, Integer dateType);

    List<AccountDetail> getAccountDetailsByFidAndCreditType(Integer fid, Integer creditType);

    Map<String,BigDecimal> getStringWordById(Integer id);

    List<AccountPeriod> getAccountPeroidByDate(String date, Integer id, Integer fid);

    List<AccountPeriod> getPeroidByOrgTimeStr(String firstday, String lastday, Integer fid);

    List<AccountDetail> getAccountDetailByOidAndCreditTypeAndBeginDate(Integer oid, Integer creditType, String beginDate);

    public List<AccountPeriod> getAccountPeroidByDateStr(String begindate, String enddate, Integer id, Integer fid);

    List<FinanceAccountHistory> getFinanceAccountHistoryByAid(Integer aid);

    List<FinanceAccount> getBankAccoutsByOid(Integer id);

    List<AccountDetail> getAccountDetailsByFidAndBeginDateAndEndDate(Integer fid, String beginDate, String endDate);

    List<AccountDetail> getAccountDetailByOidAndCreditTypeAndBeginDate(Integer oid, Integer creditType, String beginDate, Integer fid);

    List<AccountPeriod> getAccountPeroidByFidAndDate(Integer oid, Integer fid, String beginDate, String endDate, Integer periodType);

    List<AccountDetail> getAccountDetailByDate(Integer fid, String date);

    FinanceAccountHistory getFinanceAccountHistoryById(Integer id);

    void addFinanceAccountHistory(AccountDetailHistory accountDetailHistory);

    void addBankFinaceAccountHistory(FinanceAccountHistory financeAccountHistory);

    FinanceAccount getOperationAccount(Integer oid);

    void addAccountDetailHistory(AccountDetailHistory accountDetailHistory);

    void saveFinanceAccountHistory(FinanceAccountHistory financeAccountHistory);

    List<AccountDetail> getAccountDetailListByDay(Integer oid,List<Integer> orgIntegerList, Integer fid, Date beginDate,Date endDate);

    List<AccountPeriod> getAccountPeriodListByDay(Integer oid, List<Integer> orgIntegerList,Integer fid, Date date, Integer periodType);

    AccountPeriod getToDayByfId(Integer accountId);

    AccountPeriod getAccountPeriodByDay(Integer accountId,Date today);

    AccountPeriod getAccountPeriodByMonth(Integer accountId,Date month);

    List<FinanceAccount> getFinanceByOidAccount(Integer oid, String account);

    Map<String,Object> getBalance(Integer oid);

    Map<String,Object> getAllAccountData(Integer oid,List<Integer> orgIntegerList,Integer state,String beginDate,String endDate) throws ParseException;

    Map<String,Object> getAllAccountDataByTime(Integer oid,List<Integer> orgIntegerList,Integer state,Integer fid,String beginDate,String endDate) throws ParseException;

    Map<String,Object> getDataByAccount(Integer oid,List<Integer> orgIntegerList,Integer state,String beginDate,String endDate,Integer sourceCheck);   //pc和手机均有用此接口

    Map<String,Object> getAccountMonthOrDay(Integer oid,List<Integer> orgIntegerList,String monthOrDay,String beginDate,String endDate,Integer fid) throws ParseException;

    AccountDetail getAccountDetailByBusinessHistoryId(Integer businessHistory,String source);

    //数据录入的收入---现金。和/credit.do接口中的收入现金的一样，这里统一下(待合适的时间，将收入接口那里的改为此接口)
    Map<String,Object> getCreditCash(User user, String money, String billAmount, String method, String genre, String summary,
       String purpose, String auditorName, String oppositeCorp, String memo, String source, String categoryDesc,Date receiveAccountDate,Date billDate,Integer business,Date businessDate,Date factDate);

    //数据录入的收入---银行转账。和/credit.do接口中的收入银行转账的一样，这里统一下(待合适的时间，将收入接口那里的改为此接口)
    Map<String,Object> getCreditBank(User user, Integer financeAccountId, String money, String billAmount, String method, String genre, String summary,
       String purpose, String auditorName, String oppositeCorp, Date receiveAccountDate, String memo, String source, String categoryDesc,Date billDate,Integer business,Date businessDate,Date factDate);

    //数据录入的收入---回款票据(method为3或4的)。和/credit.do接口中的收入回款票据的一样，这里统一下(待合适的时间，将收入接口那里的改为此接口)
    Map<String,Object> getCreditReturn(User user, String money, String billAmount, String method, String genre,String bankName,
       String summary, String purpose, String auditorName,Date expireDate, String originalCorp, String oppositeCorp,Date receiveDate, String memo, String source, String categoryDesc,String returnNo,Date billDate,Integer business,Date businessDate,Date factDate);
}
