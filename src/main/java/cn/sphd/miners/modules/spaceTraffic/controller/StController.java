package cn.sphd.miners.modules.spaceTraffic.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.initializer.SaveRpcLog;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.sales.service.CustomerOrganizationService;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.entity.UploadJunkFileClean;
import cn.sphd.miners.modules.uploads.service.CleanJunkFilesListener;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by 朱思旭 on 2021/1/29.
 */
@Controller
@RequestMapping("/st")
public class StController {
    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    UserService userService;
    @Autowired
    CustomerOrganizationService customerOrganizationService;
    @Autowired
    UploadService uploadService;
    @Autowired
    AuthService authService;


    /**
     * <AUTHOR>
     * @Date 2021/2/18 9：59
     * 手机端-资源设置列表  (给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/getStSettings.do")
    public JsonResult getStSettings(User user){
        JsonResult result = null;
        if (user!=null&&user.getOid()==0) {
            String url = System.getProperty("miners.glptApiRoot") + "/st/getStSettings.do";
            HttpClientUtils client = new HttpClientUtils(url);
            result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        }
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2021/2/18 10：41
     * 手机端-修改空间上限接口(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/updateSpaceSetting.do")
    public JsonResult updateSpaceSetting(User user,Double quota){
        JsonResult result = null;
        if (user!=null&&user.getOid()==0){
            String url = System.getProperty("miners.glptApiRoot")+"/st/updateSpaceSetting.do";
            Map<String,String> map=new HashMap<String,String>();
            map.put("quota", quota.toString());
            map.put("creator", user.getUserID().toString());
            map.put("createName",user.getUserName());
            HttpClientUtils client = new HttpClientUtils(url);
            result = client.jsonResponseToT(client.doPost(null,url,null,null,map),JsonResult.class);
        }
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2021/2/18 10：46
     * 手机端-查看空间上限修改记录(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/getSpaceSettingHistories.do")
    public JsonResult getSpaceSettingHistories(User user) {
        JsonResult result = null;
        if (user != null && user.getOid() == 0) {
            String url = System.getProperty("miners.glptApiRoot") + "/st/getSpaceSettingHistories.do";
            HttpClientUtils client = new HttpClientUtils(url);
            result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        }
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2021/2/18 11：56
     * 手机端-修改流量上限接口(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/updateTrafficSetting.do")
    public JsonResult updateTrafficSetting(User user,Double quota){
        JsonResult result=null;
        if (user!=null&&user.getOid()==0){
            String url = System.getProperty("miners.glptApiRoot")+"/st/updateTrafficSetting.do";
            Map<String,String> map=new HashMap<String,String>();
            map.put("quota", quota.toString());
            map.put("creator", user.getUserID().toString());
            map.put("createName",user.getUserName());
            HttpClientUtils client = new HttpClientUtils(url);
            result = client.jsonResponseToT(client.doPost(null,url,null,null,map),JsonResult.class);
        }
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2021/2/18 13：25
     * 手机端-查看流量上限修改记录接口(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/getTrafficSettingHistories.do")
    public JsonResult getTrafficSettingHistories(User user) {
        JsonResult result = null;
        if (user != null && user.getOid() == 0) {
            String url = System.getProperty("miners.glptApiRoot") + "/st/getTrafficSettingHistories.do";
            HttpClientUtils client = new HttpClientUtils(url);
            result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        }
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


    /**
     * <AUTHOR>
     * @Date 2021/2/18 15：25
     * PC端-空间与流量页接口 （管理平台 特殊机构)
     */
    @ResponseBody
    @RequestMapping("/getSpaceTrafficInfo.do")
    public JsonResult getSpaceTrafficInfo(User user,Integer id) {
//        CustomerOrganization customerOrganization=customerOrganizationService.getCustomerOrganizationById(id);
        JsonResult result = null;
        if (user != null && user.getOid() == 0) {
            String url = System.getProperty("miners.glptApiRoot") + "/st/getSpaceTrafficInfo.do?id="+id;
            HttpClientUtils client = new HttpClientUtils(url);
            result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        }
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2021/2/19 16：09
     * 获取机构全部职工*（含身份）
     */
    @ResponseBody
    @RequestMapping("/getRoleAndUsersByOid.do")
    public JsonResult getRoleAndUsersByOid(User user){
       List<User> userList=userService.getRoleAndUsersByOid(user.getOid());
       return new JsonResult(1,userList);
    }

    //给管理平台提供“已用空间”接口
    @AuthPassport(system = true)
    @ResponseBody
    @RequestMapping("/getSpaceForManager.do")
    @SaveRpcLog
    public JsonResult getSpaceForManager(String oid, String accId) {
        Long usingSpace = Long.valueOf("0");
        if (StringUtils.isNotBlank(oid)) {
            Integer oidInt = Integer.parseInt(oid);
            usingSpace = uploadService.uploadFileSize(oidInt, null, "", null);
        }
        if (StringUtils.isNotBlank(accId)) {
            Long accIdL = Long.valueOf(accId);
            AuthAcc acc = authService.getEnabledOrDisabledAcc(accIdL);
            usingSpace = uploadService.uploadFileSize(acc, "", null);
        }
        String usingSpaceH = MyStrings.sizeConver(usingSpace);
        Map<String,Object> map=new HashMap<>();
        map.put("usingSpaceH", usingSpaceH);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2021/3/19
     * 移动端-资源管理页接口
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getSpaceTrafficForApp.do")
    public JsonResult getSpaceTrafficForApp(User user, AuthAcc acc, String type) {
        String url = null;
        Long usingSpace = 0L;
        Long junkFile = 0l;
        Map<String,String> map=new HashMap<String,String>();
        if (StringUtils.isNotBlank(type)) {
            Long accId = acc.getId();
            map.put("accId", accId.toString());
            //已用空间
            usingSpace = uploadService.uploadFileSize(acc, "", null);
            //垃圾空间
            junkFile = uploadService.junkFileSize(acc, "", null);
            url = System.getProperty("miners.glptApiRoot")+"/st/getSpaceTrafficByAccId.do";
            map.put("accId", accId.toString());
        } else {
            Integer oid = user.getOid();
            //已用空间
            usingSpace = uploadService.uploadFileSize(oid, null, "", null);
            //垃圾空间
            junkFile = uploadService.junkFileSize(oid, null, "", null);
            url = System.getProperty("miners.glptApiRoot")+"/st/getSpaceTrafficByOid.do";
            map.put("oid", oid.toString());
        }
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult jsonResult = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        if (jsonResult != null) {
            Map<String,Object> result = (Map<String, Object>) jsonResult.getData();
            //额定空间
            String ratedSpace = result.get("ratedSpace").toString();
            String ratedSpaceH = MyStrings.sizeConver(Long.valueOf(ratedSpace));
            //额定流量
            String ratedTraffic = result.get("ratedTraffic").toString();
            String ratedTrafficH = MyStrings.sizeConver(Long.valueOf(ratedTraffic));
            //已用流量
            String usingTeaffic = result.get("usedTraffic").toString();
            String usingTeafficH = MyStrings.sizeConver(Long.valueOf(usingTeaffic));
            //换算已用空间
            String usingSpaceH = MyStrings.sizeConver(usingSpace);
            //换算垃圾空间
            String junkFileSpaceH = MyStrings.sizeConver(junkFile);
            Date beginDate=NewDateUtils.getNewYearsDay();
            Date endDate=new Date();
            QueryData query = new QueryData();
            query.put("ratedSpaceH", ratedSpaceH);
            query.put("usingSpaceH", usingSpaceH);
            query.put("junkFileSpaceH", junkFileSpaceH);
            query.put("beginDate",beginDate);
            query.put("endDate",endDate);
            query.put("ratedTrafficH", ratedTrafficH);
            query.put("usingTeafficH", usingTeafficH);
            return new JsonResult(1,query);
        } else {
            return new JsonResult(new MyException("403", "管理平台接口错误！"));
        }
    }

    /**
     * <AUTHOR>
     * @Date 2021/3/19
     * 移动端-根据三种条件查询已用空间
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/findUsingSpace.do")
    public JsonResult findUsingSpace(User user, AuthAcc acc, Integer userID, String lastName, String module, String type) {
        QueryData query = new QueryData();
        if (StringUtils.isBlank(type)) {
            Integer oid = user.getOid();
            User userFind = null;
            if (userID == null) {
                List<Integer> listUserID = uploadService.getUploadFileUids(oid);
                List<UserHonePageDto> uploadFileUserList= new ArrayList<>();
                if (!listUserID.isEmpty()) {
                    for (Integer u : listUserID) {
                        if(u != null){
                            UserHonePageDto userHonePageDto = userService.getUserHonePageDtoByUserId(u);
                            uploadFileUserList.add(userHonePageDto);
                        }
                    }
                }
                query.put("uploadFileUserList", uploadFileUserList);
            } else {
                userFind = userService.getUserByID(userID);
                query.put("uploadFileUserList", userFind);
            }
            if (module == null) {
                List<String> listModule = uploadService.getUploadFileModules(oid);
                query.put("listModule", listModule);
            } else {
                query.put("listModule", module);
            }
            Long usingSpace = 0L;
            if (lastName != null) {
                query.put("lastName", lastName);
                if ("其他".equals(lastName)) {
                    List<String> listLastName = Arrays.asList("xlxs","zip","jpg","docx","md","pdf");
                    usingSpace = uploadService.uploadFileSize(oid,userFind,listLastName,module);
                }else {
                    usingSpace = uploadService.uploadFileSize(oid,userFind,lastName,module);
                }
            }else {
                usingSpace = uploadService.uploadFileSize(oid,userFind,"",module);
            }
            String usingSpaceH = MyStrings.sizeConver(usingSpace);
            query.put("usingSpaceH", usingSpaceH);
        } else {
            if (module == null) {
                List<String> listModule = uploadService.getUploadFileModules(acc);
                query.put("listModule", listModule);
            } else {
                query.put("listModule", module);
            }
            Long usingSpace = 0L;
            if (lastName != null) {
                query.put("lastName", lastName);
                if ("其他".equals(lastName)) {
                    List<String> listLastName = Arrays.asList("xlxs","zip","jpg","docx","md","pdf");
                    usingSpace = uploadService.uploadFileSize(acc,listLastName,module);
                }else {
                    usingSpace = uploadService.uploadFileSize(acc,lastName,module);
                }
            }else {
                usingSpace = uploadService.uploadFileSize(acc,"",module);
            }
            String usingSpaceH = MyStrings.sizeConver(usingSpace);
            query.put("usingSpaceH", usingSpaceH);
        }
        return new JsonResult(1,query);
    }

    /**
     * <AUTHOR>
     * @Date 2021/3/19
     * 移动端-获取已用空间的详细记录
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getUsingSpaceRecord.do")
    public JsonResult getUsingSpaceRecord(User user, AuthAcc acc, Integer userID, String lastName, String module, PageInfo pageInfo, String type) {
        QueryData query = new QueryData();
        List<UploadFile> uploadFileList = null;
        if (StringUtils.isBlank(type)) {
            Integer oid = user.getOid();
            User userFind = null;
            if (userID != null) {
                userFind = userService.getUserByID(userID);
            }
            if (lastName != null) {
                if ("其他".equals(lastName)) {
                    List<String> listLastName = Arrays.asList("xlxs","zip","jpg","docx","md","pdf");
                    uploadFileList = uploadService.uploadFileList(oid, userFind, listLastName, module, pageInfo);
                }else {
                    uploadFileList = uploadService.uploadFileList(oid, userFind, lastName, module, pageInfo);
                }
            }else {
                uploadFileList = uploadService.uploadFileList(oid, userFind, "", module, pageInfo);
            }
        } else {
            if (lastName != null) {
                if ("其他".equals(lastName)) {
                    List<String> listLastName = Arrays.asList("xlxs","zip","jpg","docx","md","pdf");
                    uploadFileList = uploadService.uploadFileList(acc, listLastName, module, pageInfo);
                }else {
                    uploadFileList = uploadService.uploadFileList(acc, lastName, module, pageInfo);
                }
            }else {
                uploadFileList = uploadService.uploadFileList(acc, "", module, pageInfo);
            }
        }
        if (!uploadFileList.isEmpty()) {
            for (UploadFile f : uploadFileList) {
                f.setSizeH(MyStrings.sizeConver(f.getSize()));
            }
        }
        query.put("pageInfo",pageInfo);
        query.put("uploadFileList", uploadFileList);
        return new JsonResult(1,query);
    }

    /**
     * <AUTHOR>
     * @Date 2021/3/19
     * 移动端-根据三种条件查询垃圾文件
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/findJunkFileSpace.do")
    public JsonResult findJunkFileSpace(User user, Integer userID, String lastName, String module) {
        Integer oid = user.getOid();
        QueryData query = new QueryData();
        User userFind = null;
        if (userID == null) {
            List<Integer> listUserIDs = uploadService.getJunkFileUids(oid);
            List<UserHonePageDto> junkFileUserList= new ArrayList<>();
            if (!listUserIDs.isEmpty()) {
                for (Integer u : listUserIDs) {
                    UserHonePageDto userHonePageDto = userService.getUserHonePageDtoByUserId(u);
                    junkFileUserList.add(userHonePageDto);
                }
            }
            query.put("junkFileUserList", junkFileUserList);
        }else {
            userFind = userService.getUserByID(userID);
            query.put("junkFileUserList", userFind);
        }
        if (module == null) {
            List<String> listModule = uploadService.getJunkFileModules(oid);
            query.put("junkListModule", listModule);
        }else {
            query.put("junkListModule", module);
        }
        long junkFileSpace = 0;
        if (lastName != null) {
            query.put("lastName", lastName);
            if ("其他".equals(lastName)) {
                List<String> listLastName = Arrays.asList("xlxs","zip","jpg","docx","md","pdf");
                junkFileSpace = uploadService.junkFileSize(oid,userFind,listLastName,module);
            }else {
                junkFileSpace = uploadService.junkFileSize(oid,userFind,lastName,module);
            }
        }else {
            junkFileSpace = uploadService.junkFileSize(oid,userFind,"",module);
        }
        String junkFileSpaceH = MyStrings.sizeConver(junkFileSpace);
        query.put("junkFileSpaceH", junkFileSpaceH);
        return new JsonResult(1,query);
    }

    /**
     * <AUTHOR>
     * @Date 2021/3/19
     * 移动端-一键清除垃圾文件
     */
    @ResponseBody
    @RequestMapping("/cleanJunkFile.do")
    public JsonResult cleanJunkFile(User user) {
        Map<String, String> status = new HashMap<>(3);
        status.put("status", "start");
        status.put("percentage", "0.00");
        status.put("size", "");
        String uuid = UUID.randomUUID().toString().replace("-", "");
        redisTemplate.opsForValue().set("miners:cleanJunkFile:status:" + uuid, status, 1, TimeUnit.HOURS);
        uploadService.cleanOrgJunkFile(user, new CleanJunkFilesListener() {
            String uuid;
            @Override
            public void finished(Long cleanedSize) {//wyu：推送完成消息给前端。
                String msg = MyStrings.sizeConver(cleanedSize);
                status.put("status","success");
                status.put("percentage","100.00");
                status.put("size",msg);
                redisTemplate.opsForValue().set("miners:cleanJunkFile:status:"+uuid, status, 1, TimeUnit.HOURS);
            }
            @Override
            public void progress(Double percentage) {//wyu：推送执行百分比给前端
                System.out.println(String.format("%.2f",percentage)+"%");
                status.put("status","progress");
                status.put("percentage",String.format("%.2f",percentage));
                redisTemplate.opsForValue().set("miners:cleanJunkFile:status:"+uuid, status, 1, TimeUnit.HOURS);
            }
            public CleanJunkFilesListener setParams(String uuid) {
                this.uuid = uuid;
                return this;
            }
        }.setParams(uuid));
        return new JsonResult(1,uuid);
    }
    @ResponseBody
    @RequestMapping("/getCleanJunkFileStatus.do")
    public JsonResult getCleanJunkFileStatus(String uuid) {
        Map<String,String> result = (Map<String, String>) redisTemplate.opsForValue().get("miners:cleanJunkFile:status:"+uuid);
        if(result!=null) {
            if("success".equalsIgnoreCase(result.get("status"))) {
                redisTemplate.delete("miners:cleanJunkFile:status:" + uuid);
            }
            return new JsonResult(1,result);
        } else {
            return new JsonResult(new MyException("404","找不到指定的清理记录！"));
        }
    }
    /**
     * <AUTHOR>
     * @Date 2021/3/19
     * 移动端-获取垃圾文件清理记录
     */
    @ResponseBody
    @RequestMapping("/getCleanJunkFileRecord.do")
    public JsonResult getCleanJunkFileRecord(User user, PageInfo pageInfo) {
        Integer oid = user.getOid();
        HashMap<String, Object> result = new HashMap<>();
        List<UploadJunkFileClean> list = uploadService.getCleanJunkListByOid(oid, pageInfo);
        for(UploadJunkFileClean file : list) {
            file.setSizeH(MyStrings.sizeConver(file.getSize()));
        }
        result.put("list", list);
        result.put("pageInfo",pageInfo);
        return new JsonResult(0,result,pageInfo);
    }

    /**
     * <AUTHOR>
     * @Date 2021/3/19
     * 移动端-根据2种条件查询已用流量
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/findUsingTraffic.do")
    public JsonResult findUsingTraffic(User user, AuthAcc acc, String uploadType, String timeBegin, String timeEnd, String type) {
        String url = null;
        Map<String,String> map=new HashMap<>();
        if (StringUtils.isBlank(type)) {
            url=System.getProperty("miners.glptApiRoot")+"/st/getUsedTrafficByOid.do";
            map.put("oid", user.getOid().toString());
        } else {
            url=System.getProperty("miners.glptApiRoot")+"/st/getUsedTrafficByAccId.do";
            map.put("accId", acc.getId().toString());
        }
        QueryData query = new QueryData();
        if (uploadType != null) {
            query.put("uploadType", uploadType);
            map.put("uploadType", uploadType);
        }else {
            query.put("uploadType", 0);
            map.put("uploadType", Integer.valueOf(0).toString());
        }
        Date dateBegin,dateEnd;
        if (timeBegin != null && timeEnd != null) {
            dateBegin = NewDateUtils.dateFromString(timeBegin,"yyyy-MM-dd");
            dateEnd =  NewDateUtils.getLastTimeOfDay(NewDateUtils.dateFromString(timeEnd,"yyyy-MM-dd"));
        } else {
            dateBegin = NewDateUtils.getNewYearsDay();
            dateEnd = new Date();
        }
        query.put("timeBegin", dateBegin);
        query.put("timeEnd", dateEnd);
        map.put("beginDate",Long.valueOf(dateBegin.getTime()).toString());
        map.put("endDate",Long.valueOf(dateEnd.getTime()).toString());
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult jsonResult = client.jsonResponseToT(client.doPost(null,url,null,null,map),JsonResult.class);
        if (jsonResult != null) {
            String usingTraffic = jsonResult.getData().toString();
            String usingTrafficH = MyStrings.sizeConver(Long.valueOf(usingTraffic));
            query.put("usingTrafficH", usingTrafficH);
            return new JsonResult(1, query);
        } else {
            return new JsonResult(new MyException("403", "管理平台接口错误！"));
        }
    }


    /**
     * <AUTHOR>
     * @Date 2024/12/6
     * 1.288私人领地空间流量
     * 手机端-修改领地空间上限接口(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/updateAccSpaceSetting.do")
    public JsonResult updateAccSpaceSetting(User user,Double quota) {
        String url = System.getProperty("miners.glptApiRoot")+"/st/updateAccSpaceSetting.do";
        Map<String,String> map=new HashMap<String,String>();
        map.put("quota", quota.toString());
        map.put("creator", user.getUserID().toString());
        map.put("createName",user.getUserName());
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult result= client.jsonResponseToT(client.doPost(null,url,null,null,map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;

    }

    /**
     * <AUTHOR>
     * @Date 2024/12/7
     * 1.288私人领地空间流量
     * 手机端-查看领地空间上限修改记录(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/getAccSpaceSettingHistories.do")
    public JsonResult getAccSpaceSettingHistories() {
        String url = System.getProperty("miners.glptApiRoot") + "/st/getAccSpaceSettingHistories.do";
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


    /**
     * <AUTHOR>
     * @Date 2024/12/7
     * 1.288私人领地空间流量
     * 手机端-修改领地流量上限接口(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/updateAccTrafficSetting.do")
    public JsonResult updateAccTrafficSetting(User user,Double quota) {
        String url = System.getProperty("miners.glptApiRoot")+"/st/updateAccTrafficSetting.do";
        Map<String,String> map=new HashMap<String,String>();
        map.put("quota", quota.toString());
        map.put("creator", user.getUserID().toString());
        map.put("createName",user.getUserName());
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult result= client.jsonResponseToT(client.doPost(null,url,null,null,map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2024/12/7
     * 1.288私人领地空间流量
     * 手机端-查看领地流量上限修改记录接口(给特殊机构提供)
     */
    @ResponseBody
    @RequestMapping("/getAccTrafficSettingHistories.do")
    public JsonResult getAccTrafficSettingHistories() {
        String url = System.getProperty("miners.glptApiRoot") + "/st/getAccTrafficSettingHistories.do";
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, null),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }

    /**
     * <AUTHOR>
     * @Date 2024/12/8
     * 1.288私人领地空间流量
     * PC端-用户展示列表  查询单用户领地 空间与流量页接口 （管理平台 特殊机构)
     */
    @ResponseBody
    @RequestMapping("/getAccSpaceTrafficInfo.do")
    public JsonResult getAccSpaceTrafficInfo(User user,Long id,String userName) {
        if (user.getOid()!=0){
            return new JsonResult(new MyException("-1", "权限不够！"));
        }
        String url = System.getProperty("miners.glptApiRoot") + "/st/getAccSpaceTrafficInfo.do";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String,String> map=new HashMap<>();
        map.put("id",id.toString());
        map.put("userName",userName);
        JsonResult result = client.jsonResponseToT(client.doGet(null, url, null, map),JsonResult.class);
        return result==null ? new JsonResult(new MyException("403", "管理平台接口错误！")) : result;
    }


}
