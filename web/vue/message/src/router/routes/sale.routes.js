export default [
  // ----------机构试用、修改产品----------
  {
    path: '/tryApply',
    name: 'trialApply',
    component: () => import('@/view/apply/orgTrial/trial'),
    meta: {
      title: '试用申请'
    }
  },
  {
    path: '/tryApproval',
    name: 'trialApproval',
    component: () => import('@/view/approve/orgTrial/trial'),
    meta: {
      title: '试用审批'
    }
  },
  {
    path: '/trialApplyQuery',
    name: 'trialApplyQuery',
    component: () => import('@/view/apply/orgTrial/trial_query'),
    meta: {
      title: '试用申请-查询'
    }
  },
  {
    path: '/trialApprovalQuery',
    name: 'trialApprovalQuery',
    component: () => import('@/view/approve/orgTrial/trial_query'),
    meta: {
      title: '试用审批-查询'
    }
  },
  {
    path: '/trialApplyQueryPer',
    name: 'trialApplyQueryPer',
    component: () => import('@/view/apply/orgTrial/trial_query_per'),
    meta: {
      title: '试用申请-查询结果'
    }
  },
  {
    path: '/trialApprovalQueryPer',
    name: 'trialApprovalQueryPer',
    component: () => import('@/view/approve/orgTrial/trial_query_per'),
    meta: {
      title: '试用审批-查询结果'
    }
  },
  {
    path: '/productDetail/:id',
    name: 'productDetail',
    component: () => import('@/view/approve/potentialCustomer/productDetail'),
    meta: {
      title: '产品详情'
    }
  },
  {
    path: '/editOrgPopedomApply',
    name: 'moduleChangeApply',
    component: () => import('@/view/apply/orgTrial/moduleChange'),
    meta: {
      title: '我的申请-机构所用产品的修改'
    }
  },
  {
    path: '/editOrgPopedomApproval',
    name: 'moduleChangeApproval',
    component: () => import('@/view/approve/orgTrial/moduleChange'),
    meta: {
      title: '处理/审批-机构所用产品的修改'
    }
  },
  {
    path: '/moduleChangeApplyQuery',
    name: 'moduleChangeApplyQuery',
    component: () => import('@/view/apply/orgTrial/moduleChange_query'),
    meta: {
      title: '我的申请-机构所用产品的修改-查询'
    }
  },
  {
    path: '/moduleChangeApprovalQuery',
    name: 'moduleChangeApprovalQuery',
    component: () => import('@/view/approve/orgTrial/moduleChange_query'),
    meta: {
      title: '处理/审批-机构所用产品的修改-查询'
    }
  },
  {
    path: '/moduleChangeApplyQueryPer',
    name: 'moduleChangeApplyQueryPer',
    component: () => import('@/view/apply/orgTrial/moduleChange_query_per'),
    meta: {
      title: '我的申请-机构所用产品的修改-查询结果'
    }
  },
  {
    path: '/moduleChangeApprovalQueryPer',
    name: 'moduleChangeApprovalQueryPer',
    component: () => import('@/view/approve/orgTrial/moduleChange_query_per'),
    meta: {
      title: '处理/审批-机构所用产品的修改-查询结果'
    }
  },
  // ----------增值服务----------
  {
    path: '/incrementApply',
    name: 'incrementApply',
    component: () => import('@/view/apply/orgTrial/addService'),
    meta: {
      title: '我的申请-增值服务'
    }
  },
  {
    path: '/addServiceApplyQuery',
    name: 'addServiceApplyQuery',
    component: () => import('@/view/apply/orgTrial/addService_query'),
    meta: {
      title: '我的申请-增值服务-查询'
    }
  },
  {
    path: '/addServiceApplyQueryPer',
    name: 'addServiceApplyQueryPer',
    component: () => import('@/view/apply/orgTrial/addService_query_per'),
    meta: {
      title: '我的申请-增值服务-查询结果'
    }
  },
  {
    path: '/incrementApproval',
    name: 'incrementApproval',
    component: () => import('@/view/approve/orgTrial/addService'),
    meta: {
      title: '处理/审批-增值服务'
    }
  },
  {
    path: '/addServiceApprovalQuery',
    name: 'addServiceApprovalQuery',
    component: () => import('@/view/approve/orgTrial/addService_query'),
    meta: {
      title: '处理/审批-增值服务-查询'
    }
  },
  {
    path: '/addServiceApprovalQueryPer',
    name: 'addServiceApprovalQueryPer',
    component: () => import('@/view/approve/orgTrial/addService_query_per'),
    meta: {
      title: '处理/审批-增值服务-查询结果'
    }
  },
  {
    path: '/incrementEditApply',
    name: 'incrementEditApply',
    component: () => import('@/view/apply/orgTrial/addServiceChange'),
    meta: {
      title: '我的申请-增值服务的修改'
    }
  },
  {
    path: '/addServiceChangeApplyQuery',
    name: 'addServiceChangeApplyQuery',
    component: () => import('@/view/apply/orgTrial/addServiceChange_query'),
    meta: {
      title: '我的申请-增值服务的修改-查询'
    }
  },
  {
    path: '/addServiceChangeApplyQueryPer',
    name: 'addServiceChangeApplyQueryPer',
    component: () => import('@/view/apply/orgTrial/addServiceChange_query_per'),
    meta: {
      title: '我的申请-增值服务的修改-查询结果'
    }
  },
  {
    path: '/incrementEditApproval',
    name: 'incrementEditApproval',
    component: () => import('@/view/approve/orgTrial/addServiceChange'),
    meta: {
      title: '处理/审批-增值服务的修改'
    }
  },
  {
    path: '/addServiceChangeApprovalQuery',
    name: 'addServiceChangeApprovalQuery',
    component: () => import('@/view/approve/orgTrial/addServiceChange_query'),
    meta: {
      title: '处理/审批-增值服务的修改-查询'
    }
  },
  {
    path: '/addServiceChangeApprovalQueryPer',
    name: 'addServiceChangeApprovalQueryPer',
    component: () => import('@/view/approve/orgTrial/addServiceChange_query_per'),
    meta: {
      title: '处理/审批-增值服务的修改-查询结果'
    }
  },
  {
    path: '/outOfServiceApply/:id',
    name: 'outOfServiceApply',
    component: () => import('@/view/apply/orgTrial/stopService'),
    meta: {
      title: '我的申请-暂停服务'
    }
  },
  {
    path: '/outOfServiceApproval/:id',
    name: 'outOfServiceApproval',
    component: () => import('@/view/approve/orgTrial/stopService'),
    meta: {
      title: '处理/审批-暂停服务'
    }
  },
  {
    path: '/restoreServiceApply/:id',
    name: 'restoreServiceApply',
    component: () => import('@/view/apply/orgTrial/recoveryService'),
    meta: {
      title: '我的申请-恢复服务'
    }
  },
  {
    path: '/restoreServiceApproval/:id',
    name: 'restoreServiceApproval',
    component: () => import('@/view/approve/orgTrial/recoveryService'),
    meta: {
      title: '处理/审批-恢复服务'
    }
  },
  {
    path: '/stopServiceQuery/:name',
    name: 'stopServiceQuery',
    component: () => import('@/view/query/orgTrial/stopService_query'),
    meta: {
      title: '暂停服务-查询'
    }
  },
  {
    path: '/stopServiceQueryPer',
    name: 'stopServiceQueryPer',
    component: () => import('@/view/query/orgTrial/stopService_query_per'),
    meta: {
      title: '暂停服务--查询结果'
    }
  },
  {
    path: '/recoveryServiceQuery/:name',
    name: 'recoveryServiceQuery',
    component: () => import('@/view/query/orgTrial/recoveryService_query'),
    meta: {
      title: '恢复服务-查询'
    }
  },
  {
    path: '/recoveryServiceQueryPer',
    name: 'recoveryServiceQueryPer',
    component: () => import('@/view/query/orgTrial/recoveryService_query_per'),
    meta: {
      title: '恢复服务--查询结果'
    }
  },
  // ----------潜在客户----------
  {
    path: '/potentialCustomerApply',
    name: 'potentialCustomerApply',
    component: () => import('@/view/apply/potentialCustomer/potentialCustomer'),
    meta: {
      title: '潜在客户申请'
    }
  },
  {
    path: '/potentialDetail/:id',
    name: 'potentialDetail',
    component: () => import('@/view/apply/potentialCustomer/potentialDetail'),
    meta: {
      title: '潜在客户申请-详情'
    }
  },
  {
    path: '/potentialContact/:detailId/:contactId',
    name: 'potentialContact',
    component: () => import('@/view/common/potentialCustomer/contactDetail'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialInteview/:detailId/:interviewId/:source',
    name: 'potentialInteview',
    component: () => import('@/view/common/potentialCustomer/interviewDetail'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialEdit/:updateId',
    name: 'potentialEdit',
    component: () => import('@/view/common/potentialCustomer/potentialEdit'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialToFormal/:id',
    name: 'potentialToFormal',
    component: () => import('@/view/approve/potentialCustomer/potentialToFormal'),
    meta: {
      title: ''
    }
  },
  {
    path: '/baseDetailUpdate/:id',
    name: 'baseDetailUpdate',
    component: () => import('@/view/common/potentialCustomer/baseUpdate'),
    meta: {
      title: ''
    }
  },
  {
    path: '/contactDetailUpdate/:detailId/:contactId/:type',
    name: 'contactDetailUpdate',
    component: () => import('@/view/common/potentialCustomer/contactUpdate'),
    meta: {
      title: ''
    }
  },
  {
    path: '/viewDetailUpdate/:detailId/:interviewId/:type',
    name: 'viewDetailUpdate',
    component: () => import('@/view/common/potentialCustomer/interviewUpdate'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialRecord/:detailId/:recordId/:recordType',
    name: 'potentialRecord',
    component: () => import('@/view/common/potentialCustomer/recordList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/applyPotentialQuery/:per',
    name: 'applyPotentialQuery',
    component: () => import('@/view/common/potentialCustomer/potentialSearch'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialQuerylist',
    name: 'potentialQuerylist',
    component: () => import('@/view/common/potentialCustomer/potentialShList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialCustomerApproval',
    name: 'potentialCustomerApproval',
    component: () => import('@/view/approve/potentialCustomer/potentialCustomer'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialApproveDetail/:id/:type',
    name: 'potentialApproveDetail',
    component: () => import('@/view/approve/potentialCustomer/potentialDetail'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialCustomerApprovalDuty',
    name: 'potentialCustomerApprovalDuty',
    component: () => import('@/view/approve/potentialCustomer/potentialPrincipalApprove'),
    meta: {
      title: ''
    }
  },
  {
    path: '/recordDetail/:type/:detailId/:frontId/:num/:updateName/:updateDate',
    name: 'recordDetail',
    component: () => import('@/view/common/potentialCustomer/recordDetail'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialCustomerMsg/:id',
    name: 'potentialCustomerMsg',
    component: () => import('@/view/common/potentialCustomer/potentialMsg'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialMsgRecord/:id',
    name: 'potentialMsgRecord',
    component: () => import('@/view/common/potentialCustomer/potentialMsgRecord'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialChangeRecord/:id',
    name: 'potentialChangeRecord',
    component: () => import('@/view/common/potentialCustomer/salerChangeRecord'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialQueryDetail/:id/:source',
    name: 'potentialQueryDetail',
    component: () => import('@/view/common/potentialCustomer/potentialShDetail'),
    meta: {
      title: ''
    }
  },
  {
    path: '/potentialDelList/:type/:detailId',
    name: 'potentialDelList',
    component: () => import('@/view/common/potentialCustomer/deleteList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/outMaterialInStockApply/:id',
    name: 'outMaterialInStockApply',
    component: () => import('@/view/apply/purchase/outMaterialInStockApply'),
    meta: {
      title: ''
    }
  },
  {
    path: '/outMaterialInStockApplyList/:id',
    name: 'outMaterialInStockApplyList',
    component: () => import('@/view/apply/purchase/outMaterialInStockApplyList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/outMaterialInStockApproval/:id',
    name: 'outMaterialInStockApproval',
    component: () => import('@/view/approve/purchase/outMaterialInStockApproval'),
    meta: {
      title: ''
    }
  },
  {
    path: '/outMaterialTest/:id',
    name: 'outMaterialTest',
    component: () => import('@/view/approve/purchase/outMaterialTest'),
    meta: {
      title: ''
    }
  },
  {
    path: '/outMaterialTestList/:id',
    name: 'outMaterialTestList',
    component: () => import('@/view/approve/purchase/outMaterialTestList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/unqualifiedHandlePurchase/:id',
    name: 'unqualifiedHandlePurchase',
    component: () => import('@/view/approve/purchase/unqualifiedHandlePurchase'),
    meta: {
      title: ''
    }
  },
  {
    path: '/unqualifiedHandleSkill/:id',
    name: 'unqualifiedHandleSkill',
    component: () => import('@/view/approve/purchase/unqualifiedHandleSkill'),
    meta: {
      title: ''
    }
  },
  {
    path: '/orderQuality/:id',
    name: 'orderQuality',
    component: () => import('@/view/apply/purchase/orderQuality'),
    meta: {
      title: ''
    }
  },
  {
    path: '/orderQualityList/:id',
    name: 'orderQualityList',
    component: () => import('@/view/apply/purchase/orderQualityList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseQuery/:type',
    name: 'purchaseQuery',
    component: () => import('@/view/query/purchase_query'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseQueryPer',
    name: 'purchaseQueryPer',
    component: () => import('@/view/query/purchase_query_per'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseApply/:pid',
    name: 'purchaseApply',
    component: () => import('@/view/apply/purchase/purchaseApply'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseApproval/:pid',
    name: 'purchaseApproval',
    component: () => import('@/view/approve/purchase/purchaseApproval'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseOrderApply',
    name: 'purchaseOrderApply',
    component: () => import('@/view/apply/purchase/purchaseOrderApply'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseOrderApproval',
    name: 'purchaseOrderApproval',
    component: () => import('@/view/approve/purchase/purchaseOrderApproval'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseOrderSearch/:type',
    name: 'purchaseOrderSearch',
    component: () => import('@/view/approve/purchase/purchaseOrderSearch'),
    meta: {
      title: ''
    }
  },
  {
    path: '/purchaseOrderSearchList',
    name: 'purchaseOrderSearchList',
    component: () => import('@/view/approve/purchase/purchaseOrderSearchList'),
    meta: {
      title: ''
    }
  },
  {
    path: '/materialDetail/:id',
    name: 'materialDetail',
    component: () => import('@/view/common/purchase/materialDetail'),
    meta: {
      title: ''
    }
  },
  {
    path: '/productChangePlanApproval',
    name: 'productChangePlanApproval',
    component: () => import('@/view/approve/goodsUpdated/responsePlane'),
    meta: {
      title: ''
    }
  },
  {
    path: '/productChangePlanSendApproval',
    name: 'productChangePlanSendApproval',
    component: () => import('@/view/approve/goodsUpdated/responsePlane2'),
    meta: {
      title: ''
    }
  },
  {
    path: '/responsePlaneDetails/:id/:type',
    name: 'responsePlaneDetails',
    component: () => import('@/view/approve/goodsUpdated/responsePlaneDetails'),
    meta: {
      title: ''
    }
  }
]
