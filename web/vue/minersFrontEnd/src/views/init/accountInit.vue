<template>
  <div id="accountInit">
    <div v-if="mainNum === 1">
      <div>
        <table class="ty-table ty-table-control">
          <tr>
            <td>事件</td>
            <td>操作者</td>
            <td>完成时间</td>
            <td>科目查看</td>
          </tr>
          <tr>
            <td>初始化</td>
            <td>{{recordData.updateName || ''}}</td>
            <td>{{new Date(recordData.updateDate).format('yyyy-MM-dd hh:mm:ss')}}</td>
            <td>
              <span class="ty-color-blue" @click="seeSubjectSet">查看</span>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import {initNav} from "@/utils/routeChange";
import * as initApi from "@/api/init";

export default {
  data() {
    return {
      pageName:'accountInit',
      mainNum: 0,
      rootPath:{},
      recordData: []
    }
  },
  mounted() {
    initNav({mid: 'vi', name: '会计', pageName: this.pageName}, this.getInitData, this)
  },
  methods: {
    getInitData(){
      let rootPath = localStorage.getItem('rootPath')
      this.rootPath = JSON.parse(rootPath)
      initApi.getBuildAccountState().then(res => {
            var field = res.data["data"];
            if (field.res == "4") { //4:建账完成
              this.mainNum = 1
              this.getRecordData()
            } else {
             window.location.href = this.rootPath.webRoot	+ '/accountant/subjectSet.do';
            }
      })
    },
    getRecordData() {
      initApi.initKJinfo().then(res => {
        this.recordData = res.data["data"] || {};
      })
    },
    seeSubjectSet(){
      window.location.href = this.rootPath.webRoot	+ '/accountant/subjectSet.do';
    }
  }
}
</script>
<style scoped lang="scss">
#accountInit {
  padding: 50px 100px;
  font-size: 14px;
}
</style>