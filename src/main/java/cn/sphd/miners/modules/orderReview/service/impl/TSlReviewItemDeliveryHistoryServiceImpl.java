package cn.sphd.miners.modules.orderReview.service.impl;

import java.util.List;

import cn.sphd.miners.modules.orderReview.entity.TSlReviewItemDeliveryHistory;
import cn.sphd.miners.modules.orderReview.mapper.TSlReviewItemDeliveryHistoryMapper;
import cn.sphd.miners.modules.orderReview.service.ITSlReviewItemDeliveryHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
@Transactional
public class TSlReviewItemDeliveryHistoryServiceImpl implements ITSlReviewItemDeliveryHistoryService
{
    @Autowired
    private TSlReviewItemDeliveryHistoryMapper tSlReviewItemDeliveryHistoryMapper;

    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TSlReviewItemDeliveryHistory selectTSlReviewItemDeliveryHistoryById(Long id)
    {
        return tSlReviewItemDeliveryHistoryMapper.selectTSlReviewItemDeliveryHistoryById(id);
    }

    /**
     * 查询列表
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 
     */
    @Override
    public List<TSlReviewItemDeliveryHistory> selectTSlReviewItemDeliveryHistoryList(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory)
    {
        return tSlReviewItemDeliveryHistoryMapper.selectTSlReviewItemDeliveryHistoryList(tSlReviewItemDeliveryHistory);
    }

    /**
     * 新增
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 结果
     */
    @Override
    public int insertTSlReviewItemDeliveryHistory(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory)
    {
        return tSlReviewItemDeliveryHistoryMapper.insertTSlReviewItemDeliveryHistory(tSlReviewItemDeliveryHistory);
    }

    /**
     * 修改
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 结果
     */
    @Override
    public int updateTSlReviewItemDeliveryHistory(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory)
    {
        return tSlReviewItemDeliveryHistoryMapper.updateTSlReviewItemDeliveryHistory(tSlReviewItemDeliveryHistory);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTSlReviewItemDeliveryHistoryByIds(Long[] ids)
    {
        return tSlReviewItemDeliveryHistoryMapper.deleteTSlReviewItemDeliveryHistoryByIds(ids);
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTSlReviewItemDeliveryHistoryById(Long id)
    {
        return tSlReviewItemDeliveryHistoryMapper.deleteTSlReviewItemDeliveryHistoryById(id);
    }
}
