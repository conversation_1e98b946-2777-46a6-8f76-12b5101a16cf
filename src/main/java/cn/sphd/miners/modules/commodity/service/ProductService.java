package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/10/12.
 */
public interface ProductService {
    List<PdMerchandise> getByNameInnerSn(Integer oid, String customerName, String innerSn,String outerSn);

    List<PdMerchandise> getCusProByProductId(Integer productId);

    List<PdMerchandise> getByProductId(Integer productId, Integer cusProId);

    List<PdMerchandise> getListByOid(Integer oid, String type);

    PdMerchandise getByCusProIdAndProId(Integer cusProId, Integer productId);

    PdMerchandise getPdByCusProId(Integer cusProId);

    void addPdMerchandise(PdMerchandise pdCusPro);

    void updatePdMerchandise(PdMerchandise pdMerchandise);

    void deleteByCusProId(PdMerchandise pdMerchandise);



    PdBase getById(Integer id);

    void addPdBase(PdBase pdBase);

    void updatePdBase(PdBase pdBase);

    PdBase getByInnerSn(Integer oid, String innerSn);

    List<PdBase> getByInnerSnVague(Integer oid, String innerSn);

    void deletePdBase(PdBase pdBase);

    PdMerchandise getByPdCusId(Integer id);

    PdMerchandise getByMtStock0(Integer Id);

    PdMerchandise getPdBycusIdAndOuterSn(Integer cusId, String outerSn);

    PdMerchandise getBySalesRelationShip(Integer id);

    List<PdMerchandise> getByProductIdAndOid(Integer id, Integer oid);

    List<PdMerchandise> getByINnerSn(String innerSn, Integer productId);

    void deleteById(Integer product_);

    void addPdMerchandiseHistory(PdMerchandiseHistory pcph);

    //获取所有产品，或零件
    List<PdBase> getListByType(Integer oid,String type);

    PdMerchandise getByPdCustomerId(Integer id);
    //根据客户获取商品列表
    List<Map<String,Object>> getListByCustomer(Integer customerId);
    //获取交付过的商品
    List<Map<String,Object>> getProductForJF(Integer customerId);
    //按年获取已交互过的商品
    List<Map<String,Object>> getProductJFForYearById(Integer productId,Integer customerId);
    //按月获取已交互过的商品
    List<Map<String,Object>> getProductJFForMonthById(Integer productId,String year,Integer customerId);
    //按月获取已交互过的商品
    List<Map<String,Object>> getProductJfOrderById(Integer productId,Integer month,Integer customerId,String sort,String direction);
    //hu
    List<Map<String,Object>> getProductListByType(String type,Integer oid,String enabled);


    Map getIncMerchandises(User user, String ids);
}
