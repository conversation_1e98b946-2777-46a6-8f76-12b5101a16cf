import auth from '@/sys/auth'
import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: "/",
    name: "Start",
    component: () => import("../views/Start.vue"),
  },
  {
    path: "/StartActive",
    name: "StartActive",
    component: () => import("../views/StartActive.vue"),
  },
  {
    path: "/actAcc1",
    name: "actAcc1",
    component: () => import("../views/actAcc1.vue"),
  },
  {
    path: "/actAcc2",
    name: "actAcc2",
    component: () => import("../views/actAcc2.vue"),
  },
  {
    path: "/actOldOrNew",
    name: "actOldOrNew",
    component: () => import("../views/actOldOrNew.vue"),
  },
  {
    path: "/orgList",
    name: "orgList",
    component: () => import("../views/orgList.vue"),
  },
  {
    path: "/orgListChange",
    name: "orgListChange",
    component: () => import("../views/orgListChange.vue"),
  },
  {
    path: "/errPage",
    name: "errPage",
    component: () => import("../views/errPage.vue"),
  },
  {
    path: '/demoSms',
    name: 'demoSms',
    component: () => import('@/views/demoSms.vue'),
    meta: {
      title: '测试短信',
      keepAlive: false
    }
  },
  {
    path: "/mainPage",
    name: "mainPage",
    component: () => import("../views/mainPage.vue"),
    children: [
      {
        path: '',
        name: 'home',
        component: () => import('../views/home.vue'),
        children: [
          {
            path: "/init_RAMtWh",
            name: "init_RAMtWh",
            component: () => import("../views/init/RAMtWh.vue"),
            meta: {
              title: '初始化-原辅材料库',
              keepAlive: false
            }
          },
          {
            path: "/init_shelvesWarehouse",
            name: "init_shelvesWarehouse",
            component: () => import("../views/init/shelvesWarehouse.vue"),
            meta: {
              title: '初始化-货架与库位',
              keepAlive: false
            }
          },

          {
            path: "/customerManage",
            name: "customerManage",
            component: () => import("../views/sale/customerManage.vue"),
            meta: {
              title: '销售管理-客户管理',
              keepAlive: false
            }
          },
          {
            path: "/orgManage",
            name: "orgManage",
            component: () => import("../views/sale/orgManage.vue"),
            meta: {
              title: '销售管理-机构管理',
              keepAlive: false
            }
          },
          {
            path: "/overViewCustomerInfo",
            name: "overViewCustomerInfo",
            component: () => import("../views/companyOverview/customers/customerInfo.vue"),
            meta: {
              title: '公司总览-客户-客户信息',
              keepAlive: false
            }
          },
          {
            path: "/sale_contract",
            name: "sale_contract",
            component: () => import("../views/sale/contract.vue"),
            meta: {
              title: '销售管理-销售合同',
              keepAlive: false
            }
          },
          {
            path: "/sale_category",
            name: "sale_category",
            component: () => import("../views/sale/categoryManage.vue"),
            meta: {
              title: '销售管理-类别管理',
              keepAlive: false
            }
          },
          {
            path:"/initialSettings",
            name:"initialSettings",
            component: () => import("../views/machineEquipment/initialSettings.vue"),
            meta: {
              title: '初始设置',
              keepAlive: false
            }
          },
          {
            path:"/nameManageMent",
            name:"nameManageMent",
            component: () => import("../views/equipMent/nameManageMent.vue"),
            meta: {
              title: '名称管理',
              keepAlive: false
            }
          },
          {
            path:"/categoryManageMent",
            name:"categoryManageMent",
            component: () => import("../views/equipMent/categoryManageMent.vue"),
            meta: {
              title: '类别管理',
              keepAlive: false
            }
          },
          {
            path: '/desktop',
            name: 'desktop',
            component: () => import('../views/myDesktop/desktop.vue'),
            meta: {
              title: '我的桌面',
              keepAlive: false
            }
          },
          {
            path: '/systemSet',
            name: 'systemSet',
            component: () => import('../views/userCenter/systemSet.vue'),
            meta: {
              title: '系统设置',
              keepAlive: false
            }
          },
          {
            path: '/demoPage',
            name: 'demoPage',
            component: () => import('../views/demoPage.vue'),
            meta: {
              title: '实例页面',
              keepAlive: false
            }
          },
          {
            path: '/demoUpload',
            name: 'demoUpload',
            component: () => import('@/views/demoUpload.vue'),
            meta: {
              title: '测试断点上传',
              keepAlive: false
            }
          },
          {
            path: '/loginSet',
            name: 'loginSet',
            component: () => import('../views/userCenter/loginSet.vue'),
            meta: {
              title: '系统设置',
              keepAlive: false
            }
          },
          {
            path: '/discussion',
            name: 'discussion',
            component: () => import('../views/discussion/discussion.vue'),
            meta: {
              title: '系统设置',
              keepAlive: false
            }
          },
          {
            path: '/packingManage',
            name: 'packingManage',
            component: () => import('../views/technology/packingManage.vue'),
            meta: {
              title: '包装',
              keepAlive: false
            }
          },
          {
            path: '/productFeatures',
            name: 'productFeatures',
            component: () => import('../views/processInit/productFeatures.vue'),
            meta: {
              title: '产品特性',
              keepAlive: false
            }
          },
          {
            path: '/weixinBind',
            name: 'weixinBind',
            component: () => import('../views/thirdPlatform/weixinBind.vue'),
            meta: {
              title: '微信绑定',
              keepAlive: false
            }
          },      {
            path: "/train_questionBank",
            name: "train_questionBank",
            component: () => import("../views/train/questionBank.vue"),
            meta: {
              title: '培训管理-试题库',
              keepAlive: false
            }
          },
          {
            path: '/processSet',
            name: 'processSet',
            component: () => import('../views/processManagement/processSet.vue'),
            meta: {
              title: '工序设置',
              keepAlive: false
            }
          },
          {
            path: '/materalPakaging',
            name: 'materalPakaging',
            component: () => import('../views/procure/materalPakaging.vue'),
            meta: {
              title: '材料包装',
              keepAlive: false
            }
          },
          {
            path: '/employeeIndex',
            name: 'employeeIndex',
            component: () => import('../views/generalAffairs/employeeIndex.vue'),
            meta: {
              title: '职工档案',
              keepAlive: false
            }
          },
          {
            path: '/investigation',
            name: 'investigation',
            component: () => import('../views/investigationManage/investigation.vue'),
            meta: {
              title: '调查管理',
              keepAlive: false
            }
          },
          {
            path: '/openFinance',
            name: 'openFinance',
            component: () => import('../views/init/openFinance.vue'),
            meta: {
              title: '财务',
              keepAlive: false
            }
          },
          // 下面的部分暂时不提交，文件需要提交时记得注释掉
          // {
          //   path: '/supplieContract',
          //   name: 'supplieContract',
          //   component: () => import('../views/supplierproduct/supplieContract.vue'),
          //   meta: {
          //     title: '常规信息',
          //     keepAlive: false
          //   }
          // },
          {
            path: '/MCI',
            name: 'MCI',
            component: () => import('../views/supplierproduct/MCI.vue'),
            meta: {
              title: 'MCI',
              keepAlive: false
            }
          },
          // {
          //   path: '/salesManage',
          //   name: 'salesManage',
          //   component: () => import('../views/sale/salesManage.vue'),
          //   meta: {
          //     title: '销售管理-缴费记录',
          //     keepAlive: false
          //   }
          // },
          {
            path: '/overviewReplyBill',
            name: 'overviewReplyBill',
            component: () => import('../views/companyOverview/finance/replyBill.vue'),
            meta: {
              title: '公司总览',
              keepAlive: false
            }
          },
          {
            path: '/overviewIndexForAdmin',
            name: 'overviewIndexForAdmin',
            component: () => import('../views/companyOverview/finance/indexForAdmin.vue'),
            meta: {
              title: '公司总览',
              keepAlive: false
            }
          },
          {
            path: '/authority',
            name: 'authority',
            component: () => import('../views/authority/authority.vue'),
            meta: {
              title: '权限设置',
              keepAlive: false
            }
          },
          {
            path: '/equipmentList',
            name: 'equipmentList',
            component: () => import('../views/equipMent/equipmentList.vue'),
            meta: {
              title: '装备清单',
              keepAlive: false
            }
          },
          {
            path: '/equitmentAddRe',
            name: 'equitmentAddRe',
            component: () => import('../views/equipMent/suppleMentaryRecording.vue'),
            meta: {
              title: '补录',
              keepAlive: false
            }
          },
          {
            path: '/equipMentSupplier',
            name: 'equipMentSupplier',
            component: () => import('../views/equipMent/equipMentSupplier.vue'),
            meta: {
              title: '供应商',
              keepAlive: false
            }
          },
          {
            path: '/purchaseContract',
            name: 'purchaseContract',
            component: () => import('../views/supplier/purchaseContract.vue'),
            meta: {
              title: '采购合同',
              keepAlive: false
            }
          },
          {
            path: '/equipmentInit',
            name: 'equipmentInit',
            component: () => import('../views/init/equipmentInit.vue'),
            meta: {
              title: '装备器具',
              keepAlive: false
            }
          },
          {
            path: '/accountInit',
            name: 'accountInit',
            component: () => import('../views/init/accountInit.vue'),
            meta: {
              title: '会计',
              keepAlive: false
            }
          },
          {
            path: '/loginRecord',
            name: 'loginRecord',
            component: () => import('../views/companyOverview/loginRecord.vue'),
            meta: {
              title: '公司总览-登录记录',
              keepAlive: false
            }
          },
          {
            path: '/myLoginRecord',
            name: 'myLoginRecord',
            component: () => import('../views/myDesktop/myLoginLog.vue'),
            meta: {
              title: '我的桌面-我的登录记录',
              keepAlive: false
            }
          },
          {
            path: '/myTeamLogin',
            name: 'myTeamLogin',
            component: () => import('../views/myTeam/myTeamLoginRecord.vue'),
            meta: {
              title: '我的团队-登录记录',
              keepAlive: false
            }
          },
          {
            path: '/openManage',
            name: 'openManage',
            component: () => import('../views/openManage.vue'),
            meta: {
              title: '启用管理',
              keepAlive: false
            }
          },
          {
            path: '/currentSettings',
            name: 'currentSettings',
            component: () => import('../views/authority/currentSettings.vue'),
            meta: {
              title: '权限管理-当前权限',
              keepAlive: false
            }
          },

          // {
          //   path: '/attendance',
          //   name: 'attendance',
          //   component: () => import('../views/generalAffairs/attendance.vue'),
          //   meta: {
          //     title: '总务管理-考勤管理',
          //     keepAlive: false
          //   }
          // },np
          {
            path: '/saleInit',
            name: 'saleInit',
            component: () => import('../views/init/saleInit.vue'),
            meta: {
              title: '初始化-销售1',
              keepAlive: false
            }
          },
          {
            path: '/interagency',
            name: 'interagency',
            component: () => import('../views/generalAffairs/interagencyManagement.vue'),
            meta: {
              title: '跨机构管理',
              keepAlive: false
            }
          },
          {
            path: '/scanCodeInterview',
            name: 'scanCodeInterview',
            component: () => import('../views/recruit/scanCodeInterview.vue'),
            meta: {
              title: '面试扫码',
              keepAlive: false
            }
          }
        ]
      },
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})
//无需登录可前往的页面
const guestPaths = [
    '/',
    '/StartActive',
    '/errPage',
    '/actAcc1',
    '/actAcc2',
    '/actOldOrNew',
    '/demoSms'
]
////领地或选机构页面
const manorPaths = [
    '/orgList'
]
router.beforeEach((to,from,next)=>{
  // 处理错误页面
  if (auth.isLogged() && !auth.isLogInOrg() && manorPaths.length > 0 && !manorPaths.includes(to.path) && !guestPaths.includes(to.path)) {
    console.log('情况1', to)
    next('/orgList')
  } else if (!auth.isLogged() && guestPaths.length > 0 && !guestPaths.includes(to.path)) {
    // console.log('进不去哦', to)
    console.log('情况2', to)
    next('/')

  } else {
    console.log('情况3', to)

    next()
  }
})
export default router
