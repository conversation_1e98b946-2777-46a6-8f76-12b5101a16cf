package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2020-11-09 
 */

@Entity ( name ="UserImport" )
@Table ( name ="t_sys_user_import" )
public class UserImport implements Serializable {


	/**
	 * ID
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 导入ID
	 */
   	@Column(name ="import" )
	private Integer importId;

	/**
	 * 姓名
	 */
   	@Column(name = "user_name" )
	private String userName;

	/**
	 * 手机号
	 */
   	@Column(name = "mobile" )
	private String mobile;

	/**
	 * 是否在职
	 */
   	@Column(name = "isDuty" )
	private String isDuty;

	/**
	 * 部门
	 */
   	@Column(name = "department" )
	private String department;

	/**
	 * 领导
	 */
   	@Column(name = "leader" )
	private String leader;

	/**
	 * 登录名
	 */
   	@Column(name = "logonName" )
	private String logonName;

	/**
	 * 登录密码
	 */
   	@Column(name = "logonPwd" )
	private String logonPwd;

	/**
	 * 密码修改时间
	 */
   	@Column(name = "password_time" )
	private Date passwordTime;

	/**
	 * 登录状态
	 */
   	@Column(name = "logonState" )
	private Long logonState;

	/**
	 * 领导姓名
	 */
   	@Column(name = "leader_name" )
	private String leaderName;

	/**
	 * 普通员工
	 */
   	@Column(name = "ordinary_employees" )
	private Integer ordinaryEmployees;

	/**
	 * 上级ID
	 */
   	@Column(name = "super_id" )
	private String superId;

	/**
	 * 状态
	 */
   	@Column(name = "status" )
	private String status;

	/**
	 * 管理员
	 */
   	@Column(name = "manager" )
	private Integer manager;

	/**
	 * 管理员代码
	 */
   	@Column(name = "manager_code" )
	private String managerCode;

	/**
	 * 角色代码
	 */
   	@Column(name = "role_code" )
	private String roleCode;

	/**
	 * 是否拥有权限
	 */
   	@Column(name = "has_right" )
	private Integer hasRight;

	/**
	 * 导入状态 0-开始导入,3-导入已完成
	 */
   	@Column(name = "source" )
	private String source;

	/**
	 * 等级url
	 */
   	@Column(name = "rank_url" )
	private String rankUrl;

	/**
	 * 提交状态
	 */
   	@Column(name = "submit_state" )
	private String submitState;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	/**
	 * 操作:1-增,2-删,3-修改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;
	/**
	 * user表ID
	 */
	@Column(name = "user" )
	private Integer user;

	/**
	 * 直接上级来源:1-本表,2-user表
	 */
	@Column(name = "leader_source" )
	private Integer leaderSource;

	public Integer getUser() {
		return user;
	}

	public void setUser(Integer user) {
		this.user = user;
	}

	public Integer getLeaderSource() {
		return leaderSource;
	}

	public void setLeaderSource(Integer leaderSource) {
		this.leaderSource = leaderSource;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getImportId() {
		return importId;
	}

	public void setImportId(Integer importId) {
		this.importId = importId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getIsDuty() {
		return isDuty;
	}

	public void setIsDuty(String isDuty) {
		this.isDuty = isDuty;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getLeader() {
		return leader;
	}

	public void setLeader(String leader) {
		this.leader = leader;
	}

	public String getLogonName() {
		return logonName;
	}

	public void setLogonName(String logonName) {
		this.logonName = logonName;
	}

	public String getLogonPwd() {
		return logonPwd;
	}

	public void setLogonPwd(String logonPwd) {
		this.logonPwd = logonPwd;
	}

	public Date getPasswordTime() {
		return passwordTime;
	}

	public void setPasswordTime(Date passwordTime) {
		this.passwordTime = passwordTime;
	}

	public Long getLogonState() {
		return logonState;
	}

	public void setLogonState(Long logonState) {
		this.logonState = logonState;
	}

	public String getLeaderName() {
		return leaderName;
	}

	public void setLeaderName(String leaderName) {
		this.leaderName = leaderName;
	}

	public Integer getOrdinaryEmployees() {
		return ordinaryEmployees;
	}

	public void setOrdinaryEmployees(Integer ordinaryEmployees) {
		this.ordinaryEmployees = ordinaryEmployees;
	}

	public String getSuperId() {
		return superId;
	}

	public void setSuperId(String superId) {
		this.superId = superId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getManager() {
		return manager;
	}

	public void setManager(Integer manager) {
		this.manager = manager;
	}

	public String getManagerCode() {
		return managerCode;
	}

	public void setManagerCode(String managerCode) {
		this.managerCode = managerCode;
	}

	public String getRoleCode() {
		return roleCode;
	}

	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}

	public Integer getHasRight() {
		return hasRight;
	}

	public void setHasRight(Integer hasRight) {
		this.hasRight = hasRight;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getRankUrl() {
		return rankUrl;
	}

	public void setRankUrl(String rankUrl) {
			this.rankUrl=rankUrl;
	}

	public String getSubmitState() {
		return submitState;
	}

	public void setSubmitState(String submitState) {
		this.submitState = submitState;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}
}
