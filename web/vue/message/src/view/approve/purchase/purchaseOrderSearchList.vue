<template>
  <div id="purchaseOrderSearchList" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ttl11">
          <p>您要查询：</p>
          <p>{{ beginTimeShow | formatDay('YYYY年MM月DD日')}} - {{ endTimeShow | formatDay('YYYY年MM月DD日')}}期间</p>
          <p v-if="paramQuery.supplier > 0">由<span style="color:#88aaf1">{{ paramQuery.supplierName}}</span>供应的</p>
          <p>
            <span v-if="paramQuery.approvalStatus == 3 &&paramQuery.rejectReasion">由于{{ paramQuery.rejectReasion | formatRejectReasion }}原因</span>{{ paramQuery.approvalStatus | formatApprovalStatus }}的采购申请</p>
          <p></p>
        </div>
        <div class="ttl1">符合查询条件的数据共如下 <span v-if="!monthListShow">{{list.length}}</span><span v-else>{{listSum}}</span> 条。</div>
      </div>
      <div>
        <div>
          <div class="ui-cells" v-if="!monthListShow">
            <a class="ui-cell" v-on:click="jump(item.purchaseOrder.id)" v-for="(item, index) in list" v-bind:key="index">
              <div class="orderItem">
                <div><div>{{item.purchaseOrder.supplierName}}</div><div>{{item.purchaseOrder.type | formatType}}</div></div>
                <div><div>{{item.typeKind}}种物料</div><div style="color:#ccc;">{{item.purchaseOrder.createName}} {{item.purchaseOrder.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div></div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
          <div v-else>
            <div class="flexItem" v-for="(item, index) in list" v-bind:key="index">
              <span>{{item.yearMonth}}</span><span>{{item.num}}条</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #purchaseOrderSearchList{
    .buttonGroupRight .nav_btn:last-child { padding:0 30px; }
    .ttl1{ background:#DEEBF6; padding:10px;     }
    .ttl11{ background: #edf3c6; padding:10px;     }
    .container>div>a{  background: #fff; }
    .container>div>a:nth-child(odd){ background:#f3f3f3;  }
    .orderItem{ width: 100%; font-size:1em;   }
    .orderItem:nth-child(even){ background: #eee; }
    .orderItem>div{ display: flex; }
    .orderItem>div>div{ flex:1; }
    .orderItem>div>div:nth-child(2){ text-align: right; margin-left: -30px;  font-size: 0.7em; }
    .queryCondition{  background: #fffaeb;  font-size: 12px;  padding: 8px;  margin-bottom: 5px;    }
    .flexItem{
      display:flex;
      line-height: 30px;
      text-align: center;
      &:nth-child(even){ background:#fff;   }
      span{ flex:1;  }
    }
  }
</style>
<script>
export default {
  name: 'purchaseOrderSearchList',
  data () {
    return {
      beginTimeShow: '',
      loading: true,
      endTimeShow: '',
      list: true,
      monthListShow: false,
      listSum: 0,
      num: true,
      title: '采购申请',
      paramQuery: {},
      queryData: {
        data: [],
        beginDate: '',
        endDate: ''
      }
    }
  },
  created: function () {
    let paramQuery = JSON.parse(localStorage.getItem('query'))
    this.paramQuery = paramQuery
    let that = this
    let url = '../../../purchaseOrderApproval/getPurchaseOrderApplys.do'
    that.searchType = paramQuery.searchType
    if (Number(that.searchType) === 1) {
      url = '../../../purchaseOrderApproval/getPurchaseOrderApprovals.do'
    }
    let data = {
      approvalStatus: paramQuery.approvalStatus,
      beginDate: paramQuery.beginDate,
      endDate: paramQuery.endDate,
      type: paramQuery.type,
      supplier: paramQuery.supplier,
      rejectReasion: paramQuery.rejectReasion,
      userId: that.sphdSocket.user.userID
    }
    console.log('url：', url)
    console.log('打印传参：')
    console.log(data)
    that.axios.post(url, data).then((response) => {
      that.loading = false
      let data = response.data.data
      console.log('返回值')
      console.log(data)
      if (data) {
        that.list = data.ordersList
        that.beginTimeShow = data.beginDate
        that.endTimeShow = data.endDate
        that.num = data.num
        that.listSum = 0
        console.log(that.list.length > 0)
        console.log(that.list[0]['yearMonth'])
        that.monthListShow = Boolean(that.list.length > 0 && that.list[0]['yearMonth'])
        console.log(that.monthListShow)
        if (that.monthListShow) {
          that.list.forEach(function (item) {
            that.listSum += item.num
          })
        }
      } else {
        that.$kiko_message('加载失败')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  filters: {
    formatApprovalStatus: function (ApprovalStatus) {
      let str = ''
      switch (Number(ApprovalStatus)) {
        case 2:
          str = '被批准'
          break
        case 3:
          str = '被驳回'
          break
        case 4:
          str = '所撤回'
          break
      }
      return str
    },
    formatRejectReasion: function (RejectReasion) {
      let str = ''
      switch (Number(RejectReasion)) {
        case 1:
          str = '供应商价格贵'
          break
        case 2:
          str = '供应商质量不稳定'
          break
        case 3:
          str = '供应商服务态度不好'
          break
        case 4:
          str = '供应商无法确保准时交货'
          break
        case 5:
          str = '付款方式不合理'
          break
        case 6:
          str = '暂无需采购'
          break
        case 7:
          str = '发票原因'
          break
        case 8:
          str = '其他原因'
          break
      }
      return str
    },
    formatType: function (type) {
      let str = ''
      switch (type) {
        case '1':
        case 1:
          str = '新单'
          break
        case '2':
        case 2:
          str = '库存预警的材料'
          break
        case '3':
        case 3:
          str = '个人申购的新材料'
          break
        case '4':
        case 4:
          str = '补货'
          break
        case '5':
        case 5:
          str = '零星采购'
          break
        default:
      }
      return str
    }
  },
  methods: {
    jump: function (id) {
      window.parent.floatToPage('../../../purchaseOrderApproval/purchasePage.do', { 'id': id, 't': 2 })
    }
  }
}
</script>
