package cn.sphd.miners.modules.finance.entity;

import cn.sphd.miners.modules.sales.entity.SlInvoiceApplicationDetail;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2017/7/7.
 * 发票明细表
 */
@Entity
@Table(name="t_finance_invoice_detail")
public class FinanceInvoiceDetail implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org" ,  nullable=true , unique=false)
    private Integer org;  //机构

    @Column(name="peroid" ,  nullable=true , unique=false)
    private Integer peroid;  //统计期（所属年月）

    @Column(name="invoice_no"  , length=8 , nullable=true , unique=false)
    private String invoiceNo;  //号码

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;  //状态:1-空白,2-已使用,3-作废,4-启用

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;  //金额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;   //摘要

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;   //用途

    @Column(name="applicant"   , nullable=true , unique=false)
    private Integer applicant;   //申请人ID

    @Column(name="applicant_name"  , length=100 , nullable=true , unique=false)
    private String applicantName;   //申请人姓名

    @Column(name="application_time"   , nullable=true , unique=false)
    private Date applicationTime;   //申请时间

    @Column(name="tax_inclusive"   , nullable=true , unique=false)
    private String taxInclusive;   //是否含税(0-否  1-是)

    @Column(name="operate_date"   , nullable=true , unique=false)
    private Date operateDate;   //开票日期

    @Column(name="receive_corp_id"   , nullable=true , unique=false)
    private Integer receiveCorpId;  //接受公司ID(暂时存入客户id)

    @Column(name="receive_corp"  , length=100 , nullable=true , unique=false)
    private String receiveCorp;  //接收公司(暂时存入客户名称)

    @Column(name="receive_date"   , nullable=true , unique=false)
    private Date receiveDate;   //接收日期

    @Column(name="receiver"  , length=100 , nullable=true , unique=false)
    private String receiver;   //接收经手人

    @Column(name="operator"  , length=100 , nullable=true , unique=false)
    private String operator;   //支付经手人

    @Column(name="reason"  , length=255 , nullable=true , unique=false)
    private String reason;   //作废原因

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作：0-暂存,1-增,2-删,3-改,4-作废,5-改为尚未开具

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    //新增
    @Column(name="invoice_code"  , length=255 , nullable=true , unique=false)
    private String invoiceCode;  //发票代码

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;  //1-增值税专用票,2-增值税普通票,3-其它普通票

    @Column(name="losted"  , length=1 , nullable=true , unique=false)
    private String losted;  //丢失状态:0-未丢失,1-发票被销售弄丢,2-发票被客户弄丢,计划重新开票,3-发票被客户弄丢,计划不予补开,4-发票由于其他原因丢失,计划重新开票,5-发票由于其他原因丢失,计划不予补开,6-发票未丢失但未签收,计划不予补开

    @Column(name="line"  , nullable=true , unique=false)
    private Integer line;  //行数

    @Column(name="teminate_time"   , nullable=true , unique=false)
    private Date teminateTime;  //销售终止时间(见销售开票)

    @Column(name="teminate_label"   , nullable=true , unique=false)
    private boolean teminateLabel;  //销售终止标识,true-终止  0-false 1-true

    @Column(name="version_no"  , nullable=true , unique=false)
    private String versionNo;  //版本号,每次修改+1

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="source"  , length=1 , nullable=true , unique=false)
    private String source;  //来源:1-财务录入,2-销售

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="invoice_reg", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceInvoice invoiceReg;

    @Column(name="invoice_reg"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer invoiceReg_;

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceInvoiceDetailHistory.class, fetch= FetchType.LAZY, mappedBy="invoiceDetail", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceInvoiceDetailHistory> financeInvoiceDetailHistoryHashSet = new HashSet<FinanceInvoiceDetailHistory>();



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Integer getApplicant() {
        return applicant;
    }

    public void setApplicant(Integer applicant) {
        this.applicant = applicant;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public Date getApplicationTime() {
        return applicationTime;
    }

    public void setApplicationTime(Date applicationTime) {
        this.applicationTime = applicationTime;
    }

    public String getTaxInclusive() {
        return taxInclusive;
    }

    public void setTaxInclusive(String taxInclusive) {
        this.taxInclusive = taxInclusive;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public Integer getReceiveCorpId() {
        return receiveCorpId;
    }

    public void setReceiveCorpId(Integer receiveCorpId) {
        this.receiveCorpId = receiveCorpId;
    }

    public String getReceiveCorp() {
        return receiveCorp;
    }

    public void setReceiveCorp(String receiveCorp) {
        this.receiveCorp = receiveCorp;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public FinanceInvoice getInvoiceReg() {
        return invoiceReg;
    }

    public void setInvoiceReg(FinanceInvoice invoiceReg) {
        this.invoiceReg = invoiceReg;
    }

    public Integer getInvoiceReg_() {
        return invoiceReg_;
    }

    public void setInvoiceReg_(Integer invoiceReg_) {
        this.invoiceReg_ = invoiceReg_;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLosted() {
        return losted;
    }

    public void setLosted(String losted) {
        this.losted = losted;
    }

    public Integer getLine() {
        return line;
    }

    public void setLines(Integer line) {
        this.line = line;
    }

    public Date getTeminateTime() {
        return teminateTime;
    }

    public void setTeminateTime(Date teminateTime) {
        this.teminateTime = teminateTime;
    }

    public boolean isTeminateLabel() {
        return teminateLabel;
    }

    public void setTeminateLabel(boolean teminateLabel) {
        this.teminateLabel = teminateLabel;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Set<FinanceInvoiceDetailHistory> getFinanceInvoiceDetailHistoryHashSet() {
        return financeInvoiceDetailHistoryHashSet;
    }

    public void setFinanceInvoiceDetailHistoryHashSet(Set<FinanceInvoiceDetailHistory> financeInvoiceDetailHistoryHashSet) {
        this.financeInvoiceDetailHistoryHashSet = financeInvoiceDetailHistoryHashSet;
    }

    public Integer getPeroid() {
        return peroid;
    }

    public void setPeroid(Integer peroid) {
        this.peroid = peroid;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }


}

