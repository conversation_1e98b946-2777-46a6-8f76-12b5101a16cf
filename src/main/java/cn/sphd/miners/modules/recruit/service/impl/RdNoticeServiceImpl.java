package cn.sphd.miners.modules.recruit.service.impl;


import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.recruit.dao.*;
import cn.sphd.miners.modules.recruit.entity.*;
import cn.sphd.miners.modules.recruit.service.RdNoticeService;
import cn.sphd.miners.modules.schedule.service.impl.MemoUsing;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("rdNoticeService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class RdNoticeServiceImpl implements RdNoticeService {

    @Autowired
    RdNoticeDao rdNoticeDao;
    @Autowired
    RdNoticeItemDao rdNoticeItemDao;
    @Autowired
    RdNoticeAdditionalDao rdNoticeAdditionalDao;
    @Autowired
    RdNoticePostDao rdNoticePostDao;
    @Autowired
    RdNoticeHistoryDao rdNoticeHistoryDao;
    @Autowired
    RdNoticeItemTmplDao rdNoticeItemTmplDao;
    @Autowired
    RdPostDao rdPostDao;
    @Autowired
    UploadService uploadService;

    @Override
    public List<RdNotice> getRdNoticeListByOid(Integer oid, Boolean enabled, Date beginDate, Date endDate) {
        String hql="from RdNotice where org=:oid and enabled=:enabled";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("enabled",enabled);
        if (beginDate!=null){
            hql+=" and ((:beginDate<endTime and endTime<:endDate) or(:beginDate<enabledTime and enabledTime<:endDate))";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        List<RdNotice> rdNoticeList=rdNoticeDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticeList;
    }

    @Override
    public RdNotice getRdNoticeById(Long id) {
        return rdNoticeDao.get(id);
    }

    @Override
    public List<RdNoticeItem> getRdNoticeItemsByNoticeId(Long noticeId) {
        String hql="from RdNoticeItem where noticeId=:noticeId";
        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        List<RdNoticeItem> rdNoticeItemList=rdNoticeItemDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticeItemList;
    }

    @Override
    public List<RdNoticeItem> getRdNoticeItemsByNoticeId(Long noticeId, Integer type) {
        String hql="from RdNoticeItem where noticeId=:noticeId  and type=:type";
        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        map.put("type",type);
        List<RdNoticeItem> rdNoticeItemList=rdNoticeItemDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticeItemList;
    }

    @Override
    public List<RdNoticeItem> getRdNoticeItemsByNoticePostId(Long noticePostId) {
        String hql="from RdNoticeItem where noticePostId=:noticePostId";
        Map<String,Object> map=new HashMap<>();
        map.put("noticePostId",noticePostId);
        List<RdNoticeItem> rdNoticeItemList=rdNoticeItemDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticeItemList;
    }

    @Override
    public List<RdNoticeAdditional> getRdNoticeAdditionalsByNoticeId(Long noticeId) {
        String hql="from RdNoticeAdditional where noticeId=:noticeId";
        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        List<RdNoticeAdditional> rdNoticeAdditionalList=rdNoticeAdditionalDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticeAdditionalList;
    }

    @Override
    public List<RdNoticePost> getRdNoticePostsByNoticeId(Long noticeId) {
        String hql="from RdNoticePost where noticeId=:noticeId";
        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        List<RdNoticePost> rdNoticePostList=rdNoticePostDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticePostList;
    }

    @Override
    public RdNoticePost getRdNoticePostById(Long id) {
        return rdNoticePostDao.get(id);
    }

    @Override
    public List<RdNoticeHistory> getRdNoticeHistoriesByNoticeId(Long noticeId,Integer[] operations) {
        String hql="from RdNoticeHistory where noticeId=:noticeId and operation in(:operations)";
        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        map.put("operations",operations);
        List<RdNoticeHistory> rdNoticeHistories=rdNoticeHistoryDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticeHistories;
    }

    @Override
    public void updateRdNotice(RdNotice rdNotice, User loginUser,Integer operation) {
        rdNotice.setOperation(operation);
        updateRdNotice(rdNotice,loginUser);
        saveRdNoticeHistory(rdNotice,loginUser);
    }

    public void saveRdNoticeHistory(RdNotice rdNotice,User user){
        RdNoticeHistory rdNoticeHistory=new RdNoticeHistory();
        BeanUtils.copyProperties(rdNotice,rdNoticeHistory);
        rdNoticeHistory.setRdNotice(rdNotice);
        rdNoticeHistory.setCreateDate(new Date());
        rdNoticeHistoryDao.save(rdNoticeHistory);
        if (StringUtils.isNotEmpty(rdNoticeHistory.getImagePath())){
            RdNoticeUsing callback = new RdNoticeUsing(rdNoticeHistory.getId(), rdNoticeHistory.getClass());
            String name=rdNoticeHistory.getName();
            uploadService.addFileUsing(callback, rdNoticeHistory.getImagePath(), name, user, "招聘管理");
        }
    }

    @Override
    public void updateRdNotice(RdNotice rdNotice, User loginUser) {
        rdNotice.setUpdateDate(new Date());
        rdNotice.setUpdator(loginUser.getUserID());
        rdNotice.setUpdateName(loginUser.getUserName());
        rdNoticeDao.update(rdNotice);
    }

    @Override
    public List<RdNoticeItemTmpl> getRdNoticeItemTmplList(Integer type,Integer oid) {
        String hql="from RdNoticeItemTmpl where type=:type and (org<0 or org=:oid)";
        Map<String,Object> map=new HashMap<>();
        map.put("type",type);
        map.put("oid",oid);
        List<RdNoticeItemTmpl> rdNoticeItemTmpls=rdNoticeItemTmplDao.getListByHQLWithNamedParams(hql,map);
        return rdNoticeItemTmpls;
    }

    @Override
    public void addRdNotice(User user, String json) {
        JSONObject jsonObject= JSONObject.fromObject(json);

        RdNotice rdNotice=new RdNotice(); //招聘启示主表
        rdNotice.setOrg(user.getOid());
        rdNotice.setBeginTime(new Date());
        rdNotice.setCreator(user.getUserID());
        rdNotice.setCreateDate(new Date());
        rdNotice.setCreateName(user.getUserName());
        rdNotice.setOperation(1);
        rdNoticeDao.save(rdNotice);

        JSONArray noticeItemJsonArray = JSONArray.fromObject(jsonObject.get("noticeItemList")); /// 招聘启事自定义项列表
        List  noticeItemList= JSONArray.toList(noticeItemJsonArray);
        for (int i=0;i<noticeItemList.size();i++){
            JSONObject noticeItemJson=JSONObject.fromObject(noticeItemList.get(i));
            RdNoticeItem rdNoticeItem=new RdNoticeItem();
            rdNoticeItem.setCreator(user.getUserID());
            rdNoticeItem.setCreateDate(new Date());
            rdNoticeItem.setCreateName(user.getUserName());
            rdNoticeItem.setUpdator(user.getUserID());
            rdNoticeItem.setUpdateName(user.getUserName());
            rdNoticeItem.setUpdateDate(rdNoticeItem.getCreateDate());
            rdNoticeItem.setType(1);
            rdNoticeItem.setItemCode(noticeItemJson.getString("itemCode"));
            rdNoticeItem.setItemName(noticeItemJson.getString("itemName"));
            rdNoticeItem.setContent(noticeItemJson.getString("content"));
            rdNoticeItem.setShow(Boolean.valueOf(noticeItemJson.getString("show")));
            rdNoticeItem.setRdNotice(rdNotice);
            rdNoticeItem.setOrg(user.getOid());
            String itemCode=rdNoticeItem.getItemCode();  //项目code
            String content=rdNoticeItem.getContent();
            if (!"尚无内容".equals(content)&&StringUtils.isNotEmpty(content)){
                switch (itemCode) {
                    case "address": //工作地点
                        JSONObject addressJson = JSONObject.fromObject(content);
                        rdNoticeItem.setUnitive(addressJson.getBoolean("unitive"));
                        break;
                    case "workRest"://作息时间
                        JSONObject workRestJson = JSONObject.fromObject(content);
                        rdNoticeItem.setUnitive(workRestJson.getBoolean("unitive"));
                        break;
                    case "image":  //图片路径
                        rdNotice.setImagePath(content);
                        break;
                    case "deadline":    //截至日期
                        rdNotice.setEndTime(NewDateUtils.dateFromString(content, "yyyy-MM-dd"));
                        rdNoticeItem.setRequired(true);

//                    JSONObject dateJson=JSONObject.fromObject(content);
//                    rdNotice.setBeginTime(NewDateUtils.dateFromString(dateJson.getString("beginTime"), "yyyy-MM-dd"));
//                    rdNotice.setEndTime(NewDateUtils.dateFromString(dateJson.getString("endTime"), "yyyy-MM-dd"));
                        break;

                    case "resumeChanges":  //间隔时长
                        rdNotice.setIntervals(Integer.valueOf(content));
                        break;
                    case "custom": //自定义  用户点击自定义增加的项
                        rdNoticeItem.setSystem(false);
                        if (noticeItemJson.getBoolean("unitive")) { // 1是永久生效，0是单次生效
                            if (!noticeItemJson.has("id")) {  //存在id 就是之前自定义 永久生效的再次使用，不需要重复新增
                                RdNoticeItemTmpl rdNoticeItemTmpl = new RdNoticeItemTmpl();
                                BeanUtils.copyProperties(rdNoticeItem, rdNoticeItemTmpl);
                                rdNoticeItemTmpl.setOrg(user.getOid());
                                rdNoticeItemTmplDao.save(rdNoticeItemTmpl);
                            }
                        }else {
                            rdNoticeItem.setUnitive(false);
                        }
                        break;
                }
            }
            rdNoticeDao.update(rdNotice);
            rdNoticeItemDao.save(rdNoticeItem);
        }

        if (StringUtils.isNotEmpty(rdNotice.getImagePath())){
            RdNoticeUsing callback = new RdNoticeUsing(rdNotice.getId(), rdNotice.getClass());
            String name=rdNotice.getName();
            uploadService.addFileUsing(callback, rdNotice.getImagePath(), name, user, "招聘管理");
        }

        saveRdNoticeHistory(rdNotice,user);

        JSONArray noticePostJsonArray = JSONArray.fromObject(jsonObject.get("noticePostList")); /// 招聘岗位列表
        List  noticePostList= JSONArray.toList(noticePostJsonArray);
        for (int i=0;i<noticePostList.size();i++){
            JSONArray noticePostArray = JSONArray.fromObject(noticePostList.get(i)); /// 招聘岗位列表
            List  noticeList= JSONArray.toList(noticePostArray);
            RdNoticePost rdNoticePost = new RdNoticePost();
            rdNoticePost.setCreateDate(new Date());
            rdNoticePost.setCreator(user.getUserID());
            rdNoticePost.setCreateName(user.getUserName());
            rdNoticePost.setRdNotice(rdNotice); //招聘启示外键
            rdNoticePost.setOrg(user.getOid());
            rdNoticePostDao.save(rdNoticePost);

            for(int j=0;j<noticeList.size();j++) {
                JSONObject noticePostJson = JSONObject.fromObject(noticeList.get(j));

                RdNoticeItem rdNoticeItem = new RdNoticeItem();
                rdNoticeItem.setCreator(user.getUserID());
                rdNoticeItem.setCreateDate(new Date());
                rdNoticeItem.setCreateName(user.getUserName());
                rdNoticeItem.setUpdator(user.getUserID());
                rdNoticeItem.setUpdateName(user.getUserName());
                rdNoticeItem.setUpdateDate(rdNoticeItem.getCreateDate());
                rdNoticeItem.setType(2);
                rdNoticeItem.setItemCode(noticePostJson.getString("itemCode"));
                rdNoticeItem.setItemName(noticePostJson.getString("itemName"));
                rdNoticeItem.setContent(noticePostJson.getString("content"));
                rdNoticeItem.setShow(Boolean.valueOf(noticePostJson.getString("show")));
                rdNoticeItem.setRdNotice(rdNotice);
                rdNoticeItem.setOrg(user.getOid());
                String itemCode = rdNoticeItem.getItemCode();  //项目code
                String content = rdNoticeItem.getContent();
                if (!"尚无内容".equals(content)&&StringUtils.isNotEmpty(content)){
                    switch (itemCode) {
                        case "address": //工作地点
                            JSONObject addressJson = JSONObject.fromObject(content);
                            rdNoticeItem.setUnitive(addressJson.getBoolean("unitive"));
                            break;
                        case "workRest"://作息时间
                            JSONObject workRestJson = JSONObject.fromObject(content);
                            rdNoticeItem.setUnitive(workRestJson.getBoolean("unitive"));
                            break;
                        case "recruitingNumbers":
                            rdNoticePost.setPlanCount(Integer.valueOf(content)); //招聘人数
                            rdNoticeItem.setRequired(true);
                            break;
                        case "postName":
                            JSONObject postJson = JSONObject.fromObject(content);
                            rdNoticePost.setPostName(postJson.getString("postName")); //岗位名称
                            Long postId = postJson.getLong("postId");
                            RdPost post = rdPostDao.get(postId);
                            rdNoticePost.setRdPost(post);  //岗位外键
                            rdNoticeItem.setRequired(true);
                            break;
                        case "otherAsk":    //其他条件与要求
                            JSONObject otherAskJson = JSONObject.fromObject(content);
                            RdNoticeAdditional rdNoticeAdditional = new RdNoticeAdditional();
                            rdNoticeAdditional.setOrg(user.getOid());
                            if (StringUtils.isNotEmpty(otherAskJson.getString("age")))
                                rdNoticeAdditional.setAge(otherAskJson.getInt("age"));
                            if (StringUtils.isNotEmpty(otherAskJson.getString("ageThreshold")))
                                rdNoticeAdditional.setAgeThreshold(otherAskJson.getInt("ageThreshold"));
                            if (StringUtils.isNotEmpty(otherAskJson.getString("gender")))
                                rdNoticeAdditional.setGender(otherAskJson.getInt("gender"));
                            if (StringUtils.isNotEmpty(otherAskJson.getString("education")))
                                rdNoticeAdditional.setEducation(otherAskJson.getInt("education"));
                            if (StringUtils.isNotEmpty(otherAskJson.getString("educationThreshold")))
                                rdNoticeAdditional.setEducationThreshold(otherAskJson.getInt("educationThreshold"));
                            if (StringUtils.isNotEmpty(otherAskJson.getString("residence")))
                                rdNoticeAdditional.setResidence(otherAskJson.getInt("residence"));
                            rdNoticeAdditional.setExperiences(otherAskJson.getString("experiences"));
                            rdNoticeAdditional.setCharacters(otherAskJson.getString("characters"));
                            rdNoticeAdditional.setCreateDate(rdNoticeItem.getCreateDate());
                            rdNoticeAdditional.setCreator(rdNoticeItem.getCreator());
                            rdNoticeAdditional.setCreateName(rdNoticeItem.getCreateName());
                            rdNoticeAdditionalDao.save(rdNoticeAdditional);
                            break;
                        case "custom": //自定义  用户点击自定义增加的项
                            rdNoticeItem.setSystem(false);
                            if (noticePostJson.getBoolean("unitive")) {  // 1是永久生效，0是单次生效
                                if (!noticePostJson.has("id")) {  //存在id 就是之前自定义 永久生效的再次使用，不需要重复新增
                                    RdNoticeItemTmpl rdNoticeItemTmpl = new RdNoticeItemTmpl();
                                    BeanUtils.copyProperties(rdNoticeItem, rdNoticeItemTmpl);
                                    rdNoticeItemTmplDao.save(rdNoticeItemTmpl);
                                }
                            }else {
                                rdNoticeItem.setUnitive(false);
                            }
                            break;
                    }
                }
                rdNoticeItem.setRdNoticePost(rdNoticePost);
                rdNoticeItemDao.save(rdNoticeItem);
            }
            rdNoticePostDao.update(rdNoticePost);
        }
    }

    @Override
    public void saveRdNoticeItemTmpl(RdNoticeItemTmpl rdNoticeItemTmpl) {
        rdNoticeItemTmplDao.save(rdNoticeItemTmpl);
    }

    @Override
    public RdNoticeHistory getRdNoticeHistoryById(Long id) {
        return rdNoticeHistoryDao.get(id);
    }
}
