<template>
	<div class="homeMy">
		<div class="my-flex-panel my-flex-item">
			<div class="img33">
				<img class="image" :src="userImg" mode="头像" @error="this.src=userImgErr"/>
			</div>
			<div class="user">
				<div class="officeName">{{ org.name || 'XXX的工作室' }}</div>
				<div class="userInfo">
					<div class="txt">抖音名：{{ user.userName || '我会长大的' }}</div>
					<div @click="goPage('accountDetails')">
						<span class="txt">手机号：{{ user.mobile || '***********' }}</span>
            <el-icon class="live-right relaRight">
              <arrow-right-bold color="#53B5A8" class="houseSty8" />
            </el-icon>
					</div>
				</div>
			</div>
		</div>
    <div>
      <div class="live-panel live-tr">
        <div @click="goPage('mine')">
          <span class="icon icon-privateArea"></span>私人领地
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-panel live-tr">
        <div>
          <span class="icon icon-appFee"></span>软件使用费
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-panel live-tr">
        <div @click="goPage('about')">
          <span class="icon icon-about"></span>关于
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-panel live-pad">
        <div class="live-tr live-tr-line">
          <span class="icon icon-changeOffice"></span>切换工作室
          <span class="live-right" @click="goPage('officeListMy')">
            {{ officeList.length }}个工作室 <el-icon> <arrow-right-bold color="#53B5A8" class="houseSty9" /> </el-icon>
          </span>
        </div>
        <div v-if="user.roleCode == 'super'" class="live-tr" @click="cancellation">
          <span class="icon icon-cancellation"></span>注销工作室
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
        <div v-else class="live-tr" @click="outTeam">
          <span class="icon icon-loginOffTeam"></span>退出团队
          <el-icon class="live-right">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
    </div>
	</div>
</template>
<script setup>
import { activeMemberOutTeam, getRootPathFile } from "@/api/api.js";
import userImgErr from "@/assets/img/tabbar/1.png";
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'
import auth from "../../sys/auth";

const router = useRouter();
const officeItem = auth.getOrg()
console.log('officeItem=', officeItem)
const userInfoSys = auth.getUser()

let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
console.log(' let jsonData = ', jsonData)
const org = auth.getOrg()
const user = auth.getUser()
const userInfo = auth.getUser()
console.log('user=', user)

const officeList = jsonData.officeList
let userImg = ref('')

userImg.value = jsonData.userInfo.avatarUrl || userImgErr // 这个有值先用这个-张
console.log('jsonData', jsonData)


getRootPathFile().then((res) =>{
  console.log('getRootPathFile =' , res)
  let user2= auth.getAcc()
  let user1= auth.getUser()
  let path = (user1 && user1.imgPath) ? (user1 && user1.imgPath) : user2.avatar
  userImg.value = (path && (res.fileUrl + path)) || ''
  console.log(userImg.value)
})



const goPage = (url) => {
  console.log('my to url=', url)
  router.push({name: url});
}

const cancellation = (url) => {
  ElMessage('计算中，请稍候……');
  setTimeout(function () {
    goPage('cancellation')
  },3000)
}

const outTeam = () => {
  ElMessageBox.confirm('<p>退出团队后您将无法登录该工作室，除非再获邀请。</p> 确定退出团队吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    dangerouslyUseHTMLString: true,
    type: 'warning',
  }).then((action) => {
    activeMemberOutTeam().then(res => {
      console.log('推出团队返回值：',res )
      if(res){
        ElMessage.success('操作成功');
        localStorage.setItem('isChangeToken','0')
        router.push({name: 'mine'});
      }else{
        ElMessage.success('操作失败');
      }
    }).catch(err => {
      console.log('推出团队返回值 err：',err )
    })
  }).catch((e) => {
    console.log(e);
  });
}
</script>

<style lang="scss" scoped>
  $imgWid: 80px;
  $imgWid2: 40px;
  .homeMy{
    .my-flex-panel{
      display: flex; padding:20px; background-color: #fff; margin-bottom: 20px;
      .img33{
        flex: 1; display: inline-block; overflow: hidden; background-color: #fff;
        img{ background-color: #eee; border-radius: $imgWid2; width:$imgWid; height:$imgWid; position: relative; top: 20px; }
      }
      .user{
        flex: 3;
        padding-left: 15px;
        .officeName{ font-size: 1.1em; line-height: 50px; color: $livePageHeadBg; font-weight:bold;  }
        .userInfo{ line-height:30px; color: #555; font-size: 0.9em; }
      }
    }
    .houseSty8{ position: relative; top:5px; }
    .houseSty9{ position: relative; top:3px; }
    .icon-privateArea{ background-image: url("../../../../liveMiniApp/src/assets/img/privateArea.png"); }
    .icon-appFee{ background-image: url("../../../../liveMiniApp/src/assets/img/appFee.png"); }
    .icon-about{ background-image: url("../../../../liveMiniApp/src/assets/img/about.png"); }
    .icon-changeOffice{ background-image: url("../../../../liveMiniApp/src/assets/img/changeOffice.png"); }
    .icon-loginOffTeam{ background-image: url("../../../../liveMiniApp/src/assets/img/loginOffTeam.png"); background-size: 15px }
    .icon-cancellation{ background-image: url("../../../../liveMiniApp/src/assets/img/cancellation.png"); }
    .icon{
      width: 20px;
      height:20px;
      background-color: #fff;
      background-size: 100%;
      display: inline-block;
      position: relative;
      top:6px;
      margin-right:14px;
      background-repeat: no-repeat;
    }
    .mar30 {
      margin-top: 30px;
    }
  }

</style>
