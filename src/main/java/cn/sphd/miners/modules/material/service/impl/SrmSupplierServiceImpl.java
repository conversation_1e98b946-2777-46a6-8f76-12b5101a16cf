package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.PoContractBaseService;
import cn.sphd.miners.modules.material.service.PoContractNewService;
import cn.sphd.miners.modules.material.service.SrmSupplierService;
import cn.sphd.miners.modules.sales.dao.PoDeliveryAddressDao;
import cn.sphd.miners.modules.sales.model.PoDeliveryAddress;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Created by Administrator on 2016/9/20.
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class SrmSupplierServiceImpl implements SrmSupplierService {

    @Autowired
    private SrmSupplierDao srmSupplierDao;
    @Autowired
    private SrmSupplierContactDao srmSupplierContactDao;
    @Autowired
    private MtSupplierMaterialDao mtSupplierMaterialDao;
    @Autowired
    private SrmSupplierHistoryDao srmSupplierHistoryDao;
    @Autowired
    private PoContractBaseService poContractBaseService;
    @Autowired
    private PoContractBaseDao poContractBaseDao;
    @Autowired
    private SrmSupplierContactHistoryDao srmSupplierContactHistoryDao;
    @Autowired
    private UploadService uploadService;
    @Autowired
    private SrmSupplierImageDao srmSupplierImageDao;
    @Autowired
    private SrmSupplierImageHistoryDao srmSupplierImageHistoryDao;
    @Autowired
    private SrmSupplierPostDao srmSupplierPostDao;
    @Autowired
    private SrmSupplierPostHistoryDao srmSupplierPostHistoryDao;

    @Autowired
    private SrmSupplierInvoiceDao srmSupplierInvoiceDao;
    @Autowired
    private SrmSupplierInvoiceHistoryDao srmSupplierInvoiceHistoryDao;
    @Autowired
    private SrmSupplierContactSocialDao srmSupplierContactSocialDao;
    @Autowired
    private SrmSupplierContactSocialHistoryDao srmSupplierContactSocialHistoryDao;

    @Autowired
    PoDeliveryAddressDao deliveryAddressDao;

    @Autowired
    PoContractNewService poContractNewService;

    @Override
    public List<SrmSupplier> getAll() {
        List<SrmSupplier> srmSuppliers = srmSupplierDao.getAll();
        return srmSuppliers;
    }

    @Override
    public List<SrmSupplier> getByOid(Integer oid, int i, PageInfo pageInfo, Object o, Integer type) {
        String condition = "and o.org = "+oid;
        if (type!=null) condition+=" and o.type="+type;
        List<SrmSupplier> srmSuppliers = srmSupplierDao.findCollectionByConditionNoPage(condition, null, null);
        return srmSuppliers;
    }

    @Override
    public List<SrmSupplier> searchByOid(Integer oid, Integer enabled, PageInfo pageInfo, String keyword, Integer type) {
        String hql = "from SrmSupplier where org =:oid and enabled=:enabled";
        if (StringUtils.isNotEmpty(keyword)) {
            hql = hql + " and ( fullName like '%" + keyword + "%' or codeName like '%" + keyword + "%')   ";
        }
        if(type!=null)
            hql+=" and type = "+type;
        hql=hql+" order by createDate desc";
        HashMap<String, Object> paramBase = new HashMap<>();
        paramBase.put("oid", oid);
        paramBase.put("enabled", enabled);
        return srmSupplierDao.getListByHQLWithNamedParams(hql, paramBase, pageInfo);
    }

    @Override
    public String addSupplier(SrmSupplier srmSupplier, Map<String, Object> param, User user) {
        //邮寄地址
        String yjAddressList = (String) param.get("yjAddressList");
        //联系人
        String contactsList = (String) param.get("contactsList");
        //全景图片集合
        String qImages = (String) param.get("qImages");
        //合同集合
        String contractBaseList = (String) param.get("contractBaseList");

        Date date = new Date();
        if (StringUtils.isEmpty(srmSupplier.getFullName())) {
            return "请填写供应商名称";
        }
        if (StringUtils.isEmpty(srmSupplier.getName())) {
            return "请填写供应商简称";
        }
        if (StringUtils.isEmpty(srmSupplier.getCodeName())) {
            return "请填写供应商代号";
        }
        Map<String, Object> p = new HashMap<>();
        p.put("org", user.getOid());
        p.put("fullName", srmSupplier.getFullName());
        SrmSupplier ss = (SrmSupplier) srmSupplierDao.getByHQLWithNamedParams("from SrmSupplier where org=:org and fullName=:fullName", p);
        if (ss != null) {
            return "该供应商名称已存在";
        }
        Map<String, Object> p2 = new HashMap<>();
        p2.put("org", user.getOid());
        p2.put("code", srmSupplier.getCodeName());
        SrmSupplier ss2 = (SrmSupplier) srmSupplierDao.getByHQLWithNamedParams("from SrmSupplier where org=:org and codeName=:code", p2);
        if (ss2 != null) {
            return "该供应商代号已存在";
        }
        srmSupplier.setOrg(user.getOid());
        srmSupplier.setCreateDate(new Date());
        srmSupplier.setCreator(user.getUserID());
        srmSupplier.setCreateName(user.getUserName());
        srmSupplier.setEnabled(1);
        srmSupplier.setVersionNo(1);
        srmSupplier.setSupplyCount(0);
        srmSupplier.setExclusiveCount(0);
        srmSupplier.setCutCount(0);
        srmSupplier.setOperation("1");
        srmSupplierDao.save(srmSupplier);
        Integer hisId = srmSupplierHistoryDao.saveHis(srmSupplier);

        Map<String, Integer> contactsMap = new HashMap<>();
        //添加开票历史

        SrmSupplierInvoice srmSupplierInvoice = new SrmSupplierInvoice();
        srmSupplierInvoice.setSupplier(srmSupplier.getId());
        srmSupplierInvoice.setOrg(srmSupplier.getOrg());
        srmSupplierInvoice.setCreateDate(new Date());
        srmSupplierInvoice.setCreator(user.getUserID());
        srmSupplierInvoice.setCreateName(user.getUserName());
        srmSupplierInvoice.setEnabled(1);
        srmSupplierInvoice.setOperation("1");

        if ("0".equals(srmSupplier.getInvoicable())) {
            srmSupplierInvoice.setInvoiceCategory("4");
        }
        if ("1".equals(srmSupplier.getInvoicable()) && "1".equals(srmSupplier.getVatsPayable())) {
            srmSupplierInvoice.setInvoiceCategory("1");
            srmSupplierInvoice.setTaxRate(srmSupplier.getTaxRate());
        }

        if ("1".equals(srmSupplier.getInvoicable()) && "0".equals(srmSupplier.getVatsPayable())) {
            srmSupplierInvoice.setInvoiceCategory("2");

        }
        srmSupplierInvoiceDao.saveOrUpdate(srmSupplierInvoice);
        srmSupplierInvoiceHistoryDao.saveHis(srmSupplierInvoice,hisId);


        if (qImages != null && !"".equals(qImages)) {//全景图片
            JSONArray qJSON = JSONArray.fromObject(qImages);

            if (qJSON.size() > 0) {
                for (int i = 0; i < qJSON.size(); i++) {
                    JSONObject a = qJSON.getJSONObject(i);
                    //正常图标
                    String normal = a.optString("normal");
                    SrmSupplierImage image = new SrmSupplierImage();
                    image.setType("1");
                    image.setNormal(normal);
                    image.setCreator(user.getUserID());
                    image.setCreateDate(date);
                    image.setCreateName(user.getUserName());
                    image.setSupplier(srmSupplier.getId());
                    image.setOrg(user.getOid());
                    image.setOperation("1");
                    image.setVersionNo(1);
                    srmSupplierImageDao.save(image);

                    //添加进历史
                    SrmSupplierImageHistory ih = srmSupplierImageHistoryDao.insert(image, hisId);

                    SrmSupplierUsing imageU = new SrmSupplierUsing(image.getId(), SrmSupplierImage.class);
                    uploadService.addFileUsing(imageU, image.getNormal(), null, user, "供应商");
                    SrmSupplierUsing iuh = new SrmSupplierUsing(ih.getId(), SrmSupplierImageHistory.class);
                    uploadService.addFileUsing(iuh, ih.getNormal(), null, user, "供应商");
                }
            }
        }

        if (contactsList != null && !"".equals(contactsList)) {//联系人信息
            JSONArray contacts = JSONArray.fromObject(contactsList);
            if (contacts.size() > 0) {
                for (int i = 0; i < contacts.size(); i++) {
                    JSONObject a = contacts.getJSONObject(i);
                    //姓名
                    String name = a.optString("name");
                    //职位
                    String post = a.optString("post");
                    //手机号
                    String mobile = a.optString("mobile");
                    String socialList =  a.optString("socialList");
                    //标签
                    String tags = a.optString("tags");
                    //名片
                    String cardPath = null;
                    if (a.has("cardPath")) {
                        cardPath = a.optString("cardPath");
                    }

                    SrmSupplierContact srmSupplierContact = new SrmSupplierContact();
                    srmSupplierContact.setName(name);
                    srmSupplierContact.setPost(post);
                    srmSupplierContact.setMobile(mobile);
                    srmSupplierContact.setCreator(user.getUserID());
                    srmSupplierContact.setCreateDate(date);
                    srmSupplierContact.setCreateName(user.getUserName());
                    srmSupplierContact.setSupplier(srmSupplier.getId());
                    srmSupplierContact.setOrg(user.getOid());
                    srmSupplierContact.setEnabled(1);
                    srmSupplierContact.setEnabledTime(date);
                    srmSupplierContact.setVersionNo(1);

                    if (StringUtils.isEmpty(tags) || "其他".equals(tags)) {
                        srmSupplierContact.setTags("其他");
                    } else {
                        srmSupplierContact.setTags(user.getUserName() + tags + "添加");
                    }
                    srmSupplierContact.setOperation("1");
                    srmSupplierContact.setCardPath(cardPath);
                    srmSupplierContactDao.save(srmSupplierContact);
                    srmSupplierContactDao.getSession().flush();
                    contactsMap.put(a.optString("id"), srmSupplierContact.getId());
                    //添加进历史表
                    int cchId = srmSupplierContactHistoryDao.insertAndResultId(srmSupplierContact);
                    if (cardPath != null && !"".equals(cardPath)) {
                        SrmSupplierUsing cccu = new SrmSupplierUsing(srmSupplierContact.getId(), SrmSupplierContact.class);
                        uploadService.addFileUsing(cccu, srmSupplierContact.getCardPath(), srmSupplierContact.getCardPath(), user, "供应商");
                        SrmSupplierUsing iuh = new SrmSupplierUsing(cchId, SrmSupplierContactHistory.class);
                        uploadService.addFileUsing(iuh, srmSupplierContact.getCardPath(), srmSupplierContact.getCardPath(), user, "供应商");
                    }

                    if (socialList != null && !"".equals(socialList)) {
                        JSONArray socialArray = JSONArray.fromObject(socialList);
                        for (int k = 0; k < socialArray.size(); k++) {
                            JSONObject social = socialArray.getJSONObject(k);
                            String code = social.optString("code");
                            String type = social.optString("type");
                            String socialName = social.optString("name");
                            SrmSupplierContactSocial contactSocial = new SrmSupplierContactSocial();
                            contactSocial.setCode(code);
                            contactSocial.setType(type);
                            contactSocial.setName(socialName);
                            contactSocial.setCreator(user.getUserID());
                            contactSocial.setCreateDate(new Date());
                            contactSocial.setCreateName(user.getUserName());
                            contactSocial.setSupplier(srmSupplier.getId());
                            contactSocial.setSupplierContact(srmSupplierContact.getId());
                            contactSocial.setOrg(user.getOid());
                            contactSocial.setEnabled(1);
                            contactSocial.setEnabledTime(new Date());
                            contactSocial.setOperation("1");
                            srmSupplierContactSocialDao.save(contactSocial);

                            //添加历史
                            srmSupplierContactSocialHistoryDao.insert(contactSocial, cchId);
                        }
                    }
                }
            }
        }

        if (yjAddressList != null && !"".equals(yjAddressList)) {//添加邮寄地址
            JSONArray address = JSONArray.fromObject(yjAddressList);

            if (address.size() > 0) {
                for (int i = 0; i < address.size(); i++) {
                    JSONObject a = address.getJSONObject(i);

                    SrmSupplierPost srmSupplierPost = new SrmSupplierPost();
                    srmSupplierPost.setOrg(user.getOid());
                    srmSupplierPost.setOperation("1");
                    srmSupplierPost.setSupplier(srmSupplier.getId());
                    srmSupplierPost.setAddress(a.optString("address"));
                    srmSupplierPost.setPostcode(a.optString("postcode"));
                    srmSupplierPost.setMobile(a.optString("mobile"));
                    srmSupplierPost.setType("1");
                    srmSupplierPost.setEnabled(1);
                    srmSupplierPost.setEnabledTime(date);
                    srmSupplierPost.setCreator(user.getUserID());
                    srmSupplierPost.setCreateName(user.getUserName());
                    srmSupplierPost.setCreateDate(date);
                    srmSupplierPost.setContact(a.optString("contact"));
                    if (a.optString("supplierContact") != null) {
                        srmSupplierPost.setSupplierContact(contactsMap.get(a.optString("supplierContact")));

                    }
                    srmSupplierPostDao.save(srmSupplierPost);
                    srmSupplierPostDao.getSession().flush();
                    //添加历史记录
                    srmSupplierPostHistoryDao.insert(srmSupplierPost);
                }
            }
        }

        //添加合同信息
        //poContractBaseService.addContractBaseList(contractBaseList, srmSupplier.getId(), user);
        poContractNewService.insertBatchPoContract(contractBaseList, srmSupplier.getId(), user);

        return "success";
    }

    @Override
    public SrmSupplier getById(Integer supplierId) {
        SrmSupplier srmSuppliers = srmSupplierDao.get(supplierId);
        return srmSuppliers;
    }

    @Override
    public List<SrmSupplierContact> getBySupplierId(Integer supplierId) {
        String condition = "and o.supplier = " + supplierId;
        List<SrmSupplierContact> srmSupplierContacts = srmSupplierContactDao.findCollectionByConditionNoPage(condition, null, null);
        return srmSupplierContacts;
    }

    @Override
    public String recoveryOrStopSrmSupplier(Integer supplierId,Integer enabled, User user) {
        SrmSupplier supplier=srmSupplierDao.get(supplierId);
        supplier.setEnabled(enabled);
        supplier.setUpdateDate(new Date());
        supplier.setUpdator(user.getUserID());
        supplier.setUpdateName(user.getUserName());
        if(enabled==0){
            supplier.setOperation("B");
        }else{
            supplier.setOperation("C");
        }
        srmSupplierDao.update(supplier);
        srmSupplierHistoryDao.saveHis(supplier);

        return "success";
    }


    @Override
    public void updateSrmSupplier(SrmSupplier srmSupplier) {

        srmSupplierDao.update(srmSupplier);

    }

    @Override
    @Transactional
    public void updateSrmSupplierBase(SrmSupplier mtSupplier, User user) {
        //全景图片集合
        List<Map<String, Object>> qImages = mtSupplier.getqImages();

        List<SrmSupplierImage> ims = srmSupplierImageDao.getListByHQL("from SrmSupplierImage where supplier=" + mtSupplier.getId());


        Date date = new Date();

        for (SrmSupplierImage ci : ims) {
            SrmSupplierUsing imageU = new SrmSupplierUsing(ci.getId(), SrmSupplierImage.class);
            uploadService.delFileUsing(imageU, ci.getNormal(), user);
        }
        srmSupplierImageDao.deleteAll(ims);

        SrmSupplier supplier = srmSupplierDao.get(mtSupplier.getId());
        supplier.setUpdateDate(date);
        supplier.setUpdateName(user.getUserName());
        supplier.setUpdator(user.getUserID());
        supplier.setOperation("3");
        supplier.setName(mtSupplier.getName());
        supplier.setFullName(mtSupplier.getFullName());
        supplier.setCodeName(mtSupplier.getCodeName());
        supplier.setChargeAcceptable(mtSupplier.getChargeAcceptable());
        supplier.setChargeBegin(mtSupplier.getChargeBegin());
        supplier.setChargePeriod(mtSupplier.getChargePeriod());
        supplier.setIsImprest(mtSupplier.getIsImprest());
        supplier.setImprestProportion(mtSupplier.getImprestProportion());
        srmSupplierDao.update(supplier);
        Integer hisId = srmSupplierHistoryDao.saveHis(supplier);


        if (qImages != null && !"".equals(qImages)) {//全景图片
            JSONArray qJSON = JSONArray.fromObject(qImages);

            if (qJSON.size() > 0) {
                for (int i = 0; i < qJSON.size(); i++) {
                    JSONObject a = qJSON.getJSONObject(i);
                    //正常图标
                    String normal = a.optString("normal");
                    SrmSupplierImage image = new SrmSupplierImage();
                    image.setType("1");
                    image.setNormal(normal);
                    image.setCreator(user.getUserID());
                    image.setCreateDate(date);
                    image.setCreateName(user.getUserName());
                    image.setSupplier(mtSupplier.getId());
                    image.setOrg(user.getOid());
                    image.setOperation("1");
                    image.setVersionNo(1);
                    srmSupplierImageDao.save(image);

                    //添加进历史
                    SrmSupplierImageHistory ih = srmSupplierImageHistoryDao.insert(image, hisId);

                    SrmSupplierUsing imageU = new SrmSupplierUsing(image.getId(), SrmSupplierImage.class);
                    uploadService.addFileUsing(imageU, image.getNormal(), null, user, "供应商");
                    SrmSupplierUsing iuh = new SrmSupplierUsing(ih.getId(), SrmSupplierImageHistory.class);
                    uploadService.addFileUsing(iuh, ih.getNormal(), null, user, "供应商");
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> getRecordBaseList(Integer supplierId) {
        List<Map<String, Object>> list = srmSupplierDao.getListByHQLWithNamedParams("select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate) from SrmSupplierHistory where supplier=" + supplierId + " and (operation='1' or operation='3')", new HashMap<>());
        return list;
    }

    @Override
    public Map<String, Object> getRecordBaseDetails(Integer id, Integer frontId) {
        Map<String, Object> map = new HashMap<>();
        String custHql = " from SrmSupplierHistory c where c.id=:id";
        Map param = new HashMap();
        param.put("id", id);
        SrmSupplierHistory now = (SrmSupplierHistory) srmSupplierHistoryDao.getByHQLWithNamedParams(custHql, param);

        if (now != null) {
            List<Map<String, Object>> qImages = srmSupplierImageHistoryDao.getListByHQLWithNamedParams("select new map(normal as normal) from SrmSupplierImageHistory where supplier=" + id + " and type=1", new HashMap<>());

            now.setqImages(qImages);
        }


        SrmSupplierHistory front = null;
        if (frontId != null && frontId != 0) {
            param.put("id", frontId);
            front = (SrmSupplierHistory) srmSupplierHistoryDao.getByHQLWithNamedParams(custHql, param);

            if (front != null) {
                List<Map<String, Object>> qImages = srmSupplierImageHistoryDao.getListByHQLWithNamedParams("select new map(normal as normal) from SrmSupplierImageHistory where supplier=" + frontId + " and type=1", new HashMap<>());
                front.setqImages(qImages);
            }
        }

        map.put("now", now);
        map.put("front", front);
        return map;
    }

    @Override
    public List<Map<String, Object>> getRecordInvoiceList(Integer supplierId) {
        List<Map<String, Object>> list = srmSupplierDao.getListByHQLWithNamedParams("select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate) from SrmSupplierHistory where supplier=" + supplierId + " and (operation='1' or operation='4')", new HashMap<>());
        return list;
    }

    @Override
    public Map<String, Object> getRecordInvoiceDetails(Integer id, Integer frontId) {
        Map<String, Object> map = new HashMap<>();
        String custHql = " from SrmSupplierHistory c where c.id=:id";
        Map param = new HashMap();
        param.put("id", id);
        SrmSupplierHistory now = (SrmSupplierHistory) srmSupplierHistoryDao.getByHQLWithNamedParams(custHql, param);


        SrmSupplierInvoiceHistory nowInvoice= (SrmSupplierInvoiceHistory) srmSupplierInvoiceHistoryDao.getByHQLWithNamedParams("from SrmSupplierInvoiceHistory  where supplier="+now.getId(),new HashMap<>());

        if(nowInvoice!=null){
            now.setTaxRate(nowInvoice.getTaxRate());
        }
        if(now.getVatsCount()==null){
            now.setVatsCount(0);
        }

        if(now.getOtherInvoiceCount()==null){
            now.setOtherInvoiceCount(0);
        }

        if(now.getNotInvoiceCount()==null){
            now.setNotInvoiceCount(0);
        }



        SrmSupplierHistory front = null;
        if (frontId != null && frontId != 0) {
            param.put("id", frontId);
            front = (SrmSupplierHistory) srmSupplierHistoryDao.getByHQLWithNamedParams(custHql, param);
            SrmSupplierInvoiceHistory frontInvoice= (SrmSupplierInvoiceHistory) srmSupplierInvoiceHistoryDao.getByHQLWithNamedParams("from SrmSupplierInvoiceHistory  where supplier="+front.getId(),new HashMap<>());

            if(frontInvoice!=null){
                front.setTaxRate(frontInvoice.getTaxRate());
            }
            if(front.getVatsCount()==null){
                front.setVatsCount(0);
            }

            if(front.getOtherInvoiceCount()==null){
                front.setOtherInvoiceCount(0);
            }

            if(front.getNotInvoiceCount()==null){
                front.setNotInvoiceCount(0);
            }

        }

        map.put("now", now);
        map.put("front", front);
        return map;
    }

    @Override
    public void deleteSrmSupplierContact(SrmSupplierContact srmSupplierContact) {
        srmSupplierContactDao.delete(srmSupplierContact);

    }

    @Override
    public SrmSupplierContact getSrmSupplierContactById(Integer id) {


        return srmSupplierContactDao.getSrmSupplierContactById(id);
    }


    @Override
    public void addSrmSupplierContact(SrmSupplierContact srmSupplierContact) {

        srmSupplierContactDao.save(srmSupplierContact);

    }

    @Override
    public void updateSrmSupplierContact(SrmSupplierContact srmSupplierContact) {
        srmSupplierContactDao.update(srmSupplierContact);
    }

    @Override
    public SrmSupplier getSrmSupplier(Integer id) {
        return srmSupplierDao.get(id);
    }

    @Override
    public SrmSupplier getSrmSupplierByName(Integer oid, String name) {
        String hql = "from SrmSupplier where oid=" + oid + " and name='" + name + "'";
        SrmSupplier srmSupplier = srmSupplierDao.getByHQL(hql);
        return srmSupplier;
    }

    @Override
    public void saveMtSupplierMaterial(MtSupplierMaterial supplierMaterial) {
        mtSupplierMaterialDao.saveOrUpdate(supplierMaterial);
    }

    @Override
    public void deleteSrmSupplier(Integer id) {
        srmSupplierDao.deleteById(id);
    }

    @Override
    public List<SrmSupplier> getSrmSupplierLikeName(Integer oid, String name) {
        String hql = " from SrmSupplier where oid=?0 and name like '%?1%'";
        return srmSupplierDao.getListByHQL(hql, oid, name);
    }



    @Override
    public SrmSupplier getSrmSupplierOne(Integer id) {

        SrmSupplier supplier = srmSupplierDao.get(id);
        SrmSupplierInvoice invoice = (SrmSupplierInvoice) srmSupplierInvoiceDao.getByHQLWithNamedParams("from SrmSupplierInvoice where supplier=" + id, new HashMap<>());

        if(invoice!=null){
            if(invoice.getTaxRate()!=null){
                supplier.setTaxRate(invoice.getTaxRate());
            }
        }

        //发票地址
        List<Map<String, Object>> yjAddressList = getAllAddress(id, "1");


        supplier.setYjAddressList(yjAddressList);


        List<Map<String, Object>> qjImages = srmSupplierImageDao.getListByHQLWithNamedParams("select new map(normal as normal) from SrmSupplierImage where supplier=" + id + " and type=1", new HashMap<>());

        supplier.setqImages(qjImages);


        List<Map<String, Object>> contactsList = srmSupplierContactDao.getListByHQLWithNamedParams("select new map(id as id,name as name,post as post,enabled as enabled,mobile as mobile,tags as tags) from SrmSupplierContact where supplier=" + id + " and enabled='1'  order by FIELD(enabled,0),createDate desc", new HashMap<>());
        supplier.setContactsList(contactsList);
        /*//获取合同列表
        String sql = "select cb.id as id,cb.sn as sn,cb.sign_time signTime,cb.valid_start as validStart,cb.valid_end validEnd,cb.enabled as enabled,cb.update_name updateName,(select count(0) from t_po_contract_material cc where cc.contract=cb.id) num from t_po_contract_base cb where cb.supplier=" + id + " and  cb.enabled=1 and cb.is_expired=0";

        List<Map<String, Object>> contractBaseList = poContractBaseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
        supplier.setContractBaseList(contractBaseList);*/


        return supplier;
    }

    @Override
    public Map<String, Object> getAddress(Integer addressId) {
        String hql="select new map(id as id,type as type,address as address,contact as contact,mobile as mobile,postcode as postcode,supplierContact as supplierContact) from SrmSupplierPost where id="+addressId;

        return (Map<String, Object>) srmSupplierPostDao.getDtoByHql(hql);
    }


    @Override
    public SrmSupplierInvoice getSrmSupplierInvoice(Integer supplierId) {
        SrmSupplierInvoice invoice = (SrmSupplierInvoice) srmSupplierInvoiceDao.getByHQLWithNamedParams("from SrmSupplierInvoice where supplier=" + supplierId, new HashMap<>());
        return invoice;
    }

    @Override
    public String updateSupplierInvoice(SrmSupplier supplier, User user) {


        SrmSupplier old = srmSupplierDao.get(supplier.getId());


        old.setInvoicable(supplier.getInvoicable());
        old.setVatsPayable(supplier.getVatsPayable());
        old.setDraftAcceptable(supplier.getDraftAcceptable());
        old.setUpdateDate(new Date());
        old.setUpdator(user.getUserID());
        old.setUpdateName(user.getUserName());
        old.setOperation("4");
        SrmSupplierInvoice srmSupplierInvoice = (SrmSupplierInvoice) srmSupplierInvoiceDao.getByHQLWithNamedParams("from SrmSupplierInvoice where supplier=" + supplier.getId(), new HashMap<>());
        if (srmSupplierInvoice == null) {
            srmSupplierInvoice = new SrmSupplierInvoice();
            srmSupplierInvoice.setSupplier(supplier.getId());
            srmSupplierInvoice.setOrg(old.getOrg());
            srmSupplierInvoice.setCreateDate(new Date());
            srmSupplierInvoice.setCreator(user.getUserID());
            srmSupplierInvoice.setCreateName(user.getUserName());
            srmSupplierInvoice.setEnabled(1);
            srmSupplierInvoice.setOperation("1");
        } else {
            srmSupplierInvoice.setUpdateDate(new Date());
            srmSupplierInvoice.setUpdator(user.getUserID());
            srmSupplierInvoice.setUpdateName(user.getUserName());
            srmSupplierInvoice.setOperation("3");
        }
        if ("0".equals(old.getInvoicable())) {
            srmSupplierInvoice.setInvoiceCategory("4");
        }
        if ("1".equals(old.getInvoicable()) && "1".equals(old.getVatsPayable())) {
            srmSupplierInvoice.setInvoiceCategory("1");
            srmSupplierInvoice.setTaxRate(supplier.getTaxRate());
        }

        if ("1".equals(old.getInvoicable()) && "0".equals(old.getVatsPayable())) {
            srmSupplierInvoice.setInvoiceCategory("2");

        }

        srmSupplierDao.update(old);
        Integer hisId=srmSupplierHistoryDao.saveHis(old);
        srmSupplierInvoiceDao.saveOrUpdate(srmSupplierInvoice);
        srmSupplierInvoiceHistoryDao.saveHis(srmSupplierInvoice,hisId);


        return "success";
    }

    @Override
    public List<Map<String, Object>> getContactsList(Integer supplierId, Integer enabled) {
        String sql="select id as id,name as name,mobile as mobile,post as post,enabled as enabled from t_srm_supplier_contact where supplier=" + supplierId + " and enabled = " + enabled + " order by ISNULL(tags),if( tags='其他',1,0),convert(tags using gbk)  asc";
        //   List<Map<String, Object>> contactsList = customerContactDao.getListByHQLWithNamedParams("select new map(id as id,name as name,mobile as mobile,post as post,enabled as enabled) from CustomerContact where customer=" + customerId + " and enabled = " + enabled + " order by ISNULL(tags),if( tags='其他',1,0),convert(tags,'gbk')  asc", new HashMap<>());

        List<Map<String, Object>> contactsList=srmSupplierContactDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
        return contactsList;

    }

    @Override
    @Transactional
    public String addCustomerAddress(User user, Integer supplierId, String address) {


        if (address != null && !"".equals(address)) {//添加发票地址
            JSONObject a = JSONObject.fromObject(address);
            SrmSupplierPost post = new SrmSupplierPost();
            post.setOperation("1");
            post.setOrg(user.getOid());
            post.setSupplier(supplierId);
            post.setCreateDate(new Date());
            post.setAddress(String.valueOf(a.get("address")));
            post.setContact(String.valueOf(a.get("contact")));
            post.setPostcode(String.valueOf(a.get("postcode")));
            post.setSupplierContact(a.optInt("supplierContact"));
            post.setMobile(String.valueOf(a.get("mobile")));
            post.setType("2");
            post.setEnabled(1);
            post.setEnabledTime(new Date());
            post.setCreator(user.getUserID());
            post.setCreateName(user.getUserName());
            srmSupplierPostDao.save(post);
            srmSupplierPostDao.getSession().flush();
            //添加历史记录
            srmSupplierPostHistoryDao.insert(post);
        }

        return "success";
    }

    @Override
    public String updateSupplierAddress(User user, SrmSupplierPost post) {

        SrmSupplierPost old = srmSupplierPostDao.get(post.getId());
        if (old == null) {
           return "地址信息传入错误";
        }
        if(post.getPostcode()!=null&&post.getPostcode().length()>6){
            return "邮寄编码填写错误";
        }
        old.setUpdateDate(new Date());
        old.setUpdator(user.getUserID());
        old.setUpdateName(user.getUserName());
        old.setAddress(post.getAddress());
        old.setContact(post.getContact());
        old.setMobile(post.getMobile());
        old.setPostcode(post.getPostcode());
        old.setSupplierContact(post.getSupplierContact());
        srmSupplierPostDao.update(old);
        //添加历史记录
        srmSupplierPostHistoryDao.insert(old);

        return "success";
    }

    public List<Map<String, Object>> getAllAddress(Integer supplierId, String enabled) {
        String sql = "SELECT p.id as id,c.name as name,c.mobile as mobile,p.address as address,p.enabled as enabled,p.postcode as postcode,supplier_contact as supplierContact  from t_srm_supplier_post p left join t_srm_supplier_contact c on c.id =p.supplier_contact  where p.supplier=" + supplierId;


        if (enabled != null && !"".equals(enabled)) {
            sql += "  and  p.enabled ='" + enabled + "'  ";
        }


        sql += "  order by FIELD(p.enabled,0),p.create_date desc";
        return srmSupplierPostDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }
    @Override
    public String deleteCustomerContact(Integer contactId, User user) {
        SrmSupplierContact srmSupplierContact = srmSupplierContactDao.get(contactId);

        if (srmSupplierContact == null) {
            return "联系人不存在";
        }
        srmSupplierContact.setOperation("2");
        srmSupplierContact.setEnabled(0);
        srmSupplierContact.setVersionNo(srmSupplierContact.getVersionNo() + 1);
        srmSupplierContact.setUpdateDate(new Date());
        srmSupplierContact.setUpdateName(user.getUserName());
        srmSupplierContact.setUpdator(user.getUserID());
        srmSupplierContactDao.update(srmSupplierContact);


        //添加进历史表
        int cchId = srmSupplierContactHistoryDao.insertAndResultId(srmSupplierContact);

        List<SrmSupplierContactSocial> ccsList = srmSupplierContactSocialDao.getListByHQLWithNamedParams("from SrmSupplierContactSocial where supplierContact=" + contactId,new HashMap<>());
        for (SrmSupplierContactSocial ccs : ccsList) {
            //添加进历史
            srmSupplierContactSocialHistoryDao.insert(ccs, cchId);
        }


        return "success";
    }

    @Override
    public Map<String, Object> getContactsSocial(Integer contactId) {
        Map<String, Object> data = (Map<String, Object>) srmSupplierContactSocialDao.getDtoByHql("select new map(id as id,createDate as createDate,createName as createName,name as name,post as post,enabled as enabled,mobile as mobile,tags as tags,cardPath as cardPath) from SrmSupplierContact where id=" + contactId);

        if (data != null) {

            List<Map<String, Object>> socialList = srmSupplierContactSocialDao.getListByHQLWithNamedParams("select new map(code as code,type as type,name as name) from SrmSupplierContactSocial where supplierContact=" + contactId, new HashMap<>());
            data.put("socialList", socialList);
        }


        return data;
    }

    @Override
    public String updateContactsSocialAndCard(User user, Integer contactId, HttpServletRequest request) {
        String socialList = request.getParameter("socialList");
        String name = request.getParameter("name");
        String post = request.getParameter("post");
        String cardPath = request.getParameter("cardPath");
        String mobile = request.getParameter("mobile");
        SrmSupplierContact srmSupplierContact = srmSupplierContactDao.get(contactId);

        String module="供应商";

        if (srmSupplierContact != null) {
            if (StringUtils.isNotEmpty(srmSupplierContact.getCardPath()) ) {

                //删除图片关联
                SrmSupplierUsing cccu=new SrmSupplierUsing(srmSupplierContact.getId(),SrmSupplierContact.class);
                uploadService.delFileUsing(cccu,srmSupplierContact.getCardPath(),user);
                srmSupplierContactDao.getSession().flush();

            }

            srmSupplierContact.setName(name);
            srmSupplierContact.setPost(post);
            srmSupplierContact.setMobile(mobile);
            srmSupplierContact.setOperation("5");
            srmSupplierContact.setVersionNo(srmSupplierContact.getVersionNo() + 1);
            srmSupplierContact.setUpdateDate(new Date());
            srmSupplierContact.setUpdateName(user.getUserName());
            srmSupplierContact.setUpdator(user.getUserID());
            srmSupplierContact.setCardPath(cardPath);
            srmSupplierContactDao.update(srmSupplierContact);
            //添加进历史表
            int cchId = srmSupplierContactHistoryDao.insertAndResultId(srmSupplierContact);

            if (cardPath != null && !"".equals(cardPath)) {
              //  srmSupplierContact.setCardPath(cardPath);
                SrmSupplierUsing cccu=new SrmSupplierUsing(srmSupplierContact.getId(),SrmSupplierContact.class);
                uploadService.addFileUsing(cccu,srmSupplierContact.getCardPath(),srmSupplierContact.getCardPath(),user,module);

                SrmSupplierUsing iuh = new SrmSupplierUsing(cchId, SrmSupplierContactHistory.class);
                uploadService.addFileUsing(iuh, srmSupplierContact.getCardPath(), srmSupplierContact.getCardPath(), user, module);
            }else{
                srmSupplierContact.setCardPath(null);
            }

            JSONArray socialArray = JSONArray.fromObject(socialList);

            List<SrmSupplierContactSocial> ccsList = srmSupplierContactSocialDao.getListByHQL("from SrmSupplierContactSocial where supplierContact=" + contactId);
            srmSupplierContactSocialDao.deleteAll(ccsList);
            for (int k = 0; k < socialArray.size(); k++) {
                JSONObject social = socialArray.getJSONObject(k);
                String code = social.optString("code");
                String type = social.optString("type");
                String socialName = social.optString("name");

                SrmSupplierContactSocial contactSocial = new SrmSupplierContactSocial();
                contactSocial.setCode(code);
                contactSocial.setType(type);
                contactSocial.setName(socialName);
                contactSocial.setCreator(user.getUserID());
                contactSocial.setCreateDate(new Date());
                contactSocial.setCreateName(user.getUserName());
                contactSocial.setSupplier(srmSupplierContact.getSupplier());
                contactSocial.setSupplierContact(srmSupplierContact.getId());
                contactSocial.setOrg(user.getOid());
                contactSocial.setEnabled(1);
                contactSocial.setEnabledTime(new Date());
                contactSocial.setOperation("1");
                srmSupplierContactSocialDao.save(contactSocial);
                //添加进历史
                srmSupplierContactSocialHistoryDao.insert(contactSocial, cchId);
            }
        }

        return "success";

    }

    @Override
    public Map addCustomerContact(User user, Integer supplierId, HttpServletRequest request) {

        Map m = new HashMap();

        //前端要求返回id 改为Map-zy

        SrmSupplier pd = this.getSrmSupplier(supplierId);
        if (pd == null) {
            m.put("res","供应商信息错误");
            m.put("code","error");
            return m;
        }

        List<SrmSupplierContact> list = srmSupplierContactDao.getListByHQL("from SrmSupplierContact where supplier=" + supplierId);
        if (list.size() >= 100) {
            m.put("res","超过100上限");
            m.put("code","error");
            return m;
        }
        String module="供应商";

        String socialList = request.getParameter("socialList");
        String name = request.getParameter("name");
        String post = request.getParameter("post");
        String cardPath = request.getParameter("cardPath");
        String mobile = request.getParameter("mobile");
        String tags = request.getParameter("tags");

        SrmSupplierContact srmSupplierContact = new SrmSupplierContact();
        if (StringUtils.isNotEmpty(tags)) {
            srmSupplierContact.setTags(user.getUserName() + tags + "添加");
        } else {
            srmSupplierContact.setTags("其他");
        }
        srmSupplierContact.setName(name);
        srmSupplierContact.setPost(post);
        srmSupplierContact.setMobile(mobile);
        srmSupplierContact.setCreator(user.getUserID());
        srmSupplierContact.setCreateDate(new Date());
        srmSupplierContact.setCreateName(user.getUserName());
        srmSupplierContact.setSupplier(supplierId);
        srmSupplierContact.setOrg(user.getOid());
        srmSupplierContact.setEnabled(1);
        srmSupplierContact.setEnabledTime(new Date());
        srmSupplierContact.setVersionNo(1);
        srmSupplierContact.setOperation("1");
        srmSupplierContact.setCardPath(cardPath);


        srmSupplierContactDao.save(srmSupplierContact);
        srmSupplierContactDao.getSession().flush();


        int cchId = srmSupplierContactHistoryDao.insertAndResultId(srmSupplierContact);

        if (cardPath != null && !"".equals(cardPath)) {

            SrmSupplierUsing cccu=new SrmSupplierUsing(srmSupplierContact.getId(),SrmSupplierContact.class);
            uploadService.addFileUsing(cccu,srmSupplierContact.getCardPath(),srmSupplierContact.getCardPath(),user,module);
            SrmSupplierUsing iuh=new SrmSupplierUsing(cchId,SrmSupplierContactHistory.class);
            uploadService.addFileUsing(iuh,srmSupplierContact.getCardPath(),srmSupplierContact.getCardPath(),user,module);
        }

        if (socialList != null && !"".equals(socialList)) {
            JSONArray socialArray = JSONArray.fromObject(socialList);
            for (int k = 0; k < socialArray.size(); k++) {
                JSONObject social = socialArray.getJSONObject(k);
                String code = social.optString("code");
                String type = social.optString("type");
                String socialName = social.optString("name");
                SrmSupplierContactSocial contactSocial = new SrmSupplierContactSocial();
                contactSocial.setCode(code);
                contactSocial.setType(type);
                contactSocial.setName(socialName);
                contactSocial.setCreator(user.getUserID());
                contactSocial.setCreateDate(new Date());
                contactSocial.setCreateName(user.getUserName());
                contactSocial.setSupplier(supplierId);
                contactSocial.setSupplierContact(srmSupplierContact.getId());
                contactSocial.setOrg(user.getOid());
                contactSocial.setEnabled(1);
                contactSocial.setEnabledTime(new Date());
                contactSocial.setOperation("1");
                srmSupplierContactSocialDao.save(contactSocial);

                //添加历史
                srmSupplierContactSocialHistoryDao.insert(contactSocial, cchId);
            }
        }
        m.put("res",srmSupplierContact.getId()+"");
        m.put("code","success");
        return m;
    }

    @Override
    public List<Map<String, Object>> getRecordContactList(Integer contactId) {
        String hql = "select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate) from SrmSupplierContactHistory where supplierContact=" + contactId;
        return srmSupplierContactHistoryDao.getListByHQLWithNamedParams(hql, new HashMap<>());
    }

    @Override
    public Map<String, Object> getRecordContactDetails(Integer id, Integer frontId) {
        Map<String, Object> map = new HashMap<>();

        SrmSupplierContactHistory now = (SrmSupplierContactHistory) srmSupplierContactHistoryDao.getByHQLWithNamedParams(" from SrmSupplierContactHistory where id=" + id,new HashMap<>());
        if (now != null) {
            List<Map<String, Object>> socialList = srmSupplierContactSocialHistoryDao.getListByHQLWithNamedParams("select new map(supplierContactHistory as supplierContactHistory,code as code,type as type,name as name) from SrmSupplierContactSocialHistory where supplierContactHistory=" + id, new HashMap<>());
            now.setSocialList(socialList);
        }

        SrmSupplierContactHistory  front = null;
        if (frontId != null && frontId != 0) {
            front = (SrmSupplierContactHistory) srmSupplierContactHistoryDao.getByHQLWithNamedParams(" from SrmSupplierContactHistory where id=" + frontId,new HashMap<>());;
            List<Map<String, Object>> socialList = srmSupplierContactSocialHistoryDao.getListByHQLWithNamedParams("select new map(supplierContactHistory as supplierContactHistory,code as code,type as type,name as name) from SrmSupplierContactSocialHistory where supplierContactHistory=" + frontId, new HashMap<>());
            front.setSocialList(socialList);
        }

        map.put("now", now);
        map.put("front", front);
        return map;
    }

    @Override
    public List<Map<String, Object>> getRecordAddressList(Integer addressId) {
        String hql = "select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate) from SrmSupplierPostHistory where supplierPost=" + addressId;
        return srmSupplierPostHistoryDao.getListByHQLWithNamedParams(hql, new HashMap<>());
    }

    @Override
    public Map<String, Object> getRecordShAddressDetails(Integer id, Integer frontId) {
        Map<String, Object> map = new HashMap<>();

        String hql = "select new map(id as id,contact as contact,mobile as mobile,address as address,enabled as enabled ,enabledTime as enabledTime,operation as operation,updateName as updateName,updateDate as updateDate,postcode as postcode) from SrmSupplierPostHistory  where id=:id";
        Map param = new HashMap();
        param.put("id", id);
        Map<String, Object> now = (Map<String, Object>) srmSupplierPostHistoryDao.getByHQLWithNamedParams(hql, param);

        Map<String, Object> front = null;
        if (frontId != null && frontId != 0) {
            param.put("id", frontId);
            front = (Map<String, Object>) srmSupplierPostHistoryDao.getByHQLWithNamedParams(hql, param);
        }

        map.put("now", now);
        map.put("front", front);
        return map;
    }

    @Override
    public String startOrStopAddress(Integer addressId, User user, int enabled) {
        SrmSupplierPost ca=srmSupplierPostDao.get(addressId);
        if(ca!=null){
            ca.setEnabled(enabled);
            ca.setEnabledTime(new Date());
            ca.setOperation("2");
            ca.setUpdateDate(new Date());
            ca.setUpdator(user.getUserID());
            ca.setUpdateName(user.getUserName());
            srmSupplierPostDao.update(ca);
            //添加历史记录
            srmSupplierPostHistoryDao.insert(ca);
            return "success";
        }
        return "地址不存在";
    }

    @Override
    public List<PoContractBase> getEndList() {

        String hql="from PoContractBase where isExpired=0 and TO_DAYS(validEnd) < TO_DAYS('"+ DateUtil.getDay()+"') ";
        return poContractBaseDao.getListByHQL(hql);
    }

    @Override
    public void batchUpdateSlContractBase(List<PoContractBase> list) {
        for (PoContractBase sb:list
        ) {
            poContractBaseDao.update(sb);
        }

    }

    @Override
    public List<Map<String, Object>> getSupplierByMt(Integer mtId) {
        String hql="select new Map(s.name as name ,s.fullName as fullName,s.codeName as codeName) from SrmSupplier as s where s.id in (SELECT m.supplier   from MtSupplierMaterial  as m where m.enabled=1 and m.material="+mtId+" )";
        return srmSupplierPostHistoryDao.getListByHQLWithNamedParams(hql, null);
    }

    @Override
    public List<Map<String, Object>> getStartAndStopList(Integer supplierId) {
        List<Map<String, Object>> list = srmSupplierDao.getListByHQLWithNamedParams("select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate,enabled as enabled) from SrmSupplierHistory where supplier=" + supplierId + " and (operation='B' or operation='C') order by updateDate asc", new HashMap<>());
        return list;
    }

    @Override
    public List<SrmSupplierInvoice> getSrmSuppliers(Integer id) {
        String hql=" from SrmSupplierInvoice o where o.supplier=?0 and ifnull(o.invoiceCategory,0)!=4";
        return srmSupplierInvoiceDao.getListByHQL(hql,id);
    }

    @Override
    public JsonResult addSupplierInvoice(SrmSupplierInvoice invoice, User user) {
        invoice.setCreateDate(new Date());
        invoice.setCreator(user.getUserID());
        invoice.setCreateName(user.getUserName());
        invoice.setOrg(user.getOid());
        invoice.setEnabled(1);
        invoice.setVersionNo(0);
        srmSupplierInvoiceDao.save(invoice);
        return new JsonResult(1,invoice);
    }

    @Override
    public JsonResult deleteSupplierInvoice(SrmSupplierInvoice invoice, User user) {
        List<MtSupplierMaterial> supplierMaterials = mtSupplierMaterialDao.getListByHQL(" from MtSupplierMaterial o where o.supplierInvoice=?0",invoice.getId());
        if(supplierMaterials==null||supplierMaterials.size()<=0){
            srmSupplierInvoiceDao.deleteById(invoice.getId());
        }else {
            return new JsonResult(0,"不能删除已被使用的税率");
        }

        return  new JsonResult(1,"删除成功");
    }

    @Override
    public JsonResult materialAddress(Integer id) {
        List<PoDeliveryAddress> addresses = new ArrayList<>();
        MtSupplierMaterial supplierMaterial = mtSupplierMaterialDao.getByHQL(" from MtSupplierMaterial o where o.id = ?0",id);

        if(supplierMaterial!=null){
            if(StringUtils.isNotBlank(supplierMaterial.getDeliveryAddress()))
                if(supplierMaterial.getDeliveryAddress().contains(",")){
                    for (String i : supplierMaterial.getDeliveryAddress().split(",")){
                        addresses.add(deliveryAddressDao.get(Long.valueOf(i)));
                    }
                }else if(StringUtils.isNumeric(supplierMaterial.getDeliveryAddress())){
                    addresses.add(deliveryAddressDao.get(Long.valueOf(supplierMaterial.getDeliveryAddress())));
                }

        }
        if (supplierMaterial != null) {
            supplierMaterial.setAddresses(addresses);
            supplierMaterial.setMaterial(null);

        }

        return new JsonResult(1,supplierMaterial);
    }

    @Override
    public List<MtSupplierMaterial> getSupplierMaterialsByState(Integer id,Integer enabled) {
        List<MtSupplierMaterial> supplierMaterials = mtSupplierMaterialDao.getListByHQL(" from MtSupplierMaterial o where o.supplier = ?0 and ifnull(o.enabled,0)=?1 and ifnull(o.material_,0)!=0",id,enabled);

        return supplierMaterials;
    }

    @Override
    public List<MtSupplierMaterial> getSupplierMaterialsByMaterial(Integer material) {

        List<MtSupplierMaterial> supplierMaterials = mtSupplierMaterialDao.getListByHQL(" from MtSupplierMaterial o where o.material_ = ?0 and ifnull(o.enabled,0)=?1",material,1);

        return supplierMaterials;
    }

    @Override
    public String removeAddress(User user, Integer addressId) {
        //先删除历史

        SrmSupplierPost srmSupplierPost = srmSupplierPostDao.get(addressId);
        if (srmSupplierPost!=null){
            List<SrmSupplierPostHistory> srmSupplierPostHistories = srmSupplierPostHistoryDao.getListByHQL(" from SrmSupplierPostHistory o where o.supplierPost=?0",addressId);
            srmSupplierPostHistoryDao.deleteAll(srmSupplierPostHistories);
        }

        srmSupplierPostDao.deleteById(addressId);
        return "success";
    }

    @Override
    public SrmSupplier getSrmSupplierByCode(String codeName, Integer oid) {
        return srmSupplierDao.getByHQL(" from SrmSupplier o where o.codeName=?0 and o.org=?1",codeName,oid);
    }

    @Override
    public void insertSupplier(SrmSupplier supplier) {
        srmSupplierDao.save(supplier);
    }

    @Override
    public List<SrmSupplier> getSupplierByEnabled(Integer org, int i) {
        return srmSupplierDao.getListByHQL(" from SrmSupplier o where o.org=?0 and o.enabled=?1 and type=2",org,i);
    }
}
