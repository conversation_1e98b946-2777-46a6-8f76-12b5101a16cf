package cn.sphd.miners.modules.purchaseInvoice.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.purchaseInvoice.dao.PoPaymentInvoiceAttachmentDao;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoiceAttachment;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * lyx on 2021/6/16
 */
@Repository
public class PoPaymentInvoiceAttachmentDaoImpl extends BaseDao<PoPaymentInvoiceAttachment, Serializable> implements PoPaymentInvoiceAttachmentDao {
}
