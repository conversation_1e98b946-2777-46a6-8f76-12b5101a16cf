package cn.sphd.miners.modules.dailyAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2022/01/13
 * 通用_发票认证表
 * 1.193我要报销优化2101 新增
 */
@Entity
@Table(name = "t_common_invoice_certification")
public class CommonInvoiceCertification implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="qr_info"  ,length = 100, nullable=true , unique=false)
    private String qrInfo;  //发票信息:扫二维码取的所有信息

    @Column(name="response_data"   , nullable=true , unique=false)
    private String responseData;//认证成功返回结果（认证不成功不插入记录）

    @Column(name="call_class"  , length=255 , nullable=true , unique=false)
    private String callClass;  //首次调用类

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getQrInfo() {
        return qrInfo;
    }

    public void setQrInfo(String qrInfo) {
        this.qrInfo = qrInfo;
    }

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    public String getCallClass() {
        return callClass;
    }

    public void setCallClass(String callClass) {
        this.callClass = callClass;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
