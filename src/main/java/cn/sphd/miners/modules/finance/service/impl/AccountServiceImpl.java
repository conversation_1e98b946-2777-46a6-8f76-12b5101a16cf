package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.finance.service.FinanceReturnService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

/**
 * Created by 赵应 on 2015/10/12.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class AccountServiceImpl extends BaseServiceImpl implements AccountService {
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    FinanceAccountHistoryDao financeAccountHistoryDao;
    @Autowired
    AccountDetailHistoryDao accountDetailHistoryDao;
    @Autowired
    FinanceReturnDao financeReturnDao;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    DataService dataService;
    @Autowired
    FinanceReturnService financeReturnService;
    @Autowired
    OrgService orgService;

//    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public void addAccount(FinanceAccount financeAccount) {
        financeAccountDao.save(financeAccount);
    }
    @Override
    public List<FinanceAccount> getOrgFinanceAccounts(Integer id,List<Integer> orgIntegerList) {
//        String condition =  " and o.org.id = "+id;
//        return financeAccountDao.findCollectionByConditionNoPage(condition,null,null);
        Map<String,Object> map = new HashMap<String, Object>();
        String condition = "from FinanceAccount where";
        if (id!=null){
            condition+=" org_=:org";
            map.put("org",id);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0){
            condition+=" org_ in (:orgIntegerList)";
            map.put("orgIntegerList",orgIntegerList);
        }
        List<FinanceAccount> financeAccounts = financeAccountDao.getListByHQLWithNamedParams(condition,map);
        return financeAccounts;
    }

    @Override
    public List<AccountPeriod> getAccountPeroidByOrgIDAndFid(Integer id, Integer fid) {
        String condition =  " and o.org.id = "+id+" and o.fid="+fid;
        Map<String,String> map = new HashMap<String, String>();
        map.put("id","desc");
        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,map);
    }

    @Override
    public List<AccountDetail> getPeroidDetailByOrgID(Date beginDate, Date endDate, Integer oid, Integer pid) {
        String condition =  " and o.createDate between '"+beginDate + "' and '"+endDate + "' and o.org.id = "+oid;
        if(pid!=null)
            condition+=" and o.account="+pid;
        Map<String,String> map = new HashMap<String, String>();
        map.put("id","desc");
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,map);
    }

    public List<AccountPeriod> getPeroidByOrgTime(Date beginDate, Date endDate, Integer fid){
        String condition = " and o.createDate between '"+beginDate + "' and '"+endDate + "' and o.fid = "+fid;
        Map<String,String> map = new HashMap<String, String>();
        map.put("id","desc");
        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,map);
    }

    @Override
    public List<AccountPeriod> getAccountPeroidByDate(String date, Integer id, Integer fid) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("beginDate","desc");
        String condition = " and o.org.id="+id+" and o.beginDate= '"+date+"' ";
        if(fid!=null)
            condition = " and o.fid="+fid;
        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }


    @Override
    public List<AccountPeriod> getAccountPeroidByDateStr(String begindate, String enddate, Integer id, Integer fid) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("beginDate","desc");
        String condition =  " and o.org.id="+id+" and o.beginDate between '"+begindate+"'and '"+enddate+"' ";
        if(fid!=null)
            condition = " and o.fid="+fid;
        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }


    @Override
    public List<FinanceAccountHistory> getFinanceAccountHistoryByAid(Integer aid) {
        String hql=" from FinanceAccountHistory where accountId_="+aid;
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("id","desc");
        return financeAccountHistoryDao.getListByHQL(hql);
    }

    @Override
    public List<FinanceAccount> getBankAccoutsByOid(Integer id) {
        String condition = " and o.org.id="+id+" and o.accountType=2";
        return financeAccountDao.findCollectionByConditionNoPage(condition,null,null);
    }


    @Override
    public List<AccountPeriod> getPeroidByOrgTimeStr(String firstday, String lastday, Integer fid) {
        String condition = " and o.createDate between '"+firstday + "' and '"+lastday + "' and o.fid = "+fid;
        Map<String,String> map = new HashMap<String, String>();
        map.put("id","desc");
        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,map);
    }

    @Override
    public void saveAccountDetail(AccountDetail accountDetail) {
        accountDetailDao.save(accountDetail);
    }

    @Override
    public void deleteAccountDetail(AccountDetail accountDetail) {
        accountDetailDao.delete(accountDetail);
    }

    @Override
    public void updateAccountDetail(AccountDetail accountDetail) {
        accountDetailDao.update(accountDetail);
    }

    @Override
    public AccountDetail getAccountDetailById(Integer id) {
        return accountDetailDao.get(id);
    }

    @Override
    public AccountPeriod getAccountPeroidByID(Integer pid) {
        return accountPeroidDao.get(pid);
    }

    @Override
    public AccountDetailHistory getAccountDetailHistoryById(Integer id) {
        return accountDetailHistoryDao.get(id);
    }

    @Override
    public List<AccountDetailHistory> getAccountDetailHistoryByadId(Integer adId) {
        String condition=" and o.detailId="+adId;
        Map<String,String> map = new HashMap<String, String>();
        map.put("id","desc");
        return accountDetailHistoryDao.findCollectionByConditionNoPage(condition,null,map);
    }

    @Override
    public void updateAccountPeroid(AccountPeriod accountPeriod) {
        accountPeroidDao.update(accountPeriod);
    }

    @Override
    public AccountPeriod getPeroidByType(Integer oid, int accountType, Integer fid, Integer periodType) {
        AccountPeriod accountPeriod=null;
        if (periodType==1){
            accountPeriod= this.getAccountPeriodByMonth(fid,new Date());
        }else if (periodType==2){
            accountPeriod= this.getAccountPeriodByDay(fid,new Date());
        }
        return accountPeriod;

    }

    @Override
    public List<AccountDetail> getPeroidDetailByOrgIDStr(String beginDate, String endDate, Integer fid, Integer pid) {
        String condition = " and o.createDate between '"+beginDate + "' and '"+endDate + "' and o.fid = "+fid;

        if(pid!=null)
            condition+=" and o.account="+pid;
        Map<String,String> map = new HashMap<String, String>();
        map.put("id","desc");
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,map);
    }

    @Override
    public FinanceAccount getFinanceAccountById(Integer fid) {
        return financeAccountDao.get(fid);
    }

    @Override
    public void updateFinanceAccount(FinanceAccount financeAccount) {
        financeAccountDao.update(financeAccount);
    }

    @Override
    public void addPeroid(AccountPeriod a) {
        accountPeroidDao.save(a);
    }

    @Override
    public void deleteAccountById(Integer fid) {
        financeAccountDao.deleteById(fid);
    }

    @Override
    public List<FinanceAccount> getAllFinanceAccounts() {
//        return financeAccountDao.findCollectionByConditionNoPage("",null,null);
        return financeAccountDao.getListByHQL("from FinanceAccount");
    }

    @Override
    public List<Date> getPeriodMinAndMaxDate(Integer id) {
        return accountPeroidDao.getPeriodMinAndMaxDate(id);
    }

    @Override
    public List<AccountPeriod> getAccountPeroidByOrgIDAndFidAndDate(Integer id,List<Integer> orgIntegerList, Integer fid, String beginDate, String endDate, Integer periodType) {
//        Map<String,String> orderBy = new HashMap<String,String>();
//        orderBy.put("o.beginDate","desc");
//        String condition="";
//        if (id!=null){
//            condition = " and o.org.id="+id;
//        }
//        if (orgIntegerList!=null&&orgIntegerList.size()>0){
//            condition = " and o.org.id in ("+orgIntegerList+")";
//        }
//        if(beginDate.equals(endDate)){//起始时间与结束时间相同，则查询一个月之内的数据
//            condition+=" and date_format(o.beginDate,'%Y-%m')=date_format('"+beginDate+"','%Y-%m')";
//        }
//        else{
//            condition+=" and o.beginDate>= '"+beginDate+" 00:00:00' and o.beginDate<= '"+endDate +" 23:59:59'";
//        }
//        condition+=" and o.periodType="+periodType;
//        if(fid!=null){
//            if (fid==0){
//                condition+=" and o.accountType=2";
//            }else {
//                condition+=" and o.fid="+fid;
//            }
//        }
//        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,orderBy);
        StringBuffer hql = new StringBuffer("from AccountPeriod");
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer where = new StringBuffer();
        if (id!=null) {
            where.append(" and org_ = :org");
            params.put("org", id);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0) {
            where.append(" and org_ in (:orgIntegerList)");
            params.put("orgIntegerList", orgIntegerList);
        }
        if(beginDate.equals(endDate)){//起始时间与结束时间相同，则查询一个月之内的数据
            where.append(" and date_format(beginDate,'%Y-%m')=date_format(:beginDate,'%Y-%m')");
            params.put("beginDate", beginDate);
        }
        else{
//            condition+=" and o.beginDate>= '"+beginDate+" 00:00:00' and o.beginDate<= '"+endDate +" 23:59:59'";
            where.append(" and beginDate between :beginDate and :endDate");
            params.put("beginDate", NewDateUtils.today(NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd")));
            params.put("endDate", NewDateUtils.getLastTimeOfDay(NewDateUtils.dateFromString(endDate,"yyyy-MM-dd")));
        }
        if (periodType != null) {
            where.append(" and periodType=:periodType");
            params.put("periodType", periodType);
        }
        if (fid!=null) {
            if (fid==0){
//                condition+=" and o.accountType=2";
                hql.append(" and accountType=2");
            }else {
//                condition+=" and o.fid="+fid;
                where.append(" and fid = :fid");
                params.put("fid", fid);
            }

        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4)).append(" order by beginDate desc");
        }
        List<AccountPeriod> accountPeriods = accountPeroidDao.getListByHQLWithNamedParams(hql.toString(),params);
        return accountPeriods;
    }

    @Override
    public List<AccountPeriod> getAccountPeroidByOrgIDFidDate(Integer id, Integer fid, String beginDate, String endDate, Integer periodType) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("beginDate","desc");
        String condition;
        condition = " and o.org.id="+id+" and o.beginDate>= '"+beginDate+" 00:00:00' and o.beginDate<= '"+endDate +" 23:59:59'";

        condition+=" and o.periodType="+periodType;
        if(fid!=null){
            if (fid==0){
                condition+=" and o.accountType=2";
            }else {
                condition+=" and o.fid="+fid;
            }
        }
        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }

    @Override
    public List<AccountDetail> getAccountDetailsByOrgIdAndFidAndDay(Integer id, Integer fid) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("createDate","desc");
        String condition = " and o.org.id="+id+" and o.fid="+fid+" and TO_DAYS(o.createDate)=TO_DAYS(now())";
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }

    @Override
    public List<AccountDetail> getAccountDetailsByOrgIdAndFid(Integer oid, Integer fid){
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("createDate","desc");
        String condition = " and o.org.id="+oid+" and o.fid="+fid;
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }

    @Override
    public List<AccountDetail> getAccountDetailsByOrgIdAndFidAndDate(Integer id, Integer fid, String beginDate, String endTime) {
        String condition = " and o.org.id="+id+" and o.fid="+fid+" and TO_DAYS(o.createDate)=TO_DAYS('"+beginDate+"')";
        if(endTime!=null){//查询某时间段的明细
            condition = " and o.org.id="+id+" and o.fid="+fid+" and o.createDate between '"+beginDate+"' and '"+endTime+"'";
        }
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("createDate","desc");
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }

    @Override
    public List<AccountDetail> getPeroidDetailByOrgIDAndDate(Integer id, String  beginDate, String  endDate, Integer debitType) {
        String condition = " and o.org.id="+id+" and o.createDate between '"+beginDate + "' and '"+endDate+"' and debit_type="+debitType;
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,null);
    }

    @Override
    public FinanceAccount getFinanceAccountByOidAndType(Integer id, int i) {
        Map<String,Object> map = new HashMap<>();
        String condition = " from FinanceAccount where org_=:id and accountType=:i";
        map.put("id",id);
        map.put("i",i);
        return (FinanceAccount) financeAccountDao.getByHQLWithNamedParams(condition,map);
    }

    @Override
    public void removeNoDataPeriod() {
        String condition = "DELETE FROM t_finance_account_peroid where credit=0 AND debit=0 and periodType=2;";
        accountPeroidDao.querySql(condition);
    }

    @Override
    public List<AccountDetail> getPeroidDetailByOrgIDAndDateAndFid(Integer company, String date, Integer fid, Integer dateType) {//dateType:1:查询天  2：查询月
        String condition = " and o.fid="+fid+" and TO_DAYS(o.createDate)=TO_DAYS('"+date+"')";
        if(dateType==2){
            condition = " and o.fid="+fid+"  and date_format(o.createDate,'%Y-%m')=date_format('"+date+"','%Y-%m')";
        }
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,null);
    }

    @Override
    public List<AccountDetail> getAccountDetailsByFidAndCreditType(Integer fid, Integer creditType) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("createDate","desc");
        String hql=" and o.fid='"+fid+"' and o.creditType="+creditType;
        return accountDetailDao.findCollectionByConditionNoPage(hql,null,orderBy);
    }

    @Override
    public Map<String,BigDecimal> getStringWordById(Integer id) {
        String condition = " and o.org = "+id;
        BigDecimal balance = BigDecimal.valueOf(0);
        BigDecimal previous = BigDecimal.valueOf(0);
        BigDecimal credit =  BigDecimal.valueOf(0);
        BigDecimal debit = BigDecimal.valueOf(0);
        List<FinanceAccount> financeAccounts = financeAccountDao.findCollectionByConditionNoPage(condition,null,null);
        for (FinanceAccount financeAccount : financeAccounts) {
            balance = balance.add(financeAccount.getBalance());
            previous = previous.add(financeAccount.getPreviousBalance());
            credit = credit.add(financeAccount.getCredit());
            debit = debit.add(financeAccount.getDebit());
        }
        Map<String, BigDecimal> map = new HashMap<>();
        map.put("balance",balance);
        map.put("previous",previous);
        map.put("credit",credit);
        map.put("debit",debit);
        return map;
    }

    @Override
    public List<AccountDetail> getAccountDetailByOidAndCreditTypeAndBeginDate(Integer oid, Integer creditType, String beginDate) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("createDate","desc");
        String condition;

        //起始时间与结束时间相同，则查询一个月之内的数据
        condition = " and o.org.id="+oid+" and o.creditType="+creditType+" and date_format(o.createDate,'%Y-%m')=date_format('"+beginDate+"','%Y-%m')";

        return accountDetailDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }


    @Override
    public List<AccountDetail> getAccountDetailsByFidAndBeginDateAndEndDate(Integer fid, String beginDate, String endDate) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("createDate","desc");
        String condition;
        condition = " and o.fid="+fid+" and o.createDate>='"+beginDate+"' and o.createDate<='"+endDate+"'";
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }
    @Override
    public List<AccountDetail> getAccountDetailByOidAndCreditTypeAndBeginDate(Integer oid, Integer creditType, String beginDate, Integer fid) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("auditDate","desc");
        String condition;

        //起始时间与结束时间相同，则查询一个月之内的数据
        condition = " and o.org.id="+oid+" and date_format(o.auditDate,'%Y-%m')=date_format('"+beginDate+"','%Y-%m')";
        if (creditType!=null){
            condition+=" and o.creditType="+creditType;
        }
        if (fid!=null){
            condition+=" and o.fid="+fid;
        }
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }

    @Override
    public List<AccountPeriod> getAccountPeroidByFidAndDate(Integer oid, Integer fid, String beginDate, String endDate, Integer periodType) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("beginDate","desc");
        String condition;
        if(beginDate.equals(endDate)){//起始时间与结束时间相同，则查询一个月之内的数据
            condition = " and o.org.id="+oid+" and date_format(o.beginDate,'%Y-%m')=date_format('"+beginDate+"','%Y-%m')";
        }
        else
            condition = " and o.org.id="+oid+" and o.beginDate>= '"+beginDate+"' and o.beginDate< '"+endDate +"'";
        condition+=" and o.periodType="+periodType;
        if(fid!=null)
            condition+=" and o.fid="+fid;
        return accountPeroidDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }

    @Override
    public List<AccountDetail> getAccountDetailByDate(Integer fid, String date) {
        Map<String,String> orderBy = new HashMap<String,String>();
        orderBy.put("createDate","desc");
        String condition = " and o.fid="+fid+" and o.auditDate<'"+date+"'";
        return accountDetailDao.findCollectionByConditionNoPage(condition,null,orderBy);
    }

    @Override
    public FinanceAccountHistory getFinanceAccountHistoryById(Integer id) {
        return financeAccountHistoryDao.get(id);
    }

    @Override
    public void addFinanceAccountHistory(AccountDetailHistory accountDetailHistory) {
        accountDetailHistoryDao.save(accountDetailHistory);
    }

    @Override
    public void addBankFinaceAccountHistory(FinanceAccountHistory financeAccountHistory){
        financeAccountHistoryDao.save(financeAccountHistory);
    }

    @Override
    public FinanceAccount getOperationAccount(Integer oid) {
        String condition = " from FinanceAccount o where 1=1 and o.org.id="+oid+" and o.operation='基本户'";
        return financeAccountDao.getByHQL(condition);
    }

    @Override
    public void addAccountDetailHistory(AccountDetailHistory accountDetailHistory) {
        accountDetailHistoryDao.save(accountDetailHistory);
    }

    @Override
    public void saveFinanceAccountHistory(FinanceAccountHistory financeAccountHistory) {
        financeAccountHistoryDao.save(financeAccountHistory);
    }

    @Override
    public List<AccountDetail> getAccountDetailListByDay(Integer oid, List<Integer> orgIntegerList,Integer fid, Date beginDate,Date endDate) {
//        String condition = " and o.org.id="+oid+" and day(o.createDate)=day('"+oneDate+"') and month(o.createDate)=month('"+oneDate+"') and YEAR(o.createDate)=YEAR('"+oneDate+"')";
//        if (fid!=null){
//            if (fid==0){
//                condition+=" and o.accountId.accountType=2";
//            }else {
//                condition+=" and o.accountId="+fid;
//            }
//        }
//        Map<String,String> map=new HashMap<String,String>();
////        map.put("createDate","desc");
//        map.put("id","desc");
//        List<AccountDetail> accountDetails=accountDetailDao.findCollectionByConditionNoPage(condition,null,map);
        StringBuffer condition = new StringBuffer("from AccountDetail");
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer where = new StringBuffer();
        if (oid != null) {
            where.append(" and org_ = :org");
            params.put("org", oid);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0) {
            where.append(" and org_ in (:orgIntegerList)");
            params.put("orgIntegerList",orgIntegerList);
        }
        if (fid!=null) {
            if (fid==0){
                where.append(" and accountId.accountType=2");
            }else {
                where.append(" and accountId_=:accountId");
                params.put("accountId", fid);
            }
        }
        if (beginDate!=null&&endDate!=null) {
            where.append(" and createDate between :beginDate and :endDate");
            params.put("beginDate", beginDate);
            params.put("endDate", endDate);
        }
        if (where.length() > 0) {
            condition.append(" where ").append(where.substring(4));
        }
        List<AccountDetail> accountDetails=accountDetailDao.getListByHQLWithNamedParams(condition.toString(),params);
        return accountDetails;
    }

    @Override
    public List<AccountPeriod> getAccountPeriodListByDay(Integer oid,List<Integer> orgIntegerList, Integer fid, Date oneDate, Integer periodType) {
        Map<String,Object> map=new HashMap<>();
        String condition = "from AccountPeriod where";
        if (oid!=null){
            condition+=" org_=:org";
            map.put("org",oid);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0){
            condition+=" org_ in (:orgIntegerList)";
            map.put("orgIntegerList",orgIntegerList);
        }
        if (fid!=null){
            if (fid==0){
                condition+=" and accountType=2";
            }else {
                condition+=" and fid=:fid";
                map.put("fid",fid);
            }
        }
        if (oneDate!=null){
            condition+=" and beginDate=:beginDate";
            map.put("beginDate",oneDate);
        }
        if (periodType!=null){
            condition+=" and periodType=:periodType";
            map.put("periodType",periodType);
        }
        condition+=" order by createDate desc";
        List<AccountPeriod> accountPeriods=accountPeroidDao.getListByHQLWithNamedParams(condition,map);
        return accountPeriods;
    }


    /**
     * <AUTHOR>
     * @Date 2017/4/25 16:50
     * 今日日结
     */
    @Override
    public AccountPeriod getToDayByfId(Integer accountId) {
        String hql = "from AccountPeriod o where o.periodType = 2 and o.accountId = " + accountId + " and YEAR(o.beginDate)=YEAR(now()) and month(o.beginDate)=month(now()) and TO_DAYS(o.beginDate) = TO_DAYS(now())";
        AccountPeriod accountPeriod = accountPeroidDao.getByHQL(hql);
        return accountPeriod;
    }

    /**
     * <AUTHOR>
     * @Date 2019/7/17 14:38
     * 补日结
     */
    @Override
    public AccountPeriod getAccountPeriodByDay(Integer accountId,Date today) {
        today = NewDateUtils.today(today);
        String hql="from AccountPeriod where periodType=:periodType and accountId=:accountId order by beginDate desc";
        Map<String,Object> params=new HashMap<>();
        params.put("accountId",accountId);
        params.put("periodType",2);
        AccountPeriod accountPeriod = (AccountPeriod) accountPeroidDao.getByHQLWithNamedParams(hql, params);//最新一期日结
        Date  beginDate;
        BigDecimal yesterdayBalance;
        if (accountPeriod==null || NewDateUtils.today(accountPeriod.getBeginDate()).getTime()<today.getTime()){
            beginDate = accountPeriod!=null && accountPeriod.getBeginDate()!=null ? NewDateUtils.tomorrow(accountPeriod.getBeginDate()) : today;
            yesterdayBalance = accountPeriod!=null && accountPeriod.getBalance()!=null ? accountPeriod.getBalance() : BigDecimal.valueOf(0);
            FinanceAccount f=financeAccountDao.get(accountId);
            accountPeriod=this.repairDayAccountPeriod(f,beginDate, yesterdayBalance, today); //调用补日结,并返回最新日结
            accountPeriod.setBalance(f.getBalance());//设成当前账户余额
        }
        return accountPeriod;
    }

    private final AccountPeriod repairDayAccountPeriod(FinanceAccount f, Date beginDate, BigDecimal balance,Date dayDate) {
        Date createDate=new Date();
        AccountPeriod returnAccountPeriod=new AccountPeriod();
        for (;beginDate.getTime()<=dayDate.getTime();beginDate=NewDateUtils.changeDay(beginDate,1)){
            AccountPeriod accountPeriod = new AccountPeriod();
            accountPeriod.setPeriodType(2);
            accountPeriod.setBuildState("未生成");
            accountPeriod.setCreateName("系统生成");
            accountPeriod.setAccountType(f.getAccountType());
            accountPeriod.setCreateDate(createDate);
            accountPeriod.setBeginDate(beginDate);
            accountPeriod.setDebit(BigDecimal.valueOf(0));
            accountPeriod.setCredit(BigDecimal.valueOf(0));
            accountPeriod.setBalance(balance);
            accountPeriod.setFid(f.getId());
            accountPeriod.setAccountId(f.getId());//账户外键
//            accountPeriod.setAccountId_(f.getId());
            accountPeriod.setAccount(f.getId().toString());
            accountPeriod.setOrg(f.getOrg());
            accountPeriod.setPreviousBalance(balance);//上期余额
            accountPeroidDao.save(accountPeriod);
            returnAccountPeriod=accountPeriod;
        }
        return returnAccountPeriod;//最新的日结
    }

    @Override
    public List<FinanceAccount> getFinanceByOidAccount(Integer oid, String account) {
        String hql=" from FinanceAccount where org="+oid;
        if (account!=null&&!"".equals(account)){
            hql+=" and account='"+account+"'";
        }
        List<FinanceAccount> financeAccounts=financeAccountDao.getListByHQL(hql);
        return financeAccounts;
    }

    @Override
    public AccountPeriod getAccountPeriodByMonth(Integer accountId, Date month) {
        String hql="from AccountPeriod where periodType=:periodType and accountId=:accountId order by beginDate desc";
        Map<String,Object> params=new HashMap<>();
        params.put("accountId",accountId);
        params.put("periodType",1);
        AccountPeriod accountPeriod = (AccountPeriod) accountPeroidDao.getByHQLWithNamedParams(hql, params);//最新一期月结
        Date  beginDate;
        BigDecimal yesterdayBalance;

        if (accountPeriod==null || NewDateUtils.today(accountPeriod.getBeginDate()).getTime()<NewDateUtils.changeMonth(month,0).getTime()){
            System.out.println("财务月结开始：");
            beginDate = accountPeriod!=null && accountPeriod.getBeginDate()!=null ? NewDateUtils.changeMonth(accountPeriod.getBeginDate(),1) : NewDateUtils.changeMonth(month,0);
            yesterdayBalance = accountPeriod!=null && accountPeriod.getBalance()!=null ? accountPeriod.getBalance() : BigDecimal.valueOf(0);
            FinanceAccount f=financeAccountDao.get(accountId);
            accountPeriod=this.repairMonthAccountPeriod(f,beginDate, yesterdayBalance, month); //调用补日结,并返回最新日结
            accountPeriod.setBalance(f.getBalance());//设成当前账户余额
            System.out.println("财务月结结束：" );
        }
        return accountPeriod;
    }

    private final AccountPeriod repairMonthAccountPeriod(FinanceAccount f, Date beginDate, BigDecimal balance,Date month) {
        Date createDate=new Date();
        AccountPeriod returnAccountPeriod=new AccountPeriod();
        for (;beginDate.getTime()<=NewDateUtils.changeMonth(month,0).getTime();beginDate=NewDateUtils.changeMonth(beginDate,1)){
            AccountPeriod accountPeriod = new AccountPeriod();
            accountPeriod.setPeriodType(1);
            accountPeriod.setBuildState("未生成");
            accountPeriod.setCreateName("系统生成");
            accountPeriod.setAccountType(f.getAccountType());
            accountPeriod.setCreateDate(createDate);
            accountPeriod.setBeginDate(beginDate);
            accountPeriod.setEndDate(NewDateUtils.getLastTimeOfMonth(beginDate));//月结结束时间
            accountPeriod.setDebit(BigDecimal.valueOf(0));
            accountPeriod.setCredit(BigDecimal.valueOf(0));
            accountPeriod.setBalance(balance);
            accountPeriod.setFid(f.getId());
            accountPeriod.setAccountId(f.getId());//账户外键
//            accountPeriod.setAccountId_(f.getId());
            accountPeriod.setAccount(f.getId().toString());
            accountPeriod.setOrg(f.getOrg());
            accountPeriod.setPreviousBalance(balance);//上期余额
            accountPeroidDao.save(accountPeriod);
            returnAccountPeriod=accountPeriod;
        }
        return returnAccountPeriod;//最新的日结
    }

    @Override
    public Map<String, Object> getBalance(Integer oid) {
        Map<String,Object> map=new HashMap<String,Object>();
        BigDecimal balanceAccount = new BigDecimal(0);
        BigDecimal balanceReturn = new BigDecimal(0);
        if (oid!=null){
            Map<String,Object> map1=new HashMap<String,Object>();
            String hql = "select sum(balance) from FinanceAccount where org_=:org and accountStatus=1";  //查询所有正常账户的当前余额(银行和现金的账户均有)
            map1.put("org",oid);
            balanceAccount = (BigDecimal) financeAccountDao.getByHQLWithNamedParams(hql.toString(),map1);
            balanceAccount = balanceAccount!=null?balanceAccount:new BigDecimal(0);

            map1.clear();
            String hql2 = "select sum(amount) from FinanceReturn where org_=:org and state='1'";  //已收到但尚未存入银行的转账支票及承兑汇票
            map1.put("org",oid);
            balanceReturn = (BigDecimal) financeReturnDao.getByHQLWithNamedParams(hql2.toString(),map1);
            balanceReturn = balanceReturn!=null?balanceReturn:new BigDecimal(0);
        }
        BigDecimal balanceTotal = balanceAccount.add(balanceReturn); //金额合计
        map.put("balanceAccount",balanceAccount);  //各银行账户与现金备用金的当前余额
        map.put("balanceReturn",balanceReturn);   //已收到但尚未存入银行的转账支票及承兑汇票
        map.put("balanceTotal",balanceTotal);   //金额合计
        return map;
    }

    @Override
    public Map<String, Object> getAllAccountData(Integer oid,List<Integer> orgIntegerList,Integer state, String beginDate, String endDate) {
        Map<String, Object> map = new HashMap<String, Object>();

        Double allBalance = 0.0;//余额
        Double allCredit = 0.0;//收入
        Double allDebit = 0.0;//支出
        Double allPreviousBalance = 0.0;//上月余额

        Double jianquCredit=0.0;//需要在总收入减去的内部非支出性转账金额
        Double jianquDebit=0.0;//需要在总支出减去的内部非支出性转账
        List<FinanceAccount> financeAccounts = getOrgFinanceAccounts(oid,orgIntegerList);
        String date = "";
        if (state == 1) {
//            date = sdf.format(new Date());
//            beginDate=date;
//            endDate=date;
            beginDate=NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");
            endDate=NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");
            Date begin=NewDateUtils.today(new Date());
            Date end=NewDateUtils.getLastTimeOfDay(begin);
            for (FinanceAccount f:financeAccounts){
                List<AccountPeriod> ri = this.getAccountPeriodListByDay(oid,orgIntegerList, f.getId(), begin, 2);
                if (ri.size()>0){
                    allBalance+=ri.get(0).getBalance().doubleValue();
                    allPreviousBalance+=ri.get(0).getPreviousBalance().doubleValue();
                    allCredit+=ri.get(0).getCredit().doubleValue();
                    allDebit+=ri.get(0).getDebit().doubleValue();
                }
            }

            jianquCredit=getFinanceSumByHql("AccountDetail","credit","createDate",begin,end,oid,orgIntegerList,"type='2'");
            jianquDebit=getFinanceSumByHql("AccountDetail","debit","createDate",begin,end,oid,orgIntegerList,"type='2'");

        } else if (state == 2 || state == 3) {
            Date begin=NewDateUtils.changeMonth(new Date(),0);
            Date end=NewDateUtils.changeMonth(new Date(),1);
            if (state == 2) {
//                beginDate = sdf.format(NewDateUtils.changeMonth(new Date(),0));//当前月第一天
//                endDate = sdf.format(new Date());//当前时间
                beginDate = NewDateUtils.dateToString(NewDateUtils.changeMonth(new Date(),0),"yyyy-MM-dd");//当前月第一天
                endDate =NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");//当前时间
            } else {
                begin = NewDateUtils.changeMonth(new Date(),-1);
                end = NewDateUtils.changeMonth(new Date(),0);
                beginDate = NewDateUtils.dateToString(begin,"yyyy-MM-dd");//上月第一天
                endDate = NewDateUtils.dateToString(NewDateUtils.getLastTimeOfMonth(begin),"yyyy-MM-dd");//上月最后一天
            }

            jianquCredit=getFinanceSumByHql("AccountDetail","credit","createDate",begin,end,oid,orgIntegerList,"type='2'");
            jianquDebit=getFinanceSumByHql("AccountDetail","debit","createDate",begin,end,oid,orgIntegerList,"type='2'");

            List<AccountPeriod> yue = getAccountPeroidByOrgIDAndFidAndDate(oid, orgIntegerList,null, beginDate, beginDate, 1);
            for (AccountPeriod a : yue) {
                allBalance += a.getBalance().doubleValue();
                allCredit += a.getCredit().doubleValue();
                allDebit += a.getDebit().doubleValue();
                allPreviousBalance += a.getPreviousBalance().doubleValue();
            }

        } else if (state == 4) {
            Date begin = NewDateUtils.getNewYearsDay();//获取本年第一天
            beginDate = NewDateUtils.dateToString(begin,"yyyy-MM-dd");//获取本年第一天
            endDate = NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");//获取当前时间

            Calendar c = Calendar.getInstance();
            int year = new Date().getYear() + 1900;//年
            c.clear();
            c.set(Calendar.YEAR, year);//今年第一天
            c.set(Calendar.DATE, -1);//减去一天为去年最后一天

            Double previousBalance=getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",NewDateUtils.getNewYearsDay(),NewDateUtils.changeMonth(begin,0),oid,orgIntegerList,"periodType=1");
            allPreviousBalance = previousBalance;//累积每个账户上期余额

            jianquCredit=getFinanceSumByHql("AccountDetail","credit","createDate",begin,new Date(),oid,orgIntegerList,"type='2'");
            jianquDebit=getFinanceSumByHql("AccountDetail","debit","createDate",begin,new Date(),oid,orgIntegerList,"type='2'");

            //查询本年 年初到年末
            for (FinanceAccount f : financeAccounts) {
                List<AccountPeriod> yueList = this.getAccountPeroidByOrgIDAndFidAndDate(oid,orgIntegerList, f.getId(), beginDate, endDate, 1);
                if (yueList.size() > 0) {
                    allBalance += yueList.get(0).getBalance().doubleValue();//累加每个账户余额

                    for (AccountPeriod p : yueList) {
                        allCredit += p.getCredit().doubleValue();//累积每个账户收入
                        allDebit += p.getDebit().doubleValue();//累加每个账户支出
                    }

                }
            }

        } else if (state == 5) {
            List<AccountPeriod> accountPeriods = new ArrayList<AccountPeriod>();
//            同年同月 取日结
//            if (DateUtils.getYear(sdf.parse(beginDate))==DateUtils.getYear(sdf.parse(endDate))&&DateUtils.getMonth(sdf.parse(beginDate))==DateUtils.getMonth(sdf.parse(endDate))) {
            for (FinanceAccount f : financeAccounts) {
                accountPeriods = this.getAccountPeroidByOrgIDAndFidAndDate(oid,orgIntegerList, f.getId(), beginDate, endDate, 2);
                if (accountPeriods.size() > 0) {
                    allBalance += accountPeriods.get(0).getBalance().doubleValue();//终止时间的总余额
                    allPreviousBalance += accountPeriods.get(accountPeriods.size() - 1).getPreviousBalance().doubleValue();//起始时间的上期余额
                }
                for (AccountPeriod a : accountPeriods) {
                    allCredit += a.getCredit().doubleValue();
                    allDebit += a.getDebit().doubleValue();
                }
            }
            Date d=new Date();
            String endDate1 = endDate.substring(0,10); //截取时间格式为年月日
            if (d.getTime()<NewDateUtils.dateFromString(endDate1,"yyyy-MM-dd").getTime()){
                endDate=NewDateUtils.dateToString(d,"yyyy-MM-dd");
            }

            Date begin=NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
            Date end=NewDateUtils.getLastTimeOfDay(NewDateUtils.dateFromString(endDate1,"yyyy-MM-dd"));
            jianquCredit=getFinanceSumByHql("AccountDetail","credit","createDate",begin,end,oid,orgIntegerList,"type='2'");
            jianquDebit=getFinanceSumByHql("AccountDetail","debit","createDate",begin,end,oid,orgIntegerList,"type='2'");

        }

        map.put("allBalance", allBalance);
        map.put("allCredit", allCredit-jianquCredit);
        map.put("allDebit", allDebit-jianquDebit);
        map.put("allPreviousBalance", allPreviousBalance);
        map.put("beginDate", beginDate);
        map.put("endDate", endDate);
        return map;
    }

    @Override     //state 1-本日，2-本月，3-上月，4-本年，5-自定义
    public Map<String, Object> getAllAccountDataByTime(Integer oid,List<Integer> orgIntegerList,Integer state, Integer fid, String beginDate, String endDate) throws ParseException {
        List<FinanceAccount> financeAccounts = this.getOrgFinanceAccounts(oid,orgIntegerList);  //添加了orgIntegerList，改了编写方式
        List<AccountPeriod> accountPeriodList=new ArrayList<AccountPeriod>();//存放结果集
        List<AccountDetail> accountDetails=new ArrayList<AccountDetail>();

        String sonOrgName="";
        if (oid!=null){
            Organization organization=orgService.getByOid(oid,true,false);
            sonOrgName=organization.getName();
        }

        Map<String,Object> map=new HashMap<String,Object>();
        String type=null;//A-跨年，B-跨月，C-本月，D-本日
        String jiaWhere="";
        if (fid!=null) {
            jiaWhere += " and fid=" + fid;
        }
        if (state==1){
            type="D";
            accountDetails=this.getAccountDetailListByDay(oid,orgIntegerList,null,NewDateUtils.today(),NewDateUtils.getLastTimeOfDay(new Date()));

            for (AccountDetail accountDetail:accountDetails) {
                accountDetail.setOrgName(accountDetail.getAccountId().getOrg().getName());
                //1.290首页财会（承接李亚星项目）    lixu 按前端要求加    method=3和4的 时候需要的返回值
                if ("3".equals(accountDetail.getMethod())||"4".equals(accountDetail.getMethod())){
                    accountDetail.setAccountNo(accountDetail.getAccountId().getAccount());
                    accountDetail.setReturnId(accountDetail.getBillDetail().getReturnBill());
                    accountDetail.setBankName(accountDetail.getAccountId().getBankName());
                    if (accountDetail.getBillDetail().getFinanceReturn()!=null) {
                        accountDetail.setReceiveDate(accountDetail.getBillDetail().getFinanceReturn().getReceiveDate());
                        accountDetail.setDepositDate(accountDetail.getBillDetail().getFinanceReturn().getDepositDate());
                        accountDetail.setDepositorName(accountDetail.getBillDetail().getFinanceReturn().getDepositorName());
                    }
                }
            }

        }else if (state==2||state==3){
            type="C";
            Date beginTime = null;
            Date endTime = null;
            if (state == 2) {
                //获取当前月第一天：
                beginTime = NewDateUtils.changeMonth(new Date(),0);
                endTime = new Date();
            }
            if (state == 3) {
                //获取上月第一天
                beginTime = NewDateUtils.changeMonth(new Date(),-1);
                //获取上月最后一天
                endTime = NewDateUtils.getLastTimeOfMonth(beginTime);
            }
            Date date = beginTime;

            while (date.getTime()<=endTime.getTime()) {
                Date begin=NewDateUtils.changeDay(date,0);
                Date end=NewDateUtils.getLastTimeOfDay(begin);
                Double dayBalance = getFinanceSumByHql("AccountPeriod","balance","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//日总余额
                Double dayCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//日总收入
                Double dayDebit = getFinanceSumByHql("AccountPeriod","debit","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//日总支出
                Double daypbalance = getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//昨日余额

                AccountPeriod ap = new AccountPeriod();
                ap.setOrgName(sonOrgName);
                ap.setBalance(new BigDecimal(dayBalance));
                ap.setCredit(new BigDecimal(dayCredit));
                ap.setDebit(new BigDecimal(dayDebit));
                ap.setPreviousBalance(new BigDecimal(daypbalance));
                ap.setBeginDate(date);//每日时间
//                ap.setFid(fid);

                if (dayCredit.intValue()!=0 || dayDebit.intValue()!=0) {  //收入与支出全是0的，页面不展示
                    ap.setAccountId(null);
                    accountPeriodList.add(ap);
                }
                date = NewDateUtils.changeDay(date,1);  //日加一天
            }

        }else if (state==4){
            type="B";
            //获取本年第一天
            Date date=NewDateUtils.getNewYearsDay(new Date());
//            //获取当前时间
            while (date.getTime()<=new Date().getTime()) {
                AccountPeriod ap = new AccountPeriod();
                Date begin=NewDateUtils.changeMonth(date,0);
                Date end=NewDateUtils.getLastTimeOfMonth(begin);
                Double monthBalance = getFinanceSumByHql("AccountPeriod","balance","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月总余额
                Double monthCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月总收入
                Double monthDebit = getFinanceSumByHql("AccountPeriod","debit","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月总支出
                Double monthpbalance = getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月上期余额

                ap.setOrgName(sonOrgName);
                ap.setBalance(new BigDecimal(monthBalance));
                ap.setCredit(new BigDecimal(monthCredit));
                ap.setDebit(new BigDecimal(monthDebit));
                ap.setPreviousBalance(new BigDecimal(monthpbalance));
                ap.setBeginDate(date);//每日时间
                ap.setAccountId(null);
                accountPeriodList.add(ap);//按时间查询列表

                date = NewDateUtils.changeMonth(date,1);//月加1月
            }
        }else if (state==5){   //自定义
            BigDecimal credit = new BigDecimal(0);//总收入
            BigDecimal debit = new BigDecimal(0);//总支出
            BigDecimal balance = new BigDecimal(0);//总余额
            BigDecimal preBalance = new BigDecimal(0);//上次余额

            Date beginTime = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
            Date endTime = NewDateUtils.dateFromString(endDate,"yyyy-MM-dd");
            AccountPeriod accountPeriod = new AccountPeriod();//存放一段时间结果

            //跨年
            if (NewDateUtils.getYear(beginTime)<NewDateUtils.getYear(endTime)) {
                type="A";
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//                TreeMap<Date, Date> dateMap = DateUtils.getDateMap(sdf.parse(beginDate), sdf.parse(endDate));//得到拆分后的年份
                TreeMap<Date, Date> dateMap = DateUtils.getDateMap(beginTime, endTime);//得到拆分后的年份
                Iterator ir = dateMap.keySet().iterator();

                while (ir.hasNext()) {
                    Date bDate = (Date) ir.next();//某段开始时间
                    Date eDate = dateMap.get(bDate);//某段结束时间
                    for (FinanceAccount f : financeAccounts) {
                        List<AccountPeriod> accountPeriods = this.getAccountPeroidByOrgIDAndFidAndDate(oid,orgIntegerList, f.getId(), NewDateUtils.dateToString(bDate,"yyyy-MM-dd"), NewDateUtils.dateToString(eDate,"yyyy-MM-dd"), 2);
                        if (accountPeriods.size()>0){
                            balance=balance.add(accountPeriods.get(0).getBalance());//累加余额
                            preBalance=preBalance.add(accountPeriods.get(accountPeriods.size()-1).getPreviousBalance());

                            for(AccountPeriod ap : accountPeriods){
                                credit=credit.add(ap.getCredit());
                                debit=debit.add(ap.getDebit());
                            }
                        }
                    }
                    accountPeriod.setOrgName(sonOrgName);
                    accountPeriod.setBalance(balance);
                    accountPeriod.setCredit(credit);
                    accountPeriod.setPreviousBalance(preBalance);
                    accountPeriod.setDebit(debit);
                    accountPeriod.setBeginDate(bDate);
                    accountPeriod.setEndDate(eDate);
                    accountPeriod.setAccountId(null);
                    accountPeriodList.add(accountPeriod);//放入结果集

                    accountPeriod=new AccountPeriod();

                    credit = new BigDecimal(0);//总收入清0
                    debit = new BigDecimal(0);//总支出清0
                    balance = new BigDecimal(0);//总余额清0
                    preBalance = new BigDecimal(0);//总上期余额清0
                }
            } else if (NewDateUtils.getYearMonth(beginTime)<NewDateUtils.getYearMonth(endTime)){   //跨月
                type="B";
                Date date=beginTime;
                while (NewDateUtils.getYearMonth(date)<=NewDateUtils.getYearMonth(endTime)) {
                    Date begin=NewDateUtils.changeMonth(date,0);
                    Date end=NewDateUtils.getLastTimeOfMonth(begin);
                    Double monthBalance = getFinanceSumByHql("AccountPeriod","balance","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月总余额
                    Double monthCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月总收入
                    Double monthDebit = getFinanceSumByHql("AccountPeriod","debit","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月总支出
                    Double monthpBalance = getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",begin,end,oid,orgIntegerList,"periodType=1"+jiaWhere);//月上期余额

                    accountPeriod.setOrgName(sonOrgName);
                    accountPeriod.setBalance(new BigDecimal(monthBalance));
                    accountPeriod.setCredit(new BigDecimal(monthCredit));
                    accountPeriod.setDebit(new BigDecimal(monthDebit));
                    accountPeriod.setBeginDate(date);//每日时间
                    accountPeriod.setEndDate(end);
                    accountPeriod.setPreviousBalance(new BigDecimal(monthpBalance));
                    accountPeriod.setAccountId(null);
                    accountPeriodList.add(accountPeriod);//按时间查询列表
                    accountPeriod=new AccountPeriod();

                    date = NewDateUtils.changeMonth(date,1); //月加1月
                }
            } else if (NewDateUtils.getYearMonth(beginTime).equals(NewDateUtils.getYearMonth(endTime))){   //本月
                 if (NewDateUtils.getDate(beginTime).equals(NewDateUtils.getDate(endTime))) {   //本日
                    type = "D";
                    Date date = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
                    accountDetails = this.getAccountDetailListByDay(oid,orgIntegerList, null, NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date));
                     for (AccountDetail accountDetail:accountDetails) {
                         accountDetail.setOrgName(accountDetail.getAccountId().getOrg().getName());
                         //1.290首页财会（承接李亚星项目）    lixu 按前端要求加    method=3和4的 时候需要的返回值
                         if ("3".equals(accountDetail.getMethod())||"4".equals(accountDetail.getMethod())){
                             accountDetail.setAccountNo(accountDetail.getAccountId().getAccount());
                             accountDetail.setReturnId(accountDetail.getBillDetail().getReturnBill());
                             accountDetail.setBankName(accountDetail.getAccountId().getBankName());
                             if (accountDetail.getBillDetail().getFinanceReturn()!=null) {
                                 accountDetail.setReceiveDate(accountDetail.getBillDetail().getFinanceReturn().getReceiveDate());
                                 accountDetail.setDepositDate(accountDetail.getBillDetail().getFinanceReturn().getDepositDate());
                                 accountDetail.setDepositorName(accountDetail.getBillDetail().getFinanceReturn().getDepositorName());
                             }
                         }
                     }
                }else {
                    type = "C";
                    Date date = beginTime;
                     if (endTime.getTime()>NewDateUtils.today().getTime()){
                         endTime = NewDateUtils.today();
                     }
                     while (date.getTime()<=endTime.getTime()) {
                        Date begin=NewDateUtils.changeDay(date,0);
                        Date end=NewDateUtils.getLastTimeOfDay(begin);
                        Double dayBalance = getFinanceSumByHql("AccountPeriod","balance","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//日总余额
                        Double dayCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//日总收入
                        Double dayDebit = getFinanceSumByHql("AccountPeriod","debit","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//日总支出
                        Double daypbalance = getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",begin,end,oid,orgIntegerList,"periodType=2"+jiaWhere);//昨日余额

                        AccountPeriod ap = new AccountPeriod();

                        ap.setOrgName(sonOrgName);
                        ap.setBalance(new BigDecimal(dayBalance));
                        ap.setCredit(new BigDecimal(dayCredit));
                        ap.setDebit(new BigDecimal(dayDebit));
                        ap.setPreviousBalance(new BigDecimal(daypbalance));
                        ap.setBeginDate(date);//每日时间
                        ap.setAccountId(null);
                        accountPeriodList.add(ap);

                         date = NewDateUtils.changeDay(date,1);//加一天的时间
                    }
                }
            }
        }
        map.put("type",type);
        map.put("accountDetailList",accountDetails);
        map.put("accountPeriodList",accountPeriodList);
        return map;
    }

    @Override     //sourceCheck 1-pc  2-手机端
    public Map<String, Object> getDataByAccount(Integer oid,List<Integer> orgIntegerList, Integer state, String beginDate, String endDate,Integer sourceCheck) {
        Map<String, Object> map = new HashMap<String, Object>();
        Double allBalance = 0.0;//本月/本年余额
        Double allCredit = 0.0;//收入
        Double allDebit = 0.0;//支出
        Double allPreviousBalance = 0.0;//上月/上期余额
        List<AccountPeriod> accountPeriods = new ArrayList<>();  //所有的银行账户
        AccountPeriod a = new AccountPeriod(); // 银行账户汇总
        List<AccountPeriod> accountPeriodCashs = new ArrayList<>();  //现金账户
        AccountPeriod accountPeriodBank = new AccountPeriod();  //银行每个账户

        List<FinanceAccount> financeAccounts = financeAccountService.getAccount(oid,orgIntegerList);
        if (financeAccounts.size() > 0) {
            for (FinanceAccount financeAccount : financeAccounts) {
                AccountPeriod accountPeriod = new AccountPeriod();
                if (state == 1 || state == 2 || state == 3) {
                    if (state == 1){   // 本日
//                        beginDate = sdf.format(new Date());  //本日
//                        endDate = sdf.format(new Date());  //本日
                        beginDate = NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");  //本日
                        endDate = beginDate;  //本日
                        accountPeriod = dataService.getDataDayByAccountId(financeAccount.getId());  //查看时间的日结
                        if (accountPeriod!=null) {
                            accountPeriod.setOrg_(financeAccount.getOrg_());
                            accountPeriod.setOrg(financeAccount.getOrg());
                            accountPeriod.setOrgName(financeAccount.getOrg().getName());
                        }
                    } else {   //本月
                        if (state == 2) {
//                            beginDate = sdf.format(NewDateUtils.changeMonth(new Date(),0));  //本月的第一天
//                            endDate = sdf.format(new Date());  //当前时间
                            beginDate = NewDateUtils.dateToString(NewDateUtils.changeMonth(new Date(),0),"yyyy-MM-dd");  //本月的第一天
                            endDate = NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");;  //当前时间
                        } else {  //上月
//                            beginDate = sdf.format(NewDateUtils.changeMonth(new Date(),-1));    //上月的第一天
//                            endDate = sdf.format(NewDateUtils.changeMonth(new Date(),0).getTime()-1);  //上月的最后一天
                            beginDate = NewDateUtils.dateToString(NewDateUtils.changeMonth(new Date(),-1),"yyyy-MM-dd");    //上月的第一天
                            endDate = NewDateUtils.dateToString(NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(new Date(),-1)),"yyyy-MM-dd");  //上月的最后一天
                        }
                        accountPeriod = dataService.getDataByMonth1(financeAccount.getId(), beginDate);  //查看时间的月结
                    }
                    if (accountPeriod !=null){
                        accountPeriod.setBankName(financeAccount.getBankName());
                        accountPeriod.setAccountName(financeAccount.getAccount());
                        accountPeriod.setIsBasic(financeAccount.getIsBasic());
                        accountPeriod.setIsPublic(financeAccount.getIsPublic());
                        accountPeriod.setAccountRealName(financeAccount.getName());
                        accountPeriod.setOrg_(financeAccount.getOrg_());
                        accountPeriod.setOrg(financeAccount.getOrg());
                        accountPeriod.setOrgName(financeAccount.getOrg().getName());
                    }
                    if (financeAccount.getAccountType() == 2) {
                        if (accountPeriod!=null){
                            accountPeriods.add(accountPeriod);   //每个银行账户的信息
                        }
                    }else {   //现金备用金
                        accountPeriodCashs.add(accountPeriod);
                    }
                }else {
                    List<AccountPeriod> accountPeriodList = new ArrayList<>();
                    if (state == 4) {  //本年
                        Date bd = NewDateUtils.changeYear(new Date(),0);//本年的第一天
                        Date ed = NewDateUtils.changeMonth(new Date(),1);; //本年当前月的下个月的第一天（因为月结的开始时间是月的第一天，而结束时间是下个月的第一天）
                        beginDate = NewDateUtils.dateToString(bd,"yyyy-MM-dd");//本年的第一天
                        endDate = NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");
                        accountPeriodList = dataService.getDataMonthByDate(financeAccount.getId(),NewDateUtils.dateToString(bd,"yyyy-MM-dd"),NewDateUtils.dateToString(ed,"yyyy-MM-dd"),1);  //按时间查找某个账户的月结
                    }else {   //5-自定义按时间
                        accountPeriodList = dataService.getDataMonthByDate(financeAccount.getId(),beginDate,endDate,2);  //按时间查找某个账户的日结
                    }
                    if (accountPeriodList.size()>0){
                        for (AccountPeriod aaa:accountPeriodList) {
                            allCredit = allCredit + aaa.getCredit().doubleValue();
                            allDebit = allDebit + aaa.getDebit().doubleValue();
                        }
                        allBalance = accountPeriodList.get(0).getBalance().doubleValue();  //本期余额
                        allPreviousBalance = accountPeriodList.get(accountPeriodList.size()-1).getPreviousBalance().doubleValue();  //上期余额
                    }
                    if (financeAccount.getAccountType() == 1){
                        accountPeriod.setAccountName(financeAccount.getAccount());
                        accountPeriod.setBankName(financeAccount.getBankName());
                        accountPeriod.setIsBasic(financeAccount.getIsBasic());
                        accountPeriod.setIsPublic(financeAccount.getIsPublic());
                        accountPeriod.setPreviousBalance(BigDecimal.valueOf(allPreviousBalance));
                        accountPeriod.setCredit(BigDecimal.valueOf(allCredit));
                        accountPeriod.setBalance(BigDecimal.valueOf(allBalance));
                        accountPeriod.setDebit(BigDecimal.valueOf(allDebit));
                        accountPeriod.setAccountId(financeAccount.getId());
                        accountPeriod.setAccountRealName(financeAccount.getName());
//                        accountPeriodCash.setAccountId_(financeAccount.getId());
                        accountPeriod.setOrg_(financeAccount.getOrg_());
                        accountPeriod.setOrg(financeAccount.getOrg());
                        accountPeriod.setOrgName(financeAccount.getOrg().getName());
                        accountPeriodCashs.add(accountPeriod);

                    }else {
                        accountPeriodBank.setAccountName(financeAccount.getAccount());
                        accountPeriodBank.setBankName(financeAccount.getBankName());
                        accountPeriodBank.setIsBasic(financeAccount.getIsBasic());
                        accountPeriodBank.setIsPublic(financeAccount.getIsPublic());
                        accountPeriodBank.setPreviousBalance(BigDecimal.valueOf(allPreviousBalance));
                        accountPeriodBank.setCredit(BigDecimal.valueOf(allCredit));
                        accountPeriodBank.setBalance(BigDecimal.valueOf(allBalance));
                        accountPeriodBank.setDebit(BigDecimal.valueOf(allDebit));
                        accountPeriodBank.setAccountId(financeAccount.getId());
                        accountPeriodBank.setAccountRealName(financeAccount.getName());
                        accountPeriodBank.setOrg_(financeAccount.getOrg_());
                        accountPeriodBank.setOrg(financeAccount.getOrg());
                        accountPeriodBank.setOrgName(financeAccount.getOrg().getName());
//                        accountPeriodBank.setAccountId_(financeAccount.getId());
                        accountPeriods.add(accountPeriodBank);
                    }
                    allBalance = 0.0;//本月/本年余额
                    allCredit = 0.0;//收入
                    allDebit = 0.0;//支出
                    allPreviousBalance = 0.0;//上月/上期余额
                    accountPeriodBank = new AccountPeriod();
                }
            }
            for (AccountPeriod aa:accountPeriods) {
                allBalance = allBalance + aa.getBalance().doubleValue();
                allCredit = allCredit + aa.getCredit().doubleValue();
                allDebit = allDebit + aa.getDebit().doubleValue();
                allPreviousBalance = allPreviousBalance + aa.getPreviousBalance().doubleValue();
            }
            a.setAccountId(0);  //银行汇总的账户id默认0
            a.setPreviousBalance(BigDecimal.valueOf(allPreviousBalance));
            a.setCredit(BigDecimal.valueOf(allCredit));
            a.setBalance(BigDecimal.valueOf(allBalance));
            a.setDebit(BigDecimal.valueOf(allDebit));
            a.setBankName("银行账户汇总");
        }
        map.put("beginDate",beginDate);  //开始时间
        map.put("endDate",endDate);  //结束时间
        map.put("accountPeriod1", accountPeriodCashs);  //现金账户
        map.put("accountPeriod2", a);  //银行汇总账户
        map.put("accountPeriod3", accountPeriods);//所有的银行账户信息
        if (sourceCheck!=null&&2==sourceCheck){   //现在单独处理一下手机端
            AccountPeriod accountPeriod1 = new AccountPeriod();
            if (accountPeriodCashs.size()>0) {
                accountPeriod1 = accountPeriodCashs.get(0);
            }
            map.put("accountPeriod1", accountPeriod1);  //现金账户
        }

        return map;
    }

    @Override   //fid=0是查银行汇总的
    public Map<String, Object> getAccountMonthOrDay(Integer oid,List<Integer> orgIntegerList, String monthOrDay, String beginDate, String endDate, Integer fid) throws ParseException {
        Map<String,Object> map=new HashMap<String,Object>();

        Double allBalance = 0.0;//余额
        Double allCredit = 0.0;//收入
        Double allDebit = 0.0;//支出
        Double allPreviousBalance = 0.0;//上月余额
        List<AccountPeriod> accountPeriodList=new ArrayList<AccountPeriod>();
        List<AccountDetail> accountDetails=new ArrayList<AccountDetail>();

        String type=null;//A-跨年，B-跨月，C-本月，D-本日

        Date beginTime = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
        if (beginTime==null){
            beginTime = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd HH:mm:ss");
        }
        Date endTimeBegin = NewDateUtils.dateFromString(endDate,"yyyy-MM-dd");
        if (endTimeBegin==null){
            endTimeBegin = NewDateUtils.dateFromString(endDate,"yyyy-MM-dd HH:mm:ss");
        }
        String jiaWhere="periodType=2";
        if (fid!=null) {
            if (fid==0){
                jiaWhere+= " and accountType=2";
            }else {
                jiaWhere+= " and fid=" + fid;
            }
        }

        if ("1".equals(monthOrDay)){  //1-年进月，2-月进日，3-日进明细
            type="B"; //A-跨年，B-跨月，C-本月，D-本日
            Date date=beginTime;
            if (NewDateUtils.getYearMonth(endTimeBegin)>NewDateUtils.getYearMonth(new Date())){
                endTimeBegin=new Date();
                endDate = NewDateUtils.dateToString(endTimeBegin,"yyyy-MM-dd");  //转化为string，最后展示用的
            }

            //月初总上期余额
            allPreviousBalance = getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",NewDateUtils.changeDay(date,0),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);

            while (NewDateUtils.getYearMonth(date)<=NewDateUtils.getYearMonth(endTimeBegin)) {
                Date endTime = NewDateUtils.getLastTimeOfMonth(date);  //获取某月的最后一天
                if (endTime.getTime()>new Date().getTime()){
                    endTime = new Date();
                }
                Double monthBalance = getFinanceSumByHql("AccountPeriod","balance","beginDate",NewDateUtils.today(endTime),endTime,oid,orgIntegerList,jiaWhere);//月总余额   月最后一天开始时间到 结束时间
                Double monthCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfMonth(date),oid,orgIntegerList,jiaWhere);//月总收入   月初到月末的 时间段
                Double monthDebit = getFinanceSumByHql("AccountPeriod","debit","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfMonth(date),oid,orgIntegerList,jiaWhere);//月总支出   月初到月末的 时间段
                Double monthpBalance= getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//，每段上月月  月第一天开始时间到 结束时间

                allBalance=monthBalance;
                allCredit+=monthCredit;
                allDebit+=monthDebit;

                AccountPeriod accountPeriod=new AccountPeriod();
                accountPeriod.setBalance(new BigDecimal(monthBalance));
                accountPeriod.setCredit(new BigDecimal(monthCredit));
                accountPeriod.setDebit(new BigDecimal(monthDebit));
                accountPeriod.setBeginDate(date);//每日时间
                accountPeriod.setPreviousBalance(new BigDecimal(monthpBalance));
                accountPeriod.setAccountId(fid);
                accountPeriodList.add(accountPeriod);//按时间查询列表
                date = NewDateUtils.changeMonth(date,1);//加一月的时间
            }
        }else if ("2".equals(monthOrDay)){  //1-年进月，2-月进日，3-日进明细
            type="C"; //A-跨年，B-跨月，C-本月，D-本日
            Date date = beginTime;
            if (new Date().getTime()<endTimeBegin.getTime()){
                endTimeBegin = new Date();
                endDate = NewDateUtils.dateToString(endTimeBegin,"yyyy-MM-dd");  //转化为string，最后展示用的
            }
            //总上期余额=开始时间上期总余额
            allPreviousBalance = getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",NewDateUtils.changeDay(date,0),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);

            while (date.getTime()<=endTimeBegin.getTime()) {
                Double dayBalance =getFinanceSumByHql("AccountPeriod","balance","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日总余额
                Double dayCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日总收入
                Double dayDebit =  getFinanceSumByHql("AccountPeriod","debit","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日总支出
                Double dayPreviousBalance=getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日上期总余额

                allBalance=dayBalance;//总余额等于最后一天的余额
                allCredit+=dayCredit;
                allDebit+=dayDebit;

                AccountPeriod ap = new AccountPeriod();
                ap.setPreviousBalance(new BigDecimal(dayPreviousBalance));
                ap.setBalance(new BigDecimal(dayBalance));
                ap.setCredit(new BigDecimal(dayCredit));
                ap.setDebit(new BigDecimal(dayDebit));
                ap.setBeginDate(date);//每日时间

                if (dayCredit.intValue()!=0 || dayDebit.intValue()!=0) {    //收入与支出全是0的，页面不展示
                    ap.setAccountId(fid);
                    accountPeriodList.add(ap);
                }
                date = NewDateUtils.changeDay(date,1); //日加一天
            }

        }else if ("3".equals(monthOrDay)){  //1-年进月，2-月进日，3-日进明细
            type="D"; //A-跨年，B-跨月，C-本月，D-本日
            List<AccountPeriod> ri = this.getAccountPeriodListByDay(oid,orgIntegerList, fid, beginTime,2);
            for (AccountPeriod a:ri){
                allBalance+=a.getBalance().doubleValue();
                allCredit+=a.getCredit().doubleValue();
                allDebit+=a.getDebit().doubleValue();
                allPreviousBalance+=a.getPreviousBalance().doubleValue();
            }
            Date date = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
            accountDetails=this.getAccountDetailListByDay(oid,orgIntegerList,fid,NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date));
            for (AccountDetail accountDetail:accountDetails) {
                accountDetail.setOrgName(accountDetail.getOrg().getName());
                //1.290首页财会（承接李亚星项目）    lixu 按前端要求加    method=3和4的 时候需要的返回值
                if ("3".equals(accountDetail.getMethod())||"4".equals(accountDetail.getMethod())){
                    accountDetail.setAccountNo(accountDetail.getAccountId().getAccount());
                    accountDetail.setReturnId(accountDetail.getBillDetail().getReturnBill());
                    accountDetail.setBankName(accountDetail.getAccountId().getBankName());
                    if (accountDetail.getBillDetail().getFinanceReturn()!=null) {
                        accountDetail.setReceiveDate(accountDetail.getBillDetail().getFinanceReturn().getReceiveDate());
                        accountDetail.setDepositDate(accountDetail.getBillDetail().getFinanceReturn().getDepositDate());
                        accountDetail.setDepositorName(accountDetail.getBillDetail().getFinanceReturn().getDepositorName());
                    }
                }
            }
        }else {
            //否则认为按传入两个时间来获取

            BigDecimal credit = new BigDecimal(0);//总收入
            BigDecimal debit = new BigDecimal(0);//总支出
            BigDecimal balance = new BigDecimal(0);//总余额
            BigDecimal preBalance = new BigDecimal(0);//上次余额

            AccountPeriod accountPeriod = new AccountPeriod();//存放一段时间结果

            //跨年
            if (NewDateUtils.getYear(beginTime)<NewDateUtils.getYear(endTimeBegin)) {
                type="A"; //A-跨年，B-跨月，C-本月，D-本日
//                TreeMap<Date, Date> dateMap = DateUtils.getDateMap(sdf.parse(beginDate), sdf.parse(endDate));//得到拆分后的年份
                TreeMap<Date, Date> dateMap = DateUtils.getDateMap(beginTime, endTimeBegin);//得到拆分后的年份
                Iterator ir = dateMap.keySet().iterator();

                while (ir.hasNext()) {
                    Date bDate = (Date) ir.next();//某段开始时间
                    Date eDate = dateMap.get(bDate);//某段结束时间
                    List<AccountPeriod> accountPeriods = this.getAccountPeroidByOrgIDAndFidAndDate(oid,orgIntegerList, fid, NewDateUtils.dateToString(bDate,"yyyy-MM-dd"), NewDateUtils.dateToString(eDate,"yyyy-MM-dd"), 1);
                    if (accountPeriods.size()>0){
                        balance=balance.add(accountPeriods.get(0).getBalance());//累加余额
                        preBalance=preBalance.add(accountPeriods.get(accountPeriods.size()-1).getPreviousBalance());

                        for(AccountPeriod ap : accountPeriods){
                            credit=credit.add(ap.getCredit());
                            debit=debit.add(ap.getDebit());
                        }
                    }
                    accountPeriod.setBalance(balance);
                    accountPeriod.setCredit(credit);
                    accountPeriod.setPreviousBalance(preBalance);
                    accountPeriod.setDebit(debit);
                    accountPeriod.setBeginDate(bDate);
                    accountPeriod.setEndDate(eDate);
                    accountPeriod.setAccountId(fid);
                    accountPeriodList.add(accountPeriod);//放入结果集

                    accountPeriod=new AccountPeriod();

                    credit = new BigDecimal(0);//总收入清0
                    debit = new BigDecimal(0);//总支出清0
                    balance = new BigDecimal(0);//总余额清0
                    preBalance = new BigDecimal(0);//总上期余额清0
                }
            } else if (NewDateUtils.getYear(beginTime).equals(NewDateUtils.getYear(endTimeBegin))&&NewDateUtils.getYearMonth(beginTime)<NewDateUtils.getYearMonth(endTimeBegin)){   //跨月
                type="B"; //A-跨年，B-跨月，C-本月，D-本日
                Date date=beginTime;
                String yueWhere="periodType=1";
                if (fid!=null) {
                    if (fid==0){
                        yueWhere += " and account_type=2";
                    }else {
                        yueWhere += " and fid=" + fid;
                    }
                }

                while (NewDateUtils.getYearMonth(date)<=NewDateUtils.getYearMonth(endTimeBegin)) {
                    Double monthBalance = getFinanceSumByHql("AccountPeriod","balance","beginDate",NewDateUtils.changeMonth(date,0),NewDateUtils.getLastTimeOfMonth(date),oid,orgIntegerList,yueWhere);//月总余额
                    Double monthCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",NewDateUtils.changeMonth(date,0),NewDateUtils.getLastTimeOfMonth(date),oid,orgIntegerList,yueWhere);//月总收入
                    Double monthDebit = getFinanceSumByHql("AccountPeriod","debit","beginDate",NewDateUtils.changeMonth(date,0),NewDateUtils.getLastTimeOfMonth(date),oid,orgIntegerList,yueWhere);//月总支出
                    Double monthpBalance =getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",NewDateUtils.changeMonth(date,0),NewDateUtils.getLastTimeOfMonth(date),oid,orgIntegerList,yueWhere);//月总上期余额

                    accountPeriod.setBalance(new BigDecimal(monthBalance));
                    accountPeriod.setCredit(new BigDecimal(monthCredit));
                    accountPeriod.setDebit(new BigDecimal(monthDebit));
                    accountPeriod.setBeginDate(date);//每日时间
                    accountPeriod.setPreviousBalance(new BigDecimal(monthpBalance));
                    accountPeriod.setAccountId(fid);
                    accountPeriodList.add(accountPeriod);//按时间查询列表

                    accountPeriod=new AccountPeriod();
                    date = NewDateUtils.changeMonth(date,1);  //月加1月
                }
            } else if (NewDateUtils.getYearMonth(beginTime).equals(NewDateUtils.getYearMonth(endTimeBegin))){  //本月
//                if (bday!=eday){
                if (NewDateUtils.getDate(beginTime).equals(NewDateUtils.getDate(endTimeBegin))){
                    type="C"; //A-跨年，B-跨月，C-本月，D-本日
                    Date date = beginTime;
                    while(date.getTime()<=endTimeBegin.getTime()) {
                        AccountPeriod ap = new AccountPeriod();
                        Double dayBalance =getFinanceSumByHql("AccountPeriod","balance","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日总余额
                        Double dayCredit = getFinanceSumByHql("AccountPeriod","credit","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日总收入
                        Double dayDebit =  getFinanceSumByHql("AccountPeriod","debit","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日总支出
                        Double daypbalance=getFinanceSumByHql("AccountPeriod","previousBalance","beginDate",NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date),oid,orgIntegerList,jiaWhere);//日上期总余额

                        ap.setBalance(new BigDecimal(dayBalance));
                        ap.setCredit(new BigDecimal(dayCredit));
                        ap.setDebit(new BigDecimal(dayDebit));
                        ap.setPreviousBalance(new BigDecimal(daypbalance));
                        ap.setBeginDate(date);//每日时间
                        ap.setAccountId(fid);
                        accountPeriodList.add(ap);
                        date = NewDateUtils.changeDay(date,1); //日加一天
                    }
                } else {  //本日
                    type="D"; //A-跨年，B-跨月，C-本月，D-本日
                    Date date = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
                    accountDetails=this.getAccountDetailListByDay(oid,orgIntegerList,fid,NewDateUtils.today(date),NewDateUtils.getLastTimeOfDay(date));
                    for (AccountDetail accountDetail:accountDetails) {
                        accountDetail.setOrgName(accountDetail.getOrg().getName());

                        //1.290首页财会（承接李亚星项目）    lixu 按前端要求加    method=3和4的 时候需要的返回值
                        if ("3".equals(accountDetail.getMethod())||"4".equals(accountDetail.getMethod())){
                            accountDetail.setAccountNo(accountDetail.getAccountId().getAccount());
                            accountDetail.setReturnId(accountDetail.getBillDetail().getReturnBill());
                            accountDetail.setBankName(accountDetail.getAccountId().getBankName());
                            if (accountDetail.getBillDetail().getFinanceReturn()!=null) {
                                accountDetail.setReceiveDate(accountDetail.getBillDetail().getFinanceReturn().getReceiveDate());
                                accountDetail.setDepositDate(accountDetail.getBillDetail().getFinanceReturn().getDepositDate());
                                accountDetail.setDepositorName(accountDetail.getBillDetail().getFinanceReturn().getDepositorName());
                            }
                        }
                    }
                }
            }

            //计算这段时间的总和
            List<AccountPeriod> accountPeriods=new ArrayList<AccountPeriod>();
            if ("D".equals(type)){
                accountPeriods=this.getAccountPeriodListByDay(oid,orgIntegerList,fid,beginTime,2);
            }else {
                accountPeriods = this.getAccountPeroidByOrgIDAndFidAndDate(oid, orgIntegerList,fid, beginDate, endDate, 2);
            }
            if (accountPeriods.size()>0){
                if (fid==null||fid==0) {
                    List<FinanceAccount> financeAccounts=this.getOrgFinanceAccounts(oid,orgIntegerList);
                    for (FinanceAccount f:financeAccounts){
                        if (fid!=null){
                            if (2==f.getAccountType()){
                                List<AccountPeriod> aps= this.getAccountPeroidByOrgIDAndFidAndDate(oid,orgIntegerList, f.getId(), beginDate, endDate, 2);
                                if (aps.size()>0) {
                                    allBalance += aps.get(0).getBalance().doubleValue();//终止时间的总余额
                                    allPreviousBalance += aps.get(aps.size() - 1).getPreviousBalance().doubleValue();//起始时间的上期余额
                                }
                            }
                        }else {
                            List<AccountPeriod> aps= this.getAccountPeroidByOrgIDAndFidAndDate(oid,orgIntegerList, f.getId(), beginDate, endDate, 2);
                            if (aps.size()>0) {
                                allBalance += aps.get(0).getBalance().doubleValue();//终止时间的总余额
                                allPreviousBalance += aps.get(aps.size() - 1).getPreviousBalance().doubleValue();//起始时间的上期余额
                            }
                        }

                    }
                }
                else {
                    allBalance += accountPeriods.get(0).getBalance().doubleValue();//终止时间的总余额
                    allPreviousBalance += accountPeriods.get(accountPeriods.size() - 1).getPreviousBalance().doubleValue();//起始时间的上期余额
                }
            }
            for (AccountPeriod a:accountPeriods){
                if (fid==null||fid==0) {
                    if (fid!=null){
                        if (2==a.getAccountType()){
                            allCredit+=a.getCredit().doubleValue();
                            allDebit+=a.getDebit().doubleValue();
                        }
                    }
                }else {
                    allCredit+=a.getCredit().doubleValue();
                    allDebit+=a.getDebit().doubleValue();
                }

            }
        }

        String bankName = "";  //开户行
        if (fid!=null){
            if (fid==0){
                bankName = "银行账户汇总";
            }else {
                FinanceAccount financeAccount = financeAccountDao.get(fid);
                bankName = financeAccount.getBankName();
            }
        }

        map.put("financeAccountId", fid);  //银行账户id
        map.put("bankName", bankName);  //开户行
        map.put("allBalance", allBalance);
        map.put("allCredit", allCredit);
        map.put("allDebit", allDebit);
        map.put("allPreviousBalance", allPreviousBalance);
        map.put("beginDate", beginDate);
        map.put("endDate", endDate);
        map.put("accountPeriodList",accountPeriodList);
        map.put("accountDetailList",accountDetails);
        map.put("type",type);
        return map;
    }

    private Double getFinanceSumByHql(String entity,String selectField,String whereField,Date begin,Date end,Integer oid,List<Integer> orgIntegerList,String fixedWhere){
        StringBuffer hql=new StringBuffer();
        Map<String,Object> res=new HashMap<>();
        hql.append("select sum("+selectField+") from "+entity+" WHERE");
        if (oid!=null){
            hql.append(" org_=:org");
            res.put("org",oid);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0){
            hql.append(" org_ in (:orgIntegerList)");
            res.put("orgIntegerList",orgIntegerList);
        }
        if (fixedWhere!=null){
            hql.append(" and "+fixedWhere);
        }
        hql.append(" and "+whereField+" between :begin and :end");
        res.put("begin",begin);
        res.put("end",end);
        BigDecimal result= (BigDecimal) accountPeroidDao.getByHQLWithNamedParams(hql.toString(),res);
        return result==null?0.0:result.doubleValue();

    }

    @Override
    public AccountDetail getAccountDetailByBusinessHistoryId(Integer businessHistory, String source) {
        Map<String,Object> map=new HashMap<>();
        String hql = "from AccountDetail where businessHistory=:businessHistory";
        map.put("businessHistory",businessHistory);
        if (StringUtils.isNotEmpty(source)){
            hql+=" and source=:source";
            map.put("source",source);
        }
        AccountDetail accountDetail = (AccountDetail) accountDetailDao.getByHQLWithNamedParams(hql,map);
        return accountDetail;
    }

    /**
     * 数据录入的收入---现金【和/credit.do接口中的收入现金的一样，这里统一下(待合适的时间，将收入接口那里的改为此接口)】
     * @param user
     * @param money 金额
     * @param billAmount 票面金额
     * @param method 0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param genre 类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
     * @param summary 摘要
     * @param purpose 用途
     * @param auditorName 经手人
     * @param oppositeCorp 付款单位(收款单位)
     * @param memo 备注
     * @param source 数据来源 1-手工录入,2-业务自动生成 3-销售回款 4-薪资宝转出 5-票款处理 6-工资管理 7-预付款
     * @param categoryDesc 当类别为其他时候的说明
     * @param billDate 票据日期
     * @param business 业务id
     * @param businessDate 业务发生日期
     * @param factDate 实际(付款/支出)日期'
     * @return
     */
    @Override
    public Map<String, Object> getCreditCash(User user, String money, String billAmount, String method, String genre, String summary,
        String purpose, String auditorName, String oppositeCorp, String memo, String source, String categoryDesc,Date receiveAccountDate,Date billDate,Integer business,Date businessDate,Date factDate) {
        Map<String,Object> map=new HashMap<>();
        Organization organization = user.getOrganization();
        //单纯现金
        FinanceAccount financeAccount = this.getFinanceAccountByOidAndType(organization.getId(), 1);//备用金
        AccountPeriod accountPeriod = this.getAccountPeriodByMonth(financeAccount.getId(),new Date());

        AccountPeriod ap = this.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结
        //Debug start ********
//        debug.put("financeAccount0", JSON.toJSONString(financeAccount));
//        debug.put("accountPeriodDay0", JSON.toJSONString(ap));
//        debug.put("accountPeriodMonth0", JSON.toJSONString(accountPeriod));
        //Debug finish ********
        if ("0".equals(financeAccount.getAccountStatus()) || 0 == financeAccount.getAccountStatus()) {
            map.put("status", 2);//此账户已被冻结，不允许操作
            map.put("content", "此账户已被冻结，不允许操作");//此账户已被冻结，不允许操作
        }else {

            if (accountPeriod.getBuildState() != null) {
                if (accountPeriod.getBuildState().equals("已生成"))
                    map.put("status", 3);//已生成
            }
            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setCredit(new BigDecimal(money));
            accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(organization);
            accountDetail.setCategoryDesc(categoryDesc);
            accountDetail.setAccount(accountPeriod.getId().toString());
            accountDetail.setAuditDate(new Date());
            accountDetail.setAuditorName(auditorName);//经手人
            accountDetail.setMethod(method);//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
            accountDetail.setMemo(memo);
            accountDetail.setGenre(genre);
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(purpose);//用途
            accountDetail.setOppositeCorp(oppositeCorp);//付款单位(收款单位)
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setSource(source);
            if (financeAccount.getAccount() != null) {
                accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            } else {
                accountDetail.setAccountBank(financeAccount.getBankName());
            }
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setReceiveAccountDate(receiveAccountDate);//收到日期
            accountDetail.setBillDate(billDate);
            accountDetail.setBusiness(business);
            accountDetail.setBusinessDate(businessDate);
            accountDetail.setFactDate(factDate);


//            BigDecimal credit = accountPeriod.getCredit().add(accountDetail.getCredit());
//            BigDecimal balance = accountPeriod.getBalance().add(accountDetail.getCredit());
            financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriod.setCredit(accountPeriod.getCredit().add(accountDetail.getCredit()));
            accountPeriod.setBalance(accountPeriod.getBalance().add(accountDetail.getCredit()));

            BigDecimal credit = ap.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : ap.getCredit().add(accountDetail.getCredit());
            BigDecimal balance = ap.getBalance().add(accountDetail.getCredit());
            ap.setCredit(credit);
            ap.setBalance(balance);

            accountPeroidDao.update(accountPeriod);//月结
            accountPeroidDao.update(ap);//日结
            financeAccountDao.update(financeAccount);//账户
            accountDetailDao.save(accountDetail);//明细
            map.put("financeAccountId", financeAccount.getId());//成功
            map.put("accountDetailId", accountDetail.getId());//成功
            map.put("status", 1);//成功
            map.put("content", "操作成功");//成功
        }
//
//        //Debug start ********
//        debug.put("money", money.toString());
//        debug.put("balance", balance.toString());
//        debug.put("accountId", financeAccount.getId().toString());
//        debug.put("financeAccount", JSON.toJSONString(financeAccount));
//        debug.put("accountDetail", JSON.toJSONString(accountDetail));
//        debug.put("accountPeriodDay", JSON.toJSONString(ap));
//        debug.put("accountPeriodMonth", JSON.toJSONString(accountPeriod));
//        //Debug finish ********
        return map;
    }

    /**
     * 数据录入的收入---银行转账【和/credit.do接口中的收入银行转账的一样，这里统一下(待合适的时间，将收入接口那里的改为此接口)】
     * @param user
     * @param financeAccountId 账户id
     * @param money 金额
     * @param billAmount 票面金额
     * @param method 0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param genre 类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
     * @param summary 摘要
     * @param purpose 用途
     * @param auditorName 经手人
     * @param oppositeCorp 付款单位(收款单位)
     * @param receiveAccountDate 到账时间
     * @param memo 备注
     * @param source 数据来源1-手工录入,2-业务自动生成 3-销售回款 4-薪资宝转出 5-票款处理 6-工资管理 7-预付款
     * @param categoryDesc 当类别为其他时候的说明
     * @param billDate 票据日期
     * @param business 业务id
     * @param businessDate 业务发生日期
     * @param factDate 实际(付款/支出)日期'
     * @return
     */
    @Override
    public Map<String,Object> getCreditBank(User user,Integer financeAccountId,String money,String billAmount,String method,String genre, String summary,
        String purpose, String auditorName, String oppositeCorp, Date receiveAccountDate, String memo,String source, String categoryDesc,Date billDate,Integer business,Date businessDate,Date factDate) {
        Map<String,Object> map = new HashMap<>();
        FinanceAccount financeAccount = financeAccountDao.get(financeAccountId);
        AccountPeriod accountPeriod = this.getAccountPeriodByMonth(financeAccount.getId(), new Date());
//            if (accountPeriod == null) {
//                FinanceUtils.addPeroid(financeAccount.getId(),user, accountService);
//            }
//        accountPeriod = this.getAccountPeriodByMonth(financeAccount.getId(), new Date());//再查一遍
        AccountPeriod ap = this.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

        if ("0".equals(financeAccount.getAccountStatus()) || 0 == financeAccount.getAccountStatus()) {
            map.put("status", 2);//此账户已被冻结，不允许操作
            map.put("content", "此账户已被冻结，不允许操作");
        }else {

            if (accountPeriod.getBuildState() != null) {
                if (accountPeriod.getBuildState().equals("已生成"))
                    map.put("status", 3);//已生成
            }

            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setCredit(new BigDecimal(money));
            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setAccount(accountPeriod.getId().toString());
            accountDetail.setAuditDate(new Date());
            accountDetail.setCategoryDesc(categoryDesc);
            accountDetail.setAuditorName(auditorName);
            accountDetail.setGenre(genre);  //类别
            accountDetail.setMethod(method);
            accountDetail.setMemo(memo);
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(purpose);//用途
            accountDetail.setOppositeCorp(oppositeCorp);//收款单位
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setReceiveAccountDate(receiveAccountDate);//到账日期(年-月-日)
            accountDetail.setBillAmount(new BigDecimal(billAmount));  //票面金额
            accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setSource(source);
            accountDetail.setBillDate(billDate);
            accountDetail.setBusiness(business);
            accountDetail.setBusinessDate(businessDate);
            accountDetail.setFactDate(factDate);  //实际(付款/支出)日期'

//            BigDecimal credit = accountPeriod.getCredit().add(accountDetail.getCredit());
//            BigDecimal balance = accountPeriod.getBalance().add(accountDetail.getCredit());
            financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriod.setCredit(accountPeriod.getCredit().add(accountDetail.getCredit()));
            accountPeriod.setBalance(accountPeriod.getBalance().add(accountDetail.getCredit()));

            BigDecimal credit = ap.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : ap.getCredit().add(accountDetail.getCredit());
            BigDecimal balance = ap.getBalance().add(accountDetail.getCredit());
            ap.setCredit(credit);
            ap.setBalance(balance);

            accountPeroidDao.update(accountPeriod);//月结
            accountPeroidDao.update(ap);//日结
            financeAccountDao.update(financeAccount);//账户
            accountDetailDao.save(accountDetail);//明细
            map.put("accountDetailId", accountDetail.getId());//成功
            map.put("status", 1);//成功
            map.put("content", "操作成功");//成功
        }
        //Debug start ********
//        debug.put("money", money.toString());
//        debug.put("balance", balance.toString());
//        debug.put("accountId", financeAccountId.toString());
//        debug.put("financeAccount", JSON.toJSONString(financeAccount));
//        debug.put("accountDetail", JSON.toJSONString(accountDetail));
//        debug.put("accountPeriodDay", JSON.toJSONString(ap));
//        debug.put("accountPeriodMonth", JSON.toJSONString(accountPeriod));
        //Debug finish ********
        return map;
    }

    /**
     * 数据录入的收入---回款票据(method为3或4的)【和/credit.do接口中的收入回款票据的一样，这里统一下(待合适的时间，将收入接口那里的改为此接口)】
     * @param user
     * @param money 金额
     * @param billAmount 票面金额
     * @param method 0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param genre 类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
     * @param bankName 出具票据银行
     * @param summary 摘要
     * @param purpose 用途
     * @param auditorName 经手人
     * @param expireDate 发票到期日期(年-月-日)
     * @param originalCorp 出具票据单位
     * @param oppositeCorp 付款单位
     * @param receiveDate 收到票据日期(年-月-日)
     * @param memo 备注
     * @param source 数据来源1-手工录入,2-业务自动生成 3-销售回款 4-薪资宝转出 5-票款处理 6-工资管理 7-预付款
     * @param categoryDesc 当类别为其他时候的说明
     * @param returnNo 支票号
     * @param billDate 票据日期
     * @param business 业务id
     * @param businessDate 业务发生日期
     * @param factDate 实际(付款/支出)日期'
     * @return
     */
    @Override
    public Map<String, Object> getCreditReturn(User user, String money, String billAmount, String method, String genre,String bankName,
        String summary, String purpose, String auditorName,Date expireDate, String originalCorp, String oppositeCorp,Date receiveDate,
        String memo, String source, String categoryDesc,String returnNo,Date billDate,Integer business,Date businessDate,Date factDate) {
        Map<String,Object> map = new HashMap<>();
        //回款票据 的转账支票
        FinanceReturn fr = new FinanceReturn();
        fr.setReturnNo(returnNo);//支票号码
        if ("3".equals(method)) {
            fr.setType(1);//1-转账支票 2-承兑汇票
        } else {
            fr.setType(2);//1-转账支票 2-承兑汇票
        }
        fr.setCategory(genre);//1-贷款,2-借款,3-投资款,4-废品,5-其他
        fr.setCategoryDesc(categoryDesc);//其他时候的说明
        fr.setAmount(new BigDecimal(money));//金额
        fr.setBillAmount(new BigDecimal(billAmount));//票面金额
        fr.setSummary(summary);//摘要
        fr.setPurpose(purpose);//用途
        fr.setOperatorName(auditorName);//经手人
        fr.setPayer(oppositeCorp);//付款单位
        fr.setOriginalCorp(originalCorp);//出具票据单位
        fr.setBankName(bankName);//出具票据银行
        fr.setExpireDate(expireDate);//发票到期日期(年-月-日)
        fr.setReceiveDate(receiveDate);//收到票据日期(年-月-日)
        fr.setMemo(memo);//备注
        fr.setState("1");//1-有效,2-存入银行,3-作废
        fr.setCreateDate(new Date());
        fr.setCreateName(user.getUserName());
        fr.setCreator(user.getUserID());
        fr.setOrg(user.getOrganization());
        dataService.saveFinanceReturn(fr);

        //支票明细
        FinanceAccountBill f = new FinanceAccountBill();
        f.setType("1");//1-收入，2-支出
        f.setAmount(new BigDecimal(money));//金额
        f.setBillAmount(new BigDecimal(billAmount));  //票面金额
        f.setSummary(summary);//摘要
        f.setPurpose(purpose);//用途
//            f.setBillQuantity();//票据数量
//            f.setBillPeriod();//是否本月票据
        f.setBillNo(returnNo);//支票号码
        f.setFinanceReturn(fr);//回款票据外键
        f.setOrg(user.getOrganization());//机构
        f.setOperatorName(auditorName);//经手人
        f.setMemo(memo);//备注
        f.setAccountantStatus("1");
        f.setSource(source);
        f.setBillDate(billDate);
        f.setBusiness(business);
        f.setBusinessDate(businessDate);
        f.setFactDate(factDate);
        f.setCreateDate(new Date());
        f.setCreateName(user.getUserName());
        f.setCreator(user.getUserID());
        dataService.saveFinanceAccountBill(f);
        map.put("accountBill", f.getId());//成功
        map.put("status", 1);//成功
        map.put("content", "操作成功");//成功

        //Debug start ********
//        debug.put("money", money.toString());
//        debug.put("financeReturn", JSON.toJSONString(fr));
//        debug.put("financeAccountBill", JSON.toJSONString(f));
        //Debug finish ********
        return map;
    }
}
