package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.entity.SubjectChooseParams;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.entity.UpdateVoucherParams;
import cn.sphd.miners.modules.accountant.service.AccountantInputService;
import cn.sphd.miners.modules.accountant.service.impl.VoucherUpdate;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;

/**
 * Created by root on 17-1-4.
 */
@Controller
@RequestMapping("/accountant")
public class AccountantInputController {

    @Autowired
    AccountantInputService accountantInputService;
    @Autowired
    VoucherUpdate voucherUpdate;

    @RequestMapping("/getAccountantInputs.do")
    public void getAccountantInputs(User user, HttpServletResponse response) {

//        Integer org = (Integer) session.getAttribute("oid");
        Integer org = user.getOid();
        JSONArray res = accountantInputService.getVouchers(org);
        JSONObject result = new JSONObject();
        result.put("accVoucher",res);
//        WriteJSONUtil.writeObject(res,"accVoucher",response);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    @RequestMapping("/insertAccountantInputs.do")
    public void insertAccountantInputs(User user, HttpServletResponse response, SubjectChooseParams params) {

//        User user = (User) session.getAttribute("user");
        params.setUser(user);
        params.setOid(user.getOid());
        JSONObject state = accountantInputService.insertVoucher(params);
        WriteJSONUtil.writeJSON(state.toString(),response);

    }

    @RequestMapping("/deleteAccountantInputs.do")
    public void deleteAccountantInputs(HttpServletResponse response, int id) {
        int state = accountantInputService.deleteVoucher(id);
        WriteJSONUtil.writeObject(state,"res",response);

    }

    @RequestMapping("/updateAccountantInputs.do")
    public void updateAccountantInputs(User user, HttpServletResponse response, SubjectChooseParams params) {

//        User user = (User) session.getAttribute("user");
//        int userid = user.getUserID();
//        String username = user.getUserName();
        params.setOid(user.getOid());
        params.setUser(user);

        String subjectLoan = params.getSubjectLoan();
        String subjectBorrow = params.getSubjectBorrow();
        double price = params.getPrice();
        boolean isEqual = voucherUpdate.checkPrice(subjectBorrow,subjectLoan,price);
        if (!isEqual)
        {
            JSONObject state = new JSONObject();
            state.put("res",0);
            WriteJSONUtil.writeJSON(state.toString(),response);
        }
        /*
        * 会计修改“会计录入”的凭证不许要审批
        * 所以谁修改的谁就是审核人
        * id                凭证id
        * oid               机构id
        * bill_detail       票据id
        * summary           摘要
        * price             科目金额
        * subjectBorrow     借方科目
        * subjectLoan       贷方科目
        * userid            用户id，用作创建人
        * username          用户姓名，用作创建人姓名
        * userid            这个userid用作审批人id
        * username          审批人姓名
        * cashitem          供用户选择的现金项目
        * cash              用户输入的现金
        * */
        JSONObject state = accountantInputService.updateVoucher(params);

        //        WriteJSONUtil.writeObject(state,"res",response);
        WriteJSONUtil.writeJSON(state.toString(),response);

    }

    /*
    * 判断是不是小结账
    * id 凭证id
    * voucher_id 历史表的id，存在则说明修改的是历史表的数据
    * */
    @RequestMapping("/judgeMinorSettle.do")
    public void judgeMinorSettle(User user,HttpServletResponse response,
                                 UpdateVoucherParams params) {

//        User user = (User) session.getAttribute("user");
//        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        Integer org = user.getOid();
        JSONObject result = accountantInputService.judgeMinorSettle(org,params);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    /*
    * 返回所有指定的科目,列出所有需要转结的科目
    * 当会计录入凭证时
    * 借方录入了4001或其子科目同时贷方录入了4101或其子科目时
    * 或者当借方录入了1405或其子科目同时贷方录入了4001或其子科目时
    * 或者当借方录入了5401或其子科目同时贷方录入了1405或其子科目时
    * 都会触发该接口
    * subject   贷方科目的前四位，根据他来找出所有以subject开头的科目，在凭证科目表中  t_accountant_voucher_subject
    * */
    @RequestMapping("/getSubjects.do")
    public void getSubjects(User user,HttpServletResponse response,
                                 String subject) {

//        User user = (User) session.getAttribute("user");
//        int org = user.getOid();
//        Integer org = (Integer) session.getAttribute("oid");
        Integer org = user.getOid();
        TAccountantSubjectEntity param = new TAccountantSubjectEntity();
        param.setSubject(subject);
        param.setOrg(org);
        JSONObject result = accountantInputService.getSubjects(param);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

}
