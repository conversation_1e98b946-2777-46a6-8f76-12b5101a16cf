<template>
  <div id="orgListCon" class="orgListCon" >
    <div v-if="moreVisible" class="moreVisible">
      <div class="leftC">
        <div class="windersTxt"><span>W</span>onderss</div>
        <div class="ziTxt">欢迎使用通用框架管理系统</div>
      </div>
      <div class="orgCon1">
        <p>请选择您要进入的企业:</p>
        <ul class="clearfix agencyList_big">
          <li type="btn" data-name="login" v-for="(item, index2) in orgList" :key="index2" @click="goInOrg(item)">
            <span class="message_corner" v-if="item.msgCount">({{ item.msgCount }})</span>
            <span class="mobile">{{ item.oidName }}</span>
          </li>
        </ul>
        <TyPage
            v-if="orgPage"
            :curPage="orgPageInfo.currentPageNo"
            :pageSize="orgPageInfo.pageSize"
            :allPage="orgPageInfo.totalPage"
            :pageClickFun="orgPageClick"
        ></TyPage>
        <div>
          <div class="logout_btn" @click="goOutBtn">退出登录</div>
        </div>

      </div>
    </div>
    <div class="main_avatar" v-else>
      <div class="logo_avatar">
        <div class="logo"><span class="logo_w">W</span>onderss</div>
        <div class="logo_welcome">欢迎使用通用框架管理系统</div>
      </div>
      <div class="login_login_avatar">
        <div class="login_avatar">
          <div class="orgList">
            <p class="logSuccess">登录成功</p>
            <p class="colorOrange">上一次登陆时间: 登录地:</p>
            <p class="colorBlue">请选择您要进入的企业：</p>
            <ul class="ulcc">
              <div v-for="(item, index) in orgList" :key="index">
                <li class="orgItem" v-if="index < Math.min(orgList.length,9)" @click="goInOrg(item)">
                  <span>{{ item.oidName }}</span>
                  <span v-if="item.msgCount > 0" class="ty-right">({{ item.msgCount }})</span>
                </li>
              </div>
              <li class="orgItem" v-if="orgList.length > 9">
                <div @click="showMore"><span>更多</span></div>
              </li>
              <div>
                <div class="logout_btn" @click="goOutBtn">退出登录</div>
              </div>
              <div style="text-align: right;">
                <a class="ty-linkBtn" href="http://www.btransmission.com/" target="_blank">关于我们</a>
              </div>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@media screen and (min-width: 2560px) {
  #orgListCon{
    display: flex;
    align-items: center;
  }
}
#orgListCon {
  background-color: #1c2232 ;
  background-image: url('../assets/img/bg.png') ;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  overflow-y: auto;
  height: 100%;
  .main_avatar{
    width: 960px;
    margin: 130px auto;
    display: flex;
    .logo_avatar{
      padding-top: 48px;
      width: 450px;
      flex: none;
      .logo{
        color: #fff;
        font-size: 90px;
        line-height: 90px;
        margin: 0;
        padding: 0;
        .logo_w{
          color: $ty-color-green;
        }
      }
      .logo_welcome{
        color: #9aa4a3;
        font-size: 36px;
        line-height: 36px;
      }
    }
    .login_login_avatar{
      flex: auto;
      .login_avatar{
        width: 380px;
        margin: 0 auto;
        background-color: #fbfafa;
        border-radius: 2px;
        padding: 20px 40px;
        box-shadow: 0 0 6px #242626;
        border: 2px solid #fff;
        .logSuccess{
          color: #3cb0a7; font-size: 20px;
        }
        .colorBlue{
          color: #5d9cec; font-size: 0.9em;
        }
        .colorOrange{
          color: #f58410;
          font-size: 0.9em;
        }
        .ulcc{
          padding-right: 80px;
          margin: 20px 0;
          width: 100%;
        }
        .orgItem{
          border-bottom: 1px solid #2fa59c;
          background-color: $ty-color-green;
          color: #fff;
          margin: 0;
          font-size: 14px;
          padding: 10px 20px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
          &:hover{
            background:#3cb0a7ed ;
          }
        }
        .logout_btn:hover{
          background-color: #bbb;
        }
        .logout_btn{
          display: block; text-align: center; line-height: 40px;
          width: 100%; background-color:$ty-color-gray;  color: #999;
          margin:8px 0 8px 0;
          font-weight: bold; cursor: default;
        }
      }
    }
    .login_btn{
      width: 100%;
      background: $ty-color-green;
      border-radius: 3px;
      text-align: center;
      padding: 12px 0;
      color: #fff;
      &:hover{
        background: $ty-color-green;
      }
    }
  }
  .moreVisible{
    padding:20px 50px;
    .leftC{
      padding: 0 0 20px 0;
      text-align: left;
      .windersTxt{
        color: #fff;
        font-size: 48px;
        line-height: 48px;
        &>span{
          color: #3cb0a7;
        }
      }
      .ziTxt{
        color: #9aa4a3;
        font-size: 20px;
        line-height: 20px;
      }

    }
    .orgCon1{
      background-color: #fbfafa;
      border-radius: 2px;
      padding: 20px 40px;
      box-shadow: 0 0 6px #242626;
      border: 2px solid #fff;
      margin-top: 8px;
    }
    .message_corner{
      float: right;
    }
    ul.agencyList_big {
      display: flex; justify-content: space-between; flex-wrap: wrap; padding: 0;
      li{
        flex-grow: 1; background-color: #3cb0a7;color: #fff;font-size: 14px;padding: 10px 20px;
        overflow: hidden; white-space: nowrap; text-overflow: ellipsis; cursor: pointer; width: 250px; margin: 5px; border-radius: 2px;
      }
    }
    .logout_btn:hover{
      background-color: #bbb;
    }
    .logout_btn{
      display: block; text-align: center; line-height: 40px;
      width: 100%; background-color:$ty-color-gray;  color: #999;
      margin:20px 0 30px 0; border-radius: 4px;
      font-weight: bold; cursor: default;
      letter-spacing: 10px;
    }


  }
}
@media screen and (min-width: 1900px) {
  #start{
    display: flex;
    align-items: center;
  }
}
</style>
<script lang="js">
import {organizationList, sureLogin} from '@/api/api'
import sphdSocket from '@/sys/sphd'
import operaMixin from '@/mixins/mainOperating.js'

export default {
  mixins: [operaMixin],
  data() {
    return {
      heightClent: 0,
      moreVisible: false,
      user: null,
      orgList: [],
      orgPage:true,
      orgPageInfo:null,
      error: ''
    }
  },
  components: {
  },
  mounted() {
    let height = Math.max(document.documentElement.clientHeight, document.body.clientHeight);
    console.log(height);
    this.heightClent = height;
    sphdSocket.unsubscribeAll(sphdSocket.level.user)
    this.getOrgList()

  },
  methods: {
    goOutBtn(){
      this.logout()
    },
    orgPageClick(pageInfo){
      this.getAgencyList(pageInfo.page)
    },
    showMore(){
      this.moreVisible = true
      this.getAgencyList(1)
    },
    getOrgList(){
      let data = {
        currentPageNo: 0,
        pageSize: 10
      }
      this.organizationListFun(data)
    },
    getAgencyList(currentPage){
      let perNum = 10
      if(currentPage>0) {
        var w = parseInt(window.innerWidth * 0.9 / 250);
        var h = parseInt(window.innerHeight * 0.3 / 40);
        perNum = w * h;
      } else {
        perNum =10
      }
      let data = {
        currentPageNo: currentPage,
        pageSize: perNum
      }
      this.organizationListFun(data)
    },
    async organizationListFun(data){
      this.orgPage = false
      let listRes = await organizationList(data) //orgCode:'liveHelper'，只获取直播助手的机构,
      let listData = listRes.data.data //JsonResult 格式返回，多取一层data
      console.log('organizationList', listData)
      this.orgList = listData
      this.orgPageInfo = listRes.data.page
      this.orgPage = true

    },
    goCaoZuo(){
      this.$router.push('operatInstruction')
    },
    async goInOrg(item) {
      let slRes = await sureLogin(item)
      //更新sphdSocket中的对象，用于兼容message小窗的sphdSocket.user .org代码
      // console.log('goInOrg slRes', slRes)
      sphdSocket.reflushAuth()
      // console.log('goInOrg sphdSocket', sphdSocket)
      let slData = slRes.data
      // console.log('sureLogin', slData)
      if (slData.error != undefined) {
        console.log('进入机构失败！！', slData.error)
        // this.$message.error(slData.error)
        this.$alert(slData.error, '提示', {
          confirmButtonText: '确定',
        })
        // this.$router.push('errPage')
      }else{
        // this.$router.push('mainPage')
        this.$router.push('desktop')
      }
    },


  }
}
</script>
