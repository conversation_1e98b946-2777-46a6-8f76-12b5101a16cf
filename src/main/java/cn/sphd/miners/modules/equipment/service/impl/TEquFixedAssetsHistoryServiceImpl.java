package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquFixedAssetsHistory;
import cn.sphd.miners.modules.equipment.mapper.TEquFixedAssetsHistoryMapper;
import cn.sphd.miners.modules.equipment.service.ITEquFixedAssetsHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
@Transactional
public class TEquFixedAssetsHistoryServiceImpl implements ITEquFixedAssetsHistoryService
{
    @Autowired
    private TEquFixedAssetsHistoryMapper tEquFixedAssetsHistoryMapper;

    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TEquFixedAssetsHistory selectTEquFixedAssetsHistoryById(Long id)
    {
        return tEquFixedAssetsHistoryMapper.selectTEquFixedAssetsHistoryById(id);
    }

    /**
     * 查询列表
     * 
     * @param tEquFixedAssetsHistory 
     * @return 
     */
    @Override
    public List<TEquFixedAssetsHistory> selectTEquFixedAssetsHistoryList(TEquFixedAssetsHistory tEquFixedAssetsHistory)
    {
        return tEquFixedAssetsHistoryMapper.selectTEquFixedAssetsHistoryList(tEquFixedAssetsHistory);
    }

    /**
     * 新增
     * 
     * @param tEquFixedAssetsHistory 
     * @return 结果
     */
    @Override
    public int insertTEquFixedAssetsHistory(TEquFixedAssetsHistory tEquFixedAssetsHistory)
    {
        return tEquFixedAssetsHistoryMapper.insertTEquFixedAssetsHistory(tEquFixedAssetsHistory);
    }

    /**
     * 修改
     * 
     * @param tEquFixedAssetsHistory 
     * @return 结果
     */
    @Override
    public int updateTEquFixedAssetsHistory(TEquFixedAssetsHistory tEquFixedAssetsHistory)
    {
        return tEquFixedAssetsHistoryMapper.updateTEquFixedAssetsHistory(tEquFixedAssetsHistory);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTEquFixedAssetsHistoryByIds(String ids)
    {
        return tEquFixedAssetsHistoryMapper.deleteTEquFixedAssetsHistoryByIds(ids.split(","));
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTEquFixedAssetsHistoryById(Long id)
    {
        return tEquFixedAssetsHistoryMapper.deleteTEquFixedAssetsHistoryById(id);
    }
}
