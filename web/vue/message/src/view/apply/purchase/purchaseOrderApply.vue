<template>
  <div id="purchaseOrderApply">
    <TY_NavTop title="采购订单" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div>
        <a class="ui-cell" v-on:click="jump(item.purchaseOrder.id)" v-for="(item, index) in list" v-bind:key="index">
          <div class="orderItem">
            <div><div>{{item.purchaseOrder.supplierName}}</div><div>{{item.purchaseOrder.type | formatType}}</div></div>
            <div><div>订单内的材料：{{item.typeKind}}种</div><div>{{item.purchaseOrder.createName}} {{item.purchaseOrder.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div></div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style>
  #purchaseOrderApply .container>div>a{  background: #fff; }
  #purchaseOrderApply .container>div>a:nth-child(odd){ background:#f3f3f3;  }
  .orderItem{ width: 100%; font-size:1em;   }
  .orderItem:nth-child(even){ background: #eee; }
  .orderItem>div{ display: flex; }
  .orderItem>div>div{ flex:1; }
  .orderItem>div>div:nth-child(2){ text-align: right; margin-left: -30px;  font-size: 0.7em; }

</style>
<script>
var that
export default {
  name: 'purchaseOrderApply',
  data () {
    return {
      list: [],
      listenersUid: []
    }
  },
  filters: {
    formatType: function (type) {
      let str = ''
      switch (type) {
        case '1':
        case 1:
          str = '新单'
          break
        case '2':
        case 2:
          str = '预警'
          break
        case '3':
        case 3:
          str = '个人申购的新材料'
          break
        case '4':
        case 4:
          str = '补货'
          break
        case '5':
        case 5:
          str = '零星采购'
          break
        default:
      }
      return str
    }
  },
  computed: {
  },
  created () {
    that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('purchaseOrderApplyHandle', function (data) {
        let listData = JSON.parse(data)
        that.list = listData.listMap
        console.log('采购申请列表返回值:')
        console.log(listData)
      }),
      this.sphdSocket.subscribe('purchaseOrderApplyHandle', function (data) {
        console.log('采购申请列表返回值user:')
        let getData = JSON.parse(data)
        console.log(getData)
        let poOrders = getData.poOrders
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.list.unshift(poOrders)
        } else if (operate < 0) {
          let orderId = poOrders.purchaseOrder.id
          let _index = -1
          that.list.forEach(function (item, index) {
            if (item.purchaseOrder.id === orderId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.list.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    console.log(userId)
    console.log(session)
    let json = { userId: userId, session: session }
    console.log(JSON.stringify(json))
    this.sphdSocket.send('getPurchaseOrderApplication', json)
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      window.parent.floatToPage('../../../purchaseOrderApproval/purchasePage.do', { 'id': id, 't': 0 })
    },
    search: function () {
      this.$router.push({
        path: `/purchaseOrderSearch/0`
      })
    }
  }
}
</script>
