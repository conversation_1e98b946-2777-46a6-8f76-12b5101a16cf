package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.generalAffairs.dao.*;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.personal.dao.PersonnelFolksDao;
import cn.sphd.miners.modules.personal.entity.PersonnelFolks;
import cn.sphd.miners.modules.system.entity.Offer;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/8/17.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PersonalServiceImpl implements PersonalService{
    @Autowired
    PersonnelOccupationDao personnelOccupationDao;
    @Autowired
    PersonalEducationDao personalEducationDao;
    @Autowired
    PersonnelSalaryLogDao personnelSalaryLogDao;
    @Autowired
    PersonalRewardPunishmentDao personalRewardPunishmentDao;
    @Autowired
    PersonalAssessmentDao personalAssessmentDao;
    @Autowired
    PersonnelFolksDao personnelFolksDao;
    @Autowired
    PersonnelOccupationHistoryDao personnelOccupationHistoryDao;
    @Autowired
    PersonnelEducationHistoryDao personnelEducationHistoryDao;
    @Autowired
    UserContactDao userContactDao;
    @Autowired
    UserService userService;
    @Autowired
    SWMessageService swMessageService;


    @Override
    public void savePersonnelOccupation(PersonnelOccupation personnelOccupation) {
         personnelOccupationDao.save(personnelOccupation);
    }

    @Override
    public void updatePersonnelOccupation(PersonnelOccupation personnelOccupation) {
         personnelOccupationDao.update(personnelOccupation);
    }

    @Override
    public PersonnelOccupation getPersonnelOccupationById(Integer id) {
        return personnelOccupationDao.get(id);
    }

    @Override
    public List<PersonnelOccupation> getPersonnelOccupationByUserId(Integer userId) {
        String condition = "from PersonnelOccupation o where o.user.userID="+userId+" order by id asc";
        return personnelOccupationDao.getListByHQL(condition);
    }

    @Override
    public void deletePersonnelOccupation(PersonnelOccupation personnelOccupation) {
        personnelOccupationDao.delete(personnelOccupation);
    }

    @Override
    public PersonalEducation getPersonalEducationById(Integer id) {
        return personalEducationDao.get(id);
    }

    @Override
    public List<PersonalEducation> getPersonalEducationByUserId(Integer id) {
        String condition = "from PersonalEducation o where o.user.userID="+id+" order by id asc";
        return personalEducationDao.getListByHQL(condition);
    }

    @Override
    public void savePersonalEducation(PersonalEducation personalEducation) {
        personalEducationDao.save(personalEducation);
    }

    @Override
    public void updatePersonalEducation(PersonalEducation personalEducation) {
        personalEducationDao.update(personalEducation);
    }

    @Override
    public void deletePersonalEducation(PersonalEducation personalEducation) {
        personalEducationDao.delete(personalEducation);
    }

    @Override
    public PersonnelSalaryLog getPersonnelSalaryLogById(Integer id) {
        return personnelSalaryLogDao.get(id);
    }

    @Override
    public List<PersonnelSalaryLog> getPersonnelSalaryLogByUserId(Integer id) {
        String condition = "from PersonnelSalaryLog o where o.user.userID="+id+" order by id asc";
        return personnelSalaryLogDao.getListByHQL(condition);
    }

    @Override
    public void savePersonnelSalaryLog(PersonnelSalaryLog personnelSalaryLog) {
        personnelSalaryLogDao.save(personnelSalaryLog);
    }

    @Override
    public void updatePersonnelSalaryLog(PersonnelSalaryLog personnelSalaryLog) {
        personnelSalaryLogDao.update(personnelSalaryLog);
    }

    @Override
    public void deletePersonnelSalaryLog(PersonnelSalaryLog personnelSalaryLog) {
        personnelSalaryLogDao.delete(personnelSalaryLog);
    }

    @Override
    public PersonalRewardPunishment getPersonalRewardPunishmentById(Integer id) {
        return personalRewardPunishmentDao.get(id);
    }

    @Override
    public List<PersonalRewardPunishment> getPersonalRewardPunishmentByUserId(Integer id) {
        String hql="from PersonalRewardPunishment o where o.user.userID="+id+" order by id asc";
        return personalRewardPunishmentDao.getListByHQL(hql);
    }

    @Override
    public void savePersonalRewardPunishment(PersonalRewardPunishment personalRewardPunishment) {
        personalRewardPunishmentDao.save(personalRewardPunishment);
    }

    @Override
    public void updatePersonalRewardPunishment(PersonalRewardPunishment personalRewardPunishment) {
        personalRewardPunishmentDao.update(personalRewardPunishment);
    }

    @Override
    public void deletePersonalRewardPunishment(PersonalRewardPunishment personalRewardPunishment) {
        personalRewardPunishmentDao.delete(personalRewardPunishment);
    }

    @Override
    public PersonalAssessment getPersonalAssessmentById(Integer id) {
        return personalAssessmentDao.get(id);
    }

    @Override
    public List<PersonalAssessment> getPersonalAssessmentByUserId(Integer id) {
        String hql="from PersonalAssessment o where o.user.userID="+id+" order by id asc";
        return personalAssessmentDao.getListByHQL(hql);
    }

    @Override
    public void savePersonalAssessment(PersonalAssessment personalAssessment) {
        personalAssessmentDao.save(personalAssessment);
    }

    @Override
    public void updatePersonalAssessment(PersonalAssessment personalAssessment) {
        personalAssessmentDao.update(personalAssessment);
    }

    @Override
    public void deletePersonalAssessment(PersonalAssessment personalAssessment) {
        personalAssessmentDao.delete(personalAssessment);
    }

    @Override
    public List<PersonnelFolks> getFolksByUserId(Integer userId) {
        String hql = " from PersonnelFolks o where o.userId = "+userId+" order by id asc";
        return personnelFolksDao.getListByHQL(hql);
    }

    @Override
    public void savePersonnelFolks(PersonnelFolks personnelFolks) {
        personnelFolksDao.save(personnelFolks);
    }

    @Override
    public PersonnelFolks getFolkById(Integer id) {
        return personnelFolksDao.get(id);
    }

    @Override
    public void updatePersonnelFolks(PersonnelFolks personnelFolks) {
        personnelFolksDao.update(personnelFolks);
    }

    @Override
    public void deletePersonnelFolks(PersonnelFolks personnelFolks) {
        personnelFolksDao.delete(personnelFolks);
    }

    @Override
    public void addOrUpdatePersonnelOccupation(User user,User loginUser, Integer id, String beginTime, String endTime, String corpName, String corpSize, String corpNature, String corpDepartment, String post, String jobDesc, String memo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");

        PersonnelOccupation personnelOccupation=new PersonnelOccupation();
        if (id==null) {
            personnelOccupation.setCreator(loginUser.getUserID());
            personnelOccupation.setCreateName(loginUser.getUserName());
            personnelOccupation.setCreateDate(new Date());
            personnelOccupation.setUser(user);
            personnelOccupation.setVersionNo(0);//版本号
        }else {
            personnelOccupation = personnelOccupationDao.get(id);
            this.addPersonnelOccupationHistory(personnelOccupation,true);// 产生历史记录
            if ("1".equals(user.getSubmitState()))
                personnelOccupation.setVersionNo(personnelOccupation.getVersionNo()==null?1:personnelOccupation.getVersionNo()+1);
            else
                personnelOccupation.setVersionNo(0);
        }
        try {
            if (!StringUtil.isNullOrEmpty(beginTime))
                personnelOccupation.setBeginTime(sdf.parse(beginTime));
            else
                personnelOccupation.setBeginTime(null);

            if (!StringUtil.isNullOrEmpty(endTime))
                personnelOccupation.setEndTime(sdf.parse(endTime));
            else
                personnelOccupation.setEndTime(null);

        } catch (ParseException e) {
            e.printStackTrace();
        }

        personnelOccupation.setCorpName(corpName);//公司名
        personnelOccupation.setCorpSize(corpSize);//公司规模
        personnelOccupation.setCorpNature(corpNature);//公司性质
        personnelOccupation.setCorpDepartment(corpDepartment);//所在部门
        personnelOccupation.setPost(post);//职位
        personnelOccupation.setJobDesc(jobDesc);//工作描述
        personnelOccupation.setMemo(memo);//说明
        personnelOccupation.setUpdator(loginUser.getUserID());
        personnelOccupation.setUpdateName(loginUser.getUserName());
        personnelOccupation.setUpdateDate(new Date());
        personnelOccupationDao.saveOrUpdate(personnelOccupation);

        //添加最新产生 的历史记录
        this.addPersonnelOccupationHistory(personnelOccupation,false);

    }

    //补系统之前版本产生历史记录原数据 lixu
    @Override
    public void addPersonnelOccupationHistory(PersonnelOccupation personnelOccupation,boolean check){
        PersonnelOccupationHistory p=null;
        if (check) {// 需要查
            String hql = " from PersonnelOccupationHistory where occupationId=:occupationId order by id desc";
            Map<String, Object> map = new HashMap<>();
            map.put("occupationId", personnelOccupation.getId());
            p = (PersonnelOccupationHistory) personnelOccupationDao.getByHQLWithNamedParams(hql, map);
        }
        if (p==null) {
            if ("1".equals(personnelOccupation.getUser().getSubmitState())) {
                PersonnelOccupationHistory personnelOccupationHistory = new PersonnelOccupationHistory();
                BeanUtils.copyProperties(personnelOccupation, personnelOccupationHistory);
                personnelOccupationHistory.setUserId(personnelOccupation.getUser_());
                personnelOccupationHistory.setOccupationId(personnelOccupation.getId());
                personnelOccupationHistoryDao.save(personnelOccupationHistory);
            }
        }
    }

    @Override
    public void addOrUpdatePersonalEducation(User user,User loginUser, Integer id, String beginTime, String endTime, String collegeName, String departmentName, String major, String degreeDesc, String majorDesc, String memo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM");
        PersonalEducation personalEducation = new PersonalEducation();
        if (id==null) {
            personalEducation.setCreator(loginUser.getUserID());
            personalEducation.setCreateName(loginUser.getUserName());
            personalEducation.setCreateDate(new Date());
            personalEducation.setUser(user);
            personalEducation.setVersionNo(0);//版本号
        }else {
            personalEducation = personalEducationDao.get(id);
            this.addPersonnelEducationHistory(personalEducation,true);// 产生历史记录
            if ("1".equals(user.getSubmitState()))
                personalEducation.setVersionNo(personalEducation.getVersionNo()==null?1:personalEducation.getVersionNo()+1);
            else
                personalEducation.setVersionNo(0);
        }
        try {
            if (!StringUtil.isNullOrEmpty(beginTime))
                personalEducation.setBeginTime(sdf.parse(beginTime));
            else
                personalEducation.setBeginTime(null);

            if (!StringUtil.isNullOrEmpty(endTime))
                personalEducation.setEndTime(sdf.parse(endTime));
            else
                personalEducation.setEndTime(null);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        personalEducation.setCollegeName(collegeName);//毕业院校
        personalEducation.setDepartmentName(departmentName);//院系
        personalEducation.setMajor(major);//专业
        personalEducation.setDegreeDesc(degreeDesc);//学历/学位
        personalEducation.setMajorDesc(majorDesc);//专业描述
        personalEducation.setMemo(memo);//说明
        personalEducation.setUpdator(loginUser.getUserID());
        personalEducation.setUpdateName(loginUser.getUserName());
        personalEducation.setUpdateDate(new Date());
        personalEducationDao.saveOrUpdate(personalEducation);

        this.addPersonnelEducationHistory(personalEducation,false);// 添加最新历史记录

    }

    @Override
    public void addPersonnelEducationHistory(PersonalEducation personalEducation,boolean check){
        PersonnelEducationHistory p=null;
        if (check) {// 需要查
            String hql = " from PersonnelEducationHistory where educationId=:educationId order by id desc";
            Map<String, Object> map = new HashMap<>();
            map.put("educationId", personalEducation.getId());
            p = (PersonnelEducationHistory) personnelEducationHistoryDao.getByHQLWithNamedParams(hql, map);
        }
        if (p==null) {
            if ("1".equals(personalEducation.getUser().getSubmitState())) {
                p = new PersonnelEducationHistory();
                BeanUtils.copyProperties(personalEducation, p);
                p.setUserId(personalEducation.getUser_());
                p.setEducationId(personalEducation.getId());
                personnelEducationHistoryDao.save(p);
            }
        }
    }

    @Override
    public List<PersonnelOccupationHistory> getPersonnelOccupationHistoriesById(Integer occupationId, PageInfo pageInfo) {
        String hql=" from PersonnelOccupationHistory where occupationId=:occupationId";
        Map<String,Object> map=new HashMap<>();
        map.put("occupationId",occupationId);
        List<PersonnelOccupationHistory> pList=personnelOccupationHistoryDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        return pList;
    }

    @Override
    public List<PersonnelEducationHistory> getPersonnelEducationHistoriesById(Integer educationId, PageInfo pageInfo) {
        String hql=" from PersonnelEducationHistory where educationId=:educationId";
        Map<String,Object> map=new HashMap<>();
        map.put("educationId",educationId);
        List<PersonnelEducationHistory> pList=personnelEducationHistoryDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        return pList;
    }

    @Override
    public PersonnelOccupationHistory getPersonnelOccupationHistoryById(Integer id) {
        return personnelOccupationHistoryDao.get(id);
    }

    @Override
    public PersonnelEducationHistory getPersonnelEducationHistoryById(Integer id) {
        return personnelEducationHistoryDao.get(id);
    }

    @Override
    public List<UserContact> getUserContactListByUserId(Integer userId) {
        String hql=" from UserContact where userId=:userId";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        List<UserContact> userContactList=userContactDao.getListByHQLWithNamedParams(hql,map);
        return userContactList;
    }

    @Override
    public void offerRejectSend(int loginNum, int operate, Offer offer, Integer toUserId, String pass, String title, String content, String tCode) {
        System.out.println("招聘推送开始："+new Date());
        System.out.println("招聘id："+offer.getUserID()+" userId："+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("offer", offer);
        User approvalUser= userService.getUserByID(toUserId);
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,approvalUser,tCode);
//        clusterMessageSendingOperations.convertAndSendToUser(toUserId.toString(),pass,JSON.toJSONString(hashMap));
        System.out.println("招聘推送结束："+new Date());
    }
}
