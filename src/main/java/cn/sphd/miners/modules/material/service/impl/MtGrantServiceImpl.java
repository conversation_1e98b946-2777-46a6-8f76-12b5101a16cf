package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.inv.dao.InvWarehouseBaseDao;
import cn.sphd.miners.modules.inv.entity.InvWarehouseBase;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MtGrantService;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserPopedomService;

import com.alibaba.fastjson.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("mtGrantService")
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class MtGrantServiceImpl implements MtGrantService {

    @Autowired
    private MtGrantRequisitionDao mtGrantRequisitionDao;
    @Autowired
    private MtCategoryDao categoryDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private MtOutApplicationDao mtOutApplicationDao;
    @Autowired
    private MtOutApplicationItemDao mtOutApplicationItemDao;
    @Autowired
    private MtStockInfoDao mtStockInfoDao;
    @Autowired
    private ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    private SWMessageService swMessageService;
    @Autowired
    private UserPopedomService userPopedomService;
    @Autowired
    UserSuspendMsgService suspendMsgService;
    @Autowired
    private InvWarehouseBaseDao warehouseBaseDao;
    @Autowired
    private MtLocationDao mtLocationDao;
    @Autowired
    private MtInApplicationItemDao mtInApplicationItemDao;
    @Autowired
    private MtInApplicationDao mtInApplicationDao;
    @Autowired
    MtService mtService;
    @Autowired
    private MtBaseDao mtBaseDao;

    @Override
    public List<MtGrantRequisition> getListByUser(Integer userId) {
        return mtGrantRequisitionDao.getListByHQL("from MtGrantRequisition where user=" + userId + " and enabled=1");
    }

    @Override
    public List<Map<String, Object>> getMtList(Integer oid, Integer userId) {
        String hql = "SELECT DISTINCT c.id, c.name, c.parent, c.level, c.org FROM t_mt_category c LEFT JOIN t_mt_base mb ON mb.category = c.id WHERE c.org =?0 AND c.`name` NOT in ('商品','半成品','办公用品')";
        List<Map<String, Object>> categories = (List<Map<String, Object>>) categoryDao.findMapByConditionNoPage(hql, new Object[]{oid}).get("data");

        if (categories == null) {
            categories = new ArrayList<>();
        }

//        String sql2="select mb.id,mb.`name`,mb.category from t_mt_base mb\n" +
//                "where mb.id in (select gr.material from t_mt_grant_requisition gr where gr.user=?0 )";
        String sql2 = "select mb.id,mb.`name`,mb.category,(select COUNT(1) from t_mt_grant_requisition  gr where gr.material=mb.id and gr.user=?0) isHave from t_mt_base mb\n" +
                "where mb.org=?1 and mb.enabled='1' and ifnull(mb.enabled,0)!=0 and (( IFNULL(mb.is_purchased,0)=0 AND ifnull(mb.is_appointed, 0) != 0) OR ( (IFNULL(mb.is_purchased,0)=1 AND ifnull(mb.is_appointed, 0) != 0  AND ifnull( mb.operation, 0 ) in (7,8,9)) ))";
        List<Map<String, Object>> list = (List<Map<String, Object>>) categoryDao.findMapByConditionNoPage(sql2, new Object[]{userId, oid}).get("data");
        if (list == null) {
            list = new ArrayList<>();
        }
        List<Map<String, Object>> l2 = new ArrayList<>();
        for (Map<String, Object> m : categories) {
            if (m.get("level").toString().equals("1")) {

                setSub(m, categories, list);
                l2.add(m);
            }
        }
        List<Map<String, Object>> haveList = new ArrayList<>();
        //剔除没有材料的分类
        for (Map<String, Object> m : l2) {
            Integer i = 0;
            Integer res = checkNum(m, i);

            if (res != 0) {
                haveList.add(m);
            }
        }
        return haveList;
    }

    @Override
    public List<Map<String, Object>> getAllMtList(Integer oid) {
        String hql="select new Map(id as id,code as code,name as name,model as model,specifications as specifications,unit as unit,expRequired as expRequired) from MtBase where enabled=1 and org="+oid;
        return mtBaseDao.getListByHQLWithNamedParams(hql,new HashMap<>());
    }

    @Override
    public List<Map<String, Object>> getAllMtList(Integer oid,Integer userId) {
                String sql2="id as id,code as code,name as name,model as model,specifications as specifications,unit as unit,expRequired as expRequired from t_mt_base mb\n" +
                "where mb.id in (select gr.material from t_mt_grant_requisition gr where gr.user=?0 )";
        List<Map<String, Object>> list = (List<Map<String, Object>>) categoryDao.findMapByConditionNoPage(sql2, new Object[]{userId}).get("data");
        if (list == null) {
            list = new ArrayList<>();
        }

        return list;
    }


    @Override
    public String submitMtGrant(String addIds, String deleteIds, Integer userID, User user) {

        if (StringUtils.isNotEmpty(deleteIds)) {
            String hql = "delete from MtGrantRequisition where user=" + userID + " and  material in (" + deleteIds + ")";
            mtGrantRequisitionDao.queryHQLWithNamedParams(hql, new HashMap<>());
        }

        if (StringUtils.isNotEmpty(addIds)) {
            for (String id : addIds.split(",")
            ) {
                String hql = "from MtGrantRequisition where user=" + userID + " and enabled=1 and material=" + id;

                MtGrantRequisition mg = mtGrantRequisitionDao.getByHQL(hql);
                if (mg == null) {
                    mg = new MtGrantRequisition();
                    mg.setCreateDate(new Date());
                    mg.setCreateName(user.getUserName());
                    mg.setCreator(user.getUserID());
                    mg.setAuthorityCode("1");
                    mg.setEnabled((byte) 1);
                    mg.setEnabledTime(new Date());
                    mg.setOperation("1");
                    mg.setVersionNo(1);
                    mg.setUser(userID);
                    mg.setMaterial(Integer.valueOf(id));
                    mtGrantRequisitionDao.save(mg);
                }

            }
        }


        return "success";
    }

    @Override
    public List<Map<String, Object>> getUserMtListTree(Integer oid, Integer userId, String mtIds, Integer warehouseId) {
        String hql = "SELECT DISTINCT c.id, c.name, c.parent, c.level, c.org FROM t_mt_category c LEFT JOIN t_mt_base mb ON mb.category = c.id WHERE c.org =?0 AND c.`name` NOT in ('商品','半成品','办公用品')";
        List<Map<String, Object>> categories = (List<Map<String, Object>>) categoryDao.findMapByConditionNoPage(hql, new Object[]{oid}).get("data");
        if (categories == null) {
            categories = new ArrayList<>();
        }

        String sql2 = "select mb.id,mb.`name`,mb.code,mb.category,mb.model,mb.specifications,(SELECT SUM(mm.minimum_stock) FROM t_mt_supplier_material mm WHERE mm.material=mb.id and ifnull(mm.enabled,0)!=0) as minimumStock,ms.current_stock currentStock,mu.name unit,wr.warehouse from t_mt_base mb\n" +
                " left join t_mt_unit mu on mb.unit_id=mu.id left JOIN t_mt_stock_info ms on ms.material = mb.id " +
                " left join  t_mt_location l on l.material=mb.id " +
                "LEFT JOIN t_inv_warehouse_location wl ON l.location = wl.id " +
                "LEFT JOIN t_inv_warehouse_shelf ws ON ws.id = wl.shelf " +
                "LEFT JOIN t_inv_warehouse_region wr ON wr.id = ws.region " +
                "where mb.org=?0 and mb.enabled='1' and mb.id in (select gr.material from t_mt_grant_requisition  gr where gr.user=?1)";
        List<Map<String, Object>> mbList = (List<Map<String, Object>>) categoryDao.findMapByConditionNoPage(sql2, new Object[]{oid, userId}).get("data");

        if(mbList==null){
            mbList=new ArrayList<>();
        }
        //获取材料所在仓库
        List<Map<String, Object>> list = new ArrayList<>();

        String sql3 = "SELECT\n" +
                " DISTINCT wr.warehouse\n" +
                "FROM\n" +
                " t_mt_location l \n" +
                "LEFT JOIN t_inv_warehouse_location wl ON l.location = wl.id\n" +
                "LEFT JOIN t_inv_warehouse_shelf ws ON ws.id = wl.shelf\n" +
                "LEFT JOIN t_inv_warehouse_region wr ON wr.id = ws.region\n" +
                "where wl.org=" + oid;
        if (StringUtils.isNotEmpty(mtIds)) {
            sql3 += " and l.material in (" + mtIds + ")";
        }
        if (warehouseId != null && warehouseId != 0) {
            sql3 += " and wr.warehouse=" + warehouseId;
        }
        //仓库列表
        List<Map<String, Object>> warehouseList = (List<Map<String, Object>>) warehouseBaseDao.findMapByConditionNoPage(sql3, new Object[]{}).get("data");
        Map map = new HashMap();

        for (Map<String, Object> mb : mbList) {
            if (map.get(mb.get("id")) != null) {
                continue;
            }

            if (mb.get("warehouse") == null) {
                list.add(mb);
                map.put(mb.get("id"), "1");
            } else {
                int i = 0;
                for (Map<String, Object> warehouse : warehouseList) {
                    if (warehouse.get("warehouse").toString().equals(mb.get("warehouse").toString())) {
                        i++;
                        break;
                    }
                }
                if (i > 0 || warehouseList.size() == 0) {
                    map.put(mb.get("id"), "1");
                    list.add(mb);
                }

            }
        }


        if (list == null) {
            list = new ArrayList<>();
        }
        List<Map<String, Object>> l2 = new ArrayList<>();
        for (Map<String, Object> m : categories) {
            if (m.get("level").toString().equals("1")) {

                setSub(m, categories, list);
                l2.add(m);
            }
        }
        List<Map<String, Object>> haveList = new ArrayList<>();
        //剔除没有材料的分类
        for (Map<String, Object> m : l2) {
            Integer i = 0;
            Integer res = checkNum(m, i);

            if (res != 0) {
                haveList.add(m);
            }
        }
        return haveList;
    }

    @Override
    public List<Map<String, Object>> getUserMtList(Integer oid, Integer userId, String mtIds, Integer category, Integer warehouseId) {
        String sql2 = "select mb.id,mb.`name`,mb.code,mb.category,mb.model,mb.specifications,(SELECT SUM(mm.minimum_stock) FROM t_mt_supplier_material mm WHERE mm.material=mb.id and ifnull(mm.enabled,0)!=0) as minimumStock,ms.current_stock currentStock,mu.name unit,wr.warehouse from t_mt_base mb\n" +
                " left join t_mt_unit mu on mb.unit_id=mu.id left JOIN t_mt_stock_info ms on ms.material = mb.id " +
                " left join  t_mt_location l on l.material=mb.id " +
                "LEFT JOIN t_inv_warehouse_location wl ON l.location = wl.id " +
                "LEFT JOIN t_inv_warehouse_shelf ws ON ws.id = wl.shelf " +
                "LEFT JOIN t_inv_warehouse_region wr ON wr.id = ws.region " +
                "where mb.org=?0 and mb.enabled='1' and mb.id in (select gr.material from t_mt_grant_requisition  gr where gr.user=?1)";

        if (category != null && category != 0) {
            sql2 += " and mb.category=" + category;
        }
        List<Map<String, Object>> mbList = (List<Map<String, Object>>) categoryDao.findMapByConditionNoPage(sql2, new Object[]{oid, userId}).get("data");

        if (mbList == null) {
            mbList = new ArrayList<>();
        }
        //获取材料所在仓库
        List<Map<String, Object>> list = new ArrayList<>();

        String sql3 = "SELECT\n" +
                " DISTINCT wr.warehouse\n" +
                "FROM\n" +
                " t_mt_location l \n" +
                "LEFT JOIN t_inv_warehouse_location wl ON l.location = wl.id\n" +
                "LEFT JOIN t_inv_warehouse_shelf ws ON ws.id = wl.shelf\n" +
                "LEFT JOIN t_inv_warehouse_region wr ON wr.id = ws.region\n" +
                "where wl.org=" + oid;
        if (StringUtils.isNotEmpty(mtIds)) {
            sql3 += " and l.material in (" + mtIds + ")";
        }
        if (warehouseId != null && warehouseId != 0) {
            sql3 += " and wr.warehouse=" + warehouseId;
        }
        //仓库列表
        List<Map<String, Object>> warehouseList = (List<Map<String, Object>>) warehouseBaseDao.findMapByConditionNoPage(sql3, new Object[]{}).get("data");

        if (warehouseList == null) {
            warehouseList = new ArrayList<>();
        }

        Map map = new HashMap();
        for (Map<String, Object> mb : mbList) {

            if (map.get(mb.get("id")) != null) {
                continue;
            }
            if (mb.get("warehouse") == null) {
                list.add(mb);
                map.put(mb.get("id"), "1");
            } else {
                int i = 0;
                for (Map<String, Object> warehouse : warehouseList) {
                    if (warehouse.get("warehouse").toString().equals(mb.get("warehouse").toString())) {
                        i++;
                        break;
                    }
                }
                if (i > 0 || warehouseList.size() == 0) {
                    map.put(mb.get("id"), "1");
                    list.add(mb);
                }

            }
        }

        return list;
    }

    @Override
    public List<Map<String, Object>> selectWarehouse(String mtList) {
        int size=mtList.split(",").length-1;
        String sql3 = "select count(s.location ) num,s.warehouseCode,s.warehouseName,s.id from (SELECT DISTINCT\n" +
                "   wb.id as id,wb.warehouse_code as warehouseCode,wb.warehouse_name as warehouseName,l.location,l.material\n" +
                "FROM\n" +
                " t_mt_location l\n" +
                "LEFT JOIN t_inv_warehouse_location wl ON l.location = wl.id\n" +
                "LEFT JOIN t_inv_warehouse_shelf ws ON ws.id = wl.shelf\n" +
                "LEFT JOIN t_inv_warehouse_region wr ON wr.id = ws.region\n" +
                "LEFT JOIN t_inv_warehouse_base wb on wb.id=wr.warehouse where l.material in (" + mtList + ")) s\n" +
                "GROUP BY s.location\n" +
                "HAVING num>"+size;
        //仓库列表
        List<Map<String, Object>> warehouseList = (List<Map<String, Object>>) warehouseBaseDao.findMapByConditionNoPage(sql3, new Object[]{}).get("data");

        if (warehouseList == null) {
            warehouseList = new ArrayList<>();
        }
        return warehouseList;
    }

    @Override
    public List<Map<String, Object>> selectLocation(Integer mtId) {
        String sql3 = "select id as id,location_code as locationCode,location_name as locationName from t_inv_warehouse_location wl\n" +
                "where wl.id in ( select ml.location from t_mt_location ml where ml.material="+mtId+")";
        //仓库列表
        List<Map<String, Object>> warehouseList = (List<Map<String, Object>>) warehouseBaseDao.findMapByConditionNoPage(sql3, new Object[]{}).get("data");

        if (warehouseList == null) {
            warehouseList = new ArrayList<>();
        }
        return warehouseList;
    }



    @Override
    public String pickingMtJudge(Integer mtId, BigDecimal num) {
        MtStockInfo mtStockInfo = mtStockInfoDao.getByHQL("from MtStockInfo where material_=" + mtId);

        if (mtStockInfo == null) {


            return "领的太多了，比当前库存还多！\n" +
                    "\n" +
                    "请重新录入！";
        }

        if (mtStockInfo.getCurrentStock() == null) {

            return "领的太多了，比当前库存还多！\n" +
                    "\n" +
                    "请重新录入！";
        }
        if (mtStockInfo.getCurrentStock().compareTo(num) == -1) {

            return "领的太多了，比当前库存还多！\n" +
                    "\n" +
                    "请重新录入！";
        }
        return "success";
    }

    @Override
    @Transactional
    public String pickingMtApply(User user, String mtList, String purpose, String timePlan, Long durarionFact, Integer warehouseId) {

        List<MtOutApplication> mo = mtOutApplicationDao.getListByHQL("from MtOutApplication where timePlan='" + timePlan + "'");
        if (mo.size() > 0) {
            return "领料时间冲突！\n" +
                    "\n" +
                    "请重新录入！";
        }

        SimpleDateFormat sdfTime = new SimpleDateFormat(
                "yyyy-MM-dd HH:mm:ss");
        JSONArray jsonArray = JSONArray.fromObject(mtList);
        MtOutApplication mtOutApplication = new MtOutApplication();
        mtOutApplication.setOrg(user.getOid());
        mtOutApplication.setCreateDate(new Date());
        mtOutApplication.setCreator(user.getUserID());
        mtOutApplication.setCreateName(user.getUserName());
        mtOutApplication.setApplicant(user.getUserID());
        mtOutApplication.setApplicantName(user.getUserName());
        mtOutApplication.setApplyTime(new Date());
        mtOutApplication.setApplyType("2");
        mtOutApplication.setItemPlan(Long.valueOf(jsonArray.size()));
        mtOutApplication.setApprovalStatus("2");
        mtOutApplication.setOperation("1");
        mtOutApplication.setVersionNo(1);
        mtOutApplication.setPurpose(purpose);
        mtOutApplication.setMaterialWarehouse(warehouseId);
        try {
            mtOutApplication.setTimePlan(sdfTime.parse(timePlan));
        } catch (ParseException e) {
            return "请录入正确的领料时间";
        }
        mtOutApplication.setDurarionPlan(durarionFact);
        mtOutApplicationDao.save(mtOutApplication);
        mtOutApplication.setSn(getSn(mtOutApplication.getId().toString()));
        mtOutApplicationDao.update(mtOutApplication);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);

            Integer mtId = object.optInt("id");
            BigDecimal num = new BigDecimal(object.optString("num", "0"));
            MtStockInfo mtStockInfo = mtStockInfoDao.getByHQL("from MtStockInfo where material_=" + mtId);
            if (mtStockInfo == null) {
                throw new RuntimeException("领的太多了，比当前库存还多！\n" +
                        "\n" +
                        "请重新录入！");
            }
            if (mtStockInfo.getCurrentStock() == null) {
                throw new RuntimeException("领的太多了，比当前库存还多！\n" +
                        "\n" +
                        "请重新录入！");
            }
            if (mtStockInfo.getCurrentStock().compareTo(num) == -1) {
                throw new RuntimeException("领的太多了，比当前库存还多！\n" +
                        "\n" +
                        "请重新录入！");
            }
            MtOutApplicationItem item = new MtOutApplicationItem();
            item.setOrg(user.getOid());
            item.setCreateDate(new Date());
            item.setCreator(user.getUserID());
            item.setCreateName(user.getUserName());
            item.setOperation("1");
            item.setVersionNo(1);
            item.setMaterial(mtId);
            item.setQuantityPlan(num);
            item.setLockedQuantity(num);
            item.setApprovalStatus("2");
            item.setState("0");
            item.setApplication(mtOutApplication.getId());
            mtOutApplicationItemDao.save(item);
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("outApplication", mtOutApplication);
        hashMap.put("id", mtOutApplication.getId());
        //申请人
        clusterMessageSendingOperations.convertAndSendToUser(user.getUserID().toString(), "/getPickingMtApplyList", null, null, null, null, JSON.toJSONString(hashMap));
        swMessageService.rejectSend(0, 1, hashMap, user.getUserID().toString(), "/getPickingMtApplyList", null, null, user, "pickingApply");

        List<UserDto> users = userPopedomService.getUserByOidMid(user.getOid(), "xc");
        for (UserDto ud : users) {
            //审批人
            clusterMessageSendingOperations.convertAndSendToUser(ud.getUserID().toString(), "/getPickingMtApprovalList", "有一条申请待审批", user.getUserName() + "在" + DateUtil.getTime() + "提交了领料申请", user.getOid(), user.getOrganization().getName(), JSON.toJSONString(hashMap));
            User user1 = userDao.get(ud.getUserID());
            swMessageService.rejectSend(1, 1, hashMap, ud.getUserID().toString(), "/getPickingMtApprovalList", null, null, user1, "pickingApproval");

        }


        return "success";
    }


    @Override
    public List<MtOutApplication> getMtOutApplicationList(Integer userId, Integer org, String approvalStatus) {
        String hql = "from MtOutApplication where 1=1";
        if (userId != null) {
            hql += " and applicant=" + userId;
        }

        if (org != null) {
            hql += " and org=" + org;
        }

        if (StringUtils.isNotEmpty(approvalStatus)) {
            hql += " and approvalStatus='" + approvalStatus + "'";
        }
        return mtOutApplicationDao.getListByHQL(hql);
    }

    @Override
    public List<MtOutApplication> getOtherPickingPlan(Integer org, Integer materialWarehouse) {
        String hql = "from MtOutApplication where org=" + org + " and approvalStatus='2'";
        if (materialWarehouse != null && materialWarehouse.intValue() != 0) {
            hql += " and materialWarehouse=" + materialWarehouse;
        }
        hql += "  order by timePlan desc";
        return mtOutApplicationDao.getListByHQL(hql);
    }

    @Override
    public Map<String, Object> getPickingMtDetail(Integer mtOutId) {
        Map<String, Object> map = new HashMap<>();
        MtOutApplication mtOutApplication = mtOutApplicationDao.get(mtOutId);
        if (mtOutApplication != null) {
            if (mtOutApplication.getMaterialWarehouse() != null) {
                InvWarehouseBase warehouseBase = warehouseBaseDao.get(mtOutApplication.getMaterialWarehouse().longValue());
                if (warehouseBase != null) {
                    map.put("warehouseName", warehouseBase.getWarehouseName());
                } else {
                    map.put("warehouseName", "--");
                }
            } else {
                map.put("warehouseName", "--");
            }
            map.put("applicantName", mtOutApplication.getApplicantName());
            map.put("applyTime", mtOutApplication.getApplyTime());
            map.put("itemPlan", mtOutApplication.getItemPlan());
            map.put("sn", mtOutApplication.getSn());
            map.put("purpose", mtOutApplication.getPurpose());
            map.put("timePlan", mtOutApplication.getTimePlan());
            map.put("durarionPlan", mtOutApplication.getDurarionPlan());
            User user = userDao.get(mtOutApplication.getApplicant());
            if (user != null) {
                map.put("departName", user.getDepartName());
            } else {
                map.put("departName", "");
            }
        }

        String sql = "select mb.`name`,mb.code,mb.category,mb.model,mb.specifications,(SELECT SUM(mm.minimum_stock) FROM t_mt_supplier_material mm WHERE mm.material=mb.id and ifnull(mm.enabled,0)!=0) as minimumStock,mu.name unit,item.id,item.quantity_plan quantityPlan,\n" +
                "ms.current_stock currentStock,item.delivery_fact as deliveryFact,item.approval_status AS approvalStatus,item.state,item.quantity_fact quantityFact,item.update_name updateName,item.update_date updateDate,item.create_name createName,item.create_date createDate,item.material material\n" +
                "from t_mt_out_application_item item\n" +
                "left join  t_mt_base mb on item.material =mb.id\n" +
                "left join t_mt_unit mu on mb.unit_id=mu.id \n" +
                "left JOIN t_mt_stock_info ms on ms.material = mb.id  \n" +
                "where item.application=" + mtOutId + " and item.approval_status=?0";
        //获取待领材料
        List<Map<String, Object>> list1 = (List<Map<String, Object>>) mtOutApplicationItemDao.findMapByConditionNoPage(sql, new Object[]{"2"}).get("data");
        if (list1 == null) {
            list1 = new ArrayList<>();
        }
        //获取已领材料
        List<Map<String, Object>> list2 = (List<Map<String, Object>>) mtOutApplicationItemDao.findMapByConditionNoPage(sql, new Object[]{"3"}).get("data");
        if (list2 == null) {
            list2 = new ArrayList<>();
        }
        for (Map<String, Object> m : list2) {
            Integer id = (Integer) m.get("id");
            String sql2 = "select item.quantity_fact quantityFact,item.surplus_quantity surplusQuantity,wl.location_code locationCode\n" +
                    "from t_mt_out_application_item item\n" +
                    " left join  t_mt_location l on l.id=item.material_location " +
                    "left JOIN t_inv_warehouse_location wl on wl.id = l.location  \n" +
                    "where item.instance=" + id + " order by item.id desc";
            List<Map<String, Object>> list3 = (List<Map<String, Object>>) mtOutApplicationItemDao.findMapByConditionNoPage(sql2, new Object[]{}).get("data");
            m.put("location", list3);
        }
        map.put("dl", list1);
        map.put("yl", list2);
        return map;
    }


    @Override
    @Transactional
    public String pickingMtApproval(Integer itemId, String json, User loginUser) {
        MtOutApplicationItem item = mtOutApplicationItemDao.get(itemId);
        if (item == null) {
            return "参数有误";
        } else {
            if (!"0".equals(item.getState())) {
                return "该条领料已审批";
            }
        }
        JSONArray jsonArray = JSONArray.fromObject(json);
        if (jsonArray.size() == 0) {
            return "请选择出库数量";
        }
        MtStockInfo mtStockInfo = mtStockInfoDao.getByHQL("from MtStockInfo where material_=" + item.getMaterial());

        BigDecimal oldStock = mtStockInfo.getCurrentStock()==null?new BigDecimal(0):mtStockInfo.getCurrentStock();
        if (mtStockInfo == null) {
            return "库存不足";
        }
        if (mtStockInfo.getCurrentStock() == null) {
            return "库存不足";
        }


        //修改库位库存
        String ids = "";
        BigDecimal nums = new BigDecimal(0);
        List<MtLocation> mtLocations = new ArrayList<>();
        List<MtOutApplicationItem> mtOutApplicationItems = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            Integer locationId = object.getInt("locationId");
            BigDecimal num = new BigDecimal(object.get("num").toString());
            nums = nums.add(num);
            if ("".equals(ids)) {
                ids = locationId + "";
            } else {
                ids = ids + "," + locationId;
            }
            //库位
            if (locationId != null && locationId != 0) {
                MtLocation mtLocation = mtLocationDao.get(locationId);
                if (mtLocation != null) {

                    item.setMaterialLocation(locationId);
                    if (mtLocation.getAmount().compareTo(num) == -1) {
                        return "库位材料数量不足";
                    }

                    BigDecimal amount=mtLocation.getAmount().subtract(num);
                    mtLocations.add(mtLocation);
                    MtOutApplicationItem mi = new MtOutApplicationItem();
                    mi.setInstance(item.getId());
                    mi.setMaterialLocation(locationId);
                    mi.setQuantityFact(num);
                    mi.setSurplusQuantity(amount);
                    mtOutApplicationItems.add(mi);
                }

            }

        }
        if (mtStockInfo.getCurrentStock().compareTo(nums) == -1) {
            return "库存不足";
        }

        if (item.getQuantityPlan().compareTo(nums) == 0) {
            item.setState("2");//全部出库
        } else if (item.getQuantityPlan().compareTo(nums) == 1) {
            item.setState("1");//部分出库
        } else {
            return "因为领料数量与申领数量不一致！";
        }


        mtStockInfo.setCurrentStock(mtStockInfo.getCurrentStock().subtract(nums));
        for (MtLocation m : mtLocations) {
            mtLocationDao.update(m);
        }

        for (MtOutApplicationItem mtOutApplicationItem : mtOutApplicationItems
        ) {
            mtOutApplicationItem.setMaterial(0);
            mtOutApplicationItemDao.save(mtOutApplicationItem);
        }
        mtStockInfoDao.update(mtStockInfo);

        //增加库存变动记录
        mtService.saveStockRecord(item.getOrg(),mtStockInfo.getMaterial_(), loginUser.getUserID(),loginUser.getUserName(),null,"out",nums.negate(),oldStock,mtStockInfo.getCurrentStock(),null);
//            //增加修改历史
//            MtStockInfoHistory mtSH = new MtStockInfoHistory();
//            mtSH.setStockInfo(mtStockInfo.getId());
//
//            mtSH.setMaterial(mtStockInfo.getMaterial_());
//            mtSH.setInitialStock(mtStockInfo.getInitialStock());
//            mtSH.setAvailableStock(mtStockInfo.getAvailableStock());
//            mtSH.setMinimumStock(mtStockInfo.getMinimumStock());
//            mtSH.setStockPosition(mtStockInfo.getStockPosition());
//            mtSH.setStockRequirements(mtStockInfo.getStockRequirements());
//            mtSH.setCreator(mtStockInfo.getCreator());
//            mtSH.setCreateName(mtStockInfo.getCreateName());
//            mtSH.setCreateDate(new Date());
//            mtSH.setProviderName(mtBase.getLastProviderName());
//            mtSH.setAfterInitialStock(stock);
//            mtSH.setAfterMinimumStock(mtStockInfo.getMinimumStock());
//            mtStockInfoHistoryDao.save(mtSH);

        item.setUpdateDate(new Date());
        item.setUpdateName(loginUser.getUserName());
        item.setUpdator(loginUser.getUserID());
        // item.setApprovalStatus("3");
        item.setQuantityFact(nums);
        item.setOperation("3");
        item.setSurplusQuantity(mtStockInfo.getCurrentStock());



        item.setApprovalStatus("3");
        item.setDeliveryFact(new Date());


        mtOutApplicationItemDao.update(item);

        mtOutApplicationItemDao.getSession().flush();
        //获取待领材料
        List<MtOutApplicationItem> list = mtOutApplicationItemDao.getListByHQL("from MtOutApplicationItem where application=" + item.getApplication() + " and state='0'");

        if (list.size() == 0) {
            String s = "你的领料申请被批准了!";
            suspendMsgService.saveUserSuspendMsg(1, s, "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), item.getCreator(), null, 0);
        }

        List<MtOutApplicationItem> list2 = mtOutApplicationItemDao.getListByHQL("from MtOutApplicationItem where application=" + item.getApplication() + " and approval_status='2'");


        if (list2.size() == 0) {
            MtOutApplication mtOutApplication = mtOutApplicationDao.get(item.getApplication());
            mtOutApplication.setApprovalStatus("3");
            mtOutApplicationDao.update(mtOutApplication);
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("outApplication", mtOutApplication);
            hashMap.put("id", mtOutApplication.getId());
            User user = userDao.get(mtOutApplication.getCreator());
            swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(), "/getPickingMtApplyList", null, null, user, "pickingApply");
            List<UserDto> users = userPopedomService.getUserByOidMid(user.getOid(), "xc");
            for (UserDto ud : users) {
                //审批人
                User user1 = userDao.get(ud.getUserID());
                swMessageService.rejectSend(-1, -1, hashMap, user1.getUserID().toString(), "/getPickingMtApprovalList", null, null, user1, "pickingApproval");

            }
        }

        return "success";
    }

    @Override
    public List<MtOutApplication> selectPickingList(Integer userId, String userType, String approvalStatus, String beginTime, String endTime) {
        if ("2".equals(userType)) {
            User user = userDao.get(userId);
            String hql = "from MtOutApplication where org=" + user.getOid() + " and approvalStatus='" + approvalStatus + "'";
            if (beginTime != null && endTime != null) {
                hql += " and createDate>='" + beginTime + "' and createDate<='" + endTime + "'";

            }
            return mtOutApplicationDao.getListByHQL(hql);
        } else {

            String hql = "from MtOutApplication where  applicant=" + userId + " and approvalStatus='" + approvalStatus + "'";
            if (beginTime != null && endTime != null) {
                hql += " and createDate>='" + beginTime + "' and createDate<='" + endTime + "'";
            }
            return mtOutApplicationDao.getListByHQL(hql);
        }


    }

    @Override
    public String pickingMtRevoke(Integer id, User loginUser) {

        //获取已领材料
        List<MtOutApplicationItem> list = mtOutApplicationItemDao.getListByHQL("from MtOutApplicationItem where application=" + id + " and approvalStatus='3'");
        if (list.size() == 0) {


            MtOutApplication mtOutApplication = mtOutApplicationDao.get(id);
            if (!"2".equals(mtOutApplication.getApprovalStatus())) {
                return "该申请单状态已改变,不能撤销";
            }
            mtOutApplication.setApprovalStatus("4");
            mtOutApplicationDao.update(mtOutApplication);
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("outApplication", mtOutApplication);
            hashMap.put("id", mtOutApplication.getId());
            User user = userDao.get(mtOutApplication.getCreator());
            swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(), "/getPickingMtApplyList", null, null, user, "pickingApply");
            List<UserDto> users = userPopedomService.getUserByOidMid(user.getOid(), "xc");
            for (UserDto ud : users) {
                //审批人
                User user1 = userDao.get(ud.getUserID());
                swMessageService.rejectSend(-1, -1, hashMap, user1.getUserID().toString(), "/getPickingMtApprovalList", null, null, user1, "pickingApproval");
            }

        } else {
            return "已有审批通过的材料,不能撤销";
        }

        return "success";
    }

    @Override
    public String pickingMtConfirm(Integer itemId, User loginUser) {
        MtOutApplicationItem item = mtOutApplicationItemDao.get(itemId);


        if (item == null) {
            return "参数有误";
        } else {
            if (!"2".equals(item.getApprovalStatus())) {
                return "该条领料已审批";
            }
        }

        item.setApprovalStatus("3");
        item.setDeliveryFact(new Date());
        mtOutApplicationItemDao.update(item);
        mtOutApplicationItemDao.getSession().flush();
        List<MtOutApplicationItem> list = mtOutApplicationItemDao.getListByHQL("from MtOutApplicationItem where application=" + item.getApplication() + " and approval_status='2'");


        if (list.size() == 0) {
            MtOutApplication mtOutApplication = mtOutApplicationDao.get(item.getApplication());
            mtOutApplication.setApprovalStatus("3");
            mtOutApplicationDao.update(mtOutApplication);
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("outApplication", mtOutApplication);
            hashMap.put("id", mtOutApplication.getId());
            User user = userDao.get(mtOutApplication.getCreator());
            swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(), "/getPickingMtApplyList", null, null, user, "pickingApply");
            List<UserDto> users = userPopedomService.getUserByOidMid(user.getOid(), "xc");
            for (UserDto ud : users) {
                //审批人
                User user1 = userDao.get(ud.getUserID());
                swMessageService.rejectSend(-1, -1, hashMap, user1.getUserID().toString(), "/getPickingMtApprovalList", null, null, user1, "pickingApproval");

            }
        }

        return "success";
    }


    @Override
    public List<UserDto> getPickingUserList(Integer oid, String code) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(u.userID, case when u.userName is not null and length(trim(u.userName))>0 then u.userName else u.mobile end,u.mobile) from User u where u.oid = :oid  and u.isDuty='1' and u.roleCode in('staff','super') and u.userID NOT in (select ur.userID from UserRole ur where ur.role.code=:code)";
        Map<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("code", code);
        List<UserDto> userDtoList = userDao.getListByHQLWithNamedParams(hql, params);
        return userDtoList;
    }

    @Override
    public Map<String, Object> getPickingRecordList(Integer oid,Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql="select mb.code,mb.name,mb.model,mb.specifications,unit.`name` unit,item.quantity_fact quantityFact,item.delivery_fact deliveryFact,item.create_name createName,IFNULL(su.departName,'') departName from t_mt_out_application_item item\n" +
                "LEFT JOIN t_mt_base mb on mb.id=item.material\n" +
                "LEFT JOIN t_mt_unit unit on unit.id=mb.unit_id\n" +
                "LEFT JOIN t_sys_user su on su.userID=item.creator\n" +
                "where item.org="+oid+" and item.approval_status='3' and (item.state='2' or item.state='1' or item.state='3')\n"+
                "order by item.delivery_fact asc";


        return mtOutApplicationItemDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    @Transactional
    public String manualWarehousing(MtInApplication mtInApplication, String mtList) {

        SimpleDateFormat sdfTime = new SimpleDateFormat(
                "yyyy-MM-dd HH:mm:ss");
        JSONArray jsonArray = JSONArray.fromObject(mtList);


        mtInApplication.setItemPlan(Long.valueOf(jsonArray.size()));

        mtInApplicationDao.save(mtInApplication);

        BigDecimal allNums=new BigDecimal(0);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);

            Integer mtId = object.optInt("id");

            String isKw = object.optString("isKw ");
            //供货关系id
            Integer supplierMaterial = object.optInt("supplierMaterial");
            BigDecimal nums=new BigDecimal(0);
            if("1".equals(isKw)){
                JSONArray numList=object.getJSONArray("numList");
                if(numList!=null){
                    for (int j = 0; j < numList.size(); j++) {

                        Long locationId = object.optLong("locationId");
                        BigDecimal num=new BigDecimal(object.optLong("num"));

                        nums.add(num);
                        MtLocation mtLocation = (MtLocation) mtLocationDao.getByHQLWithNamedParams("from MtLocation where material="+mtId+" and supplierMaterial="+supplierMaterial+" and location="+locationId,new HashMap<>());
                        if(mtLocation==null){
                            mtLocation=new MtLocation();
                            mtLocation.setLocation(locationId);
                            mtLocation.setSupplierMaterial(supplierMaterial);
                            mtLocation.setAmount(num);
                            mtLocation.setMaterial(mtId);
                            mtLocation.setCreator(mtInApplication.getCreator());
                            mtLocation.setCreateName(mtInApplication.getCreateName());
                            mtLocation.setCreateDate(mtInApplication.getCreateDate());
                            mtLocationDao.save(mtLocation);
                        }else{
                            mtLocation.setAmount(mtLocation.getAmount().add(num));
                            mtLocationDao.update(mtLocation);
                        }

                    }
                }
            }else{
                BigDecimal num = new BigDecimal(object.optString("num", "0"));
                nums.add(num);
            }


            MtStockInfo mtStockInfo = mtStockInfoDao.getByHQL("from MtStockInfo where material_=" + mtId);
            BigDecimal oldStock = mtStockInfo.getCurrentStock()==null?new BigDecimal(0):mtStockInfo.getCurrentStock();



            mtStockInfo.setCurrentStock(mtStockInfo.getCurrentStock().add(nums));
            //增加库存变动记录
            mtService.saveStockRecord(mtInApplication.getOrg(),mtStockInfo.getMaterial_(),mtInApplication.getCreator(),mtInApplication.getCreateName(),null,"out",nums.negate(),oldStock,mtStockInfo.getCurrentStock(),null);


            MtInApplicationItem item = new MtInApplicationItem();
            item.setApplicant(mtInApplication.getId());
            item.setCreateDate(new Date());
            item.setCreator(mtInApplication.getCreator());
            item.setCreateName(mtInApplication.getCreateName());

            item.setApplicantName(mtInApplication.getApplicantName());
            item.setArriveDate(mtInApplication.getArriveTime());
            item.setApprovalType("1");
            item.setResultType("1");
            item.setQuantityType("1");
            item.setQuantityFact(nums);
            item.setQuantityPlan(nums);
            item.setState("A");
            item.setInspectorName(mtInApplication.getInspectorName());
            item.setInspector(mtInApplication.getInspector());
            item.setMaterial(mtId);
            mtInApplicationItemDao.save(item);
        }

        mtInApplication.setSn(getSn(mtInApplication.getId().toString()));
        mtInApplication.setItemPlan(Long.valueOf(jsonArray.size()));
        mtInApplication.setQuantityFact(allNums.longValue());
        mtInApplication.setQuantityPass(allNums.longValue());
        mtInApplication.setQuantityPlan(allNums.longValue());
        mtInApplicationDao.update(mtInApplication);
        return "success";
    }


    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number = null;
        switch (code) {
            case "pickingApproval"://领料审批
                String hql = "select count(id) from MtOutApplication where approvalStatus = :approvalStatus and org = :org";
                HashMap<String, Object> param = new HashMap<>();
                param.put("approvalStatus", "2");
                param.put("org", user.getOid());
                Long num = (Long) mtOutApplicationDao.getByHQLWithNamedParams(hql, param);
                number = num.intValue();
                break;

        }
        return number;
    }

    private void setSub(Map<String, Object> category, List<Map<String, Object>> categories, List<Map<String, Object>> mtList) {

        for (Map<String, Object> mr : mtList) {
            if (mr.get("category").toString().equals(category.get("id").toString())) {
                List<Map<String, Object>> l = (List<Map<String, Object>>) category.get("mtList");
                if (l == null) {
                    l = new ArrayList<>();
                }
                l.add(mr);
                category.put("mtList", l);
            }
        }
        for (Map<String, Object> m : categories) {
            if (m.get("parent") != null) {
                if (m.get("parent").toString().equals(category.get("id").toString())) {
                    List<Map<String, Object>> l = (List<Map<String, Object>>) category.get("subList");
                    if (l == null) {
                        l = new ArrayList<>();
                    }
                    l.add(m);
                    setSub(m, categories, mtList);
                    category.put("subList", l);
                }
            }

        }
    }

    private Integer checkNum(Map<String, Object> category, Integer i) {
        List<Map<String, Object>> l = (List<Map<String, Object>>) category.get("mtList");
        if (l != null) {
            i++;

            return i;
        }

        List<Map<String, Object>> l2 = (List<Map<String, Object>>) category.get("subList");
        if (l2 != null) {
            for (Map<String, Object> m : l2) {

                Integer res = checkNum(m, i);

                if (res > 0) {
                    return res;
                }
            }
        }
        return i;
    }


    private String getSn(String id) {
        SimpleDateFormat sdfTime = new SimpleDateFormat(
                "yyyyMMddHH");
        String day = "No." + sdfTime.format(new Date());
        int length = id.length();
        if (length == 3) {
            id = day + "0" + id;
        } else if (length == 2) {
            id = day + "00" + id;
        } else if (length == 1) {
            id = day + "000" + id;
        } else {
            id = day + "id";
        }

        return id;
    }
}
