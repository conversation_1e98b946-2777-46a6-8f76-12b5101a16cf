package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_po_contract_material_history")
public class PoContractMaterialHistory {
    private Integer id;
    private Integer org;
    private Integer contract;
    private Integer contractHistory;
    private Integer contractMaterial;
    private Integer material;
    private Integer materialHistory;
    private BigDecimal unitPrice;
    private BigDecimal quantity;
    private BigDecimal amount;
    private Integer orders;
    private Integer enabled;
    private Date enabledTime;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org", nullable = true)
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "contract", nullable = true)
    public Integer getContract() {
        return contract;
    }

    public void setContract(Integer contract) {
        this.contract = contract;
    }

    @Basic
    @Column(name = "material", nullable = true)
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "material_history", nullable = true)
    public Integer getMaterialHistory() {
        return materialHistory;
    }

    public void setMaterialHistory(Integer materialHistory) {
        this.materialHistory = materialHistory;
    }

    @Basic
    @Column(name = "unit_price", nullable = true, precision = 10)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Basic
    @Column(name = "quantity", nullable = true, precision = 10)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Basic
    @Column(name = "amount", nullable = true, precision = 10)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Basic
    @Column(name = "orders", nullable = true)
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "enabled", nullable = true)
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time", nullable = true)
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "memo", nullable = true, length = 255)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator", nullable = true)
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name", nullable = true, length = 100)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator", nullable = true)
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name", nullable = true, length = 100)
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date", nullable = true)
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation", nullable = true, length = 1)
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id", nullable = true)
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no", nullable = true)
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }


    @Basic
    @Column(name = "contract_history", nullable = true)
    public Integer getContractHistory() {
        return contractHistory;
    }

    public void setContractHistory(Integer contractHistory) {
        this.contractHistory = contractHistory;
    }
    @Basic
    @Column(name = "contract_material", nullable = true)
    public Integer getContractMaterial() {
        return contractMaterial;
    }

    public void setContractMaterial(Integer contractMaterial) {
        this.contractMaterial = contractMaterial;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PoContractMaterialHistory that = (PoContractMaterialHistory) o;
        return id == that.id && Objects.equals(org, that.org) && Objects.equals(contract, that.contract) && Objects.equals(material, that.material) && Objects.equals(materialHistory, that.materialHistory) && Objects.equals(unitPrice, that.unitPrice) && Objects.equals(quantity, that.quantity) && Objects.equals(amount, that.amount) && Objects.equals(orders, that.orders) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(memo, that.memo) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, contract, material, materialHistory, unitPrice, quantity, amount, orders, enabled, enabledTime, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
