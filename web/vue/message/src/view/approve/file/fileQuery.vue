<template>
  <div id="approveFileQuery">
    <TY_NavTop title="筛选"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tip">您可按以下筛选条件，查询您经手审批的数据。</div>
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <el-form-item label="申请人">
              <el-select v-model="applyer" placeholder="选择申请人" size="small" @change=changeValue>
                <el-option
                  v-for="item in applyUserList"
                  :key="item.userID"
                  :label="item.userName"
                  :value="item.userID">
                </el-option>
              </el-select>
            </el-form-item>
            <h4 class="item-header">事件结果</h4>
            <div class="el-form-item" v-if="eventTab === 3">
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已发布/换版/废止的文件</el-radio>
                <el-radio :label="3">已驳回的文件</el-radio>
              </el-radio-group>
            </div>
            <div class="el-form-item" v-else>
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已发布/换版/废止的文件</el-radio>
                <el-radio :label="3">驳回的发布/换版/废止申请</el-radio>
              </el-radio-group>
            </div>
            <h4 class="item-header">发生时间</h4>
            <div class="el-form-item">
              <el-date-picker type="date" placeholder="开始日期" v-model="beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd" v-on:change="clearOther('date')"></el-date-picker> -
              <el-date-picker type="date" placeholder="结束日期" v-model="endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd" v-on:change="clearOther('date')"></el-date-picker>
            </div>
            <el-form-item label="仅看年报">
              <el-date-picker type="year" placeholder="选择年份" v-model="year" style="width: 100%;" size="small" value-format="yyyy" v-on:change="clearOther('year')"></el-date-picker>
            </el-form-item>
            <el-form-item label="仅看月报">
              <el-date-picker type="month" placeholder="选择月份" v-model="month" style="width: 100%;" size="small" v-on:change="clearOther('month')"></el-date-picker>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="fc-btn ui-btn_info" value="确定" @click="submitForm()" v-loading.fullscreen.lock="loading">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'approveFileQuery',
  data () {
    return {
      loading: false,
      queryForm: {
        approveStatus: 2
      },
      applyer: '',
      applyUserList: [],
      year: '',
      month: '',
      beginDate: '',
      endDate: '',
      eventTab: ''
    }
  },
  created () {
    this.eventTab = this.$route.params.event
    if (!this.eventTab) {
      this.eventTab = JSON.parse(localStorage.getItem('query')).eventType
    }
    let params = {
      'userID': this.sphdSocket.user.userID
    }
    if (this.$route.params.event === 4) {
      params.type = 1
    }
    let _this = this
    this.$http.post('../../../res/getAllOidUserByResource.do', params, {
      emulateJSON: true
    }).then((response) => {
      let data = response.body
      if (data) {
        _this.applyUserList = data.data.list
        _this.applyUserList.unshift({
          userName: '全部',
          userID: ''
        })
        _this.appyer = ''
      } else {
        console.log('菜单加载失败！')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    submitForm: function () {
      // userId 登录人
      // type （3- 自定义 ，2 -仅看年报，1- 仅看月报）
      // approveStatus   2- 被批准的，3-被驳回的
      // timeBegin 开始时间   格式举例： 2018-09-01   选择年报时需要拼成这个格式，月报同理
      // timeEnd 结束时间   格式举例： 2018-09-01
      this.loading = true
      this.queryForm.userID = this.sphdSocket.user.userID
      this.queryForm.fromUser = this.applyer
      let event = this.$route.params.event
      if (!event) {
        event = JSON.parse(localStorage.getItem('query')).eventType
      }
      this.queryForm.eventType = event
      if (event === 4) {
        this.queryForm.oid = this.sphdSocket.org.id
      } else {
        delete this.queryForm.oid
      }
      if (this.year !== '') {
        this.queryForm.type = 2
        this.queryForm.timeBegin = this.year
      } else if (this.month !== '') {
        this.queryForm.type = 1
        let date = new Date(this.month)
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        if (month < 10) {
          month = '0' + month
        }
        this.queryForm.timeBegin = year + '-' + month
      } else {
        this.queryForm.type = 3
        this.queryForm.timeBegin = this.beginDate
        this.queryForm.timeEnd = this.endDate
      }
      if (this.queryForm.timeBegin) {
        localStorage.setItem('query', JSON.stringify(this.queryForm))
        this.$router.push({
          path: '/approveFileQueryResult',
          name: 'approveFileQueryResult',
          params: {
            data: this.queryForm
          }
        })
      } else {
        this.loading = false
        this.$kiko_message('请选择查询时间')
      }
    },
    clearOther: function (type) {
      if (type === 'date') {
        this.year = ''
        this.month = ''
      } else if (type === 'year') {
        this.month = ''
        this.beginDate = ''
        this.endDate = ''
      } else if (type === 'month') {
        this.year = ''
        this.beginDate = ''
        this.endDate = ''
      }
    }
  }
}
</script>
