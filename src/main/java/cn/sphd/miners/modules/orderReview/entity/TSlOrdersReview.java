package cn.sphd.miners.modules.orderReview.entity;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 对象 t_sl_orders_review
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public class TSlOrdersReview
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long org;

    /** $column.columnComment */
    private Long orders;

    /** $column.columnComment */
    private Long phase;

    /** $column.columnComment */
    private Long reviewNum;

    /** $column.columnComment */
    private Long previousReview;

    /** $column.columnComment */
    private Long itemCount;

    /** $column.columnComment */
    private Long scheduledCount;

    /** $column.columnComment */
    private Long reviewer;

    /** $column.columnComment */
    private String reviewerName;

    /** $column.columnComment */
    private Date reviewDate;

    /** $column.columnComment */
    private Long salesResult;

    /** $column.columnComment */
    private Date salesDate;

    /** $column.columnComment */
    private Long creator;

    /** $column.columnComment */
    private String createName;

    /** $column.columnComment */
    private Date createDate;

    /** $column.columnComment */
    private Long updator;

    /** $column.columnComment */
    private String updateName;

    /** $column.columnComment */
    private Date updateDate;

    /** $column.columnComment */
    private Long operation;

    /** $column.columnComment */
    private Long previousId;

    /** $column.columnComment */
    private Long versionNo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setOrders(Long orders) 
    {
        this.orders = orders;
    }

    public Long getOrders() 
    {
        return orders;
    }
    public void setPhase(Long phase) 
    {
        this.phase = phase;
    }

    public Long getPhase() 
    {
        return phase;
    }
    public void setReviewNum(Long reviewNum) 
    {
        this.reviewNum = reviewNum;
    }

    public Long getReviewNum() 
    {
        return reviewNum;
    }
    public void setPreviousReview(Long previousReview) 
    {
        this.previousReview = previousReview;
    }

    public Long getPreviousReview() 
    {
        return previousReview;
    }
    public void setItemCount(Long itemCount) 
    {
        this.itemCount = itemCount;
    }

    public Long getItemCount() 
    {
        return itemCount;
    }
    public void setScheduledCount(Long scheduledCount) 
    {
        this.scheduledCount = scheduledCount;
    }

    public Long getScheduledCount() 
    {
        return scheduledCount;
    }
    public void setReviewer(Long reviewer) 
    {
        this.reviewer = reviewer;
    }

    public Long getReviewer() 
    {
        return reviewer;
    }
    public void setReviewerName(String reviewerName) 
    {
        this.reviewerName = reviewerName;
    }

    public String getReviewerName() 
    {
        return reviewerName;
    }
    public void setReviewDate(Date reviewDate) 
    {
        this.reviewDate = reviewDate;
    }

    public Date getReviewDate() 
    {
        return reviewDate;
    }
    public void setSalesResult(Long salesResult) 
    {
        this.salesResult = salesResult;
    }

    public Long getSalesResult() 
    {
        return salesResult;
    }
    public void setSalesDate(Date salesDate) 
    {
        this.salesDate = salesDate;
    }

    public Date getSalesDate() 
    {
        return salesDate;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setOperation(Long operation) 
    {
        this.operation = operation;
    }

    public Long getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("org", getOrg())
            .append("orders", getOrders())
            .append("phase", getPhase())
            .append("reviewNum", getReviewNum())
            .append("previousReview", getPreviousReview())
            .append("itemCount", getItemCount())
            .append("scheduledCount", getScheduledCount())
            .append("reviewer", getReviewer())
            .append("reviewerName", getReviewerName())
            .append("reviewDate", getReviewDate())
            .append("salesResult", getSalesResult())
            .append("salesDate", getSalesDate())
            .append("creator", getCreator())
            .append("createName", getCreateName())
            .append("createDate", getCreateDate())
            .append("updator", getUpdator())
            .append("updateName", getUpdateName())
            .append("updateDate", getUpdateDate())
            .append("operation", getOperation())
            .append("previousId", getPreviousId())
            .append("versionNo", getVersionNo())
            .toString();
    }
}
