<!--日流水的具体详情-->
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>

    <div v-if="info">

      <div class="hr_gray"></div>
      <ul class="margin-10-15">
        <li v-html="idcTxt"></li>
        <li style="margin-top: 10px;">
          <span>{{ idC.createName }}
            {{ new Date(idC.createDate).format('yyyy-MM-dd hh:mm:ss') }}
            {{ idC.index === 0 ? '创建' : '修改' }}
          </span>
        </li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>收款金额</span><span v-html="info.repaymentAmount"></span></li>
        <li><span>收款日期</span><span v-html="info.repaymentTime"></span></li>
        <li><span>支付方式</span><span v-html="info.repaymentMethod"></span></li>
        <li><span>收款经手人</span><span v-html="info.receiveOperatorName "></span></li>
      </ul>
      <div class="hr_gray"></div>


    </div>



  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "loanDetail",
  data() {
    return {
      idC:'',
      info: null,
      idcTxt:''
    }
  },
  mixins: [finance],
  mounted() {
    let loanRepayEditDetail = localStorage.getItem('loanRepayEditDetail')
    loanRepayEditDetail = JSON.parse(loanRepayEditDetail)
    console.log('loanRepayEditDetail=', loanRepayEditDetail)
    this.idC = loanRepayEditDetail
    this.idcTxt = loanRepayEditDetail.index === 0 ? '以下为原始信息' : '以下为第' +  loanRepayEditDetail.index + '次修改后的数据，其中本次修改的被标为了<a class="ty-color-red">红色</a>'
    this.getOrdRecordModHistoryDetail(loanRepayEditDetail.id)
  },
  methods: {
    getOrdRecordModHistoryDetail(id){
      this.financeApi.ordRecordModHistoryDetail({ id : id }).then(res1=>{
        let res = res1.data.data
        let mapBefore = res.mapBefore
        let mapParam = res.mapParam
        let info = {
          repaymentAmount : mapParam.repaymentAmount,
          repaymentTime : (new Date(mapParam.repaymentTime).format('yyyy-MM-dd') ) ,
          repaymentMethod: this.chargePayMethod(mapParam.repaymentMethod),
          receiveOperatorName :  mapParam.receiveOperatorName
        }
        if(mapBefore.createName){
          info.repaymentAmount = this.compareTxt(mapBefore.repaymentAmount, mapParam.repaymentAmount )
          info.repaymentTime = this.compareTxt((new Date(mapBefore.repaymentTime).format('yyyy-MM-dd')  ), (new Date(mapParam.repaymentTime).format('yyyy-MM-dd') )  )
          info.repaymentMethod = this.compareTxt(this.chargePayMethod(mapBefore.repaymentMethod), this.chargePayMethod(mapParam.repaymentMethod))
          info.receiveOperatorName = this.compareTxt((mapBefore.receiveOperatorName), (mapParam.receiveOperatorName))

        }
        this.info = info

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },



  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.turnHang{
  display: inline-block;  white-space:normal; line-height: 20px;
  max-width: 60%;
}
.loan{
  .fa-angle-right{ font-size: 20px;  }

}
</style>