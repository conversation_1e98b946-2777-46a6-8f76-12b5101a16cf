package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtInApplicationDao;
import cn.sphd.miners.modules.material.entity.MtInApplication;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * Created by Administrator on 2016/9/19.
 */
@Repository
public class MtInApplicationDaoImpl extends BaseDao<MtInApplication,Serializable> implements MtInApplicationDao {

}
