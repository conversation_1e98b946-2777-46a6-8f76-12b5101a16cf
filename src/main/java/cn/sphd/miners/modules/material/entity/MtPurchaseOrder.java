
package cn.sphd.miners.modules.material.entity;

import java.sql.*;
import java.util.Date;

import java.io.Serializable;
import javax.persistence.*;

@Entity (name="MtPurchaseOrder")
@Table (name="t_mt_purchase_order")
//物料_采购订单(预留)
public class MtPurchaseOrder implements Serializable {
    private static final long serialVersionUID = 1L;


	
    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

//MP-MANAGED-ADDED-AREA-BEGINNING @apply_no-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @apply_no-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-apply_no@
    @Column(name="apply_no"   , nullable=true , unique=false)
    private Integer applyNo; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @order_no-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @order_no-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-order_no@
    @Column(name="order_no"  , length=100 , nullable=true , unique=false)
    private String orderNo; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @material-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @material-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-material@
    @Column(name="material"   , nullable=true , unique=false)
    private Integer material; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @material_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @material_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-material_name@
    @Column(name="material_name"  , length=255 , nullable=true , unique=false)
    private String materialName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @supplier-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @supplier-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-supplier@
    @Column(name="supplier"   , nullable=true , unique=false)
    private Integer supplier; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @supplier_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @supplier_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-supplier_name@
    @Column(name="supplier_name"  , length=100 , nullable=true , unique=false)
    private String supplierName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @unit_price-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @unit_price-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-unit_price@
    @Column(name="unit_price"   , nullable=true , unique=false)
    private java.math.BigDecimal unitPrice; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @quantity-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @quantity-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-quantity@
    @Column(name="quantity"   , nullable=true , unique=false)
    private Long quantity; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @arrival_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @arrival_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-arrival_date@
    @Column(name="arrival_date"   , nullable=true , unique=false)
    private Date arrivalDate; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @pay_type-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @pay_type-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-pay_type@
    @Column(name="pay_type"  , length=1 , nullable=true , unique=false)
    private String payType; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @pay_method-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @pay_method-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-pay_method@
    @Column(name="pay_method"  , length=1 , nullable=true , unique=false)
    private String payMethod; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @bank_code-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @bank_code-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-bank_code@
    @Column(name="bank_code"  , length=100 , nullable=true , unique=false)
    private String bankCode; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @bank_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @bank_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-bank_name@
    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @bank_no-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @bank_no-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-bank_no@
    @Column(name="bank_no"  , length=100 , nullable=true , unique=false)
    private String bankNo; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @invoice_type-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @invoice_type-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-invoice_type@
    @Column(name="invoice_type"  , length=1 , nullable=true , unique=false)
    private String invoiceType; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @operator-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @operator-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-operator@
    @Column(name="operator"   , nullable=true , unique=false)
    private Integer operator; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @operator_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @operator_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-operator_name@
    @Column(name="operator_name"  , length=100 , nullable=true , unique=false)
    private String operatorName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @operate_time-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @operate_time-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-operate_time@
    @Column(name="operate_time"   , nullable=true , unique=false)
    private Date operateTime;
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @quantity_plan-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @quantity_plan-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-quantity_plan@
    @Column(name="quantity_plan"   , nullable=true , unique=false)
    private Long quantityPlan; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @quantity_fact-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @quantity_fact-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-quantity_fact@
    @Column(name="quantity_fact"   , nullable=true , unique=false)
    private Long quantityFact; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @state-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @state-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-state@
    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @apply_type-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @apply_type-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-apply_type@
    @Column(name="apply_type"  , length=1 , nullable=true , unique=false)
    private String applyType; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @memo-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @memo-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-memo@
    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @description-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @description-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-description@
    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @creator-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @creator-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-creator@
    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @create_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @create_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-create_name@
    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @create_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @create_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-create_date@
    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @updator-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @updator-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-updator@
    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @update_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @update_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-update_name@
    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @update_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @update_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-update_date@
    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;
//MP-MANAGED-UPDATABLE-ENDING


    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-apply_no@
    public Integer getApplyNo() {
        return applyNo;
    }
	
    public void setApplyNo (Integer applyNo) {
        this.applyNo =  applyNo;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-order_no@
    public String getOrderNo() {
        return orderNo;
    }
	
    public void setOrderNo (String orderNo) {
        this.orderNo =  orderNo;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-material@
    public Integer getMaterial() {
        return material;
    }
	
    public void setMaterial (Integer material) {
        this.material =  material;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-material_name@
    public String getMaterialName() {
        return materialName;
    }
	
    public void setMaterialName (String materialName) {
        this.materialName =  materialName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-supplier@
    public Integer getSupplier() {
        return supplier;
    }
	
    public void setSupplier (Integer supplier) {
        this.supplier =  supplier;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-supplier_name@
    public String getSupplierName() {
        return supplierName;
    }
	
    public void setSupplierName (String supplierName) {
        this.supplierName =  supplierName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-unit_price@
    public java.math.BigDecimal getUnitPrice() {
        return unitPrice;
    }
	
    public void setUnitPrice (java.math.BigDecimal unitPrice) {
        this.unitPrice =  unitPrice;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-quantity@
    public Long getQuantity() {
        return quantity;
    }
	
    public void setQuantity (Long quantity) {
        this.quantity =  quantity;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-arrival_date@
    public Date getArrivalDate() {
        return arrivalDate;
    }
	
    public void setArrivalDate (Date arrivalDate) {
        this.arrivalDate =  arrivalDate;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-pay_type@
    public String getPayType() {
        return payType;
    }
	
    public void setPayType (String payType) {
        this.payType =  payType;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-pay_method@
    public String getPayMethod() {
        return payMethod;
    }
	
    public void setPayMethod (String payMethod) {
        this.payMethod =  payMethod;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-bank_code@
    public String getBankCode() {
        return bankCode;
    }
	
    public void setBankCode (String bankCode) {
        this.bankCode =  bankCode;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-bank_name@
    public String getBankName() {
        return bankName;
    }
	
    public void setBankName (String bankName) {
        this.bankName =  bankName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-bank_no@
    public String getBankNo() {
        return bankNo;
    }
	
    public void setBankNo (String bankNo) {
        this.bankNo =  bankNo;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-invoice_type@
    public String getInvoiceType() {
        return invoiceType;
    }
	
    public void setInvoiceType (String invoiceType) {
        this.invoiceType =  invoiceType;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-operator@
    public Integer getOperator() {
        return operator;
    }
	
    public void setOperator (Integer operator) {
        this.operator =  operator;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-operator_name@
    public String getOperatorName() {
        return operatorName;
    }
	
    public void setOperatorName (String operatorName) {
        this.operatorName =  operatorName;
    }
	

	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-quantity_plan@
    public Long getQuantityPlan() {
        return quantityPlan;
    }
	
    public void setQuantityPlan (Long quantityPlan) {
        this.quantityPlan =  quantityPlan;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-quantity_fact@
    public Long getQuantityFact() {
        return quantityFact;
    }
	
    public void setQuantityFact (Long quantityFact) {
        this.quantityFact =  quantityFact;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-state@
    public String getState() {
        return state;
    }
	
    public void setState (String state) {
        this.state =  state;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-apply_type@
    public String getApplyType() {
        return applyType;
    }
	
    public void setApplyType (String applyType) {
        this.applyType =  applyType;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-memo@
    public String getMemo() {
        return memo;
    }
	
    public void setMemo (String memo) {
        this.memo =  memo;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-description@
    public String getDescription() {
        return description;
    }
	
    public void setDescription (String description) {
        this.description =  description;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-creator@
    public Integer getCreator() {
        return creator;
    }
	
    public void setCreator (Integer creator) {
        this.creator =  creator;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-create_name@
    public String getCreateName() {
        return createName;
    }
	
    public void setCreateName (String createName) {
        this.createName =  createName;
    }

	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-updator@
    public Integer getUpdator() {
        return updator;
    }
	
    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-update_name@
    public String getUpdateName() {
        return updateName;
    }
	
    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }


//MP-MANAGED-ADDED-AREA-BEGINNING @implementation@
//MP-MANAGED-ADDED-AREA-ENDING @implementation@

}
