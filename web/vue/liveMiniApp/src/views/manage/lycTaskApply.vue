<template>
	<!-- 歌词任务 -->
	<div class="lycTaskApply">
		<Pageheader title="歌词任务" :showBack="true" :runMyBackFun="true" @myBackFun="cancelAdd"/>
    <div>
      <div class="live-panel">
        以下曲目的歌词有待上传
      </div>
      <div class="mainc">
        <div class="lyricList">
          <div class="lyItem" v-for="(item, index) in list" :key="index">
            <div @click="goApplyDetails(item)" class="lyItemC">
              <span class="name"> {{ item.name }}</span>
              <span class="time" v-if="item.captionRequired">{{ item.durationTime  }}
          </span>
              <arrow-right class="arrR" />
            </div>
          </div>
        </div>
      </div>
    </div>
	</div>
</template>
<script setup>
import { reactive, ref, onMounted } from "vue";
import { ElMessage } from 'element-plus'
import { useRouter,useRoute  } from 'vue-router';
import { getLyricTask } from '@/api/music.js'
import JSONBig from 'json-bigint'

const router = useRouter();
let list = ref([])

onMounted(() => {
  getList()
})
// 取消新增
const cancelAdd = () => {
  console.log('cancelAdd')
  router.push({
    name: 'homeHandle',
  });
}

// 跳转歌曲详情
const goApplyDetails = (item) => {
  localStorage.setItem('lyricDetailsItem', JSONBig.stringify(item))
  router.push({ name: 'lyricApplyDetails' });
}

const getList = () => {
  let data = { type:1, approveStatus:1 }
  getLyricTask(data).then(res=>{
    console.log('getLyricTask 返回值', res)
    list.value = res.data.listSongs
  }).catch(err => {
    console.log('有错误', err)
  })
}



</script>
<style lang="scss" scoped>
.lycTaskApply{
  .lyricList{
    margin-top: 20px;
    background: #fff;
    .lyItem:nth-child(odd){
      background-color: #eee;
    }
    .lyItemC{
      display: flex;
      padding:0 10px;
      .name{
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 40px;
        flex: 3;
      }
      .arrR{
        width: 16px;
        display: inline-block;
      }
      .time{
        display: inline-block;
        line-height: 43px;
        text-align: right;
        flex: 1;

      }
    }
  }
}

</style>
