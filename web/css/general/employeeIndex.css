.employeeQuery .searchCon {
    padding: 20px;
}

.employeeQuery input {
    border: 1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    width: 100%;
    text-align: left;
    padding: 0 8px;
    color: #3f3f3f;
    height: 36px;
}

.employeeQuery input:focus {
    border: 1px solid #5d9cec;
}

.eq_item {
    width: 420px;
    margin-bottom: 10px;
    border-bottom: 1px solid #e2e2e2;
    padding-bottom: 10px;
}

.eq_item:last-child {
    border-bottom: none;
}

.eq_item .eq_l {
    float: left;
    width: 22%;
    line-height: 2em;
    text-align: right;
    padding-right: 20px;
}

.eq_item .eq_r {
    float: left;
    width: 78%;
    line-height: 2em;
    white-space: normal;
}

.laydate_body .laydate_y .laydate_yms ul {
    width: 120px;
}

.gapSect ul {
    padding: 10px 0;
}

.gapSect ul li {
    margin-bottom: 10px;
}

.gapSect ul li span:first-child {
    margin-right: 6px;
    display: inline-block;
    min-width: 120px;
    text-align: right;
}

.gapSect ul li i {
    font-style: normal;
}

.disEdit {
    vertical-align: middle;
    display: inline-block;
    width: 162px;
    line-height: 22px;
    background: rgba(215, 215, 215, 0.32);
    text-align: left;
    padding: 4px 6px;
    min-height: 22px;
}

#handleUpdateForm select:disabled, #handleUpdateForm input:disabled {
    background: rgba(215, 215, 215, 0.32);
}

.handleFiled label.error {
    margin-left: 102px;
    font-size: 12px;
    display: inline;
    color: red;
    white-space: nowrap
}

.handleFiled li {
    float: left;
    width: 46%;
    height: 50px;
}

.handleFiled li .filedTtl {
    min-width: 100px;
    display: inline-block;
    text-align: right;
}

.handleFiled li select {
    width: 192px;
}
.handleFiled li input {
    width: 192px;
}
.department_panel {
    position: relative;
    vertical-align: middle;
}

.department_panel .option_con {
    position: absolute;
    top: 30px;
    border: 1px solid #ccc;
    font-size: 12px;
    width: 252px;
    margin: -1px 0 0;
    background: #eaeaea;
    padding: 15px;
    z-index: 100;
}
.departItem {
    padding: 2px;
}
.levelItem{
    line-height: 28px;
}
.leaveLt{
    margin: 0 42px;
}
.allOrgCheck{
    color: #0070bf;
}



