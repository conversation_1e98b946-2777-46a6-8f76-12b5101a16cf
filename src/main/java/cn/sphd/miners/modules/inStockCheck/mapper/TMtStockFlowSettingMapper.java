package cn.sphd.miners.modules.inStockCheck.mapper;

import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting;

public interface TMtStockFlowSettingMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TMtStockFlowSetting record);

    int insertSelective(TMtStockFlowSetting record);

    TMtStockFlowSetting selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TMtStockFlowSetting record);

    int updateByPrimaryKey(TMtStockFlowSetting record);

    TMtStockFlowSetting selectByValue(Integer id);
}