<template>
  <div class="lyricTaskHandle">
    <!--      <el-page-header class icon="arrow-left-bold" content="歌曲管理"></el-page-header>-->
    <div class="mainc">
      <div class="tipc">
        系统中歌曲共如下XX首
        <el-button type="text" class="newAdd" @click="gopage(1)">新增</el-button>
        <div class="lit">（带红色！的为歌词尚未上传者）</div>
      </div>
      <div class="lyricList">
        <div class="lyItem">
          <span class="name">歌曲名称歌曲名称歌曲名称歌曲名称歌曲名称</span>
          <span class="time">0分49秒
              <arrow-right class="arrR" />
          </span>
        </div>
        <div class="lyItem">
          <span class="name">歌曲名称歌曲名称歌曲名称歌曲名称歌曲名称</span>
          <span class="time">0分49秒
              <arrow-right class="arrR" />
          </span>
        </div>
        <div class="lyItem">
          <span class="name">歌曲名称歌曲名称歌曲名称歌曲名称歌曲名称</span>
          <span class="time">0分49秒
            <arrow-right class="arrR" />
          </span>
        </div>


      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { ref, toRefs, onMounted } from 'vue';
import { useRouter } from "vue-router";
export default {
  setup() {
    let data = ref({})
    const router = useRouter();
    onMounted(() => {
      console.log("===------");


    });
    const gopage = (type) => {
      let url = 'newLyric'
      console.log('url', url)
      router.push({
        name: url,
        params: {
        },
      });
    }
    let refData = toRefs(data);
    return {
      ...refData,
      gopage
    };
  },
};
</script>
<style lang="scss">
.lyricTaskHandle{
  .mainc{
    color:#333;
  }
  .tipc{
    padding:0 10px;
    //background-color: #d8ecd8;
    //color: #333;
    .newAdd{
      float: right;
    }
    .lit{
      font-size: 0.8em;
      color: #e03c5d;
    }
  }
  .lyricList{
    margin-top: 20px;
    background: #eed;
    .lyItem:nth-child(odd){
      background-color: #eee;
    }
    .lyItem{
      display: inline-flex;
      padding:0 10px;
      .name{
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 40px;
        flex: 3;
      }
      .time{
        display: inline-block;
        line-height: 43px;
        text-align: right;
        flex: 1;
        .arrR{
          width: 16px;
          display: inline-block;
        }
      }
    }
  }
}
</style>
