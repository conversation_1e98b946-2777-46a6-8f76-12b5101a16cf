<template>
  <div>
    <div class="datePane">今天是{{date_today}}</div>
    <div class="ui-cells">
      <a class="ui-cell" @click="handleFileDetail(item.id)" v-for="(item ,index) in fileHandle" :key="index">
        <div class="fileLists">
          <div class="fileType file_xls"></div>
          <div class="fileInfo">
            <div class="fileName" v-text="item.name" :title="item.name"></div>
            <div class="fileDetail">
              {{(!item.updateName || item.updateName === null || item.updateName === undefined)?item.createName:item.updateName}}
              {{(!item.updateDate || item.updateDate === null || item.updateDate === undefined)?item.createDate:item.updateDate}}
            </div>
          </div>
          <div class="fileFormat">
            <span class="fileVersion">G{{item.changeNum}}</span>
            <span class="fileNo">编号：{{item.fileSn}}</span>
          </div>
        </div>
      </a>
    </div>
  </div>
</template>

<script>
import * as moment from 'moment'
import 'moment/locale/zh-cn'
export default {
  name: 'approvedFileIssue',
  data () {
    return {
      weekDay: '',
      date_today: moment(new Date()).format('YYYY/MM/DD'),
      list: this.$store.getters.getFileList,
      fileHandle: []
    }
  },
  created: function () {
    let today = new Date()
    let week = today.getDay()
    let weekArr = ['日', '一', '二', '三', '四', '五', '六']
    this.weekDay = '周' + weekArr[week]
    this.fileHandle = (this.list && this.list.fileHandle) || []
    this.$store.dispatch('setNewFileNav', 2)
  },
  methods: {
    handleFileDetail: function (oid) {
      this.$router.push({
        path: `/approveHandleFile/${oid}`
      })
    },
    watch: {
      '$store.state.fileList': {
        immediate: true,
        deep: true,
        handler (fileList) {
          this.list = fileList
          this.fileHandle = (this.list && this.list.fileHandle) || []
        }
      }
    }
  }
}
</script>
