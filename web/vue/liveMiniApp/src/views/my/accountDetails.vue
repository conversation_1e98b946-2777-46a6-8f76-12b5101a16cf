<template>
  <!-- 小程序》我的 账号信息 -->
  <div class="accountDetails">
    <Pageheader title="账号信息" :showBack="true"/>
    <div class="live-panel live-tr">
      <span>账号</span><span class="live-right" >{{ user.mobile}}</span>
    </div>
    <div class="live-panel live-pad">
      <div class="live-tr live-tr-line">
        <span>所在工作室</span><span class="live-right" >{{ org.name }}</span>
      </div>
      <div class="live-tr">
        <span>进入该工作室的时间</span><span class="live-right" >{{ userTime }}</span>
      </div>
    </div>

  </div>
</template>
<script setup>
import JSONBig from 'json-bigint'
import '@/utils/DateFormat.js'
import auth from "../../sys/auth";

  let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  console.log(' let jsonData = ', jsonData)
  const org = auth.getOrg()
  const user = auth.getUser()
  const officeList = jsonData.officeList
  const userTime = (new Date(user.createTime)).format("yyyy年M月d日 hh:mm:ss")

</script>