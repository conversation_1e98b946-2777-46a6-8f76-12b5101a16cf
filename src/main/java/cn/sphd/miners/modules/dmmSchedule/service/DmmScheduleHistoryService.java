package cn.sphd.miners.modules.dmmSchedule.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleHistory;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleImageHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * Created by Administrator on 2019/8/13.
 */
public interface DmmScheduleHistoryService {

    List<DmmScheduleHistory> getDmmScheduleHistories(Integer id, PageInfo pageInfo);

    DmmScheduleHistory getNewDmmScheduleHistory(Integer scheduleId);// 获取最新的历史记录

    void  saveDmmScheduleHistory(DmmSchedule dmmSchedule, AuthAcc acc, Boolean delFileUsing);//产生日程历史记录

    DmmScheduleHistory getDmmScheduleHistoryById(Integer id);

    DmmScheduleImageHistory getDmmScheduleImageHistoryById(Integer id);
}
