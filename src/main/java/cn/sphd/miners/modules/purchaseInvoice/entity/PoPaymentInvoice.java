package cn.sphd.miners.modules.purchaseInvoice.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 采购_票款处理发票表
 * 2021/6/16  lyx
 * 1.169采购的票款处理
 */
@Entity
@Table(name = "t_po_payment_invoice")
public class PoPaymentInvoice implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"  , nullable=true, unique=false)
    private Integer org;   //机构ID

    @Column(name="application"  , nullable=true , unique=false)
    private Integer application;   //申请ID

    @Column(name="application_no"  , length=100 , nullable=true , unique=false)
    private String applicationNo;  //申请单号

    @Column(name="invoice_category"  , length=1 , nullable=true , unique=false)
    private String invoiceCategory;  //发票种类:1-专用票,2-普通票,3-收据,4-其他发票

    @Column(name="invoice_code"  , length=30 , nullable=true , unique=false)
    private String invoiceCode;  //发票代码

    @Column(name="invoice_no"  , length=30 , nullable=true , unique=false)
    private String invoiceNo;  //发票号码

    @Column(name="old_invoice"  , length=100 , nullable=true , unique=false)
    private String oldInvoice;  //原发票号码(修改时,原发票仍然有效时保存),多次修改以逗号分隔

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;   //金额

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;   //票据金额

    @Column(name="par_amount"   , nullable=true , unique=false)
    private BigDecimal parAmount;   //面值(单张票据金额)

    @Column(name="item_count"  , nullable=true , unique=false)
    private Integer itemCount;   //单张票据行数（是否是一行，单行的是1，多行的为0【一张票据如果选择的是多行的，那么行数也可以是一行】）

    @Column(name="tax_amount"   , nullable=true , unique=false)
    private BigDecimal taxAmount;   //税额

    @Column(name="relative_bill_quantity"  , nullable=true , unique=false)
    private Integer relativeBillQuantity;   //相同票据数量

    @Column(name="relative_bill_no"  ,  length=255 , nullable=true , unique=false)
    private String relativeBillNo;   //相同票据号码,多个以逗号分隔

    @Column(name="primary_bill"  , nullable=true , unique=false)
    private Integer primaryBill;   //主票号码ID,多张票录入时用

    @Column(name="certification_state"  , nullable=true , unique=false)
    private Integer certificationState;   //认证状态:0-未认证(默认);1-认证通过;2-认证未通过,不抵扣,仅报销;3-此张发票认证失败,换了新的发票

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo ;   //备注

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description ;   //描述

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;   //操作:1-增,2-删,3-改

    @Column(name = "previous_id")
    private Integer previousId;   //修改前记录ID

    @Column(name = "version_no")
    private Integer versionNo;   //版本号,每次修改+1

    @Transient
    private List<PoPaymentInvoiceItem> poPaymentInvoiceItems = new ArrayList<>();   //票据中货物的信息

    @Transient
    private List<PoPaymentInvoiceAttachment> poPaymentInvoiceAttachments = new ArrayList<>();  //票据的图片信息

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getApplication() {
        return application;
    }

    public void setApplication(Integer application) {
        this.application = application;
    }

    public String getApplicationNo() {
        return applicationNo;
    }

    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getOldInvoice() {
        return oldInvoice;
    }

    public void setOldInvoice(String oldInvoice) {
        this.oldInvoice = oldInvoice;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getRelativeBillQuantity() {
        return relativeBillQuantity;
    }

    public void setRelativeBillQuantity(Integer relativeBillQuantity) {
        this.relativeBillQuantity = relativeBillQuantity;
    }

    public String getRelativeBillNo() {
        return relativeBillNo;
    }

    public void setRelativeBillNo(String relativeBillNo) {
        this.relativeBillNo = relativeBillNo;
    }

    public Integer getPrimaryBill() {
        return primaryBill;
    }

    public void setPrimaryBill(Integer primaryBill) {
        this.primaryBill = primaryBill;
    }

    public Integer getCertificationState() {
        return certificationState;
    }

    public void setCertificationState(Integer certificationState) {
        this.certificationState = certificationState;
    }

    public List<PoPaymentInvoiceItem> getPoPaymentInvoiceItems() {
        return poPaymentInvoiceItems;
    }

    public void setPoPaymentInvoiceItems(List<PoPaymentInvoiceItem> poPaymentInvoiceItems) {
        this.poPaymentInvoiceItems = poPaymentInvoiceItems;
    }

    public List<PoPaymentInvoiceAttachment> getPoPaymentInvoiceAttachments() {
        return poPaymentInvoiceAttachments;
    }

    public void setPoPaymentInvoiceAttachments(List<PoPaymentInvoiceAttachment> poPaymentInvoiceAttachments) {
        this.poPaymentInvoiceAttachments = poPaymentInvoiceAttachments;
    }

    public BigDecimal getParAmount() {
        return parAmount;
    }

    public void setParAmount(BigDecimal parAmount) {
        this.parAmount = parAmount;
    }
}
