package cn.sphd.miners.modules.resourceAuthority.task;

import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.system.service.DlmService;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * Created by 朱思旭 on 2021/9/27.
 */
public class ResTrashFileTask {

    @Autowired
    DlmService dlmService;
    @Autowired
    ResService resService;

    public void delResTrashFileTaskDay() throws ParseException {
        //获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("清除资料管理文件定时任务开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            resService.regularlyCleanedDisapperFile();
            System.out.println("清除资料管理文件定时任务结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            dlmService.releaseLock(methodName, lockKey);
        }
    }

}
