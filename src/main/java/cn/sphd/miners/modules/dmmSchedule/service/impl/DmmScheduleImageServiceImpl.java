package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.modules.dmmSchedule.dao.DmmScheduleImageDao;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleImage;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/13.
 */
@Service("dmmScheduleImageService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DmmScheduleImageServiceImpl implements DmmScheduleImageService {

    @Autowired
    DmmScheduleImageDao dmmScheduleImageDao;


    @Override
    public void deleteDmmScheduleImage(DmmScheduleImage dmmScheduleImage) {
        dmmScheduleImageDao.delete(dmmScheduleImage);
    }

    @Override
    public void deleteAllDmmScheduleImage(Integer scheduleId) {
        String hql=" delete DmmScheduleImage where scheduleId=:scheduleId";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        dmmScheduleImageDao.queryHQLWithNamedParams(hql,params);

    }

    @Override
    public List<DmmScheduleImage> getDmmScheduleImageListBySid(Integer scheduleId) {
        String hql=" from DmmScheduleImage where scheduleId=:scheduleId";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        List<DmmScheduleImage> dmmScheduleImageList= dmmScheduleImageDao.getListByHQLWithNamedParams(hql,params);
        return dmmScheduleImageList;
    }

    @Override
    public DmmScheduleImage getDmmScheduleImageById(Integer id) {
        return dmmScheduleImageDao.get(id);
    }
}
