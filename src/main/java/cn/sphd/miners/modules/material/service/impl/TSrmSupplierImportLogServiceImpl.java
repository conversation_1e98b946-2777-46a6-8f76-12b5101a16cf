package cn.sphd.miners.modules.material.service.impl;

import java.util.List;

import cn.sphd.miners.modules.material.entity.TSrmSupplierImportLog;
import cn.sphd.miners.modules.material.mapper.TSrmSupplierImportLogMapper;
import cn.sphd.miners.modules.material.service.ITSrmSupplierImportLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 供应商管理_供应商导入日志
 * 20231108 1.267供应商之批量导入Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Service
public class TSrmSupplierImportLogServiceImpl implements ITSrmSupplierImportLogService {
    @Autowired
    private TSrmSupplierImportLogMapper tSrmSupplierImportLogMapper;

    /**
     * 查询供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     *
     * @param id 供应商管理_供应商导入日志
     *           20231108 1.267供应商之批量导入主键
     * @return 供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     */
    @Override
    public TSrmSupplierImportLog selectTSrmSupplierImportLogById(Long id) {
        return tSrmSupplierImportLogMapper.selectTSrmSupplierImportLogById(id);
    }

    /**
     * 查询供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入列表
     *
     * @param tSrmSupplierImportLog 供应商管理_供应商导入日志
     *                              20231108 1.267供应商之批量导入
     * @return 供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     */
    @Override
    public List<TSrmSupplierImportLog> selectTSrmSupplierImportLogList(TSrmSupplierImportLog tSrmSupplierImportLog) {
        return tSrmSupplierImportLogMapper.selectTSrmSupplierImportLogList(tSrmSupplierImportLog);
    }

    /**
     * 新增供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     *
     * @param tSrmSupplierImportLog 供应商管理_供应商导入日志
     *                              20231108 1.267供应商之批量导入
     * @return 结果
     */
    @Override
    public int insertTSrmSupplierImportLog(TSrmSupplierImportLog tSrmSupplierImportLog) {
        return tSrmSupplierImportLogMapper.insertTSrmSupplierImportLog(tSrmSupplierImportLog);
    }

    /**
     * 修改供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     *
     * @param tSrmSupplierImportLog 供应商管理_供应商导入日志
     *                              20231108 1.267供应商之批量导入
     * @return 结果
     */
    @Override
    public int updateTSrmSupplierImportLog(TSrmSupplierImportLog tSrmSupplierImportLog) {
        return tSrmSupplierImportLogMapper.updateTSrmSupplierImportLog(tSrmSupplierImportLog);
    }

    /**
     * 批量删除供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入
     *
     * @param ids 需要删除的供应商管理_供应商导入日志
     *            20231108 1.267供应商之批量导入主键
     * @return 结果
     */
    @Override
    public int deleteTSrmSupplierImportLogByIds(String ids) {
        return tSrmSupplierImportLogMapper.deleteTSrmSupplierImportLogByIds(ids.split(","));
    }

    /**
     * 删除供应商管理_供应商导入日志
     * 20231108 1.267供应商之批量导入信息
     *
     * @param id 供应商管理_供应商导入日志
     *           20231108 1.267供应商之批量导入主键
     * @return 结果
     */
    @Override
    public int deleteTSrmSupplierImportLogById(Long id) {
        return tSrmSupplierImportLogMapper.deleteTSrmSupplierImportLogById(id);
    }
}
