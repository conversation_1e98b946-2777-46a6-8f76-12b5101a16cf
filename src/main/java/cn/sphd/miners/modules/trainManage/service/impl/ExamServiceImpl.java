package cn.sphd.miners.modules.trainManage.service.impl;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.trainManage.dao.*;
import cn.sphd.miners.modules.trainManage.dto.*;
import cn.sphd.miners.modules.trainManage.entity.*;
import cn.sphd.miners.modules.trainManage.service.ExamService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * @ClassName TrainServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/3 8:13
 * @Version 1.0
 */
@Service("examService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ExamServiceImpl implements ExamService {
    @Autowired
    TrainingQuestionBankDao trainingQuestionBankDao;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    TrainingQuestionBankAttachmentDao trainingQuestionBankAttachmentDao;
    @Autowired
    TrainingQuestionBankAttachmentHistoryDao trainingQuestionBankAttachmentHistoryDao;
    @Autowired
    TrainingExamBankDao trainingExamBankDao;
    @Autowired
    TrainingExamRuleDao trainingExamRuleDao;
    @Autowired
    TrainingMaterialDao trainingMaterialDao;
    @Autowired
    TrainingQuestionDao trainingQuestionDao;
    @Autowired
    TrainingQuestionKeyDao trainingQuestionKeyDao;
    @Autowired
    TrainingExamQuestionDao trainingExamQuestionDao;
    @Autowired
    TrainingExamKeyDao trainingExamKeyDao;
    @Autowired
    TrainingExamUserAnswerDao trainingExamUserAnswerDao;
    @Autowired
    TrainingExamDao trainingExamDao;
    @Autowired
    TrainingExamUserDao trainingExamUserDao;
    @Autowired
    UserDao userDao;
    @Autowired
    TrainingQuestionBankHistoryDao trainingQuestionBankHistoryDao;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;



    @Override
    public List<TrainingExam> selectExamList(User user, Integer type, PageInfo pageInfo, Integer status) {
        String hql = "from TrainingExam where creator = "+ user.getUserID()+" and enabled="+status +" and primaryExam is null";
        if(status==1)
            hql=hql+" and state=2";
        if(type==1)//1截至时间降序排序
            hql=hql+" ORDER BY endTime desc";
        else if(type==2)//2截至时间升序排序
            hql=hql+" ORDER BY endTime ASC";
        else if(type==3)//3创建时间降序排序
            hql=hql+" ORDER BY createDate desc";
        else if(type==4)//4创建时间升序排序
            hql=hql+" ORDER BY createDate ASC";
        else if(type==5)//5素材数量降序排序
            hql=hql+" ORDER BY bankNum desc";
        else if(type==6)//6素材数量升序排序
            hql=hql+" ORDER BY bankNum ASC";
        else if(type==7)//7终止时间降序排序
            hql=hql+" ORDER BY enabledTime desc";
        else if(type==9)//8终止时间升序排序
            hql=hql+" ORDER BY enabledTime ASC";
        List<TrainingExam> list = trainingExamDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public List<TrainingExam> selectFinishExamList(User user, Integer type, PageInfo pageInfo,Integer peroid) {
        String hql = "from TrainingExam where peroid="+peroid+" and org="+user.getOid()+"  and (state='4' or state='3') and primaryExam is null ";
        if(type==1)//1截至时间降序排序
            hql=hql+" ORDER BY endTime desc";
        else if(type==2)//2截至时间升序排序
            hql=hql+" ORDER BY endTime ASC";
        List<TrainingExam> list = trainingExamDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public List<TrainingExam> selectFinishExamListByYear(User user, Integer type, PageInfo pageInfo, Integer year) {
        int peroidStart=year*100;
        int peroidEnd=year*100+13;
        String hql = "from TrainingExam where peroid>"+peroidStart+"and peroid<"+peroidEnd+" and creator = "+ user.getUserID()+" and (state='4' or state='3') and primaryExam is null ";
        if(type==1)//1截至时间降序排序
            hql=hql+" ORDER BY endTime desc";
        else if(type==2)//2截至时间升序排序
            hql=hql+" ORDER BY endTime ASC";
        List<TrainingExam> list = trainingExamDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public RespPeroidExamList selectExamListByMonth(Integer org, Integer year) {
        RespPeroidExamList respPeroidExamList=new RespPeroidExamList();
        int peroidStart=year*100;
        int peroidEnd=year*100+13;
        String sql="select peroid,COUNT(*) as sum from t_training_exam where org="+org+" and peroid>"+peroidStart+" and peroid<"+peroidEnd+"  and (state='4' or state='3') and primary_exam is null GROUP BY peroid";
        List<Object[]> objectList= trainingExamDao.getObjectListBySQL(sql);
        List<PeroidExam> peroidExamList=new ArrayList<>();
        if (objectList.size() > 0 && !objectList.isEmpty()) {
            for (int i = 0; i <objectList.size(); i++) {
                PeroidExam peroidExam =new PeroidExam();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(objectList.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                peroidExam.setPeroid(Integer.valueOf(objects.getString(0)));
                peroidExam.setSum(Integer.valueOf(objects.getString(1)));
                int month=peroidExam.getPeroid()%100;
                peroidExam.setMonth(year+"-"+month);
                peroidExamList.add(peroidExam);
            }
        }
        respPeroidExamList.setPeroidExamList(peroidExamList);
        return respPeroidExamList;
    }

    @Override
    public RespExamListForUser selectExamForUserList(User user) {
        RespExamListForUser respExamListForUser=new RespExamListForUser();
        List<RespExamForUser> respExamForUserList=new ArrayList<>();
        List<RespExamForUser> respExamForUserAgainList=new ArrayList<>();
        String hql = "from TrainingExamUser where user="+user.getUserID()+" and stopTime is null and answerTimes=0";
        List<TrainingExamUser> trainingExamUserList = userDao.getListByHQLWithNamedParams(hql,null);
        for(TrainingExamUser trainingExamUser:trainingExamUserList){
            RespExamForUser respExamForUser=new RespExamForUser();
            respExamForUser.setTrainingExamUser(trainingExamUser);
            TrainingExam trainingExam= trainingExamDao.get(trainingExamUser.getExam());
            respExamForUser.setTrainingExam(trainingExam);
            respExamForUserList.add(respExamForUser);
        }
        respExamListForUser.setRespExamForUserList(respExamForUserList);
        respExamListForUser.setExamSum(respExamForUserList.size());
        String hql1 = "from TrainingExamUser where user="+user.getUserID()+" and stopTime is null and answerTimes>0";
        List<TrainingExamUser> trainingExamUserList1 = userDao.getListByHQLWithNamedParams(hql1,null);
        for(TrainingExamUser trainingExamUser:trainingExamUserList1){
            RespExamForUser respExamForUser=new RespExamForUser();
            respExamForUser.setTrainingExamUser(trainingExamUser);
            TrainingExam trainingExam= trainingExamDao.get(trainingExamUser.getExam());
            respExamForUser.setTrainingExam(trainingExam);
            respExamForUserAgainList.add(respExamForUser);
        }
        respExamListForUser.setRespExamForUserAgainList(respExamForUserAgainList);
        respExamListForUser.setExamAgainSum(respExamForUserAgainList.size());
        return respExamListForUser;
    }

    @Override
    public RespExamInfoForUser startExam(User user, int id) {
        TrainingExam trainingExam=trainingExamDao.get(id);
        Calendar trainingExamEndTime= Calendar.getInstance();
        trainingExamEndTime.setTime(trainingExam.getEndTime());
        Date date=new Date();
        Calendar calendar = Calendar.getInstance();
        RespExamInfoForUser respExamInfoForUser=new RespExamInfoForUser();
        respExamInfoForUser.setDate(date);
        String hql11 = "from TrainingExamUser where user="+user.getUserID()+" and exam ="+id;
        List<TrainingExamUser> trainingExamUserList = trainingExamUserDao.getListByHQLWithNamedParams(hql11,null);
        if(trainingExamUserList.size()>0)
        {
            if(trainingExamUserList.get(0).getStartTime()==null||"".equals(trainingExamUserList.get(0).getStartTime())){
                trainingExamUserList.get(0).setStartTime(date);
                trainingExamUserDao.update(trainingExamUserList.get(0));
                respExamInfoForUser.setStartTime(date);
                calendar.setTime(respExamInfoForUser.getStartTime());
                calendar.add(Calendar.MINUTE, trainingExam.getAnswerDuration());
//                Calendar calendar1 = Calendar.getInstance();
//                calendar1.setTime(calendar.getTime());
                if(trainingExamEndTime.getTime().compareTo(calendar.getTime())<0)
                {
                    calendar.setTime(trainingExamEndTime.getTime());
//                    calendar1.setTime(calendar.getTime());
//                    calendar1.add(Calendar.SECOND,2);
                }
                UserExamOverCallback userExamOverCallback = new UserExamOverCallback(trainingExamUserList.get(0).getId());
                clusterMessageSendingOperations.delayCall(calendar.getTime(), userExamOverCallback);
            }else{
                respExamInfoForUser.setStartTime(trainingExamUserList.get(0).getStartTime());
                calendar.setTime(respExamInfoForUser.getStartTime());
                calendar.add(Calendar.MINUTE, trainingExam.getAnswerDuration());
                if(trainingExamEndTime.getTime().compareTo(calendar.getTime())<0)
                {
                    calendar.setTime(trainingExamEndTime.getTime());
                }
            }
        }
        respExamInfoForUser.setEndTime(calendar.getTime());
        List<RespExamQuestion> list=new ArrayList<>();
        String hql = "from TrainingExamQuestion where exam=" + id+" order by orders asc";
        List<TrainingExamQuestion> trainingExamQuestionList= trainingExamQuestionDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamQuestion trainingExamQuestion:trainingExamQuestionList)
        {
            RespExamQuestion respExamQuestion=new RespExamQuestion();
            respExamQuestion.setTrainingExamQuestion(trainingExamQuestion);
            TrainingQuestion trainingQuestion= trainingQuestionDao.get(trainingExamQuestion.getQuestion());
            respExamQuestion.setTrainingQuestion(trainingQuestion);
            if("1".equals(trainingQuestion.getType())){
                String hql1 = "from TrainingExamKey where examQuestion=" + trainingExamQuestion.getId();
                List<TrainingExamKey> trainingExamKeyList= trainingExamKeyDao.getListByHQLWithNamedParams(hql1, null);
                respExamQuestion.setTrainingExamKeyList(trainingExamKeyList);
            }
            String hql1 = "from TrainingExamUserAnswer where examQuestion=" + trainingExamQuestion.getId()+" and user="+user.getUserID();
            List<TrainingExamUserAnswer> trainingExamUserAnswerList= trainingExamUserAnswerDao.getListByHQLWithNamedParams(hql1, null);
            if(trainingExamUserAnswerList.size()>0)
                respExamQuestion.setTrainingExamUserAnswer(trainingExamUserAnswerList.get(0));
            list.add(respExamQuestion);
        }
        respExamInfoForUser.setRespExamQuestionList(list);
        return respExamInfoForUser;
    }

    @Override
    public RespExamInfoForUser selectExamDetail(User user, int id) {
        RespExamInfoForUser respExamInfoForUser=new RespExamInfoForUser();
        List<RespExamQuestion> list=new ArrayList<>();
        String hql = "from TrainingExamQuestion where exam=" + id+" order by orders asc";
        List<TrainingExamQuestion> trainingExamQuestionList= trainingExamQuestionDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamQuestion trainingExamQuestion:trainingExamQuestionList)
        {
            RespExamQuestion respExamQuestion=new RespExamQuestion();
            respExamQuestion.setTrainingExamQuestion(trainingExamQuestion);
            TrainingQuestion trainingQuestion= trainingQuestionDao.get(trainingExamQuestion.getQuestion());
            respExamQuestion.setTrainingQuestion(trainingQuestion);

            String hql1 = "from TrainingExamKey where examQuestion=" + trainingExamQuestion.getId();
            List<TrainingExamKey> trainingExamKeyList= trainingExamKeyDao.getListByHQLWithNamedParams(hql1, null);
            respExamQuestion.setTrainingExamKeyList(trainingExamKeyList);

            String hql12 = "from TrainingExamUserAnswer where examQuestion=" + trainingExamQuestion.getId()+" and user="+user.getUserID();
            List<TrainingExamUserAnswer> trainingExamUserAnswerList= trainingExamUserAnswerDao.getListByHQLWithNamedParams(hql12, null);
            if(trainingExamUserAnswerList.size()>0)
                respExamQuestion.setTrainingExamUserAnswer(trainingExamUserAnswerList.get(0));
            list.add(respExamQuestion);
        }

        String hqluser = "from TrainingExamUser where exam=" + id +" and user="+user.getUserID();
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hqluser, null);
        if (trainingExamUserList.size()>0)
        {
            RespExamUser respExamUser=new RespExamUser();
            respExamUser.setTrainingExamUser(trainingExamUserList.get(0));
            User user1=userDao.get(user.getUserID());
            respExamUser.setUser(user1);
            respExamInfoForUser.setRespExamUser(respExamUser);
        }
        respExamInfoForUser.setRespExamQuestionList(list);
        return respExamInfoForUser;
    }
    @Override
    public RespExamInfoForUser selectExamDetailErrors(User user, int id) {
        RespExamInfoForUser respExamInfoForUser=new RespExamInfoForUser();
        List<RespExamQuestion> list=new ArrayList<>();
        String hql = "from TrainingExamQuestion where exam=" + id+" order by orders asc";
        List<TrainingExamQuestion> trainingExamQuestionList= trainingExamQuestionDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamQuestion trainingExamQuestion:trainingExamQuestionList)
        {
            RespExamQuestion respExamQuestion=new RespExamQuestion();
            respExamQuestion.setTrainingExamQuestion(trainingExamQuestion);
            TrainingQuestion trainingQuestion= trainingQuestionDao.get(trainingExamQuestion.getQuestion());
            respExamQuestion.setTrainingQuestion(trainingQuestion);

            String hql1 = "from TrainingExamKey where examQuestion=" + trainingExamQuestion.getId();
            List<TrainingExamKey> trainingExamKeyList= trainingExamKeyDao.getListByHQLWithNamedParams(hql1, null);
            respExamQuestion.setTrainingExamKeyList(trainingExamKeyList);

            String hql12 = "from TrainingExamUserAnswer where examQuestion=" + trainingExamQuestion.getId()+" and user="+user.getUserID();
            List<TrainingExamUserAnswer> trainingExamUserAnswerList= trainingExamUserAnswerDao.getListByHQLWithNamedParams(hql12, null);
            if(trainingExamUserAnswerList.size()>0)
            {
                respExamQuestion.setTrainingExamUserAnswer(trainingExamUserAnswerList.get(0));
                for(TrainingExamUserAnswer trainingExamUserAnswer:trainingExamUserAnswerList)
                {
                    if(trainingExamUserAnswer.getIsCorrect()==0)
                        list.add(respExamQuestion);
                }
            }
            if(trainingExamUserAnswerList.size()==0)
            {
                        list.add(respExamQuestion);
            }
        }
        String hqluser = "from TrainingExamUser where exam=" + id +" and user="+user.getUserID();
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hqluser, null);
        if (trainingExamUserList.size()>0)
        {
            RespExamUser respExamUser=new RespExamUser();
            respExamUser.setTrainingExamUser(trainingExamUserList.get(0));
            User user1=userDao.get(user.getUserID());
            respExamUser.setUser(user1);
            respExamInfoForUser.setRespExamUser(respExamUser);
        }
        respExamInfoForUser.setRespExamQuestionList(list);
        respExamInfoForUser.setRespExamQuestionList(list);
        return respExamInfoForUser;
    }
    @Override
    public Integer selectLastExam(User user) {
        Integer exam=0;
        String sql="select id from t_training_exam where end_time=(select max(end_time)  from t_training_exam  where state>2 and org="+user.getOid()+") and state>2 and org="+user.getOid();
        List<Object[]> objectList= trainingExamDao.getObjectListBySQL(sql);
        if (objectList.size() > 0 && !objectList.isEmpty()) {
            String s = JSON.toJSONString(objectList.get(0));
            exam=Integer.valueOf(s);
        }
        return exam;
    }

    @Override
    public RespExamInfoForUser practiceExam(User user, int id) {
        RespExamInfoForUser respExamInfoForUser=new RespExamInfoForUser();
        TrainingExam trainingExam=trainingExamDao.get(id);
        Calendar trainingExamEndTime= Calendar.getInstance();
        trainingExamEndTime.setTime(trainingExam.getEndTime());
        Date date=new Date();
        Calendar calendar = Calendar.getInstance();
        respExamInfoForUser.setDate(date);
        respExamInfoForUser.setStartTime(date);
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, trainingExam.getAnswerDuration());
        if(trainingExamEndTime.getTime().compareTo(calendar.getTime())<0)
        {
            calendar.setTime(trainingExamEndTime.getTime());
        }
        respExamInfoForUser.setEndTime(calendar.getTime());
        List<TrainingExamBank> trainingExamBankList=new ArrayList<>();
        List<TrainingExamRule> questionScoreList;
        if(trainingExam.getPrimaryExam()!=null)
        {
            String hqlbank = "from TrainingExamBank where exam=" + trainingExam.getPrimaryExam();
            trainingExamBankList = trainingExamBankDao.getListByHQLWithNamedParams(hqlbank, null);
            String hqlrule = "from TrainingExamRule where exam=" +  trainingExam.getPrimaryExam();
            questionScoreList =trainingExamRuleDao.getListByHQLWithNamedParams(hqlrule, null);

        }else {
            String hqlbank = "from TrainingExamBank where exam=" + id;
            trainingExamBankList = trainingExamBankDao.getListByHQLWithNamedParams(hqlbank, null);
            String hqlrule = "from TrainingExamRule where exam=" + id;
            questionScoreList =trainingExamRuleDao.getListByHQLWithNamedParams(hqlrule, null);

        }
        TrainingExam practiceExam=new TrainingExam();
        BeanUtils.copyProperties(trainingExam,practiceExam);
        List<RespExamQuestion> respExamQuestionList=new ArrayList<>();
        for(TrainingExamBank trainingExamBank:trainingExamBankList){
            List<RespExamQuestion> respQuestionList=this.addpracticeExamQuestion(user,trainingExamBank);//出卷
            for(RespExamQuestion question:respQuestionList)
                respExamQuestionList.add(question);
        }
        List<RespExamQuestion> respExamQuestionList1=this.updatepracticeExamQuestionScore(questionScoreList,respExamQuestionList);//赋分
        respExamInfoForUser.setRespExamQuestionList(respExamQuestionList1);
        return respExamInfoForUser;
    }

    @Override
    public int endExam(User user, int id) {
        TrainingExam trainingExam=trainingExamDao.get(id);
        Date date=new Date();
        Calendar calendar = Calendar.getInstance();
        RespExamInfoForUser respExamInfoForUser=new RespExamInfoForUser();
        respExamInfoForUser.setDate(date);
        String hql11 = "from TrainingExamUser where user="+user.getUserID()+" and exam ="+id;
        List<TrainingExamUser> trainingExamUserList = userDao.getListByHQLWithNamedParams(hql11,null);
        TrainingExamUser trainingExamUser= trainingExamUserList.get(0);
        if(trainingExamUser.getStopTime()==null||"".equals(trainingExamUser.getStopTime())) {
            trainingExamUser.setStopState(1);
            trainingExamUser.setStopTime(new Date());
            int year=NewDateUtils.getYear(trainingExamUser.getStopTime());
            int month=NewDateUtils.getMonth(trainingExamUser.getStopTime());
            trainingExamUser.setPeroid(100*year+month);
            String hql2 = "from TrainingExamUserAnswer where exam=" + id + " and user=" + trainingExamUser.getUser();
            List<TrainingExamUserAnswer> trainingExamUserAnswerList = trainingExamUserAnswerDao.getListByHQLWithNamedParams(hql2, null);
            int score = 0;
            for (TrainingExamUserAnswer trainingExamUserAnswer : trainingExamUserAnswerList) {
                score = score + trainingExamUserAnswer.getScore();
            }
            trainingExamUser.setScore(score);
            if (score >= trainingExam.getPassingScore())
                trainingExamUser.setPassingState(1);
            else
                trainingExamUser.setPassingState(0);
            HashMap<String, Object> map = new HashMap<>();
            User user1 = userDao.get(trainingExamUser.getUser());
            RespExamForUser respExamForUser = new RespExamForUser();
            respExamForUser.setTrainingExam(trainingExam);
            respExamForUser.setTrainingExamUser(trainingExamUser);
            map.put("respExamForUser", respExamForUser);
            swMessageService.rejectSend(-1, -1, map, user1.getUserID().toString(), "/trainingExamUserList", "试卷提交", "试卷提交", user1, "testPaperApproval");
            trainingExamUserDao.update(trainingExamUser);
            if (trainingExam.getPrimaryExam() != null && trainingExamUser.getPassingState() == 1) {
                TrainingExam primaryExam = trainingExamDao.get(trainingExam.getPrimaryExam());
                String hqlp = "from TrainingExamUser where user=" + user.getUserID() + " and exam =" + primaryExam.getId();
                List<TrainingExamUser> primaryExamUserList = userDao.getListByHQLWithNamedParams(hqlp, null);
                if (primaryExamUserList.size() > 0) {
                    primaryExamUserList.get(0).setPassingState(2);
                    trainingExamUserDao.update(primaryExamUserList.get(0));
                }
                primaryExam.setFailNum(primaryExam.getFailNum() - 1);
                trainingExamDao.update(primaryExam);
            }
        }
        String hql1 = "from TrainingExamUser where exam=" + id+" and stopTime is null";
        List<TrainingExamUser> trainingExamUserList1= trainingExamUserDao.getListByHQLWithNamedParams(hql1, null);
        if(trainingExamUserList1.isEmpty()){
            examOver(id);
        }
        return 1;
    }

    @Override
    public int updatePublicity(User user, Integer id, String publicity, Integer publicityRanking) {
        TrainingExam trainingExam=trainingExamDao.get(id);
        trainingExam.setPublicityRanking(publicityRanking);
        trainingExam.setPublicity(publicity);
        trainingExam.setUpdateDate(new Date());
        trainingExam.setUpdateName(user.getUserName());
        trainingExam.setUpdator(user.getUpdator());
        trainingExamDao.update(trainingExam);
        return 1;
    }

    @Override
    public Integer selectAnswerQuestion(User user, Integer examQuestion) {
        Integer id = null;
        TrainingExamQuestion trainingExamQuestion=trainingExamQuestionDao.get(examQuestion);
        String hql="from TrainingExamUserAnswer where examQuestion="+examQuestion+" and user="+user.getUserID();
        List<TrainingExamUserAnswer> trainingExamUserAnswerList=trainingExamUserAnswerDao.getListByHQLWithNamedParams(hql,null);
        if (trainingExamUserAnswerList.size()>0) {
             id=trainingExamUserAnswerList.get(0).getId();
        }
        return id;
    }

    @Override
    public RespExamInfoForUser addExamQuestionAnswer(User user, Integer examQuestion, String memo, String examKey) {
        RespExamInfoForUser respExamInfoForUser=new RespExamInfoForUser();
        TrainingExamQuestion trainingExamQuestion=trainingExamQuestionDao.get(examQuestion);
        TrainingExam trainingExam=trainingExamDao.get(trainingExamQuestion.getExam());
        TrainingExamUserAnswer trainingExamUserAnswer=new TrainingExamUserAnswer();
        String hql11 = "from TrainingExamUser where user="+user.getUserID()+" and exam ="+trainingExam.getId();
        List<TrainingExamUser> trainingExamUserList = userDao.getListByHQLWithNamedParams(hql11,null);
        if (trainingExamUserList.size()>0) {
            trainingExamUserAnswer.setExamUser(trainingExamUserList.get(0).getId());
            respExamInfoForUser.setStartTime(trainingExamUserList.get(0).getStartTime());
        }
        trainingExamUserAnswer.setOrg(trainingExam.getOrg());
        trainingExamUserAnswer.setExam(trainingExam.getId());
        trainingExamUserAnswer.setUser(user.getUserID());
        trainingExamUserAnswer.setExamQuestion(examQuestion);
        trainingExamUserAnswer.setMemo(memo);
        trainingExamUserAnswer.setExamKey(examKey);
        TrainingQuestion trainingQuestion=trainingQuestionDao.get(trainingExamQuestion.getQuestion());
        TrainingExamKey trainingExamKey=new TrainingExamKey();
        if(examKey!=null&&!"".equals(examKey))
            trainingExamKey=trainingExamKeyDao.get(Integer.valueOf(examKey));
        else{
            String hql111 = "from TrainingExamKey where question ="+trainingQuestion.getId();
            List<TrainingExamKey> trainingExamKeyList = trainingExamKeyDao.getListByHQLWithNamedParams(hql111,null);
            trainingExamKey=trainingExamKeyList.get(0);
        }
        if("1".equals(trainingQuestion.getType()))
        {
            if (trainingExamKey.getIsKey()==1)
           {
               trainingExamUserAnswer.setIsCorrect(1);
               trainingExamUserAnswer.setScore(trainingExamQuestion.getScore());
           }else{
               trainingExamUserAnswer.setIsCorrect(0);
               trainingExamUserAnswer.setScore(0);
           }
        }else {
                 if (Integer.valueOf(memo).equals(trainingExamKey.getIsKey())){
                     trainingExamUserAnswer.setIsCorrect(1);
                     trainingExamUserAnswer.setScore(trainingExamQuestion.getScore());
                 }else{
                     trainingExamUserAnswer.setIsCorrect(0);
                     trainingExamUserAnswer.setScore(0);
                 }
        }
        trainingExamUserAnswer.setCreator(user.getUserID());
        trainingExamUserAnswer.setCreateDate(new Date());
        trainingExamUserAnswer.setCreateName(user.getUserName());
        trainingExamUserAnswerDao.save(trainingExamUserAnswer);
        Calendar calendar = Calendar.getInstance();
        respExamInfoForUser.setStatus(1);
        calendar.setTime(respExamInfoForUser.getStartTime());
        calendar.add(Calendar.MINUTE, trainingExam.getAnswerDuration());
        respExamInfoForUser.setEndTime(calendar.getTime());
        respExamInfoForUser.setAnswerId(trainingExamUserAnswer.getId());
        respExamInfoForUser.setDate(new Date());
        return respExamInfoForUser;
    }

    @Override
    public RespExamInfoForUser updateExamQuestionAnswer(User user, Integer examQuestion, String memo, String examKey, Integer answerId) {
        RespExamInfoForUser respExamInfoForUser=new RespExamInfoForUser();
        TrainingExamUserAnswer trainingExamUserAnswer= trainingExamUserAnswerDao.get(answerId);
        TrainingExamQuestion trainingExamQuestion=trainingExamQuestionDao.get(examQuestion);
        TrainingExam trainingExam=trainingExamDao.get(trainingExamQuestion.getExam());
        String hql11 = "from TrainingExamUser where user="+user.getUserID()+" and exam ="+trainingExam.getId();
        List<TrainingExamUser> trainingExamUserList = userDao.getListByHQLWithNamedParams(hql11,null);
        if (trainingExamUserList.size()>0) {
            trainingExamUserAnswer.setExamUser(trainingExamUserList.get(0).getId());
            respExamInfoForUser.setStartTime(trainingExamUserList.get(0).getStartTime());
        }

        trainingExamUserAnswer.setMemo(memo);
        trainingExamUserAnswer.setExamKey(examKey);
        TrainingQuestion trainingQuestion=trainingQuestionDao.get(trainingExamQuestion.getQuestion());
        TrainingExamKey trainingExamKey=new TrainingExamKey();
        if(examKey!=null&&!"".equals(examKey))
            trainingExamKey=trainingExamKeyDao.get(Integer.valueOf(examKey));
        else{
            String hql111 = "from TrainingExamKey where question ="+trainingQuestion.getId();
            List<TrainingExamKey> trainingExamKeyList = trainingExamKeyDao.getListByHQLWithNamedParams(hql111,null);
            trainingExamKey=trainingExamKeyList.get(0);
        }
        if("1".equals(trainingQuestion.getType()))
        {
            if (trainingExamKey.getIsKey()==1)
            {
                trainingExamUserAnswer.setIsCorrect(1);
                trainingExamUserAnswer.setScore(trainingExamQuestion.getScore());
            }else{
                trainingExamUserAnswer.setIsCorrect(0);
                trainingExamUserAnswer.setScore(0);
            }
        }else {
            if (Integer.valueOf(memo).equals(trainingExamKey.getIsKey())){
                trainingExamUserAnswer.setIsCorrect(1);
                trainingExamUserAnswer.setScore(trainingExamQuestion.getScore());
            }else{
                trainingExamUserAnswer.setIsCorrect(0);
                trainingExamUserAnswer.setScore(0);
            }
        }
        trainingExamUserAnswer.setCreator(user.getUserID());
        trainingExamUserAnswer.setCreateDate(new Date());
        trainingExamUserAnswer.setCreateName(user.getUserName());
        trainingExamUserAnswerDao.update(trainingExamUserAnswer);
        Calendar calendar = Calendar.getInstance();
        respExamInfoForUser.setStatus(1);
        calendar.setTime(respExamInfoForUser.getStartTime());
        calendar.add(Calendar.MINUTE, trainingExam.getAnswerDuration());
        respExamInfoForUser.setEndTime(calendar.getTime());
        respExamInfoForUser.setAnswerId(trainingExamUserAnswer.getId());
        respExamInfoForUser.setDate(new Date());
        return respExamInfoForUser;
    }

    @Override
    public List<User> selectStaffByOrgList(int org) {
        String hql = "from User where oid = "+ org +" and roleCode in ('staff','agent','super') and isDuty in ('1','9')";
        List<User> list = userDao.getListByHQLWithNamedParams(hql,null);
        return  list;
    }

    @Override
    public RespCountQuestion  selectOptionalQuestionCount(int id) {
        RespCountQuestion respCountQuestion =new RespCountQuestion();
        TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(id);
        String hqlTf = "from TrainingMaterial where bank="+ id+" and enabled=1 and tfNum>0 ";
        List<TrainingMaterial> listTf = trainingMaterialDao.getListByHQLWithNamedParams(hqlTf,null);
        int tfNum=listTf.size();//只有判断题总数
        String hqlChoice = "from TrainingMaterial where bank="+ id+" and enabled=1 and choiceNum>0";
        List<TrainingMaterial> listChoice = trainingMaterialDao.getListByHQLWithNamedParams(hqlChoice,null);
        int choiceNum=listChoice.size();//只有选择题总数
        String hqlAll = "from TrainingMaterial where bank="+ id+" and(( enabled=1 and tfNum>0) or( choiceNum>0 and enabled=1)) ";
        List<TrainingMaterial> listAll = trainingMaterialDao.getListByHQLWithNamedParams(hqlAll,null);
        int all=listAll.size();//判断题，选择题都有的总数

        respCountQuestion.settFNum(tfNum);
        respCountQuestion.setChoiceNum(choiceNum);
        respCountQuestion.setSum(all);
        respCountQuestion.setTrainingQuestionBank(trainingQuestionBank);
        return respCountQuestion;
    }

    @Override
    public int addTrainingExam(User user, ReqTrainingExam reqTrainingExam) {
        int status=1;
        TrainingExam trainingExam= reqTrainingExam.getTrainingExam();
        List<TrainingExamUser> userList = JSONArray.parseArray(reqTrainingExam.getUserList(), TrainingExamUser.class);
        List<TrainingExamBank> examBankList = JSONArray.parseArray(reqTrainingExam.getExamBank(), TrainingExamBank.class);
        List<TrainingExamRule> questionScoreList = JSONArray.parseArray(reqTrainingExam.getQuestionScore(), TrainingExamRule.class);
        int tfNum=0,choiceNum=0;
        for (TrainingExamBank trainingExamBank:examBankList){
            tfNum=tfNum+trainingExamBank.getTfNum();
            choiceNum=choiceNum+trainingExamBank.getChoiceNum();
        }
        trainingExam.setOrg(user.getOid());
        trainingExam.setState("2");
        trainingExam.setResitTimes(0);
        trainingExam.setEnabled(1);
        trainingExam.setEnabledTime(new Date());
        trainingExam.setBeginTime(new Date());
        trainingExam.setBankNum(examBankList.size());
        trainingExam.setTfNum(tfNum);
        trainingExam.setChoiceNum(choiceNum);
        trainingExam.setUserNum(userList.size());
        trainingExam.setOnePassNum(0);
        trainingExam.setFailNum(0);
        trainingExam.setCreator(user.getUserID());
        trainingExam.setCreateDate(new Date());
        trainingExam.setCreateName(user.getUserName());
        trainingExamDao.save(trainingExam);

        for(TrainingExamUser trainingExamUser:userList){
            trainingExamUser.setOrg(user.getOid());
            trainingExamUser.setExam(trainingExam.getId());
            trainingExamUser.setAnswerTimes(0);
            trainingExamUser.setCreator(user.getUserID());
            trainingExamUser.setCreateDate(new Date());
            trainingExamUser.setCreateName(user.getUserName());
            trainingExamUserDao.save(trainingExamUser);
        }

        for(TrainingExamBank trainingExamBank:examBankList){
            trainingExamBank.setOrg(user.getOid());
            trainingExamBank.setExam(trainingExam.getId());
            trainingExamBank.setCreator(user.getUserID());
            trainingExamBank.setCreateDate(new Date());
            trainingExamBank.setCreateName(user.getUserName());
            trainingExamBankDao.save(trainingExamBank);
            status=this.addTrainingExamQuestion(user,trainingExamBank);
            if (status==0)
                return -1;
        }

        for(TrainingExamRule trainingExamRule: questionScoreList){
            trainingExamRule.setOrg(user.getOid());
            trainingExamRule.setExam(trainingExam.getId());
            trainingExamRule.setCreator(user.getUserID());
            trainingExamRule.setCreateDate(new Date());
            trainingExamRule.setCreateName(user.getUserName());
            trainingExamRuleDao.save(trainingExamRule);
      //      System.out.println(trainingExamRule.getId()+"++++"+trainingExamRule.getScore());
        }
        this.updateExamQuestionScore(questionScoreList,trainingExam);
        for(TrainingExamUser trainingExamUser:userList) {
            String messageCont = "现有交卷截止时间为" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(trainingExam.getEndTime()) + "的考核，请及时答题！";
            String memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
                    , trainingExamUser.getUser(), "", trainingExam.getId());//推送我的消息
            User user1=userDao.get(trainingExamUser.getUser());
            //推送
            HashMap<String, Object> map = new HashMap<>();
            RespExamForUser respExamForUser=new RespExamForUser();
            respExamForUser.setTrainingExam(trainingExam);
            respExamForUser.setTrainingExamUser(trainingExamUser);
            map.put("respExamForUser", respExamForUser);
            swMessageService.rejectSend(1,1,map,user1.getUserID().toString(),"/trainingExamUserList",messageCont,messageCont,user1,"testPaperApproval");

        }
        Date endDate= NewDateUtils.tomorrow();
   //     endDate=NewDateUtils.changeHour(endDate,1);
        endDate=new Date(endDate.getTime()+ TimeUnit.HOURS.toMillis(1));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(trainingExam.getEndTime());
        calendar.add(Calendar.SECOND, 1);
        if(calendar.getTime().compareTo(endDate)<0) {
            ExamOverCallback examOverCallback = new ExamOverCallback(trainingExam.getId());
            clusterMessageSendingOperations.delayCall(calendar.getTime(), examOverCallback);
        }
        return status;
    }

    @Override
    public int addTrainingExamAgain(User user, int id, Date endTime) {
        String hqluser = "from TrainingExamUser where exam=" + id +" and passingState=0";
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hqluser, null);
        String hqlbank = "from TrainingExamBank where exam=" + id;
        List<TrainingExamBank> trainingExamBankList= trainingExamBankDao.getListByHQLWithNamedParams(hqlbank, null);
        String hqlrule = "from TrainingExamRule where exam=" + id;
        List<TrainingExamRule> questionScoreList =trainingExamRuleDao.getListByHQLWithNamedParams(hqlrule, null);
        TrainingExam trainingExam=trainingExamDao.get(id);
        TrainingExam trainingExamAgain=new TrainingExam();
        int tfNum=0,choiceNum=0;
        for (TrainingExamBank trainingExamBank:trainingExamBankList){
            tfNum=tfNum+trainingExamBank.getTfNum();
            choiceNum=choiceNum+trainingExamBank.getChoiceNum();
        }
        trainingExamAgain.setGoal(trainingExam.getGoal());
        trainingExamAgain.setEndTime(endTime);
        Integer endYear=NewDateUtils.getYear(endTime);
        Integer endMonth=NewDateUtils.getMonth(endTime);
        trainingExamAgain.setPeroid(endYear*100+endMonth);
        trainingExamAgain.setAnswerDuration(Integer.valueOf(trainingExam.getAnswerDuration()));
        trainingExamAgain.setPublicity(trainingExam.getPublicity());
        trainingExamAgain.setPublicityRanking(trainingExam.getPublicityRanking());
        trainingExamAgain.setPassingScore(Integer.valueOf(trainingExam.getPassingScore()));
        trainingExamAgain.setOrg(user.getOid());
        trainingExamAgain.setState("2");
        trainingExamAgain.setResitTimes(trainingExam.getResitTimes()+1);
        trainingExamAgain.setEnabled(1);
        trainingExamAgain.setEnabledTime(new Date());
        trainingExamAgain.setBeginTime(new Date());
        trainingExamAgain.setBankNum(trainingExamBankList.size());
        trainingExamAgain.setTfNum(tfNum);
        trainingExamAgain.setChoiceNum(choiceNum);
        trainingExamAgain.setUserNum(trainingExamUserList.size());
        trainingExamAgain.setOnePassNum(0);
        trainingExamAgain.setFailNum(0);
        trainingExamAgain.setCreator(user.getUserID());
        trainingExamAgain.setCreateDate(new Date());
        trainingExamAgain.setCreateName(user.getUserName());
        trainingExamAgain.setPrimaryExam(trainingExam.getId());
        trainingExamDao.save(trainingExamAgain);
        trainingExam.setState("3");
        trainingExam.setResitTimes(trainingExam.getResitTimes()+1);
        trainingExamDao.update(trainingExam);

        for(TrainingExamUser trainingExamUserOld:trainingExamUserList){
            TrainingExamUser trainingExamUserNew =new TrainingExamUser();
            trainingExamUserNew.setOrg(user.getOid());
            trainingExamUserNew.setUser(trainingExamUserOld.getUser());
            trainingExamUserNew.setExam(trainingExamAgain.getId());
            trainingExamUserNew.setAnswerTimes(trainingExam.getResitTimes());
            trainingExamUserNew.setCreator(user.getUserID());
            trainingExamUserNew.setCreateDate(new Date());
            trainingExamUserNew.setCreateName(user.getUserName());
            trainingExamUserDao.save(trainingExamUserNew);
        }
        for(TrainingExamBank trainingExamBank:trainingExamBankList){
            TrainingExamBank trainingExamBank1 =new TrainingExamBank();
            BeanUtils.copyProperties(trainingExamBank,trainingExamBank1);
            trainingExamBank1.setExam(trainingExamAgain.getId());
            int status=this.addTrainingExamQuestion(user,trainingExamBank1);
            if (status==0)
            {
                this.addTrainingExamQuestionAgain(user,trainingExamBank.getBank(),trainingExamAgain);
            }
        }
        this.updateExamQuestionScore(questionScoreList,trainingExamAgain);
        for(TrainingExamUser trainingExamUser:trainingExamUserList) {
            String messageCont = "您有需补考的考核，交卷截止时间为" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(trainingExamAgain.getEndTime()) + "的考核，请及时答题！";
            String memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
                    , trainingExamUser.getUser(), "", trainingExamAgain.getId());//推送我的消息

            User user1=userDao.get(trainingExamUser.getUser());
            //推送
            HashMap<String, Object> map = new HashMap<>();
            RespExamForUser respExamForUser=new RespExamForUser();
            respExamForUser.setTrainingExam(trainingExamAgain);
            respExamForUser.setTrainingExamUser(trainingExamUser);
            map.put("respExamForUserAgain", respExamForUser);
            swMessageService.rejectSend(1,1,map,user1.getUserID().toString(),"/trainingExamUserList",messageCont,messageCont,user1,"testPaperApproval");
        }
        Date endDate= NewDateUtils.tomorrow();
//        endDate=NewDateUtils.changeHour(endDate,1);
        endDate=new Date(endDate.getTime()+ TimeUnit.HOURS.toMillis(1));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(trainingExamAgain.getEndTime());
        calendar.add(Calendar.SECOND, 1);
        if(calendar.getTime().compareTo(endDate)<0) {
            ExamOverCallback examOverCallback = new ExamOverCallback(trainingExamAgain.getId());
            clusterMessageSendingOperations.delayCall(calendar.getTime(), examOverCallback);
        }
        return 1;
    }

    @Override
    public int addTrainingExamQuestion(User user, TrainingExamBank trainingExamBank) {
        int resultTfNum = trainingExamBank.getTfNum();
        List<TrainingMaterial> listTfMaterial = new ArrayList<>();//确定选择的判断题
        List<TrainingMaterial> listChoiceMaterial = new ArrayList<>();//确定选择的选择题
        int resultChoiceNum = trainingExamBank.getChoiceNum();
        String hqlTf = "from TrainingMaterial where bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum>0 and choiceNum=0";
        List<TrainingMaterial> listTf = trainingMaterialDao.getListByHQLWithNamedParams(hqlTf, null);
        int tfNum = listTf.size();//只有判断题总数
        String hqlChoice = "from TrainingMaterial where bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum=0 and choiceNum>0";
        List<TrainingMaterial> listChoice = trainingMaterialDao.getListByHQLWithNamedParams(hqlChoice, null);
        int choiceNum = listChoice.size();//只有选择题总数
        String hqlAll = "from TrainingMaterial where bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum>0 and choiceNum>0";
        List<TrainingMaterial> listAll = trainingMaterialDao.getListByHQLWithNamedParams(hqlAll, null);
        int all = listAll.size();//判断题，选择题都有的总数
        String hqlSum = "from TrainingMaterial where (bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum>0) or(bank=" + trainingExamBank.getBank() + " and enabled=1 and choiceNum>0)";
        List<TrainingMaterial> listSum = trainingMaterialDao.getListByHQLWithNamedParams(hqlSum, null);
        if(listSum.size()>=(resultTfNum+resultChoiceNum)
                &&resultTfNum <=(all+tfNum) &&resultChoiceNum <=(all+choiceNum)){
            //如果全部选择题都为公共题，剩下的题为选择题的情况下，是否能按照数量生成题库
            if (all - resultChoiceNum + tfNum >= resultTfNum) {
                for (TrainingMaterial trainingMaterial : listAll) {
                    listChoice.add(trainingMaterial);
                }
                Random rnd = new Random();
                for (int i = listChoice.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listChoice.get(i - 1);
                    listChoice.set(i - 1, listChoice.get(j));
                    listChoice.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultChoiceNum; i++) {
                    listChoiceMaterial.add(listChoice.get(i));
                }
                for (TrainingMaterial trainingMaterial : listAll) {
                    int status = 1;
                    for (TrainingMaterial trainingMaterialChoice : listChoiceMaterial) {
                        if (trainingMaterial.getId() == trainingMaterialChoice.getId())
                            status = 0;
                    }
                    if (status == 1) {
                        listTf.add(trainingMaterial);
                    }
                }
                for (int i = listTf.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listTf.get(i - 1);
                    listTf.set(i - 1, listTf.get(j));
                    listTf.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultTfNum; i++) {
                    listTfMaterial.add(listTf.get(i));
                }
            } else {
                int allToTfNum = resultTfNum - tfNum;//最少需要多少道公共题为判断题
                int allToChoiceNumMin = resultChoiceNum - choiceNum;//最少需要多少公共题为选择题
                int allToChoiceNumMax = all - allToTfNum;//最多可以有多少道题为选择题
                int limits = allToChoiceNumMax - allToChoiceNumMin;
                Random rnd = new Random();
                int size = rnd.nextInt(limits + 1) + allToChoiceNumMin;
                for (int i = listAll.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listAll.get(i - 1);
                    listAll.set(i - 1, listAll.get(j));
                    listAll.set(j, trainingMaterial);
                }
                for (int i = 0; i < size; i++) {
                    listChoice.add(listAll.get(i));
                }
                for (int i = listChoice.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listChoice.get(i - 1);
                    listChoice.set(i - 1, listChoice.get(j));
                    listChoice.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultChoiceNum; i++) {
                    listChoiceMaterial.add(listChoice.get(i));
                }
                for (TrainingMaterial trainingMaterial : listAll) {
                    int status = 1;
                    for (TrainingMaterial trainingMaterialChoice : listChoiceMaterial) {
                        if (trainingMaterial.getId() == trainingMaterialChoice.getId())
                            status = 0;
                    }
                    if (status == 1) {
                        listTf.add(trainingMaterial);
                    }
                }
                for (int i = listTf.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listTf.get(i - 1);
                    listTf.set(i - 1, listTf.get(j));
                    listTf.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultTfNum; i++) {
                    listTfMaterial.add(listTf.get(i));
                }
            }
            Random rnd = new Random();
            for (TrainingMaterial trainingMaterialChoice : listChoiceMaterial) {
                String hql = "from TrainingQuestion where material=" + trainingMaterialChoice.getId() + " and enabled=1 and type='1'";
                List<TrainingQuestion> list = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
                int j = rnd.nextInt(list.size());
                TrainingExamQuestion trainingExamQuestion = new TrainingExamQuestion();
                trainingExamQuestion.setOrg(user.getOid());
                trainingExamQuestion.setExam(trainingExamBank.getExam());
                trainingExamQuestion.setQuestion(list.get(j).getId());
                trainingExamQuestion.setCreator(user.getUserID());
                trainingExamQuestion.setCreateDate(new Date());
                trainingExamQuestion.setCreateName(user.getUserName());
                trainingExamQuestion.setOrders(1);
                trainingExamQuestionDao.save(trainingExamQuestion);
                String hqlKeyTrue = "from TrainingQuestionKey where question=" + trainingExamQuestion.getQuestion() + " and enabled=1 and isKey=1";
                List<TrainingQuestionKey> listKeyTrue = trainingQuestionKeyDao.getListByHQLWithNamedParams(hqlKeyTrue, null);

                String hqlKeyFalse = "from TrainingQuestionKey where question=" + trainingExamQuestion.getQuestion() + " and enabled=1 and isKey=0";
                List<TrainingQuestionKey> listKeyFalse = trainingQuestionKeyDao.getListByHQLWithNamedParams(hqlKeyFalse, null);
                List<TrainingQuestionKey> listKey = new ArrayList<>();
                if (listKeyFalse.size() > 3) {
                    for (int i = listKeyFalse.size(); i > 1; i--) {
                        int x = rnd.nextInt(i);
                        TrainingQuestionKey trainingQuestionKey = listKeyFalse.get(i - 1);
                        listKeyFalse.set(i - 1, listKeyFalse.get(x));
                        listKeyFalse.set(x, trainingQuestionKey);
                    }
                }
                for (int i = 0; i < 3; i++) {
                    listKey.add(listKeyFalse.get(i));
                }
                listKey.add(listKeyTrue.get(0));
                for (int i = listKey.size(); i > 1; i--) {
                    int x = rnd.nextInt(i);
                    TrainingQuestionKey trainingQuestionKey = listKey.get(i - 1);
                    listKey.set(i - 1, listKey.get(x));
                    listKey.set(x, trainingQuestionKey);
                }
                int code=1;
                for (TrainingQuestionKey trainingQuestionKey : listKey) {
                    TrainingExamKey trainingExamKey = new TrainingExamKey();
                    trainingExamKey.setOrg(trainingQuestionKey.getOrg());
                    trainingExamKey.setBank(trainingQuestionKey.getBank());
                    trainingExamKey.setMaterial(trainingQuestionKey.getMaterial());
                    trainingExamKey.setQuestion(trainingQuestionKey.getQuestion());
                    trainingExamKey.setQuestionKey(trainingQuestionKey.getId());
                    trainingExamKey.setExam(trainingExamBank.getExam());
                    if(code==1)
                        trainingExamKey.setCode("A");
                    else if(code==2)
                        trainingExamKey.setCode("B");
                    else if(code==3)
                        trainingExamKey.setCode("C");
                    else if(code==4)
                        trainingExamKey.setCode("D");
                    code++;
                    trainingExamKey.setExamQuestion(trainingExamQuestion.getId());
                    trainingExamKey.setContent(trainingQuestionKey.getContent());
                    trainingExamKey.setIsKey(trainingQuestionKey.getIsKey());
                    trainingExamKey.setEnabled(1);
                    trainingExamKey.setCreator(user.getUserID());
                    trainingExamKey.setCreateDate(new Date());
                    trainingExamKey.setCreateName(user.getUserName());
                    trainingExamKeyDao.save(trainingExamKey);
                }
            }
            for (TrainingMaterial trainingMaterialTf : listTfMaterial) {
                String hql = "from TrainingQuestion where material=" + trainingMaterialTf.getId() + " and enabled=1 and type='2'";
                List<TrainingQuestion> list = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
                int j = rnd.nextInt(list.size());
                TrainingExamQuestion trainingExamQuestion = new TrainingExamQuestion();
                trainingExamQuestion.setOrg(user.getOid());
                trainingExamQuestion.setExam(trainingExamBank.getExam());
                trainingExamQuestion.setQuestion(list.get(j).getId());
                trainingExamQuestion.setCreator(user.getUserID());
                trainingExamQuestion.setCreateDate(new Date());
                trainingExamQuestion.setCreateName(user.getUserName());
                trainingExamQuestion.setOrders(3);
                trainingExamQuestionDao.save(trainingExamQuestion);

                String hqlKey = "from TrainingQuestionKey where question=" + trainingExamQuestion.getQuestion() + " and enabled=1";
                List<TrainingQuestionKey> listKey = trainingQuestionKeyDao.getListByHQLWithNamedParams(hqlKey, null);
                TrainingQuestionKey trainingQuestionKey = listKey.get(0);
                TrainingExamKey trainingExamKey = new TrainingExamKey();
                trainingExamKey.setOrg(trainingQuestionKey.getOrg());
                trainingExamKey.setBank(trainingQuestionKey.getBank());
                trainingExamKey.setMaterial(trainingQuestionKey.getMaterial());
                trainingExamKey.setQuestion(trainingQuestionKey.getQuestion());
                trainingExamKey.setQuestionKey(trainingQuestionKey.getId());
                trainingExamKey.setExam(trainingExamBank.getExam());
                trainingExamKey.setExamQuestion(trainingExamQuestion.getId());
                trainingExamKey.setContent(trainingQuestionKey.getContent());
                trainingExamKey.setIsKey(trainingQuestionKey.getIsKey());
                trainingExamKey.setEnabled(1);
                trainingExamKey.setCreator(user.getUserID());
                trainingExamKey.setCreateDate(new Date());
                trainingExamKey.setCreateName(user.getUserName());
                trainingExamKeyDao.save(trainingExamKey);
            }
            return 1;
        }else{
            return 0;
        }
    }

    @Override
    public List<RespExamQuestion> addpracticeExamQuestion(User user, TrainingExamBank trainingExamBank) {
        List<RespExamQuestion> respExamQuestions=new ArrayList<>();
        int resultTfNum = trainingExamBank.getTfNum();
        List<TrainingMaterial> listTfMaterial = new ArrayList<>();//确定选择的判断题
        List<TrainingMaterial> listChoiceMaterial = new ArrayList<>();//确定选择的选择题
        int resultChoiceNum = trainingExamBank.getChoiceNum();
        String hqlTf = "from TrainingMaterial where bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum>0 and choiceNum=0";
        List<TrainingMaterial> listTf = trainingMaterialDao.getListByHQLWithNamedParams(hqlTf, null);
        int tfNum = listTf.size();//只有判断题总数
        String hqlChoice = "from TrainingMaterial where bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum=0 and choiceNum>0";
        List<TrainingMaterial> listChoice = trainingMaterialDao.getListByHQLWithNamedParams(hqlChoice, null);
        int choiceNum = listChoice.size();//只有选择题总数
        String hqlAll = "from TrainingMaterial where bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum>0 and choiceNum>0";
        List<TrainingMaterial> listAll = trainingMaterialDao.getListByHQLWithNamedParams(hqlAll, null);
        int all = listAll.size();//判断题，选择题都有的总数
        String hqlSum = "from TrainingMaterial where (bank=" + trainingExamBank.getBank() + " and enabled=1 and tfNum>0) or(bank=" + trainingExamBank.getBank() + " and enabled=1 and choiceNum>0)";
        List<TrainingMaterial> listSum = trainingMaterialDao.getListByHQLWithNamedParams(hqlSum, null);
        if(listSum.size()>=(resultTfNum+resultChoiceNum)
                &&resultTfNum <=(all+tfNum) &&resultChoiceNum <=(all+choiceNum)) {
            //如果全部选择题都为公共题，剩下的题为选择题的情况下，是否能按照数量生成题库
            if (all - resultChoiceNum + tfNum >= resultTfNum) {
                for (TrainingMaterial trainingMaterial : listAll) {
                    listChoice.add(trainingMaterial);
                }
                Random rnd = new Random();
                for (int i = listChoice.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listChoice.get(i - 1);
                    listChoice.set(i - 1, listChoice.get(j));
                    listChoice.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultChoiceNum; i++) {
                    listChoiceMaterial.add(listChoice.get(i));
                }
                for (TrainingMaterial trainingMaterial : listAll) {
                    int status = 1;
                    for (TrainingMaterial trainingMaterialChoice : listChoiceMaterial) {
                        if (trainingMaterial.getId() == trainingMaterialChoice.getId())
                            status = 0;
                    }
                    if (status == 1) {
                        listTf.add(trainingMaterial);
                    }
                }
                for (int i = listTf.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listTf.get(i - 1);
                    listTf.set(i - 1, listTf.get(j));
                    listTf.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultTfNum; i++) {
                    listTfMaterial.add(listTf.get(i));
                }
            } else {
                int allToTfNum = resultTfNum - tfNum;//最少需要多少道公共题为判断题
                int allToChoiceNumMin = resultChoiceNum - choiceNum;//最少需要多少公共题为选择题
                int allToChoiceNumMax = all - allToTfNum;//最多可以有多少道题为选择题
                int limits = allToChoiceNumMax - allToChoiceNumMin;
                Random rnd = new Random();
                int size = rnd.nextInt(limits + 1) + allToChoiceNumMin;
                for (int i = listAll.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listAll.get(i - 1);
                    listAll.set(i - 1, listAll.get(j));
                    listAll.set(j, trainingMaterial);
                }
                for (int i = 0; i < size; i++) {
                    listChoice.add(listAll.get(i));
                }
                for (int i = listChoice.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listChoice.get(i - 1);
                    listChoice.set(i - 1, listChoice.get(j));
                    listChoice.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultChoiceNum; i++) {
                    listChoiceMaterial.add(listChoice.get(i));
                }
                for (TrainingMaterial trainingMaterial : listAll) {
                    int status = 1;
                    for (TrainingMaterial trainingMaterialChoice : listChoiceMaterial) {
                        if (trainingMaterial.getId() == trainingMaterialChoice.getId())
                            status = 0;
                    }
                    if (status == 1) {
                        listTf.add(trainingMaterial);
                    }
                }
                for (int i = listTf.size(); i > 1; i--) {
                    int j = rnd.nextInt(i);
                    TrainingMaterial trainingMaterial = listTf.get(i - 1);
                    listTf.set(i - 1, listTf.get(j));
                    listTf.set(j, trainingMaterial);
                }
                for (int i = 0; i < resultTfNum; i++) {
                    listTfMaterial.add(listTf.get(i));
                }
            }
            Random rnd = new Random();
            for (TrainingMaterial trainingMaterialChoice : listChoiceMaterial) {
                RespExamQuestion respExamQuestion=new RespExamQuestion();
                String hql = "from TrainingQuestion where material=" + trainingMaterialChoice.getId() + " and enabled=1 and type='1'";
                List<TrainingQuestion> list = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
                int j = rnd.nextInt(list.size());
                respExamQuestion.setTrainingQuestion(list.get(j));
                TrainingExamQuestion trainingExamQuestion = new TrainingExamQuestion();
                trainingExamQuestion.setOrg(user.getOid());
                trainingExamQuestion.setExam(trainingExamBank.getExam());
                trainingExamQuestion.setQuestion(list.get(j).getId());
                trainingExamQuestion.setCreator(user.getUserID());
                trainingExamQuestion.setCreateDate(new Date());
                trainingExamQuestion.setCreateName(user.getUserName());
                trainingExamQuestion.setOrders(1);
                respExamQuestion.setTrainingExamQuestion(trainingExamQuestion);
                String hqlKeyTrue = "from TrainingQuestionKey where question=" + trainingExamQuestion.getQuestion() + " and enabled=1 and isKey=1";
                List<TrainingQuestionKey> listKeyTrue = trainingQuestionKeyDao.getListByHQLWithNamedParams(hqlKeyTrue, null);

                String hqlKeyFalse = "from TrainingQuestionKey where question=" + trainingExamQuestion.getQuestion() + " and enabled=1 and isKey=0";
                List<TrainingQuestionKey> listKeyFalse = trainingQuestionKeyDao.getListByHQLWithNamedParams(hqlKeyFalse, null);
                List<TrainingQuestionKey> listKey = new ArrayList<>();
                if (listKeyFalse.size() > 3) {
                    for (int i = listKeyFalse.size(); i > 1; i--) {
                        int x = rnd.nextInt(i);
                        TrainingQuestionKey trainingQuestionKey = listKeyFalse.get(i - 1);
                        listKeyFalse.set(i - 1, listKeyFalse.get(x));
                        listKeyFalse.set(x, trainingQuestionKey);
                    }
                }
                for (int i = 0; i < 3; i++) {
                    listKey.add(listKeyFalse.get(i));
                }
                listKey.add(listKeyTrue.get(0));
                for (int i = listKey.size(); i > 1; i--) {
                    int x = rnd.nextInt(i);
                    TrainingQuestionKey trainingQuestionKey = listKey.get(i - 1);
                    listKey.set(i - 1, listKey.get(x));
                    listKey.set(x, trainingQuestionKey);
                }
                int code = 1;
                List<TrainingExamKey> trainingExamKeyList=new ArrayList<>();
                for (TrainingQuestionKey trainingQuestionKey : listKey) {
                    TrainingExamKey trainingExamKey = new TrainingExamKey();
                    trainingExamKey.setOrg(trainingQuestionKey.getOrg());
                    trainingExamKey.setBank(trainingQuestionKey.getBank());
                    trainingExamKey.setMaterial(trainingQuestionKey.getMaterial());
                    trainingExamKey.setQuestion(trainingQuestionKey.getQuestion());
                    trainingExamKey.setQuestionKey(trainingQuestionKey.getId());
                    trainingExamKey.setExam(trainingExamBank.getExam());
                    if (code == 1)
                        trainingExamKey.setCode("A");
                    else if (code == 2)
                        trainingExamKey.setCode("B");
                    else if (code == 3)
                        trainingExamKey.setCode("C");
                    else if (code == 4)
                        trainingExamKey.setCode("D");
                    code++;
                    trainingExamKey.setExamQuestion(trainingExamQuestion.getId());
                    trainingExamKey.setContent(trainingQuestionKey.getContent());
                    trainingExamKey.setIsKey(trainingQuestionKey.getIsKey());
                    trainingExamKey.setEnabled(1);
                    trainingExamKey.setCreator(user.getUserID());
                    trainingExamKey.setCreateDate(new Date());
                    trainingExamKey.setCreateName(user.getUserName());
                    trainingExamKeyList.add(trainingExamKey);
                }
                respExamQuestion.setTrainingExamKeyList(trainingExamKeyList);
                respExamQuestions.add(respExamQuestion);
            }
            for (TrainingMaterial trainingMaterialTf : listTfMaterial) {
                RespExamQuestion respExamQuestion=new RespExamQuestion();
                String hql = "from TrainingQuestion where material=" + trainingMaterialTf.getId() + " and enabled=1 and type='2'";
                List<TrainingQuestion> list = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
                int j = rnd.nextInt(list.size());
                respExamQuestion.setTrainingQuestion(list.get(j));
                TrainingExamQuestion trainingExamQuestion = new TrainingExamQuestion();
                trainingExamQuestion.setOrg(user.getOid());
                trainingExamQuestion.setExam(trainingExamBank.getExam());
                trainingExamQuestion.setQuestion(list.get(j).getId());
                trainingExamQuestion.setCreator(user.getUserID());
                trainingExamQuestion.setCreateDate(new Date());
                trainingExamQuestion.setCreateName(user.getUserName());
                trainingExamQuestion.setOrders(3);
                respExamQuestion.setTrainingExamQuestion(trainingExamQuestion);

                String hqlKey = "from TrainingQuestionKey where question=" + trainingExamQuestion.getQuestion() + " and enabled=1";
                List<TrainingQuestionKey> listKey = trainingQuestionKeyDao.getListByHQLWithNamedParams(hqlKey, null);
                TrainingQuestionKey trainingQuestionKey = listKey.get(0);
                TrainingExamKey trainingExamKey = new TrainingExamKey();
                trainingExamKey.setOrg(trainingQuestionKey.getOrg());
                trainingExamKey.setBank(trainingQuestionKey.getBank());
                trainingExamKey.setMaterial(trainingQuestionKey.getMaterial());
                trainingExamKey.setQuestion(trainingQuestionKey.getQuestion());
                trainingExamKey.setQuestionKey(trainingQuestionKey.getId());
                trainingExamKey.setExam(trainingExamBank.getExam());
                trainingExamKey.setExamQuestion(trainingExamQuestion.getId());
                trainingExamKey.setContent(trainingQuestionKey.getContent());
                trainingExamKey.setIsKey(trainingQuestionKey.getIsKey());
                trainingExamKey.setEnabled(1);
                trainingExamKey.setCreator(user.getUserID());
                trainingExamKey.setCreateDate(new Date());
                trainingExamKey.setCreateName(user.getUserName());
                List<TrainingExamKey> trainingExamKeyList=new ArrayList<>();
                trainingExamKeyList.add(trainingExamKey);
                respExamQuestion.setTrainingExamKeyList(trainingExamKeyList);
                respExamQuestions.add(respExamQuestion);
            }
        }
        return respExamQuestions;
    }

    public void addTrainingExamQuestionAgain(User user, int bank,TrainingExam trainingExamAgain)
    {
        String hql = "from TrainingExamQuestion where exam=" + trainingExamAgain.getPrimaryExam();
        List<TrainingExamQuestion> list = trainingExamQuestionDao.getListByHQLWithNamedParams(hql, null);
        List<TrainingExamQuestion> trainingExamQuestionList=new ArrayList<>();
        for(TrainingExamQuestion trainingExamQuestion :list){
            TrainingQuestion trainingQuestion=trainingQuestionDao.get(trainingExamQuestion.getQuestion());
            if(trainingQuestion.getBank()==bank)
                trainingExamQuestionList.add(trainingExamQuestion);
        }
        for(TrainingExamQuestion  t :trainingExamQuestionList) {
            TrainingExamQuestion trainingExamQuestion=new TrainingExamQuestion();
            trainingExamQuestion.setOrg(t.getOrg());
            trainingExamQuestion.setExam(trainingExamAgain.getId());
            trainingExamQuestion.setQuestion(t.getQuestion());
            trainingExamQuestion.setCreator(user.getUserID());
            trainingExamQuestion.setCreateDate(new Date());
            trainingExamQuestion.setCreateName(user.getUserName());
            if(t.getOrders()>=3)
                trainingExamQuestion.setOrders(3);
            else
                trainingExamQuestion.setOrders(1);
            trainingExamQuestionDao.save(trainingExamQuestion);
            Random rnd = new Random();
            String hqlkey = "from TrainingExamKey where examQuestion=" + t.getId();
            List<TrainingExamKey> listkey = trainingExamKeyDao.getListByHQLWithNamedParams(hqlkey, null);
            if(listkey.size()>1){
                for (int i = listkey.size(); i > 1; i--) {
                    int x = rnd.nextInt(i);
                    TrainingExamKey trainingExamKey = listkey.get(i - 1);
                    listkey.set(i - 1, listkey.get(x));
                    listkey.set(x, trainingExamKey);
                }
            }
            for(TrainingExamKey k: listkey){
                TrainingExamKey trainingExamKey = new TrainingExamKey();
                trainingExamKey.setOrg(k.getOrg());
                trainingExamKey.setBank(k.getBank());
                trainingExamKey.setMaterial(k.getMaterial());
                trainingExamKey.setQuestion(k.getQuestion());
                trainingExamKey.setQuestionKey(k.getId());
                trainingExamKey.setExam(trainingExamAgain.getId());
                trainingExamKey.setExamQuestion(trainingExamQuestion.getId());
                trainingExamKey.setContent(k.getContent());
                trainingExamKey.setIsKey(k.getIsKey());
                trainingExamKey.setEnabled(1);
                trainingExamKey.setCreator(user.getUserID());
                trainingExamKey.setCreateDate(new Date());
                trainingExamKey.setCreateName(user.getUserName());
                trainingExamKeyDao.save(trainingExamKey);
            }
        }
    }
    @Override
    public void updateExamQuestionScore(List<TrainingExamRule> questionScoreList, TrainingExam trainingExam){
        Random rnd = new Random();
        int tfScore=0;
        int tfSum=0;
        int tfSpecialScore=0;
        int choiceScore=0;
        int choiceSum=0;
        int choiceSpecialScore=0;
        for(TrainingExamRule trainingExamRule:questionScoreList) {
            if (trainingExamRule.getScore() != null) {
                if ("1".equals(trainingExamRule.getQuestionType())) {
                    if ("1".equals(trainingExamRule.getScoreType())) {
                        choiceScore = trainingExamRule.getScore();
                        choiceSum = trainingExamRule.getQuestionNum();
                    } else {
                        choiceSpecialScore = trainingExamRule.getScore();
                    }
                } else {
                    if ("1".equals(trainingExamRule.getScoreType())) {
                        tfScore = trainingExamRule.getScore();
                        tfSum = trainingExamRule.getQuestionNum();
                    } else {
                        tfSpecialScore = trainingExamRule.getScore();
                    }
                }
            }
        }
        String hql = "from TrainingExamQuestion where exam="+trainingExam.getId()+" and  orders=1";
        List<TrainingExamQuestion> list = trainingExamQuestionDao.getListByHQLWithNamedParams(hql, null);
        for (int i = list.size(); i > 1; i--) {
            int x = rnd.nextInt(i);
            TrainingExamQuestion trainingExamQuestion = list.get(i - 1);
            list.set(i - 1, list.get(x));
            list.set(x, trainingExamQuestion);
        }
        for(int i=0;i<list.size();i++)
        {
            if(i<choiceSum){
                list.get(i).setScore(choiceScore);

                trainingExamQuestionDao.update(list.get(i));
            }else{
                list.get(i).setScore(choiceSpecialScore);
                list.get(i).setOrders(2);

                trainingExamQuestionDao.update(list.get(i));
            }
        }
        String hql1 = "from TrainingExamQuestion where exam="+trainingExam.getId()+" and orders=3";
        List<TrainingExamQuestion> list1 = trainingExamQuestionDao.getListByHQLWithNamedParams(hql1, null);
        for (int i = list1.size(); i > 1; i--) {
            int x = rnd.nextInt(i);
            TrainingExamQuestion trainingExamQuestion = list1.get(i - 1);
            list1.set(i - 1, list1.get(x));
            list1.set(x, trainingExamQuestion);
        }
        for(int i=0;i<list1.size();i++)
        {
            if(i<tfSum){
                list1.get(i).setScore(tfScore);

                trainingExamQuestionDao.update(list1.get(i));
            }else{
                list1.get(i).setScore(tfSpecialScore);

                list1.get(i).setOrders(4);
                trainingExamQuestionDao.update(list1.get(i));
            }
        }
    }

    @Override
    public List<RespExamQuestion> updatepracticeExamQuestionScore(List<TrainingExamRule> questionScoreList, List<RespExamQuestion> respExamQuestionList) {
        List<RespExamQuestion> respExamQuestionList1=new ArrayList<>();
        Random rnd = new Random();
        int tfScore=0;
        int tfSum=0;
        int tfSpecialScore=0;
        int choiceScore=0;
        int choiceSum=0;
        int choiceSpecialScore=0;
        for(TrainingExamRule trainingExamRule:questionScoreList) {
            if (trainingExamRule.getScore() != null) {
                if ("1".equals(trainingExamRule.getQuestionType())) {
                    if ("1".equals(trainingExamRule.getScoreType())) {
                        choiceScore = trainingExamRule.getScore();
                        choiceSum = trainingExamRule.getQuestionNum();
                    } else {
                        choiceSpecialScore = trainingExamRule.getScore();
                    }
                } else {
                    if ("1".equals(trainingExamRule.getScoreType())) {
                        tfScore = trainingExamRule.getScore();
                        tfSum = trainingExamRule.getQuestionNum();
                    } else {
                        tfSpecialScore = trainingExamRule.getScore();
                    }
                }
            }
        }
        List<RespExamQuestion> list= new ArrayList<>();
        List<RespExamQuestion> list1= new ArrayList<>();
        for(RespExamQuestion respExamQuestion:respExamQuestionList )
        {
            if(respExamQuestion.getTrainingExamQuestion().getOrders()==1)
                list.add(respExamQuestion);
            else if(respExamQuestion.getTrainingExamQuestion().getOrders()==3)
                list1.add(respExamQuestion);
        }
        for (int i = list.size(); i > 1; i--) {
            int x = rnd.nextInt(i);
            RespExamQuestion trainingExamQuestion = list.get(i - 1);
            list.set(i - 1, list.get(x));
            list.set(x, trainingExamQuestion);
        }
        for(int i=0;i<list.size();i++)
        {
            if(i<choiceSum){
                list.get(i).getTrainingExamQuestion().setScore(choiceScore);
            }else{
                list.get(i).getTrainingExamQuestion().setScore(choiceSpecialScore);
                list.get(i).getTrainingExamQuestion().setOrders(2);
            }
            respExamQuestionList1.add(list.get(i));
        }
        for (int i = list1.size(); i > 1; i--) {
            int x = rnd.nextInt(i);
            RespExamQuestion trainingExamQuestion = list1.get(i - 1);
            list1.set(i - 1, list1.get(x));
            list1.set(x, trainingExamQuestion);
        }
        for(int i=0;i<list1.size();i++)
        {
            if(i<tfSum){
                list1.get(i).getTrainingExamQuestion().setScore(tfScore);
            }else{
                list1.get(i).getTrainingExamQuestion().setScore(tfSpecialScore);
                list1.get(i).getTrainingExamQuestion().setOrders(4);
            }
            respExamQuestionList1.add(list1.get(i));
        }
        return respExamQuestionList1;
    }

    @Override
    public TrainingExam selectExamById(int id) {
        TrainingExam trainingExam=trainingExamDao.get(id);
        return trainingExam;
    }

    @Override
    public List<RespExamBank> selectExamBankListByExam(int id) {
        List<RespExamBank> list=new ArrayList<>();
        String hql = "from TrainingExamBank where exam=" + id;
        List<TrainingExamBank> trainingExamBankList= trainingExamBankDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamBank trainingExamBank:trainingExamBankList)
        {
            RespExamBank respExamBank=new RespExamBank();
            respExamBank.setTrainingExamBank(trainingExamBank);
            TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(trainingExamBank.getBank());
            respExamBank.setTrainingQuestionBank(trainingQuestionBank);
            list.add(respExamBank);
        }
        return list;
    }
    @Override
    public List<RespExamUser> selectExamUserListByExam(int id) {
        List<RespExamUser> list=new ArrayList<>();
        String hql = "from TrainingExamUser where exam=" + id +" order by ranking asc";
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamUser trainingExamUser:trainingExamUserList)
        {
            RespExamUser respExamUser=new RespExamUser();
            respExamUser.setTrainingExamUser(trainingExamUser);
            User user=userDao.get(trainingExamUser.getUser());
            respExamUser.setUser(user);
            list.add(respExamUser);
        }
        return list;
    }

    @Override
    public List<RespExamUser> selectExamFailUserListByExam(int id) {
        List<RespExamUser> list=new ArrayList<>();
        String hql = "from TrainingExamUser where exam=" + id +" and passingState=0";
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamUser trainingExamUser:trainingExamUserList)
        {
            RespExamUser respExamUser=new RespExamUser();
            respExamUser.setTrainingExamUser(trainingExamUser);
            User user=userDao.get(trainingExamUser.getUser());
            respExamUser.setUser(user);
            list.add(respExamUser);
        }
        return list;
    }

    @Override
    public int whetherExamAgainByExam(int id) {
        int status=0;
        String hql = "from TrainingExam where primaryExam=" + id ;
        List<TrainingExam> trainingExamList= trainingExamDao.getListByHQLWithNamedParams(hql, null);
        if(trainingExamList.size()>0)
            status=1;
        return status;
    }

    @Override
    public int selectExamAgainCountByExam(int id) {
        int count=0;
        String hql = "from TrainingExam where primaryExam=" + id +" and state!='4'";
        List<TrainingExam> trainingExamList= trainingExamDao.getListByHQLWithNamedParams(hql, null);
        count=trainingExamList.size();
        return count;
    }

    @Override
    public List<RespExamUser> selectExamAgainUserListByExam(int id) {
        List<RespExamUser> list=new ArrayList<>();
        String hql1="from TrainingExam where primaryExam=" + id;
        List<TrainingExam>trainingExams=trainingExamDao.getListByHQLWithNamedParams(hql1,null);
        String idList="";
        for(TrainingExam trainingExam :trainingExams){
            if ("".equals(idList))
                idList=""+trainingExam.getId();
            else
                idList=idList+","+trainingExam.getId();
        }
        String hql = "from TrainingExamUser where exam=" + id +" and passingState!=1 order by passingState asc";
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamUser trainingExamUser:trainingExamUserList)
        {
            TrainingExamUser trainingExamUser1 = new TrainingExamUser();
            BeanUtils.copyProperties(trainingExamUser,trainingExamUser1);
            String hql2 = "from TrainingExamUser where user=" + trainingExamUser.getUser() +" and exam in("+idList+")";
            List<TrainingExamUser> trainingExamUserList2= trainingExamUserDao.getListByHQLWithNamedParams(hql2, null);
            trainingExamUser1.setAnswerTimes(trainingExamUserList2.size());
            RespExamUser respExamUser=new RespExamUser();
            respExamUser.setTrainingExamUser(trainingExamUser1);
            User user=userDao.get(trainingExamUser.getUser());
            respExamUser.setUser(user);
            list.add(respExamUser);
        }
        return list;
    }

    @Override
    public RespExamAgain selectExamAgainDetail(int id, int user) {
        RespExamAgain respExamAgain =new RespExamAgain();
        RespExamForUser respExamForUser=new RespExamForUser();
        TrainingExam trainingExam=trainingExamDao.get(id);
        respExamForUser.setTrainingExam(trainingExam);
        String hql = "from TrainingExamUser where exam=" + id +" and user="+user;
        List<TrainingExamUser> trainingExamUser= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        respExamForUser.setTrainingExamUser(trainingExamUser.get(0));
        User user1=userDao.get(user);
        respExamForUser.setUser(user1);
        respExamAgain.setRespExamformal(respExamForUser);
        List<RespExamForUser> respExamForUserList=new ArrayList<>();
        String hql1 = "from TrainingExam where primaryExam=" + id;
        List<TrainingExam> trainingExamList= trainingExamDao.getListByHQLWithNamedParams(hql1, null);
        for(TrainingExam trainingExam1: trainingExamList)
        {
            RespExamForUser respExamForUser1=new RespExamForUser();
            respExamForUser1.setTrainingExam(trainingExam1);
            String hql2 = "from TrainingExamUser where exam=" + trainingExam1.getId() +" and user="+user;
            List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql2, null);
            if(trainingExamUserList.size()>0) {
                respExamForUser1.setTrainingExamUser(trainingExamUserList.get(0));
                respExamForUserList.add(respExamForUser1);
            }
        }
        respExamAgain.setRespExamForUserList(respExamForUserList);
        return respExamAgain;
    }

    @Override
    public List<RespExamQuestion> selectExamQuestionListByExam(int id) {
        List<RespExamQuestion> list=new ArrayList<>();
        String hql = "from TrainingExamQuestion where exam=" + id+" order by orders asc";
        List<TrainingExamQuestion> trainingExamQuestionList= trainingExamQuestionDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingExamQuestion trainingExamQuestion:trainingExamQuestionList)
        {
            RespExamQuestion respExamQuestion=new RespExamQuestion();
            respExamQuestion.setTrainingExamQuestion(trainingExamQuestion);
            TrainingQuestion trainingQuestion= trainingQuestionDao.get(trainingExamQuestion.getQuestion());
            respExamQuestion.setTrainingQuestion(trainingQuestion);
            if("1".equals(trainingQuestion.getType())){
                String hql1 = "from TrainingExamKey where examQuestion=" + trainingExamQuestion.getId();
                List<TrainingExamKey> trainingExamKeyList= trainingExamKeyDao.getListByHQLWithNamedParams(hql1, null);
                respExamQuestion.setTrainingExamKeyList(trainingExamKeyList);
            }
            list.add(respExamQuestion);
        }
        return list;
    }

    @Override
    public RespExamUserInfoList selectExamUserInfoList(int org, int peroid) {
        RespExamUserInfoList respExamUserInfoList=new RespExamUserInfoList();
        String sql="SELECT a.*,d.userName,d.mobile,d.departName,d.postName FROM (" +
                "SELECT a.*,IFNULL(c.answerTimes,0) AS answerTimes FROM (" +
                "SELECT a.*,IFNULL(b.examPassing,0) AS examPassing FROM (" +
                "SELECT `user`,peroid,COUNT(*) AS examTotal,avg(score) AS meanScore,max(score) AS highestScore,min(score) AS lowestScore,min(ranking) AS highestRanking,max(ranking) AS lowestRanking FROM `t_training_exam_user` WHERE org="+org+" and stop_time is not null AND answer_times=0 AND peroid="+peroid+" GROUP BY `user`) AS a LEFT JOIN (" +
                "SELECT `user`,COUNT(*) AS examPassing FROM `t_training_exam_user` WHERE org="+org+" and stop_time is not null AND answer_times=0 AND peroid="+peroid+" AND passing_state=1 GROUP BY `user`) AS b ON a.`user`=b.`user`) AS a LEFT JOIN (" +
                "SELECT `user`,COUNT(*) AS answerTimes FROM `t_training_exam_user` WHERE org="+org+" and stop_time is not null AND answer_times> 0 AND peroid="+peroid+" GROUP BY `user`) AS c ON a.`user`=c.`user`) AS a LEFT JOIN t_sys_user AS d ON a.`user`=d.userID ORDER BY CONVERT (d.userName USING gbk) ASC";
        List<Object[]> objectList= trainingExamUserDao.getObjectListBySQL(sql);
        List<RespExamUserInfo> examUserInfoList=new ArrayList<>();
        if (objectList.size() > 0 && !objectList.isEmpty()) {
            for (int i = 0; i <objectList.size(); i++) {
                RespExamUserInfo respExamUserInfo =new RespExamUserInfo();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(objectList.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                respExamUserInfo.setUserId(Integer.valueOf(objects.getString(0)));
                respExamUserInfo.setExamTotal(Integer.valueOf(objects.getString(2)));
                respExamUserInfo.setMeanScore(Double.valueOf(objects.getString(3)));
                respExamUserInfo.setHighestScore(Double.valueOf(objects.getString(4)));
                respExamUserInfo.setLowestScore(Double.valueOf(objects.getString(5)));
                respExamUserInfo.setHighestRanking(Integer.valueOf(objects.getString(6)));
                respExamUserInfo.setLowestRanking(Integer.valueOf(objects.getString(7)));
                respExamUserInfo.setExamPassing(Integer.valueOf(objects.getString(8)));
                respExamUserInfo.setAnswerTimes(Integer.valueOf(objects.getString(9)));
                respExamUserInfo.setUserName(objects.getString(10));
                respExamUserInfo.setMobile(objects.getString(11));
                respExamUserInfo.setDepartName(objects.getString(12));
                respExamUserInfo.setPostName(objects.getString(13));
                examUserInfoList.add(respExamUserInfo);
            }
        }
        respExamUserInfoList.setExamUserInfoList(examUserInfoList);
        return respExamUserInfoList;
    }
    @Override
    public RespExamUserInfo selectExamUserInfo(int org, int peroid, int userId) {
        String sql="SELECT a.*,d.userName,d.mobile,d.departName,d.postName FROM (" +
                "SELECT a.*,IFNULL(c.answerTimes,0) AS answerTimes FROM (" +
                "SELECT a.*,IFNULL(b.examPassing,0) AS examPassing FROM (" +
                "SELECT `user`,peroid,COUNT(*) AS examTotal,avg(score) AS meanScore,max(score) AS highestScore,min(score) AS lowestScore,min(ranking) AS highestRanking,max(ranking) AS lowestRanking FROM `t_training_exam_user` WHERE `user`="+userId+" and stop_time is not null AND answer_times=0 AND peroid="+peroid+" GROUP BY `user`) AS a LEFT JOIN (" +
                "SELECT `user`,COUNT(*) AS examPassing FROM `t_training_exam_user` WHERE `user`="+userId+" and stop_time is not null AND answer_times=0 AND peroid="+peroid+" AND passing_state=1 GROUP BY `user`) AS b ON a.`user`=b.`user`) AS a LEFT JOIN (" +
                "SELECT `user`,COUNT(*) AS answerTimes FROM `t_training_exam_user` WHERE `user`="+userId+" and stop_time is not null AND answer_times> 0 AND peroid="+peroid+" GROUP BY `user`) AS c ON a.`user`=c.`user`) AS a LEFT JOIN t_sys_user AS d ON a.`user`=d.userID ORDER BY CONVERT (d.userName USING gbk) ASC";
        List<Object[]> objectList= trainingExamUserDao.getObjectListBySQL(sql);
        RespExamUserInfo respExamUserInfo =new RespExamUserInfo();
        if (objectList.size() > 0 && !objectList.isEmpty()) {
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(objectList.get(0));
                JSONArray objects = JSONArray.parseArray(s);
                respExamUserInfo.setUserId(Integer.valueOf(objects.getString(0)));
                respExamUserInfo.setExamTotal(Integer.valueOf(objects.getString(2)));
                respExamUserInfo.setMeanScore(Double.valueOf(objects.getString(3)));
                respExamUserInfo.setHighestScore(Double.valueOf(objects.getString(4)));
                respExamUserInfo.setLowestScore(Double.valueOf(objects.getString(5)));
                if(objects.getString(6)!=null)
                    respExamUserInfo.setHighestRanking(Integer.valueOf(objects.getString(6)));
                if(objects.getString(7)!=null)
                    respExamUserInfo.setLowestRanking(Integer.valueOf(objects.getString(7)));
                if(objects.getString(8)!=null)
                    respExamUserInfo.setExamPassing(Integer.valueOf(objects.getString(8)));
                if(objects.getString(9)!=null)
                    respExamUserInfo.setAnswerTimes(Integer.valueOf(objects.getString(9)));
                respExamUserInfo.setUserName(objects.getString(10));
                respExamUserInfo.setMobile(objects.getString(11));
                respExamUserInfo.setDepartName(objects.getString(12));
                respExamUserInfo.setPostName(objects.getString(13));
        }
        String hql="from TrainingExamUser where user="+userId+" and stopTime is not null and answerTimes=0 AND peroid="+peroid;
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        List<TrainingExamUser> trainingExamUserList1=new ArrayList<>();
        Date newDate=new Date();
        for(TrainingExamUser trainingExamUser: trainingExamUserList)
        {
            TrainingExam trainingExam=trainingExamDao.get(trainingExamUser.getExam());
            trainingExamUser.setExamStopTime(trainingExam.getEndTime());
            trainingExamUserList1.add(trainingExamUser);
        }

        respExamUserInfo.setTrainingExamUserList(trainingExamUserList1);
        return respExamUserInfo;
    }

    @Override
    public RespExamUserInfo selectExamUserOnePassInfo(int org, int peroid,int userId) {
        RespExamUserInfo respExamUserInfo =new RespExamUserInfo();
        String hql="from TrainingExamUser where user="+userId+" and stopTime is not null and passingState=1 and answerTimes=0 AND peroid="+peroid;
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        List<TrainingExamUser> trainingExamUserList1=new ArrayList<>();
        Date newDate=new Date();
        for(TrainingExamUser trainingExamUser: trainingExamUserList)
        {
            TrainingExam trainingExam=trainingExamDao.get(trainingExamUser.getExam());
            trainingExamUser.setExamStopTime(trainingExam.getEndTime());
//            if(trainingExam.getEndTime().compareTo(newDate)<=0){
                trainingExamUserList1.add(trainingExamUser);
//            }
        }
        respExamUserInfo.setTrainingExamUserList(trainingExamUserList1);
        return respExamUserInfo;
    }
    @Override
    public RespExamUserInfo selectExamUserAgainInfo(int org, int peroid,int userId) {
        RespExamUserInfo respExamUserInfo =new RespExamUserInfo();
        String hql="from TrainingExamUser where user="+userId+" and stopTime is not null  and answerTimes>0 AND peroid="+peroid;
        List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        List<TrainingExamUser> trainingExamUserList1=new ArrayList<>();
        Date newDate=new Date();
        for(TrainingExamUser trainingExamUser: trainingExamUserList)
        {
            TrainingExam trainingExam=trainingExamDao.get(trainingExamUser.getExam());
            trainingExamUser.setExamStopTime(trainingExam.getEndTime());
//            if(trainingExam.getEndTime().compareTo(newDate)<=0){
                trainingExamUserList1.add(trainingExamUser);
 //           }
        }
        respExamUserInfo.setTrainingExamUserList(trainingExamUserList1);
        return respExamUserInfo;
    }

    @Override
    public int stopExamJudge(User user, int id) {
        int status=1;
        String hql = "from TrainingExamUser where exam=" + id+ " and startTime is not null";
        List<TrainingExamUser> trainingExamUserAnswerList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        if(trainingExamUserAnswerList.size()>0)
            status=-1;
        return status;
    }

    @Override
    public int stopExam(User user, int id) {
        int status=1;
        String hql = "from TrainingExamUser where exam=" + id+ " and startTime is not null";
        List<TrainingExamUser> trainingExamUserAnswerList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
        if(trainingExamUserAnswerList.size()>0)
            status=-1;
        if(status==1)
        {
            TrainingExam trainingExam=trainingExamDao.get(id);
            trainingExam.setEnabled(0);
            trainingExam.setEnabledTime(new Date());
            trainingExam.setUpdateDate(new Date());
            trainingExam.setUpdateName(user.getUserName());
            trainingExam.setUpdator(user.getUserID());
            trainingExamDao.update(trainingExam);
            String hql1 = "from TrainingExamUser where exam=" + id;
            List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql1, null);
            for(TrainingExamUser trainingExamUser:trainingExamUserList) {
                trainingExamUser.setStopTime(new Date());
                trainingExamUserDao.update(trainingExamUser);
                String messageCont = "交卷截止时间为" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(trainingExam.getEndTime()) + "的考核已被终止！";
                String memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
                        , trainingExamUser.getUser(), "", trainingExam.getId());//推送我的消息
                HashMap<String, Object> map = new HashMap<>();
                User user1=userDao.get(trainingExamUser.getUser());
                RespExamForUser respExamForUser=new RespExamForUser();
                respExamForUser.setTrainingExam(trainingExam);
                respExamForUser.setTrainingExamUser(trainingExamUser);
                map.put("respExamForUser", respExamForUser);
                swMessageService.rejectSend(-1,-1,map,user1.getUserID().toString(),"/trainingExamUserList",messageCont,messageCont,user1,"testPaperApproval");
            }
        }
        return status;
    }

    @Override
    public void trainTaskDay() {
        Date beginDate=NewDateUtils.today();
//        beginDate=NewDateUtils.changeHour(beginDate,1);
        beginDate=new Date(beginDate.getTime()+ TimeUnit.HOURS.toMillis(1));
        Date endDate= NewDateUtils.tomorrow();
//        endDate=NewDateUtils.changeHour(endDate,1);
        endDate=new Date(endDate.getTime()+ TimeUnit.HOURS.toMillis(1));
        Map<String, Object> params = new HashMap<>();
        String hql = "from TrainingExam where state=2 and endTime  between :beginDate and :endDate";
        params.put("beginDate", beginDate);
        params.put("endDate", endDate);
        List<TrainingExam> trainingExamList=trainingExamDao.getListByHQLWithNamedParams(hql,params);
        for(TrainingExam trainingExam : trainingExamList) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(trainingExam.getEndTime());
            calendar.add(Calendar.SECOND, 1);
            ExamOverCallback examOverCallback = new ExamOverCallback(trainingExam.getId());
            clusterMessageSendingOperations.delayCall(calendar.getTime(), examOverCallback);
        }


    }

    @Override
    public void exceptionsExam() {
        Date endDate=new Date();
        Map<String, Object> params = new HashMap<>();
        params.put("endDate", endDate);
        String hql = "from TrainingExam where endTime<:endDate and state=2";
        List<TrainingExam> trainingExamList=trainingExamDao.getListByHQLWithNamedParams(hql,params);
        for(TrainingExam trainingExam:trainingExamList)
        {
            this.examOver(trainingExam.getId());
        }
    }

    @Override
    public void initializationPeroid() {
        String hql="from TrainingExam where peroid is null";
        List<TrainingExam> trainingExamList=trainingExamDao.getListByHQLWithNamedParams(hql,null);
        for(TrainingExam trainingExam :trainingExamList) {
            if(trainingExam.getEndTime()!=null){
            Integer endYear = NewDateUtils.getYear(trainingExam.getEndTime());
            Integer endMonth = NewDateUtils.getMonth(trainingExam.getEndTime());
            trainingExam.setPeroid(endYear * 100 + endMonth);
            trainingExamDao.update(trainingExam);
            }
        }
    }
    @Override
    public void initializationUserPeroid() {
        String hql="from TrainingExamUser where peroid is null";
        List<TrainingExamUser> trainingExamUserList=trainingExamUserDao.getListByHQLWithNamedParams(hql,null);
        for(TrainingExamUser trainingExamUser :trainingExamUserList) {
            if(trainingExamUser.getStopTime()!=null){
            Integer endYear = NewDateUtils.getYear(trainingExamUser.getStopTime());
            Integer endMonth = NewDateUtils.getMonth(trainingExamUser.getStopTime());
            trainingExamUser.setPeroid(endYear * 100 + endMonth);
            trainingExamUserDao.update(trainingExamUser);
            }
        }
    }

    @Override
    public void examOver(Integer id) {
        TrainingExam trainingExam=trainingExamDao.get(id);
        if("2".equals(trainingExam.getState())){
            String hql = "from TrainingExamUser where exam=" + id+" and startTime is null";
            List<TrainingExamUser> trainingExamUserList= trainingExamUserDao.getListByHQLWithNamedParams(hql, null);
            for(TrainingExamUser trainingExamUser: trainingExamUserList)
            {
                trainingExamUser.setStopState(3);
                trainingExamUser.setScore(0);
                trainingExamUser.setPassingState(0);
                trainingExamUser.setStopTime(trainingExam.getEndTime());
                trainingExamUser.setStartTime(trainingExamUser.getStopTime());
                trainingExamUserDao.update(trainingExamUser);
                String messageCont = "交卷截止时间为" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(trainingExam.getEndTime()) + "的考核已到截至时间！";
                String memo = "操作时间  系统 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
                        , trainingExamUser.getUser(), "", trainingExam.getId());//推送我的消息
                HashMap<String, Object> map = new HashMap<>();
                User user1=userDao.get(trainingExamUser.getUser());
                RespExamForUser respExamForUser=new RespExamForUser();
                respExamForUser.setTrainingExam(trainingExam);
                respExamForUser.setTrainingExamUser(trainingExamUser);
                map.put("respExamForUser", respExamForUser);
                swMessageService.rejectSend(-1,-1,map,user1.getUserID().toString(),"/trainingExamUserList",messageCont,messageCont,user1,"testPaperApproval");

            }
            String hql1 = "from TrainingExamUser where exam=" + id+" and startTime is not null and stopTime is null";
            List<TrainingExamUser> trainingExamUserList1= trainingExamUserDao.getListByHQLWithNamedParams(hql1, null);
            for(TrainingExamUser trainingExamUser: trainingExamUserList1)
            {
                trainingExamUser.setStopState(2);
                trainingExamUser.setStopTime(trainingExam.getEndTime());
                String hql2 = "from TrainingExamUserAnswer where exam=" + id+" and user="+trainingExamUser.getUser();
                List<TrainingExamUserAnswer> trainingExamUserAnswerList= trainingExamUserAnswerDao.getListByHQLWithNamedParams(hql2, null);
                int score=0;
                for(TrainingExamUserAnswer trainingExamUserAnswer:trainingExamUserAnswerList)
                {
                    score=score+trainingExamUserAnswer.getScore();
                }
                trainingExamUser.setScore(score);
                if(score>=trainingExam.getPassingScore())
                    trainingExamUser.setPassingState(1);
                else
                    trainingExamUser.setPassingState(0);
                trainingExamUserDao.update(trainingExamUser);
//                String messageCont = "交卷截止时间为" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(trainingExam.getEndTime()) + "的考核已到截至时间！";
//                String memo = "操作时间  系统 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//                userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
//                        , trainingExamUser.getUser(), "", trainingExam.getId());//推送我的消息
                HashMap<String, Object> map = new HashMap<>();
                User user1=userDao.get(trainingExamUser.getUser());
                RespExamForUser respExamForUser=new RespExamForUser();
                respExamForUser.setTrainingExam(trainingExam);
                respExamForUser.setTrainingExamUser(trainingExamUser);
                map.put("respExamForUser", respExamForUser);
//                swMessageService.rejectSend(-1,-1,map,user1.getUserID().toString(),"/trainingExamUserList",messageCont,messageCont,user1,"testPaperApproval");
                if(trainingExam.getPrimaryExam()!=null&&trainingExamUser.getPassingState()==1)
                {
                    TrainingExam primaryExam=trainingExamDao.get(trainingExam.getPrimaryExam());
                    String hqlp = "from TrainingExamUser where user="+user1.getUserID()+" and exam ="+primaryExam.getId();
                    List<TrainingExamUser> primaryExamUserList = userDao.getListByHQLWithNamedParams(hqlp,null);
                    if (primaryExamUserList.size()>0)
                    {
                        primaryExamUserList.get(0).setPassingState(2);
                        trainingExamUserDao.update(primaryExamUserList.get(0));
                    }
                    primaryExam.setFailNum(primaryExam.getFailNum()-1);
                    trainingExamDao.update(primaryExam);
                }
            }

            String hql3 = "from TrainingExamUser where exam=" + id+" order by score desc,(stopTime-startTime) asc,startTime asc";
            List<TrainingExamUser> trainingExamUserList3= trainingExamUserDao.getListByHQLWithNamedParams(hql3, null);
            int sum=0;int max=0,min=0;int passNum=0;int noPassNum=0;
            for(int i=0;i<trainingExamUserList3.size();i++){
                trainingExamUserList3.get(i).setRanking(i+1);
            //    System.out.println("到考试结束时间++++"+trainingExamUserList3.get(i).getId()+"+++计算排名++++"+trainingExamUserList3.get(i).getRanking());

                int year=NewDateUtils.getYear(trainingExamUserList3.get(i).getStopTime());
                int month=NewDateUtils.getMonth(trainingExamUserList3.get(i).getStopTime());
                trainingExamUserList3.get(i).setPeroid(100*year+month);
                trainingExamUserDao.update( trainingExamUserList3.get(i));
                sum=sum+trainingExamUserList3.get(i).getScore();
                if (trainingExamUserList3.get(i).getScore()>max)
                    max=trainingExamUserList3.get(i).getScore();
                if(trainingExamUserList3.get(i).getScore()<min||min==0)
                    min=trainingExamUserList3.get(i).getScore();
                if(trainingExamUserList3.get(i).getScore()>=trainingExam.getPassingScore())
                    passNum++;
                else noPassNum++;
//                if(trainingExam.getPrimaryExam()!=null){
//                    TrainingExam primaryExam=trainingExamDao.get(trainingExam.getPrimaryExam());
//                    String hqlp = "from TrainingExamUser where user="+trainingExamUserList3.get(i).getUser()+" and exam ="+primaryExam.getId();
//                    List<TrainingExamUser> primaryExamUserList = userDao.getListByHQLWithNamedParams(hqlp,null);
//                    if (primaryExamUserList.size()>0)
//                    {
//                        if(primaryExamUserList.get(0).getAnswerTimes()!=null)
//                            primaryExamUserList.get(0).setAnswerTimes(primaryExamUserList.get(0).getAnswerTimes());
//                        trainingExamUserDao.update(primaryExamUserList.get(0));
//                    }
//                }
            }
            double average=sum/trainingExamUserList3.size();
            trainingExam.setState("4");
            int year=NewDateUtils.getYear(trainingExam.getEndTime());
            int month=NewDateUtils.getMonth(trainingExam.getEndTime());
            trainingExam.setPeroid(100*year+month);
            trainingExam.setMeanScore(average);
            trainingExam.setOnePassNum(passNum);
            trainingExam.setFailNum(noPassNum);
            trainingExam.setHighestScore(Double.valueOf(max));
            trainingExam.setLowestScore(Double.valueOf(min));
            trainingExamDao.update(trainingExam);
        }

    }
    @Override
    public void answerOver(Integer id) {
        TrainingExamUser trainingExamUser= trainingExamUserDao.get(id);
        if(trainingExamUser.getStopTime()==null||"".equals(trainingExamUser.getStopTime())) {
     //       System.out.println("到考试答题时间++++"+trainingExamUser.getId()+"+++自动交卷");
            Date newDate= new Date();
            TrainingExam trainingExam = trainingExamDao.get(trainingExamUser.getExam());
            trainingExamUser.setStopState(2);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(trainingExamUser.getStartTime());
            calendar.add(Calendar.MINUTE, trainingExam.getAnswerDuration());
            if(newDate.compareTo(calendar.getTime())<0)
            {
                calendar.setTime(newDate);
            }
            trainingExamUser.setStopTime(new Date());
            int year=NewDateUtils.getYear(trainingExamUser.getStopTime());
            int month=NewDateUtils.getMonth(trainingExamUser.getStopTime());
            trainingExamUser.setPeroid(100*year+month);
            String hql2 = "from TrainingExamUserAnswer where exam=" + trainingExam.getId() + " and user=" + trainingExamUser.getUser();
            List<TrainingExamUserAnswer> trainingExamUserAnswerList = trainingExamUserAnswerDao.getListByHQLWithNamedParams(hql2, null);
            int score = 0;
            for (TrainingExamUserAnswer trainingExamUserAnswer : trainingExamUserAnswerList) {
                score = score + trainingExamUserAnswer.getScore();
            }
            trainingExamUser.setScore(score);
            if (score >= trainingExam.getPassingScore())
                trainingExamUser.setPassingState(1);
            else
                trainingExamUser.setPassingState(0);
            trainingExamUserDao.update(trainingExamUser);
            String messageCont = "交卷截止时间为" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(trainingExam.getEndTime()) + "的考核已到截至时间！";
            String memo = "操作时间  系统 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
                    , trainingExamUser.getUser(), "", trainingExam.getId());//推送我的消息
            HashMap<String, Object> map = new HashMap<>();
            User user1=userDao.get(trainingExamUser.getUser());
            RespExamForUser respExamForUser=new RespExamForUser();
            respExamForUser.setTrainingExam(trainingExam);
            respExamForUser.setTrainingExamUser(trainingExamUser);
            map.put("respExamForUser", respExamForUser);
            swMessageService.rejectSend(-1,-1,map,user1.getUserID().toString(),"/trainingExamUserList",messageCont,messageCont,user1,"testPaperApproval");
            if(trainingExam.getPrimaryExam()!=null&&trainingExamUser.getPassingState()==1)
            {
                TrainingExam primaryExam=trainingExamDao.get(trainingExam.getPrimaryExam());
                String hqlp = "from TrainingExamUser where user="+user1.getUserID()+" and exam ="+primaryExam.getId();
                List<TrainingExamUser> primaryExamUserList = userDao.getListByHQLWithNamedParams(hqlp,null);
                if (primaryExamUserList.size()>0)
                {
                    primaryExamUserList.get(0).setPassingState(2);
                    trainingExamUserDao.update(primaryExamUserList.get(0));
                }
                primaryExam.setFailNum(primaryExam.getFailNum()-1);
                trainingExamDao.update(primaryExam);
            }
        }
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=null;
        switch (code){
            case  "testPaperApproval"://我参与的考试
                String hql = "select count(id) from TrainingExamUser where stopTime is null and user=:user ";
                HashMap<String, Object> param = new HashMap<>();
                param.put("user", user.getUserID());
                Long num = (Long) trainingExamUserDao.getByHQLWithNamedParams(hql,param);
                number = num.intValue();
                break;
        }
        return number;
    }

}
