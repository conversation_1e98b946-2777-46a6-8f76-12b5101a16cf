package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.generalAffairs.service.PersonnelAttendanceMonthlyService.WholeDayStatus;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.tuple.Triple;

import java.util.*;

/**
 * 1.201项目 计算考勤中的加班、请假(应提交天数)等天数
 * 2022.1.4  lyx
 */
public class AttendanceUserDaysDto extends BaseEntity {
    private Integer rownum;//排序

    //职工基本信息
    private Integer userID;//人员id
    private String userName; //人员名称
    private String mobile; //手机号码
    private String leader; //从属于某位领导，应存领导的userID(直接上级)
    private Date onDutyDate;	//入职时间
    private Date offDutyDate;	//离职时间
    private String state; //考勤状态 1-考勤 0-不考勤
    private Integer workingDays;  //当前的上班天数
    private Integer outTimeDays; //加班天数
    private Integer leaveDays; //请假天数
    private Integer submissionDays; //截至目前应交天数(计算公式：当前的工作日天数+工作日之外加班天数-一整天请假的天数)
    private Integer notSubmissionDays; //截至目前未交天数(刘洪涛给的方法中获取)

    private Set<Date> workingDates;//当前的上班日列表
    private Set<Date> leaveDates;//请假日列表
    private Set<Date> overtimeDates;//加班天日列表
    private Set<Date> submitedDates;//当前的已交日志日列表
    private Set<Date> submitiondDates;//当前的应交日志日列表
    private Set<Date> notSubmitDates;//当前的应交未交日志日列表
    @JsonIgnore @JSONField(serialize = false)
    private Map<String, Set<Date>> wholeDays;

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLeader() {
        return leader;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    public Date getOnDutyDate() {
        return onDutyDate;
    }

    public void setOnDutyDate(Date onDutyDate) {
        this.onDutyDate = onDutyDate;
    }

    public Date getOffDutyDate() {
        return offDutyDate;
    }

    public void setOffDutyDate(Date offDutyDate) {
        this.offDutyDate = offDutyDate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getWorkingDays() {
        return workingDays;
    }

    public void setWorkingDays(Integer workingDays) {
        this.workingDays = workingDays;
    }

    public Integer getOutTimeDays() {
        return outTimeDays;
    }

    public void setOutTimeDays(Integer outTimeDays) {
        this.outTimeDays = outTimeDays;
    }

    public Integer getLeaveDays() {
        return leaveDays;
    }

    public void setLeaveDays(Integer leaveDays) {
        this.leaveDays = leaveDays;
    }

    public Integer getSubmissionDays() {
        return submissionDays;
    }

    public void setSubmissionDays(Integer submissionDays) {
        this.submissionDays = submissionDays;
    }

    public Integer getNotSubmissionDays() {
        return notSubmissionDays;
    }

    public void setNotSubmissionDays(Integer notSubmissionDays) {
        this.notSubmissionDays = notSubmissionDays;
    }

    public Set<Date> getWorkingDates() {
        return workingDates;
    }

    public void setWorkingDates(Set<Date> workingDates) {
        this.workingDates = workingDates;
    }

    public Set<Date> getLeaveDates() {
        return leaveDates;
    }

    public void setLeaveDates(Set<Date> leaveDates) {
        this.leaveDates = leaveDates;
    }

    public Set<Date> getOvertimeDates() {
        return overtimeDates;
    }

    public void setOvertimeDates(Set<Date> overtimeDates) {
        this.overtimeDates = overtimeDates;
    }

    public Set<Date> getSubmitedDates() {
        return submitedDates;
    }

    public void setSubmitedDates(Set<Date> submitedDates) {
        this.submitedDates = submitedDates;
    }

    public Set<Date> getSubmitiondDates() {
        return submitiondDates;
    }

    public void setSubmitiondDates(Set<Date> submitiondDates) {
        this.submitiondDates = submitiondDates;
    }

    public Set<Date> getNotSubmitDates() {
        return notSubmitDates;
    }

    public void setNotSubmitDates(Set<Date> notSubmitDates) {
        this.notSubmitDates = notSubmitDates;
    }

    public Map<String, Set<Date>> getWholeDays() {
        return wholeDays;
    }

    public void setWholeDays(Map<String, Set<Date>> wholeDays) {
        this.wholeDays = wholeDays;
    }

    public AttendanceUserDaysDto() {
        state = "0";
        workingDays = 0;
        outTimeDays = 0;
        leaveDays = 0;
        submissionDays = 0;
        notSubmissionDays = 0;
        workingDates = new HashSet<>();
        leaveDates = new HashSet<>();
        overtimeDates = new HashSet<>();
        submitedDates = new HashSet<>();
        submitiondDates = new HashSet<>();
        notSubmitDates = new HashSet<>();
        wholeDays = WholeDayStatus.getEmptyWholeDays();
    }

    public AttendanceUserDaysDto(Integer userID, String userName, String mobile, String leader, Date onDutyDate, Date offDutyDate) {
        this();
        this.userID = userID;
        this.userName = userName;
        this.mobile = mobile;
        this.leader = leader;
        this.onDutyDate = onDutyDate;	//入职时间
        this.offDutyDate = offDutyDate;	//离职时间
    }

    /**
     * 对下列属性赋值
     *     private Set<Date> workingDates;//当前的上班日列表
     *     private Set<Date> leaveDates;//请假日列表
     *     private Set<Date> overtimeDates;//加班天日列表
     *     private Set<Date> submitedDates;//当前的已交日志日列表
     *     private Set<Date> submitiondDates;//当前的应交日志日列表
     *     private Set<Date> notSubmitDates;//当前的应交未日志日列表
     * 最后计算
     *     private Integer workingDays;  //当前的上班天数
     *     private Integer outTimeDays; //加班天数
     *     private Integer leaveDays; //请假天数
     *     private Integer submissionDays; //截至目前应交天数(计算公式：当前的工作日天数+工作日之外加班天数-一整天请假的天数)
     *     private Integer notSubmissionDays; //截至目前未交天数(刘洪涛给的方法中获取)
     * @param beginDate
     * @param endDate
     * @param orgWorkingDates 工作日设置： left 所有, middle 班, right假,
     */
    public void fillNoAttendanced(Date beginDate, Date endDate, Triple<List<Date>, Set<Date>, Set<Date>> orgWorkingDates) {
        orgWorkingDates.getLeft().stream().forEach(date->{
            //先从个体的wholeDays里查
            if(date.getTime()<beginDate.getTime() || date.getTime()>endDate.getTime()) { //员工非在职期间
                noAttendanced(date, orgWorkingDates.getMiddle());
            } else if(wholeDays.get(WholeDayStatus.workingDay.getName()).contains(date)){//工作日
                workingDates.add(date);
                fillSubmition(date);
            } else if(wholeDays.get(WholeDayStatus.leaveDay.getName()).contains(date)) {//请假了
                leaveDates.add(date);
            } else if(wholeDays.get(WholeDayStatus.overtimeDay.getName()).contains(date)) {//加班日
                overtimeDates.add(date);
                fillSubmition(date);
            } else if(wholeDays.get(WholeDayStatus.noNeedDay.getName()).contains(date)) {//无需考勤日
                //Do Nothing
            } else if (orgWorkingDates.getMiddle().contains(date)){
                workingDates.add(date);
                fillSubmition(date);
            } else {//包括 WholeDayStatus.noAttendanced和不在月表两种情况，参考机构班假设置判断
                noAttendanced(date, orgWorkingDates.getMiddle());
            }
        });
        workingDays = workingDates.size();
        outTimeDays = overtimeDates.size();
        leaveDays = leaveDates.size();
        submissionDays = submitiondDates.size();
        notSubmissionDays = notSubmitDates.size();
    }
    private void noAttendanced(Date date, Set<Date> defaultWorkingDates) {
        if(defaultWorkingDates.contains(date)) {//班
            workingDates.add(date);
            fillSubmition(date);
        }
    }
    private void fillSubmition(Date date) {
        submitiondDates.add(date);//应交
        if(!submitedDates.contains(date)) {
            notSubmitDates.add(date);//未交
        }
    }
}