package cn.sphd.miners .modules.material.service.impl;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.MtUnit;
import cn.sphd.miners.modules.material.entity.MtUnitLatest;
import cn.sphd.miners.modules.material.entity.MtUnitRecord;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.material.service.UnitService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName UnitServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/23 17:39
 * @Version 1.0
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class UnitServiceImpl implements UnitService {

    @Autowired
    MtUnitDao mtUnitDao;

    @Autowired
    PdBaseDao pdBaseDao;

    @Autowired
    PdMerchandiseDao pdMerchandiseDao;

    @Autowired
    MtBaseDao mtBaseDao;

    @Autowired
    MtUnitLatestDao mtUnitLatestDao;

    @Autowired
    MtUnitRecordDao mtUnitRecordDao;

    @Override
    public List<MtUnit> selectUnitList(Integer org, Integer status, PageInfo pageInfo) {
        String hql = "from MtUnit where org = "+ org ;
        if(status==1)
            hql=hql+" and enalbed=true";
        else if(status==0)
            hql=hql+" and enalbed=false";
        List<MtUnit> list = mtUnitDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public List<MtUnit> selectStopUnitList(Integer org) {
        String hql = "from MtUnit where org = "+ org +" and enalbed=false";
        List<MtUnit> list = mtUnitDao.getListByHQLWithNamedParams(hql,null);
        return list;
    }
    @Override
    public List<MtUnit> selectUpUnitList(Integer org) {
        String hql = "from MtUnit where org = "+ org +" and enalbed=true";
        List<MtUnit> list = mtUnitDao.getListByHQLWithNamedParams(hql,null);
        return list;
    }

    @Override
    public List<MtUnit> selectUnitList(Integer org) {
        String hql = "from MtUnit where org = "+ org;
        List<MtUnit> list = mtUnitDao.getListByHQLWithNamedParams(hql,null);
        return list;
    }

    @Override
    public int selectUnitNumber(Integer org) {
        String hql = "from MtUnit where org = "+ org +" and enalbed=false";
        List<MtUnit> list = mtUnitDao.getListByHQLWithNamedParams(hql,null);
        return list.size();
    }

    @Override
    public MtUnit selectMtUnit(Integer id) {
        MtUnit mtUnit=mtUnitDao.get(id);
        return mtUnit;
    }

    @Override
    public RespStatus addUnit(MtUnit mtUnit, Integer module) {
        RespStatus respStatus=new RespStatus();
        int status=1;
        String hqltrue = "from MtUnit where org = "+ mtUnit.getOrg()+" and enalbed=true and name='"+mtUnit.getName()+"'";
        List<MtUnit> listtrue = mtUnitDao.getListByHQLWithNamedParams(hqltrue,null);
        if(listtrue.size()>0)
            status=0;
        else {
            String hqlfalse = "from MtUnit where org = " + mtUnit.getOrg() + " and enalbed=false and name='" + mtUnit.getName() + "'";
            List<MtUnit> listfalse = mtUnitDao.getListByHQLWithNamedParams(hqlfalse, null);
            if (listfalse.size() > 0)
                status = 2;
            else{
                mtUnitDao.save(mtUnit);
                respStatus.setName(mtUnit.getName());
                respStatus.setId(mtUnit.getId());
                if(module!=null&&module>0)
                {
                    MtUnitLatest mtUnitLatest=new MtUnitLatest();
                    mtUnitLatest.setModule(String.valueOf(module));
                    mtUnitLatest.setUnit(mtUnit.getId());
                    mtUnitLatest.setCreateDate(new Date());
                    mtUnitLatest.setCreator(mtUnit.getCreator());
                    mtUnitLatest.setCreateName(mtUnit.getCreateName());
                    mtUnitLatest.setOperation("1");
                    mtUnitLatest.setVersionNo(1);
                    mtUnitLatestDao.save(mtUnitLatest);
                }
                MtUnitRecord mtUnitRecord=new MtUnitRecord();
                mtUnitRecord.setUnit(mtUnit.getId());
                mtUnitRecord.setCreateDate(new Date());
                mtUnitRecord.setCreateName(mtUnit.getCreateName());
                mtUnitRecord.setCreator(mtUnit.getCreator());
                mtUnitRecord.setOperation("1");
                mtUnitRecord.setState("1");
                mtUnitRecordDao.save(mtUnitRecord);
            }
        }
        respStatus.setStatus(status);
        return respStatus;
    }

    @Override
    public int updateUnit(MtUnit mtUnit) {
        int status=1;
        if("3".equals(mtUnit.getOperation())){
            //要启用
            mtUnitDao.update(mtUnit);
            MtUnitRecord mtUnitRecord=new MtUnitRecord();
            mtUnitRecord.setUnit(mtUnit.getId());
            mtUnitRecord.setCreateDate(new Date());
            mtUnitRecord.setCreateName(mtUnit.getUpdateName());
            mtUnitRecord.setCreator(mtUnit.getUpdator());
            mtUnitRecord.setOperation("3");
            mtUnitRecord.setState("2");
            mtUnitRecordDao.save(mtUnitRecord);
        }else if("2".equals(mtUnit.getOperation())){
            //要停用
            String hqlMt = "from MtBase where unitId ="+ mtUnit.getId();
            List<MtUnit> listMt = mtBaseDao.getListByHQLWithNamedParams(hqlMt,null);
            String hqlPd = "from PdBase where unitId ="+ mtUnit.getId();
            List<MtUnit> listPd = pdBaseDao.getListByHQLWithNamedParams(hqlPd,null);
            String hqlPdcp= "from PdMerchandise where unitId ="+ mtUnit.getId();
            List<MtUnit> listPdcp = pdMerchandiseDao.getListByHQLWithNamedParams(hqlPdcp,null);
            if(listMt.size()>0||listPd.size()>0||listPdcp.size()>0)
            {
                status=0;
            }else{
            mtUnitDao.update(mtUnit);
            MtUnitRecord mtUnitRecord=new MtUnitRecord();
            mtUnitRecord.setUnit(mtUnit.getId());
            mtUnitRecord.setCreateDate(new Date());
            mtUnitRecord.setCreateName(mtUnit.getUpdateName());
            mtUnitRecord.setCreator(mtUnit.getUpdator());
            mtUnitRecord.setOperation("2");
            mtUnitRecord.setState("0");
            mtUnitRecordDao.save(mtUnitRecord);
            }
        }
        return status;
    }

    @Override
    public int stopUnitEnter(int id) {
        int status=1;
        String hqlMt = "from MtBase where unitId ="+ id;
        List<MtUnit> listMt = mtBaseDao.getListByHQLWithNamedParams(hqlMt,null);
        String hqlPd = "from PdBase where unitId ="+ id;
        List<MtUnit> listPd = pdBaseDao.getListByHQLWithNamedParams(hqlPd,null);
        String hqlPdcp= "from PdMerchandise where unitId ="+ id;
        List<MtUnit> listPdcp = pdMerchandiseDao.getListByHQLWithNamedParams(hqlPdcp,null);
        if(listMt.size()>0||listPd.size()>0||listPdcp.size()>0)
        {
            status = 0;
        }
        return status;
    }

    @Override
    public void selectUnit(Integer unitId, Integer module) {
        String hql = "from MtUnitLatest where unit ="+ unitId +" and module='"+module+"'";
        List<MtUnitLatest> mtUnitLatestList = mtUnitLatestDao.getListByHQLWithNamedParams(hql,null);
        if(mtUnitLatestList.isEmpty())
        {
            MtUnitLatest mtUnitLatest=new MtUnitLatest();
            mtUnitLatest.setModule(String.valueOf(module));
            mtUnitLatest.setUnit(unitId);
            mtUnitLatest.setCreateDate(new Date());
            mtUnitLatestDao.save(mtUnitLatest);
        }else{
            MtUnitLatest mtUnitLatest=mtUnitLatestList.get(0);
            mtUnitLatest.setCreateDate(new Date());
            mtUnitLatestDao.update(mtUnitLatest);
        }
    }

    @Override
    public List<MtUnit> selectUnitListForModule(Integer org, Integer module) {
        String sql = "select * from(select a.* from (select * from `t_mt_unit` where org= ?0 and enalbed=1) as a LEFT JOIN (select create_date as time,unit from`t_mt_unit_latest` where module=?1 ) as b on a.id =b.unit ORDER BY time desc ) as a where id is not null";
        List<MtUnit> list = mtUnitDao.getListBySQL(sql,org,String.valueOf(module));
        List<MtUnit> mtUnitList=new ArrayList<MtUnit>();
        if (list.size()>0 && !list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                MtUnit mtUnit = new MtUnit();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(list.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                mtUnit.setId(Integer.valueOf(objects.getString(0)));
                mtUnit.setOrg(Integer.valueOf(objects.getString(1)));
                mtUnit.setName(objects.getString(3));
                mtUnit.setEnalbed(Boolean.valueOf(objects.getString(5)));
                mtUnit.setModule(objects.getString(7));
                mtUnitList.add(mtUnit);
            }
            return mtUnitList;
        }
        return null;
    }

    @Override
    public MtUnit selectMtUnitByName(String unit, Integer oid) {
        return mtUnitDao.getByHQL(" from MtUnit o where o.name = '" + unit +"'"+" and o.org="+oid);
    }
}
