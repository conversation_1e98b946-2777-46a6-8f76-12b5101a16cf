<!--子路由 按时间查看-->
<template>
  <ul class="val_list">
    <li v-for="(item, index) in accountList" :key="index" class="ta-list">
      <div @click="goDay(item)">
        <span>{{ item.name }}</span>
        <div class="current-balance-1">
          <div class="ty-color-green">
            <p>收入</p>
            <p>{{ item.income }}</p>
          </div>
        </div>
        <div class="current-balance-1 ty-color-red">
          <div>
            <p>支出</p>
            <p>{{ item.expend }}</p>
          </div>
        </div>
        <span>
          <el-icon><arrow-right /></el-icon>
        </span>
      </div>
    </li>
  </ul>
</template>

<script>

import finance from '@/mixins/finance.js'
export default {
  data() {
    return {
      accountList: []
    }
  },
  mixins: [finance],
  mounted() {
    this.getAccountList()
  },
  methods: {
    getAccountList(fid){
      let org = this.auth.getOrg()
      let beginDate = localStorage.getItem('searchMonthDur3_beginDate')
      let endDate = localStorage.getItem('searchMonthDur3_endDate')
      console.log('beginDate =', beginDate)
      console.log('endDate =', endDate)

      if(beginDate){
        if(!endDate){
          let curMonth = beginDate.substr(0,7)
          let mm = curMonth.split('-')[1]
          let endDay = '30'
          if([1,3,5,7,8,10,12].indexOf(Number(mm) ) > -1){
            endDay = '31'
          }else if(Number(mm) === 2){
            let year = curMonth.split('-')[0]
            if(year % 4 ==0 && year % 100 !=0 || year % 400 ==0){ // alert('闰年')
              endDay = '29'
            }else{ // alert('平年')
              endDay = '28'
            }
          }
          endDate = curMonth + '-' + endDay
        }

        let params = { oid:org.id , monthOrDay:2 , beginDate:beginDate, endDate:endDate }
        this.financeApi.getAccountMonthOrDayApp(params).then(res1=>{
          let res = res1.data.data
          console.log('getAccountMonthOrDayApp res=', res)
          let accountDetailList = res.accountDetailList || []
          let accountPeriodList = res.accountPeriodList || []

          this.accountList = []
          accountPeriodList.forEach(item=>{
            let iihk = { name: (new Date(item.beginDate).format('yyyy-MM-dd')) ,
              income: this.formatCurrency(item.credit) , expend: this.formatCurrency(item.debit) }
            this.accountList.push(iihk)
          })


        }).catch(err=>{
          console.log('err=', err)
          this.$message.error('链接失败，请重试！')
        })
      }else{
        this.$message.error('没有具体日期哦！')
      }

    },
    goDay(item){
      if(item.name){
        localStorage.setItem('currentAccountDate_beginDate', item.name )
        let path = `/currentAccountDateDur/null`
        this.$router.push({ path:path })
      }else{
        this.$message.error('没有具体日期哦！')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

</style>