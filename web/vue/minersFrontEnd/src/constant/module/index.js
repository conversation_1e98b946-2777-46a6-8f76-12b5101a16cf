/** module/index.js
 * 常量枚举池
 * 可以根据文件对枚举所属的模块进行拆分
 * 此文件主要是收集模块常量然后统一暴露出去
 */

const EnumModule = {};

// 批量导入当前文件所在目录下的.js文件--直接引入
const options = import.meta.globEager('./*.js');
Object.keys(options).forEach((fileName) => {
    // const name = fileName.replace(/\.\/|\.js/g, '');
    // EnumModule[name] = options[fileName].default;
    Object.assign(EnumModule, {
        ...options[fileName].default
    })
});

export default EnumModule;