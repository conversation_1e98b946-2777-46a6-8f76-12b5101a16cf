package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.common.persistence.PageInfo;

import java.util.List;

public class RespInvestigateQuestionStatistics {
    private Integer sum;//总问题数
    private Integer essayQuestionCount;//问答题
    private Integer singleChoiceCount;//单选
    private Integer multipleChoiceCount;//多选
    private Integer objectSum;//回收总数
    private PageInfo pageInfo;
    private List<RespInvestigateQuestion> respInvestigateQuestionList;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    public Integer getEssayQuestionCount() {
        return essayQuestionCount;
    }

    public void setEssayQuestionCount(Integer essayQuestionCount) {
        this.essayQuestionCount = essayQuestionCount;
    }

    public Integer getSingleChoiceCount() {
        return singleChoiceCount;
    }

    public void setSingleChoiceCount(Integer singleChoiceCount) {
        this.singleChoiceCount = singleChoiceCount;
    }

    public Integer getMultipleChoiceCount() {
        return multipleChoiceCount;
    }

    public void setMultipleChoiceCount(Integer multipleChoiceCount) {
        this.multipleChoiceCount = multipleChoiceCount;
    }

    public Integer getObjectSum() {
        return objectSum;
    }

    public void setObjectSum(Integer objectSum) {
        this.objectSum = objectSum;
    }

    public List<RespInvestigateQuestion> getRespInvestigateQuestionList() {
        return respInvestigateQuestionList;
    }

    public void setRespInvestigateQuestionList(List<RespInvestigateQuestion> respInvestigateQuestionList) {
        this.respInvestigateQuestionList = respInvestigateQuestionList;
    }
}
