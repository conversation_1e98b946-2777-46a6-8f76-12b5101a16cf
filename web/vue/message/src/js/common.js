export function gohistory (obj, num = 1) {
  // console.log('gohistory0', localStorage.getItem('floating'))
  let floatingUrl = window.localStorage.getItem('floating')
  if (floatingUrl) {
    let pathArr = floatingUrl.split('-')
    let newArr = []
    console.log(pathArr)
    if (pathArr.length > 1 && pathArr.length - num > 0) {
      newArr = pathArr.slice(0, pathArr.length - num - 1)
      obj.$router.push({
        path: pathArr[pathArr.length - num - 1]
      })
      window.localStorage.setItem('floating', newArr.join('-'))
    } else {
      obj.$router.push({
        path: '/'
      })
    }
  }
  // console.log('gohistory1', localStorage.getItem('floating'))
}

export function formatDate (time, format) {
  let fm = format
  if (!fm) {
    fm = 'yyyy-MM-dd hh:mm:ss'
  }
  if (!time) {
    return ''
  }
  let date = new Date(time)
  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    'S': date.getMilliseconds()
  }
  if (/(y+)/.test(fm)) {
    fm = fm.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fm)) {
      fm = fm.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return fm
}

export function formatWeek (date) {
  let weekDay = ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  let myDate = new Date(date)
  return weekDay[myDate.getDay()]
}

export function filterInvoiceType (value) {
  let type = ''
  switch (value) {
    case '1':
      type = '增值税专用发票'
      break
    case '2':
      // type = '增值税普通发票'
      type = '增值税普通票/其他普通票'
      break
    case '3':
      // type = '其他普通发票'
      type = '增值税普通票/其他普通票'
      break
    default:
      type = ''
  }
  return type
}

export function numberFilter (value, n) {
  let realVal = parseFloat(value).toFixed(n)
  return realVal
}

export function isSaler (value) {
  let type = ''
  switch (value) {
    case '1':
      type = '销售事务'
      break
    case '2':
      type = '非销售事务'
      break
  }
  return type
}

export function incomeType (value) {
  let type = ''
  switch (value) {
    case '1':
      type = '现金'
      break
    case '2':
      type = '现金支票'
      break
    case '3':
      type = '转帐支票'
      break
    case '4':
      type = '承兑汇票'
      break
    case '5':
      type = '银行转帐'
      break
    default:
      type = ''
  }
  return type
}

export function certificationType (value) {
  // 认证状态 0-未认证 1-认证通过，2-认证失败，3-无需认证
  let type = ''
  switch (value) {
    case '0':
      type = '未认证'
      break
    case '1':
      type = '认证通过'
      break
    case '2':
      type = '认证失败'
      break
    case '3':
      type = '无需认证'
      break
    default:
      type = ''
  }
  return type
}

export function chargeNull (value) {
  return value === '' ? '--' : value
}

export function handleNull (value) {
  return value === null || value === undefined || value === 'null' ? '' : value
}
export function numberToChinese (number) {
  var n = number
  var fraction = ['角', '分']
  var digit = [
    '零', '壹', '贰', '叁', '肆',
    '伍', '陆', '柒', '捌', '玖'
  ]
  var unit = [
    ['圆', '万', '亿'],
    ['', '拾', '佰', '仟']
  ]
  var head = n < 0 ? '欠' : ''
  n = Math.abs(n)
  var s = ''
  for (var k = 0; k < fraction.length; k++) {
    s += (digit[Math.floor(n * 10 * Math.pow(10, k)) % 10] + fraction[k]).replace(/零./, '')
  }
  s = s || '整'
  n = Math.floor(n)
  for (var i = 0; i < unit[0].length && n > 0; i++) {
    var p = ''
    for (var j = 0; j < unit[1].length && n > 0; j++) {
      p = digit[n % 10] + unit[1][j] + p
      n = Math.floor(n / 10)
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s
  }
  return head + s.replace(/(零.)*零圆/, '圆')
    .replace(/(零.)+/g, '零')
    .replace(/^整$/, '零圆整')
}
// creator: 李玉婷，2019-07-19 11:37:17，返回当前月至n个月的所有月份的列表
export function getMonthList (num) {
  let date = new Date()
  let monthList = []
  for (let i = 0; i < num; i++) {
    let year = date.getFullYear()
    let month = date.getMonth()
    if (month - i < 0) {
      year--
      month = 12 + month - i
    } else {
      month -= i
    }
    monthList.push({
      inputLabel: year + '年' + Number(month + 1) + '月',
      inputVal: year + '-' + (month < 9 ? '0' : '') + Number(month + 1)
    })
  }
  return monthList
}
// creator: 李玉婷，2019-07-24 14:05:09，设置金额为默认格式（比如199,231,000.00)
export function formatMoney (number, places, symbol, thousand, decimal) {
  let number0 = number || 0
  let places0 = !isNaN(Math.abs(places)) ? places : 2
  let symbol0 = symbol !== undefined ? symbol : ''
  let thousand0 = thousand || ','
  let decimal0 = decimal || '.'
  let negative = number0 < 0 ? '-' : ''
  let i = parseInt(number0 = Math.abs(+number0 || 0).toFixed(places0), 10) + ''
  let j = i.length
  j = j > 3 ? j % 3 : 0
  return symbol0 + negative + (j ? i.substr(0, j) + thousand0 : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, '$1' + thousand0) + (places0 ? decimal0 + Math.abs(number0 - i).toFixed(places0).slice(2) : '')
}

// creator: 张旭博，2020-11-09 14:29:24，判断文件格式是否可以预览
export function canSeeOnline (version) {
  let customVersion = ['doc', 'docx', 'xls', 'xlsx', 'zip', 'rar', 'apk', 'ipa', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'wps', 'et', 'md']
  return customVersion.indexOf(version) !== -1
}
// creator: liyut，2023-3-31 14:29:24，访谈记录，输出共x人访谈对象
export function countNum (str) {
  let count = 0
  if (str && str !== "") {
    let arr = str.split(',')
    count = arr.length
  }
  return count
}
/*creator:lyt 2023/4/26 0026 上午 11:17 */
export function formatName (interviewerNames) {
  if (interviewerNames !== ""){
    let names = interviewerNames.split(",")
    for(let i = 0; i < names.length; i++) {
      names[i] === "" ? names[i] = '--' : "";
    }
    interviewerNames = names.toString()
  }
  return interviewerNames
}
