package cn.sphd.miners.modules.system.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.commodity.service.PdCategoryService;
import cn.sphd.miners.modules.finance.dao.AccountPeroidDao;
import cn.sphd.miners.modules.finance.dao.FinanceAccountDao;
import cn.sphd.miners.modules.finance.entity.AccountPeriod;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.generalAffairs.service.UserImportService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.material.dao.MtCategoryDao;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.sales.dao.PdModelSettingsDao;
import cn.sphd.miners.modules.sales.entity.PdModelSettings;
import cn.sphd.miners.modules.site.dao.OrgSiteDao;
import cn.sphd.miners.modules.site.entity.OrgSite;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.system.dao.*;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.PopedomDto;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.resource.transaction.spi.TransactionStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2015-09-09.
 */
@Service
public class OrgServiceImpl extends BaseServiceImpl implements OrgService {
    @Autowired
    DlmService dlmService;
    @Autowired
    AccountService accountService;
    @Autowired
    SubjectSettingService subjectSettingService;
    @Autowired
    SendMessageService sendMessageService;
    @Autowired
    OrgDao orgDao;
    @Autowired
    OrgHistoryDao orgHistoryDao;
    @Autowired
    UserDao userDao;
    @Autowired
    OrgPopedomDao orgPopedomDao;
    @Autowired
    RoleTmplDao roleTmplDao;
    @Autowired
    UserPopedomDao userPopedomDao;
    @Autowired
    MtCategoryDao mtCategoryDao;
    @Autowired
    CodeCategoryDao codeCategoryDao;
    @Autowired
    ApprovalItemDao approvalItemDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    CodeDao codeDao;
    @Autowired
    ApprovalFlowDao approvalFlowDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    PopedomDao popedomDao;
    @Autowired
    OrgSiteDao orgSiteDao;
    @Autowired
    UserService userService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    ScheduleService scheduleService;
    @Autowired
    AuthService authService;
    @Autowired
    UserImportService userImportService;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    PdModelSettingsDao pdModelSettingsDao;
    @Autowired
    OrgInitService orgInitService;
    @Autowired
    SmsService smsService;
    @Autowired
    PdCategoryService pdCategoryService;

    @Override
    public List<Organization> getAllDepartmentById(Integer oid) {
//        String condition = " and o.pid="+oid+" and o.orgType=2";
//        List<Organization> organizations = orgDao.findCollectionByConditionNoPage(condition,null,null);//得到一级部门
//        List<Organization> os =new ArrayList<Organization>();//创建新的集合
//        List<Organization> organizationList = this.getChildes(organizations,os);
//        organizations.addAll(organizationList);
        Organization organization = orgDao.get(oid);
        String hql = " from Organization where orgType=:orgType and enabled=:enabled and orgCode like:pass";
        Map<String, Object> map = new HashMap<>(3);
        map.put("orgType", OrgType.valueOf("department").getIndex());
        map.put("enabled", Boolean.TRUE);
        map.put("pass", "/" + organization.getId() + "/%");
        List<Organization> organizationList = orgDao.getListByHQLWithNamedParams(hql, map);
        return organizationList;

    }
//    private List<Organization> getChildes(List<Organization> organizations,List<Organization> os) {
//        List<Organization> organizationList = new ArrayList<Organization>();
//        for (Organization o : organizations){//遍历部门
//            String h = " from Organization o where o.orgType=2 and o.pid=" + o.getId();
//            organizations = orgDao.getListByHQL(h);
//            if(organizations.size()>0){
//                organizationList.addAll(organizations);
//            }
//        }
//        os.addAll(organizationList);
//        if(organizationList.size()>0){//若得到的子级数量大于0，继续往下遍历,否则结束
//            getChildes(organizationList,os);
//        }
//        return os;
//    }


    //获取当前部门下的所有职位
    @Override
    public List<Organization> getAllPostById(Integer id) {
        String hql = " from Organization o where o.orgType = 3 and o.pid = " + id + " ORDER BY level ASC";
        List<Organization> organizations = orgDao.getListByHQL(hql);
        return organizations;
    }

    //获取学校所有部门
    @Override
    public List<Organization> getAllBranchById(Integer oid) {
        String hql = " from Organization o where o.orgType = 2 and o.pid = " + oid;
        List<Organization> organizations = orgDao.getListByHQL(hql);
        return organizations;
    }

    //保存部门信息
    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries = true)
    public void saveOrg(Organization organization) {
        orgDao.save(organization);
        if (organization.getPid() != 0) {
            Organization pOrg = orgDao.get(organization.getPid());
            organization.setOrgCode(pOrg.getOrgCode() == null ? "/" + pOrg.getId() + "/" + organization.getId() + "/" : pOrg.getOrgCode() + organization.getId() + "/");
            orgDao.update(organization);
        }
    }

    //更新部门信息
    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries = true)
    public void updateOrg(Organization organization) {
        orgDao.update(organization);
    }

    @Override
    public void updateOrgState(String state, Integer oid) {
        Organization o = orgDao.get(oid);
        o.setState(Integer.valueOf(state));
        orgDao.update(o);
        if ("1".equals(state)) { //暂停服务
            List<User> userList = userService.getUserListByOrgId(oid);
            for (User u : userList) {
                u.setUserType(o.getState());
                u.setDefault(false); //暂停机构 下所有员工的 去掉该机构的默认登录
                userService.updateUser(u);
            }
        }
    }

    @Override
    public Organization getOrgByOid(Integer oid, OrgType type) {
        Organization result = orgDao.get(oid);
        if (result != null && type.getIndex().equals(result.getOrgType()) && Boolean.TRUE.equals(result.isEnabled())) {
            return result;
        } else {
            return null;
        }
    }

    /**
     * <AUTHOR>
     * @description 只获取机构，如果要获取包括enabled=true和false的机构，用getByOid(oid, true, false)调用，如果只获取enabled=true，就只需要getByOid(oid)就行
     * @date Create at 2022/9/16 11:29
     * @method getByOid
     * @param oid
     * @param enabled
     * @return: cn.sphd.miners.modules.system.entity.Organization:
     **/
    @Override
    public Organization getByOid(Integer oid, Boolean... enabled) {
        List<Boolean> enableds;
        if (ObjectUtils.isEmpty(enabled)) {
            enableds = Arrays.asList(true);
        } else {
            enableds = Arrays.asList(enabled);
        }
        Organization result = oid!=null ? orgDao.get(oid) : null;
        if (result != null && OrgType.isTypeOrg(OrgType.getByIndex(result.getOrgType())) && enableds.contains(result.isEnabled())) {
            return result;
        } else {
            return null;
        }
    }

//    @Override
//    public Organization getOrgById(Integer id,Integer pid,Integer adefault) {
//        Map<String,Object> params = new HashMap<>();
//        String hql = "from Organization where id =: id";
//        params.put("id",id);
//        if (pid!=null){
//            hql+=" and pid=:pid";
//            params.put("pid",pid);
//        }
//        if (adefault!=null){
////            hql+=" and adefault=:adefault";
////            params.put("adefault",adefault);
//            if (1==adefault){      //是否为销售部
//                hql+=" and adefault=true";
//            }else {
//                hql+=" and adefault=false";
//            }
//        }
//        return (Organization) orgDao.getByHQLWithNamedParams(hql,params);
//    }

    @Override
    public List<Organization> getOrgByOids(Collection<Integer> oids) {
        String hql = "from Organization where id in (:ids)";
        HashMap<String, Object> params = new HashMap<>();
        params.put("ids", oids);
        return orgDao.getListByHQLWithNamedParams(hql, params);
    }

    @Override
    public List<Organization> checkDepartmentByPId(Integer pid, Integer orgType) {
        Map<String, Object> params = new HashMap<>();
        String hql = "from Organization where pid=:pid";
        params.put("pid", pid);
        if (orgType != null) {
            hql += " and orgType=:orgType";
            params.put("orgType", orgType);
        }
        List<Organization> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        return organizations;
    }

    @Override
    public List<Organization> checkDepartmentByPId(Integer pid, Integer orgType, Integer enabled) {
        Map<String, Object> params = new HashMap<>();
        String hql = "from Organization where pid=:pid";
        params.put("pid", pid);
        if (orgType != null) {
            hql += " and orgType=:orgType";
            params.put("orgType", orgType);
        }
        if (enabled != null) {
            if (1 == enabled) {
                hql += " and enabled=true";
            } else if (0 == enabled) {
                hql += " and enabled=false";
            }
        }
        List<Organization> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        return organizations;
    }

    @Override
    public void deleteOrganization(Organization organization) {
        orgDao.delete(organization);
    }

    @Override
    public Map<String, Object> deleteOrganization(Integer orgId) {
        Map<String, Object> map = new HashMap<>();
//        List<Organization> organizations=this.getAllBranchById(orgId);
//        List<User> users=userService.getUsersByOrg(oid,orgId.toString(),null);
//        if(users.size()>0){
//            map.put("status",2);// 有职工
//            map.put("content","操作失败！因为系统中该部门下有职工。");
//        } else if (organizations.size()>0){
//            map.put("status",3);//有部门
//            map.put("content","操作失败！因为系统中该部门下有子级部门。");
//        }else {
        Organization organization = orgDao.get(orgId); //机构信息
        Organization orgParent = orgDao.get(organization.getPid());  //父级部门
//            if (organization.isAdefault()){  //销售部
//                map.put("status",4);
//                map.put("content","对不起，为确保系统功能正常，此部门不可被删除！");
//            }else {
        List<OrganizationHistory> organizationHistories = this.getDeptHistoryByDept(orgId);
        for (OrganizationHistory oh : organizationHistories) {
            orgHistoryDao.delete(oh);
        }
        orgDao.delete(organization);

        orgParent.setChildNum(orgParent.getChildNum() == null ? 0 : orgParent.getChildNum() - 1);
        orgDao.update(orgParent);
        map.put("status", 1);//删除完成
        map.put("content", "操作成功");
//            }
//        }
        return map;
    }


    //*************************************************++以下为安卓端接口++**********************************************************************


    @Override
    public void deletePost(Organization organization) {
        orgDao.delete(organization);
    }

    @Override
    public String orgName(Integer oid) {
//        String hql="and o.id = "+oid;
//        List<Organization> list = orgDao.findCollectionByConditionNoPage(hql,null,null);
//        Organization organization=null;
//        if (list!=null && list.size()>0){
//            organization=list.get(0);
//        }
//        return organization.getName();
        String orgName = null;
        if (oid != null && oid >= 0) {
            Organization org = getByOid(oid);
            if (org != null) {
                orgName = org.getName();
            }
        }
        return orgName;
    }


    //初始化机构
    Organization initializationOrg(Date createDate,Integer pid, OrgType orgType, String phone, String createName, String name, String initState, String uploadStorageType, String address,String registeredAddress, List<OrgPopedom> orgPopedomList, Map<String, Object> map) {
        if (!OrgType.isTypeOrg(orgType)) {
            throw new IllegalArgumentException("机构类型不合法");
        }

        //机构
        Organization organization = new Organization();
//        organization.setOrgType(1);
        organization.setOrgType(orgType.getIndex()); // 新增机构类型  1-机构  4-子机构
        organization.setPid(pid);  // 主机构pid 0

        organization.setName(phone);
        if (name != null && !name.isEmpty()) {
            organization.setName(name);
        }
        organization.setFullName(name);
        organization.setState(0);
        organization.setPhone(phone);
        organization.setAddress(address);
        organization.setExistenceSmallSuper("0");
//        organization.setInitState("0");
        organization.setInitState(initState);  // 正式启用状态 0-未启用  1-启用
        organization.setAccountState("0");
        organization.setRebuildLabel("N");
        organization.setCreateName(createName);
        organization.setCreateDate(createDate);
        organization.setEnableTime(new Date());
        organization.setUploadStorageType(uploadStorageType);
        orgDao.save(organization);

        DecimalFormat df = new DecimalFormat("********");

        int oid = organization.getId();
        String superId = df.format(oid);
        organization.setSuperId(superId);

        map.put("oid", organization.getId());
        map.put("superId", df.format(organization.getId()));

        List<String> addMids=new ArrayList<>();
        for (OrgPopedom o : orgPopedomList) {
            if (!addMids.contains(o.getMid())) {
                OrgPopedom orgPopedom = new OrgPopedom();
                orgPopedom.setOrg(oid);
                orgPopedom.setMid(o.getMid());
                orgPopedom.setNewName(o.getNewName());
                if (StringUtils.isNotEmpty(o.getNewName())){
                    orgPopedom.setType("1");
                }
                orgPopedomDao.save(orgPopedom);

                if (StringUtils.isNotEmpty(o.getNewName())){
                    orgPopedomService.saveOrgPopedomHistory(o,0,createName,"1",oid);
                }

                addMids.add(o.getMid());
                if (orgPopedom.getMid().equals("iba")) {//直播助手
                    organization.setCode("liveHelper"); // 特殊类型子机构 直播助手
                }
            }
        }
        orgDao.update(organization);

        // 1.330P地址优化  2025/02/14 加
        if (StringUtils.isNotEmpty(registeredAddress)) {
            OrgSite orgSite = new OrgSite();
            orgSite.setUpdateDate(new Date());
            orgSite.setUpdateName("系统");
            orgSite.setOperation("1"); //操作:1-增,2-删,3-全改,4-更换新场地
            orgSite.setOrg(organization.getId());
            orgSite.setCreateDate(new Date());
            orgSite.setCreateName("系统");
            orgSite.setUpdateName("系统");
            orgSite.setOperation("1"); //操作:1-增,2-删,3-全改,4-更换新场地
            orgSite.setType(3); //3注册地址
            orgSite.setAddress(registeredAddress);
            orgSiteDao.save(orgSite);
        }
        return organization;
    }

    //初始化机构user  主机构和 直播助手 是董事长， 1.214多地点子机构 人员有三种（选总机构的人，新录入临时管理员，亲自操作）所以摘出来，
    User initializationUser(Organization organization, String phone, String userName, String roleCode) {
        RoleTmpl roleTmpl = roleTmplDao.getRoleTmplByCode(roleCode);//机构初始化前  赋予权限的roleCode 总机构赋予 正式启用前权限， 直播助手创建就是启用 直接赋予启用后 董事长权限

        Integer oid = organization.getId();

        User user = new User();
        user.setMobile(phone);
        user.setOid(oid);
        user.setCreateTime(new Date());
        user.setIsDuty("1");
        user.setLogonState(0L);
        user.setOrganization(organization);
        user.setLoginStatus(0);
        user.setManagerCode("super");
        user.setRoleCode("super");
        user.setDefault(false);
        user.setPasswordTime(new Date());
        user.setUserType(organization.getState());
        user.setUserName("董事长");
        if (userName != null && !userName.isEmpty()) {
            user.setUserName(userName);
        }
        user.setRoleName("董事长");
        user.setLeader("0");
        user.setRankUrl("/");
        user.setOrdinaryEmployees(0);
        userService.addUser(user, organization);

//        user.setSuperId(superId);//薪资宝的
//        userService.updateUser(user);

        for (RolePopedomTmpl r : roleTmpl.getRolePopedomTmplHashSet()) {
            UserPopedom userPopedom = new UserPopedom();
            userPopedom.setMid(r.getMid());
            userPopedom.setOid(oid);
            userPopedom.setUserId(user.getUserID());
            userPopedomDao.save(userPopedom);
        }

        return user;
    }

    void initializationBusinessData(Organization organization, User user) throws InvocationTargetException, IllegalAccessException, ParseException {
        Integer oid = organization.getId();


        //创建机构默认部门-销售部
        saveOrganization(oid, 2, "销售部", "销售部", true, true, null, "系统", new Date());


        //物料分类初始化数据
        MtCategory mc1 = new MtCategory();
        mc1.setName("构成商品的原辅材料");
        mc1.setLevel(1);
        mc1.setOrg(organization);
        mtCategoryDao.save(mc1);


        MtCategory mc2 = new MtCategory();
        mc2.setName("商品");
        mc2.setLevel(1);
        mc2.setOrg(organization);
        mtCategoryDao.save(mc2);


        MtCategory mc3 = new MtCategory();
        mc3.setName("外购成品");
        mc3.setLevel(1);
        mc3.setOrg(organization);
        mtCategoryDao.save(mc3);


        MtCategory mc4 = new MtCategory();
        mc4.setName("商品的包装物");
        mc4.setLevel(1);
        mc4.setOrg(organization);
        mtCategoryDao.save(mc4);

        MtCategory mc5 = new MtCategory();
        mc5.setName("半成品");
        mc5.setLevel(1);
        mc5.setOrg(organization);
        mtCategoryDao.save(mc5);

        MtCategory mc6 = new MtCategory();
        mc6.setName("办公用品");
        mc6.setLevel(1);
        mc6.setOrg(organization);
        mtCategoryDao.save(mc6);

        MtCategory mc7 = new MtCategory();
        mc7.setName("其他原辅材料");
        mc7.setLevel(1);
        mc7.setOrg(organization);
        mtCategoryDao.save(mc7);

        MtCategory zi1 = new MtCategory();
        zi1.setName("待分类");
        zi1.setLevel(2);
        zi1.setOrg(organization);
        zi1.setParent(mc1);
        zi1.setFirstGradeId(mc1.getId());
        mtCategoryDao.save(zi1);

        MtCategory zi2 = new MtCategory();
        zi2.setName("待分类");
        zi2.setLevel(2);
        zi2.setOrg(organization);
        zi2.setParent(mc2);
        zi2.setFirstGradeId(mc2.getId());
        mtCategoryDao.save(zi2);

        MtCategory zi3 = new MtCategory();
        zi3.setName("待分类");
        zi3.setLevel(2);
        zi3.setOrg(organization);
        zi3.setParent(mc3);
        zi3.setFirstGradeId(mc3.getId());
        mtCategoryDao.save(zi3);

        MtCategory zi4 = new MtCategory();
        zi4.setName("待分类");
        zi4.setLevel(2);
        zi4.setOrg(organization);
        zi4.setParent(mc4);
        zi4.setFirstGradeId(mc4.getId());
        mtCategoryDao.save(zi4);

        MtCategory zi5 = new MtCategory();
        zi5.setName("待分类");
        zi5.setLevel(2);
        zi5.setOrg(organization);
        zi5.setParent(mc5);
        zi5.setFirstGradeId(mc5.getId());
        mtCategoryDao.save(zi5);

        MtCategory zi6 = new MtCategory();
        zi6.setName("待分类");
        zi6.setLevel(2);
        zi6.setOrg(organization);
        zi6.setParent(mc6);
        zi6.setFirstGradeId(mc6.getId());
        mtCategoryDao.save(zi6);

        MtCategory zi7 = new MtCategory();
        zi7.setName("待分类");
        zi7.setLevel(2);
        zi7.setOrg(organization);
        zi7.setParent(mc7);
        zi7.setFirstGradeId(mc7.getId());
        mtCategoryDao.save(zi7);

        //报销初始化数据
        CodeCategory codeCategory = new CodeCategory();
        codeCategory.setCode("feeCat");
        codeCategory.setEnabled(1);
        codeCategory.setName("费用类别");
        codeCategory.setOrg(organization);
        codeCategoryDao.save(codeCategory);

        Code fei1 = new Code();
        fei1.setEnabled(1);
        fei1.setName("车务支出");
        fei1.setCategory(codeCategory);
        fei1.setOrders(10);
        codeDao.save(fei1);

        //车务的 默认二级费用类别
        Code fei1Second1 = new Code();
        fei1Second1.setEnabled(1);
        fei1Second1.setName("加油");
        fei1Second1.setCategory(codeCategory);
        fei1Second1.setOrders(10);
        fei1Second1.setParent(fei1);
        codeDao.save(fei1Second1);

        Code fei1Second2 = new Code();
        fei1Second2.setEnabled(1);
        fei1Second2.setName("维修");
        fei1Second2.setCategory(codeCategory);
        fei1Second2.setOrders(20);
        fei1Second2.setParent(fei1);
        codeDao.save(fei1Second2);

        Code fei1Second3 = new Code();
        fei1Second3.setEnabled(1);
        fei1Second3.setName("保养");
        fei1Second3.setCategory(codeCategory);
        fei1Second3.setOrders(30);
        fei1Second3.setParent(fei1);
        codeDao.save(fei1Second3);

        Code fei1Second4 = new Code();
        fei1Second4.setEnabled(1);
        fei1Second4.setName("交汽车保险");
        fei1Second4.setCategory(codeCategory);
        fei1Second4.setOrders(40);
        fei1Second4.setParent(fei1);
        codeDao.save(fei1Second4);

        Code fei1Second5 = new Code();
        fei1Second5.setEnabled(1);
        fei1Second5.setName("其他");
        fei1Second5.setCategory(codeCategory);
        fei1Second5.setOrders(50);
        fei1Second5.setParent(fei1);
        codeDao.save(fei1Second5);

        Code fei2 = new Code();
        fei2.setEnabled(1);
        fei2.setName("交通费");
        fei2.setCategory(codeCategory);
        fei2.setOrders(20);
        codeDao.save(fei2);

        //  交通费 二级费用类别
        Code fei2Second1 = new Code();
        fei2Second1.setEnabled(1);
        fei2Second1.setName("飞机");
        fei2Second1.setCategory(codeCategory);
        fei2Second1.setOrders(10);
        fei2Second1.setParent(fei2);
        codeDao.save(fei2Second1);

        Code fei2Second2 = new Code();
        fei2Second2.setEnabled(1);
        fei2Second2.setName("高铁/动车");
        fei2Second2.setCategory(codeCategory);
        fei2Second2.setOrders(20);
        fei2Second2.setParent(fei2);
        codeDao.save(fei2Second2);

        Code fei2Second3 = new Code();
        fei2Second3.setEnabled(1);
        fei2Second3.setName("火车软卧");
        fei2Second3.setCategory(codeCategory);
        fei2Second3.setOrders(30);
        fei2Second3.setParent(fei2);
        codeDao.save(fei2Second3);

        Code fei2Second4 = new Code();
        fei2Second4.setEnabled(1);
        fei2Second4.setName("火车硬卧/硬座");
        fei2Second4.setCategory(codeCategory);
        fei2Second4.setOrders(40);
        fei2Second4.setParent(fei2);
        codeDao.save(fei2Second4);

        Code fei2Second5 = new Code();
        fei2Second5.setEnabled(1);
        fei2Second5.setName("轮船");
        fei2Second5.setCategory(codeCategory);
        fei2Second5.setOrders(50);
        fei2Second5.setParent(fei2);
        codeDao.save(fei2Second5);

        Code fei2Second6 = new Code();
        fei2Second6.setEnabled(1);
        fei2Second6.setName("长途大巴");
        fei2Second6.setCategory(codeCategory);
        fei2Second6.setOrders(60);
        fei2Second6.setParent(fei2);
        codeDao.save(fei2Second6);

        Code fei2Second7 = new Code();
        fei2Second7.setEnabled(1);
        fei2Second7.setName("地铁/公交");
        fei2Second7.setCategory(codeCategory);
        fei2Second7.setOrders(70);
        fei2Second7.setParent(fei2);
        codeDao.save(fei2Second7);

        Code fei2Second8 = new Code();
        fei2Second8.setEnabled(1);
        fei2Second8.setName("租车");
        fei2Second8.setCategory(codeCategory);
        fei2Second8.setOrders(80);
        fei2Second8.setParent(fei2);
        codeDao.save(fei2Second8);

        Code fei2Second9 = new Code();
        fei2Second9.setEnabled(1);
        fei2Second9.setName("网约车/出租车");
        fei2Second9.setCategory(codeCategory);
        fei2Second9.setOrders(90);
        fei2Second9.setParent(fei2);
        codeDao.save(fei2Second9);

        Code fei2Second10 = new Code();
        fei2Second10.setEnabled(1);
        fei2Second10.setName("过路过桥费");
        fei2Second10.setCategory(codeCategory);
        fei2Second10.setOrders(100);
        fei2Second10.setParent(fei2);
        codeDao.save(fei2Second10);

        Code fei2Second11 = new Code();
        fei2Second11.setEnabled(1);
        fei2Second11.setName("停车费");
        fei2Second11.setCategory(codeCategory);
        fei2Second11.setOrders(110);
        fei2Second11.setParent(fei2);
        codeDao.save(fei2Second11);

        Code fei2Second12 = new Code();
        fei2Second12.setEnabled(1);
        fei2Second12.setName("其他交通费");
        fei2Second12.setCategory(codeCategory);
        fei2Second12.setOrders(120);
        fei2Second12.setParent(fei2);
        codeDao.save(fei2Second12);

        Code fei3 = new Code();
        fei3.setEnabled(1);
        fei3.setName("差旅费");
        fei3.setCategory(codeCategory);
        fei3.setOrders(30);
        codeDao.save(fei3);

        Code fei4 = new Code();
        fei4.setEnabled(1);
        fei4.setName("招待费");
        fei4.setCategory(codeCategory);
        fei4.setOrders(40);
        codeDao.save(fei4);

        Code fei5 = new Code();
        fei5.setEnabled(1);
        fei5.setName("餐饮费");
        fei5.setCategory(codeCategory);
        fei5.setOrders(50);
        codeDao.save(fei5);

        Code fei6 = new Code();
        fei6.setEnabled(1);
        fei6.setName("住宿费");
        fei6.setCategory(codeCategory);
        fei6.setOrders(60);
        codeDao.save(fei6);

        Code fei7 = new Code();
        fei7.setEnabled(1);
        fei7.setName("会议费");
        fei7.setCategory(codeCategory);
        fei7.setOrders(70);
        codeDao.save(fei7);

        Code fei8 = new Code();
        fei8.setEnabled(1);
        fei8.setName("培训费");
        fei8.setCategory(codeCategory);
        fei8.setOrders(80);
        codeDao.save(fei8);

        Code fei9 = new Code();
        fei9.setEnabled(1);
        fei9.setName("电话/手机费");
        fei9.setCategory(codeCategory);
        fei9.setOrders(90);
        codeDao.save(fei9);

        Code fei10 = new Code();
        fei10.setEnabled(1);
        fei10.setName("日常用品");
        fei10.setCategory(codeCategory);
        fei10.setOrders(100);
        codeDao.save(fei10);

        Code fei11 = new Code();
        fei11.setEnabled(1);
        fei11.setName("食品");
        fei11.setCategory(codeCategory);
        fei11.setOrders(110);
        codeDao.save(fei11);

        Code fei12 = new Code();
        fei12.setEnabled(1);
        fei12.setName("办公用品");
        fei12.setCategory(codeCategory);
        fei12.setOrders(120);
        codeDao.save(fei12);

        Code fei13 = new Code();
        fei13.setEnabled(1);
        fei13.setName("房租");
        fei13.setCategory(codeCategory);
        fei13.setOrders(130);
        codeDao.save(fei13);

        Code fei14 = new Code();
        fei14.setEnabled(1);
        fei14.setName("物业费");
        fei14.setCategory(codeCategory);
        fei14.setOrders(140);
        codeDao.save(fei14);

        Code fei15 = new Code();
        fei15.setEnabled(1);
        fei15.setName("其它");
        fei15.setCategory(codeCategory);
        fei15.setOrders(150);
        codeDao.save(fei15);

        CodeCategory codeCategory1 = new CodeCategory();
        codeCategory1.setCode("billCat");
        codeCategory1.setEnabled(1);
        codeCategory1.setName("票据种类");
        codeCategory1.setOrg(organization);
        codeCategoryDao.save(codeCategory1);

        Code piao1 = new Code();
        piao1.setEnabled(1);
        piao1.setName("增值税专用发票");
        piao1.setCategory(codeCategory1);
        piao1.setOrders(60);
        codeDao.save(piao1);

        Code piao2 = new Code();
        piao2.setEnabled(1);
        piao2.setName("增值税普通发票");
        piao2.setCategory(codeCategory1);
        piao2.setOrders(80);
        codeDao.save(piao2);

        Code piao3 = new Code();
        piao3.setEnabled(1);
        piao3.setName("定额发票");
        piao3.setCategory(codeCategory1);
        piao3.setOrders(100);
        codeDao.save(piao3);

        Code piao4 = new Code();
        piao4.setEnabled(1);
        piao4.setName("其他普通发票");
        piao4.setCategory(codeCategory1);
        piao4.setOrders(120);
        codeDao.save(piao4);

        Code piao5 = new Code();
        piao5.setEnabled(1);
        piao5.setName("收据");
        piao5.setCategory(codeCategory1);
        piao5.setOrders(140);
        codeDao.save(piao5);

        //审批项初始化
        ApprovalItem jiaban = new ApprovalItem();
        jiaban.setBelongTo(organization.getId());
        jiaban.setDescription("加班申请");
        jiaban.setName("加班");
        jiaban.setStatus(1);
        jiaban.setLevel(1);
        jiaban.setType("Apply");
        jiaban.setCreateName("系统");
        jiaban.setCreateDate(new Date());
        jiaban.setCode("overTimeApply");
        jiaban.setOpenDate(NewDateUtils.today(new Date()));
        jiaban.setApproveStatus("2");
        jiaban.setAuditDate(new Date());
        jiaban.setOrders(10);
        jiaban.setUpperLimit(new BigDecimal(24));
        approvalItemDao.save(jiaban);

        ApprovalFlow jiabanFlow = new ApprovalFlow();
        jiabanFlow.setToUser("董事长");
        jiabanFlow.setToUserId(user.getUserID());
        jiabanFlow.setType(1);
        jiabanFlow.setItem(jiaban);
        jiabanFlow.setAmountCeiling(24.0);
        jiabanFlow.setLimitQuantity(24.0);
        jiabanFlow.setLevel(1);
        jiabanFlow.setUserName(user.getUserName());
        approvalFlowDao.save(jiabanFlow);

        ApprovalItem jiabanBu = new ApprovalItem();  //补报加班
        jiabanBu.setBelongTo(organization.getId());
        jiabanBu.setDescription("补报加班");
        jiabanBu.setName("补报加班");
        jiabanBu.setStatus(0);
        jiabanBu.setLevel(1);
        jiabanBu.setEnabled(false);
        jiabanBu.setType("Apply");
        jiabanBu.setCreateName("系统");
        jiabanBu.setCreateDate(new Date());
        jiabanBu.setCode("supplementaryOvertime");
        jiabanBu.setOpenDate(NewDateUtils.today(new Date()));
        jiabanBu.setApproveStatus("2");
        jiabanBu.setAuditDate(new Date());
        jiabanBu.setOrders(15);
        jiabanBu.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(jiabanBu);

        ApprovalItem jiabanRule = new ApprovalItem();  //加班时限规划
        jiabanRule.setBelongTo(organization.getId());
        jiabanRule.setDescription("加班时限规划");
        jiabanRule.setName("加班时限规划");
        jiabanRule.setStatus(0);
        jiabanRule.setLevel(1);
        jiabanRule.setType("Apply");
        jiabanRule.setCreateName("系统");
        jiabanRule.setCreateDate(new Date());
        jiabanRule.setCode("overtimeRule");
        jiabanRule.setOpenDate(NewDateUtils.today(new Date()));
        jiabanRule.setApproveStatus("2");
        jiabanRule.setAuditDate(new Date());
        jiabanRule.setOrders(20);
        jiabanRule.setUpperLimit(new BigDecimal(30));
        approvalItemDao.save(jiabanRule);

        ApprovalItem subJiabanRule = new ApprovalItem();  //提交实际加班时限规则
        subJiabanRule.setBelongTo(organization.getId());
        subJiabanRule.setDescription("提交实际加班时限规则");
        subJiabanRule.setName("提交实际加班时限规则");
        subJiabanRule.setStatus(0);
        subJiabanRule.setLevel(1);
        subJiabanRule.setType("Apply");
        subJiabanRule.setCreateName("系统");
        subJiabanRule.setCreateDate(new Date());
        subJiabanRule.setCode("submitOvertimeRules");
        subJiabanRule.setOpenDate(NewDateUtils.today(new Date()));
        subJiabanRule.setApproveStatus("2");
        subJiabanRule.setAuditDate(new Date());
        subJiabanRule.setOrders(25);
        subJiabanRule.setUpperLimit(new BigDecimal(3)); //默认3天
        approvalItemDao.save(subJiabanRule);

        ApprovalItem qingjia = new ApprovalItem();
        qingjia.setBelongTo(organization.getId());
        qingjia.setDescription("请假申请");
        qingjia.setName("请假");
        qingjia.setStatus(1);
        qingjia.setLevel(1);
        qingjia.setType("Apply");
        qingjia.setOrders(40);
        qingjia.setCode("leaveApply");
        qingjia.setCreateName("系统");
        qingjia.setCreateDate(new Date());
        qingjia.setOpenDate(NewDateUtils.today(new Date()));
        qingjia.setApproveStatus("2");
        qingjia.setAuditDate(new Date());
        qingjia.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(qingjia);

//        Integer amc = -1;  //如果默认的审批设置数据是任何时长或者天数时，使用。
        ApprovalFlow qingjiaFlow = new ApprovalFlow();
        qingjiaFlow.setToUser("董事长");
        qingjiaFlow.setToUserId(user.getUserID());
        qingjiaFlow.setType(1);
        qingjiaFlow.setItem(qingjia);
        qingjiaFlow.setAmountCeiling((double)-1);
        qingjiaFlow.setLimitQuantity((double) -1);
        qingjiaFlow.setLevel(1);
        qingjiaFlow.setUserName(user.getUserName());
        approvalFlowDao.save(qingjiaFlow);

        ApprovalItem qingjiaBu = new ApprovalItem();  //补报请假
        qingjiaBu.setBelongTo(organization.getId());
        qingjiaBu.setDescription("补报请假");
        qingjiaBu.setName("补报请假");
        qingjiaBu.setStatus(0);
        qingjiaBu.setLevel(1);
        qingjiaBu.setEnabled(false);
        qingjiaBu.setType("Apply");
        qingjiaBu.setOrders(45);
        qingjiaBu.setCode("supplementaryLeave");
        qingjiaBu.setCreateName("系统");
        qingjiaBu.setCreateDate(new Date());
        qingjiaBu.setOpenDate(NewDateUtils.today(new Date()));
        qingjiaBu.setApproveStatus("2");
        qingjiaBu.setAuditDate(new Date());
        qingjiaBu.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(qingjiaBu);

        ApprovalItem qingjiaRule = new ApprovalItem();  //请班时限规则
        qingjiaRule.setBelongTo(organization.getId());
        qingjiaRule.setDescription("请班时限规则");
        qingjiaRule.setName("请班时限规则");
        qingjiaRule.setStatus(0);
        qingjiaRule.setLevel(1);
        qingjiaRule.setType("Apply");
        qingjiaRule.setOrders(50);
        qingjiaRule.setCode("leaveRule");
        qingjiaRule.setCreateName("系统");
        qingjiaRule.setCreateDate(new Date());
        qingjiaRule.setOpenDate(NewDateUtils.today(new Date()));
        qingjiaRule.setApproveStatus("2");
        qingjiaRule.setAuditDate(new Date());
        qingjiaRule.setUpperLimit(new BigDecimal(30));
        approvalItemDao.save(qingjiaRule);

        ApprovalItem caiwu = new ApprovalItem();
        caiwu.setBelongTo(organization.getId());
        caiwu.setDescription("报销申请");
        caiwu.setLevel(1);
        caiwu.setName("报销");
        caiwu.setStatus(1);
        caiwu.setType("Apply");
        caiwu.setOrders(70);
        caiwu.setCode("reimburseApply");
        caiwu.setCreateName("系统");
        caiwu.setCreateDate(new Date());
        caiwu.setOpenDate(NewDateUtils.today(new Date()));
        caiwu.setApproveStatus("2");
        caiwu.setAuditDate(new Date());
        caiwu.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(caiwu);

        //报销审批流程初始化
        ApprovalFlow baoxiao = new ApprovalFlow();
        baoxiao.setToUser("董事长");
        baoxiao.setType(1);
        baoxiao.setLevel(1);
        baoxiao.setItem(caiwu);
        baoxiao.setToUserId(user.getUserID());
        baoxiao.setUserName(user.getUserName());
//        Integer amc = -1;
        baoxiao.setAmountCeiling((double)-1);
        baoxiao.setLimitAmount(BigDecimal.valueOf(-1, 2));
        approvalFlowDao.save(baoxiao);

        //付款审批
        ApprovalItem paymentApproval = new ApprovalItem();
        paymentApproval.setBelongTo(organization.getId());
        paymentApproval.setDescription("付款审批");
        paymentApproval.setLevel(1);
        paymentApproval.setName("付款");
        paymentApproval.setStatus(1);
        paymentApproval.setType("Approval");
        paymentApproval.setOrders(80);
        paymentApproval.setCode("paymentApproval");
        paymentApproval.setCreateName("系统");
        paymentApproval.setCreateDate(new Date());
        paymentApproval.setOpenDate(NewDateUtils.today(new Date()));
        paymentApproval.setApproveStatus("2");
        paymentApproval.setAuditDate(new Date());
        paymentApproval.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(paymentApproval);

        //付款审批流程初始化
        ApprovalFlow paymentFlow = new ApprovalFlow();
        paymentFlow.setToUser("董事长");
        paymentFlow.setType(1);
        paymentFlow.setLevel(1);
        paymentFlow.setItem(paymentApproval);
        paymentFlow.setLimitAmount(new BigDecimal(-1));
        paymentFlow.setAmountCeiling((double) -1);
        paymentFlow.setToUserId(user.getUserID());
        paymentFlow.setUserName(user.getUserName());
        approvalFlowDao.save(paymentFlow);

        //付款复核
        ApprovalItem paymentAudit = new ApprovalItem();
        paymentAudit.setBelongTo(organization.getId());
        paymentAudit.setDescription("付款复核");
        paymentAudit.setLevel(1);
        paymentAudit.setName("付款复核");
        paymentAudit.setStatus(1);
        paymentAudit.setType("Approval");
        paymentAudit.setOrders(90);
        paymentAudit.setCode("paymentAudit");
        paymentAudit.setCreateName("系统");
        paymentAudit.setCreateDate(new Date());
        paymentAudit.setOpenDate(NewDateUtils.today(new Date()));
        paymentAudit.setApproveStatus("2");
        paymentAudit.setAuditDate(new Date());
        paymentAudit.setUpperLimit(new BigDecimal(-1));
        paymentAudit.setPreviousItem(paymentApproval.getId()); //付款审批的id
        approvalItemDao.save(paymentAudit);

        //付款复核流程初始化【机构创建时财务还没有】
//        User paymentAuditUser = userService.getUserByRoleCode(user.getOid(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
//        ApprovalFlow paymentAuditFlow = new ApprovalFlow();
//        paymentAuditFlow.setToUser("董事长");
//        paymentAuditFlow.setType(1);
//        paymentAuditFlow.setLevel(1);
//        paymentAuditFlow.setItem(paymentAudit);
//        paymentAuditFlow.setLimitAmount(new BigDecimal(-1));
//        paymentAuditFlow.setAmountCeiling(new Double(-1));
//        paymentAuditFlow.setToUserId(user.getUserID());
//        paymentAuditFlow.setUserName(user.getUserName());
//        approvalFlowDao.save(paymentAuditFlow);

        //1.142徐智修改  修改考勤审批 为默认超管审批
        ApprovalItem kaoQin = new ApprovalItem();
        kaoQin.setBelongTo(organization.getId());
        kaoQin.setLevel(1);
        kaoQin.setName("修改考勤");
        kaoQin.setDescription("修改考勤审批");
        kaoQin.setType("Update");
        kaoQin.setStatus(1);
        kaoQin.setOrders(100);
        kaoQin.setCode("workAttendanceApply");
        kaoQin.setCreateName("系统");
        kaoQin.setCreateDate(new Date());
        kaoQin.setOpenDate(NewDateUtils.today(new Date()));
        kaoQin.setApproveStatus("2");
        kaoQin.setAuditDate(new Date());
        kaoQin.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(kaoQin);
        //考勤修改流程初始化
        ApprovalFlow kaoQinFlow = new ApprovalFlow();
        kaoQinFlow.setToUser("董事长");
        kaoQinFlow.setType(1);
        kaoQinFlow.setLevel(1);
        kaoQinFlow.setItem(kaoQin);
        kaoQinFlow.setLimitAmount(new BigDecimal(-1));
        kaoQinFlow.setAmountCeiling((double) -1);
        kaoQinFlow.setToUserId(user.getUserID());
        kaoQinFlow.setUserName(user.getUserName());
        approvalFlowDao.save(kaoQinFlow);

        ApprovalItem dangAn = new ApprovalItem();
        dangAn.setName("修改职工档案");
        dangAn.setDescription("更改删除");
        dangAn.setType("Update,Delete");
        dangAn.setStatus(0);
        dangAn.setBelongTo(organization.getId());
        dangAn.setOrders(140);
        dangAn.setCode("archivesApply");
        dangAn.setCreateName("系统");
        dangAn.setCreateDate(new Date());
        approvalItemDao.save(dangAn);

        ApprovalItem gangWei = new ApprovalItem();
        gangWei.setName("修改岗位设置");
        gangWei.setDescription("更改删除");
        gangWei.setType("Update,Delete");
        gangWei.setStatus(0);
        gangWei.setBelongTo(organization.getId());
        gangWei.setOrders(170);
        gangWei.setCode("postApply");
        gangWei.setCreateName("系统");
        gangWei.setCreateDate(new Date());
        approvalItemDao.save(gangWei);

        Integer status=0;  //判断成品库状态 0-不启用， 1-启用
        if (orgPopedomService.getOrgPopedomByMid(oid,"xb")) {  // 仓库管理-成品库
            status=1;   // 0-不启用， 1-启用
        }

        //来自客户订单的评审   1.326 销售两种模式   改名 销售订单的评审
        ApprovalItem customerOrder = new ApprovalItem();
        customerOrder.setBelongTo(organization.getId());
        customerOrder.setDescription("销售订单的评审");
        customerOrder.setLevel(1);
        customerOrder.setName("销售订单的评审");
        customerOrder.setStatus(status);  //1-需要评审 0-不需要
        customerOrder.setType("Review");   //评审
        customerOrder.setOrders(175);
        customerOrder.setCode("ordersReview");
        customerOrder.setCreateName("系统");
        customerOrder.setCreateDate(new Date());
        customerOrder.setOpenDate(NewDateUtils.today(new Date()));
        customerOrder.setApproveStatus("2");
        customerOrder.setAuditDate(new Date());
        customerOrder.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(customerOrder);

        //采购来材料的入库检验
        ApprovalItem materialInCheck = new ApprovalItem();
        materialInCheck.setBelongTo(organization.getId());
        materialInCheck.setDescription("采购来材料的入库检验");
        materialInCheck.setLevel(1);
        materialInCheck.setName("采购来材料的入库检验");
        materialInCheck.setStatus(1);
        materialInCheck.setType("Check");  //检验
        materialInCheck.setOrders(180);
        materialInCheck.setCode("materialInCheck");
        materialInCheck.setCreateName("系统");
        materialInCheck.setCreateDate(new Date());
        materialInCheck.setOpenDate(NewDateUtils.today(new Date()));
        materialInCheck.setApproveStatus("2");
        materialInCheck.setAuditDate(new Date());
        materialInCheck.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(materialInCheck);

        //货物入成品库前的检验
        ApprovalItem productInCheck = new ApprovalItem();
        productInCheck.setBelongTo(organization.getId());
        productInCheck.setDescription("货物入成品库前的检验");
        productInCheck.setLevel(1);
        productInCheck.setName("货物入成品库前的检验");
        productInCheck.setStatus(1);
        productInCheck.setType("Check");  //检验
        productInCheck.setOrders(185);
        productInCheck.setCode("productInCheck");
        productInCheck.setCreateName("系统");
        productInCheck.setCreateDate(new Date());
        productInCheck.setOpenDate(NewDateUtils.today(new Date()));
        productInCheck.setApproveStatus("2");
        productInCheck.setAuditDate(new Date());
        productInCheck.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(productInCheck);

        //成品出库时物流人员的复核   1.326销售两种模式  2025/1/6
        ApprovalItem productLogisticsItem = new ApprovalItem();
        productLogisticsItem.setBelongTo(organization.getId());
        productLogisticsItem.setDescription("成品出库时物流人员的复核");
        productLogisticsItem.setLevel(1);
        productLogisticsItem.setName("成品出库时物流人员的复核");
        productLogisticsItem.setStatus(0); // 0不需物流复核，  1需
        productLogisticsItem.setType("Check");  //检验
        productLogisticsItem.setOrders(187);
        productLogisticsItem.setCode("productLogisticsCheck");
        productLogisticsItem.setCreateName("系统");
        productLogisticsItem.setCreateDate(new Date());
        productLogisticsItem.setOpenDate(NewDateUtils.today(new Date()));
        productLogisticsItem.setApproveStatus("2");
        productLogisticsItem.setAuditDate(new Date());
        productLogisticsItem.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(productLogisticsItem);

        //商品与产品的关联改为‘产品基本信息的创建模式’ 1.231差额处理(1) 2023/1/30
        ApprovalItem commodityProduct = new ApprovalItem();
        commodityProduct.setBelongTo(organization.getId());
        commodityProduct.setDescription("产品基本信息的创建模式");
        commodityProduct.setLevel(1);
        commodityProduct.setName("产品基本信息的创建模式");
        commodityProduct.setStatus(status);  //0-自动关联(默认，不需要审批),1-手工关联(需要审批)
        commodityProduct.setType("Correlation");  //关联
        commodityProduct.setOrders(190);
        commodityProduct.setCode("commodityProduct");
        commodityProduct.setCreateName("系统");
        commodityProduct.setCreateDate(new Date());
        commodityProduct.setOpenDate(NewDateUtils.today(new Date()));
        commodityProduct.setApproveStatus("2");
        commodityProduct.setAuditDate(new Date());
        commodityProduct.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(commodityProduct);

        //产品_模式设置表
        PdModelSettings pdModelSettings = new PdModelSettings();
        pdModelSettings.setOrg(user.getOid());
        pdModelSettings.setInstance(commodityProduct.getId());
        pdModelSettings.setApprovalStatus(RoleService.ApprovalStatus.applicationPassed.getIndex());//审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-撤回
        pdModelSettings.setCorrelationModel(RoleService.CorrelationModel.manualAssociation.getIndex());//关联模式:0-自动关联(默认，不需要审批),1-手工关联(需要审批)
//        pdModelSettings.setCorrelationModel(null);//关联模式:0-自动关联(默认，不需要审批),1-手工关联(需要审批)
        pdModelSettings.setGeneralModel(RoleService.GeneralModel.model2.getIndex());//通用模式:1-模式1(默认),2-模式2
        pdModelSettings.setDedicatedModel(RoleService.DedicatedModel.model1.getIndex());//专用模式:1-模式1(默认),2-模式2
        pdModelSettings.setReviseModel(RoleService.ReviseModel.model1.getIndex());//修改人模式:1-由有权限创建商品的职工修改(默认),2-由有产品操作权限的职工修改
        pdModelSettings.setEnabledTime(new Date());
        pdModelSettings.setCreateName("系统");
        pdModelSettings.setCreateTime(new Date());
        pdModelSettingsDao.save(pdModelSettings);


        //成品库   1.326销售两种模式  2025/1/6
        ApprovalItem finishedProductItem = new ApprovalItem();
        finishedProductItem.setBelongTo(organization.getId());
        finishedProductItem.setDescription("成品库");
        finishedProductItem.setLevel(1);
        finishedProductItem.setName("成品库");
//        if (orgPopedomService.getOrgPopedomByMid(oid,"xb")) {  // 仓库管理-成品库
//            finishedProductItem.setStatus(1);   // 0-不启用， 1-启用
//        }else {
//            finishedProductItem.setStatus(0);
//        }
        finishedProductItem.setStatus(status);
        finishedProductItem.setType("Check");  //检验
        finishedProductItem.setOrders(195);
        finishedProductItem.setCode("finishedProductCheck");
        finishedProductItem.setCreateName("系统");
        finishedProductItem.setCreateDate(new Date());
        finishedProductItem.setOpenDate(NewDateUtils.today(new Date()));
        finishedProductItem.setApproveStatus("2");
        finishedProductItem.setAuditDate(new Date());
        finishedProductItem.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(finishedProductItem);

        ApprovalItem approval = new ApprovalItem();
        approval.setName("修改审批设置");
        approval.setDescription("更改");
        approval.setType("Update");
        approval.setStatus(1);
        approval.setLevel(1);
        approval.setBelongTo(organization.getId());
        approval.setOrders(200);
        approval.setCode("itemApply");
        approval.setCreateName("系统");
        approval.setCreateDate(new Date());
        approvalItemDao.save(approval);

        ApprovalFlow approvalflow = new ApprovalFlow();
        approvalflow.setToUser("超管");
        approvalflow.setToUserId(user.getUserID());
        approvalflow.setLevel(1);//第几级
        approvalflow.setType(1);
        approvalflow.setItem(approval);
        approvalflow.setUserName(user.getUserName());
        approvalFlowDao.save(approvalflow);//新增

        //1.232  孙文 新增采购审批设置 默认董事长审批

        ApprovalItem caigou = new ApprovalItem();
        caigou.setBelongTo(organization.getId());
        caigou.setLevel(1);
        caigou.setName("采购审批设置");
        caigou.setDescription("采购审批设置审批");
        caigou.setType("Update");
        caigou.setStatus(1);
        caigou.setOrders(210);
        caigou.setCode("purchaseApprovalSettings");
        caigou.setCreateName("系统");
        caigou.setCreateDate(new Date());
        caigou.setOpenDate(NewDateUtils.today(new Date()));
        caigou.setApproveStatus("2");
        caigou.setAuditDate(new Date());
        caigou.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(caigou);



        //1.281仓库模式审批-zy
        ApprovalItem stockMode = new ApprovalItem();
        stockMode.setBelongTo(organization.getId());
        stockMode.setLevel(1);
        stockMode.setName("仓库的模式");
        stockMode.setDescription("更改仓库模式");
        stockMode.setType("Update");
        stockMode.setStatus(status);   //1 智能库    0-非智能
        stockMode.setOrders(220);
        stockMode.setCode("stockModeChange");
        stockMode.setCreateName("系统");
        stockMode.setCreateDate(new Date());
        stockMode.setOpenDate(NewDateUtils.today(new Date()));
        stockMode.setApproveStatus("2");
        stockMode.setAuditDate(new Date());
        stockMode.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(stockMode);

        //1.316入库检查者设置-zy
        ApprovalItem inStockMode = new ApprovalItem();
        inStockMode.setBelongTo(organization.getId());
        inStockMode.setLevel(1);
        inStockMode.setName("外购物料入库时，包装完好情况的检查者");
        inStockMode.setDescription("外购物料入库时，包装完好情况的检查者");
        inStockMode.setType("Update");
        inStockMode.setStatus(0);
        inStockMode.setOrders(230);
        inStockMode.setCode("inStockCheckerSet");
        inStockMode.setCreateName("系统");
        inStockMode.setCreateDate(new Date());
        inStockMode.setOpenDate(NewDateUtils.today(new Date()));
        inStockMode.setApproveStatus("2");
        inStockMode.setAuditDate(new Date());
        inStockMode.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(inStockMode);

        //采购审批设置
        ApprovalFlow caigouFlow = new ApprovalFlow();
        caigouFlow.setToUser("董事长");
        caigouFlow.setType(1);
        caigouFlow.setLevel(1);
        caigouFlow.setItem(caigou);
        caigouFlow.setLimitAmount(new BigDecimal(-1));
        caigouFlow.setAmountCeiling((double) -1);
        caigouFlow.setToUserId(user.getUserID());
        caigouFlow.setUserName(user.getUserName());
        approvalFlowDao.save(caigouFlow);

        //初始化备用金
        FinanceAccount f = new FinanceAccount();
        f.setOrg(organization);
        f.setPreviousBalance(BigDecimal.valueOf(0));
        f.setBalance(BigDecimal.valueOf(0));
        f.setAccountType(1);
        f.setApproveStatus("1");
        f.setCreateDate(new Date());
        f.setCreateName("系统初始化");
        f.setDebit(BigDecimal.valueOf(0));
        f.setCredit(BigDecimal.valueOf(0));
        f.setInitialAmount(BigDecimal.valueOf(0));
        f.setAccountStatus(1);
        f.setCashable(1);//是否可取现  1：可取现  2：不可取现
        f.setName("备用金/现金账户");
        f.setBankName("备用金/现金账户");
        financeAccountDao.save(f);

        //备用金日结
        AccountPeriod ap = new AccountPeriod();
        ap.setFid(f.getId());
        ap.setAccount(String.valueOf(f.getId()));
        ap.setPreviousBalance(BigDecimal.valueOf(0));
        ap.setCredit(BigDecimal.valueOf(0));
        ap.setDebit(BigDecimal.valueOf(0));
        ap.setOrg(organization);
        ap.setBalance(BigDecimal.valueOf(0));
        ap.setBeginDate(new Date());
        ap.setAccountType(1);
        ap.setBuildState("未生成");
        ap.setPeriodType(2);
//        ap.setAccountId(f);
        ap.setAccountId(f.getId());

        ap.setCreateDate(new Date());
        ap.setCreateName("系统生成");
        accountPeroidDao.save(ap);

        //备用金月结
        AccountPeriod a = new AccountPeriod();
        a.setBalance(f.getBalance());
        a.setAccount(f.getId().toString());
        f.setPreviousBalance(new BigDecimal(0));
        a.setCreateDate(new Date());
        a.setCreateName("系统生成");
        a.setPreviousBalance(new BigDecimal(0));
        FinanceUtils.ap(f, null, organization, user, a, accountService, 1);

        subjectSettingService.accountInitialization(oid, user.getUserID(), user.getUserName());//会计模块初始化

        pdCategoryService.initPdCategory(oid,"通用型商品","org");  //1.346商品之分类 2025/06

    }

    @Override
    public Map<String, Object> initialization(Date createDate,String fullName, String name, String userName, String phone, List<OrgPopedom> orgPopedomList, String operationUserMobile, String tyDomain, String uploadStorageType, String initState, OrgType orgType, Integer pid, String roleCode,AuthInfoDto authInfo,String address,String registeredAddress) throws ParseException, InvocationTargetException, IllegalAccessException {

        Map<String, Object> map = new HashMap<>();
        Organization organization = initializationOrg(createDate,pid, orgType, phone, "系统", name, initState, uploadStorageType, address,registeredAddress, orgPopedomList, map); //初始化机构数据
        User user = initializationUser(organization, phone, userName, roleCode);// 初始化机构 第一个user  总机构和 主播助手 是董事长， 1.214多地点第一个人有三种情况（总机构选择的人员，新录入的临时管理员，总机构董事长亲自操作）

        initializationBusinessData(organization, user); //初始化其他模块业务数据

        if (organization.getPid() == 0) {  // 新建主机构 发短信，直播助手 与 子机构不用发
            if (tyDomain == null || "".equals(tyDomain)) {
                tyDomain = System.getProperty("BaseUrl");
            }
            if (!"liveHelper".equals(organization.getCode())) {
//                String cont = "欢迎您使用wonderss管理系统。\n" +
//                        "请点击下载手机端，安装后使用您" + phone + "手机号登录，并按页面提示操作。\n" +
//                        "用电脑登录" + tyDomain + "也可进行同样操作。\n" +
//                        "有疑问可随时拨打本人手机" + operationUserMobile + "。";
//        System.getProperty("BaseUrl")
//                sendMessageService.sendMessage(null, oid, phone, cont);//发短信

                // 短信重构 2024/5/22 李旭更改
                String phoneNo=phone;
                String url = tyDomain;
                Map<String, String> params = new HashMap<String, String>(3) {{
                    put("phoneNo", phoneNo);
                    put("url", url);
                    put("mobile", operationUserMobile);
                }};
                smsService.sendMessage(SmsService.SendType.sys, phoneNo, null, TemplateService.SmsContentTemplate.addOrg, params, null);

            }
        }

        return map;

    }

    // 1.214多地点子机构
    @Override
    public Map<String, Object> addSonOrg(User loginUser, String name, Integer passiveUserId, String userName, String phone, String address,HttpServletRequest request,AuthInfoDto authInfo) throws ParseException, InvocationTargetException, IllegalAccessException {
        Organization mainOrganization = loginUser.getOrganization();// 主机构
        List<OrgPopedom> mainOrgPopedomList = orgPopedomService.getOrgPopedomListByOid(loginUser.getOid());// 主机构的菜单
//        List<OrgPopedom> mainOrgPopedomList=new ArrayList<>(mainOrganization.getOrgPopedomHashSet());
        List<OrgPopedom> orgPopedomList = new ArrayList<>();// 子机构 该有的菜单
        for (OrgPopedom orgPopedom : mainOrgPopedomList) {
            if (!OrgPopedom.getSonOrgNotMids().contains(orgPopedom.getMid())) {
                orgPopedomList.add(orgPopedom);
            }
        }
        Map<String, Object> map = new HashMap<>();
        if (!StringUtils.isEmpty(userName) && !StringUtils.isEmpty(phone)) {
            User repeatUser = userService.getUserByOrgSonOrgMobile(mainOrganization.getId(), phone);
            if (repeatUser != null) {
                map.put("state", 2); //录入的临时管理员手机号 与总机构或子机构员工重复，新增失败
                return map;
            }
        }
        Organization organization = initializationOrg(new Date(),mainOrganization.getId(), OrgType.sonOrg, loginUser.getMobile(), loginUser.getUserName(), name, "0", mainOrganization.getUploadStorageType(), address,null, orgPopedomList, map); //初始化子机构数据

        User sonSuperUser = initializationUser(organization, loginUser.getMobile(), loginUser.getUserName(), "initialBeforeSuper"); //总机构董事长也是子机构董事长

        if (passiveUserId != null) { //选人
            User passiveUser = userService.getUserByID(passiveUserId);// 选择了总机构的员工 作为子机构的临时管理员（导入）
            userService.addTemporaryAdmin(sonSuperUser, organization, passiveUser.getUserName(), passiveUser.getMobile(),request,authInfo); //选择的user 用姓名和手机号 生成一条子机构的 临时管理员
        } else if (!StringUtils.isEmpty(userName) && !StringUtils.isEmpty(phone)) {
            userService.addTemporaryAdmin(sonSuperUser, organization, userName, phone,request,authInfo); //用录入的姓名和手机号 生成一条子机构的 临时管理员
        } //否则是董事长自己操作 不录新人

        initializationBusinessData(organization, sonSuperUser); //初始化其他模块业务数
        map.put("state", 1); //成功

        return map;
    }

    @Override
    public Map<String, Object> updateSonOrg(User loginUser, String name, Integer sonOid, Integer passiveUserId, String userName, String phone, String address,HttpServletRequest request,AuthInfoDto authInfo) throws ParseException, InvocationTargetException, IllegalAccessException {
        Organization sonOrg=orgDao.get(sonOid);
        User sonUser=userService.getUserByRoleCode(sonOid,"temporary");// 查询临时管理员
        if (sonUser!=null){
            userService.deleteUser(sonUser); // 删除临时管理员
        }
        userImportService.deleteUserImportsByOid(sonOid);
        User sonSuperUser=userService.getUserByRoleCode(sonOid,"super");

        sonOrg.setAddress(address);
        sonOrg.setName(name);
        orgDao.update(sonOrg);

        if(passiveUserId!=null){ //选人
            User passiveUser=userService.getUserByID(passiveUserId);// 选择了总机构的员工 作为子机构的临时管理员（导入）
            userService.addTemporaryAdmin(sonSuperUser,sonOrg,passiveUser.getUserName(),passiveUser.getMobile(),request,authInfo); //选择的user 用姓名和手机号 生成一条子机构的 临时管理员
        }else if (!StringUtils.isEmpty(userName)&&StringUtils.isEmpty(phone)){
            userService.addTemporaryAdmin(sonSuperUser,sonOrg,userName,phone,request,authInfo); //用录入的姓名和手机号 生成一条子机构的 临时管理员
        } //否则是董事长自己操作 不录新人
        return null;
    }

    @Override
    public HashMap<String, ArrayList<PopedomDto>> getOrgPopedomShow(User user) {
        String authLeafMid = popedomDao.getMidByCode("AuthLeaf");
        int oid = user.getOid();
        HashMap<String, ArrayList<PopedomDto>> result = new HashMap<>();
        ArrayList<PopedomDto> assignedList = new ArrayList<>();
        ArrayList<PopedomDto> unAssignedList = new ArrayList<>();
        HashMap<String, Boolean> orgSearch = orgPopedomDao.getOrgPopedomMapByOid(oid);
        HashMap<String, Boolean> userSearch = new HashMap<>();
        if (user.getRoleCode().equalsIgnoreCase("super")) {
            Set<OrgPopedom> orgPopedoms = user.getOrganization().getOrgPopedomHashSet();
            for (OrgPopedom item : orgPopedoms) {
                userSearch.put(item.getMid(), true);
            }
        } else {
            Set<UserPopedom> userPopedoms = user.getUserPopedomHashSet();
            for (UserPopedom item : userPopedoms) {
                userSearch.put(item.getMid(), true);
            }
        }
        getSubPopedomList(oid, "0", orgSearch,/*userSearch,*/assignedList, unAssignedList);
        result.put("assigned", assignedList);
        if (RoleTmpl.hasUnAssignedAuth(user.getRoleCode())//user是超管和总务高管
                || (userSearch.containsKey(authLeafMid)//user就是小高管
                && user.getManagerCode().equalsIgnoreCase("general"))) {//总务小高管
            result.put("unAssigned", unAssignedList);
        }
        return result;
    }

    protected ArrayList<UserDto> getSubPopedomList(int oid, String mid, HashMap<String, Boolean> orgSearch,/*HashMap<String,Boolean>userSearch,*/List<PopedomDto> assignedList, List<PopedomDto> unAssignedList) {
        String hql;
        if (mid.equalsIgnoreCase("0")) {
            hql = "select new cn.sphd.miners.modules.system.dto.PopedomDto(mid, pid, name, url, case when description is not null or length(trim(description))>0 then description else '' end) from Popedom where (pid=:pid or pid is null) and isView='1' order by orders,mid";
        } else {
            hql = "select new cn.sphd.miners.modules.system.dto.PopedomDto(mid, pid, name, url, case when description is not null or length(trim(description))>0 then description else '' end) from Popedom where pid=:pid and isView='1' order by orders,mid";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("pid", mid);
        List<PopedomDto> list = popedomDao.getListByHQLWithNamedParams(hql, map);
        if (list.isEmpty()) {
            if (orgSearch.containsKey(mid)/* && userSearch.containsKey(mid)*/) {
                map.clear();
                map.put("oid", oid);
                map.put("mid", mid);
                hql = "select new cn.sphd.miners.modules.system.dto.UserDto(u.userID,case when u.userName is not null and length(trim(u.userName))>0 then u.userName else u.realName end,u.departName,u.postName) from UserPopedom up join up.user u where u.oid=:oid and up.mid=:mid and u.isDuty='1' and u.roleCode " + RoleTmpl.getIsStaffWhere() + " order by up.userId";
                ArrayList<UserDto> users = (ArrayList) userPopedomDao.getListByHQLWithNamedParams(hql, map);
                return users;
            }
        } else {
            for (PopedomDto item : list) {
                ArrayList<PopedomDto> subAssignedList = new ArrayList<>();
                ArrayList<PopedomDto> subUnAssignedList = new ArrayList<>();
                ArrayList<UserDto> users = getSubPopedomList(oid, item.getMid(), orgSearch,/*userSearch,*/subAssignedList, subUnAssignedList);
                if (users == null) {
                    if (!subAssignedList.isEmpty()) {
                        PopedomDto tmp = (PopedomDto) SerializationUtils.clone(item);
                        tmp.setSubPopdoms(subAssignedList);
                        assignedList.add(tmp);
                    }
                    if (!subUnAssignedList.isEmpty()) {
                        PopedomDto tmp = (PopedomDto) SerializationUtils.clone(item);
                        tmp.setSubPopdoms(subUnAssignedList);
                        unAssignedList.add(tmp);
                    }
                } else {
//                    if(users.isEmpty()){
//                        unAssignedList.add(item);
//                    }else{
                    item.setUsers(users);
                    assignedList.add(item);
//                    }
                }
            }
        }
        return null;
    }

    @Override
    @Cacheable(value = "orgWithUserTree")
    public List<OrganizationDto> getOrgWithUserTreeByOid(Integer oid) {
        System.out.println("获取getOrgWithUserTreeByOid开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setSubOrgWithUser(org, oid, (ArrayList<String>) null);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty='1' and roleCode!='browse'";
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            others.setUserList(users);
            result.add(others);
        }
        System.out.println("获取getOrgWithUserTreeByOid结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }

    @Override
    public List<OrganizationDto> getOrgWithUserTreeByOidLocking(Integer oid) {
        System.out.println("获取getOrgWithUserTreeByOid开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setSubOrgWithUserLocking(org, oid, (ArrayList<String>) null);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty in('1','9') and roleCode!='browse'";
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            others.setUserList(users);
            result.add(others);
        }
        System.out.println("获取getOrgWithUserTreeByOid结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }

    protected OrganizationDto setSubOrgWithUserLocking(OrganizationDto org, Integer oid, ArrayList<String> roleCodes) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and department = :department and isDuty in('1','9') and roleCode!='browse'";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("department", org.getId().toString());
        if (roleCodes != null) {
            hql += " and roleCode in :roleCodes";
            params.put("roleCodes", roleCodes);
        }
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            org.setUserList(users);
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrgWithUserLocking(organization, oid, roleCodes);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (users.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }

    @Override
    public List<OrganizationDto> getOrgWithUserTreeByOidLocking(Integer oid, ArrayList<String> roleCodes) {
        System.out.println("获取getOrgWithUserTreeByOid+roleCodes开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setSubOrgWithUserLocking(org, oid, roleCodes);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty in('1','9') and roleCode in :roleCodes";
        params.put("roleCodes", roleCodes);
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            others.setUserList(users);
            result.add(others);
        }
        System.out.println("获取getOrgWithUserTreeByOid+roleCodes结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }

    @Override
    @Cacheable(value = "orgWithUserTree")
    public List<OrganizationDto> getOrgWithUserTreeByOid(Integer oid, ArrayList<String> roleCodes) {
        System.out.println("获取getOrgWithUserTreeByOid+roleCodes开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setSubOrgWithUser(org, oid, roleCodes);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty='1' and roleCode in :roleCodes";
        params.put("roleCodes", roleCodes);
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            others.setUserList(users);
            result.add(others);
        }
        System.out.println("获取getOrgWithUserTreeByOid+roleCodes结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }

    protected OrganizationDto setSubOrgWithUser(OrganizationDto org, Integer oid, ArrayList<String> roleCodes) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and department = :department and isDuty='1' and roleCode!='browse'";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("department", org.getId().toString());
        if (roleCodes != null) {
            hql += " and roleCode in :roleCodes";
            params.put("roleCodes", roleCodes);
        }
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            org.setUserList(users);
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrgWithUser(organization, oid, roleCodes);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (users.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }

    @Override
    public List<OrganizationDto> getOrgWithUserTreeByUids(Integer oid, List<Integer> userIds) {
        ArrayList<OrganizationDto> result = new ArrayList<>();
        if (!userIds.isEmpty()) {
            String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
            HashMap<String, Object> params = new HashMap<>();
            params.put("oid", oid);
            List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
            for (OrganizationDto org : organizations) {
                OrganizationDto o = setSubOrgWithUser(org, oid, userIds);
                if (o != null) {
                    result.add(o);
                }
            }
            OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty='1' and userID in :userIds";
            params.put("userIds", userIds);
            ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!users.isEmpty()) {
                others.setUserList(users);
                result.add(others);
            }
        }
        return result;
    }

    @Override
    public List<OrganizationDto> getOrgWithUserTreeByUidsLocking(Integer oid, List<Integer> userIds) {
        ArrayList<OrganizationDto> result = new ArrayList<>();
        if (!userIds.isEmpty()) {
            String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
            HashMap<String, Object> params = new HashMap<>();
            params.put("oid", oid);
            List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
            for (OrganizationDto org : organizations) {
                OrganizationDto o = setSubOrgWithUser(org, oid, userIds);
                if (o != null) {
                    result.add(o);
                }
            }
            OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty in('1','9') and userID in :userIds";
            params.put("userIds", userIds);
            ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!users.isEmpty()) {
                others.setUserList(users);
                result.add(others);
            }
        }
        return result;
    }

    protected OrganizationDto setSubOrgWithUser(OrganizationDto org, Integer oid, List<Integer> userIds) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty in('1','9') and userID in :userIds";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("department", org.getId().toString());
        params.put("userIds", userIds);
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            org.setUserList(users);
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrgWithUser(organization, oid, userIds);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (users.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }

    @Override
    public List<OrganizationDto> getOrgWithAuthUserTree(Integer oid, List<Integer> userIds) {
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setSubOrgWithUserByAuth(org, oid, userIds);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);

        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty='1' and roleCode in ('staff')";
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!userIds.isEmpty()) {
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty='1' and userID in :userIds";
            params.put("userIds", userIds);
            ArrayList<UserDto> usersAuth = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!users.isEmpty()) {
                for (UserDto ua : users) {
                    Integer uaId = ua.getUserID();
                    for (UserDto u : usersAuth) {
                        Integer ui = u.getUserID();
                        if (ui.equals(uaId)) {
                            ua.setRoleCode("1");
                        }
                    }
                }
                others.setUserList(users);
                result.add(others);
            }
        } else {
            others.setUserList(users);
            result.add(others);
        }
        return result;
    }

    @Override
    public List<OrganizationDto> getOrgWithAuthUserTreeLocking(Integer oid, List<Integer> userIds) {
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setSubOrgWithUserByAuthLocking(org, oid, userIds);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);

        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(imgPath,userID, case when userName is not null and length(trim(userName))>0 then userName else realName end, departName, mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty in('1','9','5') and roleCode in ('staff','see')";
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!userIds.isEmpty()) {
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty in('1','9','5') and userID in :userIds";
            params.put("userIds", userIds);
            ArrayList<UserDto> usersAuth = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!users.isEmpty()) {
                for (UserDto ua : users) {
                    Integer uaId = ua.getUserID();
                    for (UserDto u : usersAuth) {
                        Integer ui = u.getUserID();
                        if (ui.equals(uaId)) {
                            ua.setRoleCode("1");
                        }
                    }
                }
                others.setUserList(users);
                result.add(others);
            }
        } else {
            others.setUserList(users);
            result.add(others);
        }
        return result;
    }

    protected OrganizationDto setSubOrgWithUserByAuthLocking(OrganizationDto org, Integer oid, List<Integer> userIds) {
        String hqlAll = "select new cn.sphd.miners.modules.system.dto.UserDto(imgPath,userID, case when userName is not null and length(trim(userName))>0 then userName else realName end, departName, mobile) from User where oid = :oid and department = :department and isDuty in('1','9','5') and roleCode in ('staff','see')";
        HashMap<String, Object> paramsAll = new HashMap<>();
        paramsAll.put("oid", oid);
        paramsAll.put("department", org.getId().toString());
        /*if (roleCodes != null){
            hqlAll += " and roleCode in :roleCodes";
            paramsAll.put("roleCodes",roleCodes);
        }*/
        ArrayList<UserDto> usersAll = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hqlAll, paramsAll);
        String hql = null;
        HashMap<String, Object> params = new HashMap<>();
        if (!userIds.isEmpty()) {
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(imgPath,userID, case when userName is not null and length(trim(userName))>0 then userName else realName end, departName, mobile) from User where oid = :oid and department = :department and isDuty in('1','9','5') and userID in :userIds";
            params.put("oid", oid);
            params.put("department", org.getId().toString());
            params.put("userIds", userIds);
            ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!usersAll.isEmpty()) {
                for (UserDto ua : usersAll) {
                    Integer uaId = ua.getUserID();
                    for (UserDto u : users) {
                        Integer ui = u.getUserID();
                        if (ui.equals(uaId)) {
                            ua.setRoleCode("1");
                        }
                    }
                }
                org.setUserList(usersAll);
            }
        } else {
            if (!usersAll.isEmpty()) {
                org.setUserList(usersAll);
            }
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrgWithUserByAuthLocking(organization, oid, userIds);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (usersAll.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }


    protected OrganizationDto setSubOrgWithUserByAuth(OrganizationDto org, Integer oid, List<Integer> userIds) {
        String hqlAll = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end) from User where oid = :oid and department = :department and isDuty='1' and roleCode in ('staff')";
        HashMap<String, Object> paramsAll = new HashMap<>();
        paramsAll.put("oid", oid);
        paramsAll.put("department", org.getId().toString());
        /*if (roleCodes != null){
            hqlAll += " and roleCode in :roleCodes";
            paramsAll.put("roleCodes",roleCodes);
        }*/
        ArrayList<UserDto> usersAll = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hqlAll, paramsAll);
        String hql = null;
        HashMap<String, Object> params = new HashMap<>();
        if (!userIds.isEmpty()) {
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty='1' and userID in :userIds";
            params.put("oid", oid);
            params.put("department", org.getId().toString());
            params.put("userIds", userIds);
            ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!usersAll.isEmpty()) {
                for (UserDto ua : usersAll) {
                    Integer uaId = ua.getUserID();
                    for (UserDto u : users) {
                        Integer ui = u.getUserID();
                        if (ui.equals(uaId)) {
                            ua.setRoleCode("1");
                        }
                    }
                }
                org.setUserList(usersAll);
            }
        } else {
            if (!usersAll.isEmpty()) {
                org.setUserList(usersAll);
            }
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrgWithUserByAuth(organization, oid, userIds);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (usersAll.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }

    @Override
    public List<OrganizationDto> getOrgWithAuthUserTreeByRes(Integer oid, List<Integer> userParentIds, List<Integer> userIds, String type) {
        ArrayList<OrganizationDto> result = new ArrayList<>();
        if (!userParentIds.isEmpty()) {
            String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
            HashMap<String, Object> params = new HashMap<>();
            params.put("oid", oid);
            List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
            for (OrganizationDto org : organizations) {
                OrganizationDto o = setSubOrgWithUserByRes(org, oid, userParentIds, userIds, type);
                if (o != null) {
                    result.add(o);
                }
            }
            OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty='1' and userID in :userIds";
            params.put("userIds", userParentIds);
            ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!users.isEmpty()) {
                if ("1".equals(type)) {
                    for (UserDto up : users) {
                        up.setRoleCode("1");
                    }
                    others.setUserList(users);
                } else {
                    if (!userIds.isEmpty()) {
                        String hqlChild = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty='1' and userID in :userIds";
                        HashMap<String, Object> paramsChild = new HashMap<>();
                        paramsChild.put("oid", oid);
                        paramsChild.put("userIds", userIds);
                        ArrayList<UserDto> usersChild = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hqlChild, paramsChild);
                        if (!usersChild.isEmpty()) {
                            for (UserDto up : users) {
                                Integer upId = up.getUserID();
                                for (UserDto uc : usersChild) {
                                    Integer ucId = uc.getUserID();
                                    if (upId.equals(ucId)) {
                                        up.setRoleCode("1");
                                    }
                                }
                            }
                            others.setUserList(users);
                        } else {
                            others.setUserList(users);
                        }
                    } else {
                        others.setUserList(users);
                    }
                }
                result.add(others);
            }
        }
        return result;
    }

    @Override
    public List<OrganizationDto> getOrgWithAuthUserTreeByResLocking(Integer oid, List<Integer> userParentIds, List<Integer> userIds, String type) {
        ArrayList<OrganizationDto> result = new ArrayList<>();
        if (!userParentIds.isEmpty()) {
            String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
            HashMap<String, Object> params = new HashMap<>();
            params.put("oid", oid);
            List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
            for (OrganizationDto org : organizations) {
                OrganizationDto o = setSubOrgWithUserByResLocking(org, oid, userParentIds, userIds, type);
                if (o != null) {
                    result.add(o);
                }
            }
            OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
            hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty in('1','9','5') and userID in :userIds";
            params.put("userIds", userParentIds);
            ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
            if (!users.isEmpty()) {
                if ("1".equals(type)) {
                    for (UserDto up : users) {
                        up.setRoleCode("1");
                    }
                    others.setUserList(users);
                } else {
                    if (!userIds.isEmpty()) {
                        String hqlChild = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and (department is null or length(trim(department))=0) and isDuty in('1','9','5') and userID in :userIds";
                        HashMap<String, Object> paramsChild = new HashMap<>();
                        paramsChild.put("oid", oid);
                        paramsChild.put("userIds", userIds);
                        ArrayList<UserDto> usersChild = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hqlChild, paramsChild);
                        if (!usersChild.isEmpty()) {
                            for (UserDto up : users) {
                                Integer upId = up.getUserID();
                                for (UserDto uc : usersChild) {
                                    Integer ucId = uc.getUserID();
                                    if (upId.equals(ucId)) {
                                        up.setRoleCode("1");
                                    }
                                }
                            }
                            others.setUserList(users);
                        } else {
                            others.setUserList(users);
                        }
                    } else {
                        others.setUserList(users);
                    }
                }
                result.add(others);
            }
        }
        return result;
    }

    protected OrganizationDto setSubOrgWithUserByResLocking(OrganizationDto org, Integer oid, List<Integer> userParentIds, List<Integer> userIds, String type) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty in('1','9','5') and userID in :userIds";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("department", org.getId().toString());
        params.put("userIds", userParentIds);
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            if ("1".equals(type)) {
                for (UserDto up : users) {
                    up.setRoleCode("1");
                }
                org.setUserList(users);
            } else {
                if (!userIds.isEmpty()) {
                    String hqlChild = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty in('1','9','5') and userID in :userIds";
                    HashMap<String, Object> paramsChild = new HashMap<>();
                    paramsChild.put("oid", oid);
                    paramsChild.put("department", org.getId().toString());
                    paramsChild.put("userIds", userIds);
                    ArrayList<UserDto> usersChild = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hqlChild, paramsChild);
                    if (!usersChild.isEmpty()) {
                        for (UserDto up : users) {
                            Integer upId = up.getUserID();
                            for (UserDto uc : usersChild) {
                                Integer ucId = uc.getUserID();
                                if (upId.equals(ucId)) {
                                    up.setRoleCode("1");
                                }
                            }
                        }
                        org.setUserList(users);
                    } else {
                        org.setUserList(users);
                    }
                } else {
                    org.setUserList(users);
                }
            }
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrgWithUserByResLocking(organization, oid, userParentIds, userIds, type);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (users.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }


    protected OrganizationDto setSubOrgWithUserByRes(OrganizationDto org, Integer oid, List<Integer> userParentIds, List<Integer> userIds, String type) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty='1' and userID in :userIds";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("department", org.getId().toString());
        params.put("userIds", userParentIds);
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            if ("1".equals(type)) {
                for (UserDto up : users) {
                    up.setRoleCode("1");
                }
                org.setUserList(users);
            } else {
                if (!userIds.isEmpty()) {
                    String hqlChild = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty='1' and userID in :userIds";
                    HashMap<String, Object> paramsChild = new HashMap<>();
                    paramsChild.put("oid", oid);
                    paramsChild.put("department", org.getId().toString());
                    paramsChild.put("userIds", userIds);
                    ArrayList<UserDto> usersChild = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hqlChild, paramsChild);
                    if (!usersChild.isEmpty()) {
                        for (UserDto up : users) {
                            Integer upId = up.getUserID();
                            for (UserDto uc : usersChild) {
                                Integer ucId = uc.getUserID();
                                if (upId.equals(ucId)) {
                                    up.setRoleCode("1");
                                }
                            }
                        }
                        org.setUserList(users);
                    } else {
                        org.setUserList(users);
                    }
                } else {
                    org.setUserList(users);
                }
            }
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrgWithUserByRes(organization, oid, userParentIds, userIds, type);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (users.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }

    @Override
    public String getNewSuperId(List<String> superId) {
        String hql = " and o.superId!='33005499' and o.superId!='33005500' and o.superId!='33005501'";
        Map<String, String> orderBy = new HashMap<>();
        orderBy.put("o.superId", "desc");
        List<Organization> organizationList = orgDao.findCollectionByConditionNoPage(hql, null, orderBy);
        if (!organizationList.isEmpty()) {
            return organizationList.get(0).getSuperId();
        } else {
            return "";
        }
    }

    @Override
    public Map getAllOrgByOidAndType(Integer oid, Integer type) {
        String sql = "SELECT\n" +
                "	so.id,so.`name`,so.pid,so.org_type,so.state\n" +
                "FROM\n" +
                "	t_sys_org so\n" +
                "WHERE\n" +
                "	so.pid = ?0\n" +
                "AND so.org_type = ?1";
        return orgDao.findMapByConditionNoPage(sql, new Object[]{oid, type});
    }


    @Override
    public List<Organization> getDepartmentByPidAndName(Integer oid, String name, Integer orgType) {
        String hql = " from Organization o where o.pid = " + oid + " and o.name = '" + name + "' and o.orgType = " + orgType;
        return orgDao.getListByHQL(hql);
    }

    @Override
    public List<OrganizationDto> getOrgAndUserByOid(Integer oid, String code) {
        System.out.println("获取getOrgAndUserByOid开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setOrgAndUserByCode(org, oid, code);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
        params.put("code", code);
        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(u.userID, case when u.userName is not null and length(trim(u.userName))>0 then u.userName else u.realName end,u.mobile) from User u where u.oid = :oid and (u.department is null or length(trim(u.department))=0) and u.isDuty='1' and u.roleCode in('staff','super') and u.userID NOT in (select ur.userID from UserRole ur where ur.role.code=:code)";
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            others.setUserList(users);
            result.add(others);
        }
        System.out.println("获取getOrgAndUserByOid结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }

    @Override
    public List<OrganizationDto> getOrgAndUserByOidLocking(Integer oid, String code) {
        System.out.println("获取getOrgAndUserByOid开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        for (OrganizationDto org : organizations) {
            OrganizationDto o = setOrgAndUserByCodeLocking(org, oid, code);
            if (o != null) {
                result.add(o);
            }
        }
        OrganizationDto others = new OrganizationDto(0, "其他", 0, 0, 0);
        params.put("code", code);
        hql = "select new cn.sphd.miners.modules.system.dto.UserDto(u.userID, case when u.userName is not null and length(trim(u.userName))>0 then u.userName else u.realName end,u.mobile) from User u where u.oid = :oid and (u.department is null or length(trim(u.department))=0) and u.isDuty in('1','9') and u.roleCode in('staff','super') and u.userID NOT in (select ur.userID from UserRole ur where ur.role.code=:code)";
        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            others.setUserList(users);
            result.add(others);
        }
        System.out.println("获取getOrgAndUserByOid结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }

    protected OrganizationDto setOrgAndUserByCodeLocking(OrganizationDto org, Integer oid, String code) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty in('1','9') and roleCode in('staff','super') and userID NOT in (select ur.userID from UserRole ur where ur.role.code=:code)";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("department", org.getId().toString());
        params.put("code", code);

        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            org.setUserList(users);
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setOrgAndUserByCodeLocking(organization, oid, code);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (users.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }


    protected OrganizationDto setOrgAndUserByCode(OrganizationDto org, Integer oid, String code) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(userID, case when userName is not null and length(trim(userName))>0 then userName else realName end,mobile) from User where oid = :oid and department = :department and isDuty='1' and roleCode in('staff','super') and userID NOT in (select ur.userID from UserRole ur where ur.role.code=:code)";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("department", org.getId().toString());
        params.put("code", code);

        ArrayList<UserDto> users = (ArrayList<UserDto>) userDao.getListByHQLWithNamedParams(hql, params);
        if (!users.isEmpty()) {
            org.setUserList(users);
        }
        hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setOrgAndUserByCode(organization, oid, code);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        if (users.isEmpty() && sub.isEmpty()) {
            return null;
        } else {
            return org;
        }
    }

    @Override
    public List<OrganizationDto> getOrgWithDeptTreeByOid(Integer oid) {
        System.out.println("获取getOrgWithDeptTreeByOid开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid order by convert(name, 'gbk') asc";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);

        //添加一个部门，名字为“其他”
        OrganizationDto organization = new OrganizationDto();
        organization.setId(0);
        organization.setName("其他");
        organization.setLevel(1);
        organization.setOrgType(2);

        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        organizations.add(organization);
        for (OrganizationDto org : organizations) {
            if (org.getId() != null && org.getId() != 0) {
                OrganizationDto o = setSubOrg(org, oid, null);
                if (o != null) {
                    result.add(o);
                }
            } else {
                result.add(org);
            }
        }
        System.out.println("获取getOrgWithDeptTreeByOid结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }

    @Override
    public List<OrganizationDto> getOrgWithDeptTreeByOid(Integer oid, Integer enabled) {
        System.out.println("获取getOrgWithDeptTreeByOid开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        ArrayList<OrganizationDto> result = new ArrayList<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        if (enabled != null) {
            if (enabled == 1) {
                hql += " and enabled=true";
            } else {
                hql += " and enabled=false";
            }
        }
        hql += " order by convert(name, 'gbk') asc";

        //添加一个部门，名字为“其他”
        OrganizationDto organization = new OrganizationDto();
        organization.setId(0);
        organization.setName("其他");
        organization.setLevel(1);
        organization.setOrgType(2);

        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        organizations.add(organization);
        for (OrganizationDto org : organizations) {
            if (org.getId() != null && org.getId() != 0) {
                OrganizationDto o = setSubOrg(org, oid, enabled);
                if (o != null) {
                    result.add(o);
                }
            } else {
                result.add(org);
            }
        }
        System.out.println("获取getOrgWithDeptTreeByOid结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        return result;
    }


    protected OrganizationDto setSubOrg(OrganizationDto org, Integer oid, Integer enabled) {
        HashMap<String, Object> params = new HashMap<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and pid = :oid";
        params.clear();
        params.put("oid", org.getId());
        if (enabled != null) {
            if (enabled == 1) {
                hql += " and enabled=true";
            } else {
                hql += " and enabled=false";
            }
        }
        List<OrganizationDto> organizations = orgDao.getListByHQLWithNamedParams(hql, params);
        ArrayList<OrganizationDto> sub = new ArrayList<>();
        for (OrganizationDto organization : organizations) {
            OrganizationDto o = setSubOrg(organization, oid, enabled);
            if (o != null) {
                sub.add(o);
            }
        }
        if (!sub.isEmpty()) {
            org.setSubList(sub);
        }
        return org;
    }

    @Override
    public void confirmOrg(User user, Integer oid, HttpServletRequest request,AuthInfoDto authInfo) {
        List<User> users=userService.getUserListByOrgId(oid);

        for (User u:users){
//            Integer userId=u.getUserID();
            List<String> userInitMids=orgInitService.getUserInitMidsByUserId(u.getUserID());
            for (UserPopedom userPopedom:u.getUserPopedomHashSet()){
                if (!userInitMids.contains(userPopedom.getMid())) {  // 确定启用前 分配过的权限 不删除
                    userPopedomService.deleteUserPopedom(userPopedom);
                }
            }
//            userPopedomService.deleteUserPopedomByUserId(userId); //清空人员初始化前所有权限
            if ("finance".equals(u.getRoleCode())){
                userPopedomService.saveUserPopedomByCode("initialBeforeFinance",u);  //1.307初始化财务 ，需求说财务初始化完成前， 只有一个菜单。  lixu 2024/10/23
            }else if ("accounting".equals(u.getRoleCode())||"agentAccounting".equals(u.getRoleCode())){
                userPopedomService.saveUserPopedomByCode("initialBeforeAccounting",u);  //1.307初始化 会计 ，需求说 会计初始化完成 标志为 财务初始化完成，  完成前只有 初始化-会计模块 lixu 2024/12/24
            }else {
                userPopedomService.saveUserPopedomByCode(u.getRoleCode(),u);
            }

            if("staff".equals(u.getRoleCode())&&"1".equals(u.getIsDuty())){
                if(1!=u.getOrdinaryEmployees()) {  // 非普通员工     2020/11/2 我的团队版  lixu加
                    userPopedomService.saveUserPopedomByCode("seniorStaff", u); //添加非普通员工权限
                }

//                cont = "我公司将使用软件系统进行管理，请点击下载手机端，安装后使用您"+u.getMobile()+"手机号登录，并按页面提示操作。用电脑登录"+System.getProperty("BaseUrl")+"也可进行同样操作。\n" +
//                        "有疑问可给我"+user.getMobile()+"回电话";
            }else if(RoleTmpl.isHighManager(u.getRoleCode())){ //是高管身份
//                cont = "我公司将使用电脑端网址为"+System.getProperty("BaseUrl")+"的管理系统。请使用"+u.getMobile()+"手机号登录系统，之后按页面提示设置权限。有疑问可给我"+user.getMobile()+"回电话。";
            }

            if ("smallSuper".equals(u.getRoleCode())&&"1".equals(u.getIsDuty())){   // 总经理除了 总经理特有权限外 还需添加以下权限
                userPopedomService.saveUserPopedomByCode("staff", u); //添加普通员工权限
                userPopedomService.saveUserPopedomByCode("seniorStaff", u); //添加非普通员工权限
            }

//            String cont = "我公司要使用的Wonderss系统初始化已完成，请点击下载手机端，安装后使用您"+u.getMobile()+"手机号登录，并按页面提示操作。用电脑登录"+System.getProperty("BaseUrl")+"也可进行同样操作。\n" +
//                    "有疑问可拨打我手机"+user.getMobile();
//            sendMessageService.sendMessage(u.getUserID(),oid,u.getMobile(), cont);//发短信
            // 短信重构 2024/5/22 李旭更改
            String phoneNo=u.getMobile();
            String url = GetLocalIPUtils.getRootPath(request);
            Map<String, String> params = new HashMap<String, String>(3) {{
                put("phoneNo", phoneNo);
                put("url", url);
                put("mobile", user.getMobile());
            }};
            smsService.sendMessage(SmsService.SendType.general, phoneNo, authInfo, TemplateService.SmsContentTemplate.addUser, params, null);



//            if (!u.getRoleCode().equals("super")&&!u.getRoleCode().equals("smallSuper")&&!u.getRoleCode().equals("general")) {
//                String cont = "我公司将使用软件系统进行管理，请点击下载手机端，安装后使用您" + u.getMobile() + "手机号登录，并按页面提示操作。用电脑登录"+System.getProperty("BaseUrl")+"也可进行同样操作。\n" +
//                        "有疑问可给我" + user.getMobile() + "回电话。";
//                try {
//                    sendMessageService.sendMessage(u.getUserID(),oid,u.getMobile(), cont);//发短信
//                } catch (UnsupportedEncodingException e) {
//                    System.out.println(e.getMessage());
////                        jsonResult=new JsonResult(new MyException(e.getMessage()));
//                }
//            }
            userService.clearPopedomsCacheByUserId(u.getUserID());
        }

//        List<InitAllot> initAllotList=orgInitService.getInitAllotAllByOid(oid);
//        for (InitAllot initAllot:initAllotList){
//            if (1==initAllot.getState()){
//                orgInitService.initMidsToUser(oid,initAllot);
//            }
//        }

        Organization organization=orgDao.get(oid);
        organization.setInitState("1");
        orgDao.update(organization);
    }
    @Override
    public void clearOrgUserPopedomCache(Integer oid) {
        boolean commitBegin = true;
        Session session = userPopedomDao.getSession();
        Transaction transaction = session.getTransaction();
        if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
            commitBegin = false;
            transaction.begin();
        }
        long time = System.currentTimeMillis();
        Integer start = 0, limit = 50, index = 0;
        Long total = userService.getPopedomsCacheUserCount(null);
        System.out.println("clearOrgUserPopedomCache Total : " + total.toString());
        System.out.println("clearOrgUserPopedomCache limit : " + limit.toString());
        while (start < total) {
            System.out.println("clearOrgUserPopedomCache from : " + start.toString());
            List<User> users = userService.getPopedomsCacheUsers(start, limit, null);
            System.out.println("users.size:" + users.size());
            if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
                transaction.begin();
            }
            for (User u : users) {
                System.out.println("clearPopedomsCacheByUserId" + (++index).toString() + " UserId : " + u.getUserID());
                userService.clearPopedomsCacheByUserId(u.getUserID());
            }
            transaction.commit();
            System.out.println("Transaction.commit");
            start += users.size();
        }
        if (transaction.getStatus().equals(TransactionStatus.ACTIVE)) {
            transaction.commit();
            System.out.println("Transaction.commit1");
        }
        if (commitBegin) {
            transaction.begin();
        }
        System.out.println("UpdateAllBadgeNumber finished : " + start.toString());
        time = System.currentTimeMillis() - time;
        String duration = TimeUtils.toTimeString(time);
        System.out.println("updateAllBadgeNumber limit = " + limit + " Duration : " + duration);
    }


    @Override
    public void insertSpecialOrgData() throws ParseException {
        Organization organization = orgDao.get(0);
        User user = userService.getUserByRoleCode(0, "super");
        //物料分类初始化数据

        MtCategory mc1 = new MtCategory();
        mc1.setName("构成商品的原辅材料");
        mc1.setLevel(1);
        mc1.setOrg(organization);
        mtCategoryDao.save(mc1);


        MtCategory mc2 = new MtCategory();
        mc2.setName("商品");
        mc2.setLevel(1);
        mc2.setOrg(organization);
        mtCategoryDao.save(mc2);


        MtCategory mc3 = new MtCategory();
        mc3.setName("外购成品");
        mc3.setLevel(1);
        mc3.setOrg(organization);
        mtCategoryDao.save(mc3);


        MtCategory mc4 = new MtCategory();
        mc4.setName("商品的包装物");
        mc4.setLevel(1);
        mc4.setOrg(organization);
        mtCategoryDao.save(mc4);

        MtCategory mc5 = new MtCategory();
        mc5.setName("半成品");
        mc5.setLevel(1);
        mc5.setOrg(organization);
        mtCategoryDao.save(mc5);

        MtCategory mc6 = new MtCategory();
        mc6.setName("办公用品");
        mc6.setLevel(1);
        mc6.setOrg(organization);
        mtCategoryDao.save(mc6);

        MtCategory mc7 = new MtCategory();
        mc7.setName("其他原辅材料");
        mc7.setLevel(1);
        mc7.setOrg(organization);
        mtCategoryDao.save(mc7);

        MtCategory zi1 = new MtCategory();
        zi1.setName("待分类");
        zi1.setLevel(2);
        zi1.setOrg(organization);
        zi1.setParent(mc1);
        zi1.setFirstGradeId(mc1.getId());
        mtCategoryDao.save(zi1);

        MtCategory zi2 = new MtCategory();
        zi2.setName("待分类");
        zi2.setLevel(2);
        zi2.setOrg(organization);
        zi2.setParent(mc2);
        zi2.setFirstGradeId(mc2.getId());
        mtCategoryDao.save(zi2);

        MtCategory zi3 = new MtCategory();
        zi3.setName("待分类");
        zi3.setLevel(2);
        zi3.setOrg(organization);
        zi3.setParent(mc3);
        zi3.setFirstGradeId(mc3.getId());
        mtCategoryDao.save(zi3);

        MtCategory zi4 = new MtCategory();
        zi4.setName("待分类");
        zi4.setLevel(2);
        zi4.setOrg(organization);
        zi4.setParent(mc4);
        zi4.setFirstGradeId(mc4.getId());
        mtCategoryDao.save(zi4);

        MtCategory zi5 = new MtCategory();
        zi5.setName("待分类");
        zi5.setLevel(2);
        zi5.setOrg(organization);
        zi5.setParent(mc5);
        zi5.setFirstGradeId(mc5.getId());
        mtCategoryDao.save(zi5);

        MtCategory zi6 = new MtCategory();
        zi6.setName("待分类");
        zi6.setLevel(2);
        zi6.setOrg(organization);
        zi6.setParent(mc6);
        zi6.setFirstGradeId(mc6.getId());
        mtCategoryDao.save(zi6);

        MtCategory zi7 = new MtCategory();
        zi7.setName("待分类");
        zi7.setLevel(2);
        zi7.setOrg(organization);
        zi7.setParent(mc7);
        zi7.setFirstGradeId(mc7.getId());
        mtCategoryDao.save(zi7);

        //报销初始化数据
        CodeCategory codeCategory = new CodeCategory();
        codeCategory.setCode("feeCat");
        codeCategory.setEnabled(1);
        codeCategory.setName("费用类别");
        codeCategory.setOrg(organization);
        codeCategoryDao.save(codeCategory);

        Code fei1 = new Code();
        fei1.setEnabled(1);
        fei1.setName("车务支出");
        fei1.setCategory(codeCategory);
        fei1.setOrders(10);
        codeDao.save(fei1);

        //车务的 默认二级费用类别
        Code fei1Second1 = new Code();
        fei1Second1.setEnabled(1);
        fei1Second1.setName("加油");
        fei1Second1.setCategory(codeCategory);
        fei1Second1.setOrders(10);
        fei1Second1.setParent(fei1);
        codeDao.save(fei1Second1);

        Code fei1Second2 = new Code();
        fei1Second2.setEnabled(1);
        fei1Second2.setName("维修");
        fei1Second2.setCategory(codeCategory);
        fei1Second2.setOrders(20);
        fei1Second2.setParent(fei1);
        codeDao.save(fei1Second2);

        Code fei1Second3 = new Code();
        fei1Second3.setEnabled(1);
        fei1Second3.setName("保养");
        fei1Second3.setCategory(codeCategory);
        fei1Second3.setOrders(30);
        fei1Second3.setParent(fei1);
        codeDao.save(fei1Second3);

        Code fei1Second4 = new Code();
        fei1Second4.setEnabled(1);
        fei1Second4.setName("交汽车保险");
        fei1Second4.setCategory(codeCategory);
        fei1Second4.setOrders(40);
        fei1Second4.setParent(fei1);
        codeDao.save(fei1Second4);

        Code fei1Second5 = new Code();
        fei1Second5.setEnabled(1);
        fei1Second5.setName("其他");
        fei1Second5.setCategory(codeCategory);
        fei1Second5.setOrders(50);
        fei1Second5.setParent(fei1);
        codeDao.save(fei1Second5);

        Code fei2 = new Code();
        fei2.setEnabled(1);
        fei2.setName("交通费");
        fei2.setCategory(codeCategory);
        fei2.setOrders(20);
        codeDao.save(fei2);

        //  交通费 二级费用类别
        Code fei2Second1 = new Code();
        fei2Second1.setEnabled(1);
        fei2Second1.setName("飞机");
        fei2Second1.setCategory(codeCategory);
        fei2Second1.setOrders(10);
        fei2Second1.setParent(fei2);
        codeDao.save(fei2Second1);

        Code fei2Second2 = new Code();
        fei2Second2.setEnabled(1);
        fei2Second2.setName("高铁/动车");
        fei2Second2.setCategory(codeCategory);
        fei2Second2.setOrders(20);
        fei2Second2.setParent(fei2);
        codeDao.save(fei2Second2);

        Code fei2Second3 = new Code();
        fei2Second3.setEnabled(1);
        fei2Second3.setName("火车软卧");
        fei2Second3.setCategory(codeCategory);
        fei2Second3.setOrders(30);
        fei2Second3.setParent(fei2);
        codeDao.save(fei2Second3);

        Code fei2Second4 = new Code();
        fei2Second4.setEnabled(1);
        fei2Second4.setName("火车硬卧/硬座");
        fei2Second4.setCategory(codeCategory);
        fei2Second4.setOrders(40);
        fei2Second4.setParent(fei2);
        codeDao.save(fei2Second4);

        Code fei2Second5 = new Code();
        fei2Second5.setEnabled(1);
        fei2Second5.setName("轮船");
        fei2Second5.setCategory(codeCategory);
        fei2Second5.setOrders(50);
        fei2Second5.setParent(fei2);
        codeDao.save(fei2Second5);

        Code fei2Second6 = new Code();
        fei2Second6.setEnabled(1);
        fei2Second6.setName("长途大巴");
        fei2Second6.setCategory(codeCategory);
        fei2Second6.setOrders(60);
        fei2Second6.setParent(fei2);
        codeDao.save(fei2Second6);

        Code fei2Second7 = new Code();
        fei2Second7.setEnabled(1);
        fei2Second7.setName("地铁/公交");
        fei2Second7.setCategory(codeCategory);
        fei2Second7.setOrders(70);
        fei2Second7.setParent(fei2);
        codeDao.save(fei2Second7);

        Code fei2Second8 = new Code();
        fei2Second8.setEnabled(1);
        fei2Second8.setName("租车");
        fei2Second8.setCategory(codeCategory);
        fei2Second8.setOrders(80);
        fei2Second8.setParent(fei2);
        codeDao.save(fei2Second8);

        Code fei2Second9 = new Code();
        fei2Second9.setEnabled(1);
        fei2Second9.setName("网约车/出租车");
        fei2Second9.setCategory(codeCategory);
        fei2Second9.setOrders(90);
        fei2Second9.setParent(fei2);
        codeDao.save(fei2Second9);

        Code fei2Second10 = new Code();
        fei2Second10.setEnabled(1);
        fei2Second10.setName("过路过桥费");
        fei2Second10.setCategory(codeCategory);
        fei2Second10.setOrders(100);
        fei2Second10.setParent(fei2);
        codeDao.save(fei2Second10);

        Code fei2Second11 = new Code();
        fei2Second11.setEnabled(1);
        fei2Second11.setName("停车费");
        fei2Second11.setCategory(codeCategory);
        fei2Second11.setOrders(110);
        fei2Second11.setParent(fei2);
        codeDao.save(fei2Second11);

        Code fei2Second12 = new Code();
        fei2Second12.setEnabled(1);
        fei2Second12.setName("其他交通费");
        fei2Second12.setCategory(codeCategory);
        fei2Second12.setOrders(120);
        fei2Second12.setParent(fei2);
        codeDao.save(fei2Second12);

        Code fei3 = new Code();
        fei3.setEnabled(1);
        fei3.setName("差旅费");
        fei3.setCategory(codeCategory);
        fei3.setOrders(30);
        codeDao.save(fei3);

        Code fei4 = new Code();
        fei4.setEnabled(1);
        fei4.setName("招待费");
        fei4.setCategory(codeCategory);
        fei4.setOrders(40);
        codeDao.save(fei4);

        Code fei5 = new Code();
        fei5.setEnabled(1);
        fei5.setName("餐饮费");
        fei5.setCategory(codeCategory);
        fei5.setOrders(50);
        codeDao.save(fei5);

        Code fei6 = new Code();
        fei6.setEnabled(1);
        fei6.setName("住宿费");
        fei6.setCategory(codeCategory);
        fei6.setOrders(60);
        codeDao.save(fei6);

        Code fei7 = new Code();
        fei7.setEnabled(1);
        fei7.setName("会议费");
        fei7.setCategory(codeCategory);
        fei7.setOrders(70);
        codeDao.save(fei7);

        Code fei8 = new Code();
        fei8.setEnabled(1);
        fei8.setName("培训费");
        fei8.setCategory(codeCategory);
        fei8.setOrders(80);
        codeDao.save(fei8);

        Code fei9 = new Code();
        fei9.setEnabled(1);
        fei9.setName("电话/手机费");
        fei9.setCategory(codeCategory);
        fei9.setOrders(90);
        codeDao.save(fei9);

        Code fei10 = new Code();
        fei10.setEnabled(1);
        fei10.setName("日常用品");
        fei10.setCategory(codeCategory);
        fei10.setOrders(100);
        codeDao.save(fei10);

        Code fei11 = new Code();
        fei11.setEnabled(1);
        fei11.setName("食品");
        fei11.setCategory(codeCategory);
        fei11.setOrders(110);
        codeDao.save(fei11);

        Code fei12 = new Code();
        fei12.setEnabled(1);
        fei12.setName("办公用品");
        fei12.setCategory(codeCategory);
        fei12.setOrders(120);
        codeDao.save(fei12);

        Code fei13 = new Code();
        fei13.setEnabled(1);
        fei13.setName("房租");
        fei13.setCategory(codeCategory);
        fei13.setOrders(130);
        codeDao.save(fei13);

        Code fei14 = new Code();
        fei14.setEnabled(1);
        fei14.setName("物业费");
        fei14.setCategory(codeCategory);
        fei14.setOrders(140);
        codeDao.save(fei14);

        Code fei15 = new Code();
        fei15.setEnabled(1);
        fei15.setName("其它");
        fei15.setCategory(codeCategory);
        fei15.setOrders(150);
        codeDao.save(fei15);

        CodeCategory codeCategory1 = new CodeCategory();
        codeCategory1.setCode("billCat");
        codeCategory1.setEnabled(1);
        codeCategory1.setName("票据种类");
        codeCategory1.setOrg(organization);
        codeCategoryDao.save(codeCategory1);

        Code piao1 = new Code();
        piao1.setEnabled(1);
        piao1.setName("增值税专用发票");
        piao1.setCategory(codeCategory1);
        piao1.setOrders(60);
        codeDao.save(piao1);

        Code piao2 = new Code();
        piao2.setEnabled(1);
        piao2.setName("增值税普通发票");
        piao2.setCategory(codeCategory1);
        piao2.setOrders(80);
        codeDao.save(piao2);

        Code piao3 = new Code();
        piao3.setEnabled(1);
        piao3.setName("定额发票");
        piao3.setCategory(codeCategory1);
        piao3.setOrders(100);
        codeDao.save(piao3);

        Code piao4 = new Code();
        piao4.setEnabled(1);
        piao4.setName("其他普通发票");
        piao4.setCategory(codeCategory1);
        piao4.setOrders(120);
        codeDao.save(piao4);

        Code piao5 = new Code();
        piao5.setEnabled(1);
        piao5.setName("收据");
        piao5.setCategory(codeCategory1);
        piao5.setOrders(140);
        codeDao.save(piao5);

        //审批项初始化
        ApprovalItem jiaban = new ApprovalItem();
        jiaban.setBelongTo(organization.getId());
        jiaban.setDescription("加班申请");
        jiaban.setName("加班");
        jiaban.setStatus(1);
        jiaban.setLevel(1);
        jiaban.setType("Apply");
        jiaban.setCreateName("系统");
        jiaban.setCreateDate(new Date());
        jiaban.setCode("overTimeApply");
        jiaban.setOpenDate(NewDateUtils.today(new Date()));
        jiaban.setApproveStatus("2");
        jiaban.setAuditDate(new Date());
        jiaban.setOrders(10);
        jiaban.setUpperLimit(new BigDecimal(24));
        approvalItemDao.save(jiaban);

        ApprovalFlow jiabanFlow = new ApprovalFlow();
        jiabanFlow.setToUser("董事长");
        jiabanFlow.setToUserId(user.getUserID());
        jiabanFlow.setType(1);
        jiabanFlow.setItem(jiaban);
        jiabanFlow.setAmountCeiling(24.0);
        jiabanFlow.setLimitQuantity(24.0);
        jiabanFlow.setLevel(1);
        jiabanFlow.setUserName(user.getUserName());
        approvalFlowDao.save(jiabanFlow);

        ApprovalItem jiabanBu = new ApprovalItem();  //补报加班
        jiabanBu.setBelongTo(organization.getId());
        jiabanBu.setDescription("补报加班");
        jiabanBu.setName("补报加班");
        jiabanBu.setStatus(0);
        jiabanBu.setLevel(1);
        jiabanBu.setEnabled(false);
        jiabanBu.setType("Apply");
        jiabanBu.setCreateName("系统");
        jiabanBu.setCreateDate(new Date());
        jiabanBu.setCode("supplementaryOvertime");
        jiabanBu.setOpenDate(NewDateUtils.today(new Date()));
        jiabanBu.setApproveStatus("2");
        jiabanBu.setAuditDate(new Date());
        jiabanBu.setOrders(15);
        jiabanBu.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(jiabanBu);

        ApprovalItem jiabanRule = new ApprovalItem();  //加班时限规划
        jiabanRule.setBelongTo(organization.getId());
        jiabanRule.setDescription("加班时限规划");
        jiabanRule.setName("加班时限规划");
        jiabanRule.setStatus(0);
        jiabanRule.setLevel(1);
        jiabanRule.setType("Apply");
        jiabanRule.setCreateName("系统");
        jiabanRule.setCreateDate(new Date());
        jiabanRule.setCode("overtimeRule");
        jiabanRule.setOpenDate(NewDateUtils.today(new Date()));
        jiabanRule.setApproveStatus("2");
        jiabanRule.setAuditDate(new Date());
        jiabanRule.setOrders(20);
        jiabanRule.setUpperLimit(new BigDecimal(30));
        approvalItemDao.save(jiabanRule);

        ApprovalItem qingjia = new ApprovalItem();
        qingjia.setBelongTo(organization.getId());
        qingjia.setDescription("请假申请");
        qingjia.setName("请假");
        qingjia.setStatus(1);
        qingjia.setLevel(1);
        qingjia.setType("Apply");
        qingjia.setOrders(40);
        qingjia.setCode("leaveApply");
        qingjia.setCreateName("系统");
        qingjia.setCreateDate(new Date());
        qingjia.setOpenDate(NewDateUtils.today(new Date()));
        qingjia.setApproveStatus("2");
        qingjia.setAuditDate(new Date());
        qingjia.setUpperLimit(new BigDecimal(24));
        approvalItemDao.save(qingjia);

        ApprovalFlow qingjiaFlow = new ApprovalFlow();
        qingjiaFlow.setToUser("董事长");
        qingjiaFlow.setToUserId(user.getUserID());
        qingjiaFlow.setType(1);
        qingjiaFlow.setItem(qingjia);
        qingjiaFlow.setAmountCeiling(24.0);
        qingjiaFlow.setLimitQuantity(24.0);
        qingjiaFlow.setLevel(1);
        qingjiaFlow.setUserName(user.getUserName());
        approvalFlowDao.save(qingjiaFlow);

        ApprovalItem qingjiaBu = new ApprovalItem();  //补报请假
        qingjiaBu.setBelongTo(organization.getId());
        qingjiaBu.setDescription("补报请假");
        qingjiaBu.setName("补报请假");
        qingjiaBu.setStatus(0);
        qingjiaBu.setLevel(1);
        qingjiaBu.setEnabled(false);
        qingjiaBu.setType("Apply");
        qingjiaBu.setOrders(45);
        qingjiaBu.setCode("supplementaryLeave");
        qingjiaBu.setCreateName("系统");
        qingjiaBu.setCreateDate(new Date());
        qingjiaBu.setOpenDate(NewDateUtils.today(new Date()));
        qingjiaBu.setApproveStatus("2");
        qingjiaBu.setAuditDate(new Date());
        qingjiaBu.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(qingjiaBu);

        ApprovalItem qingjiaRule = new ApprovalItem();  //请班时限规则
        qingjiaRule.setBelongTo(organization.getId());
        qingjiaRule.setDescription("请班时限规则");
        qingjiaRule.setName("请班时限规则");
        qingjiaRule.setStatus(0);
        qingjiaRule.setLevel(1);
        qingjiaRule.setType("Apply");
        qingjiaRule.setOrders(50);
        qingjiaRule.setCode("leaveRule");
        qingjiaRule.setCreateName("系统");
        qingjiaRule.setCreateDate(new Date());
        qingjiaRule.setOpenDate(NewDateUtils.today(new Date()));
        qingjiaRule.setApproveStatus("2");
        qingjiaRule.setAuditDate(new Date());
        qingjiaRule.setUpperLimit(new BigDecimal(30));
        approvalItemDao.save(qingjiaRule);

        ApprovalItem caiwu = new ApprovalItem();
        caiwu.setBelongTo(organization.getId());
        caiwu.setDescription("报销申请");
        caiwu.setLevel(1);
        caiwu.setName("报销");
        caiwu.setStatus(1);
        caiwu.setType("Apply");
        caiwu.setOrders(70);
        caiwu.setCode("reimburseApply");
        caiwu.setCreateName("系统");
        caiwu.setCreateDate(new Date());
        caiwu.setOpenDate(NewDateUtils.today(new Date()));
        caiwu.setApproveStatus("2");
        caiwu.setAuditDate(new Date());
        caiwu.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(caiwu);

        //报销审批流程初始化
        ApprovalFlow baoxiao = new ApprovalFlow();
        baoxiao.setToUser("董事长");
        baoxiao.setType(1);
        baoxiao.setLevel(1);
        baoxiao.setItem(caiwu);
        baoxiao.setToUserId(user.getUserID());
        baoxiao.setUserName(user.getUserName());
        Integer amc = -1;
        baoxiao.setAmountCeiling(amc.doubleValue());
        baoxiao.setLimitAmount(new BigDecimal(-1));
        approvalFlowDao.save(baoxiao);

        //付款审批
        ApprovalItem paymentApproval = new ApprovalItem();
        paymentApproval.setBelongTo(organization.getId());
        paymentApproval.setDescription("付款审批");
        paymentApproval.setLevel(1);
        paymentApproval.setName("付款");
        paymentApproval.setStatus(1);
        paymentApproval.setType("Approval");
        paymentApproval.setOrders(80);
        paymentApproval.setCode("paymentApproval");
        paymentApproval.setCreateName("系统");
        paymentApproval.setCreateDate(new Date());
        paymentApproval.setOpenDate(NewDateUtils.today(new Date()));
        paymentApproval.setApproveStatus("2");
        paymentApproval.setAuditDate(new Date());
        paymentApproval.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(paymentApproval);

        //付款审批流程初始化
        ApprovalFlow paymentFlow = new ApprovalFlow();
        paymentFlow.setToUser("董事长");
        paymentFlow.setType(1);
        paymentFlow.setLevel(1);
        paymentFlow.setItem(paymentApproval);
        paymentFlow.setLimitAmount(new BigDecimal(-1));
        paymentFlow.setAmountCeiling((double) -1);
        paymentFlow.setToUserId(user.getUserID());
        paymentFlow.setUserName(user.getUserName());
        approvalFlowDao.save(paymentFlow);
        //1.142徐智修改  修改考勤审批 为默认超管审批
        ApprovalItem kaoQin = new ApprovalItem();
        kaoQin.setBelongTo(organization.getId());
        kaoQin.setLevel(1);
        kaoQin.setName("修改考勤");
        kaoQin.setDescription("修改考勤审批");
        kaoQin.setType("Update");
        kaoQin.setStatus(1);
        kaoQin.setOrders(100);
        kaoQin.setCode("workAttendanceApply");
        kaoQin.setCreateName("系统");
        kaoQin.setCreateDate(new Date());
        kaoQin.setOpenDate(NewDateUtils.today(new Date()));
        kaoQin.setApproveStatus("2");
        kaoQin.setAuditDate(new Date());
        kaoQin.setUpperLimit(new BigDecimal(-1));
        approvalItemDao.save(kaoQin);
        //考勤修改流程初始化
        ApprovalFlow kaoQinFlow = new ApprovalFlow();
        kaoQinFlow.setToUser("董事长");
        kaoQinFlow.setType(1);
        kaoQinFlow.setLevel(1);
        kaoQinFlow.setItem(kaoQin);
        kaoQinFlow.setLimitAmount(new BigDecimal(-1));
        kaoQinFlow.setAmountCeiling((double) -1);
        kaoQinFlow.setToUserId(user.getUserID());
        kaoQinFlow.setUserName(user.getUserName());
        approvalFlowDao.save(kaoQinFlow);

        ApprovalItem dangAn = new ApprovalItem();
        dangAn.setName("修改职工档案");
        dangAn.setDescription("更改删除");
        dangAn.setType("Update,Delete");
        dangAn.setStatus(0);
        dangAn.setBelongTo(organization.getId());
        dangAn.setOrders(140);
        dangAn.setCode("archivesApply");
        dangAn.setCreateName("系统");
        dangAn.setCreateDate(new Date());
        approvalItemDao.save(dangAn);

        ApprovalItem gangWei = new ApprovalItem();
        gangWei.setName("修改岗位设置");
        gangWei.setDescription("更改删除");
        gangWei.setType("Update,Delete");
        gangWei.setStatus(0);
        gangWei.setBelongTo(organization.getId());
        gangWei.setOrders(170);
        gangWei.setCode("postApply");
        gangWei.setCreateName("系统");
        gangWei.setCreateDate(new Date());
        approvalItemDao.save(gangWei);

        ApprovalItem approval = new ApprovalItem();
        approval.setName("修改审批设置");
        approval.setDescription("更改");
        approval.setType("Update");
        approval.setStatus(1);
        approval.setLevel(1);
        approval.setBelongTo(organization.getId());
        approval.setOrders(200);
        approval.setCode("itemApply");
        approval.setCreateName("系统");
        approval.setCreateDate(new Date());
        approvalItemDao.save(approval);

        ApprovalFlow approvalflow = new ApprovalFlow();
        approvalflow.setToUser("超管");
        approvalflow.setToUserId(user.getUserID());
        approvalflow.setLevel(1);//第几级
        approvalflow.setType(1);
        approvalflow.setItem(approval);
        approvalflow.setUserName(user.getUserName());
        approvalFlowDao.save(approvalflow);//新增

        //初始化备用金
        FinanceAccount f = new FinanceAccount();
        f.setOrg(organization);
        f.setPreviousBalance(BigDecimal.valueOf(0));
        f.setBalance(BigDecimal.valueOf(0));
        f.setAccountType(1);
        f.setApproveStatus("1");
        f.setCreateDate(new Date());
        f.setCreateName("系统初始化");
        f.setDebit(BigDecimal.valueOf(0));
        f.setCredit(BigDecimal.valueOf(0));
        f.setInitialAmount(BigDecimal.valueOf(0));
        f.setAccountStatus(1);
        f.setCashable(1);//是否可取现  1：可取现  2：不可取现
        f.setName("备用金/现金账户");
        f.setBankName("备用金/现金账户");
        financeAccountDao.save(f);

        //备用金日结
        AccountPeriod ap = new AccountPeriod();
        ap.setFid(f.getId());
        ap.setAccount(String.valueOf(f.getId()));
        ap.setPreviousBalance(BigDecimal.valueOf(0));
        ap.setCredit(BigDecimal.valueOf(0));
        ap.setDebit(BigDecimal.valueOf(0));
        ap.setOrg(organization);
        ap.setBalance(BigDecimal.valueOf(0));
        ap.setBeginDate(new Date());
        ap.setAccountType(1);
        ap.setBuildState("未生成");
        ap.setPeriodType(2);
//        ap.setAccountId(f);
        ap.setAccountId(f.getId());
        ap.setCreateDate(new Date());
        ap.setCreateName("系统生成");
        accountPeroidDao.save(ap);

        //备用金月结
        AccountPeriod a = new AccountPeriod();
        a.setBalance(f.getBalance());
        a.setAccount(f.getId().toString());
        f.setPreviousBalance(new BigDecimal(0));
        a.setCreateDate(new Date());
        a.setCreateName("系统生成");
        a.setPreviousBalance(new BigDecimal(0));
        FinanceUtils.ap(f, null, organization, user, a, accountService, 1);
    }

    private void saveOrganization(Integer pid, Integer orgType, String name, String fullName, boolean enabled, boolean isDefault, Integer userId, String userName, Date enableTime) throws InvocationTargetException, IllegalAccessException {
        Organization department = new Organization();
        department.setPid(pid);
        department.setOrgType(orgType);
        department.setName(name);
        department.setFullName(fullName);
        department.setState(0);
        department.setLevel(1);
        department.setEstablishDate(enableTime);  //成立时间
        department.setEnabled(enabled);  //是否启用
        department.setEnableTime(NewDateUtils.today());  //生效时间
        department.setUserNum(0);
        department.setChildNum(0);
        department.setDisabledChildNum(0);
        department.setAdefault(isDefault);  //是否为销售部
        department.setVersionNo(0);
        department.setOperation("1");
        department.setCreateDate(new Date());
        department.setCreator(userId);
        department.setCreateName(userName);
        this.saveOrg(department);

        User user = new User();
        if (userId != null) {
            user = userDao.get(userId);
        }
        //添加历史信息
        this.getDeptToHistory(department, department.getName(), user, "1", new Date());
    }

    //获取本部门中的职工数   orgId-部门id  isDuty-1表示在职，2表示离职 ,3可登录的代理人员 4-外部浏览者（手机端超管分配）
    private Integer getUserNum(Integer orgId, String isDuty, Integer postID) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select count(userId) from User");
        StringBuffer where = new StringBuffer();
        if (orgId != null) {
            where.append(" and department=:department");
            params.put("department", orgId.toString());
        }
        if (postID != null) {
            where.append(" and postID=:postID");
            params.put("postID", postID.toString());
        }
        if (!MyStrings.nulltoempty(isDuty).isEmpty()) {
            if ("10".equals(isDuty)) {
                where.append(" and isDuty in ('1','9')");
            } else {
                where.append(" and isDuty = :isDuty");
                params.put("isDuty", isDuty);
            }
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        hql.append(" and roleCode in ('staff','super')");
        Long userNum = (Long) userDao.getByHQLWithNamedParams(hql.toString(), params);
        return userNum == null ? 0 : userNum.intValue();
    }

    //获取某部门以及其子部门中的职工总数   orgId-部门id
    private Integer getAllUserNum(Integer orgId) {
        HashMap<String, Object> params = new HashMap<>();
        String hql = "select sum(userNum) from Organization where orgType=2 and enabled=true and orgCode like:pass";
        params.put("pass", "%/" + orgId + "/%");
        Long userNum = (Long) userDao.getByHQLWithNamedParams(hql, params);
        return userNum == null ? 0 : userNum.intValue();
    }

    //获取部门中的部门数   orgId-部门id  isDuty-1表示在职，2表示离职 ,3可登录的代理人员 4-外部浏览者（手机端超管分配）
    private Integer getChildNum(Integer pid, Integer orgType, Integer enabled) {
        Map<String, Object> params = new HashMap<>();
        String hql = "select count(id) from Organization where pid=:pid";
        params.put("pid", pid);
        if (orgType != null) {
            hql += " and orgType=:orgType";
            params.put("orgType", orgType);
        }
        if (enabled != null) {
            if (1 == enabled) {
                hql += " and enabled=true";
            } else if (0 == enabled) {
                hql += " and enabled=false";
            }
        }
        Long childNum = (Long) orgDao.getByHQLWithNamedParams(hql, params);
        return childNum == null ? 0 : childNum.intValue();
    }

    //1.124部门优化项目，新增的这几个数，原先的部门中没有，现在是将没有的更新上
    private void updateNum(Organization organization) {
        Integer userNum = 0;
        if (organization.getOrgType() == 2) {  //部門
            userNum = getUserNum(organization.getId(), "10", null);  //机构中的在职职工
        } else if (organization.getOrgType() == 3) {  //崗位
            userNum = getUserNum(null, "10", organization.getId());  //机构中的在职职工
        }
        organization.setUserNum(userNum);

        Integer organizationsNum = getChildNum(organization.getId(), organization.getOrgType(), 1);  //获取子级部门
        organization.setChildNum(organizationsNum);

        Integer organizations1Num = getChildNum(organization.getId(), organization.getOrgType(), 0);  //停用的子级部门
        organization.setDisabledChildNum(organizations1Num);

        orgDao.update(organization);
    }

    private Organization getOrgByAdefault(Integer pid, Integer adefault) {
        Map<String, Object> params = new HashMap<>();
        String hql = "from Organization where pid=:pid";
        params.put("pid", pid);
        if (adefault != null) {
            if (1 == adefault) {      //是否为销售部
                hql += " and adefault=true";
            } else {
                hql += " and adefault=false";
            }
        }
        return (Organization) orgDao.getByHQLWithNamedParams(hql, params);
    }

    @Override
    public Map<String, Object> getPostDepartments(Integer orgId, Integer orgType, Integer userId) throws InvocationTargetException, IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        User user = userDao.get(userId);
        Organization organization = this.getByOid(orgId); //获取本机构信息
        if (organization == null) {
            organization = this.getOrgByOid(orgId, OrgType.department);
        }
        if (organization.getOrgType() == 1) {  //如果是获取的机构，则要确定是否有销售部
            Organization organization1 = this.getOrgByAdefault(orgId, 1);  //获取机构中的销售部
            if (organization1 == null) {  //没有销售部，现在添加上
                saveOrganization(orgId, 2, "销售部", "销售部", true, true, null, "系统", new Date());
            }
        } else if (2 == organization.getOrgType()) {  //如果是查询部门的，若部门没有历史记录，则新增上
            this.getDeptToHistory(organization, organization.getName(), user, organization.getOperation(), new Date());

            Integer allUserNum = this.getAllUserNum(organization.getId());
            organization.setAllUserNum(allUserNum);  //查询此部门和子部门的职工人数之和
        }
        if (organization.getOrgType() != 1) {
            this.updateNum(organization); //将没有的用户数、子级部门数、停用子级部门数添加上(岗位)

            //将今天生效的修改进行操作，避免重复操作
            postSettleDay();
        }

        List<Organization> organizations = checkDepartmentByPId(organization.getId(), orgType, 1);  //获取第一级部门/或者子级部门(岗位)
        for (Organization o : organizations) {
//            if (o.getUserNum()==null || o.getChildNum()==null || o.getDisabledChildNum()==null) {
            this.updateNum(o); //将没有的用户数、子级部门数、停用子级部门数添加上(岗位)
//            }

            if (o.getOrgType() == 2) {
                Integer allUserNum1 = this.getAllUserNum(o.getId());
                o.setAllUserNum(allUserNum1);  //查询此部门和子部门的职工人数之和
            }
        }

        map.put("organizations", organizations);   //子级部门
        map.put("organization", organization);   //本部门的信息
        return map;
    }
    /**
     * postSettleDay 更新部门修改
     * @apiNote 一天执行一次，超过不执行直接返回，允许重复调用，消除并发
     * <AUTHOR>
     * @since 4.0
     * @return void
     * @date 2025-04-25 13:33:05
     **/
    @Override
    public void postSettleDay() {
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("修改部门任务 start");
            Schedule schedule = scheduleService.getScheduleByCode(methodName);
            long now = System.currentTimeMillis();
            long dayInteval = TimeUnit.DAYS.toMillis(1);
            if(schedule.getRunTime().getTime() + dayInteval <= now) {
                for (long enableTime = NewDateUtils.today(schedule.getRunTime()).getTime() + dayInteval; enableTime < now; enableTime += dayInteval) {
                    updateDepart(new Date(enableTime));
                }
                schedule.setRunTime(NewDateUtils.today(new Date(now)));
            }
            System.out.println("修改部门任务 finish");
            //wyu:释放分布式锁
            dlmService.releaseLock(methodName, lockKey);
        } else { //延时1分钟，加锁执行已完成。
            try {
                Thread.sleep(TimeUnit.MINUTES.toMillis(1));
            } catch (InterruptedException e) {
                logger.warn("Sleep interrupted!", e);
            }
        }
    }

    //仅内部调用，请用postSettleDay代替。
    private void updateDepart(Date enableTime) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from OrganizationHistory where id in (select max(id) from OrganizationHistory where operation='5' and enableTime=:enableTime group by org)";
        map.put("enableTime", enableTime);
        List<OrganizationHistory> organizationHistories = orgHistoryDao.getListByHQLWithNamedParams(hql, map);
        for (OrganizationHistory organizationHistory : organizationHistories) {
            Organization dept = orgDao.get(organizationHistory.getOrg());
            if (dept != null) {
                if (organizationHistory.getName()==null && dept.getName()==null || organizationHistory.getName().equals(dept.getName())) {  //如果已经进行修改后，不再进行一次修改
                    continue;
                }
                dept.setName(organizationHistory.getName());
                dept.setUpdateName(organizationHistory.getCreateName());
                dept.setUpdator(organizationHistory.getCreator());
                dept.setUpdateDate(organizationHistory.getEnableTime());
                dept.setOperation(organizationHistory.getOperation());
                orgDao.update(dept);
                //考勤月结表需要更新部门名称
                workAttendanceOldService.updateDeptName(organizationHistory.getOrg(), organizationHistory.getName());
                //更新用户中的部门名称
                this.updateUserDept(organizationHistory.getOrg(), organizationHistory.getOrg(), organizationHistory.getName());
            }
        }
    }

    @Override
    public Map<String, Object> updateEnabled(Integer deptId, Integer userId, Integer enabled) {
        Map<String, Object> map = new HashMap<>();
        if (deptId != null && enabled != null) {
            Organization organization = orgDao.get(deptId);
            User user = userDao.get(userId);
            if (1 == enabled) {
                organization.setEnabled(true);
            } else {
                organization.setEnabled(false);
            }
            organization.setEnableUser(userId);
            organization.setOperation("6");
            organization.setUpdateDate(new Date());
            organization.setUpdator(userId);
            organization.setUpdateName(user.getUserName());
            orgDao.update(organization);
            map.put("statue", 1);
            map.put("content", "操作成功！");
        } else {
            map.put("statue", 0);
            map.put("content", "操作失败！");
        }
        return map;
    }

    private List<OrganizationHistory> getDeptHistoryByDept(Integer deptId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from OrganizationHistory where org=:org order by enableTime asc,createDate asc";
        map.put("org", deptId);
        List<OrganizationHistory> organizationHistory = orgHistoryDao.getListByHQLWithNamedParams(hql, map);
        return organizationHistory;
    }

    @Override
    public OrganizationHistory getDeptHistoryLast(Integer deptId, Date enableTime) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from OrganizationHistory where org=:org ";
        map.put("org", deptId);
        if (enableTime != null) {
            hql += " and enableTime<=:enableTime";
            map.put("enableTime", enableTime);
        }
        hql += " order by id desc";
        return (OrganizationHistory) orgHistoryDao.getByHQLWithNamedParams(hql, map);
    }

    @Override
    public OrganizationHistory getDeptToHistory(Organization dept, String deptmentName, User user, String operation, Date enableTime) throws InvocationTargetException, IllegalAccessException {
        OrganizationHistory organizationHistory = new OrganizationHistory();
        OrganizationHistory organizationHistoryLast = getDeptHistoryLast(dept.getId(), null);  //获取机构中的最后一条历史记录
        if (organizationHistoryLast == null) {
            organizationHistoryLast = new OrganizationHistory();
//            BeanUtils.copyProperties(organizationHistoryLast,dept);
            organizationHistoryLast.setPid(dept.getPid());
            organizationHistoryLast.setOrgType(dept.getOrgType());
            organizationHistoryLast.setOrg(dept.getId());
            organizationHistoryLast.setName(dept.getName());
            organizationHistoryLast.setFullName(dept.getFullName());
            organizationHistoryLast.setState(dept.getState());
            organizationHistoryLast.setLevel(dept.getLevel());
            organizationHistoryLast.setEstablishDate(dept.getEstablishDate());  //成立时间
            organizationHistoryLast.setEnabled(dept.isEnabled());  //是否启用
            organizationHistoryLast.setEnableTime(dept.getEnableTime());  //生效时间
            organizationHistoryLast.setUserNum(dept.getUserNum());
            organizationHistoryLast.setChildNum(dept.getChildNum());
            organizationHistoryLast.setDisabledChildNum(dept.getDisabledChildNum());
            organizationHistoryLast.setAdefault(dept.getAdefault());  //是否为销售部
            organizationHistoryLast.setOperation(dept.getOperation());
            organizationHistoryLast.setCreateDate(dept.getCreateDate());
            organizationHistoryLast.setCreator(dept.getCreator());
            organizationHistoryLast.setCreateName(dept.getCreateName());
            organizationHistoryLast.setVersionNo(0);
            organizationHistoryLast.setPreviousId(0);
            orgHistoryDao.save(organizationHistoryLast);
        }
        if (!deptmentName.equals(dept.getName())) {   //两个部门名称不同，则可判断是进行修改的部门
//            BeanUtils.copyProperties(organizationHistory, dept);
            BeanUtils.copyPropertiesIgnoreNull(dept, organizationHistory);
            organizationHistory.setOrg(dept.getId());
            organizationHistory.setName(deptmentName);
            organizationHistory.setCreator(user.getUserID());
            organizationHistory.setCreateDate(new Date());
            organizationHistory.setCreateName(user.getUserName());
            organizationHistory.setVersionNo(organizationHistoryLast.getVersionNo() + 1);
            organizationHistory.setPreviousId(organizationHistoryLast.getId());
            organizationHistory.setEnableTime(NewDateUtils.today(enableTime));
            organizationHistory.setOperation(operation);
            organizationHistory.setEstablishDate(NewDateUtils.today(enableTime));
            orgHistoryDao.save(organizationHistory);
        }
        return organizationHistory;
    }

    @Override
    public void updateUserDept(Integer oid, Integer deptId, String departmentName) {
        List<User> users = userService.getUsersByOrg(oid, deptId.toString(), null);
        for (User user : users) {
            user.setDepartName(departmentName);
            userDao.update(user);
        }
    }

    @Override   //type 1-改错字操作 2-部门改名
    public Map<String, Object> updateNameWord(Integer deptId, String departmentName, Integer userId, Date enableTime, Integer type) throws InvocationTargetException, IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        if (deptId != null && !MyStrings.nulltoempty(departmentName).isEmpty()) {
            User user = userDao.get(userId);
            Organization dept = orgDao.get(deptId);
            if (1 == type) {   //改错字
                this.getDeptToHistory(dept, departmentName, user, "4", NewDateUtils.today(new Date()));

                //考勤月结表需要更新部门名称
                workAttendanceOldService.updateDeptName(deptId, departmentName);

                //更新用户中的部门名称
                this.updateUserDept(user.getOid(), deptId, departmentName);

                dept.setName(departmentName);
                dept.setUpdateName(user.getUserName());
                dept.setUpdator(userId);
                dept.setUpdateDate(new Date());
                dept.setOperation("4");
                orgDao.update(dept);
            } else {   //5-部门改名
                this.getDeptToHistory(dept, departmentName, user, "5", NewDateUtils.today(enableTime));
            }

            map.put("status", 1);
            map.put("content", "操作成功");
        } else {
            map.put("status", 0);
            map.put("content", "操作失败");
        }
        return map;
    }

    @Override
    public Map<String, Object> getRecords(Integer deptId) {
        Map<String, Object> map = new HashMap<>();
        List<OrganizationHistory> organizationHistories = this.getDeptHistoryByDept(deptId);  //第一条数据是原来的
        OrganizationHistory organizationHistory = this.getDeptHistoryLast(deptId, new Date());  //当前使用的部门信息
        List<Map<String, Object>> listMap = new ArrayList<>();  //修改记录的列表
        if (organizationHistories.size() > 1) {
            Date openDatePrevious = NewDateUtils.today(organizationHistories.get(0).getEnableTime());  //上一个的启用日期
            Date openDateCurrent = NewDateUtils.today(organizationHistory.getEnableTime());  //当前使用的加班审批流程的启用日期
            for (int i = 0; i < organizationHistories.size(); i++) {
                Map<String, Object> map1 = new HashMap<>();
                String recordState = "原始信息";  //资料状态
                String state = "有效，正在执行";  // 已失效   有效，正在执行    有效，尚未执行
                Date openDate = organizationHistories.get(i).getEnableTime();  //开始执行日期

                if (i == 0 && NewDateUtils.today(organizationHistories.get(0).getEnableTime()).getTime() < openDateCurrent.getTime()) {  //最初的数据启用日期小于当前使用的启用日期(当等于的时候，状态就是“有效，正在执行”，没有大于)
                    state = "已失效";
                } else if (i != 0) {  //除原始信息
                    recordState = "第" + i + "次修改后";
                    //若openDatePrevious和openDate、openDateCurrent这三个时间相同的话，openDateCurrent为最后一个，所以按照下面第一个判断走
                    if (openDatePrevious.getTime() == openDate.getTime()) {  //上一个启用日期和此启用日期相同
                        listMap.get(listMap.size() - 1).put("state", "已失效");  //将上一个的状态改为“已失效”
                        listMap.get(listMap.size() - 1).put("openDate", null);  //将上一个的执行日期改为null
                    }
                    if (openDate.getTime() < openDateCurrent.getTime()) {  //此启用日期小于当前使用的启用日期(若两者相等，则是“已失效”的默认)
                        state = "已失效";
                    }
                    if (openDate.getTime() > openDateCurrent.getTime()) {  //此启用日期大于当前使用的启用日期(若两者相等，则是“有效，正在执行”的默认)
                        state = "有效，尚未执行";
                    }
                }
                map1.put("recordState", recordState);  //资料状态
                map1.put("departmentName", organizationHistories.get(i).getName());  //部门名称a
                map1.put("operation", organizationHistories.get(i).getOperation());  //修改性质 4-改错字 5-部门改名
                map1.put("state", state);  //状态 已失效   有效，正在执行    有效，尚未执行
                map1.put("openDate", openDate);  //正在执行日期
                map1.put("createDate", organizationHistories.get(i).getCreateDate());  //创建或修改时间
                map1.put("createName", organizationHistories.get(i).getCreateName());  //创建人或修改人
                listMap.add(map1);

                openDatePrevious = openDate;  //将当前的启用日期更新为下一个遍历的上一个启用日期
            }

            map.put("listMap", listMap);  //修改记录的列表
            map.put("organizationHistory", organizationHistory);  //当前生效的
            map.put("num", organizationHistory.getVersionNo()); //当前是第几次修改的
        } else {
            map.put("listMap", listMap);  //修改记录的列表
            map.put("organizationHistory", organizationHistory);  //当前生效的
            map.put("num", ""); //当前是第几次修改的
        }
        return map;
    }

    @Override
    public List<Organization> getAllDepartments(Integer oid, Integer enabled) {
        String hql = " from Organization where orgType=2 and orgCode like:pass";
        Map<String, Object> map = new HashMap<>();
        map.put("pass", "/" + oid + "/%");
        if (enabled != null) {
            if (1 == enabled) {
                hql += " and enabled=true";
            } else if (0 == enabled) {
                hql += " and enabled=false";
            }
        }
        List<Organization> organizationList = orgDao.getListByHQLWithNamedParams(hql, map);
        return organizationList;
    }

    @Override
    public List<OrganizationDto> getDeptByUser(Integer userId) {
        Map<String, Object> map = new HashMap<>();
        List<Integer> deptIds = new ArrayList<>();
        if (userId != null) {
            String hql = "select cast(department as integer) from User where isDuty='1' and roleCode='staff' and (userID=:userID or rankUrl like :userIds)";
            map.put("userID", userId);
            map.put("userIds", "%/" + userId + "/%");
            deptIds = userDao.getListByHQLWithNamedParams(hql, map);
        }
        List<OrganizationDto> organizations = new ArrayList<>();
        if (deptIds.size() != 0) {
            String hql = "select new cn.sphd.miners.modules.system.dto.OrganizationDto(id, name, pid, orgType, level) from Organization where orgType = 2 and id in (:deptIds) order by convert(name, 'gbk') asc";
            HashMap<String, Object> params = new HashMap<>();
            params.put("deptIds", deptIds);
            organizations = orgDao.getListByHQLWithNamedParams(hql, params);

            Integer num = 0;
            for (Integer dept : deptIds) {
                if (dept == null && num == 0) { //无部门人员默认为其他部门(其他的部门只添加一个即可)
                    //添加一个部门，名字为“其他”
                    OrganizationDto organization = new OrganizationDto();
                    organization.setId(0);
                    organization.setName("其他");
                    organization.setLevel(1);
                    organization.setOrgType(2);
                    organizations.add(organization);

                    num = 1;
                }
            }
        }
        return organizations;
    }

    @Override
    public Map<String, Object> getReturnTips(Integer oid, Integer deptId, Integer type) {
        Map<String, Object> map = new HashMap<>();
        if (deptId != null && type != null) {
            if (1 == type) {  //1-改错字
                OrganizationHistory organizationHistoryLast = getDeptHistoryLast(deptId, null);  //获取机构中的最后一条历史记录
                if (organizationHistoryLast != null && organizationHistoryLast.getEnableTime().getTime() > new Date().getTime()) {
                    map.put("status", 2);
                    map.put("content", "操作失败！ 因为该部门新名字的生效日期尚未到来。");
                } else {
                    map.put("status", 1);
                    map.put("content", "操作成功");
                }
            } else if (2 == type || 3 == type) {  //2-停用部门   3-删除部门
                Organization organization = orgDao.get(deptId);
                List<Organization> organizations = this.getAllBranchById(deptId);  //获取子级部门
                List<User> users = userService.getUsersByOrg(oid, deptId.toString(), null);
                if (organization.getAdefault()) {  //销售部
                    if (2 == type) {
                        map.put("statue", 3);
                        map.put("content", "对不起，为确保系统功能正常，此部门不可被停用！");
                    } else {
                        map.put("status", 4);
                        map.put("content", "对不起，为确保系统功能正常，此部门不可被删除！");
                    }
                } else if (users.size() > 0) {
                    map.put("status", 5);// 有职工
                    map.put("content", "操作失败！因为系统中该部门下有职工。");
                } else if (organizations.size() > 0) {
                    map.put("status", 6);//有部门
                    map.put("content", "操作失败！因为系统中该部门下有子级部门。");
                } else {
                    map.put("status", 1);
                    map.put("content", "操作成功");
                }
            }
        } else {
            map.put("status", 0);
            map.put("content", "操作失败");
        }
        return map;
    }

    @Override
    public Long getOrgCount() {
        return (Long) orgDao.getByHQLWithNamedParams("select count(*) from Organization where orgType=1", null);
    }

    @Override
    public List<Organization> getOrgLimit(Integer lastId, Integer limit) {
        String hql = "from Organization where orgType=:orgType and id>:id order by id";
        Map<String, Object> params = new HashMap<>(2);
        params.put("orgType", 1);
        params.put("id", lastId);
        return orgDao.getListByHQLWithNamedParamsLimit(hql, params, limit);
    }

    @Override
    public List<Integer> getAllOrgIds() {
        String hql = "select id from Organization where orgType=:orgType";
        Map<String, Object> params = new HashMap<>(1);
        params.put("orgType", 1);
        return orgDao.getListByHQLWithNamedParams(hql, params);
    }

    @Override
    public List<Integer> getOrgSonOrgIds(Integer oid) {
        Organization organization = orgDao.get(oid);
        if (organization.getOrgType() != 1) {
            oid = organization.getPid();
        }
        String hql = "select id from Organization where (pid=:oid and orgType=4) or id=:oid";
        Map<String, Object> map = new HashMap<>();
        map.put("oid", oid);
        List<Integer> oids = orgDao.getListByHQLWithNamedParams(hql, map);
        return oids;
    }

    @Override
    public List<Organization> getOrgSonOrgs(Integer oid) {
        Organization organization = orgDao.get(oid);
        if (organization.getOrgType() != 1) {
            oid = organization.getPid();
        }
        String hql = "from Organization where (pid=:oid and orgType=4) or id=:oid";
        Map<String, Object> map = new HashMap<>();
        map.put("oid", oid);
        List<Organization> organizationList = orgDao.getListByHQLWithNamedParams(hql, map);
        return organizationList;
    }

    @Override
    public Integer getOidByOrgSonOrg(Integer oid, Integer sonOid) throws IOException {
        Organization sonOrganization = orgDao.get(sonOid); // 子机构
        if (sonOrganization.getPid().equals(oid) || oid.equals(sonOid)) { // 子机构的pid 等于当前登录人的机构id
            return sonOid;// 如果子机构id
        } else {
            System.out.println("getOidByOrgSonOrg子机构pid与登录人机构id不一致，怀疑恶意破坏");
            IOException e = new IOException("数据错误");
            throw e;
        }
    }


    @Override
    public Boolean isFatherOrg(Integer oid) {
        Organization organization = orgDao.get(oid);
        if (organization.getOrgType() == 1) {
            return true;
        }
        return false;
    }

    @Override
    public void closeOrg(Integer oid) {
        Organization o = orgDao.get(oid);
        o.setState(2);
        o.setEnabled(false);
        o.setUpdateDate(new Date());
        orgDao.update(o);
        List<User> userList = userService.getUserListByOrgId(oid);
        for (User u : userList) {
            u.setDefault(false); //注销 下所有员工的 去掉该机构的默认登录
            u.setIsDuty("2");
            userService.updateUser(u);
        }

    }

    @Override
    public JsonResult checkLIVEOrg(String phone) {

        AuthAcc authAcc = authService.getEnabledByMobile(phone);
        if (authAcc != null) {
            List<User> superUsers = userService.getSuperUsersByAccId(authAcc.getId()); // 该账户拥有的 超管列表
            for (User u : superUsers) {
                if (u.getOrganization().getCode() != null && u.getOrganization().getCode().equals("liveHelper") && 0 == u.getOrganization().getState()) {  // 作为超管的机构 已存在主播机构
                    return new JsonResult(new MyException("-2", "工作室已存在"));
                }
            }
        }
        return new JsonResult(1, "可以创建");//登录成功

    }


    @Override
    public Organization getOrgByName(String name) {
        String hql="from Organization where name=:name";
        Map<String,Object> map=new HashMap<>();
        map.put("name",name);
        Organization organization= (Organization) orgDao.getByHQLWithNamedParams(hql,map);
        return organization;
    }

    @Override
    public List<Organization> getDepartmentsByName(Integer oid, String name) {
        String hql = "from Organization where orgType=:orgType and orgCode like :pass and name like :name";
        Map<String, Object> params = new HashMap<String, Object>(1){{
            put("orgType", 2);
            put("orgCode", "/" + oid + "/%");
            put("name", "%" + name + "%");
        }};
        return orgDao.getListByHQLWithNamedParams(hql, params);
    }

    @Override
    public Set<String> getOrgNames(List<Integer> noIds) {
        String hql="select name from Organization where orgType in(1,4) and id not in(:noIds)  ORDER BY RAND()";
        Map<String,Object> map=new HashMap<>();
        map.put("noIds",noIds);
        List<String> names= orgDao.getListByHQLWithNamedParamsLimit(hql,map,19);
        return new HashSet<String>(names);
    }

    @Override
    public Integer getOidBycreateDate(Date createDate) {
        String hql="from Organization where createDate=:createDate";
        Map<String,Object> map=new HashMap<>();
        map.put("createDate",createDate);
        Organization organization= (Organization) orgDao.getByHQLWithNamedParams(hql,map);
        return organization.getId();
    }
}