package cn.sphd.miners.modules.material.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 供应商管理_供应商导入信息
20231108 1.267供应商之批量导入对象 t_srm_supplier_import
 * 
 * <AUTHOR>
 * @date 2023-12-11
 */
public class TSrmSupplierImport
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 机构ID */
    private Long org;

    /** 导入ID */
    private Long imports;

    /** 状态:1-导入临时表,2-录入信息,3-完成 */
    private Long state;

    /** 名称 */
    private String name;

    /** 全称 */
    private String fullName;

    /** 代号 */
    private String codeName;

    /** 是否接受挂帐 */
    private Integer chargeAcceptable;

    /** 是否需要预付款:0-不确定,1-需要,2-不需要 */
    private String isImprest;

    /** 备注 */
    private String memo;

    /** 创建人id */
    private Long creator;

    /** 创建人 */
    private String createName;

    /** 修改人id */
    private Long updator;

    /** 修改人 */
    private String updateName;

    /** 操作:1-增,2-删,3-修改 */
    private String operation;

    /** 修改前记录ID */
    private Long previousId;

    /** 版本号,每次修改+1 */
    private Long versionNo;

    private Date createTime;

    private Date updateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setImport(Long imports)
    {
        this.imports = imports;
    }

    public Long getImport() 
    {
        return imports;
    }
    public void setState(Long state) 
    {
        this.state = state;
    }

    public Long getState() 
    {
        return state;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setFullName(String fullName) 
    {
        this.fullName = fullName;
    }

    public String getFullName() 
    {
        return fullName;
    }
    public void setCodeName(String codeName) 
    {
        this.codeName = codeName;
    }

    public String getCodeName() 
    {
        return codeName;
    }
    public void setChargeAcceptable(Integer chargeAcceptable) 
    {
        this.chargeAcceptable = chargeAcceptable;
    }

    public Integer getChargeAcceptable() 
    {
        return chargeAcceptable;
    }
    public void setIsImprest(String isImprest) 
    {
        this.isImprest = isImprest;
    }

    public String getIsImprest() 
    {
        return isImprest;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setOperation(String operation) 
    {
        this.operation = operation;
    }

    public String getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    public Long getImports() {
        return imports;
    }

    public void setImports(Long imports) {
        this.imports = imports;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
