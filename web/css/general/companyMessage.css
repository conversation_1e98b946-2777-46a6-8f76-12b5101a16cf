.mar{ margin-top:40px; }
.mar>span:nth-child(1){display: inline-block; width: 80px; text-align: right; margin-right: 15px;  }
.con-txt{margin-left: 12px;padding-left: 10px;}
.thin-btn{color: #0070c0;margin-right: 20px;font-weight: bold;}
.thin-btn:last-child{margin-right: 0;}
.com_address span{display: inline-block;line-height: 30px;vertical-align: middle;}
.com_address span.con-txt{min-width: 200px;height: 30px;background: #cbe7ce;}
.item_head,.item_content{display: inline-block;}
.item_head{width: 64px;text-align: right;margin-right: 8px;}
.item .item_content{position: relative; margin-top: 14px; margin-bottom: 4px;line-height:32px;}
.item .item_content input{width: 300px;}
.modInfo [disabled]{ background: #d1d1d1;cursor: not-allowed;}
.modInfo .ty-inputText,.modInfo .ty-inputSelect, .modItem-s input{width: 100%;}
.modItemTtl{  padding-left:22px;}
.modItem-l,.modItem-m{position: relative;float:left;margin-top: 12px;}
.modItem-m:nth-child(odd){margin-right: 70px;}
.modItem-l {width: 586px;}
.modItem-m {width: 400px;}
.modItem-s {width: 120px;position: relative;}
.modItem-ss {margin-right: 20px;width: 80px;}
.modInfo{padding-bottom: 20px;margin-bottom: 20px;}
.companyDetails{margin-left: 80px;width: 50%;}
/*.companyDetails h3{margin-bottom: 40px;}*/
.companyDetails .ty-alert{justify-content: space-between;color: #101010;}
.companyDetails .panel-box:nth-of-type(n+2) {border-top: 1px solid #c7c7c7;padding-top: 8px;margin: 20px 0;}
.clearInputVal,.clearVal {position: absolute;  bottom: 0;  right: 4px; width: 10px;
    cursor: pointer; color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
input:focus + i.clearInputVal,input:focus + i.clearVal{  display: block;  }
.modItem-l .clearInputVal,.modItem-m .clearInputVal{top: 30px;}
.changeNameContainer .clearVal{top: 0;}
.bonceCon i{font-style: normal;}
.narrowBody{margin: auto;width: 80%;}
.siteBtnGroup{margin-bottom: 30px;}
.siteInfoCare{margin-top: 30px;}
.unit-btn {margin-left: -2px;display: inline-block;margin-bottom: 0;font-weight: 400;text-align: center;touch-action: manipulation;cursor: pointer;
    white-space: nowrap;padding: 4px 6px;font-size: 14px;line-height: 1.42857;border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;
    color: #333;background-color: #fff;border: 1px solid #dcdfe6;border-left-color: transparent;}
.input-group .ty-inputText{min-width: 100px;width: 128px;}
.modPics{position: relative;}
.modPics .clearPicsBtn{position: absolute;  top: 0;  right: 4px;width: 30px;  color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
.filePic{display: inline-block;min-width:10px;color: #000001;}
.file-box,.pay-box {min-height: 30px; padding: 4px 8px;border: 1px solid #dcdfe6;background: #fff;}
.pay-box{background: #bebebe;}
.inTip{color:#d1d1d1;}
.modInput{padding: 20px 0 0 70px;width: 950px;}
.modItem-flex{ display: flex;justify-content: space-between;}
.livingCost{padding-left: 70px;}
.fill{margin-bottom: 30px;}
.costs{width: 436px;}
.costs:nth-child(odd){margin-right: 38px;}
.careful{color: #0070bf;font-size: 12px;}
.doc-box{width: 182px;}
.file-box .fa-times {color: #0070c0;font-size: 12px;}
.imgsthumb {float: left;width: 40px;}
.fitSize input{width: 156px;}
.low-line{border-bottom: 1px solid #bfbfbf;}
.moreLine{margin-bottom: 20px;width: 582px;display: flex;justify-content: space-between;}
.tb-align-left td{text-align: left;}
#siteTaxList, #hydroGas,#siteTaxList_log, #hydroGas_log{margin-top: 30px;}
.comPics a{padding-right: 4px;margin-right: 6px;display: inline-block; cursor: pointer; color: #0070c0;}
.editPay{float: right;color: #0070bf;}
.limitEllipsis{display: inline-block;width: 70%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.gap{margin-bottom: 20px;}
.categoryGroup{clear: both;}
.categoryItem {position: relative;}
.categoryGroup .delRow:first-child{display: none;}
.categoryItem .delRow{position: absolute;bottom: 4px;right: -80px;width: 80px;font-weight: bold;}
.categoryItem .ctrl {line-height: 30px;}
#otherPay tr input{width: 120px;}
#otherPay tr td:last-child{border-color: rgba(238, 238, 238, 0);}
#otherPay tr td:last-child{background-color: rgba(238, 238, 238, 0);}
.funBtn{width: 160px;height: 32px;line-height: 32px;text-align: center;border-radius: 3px;cursor: pointer;display: inline-block;
    border: none;}