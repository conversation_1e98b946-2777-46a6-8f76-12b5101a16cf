<template>
  <div id="purchaseInvoiceDetails3">
    <TY_NavTop :title="ttl" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading" >
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <!--采购部门的票据审核-->
      <div v-if="pund === 0">
        <div class="con1">
          {{ details.poPaymentApplication.type | formatType(details.poPaymentApplication.amount) }}
          <div v-if="details.poPaymentApplication.type === '3'">本次付款申请没有附带的票据</div>
          <p>申请人：{{ details.poPaymentApplication.createName }}{{ details.poPaymentApplication.createDate | formatDay('YYYY-MM-DD HH:mm:ss') }}</p>
        </div>
        <div class="panel-content">
          <div>
            <table style="width:100%;">
              <tr>
                <td>供应商：{{ details.fullName  }}</td>
                <td>{{ details.codeName }}</td>
              </tr>
              <tr>
                <td>订单总额：{{ details.poOrders.amount && details.poOrders.amount.toFixed(2) }}元</td>
                <td>订单号：{{ details.poOrders.sn }} </td>
              </tr>
              <tr>
                <td>订单日期：{{ details.poOrders.createDate | formatDay('YYYY-MM-DD') }}</td>
                <td></td>
              </tr>
            </table>
          </div>
          <div class="panel-content">
            <div class="text-right linkCon">
              <el-link style="color: #409eff; margin-right: 20px;" @click="orderProgressRade = !orderProgressRade">查看订单进度
                <i :class="!orderProgressRade?'el-icon-arrow-down':'el-icon-arrow-up'"></i></el-link>
              <el-link v-on:click="toggle = -toggle">审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link>
            </div>
            <div v-show="toggle > 0">
              <div class="item-content">
                <div class="processItem">
                  <div>提交报销申请</div>
                  <div>申请人</div>
                  <div>{{details.poPaymentApplication.createName | stringSplit(4)}} {{ details.poPaymentApplication.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-for="it in details.approvalProcessList" v-bind:key="it.id">

                  <div v-if="it.approveStatus === '2' && it.businessType == 45" class="processItem">
                    <div>在线审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 46" class="processItem">
                    <div>在线审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 47" class="processItem">
                    <div>线下审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 48" class="processItem">
                    <div>采购审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 49" class="processItem">
                    <div>付款审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 50" class="processItem">
                    <div>付款方式确认</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 51" class="processItem">
                    <div>付款复核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 53" class="processItem">
                    <div>付款方式修改</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '5'" class="processItem">
                    <div>
                      <span v-if="financePayment.businessType == '1'">现金付讫</span>
                      <span v-if="financePayment.businessType == '2'">报销款已转支票</span>
                      <span v-if="financePayment.businessType == '3'">报销款已转支票</span>
                      <span v-if="financePayment.businessType == '4'">报销款已转承兑汇票</span>
                      <span v-if="financePayment.businessType == '5'">报销款已转账</span>
                    </div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <!-- 最后一条审批记录展示 -->
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===46">
                  <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                  <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ nowApproveItem.reason }}</div></div>
                  <br>
                  <div class="processItem">
                    <div></div>
                    <div>审批人</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 45">
                  <div style="margin-top:15px;">报销申请被驳回！</div>
                  <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                  <div class="processItem">
                    <div></div>
                    <div>审批人</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 47">
                  <div style="margin-top:15px;">  线下审核未通过！</div>
                  <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                  <div class="processItem">
                    <div></div>
                    <div>出纳员</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-blue">

                <span v-if="nowApproveItem.approveStatus === '1' && isNowApprover && nowApproveItem.businessType===46">
                请等待报销者提交实际票据。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                  <!--<span v-else-if="nowApproveItem.approveStatus === '1'&& nowApproveItem.toUser>0 && !isNowApprover ">{{ nowApproveItem.userName }}为下一个审批人</span>-->
                  <span v-else-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="orderProgressRade">
          <el-table :data="details.orderProgress" stripe style="width: 100%">
            <el-table-column prop="name" label="阶段"></el-table-column>
            <el-table-column prop="amount" label="金额"></el-table-column>
            <el-table-column prop="rate" label="比例"></el-table-column>
          </el-table>
        </div>
        <div v-if="[31,32,33 ].indexOf(code) > -1">
          <div class="panel-title">
            本次报销共有票据 {{ details.poPaymentApplication.billCount }} 张，合计 {{ details.poPaymentApplication.billAmount && details.poPaymentApplication.billAmount.toFixed(2)  }} 元
          </div>
          <div class="panel-content">
            <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
              <el-tab-pane label="按票据查看" name="bill">
                <div class="ui-cells_none">
                  <a class="ui-cell" @click="goBillInfo(item)" v-for="item in details.listMapBillCat" v-bind:key="item.billCat">
                    <div class="ui-cell__bd">
                      <div class="ui-cell_con">
                        <el-row>
                          <el-col :span="12">{{item.invoiceCategory | formatCat}}</el-col>
                          <el-col :span="6">{{item.num}}张</el-col>
                          <el-col :span="6">{{item.totalBillAmount && item.totalBillAmount.toFixed(2) }} 元</el-col>
                        </el-row>
                      </div>
                      <div class="ui-cell__ft"></div>
                    </div>
                  </a>
                </div>
              </el-tab-pane>
              <el-tab-pane label="按费用查看" name="feeCat">
                <a class="ui-cell" @click="goFeeInfo(item)"  v-for="item in details.listMapFeeCat" v-bind:key="item.feeCat">
                  <div class="ui-cell__bd">
                    <div class="ui-cell_con">
                      <el-row>
                        <el-col :span="12">{{item.feeCategory}}</el-col>
                        <el-col :span="6">{{item.totalBillAmount && item.totalBillAmount.toFixed(2) }} 元</el-col>
                      </el-row>
                    </div>
                  </div>
                  <div class="ui-cell__ft"></div>
                </a>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div>
            <!--财务审批  -->
            <!-- 在线审核 -->
            <a class="ui-cell" v-if="code === 31">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>
                    <p>请根据在线审核的结果进行选择：</p>
                    <p style="text-align: right;"><span class="linkBtn" @click="jumpExplain">票据审核说明</span></p>
                    <div>
                      <el-radio-group v-model="approveSta" size="small">
                        <el-radio label="2">在线审批通过，准予进入后续审批。</el-radio><br/>
                        <el-radio label="3">在线审批未通过。</el-radio><br/>
                      </el-radio-group>
                    </div>
                    <div v-if="approveSta === '3'">
                      <p>请确认在线审批无法通过的原因（可多选）</p>
                      <el-checkbox v-model="approveSelect" label="1">照片不清楚，或信息被遮挡，以至于无法审核</el-checkbox>
                      <el-checkbox v-model="approveSelect" label="2">无法入会计帐的票据较多</el-checkbox>
                      <el-checkbox v-model="approveSelect" label="3">包含公司不允许报销的票据</el-checkbox>
                      <el-checkbox v-model="approveSelect" label="4">票据内容与所录入的信息不一致</el-checkbox>
                      <br>
                      <span v-show="approveSelect.indexOf('5')>-1" style="float: right;margin-right:100px; " >{{ reason.length }}/40</span>
                      <el-checkbox v-model="approveSelect" label="5">其他原因</el-checkbox>
                      <el-input style="width:95%; " v-show="approveSelect.indexOf('5')>-1" type="textarea" :rows="2" v-model="reason"
                                placeholder="请在此录入在线审批无法通过的原因，最多可录入40个字。" v-on:keyup.native="strSub" @change="strSub"></el-input>
                    </div>
                  </div>
                  <div class="handle_button" style="text-align: center; margin:0 30px; ">
                    <button class="ui-btn ui-btn_info" style="width:300px; " @click="approve2('onlineAuditApproval')">确 定</button>
                  </div>
                </div>
              </div>
            </a>
            <!-- 待线下审核 -->
            <a class="ui-cell" v-if="code === 33">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div style="padding-right:15px; ">
                    <p>报销人给您的实际票据，与您已在系统中在线审批过的票据图片是否一致？请确认后选择：</p>
                    <div>
                      <el-radio-group v-model="approveSta" size="small">
                        <el-radio label="2">一致。线下审核通过。</el-radio><br/>
                        <el-radio label="3">不一致，驳回报销申请。</el-radio><br/>
                      </el-radio-group>
                    </div>
                  </div>
                  <div class="handle_button" style="text-align: center; margin:0 30px; ">
                    <button class="ui-btn ui-btn_info" style="width:300px; " @click="approve2('offlineAuditApproval')">确 定</button>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
        <div v-if="[22,23,24,25,34].indexOf(code) > -1">
          <div style="background: #fff; padding:10px 15px;">
            <span>付款事由：</span>
            <span>{{ details.poPaymentApplication.applicationReason | formReason(details.poPaymentApplication.applicationDesc)}}</span>
            <hr>
          </div>
        </div>
      </div>
      <!--多收来的款-->
      <div v-if="pund === 3">
        <div class="con1">
          <div>
            <span class="ttl1">原始的付款方</span><span> {{detailsInfo.supplierName }} </span>
          </div>
          <div>
            <span><span class="ttl1">多收来的金额 </span><span>  {{detailsInfo.amount && detailsInfo.amount.toFixed(2)}} 元 </span></span>
          </div>
          <p class="txt_right" v-if="detailsote.createName">
            申请人：{{ detailsInfo.createName  }} {{detailsInfo.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}
          </p>
        </div>
        <div class="panel-content" style="margin-bottom: 12px;">
          <div class="panel-content">
            <div class="text-right linkCon">
              <el-link v-on:click="toggle = -toggle">审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link>
            </div>
            <div v-show="toggle > 0">
              <div class="item-content">
                <div class="processItem" v-if="detailsInfo.createName">
                  <div>提交报销申请</div>
                  <div>申请人</div>
                  <div>{{detailsInfo.createName | stringSplit(4)}} {{ detailsInfo.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-for="it in details.approvalProcessList" v-bind:key="it.id">
                  <div v-if="it.approveStatus === '2' && it.businessType == 73" class="processItem">
                    <div>采购审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 73" class="processItem">
                    <div>付款审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 74" class="processItem">
                    <div>付款方式确认</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 75" class="processItem">
                    <div>付款复核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 76" class="processItem">
                    <div v-if="details.financePayment.method === '1'">现金付讫</div>
                    <div v-if="details.financePayment.method === '3'">付款已转支票</div>
                    <div v-if="details.financePayment.method === '4'">付款已转承兑汇票</div>
                    <div v-if="details.financePayment.method === '5'">付款已转账</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 77" class="processItem">
                    <div>付款方式修改</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>

                </div>
                <!-- 最后一条审批记录展示 -->
                <div class="color-blue">
                  <span v-if="nowApproveItem.approveStatus === '2' && nowApproveItem.businessType === 72">本次报销已完结！</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-content">
          <div style="background: #fff; padding:10px 15px;">
            <span>付款事由：</span><span style="color:red">多收来的款</span>
          </div>
        </div>
      </div>
      <!--采购部门的预付款-->
      <div v-if="pund !== 3 && pund !== 0">
        <div class="con1">
          <div class="oInfo" v-if="detailsote.poOrdersPrepayment">  <!--左侧v-if=""用于进行数组的校验-->
            <p style="margin-left: -21px;">
              计划付款金额&nbsp;{{detailsote.poOrdersPrepayment.planAmount && detailsote.poOrdersPrepayment.planAmount.toFixed(2)}}&nbsp;元
            </p>
            <p style="display: flex">
              <span style="margin-left: -21px;">
                计划付款时间&nbsp;{{detailsote.poOrdersPrepayment.planDate |formatDay('YYYY-MM-DD')}}
              </span>
              <span style="margin-left: 16px;">计划付款方式&nbsp;{{isture}}</span>
            </p>
          </div>
          <p class="txt_right" v-if="detailsote.poOrders">
            申请人：{{ detailsote.poOrders.createName  }} {{detailsote.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}
          </p>
        </div>
        <div class="panel-content" style="margin-bottom: 12px;">
          <div>
            <table style="width:100%;"> <!--左侧同理进行数组的校验-->
              <tr>
                <td>供应商：{{ detailsote.srmSupplier.fullName  }}</td>
                <td>{{ detailsote.srmSupplier.codeName }}</td>
              </tr>
              <tr>
                <td>订单总额：{{ detailsote.poOrders.amount && detailsote.poOrders.amount.toFixed(2) }}元</td>
                <td>订单号：{{ detailsote.poOrders.sn }} </td>
              </tr>
              <tr>
                <td>订单日期：{{ detailsote.poOrders.createDate | formatDay('YYYY-MM-DD') }}</td>
                <td></td>
              </tr>
            </table>
          </div>
          <div class="panel-content">
            <div class="text-right linkCon">
              <el-link style="color: #409eff; margin-right: 20px;" @click="orderProgressRade1()">订单查看
              </el-link>
              <el-link v-on:click="toggle = -toggle">审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link>
            </div>
            <div v-show="toggle > 0">
              <div class="item-content">
                <div class="processItem" v-if="detailsote.poOrders">
                  <div>提交报销申请</div>
                  <div>申请人</div>
                  <div>{{detailsote.poOrders.createName | stringSplit(4)}} {{ detailsote.poOrders.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-for="it in detailsote.approvalProcessList" v-bind:key="it.id">
                  <div v-if="it.approveStatus === '2' && it.businessType == 23" class="processItem">
                    <div>采购审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 63" class="processItem">
                    <div>付款审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 64" class="processItem">
                    <div>付款方式确认</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 65" class="processItem">
                    <div>付款复核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 66" class="processItem">
                    <div v-if="detailsote.financePayment.method === '1'">现金付讫</div>
                    <div v-if="detailsote.financePayment.method === '3'">报销款已转支票</div>
                    <div v-if="detailsote.financePayment.method === '4'">报销款已转承兑汇票</div>
                    <div v-if="detailsote.financePayment.method === '5'">报销款已转账</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 67" class="processItem">
                    <div>付款方式修改</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>

                </div>
                <!-- 最后一条审批记录展示 -->
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType ===46">
                  <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                  <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ nowApproveItem.reason }}</div></div>
                  <br>
                  <div class="processItem">
                    <div></div>
                    <div>审批人</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 45">
                  <div style="margin-top:15px;">报销申请被驳回！</div>
                  <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                  <div class="processItem">
                    <div></div>
                    <div>审批人</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 47">
                  <div style="margin-top:15px;">  线下审核未通过！</div>
                  <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                  <div class="processItem">
                    <div></div>
                    <div>出纳员</div>
                    <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-blue">
                  <span v-if="nowApproveItem.approveStatus === '1' && isNowApprover && nowApproveItem.businessType===46">
                    请等待报销者提交实际票据。<br>
                    注意：实际票据需与在线审核通过的票据一致！</span>
<!--                    <span v-else-if="nowApproveItem.approveStatus === '1'&& nowApproveItem.toUser !== 0 && nowApproveItem.toUser !== null ">{{ nowApproveItem.userName }}为下一个审批人</span>-->
                  <span v-else-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-content">
          <div style="background: #fff; padding:10px 15px;">
            <span>付款事由：</span><span style="color:red">采购的预付款</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
.linkCon .el-link{
  font-size: 12px;
}
.linkBtn{ color:#5d9cec; cursor:default; margin-right:20px; }
.linkBtn:hover{ text-decoration:underline;  }
.processItem{
  display: flex;
  font-size: 11px;
  margin-left:-5px;
  div:nth-child(1){ text-align: left; flex: 1 }
  div:nth-child(2){ text-align: center; flex: 1 }
  div:nth-child(3) { text-align: right; flex: 2 }
}
.rejectPng{
  position: absolute;
  top: calc( 50% - 60px);
  left: calc( 50% - 60px);
  z-index: 50;
  filter:alpha(opacity=60);
  opacity:0.6;
  -moz-opacity:0.6;
  -khtml-opacity: 0.6
}
.con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
.con1>p{text-align: right; font-size:0.8em;   }
.oInfo{ padding:5px 20px; }
.txt_right{ text-align: right;  }
.right{ float: right;  }
.ui-cell{ display: block;  }
</style>

<script>
// import { isSaler, chargeNull } from '../../../js/common'
import rejectPng from '../../../images/reject.png'
export default {
  name: 'purchaseInvoiceDetails3',
  data () {
    return {
      ttl: '采购部门的票据审核',
      isReject: false, // 判断是否驳回来显示驳回图片
      isRevoke: false, // 判断是否显示撤销
      isNowApprover: false,
      rejectPng: rejectPng, // 驳回图片
      dialogVisible: false,
      loading: true,
      toggle: -1, // 审批记录控制显隐
      listenersUid: [],
      approveSelect: [],
      approveSta: '',
      approveStatus: '0',
      // approveStatus: '',
      nowApproveItem: {},
      reason: '',
      invoiceID: '',
      activeName: 'bill',
      orderProgressRade: false,
      code: 0,
      pund: 0,
      detailsInfo: {},
      detailsote: {
        srmSupplier: {},
        poOrdersPrepayment: {},
        poOrders: {},
        approvalProcessList: {}
      },
      details: {
        listMapBillCat: [],
        financeReturn: {},
        financePayment: '',
        fullName: '',
        financeChequeDetail: {},
        financeAccount: {},
        poPaymentApplication: {},
        listMapFeeCat: [],
        codeName: '',
        invoiceType: '',
        approvalProcessList: [],
        poOrders: {},
        status: '',
        loanBiz: {},
        financePaymentHistory: {},
        financeReturnHistory: {}
      }

    }
  },
  filters: {
    formReason: function (num, applicationDesc) {
      switch (Number(num)) {
        case 1:
          return '符合合同约定'
        case 2:
          return applicationDesc
      }
    },
    formatCat: function (num) {
      switch (Number(num)) {
        case 1:
          return '增值税专用发票'
        case 2:
          return '增值税普通发票'
        case 3:
          return '收据'
        case 4:
          return '其他发票'
      }
    },
    formatType: function (num, amount) {
      switch (Number(num)) {
        case 1:
          return '本次仅提交票据，不付款'
        case 2:
          return `申请付款：${amount.toFixed(2)}元`
        case 3:
          return `申请付款：${amount.toFixed(2)}元`
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    let parampun = Number(this.$route.params.pun)
    that.code = Number(paramCode.substring(0, 2)) // 31-待在线审核; 32-在线审核ok; 33-待线下审核; 34 - 付款的查看详情
    that.busyid = this.$route.params.busyid
    if (that.code === 34) {
      that.ttl = '付款复核'
    }
    that.invoiceID = paramCode.substring(2, paramCode.length)
    that.pund = Number(parampun)
    if (that.pund === 3) {
      that.ttl = '多收来的款'
    } else if (that.pund !== 0) {
      that.ttl = '采购的预付款'
    }
    that.getDetail()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jumpExplain: function () {
      this.$router.push({
        path: `/approveReimburseDetailExplain`
      })
    },
    strSub: function () {
      let reasonLen = this.reason.length
      if (reasonLen > 40) {
        this.reason = this.reason.substr(0, 40)
      }
    },
    getDetail: function () {
      let that = this
      console.log(that.pund)
      if (that.pund === 3) {
        this.axios.post('../../../overpayment/getOverpaymentDetail.do', { 'loanBizId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailsInfo = res.loanBiz
            console.log("-------:" + JSON.stringify( that.details))
            console.log("***********" + JSON.stringify( that.detailsInfo))
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
          })
          .catch(function (error) {
            console.log(error)
          })
      } else if (that.pund === 0) {
        this.axios.post('../../../purchaseInvoice/getPurchaseInvoiceDetail.do', { 'applicationId': that.invoiceID })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            let poOrders = res.poOrders
            let rate1 = '- -'
            let rate2 = '- -'
            poOrders.invoicedAmount = poOrders.invoicedAmount || 0
            poOrders.payedAmount = poOrders.payedAmount || 0
            let rate3 = (poOrders.invoicedAmount / poOrders.amount * 100).toFixed(2)
            let rate4 = (poOrders.payedAmount / poOrders.amount * 100).toFixed(2)
            that.details.orderProgress = [
              { 'name': '检验已合格', 'amount': `${poOrders.checkedAmount ? poOrders.checkedAmount.toFixed(2) : ''}`, 'rate': rate1 },
              { 'name': '已入库', 'amount': `${poOrders.storedAmount ? poOrders.storedAmount.toFixed(2) : ''}`, 'rate': rate2 },
              { 'name': '票据已提交', 'amount': poOrders.invoicedAmount.toFixed(2), 'rate': rate3 + '%' },
              { 'name': '已付款', 'amount': poOrders.payedAmount.toFixed(2), 'rate': rate4 + '%' }
            ]
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            if (that.nowApproveItem.approveStatus === 1) {
              that.isRevoke = true
            }
            let lastToUser = that.nowApproveItem.toUser
            if (!lastToUser || lastToUser === that.sphdSocket.uuid()) {
              that.isNowApprover = true
            }
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do', { 'orderPrepaymentId': that.invoiceID })
          .then(function (response) {
            let res = response.data.data
            that.detailsote = res
            that.loading = false
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    orderProgressRade1 () {
      let id = this.detailsote.poOrders.id
      this.$router.push({
        path: `/purchaseInvoiceMore/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    },
    goBillInfo: function (item) {
      let params = {
        'applicationId': this.$route.params.id,
        'name': 'purchaseInvoice',
        'type': '1',
        'num': item.num,
        'totalAmount': item.totalBillAmount.toFixed(2),
        'billCat': item.invoiceCategory
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFeeInfo: function (item) {
      let params = {
        'applicationId': this.$route.params.id,
        'type': '2',
        'createName': this.details.poOrders.createName,
        'name': 'purchaseInvoice',
        'totalAmount': item.totalBillAmount && item.totalBillAmount.toFixed(2),
        'feeCategory': '采购材料',
        'feeCatName': '采购材料'
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    approve2: function (type) {
      let approveInfo = {
        approvalProcessId: this.nowApproveItem.id,
        approveStatus: this.approveSta
      }
      let that = this
      if (Number(approveInfo.approveStatus) > 0) {
      } else {
        that.$kiko_message('请先选择审批结果')
        return false
      }
      if (type === 'onlineAuditApproval') { // 在线审核
        approveInfo['reason'] = this.reason
        approveInfo['approveSelect'] = this.approveSelect.toString()
        if (approveInfo.approveSelect.length === 0 && Number(approveInfo.approveStatus) === 3) {
          that.$kiko_message('请先选择在线审批未通过的原因')
          return false
        }
        if (approveInfo.approveSelect.length === 1 && approveInfo.approveSelect[0] === '5' && approveInfo['reason'].length === 0) {
          return false
        }
        console.log(approveInfo)
        that.loading = true
        this.axios.post('../../../purchaseInvoice/paymentOnlineApproval.do', approveInfo)
          .then(function (response) {
            console.log('详情：', response)
            let res = response.data.data
            that.$kiko_message(res.content)
            that.loading = false
            if (res.content === '操作成功') {
              if (that.approveSta === 2) { // 批准
                localStorage.setItem('navNum', '2')
              } else { // 驳回
                localStorage.setItem('navNum', '1')
              }
              that.$router.push({
                path: `/purchaseBillFinanceApproval`
              })
            }
          })
          .catch(function (error) {
            console.log(error)
          })
      } else if (type === 'offlineAuditApproval') { // 线下审核
        that.loading = true
        this.axios.post('../../../purchaseInvoice/purchaseOfflineApproval.do', approveInfo)
          .then(function (response) {
            let res = response.data.data
            that.$kiko_message(res.content)
            that.loading = false
            if (res.content === '操作成功') {
              localStorage.setItem('navNum', '3')
              that.$router.push({
                path: `/purchaseBillFinanceApproval`
              })
            }
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    }
  },
  computed: {
    isture: function () {
      let one = Number(this.detailsote.poOrdersPrepayment.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  },
  watch: {
    approveSelect () {
      let index5 = this.approveSelect.indexOf('5')
      if (this.approveSelect.length > 1 && index5 > -1) {
        this.approveSelect = ['5']
        this.$kiko_message('选择“其他原因”后不可选择其他的原因')
      }
    }
  }
}
</script>
