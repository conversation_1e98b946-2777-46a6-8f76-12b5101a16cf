package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquCategoryHistory;
import cn.sphd.miners.modules.equipment.mapper.TEquCategoryHistoryMapper;
import cn.sphd.miners.modules.equipment.service.ITEquCategoryHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_分类历史
 * 20230209 1.247装备器具新增Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquCategoryHistoryServiceImpl implements ITEquCategoryHistoryService {
    @Autowired
    private TEquCategoryHistoryMapper tEquCategoryHistoryMapper;

    /**
     * 查询装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_分类历史
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_分类历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public TEquCategoryHistory selectTEquCategoryHistoryById(Long id) {
        return tEquCategoryHistoryMapper.selectTEquCategoryHistoryById(id);
    }

    /**
     * 查询装备管理_分类历史
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquCategoryHistory 装备管理_分类历史
     *                            20230209 1.247装备器具新增
     * @return 装备管理_分类历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public List<TEquCategoryHistory> selectTEquCategoryHistoryList(TEquCategoryHistory tEquCategoryHistory) {
        return tEquCategoryHistoryMapper.selectTEquCategoryHistoryList(tEquCategoryHistory);
    }

    /**
     * 新增装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategoryHistory 装备管理_分类历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int insertTEquCategoryHistory(TEquCategoryHistory tEquCategoryHistory) {
        return tEquCategoryHistoryMapper.insertTEquCategoryHistory(tEquCategoryHistory);
    }

    /**
     * 修改装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategoryHistory 装备管理_分类历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int updateTEquCategoryHistory(TEquCategoryHistory tEquCategoryHistory) {
        return tEquCategoryHistoryMapper.updateTEquCategoryHistory(tEquCategoryHistory);
    }

    /**
     * 批量删除装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_分类历史
     *            20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquCategoryHistoryByIds(String... ids) {
        return tEquCategoryHistoryMapper.deleteTEquCategoryHistoryByIds(ids);
    }

    /**
     * 删除装备管理_分类历史
     * 20230209 1.247装备器具新增信息
     *
     * @param id 装备管理_分类历史
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquCategoryHistoryById(Long id) {
        return tEquCategoryHistoryMapper.deleteTEquCategoryHistoryById(id);
    }
}
