<template>
  <div class="wheater">
    <loading v-show="isLoad" />
    <div class="logo">
      <span>Endress + Hauser</span>
      <img src="../assets/logo.png" class="logoimg" />
    </div>
    <h3>{{ info.name }}{{info.exp}}</h3>
    <div class="bobo" v-for="item in contentList" :key="item">
      <div v-if="item.type == 1" v-html="item.content"></div>
      <div v-else-if="item.type == 2">
        <img
          :src="`${linkBase}/upload/` + item.resEntity.path"
          :alt="item.title"
        />
        <div>{{ item.description }}</div>
      </div>
      <div v-else-if="item.type == 3">
        <video class="vdo" controls="controls">
          <source
            :src="
              `${linkBase}/upload/` + item.resEntity.path
            "
            type="video/mp4"
          />
        </video>
        <div>{{ item.description }}</div>
      </div>
    </div>
    <div v-if='info.exp != null && info.exp <= dayjs().add(1, "day").valueOf()'>
      可观看至：{{ dayjs(info.exp).format('YYYY年MM月DD日 HH:mm:ss') }}<span>{{countdown}}</span>
    </div>
    <h1>Endress+Hauser China 版权所有</h1>
  </div>
</template>

<style lang="scss" scoped>
.wheater {
  text-align: left;
  padding: 30px 16px;
  img,
  video {
    width: 100%;
  }
  .logo {
    text-align: right;
    height: 66px;
    line-height: 66px;
    padding: 0 20px;
    span {
      font-size: 24px;
    }
    .logoimg {
      display: inline-block;
      width: 44px;
    }
  }
  h1 {
    font-size: 14px;
    margin-top: 75px;
    text-align: center;
  }
}
</style>

<script>
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import JSONBig from 'json-bigint'
import { getFileDetails } from "../api/api";
import loading from "../components/loading.vue";
import auth from "@/sys/auth"
import { useRouter } from "vue-router";
dayjs.extend(duration)
export default {
  data() {
    return {
      timer: null,
      countdown: '',
      dayjs: dayjs,
      info: {},
      isLoad: true,
      contentList: {},
      linkBase: auth.webRoot
    };
  },
  components: {
    loading,
  },
  mounted() {
    console.log('linkBase=', this.linkBase)
    let params = localStorage.getItem('param2');//获取存储的元素
    params = JSONBig.parse(params)
    let fileID = params.id;
    // let fileID = this.$route.params.id;
    this.getfile(fileID);
    this.timer = setInterval(() => {
      let exp = this.info?.exp
      if (exp) {
        if (exp <= new Date().getTime()) {
          useRouter().go(-1);
        } else if (exp <= dayjs().add(1, 'day').valueOf()) {
          this.countdown = ',剩余' + dayjs.duration(dayjs(exp) - dayjs()).format('HH:mm:ss')
        }
      }
    }, 1000)
  },
  beforeUnmount() {
    console.log('beforeUnmount')
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    getfile(fileID) {
      this.isLoad = true;
      getFileDetails(fileID).then((ddc) => {
        let res = ddc.data
        console.log(res);
        this.isLoad = false;
        this.contentList = res.listResAtt;
        this.info = res.res;
      });
    },
  },
};
</script>