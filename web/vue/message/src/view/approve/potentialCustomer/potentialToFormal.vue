<template>
  <div id="potentialToFormal" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="潜在客户受理">
      <div class="nav_btn" @click="submitForm">提交</div>
    </TY_NavTop>
    <div class="container">
      <div class="panel-title">您可在此先确认客户信息，再进行后续操作。</div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :label-position="left" :rules="rules" label-width="110px" :model="addCus" ref="addCus">
            <el-form-item label="客户名称" prop="fullName">
              <el-input v-model="addCus.fullName" size="small"></el-input>
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input v-model="addCus.address" size="small"></el-input>
            </el-form-item>
            <el-form-item label="客户代号" prop="code">
              <el-input v-model="addCus.code" size="small"></el-input>
            </el-form-item>
            <el-form-item label="最高负责人" prop="supervisorName">
              <el-input v-model="addCus.supervisorName" size="small"></el-input>
            </el-form-item>
            <el-form-item label="手机" prop="supervisorMobile">
              <el-input v-model="addCus.supervisorMobile" size="small"></el-input>
            </el-form-item>
          </el-form>
          <div class="checkModule">
            <el-checkbox v-model="checked">转为正式客户，选择产品</el-checkbox>
          </div>
          <div v-if="checked">
            <el-form :label-position="left" :rules="rules2" label-width="110px" :model="addOrg" ref="addOrg">
              <el-form-item label="机构名称" prop="fullName">
                <el-input v-model="addOrg.fullName" size="small" @input="setSimpleName"></el-input>
              </el-form-item>
              <el-form-item label="机构简称" prop="name">
                <el-input v-model="addOrg.name" size="small"></el-input>
              </el-form-item>
              <el-form-item label="超管姓名" prop="supervisorName">
                <el-input v-model="addOrg.supervisorName" size="small"></el-input>
              </el-form-item>
              <el-form-item label="超管手机" prop="supervisorMobile">
                <el-input v-model="addOrg.supervisorMobile" size="small"></el-input>
              </el-form-item>
              <el-form-item label="数据存储地点" prop="uploadStorageType">
                <el-select v-model="addOrg.uploadStorageType" size="small" style="width: 100%;">
                  <el-option label="NFS" value="NFS"></el-option>
                  <el-option label="seafile" value="seafile" disabled></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所选产品" prop="packageId">
                <el-select v-model="addOrg.packageId" size="small" style="width: 100%;">
                  <el-option v-for="item in productOptions"
                             :key="item.id"
                             :label="item.name"
                             :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div class="text-right">
              <a class="color-blue" @click="seeProductDetail">查看所选产品</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, gohistory } from '@/js/common'
export default {
  filters: {
    formatDate,
    gohistory
  },
  name: 'potentialToFormal',
  data () {
    let checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('手机号不能为空'))
      } else {
        let pattern = /^(0|86|17951)?(13[0-9]|15[012356789]|16[012356789]|17[6780]|18[0-9]|14[57])[0-9]{8}$/
        if (pattern.exec(value) == null) { return callback(new Error('请输入正确的手机号')) } else { callback() }
      }
    }
    return {
      addCus: {
        fullName: '',
        address: '',
        code: '',
        supervisorName: '',
        supervisorMobile: ''
      },
      addOrg: {
        fullName: '',
          name: '',
          supervisorName: '',
          supervisorMobile: '',
          uploadStorageType: '',
          packageId: ''
      },
      loading: true,
      checked: false,
      rules: {
        fullName: [
          { required: true, message: '请输入必填项' }
        ],
        address: [
          { required: true, message: '请输入必填项' }
        ],
        supervisorMobile: [
          { required: true, message: '请输入必填项' },
          { validator: checkPhone, trigger: 'blur' }
        ],
        supervisorName: [
          { required: true, message: '请输入必填项' }
        ]
      },
      rules2: {
        name: [
          { required: true, message: '请输入必填项' }
        ],
        fullName: [
          { required: true, message: '请输入必填项' }
        ],
        supervisorMobile: [
          { required: true, message: '请输入必填项' },
          { validator: checkPhone, trigger: 'blur' }
        ],
        uploadStorageType: [
          { required: true, message: '请选择必填项' }
        ],
        packageId: [
          { required: true, message: '请选择必填项' }
        ]
      },
      isIndeterminate: false,
      potentialId: 0,
      productOptions: []
    }
  },
  created () {
    let that = this
    let id = this.$route.params.id
    let query = this.$route.params
    if (query.data) {
      this.addCus = query.data
    } else if (typeof this.$store.getters.getCusorg.addCus !== 'undefined') {
      this.addCus = this.$store.getters.getCusorg.addCus
      this.addOrg = this.$store.getters.getCusorg.addOrg
      this.checked = this.$store.getters.getCusorg.checked
    }
    this.potentialId = id
    this.axios.post('../../../productSetting/getSelectMpPackages.do')
      .then(function (response) {
        console.log(response)
        let getData = response.data.data
        that.productOptions = getData
        that.loading = false
      })
      .catch(function (error) {
        console.log(error)
      })
  },
  methods: {
    submitCus: function () {
      let _this = this
      return new Promise(resolve => {
        _this.axios.post('../../../special/getSaleCtrlsByMobile.do', { mobile: _this.addCus.supervisorMobile }).then(response => {
          let status = response.data.data
          if (status === 2) {
            _this.$kiko_message('系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！')
            _this.loading = false
          } else if (status === 1) {
            resolve(response)
          } else {
            _this.$kiko_message('系统错误！')
          }
        })
      }).then(res => {
        console.log('res', res)
        return new Promise(resolve => {
          _this.addCus.id = _this.potentialId
          _this.axios.post('../../../sale/turnFormalCustomer.do', _this.addCus).then(function (response) {
            let status = response.data.status
            console.log('turnFormalCustomer', response)
            if (status && status === 1) {
              resolve(status)
            } else {
              _this.$kiko_message('系统错误！')
            }
          })
        })
      })
    },
    submitForm: function () {
      console.log(this.appObject)
      let _this = this
      let promiseArr = []

      let result = new Promise(function (resolve, reject) {
        _this.$refs['addCus'].validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('addCus error'))
          }
        })
      })

      promiseArr.push(result)
      if (this.checked) {
        let result2 = new Promise(function (resolve, reject) {
          _this.$refs['addOrg'].validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('addOrg error'))
            }
          })
        })
        promiseArr.push(result2)
        Promise.all(promiseArr).then(function () {
          // let appObject = _this.appObject
          _this.loading = true
          _this.submitCus().then(val => {
            if (val && val === 1) {
              _this.addOrg.customerId = _this.potentialId
              _this.axios.post('../../../special/addOrgApply.do ', _this.addOrg).then(function (response) {
                let data = response.data.data
                if (data === 1) {
                  _this.$kiko_message('操作成功！')
                  gohistory(_this, 2)
                  _this.$router.push({
                    path: `/potentialCustomerApprovalDuty`
                  })
                  _this.loading = false
                } else {
                  _this.$kiko_message('操作失败！')
                }
              }).catch(function (error) {
                console.log(error)
              })
            }
          })
        }).catch(function (error) {
          console.log(error)
          _this.loading = false
          _this.$kiko_message('请填必填项')
        })
      } else {
        result.then(val => {
          this.$confirm('您确定暂不选择产品！', '！提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            _this.submitCus().then(val => {
              console.log(val)
              if (val === 1) {
                _this.$kiko_message('操作成功！')
                gohistory(_this, 2)
                _this.loading = false
              }
            })
          })
        })
      }
    },
    setSimpleName: function (value) {
      this.addOrg.name = value.slice(0, 6)
    },
    seeProductDetail: function () {
      this.$store.dispatch('setCusorg', {
        addCus: this.addCus,
        addOrg: this.addOrg,
        checked: this.checked
      })
      let id = this.addOrg.packageId
      if (id) {
        console.log(this.addOrg)
        this.$router.push({
          path: `/productDetail/${id}`
        })
      } else {
        this.$kiko_message('请选择产品')
      }
    }
  }
}
</script>
