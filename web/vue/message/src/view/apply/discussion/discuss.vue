<template>
  <div id="forumApply">
    <TY_NavTop title="讨论发起申请" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">如下{{toHandle.length}}个讨论主题由您发起，有待审批</div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toHandle" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.title}}</div>
              <div style="margin-left: 20px">讨论发起人 {{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'forumApply',
  data () {
    return {
      toHandle: [],
      listenersUid: []
    }
  },
  created () {
    let that = this
    this.axios.post('../../../forum/getApplyForumPost.do')
      .then(function (response) {
        console.log(response)
        let getData = response.data.data
        that.toHandle = getData.listApplyForum
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('applyForumPost', function (data) {
        console.log('applyForumPost user:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.forumPost
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.toHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toHandle.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/discussDetail/${id}/apply`
      })
    },
    search: function () {
      this.$router.push({
        path: `/discussQuery/apply`
      })
    }
  }
}
</script>
