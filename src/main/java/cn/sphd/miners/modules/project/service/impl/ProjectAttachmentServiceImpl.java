package cn.sphd.miners.modules.project.service.impl;

import cn.sphd.miners.modules.project.dao.ProjectAttachmentDao;
import cn.sphd.miners.modules.project.entity.ProjectAttachment;
import cn.sphd.miners.modules.project.service.ProjectAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Administrator on 2018/5/31.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ProjectAttachmentServiceImpl implements ProjectAttachmentService{

    @Autowired
    ProjectAttachmentDao projectAttachmentDao;

    @Override
    public void saveProjectAttachment(ProjectAttachment projectAttachment) {
        projectAttachmentDao.save(projectAttachment);
    }

    @Override
    public ProjectAttachment getProjectAttachmentById(Integer id) {
        return projectAttachmentDao.get(id);
    }
}
