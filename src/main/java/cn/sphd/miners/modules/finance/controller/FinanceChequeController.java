package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceCheque;
import cn.sphd.miners.modules.finance.entity.FinanceChequeDetail;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.finance.service.FinanceChequeService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/12/7.
 * 支票管理
 */
@Controller
@RequestMapping("/cheque")
public class FinanceChequeController {

    @Autowired
    FinanceChequeService financeChequeService;

    @Autowired
    FinanceAccountService financeAccountService;

    @Autowired
    OrgService orgService;

    /**
     *<AUTHOR>
     *@date 2016/12/9
     *获取现金/转账支票     1.214改，此接口暂时不用，换成下面的接口
     */
//    @ResponseBody
//    @RequestMapping("/getCheque.do")
//    public void getCheque(HttpServletResponse response,User user, Integer pageNumber, Integer quantum, String type) throws IOException {
//        Map<String,Object> map = new HashMap<>();
//        Integer oid = user.getOid();
//        List<FinanceCheque> financeCheque = financeChequeService.getCheque(oid,type);
//        int total = 0; //总条数
//        if (financeCheque.size() > 0){
//            total = financeCheque.size();
//        }
//        int totalPage;
//        if (total!=0 && quantum !=0){
//            if (total%quantum == 0){
//                totalPage = total/quantum;
//            }else {
//                totalPage = total/quantum+1;
//            }
//        } else {
//            totalPage = 1;
//        }
//
//        List<FinanceCheque> financeCheque1 = new ArrayList<>();
//        if (pageNumber!=0 && quantum!=0 && total!=0){
//            int min = pageNumber*quantum-quantum;
//            int max = pageNumber*quantum;
//            for (int i=min; i<max; i++){
//                if (i<total){
//                    financeCheque1.add(financeCheque.get(i));
//                }
//            }
//        }
//        map.put("pageNumber",pageNumber);  //当前页数
//        map.put("totalPage",totalPage);    //总页数
//        map.put("financeCheques",financeCheque1);
//        ObjectToJson.objectToJson1(map,new String[]{"financeChequeDetailHashSet","org"},response);
//    }


    /**
     * <AUTHOR>
     * @Description 获取现金/转账支票
     * @Date 2022/8/5
     * @param String type 1-转帐支票，2-现金汇票
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getCheque.do")
    public JsonResult getCheque(User user,Integer oid, String type, PageInfo pageInfo) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        List<FinanceCheque> financeCheque = financeChequeService.getCheque(oid,orgIntegerList,type,pageInfo);
        for (FinanceCheque fc:financeCheque) {
            fc.setOrgName(fc.getOrg().getName());
        }
//        map.put("pageNumber",pageNumber);  //当前页数
//        map.put("totalPage",totalPage);    //总页数
        map.put("financeCheques",financeCheque);
        map.put("pageInfo",pageInfo);    //页数
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2016/12/9 16:29
     *添加现金/转账支票(type 1-转帐支票，2-现金汇票)
     */
    @ResponseBody
    @RequestMapping("/addCashCheque.do")
    public void addCashCheque(FinanceCheque financeCheque,String buyDate1, Integer accountId, HttpServletResponse response,User user) throws Exception{
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        if(financeCheque != null ){
            financeCheque.setOrg(orgService.getByOid(oid));
//            financeCheque.setType(type);   //支票类型 '1-转帐支票，2-现金汇票',
            financeCheque.setBuyDate(sdf.parse(buyDate1));
            financeCheque.setCreator(user.getUserID());
            financeCheque.setCreateDate(new Date());
            financeCheque.setCreateName(user.getUserName());
            financeChequeService.addCashCheque(financeCheque);
            String beginNo1 = financeCheque.getBeginNo();
            String beginNo2 = "";
            if (beginNo1.substring(0,1).equals("0")){
                for (int i=0;i<beginNo1.length();i++){
                    if (beginNo1.substring(i,i+1).equals("0")){
                        beginNo2 = beginNo1.substring(0,i+1).trim();
                    }else {
                        break;
                    }
                }
            }
            Integer beginNo = Integer.parseInt(financeCheque.getBeginNo());
            Integer endNo = Integer.parseInt(financeCheque.getEndNo());
            Integer noSize = beginNo1.length();
            for (int i=beginNo;i<=endNo;i++){
                FinanceChequeDetail financeChequeDetail = new FinanceChequeDetail();
                financeChequeDetail.setBankName(financeCheque.getBankName());
                financeChequeDetail.setAccount(financeCheque.getAccount());
                financeChequeDetail.setAccountId(financeAccountService.getAccountById(accountId));
                String cheNo = beginNo2+String.valueOf(i);
                financeChequeDetail.setChequeNo(cheNo.length()>noSize?cheNo.substring(cheNo.length()-noSize,cheNo.length()):cheNo);
                financeChequeDetail.setChequeReg(financeCheque);
                financeChequeDetail.setType(financeCheque.getType());  //支票类型 '1-转帐支票，2-现金汇票',
                financeChequeDetail.setState("1");  //支票状态 '空或1-未使用,2-已使用,3-作废'
                financeChequeDetail.setCreator(user.getUserID());
                financeChequeDetail.setCreateName(user.getUserName());
                financeChequeDetail.setCreateDate(new Date());
                financeChequeService.addCashChequeDetail(financeChequeDetail);
            }
            map.put("status",1); //添加成功
        }else {
            map.put("status",0); //添加失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"financeChequeDetailHashSet","org","chequeReg","financeAccountBillHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2016/12/13 16:37
     *查询所有可用的银行账户
     */
    @ResponseBody
    @RequestMapping("/getBankAccount.do")
    public void getBankAccount(User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<FinanceAccount> financeAccounts = financeAccountService.getAccountByAccountStatus(oid,1,null);
        map.put("financeAccounts",financeAccounts);
        ObjectToJson.objectToJson1(map,new String[]{"org","financeAccountHistories","accountPeriodHashSet","accountDetailHashSet","financeChequeDetailHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2016/12/14 9:48
     *查询现金/转账支票明细
     * chequeReg:支票登记ID
     */
    @ResponseBody
    @RequestMapping("/getCashChequeDetail.do")
    public void getCashChequeDetail(HttpServletResponse response, Integer chequeReg) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<FinanceChequeDetail> chequeDetails = financeChequeService.getChequeDetail(chequeReg);
        map.put("chequeDetails",chequeDetails);
        ObjectToJson.objectToJson1(map,new String[]{"chequeReg","accountId","financeAccountBillHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2016/12/14 10:29
     *现金/转账支票作废
     */
    @ResponseBody
    @RequestMapping("/updateState.do")
    public void updateState(HttpServletResponse response, Integer detailId,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        FinanceChequeDetail financeChequeDetail = financeChequeService.getChequeByDetailId(detailId);
        if (financeChequeDetail != null){
            financeChequeDetail.setState("3"); //支票状态 1-未使用,2-已使用,3-作废
            financeChequeDetail.setUpdator(user.getUserID());
            financeChequeDetail.setUpdateDate(new Date());
            financeChequeDetail.setUpdateName(user.getUserName());
            financeChequeService.updateCashChequeState(financeChequeDetail);
            map.put("status",1);  //修改成功
        }else {
            map.put("status",0);  //修改失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"chequeReg","accountId","financeAccountBillHashSet"},response);
    }

    //-------------------------以下是1.155I/A首页之财会2的“申领来的银行支票”部分---------------------------------
    /**
     *<AUTHOR>
     *@date 2021/3/9 9:30
     *申领来的银行支票列表
     * oid-机构id
    */
    @ResponseBody
    @RequestMapping("/getFinanceChequeTime.do")
    public JsonResult getFinanceChequeTime(User user) {
        Map<String,Object> map = new HashMap<>();
        if (user!=null)
        map = financeChequeService.getFinanceChequeTime(user.getOid());
        return new JsonResult(1,map);
//        ObjectToJson.objectToJson1(map,new String[]{"chequeReg","accountId","financeAccountBillHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/9 10:25
     *所有账户的空白支票列表
     * oid-机构id
     * type-1-转帐支票，2-现金汇票
     */
    @ResponseBody
    @RequestMapping("/getAccountCheques.do")
    public JsonResult getAccountCheques(User user,String type) {
        Map<String,Object> map = new HashMap<>();
        if(user!=null && !MyStrings.nulltoempty(type).isEmpty()) {
            financeChequeService.getAccountCheques(user.getOid(), type,null,map);
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/9 13:56
     *某账户的所有空白支票列表
     * type-1-转帐支票，2-现金汇票
     */
    @ResponseBody
    @RequestMapping("/getAccountChequeList.do")
    public JsonResult getAccountChequeList(Integer accountId,String type) {
        Map<String,Object> map = new HashMap<>();
        if(accountId!=null) {
            map = financeChequeService.getAccountChequeList(accountId,type);
        }
        return new JsonResult(1,map);
    }
    
    /**
     *<AUTHOR>
     *@date 2021/3/9
     *某年申领的所有支票
     * yearDate-年月日（某年中的具体某天）
    */
    @ResponseBody
    @RequestMapping("/getYearCheques.do")
    public JsonResult getYearCheques(User user,String yearDate) {
        Map<String,Object> map = new HashMap<>();
        Date beginDate = NewDateUtils.getNewYearsDay(NewDateUtils.dateFromString(yearDate,"yyyy-MM-dd"));
        Date endDate = NewDateUtils.getLastTimeOfYear(NewDateUtils.dateFromString(yearDate,"yyyy-MM-dd"));
        List<FinanceCheque> financeCheques = new ArrayList<>();
        if(user!=null&&!MyStrings.nulltoempty(yearDate).isEmpty()) {
            financeCheques= financeChequeService.getYearCheques(user.getOid(),beginDate,endDate);
        }
        map.put("financeCheques",financeCheques);   //详情
        map.put("num",financeCheques.size());  //申领次数
        map.put("beginDate",beginDate);  //开始时间
        map.put("endDate",endDate);   //结束时间
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/9
     *某次申领的所有支票详情列表
     */
    @ResponseBody
    @RequestMapping("/getChequeDetails.do")
    public JsonResult getChequeDetails(Integer chequeId) {
        Map<String,Object> map = new HashMap<>();
        FinanceCheque financeCheque = financeChequeService.getFinanceChequeById(chequeId);
        List<FinanceChequeDetail> chequeDetails = financeChequeService.getChequeDetail(chequeId);
        map.put("financeCheque",financeCheque);   //某次申领的信息
        map.put("chequeDetails",chequeDetails);   //所有支票明细信息
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/9
     *某张支票详情
     */
    @ResponseBody
    @RequestMapping("/getChequeDetail.do")
    public JsonResult getChequeDetail(Integer chequeDetailId) {
        Map<String,Object> map = new HashMap<>();
        FinanceChequeDetail chequeDetail = financeChequeService.getChequeByDetailId(chequeDetailId);
        map.put("chequeDetail",chequeDetail);   //支票明细信息
        return new JsonResult(1,map);
    }
}
