package cn.sphd.miners.modules.accountantReportTax.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-09-06 
 */

@Entity ( name ="AcctTriflesDetailHistoryEntity" )
@Table ( name ="t_accountant_trifles_detail_history" )
public class AcctTriflesDetailHistoryEntity implements Serializable {

	private static final long serialVersionUID =  8502174577167515798L;

	/**
	 * ID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
   	@Column(name = "id" )
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 明细ID
	 */
   	@Column(name = "trifles_detail" )
	private Integer triflesDetail;

	/**
	 * 琐事ID
	 */
   	@Column(name = "trifles" )
	private Integer trifles;

	/**
	 * 杂项历史ID
	 */
   	@Column(name = "trifles_history" )
	private Integer triflesHistory;

	/**
	 * 申报期YYYYMMDD
	 */
   	@Column(name = "period" )
	private String period;

	/**
	 * 状态:0-未报,1-已报
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 办理时间
	 */
   	@Column(name = "delare_time" )
	private Date delareTime;

	/**
	 * 说明图片路径
	 */
   	@Column(name = "document_path" )
	private String documentPath;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getTriflesDetail() {
		return this.triflesDetail;
	}

	public void setTriflesDetail(Integer triflesDetail) {
		this.triflesDetail = triflesDetail;
	}

	public Integer getTrifles() {
		return this.trifles;
	}

	public void setTrifles(Integer trifles) {
		this.trifles = trifles;
	}

	public Integer getTriflesHistory() {
		return this.triflesHistory;
	}

	public void setTriflesHistory(Integer triflesHistory) {
		this.triflesHistory = triflesHistory;
	}

	public String getPeriod() {
		return this.period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Date getDelareTime() {
		return this.delareTime;
	}

	public void setDelareTime(Date delareTime) {
		this.delareTime = delareTime;
	}

	public String getDocumentPath() {
		return this.documentPath;
	}

	public void setDocumentPath(String documentPath) {
		this.documentPath = documentPath;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
