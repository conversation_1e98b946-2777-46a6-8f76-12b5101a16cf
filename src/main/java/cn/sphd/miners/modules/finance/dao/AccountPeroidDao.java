package cn.sphd.miners.modules.finance.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.finance.entity.AccountPeriod;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2015-07-21.
 */
public interface AccountPeroidDao extends IBaseDao<AccountPeriod,Serializable> {
    List<Date> getPeriodMinAndMaxDate(Integer id);
}
