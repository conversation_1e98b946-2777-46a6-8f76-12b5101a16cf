import { createStore } from 'vuex'
import createPersistedstate from 'vuex-persistedstate'
import J<PERSON><PERSON><PERSON>ig from 'json-bigint'
import actions from './action'
const state = {
  version: 0,
  activePage: JSONBig.parse(localStorage.getItem('wonderssPersistedstate'))?.dataList?.activePage || [],

}
const mutations = {
  SET_VERSION (state, userBadge) {
    state.version = userBadge.ver
  },
  SET_ACTIVE_PAGE (state, obj) {
    state.activePage = obj
  }
}
const getters = {
  getVersion (state) {
    return state.version
  },
  getActivePage(){
    return state.activePage
  },
}

export default createStore({
  state,
  mutations,
  getters,
  actions,
  plugins: [createPersistedstate({
    key: 'wonderssPersistedstate',
    storage: localStorage,
    reducer(val) {
      return {
        dataList: {
          activePage: val.activePage
        }
      }
    }
  })]
})