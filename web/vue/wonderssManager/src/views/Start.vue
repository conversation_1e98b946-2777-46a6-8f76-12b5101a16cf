<template>
  <div id="startpage" class="startpage">
    <div class="">
      <div class="logoCon">
        <img :src="logo" alt="">
      </div>
      <div class="frmCon">
        <div v-if="mainCVisble === 1">
<!--
          <div class="item" v-if="nav === 1">
            <div class="conTtl">账号密码登录</div>
            <el-form class="form passwordForm" label-position="right" label-width="44px" :model="formLabelAlign">
              <el-form-item label="账号" size="large" class="el-form-item_border_bottom">
                <el-input v-model="formLabelAlign.name" placeholder="如无法确定，请咨询公司管理人员" :clearable="true"></el-input>
              </el-form-item>
              <el-form-item label="密码" size="large" class="el-form-item_border_bottom">
                <el-input type="password" v-model="formLabelAlign.pass" placeholder="请输入密码" :clearable="true"></el-input>
              </el-form-item>
              <div v-if="passWdloginClickNum > 9" style="display: flex">
                <el-input style="width:200px; display: inline-block;" clear v-model="formLabelAlign.codeVerify" placeholder="请输入右图中的字符" size="large"></el-input>
                <div class="verificationCode">
                  <div id="checkCode"></div>
                  <span style="font-size: 0.9em">看不清？<a class="ty-color-blue" @click="changePicVerification">换一个</a></span>
                </div>
              </div>
              <div class="subC el-form-item">
                <div class="login_btn" @click="loginBtn">登录</div>
              </div>
            </el-form>
          </div>

          -->
          <div class="item" v-if="nav === 2">
<!--            <div class="conTtl">手机短信登录</div>-->
            <el-form class="form mobileForm" label-position="right" label-width="60px" :model="formLabelAlign">
              <el-form-item label="账号" size="large" class="el-form-item_border_bottom">
                <el-input v-model="formLabelAlign.name" placeholder="如无法确定，请咨询公司管理人员"></el-input>
              </el-form-item>
              <el-form-item label="验证码" size="large" class="el-form-item_border_bottom">
                <el-input v-model="formLabelAlign.code" placeholder="请输入验证码" class="code">
                  <template #append>
                    <span :class="['codeBtn', {'disabled': sendCodeDisabled}]" type="primary" @click="sendCode">{{sendCodeName}}</span>
                  </template>
                </el-input>

              </el-form-item>
              <div class="subC el-form-item">
                <div class="login_btn" @click="codeLoginBtn">登录</div>
              </div>
            </el-form>
          </div>
<!--
          <div class="item" v-if="nav === 3">
            <div class="conTtl">微信登录</div>
            <div class="form">
              <wxlogin
                  style="height: 416px;width: 100%"
                  :appid="app.appId"
                  scope="snsapi_login"
                  theme='black'
                  :redirect_uri="redirectUriStr"
                  :href="href"
                  :state="state"
                  rel="external nofollow"
              ></wxlogin>
            </div>
          </div>
          -->
          <div class="linkBtns" style="display: flex; justify-content: space-between; ">
<!--            <div><span class="ty-linkBtn" @click="regAccBtn">注册账号</span></div>-->
<!--            <div><a class="ty-linkBtn" target="_blank" href="https://www.btransmission.com">关于我们</a></div>-->
          </div>

          <!--
          <div class="logMethod">
            <div class="logMethod_title">
              <div class="line"></div>
              <span class="ttlTxt">登陆方式</span>
              <div class="line"></div>
            </div>
            <div class="icon_row">
              <div :class="['imIm', {'active': nav===3}]" @click="changeNav(3)">
                <span class="fa fa-weixin"></span><br>
                微信
              </div>
              <div :class="['imIm', {'active': nav===2}]" @click="changeNav(2)">
                <span class="fa fa-envelope"></span><br>
                手机短信
              </div>
              <div :class="['imIm', {'active': nav===1}]" @click="changeNav(1)">
                <span class="fa fa-user-circle-o"></span><br>
                账号密码
              </div>
            </div>
          </div>

          -->

        </div>
        <div class="regArea" v-if="mainCVisble === 2">
          <div class="small_nav">
            <div class="left_logo"><span class="fa fa-angle-left regBack" @click="regBackBtn"></span></div>
            <div class="title">注册Wonderss账号</div>
          </div>
          <div style="padding:50px 0 50px 50px; ">
            <el-radio v-model="regVal" label="1">为公司注册，全公司的同事都将使用</el-radio>
            <br>
            <el-radio v-model="regVal" label="2">为自己注册，仅个人使用</el-radio>
          </div>
          <div class="el-form-item">
            <div class="login_btn" @click="">下一步</div>
          </div>
        </div>
        <div class="codeNext" v-if="mainCVisble === 3"  style=" font-size: 16px  ">
          <div style="color: #ed5565; font-size: 14px; line-height:30px;">
            <p v-if="codeNextErr == 1">两次输入的密码不一致，请重新设置！</p>
            <p v-if="codeNextErr == 2">您设置的密码过于简单，请重新设置！</p>
          </div>
          <div style="margin-bottom: 40px; font-size: 14px; line-height:22px;  ">
            <p style="color: #f58410;">请您设置密码，以便登录更快捷</p>
            <p style="color: #5d9cec;">忘记密码时，您可使用验证码登录</p>
          </div>

          <div>
            <el-form class="form formTr" label-position="left" label-width="80px" :model="codeNextFrm">
              <el-form-item label="密码" class="el-form-item_border_bottom">
                <el-input type="password" v-model="codeNextFrm.pass" placeholder="请输入密码" :clearable="true"></el-input>
              </el-form-item>
              <el-form-item label="确认密码" class="el-form-item_border_bottom">
                <el-input type="password" v-model="codeNextFrm.pass2" placeholder="请重新输入密码" :clearable="true"></el-input>
              </el-form-item>
              <div style="color: #5d9cec; font-size: 14px; ">
                <small>注：密码需为8-16位，必须包括数字和英文字母，英文字母分大小写</small>
              </div>

              <div class="subC el-form-item" style="margin-top: 16px">
                <div class="login_btn" @click="codeNextBtn">设置完毕，登录系统</div>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
#startpage{
  box-sizing: border-box;
  .frmCon{
    width: 90%; margin:20px auto;
    .rLink{
      float: right;
    }
    .form{
      margin-top: 36px;
      .el-form-item_border_bottom{
        border-bottom: 1px solid #eee;
      }
      .el-input,.el-input__wrapper{
        border: none!important;
        box-shadow:none;
        background: inherit;
      }
      .el-input-group__append{
        border: none;
        box-shadow: none;
        background: inherit;
        border-radius: 5px;
        padding: 0;
        .codeBtn {
          padding: 0 14px;
          background-color: $tyColorGreen;
          color: #fff;
          display: inline-block;
          line-height: 36px;
          text-align: center;
          border-radius: 3px;
          height: 36px;
          cursor: pointer;
          font-size: 13px;
          border: none;
          &.disabled{
            background-color: $tyColorGray ;
            color: #666;
            cursor: default;
          }
        }
      }

    }
    .createShortCut{
      font-size: 14px;
      color: #666;
    }
    .logMethod{
      margin-top: 36px;
      .logMethod_title{
        display: flex;
        align-items: center;
        .ttlTxt{
          color: #666;
          margin: 0 16px;
        }
        .line{
          height: 1px;
          background-color: #eee;
          flex: auto;
        }
      }
      .icon_row{
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
        .imIm{
          width: 70px;
          text-align: center;
          font-size: 13px;
          line-height: 2;
          .fa{
            font-size: 22px;
            color: #ccc;
          }
          &.active .fa-weixin{
            color: #04c15f;
          }
          &.active .fa-envelope{
            color: $tyColorOrange
          }
          &.active .fa-user-circle-o{
            color: $tyColorBlue
          }
        }
      }
    }

    .small_nav{
      display: flex;
      height: 35px;
      align-items: center;
      .left_logo{
        width: 20px;
        position: absolute;
        .regBack{
          font-size: 28px;
          color: #888;
        }
      }
      .title{
        flex: auto;
        text-align: center;
      }
    }

  }

  .login_btn{
    width: 100%;
    background: $tyColorGreen ;
    border-radius: 3px;
    text-align: center;
    padding: 12px 0;
    color: #fff;
    &:hover{
      background: $tyColorGreen ;
    }
  }
  .logoCon{
    padding:60px 0 40px;
    text-align: center;
  }


}


</style>
<script lang="js">
import JSONBig from 'json-bigint'
import wx from 'weixin-js-sdk'
import { saveAs } from 'file-saver'
import { authInit, tpRegisterVerificationCode,  tpCheckRegisterVerificationCode } from "@/api/api";
import auth from '@/sys/auth'
import '@/utils/GVerify.js'
import { ElMessage } from 'element-plus'
import logo from '@/assets/wonderss/wonderssLogo.png'

var verifyCode
export default {
  name: "Start",
  data() {
    return {
      logo: logo,
      passportHost: '',
      app: {
        tgCode: '',
        appId: '',
      },
      redirectUriStr:'',
      state:'',
      href:'',
      isWindowsFirefox: false,
      shortcutTips: '',
      passWdloginClickNum:0,
      GVerifyTimer:-1,
      wxIframeSrc : '',
      qrCodeVisble : false,
      regVal : '',
      mainCVisble : 1,
      codeNextErr : 0,
      hrefStr : '',
      rootPath : '',
      heightClent: 0,
      nav: 2,
      labelPosition: "right",
      codeNextFData:{},
      codeNextFrm:{
        pass:'',
        pass2:'',
      },
      formLabelAlign: {
        name: "",
        pass: "",
        code: "",
        codeVerify: "",
        jiAcc: "",
        jiPas: "",
      },
      sendCodeName: '获取验证码',
      senCodePhone: '',
      sendCodeDisabled: false
    };
  },
  components: {
  },
  mounted() {
    this.start();
    // let height = Math.max(document.documentElement.clientHeight, document.body.clientHeight);
    // console.log(height);
    // this.heightClent = height;
    //
    //
    // let urlParams = location.search.lastIndexOf('?') >= 0 ? location.search : location.hash
    // let params = qs.parse(urlParams.substring(urlParams.lastIndexOf('?')), { ignoreQueryPrefix: true })
    // console.log('qs', params, params.code, params.state, params.state == auth.getToken())
    // if(params.logout != null && auth.isLogged()) {
    //   clearStorage()
    //   auth.getGuestToken()
    // }
    // // console.log('auth.isLogged=', auth.isLogged())
    // if(auth.isLogged()){
    //   this.$router.push('homeMy')
    // } else if(typeof params.code !== 'undefined' && typeof params.state !== 'undefined' && !auth.isLogged()) {
    //   this.mpCodeLogin(params)
    // }
  },
  watch: {
    passWdloginClickNum(newVal, oldVal) {
      console.log(newVal, oldVal)
      let that = this
      if(newVal>9){
        this.GVerifyTimer = setInterval(function () {
          that.setGVerify();
        }, 200)
      }
    },
  },
  methods: {
    start(){
      let that = this
      authInit(that).then( (res)=> {//初始话，判断是否需要登录开发环境跳转也在这里处理。
        let data = res.success ? res : res.data
        console.log('authInit then', data)
        if(data.success == 301) {//不是登录，通过挑出返回的，有url参数，直接跳转到返回页面
          console.log(301, auth.getUserID())
          this.$router.push({
            name: data.url,
            params: data.params
          })
        } else if(data.success > 0) {//处理/tp/mpCodeLogin.do登录返回值
          console.log('登录成功！')
          this.$router.push({ name: 'orgList' })
        } else {
          let error = data.error
          console.log('登录失败！error msg', data, error)
          // console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
          switch (error.code) {
            case '2': //需要短信验证
            case '6': //需要短信激活
              console.log('data', data.data)
              that.formLabelAlign.name = data?.data?.mobile ?? ''
              ElMessage.error('需要短信验证激活！')
              break
            case '3': //需要注册，调用auth/sendMessageVerificationCodeRegister.do注册通用框架账号
              // ElMessage.error('需要绑定账号')
              break
            case '4': //可能是授权或者code问题，建议重试tt.login
              ElMessage.error('可能是授权或者code问题，建议点击右上角"..."并点击重新进入小程序')
              wx.miniProgram.navigateTo({url: '/pages/start/start?reloadcode=1'})
              break
            case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
              console.log('服务器问题，比如wonderss服务器访问微信服务器异常')
              break
          }
          // that.reFrash()
        }
      }).catch(function (error) {
        console.log('authInit error', error)
        that.reFrash()
      })
    },

    wxBindFun(){
      let paramsData = {
        code: params.code
      }
      tpCodeBindAcc(paramsData).then(res => {
        let data = res.data
        console.log(data)
        if(data.success > 0) {
          console.log('绑定成功！', location.href)


        } else {
          let error = data.error
          console.log('绑定失败！error msg', error)


        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    login (localJsonData){
      console.log('登陆 login 传进来的 ', localJsonData)
      let data = {
        // tgCode:'byteDance',
        tgCode:'byteDance',
        code: localJsonData.code,
        userInfo: JSONBig.stringify(localJsonData.userInfo)
      }
      console.log('callLogin data', data)
      let routerTopage = 'register1'
      callLogin(acceptData).then(res => {
        console.log(' 登陆返回值=', res)
        localJsonData.loginRes = res
        // 登陆接口的处理
        if(res.success>0) { // 已经有私人领域的账号了

          localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))

          if(localJsonData.shareback === '1'){ // 大分享回来的
            console.log('大分享回来的')
            goOffice(localJsonData.oid, 1)

          }else if(localJsonData.shareback === '2'){ // 小分享回来的
            console.log('小分享回来的')
            goOffice(localJsonData.oid, 2)

          }else if(localJsonData.share && localJsonData.share.type ==='1'){ // 大分享进来的
            console.log('大分享进来的 , 需要判断又没有注册过机构，如果注册过 直接跳列表，没有注册过就跳转注册页面')
            // localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
            chargeSuper()

          }
          else if(localJsonData.share && localJsonData.share.type ==='2'){ // 小分享进来的
            console.log('小分享进来的')
            // localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
            chargeSuper('havJoin', localJsonData.share.oid)

          }else{ //
            console.log('啥情况都没有进来的')
            // localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
            router2.push({ name: 'officeList' });

          }

        } else { // 还没有 私人领域的 账号

          let error = res.error
          switch (error.code) {
            case '2': //需要短信验证激活
              ElMessage.error('需要短信验证激活！');
              break
            case '3': //需要注册，调用auth/sendMessageVerificationCodeRegister.do注册通用框架账号

              if(localJsonData.share && localJsonData.share.type ==='1'){ // 大分享进来的
                console.log('大分享进来的')
                localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
                router2.push({ name: 'register1' });

              }else if(localJsonData.share && localJsonData.share.type ==='2'){ // 小分享进来的
                console.log('小分享进来的')
                localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
                router2.push({ name: 'register4' });

              }else{ //
                console.log('啥情况都没有进来的')
                localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
                router2.push({ name: 'register1' });
              }
              break
            case '4': //可能是授权或者code问题，建议重试tt.login
              ElMessage.error('可能是授权或者code问题，建议重试tt.login！');
              break
            case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
              ElMessage.error('服务器问题，比如wonderss服务器访问微信/字节服务器异常');
              console.log('服务器问题，比如wonderss服务器访问微信/字节服务器异常')
              break
          }

        }
      }).catch((err)=>{
        console.log('callLogin err', err)
      })

    },
    jump2(){
      this.$router.push('orgList')
    },
    codeNextBtn(){
      if (this.codeNextFrm.pass !== this.codeNextFrm.pass2) {
        this.codeNextErr = 1
      } else {
        if (this.isPassword(this.codeNextFrm.pass)) {
          this.codeNextErr = 0
          let data = {
            phone: this.formLabelAlign.name,
            password : this.codeNextFrm.pass,
            code: this.formLabelAlign.code
          }
          // resetLoginPassWord(data).then(res => {
          //   console.log('res=', res)
          //   res = res.data
          //   if (res) {
          //     this.codeLoginFun()
          //
          //   } else {
          //     this.$message.error("设置密码失败,请重试！")
          //   }
          //
          //
          // }).catch(err => {
          //   console.log('err=', err)
          // })

        }
        else{
          this.codeNextErr = 2
        }

      }

    },
    isPassword(val) {
      var reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/;
      return reg.exec(val)
    },

    getWebRootShort() {
      return auth.getWebRootShort() ?? auth.webRoot + '/vue/minersFrontEnd/dist/index.html#'
    },
    download() {
      let url = this.getWebRootShort() + '/?from=shortcut'

      saveAs(new Blob(["[InternetShortcut]\n" +
          "URL=" + url +
          "\nIDList=\n" +
          "HotKey=0\n" +
          "IconFile=" + auth.webRoot + "/vue/minersFrontEnd/dist/favicon.ico\n" +
          "IconIndex=0"], {type: "text/x-uri;charset=utf-8"}),
          "Wonderss.url");
      // }
    },
    downloadBat() {
      let url = this.getWebRootShort() + '/?from=shortcut'
      saveAs(new Blob(["@echo off\n" +
      "set source_icon=\"" + auth.webRoot + "/vue/minersFrontEnd/dist/favicon.ico\"\n" +
      "set file_path=\"%ProgramFiles%\\Mozilla Firefox\\firefox.exe\"\n" +
      "if not exist %file_path% (\n" +
      "set file_path=\"%ProgramFiles(x86)%\\Mozilla Firefox\\firefox.exe\"\n" +
      ")\n"+
      "if not exist %file_path% (\n" +
      "for /f delims^=^\"^ tokens^=2 %%a in ('reg query \"HKLM\\Software\\Clients\\StartMenuInternet\" /f *firefox.exe /d /s') do (\n" +
      "  set file_path=\"%%~a\"\n" +
      ")\n"+
      ")\n" +
      "%file_path% -new-tab " + url], {type: "text/plain;charset=utf-8"}), "Wonderss.bat")
    },
    setGVerify(){
      var checkCodeObj = document.getElementById('checkCode');
      if(checkCodeObj){
        verifyCode = new GVerify({
          id: 'checkCode',
          width: '90',
          height: '30'
        });
        clearInterval(this.GVerifyTimer )
      }
    },
    mpCodeLogin (params) {
      let data = {
        appId: this.app.appId,
        tgCode: this.app.tgCode,
        code: params.code
      }
      debugger
      // mpCodeLogin(data).then(res => {
      //   this.$route.query = {};
      //   let data = res.data
      //   console.log(data)
      //   if(data.success > 0) {
      //     console.log('登录成功！')
      //     this.$router.push('orgList')
      //     // let url = auth.webRoot + '/vue/minersFrontEnd/dist/index.html#/orgList'
      //     // location.href = url
      //     console.log('微信登录跳转2', moment(this.startTime).format(), moment().format(),  moment(this.startTime).fromNow())
      //   }
      //   else {
      //     let error = data.error
      //     console.log('登录失败！error msg', error)
      //     // console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
      //     switch (error.code) {
      //       case '2': //超过79天，需要短信验证激活
      //         this.$alert('需要短信验证激活', '提示', {
      //           confirmButtonText: '确定',
      //         }).then(() => {
      //           this.reFrash()
      //         })
      //         break
      //       case '3': //需要注册，微信号未与通用框架账号绑定
      //         // console.log('微信号未与通用框架账号绑定')
      //         this.$message.error('操作失败，该微信未绑定账号！')
      //         this.reFrash()
      //         break
      //       case '4': //可能是授权或者code问题
      //         this.$message.error('授权问题，建议更换其他登录方式！')
      //         this.reFrash()
      //         break
      //       case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
      //         console.log('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
      //         this.$message.error('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
      //         this.reFrash()
      //         break
      //     }
      //   }
      //
      //
      // }).catch(err => {
      //   console.log('err=', err)
      // })

    },
    reFrash(){
      setTimeout(function(){
        location.reload()
      },3000)
    },
    regBackBtn(){
      this.mainCVisble = 1
      this.regVal = ''
    },
    regAccBtn(){
      this.mainCVisble = 2
    },
    changeNav(navIndex){
      this.nav = navIndex
      if(navIndex === 3){
        let protocolStr = location.protocol
        let webRootSubStr = location.href.substring( location.href.indexOf('://')+2)
        console.log('webRootSubStr', webRootSubStr)
        if (location.href.indexOf('/vue/') > 0) {
          this.redirectUriStr = protocolStr + '//' + this.passportHost + webRootSubStr + '/vue/wonderssManager/dist/index.html'
        } else {
          this.redirectUriStr = protocolStr + '//' + this.passportHost + webRootSubStr
        }
        this.state = auth.getToken()
        console.log('redirectUriStr=', this.redirectUriStr)
      }
    },
    loginBtn: function () {
      if(this.passWdloginClickNum > 9){
        let res = verifyCode.validate(this.codeVerify??'');
        if(!res){
          this.$message.error('字符输入不正确，请重新输入！')
        }else{
          this.loginFun()
        }
      }else {
        this.loginFun()
      }
    },
    loginFun(){
      let logonName = this.formLabelAlign.name
      let logonPwd = this.formLabelAlign.pass
      // login(logonName, logonPwd).then(res => {
      //   let loginData = res.data
      //   if (loginData.error != undefined) {
      //     this.passWdloginClickNum++;
      //     this.$alert(loginData.error, '提示', { confirmButtonText: '确定' })
      //   }
      //   else if (auth.isLogInOrg()) {//进入默认或者唯一机构
      //     this.$router.push('homeMy')
      //   } else if (auth.isLogged()){
      //     this.$router.push('orgList')
      //   } else {
      //     this.$alert('登录失败，请联系管理员！', '提示', { confirmButtonText: '确定' })
      //   }
      // }).catch(err => {
      //   console.log('err=', err)
      //   this.$alert('登录失败，请重试！', '提示', { confirmButtonText: '确定' })
      // })
    },
    sendCode: function () {
      let data = {
        phone: this.formLabelAlign.name ,
      }
      if(data.phone?.length !== 11){
        this.$message.error('手机号格式不对')
        return false
      }
      tpRegisterVerificationCode(data).then(res1 => {
        let res = res1.data
        this.senCodePhone = data.phone
        // success 1 验证通过 / success 0 验证失败 抛出失败信息 “手机号长度不对”，“手机号格式不对”， “验证码未过时”。
        console.log('sendMobileCode = ', res)
        console.log('发短信验证码返回值=', res)
        if(res.success) {
          this.$message.success('验证码已发送，请注意查收')
        } else {
          this.$message.error(res.error.message)
        }

      }).catch(err => {
        console.log('sendMobileCode err =', err)
      })
    },
    countdown (seconds) {
      let that = this
      if (seconds > 1){
        seconds--;
        this.sendCodeName = seconds + '秒后可重新获取'
        this.sendCodeDisabled = true
        // 定时1秒调用一次
        setTimeout(function(){
          that.countdown(seconds)
        },1000)
      } else {
        this.sendCodeName = '获取验证码'
        this.sendCodeDisabled = false
      }
    },
    codeLoginBtn:function () {
      let data = {
        phone: this.formLabelAlign.name,
        code: this.formLabelAlign.code,
      }
      if(data.phone.length === 11 && data.code.length === 4  ){
        tpCheckRegisterVerificationCode(data).then(res1 => {
          let res = res1.data
          console.log('verificationCodeStart res =', res)
          let data = res.data
          if (data) {
            this.$message.success(data)
            this.codeNextF()
          } else {
            ElMessage.error(res.errorMsg)
          }
        }).catch(err => {
          console.log('codeLogin err =', err)
          ElMessage.error('您输入的验证码有误！')
        })
      }else{
        ElMessage.error('请录入正确的数据')
      }
    },
    codeLoginFun:function () {
      // codeLogin().then(res => {
      //   console.log('codeLoginFun res=', res)
      //   let logged = false
      //   let user = res.data.user
      //   let error = res.data.error
      //   if(error != undefined){
      //     this.$alert(error, '提示', {
      //       confirmButtonText: '确定',
      //     })
      //   }else {
      //     this.codeNextFData = user
      //     this.codeNextFunOk()
      //   }
      //
      //   // //没有找到，跳转领地页？
      //   // if(!logged) {
      //   //   this.$router.push('errPage')//没有找到，跳转领地页还是报错提示？
      //   // }
      //
      // }).catch(err => {
      //   console.log('codeLoginFun err=', err)
      //   this.$alert(err, '提示', {
      //     confirmButtonText: '确定',
      //   })
      // })

    },
    codeNextF(){
      // this.mainCVisble = 3
      this.$router.push('orgList')
    },
    codeNextFunOk(){
      if(this.codeNextFData){ // 只有一个机构，进入
        this.$router.push('mainPage')
      }
      else {
        this.$router.push('orgList')
      }
    },
  },
}
</script>
