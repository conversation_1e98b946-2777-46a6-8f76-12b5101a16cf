package cn.sphd.miners.modules.recruit.service;

import cn.sphd.miners.modules.recruit.entity.*;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;

public interface RdNoticeService {

    List<RdNotice> getRdNoticeListByOid(Integer oid, Boolean enabled, Date beginDate,Date endDate);

    RdNotice getRdNoticeById(Long id);

    List<RdNoticeItem> getRdNoticeItemsByNoticeId(Long noticeId);

    List<RdNoticeItem> getRdNoticeItemsByNoticePostId(Long noticePostId);

    List<RdNoticeItem> getRdNoticeItemsByNoticeId(Long noticeId,Integer type);

    List<RdNoticeAdditional> getRdNoticeAdditionalsByNoticeId(Long noticeId);

    List<RdNoticePost> getRdNoticePostsByNoticeId(Long noticeId);

    RdNoticePost getRdNoticePostById(Long id);

    List<RdNoticeHistory> getRdNoticeHistoriesByNoticeId(Long noticeId,Integer[] operation);

    void updateRdNotice(RdNotice rdNotice,  User loginUser, Integer operation); //操作:1-增,2-删,3-改,4-启用,5-停用,6-改起始日期,7-改截止日期

    void updateRdNotice(RdNotice rdNotice,  User loginUser);

    List<RdNoticeItemTmpl> getRdNoticeItemTmplList(Integer type,Integer oid);

    void addRdNotice(User user,String json);

    void saveRdNoticeItemTmpl(RdNoticeItemTmpl rdNoticeItemTmpl);

    RdNoticeHistory getRdNoticeHistoryById(Long id);
}
