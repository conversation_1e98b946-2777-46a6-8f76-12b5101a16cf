package cn.sphd.miners.modules.recruit.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

/**
 * 招聘启事项目表
 * <AUTHOR>
 * @date 20230506
 **/
@Entity
@Table(name = "t_rd_notice_item")
public class RdNoticeItem {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    @Column
    private Integer org;//机构ID

    @Column(name="notice" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticeId;//启事ID

    @Column(name = "notice_post" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticePostId;//招聘岗位ID

    @Column(name = "item_code",length = 20)
    private String itemCode; //项目代码，自编码区分项目

    @Column(name = "item_name",length = 50)
    private String itemName; //项目名称

    @Column
    private Integer orders;//排序

    @Column
    private Integer type;//类型: 1招聘启示项目模板，  2招聘岗位项目模板

    @Column(name = "is_system")
    private Boolean isSystem=true;//是否系统自带:1-是,0-否

    @Column(name = "is_required")
    private Boolean isRequired=false;//是否必须:1-是,0-否

    @Column(name = "is_unitive")
    private Boolean isUnitive=true;//是否统一设置:1-是,0-否

    @Column(name = "is_show")
    private Boolean isShow=true;//是否显示:1-是,0-否

    @Column(name = "content",length = 255)
    private String content; //内容

    @Column(name = "file_path",length = 255)
    private String filePath; //文件/图片存储地址

    @Column(name = "begin_time")
    private Date beginTime;//'起始时间'

    @Column(name = "end_time")
    private Date endTime;//'截止时间'

    @Column(name = "take_date")
    private Date takeDate;//发生时间

    @Column(name = "take_value")
    private Integer takeValue;//发生值(数字型设置时用)

    @Column(name = "take_object",length = 255)
    private String takeObject; //text  comment '对象型时存json,联系人-{contacter:*,telephone:*,email:*,webchat:*}'

    @Column(length = 255)
    private String memo; //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"   , nullable=true , unique=false)
    private Integer operation; //操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNotice rdNotice ;//启事主表

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice_post", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNoticePost rdNoticePost ;//招聘岗位表



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public Long getNoticePostId() {
        return noticePostId;
    }

    public void setNoticePostId(Long noticePostId) {
        this.noticePostId = noticePostId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getSystem() {
        return isSystem;
    }

    public void setSystem(Boolean system) {
        isSystem = system;
    }

    public Boolean getRequired() {
        return isRequired;
    }

    public void setRequired(Boolean required) {
        isRequired = required;
    }

    public Boolean getUnitive() {
        return isUnitive;
    }

    public void setUnitive(Boolean unitive) {
        isUnitive = unitive;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getTakeDate() {
        return takeDate;
    }

    public void setTakeDate(Date takeDate) {
        this.takeDate = takeDate;
    }

    public Integer getTakeValue() {
        return takeValue;
    }

    public void setTakeValue(Integer takeValue) {
        this.takeValue = takeValue;
    }

    public String getTakeObject() {
        return takeObject;
    }

    public void setTakeObject(String takeObject) {
        this.takeObject = takeObject;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public RdNotice getRdNotice() {
        return rdNotice;
    }

    public void setRdNotice(RdNotice rdNotice) {
        this.rdNotice = rdNotice;
    }

    public RdNoticePost getRdNoticePost() {
        return rdNoticePost;
    }

    public void setRdNoticePost(RdNoticePost rdNoticePost) {
        this.rdNoticePost = rdNoticePost;
    }

    public Boolean getShow() {
        return isShow;
    }

    public void setShow(Boolean show) {
        isShow = show;
    }
}
