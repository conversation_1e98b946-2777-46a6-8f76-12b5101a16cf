// creator : hxz 2018-07-25 判断当前的登录的角色
import auth from '@/sys/auth'


export function chargeRole(roleStr) { // roleStr 值：“超管”, “总务”, “财务”, “销售”, “超级浏览者”, “普通员工”,  “会计”,  “代理会计”,  “代理小会计”,  “小超管”,
    var roleCodeList = [
        {"name": "超管", "code": "super"},
        {"name": "总经理", "code": "smallSuper"},
        {"name": "总务", "code": "general"},
        {"name": "财务", "code": "finance"},
        {"name": "销售", "code": "sale"},
        {"name": "超级浏览者", "code": "browse"},
        {"name": "浏览者", "code": "browse"},
        {"name": "普通员工", "code": "staff"},
        {"name": "会计", "code": "accounting"},
        {"name": "代理会计", "code": "agentAccounting"},
        {"name": "代理小会计", "code": "agentSmallAccounting"},
        {"name": "小超管", "code": "smallSuper"}
    ];
    // var roleCode = sphdSocket.user.roleCode ;
    var roleCode = auth.getUser().roleCode ;
    for (var i = 0; i < roleCodeList.length; i++) {
        if (roleCodeList[i]["name"] == roleStr) {
            if (roleCode == roleCodeList[i]["code"]) {
                return true;
            } else {
                return false;
            }
        }
    }
}

export function handleNull(str) {
    var result = str === 'null' || str === null || str === undefined || str ==  "\"null\"" ? '':str;
    return result;
}
// create:hxz 2022-03-07 格式化 账号
export function formatAccount(account){
    let accountStr = `****`
    if(account.length >3){
        accountStr += account.substr(account.length-4,4)
    }else{
        let n = 4-account.length ;
        for(let i = 0 ; i < n; i++){
            accountStr += '*'
        }
        accountStr += account
    }
    return accountStr
}
