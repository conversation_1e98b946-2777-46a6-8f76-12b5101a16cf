package cn.sphd.miners.modules.personal.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.system.dto.UserLoginDto;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 个人中心模块中的登录设置子模块
 * Created by Administrator on 2017/9/6.
 */
@Controller
@RequestMapping("/loginSettings")
public class LoginSettingsController {

    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;

    /**
     *<AUTHOR>
     *@date 2017/9/7 9:40
     *跳转登录设置页面
    */
    @RequestMapping("/toLoginSettings.do")
    public String toLoginSettings(){
        return "/user/loginSet";
    }

    /**
     *<AUTHOR>
     *@date 2017/9/7 16:26
     *查询用户的所有机构信息
    */
    @ResponseBody
    @RequestMapping("/getAllOrgsByUserId.do")
    public JsonResult getAllOrgs(User user, PageInfo pageInfo){
        Map<String,Object> map=new HashMap<>();
        List<Organization> organizationList = new ArrayList<>();
        if (user!=null){
//            List<User> users = userService.getUsers(user.getMobile(), user.getLogonPwd());
            List<UserLoginDto> users=userService.getUserList(user.getAccId(),pageInfo);//  /2024/4/10  lixu 优化加分页
            List<Integer> oids=new ArrayList<>();
            Map<Integer,Boolean> oidMap=new HashMap<>();
            for (UserLoginDto u:users){
                oids.add(u.getOid());
                oidMap.put(u.getOid(),u.isDefault());
            }
            organizationList=orgService.getOrgByOids(oids);
            for (Organization o:organizationList) {
                if (oidMap.get(o.getId())){
                    o.setDefaulOrg(2);
                }else {
                    o.setDefaulOrg(1);
                }
            }
        }
        map.put("orgs",organizationList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2017/9/8 9:40
     *设置(修改)默认机构
     * state 是否默认机构 1-否 2-是
    */
    @ResponseBody
    @RequestMapping("/updateDefaulOrg.do")
    public void updateDefaulOrg(Integer state,Integer orgId, HttpServletResponse response,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
//        User user = (User) session.getAttribute("user");
//        user=userService.getUserByID(user.getUserID());
        if (orgId!=null && state!=null && user!=null){
            if (state==1){
//                User user3 = userService.getUserByOidAndPhone(user.getOid(),user.getMobile());
                User user3 = userService.getUserByOidAndPhone(orgId,user.getMobile());
                if (user3!=null){
                    user3.setDefault(false);
                    userService.updateUser(user3);
                }
                map.put("state",1);  //设置成功
            }else {
//                List<User> users = userService.getUsers(user.getMobile(),user.getLogonPwd());
                List<User> users=userService.getUserList(user.getAccId());// 2021/7/19 李旭改

                if (users.size()>0){
                    for (User user2:users) {
                        user2.setDefault(false);
                        userService.updateUser(user2);
                    }
                }
//                User user3 = userService.getUserByOidAndPhone(user.getOid(),user.getMobile());
                User user3 = userService.getUserByOidAndPhone(orgId,user.getMobile());
                if (user3!=null){
                    user3.setDefault(true);
                    userService.updateUser(user3);
                }
                map.put("state",1);  //设置成功
            }
        }else {
            map.put("state",0);   //设置失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

}
