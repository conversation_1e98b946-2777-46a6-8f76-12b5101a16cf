<template>
  <!--  <p>转账支票和承兑汇票主页</p>-->
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showBack=true @myBackFun="handleCustomBack" :showClose=true
                  @mycloseFun="handleCustomClose"
                  :runMyBackFun=true>
      </PageHeader>
    </div>
    <div class="h">
      <div @click="rou(0)" :class="{ 'padding_bottom_green': isActive==2, 'padding_bottom_gray': isActive==1 }">
        收到的承兑汇票
      </div>
      <div @click="rou(1)" :class="{'padding_bottom_green': isActive==1, 'padding_bottom_gray': isActive==2} ">收到的转账支票
      </div>
    </div>
    <div>
      <div class="spaceBetween ty-fff margin_top_16">
        <p>尚在手中的</p>
        <div class="flex-d">
          <p>{{ available.num }}张</p>
          <p>{{ this.formatCurrency(available.amount) }}</p>
        </div>
        <div class="ic" @click="cmo(0)">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </div>
      <div class="spaceBetween ty-fff margin_top_16">
        <p>更多数据</p>
        <div @click="hide(0)" class="ic">
          <el-icon style="font-size: 20px" v-show="list1B==false">
            <arrow-down/>
          </el-icon>
          <el-icon v-show="list1B" style="font-size: 20px">
            <arrow-up/>
          </el-icon>
        </div>
      </div>
      <div class="margin_top_16" v-show="list1B==true">
        <div class="spaceBetween ty-fff">
          <p>今年收到的</p>
          <div class="flex-d">
            <p>{{ yearTime.num }}张</p>
            <p>{{ this.formatCurrency(yearTime.amount) }}</p>
          </div>
          <div class="ic" @click="cmo(1)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </div>
        </div>
        <div class="spaceBetween cai-liGray">
          <p>去年收到的</p>
          <div class="flex-d">
            <p>{{ lastYearTime.num }}张</p>
            <p>{{ this.formatCurrency(lastYearTime.amount) }}</p>
          </div>
          <div class="ic" @click="cmo(2)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </div>
        </div>
        <div class="spaceBetween ty-fff">
          <p>前年收到的</p>
          <div class="flex-d">
            <p>{{ beforeYearTime.num }}张</p>
            <p>{{ this.formatCurrency(beforeYearTime.amount) }}</p>
          </div>
          <div class="ic" @click="cmo(3)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </div>
        </div>
        <div class="cai-liGray" style="height: 16px"></div>
        <div class="dateP ty-fff">
          <span style="margin-right: 10px">更早的年份</span>
          <p @click="datePicker=!datePicker">选择年份</p>
        </div>
      </div>
    </div>
    <DatePicker @cancel="handlecancel" type="year" @confirm="handConfirm" v-show="datePicker"/>
  </div>

</template>

<script>
import {getReturnNum} from '@/api/financeBill'
import DatePicker from '@/components/DatePicker.vue';
import {formatCurrency} from '@/utils/utils'

export default {
  name: "cmoHome",
  components: {
    DatePicker
  },
  data() {
    return {
      tit: "财会",
      isActive: 2,
      list1B: false,
      list: {},
      available: {},
      yearTime: {},
      lastYearTime: {},
      beforeYearTime: {},
      datePicker: false,
      formatCurrency: formatCurrency,
    }
  },
  mounted() {
    this.getBill()
  },
  methods: {
    getBill() {
      const params = {type: this.isActive}
      getReturnNum(params).then(res1 => {
        this.available = res1.data.data.available
        this.yearTime = res1.data.data.yearTime
        this.lastYearTime = res1.data.data.lastYearTime
        this.beforeYearTime = res1.data.data.beforeYearTime
      }).catch(error => {
        console.error('Error:', error);
      });
    },
    handlecancel(data) {
      this.datePicker = data;
    },
    handConfirm(result) {
      this.datePicker = false
      this.cmo(4, result.year)
    },

    handleCustomBack() {
      this.$router.push("/caikuai");
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    rou(index) {
      if (index == 0) {
        this.isActive = 2
        this.list1B = false
        this.getBill()
      } else if (index == 1) {
        this.isActive = 1
        this.list1B = false
        this.getBill()
      }
    },
    hide(index) {
      if (index == 0) {
        this.list1B = !this.list1B
      } else if (index == 1) {
        this.list2B = !this.list2B
      }
    },
    cmo(index, result) {
      let Bill = {index, result}
      localStorage.setItem('Bill', JSON.stringify(Bill))
      if (this.isActive == 2) {
        this.$router.push({path: "/moneyOrderList"})
      } else if (this.isActive == 1) {
        this.$router.push({path: "/cftList"})
      }
    },
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.box {
  width: 100%;
}

.h {
  display: flex;
  text-align: center;
  padding: 0 0;
  background-color: #ffffff;

  div {
    font-size: 14px;
    width: 50%;
    line-height: 35px;
  }
}

.dateP {
  display: flex;
  align-items: center;
  padding: 10px 15px !important;
  line-height: 35px;

  > * {
    width: 30%;
  }

  > :nth-child(2) {
    text-align: center;
    border: 1px solid $tyColorGray;
    color: $tyColorGray;
  }
}

.spaceBetween {
  height: 46px;
  padding: 4px 15px;

  .flex-d {
    width: 30%;
  }
}

.ic {
  width: 20%;
}
</style>