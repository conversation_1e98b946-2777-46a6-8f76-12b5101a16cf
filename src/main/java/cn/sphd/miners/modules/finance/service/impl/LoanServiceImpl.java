package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.FinanceUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.*;
import cn.sphd.miners.modules.system.dao.OrgDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

/**
 * Created by Administrator on 2018/12/20.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class LoanServiceImpl extends BaseServiceImpl implements LoanService{

    @Autowired
    FinanceReturnDao financeReturnDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    AccountDetailHistoryDao accountDetailHistoryDao;
    @Autowired
    FinanceChequeDetailDao financeChequeDetailDao;
    @Autowired
    UserDao userDao;
    @Autowired
    OrgDao orgDao;
    @Autowired
    FinanceAccountBillHistoryDao financeAccountBillHistoryDao;
    @Autowired
    FinanceReturnHistoryDao financeReturnHistoryDao;
    @Autowired
    FinanceChequeDetailHistoryDao financeChequeDetailHistoryDao;
//    @Autowired
//    TBorrowRepaymentMapper repaymentMapper;
    @Autowired
    PoLoanDao poLoanDao;

    @Autowired
    AccountService accountService;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    DataService dataService;

    /**
     * 常规借款收入（公司借来的款）
     * @param userId  操作人id
     * @param business  借款id
     * @param method  本金型式（1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐）
     * @param money  金额
     * @param receiveAccountDate  到账日期（收到支票日期、付款日期  yyyy-MM-dd HH:mm:ss）
     * @param accountId   收款银行账户id
     * @param returnNo   支票号（汇票号）
     * @param expireDate   支票到期日（汇票到期日 yyyy-MM-dd HH:mm:ss）
     * @param originalCorp  出具支票单位（原始出具汇票单位）
     * @param bankName  出具支票银行（出具汇票银行）
     * @param memo  备注、说明
     * @param loanType  ： 借贷类型 1-借入(默认1),2-借出'
     * @param operatorName：经手人（如果是借入，此经手人为收款经手人；如果为借出，此经收人为付款经手人）
     * @param partnerName：合作方经手人姓名（如果是借入，此经手人为付款经手人；如果为借出，此经收人为收款经手人）
     * @param withinOrAbroad：支出转账支票时使用，1-内部 0-外部
     * @param oppositeCorp 收款单位
     * @param payer 付款单位
     * @return
     */
    @Override
    public Map<String,Object> addLoanCredit(Integer userId,Integer business,String method, Double money,Date receiveAccountDate,Integer accountId,
     String returnNo,Date expireDate,String originalCorp,String bankName,String memo,String loanType,String operatorName,String partnerName,
                                            String withinOrAbroad,Date paymentDate,Integer returnId,String oppositeCorp,String payer) {
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态
        User user = userDao.get(userId);
        Integer oid = user.getOid();
        Organization org = orgDao.get(oid);
        map.put("status",status);
        if("1".equals(method)){
            receiveAccountDate = paymentDate;  //method=1收款日期
        }
        String summary = NewDateUtils.dateToString(receiveAccountDate,"yyyy年MM月dd日")+"公司向"+payer+"借款";  //摘要   （此默认是收入的）
        if ("2".equals(loanType)){
            summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+oppositeCorp+"向公司借款";  //支出的（借出）
        }
        if ("1".equals(method)){   //单纯现金
            addCashCredit(business,money,oid,user,memo,loanType,paymentDate,operatorName,partnerName,"0101",summary,map);  //收入、支出 现金
        }else if ("3".equals(method)||"4".equals(method)){  //转账支票或承兑汇票
            addReturn(org,user,accountId,business,method,new BigDecimal(money),receiveAccountDate,returnNo,expireDate,originalCorp,bankName,memo,loanType,operatorName,partnerName,withinOrAbroad,paymentDate,returnId,"0101",null,payer,summary,map);  //收入的转账支票或承兑汇票
        }else if ("5".equals(method)){  //银行转账
            addBank(org,user,business,method,money,receiveAccountDate,accountId,memo,loanType,operatorName,partnerName,"0101",null,null,null,summary,map);  //收入的转账支票或承兑汇票
        }
        return map;
    }

    /**
     * 常规借款的支出（公司借出去的款）
     * @param userId  操作人id
     * @param business  借款id
     * @param method  本金型式（1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐）
     * @param money  金额
     * @param receiveAccountDate  到账日期（收到支票日期、付款日期  yyyy-MM-dd HH:mm:ss）
     * @param accountId   收款银行账户id
     * @param returnNo   支票号（汇票号）
     * @param expireDate   支票到期日（汇票到期日 yyyy-MM-dd HH:mm:ss）
     * @param originalCorp  出具支票单位（原始出具汇票单位）
     * @param bankName  出具支票银行（出具汇票银行）
     * @param memo  备注、说明
     * @param loanType  ： 借贷类型 1-借入(默认1),2-借出'
     * @param operatorName：经手人（如果是借入，此经手人为收款经手人；如果为借出，此经收人为付款经手人）
     * @param partnerName：合作方经手人姓名（如果是借入，此经手人为付款经手人；如果为借出，此经收人为收款经手人）
     * @param withinOrAbroad：支出转账支票时使用，1-内部 0-外部
     * @return
     */
    @Override
    public Map<String, Object> addLoanDebit(Integer userId, Integer business, String method, Double money, Date receiveAccountDate, Integer accountId, String returnNo,
                                            Date expireDate, String originalCorp, String bankName, String memo, String loanType, String operatorName, String partnerName,
                                            String withinOrAbroad, Date paymentDate, Integer returnId, String oppositeAccount,String oppositeBankno,String oppositeBankcode,String oppositeCorp,String payer) {
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态
//        Integer oid = (Integer) session.getAttribute("oid");
        User user = userDao.get(userId);
        Integer oid = user.getOid();
        Organization org = orgDao.get(oid);
        map.put("status",status);
        String summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+oppositeCorp+"向公司借款";
        if ("1".equals(method)){   //单纯现金
            addCashCredit(business,money,oid,user,memo,loanType,paymentDate,operatorName,partnerName,"0103",summary,map);  //收入、支出 现金
        }else if ("3".equals(method)||"4".equals(method)){  //转账支票或承兑汇票
            addReturn(org,user,accountId,business,method,new BigDecimal(money),receiveAccountDate,returnNo,expireDate,originalCorp,bankName,memo,loanType,operatorName,partnerName,withinOrAbroad,paymentDate,returnId,"0103",oppositeCorp,payer,summary,map);  //收入的转账支票或承兑汇票
        }else if ("5".equals(method)){  //银行转账
            addBank(org,user,business,method,money,paymentDate,accountId,memo,loanType,operatorName,partnerName,"0103",oppositeAccount,oppositeBankno,oppositeBankcode,summary,map);  //收入的转账支票或承兑汇票
        }
        return map;
    }

    //loanType 1-借入(收入) 2-借出(支出)
    private Map<String,Object> addCashCredit(Integer business, Double money,Integer oid,User user,String memo,String loanType,Date receiveAccountDate,String operatorName,String partnerName,String businessType,String summary,Map<String,Object> map){
        //单纯现金
        FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(oid, 1);//备用金
        BigDecimal debitBalance = financeAccount.getBalance().subtract(new BigDecimal(money));  //若是借出(支出)后的账户余额

        if (0 == financeAccount.getAccountStatus()) {
            map.put("status","2");//此账户已被冻结，不允许操作
            return map;
        }else if ("2".equals(loanType) && debitBalance.doubleValue()<0){
            map.put("status","3");//账户余额不足
            return map;
        }
        AccountPeriod accountPeriodMonth = accountService.getPeroidByType(oid, financeAccount.getAccountType(), financeAccount.getId(), 1);  //1：月结
        if (accountPeriodMonth == null) {
            try {
                FinanceUtils.addPeroid(financeAccount.getId(), user, accountService);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        accountPeriodMonth = accountService.getPeroidByType(oid, financeAccount.getAccountType(), financeAccount.getId(), 1);//再查一遍月结
        AccountPeriod accountPeriodDay = accountService.getPeroidByType(oid, financeAccount.getAccountType(), financeAccount.getId(), 2);//找到日结

        AccountDetail accountDetail = new AccountDetail();
        accountDetail.setCreateDate(new Date());
        accountDetail.setCreator(user.getUserID());
        accountDetail.setCreateName(user.getUserName());
        accountDetail.setOrg(orgDao.get(oid));
        accountDetail.setGenre("2");  //类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
//        accountDetail.setAccount(accountPeriodMonth.getId().toString());
        accountDetail.setAuditDate(new Date());
        accountDetail.setMethod("1");//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
        accountDetail.setMemo(memo);
        accountDetail.setBillAmount(new BigDecimal(money));  //票面金额
        accountDetail.setFid(financeAccount.getId().toString());
        accountDetail.setAccountId(financeAccount);//账户外键
        accountDetail.setAuditorName(operatorName);  //经手人
        accountDetail.setPartnerName(partnerName); //合作方经手人
        accountDetail.setReceiveAccountDate(receiveAccountDate);//到账日期
        if (financeAccount.getAccount()!=null){
            accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
        }else {
            accountDetail.setAccountBank(financeAccount.getBankName());
        }
        accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
        accountDetail.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
        accountDetail.setSource("2");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)
        accountDetail.setBusinessDate(receiveAccountDate);  //业务发生日期
        accountDetail.setBusiness(business);  //业务号
        accountDetail.setSummary(summary);
        accountDetail.setPurpose(summary);
        accountDetail.setBillDate(new Date());

        if ("1".equals(loanType)){  //借入(收入)
            accountDetail.setCredit(new BigDecimal(money));
            accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setBusinessType(businessType);  //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款，0103-常规借款借出 ，0104-常规借款收款

            BigDecimal credit = accountPeriodMonth.getCredit().add(accountDetail.getCredit());
            BigDecimal balance = accountPeriodMonth.getBalance().add(accountDetail.getCredit());
            financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriodMonth.setCredit(credit);
            accountPeriodMonth.setBalance(balance);

            credit = accountPeriodDay.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : accountPeriodDay.getCredit().add(accountDetail.getCredit());
            balance = accountPeriodDay.getBalance().add(accountDetail.getCredit());
            accountPeriodDay.setCredit(credit);
            accountPeriodDay.setBalance(balance);
        }else if ("2".equals(loanType)){  //借出(支出)
            accountDetail.setDebit(new BigDecimal(money));
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setBusinessType(businessType);  //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款，0103-常规借款借出 ，0104-常规借款收款

            BigDecimal debit = accountPeriodMonth.getDebit().add(accountDetail.getDebit());
            BigDecimal balance = accountPeriodMonth.getBalance().subtract(accountDetail.getDebit());
            financeAccount.setDebit(financeAccount.getDebit().subtract(accountDetail.getDebit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().subtract(accountDetail.getDebit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriodMonth.setDebit(debit);
            accountPeriodMonth.setBalance(balance);

            BigDecimal debitDay = accountPeriodDay.getDebit() == null ? new BigDecimal(0).add(accountDetail.getDebit()) : accountPeriodDay.getDebit().add(accountDetail.getDebit());
            balance = accountPeriodDay.getBalance().subtract(accountDetail.getDebit());
            accountPeriodDay.setDebit(debitDay);
            accountPeriodDay.setBalance(balance);
        }

        accountPeroidDao.update(accountPeriodMonth);//月结
        accountPeroidDao.update(accountPeriodDay);//日结
        financeAccountDao.update(financeAccount);//账户
        accountDetailDao.save(accountDetail);//明细
//        map.put("detailId",accountDetail.getId());
        map.put("status","1");  //添加成功
        return map;
    }

    private Map<String,Object> addReturn(Organization org,User user,Integer accountId,Integer business, String method, BigDecimal money,
        Date receiveAccountDate,String returnNo, Date expireDate, String originalCorp, String bankName, String memo,String loanType,String operatorName,
        String partnerName,String withinOrAbroad,Date paymentDate,Integer returnId,String businessType,String oppositeCorp,String payer,String summary,Map<String,Object> map) {
        //回款票据 的转账支票
        if ("1".equals(loanType)){   //1-借入(收入)
            FinanceReturn fr = new FinanceReturn();
            fr.setReturnNo(returnNo);//支票号码
            fr.setSummary(summary);  //摘要
            fr.setPurpose(summary);  //用途
            if ("3".equals(method)) {
                fr.setType(1);//1-转账支票 2-承兑汇票
            } else {
                fr.setType(2);//1-转账支票 2-承兑汇票
            }
            fr.setCategory("2");//1-贷款,2-借款,3-投资款,4-废品,5-其他
            fr.setAmount(money);//金额
            fr.setBillAmount(money);//票面金额
            fr.setPayer(payer);  //付款单位
            fr.setOperatorName(operatorName);//经手人
            fr.setPartnerName(partnerName);
            fr.setOriginalCorp(originalCorp);//出具票据单位
            fr.setBankName(bankName);//出具票据银行
            fr.setExpireDate(expireDate);//发票到期日期
            fr.setReceiveDate(receiveAccountDate);//收到票据日期
            fr.setMemo(memo);//备注
            fr.setState("1");//1-有效,2-存入银行,3-作废
            fr.setCreateDate(new Date());
            fr.setCreateName(user.getUserName());
            fr.setCreator(user.getUserID());
            fr.setOrg(org);
            financeReturnDao.save(fr);

            //支票明细
            FinanceAccountBill f = new FinanceAccountBill();
            f.setAmount(money);//金额
            f.setBillAmount(money);  //票面金额
            f.setBillNo(returnNo);//支票号码
            f.setFinanceReturn(fr);//回款票据外键
            f.setOrg(org);//机构
            f.setOperatorName(user.getUserName());//经手人
            f.setMemo(memo);//备注
            f.setAccountantStatus("1");
            f.setBusiness(business);
            f.setState("0");   //状态：0-未入账，1-重新入账，2-已入账
            f.setSource("2");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)
            f.setModityStatus("2");
            f.setType("1");//1-收入，2-支出
            f.setBusinessType(businessType); //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款，0103-常规借款借出，0104-常规借款收款
            f.setOppositeCorp(oppositeCorp);
            f.setSummary(summary);
            f.setPurpose(summary);
            f.setCreateDate(new Date());
            f.setCreateName(user.getUserName());
            f.setCreator(user.getUserID());
            f.setBillDate(new Date());
            financeAccountBillDao.save(f);
        }else if ("2".equals(loanType)){  //2-借出（支出）
            if (receiveAccountDate==null){
                receiveAccountDate = paymentDate;
            }
             addReturnDebit(user,business,method,money,paymentDate,withinOrAbroad,returnId,accountId,receiveAccountDate,partnerName,operatorName,expireDate,businessType,map,oppositeCorp,payer,summary);
        }
        map.put("status","1");  //添加成功
        return map;
    }


    //String loanType 借贷类型:1-借入(默认1),2-借出'
    private Map<String, Object> addBank(Organization org,User user,Integer business, String method, Double money, Date receiveAccountDate,Integer accountId,String memo,String loanType,
                                        String operatorName,String partnerName,String businessType,String oppositeAccount,String oppositeBankno,String oppositeBankcode,String summary,Map<String,Object> map){
        FinanceAccount financeAccount = financeAccountDao.get(accountId);
        BigDecimal debitBalance = financeAccount.getBalance().subtract(new BigDecimal(money));  //若是借出(支出)后的账户余额

        if (0 == financeAccount.getAccountStatus()) {
            map.put("status","2");//此账户已被冻结，不允许操作
            return map;
        }else if (debitBalance.doubleValue()<0 && "2".equals(loanType)){
            map.put("status","3");//账户余额不足
            return map;
        }
        AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());//再查一遍月结
        AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

        AccountDetail accountDetail = new AccountDetail();
        accountDetail.setCreateDate(new Date());
        accountDetail.setCreator(user.getUserID());
        accountDetail.setCreateName(user.getUserName());
        accountDetail.setOrg(org);
        accountDetail.setAuditorName(operatorName);
        accountDetail.setPartnerName(partnerName);
        accountDetail.setAuditor(user.getUserID());
        accountDetail.setAuditDate(new Date());
//        accountDetail.setAccount(accountPeriodMonth.getId().toString());
        accountDetail.setGenre("2");  //类别  2-借款
        accountDetail.setMethod(method);
        accountDetail.setMemo(memo);
//        accountDetail.setOppositeCorp(oppositeCorp);//收款单位
        accountDetail.setFid(financeAccount.getId().toString());
        accountDetail.setAccountId(financeAccount);//账户外键
        accountDetail.setReceiveAccountDate(receiveAccountDate);//到账日期
        accountDetail.setBillAmount(new BigDecimal(money));  //票面金额
        accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
        accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
        accountDetail.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
        accountDetail.setSource("2");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)
        accountDetail.setBusinessDate(receiveAccountDate);  //业务发生日期
        accountDetail.setBusiness(business);  //业务号
        accountDetail.setOppositeAccount(oppositeAccount);   //账户名称
        accountDetail.setOppositeBankno(oppositeBankno);   //开户行
        accountDetail.setOppositeBankcode(oppositeBankcode);  //银行账号
        accountDetail.setSummary(summary);
        accountDetail.setPurpose(summary);
        accountDetail.setBillDate(new Date());

        if ("1".equals(loanType)){  //借入(收入)
            accountDetail.setCredit(new BigDecimal(money));
            accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setBusinessType(businessType);  //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款，0103-常规借款借出 ，0104-常规借款收款

            BigDecimal credit = accountPeriodMonth.getCredit().add(accountDetail.getCredit());
            BigDecimal balance = accountPeriodMonth.getBalance().add(accountDetail.getCredit());
            financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriodMonth.setCredit(credit);
            accountPeriodMonth.setBalance(balance);

            credit = accountPeriodDay.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : accountPeriodDay.getCredit().add(accountDetail.getCredit());
            balance = accountPeriodDay.getBalance().add(accountDetail.getCredit());
            accountPeriodDay.setCredit(credit);
            accountPeriodDay.setBalance(balance);
        }else if ("2".equals(loanType)){  //借出(支出)
            accountDetail.setDebit(new BigDecimal(money));
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setBusinessType(businessType);  //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款，0103-常规借款借出 ，0104-常规借款收款

            BigDecimal debit = accountPeriodMonth.getDebit().add(accountDetail.getDebit());
            BigDecimal balance = accountPeriodMonth.getBalance().subtract(accountDetail.getDebit());
            financeAccount.setDebit(financeAccount.getDebit().subtract(accountDetail.getDebit()));//计入账户收入
            financeAccount.setBalance(financeAccount.getBalance().subtract(accountDetail.getDebit()));//从余额加上收入
            accountDetail.setBalance(financeAccount.getBalance());
            accountPeriodMonth.setDebit(debit);
            accountPeriodMonth.setBalance(balance);

            BigDecimal debitDay = accountPeriodDay.getDebit() == null ? new BigDecimal(0).add(accountDetail.getDebit()) : accountPeriodDay.getDebit().add(accountDetail.getDebit());
            balance = accountPeriodDay.getBalance().subtract(accountDetail.getDebit());
            accountPeriodDay.setDebit(debitDay);
            accountPeriodDay.setBalance(balance);
        }

        accountPeroidDao.update(accountPeriodMonth);//月结
        accountPeroidDao.update(accountPeriodDay);//日结
        financeAccountDao.update(financeAccount);//账户
        accountDetailDao.save(accountDetail);//明细
//        map.put("detailId",accountDetail.getId());
        map.put("status","1");  //添加成功
        return map;
    }

    /**
     * @param userId  操作人id
     * @param business  借款id
     * @param businessHistory  借款历史id
     * @param previousId  借款历史修改前id
     * @param method  本金型式（1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐）   1.55版本中，本金型式不可修改
     * @param money  金额
     * @param receiveAccountDate  到账日期（收到支票日期  yyyy-MM-dd HH:mm:ss）
     * @param accountId   收款银行账户id
     * @param returnNo   支票号（汇票号）
     * @param expireDate   支票到期日（汇票到期日  yyyy-MM-dd HH:mm:ss）
     * @param originalCorp  出具支票单位（原始出具汇票单位）
     * @param bankName  出具支票银行（出具汇票银行）
     * @param memo  备注、说明
     * @return
     */
    @Override
    public Map<String,Object> updateLoanCredit(Integer userId, Integer business,Integer businessHistory,Integer previousId, String method, Double money,
                                               Date receiveAccountDate, Integer accountId, String returnNo, Date expireDate, String originalCorp,
                                               String bankName, String memo,String operatorName,String partnerName,Date paymentDate,Integer type,String oppositeCorp,String payer){
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态  0-失败 1-成功 2-选择的账户被冻结 3-账户余额不够冲减
        User user = userDao.get(userId);
        Organization org = orgDao.get(user.getOid());
        map.put("status",status);
        String businessType ="";
        String summary = "";
        switch (type) {
            case 1:
                businessType="0101";  //借入
                if(receiveAccountDate==null){
                    receiveAccountDate = paymentDate;
                }
                summary = NewDateUtils.dateToString(receiveAccountDate,"yyyy年MM月dd日")+"公司向"+payer+"借款";  //摘要
                break;
            case 2:
                businessType="0102";   //还款
                summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+"公司向"+oppositeCorp+"付款";
                break;
            case 3:
                businessType="0103";  //借出
                summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+oppositeCorp+"向公司借款";  //支出的（借出）
                break;
            case 4:
                businessType="0104";   //收款
                summary = NewDateUtils.dateToString(receiveAccountDate,"yyyy年MM月dd日")+"公司向"+payer+"借款";  //收入的（借出）
                break;
        }
        if (business!=null) {
           if ("1".equals(method)) {   //单纯现金修改
               map = updateCashCredit(business,businessHistory,previousId,BigDecimal.valueOf(money), org, user, memo,operatorName,partnerName,paymentDate,type,summary,map);  //收入现金
           } else if ("3".equals(method) || "4".equals(method)) {  //转账支票或承兑汇票
               map = updateReturn(user,business,businessHistory,previousId,BigDecimal.valueOf(money),receiveAccountDate,returnNo,expireDate,originalCorp,bankName,memo,operatorName,partnerName,businessType,map,oppositeCorp,payer,summary);  //收入的转账支票或承兑汇票
           } else if ("5".equals(method)) {  //银行转账
               map = updateBank(user,business,businessHistory,previousId,BigDecimal.valueOf(money),receiveAccountDate,accountId,memo,partnerName,type,summary,map);  //银行转账
           }
        }
        return map;
    }

    private Map<String,Object> updateCashCredit(Integer business,Integer businessHistory,Integer previousId,BigDecimal money,Organization org,User user,String memo,String operatorName,String partnerName,Date paymentDate,Integer type,String summary,Map<String,Object> map){
         FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(org.getId(), 1);//备用金
        AccountDetail accountDetailOld = getAccountDetailByBusiness(business,type,null);
        if (money.compareTo(accountDetailOld.getCredit())!=0) {    //金额修改了
             if (0 == financeAccount.getAccountStatus()) {
                map.put("status","2");//此账户已被冻结，不允许操作
                return map;
            }
            AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());  //1：月结
            AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

            //当前余额=账户的余额-原来的收入+修改后的收入
            BigDecimal balance = financeAccount.getBalance().subtract(accountDetailOld.getCredit()).add(money);  //余额是否为0
             if (balance.doubleValue()>=0.00){    //够冲减
                 //账务明细表里添加一条新数据(金额为负)
                 financeAccountService.saveAccountDetail(user,org,financeAccount,balance,accountDetailOld.getCredit().multiply(new BigDecimal(-1)),"3",accountDetailOld.getSummary(),"1",operatorName,"2",business,"2",null,"2",null,"1",accountDetailOld.getMemo(),accountDetailOld.getBusinessType(),partnerName,paymentDate,new Date(),accountDetailOld.getId(),true);

                 //账务明细表里添加一条新数据(正确)
                 AccountDetail accountDetail = financeAccountService.saveAccountDetail(user,org,financeAccount,balance,money,"3",summary,"1",operatorName,"2",business,"2",null,"2",null,"1",memo,accountDetailOld.getBusinessType(),partnerName,paymentDate,new Date(),accountDetailOld.getId(),true);

                 BigDecimal credit = accountPeriodMonth.getCredit().subtract(accountDetailOld.getCredit()).add(money);
                 financeAccount.setCredit(financeAccount.getCredit().subtract(accountDetailOld.getCredit()).add(money));//计入账户收入
                 financeAccount.setBalance(balance);//从余额加上收入
                 accountPeriodMonth.setCredit(credit);
                 accountPeriodMonth.setBalance(balance);

                 credit = accountPeriodDay.getCredit() == null ? new BigDecimal(0).add(money) : accountPeriodDay.getCredit().subtract(accountDetailOld.getCredit()).add(money);
                 accountPeriodDay.setCredit(credit);
                 accountPeriodDay.setBalance(balance);
                 accountPeroidDao.update(accountPeriodMonth);//月结
                 accountPeroidDao.update(accountPeriodDay);//日结
                 financeAccountDao.update(financeAccount);//账户
                 accountDetailOld.setModify(true);
                 accountDetailDao.update(accountDetailOld);

                 AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,accountDetailOld.getBusinessType());

                 addAccountDetailToDetailHistory(accountDetail,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
             }else {
                map.put("status","3");//此账户余额不够冲红，不可修改
                return map;
            }
        }else {
            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,accountDetailOld.getBusinessType());

            accountDetailOld.setReceiveAccountDate(paymentDate);
            accountDetailOld.setBusinessDate(paymentDate);
            accountDetailOld.setPartnerName(partnerName);
            accountDetailOld.setMemo(memo);
            accountDetailOld.setSummary(summary);
            accountDetailOld.setPurpose(summary);
            accountDetailDao.update(accountDetailOld);

            addAccountDetailToDetailHistory(accountDetailOld,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
        }
        map.put("status","1");//成功

        return map;
    }

    private AccountDetailHistory getAccountDetailHistoryLast(AccountDetail accountDetail,Integer business,Integer businessHistory,User user,String businessType){
        AccountDetailHistory accountDetailHistorylast = getAccountDetailHistoryByManyId(accountDetail.getId(), business, null, businessType); //上一次的修改
        if(accountDetailHistorylast==null){
            accountDetailHistorylast = new AccountDetailHistory();
            accountDetailHistorylast.setOrg(accountDetail.getOrg_());
            accountDetailHistorylast.setBusinessType(businessType);
            accountDetailHistorylast.setBalance(accountDetail.getBalance());
            accountDetailHistorylast.setChequeNo(accountDetail.getChequeNo());
            accountDetailHistorylast.setAccount(accountDetail.getAccount());
            accountDetailHistorylast.setType(accountDetail.getType());
            accountDetailHistorylast.setAccountBank(accountDetail.getAccountBank());
            accountDetailHistorylast.setAccountName(accountDetail.getAccountName());
            accountDetailHistorylast.setApplyMemo(accountDetail.getApplyMemo());
            accountDetailHistorylast.setApproveStatus(accountDetail.getApproveStatus());
            accountDetailHistorylast.setAuditDate(accountDetail.getAuditDate());
            accountDetailHistorylast.setApproveLevel(accountDetail.getApproveLevel());
            accountDetailHistorylast.setAuditor(accountDetail.getAuditor());
            accountDetailHistorylast.setAuditorName(accountDetail.getAuditorName());
            accountDetailHistorylast.setBillAmount(accountDetail.getBillAmount());
            accountDetailHistorylast.setBusinessDate(accountDetail.getBusinessDate());
            accountDetailHistorylast.setCredit(accountDetail.getCredit());
            accountDetailHistorylast.setChequeId(accountDetail.getChequeId());
            accountDetailHistorylast.setOppositeCorp(accountDetail.getOppositeCorp());
            accountDetailHistorylast.setOperation(accountDetail.getOperation());
            accountDetailHistorylast.setOppositeAccount(accountDetail.getOppositeAccount());
            accountDetailHistorylast.setOppositeId(accountDetail.getOppositeId());
            accountDetailHistorylast.setApproveMemo(accountDetail.getApproveMemo());
            accountDetailHistorylast.setGenre(accountDetail.getGenre());
            accountDetailHistorylast.setMethod(accountDetail.getMethod());
            accountDetailHistorylast.setReceiveAccountDate(accountDetail.getReceiveAccountDate());
            accountDetailHistorylast.setAccountId(accountDetail.getAccountId_());
            accountDetailHistorylast.setBusiness(business);//以下为需重新赋值的
            accountDetailHistorylast.setCreateName(user.getUserName());
            accountDetailHistorylast.setCreator(user.getUserID());
            accountDetailHistorylast.setCreateDate(new Date());
            accountDetailHistorylast.setVersionNo(0);
            accountDetailHistorylast.setDetailId(accountDetail);
            accountDetailHistorylast.setBusinessHistory(businessHistory);
            accountDetailHistorylast.setOppositeBankno(accountDetail.getOppositeBankno());
            accountDetailHistorylast.setOppositeBankcode(accountDetail.getOppositeBankcode());
            accountDetailHistorylast.setOppositeAccount(accountDetail.getOppositeAccount());
            accountDetailHistorylast.setPartnerName(accountDetail.getPartnerName());
            accountDetailHistorylast.setSummary(accountDetail.getSummary());
            accountDetailHistorylast.setPurpose(accountDetail.getPurpose());
            accountDetailHistoryDao.save(accountDetailHistorylast);
        }
        return accountDetailHistorylast;
    }

    private void addAccountDetailToDetailHistory(AccountDetail accountDetail,Integer business,Integer businessHistory,User user,Integer previousId,Integer versionNo){
        AccountDetailHistory accountDetailHistory = new AccountDetailHistory();
//        BeanUtils.copyProperties(accountDetail,accountDetailHistory);
        accountDetailHistory.setOrg(accountDetail.getOrg_());
        accountDetailHistory.setBusinessType(accountDetail.getBusinessType());
        accountDetailHistory.setBalance(accountDetail.getBalance());
        accountDetailHistory.setChequeNo(accountDetail.getChequeNo());
        accountDetailHistory.setAccountId(accountDetail.getAccountId_());
        accountDetailHistory.setAccount(accountDetail.getAccount());
        accountDetailHistory.setType(accountDetail.getType());
        accountDetailHistory.setAccountBank(accountDetail.getAccountBank());
        accountDetailHistory.setAccountName(accountDetail.getAccountName());
        accountDetailHistory.setApplyMemo(accountDetail.getApplyMemo());
        accountDetailHistory.setApproveStatus(accountDetail.getApproveStatus());
        accountDetailHistory.setAuditDate(accountDetail.getAuditDate());
        accountDetailHistory.setApproveLevel(accountDetail.getApproveLevel());
        accountDetailHistory.setAuditor(accountDetail.getAuditor());
        accountDetailHistory.setAuditorName(accountDetail.getAuditorName());
        accountDetailHistory.setBillAmount(accountDetail.getBillAmount());
        accountDetailHistory.setBusinessDate(accountDetail.getBusinessDate());
        accountDetailHistory.setCredit(accountDetail.getCredit());
        accountDetailHistory.setChequeId(accountDetail.getChequeId());
        accountDetailHistory.setOppositeCorp(accountDetail.getOppositeCorp());
        accountDetailHistory.setOperation(accountDetail.getOperation());
        accountDetailHistory.setOppositeAccount(accountDetail.getOppositeAccount());
        accountDetailHistory.setOppositeId(accountDetail.getOppositeId());
        accountDetailHistory.setApproveMemo(accountDetail.getApproveMemo());
        accountDetailHistory.setGenre(accountDetail.getGenre());
        accountDetailHistory.setMethod(accountDetail.getMethod());
        accountDetailHistory.setReceiveAccountDate(accountDetail.getReceiveAccountDate());
        accountDetailHistory.setDetailId(accountDetail);//以下为需重新赋值的
        accountDetailHistory.setBusiness(business);
        accountDetailHistory.setBusinessHistory(businessHistory);
        accountDetailHistory.setCreateName(user.getUserName());
        accountDetailHistory.setCreator(user.getUserID());
        accountDetailHistory.setCreateDate(new Date());
        accountDetailHistory.setPreviousId(previousId);
        accountDetailHistory.setVersionNo(versionNo);
        accountDetailHistory.setPartnerName(accountDetail.getPartnerName());
        accountDetailHistory.setOppositeAccount(accountDetail.getOppositeAccount());
        accountDetailHistory.setOppositeBankcode(accountDetail.getOppositeBankcode());
        accountDetailHistory.setOppositeBankno(accountDetail.getOppositeBankno());
        accountDetailHistory.setSummary(accountDetail.getSummary());
        accountDetailHistory.setPurpose(accountDetail.getPurpose());
        accountDetailHistory.setBillDate(accountDetail.getBillDate());
        accountDetailHistoryDao.save(accountDetailHistory);
    }

    //获取此returnId的最后一次的历史
    public FinanceReturnHistory getReturnHistoryById(Integer returnId){
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceReturnHistory where financeReturnId=:returnId order by createDate desc";
        map.put("returnId",returnId);
        return (FinanceReturnHistory) financeAccountBillHistoryDao.getByHQLWithNamedParams(hql,map);
    }

    public FinanceReturnHistory getFinanceReturnHistoryLast(FinanceReturn financeReturn){
        FinanceReturnHistory returnHistoryLast = getReturnHistoryById(financeReturn.getId());  //查找最后的一次修改
        if (returnHistoryLast==null){
            returnHistoryLast = new FinanceReturnHistory();
            BeanUtils.copyProperties(financeReturn,returnHistoryLast);
            returnHistoryLast.setFinanceReturn(financeReturn);
            returnHistoryLast.setVersionNo(0);
            financeReturnHistoryDao.save(returnHistoryLast);
        }
//        else {  //常规借款那边修改得时候，如果更新了那么就会将新修改得赋值到这里，所以先去掉
//            if (!MyStrings.nulltoempty(financeReturn.getOperatorName()).isEmpty()){
//                returnHistoryLast.setOperatorName(financeReturn.getOperatorName());
//            }
//            if (!MyStrings.nulltoempty(financeReturn.getPartnerName()).isEmpty()){
//                returnHistoryLast.setPartnerName(financeReturn.getPartnerName());
//            }
//            financeReturnHistoryDao.update(returnHistoryLast);
//        }
        return returnHistoryLast;
    }

    private FinanceReturnHistory addReturnToReturnHistory(FinanceReturn financeReturn,Integer previousId,Integer versionNo,User user){
        FinanceReturnHistory returnHistory = new FinanceReturnHistory();
        BeanUtils.copyProperties(financeReturn,returnHistory);
        returnHistory.setVersionNo(versionNo);
        returnHistory.setPreviousId(previousId);
        returnHistory.setCreator(user.getUserID());
        returnHistory.setCreateDate(new Date());
        returnHistory.setCreateName(user.getUserName());
        returnHistory.setFinanceReturn(financeReturn);
        returnHistory.setFinanceReturnId(financeReturn.getId());
        returnHistory.setPartnerName(financeReturn.getPartnerName());
        returnHistory.setOperatorName(financeReturn.getOperatorName());
        returnHistory.setReturnNo(financeReturn.getReturnNo());
        financeReturnHistoryDao.save(returnHistory);
        return returnHistory;
    }

    //存入银行的票据不能修改有关冲账的字段，没有存入银行的票据可以直接更新保存历史
    private Map<String,Object> updateReturn(User user,Integer business,Integer businessHistory,Integer previousId, BigDecimal money,Date receiveAccountDate,String returnNo,
                                            Date expireDate,String originalCorp,String bankName,String memo,String operatorName,String partnerName,
                                            String businessType,Map<String,Object> map,String oppositeCorp,String payer,String summary){
        //回款票据 的转账支票/承兑汇票
        FinanceAccountBill financeAccountBill = financeAccountService.getAccountBillByBusiness(business,"2",businessType);  //老数据
        FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();  //老数据

        FinanceReturnHistory returnHistoryLast = getFinanceReturnHistoryLast(financeReturn);  //查找上次修改

        //原来的基础上更新
        financeReturn.setAmount(money);
        financeReturn.setBillAmount(money);
        financeReturn.setReturnNo(returnNo);//支票号码
        financeReturn.setOriginalCorp(originalCorp);//出具票据单位
        financeReturn.setBankName(bankName);//出具票据银行
        financeReturn.setExpireDate(expireDate);//发票到期日期
        financeReturn.setReceiveDate(receiveAccountDate);//收到票据日期
        financeReturn.setMemo(memo);//备注
//            financeReturn1.setMyselfId(financeReturn.getId());  //对应修改前的支票id
        financeReturn.setUpdator(user.getUserID());
        financeReturn.setUpdateDate(new Date());
        financeReturn.setUpdateName(user.getUserName());
        financeReturn.setOperatorName(operatorName);  //经手人
        financeReturn.setPartnerName(partnerName);  //对方经手人
//        financeReturn.setUpdateDate(new Date());
//        financeReturn.setUpdator(user.getUserID());
//        financeReturn.setUpdateName(user.getUserName());
        financeReturn.setPayer(payer);
        financeReturn.setSummary(summary);
        financeReturn.setPurpose(summary);
        financeReturnDao.update(financeReturn);

        //最新的再创建历史
        FinanceReturnHistory returnHistory = addReturnToReturnHistory(financeReturn,returnHistoryLast.getId(),returnHistoryLast.getVersionNo()+1,user);

        //上次修改历史
        FinanceAccountBillHistory financeAccountBillHistoryLast = getFinanceAccountBillHistoryLast(financeAccountBill,user,business,previousId,returnHistoryLast.getId(),null);

        financeAccountBill.setBillNo(returnNo);//支票号码
        financeAccountBill.setOppositeCorp(oppositeCorp);
        financeAccountBill.setSummary(summary);
        financeAccountBill.setPurpose(summary);
        financeAccountBill.setModify(true);
        financeAccountBillDao.update(financeAccountBill);

        //更新后的历史
        addBillToBillHistory(financeAccountBill,user,business,businessHistory,financeAccountBillHistoryLast,returnHistory.getId(),null);

        map.put("status","1"); //正确
        return map;
    }

    private FinanceAccountBillHistory getFinanceAccountBillHistoryById(Integer billId,Integer businessHistory,String businessType){
        Map<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from FinanceAccountBillHistory");
        List<String> where = new ArrayList<>();
        if (billId!=null){
            where.add(" billDetail_=:billId ");
            params.put("billId",billId);
        }
        if (businessHistory!=null){
            where.add(" businessHistory=:businessHistory ");
            params.put("businessHistory",businessHistory);
        }

        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            where.add(" businessType=:businessType ");
            params.put("businessType",businessType);
        }
        if (!where.isEmpty()) {
            hql.append(" where ").append(org.apache.commons.lang3.StringUtils.join(where, " and "));
        }
        return (FinanceAccountBillHistory) financeAccountBillHistoryDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    private FinanceAccountBillHistory getFinanceAccountBillHistoryLast(FinanceAccountBill financeAccountBill,User user,Integer business,Integer businessHistory,Integer returnBillHistoryId,Integer chequeDetailHistoryId){
        FinanceAccountBillHistory financeAccountBillHistoryLast = getFinanceAccountBillHistoryById(financeAccountBill.getId(),null,financeAccountBill.getBusinessType());
        if (financeAccountBillHistoryLast==null){
            financeAccountBillHistoryLast = new FinanceAccountBillHistory();
//            financeAccountBillHistoryLast.setOperatorName(user.getUserName());//经手人
            financeAccountBillHistoryLast.setCreateDate(new Date());
            financeAccountBillHistoryLast.setCreateName(user.getUserName());
            financeAccountBillHistoryLast.setCreator(user.getUserID());
            financeAccountBillHistoryLast.setType(financeAccountBill.getType());//1-收入，2-支出
            financeAccountBillHistoryLast.setAmount(financeAccountBill.getAmount());//金额
            financeAccountBillHistoryLast.setBillAmount(financeAccountBill.getBillAmount());  //票面金额
            financeAccountBillHistoryLast.setBillNo(financeAccountBill.getBillNo());//发票号码
            financeAccountBillHistoryLast.setCheque(financeAccountBill.getCheque_());//转账支票外键
//            financeAccountBillHistoryLast.setOrg(user.getOrganization());
//            financeAccountBillHistoryLast.setModityStatus("2");
//            financeAccountBillHistoryLast.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            financeAccountBillHistoryLast.setOccurrenceDate(financeAccountBill.getOccurrenceDate());  //借款中的付款时间
            financeAccountBillHistoryLast.setSource("2");
            financeAccountBillHistoryLast.setBusinessType(financeAccountBill.getBusinessType());
//            financeAccountBillHistoryLast.setCreator(user.getUserID());
//            financeAccountBillHistoryLast.setCreateDate(new Date());
//            financeAccountBillHistoryLast.setCreateName(user.getUserName());
            financeAccountBillHistoryLast.setBillDetail(financeAccountBill);
            financeAccountBillHistoryLast.setBusiness(business);
            financeAccountBillHistoryLast.setBusinessHistory(businessHistory);
            financeAccountBillHistoryLast.setVersionNo(0);
            financeAccountBillHistoryLast.setReturnBillHistory(returnBillHistoryId);
            financeAccountBillHistoryLast.setChequeHistory(chequeDetailHistoryId);
            financeAccountBillHistoryLast.setSummary(financeAccountBill.getSummary());
            financeAccountBillHistoryLast.setPurpose(financeAccountBill.getPurpose());
            financeAccountBillHistoryDao.save(financeAccountBillHistoryLast);
        }
        return financeAccountBillHistoryLast;
    }

    private void addBillToBillHistory(FinanceAccountBill financeAccountBill,User user,Integer business,Integer businessHistory,FinanceAccountBillHistory financeAccountBillHistoryLast,Integer returnHistoryId,Integer chequeDetailHistoryId){
        FinanceAccountBillHistory financeAccountBillHistory = new FinanceAccountBillHistory();
        BeanUtils.copyProperties(financeAccountBill,financeAccountBillHistory);
        financeAccountBillHistory.setCreator(user.getUserID());
        financeAccountBillHistory.setCreateDate(new Date());
        financeAccountBillHistory.setCreateName(user.getUserName());
        financeAccountBillHistory.setBusiness(business);
        financeAccountBillHistory.setBillDetail(financeAccountBill);
        financeAccountBillHistory.setVersionNo(financeAccountBillHistoryLast.getVersionNo()+1);
        financeAccountBillHistory.setPreviousId(financeAccountBillHistoryLast.getId());
        financeAccountBillHistory.setBusinessHistory(businessHistory);
        financeAccountBillHistory.setReturnBillHistory(returnHistoryId);
        financeAccountBillHistory.setChequeHistory(chequeDetailHistoryId);
        financeAccountBillHistoryDao.save(financeAccountBillHistory);
    }

    private Map<String,Object> updateBank(User user,Integer business,Integer businessHistory,Integer previousId,BigDecimal money,Date receiveAccountDate,Integer accountId,String memo,String partnerName,Integer type,String summary,Map<String,Object> map){
        AccountDetail accountDetailOld = getAccountDetailByBusiness(business,type,null);
        FinanceAccount f = financeAccountDao.get(accountDetailOld.getAccountId_());  //修改前的银行账户
        FinanceAccount f1 = financeAccountDao.get(accountId);  //修改后的银行账户
        if (0==f.getAccountStatus() || 0==f1.getAccountStatus()){
            map.put("status","2"); //账户被冻结
            return map;
        }

        BigDecimal newBanlance = f.getBalance(); //账户修改前余额
        BigDecimal newBanlance1 = f1.getBalance() ; //账户修改后的账户余额
        BigDecimal newCredit = accountDetailOld.getCredit(); //修改前收入

        //金额是否修改
//        if (accountDetailOld.getCredit()!=null && money.compareTo(accountDetailOld.getCredit())!=0){
            if (!f.getId().equals(f1.getId())){
                //收入修改 新余额=现在账户余额-修改前的收入+修改后的新收入
                newBanlance=f.getBalance().subtract(accountDetailOld.getCredit()); //修改前账户的余额
                newBanlance1=f1.getBalance().add(money);  //修改后的账户余额
            }else {
                //收入修改 新余额=现在账户余额-修改前的收入+修改后的新收入
                newBanlance=f.getBalance().subtract(accountDetailOld.getCredit()).add(money);  //账户余额
                newBanlance1 = newBanlance;
            }
//        }

        if (new BigDecimal(0).equals(newBanlance) || new BigDecimal(0).equals(newBanlance1)){
            map.put("status","3"); //账户余额不够冲减
            return map;
        }
        //如果金额修改了或者是银行账户修改了
        if (money.compareTo(accountDetailOld.getCredit())!=0|| !f.getId().equals(f1.getId())){
            AccountPeriod accountPeriod = new AccountPeriod();  //修改前账户的月结
            AccountPeriod accountPeriod1 = new AccountPeriod();  //修改后账户的月结
            AccountPeriod accountPeriodDay = new AccountPeriod();  //修改前账户的日结
            AccountPeriod accountPeriodDay1 = new AccountPeriod();  //修改后账户的日结
            if (f==f1){   //没有修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(f.getId(), new Date());  //账户的月结
                accountPeriodDay = accountService.getAccountPeriodByDay(f.getId(),new Date());  //账户的日结
            }else {    //修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(f.getId(), new Date());  //修改前账户的月结
                accountPeriod1 = accountService.getAccountPeriodByMonth(f1.getId(),new Date());  //修改后账户的账户月结
                accountPeriodDay = accountService.getAccountPeriodByDay(f.getId(), new Date()); //修改前账户的日结
                accountPeriodDay1 = accountService.getAccountPeriodByDay(f1.getId(), new Date());  //修改后账户的账户日结
            }

           //批准后，账务明细表里添加一条新数据(金额为负)
            AccountDetail accountDetail2 = new AccountDetail();//批准后，账务明细表里添加一条新数据(金额为负)
            accountDetail2.setBalance(newBanlance);
            accountDetail2.setOrg(user.getOrganization());
            accountDetail2.setAuditDate(new Date());
            accountDetail2.setGenre(accountDetailOld.getGenre());
            accountDetail2.setMethod(accountDetailOld.getMethod());
            accountDetail2.setAccountId(accountDetailOld.getAccountId());//账户外键
            accountDetail2.setAccountBank(accountDetailOld.getAccountBank());
            accountDetail2.setPreviousId(accountDetailOld.getId());
            accountDetail2.setBusinessDate(accountDetailOld.getBusinessDate());
            accountDetail2.setReceiveAccountDate(accountDetailOld.getReceiveAccountDate());
            accountDetail2.setType(accountDetailOld.getType());//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail2.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail2.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail2.setBusiness(accountDetailOld.getBusiness());
            accountDetail2.setSource("2");
            accountDetail2.setBusinessType(accountDetailOld.getBusinessType());
            accountDetail2.setAuditorName(accountDetailOld.getAuditorName());
            accountDetail2.setAuditor(user.getUserID());
            accountDetail2.setCreateDate(new Date());
            accountDetail2.setCreateName(user.getUserName());
            accountDetail2.setCreator(user.getUserID());
            accountDetail2.setAuditDate(new Date());
            accountDetail2.setCredit(accountDetailOld.getCredit().multiply(new BigDecimal(-1)));  //负数
            accountDetail2.setBillAmount(money);  //票面金额
            accountDetail2.setFid(f.getId().toString());
            accountDetail2.setBalance(newBanlance); //余额
            accountDetail2.setPartnerName(accountDetailOld.getPartnerName());
            accountDetail2.setSummary(accountDetailOld.getSummary());
            accountDetail2.setPurpose(accountDetailOld.getPurpose());
            accountDetail2.setBillDate(new Date());
            accountDetail2.setModify(true);

            //账务明细表里添加一条新数据(金额修改后的正确)
            AccountDetail accountDetail1 = new AccountDetail();
            accountDetail1.setOrg(user.getOrganization());
            accountDetail1.setAuditDate(new Date());
            accountDetail1.setGenre(accountDetailOld.getGenre());
            accountDetail1.setMethod("5");
            accountDetail1.setAuditorName(user.getUserName());
            accountDetail1.setAuditor(user.getUserID());
            accountDetail1.setAccountId(f1);//账户外键
            accountDetail1.setAccountBank(f1.getBankName() + f1.getAccount());
            accountDetail1.setPreviousId(accountDetailOld.getId());
            accountDetail1.setVersionNo((accountDetailOld.getVersionNo()==null)?0:accountDetailOld.getVersionNo()+1);
            accountDetail1.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail1.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail1.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail1.setBusiness(business);
            accountDetail1.setSource("2");
            accountDetail1.setBusinessType(accountDetailOld.getBusinessType());
//            BeanUtils.copyProperties(accountDetailOld,accountDetail1);
            accountDetail1.setCreateDate(new Date());
            accountDetail1.setCreateName(user.getUserName());
            accountDetail1.setCreator(user.getUserID());
            accountDetail1.setAuditDate(new Date());
            accountDetail1.setCredit(money);
            accountDetail1.setBillAmount(money);  //票面金额
            accountDetail1.setMemo(memo);
            accountDetail1.setReceiveAccountDate(receiveAccountDate);
            accountDetail1.setBusinessDate(accountDetailOld.getBusinessDate());
            accountDetail1.setAccountBank(f1.getBankName()+f1.getAccount());
            accountDetail1.setAccountantStatus("2");//会计数据状态  1-未选择  2-已选择
            accountDetail1.setFid(f1.getId().toString());
            accountDetail1.setAccountId(f1);//账户外键
            accountDetail1.setPartnerName(partnerName);
            accountDetail1.setSummary(summary);
            accountDetail1.setPurpose(summary);
            accountDetail1.setBillDate(new Date());
            accountDetail1.setModify(true);

            //账户是否改变
            if (accountPeriod1.getId()!=null){   //账户修改
                accountDetail1.setBalance(newBanlance1); //正确的新余额

                //修改前账户的月结
                accountPeriod.setCredit(accountPeriod.getCredit().subtract(newCredit));
                accountPeriod.setBalance(newBanlance);

                //修改后账户的月结
                accountPeriod1.setCredit(accountPeriod1.getCredit().add(money));
                accountPeriod1.setBalance(newBanlance1);

                //修改前的日结
                accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(newCredit));
                accountPeriodDay.setBalance(newBanlance);

                //修改后的日结
                accountPeriodDay1.setCredit(accountPeriodDay1.getCredit().add(money));
                accountPeriodDay1.setBalance(newBanlance1);

                //修改前账户余额
                f.setCredit(f.getCredit().subtract(newCredit));
                f.setBalance(newBanlance);

                //修改后账户余额
                f1.setCredit(f1.getCredit().add(money));
                f1.setBalance(newBanlance1);

                financeAccountDao.update(f1);
                accountPeroidDao.update(accountPeriod1);
                accountPeroidDao.update(accountPeriodDay1);

            }else {  //账户未修改
                accountDetail1.setBalance(newBanlance); //正确的新余额

                //修改后的月结
                accountPeriod.setCredit(accountPeriod.getCredit().subtract(newCredit).add(money));
                accountPeriod.setBalance(newBanlance);

                //修改后的日结
                accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(newCredit).add(money));
                accountPeriodDay.setBalance(newBanlance);

                //修改后的账户余额
                f.setCredit(f.getCredit().subtract(newCredit).add(money));
                f.setBalance(newBanlance);
            }

            accountDetailDao.save(accountDetail2);
            accountDetailDao.save(accountDetail1);
            financeAccountDao.update(f);
            accountPeroidDao.update(accountPeriod);
            accountPeroidDao.update(accountPeriodDay);

            accountDetailOld.setModify(true);
            accountDetailDao.update(accountDetailOld);

            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,accountDetailOld.getBusinessType());  //查找上次的修改

            addAccountDetailToDetailHistory(accountDetail1,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
        }else {
            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,accountDetailOld.getBusinessType());

            //批准后，如果金额和账户均没有修改则直接覆盖数据
            accountDetailOld.setMemo(memo);
            accountDetailOld.setReceiveAccountDate(receiveAccountDate);
            accountDetailOld.setSummary(summary);
            accountDetailOld.setPurpose(summary);
            accountDetailDao.update(accountDetailOld);

            addAccountDetailToDetailHistory(accountDetailOld,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
        }
        map.put("status","1"); //成功
        return map;
    }

    /**
     * @param  userId  操作人id
     * @param  business   借款还款id
     * @param method 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param money 金额
     * @param withinOrAbroad  内部or外部的转账支票  1-内部，0-外部
     * @param checkId   支票id/汇票id
     * @param accountId  //账户id
     * @param receiveDate   接收日期
     * @param expireDate  支票到期日
     * @param operator  支付经手人
     * @param receiver  接收经手人
     * @param paymentDate  付款日期(各个表中的字段不同，注意下)
     * @param oppositeCorp 收款单位
     * @param payer  付款单位
     * @return status 0-失败 1-添加成功  2-账户被关闭 3-账户余额不够冲减
     */
    @Override
    public Map<String,Object> loanPayEntry(Integer userId,Integer business,String method, Double money,String withinOrAbroad, Integer checkId,Integer accountId,Date receiveDate, Date expireDate,String operator, String receiver,Date paymentDate,String oppositeCorp,String payer) {
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态
        User user = userDao.get(userId);
        map.put("status",status);
        String summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+"公司向"+oppositeCorp+"付款";
        if ("1".equals(method)){   //单纯现金
           map =  addCashDebit(user,business,BigDecimal.valueOf(money),paymentDate,"1",receiver,summary,map);  //支出现金
        }else if ("3".equals(method)||"4".equals(method)){  //转账支票或承兑汇票
           map = addReturnDebit(user,business,method,BigDecimal.valueOf(money),paymentDate,withinOrAbroad,checkId,accountId,receiveDate,receiver,operator,expireDate,"0102",map,oppositeCorp,payer,summary);  //支出的转账支票或承兑汇票
        }else if ("5".equals(method)){  //银行转账
           map = addBankDebit(user,business,method,BigDecimal.valueOf(money),paymentDate,accountId,"1",summary,map);  //支出的银行转账
        }
        return map;
    }

    //借款的付款录入中本金型式--现金
    private Map<String,Object> addCashDebit(User user,Integer business,BigDecimal money,Date paymentDate,String status,String receiver,String summary,Map<String,Object> map){
        //单纯现金
        FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(user.getOid(), 1);//备用金
        if (0 == financeAccount.getAccountStatus()) {
            map.put("status","2"); //此账户已被冻结，不允许操作
            return map;
        }

        BigDecimal balance = financeAccount.getBalance().subtract(money);  //支出金额后的账户当前余额
        if (balance.doubleValue() < 0) {
            map.put("status","3");  //此账户余额不足
            return map;
        }

        AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());  //月结
        AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

        AccountDetail accountDetail = new AccountDetail();
        accountDetail.setDebit(money);
        accountDetail.setCreateDate(new Date());
        accountDetail.setCreator(user.getUserID());
        accountDetail.setCreateName(user.getUserName());
        accountDetail.setOrg(user.getOrganization());
        accountDetail.setAuditDate(new Date());
        accountDetail.setAuditor(user.getUserID());
        accountDetail.setAuditorName(user.getUserName());
        accountDetail.setBillAmount(money);  //票面金额
        accountDetail.setMethod("1");  //0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
        accountDetail.setFid(financeAccount.getId().toString());
        accountDetail.setAccountId(financeAccount);//账户外键
        accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
        accountDetail.setAccountBank(financeAccount.getBankName());
        accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
        accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
        accountDetail.setBusinessDate(paymentDate);
        accountDetail.setBusiness(business);
        accountDetail.setSource("2");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)
        accountDetail.setBalance(balance);
        accountDetail.setBusinessType("0102"); //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
        accountDetail.setPartnerName(receiver);
        accountDetail.setSummary(summary);
        accountDetail.setPurpose(summary);
        accountDetail.setBillDate(new Date());

        financeAccount.setDebit(financeAccount.getDebit().add(money));//计入账户支出
        financeAccount.setBalance(balance);//从余额减去支出

        accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().add(money));
        accountPeriodMonth.setBalance(accountPeriodMonth.getBalance().subtract(money));

        accountPeriodDay.setDebit(accountPeriodDay.getDebit().add(money));
        accountPeriodDay.setBalance(accountPeriodDay.getBalance().subtract(money));

        accountPeroidDao.update(accountPeriodMonth);//月结
        accountPeroidDao.update(accountPeriodDay);//日结
        financeAccountDao.update(financeAccount);//账户

//        //添加到账户明细票据表中
//        FinanceAccountBill financeAccountBill = new FinanceAccountBill();
//        financeAccountBill.setCreateDate(new Date());
//        financeAccountBill.setCreateName(user.getUserName());
//        financeAccountBill.setCreator(user.getUserID());
//        financeAccountBill.setType("2");//1-收入，2-支出
//        financeAccountBill.setAmount(money);//金额
//        financeAccountBill.setBillAmount(money);  //票面金额
//        financeAccountBill.setOrg(user.getOrganization());
//        financeAccountBill.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
//        financeAccountBill.setModityStatus("1");
//        financeAccountBill.setBusiness(business);
//        financeAccountBill.setOccurrenceDate(paymentDate);  //付款日期
//        financeAccountBill.setSource("2"); //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)
//        financeAccountBill.setBusinessType("0102");//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
//        financeAccountBill.setBusinessDate(paymentDate);
//        financeAccountBill.setSummary(summary);
//        financeAccountBill.setPurpose(summary);
//        financeAccountBillDao.save(financeAccountBill);
//
//        accountDetail.setBillDetail(financeAccountBill);//把票据明细更新进去
        accountDetailDao.save(accountDetail);//明细
        map.put("status","1");  //成功
        return map;
    }

    //借款的付款录入中本金型式--承兑汇票或者转账支票   oppositeCorp-收款单位,payer-付款单位
    private Map<String,Object> addReturnDebit(User user,Integer business,String method,BigDecimal money,Date paymentDate,String withinOrAbroad,
         Integer checkId,Integer accountId,Date receiveDate,String receiver,String operator,Date expireDate,String businessType,Map<String,Object> map,String oppositeCorp,String payer,String summary){
        //3代表转账支票
        //内部or外部   1-内部，0-外部
        if ("3".equals(method) && "1".equals(withinOrAbroad)) {  //转账支票的内部支票
            //支出的内部银行转账支票
            FinanceAccount financeAccount = financeAccountDao.get(accountId);
            if (0 == financeAccount.getAccountStatus()) {
                map.put("status","2"); //此账户已被冻结，不允许操作
                return map;
            }

            BigDecimal balance = financeAccount.getBalance().subtract(money);  //支出后账户的当前余额
            if (balance.doubleValue() < 0) {
                map.put("status","3");//余额不足
                return map;
            }
            FinanceChequeDetail financeChequeDetail = financeChequeDetailDao.get(checkId);
            financeChequeDetail.setReceiver(receiver);//接收经手人
            financeChequeDetail.setAmount(money);
            financeChequeDetail.setBillAmount(money);  //票面金额
            financeChequeDetail.setOperator(operator);//支付经手人
            financeChequeDetail.setFinancialHandling(user.getUserName());//财务经手人
            financeChequeDetail.setExpireDate(expireDate);//支票到期日
            financeChequeDetail.setReceiveDate(receiveDate);//接收日期
            financeChequeDetail.setState("2");//1-未使用,2-已使用,3-作废
            financeChequeDetail.setModityStatus("1");
            financeChequeDetail.setUpdateDate(new Date());
            financeChequeDetail.setUpdateName(user.getUserName());
            financeChequeDetail.setUpdator(user.getUserID());
            financeChequeDetail.setReceiveCorp(oppositeCorp);  //收款单位
            financeChequeDetail.setSummary(summary);
            financeChequeDetail.setPurpose(summary);
            financeChequeDetailDao.update(financeChequeDetail);

            //支票明细
            FinanceAccountBill financeAccountBill = new FinanceAccountBill();
            financeAccountBill.setOperatorName(user.getUserName());//经手人
            financeAccountBill.setCreateDate(new Date());
            financeAccountBill.setCreateName(user.getUserName());
            financeAccountBill.setCreator(user.getUserID());
            financeAccountBill.setType("2");//1-收入，2-支出
            financeAccountBill.setAmount(money);//金额
            financeAccountBill.setBillAmount(money);  //票面金额
            financeAccountBill.setBillNo(financeChequeDetail.getChequeNo());//发票号码
            financeAccountBill.setCheque(financeChequeDetail);//转账支票外键
            financeAccountBill.setOrg(user.getOrganization());
            financeAccountBill.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            financeAccountBill.setBusiness(business);
            financeAccountBill.setOccurrenceDate(paymentDate);  //借款中的付款时间
            financeAccountBill.setSource("2");
            financeAccountBill.setBusinessType(businessType);//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
            financeAccountBill.setOppositeCorp(oppositeCorp); //收款单位
            financeAccountBill.setBusinessDate(new Date());
            financeAccountBill.setSummary(summary);
            financeAccountBill.setPurpose(summary);
            financeAccountBill.setBillDate(new Date());

            AccountPeriod accountPeriodMonth = accountService.getPeroidByType(financeAccount.getOrg_(), financeAccount.getAccountType(), financeAccount.getId(), 1);//月结
            AccountPeriod accountPeriodDay = accountService.getPeroidByType(financeAccount.getOrg_(), financeAccount.getAccountType(), financeAccount.getId(), 2);//找到日结

            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setDebit(money);
            accountDetail.setBalance(balance);
            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setAuditDate(new Date());
            accountDetail.setAuditorName(user.getUserName());//经手人
            accountDetail.setBillAmount(money);  //票面金额
            accountDetail.setMethod(method);
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setBusiness(business);
            accountDetail.setSource("2");
            accountDetail.setBusinessDate(paymentDate);  //借款时的付款日期
            accountDetail.setBusinessType(businessType); //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(summary);
            accountDetail.setBillDate(new Date());

            financeAccount.setDebit(financeAccount.getDebit().add(money));//计入账户支出
            financeAccount.setBalance(balance);//从余额减去支出

            accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().add(money));
            accountPeriodMonth.setBalance(balance);

            accountPeriodDay.setDebit(accountPeriodDay.getDebit().add(money));
            accountPeriodDay.setBalance(balance);

            accountPeroidDao.update(accountPeriodMonth);//月结
            accountPeroidDao.update(accountPeriodDay);//日结
            financeAccountDao.update(financeAccount);//账户
            financeAccountBillDao.save(financeAccountBill);
            accountDetail.setBillDetail(financeAccountBill);
            accountDetailDao.save(accountDetail);//明细

        } else {    //支出的外部转账支票 或者是 承兑汇票
            FinanceReturn fr = financeReturnDao.get(checkId);
            fr.setState("4");//1-有效,2-存入银行,3-作废,4-已支出
            fr.setPartnerName(receiver);
            fr.setOperatorName(operator);
            fr.setUpdateDate(new Date());
            fr.setUpdateName(user.getUserName());
            fr.setUpdator(user.getUserID());
            fr.setPayer(payer);  //付款单位
            financeReturnDao.update(fr);

            //支票明细
            FinanceAccountBill f = new FinanceAccountBill();
            f.setCreateDate(new Date());
            f.setCreateName(user.getUserName());
            f.setCreator(user.getUserID());
            f.setType("2");//1-收入，2-支出
            f.setAmount(fr.getAmount());//金额
            f.setBillAmount(fr.getAmount());  //票面金额
            f.setBillNo(fr.getReturnNo());//发票号码
            f.setFinanceReturn(fr);//回款票据外键
            f.setOrg(fr.getOrg());
            f.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            f.setSource("2");
            f.setBusiness(business);
            f.setBusinessDate(new Date());
            f.setOccurrenceDate(paymentDate);
            f.setOppositeCorp(oppositeCorp);  //收款单位
            f.setModityStatus("2");
            f.setBusinessType(businessType);  //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
            f.setSummary(summary);
            f.setPurpose(summary);
            f.setBillDate(new Date());
            financeAccountBillDao.save(f);
        }
        map.put("status","1"); //成功
        return map;
    }

    //借款的付款录入中本金型式--银行转账
    private Map<String,Object> addBankDebit(User user,Integer business,String method,BigDecimal money,Date paymentDate,Integer accountId,String status,String summary,Map<String,Object> map){
        //银行转账
        FinanceAccount financeAccount = financeAccountDao.get(accountId);
        if (0 == financeAccount.getAccountStatus()) {
            map.put("status","2");  //此账户已被冻结，不允许操作
            return map;
        }

        BigDecimal balance = financeAccount.getBalance().subtract(money);  //支出后账户的当前余额
        if (balance.doubleValue() < 0) {
            map.put("status","3"); //余额不足
            return map;
        }

        AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());//月结
        AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

        AccountDetail accountDetail = new AccountDetail();
        accountDetail.setDebit(money);
        accountDetail.setBalance(financeAccount.getBalance());
        accountDetail.setCreateDate(new Date());
        accountDetail.setCreator(user.getUserID());
        accountDetail.setCreateName(user.getUserName());
        accountDetail.setOrg(user.getOrganization());
        accountDetail.setAuditDate(new Date());
        accountDetail.setAuditorName(user.getUserName());
        accountDetail.setMethod(method);
        accountDetail.setFid(financeAccount.getId().toString());
        accountDetail.setBillAmount(money);  //票面金额
        accountDetail.setAccountId(financeAccount);//账户外键
        accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
        accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
        accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
        accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
        accountDetail.setBusiness(business);
        accountDetail.setSource("2");
        accountDetail.setBusinessDate(paymentDate);  //借款时的付款日期
        accountDetail.setBusinessType("0102");//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
        accountDetail.setSummary(summary);
        accountDetail.setPurpose(summary);
        accountDetail.setBillDate(new Date());

        financeAccount.setDebit(financeAccount.getDebit().add(money));//计入账户支出
        financeAccount.setBalance(balance);//从余额减去支出

        accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().add(money));
        accountPeriodMonth.setBalance(balance);

        accountPeriodDay.setDebit(accountPeriodDay.getDebit().add(money));
        accountPeriodDay.setBalance(balance);

//        FinanceAccountBill financeAccountBill = new FinanceAccountBill();
//        financeAccountBill.setCreateDate(new Date());
//        financeAccountBill.setCreateName(user.getUserName());
//        financeAccountBill.setCreator(user.getUserID());
//        financeAccountBill.setType("2");//1-收入，2-支出
//        financeAccountBill.setAmount(money);//金额
//        financeAccountBill.setBillAmount(money);  //票面金额
//        financeAccountBill.setOrg(user.getOrganization());
//        financeAccountBill.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
//        financeAccountBill.setState("0");
//        financeAccountBill.setBusiness(business);
//        financeAccountBill.setOccurrenceDate(paymentDate);  //借款中的付款时间
//        financeAccountBill.setSource("2");
//        financeAccountBill.setModityStatus("1");
//        financeAccountBill.setBusinessType("0102");//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款

        accountPeroidDao.update(accountPeriodMonth);//月结
        accountPeroidDao.update(accountPeriodDay);//日结
        financeAccountDao.update(financeAccount);//账户
//        financeAccountBillDao.save(financeAccountBill);//新增为银行转账的票据明细
//        accountDetail.setBillDetail(financeAccountBill);//把票据明细更新进去
        accountDetailDao.save(accountDetail);//明细
        map.put("status","1"); //成功
//        map.put("detailId",accountDetail.getId());
        return map;
    }

    /**
     * 借款-还款录入的修改
     * @param  userId  操作人id
     * @param  payId   要修改的还款id
     * @param methodOld   修改前的本金型式
     * @param method 修改后的本金型式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param money 金额
     * @param withinOrAbroad  内部or外部的转账支票  1-内部，0-外部
     * @param checkId   支票id/汇票id
     * @param accountId  //账户id
     * @param receiveDate   接收日期
     * @param expireDate  支票到期日
     * @param operator  支付经手人
     * @param receiver  接收经手人
     * @param paymentDate  付款日期(各个表中的字段不同，注意下)
     * @param oppositeCorp 收款单位
     * @param payer 付款单位
     * @return status 0-失败 1-添加成功  2-账户被关闭 3-账户余额不够冲减
     */
    @Override
    public Map<String,Object> updateLoanPayEntry(Integer userId,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method,
          Double money,String withinOrAbroad, Integer checkId,Integer accountId,Date receiveDate, Date expireDate,String operator, String receiver,
          Date paymentDate,String oppositeCorp,String payer) {
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态
        map.put("status",status);
        User user = userDao.get(userId);
        String summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+"公司向"+oppositeCorp+"付款";
        if ("1".equals(method)){   //单纯现金
            updateCashDebit(user,payId,payHistoryId,previousId,methodOld,method,BigDecimal.valueOf(money),paymentDate,summary,map);  //支出现金
        }else if ("3".equals(method)||"4".equals(method)){  //转账支票或承兑汇票(未存入银行的票据怎么进行修改？  已存入银行的票据在付款录入中不可选择？)
            updateReturnDebit(user,payId,payHistoryId,previousId,methodOld,method,BigDecimal.valueOf(money),paymentDate,withinOrAbroad,checkId,accountId,receiveDate,receiver,operator,expireDate,"0102",map,oppositeCorp,payer,summary);  //支出的转账支票或承兑汇票
        }else if ("5".equals(method)){  //银行转账
            updateBankDebit(user,payId,payHistoryId,previousId,methodOld,method,BigDecimal.valueOf(money),paymentDate,accountId,summary,map);  //支出的银行转账
        }
        return map;
    }

    //借款还款修改前数据的操作   (此接口中返回状态为1的，都是不需冲账的，直接完成)
    private  Map<String,Object> updateBeforeHandle(User user,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method,BigDecimal money,
           Date paymentDate,Integer checkId,Integer accountId,Date receiveDate,String receiver,String operator,Date expireDate,String businessType,Map<String,Object> map){
        if ("1".equals(methodOld) || "5".equals(methodOld)){  //修改前为现金/银行转账
            AccountDetail accountDetailOld = getAccountDetailByBusiness(payId,null,businessType);
            FinanceAccount financeAccountOld = financeAccountDao.get(accountDetailOld.getAccountId_());
            BigDecimal balanceOld = financeAccountOld.getBalance().add(accountDetailOld.getDebit());

            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,payId,previousId,user,businessType); //上一次的修改

            if (money.compareTo(accountDetailOld.getDebit())==0 && method.equals(methodOld) && accountId.equals(financeAccountOld.getId())){  //没有修改金额和支出方式、银行
                if (paymentDate!=null){
                    accountDetailOld.setBusinessDate(paymentDate);
                }
                addAccountDetailToDetailHistory(accountDetailOld,payId,payHistoryId,user,accountDetailHistoryLast.getId(),(null==accountDetailHistoryLast.getVersionNo())?1:accountDetailHistoryLast.getVersionNo()+1);  //将修改后的更新到历史
                map.put("status","1");  //成功
                return map;
            }

            accountDetailOld.setModityStatus("2");
            accountDetailOld.setModify(true);
            accountDetailDao.update(accountDetailOld);

            //添加金额为负的
            AccountDetail accountDetail1 = new AccountDetail();
            accountDetail1.setCreateDate(new Date());
            accountDetail1.setCreateName(user.getUserName());
            accountDetail1.setCreator(user.getUserID());
            accountDetail1.setOrg(user.getOrganization());
            accountDetail1.setAuditDate(new Date());
            accountDetail1.setDebit(accountDetailOld.getDebit().multiply(new BigDecimal(-1)));
            accountDetail1.setBillAmount(accountDetailOld.getBillAmount());  //票面金额
            accountDetail1.setAuditorName(accountDetailOld.getAuditorName());//经手人
            accountDetail1.setMethod(accountDetailOld.getMethod());//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
            accountDetail1.setMemo(accountDetailOld.getMemo());
            accountDetail1.setGenre(accountDetailOld.getGenre());
            accountDetail1.setCategoryDesc(accountDetailOld.getCategoryDesc());
            accountDetail1.setSummary(accountDetailOld.getSummary());
            accountDetail1.setPurpose(accountDetailOld.getPurpose());//用途
            accountDetail1.setOppositeCorp(accountDetailOld.getOppositeCorp());//付款单位
            accountDetail1.setAccountBank(accountDetailOld.getAccountBank());
            accountDetail1.setAccountantStatus(accountDetailOld.getAccountantStatus());//会计数据状态  1-未选择  2-已选择
            accountDetail1.setFid(accountDetailOld.getFid());
            accountDetail1.setAccountId(accountDetailOld.getAccountId());//账户外键
            accountDetail1.setType(accountDetailOld.getType());//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail1.setBalance(balanceOld); //余额
            accountDetail1.setBillDetail(accountDetailOld.getBillDetail());
            accountDetail1.setModityStatus("2");
            accountDetail1.setBusinessType(businessType);
            accountDetail1.setSource(accountDetailOld.getSource());
            accountDetail1.setBusiness(accountDetailOld.getBusiness());
            accountDetail1.setBusinessDate(accountDetailOld.getBusinessDate());
            accountDetail1.setBusinessHistory(accountDetailOld.getBusinessHistory());
            accountDetail1.setPartnerName(accountDetailOld.getPartnerName());
            accountDetail1.setOppositeBankcode(accountDetailOld.getOppositeBankcode());
            accountDetail1.setOppositeBankno(accountDetailOld.getOppositeBankno());
            accountDetail1.setOppositeAccount(accountDetailOld.getOppositeAccount());
            accountDetail1.setSummary(accountDetailOld.getSummary());
            accountDetail1.setBillDate(new Date());
            accountDetail1.setPreviousId(accountDetailOld.getId());
            accountDetail1.setModify(true);
            accountDetailDao.save(accountDetail1);

            financeAccountOld.setBalance(balanceOld);
            financeAccountOld.setDebit(financeAccountOld.getDebit().subtract(accountDetailOld.getDebit()));
            financeAccountDao.update(financeAccountOld);

            AccountPeriod accountPeriodMonthOld = accountService.getAccountPeriodByMonth(financeAccountOld.getId(), new Date());  //月结
            AccountPeriod accountPeriodDayOld = accountService.getAccountPeriodByDay( financeAccountOld.getId(), new Date());//找到日结

            accountPeriodMonthOld.setDebit(accountPeriodMonthOld.getDebit().subtract(accountDetailOld.getDebit()));
            accountPeriodMonthOld.setBalance(balanceOld);
            accountPeroidDao.update(accountPeriodMonthOld);

            accountPeriodDayOld.setDebit(accountPeriodDayOld.getDebit().subtract(accountDetailOld.getDebit()));
            accountPeriodDayOld.setBalance(balanceOld);
            accountPeroidDao.update(accountPeriodDayOld);

            map.put("accountDetailOld",accountDetailOld);
            map.put("accountPeriodMonthOld",accountPeriodMonthOld);
            map.put("accountPeriodDayOld",accountPeriodDayOld);
//            map.put("status","1");  //成功
        }else if ("3".equals(methodOld) || "4".equals(methodOld)){  //修改前的为承兑汇票/转账支票
            FinanceAccountBill financeAccountBillOld = getFinanceAccountBillByBusiness(payId,null,businessType);
            FinanceReturn financeReturnOld = financeAccountBillOld.getFinanceReturn();
            FinanceChequeDetail financeChequeDetailOld = financeAccountBillOld.getCheque();

            if (financeReturnOld!=null){   //  外部支票/承兑汇票
                financeReturnOld.setState("1"); //1-有效,2-存入银行,3-作废，4-已支出
                FinanceReturnHistory financeReturnHistoryLast = getFinanceReturnHistoryLast(financeReturnOld); //上一次修改
                FinanceAccountBillHistory financeAccountBillHistoryLast = getFinanceAccountBillHistoryLast(financeAccountBillOld,user,payId,previousId,financeReturnHistoryLast.getId(),null);  //上一次修改
                if (money.equals(financeAccountBillOld.getAmount()) && method.equals(methodOld) && checkId.equals(financeReturnOld.getId())){  //没有修改金额和支出方式
                    FinanceReturnHistory returnHistory = addReturnToReturnHistory(financeReturnOld,financeReturnHistoryLast.getId(),financeReturnHistoryLast.getVersionNo()+1,user);
                    addBillToBillHistory(financeAccountBillOld,user,payId,payHistoryId,financeAccountBillHistoryLast,returnHistory.getId(),null);

                    map.put("status","1");  //成功
                    return map;
                }else {
//                    getFinanceReturnHistoryLast(financeReturnOld); //若没有上次的修改记录，则加上
                    getFinanceAccountBillHistoryLast(financeAccountBillOld,user,payId,previousId,financeReturnHistoryLast.getId(),null);//若没有上次的修改记录，则加上
                }
            }else if (financeChequeDetailOld!=null){   //内部支票
                //上次的修改
                FinanceChequeDetailHistory chequeDetailHistoryLast = getFinanceChequeDetailHistoryLast(financeChequeDetailOld,user,payId);
                //支票明细
                FinanceAccountBillHistory financeAccountBillHistoryLast = getFinanceAccountBillHistoryLast(financeAccountBillOld,user,payId,previousId,null,chequeDetailHistoryLast.getId());

                if (money.compareTo(financeAccountBillOld.getAmount())==0 && method.equals(methodOld) && checkId.equals(financeChequeDetailOld.getId())){  //没有修改金额和支出方式、支票
                    financeChequeDetailOld.setReceiver(receiver);//接收经手人
                    financeChequeDetailOld.setOperator(operator);//支付经手人
                    financeChequeDetailOld.setFinancialHandling(user.getUserName());//财务经手人
                    financeChequeDetailOld.setExpireDate(expireDate);//支票到期日
                    financeChequeDetailOld.setReceiveDate(receiveDate);//接收日期
                    financeChequeDetailOld.setState("2");//1-未使用,2-已使用,3-作废
                    financeChequeDetailOld.setModityStatus("1");
                    financeChequeDetailOld.setUpdateDate(new Date());
                    financeChequeDetailOld.setUpdator(user.getUserID());
                    financeChequeDetailOld.setUpdateName(user.getUserName());
//                    financeChequeDetailDao.update(financeChequeDetailOld);

                    FinanceChequeDetailHistory chequeDetailHistory = addChequeDetailToChequeDetailHistory(financeChequeDetailOld,chequeDetailHistoryLast.getId(),0,user);

                    financeAccountBillOld.setBusiness(payId);
                    financeAccountBillOld.setOccurrenceDate(paymentDate);  //借款中的付款时间
                    financeAccountBillOld.setSource("2");
                    financeAccountBillOld.setModify(true);
//                    financeAccountBillOld.setMyselfId(financeAccountBillOld.getId());
                    financeAccountBillDao.update(financeAccountBillOld);

                    addBillToBillHistory(financeAccountBillOld,user,payId,payHistoryId,financeAccountBillHistoryLast,null,chequeDetailHistory.getId());

                    map.put("status","1");  //成功
                    return map;
                }
                AccountDetail accountDetailOld = dataService.getByBillId(financeAccountBillOld.getId());  //老数据对应的财务详情
                accountDetailOld.setModityStatus("2");
                accountDetailOld.setModify(true);

                financeChequeDetailOld.setState("3"); //1-未使用,2-已使用,3-作废
                financeChequeDetailDao.update(financeChequeDetailOld);

                FinanceAccount financeAccountOld = financeAccountDao.get(financeChequeDetailOld.getAccountId_());
                BigDecimal balanceOld = financeAccountOld.getBalance().add(financeChequeDetailOld.getAmount());

                financeAccountOld.setBalance(balanceOld);
                financeAccountOld.setDebit(financeAccountOld.getDebit().subtract(financeChequeDetailOld.getAmount()));
                financeAccountDao.update(financeAccountOld);

                AccountPeriod accountPeriodMonthOld = accountService.getPeroidByType(financeAccountOld.getOrg_(), 2, financeAccountOld.getId(), 1);  //月结
                AccountPeriod accountPeriodDayOld = accountService.getPeroidByType(financeAccountOld.getOrg_(), 2, financeAccountOld.getId(), 2);//找到日结

                accountPeriodMonthOld.setDebit(accountPeriodMonthOld.getDebit().subtract(financeChequeDetailOld.getAmount()));
                accountPeriodMonthOld.setBalance(balanceOld);
                accountPeroidDao.update(accountPeriodMonthOld);

                accountPeriodDayOld.setDebit(accountPeriodDayOld.getDebit().subtract(financeChequeDetailOld.getAmount()));
                accountPeriodDayOld.setBalance(balanceOld);
                accountPeroidDao.update(accountPeriodDayOld);

                //添加金额为负
                AccountDetail accountDetail1 = new AccountDetail();
                accountDetail1.setOrg(user.getOrganization());
                accountDetail1.setAuditDate(new Date());
                accountDetail1.setBillAmount(accountDetailOld.getBillAmount());  //票面金额
                accountDetail1.setAuditorName(accountDetailOld.getAuditorName());//经手人
                accountDetail1.setMethod(accountDetailOld.getMethod());//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                accountDetail1.setMemo(accountDetailOld.getMemo());
                accountDetail1.setGenre(accountDetailOld.getGenre());
                accountDetail1.setCategoryDesc(accountDetailOld.getCategoryDesc());
                accountDetail1.setSummary(accountDetailOld.getSummary());
                accountDetail1.setPurpose(accountDetailOld.getPurpose());//用途
                accountDetail1.setOppositeCorp(accountDetailOld.getOppositeCorp());//付款单位
                accountDetail1.setAccountBank(accountDetailOld.getAccountBank());
                accountDetail1.setAccountantStatus(accountDetailOld.getAccountantStatus());//会计数据状态  1-未选择  2-已选择
                accountDetail1.setFid(accountDetailOld.getFid());
                accountDetail1.setAccountId(accountDetailOld.getAccountId());//账户外键
                accountDetail1.setType(accountDetailOld.getType());//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                accountDetail1.setBillDetail(accountDetailOld.getBillDetail());
                accountDetail1.setModityStatus("2");
                accountDetail1.setBusinessType(businessType);
                accountDetail1.setSource(accountDetailOld.getSource());
                accountDetail1.setBusiness(accountDetailOld.getBusiness());
                accountDetail1.setBusinessDate(accountDetailOld.getBusinessDate());
                accountDetail1.setBusinessHistory(accountDetailOld.getBusinessHistory());
//                BeanUtils.copyProperties(accountDetailOld,accountDetail1);
                accountDetail1.setDebit(accountDetailOld.getDebit().multiply(new BigDecimal(-1)));
                accountDetail1.setCreateDate(new Date());
                accountDetail1.setCreator(user.getUserID());
                accountDetail1.setCreateName(user.getUserName());
                accountDetail1.setBalance(balanceOld);
                accountDetail1.setSummary(accountDetailOld.getSummary());
                accountDetail1.setBillDate(new Date());
                accountDetail1.setPreviousId(accountDetailOld.getId());
                accountDetail1.setModify(true);
                accountDetailDao.save(accountDetail1);
                map.put("accountPeriodMonthOld",accountPeriodMonthOld);
                map.put("accountPeriodDayOld",accountPeriodDayOld);
//                map.put("status","1");  //成功     //后续有正确的金额
            }
            map.put("financeAccountBillOld",financeAccountBillOld);

        }
        return map;
    }

    private FinanceChequeDetailHistory getFinanceChequeDetailHistoryById(Integer chequeId){
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceChequeDetailHistory where chequeDetail=:chequeId";
        params.put("chequeId",chequeId);
        return (FinanceChequeDetailHistory) financeChequeDetailHistoryDao.getByHQLWithNamedParams(hql,params);
    }

    //查找上一次的修改
    private FinanceChequeDetailHistory getFinanceChequeDetailHistoryLast(FinanceChequeDetail financeChequeDetail, User user,Integer payId){
        FinanceChequeDetailHistory financeChequeDetailHistoryLast = getFinanceChequeDetailHistoryById(financeChequeDetail.getId());
        if (financeChequeDetailHistoryLast==null){
            financeChequeDetailHistoryLast = new FinanceChequeDetailHistory();
            financeChequeDetailHistoryLast.setAmount(financeChequeDetail.getAmount());
            financeChequeDetailHistoryLast.setOperation(financeChequeDetail.getOperation());
            financeChequeDetailHistoryLast.setType(financeChequeDetail.getType());
            financeChequeDetailHistoryLast.setAccount(financeChequeDetail.getAccount());
            financeChequeDetailHistoryLast.setAccountId(financeChequeDetail.getAccountId_());
            financeChequeDetailHistoryLast.setApproveStatus(financeChequeDetail.getApproveStatus());
            financeChequeDetailHistoryLast.setAuditDate(financeChequeDetail.getAuditDate());
            financeChequeDetailHistoryLast.setAuditor(financeChequeDetail.getAuditor());
            financeChequeDetailHistoryLast.setAuditorName(financeChequeDetail.getAuditorName());
            financeChequeDetailHistoryLast.setBankName(financeChequeDetail.getBankName());
            financeChequeDetailHistoryLast.setBankNo(financeChequeDetail.getBankNo());
            financeChequeDetailHistoryLast.setChequeNo(financeChequeDetail.getChequeNo());
            financeChequeDetailHistoryLast.setChequeReg(financeChequeDetail.getChequeReg_());
            financeChequeDetailHistoryLast.setExpireDate(financeChequeDetail.getExpireDate());
            financeChequeDetailHistoryLast.setMemo(financeChequeDetail.getMemo());
            financeChequeDetailHistoryLast.setOperateDate(financeChequeDetail.getOperateDate());
            financeChequeDetailHistoryLast.setReceiver(financeChequeDetail.getReceiver());
            financeChequeDetailHistoryLast.setState(financeChequeDetail.getState());
            financeChequeDetailHistoryLast.setReceiveDate(financeChequeDetail.getReceiveDate());
            financeChequeDetailHistoryLast.setPurpose(financeChequeDetail.getPurpose());
            financeChequeDetailHistoryLast.setOperator(financeChequeDetail.getOperator());
            financeChequeDetailHistoryLast.setReceiveCorp(financeChequeDetail.getReceiveCorp());
            financeChequeDetailHistoryLast.setSummary(financeChequeDetail.getSummary());
//            BeanUtils.copyProperties(financeChequeDetail,financeChequeDetailHistoryLast);
            financeChequeDetailHistoryLast.setCreateName(user.getUserName());
            financeChequeDetailHistoryLast.setCreateDate(new Date());
            financeChequeDetailHistoryLast.setCreator(user.getUserID());
            financeChequeDetailHistoryLast.setVersionNo(0);
            financeChequeDetailHistoryLast.setChequeDetail(financeChequeDetail.getId());
            financeChequeDetailHistoryDao.save(financeChequeDetailHistoryLast);
        }
        return financeChequeDetailHistoryLast;
    }

    private FinanceChequeDetailHistory addChequeDetailToChequeDetailHistory(FinanceChequeDetail financeChequeDetail,Integer previousId,Integer versionNo,User user){
        FinanceChequeDetailHistory financeChequeDetailHistory = new FinanceChequeDetailHistory();
        financeChequeDetailHistory.setReceiveCorp(financeChequeDetail.getReceiveCorp());
        financeChequeDetailHistory.setAmount(financeChequeDetail.getAmount());
        financeChequeDetailHistory.setApplyMemo(financeChequeDetail.getApplyMemo());
        financeChequeDetailHistory.setOperator(financeChequeDetail.getOperator());
        financeChequeDetailHistory.setPurpose(financeChequeDetail.getPurpose());
        financeChequeDetailHistory.setAccount(financeChequeDetail.getAccount());
        financeChequeDetailHistory.setAccountId(financeChequeDetail.getAccountId_());
        financeChequeDetailHistory.setApproveStatus(financeChequeDetail.getApproveStatus());
        financeChequeDetailHistory.setAuditDate(financeChequeDetail.getAuditDate());
        financeChequeDetailHistory.setAuditor(financeChequeDetail.getAuditor());
        financeChequeDetailHistory.setAuditorName(financeChequeDetail.getAuditorName());
        financeChequeDetailHistory.setBankName(financeChequeDetail.getBankName());
        financeChequeDetailHistory.setBankNo(financeChequeDetail.getBankNo());
        financeChequeDetailHistory.setChequeNo(financeChequeDetail.getChequeNo());
        financeChequeDetailHistory.setChequeReg(financeChequeDetail.getChequeReg_());
        financeChequeDetailHistory.setExpireDate(financeChequeDetail.getExpireDate());
        financeChequeDetailHistory.setMemo(financeChequeDetail.getMemo());
        financeChequeDetailHistory.setOperateDate(financeChequeDetail.getOperateDate());
        financeChequeDetailHistory.setType(financeChequeDetail.getType());
        financeChequeDetailHistory.setSummary(financeChequeDetail.getSummary());
        financeChequeDetailHistory.setState(financeChequeDetail.getState());
        financeChequeDetailHistory.setReceiver(financeChequeDetail.getReceiver());
        financeChequeDetailHistory.setReceiveDate(financeChequeDetail.getReceiveDate());
        financeChequeDetailHistory.setOperation(financeChequeDetail.getOperation());
        //        BeanUtils.copyProperties(financeChequeDetail,financeChequeDetailHistory);
        financeChequeDetailHistory.setCreateName(user.getUserName());
        financeChequeDetailHistory.setCreateDate(new Date());
        financeChequeDetailHistory.setCreator(user.getUserID());
        financeChequeDetailHistory.setVersionNo(versionNo);
        financeChequeDetailHistory.setPreviousId(previousId);
        financeChequeDetailHistory.setChequeDetail(financeChequeDetail.getId());
        financeChequeDetailHistoryDao.save(financeChequeDetailHistory);
        return financeChequeDetailHistory;
    }

    // 修改借款还款中的现金方式
    private Map<String,Object> updateCashDebit(User user,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method,BigDecimal money,Date paymentDate,String summary,Map<String,Object> map){
        Map<String,Object> map1 = new HashMap<>();

        //单纯现金
        FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(user.getOid(), 1);//备用金

        //修改前的数据处理
        map1 = updateBeforeHandle(user,payId,payHistoryId,previousId,methodOld,method,money,paymentDate,null,financeAccount.getId(),null,null,null,null,"0102",map);
        String status = (String) map1.get("status");
        if (!"1".equals(status)) {
            if (0 == financeAccount.getAccountStatus()) {
                map.put("status", "2"); //此账户已被冻结，不允许操作
                return map;
            }

            BigDecimal balance = financeAccount.getBalance().subtract(money);  //支出金额后的账户当前余额
            if (balance.doubleValue() < 0) {
                map.put("status", "3");  //此账户余额不足
                return map;
            }
            AccountPeriod accountPeriodMonth = new AccountPeriod();
            AccountPeriod accountPeriodDay = new AccountPeriod();
            if ("1".equals(methodOld)){
                accountPeriodMonth = (AccountPeriod) map1.get("accountPeriodMonthOld");  //月结
                accountPeriodDay = (AccountPeriod) map1.get("accountPeriodDayOld");//找到日结
            }else {
                accountPeriodMonth = accountService.getPeroidByType(financeAccount.getOrg_(), 1, financeAccount.getId(), 1);  //月结
                accountPeriodDay = accountService.getPeroidByType(financeAccount.getOrg_(), 1, financeAccount.getId(), 2);//找到日结
            }

            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setDebit(money);
            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setAuditDate(new Date());
            accountDetail.setBillAmount(money);  //票面金额
            accountDetail.setMethod("1");  //0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setAccountBank(financeAccount.getBankName());
            accountDetail.setAuditorName(user.getUserName());
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setBusinessDate(paymentDate);
            accountDetail.setBusiness(payId);
            accountDetail.setSource("2");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)
            accountDetail.setBalance(balance);
            accountDetail.setBusinessType("0102");//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(summary);
            accountDetail.setBillDate(new Date());
            accountDetail.setModify(true);

            financeAccount.setDebit(financeAccount.getDebit().add(money));//计入账户支出
            financeAccount.setBalance(balance);//从余额减去支出

            accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().add(money));
            accountPeriodMonth.setBalance(accountPeriodMonth.getBalance().subtract(money));

            accountPeriodDay.setDebit(accountPeriodDay.getDebit().add(money));
            accountPeriodDay.setBalance(accountPeriodDay.getBalance().subtract(money));

            accountPeroidDao.update(accountPeriodMonth);//月结
            accountPeroidDao.update(accountPeriodDay);//日结
            financeAccountDao.update(financeAccount);//账户
            accountDetailDao.save(accountDetail);//明细

            if ("1".equals(methodOld) || "5".equals(methodOld)) {
                AccountDetail accountDetailOld = (AccountDetail) map1.get("accountDetailOld");  //修改前的详情
                accountDetail.setPreviousId(accountDetailOld.getId());
                accountDetailDao.update(accountDetail);
                AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld, payId, previousId, user,"0102"); //上一次的修改
                addAccountDetailToDetailHistory(accountDetail, payId, payHistoryId, user, accountDetailHistoryLast.getId(), accountDetailHistoryLast.getVersionNo() + 1);
            } else {
                addAccountDetailToDetailHistory(accountDetail, payId, payHistoryId, user, null, 1);
            }
        }
        map.put("status","1");  //成功
        return map;
    }

    //借款还款(借出去的款)-支出的转账支票或承兑汇票的修改
    private Map<String, Object> updateReturnDebit(User user,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method,BigDecimal money,
        Date paymentDate,String withinOrAbroad,Integer checkId,Integer accountId,Date receiveDate,String receiver,String operator,Date expireDate,
                                                  String businessType,Map<String,Object> map,String oppositeCorp,String payer,String summary){
        Map<String,Object> map1 = new HashMap<>();
        //修改前的数据处理
        map1 = updateBeforeHandle(user,payId,payHistoryId,previousId,methodOld,method,money,paymentDate,checkId,accountId,receiveDate,receiver,operator,expireDate,businessType,map);
        String status = (String) map1.get("status");
        //3代表转账支票
//内部or外部   1-内部，0-外部
        if (!"1".equals(status)) {
            if ("3".equals(method) && "1".equals(withinOrAbroad)) {  //转账支票的内部支票

                //支出的内部银行转账支票
                FinanceAccount financeAccount = financeAccountDao.get(accountId);
                if (0 == financeAccount.getAccountStatus()) {
                    map.put("status", "2"); //此账户已被冻结，不允许操作
                    return map;
                }

                BigDecimal balance = financeAccount.getBalance().subtract(money);  //支出后账户的当前余额
                if (balance.doubleValue() < 0) {
                    map.put("status", "3");//余额不足
                    return map;
                }
                FinanceChequeDetail financeChequeDetail = financeChequeDetailDao.get(checkId);
                financeChequeDetail.setReceiver(receiver);//接收经手人
                financeChequeDetail.setAmount(money);
                financeChequeDetail.setBillAmount(money);  //票面金额
                financeChequeDetail.setOperator(operator);//支付经手人
                financeChequeDetail.setFinancialHandling(user.getUserName());//财务经手人
                financeChequeDetail.setExpireDate(expireDate);//支票到期日
                financeChequeDetail.setReceiveDate(receiveDate);//接收日期
                financeChequeDetail.setState("2");//1-未使用,2-已使用,3-作废
                financeChequeDetail.setModityStatus("2");
                financeChequeDetail.setUpdateDate(new Date());
                financeChequeDetail.setUpdator(user.getUserID());
                financeChequeDetail.setUpdateName(user.getUserName());
                financeChequeDetail.setReceiveCorp(oppositeCorp);
                financeChequeDetail.setSummary(summary);
                financeChequeDetail.setPurpose(summary);
//            financeChequeDetailDao.update(financeChequeDetail);

                //支票明细
                FinanceAccountBill financeAccountBill = new FinanceAccountBill();
                financeAccountBill.setOperatorName(user.getUserName());//经手人
                financeAccountBill.setCreateDate(new Date());
                financeAccountBill.setCreateName(user.getUserName());
                financeAccountBill.setCreator(user.getUserID());
                financeAccountBill.setType("2");//1-收入，2-支出
                financeAccountBill.setAmount(money);//金额
                financeAccountBill.setBillAmount(money);  //票面金额
                financeAccountBill.setBillNo(financeChequeDetail.getChequeNo());//发票号码
                financeAccountBill.setCheque(financeChequeDetail);//转账支票外键
                financeAccountBill.setOrg(user.getOrganization());
                financeAccountBill.setModityStatus("2");
                financeAccountBill.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                financeAccountBill.setBusiness(payId);
                financeAccountBill.setOccurrenceDate(paymentDate);  //借款中的付款时间
                financeAccountBill.setSource("2");
                financeAccountBill.setBusinessDate(new Date());
                financeAccountBill.setBusinessType(businessType);
                financeAccountBill.setOppositeCorp(oppositeCorp);
                financeAccountBill.setSummary(summary);
                financeAccountBill.setPurpose(summary);
                financeAccountBill.setBillDate(new Date());
                financeAccountBill.setModify(true);

                AccountPeriod accountPeriodMonth =accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());//月结;
                AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结
                FinanceAccountBill financeAccountBillOld = new FinanceAccountBill();
                //改后的正确的财务明细
                AccountDetail accountDetail = new AccountDetail();
                accountDetail.setDebit(money);
                accountDetail.setBalance(balance);
                accountDetail.setCreateDate(new Date());
                accountDetail.setCreator(user.getUserID());
                accountDetail.setCreateName(user.getUserName());
                accountDetail.setOrg(user.getOrganization());
                accountDetail.setAuditDate(new Date());
                accountDetail.setAuditorName(user.getUserName());//经手人
                accountDetail.setBillAmount(money);  //票面金额
                accountDetail.setMethod(method);
                accountDetail.setFid(financeAccount.getId().toString());
                accountDetail.setAccountId(financeAccount);//账户外键
                accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
                accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                accountDetail.setBusiness(payId);
                accountDetail.setSource("2");
                accountDetail.setBusinessDate(paymentDate);  //借款时的付款日期
                accountDetail.setBusinessType(businessType);
                accountDetail.setSummary(summary);
                accountDetail.setPurpose(summary);
                accountDetail.setBillDate(new Date());
                accountDetail.setModify(true);

                financeAccount.setDebit(financeAccount.getDebit().add(money));//计入账户支出
                financeAccount.setBalance(balance);//从余额减去支出

                accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().add(money));
                accountPeriodMonth.setBalance(balance);

                accountPeriodDay.setDebit(accountPeriodDay.getDebit().add(money));
                accountPeriodDay.setBalance(balance);

                accountPeroidDao.update(accountPeriodMonth);//月结
                accountPeroidDao.update(accountPeriodDay);//日结
                financeAccountDao.update(financeAccount);//账户
                financeAccountBillDao.save(financeAccountBill);
                accountDetail.setBillDetail(financeAccountBill);
                accountDetailDao.save(accountDetail);//明细

                if ("3".equals(methodOld) || "4".equals(methodOld)) {
                    FinanceChequeDetailHistory financeChequeDetailHistory1 = new FinanceChequeDetailHistory();
                    FinanceChequeDetailHistory financeChequeDetailHistoryLast1 = new FinanceChequeDetailHistory();
                    financeAccountBillOld = (FinanceAccountBill) map1.get("financeAccountBillOld");
                    financeAccountBill.setPreviousId(financeAccountBillOld.getId());
                    if (financeAccountBillOld.getCheque() != null) {
                        financeChequeDetailHistoryLast1 = getFinanceChequeDetailHistoryLast(financeChequeDetail, user, payId); //上一次修改
                        financeChequeDetailHistory1 = addChequeDetailToChequeDetailHistory(financeChequeDetail, financeChequeDetailHistoryLast1.getId(), financeChequeDetailHistoryLast1.getVersionNo() + 1, user);
                    } else {
                        financeChequeDetailHistory1 = addChequeDetailToChequeDetailHistory(financeChequeDetail, null, 0, user);
                    }

                    FinanceAccountBillHistory financeAccountBillHistoryLast = getFinanceAccountBillHistoryLast(financeAccountBillOld, user, payId, previousId, null, financeChequeDetailHistoryLast1.getId());//上一次修改
                    addBillToBillHistory(financeAccountBill, user, payId, payHistoryId, financeAccountBillHistoryLast, null, financeChequeDetailHistory1.getId());
                } else {
                    if ("1".equals(methodOld) || "5".equals(methodOld)) {
                        AccountDetail accountDetailOld = (AccountDetail) map1.get("accountDetailOld");  //修改前的详情
                        accountDetail.setPreviousId(accountDetailOld.getId());
                        accountDetailDao.update(accountDetail);
                    }
                    FinanceChequeDetailHistory financeChequeDetailHistory1 = addChequeDetailToChequeDetailHistory(financeChequeDetail, null, 0, user); //此次修改
                    getFinanceAccountBillHistoryLast(financeAccountBill, user, payId, payHistoryId, null, financeChequeDetailHistory1.getId());//此次修改
                }

            } else {    //支出的外部转账支票 或者是 承兑汇票
                if ("3".equals(methodOld) || "4".equals(methodOld)) {
                    FinanceReturn fr = financeReturnDao.get(checkId);
                    fr.setPartnerName(receiver);
                    fr.setOperatorName(operator);
                    fr.setPayer(payer);
                    fr.setState("4");//1-有效,2-存入银行,3-作废,4-已使用
                    fr.setUpdateDate(new Date());
                    fr.setUpdator(user.getUserID());
                    fr.setUpdateName(user.getUserName());
                    financeReturnDao.update(fr);

                    FinanceAccountBill financeAccountBillOld1 = (FinanceAccountBill) map1.get("financeAccountBillOld");  //原来的
                    FinanceAccountBill f = financeAccountBillOld1;  //原来的基础上更新
                    f.setFinanceReturn(fr);
                    f.setAmount(fr.getAmount());//金额
                    f.setBillAmount(fr.getAmount());  //票面金额
                    f.setBillNo(fr.getReturnNo());//发票号码
                    f.setFinanceReturn(fr);//回款票据外键
                    f.setOrg(fr.getOrg());
                    f.setBusinessDate(new Date());
                    f.setOppositeCorp(oppositeCorp);  //收款单位
                    f.setOccurrenceDate(paymentDate);
                    f.setModityStatus("2");
                    f.setBusinessType(businessType);
                    f.setSummary(summary);
                    f.setPurpose(summary);
                    f.setBillDate(new Date());
                    f.setModify(true);

                    FinanceReturnHistory financeReturnHistory1 = new FinanceReturnHistory();
                    FinanceReturnHistory financeReturnHistoryLast1 = new FinanceReturnHistory();
                    if (financeAccountBillOld1.getFinanceReturn() != null) {
                        financeReturnHistoryLast1 = getFinanceReturnHistoryLast(fr); //上一次修改
                        financeReturnHistory1 = addReturnToReturnHistory(fr, financeReturnHistoryLast1.getId(), financeReturnHistoryLast1.getVersionNo() + 1, user);  //此次修改
                    } else {
                        financeReturnHistory1 = addReturnToReturnHistory(fr, null, 0, user);  //此次修改
                    }
                    FinanceAccountBillHistory financeAccountBillHistoryLast = getFinanceAccountBillHistoryLast(financeAccountBillOld1, user, payId, previousId, financeReturnHistoryLast1.getId(), null);//上一次修改

                    addBillToBillHistory(f, user, payId, payHistoryId, financeAccountBillHistoryLast, financeReturnHistory1.getId(), null);//此次修改
                } else {
                    FinanceReturn fr = financeReturnDao.get(checkId);
                    fr.setState("4");//1-有效,2-存入银行,3-作废,4-已使用
                    fr.setPayer(payer);
                    fr.setUpdateDate(new Date());
                    fr.setUpdator(user.getUserID());
                    fr.setUpdateName(user.getUserName());
                    financeReturnDao.update(fr);

                    //支票明细
                    FinanceAccountBill f = new FinanceAccountBill();
                    f.setCreateDate(new Date());
                    f.setCreateName(user.getUserName());
                    f.setCreator(user.getUserID());
                    f.setType("2");//1-收入，2-支出
                    f.setAmount(fr.getAmount());//金额
                    f.setBillAmount(fr.getAmount());  //票面金额
                    f.setBillNo(fr.getReturnNo());//发票号码
                    f.setFinanceReturn(fr);//回款票据外键
                    f.setOrg(fr.getOrg());
                    f.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                    f.setSource("2");
                    f.setBusiness(payId);
                    f.setBusinessDate(new Date());
                    f.setOccurrenceDate(paymentDate);
                    f.setModityStatus("2");
                    f.setBusinessType(businessType);
                    f.setOppositeCorp(oppositeCorp);
                    f.setSummary(summary);
                    f.setPurpose(summary);
                    f.setBillDate(new Date());
                    f.setModify(true);
                    dataService.saveFinanceAccountBill(f);

                    if ("3".equals(methodOld) || "4".equals(methodOld)) {
                        FinanceAccountBill financeAccountBillOld = (FinanceAccountBill) map1.get("financeAccountBillOld");
                        f.setPreviousId(financeAccountBillOld.getId());
                    }

                    FinanceReturnHistory financeReturnHistory1 = getFinanceReturnHistoryLast(fr); //此次修改
                    getFinanceAccountBillHistoryLast(f, user, payId, payHistoryId, financeReturnHistory1.getId(), null);//此次修改
                }
            }
        }
        map.put("status","1");  //成功
        return map;
    }

    //借款还款支出的银行转账修改
    private Map<String,Object> updateBankDebit(User user,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method,BigDecimal money,Date paymentDate,Integer accountId,String summary,Map<String,Object> map){
        Map<String,Object> map1 = new HashMap<>();

        //修改前的数据处理
        map1 = updateBeforeHandle(user,payId,payHistoryId,previousId,methodOld,method,money,paymentDate,null,accountId,null,null,null,null,"0102",map);
        String status = (String) map1.get("status");
        if (!"1".equals(status)) {
            AccountDetail accountDetailOld = (AccountDetail) map1.get("accountDetailOld");  //原来的明细详情

            FinanceAccount financeAccount = financeAccountDao.get(accountId);
            if (0 == financeAccount.getAccountStatus()) {
                map.put("status", "2");  //此账户已被冻结，不允许操作
                return map;
            }

            BigDecimal balance = financeAccount.getBalance().subtract(money);  //支出后账户的当前余额
            if (balance.doubleValue() < 0) {
                map.put("status", "3"); //余额不足
                return map;
            }

            AccountPeriod accountPeriodMonth = accountService.getPeroidByType(financeAccount.getOrg_(), financeAccount.getAccountType(), financeAccount.getId(), 1);//月结
            AccountPeriod accountPeriodDay = accountService.getPeroidByType(financeAccount.getOrg_(), financeAccount.getAccountType(), financeAccount.getId(), 2);//找到日结
            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setDebit(money);
            accountDetail.setBalance(balance);
            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setAuditDate(new Date());
            accountDetail.setMethod(method);
            accountDetail.setFid(financeAccount.getId().toString());
            accountDetail.setBillAmount(money);  //票面金额
            accountDetail.setAuditorName(user.getUserName());
            accountDetail.setAccountId(financeAccount);//账户外键
            accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
            if (accountDetailOld!=null) {
                accountDetail.setPreviousId(accountDetailOld.getId());
                accountDetail.setVersionNo(accountDetailOld.getVersionNo() == null ? 0 : accountDetailOld.getVersionNo() + 1);
            }
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail.setBusiness(payId);
            accountDetail.setSource("2");
            accountDetail.setBusinessDate(paymentDate);  //借款时的付款日期
            accountDetail.setBusinessType("0102");
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(summary);
            accountDetail.setBillDate(new Date());
            accountDetail.setModify(true);

            financeAccount.setDebit(financeAccount.getDebit().add(money));//计入账户支出
            financeAccount.setBalance(balance);//从余额减去支出

            accountPeriodMonth.setDebit(accountPeriodMonth.getDebit().add(money));
            accountPeriodMonth.setBalance(balance);

            accountPeriodDay.setDebit(accountPeriodDay.getDebit().add(money));
            accountPeriodDay.setBalance(balance);

            accountPeroidDao.update(accountPeriodMonth);//月结
            accountPeroidDao.update(accountPeriodDay);//日结
            financeAccountDao.update(financeAccount);//账户
            accountDetailDao.save(accountDetail);//明细

            if (accountDetailOld==null){
                accountDetailOld = accountDetail;
            }
            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld, payId, previousId, user,"0102");
            addAccountDetailToDetailHistory(accountDetail, payId, payHistoryId, user, accountDetailHistoryLast.getId(), accountDetailHistoryLast.getVersionNo() + 1);
        }
        map.put("status","1"); //成功
        return map;
    }

    /**
     * 查询某一条借款收入/支出的详情
     * @param business  财务明细id/财务票据id
     * @param method  收入方式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param type 1-借款  2-还款
     * @return
     */
    @Override
    public Map<String, Object> getLoanCreditDetail(Integer business, String method,Integer type) {
        Map<String,Object> map = new HashMap<>();
        if("1".equals(method) || "5".equals(method)){    //现金/银行存款
            AccountDetail accountDetail = getAccountDetailByBusiness(business,type,null);
            if (accountDetail!=null) {
                map.put("memo", accountDetail.getMemo());  //备注、说明
                map.put("receiveAccountDate", accountDetail.getReceiveAccountDate());   //到账日期
                map.put("accountId", accountDetail.getAccountId_());   //收款银行账户id
                map.put("accountBank", accountDetail.getAccountBank());  //银行 账号+银行名
                map.put("partnerName",accountDetail.getPartnerName());   //合作方经手人
                map.put("operatorName",accountDetail.getAuditorName());   //经手人
                map.put("paymentDate",accountDetail.getBusinessDate());
                map.put("oppositeAccount",accountDetail.getOppositeAccount());   //账户名称
                map.put("oppositeBankcode",accountDetail.getOppositeBankcode());   //对方银行账号
                map.put("oppositeBankno",accountDetail.getOppositeBankno());   //开户行
                map.put("receiver", accountDetail.getPartnerName());  //收款经手人
                map.put("arriveDate", accountDetail.getReceiveAccountDate());  //到账日期
                map.put("summary", accountDetail.getSummary());  //摘要
                map.put("purpose", accountDetail.getPurpose());  //用途
                map.put("oppositeCorp",accountDetail.getOppositeCorp());  //收款单位(付款单位)
            }
        }else if ("3".equals(method) || "4".equals(method)){   //  承兑汇票/转账支票
            FinanceAccountBill financeAccountBill = getFinanceAccountBillByBusiness(business,type,null);
            if (financeAccountBill!=null) {
                FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();
                FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                if (financeReturn != null) {  //承兑汇票or外部支票
                    map.put("receiveDate", financeReturn.getReceiveDate());  //收到支票日期
                    map.put("chequeId",financeReturn.getId()); //票据id
                    map.put("returnNo", financeReturn.getReturnNo());   //支票号
                    map.put("expireDate", financeReturn.getExpireDate());  //支票到期日
                    map.put("originalCorp", financeReturn.getOriginalCorp());  //出具支票单位
                    map.put("bankName", financeReturn.getBankName());  //出具支票银行
                    map.put("withinOrAbroad", "0");  //1-内部 0-外部
                    map.put("returnState",financeReturn.getState()); //1-有效,2-存入银行,3-作废，4-已支出
                    map.put("memo", financeReturn.getMemo());  //备注、说明
                    map.put("partnerName",financeReturn.getPartnerName());   //合作方经手人
                    map.put("operatorName",financeReturn.getOperatorName());   //经手人
                    map.put("paymentDate",financeAccountBill.getOccurrenceDate());
                    map.put("payer",financeReturn.getPayer());   //付款单位
                    map.put("oppositeCorp",financeAccountBill.getOppositeCorp());  //收款单位
//                    map.put("account",financeReturn.getAccout()); //存入银行帐号
                    map.put("saveBankAccount",financeReturn.getSaveBankName()+"+"+financeReturn.getAccout()); //存入支票/汇票的银行名称+存入银行帐号

                } else if (financeChequeDetail != null) {    //支出时内部支票
                    map.put("receiveDate", financeChequeDetail.getReceiveDate());  //收到支票日期
                    map.put("chequeId",financeChequeDetail.getId()); //票据id
                    map.put("returnNo", financeChequeDetail.getChequeNo());   //支票号
                    map.put("expireDate", financeChequeDetail.getExpireDate());  //支票到期日
                    map.put("receiver", financeChequeDetail.getReceiver());  //接收经手人
                    map.put("operator", financeChequeDetail.getOperator());  //支付经手人
                    map.put("bankName", financeChequeDetail.getBankName());  //银行
                    map.put("accountId",financeChequeDetail.getAccountId_());   //收款银行账户id
                    map.put("account",financeChequeDetail.getAccount());  //账户
                    map.put("withinOrAbroad", "1");  //1-内部 0-外部
                    map.put("memo", financeChequeDetail.getMemo());  //备注、说明
                    map.put("partnerName", financeChequeDetail.getReceiver());   //合作方经手人
                    map.put("operatorName",financeChequeDetail.getOperator());   //经手人
                    map.put("paymentDate",financeAccountBill.getOccurrenceDate());
                    map.put("oppositeCorp",financeAccountBill.getOppositeCorp());  //收款单位
                }
                map.put("summary", financeAccountBill.getSummary());  //摘要
                map.put("purpose", financeAccountBill.getPurpose());  //用途
            }
        }
        return map;
    }

    public AccountDetail getAccountDetailByBusiness(Integer business,Integer type,String businessType){
        Map<String,Object> params = new HashMap<>();
        String hql = "from AccountDetail where id = (select max(id) from AccountDetail where business=:business and source='2'";
        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            hql+=" and businessType=:businessType)";
            params.put("businessType",businessType);
        }else {
            switch (type) {
                case 1:
                    hql += " and businessType='0101')";  //借入
                    break;
                case 2:
                    hql += " and businessType='0102')";   //还款
                    break;
                case 3:
                    hql += " and businessType='0103')";  //借出
                    break;
                case 4:
                    hql += " and businessType='0104')";   //收款
                    break;
            }
        }
        params.put("business",business);
        return (AccountDetail) accountDetailDao.getByHQLWithNamedParams(hql,params);
    }

    public FinanceAccountBill getFinanceAccountBillByBusiness(Integer business,Integer type,String businessType) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountBill where id = (select max(id) from FinanceAccountBill where business=:business and source='2'";
        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            hql+=" and businessType=:businessType)";
            params.put("businessType",businessType);
        }else {
            switch (type) {
                case 1:
                    hql += " and businessType='0101')";  //借款
                    break;
                case 2:
                    hql += " and businessType='0102')";   //还款
                    break;
                case 3:
                    hql += " and businessType='0103')";  //借出
                    break;
                case 4:
                    hql += " and businessType='0104')";   //收款
                    break;
            }
        }
        params.put("business",business);
        return (FinanceAccountBill) financeAccountBillDao.getByHQLWithNamedParams(hql,params);
    }

//    @Override
    private AccountDetailHistory getAccountDetailHistoryByManyId(Integer detailId,Integer business,Integer businessHistory,String businessType) {
        Map<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from AccountDetailHistory");
        List<String> where = new ArrayList<>();
        if (detailId!=null){
            where.add(" detailId_=:detailId ");
            params.put("detailId",detailId);
        }
        if (business!=null){
            where.add(" business=:business ");
            params.put("business",business);
        }
        if (businessHistory!=null){
            where.add(" businessHistory=:businessHistory ");
            params.put("businessHistory",businessHistory);
        }
        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            where.add("businessType=:businessType");
            params.put("businessType",businessType);
        }
        if (!where.isEmpty()) {
            hql.append(" where ").append(org.apache.commons.lang3.StringUtils.join(where, " and "));
        }
        return (AccountDetailHistory) accountDetailHistoryDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    /**
     *查看某一条借款还款、收款的修改记录详情（财务借款收入/支出的修改详情）
     * @param businessHistory 历史id
     * @param method 收入方式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param businessType  0101-借款收入 0102-借款还款  0103-借款支出  0104-借款收款
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> getLoanPayUpdateDetail(Integer businessHistory, String method,String businessType, Map<String, Object> map) {
        if ("3".equals(method) || "4".equals(method)){
            FinanceAccountBillHistory financeAccountBillHistory = getFinanceAccountBillHistoryById(null,businessHistory,businessType);
            if (financeAccountBillHistory!=null) {
                if (financeAccountBillHistory.getChequeHistory() != null) {   //支出的是转账支票的内部支票
                    FinanceChequeDetailHistory financeChequeDetailHistory = financeChequeDetailHistoryDao.get(financeAccountBillHistory.getChequeHistory());
                    map.put("method", 3);  //转账支票
                    map.put("chequeId", financeChequeDetailHistory.getChequeDetail());//支票id
                    map.put("returnNo", financeChequeDetailHistory.getChequeNo());  //支票号
                    map.put("accountId", financeChequeDetailHistory.getAccountId()); //银行账户id
                    map.put("account",financeChequeDetailHistory.getAccount());  //账号
                    map.put("accountBank", financeChequeDetailHistory.getBankName());  //银行名称
                    map.put("receiver", financeChequeDetailHistory.getReceiver());  //接收经手人
                    map.put("operator", financeChequeDetailHistory.getOperator());  //支付经手人
                    map.put("expireDate", financeChequeDetailHistory.getExpireDate()!=null?financeChequeDetailHistory.getExpireDate().getTime():financeChequeDetailHistory.getExpireDate());  //支票或汇票到期日
                    map.put("receiveDate", financeChequeDetailHistory.getReceiveDate().getTime());  //收到支票或汇票日期
                    map.put("withinOrAbroad", "1");  //1-内部 0-外部
                    map.put("memo", financeChequeDetailHistory.getMemo());  //备注、说明
                    map.put("partnerName", financeChequeDetailHistory.getReceiver());   //合作方经手人
                    map.put("operatorName",financeChequeDetailHistory.getOperator());   //经手人
                    map.put("paymentDate",financeAccountBillHistory.getOccurrenceDate());
                } else if (financeAccountBillHistory.getReturnBillHistory() != null) {  //支出的是承兑汇票/转账支票的外部支票
                    FinanceReturnHistory financeReturnHistory = financeReturnHistoryDao.get(financeAccountBillHistory.getReturnBillHistory());
                    map.put("method", 4);  //承兑汇票
//                    map.put("checkId", financeReturnHistory.getFinanceReturnId());
//                    map.put("billNo", financeReturnHistory.getReturnNo());  //支票号
//                    map.put("billSource", financeReturnHistory.getOriginalCorp());  //出具支票或汇票单位
//                    map.put("billBank", financeReturnHistory.getBankName());  //出具支票或汇票银行
//                    map.put("arriveDate", financeReturnHistory.getReceiveAccountDate()); //到账日期
//                    map.put("billEndDate", financeReturnHistory.getExpireDate());  //支票或汇票到期日
//                    map.put("billReceiveDate", financeReturnHistory.getReceiveDate());  //收到支票或汇票日期
//                    map.put("billType", "0");  //1-内部 0-外部
                    if (financeReturnHistory.getReceiveDate()!=null){
                        map.put("receiveDate", financeReturnHistory.getReceiveDate().getTime());  //收到支票日期
                    }else {
                        map.put("receiveDate", financeReturnHistory.getReceiveDate());  //收到支票日期
                    }
//                    map.put("receiveDate", financeReturnHistory.getReceiveDate());  //收到支票日期
                    map.put("chequeId",financeReturnHistory.getId()); //票据id
                    map.put("returnNo", financeReturnHistory.getReturnNo());   //支票号
                    map.put("expireDate", financeReturnHistory.getExpireDate()!=null?financeReturnHistory.getExpireDate().getTime():financeReturnHistory.getExpireDate());  //支票到期日
                    map.put("originalCorp", financeReturnHistory.getOriginalCorp());  //出具支票单位
                    map.put("bankName", financeReturnHistory.getBankName());  //出具支票银行
                    map.put("withinOrAbroad", "0");  //1-内部 0-外部
                    map.put("returnState",financeReturnHistory.getState()); //1-有效,2-存入银行,3-作废，4-已支出
                    map.put("memo", financeReturnHistory.getMemo());  //备注、说明
                    map.put("partnerName",financeReturnHistory.getPartnerName());   //合作方经手人
                    map.put("operatorName",financeReturnHistory.getOperatorName());   //经手人
                    map.put("paymentDate",financeAccountBillHistory.getOccurrenceDate());
                    map.put("payer",financeReturnHistory.getPayer()); //付款单位
                }
//                map.put("paymentDate", financeAccountBillHistory.getOccurrenceDate());
            }
        }else {  // 支出的是现金/银行转账
            AccountDetailHistory accountDetailHistory = getAccountDetailHistoryByManyId(null,null,businessHistory,businessType);
            if (accountDetailHistory!=null) {
                map.put("paymentDate", accountDetailHistory.getBusinessDate());
                map.put("method", accountDetailHistory.getMethod());
                map.put("accountId",accountDetailHistory.getAccountId());  //银行id
                map.put("receiveBank",accountDetailHistory.getAccountBank());  //银行名称
                map.put("arriveDate",accountDetailHistory.getReceiveAccountDate());  //到账日期
                map.put("receiveOperatorName",accountDetailHistory.getPartnerName());  //收款经手人
                map.put("receiveAccountDate", accountDetailHistory.getReceiveAccountDate());   //到账日期
                map.put("memo", accountDetailHistory.getMemo());  //备注、说明
                map.put("partnerName",accountDetailHistory.getPartnerName());   //合作方经手人
                map.put("operatorName",accountDetailHistory.getAuditorName());   //经手人
                map.put("oppositeAccount",accountDetailHistory.getOppositeAccount());   //账户名称
                map.put("oppositeBankcode",accountDetailHistory.getOppositeBankcode());   //对方银行账号
                map.put("oppositeBankno",accountDetailHistory.getOppositeBankno());   //开户行
                map.put("receiver", accountDetailHistory.getPartnerName());  //收款经手人
            }
        }
        return map;
    }

    /**
     * 借款的收款（收入）
     * @param userId  登陆人id
     * @param business  收款id
     * @param method 还款/收款方式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
     * @param money  金额
     * @param accountId  账户id
     * @param receiveDate  接受日期
     * @param expireDate  支票到期日
     * @param operator  接收经手人
     * @param receiver   支付经手入
     * @param paymentDate  付款日期
     * @param oppositeCorp   收款单位
     * @param payer  付款单位
     * @return
     */
    @Override
    public Map<String, Object> loanReceivables(Integer userId, Integer business, String method, Double money, Integer accountId, Date receiveDate,
           Date expireDate, String operator, String receiver, Date paymentDate,String returnNo,String originalCorp,String bankName,String oppositeCorp,String payer) {
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态
        User user = userDao.get(userId);
        Organization org = orgDao.get(user.getOid());
        map.put("status",status);
        String summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+"公司向"+payer+"收款";
        if ("1".equals(method)){   //单纯现金
            addCashCredit(business,money,org.getId(),user,null,"1",paymentDate,operator,receiver,"0104",summary,map);  //收入、支出 现金
        }else if ("3".equals(method)||"4".equals(method)){  //转账支票或承兑汇票
            addReturn(org,user,accountId,business,method,new BigDecimal(money),receiveDate,returnNo,expireDate,originalCorp,bankName,null,"1",operator,receiver,null,paymentDate,null,"0104",oppositeCorp,payer,summary,map);  //收入的转账支票或承兑汇票
        }else if ("5".equals(method)){  //银行转账
            addBank(org,user,business,method,money,receiveDate,accountId,null,"1",operator,receiver,"0104",null,null,null,summary,map);  //收入的转账支票或承兑汇票
        }

        return map;
    }

    /**
     * 修改新增借款的支出类型
     * @param userId 操作人id
     * @param business 借款id
     * @param businessHistory  借款历史id
     * @param previousId  借款历史修改前id
     * @param method  本金型式（1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐）
     * @param money  金额
     * @param receiveAccountDate  到账日期（收到支票日期  yyyy-MM-dd HH:mm:ss）
     * @param accountId  账户id
     * @param returnNo  支票号
     * @param expireDate 支票到期日（汇票到期日  yyyy-MM-dd HH:mm:ss）
     * @param originalCorp  出具支票单位（原始出具汇票单位）
     * @param bankName  出具支票银行（出具汇票银行）
     * @param memo  备注
     * @param operatorName 经手人（如果是借入，此经手人为收款经手人；如果为借出，此经收人为付款经手人）
     * @param partnerName  合作方经手人姓名（如果是借入，此经手人为付款经手人；如果为借出，此经收人为收款经手人）
     * @param paymentDate  付款日期
     * @param returnId  支票id
     * @param withinOrAbroad  转账支票时使用，1-内部 0-外部
     * @return
     */
    @Override
    public Map<String, Object> updateLoanDebit(Integer userId, Integer business, Integer businessHistory, Integer previousId, String method, Double money, Date receiveAccountDate,
                                               Integer accountId, String returnNo, Date expireDate, String originalCorp, String bankName, String memo, String operatorName, String partnerName,
                                               Date paymentDate, Integer returnId, String withinOrAbroad,String oppositeAccount,String oppositeBankno,String oppositeBankcode,String oppositeCorp,String payer) {
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态  0-失败 1-成功 2-选择的账户被冻结 3-账户余额不够冲减
        User user = userDao.get(userId);
        Organization org = orgDao.get(user.getOid());
        map.put("status",status);
        String summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+oppositeCorp+"向公司借款";
        if (receiveAccountDate==null){
            receiveAccountDate = paymentDate;
        }
        if (business!=null) {
            if ("1".equals(method)) {   //单纯现金支出修改
                map = updateLoanCashDebit(business,businessHistory,previousId,BigDecimal.valueOf(money), org, user, memo,operatorName,partnerName,paymentDate,summary,map);  //收入现金
            } else if ("3".equals(method) || "4".equals(method)) {  //转账支票或承兑汇票
                map = updateReturnDebit(user,business,businessHistory,previousId,method,method,BigDecimal.valueOf(money),paymentDate,withinOrAbroad,returnId,
                        accountId,receiveAccountDate,partnerName,operatorName,expireDate,"0103",map,oppositeCorp,payer,summary);
//                map = updateLoanReturnDebit(user,business,businessHistory,previousId,method,BigDecimal.valueOf(money),receiveAccountDate,returnNo,expireDate,originalCorp,bankName,memo,withinOrAbroad,map);  //收入的转账支票或承兑汇票
            } else if ("5".equals(method)) {  //银行转账
                map = updateLoanBankDebit(user,business,businessHistory,previousId,BigDecimal.valueOf(money),receiveAccountDate,accountId,memo,map,operatorName,partnerName,paymentDate,oppositeAccount,oppositeBankno,oppositeBankcode,summary);  //银行转账
            }
        }
        return map;
    }

    private Map<String,Object> updateLoanCashDebit(Integer business,Integer businessHistory,Integer previousId,BigDecimal money,Organization org,User user,String memo,String operatorName,String partnerName,Date paymentDate,String summary,Map<String,Object> map){
        FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(org.getId(), 1);//备用金
        AccountDetail accountDetailOld = getAccountDetailByBusiness(business,3,null);
        if (money.compareTo(accountDetailOld.getDebit())!=0) {    //金额修改了
            if (0 == financeAccount.getAccountStatus()) {
                map.put("status","2");//此账户已被冻结，不允许操作
                return map;
            }
            AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(),new Date());  //1：月结
            AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

            //当前余额=账户的余额+原来的支出-现在的支出
            BigDecimal balance = financeAccount.getBalance().add(accountDetailOld.getDebit()).subtract(money);  //余额是否为0
            if (balance.doubleValue()>=0.00){    //够冲减
                //账务明细表里添加一条新数据(金额为负)
                financeAccountService.saveAccountDetail(user,org,financeAccount,balance,null,"4",accountDetailOld.getSummary(),"1",operatorName,"2",business,"2",accountDetailOld.getDebit().multiply(new BigDecimal(-1)),"2",null,"1",accountDetailOld.getMemo(),"0103",partnerName,paymentDate,new Date(),accountDetailOld.getId(),true);

                //账务明细表里添加一条新数据(正确)
                AccountDetail accountDetail = financeAccountService.saveAccountDetail(user,org,financeAccount,balance,null,"4",summary,"1",operatorName,"2",business,"2",money,"2",null,"1",memo,"0103",partnerName,paymentDate,new Date(),accountDetailOld.getId(),true);

                BigDecimal debit = accountPeriodMonth.getDebit().subtract(accountDetailOld.getDebit()).add(money);
                financeAccount.setDebit(financeAccount.getDebit().subtract(accountDetailOld.getDebit()).add(money));//计入账户收入
                financeAccount.setBalance(balance);//从余额加上收入
                accountPeriodMonth.setDebit(debit);
                accountPeriodMonth.setBalance(balance);

                BigDecimal debitDay = accountPeriodDay.getDebit() == null ? new BigDecimal(0).add(money) : accountPeriodDay.getDebit().subtract(accountDetailOld.getDebit()).add(money);
                accountPeriodDay.setDebit(debitDay);
                accountPeriodDay.setBalance(balance);
                accountPeroidDao.update(accountPeriodMonth);//月结
                accountPeroidDao.update(accountPeriodDay);//日结
                financeAccountDao.update(financeAccount);//账户

                accountDetailOld.setModify(true);
                accountDetailDao.update(accountDetailOld);

                AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,"0103");

                addAccountDetailToDetailHistory(accountDetail,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
            }else {
                map.put("status","3");//此账户余额不够冲红，不可修改
                return map;
            }
        }else {
            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,"0103");

            accountDetailOld.setBusinessDate(paymentDate);
            accountDetailOld.setAuditorName(operatorName);
            accountDetailOld.setPartnerName(partnerName);
            accountDetailOld.setMemo(memo);
            accountDetailOld.setPurpose(summary);
            accountDetailOld.setSummary(summary);
            accountDetailDao.update(accountDetailOld);

            addAccountDetailToDetailHistory(accountDetailOld,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
        }
        map.put("status","1");//成功
        return map;
    }

    //存入银行的票据不能修改有关冲账的字段，没有存入银行的票据可以直接更新保存历史
//    private Map<String,Object> updateLoanReturnDebit(User user,Integer business,Integer businessHistory,Integer previousId,String method, BigDecimal money,
//          Date receiveAccountDate,String returnNo,Date expireDate,String originalCorp,String bankName,String memo,String withinOrAbroad,Map<String,Object> map){
//        //回款票据 的转账支票/承兑汇票
//        FinanceAccountBill financeAccountBill = financeAccountService.getAccountBillByBusiness(business,"2");  //老数据
//        FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();  //老数据
//        if (financeReturn!=null) {    //外部支票和承兑汇票
//            FinanceReturnHistory returnHistoryLast = getFinanceReturnHistoryLast(financeReturn);  //查找上次修改
//
//            //原来的基础上更新
//            financeReturn.setAmount(money);
//            financeReturn.setBillAmount(money);
//            financeReturn.setReturnNo(returnNo);//支票号码
//            financeReturn.setOriginalCorp(originalCorp);//出具票据单位
//            financeReturn.setBankName(bankName);//出具票据银行
//            financeReturn.setExpireDate(expireDate);//发票到期日期
//            financeReturn.setReceiveDate(receiveAccountDate);//收到票据日期
//            financeReturn.setMemo(memo);//备注
////            financeReturn1.setMyselfId(financeReturn.getId());  //对应修改前的支票id
//            financeReturn.setUpdator(user.getUserID());
//            financeReturn.setUpdateDate(new Date());
//            financeReturn.setUpdateName(user.getUserName());
//            financeReturnDao.update(financeReturn);
//
//            //最新的再创建历史
//            FinanceReturnHistory returnHistory = addReturnToReturnHistory(financeReturn, returnHistoryLast.getId(), returnHistoryLast.getVersionNo() + 1, user);
//
//            //上次修改历史
//            FinanceAccountBillHistory financeAccountBillHistoryLast = getFinanceAccountBillHistoryLast(financeAccountBill, user, business, previousId, returnHistoryLast.getId(), null);
//
//            financeAccountBill.setBillNo(returnNo);//支票号码
//            financeAccountBillDao.update(financeAccountBill);
//
//            //更新后的历史
//            addBillToBillHistory(financeAccountBill, user, business, businessHistory, financeAccountBillHistoryLast, returnHistory.getId(), null);
//        }

//        map.put("status","1"); //正确
//        return map;
//    }

    //借款支出的修改
    private Map<String,Object> updateLoanBankDebit(User user,Integer business,Integer businessHistory,Integer previousId,BigDecimal money,Date receiveAccountDate,Integer accountId,String memo,
                                                   Map<String,Object> map,String operatorName,String partnerName,Date paymentDate,String oppositeAccount,String oppositeBankno,String oppositeBankcode,String summary){
        AccountDetail accountDetailOld = getAccountDetailByBusiness(business,3,null);
        FinanceAccount f = financeAccountDao.get(accountDetailOld.getAccountId_());  //修改前的银行账户
        FinanceAccount f1 = financeAccountDao.get(accountId);  //修改后的银行账户
        if (0==f.getAccountStatus() || 0==f1.getAccountStatus()){
            map.put("status","2"); //账户被冻结
            return map;
        }

        BigDecimal newBanlance = f.getBalance(); //账户修改前余额
        BigDecimal newBanlance1 = f1.getBalance() ; //账户修改后的账户余额
        BigDecimal newDebit = accountDetailOld.getDebit(); //修改前支出

        if (!f.getId().equals(f1.getId())){
            //收入修改 新余额=现在账户余额-修改前的收入+修改后的新收入
            newBanlance=f.getBalance().add(accountDetailOld.getDebit()); //修改前账户的余额
            newBanlance1=f1.getBalance().subtract(money);  //修改后的账户余额
        }else {
            //收入修改 新余额=现在账户余额-修改前的收入+修改后的新收入
            newBanlance=f.getBalance().add(accountDetailOld.getDebit()).subtract(money);  //账户余额
            newBanlance1 = newBanlance;
        }

        if (new BigDecimal(0).equals(newBanlance) || new BigDecimal(0).equals(newBanlance1)){
            map.put("status","3"); //账户余额不够冲减
            return map;
        }
        //如果金额修改了或者是银行账户修改了
        if (money.compareTo(accountDetailOld.getDebit())!=0|| !f.getId().equals(f1.getId())){
            AccountPeriod accountPeriod = new AccountPeriod();  //修改前账户的月结
            AccountPeriod accountPeriod1 = new AccountPeriod();  //修改后账户的月结
            AccountPeriod accountPeriodDay = new AccountPeriod();  //修改前账户的日结
            AccountPeriod accountPeriodDay1 = new AccountPeriod();  //修改后账户的日结
            if (f==f1){   //没有修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(f.getId(), new Date());  //账户的月结
                accountPeriodDay = accountService.getAccountPeriodByDay(f.getId(),new Date());  //账户的日结
            }else {    //修改账户
                accountPeriod = accountService.getAccountPeriodByMonth(f.getId(), new Date());  //修改前账户的月结
                accountPeriod1 = accountService.getAccountPeriodByMonth(f1.getId(), new Date());  //修改后账户的账户月结
                accountPeriodDay = accountService.getAccountPeriodByDay(f.getId(), new Date()); //修改前账户的日结
                accountPeriodDay1 = accountService.getAccountPeriodByDay(f1.getId(), new Date());  //修改后账户的账户日结
            }

            //批准后，账务明细表里添加一条新数据(金额为负)
            AccountDetail accountDetail2 = new AccountDetail();//批准后，账务明细表里添加一条新数据(金额为负)
            accountDetail2.setBalance(newBanlance);
            accountDetail2.setOrg(user.getOrganization());
            accountDetail2.setAuditDate(new Date());
            accountDetail2.setGenre(accountDetailOld.getGenre());
            accountDetail2.setMethod(accountDetailOld.getMethod());
            accountDetail2.setAccountId(accountDetailOld.getAccountId());//账户外键
            accountDetail2.setAccountBank(accountDetailOld.getAccountBank());
            accountDetail2.setPreviousId(accountDetailOld.getId());
            accountDetail2.setBusinessDate(accountDetailOld.getBusinessDate());
//            accountDetail2.setVersionNo(accountDetailOld.getVersionNo() + 1);
            accountDetail2.setType(accountDetailOld.getType());//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail2.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail2.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail2.setBusiness(accountDetailOld.getBusiness());
            accountDetail2.setSource("2");
            accountDetail2.setBusinessType(accountDetailOld.getBusinessType());
            accountDetail2.setAuditorName(user.getUserName());
            accountDetail2.setAuditor(user.getUserID());
//            BeanUtils.copyProperties(accountDetailOld,accountDetail2);
            accountDetail2.setCreateDate(new Date());
            accountDetail2.setCreateName(user.getUserName());
            accountDetail2.setCreator(user.getUserID());
            accountDetail2.setAuditDate(new Date());
            accountDetail2.setDebit(accountDetailOld.getDebit().multiply(new BigDecimal(-1)));  //负数
            accountDetail2.setBillAmount(money);  //票面金额
            accountDetail2.setFid(f.getId().toString());
            accountDetail2.setBalance(newBanlance); //余额
            accountDetail2.setPartnerName(accountDetailOld.getPartnerName());
            accountDetail2.setOppositeAccount(accountDetailOld.getOppositeAccount());
            accountDetail2.setOppositeBankno(accountDetailOld.getOppositeBankno());
            accountDetail2.setOppositeBankcode(accountDetailOld.getOppositeBankcode());
            accountDetail2.setSummary(accountDetailOld.getSummary());
            accountDetail2.setPurpose(accountDetailOld.getPurpose());
            accountDetail2.setBillDate(new Date());
            accountDetail2.setModify(true);

            //账务明细表里添加一条新数据(金额修改后的正确)
            AccountDetail accountDetail1 = new AccountDetail();
            accountDetail1.setOrg(user.getOrganization());
            accountDetail1.setAuditDate(new Date());
            accountDetail1.setGenre(accountDetailOld.getGenre());
            accountDetail1.setMethod("5");
            accountDetail1.setAuditor(user.getUserID());
            accountDetail1.setAccountId(f1);//账户外键
            accountDetail1.setAccountBank(f1.getBankName() + f1.getAccount());
            accountDetail1.setPreviousId(accountDetailOld.getId());
            accountDetail1.setVersionNo((accountDetailOld.getVersionNo()==null)?0:accountDetailOld.getVersionNo()+1);
            accountDetail1.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail1.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail1.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
            accountDetail1.setBusiness(business);
            accountDetail1.setSource("2");
            accountDetail1.setBusinessType("0103");
            accountDetail1.setCreateDate(new Date());
            accountDetail1.setCreateName(user.getUserName());
            accountDetail1.setCreator(user.getUserID());
            accountDetail1.setAuditDate(new Date());
            accountDetail1.setDebit(money);
            accountDetail1.setBillAmount(money);  //票面金额
            accountDetail1.setMemo(memo);
            accountDetail1.setReceiveAccountDate(receiveAccountDate);
            accountDetail1.setBusinessDate(paymentDate);
            accountDetail1.setAccountBank(f1.getBankName()+f1.getAccount());
            accountDetail1.setAccountantStatus("2");//会计数据状态  1-未选择  2-已选择
            accountDetail1.setFid(f1.getId().toString());
            accountDetail1.setAccountId(f1);//账户外键
            accountDetail1.setPartnerName(partnerName);
            accountDetail1.setAuditorName(operatorName);
            accountDetail1.setOppositeAccount(oppositeAccount);
            accountDetail1.setOppositeBankno(oppositeBankno);
            accountDetail1.setOppositeBankcode(oppositeBankcode);
            accountDetail1.setSummary(summary);
            accountDetail1.setPurpose(summary);
            accountDetail1.setBillDate(new Date());
            accountDetail1.setModify(true);

            //账户是否改变
            if (accountPeriod1.getId()!=null){   //账户修改
                accountDetail1.setBalance(newBanlance1); //正确的新余额

                //修改前账户的月结
                accountPeriod.setDebit(accountPeriod.getDebit().subtract(newDebit));
                accountPeriod.setBalance(newBanlance);

                //修改后账户的月结
                accountPeriod1.setDebit(accountPeriod1.getDebit().add(money));
                accountPeriod1.setBalance(newBanlance1);

                //修改前的日结
                accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(newDebit));
                accountPeriodDay.setBalance(newBanlance);

                //修改后的日结
                accountPeriodDay1.setDebit(accountPeriodDay1.getDebit().add(money));
                accountPeriodDay1.setBalance(newBanlance1);

                //修改前账户余额
                f.setDebit(f.getDebit().subtract(newDebit));
                f.setBalance(newBanlance);

                //修改后账户余额
                f1.setDebit(f1.getDebit().add(money));
                f1.setBalance(newBanlance1);

                financeAccountDao.update(f1);
                accountPeroidDao.update(accountPeriod1);
                accountPeroidDao.update(accountPeriodDay1);

            }else {  //账户未修改
                accountDetail1.setBalance(newBanlance); //正确的新余额

                //修改后的月结
                accountPeriod.setDebit(accountPeriod.getDebit().subtract(newDebit).add(money));
                accountPeriod.setBalance(newBanlance);

                //修改后的日结
                accountPeriodDay.setDebit(accountPeriodDay.getDebit().subtract(newDebit).add(money));
                accountPeriodDay.setBalance(newBanlance);

                //修改后的账户余额
                f.setDebit(f.getDebit().subtract(newDebit).add(money));
                f.setBalance(newBanlance);
            }

            accountDetailDao.save(accountDetail2);
            accountDetailDao.save(accountDetail1);
            financeAccountDao.update(f);
            accountPeroidDao.update(accountPeriod);
            accountPeroidDao.update(accountPeriodDay);

            accountDetailOld.setModify(true);
            accountDetailDao.update(accountDetailOld);

            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,"0103");  //查找上次的修改

            addAccountDetailToDetailHistory(accountDetail1,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
        }else {
            AccountDetailHistory accountDetailHistoryLast = getAccountDetailHistoryLast(accountDetailOld,business,previousId,user,"0103");

            //批准后，如果金额和账户均没有修改则直接覆盖数据
            accountDetailOld.setMemo(memo);
            accountDetailOld.setReceiveAccountDate(receiveAccountDate);
            accountDetailOld.setAuditorName(operatorName);
            accountDetailOld.setPartnerName(partnerName);
            accountDetailOld.setBusinessDate(paymentDate);
            accountDetailOld.setOppositeAccount(oppositeAccount);
            accountDetailOld.setOppositeBankcode(oppositeBankcode);
            accountDetailOld.setOppositeBankno(oppositeBankno);
            accountDetailOld.setPurpose(summary);
            accountDetailOld.setSummary(summary);
            accountDetailDao.update(accountDetailOld);

            addAccountDetailToDetailHistory(accountDetailOld,business,businessHistory,user,accountDetailHistoryLast.getId(),accountDetailHistoryLast.getVersionNo()+1);
        }
        map.put("status","1"); //成功
        return map;
    }

    /**
     * 借款的收款修改
     * @param  userId  操作人id
     * @param  payId   要修改的还款id
     * @param methodOld   修改前的本金型式
     * @param method 修改后的本金型式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
     * @param money 金额
     * @param accountId  //账户id
     * @param receiveDate   接收日期
     * @param expireDate  支票到期日
     * @param operator  支付经手人
     * @param receiver  接收经手人
     * @param paymentDate  付款日期(各个表中的字段不同，注意下)
     * @return status 0-失败 1-添加成功  2-账户被关闭 3-账户余额不够冲减
     */
    @Override
    public Map<String, Object> updateLoanPayCredit(Integer userId, Integer payId, Integer payHistoryId, Integer previousId, String methodOld, String method,
        Double money, Integer accountId, Date receiveDate, String operator, String receiver, Date paymentDate,Integer type,
        String returnNo,Date expireDate,String originalCorp,String bankName,String oppositeCorp,String payer) {
        Map<String,Object> map = new HashMap<>();
        String status = "0";   //返回状态
        map.put("status",status);
        User user = userDao.get(userId);
        Organization org = orgDao.get(user.getOid());
        String businessType = "";
        String summary = "";
        switch (type) {
            case 1:
                businessType="0101";  //借入
                summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+"公司向"+payer+"借款";  //摘要   （此默认是收入的）
                break;
            case 2:
                businessType="0102";   //还款
                summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+"公司向"+oppositeCorp+"付款";
                break;
            case 3:
                businessType="0103";  //借出
                summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+oppositeCorp+"向公司借款";
                break;
            case 4:
                businessType="0104";   //收款
                summary = NewDateUtils.dateToString(paymentDate,"yyyy年MM月dd日")+"公司向"+payer+"借款";  //收入的（借出）
                break;
        }
        if ("1".equals(method)){   //单纯现金
            updateCashCredit(payId,payHistoryId,previousId,BigDecimal.valueOf(money),org,user,null,operator,receiver,paymentDate,type,summary,map);  //收入现金
        }else if ("3".equals(method)||"4".equals(method)){  //转账支票或承兑汇票(未存入银行的票据怎么进行修改？  已存入银行的票据在付款录入中不可选择？)
            updateReturn(user,payId,payHistoryId,previousId,BigDecimal.valueOf(money),receiveDate,returnNo,expireDate,originalCorp,bankName,null,receiver,operator,businessType,map,oppositeCorp,payer,summary);  //支出的转账支票或承兑汇票
        }else if ("5".equals(method)){  //银行转账
            updateBank(user,payId,payHistoryId,previousId,BigDecimal.valueOf(money),receiveDate,accountId,null,receiver,type,summary,map);  //支出的银行转账
        }

        return map;
    }

//    @Override
//    public PoLoan addPoLoan(User user,Integer paymentApply, Integer financePayment, Integer ordersPrepayment,BigDecimal amount, Integer type,Integer supplier,String supplierName,Integer orders) {
//        PoLoan poLoan = new PoLoan();
//        poLoan.setOrg(user.getOid()); //机构id
//        poLoan.setPaymentApply(paymentApply); //支付申请ID
//        poLoan.setFinancePayment(financePayment); //财务支付ID
//        poLoan.setOrdersPrepayment(ordersPrepayment);  //预付款申请ID
//        poLoan.setType(type);  //类型:1-货款(票据处理的),2-预付款
//        poLoan.setSupplier(supplier);
//        poLoan.setSupplierName(supplierName);
//        poLoan.setOrders(orders);
//        poLoan.setAmount(amount);
//        poLoan.setCreator(user.getUserID());
//        poLoan.setCreateName(user.getUserName());
//        poLoan.setCreateDate(new Date());
//        poLoanDao.save(poLoan);
//
//        return poLoan;
//    }
}
