package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubject;

import java.util.List;

/**
 * @ClassName RespInvestigationListPage
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/20 9:43
 * @Version 1.0
 */
public class RespInvestigateListPage {
    private Integer sum;
    private PageInfo pageInfo;
    private List<InvestigateSubject> investigateSubjectList;

    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<InvestigateSubject> getInvestigateSubjectList() {
        return investigateSubjectList;
    }

    public void setInvestigateSubjectList(List<InvestigateSubject> investigateSubjectList) {
        this.investigateSubjectList = investigateSubjectList;
    }
}
