<!--
分页子组件
----desc：该子组件为全局公用组件，
----pages（用到的页面）：[很多，此处不详细列举]
-->
<template>
 <div class="TyPage">
   <div class="yeCon">
     <div class="yeDiv" v-for="(page, pIndex) in pageNums" :key="pIndex" @click="showPage(page)">
       <span :class="page.klass" v-html="page.name"></span>
     </div>
     <div class="yeDiv"><span class="yeC">共{{ allPage }}页</span></div>
     <div class="yeDiv"><span class="yeC">当前第{{ curPage }}页</span></div>
   </div>
 </div>
</template>
<script>

export default {
  name: "TyPage",
  data() {
    return {
      pageNums:[],
      showDot:false
    };
  },
  props:{
    pageSize: { 'type':[Number, String], 'default':20 },
    curPage: { 'type':[Number, String], 'default':1 },
    allPage: { 'type':[Number, String], 'default': 1 },
    pageClickFun: { 'type':Function, 'default': null },
  },
  watch: {
    pageSize(newValue) {
      this.setPageNum()
    },
    curPage(newValue) {
      this.setPageNum()
    },
    allPage(newValue) {
      this.setPageNum()
    }
  },
  mounted() {
    this.setPageNum()
  },
  methods: {
    setPageNum(){
      let all = this.allPage
      let cur = this.curPage
      if(all && cur){
        let pageArr = []
        if(all > 8){
          let n = 0
          let end = parseInt(cur) + 3;
          let star = cur - 3
          if( star < 1 ){ star = 1; n = 3 - cur; end += n ;  }
          if( end > all ){ end = all ; }
          if( end - star < 5 ){ star = end - 5 ; }
          if( star < 1 ){ star = 1 ; }


          if( cur != 1 ){
            pageArr.push({ 'name': '首页', 'page':1, 'klass':'ye', 'type':'first' })
          }
          if( star > 1 ){
            pageArr.push({ 'name': '....', 'page':'....', 'klass':'yeDot', 'type':'more1'  })
          }
          for (var i = star; i <= end; i++) {
            if( i == cur ){
              pageArr.push({ 'name': i, 'page': i , 'klass':'yecur', 'type':'curPage' })
            }
            else {
              pageArr.push({ 'name': i, 'page': i , 'klass':'ye', 'type':'normal' })
            }
          }
          if( end < all ){
            pageArr.push({ 'name': '....', 'page':'....', 'klass':'yeDot', 'type':'more2'  })
          }
          if( cur != all ){
            pageArr.push({ 'name': '尾页', 'page': all , 'klass':'ye', 'type':'last' })
          }
        }
        else {
          for (let i = 1; i <= all; i++) {
            if(i==cur){
              pageArr.push({ 'name': i, 'page': i , 'klass':'yecur', 'type':'curPage' })
            }else{
              pageArr.push({ 'name': i, 'page': i , 'klass':'ye', 'type':'normal' })
            }
          }

        }
        this.pageNums = pageArr
      }else{
        this.pageNums = []
      }
    },
    showPage(pageInfo){
      if(pageInfo.klass === 'yeDot'){

      }else {
        this.pageClickFun(pageInfo)
      }
    }

  },
}
</script>

<style lang="scss" scoped>
.TyPage{

  /* page css */
  .yeCon{ padding:10px 15px; margin:20px auto 0; text-align:center; }
  .ye{margin-right:10px; display:inline; padding:5px 8px; cursor:pointer;  }
  .ye:hover{ background:#48cfad; color:#fff; }
  .yecur{margin-right:10px; display:inline; padding:5px 8px;  cursor:pointer; background:#48cfad; color:#fff;text-decoration:underline; }
  .yeC{ margin-left:0; display:inline; padding:5px 8px; cursor:pointer;  }

  .yeDiv{ display: inline-block;  }
  .yeDot{ margin-right: 10px; }

}
</style>