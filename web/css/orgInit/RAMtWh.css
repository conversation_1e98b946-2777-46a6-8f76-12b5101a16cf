.ty-alert-padding{
    padding: 8px;
}
.LP_16{
    padding-left: 16px;
}
.LP_32{
    padding-left: 32px;
}
.bounceMainCon{
    width: 90%;
    margin: 0 auto;
}
.page b{
    margin: 0 4px;
}

.adddis {
    margin-bottom: 20px;
}
.ty-disabled, .ty-disabled span {
    color:
            #aaa;
    cursor: default;
}
.bonceCon input, .bonceCon select, .bonceCon textarea {
    border: 1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    text-align: left;
    padding: 0 8px;
    color: #3f3f3f;
    width: 180px;
}

.bonceCon textarea {
    line-height: 20px;
    padding: 5px;
}

.bonceCon input {
    height: 36px;
}

.bonceCon input:disabled {
    background-color: #dff0ff;
}

.bonceCon input:focus, .bonceCon select:focus, .bonceCon textarea:focus {
    border: 1px solid #5d9cec;
}

.formItem {
    width: 270px;
    float: left;
    margin: 8px;
}

.formTitle, .formCon {
    float: left;
    line-height: 36px;
}

.formTitle {
    width: 90px;
    text-align: right;
    padding: 0 10px;
}

.formCon {
    width: 180px;
}

.select2-container {
    z-index: 9999999;
}

.hoverDetail {
    cursor: default;
}

.hoverDetailCon {
    position: absolute;
    top: 0;
    right: 50%;
    width: 300px;
    min-height: 120px;
    background-color: #fff;
    padding: 10px 20px 20px 20px;
    border: 1px solid #ccc;
    box-shadow: 0 0 5px #ccc;
    display: none;
    text-align: left;
    z-index: 999;
}

.hoverDetail:hover .hoverDetailCon {
    display: block;
}

/*.countAll{*/
/*width: 100%;*/
/*height: 35px;*/
/*line-height: 35px;*/
/*background-color: #ffffe1;*/
/*border: 1px dashed #e6d8b6;*/
/*padding: 0 15px;*/
/*color: #b38f6c;;*/
/*margin-top: 8px;*/
/*}*/

#signInfoRecord .recordTitle {
    padding: 0 20px;
    height: 26px;
    line-height: 26px;
    background-color: #f3f3f3;
    color: #666;
    border-left: 4px solid #5d9cec;
    font-weight: bold;
}

#signInfoRecord .recordHeader b, #signInfoRecord .recordFooter b {
    font-weight: normal;
    color: #888;
    margin-left: 20px;
}

#signInfoRecord .recordCon {
    padding: 10px 20px;
    color: #444;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    margin: 5px 0;
}














.applyAll {
    width: 100%;
    height: 35px;
    line-height: 35px;
    background-color: #f3f3f3;
    padding: 0 15px;
    color: #666;;
    margin-top: 8px;
    text-align: right;
}

.radioBox {
    width: 220px;
    padding: 5px 10px;
    margin: 0 auto;
}

.radioBox .ty-radio {
    background-color: #e8f0f5;
    margin-bottom: 8px;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
}

.radioBox .ty-radio i.fa {
    margin-right: 5px;
}

.radioDisabled, .radioDisabled .ty-radio {
    background-color: #d6dde7;
    cursor: not-allowed;
}

.radioBox .numOk.ty-radioActive {
    background-color: #5d9cec;
    color: #fff;
}

.radioBox .numNo.ty-radioActive {
    background-color: #ed5565;
    color: #fff;
}

.gapRt{
    margin-right: 10px;
}
.gapLt{
    margin-left: 10px;
}
.stockHandle {
    padding-left: 100px;
    width: 1100px;
}

.stockHandle h5 {
    font-weight: 800;
    line-height: 50px;
}

.fragment {
    width: 1000px;
    padding: 10px 0;
    overflow: hidden;
}

.fragment .ty-left {
    width: 430px;
}

.fragment .partRt {
    text-align: right;
}

.fragment span {
    display: inline-block;
}

.frCon {
    width: 280px;
    color: #c65911;
}

.frDeal {
    margin-right: 180px;
}

.frCheck {
    margin-right: 70px;
}

.pt-table {
    width: 100%;
}

.warehouse {
    padding: 20px 0;
}

.searckSect {
    padding-top: 10px;
}

.pt-table tr td {
    padding: 0 10px;
    line-height: 26px;
}

.pt-table tr td p {
    margin-bottom: 0;
}

.bg-yellow thead td {
    border-color: #fff0;
    background-color: #fff2cc;
}

.ty-table tbody td.td-orange {
    background-color: #f4b084;
    text-align: center;
}
.pt-table tbody td.td-orange {
    background-color: #f4b084;
    text-align: center;
}
.td-lightOrange {
    background: #fce4d6;
    text-align: center;
    margin:10px;
}

.keywordSearch {
    position: relative;
}

.keywordSearch input {
    padding: 0 10px;
    font-size: 12px;
    min-width: 290px;
    height: 26px;
    line-height: 26px;
    border: 1px solid #ccc;
}
.keywordSearch input::placeholder{
    color:#aaa;
}
.keywordSearch i {
    display: inline-block;
    width: 26px;
    height: 26px;
    background: url('../common/theme/icon/search1.png') no-repeat;
    background-position: center center;
    background-size: 20px 20px;
    border-radius: 0 5px 5px 0;
    position: absolute;
    top: 0;
    right: 0;
}

.jumpWarehouse {
    float: right;
    font-size: 10px;
}

.houseName {
    width: 260px;
}

.houseInfo {
    width: 640px;
}
.single p.bg-lightOrange{
    background: #fce4d6;
    text-align: center;
    line-height: 80px;
    min-height: 80px;
}
.single tr{
    border: 1px solid #ccc;
}
.single tr{
    border: 1px solid #ddd;
}
.single tr td:first-child{
    width:310px;
}
.single tr td:not(:first-of-type) {
    padding-top: 10px;
    padding-bottom: 10px;
}

.backMain {
    color: #0b94ea;
    background-color: #fff;
    border-color: #ccc;
}

.backMain:hover {
    color: #0b94ea;
    background: none;
}

.selectSect {
    margin-left: 50px;
    line-height: 36px;
}

.checkCondition span {
    margin-right: 30px;
}

.gap {
    margin: 10px 0;
}

.checkCtrl {
    text-align: left;
    width: 40%;
}

.addOneStore {
    color: #2179ea;
    font-weight: 600;
}
.addOneStore:hover {
    text-decoration:underline ;
}

.storesFill li > div {
    float: left;
    width: 50%;
}

.notSelectStores {
    display: none;
}

.seeStoresName {
    margin-left: 20px;
    display: inline-block;
    width: 200px;
    text-align: center;
    line-height: 36px;
    border-bottom: 1px solid #c65911;
}

.storeTb thead tr td {
    border-color: rgba(255, 255, 255, 0);
    background: #fff2cc;
}

.storeTb thead tr td.hdNot {
    background: none;
}

.storesCreateInfo {
    margin-top: 30px;
}

div.left_container > div > form, div.left_container > div > form > ul, div.left_container > div > form > ul > li {
    margin: 0;
}

/*右面头部*/
div.Con > p {
    font-weight: 400;
    font-size: 24px;
    padding: 0 15px;
    margin-top: 25px;
}

/*左面*/
.between {
    position: relative;
    float: left;
    border: 1px solid #e3e3e3;
    border-top-color: #f0f8ff;
    border-bottom: 3px solid #fff;
    height: 100%;
    width: 20px;
    top: -2px;
    background-color: #f0f8ff;
}

.between2 {
    border-left: 1px solid #e3e3e3;
    border-right: 1px solid #e3e3e3;
    width: 109%;
    position: absolute;
    bottom: -6px;
    height: 10px;
    left: -1px;
    background-color: #f0f8ff;
}
.Btop {
    padding: 10px 0 10px 15px;
    background: #bdd7ee;
}
.faceul {
    list-style-type: none;
}

.faceul1:hover {
    background: #eaeaea;
}

.faceul1 {
    height: 35px;
    line-height: 35px;
}

.faceul1 > a {
    color: #0069b4;
    text-decoration: none;
    display: block;
    padding: 0 0 0 26px;
}

.back-btn, .back-btn:hover {
    font-weight: 700;
    font-size: 14px;
    color: black;
}

.back-b {
    padding: 50px 0 20px 30px;
}

.add-b > a {
    color: #bf8f00;
}

.add-b > a:hover {
    text-decoration: none;
}

.add-b {
    float: left;
    padding: 10px 15px;
    margin-bottom: 15px;
}

div.add-b:hover {
}

.faceul1 > a > span {
    display: inline-block;
    vertical-align: middle;
}

.faceul1 > a > span:nth-child(1) {
    max-width: 80%;
    overflow: hidden;
    height: 35px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.faceul1 > a > span:nth-child(2) {
    overflow: hidden;
    text-align: center;
}

.faceul1 > a > span:nth-child(3) {
    width: 25%;
    min-width: 50px;
    text-align: right;
}

/*右面*/
.bigContainer {
    position: relative;
    margin-left: 15px;
    border: 1px solid #cccccc;
}

.left_container {
    float: left;
    width: 235px;
}

.right_container {
    padding-left: 255px;
}

.Right-label, .Left-label {
    padding: 0;
}

.Left-label {
    min-height: 186px;
}

.left-bottom {
    margin-top: 45px;
    position: absolute;
    bottom: 0;
}

.Right-label {
    padding: 10px 15px 5px 15px;
    min-width: 700px;
}

.conon {
    padding: 10px 0;
    display: inline-block;
    float: left;

}

.conon > a, .concon > a:hover {
    color: #909090;
    font-family: "方正兰亭超细黑简体";
    font-weight: 700;
}

.conon > p, .conon > p.a:hover {
    display: inline-block;
    padding: 0 5px;
    font-weight: 700;
}

.conon > a:nth-child(2), .conon > a:nth-child(2):hover {
    color: black;
    font-weight: lighter;
    text-decoration: none;
}

.dq > a {
    color: black;
}

.bj > a, .bj > a:hover {
    color: black;
    text-decoration: none;

}

.noteTtl {
    margin-right: 30px;
}

.initBody {
    margin: auto;
}

/*---------查看*/
ul.hel {
    position: absolute;
    width: 130px;
    right: 0;
    top: 25px;
    padding: 2px 0;
    background-color: #fff;
    box-shadow: 0 0 3px #aaa;
    z-index: 2;
}

.ulli {
    float: left;
    width: 100%;
}

.ulli span {
    display: block;
    height: 28px;
    line-height: 28px;
    text-align: left;
    margin: 0 2px;
    padding: 0 4px;
    border-radius: 2px;
    transition: all .2s;
}

.ulli:not(.ty-disabled) span {
    color: #5d9cec;
}

.ulli:not(.ty-disabled) span:hover {
    background-color: #5d9cec;
    color: #fff;
}

.cz_handle {
    position: relative;
}

.stockJump {
    margin-bottom: 30px;
    color: #0070ca;
    display: none;
    margin-left: 100px;
}

.stockScreen{
    margin-left: 100px;
    width: 80%;
    min-width: 1100px;
}

.goodsUnit{
    margin-left:10px;
}
.resetCurrentStation .ty-table{
    width:auto;
}
.ty-table tr td.createInfo{
    font-size: 12px;
}

.bg-yellow tr:nth-child(even) {
    background: #ddebf7;
}
.bg-yellow tr td span.ty-color-red, .ty-table tr td span.ty-color-blue {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 2px;
    font-weight: bold;
    cursor: pointer;
}

.bg-yellow td .ty-color-red:hover {
    color: #fff;
    background-color: #ed5565;
}
.bg-yellow .ty-color-blue:hover {
    color: #fff;
    background-color: #5d9cec;
    transition: all .2s
}
.supKuItem{border: 1px solid #dbe2e8;border-radius: 3px;margin-top: 15px;padding: 16px;width: 770px;background: #fff;}
.supKuItem div p{ line-height:25px;  }
.supKuItem>div{display:flex;   }
.supKuItem>div>div{ flex:1;   }
#onlyNumInfo{ padding:25px;  }


.mainCon2, .hd{ display:none;  }
.mainCon>div{ border:1px solid #ddd;border-radius:2px;  }
div.leCat{ width: 250px; min-height:600px;   float: left; color:#0b94ea; line-height:30px; position: relative ; padding-bottom:80px;   }
div.leCat:hover{ cursor: default;  }
.leCat>.bottom{position: absolute; bottom:0; width:100%;   }
.leCat>div{ color: #333; padding-left:15px; font-weight: bold; background: #c0d3ea;    }
.leCat>ul>li{ padding-left:25px;font-size:13px;line-height: 25px;   }
.leCat>ul>li>.name{ white-space: nowrap; text-overflow: ellipsis; overflow: hidden; word-break: break-all; width:160px; display:inline-block;     }
.leCat>ul>li:hover{ background: #cdddea; font-weight: bold; color: #0f62ea;   }
.btnCat{ padding-right:15px; color: #0f62ea }
.btnCat:hover{ text-decoration:underline; cursor: default; }
.catNum{ float: right; padding-right:15px;  }
.lePad{ padding-left:40px;  }
div.riMt{ margin-left:255px; padding:15px; min-height:600px;  }
div.riMt thead{ background: #f7e5baa6;  }
.search{ float:right;  }
.search>input{ height:30px; padding:0 5px; border: 1px solid #ccc; border-right:none;
    border-radius:4px 0 0 4px; width:250px;  }
.search>span{ display:inline-block;height:30px; float:right; border: 1px solid #ccc; cursor: default;
    background: #0b94ea; color:#fff; line-height:25px; padding:0 10px; border-radius:0 4px 4px 0; }

#scanCtrlCon{display:none; position: absolute;background:#fff; padding:10px 15px; cursor: default; width:155px; border-radius: 3px; border:1px solid #ccc;   }
#scanCtrlCon>ul:before{ position:relative;   }
#scanCtrlCon>ul:before{ content:"";  border:1px solid #ccc;border-right: none; border-bottom:none; display: inline-block; width:16px; height:16px; position: absolute ;
    transform:rotate(45deg);
    -ms-transform:rotate(45deg); 	/* IE 9 */
    -moz-transform:rotate(45deg); 	/* Firefox */
    -webkit-transform:rotate(45deg); /* Safari 和 Chrome */
    -o-transform:rotate(45deg); 	/* Opera */
    top:-8px; right:27px;
    background: #fff;
}
#scanCtrlCon li{ padding:2px 5px; background:#0b94ea; color:#fff; margin-top:5px; text-align: center; display: inline-block; border-radius:  4px;  }
#scanCtrlCon li:hover{ background: #0482ea;    }
#scanCtrlCon li[disabled]{ background:#ccc; color:#666;    }

.hasChoose, .noChoose{  padding:5px 20px;  }
.hasChoose .fa , .noChoose .fa { color: #0d9e90; display:inline-block; margin-right:15px;   }
.p1 h3, .p0 h3{
    text-align: center;
    margin: 0 0 40px 0;
}

.stockChange, .dataFilter {
    display: none;
}












