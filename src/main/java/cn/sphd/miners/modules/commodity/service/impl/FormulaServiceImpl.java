package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.FormulaService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.material.dao.MtBaseDao;
import cn.sphd.miners.modules.material.dao.MtBaseHistoryDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtBaseHistory;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service("formulaService")
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class FormulaServiceImpl implements FormulaService {


    @Autowired
    private ApprovalProcessDao approvalProcessDao;
    @Autowired
    private PdBaseDao pdBaseDao;
    @Autowired
    private PdBaseHistoryDao pdBaseHistoryDao;
    @Autowired
    private PdFormulaDao pdFormulaDao;
    @Autowired
    private PdFormulaHistoryDao pdFormulaHistoryDao;
    @Autowired
    private MtBaseDao mtBaseDao;
    @Autowired
    private MaterielService materielService;
    @Autowired
    PdCompositionMaterialDao pdCompositionMaterialDao;
    @Autowired
    PdCompositionMaterialHistoryDao pdCompositionMaterialHistoryDao;
    @Autowired
    UnitService unitService;
    @Autowired
    MtBaseHistoryDao mtBaseHistoryDao;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserService userService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    UserSuspendMsgService suspendMsgService;
    @Override
    public Map<String, Object> getPdFoBaseList(Integer oid, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "p.phrase AS phrase,\n" +
                "if(p.weight_unit ='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight from t_pd_base p \n" +
                "LEFT JOIN t_pd_composition_material cm on p.id=cm.product\n" +
                "where p.oid=" + oid + " and cm.id is null and p.composition='3' order by p.create_date ";
        return pdBaseDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getFormulaList(Integer oid, String state, String keyword, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};

        String sql = "select f.id as id,f.code as code,f.name as name,f.material_name as materialName,f.major_ingredient as majorIngredient,f.minor_ingredient as minorIngredient,f.products as products,f.create_name as createName,f.create_date as createDate,f.update_name as updateName,f.update_date as updateDate,f.enabled as enabled,f.memo as memo,(select count(c.id) from t_pd_composition_material c LEFT JOIN t_pd_base p on p.id=c.product where c.product is not null and p.id is not null and c.formula=f.id ) as num from t_pd_formula f  where f.org=" + oid + " and f.enabled=" + state;


        if (StringUtils.isNotBlank(keyword)) {

            sql += " and (name like '%" + keyword + "%' or code like '%" + keyword + "%')";
        }

        sql += " order by create_date desc";
        return pdFormulaDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getStopFormulaList(Integer oid, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};

        String sql = "select f.id as id,f.code as code,f.name as name,f.material_name as materialName,f.major_ingredient as majorIngredient,f.minor_ingredient as minorIngredient,f.products as products,f.create_name as createName,f.create_date as createDate,f.update_name as updateName,f.update_date as updateDate,f.enabled as enabled,f.memo as memo,(select\n" +
                "count(cm.id) from t_pd_composition_material_history cm LEFT JOIN t_pd_composition_material c on cm.composition_material=c.id and c.formula=cm.formula LEFT JOIN t_pd_base p on p.id=cm.product " +
                "where c.id is null and p.id is not null and cm.formula=f.id and cm.product is not null) as num from t_pd_formula f  where f.org=" + oid + " and f.enabled='0'";


        sql += " order by create_date desc";
        return pdFormulaDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public List<Map<String, Object>> getFormulaList(Integer oid, String state) {
        String sql = "select id as id,code as code,name as name,material_name as materialName,major_ingredient as majorIngredient,minor_ingredient as minorIngredient,products as products,create_name as createName,create_date as createDate,update_name as updateName,update_date as updateDate,enabled as enabled,memo as memo from t_pd_formula  where org=" + oid + " and enabled=" + state;
        sql += " order by create_date desc";
        return pdFormulaDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public PdFormula getPdFormula(Integer id) {
        PdFormula pdFormula = pdFormulaDao.get(id);
        String sql = "SELECT\n" +
                "m.id AS id,\n" +
                "m.code AS code,\n" +
                "m.name AS name,\n" +
                "m.specifications AS specifications,\n" +
                "m.model AS model,\n" +
                "m.unit AS unit,\n" +
                "m.unit_id AS unitId,\n" +
                "m.memo AS memo,\n" +
                "m.create_name AS createName,\n" +
                "m.create_date AS createDate,\n" +
                "m.enabled AS enabled,\n" +
                "f.amount as amount,\n" +
                "f.id as csmId\n" +
                "FROM\n" +
                "t_mt_base m\n" +
                "INNER JOIN t_pd_composition_material f ON m.id = f.material \n";
        if (pdFormula != null) {
            String fl = sql + " AND f.ingredients = '2'  where f.formula=" + id;
            pdFormula.setFlList(mtBaseDao.getSession().createSQLQuery(fl).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list());

            String zl = sql + " AND f.ingredients = '1'  where f.formula=" + id;
            pdFormula.setZlList(mtBaseDao.getSession().createSQLQuery(zl).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list());
        }
        return pdFormula;
    }

    @Override
    public PdFormula getLikeByApproval(Integer approvalId) {
        PdFormula pdFormula = pdFormulaDao.getByHQL("from PdFormula where  instanceChain like '"+approvalId+",%'  or instance_chain like '%,"+approvalId+"%'");

        return getPdFormula(pdFormula.getId());
    }

    @Override
    public PdFormula getByInstance(Integer approvalId) {
        PdFormula pdFormula = pdFormulaDao.getByHQL("from PdFormula where  instance = '"+approvalId+"'");

        return getPdFormula(pdFormula.getId());
    }

    @Override
    public PdFormulaHistory getPdFormulaHistoryById(Integer id) {
        if (id==null){
            return null;
        }
        PdFormulaHistory pdFormulaHistory=pdFormulaHistoryDao.get(id);
        String sql = "SELECT\n" +
                "m.id AS id,\n" +
                "m.code AS code,\n" +
                "m.name AS name,\n" +
                "m.specifications AS specifications,\n" +
                "m.model AS model,\n" +
                "m.unit AS unit,\n" +
                "m.unit_id AS unitId,\n" +
                "m.memo AS memo,\n" +
                "m.create_name AS createName,\n" +
                "m.create_date AS createDate,\n" +
                "m.enabled AS enabled,\n" +
                "f.amount as amount,\n" +
                "f.id as csmId\n" +
                "FROM\n" +
                "t_mt_base_history m\n" +
                "INNER JOIN t_pd_composition_material_history f ON m.id = f.material_history \n";
        if (pdFormulaHistory != null) {
            String fl = sql + " AND f.ingredients = '2'  where f.previous_id=" + pdFormulaHistory.getId();
            pdFormulaHistory.setFlList(mtBaseDao.getSession().createSQLQuery(fl).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list());

            String zl = sql + " AND f.ingredients = '1'  where f.previous_id=" + pdFormulaHistory.getId();
            pdFormulaHistory.setZlList(mtBaseDao.getSession().createSQLQuery(zl).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list());
        }
        return pdFormulaHistory;
    }

    @Override
    public String addOrUpdateFormula(Integer oid, User user, String jsonData) {

        //配方与材料已绑定 id 集合
        StringBuilder ids = new StringBuilder();


        PdFormula pdFormula = null;

        JSONObject data = JSONObject.fromObject(jsonData);
        if (StringUtils.isEmpty(data.optString("code"))) {
            return "请填写代号";

        }


        //主料
        JSONArray zlListJson = data.getJSONArray("zlList");
        //辅料
        JSONArray flListJson = data.getJSONArray("flList");

        //判断配方是不是新增
        if (data.optInt("id") == 0) {


            //配方更改
            PdFormula pdf = pdFormulaDao.getByHQL("from PdFormula where code='" + data.optString("code") + "' and org=" + user.getOid());
            if (pdf != null) {
                return "代号已存在";
            }
            //新增配方
            pdFormula = new PdFormula();
            pdFormula.setName(data.optString("name"));
            pdFormula.setCode(data.optString("code"));
            pdFormula.setMaterialName(data.optString("materialName"));
            pdFormula.setCreateDate(new Date());
            pdFormula.setCreator(user.getUserID());
            pdFormula.setCreateName(user.getUserName());
            pdFormula.setVersionNo(1);
            pdFormula.setEnabled("1");
            pdFormula.setOrg(user.getOid());
            pdFormula.setMajorIngredient(zlListJson.size());
            pdFormula.setMinorIngredient(flListJson.size());
            addOrUpdate(data, pdFormula, zlListJson, flListJson, user, true);
        } else {
            //配方更改
            PdFormula pdf = pdFormulaDao.getByHQL("from PdFormula where code='" + data.optString("code") + "' and org=" + user.getOid());
            if (pdf != null) {
                if (data.optInt("id") != pdf.getId()) {
                    return "代号已存在";
                }
            }

            PdFormula p = pdFormulaDao.get(data.optInt("id"));
            if (p == null) {
                return "id参数错误";


            }

            if(StringUtils.isNotEmpty(data.optString("product"))){
                addOrUpdate(data, p, zlListJson, flListJson, user, false);
                return "success";
            }




            if("1".equals(p.getApproveStatus())){
                return "有未完成的配方修改申请";
            }

            PdFormulaHistory newHis=pdFormulaHistoryDao.getByHQL("from PdFormulaHistory where formula="+p.getId()+" order by versionNo desc");

            PdFormulaHistory his = new PdFormulaHistory();
            his.setName(data.optString("name"));
            his.setCode(data.optString("code"));
            his.setMaterialName(data.optString("materialName"));
            his.setUpdateDate(new Date());
            his.setUpdator(user.getUserID());
            his.setUpdateName(user.getUserName());
            his.setVersionNo(p.getVersionNo() + 1);
            his.setMajorIngredient(zlListJson.size());
            his.setMinorIngredient(flListJson.size());
            his.setOrg(user.getOid());
            if(newHis!=null){
                his.setPreviousId(newHis.getId());
            }
            pdFormulaHistoryDao.save(his);


            User superUser=userService.getUserByRoleCode(oid,"super");

            if(superUser==null){
                new RuntimeException("没有董事长，无法修改");
            }
            //审批过程，配方审批
            ApprovalProcess ap = new ApprovalProcess();
            ap.setBusiness(his.getId());
            ap.setLevel(1);
            ap.setDescription("配方"+p.getCode()+"的修改");
            ap.setBusinessType(30);//业务类型  1-财务修改，2- 加班，3-请假 4-入库
            ap.setUserName(user.getUserName());//审批人
            ap.setCreateDate(new Date());
            ap.setToUser(superUser.getUserID());
            ap.setToUserName(superUser.getUserName());
            ap.setOrg(oid);
            ap.setFromUser(user.getUserID());
            ap.setHandleTime(new Date());
            ap.setApproveStatus("1");
            ap.setApproveMemo("配方"+p.getCode()+"的修改");
            ap.setHandleTime(new Date());
            approvalProcessDao.save(ap);

            p.setInstance(ap.getId());
            if(p.getInstanceChain()==null){
                p.setInstanceChain(ap.getId()+"");
            }else{
                p.setInstanceChain(p.getInstanceChain()+","+ap.getId());
            }

            p.setApproveStatus("1");


            updateForApproval(his, p.getId(), zlListJson, flListJson, user);
            HashMap<String,Object> hashMap=new HashMap<>();
            hashMap.put("ap", ap);
            hashMap.put("formula",his);

            //审批人
            clusterMessageSendingOperations.convertAndSendToUser(ap.getToUser()+"","/formulaEditApprovalList",null,null,null,null, JSON.toJSONString(hashMap));

            //申请人
            clusterMessageSendingOperations.convertAndSendToUser(ap.getFromUser()+"","/formulaEditApplyList",null,null,null,null, JSON.toJSONString(hashMap));


            swMessageService.rejectSend(0,1,hashMap,user.getUserID()+"","/formulaEditApplyList",null,null,user,"formulaEditApply");
            swMessageService.rejectSend(1,1,hashMap,superUser.getUserID()+"","/formulaEditApprovalList",null,null,superUser,"formulaEditApproval");
        }


        return "success";
    }

    /**
     * @param data
     * @param pdFormula
     * @param zlListJson
     * @param flListJson
     * @param user
     * @param flag       是否新增  true 新增， false修改
     */
    private void addOrUpdate(JSONObject data, PdFormula pdFormula, JSONArray zlListJson, JSONArray flListJson, User user, boolean flag) {
        Integer formulaHistoryId=0;
        if (flag) {
            pdFormulaDao.saveOrUpdate(pdFormula);
            pdFormulaDao.getSession().flush();

            formulaHistoryId=pdFormulaHistoryDao.setHistory(pdFormula);
        }
        MtCategory category = null;

        //先获取类别
        if (category == null) {

            List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(user.getOid(), null);
            for (MtCategory m : mtCategories) {
                if ("构成商品的原辅材料".equals(m.getName())) {

                    List<MtCategory> l = materielService.getMtCategoriesById(m.getId());
                    for (MtCategory m2 : l) {
                        if ("待分类".equals(m2.getName())) {
                            category = m2;
                        }
                    }
                }
            }
        }
        //把产品绑定到配方对象中
        if (data.optInt("product") != 0) {
            pdFormula.setProduct(data.optInt("product"));
        }

        for (int i = 0; i < zlListJson.size(); i++) {
            JSONObject z = zlListJson.getJSONObject(i);

            int amount=z.optInt("amount");
            if(amount==0){
                throw new RuntimeException("请填写正确的份数");
            }
            //如果没有id，说明是新增的主料
            if (StringUtils.isEmpty(z.optString("id")) || "\"null\"".equals(z.optString("id"))) {

                MtBase mt = mtBaseDao.getByHQL("from MtBase where code='" + z.optString("code") + "' and org=" + user.getOid());
                if (mt != null) {
                    throw new RuntimeException("该材料代号已存在");

                }


                MtBase mtBase = new MtBase();
                mtBase.setCategory(category);
                mtBase.setCategory_(category.getId());
                mtBase.setCreateDate(new Date());
                mtBase.setCreator(user.getUserID());
                mtBase.setCreateName(user.getUserName());
                mtBase.setVersionNo(1);
                mtBase.setEnabledTime(new Date());
                mtBase.setEnabled("1");
                mtBase.setOrg(user.getOid());
                mtBase.setSource("3");
                if ("1".equals(z.optString("isCurrent"))) {
                    mtBase.setIsCurrent("1");
                    mtBase.setOrigin("6");
                } else {
                    mtBase.setIsCurrent("0");
                    mtBase.setOrigin("7");
                }
                mtBase.setCode(z.optString("code"));
                mtBase.setName(z.optString("name"));
                mtBase.setUnit(z.optString("unit"));
                mtBase.setUnitId(z.optInt("unitId"));
                mtBase.setSpecifications(z.optString("specifications"));
                mtBase.setModel(z.optString("model"));
                mtBase.setMemo(z.optString("memo"));
                mtBase.setOperation("1");
                mtBaseDao.save(mtBase);
                mtBaseDao.getSession().flush();
                Integer mtHisId=mtBaseHistoryDao.insert(mtBase);

                if (mtBase.getUnitId() != null) {
                    //更新计量单位
                    unitService.selectUnit(mtBase.getUnitId(), 6);
                }
                addPdCompositionFormulaAndMaterial(mtBase, pdFormula, user, "1", z.optInt("amount"), flag,formulaHistoryId,mtHisId);
            } else {
                MtBase mtb = new MtBase();
                mtb.setId(z.optInt("id"));
                mtb.setCode(z.optString("code"));

                MtBaseHistory mtBaseHistory=mtBaseHistoryDao.getNew(mtb.getId());

                addPdCompositionFormulaAndMaterial(mtb, pdFormula, user, "1", z.optInt("amount"), flag,formulaHistoryId,mtBaseHistory.getId());
            }

        }

        for (int i = 0; i < flListJson.size(); i++) {
            JSONObject f = flListJson.getJSONObject(i);
            int amount=f.optInt("amount");
            if(amount==0){
                throw new RuntimeException("请填写正确的份数");
            }
            //如果没有id，说明是新增的主料
            if (StringUtils.isEmpty(f.optString("id")) || "\"null\"".equals(f.optString("id"))) {

                MtBase mt = mtBaseDao.getByHQL("from MtBase where code='" + f.optString("code") + "' and org=" + user.getOid());
                if (mt != null) {
                    throw new RuntimeException("材料代号\"" + f.optString("code") + "\"已存在");

                }

                MtBase mtBase = new MtBase();
                mtBase.setCategory(category);
                mtBase.setCreateDate(new Date());
                mtBase.setCreator(user.getUserID());
                mtBase.setCreateName(user.getUserName());
                mtBase.setVersionNo(1);
                mtBase.setEnabledTime(new Date());
                mtBase.setEnabled("1");
                mtBase.setOrg(user.getOid());
                mtBase.setSource("3");
                if ("1".equals(f.optString("code"))) {
                    mtBase.setIsPurchased("1");
                    mtBase.setOrigin("6");
                } else {
                    mtBase.setIsPurchased("0");
                    mtBase.setOrigin("7");
                }
                mtBase.setCode(f.optString("code"));
                mtBase.setName(f.optString("name"));
                mtBase.setUnit(f.optString("unit"));
                mtBase.setUnitId(f.optInt("unitId"));
                mtBase.setSpecifications(f.optString("specifications"));
                mtBase.setModel(f.optString("model"));
                mtBase.setMemo(f.optString("memo"));
                mtBase.setOperation("1");
                mtBaseDao.save(mtBase);
                mtBaseDao.getSession().flush();
                Integer mtHisId=mtBaseHistoryDao.insert(mtBase);
                if (mtBase.getUnitId() != null) {
                    //更新计量单位
                    unitService.selectUnit(mtBase.getUnitId(), 6);
                }

                addPdCompositionFormulaAndMaterial(mtBase, pdFormula, user, "2", f.optInt("amount"), flag,formulaHistoryId,mtHisId);
            } else {
                MtBase mtb = new MtBase();
                mtb.setId(f.optInt("id"));
                mtb.setCode(f.optString("code"));
                MtBaseHistory mtBaseHistory=mtBaseHistoryDao.getNew(mtb.getId());
                addPdCompositionFormulaAndMaterial(mtb, pdFormula, user, "2", f.optInt("amount"), flag,formulaHistoryId,mtBaseHistory.getId());
            }


        }


        addPdCompositionProductAndFormula(pdFormula, user);
    }

    /**
     * @param zlListJson
     * @param flListJson
     * @param user
     */
    private void updateForApproval(PdFormulaHistory pdFormulaHistory, Integer pdFormulaId, JSONArray zlListJson, JSONArray flListJson, User user) {

        MtCategory category = null;

        //先获取类别
        if (category == null) {

            List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(user.getOid(), null);
            for (MtCategory m : mtCategories) {
                if ("构成商品的原辅材料".equals(m.getName())) {

                    List<MtCategory> l = materielService.getMtCategoriesById(m.getId());
                    for (MtCategory m2 : l) {
                        if ("待分类".equals(m2.getName())) {
                            category = m2;
                        }
                    }

                }
            }
        }
        for (int i = 0; i < zlListJson.size(); i++) {
            Integer mtBaseId = 0;
            JSONObject z = zlListJson.getJSONObject(i);
            //如果没有id，说明是新增的主料
            if (StringUtils.isEmpty(z.optString("id")) || "\"null\"".equals(z.optString("id"))) {

                MtBase mt = mtBaseDao.getByHQL("from MtBase where code='" + z.optString("code") + "' and org=" + user.getOid());
                if (mt != null) {
                    throw new RuntimeException("该材料代号已存在");

                }
                MtBaseHistory mtBaseHistory = new MtBaseHistory();
                mtBaseHistory.setCategory(category.getId());
                mtBaseHistory.setCreateDate(new Date());
                mtBaseHistory.setCreator(user.getUserID());
                mtBaseHistory.setCreateName(user.getUserName());
                mtBaseHistory.setVersionNo(1);
                mtBaseHistory.setEnabledTime(new Date());
                mtBaseHistory.setEnabled("1");
                mtBaseHistory.setOrg(user.getOid());
                mtBaseHistory.setSource("3");
                if ("1".equals(z.optString("isCurrent"))) {
                    mtBaseHistory.setIsCurrent("1");
                    mtBaseHistory.setOrigin("6");
                } else {
                    mtBaseHistory.setIsCurrent("0");
                    mtBaseHistory.setOrigin("7");
                }
                mtBaseHistory.setCode(z.optString("code"));
                mtBaseHistory.setName(z.optString("name"));
                mtBaseHistory.setUnit(z.optString("unit"));
                mtBaseHistory.setUnitId(z.optInt("unitId"));
                mtBaseHistory.setSpecifications(z.optString("specifications"));
                mtBaseHistory.setModel(z.optString("model"));
                mtBaseHistory.setMemo(z.optString("memo"));
                mtBaseHistory.setOperation("1");

                mtBaseHistory.setPreviousId(pdFormulaHistory.getId());
                mtBaseHistoryDao.save(mtBaseHistory);
                mtBaseHistoryDao.getSession().flush();


                if (mtBaseHistory.getUnitId() != null) {
                    //更新计量单位
                    unitService.selectUnit(mtBaseHistory.getUnitId(), 6);
                }
                updatePdCompositionFormulaAndMaterialForApproval(z.optString("code"), pdFormulaHistory, pdFormulaId,mtBaseHistory.getId(), mtBaseId, user, "1", z.optInt("amount"));
            } else {

                mtBaseId = z.optInt("id");
                MtBaseHistory mtBaseHistory = new MtBaseHistory();
                mtBaseHistory.setCategory(category.getId());
                mtBaseHistory.setCreateDate(new Date());
                mtBaseHistory.setCreator(user.getUserID());
                mtBaseHistory.setCreateName(user.getUserName());
                mtBaseHistory.setVersionNo(1);
                mtBaseHistory.setEnabledTime(new Date());
                mtBaseHistory.setEnabled("1");
                mtBaseHistory.setOrg(user.getOid());
                mtBaseHistory.setSource("3");
                mtBaseHistory.setMaterial(mtBaseId);
                if ("1".equals(z.optString("code"))) {
                    mtBaseHistory.setIsPurchased("1");
                    mtBaseHistory.setOrigin("6");
                } else {
                    mtBaseHistory.setIsPurchased("0");
                    mtBaseHistory.setOrigin("7");
                }
                mtBaseHistory.setCode(z.optString("code"));
                mtBaseHistory.setName(z.optString("name"));
                mtBaseHistory.setUnit(z.optString("unit"));
                mtBaseHistory.setUnitId(z.optInt("unitId"));
                mtBaseHistory.setSpecifications(z.optString("specifications"));
                mtBaseHistory.setModel(z.optString("model"));
                mtBaseHistory.setMemo(z.optString("memo"));
                mtBaseHistory.setOperation("1");

                mtBaseHistory.setPreviousId(pdFormulaHistory.getId());
                mtBaseHistoryDao.save(mtBaseHistory);
                updatePdCompositionFormulaAndMaterialForApproval(z.optString("code"), pdFormulaHistory, pdFormulaId,mtBaseHistory.getId(), mtBaseId, user, "1", z.optInt("amount"));
            }


        }

        for (int i = 0; i < flListJson.size(); i++) {
            JSONObject f = flListJson.getJSONObject(i);
            Integer mtBaseId = 0;
            //如果没有id，说明是新增的主料
            if (StringUtils.isEmpty(f.optString("id")) || "\"null\"".equals(f.optString("id"))) {

                MtBase mt = mtBaseDao.getByHQL("from MtBase where code='" + f.optString("code") + "' and org=" + user.getOid());
                if (mt != null) {
                    throw new RuntimeException("材料代号\"" + f.optString("code") + "\"已存在");

                }

                //先获取类别
                if (category == null) {

                    List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(user.getOid(), null);
                    for (MtCategory m : mtCategories) {
                        if ("构成商品的原辅材料".equals(m.getName())) {

                            List<MtCategory> l = materielService.getMtCategoriesById(m.getId());
                            for (MtCategory m2 : l) {
                                if ("待分类".equals(m2.getName())) {
                                    category = m2;
                                }
                            }

                        }
                    }
                }
                MtBaseHistory mtBaseHistory = new MtBaseHistory();
                mtBaseHistory.setCategory(category.getId());
                mtBaseHistory.setCreateDate(new Date());
                mtBaseHistory.setCreator(user.getUserID());
                mtBaseHistory.setCreateName(user.getUserName());
                mtBaseHistory.setVersionNo(1);
                mtBaseHistory.setEnabledTime(new Date());
                mtBaseHistory.setEnabled("1");
                mtBaseHistory.setOrg(user.getOid());
                mtBaseHistory.setSource("3");
                if ("1".equals(f.optString("code"))) {
                    mtBaseHistory.setIsPurchased("1");
                    mtBaseHistory.setOrigin("6");
                } else {
                    mtBaseHistory.setIsPurchased("0");
                    mtBaseHistory.setOrigin("7");
                }
                mtBaseHistory.setCode(f.optString("code"));
                mtBaseHistory.setName(f.optString("name"));
                mtBaseHistory.setUnit(f.optString("unit"));
                mtBaseHistory.setUnitId(f.optInt("unitId"));
                mtBaseHistory.setSpecifications(f.optString("specifications"));
                mtBaseHistory.setModel(f.optString("model"));
                mtBaseHistory.setMemo(f.optString("memo"));
                mtBaseHistory.setOperation("1");

                mtBaseHistory.setPreviousId(pdFormulaHistory.getId());
                mtBaseHistoryDao.save(mtBaseHistory);


                if (mtBaseHistory.getUnitId() != null) {
                    //更新计量单位
                    unitService.selectUnit(mtBaseHistory.getUnitId(), 6);
                }
                updatePdCompositionFormulaAndMaterialForApproval(f.optString("code"), pdFormulaHistory, pdFormulaId,mtBaseHistory.getId(),mtBaseId, user, "2", f.optInt("amount"));


            } else {
                mtBaseId = f.optInt("id");
                MtBaseHistory mtBaseHistory = new MtBaseHistory();
                mtBaseHistory.setCategory(category.getId());
                mtBaseHistory.setCreateDate(new Date());
                mtBaseHistory.setCreator(user.getUserID());
                mtBaseHistory.setCreateName(user.getUserName());
                mtBaseHistory.setVersionNo(1);
                mtBaseHistory.setEnabledTime(new Date());
                mtBaseHistory.setEnabled("1");
                mtBaseHistory.setOrg(user.getOid());
                mtBaseHistory.setSource("3");
                mtBaseHistory.setMaterial(mtBaseId);
                if ("1".equals(f.optString("code"))) {
                    mtBaseHistory.setIsPurchased("1");
                    mtBaseHistory.setOrigin("6");
                } else {
                    mtBaseHistory.setIsPurchased("0");
                    mtBaseHistory.setOrigin("7");
                }
                mtBaseHistory.setCode(f.optString("code"));
                mtBaseHistory.setName(f.optString("name"));
                mtBaseHistory.setUnit(f.optString("unit"));
                mtBaseHistory.setUnitId(f.optInt("unitId"));
                mtBaseHistory.setSpecifications(f.optString("specifications"));
                mtBaseHistory.setModel(f.optString("model"));
                mtBaseHistory.setMemo(f.optString("memo"));
                mtBaseHistory.setOperation("1");

                mtBaseHistory.setPreviousId(pdFormulaHistory.getId());
                mtBaseHistoryDao.save(mtBaseHistory);

                updatePdCompositionFormulaAndMaterialForApproval(f.optString("code"), pdFormulaHistory, pdFormulaId,mtBaseHistory.getId(),mtBaseId, user, "2", f.optInt("amount"));

            }

        }

        // 更换配方
    }

    private void updatePdCompositionFormulaAndMaterialForApproval(String code, PdFormulaHistory pdFormulaHistory, Integer pdFormulaId, Integer mtBaseHisId,Integer mtBaseId, User user, String type, Integer num) {
        //判断配方，材料有没有绑定
        PdCompositionMaterial pm = pdCompositionMaterialDao.getByHQL("from PdCompositionMaterial where material=" + mtBaseId + " and formula=" + pdFormulaId);

        //为null 说明未绑定
        if (pm == null) {

            PdCompositionMaterialHistory pmh = new PdCompositionMaterialHistory();
            pmh.setOperation("1");
            pmh.setCreateDate(new Date());
            pmh.setCreateName(user.getUserName());
            pmh.setCreator(user.getUserID());
            if(mtBaseId!=null&&mtBaseId!=0){
                //新增的材料
                pmh.setMaterial(mtBaseId);
            }else{
                //选择材料
                pmh.setMaterial(mtBaseHisId);
            }

            pmh.setInnerSn(code);
            pmh.setFormula(pdFormulaId);
            pmh.setOrg(user.getOid());
            pmh.setVersionNo(1);
            pmh.setIngredients(type);
            pmh.setAmount(num);
            pmh.setMaterialHistory(mtBaseHisId);
            pmh.setPreviousId(pdFormulaHistory.getId());
            pdCompositionMaterialHistoryDao.save(pmh);
        } else {
            PdCompositionMaterialHistory pmh = new PdCompositionMaterialHistory();

            //不为null，说明已绑定，修改装配数量
            pmh.setUpdateName(user.getUserName());
            pmh.setUpdateDate(new Date());
            pmh.setUpdator(user.getUserID());
            pmh.setOperation("3");
            pmh.setVersionNo(pm.getVersionNo() + 1);
            pmh.setAmount(num);
            pmh.setMemo(pm.getId() + "");//就是 composition_material字段，审批通过后再重新赋值
            pmh.setCreateDate(pm.getCreateDate());
            pmh.setCreateName(pm.getCreateName());
            pmh.setCreator(pm.getCreator());
            pmh.setIngredients(type);
            pmh.setMaterialHistory(mtBaseHisId);
            pmh.setPreviousId(pdFormulaHistory.getId());
            //添加进历史
            pdCompositionMaterialHistoryDao.save(pmh);
        }
    }

    @Override
    public String bindOperation(String baseList, Integer formula, User user) {
        String[] s = baseList.split(",");
        PdFormula pdFormula = pdFormulaDao.get(formula);
        if (pdFormula == null) {
            return "配方信息错误";
        }
        for (String str : s) {

            pdFormula.setProduct(Integer.valueOf(str));
            addPdCompositionProductAndFormula(pdFormula, user);

        }
        return "success";

    }

    @Override
    public String deleteFormula(Integer id, Integer oid) {
        PdFormula pdFormula = pdFormulaDao.get(id);
        if (pdFormula == null) {
            return "配方参数有误";
        }

        if (!oid.equals(pdFormula.getOrg())) {
            return "配方参数有误";
        }

        List<PdCompositionMaterial> list = pdCompositionMaterialDao.getListByHQL("from PdCompositionMaterial m LEFT JOIN PdBase p on p.id=m.product where m.formula=" + pdFormula.getId() + " and m.product is not null and p.id is not null");
        if (list.size() > 0) {
            return "操作失败，因为还有使用该配方的产品或零件！！";
        }

        List<PdCompositionMaterialHistory> historyList = pdCompositionMaterialHistoryDao.getListByHQL("from PdCompositionMaterialHistory where formula=" + pdFormula.getId() + " and product is not null");
        if (historyList.size() > 0) {
            return "删除失败，因为此配方曾被某产品或零件使用过！！";
        }


        pdFormulaDao.delete(pdFormula);

        Map<String, Object> params = new HashMap<>();
        params.put("formula", pdFormula.getId());
        String hql = "delete from PdCompositionMaterial where formula=:formula";
        pdCompositionMaterialDao.queryHQLWithNamedParams(hql, params);

        String hql2 = "delete from PdCompositionMaterialHistory where formula=:formula";
        pdCompositionMaterialHistoryDao.queryHQLWithNamedParams(hql2, params);
        return "success";
    }

    @Override
    public String startStopFormula(Integer id, String state, User user) {
        PdFormula pdFormula = pdFormulaDao.get(id);
        if (pdFormula == null) {
            return "配方参数有误";
        }

        if (!user.getOid().equals(pdFormula.getOrg())) {
            return "配方参数有误";
        }

        List<PdCompositionMaterial> list = pdCompositionMaterialDao.getListByHQL("from PdCompositionMaterial m LEFT JOIN PdBase p on p.id=m.product where m.formula=" + pdFormula.getId() + " and m.product is not null and p.id is not null");
        if (list.size() > 0) {
            return "操作失败，因为还有使用该配方的产品或零件！！";
        }


        pdFormula.setUpdateDate(new Date());
        pdFormula.setUpdateName(user.getUserName());
        pdFormula.setUpdator(user.getUserID());
        if ("1".equals(state)) {
            pdFormula.setOperation("2");
            pdFormula.setEnabled("1");
        } else {
            pdFormula.setOperation("4");
            pdFormula.setEnabled("0");
        }


        pdFormulaDao.update(pdFormula);

        pdFormulaHistoryDao.setHistory(pdFormula);

        return "success";
    }

    @Override
    public Map<String, Object> getBindingPdList(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql = "select\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.update_name AS updateName,\n" +
                "p.update_date AS updateDate,\n" +
                "p.phrase AS phrase,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "cm.product as product,\n" +
                "cm.formula as formula\n" +
                "from\n" +
                "t_pd_composition_material cm\n" +
                "LEFT JOIN t_pd_base p on cm.product=p.id\n" +
                "where cm.product is not null and  p.id is not null and cm.formula=" + id;
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getBeforeBindingPdList(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql = "select\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.update_name AS updateName,\n" +
                "p.update_date AS updateDate,\n" +
                "p.phrase AS phrase,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "cm.product as product,\n" +
                "cm.formula as formula\n" +
                "from\n" +
                "t_pd_composition_material_history cm\n" +
                "LEFT JOIN t_pd_composition_material c on cm.composition_material=c.id and c.formula=" + id + "\n" +
                "LEFT JOIN t_pd_base p on cm.product=p.id\n" +
                "where c.id is null and p.id is not null and cm.product is not null  and cm.formula=" + id;
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getFormulaListByPdBase(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql = "select\n" +
                "p.name as name,\n" +
                "p.code AS code,\n" +
                "p.material_name AS materialName,\n" +
                "p.major_ingredient AS majorIngredient,\n" +
                "p.minor_ingredient AS minorIngredient,\n" +
                "cm.create_name AS createName,\n" +
                "cm.create_date AS createDate,\n" +
                "cm.update_name AS updateName,\n" +
                "cm.update_date AS updateDate,\n" +
                "cm.product as product,\n" +
                "cm.formula as formula,\n" +
                "cm.operation as operation\n" +
                "from\n" +
                "t_pd_composition_material_history cm\n" +
                "LEFT JOIN t_pd_formula p on cm.formula=p.id\n" +
                "where cm.formula is not null and cm.product=" + id +
                " order by cm.id desc";
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getBindingFormulaList(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql = "select\n" +
                "p.name as name,\n" +
                "p.code AS code,\n" +
                "p.material_name AS materialName,\n" +
                "p.major_ingredient AS majorIngredient,\n" +
                "p.minor_ingredient AS minorIngredient,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.update_name AS updateName,\n" +
                "p.update_date AS updateDate,\n" +
                "cm.product as product,\n" +
                "cm.material as material\n" +
                "from\n" +
                "t_pd_composition_material cm\n" +
                "LEFT JOIN t_pd_formula p on cm.formula=p.id\n" +
                "where cm.formula is not null and cm.material=" + id;
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getBeforeBindingFormulaList(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql = "select\n" +
                "p.name as name,\n" +
                "p.code AS code,\n" +
                "p.material_name AS materialName,\n" +
                "p.major_ingredient AS majorIngredient,\n" +
                "p.minor_ingredient AS minorIngredient,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.update_name AS updateName,\n" +
                "p.update_date AS updateDate,\n" +
                "cm.product as product,\n" +
                "cm.formula as formula\n" +
                "from\n" +
                "t_pd_composition_material_history cm\n" +
                "LEFT JOIN t_pd_composition_material c on cm.composition_material=c.id and c.material=" + id + "\n" +
                "LEFT JOIN t_pd_formula p on cm.formula=p.id\n" +
                "where c.id is null and cm.material=" + id;
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public String formulaApproval(ApprovalProcess approvalProcess,String sessionid) {
        PdFormula pdFormula = pdFormulaDao.getByHQL("from PdFormula where instance=" + approvalProcess.getId());
        pdFormula.setApproveStatus(approvalProcess.getApproveStatus());
        pdFormula.setRejectReasionDesc(approvalProcess.getReason());
        User user=userService.getUserByID(approvalProcess.getFromUser());
        User superUser=userService.getUserByID(approvalProcess.getToUser());
        PdFormulaHistory pdFormulaHistory = pdFormulaHistoryDao.getByHQL("from PdFormulaHistory where id=" + approvalProcess.getBusiness());
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("ap",approvalProcess);

        approvalProcessDao.update(approvalProcess);

        //2批准 3驳回
        if ("2".equals(approvalProcess.getApproveStatus())) {


            pdFormulaHistory.setFormula(pdFormula.getId());

            pdFormula.setVersionNo(pdFormula.getVersionNo() + 1);
            pdFormula.setName(pdFormulaHistory.getName());
            pdFormula.setCode(pdFormulaHistory.getCode());
            pdFormula.setMaterialName(pdFormulaHistory.getMaterialName());
            pdFormula.setUpdateDate(new Date());//修改时间，使用审批时间
            pdFormula.setUpdator(pdFormulaHistory.getUpdator());
            pdFormula.setUpdateName(pdFormulaHistory.getUpdateName());
            pdFormula.setVersionNo(pdFormulaHistory.getVersionNo());
            pdFormula.setMajorIngredient(pdFormulaHistory.getMajorIngredient());
            pdFormula.setMinorIngredient(pdFormulaHistory.getMinorIngredient());
            pdFormula.setApproveStatus("2");//审批通过

            List<MtBaseHistory> mtBaseHistoryList = mtBaseHistoryDao.getListByHQL("from MtBaseHistory where material is null and previousId=" + pdFormulaHistory.getId());

            for (MtBaseHistory mh : mtBaseHistoryList) {
                if ("1".equals(mh.getOperation())) {
                    MtBase mt = mtBaseDao.getByHQL("from MtBase where code='" + mh.getCode() + "' and org=" + mh.getOrg());
                    if (mt != null) {
                        approvalProcess.setApproveStatus("3");
                        approvalProcess.setReason("材料代号\"" + mh.getCode() + "\"已存在,自动驳回该次配方修改");
                        swMessageService.rejectSend(0,-1,hashMap,user.getUserID()+"","/formulaEditApplyList",null,null,user,"formulaEditApply");
                        swMessageService.rejectSend(-1,-1,hashMap,superUser.getUserID()+"","/formulaEditApprovalList",null,null,superUser,"formulaEditApproval");

                        return "材料代号\"" + mh.getCode() + "\"已存在,自动驳回该次配方修改";
                    }
                    MtCategory category=materielService.getMtCategoryById(mh.getCategory());
                    MtBase mtBase = new MtBase();
                    BeanUtils.copyProperties(mh, mtBase);
                    mtBase.setId(null);
                    mtBase.setCategory(category);
                    mtBase.setCreateDate(new Date());
                    mtBase.setOperation("1");
                    mtBaseDao.save(mtBase);
                    mh.setMaterial(mtBase.getId());

                }
            }

            List<PdCompositionMaterialHistory> pdCompositionMaterialHistoryList = pdCompositionMaterialHistoryDao.getListByHQL("from PdCompositionMaterialHistory where previousId=" + pdFormulaHistory.getId());
            //删除
            String ids = "";
            for (PdCompositionMaterialHistory pmh : pdCompositionMaterialHistoryList) {
                if ("3".equals(pmh.getOperation())) {
                    ids+= pmh.getMemo() + ",";
                }
            }
            if (ids.length() > 0) {
                deletePdCompositionFormula(pdFormula.getId(), ids.substring(0, ids.length() - 1));
            } else {
                deletePdCompositionFormulaAndMaterial(pdFormula.getId());
            }


            for (PdCompositionMaterialHistory pmh : pdCompositionMaterialHistoryList) {
                if ("3".equals(pmh.getOperation())) {


                    PdCompositionMaterial pdCompositionMaterial=pdCompositionMaterialDao.get(Integer.valueOf(pmh.getMemo()));

                    pdCompositionMaterial.setAmount(pmh.getAmount());
                    pdCompositionMaterial.setUpdator(pmh.getUpdator());
                    pdCompositionMaterial.setUpdateName(pmh.getUpdateName());
                    pdCompositionMaterial.setUpdateDate(new Date());
                    pdCompositionMaterial.setVersionNo(pmh.getVersionNo());

                    pmh.setOrg(pdCompositionMaterial.getOrg());
                    pmh.setCompositionMaterial(pdCompositionMaterial.getId());
                    pmh.setInnerSn(pdCompositionMaterial.getInnerSn());
                    pmh.setMaterial(pdCompositionMaterial.getMaterial());
                    pmh.setFormula(pdCompositionMaterial.getFormula());
                    pmh.setCompositionMaterial(Integer.valueOf(pmh.getMemo()));

                    pdCompositionMaterialDao.update(pdCompositionMaterial);
                    pdCompositionMaterialHistoryDao.update(pmh);

                }else{

                    for (MtBaseHistory mh:mtBaseHistoryList) {
                        if(pmh.getMaterial().equals(mh.getId())&&pmh.getPreviousId().equals(mh.getPreviousId())){
                            pmh.setMaterial(mh.getMaterial());
                            break;
                        }
                    }
                    pmh.setFormula(pdFormula.getId());
                    PdCompositionMaterial pdCompositionMaterial=new PdCompositionMaterial();
                    BeanUtils.copyProperties(pmh,pdCompositionMaterial);
                    pdCompositionMaterial.setId(null);
                    pdCompositionMaterialDao.save(pdCompositionMaterial);

                    pmh.setCompositionMaterial(pdCompositionMaterial.getId());
                    pdCompositionMaterialHistoryDao.update(pmh);
                }

            }

            hashMap.put("status",1);
            hashMap.put("pdFormula",pdFormula);
            swMessageService.rejectSend(0,-1,hashMap,user.getUserID()+"","/formulaEditApplyList",null,null,user,"formulaEditApply");
            swMessageService.rejectSend(-1,-1,hashMap,superUser.getUserID()+"","/formulaEditApprovalList",null,null,superUser,"formulaEditApproval");
            String s=approvalProcess.getApproveMemo()+"申请被批准了!";

            suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),approvalProcess.getFromUser(),null,0);


        }else{
            hashMap.put("status",1);
            hashMap.put("pdFormula",pdFormula);
            //pdFormulaHistoryDao.deleteById(pdFormulaHistory.getId());
            swMessageService.rejectSend(0,-1,hashMap,user.getUserID()+"","/formulaEditApplyList",null,null,user,"formulaEditApply");
            swMessageService.rejectSend(-1,-1,hashMap,superUser.getUserID()+"","/formulaEditApprovalList",null,null,superUser,"formulaEditApproval");
            String s=approvalProcess.getApproveMemo()+"申请被驳回了!";

            suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),approvalProcess.getFromUser(),null,0);
        }

        pdFormulaDao.update(pdFormula);

        return "success";
    }


    @Override
    public List<Map<String, Object>> getPdFoBaseList(Integer oid) {
        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "p.phrase AS phrase,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight from t_pd_base p \n" +
                "LEFT JOIN t_pd_composition_material cm on p.id=cm.product\n" +
                "where p.oid=" + oid + " and cm.id is null and p.composition='3' order by p.create_date ";
        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
    }


    //配方与材料绑定
    //formulaHistoryId 配方历史id
    //mtHisId 材料历史id
    private void addPdCompositionFormulaAndMaterial(MtBase mtBase, PdFormula pdFormula, User user, String type, Integer num, boolean flag,Integer formulaHistoryId,Integer mtHisId) {

        //先判断配方是否是直接添加
        if (flag) {
            PdCompositionMaterial pm = new PdCompositionMaterial();
            pm.setOperation("1");
            pm.setCreateDate(new Date());
            pm.setCreateName(user.getUserName());
            pm.setCreator(user.getUserID());
            pm.setMaterial(mtBase.getId());
            pm.setInnerSn(mtBase.getCode());
            pm.setFormula(pdFormula.getId());
            pm.setOrg(user.getOid());
            pm.setVersionNo(1);
            pm.setIngredients(type);
            pm.setAmount(num);
            pm.setPreviousId(formulaHistoryId);
            pdCompositionMaterialDao.save(pm);
            pdCompositionMaterialDao.getSession().flush();
            pdCompositionMaterialHistoryDao.insert(pm,mtHisId);

        } else {
            //判断配方，材料有没有绑定
            PdCompositionMaterial pm = pdCompositionMaterialDao.getByHQL("from PdCompositionMaterial where material=" + mtBase.getId() + " and formula=" + pdFormula.getId());

            //为null 说明未绑定
            if (pm == null) {

                pm = new PdCompositionMaterial();
                pm.setOperation("1");
                pm.setCreateDate(new Date());
                pm.setCreateName(user.getUserName());
                pm.setCreator(user.getUserID());
                pm.setMaterial(mtBase.getId());
                pm.setInnerSn(mtBase.getCode());
                pm.setFormula(pdFormula.getId());
                pm.setOrg(user.getOid());
                pm.setVersionNo(1);
                pm.setIngredients(type);
                pm.setAmount(num);
                pm.setPreviousId(formulaHistoryId);
                pdCompositionMaterialDao.save(pm);

                pdCompositionMaterialDao.getSession().flush();

                pdCompositionMaterialHistoryDao.insert(pm,mtHisId);
            } else {
                //不为null，说明已绑定，修改装配数量
                pm.setUpdateName(user.getUserName());
                pm.setUpdateDate(new Date());
                pm.setUpdator(user.getUserID());
                pm.setOperation("3");
                pm.setVersionNo(pm.getVersionNo() + 1);
                pm.setAmount(num);
                pm.setPreviousId(formulaHistoryId);
                pdCompositionMaterialDao.update(pm);
                //添加进历史
                pdCompositionMaterialHistoryDao.insert(pm,mtHisId);
            }
        }
    }


    //产品与配方绑定
    private void addPdCompositionProductAndFormula(PdFormula pdFormula, User user) {

        //先判断配方是否是直接添加
        if (pdFormula.getProduct() != null && !"0".equals(pdFormula.getProduct())) {

            //判断产品有没有关联
            PdCompositionMaterial pm = pdCompositionMaterialDao.getByHQL("from PdCompositionMaterial where product=" + pdFormula.getProduct() + " and formula is not null");

            //如果关联
            if (pm != null) {
                //如果与配方id不一致，说明是修改的材料
                if (!pm.getFormula().equals(pdFormula.getId())) {
                    PdBase pdBase=pdBaseDao.get(pdFormula.getProduct());
                    pdBase.setOperation("8");
                    Integer pdHisId=pdBaseHistoryDao.setHistory(pdBase);
                    Integer forHisId=pdFormulaHistoryDao.setHistory(pdFormula);


                    pm.setFormula(pdFormula.getId());
                    pm.setUpdateName(user.getUserName());
                    pm.setUpdateDate(new Date());
                    pm.setUpdator(user.getUserID());
                    pm.setOperation("3");
                    pm.setVersionNo(pm.getVersionNo() + 1);
                    pdCompositionMaterialDao.update(pm);
                    //添加进历史
                    pdCompositionMaterialHistoryDao.insertPdFormula(pm,forHisId,pdHisId);
                }
            } else {
                PdBase pdBase=pdBaseDao.get(pdFormula.getProduct());
                pdBase.setOperation("8");
                Integer pdHisId=pdBaseHistoryDao.setHistory(pdBase);
                Integer forHisId=pdFormulaHistoryDao.setHistory(pdFormula);


                pm = new PdCompositionMaterial();
                pm.setOperation("1");
                pm.setCreateDate(new Date());
                pm.setCreateName(user.getUserName());
                pm.setCreator(user.getUserID());
                pm.setFormula(pdFormula.getId());
                pm.setProduct(pdFormula.getProduct());

                pm.setOrg(user.getOid());
                pm.setVersionNo(1);
                pdCompositionMaterialDao.save(pm);

                pdCompositionMaterialDao.getSession().flush();

                //添加进历史
                pdCompositionMaterialHistoryDao.insertPdFormula(pm,forHisId,pdHisId);
            }

        }
    }


    private void deletePdCompositionFormula(Integer formulaId, String ids) {


        String hql = "delete from PdCompositionMaterial where formula=" + formulaId + " and ingredients in ('1','2') and id not in(" + ids + ")";
        pdCompositionMaterialDao.queryHQLWithNamedParams(hql, new HashMap<>());
    }

    private void deletePdCompositionFormulaAndMaterial(Integer formulaId, String ids) {


        String hql = "delete from PdCompositionMaterial where formula=" + formulaId + " and ingredients in ('1','2') and material not in(" + ids + ")";
        pdCompositionMaterialDao.queryHQLWithNamedParams(hql, new HashMap<>());
    }

    private void deletePdCompositionFormulaAndMaterial(Integer formulaId) {


        String hql = "delete from PdCompositionMaterial where formula=" + formulaId + " and ingredients in ('1','2') ";
        pdCompositionMaterialDao.queryHQLWithNamedParams(hql, new HashMap<>());
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=null;
        switch (code){
            case "formulaEditApproval":
                String hql = "select count(id)  from ApprovalProcess where approveStatus=:approveStatus and toUser = :toUser and businessType=:businessType";
                HashMap<String, Object> param = new HashMap<>();
                param.put("approveStatus", "1");
                param.put("businessType", 30);
                param.put("toUser", user.getUserID());
                Long num = (Long) approvalProcessDao.getByHQLWithNamedParams(hql,param);
                number = num.intValue();
                break;
            case "formulaEditApply":
//                String hql2 = "select count(id)  from ApprovalProcess where approveStatus=:approveStatus and fromUser = :fromUser and businessType=:businessType";
//                HashMap<String, Object> param2 = new HashMap<>();
//                param2.put("approveStatus", "1");
//                param2.put("businessType", 30);
//                param2.put("fromUser", user.getUserID());
//                Long num2 = (Long) approvalProcessDao.getByHQLWithNamedParams(hql2,param2);
                number = 0;
                break;
        }
        return number;
    }
}
