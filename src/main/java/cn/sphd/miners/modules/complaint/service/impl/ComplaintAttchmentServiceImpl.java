package cn.sphd.miners.modules.complaint.service.impl;

import cn.sphd.miners.modules.complaint.dao.ComplaintAttachmentDao;
import cn.sphd.miners.modules.complaint.entity.ComplaintAttachment;
import cn.sphd.miners.modules.complaint.entity.ComplaintSupplement;
import cn.sphd.miners.modules.complaint.service.ComplaintAttchmentService;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.dao.UserRoleDao;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by 朱思旭 on 2019/7/18.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ComplaintAttchmentServiceImpl implements ComplaintAttchmentService {

    @Autowired
    ComplaintAttachmentDao attachmentDao;
    @Autowired
    UserDao userDao;
    @Autowired
    UserRoleDao userRoleDao;
    @Autowired
    UploadService uploadService;

    @Override
    public void addComplaintAttachment(String listAttachment, Integer baseId, Integer detailId, Integer supplementId, User user, Date time, ComplaintSupplement complaintSupplement,String module) {
        if (listAttachment != null) {
            List<ComplaintAttachment> attList = JSON.parseArray(listAttachment, ComplaintAttachment.class);
            complaintSupplement.setComplaintAttachmentHashSet(new HashSet<>(attList));
            for(ComplaintAttachment ca : attList){
                ca.setComplaint(baseId);
                if (!detailId.equals(0)) {
                    ca.setComplaintDetail(detailId);
                }
                ca.setSupplementAttachment(complaintSupplement);
                ca.setCreateName(user.getUserName());
                ca.setCreator(user.getUserID());
                ca.setCreateTime(time);
                attachmentDao.save(ca);
                SupplmentUsing callback = new SupplmentUsing(ca.getId());
                uploadService.addFileUsing(callback,ca.getPath(),ca.getTitle(),user,module);
            }
        }
    }

    @Override
    public List<UserDto> getCoreSettingUserListByComplaint(Integer oid) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(u.userID, case when u.userName is not null and length(trim(u.userName))>0 then u.userName else u.realName end,u.mobile) from User u where u.oid = :oid  and u.isDuty in('1','9') and u.roleCode in('staff','super') and u.userID NOT in (select ur.userID from UserRole ur where ur.role.code=:codef or ur.role.code =:codeh or ur.role.code =:codec)";
        Map<String,Object> params = new HashMap<>();
        params.put("oid",oid);
        params.put("codef","filing");
        params.put("codeh", "handle");
        params.put("codec", "core");
        List<UserDto> userDtoList=userDao.getListByHQLWithNamedParams(hql,params);
        return userDtoList;
    }


}
