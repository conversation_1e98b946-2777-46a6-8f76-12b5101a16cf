package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.FinanceReimburseBill;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillItem;

import java.util.List;

/**
 * Created by Administrator on 2017/12/5.
 */
public interface FinanceReimburseBillService {

    void saveFinanceReimburseBill(FinanceReimburseBill financeReimburseBill);

    FinanceReimburseBill getFinanceReimburseBillById(Integer id);

    void updateFinanceReimburseBill(FinanceReimburseBill financeReimburseBill);

    void saveFinanceReimburseBillItem(FinanceReimburseBillItem financeReimburseBillItem);

    List<FinanceReimburseBill> getFinanceReimburseBillByCertificationState(Integer oid,String certificationState);

    List<FinanceReimburseBill> getFinanceReimburseBillByPid(Integer pid);

}
