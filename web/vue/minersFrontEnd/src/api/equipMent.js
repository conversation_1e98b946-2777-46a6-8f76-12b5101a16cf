
import {request} from "@/utils/request";
// 装备器具模块
/*
*   creator: sy 2024-05-21  点击新增装备名称弹窗中的“确定”按钮
 */
export function addnamer(data){
    data = data || {};
    return request({
        url:'/equipment/add',
        data: data
    })
}

/*
*   creator: sy 2024-06-03  点击‘直接查看全部’获取数据
 */
export function getlooker(data){
    data = data || {};
    return request({
        url:"/equipment/model/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-05-31  点击‘查看’按钮获取‘系统赋予的编码’相关数据/点击’装备器具明显‘下面的按钮获取’系统赋予的编码‘相关数据
 */
export function havelookern(data){
    data  = data || {};
    return request({
        url:"/equipment/model/list",
        method: "post",
        data: data
    })
}

/*
* creator: sy 2024-04-19    获取装备器具数据
* */
export function initCractern(data) {
    data = data || {}
    return request({
        url:"/equipment/list",
        method: 'post',
        data: data,
    })
}

/*
*   creator: sy 2024-05-29  点击单行的数量数字跳转并获取相应型号数据
 */
export function quantity(data){
    data = data || {};
    return request({
        url:"/equipment/model/groupList",
        method: 'get',
        data:data
    })
}

/*
*   creator: sy 2024-05-28  点击修改装备器具名称弹窗中的“确定”按钮
 */
export function upmodif(data){
    data = data || {};
    return request({
        url:'/equipment/edit',
        data: data
    })
}

/*
*   creator: sy 2024-05-28  点击‘名称修改记录’按钮在弹窗中展示相应内容
 */
export function upname(data){
    data = data || {};
    return request({
        url:"/equipment/history/details",
        data: data
    })
}

/*
*   creator: sy 2024-06-07  获取类别管理表格数据
 */
export function getcatmsg(data){
    data = data || {};
    return request({
        url:"/equipment/category/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-07  获取被停用的类别数据
 */
export function stopnusern(data){
    data = data || {};
    return request({
        url:"/equipment/category/list",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2024-06-18  点击确定按钮进行类别新增
 */
export function addcamadn(data){
    data = data || {};
    return request({
        url:"/equipment/category/add",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2024-06-19  获取一级直属的子类别
 */
export function getchildcat(data){
    data = data || {};
    return request({
        url:"/equipment/category/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-20  获取修改记录
 */
export function catrecordn(data){
    data = data || {};
    return request({
        url:"/equipment/category/history/list",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-20  修改类别信息
 */
export function upcatmsgsure(data){
    data = data || {};
    return request({
        url:"/equipment/category/edit",
        method:"post",
        data: data
    })
}

/*
*   creator: sy 2024-06-24  停用类别数据
 */
export function stoppened(data){
    data = data || {};
    return request({
        url:"/equipment/category/edit",
        method:"post",
        data: data
    })
}

/*
*   creator: sy 2024-06-26  删除类别数据
 */
export function detcatet(data){
    data = data || {};
    return request({
        url:"/equipment/category/remove",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-06-26  恢复选中的数据
 */
export function restorecho(data){
    data = data || {};
    return request({
        url:"/equipment/category/edit",
        method: "post",
        data: data
    })
}

//------------装备清单---------------
/*
*   creator: 李玉婷 2024-10-25 08:02  获取装备列表信息
 */
export function getEqList(data){
    data = data || {};
    return request({
        url:'../equipment/model/pageList',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-10-25 13:20  获取类别列表
 */
export function getEqDetail(data){
    return request({
        url:'../equipment/model/list',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-10-28 09:20  获取供应商列表
 */
export function getSupplierList(data){
    return request({
        url: '../supplier/getSupplierList.do',
        data: data
    })
}

/*
*   creator: 李玉婷 2024-11-01 10:05  装备器具修改
 */
export function editEquipment(data, url){
    return request({
        url: url,
        data: data
    })
}

/*
*   creator: 李玉婷 2024-11-01 10:05  装备器具修改
 */
export function addSupplierDo(data){
    return request({
        url: '../supplier/addSupplier.do',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-11-01 10:05  装备器具修改
 */
export function getHistories(data){
    return request({
        url: '../equipment/model/history/histories',
        'method':'get',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-11-01 13:12  装备器具修改
 */
export function saveBatchCatOk(data){
    return request({
        url: '../equipment/model/batchClassification?' + data,
        'method':'get',
        data: {}
    })
}
/*
*   creator: 李玉婷 2024-11-02 09:30  装备器具修改
 */
export function uniformSure(data){
    return request({
        url: '../equipment/model/batchClassification?ids=' + data,
        'method':'get',
        data: {}
    })
}
/*
*   creator: 李玉婷 2024-11-05 10:12  补录提交
 */
export function addEquipmentSure(data){
    let list = JSON.stringify(data)
    return request({
        'url':'../equipment/model/batchModelAdd',//新接口
        "data": {'tEquModelsJson': list},
    })
}
/*
*   creator: 李玉婷 2024-11-05 10:12  补录提交
 */
export function getStorageList(){
    return request({
        url: '../model/import/list',
        data: {}
    })
}
/*
*   creator: 李玉婷 2024-11-05 10:12  补录提交
 */
export function importEqOk(data){
    return request({
        url: '../model/import/preImport',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-11-05 16:03  删除提交
 */
export function importEqRemove(data){
    return request({
        url: '../model/import/remove',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-11-05 16:03  导入列表-修改提交
 */
export function importEqEditOk(data){
    return request({
        url: '../model/import/edit',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-11-06 09:20  检测装备器具编号重复
 */
export function importEqCheck(){
    return request({
        url: '../model/import/check',
        'method': 'get',
    })
}
/*
*   creator: 李玉婷 2024-11-05 16:03  检测装备器具编号重复
 */
export function importCompleteOk(){
    return request({
        url: '../model/import/import',
        data: {}
    })
}

//------------装备清单END---------------
//------------装备-供应商start---------------
export function searchSupplier(){
    return request({
        url: '../supplier/searchSupplier.do',
        data: {}
    })
}
/*
*   creator: 李玉婷 2024-11-08 13:40  已暂停的供应商列表
 */
export function getSuspendedList(data){
    return request({
        url: '../supplier/getSuspendSupplierList.do',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-11-09 08:11  供应商查看
 */
export function getSupplierData(data){
    return request({
        url: '../supplier/getSrmSupplierOne.do',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-11-10 08:03  1-正常 2-终止 3-到期合同
 */
export function getContract(data){
    return request({
        url: '../supplier/listContractBase.do',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-11 10:06  暂停采购/恢复采购的操作记录
 */
export function getSuspendList(data){
    return request({
        url: '../supplier/getStartAndStopList.do',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-11 10:06  修改记录
 */
export function getRecordBaseList(data, url){
    return request({
        url: url,
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-11 14:21  修改基本信息弹窗中点击确定
 */
export function updateSupplierBase(data){
    return request({
        //url: '../supplier/updateSupplierBaseJson.do',
        url: '../supplier/updateSupplierBase.do',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-26 10:11  暂停采购/恢复
 */
export function stopSupplierRes(data, url){
    return request({
        url: url,
        data: data
    })
}

/*
*   creator: 李玉婷 2024-12-26 13:02  暂停采购/恢复
 */
export function deleteSupplierOne(data){
    return request({
        url: '../supplier/deleteSrmSupplier.do',
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-26 15:36  点击“供应商常规信息的修改”弹窗中的开票信息后的“修改”按钮
 */
export function updateInvoiceData(data){
    return request({
        url: "../saleSupplier/editSupplierInvoice",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-30 09:25  基本信息修改记录-查看
 */
export function getRecordDetails(data, url){
    return request({
        url: url,
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 08:11  管理-邮寄信息新增、修改
 */
export function manageUpdateAddress(data, url){
    return request({
        //"../supplier/addSupplierAddress.do","../supplier/updateSupplierAddress",
        url: url,
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 08:11  管理-邮寄信息停用、启用
 */
export function manageAddressAble(data){
    return request({
        url: "../supplier/startOrStopAddress.do",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 11:02  供应商常规信息修改-邮寄信息已被停用的数据按钮
 */
export function getSuspendAddressList(data){
    return request({
        url: "../supplier/getSuspendAddress.do",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 13:06  供应商常规信息修改-修改联系人
 */
export function updateContactsSure(data){
    return request({
        url: "../supplier/updateContactsSocialAndCard.do",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 13:06  供应商常规信息修改-修改联系人
 */
export function addContactsSure(data){
    return request({
        url: "../supplier/addContact.do",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 13:06  供应商常规信息修改-修改联系人
 */
export function getDeleteContactsListSupplier(data){
    return request({
        url: "../supplier/getDeleteContactsList.do",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 13:06  供应商常规信息修改-删除联系人
 */
export function deleteSupplierContact(data){
    return request({
        url: "../supplier/deleteCustomerContact.do",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 14:23 点击“供应商常规信息的修改”弹窗中的开票信息后的“修改”按钮
 */
export function getInvoiceInfo(data){
    return request({
        url: "../saleSupplier/editSupplierInvoice",
        data: data
    })
}
/*
*   creator: 李玉婷 2024-12-31 16:35 点击“供应商常规信息的修改”弹窗中的开票信息后的“修改”按钮
 */
export function contractBaseScan(data){
    return request({
        url: '/supplier/poContractBaseMes.do',
        data: data
    })
}

/*
*   creator: 李玉婷 2024-01-07 11:11
 */
export function getContactsSocialData(data){
    return request({
        url: '/supplier/getContactsSocial.do',
        data: data
    })
}


//------------装备-供应商END---------------




























