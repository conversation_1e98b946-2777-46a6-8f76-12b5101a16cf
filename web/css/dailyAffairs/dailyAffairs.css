.affair{
    margin-right: 20px;
    float: left;
    padding: 20px;
    margin-top: 10px;
}
.icon{
    display: inline-block;
    font-size: 36px;
    margin-top: 18px;
    max-width: 100%;
    width: 1.0048828125em;
    height: 1em;
    vertical-align: middle;
    fill: currentColor;
    overflow: hidden;
    -webkit-background-size: 36px 36px;
    background-size: 36px auto;
    background-repeat: no-repeat;
}
.affair li .icon-name{
    display: block;
    width: 100%;
    height: 20px;
    line-height: 20px;
    color: #666;
    font-size: 12px;
    text-align: center;
    margin-top: 15px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.affair li.disabled {
    cursor: default;
    box-shadow: none;
}
.affair li.disabled .icon{
    color: #ccc;
    opacity: 0.3;
}
.affair li.disabled .icon-name{
    color: #aaa;
}


.icon_overtime{
    background-image: url("images/overTime.svg");
}
.icon_overtimeSupplementary{
    background-image: url("images/overtimeSupplementary.svg");
}
.icon_overtimeAssign{
    background-image: url("images/overtimeAssign.svg");
}
.icon_leave{
    background-image: url("images/leave.svg");
}
.icon_leaveSupplementary{
    background-image: url("images/leaveSupplementary.svg");
}
.icon_application{
    background-image: url("images/application.svg");
}
.icon_shopping{
    background-image: url("images/shopping.svg");
}
.icon_outgoing{
    background-image: url("images/outgoing.svg");
}
.icon_car{
    background-image: url("images/car.svg");
}
.icon_trip{
    background-image: url("images/trip.svg");
}
.icon_collar{
    background-image: url("images/collar.svg");
}

/*#detail{ width:600px; }*/
/*.ty-left{ width:50%;   }*/
/*.infoCon{ padding:30px 15px 20px ; }*/
.bonceCon .item input.ty-input, .bonceCon .item select.ty-input {
    border:1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    height:36px;
    color: #3f3f3f;
}
.bonceCon .item textarea.ty-textarea {
    border:1px solid #dee8f0;
    background-color: #fff;
    color: #3f3f3f;
    border-radius: 2px;
}

.item{
    padding: 4px 0;
}
.item .item_title{
    line-height:24px;
}

/* ------------------ 报销 ------------------ */
.bill_confirm{
    width: 800px;
    margin: auto;
}
.radio_btn{
    padding: 5px 8px;
    color: #5d9cec;
    font-weight: bold;
}
.isMonthTip{
    padding: 3px 16px;
}
.formItem{
    margin-top: 8px;
    overflow: hidden;
    clear: both;
}
.formTitle,.formCon{
    float: left;
}
.formTitle{
    min-width: 200px;
    text-align: right;
    padding:0 10px;
}
.formCon{
}
.smallInput{
    width: 50px;
}
.cp_img_box{
    display: inline-block;
    width: 100px;
    position: relative;
    vertical-align: top;
}
.cp_img_box .fileType{
    padding-top: 20px;
}
.cp_img_box .fileType a{
    display: none;
    background-color: rgba(93, 156, 236,.9);
    color: #fff;
    height: 18px;
    line-height:18px;
    width:36px;
    margin: 0 auto 5px;
    font-size: 12px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
}
.cp_img_box:hover .fileType a{
    display: block;
}
.cp_img_name{
    font-size: 12px;
    color: #666;
    text-align: center;
    word-break:break-all;
    max-height: 70px;
}
.uploadify-button {
    margin-bottom: 0;
    border: none;
    background-color: #e7e7e7;
    line-height: 24px;
    border-radius: 3px;
    padding: 3px 6px;
    font-size: 12px;
    font-weight: 400;
    color: #535353;
    cursor: pointer;
    text-decoration: none;
    white-space: nowrap;
}
.fileType {
    width: 60px;
    height: 80px;
    margin-left: 20px;
    margin-bottom: 2px;
    background-position: center;
    background-color: #eee;
    background-size:60px 80px;
}
.uploadify-progress {
    display: block;
    width: 25px;
    height:5px;
    background-color: #ccc;
    border-radius: 20px;
    border: 0px groove #666;
    vertical-align: middle;
    margin-top: 15px;
    margin: 4px 0;
    border-radius: 4px;
}
.uploadify-progress-bar {
    width: 0;
    height: 100%;
    border-radius:4px;
    background-color: #0099FF;
}
.progressnum,.uploadDeleteBtn,.up_percent,.uploadbtn{
    display: none;
}
.cp_imgUpload,.cp_imgShow{
    float: left;
}
.cp_imgUpload{
    height: 102px;
    width: 120px;
}
.up_filename{
    font-size: 12px;
    color: #666;
    word-break: break-all;
    max-height: 70px;
    overflow: hidden;
}
.handleBtn{
    width: 270px;
    margin: 20px auto;
}
.formItem_auto .formTitle{
    width: 450px;
}
.repeatCon{
    width: 700px;
}
.billNoOther{
    margin:4px ;
}
.bonceCon input:not([type="radio"]):disabled{
    background-color: #cacbcc;
}
.bonceCon{
    max-height: 600px;
    overflow-y: auto;
}
.isBillAmountTip,.oneRowInvoice{
    display: none;
}
.textMax{
    color: #999;
    font-size: 12px;
}
.overtimeApply textarea {
    width: 100%;
}

.ty-nextTip{ line-height:35px; display: none; margin-left:150px;   }
.ty-nextTip .fa{ color: #48cfad; }
.wid{ display: inline-block; width: 50px; }
#goodEntry table { width: 1430px; }
#goodEntry tbody td input,#goodEntry tbody td select {  border:none; background:#fff;   }
#goodEntry tbody tr:hover td input,#goodEntry tbody tr:hover td select { background:#f0f8ff!important;   }
#goodEntry tbody td input { margin:-16px; width:100px;  }
#goodEntry tbody td input.big { width:150px;  }
#goodEntry tbody td input.right { padding-right:20px; text-align: right;  }
#goodEntry tbody td select{  margin:-16px; width:195px;    }
.ty-clear{  clear: both; }
.ctrlCon{ position: relative; height: 0; }
.ctrlConBtn{ width:140px; position: absolute;top:-27px; font-size:20px!important; padding:0 !important; right:-150px; color:#48cfad;  }
.ctrlConBtn>span.img{ display: none;  }
.ctrlConBtn>span{ margin-left: 20px; width: 20px!important; font-size: 20px!important; padding: 0!important; float:left;  }
.ctrlCon .uploadify-button{ display: none; }

.upItem{ display:inline-block; width:25px; position: relative; overflow: hidden;  }
.upItem>span.up_name{ height:18px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;  }
.upItem>span{ width: 50px; display: inline-block; }
.upItem>.up_delBtn{ position: absolute; right:2px; color: #e5303e;  }
.upItem>.up_delBtn:hover{ color: #9f212c;  }
.upItem .up_percent{ display: none; }
.upItem>.up_name{ overflow: hidden; text-overflow:ellipsis; line-height: 15px;
    white-space:nowrap; font-size:12px;  }
.uploadify-progress { height:4px!important; width:20px!important; background-color: #ccc; border-radius:4px; border: 0 groove #666; vertical-align: middle;}
.uploadify-progress-bar { width: 0; height: 100%; border-radius:4px; background-color: #0099FF;}
.upItem>span.progressnum, .upItem>span.up_fileType{ display: none; }
.fileArr{text-align: left;}
.upItem>span.up_fileType .ty-fileType{ width:50px; height:67px; background-size:25px 25px; position:absolute; top:2px; left:2px;   }
.uploadify-button { margin:0 10px; border: none; background-color: #e7e7e7; line-height: 24px; border-radius: 3px;
    padding: 3px 10px; font-size: 12px; font-weight: 400; color: #0b94ea; cursor: pointer;
    text-decoration: none; white-space: nowrap;
}
div.uploadify-queue{ display:inline-block;  }
.fileScanMark{ display: none; position:absolute; width:100%; height:100%; background: rgba(100,100,100,0.6); left:0; top:0;      }
.upItem .fileScanMark>a.fileScan{ margin-top:15px; }
.upItem .fileScanMark>a{
    display:inline-block; background:#0b94ea; color:#fff; border-radius:4px;  cursor: default; padding:2px 5px; margin:5px 0 ;
}
.upItem .fileScanMark>a:hover{ background: #41b3fd;    }
.upItem>img{ display: inline-block; width:25px; height:25px; }
.upItem>.up_name{ display:none;  }
.upItem>img:hover{ border:1px double #eaeaea;  }
.IMGinfo>.picName{ font-size:14px; color:#666; margin-top:-10px; overflow: hidden;
    text-overflow: ellipsis; white-space: nowrap; width:150px;    }
.IMGinfo{ display:none;
    position:fixed; z-index: 999999; border-radius:4px; width:200px; height:100px;
    padding:0 0 10px 10px; border: 1px solid #ccc; background: #fff;text-align: left;
}
.IMGinfo:before{ content: ""; border-left:2px solid #ccc; border-top:1px solid #ccc; padding:7px;background: #fff;
    transform:rotate(7deg); -ms-transform:rotate(45deg);  -moz-transform:rotate(45deg); -webkit-transform:rotate(45deg);
    display: inline-block; position: relative; top:-15px; left:150px;
}

.summaryTip{ line-height:50px;   }
.addMemoBtn{ width:25px; color:#0b94ea;line-height:25px;   }
.addMemoBtn:hover{ color: #44b0ea; box-shadow:0 0 3px #ccc;   }
#memo3{ width:350px; height:80px;  }
.memoAcronym{ font-weight:normal!important;   }
select[disabled]{ background:#ddd;  }
.litFont{ font-size:13px!important; width:100%;  }

.fileUid{ display: none!important;  }



.input_choose{
    display: flex;
    position: relative;
    border: 1px solid #dcdfe6;
    min-height: 32px;
    width: 350px;
    background-color: #fff;
    padding: 4px 8px;
    color: #666;
}
.input_choose input.search{
    border: none;
    width: 20px;
}
.search{
    min-width: 80px;
}
.selected_item{
    display: inline-block;
    height: 26px;
    line-height: 26px;
    padding: 0 12px;
    background-color: #eef1f2;
    border-radius: 13px;
    font-size: 14px;
}
.input_choose_list {
    width: 350px;
    display: flex;
    flex-direction: column;
    background: #fff;
    display: none;
    max-height: 160px;
    overflow: auto;
    box-shadow: 0 1px 2px #ddd;
}
.input_choose_list.dir{
    display: flex;
    max-height: 300px
}
.input_choose_item{
    display: flex;
    padding: 8px 10px;
    cursor: default;
}
.input_choose_item .icon_avatar{
    width: 36px;
    height: 36px;
    margin-right: 8px;
    border-radius: 18px;
    background-color: #dcdfe6;
}
.input_choose_item_info{
    display: flex;
    flex-direction:column;
}
.input_choose_item_post{
    color: #aaa;
    font-size: 12px;
}
.input_choose_item.selected{
    opacity:0.4;
    filter:alpha(opacity=40);
}
.input_choose_item:not(.selected):hover{
    background-color: #e6f0f7;
}

.delRole, .delFile{
    transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    display: inline-block;
    font-size: 18px;
    width: 18px;
    height: 18px;
    text-align: center;
    vertical-align: middle;
    line-height: 18px;
    cursor: pointer;
}
