<!--日流水的具体详情--><template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>
    <div>
      <div class="repItem" v-for="(item,index) in list" :key="index" @click="goOrdRepaymentDetail(item)">
        <span class="fa fa-angle-right detal"></span>
        <div class="cre">{{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }} 录入</div>
        <table>
          <tbody>
          <tr>
            <td>收款日期 <br> {{ new Date(item.repaymentTime).format('yyyy-MM-dd') }}</td>
            <td>收款方式 <br> {{ chargePayMethod(item.repaymentMethod) }} </td>
            <td>收款金额 <br> {{ item.repaymentAmount  }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "loanDetail",
  data() {
    return {
      list: [],
    }
  },
  mixins: [finance],
  mounted() {
    let loanRepayList = JSON.parse(localStorage.getItem('loanRepayList'))
    this.list = loanRepayList
  },
  methods: {
    goOrdRepaymentDetail(item){
      localStorage.setItem('loanRepayDetail', JSON.stringify(item))
      this.$router.push("loanRepayDetail")
    },

  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";


</style>