package cn.sphd.miners.modules.thirdPlatform.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table( name ="t_tp_app_version" )
public class TpAppVersion extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "app_id", length=34, unique = true)
    private String appId;//`app_id` VARCHAR(34) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT 'appId',
    @Column(name = "app_name",length=30)
    private String appName;//`app_name` VARCHAR(30) DEFAULT NULL COMMENT '第三方应用名称appName/nickName,tt:4-30chars;wx:4-30chars'
    @Column(length=50)
    private String module;//`module` VARCHAR(50) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '模块',
    @Column
    private Byte env;//`env` TINYINT DEFAULT NULL COMMENT '1开发/2生产/3共用',
    @Column(name = "app_version",length=50)
    private String appVersion;//`app_version` VARCHAR(50) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '应用版本',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Byte getEnv() {
        return env;
    }

    public void setEnv(Byte env) {
        this.env = env;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }
}
