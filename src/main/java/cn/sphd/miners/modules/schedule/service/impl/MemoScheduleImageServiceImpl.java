package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.modules.schedule.dao.MemoScheduleImageDao;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleImage;
import cn.sphd.miners.modules.schedule.service.MemoScheduleImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/13.
 */
@Service("memoScheduleImageService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoScheduleImageServiceImpl implements MemoScheduleImageService {

    @Autowired
    MemoScheduleImageDao memoScheduleImageDao;


    @Override
    public void deleteMemoScheduleImage(MemoScheduleImage memoScheduleImage) {
        memoScheduleImageDao.delete(memoScheduleImage);
    }

    @Override
    public void deleteAllMemoScheduleImage(Integer scheduleId) {
        String hql=" delete MemoScheduleImage where scheduleId=:scheduleId";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        memoScheduleImageDao.queryHQLWithNamedParams(hql,params);

    }

    @Override
    public List<MemoScheduleImage> getMemoScheduleImageListBySid(Integer scheduleId) {
        String hql=" from MemoScheduleImage where scheduleId=:scheduleId";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        List<MemoScheduleImage> memoScheduleImageList= memoScheduleImageDao.getListByHQLWithNamedParams(hql,params);
        return memoScheduleImageList;
    }

    @Override
    public MemoScheduleImage getMemoScheduleImageById(Integer id) {
        return memoScheduleImageDao.get(id);
    }
}
