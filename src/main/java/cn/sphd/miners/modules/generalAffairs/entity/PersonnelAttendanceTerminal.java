package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2023/9/25
 * 人事_打卡终端表
 */
@Entity
@Table(name = "t_personnel_attendance_terminal")
public class PersonnelAttendanceTerminal implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="type"   , nullable=true , unique=false)
    private Byte type;//类型:1-设备,2-webchat

    @Column(name="terminal_uuid"  , length=36 , nullable=true , unique=false)
    private String terminalUuid; //唯一标识/webchat union_id

    @Column(name="brand"  , length=50 , nullable=true , unique=false)
    private String brand;//品牌/昵称

    @Column(name="model"  , length=50 , nullable=true , unique=false)
    private String model;//型号

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime; //创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getTerminalUuid() {
        return terminalUuid;
    }

    public void setTerminalUuid(String terminalUuid) {
        this.terminalUuid = terminalUuid;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
