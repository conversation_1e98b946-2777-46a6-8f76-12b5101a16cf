<template>
  <div id="discussDetail">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel handle" v-if="params.mark === 'manage'">
        <div class="handle_title">本讨论组 <span class="color-blue">{{discussDetail.forumPost.expriationTime}}</span></div>
        <div class="handle_content">
          <div>于{{discussDetail.forumPost.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}由{{discussDetail.forumPost.createName}}创建</div>
          <div>于{{discussDetail.forumPost.enabledTime |formatDay('YYYY-MM-DD HH:mm:ss')}}被停用</div>
          <div>于{{discussDetail.forumPost.validTime |formatDay('YYYY-MM-DD HH:mm:ss')}}被{{discussDetail.forumPost.compereName}}删除</div>
        </div>
        <div class="handle_title">如认为本讨论组还有用处，消失前，您还可将其还原。</div>
        <div class="handle_button">
          <input type="submit" class="ui-btn ui-btn_info" value="查看" @click="seeDiscuss()"/>
          <input type="submit" class="ui-btn ui-btn_success" value="还原" @click="restore()"/>
        </div>
      </div>
      <div class="panel" v-if="params.mark === 'approveLast'">
        <div class="item">
          <div class="item_title">审批记录</div>
        </div>
        <div class="item">
          <div class="rightSide">
            <div v-if="discussDetail.listAp.length > 0">
              <span class="process_desName">申请人</span>
              <span class="process_name">{{discussDetail.listAp[0].userName}}</span>
              <span>{{discussDetail.listAp[0].createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
            </div>
            <div v-for="(item, index) in discussDetail.listAp" :key="index">
              <div v-if="item.approveStatus === '2'">
                <div v-if="item.toMid === null">
                  <span class="process_desName">审批人</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-else>
                  <span class="process_desName">文&nbsp;&nbsp;管</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
              </div>
              <div v-if="item.approveStatus === '3'">
                <div v-if="item.toMid === null">
                  <span class="process_desName">审批人</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div class="color-red" v-else>
                  <span class="process_desName">文&nbsp;&nbsp;管</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div class="color-red">
                  <span>驳回理由</span>
                  <span>{{item.approveMemo}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="panel" v-if="params.mark === 'approveLast'">
        <div class="item">是否允许该讨论组生成？</div>
        <div class="handle_button">
          <input type="submit" class="ui-btn ui-btn_error" value="驳回" @click="approve(3)"/>
          <input type="submit" class="ui-btn ui-btn_info" value="批准" @click="approve(2)"/>
        </div>
      </div>
      <div class="panel">
        <div class="panel-title">
          {{discussDetail.forumPost.title}}
        </div>
        <div>
          <div>
            <table class="table">
              <tbody>
              <tr>
                <td class="th" style="width: 60px">描述</td>
                <td>{{discussDetail.forumPost.content}}</td>
              </tr>
              <tr>
                <td class="th">附件</td>
                <td><span class="file" v-for="(item, i) in discussDetail.forumPostAttachmentHashSer" v-bind:key="item.id" @click="openDialog(item)">{{i+1}}</span></td>
              </tr>
              <tr>
                <td class="th">参与人员</td>
                <td><span class="forumUser" v-for="it in discussDetail.forumPostUserHashSer" v-bind:key="it.id">{{it.userName}}</span></td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="panel" v-if="params.mark !== 'approveLast'">
        <div class="item">
          <div class="item_title">审批记录</div>
        </div>
        <div class="item">
          <div class="rightSide">
            <div v-if="discussDetail.listAp.length > 0">
              <span class="process_desName">申请人</span>
              <span class="process_name">{{discussDetail.listAp[0].userName}}</span>
              <span>{{discussDetail.listAp[0].createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
            </div>
            <div v-for="(item, index) in discussDetail.listAp" :key="index">
              <div v-if="item.approveStatus === '2'">
                <div v-if="item.toMid === null">
                  <span class="process_desName">审批人</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-else>
                  <span class="process_desName">文&nbsp;&nbsp;管</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
              </div>
              <div v-if="item.approveStatus === '3'">
                <div v-if="item.toMid === null">
                  <span class="process_desName">审批人</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div class="color-red" v-else>
                  <span class="process_desName">文&nbsp;&nbsp;管</span>
                  <span class="process_name">{{item.toUserName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div class="color-red">
                  <span>驳回理由</span>
                  <span>{{item.approveMemo}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="panel" v-if="params.mark === 'approveMiddle1'">
        <el-form :rules="rules" :model="selectRule" class="handleFile rightSide">
          <el-form-item prop="radio">
            <el-radio-group v-model="selectRule.radio">
              <el-radio :label="1" :disabled="usNot">本人无异议，选择下一个审批人</el-radio>
              <el-form-item v-show="selectRule.radio == '1'" class="approverNext" size="mini">
                <el-select border v-model="selectRule.nextApprover" placeholder="选择下一位审批人" @change="nextAp" :disabled="usNot">
                  <el-option
                    v-for="item in approverSelect"
                    :key="item.userID"
                    :label="item.userName"
                    :value="item.userID">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-radio :label="2">本人无异议，请文管继续审批</el-radio>
              <br/>
              <el-radio :label="3">驳回该讨论申请</el-radio>
              <el-input :disabled="usNot" v-show="selectRule.radio == '3'" type="textarea" v-model="opposeForm.approveMemo"></el-input>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div class="handle_button">
          <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="toHandle()"/>
        </div>
      </div>
    </div>
    <el-dialog
      title="当前的有效文件"
      :visible.sync="dialog_handle"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="handleType">
            <el-radio :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button">
          <a class="ui-btn" @click="dialog_handle = false">取消</a>
          <span class="ui-btn ui-btn_info" ref="operate" :path="selectDiscuss.path" :download="selectDiscuss.title" @click="handleFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<style scoped="">
  .panel-title{
    background: inherit;
    border-bottom: 1px solid #f2f2f7;
  }
  .handle_title{
    line-height:1.5;
    margin: 8px 0;
  }
  .handle_content{
    margin-left: 20px;
    font-size: 13px;
    padding: 8px;
    border-bottom: 1px solid #f2f2f7;
  }
  .applyInfo{
    text-align: right;
    margin-top: 8px;
    padding:8px 16px;
  }
  .forumUser{
    margin-left: 4px;
  }
  .file{
    font-size: 12px;
    color: #4797f1;
    cursor: pointer;
    margin-left: 8px;
    text-decoration: underline;
  }
</style>

<script>
import { gohistory } from '@/js/common'
export default {
  name: 'applyReimburse',
  data () {
    return {
      title: '',
      loading: true,
      listenersUid: [],
      discussDetail: {
        forumPost: [],
        forumPostUserHashSer: [],
        forumPostAttachmentHashSer: [],
        listAp: []
      },
      discussId: 0,
      dialog_handle: false,
      handleType: 1,
      selectDiscuss: {
        path: '',
        title: ''
      },
      selectRule: {
        radio: '',
        nextApprover: ''
      },
      rules: {
        radio: [
          { required: true, message: '请选择下一下一个审批人', trigger: 'change' }
        ]
      },
      approverSelect: [],
      opposeForm: { // 驳回表单
        approveMemo: ''
      },
      selectedAp: {},
      lastApprover: {}
    }
  },
  created () {
    // 赋值地址栏传值（id: 借阅id， mark: 来源标记）
    this.params = this.$route.params
    console.log('borrow' + JSON.stringify(this.params))
    // 获取借阅文件详情
    this.getForumPostMessage()
    if (this.params.mark === 'approveMiddle1') {
      // 来源为中间审批人时获取其他审批人列表
      this.getNextApprover()
    }
    switch (this.params.mark) {
      case 'apply':
      case 'approveMiddle1':
      case 'approveMiddle2':
      case 'query1': // 后面数字为eventType 1 发起申请 2 中间审批 3文管
      case 'query2':
        this.title = '讨论发起申请'
        break
      case 'approveLast':
      case 'query3':
        this.title = '讨论发起审批'
        break
      case 'manage': // 来源为 管理的讨论组
        this.title = '管理的讨论组'
        break
    }

    this.listenersUid = [
      this.sphdSocket.subscribe('applyForumPost', function (data) {
        // 所有提交结果在此显示
        console.log('applyForumPost Socket received OK:' + data)
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getForumPostMessage () {
      let that = this
      let discussId = this.$route.params.id
      this.axios.post('../../../forum/getForumPostMessage.do', {
        id: discussId
      })
        .then(function (response) {
          that.loading = false
          console.log(response)
          that.discussDetail = response.data.data
          that.lastApprover = that.discussDetail.listAp[that.discussDetail.listAp.length - 1].id
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    getNextApprover () {
      let that = this
      let params = {
        postId: this.params.id,
        userID: this.sphdSocket.user.userID,
        type: 4
      }
      this.$http.post('../../../forum/getAllParticipants.do', params, {
        emulateJSON: true
      }).then((response) => {
        let data = response.body
        that.approverSelect = data.listPostUser
        that.loading = false
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    nextAp (vId) {
      this.selectedAp = this.approverSelect.find((item) => {
        return item.userID === vId // 筛选出匹配数据
      })
    },
    toHandle () {
      // 中间审批人审批
      let paramsData = {
        postId: this.params.id,
        processId: this.lastApprover,
        type: 0, // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
        approveStatus: 0 // 2-批准 3-驳回
      }
      if (this.selectRule.radio === 1) {
        paramsData.type = 1
        paramsData.approveStatus = 2
        paramsData.toUser = this.selectedAp.userID
        paramsData.toUserName = this.selectedAp.userName
      } else if (this.selectRule.radio === 2) {
        paramsData.type = 2
        paramsData.approveStatus = 2
      } else if (this.selectRule.radio === 3) {
        paramsData.type = 1
        paramsData.approveStatus = 3
        paramsData.approveMemo = this.opposeForm.approveMemo
      }
      this.loading = true
      this.handleDiscussFile(paramsData)
    },
    handleDiscussFile: function (paramsData) {
      let that = this
      this.$http.post('../../../forum/approveForum.do', paramsData, {
        emulateJSON: true
      }).then((response) => {
        that.loading = false
        let data = response.body.data
        let status = data.status
        if (status === '1') {
          that.$kiko_message('操作成功')
          gohistory(that)
        } else {
          that.$kiko_message('讨论已被审批不用重复操作')
        }
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    approve: function (type) {
      let that = this
      let param = {
        postId: this.params.id,
        processId: this.lastApprover,
        type: 3, // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
        approveStatus: type // 2-批准 3-驳回
      }
      console.log(param)
      if (type === 2) {
        this.$confirm('您确定批准该讨论申请吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.loading = true
          that.handleDiscussFile(param)
        })
      } else {
        this.$prompt('请输入驳回理由', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '最多可录入50字'
        }).then(({ value }) => {
          that.loading = true
          param.approveMemo = value || ''
          if (param.approveMemo.length < 50) {
            that.handleDiscussFile(param)
          } else {
            that.loading = false
            that.$kiko_message('最多可录入50字')
          }
        })
      }
    },
    openDialog (item) {
      this.dialog_handle = true
      if (item.type === '3') {
        item.path = item.resEntity.path
        item.title = item.resEntity.name + '.' + item.resEntity.version
      }
      this.selectDiscuss = item
      console.log(item)
    },
    handleFile (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.handleType)
    },
    seeDiscuss: function () {
      window.parent.location.href = window.parent.$.webRoot + '/forum/discussionOtherIndex.do?id='+ this.params.id
    },
    restore: function () {
      let that = this
      this.$confirm('“还原”后，本讨论组的本次“删除”将不起作用。确定“还原”本讨论组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        that.loading = true
        this.axios.post('../../../forum/restoreForumPost.do', {
          postId: that.params.id
        })
          .then(function (response) {
            that.loading = false
            console.log(response)
            that.$kiko_message('操作成功')
            gohistory(that)
          })
          .catch(function (error) {
            console.log(error)
          })
      })
    }
  }
}
</script>
