import {request} from "@/utils/request";
import JSONBig from 'json-bigint'

/*
 * creator: 2025-6-19 kl
 * 收到的承兑汇票/转账支票主页数据
 * 传递参数：
	Integer type：1-转帐支票，2-承兑汇票
*/

/*
*
返回值：
	available：尚在手中的{
		num:张数
		amount：金额
	}
	yearTime：今年{
		num:张数
		amount：金额
	}
	lastYearTime：去年{
		num:张数
		amount：金额
	}
	beforeYearTime：前年{
		num:张数
		amount：金额
	}
*
* */
export function getReturnNum(data) {
    return request({
        url: '/return/getReturnNum.do',
        data: data
    })
}


/*
 * creator: 2025-6-19 kl
 * 获取承兑汇票/转账支票列表【附带某状态的票据查询也用此接口】
 * 传递参数：
	Integer type：1-转帐支票，2-承兑汇票
	String state: 1-有效,2-存入银行,3-作废，4-已支出
	String beginTime:时间（格式为yyyy-MM-dd，某年中的某一天即可）
*/

/*
*
返回值：
beginDate：开始时间
	endDate：结束时间

	numAmount:总金额和总张数{
		num：总张数
		amount：总金额
	}
	financeReturns：{
		id：票据id
		returnNo：支票号码/汇票号码
		type：1-转帐支票，2-承兑汇票
		amount：金额
		billAmount：票面金额
		operatorName：接收经手人姓名
		partnerName：合作方经手人姓名
		receiveDate：收到支票/汇票日期（付款日期）
		payer：付款单位
		originalCorp：原始出具支票/汇票单位
		bankName：出具支票/汇票银行(出具支票银行)
		expireDate：支票到期日期/汇票到期日
		state：1-有效,2-存入银行,3-作废，4-已支出
		accout：存入银行帐号
		depositDate：存入银行日期
		depositorName：存入经手人姓名
		receiveAccountDate：到帐日期
		memo：备注
		createName：创建人（财务经手人、录入者）
		createDate：创建时间
		updateName：出纳人
		updateDate：出纳时间
		saveBankName：存入支票/汇票的银行名称
		accountId：存入存入银行的id
	}
*
* */
export function getReturnList(data) {
    return request({
        url: '/return/getReturnList.do',
        data: data
    })
}

/*
 * creator: 2025-6-23 kl
 * 获取某票据详细接口
 * 传递参数：
	Integer returnId：票据id
*/

/*
*
返回值：
    source：来源（在此版本中此字段用不上）
	financeReturn：票据信息 {
		id：票据id
		returnNo：支票号码/汇票号码
		type：1-转帐支票，2-承兑汇票
		amount：金额面金额
		operatorName：接收经手人姓名
		partnerName：合作方经手人姓名
		receiveDate：收到支票/汇票日期（付款日期）
		payer：付款单位
		originalCorp：原始出具支票/汇票单位
		bankName：出具支票/汇票银行(出具支票银行)
		expireDate：支票到期日期/汇票到期日
		state：1-有效,2-存入银行,3-作废，4-已支出
		accout：存入银行帐号
		depositDate：存入银行日期
		depositorName：存入经手人姓名
		receiveAccountDate：到帐日期
		memo：备注
		createName：创建人（财务经手人、录入者）
		createDate：创建时间
		updateName：出纳人
		updateDate：出纳时间
		saveBankName：存入支票/汇票的银行名称
		accountId：存入存入银行的id
		oppositeCorp：收款单位
	}
*
* */
export function getReturnDetail(data) {
    return request({
        url: '/return/getReturnDetail.do',
        data: data
    })
}

/*
 * creator: 2025-6-24 kl
 * 承兑汇票/转账支票的统计【按收款方统计】
 * 传递参数：
	Integer type：1-转帐支票，2-承兑汇票
	String beginTime:时间（格式为yyyy-MM-dd，某年中的某一天即可）
*/

/*
*
返回值：
	beginDate：开始时间
	endDate：结束时间
	numAll：总张数
	amountAll：总金额

	listMap：{
		id票据id（0或其他值）
		num：张数
		amount：总金额
		oppositeCorp：收款方名称
	}
*/
export function getReturnStatistics(data) {
    return request({
        url: '/return/getReturnStatistics.do',
        data: data
    })
}

/*
 * creator: 2025-6-24 kl
 * 统计查询
 * 传递参数：
	Integer type：1-转帐支票，2-承兑汇票
	String beginTime:时间（格式为yyyy-MM-dd，某年中的某一天即可）
*/

/*
*
返回值：
	numAll 总张数
    amountAll 总金额
	available：尚在手中的{
		num:张数
		amount：金额
	}
	saveBanks：存入银行的{
		num:张数
		amount：金额
	}
	payReturns：又付出去的{
		num:张数
		amount：金额
	}
*/
export function getReturnStatisticState(data) {
    return request({
        url: '/return/getReturnStatisticState.do',
        data: data
    })
}

/*
 * creator: 2025-6-26 kl
 * 某收款方的票据列表
 * 传递参数：
	Integer type：1-转帐支票，2-承兑汇票
	String oppositeCorp：收款方名称
	String beginTime:时间（格式为yyyy-MM-dd，某年中的某一天即可）
	Integer returnId：票据id(上个页面返回的值)
*/

/*
返回值：
	beginDate：开始时间
	endDate：结束时间
	financeReturns：{
		id：票据id
		returnNo：支票号码/汇票号码
		type：1-转帐支票，2-承兑汇票
		amount：金额
		billAmount：票面金额
		operatorName：接收经手人姓名
		partnerName：合作方经手人姓名
		receiveDate：收到支票/汇票日期（付款日期）
		payer：付款单位
		originalCorp：原始出具支票/汇票单位
		bankName：出具支票/汇票银行(出具支票银行)
		expireDate：支票到期日期/汇票到期日
		state：1-有效,2-存入银行,3-作废，4-已支出
		accout：存入银行帐号
		depositDate：存入银行日期
		depositorName：存入经手人姓名
		receiveAccountDate：到帐日期
		memo：备注
		createName：创建人（财务经手人、录入者）
		createDate：创建时间
		updateName：出纳人
		updateDate：出纳时间
		saveBankName：存入支票/汇票的银行名称
		accountId：存入存入银行的id
	}
*/
export function getReturnStatisticsList(data) {
    return request({
        url: '/return/getReturnStatisticsList.do',
        data: data
    })
}

/*
 * creator: 2025-7-16 kl
 * 申领来的银行支票列表
 * 传递参数：
	oid：机构id
*/

/*
返回值：
    cashTime:空白的现金支票
	transferTime:空白的转账支票
	yearTime:今年申领次数
	lastYearTime:去年申领次数
	beforeYearTime:前年申领次数
*/
export function getFinanceChequeTime(data) {
    return request({
        url: '/cheque/getFinanceChequeTime.do',
        data: data
    })
}

/*
 * creator: 2025-7-16 kl
 * 获取申领来的空白发票列表
 * 传递参数：
		无
*/

/*
返回值：
	specialNum：增值税专用票数量
	ordinaryNum：增值税普通票数量
	otherNum：其它普通票数量
	yearTime：今年申领
	lastYearTime：去年申领
	beforeYearTime：前年申领
*/
export function getAllInvoiceNum(data) {
    return request({
        url: '/invoice/getAllInvoiceNum.do',
        data: data
    })
}

/*
 * creator: 2025-7-16 kl
 * 发票的使用记录(按年)
 * 传递参数：
		无
*/

/*
返回值：
	newYearUsed：今年的{
		totalAmount：总金额
		num：总张数
	}
	lastYearUsed：去年的{
		totalAmount：总金额
		num：总张数
	}
	beforeLastYearUsed：前年的{
		totalAmount：总金额
		num：总张数
	}
*/
export function getInvoiceUsed(data) {
    return request({
        url: '/invoice/getInvoiceUsed.do',
        data: data
    })
}

/*
 * creator: 2025-7-17 kl
 * 使用记录-获取各种发票统计(有月列表的)【月列表中查看所有种类的发票】
 * 传递参数：
	String beginDate:某年/某月的某一天(年月日)
	Integer typeMonth:1-有月列表 2-没有月列表
	Integer dateType:1-按年统计 2-按月统计
*/

/*
返回值：
totalNum：总数
	totalAmount：总金额
	beginTime：开始时间
	endTime：结束时间
	specialInvoice：增值税专用票{
		totalAmount：总金额
		num：总张数
	}
	ordinaryInvoice：增值税普通票{
		totalAmount：总金额
		num：总张数
	}
	otherInvoice：其他普通票{
		totalAmount：总金额
		num：总张数
	}
	monthList：月列表{
		monthYear：年月
		allInvoice：数据{
			totalAmount：总金额
			num：总张数
		}
	}
*/
export function getInvoiceStatistics(data) {
    return request({
        url: '/invoice/getInvoiceStatistics.do',
        data: data
    })
}

/*
 * creator: 2025-7-21 kl
 * 统计某类型发票某年(月列表)
 * 传递参数：
	String type：1-增值税专用票,2-增值税普通票,3-其它普通票
	String beginDate：开始时间
	String endDate：结束时间
*/

/*
返回值：
【其他的总张数和总金额取上一个接口的返回值】：

	monthList：月列表{
		monthYear：年月
		allInvoice：数据{
			totalAmount：总金额
			num：总张数
		}
	}
*/
export function getInvoiceType(data) {
    return request({
        url: '/invoice/getInvoiceType.do',
        data: data
    })
}

/*
 * creator: 2025-7-23 kl
 * 某类型某月的发票列表
 * 传递参数：
	String type：1-增值税专用票,2-增值税普通票,3-其它普通票
	String beginDate：开始时间
	String endDate：结束时间
*/

/*
返回值：
	beginTime：开始时间
	endTime：结束时间
	monthStatistics：数据统计{
		totalAmount：总金额
		num：总张数
	}
	financeInvoiceDetails：信息列表{
		id:id
		invoiceNo:号码
		amount：金额
		applicantName：申请人姓名
		applicationTime：申请时间
		operateDate：开票日期
		receiveCorp：客户名称
	}
*/
export function getInvoiceListMonth(data) {
    return request({
        url: '/invoice/getInvoiceListMonth.do',
        data: data
    })
}

/*
 * creator: 2025-7-24 kl
 * 获取发票详情
 * 传递参数：
       invoiceDetailId：发票详情id
*/

/*
返回值：

	invoiceDetail：销售使用具体的详情{
        name：名称
        item_quantity：數量
        invoice_amount：稅額
        item_amount：金額
        unit_price_c：单价
	}
	financeInvoiceDetail：信息列表{
		id:id
		source:来源:1-财务录入,2-销售
		invoiceNo:号码
		amount：金额
		applicantName：申请人姓名
		applicationTime：申请时间
		operateDate：开票日期
		receiveCorp：客户名称
	}
*/
export function getInvoiceDetailAll(data) {
    return request({
        url: '/invoice/getInvoiceDetailAll.do',
        data: data
    })
}