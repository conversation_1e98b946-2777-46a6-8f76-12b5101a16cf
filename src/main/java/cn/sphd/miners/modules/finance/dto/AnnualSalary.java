package cn.sphd.miners.modules.finance.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName AnnualSalaryByOrg
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/7 8:33
 * @Version 1.0
 */
public class AnnualSalary {
    private String year;
    private List<MonthSalary> monthSalaryList;
    private Double sumSalary;
    private Double averageSalary;

    public Double getSumSalary() {
        return sumSalary;
    }

    public void setSumSalary(Double sumSalary) {
        this.sumSalary = sumSalary;
    }

    public Double getAverageSalary() {
        return averageSalary;
    }

    public void setAverageSalary(Double averageSalary) {
        this.averageSalary = averageSalary;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public List<MonthSalary> getMonthSalaryList() {
        return monthSalaryList;
    }

    public void setMonthSalaryList(List<MonthSalary> monthSalaryList) {
        this.monthSalaryList = monthSalaryList;
    }
}
