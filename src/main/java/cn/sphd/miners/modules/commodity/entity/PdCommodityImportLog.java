package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-08-29 
 */

@Entity ( name ="PdCommodityImportLog" )
@Table ( name ="t_pd_commodity_import_log" )
public class PdCommodityImportLog  implements Serializable {

	private static final long serialVersionUID =  604545461450379010L;

	/**
	 * ID
	 */
	@Id
	@Column(name="id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 导入总数
	 */
   	@Column(name = "commodity_count" )
	private Integer productCount;

	/**
	 * 导入成功数
	 */
   	@Column(name = "commodity_success" )
	private Integer productSuccess;

	/**
	 * 上传文件路径
	 */
   	@Column(name = "upload_file" )
	private String uploadFile;

	/**
	 * 商品类型:1-通用,2-专属
	 */
   	@Column(name = "commodity_catetory" )
	private String produdctCatetory;
	/**
	 * 导入选项:1-全部,2-仅开增值税专用发票的,3-仅开普通发票的,4-仅不开发票的,5-仅不开增值税专用发票的,9-其它',
	 */
	@Column(name = "import_option" )
	private String importOption;
	/**
	 * 导入税率
	 */
	@Column(name="tax_rate")
	private BigDecimal taxRate;
	/**
	 * 是否含税价:TRUE-含税
	 */
	@Column(name = "tax_inclusive" )
	private Integer taxInclusive;

	/**
	 * 是否采购过,true-是,false-否
	 */
   	@Column(name = "is_purchased" )
	private Integer isPurchased;

	/**
	 * 状态:1-上传成功,2-导入临时表,3-完成
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	/**
	 * 操作:1-增,2-删,3-修改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getProductCount() {
		return this.productCount;
	}

	public void setProductCount(Integer productCount) {
		this.productCount = productCount;
	}

	public Integer getProductSuccess() {
		return this.productSuccess;
	}

	public void setProductSuccess(Integer productSuccess) {
		this.productSuccess = productSuccess;
	}

	public String getUploadFile() {
		return this.uploadFile;
	}

	public void setUploadFile(String uploadFile) {
		this.uploadFile = uploadFile;
	}

	public String getProdudctCatetory() {
		return this.produdctCatetory;
	}

	public void setProdudctCatetory(String produdctCatetory) {
		this.produdctCatetory = produdctCatetory;
	}

	public String getImportOption() {
		return importOption;
	}

	public void setImportOption(String importOption) {
		this.importOption = importOption;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public Integer getTaxInclusive() {
		return taxInclusive;
	}

	public void setTaxInclusive(Integer taxInclusive) {
		this.taxInclusive = taxInclusive;
	}

	public Integer getIsPurchased() {
		return this.isPurchased;
	}

	public void setIsPurchased(Integer isPurchased) {
		this.isPurchased = isPurchased;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
