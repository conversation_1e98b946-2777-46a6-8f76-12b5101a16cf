package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquFixedAssetsHistory;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface TEquFixedAssetsHistoryMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquFixedAssetsHistory selectTEquFixedAssetsHistoryById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquFixedAssetsHistory 
     * @return 集合
     */
    public List<TEquFixedAssetsHistory> selectTEquFixedAssetsHistoryList(TEquFixedAssetsHistory tEquFixedAssetsHistory);

    /**
     * 新增
     * 
     * @param tEquFixedAssetsHistory 
     * @return 结果
     */
    public int insertTEquFixedAssetsHistory(TEquFixedAssetsHistory tEquFixedAssetsHistory);

    /**
     * 修改
     * 
     * @param tEquFixedAssetsHistory 
     * @return 结果
     */
    public int updateTEquFixedAssetsHistory(TEquFixedAssetsHistory tEquFixedAssetsHistory);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquFixedAssetsHistoryById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquFixedAssetsHistoryByIds(String[] ids);
}
