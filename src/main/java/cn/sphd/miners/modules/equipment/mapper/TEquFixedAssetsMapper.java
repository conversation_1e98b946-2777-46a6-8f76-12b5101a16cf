package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquFixedAssets;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface TEquFixedAssetsMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquFixedAssets selectTEquFixedAssetsById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquFixedAssets 
     * @return 集合
     */
    public List<TEquFixedAssets> selectTEquFixedAssetsList(TEquFixedAssets tEquFixedAssets);

    /**
     * 新增
     * 
     * @param tEquFixedAssets 
     * @return 结果
     */
    public int insertTEquFixedAssets(TEquFixedAssets tEquFixedAssets);

    /**
     * 修改
     * 
     * @param tEquFixedAssets 
     * @return 结果
     */
    public int updateTEquFixedAssets(TEquFixedAssets tEquFixedAssets);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquFixedAssetsById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquFixedAssetsByIds(String[] ids);
}
