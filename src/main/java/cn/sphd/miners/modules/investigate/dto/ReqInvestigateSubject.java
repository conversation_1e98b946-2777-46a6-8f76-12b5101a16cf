package cn.sphd.miners.modules.investigate.dto;


import java.util.List;

//title 标题 header页眉
//questionList 问题列表
//tagList 类别描述
public class ReqInvestigateSubject {
    private Integer id;
    private Integer tagStatus;
    private String title;
    private String header;
    private String preface;
    private String questionListJson;
    private String subjectTagListJson;
    private List<RespInvestigateQuestion> respInvestigateQuestionList;
    private List<RespInvestigateSubjectTag> respInvestigateSubjectTagList;

    public Integer getTagStatus() {
        return tagStatus;
    }

    public void setTagStatus(Integer tagStatus) {
        this.tagStatus = tagStatus;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPreface() {
        return preface;
    }

    public void setPreface(String preface) {
        this.preface = preface;
    }

    public List<RespInvestigateSubjectTag> getRespInvestigateSubjectTagList() {
        return respInvestigateSubjectTagList;
    }

    public void setRespInvestigateSubjectTagList(List<RespInvestigateSubjectTag> respInvestigateSubjectTagList) {
        this.respInvestigateSubjectTagList = respInvestigateSubjectTagList;
    }

    public List<RespInvestigateQuestion> getRespInvestigateQuestionList() {
        return respInvestigateQuestionList;
    }

    public void setRespInvestigateQuestionList(List<RespInvestigateQuestion> respInvestigateQuestionList) {
        this.respInvestigateQuestionList = respInvestigateQuestionList;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public String getQuestionListJson() {
        return questionListJson;
    }

    public void setQuestionListJson(String questionListJson) {
        this.questionListJson = questionListJson;
    }

    public String getSubjectTagListJson() {
        return subjectTagListJson;
    }

    public void setSubjectTagListJson(String subjectTagListJson) {
        this.subjectTagListJson = subjectTagListJson;
    }
}
