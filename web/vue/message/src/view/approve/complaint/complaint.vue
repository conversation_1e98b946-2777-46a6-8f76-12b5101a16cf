<template>
  <div id="complaint" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="投诉处理" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="tabs">
      <div class="tabs-bar">
        <router-link :to="{name: 'complaintApproval1'}" tag="div" v-if="he === 1" :style="{width:(he===1?'33%':'50%')}">
          <TY_TabBar label="立案待审批" :msgCount="list1.length" :bool="$store.getters.getComplaintNav === 1">
          </TY_TabBar>
        </router-link>
        <router-link :to="{name: 'complaintApproval2'}" tag="div" :style="{width:(he===1?'33%':'50%')}">
          <TY_TabBar label="待结案" :msgCount="list2.length" :bool="$store.getters.getComplaintNav === 2">
          </TY_TabBar>
        </router-link>
        <router-link :to="{name: 'complaintApproval3'}" tag="div" :style="{width:(he===1?'33%':'50%')}">
          <TY_TabBar label="结案待审批" :msgCount="he===1 ? list3.length :'' " :bool="$store.getters.getComplaintNav === 3">
          </TY_TabBar>
        </router-link>
      </div>
      <div class="tabs-content">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #complaint {
    .tabs-bar>div{ float: left; display: inline-block;  }
    .tabs-tab {  width:100%;  }
    .cusName{ margin-bottom:1rem;   }
    .info{margin-left:2rem; }
  }
</style>
<script>
export default {
  name: 'complaintHandle',
  data () {
    return {
      list: this.$store.getters.getComplaintList,
      loading: true,
      he: 2,
      list1: [], // 立案待审批
      list2: [], // 待结案
      list3: [], // 结案待审批
      listeners: []
    }
  },
  created () {
    let _this = this
    // 获取当前人的角色是不是核心人物
    _this.axios.post('../../../coreSetting/getCoreUser.do').then(data => {
      let res = data.data
      let coreUserID = res['coreUser']['userID']
      let curUserID = _this.sphdSocket.user.userID
      if (coreUserID === curUserID) {
        _this.he = 1
      } else {
        _this.$router.push({
          path: `/complaintHandle/complaintApproval2`
        })
      }
      _this.$store.dispatch('setNewComplaintHe', _this.he)
      // 获取列表
      _this.$store.dispatch('setNewComplaintList', { 'userID': curUserID, 'role': _this.he }).then((result) => {
        _this.loading = false
      })
    }).catch((err) => {
      console.log('获取数据错误了！', err)
    })
  },
  destroyed: function () {
  },
  methods: {
    search: function () {
      this.$router.push({
        name: `approveComplaintSearch`
      })
    }
  },
  watch: {
    '$store.state.complaintList': {
      deep: true,
      immediate: true,
      handler (complaintList) {
        console.log('complaintList', complaintList)
        if (complaintList) {
          this.list = complaintList
          this.list1 = complaintList['listApproveComplaint'] || []
          this.list2 = complaintList['listPendingComplaintEnd'] || []
          this.list3 = complaintList['listApproveComplaintEnd'] || []
        }
      }
    }
  }
}
</script>
