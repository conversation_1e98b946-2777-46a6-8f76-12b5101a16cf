<!--子路由 按时间查看-->
<template>
  <ul class="val_list">
    <li v-for="item in accountList" :key="item.id" class="ta-list">
      <div @click="seeDetails(item)">
        <span>{{ item.dat }}</span>
        <span>
          <span :class="item.style"> {{ item.le }} {{ item.money }}</span>
          <span class="btnM" >
            <i class="fa fa-angle-right"></i>
          </span>
        </span>
      </div>
    </li>
  </ul>
</template>

<script>

import finance from '@/mixins/finance.js'
export default {
  // name: "ViewByTime", 两个地方用这个页面了
  data() {
    return {
      accountList: []
    }
  },
  mixins: [finance],
  mounted() {
    let name = this.$route.name
    if(name === 'ViewByTimeB'){ // 月流水
      this.getMonthData()
    }else if(name === 'ViewByTime'){ // 日流水
      this.getTimeData(1)
    }

  },
  methods: {
    getMonthData(){
      let org = this.auth.getOrg()
      let params = { oid:org.id  }
      this.financeApi.getDetailByMonth(params).then(res1=>{
        let res = res1.data.data
        let accountDetailList = res.accountDetails
        this.accountList = []
        accountDetailList.forEach(item=>{
          let itemA = {}
          if(item.credit || item.credit === 0){ // 收入
            itemA = {
              id: item.id , dat: (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')) , le: "收入",
              money: this.formatCurrency(item.credit) ,
              style: "ty-color-green"}
          }else if(item.debit){ // 支出
            itemA =  {
              id:  item.id,
              dat: (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'))  ,
              le: "支出",
              money: this.formatCurrency(item.debit),
              style: "ty-color-red"
            }
          }
          itemA.info = item
          this.accountList.push(itemA)
        })

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    getTimeData(state){
      let org = this.auth.getOrg()
      let params = { oid:org.id , state:state }
      this.financeApi.getAllAccountDataByTimeApp(params).then(res1=>{
        let res = res1.data.data
        let accountDetailList = res.accountDetailList
            this.accountList = []
            accountDetailList.forEach(item=>{
              let itemA = {}
              if(item.credit || item.credit === 0){ // 收入
                itemA = {
                  id: item.id , dat: (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')) , le: "收入",
                  money: this.formatCurrency(item.credit) ,
                  style: "ty-color-green"  }
              }else if(item.debit){ // 支出
                itemA =  {
                  id:  item.id,
                  dat: (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'))  ,
                  le: "支出",
                  money: this.formatCurrency(item.debit),
                  style: "ty-color-red"
                }
              }
              itemA.info = item
              this.accountList.push(itemA)
            })

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },

  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

</style>