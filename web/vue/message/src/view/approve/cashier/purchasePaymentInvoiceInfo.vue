<template>
  <div id="purchasePaymentInvoiceInfo">
    <TY_NavTop :title="ttl" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tip2">{{tip}}</div>
      <div class="info" v-if="invoiceInfo.type == 2">
        <table>
          <tr><td colspan="2" class="bg">&nbsp;</td></tr>
          <tr><td width="50%">支票号</td><td width="50%">{{ invoiceInfo.returnNo }}</td></tr>
          <tr><td>所开具发票或收据的金额</td><td>{{ invoiceInfo.billAmount.toFixed(2) }}</td></tr>
          <tr><td>到期日</td><td>{{ invoiceInfo.expireDate | formatDay('YYYY-MM-DD') }}</td></tr>
          <tr><td>出具的银行</td><td>{{ invoiceInfo.bankName }}</td></tr>
          <tr><td>付款单位</td><td>{{ invoiceInfo.payer }}</td></tr>
          <tr><td>出具的单位</td><td>{{ invoiceInfo.originalCorp }}</td></tr>
          <tr><td>备注</td><td>{{ invoiceInfo.memo }}</td></tr>
          <tr><td colspan="2" class="bg">&nbsp;</td></tr>
          <tr><td>收到日期</td><td>{{ invoiceInfo.receiveDate | formatDay('YYYY-MM-DD') }}</td></tr>
          <tr><td colspan="2" class="sm">录入者 {{ invoiceInfo.createName }}
            {{ invoiceInfo.createDate | formatDay('YYYY-MM-DD') }}</td></tr>
        </table>
      </div>
      <div class="info" v-if="invoiceInfo.type == 3">
        <table>
          <tr><td colspan="2" class="bg">&nbsp;</td></tr>
          <tr><td width="50%">汇票号</td><td width="50%">{{ invoiceInfo.returnNo }}</td></tr>
          <tr><td>所开具发票或收据的金额</td><td>{{ invoiceInfo.billAmount.toFixed(2) }}</td></tr>
          <tr><td>到期日</td><td>{{ invoiceInfo.expireDate | formatDay('YYYY-MM-DD') }}</td></tr>
          <tr><td>出具的银行</td><td>{{ invoiceInfo.bankName }}</td></tr>
          <tr><td>付款单位</td><td>{{ invoiceInfo.payer }}</td></tr>
          <tr><td>最初出具的单位</td><td>{{ invoiceInfo.originalCorp }}</td></tr>
          <tr><td>备注</td><td>{{ invoiceInfo.memo }}</td></tr>
          <tr><td colspan="2" class="bg">&nbsp;</td></tr>
          <tr><td>收到日期</td><td>{{ invoiceInfo.receiveDate | formatDay('YYYY-MM-DD') }}</td></tr>
          <tr><td colspan="2" class="sm">录入者 {{ invoiceInfo.createName }}
            {{ invoiceInfo.createDate | formatDay('YYYY-MM-DD HH:mm:ss') }}</td></tr>
        </table>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #purchasePaymentInvoiceInfo{
    .container{ background: #fff }
    .tip2{ background: #fff;  padding: 5px 15px; line-height:40px; }
    .info{ background: #fff;}
    .bg{ background: #eee; line-height: 15px; }
    table{ width: 100%; }
    td{ line-height: 30px; padding:0 15px; }
    .sm{ font-size: 0.8em; text-align: right;  }
  }

</style>

<script>
export default {
  name: 'purchasePaymentInvoiceInfo',
  data () {
    return {
      invoiceInfo: {},
      tip: '',
      ttl: '',
      loading: false
    }
  },
  created () {
    let that = this
    that.invoiceInfo = JSON.parse(localStorage.invoiceTicketInfo)
    console.log('getInvoiceTicketInfo', that.invoiceInfo)
    let type = that.invoiceInfo.type
    if (Number(type) === 2) {
      that.tip = '转账支票详情'
    } else if (Number(type) === 3) {
      that.tip = '承兑汇票详情'
    }
    if (Number(that.invoiceInfo.pund) === 3) {
      that.ttl = '多收来的款'
    } else {
      that.ttl = '采购部门的付款'
    }
  }
}
</script>
