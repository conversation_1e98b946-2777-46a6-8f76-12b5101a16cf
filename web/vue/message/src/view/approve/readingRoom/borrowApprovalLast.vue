<template>
  <div id="approveFile" isBackHome="false" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="文件借阅审批" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="tip">下列文件借阅申请有待您审批。</div>
    <div class="ui-cells">
      <a class="ui-cell" v-on:click="jump(item.id, 3)" v-for="(item, index) in toApprovalList" :key="index">
        <div class="fileLists">
          <div class="fileType file_xls"></div>
          <div class="fileInfo">
            <div class="fileName" v-text="item.name" :title="item.name"></div>
            <div class="fileDetail">{{item.createName}} {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          </div>
          <div class="fileFormat">
            <span class="fileVersion">G{{item.changeNum}}</span>
            <span class="fileNo">编号：{{item.fileSn}}</span>
          </div>
        </div>
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'fileApproval',
  data () {
    return {
      loading: true,
      isBackHome: true,
      listenersUid: [],
      toApprovalList: []
    }
  },
  created: function () {
    let that = this
    this.$http.post('../../../read/getLastApproveReadFile.do', { userID: this.sphdSocket.user.userID, oid: this.sphdSocket.user.oid }, {
      emulateJSON: true
    }).then((response) => {
      let list = response.body.data
      that.toApprovalList = list.lastApproveReadFile
      that.loading = false
    }).catch((err) => {
      console.log('获取数据错误了！', err)
    })
    that.setSubscribe()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    setSubscribe: function () {
      let _this = this
      _this.loading = false
      this.listenersUid = [
        this.sphdSocket.subscribe('FinalApproveReadFile', function (data) {
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            newDate = reqData.rd
            _this.toApprovalList.push(newDate)
          } else if (update < 0) { // 减少一条,文件被驳回时用
            let deleteId = reqData.rd.id
            _this.toApprovalList.forEach(function (item, index) {
              if (deleteId === item.id) {
                _this.toApprovalList.splice(index, 1)
              }
            })
          }
        }, null, 'custom', this.sphdSocket.user.oid + 'AuthApprovalgeneral')
      ]
    },
    jump: function (id, mark) {
      this.$router.push({
        path: `/borrowFileDetail/${id}/${mark}`
      })
    },
    screen: function () {
      this.$router.push({
        path: '/borrowQuery/lastApprove'
      })
    }
  }
}
</script>
