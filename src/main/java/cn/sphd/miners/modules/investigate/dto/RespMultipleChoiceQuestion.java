package cn.sphd.miners.modules.investigate.dto;

import java.util.List;

public class RespMultipleChoiceQuestion {
    private Integer objectSum;
    private Integer sum;
    private RespInvestigateQuestion respInvestigateQuestion;
    private List<RespAnswerCount> respAnswerCountList;

    public Integer getObjectSum() {
        return objectSum;
    }

    public void setObjectSum(Integer objectSum) {
        this.objectSum = objectSum;
    }

    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    public RespInvestigateQuestion getRespInvestigateQuestion() {
        return respInvestigateQuestion;
    }

    public void setRespInvestigateQuestion(RespInvestigateQuestion respInvestigateQuestion) {
        this.respInvestigateQuestion = respInvestigateQuestion;
    }

    public List<RespAnswerCount> getRespAnswerCountList() {
        return respAnswerCountList;
    }

    public void setRespAnswerCountList(List<RespAnswerCount> respAnswerCountList) {
        this.respAnswerCountList = respAnswerCountList;
    }
}
