<template>
  <div class="contact">
    <el-dialog
        v-model="dialog_visible_qrCode"
        title="二维码"
        width="550"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 450px; margin: 0 auto; text-align: center">
        <qrcode-vue
            :value="qrCode.url"
            size="300"
            @click="zoomQrCode()"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_qrCode = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <div class="ty-container" id="home">
      <div class="page">
        <div class="tblContainer recruit">
          <div class="ty-alert">本页面下方的二维码用于应聘者面试前扫描，填写并提交后，可大幅降低人事管理者的工作量！</div>
          <div class="declare_avatar">
            <div>
              <h4>使用流程</h4>
              <p>1. 应聘者扫描右侧二维码，在所见页面填写内容并提交后，应聘资料即进入系统</p>
              <p>2. 人事的手机端会收到提示</p>
              <p>3. 人事查看应聘者的资料后，可为其选择一位或多位面试官</p>
              <p>4. 所选各位面试官的手机端会收到提示</p>
              <p>5. 面试官面试时可在手机端填写面试意见</p>
              <p>6. 人事可查看面试意见，并可随时终止面试或向高层建议入职</p>
            </div>
            <div class="qrCode">
              <div class="qrCode_small">
                <qrcode-vue
                    :value="qrCode.url"
                    size="130"
                    @click="zoomQrCode()"
                />
              </div>
              <div>供求职者扫描的二维码</div>
              <div>可点击放大后截图保存使用</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ty-alert{
  width: 100%;
  padding: 8px 0;
  box-sizing: border-box;
  border-radius: 2px;
  position: relative;
  font-size: 13px;
  color: #909399;
  overflow: hidden;
  opacity: 1;
  display: flex;
  align-items: center;
  transition: opacity .2s;
  margin-bottom: 4px;
}
.ty-container{
  margin-top:35px; padding:10px;
}
.page{
  padding: 8px 0;
  max-width: 1200px;
  position: relative;
  margin: 0 70px;
}
.qrCode{
  text-align: center;
  padding:16px;
  color: #999;
  background-color: #fff;
  font-size: 12px;
}
.qrCode_small{
  width: 150px;
  height: 140px;
  margin-bottom: 4px;
}
.recruit{
  width: 1000px;
}
.declare_avatar{
  padding: 32px 64px;
  background: #f5f6f7;
  display: flex;
  justify-content: space-between;
  color: #333;
  font-size: 14px;
}
.declare_avatar p{
  margin-bottom: 6px;
}
.declare_avatar h4{
  display: inline-block;
  padding-left: 8px;
  font-size: 16px;
  color: #666;
  font-weight: bold;
  border-left: 4px solid #5d9cec;
  margin-left: -12px;
  margin-bottom: 24px;
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import QRCode from 'qrcode.vue'
import * as api from '@/api/recruit'
export default {
  components: {
    'qrcode-vue': QRCode
  },
  data() {
    return {
      page: '',
      pageName: 'scanCodeInterview',
      dialog_visible_qrCode: false,
      qrCode: {
        url: '',
        size: 150
      }
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'kn', name: '面试扫码', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      this.$nextTick(() => {
        api.getQRLink()
            .then(res => {
              let data = res.data
              let url = data.data
              this.qrCode = {
                url: url
              }
            })
      })
    },
    zoomQrCode() {
      this.dialog_visible_qrCode = true
    }
  }
}


</script>