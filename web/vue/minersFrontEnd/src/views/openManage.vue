<template>
  <div class="openManageComp">
    <!--  一级 tip 提示框  -->
    <TyDialog v-if="dialog_visible_tip" dialogTitle="！提示" color="red" :dialogHide="dialogHideTip">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialog_visible_tip = false">我知道了</el-button>
      </template>
      <template #dialogBody>
        <div class="tipWord ty-center">{{tipWord}}</div>
      </template>
    </TyDialog>

    <div class="ty-container">
      <div class="ty-mainData">
        <p style="margin:20px;" id="TimeState">今天是{{todayDate}}</p>
        <p style="margin:20px;">以下状态为“未设置”的项目，其系统内相关功能尚未启用。您可向其负责人发出提示。</p>

        <div class="mainCon">
          <table class="ty-table ty-table-control " id="list">
            <thead>
            <tr>
              <td>项目</td>
              <td>状态</td>
              <td>负责人</td>
              <td>提醒方式</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item, index1) in openIndexList" :key="index1">
              <td>{{item['name']}}</td>
              <td>{{chargeState(item['state'] , 'state')}}</td>
              <td>{{(item['userName'] || "暂无")}}</td>
              <td>
                <span class="ty-color-blue" @click='sendMeg(1, item)' >发系统消息</span>
                <span class="ty-color-blue" @click='sendMeg(2, item)'>发手机短信</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>

      </div>
    </div>
  </div>
</template>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange";
import * as openApi from "@/api/openManage";
import auth from '@/sys/auth'

export default {
  data() {
    return {
      pageName: 'openManage',
      openIndexList: [],
      tipWord: '',
      todayDate: '',
      dialog_visible_tip: false
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'ia', name: '启用管理', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created() {
      this.getOpenList()
    },
    getOpenList() {
      auth.getHostTime('/favicon.ico', hosttime => { // 获取服务器当前时间
        console.log('哈哈哈:', new Date(hosttime));
        let dayStr = new Date(hosttime).format("yyyy年MM月dd日");
        let week =new Date(hosttime).getDay(), weekStr = "";
        if (week == 0) {
          weekStr = "星期日";
        } else if (week == 1) {
          weekStr = "星期一";
        } else if (week == 2) {
          weekStr = "星期二";
        } else if (week == 3) {
          weekStr = "星期三";
        } else if (week == 4) {
          weekStr = "星期四";
        } else if (week == 5) {
          weekStr = "星期五";
        } else if (week == 6) {
          weekStr = "星期六";
        }
        this.todayDate = dayStr  + " " + weekStr
      });
      openApi.getStartManages()
          .then(res => {
            this.openIndexList = res.data["data"] || []
          })
    },
    sendMeg(messageType, info) {
      let data = { "userId": auth.getUserID() , "itemId": info.id , "messageType":messageType  }
      openApi.sendSysMeg(data)
          .then(res => {
            let result = res.data["data"] || []
            this.tipWord = result.cont
            this.dialog_visible_tip = true
          })
    },
    chargeState(state , type) {
      let str = "";
      switch (type){
        case 'state':
          if(state == 1){
            str = "已设置";
          }else if(state == 0){
            str = "未设置";
          }
          break;
      }
      return str ;
    },
    dialogHideTip(){
      this.dialog_visible_tip = false
    }
  }
}
</script>

<style scoped lang="scss">
.openManageComp{
  .ty-container {
    margin-top: 10px;
    padding: 10px;
  }
  .ty-container {
    font-size: 14px;
    font-family: "Microsoft Yahei";
    color: #101010;
  }
  .tipWord{
    font-size: 14px;
  }
  #list thead>td{ background:#0b94ea!important; color:#fff;   }
  tbody>tr:nth-child(odd)>td{   }
  tbody>tr:nth-child(even)>td{ background:#eee;   }
}
</style>