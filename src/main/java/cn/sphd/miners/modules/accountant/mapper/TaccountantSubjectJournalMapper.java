package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TaccountantSubjectJournal;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TaccountantSubjectJournalMapper  extends BaseMapper<TaccountantSubjectJournal>{

    List<TaccountantSubjectJournal> getSubjectJournal(TaccountantSubjectJournal subjectJournal);
    List<TaccountantSubjectJournal> getSubjectJournallistPage(QueryData param);

    TaccountantSubjectJournal getByCurDate(int org);

}