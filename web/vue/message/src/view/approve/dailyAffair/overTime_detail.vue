<template>
  <div id="dailyAffairs">
    <TY_NavTop title="加班审批"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="bg">
        <div class="item">
          <div class="item_title">{{detail.userName}}</div>
          <div class="item_content"></div>
        </div>

        <div>
          <div class="item">
            <div class="item_title">加班所属日期</div>
            <div class="item_content">{{detail.beginTime | formatDay('YYYY-MM-DD dddd')}}</div>
          </div>
          <div class="kj-panel" v-if="(processList1.length > 1 && detail.actualType === '1') || detail.approveDuration !== null">
            <div class="item">
              <div class="item_title">批准时长</div>
              <div class="item_content">{{detail.approveDuration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">说明</div>
              <div class="item_content">{{detail.approveExplain}}</div>
            </div>
            <div v-if="td.approveStatus === '3'" v-for="(td, index) in processList1" v-bind:key="index">
              <div class="item color-red">
                加班申报被{{td.userName}}驳回！
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{td.reason}}</div>
              </div>
            </div>
            <div class="processList">
              <div class="process">
                <div v-for="(item, index) in processList1" v-bind:key="index">
                  <div v-if="item.approveStatus !== '1'">审批人 <span class="processName">{{item.userName}}</span> {{item.handleTime | formatDay}}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="kj-panel applyDetail" v-if="detail.actualType === '1'">
            <div class="item">
              <div class="item_title">申报时长</div>
              <div class="item_content">{{detail.actualDuration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">申报起止时间</div>
              <div class="item_content">{{detail.actualBeginTime | formatDay('HH:mm')}} - {{detail.actualEndTime | formatDay('HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">加班事由</div>
              <div class="item_content">{{detail.actualReason}}</div>
            </div>
            <div class="processList">
              <div class="process">
                <div>申请人 <span class="processName">{{detail.userName}}</span> {{detail.actualApplyTime | formatDay}}</div>
              </div>
            </div>
          </div>
          <div class="kj-panel noOverTime" v-if="detail.actualType === '2'">
            <div class="item">
              <div class="item_title color-red">实际未加班！</div>
              <div class="item_content color-red" v-if="detail.finalResult === '6'">规定时间内未提交实际加班的数据</div>
              <div class="item_content" v-else></div>
            </div>
            <div class="processList">
              <div class="process">
                <div>
                  申请人
                  <span class="processName" v-if="detail.finalResult === '6'">系统</span>
                  <span class="processName" v-else>{{detail.userName}}</span>
                  {{detail.updateDate | formatDay('YYYY-MM-DD HH:mm:ss')}}
                </div>
              </div>
            </div>
          </div>
          <div class="kj-panel noNeedAppy" v-if="detail.finalResult === '4'">
            <div class="item">
              <div class="item_content">
                由于考勤管理员已成功修改了{{detail.userName}}{{detail.beginTime | formatDay('YYYY-MM-DD dddd')}}的考勤，故此次加班的时长无需再申报。
              </div>
            </div>
            <div class="item">
              <div class="item_content">
                如有异议，请与考勤管理员确认。
              </div>
            </div>
          </div>
          <div class="kj-panel planDetail" v-if="detail.kind !== 2">
            <div class="item">
              <div class="item_title">计划时长</div>
              <div class="item_content">{{detail.duration}}h</div>
            </div>
            <div class="item">
              <div class="item_title">计划起止时间</div>
              <div class="item_content">{{detail.beginTime | formatDay('HH:mm')}} - {{detail.endTime | formatDay('HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">加班事由</div>
              <div class="item_content">{{detail.reason}}</div>
            </div>
            <div v-if="is.approveStatus === '3'" v-for="(is, index) in processList" v-bind:key="index">
              <div class="item">
                <div class="color-red">加班申请被{{is.userName}}驳回！</div>
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{is.reason}}</div>
              </div>
            </div>
            <div class="processList">
              <div class="process">
                <div>申请人 <span class="processName">{{detail.userName}}</span> {{detail.createDate | formatDay}}</div>
                <div v-if="it.approveStatus !== '1'" v-for="(it, index) in processList" v-bind:key="index">
                  <div>审批人 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay}}</div>
                </div>
              </div>
            </div>
          </div>
          <!--判断按钮是否显示的判断1.是否为待审批2.当前人物是否为审批者3.是否是待审批的数据-->
          <div class="handle_button" v-if="detail.approveStatus === '1' && $store.getters.getOverTimeNav === '1' && isNowApprover">
            <input type="submit" class="ui-btn ui-btn_info" value="批准" @click="approveOverTime(1)">
            <input type="submit" class="ui-btn" value="修改加班时长" @click="approveOverTime(3)" v-if="isAgree === 1 && detail.actualType === '1'">
            <input type="submit" class="ui-btn ui-btn_info" value="驳回" @click="approveOverTime(2)" v-if="isAgree === 0">
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="97%"
      :before-close="handleClose">
      <el-form :label-position="left" label-width="70px">
        <el-form-item label="批准时长">
          <el-input v-model="approveInfo.approveDuration" size="small"></el-input>
        </el-form-item>
        <el-form-item label="说明">
          <el-input v-model="approveInfo.approveExplain" size="small"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value="取消" @click="dialogVisible = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定"  @click="changeOverTime()">
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
  .bg{
    background: #fff;
    padding: 4px 16px;
  }
  .item{
    display: flex;
    padding: 4px 0;
    line-height: 18px;
    .item_title{
      width: 90px;
      flex: none;
      color: #666;
      line-height: 2rem;
      padding: 0;
    }
    .item_content{
      flex: auto;
      line-height: 2rem;
      text-align: left;
    }
  }
  .kj-panel{
    border-top:1px solid #ddd;
  }
  .process{
    font-size: 12px;
    color: #666;
    margin-top: 8px;
    margin-bottom: 8px;
    margin-left: 90px;
    .processName{
      display: inline-block;
      width: 52px;
      margin-left: 4px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
      text-align: left;
    }
  }
  .item .item_title.color-red{
    color: red;
  }
</style>
<script>
import { gohistory } from '@/js/common'
export default {
  name: 'approveOverTimeDetail',
  data () {
    return {
      dialogVisible: false,
      loading: false,
      isAgree: 0,
      isNowApprover: false,
      approveStatus: '1',
      approvalProcessId: 0,
      listenersUid: [],
      detail: {},
      processList: [],
      processList1: [],
      approveInfo: {} // 逐级审批加班申请
    }
  },
  created () {
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('notice', function (data) {
        // 所有提交结果在此显示
        console.log('notice Socket received OK:' + data)
        that.loading = false
        let status = Number(data)
        if (status === 1) {
          // that.$router.push({
          //   path: '/approvalOutTime',
          //   name: 'approvalOutTime'
          // })
          that.$kiko_message('操作成功')
          gohistory(that)
        } else if (status === 4) {
          that.$kiko_message('已审批，不能重复审批')
        } else if (status === 9) {
          that.$kiko_message('操作失败，因为该条申请已被撤销')
          gohistory(that)
          // that.$router.push({
          //   path: '/approvalOutTime',
          //   name: 'approvalOutTime'
          // })
        } else {
          that.$kiko_message('操作失败')
        }
      })
    ]
    this.getOverTimeDetail()
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getOverTimeDetail: function () {
      this.loading = true
      let that = this
      let params = {
        outTimeId: this.$route.params.outTimeId,
        session: ''
      }
      this.$http.post('../../../leaveAndOutTime/approverOverTimeDetails.do', { json: JSON.stringify(params) }, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data

        that.detail = reqData.personnelOvertime // 加班详情
        that.processList = reqData.processList // 计划加班审批流程
        that.processList1 = reqData.processList1 // 申报加班和批准加班的审批流程

        if (reqData.processList1.length > 0) {
          if (reqData.processList1[reqData.processList1.length - 1].toUser === that.sphdSocket.user.userID) {
            that.isNowApprover = true
          }
        } else {
          if (reqData.processList[reqData.processList.length - 1].toUser === that.sphdSocket.user.userID) {
            that.isNowApprover = true
          }
        }
        that.isAgree = reqData.button
        that.approveInfo = {
          session: that.sphdSocket.sessionid, // sessionId
          userId: that.sphdSocket.user.userID, // userID
          approveDuration: '', // 加班详情id
          approveExplain: '', // 加班详情id
          id: that.$route.params.outTimeId, // 加班详情id
          approvalProcessId: reqData.approvalProcessId // 审批id
        }

        that.loading = false
      }).catch(function () {
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    jump: function (path) {
      this.$router.push({
        path: '/' + path,
        name: path
      })
    },
    approveOverTime (approveStatus) {
      let that = this
      // 获取加班状态（1-计划加班 2-申报加班 3-批准加班)(isAgree === 1 显示修改加班时长 === 0 显示驳回）
      let duration = this.detail.duration
      let actualDuration = this.detail.actualDuration
      let approveDuration = this.detail.approveDuration
      let approveExplain = this.detail.approveExplain

      this.approveInfo.reason = ''
      this.approveInfo.approvalStatus = approveStatus
      this.approveInfo.approveExplain = approveExplain || ''
      this.approveInfo.approveDuration = approveDuration || actualDuration || duration

      if (approveStatus === 3) {
        this.dialogVisible = true
      } else {
        if (this.detail.actualType === '0') {
          // 计划加班审批
          this.approveInfo.type = 1
        } else {
          // 批准加班审批（多级审批）
          if (approveDuration !== null && approveDuration !== actualDuration) {
            this.approveInfo.type = 3
          } else {
            // 申报加班审批
            this.approveInfo.type = 2
          }
        }
        if (approveStatus === 2) {
          this.$prompt('驳回理由', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(({ value }) => {
            if (value === null) {
              that.approveInfo.reason = ''
            } else {
              that.approveInfo.reason = value
            }
            console.log(JSON.stringify(that.approveInfo))
            that.sphdSocket.send('progressiveApprovalOutTime', that.approveInfo)
          })
        } else {
          console.log(JSON.stringify(that.approveInfo))
          this.sphdSocket.send('progressiveApprovalOutTime', that.approveInfo)
          this.loading = true
        }
      }
    },
    changeOverTime: function () {
      this.approveInfo.approvalStatus = 1
      this.approveInfo.type = 3
      this.sphdSocket.send('progressiveApprovalOutTime', this.approveInfo)
    }
  }
}
</script>
