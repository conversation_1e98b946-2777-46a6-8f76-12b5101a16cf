<template>
  <div id="attendanceClerkChangePerson" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="个人提交的考勤修改" :isBackHome="false" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <p class="tip"> 下列考勤的修改申请有待审批。</p>
      <div class="ui-cells">
        <a class="ui-cell" v-for="(item, index) in toApproval" v-on:click="jump(item.business, 2)" :key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div class="cusName">{{item.description}}</div>
              <p class="text-right"><span>{{item.askName}}</span> <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span></p>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<style lang="less">
  #attendanceClerkChangePerson{
    .cusName{ margin-bottom:1rem; }
  }
</style>

<script>
export default {
  name: 'attendanceClerkChangePerson',
  data () {
    return {
      toApproval: [],
      loading: false,
      listeners: []
    }
  },
  created () {
    let _this = this
    _this.loading = false
    _this.getList()
    _this.listeners.push(_this.sphdSocket.subscribe('myAttendanceApproval', _this.attendanceApprovalInstanceCallBack, null, 'user'))
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    // 获取职工考勤修改的列表
    getList: function () {
      let that = this
      this.loading = true
      this.axios.post('../../../workAttendance/attendanceApprovalList.do', { 'userId': that.sphdSocket.user.userID }).then(function (response) {
        let reqData = response.data
        that.toApproval = reqData.data.approvalProcesses
        console.log(reqData)
        that.loading = false
      }).catch(function (error) {
        console.log(error)
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    jump: function (id, mark) {
      this.$router.push({
        path: `/myAttendanceChangeDetail/${id}/${mark}/3`
      })
    },
    attendanceApprovalInstanceCallBack: function (data) {
      let that = this
      console.log('职工考勤修改待审批通道:' + data)
      let reqData = JSON.parse(data)
      let operate = reqData.operate
      if (operate > 0) { // 增加一条
        console.log('增加一条')
        let newFile = reqData.approvalInstance
        that.toApproval.push(newFile)
      } else if (operate < 0) { // 减少一条
        console.log('减少一条')
        let deleteId = reqData.approvalInstance.id
        that.toApproval.forEach(function (item, index) {
          if (deleteId === item.id) {
            that.toApproval.splice(index, 1)
          }
        })
      }
    },
    search: function () {
      this.$router.push({
        path: `/attendanceChangeQuery/approve2`
      })
    }
  },
  watch: {
  }
}
</script>
