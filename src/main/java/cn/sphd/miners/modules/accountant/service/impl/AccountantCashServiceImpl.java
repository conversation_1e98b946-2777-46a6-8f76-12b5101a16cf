package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.TAccountantCash;
import cn.sphd.miners.modules.accountant.mapper.TAccountantCashMapper;
import cn.sphd.miners.modules.accountant.service.AccountantCashService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by root on 17-5-26.
 */
@Service
@Transactional(readOnly=false)
public class AccountantCashServiceImpl implements AccountantCashService {

    @Autowired
    TAccountantCashMapper cashMapper;

    @Override
    public List<TAccountantCash> getCashItems() {
        List<TAccountantCash> list = cashMapper.getCashItems();
        return list;
    }

}
