package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table( name ="t_tp_app_request" )
public class TpAppRequest implements Serializable {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    /**
     * 机构ID
     */
    @Column(name = "org", nullable=true , unique=false )
    private Integer org;

    /**
     * 平台ID
     */
    @Column(name = "platform", nullable=true , unique=false )
    private Long platform;

    /**
     * 类型:1-申请小程序,2-绑定小程序,3-发布申请
     */
    @Column(name = "type", nullable=true , unique=false )
    private Byte type;

    /**
     * 访问令牌
     */
    @Column(name = "token", length=128, nullable=true , unique=false )
    private String token;

    /**
     * 标题
     */
    @Column(name = "title", length=100, nullable=true , unique=false )
    private String title;

    /**
     * 内容
     */
    @Column(name = "content", length=255, nullable=true , unique=false )
    private String content;

    /**
     * 状态:0-草稿,1-提交申请,2-审批通过,3-审批否决
     */
    @Column(name = "approve_status", nullable=true , unique=false )
    private Byte approveStatus;

    /**
     * 返回状态
     */
    @Column(name = "response_status", nullable=true , unique=false )
    private Integer responseStatus;

    /**
     * 请求json串
     */
    @Column(name = "request_json", length=65535, nullable=true , unique=false )
    private String requestJson;

    /**
     * 响应json串
     */
    @Column(name = "response_json", length=65535, nullable=true , unique=false )
    private String responseJson;

    /**
     * 回调类
     */
    @Column(name = "callback_class", length=255, nullable=true , unique=false )
    private String callbackClass;

    /**
     * 回调对象
     */
    @Column(name = "callback_json", length=255, nullable=true , unique=false )
    private String callbackJson;

    /**
     * 回调表id
     */
    @Column(name = "callback_key", length=255, nullable=true , unique=false )
    private String callbackKey;

    /**
     * 备注
     */
    @Column(name = "memo", length=255, nullable=true , unique=false )
    private String memo;

    /**
     * 创建人id
     */
    @Column(name = "creator", nullable=true , unique=false )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name", length=100, nullable=true , unique=false )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable=true , unique=false )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator", nullable=true , unique=false )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name", length=100, nullable=true , unique=false )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time", nullable=true , unique=false )
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getPlatform() {
        return platform;
    }

    public void setPlatform(Long platform) {
        this.platform = platform;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Byte getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Byte approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(Integer responseStatus) {
        this.responseStatus = responseStatus;
    }

    public String getRequestJson() {
        return requestJson;
    }

    public void setRequestJson(String requestJson) {
        this.requestJson = requestJson;
    }

    public String getResponseJson() {
        return responseJson;
    }

    public void setResponseJson(String responseJson) {
        this.responseJson = responseJson;
    }

    public String getCallbackClass() {
        return callbackClass;
    }

    public void setCallbackClass(String callbackClass) {
        this.callbackClass = callbackClass;
    }

    public String getCallbackJson() {
        return callbackJson;
    }

    public void setCallbackJson(String callbackJson) {
        this.callbackJson = callbackJson;
    }

    public String getCallbackKey() {
        return callbackKey;
    }

    public void setCallbackKey(String callbackKey) {
        this.callbackKey = callbackKey;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
