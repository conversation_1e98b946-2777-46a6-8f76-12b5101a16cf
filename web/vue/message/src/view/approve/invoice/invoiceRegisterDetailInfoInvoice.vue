<template>
  <div id="invoiceRegisterDetailInfoInvoice">
    <TY_NavTop title="开票资料" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <a class="ui-cell"><div class="ui-cell__bd"><div class="ui-cell_con"><div>公司名称</div> <div>{{ info.name }}</div></div></div></a>
      <a class="ui-cell"><div class="ui-cell__bd"><div class="ui-cell_con"><div>纳税人识别号</div> <div>{{  info.taxpayerID  }}</div></div></div></a>
      <a class="ui-cell"><div class="ui-cell__bd"><div class="ui-cell_con"><div>地址、电话</div> <div>{{ info.address }}{{ info.telephone }}</div></div></div></a>
      <a class="ui-cell"><div class="ui-cell__bd"><div class="ui-cell_con"><div>开户行及账号</div> <div>{{ info.bank_name }}{{ info.bank_no }}</div></div></div></a>
    </div>
  </div>
</template>
<style lang="less">
  #invoiceRegisterDetailInfo .namec{display:inline-block;width:96px;text-align:left;overflow:hidden;white-space:nowrap;text-overflow:ellipsis; position: relative; top:5px;}
  #invoiceRegisterDetailInfo .flexc{display:-webkit-box;display:-ms-flexbox;display:flex}
  #invoiceRegisterDetailInfo .flexc .linkbtn{color:#0a9bee;cursor:default;font-size:15px;  position: relative; top:10px;}
  #invoiceRegisterDetailInfo .flexc .linkbtn:hover{text-decoration:underline}
  #invoiceRegisterDetailInfo .flexc>div:first-child{-webkit-box-flex:1;-ms-flex:1;flex:1}
  #invoiceRegisterDetailInfo .flexc>div:nth-child(2){-webkit-box-flex:5;-ms-flex:5;flex:5;font-size:.7em;text-align:right}
  #invoiceRegisterDetailInfo .wid{display:inline-block;width:30px}#invoiceRegisterDetailInfo .btndisabled{color:red}
  #invoiceRegisterDetailInfo .centerBtn{text-align:center}
  #invoiceRegisterDetailInfo .centerBtn .ui-btn{display:inline-block;border-radius:15px;width:70%;height:auto;line-height:1;vertical-align:middle;padding:8px 0}.rejectPng{position:absolute;top:calc(50% - 60px);left:calc(50% - 60px);z-index:50;filter:alpha(opacity=60);opacity:.6;-moz-opacity:.6;-khtml-opacity:.6}
</style>
<script>
import { formatDate, filterInvoiceType, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceRegisterDetailInfoInvoice',
  data () {
    return {
      cusID:'',
      loading:true,
      info: {}
    }
  },
  filters: {
    formatDate,
    numberFilter,
    filterInvoiceType
  },
  created () {
    let _this = this
    _this.cusID = _this.$route.params.id
    // 数据初始化
    _this.getInfo()
  },
  destroyed: function () {
  },
  methods: {
    //
    getInfo: function () {
      let _this = this
      // 获取详情
      _this.$http.post('../../../sales/getCustomerInvoice', {
        'id': _this.cusID
      }, {
        emulateJSON: true
      }).then((response) => {
        _this.info = response.body.data[0]
        console.log(response.body)
        _this.loading = false
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
  }
}
</script>
