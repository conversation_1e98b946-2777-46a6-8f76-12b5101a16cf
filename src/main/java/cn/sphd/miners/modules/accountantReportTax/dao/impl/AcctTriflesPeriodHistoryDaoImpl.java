package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesPeriodDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesPeriodHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesPeriodEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesPeriodHistoryEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTriflesPeriodHistoryDaoImpl extends BaseDao<AcctTriflesPeriodHistoryEntity, Serializable> implements AcctTriflesPeriodHistoryDao {

    @Autowired
    AcctTriflesPeriodHistoryDao triflesPeriodHistoryDao;

    @Override
    public List<AcctTriflesPeriodHistoryEntity> getListbytriflesId(Integer hisId) {
        String hql = "from AcctTriflesPeriodHistoryEntity where triflesHistory=:triflesHistory";
        HashMap<String, Object> param = new HashMap<>();
        param.put("triflesHistory",hisId);
        List<AcctTriflesPeriodHistoryEntity> list = triflesPeriodHistoryDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }
}
