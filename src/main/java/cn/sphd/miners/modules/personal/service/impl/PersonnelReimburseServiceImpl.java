package cn.sphd.miners.modules.personal.service.impl;

/*
**
*<AUTHOR>
*@Date 2016/12/7 16:41
*/

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.accountant.service.ReimburseRelevanceService;
import cn.sphd.miners.modules.dailyAffairs.dao.CommonInvoiceCertificationDao;
import cn.sphd.miners.modules.dailyAffairs.entity.CommonInvoiceCertification;
import cn.sphd.miners.modules.dailyAffairs.entity.PersonnelReimbursePayment;
import cn.sphd.miners.modules.dailyAffairs.service.PaymentApprovalService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.dailyAffairs.service.impl.ReimburseUsing;
import cn.sphd.miners.modules.finance.dao.FinanceAccountDao;
import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillAttachmentDao;
import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillDao;
import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillItemDao;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillAttachmentService;
import cn.sphd.miners.modules.personal.dao.*;
import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.dao.ApprovalItemDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.CodeService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("personnelReimburseService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PersonnelReimburseServiceImpl implements PersonnelReimburseService {
    @Autowired
    CodeService codeService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    AccountService accountService;
    @Autowired
    ReimburseRelevanceService reimburseRelevanceService;
    @Autowired
    DataService dataService;
    @Autowired
    RoleService roleService;
    @Autowired
    PaymentApprovalService paymentApprovalService;
    @Autowired
    FinanceReimburseBillAttachmentService financeReimburseBillAttachmentService;
    @Autowired
    UploadService uploadService;

    @Autowired
    PersonnelReimburseDao personnelReimburseDao;
    @Autowired
    PersonnelReimburseHistoryDao personnelReimburseHistoryDao;
    @Autowired
    PersonnelReimbursetAttachmentHistoryDao personnelReimbursetAttachmentHistoryDao;
    @Autowired
    PersonnelReimbursetAttachmentDao personnelReimbursetAttachmentDao;
    @Autowired
    FinanceReimburseBillDao financeReimburseBillDao;
    @Autowired
    FinanceReimburseBillItemDao financeReimburseBillItemDao;
    @Autowired
    UserDao userDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    FinanceReimburseBillAttachmentDao financeReimburseBillAttachmentDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    ApprovalItemDao approvalItemDao;
    @Autowired
    CommonInvoiceCertificationDao commonInvoiceCertificationDao;

    @Override
    public void updatePersonnelReimburse(PersonnelReimburse personnelReimburse) {
        personnelReimburseDao.update(personnelReimburse);
    }

    @Override
    public PersonnelReimburse personnelReimburseById(Integer id) {
        return personnelReimburseDao.get(id);
    }

    @Override
    public List<PersonnelReimburse> getPersonnelReimburseByUser(Integer id, String approveStatus) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from PersonnelReimburse where (user_=:userId or factUser=:userId)";   //申请人或者factUser都是userId的
        params.put("userId",id);
        if (!MyStrings.nulltoempty(approveStatus).isEmpty()) {
            hql+=" and approveStatus=:approveStatus";
            params.put("approveStatus", approveStatus);
        }
        hql+=" order by createDate desc";
        return personnelReimburseDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public void addPersonnelReimburseHistory(PersonnelReimburseHistory prh) {
        personnelReimburseHistoryDao.save(prh);
    }

    @Override
    public void addPersonnelReimburseAttachmentHistory(PersonnelReimbursetAttachmentHistory prah) {
        personnelReimbursetAttachmentHistoryDao.save(prah);
    }

    @Override
    public PersonnelReimburseHistory getPersonnelReimburseHistoryById(Integer id) {
        return personnelReimburseHistoryDao.get(id);
    }

    @Override
    public void deletePersonnelReimburseAttachment(PersonnelReimbursetAttachment personnelReimburseAttachment) {
        personnelReimbursetAttachmentDao.delete(personnelReimburseAttachment);
    }


    @Override
    public List<PersonnelReimburse> getMyPersonnelReimburseList(Integer userId, Integer approveStatus, PageInfo pageInfo) {
        HashMap<String,Object> params = new HashMap<>();
        StringBuffer hql=new StringBuffer("from PersonnelReimburse");
        String link=" where ";
        if (approveStatus!=null){
            params.put("approveStatus",String.valueOf(approveStatus));
            hql.append(link).append("approveStatus=:approveStatus");
            link=" and ";
        }
        if (userId!=null){
            params.put("user_",userId);
            hql.append(link).append("user_=:user_");
        }
        return personnelReimburseDao.getListByHQLWithNamedParams(hql.toString(),params,pageInfo);
    }

    @Override
    public boolean approvalReimburse(User user,Integer approvalProcessId, Integer approvalStatus, String approveMemo) {
        if (approvalProcessId!=null) {
            ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcessId);
            PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse().getId());
            personnelReimburse.setApproveLevel(approvalProcess.getLevel());
            UserMessage userMessage1 = userMessageService.getUserMessageByMessageId(approvalProcessId, "4");
            //approvalStatus等于1为批准，否则都是驳回
            if (approvalStatus == 1) {
                approvalProcess.setApproveStatus("2");
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setUserName(user.getUserName());
                approvalProcessService.updateApprovalProcess(approvalProcess);

                userMessage1.setApprovalStatus(2);  //消息中的审批状态为批准

//                ApprovalItem approvalItem = approvalProcessService.getc(user.getOid(), "reimburseApply");
                ApprovalItem approvalItem  = approvalService.getById(personnelReimburse.getApproveItem());
                ApprovalFlow af = approvalService.getApprovalFlowByItemIdAndLevel(approvalItem.getId(), approvalProcess.getLevel());

                //总级别<=当前审批级别 或者 审批金额小于当前审批人金额上限 或者 此级别审批者是超管 ，直接去财务审批
                if (approvalItem.getLevel() <= approvalProcess.getLevel() || personnelReimburse.getAmount().doubleValue() <= af.getAmountCeiling() || user.getRoleCode().equals("super")) {

                    ApprovalProcess app = new ApprovalProcess();
                    app.setApproveStatus("6");//1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                    app.setReimburse(personnelReimburse);
//                    User cu=userService.getUserByOidAndUserType(oid,1);//机构财务
//                    app.setToUser(cu.getUserID());
//                    app.setUserName(cu.getUserName());
                    app.setToUserName("财务处理者");
                    app.setToMid("lp");//报销受理模块
                    app.setCreateDate(new Date());
                    approvalProcessService.saveApprovalProcess(app);

                    List<UserPopedom> userPopedomList = userPopedomService.getUserPopedomByMid(user.getOid(), "lp");
                    for (UserPopedom u : userPopedomList) {

                        //给财务审批者的消息
                        UserMessage userMessage = new UserMessage();
                        userMessage.setUser(personnelReimburse.getUser());
                        userMessage.setApprovalStatus(6);  //进入第一个财务待接收(此字段为从消息进入后调那个页面)
                        userMessage.setMessageType("4");
                        userMessage.setHandleId(u.getUserId().toString());
                        userMessage.setMessageId(app.getId());
                        userMessage.setEventType("报销申请");
                        userMessage.setIllustrate(personnelReimburse.getUser().getUserName() + "的报销申请");
                        userMessage.setPersonnelReimburId(personnelReimburse.getId());
                        userMessage.setState(1);
                        userMessage.setReceiveUserId(u.getUserId());  //接收消息人
                        userMessage.setCreateDate(new Date());
                        userMessageService.addUserMassage(userMessage);
                    }

                } else {
                    Integer handleId = null;
                    String handleName = "";
                    String userName = "";
                    Integer uid = null;
                    Integer level = approvalProcess.getLevel() + 1;
                    for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
                        if (f.getLevel() == level) {
                            uid = f.getToUserId();
                            handleName = f.getToUser();//审批人名称
                            userName = f.getUserName();
                        }
                    }
                    if (uid == 0) {
                        handleId = Integer.decode(user.getLeader());//当前审批人的直接上级
//                        handleName="直属上级";
                        userName = userDao.get(Integer.decode(user.getLeader())).getUserName();
                    } else {
                        handleId = uid;
//                        handleName="指定审批人";
                    }
                    ApprovalProcess app = new ApprovalProcess();
                    app.setApproveStatus("1");
                    app.setReimburse(personnelReimburse);
                    app.setToUser(handleId);
                    app.setLevel(level);
                    app.setToUserName(handleName);//审批人总称
                    app.setUserName(userName);//审批人名称
                    app.setCreateDate(new Date());
                    approvalProcessService.saveApprovalProcess(app);

                    //给下级审批者的消息
                    UserMessage userMessage = new UserMessage();
                    userMessage.setUser(personnelReimburse.getUser());
                    userMessage.setApprovalStatus(1);
                    userMessage.setMessageType("4");
                    userMessage.setHandleId(handleId.toString());
                    userMessage.setMessageId(app.getId());
                    userMessage.setEventType("报销申请");
                    userMessage.setIllustrate(personnelReimburse.getUser().getUserName() + "的报销申请");
                    userMessage.setPersonnelReimburId(personnelReimburse.getId());
                    userMessage.setMessageType("4");
                    userMessage.setState(1);
                    userMessage.setReceiveUserId(handleId);  //接收消息人
                    userMessage.setCreateDate(new Date());
                    userMessageService.addUserMassage(userMessage);

                }

            } else {
                approvalProcess.setApproveStatus("3");
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setUserName(user.getUserName());
                approvalProcess.setApproveMemo(approveMemo);

                personnelReimburse.setApproveMemo(approveMemo);
                personnelReimburse.setApproveStatus("3");
                approvalProcessService.updateApprovalProcess(approvalProcess);

                userMessage1.setApprovalStatus(3);  //消息中的审批状态为驳回
            }
            personnelReimburseDao.update(personnelReimburse);

            //   批准/驳回后此审批者查看的消息改为已读状态
            userMessage1.setState(2);
            userMessage1.setHandleId(user.getUserID().toString());
            userMessage1.setHandleName(user.getUserName());
            userMessage1.setHandleReply(approveMemo);
            userMessage1.setHandleTime(new Date());
            userMessageService.updateUserMassage(userMessage1);

            //   批准/驳回后给申请者的消息
            UserMessage userMessage = new UserMessage();
            userMessage.setUser(personnelReimburse.getUser());
            if (approvalStatus == 1) {
                userMessage.setApprovalStatus(2);
            } else {
                userMessage.setApprovalStatus(3);
            }
            userMessage.setHandleId(approvalProcess.getToUser().toString());
            userMessage.setMessageId(approvalProcess.getId());
            userMessage.setEventType("报销结果");
            userMessage.setIllustrate(personnelReimburse.getUser().getUserName() + "的报销申请");
            userMessage.setPersonnelReimburId(personnelReimburse.getId());
            userMessage.setMessageType("4");
            userMessage.setState(1);
            userMessage.setReceiveUserId(personnelReimburse.getUser().getUserID());  //接收消息人
            userMessage.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
            userMessage.setCreateDate(new Date());
            userMessageService.addUserMassage(userMessage);
            return true;
        }else {
            return false;

        }
    }

    @Override
    public Map<String,Object> getReimburseDetail(Integer reimburseId) {
        Map<String,Object> map = new HashMap<>();
        PersonnelReimburse personnelReimburse = personnelReimburseDao.get(reimburseId);
        if (personnelReimburse!=null) {
            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByPersonnelReimburseId(personnelReimburse.getId());
            List<Map<String,Object>> listMapBillCat = new ArrayList<>();
            List<Map<String,Object>> listMapFeeCat = new ArrayList<>();

            // 按票据种类----需要的为票面金额
            Map<String,Object> params = new HashMap<>();
            String hql = "select b.id,b.name,sum(a.relativeBillQuantity),sum(a.billAmount) from FinanceReimburseBill a,Code b where a.relativeBillQuantity!=0 and a.reimburseId=:reimburseId and a.billCat in (select b.id from CodeCategory c where c.org_ =:org order by b.enabled desc) group by a.billCat";
            params.put("reimburseId",reimburseId);
            params.put("org",personnelReimburse.getUser().getOid());
            List<Object[]> listObjects = financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
            for (Object[] listObject:listObjects) {
                Map<String,Object> map1 = new HashMap<>();
                map1.put("billCat",listObject[0]);  //票据种类id
                map1.put("billCatName",listObject[1]);  //票据种类名字
                map1.put("num",listObject[2]);  //此票据种类的票据数量
                map1.put("totalAmount",listObject[3]);   //此票据种类的总金额
                listMapBillCat.add(map1);
            }
            this.sort(listMapBillCat);  //按金额倒序

            // 按费用类别
            Map<String,Object> params1 = new HashMap<>();
            String hql1 = "select c.id,a.feeCatName,sum(a.amount) from FinanceReimburseBillItem a,Code c where a.billId in (select b.id from FinanceReimburseBill b,CodeCategory d where b.reimburseId =:reimburseId and a.feeCat=c.id and d.org_=:org) group by a.feeCat";
            params1.put("reimburseId",reimburseId);
            params1.put("org",personnelReimburse.getUser().getOid());
            List<Object[]> listObject1s = financeReimburseBillDao.getListByHQLWithNamedParams(hql1,params1);
            for (Object[] listObject1:listObject1s) {
                Map<String,Object> map2 = new HashMap<>();
                Integer feeCatId = (Integer) listObject1[0];
                Code code = codeService.getCodeById(feeCatId);
                if (code!=null && code.getParent_()!=null){  //
                    map2.put("feeCat",code.getParent_());  //一级费用类别id
                    map2.put("feeCatName",code.getParent().getName());  //费用类别名字
                    map2.put("secondFeeCat",feeCatId);  //二级费用类别id
                    map2.put("secondFeeCatName",listObject1[1]);  //二级费用类别名字
                }else {
                    map2.put("feeCat",feeCatId);  //一级费用类别id
                    map2.put("feeCatName",listObject1[1]);  //费用类别名字
                    map2.put("secondFeeCat","");  //二级费用类别id
                    map2.put("secondFeeCatName","");  //二级费用类别名字
                }
                map2.put("totalAmount",listObject1[2]);   //此费用类别的总金额
                listMapFeeCat.add(map2);
            }
            this.sort(listMapFeeCat);  //按金额倒序

            PersonnelReimbursePayment personnelReimbursePayment = paymentApprovalService.getCurrentPaymentMethod(personnelReimburse.getId());
            FinanceAccount financeAccount = new FinanceAccount();
            if (personnelReimbursePayment!=null) {
                financeAccount = financeAccountDao.get(personnelReimbursePayment.getAccountId());
            }
            map.put("financeAccount",financeAccount);  //账户信息
            map.put("personnelReimbursePayment",personnelReimbursePayment);  //付款方式
            map.put("personnelReimburse",personnelReimburse);  //申请报销数据
            map.put("approvalProcessList",approvalProcessList);  //申请报销流程
            map.put("listMapBillCat",listMapBillCat);  //根据票据种类显示列表
            map.put("listMapFeeCat",listMapFeeCat);  //根据费用类别显示列表
        }
        return map;
    }

    //根据reimburseId查询票据详情
    @Override
    public List<FinanceReimburseBill> getReimburseBillByReimburseId(Integer reimburseId,Integer primaryBill){
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceReimburseBill where reimburseId=:reimburseId";
        params.put("reimburseId",reimburseId);
        if (primaryBill!=null){
            hql+=" and primaryBill=:primaryBill";
            params.put("primaryBill",primaryBill);
        }
        return financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public Map<String, Object> cancel(Integer reimburseId, Map<String, Object> map) {
        Map<String,Object> params = new HashMap<>();
        PersonnelReimburse personnelReimburse = personnelReimburseDao.get(reimburseId);  //报销详情

        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByPersonnelReimburseId(reimburseId);  //报销流程
        if (approvalProcessList.size()>1) {
            map.put("state",2);  //已有审批人进行审批，不可再进行撤销
        }else if (approvalProcessList.size()==1){
            ApprovalProcess approvalProcess = approvalProcessList.get(0);
            if (StringUtils.isNotEmpty(approvalProcess.getApproveStatus())&&!"1".equals(approvalProcess.getApproveStatus())){
                map.put("state",2);  //已有审批人进行审批，不可再进行撤销
            }else {
                List<Integer> billIds = getReimburseBillIds(reimburseId);  //报销下的所有票据id
                if (billIds.size() > 0) {
                    String hql = "delete from FinanceReimburseBillItem where billId in (:billId)";
                    params.put("billId", billIds);
                    financeReimburseBillItemDao.queryHQLWithNamedParams(hql, params);  //删除票据明细

                    String hql1 = "delete from FinanceReimburseBillAttachment where reimburseBillId in (:billId)";
                    financeReimburseBillAttachmentDao.queryHQLWithNamedParams(hql1, params);

                    String hql2 = "delete from FinanceReimburseBill where id in (:billId)";
                    financeReimburseBillDao.queryHQLWithNamedParams(hql2, params);  //删除票据
                    params.clear();
                }

                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(approvalProcessList.get(0).getOrg(), "lp");  //财务为权限审批
                for (UserPopedom u : userPopedomList) {
                    //待在线审批 -1
                    String content = this.pushContent(personnelReimburse, 2);
                    this.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/onlineAuditHandle", content, content, "reimburseBillVerification");
                }
                if (personnelReimburse.getFactUser() != null && personnelReimburse.getFactUser() != 0) {
                    this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getFactUser(), "/personnelReimbursePend", null, null, "applyReimburse");//申请人
                } else {
                    this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getCreator(), "/personnelReimbursePend", null, null, "applyReimburse");//申请人
                }
                approvalProcessDao.deleteAll(approvalProcessList);  //删除审批流程
                personnelReimburseDao.delete(personnelReimburse);  //删除报销申请
                map.put("state", 1);  //删除成功
            }
        }
        return map;
    }

    private List<Integer> getReimburseBillIds(Integer reimburseId){
        Map<String,Object> params = new HashMap<>();
        String hql = "select id from FinanceReimburseBill where reimburseId=:reimburseId";
        params.put("reimburseId",reimburseId);
        return financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
    }

    //报销 撤销 长连接推送   pass 通道  superscript 角标
    @Override
    public void reimburseRejectSend(int loginNum,int operate,PersonnelReimburse personnelReimburse,Integer toUserId,String pass, String title, String content, String code){
        System.out.println("报销推送开始:"+new Date());
        System.out.println("报销id："+personnelReimburse.getId()+" userId: "+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("personnelReimburse", personnelReimburse);
        User user = userDao.get(toUserId);  // 推送人
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,user,code);
        System.out.println("报销推送结束:"+new Date());
    }

    public void reimburseRejectSendList(int loginNum,int operate,List<Map<String,Object>> listMap,Integer toUserId,String pass, String title, String content, String code){
        System.out.println("报销增票认证推送开始:"+new Date());
//        System.out.println("报销id："+personnelReimburse.getId()+" userId: "+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("listMap", listMap);
        User user = userDao.get(toUserId);  // 推送人
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,user,code);
        System.out.println("报销增票认证推送结束:"+new Date());
    }

    @Override
    public List<PersonnelReimburse> getPersonnelReimburseByApprover(Integer userId, String approvalStatus) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from PersonnelReimburse where approveStatus='1' and id in (select reimburse_ from ApprovalProcess where toUser=:userId and approveStatus=:approveStatus and businessType is null)";
        params.put("userId",userId);
        params.put("approveStatus",approvalStatus);
        hql+=" order by createDate desc";
        return financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
    }

    /**
     *
     * @param userId  审批人id
     * @param approvalProcessId  审批流程id
     * @param approvalStatus  审批状态 1-批转 2-驳回
     * @param reason 驳回理由
     * @return
     */
    @Override
    public boolean approvalReimburse1(Integer userId, Integer approvalProcessId, String approvalStatus, String reason) {
        if (approvalProcessId!=null) {
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcessId);
            PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse().getId());
            personnelReimburse.setApproveLevel(approvalProcess.getLevel());
            Integer over = 1;  //1-整个报销的报销审批阶段完成，0-进入下级报销审批人
            //approvalStatus等于1为批准，否则都是驳回
            if ("1".equals(approvalStatus)) {
                approvalProcess.setApproveStatus("2");
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setUserName(user.getUserName());
                approvalProcessService.updateApprovalProcess(approvalProcess);

                ApprovalItem approvalItem = approvalService.getById(personnelReimburse.getApproveItem());
                ApprovalFlow af = approvalService.getApprovalFlowByItemIdAndLevel(approvalItem.getId(), approvalProcess.getLevel());

                //总级别<=当前审批级别 或者 审批金额小于当前审批人金额上限 或者 此级别审批者是超管 ，直接去财务审批
                if (approvalItem.getLevel() <= approvalProcess.getLevel() || personnelReimburse.getAmount().doubleValue() <= af.getAmountCeiling() || user.getRoleCode().equals("super")) {

                    //财务-待线下审核的流程
                    ApprovalProcess process = new ApprovalProcess();
                    process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                    process.setLevel(1);
                    process.setToUserName("财务处理者");
                    process.setToMid("lp");  //报销受理模块
                    process.setReimburse(personnelReimburse);
                    process.setCreateDate(new Date());
                    process.setOrg(user.getOid());
                    process.setUserName("财务处理者");
                    process.setFromUser(personnelReimburse.getUser_());
                    process.setHandleTime(new Date());
                    process.setBusinessType(33);
                    approvalProcessDao.save(process);

                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //财务审批人待线下审核+1
                        String contnet = this.pushContent(personnelReimburse,3);
                        this.reimburseRejectSend(1,1,personnelReimburse, u.getUserId(), "/offlineAuditHandle",contnet,contnet,"reimburseBillVerification");
                        //财务审批人在线审核OK 數據-1
                        this.reimburseRejectSend(0,-1,personnelReimburse, u.getUserId(), "/onlineAuditHandle",null,null,"reimburseBillVerification");
                    }
                    if (personnelReimburse.getFactUser()!=null&&personnelReimburse.getFactUser()!=0){
                        // 批准给申请人发消息
                        userSuspendMsgService.saveUserSuspendMsg(1, "报销申请在线审批已通过，请将实际票据交财务审核。", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), personnelReimburse.getFactUser(), "applyReimburseDetail", personnelReimburse.getId());  //给前端要查看详情的链接

                    }else {
                        // 批准给申请人发消息
                        userSuspendMsgService.saveUserSuspendMsg(1, "报销申请在线审批已通过，请将实际票据交财务审核。", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), personnelReimburse.getUser_(), "applyReimburseDetail", personnelReimburse.getId());  //给前端要查看详情的链接
                    }
                } else {
                    Integer handleId = null;
                    String handleName = "";
                    String userName = "";
                    Integer uid = null;
                    Integer level = approvalProcess.getLevel() + 1;
                    for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
                        if (f.getLevel() == level) {
                            uid = f.getToUserId();
                            handleName = f.getToUser();//审批人名称
                            userName = f.getUserName();
                        }
                    }
                    if (uid == 0) {
                        handleId = Integer.decode(user.getLeader());//当前审批人的直接上级
                        userName = userDao.get(Integer.decode(user.getLeader())).getUserName();
                    } else {
                        handleId = uid;
                    }
                    ApprovalProcess app = new ApprovalProcess();
                    app.setApproveStatus("1");
                    app.setReimburse(personnelReimburse);
                    app.setToUser(handleId);
                    app.setLevel(level);
                    app.setToUserName(handleName);//审批人总称
                    app.setUserName(userName);//审批人名称
                    app.setCreateDate(new Date());
                    app.setFromUser(personnelReimburse.getUser_());
                    app.setOrg(user.getOid());
                    approvalProcessService.saveApprovalProcess(app);

                    //下级审批者待处理+1
                    String contnet = this.pushContent(personnelReimburse,1);
                    this.reimburseRejectSend(1,1,personnelReimburse, handleId, "/reimburseHandle",contnet,contnet,"approvalReimburse");
                    over = 0;  //1-整个报销的报销审批阶段完成，0-进入下级报销审批人
                }
                //当前审批者待处理-1
                this.reimburseRejectSend(-1,-1,personnelReimburse, approvalProcess.getToUser(), "/reimburseHandle",null,null,"approvalReimburse");

                //当前审批者已批准--加一条数据
                this.reimburseRejectSend(0, 1, personnelReimburse, approvalProcess.getToUser(), "/reimbursesApproval", null, null, "approvalReimburse");

            } else {  //2-驳回
                approvalProcess.setApproveStatus("3");
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setUserName(user.getUserName());
                approvalProcess.setApproveMemo(reason);
                approvalProcess.setReason(reason);

                personnelReimburse.setApproveMemo(reason);
                personnelReimburse.setApproveStatus("3");
                approvalProcessService.updateApprovalProcess(approvalProcess);
                //当前审批者待处理-1
                this.reimburseRejectSend(-1,-1,personnelReimburse, approvalProcess.getToUser(), "/reimburseHandle",null,null,"approvalReimburse");

                if (personnelReimburse.getFactUser()!=null&&personnelReimburse.getFactUser()!=0) {
                    //申请人待处理-1
                    this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getFactUser(), "/personnelReimbursePend", "申请被驳回", "申请被驳回", "applyReimburse");
                    // 驳回给-申请人发消息
                    userSuspendMsgService.saveUserSuspendMsg(1, " 您的报销申请被驳回了！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) , personnelReimburse.getFactUser(),"applyReimburseDetail",personnelReimburse.getId());  //给前端要查看详情的链接

                }else {
                    //申请人待处理-1
                    this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getUser_(), "/personnelReimbursePend", "申请被驳回", "申请被驳回", "applyReimburse");
                    // 驳回给-申请人发消息
                    userSuspendMsgService.saveUserSuspendMsg(1, " 您的报销申请被驳回了！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) , personnelReimburse.getUser_(),"applyReimburseDetail",personnelReimburse.getId());  //给前端要查看详情的链接

                }

            }
            personnelReimburseDao.update(personnelReimburse);

            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByPersonnelReimburseId(personnelReimburse.getId());
            for (ApprovalProcess appProcess:approvalProcessList) {
                if (over==1 && appProcess.getBusinessType()==null && "2".equals(appProcess.getApproveStatus())) {//历史审批人已批准中减数据(就只有报销审批的流程，over==1是判断是否是报销审批流程完结了)
                    this.reimburseRejectSend(0,-1,personnelReimburse, appProcess.getToUser(), "/reimbursesApproval",null,null,"approvalReimburse");//历史审批人已批准中减数据
                }
            }

            return true;
        }else {
            return false;
        }
    }

    /**
     * 财务出纳-个人报销待处理/待两讫/待认证列表
     * @param oid  机构id
     * @param approvalStatus  1-待处理 2-待两讫 3-待认证
     * @return
     */
    @Override
    public List<PersonnelReimburse> cashier(Integer userId,Integer oid, String approvalStatus) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from PersonnelReimburse where approveStatus='1' and id in (select reimburse_ from ApprovalProcess where org=:org and toMid='lp' and approveStatus=:approveStatus";
        params.put("org",oid);
        params.put("approveStatus",approvalStatus);
        if (userId!=null){
            hql+=" and toUser=:toUser";
            params.put("toUser",userId);
        }
        hql+=")";
        hql+=" order by createDate desc";
        return financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public List<Map<String,Object>> cashierAuthentication(Integer oid) {
        Code code = codeService.getCodeByName(oid,"增值税专用发票");
        Map<String,Object> params = new HashMap<>();
//        String hql = "select count(case when certificationState='0' then id else null end)," +
//                "count(case when (certificationState='1' or certificationState='2') then id else null end)," +
//                "reimburse.id,reimburse.createDate,reimburse.createName,billCat from FinanceReimburseBill where " +
//                "reimburse.user.oid=:oid and reimburse.approveStatus=2 and billCat=:codeId group by reimburseId";
//        params.put("oid",oid);
//        params.put("codeId",code.getId());
//        List<Object[]> listObjects = financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
//        List<Map<String,Object>> cashierAuthentication = new ArrayList<>();
//        for (Object[] listObject:listObjects) {
//            Map<String,Object> map = new HashMap<>();
//            int num1 = listObject[0].hashCode();  //待认证票据数量
//            int num2 = listObject[1].hashCode();  //已认证票据数量
//            map.put("reimburseId",listObject[2]);  //报销id
//            map.put("createDate",listObject[3]);  //创建时间
//            map.put("createName",listObject[4]);   //创建人
//            map.put("billCat",listObject[5]);   //票据种类id
//            if (num1!=0){
//                map.put("num",num1+num2);  //票据数量
//                cashierAuthentication.add(map);
//            }
//
//        }

        String hql = "select id,billNo,itemAmount,billCat from FinanceReimburseBill where " +
            "reimburse.user.oid=:oid and reimburse.approveStatus='2' and certificationState='0' and billCat=:billCat";
        params.put("oid",oid);
        params.put("billCat",code.getId());
        List<Object[]> listObjects = financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
        List<Map<String,Object>> cashierAuthentication = new ArrayList<>();
        for (Object[] listObject:listObjects) {
            Map<String,Object> map = new HashMap<>();
//            map.put("num",listObject[0]);  //票据数量
            map.put("id",listObject[0]);  //报销票据id
            map.put("billNo",listObject[1]);  //发票号码
            map.put("itemAmount",listObject[2]);   //单张票面金额
            map.put("billCat",listObject[3]);   //票据种类id
            cashierAuthentication.add(map);
        }
        return cashierAuthentication;
    }

    /**
     * 财务出纳 --待两讫 驳回 两讫
     * @param userId  登录人id
     * @param approvalProcessId  审批过程
     * @param factDate 实际时间
     * @return
     */
    @Override
    public Map<String ,Object> cashierTwoApproval(Integer userId, Integer approvalProcessId, Date factDate,String summary) {
        Map<String ,Object> map = new HashedMap();
        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcessId);
        User user = userDao.get(userId);
        if (approvalProcess!=null ){
            PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse_());
            personnelReimburse.setApproveLevel(approvalProcess.getLevel());
            PersonnelReimbursePayment personnelReimbursePayment = paymentApprovalService.getCurrentPaymentMethod(personnelReimburse.getId());
            if (personnelReimbursePayment!=null) {
                personnelReimbursePayment.setFactDate(factDate);
                //这里只剩下银行转账的方式了(确定付账的)
                map = this.reimbursementEntry(personnelReimburse, user, "3", summary,factDate, personnelReimbursePayment.getAccountId(), approvalProcess,3);
            }
        }
        return map;
    }

    @Override
    public String reimburseApply(Integer userId, String reimburse, String commonBills,Integer factUser) throws ParseException {
        String content = "申请成功";  //申请后返回的状态
        User user = userDao.get(userId);
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        JSONObject jsonReimburse = JSONObject.fromObject(reimburse);
        ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "reimburseApply");  //报销流程
        ApprovalItem approvalItem1 = roleService.getCurrentItem(user.getOid(),"paymentApproval");  //付款审批流程
        ApprovalItem approvalItem2 = roleService.getApprovalItemByPreviousItem(approvalItem1.getId()); //付款复核的流程
        Double amount = jsonReimburse.getDouble("amount");
        if (amount<=approvalItem.getUpperLimit().doubleValue() || approvalItem.getUpperLimit().compareTo(new BigDecimal(-1))==0) {
            PersonnelReimburse personnelReimburse = new PersonnelReimburse();
            personnelReimburse.setBeginDate(sdf.parse(jsonReimburse.getString("beginDate")));//发生日期 开始
            personnelReimburse.setEndDate(sdf.parse(jsonReimburse.getString("endDate")));// 发生日期 结束
            personnelReimburse.setTransactionType(jsonReimburse.getString("transactionType"));//事务类型:1-销售事务,其它-非销售事务
            personnelReimburse.setPurpose(jsonReimburse.getString("purpose"));//报销事由
            personnelReimburse.setBillQuantity(jsonReimburse.getInt("billQuantity"));//票据数量
            personnelReimburse.setBillAmount(new BigDecimal(jsonReimburse.getDouble("billAmount")));  //票面金额
            personnelReimburse.setAmount(new BigDecimal(jsonReimburse.getDouble("amount")));//合计金额
            personnelReimburse.setCreator(user.getUserID());//创建人id
            personnelReimburse.setCreateName(user.getUserName());//创建人
            personnelReimburse.setCreateDate(new Date());//创建时间
            personnelReimburse.setUser(user);
            personnelReimburse.setApproveStatus("1");//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
            personnelReimburse.setApproveItem(approvalItem.getId());//报销流程申批项目 1-财务  2-加班  3-请假
            personnelReimburse.setInstance(approvalItem1.getId());  //付款审批流程
            personnelReimburse.setAuditInstance(approvalItem2.getId());  //付款复核的流程
            String summary = user.getUserName() + "于" + sdf.format(personnelReimburse.getBeginDate()) + "到" + sdf.format(personnelReimburse.getEndDate()) + "发生的";
            if (!"1".equals(personnelReimburse.getTransactionType())) {
                summary += "非";
            }
            summary += "销售费用（";
            personnelReimburse.setFactUser(factUser); //实际用户(同事userid)', 0-本人的财务支出

            String module = "数据录入";  //为下面保存图片using中使用的
            if (factUser!=null&&factUser!=0){
                User user1 = userDao.get(factUser);  //同事user
                String userName = user.getUserName()+"代其"+user1.getUserName();
                personnelReimburse.setCreateName(userName);//创建人
            }else {
                module = "日常事务";   //为下面保存图片using中使用的
            }
            personnelReimburseDao.save(personnelReimburse);

            if (commonBills != null && !commonBills.equals("")) {
                JSONArray arrayCommonBills = JSONArray.fromObject(commonBills, new JsonConfig());
                List commonBillList = JSONArray.toList(arrayCommonBills);
                for (int i = 0; i < commonBillList.size(); i++) {
                    JSONObject jsonCommonBill = JSONObject.fromObject(commonBillList.get(i));//单种类的票
                    Code bcode = codeService.getCodeById(jsonCommonBill.getInt("billCat")); //票据种类
                    List billList = jsonCommonBill.getJSONArray("bills");
                    Integer itemCount = Integer.parseInt(jsonCommonBill.getString("itemNum"));  //是否是一行内容 1-是 0-否
                    int pid = 0;
                    for (int z = 0; z < billList.size(); z++) {
                        JSONObject jsonBill = JSONObject.fromObject(billList.get(z));//单种类的票
                        List imgPaths = jsonBill.getJSONArray("imgPaths");//多附件路径
                        List billItemList = jsonBill.getJSONArray("billItems");// 货物清单

                        if (bcode.getName().equals("定额发票")) {
                            FinanceReimburseBill financeReimburseBill = new FinanceReimburseBill();
                            Integer billCat = jsonCommonBill.getInt("billCat");
                            financeReimburseBill.setBillCat(billCat);//票据种类
                            financeReimburseBill.setBillCatName(codeService.getCodeById(billCat).getName());//票据种类名
                            financeReimburseBill.setBillAmount(new BigDecimal(jsonBill.getDouble("billAmount")));  //票面金额(总金额)
                            financeReimburseBill.setAmount(new BigDecimal(jsonBill.getDouble("amount")));//
                            financeReimburseBill.setMemo(jsonBill.getString("memo"));//备注
                            financeReimburseBill.setCreator(user.getUserID());//创建人id
                            financeReimburseBill.setCreateName(user.getUserName());//创建人
                            financeReimburseBill.setCreateDate(new Date());//创建时间
                            financeReimburseBill.setCertificationState("0");//认证状态
                            financeReimburseBill.setReimburseId(personnelReimburse.getId());
                            Integer quantity = 0;
                            financeReimburseBill.setRelativeBillQuantity(quantity);//相同票据数量
                            financeReimburseBill.setItemCount(itemCount);  //是否为一行内容
                            financeReimburseBill.setPrimaryBill(pid);
                            financeReimburseBillDao.save(financeReimburseBill);

                            int k = 0;
                            int itemQuantity = 0;
                            while (k < billItemList.size()) {
                                JSONObject jsonBillItem = JSONObject.fromObject(billItemList.get(k));

                                Integer feeCatId = jsonBillItem.getInt("feeCat");
                                String feeCatPath = "";
                                Code code = codeService.getFirstCodeByid(feeCatId);
                                if (code.getId().equals(feeCatId)) {
                                    feeCatPath = "/" + feeCatId + "/";
                                } else {
                                    feeCatPath = "/" + code.getId() + "/" + feeCatId + "/";
                                }
                                Code ziCode = codeService.getCodeById(feeCatId);
                                financeReimburseBill.setFeeCat(feeCatId);//费用类别
                                financeReimburseBill.setFeeCatName(ziCode.getName());//费用类别名

                                FinanceReimburseBillItem financeReimburseBillItem = new FinanceReimburseBillItem();
                                financeReimburseBillItem.setItemQuantity(jsonBillItem.getDouble("itemQuantity"));//数量
                                financeReimburseBillItem.setFeeCatName(ziCode.getName());//费用类别名
                                itemQuantity += financeReimburseBillItem.getItemQuantity();
                                financeReimburseBillItem.setUniPrice(jsonBillItem.getDouble("uniPrice"));//单价

                                financeReimburseBillItem.setFeeCat(feeCatId);//费用类别
                                financeReimburseBillItem.setFeeCatPath(feeCatPath);//费用类别路径
                                financeReimburseBillItem.setPrice(new BigDecimal(jsonBillItem.getDouble("price")));//金额
                                financeReimburseBillItem.setBillId(financeReimburseBill.getId());
                                financeReimburseBillItem.setCreator(user.getUserID());//创建人id
                                financeReimburseBillItem.setCreateName(user.getUserName());//创建人
                                financeReimburseBillItem.setCreateDate(new Date());//创建时间
                                financeReimburseBillItem.setAmount(new BigDecimal(jsonBillItem.getDouble("price")));
                                financeReimburseBillItem.setRowNo(k + 1);  //行号
                                financeReimburseBillItem.setMemo(jsonBillItem.getString("memo"));
                                financeReimburseBillItemDao.save(financeReimburseBillItem);
                                k++;

                                summary += code.getName() + "与";
                            }
                            financeReimburseBill.setItemQuantity(itemQuantity);
                            financeReimburseBill.setRelativeBillQuantity(itemQuantity);
                            int l = 0;
                            while (l < imgPaths.size() && imgPaths.size() > 0) {//附件
                                if (!MyStrings.nulltoempty(imgPaths.get(l).toString()).isEmpty()){
                                    FinanceReimburseBillAttachment financeReimburseBillAttachment = new FinanceReimburseBillAttachment();
                                    financeReimburseBillAttachment.setPath(imgPaths.get(l).toString());
                                    financeReimburseBillAttachment.setFinanceReimburseBill(financeReimburseBill);
                                    financeReimburseBillAttachmentDao.save(financeReimburseBillAttachment);

                                    ReimburseUsing reimburseUsing = new ReimburseUsing(financeReimburseBillAttachment.getId());
                                    uploadService.addFileUsing(reimburseUsing, imgPaths.get(l).toString(),null, user, module);//新增引用表
                                }
                                l++;
                            }
                            if (z == 0) {
                                pid = financeReimburseBill.getId();   //全部
                            }

                        } else {
                            List billNos = jsonBill.getJSONArray("billNos");// 多张发票号
                            Integer forNum = 1;
                            if (0 == itemCount) {
                                forNum = jsonCommonBill.getInt("number");  //多行的有相同的票据(票号不同)
                            }
                            for (int j = 0; j < forNum; j++) {
                                FinanceReimburseBill financeReimburseBill = new FinanceReimburseBill();
                                financeReimburseBill.setItemAmount(new BigDecimal(jsonBill.getDouble("billAmount")));  //单张票面金额
                                financeReimburseBill.setBillAmount(new BigDecimal(forNum * jsonBill.getDouble("billAmount")));//票面金额(总金额)
                                financeReimburseBill.setRelativeBillQuantity(forNum);//相同票据数量
                                if (0 == itemCount && pid!=0 && forNum>1) {
                                    financeReimburseBill.setRelativeBillQuantity(0);//相同票据数量
                                }
                                financeReimburseBill.setBillCat(jsonCommonBill.getInt("billCat"));//票据种类
                                financeReimburseBill.setBillCatName(bcode.getName());
                                financeReimburseBill.setAmount(new BigDecimal(jsonBill.getDouble("amount")));//
                                if (billNos.size() > 0 && itemCount == 0) {
                                    financeReimburseBill.setBillNo(billNos.get(j).toString());// 相同信息的 发票号
                                } else if (billNos.size() == 1 && 1 == itemCount) {
                                    financeReimburseBill.setBillNo(billNos.get(0).toString());// 相同信息的 发票号
                                }
                                if (!bcode.getName().equals("收据")) {
                                    financeReimburseBill.setMemo(jsonBill.getString("memo"));//备注
                                }
                                financeReimburseBill.setCreator(user.getUserID());//创建人id
                                financeReimburseBill.setCreateName(user.getUserName());//创建人
                                financeReimburseBill.setCreateDate(new Date());//创建时间
                                financeReimburseBill.setCertificationState("0");//认证状态
                                financeReimburseBill.setReimburseId(personnelReimburse.getId());
                                try {
                                    financeReimburseBill.setIssueDate(sdf.parse(jsonBill.getString("issueDate")));//开票日期
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }

                                String qrInfo = (String) jsonBill.get("qrInfo");  //发票信息:扫二维码取的所有信息
                                if (StringUtils.isNotEmpty(qrInfo)){
                                    financeReimburseBill.setQrInfo(qrInfo);
                                }
                                financeReimburseBill.setPrimaryBill(pid);//自关联id
                                financeReimburseBill.setItemCount(itemCount);
                                financeReimburseBillDao.save(financeReimburseBill);
                                if (0 == itemCount && j == 0) {  //多行时
                                    pid = financeReimburseBill.getId();
                                } else if (1 == itemCount && j == 0 && z == 0) {   //单行时
                                    pid = financeReimburseBill.getId();
                                }

                                int k = 0;
                                while (k < billItemList.size()) {
                                    JSONObject jsonBillItem = JSONObject.fromObject(billItemList.get(k));

                                    FinanceReimburseBillItem financeReimburseBillItem = new FinanceReimburseBillItem();
                                    Integer feeCatId = jsonBillItem.getInt("feeCat");
                                    String feeCatPath = "";
                                    Code code = codeService.getFirstCodeByid(feeCatId);

                                    if (code.getId().equals(feeCatId)) {
                                        feeCatPath = "/" + feeCatId + "/";
                                    } else {
                                        feeCatPath = "/" + code.getId() + "/" + feeCatId + "/";
                                    }
                                    Code ziCode = codeService.getCodeById(feeCatId);

                                    financeReimburseBill.setBillCat(jsonCommonBill.getInt("billCat"));
                                    financeReimburseBill.setFeeCat(feeCatId);
                                    financeReimburseBill.setFeeCatName(ziCode.getName());//费用类别名

                                    financeReimburseBillItem.setFeeCat(feeCatId);//费用类别
                                    financeReimburseBillItem.setFeeCatName(ziCode.getName());//费用类别名
                                    financeReimburseBillItem.setFeeCatPath(feeCatPath);//费用类别路径
                                    financeReimburseBillItem.setItemName(jsonBillItem.getString("itemName"));//货物名称
                                    financeReimburseBillItem.setModel(jsonBillItem.getString("model"));//规格型号
                                    financeReimburseBillItem.setUnit(jsonBillItem.getString("unit"));//单位
                                    String itemQuantity = jsonBillItem.getString("itemQuantity");
                                    if (!MyStrings.nulltoempty(itemQuantity).isEmpty()) {
                                        financeReimburseBillItem.setItemQuantity(jsonBillItem.getDouble("itemQuantity"));//数量
                                        Double uniPrice = jsonBillItem.getDouble("uniPrice");
                                        if (uniPrice != null) {
                                            financeReimburseBillItem.setUniPrice(jsonBillItem.getDouble("uniPrice"));//单价
                                        }
                                    }

                                    financeReimburseBillItem.setPrice(new BigDecimal(jsonBillItem.getDouble("price")));//金额
                                    financeReimburseBillItem.setAmount(new BigDecimal(jsonBillItem.getDouble("price")));//
                                    if (bcode.getName().equals("增值税专用发票") || (bcode.getName().equals("增值税普通发票") && itemCount == 0)) {
                                        financeReimburseBillItem.setTaxRate(new BigDecimal(jsonBillItem.getDouble("taxRate")));//税率
                                        financeReimburseBillItem.setTaxAmount(new BigDecimal(jsonBillItem.getDouble("taxAmount")));//税额
                                        financeReimburseBillItem.setAmount(new BigDecimal(jsonBillItem.getDouble("amount")));//含税合计
                                    }
                                    summary += code.getName() + "与";

                                    financeReimburseBillItem.setBillId(financeReimburseBill.getId());
                                    financeReimburseBillItem.setCreator(user.getUserID());//创建人id
                                    financeReimburseBillItem.setCreateName(user.getUserName());//创建人
                                    financeReimburseBillItem.setCreateDate(new Date());//创建时间
                                    financeReimburseBillItem.setRowNo(k + 1);  //行号
                                    if (!bcode.getName().equals("收据")) {
                                        financeReimburseBillItem.setMemo(jsonBill.getString("memo"));  //备注
                                    }
                                    financeReimburseBillItemDao.save(financeReimburseBillItem);
                                    k++;
                                }

                                int l = 0;
                                while (l < imgPaths.size() && imgPaths.size() > 0) {//附件
                                    if (!MyStrings.nulltoempty(imgPaths.get(l).toString()).isEmpty()) {
                                        FinanceReimburseBillAttachment financeReimburseBillAttachment = new FinanceReimburseBillAttachment();
                                        financeReimburseBillAttachment.setPath(imgPaths.get(l).toString());
                                        financeReimburseBillAttachment.setFinanceReimburseBill(financeReimburseBill);
                                        financeReimburseBillAttachmentDao.save(financeReimburseBillAttachment);

                                        ReimburseUsing reimburseUsing = new ReimburseUsing(financeReimburseBillAttachment.getId());
                                        uploadService.addFileUsing(reimburseUsing, imgPaths.get(l).toString(),null, user, module);//新增引用表
                                    }
                                    l++;
                                }
                            }
                        }
                    }
                }
            }

            summary = StringUtils.substringBeforeLast(summary, "与");
            summary += "）";
            personnelReimburse.setSummary(summary);
            personnelReimburseDao.update(personnelReimburse);

            //财务-带在线审核的流程
            ApprovalProcess process = new ApprovalProcess();
            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
            process.setLevel(1);
            process.setToUserName("财务处理者");
            process.setToMid("lp");  //报销受理模块
            process.setReimburse(personnelReimburse);
            process.setCreateDate(new Date());
            process.setOrg(user.getOid());
            process.setUserName("财务处理者");
            process.setFromUser(userId);
            process.setAskName(user.getUserName());
            process.setHandleTime(new Date());
            process.setBusinessType(32);
            approvalProcessDao.save(process);

            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
            for (UserPopedom u : userPopedomList) {
                //财务审批人待在线审核+1
                String content1 = this.pushContent(personnelReimburse,1);
                this.reimburseRejectSend(1,1,personnelReimburse, u.getUserId(), "/onlineAuditHandle",content1,content1,"reimburseBillVerification");
            }
            if(factUser!=null&&factUser!=0){  //财务录入的相关报销，推送给对应的同事
                this.reimburseRejectSend(0, 1, personnelReimburse, factUser, "/personnelReimbursePend", null, null, "applyReimburse");//申请人
            }else {
                this.reimburseRejectSend(0, 1, personnelReimburse, userId, "/personnelReimbursePend", null, null, "applyReimburse");//申请人
            }
        }else {
            content = "公司规定：报销最高不得超过"+approvalItem.getUpperLimit()+"元!";
        }
        return content;
    }

    /**
     * 按发票种类/费用类别查询发票详情
     * @param reimburseId  报销id
     * @param billCat  票据种类id
     * @param feeCat  一级费用类别id
     * @param secondFeeCat  二级费用类别id
     * @return  map
     */
    @Override
    public Map<String, Object> getReimburseBillDetail(Integer reimburseId, Integer billCat, Integer feeCat, Integer secondFeeCat,Map<String,Object> map) {
        Map<String,Object> params = new HashMap<>();
        String hql;
        Integer feeCatFinal = feeCat;  //最终使用的费用类别
        if (secondFeeCat!=null){
            feeCatFinal = secondFeeCat;
        }
        List<Map<String,Object>> listMap = new ArrayList<>();

        if (billCat!=null){  //按票据种类查询
            Code bcode = codeService.getCodeById(billCat); //票据种类
            hql = "from FinanceReimburseBill where  reimburseId=:reimburseId and billCat=:billCat";
            params.put("reimburseId",reimburseId);
            params.put("billCat",billCat);
            if (!"定额发票".equals(bcode.getName())){
                hql+=" and relativeBillQuantity!=0";
            }
            List<FinanceReimburseBill> financeReimburseBills = financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);  //带详情的货物

            for (FinanceReimburseBill financeReimburseBill:financeReimburseBills) {
                List<FinanceReimburseBillItem> financeReimburseBillItems = getFinanceReimburseBillItemByBillId(financeReimburseBill.getId());
                List<FinanceReimburseBillAttachment> financeReimburseBillAttachments = getFinanceReimburseBillAttachmentByBillId(financeReimburseBill.getId());
                financeReimburseBill.setFinanceReimburseBillItemList(financeReimburseBillItems);
                financeReimburseBill.setPictures(financeReimburseBillAttachments);
            }
            map.put("financeReimburseBills",financeReimburseBills);

        }else if (feeCatFinal!=null){  //按费用类别
            hql = "select billItem.itemName,billItem.amount,bill.id from FinanceReimburseBillItem billItem, FinanceReimburseBill bill where bill.reimburseId=:reimburseId  and billItem.feeCat=:feeCat and billItem.billId=bill.id";
            params.put("reimburseId",reimburseId);
            params.put("feeCat",feeCatFinal);
            List<Object[]> listObjects = financeReimburseBillItemDao.getListByHQLWithNamedParams(hql,params);
            for (Object[] listObject:listObjects) {
                Map<String,Object> map1 = new HashMap<>();
                map1.put("itemName",listObject[0]);  //明细项名称(货物或应税劳务、服务名称)
                map1.put("amount",listObject[1]);   //金额

                Integer billId = (Integer) listObject[2];
                List<FinanceReimburseBillAttachment> financeReimburseBillAttachments = getFinanceReimburseBillAttachmentByBillId(billId);
                map1.put("pictures",financeReimburseBillAttachments);  //票据图片
                listMap.add(map1);
            }
            map.put("listMap",listMap);
        }
        return map;
    }

    //查询票据的货物详情
    private List<FinanceReimburseBillItem> getFinanceReimburseBillItemByBillId(Integer billId){
        Map<String,Object> params = new HashMap<>();
        String hql="from FinanceReimburseBillItem where billId=:billId";
        params.put("billId",billId);
        return financeReimburseBillItemDao.getListByHQLWithNamedParams(hql,params);
    }

    //查看票据的图片信息
    @Override
    public List<FinanceReimburseBillAttachment> getFinanceReimburseBillAttachmentByBillId(Integer billId){
        Map<String,Object> params = new HashMap<>();
        String hql="from FinanceReimburseBillAttachment where reimburseBillId=:reimburseBillId";
        params.put("reimburseBillId",billId);
        return financeReimburseBillAttachmentDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public FinanceReimburseBillAttachment getFinanceReimburseBillAttachmentByBill(Integer attachmentId) {
        FinanceReimburseBillAttachment financeReimburseBillAttachment = financeReimburseBillAttachmentDao.get(attachmentId);
        return financeReimburseBillAttachment;
    }

    /**
     * 查询某次报销中的所有增专发票
     * @param reimburseId 报销id
     * @param billCat  增值税专用发票id
     * @return
     */
    @Override
    public List<FinanceReimburseBill> authInvoiceDetail(Integer reimburseId, Integer billCat,String certificationState) {
        Map<String,Object> params = new HashMap<>();
        String hql="from FinanceReimburseBill where reimburseId=:reimburseId and billCat=:billCat";
        params.put("reimburseId",reimburseId);
        params.put("billCat",billCat);
        if (!MyStrings.nulltoempty(certificationState).isEmpty()){
            hql+=" and certificationState=:certificationState";
            params.put("certificationState",certificationState);
        }
        return financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
    }

    /**
     * 待认证中-增专发票的具体货物详情
     * @param billId  报销票据id
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> authBillItemDetail(Integer billId, Map<String, Object> map) {
        FinanceReimburseBill financeReimburseBill = financeReimburseBillDao.get(billId);

        Map<String, Object> params = new HashMap<>();
        String hql = "from FinanceReimburseBillAttachment where reimburseBillId=:billId";
        params.put("billId",billId);
        List<FinanceReimburseBillAttachment> billPictures = financeReimburseBillAttachmentDao.getListByHQLWithNamedParams(hql,params);  //票据图片

        String hql1 = "from FinanceReimburseBillItem where billId=:billId";
        List<FinanceReimburseBillItem> financeReimburseBillItems = financeReimburseBillItemDao.getListByHQLWithNamedParams(hql1,params);  //具体货物详情

        map.put("financeReimburseBill",financeReimburseBill);
        map.put("billPictures",billPictures);
        map.put("financeReimburseBillItems",financeReimburseBillItems);
        return map;
    }

    /**
     * 财务出纳-待认证 确定审批
     * @param approvalStatus  1-认证已通过 2-认证未通过，不抵扣，仅报销  3-此张发票认证失败，换了新的发票
     * @param bills  处理的数据
     * @return
     */
    @Override
    public Map<String,Object> authApproval(String approvalStatus, String bills) {
        Map<String,Object> map = new HashMap<>();
        if (!MyStrings.nulltoempty(approvalStatus).isEmpty()&&!MyStrings.nulltoempty(bills).isEmpty()){
            JSONArray billArray = JSONArray.fromObject(bills);
            List billList = JSONArray.toList(billArray);
            Integer oid = null;
            Integer num = 0;
            if (billList!=null&&billList.size()>0){
                List<Map<String,Object>> listMap = new ArrayList<>();
                for (int i = 0; i < billList.size(); i++) {
                    JSONObject jo = JSONObject.fromObject(billList.get(i));
                    Integer billId = jo.getInt("billId");  //报销票据id
                    String billNo = jo.getString("billNo");  //票据号码
                    String issueDate = jo.getString("issueDate");  //开票时间

                    FinanceReimburseBill financeReimburseBill = financeReimburseBillDao.get(billId);
                    if (financeReimburseBill!=null ) {
                        oid = financeReimburseBill.getReimburse().getUser().getOid();  //下面的推送部门用到
                        financeReimburseBill.setCertificationState(approvalStatus); //认证状态 0-未认证 1-认证通过，2-认证失败（认证未通过，不抵扣，仅报销），3-无需认证
                        if ("3".equals(approvalStatus)) {
                            if (!MyStrings.nulltoempty(billNo).isEmpty()) {
                                financeReimburseBill.setBillNo(billNo);
                            }
                            if (!MyStrings.nulltoempty(issueDate).isEmpty()) {
                                try {
                                    financeReimburseBill.setIssueDate(new SimpleDateFormat("yyyy-MM-dd").parse(issueDate));
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                        financeReimburseBillDao.update(financeReimburseBill);

                        List<FinanceReimburseBill> financeReimburseBills = authInvoiceDetail(financeReimburseBill.getReimburseId(),financeReimburseBill.getBillCat(),"0"); //一个报销申请中是否已全部认证
                        if (financeReimburseBills.size()==0) { //已全部认证，或者全部无需认证(票据种类不是增值税专用发票)
                            PersonnelReimburse personnelReimburse = financeReimburseBill.getReimburse();
                            AccountDetail accountDetail = dataService.getDetailByReimburse(personnelReimburse.getId());
                            reimburseRelevanceService.autoMatchSubject(personnelReimburse.getUser().getOid(), personnelReimburse, accountDetail.getAccountId());
                        }
                        Map<String,Object> map1 = new HashMap<>();
                        map1.put("id",financeReimburseBill.getId());  //报销票据id
                        map1.put("billNo",financeReimburseBill.getBillNo());  //发票号码
                        map1.put("itemAmount",financeReimburseBill.getItemAmount());   //单张票面金额
                        map1.put("billCat",financeReimburseBill.getBillCat());   //票据种类id
                        listMap.add(map);
                    }
                    num++;

                }

                if (oid!=null) {
                    //推送相关信息
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(oid, "lp");  //财务为权限审批
                    for (UserPopedom u : userPopedomList) {   //已全部认证完后，角标减去
                        //财务审批人待认证减角标、减数据
                        this.reimburseRejectSendList(-num, -num, listMap, u.getUserId(), "/cashierAuthentication", null, null, "ticketAuthentication");
                    }
                }

                map.put("status",1);
                map.put("content","操作成功");
            }else {
                map.put("status",0);
                map.put("content","操作失败");
            }
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return map;
    }

    /**
     *
     * @param userId  审批人id
     * @param approvalStatus  审批状态
     * @param oid  机构
     * @param mid  菜单id（"lp","ln"等）
     * @return
     */
    @Override
    public Integer getPersonnelReimburseNum(Integer userId, String approvalStatus,Integer oid,String mid) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select count(id) from ApprovalProcess where org=:oid";
        params.put("oid",oid);
        if (userId!=null){
            hql+=" and toUser=:userId";
            params.put("userId",userId);
        }

        if (!MyStrings.nulltoempty(approvalStatus).isEmpty()){
            String[] object = approvalStatus.split(",");  //转化为数组
            hql+=" and approveStatus in (:approveStatus)";
            params.put("approveStatus",object);
        }

        if (!MyStrings.nulltoempty(mid).isEmpty()){
            hql+=" and toMid=:mid";
            params.put("mid",mid);
        }
        hql+=" and businessType is null and reimburse_ is not null";
        Long num = (Long) approvalProcessDao.getByHQLWithNamedParams(hql,params);
        return num.intValue();
    }

    @Override
    public Integer getPersonnelReimburseAuthNum(Integer oid) {
        Code code = codeService.getCodeByName(oid,"增值税专用发票");
        Integer num = 0;
        if (code!=null) {
            Map<String, Object> params = new HashMap<>();
//            String hql = "from FinanceReimburseBill where reimburse.user.oid=:oid and certificationState='0' and reimburse.approveStatus=2 and billCat=:codeId group by reimburseId";
            String hql = "from FinanceReimburseBill where reimburse.user.oid=:oid and certificationState='0' and reimburse.approveStatus=2 and billCat=:codeId";
            params.put("oid", oid);
            params.put("codeId", code.getId());
            List<FinanceReimburseBill> financeReimburseBills = financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
            num = financeReimburseBills.size();
        }

        return num;
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=0;
        Integer number1=0;
        Integer number2=0;
        switch (code){
            case "approvalReimburse": //报销审批
                number = this.getPersonnelReimburseNum(user.getUserID(), "1", user.getOid(), null);  //报销审批人
                break;
            case "approvalCashier": //出纳
                if ("finance".equals(user.getManagerCode())) {
                    number1 = this.getPersonnelReimburseNum(null, "4", user.getOid(), "lp");  //出纳-个人报销-待处理、待两讫
//                    number2 = this.getPersonnelReimburseAuthNum(user.getOid());//出纳-个人报销-待认证
                    number2 = paymentApprovalService.cashierPaymentHandle(user.getOid(),"1",21).size();  //财务可付款

                    number = number1 + number2;
                }
                break;
            case "financeReimburse"://个人报销
                if ("finance".equals(user.getManagerCode())) {
                    number1 = this.getPersonnelReimburseNum(null, "4", user.getOid(), "lp");  //出纳-个人报销-待处理、待两讫
//                    number2 = this.getPersonnelReimburseAuthNum(user.getOid());//出纳-个人报销-待认证
                    number2 = paymentApprovalService.cashierPaymentHandle(user.getOid(),"1",21).size();  //财务可付款
                    number = number1 + number2;
                }
                break;
            case "ticketAuthentication"://增票认证
                if ("finance".equals(user.getManagerCode())) {
                    number = this.getPersonnelReimburseAuthNum(user.getOid());//增票认证
                }
                break;
            case "reimburseBillVerification"://出纳-个人报销的票据审核
                if ("finance".equals(user.getManagerCode())) {
                    number = this.verificationBillsNum(user.getOid());//出纳-个人报销的票据审核
                }
                break;
        }

        return number;
    }

    public Integer verificationBillsNum(Integer org) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id) from PersonnelReimburse where approveStatus='1' and id in (select reimburse_ from ApprovalProcess where approveStatus='1' and businessType in (32,33) and org=:org)";
        map.put("org",org);
        Long num = (Long) personnelReimburseDao.getByHQLWithNamedParams(hql,map);
        return num==null?0:num.intValue();
    }

    private void sort(List<Map<String,Object>> listMap){
        Collections.sort(listMap, new Comparator<Map<String,Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                BigDecimal totalAmount1 = (BigDecimal) o1.get("totalAmount")==null?new BigDecimal(0):(BigDecimal)o1.get("totalAmount");
                BigDecimal totalAmount2 = (BigDecimal) o2.get("totalAmount")==null?new BigDecimal(0):(BigDecimal)o2.get("totalAmount");
                return totalAmount2.intValue()-totalAmount1.intValue();
            }

        });
    }

    @Override
    public Map<String, Object> getReimburseInfo(Integer reimburseId) {
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> listMap = new ArrayList<>();
        PersonnelReimburse personnelReimburse=personnelReimburseDao.get(reimburseId);//详情
        List<ApprovalProcess> processList=approvalProcessService.getApprovalProcessByPersonnelReimburseId(reimburseId);//审批流程

        List<FinanceReimburseBill> bills = getReimburseBillByReimburseId(reimburseId,0);
        for (FinanceReimburseBill financeReimburseBill:bills) {
            Map<String,Object> params = new HashMap<>();
            String hql = "select primaryBill,billCatName,itemCount,sum(amount),sum(billAmount),sum(relativeBillQuantity),price from FinanceReimburseBill " +
                    "where primaryBill=:primaryBill and relativeBillQuantity!=0 group by primaryBill";
            params.put("primaryBill",financeReimburseBill.getId());
            Object[] object = (Object[]) financeReimburseBillDao.getByHQLWithNamedParams(hql,params);
            Map<String, Object> map1 = new HashMap<>();
            if (object!=null) {
                map1.put("reimburseBillId", object[0]);  //第一个的票据id
                map1.put("billCatName", object[1]);   //票据种类名称
                map1.put("itemCount", object[2]);   //是否为多行
                BigDecimal amount1 = (BigDecimal) object[3];
                map1.put("amount", financeReimburseBill.getAmount().add(new BigDecimal(amount1.doubleValue())));   //金额
                BigDecimal billAmount1 = (BigDecimal) object[4];
                map1.put("billAmount", financeReimburseBill.getBillAmount().add(new BigDecimal(billAmount1.doubleValue())));   //票面金额
                Long quantity = (Long) object[5];
                map1.put("num", financeReimburseBill.getRelativeBillQuantity() + quantity.intValue());   //张数
                map1.put("price", object[6]);   //单价
            }else {
                map1.put("reimburseBillId", financeReimburseBill.getId());  //第一个的票据id
                map1.put("billCatName", financeReimburseBill.getBillCatName());   //票据种类名称
                map1.put("itemCount", financeReimburseBill.getItemCount());   //是否为多行
                map1.put("amount", financeReimburseBill.getAmount());   //金额
                map1.put("billAmount", financeReimburseBill.getBillAmount());   //票面金额
                map1.put("num", financeReimburseBill.getRelativeBillQuantity());   //张数
                map1.put("price", financeReimburseBill.getPrice());   //单价
            }
            List<FinanceReimburseBillAttachment> financeReimburseBillAttachments = financeReimburseBillAttachmentService.getFinanceReimburseBillAttachmentListByBillId(financeReimburseBill.getId(),1);
            map1.put("pictures",financeReimburseBillAttachments);
            listMap.add(map1);
        }
        map.put("personnelReimburse",personnelReimburse);
        map.put("processList",processList);
        map.put("billList",listMap);
        return map;
    }

    //报销进入财务的操作（报销入账）【此接口是从报销待两讫入账的接口改过来的】  type 1-待复核的修改  2-待付款的修改 3-待付款的最后确定 4-可付款直接现金结算 5-无需付款复核的（可付款除现金外的直接进账的）
    @Override
    public Map<String,Object> reimbursementEntry(PersonnelReimburse personnelReimburse,User user,String payMethod,String summary,Date factDate,Integer financeAccountId,ApprovalProcess approvalProcess,Integer type){
        Map<String,Object> map = new HashMap<>();
        FinanceAccount f = new FinanceAccount();
        if (financeAccountId != null && financeAccountId != 0) {
            f = accountService.getFinanceAccountById(financeAccountId);  //银行转账的账户
        } else {
            f = accountService.getFinanceAccountByOidAndType(user.getOid(), 1);//  现金/备用金
        }
        Double newBalance = f.getBalance().subtract(personnelReimburse.getAmount()).doubleValue(); //新的余额

        if (newBalance < 0) {
            map.put("status",3);//账户余额不足
            map.put("content","账户余额不足");
        } else if ("2".equals(personnelReimburse.getApproveStatus()) || "2".equals(approvalProcess.getApproveStatus()) || "5".equals(approvalProcess.getApproveStatus())) {
            map.put("status",2);//已批准 ，不与重复批准
            map.put("content","不可重复操作");
        } else {

            Integer approvalUserId = approvalProcess.getToUser();  //原本的审批人（修改时-推送角标用）

            approvalProcess.setApproveStatus("5");//批准报销
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setToUser(user.getUserID());
            approvalProcess.setBusinessType(null);
            if (3!=type&&5!=type) {
                approvalProcess.setCreateDate(new Date());
            }
            approvalProcessDao.update(approvalProcess);

            personnelReimburse.setAccount(f.getAccount());
            personnelReimburse.setPayMethod(payMethod);
            personnelReimburse.setApproveStatus("2");
            personnelReimburse.setAuditDate(new Date());
            personnelReimburse.setAuditor(user.getUserID());
            personnelReimburse.setAuditorName(user.getUserName());
            personnelReimburse.setSummary(summary);  //摘要

            //生成财务明细
            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setDebit(personnelReimburse.getAmount());//合计报销金额 录入支出
            accountDetail.setBalance(new BigDecimal(newBalance));
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setCreateDate(new Date());
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setSummary(summary);
            accountDetail.setPurpose(personnelReimburse.getPurpose());
            accountDetail.setMemo(personnelReimburse.getMemo());
            accountDetail.setAuditorName(user.getUserName());
            accountDetail.setPersonnelReimburse(personnelReimburse);
            accountDetail.setReimburseId(personnelReimburse.getId());
            accountDetail.setBillAmount(personnelReimburse.getBillAmount());
            accountDetail.setModityStatus("2");//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
            if ("1".equals(payMethod)) {
                accountDetail.setMethod("1");//现金
            } else if ("3".equals(payMethod)) {
                accountDetail.setMethod("5");//银行转账
            }
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
            accountDetail.setFactDate(factDate);
            accountDetail.setBillDate(new Date());
            if (personnelReimburse.getFactUser()!=null){
                if (personnelReimburse.getFactUser()==0){
                    accountDetail.setSubType("1");
                }else {
                    accountDetail.setSubType("2");   //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出
                }
            }

            //更新账户
            f.setDebit(f.getDebit().add(personnelReimburse.getAmount()));//总支出加上
            f.setBalance(new BigDecimal(newBalance));//在余额中减去
            accountService.updateFinanceAccount(f);

            //月结
            AccountPeriod yue = accountService.getAccountPeriodByMonth(f.getId(), new Date());
            yue.setDebit(yue.getDebit().add(personnelReimburse.getAmount()));
            yue.setBalance(yue.getBalance().subtract(personnelReimburse.getAmount()));
            accountService.updateAccountPeroid(yue);

            //日结
            AccountPeriod ri = accountService.getAccountPeriodByDay(f.getId(), new Date());
            ri.setDebit(ri.getDebit().add(personnelReimburse.getAmount()));
            ri.setBalance(ri.getBalance().subtract(personnelReimburse.getAmount()));
            accountService.updateAccountPeroid(ri);

            accountDetail.setFid(f.getId().toString());
            accountDetail.setAccountId(f);
            accountDetail.setAccount(ri.getId().toString());
            accountDetail.setSource("1");
            accountService.saveAccountDetail(accountDetail);

            personnelReimburseDao.update(personnelReimburse);

            //整理成listMap推送给财务增票认证
            List<Map<String, Object>> listMap = new ArrayList<>();
            Integer num = 0;
            Code code = codeService.getCodeByName(user.getOid(), "增值税专用发票");
            Set<FinanceReimburseBill> financeReimburseBills = personnelReimburse.getFinanceReimburseBillHashSet();
            for (FinanceReimburseBill financeReimburseBill : financeReimburseBills) {
                if (code.getId().equals(financeReimburseBill.getBillCat())) {
                    num += 1;
                    Map<String,Object> map1 = new HashMap<>();
                    map1.put("id",financeReimburseBill.getId());  //报销票据id
                    map1.put("billNo",financeReimburseBill.getBillNo());  //发票号码
                    map1.put("itemAmount",financeReimburseBill.getItemAmount());   //单张票面金额
                    map1.put("billCat",financeReimburseBill.getBillCat());   //票据种类id
                    listMap.add(map);
                }
            }

            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
            for (UserPopedom u : userPopedomList) {
                if (4==type&&"1".equals(payMethod)) {   //现金从可付款直接进账
                    //财务审批人可付款审批-1
                    this.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/financePayable", null, null, "financeReimburse");
                }else if ((2==type&&"1".equals(payMethod))||(3==type && "3".equals(payMethod))) {   //type=2(从财务待付款进来的修改为现金)  type=3(银行转账的在待付款确认后进账)
                    //财务审批人待付款-1
                    this.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/cashierTwoSettled", null, null, "financeReimburse");
                }else if (1==type && "1".equals(payMethod)){  //从待复核进入的现金
                    //财务审批人待复核-1
                    this.reimburseRejectSend(0, -1, personnelReimburse, u.getUserId(), "/financeReview", null, null, "financeReimburse");
                }
                if (num != 0) {
                    //财务增票认证审批人加角标、加数据
                    this.reimburseRejectSendList(num, num, listMap, u.getUserId(), "/cashierAuthentication", null, null, "ticketAuthentication");
                }
            }

            if (num==0){  //无需认证的系统自动选择科目
                reimburseRelevanceService.autoMatchSubject(personnelReimburse.getUser().getOid(), personnelReimburse, f);
            }

            //无论那阶段审批通过，都需给历史审批人已批准中减数据
            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByPersonnelReimburseId(personnelReimburse.getId());
            for (ApprovalProcess appProcess:approvalProcessList) {
                if (!"lp".equals(appProcess.getToMid())){
                    //报销审批历史审批人已批准中减数据
                    if (appProcess.getBusinessType()==null) {
                        this.reimburseRejectSend(0, -1, personnelReimburse, appProcess.getToUser(), "/reimbursesApproval", null, null, "approvalReimburse");
                    }else if (17==appProcess.getBusinessType()){   //待付款审批-已批准
                        this.reimburseRejectSend(0, -1, personnelReimburse, appProcess.getToUser(), "/paymentApproval", null, null, "personalReimburseApproval");
                    }else if (20==appProcess.getBusinessType()){
                        //付款复核-已批准
                        this.reimburseRejectSend(0, -1, personnelReimburse, appProcess.getToUser(), "/paymentReviewApproval", null, null, "reviewFinanceReimburse");
                    }
                }
            }

            //当修改为现金的时候
            if (1==type&&"1".equals(payMethod)){  //待复核修改为现金的--待复核审批人的待处理要减角标减数据
                //付款复核-待处理
                this.reimburseRejectSend(-1, -1, personnelReimburse, approvalUserId, "/paymentReviewHandle", null, null, "reviewFinanceReimburse");
            }

            if (personnelReimburse.getFactUser()!=null&&personnelReimburse.getFactUser()!=0) {
                // 批准给申请人发消息
                userSuspendMsgService.saveUserSuspendMsg(1, "钱票两讫，本次报销已完成！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), personnelReimburse.getFactUser(), "applyReimburseDetail", personnelReimburse.getId());  //给前端要查看详情的链接

                //申请人待处理-1
                this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getFactUser(), "/personnelReimbursePend", "申请被批准", "申请被批准", "applyReimburse");
            }else {
                // 批准给申请人发消息
                userSuspendMsgService.saveUserSuspendMsg(1, "钱票两讫，本次报销已完成！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), personnelReimburse.getUser_(), "applyReimburseDetail", personnelReimburse.getId());  //给前端要查看详情的链接

                //申请人待处理-1
                this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getUser_(), "/personnelReimbursePend", "申请被批准", "申请被批准", "applyReimburse");
            }
            map.put("status",1);
            map.put("content","操作成功");
        }
        return map;
    }

    /**
     * 查询某次报销中的所有增专发票
     *  @param oid 机构id
     * @param beginDate  开始时间
     * @param endDate  结束时间
     * @param certificationState  认证状态 0-未认证 1-认证通过，2-认证失败（认证未通过，不抵扣，仅报销），3-无需认证
     * @return
     */
    @Override
    public List<FinanceReimburseBill> getAuthenticationQuery(Integer oid,Date beginDate,Date endDate,String certificationState) {
        Code code = codeService.getCodeByName(oid,"增值税专用发票");
        Map<String,Object> params = new HashMap<>();
        String hql="from FinanceReimburseBill where billCat=:billCat and reimburse.approveStatus='2' ";
        params.put("billCat",code.getId());
//        params.put("oid",oid);
        if (!MyStrings.nulltoempty(certificationState).isEmpty()){
            hql+=" and certificationState=:certificationState";
            params.put("certificationState",certificationState);
        }else {
            hql+=" and certificationState='1'";
        }
        if (beginDate!=null && endDate!=null){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
            params.put("beginDate",beginDate);
            params.put("endDate",endDate);
        }
        List<FinanceReimburseBill> financeReimburseBills = financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
        return financeReimburseBills;
    }

    /**
     * 待在线审核的审批
     * @param userId  登陆id
     * @param approvalProcessId  审批流程id
     * @param approveStatus 2-审批通过  3-驳回
     * @param reason 驳回理由（其他的时候录入的）
     * @param approveSelect 驳回理由(数字以逗号分隔)
     * @return
     */
    @Override
    public Map<String, Object> onlineAuditApproval(Integer userId, Integer approvalProcessId, String approveStatus,String reason,String approveSelect) {
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null&&userId!=null&&!MyStrings.nulltoempty(approveStatus).isEmpty()) {
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (!"1".equals(approvalProcess.getApproveStatus())) {
                map.put("status", 2);  //已批准 ，不与重复批准
                map.put("content", "不可重复操作");
            } else {
                PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse_());
                if ("2".equals(approveStatus)) {  //  1-批准
                    personnelReimburse.setApproveLevel(approvalProcess.getLevel());

                    approvalProcess.setApproveStatus(approveStatus);  // 2-批准
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcess.setToUser(user.getUserID());
                    approvalProcessDao.update(approvalProcess);

                    //以下为报销审批的基本流程
                    ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "reimburseApply");
                    if (personnelReimburse.getAmount().doubleValue() <= approvalItem.getUpperLimit().doubleValue() || approvalItem.getUpperLimit().compareTo(new BigDecimal(-1)) == 0) {
                        personnelReimburse.setApproveItem(approvalItem.getId());//申批项目 1-财务  2-加班  3-请假

                        Integer handleId = null;
                        String handleName = "";//审批人总称
                        String userName = "";//审批人名字
                        Integer uid = null;
                        for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
                            if (f.getLevel() == 1) {
                                uid = f.getToUserId();
                                handleName = f.getToUser();
                                userName = f.getUserName();
                            }
                        }
                        if (uid == 0) {
                            handleId = Integer.decode(user.getLeader());//直属上级
                            userName = userDao.get(Integer.decode(user.getLeader())).getUserName();
                        } else {
                            handleId = uid;//指定的人
                        }
                        ApprovalProcess app = new ApprovalProcess();
                        app.setLevel(1);
                        app.setApproveStatus("1");
                        app.setToUser(handleId);
                        app.setReimburse(personnelReimburse);
                        app.setToUserName(handleName);//审批人总称
                        app.setUserName(userName);//审批人名称
                        app.setCreateDate(new Date());
                        app.setOrg(user.getOid());
                        app.setFromUser(personnelReimburse.getUser_());
                        approvalProcessService.saveApprovalProcess(app);

                        String content = this.pushContent(personnelReimburse,1);
                        this.reimburseRejectSend(1, 1, personnelReimburse, handleId, "/reimburseHandle", content, content, "approvalReimburse");//报销审批人-待处理（审批设置）
                    }
                    //                //给申请人的详情页发送(状态已变更)
//                personnelReimburseService.reimburseRejectSend(0, 0, personnelReimburse, approvalProcess.getFromUser(), "/paymentDetail",null,null,"approvalSettingsApply");
                this.reimburseRejectSend(0, 0, personnelReimburse, approvalProcess.getFromUser(), "/applyReimburseDetail",null,null,"applyReimburse");

                } else {  // 3-驳回
                    approvalProcess.setApproveStatus(approveStatus);  //3-在线审批驳回
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcess.setReason(reason);
                    approvalProcess.setToUser(userId);
                    approvalProcess.setApproveSelect(approveSelect); //1-照片不清楚，或信息被遮挡，以至于无法审核 2-无法入会计帐的票据较多 3-包含公司不允许报销的票据  4-票据内容与所录入的信息不一致  5-其他原因

                    personnelReimburse.setApproveMemo(approveSelect);
                    personnelReimburse.setApproveStatus("3");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    if (personnelReimburse.getFactUser()!=null&&personnelReimburse.getFactUser()!=0) {
                        //申请人待处理-1
                        this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getFactUser(), "/personnelReimbursePend", "申请被驳回", "申请被驳回", "applyReimburse");

                        // 驳回给申请人发消息
                        userSuspendMsgService.saveUserSuspendMsg(1, "您的报销申请被驳回了！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), personnelReimburse.getFactUser(), "applyReimburseDetail", personnelReimburse.getId());  //给前端要查看详情的链接
                    }else {
                        //申请人待处理-1
                        this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getUser_(), "/personnelReimbursePend", "申请被驳回", "申请被驳回", "applyReimburse");

                        // 驳回给申请人发消息
                        userSuspendMsgService.saveUserSuspendMsg(1, "您的报销申请被驳回了！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), personnelReimburse.getUser_(), "applyReimburseDetail", personnelReimburse.getId());  //给前端要查看详情的链接
                    }
                }
                personnelReimburseDao.update(personnelReimburse);

                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                for (UserPopedom u : userPopedomList) {
                    //财务待在线审核审批人待处理-1
                    this.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/onlineAuditHandle", null, null, "reimburseBillVerification");
                }
                map.put("status", 1);
                map.put("content", "操作成功");
            }
        }else{
            map.put("status", 0);
            map.put("content", "操作失败");
        }

        return map;
    }

    /**
     * 待线下审核的审批
     * @param userId  登陆id
     * @param approvalProcessId  审批流程id
     * @param approveStatus 2-审批通过  3-驳回
     * @return
     */
    @Override
    public Map<String, Object> offlineAuditApproval(Integer userId, Integer approvalProcessId, String approveStatus) {
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null&&userId!=null&&!MyStrings.nulltoempty(approveStatus).isEmpty()) {
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (!"1".equals(approvalProcess.getApproveStatus())) {
                map.put("status", 2);  //已批准 ，不与重复批准
                map.put("content", "不可重复操作");
            } else {
                PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse_());
                ApprovalItem approvalItem = new ApprovalItem();
                if (personnelReimburse.getInstance()!=null) {
                    approvalItem = approvalItemDao.get(personnelReimburse.getInstance());  //查询当前使用的付款审批(暂时默认一级)
                }else {    //老数据的没有存这个id
                    approvalItem = roleService.getCurrentItem(user.getOid(), "paymentApproval");  //查询当前使用的付款审批(暂时默认一级)
                    personnelReimburse.setInstance(approvalItem.getId());
                    if (personnelReimburse.getAuditInstance()==null){   //复核的审批流程
                        ApprovalItem approvalItem1 = roleService.getApprovalItemByPreviousItem(approvalItem.getId());
                        personnelReimburse.setAuditInstance(approvalItem1.getId());
                    }
                }

                if ("2".equals(approveStatus)) {  //  1-批准
                    personnelReimburse.setApproveLevel(approvalProcess.getLevel());

                    approvalProcess.setApproveStatus(approveStatus);  // 2-批准
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcess.setToUser(user.getUserID());
                    approvalProcessDao.update(approvalProcess);

                    if (1==approvalItem.getStatus()) {  //需要付款审批的
                        ApprovalProcess process = new ApprovalProcess();
                        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process.setLevel(1);
                        process.setToUser(approvalItem.getApprovalFlowHashSet().get(0).getToUserId());  //审批人id
                        process.setToUserName(approvalItem.getApprovalFlowHashSet().get(0).getToUser());  //审批人总称
                        process.setUserName(approvalItem.getApprovalFlowHashSet().get(0).getUserName()); //审批人名称
                        process.setReimburse(personnelReimburse);
                        process.setCreateDate(new Date());
                        process.setOrg(user.getOid());
                        process.setFromUser(approvalProcess.getFromUser());
                        process.setBusiness(personnelReimburse.getId());
                        process.setBusinessType(17);
                        approvalProcessService.saveApprovalProcess(process);

                        //付款审批人待处理+1
                        String content = this.pushContent(personnelReimburse,3);
                        this.reimburseRejectSend(1, 1, personnelReimburse, approvalItem.getApprovalFlowHashSet().get(0).getToUserId(), "/paymentHandle", content, content, "personalReimburseApproval");
                    }else {  //无需付款审批的，直接进入出纳-可付款

                          //财务-可付款的流程（未确定付款方式时）
                        ApprovalProcess process = new ApprovalProcess();
                        process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process.setLevel(approvalProcess.getLevel());
                        process.setToUserName("财务处理者");
                        process.setToMid("lp");  //报销受理模块
                        process.setReimburse(approvalProcess.getReimburse());
                        process.setCreateDate(new Date());
                        process.setOrg(user.getOid());
                        process.setUserName("财务处理者");
                        process.setFromUser(personnelReimburse.getUser_());
                        process.setHandleTime(new Date());
                        process.setBusinessType(21);
                        approvalProcessDao.save(process);
                    }
                } else {  // 3-驳回
                    approvalProcess.setApproveStatus("3");  //8-财务驳回
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcess.setApproveMemo("实际票据与在线审核通过的不一致。");
                    personnelReimburse.setApproveMemo("实际票据与在线审核通过的不一致。");
                    approvalProcess.setReason("实际票据与在线审核通过的不一致。");

                    approvalProcess.setToUser(userId);

                    personnelReimburse.setApproveStatus("3");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByPersonnelReimburseId(personnelReimburse.getId());
                    for (ApprovalProcess appProcess:approvalProcessList) {
                        if (!approvalProcess.getId().equals(appProcess.getId())){
                            //历史审批人已批准中减数据
                            this.reimburseRejectSend(0,-1,personnelReimburse, appProcess.getToUser(), "/reimbursesApproval",null,null,"approvalReimburse");
                        }
                    }

                    if (personnelReimburse.getFactUser()!=null&&personnelReimburse.getFactUser()!=0){

                        //申请人待处理-1
                        this.reimburseRejectSend(0,-1,personnelReimburse, personnelReimburse.getFactUser(), "/personnelReimbursePend","申请被驳回","申请被驳回","applyReimburse");

                        // 驳回给申请人发消息
                        userSuspendMsgService.saveUserSuspendMsg(1, "您的报销申请被驳回了！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) , personnelReimburse.getFactUser(),"applyReimburseDetail",personnelReimburse.getId());  //给前端要查看详情的链接
                    }else {

                        //申请人待处理-1
                        this.reimburseRejectSend(0, -1, personnelReimburse, personnelReimburse.getUser_(), "/personnelReimbursePend", "申请被驳回", "申请被驳回", "applyReimburse");

                        // 驳回给申请人发消息
                        userSuspendMsgService.saveUserSuspendMsg(1, "您的报销申请被驳回了！", "审批时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), personnelReimburse.getUser_(), "applyReimburseDetail", personnelReimburse.getId());  //给前端要查看详情的链接
                    }
                }
                personnelReimburseDao.update(personnelReimburse);

                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                for (UserPopedom u : userPopedomList) {
                    //财务待线下审核审批人-1
                    this.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/offlineAuditHandle", null, null, "reimburseBillVerification");

                    if ("2".equals(approveStatus)&&1==approvalItem.getStatus()){  //审批通过，且需要付款审批的才进入待付款
                        //财务审批人待付款审批列表数据加1，角标没有
                        this.reimburseRejectSend(0,1,personnelReimburse, u.getUserId(), "/cashierPaymentHandle",null,null,"financeReimburse");
                    }else if ("2".equals(approveStatus)&&0==approvalItem.getStatus()){
                        //财务审批人可付款+1
                        String content = this.pushContent(personnelReimburse,3);
                        this.reimburseRejectSend(1,1,personnelReimburse, u.getUserId(), "/financePayable",content,content,"financeReimburse");
                    }
                }
                map.put("status", 1);
                map.put("content", "操作成功");
            }
        }else{
            map.put("status", 0);
            map.put("content", "操作失败");
        }
        return map;
    }

    @Override
    public List<PersonnelReimburse> verificationBillsQuery(Integer userId, Date begin, Date end, String approveStatus, Integer applyId) {
        Map<String,Object> map = new HashMap<>();
        User user = userDao.get(userId);
        String hql = "from PersonnelReimburse where approveStatus='3' and id in (select reimburse_ from ApprovalProcess where approveStatus='3'";
        if (userId!=null && !"2".equals(approveStatus)){
            hql+=" and toUser=:userId";
            map.put("userId",userId);
        }
        if (!MyStrings.nulltoempty(approveStatus).isEmpty()){
            if ("1".equals(approveStatus)){  //1-票据在线审批未通过 2-审批流程被驳回 3-线下票据不一致
                hql+=" and businessType=32";
            }else if ("2".equals(approveStatus)){
                hql+=" and businessType is null and reimburse_ is not null and org=:org";
                map.put("org",user.getOid());
            }else if ("3".equals(approveStatus)){
                hql+=" and businessType=33";
            }
        }
        if (applyId!=null){
            hql+=" and fromUser=:applyId";
            map.put("applyId",applyId);
        }
        hql+=")";
        if (begin!=null&&end!=null){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
            map.put("beginDate",begin);
            map.put("endDate",end);
        }
        hql+=" order by createDate desc";
        List<PersonnelReimburse> personnelReimburses = personnelReimburseDao.getListByHQLWithNamedParams(hql,map);
        return personnelReimburses;
    }

    @Override
    public List<PersonnelReimburse> getOnlineAuditOK(Integer oid, String approveStatus) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from PersonnelReimburse where approveStatus='1' and id in (select reimburse_ from ApprovalProcess where org=:org " +
                "and (approveStatus='1' or approveStatus='2') and businessType is null and reimburse_ is not null) " +
                "and id not in (select reimburse_ from ApprovalProcess where org=:org and businessType=33)";//將报销本流程的审批完的排除
        params.put("org",oid);
        hql+=" order by createDate desc";
        return financeReimburseBillDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public CommonInvoiceCertification getCertificationByCode(String qrINfo) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from CommonInvoiceCertification where qrInfo=:qrInfo";
        map.put("qrInfo",qrINfo);
        CommonInvoiceCertification commonInvoiceCertification = (CommonInvoiceCertification) commonInvoiceCertificationDao.getByHQLWithNamedParams(hql,map);
        return commonInvoiceCertification;
    }

    /**
     * 添加发票相关信息
     * @param qrINfo 扫码二维码获取的
     * @param jsonResult 获取的具体的发票详情信息
     * @return
     */
    @Override
    public CommonInvoiceCertification addInvoiceCertification(String qrINfo,Map<String,Object> jsonResult) {
        CommonInvoiceCertification commonInvoiceCertification = new CommonInvoiceCertification();
        commonInvoiceCertification.setQrInfo(qrINfo);
        commonInvoiceCertification.setResponseData(jsonResult.toString()); //认证结果
        commonInvoiceCertification.setCallClass(PersonnelReimburseServiceImpl.class.getName());
        commonInvoiceCertification.setCreateDate(new Date());
        commonInvoiceCertificationDao.save(commonInvoiceCertification);
        return commonInvoiceCertification;
    }

    @Override    //type 1-申请 2-撤销
    public String pushContent(PersonnelReimburse personnelReimburse,Integer type){
        String askName = personnelReimburse.getCreateName();
        if (personnelReimburse.getFactUser()!=null&&personnelReimburse.getFactUser()!=0){
            User factUser = userDao.get(personnelReimburse.getFactUser());
            askName = factUser.getUserName();
        }
        String contentType = "";
        switch (type){
            case 1:
                contentType = "提交";
                break;
            case 2:
                contentType = "撤销";
            break;
        }
        String content = askName+contentType+"了报销申请";
        if (type!=null&&type==3){
            content = "有一条申请待审批";
        }
        return content;
    }
}
