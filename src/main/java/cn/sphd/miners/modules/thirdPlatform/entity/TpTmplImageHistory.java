package cn.sphd.miners.modules.thirdPlatform.entity;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-09-29 
 */

@Entity ( name ="cn.sphd.miners.modules.thirdPlatform.entity.TpTmplImageHistory" )
@Table ( name ="t_tp_tmpl_image_history" )
public class TpTmplImageHistory  implements Serializable {

	private static final long serialVersionUID =  8594334848857671329L;

	/**
	 * ID
	 */
	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 第三方模板ID
	 */
   	@Column(name = "app_tmpl" )
	private Integer appTmpl;

	/**
	 * 第三方模板历史表ID
	 */
   	@Column(name = "app_tmpl_history" )
	private Integer appTmplHistory;

	/**
	 * 模板图片库ID
	 */
   	@Column(name = "tmpl_image" )
	private Integer tmplImage;

	/**
	 * 标题
	 */
   	@Column(name = "title" )
	private String title;

	/**
	 * 类型:1-图片,2-视频,3-文档
	 */
   	@Column(name = "type" )
	private String type;

	/**
	 * 文件上传路径
	 */
   	@Column(name = "uplaod_path" )
	private String uplaodPath;

	/**
	 * 排序
	 */
   	@Column(name = "orders" )
	private Integer orders;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改;4-启停用
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getAppTmpl() {
		return this.appTmpl;
	}

	public void setAppTmpl(Integer appTmpl) {
		this.appTmpl = appTmpl;
	}

	public Integer getAppTmplHistory() {
		return this.appTmplHistory;
	}

	public void setAppTmplHistory(Integer appTmplHistory) {
		this.appTmplHistory = appTmplHistory;
	}

	public Integer getTmplImage() {
		return this.tmplImage;
	}

	public void setTmplImage(Integer tmplImage) {
		this.tmplImage = tmplImage;
	}

	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getUplaodPath() {
		return this.uplaodPath;
	}

	public void setUplaodPath(String uplaodPath) {
		this.uplaodPath = uplaodPath;
	}

	public Integer getOrders() {
		return this.orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
