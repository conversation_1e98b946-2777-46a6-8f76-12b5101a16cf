package cn.sphd.miners.modules.material.mapper;

import cn.sphd.miners.modules.material.entity.TSrmSupplierImport;

import java.util.List;

/**
 * 供应商管理_供应商导入信息
 * 20231108 1.267供应商之批量导入Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface TSrmSupplierImportMapper {
    /**
     * 查询供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param id 供应商管理_供应商导入信息
     *           20231108 1.267供应商之批量导入主键
     * @return 供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     */
    public TSrmSupplierImport selectTSrmSupplierImportById(Long id);

    /**
     * 查询供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入列表
     *
     * @param tSrmSupplierImport 供应商管理_供应商导入信息
     *                           20231108 1.267供应商之批量导入
     * @return 供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入集合
     */
    public List<TSrmSupplierImport> selectTSrmSupplierImportList(TSrmSupplierImport tSrmSupplierImport);

    /**
     * 新增供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param tSrmSupplierImport 供应商管理_供应商导入信息
     *                           20231108 1.267供应商之批量导入
     * @return 结果
     */
    public int insertTSrmSupplierImport(TSrmSupplierImport tSrmSupplierImport);

    /**
     * 修改供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param tSrmSupplierImport 供应商管理_供应商导入信息
     *                           20231108 1.267供应商之批量导入
     * @return 结果
     */
    public int updateTSrmSupplierImport(TSrmSupplierImport tSrmSupplierImport);

    /**
     * 删除供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param id 供应商管理_供应商导入信息
     *           20231108 1.267供应商之批量导入主键
     * @return 结果
     */
    public int deleteTSrmSupplierImportById(Long id);

    /**
     * 批量删除供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTSrmSupplierImportByIds(String[] ids);
}
