<template>
  <div id="approvalOverTimeQuery">
    <TY_NavTop title="筛选"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tip">您可按以下筛选条件，查询您经手审批的加班数据</div>
          <el-form :model="applyer" ref="queryForm" label-width="70px" class="educationForm">
            <el-form-item label="申请人">
              <el-select v-model="applyer" placeholder="选择申请人" size="small" @change=changeValue>
                <el-option
                  v-for="item in applyUserList"
                  :key="item.userID"
                  :label="item.userName"
                  :value="item.userID">
                </el-option>
              </el-select>
            </el-form-item>
            <h4 class="item-header">事件结果</h4>
            <el-radio-group v-model="queryForm.type">
              <el-radio :label="1">已完结的加班</el-radio>
              <el-radio :label="2">未被认可的加班</el-radio>
              <el-radio :label="3">提交了加班申请，但实际未加班</el-radio>
              <el-radio :label="4">无需再填报时长的加班</el-radio>
              <el-radio :label="5">加班申请被驳回</el-radio>
            </el-radio-group>
            <h4 class="item-header">发生时间</h4>
            <div class="el-form-item">
              <el-date-picker type="date" placeholder="开始日期" v-model="beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd" v-on:change="clearOther('date')"></el-date-picker> -
              <el-date-picker type="date" placeholder="结束日期" v-model="endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd" v-on:change="clearOther('date')"></el-date-picker>
            </div>
            <el-form-item label="仅看年报">
              <el-date-picker type="year" placeholder="选择年份" v-model="year" style="width: 100%;" size="small" value-format="yyyy" v-on:change="clearOther('year')"></el-date-picker>
            </el-form-item>
            <el-form-item label="仅看月报">
              <el-date-picker type="month" placeholder="选择月份" v-model="month" style="width: 100%;" size="small" v-on:change="clearOther('month')"></el-date-picker>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submitForm(1)" v-loading.fullscreen.lock="loading">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  #approvalOverTimeQuery .el-radio-group .el-radio{
    display: block;
    margin-top: 12px;
  }
  #approvalOverTimeQuery .el-radio + .el-radio{
    margin-left: 0;
  }
</style>

<script>
export default {
  name: 'approvalOverTimeQuery',
  data () {
    return {
      queryForm: {
        type: 1
      },
      applyUserList: [],
      year: '',
      month: '',
      beginDate: '',
      endDate: '',
      applyer: '',
      applyName: '',
      loading: false
    }
  },
  created () {
    let params = {
      userId: this.sphdSocket.user.userID,
      businessType: 2
    }
    let _this = this
    this.$http.post('../../../leaveAndOutTime/getApplyUserList.do', params, {
      emulateJSON: true
    }).then((response) => {
      let data = response.body
      if (data) {
        _this.applyUserList = data.data
        _this.applyUserList.unshift({
          userName: '全部',
          userID: ''
        })
        _this.appyer = ''
      } else {
        console.log('菜单加载失败！')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    submitForm: function () {
      // userId 登录人
      // state （1- 自定义 ，2 -仅看年报，3- 仅看月报）
      // approveStatus   2- 被批准的，3-被驳回的
      // beginDate 开始时间   格式举例： 2018-09-01   选择年报时需要拼成这个格式，月报同理
      // endDate 结束时间   格式举例： 2018-09-01
      this.loading = true
      this.queryForm.userId = this.sphdSocket.user.userID
      this.queryForm.applyUserId = this.applyer
      this.queryForm.applyUserName = this.applyerName
      if (this.year !== '') {
        this.queryForm.state = 2
        this.queryForm.beginDate = this.year + '-01-01'
        this.queryForm.endDate = this.year + '-12-31'
      } else if (this.month !== '') {
        this.queryForm.state = 3
        let date = new Date(this.month)
        console.log(date)
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let lastDayOfMonth = new Date(year, month, 0)
        this.queryForm.beginDate = year + '-' + month + '-01'
        this.queryForm.endDate = year + '-' + month + '-' + lastDayOfMonth.getDate()
      } else {
        this.queryForm.state = 1
        this.queryForm.beginDate = this.beginDate
        this.queryForm.endDate = this.endDate
      }
      if (this.queryForm.beginDate) {
        localStorage.setItem('query', JSON.stringify(this.queryForm))
        this.$router.push({
          path: '/approvalOverTimeQueryPer',
          name: 'approvalOverTimeQueryPer',
          query: {
            data: this.queryForm
          }
        })
      } else {
        this.loading = false
        this.$kiko_message('请选择查询时间')
      }
    },
    clearOther: function (type) {
      if (type === 'date') {
        this.year = ''
        this.month = ''
      } else if (type === 'year') {
        this.month = ''
        this.beginDate = ''
        this.endDate = ''
      } else if (type === 'month') {
        this.year = ''
        this.beginDate = ''
        this.endDate = ''
      }
    },
    changeValue: function (value) {
      let _this = this
      this.applyUserList.forEach(function (item) {
        if (item.userID === value) {
          _this.applyerName = item.userName
          console.log(_this.applyerName)
        }
      })
    }
  }
}
</script>
