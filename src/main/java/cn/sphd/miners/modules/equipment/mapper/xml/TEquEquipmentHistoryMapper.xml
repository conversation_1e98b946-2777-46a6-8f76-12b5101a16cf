<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquEquipmentHistoryMapper">
    
    <resultMap type="TEquEquipmentHistory" id="TEquEquipmentHistoryResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="equipment"    column="equipment"    />
        <result property="name"    column="name"    />
        <result property="fullName"    column="full_name"    />
        <result property="units"    column="units"    />
        <result property="modelCount"    column="model_count"    />
        <result property="supplierCount"    column="supplier_count"    />
        <result property="quantity"    column="quantity"    />
        <result property="memo"    column="memo"    />
        <result property="enabled"    column="enabled"    />
        <result property="enabledTime"    column="enabled_time"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTEquEquipmentHistoryVo">
        select id, org, equipment, name, full_name, units, model_count, supplier_count, quantity, memo, enabled, enabled_time, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_equ_equipment_history
    </sql>

    <select id="selectTEquEquipmentHistoryList" parameterType="TEquEquipmentHistory" resultMap="TEquEquipmentHistoryResult">
        <include refid="selectTEquEquipmentHistoryVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="equipment != null "> and equipment = #{equipment}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="fullName != null  and fullName != ''"> and full_name like concat('%', #{fullName}, '%')</if>
            <if test="units != null  and units != ''"> and units = #{units}</if>
            <if test="modelCount != null "> and model_count = #{modelCount}</if>
            <if test="supplierCount != null "> and supplier_count = #{supplierCount}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="enabledTime != null "> and enabled_time = #{enabledTime}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTEquEquipmentHistoryById" parameterType="Long" resultMap="TEquEquipmentHistoryResult">
        <include refid="selectTEquEquipmentHistoryVo"/>
        where id = #{id}
    </select>
    <select id="history" parameterType="Long" resultType="java.util.HashMap">
        select eeh.id,eeh.version_no as versionNo,eeh.full_name as fullName,eeh.create_name as createName,date_format(eeh.create_date,'%Y-%m-%d %H:%i:%s') as createDate from t_equ_equipment_history eeh where equipment = #{id} order by version_no desc
    </select>

    <insert id="insertTEquEquipmentHistory" parameterType="TEquEquipmentHistory" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_equipment_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="equipment != null">equipment,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="fullName != null">full_name,</if>
            <if test="units != null">units,</if>
            <if test="modelCount != null">model_count,</if>
            <if test="supplierCount != null">supplier_count,</if>
            <if test="quantity != null">quantity,</if>
            <if test="memo != null">memo,</if>
            <if test="enabled != null">enabled,</if>
            <if test="enabledTime != null">enabled_time,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="equipment != null">#{equipment},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="units != null">#{units},</if>
            <if test="modelCount != null">#{modelCount},</if>
            <if test="supplierCount != null">#{supplierCount},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="memo != null">#{memo},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="enabledTime != null">#{enabledTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTEquEquipmentHistory" parameterType="TEquEquipmentHistory">
        update t_equ_equipment_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="equipment != null">equipment = #{equipment},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="units != null">units = #{units},</if>
            <if test="modelCount != null">model_count = #{modelCount},</if>
            <if test="supplierCount != null">supplier_count = #{supplierCount},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="enabledTime != null">enabled_time = #{enabledTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquEquipmentHistoryById" parameterType="Long">
        delete from t_equ_equipment_history where id = #{id}
    </delete>

    <delete id="deleteTEquEquipmentHistoryByIds" parameterType="String">
        delete from t_equ_equipment_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>