<template>
  <div id="potentialContact" class="potential">
    <TY_NavTop title="潜在客户受理"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content">
        <div v-for="(item, index) in recordList" :key="index">
          <el-row v-if="index === 0">
            <el-col :span="8">原始信息</el-col>
            <el-col :span="16" class="createDetail">录入者：{{item.createName}}  {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</el-col>
          </el-row>
          <el-row v-if="index !== 0">
            <el-col :span="8">第{{index}}次修改后</el-col>
            <el-col :span="16" class="createDetail">修改者：{{item.updateName}}  {{item.updateDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</el-col>
          </el-row>
          <div class="clear">
            <div class="put-left">
              <el-image
                style="width: 80px; height: 80px"
                :src="item.imgPath"
                fit="contain"></el-image>
            </div>
            <div class="put-left otherInfo">
              <p>{{item.principalName}} {{item.departName}}</p>
              <p>{{item.mobile}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style>
  .sect-con .el-col{
    line-height: 32px;
  }
</style>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'potentialChangeRecord',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      recordList: []
    }
  },
  created () {
    let _this = this
    this.axios.post('../../../sale/replacePotentialPrincipalRecord.do', {
      customerId: _this.$route.params.id
    }).then(function (response) {
      _this.loading = false
      _this.recordList = response.data.list
    }).catch(function (error) {
      console.log(error)
    })
  }
}
</script>
