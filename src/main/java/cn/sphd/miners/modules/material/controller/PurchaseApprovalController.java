package cn.sphd.miners.modules.material.controller;


import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.system.dao.ApprovalFlowDao;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.math3.ode.FirstOrderDifferentialEquations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 孙文
 */
@Controller
@RequestMapping("/purchaseApproval")
public class PurchaseApprovalController {

    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserService userService;
    @Autowired
    RoleService roleService;

    /**
     * 修改审批设置申请
     */
    @RequestMapping("/purchaseApprovalApply.do")
    @ResponseBody
    public JsonResult purchaseApprovalApply(User user,String json) {
        JSONObject after=JSONObject.parseObject(json);
        //id
        Integer id= Integer.valueOf(after.getString("id"));


        List<ApprovalProcess> approvalProcessList=approvalProcessService.getApprovalProcessByBusiness(id,10,"1");
        if(approvalProcessList.size()>0){
            return new JsonResult(0,"","400","此修改申请正在申请中，不能再次提交！");
        }
        ApprovalItem item=approvalService.getById(id);
        JSONObject before=new JSONObject();
        //是否需要审批
        before.put("status",item.getStatus());
        //审批级次
        before.put("level",item.getLevel());
        //开始执行时间
        before.put("openDate",item.getOpenDate());

        JSONArray jsonArray=new JSONArray();
        //审批人
        //获取当前的修改
        List<ApprovalFlow> list=approvalService.approvalFlowList(id);

        for (ApprovalFlow af:list) {
            JSONObject afo=new JSONObject();
            afo.put("level",af.getLevel());
            afo.put("toUser",af.getToUser());
            afo.put("toUserId",af.getToUserId());
            afo.put("userName",af.getUserName());
            User u=userService.getUserByID(af.getToUserId());
            if(u!=null){
                afo.put("userName",u.getUserName());
                afo.put("mobile",u.getMobile());
            }else{
                afo.put("userName","");
                afo.put("mobile","");
            }
            jsonArray.add(afo);
        }
        before.put("userList",jsonArray);

        String res=roleService.updatePurchaseItemApply(user,after,before);
        if("success".equals(res)){
            return new JsonResult(1, null);
        }else{
            return new JsonResult(0,"","400",res);
        }

//        map = roleService.updateItemApply(userId,itemId,openDate,state,approvalFlows,paymentAuditState);
//        map.put("today", NewDateUtils.today());  //当前时间
//        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/updateItemApply",null,null,null,null,JSON.toJSONString(map));
//        return new JsonResult(1,map);
    }
    /**
     * 获取采购审批设置查看
     */
    @RequestMapping("/purchaseApprovalDetail.do")
    @ResponseBody
    public JsonResult purchaseApprovalDetail(User user,Integer id) {

        ApprovalProcess ap = approvalProcessService.getApprovalProcessById(id);

        JSONObject obj=JSONObject.parseObject(ap.getApproveData());
        obj.put("createName",ap.getAskName());// 审批人
        obj.put("createDate",ap.getCreateDate());//申请时间
        obj.put("userName",ap.getToUserName());//审批人
        obj.put("approvalId",ap.getId());
        return new JsonResult(1, obj);
    }

    /**
     * 采购设置审批
     */
    @RequestMapping("/purchaseApprovalApproval.do")
    @ResponseBody
    public JsonResult purchaseApprovalApproval(String json, User user) {
        JSONObject jsonObject = JSON.parseObject(json);
        Integer approvalId = jsonObject.getInteger("approvalId"); // 审批id
        String approveStatus = jsonObject.getString("approveStatus"); //审批结果
        String reason = jsonObject.getString("reason"); //审批原因

        Map<String, Object> map = new HashMap<>();

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);

        if (approvalProcess == null) {
            return new JsonResult(0,"","400","操作失败");
        }
        if (!"1".equals(approvalProcess.getApproveStatus())) {
            return new JsonResult(0,"","400","操作失败");
        }
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setReason(reason);
        approvalProcess.setHandleTime(new Date());

        String res = "";
        try {
            res = roleService.purchaseApprovalApproval(approvalProcess);
        } catch (RuntimeException e) {
            e.printStackTrace();
            res = e.getMessage();
        }

        if ("success".equals(res)) {
            map.put("status", 1);
            map.put("msg", "操作成功");
        } else {

            map.put("status", 0);
            map.put("msg", res);
        }

        System.out.println("执行完毕");
        return new JsonResult(1, map);
    }

}
