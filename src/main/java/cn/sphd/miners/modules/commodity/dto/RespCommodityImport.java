package cn.sphd.miners.modules.commodity.dto;

import cn.sphd.miners.modules.commodity.entity.PdCommodityImport;
import cn.sphd.miners.modules.commodity.entity.PdCommodityImportAttach;

import java.util.List;

public class RespCommodityImport extends PdCommodityImport {
    private List<PdCommodityImportAttach> pdCommodityImportAttachList;
    private String pdCommodityImportAttachListJson;
    private PdCommodityImportAttach video;
    private String deletePdCommodityImportAttachListJson;
    private String addPdCommodityImportAttachListJson;

    public String getAddPdCommodityImportAttachListJson() {
        return addPdCommodityImportAttachListJson;
    }

    public void setAddPdCommodityImportAttachListJson(String addPdCommodityImportAttachListJson) {
        this.addPdCommodityImportAttachListJson = addPdCommodityImportAttachListJson;
    }

    public String getDeletePdCommodityImportAttachListJson() {
        return deletePdCommodityImportAttachListJson;
    }

    public void setDeletePdCommodityImportAttachListJson(String deletePdCommodityImportAttachListJson) {
        this.deletePdCommodityImportAttachListJson = deletePdCommodityImportAttachListJson;
    }

    public PdCommodityImportAttach getVideo() {
        return video;
    }

    public void setVideo(PdCommodityImportAttach video) {
        this.video = video;
    }

    public List<PdCommodityImportAttach> getPdCommodityImportAttachList() {
        return pdCommodityImportAttachList;
    }

    public void setPdCommodityImportAttachList(List<PdCommodityImportAttach> pdCommodityImportAttachList) {
        this.pdCommodityImportAttachList = pdCommodityImportAttachList;
    }

    public String getPdCommodityImportAttachListJson() {
        return pdCommodityImportAttachListJson;
    }

    public void setPdCommodityImportAttachListJson(String pdCommodityImportAttachListJson) {
        this.pdCommodityImportAttachListJson = pdCommodityImportAttachListJson;
    }


}
