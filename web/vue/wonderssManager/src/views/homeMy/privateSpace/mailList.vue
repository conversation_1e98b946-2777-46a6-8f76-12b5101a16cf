<template>
	<div class="uni-container">
		<div class="wating icon-mailList">  </div>
			<div class="watTxt">敬请期待</div>

	</div>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
    props:{
      userInfo: {
        type: Object,
        default: ''
      },
      fileUrl: {
        type: String,
        default: ''
      },
    },
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>


<style lang="scss">

	
</style>