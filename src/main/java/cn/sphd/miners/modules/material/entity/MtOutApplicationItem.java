package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/5/17 10:56
 */

@Entity
@Table(name = "t_mt_out_application_item")
public class MtOutApplicationItem {
    private Integer id;
    private Integer org;
    private Integer application;
    private Integer instance;
    private String instanceChain;
    private Integer material;
    private Integer materialLocation;
    private String purpose;
    private Date requisitionTime;
    private Long requisitionDurarion;
    private BigDecimal quantityPlan;
    private BigDecimal quantityFact;
    private Date deliveryFact;
    private BigDecimal allotedQuantity;
    private BigDecimal lockedQuantity;
    private BigDecimal surplusQuantity;
    private String approvalStatus;
    private String state;
    private String memo;
    private String description;
    private String operation;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private Integer previousId;
    private Integer versionNo;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "instance")
    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    @Basic
    @Column(name = "instance_chain")
    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    @Basic
    @Column(name = "material")
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "material_location")
    public Integer getMaterialLocation() {
        return materialLocation;
    }

    public void setMaterialLocation(Integer materialLocation) {
        this.materialLocation = materialLocation;
    }

    @Basic
    @Column(name = "purpose")
    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    @Basic
    @Column(name = "requisition_time")
    public Date getRequisitionTime() {
        return requisitionTime;
    }

    public void setRequisitionTime(Date requisitionTime) {
        this.requisitionTime = requisitionTime;
    }

    @Basic
    @Column(name = "requisition_durarion")
    public Long getRequisitionDurarion() {
        return requisitionDurarion;
    }

    public void setRequisitionDurarion(Long requisitionDurarion) {
        this.requisitionDurarion = requisitionDurarion;
    }

    @Basic
    @Column(name = "quantity_plan")
    public BigDecimal getQuantityPlan() {
        return quantityPlan;
    }

    public void setQuantityPlan(BigDecimal quantityPlan) {
        this.quantityPlan = quantityPlan;
    }

    @Basic
    @Column(name = "quantity_fact")
    public BigDecimal getQuantityFact() {
        return quantityFact;
    }

    public void setQuantityFact(BigDecimal quantityFact) {
        this.quantityFact = quantityFact;
    }

    @Basic
    @Column(name = "delivery_fact")
    public Date getDeliveryFact() {
        return deliveryFact;
    }

    public void setDeliveryFact(Date deliveryFact) {
        this.deliveryFact = deliveryFact;
    }

    @Basic
    @Column(name = "alloted_quantity")
    public BigDecimal getAllotedQuantity() {
        return allotedQuantity;
    }

    public void setAllotedQuantity(BigDecimal allotedQuantity) {
        this.allotedQuantity = allotedQuantity;
    }

    @Basic
    @Column(name = "locked_quantity")
    public BigDecimal getLockedQuantity() {
        return lockedQuantity;
    }

    public void setLockedQuantity(BigDecimal lockedQuantity) {
        this.lockedQuantity = lockedQuantity;
    }

    @Basic
    @Column(name = "surplus_quantity")
    public BigDecimal getSurplusQuantity() {
        return surplusQuantity;
    }

    public void setSurplusQuantity(BigDecimal surplusQuantity) {
        this.surplusQuantity = surplusQuantity;
    }

    @Basic
    @Column(name = "approval_status")
    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    @Basic
    @Column(name = "state")
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }


    @Basic
    @Column(name = "application")
    public Integer getApplication() {
        return application;
    }

    public void setApplication(Integer application) {
        this.application = application;
    }
}
