<template>
  <div id="attendanceChangeQuery" class="reimburseQuery">
    <TY_NavTop :title="title" isButton="true" @toggleSubmit="submit"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，得到相应的查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px" @submit.native.prevent>
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.status" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.status === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <!--<div class="handle_button" style="margin-top: 20px">-->
              <!--<input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit">-->
            <!--</div>-->
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<style>
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  #reimburseApproveQuery .el-radio__input, #reimburseApplyQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
</style>

<script>
export default {
  name: 'applyReimburseQuery',
  data () {
    return {
      queryForm: {
        status: '1',
        type: '2',
        beginDate: '',
        endDate: ''
      },
      title: '职工考勤修改'
    }
  },
  created: function () {
    let type = this.$route.params.name
    if(type === 'approve2'){
      this.title = '个人提交的考勤修改'
    } else if ( type === 'apply2'){
      this.title = '我的考勤修改'
    }
  },
  methods: {
    submit: function () {
      // state 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // type 1- 批准 0- 驳回
      console.log(JSON.stringify(this.queryForm))
      let source = this.$route.params.name.indexOf('apply') > -1 ? 1 : 2
      let queryParam = {
        title: this.title,
        userId: this.sphdSocket.user.userID,
        source: source,
        approveStatus: this.queryForm.type,
        status: this.queryForm.status
      }
      let state = 0
      if (this.queryForm.status === '3') {
        queryParam.beginDate = this.queryForm.beginDate
        queryParam.endDate = this.queryForm.endDate
        if (!queryParam.beginDate) {
          this.$kiko_message('请选择自定义起时间')
          state++
        }
        if (!queryParam.endDate) {
          this.$kiko_message('请选择自定义止时间')
          state++
        }
      }
      if (state === 0) {
        localStorage.setItem('query', JSON.stringify(queryParam))
        let ty = this.$route.params.name
        let pathStr = ty === 'approve2'|| ty === 'apply2' ? 'attendanceChangePersonQueryPer' : 'attendanceChangeQueryPer'
        this.$router.push({
          path: `/${pathStr}`,
          name: pathStr,
          params: {
            data: queryParam
          }
        })
      }
    }
  }
}
</script>
