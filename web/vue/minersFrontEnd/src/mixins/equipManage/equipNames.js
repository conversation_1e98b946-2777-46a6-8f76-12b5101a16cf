import {addnamer, getlooker, havelookern, initCractern, quantity, upmodif, upname} from "@/api/equipMent";

export default {
    name:'demoMixin',
    components:{},
    data() {
        return {
            pageName: "nameManageMent",//pageName是需要写在data-return下的，写在外面会获取不到
            nameditio: 0,//用于实现多个div的隐藏和显示
            orhas: 0,//用于区分渲染的数据是否有值，并展示不同的table
            input3: '',
            select: '',
            text: '',
            listn1: [],
            listr4: [],
            listr5: [],
            listk5: [],
            pageShow: true,
            pageInfo:{},
            timeos: [{}],
            delVisiblen1: null,
            delVisiblen2: null,
            delVisiblen3: null,
            delVisiblen4: null,
            dialogTitle: "",
            tableData:[],
            tableData2: [],
            tableData3: [],
            tableData4: [],
            tableData5: [],
            tableData6: [],
            tableData7: [],
            addorupe: 0,//用于区分是新增还是修改，1-新增装备器具，2-修改装备器具
            choid: null,
            choid2: null,
            choid3: null,
            fullName: null,
            name: null,
            listt: [],
            options: [],
            value: '',
            kedn: '',
            tep: '',
            setbox: ''
        }
    },
    created(){},
    mounted(){},
    computed(){},
    methods: {
        pageClick(pageItem){
            let data = {//用于进行分页功能实现
                pageSize: 20,
                currentPageNo: pageItem.page
            }
            this.initCreateda(data)
        },
        initCreateda(){//获取调用接口时需要传入的数值
            let params = { 'currentPageNo':1,'pageSize':20,'totalPage':15 }//可能是因为接口部分并没有加入分页相关字段吧，所以
            //分页部分切换功能并没有生效
            this.initCreatacte(params);
        },
        initCreatacte(params){//用于调用接口传值
            initCractern(params).then(res => {//不知道这个接口它现在能不能正常调用
                // console.log('res',res);
                const newbox = res.data;
                //console.log('newbox',newbox);//newbox是获取到的数据
                this.listn1 = newbox;
                //console.log(this.listn1);
                const listbox = this.listn1;
                for(let i  = 0;i<listbox.length; i++){
                    const name = listbox[i].fullName;
                    //console.log('fullName:',name);
                    const model = listbox[i].modelCount;
                    //console.log('model:',model);//这里能获取到值，但是下面的判断却不正确生效。
                    if(model == null){
                        listbox[i].modelCount = "--";
                    }else{
                        listbox[i].modelCount = listbox[i].modelCount+"个型号";
                    }
                    const supplie = listbox[i].supplierCount;
                    // console.log('supplie:',supplie);
                    if(supplie == null){
                        listbox[i].supplierCount = "--";
                    }else{
                        listbox[i].supplierCount = listbox[i].supplierCount+"个来源";
                    }
                    const tunch = listbox[i].quantity;
                    //console.log('tunch:',tunch);
                    if(tunch == null){
                        listbox[i].quantity = "--";
                    }else{
                        listbox[i].quantity = listbox[i].quantity+"种";
                    }
                    const unitn = listbox[i].units;
                    if(unitn == null){
                        listbox[i].units = "--";
                    }else{}
                    // this.choid2 = listbox[i].id;
                    // this.fullName = listbox[i].fullName;
                }
                this.tableData = listbox;
            })
        },

        addname(){
            this.delVisiblen1 = true;
            this.addorupe = 1;
            this.text = "";
            const kepd = this.addorupe;
            if(kepd == 1){//新增名称
                const orn1 = "新增名称";
                this.dialogTitle = orn1;
                //console.log('this.dialogTitle现在的值',this.dialogTitle);
            }else{//修改名称
                const orn2 = "修改名称";
                this.dialogTitle = orn2;
                //console.log('this.dialogTitle现在的值',this.dialogTitle);
            }
        },
        getlookall(){
            this.nameditio = 4;
            const useid = this.choid3;
            console.log('数据获取到了么',useid);
            const jsong = {
                "equipment": useid
            };
            getlooker(jsong).then(res => {
                console.log('res',res);
                const lank4 = res.data;//获取到的可用部分的数据
                this.listr5 = [];
                const jsonk = {
                    "modelName": this.name,
                    "longr": lank4.length
                };
                this.listr5.push(jsonk);
                this.tableData6 = lank4;
                console.log('this.tableData6现在的值',this.tableData6);
                for(let k = 0;k<this.tableData6.length;k++){
                    const supplier = this.tableData6[k].supplier;
                    const supplierName = this.tableData6[k].supplierName;
                    if(supplier == 0 || supplier == ""){
                        this.tableData6[k].supplierName = "整机系自行装配（零部件可能为外购、外加工或自制）";
                    }else{
                        if(supplierName == undefined){
                            this.tableData6[k].supplierName = "--";
                        }
                    }
                    this.tableData6[k].productCount = this.tableData6[k].productCount+'种';
                }
            })
        },
        getsecher(){
            this.nameditio = 5;
            const chose1 = this.input3;
            console.log('chose1现在的值',chose1);
            const param2 = {
                'currentPageNo': 1,
                'pageSize': 20,
                'totalPage': 15,
                'fullName': chose1
            };
            this.listk5 = [];
            initCractern(param2).then(res => {
                const newbox2 = res.data;//假设是5
                //this.listk5 = newbox2;
                const json5 = {
                    "kd": newbox2.length
                };
                this.listk5.push(json5);
                const listbox2 = newbox2;
                for(let k = 0;k<listbox2.length;k++){
                    const model2 = listbox2[k].modelCount;
                    if(model2 == null){
                        listbox2[k].modelCount = "--";
                    }else{
                        listbox2[k].modelCount = listbox2[k].modelCount+'个型号';
                    }
                    const supplie2 = listbox2[k].supplierCount;
                    if(supplie2 == null){
                        listbox2[k].supplierCount = "--";
                    }else{
                        listbox2[k].supplierCount = listbox2[k].supplierCount+'个来源';
                    }
                    const tunch2 = listbox2[k].quantity;
                    if(tunch2 == null){
                        listbox2[k].quantity = "--";
                    }else{
                        listbox2[k].quantity = listbox2[k].quantity+"种";
                    }
                    const unitn2 = listbox2[k].units;
                    if(unitn2 == null){
                        listbox2[k].units = "--";
                    }else{}
                }
                this.tableData7 = listbox2;
            })
        },
        gobcken(nubd){
            if(nubd == 1){
                console.log('this.tep的值',this.tep);
                if(this.tep == 1){
                    this.nameditio = 0;
                }else if(this.tep == 2){
                    this.nameditio = 5;
                }
            }else if(nubd == 2){
                this.nameditio = 1;
            }else if(nubd == 3){
                console.log(this.kedn);
                if(this.kedn == 1){
                    this.nameditio = 2;
                }else if(this.kedn == 2){
                    this.nameditio = 4;
                }
            }else if(nubd == 4){
                this.nameditio = 1;
            }else if(nubd == 5){
                this.nameditio = 0;
                //如何将搜索选择框恢复至未填写的清空呢
                this.input3 = "";
            }
        },
        hideFun1(){
            this.delVisiblen1 = false;
        },
        hideFun2(){
            this.delVisiblen2 = false;
        },
        hideFun3(){
            this.delVisiblen3 = false;
        },
        hideFun4(){
            this.delVisiblen4 = false;
        },
        lookmore(havlook){
            //console.log('havlook的值',havlook);
            this.nameditio = 2;
            const uid1 = this.choid3;
            //console.log('下面的打印是6.3打印的');
            //console.log('uid1的值',uid1);//这个数据可以调用接口获取全部数据了
            const jsonh = {
                "modelName": havlook.modelName,
                "equipment": uid1
            };
            //this.name = havlook.modelName;
            //console.log('havlook.modelName有值么',havlook.modelName);
            havelookern(jsonh).then(res => {
                //console.log('res',res);
                this.tableData4 = res.data;
                this.options = [];
                this.value = "";
                for(let t = 0;t<this.tableData4.length;t++){
                    const supplier = this.tableData4[t].supplier;
                    const supplierName = this.tableData4[t].supplierName;
                    if(supplier == 0 || supplier == ""){
                        console.log('走到这里了么?');
                        this.tableData4[t].supplierName = "整机系自行装配（零部件可能为外购、外加工或自制）";
                        console.log('现在的值是多少？',this.tableData4[t].supplierName);//明明这里已经成功打印出了，为何tableData4中并没有成功赋值上呢
                    }else{
                        if(supplierName == undefined) {
                            this.tableData4[t].supplierName = "--";
                        }
                    }
                    this.tableData4[t].productCount = this.tableData4[t].productCount+'种';
                    //需要将获取到的所有来源数据都存起来赋值给options
                    const jsono = {
                        value:'',
                        label: ''
                    }
                    if(this.tableData4[t].supplierName == "--"){
                    }else{
                        if(this.options.length === 0){
                            jsono.value = '选项'+t+1;
                            jsono.label = this.tableData4[t].supplierName;
                            this.options.push(jsono);
                        }else{
                            for(let o = 0;o<this.options.length;o++){
                                if(this.tableData4[t].supplierName === this.options[o].label){}else{
                                    jsono.value = '选项'+t+1;
                                    jsono.label = this.tableData4[t].supplierName;
                                    this.options.push(jsono);
                                }
                            }
                        }
                    }
                    console.log('现在的数据对么',this.options);
                }
                console.log('this.tableData4',this.tableData4);
                this.listr4 = [];
                const jsont = {
                    "name": "",
                    "modelName": havlook.modelName,
                    "longr": this.tableData4.length
                };
                const link4 = this.listt;
                //console.log('this.listt现在的样子',this.listt);
                for(let j = 0;j<link4.length;j++){
                    jsont.name = link4[j].name;
                }
                //console.log(jsont);
                this.listr4.push(jsont);
                //console.log('tableData4',this.tableData4);
            })
        },
        lookallmore(all,kepds){
            this.nameditio = 3;
            const useid = this.choid3;
            console.log('useid的值是51么',useid);
            let ked = "";
            if(kepds === 1){
                ked = 1;
            }else if(kepds === 2){
                ked = 2;
            }
            this.kedn = ked;
            //debugger;
            console.log('all现在的值',all);
            this.setbox = all;
            console.log('4.22-9:17',all);
            console.log('4.22-9:56',this.tableData4);
        },
        quantitymess(meesg,tepnd){
            let temp = '';
            if(tepnd === 1){//首页表格中点击跳转
                temp = 1;
            }else if(tepnd === 2){
                temp = 2;
            }
            this.tep = temp;
            this.nameditio = 1;
            //console.log('meesg的值',meesg);
            const chid = meesg.id;//装备器具对应的Id
            const name = meesg.fullName;//装备器具对应的名称
            this.name = name;
            console.log('下面的打印是6.3');
            console.log('值正确么',chid);
            //console.log('值正确么',name);
            const link1 = {
                "name": "",
                "numb": ""
            };
            link1.name = name;
            //this.listt.push(link1);
            //console.log('chid有值么',chid);
            this.choid3 = chid;
            this.listt = [];
            const json4 = {
                "equipment":chid
            };
            quantity(json4).then(res => {
                //console.log('res',res);
                //console.log('能获取到数据么',res.data);
                if(res.data.length == 0){
                    link1.numb = 0;
                    this.tableData3 = [];
                }else{
                    link1.numb = res.data.length;
                    this.tableData3 = res.data;
                    //console.log('this.tableData3的值',this.tableData3);
                }
                this.listt.push(link1);
                //console.log('listt的样子',this.listt);
            })
        },
        tipOk1(keept,diffen){
            //当需要获取input输入框中输入的值时，可以采用下面的方法：1.用const定义一个属性名，将input的v-model的值前面加上this.，并
            //将其赋值给const，这样const获取到的就是输入框中输入的值。
            const content1 = this.text;
            const unid = this.choid;
            const json1 = {
                "fullName": content1
            };
            const json2 = {
                "id": unid,
                "fullName": content1
            };
            //console.log(json1);
            //console.log(json2);
            if(keept == 1){//新增名称
                addnamer(json1).then(res => {
                    //console.log('res',res);
                    const mess = res.data.msg;
                    //console.log(mess);
                    if(mess == "操作成功"){
                        if(diffen == 1){//nameditio = 0
                            this.delVisiblen1 = false;
                        }else if(diffen = 2){//nameditio = 5
                            this.delVisiblen3 = false;
                        }
                        const params1 = { 'currentPageNo':1,'pageSize':20,'totalPage':15};
                        initCractern(params1).then(res => {//不知道这个接口它现在能不能正常调用
                            //console.log('res',res);
                            const newbox = res.data;
                            //console.log('newbox',newbox);//newbox是获取到的数据
                            this.listn1 = newbox;
                            //console.log(this.listn1);
                            const listbox = this.listn1;
                            for(let i  = 0;i<listbox.length; i++){
                                const name = listbox[i].fullName;
                                //console.log('fullName:',name);
                                const model = listbox[i].modelCount;
                                //console.log('model:',model);//这里能获取到值，但是下面的判断却不正确生效。
                                if(model == null){
                                    listbox[i].modelCount = "--";
                                }else{
                                    listbox[i].modelCount = listbox[i].modelCount+"个型号";
                                }
                                const supplie = listbox[i].supplierCount;
                                //console.log('supplie:',supplie);
                                if(supplie == null){
                                    listbox[i].supplierCount = "--";
                                }else{
                                    listbox[i].supplierCount = listbox[i].supplierCount+"个来源";
                                }
                                const tunch = listbox[i].quantity;
                                //console.log('tunch:',tunch);
                                if(tunch == null){
                                    listbox[i].quantity = "--";
                                }else{
                                    listbox[i].quantity = listbox[i].quantity+"种";
                                }
                                const unitn = listbox[i].units;
                                if(unitn == null){
                                    listbox[i].units = "--";
                                }else{}
                            }
                            if(diffen == 1){//nameditio = 0
                                this.tableData = listbox;
                            }else if(diffen == 2){//nameditio = 5
                                this.tableData7 = listbox;
                            }
                        })
                    }else{
                        debugger;
                        if(diffen == 1){//nameditio = 0
                            this.delVisiblen1 = true;
                            if(mess === "操作失败"){
                                //三秒提示👇
                                this.$message.error("操作失败，因为系统里已有这个名称了！");
                                //layer.msg("操作失败，因为系统里已有这个名称了！");
                                //var meos = "操作失败，因为系统里已有这个名称了！";
                            }
                            //layer.msg(m)
                            //三秒提示👇
                            //this.$.message.error("操作失败，因为系统里已有这个名称了！");
                        }else if(diffen == 2){//nameditio = 5
                            this.delVisiblen3 = true;
                        }
                    }
                })
            }else{//修改名称
                upmodif(json2).then(res => {
                    //console.log('res',res);
                    const memss = res.data.msg;
                    if(memss == "操作成功"){
                        if(diffen == 1){//nameditio = 0
                            this.delVisiblen1 = false;
                        }else if(diffen == 2){//nameditio = 5
                            this.delVisiblen3 = false;
                        }
                        const chose12 = this.input3;
                        console.log('chose12的值是否能获取到',chose12);
                        const params2 = { 'currentPageNo':1,'pageSize':20,'totalPage':15,'fullName': chose12 };
                        initCractern(params2).then(res => {//不知道这个接口它现在能不能正常调用
                            //console.log('res',res);
                            console.log('res.data的值是否是最新的',res.data);//这里获取到的数据是最新的
                            const newbox = res.data;
                            //console.log('newbox',newbox);//newbox是获取到的数据
                            this.listn1 = newbox;
                            //console.log(this.listn1);
                            const listbox = this.listn1;
                            for(let i  = 0;i<listbox.length; i++){
                                const name = listbox[i].fullName;
                                //console.log('fullName:',name);
                                const model = listbox[i].modelCount;
                                //console.log('model:',model);//这里能获取到值，但是下面的判断却不正确生效。
                                if(model == null){
                                    listbox[i].modelCount = "--";
                                }else{
                                    listbox[i].modelCount = listbox[i].modelCount+"个型号";
                                }
                                const supplie = listbox[i].supplierCount;
                                //console.log('supplie:',supplie);
                                if(supplie == null){
                                    listbox[i].supplierCount = "--";
                                }else{
                                    listbox[i].supplierCount = listbox[i].supplierCount+"个来源";
                                }
                                const tunch = listbox[i].quantity;
                                //console.log('tunch:',tunch);
                                if(tunch == null){
                                    listbox[i].quantity = "--";
                                }else{
                                    listbox[i].quantity = listbox[i].quantity+"种";
                                }
                                const unitn = listbox[i].units;
                                if(unitn == null){
                                    listbox[i].units = "--";
                                }else{}
                            }
                            if(diffen == 1){//nameditio = 0
                                this.tableData = listbox;
                            }else if(diffen == 2){
                                this.tableData7 = listbox;
                            }
                        })
                    }else{
                        if(diffen == 1){//nameditio == 0
                            this.delVisiblen1 = true;
                        }else if(diffen == 2){//nameditio == 5
                            this.delVisiblen3 = true;
                        }
                    }
                })
            }
        },
        upeqname(keoplis,diff1){
            //所以是因为‘修改’和‘修改记录’弹窗在nameditio=0的页面，而不在nameditio=5的页面，弹窗就被覆盖了？
            //那将弹窗再写一遍？
            if(diff1 == 1) {//nameditio == 0
                this.delVisiblen1 = true;
            }else if(diff1 == 2){//nameditio == 5
                this.delVisiblen3 = true;
            }
            this.addorupe = 2;
            const upden = this.addorupe;
            if(upden == 1){//新增名称
                const orn1 = "新增名称";
                this.dialogTitle = orn1;
            }else{//修改名称
                const orn2 = "修改名称";
                this.dialogTitle = orn2;
            }
            //获取函数所在那行数据的方法：
            //console.log('keoplis现在的值',keoplis);
            //console.log('能获取到名称么',keoplis.fullName);
            this.text = keoplis.fullName;
            this.choid = keoplis.id;
        },
        upnamerecord(keop2,diff2){
            if(diff2 == 1){//nameditio == 0
                this.delVisiblen2 = true;
            }else if(diff2 == 2){//nameditio == 5
                this.delVisiblen4 = true;
            }
            //console.log('keoplis现在的值',keop2);
            const choid2 = keop2.id;//获取到该行数据的id
            const json3 = {
                "id": choid2
            };
            //console.log(json3);
            upname(json3).then(res => {
                //console.log('res',res);
                const list3 = res.data;
                for(let j = 0; j<list3.length;j++){
                    const versionNo = list3[j].versionNo;
                    if(versionNo == 0){
                        list3[j].msgmade = "数据的创建";
                    }else{
                        list3[j].msgmade = "第"+list3[j].versionNo+"次修改后";
                    }
                }
                this.tableData2 = list3;
            })
        },
        chonseotherq(){
            var name = this.value;//选择的来源
            var box = this.tableData4;//筛选框下面列表中所有的数据
            var mibox = this.options;//筛选框供选择的数据
            console.log('4.8-13:12',name);
            console.log('4.8-13:13',box);
            console.log('4.8-13:18',mibox);
            let newbox = [];
            for(let o = 0;o<mibox.length;o++){
                if(mibox[o].value === name){//假如都是选项1
                    let namet = mibox[o].label;
                    for(let b = 0;b<box.length;b++){
                        if(box[b].supplierName === namet){
                            newbox.push(box[b]);
                        }
                    }
                }
            }
            console.log('4.8-13:23',newbox);
            this.tableData4 = newbox;
        }
    }
}



/*补录结束*/
