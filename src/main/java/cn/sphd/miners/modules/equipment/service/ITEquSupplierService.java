package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquSupplier;

import java.util.List;

/**
 * 装备管理_供应商名录
 * 20230209 1.247装备器具新增Service接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public interface ITEquSupplierService {
    /**
     * 查询装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_供应商名录
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */
    public TEquSupplier selectTEquSupplierById(Long id);

    /**
     * 查询装备管理_供应商名录
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquSupplier 装备管理_供应商名录
     *                     20230209 1.247装备器具新增
     * @return 装备管理_供应商名录
     * 20230209 1.247装备器具新增集合
     */
    public List<TEquSupplier> selectTEquSupplierList(TEquSupplier tEquSupplier);

    /**
     * 新增装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplier 装备管理_供应商名录
     *                     20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquSupplier(TEquSupplier tEquSupplier);

    /**
     * 修改装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplier 装备管理_供应商名录
     *                     20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquSupplier(TEquSupplier tEquSupplier);

    /**
     * 批量删除装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_供应商名录
     *            20230209 1.247装备器具新增主键集合
     * @return 结果
     */
    public int deleteTEquSupplierByIds(String... ids);

    /**
     * 删除装备管理_供应商名录
     * 20230209 1.247装备器具新增信息
     *
     * @param id 装备管理_供应商名录
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    public int deleteTEquSupplierById(Long id);
}
