package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpPlatformDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpPlatform;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpPlatformDaoImpl extends BaseDao<TpPlatform, Serializable> implements TpPlatformDao {
}
