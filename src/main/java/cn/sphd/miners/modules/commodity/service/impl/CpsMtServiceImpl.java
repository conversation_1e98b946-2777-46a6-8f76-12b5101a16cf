package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.PdBaseDao;
import cn.sphd.miners.modules.commodity.dao.PdBaseHistoryDao;
import cn.sphd.miners.modules.commodity.dao.PdCompositionMaterialDao;
import cn.sphd.miners.modules.commodity.dao.PdCompositionMaterialHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdCompositionMaterial;
import cn.sphd.miners.modules.commodity.service.CpsMtService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.material.dao.MtBaseDao;
import cn.sphd.miners.modules.material.dao.MtBaseHistoryDao;
import cn.sphd.miners.modules.material.dao.MtStockInfoDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.entity.MtStockInfo;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.sales.dao.PoOrdersDao;
import cn.sphd.miners.modules.sales.model.PoOrders;
import cn.sphd.miners.modules.system.dao.OrgDao;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import org.hibernate.Criteria;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class CpsMtServiceImpl implements CpsMtService {
    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    PdBaseHistoryDao pdBaseHistoryDao;

    @Autowired
    PdCompositionMaterialDao pdCompositionMaterialDao;
    @Autowired
    PdCompositionMaterialHistoryDao pdCompositionMaterialHistoryDao;
    @Autowired
    MtBaseDao mtBaseDao;
    @Autowired
    MtBaseHistoryDao mtBaseHistoryDao;
    @Autowired
    PoOrdersDao poOrdersDao;
    @Autowired
    OrgDao orgDao;
    @Autowired
    UserSuspendMsgService suspendMsgService;
    @Autowired
    UnitService unitService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    MtStockInfoDao stockInfoDao;
    @Override
    public Map<String, Object> getPdMtBaseList(Integer oid, Integer currPage, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "p.phrase AS phrase,\n" +
                "if(p.weight_unit ='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight from t_pd_base p \n" +
                "LEFT JOIN t_pd_composition_material cm on p.id=cm.product\n" +
                "where p.oid="+oid+" and cm.id is null and p.composition='4' order by p.create_date ";
        return pdBaseDao.findMapByConditionByPage(sql, "", p, currPage, pageSize);

    }

    @Override
    public List<Map<String, Object>> getPdMtBaseList(Integer oid) {
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "p.phrase AS phrase,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight from t_pd_base p \n" +
                "LEFT JOIN t_pd_composition_material cm on p.id=cm.product\n" +
                "where p.oid="+oid+" and cm.id is null and p.composition='4' order by p.create_date ";
        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

    }

    @Override
    public String saveMt(MtBase mtBase, MtCategory category, User user) {

        //添加
        if(mtBase.getId()==null||"0".equals(mtBase.getId())){
            MtBase m=mtBaseDao.getByHQL("from MtBase where code='"+mtBase.getCode()+"' and org="+user.getOid());
            if (m!=null){
                return "该材料代号已存在";
            }
            mtBase.setCategory(category);
            mtBase.setCreateDate(new Date());
            mtBase.setCreator(user.getUserID());
            mtBase.setCreateName(user.getUserName());
            mtBase.setSource("2");
            mtBase.setVersionNo(1);
            mtBase.setEnabledTime(new Date());
            mtBase.setEnabled("1");
            mtBase.setOrg(user.getOid());
            mtBase.setOperation("1");
            if("1".equals(mtBase.getIsCurrent())){
                mtBase.setOrigin("4");
                mtBase.setIsPurchased("1");
            }else{
                mtBase.setOrigin("4");
                mtBase.setIsPurchased("0");
            }

            mtBaseDao.save(mtBase);

            //添加后先判断库存情况  -zy
            MtStockInfo stockInfo = stockInfoDao.getByHQL(" from MtStockInfo o where 1=1 and o.material_ = "+mtBase.getId());

            BigDecimal zero=new BigDecimal(0);
            if(stockInfo==null){
                stockInfo = new MtStockInfo();
                stockInfo.setCurrentStock(zero);
                stockInfo.setAvailableStock(zero);
                stockInfo.setMinimumStock(zero);
                stockInfo.setMaterial(mtBase);
                stockInfo.setCreator(mtBase.getCreator());
                stockInfo.setInitialStock(zero);
                stockInfoDao.save(stockInfo);
            }
            mtBaseDao.getSession().flush();


            if(mtBase.getUnitId()!=null){
                //更新计量单位
                unitService.selectUnit(mtBase.getUnitId(),8);
            }

        }
            addPdCompositionMaterial(mtBase,user);

        return "success";
    }

    @Override
    public String bindOperation(String baseList, Integer mtId, User user) {
        String[] s=baseList.split(",");
        MtBase mtBase=mtBaseDao.get(mtId);
        if(mtBase==null){
            return "材料信息错误";
        }
        for (String str:s) {

            mtBase.setProduct_(Integer.valueOf(str));
            addPdCompositionMaterial(mtBase,user);

        }
        return "success";
    }

    @Override
    public String deleteMt(Integer id, Integer oid) {
        MtBase mtBase=mtBaseDao.get(id);
        if(mtBase==null){
            return "材料参数有误";
        }

        if(!oid.equals(mtBase.getCategory().getOrg_())){
            return "材料参数有误";
        }

        List<PdCompositionMaterial> list=pdCompositionMaterialDao.getListByHQL("from PdCompositionMaterial where material="+mtBase.getId());
        if (list.size()>0){
            return "删除失败，因为还有使用该材料的产品、零件或配方！";
        }



        mtBaseDao.delete(mtBase);
        return "success";
    }

    @Override
    public String startStopMt(Integer id,String state,User user) {
        MtBase mtBase=mtBaseDao.get(id);
        if(mtBase==null){
            return "材料参数有误";
        }

        if(!user.getOid().equals(mtBase.getCategory().getOrg_())){
            return "材料参数有误";
        }
        List<PdCompositionMaterial> list=pdCompositionMaterialDao.getListByHQL("from PdCompositionMaterial where material="+mtBase.getId());
        if (list.size()>0){
            return "操作失败，因为还有使用该材料的产品、零件或配方！";
        }

        mtBase.setUpdateDate(new Date());
        mtBase.setUpdateName(user.getUserName());
        mtBase.setUpdator(user.getUserID());
        if("1".equals(state)){
            mtBase.setOperation("2");
            mtBase.setEnabled("1");
            mtBase.setEnabledTime(new Date());
        }else{
            mtBase.setOperation("4");
            mtBase.setEnabled("0");
            mtBase.setEnabledTime(new Date());
        }


        mtBaseDao.update(mtBase);

        mtBaseHistoryDao.insert(mtBase);

        return "success";

    }

    @Override
    public String updateMt(MtBase mtBase, User user) {
        MtBase mt=mtBaseDao.get(mtBase.getId());
        if (mt==null){
            return "材料参数错误";
        }

        if(!user.getOid().equals(mt.getCategory().getOrg_())){
            return "材料参数错误";
        }
        MtBase m=mtBaseDao.getByHQL("from MtBase where code='"+mtBase.getCode()+"' and org="+user.getOid());
        if (m!=null){
            if(!m.getId().equals(mtBase.getId())){
                return "该材料代号已存在";
            }
        }
        mt.setUpdator(user.getUserID());
        mt.setUpdateName(user.getUserName());
        mt.setUpdateDate(new Date());
        mt.setName(mtBase.getName());
        mt.setCode(mtBase.getCode());
        mt.setUnit(mtBase.getUnit());
        mt.setUnitId(mtBase.getUnitId());
        mt.setSpecifications(mtBase.getSpecifications());
        mt.setModel(mtBase.getModel());
        mt.setMemo(mtBase.getMemo());
        mt.setTerminateOrders(mtBase.getTerminateOrders());
        mt.setOperation("3");
        mtBaseDao.update(mt);

        mtBaseHistoryDao.insert(mt);

        if(mtBase.getUnitId()!=null){
            //更新计量单位
            unitService.selectUnit(mtBase.getUnitId(),8);
        }

        if("1".equals(mtBase.getTerminateOrders())){

            //拥有材料管理权限的用户
            List<UserDto> zdList= userPopedomService.getUserByOidMid(user.getOid(),"zd");
            //获取未完结订单
            List<PoOrders> list=this.getPoOrderByState(mt.getId());
            String s=user.getUserName()+"修改了\""+mt.getCode()+"\"的材料的信息,你需要终止进行中的"+list.size()+"个采购订单";
            for (UserDto u:zdList) {
                suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),u.getUserID().intValue(),null,0);
            }



        }else{
            //拥有材料管理权限的用户
            List<UserDto> zdList= userPopedomService.getUserByOidMid(user.getOid(),"zd");
            String s=user.getUserName()+"修改了\""+mt.getCode()+"\"的材料的信息,特此告知";
            for (UserDto u:zdList) {
                suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),u.getUserID().intValue(),null,0);
            }

        }
        return "success";
    }

    @Override
    public Map<String, Object> getBindingPdList(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql="select\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.update_name AS updateName,\n" +
                "p.update_date AS updateDate,\n" +
                "p.phrase AS phrase,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "cm.product as product,\n" +
                "cm.material as material\n" +
                "from\n" +
                "t_pd_composition_material cm\n" +
                "LEFT JOIN t_pd_base p on cm.product=p.id\n" +
                "where  cm.product is not null and cm.material="+id;
        return mtBaseDao.findMapByConditionByPage(sql,null,p,currPage,pageSize);
    }

    @Override
    public Map<String, Object> getBeforeBindingPdList(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql="select\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.update_name AS updateName,\n" +
                "p.update_date AS updateDate,\n" +
                "p.phrase AS phrase,\n" +
                "p.process_dept_name AS processDeptName,\n" +
                "cm.product as product,\n" +
                "cm.material as material\n" +
                "from\n" +
                "t_pd_composition_material_history cm\n" +
                "LEFT JOIN t_pd_composition_material c on cm.composition_material=c.id and c.material="+id+"\n"+
                "LEFT JOIN t_pd_base p on cm.product=p.id\n" +
                "where c.id is null and cm.material="+id;
        return mtBaseDao.findMapByConditionByPage(sql,null,p,currPage,pageSize);
    }

    @Override
    public Map<String, Object> getMtListByPdBase(Integer id, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        String sql="select\n" +
                "m.code as innerSn,\n" +
                "m.name AS name,\n" +
                "m.model AS model,\n" +
                "m.specifications AS specifications,\n" +
                "m.unit AS unit,\n" +
                "cm.create_name AS createName,\n" +
                "cm.create_date AS createDate,\n" +
                "cm.update_name AS updateName,\n" +
                "cm.update_date AS updateDate,\n" +
                "cm.product as product,\n" +
                "cm.material as material,\n" +
                "cm.operation as operation\n" +
                "from\n" +
                "t_pd_composition_material_history cm\n" +
                "LEFT JOIN t_mt_base m on cm.material=m.id\n" +
                "where cm.product="+id+" and m.id is not null ORDER BY cm.id desc";
        return mtBaseDao.findMapByConditionByPage(sql,null,p,currPage,pageSize);
    }

    @Override
    public  List<PoOrders> getPoOrderByState(Integer id) {
        String hql="FROM PoOrders where id in (select i.orders from PoOrdersItem i LEFT JOIN MtSupplierMaterial m on i.supplierMaterial=m.id where m.material='"+id+"')";




        return poOrdersDao.getListByHQL(hql);
    }

    @Override
    public List<Map<String, Object>> getOrg() {
        String sql="SELECT o.id,\n" +
                "(SELECT COUNT(id) from t_mt_base p where p.org=o.id and p.origin in ('4','5','6','7') and date_format( p.create_date, '%Y-%m-%d' )=DATE_SUB(curdate(),INTERVAL 1 DAY)) sum\n" +
                " from t_sys_org o\n" +
                "where o.pid=0\n" +
                "HAVING sum>0";
        return orgDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> getOrgForStop() {
        String sql="SELECT o.id,\n" +
                "(SELECT COUNT(id) from t_mt_base p where p.org=o.id and p.origin in ('4','5','6','7') and enabled='0' and  date_format( p.update_date, '%Y-%m-%d' )=DATE_SUB(curdate(),INTERVAL 1 DAY)) sum\n" +
                " from t_sys_org o\n" +
                "where o.pid=0\n" +
                "HAVING sum>0";
        return orgDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }


    @Override
    public List<Map<String, Object>> zrtj(Integer org) {
        String sql="select p.creator userId,p.create_name userName,DATE_FORMAT(p.create_date,'%Y-%m-%d %H:%i:%S') date,COUNT(p.id) sum from t_mt_base p\n" +
                "WHERE p.org="+org+"  and p.origin in ('4','5','6','7') and  date_format( p.create_date, '%Y-%m-%d' )=DATE_SUB(curdate(),INTERVAL 1 DAY)\n" +
                "GROUP BY p.creator";

        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> zrtytj(Integer org) {
        String sql="select p.creator userId,p.create_name userName,DATE_FORMAT(p.create_date,'%Y-%m-%d %H:%i:%S') date,COUNT(p.id) sum from t_mt_base p\n" +
                "WHERE p.org="+org+" and p.enabled='0' and p.origin in ('4','5','6','7') and  date_format( p.update_date, '%Y-%m-%d' )=DATE_SUB(curdate(),INTERVAL 1 DAY)\n" +
                "GROUP BY p.creator";

        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public Map<String, Object> zrzj(Integer org) {
        String sql="select count(id) as num from t_mt_base where org="+org+" and  origin in ('4','5','6','7') and date_format( create_date, '%Y-%m-%d' )=DATE_SUB(curdate(),INTERVAL 1 DAY) ";
        Map<String,Object> m=(HashMap<String,Object>)pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list().get(0);
        return m;
    }

    private void addPdCompositionMaterial(MtBase mtBase,User user){

        //先判断材料是否是直接添加
        if(mtBase.getProduct_()!=null&&!"0".equals(mtBase.getProduct_())){

            //判断产品有没有关联
            PdCompositionMaterial pm=pdCompositionMaterialDao.getByHQL("from PdCompositionMaterial where product="+mtBase.getProduct_()+" and (formula is null or formula='')");
            //如果关联
            if(pm!=null){
                //如果与mtbase不一致，说明是修改的材料
                if(!pm.getMaterial().equals(mtBase.getId())){
                    PdBase pdBase=pdBaseDao.get(pm.getProduct());
                    pdBase.setOperation("8");
                    Integer hisPdBaseId= pdBaseHistoryDao.setHistory(pdBase);
                    Integer hisMtBaseId=mtBaseHistoryDao.insert(mtBase);

                    pm.setMaterial(mtBase.getId());
                    pm.setUpdateName(user.getUserName());
                    pm.setUpdateDate(new Date());
                    pm.setUpdator(user.getUserID());
                    pm.setOperation("3");
                    pm.setVersionNo(pm.getVersionNo()+1);
                    pdCompositionMaterialDao.update(pm);
                    //添加进历史
                    pdCompositionMaterialHistoryDao.insert(pm,hisMtBaseId,hisPdBaseId);


                }

            }else{
                PdBase pdBase=pdBaseDao.get(mtBase.getProduct_());
                pdBase.setOperation("8");
                Integer hisPdBaseId= pdBaseHistoryDao.setHistory(pdBase);
                Integer hisMtBaseId=mtBaseHistoryDao.insert(mtBase);

                pm=new PdCompositionMaterial();
                pm.setOperation("1");
                pm.setCreateDate(new Date());
                pm.setCreateName(user.getUserName());
                pm.setCreator(user.getUserID());
                pm.setMaterial(mtBase.getId());
                pm.setProduct(mtBase.getProduct_());
                pm.setInnerSn(mtBase.getCode());
                pm.setOrg(user.getOid());
                pm.setVersionNo(1);
                pdCompositionMaterialDao.save(pm);

                pdCompositionMaterialDao.getSession().flush();

                pdCompositionMaterialHistoryDao.insert(pm,hisMtBaseId,hisPdBaseId);
            }

        }
    }
}
