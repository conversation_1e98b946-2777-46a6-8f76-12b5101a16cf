<template>
	<div class="reg2">
    <Pageheader title="注册主播助手"></Pageheader>
    <div class="live-container">
      <div class="centerTxt">
        <div>主播助手，将为您的直播事业提供助力！</div>
        <div class="bluetip" v-if="shareMesage && shareMesage.type == 1">注：本条数据由{{ shareMesage.shareName}}于{{shareMesage.shareTime }}分享</div>
      </div>

      <form class="li-form" ref="userForm">
        <div class="li-formItem">
          <span class="li-ttl">手机号</span>
          <input type="text" v-model="phone" placeholder="请输入中国大陆地区的手机号"/>
        </div>
        <div class="li-formItem">
          <span class="li-ttl">验证码</span>
          <span class="li-codeBtn" @click="getCode">获取验证码</span>
          <input type="text" v-model="code" class="litCode" placeholder="请输入验证码" />
        </div>
        <span @click="goPage('register3')" class="primary live-button">下一步，选择套餐</span>

        <div class="li-formItem">
          <el-checkbox v-model="checked1" color="#0ca751"  label="同意《用户协议》和《隐私政策》"></el-checkbox>
        </div>
      </form>
    </div>
		 
	</div>
</template>
<script setup>
import { sendMobileCode , checkCode  } from '@/api/api.js'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'
import { ElMessage } from 'element-plus'
import auth from '@/sys/auth'


const router = useRouter();
const phone = ref('')
const code = ref('')
let senCodePhone = 'nm'
const checked1 = ref(false);
let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
console.log(' let jsonData = ', jsonData)
const shareMesage = jsonData.shareMesage

const goPage = (url) => {
  if(checked1.value){
    if(senCodePhone == phone.value){
      let data = {
        code: code.value,
        phone: phone.value
      }
      console.log('传参 data ', data)
      if(!data.code){
        ElMessage.error('验证码不能为空！')
        return false
      }
      if(data.phone.length != 11){
        ElMessage.error('手机号格式不对！')
        return false
      }
      checkCode(data).then(res => {
        console.log('checkCode res', res)
        if(res.success) {
          router.push(url)
        } else {
          ElMessage.error(res.error.message)
        }
        // if(res){
        //   router.push(url)
        // }else{
        //   ElMessage.error('这个手机号已被注销了，换个手机号吧！')
        // }
      }).catch(err => {
        console.log('checkCode err', err)

      })
    }else{
      ElMessage.error('手机号码与验证码不匹配！')
    }
  }else {
    ElMessage.warning('请先勾选 同意《用户协议》和《隐私政策》！')

  }
}
const getCode = () => {
  let data = {
    phone: phone.value,
  }
  if(data.phone?.length !== 11){
    ElMessage.error('手机号格式不对')
    return false
  }
  sendMobileCode(data).then(res => {
    senCodePhone = data.phone
    // 1-成功 0-失败，3-手机号格式不对，5 验证码已经发过还未过时
    console.log('sendMobileCode = ', res)
    if(res.success) {
        ElMessage.success('验证码已发送，请注意查收')
    } else {
      ElMessage.error(res.error.message)
    }
    // if(res.data == 1){
    //   ElMessage.success('验证码已发送，请注意查收')
    // }else if(res.data == 3){
    //   ElMessage.error('手机号格式不对')
    // }else if(res.data == 5){
    //   ElMessage.error('验证码已经发过还未过时')
    // }else {
    //   ElMessage.error('验证码发送失败')
    // }
  }).catch(err => {
    console.log('sendMobileCode err =', err)
  })
}
</script>

<style lang="scss" scoped>
$leftWid: 100px;
.centerTxt{ text-align: center; margin-top: 30px; margin-bottom: 50px; }
.bluetip{
  font-size: 0.8em;
  color: #0b94ea;
  line-height: 30px;
}
.li-form{
  .li-formItem{
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    position: relative;
    .li-ttl{
      position: absolute;
      width: $leftWid;
      text-align: center;
    }
    input.litCode{
      padding-right:150px;
    }
    input{
      font-size: 1.1em;
      color: #333;
      outline: none;
      padding-left: $leftWid;
      border: none;
      display: block;
      height: 30px;
      line-height: 30px;
      padding-bottom: 8px;
      width: 100%;
      background-color:#f8f8f8 ;
      box-sizing: border-box;
    }
    .li-codeBtn{
      border-radius: 3px;
      background-color: $livePageHeadBg;
      position: absolute;
      right: 10px;
      color: #fff;
      padding: 2px 5px;
      font-size: 0.9em;
    }
  }
}
</style>

