package cn.sphd.miners.modules.commodity.controller;


import cn.sphd.miners.modules.commodity.service.CpsMtService;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/productMaterial")
public class ProMaterialController {

    @Autowired
    private MtService mtService;


    @Autowired
    private MaterielService materielService;

    @Autowired
    private CpsMtService cpsMtService;

    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserSuspendMsgService suspendMsgService;

    /**
     * 主页跳转
     */
    @RequestMapping("/getHome.do")
    public String getHome(){

        return "" ;
    }


    /**
     * 获取首页数据
     */
    @ResponseBody
    @RequestMapping("/getHomeData.do")
    public Map<String,Object> getHomeData(User user,String keyword,Integer currPage, Integer pageSize) {
        Integer oid=user.getOid();

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }


        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        List<Integer> codes=new ArrayList<>();
        for (MtCategory m:mtCategories) {
            if("构成商品的原辅材料".equals(m.getName())){

                List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                for (MtCategory m2:l) {
                    codes.add(m2.getId());
                }

            }
        }
        //材料列表
        Map<String,Object> map=mtService.getMtList("1",keyword,codes, currPage,  pageSize);

        //待处理产品
        Map<String,Object> r=cpsMtService.getPdMtBaseList(oid,1,1);

        if("200".equals(r.get("code"))){
            map.put("pdNum",r.get("totalRows"));
        }else{
            map.put("pdNum",0);
        }


        //已停止
        Map<String,Object> stop=mtService.getMtList("0",null,codes, 1,  1);
        if("200".equals(stop.get("code"))){
            map.put("stopNum",stop.get("totalRows"));
        }else{
            map.put("stopNum",0);
        }

        return map;
    }

    /**
     *录入材料
     * @return
     */
    @RequestMapping("/addCompositionMaterial.do")
    @ResponseBody
    public Map<String,Object> addCompositionMaterial(User user, MtBase mtBase){
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid= user.getOid();
        


        if(StringUtils.isEmpty(mtBase.getName())){
            map.put("code",400);
            map.put("msg","请填写名称");
            return map;
        }
        if(StringUtils.isEmpty(mtBase.getCode())){
            map.put("code",400);
            map.put("msg","请填写代号");
            return map;
        }
        if(StringUtils.isEmpty(mtBase.getUnit())){
            map.put("code",400);
            map.put("msg","请填写计量单位");
            return map;
        }
//
//        if(StringUtils.isEmpty(mtBase.getIsCurrent())){
//            map.put("code",400);
//            map.put("msg","请选择是否需要填写当前库存");
//            return map;
//        }


        MtCategory category=null;
        if(mtBase.getId()==null||"0".equals(mtBase.getId())){
            List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
            for (MtCategory m:mtCategories) {
                if("构成商品的原辅材料".equals(m.getName())){

                    List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                    for (MtCategory m2:l ) {
                        if("待分类".equals(m2.getName())){
                            category=m2;
                        }
                    }

                }
            }
        }

        //分类

        String res=cpsMtService.saveMt(mtBase, category,user);
        //录入构成商品的原辅材料时 增加科目
        materielService.changeIntoSubject(oid,user,mtBase.getName(),"1403",mtBase.getName()+""+mtBase.getCode(),null,"2",mtBase.getId());

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }


        return map ;
    }


    /**
     * 获取待选择产品列表
     */
    @RequestMapping("/getPdMtBaseList.do")
    @ResponseBody
    public Map<String,Object> getPdMtBaseList(User user,Integer currPage, Integer pageSize){

        Integer oid= user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }
        Map<String,Object> r=cpsMtService.getPdMtBaseList(oid,currPage,pageSize);

        String code= (String) r.get("code");
        if ("400".equals(code)){
            r.put("totalRows",0);
        }
        return r;
    }


    /**
     * 获取待选择产品列表(无分页)
     */
    @RequestMapping("/getPdMtBaseListNoPage.do")
    @ResponseBody
    public Map<String,Object> getPdMtBaseListNoPage(User user){

        Integer oid= user.getOid();

        List<Map<String,Object>> list=cpsMtService.getPdMtBaseList(oid);

        return returnAllListMap(list);
    }


    /**
     * 获取材料列表
     */
    @RequestMapping("/getMtBaseList.do")
    @ResponseBody
    public Map<String,Object> getMtBaseList(User user,Integer currPage, Integer pageSize){

        Integer oid= user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        List<Integer> codes=new ArrayList<>();
        for (MtCategory m:mtCategories) {
            if("构成商品的原辅材料".equals(m.getName())){

                List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                for (MtCategory m2:l) {
                    codes.add(m2.getId());
                }

            }
        }
        Map<String,Object> map=mtService.getMtList("1",null,codes, currPage,  pageSize);

        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        return map;
    }

    /**
     * 获取材料列表
     */
    @RequestMapping("/getMtBaseListNoPage.do")
    @ResponseBody
    public Map<String,Object> getMtBaseListNoPage(User user){

        Map<String,Object> map=new HashMap<>();
        Integer oid= user.getOid();

        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        List<Integer> codes=new ArrayList<>();
        for (MtCategory m:mtCategories) {
            if("构成商品的原辅材料".equals(m.getName())){

                List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                for (MtCategory m2:l) {
                    codes.add(m2.getId());
                }

            }
        }
        List<Map<String,Object>> list=mtService.getMtList("1",codes);

        map.put("list",list);
        map.put("code",200);
        return map;
    }


    /**
     * 材料与产品绑定   产品多  材料一
     */
    @RequestMapping("/bindOperation.do")
    @ResponseBody
    public Map<String,Object> bindOperation(User user,String baseList,Integer mtId){
        Map<String,Object> map=new HashMap<>();
        
        if(StringUtils.isEmpty(baseList)){
            map.put("code",400);
            map.put("msg","请至少选择一项！");
            return map;
        }
        String res=cpsMtService.bindOperation(baseList,mtId,user);


        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }


        return map ;
    }


    /**
     * id 材料id
     *  材料删除
     */
    @RequestMapping("/deleteMt.do")
    @ResponseBody
    public Map<String,Object> deleteMt(User user,Integer id){
        Map<String,Object> map=new HashMap<>();
        Integer oid= user.getOid();
        String res=cpsMtService.deleteMt(id,oid);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }


        return map ;
    }

    /**
     * id 材料id
     *  材料停用/启动
     */
    @RequestMapping("/startStopMt.do")
    @ResponseBody
    public Map<String,Object> startStopMt(User user,Integer id,String state){
        Map<String,Object> map=new HashMap<>();
        
        String res=cpsMtService.startStopMt(id,state,user);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }


        return map ;
    }

    /**
     * 材料修改
     * @return
     */
    @RequestMapping("/updateMaterial.do")
    @ResponseBody
    public Map<String,Object> updateMaterial(User user, MtBase mtBase){
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid= user.getOid();
        


        if(StringUtils.isEmpty(mtBase.getName())){
            map.put("code",400);
            map.put("msg","请填写名称");
            return map;
        }
        if(StringUtils.isEmpty(mtBase.getCode())){
            map.put("code",400);
            map.put("msg","请填写代号");
            return map;
        }
        if(StringUtils.isEmpty(mtBase.getUnit())){
            map.put("code",400);
            map.put("msg","请填写计量单位");
            return map;
        }




        String res=cpsMtService.updateMt(mtBase,user);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }


        return map ;
    }

    /**
     * 获取已停用材料列表
     */
    @RequestMapping("/getStopMtBaseList.do")
    @ResponseBody
    public Map<String,Object> getStopMtBaseList(User user,Integer currPage, Integer pageSize){

        Integer oid= user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        List<Integer> codes=new ArrayList<>();
        for (MtCategory m:mtCategories) {
            if("构成商品的原辅材料".equals(m.getName())){

                List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                for (MtCategory m2:l) {
                    codes.add(m2.getId());
                }

            }
        }
        Map<String,Object> map=mtService.getStopMtBaseList(codes, currPage,  pageSize);

        return map;
    }

    /**
     * 停用材料查看
     */
    @RequestMapping("/getStopMtBaseData.do")
    @ResponseBody
    public Map<String,Object> getStopMtBaseData(Integer id){


        MtBase mtBase=mtService.getStopMtBaseData(id);

        Map<String,Object> map=new HashMap<>();
        if(mtBase==null){
            map.put("code",400);
            map.put("data",mtBase);
        }else{
            map.put("code",200);
            map.put("data",mtBase);
        }

        return map;
    }
    /**
     * id 材料id
     * 根据材料获取已绑产品列表
     */
    @RequestMapping("/getBindingPdList.do")
    @ResponseBody
    public Map<String,Object> getBindingPdList(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=cpsMtService.getBindingPdList(id, currPage,  pageSize);

        Map<String,Object> before=cpsMtService.getBeforeBindingPdList(id, 1,  1);

        if("400".equals(before.get("code"))){
            map.put("beforeNum",0);
        }else{
            map.put("beforeNum",before.get("totalRows"));
        }
        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        return map;
    }

    /**
     * id 材料id
     * 根据已停用材料获取曾经绑定的产品
     */
    @RequestMapping("/getBeforeBindingPdList.do")
    @ResponseBody
    public Map<String,Object> getBeforeBindingPdList(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=cpsMtService.getBeforeBindingPdList(id, currPage,  pageSize);

        String code= (String) map.get("code");
        if ("400".equals(code)){
            map.put("totalRows",0);
        }
        return map;
    }

    /**
     * id 材料id
     * 根据产品获取材料更换记录
     */
    @RequestMapping("/getMtListByPdBase.do")
    @ResponseBody
    public Map<String,Object> getMtListByPdBase(Integer id,Integer currPage, Integer pageSize){

        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=cpsMtService.getMtListByPdBase(id, currPage,  pageSize);

        return map;
    }

    /**
     * id 材料id
     * 获取材料信息
     */
    @RequestMapping("/getOrderNum.do")
    @ResponseBody
    public Map<String,Object> getOrderNum(Integer id){

        Map<String,Object> map=new HashMap<>();


        map.put("data",cpsMtService.getPoOrderByState(id).size());
        return map;
    }


    private Map<String,Object> returnAllListMap(List list){
        Map<String,Object> map=new HashMap<>();
        map.put("data",list);
        map.put("totalRows",list.size());
        map.put("code",200);


        return map;
    }


    @Scheduled(cron = "0 0 10 * * ?")//每天10点运行
    public void task(){
        //不区分机构
        //昨天创建
        List<Map<String,Object>> orgList=cpsMtService.getOrg();

        for (Map<String,Object> o:orgList){
            //拥有材料管理权限的用户
            List<UserDto> zdList= userPopedomService.getUserByOidMid((Integer) o.get("id"),"zd");
            //拥有材料录入权限的用户
            List<UserDto> zcList= userPopedomService.getUserByOidMid((Integer) o.get("id"),"zc");
            List<Map<String,Object>> tj=cpsMtService.zrtj((Integer) o.get("id"));
            for (Map<String,Object> m :tj) {
                String s=m.get("userName")+"于"+m.get("date")+"录入了"+m.get("sum")+"种材料，请及时在系统中确认其是否需要定点采购。";
                for (UserDto u:zdList) {
                    List<UserSuspendMsg>  l=suspendMsgService.getByContentAndToday(s,u.getUserID());
                    if(l.size()==0){
                        suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),u.getUserID(),null,0);
                    }

                }
            }

            Map<String, Object> m=cpsMtService.zrzj((Integer) o.get("id"));
            String s="系统增加了"+m.get("num")+"种材料，特此告知";
                for (UserDto u:zcList) {
                    List<UserSuspendMsg>  l=suspendMsgService.getByContentAndToday(s,u.getUserID());
                    if(l.size()==0){
                        suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),u.getUserID(),null,0);
                    }

                }

        }


        //昨日停用
        List<Map<String,Object>> stopList=cpsMtService.getOrgForStop();


        for (Map<String,Object> o:stopList){
            List<Map<String,Object>> tj=cpsMtService.zrtytj((Integer) o.get("id"));
            //拥有材料管理权限的用户
            List<UserDto> zdList= userPopedomService.getUserByOidMid((Integer) o.get("id"),"zd");
            //拥有材料录入权限的用户
            List<UserDto> zcList= userPopedomService.getUserByOidMid((Integer) o.get("id"),"zc");

            for (Map<String,Object> m :tj) {
                String s=m.get("userName")+"于"+m.get("date")+"停用了"+m.get("sum")+"种材料，特此告知。";
                for (UserDto u:zdList) {
                    List<UserSuspendMsg>  l=suspendMsgService.getByContentAndToday(s,u.getUserID());
                    if(l.size()==0){
                        suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),u.getUserID(),null,0);
                    }

                }
            }

            for (Map<String,Object> m :tj) {
                for (UserDto u:zcList) {
                    String s=m.get("userName")+"于"+m.get("date")+"停用了"+m.get("sum")+"种材料，特此告知。";
                    List<UserSuspendMsg>  l=suspendMsgService.getByContentAndToday(s,u.getUserID());
                    if(l.size()==0){
                        suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),u.getUserID(),null,0);
                    }

                }
            }

        }


    }
}
