package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import javax.persistence.Basic;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * Created by khmsoft on 2017/6/21.
 */
@Alias("subjectHistory")
public class TAccountantSubjectHistory {
    private int id;
    private Integer subjectId;
    private String subject;
    private Integer org;
    private String parent;
    private String name;
    private Integer category;
    private int level;
    private Integer orders;
    private Byte state;
    private Integer maxChildSubjects;
    private BigDecimal initialAmount;
    private BigDecimal beginningBalance;
    private BigDecimal previousBalance;
    private Date carriedForwardDate;
    private BigDecimal balance;
    private double quantity;
    private String summary;
    private BigDecimal credit;
    private BigDecimal debit;
    private BigDecimal creditAccumulative;
    private BigDecimal debitAccumulative;
    private String beginningDirection;
    private String previousDirection;
    private String balanceDirection;
    private String endDirection;
    private String measureUnit;
    private String memo;
    private Integer creator;
    private String createName;
    private Timestamp createDate;
    private Integer updator;
    private String updateName;
    private Timestamp updateDate;
    private Integer approveItem;
    private String approveStatus;
    private Integer approveLevel;
    private Integer auditor;
    private String auditorName;
    private Timestamp auditDate;
    private String operation;
    private String applyMemo;
    private String approveMemo;
    private Integer messageId;
    private String quantityAssistingAccounting;
    private Double initialQuantity;//初始数量
    private Double beginningQuantity;//年初数量
    private Double previousQuantity;//期初数量
    private Double creditAccumulativeQuantity;//本年累计借方数量
    private Double debitAccumulativeQuantity;//本年累计贷方数量

    @Transient
    private boolean quantityDifferent;//余额数量差异，false-默认值，计算结果和用户输入不同，页面需要标红，true-计算结果与用户输入一致
    @Transient
    private boolean balanceDifferent;//期末余额差异，false-默认值，计算结果和用户输入不同，页面需要标红，true-计算结果与用户输入一致

    private Integer previousId;
    @Basic
    @javax.persistence.Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    private Integer versionNo;
    @Basic
    @javax.persistence.Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    private Integer establish;
    @Basic
    @javax.persistence.Column(name = "establish")
    public Integer getEstablish() {
        return establish;
    }

    public void setEstablish(Integer establish) {
        this.establish = establish;
    }

    public boolean isQuantityDifferent() {
        return quantityDifferent;
    }

    public void setQuantityDifferent(boolean quantityDifferent) {
        this.quantityDifferent = quantityDifferent;
    }

    public boolean isBalanceDifferent() {
        return balanceDifferent;
    }

    public void setBalanceDifferent(boolean balanceDifferent) {
        this.balanceDifferent = balanceDifferent;
    }

    public Double getInitialQuantity() {
        return initialQuantity;
    }

    public void setInitialQuantity(Double initialQuantity) {
        this.initialQuantity = initialQuantity;
    }

    public Double getBeginningQuantity() {
        return beginningQuantity;
    }

    public void setBeginningQuantity(Double beginningQuantity) {
        this.beginningQuantity = beginningQuantity;
    }

    public Double getPreviousQuantity() {
        return previousQuantity;
    }

    public void setPreviousQuantity(Double previousQuantity) {
        this.previousQuantity = previousQuantity;
    }

    public Double getCreditAccumulativeQuantity() {
        return creditAccumulativeQuantity;
    }

    public void setCreditAccumulativeQuantity(Double creditAccumulativeQuantity) {
        this.creditAccumulativeQuantity = creditAccumulativeQuantity;
    }

    public Double getDebitAccumulativeQuantity() {
        return debitAccumulativeQuantity;
    }

    public void setDebitAccumulativeQuantity(Double debitAccumulativeQuantity) {
        this.debitAccumulativeQuantity = debitAccumulativeQuantity;
    }

    public String getQuantityAssistingAccounting() {
        return quantityAssistingAccounting;
    }

    public void setQuantityAssistingAccounting(String quantityAssistingAccounting) {
        this.quantityAssistingAccounting = quantityAssistingAccounting;
    }

    public BigDecimal getCreditAccumulative() {
        return creditAccumulative;
    }

    public void setCreditAccumulative(BigDecimal creditAccumulative) {
        this.creditAccumulative = creditAccumulative;
    }

    public BigDecimal getDebitAccumulative() {
        return debitAccumulative;
    }

    public void setDebitAccumulative(BigDecimal debitAccumulative) {
        this.debitAccumulative = debitAccumulative;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Integer getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Integer subjectId) {
        this.subjectId = subjectId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getMaxChildSubjects() {
        return maxChildSubjects;
    }

    public void setMaxChildSubjects(Integer maxChildSubjects) {
        this.maxChildSubjects = maxChildSubjects;
    }

    public BigDecimal getInitialAmount() {
        return initialAmount;
    }

    public void setInitialAmount(BigDecimal initialAmount) {
        this.initialAmount = initialAmount;
    }

    public BigDecimal getBeginningBalance() {
        return beginningBalance;
    }

    public void setBeginningBalance(BigDecimal beginningBalance) {
        this.beginningBalance = beginningBalance;
    }

    public BigDecimal getPreviousBalance() {
        return previousBalance;
    }

    public void setPreviousBalance(BigDecimal previousBalance) {
        this.previousBalance = previousBalance;
    }

    public Date getCarriedForwardDate() {
        return carriedForwardDate;
    }

    public void setCarriedForwardDate(Date carriedForwardDate) {
        this.carriedForwardDate = carriedForwardDate;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }


    public String getBeginningDirection() {
        return beginningDirection;
    }

    public void setBeginningDirection(String beginningDirection) {
        this.beginningDirection = beginningDirection;
    }

    public String getPreviousDirection() {
        return previousDirection;
    }

    public void setPreviousDirection(String previousDirection) {
        this.previousDirection = previousDirection;
    }

    public String getBalanceDirection() {
        return balanceDirection;
    }

    public void setBalanceDirection(String balanceDirection) {
        this.balanceDirection = balanceDirection;
    }

    public String getEndDirection() {
        return endDirection;
    }

    public void setEndDirection(String endDirection) {
        this.endDirection = endDirection;
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Timestamp getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Timestamp auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TAccountantSubjectHistory that = (TAccountantSubjectHistory) o;

        if (id != that.id) return false;
        if (level != that.level) return false;
        if (subjectId != null ? !subjectId.equals(that.subjectId) : that.subjectId != null) return false;
        if (subject != null ? !subject.equals(that.subject) : that.subject != null) return false;
        if (org != null ? !org.equals(that.org) : that.org != null) return false;
        if (parent != null ? !parent.equals(that.parent) : that.parent != null) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (category != null ? !category.equals(that.category) : that.category != null) return false;
        if (orders != null ? !orders.equals(that.orders) : that.orders != null) return false;
        if (state != null ? !state.equals(that.state) : that.state != null) return false;
        if (maxChildSubjects != null ? !maxChildSubjects.equals(that.maxChildSubjects) : that.maxChildSubjects != null)
            return false;
        if (initialAmount != null ? !initialAmount.equals(that.initialAmount) : that.initialAmount != null)
            return false;
        if (beginningBalance != null ? !beginningBalance.equals(that.beginningBalance) : that.beginningBalance != null)
            return false;
        if (previousBalance != null ? !previousBalance.equals(that.previousBalance) : that.previousBalance != null)
            return false;
        if (carriedForwardDate != null ? !carriedForwardDate.equals(that.carriedForwardDate) : that.carriedForwardDate != null)
            return false;
        if (balance != null ? !balance.equals(that.balance) : that.balance != null) return false;
        if (summary != null ? !summary.equals(that.summary) : that.summary != null) return false;
        if (credit != null ? !credit.equals(that.credit) : that.credit != null) return false;
        if (debit != null ? !debit.equals(that.debit) : that.debit != null) return false;
        if (creditAccumulative != null ? !creditAccumulative.equals(that.creditAccumulative) : that.creditAccumulative != null)
            return false;
        if (debitAccumulative != null ? !debitAccumulative.equals(that.debitAccumulative) : that.debitAccumulative != null)
            return false;
        if (beginningDirection != null ? !beginningDirection.equals(that.beginningDirection) : that.beginningDirection != null)
            return false;
        if (previousDirection != null ? !previousDirection.equals(that.previousDirection) : that.previousDirection != null)
            return false;
        if (balanceDirection != null ? !balanceDirection.equals(that.balanceDirection) : that.balanceDirection != null)
            return false;
        if (measureUnit != null ? !measureUnit.equals(that.measureUnit) : that.measureUnit != null) return false;
        if (memo != null ? !memo.equals(that.memo) : that.memo != null) return false;
        if (creator != null ? !creator.equals(that.creator) : that.creator != null) return false;
        if (createName != null ? !createName.equals(that.createName) : that.createName != null) return false;
        if (createDate != null ? !createDate.equals(that.createDate) : that.createDate != null) return false;
        if (updator != null ? !updator.equals(that.updator) : that.updator != null) return false;
        if (updateName != null ? !updateName.equals(that.updateName) : that.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(that.updateDate) : that.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(that.approveItem) : that.approveItem != null) return false;
        if (approveStatus != null ? !approveStatus.equals(that.approveStatus) : that.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(that.approveLevel) : that.approveLevel != null) return false;
        if (auditor != null ? !auditor.equals(that.auditor) : that.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(that.auditorName) : that.auditorName != null) return false;
        if (auditDate != null ? !auditDate.equals(that.auditDate) : that.auditDate != null) return false;
        if (operation != null ? !operation.equals(that.operation) : that.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(that.applyMemo) : that.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(that.approveMemo) : that.approveMemo != null) return false;
        if (messageId != null ? !messageId.equals(that.messageId) : that.messageId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (subjectId != null ? subjectId.hashCode() : 0);
        result = 31 * result + (subject != null ? subject.hashCode() : 0);
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (parent != null ? parent.hashCode() : 0);
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (category != null ? category.hashCode() : 0);
        result = 31 * result + level;
        result = 31 * result + (orders != null ? orders.hashCode() : 0);
        result = 31 * result + (state != null ? state.hashCode() : 0);
        result = 31 * result + (maxChildSubjects != null ? maxChildSubjects.hashCode() : 0);
        result = 31 * result + (initialAmount != null ? initialAmount.hashCode() : 0);
        result = 31 * result + (beginningBalance != null ? beginningBalance.hashCode() : 0);
        result = 31 * result + (previousBalance != null ? previousBalance.hashCode() : 0);
        result = 31 * result + (carriedForwardDate != null ? carriedForwardDate.hashCode() : 0);
        result = 31 * result + (balance != null ? balance.hashCode() : 0);
        result = 31 * result + (summary != null ? summary.hashCode() : 0);
        result = 31 * result + (credit != null ? credit.hashCode() : 0);
        result = 31 * result + (debit != null ? debit.hashCode() : 0);
        result = 31 * result + (creditAccumulative != null ? creditAccumulative.hashCode() : 0);
        result = 31 * result + (debitAccumulative != null ? debitAccumulative.hashCode() : 0);
        result = 31 * result + (beginningDirection != null ? beginningDirection.hashCode() : 0);
        result = 31 * result + (previousDirection != null ? previousDirection.hashCode() : 0);
        result = 31 * result + (balanceDirection != null ? balanceDirection.hashCode() : 0);
        result = 31 * result + (measureUnit != null ? measureUnit.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        return result;
    }
}
