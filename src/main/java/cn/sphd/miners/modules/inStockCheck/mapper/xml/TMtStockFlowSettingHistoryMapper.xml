<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.inStockCheck.mapper.TMtStockFlowSettingHistoryMapper">
  <resultMap id="BaseResultMap" type="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory">
    <!--@mbg.generated-->
    <!--@Table t_mt_stock_flow_setting_history-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org" jdbcType="INTEGER" property="org" />
    <result column="stock_flow_setting" jdbcType="INTEGER" property="stockFlowSetting" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="_key" jdbcType="TINYINT" property="key" />
    <result column="_value" jdbcType="VARCHAR" property="value" />
    <result column="enable" jdbcType="BOOLEAN" property="enable" />
    <result column="effect_date" jdbcType="DATE" property="effectDate" />
    <result column="effect_state" jdbcType="TINYINT" property="effectState" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="updator" jdbcType="INTEGER" property="updator" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="operation" jdbcType="TINYINT" property="operation" />
    <result column="previous_id" jdbcType="INTEGER" property="previousId" />
    <result column="version_no" jdbcType="INTEGER" property="versionNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org, stock_flow_setting, category, _key, _value, `enable`, effect_date, effect_state, 
    memo, creator, create_name, create_date, updator, update_name, update_date, `operation`, 
    previous_id, version_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_mt_stock_flow_setting_history
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_mt_stock_flow_setting_history
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting_history (org, stock_flow_setting, category, 
      _key, _value, `enable`, 
      effect_date, effect_state, memo, 
      creator, create_name, create_date, 
      updator, update_name, update_date, 
      `operation`, previous_id, version_no
      )
    values (#{org,jdbcType=INTEGER}, #{stockFlowSetting,jdbcType=INTEGER}, #{category,jdbcType=VARCHAR}, 
      #{key,jdbcType=TINYINT}, #{value,jdbcType=VARCHAR}, #{enable,jdbcType=BOOLEAN}, 
      #{effectDate,jdbcType=DATE}, #{effectState,jdbcType=TINYINT}, #{memo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{updator,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{operation,jdbcType=TINYINT}, #{previousId,jdbcType=INTEGER}, #{versionNo,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="org != null">
        org,
      </if>
      <if test="stockFlowSetting != null">
        stock_flow_setting,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="key != null">
        _key,
      </if>
      <if test="value != null">
        _value,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="effectDate != null">
        effect_date,
      </if>
      <if test="effectState != null">
        effect_state,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="previousId != null">
        previous_id,
      </if>
      <if test="versionNo != null">
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="org != null">
        #{org,jdbcType=INTEGER},
      </if>
      <if test="stockFlowSetting != null">
        #{stockFlowSetting,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BOOLEAN},
      </if>
      <if test="effectDate != null">
        #{effectDate,jdbcType=DATE},
      </if>
      <if test="effectState != null">
        #{effectState,jdbcType=TINYINT},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=TINYINT},
      </if>
      <if test="previousId != null">
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory">
    <!--@mbg.generated-->
    update t_mt_stock_flow_setting_history
    <set>
      <if test="org != null">
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="stockFlowSetting != null">
        stock_flow_setting = #{stockFlowSetting,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        _key = #{key,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        _value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BOOLEAN},
      </if>
      <if test="effectDate != null">
        effect_date = #{effectDate,jdbcType=DATE},
      </if>
      <if test="effectState != null">
        effect_state = #{effectState,jdbcType=TINYINT},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=TINYINT},
      </if>
      <if test="previousId != null">
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory">
    <!--@mbg.generated-->
    update t_mt_stock_flow_setting_history
    set org = #{org,jdbcType=INTEGER},
      stock_flow_setting = #{stockFlowSetting,jdbcType=INTEGER},
      category = #{category,jdbcType=VARCHAR},
      _key = #{key,jdbcType=TINYINT},
      _value = #{value,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BOOLEAN},
      effect_date = #{effectDate,jdbcType=DATE},
      effect_state = #{effectState,jdbcType=TINYINT},
      memo = #{memo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      `operation` = #{operation,jdbcType=TINYINT},
      previous_id = #{previousId,jdbcType=INTEGER},
      version_no = #{versionNo,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_mt_stock_flow_setting_history
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.org,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="stock_flow_setting = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.stockFlowSetting,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="category = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.category,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.key,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.value,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`enable` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.enable,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="effect_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.effectDate,jdbcType=DATE}
        </foreach>
      </trim>
      <trim prefix="effect_state = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.effectState,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.memo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updateDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.operation,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="previous_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.previousId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="version_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.versionNo,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_mt_stock_flow_setting_history
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.org != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.org,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="stock_flow_setting = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stockFlowSetting != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.stockFlowSetting,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="category = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.category != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.category,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.key != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.key,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.value != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.value,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`enable` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enable != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.enable,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="effect_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.effectDate != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.effectDate,jdbcType=DATE}
          </if>
        </foreach>
      </trim>
      <trim prefix="effect_state = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.effectState != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.effectState,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.memo != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.memo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createDate != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updator != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDate != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operation != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.operation,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="previous_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.previousId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.previousId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="version_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.versionNo != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.versionNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting_history
    (org, stock_flow_setting, category, _key, _value, `enable`, effect_date, effect_state, 
      memo, creator, create_name, create_date, updator, update_name, update_date, `operation`, 
      previous_id, version_no)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.org,jdbcType=INTEGER}, #{item.stockFlowSetting,jdbcType=INTEGER}, #{item.category,jdbcType=VARCHAR}, 
        #{item.key,jdbcType=TINYINT}, #{item.value,jdbcType=VARCHAR}, #{item.enable,jdbcType=BOOLEAN}, 
        #{item.effectDate,jdbcType=DATE}, #{item.effectState,jdbcType=TINYINT}, #{item.memo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=INTEGER}, #{item.createName,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, 
        #{item.updator,jdbcType=INTEGER}, #{item.updateName,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP}, 
        #{item.operation,jdbcType=TINYINT}, #{item.previousId,jdbcType=INTEGER}, #{item.versionNo,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from t_mt_stock_flow_setting_history where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>
  <update id="updateBatchUseMultiQuery" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update t_mt_stock_flow_setting_history
      <set>
        <if test="item.org != null">
          org = #{item.org,jdbcType=INTEGER},
        </if>
        <if test="item.stockFlowSetting != null">
          stock_flow_setting = #{item.stockFlowSetting,jdbcType=INTEGER},
        </if>
        <if test="item.category != null">
          category = #{item.category,jdbcType=VARCHAR},
        </if>
        <if test="item.key != null">
          _key = #{item.key,jdbcType=TINYINT},
        </if>
        <if test="item.value != null">
          _value = #{item.value,jdbcType=VARCHAR},
        </if>
        <if test="item.enable != null">
          `enable` = #{item.enable,jdbcType=BOOLEAN},
        </if>
        <if test="item.effectDate != null">
          effect_date = #{item.effectDate,jdbcType=DATE},
        </if>
        <if test="item.effectState != null">
          effect_state = #{item.effectState,jdbcType=TINYINT},
        </if>
        <if test="item.memo != null">
          memo = #{item.memo,jdbcType=VARCHAR},
        </if>
        <if test="item.creator != null">
          creator = #{item.creator,jdbcType=INTEGER},
        </if>
        <if test="item.createName != null">
          create_name = #{item.createName,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null">
          create_date = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updator != null">
          updator = #{item.updator,jdbcType=INTEGER},
        </if>
        <if test="item.updateName != null">
          update_name = #{item.updateName,jdbcType=VARCHAR},
        </if>
        <if test="item.updateDate != null">
          update_date = #{item.updateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.operation != null">
          `operation` = #{item.operation,jdbcType=TINYINT},
        </if>
        <if test="item.previousId != null">
          previous_id = #{item.previousId,jdbcType=INTEGER},
        </if>
        <if test="item.versionNo != null">
          version_no = #{item.versionNo,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting_history
    (org, stock_flow_setting, category, _key, _value, `enable`, effect_date, effect_state, 
      memo, creator, create_name, create_date, updator, update_name, update_date, `operation`, 
      previous_id, version_no)
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.org != null">
          #{item.org,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.stockFlowSetting != null">
          #{item.stockFlowSetting,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.category != null">
          #{item.category,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.key != null">
          #{item.key,jdbcType=TINYINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.value != null">
          #{item.value,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.enable != null">
          #{item.enable,jdbcType=BOOLEAN},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.effectDate != null">
          #{item.effectDate,jdbcType=DATE},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.effectState != null">
          #{item.effectState,jdbcType=TINYINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.memo != null">
          #{item.memo,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.creator != null">
          #{item.creator,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createName != null">
          #{item.createName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createDate != null">
          #{item.createDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updator != null">
          #{item.updator,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateName != null">
          #{item.updateName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateDate != null">
          #{item.updateDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.operation != null">
          #{item.operation,jdbcType=TINYINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.previousId != null">
          #{item.previousId,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.versionNo != null">
          #{item.versionNo,jdbcType=INTEGER}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      org,
      stock_flow_setting,
      category,
      _key,
      _value,
      `enable`,
      effect_date,
      effect_state,
      memo,
      creator,
      create_name,
      create_date,
      updator,
      update_name,
      update_date,
      `operation`,
      previous_id,
      version_no,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{org,jdbcType=INTEGER},
      #{stockFlowSetting,jdbcType=INTEGER},
      #{category,jdbcType=VARCHAR},
      #{key,jdbcType=TINYINT},
      #{value,jdbcType=VARCHAR},
      #{enable,jdbcType=BOOLEAN},
      #{effectDate,jdbcType=DATE},
      #{effectState,jdbcType=TINYINT},
      #{memo,jdbcType=VARCHAR},
      #{creator,jdbcType=INTEGER},
      #{createName,jdbcType=VARCHAR},
      #{createDate,jdbcType=TIMESTAMP},
      #{updator,jdbcType=INTEGER},
      #{updateName,jdbcType=VARCHAR},
      #{updateDate,jdbcType=TIMESTAMP},
      #{operation,jdbcType=TINYINT},
      #{previousId,jdbcType=INTEGER},
      #{versionNo,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      org = #{org,jdbcType=INTEGER},
      stock_flow_setting = #{stockFlowSetting,jdbcType=INTEGER},
      category = #{category,jdbcType=VARCHAR},
      _key = #{key,jdbcType=TINYINT},
      _value = #{value,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BOOLEAN},
      effect_date = #{effectDate,jdbcType=DATE},
      effect_state = #{effectState,jdbcType=TINYINT},
      memo = #{memo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      `operation` = #{operation,jdbcType=TINYINT},
      previous_id = #{previousId,jdbcType=INTEGER},
      version_no = #{versionNo,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_stock_flow_setting_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="org != null">
        org,
      </if>
      <if test="stockFlowSetting != null">
        stock_flow_setting,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="key != null">
        _key,
      </if>
      <if test="value != null">
        _value,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="effectDate != null">
        effect_date,
      </if>
      <if test="effectState != null">
        effect_state,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="previousId != null">
        previous_id,
      </if>
      <if test="versionNo != null">
        version_no,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null">
        #{org,jdbcType=INTEGER},
      </if>
      <if test="stockFlowSetting != null">
        #{stockFlowSetting,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BOOLEAN},
      </if>
      <if test="effectDate != null">
        #{effectDate,jdbcType=DATE},
      </if>
      <if test="effectState != null">
        #{effectState,jdbcType=TINYINT},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=TINYINT},
      </if>
      <if test="previousId != null">
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null">
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="stockFlowSetting != null">
        stock_flow_setting = #{stockFlowSetting,jdbcType=INTEGER},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        _key = #{key,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        _value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BOOLEAN},
      </if>
      <if test="effectDate != null">
        effect_date = #{effectDate,jdbcType=DATE},
      </if>
      <if test="effectState != null">
        effect_state = #{effectState,jdbcType=TINYINT},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=TINYINT},
      </if>
      <if test="previousId != null">
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="selectBySettingId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_mt_stock_flow_setting_history
    where stock_flow_setting = #{id,jdbcType=INTEGER}
    order by create_date desc
  </select>

  <select id="selectByValue" resultType="cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting">
    select
    <include refid="Base_Column_List" />
    from t_mt_stock_flow_setting_history
    where stock_flow_setting = #{id,jdbcType=INTEGER}
  </select>
</mapper>