/* ----------------------upload--------------------*/
.Upload{
    width: 100%;
    height: 64px;
    border:1px dashed #ccc;
    margin-bottom:8px;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding:10px;
}
.Upload p{
    color: #666;
}
.col-hr{
    float: left;
    border-left:1px dashed #ccc;
    height: 390px;
    margin-left: 23px;
}
.uploadify-button,.uploadDeleteBtn {
    border: none;
    background-color: #e7e7e7;
    line-height: 12px;
    border-radius: 3px;
    padding: 3px 6px;
    font-size: 12px;
    font-weight: 400;
    color: #535353;
    cursor: pointer;
    text-decoration: none;

}
.uploadify-button:hover{
    background-color: #5d9cec;
    color: #fff;
}
.uploadDeleteBtn:hover{
    background-color: #ed5565;
    color: #fff;
}
.fileType{
    width: 30px;
    height: 40px;
    margin-right: 10px;
}
.up_filename{
    width:200px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}
/* -------------------end--------------------*/
.ty-fileNull{
    height: 128px;
    padding-top: 120px;
    text-align: center;
    background-color: #fff;
    min-height: 593px;
}
.ty-fileNull img{
    width: 80px;
}
.ty-fileNull p{
    color: #aaa;
    font-size: 14px;
}
.seeDetail{
    display: block;
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 3px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
}
.seeDetail:hover{
    color: #fff;
    background-color: #5d9cec;
}

/* --------------------more--------------------*/
ul.hel{
    position: absolute;
    width: 84px;
    right: 0;
    top: 25px;
    padding: 2px 0;
    background-color: #fff;
    box-shadow: 0 0 1px #5d9ded;
    z-index: 2;
}
.ulli{
    float: left;
    width: 100%;
}
.ulli span{
    display: block;
    height: 28px;
    line-height: 28px;
    text-align:left;
    margin:0 2px;
    padding: 0 4px;
    border-radius: 2px;
    transition: all .2s;
}
.ulli:not(.ty-disabled) span{
    color: #5d9cec;
}
.ulli:not(.ty-disabled) span:hover{
    background-color: #5d9cec;
    color: #fff;
}

/* --------------------end--------------------*/
.ty-fileItem > .ty-fileHandle >.handleActive{
    background-color: #5d9cec;
    color:#fff;
}
.ty-fileHandle{
    flex: auto;
    text-align: right;
    position: relative;
}

.ty-searchContent{
    padding-top: 8px;
}
.employeeQuery .searchCon{
    padding: 20px;
}
.employeeQuery input{
    border:1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    width: 100%;
    text-align: left;
    padding: 0 8px;
    color: #3f3f3f;
    height: 36px;
}
.employeeQuery input:focus{
    border:1px solid #5d9cec;
}
.eq_item{
    margin-bottom: 10px;
    padding-bottom: 10px;
}
.eq_item .eq_l{
    float: left;
    width: 20%;
    line-height:2em;
    text-align: right;
    padding-right:20px;
}
.eq_item .eq_r{
    float: left;
    width: 80%;
    line-height:2em;
    white-space: normal;
}
.searchCon{
    margin-top: 20px;
}
.currentFileName,.currentFileNo{
    display: block;
    font-size: 14px;
    color: #5d9cec;
    margin: 12px 0;
}
.changeFileName,.changeFileNo{
    margin: 12px 0;
}
.ty-disabled,.ty-disabled span{
    color: #aaa;
    cursor: default;
}
.ty-disabled:hover,.ty-disabled span:hover{
    cursor: default;
}

/* 文件查看基本信息 */
.infoCon{
    margin: 0 20px;
}
.infoCon>div{
    width:365px;
}
.trItem{
    padding:5px 8px;
}
.trItem .ttl, .trItem .ttl2, .trItem .ttl3{
    display: inline-block;
    color: #6e7c8a;
    vertical-align: top;
}
.trItem .ttl{
    width:80px;
}
.trItem .ttl2{
    width:70px;
}
.trItem .ttl3{
    width:150px;
    text-align: left;
}
.trItem .con{
    display: inline-block;
    word-wrap: break-word;
    word-spacing:normal;
    max-width: 240px;
}
.currentFileName,.currentFileNo{
    word-wrap: break-word;
    word-break:break-all;
}
#docInfoScan{
    width: 690px;
}
#changeDoc , .notZW {
    display: none;
}
.ty-colFileTree .fa-folder+span{
    width:100px;
    display:inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mar{
    width: 1000px;
    flex-grow: 0;
    flex-shrink: 0;
    margin-left: 8px;
}
.ty-fileList{
    margin-top: 8px;
}

.changeHistory{
    border-top: 1px solid #eee;
    margin-top: 8px;
}
.changeHistory button{
    margin-left: 10px;
}
.bounce-uploadFile .ty-right,.bounce-CVuploadFile .ty-right{
    width:48%;
}
.bounce .fa{
    color: #48cfad;
    margin-right: 5px;
    font-size: 14px;
    width:16px;
}
.directSect{
    margin-top:30px;
    width:100%;
}
.clearName{
    position:relative;
}
.clearNameFa{
    position:absolute;
    top:9px;
    right:0;
}
#uploadDirect,#changeDirect{
    font-style:normal;
}
.fileDirectSect,.changeDirectSect{
    margin-top:28px;
}
.ty-secondTab span.sort{
    font-size: 12px;
    font-family: 宋体;
    border-radius: 1px;
    font-weight: bold;
    cursor: pointer;
    color: #48cfad;
    margin-left: 5px;
}
.item-header{
    font-size: 14px;
    font-weight: 700;
    padding-left: 5px;
    border-left: 3px solid #ccc;
    margin: 16px 0;
    line-height: 1;
    color: #606266;
}
.info_title{
    font-weight: bold;
    color: #576471;
    font-size: 16px;
    padding: 5px 8px;
}
.censusInfo .times{
    width: 60px;
}
.info_name{
    display: inline-block;
    width: 80px;
    overflow: hidden;
    vertical-align: top;
    height: 20px;
}
.setBgHeight{min-height:613px;}


/*新的*/
.upload_avatar{
    margin-bottom: 8px;
}
.item-row, .item-column{
    display: flex;
    flex-direction: row;
    margin-bottom: 8px;
    padding: 0 8px;
}
.item-column{
    flex-direction: column;
}
.item-title{
    width: 80px;
    line-height: 30px;
}
.item-title-long{
    width: 280px;
}
.item-content{
    flex-grow: 1;
    position: relative;
}
.item-content .clearInput{
    position: absolute;
    top: 1px;
    right: 0;
    color: #ccc;
    line-height: 30px;
    font-size: 16px;
}
.savePlace{
    color: #444;
    font-size: 13px;
    background-color: #f0f9eb;
    border-radius: 3px;
    line-height: 1.5;
    padding: 4px 8px;
    border: 1px solid #e6e6e6;
}
.savePlace i.fa {
    margin-left: 8px;
    color: #ddc667;
    vertical-align: middle;
}
.text_disabled{
    width: 100%;
    border: 1px solid #dcdfe6;
    background-color: #efefef;
    border-radius: 2px;
    display: inline-block;
    line-height: 1.5;
    box-sizing: border-box;
    color: #606266;
    font-size: inherit;
    outline: none;
    padding: 4px 8px;
}
#fileUpload input.ty-inputText, #folderUpload input.ty-inputText{
    width: 100%;
}
.ty-hr{
    border-bottom: 1px solid #0b9df9;
}
.hr{
    border-bottom: 1px solid #d8e4ee;
    margin: 8px 0;
}

/*文件夹上传限高*/
.k-upload-files{
    max-height:300px;
    overflow-y:scroll;
}
.ty-fileContent{
    display: flex;
    flex-direction: row;
    justify-content:flex-start;
}

#listDoc {
    max-width: 450px;
}
#scanSet { width:760px;  }
#scanSet .departTree > ul, #scanSet .departTree>form{ height:250px;  }
#scanSet .departTree .arrow { line-height:270px  }
#scanSet .departTree{ width:720px;    }
#scanSet .ctrl{ float:right; line-height:30px; color:#0b94ea; cursor: default;  }
#scanSet .ctrl:hover{ text-decoration:underline;   }
.txtR{ text-align: right; }
.btnLink{ color:#0b94ea;cursor:default;     }
.btnLink:hover{ text-decoration:underline;   }
#fileUpload .k-upload-button{
    background-color: #48cfad;
    color: #fff;
    border: none;
    box-shadow: none;
}
#fileUpload .k-upload-button:hover{
    background-color: #3dbf9e;
}
.tipMsg{
    text-align: center;
    margin: 8px 0;
    line-height: 1.5;
}
.ty-fileItem{
    position: relative;
}
.disablePng ,.abolishPng, .reusePng{
    position: absolute;
    top: 0;
    left: 50%;
    width: 75px;
    opacity: 0.6;
}
.ty-hr{
    border-color: #dde3e8;
    margin: 8px 0;
}

.field{
    display: flex;
    flex-direction: row;
    margin-top: 6px;
    padding: 4px 0;
}
.field label{
    width: 80px;
    flex-grow: 0;
    min-height: 5px;
    flex-shrink: 0;
    font-weight: bold;
}
.uploadBg{
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    flex: auto;
}
.file_item{
    width: 80px;
    position: relative;
}
.file_item .fileType{
    width: 36px;
    height: 40px;
    margin: auto;
    background-size:36px;
    background-repeat: no-repeat;
}
.file_item  .file_name {
    font-size: 12px;
    display:inline-block;
    width:100%;
    overflow:hidden;
    height: 50px;
    margin-top: 4px;
    text-align: center;
}
.fileShowList{
    display: flex;
}
.delRole, .delFile{
    transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    display: inline-block;
    font-size: 18px;
    width: 18px;
    height: 18px;
    text-align: center;
    vertical-align: middle;
    line-height: 18px;
    cursor: pointer;
}
.delFile{
    position: absolute;
    right: 5px;
    top: -5px;
}
.item-content .clearInput {
    position: absolute;
    top: 1px;
    right: 0;
    color: #ccc;
    line-height: 30px;
    font-size: 16px;
}
.ty-search .clearInput{
    position: absolute;
    top: 1px;
    right: 35px;
    color: #ccc;
    line-height: 30px;
    font-size: 16px;
}
.ty-switch {
    display: inline-flex;
    align-items: center;
    position: relative;
    font-size: 14px;
    line-height: 20px;
    height: 20px;
    vertical-align: middle;
}
.ty-switch__input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    margin: 0;
}
.ty-switch__label--left {
    margin-right: 10px;
}
.ty-switch__label {
    transition: .2s;
    height: 20px;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    vertical-align: middle;
    color: #303133;
}
.ty-switch__core{
    margin: 0;
    display: inline-block;
    position: relative;
    width: 40px;
    height: 20px;
    border: 1px solid #dcdfe6;
    outline: none;
    border-radius: 10px;
    box-sizing: border-box;
    background: #dcdfe6;
    cursor: pointer;
    transition: border-color .3s,background-color .3s;
    vertical-align: middle;
}
.ty-switch__core::after {
    content: "";
    position: absolute;
    top: 1px;
    left: 1px;
    border-radius: 100%;
    transition: all .3s;
    width: 16px;
    height: 16px;
    background-color: #fff;
}
.ty-switch__label--right {
    margin-left: 10px;
}
.ty-switch__label {
    transition: .2s;
    height: 20px;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    vertical-align: middle;
    color: #101010;
}
.ty-switch.is-checked .ty-switch__core {
    border-color: #5d9cec;
    background-color: #5d9cec;
}
.ty-switch.is-checked .ty-switch__core::after {
    left: 100%;
    margin-left: -17px;
}
.ty-fileItem.abolish{
    border: 1px dashed #ff5562;
    background-color: #ffdadc;
}
.item{  margin: 8px 0;}
.moveSetCon{width: 600px;margin: 0 auto;}
.ty-hr{border-bottom: 1px solid #dae6f0;margin: 12px 0;}
.roleList{height: 76px;background: #fff;padding: 2px;overflow-y: auto;}
.user_avatar{display: inline-block;background-color: #eee;padding: 4px 8px;border-radius: 3px;margin: 4px; cursor: pointer}
.user_avatar i.delUser {color: #ed5565;margin-left: 4px;}
.user_avatar i.addUser {color: #36ab60;margin-left: 4px;}