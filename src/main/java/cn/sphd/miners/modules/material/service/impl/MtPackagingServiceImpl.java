package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.service.impl.UnitUnit;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MtPackagingService;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class MtPackagingServiceImpl implements MtPackagingService {


    @Autowired
    private SrmSupplierDao  srmSupplierDao;

    @Autowired
    private MtBaseDao mtBaseDao;


    @Autowired
    private MtPackagingInfoDao mtPackagingInfoDao;

    @Autowired
    private MtPackagingItemDao mtPackagingItemDao;

    @Autowired
    private MtPackagingItemHistoryDao mtPackagingItemHistoryDao;

    @Autowired
    private MtPackagingStructureDao mtPackagingStructureDao;

    @Autowired
    private MtPackagingStructureHistoryDao mtPackagingStructureHistoryDao;

    @Autowired
    private MtPackagingInfoHistoryDao mtPackagingInfoHistoryDao;

    @Autowired
    private MtBaseHistoryDao mtBaseHistoryDao;

    @Autowired
    private MtWrappageDao mtWrappageDao;

    @Override
    public List<Map<String, Object>> getSupplierByMtBase(Integer mtBaseId,Integer oid) {
        String hql="select new Map(msm.id as supplierMaterial,msm.supplier as supplier,msm.enabled as enabled," +
                " (SELECT COUNT(i.id) FROM MtPackagingInfo i WHERE i.supplierMaterial = msm.id  ) as hnum," +
                " (SELECT COUNT(i.id) FROM MtPackagingInfo i WHERE i.supplierMaterial = msm.id and i.layersCount != 0 and i.enabled=1 ) as num) " +
                " from MtSupplierMaterial as msm where msm.material_ = :material_ ";

        Map param = new HashMap();
        param.put("material_", mtBaseId);
        List<Map<String, Object>> list= mtBaseDao.getListByHQLWithNamedParams(hql, param);

        List<SrmSupplier> suppliers=srmSupplierDao.getListByHQLWithNamedParams("from SrmSupplier where org="+oid,new HashMap<>());
        for (Map<String, Object> m:list ) {
            for (SrmSupplier srmSupplier:suppliers) {
                if(m.get("supplier").equals(srmSupplier.getId())){
                    m.put("fullName",srmSupplier.getFullName());
                    m.put("codeName",srmSupplier.getCodeName());
                    break;
                }
            }
        }

        return list;
    }

    /**
     * @param oid
     * @param keyword
     * @param type     1 已设置 0未设置
     * @param pageInfo
     * @return
     */
    @Override
    public List<Map<String, Object>> getPackagingList(Integer oid, String keyword, int type, PageInfo pageInfo) {

        String hql = "SELECT new Map( " +
                "p.id AS id,\n" +
                "p.name AS name,\n" +
                "p.code as code,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "u.name as unit,\n" +
                "p.unitId as unitId," +
                "p.packagingState as packagingState," +
                "p.createName as createName,\n" +
                "p.createDate as createDate,\n" +
                "p.netWeight as netWeight,\n" +
                "(SELECT COUNT(i.id) FROM MtSupplierMaterial i WHERE i.material = p.id ) as supplierNum," +
                "(SELECT COUNT(i.id) FROM MtPackagingInfo i WHERE i.material = p.id and i.layersCount != 0 and i.enabled=1 ) as num) " +
                "from MtBase p " +
                "LEFT JOIN  MtUnit u on u.id = p.unitId " +
                "where " +
                "p.org=:org and p.enabled=1 ";
        if (type == 1) {
            hql += " and  p.packagingState = 2";
        } else {
            hql+= " and  ( p.packagingState != 2 or  p.packagingState is null) and (SELECT COUNT(i.id) FROM MtSupplierMaterial i WHERE i.material = p.id )>0 ";
        }
        if(StringUtils.isNotEmpty(keyword)){
            hql= hql + " and (p.code like '%"+keyword+"%' or p.name like '%"+keyword+"%')" ;
        }

        Map param = new HashMap();
        param.put("org", oid);
        return mtBaseDao.getListByHQLWithNamedParams(hql, param, pageInfo);
    }

    @Override
    public Object getWszNum(Integer oid) {
        String hql = "SELECT COUNT(0) as num from MtBase p where " +
                "p.org=:org and  p.enabled=1 and (  p.packagingState != 2  or p.packagingState  is null )  and (SELECT COUNT(i.id) FROM MtSupplierMaterial i WHERE i.material = p.id )>0 ";
        Map param = new HashMap();
        param.put("org", oid);
        return mtBaseDao.getByHQLWithNamedParams(hql, param);
    }

    @Override
    public List<MtWrappage> getWrappageList(Integer oid) {
        String hql="from MtWrappage where org=:org and enabled=1";
        Map param = new HashMap();
        param.put("org", oid);
        return mtWrappageDao.getListByHQLWithNamedParams(hql,param);
    }



    @Override
    public void deletePackaging(Long id) {
        mtPackagingInfoDao.deleteById(id);

        List<MtPackagingInfoHistory> allList = mtPackagingInfoDao.getListByHQLWithNamedParams("from MtPackagingInfoHistory where packaging=" + id, new HashMap<>());

        mtPackagingInfoHistoryDao.deleteAll(allList);


    }

    @Override
    @Transactional
    public String addPackaging(List<MtPackagingInfo> list, User user) {

        for (MtPackagingInfo info:list ) {
            info.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));
            Integer material = info.getMaterial();
            List<MtPackagingStructure> mtPackagingStructureList = JSONObject.parseArray(info.getList(), MtPackagingStructure.class);


            if (mtPackagingStructureList == null) {
                mtPackagingStructureList = new ArrayList<>();
            }
            MtBase base = mtBaseDao.get(material);
            if (base == null) {
              throw new RuntimeException("材料不存在");
            }


            if (base.getNetWeight() == null) {
                base.setNetWeight(new BigDecimal(0));
                base.setWeightUnit("1");
            }

//        MtPackagingInfo info=new MtPackagingInfo();
            info.setOrg(user.getOid());
            info.setMaterial(material);
            info.setEnabledTime(new Date());
            info.setEnabled(1);
            info.setCreateDate(new Date());
            info.setCreateName(user.getUserName());
            info.setCreator(user.getUserID());
            info.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));
            info.setOperation(1);

            info.setLayersCount(mtPackagingStructureList.size());

            //无需包装，全部暂停
            if (mtPackagingStructureList.size() == 0) {
                Map<String, Object> p = new HashMap<>();
                p.put("material", material);
                p.put("supplier",info.getSupplier());
                List<MtPackagingInfo> infoList = mtPackagingInfoDao.getListByHQLWithNamedParams("from MtPackagingInfo where material=:material and supplier=:supplier", p);

                if (infoList.size() > 0) {
                    if (base.getPackagingState() == null || base.getPackagingState() == 0 || base.getPackagingState() == 1) {
                        for (MtPackagingInfo info1 : infoList) {
                            mtPackagingInfoDao.delete(info1);
                            mtPackagingInfoHistoryDao.queryHQLWithNamedParams("delete from MtPackagingInfoHistory where packaging =" + info1.getId(), new HashMap<>());
                        }
                    } else {
                        for (MtPackagingInfo info1 : infoList) {
                            info1.setEnabled(0);
                            info1.setEnabledTime(new Date());
                            mtPackagingInfoDao.update(info1);
                        }
                        Map<String, Object> m2 = new HashMap<>();
                        m2.put("material", material);
                        List<MtPackagingInfoHistory> allList = mtPackagingInfoDao.getListByHQLWithNamedParams("from MtPackagingInfoHistory where id in (select max(sh.id) from MtPackagingInfoHistory as sh where sh.material=:material  and sh.effectTime <= current_timestamp() group by sh.packaging ) ", m2);

                        for (MtPackagingInfoHistory hi:allList ) {
                            hi.setEnabled(0);
                            hi.setEnabledTime(new Date());
                            mtPackagingInfoHistoryDao.update(hi);
                        }
                    }
                }

            }else{
                //删除无需包装的数据
                List<MtPackagingInfo> wx = getWxPackagingList(material,info.getSupplierMaterial());
                if(wx.size()>0){
                    for (MtPackagingInfo w : wx) {
                        mtPackagingInfoDao.delete(w);
                        mtPackagingInfoHistoryDao.queryHQLWithNamedParams("delete from MtPackagingInfoHistory where packaging =" + w.getId(), new HashMap<>());
                    }

                }

            }

            for (int i = 0; i < mtPackagingStructureList.size(); i++) {

                if (mtPackagingStructureList.get(i).getItemList() == null) {
                    mtPackagingStructureList.get(i).setItemList(new ArrayList<>());
                }

                mtPackagingStructureList.get(i).setMaterial(material);
                mtPackagingStructureList.get(i).setLevel(i + 1);

                mtPackagingStructureList.get(i).setIsLeaf(0);

//                mtPackagingStructureList.get(i).setLayersCount(mtPackagingStructureList.size());
                mtPackagingStructureList.get(i).setMaterialCount(mtPackagingStructureList.get(i).getItemList().size() + 1);
                mtPackagingStructureList.get(i).setPackagingCount(mtPackagingStructureList.get(i).getMaterialCount());
                mtPackagingStructureList.get(i).setEnabled(1);
                mtPackagingStructureList.get(i).setEnabledTime(info.getCreateDate());
                mtPackagingStructureList.get(i).setCreateDate(info.getCreateDate());
                mtPackagingStructureList.get(i).setCreateName(info.getCreateName());
                mtPackagingStructureList.get(i).setCreator(info.getCreator());


                if (i == 0) {
                    // mtPackagingStructureList.get(i).setParent(0);
                    //添加产品重量
                    setNetWeight(mtPackagingStructureList.get(i), base, null, null);
                    //添加包装重量
                    setGrossWeight(mtPackagingStructureList.get(i));
                } else {
                    // mtPackagingStructureList.get(i).setParent(mtPackagingStructureList.get(i-1).getId());
                    //添加产品重量
                    setNetWeight(mtPackagingStructureList.get(i), base, mtPackagingStructureList.get(i - 1).getNetWeight(), mtPackagingStructureList.get(i - 1).getNetUnitId());
                    //添加包装重量
                    setGrossWeight(mtPackagingStructureList.get(i));
                }
            }

            if (mtPackagingStructureList.size() > 0) {
                //计算产品净重
                info.setNetWeight(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getNetWeight());
                info.setNetUnitId(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getNetUnitId());
                info.setNetUnit(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getNetUnit());

                info.setGrossWeight(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getGrossWeight());
                info.setGrossUnitId(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getGrossUnitId());
                info.setGrossUnit(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getGrossUnit());

                info.setTotalWeight(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getTotalWeight());
                info.setTotalUnitId(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getTotalUnitId());
                info.setTotalUnit(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getTotalUnit());

                info.setMaterialCount(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getMaterialCount());
                info.setPackagingCount(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getPackagingCount());
            }

            mtPackagingInfoDao.save(info);
            MtPackagingInfoHistory infoHistory = mtPackagingInfoHistoryDao.insert(info);

            for (MtPackagingStructure mtPackagingStructure : mtPackagingStructureList) {
                mtPackagingStructure.setPackaging(info.getId());
                mtPackagingStructureDao.save(mtPackagingStructure);
                MtPackagingStructureHistory packagingStructureHistory = mtPackagingStructureHistoryDao.insert(mtPackagingStructure, infoHistory.getId());

                for (MtPackagingItem item : mtPackagingStructure.getItemList()
                ) {
                    item.setPackagingStruct(mtPackagingStructure.getId());
                    mtPackagingItemDao.save(item);


                    mtPackagingItemHistoryDao.insert(item, packagingStructureHistory.getId());
                }

                mtPackagingStructure.getZyPackaging().setPackagingStruct(mtPackagingStructure.getId());
                mtPackagingItemDao.save(mtPackagingStructure.getZyPackaging());


                mtPackagingItemHistoryDao.insert(mtPackagingStructure.getZyPackaging(), packagingStructureHistory.getId());
            }


//        if (pdMerchandise.getPackagingState() == null || pdMerchandise.getPackagingState().intValue() == 0) {
//            pdMerchandise.setPackagingState(2);
//            pdMerchandiseDao.update(pdMerchandise);
//        }
            base.setPackagingState(2);
            mtBaseDao.update(base);
        }

        return "success";
    }

    @Override
    public String updatePackaging(MtPackagingInfo info, List<MtPackagingStructure> mtPackagingStructureList, User user) {


        Integer materialId = info.getMaterial();
        MtBase base = mtBaseDao.get(materialId);
        if (base == null) {
            return "材料信息不存在";
        }


        if (base.getWeightUnit() == null) {
            base.setNetWeight(new BigDecimal(0));
            base.setWeightUnit("1");
        }


        //切断历史数据关联
        Map<String, Object> map = new HashMap<>();
        map.put("packaging", info.getId());
        List<MtPackagingStructure> structureList = mtPackagingStructureDao.getListByHQLWithNamedParams("from MtPackagingStructure where packaging=:packaging and enabled=1", map);
        mtPackagingStructureDao.deleteAll(structureList);

//        MtPackagingInfo info=new MtPackagingInfo();
        info.setOrg(user.getOid());
        info.setMaterial(materialId);
        info.setLayersCount(mtPackagingStructureList.size());

        //无需包装，删除所有的包装
        if (mtPackagingStructureList.size() == 0) {
            Map<String, Object> p = new HashMap<>();
            p.put("material", materialId);
            List<MtPackagingInfo> infoList = mtPackagingStructureDao.getListByHQLWithNamedParams("from MtPackagingInfo where material=:material", p);
            if (infoList.size() > 0) {
                if (base.getPackagingState() == null || base.getPackagingState() == 0 || base.getPackagingState() == 1) {


                    for (MtPackagingInfo info1 : infoList) {
                        mtPackagingInfoDao.delete(info1);
                        mtPackagingInfoHistoryDao.getByHQLWithNamedParams("delete from MtPackagingInfoHistory where packaging =" + info1.getId(), new HashMap<>());
                    }

                } else {
                    for (MtPackagingInfo info1 : infoList) {
                        info1.setEnabled(0);
                        info1.setEnabledTime(new Date());
                        mtPackagingInfoDao.update(info1);
                    }
                    Map<String, Object> m2 = new HashMap<>();
                    m2.put("material", materialId);
                    List<MtPackagingInfoHistory> allList = mtPackagingInfoDao.getListByHQLWithNamedParams("from MtPackagingInfoHistory where id in (select max(sh.id) from MtPackagingInfoHistory as sh where sh.material=:material  and sh.effectTime <= current_timestamp() group by sh.packaging ) ", m2);

                    for (MtPackagingInfoHistory hi:allList ) {
                        hi.setEnabled(0);
                        hi.setEnabledTime(new Date());
                        mtPackagingInfoHistoryDao.update(hi);
                    }
                }
            }
        }else{
            //删除无需包装

            List<MtPackagingInfo> wx = getWxPackagingList(materialId,info.getSupplierMaterial());
            if(wx.size()>0){
                for (MtPackagingInfo w : wx) {
                    mtPackagingInfoDao.delete(w);
                    mtPackagingInfoHistoryDao.queryHQLWithNamedParams("delete from MtPackagingInfoHistory where packaging =" + w.getId(), new HashMap<>());
                }
            }
        }

        for (int i = 0; i < mtPackagingStructureList.size(); i++) {

            if (mtPackagingStructureList.get(i).getItemList() == null) {
                mtPackagingStructureList.get(i).setItemList(new ArrayList<>());
            }

            mtPackagingStructureList.get(i).setMaterial(materialId);
            mtPackagingStructureList.get(i).setLevel(i + 1);


            mtPackagingStructureList.get(i).setIsLeaf(0);

            //mtPackagingStructureList.get(i).setLayersCount(mtPackagingStructureList.size());
            mtPackagingStructureList.get(i).setMaterialCount(mtPackagingStructureList.get(i).getItemList().size() + 1);
            mtPackagingStructureList.get(i).setPackagingCount(mtPackagingStructureList.get(i).getMaterialCount());
            mtPackagingStructureList.get(i).setEnabled(1);
            mtPackagingStructureList.get(i).setEnabledTime(info.getCreateDate());
            mtPackagingStructureList.get(i).setCreateDate(info.getCreateDate());
            mtPackagingStructureList.get(i).setCreateName(info.getCreateName());
            mtPackagingStructureList.get(i).setCreator(info.getCreator());


            if (i == 0) {
                // mtPackagingStructureList.get(i).setParent(0);
                //添加产品重量
                setNetWeight(mtPackagingStructureList.get(i), base, null, null);
                //添加包装重量
                setGrossWeight(mtPackagingStructureList.get(i));
            } else {
                // mtPackagingStructureList.get(i).setParent(mtPackagingStructureList.get(i-1).getId());
                //添加产品重量
                setNetWeight(mtPackagingStructureList.get(i), base, mtPackagingStructureList.get(i - 1).getNetWeight(), mtPackagingStructureList.get(i - 1).getNetUnitId());
                //添加包装重量
                setGrossWeight(mtPackagingStructureList.get(i));
            }
        }

        if (mtPackagingStructureList.size() > 0) {
            //计算产品净重
            info.setNetWeight(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getNetWeight());
            info.setNetUnitId(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getNetUnitId());
            info.setNetUnit(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getNetUnit());

            info.setGrossWeight(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getGrossWeight());
            info.setGrossUnitId(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getGrossUnitId());
            info.setGrossUnit(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getGrossUnit());

            info.setTotalWeight(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getTotalWeight());
            info.setTotalUnitId(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getTotalUnitId());
            info.setTotalUnit(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getTotalUnit());

            info.setMaterialCount(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getMaterialCount());
            info.setPackagingCount(mtPackagingStructureList.get(mtPackagingStructureList.size() - 1).getPackagingCount());


        }

        mtPackagingInfoDao.update(info);
        MtPackagingInfoHistory infoHistory = mtPackagingInfoHistoryDao.insert(info);

        for (MtPackagingStructure mtPackagingStructure : mtPackagingStructureList) {
            mtPackagingStructure.setPackaging(info.getId());
            mtPackagingStructureDao.save(mtPackagingStructure);
            MtPackagingStructureHistory packagingStructureHistory = mtPackagingStructureHistoryDao.insert(mtPackagingStructure, infoHistory.getId());

            for (MtPackagingItem item : mtPackagingStructure.getItemList()
            ) {
                item.setPackagingStruct(mtPackagingStructure.getId());


                mtPackagingItemDao.save(item);
                mtPackagingItemHistoryDao.insert(item, packagingStructureHistory.getId());

            }

            mtPackagingStructure.getZyPackaging().setPackagingStruct(mtPackagingStructure.getId());
            mtPackagingItemDao.save(mtPackagingStructure.getZyPackaging());


            mtPackagingItemHistoryDao.insert(mtPackagingStructure.getZyPackaging(), packagingStructureHistory.getId());

        }


        return "success";
    }

    @Override
    public String updatePackagingInfo(MtPackagingInfo info) {

        mtPackagingInfoDao.update(info);
        return "success";
    }

    @Override
    public MtPackagingInfo getPackagingDetail(Long id) {
        MtPackagingInfo info = mtPackagingInfoDao.get(id);
        Map<String, Object> map = new HashMap<>();
        map.put("packaging", id);
        List<MtPackagingStructure> structureList = mtPackagingStructureDao.getListByHQLWithNamedParams("from MtPackagingStructure where packaging=:packaging and enabled=1", map);

        for (MtPackagingStructure pps : structureList) {

            if (pps.getNetWeight() != null && pps.getNetWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setNetWeight(null);
                pps.setNetUnit(null);
                pps.setNetUnitId(null);
            }

            if (pps.getGrossWeight() != null && pps.getGrossWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setGrossWeight(null);
                pps.setGrossUnit(null);
                pps.setGrossUnitId(null);
            }

            if (pps.getTotalWeight() != null && pps.getTotalWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setTotalWeight(null);
                pps.setTotalUnit(null);
                pps.setTotalUnitId(null);
            }

            Map<String, Object> param = new HashMap<>();
            param.put("packagingStruct", pps.getId());
            List<MtPackagingItem> itemList = mtPackagingItemDao.getListByHQLWithNamedParams("from MtPackagingItem where packagingStruct=:packagingStruct  and enabled=1", param);

            List<Long> itemIds = itemList.stream()
                    .map(MtPackagingItem::getWrappage)
                    .collect(Collectors.toList());
            String ids = StringUtils.join(itemIds, ",");

            //获取包装物
            List<MtWrappage> wrappageList = mtWrappageDao.getListByHQLWithNamedParams("from MtWrappage where id in (" + ids + ")", new HashMap<>());


            List<MtPackagingItem> l = new ArrayList<>();
            for (MtPackagingItem mtPackagingItem : itemList) {

                for (MtWrappage wrappage : wrappageList
                ) {
                    if (mtPackagingItem.getWrappage().longValue() == wrappage.getId().longValue()) {
                        mtPackagingItem.setName(wrappage.getName());
                        mtPackagingItem.setCode(wrappage.getCode());
                        break;
                    }
                }

                if (mtPackagingItem.getIsPrincipal().intValue() == 1) {
                    pps.setZyPackaging(mtPackagingItem);
                } else {
                    l.add(mtPackagingItem);
                }
            }
            pps.setItemList(l);

        }
        info.setStructureList(structureList);
        return info;
    }

    @Override
    public MtPackagingInfo getPackaging(Long id) {
        return mtPackagingInfoDao.get(id);
    }

    @Override
    public List<MtPackagingInfoHistory> getPackagingList(Integer mtBaseId,Integer supplierMaterial) {

        MtBase base = mtBaseDao.get(mtBaseId);

        Map<String, Object> map = new HashMap<>();
        map.put("material", mtBaseId);
        map.put("supplierMaterial", supplierMaterial);

        List<MtPackagingInfoHistory> allList = mtPackagingInfoDao.getListByHQLWithNamedParams("from MtPackagingInfoHistory where id in (select max(sh.id) from MtPackagingInfoHistory as sh where sh.material=:material and supplierMaterial=:supplierMaterial  and sh.effectTime <= current_timestamp() group by sh.packaging ) ", map);

        Map<String, Object> param1 = new HashMap<>();
        param1.put("material", mtBaseId);
        List<MtPackagingStructureHistory> sl = mtPackagingStructureDao.getListByHQLWithNamedParams("from MtPackagingStructureHistory where material=:material and enabled=1", param1);

        List<MtPackagingStructureHistory> structureList=new ArrayList<>();

        for (MtPackagingStructureHistory sh:sl ) {
            for (MtPackagingInfoHistory ih:allList ) {
                if(sh.getPackaging().longValue()==ih.getId()){
                    structureList.add(sh);
                    break;
                }
            }
        }


        for (MtPackagingInfoHistory info : allList) {


            List<MtPackagingStructureHistory> structures = new ArrayList<>();
            for (MtPackagingStructureHistory pps : structureList) {


                if (pps.getNetWeight() != null && pps.getNetWeight().compareTo(BigDecimal.ZERO) == 0) {
                    pps.setNetWeight(null);
                    pps.setNetUnit(null);
                    pps.setNetUnitId(null);
                }

                if (pps.getGrossWeight() != null && pps.getGrossWeight().compareTo(BigDecimal.ZERO) == 0) {
                    pps.setGrossWeight(null);
                    pps.setGrossUnit(null);
                    pps.setGrossUnitId(null);
                }

                if (pps.getTotalWeight() != null && pps.getTotalWeight().compareTo(BigDecimal.ZERO) == 0) {
                    pps.setTotalWeight(null);
                    pps.setTotalUnit(null);
                    pps.setTotalUnitId(null);
                }

                if (pps.getPackaging().intValue() == info.getId()) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("packagingStruct", pps.getId());
                    List<MtPackagingItemHistory> itemList = mtPackagingItemDao.getListByHQLWithNamedParams("from MtPackagingItemHistory where packagingStruct=:packagingStruct  and enabled=1", param);

                    List<Long> itemIds = itemList.stream()
                            .map(MtPackagingItemHistory::getWrappage)
                            .collect(Collectors.toList());
                    String ids = StringUtils.join(itemIds, ",");

                    //获取包装物
                    List<MtWrappage> wrappageList = mtWrappageDao.getListByHQLWithNamedParams("from MtWrappage where id in (" + ids + ")", new HashMap<>());


                    List<MtPackagingItemHistory> l = new ArrayList<>();
                    for (MtPackagingItemHistory mtPackagingItem : itemList) {

                        MtPackagingItemHistory pi = new MtPackagingItemHistory();
                        BeanUtils.copyProperties(mtPackagingItem, pi);

                        for (MtWrappage wrappage : wrappageList
                        ) {
                            if (mtPackagingItem.getWrappage().longValue() == wrappage.getId()) {


                                pi.setName(wrappage.getName());
                                pi.setCode(wrappage.getCode());
                                pi.setSpecifications(wrappage.getSpecifications());
                                pi.setModel(wrappage.getModel());
                                pi.setUnit(wrappage.getUnit());
                                pi.setUnitId(wrappage.getUnitId());
                                pi.setWeight(UnitUnit.milligramToBest(wrappage.getPieceWeight()));
                                pi.setWeightUnit(UnitUnit.milligramToUnit(wrappage.getPieceWeight()));
                                pi.setWrappage(wrappage.getId());


                                break;
                            }
                        }
                        if (pi.getIsPrincipal() == 1) {
                            pps.setZyPackaging(pi);
                        } else {
                            l.add(pi);
                        }
                    }
                    pps.setItemList(l);
                    structures.add(pps);
                }
            }

            info.setStructureList(structures);
            info.setUnit(base.getUnit());
        }


        return allList;
    }

    @Override
    public List<MtPackagingInfoHistory> getPackagingRecordList(Long id) {
        String hql = "from MtPackagingInfoHistory where packaging=:packaging";
        Map<String, Object> param = new HashMap<>();
        param.put("packaging", id);
        return mtPackagingInfoHistoryDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public MtPackagingInfoHistory getPackagingRecordDetail(Long recordId) {
        MtPackagingInfoHistory infoHistory = mtPackagingInfoHistoryDao.get(recordId);
        Map<String, Object> map = new HashMap<>();
        map.put("packaging", recordId);
        List<MtPackagingStructureHistory> structureList = mtPackagingStructureDao.getListByHQLWithNamedParams("from MtPackagingStructureHistory where packaging=:packaging and enabled=1", map);

        for (MtPackagingStructureHistory pps : structureList) {

            if (pps.getNetWeight() != null && pps.getNetWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setNetWeight(null);
                pps.setNetUnit(null);
                pps.setNetUnitId(null);
            }

            if (pps.getGrossWeight() != null && pps.getGrossWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setGrossWeight(null);
                pps.setGrossUnit(null);
                pps.setGrossUnitId(null);
            }

            if (pps.getTotalWeight() != null && pps.getTotalWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setTotalWeight(null);
                pps.setTotalUnit(null);
                pps.setTotalUnitId(null);
            }


            Map<String, Object> param = new HashMap<>();
            param.put("packagingStruct", pps.getId());
            List<MtPackagingItemHistory> itemList = mtPackagingItemDao.getListByHQLWithNamedParams("from MtPackagingItemHistory where packagingStruct=:packagingStruct  and enabled=1", param);

            List<Long> itemIds = itemList.stream()
                    .map(MtPackagingItemHistory::getWrappage)
                    .collect(Collectors.toList());
            String ids = StringUtils.join(itemIds, ",");

            //获取包装物
            List<MtWrappage> wrappageList = mtWrappageDao.getListByHQLWithNamedParams("from MtWrappage where id in (" + ids + ")", new HashMap<>());


            List<MtPackagingItemHistory> l = new ArrayList<>();
            for (MtPackagingItemHistory mtPackagingItem : itemList) {
                MtPackagingItemHistory pi = new MtPackagingItemHistory();
                BeanUtils.copyProperties(mtPackagingItem, pi);
                for (MtWrappage wrappage : wrappageList
                ) {
                    if (mtPackagingItem.getWrappage().intValue() == wrappage.getId()) {
                        pi.setName(wrappage.getName());
                        pi.setCode(wrappage.getCode());
                        pi.setSpecifications(wrappage.getSpecifications());
                        pi.setModel(wrappage.getModel());
                        pi.setUnit(wrappage.getUnit());
                        pi.setUnitId(wrappage.getUnitId());
                        pi.setWeight(UnitUnit.milligramToBest(wrappage.getPieceWeight()));
                        pi.setWeightUnit(UnitUnit.milligramToUnit(wrappage.getPieceWeight()));
                        pi.setWrappage(wrappage.getId());
                        break;
                    }
                }

                if (pi.getIsPrincipal().intValue() == 1) {
                    pps.setZyPackaging(pi);
                } else {
                    l.add(pi);
                }
            }
            pps.setItemList(l);

        }
        infoHistory.setStructureList(structureList);
        return infoHistory;
    }

    @Override
    public List<MtPackagingInfo> getWxPackagingList(Integer material,Integer supplierMaterial) {

        Map<String, Object> map = new HashMap<>();
        map.put("material", material);
        map.put("supplierMaterial", supplierMaterial);
        List<MtPackagingInfo> allList = mtPackagingInfoDao.getListByHQLWithNamedParams("from MtPackagingInfo where material=:material and supplierMaterial =:supplierMaterial and layersCount=0  and  enabled=1", map);

        return allList;
    }

    @Override
    public String addBzw(MtWrappage wrappage) {

        mtWrappageDao.save(wrappage);
        return "success";
    }


    //添加产品重量
    private void setNetWeight(MtPackagingStructure mtPackagingStructure, MtBase base, BigDecimal parentNetWeight, Integer parentWeightUnit) {
        BigDecimal a = new BigDecimal(0);
        if (parentNetWeight == null) {
            a = UnitUnit.toMilligram(base.getNetWeight().multiply(new BigDecimal(mtPackagingStructure.getMaterialCount())), base.getWeightUnit());
        } else {
            a = UnitUnit.toMilligram(parentNetWeight, parentWeightUnit + "").multiply(new BigDecimal(mtPackagingStructure.getMaterialCount()));

        }

        mtPackagingStructure.setNetWeight(UnitUnit.milligramToBest(a));
        mtPackagingStructure.setNetUnitId(Integer.valueOf(UnitUnit.milligramToUnit(a)));
        mtPackagingStructure.setNetUnit(UnitUnit.milligramToUnitName(a));
    }

    //添加包装毛重
    private void setGrossWeight(MtPackagingStructure mtPackagingStructure) {
        //获取辅助包装
        List<MtPackagingItem> items = mtPackagingStructure.getItemList();

        //辅助材料总重量
        BigDecimal sum = new BigDecimal(0);
        if (items != null) {
            for (MtPackagingItem item : items) {
                item.setOrg(mtPackagingStructure.getOrg());
                item.setCreateDate(mtPackagingStructure.getCreateDate());
                item.setCreateName(mtPackagingStructure.getCreateName());
                item.setCreator(mtPackagingStructure.getCreator());
                item.setPackagingStruct(mtPackagingStructure.getId());
                item.setEnabled(1);
                item.setEnabledTime(mtPackagingStructure.getCreateDate());
                item.setIsPrincipal(0);

//                if (item.getRatedAmout() == null) {
//                    item.setWeight(new BigDecimal(0));
//                }



                BigDecimal a = UnitUnit.toMilligram(item.getWeight(), item.getWeightUnit() + "");

                sum = sum.add(a);

//                item.setStockWeight(UnitUnit.milligramToBest(a));
//                item.setStockWeightUnitId(Integer.valueOf(UnitUnit.milligramToUnit(a)));
//                item.setStockWeightUnit(UnitUnit.milligramToUnitName(a));


            }
        }

        //计算主要材料毛重
        MtPackagingItem zy = mtPackagingStructure.getZyPackaging();

        if (zy.getWeight() == null) {
            zy.setWeight(new BigDecimal(0));
            zy.setWeightUnit("1");
        }


        BigDecimal a = UnitUnit.toMilligram(zy.getWeight(), zy.getWeightUnit() + "");
        sum = sum.add(a);
//        zy.setStockWeight(UnitUnit.milligramToBest(a));
//        zy.setStockWeightUnitId(Integer.valueOf(UnitUnit.milligramToUnit(a)));
//        zy.setStockWeightUnit(UnitUnit.milligramToUnitName(a));
        zy.setOrg(mtPackagingStructure.getOrg());
        zy.setCreateDate(mtPackagingStructure.getCreateDate());
        zy.setCreateName(mtPackagingStructure.getCreateName());
        zy.setCreator(mtPackagingStructure.getCreator());
        zy.setPackagingStruct(mtPackagingStructure.getId());
        zy.setEnabled(1);
        zy.setEnabledTime(mtPackagingStructure.getCreateDate());
        zy.setIsPrincipal(1);


        mtPackagingStructure.setGrossWeight(UnitUnit.milligramToBest(sum));
        mtPackagingStructure.setGrossUnitId(Integer.valueOf(UnitUnit.milligramToUnit(sum)));
        mtPackagingStructure.setGrossUnit(UnitUnit.milligramToUnitName(sum));

        BigDecimal s = UnitUnit.toMilligram(mtPackagingStructure.getNetWeight(), mtPackagingStructure.getNetUnitId() + "").add(UnitUnit.toMilligram(mtPackagingStructure.getGrossWeight(), mtPackagingStructure.getGrossUnitId() + ""));
        mtPackagingStructure.setTotalWeight(UnitUnit.milligramToBest(s));
        mtPackagingStructure.setTotalUnitId(Integer.valueOf(UnitUnit.milligramToUnit(s)));
        mtPackagingStructure.setTotalUnit(UnitUnit.milligramToUnitName(s));


    }

}
