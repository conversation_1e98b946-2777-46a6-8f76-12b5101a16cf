package cn.sphd.miners.modules.accountant.service;

import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectHistory;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectPeroid;

import java.text.ParseException;
import java.util.List;

/**
 * Created by root on 17-5-8.
 */
public interface AccountBookManagerService {


    List<TAccountantSubjectHistory> getAllSubjects(int oid);

    List<TAccountantSubjectPeroid> getSubjectLedger(TAccountantSubjectHistory subjectEntity, int flag, String queryTime) throws ParseException;
}
