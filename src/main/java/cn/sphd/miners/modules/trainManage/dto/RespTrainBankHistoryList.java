package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBank;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBankHistory;

import java.util.List;

/**
 * @ClassName RespTrainBankHistory
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/19 11:04
 * @Version 1.0
 */
public class RespTrainBankHistoryList {
    private TrainingQuestionBank trainingQuestionBank;
    private List<TrainingQuestionBankHistory> trainingQuestionBankHistoryList;

    public TrainingQuestionBank getTrainingQuestionBank() {
        return trainingQuestionBank;
    }

    public void setTrainingQuestionBank(TrainingQuestionBank trainingQuestionBank) {
        this.trainingQuestionBank = trainingQuestionBank;
    }

    public List<TrainingQuestionBankHistory> getTrainingQuestionBankHistoryList() {
        return trainingQuestionBankHistoryList;
    }

    public void setTrainingQuestionBankHistoryList(List<TrainingQuestionBankHistory> trainingQuestionBankHistoryList) {
        this.trainingQuestionBankHistoryList = trainingQuestionBankHistoryList;
    }
}
