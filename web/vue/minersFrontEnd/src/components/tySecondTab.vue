<!--
demo子组件
----desc：该子组件不是全局子组件
----pages（用到的页面）：['/views/generalAffairs/employeeIndex.vue']
-->
<template>
 <div class="tySecondTab">
   <div class="ttl">
     <span v-for="(item, index) in namelist"><span class="tab" :class="{tabIndex:tabIndex==index}" v-html="item" @click="changeTabIndex(index)"></span></span>
   </div>
 </div>
</template>
<script>

export default {
  name: "tySecondTab",
  data() {
    return {
      tabIndex: 0
    };
  },
  props:{
    namelist: {type: Array, default:[]},
    changeTabFun: {type: Function, default: (tableIndex) => {}}
  },
  methods: {
    changeTabIndex(index) {
      if(this.tabIndex != index) {
        this.tabIndex = index
        this.changeTabFun(index)
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.tySecondTab{
  .ttl{
    border-bottom:1px solid #eaeaea;  font-size:14px; line-height:50px; height:50px;
    .tab {
      list-style:none; display:inline-block; height:50px; padding:0 20px; cursor:pointer;
    }
    .tabIndex {
      color:#48cfad; font-weight: bold;
      border-bottom:2px solid $tyBounce-color-green
    }
  }
}
</style>