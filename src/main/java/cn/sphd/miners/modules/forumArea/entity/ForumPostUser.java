package cn.sphd.miners.modules.forumArea.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-02-1
 * @description
 **/
@Entity
@Table(name = "t_forum_post_user")
public class ForumPostUser extends BaseEntity {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "post", nullable = false)
  private Integer post;
  @Column(name = "userID", nullable = false)
  private Integer userID;
  @Column(name = "username")
  private String userName;
  @Column(name = "has_read")
  private Integer hasRead;
  @Column(name = "memo")
  private String memo;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_date")
  private Date createDate;
  @Column(name = "updator")
  private Date updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_date")
  private Date updateDate;
  @Column(name = "approve_item")
  private Integer approveItem;
  @Column(name = "approve_status")
  private String approveStatus;
  @Column(name = "approve_level")
  private Integer approveLevel;
  @Column(name = "auditor")
  private Integer auditor;
  @Column(name = "auditor_name")
  private String auditorName;
  @Column(name = "audit_date")
  private Date auditDate;
  @Column(name = "operation")
  private String operation;
  @Column(name = "apply_memo")
  private String applyMemo;
  @Column(name = "approve_memo")
  private String approveMemo;
  @Column(name = "message_id")
  private Integer messageId;
  @Column(name = "org")
  private Integer org;
  @Column(name = "previous_id")
  private Integer previousId;
  @Column(name = "version_no")
  private Integer versionNo;

//  @ManyToOne(fetch= FetchType.EAGER )
//  @JoinColumn(name="post", referencedColumnName = "id" , nullable=false , unique=false , insertable=false, updatable=false)
  @JsonIgnore @JSONField(serialize = false)
  @ManyToOne(fetch=FetchType.LAZY)//{CascadeType.MERGE, CascadeType.REFRESH}
  @JoinColumn(name = "post", referencedColumnName = "id", insertable=false, updatable=false)
  private ForumPost forumPost;

  @Transient
  private String imgPath;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getPost() {
    return post;
  }

  public void setPost(Integer post) {
    this.post = post;
  }

  public Integer getUserID() {
    return userID;
  }

  public void setUserID(Integer userID) {
    this.userID = userID;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public Integer getHasRead() {
    return hasRead;
  }

  public void setHasRead(Integer hasRead) {
    this.hasRead = hasRead;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Date getUpdator() {
    return updator;
  }

  public void setUpdator(Date updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getApproveItem() {
    return approveItem;
  }

  public void setApproveItem(Integer approveItem) {
    this.approveItem = approveItem;
  }

  public String getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(String approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApproveLevel() {
    return approveLevel;
  }

  public void setApproveLevel(Integer approveLevel) {
    this.approveLevel = approveLevel;
  }

  public Integer getAuditor() {
    return auditor;
  }

  public void setAuditor(Integer auditor) {
    this.auditor = auditor;
  }

  public String getAuditorName() {
    return auditorName;
  }

  public void setAuditorName(String auditorName) {
    this.auditorName = auditorName;
  }

  public Date getAuditDate() {
    return auditDate;
  }

  public void setAuditDate(Date auditDate) {
    this.auditDate = auditDate;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public ForumPost getForumPost() {
    return forumPost;
  }

  public void setForumPost(ForumPost forumPost) {
    this.forumPost = forumPost;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public Integer getPreviousId() {
    return previousId;
  }

  public void setPreviousId(Integer previousId) {
    this.previousId = previousId;
  }

  public Integer getVersionNo() {
    return versionNo;
  }

  public void setVersionNo(Integer versionNo) {
    this.versionNo = versionNo;
  }

  public String getImgPath() {
    return imgPath;
  }

  public void setImgPath(String imgPath) {
    this.imgPath = imgPath;
  }
}
