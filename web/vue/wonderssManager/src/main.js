import { createApp } from 'vue'
import App from '@/App.vue'
import auth from '@/sys/auth'
import router from '@/router'
import sphdSocket from '@/sys/sphd'
import store from '@/store/index'
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import "font-awesome/css/font-awesome.min.css"
import moment from 'moment'
import 'moment/dist/locale/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import "@/style/common.scss"
import PageHeader from "@/components/PageHeader.vue"
import "@/utils/formatDate.js"
auth.init({isGuest: true})
sphdSocket.start()

moment.defaultFormat = "YYYY-MM-DD HH:mm:ss.SSS"
const app = createApp(App)
    .use(router).use(store)
    .use(ElementPlus, { locale: zhCn })

app.component('PageHeader',PageHeader)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.config.globalProperties.$filter = {
    format(value, type) {
        let format = "YYYY-MM-DD HH:mm:ss"
        if (type) {
            if (type === 'day') {
                format = "YYYY-MM-DD"
            } else {
                format = type
            }
        }
        if (!value) {
            return ''
        } else {
            return moment(value).format(format)
        }
    }
}
app.mount('#app')





