
td span.radioBtn>.radioShow { top:-2px;  }
td span.radioBtn { top:1px;  }

.btn{ border:1px solid #ccc; border-radius:4px;     }

/*bounce*/
#detailKind_1 , #detailKind_2 , #detailKind_3 , #detailKind_4 , #detailKind_5 , #mtTip , #detailKind_3_{ width:800px;  }
.bonceCon1{   padding:20px 15px; background:#fff;  }

.itemTr{ padding:5px 0; position:relative;     }
.itemTr>span.ttl{ display: inline-block ; width:200px; text-align:right ; padding-right:10px;line-height:30px; position: absolute; top:5px;  }
.con{ display: inline-block ; line-height:30px; width:350px; position:relative; margin-left:200px;    }
.scan{ display:block ;  }
 .update{ display: inline-block }
.scan ,.update{ width:350px; border:1px solid #ccc; padding-left:10px; height:30px; overflow:hidden ;   }
.updateRadio{ width:350px; padding-left:10px; height:30px;    }
.mark{ background:rgba(0,0,0,0.15); position:absolute; width:100%; height:100%; z-index:1;           }


.applyTtl{ text-align: right ;font-weight:bold;       }


.ok-icon { background: url(../user/icon/ok.png) no-repeat center center;  }
.no-icon { background: url(../user/icon/no.png) no-repeat center center;  }
.wait-icon { background: url(../user/icon/wait.png) no-repeat center center;  }
.ok-icon , .no-icon , .wait-icon { background-size:25px 25px; display:inline-block; width:25px; height:25px;margin-right:15px;    }
.processTr{ padding-top:5px;      }
.touserInfo{ margin-right:30px;    }
.touserInfo , .timeInfo{ position:relative; top:-8px;    }
.memoInfo{  padding:5px 0 5px 50px;   }
.noProcess{ color:#E7505A;  }

.applyYes{ background: rgb(224, 235, 249);   }
.applyNo{ background: rgb(254, 231, 232);  }
.dealSee_continer{ margin:20px 0 ; padding:30px; color:#3598dc;   }
.dealSee_continer .ttl { font-size:20px;     }

.approvCon{ padding: 20px 30px; color: #3598dc;           }

.scantd{ padding-left:10px; line-height:30px;  }

#info_images>img{ height:50px;      }
#bigImagcon{ position:absolute;  bottom: 100px; left:100px; display:none;      }
#bigImag{ display: block; width:650px; box-shadow:0 0 2px #000; }


















