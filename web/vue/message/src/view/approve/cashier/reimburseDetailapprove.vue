<template><!-- 日常事务报销审批跳转的页面，与财务无关的页面 -->
  <div id="reimburseDetailapprove"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="报销审批"></TY_NavTop>
    <div class="container" v-if="code !== '23'">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div class="panel-title">
        {{personnelReimburse.createName}} 申请报销 {{personnelReimburse.amount}} 元  <div class="right">{{personnelReimburse.transactionType | isSaler}}</div>
      </div>
      <div class="panel-content">
        <div class="item-header">报销事由</div>
        <div>事件的发生日期：{{personnelReimburse.beginDate | formatDay('YYYY年MM月DD日')}} - {{personnelReimburse.endDate | formatDay('DD日')}}</div>
        <div>
          {{personnelReimburse.purpose}}
        </div>
      </div>
      <div class="panel-content">
        <div class="text-right" v-on:click="toggle = -toggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
        <div v-show="toggle > 0">
          <div class="item-content">
            <div class="processItem">
              <div>提交报销申请</div>
              <div>申请人</div>
              <div>{{personnelReimburse.createName | stringSplit(4)}} {{personnelReimburse.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
            <div v-for="it in approvalProcessList" v-bind:key="it.id">
              <div v-if="it.approveStatus === '2' && !it.businessType" class="processItem" >
                <div>在线审批通过</div>
                <div>审批人</div>
                <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-if="it.approveStatus === '2' && it.businessType == 32" class="processItem">
                <div>在线审核通过</div>
                <div>出纳员</div>
                <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <!-- 最后一条审批记录展示 -->
            <div class="color-red" v-if="nowApproveItem.approveStatus === '3'">
              <div style="margin-top:15px;">被驳回！</div>
              <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
              <div class="processItem">
                <div></div>
                <div>审批人</div>
                <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="color-red" v-if="nowApproveItem.approveStatus === '8'">
              <div style="margin-top:15px;">被驳回！</div>
              <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
              <div class="processItem">
                <div></div>
                <div>出纳员</div>
                <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="color-blue">
              <!--<span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && !nowApproveItem.businessType"><b>{{nowApproveItem.userName}}</b>为本申请的下一个审批人</span>-->
              <span v-if="complateApprove">报销申请在线审批已完成。</span>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-title">
        本次报销共有票据 {{personnelReimburse.billQuantity}} 张，合计 {{personnelReimburse.billAmount}} 元
      </div>
      <div class="panel-content">
        <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
          <el-tab-pane label="按票据查看" name="bill">
            <div class="ui-cells_none">
              <a class="ui-cell" @click="goBillInfo(item)" v-for="item in listMapBillCat" v-bind:key="item.billCat">
                <div class="ui-cell__bd">
                  <div class="ui-cell_con">
                    <el-row>
                      <el-col :span="12">{{item.billCatName}}</el-col>
                      <el-col :span="6">{{item.num}}张</el-col>
                      <el-col :span="6">{{item.totalAmount}} 元</el-col>
                    </el-row>
                  </div>
                  <div class="ui-cell__ft"></div>
                </div>
              </a>
            </div>
          </el-tab-pane>
          <el-tab-pane label="按费用查看" name="feeCat">
            <a class="ui-cell" @click="goFeeInfo(item)"  v-for="item in listMapFeeCat" v-bind:key="item.feeCat">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <el-row>
                    <el-col :span="12">{{item.feeCatName}}</el-col>
                    <el-col :span="6">{{item.secondFeeCatName | chargeNull}}</el-col>
                    <el-col :span="6">{{item.totalAmount}} 元</el-col>
                  </el-row>
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <div class="ui-cells_none">
          <div v-if="isNowApprover">
            <!--正常审批-->
            <a class="ui-cell" v-if="approveStatus === '1' && code === '11'">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div class="handle_button">
                    <button class="ui-btn" @click="approve('approvalReimburse', 2)">驳回</button>
                    <button class="ui-btn ui-btn_info" @click="approve('approvalReimburse', 1)">批准</button>
                  </div>
                </div>
              </div>
            </a>
            <!--财务审批-->
            <a class="ui-cell" v-if="approveStatus === '6' && code === '21'">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>
                    <p>请核对本次报销实际票据与系统中的票据。</p>
                    <div>
                      <el-radio-group v-model="approveSta" size="small">
                        <el-radio label="1">票据ok，现申请付款。</el-radio><br/>
                        <el-radio label="2">实际票据与系统中的不符，驳回报销申请。</el-radio><br/>
                        <el-radio label="3">由于票据或其他原因，驳回报销申请。</el-radio><br/>
                      </el-radio-group>
                    </div>
                  </div>
                  <div class="handle_button" style="text-align: center; margin:0 30px; ">
                    <!--<button class="ui-btn" @click="approve('cashierHandleApproval', 2)">驳回</button>-->
                    <button class="ui-btn ui-btn_info" style="width:300px; " @click="approve('cashierHandleApproval', 1)">确 定</button>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #reimburseDetailapprove{
    .processItem{
      display: flex;
      font-size: 11px;
      margin-left:-5px;
      div:nth-child(1){ text-align: left; flex: 1 }
      div:nth-child(2){ text-align: center; flex: 1 }
      div:nth-child(3) { text-align: right; flex: 2 }
    }
    .handle_btn .ui-btn{
      padding: 6px 12px;
      font-size: 12px;
      line-height: 1;
      height: inherit;
      border-radius: 3px;
      width: auto;
    }
    .tipBlue{
      color:#0a4d85; text-align: center;
    }
  }
</style>

<script>
import { isSaler, chargeNull } from '../../../js/common'
import rejectPng from '../../../images/reject.png'
export default {
  name: 'approveReimburseDetail2',
  data () {
    return {
      isReject: false, // 判断是否驳回来显示驳回图片
      isNowApprover: false,
      rejectPng: rejectPng, // 驳回图片
      twoApproval: false,
      tips: false,
      tipContent: '',
      approveSta: '',
      loading: true,
      toggle: -1,
      code: '',
      approveStatus: '0',
      personnelReimburse: {},
      approvalProcessList: [],
      listMapBillCat: {},
      listMapFeeCat: {},
      num: 0,
      financeReimburseBills: [],
      isSpecialBill: 0,
      isPublic: '',
      nowApproveItem: {
        'approveStatus': '0'
      },
      square: {},
      bankList: [],
      listenersUid: [],
      rules: {
        payMethod: [
          { required: true }
        ],
        financeAccountId: [
          { required: true }
        ],
        summary: [
          { required: true }
        ]
      },
      activeName: 'bill'
    }
  },
  filters: {
    isSaler,
    chargeNull,
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    console.log(this.$route.params)
    let reimburseId = paramCode.substring(2, paramCode.length)
    let code = paramCode.substring(0, 2)
    this.code = code
    console.log("subscribe reimburseNotice sessionid = ", this.sphdSocket.sessionid);
    this.listenersUid = [
      this.sphdSocket.subscribe('reimburseNotice', function (data) {
        that.loading = false
        let obj = JSON.parse(data)
        let status = Number(obj.status)
        console.log('审批返回值')
        console.log('reimburseNotice receive OK : ' + status)
        if (code === '11' || code === '21') {
          // 来源为出纳待处理列表的详情/一级审批待处理列表的详情
          if (status === 1) {
            that.$kiko_message('操作成功')
          } else if (status === 2) {
            that.$kiko_message('已审批，不予重复操作')
          } else if (status === 0) {
            that.$kiko_message('操作失败')
          } else {
            that.$kiko_message('系统错误')
          }
        } else if (code === '22') {
          // 来源为出纳待两讫列表的详情
          if (status === 1) {
            that.$kiko_message('操作成功')
          } else if (status === 2) {
            that.$kiko_message('该报销申请已两讫，不能重复批准')
          } else if (status === 3) {
            that.$kiko_message('余额不足')
          } else if (status === 0) {
            that.$kiko_message('两讫失败')
          } else {
            that.$kiko_message('系统错误')
          }
        }
        if (code === '21' || code === '22' || code === '23') {
          that.$router.push({
            path: '/financeReimburse',
            name: 'financeReimburse'
          })
        } else {
          that.$router.push({
            path: '/approvalReimburse',
            name: 'approvalReimburse'
          })
        }
      }),
      this.sphdSocket.subscribe('getReimburseDetail', function (data) {
        that.loading = false
        let ReimburseDetail = JSON.parse(data)
        // 报销申请信息
        that.personnelReimburse = ReimburseDetail.personnelReimburse
        that.square.summary = that.personnelReimburse.summary
        // 审批列表
        that.approvalProcessList = ReimburseDetail.approvalProcessList
        if (ReimburseDetail.approvalProcessList.length > 0) {
          that.nowApproveItem = that.approvalProcessList[that.approvalProcessList.length - 1]
          let lastToUser = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].toUser
          if (!lastToUser || lastToUser === that.sphdSocket.user.userID) {
            that.isNowApprover = true
          }
        }
        that.complateApprove = false
        that.approvalProcessList.forEach(function (it) {
          if (it.approveStatus === '7') {
            that.complateApprove = true
          }
        })
        that.approveStatus = that.approvalProcessList[that.approvalProcessList.length - 1].approveStatus
        if (that.approveStatus === '3' || that.approveStatus === '8') {
          that.isReject = true
        }
        // 按照票据种类列表
        that.listMapBillCat = ReimburseDetail.listMapBillCat
        for (var i in ReimburseDetail.listMapBillCat) {
          let billCatName = ReimburseDetail.listMapBillCat[i].billCatName
          if (billCatName === '增值税专用发票') {
            that.isSpecialBill = 1
          } else if (billCatName === '收据') {
            that.isSpecialBill = 2
          }
        }
        // 按费用类别列表
        that.listMapFeeCat = ReimburseDetail.listMapFeeCat
        console.log('getReimburseDetail session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('authInvoiceDetail', function (data) {
        that.loading = false
        let authInvoiceDetail = JSON.parse(data)
        // 报销申请信息
        that.num = authInvoiceDetail.num
        that.financeReimburseBills = authInvoiceDetail.financeReimburseBills
        console.log('authInvoiceDetail session Socket received OK:' + data)
      })
    ]
    let session = this.sphdSocket.sessionid
    // this.sphdSocket.send('getReimburseDetail', { reimburseId: reimburseId, session: session })
    this.getDetails(reimburseId)
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getDetails: function (reimburseId) {
      let that = this
      let params = {
        reimburseId:reimburseId
      }
      this.axios.post('../../../reimburseWindow/getReimburseDetail.do', params)
        .then(function (response) {
          console.log('详情=', response)
          let data = response.data.data
          console.log('详情 data=', data)

          that.loading = false
          let ReimburseDetail = data
          // 报销申请信息
          that.personnelReimburse = ReimburseDetail.personnelReimburse
          that.square.summary = that.personnelReimburse.summary
          // 审批列表
          that.approvalProcessList = ReimburseDetail.approvalProcessList
          if (ReimburseDetail.approvalProcessList.length > 0) {
            that.nowApproveItem = that.approvalProcessList[that.approvalProcessList.length - 1]
            let lastToUser = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].toUser
            if (!lastToUser || lastToUser === that.sphdSocket.user.userID) {
              that.isNowApprover = true
            }
          }
          that.complateApprove = false
          that.approvalProcessList.forEach(function (it) {
            if (it.approveStatus === '7') {
              that.complateApprove = true
            }
          })
          that.approveStatus = that.approvalProcessList[that.approvalProcessList.length - 1].approveStatus
          if (that.approveStatus === '3' || that.approveStatus === '8') {
            that.isReject = true
          }
          // 按照票据种类列表
          that.listMapBillCat = ReimburseDetail.listMapBillCat
          for (var i in ReimburseDetail.listMapBillCat) {
            let billCatName = ReimburseDetail.listMapBillCat[i].billCatName
            if (billCatName === '增值税专用发票') {
              that.isSpecialBill = 1
            } else if (billCatName === '收据') {
              that.isSpecialBill = 2
            }
          }
          // 按费用类别列表
          that.listMapFeeCat = ReimburseDetail.listMapFeeCat

        })
        .catch(function (error) {
          console.log(error)
        })
    },

    search: function () {
      this.$router.push({
        path: `/approveReimburseQuery`
      })
    },
    goBillInfo: function (item) {
      let params = {
        'name': 'approve',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': item.billCat,
        'feeCat': '',
        'secondFeeCat': '',
        'billCatName': item.billCatName,
        'feeCatName': '',
        'secondFeeCatName': '',
        'totalAmount': item.totalAmount,
        'num': item.num,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFinanceBillInfo: function (id) {
      let params = {
        id: id
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFeeInfo: function (item) {
      let params = {
        'name': 'approve',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': '',
        'feeCat': item.feeCat,
        'secondFeeCat': item.secondFeeCat,
        'billCatName': '',
        'feeCatName': item.feeCatName,
        'secondFeeCatName': item.secondFeeCatName,
        'totalAmount': item.totalAmount,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    approve: function (type, state) {
      console.log('approve 方法 参数=', type, state)
      let approveInfo = {
        // userId: this.sphdSocket.user.userID,
        approvalProcessId: this.approvalProcessList[this.approvalProcessList.length - 1].id,
        approvalStatus: state,
        // session: this.sphdSocket.sessionid,
        reason: ''
      }
      console.log(JSON.stringify(this.approvalProcessList))
      console.log(JSON.stringify(approveInfo))
      let that = this
      if (state === 2) {
        if (type === 'cashierTwoApproval') {
          approveInfo.reason = ''
          that.sphdSocket.send(type, approveInfo)
          that.loading = true
        } else {
          this.$prompt('驳回理由', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(({ value }) => {
            that.loading = true
            approveInfo.reason = value || ''
            that.sphdSocket.send(type, approveInfo)
            console.log(JSON.stringify(approveInfo))
          })
        }
      } else {
        if (type === 'cashierTwoApproval') {
          if (!that.square.payMethod) {
            that.$kiko_message('请选择支出方式')
          } else if (Number(that.square.payMethod) === 3 && !that.square.financeAccountId) {
            that.$kiko_message('请选择转账银行')
          } else if (!that.square.summary) {
            that.$kiko_message('请输入摘要')
          } else {
            approveInfo.payMethod = that.square.payMethod
            approveInfo.financeAccountId = that.square.financeAccountId || ''
            approveInfo.summary = that.square.summary
            let tips = ''
            if (that.isSpecialBill === 1) {
              tips = '本次报销中含有增值税专用发票。<span class="color-red">您需在系统的“增票认证”中确认认证情况后，</span>报销数据才会进入会计模块。'
            } else if (that.isSpecialBill === 2 && that.isPublic === '1') {
              tips = '会计规定：<span class="color-red">收据不宜使用对公户报销</span><p>再遇收据报销时请使用非对公户转账或现金</p>'
            }
            if (tips) {
              this.$alert(tips, '！！提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '我知道了',
                callback: action => {
                  that.loading = true
                  that.sphdSocket.send(type, approveInfo)
                  console.log(JSON.stringify(approveInfo))
                }
              })
            } else {
              that.loading = true
              that.sphdSocket.send(type, approveInfo)
            }
          }
        }
        else {
          let isOk = true
          if (type === 'cashierHandleApproval') {
            type
            if (that.approveSta) {
              approveInfo['approvalStatus'] = that.approveSta
            } else {
              that.$kiko_message('请选择审批情况！')
              isOk = false
            }
          }
          if (isOk) {
            console.log('这是最后发送的 通道名 ', type, )
            console.log('这是最后发送的 参数 =', approveInfo )
            that.loading = true
            that.sphdSocket.send(type, approveInfo)
          }
        }
      }
    },
    getIsPublic: function (value) {
      let that = this
      let bankList = this.bankList
      for (let i in bankList) {
        if (bankList[i].value === value) {
          that.isPublic = bankList[i].isPublic
        }
      }
    },
    dialogOpen: function (name) {
      this[name] = true
      if (name === 'twoApproval') {
        let that = this
        that.loading = true
        this.axios.get('../../../reimburseWindow/getAccounts.do?oid=' + this.sphdSocket.user.oid)
          .then(function (response) {
            let data = response.data.data
            let bankList = []
            for (let i in data) {
              let bankItem = {
                label: data[i].bankName + ' ' + data[i].account,
                value: data[i].id,
                isPublic: data[i].isPublic
              }
              bankList.push(bankItem)
            }
            that.bankList = bankList
            that.loading = false
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    certificationType: function (row, column) {
      let type = ''
      switch (row.certificationType) {
        case '0':
          type = '未认证'
          break
        case '1':
          type = '认证通过'
          break
        case '2':
          type = '认证失败'
          break
        case '3':
          type = '无需认证'
          break
        default:
          type = ''
      }
      return type
    }
  }
}
</script>
