package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtStockDao;
import cn.sphd.miners.modules.material.dao.MtStockDetailDao;
import cn.sphd.miners.modules.material.entity.MtStock;
import cn.sphd.miners.modules.material.entity.MtStockDetail;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * Created by Administrator on 2017/8/2.
 */
@Repository
public class MtStockDetailDaoImpl extends BaseDao<MtStockDetail,Serializable> implements MtStockDetailDao {

}
