package cn.sphd.miners.modules.publicInformation.service;

import cn.sphd.miners.modules.publicInformation.entity.PiCategory;
import cn.sphd.miners.modules.publicInformation.entity.PiCategoryHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.HashMap;
import java.util.List;

public interface PiCatService {

    //获取目录实体
    PiCategory piCategorySingle(Integer categoryId);

    //获取目录实体历史
    PiCategoryHistory piCategoryHisSingle(Integer categoryHisId);

    //获取1级目录
    HashMap<String, Object> getPiInitialDir(User user);

    //新增目录
    HashMap<String, Object> addPiCat(User user, String name);

    //修改目录名字
    HashMap<String, Object> upPiCat(User user, Integer categoryId, String name);

    //目录的历史记录
    List<PiCategoryHistory> listPiCatHis(Integer categoryId);

    //删除目录
    Integer delPitCat(User user, Integer categoryId);

}
