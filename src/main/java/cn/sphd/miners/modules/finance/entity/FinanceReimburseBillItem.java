package cn.sphd.miners.modules.finance.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2017/11/29.
 * 财务报销票据明细表（货物录入）
 */
@Entity
@Table(name = "t_finance_reimburse_bill_item")
public class FinanceReimburseBillItem implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "bill_id" ,insertable = true)
    private Integer billId;//票据id

    @Column(name = "item_name",length = 255)
    private String itemName;//明细项名称(货物或应税劳务、服务名称)

    @Column(name = "model",length = 255)
    private String model;//规格型号

    @Column(name = "unit",length = 100)
    private String unit;//单位

    @Column(name = "item_quantity")
    private double itemQuantity;//数量(注意double是为了凑税额)

    @Column(name = "uni_price")
    private double uniPrice;//单价(注意double是为了凑税额)

    @Column(name="price"   , nullable=true , unique=false)
    private BigDecimal price;//价格金额(不含税金额)

    @Column(name="tax_rate"   , nullable=true , unique=false)
    private BigDecimal taxRate;//税率

    @Column(name="tax_amount"   , nullable=true , unique=false)
    private BigDecimal taxAmount;//税额

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;//价税合计

    @Column(name = "memo",length = 255)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;


    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;//申批项目

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;//审批次级

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;//审批者ID

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;//审批者

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;//审批日期

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作：1-增，2—删，3-—改

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;//申请备注

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;//审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;//消息ID

    @Column(name="fee_cat"  ,  nullable=true , unique=false)
    private Integer feeCat;//费用类别 (字典表id)

    @Column(name="subject"  , length=255 , nullable=true , unique=false)
    private String subject;//科目编号

    @Column(name="subject_name"  , length=255 , nullable=true , unique=false)
    private String subjectName;//科目编号名称

    @Column(name = "fee_cat_path", length = 255, nullable=true , unique=false)
    private String feeCatPath;// 费用类别路径

    @JsonIgnore @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="bill_id", referencedColumnName = "id" , nullable=true , unique=false , insertable=false, updatable=false)
    private FinanceReimburseBill reimburseBill;

    @Column(name = "fee_cat_name", length = 255, nullable=true , unique=false)
    private String feeCatName;//费用类别名

    @Column(name = "row_no" ,insertable = true)
    private Integer rowNo;//行号

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBillId() {
        return billId;
    }

    public void setBillId(Integer billId) {
        this.billId = billId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public double getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(double itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public double getUniPrice() {
        return uniPrice;
    }

    public void setUniPrice(double uniPrice) {
        this.uniPrice = uniPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public FinanceReimburseBill getReimburseBill() {
        return reimburseBill;
    }

    public void setReimburseBill(FinanceReimburseBill reimburseBill) {
        this.reimburseBill = reimburseBill;
    }

    public Integer getFeeCat() {
        return feeCat;
    }

    public void setFeeCat(Integer feeCat) {
        this.feeCat = feeCat;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getFeeCatPath() {
        return feeCatPath;
    }

    public void setFeeCatPath(String feeCatPath) {
        this.feeCatPath = feeCatPath;
    }

    public String getFeeCatName() {
        return feeCatName;
    }

    public void setFeeCatName(String feeCatName) {
        this.feeCatName = feeCatName;
    }

    public Integer getRowNo() {
        return rowNo;
    }

    public void setRowNo(Integer rowNo) {
        this.rowNo = rowNo;
    }
}
