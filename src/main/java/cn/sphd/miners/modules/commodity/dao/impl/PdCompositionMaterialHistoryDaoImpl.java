package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdCompositionMaterialHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdCompositionMaterial;
import cn.sphd.miners.modules.commodity.entity.PdCompositionMaterialHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class PdCompositionMaterialHistoryDaoImpl extends BaseDao<PdCompositionMaterialHistory, Serializable> implements PdCompositionMaterialHistoryDao {


    @Override
    public void insert(PdCompositionMaterial pdCompositionMaterial,Integer mtHisId) {
        PdCompositionMaterialHistory pmh=new PdCompositionMaterialHistory();

        BeanUtils.copyProperties(pdCompositionMaterial,pmh);
        pmh.setId(null);
        pmh.setCompositionMaterial(pdCompositionMaterial.getId());
        pmh.setMaterialHistory(mtHisId);
        save(pmh);


    }

    @Override
    public void insert(PdCompositionMaterial pdCompositionMaterial, Integer mtHisId, Integer pdHisId) {
        PdCompositionMaterialHistory pmh=new PdCompositionMaterialHistory();

        BeanUtils.copyProperties(pdCompositionMaterial,pmh);
        pmh.setId(null);
        pmh.setCompositionMaterial(pdCompositionMaterial.getId());
        pmh.setMaterialHistory(mtHisId);
        pmh.setProduct(pdHisId);
        save(pmh);
    }

    @Override
    public void insertPdFormula(PdCompositionMaterial pdCompositionMaterial, Integer forId, Integer pdHisId) {
        PdCompositionMaterialHistory pmh=new PdCompositionMaterialHistory();

        BeanUtils.copyProperties(pdCompositionMaterial,pmh);
        pmh.setId(null);
        pmh.setCompositionMaterial(pdCompositionMaterial.getId());
        pmh.setMemo(forId+"");
        pmh.setProduct(pdHisId);
        save(pmh);
    }
}
