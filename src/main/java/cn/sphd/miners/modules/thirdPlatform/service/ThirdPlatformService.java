package cn.sphd.miners.modules.thirdPlatform.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.system.service.RegionService;
import cn.sphd.miners.modules.thirdPlatform.entity.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.web.bind.annotation.PathVariable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @description ThirdPlatformService放第三方平台和小程序的平台无关功能，并封装TgService中的平台有关功能；
 *                TgService接口采用多实现类实现各个平台的功能；
 *                具体业务功能放到各个MpXxx小程序包中。
 * @date Create at 2022/6/7 15:04
**/
public interface ThirdPlatformService {
    //第三方大厂代码:weChat-微信，byteDance-字节
    enum TgCode {
        weChat("weChat", "wx", "微信", "/build/mp-weixin", (byte)1),
        byteDance("byteDance", "tt", "字节", "/build/mp-toutiao", (byte)2),
        obIcbc("obIcbc", "icbc", "工行", null, (byte)3);
        private String name;
        private String abbr;
        private String cn;
        private String path;
        private Byte index;
        TgCode(String name, String abbr, String cn, String path, Byte index) {
            this.name = name;
            this.abbr = abbr;
            this.cn = cn;
            this.path = path;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public String getAbbr() { return abbr; }
        public String getCn() {return  cn; }

        public String getPath() {
            return path;
        }

        public Byte getIndex() {
            return index;
        }
        public boolean isBank() {
            return name.startsWith("ob");
        }
        public static TgCode getByIndex(Byte index) {
            for (TgCode item :TgCode.values()) {
                if (item.getIndex().equals(index)) {
                    return item;
                }
            }
            return null;
        }
        /**
         * <AUTHOR>
         * @description getInstance 取代valueOf，抛出异常提示代替空指针报错。
         * @date Create at 2022/10/27 10:15
         * @method getInstance
         * @param name
         * @return: cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService.TgCode:
        **/
        public static TgCode getInstance(String name) {
            TgCode result;
            if(StringUtils.isEmpty(name)) {
                throw new IllegalArgumentException("tgCode不能为空！");
            } else if ((result = Arrays.stream(TgCode.values()).filter(e -> name.equals(e.getName())).findAny().orElse(null)) == null ) {
                throw new IllegalArgumentException("tgCode的值“"+name+"”不合法！");
            }
            return result;
        }
    }

    //tpType;//`tp_type` TINYINT DEFAULT NULL COMMENT '类型:1-第三方小程序,2-独立小程序,3-移动应用,4-网站应用
    enum TpType {
        tpMiniApp("第三方小程序", (byte)1),
        indMiniApp("独立小程序", (byte)2),
        mobileApp("移动应用", (byte)3),
        webApp("网站应用", (byte)4);
        private String name;
        private Byte index;

        TpType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }

        public String getName() {
            return name;
        }

        public Byte getIndex() {
            return index;
        }
        public static boolean isMiniApp(TpApp app) {
            return app!=null && (TpType.tpMiniApp.index.equals(app.getTpType())
                    || TpType.indMiniApp.index.equals(app.getTpType()));
        }
        public static boolean isMobileApp(TpApp app) {
            return app!=null && TpType.mobileApp.index.equals(app.getTpType());
        }
    }
    enum NotifyStatus {//`notify_status` TINYINT DEFAULT NULL COMMENT '通知结果:1操作成功,2重复通知,3找不到订单,4支付未完成,-1操作失败,-2未知操作',
        success("操作成功", (byte)1),
        duplicateNotify("重复通知", (byte)2),
        duplicateOpration("重复操作", (byte)3),
        notFound("找不到订单", (byte)4),
        doing("操作未完成", (byte)5),
        error("操作失败", (byte)-1),
        unkonw("未知操作", (byte)-2);
        private String name;
        private Byte index;

        NotifyStatus(String name, Byte index) {
            this.name = name;
            this.index = index;
        }

        public String getName() {
            return name;
        }

        public Byte getIndex() {
            return index;
        }
        public static NotifyStatus fromJsonResult(JsonResult result) {
//        return new JsonResult(1,"操作成功");
//        return new JsonResult(new MyException("3", "已经支付过"));
//        return new JsonResult(new MyException("-1", "操作失败"));
            Byte errorCode;
            if(result.getSuccess().intValue()>0) {
                return success;
            } else if ((errorCode = Byte.valueOf(result.getErrorCode()))!=null){
                return values()[errorCode];
            }
            return null;
        }
    }

    /*enum State {
        w("1", "w"),
        o("2", "o"),
        n("3", "n"),
        d("4", "d"),
        e("5", "e"),
        r("6", "r"),
        s("7", "s");
        private String name;
        private String index;
        State(String name, String index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }

        public String getIndex() {
            return index;
        }
        public static ThirdPlatformService.State getWonderss(String state) {
            return Arrays.stream(ThirdPlatformService.State.values()).sorted().filter(State -> !State.equals(ThirdPlatformService.State.7) && state.contains(State.name)).findFirst().orElse(RegionService.Level.others);
        }
    }*/

    //定时任务方法，向各个大厂平台获取各个第三方平台和小程序的accesstoken
    void tpAccessTokenTask();
    //启动任务方法，向各个大厂（微信）平台获取各个商户的NativePayService
    void tpMerchantTask();

    //微信接收平台推送给第三方平台帐号的消息与事件，如授权事件通知、component_verify_ticket等。
    void wxReceiveAuthEvent(String postData, String signature, String timeStamp, String nonce, String encryptType, String msgSignature);
    void wxReceivePayNotify(Map<String, Object> params);
    void savePayNotify(TpPayNotify notify);
    void test();

//    //字节接收平台推送给第三方平台帐号的消息与事件，如授权事件通知、component_verify_ticket等。
//    void ttReceiveAuthEvent(String postData, String signature, String timeStamp, String nonce, String encryptType, String msgSignature);

    //将第三方平台的accessToken保存到Redis，仅供tgServcie使用
    void saveTpAccessTokenToRedis(TpPlatform platform, Map<String, Object> tokenMap);
    //将第三方平台的ComponentVerifyTicket保存到Redis，仅供tgServcie使用
    boolean saveTpVerifyTicketToRedis(TpPlatform platform, String ticket, Long timeStamp);
    //将小程序的accessToken保存到Redis，仅供tgServcie使用
    void saveAppAccessTokenToRedis(TpApp app, Map<String, Object> tokenMap);

    //获取已经缓存的平台accessToken
    String getTpVerifyTicket(TpPlatform platform);

    //获取已经缓存的平台accessToken
    String getTpAccessToken(TpPlatform platform);

    //获取已经缓存的小程序accessToken
    String getAppAccessToken(TpApp app);

    //获取第三方平台
    TpPlatform getPlatformByAppId(TgCode code, String appId, Boolean enabled);
    //获取小程序
    TpApp getTpApp(TgCode tgCode, String appId, Boolean enabled);
    List<TpApp> getOtherAppByGroup (TpApp app);

    //支付相关
    TpMerchant getMerchantByMchid(String mchid);
    JsonResult preOrder(HttpServletRequest request, EcOrder order);//预下单JSAPI/create_order
    JsonResult refundOrder(HttpServletRequest request, EcOrder order);//订单退款
    List<TpPayNotify> getPayNotifyListByNotifyId(String notifyId, NotifyStatus notifyStatus);

    //code获取/新建第三方用户
    Triple<TpApp, TpMember, MyException> code2Member(String tgCodeString, String appId, String code, Map<String, String> userInfo);
    JsonResult code2SessionMember(HttpServletRequest request, HttpServletResponse response, String oldToken, String sessionid, String tgCodeString, String appId, String code, Map<String, String> info);
    //code获取/新建第三方用户
    JsonResult codeBindAcc(HttpServletResponse response, String tgCodeString, String appId, String code, Map<String, String> userInfo, AuthAcc acc);
    JsonResult unBindAcc(HttpServletResponse response, String token, String tgCodeString, String appId, AuthAcc acc);
    JsonResult verificationCodeMpLogin(HttpServletRequest request, HttpServletResponse response, String sessionid, TpMember member, String phone, String code, String sendMethordName, AuthInfoDto authInfo);
    //为TpMember填充userInfo信息
    public void fillMemberUserInfoWithMap(TpMember member, Map<String, String> userInfo);
    //查询TpMember获取/新建第三方用户，仅供tgServcie使用
    TpMember getByAppUnionId(TpApp app, String unionId, String openId);
    List<TpMember> getGroupOtherAppUnionId(TpApp app, String unionId);

    //登录更新TpMember对象
    void updateLoginTpMember(TpMember member);

    //将用户sessionKey保存到Redis，仅供tgServcie使用
    void saveMemberSessionKeyRedis(TpApp app, String code, Map<String, Object> dataMap);
    Map<String, Object> getMemberSessionKeyRedis(TpApp app, String code);

    TpMember getMemberById(Long id);
    TpMember getMemberByOpenId(TpApp app, String openId);
    //通过app和acc获取绑定的TpMember，也有可能是app同组的绑定member
    TpMember getOrGroupByAcc(TpApp app, AuthAcc acc);

    //返回编译后小程序文件目录，仅在后台内部使用，请勿暴露外部
    String getMpPath(String module);
    //返回编译后uniapp目录，仅在后台内部使用，请勿暴露外部
    String getMpUniappPath(String module, TgCode tgCode);

//    //获取抖音的unionId和openId
//    TtCodeSessionDto callingTtcodeSession(String code, TpApp tpApp);

    //根据module获取app
//    TpApp getAppByModule(String module);

    //获取具体分享记录
    TpAppShare getTpAppShareSingle(Long shareId);

    //三表关联查询是否有这个登录人
//    Long getMemberByThreeform(String unionId, String openId);

//    //根据unionId和openId查询用户
//    TpMember getMemberByUnionIdAndopenId(String unionId, String openId);

//    //根据accId查询用户
//    TpMember getMemberByAccId(Integer accId);

    //新增用户
//    void insertTtMember(String code, Long shareId, String module, Integer accId, String nickName, String avatar, Integer type);

    int cleanMembeAccId(AuthAcc acc);

    JsonResult debugRemoveAllTpMemberAndAccUserOrg(HttpServletRequest request);

    List<TpMemberHistory> getTpMemberHistories(Long accId);

    //获取app的pending_version用于审核抖音小程序登录时是否在审核
    String appTokenKey(String appId, String secretKey, String appVersion, String envType);
}