package cn.sphd.miners.modules.complaint.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018-01-12 15:23
 * @description
 **/
@Entity(name = "ComplaintSupplement")
@Table(name = "t_complaint_supplement")
public class ComplaintSupplement implements Serializable{
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "complaint")
    private Integer complaint;
    @Column(name = "title")
    private String title;
    @Column(name = "description")
    private String description;
    @Column(name = "orders")
    private Integer orders;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_time")
    private Date updateTime;

/*    @ManyToOne(fetch= FetchType.LAZY)
    @JoinColumn(name="complaint", referencedColumnName = "id" , nullable=true , unique=false , insertable=false, updatable=false)
    private ComplaintBase baseSupplement;*/


    @OneToMany(targetEntity=ComplaintAttachment.class, fetch= FetchType.LAZY, mappedBy="supplementAttachment", cascade= CascadeType.REMOVE)
    private Set<ComplaintAttachment> complaintAttachmentHashSet = new HashSet<ComplaintAttachment>();




    @Transient
    private String picPatch;//图片路径
    @Transient
    private String attachPatch;//附件路径
    @Transient
    private String listAttachment;   //string类型图片附件数组


    public String getPicPatch() {
        return picPatch;
    }

    public void setPicPatch(String picPatch) {
        this.picPatch = picPatch;
    }

    public String getAttachPatch() {
        return attachPatch;
    }

    public void setAttachPatch(String attachPatch) {
        this.attachPatch = attachPatch;
    }


    //waijian


    public Set<ComplaintAttachment> getComplaintAttachmentHashSet() {
        return complaintAttachmentHashSet;
    }

    public void setComplaintAttachmentHashSet(Set<ComplaintAttachment> complaintAttachmentHashSet) {
        this.complaintAttachmentHashSet = complaintAttachmentHashSet;
    }

    public Integer getComplaint() {
        return complaint;
    }

    public void setComplaint(Integer complaint) {
        this.complaint = complaint;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getListAttachment() {
        return listAttachment;
    }

    public void setListAttachment(String listAttachment) {
        this.listAttachment = listAttachment;
    }
}
