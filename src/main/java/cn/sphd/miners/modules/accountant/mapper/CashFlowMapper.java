package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.CashFlow;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface CashFlowMapper extends BaseMapper<CashFlow> {
    List<CashFlow> getCashDataByPeriod(CashFlow cashFlow);

    //按期清除现金数据
    int delCashFlow(CashFlow c);

    CashFlow getCashDataByCode(CashFlow cashFlow);
}