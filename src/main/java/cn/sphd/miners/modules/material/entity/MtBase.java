package cn.sphd.miners.modules.material.entity;

import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdPack;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2016/9/19.
 */
@Entity(name="MtBase")
@Table(name="t_mt_base")
public class MtBase implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;


    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;

    @Column(name="code"  , length=20 , nullable=true , unique=false)
    private String code;

    @Column(name="model"  , length=100 , nullable=true , unique=false)
    private String model;

    @Column(name="specifications"  , length=255 , nullable=true , unique=false)
    private String specifications;

    @Column(name="source"  , length=1 , nullable=true , unique=false)
    private String source;

    @Column(name="import_log"  , nullable=true , unique=false)
    private Integer importLog;

    @Column(name="unit_price"   , nullable=true , unique=false)
    private BigDecimal unitPrice;

    @Column(name="unit"  , length=100 , nullable=true , unique=false)
    private String unit;

    @Column(name="net_weight"   , nullable=true , unique=false)
    private BigDecimal netWeight;

    @Transient
    private String weightUnit;  //重量单位:1-毫克(mg),2-克(g),3-千克(kg),4-吨(T)

    @Column(name="minimum_purchase"   , nullable=true , unique=false)
    private Long minimumPurchase;

    @Column(name="last_unit_price"   , nullable=true , unique=false)
    private BigDecimal lastUnitPrice;

    @Column(name="last_provider"   , nullable=true , unique=false)
    private Integer lastProvider;

    @Column(name="last_provider_name"  , length=255 , nullable=true , unique=false)
    private String lastProviderName;

    @Column(name="is_waring"   , nullable=true , unique=false)
    private Boolean isWaring;

    @Column(name="is_ignore"   , nullable=true , unique=false)
    private Boolean isIgnore;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name = "matching" , length=100 , nullable=true , unique=false)
    private String matching;//配比比例

    //库管员是否需要填初始库存:1-需要,0-不需要
    @Column(name="is_initialized" )
    private String isInitialized;

    //库管员是否需要填当前库存:1-需要,0-不需要
    @Column(name="is_current" )
    private String isCurrent;
    //1.106新增修改时是否终止未完结的订单:1-是,0-否
    @Column(name="terminate_orders")
    private String terminateOrders;

    @Column(name="operation")
    private String operation;

    @ManyToOne (fetch=FetchType.EAGER)
    @JoinColumn(name="product", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PdBase product;

    @Column(name="product"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer product_;

    @ManyToOne (fetch=FetchType.EAGER)
    @JoinColumn(name="category", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MtCategory category;

    @Column(name="category"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer category_;




    //1.95新增字段  enabled,enabled_time,is_purchased,location_number,supplier_number,cancelled_number,is_appointed
    @Column(name="enabled")
    String enabled;

    @Column(name="enabled_time")
    Date enabledTime;

    @Column(name="is_purchased")
    String isPurchased;

    @Column(name="location_number")
    Integer locationNumber;
    @Column(name="supplier_number")
    Integer supplierNumber;
    @Column(name="cancelled_number")
    Integer cancelledNumber;
    @Column(name="is_appointed")
    String isAppointed;

    @Column(name="origin")
    String origin; //来源 3构成 4技术材料需要 5技术材料不需要 6配方需要 7配方不需要  外购成品 origin 是 3  构成商品的原辅材料为4  包装物为5

    @Column(name="previous_id")
    Integer previousId;//          int comment '修改前记录ID',
    @Column(name="version_no")
    Integer versionNo;//           int comment '版本号,每次修改+1',


    @Column(name="appointed_time"  , nullable=true , unique=false)
    private Date appointedTime;

    @Column(name="appointed_user"  , nullable=true , unique=false)
    private String appointedUser;
    //1.119新增字段 unit_id
    @Column(name="unit_id"  , nullable=true , unique=false)
    private Integer unitId;
    //2.92新增
    @Column(name = "instance")
    private Integer instance;
    //2.92新增
    @Column(name = "instance_chain")
    private String instanceChain;
    //2.92新增
    @Column(name = "approve_status")
    private String approveStatus;
    //2.92新增
    @Column(name = "reject_reasion_desc")
    private String rejectReasionDesc;

    //1.266新增字段
    @Column(name="exp_required")
    private Integer expRequired;//是否有保质期方面的要求
    @Column(name = "open_duration")
    private Integer openDuration;//开封/开瓶后可使用天数

    @Column(name = "related_item")
    private Integer relatedItem;//相关的数据:1-截止日期,2-生产日期
    @Column(name = "same_expiration")
    private Integer sameExpiration;//保质期是否相同
    @Column(name = "expiration_days")
    private Integer expirationDays;//保质期天数

    @Column(name = "packaging_state")
    private Integer packagingState;//包装设置状态:0-未设置,1-设置中,2-设置完成
    @Column(name = "packaging_count")
    private Integer packagingCount;//包装方式数量
    @Column(name = "packaging_required")
    private Integer packagingRequired;//是否需要包装

    @Column(name = "drip_supplier")
    private Integer dripSupplier;//是否区分供应商

    @Column(name = "drip_packaging")
    private Integer dripPackaging;//是否区分包装

    @Transient
    private BigDecimal quantity;
    @Transient
    private BigDecimal out;

    //1.308新增



    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Date getAppointedTime() {
        return appointedTime;
    }

    public void setAppointedTime(Date appointedTime) {
        this.appointedTime = appointedTime;
    }

    public String getAppointedUser() {
        return appointedUser;
    }

    public void setAppointedUser(String appointedUser) {
        this.appointedUser = appointedUser;
    }

    //与质量情况表的
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany (targetEntity=MtQuality.class, fetch=FetchType.LAZY, mappedBy="material", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<MtQuality> mtQualityMtBaseViaId = new HashSet<MtQuality>();

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    //   预留
//    @OneToMany (targetEntity=MtStock.class, fetch=FetchType.LAZY, mappedBy="material", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
//    private Set <MtStock> TMtStockTMtBaseViaMaterial = new HashSet<MtStock>();


    //与库存表的
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany (targetEntity=MtStockInfo.class, fetch=FetchType.LAZY, mappedBy="material", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set <MtStockInfo> mtStockInfoHashSet = new HashSet<MtStockInfo>();

    //供货历史表
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany (targetEntity=MtSupplierMaterialHistory.class, fetch=FetchType.LAZY, mappedBy="material", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set <MtSupplierMaterialHistory> mtSupplierMaterialHistoryHashSet = new HashSet<MtSupplierMaterialHistory>();

    //供货对照表
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany (targetEntity=MtSupplierMaterial.class, fetch=FetchType.LAZY, mappedBy="material", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set <MtSupplierMaterial> mtSupplierMaterialHashSet = new HashSet<MtSupplierMaterial>();


    //商品包装方式表
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity=PdPack.class, fetch = FetchType.LAZY, mappedBy = "material",cascade = CascadeType.REMOVE)
    private Set<PdPack> pdPackHashSet=new HashSet<PdPack>();

    @Transient
    private BigDecimal minimumStock;//最低库存

    @Transient
    private String stockPosition;//库存位

    @Transient
    private String categoryName;//分类名称

    @Transient
    private String currentStock;//当前库存

    @Transient
    private String invoiceAble;

    @Transient
    private String invoiceCategory;

    @Transient
    private String isTax;



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    public String getEnabled() {
        return enabled;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public String getIsPurchased() {
        return isPurchased;
    }

    public void setIsPurchased(String isPurchased) {
        this.isPurchased = isPurchased;
    }

    public Integer getLocationNumber() {
        return locationNumber;
    }

    public void setLocationNumber(Integer locationNumber) {
        this.locationNumber = locationNumber;
    }

    public Integer getSupplierNumber() {
        return supplierNumber;
    }

    public void setSupplierNumber(Integer supplierNumber) {
        this.supplierNumber = supplierNumber;
    }

    public Integer getCancelledNumber() {
        return cancelledNumber;
    }

    public void setCancelledNumber(Integer cancelledNumber) {
        this.cancelledNumber = cancelledNumber;
    }

    public String getIsAppointed() {
        return isAppointed;
    }

    public void setIsAppointed(String isAppointed) {
        this.isAppointed = isAppointed;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getDripSupplier() {
        return dripSupplier;
    }

    public void setDripSupplier(Integer dripSupplier) {
        this.dripSupplier = dripSupplier;
    }

    public Integer getDripPackaging() {
        return dripPackaging;
    }

    public void setDripPackaging(Integer dripPackaging) {
        this.dripPackaging = dripPackaging;
    }

    public String getInvoiceAble() {
        return invoiceAble;
    }

    public void setInvoiceAble(String invoiceAble) {
        this.invoiceAble = invoiceAble;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public String getIsTax() {
        return isTax;
    }

    public void setIsTax(String isTax) {
        this.isTax = isTax;
    }

    public Integer getImportLog() { return importLog; }

    public void setImportLog(Integer importLog) {  this.importLog = importLog;  }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public Long getMinimumPurchase() {
        return minimumPurchase;
    }

    public void setMinimumPurchase(Long minimumPurchase) {
        this.minimumPurchase = minimumPurchase;
    }

    public BigDecimal getLastUnitPrice() {
        return lastUnitPrice;
    }

    public void setLastUnitPrice(BigDecimal lastUnitPrice) {
        this.lastUnitPrice = lastUnitPrice;
    }

    public Integer getLastProvider() {
        return lastProvider;
    }

    public void setLastProvider(Integer lastProvider) {
        this.lastProvider = lastProvider;
    }

    public String getLastProviderName() {
        return lastProviderName;
    }

    public void setLastProviderName(String lastProviderName) {
        this.lastProviderName = lastProviderName;
    }

    public Boolean getWaring() {
        return isWaring;
    }

    public Integer getExpRequired() {
        return expRequired;
    }

    public void setExpRequired(Integer expRequired) {
        this.expRequired = expRequired;
    }

    public Integer getOpenDuration() {
        return openDuration;
    }

    public void setOpenDuration(Integer openDuration) {
        this.openDuration = openDuration;
    }

    public Integer getRelatedItem() {
        return relatedItem;
    }

    public void setRelatedItem(Integer relatedItem) {
        this.relatedItem = relatedItem;
    }

    public Integer getSameExpiration() {
        return sameExpiration;
    }

    public void setSameExpiration(Integer sameExpiration) {
        this.sameExpiration = sameExpiration;
    }

    public Integer getExpirationDays() {
        return expirationDays;
    }

    public void setExpirationDays(Integer expirationDays) {
        this.expirationDays = expirationDays;
    }

    public Set<MtSupplierMaterialHistory> getMtSupplierMaterialHistoryHashSet() {
        return mtSupplierMaterialHistoryHashSet;
    }

    public void setMtSupplierMaterialHistoryHashSet(Set<MtSupplierMaterialHistory> mtSupplierMaterialHistoryHashSet) {
        this.mtSupplierMaterialHistoryHashSet = mtSupplierMaterialHistoryHashSet;
    }

    public void setWaring(Boolean waring) {
        isWaring = waring;
    }

    public Boolean getIgnore() {
        return isIgnore;
    }

    public void setIgnore(Boolean ignore) {
        isIgnore = ignore;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public MtCategory getCategory() {
        return category;
    }

    public void setCategory(MtCategory category) {
        this.category = category;
    }

    public Integer getCategory_() {
        return category_;
    }

    public void setCategory_(Integer category_) {
        this.category_ = category_;
    }

    public Set<MtQuality> getMtQualityMtBaseViaId() {
        return mtQualityMtBaseViaId;
    }

    public void setMtQualityMtBaseViaId(Set<MtQuality> mtQualityMtBaseViaId) {
        this.mtQualityMtBaseViaId = mtQualityMtBaseViaId;
    }

    public Set<MtStockInfo> getMtStockInfoHashSet() {
        return mtStockInfoHashSet;
    }

    public void setMtStockInfoHashSet(Set<MtStockInfo> mtStockInfoHashSet) {
        this.mtStockInfoHashSet = mtStockInfoHashSet;
    }


    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Set<MtSupplierMaterialHistory> getMtSupplierHistoryHashSet() {
        return mtSupplierMaterialHistoryHashSet;
    }

    public void setMtSupplierHistoryHashSet(Set<MtSupplierMaterialHistory> mtSupplierHistoryHashSet) {
        this.mtSupplierMaterialHistoryHashSet = mtSupplierHistoryHashSet;
    }

    public Set<MtSupplierMaterial> getMtSupplierMaterialHashSet() {
        return mtSupplierMaterialHashSet;
    }

    public void setMtSupplierMaterialHashSet(Set<MtSupplierMaterial> mtSupplierMaterialHashSet) {
        this.mtSupplierMaterialHashSet = mtSupplierMaterialHashSet;
    }

    public String getStockPosition() {
        return stockPosition;
    }

    public void setStockPosition(String stockPosition) {
        this.stockPosition = stockPosition;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }



    public PdBase getProduct() {
        return product;
    }

    public void setProduct(PdBase product) {
        this.product = product;
    }

    public Integer getProduct_() {
        return product_;
    }

    public void setProduct_(Integer product_) {
        this.product_ = product_;
    }

    public String getMatching() {
        return matching;
    }

    public void setMatching(String matching) {
        this.matching = matching;
    }

    public Set<PdPack> getPdPackHashSet() {
        return pdPackHashSet;
    }

    public void setPdPackHashSet(Set<PdPack> pdPackHashSet) {
        this.pdPackHashSet = pdPackHashSet;
    }

    public String getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(String currentStock) {
        this.currentStock = currentStock;
    }

    public String getIsInitialized() {
        return isInitialized;
    }

    public void setIsInitialized(String isInitialized) {
        this.isInitialized = isInitialized;
    }

    public String getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(String isCurrent) {
        this.isCurrent = isCurrent;
    }

    public String getTerminateOrders() {
        return terminateOrders;
    }

    public void setTerminateOrders(String terminateOrders) {
        this.terminateOrders = terminateOrders;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setOut(BigDecimal out) {
        this.out = out;
    }

    public BigDecimal getOut() {
        return out;
    }

    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getRejectReasionDesc() {
        return rejectReasionDesc;
    }

    public void setRejectReasionDesc(String rejectReasionDesc) {
        this.rejectReasionDesc = rejectReasionDesc;
    }

    public Integer getPackagingState() {
        return packagingState;
    }

    public void setPackagingState(Integer packagingState) {
        this.packagingState = packagingState;
    }

    public Integer getPackagingCount() {
        return packagingCount;
    }

    public void setPackagingCount(Integer packagingCount) {
        this.packagingCount = packagingCount;
    }

    public Integer getPackagingRequired() {
        return packagingRequired;
    }

    public void setPackagingRequired(Integer packagingRequired) {
        this.packagingRequired = packagingRequired;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }
}
