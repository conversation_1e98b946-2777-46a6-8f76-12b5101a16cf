.table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, 
.table > thead > tr > td, .table > thead > tr > th{
	padding: 12px!important;
}
.hd{ display: none!important; }

.check_false{ background:url("icon/check_false.png") no-repeat center center ;      }
.check_true{ background:url("icon/check_true.png") no-repeat center center ;        }
.check_all_true{ background:url("icon/check_all_true.png") no-repeat center center ;        }
.check_all_false{ background:url("icon/check_all_false.png") no-repeat center center ;        }
.check_false , .check_true ,.check_all_false , .check_all_true { padding:10px; background-size:15px 15px;                 }
.fl_r{float:right}

 .ty-form-checkbox,.ty-form-checkbox *,.ty-form-radio,.ty-form-radio{
	 display: inline-block;
	 vertical-align: bottom;
 }
.ty-form-checkbox{
	height: auto !important;
	line-height: normal !important;
	border: none !important;
	margin-right: 0;
	padding-right: 0;
	background: 0 0;
}
.ty-form-checkbox i {
	position: absolute;
	right: 0;
	width: 30px;
	color: #fff;
	font-size: 20px;
	text-align: center;
}
.ty-form-checkbox[skin="green"] i {
	position: relative;
	top: 0;
	width: 16px;
	height: 16px;
	line-height: 16px;
	border: 1px solid #d2d2d2;
	font-size: 12px;
	border-radius: 2px;
	background-color: #fff;
	-webkit-transition: .1s linear;
	transition: .1s linear;
}
.ty-form-checked[skin="green"] i {
	border-color: #48cfad;
	background-color: #48cfad;
	color: #fff;
}
.ty-form-checkbox span {
	padding: 0 10px;
	padding-right: 10px;
	height: 100%;
	font-size: 14px;
	background-color: #d2d2d2;
	color: #fff;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.ty-form-checkbox[skin="green"] span {
	float: right;
	padding-right: 15px;
	line-height: 18px;
	background: 0 0;
	color: #666;
}
.ty-checkbox-disabled[skin="green"] span{
	color: #aaa;
}
.ty-checkbox-disabled:hover {
	color: #d2d2d2 !important;
	cursor: not-allowed !important;
}



.ty-table-left td {
	text-align: left;
}







