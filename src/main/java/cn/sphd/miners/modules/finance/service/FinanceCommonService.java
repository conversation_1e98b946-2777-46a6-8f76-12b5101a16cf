package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * lyx/********
 * 摘出财务公共的方法---所以的方法均可以随着使用进行优化
 */
public interface FinanceCommonService {

    //现金收入
    void cashIncome();

    //现金支出
    AccountDetail cashDisbursements(User user, FinanceAccount financeAccount, BigDecimal amount, BigDecimal billAmount, BigDecimal balance,
                                    String summary, String purpose, String method, Integer business, String source, String oppositeCorp, Date factDate, String modityStatus);

    //转账支票、承兑汇票和内部转账支票的支出(内部银行转账的还没走账，只是在审批过程中)
    FinanceAccountBill invoiceDisbursements(User user, BigDecimal amount, BigDecimal billAmount, String method, Integer invoiceId, String summary,
         String purpose, String source, Integer business, String oppositeCorp, String receiver, String operator, String expireDate, String receiveDate,String modityStatus);

    //转账支票、承兑汇票和内部转账支票的支出(内部银行转账的走账，审批确定，与上面的接口对应)
    void invoiceDisbursementsApproval(User user, FinanceAccount financeAccount, FinancePayment financePayment, BigDecimal amount, BigDecimal billAmount, BigDecimal balance,
         String summary, String purpose, String source, String oppositeCorp, String operatorName, Date factDate, Integer business, String modityStatus);


    //查询FinancePayment中的计数，就是待复核的付款笔数
    Integer getFinancePaymentNum(Integer business,String payStatus,String businessType);

    //修改前的数据处理[没有现金这种情况]  method(1-现金,2-现金支票,3-转帐支票(外部或内部),4-承兑汇票,5-银行转帐)  financeAccountBill-老数据的
    void updateBeforeFinancePayment(User user,FinancePayment financePayment,FinanceAccountBill financeAccountBill,FinanceAccountBill financeAccountBillNew);

    //暂时-需收回的款中查找已收回的列表
    List<FinanceReceipt> getFinanceReceiptList(Integer org,String businessType,Integer business);
    }
