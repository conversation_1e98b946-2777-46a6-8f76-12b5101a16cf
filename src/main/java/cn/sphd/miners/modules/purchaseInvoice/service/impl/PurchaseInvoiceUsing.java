package cn.sphd.miners.modules.purchaseInvoice.service.impl;

import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoiceAttachment;
import cn.sphd.miners.modules.purchaseInvoice.service.PurchaseInvoiceService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class PurchaseInvoiceUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
//    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            PurchaseInvoiceService service = ac.getBean(PurchaseInvoiceService.class, "purchaseInvoiceService");
            PoPaymentInvoiceAttachment entity = service.getInvoiceAttachment(id);
            //报销附件表、报销票据表以及报销本表均不为空
            if (entity != null && entity.getInvoice()!=null) {
                return filename.equals(entity.getPath());
            }
        }
        return false;
    }

//    @Override
//    public String getKey() {
//        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
//         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
//        return id+entityClass;
//    }

//    public ReimburseUsing(Integer id, Class entityClass) {
//        this.id = id;
//        String className = entityClass.getName();
//        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
//        this.entityClass = className.substring(className.lastIndexOf('.')+1);
//    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是单实体使用，只需要使用id；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id.toString();
    }

    public PurchaseInvoiceUsing() {

    }

    public PurchaseInvoiceUsing(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id){
        this.id=id;
    }

//    public String getEntityClass() {
//        return entityClass;
//    }
}
