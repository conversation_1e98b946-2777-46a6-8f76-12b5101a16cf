package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.modules.accountant.entity.TBalanceSheet;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface TBalanceSheetMapper extends BaseMapper<TBalanceSheet> {

    List<TBalanceSheet> listByTBalanceSheet (TBalanceSheet sheet);


    //获取每行的余额
    TBalanceSheet getTBalanceSheet(TBalanceSheet sheet);

    //根据期数来清除数据
    int delSheet(TBalanceSheet s);

}