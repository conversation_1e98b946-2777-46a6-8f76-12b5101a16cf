/*分工设置*/
.bonceCon input,.bonceCon select,.bonceCon textarea {
    border:1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    text-align: left;
    padding: 0 8px;
    color: #3f3f3f;
    width: 180px;
}
.bonceCon textarea{
    line-height: 20px;
    padding:5px;
}
.bonceCon input{
    height: 36px;
}
.bonceCon input:disabled{
    background-color: #dff0ff;
}
.bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
    border:1px solid #5d9cec;
}
.importIntro{
    float: left;
    padding:20px;
    color: #646c74;
    border: 1px dotted #5d9cec;
    width: 32%;
    margin-top: 10px;
    background-color: #fff;
    min-width: 420px ;
    height: 280px;
    margin-right: 8px;
    display: none;
    position: relative;
}
.importIntro h3{
    font-weight: bold;
    font-size: 16px;
    color: #5d9cec;
    margin: 5px 0 8px 0;
}
.importIntro p{
    margin: 5px;
    font-size: 14px;
}
.handleTip{
    height: 180px;
}
.userRole{
    font-weight: bold;
    font-size: 16px;
    margin-left: 16px;
}
.ty-colFileTree>ul ul{display: none ; padding-left:15px }
.ty-colFileTree .ty-treeItemActive>i.fa-file{  color: #fff;}
.planIndent{text-indent: 2em;}
.bg{
    font-size: 139px;
    color: rgba(93, 156, 236, 0.05);
    position: absolute;
    right: 16px;
    top: 16px;
    line-height: 1;
}
/*当前权限*/
td > div:last-child{
    border: none;
}
.ty-table td {
    /*padding: 0;*/
}
.importantTip{
    width: 638px;
    height: 128px;
    border:2px dashed #0099FF;
    padding:20px 40px;
    text-align: center;
    background-color: #fff;
    margin-bottom: 8px;
}
.ty-table .ty-head li{
    border:none;
}
.ty-table .ty-body{
    background: #fff;
}
.ty-table ul{
    width: 100%;
    overflow: hidden;
}
.ty-table ul li{
    float: left;
    border:1px solid #efefef;
    min-height: 40px;
    line-height:40px;
    text-align: center;
    margin-right: -1px;
    height:100%;
    overflow:hidden;
    padding-bottom:9999px;
    margin-bottom:-9999px
}
.ty-table .ty-head li{
    border:none;
}
.ty-table .ty-body{
    background: #fff;
}
.ty-table ul{
    width: 100%;
    overflow: hidden;
}
.ty-table ul li{
    float: left;
    border:1px solid #efefef;
    min-height: 40px;
    line-height:40px;
    text-align: center;
    margin-right: -1px;
    height:100%;
    overflow:hidden;
    padding-bottom:9999px;
    margin-bottom:-9999px
}
/*职工权限*/
#biao{ position: relative; padding: 0; }
.biaotou{  border-top: 40px rgba(0,0,0,0) solid; border-left: 130px rgba(200,200,200,0.5) solid;
    width: 100%;  position: absolute ; top:0 ; ;left:0;  }
.tt1, .tt2{ position: absolute;  }
.tt1{ top:5px; right:5px; }
.tt2{ bottom:5px; left: 5px;    }
#authList_1_1 tr:nth-child(1) td{ height: 80px; }
#auth_1_1 , #auth_1_2{ float: left;  }
#auth_1_1{ width:10%;   }
#auth_1_2{ min-width:800px; overflow-x: auto; width:82%; border-right:1px solid #ddd;  }
#generalAuthority .ty-table td{
    padding: 0;
}
#generalAuthority .ty-table td .ty-form-checkbox{
    margin-left: 12px;
    width: auto;
}
#generalAuthority .ty-table td .text {
    padding: 0 10px;
    color: #666;
}
#generalAuthority .ty-table thead td {
    text-align: center;
}
.btn{
    border: none;
    margin-top: inherit;
}