package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.system.entity.User;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2015-11-09.
 */
@Entity
@Table(name="t_sys_user_message")
public class UserMessage implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="client_id"  , length=100 , nullable=true , unique=false)
    private String clientId;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;

    @Column(name="content"  , length=255 , nullable=true , unique=false)
    private String content;

    @Column(name="state"   , nullable=true , unique=false)
    private Integer state;// 1-未处理  2-已处理

    @Column(name="push_time"   , nullable=true , unique=false)
    private Date pushTime;

    @Column(name = "create_time")
    private Date createDate;

    @Column(name="sender_name"  , length=100 , nullable=true , unique=false)
    private String senderName;

    @Column(name="sender"   , nullable=true , unique=false)
    private Integer sender;

    @Column(name="send_time"   , nullable=true , unique=false)
    private Date sendTime;

    @Column(name="function"   , nullable=true , unique=false)
    private Integer function;

    @Column(name="approval_item"   , nullable=true , unique=false)
    private Integer approvalItem;

    @Column(name="approval_status"   , nullable=true , unique=false)
    private Integer approvalStatus;//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name = "type")
    private Integer type;//1-立案 2-结案

    @Column(name = "message_type")
    private String messageType;//消息类型  1-财务  2-计划加班  3-计划请假   4-报销 5-入库 6-出库，7-投诉，8-讨论 9-申报加班 10-提前结束请假 11-科目设置 12-会计报税 13-持续改进 14-项目管理  15-常规借款 16-开票申请审批 17-开票终止 18-修改订单号或日期 19-修改评审负责人 20 -修改要货信息

    @Column(name = "handle_time")
    private Date handleTime;//处理时间

    @Column(name = "handle_id")
    private String handleId;//处理人id

    @Column(name = "handle_name")
    private String handleName;//处理人

    @Column(name = "ask_name")
    private String askName;//请求人

    @Column(name = "operation_type")
    private String operationType;//操作类型（1-增加，2-删除，3-修改，4-查询）

    @Column(name = "account_type")
    private Integer accountType; //账户类型：1-现金  2-银行

    @Column(name="account_or_detailed")
    private String accountOrDetailed;// 1-账户 2-明细

    @Column(name = "account_id")
    private String accountId;//账户id

    @Column(name = "history_id")
    private Integer historyId;//历史id

    @Column(name = "illustrate", length=255 , nullable=true , unique=false)
    private String illustrate;//操作说明（申请事件）

    @Column(name = "handle_reply" , length=255 , nullable=true , unique=false)
    private String handleReply;//处理回复

    @Column(name="level",length = 1 , nullable=true , unique=false)
    private Integer level;//报销审批处理级别

    @Column(name="fee_cat"  ,  nullable=true , unique=false)
    private Integer feeCat;//费用类别 (字典表id)

    @Column(name="bill_cat"  , nullable=true , unique=false)
    private Integer billCat;//票据类型 (字典表id)

    @Column(name = "event_type" , length=255 , nullable=true , unique=false)
    private String eventType; //事件类型

    @Column(name = "is_null" , length=1 , nullable=true , unique=false)
    private String isNull;//为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。

    @Column(name="receive_user_id"  , nullable=true , unique=false)
    private Integer receiveUserId;  //接收消息人的id

    @Column(name="personnel_reimbur_id"  , nullable=true , unique=false)
    private Integer personnelReimburId;  //在此为某一条报销的id(若加班、请假为多级审批时也可用)

    @Column(name="mtStockId"  , nullable=true , unique=false)
    private Integer mtStockId;  //在此为出入库表ID

    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=true, updatable=true)
    private User user;

    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer userID;


    @Transient
    private String qingjiatype;//类型

    @Transient
    private Date beginTime;//开始时间

    @Transient
    private Date endTime;//结束时间

    @Transient
    private Double duration;//时长

    @Transient
    private String feeCatName;//费用类别名称

    @Transient
    private String billCatName;//票据类别名称

    @Transient
    private Date approDateTime;//申请时间

    @Transient
    private BigDecimal totalAmount;//总金额

    @Transient
    private String applicant;  //申请人

    public Integer getMtStockId() {
        return mtStockId;
    }

    public void setMtStockId(Integer mtStockId) {
        this.mtStockId = mtStockId;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Date getApproDateTime() {
        return approDateTime;
    }

    public void setApproDateTime(Date approDateTime) {
        this.approDateTime = approDateTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public Integer getSender() {
        return sender;
    }

    public void setSender(Integer sender) {
        this.sender = sender;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getFunction() {
        return function;
    }

    public void setFunction(Integer function) {
        this.function = function;
    }

    public Integer getApprovalItem() {
        return approvalItem;
    }

    public void setApprovalItem(Integer approvalItem) {
        this.approvalItem = approvalItem;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getHandleName() {
        return handleName;
    }

    public void setHandleName(String handleName) {
        this.handleName = handleName;
    }

    public String getAskName() {
        return askName;
    }

    public void setAskName(String askName) {
        this.askName = askName;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public String getAccountOrDetailed() {
        return accountOrDetailed;
    }

    public void setAccountOrDetailed(String accountOrDetailed) {
        this.accountOrDetailed = accountOrDetailed;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getHandleReply() {
        return handleReply;
    }

    public void setHandleReply(String handleReply) {
        this.handleReply = handleReply;
    }

    public String getIllustrate() {
        return illustrate;
    }

    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public Integer getHistoryId() {
        return historyId;
    }

    public void setHistoryId(Integer historyId) {
        this.historyId = historyId;
    }

    public String getHandleId() {
        return handleId;
    }

    public void setHandleId(String handleId) {
        this.handleId = handleId;
    }

    public String getQingjiatype() {
        return qingjiatype;
    }

    public void setQingjiatype(String qingjiatype) {
        this.qingjiatype = qingjiatype;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getFeeCat() {
        return feeCat;
    }

    public void setFeeCat(Integer feeCat) {
        this.feeCat = feeCat;
    }

    public Integer getBillCat() {
        return billCat;
    }

    public void setBillCat(Integer billCat) {
        this.billCat = billCat;
    }

    public String getFeeCatName() {
        return feeCatName;
    }

    public void setFeeCatName(String feeCatName) {
        this.feeCatName = feeCatName;
    }

    public String getBillCatName() {
        return billCatName;
    }

    public void setBillCatName(String billCatName) {
        this.billCatName = billCatName;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getIsNull() {
        return isNull;
    }

    public void setIsNull(String isNull) {
        this.isNull = isNull;
    }

    public Integer getReceiveUserId() {
        return receiveUserId;
    }

    public void setReceiveUserId(Integer receiveUserId) {
        this.receiveUserId = receiveUserId;
    }

    public String getApplicant() {
        return applicant;
    }
    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public Integer getPersonnelReimburId() {
        return personnelReimburId;
    }

    public void setPersonnelReimburId(Integer personnelReimburId) {
        this.personnelReimburId = personnelReimburId;
    }
}
