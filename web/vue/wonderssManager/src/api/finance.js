import {request} from "@/utils/request";
import JSONBig from 'json-bigint'


/*
 * creator: 2025-2-3 hxz
 * 获取首页的当前余额
 * params: oid：机构id
 * 返回值：
 * balanceAccount：各银行账户与现金备用金的当前余额
	balanceReturn：已收到但尚未存入银行的转账支票及承兑汇票
*/
export function getBalance(data) {
    return request({
        url: '/appCommodity/getBalance.do',
        data: data
    })
}


/*
 * creator: 2025-2-3 hxz
 * 按月流水查看
 * params: oid：机构id
 * 返回值：
 * accountDetails：列表信息{
		id：id
		createDate：创建时间
		credit：收入
		debit：支出
	}
*/
export function getDetailByMonth(data) {
    return request({
        url: '/appCommodity/getDetailByMonth.do',
        data: data
    })
}


/*
 * creator: 2025-2-3 hxz
 * 获取4个总结金额
 * params:
 * Integer oid：机构id
	Integer state：1-本日，2-本月，4-本年，5-自定义【3-上月这个暂时不需要，但是代码带着】
	String beginDate：开始时间
	String endDate：结束时间
 * 返回值：
 * beginDate：开始时间
	endDate：结束时间
	allCredit：今日收入(本月收入/本年收入/本期收入)
	allPreviousBalance：昨日余额（上月余额/去年余额/上期余额）
	allDebit：今日支出(本月支出/本年支出/本期支出)
	allBalance：当前余额(本期余额)
*/
export function financeAmount(data) {
    return request({
            url: '/appCommodity/financeAmount.do',
            data: data
        }
    )
}


/*
 * creator: 2025-2-3 hxz
 * 按时间查看
 * params:
 * Integer oid：机构id
	Integer state：1-本日，2-本月，4-本年，5-自定义【3-上月这个暂时不需要，但是代码带着】
	Integer fid：账户id（用则传值，不用不传值）
	String beginDate：开始时间
	String endDate：结束时间
 * 返回值：
 * type：A-跨年，B-跨月，C-本月，D-本日
	accountDetailList：{
		id：id
		createDate：创建时间
		credit：收入
		debit：支出
	}

	accountPeriodList：{
		beginDate：开始时间
		endDate：结束时间
		credit：收入
		debit：支出
	}
*/
export function getAllAccountDataByTimeApp(data) {
    return request({
        url: '/appCommodity/getAllAccountDataByTimeApp.do',
        data: data
    })
}


/*
 * creator: 2025-2-3 hxz
 * 按账户查看
 * params:
 * Integer oid：机构id
	Integer state：1-本日，2-本月，4-本年，5-自定义【3-上月这个暂时不需要，但是代码带着】
	String beginDate：开始时间
	String endDate：结束时间
 * 返回值：
 * beginDate：开始时间
	endDate：结束时间
	accountPeriod1：现金账户{
		id
		bankName:开户行
		balance：当前余额
	}
	accountPeriod2：银行汇总账户{
		balance：当前余额
	}
	accountPeriod3：所有的银行账户信息{
		id
		fid：账户id
		bankName:开户行
		balance：当前余额
	}
*/
export function getDataByAccountApp(data) {
    return request({
        url: '/appCommodity/getDataByAccountApp.do',
        data: data
    })
}


/*
 * creator: 2025-2-3 hxz
 * 年进月、月进日、日进明细
 * params:
 * 	Integer fid：账户id（银行汇总的此传0）
	String beginDate：开始时间
	String endDate：结束时间
	Integer oid：机构id
	String monthOrDay：1-年进月，2-月进日，3-日进明细
 * 返回值：
 * accountId:银行账户id
	bankname:开户行
	beginDate：开始时间
	endDate：结束时间
	allCredit：今日收入(本月收入/本年收入/本期收入)
	allPreviousBalance：昨日余额（上月余额/去年余额/上期余额）
	allDebit：今日支出(本月支出/本年支出/本期支出)
	allBalance：当前余额(本期余额)
	type：A-跨年，B-跨月，C-本月，D-本日
	accountDetailList：{
		id：id
		createDate：创建时间
		credit：收入
		debit：支出
	}
	accountPeriodList：{
		beginDate：开始时间
		endDate：结束时间
		credit：收入
		debit：支出
	}
*/
export function getAccountMonthOrDayApp(data) {
    return request({
        url: '/appCommodity/getAccountMonthOrDayApp.do',
        data: data
    })
}


/*
 * creator: 2025-2-3 hxz
 * 获取数据详情
 * params:
 * 	Integer detatilId：详情id（银行汇总的此传0）
 */

/* 返回值：
* kind=1时，支付方式为现金/银行转账的报销数据{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           purpose：报销事由
           amount：总金额
           billAmount：票面总金额
           payMethod：支出方式,0-未两讫,1-现金,2-现金支票,3-银行转帐
           createName：申请人
           transactionType：事务类型  1-销售事务
       }

       processList：审批流程{
           id
           org：机构id
           toUser：审批人id
           approveStatus：1 - 提交 2-批准（可付款/付款方式修改的确认），3-驳回 4-待两讫(待付款) 5-已报销 6-待接收(待票据审批) 7-已接收(待票据审批) 8-财务驳回
           handleTime：处理时间
           userName：审批人名称
           business：业务数据表id
           businessType：17-付款审批 20-报销的付款复核（待复核） 21-报销的可付款流程（财务的付款方式确定） 22-报销财务的付款方式修改
           reason：驳回理由
       }
   }

   kind=2时，收入中收入方式为现金/银行转账{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           type：1-初始金额冲减,2-转帐交易,3-收入,4-支出
           method：0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
           genre：类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
           categoryDesc：当类别为其他时候的说明
           oppositeCorp：付款单位
           receiveAccountDate：到账时间
           purpose：用途
           oppositeAccount：对方账户名称（收款时为付款账号，付款时为收款账号）
           oppositeId：对方账户id（收款时为付款账号id，付款时为收款账号id）
           oppositeBankno：对方银行代码银行转帐时使用（开户行）
           oppositeBankcode：对方银行帐号(银行转帐时使用)
           detailId：另一条明细id，内部非支出性转账时使用
           billAmount：票面金额
           balance：余额
           credit：收入
           debit：支出
           auditorName|：经手人
           partnerName：合作方经手人姓名
           transactionType：事务类型  1-销售事务
       }
   }

   kind=3时，支出中支付方式为现金/银行转账{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           type：1-初始金额冲减,2-转帐交易,3-收入,4-支出
           method：0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
           genre：类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
           categoryDesc：当类别为其他时候的说明
           oppositeCorp：付款单位
           receiveAccountDate：到账时间
           purpose：用途
           oppositeAccount：对方账户名称（收款时为付款账号，付款时为收款账号）
           oppositeId：对方账户id（收款时为付款账号id，付款时为收款账号id）
           oppositeBankno：对方银行代码银行转帐时使用（开户行）
           oppositeBankcode：对方银行帐号(银行转帐时使用)
           detailId：另一条明细id，内部非支出性转账时使用
           billAmount：票面金额
           balance：余额
           credit：收入
           debit：支出
           auditorName|：经手人
           partnerName：合作方经手人姓名
           transactionType：事务类型  1-销售事务
           billQuantity：票据数量
           billCat：票据类型
           billPeriod：1-本月票据,2-非本月票据
       }
   }

   kind=4时，现金支票/转账支票(内部支票)的使用信息{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           chequeNo：支票号
           type：1-转帐支票，2-现金汇票
           account：账号
           bankName：银行名称
           state：1-未使用,2-已使用,3-作废
           amount：金额
           billAmount：票面金额
           summary：摘要
           purpose：用途
           expireDate：支票到期日
           receiveCorp：收款单位
           receiveDate：/接收日期
           receiver：接收经手人
           operator：支付经手人
           financialHandling：财务经手人
           dateType：支票的用途  1-初始金额冲减,2-转帐交易,3-收入,4-支出
           billQuantity：票据数量
           billCat：票据类型
           billPeriod：1-本月票据,2-非本月票据
       }
   }

   kind=5时，承兑汇票/转账支票(外部支票)的信息信息{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           returnNo：支票号
           type：1-转帐支票，2-承兑汇票
           amount：金额
           billAmount：票面金额
           summary：摘要
           purpose：用途
           category：种类:1-贷款,2-借款,3-投资款,4-废品,5-其他
           categoryDesc：类别说明,当类别为5-其他时,录入文字补充性说明
           operatorName：接收经手人姓名
           partnerName：合作方经手人姓名
           receiveDate：收到票据日期
           payer：付款单位
           originalCorp：原始出具票据单位
           bankName：出具支票/汇票银行(出具支票银行)
           expireDate：支票到期日期
           state：1-有效,2-存入银行,3-作废，4-已支出
           accout：存入银行帐号
           depositDate：存入银行日期
           depositor：存入经手人ID
           depositorName：存入经手人姓名
           receiveAccountDate：到帐日期
           createName：创建人（财务经手人）
           saveBankName：存入支票/汇票的银行名称
           accountId：存入存入银行的id
           billType：1-收入,2-支出
       }
   }


   kind=6时，项目是内部非支出性转账的取现金中的基本户{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           type：1-初始金额冲减,2-转帐交易,3-收入,4-支出
           method：0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
           oppositeCorp：付款单位
           receiveAccountDate：到账时间
           auditDate：业务发生日期
           summary：摘要
           purpose：用途
           oppositeAccount：对方账户名称（收款时为付款账号，付款时为收款账号）
           oppositeId：对方账户id（收款时为付款账号id，付款时为收款账号id）
           oppositeBankno：对方银行代码银行转帐时使用（开户行）
           oppositeBankcode：对方银行帐号(银行转帐时使用)
           detailId：另一条明细id，内部非支出性转账时使用
           billAmount：票面金额
           balance：余额
           credit：收入
           debit：支出
           Integer chequeId：支票id
           chequeNo：支票号
           bankBase：账户类型 (是否为基本户)
       }
   }

   kind=7时，项目是内部非支出性转账的取现金中的非基本户{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           type：1-初始金额冲减,2-转帐交易,3-收入,4-支出
           method：0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
           oppositeCorp：付款单位
           receiveAccountDate：到账时间
           summary：摘要
           purpose：用途
           oppositeAccount：对方账户名称（收款时为付款账号，付款时为收款账号）
           oppositeId：对方账户id（收款时为付款账号id，付款时为收款账号id）
           oppositeBankno：对方银行代码银行转帐时使用（开户行）
           oppositeBankcode：对方银行帐号(银行转帐时使用)
           detailId：另一条明细id，内部非支出性转账时使用
           billAmount：票面金额
           balance：余额
           credit：收入
           debit：支出
       }
   }

   kind=8时，项目是内部非支出性转账的除去取现金的数据{
       content：详情数据（简略的详情，具体问题具体解决）{
           id：id
           type：1-初始金额冲减,2-转帐交易,3-收入,4-支出
           method：0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
           oppositeCorp：付款单位
           receiveAccountDate：到账时间
           summary：摘要
           purpose：用途
           oppositeAccount：对方账户名称（收款时为付款账号，付款时为收款账号）
           oppositeId：对方账户id（收款时为付款账号id，付款时为收款账号id）
           oppositeBankno：对方银行代码银行转帐时使用（开户行）
           oppositeBankcode：对方银行帐号(银行转帐时使用)
           detailId：另一条明细id，内部非支出性转账时使用
           billAmount：票面金额
           balance：余额
           credit：收入
           debit：支出
       }
   }
*
*
*
*
*
*/
export function getDetail(data) {
    return request({
        url: '/appCommodity/getDetail.do',
        data: data
    })
}


/*
 * creator: hxz 2025-02-28
 * 获取现金/备用金与银行账户详情(承兑汇票/转账支票存入银行详情)
 *  params: detailId：详情id
*/

/* response :
 *  按kind的返回值取值
	当kind：1-来源于报销数据的现金/银行转账信息
	personnelReimburse：报销数据{
		id：报销id
		feeCat：费用类别
		billCat：票据类型
		feeCatName：费用类别名称
		billCatName：票据类型名称
		summary：摘要
		purpose：用途(即报销事由)
		billQuantity：票据数量
		amount：合计金额
		billAmount：票面金额
		memo：备注
		payMethod：支出方式,0-未两讫,1-现金,2-现金支票,3-银行转帐
		payDate：支出日期
		account：银行帐号
		createName：创建人
		createDate：创建时间
		approveStatus：审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
		detailId：数据详情id
		transactionType：事务类型:1-销售事务,其它-非销售事务
		beginDate：发生日期(开始)
		endDate：发生日期（截止）
		subType：帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出
	}
	financeAccountBillImages：图片信息{
		id：id
		uplaodPath：文件上传路径
		orders：排序
	}

	当kind：2-项目是收入的现金/银行转账信息，3-项目是支出的现金/银行转账信息
	accountDetail：数据{
		id：数据详情id
		credit：收入金额
		debit：支出金额
		memo：备注
		creator：创建人
		createName：创建人
		createDate：创建时间
		auditorName：经手人
		type：1-初始金额冲减,2-转帐交易,3-收入,4-支出
		method：0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
		genre：类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
		categoryDesc：当类别为其他时候的说明
		oppositeCorp：付款单位(收款单位)
		summary：摘要
		purpose：用途
		accountBank：账号+银行名
		billAmount：票面金额
		source：数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
			5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8报销时多付出去的款---需收回的款(1.231差额处理) 9多收来的款(1.233差额处理)
			10-多收来的款-需收回的款(1.233差额处理)
		subType：帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出
		stakeholderCategory：类别:1-供应商,2-员工,3-自行录入
		businessType;//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款，0103-常规借款借出，0104-常规借款收款
		billQuantity：票据数量
		billDate：票据日期
		factDate：实际(付款/支出)日期'
		bankBase：账户类型 (是否为基本户)
		taxName：税种名称
	}
	financeAccountBillImages：图片信息{
		id：id
		uplaodPath：文件上传路径
		orders：排序
	}

	当kind：4-现金支票/内部支票的信息
	financeChequeDetail：支票数据{
		id：内部票据id
		chequeNo：支票号
		type：1-转帐支票，2-现金汇票
		account：账号
		bankName：银行名称
		state：1-未使用,2-已使用,3-作废
		amount：金额
		billAmount：票面金额
		summary：摘要
		purpose：用途
		expireDate：支票到期日
		operateDate：发生日期
		receiveCorp：收款单位
		receiveDate：接收日期
		receiver：接收经手人
		operator：支付经手人
		memo：备注
		createName：创建人
		createDate：创建时间
		financialHandling：财务经手人
		billQuantity：票据数量
		isBasic：是否为基本户
		billDate：票据日期
		subType：帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出
	}

	当kind：5-承兑汇票/外部支票的信息
	financeReturn：回款数据{
		id：回款票据id
		returnNo：支票号码
		type：1-转帐支票，2-承兑汇票
		category：种类:1-贷款,2-借款,3-投资款,4-废品,5-其他
		categoryDesc：类别说明,当类别为5-其他时,录入文字补充性说明
		amount：金额
		billAmount：票面金额(所开具发票或收据的金额)
		summary：摘要
		purpose：用途
		receiveDate：收到票据日期(收到日期)
		payer：付款单位
		originalCorp：原始出具票据单位(最初出具的单位)
		bankName：出具支票/汇票银行(出具支票银行/出具的银行)
		expireDate：支票到期日期(到期日)
		state：1-有效,2-存入银行,3-作废，4-已支出
		accout：存入银行帐号
		depositDate:存入银行日期
		depositorName:存入经手人姓名
		receiveAccountDate:到帐日期
		createName:创建人（财务经手人）
		createDate:创建时间
		billType:1-收入,2-支出
		billQuantity:票据数量
		oppositeCorp:收款单位
		customerName:客户名称
		subType：帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出
	}


	当kind：6-项目是内部非支出性转账的除去取现金的数据，7-项目是内部非支出性转账的取现金中的非基本户，8-项目是内部非支出性转账的取现金中的基本户
	accountDetail：数据{
		id：数据详情id
		credit：收入金额
		debit：支出金额
		memo：备注
		creator：创建人
		createName：创建人
		createDate：创建时间
		auditorName：经手人
		type：1-初始金额冲减,2-转帐交易,3-收入,4-支出
		method：0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
		genre：类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
		categoryDesc：当类别为其他时候的说明
		oppositeCorp：付款单位(收款单位)
		summary：摘要
		purpose：用途
		accountBank：账号+银行名
		billAmount：票面金额
		source：数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
			5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8报销时多付出去的款---需收回的款(1.231差额处理) 9多收来的款(1.233差额处理)
			10-多收来的款-需收回的款(1.233差额处理)
		subType：帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出
		stakeholderCategory：类别:1-供应商,2-员工,3-自行录入
		businessType;//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款，0103-常规借款借出，0104-常规借款收款
		billQuantity：票据数量
		billDate：票据日期
		factDate：实际(付款/支出)日期'
		bankBase：账户类型 (是否为基本户)
		taxName：税种名称
		oppositeAccount:对方账户名称（收款时为付款账号，付款时为收款账号）
		oppositeId:对方账户id（收款时为付款账号id，付款时为收款账号id）
		accountBank:账号+银行名
		oppositeBankno:对方银行代码银行转帐时使用（开户行）
		oppositeBankcode:对方银行帐号(银行转帐时使用)
		chequeId:内部票据id
		chequeNo：支票号
	}

 *
 *
*/

export function getDataDetail(data) {
    return request({
        url: '/data/getDataDetail.do',
        data: data
    })
}

/*
 * creator: hxz 2025-03-07
 * 回款票据详情
 *  传递参数：
	returnId = 695
*/
export function getReturnDetail(data) {
    return request({
        url: '/return/getReturnDetail.do',
        data: data
    })
}


/*
 * creator: hxz 2025-02-28
 * 常规借款-支出(款项借出)/收入(款项借入)
 *  传递参数：
	Integer id：借款id
	Integer sourceType:1-pc端查看 2-手机端查看
	Integer accountDetailId：详情id
*/

/*
* 返回值：

	summary：摘要
	purpose：用途
	sumRepaymentAmount：已付款金额(已收款金额)
	loanDetail：借款信息{
		id：借款id
		borrower：借入方(收款方)
		loaner：借出方(出资方)
		loanType：借贷类型:1-借入(默认1),2-借出
		principalAmount：本金金额
		nominalRate：名义利率
		repaymentMethod：归还本金的约定:1-未约定具体日期,2-已约定归还的日期
		repaymentDate：已约定归还的日期
		incomeMethod：本金型式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
		interestMethod：利息的支付方式：0-无利息,1-按日付,2-按月付,3-按年付
		interestAccrualDate：开始计息日期
		periodRepaymentDate：每期还款日
		periodRepaymentAmount：每期还款金额
		credit：本期借(收入金额)
		debit：本期贷(支出金额)
		memo：备注
		amountRepayed：已还金额
		createName：创建人
		createDate：创建时间
		billReceiveDate：收到支票或汇票日期
		billNo：支票或汇票号码
		billEndDate：支票或汇票到期日
		billSource：出具支票或汇票单位
		billBank：出具支票或汇票银行
		arriveDate：到账日期
		receiveBank：收款银行
		accountId：收款银行账户ID
		account：银行账号
		returnState：票据状态：1-有效,2-存入银行,3-作废，4-已支出
		partnerName：合作方经手人
		operatorName：经手人
		paymentDate：付款日期
		withinOrAbroad：支出转账支票时使用，1-内部 0-外部
		returnId：支出时的票据id
		oppositeAccount：账户名称
		oppositeBankcode：对方银行账号
		oppositeBankno：开户行
	}
	borrorModList：借款的修改记录{
		id：id
		borrowCommon：借款ID
		borrower：借入方(收款方)
		loaner：借出方(出资方)
		loanType：借贷类型:1-借入(默认1),2-借出
		principalAmount：本金金额
		nominalRate：名义利率
		repaymentMethod：归还本金的约定:1-未约定具体日期,2-已约定归还的日期
		repaymentDate：已约定归还的日期
		incomeMethod：本金型式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
		interestMethod：利息的支付方式：0-无利息,1-按日付,2-按月付,3-按年付
		interestAccrualDate：开始计息日期
		periodRepaymentDate：每期还款日
		periodRepaymentAmount：每期还款金额
		credit：本期借(收入金额)
		debit：本期贷(支出金额)
		memo：备注
	}
	repaymentList：付款记录{
		id：id
		borrowCommon：借款ID
		loanType：借贷类型:1-付款(默认1),2-收款'
		repaymentTime：还款时间
		repaymentMethod：还款方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
		repaymentAmount：还款金额
		repaymentCapital：还款本金
		repaymentIntest：还款利息
		memo：备注
		createName：创建人
		createDate：创建时间
	}

*
* */

export function ordLoanDetail(data) {
    return request({
        url: '/loan/ordLoanDetail.do',
        data: data
    })
}


/*
 * creator: hxz 2025-02-28
 * 常规借款-付款(还款)/收款(收回借款)
 *  传递参数：
	Integer id：还款/收款id
*/

/*
*返回值：
	{
		id：还款/收款id
		borrowCommon：借款ID
		loanType：借贷类型:1-付款(默认1),2-收款'
		repaymentTime：还款时间
		repaymentMethod：还款方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
		repaymentAmount：还款金额
		repaymentCapital：还款本金
		repaymentIntest：还款利息
		memo：备注
		createName：创建人
		createDate：创建时间
		billType：票据类型：1-内部支票 0-外部支票
		accountId：收款银行账户ID
		billNo：支票或汇票号
		billEndDate：支票或汇票到期日
		billReceiveDate：收到支票或汇票日期
		receiveOperatorName：接收经手人
		paymentOperatorName：支付经手人
		receiveBank：收款银行
		checkId：支票或汇票id
		arriveDate：到账日期
		billSource：出具支票或汇票单位
		billBank：出具支票或汇票银行
		account：银行账号
		returnState：票据状态：1-有效,2-存入银行,3-作废，4-已支出
		payer：付款单位
		summary：摘要
		purpose：用途
	}

* */

export function ordRepaymentDetail(data) {
    return request({
        url: '/loan/ordRepaymentDetail.do',
        data: data
    })
}

/*********************************************************
 * creator: hxz 2025-02-28
 * 常规借款-付款(还款)/收款(收回借款) - 修改记录
 *  传递参数：
 Integer id：还款/收款id
 */
export function ordRecordModHistoryList(data) {
    return request({
        url: '/loan/ordRecordModHistoryList.do',
        data: data
    })
}

/*********************************************************
 * creator: hxz 2025-02-28
 * 常规借款-付款(还款)/收款(收回借款) - 修改记录 - 详情
 *  传递参数：
 Integer id：还款/收款id
 */
export function ordRecordModHistoryDetail(data) {
    return request({
        url: '/loan/ordRecordModHistoryDetail.do',
        data: data
    })
}


/*********************************************************
 * creator: hxz 2025-02-28
 * 常规借款-付款(还款)/收款(收回借款) - 修改记录 - 详情
 *  传递参数：
 Integer tBorrowCommonHistoryId
 */
export function getLoanHistoryDetailCompare(data) {
    return request({
        url: '/loan/getLoanHistoryDetailCompare.do',
        data: data
    })
}


/*
 * creator: hxz 2025-02-28
 * 客户的回款详情
 *  传递参数：
	Integer collectId：回款id
*/

/*
* 返回值：

	summary：摘要
	purpose：用途
	customerName：客户名称
	slCollectApplication：回款数据{
		id：回款id
		itemCount：明细数量
		amount：回款金额
		overpayment：多付金额
		overpayPurpose：多付款用途:1-用于借款
		customer：客户ID
		recordType：录入方式:0-非财务录入,1-财务录入
		method：回款方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
		expectBeginDate：预计到帐开始日期
		expectEndDate：预计到帐截止日期
		receiveDate：收到票据日期
		receiveBank：收款银行
		returnNo：支票号码
		payer：付款单位
		originalCorp：原始出具票据单位
		bankName：出具支票银行(银行转账的收款银行名称)
		expireDate：到期日期(银行转账的到账日期)
		applyDate：申请日期
		approvalDate：受理时间
		financeTime：财务入账时间
		ordersState：订单状态:1-已全分配,2-需补发,3-补发完毕
		financeAccountDetail：关联的财务明细表ID
		financeAccountBill：关联的财务明细票据表ID
		financer：财务ID
		financerName：财务姓名
		financerTime：财务姓名
		saler：销售ID
		salerName：销售姓名
		salerTime：回款处置时间
		memo：备注
		description：描述
		creator：创建人id
		createName：创建人
		createDate：创建时间
		passEntry=false：实际入帐标志:true-是,false-否

		//承兑汇票/转账支票存入银行后相关的时间
		depositDate：存入时间
		receiveAccountDate：到账时间
		receiveBankName：收款银行
		customerName：客户名称
		depositorName；存入经手人
	}

* */

export function getSlCollectDetailPC(data) {
    return request({
        url: '/collectWindow/getSlCollectDetailPC.do',
        data: data
    })
}


/*
 * creator: hxz 2025-02-28
 * 职工工资/个人所得税/职工社保/职工公积金详情(以及详情中的详情)**
 *  传递参数：
	Integer businessHistory：businessHistory的id()
*/

/*
* 返回值：

	userNum:人数
	pay:实发总额
	summary:摘要
	purpose:用途
	mapList:列表信息{
		payId:id
		userId：职工id
		userName：名称
		mobile：手机号
		departName：部门名
		postName：岗位
		factPay：实发金额
	}
	personnelPayHistory：工资信息{
		id：id
		personnelPay:工资id
		period：所属月份
		type：类型:1-工资,2-所得税,3-社保,4-公积金
		pay：应付金额
		factPay：实付金额
		payTime：发放时间
		payWay：发放方式:1-现金,2-转帐
		creator：创建人
		createName：创建人
		createDate：创建时间
		versionNo：版本号
	}
	financeAccount：银行账户信息{
		id：id
		isPublic：是否为对公帐号:1-是 0-否
		bankName：开户行(银行名称)
		name：账户名称
		account:帐户
		isBasic：是否是 基本户 true-是，false-否
	}

* */

export function getPayHistory(data) {
    return request({
        url: '/salary/getPayHistory.do',
        data: data
    })
}


/*
 * creator: hxz 2025-02-28
 * 职工工资/个人所得税/职工社保/职工公积金修改记录
 *  传递参数：
	Integer type：1-工资 2-所得税 3-社保 4-公积金
	String period：所属月份
	Integer versionNo：版本号
*/

/*
*
返回值：

	personnelPayHistories：修改记录信息{
		id：id
		personnelPay：工资id
		period：所属月份
		type：类型:1-工资,2-所得税,3-社保,4-公积金
		creator：创建人
		createName：创建人
		createDate：创建时间
		updator：修改人
		updateName：修改人
		updateDate：修改时间
		versionNo：版本号
		subVersionNo：子版本号(修改记录，值为数字几则为第几次修改，值为0时则为原始信息)
	}


*
* */

export function getRecords(data) {
    return request({
        url: '/salary/getRecords.do',
        data: data
    })
}


/*
 * creator: hxz 2025-02-28
 * 职工工资/个人所得税/职工社保/职工公积金修改记录详情**
 *  传递参数：
	Integer type：1-工资 2-所得税 3-社保 4-公积金
	String period：所属月份
	Integer versionNo：版本号
	Integer subVersionNo：子版本号
*/

/*
*
返回值：

	personnelPayHistories：修改后信息{
		type：1-工资 2-所得税 3-社保 4-公积金
		pay：实发总额
		payTime：发放日期
		payWay：发放方式:1-现金,2-转帐
		operation：操作:1-增,2-删,3-改,4-调财务数据,5-修改发放信息,6-修改工资数,7-修改支出信息,8-修改职工项
		mapList：列表信息{
			payHistoryId:id
			userId：职工id
			userName：名称
			mobile：手机号
			departName：部门名
			postName：岗位
			factPay：实发金额
		}
		financeAccount：账户信息{
			id：id
			isPublic：是否为对公帐号:1-是 0-否
			bankName：开户行(银行名称)
			name：账户名称
			account:帐户
			isBasic：是否是 基本户 true-是，false-否
		}
		createDate：修改时间
		createName：修改人
		createDate0：创建时间
		createName0：创建人
	}

	personnelPayHistoriesOld：修改前信息{
		type：1-工资 2-所得税 3-社保 4-公积金
		pay：实发总额
		payTime：发放日期
		payWay：发放方式:1-现金,2-转帐
		operation：操作:1-增,2-删,3-改,4-调财务数据,5-修改发放信息,6-修改工资数,7-修改支出信息,8-修改职工项
		mapList：列表信息{
			payHistoryId:id
			userId：职工id
			userName：名称
			mobile：手机号
			departName：部门名
			postName：岗位
			factPay：实发金额
		}
		financeAccount：账户信息{
			id：id
			isPublic：是否为对公帐号:1-是 0-否
			bankName：开户行(银行名称)
			name：账户名称
			account:帐户
			isBasic：是否是 基本户 true-是，false-否
		}
		createDate：修改时间
		createName：修改人
		createDate0：创建时间
		createName0：创建人
	}

*
* */
export function getRecordDetails(data) {
    return request({
        url: '/salary/getRecordDetails.do',
        data: data
    })
}


/*
 * creator: kl 2025-05-09
 * 返回贷款列表(已完结)**
 *  传递参数：
	boolean state：借款状态，true开启，false关闭
	String loanType：借贷类型:1-借入(默认1)
*/

/*
*
返回值：

	list：公司贷款列表{
		id：id
		loaner：出资方
		principalAmount：本金金额
		arriveDate：收款日期
	}

*
* */
export function getLoansList(data) {
    return request({
        url: '/loan/getLoanCredit.do',
        data: data
    })
}









