import { createStore } from 'vuex'
import createPersistedstate from 'vuex-persistedstate'
import JSONBig from 'json-bigint'
import actions from './action'
const state = {
  version: 0,
  activePage: JSONBig.parse(localStorage.getItem('wonderssPersistedstate'))?.dataList?.activePage || [],
  manage: 0,
  manageCode: '',
  manageParam: {},
}
const mutations = {
  SET_VERSION (state, userBadge) {
    state.version = userBadge.ver
  },
  SET_ACTIVE_PAGE (state, obj) {
    state.activePage = obj
  },
  SET_MANAGE(state, num) {
    state.manage = num
  },
  SET_MANAGE_CODE(state, code) {
    state.manageCode = code
  },
  SET_MANAGE_PARAM(state, obj) {
    console.log('SET_MANAGE_PARAM', obj)
    state.manageParam = obj
  }
}
const getters = {
  getVersion (state) {
    return state.version
  },
  getActivePage(state){
    return state.activePage
  },
  getManage(state){
    return state.manage
  },
  getManageCode(state){
    return state.manageCode
  },
  getManageParam(state){
    return state.manageParam
  },
}

export default createStore({
  state,
  mutations,
  getters,
  actions,
  plugins: [createPersistedstate({
    key: 'wonderssPersistedstate',
    storage: localStorage,
    reducer(val) {
      return {
        dataList: {
          activePage: val.activePage
        }
      }
    }
  })]
})