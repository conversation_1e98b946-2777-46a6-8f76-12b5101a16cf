<!-- 搜索 年 月列表  -->
<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :showBack="true" :runMyBackFun="true" @myBackFun="backFun"  :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <p class="margin-10-15">{{ todayTime }}</p>
  <div class="hr_gray"></div>
  <div class="current-balance-1 margin-10-15">
    <div>
      <p class="ty-color-orange">{{ bankName }}</p>
      <p>本期余额</p>
    </div>
    <span>{{ allBalance }}</span>
  </div>
  <div class="hr_gray"></div>
  <div class="flex">
    <div class="balance1" v-for="(item) in bala_list" :key="item.id">
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <div class="flex date-account">
    <div @click="tab(1)" :class="num === 1 ? 'padding_bottom_green':''">按时间查看</div>
    <div @click="tab(2)" :class="num === 2 ? 'padding_bottom_green':''">按账户查看</div>
  </div>
  <div>
    <router-view />
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "currentAccountDate",
  mixins: [finance],
  data() {
    return {
      accID:'',
      num:1,
      todayTime:'今天是XXXX年X月XX日 星期X',
      allBalance:0,
      bala_list: [
      ],
      accountList: []
    }
  },
  mounted() {
    this.getAccountList()
    let name = this.$route.name
    if(name === 'viewByTimemonthList'){
      this.num = 1
    }else{
      this.num = 2
    }
  },
  methods: {
    backFun(){
      this.$router.push("/timePicker");
    },
    tab(num){
      if(this.num != num){
        this.num = num
        if(num === 1){
          let path = `/viewByTimemonthList`
          this.$router.push({ path:path })
        }else if(num === 2){
          let path = `/ViewByAccountMonthList`
          this.$router.push({ path:path })
        }
      }
    },
    getAccountList(){
      let org = this.auth.getOrg()
      let beginDate = localStorage.getItem('searchYearDur_monthBegin')
      let endDate = localStorage.getItem('searchYearDur_monthEnd')
      if(beginDate && endDate){
        this.todayTime = beginDate + ' ~ ' + endDate
        let params = { oid:org.id , state:5 , beginDate:beginDate, endDate:endDate }
        this.financeApi.financeAmount(params).then(res1=>{
          let res = res1.data.data
          this.allBalance = this.formatCurrency(res.allBalance)
          this.bankName = res.bankName
          this.bala_list = [
            {id: 0, da: "上期余额", money: this.formatCurrency(res.allPreviousBalance ) , sr: this.balance1 },
            {id: 1, da: "本期收入", money: this.formatCurrency(res.allCredit ) , sr:  this.balance2 ,style: "ty-color-green"},
            {id: 2, da: "本期支出", money: this.formatCurrency(res.allDebit) , sr: this.balance3 ,style: "ty-color-red"},
          ]

        }).catch(err=>{
          console.log('err=',err)
          this.$message.error('链接失败，请重试！')
        })

      }else{
        this.$message.error('没有具体日期哦！')
      }

    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
.centerSp{ text-align: center; }
</style>