package cn.sphd.miners.modules.thirdPlatform.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * 电商sku生成器表
 * <AUTHOR>
 * @date 2023年02月25日 11:33
 **/
@Entity
@Table(name = "t_tp_pay_notify")
public class TpPayNotify extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    @Column(name = "rpc_log_id")
    private Long rpcLogId;//`rpc_log_id` BIGINT DEFAULT NULL COMMENT 'RPC日志id',
    @Column(name = "tg_code")
    private Byte tgCode;//`tg_code` TINYINT DEFAULT NULL COMMENT '第三方大厂(1-weChat,2-byteDance)',
    @Column(name = "tg_notify_id", length = 36)
    private String tgNotifyId;//`tg_notify_id` VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_bin  DEFAULT NULL COMMENT '第三方通知ID',
    @Column(length = 32)
    private String mchid;//`mchid` VARCHAR(32) CHARACTER SET latin1 COLLATE latin1_bin  DEFAULT NULL COMMENT '商户号',
    @Column(name = "serial_number", length = 40)
    private String serialNumber;//`serial_number` VARCHAR(40) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '证书序列号Wechatpay-Serial',
    @Column
    private String signature;//`signature` TEXT CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '应答签名Wechatpay-Signature',
    @Column(length = 32)
    private String nonce;//`nonce` VARCHAR(32) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '应答随机串Wechatpay-Nonce',
    @Column(length = 20)
    private String timestamp;//`timestamp` VARCHAR(20) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '应答时间戳Wechatpay-Timestamp',
    @Column(name = "sign_type", length = 32)
    private String signType;//`sign_type` VARCHAR(32) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '认证类型Wechatpay-Signature-Type',
    @Column
    private String body;//`body` TEXT DEFAULT NULL COMMENT '应答主体body',
    @Column(name = "original_type", length = 20)
    private String originalType;//`original_type` VARCHAR(20) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '通知类型',
    @Column(name = "decoded_data")
    private String decodedData;//decoded_data` TEXT DEFAULT NULL COMMENT '交易数据',
    @Column
    private String error;//`error` TEXT DEFAULT NULL COMMENT '报错信息',
    @Column(name = "notify_status")
    private Byte notifyStatus;//`notify_status` TINYINT DEFAULT NULL COMMENT '通知结果:1操作成功,2重复通知,3找不到订单,4支付未完成,-1操作失败,-2未知操作',
    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;//`create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRpcLogId() {
        return rpcLogId;
    }

    public void setRpcLogId(Long rpcLogId) {
        this.rpcLogId = rpcLogId;
    }

    public Byte getTgCode() {
        return tgCode;
    }

    public void setTgCode(Byte tgCode) {
        this.tgCode = tgCode;
    }

    public String getTgNotifyId() {
        return tgNotifyId;
    }

    public void setTgNotifyId(String tgNotifyId) {
        this.tgNotifyId = tgNotifyId;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getOriginalType() {
        return originalType;
    }

    public void setOriginalType(String originalType) {
        this.originalType = originalType;
    }

    public String getDecodedData() {
        return decodedData;
    }

    public void setDecodedData(String decodedData) {
        this.decodedData = decodedData;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public Byte getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(Byte notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public TpPayNotify() {
    }

    public TpPayNotify(Long rpcLogId, Byte tgCode, String tgNotifyId, String mchid, String serialNumber, String signature, String nonce, String timestamp, String signType, String body, String originalType, String decodedData, String error, ThirdPlatformService.NotifyStatus notifyStatus) {
        this.rpcLogId = rpcLogId;
        this.tgCode = tgCode;
        this.tgNotifyId = tgNotifyId;
        this.mchid = mchid;
        this.serialNumber = serialNumber;
        this.signature = signature;
        this.nonce = nonce;
        this.timestamp = timestamp;
        this.signType = signType;
        this.body = body;
        this.originalType = originalType;
        this.decodedData = decodedData;
        this.error = error;
        this.notifyStatus = notifyStatus.getIndex();
    }
}