<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TaccountantSubjectJournalMapper" >
  <resultMap id="BaseResultMap" type="TaccountantSubjectJournal" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="org" property="org" jdbcType="INTEGER" />
    <result column="establish" property="establish" jdbcType="INTEGER" />
    <result column="change_date" property="changeDate" jdbcType="DATE" />
    <result column="subject1" property="subject1" jdbcType="INTEGER" />
    <result column="subject2" property="subject2" jdbcType="INTEGER" />
    <result column="subject3" property="subject3" jdbcType="INTEGER" />
    <result column="subject4" property="subject4" jdbcType="INTEGER" />
    <result column="memo" property="memo" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="INTEGER" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="INTEGER" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
  </resultMap>

<!--  <sql id="Base_Column_List" >-->
<!--    id, org, change_date as changeDate, subject1, subject2, subject3, subject4, memo, creator, create_name as createName,-->
<!--    create_date as createDate, updator, update_name as updateName, update_date as updateDate-->
<!--  </sql>-->

  <sql id="Base_Column_List" >
    id, org, establish, change_date, subject1, subject2, subject3, subject4, memo, creator, create_name,
    create_date, updator, update_name, update_date
  </sql>

  <select id="listPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_accountant_setting_history
  </select>

  <select id="getSubjectJournallistPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_accountant_subject_journal
    where establish = #{obj.establish} and org=#{obj.org} order by change_date desc
  </select>

  <select id="getSingle" resultMap="BaseResultMap" parameterType="TaccountantSubjectJournal" >
    select 
    <include refid="Base_Column_List" />
    from t_accountant_subject_journal
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="getSubjectJournal" resultType="TaccountantSubjectJournal" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_accountant_subject_journal
    where establish = #{establish} and org=#{org} order by change_date desc
  </select>

  <select id="getByCurDate" resultType="TaccountantSubjectJournal" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_accountant_subject_journal
    where org=#{org} and DATE_FORMAT(change_date, '%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d')
  </select>

  <delete id="delete" parameterType="TaccountantSubjectJournal" >
    delete from t_accountant_subject_journal
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="TaccountantSubjectJournal" useGeneratedKeys="true" keyProperty="id" >
    insert into t_accountant_subject_journal
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="org != null" >
        org,
      </if>
      <if test="establish != null" >
        establish,
      </if>
      <if test="changeDate != null" >
        change_date,
      </if>
      <if test="subject1 != null" >
        subject1,
      </if>
      <if test="subject2 != null" >
        subject2,
      </if>
      <if test="subject3 != null" >
        subject3,
      </if>
      <if test="subject4 != null" >
        subject4,
      </if>
      <if test="memo != null" >
        memo,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createName != null" >
        create_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="updateName != null" >
        update_name,
      </if>
      <if test="updateDate != null" >
        update_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null" >
        #{org,jdbcType=INTEGER},
      </if>
      <if test="establish != null" >
        #{establish,jdbcType=INTEGER},
      </if>
      <if test="changeDate != null" >
        #{changeDate,jdbcType=DATE},
      </if>
      <if test="subject1 != null" >
        #{subject1,jdbcType=INTEGER},
      </if>
      <if test="subject2 != null" >
        #{subject2,jdbcType=INTEGER},
      </if>
      <if test="subject3 != null" >
        #{subject3,jdbcType=INTEGER},
      </if>
      <if test="subject4 != null" >
        #{subject4,jdbcType=INTEGER},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="TaccountantSubjectJournal" >
    update t_accountant_subject_journal
    <set >
      <if test="org != null" >
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="establish != null" >
        establish = #{establish,jdbcType=INTEGER},
      </if>
      <if test="changeDate != null" >
        change_date = #{changeDate,jdbcType=DATE},
      </if>
      <if test="subject1 != null" >
        subject1 = #{subject1,jdbcType=INTEGER},
      </if>
      <if test="subject2 != null" >
        subject2 = #{subject2,jdbcType=INTEGER},
      </if>
      <if test="subject3 != null" >
        subject3 = #{subject3,jdbcType=INTEGER},
      </if>
      <if test="subject4 != null" >
        subject4 = #{subject4,jdbcType=INTEGER},
      </if>
      <if test="memo != null" >
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>