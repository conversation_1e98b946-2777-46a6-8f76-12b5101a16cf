<template>
  <div id="reviewFinanceReimburse" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="付款复核" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待复核" name="1" closable='false' :msgCount="financeReview.length">
        <div class="tip">
          <p>下列待付款项有待您复核。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump(item.id, '1', 1,  item.billCat)" v-for="(item, index) in financeReview" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待付款" name="2" closable='false'>
        <div class="tip">
          <p>下列待付款项复核通过，有待付款。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump(item.id, '1', 1, item.billCat)" v-for="(item, index) in cashierTwoSettled" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
  #reviewFinanceReimburse{
    .tabs-tab{ width:50%;  }
    .ui-cell{ display: block; }
    .item_fn{
      display: flex;
      div:nth-child(1){ flex:1;  }
      div:nth-child(2){ flex:3;  }
    }
    .txt_right{
      text-align: right;
      font-size:11px;
      padding-right:15px;
    }
    .tip{
       padding:5px 12px;
       background-color: #E2EFDA;
       display: block;
     }
  }
</style>

<script>
var that
export default {
  name: 'reviewFinanceReimburse',
  data () {
    return {
      cashierTwoSettled: [],
      financeReview: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    this.tabValue = this.$store.getters.getPayReimburseNav
    that = this
    this.listenersUid = [
      // 待复核
      this.sphdSocket.subscribe('paymentReviewHandle', function (data) {
        console.log('financeReview session Socket received OK:' + data)
        console.log(JSON.parse(data))
        that.financeReview = JSON.parse(data)
      }),
      this.sphdSocket.subscribe('paymentReviewHandle', function (data) {
        console.log('待复核审批 订阅:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financeReview.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.financeReview.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.financeReview.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待付款
      this.sphdSocket.subscribe('paymentReviewApproval', function (data) {
        that.cashierTwoSettled = JSON.parse(data)
        console.log('cashierTwoSettled session Socket received OK:' + data)
        console.log(JSON.parse(data))
      }),
      this.sphdSocket.subscribe('paymentReviewApproval', function (data) {
        console.log('cashierTwoSettled session Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierTwoSettled.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.cashierTwoSettled.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierTwoSettled.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('notice', function (data) {
        console.log('notice Socket received OK:' + data)
        if (data === '1') {
          that.$router.push({
            path: '/approveReimburse'
          })
        }
      })
    ]
    let oid = this.sphdSocket.user.oid
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('cashier', { oid: oid, session: session })
    this.sphdSocket.send('paymentReviewApprover', { 'session': session, 'userId': userId })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id, tab, num, billCat) {
      this.$store.dispatch('setPayReimburseNav', tab)
      if (num === 1) {
        this.$router.push({
          path: `/reviewApproveFinanceDetail/${id}/${billCat}`
        })
      }
    },
    search: function () {
      this.$router.push({
        path: `/reviewApproveFinanceSearch`
      })
    }
  }
}
</script>
