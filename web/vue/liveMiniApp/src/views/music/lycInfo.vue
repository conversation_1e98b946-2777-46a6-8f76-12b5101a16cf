<template>
  <div class="lycInfo">
    <Pageheader title="基本信息" :showBack="true"/>
    <div class="" v-if="songMessage.song">
      <div class="txtCenter" @click="goPage('lycInfo')">
        <div class="ttl">{{ songMessage.song.name }}</div>
        <div>{{ songMessage.song.songDuration }}</div>
      </div>
      <div class="trAll">
        <div class="tr">
          任务发布： <span class="uName">{{ songMessage.song.createName }}</span>
          <span class="uTime">{{  createTime  }}</span>
        </div>
        <div v-if="songMessage.song.publishType == 1" class="tr">
          歌词上传：
          <span class="uName">{{ songMessage.song.createName }}</span>
          <span>{{ (new Date(songMessage.song.createTime).format('yyyy-MM-dd hh:mm:ss')) }}</span>
        </div>
        <div class="tr" v-if="songMessage.song.publishType == 2" >
          审定时间： {{ songMessage.song.createName }}
          {{ (new Date(songMessage.cmwRhythm.updateTime).format('yyyy-MM-dd hh:mm:ss')) }}
        </div>
      </div>

      <div class="live-panel live-tr" v-if="songMessage.song.publishType == 2">
        <div @click="goPage('lycUpload')">
          歌词上传者名单
          <el-icon class="live-right relaRight" >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-panel">
        <div class="live-tr live-tr-line" >
            原唱的版本
            <el-icon class="live-right relaRight" ><arrow-right-bold color="#53B5A8" class="houseSty8" /></el-icon>
        </div>
        <div class="live-tr">
          伴奏的版本
          <el-icon class="live-right relaRight" ><arrow-right-bold color="#53B5A8" class="houseSty8" /></el-icon>
        </div>
      </div>

      <div class="live-panel">
        <div class="live-tr" >
          说明:{{ songMessage.song.inllustration }}
        </div>
        <div class="live-tr">
          第一句: {{ songMessage.song.firstSentence }}
        </div>
        <div class="live-tr">
          最后一句：{{ songMessage.song.lastSentence }}
        </div>
      </div>

    </div>

  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import JSONBig from 'json-bigint'
import '@/utils/DateFormat.js'

const router = useRouter();
const songMessage = JSONBig.parse(localStorage.getItem('songMessage'));
const createTime = new Date(songMessage.song.createTime).format("yyyy-MM-dd hh:mm:ss")

onMounted(() => {
  console.log('songMessage=', songMessage)
});

const goPage = (url) => {
  router.push({ name: url});
}

</script>
<style lang="scss" scoped>
.lycInfo{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
  }
  .houseSty8{
    position: relative;
    top:5px;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .uName{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 80px;
    display: inline-block;
    position: relative;
    top:10px;
  }
  .uTime{

  }
  .trAll{
    margin-bottom: 30px;
    padding:20px;
    background: #fff;

    .tr{
      line-height: 30px;
    }
  }
}
</style>
