package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdComposition;

import java.util.List;

/**
 * Created by Administrator on 2016/10/13.
 */
public interface PdCompositionService {

    List<PdBase> getPdBase(Integer oid, String innerSn);

    PdComposition getPdCompositionById(Integer id);

    void deletePdComposition(PdComposition pdComposition);

    void savePdComposition(PdComposition pdComposition);

    void updatePdComposition(PdComposition pdComposition);

    List<PdComposition> getPdCompositionLists(Integer baseID);

}
