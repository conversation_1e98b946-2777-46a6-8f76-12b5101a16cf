<template>
  <div id="purchasePaymentFinanceApprovalDetail2" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="付款复核"></TY_NavTop>
    <div class="detailInfo">
      <!--票据-->
      <div v-if="pund === 0">
        <div @click="jumpDetail(0)" >
          <span><span class="ttl1">申请付款金额 </span><span> {{detailsInfo.amount && detailsInfo.amount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span>  {{details.fullName }}</span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailsInfo.payNum || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.numApplication || 0 }}笔，共{{(detailsInfo.lockedAmount && detailsInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
      <!--预付款-->
      <div v-if="pund === 1">
        <div @click="jumpDetail(1)" >
          <span><span class="ttl1">申请付款金额 </span><span>  {{detailsInfo.planAmount && detailsInfo.planAmount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span> {{details.srmSupplier.fullName }} </span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailsInfo.payNum || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailsInfo.lockedAmount && detailsInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">计划付款金额</span>{{detailsInfo.planAmount && detailsInfo.planAmount.toFixed(2)}}元
        </div>
        <div>
          <span class="ttl1">计划付款时间</span>{{detailsInfo.planDate |formatDay('YYYY-MM-DD')}}
          <span class="redTxt"><span class="ttl1">计划付款方式</span>{{isture}}</span>
        </div>
        <div class="martop10">
          <span class="ttl1">付款事由</span>采购的预付款
        </div>
      </div>
      <!--多收来的款-->
      <div v-if="pund === 3">
        <div>
          <span class="ttl1">原始的付款方</span><span> {{detailsInfo.supplierName }} </span>
        </div>
        <div>
          <span><span class="ttl1">多收来的金额 </span><span>  {{detailsInfo.amount && detailsInfo.amount.toFixed(2)}} 元 </span></span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailsInfo.paidTimes || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailsInfo.lockedAmount && detailsInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
    </div>
    <div v-if="detailsInfo">
      <div class="handle_button">
        <span @click="jumpFu3()" class="btncat" style="margin-right:50px; font-size:1.2em; ">原定的付款方式</span>
      </div>
      <el-form :label-position="left" label-width="180px">
        <div v-if="details.financePayment.method === '5'" >
          <el-form-item label="拟付款方式"><span>银行转账</span></el-form-item>
          <!--          <el-form-item label="计划付款日期"><span>{{ details.financePayment.planDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>-->
          <el-form-item label="银行账户"><span>{{ details.financeAccount.name }}</span></el-form-item>
          <el-form-item label="账户类型"><span>{{ details.financeAccount.isPublic == '1' ? '对公户 ': '非对公户' }} </span></el-form-item>
          <el-form-item label="开户行"><span>{{details.financeAccount.bankName}}</span></el-form-item>
          <el-form-item label="账号"><span>{{details.financeAccount.account}}</span></el-form-item>
          <!-- <el-form-item label="账户余额"><span>{{details.financeAccount.balance}}</span></el-form-item> -->
          <el-form-item label="拟付款金额"><span>{{details.financePayment.factAmount.toFixed(2)}}</span></el-form-item>
          <el-form-item label="拟付款日期"><span>{{ details.financePayment.planDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>

        </div>
        <div v-if="details.financePayment.method === '4'">
          <el-form-item label="拟付款方式"><span>承兑汇票（外）</span></el-form-item>
          <el-form-item label="汇票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
          <el-form-item label="所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount.toFixed(2) }}</span></el-form-item>
          <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
          <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName }}</span></el-form-item>
          <p class="orangeTip" v-if="Number(detailsInfo.balanceAmount) > 0">！本次将多支付{{ detailsInfo.balanceAmount && detailsInfo.balanceAmount.toFixed(2) }}元！该差额将进入“借款管理”模块，请跟踪管理。</p>

          <div class="handle_button">
            <span @click="seeticket()" class="btncat" style="margin-right:50px; font-size:15px; ">查看汇票</span>
          </div>

        </div>
        <div v-if="details.financePayment.method === '3'">
          <div v-if="details.invoiceType == '2'">  <!--(1-其他付款方式  2-内部转账支票 3-外部转账支票)-->
            <el-form-item label="拟付款方式"><span>转账支票（内）</span></el-form-item>
            <el-form-item label="转账支票的开户行"><span>{{ details.financeAccount.bankName  }}</span></el-form-item>
            <el-form-item label="转账支票的支票号"><span>{{ details.financeChequeDetail.chequeNo }}</span></el-form-item>
            <el-form-item label="支票金额"><span>{{ details.financeChequeDetail.billAmount.toFixed(2) }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeChequeDetail.expireDate | formatDay('YYYY-MM-DD')  }} </span></el-form-item>


          </div>
          <div v-if="details.invoiceType == '3'">
            <el-form-item label="拟付款方式"><span>转账支票（外）</span></el-form-item>
            <el-form-item label="支票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
            <el-form-item label="所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount.toFixed(2) }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
            <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName}}</span></el-form-item>
            <p class="orangeTip" v-if="Number(detailsInfo.balanceAmount) > 0">！本次将多支付{{ detailsInfo.balanceAmount && detailsInfo.balanceAmount.toFixed(2) }}元！该差额将进入“借款管理”模块，请跟踪管理。</p>

            <div class="handle_button">
              <span @click="seeticket()" class="btncat" style="margin-right:50px; font-size:15px; ">查看支票</span>
            </div>

          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<style lang="less">
#purchasePaymentFinanceApprovalDetail2{
  overflow: auto!important;
  background: #fff;
  .orangeTip{
    margin-top: 20px;
    color: #ff9304;
    font-size: 12px;
    padding: 0 35px;
  }
  .el-icon-arrow-right{ float: right; font-size: 16px; }
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  .detailInfo{
    padding:10px 20px 0px 20px;
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
    line-height: 26px;
    .ttl1{
      display: inline-block;
      width: 86px;
      text-align: right;
      margin-right: 10px;
    }
  }
  .el-form-item__label, .el-form-item__content{
    line-height:25px;
  }
  .el-form-item__content{text-align: left;}
  .el-form-item{ width:364px; }
  .el-textarea{ width: 232px!important; margin-top: 5px; }
  .el-input{ width: 232px!important; }
  .flexC{
    display: flex;margin-bottom:15px ;
    &>span{ display:block; flex: 1;  }
  }
  .mar30{ margin-right:30px;  }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
}
</style>
<script>
export default {
  name: 'purchasePaymentFinanceApprovalDetail2',
  data () {
    return {
      loading: true,
      updatePay: false,
      detailsInfo:{},
      toggle: -1,
      balance: 0,
      selectFin: {
        'name': '',
        'balance': 0,
        'isPublic': 0,
        'bankName': '',
        'account': ''
      },
      nowApproveItem: {},
      isNowApprover: false,
      pund: 0,
      code: 0,
      busyid: '',
      details: {
        poPaymentApplication: {
          amount: ''
        },
        listMapBillCat: [],
        financeReturn: {},
        financeChequeDetail: {},
        financeAccount: {
          balance: ''
        },
        listMapFeeCat: [],
        srmSupplier: {},
        poOrdersPrepayment: {
          planAmount: ''
        },
        poOrders: {
          amount: ''
        },
        approvalProcessList: {},
        financePayment: {},
        financeReturnHistory: {}
      }
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    this.busyid = this.$route.params.busyid
    let parampun = Number(this.$route.params.pun)
    that.code = Number(paramCode.substring(0, 2))
    console.log('code:', that.code)
    that.invoiceID = paramCode.substring(2, paramCode.length)
    that.pund = Number(parampun)

    that.listenersUid = [
      that.sphdSocket.subscribe('getReimburseDetail', function (data) {
        that.loading = false
        let ReimburseDetail = JSON.parse(data)
        // 报销申请信息
        that.personnelReimburse = ReimburseDetail.personnelReimburse
        // that.param = ReimburseDetail
        // 审批列表
        that.approvalProcessList = ReimburseDetail.approvalProcessList
        that.nowApproveItem = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1]
        if (ReimburseDetail.approvalProcessList.length > 0) {
          let lastToUser = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].toUser
          if (!lastToUser || lastToUser === that.sphdSocket.user.userID) {
            that.isNowApprover = true
          }
        }
        // 付款方式信息
        that.payInfo = ReimburseDetail.personnelReimbursePayment
        // 账户信息
        that.selectFin = ReimburseDetail.financeAccount
      })
    ]
    that.getDetail()
  },
  methods: {
    getAlreadyPayDetails () {
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceAlready/${this.invoiceID}`
      })
    },
    getCheckDetails () {
      console.log('getCheckDetails ')
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceCheck/${this.invoiceID}`
      })
    },
    handleChange: function () {
      let that = this
      that.updatePay = !that.updatePay
    },
    approve: function () {
      let that = this
      if (that.updatePay) {
        let approvalProcessId = that.approvalProcessList[that.approvalProcessList.length - 1]['id']
        this.sphdSocket.send('reviewApproval', { userId: that.sphdSocket.user.userID, session: that.sphdSocket.sessionid, approvalProcessId: approvalProcessId })
      } else {
        that.$kiko_message('需要先勾选确认!')
      }
    },
    seeticket: function () {
      let that = this
      let item = {}
      item.pund = that.pund
      item = this.details.financeReturn
      if (this.details.financePayment.method === '4') {
        item.type = 3
      } else if (this.details.financePayment.method === '3') {
        item.type = 2
      }
      console.log(item)
      localStorage.setItem('invoiceTicketInfo', JSON.stringify(item))
      that.$router.push({
        path: `/purchasePaymentInvoiceInfo`
      })
    },
    jumpDetail: function (pun) {
      let that = this
      that.$router.push({
        path: `/purchaseInvoiceDetails3/34${that.invoiceID}/${pun}/${that.busyid}`
      })
    },
    getDetail: function () {
      let that = this
      if (that.pund === 0) {
        this.axios.post('../../../purchaseInvoice/getPurchaseInvoiceDetail.do', { 'applicationId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailsInfo = res.poPaymentApplication
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
          })
          .catch(function (error) {
            console.log(error)
          })
      } else if (that.pund === 3) {
        this.axios.post('../../../overpayment/getOverpaymentDetail.do', { 'loanBizId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailsInfo = res.loanBiz
            that.detailsInfo.balanceAmount = res.loanBiz.balance
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            console.log('pppppppppppp' + JSON.stringify(that.detailsInfo))
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do', { 'orderPrepaymentId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailsInfo = res.poOrdersPrepayment
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            let bills = res.listMapBillCat || []
            bills.forEach(function (item2) {
              if (item2.invoiceCategory === '1') {
                that.isSpecialBill = 1
              }
            })
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    jumpFu3: function () {
      let that = this
      if (Number(that.details.status) === 0 ) {
        that.$kiko_message('原来计划的付款方式没有修改过!')
        return false
      }
      this.$router.push({
        path: `/purchasePaymentFinanceFu3/${this.$route.params.id}/${this.$route.params.pun}/${this.busyid}`
      })
    }
  },
  computed: {
    isture: function () {
      let one = Number(this.detailsInfo.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  }
}
</script>
