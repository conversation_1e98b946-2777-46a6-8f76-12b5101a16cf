package cn.sphd.miners.modules.accountantReportTax.dto;

import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * */
public class AcctTaxDto implements Serializable {

    private Integer taxHisId;
    private Integer taxId;
    private Integer reportId;
    private String taxName;//名称
    private String createName;//编辑人名
    private Date createDate;//数据创建时间,应报的最后编辑时间
    private Date updateDate;//已报税的最后编辑时间
    private String updateName;//已报税的最后编辑人
    private Date payTime;//报税实际缴纳的日期
    private BigDecimal planAmount;//应缴金额
    private BigDecimal factAmount;//实缴金额
    private AcctTaxPeriodEntity periodEntity;
    private AcctReportHistoryEntity reportEntity;

    private String reportName;//报表名称
    private String latestDeclare;//最后编辑的日期
    private Date factReportDate;//实际申报的日期
    private String startPeriod;//申报的开始日期
    private String endPeriod;//申报的结束日期

    private Integer accountDetail;//财务明细账ID
    private Integer detailId;//t_accountant_tax_detail 表的 ID
    private String belongsBegin;//所属开始日期
    private String belongsEnd;//所属截止日期
    private String method;//0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账



//    public AcctTaxDto(Integer taxId,Integer taxHisId,String taxName,String reportName,String createName,Date createDate,Date factReportDate,BigDecimal planAmount,BigDecimal factAmount) {
    public AcctTaxDto(Integer taxId,Integer taxHisId,String taxName,String reportName,Integer detailId) {
        this.taxId = taxId;
        this.taxHisId = taxHisId;
        this.taxName = taxName;
        this.reportName = reportName;
//        this.createName = createName;
//        this.createDate = createDate;
//        this.factReportDate = factReportDate;
//        this.planAmount = planAmount;
//        this.factAmount = factAmount;
        this.detailId = detailId;
    }

    //tax.id,tax.name,r.name,r.latestDeclare
    public AcctTaxDto(Integer taxId,String taxName,String reportName,String latestDeclare) {
        this.taxId = taxId;
        this.taxName = taxName;
        this.reportName = reportName;
        this.latestDeclare = latestDeclare;
    }

    public AcctTaxDto(Integer taxHisId,Integer reportId,String taxName,Date updateDate,BigDecimal planAmount,BigDecimal factAmount) {
        this.taxHisId = taxHisId;
        this.reportId = reportId;
        this.taxName = taxName;
        this.updateDate = updateDate;
        this.planAmount = planAmount;
        this.factAmount = factAmount;
    }

    public AcctTaxDto(Integer taxHisId,Integer reportId,String taxName,Date updateDate) {
        this.taxHisId = taxHisId;
        this.reportId = reportId;
        this.taxName = taxName;
        this.updateDate = updateDate;
    }

    public AcctTaxDto(Integer taxHisId,Integer reportId,String taxName,Date updateDate,BigDecimal planAmount,BigDecimal factAmount,
                      AcctTaxPeriodEntity periodEntity,AcctReportHistoryEntity reportEntity) {
        this.taxHisId = taxHisId;
        this.reportId = reportId;
        this.taxName = taxName;
        this.updateDate = updateDate;
        this.planAmount = planAmount;
        this.factAmount = factAmount;
        this.periodEntity = periodEntity;
        this.reportEntity = reportEntity;
    }

    public AcctTaxDto(Integer taxHisId,Integer reportId,String taxName,Date updateDate,BigDecimal planAmount,BigDecimal factAmount,
                      AcctReportHistoryEntity reportEntity) {
        this.taxHisId = taxHisId;
        this.reportId = reportId;
        this.taxName = taxName;
        this.updateDate = updateDate;
        this.planAmount = planAmount;
        this.factAmount = factAmount;
        this.reportEntity = reportEntity;
    }

    public AcctTaxDto(Integer taxId,Integer reportId,String taxName) {
        this.taxId = taxId;
        this.reportId = reportId;
        this.taxName = taxName;
    }

    public AcctTaxDto() {}

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getBelongsBegin() {
        return belongsBegin;
    }

    public void setBelongsBegin(String belongsBegin) {
        this.belongsBegin = belongsBegin;
    }

    public String getBelongsEnd() {
        return belongsEnd;
    }

    public void setBelongsEnd(String belongsEnd) {
        this.belongsEnd = belongsEnd;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getTaxId() {
        return taxId;
    }

    public void setTaxId(Integer taxId) {
        this.taxId = taxId;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public String getLatestDeclare() {
        return latestDeclare;
    }

    public void setLatestDeclare(String latestDeclare) {
        this.latestDeclare = latestDeclare;
    }

    public Date getFactReportDate() {
        return factReportDate;
    }

    public void setFactReportDate(Date factReportDate) {
        this.factReportDate = factReportDate;
    }

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public Integer getTaxHisId() {
        return taxHisId;
    }

    public void setTaxHisId(Integer taxHisId) {
        this.taxHisId = taxHisId;
    }

    public Integer getReportId() {
        return reportId;
    }

    public void setReportId(Integer reportId) {
        this.reportId = reportId;
    }

    public String getTaxName() {
        return taxName;
    }

    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public BigDecimal getFactAmount() {
        return factAmount;
    }

    public void setFactAmount(BigDecimal factAmount) {
        this.factAmount = factAmount;
    }

    public AcctTaxPeriodEntity getPeriodEntity() {
        return periodEntity;
    }

    public void setPeriodEntity(AcctTaxPeriodEntity periodEntity) {
        this.periodEntity = periodEntity;
    }

    public AcctReportHistoryEntity getReportEntity() {
        return reportEntity;
    }

    public void setReportEntity(AcctReportHistoryEntity reportEntity) {
        this.reportEntity = reportEntity;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }
}
