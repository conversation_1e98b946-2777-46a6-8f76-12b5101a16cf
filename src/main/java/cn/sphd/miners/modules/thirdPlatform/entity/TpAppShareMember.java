package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table ( name ="t_tp_app_share_member" )
public class TpAppShareMember implements Serializable {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    /**
     * 共享ID
     */
    @Column(name = "share", nullable=true , unique=false )
    private Long share;

    /**
     * 用户ID
     */
    @Column(name = "member", nullable=true , unique=false )
    private Long member;

    /**
     * 创建人id
     */
    @Column(name = "creator", nullable=true , unique=false )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name", length=100, nullable=true , unique=false )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable=true , unique=false )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator", nullable=true , unique=false )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name", length=100, nullable=true , unique=false )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time", nullable=true , unique=false )
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShare() {
        return share;
    }

    public void setShare(Long share) {
        this.share = share;
    }

    public Long getMember() {
        return member;
    }

    public void setMember(Long member) {
        this.member = member;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
