<template>
	<!-- 关于 -->
	<div class="about">
    <Pageheader title="关于" :showBack="true"/>
    <div>
      <div class="live-cells">
        <div class="live-cell gap" v-for="(item, index) in list" :key="item.id" @click="goItem(item)">
          <span class="icon icon-privateArea"></span>{{ item.name }}
          <el-icon class="arrow-right"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
	</div>
</template>

<script setup>
import { getAboutModuleList } from "@/api/api.js";
import { reactive, ref, onMounted } from "vue";
import { Request } from "@/utils/request";
import { useRouter, useRoute } from "vue-router";

let list = ref([])
const router = useRouter();
const route = useRoute();

onMounted(() => {
  getModuleList()
  console.log('list', list)
})

const urlMap = [
  {name: '服务协议', url: 'aboutFinalDetail/userAgreement'},
  {name: '隐私条款', url: 'aboutFinalDetail/privacyPolicy'},
  {name: '版本说明', url: 'release'},
  {name: '使用帮助', url: 'usingHelp'}
]

const getModuleList = () => {
  getAboutModuleList().then(res => {
    let data = res.data.listFirstFolder
    let ajaxList = []
    for (let item of data) {
      ajaxList.push({
        id: item.id,
        name: item.name,
        url: urlMap.find(it => it.name === item.name).url
      })
    }
    console.log('ajaxList', ajaxList)
    list.value = [...[
      {id:0, name: '关于字幕精灵', url: 'aboutApp'},
      {id:0, name: '价格政策', url: 'pricePolicy'}], ...ajaxList, ...[{id:0, name: 'APP下载', url: 'appDownload'}]]
  })
}

const goItem = (item) => {
  if (item.id === 0) {
    router.push({ path: '/' + item.url});
  } else {
    router.push({ path: '/' + item.url + '/' + item.id });
  }
}
</script>

