
/* ===========投诉录入======================== */
input[readonly]{ background:#eee;  }
.yesPro, .noPro, .chargePro, .blueTr, .isHe{ display: none; }
.control{ margin:0 75px; }
.control span#divisionSettingBtn{ position: relative; top: 15px; left: 35px; }
.main>table{display:inline-block;  border:2px solid #999; padding:20px; margin:0 50px; }
.main>table>tbody>tr>td{ height: 40px; line-height: 40px; text-align: right; }
.main>table>tbody>tr>td select,.main td input,.main td textarea{ display: inline-block; border:1px solid #ccc; padding:0 5px; margin:0 10px;  line-height:25px; border-radius: 4px;}
.main>table>tbody>tr>td select,.main td input{ width:200px; height:30px; }
.main>table>tbody>tr>td textarea{ width:790px;height:60px; }
.main>table>tbody>tr>td>span{ vertical-align: top; }
.main>table>tbody>tr>td .bigttl{ font-weight: bolder; margin-right: 50px;}
.main>table>tbody>tr>td input.bigCon{ width:500px;}
.main>table>tbody>tr.blueTr div{ width:100%; margin:8px; border-top:1px solid #0b94ea;   }
.main>table>tbody>tr.blueTr td{ height:15px; }
.main>table>tbody>tr.center>td>div{ display: inline-block; width:49%; text-align: center;  }
.main>table>tbody>tr.center>td>div.fileCon{ position:relative; width: 100%;   }
.main>table>tbody>tr.center>td>div.fileCon>div{ width:47%; float: right; }
div.fileCon>div>div{ text-align:left; width:397px; min-height:80px; vertical-align: top;
    border:1px solid #ccc;border-radius:3px; background: #fff; display: inline-block; overflow-y: auto; max-height: 200px;  }
.xing, .red{ color: #e5303e;  }
#goodsList{ width: 780px; margin: 0 auto; }
#userList{ width:200px; text-align: center }
.delInfo{ display: none!important;  }
/* 重写弹框main的部分样式 */
#selectGoods .main>table>tbody>tr>td select,#selectGoods .main td input{ width:150px; height:30px; }
#selectGoods .main>table{display:inline-block;  border:none; padding:0; margin:0; }
#selectGoods .main>table>tbody>tr>td textarea{ width:800px;height:60px; }
table>tbody>tr>td.left{ text-align: left; }
.lit{ font-size:12px;  }
#timeCon>span{ box-shadow:0 0 3px #aaa;padding: 5px;border-radius: 2px;   }
#timeCon>span>i{ display: inline-block; margin-left:5px;   }
#timeCon>span:hover{ background:#eee;  }
.upBtn{ display: inline-block; }
.upItem{ display:inline-block;  width:60px; position: relative; overflow: hidden; text-align: center;
    box-shadow: 0 0 3px #ccc; padding: 5px 5px 0 5px; margin:5px;  }
.upItem>span.up_name{ height:18px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;  }
.upItem>span{ width: 50px; display: inline-block; }
.upItem>.up_delBtn{ position: absolute; right:2px; color: #e5303e;  }
.upItem>.up_delBtn:hover{ color: #9f212c;  }
.upItem .up_percent{ display: none; }
.main>table>tbody>tr.center>td .upItem>.up_name{ overflow: hidden; text-overflow:ellipsis; line-height: 15px;
    white-space:nowrap; font-size:12px;  }
.uploadify-progress { height: 8px; background-color: #ccc; border-radius:4px; border: 0px groove #666; vertical-align: middle;}
.uploadify-progress-bar { width: 0; height: 100%; border-radius:4px; background-color: #0099FF;}
.upItem>span.progressnum, .upItem>span.up_fileType{ display: none; }
.fileArr{text-align: left;}
.upItem>span.up_fileType .ty-fileType{ width:50px; height:67px; background-size:50px;   }
.uploadify-button { margin:0 10px; border: none; background-color: #e7e7e7; line-height: 24px; border-radius: 3px;
    padding: 3px 10px; font-size: 12px; font-weight: 400; color: #0b94ea; cursor: pointer;
    text-decoration: none; white-space: nowrap;
}
.fileScanMark{ display: none; position:absolute; width:100%; height:100%; background: rgba(100,100,100,0.6); left:0; top:0;      }
.upItem .fileScanMark>a.fileScan{ margin-top:15px; }
.upItem .fileScanMark>a{
    display:inline-block; background:#0b94ea; color:#fff; border-radius:4px;  cursor: default; padding:2px 5px; margin:5px 0 ;
}
.upItem .fileScanMark>a:hover{ background: #41b3fd;    }

/*==================== 投诉详情 ===========================*/
.apprpve, .apply, .type2,.type3,.type4,.type6, .yesPro, .noPro,
.chargeNo, .chargeEndApoNo, .endComplaint, .ctrl_li, .ctrl_applyEnd, .ctrl_End, .ctrl_chargeApplyEnd{ display: none; }
.centerCon{ width: 900px; margin:50px auto 100px; }
.centerCon td.left{ text-align: left;  }
.centerCon td.halfTdCon{ padding:0!important; }
.centerCon #title{ text-align: center; line-height:50px; font-size:20px;    }
.centerCon .controlArr{ margin-bottom: 5px;   }
.centerCon .ty-table td p{ margin-top:10px;    }
.centerCon .ty-table .ty-table td{ font-size:12px;    }
.centerCon .halfTd, #scanGoods .halfTd, #seeOral .halfTd{ width: 50%; float: left;   }
.centerCon .halfTd+.halfTd, .scanGoods .halfTd+.halfTd{ border-left: 1px solid #ccc;  }
.centerCon .halfTd>div, .scanGoods .halfTd>div{ padding:10px; margin:10px;min-height:50px; max-height: 300px; overflow-y: auto; }
#processorName{ font-weight: normal }
#scanContact .bonceCon, #editContactLog .bonceCon { max-height: 600px; overflow-y: auto;  }
#scanContact{ width:675px; }
.personItem{ border-radius:4px; margin:5px 0 5px 10px; border: 1px solid #ccc;
    padding:15px; width:300px; height:180px; display:inline-block; overflow: auto; }
.personItem i.fa{ font-size:20px; color: #48cfad; display: inline-block; margin-left:10px;   }
.personItem i.fa.fa-edit{ font-size: 24px; position: relative; top: 3px; }
.personItem i.fa:hover{ color: #2e856f;  }
#editContact{ width: 510px; }
#editContact .main table{ border:none; margin:0; padding:0; }
#editContact .main table input, #editContact .main table textarea{ width:355px; }
#editContactLog, #scanGoods{ width:820px; }
#changeHandlerLog{ width:625px;  }
.logItem p{ margin: 15px 0 0 0; color: #f58410 }
.logItem .right{ float: right; color:#aaa; }
#scanGoods pre{ width:760px; }
#scanGoods .bonceCon{ max-height:400px; overflow-y: auto;   }
#seeOral{ width:850px; }
#seeOral .bonceCon{ max-height:350px; overflow-y:auto;   }
.state2>p, .state3>p{ padding-left: 50px; }
#approveComplaint textarea, #approveComplaint select{ width: 315px; }
.bounce_Fixed2 {position: fixed;z-index: 10002;display: none; background: rgba(100, 100, 100, 0.5); width: 100%; padding-bottom: 200px; z-index: 10002;  }
.bounce_Fixed2{ z-index: 10002; }
#title2{ display: block; width:397px;  }
#addMore .bonceCon, #seeMore .bonceCon{ max-height:350px; overflow-y: auto;  }
#endComplaint .fa{ color:#0b94ea; }
#endComplaint p{ cursor: pointer; }
#settleOpinion, #endReason{ width: 100%;}
#more{ display: none; }

#chargeFloat, #more{ width:95%; margin-left: 2.5%; }
#title3{ height:60px; overflow-y: auto; border: 1px solid #ccc; width: 397px;  }
.seeMoreTip{ background:#dce5de; padding:10px; }
pre{ background:#fff; border:none;  }
#seeEndApply .brd{ border:1px solid #ccc; border-radius:4px; display: inline-block; margin-left:10px; padding:5px; width:360px; min-height:30px;   }
#seeEndApply .big{ height: 60px; overflow-y: auto;   }
#seeEndApply .verATop{ position: relative;   }






