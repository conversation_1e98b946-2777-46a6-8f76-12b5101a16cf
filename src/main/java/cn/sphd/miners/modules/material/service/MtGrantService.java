package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.material.entity.MtGrantRequisition;
import cn.sphd.miners.modules.material.entity.MtInApplication;
import cn.sphd.miners.modules.material.entity.MtOutApplication;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface MtGrantService extends BadgeNumberCallback {

    //根据用户id获取材料
    List<MtGrantRequisition> getListByUser(Integer userId);
    //获取材料树
    List<Map<String,Object>> getMtList(Integer oid,Integer userId);
    //获取材料树
    List<Map<String,Object>> getAllMtList(Integer oid);

    List<Map<String,Object>> getAllMtList(Integer oid,Integer userId);
    // 材料授权
    String submitMtGrant(String addIds,String deleteIds,Integer userID, User loginUser);
    //获取用户材料树
    List<Map<String,Object>> getUserMtListTree(Integer oid,Integer userId,String mtIds,Integer warehouseId);
    //获取用户材料
    List<Map<String,Object>> getUserMtList(Integer oid,Integer userId,String mtIds,Integer category,Integer warehouseId);
    //领料申请(库房选择)
    List<Map<String,Object>> selectWarehouse(String mtIds);
    //领料(库位选择)
    List<Map<String,Object>> selectLocation(Integer mtId);
    String pickingMtJudge(Integer mtId, BigDecimal num);
    //领料申请
    String pickingMtApply(User loginUser,String mtList,String purpose,String timePlan,Long durarionFact,Integer warehouseId) ;
    //获取申请单列表
    List<MtOutApplication> getMtOutApplicationList(Integer userId,Integer org,String approvalStatus);
    //获取其他领料计划
    List<MtOutApplication> getOtherPickingPlan(Integer org,Integer materialWarehouse);
    //领料单详情
    Map<String,Object> getPickingMtDetail(Integer mtOutId);
    //领料审批
    String pickingMtApproval(Integer itemId,String json,User loginUser);
    //领料查询
    List<MtOutApplication> selectPickingList(Integer userId,String userType,String approvalStatus,String beginTime,String endTime );
    //领料撤销
    String pickingMtRevoke(Integer id,User loginUser);
    //领料确认
    String pickingMtConfirm(Integer itemId,User loginUser);
    List<UserDto> getPickingUserList(Integer oid,String code);

    //获取领料记录
    Map<String,Object> getPickingRecordList(Integer oid,Integer currPage, Integer pageSize);
    //手动入库
    String manualWarehousing(MtInApplication mtInApplication, String mtList);
}
