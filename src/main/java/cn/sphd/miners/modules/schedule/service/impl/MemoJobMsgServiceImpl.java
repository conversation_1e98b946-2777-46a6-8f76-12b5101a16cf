package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.schedule.dao.MemoJobMsgDao;
import cn.sphd.miners.modules.schedule.entity.MemoJobMsg;
import cn.sphd.miners.modules.schedule.service.MemoJobMsgService;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2020/1/2.
 */
@Service("memoJobMsgService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoJobMsgServiceImpl implements MemoJobMsgService{
    @Autowired
    MemoJobMsgDao memoJobMsgDao;
    @Autowired
    UserDao userDao;
    @Autowired
    SWMessageService swMessageService;

    @Override
    public MemoJobMsg getMemoJobMsgById(Integer id) {
        return memoJobMsgDao.get(id);
    }

    @Override
    public MemoJobMsg saveMemoJobMsg(Integer superscript, String title, String content, String memo, Integer toUserId, Integer jobId, Integer scheduleId) {
        MemoJobMsg memoJobMsg=new MemoJobMsg();
        memoJobMsg.setCreateDate(new Date());
        memoJobMsg.setContent(content);
        memoJobMsg.setMemo(memo);
        memoJobMsg.setState("1");
        memoJobMsg.setUserId(toUserId);
        memoJobMsg.setSendTime(new Date());
        memoJobMsg.setJob(jobId);
        memoJobMsg.setSchedule(scheduleId);
        memoJobMsgDao.save(memoJobMsg);//新增

        User user=userDao.get(toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("memoJobMsg", memoJobMsg);
        hashMap.put("colHeadNum",superscript); // 大于 0 数据加 角标加， 小于0 数据减 角标减
        swMessageService.rejectSend(superscript,superscript,hashMap,toUserId.toString(),"memoJobMsg",title,content,user,"memoJobMsg");// 长连接推送
        return memoJobMsg;
    }

    @Override
    public void updateMemoJobMsg(MemoJobMsg memoJobMsg,String state, Integer toUserId) {
        if (memoJobMsg.getState().equals("1")) {
            memoJobMsg.setState(state);// 2- 已读  4-删除
            memoJobMsg.setUpdateDate(new Date());
            memoJobMsgDao.update(memoJobMsg);
            User user = userDao.get(toUserId);

            HashMap<String,Object> hashMap=new HashMap<>();
            hashMap.put("memoJobMsg", memoJobMsg);
            hashMap.put("colHeadNum",-1); // 大于 0 数据加 角标加， 小于0 数据减 角标减
            swMessageService.rejectSend(-1,-1,hashMap,toUserId.toString(),"memoJobMsg",null,null,user,"memoJobMsg");// 长连接推送
        }
    }



    @Override
    public List<MemoJobMsg> getMemoJobMsgByJobId(Integer jobId,String state) {
        String hql="from MemoJobMsg where job=:jobId";

        Map<String,Object> params=new HashMap<>();
        params.put("jobId",jobId);
        if (!StringUtil.isNullOrEmpty(state)){
            hql+=" and state=:state";
            params.put("state",state);
        }
        List<MemoJobMsg> memoJobMsgList=memoJobMsgDao.getListByHQLWithNamedParams(hql,params);
        return memoJobMsgList;
    }

    @Override
    public List<MemoJobMsg> getMemoJobMsgByScheduleId(Integer scheduleId,String state) {
        String hql="from MemoJobMsg where schedule=:scheduleId";

        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        if (!StringUtil.isNullOrEmpty(state)){
            hql+=" and state=:state";
            params.put("state",state);
        }
        List<MemoJobMsg> memoJobMsgList=memoJobMsgDao.getListByHQLWithNamedParams(hql,params);
        return memoJobMsgList;

    }

    @Override
    public List<MemoJobMsg> getMemoJobMsgBuUserId(User user, String state) {
        String hql="from MemoJobMsg where userId=:userId";

        Map<String,Object> params=new HashMap<>();
        params.put("userId",user.getUserID());

        if (!StringUtil.isNullOrEmpty(state)){
            hql+=" and state=:state";
            params.put("state",state);
        }
        List<MemoJobMsg> memoJobMsgList=memoJobMsgDao.getListByHQLWithNamedParams(hql,params);
        return memoJobMsgList;
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code) {
            case "memoJobMsg": //日程处理
                result = this.getMemoJobMsgBuUserId(user,"1").size();
        }
        return result;
    }

    @Override
    public void deleteAllMemoJobMsg(List<MemoJobMsg> memoJobMsgList) {
        memoJobMsgDao.deleteAll(memoJobMsgList);
    }
}
