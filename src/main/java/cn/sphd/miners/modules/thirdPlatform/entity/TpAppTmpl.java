package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022-12-21
 */

@Entity ( name ="TpAppTmpl" )
@Table ( name ="t_tp_app_tmpl" )
public class TpAppTmpl  implements Serializable {

    private static final long serialVersionUID =  4191380498261693533L;

    /**
     * ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 机构ID
     */
    @Column(name = "org" )
    private Integer org;

    /**
     * 小程序ID
     */
    @Column(name = "app" )
    private Integer app;

    /**
     * 第三方模板ID
     */
    @Column(name = "app_tmpl" )
    private Integer appTmpl;

    /**
     * 应用环境:1-开发,2-测试,3-生产
     */
    @Column(name = "app_evn" )
    private Integer appEvn;

    /**
     * 应用版本
     */
    @Column(name = "app_version" )
    private String appVersion;

    /**
     * 发布者ID
     */
    @Column(name = "publisher" )
    private Integer publisher;

    /**
     * 发布者ID
     */
    @Column(name = "publisher_name" )
    private String publisherName;

    /**
     * 发布时间
     */
    @Column(name = "publish_time" )
    private Date publishTime;

    /**
     * 备注
     */
    @Column(name = "memo" )
    private String memo;

    /**
     * 创建人id
     */
    @Column(name = "creator" )
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name" )
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time" )
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator" )
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name" )
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time" )
    private Date updateTime;

    /**
     * 修改前记录ID
     */
    @Column(name = "previous_id" )
    private Integer previousId;

    /**
     * 版本号,每次修改+1
     */
    @Column(name = "version_no" )
    private Integer versionNo;

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return this.org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getApp() {
        return this.app;
    }

    public void setApp(Integer app) {
        this.app = app;
    }

    public Integer getAppTmpl() {
        return this.appTmpl;
    }

    public void setAppTmpl(Integer appTmpl) {
        this.appTmpl = appTmpl;
    }

    public Integer getAppEvn() {
        return this.appEvn;
    }

    public void setAppEvn(Integer appEvn) {
        this.appEvn = appEvn;
    }

    public String getAppVersion() {
        return this.appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public Integer getPublisher() {
        return this.publisher;
    }

    public void setPublisher(Integer publisher) {
        this.publisher = publisher;
    }

    public String getPublisherName() {
        return this.publisherName;
    }

    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName;
    }

    public Date getPublishTime() {
        return this.publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getMemo() {
        return this.memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return this.creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return this.updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return this.updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getPreviousId() {
        return this.previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return this.versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

}
