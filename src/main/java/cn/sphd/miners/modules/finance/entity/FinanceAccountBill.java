package cn.sphd.miners.modules.finance.entity;

import cn.sphd.miners.modules.system.entity.Organization;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_finance_account_bill")
public class FinanceAccountBill implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;   //1-收入,2-支出

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;  //票面金额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;

    @Column(name="has_bill"   , nullable=true , unique=false)
    private Boolean hasBill;

    @Column(name="bill_cat"  , length=50 , nullable=true , unique=false)
    private String billCat;

    @Column(name="bill_quantity"   , nullable=true , unique=false)
    private Integer billQuantity;

    @Column(name="bill_period"  , length=1 , nullable=true , unique=false)
    private String billPeriod;

    @Column(name="bill_no"  , length=255 , nullable=true , unique=false)
    private String billNo;

    @Column(name="opposite_corp"  , length=100 , nullable=true , unique=false)
    private String oppositeCorp;  //收款单位

    @Column(name="opposite_bankno"  , length=100 , nullable=true , unique=false)
    private String oppositeBankno;  //对方单位(现金、银行转帐时使用)

    @Column(name="opposite_bankcode"  , length=100 , nullable=true , unique=false)
    private String oppositeBankcode; //对方银行帐号(银行转帐时使用)

    @Column(name="receive_account_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date receiveAccountDate;  //到帐日期(转帐，收到支票时使用)

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="operator_name"  , length=100 , nullable=true , unique=false)
    private String operatorName;//经手人

    @Column(name = "accountant_status")
    private String accountantStatus;  //会计数据状态  0-取现金 1-未选择  2-已选择  3-财务修改的时使用(修改后正确的数据添加的bill表数据)

    @Column(name="account_id"   , nullable=true , unique=false)
    private Integer myselfId;  //数据修改时，对应修改前支票所对应的bill表的数据(本表)

    @Column(name = "modity_status")
    private String modityStatus;  //数据修改状态  null/1-未修改  2-已修改(每条数据仅能修改一次)

    @Column(name = "business")
    private Integer business;//业务号

    @Column(name= "source",length = 1,nullable = true,unique = false)
    private String source;//数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
    // 5-票款处理(1.169采购的票款处理) 6-工资管理(1.206工资之支付) 7-预付款(1.229采购之预付款) 8-报销时多付出去的款---需收回的款(1.231差额处理)
    //9-多收来的款(1.233差额处理2) 10(A)-多收来的款-需收回的款(1.233差额处理)

    @Column(name= "state",length = 1,nullable = true,unique = false)
    private String state; //状态：0-未入账，1-重新入账，2-已入账

    @Column(name="occurrence_date"   , nullable=true , unique=false)
    private Date occurrenceDate;  //发生日期(借款付款录入中为付款日期)

    @Column(name = "previous_id")
    private Integer previousId;   //修改前记录ID

    @Column(name = "version_no")
    private Integer versionNo;   //版本号,每次修改+1

    @Column(name="business_date"   , nullable=true , unique=false)
    private Date businessDate;  //业务发生日期

    @Column(name= "business_type",nullable = true,unique = false)
    private String businessType;//业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款,0103-常规借款借出。0104-常规借款收款

    @Column(name = "business_history")
    private Integer businessHistory;//历史表中的业务号

    @Column(name = "cheque_history")
    private Integer chequeHistory;//历史支票明细ID

    @Column(name = "return_bill_history")
    private Integer returnBillHistory;//历史回款票据ID

    @Column(name="sub_type"  , length=1 , nullable=true , unique=false)
    private String subType;   //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出

    @Column(name="stakeholder_category"  , length=1 , nullable=true , unique=false)
    private String stakeholderCategory;   //类别:1-供应商,2-员工,3-自行录入

    @Column(name = "stakeholder")
    private Integer stakeholder;//干系人ID

    @Column(name = "stakeholders_history")
    private Integer stakeholdersHistory;//干系人历史ID

    @Column(name = "fact_user")
    private Integer factUser;//实际用户(同事userid)',

    @Column(name="bill_date"   , nullable=true , unique=false)
    private Date billDate;  //票据日期

    @Column(name="fact_date"   , nullable=true , unique=false)
    private Date factDate;  //实际(付款/支出)日期'

    @Column(name = "voucher")
    private Integer voucher;//凭证ID,为空时-未入会计

    @Column(name="is_account"   , nullable=true , unique=false)
    private Boolean isAccount;  //是否下账,false-不予下账,true-下帐

    @Column(name="is_modify"   , nullable=true , unique=false)
    private Boolean isModify;  //是否修改,false-未修改(0),true-已修改(1)

    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="cheque", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceChequeDetail cheque;

    @Column(name="cheque"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer cheque_;


    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="org", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private Organization org;

    @Column(name="org"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer org_;

    @JsonIgnore
    @OneToOne(fetch = FetchType.EAGER, cascade= CascadeType.REMOVE)
    @JoinColumn(name = "return_bill")
    private FinanceReturn financeReturn;

    @Column(name="return_bill"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer returnBill;

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceAccountBillHistory.class, fetch= FetchType.LAZY, mappedBy="billDetail", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceAccountBillHistory> financeAccountBillHistoryHashSet = new HashSet<FinanceAccountBillHistory>();

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=AccountDetail.class, fetch= FetchType.LAZY, mappedBy="billDetail", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<AccountDetail> accountDetailHashSet = new HashSet<AccountDetail>();

    @Transient
    private String method; //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账

    @Transient
    private String oppositeAccount;//账户名和账号

//    @Transient
//    private String accountBank;//账户名和账号

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Boolean getHasBill() {
        return hasBill;
    }

    public void setHasBill(Boolean hasBill) {
        this.hasBill = hasBill;
    }

    public String getBillCat() {
        return billCat;
    }

    public void setBillCat(String billCat) {
        this.billCat = billCat;
    }

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    public String getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(String billPeriod) {
        this.billPeriod = billPeriod;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getOppositeCorp() {
        return oppositeCorp;
    }

    public void setOppositeCorp(String oppositeCorp) {
        this.oppositeCorp = oppositeCorp;
    }

    public String getOppositeBankno() {
        return oppositeBankno;
    }

    public void setOppositeBankno(String oppositeBankno) {
        this.oppositeBankno = oppositeBankno;
    }

    public String getOppositeBankcode() {
        return oppositeBankcode;
    }

    public void setOppositeBankcode(String oppositeBankcode) {
        this.oppositeBankcode = oppositeBankcode;
    }

    public Date getReceiveAccountDate() {
        return receiveAccountDate;
    }

    public void setReceiveAccountDate(Date receiveAccountDate) {
        this.receiveAccountDate = receiveAccountDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Set<AccountDetail> getAccountDetailHashSet() {
        return accountDetailHashSet;
    }

    public void setAccountDetailHashSet(Set<AccountDetail> accountDetailHashSet) {
        this.accountDetailHashSet = accountDetailHashSet;
    }

    public FinanceChequeDetail getCheque() {
        return cheque;
    }

    public void setCheque(FinanceChequeDetail cheque) {
        this.cheque = cheque;
    }

    public Integer getCheque_() {
        return cheque_;
    }

    public void setCheque_(Integer cheque_) {
        this.cheque_ = cheque_;
    }

    public FinanceReturn getFinanceReturn() {
        return financeReturn;
    }

    public void setFinanceReturn(FinanceReturn financeReturn) {
        this.financeReturn = financeReturn;
    }

    public Set<FinanceAccountBillHistory> getFinanceAccountBillHistoryHashSet() {
        return financeAccountBillHistoryHashSet;
    }

    public void setFinanceAccountBillHistoryHashSet(Set<FinanceAccountBillHistory> financeAccountBillHistoryHashSet) {
        this.financeAccountBillHistoryHashSet = financeAccountBillHistoryHashSet;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Organization getOrg() {
        return org;
    }

    public void setOrg(Organization org) {
        this.org = org;
    }

    public Integer getOrg_() {
        return org_;
    }

    public void setOrg_(Integer org_) {
        this.org_ = org_;
    }

    public String getAccountantStatus() {
        return accountantStatus;
    }

    public void setAccountantStatus(String accountantStatus) {
        this.accountantStatus = accountantStatus;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getOppositeAccount() {
        return oppositeAccount;
    }

    public void setOppositeAccount(String oppositeAccount) {
        this.oppositeAccount = oppositeAccount;
    }

    public Integer getMyselfId() {
        return myselfId;
    }

    public void setMyselfId(Integer myselfId) {
        this.myselfId = myselfId;
    }

    public String getModityStatus() {
        return modityStatus;
    }

    public void setModityStatus(String modityStatus) {
        this.modityStatus = modityStatus;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getOccurrenceDate() {
        return occurrenceDate;
    }

    public void setOccurrenceDate(Date occurrenceDate) {
        this.occurrenceDate = occurrenceDate;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getBusinessHistory() {
        return businessHistory;
    }

    public void setBusinessHistory(Integer businessHistory) {
        this.businessHistory = businessHistory;
    }

    public Integer getChequeHistory() {
        return chequeHistory;
    }

    public void setChequeHistory(Integer chequeHistory) {
        this.chequeHistory = chequeHistory;
    }

    public Integer getReturnBillHistory() {
        return returnBillHistory;
    }

    public void setReturnBillHistory(Integer returnBillHistory) {
        this.returnBillHistory = returnBillHistory;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getStakeholderCategory() {
        return stakeholderCategory;
    }

    public void setStakeholderCategory(String stakeholderCategory) {
        this.stakeholderCategory = stakeholderCategory;
    }

    public Integer getStakeholder() {
        return stakeholder;
    }

    public void setStakeholder(Integer stakeholder) {
        this.stakeholder = stakeholder;
    }

    public Integer getStakeholdersHistory() {
        return stakeholdersHistory;
    }

    public void setStakeholdersHistory(Integer stakeholdersHistory) {
        this.stakeholdersHistory = stakeholdersHistory;
    }

    public Integer getFactUser() {
        return factUser;
    }

    public void setFactUser(Integer factUser) {
        this.factUser = factUser;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public Date getFactDate() {
        return factDate;
    }

    public void setFactDate(Date factDate) {
        this.factDate = factDate;
    }

    public Integer getVoucher() {
        return voucher;
    }

    public void setVoucher(Integer voucher) {
        this.voucher = voucher;
    }

    public Boolean getAccount() {
        return isAccount;
    }

    public void setAccount(Boolean account) {
        isAccount = account;
    }

    public Boolean getModify() {
        return isModify;
    }

    public void setModify(Boolean modify) {
        isModify = modify;
    }

    public Integer getReturnBill() {
        return returnBill;
    }

    public void setReturnBill(Integer returnBill) {
        this.returnBill = returnBill;
    }
}
