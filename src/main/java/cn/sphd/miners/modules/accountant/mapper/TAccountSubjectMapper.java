package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.modules.accountant.entity.SubjectDefEntity;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.entity.TaccountantSubjectChangeDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public interface TAccountSubjectMapper extends BaseMapper<TAccountantSubjectEntity> {

    List<TAccountantSubjectEntity> listByAccountSubject(HashMap param);

    List<TAccountantSubjectEntity> listBySubjects(HashMap param);

//    List<TAccountantSubjectEntity> listByFirstSubject(TAccountantSubjectEntity s);
    TAccountantSubjectEntity listByFirstSubject(TAccountantSubjectEntity s);

    List<TAccountantSubjectEntity> listAllSubject(TAccountantSubjectEntity s);

    //根据编号获取这个科目
    TAccountantSubjectEntity subjectAllMessage(TAccountantSubjectEntity s);

    //获取科目表中所有的科目（不区分机构）
    List<TAccountantSubjectEntity> listAll();

    String newSubject(HashMap param);

    //月初初始化科目表
    int updateBehiningMonth(TAccountantSubjectEntity s);

   int upadteParentSubject(TAccountantSubjectEntity os);

    int insertSubject(TAccountantSubjectEntity s);

    int updateSubject(TAccountantSubjectEntity s);

    List<TAccountantSubjectEntity> childSubject(TAccountantSubjectEntity s);

    int updateStateForSubject(TAccountantSubjectEntity s);

    /*结帐后需要把一些数据写回到科目表作为下次结帐时使用*/
    int updateOrgSubject(TAccountantSubjectEntity s);

    List<TAccountantSubjectEntity> lastSubject(TAccountantSubjectEntity s);

    List<TAccountantSubjectEntity> listProfitAndLossSubject(Integer oid);

    int updateBeginning();

    int updateUseable(TAccountantSubjectEntity paramSubject);

    int deleteSubject(TAccountantSubjectEntity paramSubject);

    //新增初始化的科目
    int addInitializationSubject(Integer org);

    TAccountantSubjectEntity getByOrgAndSubject(TAccountantSubjectEntity subjectEntity);

    List<TAccountantSubjectEntity> getSubjects(TAccountantSubjectEntity param);

    List<TAccountantSubjectEntity> getSpecifySubjectInfo(int oid);

    TAccountantSubjectEntity checkNameDuplicate(TAccountantSubjectEntity param);

    int reduceChild(TAccountantSubjectEntity parentSubject);

    /*
    * @param subject 父节点编号 name:名字
    * <AUTHOR>
    * */
    @Select("select * from t_accountant_subject o where o.parent=#{subject} and o.name=#{name} and o.relevance_item=#{id}")
    List<TAccountantSubjectEntity> getRepeatSubjects(@Param("subject") String subject,@Param("name") String name,@Param("id") Integer id);

    int financeRelevanceModify(TAccountantSubjectEntity subjectEntity);

    TAccountantSubjectEntity getFinanceRelevanceSubject(TAccountantSubjectEntity financeSubject);

    TAccountantSubjectEntity getByPhone(TAccountantSubjectEntity subjectEntity);

    TAccountantSubjectEntity getByName(TAccountantSubjectEntity subjectEntity);

    void updateEstablishId(Map<String, Object> initMonth);

    int getSubjectCount(TAccountantSubjectEntity subjectEntityParam);

    List<TAccountantSubjectEntity> getSubjectAndChildren(TAccountantSubjectEntity param);
}