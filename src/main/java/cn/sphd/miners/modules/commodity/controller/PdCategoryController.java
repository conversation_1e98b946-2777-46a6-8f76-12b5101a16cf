package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.entity.PdCategory;
import cn.sphd.miners.modules.commodity.service.PdCategoryService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/category")
public class PdCategoryController {

    @Autowired
    PdCategoryService pdCategoryService;


    /**
     * 一级类别列表接口
     * 1.346 商品之分类  202506
     * lixu
     */
    @ResponseBody
    @RequestMapping("/getFirstCategoryListByOid.do")
    public JsonResult getFirstCategoryListByOid(User user, PageInfo pageInfo){
        List<PdCategory> pdCategoryList=pdCategoryService.getFirstCategoryListByOid(user.getOid(),pageInfo);
        Map<String,Object> map=new HashMap<>();
        map.put("pageInfo",pageInfo);
        map.put("pdCategoryList",pdCategoryList);
        return new JsonResult(1,map);
    }

    /**
     * 管理下一级类别列表接口（待分类不能）
     * 1.346 商品之分类  202506
     * lixu
     */
    @ResponseBody
    @RequestMapping("/getSonCategoryListByPid.do")
    public JsonResult getSonCategoryListByPid(User user,Integer id,PageInfo pageInfo){
        List<PdCategory> pdCategoryList=pdCategoryService.getSonCategoryListByPid(id,pageInfo);
        PdCategory pdCategory=pdCategoryService.getPdCategoryById(id);
        if (pdCategoryList.size()<=0&&pdCategory.getName().equals("待分类")){
            return new JsonResult(new MyException("操作失败！\n 此类别下没有下一级类别！"));
        }
        Map<String,Object> map=new HashMap<>();
        map.put("pageInfo",pageInfo);
        map.put("pdCategoryList",pdCategoryList);
        map.put("categoryInfo",pdCategory);
        return new JsonResult(1,map);
    }

    /**
     * 新增类别  类别从属列表
     * 1.346 商品之分类  202506
     * lixu
     */
    @ResponseBody
    @RequestMapping("/getPathCategoryListById.do")
    public JsonResult getPathCategoryListById(User user,Integer id){
        List<PdCategory> pdCategoryList=pdCategoryService.getPathCategoryListById(id);
        return new JsonResult(1,pdCategoryList);
    }

    /**
     * 新增类别  保存接口   同级不能同名
     * 1.346 商品之分类  202506
     * lixu
     */
    @ResponseBody
    @RequestMapping("/addPdCategory.do")
    public JsonResult addPdCategory(User user,Integer id,String name){
        PdCategory pdCategory=pdCategoryService.getPdCategoryByName(id,name);
        if (pdCategory==null){
            pdCategoryService.addPdCategory(user,id,name);
            return new JsonResult(1,"操作成功！");
        }
        return new JsonResult(new MyException("这个类别已经有啦！"));
    }

    /**
     * 修改类别  保存接口   同级不能同名
     * 1.346 商品之分类  202506
     * lixu
     */
    @ResponseBody
    @RequestMapping("/editPdCategory.do")
    public JsonResult editPdCategory(User user,Integer id,String name){
        return pdCategoryService.updatePdCategory(user,id,name);
    }

    /**
     * 删除类别    待分类不能删除
     * 1.346 商品之分类  202506
     * lixu
     */
    @ResponseBody
    @RequestMapping("/deletePdCategory.do")
    public JsonResult deletePdCategory(User user,Integer id){
        return pdCategoryService.deletePdCategory(id);
    }

}
