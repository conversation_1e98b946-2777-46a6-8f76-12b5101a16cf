package cn.sphd.miners.modules.material.entity;


import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;


@Entity (name="MtStorage")
@Table (name="t_mt_storage")
//物料_库位表
public class MtStorage implements Serializable {
    private static final long serialVersionUID = 1L;
	
    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org; 

    @Column(name="sn"   , nullable=true , unique=false)
    private Integer sn; 

    @Column(name="name"  , length=100 , nullable=false , unique=false)
    private String name; 

    @Column(name="full_name"  , length=255 , nullable=true , unique=false)
    private String fullName; 

    @Column(name="volume"   , nullable=true , unique=false)
    private Double volume;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo; 

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;



    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    

    public Integer getOrg() {
        return org;
    }
	
    public void setOrg (Integer org) {
        this.org =  org;
    }

    public Integer getSn() {
        return sn;
    }
	
    public void setSn (Integer sn) {
        this.sn =  sn;
    }
	

    public String getName() {
        return name;
    }
	
    public void setName (String name) {
        this.name =  name;
    }

    public String getFullName() {
        return fullName;
    }
	
    public void setFullName (String fullName) {
        this.fullName =  fullName;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public String getMemo() {
        return memo;
    }
	
    public void setMemo (String memo) {
        this.memo =  memo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public Date getUpdateTime() {
        return updateTime;
    }



    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
