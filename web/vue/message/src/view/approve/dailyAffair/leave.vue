<template>
  <div id="overTimeApply">
    <TY_NavTop title="请假审批" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="approvalHandleCount">
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, '1')" v-for="(item, index) in approvalHandle" v-bind:key="index">
            <div class="ui-cell__hd">
              <span class="leave_type">{{item.createName}}</span>
            </div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con_s">
                <div>{{item.leaveTypeName}}</div>
                <div>开始时间：{{item.beginTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                <div>结束时间：{{item.endTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.actualState === '1'">提</div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, '2')" v-for="(item, index) in approvalApproval" v-bind:key="index">
            <div class="ui-cell__hd">
              <span class="leave_type">{{item.createName}}</span>
            </div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con_s">
                <div>{{item.leaveTypeName}}</div>
                <div>开始时间：{{item.beginTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
                <div>结束时间：{{item.endTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.actualState === '1'">提</div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>

export default {
  name: 'approveLeave',
  data () {
    return {
      tabValue: '1',
      approvalHandle: [],
      approvalApproval: [],
      listenersUid: []
    }
  },
  computed: {
    approvalHandleCount: function () {
      return this.approvalHandle.length
    }
  },
  created () {
    this.tabValue = this.$store.getters.getLeaveNav
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('approvalLeaveHandle', function (data) {
        that.approvalHandle = JSON.parse(data)
        console.log('approvalLeaveHandle session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('approvalLeaveHandle', function (data) {
        let getData = JSON.parse(data)
        let personnelLeave = getData.personnelLeave
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.approvalHandle.unshift(personnelLeave)
        } else if (operate < 0) {
          let leaveId = personnelLeave.id
          let _index = -1
          console.log(that.approvalHandle)
          that.approvalHandle.forEach(function (item, index) {
            if (item.id === leaveId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.approvalHandle.splice(_index, 1)
          }
          console.log(that.approvalHandle)
        }
        console.log('approvalLeaveHandle user Socket received OK:' + data)
      }, function () {
        console.log('approvalLeaveHandle user Socket Error:')
      }, 'user'),
      this.sphdSocket.subscribe('approvalLeaveApproval', function (data) {
        that.approvalApproval = JSON.parse(data)
        console.log('approvalLeaveApproval session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('approvalLeaveApproval', function (data) {
        let getData = JSON.parse(data)
        let personnelLeave = getData.personnelLeave
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.approvalApproval.unshift(personnelLeave)
        } else if (operate < 0) {
          let leaveId = personnelLeave.id
          let _index = -1
          console.log(that.approvalApproval)
          that.approvalApproval.forEach(function (item, index) {
            if (item.id === leaveId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.approvalApproval.splice(_index, 1)
          }
          console.log(that.approvalApproval)
        }
        console.log('approvalLeaveApproval user Socket received OK:' + data)
      }, function () {
        console.log('approvalLeaveApproval user Socket Error:')
      }, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('approvalLeave', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setLeaveNav', tab)
      this.$router.push({
        path: `/approvalLeaveDetail/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/approvalLeaveQuery`
      })
    }
  },
  watch: {
    tabValue: function () {
      console.log('1001000111--=========:' + this.tabValue)
    }
  }
}
</script>
