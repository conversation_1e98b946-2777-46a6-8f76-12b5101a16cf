<template id="base">
  <div class="base">
    <div class="panel"><span class="panelTitle">正在加载……</span></div>
  </div>
</template>
<script>
// wyu：引相关的库
import JsEncrypt from 'jsencrypt/bin/jsencrypt'
import sha1 from 'js-sha1'
export default {
  data () {
    return {
      serverTime: ''
    }
  },
  created () {
    this.$toast.loading({
      message: 'Loading...',
      forbidClick: true,
      loadingType: 'spinner'
    })
    if (typeof (this.$route.params.item) === 'undefined') {
      console.log('二维码错误，没有参数！')
      this.jumpError('二维码错误，没有参数！')
    }
    this.$http({}).then((response) => { // wyu: 获取服务器时间
      this.serverTime = new Date(response.headers.get('Date'))
      this.checkParams(this.serverTime)
    }, (response) => {
      console.log(response)
      console.log('服务器无法访问！')
      this.jumpError('服务器无法访问！')
    })
  },
  methods: {
    jumpError (message) {
      this.$toast.clear()
      this.$router.replace({name: 'error', params: {message: message}})
    },
    checkParams (now) {
      try {
        let data = this.decrypt(decodeURIComponent(this.$route.params.item))
        let items = JSON.parse(data)
        let salt = sha1(items.id + items.userId + items.oid + items.expire_date + 'bC1]gB5@')
        if (salt !== items.salt) {
          console.log('二维码错误，参数不正确！')
          this.jumpError('二维码错误，参数不正确！')
        } else {
          this.checkCache(items,now >= new Date(items.expire_date))
        }
      } catch(e) {
        console.log('二维码错误，参数错误！', e)
        this.jumpError('二维码错误，参数错误！')
      }
    },
    checkCache(items,expired) {
      let wonderssSurveyObject = window.localStorage.getItem('wonderssSurveyObject')
      if (typeof wonderssSurveyObject === 'string' && wonderssSurveyObject.length > 40) {
        let shaVal = wonderssSurveyObject.substr(0, 40)
        let data = wonderssSurveyObject.substr(40)
        try {
          let keys = JSON.parse(this.decrypt(decodeURIComponent(data)))
          let salt = sha1(keys.objective.toString() + keys.answerTime + keys.hashKey + 'CPs%1J:3')
          if (salt === shaVal && keys.id === items.id && keys.org === items.oid) {
            this.jumpNext(items.id, items.userId, items.oid, keys.hashKey, keys.objective, keys.answerTime)
            return
          }
        } catch (e) {
          console.log('数据错误，缓存错误！', e)
        }
      }
      if (expired) {
        console.log('二维码已过期！')
        this.jumpError('二维码已过期！')
      } else {
        this.jumpNext(items.id, items.userId, items.oid, null)
      }
    },
    jumpNext (id, userId, oid, hashKey, objective, answerTime) {
      // wyu：设置系统路径
      if (window.location.href.indexOf('/vue/') > 0) {
        if (typeof window.parent.$ === 'undefined') {
          window.parent.$ = {}
        }
        window.parent.$.webRoot = window.location.href.substring(0, window.location.href.indexOf('/vue/'))
        this.$http.get(window.parent.$.webRoot + '/uploads/getRootPath.do', {params: {userId: userId}}).then((response) => {
          window.parent.$.webRoot = response.data.webRoot
          window.parent.$.fileUrl = response.data.fileUrl
          window.parent.$.uploadUrl = response.data.uploadUrl
          window.parent.$.ow365url = response.data.ow365url
          let wonderssSurveyKey = window.localStorage.getItem('wonderssSurveyKey')
          if (typeof wonderssSurveyKey !== 'string' || wonderssSurveyKey.length !== 32) {
            wonderssSurveyKey = this.uuid()
            window.localStorage.setItem('wonderssSurveyKey', wonderssSurveyKey)
          } else if(hashKey !== wonderssSurveyKey) {
            hashKey = null
          }
          // wyu：注释主干相关更改，等合并主干后可以恢复
          if (hashKey===null) {
            this.$router.replace({name: 'home', params: {id: id, userId: userId, oid: oid, hashKey: wonderssSurveyKey}})
        } else {
          this.$router.replace({name: 'answer', params: {id: id, userId: userId, oid: oid, hashKey: hashKey, objective: objective, answerTime: answerTime, serverTime: this.serverTime}})
        }
        }, (response) => {
          console.log('请求失败！', response)
          this.jumpError('服务器请求失败！')
        })
      } else {
        this.jumpError('服务器路径错误！')
      }
    },
    uuid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      }).replace(/-/g,'')
    },
    decrypt (data) {
      let jse = new JsEncrypt()
      jse.setPrivateKey(`**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)
      return jse.decrypt(data)
    },
    encrypt (data) {
      let jse = new JsEncrypt()
      jse.setPublicKey(`-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAvySpSYm/34kJikFY7MqL
/sGWSbhUvKug94zeAQzvMEvJSkqRLHL1mYt1kluCD0xyqO/2opIiODuzZuskZokB
/VRyAkL8G5Ntgsv48cxvnpnL2fP+TP+PLdZYan7p+Ygs/aKdm01mT94L26kex41M
lkvghzncBfHtYhTH5lWhtmXi7mHPpUKrFVyDjPA3JrTJzZzeQvAdrT8UflHf2/Ea
hQo4EDwZczIiVYVosjiWKBoX4QKPXAJhIVt88Y39/mDaLYeOkDNE/5/jNrGZm3Pl
Wb/xB+mWRf1cH4UZl3qIqI/a9eyhjMFpX5FP3DB97TVg1s49AzLL0exBFjd2jK+Z
LLFShpLVpiN+tJAMJT3t5cvh5Ctf5kuLPp4Bh8+YD+GnuSmfdvK+cpt25atRkdbn
S76KY3u17VdKxRi2kklOl2T7HZQzAK9FPg9e0v99DHYN/4t1R33pm+u5cANF8uAl
INKpipDP2+eDKiYbasGeSe9henPyO1rcu9JFHF0fbZJuup8nhMZLpvmj5B3jHhBs
j36pdwwFGPAbxsv1E7FIaIBb0mf1+xyFWIJnkDOf7UIeDDKHqYEe/2u82D3I1xV8
wWdml7K33E7fpqTi8ShWGV89STAnUTLO1N4yy7A4p90jobYmHte1Amoy1HbvsQJg
x0o+z7lqYGkzb/yE+a67xf8CAwEAAQ==
-----END PUBLIC KEY-----`)
      return jse.encrypt(data)
    }
  }
}
</script>
