<!--
demo子组件
----desc：该子组件不是全局子组件，此处只为说明子组件的开发规范
----pages（用到的页面）：['views/demoUpload.vue']
-->
<template>
 <div class="TyTab">
   <div class="ttl">
     <span v-for="(item, index) in namelist"><span class="tab" :class="{tabIndex:tabIndex==index}" v-html="item" @click="changeTabIndex(index)"></span><span v-if="index < namelist.length-1"> | </span></span>
   </div>
 </div>
</template>
<script>

export default {
  name: "TyTab",
  data() {
    return {
      tabIndex: 0
    };
  },
  props:{
    namelist: {type: Array, default:[]},
    changeTabFun: {type: Function, default: (tableIndex) => {}}
  },
  methods: {
    changeTabIndex(index) {
      if(this.tabIndex != index) {
        this.tabIndex = index
        this.changeTabFun(index)
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.TyTab{
  .ttl{
    background: linear-gradient(to right, #ff7e5f, #feb47b);
    color: #fff;
    padding: 0 20px;
    line-height: 50px;
    margin-bottom: 10px;
    font-weight: bold;
    cursor: pointer;
    .tab {
      color: #bbb;
    }
    .tabIndex {
      color: #fff;
    }
  }
}
</style>