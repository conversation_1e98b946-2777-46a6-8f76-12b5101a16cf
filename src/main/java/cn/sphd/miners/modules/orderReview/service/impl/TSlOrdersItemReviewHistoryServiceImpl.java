package cn.sphd.miners.modules.orderReview.service.impl;

import java.util.List;

import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReviewHistory;
import cn.sphd.miners.modules.orderReview.mapper.TSlOrdersItemReviewHistoryMapper;
import cn.sphd.miners.modules.orderReview.service.ITSlOrdersItemReviewHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
@Transactional
public class TSlOrdersItemReviewHistoryServiceImpl implements ITSlOrdersItemReviewHistoryService
{
    @Autowired
    private TSlOrdersItemReviewHistoryMapper tSlOrdersItemReviewHistoryMapper;

    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TSlOrdersItemReviewHistory selectTSlOrdersItemReviewHistoryById(Long id)
    {
        return tSlOrdersItemReviewHistoryMapper.selectTSlOrdersItemReviewHistoryById(id);
    }

    /**
     * 查询列表
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 
     */
    @Override
    public List<TSlOrdersItemReviewHistory> selectTSlOrdersItemReviewHistoryList(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory)
    {
        return tSlOrdersItemReviewHistoryMapper.selectTSlOrdersItemReviewHistoryList(tSlOrdersItemReviewHistory);
    }

    /**
     * 新增
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 结果
     */
    @Override
    public int insertTSlOrdersItemReviewHistory(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory)
    {
        return tSlOrdersItemReviewHistoryMapper.insertTSlOrdersItemReviewHistory(tSlOrdersItemReviewHistory);
    }

    /**
     * 修改
     * 
     * @param tSlOrdersItemReviewHistory 
     * @return 结果
     */
    @Override
    public int updateTSlOrdersItemReviewHistory(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory)
    {
        return tSlOrdersItemReviewHistoryMapper.updateTSlOrdersItemReviewHistory(tSlOrdersItemReviewHistory);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTSlOrdersItemReviewHistoryByIds(Long[] ids)
    {
        return tSlOrdersItemReviewHistoryMapper.deleteTSlOrdersItemReviewHistoryByIds(ids);
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTSlOrdersItemReviewHistoryById(Long id)
    {
        return tSlOrdersItemReviewHistoryMapper.deleteTSlOrdersItemReviewHistoryById(id);
    }
}
