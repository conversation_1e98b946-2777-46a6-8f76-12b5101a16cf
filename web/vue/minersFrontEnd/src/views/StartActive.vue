<template>
  <div class="StartActive">
    <el-container >
      <el-aside width="185px" >
        <el-container>
          <el-header class="commonHeaderBg">
            <span class="logo">
              <span>W</span>onderss
            </span>
            <span class="menuBtn fa fa-navicon"></span>
          </el-header>
          <el-main class="menuBg" >
            <div class="menuBgCon" :style="{ height:menuHeight + 'px', }">
              <div class="navList">
              </div>
            </div>
          </el-main>
        </el-container>
      </el-aside>
      <el-container style="background-color: #fff;">
        <el-header class="commonHeaderBg">
          <div><div class="hRight"></div><div class="hLeft"></div></div>
        </el-header>
        <el-main class="mainall" :style="{ height:homePageHeight + 'px', }">
          <div class="activeCon" v-if="quesType === 'default'">
            <div v-if="step === 1">
              <div class="activeTip">
                您已有89天以上没有登录系统了！<br>
                为了您数据的安全，本次登录需要您回答安全问题。
              </div>
              <div class="line30">
                <div>
                  安全问题1 <br>
                  您曾在以下哪个机构中工作过？请选择，之后点击下一步。
                </div>
                <div class="ty-color-blue">
                  注：本问题共有三次回答机会，如第三次还是答不对，则您私人领地内的数据将无法再访问！
                  故如无把握，请向该机构总务人员或您的同事寻求协助！
                </div>
                <div class="flexCon">
                  <div class="flitem" v-for="(ques , index1) in ques1" :key="index1">
                    <el-radio v-model="ans1" :label="ques">{{ ques }}</el-radio>
                  </div>
                  <div style="clear: both" class="ty-clear"></div>
                </div>
              </div>
              <div class="contrlCon">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marRight20" @click="nextBtn">下一步</span>
                <span class=""></span>
              </div>
            </div>
            <div v-if="step === 2">
              <div class="line30">
                安全问题2 <br>
                您曾在 {{ ans1 }} 服务过。请在下列各个空格内输入该机构您同事的姓名，之后点击确定。
                <div class="ty-color-blue" style="line-height: 20px; margin:10px;">
                  注：本问题共有三次回答机会，如第三次还是答不对，则您私人领地内的数据将无法再访问！<br>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您录入的几个姓名必须与相应同事在系统内的姓名完全一致方算作正确。故如无把握，请向该机构总务人员或您的同事寻求协助！
                </div>
                <div style="line-height: 40px">
                  <el-input class="inputA" v-model="staffName1" placeholder="请输入"></el-input>
                  <br>
                  <el-input class="inputA" v-model="staffName2" placeholder="请输入"></el-input>
                  <br>
                  <el-input v-if="orgStaffNumber>2" class="inputA" v-model="staffName3" placeholder="请输入"></el-input>
                </div>
                <div class="contrlCon">
                  <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5 marRight20" @click="outBtn">退出</span>
                  <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marRight20" @click="okBtn">确定</span>
                  <span class=""></span>
                </div>
              </div>
            </div>
          </div>
          <div class="activeCon" v-if="quesType === 'ownSecurity'">
            <div>
              <div class="activeTip">
                您已有89天以上没有登录系统了！<br>
                为了您数据的安全，本次登录需要您回答安全问题。
              </div>
              <div class="line30">
                <div v-for="(ownQues, index2) in ownQuesList" :key="index2" class="quesItem">
                  安全问题{{ ownQues.orders + 1 }}<br>
                  {{ ownQues.question }}<br>
                  <el-input class="inputA" v-model="ownQues.ans" placeholder="请输入内容"></el-input>
                </div>
              </div>
              <div class="contrlCon">
                <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5 marRight20" @click="outBtn">退出</span>
                <span class="ty-btn ty-btn-big ty-btn-gray ty-circle-5 marRight20" :class="okOrNot ? 'ty-btn-blue' : 'ty-btn-gray'" @click="okBtn2">确定</span>
                <span class=""></span>
              </div>
            </div>
          </div>

        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script lang="js">
import { ownSecurityQuestion, defaultQuestionBankOne, verifyQuestionBankOne, answerSecurityQuestion,
  verifyQuestionBankTwo, unlockAcc } from "@/api/api";

export default {
  name:'StartActive',
  data() {
    return {
      menuHeight:window.innerHeight-60,
      homePageHeight:window.innerHeight-100,
      ques1:[],
      phone: localStorage.getItem('activePhone'),
      ans1:'',
      quesType:'ownSecurity',
      step:1,
      staffName1:'',
      staffName2:'',
      staffName3:'',
      orgStaffNumber:'',
      ownQuesList:[],
    }
  },
  components: {
  },
  created() {
    this.mainMountedFun()
    this.getQuestion()
  },
  computed: {
    okOrNot() {
      let boolVal = true
      if(this.ownQuesList.length > 0){
        this.ownQuesList.forEach(ques =>{
          if(ques.ans){
          }else{
            boolVal = false
          }
        })
      }else {
        boolVal = false
      }
      return boolVal
    }
  },
  methods: {
    outBtn(){
      this.$router.replace('Start')
    },
    unlockAccFun(){
      unlockAcc().then(res1=>{
        let res = res1.data
        let success = res.success
        if(success === 1){
          this.$message.success('认证通过,账号已激活！')
          this.$router.push('Start')
        }else{
          this.$message.error('认证通过,账号激活失败！')
        }
      }).catch(err=>{
        console.log('激活失败 err=', err)
      })
    },
    okBtn2(){
      if(!this.okOrNot){
        return false
      }
      let answersList = []
      this.ownQuesList.forEach(ques =>{
        answersList.push({ id:ques.id , answer:ques.ans  })
      })
      let params = {
        answers: JSON.stringify(answersList)
      }
      answerSecurityQuestion(params).then(res1=>{
        let res = res1.data
        let success = res.success
        if(success === 1){ // 认证通过
          // this.$message.success('认证通过')
         this.unlockAccFun()
        }else{
          this.$alert(res.errorMsg, '提示', {
            confirmButtonText: '确定',
            type:'error',
            callback: (action) => {
              if(res.errorMsg === '对不起，您已连续答错多次，明天再继续回答吧！'){
                this.$router.replace('Start')
              }
            },
          })
        }

      }).catch(err=>{
        console.log('err=',err)
      })
    },
    okBtn(){
      if(this.staffName1 === ''){ this.$message.error('请先输入同事姓名！'); return false }
      if(this.staffName2 === ''){ this.$message.error('还有同事姓名未录入！'); return false }
      let userNames = [ this.staffName1, this.staffName2 ]
      if(this.orgStaffNumber > 2){
        if(this.staffName3 === ''){ this.$message.error('还有同事姓名未录入！'); return false }
        userNames = [ this.staffName1, this.staffName2,  this.staffName3  ]
      }
      let params = {
        phone: this.phone,
        orgName:this.ans1,
        userNames:userNames.join()
      }
      verifyQuestionBankTwo(params).then(res1=>{
        let res = res1.data
        let success = res.success
        if(success === 1){
          // this.$router.push('Start')
          this.unlockAccFun()
        }else{
          this.$alert(res.errorMsg, '提示', {
            confirmButtonText: '确定',
            type:'error',
            callback: (action) => {
              if(res.errorMsg === '对不起，您已连续答错多次，明天再继续回答吧！'
                  || res.errorMsg == '对不起，您没能答对安全问题，您私人领地内的数据已无法再访问！您依旧可登录系统，但需请公司总务协助操作！'){
                this.$router.replace('Start')
              }
            },
          })
        }


      }).catch(err=>{
        console.log('err=',err)
      })
    },
    getSecondQues(){

    },
    nextBtn(){
      if(this.quesType === 'ownSecurity'){ // 自定义的
        let ansList = []
        let param = { answers: ansList }
        answerSecurityQuestion(param).then(res1=>{
          let res = res1.data
        }).catch(err=>{
          console.log('err=',err)
        })

      }else{ // 默认
        if(this.ans1){
          let params = { phone: this.phone, orgName: this.ans1 }
          verifyQuestionBankOne(params).then(res1=>{
            let res = res1.data
            let success = res.success
            if(success === 1){
              this.step = 2
              this.orgStaffNumber = Number(res.data)
              this.getSecondQues()
            }else{
              this.$alert(res.errorMsg, '提示', {
                confirmButtonText: '确定',
                type:'error',
                callback: (action) => {
                  if(res.errorMsg === '第三次的提示'){
                    this.$router.replace('Start')
                  }else{
                    this.defaultQuestionBankOneFun()
                  }
                },
              })
            }


          }).catch(err=>{
            console.log('err=',err)
          })

        }else{
          this.$message.error('请先选择机构')
        }
      }

    },
    defaultQuestionBankOneFun(){
      this.ans1 = ''
      let phone = this.phone
      if(phone){
        // localStorage.removeItem('activePhone')
      }else {
        this.$router.replace('Start')
        return false
      }
      defaultQuestionBankOne({ phone:phone }).then(res2=>{
        console.log('调用的自定义的')
        this.quesType = 'default'
        let res20 = res2.data
        let success = res20.success
        if(success === 0){
          this.$alert(res20.errorMsg, '提示', {
            confirmButtonText: '确定',
            type:'error',
            callback: (action) => {
              this.$router.replace('Start')
            },
          })
        }else{//
          console.log('res20.data=', res20.data)
          let list = res20.data || []
          this.ques1 = list

        }

      }).catch(err=>{
        console.log('err=',err)
      })

    },
    getQuestion(){
      this.ans1 = ''
      ownSecurityQuestion().then(res1=>{
        let res = res1.data
        let list = res.data || [ {}, {}, {} ]
        if(list && list.length >0){
          this.ownQuesList = list
        }else{ // 没有自定义问题
          this.defaultQuestionBankOneFun()
        }

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    getMenuYwidth(){
      let height = window.innerHeight
      let litHeight = height - 66
      this.menuHeight = litHeight
      this.homePageHeight = litHeight -30
    },
    mainMountedFun(){
      window.addEventListener("resize", this.getMenuYwidth);

    },

  }
}
</script>

<style>
.colorRed{ color: red; }
</style>
<style lang="scss" scoped>
@import "@/style/tyIcon/icon_common.css";
body{
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  position: relative;
}
.mainall{ padding: 0!important; }
.commonHeaderBg{
  background: #2b3643;
  height:46px;
  color:#eaeaea ;
  .logo{
    font-size: 25px;
    position: relative;
    cursor: default;
    font-family: 'Arial';
    line-height: 46px;
    span{
      color: $ty-color-green;
    }
  }
  .menuBtn{
    float: right;
    position: relative;
    top: 17px;
    color:#eaeaea ;
    opacity: 0.3;
    &:hover{
      opacity: 1;
    }
  }
}
.menuBg{
  background: #34495e;
  padding: 0;
  cursor: default;
  .menuBgCon{
    //padding-bottom: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    //border: 2px solid red;
  }
}
.navList{
  background: #34495e;
  .menuIcon{
    display: inline-block;
    width: 16px; height: 16px;
    margin-right: 5px;
  }
  .menu3 .menuItem{ padding-left:40px; }

  .menu1{  line-height:42px; font-size:14px; position: relative; font-weight: 500; }
  .menu1>.menuItem.active{
    background: #36c6d3!important; color: #fff!important;
    .selected{
      display: block;
      float: right;
      position: absolute;
      right: 0;
      top: 12px;
      background: 0 0;
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-right: 8px solid #fcfcfc;
    }
  }
  .menu2>.menuItem.active{ background: #3e4b5c!important;   }
  .menu3>.menuItem.active{ background: #3e4b5c!important;   }
  .menuItem{
    color: #b4bcc8;
    background: #364150!important;
    border-top: 1px solid #3d4957;
    padding-left:20px;
    &:hover{
      background: #2C3542!important;
    }
  }
}
.topBtn{
  display: inline-block;
  height: 46px;
  padding:0 10px;
  cursor: pointer;
  &:hover{
    background: rgba(67, 83, 103, 0.85);
  }
}
.hLeft{
  line-height: 50px;
  font-size: 15px;
  color: #b4bcc8;
  .homePage{
    font-size: 20px; margin-right: 15px;  position: relative;
  }
  .curRole{ position: relative; top:-3px; }
  .otherRoleC{
    margin-left:20px;
    display: inline-block;
    position: relative;
    top: -3px;
    .roleItem2{ display: inline-block; }

  }
}
.hRight{
  float: right;
  line-height: 50px;

  .iframeCon{
    position: relative;
  }
  /* 浮窗 */
  .bounceFloating {
    //display: none;
    position: absolute; top: 48px; z-index: 99; box-shadow: 0 1px 4px #bdbdbd;
    border: 1px solid #c8c8c8; background-color: #36C6D3; border-top: none; width: 395px; height: 550px; right: 0;

    iframe { width: 100%; height: 100%; border: none; }

  }
  .bounceFloating:after{
    content: ''; position: absolute; display: block; border: 8px solid transparent; border-bottom-color: transparent; border-bottom-color: #36c6d3; top: -16px; right: 16px;
  }

  div.floatingContainer {
    position: relative; width: 100%; height: 100%; overflow: hidden;
  }

  div.floatingContainer > div.bonceHead {
    border-bottom: none; background: none; text-align: right; cursor: all-scroll; height: 8px;
    position: absolute; top: 0; width: 100%; right: 0; padding-right: 32px;

  }



  .sysTip{
    position: relative;
    .fa{
      font-size: 20px;
      padding: 0 10px;
      color:#b4bcc8;
    }
    .dotNum{
      background: $ty-color-green;
      position: absolute;
      top: 3px;
      right: 9px;
      display: inline-block;
      height:14px;
      line-height:14px;
      padding: 2px;
      border-radius: 10px;
      font-size: 12px;
      min-width: 14px;
      text-align: center;
    }
  }
  .userCon{
    position: relative;
    font-size: 14px;
    padding: 0 10px;
    line-height:40px;
    top:-2px;
    font-weight:normal;
    &>span{ display: inline-block; }
    .userimg{ height: 30px; margin:0 5px; position: relative; top:9px;   width: 30px;
      border-radius: 15px; }
    .fa{ display: inline-block; margin:0 0 0 10px; color: #b4bcc8;  }
    .username{ padding:0 5px; color: #b4bcc8; }
    .orgfullname{ color: #b4bcc8;  }
    .userControl {
      position: absolute; top:48px; right:0; width: 200px; background: #fff; color: #333;
      box-shadow: 0 6px 12px #ddd;
      border-radius: 5px;
      line-height: 1.2;
      padding: 16px;
      cursor: default;
      z-index: 99;
      .fa { margin: 0; margin-right: 10px; }
      .control1{
        .changeImgBtn { width: 64px; height: 64px;
          position: absolute; left: 0; top: 0; text-align: center; z-index: 1; background: rgba(0,0,0,0.3); border-radius: 50%;
        }
        .litLi{
          display: flex;
          padding: 10px 4px;
          border-bottom: 1px solid #f6f6f6;
          color: #333;
          cursor: pointer;
          border-radius: 4px;
          .label{
            flex: auto;
          }
          i{
            font-size: 16px;
            color: #333;
            &.fa{
              width: 16px;
              text-align: center;
              color: #333;
            }
          }
          &:last-child{
            border-bottom: none;
          }
          &:hover{
            color:#0b94ea;
            i{
              color:#0b94ea;
            }
          }
        }
        .user_info {
          display: flex; flex-direction: row;align-items: center; margin: 8px 0 16px 0;
          .logo-avatar {
            position: relative;
            img.img-circle, .changeImgCon { width: 56px; height: 56px; border-radius: 50%; border: 3px solid #eee}
            .changeImgCon{
              position: absolute;
              background-color: rgba(100,100,100, 0.3);
              z-index: 1; text-align: center; top:0;
              .txtLink{
                line-height: 60px;
                color: #fff;
                &:hover{
                  font-weight: bold;
                }
              }
            }
          }
          .u1 {
            margin-left: 8px;
            line-height: 1.5;
            color: #333;
            .name{font-weight: bold; font-size: 16px;}
            .mobile_avatar{
              display: flex; align-items: center;
              .el-icon{
                font-size: 16px;
                margin-left: 8px;
                color: #0b94ea;
                cursor: pointer;
              }
            }
          }

        }
      }
      .bigLi>div {
        padding: 0 15px;
        background: #fff;
        a{
          display: block; height: 36px; color: #333; line-height: 36px; border-bottom: 1px solid #eaeaea;
          &:hover{ color:$ty-color-green; }
        }
        #bdWx { font-size: 0.8em; margin-right: 5px; }
      }

      .control2{
        .header_nav{
          padding: 4px 0;
          margin-bottom: 8px;
          font-size: 16px;
          position: relative;
          .nav_title{
            flex: auto;
            text-align: center;
          }
          .el-icon{
            cursor: pointer;
            font-size: 18px;
            position: absolute;
            top: 4px;
            left: 0;
          }
        }
        .litLi{
          display: flex;
          padding: 10px 4px;
          border-bottom: 1px solid #f6f6f6;
          color: #333;
          cursor: pointer;
          border-radius: 4px;
          .label{
            flex: auto;
          }
          i{
            font-size: 16px;
            color: #333;
            &.fa{
              width: 16px;
              text-align: center;
              color: #333;
            }
          }
          &:last-child{
            border-bottom: none;
          }
          &:hover{
            color:#0b94ea;
            i{
              color:#0b94ea;
            }
          }
        }
      }
    }

  }

  @-webkit-keyframes twink {
    from {opacity: 1.0;}
    50% {opacity: 0.4;}
    to {opacity: 1.0;}
  }
  @keyframes twink {
    from {opacity: 1.0;}
    50% {opacity: 0.2;}
    to {opacity: 1.0;}
  }
  .twink{
    color: #fff;
    -webkit-animation: twink 0.8s linear 1s 5 alternate;
    animation: twink 0.8s linear infinite;
  }


}
.passwordEditCon{
  margin: 20px;
  .tip{ font-size: 0.8em; color: $ty-color-blue; }
}

.accEditTip{
  font-size: 0.8em;
  .orangeTip{ color:$ty-color-orange; }
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
  .fade-enter, .fade-leave-to { opacity: 0; }
}
.activeCon{
  padding: 40px;

  .activeTip{ line-height: 30px;   }
  .line30{
    line-height: 30px; margin-top: 20px;
    .ty-color-blue{ font-size:14px;  }
  }
  .flexCon{
     padding: 10px; width: 90%;
    .flitem{ float: left; width: 300px; }
  }
  .contrlCon{ padding:50px 200px; }
  .marRight20{ margin-right: 50px; }
  .inputA{ width: 400px; }
  .quesItem{ margin:20px;  }
}
</style>







