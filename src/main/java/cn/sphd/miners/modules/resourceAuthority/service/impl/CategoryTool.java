package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.modules.resourceAuthority.entity.ResCategory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResCategoryMapper;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResMapper;

import java.math.BigInteger;
import java.util.List;

/**
 * Created by 朱思旭 on 2019/7/9.
 */
public class CategoryTool {

    private Integer leafs = 0;

    private BigInteger size = BigInteger.valueOf(0);

    public Integer getLeafs() {
        return leafs;
    }

    public void setLeafs(Integer leafs) {
        this.leafs = leafs;
    }

    public BigInteger getSize() {
        return size;
    }

    public void setSize(BigInteger size) {
        this.size = size;
    }

    public void updateCategoryMessage(Integer categoryId, ResCategoryMapper resCategoryMapper, ResMapper resMapper){
        List<ResCategory> listChild = resCategoryMapper.getAllChildCategoryByParent(categoryId);
        if(listChild.isEmpty()){
            List<ResEntity> listFile = resMapper.getFileBycategory(categoryId);
            if (!listFile.isEmpty()) {
                leafs = leafs + listFile.size();
                for (ResEntity res : listFile) {
                    BigInteger resSize = (BigInteger.valueOf(res.getSize()).multiply(BigInteger.valueOf(res.getChangeNum()))).add(BigInteger.valueOf(res.getSize()));
                    size = size.add(resSize);
                }
            }
        } else {
            for(ResCategory r : listChild){
                categoryId = r.getId();
                updateCategoryMessage(categoryId,resCategoryMapper, resMapper);
            }
        }

    }



}
