package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.modules.schedule.entity.MemoJobMsg;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.List;

/**
 * Created by Administrator on 2020/1/2.
 */
public interface MemoJobMsgService extends BadgeNumberCallback {

    MemoJobMsg getMemoJobMsgById(Integer id);

    MemoJobMsg saveMemoJobMsg(Integer superscript,String title, String content, String memo, Integer toUserId,Integer jobId,Integer scheduleId);

    void updateMemoJobMsg(MemoJobMsg memoJobMsg,String state,Integer toUserId);//提醒 单条减角标

    List<MemoJobMsg> getMemoJobMsgByJobId(Integer jobId,String state);

    List<MemoJobMsg> getMemoJobMsgByScheduleId(Integer scheduleId,String state);

    List<MemoJobMsg> getMemoJobMsgBuUserId(User user, String state);

    void  deleteAllMemoJobMsg(List<MemoJobMsg> memoJobMsgList);


}
