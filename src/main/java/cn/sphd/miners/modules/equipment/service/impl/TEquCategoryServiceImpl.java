package cn.sphd.miners.modules.equipment.service.impl;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquCategory;
import cn.sphd.miners.modules.equipment.entity.TEquCategoryHistory;
import cn.sphd.miners.modules.equipment.entity.TEquEquipmentHistory;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.mapper.TEquCategoryHistoryMapper;
import cn.sphd.miners.modules.equipment.mapper.TEquCategoryMapper;
import cn.sphd.miners.modules.equipment.mapper.TEquModelMapper;
import cn.sphd.miners.modules.equipment.service.ITEquCategoryService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_分类
 * 20230209 1.247装备器具新增Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquCategoryServiceImpl implements ITEquCategoryService {
    @Autowired
    private TEquCategoryMapper tEquCategoryMapper;

    @Autowired
    private TEquCategoryHistoryMapper categoryHistoryMapper;

    @Autowired
    private TEquModelMapper modelMapper;

    /**
     * 查询装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_分类
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_分类
     * 20230209 1.247装备器具新增
     */
    @Override
    public TEquCategory selectTEquCategoryById(Long id) {
        return tEquCategoryMapper.selectTEquCategoryById(id);
    }

    /**
     * 查询装备管理_分类
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @return 装备管理_分类
     * 20230209 1.247装备器具新增
     */
    @Override
    public List<TEquCategory> selectTEquCategoryList(TEquCategory tEquCategory) {
        return tEquCategoryMapper.selectTEquCategoryList(tEquCategory);
    }

    /**
     * 新增装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @param user
     * @return 结果
     */
    @Override
    public int insertTEquCategory(TEquCategory tEquCategory, User user) {
        //维护path
        StringBuilder path = new StringBuilder(tEquCategory.getName());

        StringBuilder catIds = new StringBuilder(tEquCategory.getDescendants() == null ? "" : tEquCategory.getDescendants());

        tEquCategory.setEnabled(1L);
        tEquCategory.setCreateDate(new Date());
        tEquCategory.setCreateName(user.getUserName());
        tEquCategory.setCreator(Long.valueOf(user.getUserID()));
        //维护childrens 直接子类的个数
        tEquCategory.setChildrens(0L);//当前新增的分类无子类
        tEquCategory.setOrg(Long.valueOf(user.getOid()));
        tEquCategory.setIsSystem(0);
        tEquCategory.setRevisable(1);
        TEquCategory ec = tEquCategory;


        TEquCategory parent = tEquCategoryMapper.selectTEquCategoryById(ec.getParent());
        if (parent != null) {
            parent.setChildrens(parent.getChildrens() == null ? 1L : parent.getChildrens() + 1L);//父类的直接子类数量加1
            tEquCategoryMapper.updateTEquCategory(parent);
        }


        while (ec.getParent() != null && ec.getParent() != 0L) {
            parent = tEquCategoryMapper.selectTEquCategoryById(ec.getParent());
            if (parent != null) {

                catIds.insert(0, parent.getId() + "/");
                tEquCategoryMapper.updateTEquCategory(parent);
                path.insert(0, parent.getPath() + "/");
                ec = parent;
            } else {
                break;
            }
        }

        tEquCategory.setPath(path.toString());
        tEquCategory.setDescendants(catIds.toString());

        return tEquCategoryMapper.insertTEquCategory(tEquCategory);
    }

    /**
     * 修改装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @param user
     * @return 结果
     */
    @Override
    public int updateTEquCategory(TEquCategory tEquCategory, User user) {
        TEquCategory parent;
        TEquCategory now = tEquCategoryMapper.selectTEquCategoryById(tEquCategory.getId());

        Long versionNo = 0L;

        if (tEquCategory.getEnabled() == null) {
            //查询是否需要保存原始信息
            TEquCategoryHistory tEquCategoryHistory = new TEquCategoryHistory();
            tEquCategoryHistory.setCategory(tEquCategory.getId());
            List<TEquCategoryHistory> histories = categoryHistoryMapper.selectTEquCategoryHistoryList(tEquCategoryHistory);
            if (histories == null || histories.size() == 0) {
                TEquCategoryHistory history = new TEquCategoryHistory();
                BeanUtils.copyProperties(now, history);
                history.setCreateDate(now.getCreateDate());
                history.setCreator(Long.valueOf(user.getUserID()));
                history.setCreateName(user.getUserName());
                history.setCategory(now.getId());
                history.setVersionNo(versionNo);
                categoryHistoryMapper.insertTEquCategoryHistory(history);
            }
            histories = categoryHistoryMapper.selectTEquCategoryHistoryList(tEquCategoryHistory);

            TEquCategoryHistory h = histories.stream().max(Comparator.comparing(TEquCategoryHistory::getVersionNo)).get();
            versionNo = h.getVersionNo() + 1;

            if (StringUtils.isNotBlank(tEquCategory.getName()))
                now.setName(tEquCategory.getName());
            if (StringUtils.isNotBlank(tEquCategory.getContent()))
                now.setContent(tEquCategory.getContent());


            TEquCategoryHistory history = new TEquCategoryHistory();
            BeanUtils.copyProperties(tEquCategory, history);
            history.setCreateDate(new Date());
            history.setCreator(Long.valueOf(user.getUserID()));
            history.setCreateName(user.getUserName());
            history.setCategory(tEquCategory.getId());
            history.setVersionNo(versionNo);
            categoryHistoryMapper.insertTEquCategoryHistory(history);
        } else {//若是停用，查询是否可停用  若可以停用 父类别直属子类别减1


            parent = tEquCategoryMapper.selectTEquCategoryById(now.getParent());
            if (parent != null) {
                if (tEquCategory.getEnabled() == 0L) {
                    TEquModel model = new TEquModel();
                    model.setCategory(now.getId());
                    List<TEquModel> models = modelMapper.selectTEquModelList(model);
                    if (!models.isEmpty()) {
                        return -1;
                    }

                    parent.setChildrens(parent.getChildrens() - 1);
                    tEquCategoryMapper.updateTEquCategory(parent);
                    tEquCategory.setEnabledTime(new Date());
                    tEquCategory.setUpdateName(user.getUserName());
                    tEquCategory.setRevisable(1);//停用后可删除
                    tEquCategoryMapper.updateTEquCategory(parent);
                } else if (tEquCategory.getEnabled() == 1L) {
                    tEquCategory.setRevisable(1);
                    parent.setChildrens(parent.getChildrens() + 1);
                    tEquCategoryMapper.updateTEquCategory(parent);
                }
            }


        }

        tEquCategory.setRevisable(0);

        Integer result = tEquCategoryMapper.updateTEquCategory(tEquCategory);

        //修改后 更新树
        TEquCategory oc = new TEquCategory();
        oc.setOrg(Long.valueOf(user.getOid()));
        List<TEquCategory> ocs = tEquCategoryMapper.selectTEquCategoryList(oc);
        for (TEquCategory equCategory : ocs) {
            if (StringUtils.isNotBlank(equCategory.getDescendants())) {
                StringBuilder p = new StringBuilder();
                String[] s = equCategory.getDescendants().split("/");
                for (String s1 : s) {
                    TEquCategory c = tEquCategoryMapper.selectTEquCategoryById(Long.valueOf(s1));
                    if (c != null) {
                        p.append(c.getName()).append('/');
                    }
                }
                p.append(equCategory.getName());

                equCategory.setPath(p.toString());

            } else {
                equCategory.setPath(equCategory.getName());
            }

            tEquCategoryMapper.updateTEquCategory(equCategory);
        }
        return result;

    }

    /**
     * 批量删除装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_分类
     *            20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquCategoryByIds(String... ids) {
        //查询分类是否被使用
        for (String id : ids) {
            TEquModel m = new TEquModel();
            m.setCategory(Long.valueOf(id));
            List<TEquModel> tEquModels = modelMapper.selectTEquModelListByCategory(m);
            if (tEquModels != null && tEquModels.size() > 0) {
                return 0;
            } else {
                TEquCategory c = tEquCategoryMapper.selectTEquCategoryById(Long.valueOf(id));
                if (c != null) {
                    //查询是否有子类
                    if (c.getChildrens() != null && c.getChildrens() > 0) {
                        return 0;
                    }
                    if (c.getParent() != null && c.getParent() != 0L) {
                        TEquCategory p = tEquCategoryMapper.selectTEquCategoryById(c.getParent());
                        p.setChildrens(p.getChildrens() - 1);
                        tEquCategoryMapper.updateTEquCategory(p);
                    }
                }
            }
        }

        //删除后 父亲的直属子类别减1


        return tEquCategoryMapper.deleteTEquCategoryByIds(ids);
    }

    /**
     * 删除装备管理_分类
     * 20230209 1.247装备器具新增信息
     *
     * @param id 装备管理_分类
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquCategoryById(Long id) {
        return tEquCategoryMapper.deleteTEquCategoryById(id);
    }

    @Override
    public void initCategories(User user) {
        TEquCategory c = new TEquCategory();

        c.setName("一类装备");
        c.setOrg(Long.valueOf(user.getOid()));
        c.setContent("直接用于生产产品或零件的机器、设备及其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(0);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(1);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("二类装备");
        c.setContent("直接用于生产产品或零件的模具、夹具、工装及其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(0);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(1);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("三类装备");
        c.setContent("直接用于产品或零件的检测仪器及其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(0);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(1);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("四类装备");
        c.setContent("直接用于产品或零件的量具、检具及其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(0);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(1);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("五类装备");
        c.setContent("不直接用于生产产品或零件的机器、设备及其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(1);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(0);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("六类装备");
        c.setContent("不直接用于生产产品或零件的模具、夹具、工装及其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(1);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(0);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("七类装备");
        c.setContent("不直接用于产品或零件的检测仪器及其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(1);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(0);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("八类装备");
        c.setContent("不直接用于产品或零件的量具、检具或其他类似装备");
        c.setPath(c.getName());
        c.setRevisable(1);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(0);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("九类装备");
        c.setContent("其他装备器具");
        c.setPath(c.getName());
        c.setRevisable(1);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(0);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
        c = new TEquCategory();
        c.setOrg(Long.valueOf(user.getOid()));
        c.setName("不属于装备器(所以无法分类)的固定资产或低值易耗品");
        c.setContent("");
        c.setPath(c.getName());
        c.setRevisable(1);
        c.setCreateName("系统自带");
        c.setCreateDate(new Date());
        c.setIsSystem(0);
        c.setEnabled(1L);
        c.setChildrens(0L);
        c.setParent(0L);
        tEquCategoryMapper.insertTEquCategory(c);
    }
}
