<template><!-- 财务部分能看到全部审批流程的详情 -->
  <div id="reimburseApprove"  v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="tittle"></TY_NavTop>
    <div class="container" v-if="code !== '23'">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div class="panel-title">
        {{personnelReimburse.createName}} 申请报销 {{personnelReimburse.amount}} 元  <div class="right">{{personnelReimburse.transactionType | isSaler}}</div>
      </div>
      <div class="panel-content">
        <div class="item-header">报销事由</div>
        <div>事件的发生日期：{{personnelReimburse.beginDate | formatDay('YYYY年MM月DD日')}} - {{personnelReimburse.endDate | formatDay('DD日')}}</div>
        <div>
          {{personnelReimburse.purpose}}
        </div>
      </div>
      <div class="panel-content">
        <div class="text-right" v-on:click="toggle = -toggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
        <div v-show="toggle > 0">
          <div class="item-content">
            <div class="processItem">
              <div>提交报销申请</div>
              <div>申请人</div>
              <div>{{personnelReimburse.createName | stringSplit(4)}} {{personnelReimburse.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
            <div v-for="it in approvalProcessList" v-bind:key="it.id">
              <div v-if="it.approveStatus === '2' && it.businessType == 32" class="processItem">
                <div>在线审核通过</div>
                <div>出纳员</div>
                <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-if="it.approveStatus === '2' && !it.businessType" class="processItem" >
                <div>在线审批通过</div>
                <div>审批人</div>
                <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-if="code !== '35'">
                <div v-if="it.approveStatus === '2' && it.businessType == 33" class="processItem">
                  <div>线下审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 17" class="processItem">
                  <div>付款审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 21" class="processItem">
                  <div>付款方式确认</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 20" class="processItem">
                  <div>付款复核通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 22" class="processItem">
                  <div>付款方式修改</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '5'" class="processItem">
                  <div>
                    <span v-if="financeAccount.accountType == '2'">报销款已转账</span>
                    <span v-if="financeAccount.accountType == '1'">现金付讫</span>
                  </div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            </div>
            <div v-if="code !== '35'">
              <!-- 最后一条审批记录展示 -->
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===32">
                <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{  nowApproveItem.reasonStr }}</div></div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && !nowApproveItem.businessType">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===33">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '8'">
                <div style="margin-top:15px;">被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue">
                <!--<span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && !nowApproveItem.businessType"><b>{{nowApproveItem.userName}}</b>为下一个审批人</span>-->
                <span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===32">等待出纳在线审核。</span>
                <span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===33">
                请将实际票据交出纳，由出纳进行线下审核。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-if="nowApproveItem.approveStatus === '1' && isNowApprover && nowApproveItem.businessType===33">
                请等待报销者提交实际票据。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="panel-title">
        本次报销共有票据 {{personnelReimburse.billQuantity}} 张，合计 {{personnelReimburse.billAmount}} 元
      </div>
      <div class="panel-content">
        <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
          <el-tab-pane label="按票据查看" name="bill">
            <div class="ui-cells_none">
              <a class="ui-cell" @click="goBillInfo(item)" v-for="item in listMapBillCat" v-bind:key="item.billCat">
                <div class="ui-cell__bd">
                  <div class="ui-cell_con">
                    <el-row>
                      <el-col :span="12">{{item.billCatName}}</el-col>
                      <el-col :span="6">{{item.num}}张</el-col>
                      <el-col :span="6">{{item.totalAmount}} 元</el-col>
                    </el-row>
                  </div>
                  <div class="ui-cell__ft"></div>
                </div>
              </a>
            </div>
          </el-tab-pane>
          <el-tab-pane label="按费用查看" name="feeCat">
            <a class="ui-cell" @click="goFeeInfo(item)"  v-for="item in listMapFeeCat" v-bind:key="item.feeCat">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <el-row>
                    <el-col :span="12">{{item.feeCatName}}</el-col>
                    <el-col :span="6">{{item.secondFeeCatName | chargeNull}}</el-col>
                    <el-col :span="6">{{item.totalAmount}} 元</el-col>
                  </el-row>
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <hr>
        <div class="ui-cells_none">
          <div v-if="isNowApprover">
            <!--正常审批-->
            <a class="ui-cell" v-if="approveStatus === '1' && code === '11'">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div class="handle_button">
                    <button class="ui-btn" @click="approve('approvalReimburse', 2)">驳回</button>
                    <button class="ui-btn ui-btn_info" @click="approve('approvalReimburse', 1)">批准</button>
                  </div>
                </div>
              </div>
            </a>
            <!--财务审批  -->
            <!-- 在线审核 -->
            <a class="ui-cell" v-if="code === '31'">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>
                    <p>请根据在线审核的结果进行选择：</p>
                    <p style="text-align: right;"><span class="linkBtn" @click="jumpExplain">票据审核说明</span></p>
                    <div>
                      <el-radio-group v-model="approveSta" size="small">
                        <el-radio label="2">在线审批通过，准予进入后续审批。</el-radio><br/>
                        <el-radio label="3">在线审批未通过。</el-radio><br/>
                      </el-radio-group>
                    </div>
                    <div v-if="approveSta === '3'">
                      <p>请确认在线审批无法通过的原因（可多选）</p>
                        <el-checkbox v-model="approveSelect" label="1">照片不清楚，或信息被遮挡，以至于无法审核</el-checkbox>
                        <el-checkbox v-model="approveSelect" label="2">无法入会计帐的票据较多</el-checkbox>
                        <el-checkbox v-model="approveSelect" label="3">包含公司不允许报销的票据</el-checkbox>
                        <el-checkbox v-model="approveSelect" label="4">票据内容与所录入的信息不一致</el-checkbox>
                        <br>
                        <span v-show="approveSelect.indexOf('5')>-1" style="float: right;margin-right:100px; " >{{ reason.length }}/40</span>
                        <el-checkbox v-model="approveSelect" label="5">其他原因</el-checkbox>
                        <el-input style="width:95%; " v-show="approveSelect.indexOf('5')>-1" type="textarea" :rows="2" v-model="reason"
                                  placeholder="请在此录入在线审批无法通过的原因，最多可录入40个字。" v-on:keyup.native="strSub" @change="strSub"></el-input>
                    </div>
                  </div>
                  <div class="handle_button" style="text-align: center; margin:0 30px; ">
                    <button class="ui-btn ui-btn_info" style="width:300px; " @click="approve2('onlineAuditApproval')">确 定</button>
                  </div>
                </div>
              </div>
            </a>
            <!-- 待线下审核 -->
            <a class="ui-cell" v-if="code === '33'">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div style="padding-right:15px; ">
                    <p>报销人给您的实际票据，与您已在系统中在线审批过的票据图片是否一致？请确认后选择：</p>
                    <div>
                      <el-radio-group v-model="approveSta" size="small">
                        <el-radio label="2">一致。现申请付款。</el-radio><br/>
                        <el-radio label="3">不一致，驳回报销申请。</el-radio><br/>
                      </el-radio-group>
                    </div>
                  </div>
                  <div class="handle_button" style="text-align: center; margin:0 30px; ">
                    <button class="ui-btn ui-btn_info" style="width:300px; " @click="approve2('offlineAuditApproval')">确 定</button>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="container" v-loading.fullscreen.lock="loading" v-if="code === '23'">
      <div class="panel-title">
        此项报销中，需认证处理的增值税专用发票共 {{num}} 张
      </div>
      <div>
        <div class="ui-cells_none">
          <el-table
            :data="financeReimburseBills"
            fit
            border
          >
            <el-table-column
              prop="billNo"
              label="发票号码"
              width="120"
            >
            </el-table-column>
            <el-table-column
              prop="itemAmount"
              label="价税合计"
            >
            </el-table-column>
            <el-table-column
              prop="certificationState"
              :formatter="certificationType"
              label="状态"
            >
              <template slot="header" slot-scope="scope">
                状态
              </template>
              <template slot-scope="scope">
                <div class="handle_btn">
                  <span v-if="scope.row.certificationState == '0'">未认证</span>
                  <span class="color-green" v-if="scope.row.certificationState == '1'">认证已通过</span>
                  <span class="color-red" v-if="scope.row.certificationState == '2'">不抵扣</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="certificationState"
              :formatter="certificationType"
              label="状态"
            >
              <template slot="header" slot-scope="scope">
                操作
              </template>
              <template slot-scope="scope">
                <div class="handle_btn">
                  <input type="submit" class="ui-btn ui-btn_info" value="认证处理" :disabled="scope.row.certificationState !== '0'" @click="goFinanceBillInfo((scope.row.id))">
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  .linkBtn{ color:#5d9cec; cursor:default; margin-right:20px; }
  .linkBtn:hover{ text-decoration:underline;  }
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .handle_btn .ui-btn{
    padding: 6px 12px;
    font-size: 12px;
    line-height: 1;
    height: inherit;
    border-radius: 3px;
    width: auto;
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  hr{ border:none; margin:15px 30px; border-bottom:1px solid #ccc; }
</style>
<script>
import { isSaler, chargeNull } from '../../../js/common'
import rejectPng from '../../../images/reject.png'
export default {
  name: 'approveReimburseDetail',
  data () {
    return {
      isReject: false, // 判断是否驳回来显示驳回图片
      isNowApprover: false,
      rejectPng: rejectPng, // 驳回图片
      twoApproval: false,
      tips: false,
      tipContent: '',
      approveSta: '',
      loading: true,
      toggle: -1,
      code: '',
      tittle: '',
      approveSelect: [],
      reason: '',
      approveStatus: '0',
      personnelReimburse: {},
      approvalProcessList: [],
      listMapBillCat: {},
      listMapFeeCat: {},
      financeAccount: {},
      num: 0,
      financeReimburseBills: [],
      isSpecialBill: 0,
      isPublic: '',
      nowApproveItem: {
        'approveStatus': '0'
      },
      square: {},
      bankList: [],
      listenersUid: [],
      rules: {
        payMethod: [
          { required: true }
        ],
        financeAccountId: [
          { required: true }
        ],
        summary: [
          { required: true }
        ]
      },
      activeName: 'bill'
    }
  },
  filters: {
    isSaler,
    chargeNull,
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    },
    setReasonList: function (approveSelect) {
      let list = approveSelect.split(',')
      let str = ``
      list.each(function (item) {
        if (item === 1) {
          str += `照片不清楚，或信息被遮挡，以至于无法审核。<br>`
        } else if (item === 2) {
          str += `无法入会计帐的票据较多。<br>`
        } else if (item === 3) {
          str += `包含公司不允许报销的票据。<br>`
        } else if (item === 4) {
          str += `票据内容与所录入的信息不一致。<br>`
        } else if (item === 5) {
          str += `其他原因：${this.nowApproveItem.reason}`
        }
      })
      return str
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    let billCat = this.$route.params.billCat || '0'
    console.log(this.$route.params)
    let reimburseId = paramCode.substring(2, paramCode.length)
    let code = paramCode.substring(0, 2)
    this.code = code
    switch (code) {
      case '00':
        this.tittle = '付款复核'
        break
      case '11':
      case '35':
        this.tittle = '报销审批'
        break
      case '22':
      case '36':
        this.tittle = '个人报销的付款'
        break
      case '31':
      case '32':
      case '33':
      case '34':
        this.tittle = '个人报销的票据审核'
        break
    }
    this.listenersUid = [
      this.sphdSocket.subscribe('reimburseNotice', function (data) {
        that.loading = false
        let obj = JSON.parse(data)
        let status = Number(obj.status)
        console.log('reimburseNotice receive OK : ' + status)
        if (code === '11' || code === '21') {
          // 来源为出纳待处理列表的详情/一级审批待处理列表的详情
          if (status === 1) {
            that.$kiko_message('操作成功')
          } else if (status === 2) {
            that.$kiko_message('已审批，不予重复操作')
          } else if (status === 0) {
            that.$kiko_message('操作失败')
          } else {
            that.$kiko_message('系统错误')
          }
        } else if (code === '22') {
          // 来源为出纳待两讫列表的详情
          if (status === 1) {
            that.$kiko_message('操作成功')
          } else if (status === 2) {
            that.$kiko_message('该报销申请已两讫，不能重复批准')
          } else if (status === 3) {
            that.$kiko_message('余额不足')
          } else if (status === 0) {
            that.$kiko_message('两讫失败')
          } else {
            that.$kiko_message('系统错误')
          }
        } else if (code === '31' || code === '33') {
          // 来源在线审批 0-操作失败 1-操作成功 2-不可重复操作
          if (status === 1) {
            that.$kiko_message('操作成功')
          } else if (status === 2) {
            that.$kiko_message('不可重复操作')
          } else if (status === 0) {
            that.$kiko_message('操作失败')
          } else {
            that.$kiko_message('无法识别，操作失败')
          }
        }
        if (code === '21' || code === '22' || code === '23') {
          that.$router.push({
            path: '/financeReimburse',
            name: 'financeReimburse'
          })
        } else if (code === '31' || code === '32' || code === '33') {
          that.$router.push({
            path: '/reimburseBillVerification',
            name: 'reimburseBillVerification'
          })
        } else {
          that.$router.push({
            path: '/approvalReimburse',
            name: 'approvalReimburse'
          })
        }
      }),
      this.sphdSocket.subscribe('getReimburseDetail', function (data) {
        that.loading = false
        let ReimburseDetail = JSON.parse(data)
        // 报销申请信息
        that.personnelReimburse = ReimburseDetail.personnelReimburse
        that.financeAccount = ReimburseDetail.financeAccount
        that.square.summary = that.personnelReimburse.summary
        // 审批列表
        that.approvalProcessList = ReimburseDetail.approvalProcessList
        if (ReimburseDetail.approvalProcessList.length > 0) {
          that.nowApproveItem = that.approvalProcessList[that.approvalProcessList.length - 1]
          console.log('approvalProcessList')
          console.log(that.approvalProcessList)
          console.log('nowApproveItem')
          console.log(that.nowApproveItem)
          let lastToUser = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].toUser
          if (!lastToUser || lastToUser === that.sphdSocket.user.userID) {
            that.isNowApprover = true
          }
        }
        that.approveStatus = that.approvalProcessList[that.approvalProcessList.length - 1].approveStatus
        if (that.approveStatus === '3' || that.approveStatus === '8') {
          that.isReject = true
          that.showReason()
        }
        // 按照票据种类列表
        that.listMapBillCat = ReimburseDetail.listMapBillCat
        for (var i in ReimburseDetail.listMapBillCat) {
          let billCatName = ReimburseDetail.listMapBillCat[i].billCatName
          if (billCatName === '增值税专用发票') {
            that.isSpecialBill = 1
          } else if (billCatName === '收据') {
            that.isSpecialBill = 2
          }
        }
        // 按费用类别列表
        that.listMapFeeCat = ReimburseDetail.listMapFeeCat
        console.log('getReimburseDetail session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('authInvoiceDetail', function (data) {
        that.loading = false
        let authInvoiceDetail = JSON.parse(data)
        // 报销申请信息
        that.num = authInvoiceDetail.num
        that.financeReimburseBills = authInvoiceDetail.financeReimburseBills
        console.log('authInvoiceDetail session Socket received OK:' + data)
      })
    ]
    let session = this.sphdSocket.sessionid
    if (!billCat || billCat === '0') {
      // billCat 为 0 代表获取普通报销详情 有具体值的话获取财务待认证详情
      // this.sphdSocket.send('getReimburseDetail', { reimburseId: reimburseId, session: session })
      this.getDetails(reimburseId)
    } else {
      this.sphdSocket.send('authInvoiceDetail', { billCat: billCat, reimburseId: reimburseId, session: session })
    }
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getDetails: function (reimburseId) {
      let that = this
      let params = {
        reimburseId:reimburseId
      }
      this.axios.post('../../../reimburseWindow/getReimburseDetail.do', params)
        .then(function (response) {
          console.log('详情=', response)
          let data = response.data.data
          console.log('详情 data=', data)

          that.loading = false
          let ReimburseDetail = data
          // 报销申请信息
          that.personnelReimburse = ReimburseDetail.personnelReimburse
          that.financeAccount = ReimburseDetail.financeAccount
          that.square.summary = that.personnelReimburse.summary
          // 审批列表
          that.approvalProcessList = ReimburseDetail.approvalProcessList
          if (ReimburseDetail.approvalProcessList.length > 0) {
            that.nowApproveItem = that.approvalProcessList[that.approvalProcessList.length - 1]
            console.log('approvalProcessList')
            console.log(that.approvalProcessList)
            console.log('nowApproveItem')
            console.log(that.nowApproveItem)
            let lastToUser = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].toUser
            if (!lastToUser || lastToUser === that.sphdSocket.user.userID) {
              that.isNowApprover = true
            }
          }
          that.approveStatus = that.approvalProcessList[that.approvalProcessList.length - 1].approveStatus
          if (that.approveStatus === '3' || that.approveStatus === '8') {
            that.isReject = true
            that.showReason()
          }
          // 按照票据种类列表
          that.listMapBillCat = ReimburseDetail.listMapBillCat
          for (var i in ReimburseDetail.listMapBillCat) {
            let billCatName = ReimburseDetail.listMapBillCat[i].billCatName
            if (billCatName === '增值税专用发票') {
              that.isSpecialBill = 1
            } else if (billCatName === '收据') {
              that.isSpecialBill = 2
            }
          }
          // 按费用类别列表
          that.listMapFeeCat = ReimburseDetail.listMapFeeCat


        })
        .catch(function (error) {
          console.log(error)
        })
    },
    strSub: function () {
      let reasonLen = this.reason.length
      if (reasonLen > 40) {
        this.reason = this.reason.substr(0, 40)
      }
    },
    showReason: function () {
      let str = ''
      let reasonStr = this.nowApproveItem.reason
      if (String(this.nowApproveItem.approveSelect) === '5') {
        str += '其他原因:\n' + reasonStr
      } else {
        let approveSelectArr = []
        let selectStr = this.nowApproveItem.approveSelect
        if (selectStr && selectStr !== 'null') {
          if (selectStr.length > 2) {
            approveSelectArr = selectStr.split(',')
          } else {
            approveSelectArr = [selectStr]
          }
          for (let i = 0; i < approveSelectArr.length; i++) {
            switch (String(approveSelectArr[i])) {
              case '1':
                str += '照片不清楚，或信息被遮挡，以至于无法审核\n'
                break
              case '2':
                str += '无法入会计帐的票据较多\n'
                break
              case '3':
                str += '包含公司不允许报销的票据\n'
                break
              case '4':
                str += '票据内容与所录入的信息不一致\n'
                break
              default:
            }
          }
        }
      }
      this.nowApproveItem['reasonStr'] = str
    },
    search: function () {
      this.$router.push({
        path: `/approveReimburseQuery`
      })
    },
    jumpExplain: function () {
      this.$router.push({
        path: `/approveReimburseDetailExplain`
      })
    },
    goBillInfo: function (item) {
      let params = {
        'name': 'approve',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': item.billCat,
        'feeCat': '',
        'secondFeeCat': '',
        'billCatName': item.billCatName,
        'feeCatName': '',
        'secondFeeCatName': '',
        'totalAmount': item.totalAmount,
        'num': item.num,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFinanceBillInfo: function (id) {
      let params = {
        id: id
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFeeInfo: function (item) {
      let params = {
        'name': 'approve',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': '',
        'feeCat': item.feeCat,
        'secondFeeCat': item.secondFeeCat,
        'billCatName': '',
        'feeCatName': item.feeCatName,
        'secondFeeCatName': item.secondFeeCatName,
        'totalAmount': item.totalAmount,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    approve: function (type, state) {
      let approveInfo = {
        userId: this.sphdSocket.user.userID,
        approvalProcessId: this.approvalProcessList[this.approvalProcessList.length - 1].id,
        approvalStatus: state,
        session: this.sphdSocket.sessionid,
        reason: ''
      }
      console.log(JSON.stringify(this.approvalProcessList))
      console.log(JSON.stringify(approveInfo))
      let that = this
      if (state === 2) {
        if (type === 'cashierTwoApproval') {
          approveInfo.reason = ''
          that.sphdSocket.send(type, approveInfo)
          that.loading = true
        } else {
          this.$prompt('驳回理由', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(({ value }) => {
            that.loading = true
            approveInfo.reason = value || ''
            that.sphdSocket.send(type, approveInfo)
            console.log(JSON.stringify(approveInfo))
          })
        }
      } else {
        if (type === 'cashierTwoApproval') {
          if (!that.square.payMethod) {
            that.$kiko_message('请选择支出方式')
          } else if (Number(that.square.payMethod) === 3 && !that.square.financeAccountId) {
            that.$kiko_message('请选择转账银行')
          } else if (!that.square.summary) {
            that.$kiko_message('请输入摘要')
          } else {
            approveInfo.payMethod = that.square.payMethod
            approveInfo.financeAccountId = that.square.financeAccountId || ''
            approveInfo.summary = that.square.summary
            let tips = ''
            if (that.isSpecialBill === 1) {
              tips = '本次报销中含有增值税专用发票。<span class="color-red">您需在系统的“增票认证”中确认认证情况后，</span>报销数据才会进入会计模块。'
            } else if (that.isSpecialBill === 2 && that.isPublic === '1') {
              tips = '会计规定：<span class="color-red">收据不宜使用对公户报销</span><p>再遇收据报销时请使用非对公户转账或现金</p>'
            }
            if (tips) {
              this.$alert(tips, '！！提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '我知道了',
                callback: action => {
                  that.loading = true
                  that.sphdSocket.send(type, approveInfo)
                  console.log(JSON.stringify(approveInfo))
                }
              })
            } else {
              that.loading = true
              that.sphdSocket.send(type, approveInfo)
            }
          }
        } else {
          let isOk = true
          if (type === 'cashierHandleApproval') {
            if (that.approveSta) {
              approveInfo['approvalStatus'] = that.approveSta
            } else {
              that.$kiko_message('请选择审批情况！')
              isOk = false
            }
          }
          if (isOk) {
            that.loading = true
            that.sphdSocket.send(type, approveInfo)
            console.log(JSON.stringify(approveInfo))
          }
        }
      }
    },
    approve2: function (type) {
      let approveInfo = {
        userId: this.sphdSocket.user.userID,
        approvalProcessId: this.approvalProcessList[this.approvalProcessList.length - 1].id,
        approveStatus: this.approveSta,
        session: this.sphdSocket.sessionid
      }
      let that = this
      if (Number(approveInfo.approveStatus) > 0) {
      } else {
        that.$kiko_message('请先选择审批结果')
        return false
      }
      if (type === 'onlineAuditApproval') {
        approveInfo['reason'] = this.reason
        approveInfo['approveSelect'] = this.approveSelect.toString()
        console.log(approveInfo)
        if (approveInfo.approveSelect.length === 0 && Number(approveInfo.approveStatus) === 3) {
          that.$kiko_message('请先选择在线审批未通过的原因')
          return false
        }
        that.sphdSocket.send(type, approveInfo)
        that.loading = true
      } else if (type === 'offlineAuditApproval') {
        console.log(approveInfo)
        that.sphdSocket.send(type, approveInfo)
        that.loading = true
      }
    },
    getIsPublic: function (value) {
      let that = this
      let bankList = this.bankList
      for (let i in bankList) {
        if (bankList[i].value === value) {
          that.isPublic = bankList[i].isPublic
        }
      }
    },
    dialogOpen: function (name) {
      this[name] = true
      if (name === 'twoApproval') {
        let that = this
        that.loading = true
        this.axios.get('../../../reimburseWindow/getAccounts.do?oid=' + this.sphdSocket.user.oid)
          .then(function (response) {
            let data = response.data.data
            let bankList = []
            for (let i in data) {
              let bankItem = {
                label: data[i].bankName + ' ' + data[i].account,
                value: data[i].id,
                isPublic: data[i].isPublic
              }
              bankList.push(bankItem)
            }
            that.bankList = bankList
            that.loading = false
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    certificationType: function (row, column) {
      let type = ''
      switch (row.certificationType) {
        case '0':
          type = '未认证'
          break
        case '1':
          type = '认证通过'
          break
        case '2':
          type = '认证失败'
          break
        case '3':
          type = '无需认证'
          break
        default:
          type = ''
      }
      return type
    }
  },
  watch: {
    approveSelect () {
      console.log(this.approveSelect, this.approveSelect.length)
      let index5 = this.approveSelect.indexOf('5')
      if (this.approveSelect.length > 1 && index5 > -1) {
        this.approveSelect = ['5']
        this.$kiko_message('选择“其他原因”后不可选择其他的原因')
      }
    }
  }
}
</script>
