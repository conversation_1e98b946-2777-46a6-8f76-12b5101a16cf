package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.ExcelUtils;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.material.entity.TSrmSupplierImport;
import cn.sphd.miners.modules.material.entity.TSrmSupplierImportLog;
import cn.sphd.miners.modules.material.service.ITSrmSupplierImportService;
import cn.sphd.miners.modules.material.service.SrmSupplierService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 供应商管理_供应商导入信息
 * 20231108 1.267供应商之批量导入Controller
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Controller
@RequestMapping("/supplier/import")
public class TSrmSupplierImportController {

    @Autowired
    private ITSrmSupplierImportService tSrmSupplierImportService;

    @Autowired
    UploadService uploadService;

    @Autowired
    SrmSupplierService srmSupplierService;

    //获取待处理信息
    @ResponseBody
    @RequestMapping("/getImportData")
    public Map getImportData(User user) {
        Map result = new HashMap();
        //获取无法保存至系统的数据
        TSrmSupplierImport supplierImport = new TSrmSupplierImport();
        supplierImport.setOrg(Long.valueOf(user.getOid()));
        supplierImport.setState(1L);
        supplierImport.setOrg(Long.valueOf(user.getOid()));

        supplierImport.setOperation("4");
        List<TSrmSupplierImport> exImports = tSrmSupplierImportService.selectTSrmSupplierImportList(supplierImport);

        //获取可以导入的数据
        supplierImport.setOperation("3");
        List<TSrmSupplierImport> okImports = tSrmSupplierImportService.selectTSrmSupplierImportList(supplierImport);

        //获取已放弃的数据
        supplierImport.setOperation("2");
        List<TSrmSupplierImport> delImports = tSrmSupplierImportService.selectTSrmSupplierImportList(supplierImport);

        result.put("exImports", exImports);
        result.put("okImports", okImports);
        result.put("delImports", delImports.size());

        return result;

    }

    //放弃/删除无法导入的数据
    @ResponseBody
    @RequestMapping("/delImports")
    public Integer delImports(User user, Long id, Integer operation) {
        if (id == null) {//删除所有无法导入的数据
            if (operation == 2) {
                TSrmSupplierImport supplierImport = new TSrmSupplierImport();
                supplierImport.setOrg(Long.valueOf(user.getOid()));
                supplierImport.setState(1L);
                supplierImport.setOperation("4");
                List<TSrmSupplierImport> exImports = tSrmSupplierImportService.selectTSrmSupplierImportList(supplierImport);
                if (exImports != null) {
                    for (TSrmSupplierImport exImport : exImports) {
                        exImport.setOperation("2");
                        tSrmSupplierImportService.updateTSrmSupplierImport(exImport);
                    }
                }

                TSrmSupplierImport supplierImport1 = new TSrmSupplierImport();
                supplierImport1.setOrg(Long.valueOf(user.getOid()));
                supplierImport1.setState(1L);
                List<TSrmSupplierImport> exImports2 = tSrmSupplierImportService.selectTSrmSupplierImportList(supplierImport1);
                if(exImports2!=null && !exImports2.isEmpty()){
                    //如果exImports2中所有operation状态都为2时 删除所有数据
                    int count = 0;
                    for (TSrmSupplierImport tSrmSupplierImport : exImports2) {
                        if("2".equals(tSrmSupplierImport.getOperation())){
                            count+=1;
                        }
                    }
                    if(count== exImports2.size()){
                        for (TSrmSupplierImport exImport : exImports2) {
                            tSrmSupplierImportService.deleteTSrmSupplierImportById(exImport.getId());
                        }
                    }
                }


            } else if (operation == 3) {
                TSrmSupplierImport supplierImport = new TSrmSupplierImport();
                supplierImport.setOrg(Long.valueOf(user.getOid()));
                List<TSrmSupplierImport> exImports = tSrmSupplierImportService.selectTSrmSupplierImportList(supplierImport);

                for (TSrmSupplierImport exImport : exImports) {
                    tSrmSupplierImportService.deleteTSrmSupplierImportById(exImport.getId());
                }
            }

        } else {
            TSrmSupplierImport srmSupplierImport = tSrmSupplierImportService.selectTSrmSupplierImportById(id);
            if (srmSupplierImport != null) {
                srmSupplierImport.setOperation("2");
                tSrmSupplierImportService.updateTSrmSupplierImport(srmSupplierImport);
            }
        }


        return 1;
    }

    @ResponseBody
    @RequestMapping("/editImports")
    public Map editImports(TSrmSupplierImport supplierImport, User user) {
        Map m = new HashMap();
//        if(StringUtils.isBlank(supplierImport.getCodeName()))
//            supplierImport.setCodeName(tSrmSupplierImportService.selectTSrmSupplierImportById(supplierImport.getId()).getCodeName());
        //如果传了codeName 需要查重
        if (StringUtils.isNotBlank(supplierImport.getCodeName())) {
            SrmSupplier srmSupplier = srmSupplierService.getSrmSupplierByCode(supplierImport.getCodeName(), user.getOid());
            if (srmSupplier != null) {
                m.put("code", 2);
                m.put("name", supplierImport.getName());
                return m;
            }
            TSrmSupplierImport tSrmSupplierImport = new TSrmSupplierImport();

            tSrmSupplierImport.setCodeName(supplierImport.getCodeName());

            List<TSrmSupplierImport> importS = tSrmSupplierImportService.selectTSrmSupplierImportList(tSrmSupplierImport);
            if (importS != null && !importS.isEmpty()) {
                m.put("code", 3);
                m.put("name", importS.get(0).getName());
                return m;
            }

        }
        //当前数据查重
        TSrmSupplierImport si = tSrmSupplierImportService.selectTSrmSupplierImportById(supplierImport.getId());
        TSrmSupplierImport std = new TSrmSupplierImport();

        std.setCodeName(supplierImport.getCodeName());
        List<TSrmSupplierImport> importS = tSrmSupplierImportService.selectTSrmSupplierImportList(std);
        if (importS != null && !importS.isEmpty()) {
            for (TSrmSupplierImport anImport : importS) {
                if(anImport.getCodeName().equals(si.getCodeName())&& !Objects.equals(anImport.getId(), si.getId())){
                    m.put("code", 3);
                    m.put("name", anImport.getName());
                    return m;
                }
            }
        }

        TSrmSupplierImport srmSupplierImport = tSrmSupplierImportService.selectTSrmSupplierImportById(supplierImport.getId());
        srmSupplierImport.setFullName(supplierImport.getFullName());
        srmSupplierImport.setName(supplierImport.getName());
        srmSupplierImport.setCodeName(supplierImport.getCodeName());

        if ("4".equals(srmSupplierImport.getOperation())) {
            srmSupplierImport.setOperation("3");
        }

        tSrmSupplierImportService.updateTSrmSupplierImport(srmSupplierImport);
        m.put("code", 1);
        return m;
    }

    @ResponseBody
    @RequestMapping("importSave")
    public Integer importSave(@RequestBody List<TSrmSupplierImport> supplierImports, User user) {
        for (TSrmSupplierImport supplierImport : supplierImports) {
            supplierImport.setState(3L);
            tSrmSupplierImportService.updateTSrmSupplierImport(supplierImport);

            SrmSupplier supplier = new SrmSupplier();
            BeanUtils.copyProperties(supplierImport, supplier);

            supplier.setCreateDate(new Date());
            supplier.setCreator(user.getUserID());
            supplier.setCreateName(user.getUserName());
            supplier.setOrg(user.getOid());
            supplier.setType(1);
            supplier.setEnabled(1);
            srmSupplierService.insertSupplier(supplier);
        }
        TSrmSupplierImport tSrmSupplierImport = new TSrmSupplierImport();
        tSrmSupplierImport.setOrg(Long.valueOf(user.getOid()));
        List<TSrmSupplierImport> srmSupplierImports = tSrmSupplierImportService.selectTSrmSupplierImportList(tSrmSupplierImport);
        //如果不为空则全部删除
        if (srmSupplierImports != null && !srmSupplierImports.isEmpty()) {
            for (TSrmSupplierImport srmSupplierImport : srmSupplierImports) {
                tSrmSupplierImportService.deleteTSrmSupplierImportById(srmSupplierImport.getId());
            }
        }
        return 1;
    }

    //导入
    @RequestMapping("/import")
    @ResponseBody
    public Map importSupplier(User user, String filePath) throws IOException {
        Map result = new HashMap();

        File f = uploadService.copyTempFile(filePath);
        if (f.length() > 10485760) {
            result.put("code", "2");
            result.put("data", null);
            return result;
        }
        LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();


        fileMap.put("供应商名称", "fullName");
        fileMap.put("简称", "name");
        fileMap.put("代号", "codeName");
        try {
            List<TSrmSupplierImport> srmSupplierImports = ExcelUtils.excelToList(new FileInputStream(f), "供应商清单", TSrmSupplierImport.class, fileMap, new String[]{"供应商名称", "简称", "代号"});

            List<TSrmSupplierImport> norepeats = new ArrayList<>();
            List<TSrmSupplierImport> repeats = new ArrayList<>();
            for (TSrmSupplierImport srmSupplierImport : srmSupplierImports) {
                if(StringUtils.isBlank(srmSupplierImport.getName())){
                    if(srmSupplierImport.getFullName().length()>5)
                        srmSupplierImport.setName(srmSupplierImport.getFullName().substring(0,5));
                }
                if (!norepeats.contains(srmSupplierImport)) {

                    SrmSupplier srmSupplier = null;

                    if (StringUtils.isNotBlank(srmSupplierImport.getCodeName()))
                        srmSupplier = srmSupplierService.getSrmSupplierByCode(srmSupplierImport.getCodeName(), user.getOid());
                    TSrmSupplierImport s = new TSrmSupplierImport();
                    s.setCodeName(srmSupplierImport.getCodeName());
                    s.setState(1L);
                    s.setOrg(Long.valueOf(user.getOid()));
                    List<TSrmSupplierImport> srmSupplierImports1 = tSrmSupplierImportService.selectTSrmSupplierImportList(s);
                    if (srmSupplier != null || (!srmSupplierImports1.isEmpty())) {
                        if (!repeats.contains(srmSupplierImport)) {
                            srmSupplierImport.setOperation("4");
                            repeats.add(srmSupplierImport);
                        }else {
                            srmSupplierImport.setOperation("3");
                            norepeats.add(srmSupplierImport);
                        }
                    } else if ((StringUtils.isBlank(srmSupplierImport.getName())) || StringUtils.isBlank(srmSupplierImport.getFullName()) || StringUtils.isBlank(srmSupplierImport.getCodeName())) {
                        if (!repeats.contains(srmSupplierImport)) {
                            srmSupplierImport.setOperation("4");
                            repeats.add(srmSupplierImport);

                        }
                    } else {
                        srmSupplierImport.setOperation("3");
                        norepeats.add(srmSupplierImport);
                    }

                } else {//列入导入失败
                    srmSupplierImport.setOperation("4");
                    repeats.add(srmSupplierImport);
                }

                srmSupplierImport.setOrg(Long.valueOf(user.getOid()));
                srmSupplierImport.setCreator(Long.valueOf(user.getUserID()));
                srmSupplierImport.setCreateName(user.getUserName());
                srmSupplierImport.setState(1L);
                tSrmSupplierImportService.insertTSrmSupplierImport(srmSupplierImport);
            }


        } catch (ExcelUtils.ExcelException e) {
            result.put("code", "2");
            result.put("data", null);
            return result;
        }
        result.put("code", "3");
        result.put("data", null);
        return result;
    }

    /**
     * 查询供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TSrmSupplierImport tSrmSupplierImport) {
        List<TSrmSupplierImport> list = tSrmSupplierImportService.selectTSrmSupplierImportList(tSrmSupplierImport);
        return list;
    }


    /**
     * 新增保存供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TSrmSupplierImport tSrmSupplierImport) {
        return toAjax(tSrmSupplierImportService.insertTSrmSupplierImport(tSrmSupplierImport));
    }


    /**
     * 修改保存供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TSrmSupplierImport tSrmSupplierImport) {
        return toAjax(tSrmSupplierImportService.updateTSrmSupplierImport(tSrmSupplierImport));
    }

    /**
     * 删除供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tSrmSupplierImportService.deleteTSrmSupplierImportByIds(ids));
    }
}
