<template>
  <div id="financeModify">
    <TY_NavTop title="财务修改" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">您有如下财务修改申请有待审批：</div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toHandle" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.description}}</div>
              <div class="right">{{item.askName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'financeModify',
  data () {
    return {
      toHandle: [],
      listenersUid: []
    }
  },
  created () {
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('updateApplyHandle', function (data) {
        let content = JSON.parse(data)
        that.toHandle = content.approvalProcessList
        console.log('updateApplyHandle session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('updateApplyHandle', function (data) {
        console.log('updateApplyHandle user Socket Error:')
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.toHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toHandle.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    console.log(userId)
    console.log(session)
    this.sphdSocket.send('updateApplyHandle', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      parent.location.href = '../../../update/toUpdatePage.do?id=' + id
    },
    search: function () {
      this.$router.push({
        path: `/financeChangeQuery/apply`
      })
    }
  }
}
</script>
