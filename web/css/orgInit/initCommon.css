.ty-page-header{
    display: flex;
    line-height: 24px;
    padding: 0 0 0 70px;
    color: #5d9cec;
    margin-top: 16px;
}
.page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
}
.page-header__left::after {
    content: "";
    position: absolute;
    width: 1px;
    height: 16px;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #dcdfe6;
}
.page-header__left .icon-back {
    font-size: 18px;
    margin-right: 6px;
    align-self: center;
    position: relative;
    top: 1px;
}
.page{
    display: none;
    padding: 8px 0;
    max-width: 1000px;
    position: relative;
    margin: 0 70px;
}
.panel-box{
    border-top: 1px solid #ddd;
    padding-top: 8px;
}
.panel-box:first-child{
    border: none
}
.panel-box_item{
    width: 100%;
    padding: 8px 0;
    box-sizing: border-box;
    border-radius: 2px;
    position: relative;
    font-size: 14px;
    color: #3f4144;
    overflow: hidden;
    opacity: 1;
    display: flex;
    align-items: center;
    transition: opacity .2s;
    margin-bottom: 8px;
}
.left_item{
    width: 500px;
    flex: none;
}
.left_item .link-blue{
    margin-left: 16px;
}
.right_item{
    flex: auto;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
}
.right_btn_item{
    flex: auto;
    text-align: right;
}
.panel-box-title{
    font-size: 15px;
    font-weight: bold;
    color: #4a4c4f;
    padding: 8px 0;
    margin-bottom: 8px;
}
.topRightBtn{
    position: absolute;
    top: -28px;
    right: 0;
}
.ty-alert .btn-group{
    flex: auto;
    text-align: right;
}
.bonceContainer .mainCon{
    width: 95%;
    margin: 8px auto;
}

.cItem > div.file_avatar{
    border: 1px solid #dcdfe6;
    min-height: 30px;
    border-radius: 2px;
    background: #fff;
    padding: 4px 8px;
}
.file_avatar .fileItem{
    line-height: 1.2;
    border-radius: 2px;
    padding: 4px;
    display: flex;
    align-items: baseline;
    color: #2e465c;
}
.file_avatar .fileItem .fileName{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 320px;
    margin: 0 8px;
    font-size: 12px;
    text-decoration: underline;
    color: #5d9cec;
    cursor: pointer;
}
.file_avatar .fileItem .fa-times-circle{
    cursor: pointer;
    display: none;
}
.file_avatar .fileItem:hover .fa-times-circle{
    display: inline;
}
.file_avatar .fileItem .fa-times-circle:hover{
    color: #5a6368;
}
.insertItem td{
    background: #fbfbfb;
}
.insertItem td:first-child{
    padding-left: 32px;
}