<template>
	<div class="uni-container">
		<div class="wating icon-manage1">  </div>
			<div class="watTxt">敬请期待</div>

	</div>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
		onLoad() {

		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>

<style lang="scss">
	.icon-manage1{ background: url("../../../../../liveMiniApp/src/assets/img/tabbar/manage1.png");   background-size: 200px; }

	
</style>
