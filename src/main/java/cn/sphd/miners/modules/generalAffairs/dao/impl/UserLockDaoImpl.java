package cn.sphd.miners.modules.generalAffairs.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.generalAffairs.dao.*;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * @ClassName SysUserLockDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/28 8:59
 * @Version 1.0
 */

@Repository
public class UserLockDaoImpl extends BaseDao<SysUserLock,Serializable> implements UserLockDao {

}