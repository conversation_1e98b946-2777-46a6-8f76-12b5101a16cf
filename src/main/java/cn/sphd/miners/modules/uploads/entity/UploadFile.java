package cn.sphd.miners.modules.uploads.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * 上传文件实体类
 * <AUTHOR>
 * @since 2021/2/4 12:04
 */
@Entity
@Table(name ="t_upload_file")
public class UploadFile  extends BaseEntity {
	@Id
	@Column
	private String filename;

   	@Column
	@JsonIgnore@JSONField(serialize = false)
	private Integer org;

	@Column(name = "acc_id")
	@JsonIgnore@JSONField(serialize = false)
   	private Long accId;

   	@Column(name = "real_path")
	@JsonIgnore@JSONField(serialize = false)
	private String realPath;

   	@Column(name = "display_name")
	private String displayName;

   	@Column(name="original_filename")
	private String originalFilename;

   	@Column(name="last_name")
	private String lastName;

   	@Column
	private Long size;

   	@Column(length = 128)
	private String hash;//SHA512 of file

   	@Column(length = 50)
	private String module;

   	@Column(name = "user_id")
	@JsonIgnore@JSONField(serialize = false)
	private Integer userId;

   	@Column(name = "user_name",length = 100)
	private String userName;

   	@Column(name = "create_date")
	@CreationTimestamp
	private Date createDate;

//	@Column(name = "using_count", length = 32)
//	@JsonIgnore@JSONField(serialize = false)
//	private Integer usingCount;

	@Column(name = "group_uuid", length = 32)
	private String groupUuid;

	@Transient
	private Boolean uploaded;
   	@Transient
	private String fileUid;
	@Transient
	private String originalRelativePath;
	@Transient
	private String relativePath;
	@Transient
	private String sizeH;
	@Transient
	private String token;
	@Transient
	private Long minMissIndex;

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Long getAccId() {
		return accId;
	}

	public void setAccId(Long accId) {
		this.accId = accId;
	}

	public String getRealPath() {
		return realPath;
	}

	public void setRealPath(String realPath) {
		this.realPath = realPath;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getOriginalFilename() {
		return originalFilename;
	}

	public void setOriginalFilename(String originalFilename) {
		this.originalFilename = originalFilename;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Long getSize() {
		return size;
	}

	public void setSize(Long size) {
		this.size = size;
	}

	public String getHash() {
		return hash;
	}

	public void setHash(String hash) {
		this.hash = hash;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Boolean getUploaded() {
		return uploaded;
	}

//	public Integer getUsingCount() {
//		return usingCount==null?0:usingCount;
//	}
//
//	public void setUsingCount(Integer usingCount) {
//		this.usingCount = usingCount==null||usingCount<0?0:usingCount;
//	}

	public String getGroupUuid() {
		return groupUuid;
	}

	public void setGroupUuid(String groupUuid) {
		this.groupUuid = groupUuid;
	}

	public void setUploaded(Boolean uploaded) {
		this.uploaded = uploaded;
	}

	public String getFileUid() {
		return fileUid;
	}

	public void setFileUid(String fileUid) {
		this.fileUid = fileUid;
	}

	public String getOriginalRelativePath() {
		return originalRelativePath;
	}

	public void setOriginalRelativePath(String originalRelativePath) {
		this.originalRelativePath = originalRelativePath;
	}

	public String getRelativePath() {
		return relativePath;
	}

	public void setRelativePath(String relativePath) {
		this.relativePath = relativePath;
	}

	public String getSizeH() {
		return sizeH;
	}

	public void setSizeH(String sizeH) {
		this.sizeH = sizeH;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public Long getMinMissIndex() {
		return minMissIndex;
	}

	public void setMinMissIndex(Long minMissIndex) {
		this.minMissIndex = minMissIndex;
	}
}