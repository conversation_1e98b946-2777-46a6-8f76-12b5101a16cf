<template>
	<div class="officeListMy">
		<Pageheader title="主播工作室列表" :showBack="true"/>

    <div class="live-panel live-tr" v-for="(item , index) in list" :key="index">
      <div @click="goOffice(item)">
        <div>
          <el-icon>
            <video-camera color="#53B5A8" class="houseSty" />
          </el-icon>
          {{item.name}}

          <el-icon class="live-right relaRight" v-if="org.id === item.id">
            <check color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
    </div>
	</div>
</template>

<script setup>
  import { ElMessage } from 'element-plus'
  import { useRouter } from "vue-router";
  import auth from '@/sys/auth'
  import { sureLogin  } from '@/api/api.js'
  import JSONBig from 'json-bigint'

  const router = useRouter();


  let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  console.log(' let jsonData = ', jsonData)
  const org =  auth.getOrg()
  const user = auth.getUser()
  const list = jsonData.officeList

  const goOffice = (officeItem) => {
    console.log('officeItem', officeItem)
    if(org.id === officeItem.id){
      router.replace({name: 'homeMy'});
      return false
    }
    let userInfo = auth.getUser()
    let jsonDataStr = localStorage.getItem('jsonData')
    let localJsonData = ''
    if(jsonDataStr && jsonDataStr.length > 5){
      localJsonData = JSONBig.parse(jsonDataStr)
    }else{
      localJsonData = {}
    }
    if(officeItem.code === "liveHelper"){
      // 用户登陆
      console.log('officeItem', officeItem)
      sureLogin({"oid": officeItem.id}).then((res)=>{
        localJsonData.officeItem = officeItem
        localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
        console.log('进机构存的数据', localJsonData)
        if(userInfo.roleCode == 'staff'){
          router.replace({name: 'homeManage'});
        }else{
          router.replace({name: 'homeData'});
        }
      })
    }else{
      ElMessage.error('抱歉，抖音短暂不支持该类机构的登陆！');
    }

  }


</script>

<style lang="scss" scoped>
.houseSty{
  position: relative;
  top:4px;
}
.houseSty8{
  position: relative;
  top:8px;
}
.live-panel{
  margin-top: 10px;
}
</style>
