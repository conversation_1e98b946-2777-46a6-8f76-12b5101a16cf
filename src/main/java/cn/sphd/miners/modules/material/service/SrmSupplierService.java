package cn.sphd.miners.modules.material.service;



import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/9/20.
 */
public interface SrmSupplierService {

    List<SrmSupplier> getAll();

    List<SrmSupplier> getByOid(Integer oid, int i, PageInfo pageInfo, Object o, Integer type);

    List<SrmSupplier> searchByOid(Integer oid,Integer enabled, PageInfo pageInfo,String keyword, Integer type);
    //添加供应商
    String addSupplier(SrmSupplier srmSupplier, Map<String,Object> param, User user);

    SrmSupplier getById(Integer supplierId);

    List<SrmSupplierContact> getBySupplierId(Integer supplierId);
    //暂停 或恢复
    String recoveryOrStopSrmSupplier(Integer supplierId,Integer enabled,User user);
    void updateSrmSupplier(SrmSupplier mtSupplier);
    //修改基本信息
    void updateSrmSupplierBase(SrmSupplier mtSupplier,User user);
    //获取基本信息修改记录列表
    List<Map<String,Object>> getRecordBaseList(Integer supplierId);
    //获取基本信息修改详情
    Map<String,Object> getRecordBaseDetails(Integer id,Integer frontId);
    //获取开票信息修改记录
    List<Map<String,Object>> getRecordInvoiceList(Integer supplierId);
    //货期开票信息修改详情
    Map<String,Object> getRecordInvoiceDetails(Integer id,Integer frontId);
    void deleteSrmSupplierContact(SrmSupplierContact mtSupplierContact);

    SrmSupplierContact getSrmSupplierContactById(Integer id);

    void addSrmSupplierContact(SrmSupplierContact mtSupplierContact);

    void updateSrmSupplierContact(SrmSupplierContact mtSupplierContact);

    SrmSupplier getSrmSupplier(Integer id);

    SrmSupplier getSrmSupplierByName(Integer oid, String name);

    void saveMtSupplierMaterial(MtSupplierMaterial supplierMaterial);

    void deleteSrmSupplier(Integer id);

    List<SrmSupplier> getSrmSupplierLikeName(Integer oid, String name);

    //供应商信息查看
    SrmSupplier getSrmSupplierOne(Integer id);
    //获取地址详情
    Map<String,Object> getAddress(Integer addressId);
    //获取开票信息
    SrmSupplierInvoice getSrmSupplierInvoice(Integer supplierId);
    //修改开票信息
    String updateSupplierInvoice(SrmSupplier supplier,User user);
    //获取联系人列表
    List<Map<String,Object>> getContactsList(Integer supplierId,Integer enabled);
    //添加收货地址
    String addCustomerAddress(User user, Integer supplierId,String address) ;
    //修改收获地址
    String updateSupplierAddress(User user, SrmSupplierPost post);
    //获取所有地址
    List<Map<String, Object>> getAllAddress(Integer supplierId,String enabled);
    //删除联系人
    String deleteCustomerContact(Integer contactId,User user);
    //获取联系人社交号
    Map<String,Object> getContactsSocial(Integer contactId);
    //修改联系人社交号，与名片,联系人基本信息
    String updateContactsSocialAndCard(User user, Integer contactId, HttpServletRequest request);
    //客户管理>新增客户联系人
    Map addCustomerContact(User user,Integer supplierId,HttpServletRequest request) ;
    //获取联系人修改记录列表
    List<Map<String,Object>> getRecordContactList(Integer contactId);
    //获取联系修改记录详情
    Map<String,Object> getRecordContactDetails(Integer id,Integer frontId);
    //获取收货地址修改记录列表
    List<Map<String,Object>> getRecordAddressList(Integer addressId);
    //获取修改记录地址详细信息
    Map<String,Object> getRecordShAddressDetails(Integer id,Integer frontId);
    //停用或启用地址
    String startOrStopAddress(Integer addressId,User user,int enabled);
    //获取到期的合同
    List<PoContractBase> getEndList();
    //批量修改合同
    void batchUpdateSlContractBase(  List<PoContractBase> list);
    //根据材料id 获取供应商基本信息
    List<Map<String,Object>> getSupplierByMt(Integer mtId);

    //获取供应商暂停启用记录
    List<Map<String,Object>> getStartAndStopList(Integer supplierId);

    List<SrmSupplierInvoice> getSrmSuppliers(Integer id);

    JsonResult addSupplierInvoice(SrmSupplierInvoice invoice, User user);

    JsonResult deleteSupplierInvoice(SrmSupplierInvoice invoice, User user);

    JsonResult materialAddress(Integer id);

    List<MtSupplierMaterial> getSupplierMaterialsByState(Integer id,Integer enabled);

    List<MtSupplierMaterial> getSupplierMaterialsByMaterial(Integer material);

    String removeAddress(User user, Integer addressId);

    SrmSupplier getSrmSupplierByCode(String codeName, Integer oid);

    void insertSupplier(SrmSupplier supplier);

    List<SrmSupplier> getSupplierByEnabled(Integer org, int i);
}
