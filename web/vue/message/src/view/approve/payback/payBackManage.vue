<template>
  <div id="payList">
    <TY_NavTop title="回款处置" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tipPane">您需确认如下{{rowCount}}笔回款分别属于哪些订单。</div>
      <div class="ui-cells">
        <a class="ui-cell" v-for="(item, index) in dealList" v-on:click="applyTo(item.id)" :key="index">
          <div class="backApplyLists">
            <p class="company">{{item.customerName}}</p>
            <p class="listCon"> 本次回款 {{item.method | incomeType}} {{item.amount}}元</p>
            <p class="listCon">{{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<style>
</style>

<script>
import { incomeType, formatDate } from '../../../js/common'
export default {
  name: 'payManage',
  filters: {
    incomeType,
    formatDate
  },
  data () {
    return {
      rowCount: 0,
      dealList: [],
      loading: true,
      listenersUid: []
    }
  },
  created () {
    let _this = this
    let userid = _this.sphdSocket.user.userID
    _this.listenersUid = [
      _this.sphdSocket.subscribe('collectDisposalList', function (data) {
        _this.loading = false
        let listData = JSON.parse(data)
        _this.rowCount = listData.num
        _this.dealList = listData.list
      }),
      _this.sphdSocket.subscribe('collectDisposalList', function (data) {
        let resData = JSON.parse(data)
        let update = resData.personnelReimburse
        if (resData.operate > 0) {
          _this.dealList.push(update)
        } else if (resData.operate < 0) {
          _this.dealList.forEach(function (item, index) {
            let deletId = update.id
            if (deletId === item.id) {
              _this.dealList.splice(index, 1)
            }
          })
        }
        _this.rowCount = _this.dealList.length
      }, function () {
        console.log('Socket check Error:')
      }, 'user'),
      _this.sphdSocket.subscribe('updateDisposal', function (data) {
        let listData = JSON.parse(data)
        _this.rowCount = listData.num
        _this.dealList = listData.list
      }, function () {
        console.log('Socket check Error:')
      }, 'user', userid)
    ]
    _this.sphdSocket.send('collectDisposalList', {
      'oid': this.sphdSocket.user.oid,
      'session': this.sphdSocket.sessionid,
      'userId': userid
    })
  },
  destroyed: function () {
    let _this = this
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    applyTo: function (id) {
      window.parent.floatToPage('../../../salesBack/cashBkManage.do', { 'id': id })
    },
    screen: function () {
      this.$router.push({
        path: '/payApproveScreen'
      })
    }
  }
}
</script>
