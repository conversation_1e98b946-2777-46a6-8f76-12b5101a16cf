<template>
  <div class="mine">
    <div class="my-flex-panel my-flex-item">
      <div class="img">
        <img v-if="userImg" alt="11" class="image" :src="userImg || userImgErr" :onerror="imgOnError"/>
        <img v-else alt="22"  class="image" :src="userImgErr" :onerror="userImgErr"/>
      </div>
      <div class="user">
        <div class="officeName">{{ (user&& (user.name)) || '未知名' }} 的领地</div>
        <div class="userInfo">
          <div>
            <span class="txt">账号：{{ (user && user.mobile) || '未知账号' }}</span>
            <el-icon class="live-right relaRight">
              <arrow-right-bold color="#53B5A8" class="houseSty8" />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="ty-panel">
        <div class="ty-p" @click="goBackOrg">
          <i class="icon icon-return"></i> 返回机构
          <i class="fa fa-angle-right"></i>
        </div>
      </div>
      <div class="ty-panel">
        <div class="ty-p" @click="tip">
          <i class="icon icon-cancellation"></i> 解绑退出
          <i class="fa fa-angle-right"></i>
        </div>
        <div class="ty-p" @click="tip">
          <i class="icon icon-cancellation"></i> 退出
          <i class="fa fa-angle-right"></i>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import userImgErr from "@/assets/commonImg/userImgErr.png"
import { sureLogin} from "@/api/api";
import sphdSocket from '@/sys/sphd'
import auth from '@/sys/auth'
import JSONBig from 'json-bigint'


export default {
  data() {
    return {
      activeIndex: 4,
      userImgErr: userImgErr,
      userImg: '',
    }
  },
  props:{
    userInfo: {
      type: Object,
      default: ''
    },
    fileUrl: {
      type: String,
      default: ''
    },
  },
  mounted() {
    let scopeThis = this
    let user2= auth.getAcc()

    if(user2){
      this.user = user2
      console.log('this.user=', this.user)
      let path = user2.avatar
      console.log('直接读取的')
      let webrootUrl = auth.webRoot
      this.userImg = (path && (webrootUrl + '/upload/' + path)) || ''
      console.log('this.userImg=', this.userImg)
    }
    // else{
    //   console.log('需要从接口获取的')
    //   scopeThis.uTimer = setInterval(function () {
    //     console.log('这里循环')
    //     if(scopeThis.user){
    //       clearInterval( scopeThis.uTimer )
    //     }else{
    //       scopeThis.user = scopeThis.userInfo
    //     }
    //   },100)
    //
    // }


  },
  methods: {
    tip(){
      this.$message.info('后续功能敬请期待！')
    },
    imgOnError(e){
      let img = e.srcElement;
      img.src = userImgErr
      img.onerror = null; //防止闪图
    },
    async goBackOrg(){
      let orgStr = localStorage.getItem('orgInfo')
      if(orgStr){
        let orgInfo = JSON.parse(orgStr)
        let params = {
          userID: orgInfo.userID,
          oid: orgInfo.id ,
        }
        let slRes = await sureLogin(params)
        //更新sphdSocket中的对象，用于兼容message小窗的sphdSocket.user .org代码
        sphdSocket.reflushAuth()
        let slData = slRes.data
        // console.log('sureLogin', slData)
        if (slData.error != undefined) {
          this.$alert(slData.error, '提示', {
            confirmButtonText: '确定',
          })
        }else{
          this.$router.push('homeMy')
        }
      }else{
        this.$message.error('操作失败！因为您未属于任何机构！')
      }

    },
  }
}

</script>

<style lang="scss" scoped>
$imgWid: 80px;
$imgWid2: 40px;
.mine{
  .houseSty8{
    position: relative;
    top:5px;
  }
  .my-flex-panel{
    display: flex; padding:20px; background-color: #fafafa; margin-bottom: 20px;
    .img{
      flex: 1; display: inline-block; overflow: hidden;  height: 104px;
      img{ background-color: #eee; border-radius: $imgWid2; width:$imgWid; height:$imgWid; position: relative; top: 20px; }
    }
    .user{
      flex: 3;
      padding-left: 15px;
      .officeName{ font-size: 1.1em; line-height: 50px; color: $tyColorGreen ; font-weight:bold;  }
      .userInfo{ line-height:30px; color: #555; font-size: 0.9em; }
    }
  }
  .icon-return{ background-image: url("@/assets/wonderss/return.png"); background-size: 16px!important; }
  .icon-cancellation{ background-image: url("@/assets/wonderss/cancellation.png"); background-size: 16px!important;}
  .icon{ width: 20px; height:20px; background-color: #fafafa; background-repeat: no-repeat; background-size: 100%; display: inline-block; position: relative; top:6px; margin-right:0px; }

  .ty-panel{
    background-color: #fafafa; line-height: 40px;margin:20px 0;
    padding:4px 20px;
    .ty-p:not(:last-child){ border-bottom: 1px solid #f8f8f8; }
    .fa-angle-right{ float: right;font-size: 25px; color: #ddd; margin-top: 6px;  }
  }

}


	
</style>
