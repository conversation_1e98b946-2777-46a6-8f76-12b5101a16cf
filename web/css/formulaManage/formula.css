.areaOne {margin-bottom: 10px;}
.areaOne>div {margin-right: 10px;}
.col-b {margin-left: 50px;}
.col-c {text-align: right;}
.col-c .col-btn {margin-left: 10px;  display: inline-block;  width: 50px;}
.singepannel{ padding:15px 15px 15px 20px;  }
.searchSect{overflow: hidden;}
.keywordSearch input,.bonceCon .keywordSearch input {  padding: 0 10px;  font-size: 12px;  min-width: 200px;  height: 26px;  line-height: 26px;  }
thead{ background: #f7e5baa6;  }
.tryBtn{margin-left: 100px;}
.btnDo{font-weight: bold; cursor: pointer;}
.narrowWrap{width: 70%;}
.mainCon2,.mainCon3,.mainCon4,.mainCon5,.mainCon6,.mainCon7,.mainCon8{ display: none;  }
.backPre{padding: 0 0 10px 0;}
#mtEntry, #mtScan { width:800px;  }
.tip{ color: #0b94ea; font-size: 0.8em;  }
.case3 .fa, .case2 .fa{ margin:0 15px 0 40px;color: #0b94ea;  }
.item{ margin-bottom: 10px; }
.item-title{ display:inline-block; width:75px; text-align:center;}
.red{ color:red; }
.gapT{margin: 10px 15px;}
.gapIn{margin: 16px 0;}
.ty-weight{  font-weight: bold;}
.blueTip{line-height: inherit;color: #5d9cec;font-size: 12px;}
.bonceContainer .bonceCon table.bd-none input:not(.long){border: none; text-align: center}
input.long{width: 100%;}
#selectFS{  margin-left: 75px;}
#selecGS, #selecGN{ display: none; position: absolute; background:#fff;  width:192px; max-height:200px; overflow-y:auto; border-radius:4px; border:1px solid #ccc; border-top:none;   }
#selecGS option, #selecGN option{ border-bottom:1px solid #eee; background:#f9f8f8;    }
#selecGS option:hover, #selecGS option.active, #selecGN option:hover, #selecGN option.active, #selectFS option.active{ background:#ddd; cursor: default;    }
.space{padding: 12px;}
.isCurrentStock .dots{padding: 0 16px;}
.isCurrentStock .dots span{margin: 0 16px;}
.clearStr{padding: 2px 4px;font-style: normal;}
.textMax{text-align: right;}
.amountPos{position: relative;}
.amountPos input{text-align: center;}
.amountPos i{position: absolute;right: 36px;  top: 4px;  color: #333;}
.addUnitBtn {padding:0 5px;font-weight:bold; color:#898989;}
.addUnitActive{ color:#0b94ea;}
.butCol{position: relative}
#selectFS{ z-index: 1;display: none; position: absolute; padding: 0 8px; background: #e1eef9;  width:162px; max-height:200px; overflow-y:auto; border-radius:4px; border:1px solid #a5cbcc; border-top:none;   }
#selectFS option{ padding-top: 6px; border-bottom:1px solid #bef9f0;    }
#entryMainMatBtn,#entryFormulaMatBtn{background-color: rgba(255, 255, 255, 0);border: none;color: #5d9cec;}
#entryMainMatBtn[disabled],#entryFormulaMatBtn[disabled]{color: #818181;}
.clear:before, .clear:after {  display: block;  clear: both;  content: "";  visibility: hidden;  height: 0  }





