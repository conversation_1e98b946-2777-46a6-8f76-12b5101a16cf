<template>
  <div class="messageContainer" id="bases">
    <div class="messageHead clearfix">
      <div class="user_logo-wrapper">
        <img class="user_logo" :src="c_logo">
      </div>
      <div class="user_info">
        <div class="user_name">{{user.userName}}</div>
        <div class="user_phone">{{user.mobile}}</div>
      </div>
      <div class="navigation_min" v-on:click="minFloating"><i class="min"></i></div>
    </div>
    <div class="messageCon">
      <div class="tabs">
        <div class="tabs-bar">
          <router-link :to="{name: 'homeApply'}" tag="div">
            <TY_TabBar label="我的申请" :msgCount="$store.state.menu[1].corner" :bool="$store.getters.getHomeNav === 1">
            </TY_TabBar>
          </router-link>
          <router-link :to="{name: 'homeApprove'}" tag="div">
            <TY_TabBar label="处理/审批" :msgCount="$store.state.menu[2].corner" :bool="$store.getters.getHomeNav === 2">
            </TY_TabBar>
          </router-link>
          <router-link :to="{name: 'myMessage'}" tag="div">
            <TY_TabBar label="我的消息" :msgCount="$store.state.menu[3].corner" :bool="$store.getters.getHomeNav === 3">
            </TY_TabBar>
          </router-link>
        </div>
        <div class="tabs-content">
          <router-view></router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  #bases .tabs-tab{
    width: 33%;
  }
  #bases .tabs-content{
    height: calc(100vh - 146px);
  }
  #bases .navigation_min{
    position: absolute;
    top: 0;
    display: block;
    width: 32px;
    height: 48px;
    cursor: pointer;
    line-height: 48px;
    right: 32px;
    text-align: center;
  }
  #bases .navigation_min i{
    display: inline-block;
    width: 12px;
    height: 1px;
    background: #fff;
    line-height: 0;
    font-size: 0;
    vertical-align: middle;
  }
</style>

<script>
import defaultLogo from '../images/avatar_default.png'
import male from '../images/avatar_male.png'
import female from '../images/avatar_female.png'
export default {
  name: 'home',
  data () {
    return {
      user: this.sphdSocket.user
    }
  },
  computed: {
    c_logo: function () {
      let imgPath = this.sphdSocket.user.imgPath
      console.log("imgpath" + imgPath)
      let logoPath = ''
      if (imgPath) {
        logoPath = window.parent.$.fileUrl + this.sphdSocket.user.imgPath
      } else {
        let gender = this.sphdSocket.user.gender
        console.log("gender", gender)
        if (gender) {
          if (gender === '1') {
            logoPath = male
          } else if (gender === '0') {
            logoPath = female
          } else {
            logoPath = defaultLogo
          }
        } else {
          logoPath = defaultLogo
        }
      }
      console.log("logoPath", logoPath)
      return logoPath
    }
  },
  created () {
    // window.localStorage.setItem('floating', '')
    // console.log(this.sphdSocket)
    console.log('debug')
    console.log(window.parent.sphdSocket)
    console.log(window.parent.$.webRoot)
    console.log('debug')
  },
  methods: {
    minFloating: function () {
      window.parent.minFloating()
    },
    defaultImg: function () {
      this.src = ''
    }
  }
}
</script>
