package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.beanutils.BeanUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Wyu on 2018/10/26.
 * 员工月考勤统计表
 */
public class PersonnelAttendanceMonthlyDto implements Serializable{

    private Integer id;
    private Integer yearmonth;//年月数（如：201810）
    private Integer user;//用户id ，考虑到以前好多接口，暂时未加集联关系
    private String userName;//用户姓名
    private Integer org;//机构id
    private Integer department;//部门id
    private String departmentName = "其他";//部门名称
    private Short leaveNum;//月请假次数
    private Short travelNum;//月出差次数
    private Short lateNum;//月迟到次数
    private Short leaveEarlyNum;//月早退次数
    private Short outsideNum;//月外出次数
    private Short absenteeismNum;//月旷工次数
    private Short overtimeNum;//月加班次数
    private String imgPath;  //职工图片路径

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getYearmonth() {
        return yearmonth;
    }

    public void setYearmonth(Integer yearmonth) {
        this.yearmonth = yearmonth;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Short getLeaveNum() {
        return leaveNum;
    }

    public void setLeaveNum(Short leaveNum) {
        this.leaveNum = leaveNum;
    }

    public Short getTravelNum() {
        return travelNum;
    }

    public void setTravelNum(Short travelNum) {
        this.travelNum = travelNum;
    }

    public Short getLateNum() {
        return lateNum;
    }

    public void setLateNum(Short lateNum) {
        this.lateNum = lateNum;
    }

    public Short getLeaveEarlyNum() {
        return leaveEarlyNum;
    }

    public void setLeaveEarlyNum(Short leaveEarlyNum) {
        this.leaveEarlyNum = leaveEarlyNum;
    }

    public Short getOutsideNum() {
        return outsideNum;
    }

    public void setOutsideNum(Short outsideNum) {
        this.outsideNum = outsideNum;
    }

    public Short getAbsenteeismNum() {
        return absenteeismNum;
    }

    public void setAbsenteeismNum(Short absenteeismNum) {
        this.absenteeismNum = absenteeismNum;
    }

    public Short getOvertimeNum() {
        return overtimeNum;
    }

    public void setOvertimeNum(Short overtimeNum) {
        this.overtimeNum = overtimeNum;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }
}