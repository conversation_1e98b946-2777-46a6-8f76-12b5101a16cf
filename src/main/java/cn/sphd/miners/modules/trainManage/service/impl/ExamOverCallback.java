package cn.sphd.miners.modules.trainManage.service.impl;

import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import cn.sphd.miners.modules.trainManage.service.ExamService;
import org.springframework.context.ApplicationContext;

/**
 * @ClassName ExamOverCallback
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/3 10:58
 * @Version 1.0
 */
public class ExamOverCallback implements DelayCallback {
    private Integer id;

    public Integer getId() {
        return id;
    }

    public ExamOverCallback(Integer id) {
        this.id = id;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        ExamService examService = ac.getBean(ExamService.class);
        examService.examOver(id);
    }
}
