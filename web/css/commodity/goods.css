*{ font-family:Microsoft Yahei ;   }
td,th{  text-align:center!important;  }
.clr{ clear:both;  }
.contn{ margin-left:-20px; margin-top:-10px;   }
div table tr td{ vertical-align: middle;  }
.conNav>a{
	font-size:13px; 
	color: #999;
	font-weight:700;
	padding: 0 0 0 15px;
}
.Con-pwd,.Con-pwd1{
	padding-right: 25px;
	font-weight: 700;
	font-size: 16px;
	color: #4276a4;
	padding: 25px 0 8px 8px;
	text-align: right;
}
.Con-pwd{
	padding: 0px 0 8px 8px;
}
.Con-pwd1{
	background-color:#f2f2f2; 
}
.txl,.cx,.cx1,.txl1{
	display: inline-block;
}
.txl{
	color: #333;
	font-size: 24px;
	font-weight: 400;
}
.txl1{
	color: #333;
	font-size: 18px;
	font-weight: 400;
}
.cx1{
	margin-left: 10px;
}
.cx1>input{
	width: 100px;
	height: 26px;
}
.txl,.txl1{
	float: left;
}
.xm{
	 font-size: 13px;
	 font-weight: 400;
	 color: #333;
	 position: relative; top:-5px;
	 margin-right: 5px;
}
.aboutbtn{ 
	display:inline-block; 	
	cursor:pointer; 
	font-size:16px; 
	margin-right: 5px; 
	padding:7px 20px;
	font-weight:400;  
}
.aboutbtn1{ 
	display:inline-block; 	
	cursor:pointer; 
	font-size:13px; 
	margin-right: 5px; 
	padding:1px 15px;
	font-weight:400;  
}
.azury,.azury:hover,.azury1,.azury1:hover,.azury2,.azury2:hover,.azury_bas,.azury_bas:hover{ 
	color:#fff;  
	text-decoration: none;		
}
.azury,.azury:hover{
	background:#32C5D2;
}
.azury:hover{
	background-color: #26a0aa;
}
.azury_bas,.azury_bas:hover{
	background: #e1e5ec;
	color: #333;
}
.azury1,.azury1:hover{ 
	background:#e65059;
}
.azury1:hover{
	background-color: #e0232f;
}
.azury2,.azury2:hover{ 
	background:#3698da;
}
.azury2:hover{
	background-color: #237ebc;
}
.resourceAddBtn{ position: relative; top:-4px; }
.Border-big1{
	padding: 0 40px;
}
.Border-big{
	padding: 0 10px;
}
.Border-big2{
	height: 650px;
	padding: 0 40px;
}
.bg{
	background-color: #f1f1f1;
}
.bonceContainer1{
	width: 1500px;
	height: 800px;
	font-size: 16px;
}
.GDS,.GDS-spa,.GDS-spa1,.GDS-spa2{
	display: inline-block;
}
.GDS{
	width: 35%;
	padding: 5px 0;
}
.GDS1{
	width: 63%;
	padding: 5px 0;
}
.GDS-spa,.GDS-spa2{
	text-align: right;
	font-size: 16px;
}
.GDS-spa{
	width: 34%;
	font-weight: 400;
	padding: 0 5px 0 0;	
}
.GDS-spa2{
	width: 42%;
	font-weight: 700;
}
.GDS-spa1{
	width: 40%;
	text-align: left;
	padding:10px; 
}
.save-btn{
	margin-right: 50px;
}
.pic{
	padding:32px 35px 18px 20px;
	background: #fff 
	url(img/goback.png) no-repeat center center;
}
.pic1{
	margin: 30px 0;
	padding:18px 28px 20px 20px;
	background: #fff 
	/*url(../img/goback1.png) no-repeat center center; */

}
.Content-container{
	padding: 20px 0 0 20px;
}
.Con-labONE{
	font-size: 16px;
	font-weight: 400;
}
.Con-labONE>span:nth-child(3),.Con-labONE>span:nth-child(6),.Con-labONE>span:nth-child(9),.Con-labONE>span:nth-child(12){
	color: #999;
}
.Con-labONE>span:nth-child(4),.Con-labONE>span:nth-child(7),.Con-labONE>span:nth-child(10){
	margin-left: 10px;
}
.Con-labTOW{
	margin-top: 20PX;
	font-size: 14px;
	font-weight: 400;
}
.Con-con{
	display: inline-block;
	width: 340px;
}
 
.Content-container>div:nth-child(2){
	margin-top: 15px;

}
.tab1{
	margin-top: 20px;
}
.Bounce_boder{
	clear: both;
}
.bonce_left,.GDS_left,.GDS_right,.GDS_left1,.GDS_left2{
	float: left;
}
.bonce_left{
	width: 50%;

}
.GDS_left,.GDS_left1{
	width: 40%;
	text-align: right;
	font-size: 16px;
}
.GDS_left2{
	width: 49%;
	text-align: right;
	font-size: 16px;
	font-weight: 700;
}
.GDS_left{
	font-weight: 700;
}
.GDS_right{
	width: 50%;
	margin-left: 10px;
}
.BDIV{
	display: block;
	padding: 15px 0;
	height: 25px;
}
.bonceHead{
	font-size: 16px;
	font-weight: 700;
}
.Col_c{
	background-color: #f2f2f2;
}
.goods_hide,.pro_hide,.UPDAT_hide,.UPDAT_Hide,.UPDAT_Hide1,.UPDAT_hide1,.UPDAT_Hide11,.basic_hide,.customer_hide,.add_hide,.basic-seehide,.custom-seehide{
	display: none;
}
.UPDAT_z,.Tab{
	margin-top: 10px;
}
.A_hr>a,.A_hr>a:hover{
	color:#009dda;
	font-size: 14px;
	font-weight: 400;

}
.UPDATcl_hide{
	background-color: #e4e4e4;
}
.table{
	font-size: 14px;
}
.bas_A>a,.bas_A>a:hover{
	text-decoration: none;
	color: #333;

}
/*基本信息查看*/
.bas_label{
	border: 1px solid #e4e4e4;
	margin-top: 35px;
	height: 600px;
}
.bas_title{
	margin: 0 10px;
	border-bottom: 1px solid #e4e4e4;
}
.bas_font{
	padding: 20px;
}
.bas_font>span{
	font-size: 16px;
	font-weight: 700;
	color: #4276a4;
}
.bas_continer{
	padding: 20px 0;
}
.bas_infor{
	display: inline-block;
	min-width: 28%;
	text-align: right;
	font-size: 15px;
	font-weight: 400;
}
.bas_infor1{
	display: inline-block;
	margin-left: 30px;
	color: #999;
	padding: 5px 0;
}
.bas_inforbor{
	height: 50px;
	padding: 3px 0;
}
.bas_inforlab{
	padding: 10px 0;
	margin-top: 50px;
	height: 384px;
}
.bas_btn{
	border-top: 1px solid #e4e4e4;
	padding: 20px;

}
.bas_btnlab{
	width: 34.5%;
	text-align: right;
}
.bas_btnlab1{
	width: 41.5%;
	text-align: right;
}
.cus_inforbor{
	height: 40px;
	padding: 3px 0;
}
.cus_infor{
	display: inline-block;
	min-width: 31.5%;
	text-align: right;
	font-size: 15px;
	font-weight: 400;
}

.cus_continer{
	padding: 10px 0;
}
.cus_btn{
	border-top: 1px solid #e4e4e4;
	padding: 20px;
}
.cus_label{
	border: 1px solid #e4e4e4;
	margin-top: 35px;
	height: 790px;
}
.cus_inforlab{
	padding: 10px 0;
	margin-top: 10px;
	height: 600px;
}
.cus_btnlab1{
	width: 41.5%;
	text-align: right;
}

.add_infor{
	display: inline-block;
	min-width: 31.5%;
	text-align: right;
	font-size: 15px;
	font-weight: 400;
}
.add_label{
	border: 1px solid #e4e4e4;
	margin-top: 35px;
	height: 1100px;
}
.add_inforlab{
	padding: 10px 0;
	margin-top: 10px;
	height: 930px;
}
.add_btnlab1{
	width: 43.5%;
	text-align: right;
}
#process_tbl td>span{ cursor: pointer;  }
.bzInput{ display: inline-block; width: 225px; overflow: hidden;  }


