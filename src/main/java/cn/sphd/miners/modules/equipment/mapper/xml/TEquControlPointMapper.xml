<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquControlPointMapper">
    
    <resultMap type="TEquControlPoint" id="TEquControlPointResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="controlOption"    column="control_option"    />
        <result property="model"    column="model"    />
        <result property="unitId"    column="unit_id"    />
        <result property="lowerLimit"    column="lower_limit"    />
        <result property="upperLimit"    column="upper_limit"    />
        <result property="enabled"    column="enabled"    />
        <result property="enabledTime"    column="enabled_time"    />
        <result property="orders"    column="orders"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />

        <result property="optionName"    column="option_name"    />
        <result property="unit"    column="unit"    />

    </resultMap>

    <sql id="selectTEquControlPointVo">
        select id, org, control_option, model, unit_id, lower_limit, upper_limit, enabled, enabled_time, orders, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_equ_control_point
    </sql>

    <select id="selectTEquControlPointList" parameterType="TEquControlPoint" resultMap="TEquControlPointResult">
        <include refid="selectTEquControlPointVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="controlOption != null "> and control_option = #{controlOption}</if>
            <if test="model != null "> and model = #{model}</if>
            <if test="unitId != null "> and unit_id = #{unitId}</if>
            <if test="lowerLimit != null "> and lower_limit = #{lowerLimit}</if>
            <if test="upperLimit != null "> and upper_limit = #{upperLimit}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="enabledTime != null "> and enabled_time = #{enabledTime}</if>
            <if test="orders != null "> and orders = #{orders}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>

    <select id="selectTEquControlPointListWithPics" parameterType="TEquControlPoint" resultMap="TEquControlPointResult">
        SELECT
        ecp.id,ecp.control_option,eco.name as
        option_name,mu.`name` as unit,ecp.lower_limit,ecp.upper_limit,ecp.create_date,ecp.create_name,ecp.update_date,ecp.update_name,GROUP_CONCAT(eci.normal_path)
        as pics
        FROM
        t_equ_control_point ecp
        left join t_equ_control_option eco ON ecp.control_option = eco.id
        LEFT JOIN t_mt_unit mu ON ecp.unit_id = mu.id
        LEFT JOIN t_equ_control_image eci ON eci.control_point = ecp.id
        <where>
            <if test="model != null ">and model = #{model}</if>
        </where>
        group by ecp.id ORDER BY IF(ecp.update_date IS NULL, ecp.create_date, ecp.update_date) DESC
    </select>
    
    <select id="selectTEquControlPointById" parameterType="Long" resultMap="TEquControlPointResult">
        <include refid="selectTEquControlPointVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTEquControlPoint" parameterType="TEquControlPoint" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_control_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="controlOption != null">control_option,</if>
            <if test="model != null">model,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="lowerLimit != null">lower_limit,</if>
            <if test="upperLimit != null">upper_limit,</if>
            <if test="enabled != null">enabled,</if>
            <if test="enabledTime != null">enabled_time,</if>
            <if test="orders != null">orders,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="controlOption != null">#{controlOption},</if>
            <if test="model != null">#{model},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="lowerLimit != null">#{lowerLimit},</if>
            <if test="upperLimit != null">#{upperLimit},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="enabledTime != null">#{enabledTime},</if>
            <if test="orders != null">#{orders},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTEquControlPoint" parameterType="TEquControlPoint">
        update t_equ_control_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="controlOption != null">control_option = #{controlOption},</if>
            <if test="model != null">model = #{model},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="lowerLimit != null">lower_limit = #{lowerLimit},</if>
            <if test="upperLimit != null">upper_limit = #{upperLimit},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="enabledTime != null">enabled_time = #{enabledTime},</if>
            <if test="orders != null">orders = #{orders},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquControlPointById" parameterType="Long">
        delete from t_equ_control_point where id = #{id}
    </delete>

    <delete id="deleteTEquControlPointByIds" parameterType="String">
        delete from t_equ_control_point where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>