package cn.sphd.miners.modules.equipment.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.persistence.Transient;

/**
 * 装备管理_导入对象 t_equ_model_import
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
public class TEquModelImport
{
    private static final long serialVersionUID=1L;

    /** ID */
    private Long id;

    /** 机构ID */
    private Long org;

    /** 导入ID */
    private Long imports;

    /** 状态:1-导入临时表,2-录入信息,3-完成 */
    private Long state;

    /** 装备ID */
    private Long equipment;

    /** 装备名称 */
    private String equipmentName;

    /** 型号,为空代表未知 */
    private String modelName;

    /** 编号 */
    private String modelCode;

    private String codeRepeat;

    /** 计量单位,指向t_mt_unit表ID */
    private Long unitId;

    /** 单位名称 */
    private String unit;

    /** 所属类别,为空时代表未分类 */
    private Long category;

    /** 供应商ID,为空时代表自行装配 */
    private Long supplier;

    /** 使用寿命 */
    private Long lifeSpan;

    /** 导入的使用寿命 */
    private String oLifeSpan;

    /** 原值 */
    private BigDecimal originalValue;

    /** 导入的原值 */
    private String oValue;

    /** 到厂日期,统一加01,返回前端时返YYYYMM */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date receiveDate;

    /** 到厂日期,统一加01,返回前端时返YYYYMM */
    private String rDate;

    /** 新旧情况:1-新,2-旧 */
    private Long conditions;

    /** 备注 */
    private String memo;

    /** 创建人id */
    private Long creator;

    /** 创建人 */
    private String createName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /** 修改人id */
    private Long updator;

    /** 修改人 */
    private String updateName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /** 操作:1-增,2-删,3-修改 */
    private String operation;

    /** 修改前记录ID */
    private Long previousId;

    /** 版本号,每次修改+1 */
    private Long versionNo;

    @Transient
    private String categoryName;

    @Transient
    private String name;

    @Transient
    private Integer repeat;

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setOrg(Long org)
    {
        this.org = org;
    }

    public Long getOrg()
    {
        return org;
    }
    public void setImports(Long imports)
    {
        this.imports = imports;
    }

    public Long getImports()
    {
        return imports;
    }
    public void setState(Long state)
    {
        this.state = state;
    }

    public Long getState()
    {
        return state;
    }
    public void setEquipment(Long equipment)
    {
        this.equipment = equipment;
    }

    public Long getEquipment()
    {
        return equipment;
    }
    public void setEquipmentName(String equipmentName)
    {
        this.equipmentName = equipmentName;
    }

    public String getEquipmentName()
    {
        return equipmentName;
    }
    public void setModelName(String modelName)
    {
        this.modelName = modelName;
    }

    public String getModelName()
    {
        return modelName;
    }
    public void setModelCode(String modelCode)
    {
        this.modelCode = modelCode;
    }

    public String getModelCode()
    {
        return modelCode;
    }
    public void setUnitId(Long unitId)
    {
        this.unitId = unitId;
    }

    public Long getUnitId()
    {
        return unitId;
    }
    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    public String getUnit()
    {
        return unit;
    }
    public void setCategory(Long category)
    {
        this.category = category;
    }

    public Long getCategory()
    {
        return category;
    }
    public void setSupplier(Long supplier)
    {
        this.supplier = supplier;
    }

    public Long getSupplier()
    {
        return supplier;
    }
    public void setLifeSpan(Long lifeSpan)
    {
        this.lifeSpan = lifeSpan;
    }

    public Long getLifeSpan()
    {
        return lifeSpan;
    }
    public void setoLifeSpan(String oLifeSpan)
    {
        this.oLifeSpan = oLifeSpan;
    }

    public String getoLifeSpan()
    {
        return oLifeSpan;
    }
    public void setOriginalValue(BigDecimal originalValue)
    {
        this.originalValue = originalValue;
    }

    public BigDecimal getOriginalValue()
    {
        return originalValue;
    }
    public void setoValue(String oValue)
    {
        this.oValue = oValue;
    }

    public String getoValue()
    {
        return oValue;
    }
    public void setReceiveDate(Date receiveDate)
    {
        this.receiveDate = receiveDate;
    }

    public Date getReceiveDate()
    {
        return receiveDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCodeRepeat() {
        return codeRepeat;
    }

    public void setCodeRepeat(String codeRepeat) {
        this.codeRepeat = codeRepeat;
    }

    public String getrDate() {
        return rDate;
    }

    public void setrDate(String rDate) {
        this.rDate = rDate;
    }

    public void setConditions(Long conditions)
    {
        this.conditions = conditions;
    }

    public Long getConditions()
    {
        return conditions;
    }
    public void setMemo(String memo)
    {
        this.memo = memo;
    }

    public String getMemo()
    {
        return memo;
    }
    public void setCreator(Long creator)
    {
        this.creator = creator;
    }

    public Long getCreator()
    {
        return creator;
    }
    public void setCreateName(String createName)
    {
        this.createName = createName;
    }

    public String getCreateName()
    {
        return createName;
    }
    public void setUpdator(Long updator)
    {
        this.updator = updator;
    }

    public Long getUpdator()
    {
        return updator;
    }
    public void setUpdateName(String updateName)
    {
        this.updateName = updateName;
    }

    public String getUpdateName()
    {
        return updateName;
    }
    public void setOperation(String operation)
    {
        this.operation = operation;
    }

    public String getOperation()
    {
        return operation;
    }
    public void setPreviousId(Long previousId)
    {
        this.previousId = previousId;
    }

    public Long getPreviousId()
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo)
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo()
    {
        return versionNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getRepeat() {
        return repeat;
    }

    public void setRepeat(Integer repeat) {
        this.repeat = repeat;
    }
}
