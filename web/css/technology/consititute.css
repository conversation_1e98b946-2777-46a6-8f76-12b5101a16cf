.ty-container>div{ display: none; }
.ty-container>div.mainCon1{ display:block; width: 800px;}
.flexArea{ display: flex }
.flexArea>div{ flex:1;   }
.nav-bar{ padding:15px 15px 20px 0; width:600px; border-radius:4px;   }
.nav-bar>p.bold{ font-weight: bold; margin-bottom:10px; border-bottom:1px dashed #ccc;   }
.nav-bar>p.tip{ color:#0b94ea;   }
.navs{ padding:15px 0; }
.handle{ margin-left: 50px; }
.illustrate {width:540px; }
.mainCon3 .fa{ color:#0b94ea; font-weight: bold;   }
.giveTip .bonceCon{ text-align: center; }
.selectedCon .navs>div { margin:15px 25px; border:1px dashed #0b94ea; width:600px;padding:5px 15px 0;    }
#scanGS{ width:800px;  }
#splitGS, #splitGS2, #partEntry{ width:970px;  }
#scanGS .memo{ text-align: left;   }
.linkBtn{ margin:0 5px 0 50px; color: #0b94ea; cursor: default; }
.linkBtn:hover{ text-decoration: underline; font-weight: bold;  }
#splitGS .bonceCon {max-height: 500px; overflow-y: auto;   }
#partEntry input,#partEntry textarea{ width: 100%; border:none;  }
span.red{ color: red;  }
.mainCon1 .h1 , .mainCon1 .h2, .mainCon1 .h3{ font-size: inherit; font-weight:inherit;   }
#selecGS{ display: none; position: absolute; background:#fff;  width:192px; max-height:200px; overflow-y:auto; border-radius:4px; border:1px solid #ccc; border-top:none;   }
#selecGS option{ border-bottom:1px solid #eee; background:#f9f8f8;    }
#selecGS option:hover, #selecGS option.active{ background:#ddd; cursor: default;    }
#partEntry td[disabled], #partEntry td[disabled] input, #partEntry td[disabled] select,#partEntry td[disabled] textarea{ background:#eee;  }
select[disabled],input[disabled]{ background:#eee!important;  }
.row_cont{margin: 20px 0;}
.panel-br{border-bottom: 1px solid #bfbfbf;}
.gapW{display: inline-block;width:130px;}
.gapL{margin-left: 80px;}
.gap{margin-left: 4px;}
.mainCon1 select{width:310px;}















