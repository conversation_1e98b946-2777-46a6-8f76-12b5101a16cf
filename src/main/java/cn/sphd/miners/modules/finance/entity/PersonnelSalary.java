package cn.sphd.miners.modules.finance.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-06-07 
 */

@Entity ( name ="PersonnelSalary" )
@Table ( name ="t_personnel_salary" )
public class PersonnelSalary  implements Serializable {

	private static final long serialVersionUID =  8805584264382133478L;

	/**
	 * ID
	 */
	@Id
	@Column(name="id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 用户ID
	 */
   	@Column(name = "user" )
	private Integer user;

	/**
	 * 招聘ID
	 */
   	@Column(name = "offer" )
	private Integer offer;

	/**
	 * 所属月份
	 */
   	@Column(name = "reporting_period" )
	private String reportingPeriod;

	/**
	 * 月薪(应发工资)
	 */
   	@Column(name = "salary" )
	private Double salary;

	/**
	 * 实发工资
	 */
	@Column(name = "fact_salary" )
	private Double factSalary;

	/**
	 * 月薪时间
	 */
	@Column(name = "salary_time" )
	private Date salaryTime;

	/**
	 * 月薪用户ID
	 */
	@Column(name = "salary_user" )
	private Integer salaryUser;

	/**
	 * 月薪用户名
	 */
	@Column(name = "salary_name" )
	private String salaryName;

	/**
	 * 所得税
	 */
	@Column(name = "income_tax" )
	private Double incomeTax;

	/**
	 * 个税时间
	 */
	@Column(name = "tax_time" )
	private Date taxTime;

	/**
	 * 个税用户ID
	 */
	@Column(name = "tax_user" )
	private Integer taxUser;

	/**
	 * 个税用户名
	 */
	@Column(name = "tax_name" )
	private String taxName;

	/**
	 * 社保费
	 */
	@Column(name = "insurance_premium" )
	private Double insurancePremium;

	/**
	 * 社保时间
	 */
	@Column(name = "premium_time" )
	private Date premiumTime;

	/**
	 * 社保用户ID
	 */
	@Column(name = "premium_user" )
	private Integer premiumUser;

	/**
	 * 社保用户名
	 */
	@Column(name = "premium_name" )
	private String premiumName;

	/**
	 * 公积金
	 */
	@Column(name = "accumulation_fund" )
	private Double accumulationFund;

	/**
	 * 公积金时间
	 */
	@Column(name = "fund_time" )
	private Date fundTime;

	/**
	 * 公积金用户ID
	 */
	@Column(name = "fund_user" )
	private Integer fundUser;

	/**
	 * 公积金用户名
	 */
	@Column(name = "fund_name" )
	private String fundName;

	/**
	 * 调整日期
	 */
   	@Column(name = "adjust_date" )
	private Date adjustDate;

	/**
	 * 操作时间
	 */
   	@Column(name = "operate_time" )
	private Date operateTime;

	/**
	 * 操作人员
	 */
   	@Column(name = "operator" )
	private Integer operator;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改,4-调薪资,5-调所得税,6-调社险,7-调公积金
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号(修改批次)
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	/**
	 * 子版本号(修改记录)
	 */
   	@Column(name = "sub_version_no" )
	private Integer subVersionNo;

	public Integer getSalaryUser() {
		return salaryUser;
	}

	public void setSalaryUser(Integer salaryUser) {
		this.salaryUser = salaryUser;
	}

	public String getSalaryName() {
		return salaryName;
	}

	public void setSalaryName(String salaryName) {
		this.salaryName = salaryName;
	}

	public Integer getTaxUser() {
		return taxUser;
	}

	public void setTaxUser(Integer taxUser) {
		this.taxUser = taxUser;
	}

	public String getTaxName() {
		return taxName;
	}

	public void setTaxName(String taxName) {
		this.taxName = taxName;
	}

	public Integer getPremiumUser() {
		return premiumUser;
	}

	public void setPremiumUser(Integer premiumUser) {
		this.premiumUser = premiumUser;
	}

	public String getPremiumName() {
		return premiumName;
	}

	public void setPremiumName(String premiumName) {
		this.premiumName = premiumName;
	}

	public Integer getFundUser() {
		return fundUser;
	}

	public void setFundUser(Integer fundUser) {
		this.fundUser = fundUser;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public Date getSalaryTime() {
		return salaryTime;
	}

	public void setSalaryTime(Date salaryTime) {
		this.salaryTime = salaryTime;
	}

	public Date getTaxTime() {
		return taxTime;
	}

	public void setTaxTime(Date taxTime) {
		this.taxTime = taxTime;
	}

	public Date getPremiumTime() {
		return premiumTime;
	}

	public void setPremiumTime(Date premiumTime) {
		this.premiumTime = premiumTime;
	}

	public Date getFundTime() {
		return fundTime;
	}

	public void setFundTime(Date fundTime) {
		this.fundTime = fundTime;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getUser() {
		return this.user;
	}

	public void setUser(Integer user) {
		this.user = user;
	}

	public Integer getOffer() {
		return this.offer;
	}

	public void setOffer(Integer offer) {
		this.offer = offer;
	}

	public String getReportingPeriod() {
		return this.reportingPeriod;
	}

	public void setReportingPeriod(String reportingPeriod) {
		this.reportingPeriod = reportingPeriod;
	}

	public Double getSalary() {
		return this.salary;
	}

	public void setSalary(Double salary) {
		this.salary = salary;
	}

	public Double getFactSalary() {
		return this.factSalary;
	}

	public void setFactSalary(Double factSalary) {
		this.factSalary = factSalary;
	}

	public Double getIncomeTax() {
		return this.incomeTax;
	}

	public void setIncomeTax(Double incomeTax) {
		this.incomeTax = incomeTax;
	}

	public Double getInsurancePremium() {
		return this.insurancePremium;
	}

	public void setInsurancePremium(Double insurancePremium) {
		this.insurancePremium = insurancePremium;
	}

	public Double getAccumulationFund() {
		return this.accumulationFund;
	}

	public void setAccumulationFund(Double accumulationFund) {
		this.accumulationFund = accumulationFund;
	}

	public Date getAdjustDate() {
		return this.adjustDate;
	}

	public void setAdjustDate(Date adjustDate) {
		this.adjustDate = adjustDate;
	}

	public Date getOperateTime() {
		return this.operateTime;
	}

	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}

	public Integer getOperator() {
		return this.operator;
	}

	public void setOperator(Integer operator) {
		this.operator = operator;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

	public Integer getSubVersionNo() {
		return this.subVersionNo;
	}

	public void setSubVersionNo(Integer subVersionNo) {
		this.subVersionNo = subVersionNo;
	}

}
