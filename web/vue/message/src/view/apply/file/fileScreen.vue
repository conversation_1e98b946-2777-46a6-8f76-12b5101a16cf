<template>
  <div id="applyFileQuery">
    <TY_NavTop title="筛选"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tip">请确定要查询内容的筛选条件</div>
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <h4 class="item-header">事件结果</h4>
            <div class="el-form-item" v-if="eventTab == '1'">
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已发布的文件</el-radio>
                <el-radio :label="3">被驳回的文件</el-radio>
              </el-radio-group>
            </div>
            <div class="el-form-item" v-else-if="eventTab == '2'">
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已换版/废止的文件</el-radio>
                <el-radio :label="3">被驳回的文件</el-radio>
              </el-radio-group>
            </div>
            <h4 class="item-header">发生时间</h4>
            <div class="el-form-item">
              <el-date-picker type="date" placeholder="开始日期" v-model="beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd" v-on:change="clearOther('date')"></el-date-picker> -
              <el-date-picker type="date" placeholder="结束日期" v-model="endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd" v-on:change="clearOther('date')"></el-date-picker>
            </div>
            <el-form-item label="仅看年报">
              <el-date-picker type="year" placeholder="选择年份" v-model="year" style="width: 100%;" size="small" value-format="yyyy" v-on:change="clearOther('year')"></el-date-picker>
            </el-form-item>
            <el-form-item label="仅看月报">
              <el-date-picker type="month" placeholder="选择月份" v-model="month" style="width: 100%;" size="small" v-on:change="clearOther('month')"></el-date-picker>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="fc-btn ui-btn_info" value="确定" @click="submitForm(1)" v-loading.fullscreen.lock="loading">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'applyFileQuery',
  data () {
    return {
      queryForm: {
        approveStatus: 2
      },
      year: '',
      month: '',
      beginDate: '',
      endDate: '',
      loading: false,
      eventTab: ''
    }
  },
  created: function () {
    this.eventTab = this.$route.params.event
    if (!this.eventTab) {
      this.eventTab = JSON.parse(localStorage.getItem('query')).eventType
    }
  },
  methods: {
    submitForm: function () {
      // userId 登录人
      // type （3- 自定义 ，2 -仅看年报，1- 仅看月报）
      // approveStatus 2-是查已批准的  3-是查驳回的
      // timeBegin  开始时间   查询的时间 此值每次都要传，例如本月时传“2019-01”，查本年时传“2019”，自定义时传那个设定的开始时间“2019-01-01”
      // timeEnd  自定义查询时的结束时间 只有自定义时才传 ，不是自定义不传
      this.loading = true
      this.queryForm.userID = this.sphdSocket.user.userID
      let event = this.$route.params.event
      if (!event) {
        event = JSON.parse(localStorage.getItem('query')).eventType
      }
      this.queryForm.eventType = event
      if (this.year !== '') {
        this.queryForm.type = 2
        this.queryForm.timeBegin = this.year
        if (this.queryForm.timeEnd) {
          delete this.queryForm.timeEnd
        }
      } else if (this.month !== '') {
        this.queryForm.type = 1
        let date = new Date(this.month)
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        if (month <= 9) {
          month = '0' + month
        }
        this.queryForm.timeBegin = year + '-' + month
        if (this.queryForm.timeEnd) {
          delete this.queryForm.timeEnd
        }
      } else {
        this.queryForm.type = 3
        this.queryForm.timeBegin = this.beginDate
        this.queryForm.timeEnd = this.endDate
      }
      if (this.queryForm.timeBegin) {
        localStorage.setItem('query', JSON.stringify(this.queryForm))
        this.$router.push({
          path: '/applyFileQueryResult',
          name: 'applyFileQueryResult',
          params: {
            data: this.queryForm
          }
        })
      } else {
        this.loading = false
        this.$kiko_message('请选择查询时间')
      }
    },
    clearOther: function (type) {
      if (type === 'date') {
        this.year = ''
        this.month = ''
      } else if (type === 'year') {
        this.month = ''
        this.beginDate = ''
        this.endDate = ''
      } else if (type === 'month') {
        this.year = ''
        this.beginDate = ''
        this.endDate = ''
      }
    }
  }
}
</script>
