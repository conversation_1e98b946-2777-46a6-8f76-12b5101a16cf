package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpAppHistoryDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpAppHistory;
import cn.sphd.miners.modules.thirdPlatform.entity.TpAppHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpAppHistoryDaoImpl extends BaseDao<TpAppHistory, Serializable> implements TpAppHistoryDao {
}
