
@charset "utf-8";
@use "@/style/variable.scss" as *;

.maintip{
  width: 500px;
  margin:130px 0 0 50px;
}
.maintip>p{
  margin-bottom: 10px;
}
.maintip>p:nth-child(3){
  margin-top: 40px;
}
.maintip>p:nth-child(3)>span:nth-child(2){
  margin-left: 100px;
}
.impotEdit{
  width: 100%;
  border-collapse: collapse;
}
.impotEdit td{
  padding:6px 10px;
  position: relative;
  border: 1px solid #ccc;
}
#litAdd td input, #litAdd td select{
  max-width: 150px;
}
#litAdd tr.noBr td{
  border: none;
}
#litAdd table{
  width: 100%;
  border-collapse: collapse;
}
#litAdd td{
  padding:8px 10px;
  position: relative;
  border: 1px solid #ccc;
}
#litAdd {
  .el-form-item {
    margin-bottom: 0;
  }
}
.linkBtn{
  color: #0b94ea;
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
}
.linkBtn:hover{
  text-decoration: underline;
}
.color-red{
  color: #e04f3c!important;
}
.exportStep {
  padding-left: 40px;
}
.stepItem {
  margin-bottom: 20px;
}
.stepItem p{
  margin-bottom: 10px;
}
.flexRow {
  margin-left: 50px;
  display: flex;
  justify-content: space-between;
  width: 356px;
}
#uploadAdd{
  width: 550px;
}
#tab2{
  margin-top: 30px;
}
#catContainer{
  max-height: 300px;
  overflow: auto;
  border: 1px solid #eee;
}
#catContainer li{
  line-height: 25px;
}
#catContainer .catName{
  cursor: pointer;
  padding:0 10px;
}
#catContainer .catName:hover{
  background: #b7d4e7;
}
#catContainer .catName.selectedCat{
  background:#0b94ea ;
  color:#fff;
}
#catContainer ul ul{
  margin-left: 30px;
  width: 300px;
  border-left: 1px solid #0b94ea;
}
.preTab{
  width: 100%;
  margin-bottom: 30px;
}
.preTab td{
  vertical-align: sub;
}
/*批量导入*/
.fileFullName{  width: 300px; line-height:24px;background: #fff;text-align: center;}
.importSect{padding-top: 20px;margin-right: 60px;}
.narrowBody{margin: 0 auto;width: 80%;}
.viewBtn .uploadify-button{ padding: 0 12px;  margin: 0;display: inline-block; border-radius: 0; height: 24px;  line-height: 26px;background-color: #5d9cec;  color: #fff;border: none;}
.viewBtn .uploadify-button:hover{ background: #5d9cec;}
.mainCon2 p{ margin-bottom: 6px;}
.mainCon3 .importCon2 select{ border: 1px solid #d7d7d7;}
.mainCon3 .importCon2 input{width: 120px; border: 1px solid #d7d7d7;text-align: center;}
/*补录*/
.eqOtherItems{margin-top: 50px;}
.ty-table .el-form-item ,.impotEdit .el-form-item{margin-bottom: 0;}
.impotEdit td .el-input, .impotEdit td .el-select{max-width: 220px;}
#msgCon{margin: 28px auto 0; width: 80%;}
#msgCon p{margin-bottom: 14px;}
/*装备清单*/

 .hrLine {
   border-top: 1px solid #d7d7d7;
 }

.rowGap {
  padding: 24px 0;
}
.gapRt {
  margin-right: 10px;
}

input {
  padding: 6px 12px;
  background-color: #fff;
  border: 1px solid #c2cad8;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

/*装备器具 - 装备清单 css*/
.linkBtn {
  color: #0b94ea;
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
}

.linkBtn:hover {
  text-decoration: underline;
}

.filter i {
  font-size: 25px;
  color: #0b94ea;
  position: absolute;
  right: 7px;
  top: 5px;
}

.filter {
  text-align: right;
  float: right;
  position: relative;
}

.allWidth {
  width: 100%;
  margin-top: 30px;
  margin-bottom: 30px;
}

#eqScan td > div:nth-child(2), #eqLogScan td > div:nth-child(2) {
  color: #0b94ea;
  padding-left: 15px;
  height: 30px;
  background: #fff;
  line-height: 30px;
  border-radius: 4px;
}

.tdRela i {
  position: absolute;
  left: -40px;
  font-size: 16px;
  color: #5d9cec;
  top: 2px;
}

#catPath {
  font-size: 16px;
  color: #5d9cec;
}

.tdRela {
  position: relative;
}

#selectTab {
  width: 94%;
  margin-left: 5%;
}


#batchClassCon .fa {
  color: #0b94ea;
  font-size: 18px;
  width: 18px;
  font-weight: bold;
}

.catItemName .fa {
  position: relative;
  top: 2px;
}

#batchClassCon .catItemName {
  cursor: pointer;
}
.gapLt {
  margin-left: 50px;
}

.gapNr {
  margin-left: 10px;
}

.gapB {
  margin-bottom: 50px;
}
.gapBr {
  margin-top: 10px;
}

.ty-table tbody td.grayBg {
  width: 80px;
  font-size: 24px;
  background: #f1f1f1;
}

.ty-table tbody td.grayBg .fa {
  vertical-align: top;
  line-height: 28px;
}

.setTip {
  margin-right: 50px;
  line-height: 32px;
}

.catBody {
  margin: auto;
  width: 460px;
}

#uniformCatName {
  margin-top: 30px;
  width: 350px;
  height: 20px;
}

.downBtn {
  display: inline-block;
  width: 70px;
  background: #d8d8d8;
  text-align: center;
  height: 34px;
  line-height: 30px;
  font-size: 20px;
  margin-left: -5px;
}

#selectCatPath {
  margin-top: 20px;
}

.eqDetails  table {
  margin-bottom: 30px;
}

.eqDetails > div span:not(:last-child) {
  margin-right: 30px;
}

.bonceCon .infoEdit input {
  max-width: 120px;
}

.moreBd {
  border-bottom: 1px solid #ddd;
  margin: 40px 0;
}

.singleSect span {
  margin-bottom: 10px;
  padding: 10px 2px 6px 2px;
  width: 120px;
  display: inline-block;
  border-bottom: 3px solid #ccc;
}

.equipEdit {
  padding-top: 20px;
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.equipEdit hr {
  border-bottom: 1px solid #ccc;
}

.gapTp {
  margin-top: 50px;
}

.limitPos {
  position: relative;
}

.subLogScan {
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid #ccc;
}

.cateCon {
  max-width: 300px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.eqDetails .memo {
  max-width: 260px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.clear{
  overflow: hidden;
}
.txtRight {
  text-align: right;
}
.ty-table .el-form-item {
  margin-bottom: 0;
}
.angleBtn{
  position: absolute;
  right: 16px;
}
/*装备器具 - 装备清单 end*/
/*名称管理*/
$size1: 100%;
.el-button--typcolor{
  font-size: 12px;
  padding: 5px 15px 5px;
  border-radius: 3px;
  font-weight: bold;
  cursor: pointer;
  color: #5d9cec;
  white-space: nowrap;
  text-align: center;
  word-wrap: break-word;
  word-spacing: normal;
  word-break: break-all;
  box-sizing: border-box;
  border: none;
}
//当需要给某个特定的el-button设定特定的hover属性时，可以像下面这样自定义设定，然后再将:hover前面的部分赋值给需要设定该
//样式的el-button的class,这样就能正常展示了。
.impont-hover:hover{
  background-color: var(--el-color-primary);
  color: #ffffff;
}
.el-select .el-input {
  width: 130px;
}
.nameagent{
  .rolor{
    .el-row{
      > .el-col:nth-child(1){
        .plence{
          position: absolute;top: 7px;
        }
      }
      > .el-col:nth-child(3){
        .grid-content .el-row{align-items: center;}
      }
    }
    .upolence{
      margin-top: 20px;
      .el-table{width: $size1;}
    }
  }
}
.quantitydetails{
  .plencew{
    .upolence{
      margin-top: 20px;
      > .el-col:nth-child(1){
        .upolence2{margin-top: 7px;}
      }
      > .el-col:nth-child(3){
        .grid-content .el-row > .el-col:nth-child(3){
          .grid-content .el-row > .el-col:nth-child(2){
            .grid-content{
              .el-button{
                span{font-weight: bold;}
              }
            }
          }
        }
      }
      .el-table{width: $size1;}
    }
  }
}
.screenboder{
  .plencew{
    .upolence{
      margin-top: 20px;
      > .el-col:nth-child(1){
        .upolence2{margin-top: 7px;}
      }
      .el-table{width: $size1;}
    }
  }
}
.drawingnumber{
  .plencew{
    .uploence{
      margin-top: 20px;
      .el-table{width: $size1;}
    }
  }
}
.categorycode{
  .plencew{
    .upolence{
      margin-top: 20px;
      .el-table{width: $size1;}
    }
  }
}
.choseagent{
  .plencew{
    .upolence{
      margin-top: 20px;
      .el-table{width: $size1;}
    }
  }
}
.tyDialog{
  div{
    .plencen{
      margin-bottom: 18px;margin-top: 10px;
    }
    .el-table{width: $size1;}
  }
}

.hd{
  display: none;
}
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
//.plence{
//  position: absolute;top: 7px;
//}
//.plencen{
//  margin-bottom: 18px;margin-top: 10px;
//}
//.plencew{
//  margin: 48px 107px 20px;
//}
//.plpen{
//  padding: 7px 16px;
//}
//.rolor{
//  margin: 57px 96px 0 96px;
//}
//.upolence{
//  margin-top: 20px;
//}
//.upolence2{
//  margin-top: 7px;
//}
//.upolence3{
//  > .el-col:nth-child(3){
//    .el-row > .el-col:nth-child(3){
//      .el-row > .el-col:nth-child(2){
//        .el-button span{font-weight: bold;}
//      }
//    }
//  }
//}
/*类别管理*/
$size2: 100%;
.diffenten{
  color: #5d9cec;
}
.diffentenb{
  color: #ED5565;
}
.el-button--typcolor{
  font-size: 12px;
  padding: 5px 15px 5px;
  border-radius: 3px;
  font-weight: bold;
  cursor: pointer;
  white-space: nowrap;
  text-align: center;
  word-wrap: break-word;
  word-spacing: normal;
  word-break: break-all;
  box-sizing: border-box;
  border: none;
}
.el-input.is-disabled ::v-deep .el-input__wrapper{
  background-color: #CCCCCC;
}
.impont-hover:hover{
  background-color: var(--el-color-primary);
  color: #ffffff;
}
.impont-hover2:hover{
  background-color: var(--el-color-danger);
  color: #ffffff;
}

.cateagent{
  div .rolor{
    .el-row{
      .plence{ position: absolute;top: 7px;}
      .el-col:nth-child(3){
        .grid-content .el-row{
          .el-col{
            .catadd{
              justify-content: flex-end;display: flex;
              .el-button span{font-weight: bold;}
            }
          }
        }
      }
    }
    .upolence{
      margin-top: 30px;
      .el-table{width: $size2;}
    }
  }
}
.stopcatoney{
  .plencew{
    .upolence{
      margin-top: 30px;
      .el-table{width: $size2;}
    }
  }
}
.diretsbaeory{
  .plencew{
    .upolence{
      margin-top: 30px;
      .el-col:nth-child(3){
        .grid-content .el-row .el-col:nth-child(3){
          .grid-content .el-row .el-col:nth-child(2) .grid-content{
            .el-button span{font-weight: bold;}
          }
        }
      }
      .el-table{width: $size2;}
    }
  }
}
.tyDialog{
  div{
    .plencen{
      margin-bottom: 18px;margin-top: 20px;
      .el-col:nth-child(3){
        .grid-content .el-row > .el-col:nth-child(3){
          .grid-content .el-button{
            span{font-weight: bold;}
          }
        }
      }
    }
    .el-table{width: $size2;}
  }
  .only{
    margin-top: 24px;
  }
}

//.catadd{
//  justify-content: flex-end;
//  display: flex;
//}
.colorn{
  color: #5D96E6;
  font-size: 14px;
}
.modev{
  margin-top: 7px;
}
//.plence{
//  position: absolute;top: 7px;
//}
//.plencen{
//  margin-bottom: 18px;margin-top: 20px;
//}
//.plencew{
//  margin: 48px 107px 20px;
//}
.plend1{
  margin-top: 5px;
}
//.rolor{
//  margin: 57px 96px 0 96px;
//}
//.upolence{
//  margin-top: 30px;
//}
/*类别管理end*/

.part{padding: 4px 8px;border-bottom: 1px solid #e1e7ec;}  .part21{padding: 4px 8px;}  .part_end{padding: 4px 8px;}

.flexItem{
  display: flex;
}
.item_theme{
  line-height:32px;
  display: inline-block;
  min-width: 100px;
}
.item_theme .title{
  line-height: 14px;
  color: #5d9cec;
  border-left: 3px solid #5d9cec;
  padding-left: 4px;
  font-weight: bold;
}
.flexItem .item_title{
  display: inline-block;
  min-width: 100px;
  line-height:32px;
  text-align: right;
  margin-right: 4px;
  flex:none;
}
.flexItem .item_content{
  display: inline-block;
  min-width: 200px;
  line-height:32px;
  color: #62707e;
  flex: auto;
}
.flexItem .reciveDescript{color: #101010;}
.right_btn {
  flex: auto;
  text-align: right;
  line-height: 32px;
}
.addOtherInfo{
  margin:20px 0 0;
}
.receiveListo{
  margin-top: 15px;
}

.equipMentSupplier{
  .gapLg{margin-bottom: 12px;}
  .supplierAddCC{
    line-height:40px; font-size: 14px; padding-left:40px;
    .ttl{ font-weight: bold; font-size: 13px; }
    .marL30{ margin-left:30px;  }
    .marL50{ margin-left:42px;  }
    table{width: 100%;}
    .radiop{ padding: 0 10px;
      .fa{ color:$tyBounce-color-blue; margin-right: 6px; font-size: 18px; }
    }
    .pa20{ padding:0 20px; display: inline-block; position: relative; top:8px;  }
    .imgList{
      .imgI{
        position: relative; margin-right: 10px;
        .delBtn{ cursor: pointer;
          .fa{ font-size: 20px; opacity:0.2; margin:0 10px; }
          .fa:hover{ opacity:1;  }
        }
        .img, img{width: 90px; height:60px;display: inline-block; background-size: 100%;
          background-position: center center; box-shadow:0 0 3px #ccc ; background-color: #fff;}
      }
    }
  }
  .pa20{ padding:0 20px; display: inline-block; position: relative; top:8px;  }
  .hang_7{margin-left: 80px;}
  .scanSupplierCon{
    .flexItem{display: flex}
    .item_title{text-align: left;line-height: 35px;}
  }
  .newMailInfo{
    margin-left: 20px;
    line-height: 40px;
    td{ padding-left:10px; position: relative; }
    .fa-angle-down{ font-size: 18px; }
    .mask{ position: absolute; width: 100%; height: 100%; z-index: 1 }

  }
  .chooseCusContactcc{
    line-height:20px;
    .fa{ color: $tyBounce-color-blue; margin-right: 5px;   }
    .table{ background: #fff; padding: 10px; margin:10px;   }
    .clickArea{ cursor: pointer;
      -webkit-user-select: none; /* Safari */
      -moz-user-select: none; /* Firefox */
      -ms-user-select: none; /* IE10+/Edge */
      user-select: none; /* Standard syntax */
      span{
        margin-left: 5px;margin-right: 5px;
      }
    }
    table{ width: 100%; }

  }
  .editContactLianXicc{
    line-height: 30px; font-size: 15px;
    table{ width: 100%;  }
    .imgcc{
      img{ max-width:100px; max-height: 80px; }
    }
    .lix{
      position: relative;
    }
    :deep(.el-input){ width: 180px; }
    :deep(.el-select){ width: 180px; }
  }
  .useDefinedLabelcc{
    text-align: center; padding: 20px 100px; line-height: 40px;
  }
  .updateRecords{
    .recInfo{ line-height: 40px }
  }
  .sale_ttl1{
    margin-right: 10px;
    display: inline-block;
    min-width: 100px;
    text-align: right;
    line-height: 32px;
    vertical-align: top;
  }
  .recordMain {
    p{margin-bottom: 10px;}
    .sale-con{padding-left:10px;min-width: 142px;line-height:32px;}
  }
  .redFlag{color:red;}
  .cusName{margin-bottom: 20px;}
  .hisList{ padding: 6px 10px; line-height: 22px; font-size: 14px;  color: #666; }
  .warpHis{display: inline-block;width: 130px;text-align: left;}
  .eqMsg {margin: 0 96px;}
  .pageGap {margin-top: 26px;}
  .contact-con{
    padding-left: 10px;
    margin-right: 20px;
    min-width: 142px;
    display: inline-block;
    line-height: 32px;
  }
  .Search {  display: inline-block; height: 35px;    }
  .opinionCon{margin-top: 50px;}
  :deep(.el-input-group__append), :deep(.el-input-group__prepend){ cursor: default; background:$ty-color-blue-btn; border-color:$ty-color-blue-btn; color: #fff;   }
  .pull-right {
    span{ margin-right: 10px; }
  }
  .marL10{margin-left: 10px;}
  .lineRow{display: inline-block;}

  .editContract{
    :deep(.el-upload){
      display: none;
    }
  }
}

.widthLimit :deep(.el-form-item__label){
  width: 100%;
  display: block;
}
.gapPage{
  margin: 10px 0;
}
:deep(.el-tree-node) {
  &.is-current>.el-tree-node__content{
    background: #0b9df9;
    color: #fff;
  }
}
.catch{
  line-height: 30px;
}
.mainCon{
  margin-top: 20px;
}














