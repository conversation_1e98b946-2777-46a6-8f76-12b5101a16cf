package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils.Period;
import cn.sphd.miners.modules.generalAffairs.dto.*;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.log4j.Logger;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.*;

/**
 * Created by Administrator on 2018/4/11.
 */
public interface WorkAttendanceOldService extends BadgeNumberCallback {
    //考勤设置表:操作:0-正常,1-增,2-删,3-改,4-修改录入日期,5-修改迟到早退和请假期设置
    enum AttendanceConfigOperation {
        normal("normal", (byte)0),
        create("create", (byte)1),
        delete("delete", (byte)2),
        modify("modify", (byte)3),
        modifyEntryDate("modifyEntryDate", (byte)4),
        modifyLateLeave("modifyLateLeave", (byte)5);
        private String name;
        private Byte index;
        AttendanceConfigOperation(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //考勤设置表:考勤模式:1-考勤宝,2-手工录入
    enum ConfigAttendancePattern {
        attendanceTreasure("attendanceTreasure", "考勤宝", (byte)1),
        manualEntry("manualEntry", "手工录入", (byte)2);
        private String name;
        private String info;
        private Byte index;
        ConfigAttendancePattern(String name, String info, Byte index) {
            this.name = name;
            this.info = info;
            this.index = index;
        }
        public String getName() {
            return name;
        }

        public String getInfo() {
            return info;
        }

        public Byte getIndex() {
            return index;
        }
        public static ConfigAttendancePattern getByIndex(Byte index) {
            try {
                return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().get();
            } catch (NoSuchElementException e) {
                Logger.getLogger(ConfigAttendancePattern.class).warn("index " + index + " not find!" + e.getMessage(), e);
                return null;
            }
        }

        @Override
        public String toString() {
            return "{index:"+getIndex()+",name:'"+getName()+"',info:'"+getInfo()+"'}";
        }
    }

    //人事-员工打卡表:打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
    enum PunchType {
        goToWork("上班", (byte)1),
        offWork("下班", (byte)2),
        forenoon("午前", (byte)3),
        afternoon("午后", (byte)4),
        leavePost("离岗", (byte)5),
        returningToWork("返岗", (byte)6),
        beforeOvertime("加班前", (byte)7),
        afterOvertime("加班后", (byte)8),
        other("其它", (byte)0);
        private String name;
        private Byte index;
        PunchType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
        public static PunchType getByIndex(Byte index) {
            try {
                return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().get();
            } catch (NoSuchElementException e) {
                Logger.getLogger(PunchType.class).warn("index " + index + " not find!" + e.getMessage(), e);
                return null;
            }
        }
    }

    //员工考勤明细打卡对照表: 类型:1-开始,2-结束
    enum DetailPunchType {
        begin("begin", (byte)1),
        end("end", (byte)2);
        private String name;
        private Byte index;
        DetailPunchType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //考勤类型:1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-其它
    enum AttendanceType {
        normal("1", (byte)1),
        late("2", (byte)2),
        leaveEarly("3", (byte)3),
        goOut("4", (byte)4),
        leave("5", (byte)5),
        awayOnOfficial("6", (byte)6),
        absenteeism("7", (byte)7),
        overtime("8", (byte)8),
        other("9", (byte)0);
        private String name;
        private Byte index;
        AttendanceType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
        public static AttendanceType getByIndex(Byte index) {
            try {
                return Arrays.stream(values()).filter(e -> e.getIndex().equals(index)).findAny().get();
            } catch (NoSuchElementException e) {
                Logger.getLogger(AttendanceType.class).warn("index " + index + " not find!" + e.getMessage(), e);
                return null;
            }
        }
        public static AttendanceType getByName(String name) {
            try {
                return Arrays.stream(values()).filter(e -> e.getName().equals(name)).findAny().get();
            } catch (NoSuchElementException e) {
                Logger.getLogger(AttendanceType.class).warn("name " + name + " not find!" + e.getMessage(), e);
                return null;
            }
        }
    }

    //人事_打卡设备表: 类型:1-设备,2-webchat
    enum AttendanceTerminaType {
        device("device", (byte)1),
        webchat("webchat", (byte)2);
        private String name;
        private Byte index;
        AttendanceTerminaType(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }
//    void attendanceTimeSetting(String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,String personnelAttendanceConfigs,User user);

//    PersonnelAttendanceConfig addPersonnelAttendanceConfig(User user,Date inputTime,Date beginTime,Date endTime,Date openDate,String type,Boolean isBreak,
//                                      Date breakBegin,Date breakEnd,Byte operation,Byte attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,Integer recordId,String departmentIds);

//    List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigList(Integer oid,Integer type,Date openDate);

    PersonnelAttendanceConfig getPersonnelAttendanceConfigById(Integer attendanceId);

    void deletePersonnelAttendanceConfig(PersonnelAttendanceConfig personnelAttendanceConfig);

    List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigByOpenDate(Integer oid,Integer type,Date openDate);

    PersonnelAttendanceConfig updatePersonnelAttendanceConfig(PersonnelAttendanceConfig personnelAttendanceConfig);

//    void addPersonnelAttendanceDepartmentConfig(String deptId,User user,PersonnelAttendanceConfig personnelAttendanceConfig,PersonnelAttendanceDepartmentConfigHistory pdch,Integer type); //type 1-直接添加 2-由修改进行的添加

    void addPersonnelAttendanceException(Integer oid, Date nextBeginTime,Date nextEndTime);
    void addPersonnelAttendanceException(Integer oid,Date exceptionDate,String type,User user);

    PersonnelAttendanceException getPersonnelAttendanceExceptionByOid(Integer oid,Date exceptionDate,String type);

//    boolean hasPersonnelAttendanceExceptionByMonth(Integer oid,Date beginTime,Date endTime);
    List<PersonnelAttendanceException> getPersonnelAttendanceExceptionByMonth(Integer oid,Date beginTime,Date endTime);

//    PersonnelAttendanceException getPersonnelAttendanceExceptionByExceptionDate(Integer oid, Date exceptionDate);

//    void deletePersonnelAttendanceException(PersonnelAttendanceException personnelAttendanceException);

//    PersonnelAttendanceUser getPersonnelAttendanceUserByUserIdAndId(Integer id,Integer userId,Date time,String beginState);

//    //将旷工、请假、加班等等的状态考勤，保存到员工考勤明细表中
//    PersonnelAttendanceUserDetail addPersonnelAttendanceUserDetail(Date beginDate,Date endDate,String type,String source,String businessType,Integer leaveType,Integer business,String reason,PersonnelAttendanceUser p,User user,String overDuration,String overMemo,String state,Date createDate) throws ParseException;

    //此方法，只返回不保存
    PersonnelAttendanceUserDetail addAttendanceUserDetail(Date beginDate,Date endDate,String type,String source,String businessType,Integer leaveType,Integer business,String reason,PersonnelAttendanceUser p,User user,String overDuration,String overMemo,String state,Date createDate) throws ParseException;

    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByUserId(Integer userId,Integer attendanceId,Date time,String type,String duration);

//    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByBusiness(Integer attendanceId,String type,String source,Integer business,String state);

//    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByMonth(Integer userId,Integer attendanceId,Date begin,Date end,String type);

    List<PersonnelAttendanceUserDetailHistory> getPersonnelAttendanceUserDetailHistoryByPHId(Integer phId,String state,String type);

    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailById(Integer pId);

    List<PersonnelAttendanceConfigHistory> getPersonnelAttendanceConfigHistoryByRecordId(Integer attendanceRecordId,Integer oid,Integer ruleId,String operation);

    PersonnelAttendanceUserDetail getPersonnelAttendanceUserDetailByDeatilId(Integer atterndanceDetailId);

//    Date getStartUsingSystemTime(Integer oid);//获取系统启用时间
//    boolean isOpenConfig(Integer oid, Date openDate);//获取当天是否配置生效

    List<PersonnelAttendanceUserHistory> getPersonnelAttendanceUserHistoryList(Integer oid,Integer attendanceUserId,Integer attendanceId,String approveStatus,Date attendanceDate,Integer creator,Integer businessType);

    PersonnelAttendanceUserHistory getPersonnelAttendanceUserHistoryById(Integer attendanceUserHistoryId);

    PersonnelAttendanceUserDetailHistory addPersonnelAttendanceUserDetailHistory(Date beginDate,Date endDate,String type,String source,String businessType,String reason,PersonnelAttendanceUserHistory ph,User user,String overDuration,String overMemo,String state) throws ParseException;

    PersonnelAttendanceRecord addPersonnelAttendanceRecord(User user,String effectDate,String operation,Boolean patternType) throws ParseException;

//    List<PersonnelAttendanceRecord> getPersonnelAttendanceRecordByOid(Integer oid,String operation,Date effectDate,PageInfo page);

    PersonnelAttendanceRecord getPersonnelAttendanceRecordById(Integer attendanceRecordId);

    void updatePersonnelAttendanceRecord(PersonnelAttendanceRecord personnelAttendanceRecord);

    void updatePersonnelAttendanceConfigByRecordId(Integer recordId);

    void updatePersonnelAttendanceConfigHistoryByRecordId(Integer recordId);

    List<PersonnelAttendanceUser> getPersonnelAttendanceUser(Integer id,List<Integer> oids, List<Integer> uids, Date beginDate, Date endDate, List<String> types);

//    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByType(Integer attendanceId,Date begin,String source);

    List<PersonnelAttendanceUser> getPersonnelAttendanceUserByOid(Integer oid,Integer dept,Integer userId,Date openDate,String beginState,String userName,Integer sorcType);

    PersonnelAttendanceUser getOnePersonnelAttendanceUserByOid(Integer oid,Integer dept,Date openDate,String beginState,Integer userId);

    List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigByDeptId(Integer oid,Integer deptId,Date openDate);  //根据部门id和时间、机构id，查询可用的考勤规则

    AttendanceNumDto attendanceNumDay(Integer userId, Date time);  //某日的，职工考勤各个状态的详细次数

//    List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailBykg(Integer attendanceId,Integer userId,Date begin,String source);  //不取系统默认的旷工，且没有进行下班录入的

    Long countPersonnelAttendanceUserDetailBykg(Integer attendanceId, Integer userId, Date begin, String source);  //不取系统默认的旷工，且没有进行下班录入的

    //将personnelAttendanceConfig存到历史表PersonnelAttendanceConfigHistory中    operation(0-正常,1-增,2-删,3-改)
    PersonnelAttendanceConfigHistory pcToPH(PersonnelAttendanceConfig personnelAttendanceConfig,PersonnelAttendanceConfig oldPersonnelAttendanceConfig,User user,PersonnelAttendanceRecord personnelAttendanceRecord,Byte operation);

    //查最新的考勤时间
    Date getMaxdateByOpenDate(Integer oid, Date openDate);

    //根据部门查找规则
    PersonnelAttendanceConfig getPersonnelAttendanceConfigByDept(Integer oid, Integer deptId, Date openDate);

    //返回某个部门(或者没部门)的最新的考勤规则的开始时间、结束时间
    Map<String,Object> returnTime(Integer oid,Integer deptId,Date openDate,Map<String, Object> map,Integer type);

    String overTimeType(String type,User user,Date begin,Integer dept);  //根据考勤作息时间的设置，来判断此加班是什么类型的加班（即加班的type为什么值）

//    void updateYesterdayAttendance(PersonnelAttendanceUser py,User user,Date yesterday,Date inputTime,Date start1,String newDate,Integer oneDay) throws ParseException;  //更新昨天的考勤

    PersonnelAttendanceUser addPersonnelAttendanceUser1(Integer oid,Integer deptId,User user,String beginState,String endState,String type,Integer userId,Date openDate,Date workBegin,Date workEnd,Date breakBegin, Date breakEnd,Integer source);

    void pdcToPdch(User user,PersonnelAttendanceConfig personnelAttendanceConfig,PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory);

    void addPersonnelAttendanceDepartmentConfigHistory(User user,PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory,PersonnelAttendanceRecord record,Integer deptId);

    PersonnelAttendanceConfig addPersonnelAttendanceConfigByDetail(User user, PersonnelAttendanceRecord record, PersonnelAttendanceConfig personnelAttendanceConfig, JSONObject jo, String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork) throws ParseException;

    String returnStartUsingSystemTime(Integer oid);  //返回系统开始使用时间

//    Date getDateByWork(Date currendTime,Integer oid);  //取距离今天/昨天最近的日期，此日期必须是“班” 1-假，2-班
    Date getNextDateByWork(Date currendDate,Integer oid);  //取下一个工作日日期

    List<String> getDeptIds(Date currentTime, Integer oid); //参与考勤的部门

    void getAttendanceRecord(PersonnelAttendanceUser personnelAttendanceUser,String date,Integer dtype,User user,String kuangGong);  //考勤录入的处理接口

    void setDefaultWorkAttendance(Integer oid);

    Map<String,Object> noNeedAttendance(User user,JSONObject attendanceJson,Map<String, Object> map,HttpServletRequest request) throws ParseException;   //无需考勤接口

    Map<String,Object> attendanceUserHistory(User user,Integer noNeedType,String absenteeismAll,String leaveListAll,String overListAll,Integer attendanceId,String attendanceDate,Integer attendanceUserId,String upIsNormal,String isAbsenteeism,String isLeave,String downIsNormal,String isOverTime,String updateDesc);  //考勤修改

//    int applyUpdateAttendanceUser(User user,JSONObject attendanceJson,HttpServletRequest request) throws ParseException;  //考勤修改

//    List<ApprovalInstance>  selectApprovalInstanceListByUserId(int userId,String description) throws ParseException;

//    List<ApprovalInstance>  selectApproveApprovalInstanceListByUserId(int userId,String description) throws ParseException;

//    int handleApprovalInstance(User user,Integer id,String approveMemo,int type) throws ParseException;

    int judgeUpdateAttendanceUser(User user,Integer id,Integer attendanceUserId,String attendanceDate) throws ParseException;

    ApprovalDto approvalInstanceByCriterionList(SelectCriterionDto selectCriterionDto) throws ParseException;

//    ApprovalInstanceDto applyApprovalInstanceDetail(int instance) throws ParseException;

//    void initializeWorkAttendanceApply(int org);  //初始化考勤修改审批设置

    //审批的加班和请假计入考勤
    void leaveOvertimeToAttendance(Integer type,Integer userId,Integer approvelUserId,Integer business,String businessType,Integer leaveType,Double duration,Date beginDate,Date endDate,String reason);

    //获取各种类型的考勤信息
    Map<String,Object> getAllTypeAttendance(Integer oid,Integer deptId,Integer userId,Date currentTime,Map<String, Object> map);

    //获取机构指定日期的考勤录入时间
//    Date getInputTime(Integer oid, Date openDate);
//    Date getBeginTime(Integer oid, Integer deptId, Date openDate);
    //检查机构考勤执行的最新日期
    PersonnelAttendanceOrgMonitor getOrgMonitor(Integer oid);

    //设置用户跟换部门的考勤设置
    void changeUserDepartment(User user, String newDeptId, ChangeDeptReasons reason);
    enum ChangeDeptReasons {
        /**
         * 更换部门
         */
        ExchangDept,
        /**
         * 入职
         */
        Enroll,
        /**
         * 离职
         */
        Resign
    }

//    List<PersonnelAttendanceUserDto> getAttendanceList(Integer oid, Date today, Date yesterday,PageInfo pageInfo);
    //添加新增考勤人员，当月变更前为无需考勤
    void setPersonelAttendanceNoNeed(Integer oid, Date day);

    void updateInputTime(Integer attendanceId,String inputTime,User user) throws ParseException;

    List<PersonnelAttendanceDepartmentConfigHistory> getPerAttendanceDeptConfigHistory(Integer attendanceConfigHistoryId);

    List<PersonnelAttendanceDepartmentConfig> getAttendanceDeptConfigByConfigId(Integer attendanceConfigId);

    void updateAttendanceSetting(String deleteAttendanceSetIds,List personnelAttendanceConfigList,User user,String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,Boolean patternType) throws ParseException;

    Map<String,Object> getOverTimeDetail(Integer overTimeId,String source,Integer historyType);

    Map<String,Object> getLeaveDetail(Integer leaveId,String source,Integer historyType);

    Map<String,Object> getAttendanceDayDetail(Integer userId,String attendanceDate,String type) throws ParseException;

//    List<PersonnelAttendanceConfig> attendanceDept(Date openDate,Integer oid,String type);

//    Map<String,Object> getTimeTable(Integer oid,String timeTable);

//    Map<String,Object> getAttendanceMonthOrDay(Integer ttType,String type,Date beginDay,Integer userId);

    void updateDeptName(Integer deptId,String departmentName);

    Map<String,Object> getDateAndWork(Integer oid,Date attendanceDate) ;  //获取系统考勤时间，以及某天在考勤系统中是休息日还是上班日(刘洪涛用)

    Map<String,Object> getLeaveOrOverTime(Integer userId,Date attendanceDate,String type); //获取考勤系统里的加班或请假   刘洪涛-1.145我的工作记录项目使用

//    int applyAttendance(User user,String attendanceAll,String absenteeismAll,String leaveListAll,String overListAll); //此为使用ApprovalInstance表写的接口，暂时不用了，重新写。
    Map<String,Object> applyAttendance(User user,Integer noNeedType,String attendanceAll,String absenteeismAll,String leaveListAll,String overListAll);

    Map<String,Object> approvalAttendance(User user,Integer approvalProcessId,String approveMemo,String approveStatus,Integer updateType);

    //type 1-加班 2-请假 3-申报加班 4-提前结束请假 5-补报请假
    Integer getStateTime(Integer oid,Integer deptId,Date openDate,Integer type, String beginTime1, String endTime1);

    Map<String,Object> getAttendanceDays(User user,Integer deptId,Date beginDate,Integer sourceType,Integer dateType,Integer onlyUser,PageInfo pageInfo); //工作记录中获取加班天数和请假天数的等
    Map<String,Object> newGetAttendanceDays(User user,Integer deptId,Date beginDate,Integer sourceType,Integer dateType,Integer onlyUser,PageInfo pageInfo); //工作记录中获取加班天数和请假天数的等

    Map<String,Object> directSuperior(Integer loginUserId,Integer userId);
    PersonnelAttendanceMonthly getOrFalseMonthly(Integer yearmonth, User user);
    Triple<List<Date>, Set<Date>, Set<Date>> getWorkingDates(Date beginDate, Date endDate, Integer oid);//left 总, middle 班, right假,

    Map<String,Object> setTimeTable(User user,List timeTable);

    Date getPersonnelAttendanceConfigOpenDate(Integer oid, String type, Date openDate); //获取使用规则的启用日期

    Map<String,Object> clock(User user, Date punchTime, Integer leaveId, Integer overTimeId, Integer iotTerminalId, Byte punchType, String image,String terminalUuid,String brand,String model);

//    PersonnelAttendanceUserDetail getPersonnelAttendanceUserDetail(Integer user,Date attendanceDate,String type,Integer business,String state);

    Map<String,Object> clockRecord(User user,String attendanceDate);

    List<Map<String, Object>> getClockRecordList(Integer org,Integer userId,Date attendanceDate,Byte punchType,Integer personnelAttendanceUserDetailId,String type,Date punchTime);

    Map<String,Object> clockRecordDetail(User user,Integer punchId);

    Map<String,Object> modelRecord(User user);

    int getAttendanceUserDetailAndDetailPunch(Integer user,Date attendanceDate,String type,String state,Byte punchType);

    Map<String,Object> modelRecordDetail(User user,Integer historyId);

    Map<String,Object> updateLimit(User user,Short lateLimit,Short earlyLimit,Boolean leaveWork,Integer configId);

//    Map<String, Object> initWorkHoursAndLeaveDuration(PrintWriter writer);

    Map<String, Object> checkAttendanceUpdate(Integer oid);

    Map<String, Object> updateTimeTable(User user,String time);

    List<PersonnelAttendanceUser> getPersonnelAttendanceUserByOpenDate(Integer org, Integer dept, Integer userId, Date openDate);

//    void addDefaultClockRecord(PersonnelOvertime outTime,Integer type,PersonnelAttendanceUser personnelAttendanceUser,Integer detailId);  //默认打卡得

//    void overPunchTurn(Integer overUserId, Date punchTime, Byte punchType, Byte detailPunchType, Integer detailId, Integer overtimeId,Byte type);

//    PersonnelAttendanceUserDetailPunch getUserDetailPunch(Integer org,Integer detailId, Byte detailPunchType,Date punchTime, String createName);  //获取员工考勤明细打卡对照信息

    List<PersonnelAttendanceUserDetailPunch> getUserDetailPunchList(Integer org,Integer detailId, Byte detailPunchType, Date punchTime, String createName);

    Map<String, Object> getClockRecord(Integer userId,Byte punchType,Integer personnelAttendanceUserDetailId,Integer order);

    PersonnelAttendanceTerminal getPersonnelAttendanceTerminal(String terminalUuid,String brand,String model,Byte type);

    List<PunchDto> getPersonnelAttendancePunches(Date start, Period period, Date end, PunchDto search, User user, PageInfo pageInfo);



}