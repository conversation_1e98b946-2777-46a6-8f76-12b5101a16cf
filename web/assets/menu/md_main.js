function initBounceFloating() {
    var n = $("#msgNum").html();
    var bnsShow = localStorage.bnsShow
    if (bnsShow) {
        if (Number(bnsShow) === 1) {
            $(".bounceFloating").show('fast')
        } else {
            $(".bounceFloating").hide('fast')
        }
    } else {
        if (Number(n) > 0) {
            $(".bounceFloating").show('fast')
            localStorage.bnsShow = 1
        } else {
            $(".bounceFloating").hide('fast')
            localStorage.bnsShow = 2
        }
    }
}

//radio点击前的选中状态
var _is_checked_ = false;
$("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
}).click(function (e) {
    var iput = $(this);
    var isPrevent = iput.attr('isprevent')
    console.log('isPrevent', isPrevent)
    if (_is_checked_ && isPrevent === undefined) {
        //如果radio点击前是选中状态，则取消选中
        iput.prop("checked", false);
    }
});
$("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
});

const passportHost = 'passport.btransmission.com'
const tgCode = 'weChat'
const appId = 'wx6344f9634107c46e'
//showMenuWxMsg=true 显示右上方菜单微信绑定状态；showMenuWxMsg=false 不显示显示右上方菜单微信绑定状态；
const showMenuWxMsg = false
function flushWxMember() {
    let accId = auth.getAcc().id
    return new Promise((resolve, reject) => {
        $.ajax({
            url: $.webRoot + '/tp/getOrGroupByAcc.do',
            type: 'POST',
            beforeSend:function(){},
            data: {
                appId: appId,
                tgCode: tgCode,
            },
            success: function (data) {
                if (data.success > 0) {
                    setWxMember(accId, data.data)
                } else {
                    setWxMember(accId, null)
                }
                resolve(getWxMember(accId))
            },
            error: function (err) {
                console.log('flushWxMember err', err)
                reject(err)
            }
        })
    })
}
function getWxMember(accId) { //没绑定返回null，没调用过后端接口返回undefined
    let ddats = localStorage.getItem('wxMsg')
    ddats = (ddats && JSON.parse(ddats)) || {}
    if(ddats[accId] === 'null') {
        return null
    }
    return ddats[accId]
}
function setWxMember(accId, data, keep) {
    let ddats = localStorage.getItem('wxMsg')
    ddats = (ddats && JSON.parse(ddats)) || {}
    if(data===null) {
        data = 'null'
    }
    ddats[accId] = data
    localStorage.setItem('wxMsg',JSON.stringify(ddats))
    if(keep) {
        localStorage.setItem('wxMsgKeep',true)
    } else {
        localStorage.removeItem('wxMsgKeep')
    }
}
function clearWxMember() {
    if(localStorage.getItem('wxMsgKeep')) {
        localStorage.removeItem('wxMsgKeep')
    } else {
        localStorage.removeItem('wxMsg')
    }
}
function getBingWXMsg() {
    let accId = auth.getAcc().id
    let member = getWxMember(accId)
    if(typeof member === 'undefined') {
        flushWxMember().then(member => {
            console.log('getBingWXMsg flushWxMember then', member)
            setMenuWxMsg(member)
        }).catch(err => {
            console.log('getBingWXMsg flushWxMember err', err)
        })
    } else {
        setMenuWxMsg(member)
    }
}
function setMenuWxMsg(member) {
    if(typeof member !== 'undefined'){
        if(member === null) {
            $("#bdWx").html('未绑定')
        } else {
            console.log('已绑定')
            $("#bdWx").html('已绑定')
            // layer.msg('本账号已经绑定微信了!')
        }
    }
}
// //wyu:绑定微信
// function showBindWx() {
//     $.ajax({
//         url: $.webRoot + '/tp/getOrGroupByAcc.do',
//         type: 'POST',
//         beforeSend:function(){},
//         data: {
//             appId: appId,
//             tgCode: tgCode,
//         },
//         success: function (data) {
//             console.log(data)
//             if (data.success > 0) {
//                 console.log('已绑定')
//                 showWx(data.data)
//             } else {
//                 let error = data.error
//                 console.log('未绑定！error msg', error)
//                 switch (error.code) {
//                     case '2': //超过79天，需要短信验证激活
//                         console.log('bingWx')
//                         bingWx()
//                         break
//                 }
//             }
//         }
//     });
// }
// function showWx(member) {
//     var htmlStr =   "<div class='dialog_content' id='showWx'>"+
//         "<p id='pEtip'  ></p>"+
//         "<p class='pe1 pE oline'><span>当前微信：</span>"+member.nickName+"</p>"+
//         "<p class='pe1 pE oline'><span>头像：</span><img src='"+member.avatar+"'>" +
//         "</p>" +
//         "</div>";
//     dialogPE.kendoDialog({
//         width: "450px",
//         // buttonLayout:"normal",
//         title: "绑定微信",
//         closable: true,
//         modal: true,
//         content: htmlStr,
//         actions: [{
//             text: "解除绑定",
//             action: function () {
//                 $.ajax({
//                     url: $.webRoot + '/tp/tpUnBindAcc.do',
//                     type: 'POST',
//                     data: {
//                         appId: appId,
//                         tgCode: tgCode,
//                     },
//                     success: function (data) {
//                         console.log(data)
//                         if (data.success > 0) {
//                             console.log('操作成功')
//                             location.href = $.webRoot + '/sys/index.do'
//                         } else {
//                             let error = data.error
//                             console.log('操作失败！error msg', error)
//                         }
//                     }
//                 });
//             }
//         }],
//         close: function () {
//             this.destroy();
//         }
//     }).data("kendoDialog").open();
// }
// function bingWx() {
//     var htmlStr =   "<div class='dialog_content' id='wxBind'>"+
//         "</div>";
//     dialogPE.kendoDialog({
//         width: "450px",
//         // buttonLayout:"normal",
//         title: "绑定微信",
//         closable: true,
//         modal: true,
//         content: htmlStr,
//         actions: [  ],
//         close: function () {
//             this.destroy();
//         }
//     }).data("kendoDialog").open();
//     $(".k-dialog-title").css({ "font-size":"14px"});
//     console.log(location.protocol+'//'+passportHost+'/'+$.webRoot.substring($.webRoot.indexOf('://')+2)+'/sys/index.do')
//     var obj = new WxLogin({
//         self_redirect:false,
//         id:"wxBind",
//         appid: appId,
//         scope: "snsapi_login,snsapi_userinfo",
//         redirect_uri: encodeURIComponent(location.protocol+'//'+passportHost+'/'+$.webRoot.substring($.webRoot.indexOf('://')+2)+'/sys/index.do'),
//         state: auth.getToken(),
//         style: "",
//         href: ""
//     });
//     console.log('WxLogin1', obj)
// }


$(function(){
    if(showMenuWxMsg) {
        getBingWXMsg()
    }

    // creator: hxz 2018-09-21  悬浮窗显示与隐藏
    $("#faEnvelope").click(function (e) {
        var bnsShow = localStorage.bnsShow
        if (Number(bnsShow) === 1) {
            $(".bounceFloating").hide('fast')
            localStorage.bnsShow = 2
        } else {
            $(".bounceFloating").show('fast')
            localStorage.bnsShow = 1
        }
        return false
    });

    if (sphdSocket.user.roleCode === 'super' || sphdSocket.user.roleCode === 'staff' || sphdSocket.user.roleCode === 'see' || sphdSocket.user.roleCode === 'smallSuper') {
        $("#discussion").show()
    } else {
        $("#discussion").remove()
    }

    // creator: hxz 2019-07-17 点击处理框外， 隐藏处理窗
    $("body").click(function(e){
        localStorage.bnsShow = 2;
        $(".bounceFloating").hide('fast')
    })
    // 实例化上传头像
    $('#peImgFile').Huploadify({
        auto: true,
        fileTypeExts: '*.png;*.jpg;*.bmp;*.gif;',
        fileSizeLimit: 40960,  // 300M = ( 300 * 1024 * 1024 ) B
        showUploadedSize: true,
        removeTimeout: 99999999,
        multi: false,
        buttonText: "添加",
        formData:{
            module: '职工头像',
            userId: sphdSocket.user.userID
        },
        queueID: 'fileQueue',
        uploader: $.webRoot + "/uploads/uploadfyByFile.do",
        fileObjName:'file',
        onSelect: function () {
        },
        onUploadError: function () {
            layer.msg("上传失败！");
        },
        onUploadSuccess: function (file, json) {
            var data = JSON.parse(json)
            if( typeof data.filename === 'string' && data.filename.length > 0 ) {
                let path = $.fileUrl + data.filename
                var fileUid = 'upload/' + data.fileUid;
                var pathOral = data.filename;
                sphdSocket.user.imgPath = path;
                $("#loginUserIMG2").attr("src", path);
                $("#loginUserIMG").attr("src", path);
                $(".floatingContainer iframe").contents().find(".user_logo").attr("src", path);

                updateUserImg(pathOral, fileUid);
            }
        },
        onQueueComplete:function() {
        }
    })

    $(".principal_list").on("click", '.principal_item', function () {
        let loginUserId = $(this).data('id');
        changeManager(loginUserId);
    });
    // console.log('sphd_user',sphdSocket.user)

    //wyu：订阅并处理当前身份被换推送。
    sphdSocket.subscribe('changeRolePrincipals',function (res) {
        // console.log('changeRolePrincipals', res)
        localStorage.setItem("rolePrincipals",res);
        addRolePrincipals(JSON.parse(res))
    },null,'user');

    sphdSocket.subscribe('recoveryStaff',recoveryStaff,null,'user');
    function recoveryStaff(loginUserId) {
        // console.log(loginUserId,isNan(loginUserId));
        if (!isNaN(loginUserId)) {//wyu：切换用户
            changeManager(loginUserId);
        }
    }
});


function changeManager(loginUserId) {
    let form = $('<form></form>');
    form.attr('action', $.webRoot + '/sys/sureLogin.do');
    form.attr('method', 'post');
    form.append('<input type="hidden" name="loginUserId" value="' + loginUserId + '" />');
    form.css('display', 'none');
    form.appendTo('body');
    form.submit();
    localStorage.bnsShow = true; // 每次登陆浮窗都显示
    localStorage.removeItem("floating") // 清楚悬浮窗设置的导航（跟返回有关的）
    localStorage.removeItem('floatMenu') // 清除悬浮窗的url
    localStorage.removeItem("mainMenu");
    localStorage.removeItem("messageMenu");
    localStorage.removeItem("userBadgeNumbers");
    localStorage.removeItem("rolePrincipals");
}

// creator: 李玉婷，2020-03-09 10:24:20，输出角色
function roleStr(code, name, state) {
    if (state == "all") {
        switch (code) {
            case 'super':
                return '董事长';
                break
            case 'smallSuper':
                return '总经理';
                break
            case 'general':
                return '总务';
                break
            case 'finance':
                return '财务';
                break
            case 'sale':
                return '销售';
                break
            case 'accounting':
                return '会计';
                break
            case 'agent':
                return name;
                break
            default:
                return '普通员工'
        }
    } else {
        switch (code) {
            case 'super':
                return '董';
                break
            case 'smallSuper':
                return '全';
                break
            case 'general':
                return '总';
                break
            case 'finance':
                return '财';
                break
            case 'sale':
                return '销';
                break
            case 'accounting':
                return '会';
                break
            case 'agent':
                return name;
                break
            default:
                return '普通员工'
        }
    }
}

function updateUserImg(path , fileUid) {
    let userID = sphdSocket.user.userID
    $.ajax({
        "url":"../general/updateUserimgByUserID.do",
        "data":{"path":path , "userId":userID},
        beforeSend:function () {
        },
        success:function(res){
            cancelFileDel({'type':'fileId', 'fileId':fileUid}, true)
        },
        error:function(){

        }
    })
}
// creator : hxz 2018-09-21 关闭浮窗
function minFloating() {
    localStorage.bnsShow = 2
    $(".bounceFloating").hide('fast')
}
/* creator:hxz 2019-07-18 修改密码  */
var dialogPE = $('#dialog') ;
function passwordEditBtn(e) {
    e && e.stopPropagation();
    var mobile = auth.getAcc().mobile
    var htmlStr =   "<div class='dialog_content'>"+
                    "<p id='pEtip'  ></p>"+
                    "<p class='pe1 pE oline'><span>账号：</span><input type='text' class='pEmobile' value='"+ mobile +"' readonly></p>"+
                    "<p class='pe1 pE oline'><span>验证码：</span><input type='text' class='pECode'>" +
                    "<span class='ri k-button' onclick='sendVCode()'>获取验证码</span>" +
                    "</p>" +
                    "<div class='pe1 pE oline k-button k-succes' onclick='checkVCode(2)'>确定</div>" +
                    "<p class='pe2 oline ty-color-orange' style='text-align: center;'>请您设置密码，以便登录更快捷。</p>"+
                    "<p class='pe2 pE oline'><span>密码：</span><input type='password' id='pEmm' placeholder='8-16位字符' onkeyup='clearChinese(this)'>" +
                    "<i class='ri fa fa-times' onclick='deAll($(this))'></i>" +
                    "</p>"+
                    "<p class='pe2 pE oline'><span>确认密码：</span><input type='password' id='pEokmm' placeholder='8-16位字符' >" +
                    "<i class='ri fa fa-times' onclick='deAll($(this))'></i>" +
                    "</p>"+
                    "<p class='pe2 pe2Tip oline'>注：密码需为8-16位，必须包括数字和英文字母，英文字母分大小写。</p>"+
                    "<div class='pe2 pE oline k-button k-succes' onclick='reSetOk()'>设置完毕，重新登陆</div>" +
                    "</div>";
    dialogPE.kendoDialog({
        width: "450px",
        // buttonLayout:"normal",
        title: "修改密码",
        closable: true,
        modal: true,
        content: htmlStr,
        actions: [  ],
        close: function () {
            this.destroy();
        }
    }).data("kendoDialog").open();
    $(".k-dialog-title").css({ "font-size":"14px"});
}
/*creator: hxz 2019-07-15 清空输入 */
function reSetOk(num) { // num : 1=修改手机号 2=修改密码
    var mm = $("#pEmm").val();
    var okmm = $("#pEokmm").val();
    var isOk = isPassword(mm) ;
    if(mm !== okmm){
        $("#pEtip").html("两次输入的密码不一致，请重新设置！");
        return false;
    }
    if(!isOk){
        $("#pEtip").html("您设置的密码过于简单，请重新设置！");
        return false;
    }
    var json = {}, url = "", password = mm, up_ph = "" ;
    if (num == 1) {
        up_ph = $("#newPhone").val();
        url  = "../userInfo/updateUserPhonePassWord.do";
        json = { "userId": sphdSocket.user.userID,"phone": up_ph , "password":password, code: $(".pe2").data("code")  };
    }else{
        url  = "../auth/resetLoginPassWord.do";
        json = { "phone": $(".pEmobile").val() , "password":password, code: $(".pe2").data("code")  };
    }
    $.ajax({
        "url": url,
        "data": json,
        success:function(res){
            // true 设置成功 false 设置失败
            dialogPE.data("kendoDialog").close();
            $("#peRes").val(res);
            var htmlStr = "";
            if (num == 1) {
                var suc =
                    "<div class='ty-center'>" +
                    "<p>修改成功！</p>" +
                    "<p>再登录系统，请使用"+ up_ph +"账号！</p>" +
                    "<p>退出中……</p>" +
                    "</div>";
                htmlStr = res ? suc : "<p style='text-align:center; '>设置失败，请重新设置！</p>" ;
                htmlStr += "<div class='btn-rt'><div class='k-button up-succes' onclick='peClose()'>我知道了</div></div>" ;
            } else {
                htmlStr = res ? "<p style='text-align:center; '>您wonderss系统的密码已修改，请重新登录。</p>" : "<p style='text-align:center; '>设置失败，请重新设置！</p>" ;
                htmlStr += "<div class='pE k-button k-succes' onclick='peClose()'>确定</div>" ;
            }
            dialogPE.kendoDialog({
                width: "450px",
                title: "！ 提示",
                closable: true,
                modal: true,
                content: htmlStr,
                actions: [  ],
                close: null
            }).data("kendoDialog").open();
            $(".k-dialog-title").css({"font-size":"14px", "color":"#ed5565"});
        }
    });
}
function peClose() {
    var res = $("#peRes").val();
    if(res == "true"){
        loading.open();
        setTimeout(function(){
            location.href = "../sys/logout.do";
        },3000);

    }else{
        dialogPE.data("kendoDialog").close();
    }
}
// creator: hxz ，2019-07-04 09:18:51，校验密码
function isPassword(val) {
    // var reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/;
    // var reg = /^(?!.*\s)(?!^[\u4e00-\u9fa5]+$)(?!^[0-9]+$)(?!^[A-z]+$)(?!^[^A-z0-9]+$)^.{8,16}$/;
    var reg = /^(?=.*\d)(?=.*[a-zA-Z]).{8,16}$/;
    return reg.exec(val)
}
/*creator: hxz 2019-07-15 清空输入 */
function deAll(obj) {
    obj.prev("input").val("");
}
/*creator: hxz 2019-07-13 发送验证码 */
function sendVCode(num) {
    // updater: zxb 修改账号获取验证码用新的接口 2021/6/17
    var url = '../auth/sendMessageVerificationCode.do'
    if (num) {
        url = '../auth/editMobileSendMessageVerificationCode.do'
    }
    $.ajax({
        "url": url,
        "data":{ "phone": $(".pEmobile:visible").val() },
        beforeSend:function(){ },
        success:function(res){
            // 1- 成功（手机将收到带验证码的短信） 2- 手机号不是11位 3- 手机号格式不对 4- 非系统内手机号
            var content = "" ;
            switch (Number(res["data"])){
                case 1: content = "发送成功！" ; break;
                case 2: content = "手机号不是11位！" ; break;
                case 3: content = "手机号格式不对！" ; break;
                case 4: content = "非系统内手机号！" ; break;
                case 5: content = "验证码已经发过还未过时" ; break;
                default:content = "未知错误！" ;
            }
            if (content != "") {
                $("<div id='sendVCodeTip' class='ty-center'></div>").kendoAlert({
                    minWidth: "200px",
                    title: "！提示",
                    content: content
                }).data("kendoAlert").open();
            }
        }
    });
}
/*creator: hxz 2019-07-13 检验验证码  */
function checkVCode(step) {
    var pECode = $(".pECode:visible").val();
    if (pECode != "") {
        $("#pEtip").html("");
        $.ajax({
            "url":"../sendMessage/checkVerificationCode.do",
            "data":{ "phone":$(".pEmobile:visible").val() , "verificationCode":pECode },
            success:function(res){
                // 返回值： true- 验证成功 false-验证失败
                if(res){
                    $(".oline").hide();
                    $(".pe" + step).show();
                    $(".pe2").data("code", pECode)
                    if (step == 3){
                        var ph = $("#newMobile").val();
                        $("#newPhone").val(ph);
                        dialogPE.data("kendoDialog").title(' 设置密码');
                    }
                }else{
                    $("#pEtip").html("您输入的验证码有误！");
                }
            }
        })
    }
}
// creator: hxz 2019-07-17  换头像
function changeImgBtn(type) {
    if(type){
        $(".control1 .changeImgBtn").show();
    }else{
        $(".control1 .changeImgBtn").hide();
    }
}
// creator: hxz 2019-07-17  上传头像showBindWx()
function upPeImg(e) {
    // $("#peImgFile").click()
    $("#file_upload_1-button").click();
    e.stopPropagation();
}
// creator: hxz 2019-07-17  初始化悬浮窗位置
var float = {}

// creator:hxz 2018-09-25 改变浮窗头部高度 , 带头像的传(115,314) ， 不带头像的传(57,252)
function changeFloatingHeader(height, width, right) {
    $(".floatingContainer bonceHead").css({"height": height + "px", "width": width, "right": right});
}
// creator: hxz 2019-07-13 跳转关于
function g2(e , num) {
    $(".userControl .control"+ num).show().siblings().hide();
    if (num === 2) {
        let about = localStorage.getItem("about")
        let listFirstFolder = []
        if (about) {
            listFirstFolder = JSON.parse(about)
            setAboutList(listFirstFolder)
        } else {
            $.ajax({
                url: '../about/getAboutInitialDirectory.do',
                success: function (res) {
                    let listFirstFolder = res.data.listFirstFolder
                    localStorage.setItem("about", JSON.stringify(listFirstFolder))
                    setAboutList(listFirstFolder)

                }
            })
        }

    }
    e.stopPropagation();
}
// creator: 张旭博，2021-10-12 10:29:25，关于列表字符串
function setAboutList(listFirstFolder) {
    let iconArr = ['bars', 'bell-o', 'info', 'book']
    var str = '<a href="'+$.webRoot+'/about/scanDownload.do"><i class="fa fa-download"></i>扫描下载</a>'
    for (let i = 0; i<listFirstFolder.length; i++) {
        str += '<a href="'+$.webRoot+'/about/aboutCommon.do?id='+listFirstFolder[i].id+'"><i class="fa fa-'+iconArr[i]+'"></i>'+listFirstFolder[i].name+'</a>'
    }
    $(".userControl .control2 .bigLi>div").html(str)
}
// creator : hxz 2018-07-25 判断当前的登录的角色
function chargeRole(roleStr) { // roleStr 值：“超管”, “总务”, “财务”, “销售”, “超级浏览者”, “普通员工”,  “会计”,  “代理会计”,  “代理小会计”,  “小超管”,
    var roleCodeList = [
        {"name": "超管", "code": "super"},
        {"name": "总经理", "code": "smallSuper"},
        {"name": "总务", "code": "general"},
        {"name": "财务", "code": "finance"},
        {"name": "销售", "code": "sale"},
        {"name": "超级浏览者", "code": "browse"},
        {"name": "浏览者", "code": "browse"},
        {"name": "普通员工", "code": "staff"},
        {"name": "会计", "code": "accounting"},
        {"name": "代理会计", "code": "agentAccounting"},
        {"name": "代理小会计", "code": "agentSmallAccounting"},
        {"name": "小超管", "code": "smallSuper"}
    ];
    var roleCode = sphdSocket.user.roleCode;
    for (var i = 0; i < roleCodeList.length; i++) {
        if (roleCodeList[i]["name"] == roleStr) {
            if (roleCode == roleCodeList[i]["code"]) {
                return true;
            } else {
                return false;
            }
        }
    }
}
function getAvatarPath(gender, type) {
    var path = ''
    if (gender === '1') {
        gender = 'male'
    } else if (gender === '0') {
        gender = 'female'
    } else {
        gender = 'default'
    }
    path = $.webRoot + '/assets/images/avatar/avatar_' + gender + (type? '_'+type: '') + '.png'
    return path
}
// 创建菜单
function addMenu(){
    console.log('mainMenu', localStorage.getItem('mainMenu'))
    var list = JSON.parse(localStorage.getItem('mainMenu'));
    var loginUser = auth.getUser()//sphdSocket.user;
    var org = auth.getOrg()//sphdSocket.org;
    $("#loginUser").html(JSON.stringify(loginUser)) ;
    creatMenu(list,$("#mainMenu"));
    setFirstkidSelected($("#mainMenu"));

    var gender = loginUser.gender
    $("#loginUserIMG, #loginUserIMG2").on("error", function () {
        $(this).attr("src", getAvatarPath (loginUser.gender, 'small'))
    })
    let path = loginUser.imgPath ? $.fileUrl + loginUser.imgPath : getAvatarPath (loginUser.gender, 'small')
    $("#loginUserIMG, #loginUserIMG2").attr("src" , path );
    $("#loginUserNAME").attr( "title" , loginUser.userName ) ;
    $("#loginUserNAME2").attr( "title" , loginUser.userName ) ;
    $("#loginUserNAME").html( loginUser.userName ) ;
    $("#loginUserNAME2").html( loginUser.userName ) ;
    $("#loginUserPhone").html( auth.getAcc().mobile ) ;
    $("#loginUserType").html(loginUser.roleCode);   // chargeRole 方法里面查看对应类型值


    $("#orgName").html("（"+ org.name+"）" )
    console.log("auth.getAcc().mobile",auth.getAcc().mobile)
    $(".accMobile").html(auth.getAcc().mobile)
    console.log(loginUser)
    console.log(typeof loginUser)
    console.log(loginUser.userName)
    //页面加载时需要使用此接口返回值的函数
    if(typeof successUserTypeInitPage === "function") {	successUserTypeInitPage()	}
    //超级浏览者登录隐藏讨论区
    if (chargeRole("超级浏览者") || chargeRole("代理会计") || chargeRole("代理小会计")) {
        $("#discussion").hide();
    }else{
        $("#discussion").show();
    }


}

// creator: 张旭博，2020-11-12 14:42:03，创建身份
function addRolePrincipals(roleList) {
    var str = ''
    var recent = roleList.findIndex(item => item.userID === sphdSocket.user.userID);
    for (var i = 0; i<roleList.length; i++) {
        if (i == recent) {
            $(".middle-menu .principal_recent").html(roleStr(roleList[i].roleCode, roleList[i].roleName, "all"))
        } else {
            if (i === 6) {
                str += '<div class="dropDown">更多 <i class="fa fa-angle-down"></i><div class="dropDownMenu">'
            }
            str +=  '<div class="principal_item" data-id="'+roleList[i].userID+'" title="您在系统中有其他身份，请点击进入，以便及时处理与您有关的事务。">'+
                roleStr(roleList[i].roleCode, roleList[i].roleName) + '(<span class="msgCount">' +roleList[i].msgCount +'</span>)'+
                '</div>'

            if (roleList.length > 6 && i === roleList.length-1) {
                str += '</div></div>'
            }
            sphdSocket.subscribe('updateUserBadgeNumbers', function (data) {
                // console.log('PC updateUserBadgeNumbers', data)
                var getData = JSON.parse(data)
                var $dom = $(".page-header-inner .principal_list .principal_item[data-id='"+getData.userId+"'] .msgCount")
                if ($dom.length > 0) {
                    var oldVer = $dom.attr("ver") || 0
                    if (oldVer < getData.ver) {
                        $(".page-header-inner .principal_list .principal_item[data-id='"+getData.userId+"'] .msgCount").html(getData.list.handle).attr('ver', getData.ver)
                    }
                }
            }, null, 'custom', roleList[i].userID)
        }

    }
    $(".middle-menu .principal_list").html(str)
}


/*
	创建导航树须知：1. 只要菜单下面还有子菜单，这条菜单记录的 isParent就应该为1 ； 
					2. mid 作为 本菜单的id
					3. pid 作为 父级id 
					4. 其他字段不再介绍，依照明德教育原表设计 
 */
function creatMenu(menuObj,creatObj){
	var _creatObj = creatObj ;
    // var menu =[
    //     {
    //         code: "a1", icon: "tykj tykj-authority", isMenu: 1, isParent: true, isView: "1", mid: "AA", name: "一级菜单", orders: 10, parent: true, pid: "0", target: "mianFrame", url: ""
    //     },
    //     {
    //         code: "a1", icon: "tykj tykj-authority", isMenu: 1, isParent: true, isView: "1", mid: "AB", name: "二级菜单", orders: 10, parent: true, pid: "AA", target: "mianFrame", url: ""
    //     },
    //     {
    //         code: "a1", icon: "tykj tykj-authority", isMenu: 1, isParent: true, isView: "1", mid: "AC", name: "二级菜单222", orders: 10, parent: true, pid: "AA", target: "mianFrame", url: "../loan/ordinaryLoanList.do"
    //     },
    //     {
    //         code: "a1", icon: "tykj tykj-authority", isMenu: 1, isParent: true, isView: "1", mid: "ABA", name: "三级菜单", orders: 10, parent: true, pid: "AB", target: "mianFrame", url: ""
    //     },
    //     {
    //         code: "a1", icon: "tykj tykj-authority", isMenu: 1, isParent: true, isView: "1", mid: "ABA", name: "三级菜单2", orders: 10, parent: true, pid: "AB", target: "mianFrame", url: "../loan/ordinaryLoanList.do"
    //     },
    //     {
    //         code: "a1", icon: "tykj tykj-authority", isMenu: 1, isParent: true, isView: "1", mid: "ABAB", name: "四级菜单", orders: 10, parent: true, pid: "ABA", target: "mianFrame", url: "../loan/ordinaryLoanList.do"
    //     }
    // ];
    // var list = menu.concat(menuObj);
    var list = menuObj || [];
    var str;
	if (list.length > 0) {
		for (var i = 0; i < list.length; i++) {
			if (list[i]["isMenu"]) {
                if(list[i]["pid"]=='0') {
                    if (!list[i]["url"]) {
                        str = "<li onclick='setMenuCk($(this))' class='nav-item '>"
                            + " <a href='javascript:void()' class='nav-link nav-toggle'> <i class='" + list[i]["icon"] + "'></i> <span class='title'>" + list[i]["name"] + "</span><span class=''></span> <span class='arrow hd menu_mid'>" + list[i]["mid"] + "</span> </a>"
                            + " <ul class='sub-menu' id='" + list[i]["mid"] + "'></ul>"
                            + "</li>";
                    } else {
                        str = "<li onclick='setMenuCk($(this))' class='nav-item '>"
                            + " <a data-info ='" + JSON.stringify(list[i]) + "'  onclick='aHref($(this))' class='nav-link nav-toggle'> <i class='" + list[i]["icon"] + "'></i> <span class='title'>" + list[i]["name"] + "</span><span class=''></span> <span class='arrow hd menu_mid'>" + list[i]["mid"] + "</span> </a>"
                            + "</li>";
                    }
                    _creatObj.append(str);
                }else{
                	if(!list[i]["url"]) {
						str = "<li onclick='setMenuCk($(this))' class='nav-item  '> <a href='javascript:void()' class='nav-link '> <span class='title'>"+list[i]['name']+"</span> <span class='hd menu_mid'>"+ list[i]["mid"] +"</span> </a>"
							+ " <ul class='sub-menu' id='" + list[i]["mid"]+"'></ul>"
							+ "</li>";
					}else{
                        str = "<li onclick='setMenuCk($(this))' class='nav-item  '> <a data-info ='" + JSON.stringify(list[i]) + "' onclick='aHref($(this))' class='nav-link '> <span class='title'>"+list[i]['name']+"</span> <span class='hd menu_mid'>"+ list[i]["mid"] +"</span> </a>"
                            + " <ul class='sub-menu' id='" + list[i]["mid"]+"'></ul>"
							+ "</li>";
					}
					var addObj = $("#"+list[i]["pid"]);
					addObj.append(str);
					addObj.closest('li').attr('onclick','setMenuCk($(this))');
                    var aObj=addObj.closest('li').children('a');
                    aObj.attr('href','javascript:void()');
                    aObj.addClass('nav-toggle');
				}
			}
		}
	}

}
// 跳转没有菜单的 重构页面
function aHrefNoMenu(thisObj){
    let routerStr = thisObj.data('info')
    // http://127.0.0.1:8081/vue/minersFrontEnd/dist/index.html#/desktop
    let allUrl = $.webRoot + '/vue/minersFrontEnd/dist/index.html#/' + routerStr;
    location.href = allUrl
}

function aHref(thisObj) {
    let info = thisObj.data('info')
    if(info?.url?.length>4) {

        let url = $.webRoot + (info.url.startsWith('..') ? info.url.substring(2) : info.url)
        // let mid = info.mid
        // console.log('aHref ==', url, mid)
        // let rePages = pageMenus
        // console.log('rePages=', rePages)
        // let goRePage = false
        // let urlName = ''
        // rePages.forEach( reP => {
        //     if(reP.mid === mid){
        //         goRePage = true
        //         urlName = reP.pageName
        //     }
        // })
        // if(goRePage){
        //     // console.log('jumpName', urlName)
        //     // localStorage.setItem('jumpName', urlName);
        //     // let newUrl = $.webRoot + '/vue/minersFrontEnd/dist/index.html#/mainPage' + urlName
        //     // let newUrl = $.webRoot + '/vue/minersFrontEnd/dist/index.html#/mainPage'
        //     // https://hxz-t.frp.btransmission.com/vue/minersFrontEnd/dist/index.html#/systemSet
        //     // https://hxz-t.frp.btransmission.com/vue/minersFrontEnd/dist/undefined
        //     // location.href = newUrl
        //     location.href = $.webRoot + '/vue/minersFrontEnd/dist/index.html#/'+urlName
        // }else {
        location.href = url
        // }
    }
}
/* updater：hxz，2019-07-22 08:34:16，侧边导航设置选中  */
function setFirstkidSelected(obj){
    for(var i= 1; i < 5; i++){ // 目前支持到四级
        // var menuID = $.cookie("level" + i);
        var menuID =  localStorage.getItem('level' + i);
        // console.log("level" + i , menuID);
        if(menuID != ""){
            $(".menu_mid").each(function(){
                if ($(this).html() === menuID) {
                    $(this).parent().parent().attr("class","nav-item start active open");
                    $(this).siblings(":last").attr("class","selected");
                }
            });
        }
    }

	/*var pid = $.cookie("pid");	//一级菜单
	var sid = $.cookie("sid");	//最后一级
	if (pid === null || pid === undefined || pid === "") {
		obj.children(":eq(3)").attr("class","nav-item start active open")
			.children("ul").children(":eq(0)").attr("class","nav-item start active open")
			.children("ul").children(":eq(0)").attr("class","nav-item start active open");
		pid = obj.children(":eq(3)").children("a").children(".menu_mid").html();
        sid = obj.children(":eq(3)").children("ul").children(":eq(0)").children("a").children(".menu_mid").html();
        $.cookie('pid', pid, { path: '/' });
        $.cookie('sid', sid, { path: '/' });
	}else{
        if (sid !== null && sid !== undefined && sid !== "") {
            $(".menu_mid").each(function(){
                if ($(this).html() === sid) {
                    $(this).parents("li").attr("class","nav-item start active open");
                }
            });
        }else {
            $(".menu_mid").each(function(){
                if ($(this).html() === pid) {
                    $(this).parent().parent().attr("class","nav-item start active open");
                    $(this).siblings(":last").attr("class","selected");
                }
            });
        }


	}*/
}
function setMenuCk(obj , e){
    // e.stopPropagation();
    var level = obj.parents("ul").length ;
    var mid = obj.children("a").children(".menu_mid").html();
    // console.log(level , mid);
    // $.cookie('level' + level, mid, { path: '/' });
    localStorage.setItem('level' + level, mid)
}
// creator : hxz 2019-06-004 从浮窗列表 跳转到 详情页（主页面的）
function floatToPage(action, params, setIframeStyle) {
    console.log('params=', params)
    var urlStr = "" + action ;
    if(params){
        urlStr += "?" ;
        for(var key in params) {
            // console.log(key, params[key]);
            urlStr += key + "=" + params[key] + "&" ;
        }
        urlStr = urlStr.substr(0, urlStr.length-1) ;
    }
    if(setIframeStyle){
        let styleStr = ``
        for(var key in setIframeStyle) {
            styleStr += `${ key } : ${ setIframeStyle[key] };`
        }
        $("#somethingItemDetailsPage iframe").attr('style',styleStr)
    }else{
        $("#somethingItemDetailsPage iframe").removeAttr('style')
    }
    // location.href= urlStr ;
    console.log('urlStr=', urlStr)
    $("#somethingItemDetailsPage iframe").attr("src",urlStr);
    //
    $("#somethingItemDetailsPage").css("display","flex");
    // $("#somethingItemDetailsPage").show();

}
function hideDetailsPage(){
    $('#somethingItemDetailsPage').css('display','none');
    $("#somethingItemDetailsPage iframe").removeAttr("src");
}

// 加载菜单
addMenu();

var roleList = JSON.parse(localStorage.getItem('rolePrincipals'));
addRolePrincipals(roleList)