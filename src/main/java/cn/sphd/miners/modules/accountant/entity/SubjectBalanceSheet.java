package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import java.math.BigDecimal;

/**
 * Created by root on 17-5-17.
 * 科目余额表、试算平衡表，都能使用该实体类
 * 主要是存放科目余额表的显示字段，这些字段实际上来自两个表，科目表和科目周期表
 * 这个实体类综合了两个表的数据，所以不是从数据库生成的
 */
@Alias("balanceSheet")
public class SubjectBalanceSheet {

//    private int id;
    private String subject;//科目代码，来自科目表
    private String subName;//科目名称，来自科目表
    private BigDecimal beginningBalance;//年初余额，来自科目表
    private String beginningDirection;//年初余额方向，来自科目表
    private BigDecimal previousBalance;//期初余额，来自科目表
    private String previousDirection;//期初余额方向，来自科目表
    private BigDecimal creditAccumulative;//本年累计借，来自科目表
    private BigDecimal debitAccumulative;//本年累计贷，来自科目表
    private String subDirection;//余额方向，来自科目表
    private int level;//科目级别，来自科目表
    private int oid;//机构id
    private String parent;//父科目编号
    private int maxChildSubjects;//该科目所拥有的子科目数

    private BigDecimal credit;//本期借方发生额，来自科目周期表
    private BigDecimal debit;//本期贷方发生额，来自科目周期表
    private BigDecimal balance;//期末余额，来自科目周期表
    private String peroid;//周期数，要查询的时间，如：201705，默认是当月，来自科目周期表
    private String balanceDirection;//期末余额方向，来自科目周期表
    private int subjectId;
    private String curYear;

    public int getMaxChildSubjects() {
        return maxChildSubjects;
    }

    public void setMaxChildSubjects(int maxChildSubjects) {
        this.maxChildSubjects = maxChildSubjects;
    }

    public String getCurYear() {
        return curYear;
    }

    public void setCurYear(String curYear) {
        this.curYear = curYear;
    }

    public int getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(int subjectId) {
        this.subjectId = subjectId;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public String getBalanceDirection() {
        return balanceDirection;
    }

    public void setBalanceDirection(String balanceDirection) {
        this.balanceDirection = balanceDirection;
    }

    public BigDecimal getBeginningBalance() {
        return beginningBalance;
    }

    public void setBeginningBalance(BigDecimal beginningBalance) {
        this.beginningBalance = beginningBalance;
    }

    public String getBeginningDirection() {
        return beginningDirection;
    }

    public void setBeginningDirection(String beginningDirection) {
        this.beginningDirection = beginningDirection;
    }

    public BigDecimal getPreviousBalance() {
        return previousBalance;
    }

    public void setPreviousBalance(BigDecimal previousBalance) {
        this.previousBalance = previousBalance;
    }

    public String getPreviousDirection() {
        return previousDirection;
    }

    public void setPreviousDirection(String previousDirection) {
        this.previousDirection = previousDirection;
    }

    public BigDecimal getCreditAccumulative() {
        return creditAccumulative;
    }

    public void setCreditAccumulative(BigDecimal creditAccumulative) {
        this.creditAccumulative = creditAccumulative;
    }

    public BigDecimal getDebitAccumulative() {
        return debitAccumulative;
    }

    public void setDebitAccumulative(BigDecimal debitAccumulative) {
        this.debitAccumulative = debitAccumulative;
    }

    public String getSubDirection() {
        return subDirection;
    }

    public void setSubDirection(String subDirection) {
        this.subDirection = subDirection;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getPeroid() {
        return peroid;
    }

    public void setPeroid(String peroid) {
        this.peroid = peroid;
    }

    public int getOid() {
        return oid;
    }

    public void setOid(int oid) {
        this.oid = oid;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
}
