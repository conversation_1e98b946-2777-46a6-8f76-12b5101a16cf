package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.Accountant1_209_Service;
import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.equipment.service.ITEquFixedAssetsService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional(readOnly=false)
public class Accountant1_209_ServiceImpl implements Accountant1_209_Service {

    @Autowired
    TaccountantSubjectJournalMapper subjectJournalMapper;
    @Autowired
    TaccountantSubjectRecordMapper subjectRecordMapper;
    @Autowired
    TaccountantSubjectChangeMapper subjectChangeMapper;
    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    BuildAccountMapper buildAccountMapper;
    @Autowired
    SubjectSelectMapper subjectSelectMapper;
    @Autowired
    TAccountSubjectMapper subjectMapper;
    @Autowired
    TaccountantSubjectRecordMapper recordMapper;
    @Autowired
    SettleMapper settleMapper;
    @Autowired
    SubjectSettingService settingService;
    @Autowired
    DataService dataService;
    @Autowired
    GeneralSubjectRecord generalSubjectRecord;
    @Autowired
    TaccountantEstablishEntityMapper establishEntityMapper;
    @Autowired
    VoucherUpdate voucherUpdate;
    @Autowired
    SubjectPeroidMapper peroidMapper;


    @Autowired
    ITEquFixedAssetsService fixedAssetsService;
    /*
     * 获取科目变动记录
     * */
    @Override
    public List<TaccountantSubjectJournal> getSubjectJournal(User user, Integer establish,PageInfo pageInfo) {

        TaccountantSubjectJournal subjectJournal = new TaccountantSubjectJournal();
        if (establish != null) {
            subjectJournal.setEstablish(establish);
        }
        else {
            Integer eid = generalSubjectRecord.getEstablishByOrg(user.getOid());
            if (eid == null) {
                eid = voucherUpdate.establishCompatible(user.getOid(),user.getUserID(),user.getUserName());
            }
            subjectJournal.setEstablish(eid);
        }
        subjectJournal.setOrg(user.getOid());
//        List<TaccountantSubjectJournal> list = subjectJournalMapper.getSubjectJournal(subjectJournal);
        QueryData param = new QueryData();
        param.put("obj",subjectJournal);
        param.put("pageInfo",pageInfo);
        List<TaccountantSubjectJournal> list = subjectJournalMapper.getSubjectJournallistPage(param);
        return list;
    }

    /*
     * 获取科目变动内容
     * 科目的操作记录
     * establish    建账ID    选填
     * createDate   变动日期   必填
     * */
    @Override
    public List<TaccountantSubjectRecord> getSubjectRecords(User user, TaccountantSubjectRecord subjectRecord) {
        Integer establish = subjectRecord.getEstablish();
        subjectRecord.setCreateDate(subjectRecord.getChangeDate());
//        Date createDate = subjectRecord.getCreateDate();
        if (establish != null) {
            subjectRecord.setEstablish(establish);
        }
        else {
            subjectRecord.setEstablish(1);
        }
        subjectRecord.setOrg(user.getOid());
//        Date date = subjectRecord.getCreateDate();
//        System.out.println(NewDateUtils.dateToString(date,"yyyy-MM-dd"));
        List<TaccountantSubjectRecord> list = subjectRecordMapper.getSubjectRecords(subjectRecord);
        return list;
    }

    /*
     * 获取科目修改前的数据
     * id            查看修改后时传
     * previousId    修改前时数据ID
     * */
    @Override
    public TaccountantSubjectRecordDto getSubjectModifyInfo(TaccountantSubjectRecordDto subjectRecordDto) {
//        Integer id = subjectRecordDto.getId();
//        Integer previousId = subjectRecordDto.getPreviousId();
        subjectRecordDto = subjectRecordMapper.getSubjectModifyInfo(subjectRecordDto);
        if (subjectRecordDto != null) {
//            String parent = subjectRecordDto.getParent();
            TaccountantSubjectRecordDto parentInfo = subjectRecordMapper.getParentInfo(subjectRecordDto);
            if (parentInfo != null) {
                subjectRecordDto.setParentName(parentInfo.getParentName());
                subjectRecordDto.setCategoryName(parentInfo.getCategoryName());
                subjectRecordDto.setBalanceDirection(parentInfo.getBalanceDirection());
            }
        }
        return subjectRecordDto;
    }

    /*
     * 获取变动后的科目或建账后的科目
     * isChanged            必填，0-建账后，1-变动后
     * changeDate           当上一个参数=1时必填，否则不填，修改前时数据ID
     * establish            建账ID
     * category             科目类别
     * */
    @Override
    public List<TaccountantSubjectChange> getChangeSubjects(TaccountantSubjectChange subjectChange) {
        Integer category = subjectChange.getCategory();//科目类别
        if(category == null || category == 0) {
            category = 1;//默认显示资产类科目
        }
        String categoryStr = String.valueOf(category);
        List<Integer> categoryIdlist = settingService.idesBySubjectCategory(categoryStr);//根据科目类别拿到t_accountant_subject_category表的ID列表
        subjectChange.setCategoryList(categoryIdlist);
        List<TaccountantSubjectChange> list = subjectChangeMapper.getChangeSubjects(subjectChange);
        return list;
    }

    /*
     * 获取新凭证管理首页数据
     * */
    @Override
    public VoucherHomeData getVoucherHomeData(User user) {
        //获取结账月份，最近的未结账的月份，settle表无数据的话就是上个月
        Integer org = user.getOid();
        String settleMonth = getSettleMonth(org);//需结账的月份
        String monthParam = settleMonth.replace("月","").replace("年","-");

        //0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证
        List<TAccountantVoucherDto> manualSelectionList = getVoucherByMonth(user.getOid(), monthParam,"0");
        List<TAccountantVoucherDto> accountantSelectionList = getVoucherByMonth(user.getOid(), monthParam,"1");
        List<TAccountantVoucherDto> osSelectionList = getVoucherByMonth(user.getOid(), monthParam,"2");

        Integer manualSelection = manualSelectionList.size();//手动选择的凭证数量
        Integer osSelection = osSelectionList.size();//系统选择的凭证数量
        Integer accountantSelection = accountantSelectionList.size();//会计选择的凭证数量

        // TODO: 以下三个数需要财务那边提供
        Date dateFirst = NewDateUtils.dateFromString(settleMonth + "01日 00:00:00","yyyy年MM月dd日 HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateFirst);
        int cur = calendar.getActualMaximum(Calendar.DATE);//当月最后一天
        calendar.set(Calendar.DATE, cur);
        Date lastTime = NewDateUtils.getLastTimeOfMonth(calendar.getTime());

        Map<String,Object> map = dataService.getNumTotal(org,dateFirst,lastTime);//获取财务统计数据
        Integer billQuantity = (Integer) map.get("numVoucher");//可做凭证的票据总数
        Integer billsNotRecorded = (Integer) map.get("numNotRecorded");//尚未入会计帐的票据
        Integer billsNotAccount = (Integer) map.get("numNoAccounting");//已制作过凭证但选为“不予做账”的

        VoucherHomeData voucherHomeData = new VoucherHomeData();
        voucherHomeData.setSettleMonth(settleMonth);
        voucherHomeData.setBillQuantity(billQuantity);
        voucherHomeData.setManualSelection(manualSelection);
        voucherHomeData.setOsSelection(osSelection);
        voucherHomeData.setAccountantSelection(accountantSelection);
        voucherHomeData.setBillsNotRecorded(billsNotRecorded);
        voucherHomeData.setBillsNotAccount(billsNotAccount);
        return voucherHomeData;
    }

    /*
     * 获取机构当前需要结账的月份
     * */
    public String getSettleMonth(Integer org) {
        String settleMonth = "";//需结账的月份
        TAccountantSettle settle = settleMapper.judgeCharge(org);
        if (settle != null) {
            if (!"3".equals(settle.getState())) {//当前结账表最后一条不是总结账的话就是需要结账的月份，如果没有符合的数据，需要结账的月份就是当前月
                settleMonth = settle.getPeriod();
                settleMonth = settleMonth.replace("-","年") + "月";
            }
            else {
                Date date = NewDateUtils.dateFromString(settle.getPeriod() + "-01","yyyy-MM-dd");
                Date changeDate = NewDateUtils.changeMonth(date,1);
                settleMonth = NewDateUtils.dateToString(changeDate,"yyyy年MM月");
            }
        }
        else {
            Date changeDate = NewDateUtils.changeMonth(new Date(),-1);
            settleMonth = NewDateUtils.dateToString(changeDate,"yyyy年MM月");
        }

        return settleMonth;
    }

    /*
     * 获取指定月份不同类型的凭证
     * month            必填，需结账的月份 yyyy-MM
     * type             0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证
     * */
    @Override
    public List<TAccountantVoucherDto> getVoucherByMonth(User user, String month,String type) {
        List<TAccountantVoucherDto> list = getVoucherByMonth(user.getOid(), month,type);
        for (TAccountantVoucherDto voucherDto : list) {
            voucherDto = getTAccountantVoucherSubject(voucherDto);

            String source = voucherDto.getSource();
            if ("1".equals(source) || "2".equals(source)) {
                voucherDto.setFinanceCreateDate(voucherDto.getAddtime());
                voucherDto.setFinanceCreateName(voucherDto.getOperatorName());
            }
            else if ("0".equals(source) || "3".equals(source)){
                Integer detailId = voucherDto.getDetailId();
                Integer billDetail = voucherDto.getBillDetail();
                Map map = dataService.getDetailBillCreateDate(detailId,billDetail);
                String createName = (String) map.get("createName");
                Date createDate = (Date) map.get("createDate");
                if (createName == null)
                    createName = "会计高管";
                if (createDate == null)
                    createDate = voucherDto.getAddtime();
                voucherDto.setFinanceCreateDate(createDate);
                voucherDto.setFinanceCreateName(createName);
            }

        }
        return list;
    }

    private TAccountantVoucherDto getTAccountantVoucherSubject(TAccountantVoucherDto voucherDto) {
        TAccountantVoucherSubject vs = new TAccountantVoucherSubject();
        vs.setVoucherId(voucherDto.getId());
        vs.setDirection("1");
        List<TAccountantVoucherSubject> listBorrow = voucherSubjectMapper.listByVoucherDirection(vs) ;
        if(listBorrow.size() > 0)
        {
            for (TAccountantVoucherSubject avs : listBorrow ) {
                avs.setSubjectName(getSubjectName(avs));
            }
            voucherDto.setBorrowSubject(listBorrow);

        }
        vs.setDirection("2");
        List<TAccountantVoucherSubject> listLoan = voucherSubjectMapper.listByVoucherDirection(vs) ;
        if(listLoan.size() > 0)
        {
            for (TAccountantVoucherSubject avs : listLoan ) {
                avs.setSubjectName(getSubjectName(avs));
            }
            voucherDto.setLoanSubject(listLoan);
        }

        return voucherDto;
    }

    /*
     * 获取指定月份不同类型的凭证list
     * month            需结账的月份 yyyy-MM
     * type             0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证
     * */
    private List<TAccountantVoucherDto> getVoucherByMonth(Integer org, String month,String type) {
        //source ：0-财务票据，会计手动选择，1-来自会计录入,2-结转损益,系统生成 3-个人报销，系统生成
        TAccountantVoucher param = new TAccountantVoucher();
        if (type != null) {
            switch(type) {
                case "0": {//手动选科目
                    param.setSource("0");
                    break;
                }
                case "1": {//会计录入
                    param.setSource("1");
                    break;
                }
                case "2": {//系统自动选择。source=2或3都是系统自动选择的
                    param.setSource("3");
                    break;
                }
            }
        }
        if (month != null) {
            param.setCreateDate(month);
        }
//        param.setCreateDate("2022-03-31");//票据日期。之前存的是凭证日期，本月或上月最后一天，显改成了票据日期
        param.setOrg(org);
        List<TAccountantVoucherDto> list = voucherMapper.getVoucherBySource(param);
        return list;
    }

    /*
     * 获取结账管理首页数据
     * */
    @Override
    public SettleHomeData getSettleHomeData(User user) {
        Integer org = user.getOid();
        String settleMonth = getSettleMonth(org);//需结账的月份
        String monthParam = settleMonth.replace("月","").replace("年","-");

        //0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证
        List<TAccountantVoucherDto> manualSelectionList = getVoucherByMonth(user.getOid(), monthParam,"0");
        List<TAccountantVoucherDto> accountantSelectionList = getVoucherByMonth(user.getOid(), monthParam,"1");
        List<TAccountantVoucherDto> osSelectionList = getVoucherByMonth(user.getOid(), monthParam,"2");

        Integer manualSelection = manualSelectionList.size();//手动选择的凭证数量
        Integer osSelection = osSelectionList.size();//系统选择的凭证数量
        Integer accountantSelection = accountantSelectionList.size();//会计选择的凭证数量
        Integer monthVoucher = manualSelection + osSelection + accountantSelection;//某月所有凭证

        SettleHomeData settleHomeData = new SettleHomeData();
        settleHomeData.setSettleMonth(settleMonth);
        settleHomeData.setMonthVoucher(monthVoucher);
        settleHomeData.setManualSelection(manualSelection);
        settleHomeData.setOsSelection(osSelection);
        settleHomeData.setAccountantSelection(accountantSelection);
        return settleHomeData;
    }

    @Autowired
    SubjectSelectService subjectSelectService;
    @Override
    public JsonResult test(User user) {
        int SUCCESS = 1;
        int ERROR = 0;

//        peroidMapper.delTestData();
        List<String> list = settingService.getEquipmentToolsSubjectNames(user);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(list);

        return result;
    }

    /*
     * 获取某天的结账记录
     * year     查询年份，不填时默认查询本年度的结账记录
     * */
    @Override
    public List<TAccountantSettle> getSettleListByYear(User user, Integer year) {
        if (year == null)
            year = Calendar.getInstance().get(Calendar.YEAR);
        TAccountantSettle param = new TAccountantSettle();
        param.setOrg(user.getOid());
        param.setPeriod(String.valueOf(year));
        List<TAccountantSettle> list = settleMapper.getSettleListByYear(param);
        return list;
    }

    /*
     * 查询凭证，请输入凭证金额、经手人或摘要内可能包含的内容
     * settleMonth  凭证所属月份
     * type         0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证
     * con        查询内容
     * */
    @Override
    public List<TAccountantVoucherDto> searchVoucher(User user, String settleMonth,String type,String con) {
        Integer org = user.getOid();
        String source = "";
        if (type != null) {
            switch(type) {
                case "0": {//手动选科目
                    source = "0";
                    break;
                }
                case "1": {//会计录入
                    source = "1";
                    break;
                }
                case "2": {//系统自动选择。source=2或3都是系统自动选择的
                    source = "3";
                    break;
                }
            }
        }

        try {
            double amount = Double.parseDouble(con);
            //按金额查，到t_accountant_voucher_subject表找price
            Set<Integer> vids = new HashSet<>();
            List<TAccountantVoucherDto> result = new ArrayList<>();
            TAccountantVoucherSubject param = new TAccountantVoucherSubject();
            param.setOrg(org);
            param.setAmount(BigDecimal.valueOf(amount));
            param.setAcountDate(settleMonth);
            param.setSource(source);
            List<TAccountantVoucherSubject> listSubject = voucherSubjectMapper.getSubjectByAmount(param);
            for (TAccountantVoucherSubject subject : listSubject) {
                vids.add(subject.getVoucherId());
            }

            for (Integer voucherId : vids) {
                TAccountantVoucherDto voucherDto = voucherMapper.getDtoById(voucherId);
                voucherDto = getTAccountantVoucherSubject(voucherDto);
                result.add(voucherDto);

                setFinanceInfo(voucherDto);

            }
            return result;
        }
        catch (NumberFormatException e) {
            //按经手人或摘要查
            TAccountantVoucherDto param = new TAccountantVoucherDto();
            param.setOrg(org);
            param.setOperatorName(con);
            param.setSummary(con);
            param.setAcountDate(settleMonth);
            List<TAccountantVoucherDto> list = voucherMapper.getVoucherBySearcherCon(param);
            for (TAccountantVoucherDto voucherDto : list) {
                voucherDto = getTAccountantVoucherSubject(voucherDto);
                setFinanceInfo(voucherDto);
            }
            return list;
        }
    }

    private void setFinanceInfo(TAccountantVoucherDto voucherDto) {
        Integer detailId = voucherDto.getDetailId();
        Integer billDetail = voucherDto.getBillDetail();
        Map map = dataService.getDetailBillCreateDate(detailId,billDetail);
        String createName = (String) map.get("createName");
        Date createDate = (Date) map.get("createDate");
        String source = voucherDto.getSource();
        if ("1".equals(source) || "2".equals(source)) {
            voucherDto.setFinanceCreateDate(voucherDto.getAddtime());
            voucherDto.setFinanceCreateName(voucherDto.getOperatorName());
        }
        else if ("0".equals(source) || "3".equals(source)){
            if (createName == null)
                createName = "会计高管";
            if (createDate == null)
                createDate = voucherDto.getAddtime();
            voucherDto.setFinanceCreateDate(createDate);
            voucherDto.setFinanceCreateName(createName);
        }

    }

    public String  getSubjectName(TAccountantVoucherSubject v){
        TAccountantSubjectEntity as = new TAccountantSubjectEntity();
        as.setOrg(v.getOrg());
        as.setSubject(v.getSubject());
        TAccountantSubjectEntity s = tAccountSubjectMapper.subjectAllMessage(as);
        String subjectName = s.getName();
        if (s.getParent() != null) {
            as.setSubject(s.getParent());
            TAccountantSubjectEntity ss = tAccountSubjectMapper.subjectAllMessage(as);
            subjectName = ss.getName() + "-" + subjectName;
            if (ss.getParent() != null) {
                as.setSubject(ss.getParent());
                TAccountantSubjectEntity sss = tAccountSubjectMapper.subjectAllMessage(as);
                subjectName = sss.getName() + "-" + subjectName;
            }
        }
        return subjectName;
    };

}
