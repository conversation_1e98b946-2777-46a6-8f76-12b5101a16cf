<!-- 本月 下面的 日列表 -->
<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <p class="margin-10-15">{{ durTime }}</p>
  <div class="hr_gray"></div>
  <div class="current-balance-1 margin-10-15">
    <div>
      <p class="ty-color-orange">{{ bankName }}</p>
      <p>本期余额</p>
    </div>
    <span>{{ allBalance }}</span>
  </div>
  <div class="hr_gray"></div>
  <div class="flex" >
    <div class="balance1" v-for="(item) in bala_list" :key="item.id">
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <ul class="val_list">
    <li v-for="item in accountList" :key="item.id" class="ta-list">
      <div  @click="goDay(item)">
        <span>{{ item.name }}</span>
        <div class="current-balance-1">
          <div class="ty-color-green">
            <p>收入</p>
            <p>{{ item.income }}</p>
          </div>
        </div>
        <div class="current-balance-1">
          <div class="ty-color-red">
            <p>支出</p>
            <p>{{ item.expend }}</p>
          </div>
        </div>
        <span>
          <el-icon><arrow-right /></el-icon>
        </span>
      </div>
    </li>
  </ul>
</template>

<script>
// 本页面为日列表
import TimeBudget1 from "../cai-views/timeBudget.vue";
import viewByTime from "../cai-views/ViewByTime.vue";

import finance from '@/mixins/finance.js'

export default {
  components: {TimeBudget1,viewByTime},
  name: "currentAD2",
  mixins: [finance],
  data() {
    return {
      accID:0,
      bala_list: [],
      accountList: [],
    }
  },
  mounted() {
    let accID = this.$route.params.id
    if(accID){
      this.accID = accID
      this.getAccountList(accID)
    }else{
      this.$router.push('ViewBA2')
    }

  },
  methods: {
    getAccountList(fid){
      let currentAD2Data = JSON.parse(localStorage.getItem('currentAD2Data'))
      console.log('currentAD2Data=', currentAD2Data)
      let org = this.auth.getOrg()
      let beginDate = currentAD2Data.beginDate
      let endDate = currentAD2Data.endDate
      let params = { oid:org.id , monthOrDay:2 , beginDate:beginDate, endDate:endDate }
      if(fid && fid != 'null'){
        params.fid = fid
      }
      this.financeApi.getAccountMonthOrDayApp(params).then(res1=>{
        let res = res1.data.data
        console.log('getAccountMonthOrDayApp res=', res)

        this.durTime = (new Date(res.beginDate).format('yyyy-MM-dd')) + ' ~ ' + (new Date(res.endDate).format('yyyy-MM-dd'))
        this.allBalance = this.formatCurrency(res.allBalance)
        this.bankName = res.bankName
        let accountDetailList = res.accountDetailList || []
        let accountPeriodList = res.accountPeriodList || []
        this.bala_list = [
          {id: 0, da: "上月余额", money: this.formatCurrency(res.allPreviousBalance ) , sr: this.balance1 },
          {id: 1, da: "本月收入", money: this.formatCurrency(res.allCredit ) , sr:  this.balance2 ,style: "ty-color-green"},
          {id: 2, da: "本月支出", money: this.formatCurrency(res.allDebit) , sr: this.balance3 ,style: "ty-color-red"},
        ]
        this.accountList = []
        accountPeriodList.forEach(item=>{
          let itemA =  {id: 0, name:(new Date(item.beginDate).format('yyyy-MM-dd')) ,
            income: this.formatCurrency(item.credit) ,
            expend:this.formatCurrency(item.debit) ,
            ...item
          }
          this.accountList.push(itemA)
        })

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    goDay(item){
      if(item.beginDate){
        localStorage.setItem('currentAccountDate_beginDate', new Date(item.beginDate).format('yyyy-MM-dd'))
        let path = `/currentAccountDateDur/${ this.accID }`
        this.$router.push({ path:path })
      }else{
        this.$message.error('没有具体日期哦！')
      }

    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.dis {
  display: none;
}
.flex{
  padding: 5px 20px;
  line-height: 30px;
}
.balance1{
  margin: 8px 0;
  line-height: 20px;
  width: 100%;
  align-items: center;
}
</style>