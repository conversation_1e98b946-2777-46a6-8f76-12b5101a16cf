<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="approvalSetting">
    <TY_NavTop title="审批设置的修改申请" isBackHome="true" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div>
        <a class="ui-cell" @click="applyDetail(item.id)" v-for='(item, index) in list' v-bind:key='index'>
          <div class="dd1">
            <p>{{item.description}}</p>
            <p class="right"><span>{{item.askName}}</span> <span>{{item.createDateItem | formatDay('YYYY/MM/DD HH:mm:ss')}}</span></p>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #approvalSetting{
    .dd1{
      width:100%; box-sizing:border-box;
      .right{
        text-align:right; color:#aaa; font-size:0.7em;
        span{ margin-right: 20px;  }
      }
    }

  }
</style>
<script>
export default {
  name: 'approvalSettingsApply',
  data () {
    return {
      list: [],
      listenersUid: [],
      isBackHome: false,
      loading: false
    }
  },
  created: function () {
    this.loading = true
    let _this = this
    _this.getList()
    _this.listenersUid = [
      // 订阅审批结果
      _this.sphdSocket.subscribe('handleApply', function (data) {
        console.log('获取订阅审批结果:' + data)
        _this.loading = false
        _this.$kiko_message("审批状态已变更")
        _this.getList()
      }, function () {
        _this.$kiko_message("获取订阅审批结果失败")
      }, 'user')
    ]
  },
  methods: {
    getList: function () {
      let _this = this
      let session = _this.auth.getSessionid()
      let user = _this.auth.getUser()
      let userId = user.userID
      _this.axios.post('../../../popedom/handleApply.do', { 'session':session , 'userId': userId })
        .then(function (response) {
          _this.loading = false
          let data = response.data.data
          _this.list = data.approvalProcessList
          console.log('list', response)
        }).catch(function (error) {
          _this.$kiko_message("链接错误，请重试！")
        })
    },
    search: function () {
      this.$router.push({
        path: `/approvalSettingsSearch/1`
      })
    },
    applyDetail: function (itemID) {
      this.$router.push({
        path: '/approvalSettingsApplyDetails/' + itemID
      })
    },
    destroyed: function () {
      let _this = this
      _this.listenersUid.forEach(function (item) {
        _this.sphdSocket.unsubscribe(item)
      })
    }
  },
  destroyed: function () {
    let _this = this
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  }
}
</script>
