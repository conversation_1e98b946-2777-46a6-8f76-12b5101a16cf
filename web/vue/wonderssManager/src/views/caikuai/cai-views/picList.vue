<!--日流水的具体详情-->
<template>
  <div>
    <div>
      <PageHeader title="财会" :showBack="true" ></PageHeader>
    </div>
    <div class="imgCon">
      <div class="imgItem" v-for="(img , index) in list" :key="index">
        <el-image :src="img.newPath" :preview-src-list="[img.newPath]">
        </el-image>
      </div>
      <div style="clear: both;"></div>

    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "xiangQing",
  data() {
    return {
      list:[],
      fileUrl:''
    }
  },
  mixins: [finance],
  mounted() {
     let picList = localStorage.getItem('picList')
    if(picList){
      picList = JSON.parse(picList)
      this.list = []
      picList.forEach(img=>{
        img.newPath = this.auth.webRoot+ '/upload/' + img.uplaodPath
        // img.newPath = 'https://dvm01.btransmission.com/upload/org/3611/202502/947349becd534ed18d39476575795847.jpg'
        this.list.push(img)
      })
      console.log('list=', this.list)

    }
  },
  methods: {
  },
}
</script>

<style scoped lang="scss">
.imgCon{
  position: relative; padding: 20px;
  .imgItem{
    width: 40%; float: left; margin:10px 5%;
  }
}
</style>