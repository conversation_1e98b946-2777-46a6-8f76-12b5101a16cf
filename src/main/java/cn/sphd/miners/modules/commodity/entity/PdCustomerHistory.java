/**
	* Copyright (c) minuteproject, <EMAIL>
	* All rights reserved.
	* 
	* Licensed under the Apache License, Version 2.0 (the "License")
	* you may not use this file except in compliance with the License.
	* You may obtain a copy of the License at
	* 
	* http://www.apache.org/licenses/LICENSE-2.0
	* 
	* Unless required by applicable law or agreed to in writing, software
	* distributed under the License is distributed on an "AS IS" BASIS,
	* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
	* See the License for the specific language governing permissions and
	* limitations under the License.
	* 
	* More information on minuteproject:
	* twitter @minuteproject
	* wiki http://minuteproject.wikispaces.com 
	* blog http://minuteproject.blogspot.net
	* 
*/
/**
	* template reference : 
	* - Minuteproject version : 0.9.5
	* - name      : DomainEntityJPA2Annotation
	* - file name : DomainEntityJPA2Annotation.vm
	* - time      : 2016/09/12 ��Ԫ at 14:29:06 GMT+08:00
*/
package cn.sphd.miners.modules.commodity.entity;

//MP-MANAGED-ADDED-AREA-BEGINNING @import@
//MP-MANAGED-ADDED-AREA-ENDING @import@

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 *
 * <p>Title: TPdCustomerHistory</p>
 *
 * <p>Description: Domain Object describing a TPdCustomerHistory entity</p>
 *
 */
@Entity (name="PdCustomerHistory")
@Table (name="t_pd_customer_history")

public class PdCustomerHistory implements Serializable {

	
    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

//MP-MANAGED-ADDED-AREA-BEGINNING @name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-name@
    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name; 

    @Column(name="full_name"  , length=255 , nullable=true , unique=false)
    private String fullName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @contact-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @contact-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-contact@
    @Column(name="contact"  , length=100 , nullable=true , unique=false)
    private String contact; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @mobile-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @mobile-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-mobile@
    @Column(name="mobile"  , length=100 , nullable=true , unique=false)
    private String mobile; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @telephone-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @telephone-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-telephone@
    @Column(name="telephone"  , length=100 , nullable=true , unique=false)
    private String telephone; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @fax-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @fax-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-fax@
    @Column(name="fax"  , length=100 , nullable=true , unique=false)
    private String fax; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @email-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @email-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-email@
    @Column(name="email"  , length=100 , nullable=true , unique=false)
    private String email; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @address-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @address-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-address@
    @Column(name="address"  , length=100 , nullable=true , unique=false)
    private String address; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @payment_type-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @payment_type-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-payment_type@
    @Column(name="payment_type"  , length=1 , nullable=true , unique=false)
    private String paymentType; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @payment_method-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @payment_method-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-payment_method@
    @Column(name="payment_method"  , length=1 , nullable=true , unique=false)
    private String paymentMethod; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @bank_code-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @bank_code-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-bank_code@
    @Column(name="bank_code"  , length=100 , nullable=true , unique=false)
    private String bankCode; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @bank_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @bank_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-bank_name@
    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @bank_no-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @bank_no-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-bank_no@
    @Column(name="bank_no"  , length=100 , nullable=true , unique=false)
    private String bankNo; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @keywords-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @keywords-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-keywords@
    @Column(name="keywords"  , length=100 , nullable=true , unique=false)
    private String keywords;

    @Column(name="g_tenant_id"   , nullable=true , unique=false)
    private Integer gTenantId;//管理平台租户ID

//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @creator-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @creator-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-creator@
    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @create_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @create_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-create_name@
    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @create_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @create_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-create_date@
    @Column(name="create_date"   , nullable=true , unique=false)
    private Timestamp createDate; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @updator-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @updator-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-updator@
    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @update_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @update_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-update_name@
    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @update_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @update_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-update_date@
    @Column(name="update_date"   , nullable=true , unique=false)
    private Timestamp updateDate; 
//MP-MANAGED-UPDATABLE-ENDING

//    @Column(name="supervisor_name"  , length=50 , nullable=true , unique=false)
//    private String supervisorName;   //超管姓名（特殊机构 lixu添加）
//
//    @Column(name="supervisor_mobile"  , length=50 , nullable=true , unique=false)
//    private String supervisorMobile;   //超管手机 （特殊机构 lixu添加）

    @ManyToOne (fetch=FetchType.LAZY )
    @JoinColumn(name="customer", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true) 
    private SlCustomer customer;

    @Column(name="customer"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer customer_;

    /**
    * Default constructor
    */
    public PdCustomerHistory() {
    }

	/**
	* All field constructor 
	*/
    public PdCustomerHistory(
       Integer id,
       Integer customer,
       String name,
       String fullName,
       String contact,
       String mobile,
       String telephone,
       String fax,
       String email,
       String address,
       String paymentType,
       String paymentMethod,
       String bankCode,
       String bankName,
       String bankNo,
       String keywords,
       Integer creator,
       String createName,
       Timestamp createDate,
       Integer updator,
       String updateName,
       Timestamp updateDate) {
	 this(
       id,
       customer,
       name,
       fullName,
       contact,
       mobile,
       telephone,
       fax,
       email,
       address,
       paymentType,
       paymentMethod,
       bankCode,
       bankName,
       bankNo,
       keywords,
       creator,
       createName,
       createDate,
       updator,
       updateName,
       updateDate
	 ,true);
	}
    
	public PdCustomerHistory(
       Integer id,
       Integer customer,
       String name,
       String fullName,
       String contact,
       String mobile,
       String telephone,
       String fax,
       String email,
       String address,
       String paymentType,
       String paymentMethod,
       String bankCode,
       String bankName,
       String bankNo,
       String keywords,
       Integer creator,
       String createName,
       Timestamp createDate,
       Integer updator,
       String updateName,
       Timestamp updateDate	
    , boolean setRelationship) {
       //primary keys
       setId (id);
       //attributes
       setName (name);
       setFullName (fullName);
       setContact (contact);
       setMobile (mobile);
       setTelephone (telephone);
       setFax (fax);
       setEmail (email);
       setAddress (address);
       setPaymentType (paymentType);
       setPaymentMethod (paymentMethod);
       setBankCode (bankCode);
       setBankName (bankName);
       setBankNo (bankNo);
       setKeywords (keywords);
       setCreator (creator);
       setCreateName (createName);
       setCreateDate (createDate);
       setUpdator (updator);
       setUpdateName (updateName);
       setUpdateDate (updateDate);
       //parents
       if (setRelationship) this.customer = new SlCustomer();
       if (setRelationship) this.customer.setId(customer); 
	   setCustomer_ (customer);
    }

	public PdCustomerHistory flat() {
	   return new PdCustomerHistory(
          getId(),
          getCustomer_(),
          getName(),
          getFullName(),
          getContact(),
          getMobile(),
          getTelephone(),
          getFax(),
          getEmail(),
          getAddress(),
          getPaymentType(),
          getPaymentMethod(),
          getBankCode(),
          getBankName(),
          getBankNo(),
          getKeywords(),
          getCreator(),
          getCreateName(),
          getCreateDate(),
          getUpdator(),
          getUpdateName(),
          getUpdateDate()
       , false
	   );
	}

    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-name@
    public String getName() {
        return name;
    }
	
    public void setName (String name) {
        this.name =  name;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-full_name@
    public String getFullName() {
        return fullName;
    }
	
    public void setFullName (String fullName) {
        this.fullName =  fullName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-contact@
    public String getContact() {
        return contact;
    }
	
    public void setContact (String contact) {
        this.contact =  contact;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-mobile@
    public String getMobile() {
        return mobile;
    }
	
    public void setMobile (String mobile) {
        this.mobile =  mobile;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-telephone@
    public String getTelephone() {
        return telephone;
    }
	
    public void setTelephone (String telephone) {
        this.telephone =  telephone;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-fax@
    public String getFax() {
        return fax;
    }
	
    public void setFax (String fax) {
        this.fax =  fax;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-email@
    public String getEmail() {
        return email;
    }
	
    public void setEmail (String email) {
        this.email =  email;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-address@
    public String getAddress() {
        return address;
    }
	
    public void setAddress (String address) {
        this.address =  address;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-payment_type@
    public String getPaymentType() {
        return paymentType;
    }
	
    public void setPaymentType (String paymentType) {
        this.paymentType =  paymentType;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-payment_method@
    public String getPaymentMethod() {
        return paymentMethod;
    }
	
    public void setPaymentMethod (String paymentMethod) {
        this.paymentMethod =  paymentMethod;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-bank_code@
    public String getBankCode() {
        return bankCode;
    }
	
    public void setBankCode (String bankCode) {
        this.bankCode =  bankCode;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-bank_name@
    public String getBankName() {
        return bankName;
    }
	
    public void setBankName (String bankName) {
        this.bankName =  bankName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-bank_no@
    public String getBankNo() {
        return bankNo;
    }
	
    public void setBankNo (String bankNo) {
        this.bankNo =  bankNo;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-keywords@
    public String getKeywords() {
        return keywords;
    }
	
    public void setKeywords (String keywords) {
        this.keywords =  keywords;
    }

    public Integer getgTenantId() {
        return gTenantId;
    }

    public void setgTenantId(Integer gTenantId) {
        this.gTenantId = gTenantId;
    }

    //MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-creator@
    public Integer getCreator() {
        return creator;
    }
	
    public void setCreator (Integer creator) {
        this.creator =  creator;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-create_name@
    public String getCreateName() {
        return createName;
    }
	
    public void setCreateName (String createName) {
        this.createName =  createName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-create_date@
    public Timestamp getCreateDate() {
        return createDate;
    }
	
    public void setCreateDate (Timestamp createDate) {
        this.createDate =  createDate;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-updator@
    public Integer getUpdator() {
        return updator;
    }
	
    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-update_name@
    public String getUpdateName() {
        return updateName;
    }
	
    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-update_date@
    public Timestamp getUpdateDate() {
        return updateDate;
    }
	
    public void setUpdateDate (Timestamp updateDate) {
        this.updateDate =  updateDate;
    }
	
//MP-MANAGED-UPDATABLE-ENDING


    public SlCustomer getCustomer () {
    	return customer;
    }
	
    public void setCustomer (SlCustomer customer) {
    	this.customer = customer;
    }

    public Integer getCustomer_() {
        return customer_;
    }
	
    public void setCustomer_ (Integer customer) {
        this.customer_ =  customer;
    }
//
//    public String getSupervisorName() {
//        return supervisorName;
//    }
//
//    public void setSupervisorName(String supervisorName) {
//        this.supervisorName = supervisorName;
//    }
//
//    public String getSupervisorMobile() {
//        return supervisorMobile;
//    }
//
//    public void setSupervisorMobile(String supervisorMobile) {
//        this.supervisorMobile = supervisorMobile;
//    }

    //MP-MANAGED-ADDED-AREA-BEGINNING @implementation@
//MP-MANAGED-ADDED-AREA-ENDING @implementation@

}
