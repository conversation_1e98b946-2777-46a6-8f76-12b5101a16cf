.flexCon{ display: flex; margin-top:20px;  }
.flexCon>div:nth-child(1){ flex:3; }
.flexCon>div:nth-child(2){ flex:2; }
.btnCat{ padding-right:15px; color: #0f62ea; font-weight: bold; }
.btnCat:hover{ text-decoration:underline; cursor: default; }
.mainCon2,.mainCon3,.mainCon4,.mainCon5,.mainCon6,.mainCon7,.mainCon8{ display: none;  }
.centerCon{ text-align: center;  }
tbody>tr:nth-child(1) td{ background:#eee;  }
#scanGS{ width:830px;  }
#mtEntry, #mtScan { width:950px;  }
.item{ line-height:35px; }
.red{ color:red; }
.item-title{ display:inline-block; width:75px; text-align:center; }
input,select{ width:185px;  }
span.disabled{ color:#999;  }
.orangeTip{ color: #ff9d06; }
.border{ border:1px solid #ccc; padding: 5px 15px; border-radius:2px; }
.mainCon3 tbody .fa, .mainCon4 tbody .fa{ color:#0a6aa1;  }
#selectMt{ display: none; position: absolute; background:#fff;  width:182px; height:70px; overflow-y:auto; border-radius:4px;
    border:1px solid #ccc; border-top:none; z-index:1 ; left:95px;top:32px;    }
#selectMt option{ border-bottom:1px solid #eee; text-align: center;   }
#selectMt option:hover, #selectMt option.active{ background:#ddd; cursor: default;    }
.isCurrent .fa{ margin:0 15px 0 40px;color: #0b94ea;  }
.tip{ color: #0b94ea; font-size: 0.8em;  }
.search{ border: 1px solid #0b94ea; display: inline-block; border-radius: 20px; width:300px;padding:4px 0; position: relative   }
.search span:nth-child(1){ border-radius: 20px 0 0 20px; background:#0b94ea;padding: 5px 10px; color: #fff;    }
.search span:nth-child(3){ text-align: right; width: 60px; height:27px; border-radius:0 20px 20px 0;  padding: 5px 10px; position: absolute; right: 0; top: 0;   }
.search span:nth-child(3):hover{ background:#ecf6ff;    }
.search span:nth-child(3)>i{ display:inline-block; width:20px; height: 20px; background: url("img/search.png") no-repeat; background-size:20px 20px;    }
.search input{ border:none; margin: 0; display: inline-block; height: 27px; position: absolute; top: 0; padding: 0 5px;    }
.search input:focus{ border:none;    }
#mtEntry input[disabled], #mtEntry select[disabled]{ background: #eaeaea; }





