package cn.sphd.miners.modules.publicInformation.service.impl;

import cn.sphd.miners.modules.publicInformation.dao.PiCategoryDao;
import cn.sphd.miners.modules.publicInformation.dao.PiCategoryHistoryDao;
import cn.sphd.miners.modules.publicInformation.entity.PiCategory;
import cn.sphd.miners.modules.publicInformation.entity.PiCategoryHistory;
import cn.sphd.miners.modules.publicInformation.service.PiCatService;
import cn.sphd.miners.modules.publicInformation.service.PiInfoServies;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("piCatService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class PiCatServiceImpl implements PiCatService {

    @Autowired
    PiCategoryDao piCategoryDao;
    @Autowired
    PiCategoryHistoryDao piCategoryHistoryDao;


    @Autowired
    PiInfoServies piInfoServies;

    @Override
    public PiCategory piCategorySingle(Integer categoryId) {
        PiCategory piCategory = piCategoryDao.get(categoryId);
        return piCategory;
    }

    @Override
    public PiCategoryHistory piCategoryHisSingle(Integer categoryHisId) {
        PiCategoryHistory piCategoryHistory = piCategoryHistoryDao.get(categoryHisId);
        return piCategoryHistory;
    }

    @Override
    public HashMap<String, Object> getPiInitialDir(User user) {
        String hql = "from PiCategory where valid = 1 and org = " +user.getOid();
        List<PiCategory> firstCat = piCategoryDao.getListByHQLWithNamedParams(hql, null);
        if(firstCat.isEmpty()){
            this.addPiInitializeDir(user);
            firstCat = piCategoryDao.getListByHQLWithNamedParams(hql, null);
        }
        Long allFileNum = piInfoServies.getPiFileNum(user.getOid(), null);
        Long allFileSize = piInfoServies.getCatSize(user.getOid(), null);
        PiCategory piCatRecycleBin = new PiCategory();
        for (PiCategory cat : firstCat) {
            Long childFileNum = piInfoServies.getPiFileNum(user.getOid(), cat.getId());
            cat.setLeafs(childFileNum);
            Long childFileSize = piInfoServies.getCatSize(user.getOid(), cat.getId());
            cat.setSize(childFileSize);
            if (Byte.valueOf("9").equals(cat.getType())) {
                piCatRecycleBin = cat;
            }
        }
        firstCat.remove(piCatRecycleBin);
        /*List<PiCategory> listFirstCat = new ArrayList<>();
        listFirstCat.addAll(firstCat);
        listFirstCat.add(piCatRecycleBin);*/
        firstCat.add(piCatRecycleBin);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listFirstFolder", firstCat);
        map.put("fileNum", allFileNum);
        map.put("fileSize", allFileSize);
        return map;
    }

    @Override
    public HashMap<String, Object> addPiCat(User user, String name) {
        PiCategory oldCat = this.checkPiCatName(user, name);
        Integer state = 1;
        HashMap<String, Object> map = new HashMap<>();
        if (oldCat == null) {
            PiCategory cat = new PiCategory();
            cat.setName(name);
            cat.setIsSystem(Byte.valueOf("2"));
            cat.setValid(true);
            cat.setTrash(false);
            cat.setOrg(user.getOid());
            cat.setCreator(user.getUserID());
            cat.setCreateName(user.getUserName());
            cat.setCreateDate(new Date());
            cat.setVersionNo(0);
            piCategoryDao.save(cat);
            map.put("newCategory",cat);
        }else {
            state = 2;
        }
        map.put("state",state);
        return map;
    }

    @Override
    public HashMap<String, Object>  upPiCat(User user, Integer categoryId, String name) {
        PiCategory oldCat = this.checkPiCatName(user, name);
        Integer state = 1;
        HashMap<String, Object> map = new HashMap<>();
        if (oldCat == null) {
            PiCategory cat = this.piCategorySingle(categoryId);
            List<PiCategoryHistory> list = this.listPiCatHis(categoryId);
            if (list.isEmpty()) {
                this.addPiHisCat(cat);  //没有原始版本时新增原始版本
            }
            cat.setName(name);
            cat.setUpdator(user.getUserID());
            cat.setUpdateName(user.getUserName());
            cat.setUpdateDate(new Date());
            cat.setVersionNo(cat.getVersionNo()+1);
            map.put("category",oldCat);
            this.addPiHisCat(cat);   //新增新版本
        }else {
            state = 2;
        }
        map.put("state",state);
        return map;
    }

    @Override
    public List<PiCategoryHistory> listPiCatHis(Integer categoryId) {
        String hql = "from PiCategoryHistory where piCategory = :piCategory";
        HashMap<String, Object> param = new HashMap<>();
        param.put("piCategory", categoryId);
        List<PiCategoryHistory> list = piCategoryHistoryDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public Integer delPitCat(User user, Integer categoryId) {
        PiCategory cat = this.piCategorySingle(categoryId);
        Integer state = 1;
        if (cat.getValid()) {
            Long childNum = piInfoServies.getPiFileNum(user.getOid(),categoryId);
            if (childNum < 1) {
                cat.setValid(false);
            }else {
                state = 3;
            }
        } else {
            state = 2;
        }
        return state;
    }

    private void addPiHisCat(PiCategory cat){
        PiCategoryHistory catHis = new PiCategoryHistory();
        catHis.setPiCategory(cat.getId());
        catHis.setName(cat.getName());
        catHis.setIsSystem(cat.getIsSystem());
        catHis.setValid(cat.getValid());
        catHis.setOrg(cat.getOrg());
        catHis.setCreator(cat.getCreator());
        catHis.setCreateName(cat.getCreateName());
        catHis.setCreateDate(cat.getCreateDate());
        catHis.setUpdator(cat.getUpdator());
        catHis.setUpdateName(cat.getUpdateName());
        catHis.setUpdateDate(cat.getUpdateDate());
        catHis.setVersionNo(cat.getVersionNo());
        piCategoryHistoryDao.save(catHis);
    }

    private PiCategory checkPiCatName(User user, String name){
        String hql = "from PiCategory where org = :org and name = :name and valid = 1";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("name", name);
        PiCategory cat = (PiCategory) piCategoryDao.getByHQLWithNamedParams(hql,param);
        return cat;
    }

    private void addPiInitializeDir(User user){
        PiCategory catOne = new PiCategory();
        catOne.setName("公共信息1");
        catOne.setType(Byte.valueOf("1"));
        catOne.setIsSystem(Byte.valueOf("1"));
        catOne.setValid(true);
        catOne.setTrash(false);
        catOne.setOrg(user.getOid());
        catOne.setCreator(user.getUserID());
        catOne.setCreateName(user.getUserName());
        catOne.setCreateDate(new Date());
        catOne.setVersionNo(0);
        piCategoryDao.save(catOne);

        PiCategory catTwo = new PiCategory();
        catTwo.setName("公共信息2");
        catTwo.setType(Byte.valueOf("2"));
        catTwo.setIsSystem(Byte.valueOf("1"));
        catTwo.setValid(true);
        catTwo.setTrash(false);
        catTwo.setOrg(user.getOid());
        catTwo.setCreator(user.getUserID());
        catTwo.setCreateName(user.getUserName());
        catTwo.setCreateDate(new Date());
        catTwo.setVersionNo(0);
        piCategoryDao.save(catTwo);

        PiCategory piCatRecycleBin = new PiCategory();
        piCatRecycleBin.setName("暂时不用的文件");
        piCatRecycleBin.setType(Byte.valueOf("9"));
        piCatRecycleBin.setIsSystem(Byte.valueOf("1"));
        piCatRecycleBin.setValid(true);
        piCatRecycleBin.setTrash(true);
        piCatRecycleBin.setOrg(user.getOid());
        piCatRecycleBin.setCreator(user.getUserID());
        piCatRecycleBin.setCreateName(user.getUserName());
        piCatRecycleBin.setCreateDate(new Date());
        piCatRecycleBin.setVersionNo(0);
        piCategoryDao.save(piCatRecycleBin);

        piInfoServies.addPiFile(user,catOne.getId(),"营业执照","1",null,"1", "1",null,null,null,null,null,null,null,null,null,"公共信息");
        piInfoServies.addPiFile(user,catOne.getId(),"税务登记证","2",null,"1", "1",null,null,null,null,null,null,null,null,null,"公共信息");
        piInfoServies.addPiFile(user,catOne.getId(),"公司章程（可编辑版）","3",null,"1", "1",null,null,null,null,null,null,null,null,null,"公共信息");
        piInfoServies.addPiFile(user,catOne.getId(),"公司章程（照片或扫描版）","4",null,"1", "1",null,null,null,null,null,null,null,null,null,"公共信息");

        piInfoServies.addPiFile(user,catTwo.getId(),"开票资料（增票）","5",null,"2", "1",null,null,null,null,null,null,null,null,null,"公共信息");
        piInfoServies.addPiFile(user,catTwo.getId(),"开票资料（普票）","6",null,"3", "1",null,null,null,null,null,null,null,null,null,"公共信息");
        piInfoServies.addPiFile(user,catTwo .getId(),"收款资料","7",null,"4", "1",null,null,null,null,null,null,null,null,null,"公共信息");
    }
}
