<template>
  <div id="purchaseInvoiceMoreadd">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading" v-bind:key="index">
      <div class="con1">
        <div class="oInfo" v-if="honeynew.orders">
          <p v-if="honeynew.orders[0]" style="display: none">{{honeynew.orders[0].type | formatType(honeynew.ordersItem[0].amount) }}</p>
          <div style="display: flex">
            <p v-if="honeynew.orders[0].type !== '3'" style="margin-left: -21px;">
              {{honeynew.orders[0].full_name}}
            </p>
            <p class="txt_right" v-if="honeynew.ordersItem[0].type !== '3'" style="margin-left: -21px;">
              {{honeynew.ordersItem[0].model}}
            </p>
          </div>
          <div style="display: flex" v-if="honeynew.ordersItem">
            <p v-if="honeynew.ordersItem[0].type !== '3'" style="margin-left: -21px;">
              订单日期{{honeynew.ordersItem[0].planDate |formatDay('YYYY-MM-DD')}}
            </p>
            <p class="txt_right" v-if="honeynew.ordersItem[0].type !== '3'" style="margin-left: 106px;">
              订单总额{{honeynew.orders[0].amount}}
            </p>
          </div>
        </div>
        <div class="panel-content">
          <div class="text-right">
            <el-link style="color: #409eff; margin-right: 20px;" @click="openlook" v-show="conshou1">展开</el-link>
            <el-link style="color: #409eff; margin-right: 20px;" @click="openlook" v-show="conshou2">收起</el-link>
          </div>
          <div v-show="tacklook">
            <div style="display: flex" v-if="honeynew.orders">
              <p v-if="honeynew.orders[0].type !== '3'" style="margin-left: -12px;">
                订单号{{honeynew.orders[0].sn}}
              </p>
            </div>
            <div style="display: flex" v-if="honeynew.orders" class="text-right">
              <p class="txt_right" v-if="honeynew.orders[0].type !== '3'" style="margin-left: 120px;">
                录入{{honeynew.orders[0].create_name}}{{honeynew.orders[0].create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-content" style="margin-bottom: 12px;">
        <div>
          <table>
            <thead>
            <tr>
              <td style="text-align: center">材料</td>
              <td style="text-align: center">要求到货日期/数量</td>
            </tr>
            </thead>
            <tbody>
            <tr v-if="honeynew.ordersItem">
              <td style="width: 211px;text-align: center">
                {{honeynew.ordersItem[0].code}}{{"\xa0\xa0"}}{{honeynew.ordersItem[0].name}}
                {{"\xa0\xa0"}}{{honeynew.ordersItem[0].specifications}}{{"\xa0\xa0"}}
                {{honeynew.ordersItem[0].model}}
              </td>
              <td style="text-align: center">
                {{honeynew.ordersItem[0].delivery_date |formatDay('YYYY-MM-DD')}}
                {{honeynew.ordersItem[0].unit}}计量单位
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "purchaseInvoiceMoreadd",
  data(){
    return {
      honeynew: {},
      loading: true,
      toggle: -1,
      title: '采购预付款的申请',
      tacklook: false,
      conshou1: true,
      conshou2: false
    }
  },
  created() {
    let that = this;
    let paramCode = localStorage.getItem('accessToken');  //读取别的文件中存储的数据
    console.log(paramCode);
    that.invoiceID = paramCode;
    that.getDetail();
  },
  methods:{
    getDetail:function(){
      let that = this;
      console.log('invoiceID',that.invoiceID);
      this.axios.get('../../../po/orderDetail',{  //vue中调用get接口的格式
        params:{
          'orderId':that.invoiceID
        }
      })
        .then(function(response){
          let res = response.data;
          console.log('数据材料',res);
          that.honeynew = res;
          that.loading = false;
        })
        .catch(function(error){
          console.log(error);
        })
    },
    openlook(){
      this.tacklook = !this.tacklook;
      this.conshou1 = !this.conshou1;
      this.conshou2 = !this.conshou2;
    }
  }
}
</script>

<style lang="less">
#purchaseInvoiceMoreadd{
  .oInfo{ padding:5px 20px; }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
  .right{ float: right;  }
  .ui-cell{ display: block;  }
}
</style>
