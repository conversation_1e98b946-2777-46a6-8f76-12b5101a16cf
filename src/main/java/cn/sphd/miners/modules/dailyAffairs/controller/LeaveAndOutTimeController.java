package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.controller.BaseController;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.dailyAffairs.service.impl.OverturnLeaveItem;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.message.service.MessageService;
import cn.sphd.miners.modules.personal.dto.LeaveQuery;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelLeave;
import cn.sphd.miners.modules.personal.entity.PersonnelLeaveItem;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.personal.service.LeaveOutTimeUtilsService.ApprovalStatus;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2018/9/28.
 * 1.49处理与角标 加班、请假
 */
@Controller
@RequestMapping("/leaveAndOutTime")
public class LeaveAndOutTimeController extends BaseController {

    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    OvertimeService overtimeService;
    @Autowired
    LeaveService leaveService;
    @Autowired
    MessageService messageService;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    RoleService roleService;



    @Autowired
    public LeaveAndOutTimeController() {
        this.sdf="yyyy-MM-dd HH:mm:ss";
        //关闭父类参数自动转换
        setInitBinderConfig(InitBinderConfig.DisableXSSDefence, InitBinderConfig.DisableDateChange);
    }

   /**
    *<AUTHOR>
    *@date 2018/10/3 17:19
    *提交请假申请
    *请假类型为leaveType    ruleTime-提交请假时候的提前量
   */
    @ResponseBody
    @RequestMapping("/addLeave.do")
    public JsonResult addLeave(PersonnelLeave personnelLeave,String beginTime1, String endTime1,Double ruleTime,User user) {
        System.out.println("addLeave.do开始");
        Map<String, Object> map = new HashMap<String, Object>();
        map = leaveService.addLeave(beginTime1,endTime1,personnelLeave.getLeaveType(),personnelLeave.getReason(),LeaveService.Kind.ordinary.getIndex(),user,ruleTime,map);
        System.out.println("addLeave.do结束");
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/10/8 10:59
     *提前结束请假
     * userId (申请人id)
    */
    @ResponseBody
    @RequestMapping("/leaveAhead.do")
    @MessageMapping("/leaveAhead")
    public JsonResult leaveAhead(String json,String sessionid,User user) throws ParseException {
        System.out.println("leaveAhead开始");
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer leaveId=jsonObject.getInteger("leaveId");
        String actualReason = jsonObject.getString("actualReason");
        String endTime1 = jsonObject.getString("endTime1");

        Integer status=0;
        String content = "申请失败";
        if (leaveId!=0 && user!=null){
            PersonnelLeave personnelLeave = userMessageService.getPersonnelLeaveById(leaveId);
            List<PersonnelLeaveItem> personnelLeaveItemList=leaveService.getPersonnelLeaveItemByleaveId(leaveId,null);
            if (personnelLeaveItemList.isEmpty() || (personnelLeaveItemList.size()>0&&Arrays.asList("3","9").contains(personnelLeaveItemList.get(0).getApproveStatus()))) {
                if (!"".equals(endTime1) && endTime1 != null && new Date().before(personnelLeave.getEndTime())) {    //计划上班时间需在计划请假开始时间和结束时间之间
                    Integer dept = null;
                    if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()){
                        dept = Integer.getInteger(user.getDepartment());
                    }
                    Integer stateTime = workAttendanceOldService.getStateTime(user.getOid(),dept,NewDateUtils.today(NewDateUtils.dateFromString(endTime1,"yyyy-MM-dd HH:mm:ss")),4,endTime1,endTime1);
                    if (1==stateTime) {   //加班申请的时间在正常的范围之内

                        if (NewDateUtils.dateFromString(endTime1, sdf).getTime() >= personnelLeave.getBeginTime().getTime() && NewDateUtils.dateFromString(endTime1, sdf).before(personnelLeave.getEndTime())) {  //并且提前结束请假的计划上班时间要在结束时间之前【包括开始时间】，则让提交

                            PersonnelLeaveItem personnelLeaveItem = leaveService.addPersonnelLeaveItem(personnelLeave, endTime1, actualReason, user);

                            ApprovalItem approvalItem = approvalService.getById(personnelLeave.getApproveItem());
                            //审批状态为1则需要审批，去找审批流程
                            if (approvalItem.getStatus() == 1) {
                                Integer handleId = approvalProcessService.addApplyApprovalProcess(personnelLeaveItem.getId(), 8, user, "请假申请", approvalItem);

                            if (NewDateUtils.dateFromString(endTime1,sdf).getTime() <= new Date().getTime()) {
                                //给审批人待处理发送
                                leaveService.leaveRejectSend(1, 1, personnelLeave, handleId, "/approvalLeaveHandle", null, null, "approvalLeave");
                                //给申请人 已批准推
                                leaveService.leaveRejectSend(-1,-1,personnelLeave,personnelLeave.getUser_(),"/applyLeaveApproval",null,null,"applyLeave");
                                leaveService.delayCallPersonnelLeaveItem(personnelLeaveItem.getId());
                                } else {

                                    //给申请人待处理的发送
                                    leaveService.leaveRejectSend(0, 1, personnelLeave, user.getUserID(), "/applyLeaveHandle", null, null, "applyLeave");


                                    //给申请人已批准发送
                                    leaveService.leaveRejectSend(-1, -1, personnelLeave, personnelLeave.getUser_(), "/applyLeaveApproval", null, null, "applyLeave");

                                    //给审批人待处理发送
                                    leaveService.leaveRejectSend(1, 1, personnelLeave, handleId, "/approvalLeaveHandle", user.getUserName() + "提交了提前结束请假申请", user.getUserName() + "提交了提前结束请假申请", "approvalLeave");

                                    //给审批人已批准 推
                                    leaveService.leaveRejectSend(0, -1, personnelLeave, handleId, "/approvalLeaveApproval", null, null, "approvalLeave");

                                    //申请 到时间自动驳回
                                    OverturnLeaveItem overturnLeaveItem = new OverturnLeaveItem(personnelLeaveItem.getId());
                                    clusterMessageSendingOperations.delayCall(personnelLeaveItem.getActualEndTime(), overturnLeaveItem);
                                    System.out.println("leaveAhead提前结束请假 actualEndTime: " + personnelLeaveItem.getActualEndTime());
                                }
                            } else {
                                //不需要审批，直接通过
                                personnelLeaveItem.setApproveStatus("2");
                                personnelLeaveItem.setAuditDate(new Date());
                                userMessageService.updatePersonnelLeaveItem(personnelLeaveItem);

                                personnelLeave.setApproveStatus("2");
                                personnelLeave.setAuditDate(new Date());
                                userMessageService.updatePersonnelLeave(personnelLeave);
                            }
                            status = 1;
                            content = "申请成功";
                        } else {
                            status = 3;//提前结束时间应在请假时间段之内。
                            content = "您申请中的时间有些问题，请重新申请！";

                        }
                    }else if (0==stateTime){
                        status = 3;//提前结束时间应在请假时间段之内。
                        content = "您申请中的时间有些问题，请重新申请！";
                    }
                } else {   //若当前时间在计划请假结束时间之后，则不让他提交
                    status = 2;  //提前结束时间在计划请假结束时间之后，不让提了
                    content = "您申请中的时间有些问题，请重新申请！";
                }
            }else {
                status = 4;//倒霉孩子，你已经提交过了
                content = "已提交申请，不能重复提交";
            }
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("status",status);  //手机端需要返回的状态和内容，待PC端需要的时候再改
        map.put("content",content);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/notice",null,null,null,null,status);   //返回 处理结果的订阅
        System.out.println("leaveAhead结束");
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2018/9/27 11:27
    * 申请人请假详情(暂时不用，留着)
     * numType 1-提前结束请假
    */
    @ResponseBody
    @RequestMapping("/myLeaveInfoById.do")
    public void myLeaveInfoById(Integer userId,Integer id,Integer numType,HttpServletResponse response) throws IOException {
        HashMap<String,Object> map=new HashMap<String, Object>();
        if (id!=null) {
            leaveService.myLeaveInfoById(userId,id,numType,map);
        }
        ObjectToJson.objectToJson1(map,new String[]{"user","approvalProcessHashSet","approvalFlow","reimburse","leave"},response);
    }

    /**
     * <AUTHOR>
     * @Date 2018/9/27 14:36
     * 查看请假详情(审批人和申请人共用)
     */
    @ResponseBody
    @RequestMapping("/leaveDetails.do")
    @MessageMapping("/leaveDetails")
    public JsonResult leaveDetails(String json,String sessionid) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer leaveId=jsonObject.getInteger("leaveId");
        Map<String,Object> map=new HashMap<String,Object>();
        PersonnelLeave leave = new PersonnelLeave();
        List<ApprovalProcess> processList = new ArrayList<>();
        List<PersonnelLeaveItem> personnelLeaveItemList = new ArrayList<>();
        Integer approvalProcessId = null;
        Integer button = 0;   //0-没有提前结束请假按钮  1-有提前结束请假按钮
        if (leaveId!=0){
            leave=userMessageService.getPersonnelLeaveById(leaveId);
            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));  //处理请假类型名称
            processList=approvalProcessService.getApprovalProcessByBusiness(leaveId,3,null); //计划请假的审批流程
//            approvalProcessId = processList.get(processList.size()-1).getId();
            approvalProcessId = processList.stream().filter(process -> !ApprovalStatus.withdraw.getName().equals(process.getApproveStatus())).reduce((first, second) -> second).orElse(new ApprovalProcess(approvalProcessId)).getId();
//            if (!"".equals(leave.getActualState()) && leave.getActualState()!=null && "1".equals(leave.getActualState())){   //有提前结束请假
            if ("1".equals(leave.getActualState())){   //有提前结束请假
                List<PersonnelLeaveItem> personnelLeaveItems = leaveService.getPersonnelLeaveItemByleaveId(leaveId,null);
                if (personnelLeaveItems.size()>0){
                    for (PersonnelLeaveItem pp:personnelLeaveItems) {
                        List<ApprovalProcess> processList2 = approvalProcessService.getApprovalProcessByBusiness(pp.getId(),8,null);
                        pp.setProcessList(processList2);
                    }
                    personnelLeaveItemList.addAll(personnelLeaveItems);

                    PersonnelLeaveItem personnelLeaveItem = personnelLeaveItems.get(0);
                    List<ApprovalProcess> processList3 = approvalProcessService.getApprovalProcessByBusiness(personnelLeaveItem.getId(),8,null);
//                    approvalProcessId = processList3.get(processList3.size()-1).getId();
                    approvalProcessId = processList3.stream().filter(process -> !Arrays.asList(ApprovalStatus.rejected.getName(), ApprovalStatus.withdraw.getName()).contains(process.getApproveStatus())).reduce((first, second) -> second).orElse(new ApprovalProcess(approvalProcessId)).getId();;

                    if (!MyStrings.nulltoempty(personnelLeaveItem.getApproveStatus()).isEmpty() && "3".equals(personnelLeaveItem.getApproveStatus()) && "2".equals(leave.getApproveStatus()) && leave.getEndTime().getTime()>System.currentTimeMillis()){
                       button=1;
                    }
                }
            }else {
                if ("2".equals(leave.getApproveStatus()) && leave.getActualEndTime().getTime()>System.currentTimeMillis()){
                    button=1;
                }
            }
        }
        map.put("button",button);   //0-没有提前结束请假按钮  1-有提前结束请假按钮
        map.put("processList",processList);//请假申请审批流程列表
        map.put("personnelLeave",leave);
        map.put("personnelLeaveItemList",personnelLeaveItemList);//提前结束请假
        map.put("approvalProcessId",approvalProcessId);   //最后的一个审批id(审批时用)
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/leaveDetails",null,null,null,null,map);   //返回 处理结果的订阅
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2021/4/1
     * 查看请假详情(审批人和申请人共用)【此接口和上面的/leaveDetails.do接口一样，只是接口的传参格式不同，以后使用首先考虑此接口】
     * 1.314 目前三前端均未使用，注释
     */
//    @ResponseBody
//    @RequestMapping("/leaveDetail.do")
//    @Deprecated//1.314 目前三前端均未使用，注释
//    public JsonResult leaveDetail(Integer leaveId) {
//        Map<String,Object> map=new HashMap<String,Object>();
//        PersonnelLeave leave = new PersonnelLeave();
//        List<ApprovalProcess> processList = new ArrayList<>();
//        List<PersonnelLeaveItem> personnelLeaveItemList = new ArrayList<>();
//        Integer approvalProcessId = null;
//        Integer button = 0;   //0-没有提前结束请假按钮  1-有提前结束请假按钮
//        if (leaveId!=0){
//            leave=userMessageService.getPersonnelLeaveById(leaveId);
//            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));  //处理请假类型名称
//            processList=approvalProcessService.getApprovalProcessByBusiness(leaveId,3,null); //计划请假的审批流程
//            approvalProcessId = processList.get(processList.size()-1).getId();
//            if (!"".equals(leave.getActualState()) && leave.getActualState()!=null && "1".equals(leave.getActualState())){   //有提前结束请假
//                List<PersonnelLeaveItem> personnelLeaveItems = leaveService.getPersonnelLeaveItemByleaveId(leaveId,null);
//                if (personnelLeaveItems.size()>0){
//                    for (PersonnelLeaveItem pp:personnelLeaveItems) {
//                        List<ApprovalProcess> processList2 = approvalProcessService.getApprovalProcessByBusiness(pp.getId(),8,null);
//                        pp.setProcessList(processList2);
//                    }
//                    personnelLeaveItemList.addAll(personnelLeaveItems);
//
//                    PersonnelLeaveItem personnelLeaveItem = personnelLeaveItems.get(0);
//                    List<ApprovalProcess> processList3 = approvalProcessService.getApprovalProcessByBusiness(personnelLeaveItem.getId(),8,null);
//                    approvalProcessId = processList3.get(processList3.size()-1).getId();
//
//                    if (!MyStrings.nulltoempty(personnelLeaveItem.getApproveStatus()).isEmpty() && "3".equals(personnelLeaveItem.getApproveStatus()) && "2".equals(leave.getApproveStatus()) && leave.getEndTime().getTime()>System.currentTimeMillis()){
//                        button=1;
//                    }
//                }
//            }else {
//                if ("2".equals(leave.getApproveStatus()) && leave.getActualEndTime().getTime()>System.currentTimeMillis()){
//                    button=1;
//                }
//            }
//        }
//        map.put("button",button);   //0-没有提前结束请假按钮  1-有提前结束请假按钮
//        map.put("processList",processList);//请假申请审批流程列表
//        map.put("personnelLeave",leave);
//        map.put("personnelLeaveItemList",personnelLeaveItemList);//提前结束请假
//        map.put("approvalProcessId",approvalProcessId);   //最后的一个审批id(审批时用)
//        return new JsonResult(1,map);
//    }

    /**
     *<AUTHOR>
     *@date 2018/10/8 2:07
     * 逐级审批请假申请
     * type 1-计划请假申请 2-提前结束请假
     * approvalStatus 1-批准 2-驳回    userId 审批人id
    */
    @ResponseBody
    @RequestMapping("/progressiveApprovalLeave.do")
    @MessageMapping("/progressiveApprovalLeave")
    public JsonResult progressiveApprovalLeave(String json, Integer approvalProcessId, String approvalStatus, Integer type, String reason,String sessionid,User user) {
        System.out.println("progressiveApprovalLeave审批请假开始");
        JSONObject jsonObject = JSONObject.parseObject(json);
        if (jsonObject != null) {
            if (approvalProcessId == null && jsonObject.keySet().contains("approvalProcessId")) {
                approvalProcessId = jsonObject.getInteger("approvalProcessId");
            }
            if (reason == null && jsonObject.keySet().contains("reason")) {
                reason = jsonObject.getString("reason");
            }
            if (approvalStatus == null && jsonObject.keySet().contains("approvalStatus")) {
                approvalStatus = jsonObject.getString("approvalStatus");
            }
            if (type == null && jsonObject.keySet().contains("type")) {
                type = jsonObject.getInteger("type");
            }
        }
        JsonResult result = leaveService.progressiveApprovalLeave(approvalProcessId, approvalStatus, type, reason, user, sdf);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/notice",null,null,null,null,result.getData());   //返回 处理结果的订阅
        System.out.println("progressiveApprovalLeave审批请假结束");
        return result;
    }
    //-----------------------------以下的为加班----------------------------------//

    /**
     *<AUTHOR>
     *@date 2018/9/29 17:27
     * 提交计划加班申请  ruleTime-提交时候的加班提前量时间
    */
    @ResponseBody
    @RequestMapping("/addPlanOverTime.do")
    public JsonResult addPlanOverTime(User user,PersonnelOvertime personnelOvertime,String beginTime1, String endTime1,Double ruleTime) throws ParseException {
        System.out.println("addPlanOverTime.do计划加班开始");
        Map<String, Object> map = new HashMap<>();
        map = overtimeService.addPlanOverTime(user,personnelOvertime,beginTime1,endTime1,ruleTime,map);
        System.out.println("addPlanOverTime.do计划加班结束");
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/10/19 11:27
     * 提交申报加班申请
     */
    @ResponseBody
    @RequestMapping("/addDeclareOverTime.do")
    @MessageMapping("/addDeclareOverTime")
    public JsonResult addDeclareOverTime(String json,String sessionid,User user) throws ParseException{
        System.out.println("addDeclareOverTime.do申报加班开始");
        JSONObject jsonObject = JSONObject.parseObject(json);
        Map<String, Object> map = new HashMap<>();
        Integer overtimeId = jsonObject.getInteger("overtimeId");   //加班id
        String beginTime1 = jsonObject.getString("beginTime1");   //开始时间
        String endTime1 = jsonObject.getString("endTime1");   //结束时间
        Double actualDuration = jsonObject.getDouble("actualDuration");  //申报时长
        String actualReason = jsonObject.getString("actualReason");   //申报的加班事由
        overtimeService.addDeclareOverTime(sessionid,user,overtimeId,beginTime1,endTime1,actualDuration,actualReason,map);  //提交申报加班申请
        System.out.println("addDeclareOverTime.do申报加班结束");
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2018/9/27 10:22
     * 申请人加班详情
     */
    @ResponseBody
    @RequestMapping("/applyOverTimeDetails.do")
    @MessageMapping("/applyOverTimeDetails")
    public JsonResult applyOverTimeDetails(String json,String sessionid,User user) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer id=jsonObject.getInteger("id");
        HashMap<String, Object> map = new HashMap<String, Object>();
        if (id != 0) {
            map=overtimeService.myOutTimeInfoById(user.getUserID(), id, map);
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/applyOverTimeDetails",null,null,null,null,map);   //返回 处理结果的订阅
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2021/4/1
     * 申请人加班详情【此接口和上面的/applyOverTimeDetails.do接口一样，只是接口的传参格式不同，以后使用首先考虑此接口】
     * userId 登录人id  id：加班id
     */
    @ResponseBody
    @RequestMapping("/applyOverTimeDetail.do")
    public JsonResult applyOverTimeDetail(User user,Integer id) {
        HashMap<String, Object> map = new HashMap<String, Object>();
        if (id != 0) {
            map=overtimeService.myOutTimeInfoById(user.getUserID(), id, map);
        }else {
            map.put("status",0);//获取失败
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2018/9/27 14:13
    * 审批人查看加班详情
    */
    @ResponseBody
    @RequestMapping("/approverOverTimeDetails.do")
    @MessageMapping("/approverOverTimeDetails")
    public JsonResult approverOverTimeDetails(String json,String sessionid,Integer outTimeId) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        if (outTimeId==null) {
            outTimeId = jsonObject.getInteger("outTimeId");
        }
        Map<String,Object> map=new HashMap<String,Object>();
        if (outTimeId!=0){
            map = overtimeService.getOverTimeDetails(outTimeId,map);
        }else {
            map.put("status",0);//获取失败
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/approverOverTimeDetails",null,null,null,null,map);   //返回 处理结果的订阅
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/10/9 12:23
     * 逐级审批加班申请【包括修改加班时长的审批和申报加班审批】【此接口中的加班添加到考勤中的接口】
     * approvalStatus（1-批准 2-驳回）  type (1-计划加班 2-申报加班 3-批准加班) approveDuration(批准时长) approveExplain（批准说明） reason（驳回原因） id（加班详情id）
    */
    @ResponseBody
    @RequestMapping("/progressiveApprovalOutTime.do")
    @MessageMapping("/progressiveApprovalOutTime")
    public JsonResult progressiveApprovalOutTime(String json, Integer approvalProcessId, Integer id, Integer type, Double approveDuration, String approveExplain, String reason, String approvalStatus, String sessionid,User user) {
        System.out.println("progressiveApprovalOutTime审批加班开始");
        JSONObject jsonObject = JSONObject.parseObject(json);
        if(jsonObject!=null) {
            if (approvalProcessId == null && jsonObject.keySet().contains("approvalProcessId")) {
                approvalProcessId = jsonObject.getInteger("approvalProcessId");
            }
            if (id == null && jsonObject.keySet().contains("id")) {
                id = jsonObject.getInteger("id");
            }
            if (approveDuration == null && jsonObject.keySet().contains("approveDuration")) {
                approveDuration = jsonObject.getDouble("approveDuration");
            }
            if (approveExplain == null && jsonObject.keySet().contains("approveExplain")) {
                approveExplain = jsonObject.getString("approveExplain");
            }
            if (reason == null && jsonObject.keySet().contains("reason")) {
                reason = jsonObject.getString("reason");
            }
            if (approvalStatus == null && jsonObject.keySet().contains("approvalStatus")) {
                approvalStatus = jsonObject.getString("approvalStatus");
            }
            if (type == null && jsonObject.keySet().contains("type")) {
                type = jsonObject.getInteger("type");
            }
        }
        JsonResult result = overtimeService.progressiveApprovalOutTime(approvalProcessId, id, type, approveDuration, approveExplain, reason, approvalStatus, sessionid,user);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/notice",null,null,null,null,result.getData());   //返回 处理结果的订阅
        System.out.println("progressiveApprovalOutTime审批加班结束");
        return result;
    }

    /**
     *<AUTHOR>
     *@date 2018/10/10 13:21
     *实际未加班
    */
    @ResponseBody
    @RequestMapping("/deletePersonnelOvertime.do")
    @MessageMapping("/deletePersonnelOvertime")
    public JsonResult deletePersonnelOvertime(String json,String sessionid) {
        System.out.println("deletePersonnelOvertime实际未加班开始");
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer outTimeId=jsonObject.getInteger("outTimeId");
        Integer status=0;

        if (outTimeId!=0){
            status=overtimeService.deletePersonnelOvertime(outTimeId,1);
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/notice",null,null,null,null,status);   //返回 处理结果的订阅
        System.out.println("deletePersonnelOvertime实际未加班结束");
        return new JsonResult(1,status);

    }

    /**
    * <AUTHOR>
    * @Date 2018/11/6 16:53
    * 申请人 请假 筛选查询功能
     * state 1- 自定义 ，2 -仅看年报，3- 仅看月报
    */
    @ResponseBody
    @RequestMapping("/myLeaveQuery.do")
    public JsonResult  myLeaveQuery(User user,Integer state,Integer approveStatus,String beginDate,String endDate) throws ParseException {

        SimpleDateFormat sdf1=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=NewDateUtils.today(sdf1.parse(beginDate));
        Date end=NewDateUtils.tomorrow(sdf1.parse(endDate));
        if (end.getTime()>new Date().getTime()){
            end=new Date();
        }
        long between = NewDateUtils.getDaies(end.getTime() - begin.getTime());
        List<LeaveQuery> jsonResult=new ArrayList<>();
        if ((state==1&&between <= 30)||state==3){
            List<PersonnelLeave> pls=leaveService.getPersonnelLeaveListQuery(user.getUserID(),approveStatus,begin,end);
            jsonResult =leaveService.getLeaveStatistics(pls,user.getOid());
        }else {
            jsonResult =leaveService.getLeaveNum(user,approveStatus,begin,end);
        }
        return new JsonResult(1,jsonResult);
    }

    /**
    * <AUTHOR>
    * @Date 2018/11/8 10:07
    *  请假审批  筛选查询功能
     *  state 1- 自定义 ，2 -仅看年报，3- 仅看月报
    */
    @ResponseBody
    @RequestMapping("/approvalLeaveQuery.do")
    public JsonResult approvalLeaveQuery(User user,Integer state,Integer approveStatus,String beginDate,String endDate,Integer applyUserId) throws ParseException {
        SimpleDateFormat sdf1=new SimpleDateFormat("yyyy-MM-dd");
        Date begin=NewDateUtils.today(sdf1.parse(beginDate));
        Date end=NewDateUtils.tomorrow(sdf1.parse(endDate));
        if (end.getTime()>new Date().getTime()){
            end=new Date();
        }

        long between = NewDateUtils.getDaies(end.getTime() - begin.getTime());
        List<LeaveQuery> jsonResult=new ArrayList<>();
        if ((state==1&&between <= 30)||state==3){
            List<PersonnelLeave> pls=leaveService.getApprovalLeaveListQuery(user.getUserID(),approveStatus,begin,end,applyUserId);
            jsonResult =leaveService.getLeaveStatistics(pls,user.getOid());
        }else {
            jsonResult =leaveService.getApprovalNum(user,approveStatus,begin,end,applyUserId);
        }
        return new JsonResult(1,jsonResult);
    }

    /**
    * <AUTHOR>
    * @Date 2018/11/12 10:56
    * 请假、加班 获取申请人列表
     * businessType  2-加班  3-请假
    */
    @ResponseBody
    @RequestMapping("/getApplyUserList.do")
    public JsonResult  getApplyUserList(User user,Integer businessType){
        List<UserHonePageDto> userHonePageDtoList=approvalProcessService.getUserHonePageDtoListByApprovalId(user.getUserID(),businessType);
        return new JsonResult(1,userHonePageDtoList);
    }

    /**
    * <AUTHOR>
    * @Date 2018/11/12 11:08
    * 申请人 加班查询 条件筛选
     * type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班 5-加班申请被驳回
     * state 1- 自定义 ，2 -仅看年报，3- 仅看月报
    */
    @ResponseBody
    @RequestMapping("/myOverTimeQuery.do")
    public JsonResult myOverTimeQuery(User user,Integer type,Integer state,String beginDate,String endDate) throws ParseException {
        SimpleDateFormat sdf1=new SimpleDateFormat("yyyy-MM-dd");

        Date begin=NewDateUtils.today(sdf1.parse(beginDate));
        Date end=NewDateUtils.getLastTimeOfDay(sdf1.parse(endDate));
        long between = NewDateUtils.getDaies(end.getTime()+1 - begin.getTime());
        if (end.getTime()>new Date().getTime()){
            end=new Date();
        }
        HashMap<String,Object> hashMap=new HashMap<>();
        if ((state==1&&between <= 30)||state==3){
            List<PersonnelOvertime> personnelOvertimeList=overtimeService.getPersonnelOvertimeListQuery(user.getUserID(),type,begin,end);
            hashMap=overtimeService.getOvertimeStatistics(personnelOvertimeList);
            hashMap.put("haveDetail",true);
        }else {
            hashMap=overtimeService.getOverTimeSum(user.getUserID(),type,begin,end,null,false);
            hashMap.put("haveDetail",false);
        }
        hashMap.put("type",type);
        return new JsonResult(1,hashMap);
    }

    /**
    * <AUTHOR>
    * @Date 2018/11/14 9:32
    * 审批人  加班查询  条件筛选
     * type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班 5-加班申请被驳回
     * state 1- 自定义 ，2 -仅看年报，3- 仅看月报
    */
    @ResponseBody
    @RequestMapping("/approvalOverTimeQuery.do")
    public JsonResult approvalOverTimeQuery(User user,Integer type,Integer state,String beginDate,String endDate,Integer applyUserId ) throws ParseException {
        SimpleDateFormat sdf1=new SimpleDateFormat("yyyy-MM-dd");

        Date begin=NewDateUtils.today(sdf1.parse(beginDate));
        Date end=NewDateUtils.getLastTimeOfDay(sdf1.parse(endDate));
        long between = NewDateUtils.getDaies(end.getTime()+1 - begin.getTime());
        if (end.getTime()>=new Date().getTime()){
            end=new Date();
        }
        HashMap<String,Object> hashMap=new HashMap<>();
        if ((state==1&&between <= 30)||state==3){
            List<PersonnelOvertime> personnelOvertimeList=overtimeService.getApprovalOverTimeListQuery(user.getUserID(),type,begin,end,applyUserId);
            hashMap=overtimeService.getOvertimeStatistics(personnelOvertimeList);
            hashMap.put("haveDetail",true);
        }else {
            hashMap=overtimeService.getOverTimeSum(user.getUserID(),type,begin,end,applyUserId,true);
            hashMap.put("haveDetail",false);
        }
        hashMap.put("type" ,type);
        return new JsonResult(1,hashMap);
    }

    //----------------2.78/2.79职工动态的部分接口，有的接口与上面的接口共用-------------------------------
    /**
     *<AUTHOR>
     *@date 2021/4/1 9:13
     *获取请假动态列表  type 1-职工中的   2-我的团队（本人及其下属）
    */
    @ResponseBody
    @RequestMapping("/getLeaveList.do")
    public JsonResult getLeaveList(User user,Integer type) throws ParseException {
        Map<String,Object> map=new HashMap<>();
        List<PersonnelLeave> untreated1 = leaveService.getPersonnelLeaveListByUser(user.getUserID(),user.getOid(), "1", "0", null, null,type);  //请假待处理
        List<PersonnelLeave> untreated2 = leaveService.getPersonnelLeaveListByUser(user.getUserID(),user.getOid(), "1", "1", null, null,type);  //提前结束请假待处理
        List<PersonnelLeave> leaveHandle = new ArrayList<>();
        if (untreated1.size() > 0) {
            leaveHandle.addAll(untreated1);
        }
        if (untreated2.size() > 0) {
            leaveHandle.addAll(untreated2);
        }
        for (PersonnelLeave leave:leaveHandle) {
            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));
        }
        List<PersonnelLeave> leaveApproved = leaveService.getPersonnelLeaveListByUser(user.getUserID(),user.getOid(), "2", null, null, null,type);   //已批准
        for (PersonnelLeave leave:leaveApproved) {
            leave.setLeaveTypeName(leaveService.getLeaveTypeName(leave.getLeaveType(),leave.getType()));
        }
        map.put("leaveHandle",leaveHandle);  //待处理
        map.put("leaveApproved",leaveApproved);   //已批准
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/4/1 9:13
     *获取加班动态列表
     * type 1-职工中的(所有人的)   2-我的团队（本人及其下属）
     */
    @ResponseBody
    @RequestMapping("/getOverTimeList.do")
    public JsonResult getOverTimeList(User user,Integer type) {
        Map<String,Object> map=new HashMap<>();
        List<PersonnelOvertime> untreated = overtimeService.getPersonnelOvertimeListByUser(user.getOid(),null,user.getUserID(), 1, null,type);
        List<PersonnelOvertime> approved = overtimeService.getPersonnelOvertimeListByUser(user.getOid(),null,user.getUserID(), 2, null,type);
        map.put("overTimeHandle",untreated);  //待处理
        map.put("overTimeApproved",approved);   //已批准
        return new JsonResult(1,map);
    }

    //----------------1.154加班请假优化 部分接口，有的接口与上面的接口共用-------------------------------
    /**
     *<AUTHOR>
     *@date 2022/03/17
     * 补报请假申请
     * beginTime(开始时间)  endTime（结束时间）  leaveType（请假类型）   reason（事由）
     */
    @ResponseBody
    @RequestMapping("/supplementaryLeave.do")
    public JsonResult supplementaryLeave(User user,String beginTime, String endTime,Integer leaveType, String reason) {
        Map<String, Object> map = new HashMap<>();
        map = leaveService.addLeave(beginTime,endTime,leaveType,reason,LeaveService.Kind.supplementary.getIndex(),user,null,map);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2022/03/18
     * 补报加班申请
     * beginTime(开始时间)  endTime（结束时间）  duration（时长）   reason（加班事由）
     */
    @ResponseBody
    @RequestMapping("/supplementaryOvertime.do")
    public JsonResult supplementaryOvertime(User user,String beginTime, String endTime,Double durationSub,String reason) {
        Map<String, Object> map = overtimeService.supplementaryOvertime(user,beginTime,endTime,durationSub,reason);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2022/03/22 12:23
     * 获取指派加班的需加班者
     *
     */
    @ResponseBody
    @RequestMapping("/getOverTimeUsers.do")
    public JsonResult getOverTimeUsers(User user) {
        Map<String,Object> map = new HashMap<>();
        List<User> users = userService.getUserDtos(user.getUserID(),3);
        map.put("users",users);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2022/03/16
     * 指派加班申请
     * beginTime(开始时间)  endTime（结束时间）  duration（时长）   reason（加班事由）  overtimeUserIds(需加班者id，用逗号隔开)
     */
    @ResponseBody
    @RequestMapping("/assignOvertime.do")
    public JsonResult supplementaryOvertime(User user,String beginTime, String endTime,Double durationSub,String reason,String overtimeUserIds) {
        Map<String, Object> map = overtimeService.assignOvertime(user,beginTime,endTime,durationSub,reason,overtimeUserIds);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 被指派的加班列表
     * @Date 2022/3/19
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/assignOvertimeList.do")
    @MessageMapping("/assignOvertimeList")
    public JsonResult assignOvertimeList(User user){
        Map<String, Object> map = new HashMap<>();
        List<PersonnelOvertime> personnelOvertimes = overtimeService.assignOvertimeList(user.getUserID(),"1",null,null);
        map.put("personnelOvertimes",personnelOvertimes);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 指派加班详情
     * @Date 2022/3/19
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/assignDetail.do")
    public JsonResult assignDetail(Integer assignOvertimeId){
        Map<String, Object> map = new HashMap<>();
        PersonnelOvertime personnelOvertime = overtimeService.getPersonnelOvertimeById(assignOvertimeId);
        List<ApprovalProcess> processes = approvalProcessService.getApprovalProcessAll(null,assignOvertimeId,null,60);
        map.put("personnelOvertime",personnelOvertime);
        map.put("processes",processes);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2022/03/22 12:23
     * 审批指派加班
     * approveStatus（1-同意 2-驳回） reason（驳回原因）
     */
    @ResponseBody
    @RequestMapping("/approvalAssignOutTime.do")
    public JsonResult approvalAssignOutTime(User user,String approveStatus,Integer assignOvertimeId,String reason) {
        Map<String,Object> map = overtimeService.approvalAssignOutTime(user,assignOvertimeId,approveStatus,reason);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2022/03/23 12:23
     * 指派加班查询（不同意指派）
     * type 1-近七日 2-本月 3-自定义
     */
    @ResponseBody
    @RequestMapping("/assignOvertimeQuery.do")
    public JsonResult assignOvertimeQuery(User user,String beginTime,String endTime,Integer type) {
        Map<String,Object> map = new HashMap<>();
        Date begin;
        Date end = new Date();
        if (1==type){
            begin = NewDateUtils.today(NewDateUtils.changeDay(new Date(),-6));
        }else if (2==type){
            begin = NewDateUtils.changeMonth(new Date(),0); //月的第一天
        }else {
            begin = NewDateUtils.today(NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd"));
            if (NewDateUtils.today(NewDateUtils.dateFromString(endTime,"yyyy-MM-dd")).getTime()<new Date().getTime()){
                end = NewDateUtils.today(NewDateUtils.dateFromString(endTime,"yyyy-MM-dd"));
            }
        }
        List<PersonnelOvertime> personnelOvertimes = overtimeService.assignOvertimeList(user.getUserID(),"3",begin,end);
        map.put("personnelOvertimes",personnelOvertimes);
        map.put("begin",begin);
        map.put("end",end);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2022/03/28 8:00
     * 日常事务 获取动态按钮块
     */
    @ResponseBody
    @RequestMapping("/getAffairs.do")
    public JsonResult getAffairs(User user){
        Integer oid=user.getOid();
        ArrayList<String> arrayList=new ArrayList<>();
        arrayList.add("我要请假");
        if (roleService.getItemState(oid,"supplementaryLeave")){
            arrayList.add("我要事后补假");
        }
        arrayList.add("我要申请加班");
        if (roleService.getItemState(oid,"supplementaryOvertime")){
            arrayList.add("我要补报加班");
        }
        if (1!=user.getOrdinaryEmployees()){
            arrayList.add("指派下属加班");
        }
        return new JsonResult(1,arrayList);

    }
}