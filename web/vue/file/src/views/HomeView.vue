<template>
  <div class="">
    <div class="logo">
      <span>End<PERSON> + <PERSON><PERSON></span>
      <img src="../assets/logo.png" class="logoimg" />
    </div>
    <div>
      <div class="search-wrapper">
        <input
          placeholder="搜索常见问题"
          type="text"
          placeholder-class="search-placeholder"
          class="search-input"
          v-model="input1"
        />
        <img src="../assets/search.png" class="search">
        <span class="searchbtn" @click="goSearch">搜索</span>
      </div>
    </div>
<!--    <div class="navbtn-wrapper">-->
<!--      <a class="navbtn-around" href='https://vod.btransmission.com/html/vod/vod.html'>-->
<!--        <span class="navbtn">测试vod静态视频</span>-->
<!--      </a>-->
<!--    </div>-->
<!--        <a href='https://vod.btransmission.com/html/vod/vod.html'><i>（点击测试视频）</i></a>-->
    <div class="navbtn-wrapper">
      <router-link :to="{path:'/vod',query:{}}" class="navbtn-around">
        <span class="navbtn">测试vue播放视频</span>
      </router-link>
<!--        <router-link :to="{path:'/vod',query:{}}" class="around"><i>（点击测试视频）</i></router-link>-->
    </div>
    <div class="navbtn-wrapper">
      <router-link :to="{path:'/myOrders',query:{}}" class="navbtn-around">
<!--        <span class="navbtn" onclick="alert('敬请期待');return true;">我的订单</span>-->
        <span class="navbtn">我的订单</span>
      </router-link>
    </div>
    <div v-show="!showAllPackes">
      <div style="position: relative">
        <div class="toggleBtn" @click="toggleNav">
          <img src="../assets/left_arrow.png" />
        </div>
        <div class="nav">
          <div class="listcon">
            <span v-for="(item, index) in list"  :key="index" class="listI" @click="clickItem(item)"
            :class="activeFolderID == item.id ? 'active':''" >
              <span class="title"> {{ item.name }} </span>
            </span>
          </div>
        </div>
      </div>
      <div class="packges">
        <van-swipe class="swiper">
          <van-swipe-item v-for=" (itemarr , indexArr) in childFolder" :key="indexArr">
            <div class="list">
              <div class="pakI" v-for="(itemj, indexj) in itemarr" :key="indexj" >
                <div @click="gopage(itemj)" class="packcon">
                  <span>{{ itemj.name }}</span>
                </div>
              </div>
            </div>
          </van-swipe-item> 
        </van-swipe>
 
        <!-- <div class="dots" hidden="{{ childFolder.length == 1 }}">
          <div v-for=" (itemarr , indexArr2) in childFolder" :key="indexArr2">
            <div class="dot{{indexArr2 == current ? ' active' : ''}}"></div>
          </div>
        </div> -->
      </div>
    </div>
    <div v-show="showAllPackes">
      <div class="nav2">
        <span>为您提供以下分类</span>
        <div class="toggleBtn" @click="toggleNav">
          <img src="../assets/left_arrow.png">
        </div>
      </div>
      <div>
        <div class="list">
          <div v-for="(item , index2) in list" :key="index2"
            class="pakI"
            data-id="item.id"
            @click="clickItem(item)"
          >
            <div class="packcon">
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div></div>
  </div> 
</template>

<style>
.logo {
  text-align: right;
  height: 33px;
  line-height: 33px;
  padding: 0 10px;
}
.logo span {
  font-size: 22px;
}
.logoimg {
  width: 38px;
  height: 25px;
}
.search-wrapper {
  height: 48px;
  padding: 6px 13px;
  /* background: #960e0e; */
  position: relative;
  margin-bottom: 12px;
}
.search-input {
  width: 100%;
  height: 100%;
  padding-left: 32px;
  font-size: 14px;
  color: #666;
  box-sizing: border-box;
  border: 1px solid #ccc;
}
.search {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 20px;
  top: 22px;
}
.search-placeholder {
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #999999;
}
.scroll-item {
  box-shadow: 0 0 3px #ccc;
  float: left;
  background: #eee;
  width: 50px;
  height: 25px;
  margin-right: 10px;
}
.searchbtn {
  position: absolute;
  top:6px;
  right: 13px;
  background-color: #63a8c9;
  color: #fff;
  height: 35px;
  line-height: 35px;
  padding: 0 20px;
  z-index: 99;
}
.navbtn-wrapper {
  margin: 1px 0;
}
.navbtn-around{
  width: 100%;
}
.navbtn {
  background-color: #63a8c9;
  color: #fff;
  display:block;
  width: 100%;
  height: 35px;
  line-height: 35px;
  padding: 0 20px;
  text-align: center;
}
.xflow {
  margin: 50px;
}

.nav {
  padding: 15px;
  overflow-x: auto;
  position: relative;
  box-shadow: 0 0 3px #ccc;
  white-space: nowrap;
  margin-bottom: 15px;
  width: 100%;
  box-sizing: border-box;
}
.nav .listcon {
  word-break: none;
}
.nav .active {
  color: #63a8c9;
}
.nav .listI {
  padding: 0 20px;
  border-left: v-if(1px solid #63a8c9);
  font-size: 17px;
}
.toggleBtn {
  position: absolute;
  right: 0;
  top: 2px;
  background: #fff;
  width: 30px;
  height: 45px;
  text-align: center;
  background-color: #fff;
  z-index: 1;
}
.toggleBtn img {
  width: 20px;
  height: 20px;
  transform: rotate(-90deg);
  margin-top: 20px;
}
.list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 10px;
}
.list .pakI {
  flex-grow: 0;
  overflow: hidden;
  width: 33%;
  height: 60px;
  position: relative;
  margin-bottom: 8px;
}
.list .pakI .packcon {
  display: flex;
  width: 90%;
  height: 98%;
  background-color: #e7ecf0;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 2px 2px #ccc;
  border-radius: 4px;
  align-items: center;
  flex-direction: row;
  position: relative;
  right: -7%;
}
.packcon text {
  display: block;
  color: #596a75;
  font-size: 10px;
  width: 100%;
  text-align: center;
}
.packges {
  position: relative;
}

.swiper {
  height: 166px;
}

.swiperContainer {
  position: relative;
}
.img {
  width: 100%;
  height: 100%;
}
.imageCount {
  width: 60px;
  height: 25px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  line-height: 25px;
  color: #fff;
  text-align: center;
  font-size: 13px;
  position: absolute;
  left: 13px;
  bottom: 10px;
}
.dots {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 10px;
  display: flex;
  justify-content: center;
}
.dots .dot {
  margin: 0 2px;
  width: 25px;
  height: 4px;
  background: #999;
  border-radius: 4px;
  transition: all 0.6s;
}
.dots .dot.active {
  width: 25px;
  background: #63a8c9;
}
.nav2 {
  position: relative;
  height: 50px;
  padding: 0 20px;
  /* background: #ccc; */
  line-height: 50px;
}
.nav2 .toggleBtn {
  right: 20px;
}
.nav2 .toggleBtn img {
  transform: rotate(90deg);
}
</style>
<script>
import { getWxHomePage, getWxParentAndChildDir, authInit } from "@/api/api";
import JSONBig from 'json-bigint'
 export default {
  data() {
    return {
      inputValue: "",
      input1:"",
      searchCon: "",
      showAllPackes: false,
      list: [],
      childFolder: [],
      background: ["demo-text-1", "demo-text-2", "demo-text-3"],
      current: 0,
      activeFolderID: 0,
      system: {},
    };
  }, 
  components:{
  },
  mounted() {
    this.start();
  },
  methods: { 
    start(){
      let that = this
      authInit(that).then( (res)=> {
        let data = res.success ? res : res.data
        console.log('authInit then', data)
        if(data.success == 301) {
          this.$router.push({
            name: data.url,
            params: data.params
          });
        } else if(data.success > 0) {
          console.log('登录成功！')
          this.getList()
        } else {
          let error = data.error
          console.log('登录失败！error msg', data, error)
          // that.getList()

          // console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
          switch (error.code) {
            case '2': //需要短信验证激活
              break
            case '3': //需要注册，调用auth/sendMessageVerificationCodeRegister.do注册通用框架账号
              //	测试
              // that.getTxtFun()
              // console.log('user', auth.getUser())
              // 跳转注册页
              break
            case '4': //可能是授权或者code问题，建议重试tt.login
              // that.ttLogin()
              break
            case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
              console.log('服务器问题，比如wonderss服务器访问微信/字节服务器异常')
              break
          }
          that.reload()
        }
      }).catch(function (error) {
        console.log('authInit error', error)
        that.reload()
      }) 
    },
    getList() {
      this.isLoad = true;
      getWxHomePage().then((res) => {
        console.log(res);
        this.isLoad = false;
      console.log('getList res', res)
        let data = res.data
        if (data) {
          let firstList = data.listFirstCategory || [];
          let childs = data.childFolder || [];
          let childFolder = [];
          console.log('childs ', childs)
          for (var i = 0, len = childs.length; i < len; i += 6) {
            childFolder.push(childs.slice(i, i + 6));
          }
          this.list = firstList
          this.childFolder = childFolder
          console.log('this.list', this.list)
          console.log('this.childFolder', this.childFolder)
          if (firstList.length > 0) {
            this.activeFolderID = firstList[0]["id"];
          }
        }
      });
    },  
    toggleNav() {
      this.showAllPackes = !this.showAllPackes
    },
    clickItem(info) { 
      this.activeFolderID = info.id
      this.showAllPackes = false
      this.isLoad = true;
      getWxParentAndChildDir(Number(info.id)).then((res)=>{
        this.isLoad = false;

        if (res.data) {
          let childs = res.data.childFolder;
          let fileList = res.data.list;
          console.log('fileList', fileList)
          if (childs && childs.length > 0) {
            let childFolder = [];
            for (var i = 0, len = childs.length; i < len; i += 6) {
              childFolder.push(childs.slice(i, i + 6));
            } 
            this.childFolder = childFolder
          } else { 
            this.childFolder = []
          }
        }
      })
    },
    goSearch(){
      let p = this.searchCon;
      this.$router.push({ 
        name: "folderOrFile", 
        params: { p: p, s:1 } 
      });
    },
    gopage (item) {
      let id = item.id;
      let p = item.name;
      console.log('跳过去之前 id p =' , id , p)
      let params = { p: p, id:id } 
      console.log('params=', params)
      localStorage.setItem('param', JSONBig.stringify(params))
      this.$router.push({ 
        name: "folderOrFile", 
        params: params
      });
    },
    reload() {
      if(isWeixinBrowser()) {
        wx.miniProgram.navigateTo({url: '/pages/pages/index/index'})
      }
    }
  },
};
</script>