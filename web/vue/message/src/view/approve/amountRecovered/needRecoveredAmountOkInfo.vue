<template>
  <div id="needRecoveredAmountOkInfo"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="已收回"></TY_NavTop>
    <div class="detailInfo" v-if="details.financeReceipt">
      <div v-if="details.financeReceipt.method==1">
        <div>
          <span><span class="ttl1">所收到款项的型式</span><span>现金 </span></span>
        </div>
        <div >
          <span class="ttl1">金额</span><span> {{ details.financeReceipt.amount && details.financeReceipt.amount.toFixed(2) }}元</span>
        </div>
        <div>
          <span class="ttl1">收到日期</span><span> {{ details.financeReceipt.factDate | formatDay('YYYY-MM-DD')}} </span>
        </div>
        <div>
          <span class="ttl1">摘要</span><br><span> {{details.financeReceipt.summary }} </span>
        </div>
      </div>

      <div v-if="details.financeReceipt.method==5">
        <div>
          <span><span class="ttl1">所收到款项的型式</span><span>银行转账 </span></span>
        </div>
        <div>
          <span><span class="ttl1">银行账户 </span><span>{{details.financeAccount.name }} </span></span>
        </div>
        <div>
          <span><span class="ttl1">账户类型 </span><span>{{details.financeAccount.isPublic == '1' ? '对公户 ': '非对公户'  }} </span></span>
        </div>
        <div>
          <span><span class="ttl1">开户行 </span><span>{{details.financeAccount.bankName }} </span></span>
        </div>
        <div>
          <span><span class="ttl1">账号 </span><span>{{details.financeAccount.account }} </span></span>
        </div>
        <div >
          <span class="ttl1">金额 </span><span> {{ details.financeReceipt.amount && details.financeReceipt.amount.toFixed(2) }}元</span>
        </div>
        <div>
          <span class="ttl1">到账日期 </span><span> {{details.financeReceipt.planDate | formatDay('YYYY-MM-DD') }} </span>
        </div>
        <div v-if="type === 2">
          <span class="ttl1">摘要</span><br><span> {{details.financeReceipt.summary }} </span>
        </div>

      </div>

      <div v-if="details.financeReceipt.method==4">
        <div>
          <span><span class="ttl1">{{type === 1? '拟付款方式 ': '付款方式'}} </span><span>承兑汇票（外）</span></span>
        </div>
        <div @click="seeticket">
          <span><span class="ttl1">汇票号 </span><span>{{ details.financeReturn.returnNo }}<span class="el-icon-arrow-right"></span></span></span>
        </div>
        <div>
          <span><span class="ttl1">所开具发票或收据的金额 </span><span>{{ details.financeReturn.billAmount.toFixed(2) }}元</span></span>
        </div>
        <div>
          <span><span class="ttl1">到期日 </span><span>{{ details.financeReturn.expireDate | formatDay('YYYY-MM-DD') }}</span></span>
        </div>
        <div>
          <span><span class="ttl1">出具的银行 </span><span>{{ details.financeReturn.originalCorp }}</span></span>
        </div>
        <div v-if="type === 2">
          <div>
            <span><span class="ttl1">收款单位接收日期 </span><span>{{ details.financeReceipt.factDate | formatDay('YYYY-MM-DD') }}</span></span>
          </div>
          <div>
            <span><span class="ttl1">收款单位接收人 </span><span>{{ details.financeReturn.operatorName }}</span></span>
          </div>
          <div>
            <span class="ttl1">摘要</span><br><span> {{details.financeReceipt.summary }} </span>
          </div>
        </div>

      </div>

      <div v-if="details.financeReceipt.method==3">
        <div>
          <div>
            <span><span class="ttl1">所收到款项的型式</span><span>转帐支票</span></span>
          </div>
          <div @click="seeticket">
            <span class="ttl1">支票号</span><span> {{ details.financeReturn.returnNo }}<span class="el-icon-arrow-right"></span></span>
          </div>
          <div>
            <span class="ttl1">所开具发票或收据的金额</span><span> {{details.financeReturn.billAmount.toFixed(2) }}元 </span>
          </div>
          <div>
            <span class="ttl1">到期日</span><span> {{details.financeReturn.expireDate | formatDay('YYYY-MM-DD') }} </span>
          </div>
          <div>
            <span class="ttl1">出具的银行</span><span> {{details.financeReturn.bankName }} </span>
          </div>
            <div>
              <span class="ttl1">收款单位接收日期</span><span> {{details.financeReceipt.factDate | formatDay('YYYY-MM-DD') }} </span>
            </div>
            <div>
              <span class="ttl1">收款单位接收人</span><span> {{details.financeReturn.operatorName }} </span>
            </div>
            <div>
              <span class="ttl1">摘要</span><br><span> {{details.financeReceipt.summary }} </span>
            </div>
        </div>
      </div>
    </div>
    <div class="marT20" v-if="nowApproveItem">
      <h4>审批记录</h4>
      <div>
        <div class="process">
          <div>
            <div class="item-content">

              <div v-for="it in details.approvalProcessList" v-bind:key="it.id">
                <!--                50-可付款（采购的票款处理,出纳的可付款） 51-待复核（采购的票款处理,出纳与复核审批人-待复核） 52-待付款（采购的票款处理,出纳-待付款）-->
                <!--                // 53-付款方式修改（采购的票款处理,出纳-付款方式修改）-->

                <!--                63-待付款审批(1.229采购的预付款中) 64-可付款(1.229采购的预付款中) 65-待复核(1.229采购的预付款中-待复核) 66-待付款(1.229采购的预付款中)-->
                <!--                //67-付款方式修改(1.229采购的预付款中)-->
                <!--  888888888888  -->
                <div v-if="it.approveStatus === '2' && it.businessType == 70" class="processItem">
                  <div>付款方式确认</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 71" class="processItem">
                  <div>付款复核通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 73" class="processItem">
                  <div>付款方式修改</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '5'" class="processItem">
                  <div>
                    <span v-if="financeReceipt.businessType == '1'">现金付讫</span>
                    <span v-if="financeReceipt.businessType == '2'">报销款已转支票</span>
                    <span v-if="financeReceipt.businessType == '3'">报销款已转支票</span>
                    <span v-if="financeReceipt.businessType == '4'">报销款已转承兑汇票</span>
                    <span v-if="financeReceipt.businessType == '5'">报销款已转账</span>
                  </div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <!-- 最后一条审批记录展示 -->
              <div class="color-blue">
                <!--<span v-else-if="nowApproveItem.approveStatus === '1'&& nowApproveItem.toUser>0 && !isNowApprover ">{{ nowApproveItem.userName }}为下一个审批人</span>-->
                <span v-else-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
              </div>

              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 63">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 64">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue">
                <span v-if="details.approveStatus == '2'">本次报销已完结！</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.el-icon-arrow-right{
  margin-left: 10px;
}
.tbddd .el-table__header-wrapper{
  position: relative;
  left: 18px;
}
.marT20{
  margin-top: 20px;
  background: #fff;
  padding: 20px;
}
.detailInfo{
  padding:10px 20px 10px 20px;
  background:#fff; font-size:14px;
  border-bottom:1px solid #ddd ;
  line-height: 26px;
  .ttl1{
    display: inline-block;
    width: 114px;
    margin-right: 10px;
  }
  .ttl1+span{
    float: right;
  }
}
</style>
<script>
import * as moment from 'moment'
export default {
  name: 'needRecoveredAmountOkInfo',
  data () {
    return {
      loading: true,
      toggle: -1,
      details: {
      },
      nowApproveItem:null
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let payid =  this.$route.params.id
    let url = '../../../amountRecovered/getRecoveredDetail.do'
    let params = { recoveredId: payid}
    this.getInfo(url,params)
  },
  destroyed: function () {
  },
  methods: {
    seeticket: function () {
      let that = this
      let item = this.details.financeReturn
      item.pund = 1
      if (this.details.financeReceipt.method === '4') {
        item.type = 3
      } else if (this.details.financeReceipt.method === '3') {
        item.type = 2
      }
      console.log(item)
      localStorage.setItem('invoiceTicketInfo', JSON.stringify(item))
      that.$router.push({
        path: `/purchasePaymentInvoiceInfo`
      })
    },
    getInfo: function (url,params) {
      let that = this
      that.loading = false
      this.axios.post(url, params)
        .then(({ data }) => {
          that.details = data.data
          console.log('that.details', that.details)
          let arr = that.details.approvalProcessList
          that.nowApproveItem =  arr[arr.length-1]
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  },
  computed: {
  }
}
</script>
