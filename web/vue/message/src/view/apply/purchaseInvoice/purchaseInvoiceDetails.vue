<template>
  <div id="purchaseInvoiceDetails">
    <TY_NavTop :title="title" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading" v-if="details.poPaymentApplication">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div class="con1">
        {{ details.poPaymentApplication.type | formatType(details.poPaymentApplication.amount) }}
        <div v-if="details.poPaymentApplication.type === '3'">本次付款申请没有附带的票据</div>
        <p>申请人：{{ details.poPaymentApplication.createName }}{{ details.poPaymentApplication.createDate | formatDay('YYYY-MM-DD HH:mm:ss') }}</p>
      </div>
      <div class="panel-content">
        <div>
          <table style="width:100%;">
            <tr>
              <td>供应商：{{ details.fullName  }}</td>
              <td>{{ details.codeName }}</td>
            </tr>
            <tr>
              <td>订单总额：{{ details.poOrders.amount && details.poOrders.amount.toFixed(2) }}元</td>
              <td>订单号：{{ details.poOrders.sn }} </td>
            </tr>
            <tr>
              <td>订单日期：{{ details.poOrders.createDate | formatDay('YYYY-MM-DD') }}</td>
              <td></td>
            </tr>
          </table>
        </div>
        <div class="panel-content">
          <div class="text-right">
            <el-link style="color: #409eff; margin-right: 20px;" @click="orderProgressRade = !orderProgressRade">查看订单进度
              <i :class="!orderProgressRade?'el-icon-arrow-down':'el-icon-arrow-up'"></i></el-link>
            <el-link v-on:click="toggle = -toggle">审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link>
          </div>
          <div v-show="toggle > 0">
            <div class="item-content">
              <div class="processItem">
                <div>提交报销申请</div>
                <div>申请人</div>
                <div>{{details.poPaymentApplication.createName | stringSplit(4)}} {{ details.poPaymentApplication.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-for="it in details.approvalProcessList" v-bind:key="it.id">

                <div v-if="it.approveStatus === '2' && it.businessType == 45" class="processItem">
                  <div>在线审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 46" class="processItem">
                  <div>在线审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 47" class="processItem">
                  <div>线下审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 48" class="processItem">
                  <div>采购审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 49" class="processItem">
                  <div>付款审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 50" class="processItem">
                  <div v-if="details.financePayment.method === '1'">现金付讫</div>
                  <div v-else>付款方式确认</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 51" class="processItem">
                  <div>付款复核通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 52" class="processItem">
                  <div v-if="details.financePayment.method === '1'">现金付讫</div>
                  <div v-if="details.financePayment.method === '5'">报销款已转账</div>
                  <div v-if="details.financePayment.method === '4'">报销款已转承兑汇票</div>
                  <div v-if="details.financePayment.method === '3'">报销款已转支票
                    <!--<span v-if="details.invoiceType == '2'"></span>-->
                    <!--<span v-if="details.invoiceType == '3'"></span>-->
                  </div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 53" class="processItem">
                  <div>付款方式修改</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>

              <!-- 最后一条审批记录展示 -->
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===46">
                <div style="margin-top:15px;"> 报销申请被驳回！</div>
                <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ nowApproveItem.reason }}</div></div>
                <br>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 45">
                <div style="margin-top:15px;">票据在线审核未通过！</div>
                <div>驳回理由： {{nowApproveItem.reasonStr }}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 47">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 48">
                <div style="margin-top:15px;"> 报销申请被驳回！</div>
                <div>驳回理由: <span style="white-space: pre-wrap;" v-html="nowApproveItem.reasonStr"></span></div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue">
                <span v-if="nowApproveItem.approveStatus === '1'&& nowApproveItem.businessType===45">等待出纳在线审核。</span>
                <span v-else-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===46">
                请将实际票据交出纳，由出纳进行线下审核。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-else-if="nowApproveItem.approveStatus === '1' && isNowApprover && nowApproveItem.businessType===46">
                请等待报销者提交实际票据。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-else-if="nowApproveItem.approveStatus === '1' && nowApproveItem.userName !== 0 && nowApproveItem.userName !==null">{{ nowApproveItem.userName }}为下一个审批人</span>
                <span v-else-if="details.poPaymentApplication.approveStatus == '2' ">本次报销已完结！</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="orderProgressRade">
        <el-table :data="details.orderProgress" stripe style="width: 100%">
          <el-table-column prop="name" label="阶段"></el-table-column>
          <el-table-column prop="amount" label="金额"></el-table-column>
          <el-table-column prop="rate" label="比例"></el-table-column>
        </el-table>
      </div>
      <div>
        <div v-if="details.poPaymentApplication.type !== '3'">
          <div class="panel-title">
            本次报销共有票据 {{ details.poPaymentApplication.billCount }} 张，合计 {{ details.poPaymentApplication.billAmount && details.poPaymentApplication.billAmount.toFixed(2) }} 元
          </div>
          <div class="panel-content">
            <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
              <el-tab-pane label="按票据查看" name="bill">
                <div class="ui-cells_none">
                  <a class="ui-cell" @click="goBillInfo(item)" v-for="item in details.listMapBillCat" v-bind:key="item.billCat">
                    <div class="ui-cell__bd">
                      <div class="ui-cell_con">
                        <el-row>
                          <el-col :span="12">{{item.invoiceCategory | formatCat}}</el-col>
                          <el-col :span="6">{{item.num}}张</el-col>
                          <el-col :span="6">{{item.totalBillAmount && item.totalBillAmount.toFixed(2)}} 元</el-col>
                        </el-row>
                      </div>
                      <div class="ui-cell__ft"></div>
                    </div>
                  </a>
                </div>
              </el-tab-pane>
              <el-tab-pane label="按费用查看" name="feeCat">
                <a class="ui-cell" @click="goFeeInfo(item)"  v-for="item in details.listMapFeeCat" v-bind:key="item.feeCat">
                  <div class="ui-cell__bd">
                    <div class="ui-cell_con">
                      <el-row>
                        <el-col :span="12">{{item.feeCategory}}</el-col>
                        <el-col :span="6">{{item.totalBillAmount && item.totalBillAmount.toFixed(2)}} 元</el-col>
                      </el-row>
                    </div>
                  </div>
                  <div class="ui-cell__ft"></div>
                </a>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div v-if="details.poPaymentApplication.type != '1'">
          <div style="background: #fff; padding:10px 15px;">
            <span>付款事由：</span><span>{{ details.poPaymentApplication.applicationReason | formReason(details.poPaymentApplication.applicationDesc)}}</span>
          </div>
        </div>
        <div>
          <div class="ui-cells_none">
            <a class="ui-cell" v-if="isRevoke">
              <hr>
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div class="handle_button" style="text-align: center; margin-top:5px; ">
                    <button class="ui-btn ui-btn_info" @click="revokeBtn">撤回</button>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="70%" >
      <div class="text-center">
        <div>撤销后，该条报销将从系统中消失。</div>
        <div>您确定撤销吗？</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value=" 取消" @click="dialogVisible = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="revoke">
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="less">
#purchaseInvoiceDetails{
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .rejectPng{
    position: absolute;
    top: calc( 50% - 195px);
    left: calc( 50% - 74px);
    z-index: 50;
    filter:alpha(opacity=60);
    opacity:0.6;
    -moz-opacity:0.6;
    -khtml-opacity: 0.6
  }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
}
</style>

<script>
import { isSaler, chargeNull } from '../../../js/common'
import rejectPng from '../../../images/reject.png'
export default {
  name: 'purchaseInvoiceDetails',
  data () {
    return {
      isReject: false, // 判断是否驳回来显示驳回图片
      isRevoke: false, // 判断是否显示撤销
      rejectPng: rejectPng, // 驳回图片
      dialogVisible: false,
      loading: true,
      toggle: -1, // 审批记录控制显隐
      listenersUid: [],
      nowApproveItem: {},
      isNowApprover: false,
      invoiceID: '',
      code: '',
      title: '采购部门的票据审核',
      approveStatus: '',
      activeName: 'bill',
      orderProgressRade: false,
      details: {}
    }
  },
  filters: {
    formReason: function (num, applicationDesc) {
      switch (Number(num)) {
        case 1:
          return '符合合同约定'
          break
        case 2:
          return applicationDesc
          break
      }
    },
    formatCat: function (num) {
      switch (Number(num)) {
        case 1:
          return '增值税专用发票'
          break
        case 2:
          return '增值税普通发票'
          break
        case 3:
          return '收据'
        case 4:
          return '其他发票'
          break
      }
    },
    formatType: function (num, amount) {
      switch (Number(num)) {
        case 1:
          return '本次仅提交票据，不付款'
          break
        case 2:
          return `申请付款：${amount.toFixed(2)}元`
          break
        case 3:
          return `申请付款：${amount.toFixed(2)}元`
          break
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    // 35+票据id的是票据没有付款方式的；
    // 36+付款方式id是票据有付款方式的；

    that.code = paramCode.substring(0, 2) // 11-采购部门的票据审核; 12-采购部门的付款; 35-消息查看 票据 ；36 消息查看 预付款
    if (Number(that.code) === 12) {
      that.title = '采购部门的付款'
    }
    that.invoiceID = paramCode.substring(2, paramCode.length)
    this.listenersUid = [
      this.sphdSocket.subscribe('cancel', function (data) {
        that.loading = false
        let getData = JSON.parse(data)
        let state = Number(getData.state)
        if (state === 1) {
          that.$kiko_message('撤销成功')
          that.$router.push({
            path: '/applyReimburse',
            name: 'applyReimburse'
          })
        } else if (state === 2) {
          that.$kiko_message('已有审批人进行审批，不可再进行撤销')
        } else if (state === 0) {
          that.$kiko_message('撤销失败')
        } else {
          that.$kiko_message('错误的返回值')
        }
      })
    ]
    let param = { 'applicationId': that.invoiceID }
    if(that.code === '36'){
      param = { 'financePaymentId': that.invoiceID }
    }
    that.getDetail(param)
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getDetail: function (param) {
      let that = this
      this.axios.post('../../../purchaseInvoice/getPurchaseInvoiceDetail.do', param)
        .then(function (response) {
          let res = response.data.data
          that.details = res
          let poOrders = res.poOrders
          let rate1 = '- -'
          let rate2 = '- -'
          if (that.code == 35 && that.details.poPaymentApplication.type !== '1') {
            that.title = '采购部门的付款'
          }
          poOrders.invoicedAmount = poOrders.invoicedAmount || 0
          poOrders.payedAmount = poOrders.payedAmount || 0
          let rate3 = (poOrders.invoicedAmount / poOrders.amount * 100).toFixed(2)
          let rate4 = (poOrders.payedAmount / poOrders.amount * 100).toFixed(2)
          that.details.orderProgress = [
            { 'name': '检验已合格', 'amount': `${poOrders.checkedAmount ? poOrders.checkedAmount.toFixed(2) : ''}`, 'rate': rate1 },
            { 'name': '已入库', 'amount': `${poOrders.storedAmount ? poOrders.storedAmount.toFixed(2) : ''}`, 'rate': rate2 },
            { 'name': '票据已提交', 'amount': poOrders.invoicedAmount.toFixed(2), 'rate': rate3 + '%' },
            { 'name': '已付款', 'amount': poOrders.payedAmount.toFixed(2), 'rate': rate4 + '%' }
          ]
          that.loading = false
          let ap = res.approvalProcessList || []
          that.nowApproveItem = ap[ap.length - 1]
          let firstap = ap[0]
          if (firstap.approveStatus === '1') {
            that.isRevoke = true
          }
          console.log('firstap.approveStatus === 1', firstap.approveStatus === '1')
          let lastToUser = that.nowApproveItem.toUser
          if (!lastToUser || lastToUser === that.sphdSocket.uuid()) {
            that.isNowApprover = true
          }
          that.approveStatus = that.nowApproveItem.approveStatus
          if (that.approveStatus === '3' || that.approveStatus === '8') {
            that.isReject = true
            that.showReason()
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    showReason: function () {
      let str = ''
      let reasonStr = this.nowApproveItem.reason
      if (String(this.nowApproveItem.approveSelect) === '5') {
        str += '其他原因:\n' + reasonStr
      } else {
        let approveSelectArr = []
        let selectStr = this.nowApproveItem.approveSelect
        if (selectStr && selectStr !== 'null') {
          if (selectStr.length > 2) {
            approveSelectArr = selectStr.split(',')
          } else {
            approveSelectArr = [selectStr]
          }
          for (let i = 0; i < approveSelectArr.length; i++) {
            switch (String(approveSelectArr[i])) {
              case '1':
                str += '照片不清楚，或信息被遮挡，以至于无法审核\n'
                break
              case '2':
                str += '无法入会计帐的票据较多\n'
                break
              case '3':
                str += '包含公司不允许报销的票据\n'
                break
              case '4':
                str += '票据内容与所录入的信息不一致\n'
                break
              default:
            }
          }
        }
      }
      this.nowApproveItem['reasonStr'] = str
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    },
    goBillInfo: function (item) {
      //        let total = Number(item.totalBillAmount) * item.num
      let params = {
        'applicationId': this.$route.params.id,
        'name': 'purchaseInvoice',
        'type': '1',
        'num': item.num,
        'totalAmount': item.totalBillAmount.toFixed(2),
        'billCat': item.invoiceCategory
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFeeInfo: function (item) {
      let params = {
        'applicationId': this.$route.params.id,
        'type': '2',
        'createName': this.details.poOrders.createName,
        'name': 'purchaseInvoice',
        'totalAmount': item.totalBillAmount && item.totalBillAmount.toFixed(2),
        'feeCategory': '采购材料',
        'feeCatName': '采购材料'
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    revokeBtn: function () {
      this.dialogVisible = true
    },
    revoke: function () {
      let that = this
      this.loading = true
      this.dialogVisible = false
      this.axios.post('../../../purchaseInvoice/cancelPurchaseInvoice.do', { 'applicationId': that.invoiceID })
        .then(function (response) {
          let res = response.data.data
          that.$kiko_message(res.content)
          that.loading = false
          if (res.content == '操作成功') {
            this.$router.push({
              path: `/purchaseBillApply`
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  }
}
</script>
