package cn.sphd.miners.modules.material.entity;

import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2019-10-12 
 */

@Entity ( name ="MtInApplication" )
@Table ( name ="t_mt_in_application" )
public class MtInApplication  implements Serializable {

	private static final long serialVersionUID =  4161768232163744495L;

	@Id
   	@Column(name = "id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "org" )
	private Integer org;
	/**
	 * 供应商ID
	 */
   	@Column(name = "supplier" )
	private Integer supplier;

	/**
	 * 库存类型:1-申请/审批,2-库管录入(无需审批)
	 */
	@Column(name = "stock_type" )
	private Integer stockType;
	/**
	 * 入库申请单号
	 */
   	@Column(name = "sn" )
	private String sn;

	/**
	 * 到货时间
	 */
   	@Column(name = "arrive_time" )
	private Date arriveTime;

	/**
	 * 物料品目数量
	 */
   	@Column(name = "item_plan" )
	private Long itemPlan;

	/**
	 * 已处理物料品目数量
	 */
   	@Column(name = "item_handle" )
	private Long itemHandle;

	/**
	 * 合格入库品目数
	 */
   	@Column(name = "item_pass" )
	private Long itemPass;

	/**
	 * 让步入库品目数
	 */
   	@Column(name = "item_compromise" )
	private Long itemCompromise;

	/**
	 * 不合格品目数量
	 */
   	@Column(name = "item_reject" )
	private Long itemReject;

	/**
	 * 计划入库数量
	 */
   	@Column(name = "quantity_plan" )
	private Long quantityPlan;

	/**
	 * 实际入库数量
	 */
   	@Column(name = "quantity_fact" )
	private Long quantityFact;

	/**
	 * 合格入库数量
	 */
   	@Column(name = "quantity_pass" )
	private Long quantityPass;

	/**
	 * 让步入库数量
	 */
   	@Column(name = "quantity_compromise" )
	private Long quantityCompromise;

	/**
	 * 不合格数量
	 */
   	@Column(name = "quantity_reject" )
	private Long quantityReject;

	/**
	 * 状态：0-处理中,1-全部入库,2-部分入库,3-全部拒入
            
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 描述
	 */
   	@Column(name = "description" )
	private String description;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Long updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 质检员ID
	 */
	@Transient
	private Integer inspector;
	/**
	 * 质检员姓名
	 */
	@Transient
	private String inspectorName;


	/**
	 * 申请人id
	 */
	@Transient
	private Integer applicant;
	/**
	 * 申请人姓名
	 */
	@Transient
	private String applicantName;

	//供应商名称
	@Transient
	private String name;



	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getSupplier() {
		return supplier;
	}

	public void setSupplier(Integer supplier) {
		this.supplier = supplier;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public Date getArriveTime() {
		return arriveTime;
	}

	public void setArriveTime(Date arriveTime) {
		this.arriveTime = arriveTime;
	}

	public Long getItemPlan() {
		return itemPlan;
	}

	public void setItemPlan(Long itemPlan) {
		this.itemPlan = itemPlan;
	}

	public Long getItemHandle() {
		return itemHandle;
	}

	public void setItemHandle(Long itemHandle) {
		this.itemHandle = itemHandle;
	}

	public Long getItemPass() {
		return itemPass;
	}

	public void setItemPass(Long itemPass) {
		this.itemPass = itemPass;
	}

	public Long getItemCompromise() {
		return itemCompromise;
	}

	public void setItemCompromise(Long itemCompromise) {
		this.itemCompromise = itemCompromise;
	}

	public Long getItemReject() {
		return itemReject;
	}

	public void setItemReject(Long itemReject) {
		this.itemReject = itemReject;
	}

	public Long getQuantityPlan() {
		return quantityPlan;
	}

	public void setQuantityPlan(Long quantityPlan) {
		this.quantityPlan = quantityPlan;
	}

	public Long getQuantityFact() {
		return quantityFact;
	}

	public void setQuantityFact(Long quantityFact) {
		this.quantityFact = quantityFact;
	}

	public Long getQuantityPass() {
		return quantityPass;
	}

	public void setQuantityPass(Long quantityPass) {
		this.quantityPass = quantityPass;
	}

	public Long getQuantityCompromise() {
		return quantityCompromise;
	}

	public void setQuantityCompromise(Long quantityCompromise) {
		this.quantityCompromise = quantityCompromise;
	}

	public Long getQuantityReject() {
		return quantityReject;
	}

	public void setQuantityReject(Long quantityReject) {
		this.quantityReject = quantityReject;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Long getUpdator() {
		return updator;
	}

	public void setUpdator(Long updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getStockType() {
		return stockType;
	}

	public void setStockType(Integer stockType) {
		this.stockType = stockType;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getInspector() {
		return inspector;
	}

	public void setInspector(Integer inspector) {
		this.inspector = inspector;
	}

	public String getInspectorName() {
		return inspectorName;
	}

	public void setInspectorName(String inspectorName) {
		this.inspectorName = inspectorName;
	}

	public Integer getApplicant() {
		return applicant;
	}

	public void setApplicant(Integer applicant) {
		this.applicant = applicant;
	}

	public String getApplicantName() {
		return applicantName;
	}

	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
