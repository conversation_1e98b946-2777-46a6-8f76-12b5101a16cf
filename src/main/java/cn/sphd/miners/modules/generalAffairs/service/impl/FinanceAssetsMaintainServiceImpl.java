package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.modules.generalAffairs.dao.FinanceAssetsMaintainDao;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsMaintain;
import cn.sphd.miners.modules.generalAffairs.service.FinanceAssetsMaintainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2017/7/11.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceAssetsMaintainServiceImpl implements FinanceAssetsMaintainService {
   @Autowired
   FinanceAssetsMaintainDao financeAssetsMaintainDao;


    @Override
    public void addFinanceAssetsMaintain(FinanceAssetsMaintain financeAssetsMaintain) {
        financeAssetsMaintainDao.save(financeAssetsMaintain);
    }

    @Override
    public List<FinanceAssetsMaintain> getFinanceAssetsMaintainList(Integer aid) {
        String hql="from FinanceAssetsMaintain o where o.assets="+aid+" order by o.createDate desc";
        return financeAssetsMaintainDao.getListByHQL(hql);
    }
}
