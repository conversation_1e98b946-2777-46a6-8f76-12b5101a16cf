package cn.sphd.miners.modules.forumArea.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018-02-1
 * @description
 **/
@Entity
@Table(name = "t_forum_post")
public class ForumPost extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "org")
    private Integer org;
    @Column(name = "category")
    private Integer category;
    @Column(name = "name")
    private String name;
    @Column(name = "title", length = 80)
    private String title;
    @Column(name = "file_sn")
    private String fileSn;
    @Column(name = "content")
    private String content;
    @Column(name = "size")
    private Integer size;
    @Column(name = "keyword")
    private String keyword;
    @Column(name = "valid")
    private Integer valid;
    @Column(name = "valid_time")
    private Date validTime;
    @Column(name = "enabled")
    private Integer enabled;
    @Column(name = "enabled_time")
    private Date enabledTime;
    @Column(name = "is_stick")
    private Integer isStick;
    @Column(name = "is_open")
    private Integer isOpen;
    @Column(name = "participants_num")
    private Integer participantsNum;
    @Column(name = "read_num")
    private Integer readNum;
    @Column(name = "reply_num")
    private Integer replyNum;
    @Column(name = "lastest_reply_time")
    private Date lastestReplyTime;
    @Column(name = "download_num")
    private Integer downloadNum;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_date")
    private Date createDate;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_date")
    private Date updateDate;
    @Column(name = "approve_item")
    private Date approveItem;
    @Column(name = "approve_status")
    private String approveStatus;
    @Column(name = "approve_level")
    private Integer approveLevel;
    @Column(name = "auditor")
    private Integer auditor;
    @Column(name = "auditor_name")
    private String auditorName;
    @Column(name = "audit_date")
    private Date auditDate;
    @Column(name = "operation")
    private String operation;
    @Column(name = "apply_memo")
    private String applyMemo;
    @Column(name = "approve_memo")
    private String approveMemo;
    @Column(name = "message_id")
    private Integer messageId;
    @Column(name = "compere")
    private Integer compere;
    @Column(name = "compere_name")
    private String compereName;
    @Column(name = "previous_id")
    private Integer previousId;
    @Column(name = "version_no")
    private Integer versionNo;
    @Column(name = "is_archived")
    private Integer isArchived;
    @Column(name = "is_cleaned")
    private Integer isCleaned;
    @Column(name = "clean_time")
    private Date cleanTime;
    @Column(name = "open_time")
    private Date openTime;



/* @JsonIgnore
 @OrderBy("id ASC")
 @OneToMany(targetEntity=ForumPostAttachment.class, fetch= FetchType.EAGER, mappedBy="forumPost", cascade= CascadeType.REMOVE)
 private Set<ForumPostAttachment> forumPostAttachmentHashSer = new HashSet<>();
*/
  @JsonIgnore @JSONField(serialize = false)
  @OneToMany(mappedBy = "forumPost", fetch = FetchType.LAZY)
  private Set<ForumPostUser> forumPostUsers = new HashSet<>();

    @Transient
    private String listForumPostAttachment;
    @Transient
    private String listForumPostUser;
    @Transient
    private Integer messageNum;
    @Transient
    private PostUserMussage postUserMussage;

    @Transient
    private Integer atTag;//@标记

    @Transient
    private Integer replyTag;//回复标记

    @Transient
    private ForumReply lastReply; //最后一条消息

    @Transient
    private String expriationTime; //讨论有效期


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFileSn() {
        return fileSn;
    }

    public void setFileSn(String fileSn) {
        this.fileSn = fileSn;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Date getValidTime() {
        return validTime;
    }

    public void setValidTime(Date validTime) {
        this.validTime = validTime;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getIsStick() {
        return isStick;
    }

    public void setIsStick(Integer isStick) {
        this.isStick = isStick;
    }

    public Integer getParticipantsNum() {
        return participantsNum;
    }

    public void setParticipantsNum(Integer participantsNum) {
        this.participantsNum = participantsNum;
    }

    public Integer getReadNum() {
        return readNum;
    }

    public void setReadNum(Integer readNum) {
        this.readNum = readNum;
    }

    public Integer getReplyNum() {
        return replyNum;
    }

    public void setReplyNum(Integer replyNum) {
        this.replyNum = replyNum;
    }

    public Date getLastestReplyTime() {
        return lastestReplyTime;
    }

    public void setLastestReplyTime(Date lastestReplyTime) {
        this.lastestReplyTime = lastestReplyTime;
    }

    public Integer getDownloadNum() {
        return downloadNum;
    }

    public void setDownloadNum(Integer downloadNum) {
        this.downloadNum = downloadNum;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Date approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

   /* public Set<ForumPostAttachment> getForumPostAttachmentHashSer() {
        return forumPostAttachmentHashSer;
    }

    public void setForumPostAttachmentHashSer(Set<ForumPostAttachment> forumPostAttachmentHashSer) {
       this.forumPostAttachmentHashSer = forumPostAttachmentHashSer;
   }*/

    public Set<ForumPostUser> getForumPostUsers() {
        return forumPostUsers;
    }

    public void setForumPostUsers(Set<ForumPostUser> forumPostUsers) {
        this.forumPostUsers = forumPostUsers;
    }

    public String getListForumPostAttachment() {
        return listForumPostAttachment;
    }

    public void setListForumPostAttachment(String listForumPostAttachment) {
        this.listForumPostAttachment = listForumPostAttachment;
    }

    public String getListForumPostUser() {
        return listForumPostUser;
    }

    public void setListForumPostUser(String listForumPostUser) {
        this.listForumPostUser = listForumPostUser;
    }

    public Integer getMessageNum() {
        return messageNum;
    }

    public void setMessageNum(Integer messageNum) {
        this.messageNum = messageNum;
    }

    public PostUserMussage getPostUserMussage() {
        return postUserMussage;
    }

    public void setPostUserMussage(PostUserMussage postUserMussage) {
        this.postUserMussage = postUserMussage;
    }

    public Integer getAtTag() {
        return atTag;
    }

    public void setAtTag(Integer atTag) {
        this.atTag = atTag;
    }

    public Integer getReplyTag() {
        return replyTag;
    }

    public void setReplyTag(Integer replyTag) {
        this.replyTag = replyTag;
    }

    public Integer getCompere() {
        return compere;
    }

    public void setCompere(Integer compere) {
        this.compere = compere;
    }

    public String getCompereName() {
        return compereName;
    }

    public void setCompereName(String compereName) {
        this.compereName = compereName;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public ForumReply getLastReply() {
        return lastReply;
    }

    public void setLastReply(ForumReply lastReply) {
        this.lastReply = lastReply;
    }

    public Integer getIsArchived() {
        return isArchived;
    }

    public void setIsArchived(Integer isArchived) {
        this.isArchived = isArchived;
    }

    public Integer getIsCleaned() {
        return isCleaned;
    }

    public void setIsCleaned(Integer isCleaned) {
        this.isCleaned = isCleaned;
    }

    public Integer getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Integer isOpen) {
        this.isOpen = isOpen;
    }

    public String getExpriationTime() {
        return expriationTime;
    }

    public void setExpriationTime(String expriationTime) {
        this.expriationTime = expriationTime;
    }

    public Date getCleanTime() {
        return cleanTime;
    }

    public void setCleanTime(Date cleanTime) {
        this.cleanTime = cleanTime;
    }

    public Date getOpenTime() {
        return openTime;
    }

    public void setOpenTime(Date openTime) {
        this.openTime = openTime;
    }
}
