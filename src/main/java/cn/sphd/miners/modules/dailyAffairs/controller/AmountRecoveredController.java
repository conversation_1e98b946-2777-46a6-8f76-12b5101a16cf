package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.dailyAffairs.service.AmountRecoveredService;
import cn.sphd.miners.modules.finance.entity.PoLoan;
import cn.sphd.miners.modules.loan.entity.LoanBiz;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 1.231差额处理(1)
 * 李娅星  2022/12/30
 * 1.231差额处理(1)--需收回的款   小窗的操作接口
 */
@Controller
@RequestMapping("/amountRecovered")
public class AmountRecoveredController {

    @Autowired
    AmountRecoveredService amountRecoveredService;

    /**
    *@Description 出纳/采购审批人--需收回的款列表
    *@auther 李娅星
    *@date 2022/12/30
    *@param
    */
    @ResponseBody
    @RequestMapping("/getAmountRecoveredList.do")
    public JsonResult getAmountRecoveredList(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> poLoanHandle = amountRecoveredService.getPoLoanList(user.getOid(),null,null,"1",null,69); //待处理
        map.put("poLoanHandle",poLoanHandle);  //待处理
        return new JsonResult(1,map);
    }

    /**
    *@Description 需收回的款获取详情
    *@auther 李娅星
    *@date 2022/12/30
    *@param
    */
    @ResponseBody
    @RequestMapping("/getPoLoanDetail.do")
    public JsonResult getPoLoanDetail(Integer poLoanId){
        Map<String,Object> map = amountRecoveredService.getPoLoanDetail(poLoanId);
        return new JsonResult(1,map);
    }
    
    /**
    *@Description 需收回的款--审批
    *@auther 李娅星
    *@date 2023/1/4
    *@param oppositeCorp-付款单位(收款单位) receiveAccountDate-到账时间 receiveDate-收到票据日期(收到日期)  expireDate-支票到期日期 originalCorp-原始出具票据单位
    *@param bankName-出具支票/汇票银行(出具支票银行) returnNo-支票号
    */
    @ResponseBody
    @RequestMapping("/amountRecoveredApproval.do")
    public JsonResult amountRecoveredApproval(User user, Integer poLoanId, String method, String money, Integer financeAccountId, String oppositeCorp,
            String receiveAccountDate, String receiveDate, String expireDate, String originalCorp, String bankName, String returnNo){
        Map<String,Object> map = amountRecoveredService.amountRecoveredApproval(user,poLoanId,method,money,financeAccountId,oppositeCorp,receiveAccountDate,receiveDate,expireDate,originalCorp,bankName,returnNo);
        return new JsonResult(1,map);
    }

    /**
    *@Description 需收回的款-已收回列表
    *@auther 李娅星
    *@date 2023/7/19
    *@param business：业务id   businessType:业务类型sale-销售,po_loan-差额借款,payment-支付
    */
    @ResponseBody
    @RequestMapping("/getRecoveredList.do")
    public JsonResult getRecoveredList(User user,Integer business,String businessType){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> recoveredList = amountRecoveredService.getRecoveredList(user.getOid(),businessType,business);
        map.put("recoveredList",recoveredList);
        return new JsonResult(1,map);
    }

    /**
     *@Description 需收回的款-已收回的获取详情
     *@auther 李娅星
     *@date 2023/7/19
     *@param
     */
    @ResponseBody
    @RequestMapping("/getRecoveredDetail.do")
    public JsonResult getRecoveredDetail(Integer recoveredId){
        Map<String,Object> map = amountRecoveredService.getRecoveredDetail(recoveredId);
        return new JsonResult(1,map);
    }
}
