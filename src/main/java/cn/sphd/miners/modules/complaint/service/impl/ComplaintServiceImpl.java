package cn.sphd.miners.modules.complaint.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseDao;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.complaint.dao.*;
import cn.sphd.miners.modules.complaint.dto.ComplaintTH;
import cn.sphd.miners.modules.complaint.dto.ProductMessageByComplaint;
import cn.sphd.miners.modules.complaint.entity.*;
import cn.sphd.miners.modules.complaint.service.ComplaintService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.message.dao.MessageDao;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.sales.dao.PdCustomerDao;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.stock.dao.PdInOutStockDao;
import cn.sphd.miners.modules.stock.model.PdInoutStock;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-01-17 10:15
 * @description
 **/
@Service("complaintService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ComplaintServiceImpl implements ComplaintService {

    @Autowired
    PdCustomerDao pdCustomerDao;
    @Autowired
    ComplaintBaseDao complaintBaseDao;
    @Autowired
    ComplaintAttachmentDao attachmentDao;
    @Autowired
    ComplaintSupplementDao supplementDao;
    @Autowired
    PdCustomerService customerService;
    @Autowired
    UserService userService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    MessageDao messageDao;
    @Autowired
    ComplaintDetailDao complaintDetailDao;
    @Autowired
    ComplaintBaseHistoryDao complaintBaseHistoryDao;
    @Autowired
    ComplaintProductOutputDao complaintProductOutputDao;
    @Autowired
    ComplaintContactDao complaintContactDao;
    @Autowired
    ComplaintContactHistoryDao complaintContactHistoryDao;
    @Autowired
    PdMerchandiseDao pdMerchandiseDao;
    @Autowired
    PdInOutStockDao pdInOutStockDao;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    UploadService uploadService;



    @Override
    public List<ProductMessageByComplaint> listOutPutTime(Integer product) {
        String hql = "from PdInoutStock where product = :product and state = 9";
        HashMap<String, Object> param = new HashMap<>();
        param.put("product", product);
        List<PdInoutStock> list = pdInOutStockDao.getListByHQLWithNamedParams(hql,param);
        List<ProductMessageByComplaint> listProduct = new ArrayList<>();
        if(!list.isEmpty()){
            for (PdInoutStock ps : list) {
                ProductMessageByComplaint p = new ProductMessageByComplaint();
                p.setOutputTime(ps.getSginTime());
                p.setOutputStockSn(ps.getId());
                listProduct.add(p);
            }
        }
        return listProduct;
    }

    @Override
    public void saveComplaint(ComplaintBase complaintBase, ComplaintContact complaintContact, String listComplaintDetail, User user, String module) {
        //新增投诉基本信息
        complaintBaseDao.save(complaintBase);
        //若联系人等信息不是空的，新增一版联系人信息和联系人历史记录
        if (complaintContact.getContactName() != null || complaintContact.getContact() != null ||
                complaintContact.getContactPhone() != null || complaintContact.getPost() != null)
        {
            complaintContact.setComplaintBase(complaintBase.getId());
            complaintContact.setCreateName(user.getUserName());
            complaintContact.setCreator(user.getUserID());
            complaintContact.setCreateTime(complaintBase.getCreateTime());
            complaintContact.setVersionNo(0);
            this.addCustomerContact(complaintContact);
        }
        //新增详细信息
        List<ComplaintDetail> listDatail = JSON.parseArray(listComplaintDetail, ComplaintDetail.class);
        for(ComplaintDetail c : listDatail){
            ComplaintDetail detail = new ComplaintDetail();
            if (!"".equals(c.getProblem()) && c.getProblem() != null) {
                detail.setProblem(c.getProblem());
            }
            if (!"".equals(c.getConsequence()) && c.getConsequence() != null) {
                detail.setConsequence(c.getConsequence());
            }
            if (!"".equals(c.getAppeal()) && c.getAppeal() != null) {
                detail.setAppeal(c.getAppeal());
            }
            if (!"".equals(c.getMemo()) && c.getMemo() != null) {
                detail.setMemo(c.getMemo());
            }
            if (!"".equals(c.getPercent()) && c.getPercent() != null) {
                detail.setPercent(c.getPercent());
            }
            if (!"".equals(c.getAmount()) && c.getAmount() != null) {
                detail.setAmount(c.getAmount());
            }
            detail.setPeroid(complaintBase.getPeroid());
            detail.setCustomerProduct(c.getCustomerProduct());
            detail.setComplaint(complaintBase.getId());
            detail.setCreateName(user.getUserName());
            detail.setCreator(user.getUserID());
            detail.setCreateTime(complaintBase.getCreateTime());
            complaintDetailDao.save(detail);
            String listProductOutput = c.getListProductOutput();
            //新增投诉产品出库
            if (listProductOutput != null){
                List<ComplaintProductOutput> outputList = JSON.parseArray(listProductOutput, ComplaintProductOutput.class);
                for (ComplaintProductOutput cp : outputList) {
                    cp.setComplaint(complaintBase.getId());
                    cp.setComplaintDetail(detail.getId());
                    cp.setCreateName(user.getUserName());
                    cp.setCreator(user.getUserID());
                    cp.setCreateTime(complaintBase.getCreateTime());
                    complaintProductOutputDao.save(cp);
                }
            }
            //新增投诉附件
            String listAttachment = c.getListAttachment();
            this.addComplaintAttachment(listAttachment,detail, user, module);
        }

        this.getSingelBase(complaintBase);
        HashMap<String, Object> map = new HashMap<>();
        map.put("complaint", complaintBase);
        String noticeMes = null;
        if ("1".equals(complaintBase.getState())) {
            User userCentre=userService.getUserByCoreCode(complaintBase.getOrg(),"core");
            this.insertApprovalProcessByComplaintBase(complaintBase.getId(),"1",complaintBase.getCreateTime(),user, null, userCentre, null, null);
            swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/complaintEntry",null,null,user,"complaintInput");  //发给立案者的投诉录入
            noticeMes = this.complaintNoticeMes("1", user.getUserName(), complaintBase.getCustomName());
            swMessageService.rejectSend(1,1,map,userCentre.getUserID().toString(),"/pendingApproveComplaint",noticeMes,noticeMes,userCentre,"complaintHandle");  //发给核心人物的立案待审批
        } else {
            this.insertApprovalProcessByComplaintBase(complaintBase.getId(),"2",complaintBase.getCreateTime(),user, complaintBase.getCreateTime(), user, null, null);
            User userProcesser = userService.getUserByID(complaintBase.getProcessor());
            this.addCompliantProcessor(complaintBase.getProcessor(), complaintBase.getId(),complaintBase.getCreateTime());      //核心人物新增时新增一版处理人历史
            noticeMes = this.complaintNoticeMes("2", null, complaintBase.getCustomName());
            swMessageService.rejectSend(1,1,map,user.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes,user,"complaintHandle");  //发给核心人物的待结案
            swMessageService.rejectSend(1,1,map,userProcesser.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes, userProcesser,"complaintHandle");  //发给处理者的待结案
        }
    }

    @Override
    public HashMap ComplaintEntryList(User user, String time, Integer oid) {
        HashMap<String, Object> map = new HashMap<>();
        String hql = "from ComplaintBase where state = :state";
        HashMap<String, Object> param = new HashMap<>();
        if (time == "") {
            time = NewDateUtils.getYear(new Date()).toString();
        }
        if(time != null){
            param.put("state", "2");
            param.put("timeBegin", NewDateUtils.getNewYearsDay(NewDateUtils.dateFromString(time + "-01-01", "yyyy-MM-dd")));
            param.put("timeEnd", NewDateUtils.getNewYearsDay(NewDateUtils.dateFromString(time + "-12-31", "yyyy-MM-dd")));
            hql = hql + " and createTime between :timeBegin and :timeEnd";
        }else {
            param.put("state", "1");
        }
        if (user != null) {
            hql = hql + " and creator = :creator ";
            param.put("creator", user.getUserID());
        }
        if (oid != null) {
            hql = hql + " and org = :org ";
            param.put("org", oid);
        }
        hql = hql + " and terminateState = 0 order by createTime desc";
        List<ComplaintBase> list = complaintBaseDao.getListByHQLWithNamedParams(hql, param);
        if(!list.isEmpty()){
            for (ComplaintBase c: list) {
                this.getSingelBase(c);
            }
        }
        map.put("listComplaint", list);
        if(time != null){
            map.put("timeBegin", time + "-01-01");
            if(time.equals(NewDateUtils.getYear(new Date()).toString())){
                map.put("timeEnd", NewDateUtils.dateToString(new Date(), "yyyy-MM-dd"));
            } else {
                map.put("timeEnd", time + "-12-31");
            }
            map.put("num", list.size());
        }
        return map;
    }

    @Override
    public List<ComplaintBase> getComplaintByStateAndRole(User user, String state, String role) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("state", state);
        String hql = null;
        if("1".equals(role)){
            hql = "from ComplaintBase where state = :state and org = :org order by createTime desc";
            param.put("org", user.getOid());
        } else {
            param.put("creator", user.getUserID());
            param.put("processor", user.getUserID());
            hql = "from ComplaintBase where state = :state and ( (creator = :creator and terminateState = 0)  or processor = :processor )  order by createTime desc";
        }
        List<ComplaintBase> list = complaintBaseDao.getListByHQLWithNamedParams(hql,param);
        if(!list.isEmpty()){
            for (ComplaintBase c: list) {
                this.getSingelBase(c);
            }
        }
        return list;
    }

    @Override
    public HashMap listCompleteBase(User user, String role, String time, String settleType, Integer customerId) {
        String hql = "from ComplaintBase where state = 6";
        HashMap<String, Object> param = new HashMap<>();
        if ("1".equals(role)) {
            hql = hql + " and org = :org";
            param.put("org", user.getOid());
        } else {
            hql = hql + " and ( creator = :creator or processor = :processor )";
            param.put("creator",user.getUserID());
            param.put("processor", user.getUserID());
        }
        if (settleType != null && settleType != "") {
            if ("2".equals(settleType)) {
                hql = hql + " and ( settleType = :settleType1 or settleType = :settleType2 )";
                param.put("settleType1", "2");
                param.put("settleType2", "3");
            } else {
                hql = hql + " and settleType = :settleType";
                param.put("settleType", settleType);
            }
        }
        if (customerId != null) {
            hql = hql + " and customer = :customer";
            param.put("customer", customerId);
        }
        hql = hql + " and createTime between :timeBegin and :timeEnd order by createTime desc";
        if (time == "") {
            time = NewDateUtils.getYear(new Date()).toString();
        }

        param.put("timeBegin", NewDateUtils.getNewYearsDay(NewDateUtils.dateFromString(time + "-01-01", "yyyy-MM-dd")));
        param.put("timeEnd", NewDateUtils.getLastTimeOfYear((Date) param.get("timeBegin")));
        List<ComplaintBase> list = complaintBaseDao.getListByHQLWithNamedParams(hql,param);
        if(!list.isEmpty()){
            for (ComplaintBase c: list) {
                this.getSingelBase(c);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("listComplaint", list);
        map.put("timeBegin", time + "-01-01");
        if(time.equals((NewDateUtils.getYear(new Date()).toString()))){
            map.put("timeEnd", NewDateUtils.dateToString(new Date(), "yyyy-MM-dd"));
        } else {
            map.put("timeEnd", time + "-12-31");
        }
        map.put("num", list.size());
        return map;
    }

    @Override
    public HashMap listOverviewCompleteBase(Integer oid, String type, Integer customer, String year, String month, PageInfo pageInfo, String state, String settleType) {
        String hql = "from ComplaintBase where org = :oid";
        HashMap<String, Object> param = new HashMap<>();
        HashMap<String, Object> map = new HashMap<>();
        param.put("oid", oid);
        if (state == null) {
            hql = hql + " and (state = 1 or state = 3 or state = 4)";
        } else {
            hql = hql + " and state = :state";
            param.put("state", state);
        }
        if (settleType != "") {
            hql = hql + " and settleType = :settleType";
            param.put("settleType", settleType);
            map.put("settleType", settleType);
        }
        if (customer != null) {
            hql = hql + " and customer = :customer";
            param.put("customer", customer);
        }
        if (year != "") {
            String time = year;
            if (month != "") {
                time = year + "-" + month + "-01";
                map.put("month", month);
            }else {
                time = year + "-01-01";
            }
            Date dateBegin = NewDateUtils.dateFromString(time,"yyyy-MM-dd");
            Date dateEnd = NewDateUtils.getLastTimeOfYear(dateBegin);
            param.put("dateBegin",dateBegin);
            param.put("dateEnd",dateEnd);
            if ("1".equals(type)) {
                hql = hql + " and createTime between :dateBegin and :dateEnd order by createTime desc";
            }else if ("2".equals(type)) {
                hql = hql + " and settleApproveTime between :dateBegin and :dateEnd order by settleApproveTime desc";
            } else {
                hql = hql + " and (receiptTime between :dateBegin and :dateEnd or receiptTime is null) order by receiptTime desc";
            }
            map.put("year", year);
        } else {
            if ("1".equals(type)) {
                hql = hql + " order by createTime desc";
            }else if ("2".equals(type)) {
                hql = hql + " order by settleApproveTime desc";
            } else {
                hql = hql + " order by receiptTime desc, createTime desc";
            }
        }
        List<ComplaintBase> list = complaintBaseDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        if(!list.isEmpty()){
            for (ComplaintBase c: list) {
                this.getSingelBase(c);
            }
            if (customer != null) {
                map.put("customerAll", list.get(0).getCustomName()+list.get(0).getCustomCode());
            }
        }
        Date date = new Date();
        map.put("date", date);
        map.put("type", type);
        map.put("overviewCompleteBase", list);
        map.put("pageInfo", pageInfo);
        return map;
    }

    @Override
    public List<ComplaintBase> overviewComplaintByCustomerAndState(User user, String state, Integer customer) {
        String hql = "from ComplaintBase where state = :state and org = :org and customer = :customer order by createTime desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("state", state);
        param.put("org", user.getOid());
        param.put("customer", customer);
        List<ComplaintBase> list = complaintBaseDao.getListByHQLWithNamedParams(hql,param);
        if(!list.isEmpty()){
            for (ComplaintBase c: list) {
                this.getSingelBase(c);
            }
        }
        return list;
    }

    @Override
    public HashMap<String, Object> getAllComplaintByOid(User user, Integer peroid, PageInfo pageInfo) {
        String hql = "from ComplaintBase where org = :org and peroid = :peroid order by receiptTime desc, createTime desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("peroid", peroid);
        List<ComplaintBase> list = complaintBaseDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("pageInfo", pageInfo);
        return map;
    }

    @Override
    public ComplaintTH macroComplaintByCustomer(User user, String time, Integer customer, String type) {
        Date date = NewDateUtils.dateFromString(time,"yyyy-MM-dd HH:mm:ss");
        Integer peroidYear = NewDateUtils.getYear(date);
        Integer peroidMonth = NewDateUtils.getMonth(date);
        String hql = "select count(id) from ComplaintBase where org = :org and state != 2";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        if (customer != null) {
            hql = hql + " and customer = :customer";
            param.put("customer", customer);
        }
        Long num = Long.valueOf("0");
        Long numLast = Long.valueOf("0");
        Long numSame = Long.valueOf("0");
        String nowHql;
        String lastHql;
        HashMap<String, Object> map = new HashMap<>();
        if ("1".equals(type)) {
            String peroidBegin = peroidYear.toString()+ "01";
            String peroidEnd = peroidYear.toString() + "12";
            Integer lastPeroid = peroidYear - 1;
            String lastPeroidBegin = lastPeroid.toString() + "01";
            String lastPeroidEnd = lastPeroid.toString() + "12";
            param.put("peroidBegin", Integer.valueOf(peroidBegin));
            param.put("peroidEnd", Integer.valueOf(peroidEnd));
            nowHql = hql + " and peroid >= :peroidBegin and peroid <= :peroidEnd";
            num  = (Long) complaintBaseDao.getByHQLWithNamedParams(nowHql,param);

            HashMap<String, Object> paramLast = new HashMap<>();
            paramLast.put("org", user.getOid());
            paramLast.put("lastPeroidBegin", Integer.valueOf(lastPeroidBegin));
            paramLast.put("lastPeroidEnd", Integer.valueOf(lastPeroidEnd));
            if (customer != null) {
                paramLast.put("customer", customer);
            }
            lastHql = hql + " and peroid >= :lastPeroidBegin and peroid <= :lastPeroidEnd";
            numLast = (Long) complaintBaseDao.getByHQLWithNamedParams(lastHql,paramLast);
        } else {
            DecimalFormat df = new DecimalFormat("00");
            String peroidMonthString = df.format(peroidMonth);
            String peroidYearString = peroidYear.toString();
            String peroid = peroidYearString+peroidMonthString;
            String lastPeroidMonthString;
            String lastPeroidYearString = df.format(peroidYear - 1);;
            String lastPeroid;
            if(peroidMonth.equals(1)){
                lastPeroidMonthString = "12";
                lastPeroid = lastPeroidYearString+lastPeroidMonthString;
            }else {
                lastPeroidMonthString = df.format(peroidMonth - 1);
                lastPeroid = peroidYearString+ lastPeroidMonthString;
            }
            String sameTimePeroid = lastPeroidYearString+peroidMonthString;

            param.put("peroid", Integer.valueOf(peroid));
            nowHql = hql + " and peroid = :peroid";
            num  = (Long) complaintBaseDao.getByHQLWithNamedParams(nowHql,param);

            lastHql = hql + " and peroid = :lastPeroid";
            HashMap<String, Object> paramLast = new HashMap<>();
            paramLast.put("org", user.getOid());
            paramLast.put("lastPeroid",Integer.valueOf(lastPeroid));
            if (customer != null) {
                paramLast.put("customer", customer);
            }
            numLast = (Long) complaintBaseDao.getByHQLWithNamedParams(lastHql,paramLast);

            String samtHql = hql + " and peroid = :sameTimePeroid";
            HashMap<String, Object> paramSame = new HashMap<>();
            paramSame.put("org", user.getOid());
            paramSame.put("sameTimePeroid", Integer.valueOf(sameTimePeroid));
            if (customer != null) {
                paramSame.put("customer", customer);
            }
            numSame = (Long) complaintBaseDao.getByHQLWithNamedParams(samtHql,paramSame);
        }

        BigDecimal numBig = new BigDecimal(String.valueOf(num));
        BigDecimal numLastBig = new BigDecimal(String.valueOf(numLast));

        BigDecimal tongbi;
        ComplaintTH complaintTH = new ComplaintTH();
        if (numLastBig.compareTo(new BigDecimal(0)) != 0) {
            tongbi = (numBig.subtract(numLastBig)).divide(numLastBig,2,RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            complaintTH.setTongbiComplaint(tongbi.toString());
        }else {
            complaintTH.setTongbiComplaint("--");
        }

        if ("2".equals(type)) {
            BigDecimal numSameBig = new BigDecimal(String.valueOf(numSame));
            BigDecimal huanbi;
            if (numSameBig.compareTo(new BigDecimal(0)) != 0) {
                huanbi = (numBig.subtract(numSameBig)).divide(numSameBig,2,RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                complaintTH.setHuanbiComplaint(huanbi.toString());
            }else {
                complaintTH.setHuanbiComplaint("--");
            }
        }else {
            complaintTH.setHuanbiComplaint("--");
        }
        complaintTH.setNumComplaint(new BigDecimal(num));
        return complaintTH;
    }


    @Override
    public void userCentreApproveComplaint(User user, ComplaintBase complaintBase, Integer processId) {
        ComplaintBase complaint = complaintBaseDao.get(complaintBase.getId());
        complaint.setState(complaintBase.getState());
        complaint.setRegisterApprover(user.getUserID());
        complaint.setRegisterApproveTime(new Date());
        HashMap<String, Object> map = new HashMap<>();
        User userCreator = userService.getUserByID(complaint.getCreator());
        if("3".equals(complaintBase.getState())){
            complaint.setProcessor(complaintBase.getProcessor());
            complaintBaseDao.saveOrUpdate(complaint);
            this.updateApprovalProcessByComplaintBase(processId, "2" , complaint.getRegisterApproveTime(),null);
            this.getSingelBase(complaint);
            map.put("complaint", complaint);
            this.addCompliantProcessor(complaint.getProcessor(), complaint.getId(),complaint.getRegisterApproveTime());      //核心人物批准时新增一版处理人历史
            swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/complaintEntry",null,null,userCreator,"complaintInput");  //发给立案者的投诉录入-1
            swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/pendingApproveComplaint",null,null,user,"complaintHandle");  //发给核心人物的立案待审批-1
            User userProcesser = userService.getUserByID(complaintBase.getProcessor());
            String noticeMes = this.complaintNoticeMes("2", null, complaint.getCustomName());
            swMessageService.rejectSend(1,1,map,user.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes,user,"complaintHandle");  //发给核心人物的待结案+1
            swMessageService.rejectSend(1,1,map,userProcesser.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes, userProcesser,"complaintHandle");  //发给处理者的待结案+1
            if (!complaint.getCreator().equals(complaint.getProcessor())) {
                if ("0".equals(complaint.getTerminateState())) {
                    swMessageService.rejectSend(1,1,map,userCreator.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes, userCreator,"complaintHandle");  //发给立案者的待结案+1
                }
            }
            this.sendComplaintMessage(complaint.getId(), "1");   //给查看当前投诉详情的人推送消息，提示立案通过了
        } else {
            complaint.setRegisterRejectReason(complaintBase.getRegisterRejectReason());
            complaintBaseDao.saveOrUpdate(complaint);
            this.getSingelBase(complaint);
            map.put("complaint", complaint);
            this.updateApprovalProcessByComplaintBase(processId, "3" , complaint.getRegisterApproveTime(),complaint.getRegisterRejectReason());
            swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/complaintEntry",null,null,userCreator,"complaintInput");  //发给立案者的投诉录入-1
            swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/pendingApproveComplaint",null,null,user,"complaintHandle");  //发给核心人物的立案待审批-1
            this.sendComplaintMessage(complaint.getId(), "2");   //给查看当前投诉详情的人推送消息，提示立案驳回了
            userSuspendMsgService.saveUserSuspendMsg(1, "您提交的投诉立案申请被驳回了！", "驳回时间 "+ NewDateUtils.dateToString(complaint.getRegisterApproveTime(), "yyyy-MM-dd HH:mm:ss"), userCreator.getUserID(), "complaintInfo",complaint.getId());
        }
    }

    @Override
    public void applyFinishComplaintByRole(User user, ComplaintBase complaintBase,String role,Integer processId) {
        ComplaintBase complaint = complaintBaseDao.get(complaintBase.getId());
        complaint.setSettleType(complaintBase.getSettleType());
        complaint.setSettleOpinion(complaintBase.getSettleOpinion());
        complaint.setSettler(user.getUserID());
        complaint.setSettleTime(new Date());
        HashMap<String, Object> map = new HashMap<>();
        String noticeMes = null;
        if("1".equals(role)){
            complaint.setState("6");       //核心人物直接结案状态为6
            complaint.setSettleApprover(user.getUserID());
            complaint.setSettleApproveTime(complaint.getSettleTime());
            complaintBaseDao.saveOrUpdate(complaint);
            this.getSingelBase(complaint);
            map.put("complaint",complaint);
            this.insertApprovalProcessByComplaintBase(complaint.getId(), "2", complaint.getSettleTime(), user, complaint.getSettleTime(), user, complaint.getSettleType(), complaint.getSettleOpinion());
            swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/pendingComplaintEnd",null,null,user,"complaintHandle"); //发给核心人物的待结案-1
            User userProcesser = userService.getUserByID(complaint.getProcessor());
            swMessageService.rejectSend(-1,-1,map,userProcesser.getUserID().toString(),"/pendingComplaintEnd",null,null,userProcesser,"complaintHandle"); //发给处理者的待结案-1
            if(!complaint.getCreator().equals(user.getUserID())){
                if ("0".equals(complaint.getTerminateState())) {
                    User userCreator = userService.getUserByID(complaint.getCreator());
                    swMessageService.rejectSend(-1,-1,map,userCreator.getUserID().toString(),"/pendingComplaintEnd",null,null,userCreator,"complaintHandle"); //发给立案者的待结案-1
                }
            }
            this.sendComplaintMessage(complaint.getId(), "4"); //给查看当前投诉详情的人推送消息，提示结案完成。
        } else {
            complaint.setState("4");     //立案者或处理者申请结案状态为4
            complaintBaseDao.saveOrUpdate(complaint);
            this.getSingelBase(complaint);
            map.put("complaint",complaint);
            User userCentre=userService.getUserByCoreCode(complaint.getOrg(),"core");
            this.insertApprovalProcessByComplaintBase(complaint.getId(),"1", complaint.getSettleTime(),user,null,userCentre, complaint.getSettleType(), complaint.getSettleOpinion());
            swMessageService.rejectSend(-1,-1,map,userCentre.getUserID().toString(),"/pendingComplaintEnd",null,null,userCentre,"complaintHandle"); //发给核心人物的待结案
            noticeMes = this.complaintNoticeMes("3", user.getUserName(), complaint.getCustomName());
            swMessageService.rejectSend(1,1,map,userCentre.getUserID().toString(),"/pendingApproveComplaintEnd",noticeMes,noticeMes,userCentre,"complaintHandle"); //发给核心人物的结案待审批
            if (user.getUserID().equals(complaint.getProcessor())) {
                swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/pendingComplaintEnd",null,null,user,"complaintHandle"); //发给登录人的待结案-1
                swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,user,"complaintHandle"); //发给登录人的结案待审批+1
                if ("0".equals(complaint.getTerminateState())) {
                    if(!complaint.getCreator().equals(userCentre.getUserID())){
                        User userCreator = userService.getUserByID(complaint.getCreator());
                        swMessageService.rejectSend(-1,-1,map,userCreator.getUserID().toString(),"/pendingComplaintEnd",null,null,userCreator,"complaintHandle"); //发给立案者的待结案-1
                        swMessageService.rejectSend(0,1,map,userCreator.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,userCreator,"complaintHandle"); //发给立案者的结案待审批+1
                    }
                }
            } else {
                User userProcesser = userService.getUserByID(complaint.getProcessor());
                swMessageService.rejectSend(-1,-1,map,userProcesser.getUserID().toString(),"/pendingComplaintEnd",null,null,userProcesser,"complaintHandle"); //发给处理者的待结案-1
                swMessageService.rejectSend(0,1,map,userProcesser.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,userProcesser,"complaintHandle"); //发给处理者的结案待审批+1
                if ("0".equals(complaint.getTerminateState())) {
                    if(!complaint.getCreator().equals(userCentre.getUserID())){
                        swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/pendingComplaintEnd",null,null,user,"complaintHandle"); //发给登录人的待结案-1
                        swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,user,"complaintHandle"); //发给登录人的结案待审批+1
                    }
                }
            }
            this.sendComplaintMessage(complaint.getId(), "3"); //给查看当前投诉详情的人推送消息，提示投诉状态变为待结案。
        }
    }

    @Override
    public void userCentreFinishComplaint(User user, ComplaintBase complaintBase, Integer processId) {
        ComplaintBase complaint = complaintBaseDao.get(complaintBase.getId());
        if ("5".equals(complaintBase.getState())) {
            complaint.setState("3");
        } else {
            complaint.setState(complaintBase.getState());
        }
        complaint.setSettleApprover(user.getUserID());
        complaint.setSettleApproveTime(new Date());
//        complaint.setSettleType(complaintBase.getSettleType());
        complaint.setSettleRejectReason(complaintBase.getSettleRejectReason());
        complaintBaseDao.saveOrUpdate(complaint);
        HashMap<String, Object> map = new HashMap<>();
        this.getSingelBase(complaint);
        map.put("complaint",complaint);
        // 核心人物驳回/批准
        swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,user,"complaintHandle"); //发给核心人物的结案待审批-1
        User userProcesser = userService.getUserByID(complaint.getProcessor());
        swMessageService.rejectSend(0,-1,map,userProcesser.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,userProcesser,"complaintHandle"); //发给处理者的结案待审批-1
        String noticeMes = null;
        if(!complaint.getCreator().equals(user.getUserID())){
            if ("0".equals(complaint.getTerminateState())) {
                User userCreator=userService.getUserByID(complaint.getCreator());
                swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,userCreator,"complaintHandle"); //发给立案者的结案待审批-1
                if ("5".equals(complaintBase.getState())) {
                    noticeMes = this.complaintNoticeMes("2", null, complaint.getCustomName());
                    swMessageService.rejectSend(1,1,map,userCreator.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes,userCreator,"complaintHandle"); //发给立案者的待结案+1
                }
            }
        }
        if("6".equals(complaintBase.getState())){
            ApprovalProcess ap = this.updateApprovalProcessByComplaintBase(processId,"2",complaint.getSettleApproveTime(),null);
            this.sendComplaintMessage(complaint.getId(), "4"); //给查看当前投诉详情的人推送消息，提示结案完成。
            userSuspendMsgService.saveUserSuspendMsg(1, "您提交的投诉结案报告已审批通过！", "批准时间 " + NewDateUtils.dateToString(complaint.getSettleApproveTime(), "yyyy-MM-dd HH:mm:ss"), ap.getFromUser(), "complaintInfo", complaint.getId());
        } else {
            this.updateApprovalProcessByComplaintBase(processId,"3",complaint.getSettleApproveTime(),complaint.getSettleRejectReason());
            noticeMes = this.complaintNoticeMes("2", null, complaint.getCustomName());
            swMessageService.rejectSend(1,1,map,user.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes,user,"complaintHandle"); //发给核心人物的待结案+1
            swMessageService.rejectSend(1,1,map,userProcesser.getUserID().toString(),"/pendingComplaintEnd",noticeMes,noticeMes,userProcesser,"complaintHandle"); //发给处理者的待结案+1
            this.sendComplaintMessage(complaint.getId(), "5"); //给查看当前投诉详情的人推送消息，提示结案驳回。
        }
    }

    @Override
    public void userCentreChangeHandler(ComplaintBase complaintBase) {
        ComplaintBase complaint = complaintBaseDao.get(complaintBase.getId());
        Integer oldProcessor = complaint.getProcessor();
        complaint.setProcessor(complaintBase.getProcessor());
        complaintBaseDao.saveOrUpdate(complaint);
        this.addCompliantProcessor(complaintBase.getProcessor(), complaintBase.getId(),new Date());
        HashMap<String, Object> map = new HashMap<>();
        this.getSingelBase(complaint);
        map.put("complaint",complaint);
        User newProcessor = userService.getUserByID(complaint.getProcessor());
        if("3".equals(complaint.getState())){
            if(!oldProcessor.equals(complaint.getCreator())) {
                User oldProssor = userService.getUserByID(oldProcessor);
                swMessageService.rejectSend(-1,-1,map,oldProcessor.toString(),"/pendingComplaintEnd",null,null,oldProssor,"complaintHandle");   //让老的处理者待结案-1
            }
            String noticeMes = this.complaintNoticeMes("2", null, complaint.getCustomName());
            swMessageService.rejectSend(1,1,map,complaint.getProcessor().toString(),"/pendingComplaintEnd",noticeMes,noticeMes,newProcessor,"complaintHandle");  //让新的处理者待结案+1
        }else if ("4".equals(complaint.getState())) {
            if(!complaint.getCreator().equals(oldProcessor)){
                User oldProssor = userService.getUserByID(oldProcessor);
                swMessageService.rejectSend(0,-1,map,oldProcessor.toString(),"/pendingApproveComplaintEnd",null,null,oldProssor,"complaintHandle"); //让老处理者的结案待审批-1
            }
            swMessageService.rejectSend(0,1,map,complaint.getProcessor().toString(),"/pendingApproveComplaintEnd",null,null,newProcessor,"complaintHandle"); //让新处理者的结案待审批+1
        }
        this.sendComplaintMessage(complaint.getId(), "6");//给查看当前投诉详情的人推送消息，提示跟换了处理者。
    }

    @Override
    public void userCentreStoppedPermission(Integer userID, Integer baseId) {
        ComplaintBase complaint = complaintBaseDao.get(baseId);
        complaint.setTerminateState("1");
        complaintBaseDao.saveOrUpdate(complaint);
        HashMap<String, Object> map = new HashMap<>();
        map.put("complaint",complaint);
        if("1".equals(complaint.getState())){
            User userCreator = userService.getUserByID(complaint.getCreator());
            swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/complaintEntry",null,null,userCreator,"complaintInput");  //发给立案者的投诉录入-1
        }else if ("3".equals(complaint.getState())) {
            if(!complaint.getCreator().equals(complaint.getProcessor())){
                User userCreator = userService.getUserByID(complaint.getCreator());
                swMessageService.rejectSend(-1,-1,map,userCreator.getUserID().toString(),"/pendingComplaintEnd",null,null,userCreator,"complaintHandle"); //发给立案者的待结案-1
            }
        }else if ("4".equals(complaint.getState())){
            if(!complaint.getCreator().equals(complaint.getProcessor())){
                User userCreator = userService.getUserByID(complaint.getCreator());
                swMessageService.rejectSend(0,-1,map,userCreator.getUserID().toString(),"/pendingApproveComplaintEnd",null,null,userCreator,"complaintHandle"); //发给立案者的待结案-1
            }
        }
        HashMap<String, Object> mapMessage = this.getComplaintBase(complaint.getId());
        mapMessage.put("messageType", "7");
        clusterMessageSendingOperations.convertAndSendToUser(userID.toString(),"/complaintMessage",null,null,null,null,JSON.toJSONString(mapMessage)); //停权时单独给核心人物推送投诉详情
    }

    @Override
    public List<ProductMessageByComplaint> listConplaintBaseHistory(Integer baseId) {
        String hql = "from ComplaintBaseHistory where complaint = "+ baseId;
        List<ComplaintBaseHistory>  list = complaintBaseHistoryDao.getListByHQLWithNamedParams(hql,null);
        List<ProductMessageByComplaint> listProcessor = new ArrayList<>();
        if (!list.isEmpty()) {
            for (ComplaintBaseHistory c : list) {
                ProductMessageByComplaint p = new ProductMessageByComplaint();
                User user = userService.getUserByID(c.getProcessor());
                p.setUserID(user.getUserID());
                p.setUserName(user.getUserName());
                p.setPostName(user.getPostName());
                p.setMobile(user.getMobile());
                p.setChangeTime(c.getCreateTime());
                p.setVersionNo(c.getVersionNo());
                listProcessor.add(p);
            }
        }
        return listProcessor;
    }

    @Override
    public List<ComplaintContact> listComplaintContact(Integer baseId) {
        String hql = "from ComplaintContact where complaintBase = " +baseId ;
        List<ComplaintContact> list = complaintContactDao.getListByHQLWithNamedParams(hql,null);
        return list;
    }

    @Override
    public ComplaintContact addCustomerContact(ComplaintContact complaintContact) {
        //存联系人信息
        complaintContactDao.save(complaintContact);
        this.addComplaintContactHistory(complaintContact);
        return complaintContact;
    }

    @Override
    public ComplaintContact upContact(User user, ComplaintContact complaintContact) {
        ComplaintContact contact = complaintContactDao.get(complaintContact.getId());
        /*if (!("".equals(complaintContact.getContactName())) & complaintContact.getContactName() != null) {
            contact.setContactName(complaintContact.getContactName());
        }
        if (!("".equals(complaintContact.getContact())) & complaintContact.getContact() != null) {
            contact.setContact(complaintContact.getContact());
        }
        if (!("".equals(complaintContact.getContactPhone())) & complaintContact.getContactPhone() != null) {
            contact.setContactPhone(complaintContact.getContactPhone());
        }
        if (!("".equals(complaintContact.getPost())) & complaintContact.getPost() != null) {
            contact.setPost(complaintContact.getPost());
        }*/

        contact.setContactName(complaintContact.getContactName());
        contact.setContact(complaintContact.getContact());
        contact.setContactPhone(complaintContact.getContactPhone());
        contact.setPost(complaintContact.getPost());

        contact.setUpdator(user.getUserID());
        contact.setUpdateName(user.getUserName());
        contact.setUpdateTime(new Date());
        contact.setVersionNo(contact.getVersionNo() + 1);
        complaintContactDao.saveOrUpdate(contact);
        this.addComplaintContactHistory(contact);
        return contact;
    }

    @Override
    public List<ComplaintContactHistory> listContactHistory(Integer contactId) {
        String hql = "from ComplaintContactHistory where complaintContact = " +contactId;
        List<ComplaintContactHistory> list = complaintBaseHistoryDao.getListByHQLWithNamedParams(hql,null);
        return list;
    }

    @Override
    public ComplaintSupplement addSupplement(ComplaintSupplement complaintSupplement, User user) {
        complaintSupplement.setCreator(user.getUserID());
        complaintSupplement.setCreateName(user.getUserName());
        complaintSupplement.setCreateTime(new Date());
        supplementDao.save(complaintSupplement);
        return complaintSupplement;
    }

    @Override
    public HashMap getComplaintBase(Integer baseId) {
        ComplaintBase complaintBase = complaintBaseDao.get(baseId);
        this.getSingelBase(complaintBase);
        String hql = "from ComplaintDetail where complaint = :complaint";
        HashMap<String, Object> param = new HashMap<>();
        param.put("complaint", complaintBase.getId());
        List<ComplaintDetail> listDetail = complaintDetailDao.getListByHQLWithNamedParams(hql,param);

        if (!listDetail.isEmpty()) {
            for (ComplaintDetail cd : listDetail) {
                if("1".equals(complaintBase.getType())) {
                    PdMerchandise product = pdMerchandiseDao.get(cd.getCustomerProduct());
                    cd.setInnerName(product.getProduct().getName());
                    cd.setInnerSn(product.getProduct().getInnerSn());
                    cd.setOuterName(product.getOuterName());
                    cd.setOutersn(product.getOuterSn());
                }
                /*if(!cd.getDetailComplaintAttachmentHashSer().isEmpty()){
                    List<ComplaintAttachment> listAtt = new ArrayList<>();
                    for (ComplaintAttachment ca : cd.getDetailComplaintAttachmentHashSer()) {
                        listAtt.add(ca);
                    }
                    cd.setListAtt(listAtt);
                }
                if(!cd.getDetailComplaintProductOutputHashSer().isEmpty()){
                    List<ComplaintProductOutput> listOutPut = new ArrayList<>();
                    for (ComplaintProductOutput cp : cd.getDetailComplaintProductOutputHashSer()) {
                        listOutPut.add(cp);
                    }
                    cd.setListOutPut(listOutPut);
                }*/
            }
        }
        String hqlSup = "from ComplaintSupplement where complaint = " + complaintBase.getId();
        List<ComplaintSupplement> listSupplement = supplementDao.getListByHQLWithNamedParams(hqlSup,null);
        /*if (!listSupplement.isEmpty()) {
            List<ComplaintAttachment> listAttSup = new ArrayList<>();
            for(ComplaintSupplement cs : listSupplement){
                for (ComplaintAttachment c : cs.getComplaintAttachmentHashSet()) {
                    listAttSup.add(c);
                }
                cs.setListSupAtt(listAttSup);
            }
        }*/
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusiness(baseId, 5, "");
        HashMap<String, Object> map = new HashMap<>();
        map.put("complaint", complaintBase);
        map.put("listApprovalProcess", list);
        map.put("listDetail",listDetail);
        map.put("listSupplement", listSupplement);
        return map;
    }


    @Override
    public ComplaintBase getSingelBase(ComplaintBase complaintBase) {
        if (complaintBase.getCustomer() != null){
//            SlCustomer customer = complaintBase.getSlCustomer();
            SlCustomer customer = customerService.getPdCustomerById(complaintBase.getCustomer());
            if (customer != null)
            {
                complaintBase.setCustomName(customer.getName());
                complaintBase.setCustomCode(customer.getCode());
            }
        }
        String hql = "select userName from User where userID=:userID";
        Map<String,Object> params = new HashMap<>();
        if (complaintBase.getProcessor() != null){
            Integer processor = complaintBase.getProcessor();
            params.put("userID",processor);
            String processorUser = (String) complaintBaseDao.getByHQLWithNamedParams(hql,params);
//            User processorUser = complaintBase.getProcessorUser();
            if (processorUser != null) {
                complaintBase.setProcessorName(processorUser);
            }
        }

        if (complaintBase.getSettler() != null)
        {
            params.put("userID",complaintBase.getSettler());
            String settleUser = (String) complaintBaseDao.getByHQLWithNamedParams(hql,params);
//            User settleUser = complaintBase.getSettlerUser();
            if (settleUser != null)
            {
                complaintBase.setSettlerName(settleUser);
            }
        }

        return complaintBase;
    }

    @Override
    public Integer getComplaintNum(Integer userId, Integer oid) {
        User userCentre=userService.getUserByCoreCode(oid,"core");
        String hql = "select count(id) from ComplaintBase where ";
        HashMap<String, Object> param = new HashMap<>();
        Long num = Long.valueOf("0");
        if (userCentre != null) {
            if(userCentre.getUserID().equals(userId) || userId == null){
                param.put("org", oid);
                hql = hql + " ( state = 1 or state = 3 or state = 4) and org = :org";
            } else {
                param.put("creator", userId);
                param.put("processor", userId);
                hql = hql + " state = 3 and ( creator = :creator or processor = :processor ) and terminateState = 0";
            }
            num = (Long) complaintBaseDao.getByHQLWithNamedParams(hql,param);
        }
        return num.intValue();
    }

    //新增审批流程
    private void insertApprovalProcessByComplaintBase(Integer baseId, String approveStatus, Date createDate, User fromuser, Date handlerDate, User toUser, String settleType, String settleOpinion){
        ApprovalProcess approvalProcess = new ApprovalProcess();
        approvalProcess.setBusiness(baseId);
        approvalProcess.setBusinessType(5);
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setFromUser(fromuser.getUserID());
        approvalProcess.setUserName(fromuser.getUserName());
        approvalProcess.setCreateDate(createDate);
        approvalProcess.setToUser(toUser.getUserID());
        approvalProcess.setToUserName(toUser.getUserName());
        approvalProcess.setOrg(fromuser.getOid());
        if (handlerDate != null) {
            approvalProcess.setHandleTime(handlerDate);
        }
        if(settleType != null){
            switch (settleType) {
                case "1" :
                    approvalProcess.setType(6);
                    break;
                case "2":
                    approvalProcess.setType(7);
                    break;
                case "3":
                    approvalProcess.setType(8);
                    break;
                case "4":
                    approvalProcess.setType(9);
                    break;
            }
        }
        if (settleOpinion != null) {
            approvalProcess.setDescription(settleOpinion);
        }
        approvalProcessDao.save(approvalProcess);
    }

    //修改审批流程
    private ApprovalProcess updateApprovalProcessByComplaintBase(Integer processId, String approveStatus, Date handlerDate, String reason){
        ApprovalProcess approvalProcess = approvalProcessDao.get(processId);
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setHandleTime(handlerDate);
        if(reason != null){
            approvalProcess.setReason(reason);
        }
        approvalProcessDao.saveOrUpdate(approvalProcess);
        return approvalProcess;
    }

    //新增一版联系人历史记录
    private void addComplaintContactHistory(ComplaintContact contact){
        ComplaintContactHistory contactHistory = new ComplaintContactHistory();
        contactHistory.setComplaintBase(contact.getComplaintBase());
        contactHistory.setComplaintContact(contact.getId());
        contactHistory.setContactName(contact.getContactName());
        contactHistory.setContact(contact.getContact());
        contactHistory.setContactPhone(contact.getContactPhone());
        contactHistory.setPost(contact.getPost());
        contactHistory.setCreator(contact.getCreator());
        contactHistory.setCreateName(contact.getCreateName());
        contactHistory.setCreateTime(contact.getCreateTime());
        contactHistory.setUpdator(contact.getUpdator());
        contactHistory.setUpdateName(contact.getUpdateName());
        contactHistory.setUpdateTime(contact.getUpdateTime());
        contactHistory.setVersionNo(contact.getVersionNo());
        complaintContactHistoryDao.save(contactHistory);
    }

    //新增处理者历史记录
    private void addCompliantProcessor(Integer processer, Integer baseId, Date createTime){
        ComplaintBaseHistory complaintBaseHistory = new ComplaintBaseHistory();
        complaintBaseHistory.setComplaint(baseId);
        complaintBaseHistory.setProcessor(processer);
        complaintBaseHistory.setCreateTime(createTime);
        complaintBaseHistoryDao.save(complaintBaseHistory);
    }

    private void addComplaintAttachment(String listAttachment, ComplaintDetail detail, User user, String module){
        if (listAttachment != null) {
            List<ComplaintAttachment> attList = JSON.parseArray(listAttachment, ComplaintAttachment.class);
            for(ComplaintAttachment ca : attList){
                ca.setComplaint(detail.getComplaint());
                ca.setComplaintDetail(detail.getId());
                ca.setCreateName(user.getUserName());
                ca.setCreator(user.getUserID());
                ca.setCreateTime(detail.getCreateTime());
                attachmentDao.save(ca);
                ComplaintUsing callback = new ComplaintUsing(ca.getId());
                uploadService.addFileUsing(callback,ca.getPath(),ca.getTitle(),user,module);
            }
        }
    }

    //长连接推送消息详情
    @Override
    public void sendComplaintMessage(Integer baseId, String type){
        HashMap<String, Object> mapMessage = this.getComplaintBase(baseId);
        mapMessage.put("messageType", type);
        clusterMessageSendingOperations.convertAndSendToUser(baseId.toString(),"/complaintMessage",null,null,null,null,JSON.toJSONString(mapMessage));
    }

    @Override
    public ComplaintAttachment getSingelByComAtt(Integer id) {
        ComplaintAttachment complaintAttachment = attachmentDao.get(id);
        return complaintAttachment;
    }

    @Override
    public ComplaintDetail getSingleByComDet(Integer id) {
        return complaintDetailDao.get(id);
    }

    @Override
    public ComplaintBase getSingleByCom(Integer id) {
        ComplaintBase complaintBase = complaintBaseDao.get(id);
        return complaintBase;
    }

    @Override
    public ComplaintSupplement getSingleBySupplement(Integer id) {
        ComplaintSupplement complaintSupplement = supplementDao.get(id);
        return complaintSupplement;
    }

    @Override
    public Date getFirstComplaintBaseDate(Integer oid) {
        String hql = "select min(createTime) from ComplaintBase where org = "+ oid;
        Date date = (Date) complaintBaseDao.getByHQLWithNamedParams(hql,null);
        if (date == null) {
            date = new Date();
        }
        return date;
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=null;
        switch (code){
            case "complaintHandle":
                number = this.getComplaintNum(user.getUserID(), user.getOid());//投诉处理
                break;
        }
        return number;
    }

    private String complaintNoticeMes(String type, String userName, String customerName){
        String mes = null;
        switch (type)
        {
            case "1":
                mes = userName + "提交了" + customerName + "的投诉立案申请";
                break;
            case "2":
                mes = customerName + "的投诉立案有待处理";
                break;
            case "3":
                mes = userName + "提交了" + customerName + "的投诉结案申请";
                break;
        }
        return mes;
    }
}
