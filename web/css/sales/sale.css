
*{ font-family: "Microsoft YaHei";   }
td,th{
	vertical-align:middle !important;
	overflow:hidden;white-space:normal; word-break:break-all;text-overflow:ellipsis;
}
.clr{ clear:both;  }

.aboutbtn{
	display:inline-block;
	cursor:pointer;
	font-size:16px;
	margin-right: 5px;
	padding:5px 18px;
	font-weight:400;
}
.aboutbtn1{
	display:inline-block;
	cursor:pointer;
	font-size:13px;
	margin-right: 5px;
	padding:5px 20px;
	font-weight:400;
}
.azury,.azury:hover{
	color:#32C5D2;
	background:#fff;
	border: 1px solid #32C5D2;
	text-decoration: none;
}
.azury:hover{
	color: #fff;
	background-color: #32C5D2;
}
.resourceAddBtn{ position: relative; top:-4px; }
.sale_head{
	/*text-align: right;*/
	width: 98%;
}
.head_left{
	display: inline-block;
	font-size: 18px;
	font-weight: 700;
}
.head_right{
	display: inline-block;
	float: right;
}
.bg{
	background-color: #f1f1f1;
}

.sale_delete{
	font-size: 20px;
	font-weight: 400;
}
.bonceHead{
	font-size: 14px;
	font-weight: 700;
}
#addAccount .bonceCon{
	margin-left:30px;
}
.sale_c{
	margin-left:20px;
	font-size: 13px;
	font-weight: 400;
	vertical-align: middle;
	padding: 4px 0;
	overflow: hidden;
	display: flex;
}
.sale_ttl1{
	margin-right: 10px;
	display: inline-block;
	min-width: 100px;
	text-align: right;
	line-height: 32px;
	vertical-align: top;
}
.sale_ttl2{
	display: inline-block;
	padding: 0 10px;
	line-height: 32px;
	min-width: 120px;
}
.sale_con2>div{
	background-color: #f1f1f1;
	border: 1px solid #e4e4e4;
	width: 155px;
	padding: 5px 35px;
	text-align: center;
}
.sale_con,.sale_con1,.sale_con2,.sale_con3,.sale_con4,.sale_seecon,.sale_seecon1{
	display: inline-block;
}
.sale_con1>input{
	width: 220px;
}
.sale_con2>input{
	width: 320px;
}
.sale_con4>input{
	width: 895px;
}
.sale_seecon{
	width: 195px;
	text-align: left;
	overflow:hidden;white-space:normal; word-break:break-all;text-overflow:ellipsis;
}
.sale_seecon1{
	width: 377px;
	text-align:left;
	white-space:normal; word-break:break-all;text-overflow:ellipsis;
}
.sale_c label{
	margin-left:30px;
	font-weight: normal;
}
.sale_c input[type='radio']{
	margin-right:10px;
}
.par_ttl{
	display: inline-block;
	width:160px;
}
.addOtherInfo{
	margin:20px 0 0;
}
.purchased{
	margin-left:40px;
	display: none;
}
/*订单查看*/
.form_head{
	padding-bottom: 32px;

}
.form_left{
	font-size: 18px;
	font-weight: 700;
	float: left;
}
.bonceContainer1{
	background:#fff none repeat scroll 0 0;
	border:1px solid #ccc;
	box-shadow:0 0 5px #333;
	margin:70px auto 100px;
	width: 1400px;
	font-size: 13px;
}
.orderform{
	width: 1370px;
	padding: 3px 0 10px 24px;
}
.ordersee_form{
	display: inline-block;
	/*width: 440px;	*/
}
.ordersee_cus{
	display: inline-block;
	background-color: #f1f1f1;
	border: 1px solid #e4e4e4;
	width: 155px;
	padding: 5px 35px;
	text-align: center;
}
.ordersee_cus1{
	width: 200px;
	margin-left: 10px;
	display: inline-block;
	text-align: left;
	overflow:hidden;white-space:normal; word-break:break-all;text-overflow:ellipsis;
}
.orderform_t{
	padding: 23px;
}
.ofm{
	text-align: left;
}
/*.ofm>div:nth-child(1){
	margin-left: 24px;
}*/

/*订单管理*/
.goods_btn{
	text-align: right;
	padding: 30px 20px 10px 0;
}
.aboutbtn_mg{
	display:inline-block;
	cursor:pointer;
	font-size:13px;
	margin-right: 5px;
	padding:5px 18px;
	font-weight:400;

}
.azury_mg,.azury_mg:hover{
	color: #333;
	background-color: #f1f1f1;
	border: 1px solid #e4e4e4;
	text-decoration: none;

}
.goods_tb{
	padding: 0 23px;
}
.ordersee_cus2{
	width: 160px;
	display: inline-block;
	text-align: left;
}
.ordersee_cus2>select,.ordersee_cus2>input{
	border: 1px solid #ccc;
	width: 270px;
	height: 30px;
	background-color: #fff;
}
.sale_con3>select{
	border: 1px solid #ccc;
	width: 235px;
	height: 30px;
	background-color: #fff;
}
/*客户管理*/
.sale_one,.sale_last{ text-align: left; position: relative ; top:25px;padding-left: 100px;  width: 206px;  z-index: 1000;}
.saleTr{  position: relative;width:90%; margin: 10px auto; }
.saleMenu{  position: absolute;top: 6px; width:150px; text-align: center;    }
.seeCon{ margin-left: 150px;   }
.saleTtl , .saleCon{ display: inline-block; border:1px solid #ddd; height:30px; line-height: 30px; vertical-align: middle;     }
.saleTtl{ width:25%;  text-align: center; background: #f1f1f1; margin: 0 10px 0 20px   }
.saleCon{ width: 235px;  padding-left:30px;    }
.seeIn{padding-left: 30px;  border: 1px solid #ddd;  height: 30px;  line-height: 30px;  margin: 0 10px 0 20px;  width: 382px; }
.flexBox{width: 100%;box-sizing: border-box;border-radius: 2px;position: relative;font-size: 13px;overflow: hidden;opacity: 1;display: flex;align-items: center;
	justify-content: space-between;transition: opacity .2s;}
.careful{color: #0070bf;}
.receiveMethod > div{padding: 30px 0;border-top: 1px solid #dddada;}
.receiveMethod .linkBtn{padding-right: 0;font-weight: bold}
.checkItem .fa-dot-circle-o{color:#5d9cec ;}
.checkItem{margin: 30px 0;}
.checkItem i{margin-right: 10px;}
.wideBody{margin: auto;width: 80%;}
.wideBold{padding: 30px 0 ;margin: auto;width: 80%;}
.goodsAddress.require-box {margin: 10px 0 0 30px;}
.recivePlaceScanBtn{margin-left: 20px;}
.receiveScan >div{padding: 30px 0;border-bottom: 1px solid #dddada;}
.receiveScan >div:last-child{border-bottom: none;}

/*订单管理页的整体样式修改*/
.orderTip,.goodsAddTip{ color:red; padding:15px 0 ; text-align:center;  }
.goodsAddTip{ position: absolute; right:-105px;top:-10px;  }
#outerSnContainer{ position:relative;}
.orderTip{ position: absolute; left:25px; top:10px; }
#ordermanage_addAccount{ margin: 100px auto;  width: 95%;    }
#goods_addAccount{ width:600px; }
#ordermanage_seeAccount{ width:95%;  }
#goods_deleteAccount { width:70%;  }
.orderTr{ margin:0 15px;  margin-bottom: 10px;            }
.orderTr>.orderTd:first-child{ margin-left: 0; }
.orderTd{ width:32%  ; display: inline-block;  margin-left: 1%;          }
.tdTtl , .tdCon {  border:1px solid #ccc; display: inline-block; height:35px; line-height: 35px;   font-size:13px;       }
.tdTtl{ width:  25% ; background: #f1f1f1; text-align:center;         }
.tdCon{ width:  70% ;  }
.tdCon>span{ padding-left: 20px;     }
.tdCon input ,.tdCon select { width: 100%; background: #fff; height:30px; border:none;         }

/*订单查询*/
#ordersee_seeAccount{ width:90%; }

#importCon{  width:95%; height:450px; border:none;    }


/*新增商品*/
.commodityCon{ position: relative;    }
.commodityList{ background-color: #fff; border: 1px solid #ccc; max-height: 150px; text-align:left; width: 235px; 	z-index: 100; overflow:auto;  }
.commodityList>option{ width:100%; padding:5px 15px ;                   }
.commodityList>option:hover{ background-color:#eaeaea;               }
.outSn{ width:235px;  }
.togShow{ display:block ; position: absolute; margin-top:-1px;  }
.togHide{ display:none ;   }

/*销售分工*/
#changeSaler{max-width:500px;}
.bg-gray{background-color: rgba(204, 203, 203, 0.4);}
.changeDetails{margin:auto;width:300px;}
.sale-ttl{width:100px;margin-right:10px;display:inline-block;}
.gapLine{margin:10px 0;}
.sale-con{padding-left:10px;min-width: 142px;line-height:32px;}
.sale-ttl2{display: inline-block;/*overflow: hidden;*/white-space:nowrap;}
.sale-con2{margin-right:20px;/*display:inline-block;max-width:80px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;*/white-space:nowrap;}
#changeSalerRecord .bonceCon{max-height:500px;overflow-y:auto;}
.single{margin-bottom:40px;width:500px;}

#seeAccount .bonceCon,#updataccount .bonceCon,#editRecords .bonceCon,#editBeforeRecords .bonceCon,#editAfterRecords .bonceCon{max-height:500px;overflow-y: auto;}
#editRecords .ty-table td{height:30px;}
.sale_bor .sale_btn{margin-left:184px;}
.clear:before,.clear:after{display:block; clear:both; content:"";  visibility:hidden;  height:0}
.seePastMail,.seePastAddress{margin-left:20px;}
.edit_mailRecive,.edit_recive{position:relative;}
.sale_rt{position:absolute;right:20px;top:5px;}
.wrapRow{padding-bottom:16px;border-bottom:1px solid #ccc;}
#cus_pastAddress .bonceCon{max-height:500px;overflow-y: auto;}
#record_pastAddress,#record_pastMail{margin-left:20px;}
.redFlag{color:red;}
.bounce_Fixed2 , .bounce_Fixed3 , .bounce_Fixed4, .bounce_Fixed5 {  position: fixed;  display: none;  background: rgba(100, 100, 100, 0.5);  width: 100%;  padding-bottom: 200px;  }
.bounce_Fixed2{ z-index: 10002;  }
.bounce_Fixed3{ z-index: 10003;  }
.bounce_Fixed4{ z-index: 10004;  }
.bounce_Fixed5{ z-index: 10005;  }
.gapTtl{margin: 0 10px 0 20px;display: inline-block;width:25%;height:1px;}
#record_historyAddress .bonceCon{max-height:550px;overflow-y:auto;}
.saleCon span{display:inline-block;width:210px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
/*客户管理1.79*/
.linkUploadify .uploadify-button { margin: 0; background-color: inherit; border: none;  line-height: 12px;  padding: 3px 6px;  font-size: 12px;  color: #1685ea;  cursor: pointer;  text-decoration: none;
	font-weight: normal;  display: inline-block;  vertical-align: middle;  }
.linkUploadify .uploadify-button:active{border: none;}
.saleUploadBtn .uploadify-button {margin: 0;background-color: #5d9cec;  color: #fff;padding: 0 24px;  height: 32px;  line-height: 32px;  border: none;border-radius: 0;}
.saleUploadBtn .uploadify-button:hover { color:#fff; background-color: #5d96e6;}
.uploadify-button:visited{text-decoration: none}
#newContectInfo .uploadify-button{line-height: 28px;}
.filePic{background-repeat: no-repeat;}
.imgsthumb{position: relative;  margin: 10px 10px 10px 0;;width:136px;display: inline-block;overflow: hidden;}
.imgsthumb .filePic{  display: inline-block;width:90px;height:70px;  background-size: 100%;  background-position: center center;  }
.imgsthumb span{  padding: 2px;  color: red;  cursor: pointer;  }
.imgsthumb a{  display: none;  background-color: rgba(93, 156, 236,.9);  color: #fff;  height: 18px;  line-height:18px;  width:36px;  margin: 0 auto 5px;  font-size: 12px;
	text-align: center;  border-radius: 2px;  cursor: pointer;  position: absolute;  top: 28px;  left: 28px;  }
.imgsthumb:hover a{  display: block;  }
.imgWall{  display: -webkit-flex;  display: flex;align-items:center;flex-wrap: wrap;}
#picShow{  position: fixed;  display: inline-flex;  justify-content: center;  background: rgba(7, 7, 7, 0.64);  z-index: 100009;  width: 100%;
	height: 100%;  text-align: center;  vertical-align: middle;  top: 0;  left: 0;  align-items: center;  }
#picShow img{  margin-top:30px;  }
#uploadCard{display:inline-block;}
.bussnessCard{display: inline-block;}
.bussnessCard .filePic{display:inline-block;width: 260px; height: 150px; background-size: contain;  background-repeat: no-repeat;}
.flex-box{display: flex;justify-content:space-between;flex-wrap: wrap;}
.flex-box>div{margin-bottom: 12px;}
.flex-box .fieldSm{width:150px;}
.flex-box .fieldMid span{  margin-right: 20px;  }
.rowGap{margin-bottom:12px;}
.sale_see{margin:0 30px;}
#customer_main{ font-size: 16px; font-weight: bold; }
.indexUpdateList tbody tr td{  text-align:left;  }
.indexUpdateList tbody tr td:first-child{  max-width:400px;  }
.sign{margin-left:2em;}
.signed{margin-left:2em;color: #e26b69;}
.sign span{margin-right:12px;}
.signed span{margin-right:12px;}
.otherContact{overflow-y: hidden; }
.otherContact li{float:left;margin-bottom:10px;width:346px;}
#newContectData .addMore{margin-left: 6px;margin-right: 23px;}
.gap{ margin-right:4%; }
.shDisUse,.fpDisUse {margin-left:10px;}
#see_contactsCard{width:400px;height: 220px;text-align: center;}
#see_contactsCard img{max-height: 100%;  max-width: 100%;}
.intervieweeName{margin-right:20px;font-size:16px; color: #069b87;}
#addMoreContact option[disabled]{background: #ddd;cursor: pointer;}
.definedCon{position: relative;margin: auto;text-align: center;  width: 300px; }
.definedCon .ttl{font-weight:600;}
.definedCon input{padding-right:10px;width:100%;}
.clearLableText{position: absolute;  right: 8px;bottom: 6px;  cursor: pointer;  }
.sale_gap{margin-right:32px;}
 .see_otherContact,.record_otherContact{ margin: 20px auto;  }
 .see_otherContact tr>td:nth-child(odd), .record_otherContact tr>td:nth-child(odd){ width:20%;   }
 .see_otherContact tr>td:nth-child(even), .record_otherContact tr>td:nth-child(even){ width: 30%;   }
.contactflex{display:flex;padding-left: 30px;}
.createDetail{text-align:right;padding-right: 30px;}
.see_createName{margin: 0 10px 0 20px;}
.record_otherContact{overflow: hidden;}
.record_otherContact li{float: left;width:50%;margin-bottom: 12px;}
.record_otherContact li>div{float: left;}
.type-text{width:30px;}
#record_contactName{width:130px;}
.interviewRecordsList{margin-top:12px;}
.contectList{overflow: hidden;}
.leftList,.rightList{width:50%;float: left;}
#record_contactsCard{margin-left: 120px;}
.siteSize{min-width:850px;}
#mailNumError{margin-left: 30px;color:red;display: none;}

#mtTip span,#mtTip p{font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;}
#mtTip p{margin:0;}
.bonceHead{font-size:16px;font-weight: normal;}
.bonceContainer {font-size: 14px;}
.module_avatar{
	width: 620px;
}
.module_avatar .module_header{
	background-color: #f2f2f2;
	border-right: 1px solid #e2e2e2;
}
.module_avatar .module_body{
	border-right: 1px solid #e2e2e2;
	border-bottom: 1px solid #e2e2e2;
	background-color: #fff;
}
.module_row{
	display: flex;
	align-items: center;
	border-top: 1px solid #e2e2e2;
	margin-top: -1px;
}
.module_list{
	border-left: 1px solid #e2e2e2;
	min-height: 36px;
	width: 100%;
}
.module_item{
	padding: 8px 40px;
	min-width: 200px;
}
.ty-table-none tbody tr td{
	text-align: left;
	padding: 0 24px;
}
.orgInfo_content{
	color: #666;
}
.btn-group{
	display: block;
}
a.ty-color-blue{
	padding: 6px 8px;
	margin-right: 8px;
	font-weight: bold;
}
a.ty-color-blue:hover {
	text-decoration: underline;
}
.bonceCon input:not([type="radio"]):not([type="checkbox"]):disabled, .bonceCon select:disabled{
	background-color: #eee;
}
#seeAccount .specialOrgPart{
	font-size: 12px;
}
.gapT{
	padding: 10px;
}
.gapL{
	margin-right: 150px;
}
.loginQuery .searchCon{
	width: 274px;
	padding: 20px 0;
}
.loginQuery table td{
	border: none;
}
.loginQuery input{
	height:30px
}
.ty-btn-groupCom span {
	position: relative;
	line-height: 32px;
	background-color: #bfbfbf;
	color: #000;
	font-size: 14px;
}
.ty-btn.loginQuery{
	background-color: #bfbfbf;
	color: #000;
}
.ty-btn-blue.loginQuery{
	background-color: #5d9cec;
	color: #fff;
}
.ty-btn-groupCom {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	white-space: nowrap;
	font-size: 0;
	-webkit-text-size-adjust: none;
}
.ty-btn-groupCom > .ty-btn-active-blue {
	background-color: #5d9cec;
	color: #fff;
}
.ty-table.definedSize td{
	padding: 0 4px;
}

.item_avatar{
	margin-bottom: 8px;
}
.item{
	display: flex;
}
.item_theme{
	line-height:32px;
	display: inline-block;
	min-width: 100px;
}
.item_theme .title{
	line-height: 14px;
	color: #5d9cec;
	border-left: 3px solid #5d9cec;
	padding-left: 4px;
	font-weight: bold;
}
.item .item_title{
	display: inline-block;
	min-width: 100px;
	line-height:32px;
	text-align: right;
	margin-right: 4px;
	flex:none;
}
.item .item_content{
	display: inline-block;
	min-width: 200px;
	line-height:32px;
	color: #62707e;
	flex: auto;
}

/*张*/
section.salePanel{
	margin: 16px 0;
	padding: 0;
	border: none;
}
section.salePanel .panel_title{
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}
section.salePanel .panel_title .title{
	height: 18px;
	line-height: 18px;
	color: #666;
	border-left: 3px solid #999;
	padding-left: 4px;
	font-weight: bold;
}
section.salePanel .panel_title .btn-group{
	flex: auto;
}
section.salePanel .panel_content{
	padding: 8px 0 8px 48px;
}
section.salePanel .item .item_title{
	text-align: left;
}

.right_btn{
	flex: auto;
	text-align: right;
	line-height:32px;
}
.part{
	padding: 4px 8px;
	border-bottom: 1px solid #e1e7ec;
}

.part21{
	padding: 4px 8px;
}
.part_end{
	padding: 4px 8px;
}
.ty-table tr.disable td{
	color: #ed5565;
}
.red{ color: #ed5565; }

.chooseCusBtn{ background: #ddd; width: 40px; display:inline-block; text-align: center; line-height: 30px; height: 30px; margin-left: -5px;  }
.linkBtn{ color: #036bc0; line-height: 30px; padding: 0 20px; cursor: default;}
.linkBtn:hover{ color: #0482ea; }
.cusList { margin:20px auto; width:90%;   }
.cusList>li{ list-style: none; background:#fff; line-height:30px; padding:0 15px;  }
.cusList>li:nth-child(even){ background:#f5f5f5;  }
#mailName, #ReceiveName{ height: 30px; width: 180px; line-height: 30px;  }
#contactFlag{ line-height:30px;  }
#chooseCusContact .fa{ color: #50a9ea;    }

.rscon{ padding:20px; border-bottom:1px solid #ddd;   }
.rscon p>span:nth-child(1){ display: inline-block; width:150px;   }
.setItem>.fa{ color:#5d9cec; margin-right:15px;   }
#invoiceSet .tipsmall{ color:#5d9cec; font-size:0.8em;   }
.fileCon{ margin-bottom: 10px; border: 1px solid #ddd; min-height: 30px; border-radius: 2px; background: #fff; line-height: 30px; padding: 0 9px; }
.fileCon .fileIm{ margin:0;  }
.fileCon .fileIm .fa{ margin:0 8px; color:#5d9cec;   }

#tipcontractGoods .controlTd{ position: relative; overflow: initial; -webkit-user-select:none; -moz-user-select:none; -ms-user-select:none; user-select:none; }
#tipcontractGoods .controlTd .fa {  position: absolute;  left: -46px;  font-size: 21px;  color: #5d9cec;  }

#cScan .leftTab .fa{ color: #5d9cec; }
#cScan .leftTab tbody tr td{  text-align:left;  }
#cScan .leftTab tbody tr td:first-child{  max-width:400px;  }
#cScan .fileImScan>span{ color: #5d9cec; font-size: 18px; padding: 0;}
#cScan .enabledList{ text-align: right; max-height: 100px; overflow: auto; margin-top: 10px; color: #666; font-size: 13px;}
#cScan .enabledList p{ margin-bottom: 4px  }
#cScan .enabledList .enName{ display: inline-block; width: 60px; text-align: center;  }
.uploadify-queue{display: none;}

.exportStep {  padding-left: 40px;  }
.flexRow{  margin-left: 50px;  display: flex;  justify-content:space-between;  width: 356px;  }
.fileFullName{  width: 278px; line-height:26px;background: #fff;text-align: center;}
.stepItem {  margin-bottom: 20px;  }
.viewBtn .uploadify-button{ padding: 0 22px; min-width: 80px;  margin: 0; border-radius: 0; height: 26px;  line-height: 26px;background-color: #5d9cec;  color: #fff;border: none;}
.viewBtn .uploadify-button:hover{ background: #5d9cec;}
.ty-btn-middle {  padding: 0px 22px;  height: 26px;  line-height: 26px;  }
.Search {  display: inline-block;  width: 250px;  height: 35px;  position: relative;  }
.Search input {  display: inline-block;  width: 100%;  height: 100%;  border: 1px solid #48cfad;  color: #333;  padding: 0 60px 0 10px;  border-radius: 4px;  }
.Search .se {  position: absolute;  top: 0;  right: 0;  border-radius: 0 4px 4px 0;  display: inline-block;  width: 50px;  height: 100%;  background: #48cfad;
	color: #fff;  line-height: 35px;  text-align: center;  cursor: default;  }
.Search .se:hover {  background: #37bc9b;  }
.gap-lt{margin-left: 50px;}

.brdTop{
	display: block;
	margin: 16px 0;
	line-height: 1.5;
	padding: 8px;
	font-weight: bold;
	border-left: 8px solid #eee;
	background-color: #fff;
}
.brdTopRed{ border-color:#ed5565;color: #ed5565; }
.brdTopGreen{ border-color:#48cfad;color: #48cfad;}
.brdTopBlue{ border-color:#5d9cec;color: #5d9cec; }
.productName{
	font-size: 16px;
	font-weight: bold;
}
.modelNameRow{
	font-size: 14px;
	line-height: 36px;
	border-bottom: 1px solid #eee;
	color: #999;
}
.modelNameRow .modelName{
	margin-left: 8px;
	color: #666;
}
.changeNum{
	margin: 0 4px;
	font-weight: bold;
}
.nullStr{
	padding: 4px;
	text-align: center;
	color: #999;
}

.mode{display: block;border-bottom: 1px solid #bfbfbf;margin-bottom: 17px;margin-top: 19px;}
.ud{margin:14px 0;margin-bottom:23px;}
.item .reciveDescript{color: #101010;}
.areaForm {margin: auto;width: 85%;}
.areaForm span.sale_ttl1 {width: 180px;text-align: left;}
.sale_con1 > input,.areaCheck {width: 220px;}
.clearVal{position: relative;}
input:focus + i.clearInputVal{  display: block;  }
.clearVal .clearInputVal{position: absolute;  display: none;right: 8px;font-style: normal;color: #7a7b7e;top: 4px;cursor: pointer;}

#updateRecords{ width: 740px; }
.regionSect .sale_ttl1{width: 180px;text-align: left;}
#requirements{width: 258px;}
.otherContact .cellCon{margin-left: 4px;margin-right: 8px;}


.cItem{
	margin-bottom: 8px;
	line-height: 30px;
}
.cItem > div{
	margin-bottom: 4px;
	min-height: 20px;
	line-height: 20px;
}
.bounceMainCon{
	width: 95%;
	margin: 4px auto;
}

.row{
	padding: 8px 20px;
	display: flex;
	justify-content: space-between;
}
.item_name {
	width: 200px;
}
.item_code{
	flex: 1;
}
.item_principalName{
	width: 180px;
}
.item_mobile{
	width: 120px;
}
.item_button{
	flex: 1;
}
.item_address {
	width: 100%;
}
.bonceCon input:not([type="radio"]):not([type="checkbox"]):disabled, .bonceCon select:disabled{
	background-color: #eee;
}
.ty-spanText{
	border: 1px solid #dcdfe6;
	border-radius: 2px;
	background-color: #f0f0f0;
	line-height: 30px;
	box-sizing: border-box;
	color: #606266;
	display: inline-block;
	width: 100%;
	padding: 0 8px;
	min-height: 32px;
}
.tip{
	padding: 8px;
	text-align: center;
}
.brdTop{
	display: block;
	margin: 16px 0;
	line-height: 1.5;
	border-left: 8px solid #0b94ea;
	padding: 8px;
	background: #f6f6f6;
	font-weight: bold;
	width: 604px;
}
.brdTopRed{ border-left-color:#ed5565;color: #ed5565; }
.brdTopGreen{ border-left-color:#48cfad;color: #48cfad;}
.brdTopBlue{ border-left-color:#5d9cec;color: #5d9cec; }
.modelTabCon{
	max-height: 250px;
	overflow-y: auto;
	overflow-x: hidden;
}
.module-item{
	padding: 8px 16px;
	min-width: 198px;
}
.kj-table.noPadding th,.kj-table.noPadding td{
	padding: 0;
}
.modelTabCon .kj-table td:first-child{
	width: 200px;
}
.productName{
	font-size: 16px;
	font-weight: bold;
}
.modelNameRow{
	font-size: 14px;
	line-height: 36px;
	border-bottom: 1px solid #eee;
	color: #999;
}
.modelNameRow .modelName{
	margin-left: 8px;
	color: #666;
}
.changeNum{
	margin: 0 4px;
	font-weight: bold;
}
.nullStr{
	padding: 4px;
	text-align: center;
	color: #999;
}
.tipMsg{
	padding: 16px;
	text-align: center;
}
.item-flex{
	display: flex;
	line-height: 30px;
	margin: 8px 0;
}
.item-title{
	width: 120px;
	flex: none;
}
.item-content{
	flex: auto;
}
.orgBase td .ty-inputText, .orgBase td .ty-select{
	width: 90%;
}
.kj-table td.ty-null{
	text-align: center;
}
.kj-table td.ty-null img{
	width: 36px;
}
.kj-table td.ty-null .text{
	display: inline;
	font-size: 13px;
	color: #aaa;
	margin-left: 4px;
}
#newContractInfo .link-blue{
	padding: 1px 8px 2px 8px;
}
.gapFar{
	margin-left: 22px;
	margin-right: 12px;
}