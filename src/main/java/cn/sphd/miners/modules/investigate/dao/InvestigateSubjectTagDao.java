package cn.sphd.miners.modules.investigate.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubjectTag;

import java.io.Serializable;

/**
 * @ClassName UnitDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/23 17:38
 * @Version 1.0
 */
public interface InvestigateSubjectTagDao extends IBaseDao<InvestigateSubjectTag, Serializable> {

}
