package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.dailyAffairs.service.impl.IncomingQueryLeave;
import cn.sphd.miners.modules.dailyAffairs.service.impl.OverturnLeave;
import cn.sphd.miners.modules.generalAffairs.dao.LeaveTypeDao;
import cn.sphd.miners.modules.generalAffairs.dto.WorkOffBlockDto;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUserDetail;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelLeaveType;
import cn.sphd.miners.modules.generalAffairs.service.AttendanceEventSerivce;
import cn.sphd.miners.modules.generalAffairs.service.LeaveTypeService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.WorkdayType;
import cn.sphd.miners.modules.message.service.MessageService;
import cn.sphd.miners.modules.personal.dao.PersonnelLeaveDao;
import cn.sphd.miners.modules.personal.dao.PersonnelLeaveItemDao;
import cn.sphd.miners.modules.personal.dto.LeaveQuery;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelLeave;
import cn.sphd.miners.modules.personal.entity.PersonnelLeaveItem;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.PopedomTaskService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2017/5/8.
 */
@Service("leaveService")
public class LeaveServiceImpl extends BaseServiceImpl implements LeaveService, AttendanceEventSerivce {
    final String sdf = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    PersonnelLeaveDao personnelLeaveDao;
    @Autowired
    PersonnelLeaveItemDao personnelLeaveItemDao;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    MessageService messageService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserService userService;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    WorkAttendanceService workAttendanceService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    RoleService roleService;
    @Autowired
    LeaveTypeDao leaveTypeDao;
    @Autowired
    LeaveTypeService leaveTypeService;
    @Autowired
    PopedomTaskService popedomTaskService;

    @Override    //type 1-职工中的(所有人)   2-我的团队（本人及其下属）
    public List<PersonnelLeave> getPersonnelLeaveListByUser(Integer userId,Integer oid, String approvalStatus,String actualState,String beginTime,String endTime,Integer type) {
        Map<String,Object> params = new HashMap<String, Object>();
//        String hql = "from PersonnelLeave o where o.actualEndTime>=:newDate";
//        params.put("newDate",new Date());
//        if (oid!=null){
//            hql+=" and o.org=:org";
//            params.put("org",oid);
//        }
        String hql = "from PersonnelLeave o where o.org=:org";
        params.put("org",oid);
        if (type!=null&&type==2){
            if (userId!=null){
                Map<String, Object> map = userService.getUserIds(userId, 1);
                String userIds = (String) map.get("userIdsString");
                hql+=" and o.user_ in ("+userIds+")";
            }
        }else if (type==null||type!=1){
            if (userId!=null){
                hql+=" and o.user_=:userId";
                params.put("userId",userId);
            }
        }

        if (!MyStrings.nulltoempty(approvalStatus).isEmpty()){
            hql+=" and o.approveStatus=:approvalStatus";
            params.put("approvalStatus",approvalStatus);
            if ("2".equals(approvalStatus)){  //查询已批准的，已批准中的是请假时间未完的请假
                hql+=" and o.actualEndTime>=:newDate";
                params.put("newDate",new Date());
            }
        }else {
            hql+=" and o.approveStatus=1";
        }
        if (!MyStrings.nulltoempty(actualState).isEmpty() && "1".equals(actualState)){
            hql+=" and o.actualState=:actualState";
            params.put("actualState",actualState);
        }else if ("0".equals(actualState)){
            hql+=" and (o.actualState = null or o.actualState = 0)";
        }
        if (!MyStrings.nulltoempty(beginTime).isEmpty()){
            hql+=" and o.createDate>=:beginTime";
            params.put("beginTime",NewDateUtils.today(NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd HH:mm:ss")));
        }
        if (!MyStrings.nulltoempty(endTime).isEmpty()){
            hql+=" and o.createDate<=:endTime";
            params.put("endTime",NewDateUtils.getLastTimeOfDay((NewDateUtils.dateFromString(endTime,"yyyy-MM-dd HH:mm:ss"))));
        }
        hql+=" order by o.beginTime asc";
        return personnelLeaveDao.getListByHQLWithNamedParams(hql,params);
    }



    @Override
    public Double getDuration(Date begin, Date end) {
        //wyu：时间差毫秒数除以小时毫秒数后，四舍五入保留一位小数。
        return BigDecimal.valueOf(Double.valueOf(end.getTime() - begin.getTime())/ TimeUnit.HOURS.toMillis(1)).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
    }



    @Override
    public List<PersonnelLeaveItem> getPersonnelLeaveItemByUserId(Integer userId, String approvalStatus, String actualState, String beginTime, String endTime) {
        String hql=" and o.leave.user="+userId;
        if (!"".equals(approvalStatus) && approvalStatus!=null){
            hql+=" and o.approveStatus="+approvalStatus;
        }else {
            hql+=" and o.approveStatus=1";
        }
        if (!"".equals(actualState) && actualState!=null){
            hql+=" and o.actualState = "+actualState;
        }
        if (!"".equals(beginTime) && beginTime!=null){
            hql+=" and o.createDate>='"+beginTime+" 00:00:00'";
        }
        if (!"".equals(endTime) && endTime!=null){
            hql+=" and o.createDate<='"+endTime+" 23:59:59'";
        }
        Map<String,String> orderBy = new HashMap<String, String>();
        orderBy.put("o.createDate","desc");
        return personnelLeaveItemDao.findCollectionByConditionNoPage(hql,null,orderBy);
    }

    @Override
    public List<PersonnelLeave> getPersonnelLeaveListByTime(Integer userId,Date beginTime, Date endTime) {
        Map<String,Object> params = new HashMap<>();
        String hql="from PersonnelLeave where user_=:userId and (approveStatus='2' or approveStatus='1' and actualState='1') and actualEndTime>:beginTime and actualBeginTime<:endTime and actualBeginTime<actualEndTime order by actualBeginTime,id";
        params.put("userId", userId);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        List<PersonnelLeave> personnelLeave=personnelLeaveDao.getListByHQLWithNamedParams(hql,params);
        return personnelLeave;
    }

    @Override
    public List<WorkOffBlockDto> getBlocks(User user, Date begin, Date end) {
        List<PersonnelLeave> list = getPersonnelLeaveListByTime(user.getUserID(), begin, end);
        String beanName = getBeanName();
        return list.stream().map(source->new WorkOffBlockDto(WorkdayType.DayOff, beanName, source.getId(), source.getActualBeginTime(), source.getActualEndTime(), source.getUpdateDate())).collect(Collectors.toList());
    }

    @Override
    public Pair<Integer, WorkOffBlockDto> getToUpdateEvent(Integer business) {
        PersonnelLeave leave = personnelLeaveDao.get(business);
        return Pair.of(leave.getUser_(), new WorkOffBlockDto(WorkdayType.DayOff, getBeanName(), leave.getId(), leave.getActualBeginTime(), leave.getActualEndTime(), leave.getUpdateDate()));
    }

    @Override
    public void fillNewDetail(PersonnelAttendanceUserDetail detail) {
        PersonnelLeave item = personnelLeaveDao.get(detail.getBusiness());
        detail.setReason(StringUtils.isNotBlank(item.getActualReason())?item.getActualReason():item.getReason());
        detail.setBusinessType(item.getType());
        detail.setLeaveType(item.getLeaveType());
        detail.setLeaveTypeName(getLeaveTypeName(item.getLeaveType(), item.getType()));
        Integer userId = item.getCreator()!=null?item.getCreator():item.getUpdator();
        String userName = StringUtils.isNotBlank(item.getCreateName())?item.getCreateName():item.getUpdateName();
        if(userId!=null){
            detail.setCreator(userId);
        }
        if(StringUtils.isNotBlank(userName)){
            detail.setCreateName(userName);
        }
        userId = item.getUpdator()!=null?item.getUpdator():item.getCreator();
        userName = StringUtils.isNotBlank(item.getUpdateName())?item.getUpdateName():item.getCreateName();
        if(userId!=null){
            detail.setUpdator(userId);
        }
        if(StringUtils.isNotBlank(userName)){
            detail.setUpdateName(userName);
        }
    }

    @Override
    public List<PersonnelLeaveItem> getPersonnelLeaveItemByleaveId(Integer leaveId, String approvalStatus) {
        Map<String,Object> orderBy = new HashMap<String, Object>();
        String hql = " from PersonnelLeaveItem where leaveId=:leaveId";
        if (!MyStrings.nulltoempty(approvalStatus).isEmpty()){
            if ("4".equals(approvalStatus)){
                hql+=" and approveStatus is not '1'";
            }else {
                hql+=" and approveStatus=:approveStatus";
                orderBy.put("approveStatus",approvalStatus);
            }
        }
        orderBy.put("leaveId",leaveId);
        hql+=" order by id desc";
        return personnelLeaveItemDao.getListByHQLWithNamedParams(hql,orderBy);
    }

//    //申请人默认驳回 type(1-加班 2-计划请假 3-提前结束请假)  approveStatus(3-驳回)
//    @Override
//    public void defaultRejectsApplicant(Integer userId,Integer type, PersonnelLeave personnelLeave, PersonnelLeaveItem personnelLeaveItem, PersonnelOvertime personnelOvertime, String approveStatus) {
//        Map<String,Object> map = new HashMap<>();
//        String reason = null;   //reason(驳回原因)
//        String eventType = null;  //eventType(申请事件)
//        Date beginTime = new Date();  //当前时间
//        if (type!=null){
//            if (type==1){  //1-加班
//                eventType = "加班结果";  //申请事件
//            }else if (type==2){   //2-计划请假
//                eventType = "请假结果";  //申请事件
//                reason = " 该申请未被及时审批";
//                String hql = " update PersonnelLeave pl, ApprovalProcess ap,UserMessage um " +
//                        "set pl.approveStatus=3,pl.applyMemo=:reason,pl.auditDate=:handleTime,ap.approveStatus=3,ap.reason=:reason,ap.handleTime=:handleTime,um.approvalStatus=3,um.handleReply=:reason,um.eventType=:eventType,um.state=2,um.handleTime=:handleTime" +
//                        "where pl.user=:userId and pl.actualState!=1 and pl.approvalStatus=1 and pl.beginTime<=:beginTime and ap.business=pl.id and ap.businessType=3 and ap.approveStatus=1 on um.messageType=3 and um.messageId=ap.id and um.approvalStatus=1;";
//                map.put("eventType",eventType);
//                map.put("reason",reason);
//                map.put("userId",userId);
//                map.put("handleTime",personnelLeave.getBeginTime());
//                map.put("beginTime",beginTime);
//                personnelLeaveDao.queryHQLWithNamedParams(hql,map);
//
//            }else {   //3-提前结束请假
//                eventType = "请假结果";  //申请事件
//            }
//        }
//    }

    //此接口查出的为已结束的请假(即请假的开始时间与结束时间均在当前时间之前)
    @Override
    public List<PersonnelLeave> getPersonnelLeaveListByBeginTime(Integer userId, String approvalStatus, String actualState, String beginTime, String endTime) {
        Date today = new Date();
        String hql=" and o.user="+userId;
        if (!"".equals(approvalStatus) && approvalStatus!=null){
            hql+=" and o.approveStatus='"+approvalStatus+"'";
        }else {
            hql+=" and o.approveStatus=1";
        }
        if (!"".equals(actualState) && actualState!=null && "1".equals(actualState)){
            hql+=" and o.actualState = "+actualState;
        }else if ("0".equals(actualState)){
            hql+=" and (o.actualState = null or o.actualState = 0)";
        }
        if (!"".equals(beginTime) && beginTime!=null){
            hql+=" and (o.actualEndTime between '"+beginTime+" 00:00:00' and '"+NewDateUtils.dateToString(today,"yyyy-MM-dd HH:mm:ss")+"')";
        }
        if (!"".equals(endTime) && endTime!=null){
            hql+=" and o.actualBeginTime<='"+endTime+" 23:59:59'";
        }
        Map<String,String> orderBy = new HashMap<String, String>();
        orderBy.put("o.actualBeginTime","desc");
        return personnelLeaveDao.findCollectionByConditionNoPage(hql,null,orderBy);
    }

    @Override   //toUserId (审批人的加班)   approvalStatus(审批流程中的审批状态)   businessType(审批流程中的审批类型)
    public List<PersonnelLeave> getPersonnelLeaveListByApproverId(Integer leaveUserId,Integer toUserId, String approvalStatus, Integer businessType, String beginTime, String endTime, PageInfo page) {
        Date today = new Date();
        String hql = " and o.approveStatus=2";
        if (leaveUserId!=null){
            hql+=" and o.user="+leaveUserId;    //申请人id
        }
        if (!"".equals(beginTime) && beginTime!=null){
            hql+=" and (o.actualEndTime between '"+beginTime+" 00:00:00' and '"+NewDateUtils.dateToString(today,"yyyy-MM-dd HH:mm:ss")+"')";
        }
        if (!"".equals(endTime) && endTime!=null){
            hql+=" and o.actualBeginTime<='"+endTime+" 23:59:59'";
        }
        if (toUserId!=null){
            hql+=" and o.id in (select business from ApprovalProcess p where p.toUser="+toUserId+" and p.approveStatus="+approvalStatus+" and p.businessType="+businessType+")";
        }
        Map<String,String> orderBy = new HashMap<String, String>();
        orderBy.put("o.beginTime","desc");  //以加班开始时间倒序排列
        List<PersonnelLeave> personnelLeaves = personnelLeaveDao.findCollectionByConditionNoPage(hql,null,orderBy);
        return personnelLeaves;
    }


    @Override
    public List<PersonnelLeave> getPersonnelLeaveByApproval(Integer oid,Integer toUserId, String approvalStatus, Integer businessType) {
        Map<String,Object> map = new HashMap<>();
//        StringBuffer hql = new StringBuffer(" from PersonnelLeave o where o.actualEndTime>=:newDate");
//        map.put("newDate",new Date());  //请假结束后不再已批准中存在，近查询
        StringBuffer hql = new StringBuffer(" from PersonnelLeave o where o.org=:org");
        map.put("org",oid);

        if (toUserId!=null){
            hql.append(" and o.id in (select business from ApprovalProcess p where p.toUser=:toUserId");
            map.put("toUserId",toUserId);
            if (!"".equals(approvalStatus) && approvalStatus!=null){
                hql.append(" and p.approveStatus=:approvalStatus");
                map.put("approvalStatus",approvalStatus);  //必须有
            }

            if (businessType!=null){
                hql.append(" and p.businessType=:businessType");
                map.put("businessType",businessType);
            } else {
                hql.append(" and (p.businessType=3 or p.businessType=8)");
            }
            hql.append(")");
        }

        hql.append(" order by o.beginTime asc");  //以请假开始时间顺序排列

        List<PersonnelLeave> personnelLeaves = personnelLeaveDao.getListByHQLWithNamedParams(hql.toString(),map,null);
        return personnelLeaves;
    }

    @Override
    public List<PersonnelLeave> getPersonnelLeaveByApprovalState(Integer userId) {
        Map<String,Object> map = new HashMap<>();  //计划请假的审批流程与提前结束请假的审批流程一样
        String hql = "select max(ap.id) from PersonnelLeave po,ApprovalProcess ap where po.id=ap.business and ap.businessType=3 and po.approveStatus in('1','2') and po.actualEndTime>=:newDate";
        if (userId!=null){
            hql+=" and ap.toUser=:toUserId";
            map.put("toUserId",userId);
        }
        map.put("newDate",new Date());  //请假结束后不再已批准中存在，近查询
        hql+=" group by po.id";
        List<Integer> ids = (List<Integer>) personnelLeaveDao.getListByHQLWithNamedParams(hql,map);

        map.clear();

        //查询提前结束请假未审批的请假id
        String hql1 = "select po.leaveId from PersonnelLeaveItem po,ApprovalProcess ap where po.id=ap.business and ap.businessType=8 and po.approveStatus='1' and ap.approveStatus ='1'";
        if (userId!=null){
            hql1+=" and ap.toUser=:toUserId";
            map.put("toUserId",userId);
        }
        List<Integer> leaveItemIds = (List<Integer>) personnelLeaveDao.getListByHQLWithNamedParams(hql1,map);

        map.clear();

        List<PersonnelLeave> personnelLeaves = new ArrayList<>();
        if (ids.size()>0){
            String hql2 = "select po from PersonnelLeave po,ApprovalProcess ap where ap.id in(:ids) and po.id=ap.business and ap.approveStatus='2'";
            map.put("ids",ids);
            if (leaveItemIds.size()>0){   //去掉提前结束请假未审批的请假
                hql2+=" and po.id not in(:leaveItemIds)";
                map.put("leaveItemIds",leaveItemIds);
            }
            hql2+=" group by po.id";
            personnelLeaves = personnelLeaveDao.getListByHQLWithNamedParams(hql2.toString(),map);
        }
        return personnelLeaves;
    }

    @Override
    public PersonnelLeave addPersonnelLeave(String beginTime, String endTime,Integer leaveType, String reason,Byte kind, User user,Integer itemId,Double duration) {
        PersonnelLeave personnelLeave = new PersonnelLeave();
        Date begin = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd HH:mm:ss");
        Date end = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd HH:mm:ss");

        personnelLeave.setKind(kind);  //类型:1-普通(默认),2-补报
//        Integer leaveType = personnelLeave.getLeaveType();
        if (leaveType>0){
            personnelLeave.setLeaveType(leaveType);
        }else if (leaveType<0){
            leaveType = -leaveType;
            personnelLeave.setType(leaveType.toString());
            personnelLeave.setActualType(personnelLeave.getType());  //1-事假,2-病假,3-婚假,4-产假,5-产假(1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他')
            personnelLeave.setLeaveType(null);
        }
        personnelLeave.setApproveStatus("1");
        personnelLeave.setBeginTime(begin);
        personnelLeave.setEndTime(end);
        personnelLeave.setDuration(duration);//时长
        personnelLeave.setActualDuration(duration);  //实际时长
        personnelLeave.setApproveDuration(duration);  //批准时长
        personnelLeave.setActualState("0");  //实际请假状态:0/null-按计划完成,1-提前消假',
        personnelLeave.setActualBeginTime(personnelLeave.getBeginTime());   //实际起始时间
        personnelLeave.setActualEndTime(personnelLeave.getEndTime());  //实际截止时间
        personnelLeave.setCreateName(user.getUserName());
        personnelLeave.setOrg(user.getOid());
        personnelLeave.setCreator(user.getUserID());
        personnelLeave.setUser(user);
        personnelLeave.setUser_(user.getUserID());
        personnelLeave.setAuditDate(new Date());
        personnelLeave.setReason(reason);
        if (StringUtils.isNotEmpty(personnelLeave.getReason())){
            personnelLeave.setActualReason(personnelLeave.getReason());  //实际原因
        }
        if (StringUtils.isNotEmpty(user.getDepartment())){
            personnelLeave.setDepartment(Integer.getInteger(user.getDepartment()));
        }
        personnelLeave.setApproveItem(itemId);  //审批设置的id
        personnelLeaveDao.save(personnelLeave);
        return personnelLeave;
    }

    @Override
    public void overturn(Integer id) {
        System.out.println("请假申请自动驳回开始");

        PersonnelLeave personnelLeave=personnelLeaveDao.get(id);
        if ("1".equals(personnelLeave.getApproveStatus())&&personnelLeave.getApprovalProcessHashSet().size()==0){
            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(id, 3, null);
            String reason="该申请未被及时审批";
            personnelLeave.setApproveStatus("3");
            personnelLeave.setApplyMemo(reason);//驳回理由
            personnelLeave.setAuditDate(personnelLeave.getBeginTime());
            personnelLeaveDao.update(personnelLeave);

            //给申请人 待处理推
            this.leaveRejectSend(0,-1,personnelLeave,personnelLeave.getUser_(),"/applyLeaveHandle",null,null,"applyLeave");

            System.out.println("请假申请自动驳回开始,id 是："+id);

            userSuspendMsgService.saveUserSuspendMsg(1, "您的请假申请被驳回了！", "驳回时间  " + NewDateUtils.dateToString(personnelLeave.getBeginTime(),"yyyy-MM-dd HH:mm:ss"), personnelLeave.getUser_(),"applyLeaveDetail",id);

            for (ApprovalProcess approvalProcess : approvalProcessList) {
                if ("1".equals(approvalProcess.getApproveStatus())) {

                    //待处理的变为 驳回
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setHandleTime(personnelLeave.getBeginTime());
                    approvalProcess.setReason(reason);
                    approvalProcess.setUserName("系统");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    //给审批者 待处理推
                    this.leaveRejectSend(-1,-1,personnelLeave,approvalProcess.getToUser(),"/approvalLeaveHandle",null,null,"approvalLeave");

                } else if ("2".equals(approvalProcess.getApproveStatus())) {

                    //给审批者 已批准推
                    this.leaveRejectSend(0,-1,personnelLeave, approvalProcess.getToUser(), "/approvalLeaveApproval",null,null,"approvalLeave");

                }
            }
        }
        System.out.println("请假申请自动驳回结束");

    }


    //请假申请 驳回 长连接推送   pass 通道  superscript 角标
    @Override
    public void leaveRejectSend(int loginNum,int operate,PersonnelLeave personnelLeave,Integer toUserId,String pass,String title, String content,String code){
        System.out.println("请假推送开始："+new Date());
        System.out.println("请假id："+personnelLeave.getId()+" userId："+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("personnelLeave", personnelLeave);
        User approvalUser= userService.getUserByID(toUserId);
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,approvalUser,code);
        System.out.println("请假推送结束："+new Date());

    }

    @Override
    public Integer getLeaveCounts(Integer userId, String approveStatus) {
        String hql="select count(id) from PersonnelLeave where user.userID=:userId and approveStatus=:approveStatus and actualEndTime>:actualEndTime ";
        Map<String,Object> map = new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus);
        map.put("actualEndTime",new Date());
        Long count= (Long) personnelLeaveDao.getByHQLWithNamedParams(hql,map);
        return count.intValue();
    }

    @Override
    public HashMap<String, Object> myLeaveInfoById(Integer userId, Integer id, Integer numType,HashMap<String,Object> map) {
        List<PersonnelLeaveItem> personnelLeaveItems = new ArrayList<>();
        PersonnelLeave leave = new PersonnelLeave();
        if (numType!=null && numType==1){
            PersonnelLeaveItem personnelLeaveItem = userMessageService.getPersonnelLeaveItemById(id);
            leave = personnelLeaveItem.getLeave();
        }else {
            leave = userMessageService.getPersonnelLeaveById(id);
        }
        List<ApprovalProcess> processList = approvalProcessService.getApprovalProcessByBusiness(leave.getId(),3,null);

        if (!"".equals(leave.getActualState()) && leave.getActualState()!=null && "1".equals(leave.getActualState())){
            personnelLeaveItems = userMessageService.getPersonnelLeaveItemByLeaveId(leave.getId());
            for (PersonnelLeaveItem p:personnelLeaveItems) {
                p.setProcessList(approvalProcessService.getApprovalProcessByBusiness(p.getId(),8,null));
            }

            if (personnelLeaveItems.size()>0){
                PersonnelLeaveItem personnelLeaveItem = personnelLeaveItems.get(0);
                if (personnelLeaveItem.getActualEndTime().before(new Date()) || !userId.equals(leave.getUser_())){
                    leave.setButtonStatus(1);
                }
            }
        }
        map.put("personnelLeave", leave);
        map.put("processList", processList);//审批流程列表
        map.put("personnelLeaveItems", personnelLeaveItems);//提前结束请假审批流程列表
        return map;
    }

    @Override
    public PersonnelLeaveItem addPersonnelLeaveItem(PersonnelLeave personnelLeave,String endTime1,String actualReason,User user) throws ParseException {

        personnelLeave.setActualState("1");
        personnelLeave.setApproveStatus("1");
        personnelLeaveDao.update(personnelLeave);

        Date endTime = NewDateUtils.dateFromString(endTime1,"yyyy-MM-dd HH:mm:ss");
        PersonnelLeaveItem personnelLeaveItem = new PersonnelLeaveItem();
        personnelLeaveItem.setApproveStatus("1");
        personnelLeaveItem.setActualState("1"); //实际请假状态:0-按计划完成,1-提前消假
        personnelLeaveItem.setActualType(personnelLeave.getType());//请假类型:1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他
        personnelLeaveItem.setActualReason(actualReason);  //说明
        personnelLeaveItem.setActualBeginTime(personnelLeave.getBeginTime()); //开始上班时间
        personnelLeaveItem.setActualEndTime(endTime);  //计划上班时间
        personnelLeaveItem.setLeave(personnelLeave);
        personnelLeaveItem.setCreateName(user.getUserName());
        personnelLeaveItem.setCreateDate(new Date());
        personnelLeaveItem.setCreator(user.getUserID());
        personnelLeaveItem.setAuditDate(new Date());
        personnelLeaveItemDao.save(personnelLeaveItem);
        return personnelLeaveItem;
    }

    @Override
    public void delayCallPersonnelLeaveItem(Integer id) {
        System.out.println("提前结束请假自动驳回开始");

        PersonnelLeaveItem personnelLeaveItem=personnelLeaveItemDao.get(id);
        if ("1".equals(personnelLeaveItem.getApproveStatus())) {
            String reason = "该申请未被及时审批";
            personnelLeaveItem.setApproveStatus("3");
            personnelLeaveItem.setApplyMemo(reason);//驳回原因
            personnelLeaveItem.setAuditDate(personnelLeaveItem.getActualEndTime());
            userMessageService.updatePersonnelLeaveItem(personnelLeaveItem);

            PersonnelLeave personnelLeave = personnelLeaveDao.get(personnelLeaveItem.getLeaveId());
            personnelLeave.setApproveStatus("2");  //将申请提前请假后的审批状态“1”，变成“2”已批准
            personnelLeave.setAuditDate(personnelLeaveItem.getActualEndTime());
            userMessageService.updatePersonnelLeave(personnelLeave);

            //给申请人 待处理推
            this.leaveRejectSend(0,-1,personnelLeave,personnelLeave.getUser_(),"/applyLeaveHandle",null,null,"applyLeave");

            //给申请人 已批准推
            this.leaveRejectSend(1,1,personnelLeave,personnelLeave.getUser_(),"/applyLeaveApproval",null,null,"applyLeave");

            //给申请人 发我的消息
            userSuspendMsgService.saveUserSuspendMsg(1, "您的提前结束请假申请被驳回了！", "驳回时间  " + NewDateUtils.dateToString(personnelLeaveItem.getActualEndTime(),"yyyy-MM-dd HH:mm:ss"), personnelLeave.getUser_(),"applyLeaveDetail",personnelLeave.getId());

            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(personnelLeaveItem.getId(),8,"1");  //未批准的
            for (ApprovalProcess approvalProcess : approvalProcessList) {
                if ("1".equals(approvalProcess.getApproveStatus())) {
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setReason(reason);
                    approvalProcess.setHandleTime(personnelLeaveItem.getActualEndTime());
                    approvalProcess.setUserName("系统");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    //给审批者 待处理推
                    this.leaveRejectSend(-1,-1,personnelLeave,approvalProcess.getToUser(),"/approvalLeaveHandle",null,null,"approvalLeave");
                }else if ("2".equals(approvalProcess.getApproveStatus())) {

                    //给审批者 已批准推
                    this.leaveRejectSend(0,-1,personnelLeave, approvalProcess.getToUser(), "/approvalLeaveApproval",null,null,"approvalLeave");
                }
            }
        }
        System.out.println("提前结束请假自动驳回结束");

    }

    @Override    //type 1-计划请假申请 2-提前结束请假
    public void approvalLeaveApproval(ApprovalProcess approvalProcess,User user,PersonnelLeave leave,Integer type,PersonnelLeaveItem personnelLeaveItem,Integer id) {
        approvalProcess.setApproveStatus("2");
        approvalProcess.setHandleTime(new Date());
        approvalProcess.setUserName(user.getUserName());//审批人名字
        approvalProcessService.updateApprovalProcess(approvalProcess);
        String  count="";
        //找到请假审批项
        ApprovalItem approvalItem  = approvalService.getById(leave.getApproveItem());
        ApprovalFlow thisFlow=approvalService.getApprovalFlowByItemIdAndLevel(approvalItem.getId(),approvalProcess.getLevel());

        //总级别<=当前审批级别 或者 请假时长<=当前审批人审批时限  直接出结果，不再往下提交
        if (approvalItem.getLevel()<=approvalProcess.getLevel()||leave.getDuration()<=thisFlow.getAmountCeiling()){
            leave.setApproveStatus("2");
            leave.setAuditorName(user.getUserName());
            leave.setAuditDate(new Date()); //最后的审批日期
//                    leave.setApproveExplain(reason);  //批准说明

            if (type==2){
                count="您提前结束请假的申请被批准了！";
                if (personnelLeaveItem.getId()!=null){
                    personnelLeaveItem.setApproveStatus("2");
                    personnelLeaveItem.setAuditDate(new Date());  //最后的审批日期
                    personnelLeaveItem.setAuditorName(user.getUserName());
                    userMessageService.updatePersonnelLeaveItem(personnelLeaveItem);

                    leave.setActualEndTime(personnelLeaveItem.getActualEndTime());  //将计划上班时间更新到请假表的实际截止日期
                    String sdf = "yyyy-MM-dd HH:mm:ss";
                    leave.setActualDuration(this.getDuration(leave.getBeginTime(),personnelLeaveItem.getActualEndTime()));  //实际时长
                    leave.setApproveDuration(this.getDuration(leave.getBeginTime(),personnelLeaveItem.getActualEndTime()));  //批准时长
                    leave.setActualReason(personnelLeaveItem.getActualReason());

                    //将当天提前结束请假添加到考勤中
//                    workAttendanceOldService.leaveOvertimeToAttendance(3,leave.getUser_(),user.getUserID(),leave.getId(),leave.getType(),leave.getLeaveType(),leave.getActualDuration(),leave.getActualBeginTime(),personnelLeaveItem.getActualEndTime(),leave.getReason());  //将当天提前结束请假添加到考勤中
                    workAttendanceService.setAttendanceUserEvent(getToUpdateEvent(leave.getId()));
                }
            }else if (type==1){
                count="您的请假申请被批准了！";
                Integer toAttendanceType = 2;   //普通请假
                if (leave.getKind()!=null&&2==leave.getKind()){
                    toAttendanceType = 4;  //补报请假
                }
//                workAttendanceOldService.leaveOvertimeToAttendance(toAttendanceType,leave.getUser_(),user.getUserID(),leave.getId(),leave.getType(),leave.getLeaveType(),leave.getActualDuration(),leave.getActualBeginTime(),leave.getActualEndTime(),leave.getReason());  //将当天请假添加到考勤中
                workAttendanceService.setAttendanceUserEvent(getToUpdateEvent(leave.getId()));
            }
            userMessageService.updatePersonnelLeave(leave);//结果

            //给申请人待处理的发送
            this.leaveRejectSend(0,-1,leave,leave.getUser_(),"/applyLeaveHandle",null,null,"applyLeave");
            //给申请人已批准发送
            this.leaveRejectSend(1,1,leave, leave.getUser_(), "/applyLeaveApproval",count,count,"applyLeave");

            //已批准 到请假结束时间 进查询
            IncomingQueryLeave incomingQueryLeave=new IncomingQueryLeave(leave.getId());
            clusterMessageSendingOperations.delayCall(leave.getActualEndTime(),incomingQueryLeave);

        }else{
            //否则向下一级审批者提交此申请
            Integer handleId=null;
            String handleName="";//审批人总称
            String userName="";//审批人姓名
            Integer uid=null;
            Integer level=approvalProcess.getLevel()+1;
            for (ApprovalFlow f:approvalItem.getApprovalFlowHashSet()){
                if (f.getLevel()==level){
                    uid=f.getToUserId();
                    handleName=f.getToUser();//审批人总称
                    userName=f.getUserName();
                }
            }
            if (0==uid){
                handleId=Integer.decode(user.getLeader());
//                        handleName="直接上级";
                userName=userService.getUserByID(Integer.decode(user.getLeader())).getUserName();
            }else {
                handleId=uid;
//                        handleName="指定审批人";
            }
            ApprovalProcess app=new ApprovalProcess();
            app.setApproveStatus("1");
            app.setBusiness(id);
            if (approvalProcess.getBusinessType()!=null && approvalProcess.getBusinessType()==3){
                app.setBusinessType(3);//业务类型  1-财务修改 2-计划加班 3-请假 4-入库 5-投诉 6-申报加班,7-审批流程更改 8-提前结束请假
            }else {
                app.setBusinessType(8);
            }
            app.setToUser(handleId);
            app.setLevel(level);
            app.setToUserName(handleName);//审批人总称
            app.setUserName(userName);//审批人名称
            app.setCreateDate(new Date());
            app.setDescription(approvalProcess.getDescription());
            app.setHandleTime(new Date());
            app.setFromUser(leave.getUser().getUserID());//申请人 id
            app.setAskName(leave.getUser().getUserName());//申请人名
            app.setOrg(user.getOid());
            approvalProcessService.saveApprovalProcess(app);

            if (type==1){
                count=leave.getUser().getUserName()+"提交了请假申请";
            }else {
                count=leave.getUser().getUserName()+"提交了提前结束请假申请";
                this.leaveRejectSend(0,-1,leave, handleId, "/approvalLeaveApproval",null,null,"approvalLeave");//下一级审批人已批准减数据
            }

            //给下一级审批者 待处理推
            this.leaveRejectSend(1,1,leave, handleId, "/approvalLeaveHandle",count,count,"approvalLeave");

        }
    }

    @Override   //type 1-计划请假申请 2-提前结束请假
    public void approvalLeaveDisapproval(ApprovalProcess approvalProcess,User user,Integer type,PersonnelLeave leave,PersonnelLeaveItem personnelLeaveItem,String reason) {
        approvalProcess.setApproveStatus("3");
        approvalProcess.setHandleTime(new Date());
        approvalProcess.setUserName(user.getUserName());
        approvalProcess.setReason(reason);  //驳回理由
        approvalProcessService.updateApprovalProcess(approvalProcess);

        if (type==1){   //1-计划请假申请
            leave.setApproveStatus("3");
            leave.setAuditorName(user.getUserName());
            leave.setAuditDate(new Date());  //最后的审批日期
            leave.setApplyMemo(reason);  //驳回理由
            userMessageService.updatePersonnelLeave(leave);//结果
        }else {   //2-提前结束请假
            leave.setApproveStatus("2");
            leave.setActualState("0");//还原提前结束请假标识
            leave.setAuditDate(new Date());  //最后的审批日期
            userMessageService.updatePersonnelLeave(leave);//结果
            if (personnelLeaveItem.getId()!=null){
                personnelLeaveItem.setApproveStatus("3");
                personnelLeaveItem.setAuditDate(new Date());  //最后的审批日期
                personnelLeaveItem.setAuditorName(user.getUserName());
                personnelLeaveItem.setApplyMemo(reason);//驳回理由
                userMessageService.updatePersonnelLeaveItem(personnelLeaveItem);
            }
        }
    }

    //    @Override
    public Map<String,Object> getApplicantLeave(Integer userId,Integer state,String begin,String end,Map<String,Object> map) throws ParseException {
        SimpleDateFormat sdff = new SimpleDateFormat("yyyy-MM-dd");
        Integer totalNum = 0;  //请假总次数
        Integer personalNum = 0; //事假
        Integer sickNum = 0;  //病假
        Integer yearNum = 0;  //年假
        Integer offNum = 0;  //调休
        Integer marriageNum = 0; //婚假
        Integer maternityNum = 0;  //产假
        Integer paternityNum = 0;  //陪产假
        Integer roadNum = 0;  //路途假
        Integer otherNum = 0; //其它假
        Date begin1 = null;
        Date end1 = null;

        List<String> dateList = new ArrayList<>();  //存日期
        if (state!=3){
            end1 = new Date();
            end = sdff.format(end1);
            if (state==1){   //本月
                begin1 = DateUtils.thisMonthFirstDate();  //当月第一天
                begin = sdff.format(begin1);
            }else if (state==2){  //本年
                begin1 = DateUtils.aYearFirstDay(new Date());  //本年第一天
                begin = sdff.format(begin1);
                dateList = DateUtils.dateList(begin,end);
            }

        }else {
            dateList = DateUtils.dateList(begin,end);
        }

        List<PersonnelLeave> leaveList=this.getPersonnelLeaveListByBeginTime(userId,"2",null,begin,end);  //请假申请的开始时间和结束时间在这段时间的数据
        for (PersonnelLeave personnelLeave:leaveList) {
            if (!"".equals(personnelLeave.getType()) && personnelLeave.getType()!=null) {
                switch (personnelLeave.getType()){     //1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他'
                    case "1":
                        personalNum = personalNum + 1;
                        break;
                    case "2":
                        sickNum = sickNum + 1;
                        break;
                    case "3":
                        yearNum = yearNum + 1;
                        break;
                    case "4":
                        offNum = offNum + 1;
                        break;
                    case "5":
                        marriageNum = marriageNum + 1;
                        break;
                    case "6":
                        maternityNum = maternityNum + 1;
                        break;
                    case "7":
                        paternityNum = paternityNum + 1;
                        break;
                    case "8":
                        roadNum = roadNum + 1;
                        break;
                    case "9":
                        otherNum = otherNum + 1;
                        break;
                }
            }
        }
        totalNum = leaveList.size();
        map.put("leaveList",leaveList);  //请假列表
        map.put("beginTime",begin);  //开始时间
        map.put("endTime",end);  //结束时间
        map.put("totalNum",totalNum);  //请假总次数
        map.put("personalNum",personalNum);//事假
        map.put("sickNum",sickNum);//病假
        map.put("yearNum",yearNum);//年假
        map.put("offNum",offNum);//调休
        map.put("marriageNum",marriageNum);//婚假
        map.put("maternityNum",maternityNum);//产假
        map.put("paternityNum",paternityNum);//陪产假
        map.put("roadNum",roadNum);//路途假
        map.put("otherNum",otherNum);//其它假
        map.put("dateList",dateList);   //跨年或者跨月时的日期
        return map;
    }

//    @Override
    public Map<String, Object> getApproverLeave(Integer userId,Integer state,String begin,String end,Map<String,Object> map1) throws ParseException {
        SimpleDateFormat sdff = new SimpleDateFormat("yyyy-MM-dd");
        //此审批人共审批了多少的各种类型的请假次数
        Integer userNum = 0;  //请假总人数
        Integer totalNum = 0;  //请假总次数
        Integer personalNum = 0; //事假
        Integer sickNum = 0;  //病假
        Integer yearNum = 0;  //年假
        Integer offNum = 0;  //调休
        Integer marriageNum = 0; //婚假
        Integer maternityNum = 0;  //产假
        Integer paternityNum = 0;  //陪产假
        Integer roadNum = 0;  //路途假
        Integer otherNum = 0; //其它假
        List<PersonnelLeave> leaveList = new ArrayList<>();
        List<PersonnelLeave> personnelLeaveList = new ArrayList<>();
        List<Map<String,Object>> listMap = new ArrayList<>();

        Date begin1 = null;
        Date end1 = null;
        List<String> dateList = new ArrayList<>();  //存日期
        if (state!=3){  //当state为3时，则为自定义
            end1 = new Date();
            end = sdff.format(end1);
            if (state==1){   //本月
                begin1 = DateUtils.thisMonthFirstDate();  //当月第一天
                begin = sdff.format(begin1);
            }else if (state==2){  //本年
                begin1 = DateUtils.aYearFirstDay(new Date());  //本年第一天
                begin = sdff.format(begin1);
                dateList = DateUtils.dateList(begin,end);
            }
            if (state==4){  //昨日
                begin1 = NewDateUtils.yesterday();
                begin = sdff.format(begin1);
                end1 = begin1;
            }

        }else {    //自定义
            dateList = DateUtils.dateList(begin,end);
        }

        leaveList = this.getPersonnelLeaveListByApproverId(null,userId,"2",3,begin,end,null);
        if (leaveList.size()>0){
            personnelLeaveList = removeDuplicateUser2(leaveList,2);  //去掉重复的申请人
            for (PersonnelLeave p:personnelLeaveList) {   //有多少人请假
                //某个人的各种请假类型的次数
                Integer totalNumOne = 0;  //请假总次数
                Integer personalNumOne = 0; //事假
                Integer sickNumOne = 0;  //病假
                Integer yearNumOne = 0;  //年假
                Integer offNumOne = 0;  //调休
                Integer marriageNumOne = 0; //婚假
                Integer maternityNumOne = 0;  //产假
                Integer paternityNumOne = 0;  //陪产假
                Integer roadNumOne = 0;  //路途假
                Integer otherNumOne = 0; //其它假
                Map<String,Object> map = new HashMap<>();
                List<PersonnelLeave> personnelLeaves = new ArrayList<>();    //某人的所有请假信息
                for (PersonnelLeave personnelLeave:leaveList) {
                    if (p.getUser_().equals(personnelLeave.getUser().getUserID())){  //相同的用户
                        if (!"".equals(personnelLeave.getType()) && personnelLeave.getType()!=null){

                            switch (personnelLeave.getType()){     //1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他'
                                case "1":
                                    personalNumOne = personalNumOne+1;
                                    personalNum = personalNum+1;
                                    break;
                                case "2":
                                    sickNumOne = sickNumOne+1;
                                    sickNum = sickNum+1;
                                    break;
                                case "3":
                                    yearNumOne = yearNumOne+1;
                                    yearNum = yearNum+1;
                                    break;
                                case "4":
                                    offNumOne = offNumOne+1;
                                    offNum = offNum+1;
                                    break;
                                case "5":
                                    marriageNumOne = marriageNumOne+1;
                                    marriageNum = marriageNum+1;
                                    break;
                                case "6":
                                    maternityNum = maternityNumOne+1;
                                    maternityNum = maternityNum+1;
                                    break;
                                case "7":
                                    paternityNumOne = paternityNumOne+1;
                                    paternityNum = paternityNum+1;
                                    break;
                                case "8":
                                    roadNumOne = roadNumOne+1;
                                    roadNum = roadNum+1;
                                    break;
                                case "9":
                                    otherNumOne = otherNumOne+1;
                                    otherNum = otherNum+1;
                                    break;
                            }
                        }
                        personnelLeaves.add(personnelLeave);
                    }
                }
                totalNumOne = personnelLeaves.size();
                map.put("totalNumOne",totalNumOne);//某人请假总次数
                map.put("personalNumOne",personalNumOne);//事假
                map.put("sickNumOne",sickNumOne);//病假
                map.put("yearNumOne",yearNumOne);//年假
                map.put("offNumOne",offNumOne);//调休
                map.put("marriageNumOne",marriageNumOne);//婚假
                map.put("maternityNumOne",maternityNumOne);//产假
                map.put("paternityNumOne",paternityNumOne);//陪产假
                map.put("roadNumOne",roadNumOne);//路途假
                map.put("otherNumOne",otherNumOne);//其它假
                map.put("PersonnelLeaves",personnelLeaves);  //某人的所有请假列表
                map.put("userName",p.getUser().getUserName());  //某人的信息
                listMap.add(map);
            }
            userNum = personnelLeaveList.size();  //去掉重复的申请人
            totalNum = leaveList.size(); //总的请假总次数
        }
        map1.put("leaveList",leaveList);  //所有的请假列表
        map1.put("listMap",listMap);  //某个人员的详细情况组成的列表
        map1.put("beginTime",begin);  //开始时间
        map1.put("endTime",end);  //结束时间
        map1.put("userNum",userNum);  //请假总人数
        map1.put("totalNum",totalNum);  //请假总次数
        map1.put("personalNum",personalNum);//事假
        map1.put("sickNum",sickNum);//病假
        map1.put("yearNum",yearNum);//年假
        map1.put("offNum",offNum);//调休
        map1.put("marriageNum",marriageNum);//婚假
        map1.put("maternityNum",maternityNum);//产假
        map1.put("paternityNum",paternityNum);//陪产假
        map1.put("roadNum",roadNum);//路途假
        map1.put("otherNum",otherNum);//其它假
        map1.put("dateList",dateList);   //跨年或者跨月时的日期
        return map1;
    }

    /**
     *<AUTHOR>
     *@date 2018/3/30 17:15
     *list中去除重复的id(在审批人请假查询中使用)
     * type 1-去掉重复的请假id 2-去掉重复的申请人id
     */
    private static ArrayList<PersonnelLeave> removeDuplicateUser2(List<PersonnelLeave> users,Integer type) {
        Set<PersonnelLeave> set = new TreeSet<PersonnelLeave>(new Comparator<PersonnelLeave>() {
            @Override
            public int compare(PersonnelLeave o1, PersonnelLeave o2) {
                //字符串,则按照asicc码升序排列
                if (type==1){
                    return o1.getId().compareTo(o2.getId());
                }else {
                    return o1.getUser_().compareTo(o2.getUser_());
                }
            }
        });
        set.addAll(users);
        return new ArrayList<PersonnelLeave>(set);
    }

//    @Override
    public List<PersonnelLeave> getRejectLeave(Integer userId, String beginTime,String endTime) throws ParseException {
        Map<String,Object> params = new HashMap<>();

        //获取提前结束请假最终为驳回的请假id
        String hql1 = "select a.leave_id from (select leave_id,MAX(id),approve_status from t_personnel_leave_item group by leave_id) as a where a.approve_status=3 ";
        List leaveIds = personnelLeaveItemDao.getIntegerListBySql(hql1);

        String hql = "";
        if (leaveIds.size()>0){
            hql = "from PersonnelLeave where (approveStatus=3 or id in (:leaveIds)) and actualEndTime>=:newdate1";
            params.put("leaveIds",leaveIds);
        }else {
            hql = "from PersonnelLeave where approveStatus=3";
        }
        params.put("newdate1",new Date());  //已驳回中的数据为请假未完成的

        if (userId!=null){
            hql+=" and user_=:userId";
            params.put("userId",userId);
        }
        if (!"".equals(beginTime) && beginTime!=null){
            hql+=" and createDate>=:beginTime";
            params.put("beginTime",NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd HH:mm:ss"));
        }
        if (!"".equals(endTime) && endTime!=null){
            hql+=" and createDate<=:endTime";
            params.put("endTime",NewDateUtils.dateFromString(endTime,"yyyy-MM-dd HH:mm:ss"));
        }
        hql+=" order by auditDate desc";
        List<PersonnelLeave> personnelLeaves = personnelLeaveDao.getListByHQLWithNamedParams(hql,params);
        return personnelLeaves;
    }

//    @Override    //在此的endTime为当前时间
    public List<PersonnelLeave> getApprovalRejectLeave(Integer userId,String beginTime,String endTime) {
        Map<String,Object> params = new HashMap<>();
        String hql1 = " select business from t_sys_approval_process where approve_status=3 and to_user="+userId+" and create_date>='"+beginTime+"' and create_date<='"+endTime+"' and business_type=3";  //计划请假驳回
        List<Integer> leaveIds = personnelLeaveItemDao.getIntegerListBySql(hql1);

        //提前结束请假驳回,已驳回中的数据为请假未完成的
        String hql2 = " select d.leaveId from (select a.leave_id as leaveId,MAX(a.id) as id,a.actual_end_time as actualEndTime from t_personnel_leave_item a,t_sys_approval_process b where a.id = b.business and b.approve_status=3 and b.to_user="+userId+" and b.create_date>='"+beginTime+"' and b.create_date<='"+endTime+"' and b.business_type=8 group by a.leave_id) as d where d.actualEndTime>='"+endTime+"'";
        List<Integer> leaveIds1 = personnelLeaveItemDao.getIntegerListBySql(hql2);

        List<Integer> listID = new ArrayList<>();
        listID.addAll(leaveIds);
        listID.addAll(leaveIds1);
        String hql = " from PersonnelLeave where id in (:listID) order by auditDate desc";
        params.put("listID",listID);
        List<PersonnelLeave> personnelLeaves = personnelLeaveDao.getListByHQLWithNamedParams(hql,params);

        return personnelLeaves;
    }

    @Override   //审批人 提前结束请假的待处理
    public List<PersonnelLeave> getPersonnelLeaveByApprovalAdvance(Integer toUserId, String approvalStatus, Integer businessType) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql = new StringBuffer(" from PersonnelLeave o where o.approveStatus=:approval");
        map.put("approval",approvalStatus);  //必须有
        if (toUserId!=null){
            hql.append(" and o.id in (select leaveId from PersonnelLeaveItem pli where pli.id in (select business from ApprovalProcess p where p.toUser=:toUserId");
            map.put("toUserId",toUserId);
            if (!"".equals(approvalStatus) && approvalStatus!=null){
                hql.append(" and p.approveStatus=:approvalStatus");
                map.put("approvalStatus",approvalStatus);  //必须有
            }

            if (businessType!=null){
                hql.append(" and p.businessType=:businessType");
                map.put("businessType",businessType);
            }
            hql.append("))");
        }

        hql.append(" order by o.beginTime asc");  //以请假开始时间顺序排列

        List<PersonnelLeave> personnelLeaves = personnelLeaveDao.getListByHQLWithNamedParams(hql.toString(),map,null);
        return personnelLeaves;
    }

    @Override
    public List<PersonnelLeave> getPersonnelLeaveListQuery(Integer userId, Integer approveStatus, Date beginDate, Date endDate) {
        String hql=" from PersonnelLeave where user_=:userId and approveStatus=:approveStatus";
        if (approveStatus==3){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
        }else {
            hql+=" and actualBeginTime<:endDate and actualEndTime>:beginDate and actualEndTime<=:endDate";
        }
        Map<String,Object> map = new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus.toString());
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        List<PersonnelLeave> personnelLeaveList=personnelLeaveDao.getListByHQLWithNamedParams(hql,map);
        return personnelLeaveList;
    }

    public List<LeaveQuery> initLeaveQuery(){
        List<LeaveQuery> neiList = Arrays.asList(
                new LeaveQuery(-1,"事假"),
                new LeaveQuery(-2,"病假"),
                new LeaveQuery(-3,"婚假"),
                new LeaveQuery(-4,"丧假"),
                new LeaveQuery(-5,"产假"));
        return neiList;
    }

    @Override
    public List<LeaveQuery> getLeaveStatistics(List<PersonnelLeave> personnelLeaveList,Integer oid) {
        List<LeaveQuery> neiList=this.initLeaveQuery();   //系统默认的请假类型【默认请假类型id默认为负值】
        List<LeaveQuery> neiList1=new ArrayList<>(); //自定义的所有请假类型
        List<PersonnelLeaveType> personnelLeaveTypesCustomize = leaveTypeService.getLeaveTypes(oid,null);  //自定义的所有请假类型
        for (PersonnelLeaveType p:personnelLeaveTypesCustomize) {  //可能会和上面默认的id重复
            neiList1.add(new LeaveQuery(p.getId(),p.getName()));
        }

        for (PersonnelLeave personnelLeave:personnelLeaveList){
            if (personnelLeave.getType()!=null){   //请假类型默认的请假
                Integer typeDefaule = Integer.valueOf(personnelLeave.getType());
                typeDefaule = -typeDefaule;
                if (typeDefaule.equals(neiList.get(0).getId())){
                    neiList.get(0).addPersonnelLeaves(personnelLeave);
                    personnelLeave.setLeaveTypeName(neiList.get(0).getName());
                }else if (typeDefaule.equals(neiList.get(1).getId())){
                    neiList.get(1).addPersonnelLeaves(personnelLeave);
                    personnelLeave.setLeaveTypeName(neiList.get(1).getName());
                }else if (typeDefaule.equals(neiList.get(2).getId())){
                    neiList.get(2).addPersonnelLeaves(personnelLeave);
                    personnelLeave.setLeaveTypeName(neiList.get(2).getName());
                }else if (typeDefaule.equals(neiList.get(3).getId())){
                    neiList.get(3).addPersonnelLeaves(personnelLeave);
                    personnelLeave.setLeaveTypeName(neiList.get(3).getName());
                }else {   //neiList.get(4))的以及其他类型的
                    neiList.get(4).addPersonnelLeaves(personnelLeave);
                    personnelLeave.setLeaveTypeName(neiList.get(4).getName());
                }
            }else if (personnelLeave.getLeaveType()!=null){
                for (LeaveQuery n : neiList1) {
                    if (personnelLeave.getLeaveType().equals(n.getId())) {
                        n.addPersonnelLeaves(personnelLeave);
                        personnelLeave.setLeaveTypeName(n.getName());
                    }
                }
            }
        }

        for (LeaveQuery n : neiList) {
            n.SetCount();
        }
        for (LeaveQuery n : neiList1) {
            n.SetCount();
        }
        List arrList = new ArrayList(neiList);
        arrList.addAll(neiList1);
        return arrList;
    }

    @Override
    public List<LeaveQuery> getLeaveNum(User user, Integer approveStatus, Date beginDate, Date endDate) {
        List<LeaveQuery> neiList=this.initLeaveQuery();  //系统默认的
        String hql = "select count(*) as num,type from PersonnelLeave where user_=:userId and approveStatus=:approveStatus and type is not null";
        if (approveStatus==3){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
        }else {
            hql+=" and actualBeginTime>=:beginDate and actualEndTime<=:endDate";
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId",user.getUserID());
        map.put("approveStatus",approveStatus.toString());
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        List<Object[]> list = (List<Object[]>)personnelLeaveDao.getListByHQLWithNamedParams(hql+" group by type order by type",map);
        for (Object[] query : list) {
            Integer type1 = Integer.valueOf((String) query[1]);
            type1 = -type1;
            for (LeaveQuery n : neiList) {
                if (n.getId().equals(type1)) {
                    n.setCount((Long) query[0]);
                }
            }
        }

        //自定义请假类型的
        List<LeaveQuery> neiList1=new ArrayList<>(); //自定义的所有请假类型
        List<PersonnelLeaveType> personnelLeaveTypesCustomize = leaveTypeService.getLeaveTypes(user.getOid(),null);  //自定义的所有请假类型
        for (PersonnelLeaveType p:personnelLeaveTypesCustomize) {  //可能会和上面默认的id重复
            neiList1.add(new LeaveQuery(p.getId(),p.getName()));
        }
        String hql1 = "select count(*) as num,leaveType from PersonnelLeave where user_=:userId and approveStatus=:approveStatus and leaveType is not null";
        if (approveStatus==3){
            hql1+=" and createDate>=:beginDate and createDate<=:endDate";
        }else {
            hql1+=" and actualBeginTime>=:beginDate and actualEndTime<=:endDate";
        }
        HashMap<String, Object> map1 = new HashMap<>();
        map1.put("userId",user.getUserID());
        map1.put("approveStatus",approveStatus.toString());
        map1.put("beginDate",beginDate);
        map1.put("endDate",endDate);
        List<Object[]> list1 = (List<Object[]>)personnelLeaveDao.getListByHQLWithNamedParams(hql1+" group by leaveType order by leaveType",map1);
        for (Object[] query : list1) {
            for (LeaveQuery n : neiList1) {
                if (n.getId().equals(query[1])) {
                    n.setCount((Long) query[0]);
                }
            }
        }
        List arrList = new ArrayList(neiList);
        arrList.addAll(neiList1);
        return arrList;
    }


    @Override
    public List<PersonnelLeave> getApprovalLeaveListQuery(Integer userId, Integer approveStatus, Date beginDate, Date endDate,Integer applyUserId) {
        String hql=" from PersonnelLeave where approveStatus=:approveStatus and id in(select business from ApprovalProcess where businessType=3 and toUser=:userId)";
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus.toString());
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        if (approveStatus==3){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
        }else {
            hql+=" and :beginDate<actualEndTime and actualBeginTime<:endDate and actualEndTime<=:endDate";
        }
        if (applyUserId!=null){
            hql+=" and user_=:applyUserId";
            map.put("applyUserId",applyUserId);
        }
        List<PersonnelLeave> personnelLeaveList=personnelLeaveDao.getListByHQLWithNamedParams(hql,map);
        return personnelLeaveList;
    }

    @Override
    public List<LeaveQuery>  getApprovalNum(User user, Integer approveStatus, Date beginDate, Date endDate,Integer applyUserId) {
        //系统默认请假类型的
        List<LeaveQuery> neiList=this.initLeaveQuery();
        String hql = "select count(*) as num,type from PersonnelLeave where approveStatus=:approveStatus and type is not null and id in(select business from ApprovalProcess where businessType=3 and toUser=:userId)";
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId",user.getUserID());
        map.put("approveStatus",approveStatus.toString());
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        if (approveStatus==3){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
        }else {
            hql+=" and actualBeginTime>=:beginDate and actualEndTime<=:endDate";
        }
        if (applyUserId!=null){
            hql+=" and user_=:applyUserId";
            map.put("applyUserId",applyUserId);
        }
        List<Object[]> list = (List<Object[]>)personnelLeaveDao.getListByHQLWithNamedParams(hql+" group by type order by type",map);
        for (Object[] query : list) {
            Integer type1 = Integer.valueOf((String) query[1]);
            type1 = -type1;
            for (LeaveQuery n : neiList) {
                if (n.getId().equals(type1)) {
                    n.setCount((Long) query[0]);
                }
            }
        }

        //自定义请假类型的
        List<LeaveQuery> neiList1=new ArrayList<>(); //自定义的所有请假类型
        List<PersonnelLeaveType> personnelLeaveTypesCustomize = leaveTypeService.getLeaveTypes(user.getOid(),null);  //自定义的所有请假类型
        for (PersonnelLeaveType p:personnelLeaveTypesCustomize) {  //可能会和上面默认的id重复
            neiList1.add(new LeaveQuery(p.getId(),p.getName()));
        }
        String hqll = "select count(*) as num,leaveType from PersonnelLeave where approveStatus=:approveStatus and leaveType is not null and id in(select business from ApprovalProcess where businessType=3 and toUser=:userId)";
        HashMap<String, Object> map1 = new HashMap<>();
        map1.put("userId",user.getUserID());
        map1.put("approveStatus",approveStatus.toString());
        map1.put("beginDate",beginDate);
        map1.put("endDate",endDate);
        if (approveStatus==3){
            hqll+=" and createDate>=:beginDate and createDate<=:endDate";
        }else {
            hqll+=" and actualBeginTime>=:beginDate and actualEndTime<=:endDate";
        }
        if (applyUserId!=null){
            hqll+=" and user_=:applyUserId";
            map1.put("applyUserId",applyUserId);
        }
        List<Object[]> list1 = (List<Object[]>)personnelLeaveDao.getListByHQLWithNamedParams(hqll+" group by leaveType order by leaveType",map1);
        for (Object[] query : list1) {
            for (LeaveQuery n : neiList1) {
                if (n.getId().equals(query[1])) {
                    n.setCount((Long) query[0]);
                }
            }
        }
        List arrList = new ArrayList(neiList);
        arrList.addAll(neiList1);
        return arrList;
    }

    @Override
    public void incomingQueryLeave(Integer leaveId) {
        System.out.println("请假已批准进查询开始："+new Date());
        PersonnelLeave personnelLeave=personnelLeaveDao.get(leaveId);
        if(!"T".equals(personnelLeave.getState())){
            personnelLeave.setState("T");
            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(leaveId, 3, null);
            for (ApprovalProcess approvalProcess:approvalProcessList){
                //给审批人已批准 推
                this.leaveRejectSend(0,-1,personnelLeave, approvalProcess.getToUser(), "/approvalLeaveApproval",null,null,"approvalLeave");
            }
            //给申请人已批准 推
            this.leaveRejectSend(-1,-1,personnelLeave, personnelLeave.getUser_(), "/applyLeaveApproval",null,null,"applyLeave");
        }

        System.out.println("请假已批准进查询结束："+new Date());

    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code) {
            case "applyLeave": //请假申请
                result = this.getLeaveCounts(user.getUserID(), ApprovalStatus.approved.getName());//请假申请
                break;
        }
        return result;
    }

    @Override
    public Integer getPersonnelLeaveCountsByOid(Integer oid) {
        String hql="select count(id) from PersonnelLeave where user.oid=:oid and approveStatus!='3' and actualEndTime>=:actualEndTime";
        HashMap<String, Object> map = new HashMap<>();
        map.put("oid",oid);
        map.put("actualEndTime",new Date());
        Long counts=(long)personnelLeaveDao.getByHQLWithNamedParams(hql,map);
        return counts.intValue();
    }

    public boolean checkLeaveOcupiedByUserTime(Date beginTime, Date endTime, User user,List<String> approveStatuses, Integer excludeId) {
        StringBuffer hql = new StringBuffer("select 1 from PersonnelLeave where user_ = :userId and approveStatus in (:approveStatuses) and coalesce(actualBeginTime, beginTime) < :endTime and coalesce(actualEndTime, endTime) > :beginTime");
        Map<String, Object> params = new HashMap<String, Object>(4){{
            put("userId", user.getUserID());
            put("approveStatuses", approveStatuses);
            put("endTime", endTime);
            put("beginTime", beginTime);
        }};
        if (excludeId!=null) {
            hql.append(" and id != :excludeId");
            params.put("excludeId", excludeId);
        }
//        System.out.println(personnelLeaveDao.getByHQLWithNamedParams(hql, params));
        return personnelLeaveDao.getByHQLWithNamedParams(hql.toString(), params) != null;
    }

    @Override  //kind 1-普通 2-补报
    public Map<String, Object> addLeave(String beginTime, String endTime,Integer leaveType, String reason,Byte kind,User user,Double ruleTime,Map<String, Object> map) {
        Pair<Date, Date> beginEnd = sortDatePair(Pair.of(NewDateUtils.dateFromString(beginTime, sdf), NewDateUtils.dateFromString(endTime, sdf)));
        beginTime = NewDateUtils.dateToString(beginEnd.getLeft(), sdf);
        endTime = NewDateUtils.dateToString(beginEnd.getRight(), sdf);
        Date now = new Date(System.currentTimeMillis());
//        ApprovalItem approvalItemSupplementary = roleService.getCurrentItem(user.getOid(), "supplementaryLeave");  //补报请假的是否已关闭
//        if (kind!=null&&kind==2&&approvalItemSupplementary!=null&&!approvalItemSupplementary.isEnabled()){  //补报请假的,提交的时候按钮已关闭
        ApprovalItem approvalItemSupplementary;
        if (Byte.valueOf((byte) 2).equals(kind) && (approvalItemSupplementary = roleService.getCurrentItem(user.getOid(), "supplementaryLeave")) != null
                && !Boolean.TRUE.equals(approvalItemSupplementary.isEnabled())) {
            map.put("status",5);   //补报功能已关闭(和下面用一个状态)
            map.put("content", "操作失败，因为总务人员已修改了系统设置！");
        } else if (checkLeaveOcupiedByUserTime(beginEnd.getLeft(), beginEnd.getRight(), user, Arrays.asList(ApprovalStatus.pending.getName(), ApprovalStatus.approved.getName()), null)) {
            map.put("status",7);
            map.put("content", "操作失败，因为您选择的时间已有请假！");
        } else {
//            ApprovalItem approvalItemRule = roleService.getCurrentItem(user.getOid(), "leaveRule");  //获取请假时间规则(提前量)
//            if (kind!=null&&kind==1&&approvalItemRule!=null&&ruleTime!=null&&approvalItemRule.getUpperLimit().compareTo(new BigDecimal(ruleTime))!=0){
            ApprovalItem approvalItemRule;
            if (Kind.ordinary.getIndex().equals(kind)&&(approvalItemRule = roleService.getCurrentItem(user.getOid(), "leaveRule")) !=null && approvalItemRule.getUpperLimit() !=null && ruleTime !=null
//                    && approvalItemRule.getUpperLimit().doubleValue() != ruleTime.doubleValue()){
                    && approvalItemRule.getUpperLimit().compareTo(BigDecimal.valueOf(ruleTime).setScale(2))!=0){
                map.put("status",5);   //暂时先用一个状态，这个是修改了提前量
                map.put("content", "操作失败，因为总务人员已修改了系统设置！");
            }else {
                if (leaveType != null && leaveType != 0) {
                    ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "leaveApply");
                    Double duration = this.getDuration(beginEnd.getLeft(), beginEnd.getRight());   //请假时长
                    Integer dept = null;
                    if (StringUtils.isNotEmpty(user.getDepartment())) {
                        dept = Integer.valueOf(user.getDepartment());
                    }
                    //获取考勤的开始时间、结束时间、午休开始和结束时间与请假时间对比
                    Integer type = 2;
                    if (kind != null && kind == 2) {  //补假的
                        type = 5;
                    }
                    Integer stateTime = workAttendanceOldService.getStateTime(user.getOid(), dept, NewDateUtils.today(beginEnd.getLeft()), type, beginTime, endTime);
                    if (0 == stateTime) {
                        map.put("status", 4);  //您申请中的时间有些问题，请重新申请！
                        map.put("content", "您申请中的时间有些问题，请重新申请！");
                    } else if (1 == stateTime) {   //加班申请的时间在正常的范围之内
                        //审批状态为1则需要审批，去找审批流程
                        if (approvalItem.getStatus() == 1) {
                            if (approvalItem.getUpperLimit().doubleValue() >= duration || approvalItem.getUpperLimit().compareTo(new BigDecimal(-1)) == 0) {
                                PersonnelLeave personnelLeave1 = this.addPersonnelLeave(beginTime, endTime, leaveType, reason, kind, user, approvalItem.getId(), duration);  //保存后的请假
                                Integer handleId = approvalProcessService.addApplyApprovalProcess(personnelLeave1.getId(), 3, user, "请假申请", approvalItem);
                                if (1 == kind && beginEnd.getLeft().getTime() <= now.getTime()) {
                                    //给审批人的发送
                                    this.leaveRejectSend(1, 1, personnelLeave1, handleId, "/approvalLeaveHandle", null, null, "approvalLeave");
                                    this.overturn(personnelLeave1.getId());
                                } else {
                                    //给申请人的发送
                                    this.leaveRejectSend(0, 1, personnelLeave1, user.getUserID(), "/applyLeaveHandle", null, null, "applyLeave");
                                    //给审批人的发送
                                    this.leaveRejectSend(1, 1, personnelLeave1, handleId, "/approvalLeaveHandle", user.getUserName() + "提交了请假申请", user.getUserName() + "提交了请假申请", "approvalLeave");
                                    if (1 == kind) {  //普通请假的需要到时间自动驳回，补假的不需要
                                        //申请 到时间自动驳回
                                        OverturnLeave overturnLeave = new OverturnLeave(personnelLeave1.getId());
                                        clusterMessageSendingOperations.delayCall(personnelLeave1.getActualBeginTime(), overturnLeave);
                                    }
                                    System.out.println("addLeave.do  actualBeginTime: " + personnelLeave1.getActualBeginTime().getTime());
                                }
                                map.put("status", 1);//成功
                                map.put("content", "申请成功");
                            } else {
                                map.put("status", 2);  //高过审批人的审批时长
                                BigDecimal upperLimit = approvalItem.getUpperLimit();
                                if (upperLimit == null) {
                                    map.put("content", "公司规定：请假最高不得超过" + 1 + "天!");
                                } else if (upperLimit.doubleValue() >= new BigDecimal(24).doubleValue()) {
                                    Double dayNum = upperLimit.doubleValue() / 24;
                                    map.put("content", "公司规定：请假最高不得超过" + dayNum + "天!");
                                } else {
                                    map.put("content", "公司规定：请假最高不得超过" + approvalItem.getUpperLimit() + "小时!");
                                }
                            }
                        }
                    }
                } else {
                    map.put("status", 0);//选择请假类型
                    map.put("content", "请选择请假类型");
                }
            }
        }
        return map;
    }

    @Override
    public String getLeaveTypeName(Integer leaveTypeId,String type) {
        String leaveTypeName = null;
        if (leaveTypeId!=null&&leaveTypeId>0){
            PersonnelLeaveType leaveType = leaveTypeDao.get(leaveTypeId);
            leaveTypeName = leaveType.getName();
        }else if (!MyStrings.nulltoempty(type).isEmpty()){  //1-事假,2-病假,3-婚假,4-丧假,5-产假(1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-丧假')
            switch (type){
                case "1":
                    leaveTypeName = "事假";
                    break;
                case "2":
                    leaveTypeName = "病假";
                    break;
                case "3":
                    leaveTypeName = "婚假";
                    break;
                case "4":
                    leaveTypeName = "丧假";
                    break;
                case "5":
                    leaveTypeName = "产假";
                    break;
            }
        }
        return leaveTypeName;
    }

    @Override
    public JsonResult progressiveApprovalLeave(Integer approvalProcessId, String approvalStatus, Integer type, String reason, User user, String sdf) {
        Integer status = 0;
        Map<String, Object> map = this.getProgressiveApprovalLeaveContext(approvalProcessId, approvalStatus, user);
        if(ObjectUtils.isNotEmpty(map) && Integer.valueOf(1).equals(status = (Integer) map.getOrDefault("status", 0))) {
            ApprovalProcess approvalProcess = (ApprovalProcess) map.get("approvalProcess");
            PersonnelLeaveItem personnelLeaveItem = (PersonnelLeaveItem) map.get("personnelLeaveItem");
            PersonnelLeave leave = (PersonnelLeave) map.get("leave");
            Integer business = (Integer) map.get("business");
            if (ApprovalStatus.withdraw.getName().equals(approvalStatus)) {//撤销
                if (leave != null) {
                    if("1".equals(leave.getActualState())) {
                        leave.setActualState("0");//还原提前结束请假标识
                        leave.setApproveStatus("2");
                    } else {
                        leave.setApproveStatus("9");
                    }
                    personnelLeaveDao.update(leave);
                }
                if (personnelLeaveItem != null) {
                    personnelLeaveItem.setApproveStatus("9");
                }
                approvalProcess.setApproveStatus(ApprovalStatus.withdraw.getName());
                approvalProcessService.updateApprovalProcess(approvalProcess);
                updateApprovalProcessUsersBadgeNumber(popedomTaskService, userService, approvalProcess);
            } else {
                if ("1".equals(approvalStatus)) {
                    System.out.println("批准请假开始：" + new Date());
                    approvalLeaveApproval(approvalProcess, user, leave, type, personnelLeaveItem, business);   //审批请假批准
                    //给审批者 已批准推
                    leaveRejectSend(0, 1, leave, user.getUserID(), "/approvalLeaveApproval", null, null, "approvalLeave");
                    System.out.println("批准请假结束：" + new Date());

                } else {   //否则皆为驳回
                    System.out.println("驳回请假开始：" + new Date());

                    approvalLeaveDisapproval(approvalProcess, user, type, leave, personnelLeaveItem, reason);  //审批请假驳回

                    //给申请人待处理的发送
                    leaveRejectSend(0, -1, leave, leave.getUser_(), "/applyLeaveHandle", null, null, "applyLeave");

                    // 新增一条提示消息,同时发送给接收消息的人
                    String content = "";
                    if (type == 1) {
                        content = "您的请假申请被驳回了！";
                    } else {
                        content = "您的提前结束请假申请被驳回了！";
                        //给申请人已批准发送
                        leaveRejectSend(1, 1, leave, leave.getUser_(), "/applyLeaveApproval", null, null, "applyLeave");

                    }
                    userSuspendMsgService.saveUserSuspendMsg(1, content, "驳回时间  " + NewDateUtils.dateToString(new Date(), sdf), leave.getUser_(), "applyLeaveDetail", leave.getId());

                    if (type == 1) {
                        List<ApprovalProcess> processList = approvalProcessService.getApprovalProcessByBusiness(business, 3, null);  //计划请假
                        for (ApprovalProcess ap : processList) {
                            //给审批人的已批准发送
                            leaveRejectSend(0, -1, leave, ap.getToUser(), "/approvalLeaveApproval", null, null, "approvalLeave");
                        }
                    }
                    System.out.println("驳回请假结束：" + new Date());

                }
                //给审批人待处理的发送
                leaveRejectSend(-1, -1, leave, user.getUserID(), "/approvalLeaveHandle", null, null, "approvalLeave");
            }
        }
        return new JsonResult(1,status);
    }

    private Map<String, Object> getProgressiveApprovalLeaveContext(Integer approvalProcessId, String approvalStatus, User user) {
        Integer status = 0;
        ApprovalProcess approvalProcess;
        Map<String, Object> result = new HashMap<>(4);
        if (approvalProcessId!=null && (approvalProcess=approvalProcessService.getApprovalProcessById(approvalProcessId))!=null) {
            Triple<PersonnelLeave, PersonnelLeaveItem, Integer> triple = this.getLeaveFromProcess(approvalProcess);
            Integer business = triple.getRight();
            PersonnelLeave leave = triple.getLeft();
            PersonnelLeaveItem personnelLeaveItem = triple.getMiddle();
            result.put("approvalProcess", approvalProcess);
            result.put("business", business);
            result.put("leave", leave);
            result.put("personnelLeaveItem", personnelLeaveItem);
            if(ApprovalStatus.withdraw.getName().equals(approvalStatus) && user.getUserID().equals(approvalProcess.getFromUser())) {
                if(approvalProcess.getApproveStatus().equals("1")) {
                    status = 1;
                } else {
                    status = 4;//已被批准或驳回
                }
            } else if (approvalProcess.getApproveStatus().equals("1") && user.getUserID().equals(approvalProcess.getToUser())) {
                status = 1;
            } else if (approvalProcess.getApproveStatus().equals("9")) {
                status = Integer.valueOf(ApprovalStatus.withdraw.getIndex());//已被撤销
            } else {
                status = 4;//已被批准或驳回
            }

        }
        result.put("status", status);
        return result;
    }
    private Triple<PersonnelLeave, PersonnelLeaveItem, Integer> getLeaveFromProcess(ApprovalProcess approvalProcess) {
        PersonnelLeave leave;
        PersonnelLeaveItem personnelLeaveItem = null;
        Integer business = approvalProcess.getBusiness();
        if (Integer.valueOf(3).equals(approvalProcess.getBusinessType())) {
            leave = userMessageService.getPersonnelLeaveById(approvalProcess.getBusiness());
        } else {
            personnelLeaveItem = userMessageService.getPersonnelLeaveItemById(approvalProcess.getBusiness());
            leave = personnelLeaveItem.getLeave();
        }
        return Triple.of(leave, personnelLeaveItem, business);
    }
}