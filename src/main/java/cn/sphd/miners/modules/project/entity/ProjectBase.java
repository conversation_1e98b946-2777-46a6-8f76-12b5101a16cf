package cn.sphd.miners.modules.project.entity;

import cn.sphd.miners.modules.generalAffairs.entity.PersonalEducation;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2018/5/31.
 * 项目表
 */
@Entity
@Table(name = "t_project_base")
public class ProjectBase {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="project_name"  , length=100 , nullable=true , unique=false)
    private String projectName;//项目名称

    @Column(name="category"  , length=100 , nullable=true , unique=false)
    private String category;//类别

    @Column(name="tag"  , length=100 , nullable=true , unique=false)
    private String tag;//标签

    @Column(name="code"  , length=100 , nullable=true , unique=false)
    private String code;//项目编号

    @Column(name="foundation"  , length=100 , nullable=true , unique=false)
    private String foundation;//立项依据

    @Column(name="principal"   , nullable=true , unique=false)
    private Integer principal;//项目负责人ID

    @Column(name="principal_name"  , length=100 , nullable=true , unique=false)
    private String principalName;//项目负责人姓名

    @Column(name="member"  , length=100 , nullable=true , unique=false)
    private String member;//小组成员

    @Column(name="current_situation"  , length=100 , nullable=true , unique=false)
    private String currentSituation;//项目现况

    @Column(name="proposal"  , length=200 , nullable=true , unique=false)
    private String proposal;//立项目的

    @Column(name="description"  , length=300 , nullable=true , unique=false)
    private String description;//项目简介

    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;//预计开始时间

    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;//预计结束时间

    @Column(name="profit_estimate"  , length=200 , nullable=true , unique=false)
    private String profitEstimate;//收益估算

    @Column(name="outlook"  , length=300 , nullable=true , unique=false)
    private String outlook;//相关展望

    @Column(name="archive"  , length=255 , nullable=true , unique=false)
    private String archive;//输出资料 

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注


    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;//状态:0-录入,1-立案待审批,2-立案驳回,3-立案通过,4-结案待审批,5-结案驳回,6-结案通过

    @Column(name="register_approve_time"   , nullable=true , unique=false)
    private Date registerApproveTime;//立案批准时间

    @Column(name="register_approver"   , nullable=true , unique=false)
    private Integer registerApprover;//立案批准人ID,目前均为核心人员

    @Column(name="register_reject_reason"  , length=255 , nullable=true , unique=false)
    private String registerRejectReason;//立案驳回原因

    @Column(name="processor"   , nullable=true , unique=false)
    private Integer processor;//处理人ID

    @Column(name="settle_type"  , length=1 , nullable=true , unique=false)
    private String settleType;//结案类型:1-通过,2- 驳回

    @Column(name="settle_opinion"  , length=255 , nullable=true , unique=false)
    private String settleOpinion;//结案意见

    @Column(name="settle_time_fact"   , nullable=true , unique=false)
    private Date settleTimeFact;//实际结案时间

    @Column(name="settler"   , nullable=true , unique=false)
    private Integer settler;//结案提交者

    @Column(name="settle_time"   , nullable=true , unique=false)
    private Date settleTime;//结案提交时间

    @Column(name="settle_reject_reason"  , length=255 , nullable=true , unique=false)
    private String settleRejectReason;//结案驳回原因

    @Column(name="settle_approver"   , nullable=true , unique=false)
    private Integer settleApprover;//结案批准人

    @Column(name="settle_approve_time"   , nullable=true , unique=false)
    private Date settleApproveTime;//结案批准时间

    @Column(name="initial_receipter"   , nullable=true , unique=false)
    private Integer initialReceipter;//初始立案人ID

    @Column(name="initial_handler"   , nullable=true , unique=false)
    private Integer initialHandler;//初始处理人ID

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;//创建人id  初始立案人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;//立案申请人

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;


    //与项目附件表
    @OneToMany (targetEntity=ProjectAttachment.class, fetch=FetchType.LAZY, mappedBy="projectBase", cascade=CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    @JsonIgnore
    private Set<ProjectAttachment> projectAttachmentHashSet = new HashSet<ProjectAttachment>();

    @Transient
    private String registerName;//立案人

    @Transient
    private String registerApproveName;//立案批准人

    @Transient
    private String settleName;//结案提交者

    @Transient
    private String settleApproveName;//结案批准人




    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFoundation() {
        return foundation;
    }

    public void setFoundation(String foundation) {
        this.foundation = foundation;
    }

    public Integer getPrincipal() {
        return principal;
    }

    public void setPrincipal(Integer principal) {
        this.principal = principal;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public String getMember() {
        return member;
    }

    public void setMember(String member) {
        this.member = member;
    }

    public String getCurrentSituation() {
        return currentSituation;
    }

    public void setCurrentSituation(String currentSituation) {
        this.currentSituation = currentSituation;
    }

    public String getProposal() {
        return proposal;
    }

    public void setProposal(String proposal) {
        this.proposal = proposal;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getProfitEstimate() {
        return profitEstimate;
    }

    public void setProfitEstimate(String profitEstimate) {
        this.profitEstimate = profitEstimate;
    }

    public String getOutlook() {
        return outlook;
    }

    public void setOutlook(String outlook) {
        this.outlook = outlook;
    }

    public String getArchive() {
        return archive;
    }

    public void setArchive(String archive) {
        this.archive = archive;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getRegisterApproveTime() {
        return registerApproveTime;
    }

    public void setRegisterApproveTime(Date registerApproveTime) {
        this.registerApproveTime = registerApproveTime;
    }

    public Integer getRegisterApprover() {
        return registerApprover;
    }

    public void setRegisterApprover(Integer registerApprover) {
        this.registerApprover = registerApprover;
    }

    public String getRegisterRejectReason() {
        return registerRejectReason;
    }

    public void setRegisterRejectReason(String registerRejectReason) {
        this.registerRejectReason = registerRejectReason;
    }

    public Integer getProcessor() {
        return processor;
    }

    public void setProcessor(Integer processor) {
        this.processor = processor;
    }

    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public String getSettleOpinion() {
        return settleOpinion;
    }

    public void setSettleOpinion(String settleOpinion) {
        this.settleOpinion = settleOpinion;
    }

    public Date getSettleTimeFact() {
        return settleTimeFact;
    }

    public void setSettleTimeFact(Date settleTimeFact) {
        this.settleTimeFact = settleTimeFact;
    }

    public Integer getSettler() {
        return settler;
    }

    public void setSettler(Integer settler) {
        this.settler = settler;
    }

    public Date getSettleTime() {
        return settleTime;
    }

    public void setSettleTime(Date settleTime) {
        this.settleTime = settleTime;
    }

    public String getSettleRejectReason() {
        return settleRejectReason;
    }

    public void setSettleRejectReason(String settleRejectReason) {
        this.settleRejectReason = settleRejectReason;
    }

    public Integer getSettleApprover() {
        return settleApprover;
    }

    public void setSettleApprover(Integer settleApprover) {
        this.settleApprover = settleApprover;
    }

    public Date getSettleApproveTime() {
        return settleApproveTime;
    }

    public void setSettleApproveTime(Date settleApproveTime) {
        this.settleApproveTime = settleApproveTime;
    }

    public Integer getInitialReceipter() {
        return initialReceipter;
    }

    public void setInitialReceipter(Integer initialReceipter) {
        this.initialReceipter = initialReceipter;
    }

    public Integer getInitialHandler() {
        return initialHandler;
    }

    public void setInitialHandler(Integer initialHandler) {
        this.initialHandler = initialHandler;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Set<ProjectAttachment> getProjectAttachmentHashSet() {
        return projectAttachmentHashSet;
    }

    public void setProjectAttachmentHashSet(Set<ProjectAttachment> projectAttachmentHashSet) {
        this.projectAttachmentHashSet = projectAttachmentHashSet;
    }

    public String getRegisterName() {
        return registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    public String getRegisterApproveName() {
        return registerApproveName;
    }

    public void setRegisterApproveName(String registerApproveName) {
        this.registerApproveName = registerApproveName;
    }

    public String getSettleName() {
        return settleName;
    }

    public void setSettleName(String settleName) {
        this.settleName = settleName;
    }

    public String getSettleApproveName() {
        return settleApproveName;
    }

    public void setSettleApproveName(String settleApproveName) {
        this.settleApproveName = settleApproveName;
    }
}
