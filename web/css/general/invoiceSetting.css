#invoiceSet i {
    color: #ed5565;
}

#invoiceSet {
    width: 665px
}

#invoiceSet span.item_i {
    margin-right: 20px;
}

#invoiceSet i.fa {
    color: #48cfad;
    margin-right: 5px;
}

#editeLogInfo {
    width: 525px
}

#editeLogInfo .bonceCon > div {
    padding: 10px 40px;
}

#invoiceSet .bonceCon > div {
    padding: 10px 40px;
    display: none;
}

#invoiceSet .bonceCon > div:hover {
    background: #eaeaea;
}

.bonceCon > div .ttl {
    display: inline-block;
    width: 305px;
}

#lvs {
    padding: 10px;
}

.lv_item {
    border: 1px solid #48cfad;
    line-height: 25px;
    display: inline-block;
    padding: 2px 5px;
    cursor: default;
    margin: 10px 10px 0 0;
}

.lv_gray {
    border: 1px solid #ccc;
}

.lv_control {
    float: right;
    width: 50px;
    text-align: center;
}

#addLv {
    cursor: default;
    border: 1px solid #f0f8ff;
}

#addLv:hover {
    border: 1px solid #219a54;
}
#stopLv , #stopLv2 {
    color: #337ab7;
    text-decoration: underline;
    cursor: pointer;
    display: inline-block;
    float: right;
}

#stopLv:hover , #stopLv2:hover{
    color: #3785c7;
}
#scanStopLvs td > div, #scanStopLvs2 td > div {
    height: 30px;
    margin: 0 -15px;
    border-bottom: 1px solid #ccc;
    line-height: 30px;
}

#scanStopLvs td > div:last-child, #scanStopLvs2 td > div:last-child {
    border-bottom: none;
}

#scanStopLvs , #scanStopLvs2{
    width: 900px;
}

.bounce_Fixed2 {
    position: fixed;
    z-index: 10002;
    background: rgba(100, 100, 100, 0.5);
}

#setCatoryCon , #setCatory1, #setCatory2, #setCatory3 {
    position: relative;
}

.mask {
    position: absolute;
    z-index: 1;
    background: rgba(100, 100, 100, 0.2);
    display: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
#editLog{ width: 610px;  }
#setCatory1, #setCatory2, #setCatory3 {
    display: inline-block; width: 150px; height:20px;
}













