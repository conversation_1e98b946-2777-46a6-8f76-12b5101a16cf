<template>
  <div id="potentialContact" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content">
        <table class="sect-table table">
          <thead>
          <tr>
            <td>名称</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>基本信息</td>
            <td>
              <span class="sect-blue" @click="jumpRecordList(1, 0)">修改记录</span>
            </td>
          </tr>
          <tr>
            <td>联系人</td>
            <td>
              <span class="sect-blue" @click="jumpDelData(22)">已被删除的数据</span>
            </td>
          </tr>
          <tr v-for="(contact, index) in details.contactsList" :key="index">
            <td>{{contact.name}}  {{(contact && contact.post).substr(0,10)}}</td>
            <td>
              <span class="sect-blue" @click="jumpRecordList(2, contact.id)">修改记录</span>
            </td>
          </tr>
          <tr>
            <td>访谈记录</td>
            <td>
              <span class="sect-blue" @click="jumpDelData(33)">已被删除的数据</span>
            </td>
          </tr>
          <tr v-for="(interview, index) in details.interviewList" :key="index">
            <td>访谈对象{{interview.interviewer | countNum}}</td>
            <td>
              <span class="sect-blue" @click="jumpRecordList(3, interview.id)">修改记录</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<script>
import { formatDate, countNum } from '../../../js/common'
export default {
  name: 'potentialMsgRecord',
  data () {
    return {
      loading: true,
      details: {
        contactsList: [],
        interviewList: []
      }
    }
  },
  filters: {
    formatDate, countNum
  },
  created () {
    this.getUpdateDetail()
  },
  methods: {
    getUpdateDetail: function () {
      let _this = this
      this.axios.post('../../../sale/getPotentialUpdatePageData.do', {
        'customerId': _this.$route.params.id
      }).then(function (response) {
        _this.loading = false
        _this.details = response.data
      }).catch(function (error) {
        console.log(error)
      })
    },
    jumpDelData: function (type) {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialDelList/${type}/${detailId}`
      })
    },
    jumpRecordList: function (tab, id) {
      let detailId = this.$route.params.id
      this.$router.push({
        path: `/potentialRecord/${detailId}/${id}/${tab}`
      })
    }
  }
}
</script>
