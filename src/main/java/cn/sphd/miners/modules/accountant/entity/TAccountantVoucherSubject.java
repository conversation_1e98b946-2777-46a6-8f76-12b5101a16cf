package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import javax.persistence.Basic;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by root on 17-3-24.
 */
@Alias("voucherSubject")
public class TAccountantVoucherSubject implements Serializable {
    private static final long serialVersionUID = 2L;
    private Integer id;
    private Integer reimburseBill;//个人报销的票据id
    private Integer reimburseBillItem;//个人报销的票据详情id
    private String memo;//个人报销的备注
    private String source;//0 - 手动录入的凭证，1 - 会计录入的凭证，2,3 - 系统自动选择的凭证

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getReimburseBill() {
        return reimburseBill;
    }

    public void setReimburseBill(Integer reimburseBill) {
        this.reimburseBill = reimburseBill;
    }

    public Integer getReimburseBillItem() {
        return reimburseBillItem;
    }

    public void setReimburseBillItem(Integer reimburseBillItem) {
        this.reimburseBillItem = reimburseBillItem;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Id
    @javax.persistence.Column(name = "id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    private Integer org;

    @Basic
    @javax.persistence.Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    private Integer voucherId;

    @Basic
    @javax.persistence.Column(name = "voucher_id")
    public Integer getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(Integer voucherId) {
        this.voucherId = voucherId;
    }

    private String acountDate;

    @Basic
    @javax.persistence.Column(name = "acount_date")
    public String getAcountDate() {
        return acountDate;
    }

    public void setAcountDate(String acountDate) {
        this.acountDate = acountDate;
    }

    private String subject;

    @Basic
    @javax.persistence.Column(name = "subject")
    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    private String direction;

    @Basic
    @javax.persistence.Column(name = "direction")
    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    private BigDecimal amount;

    @Basic
    @javax.persistence.Column(name = "amount")
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    private Integer subjectDetail;

    @Basic
    @javax.persistence.Column(name = "subject_detail")
    public Integer getSubjectDetail() {
        return subjectDetail;
    }

    public void setSubjectDetail(Integer subjectDetail) {
        this.subjectDetail = subjectDetail;
    }

    private Integer creator;

    @Basic
    @javax.persistence.Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    private String createName;

    @Basic
    @javax.persistence.Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    private String createDate;

    @Basic
    @javax.persistence.Column(name = "create_date")
    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    private Integer updator;

    @Basic
    @javax.persistence.Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    private String updateName;

    @Basic
    @javax.persistence.Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    private String updateDate;

    @Basic
    @javax.persistence.Column(name = "update_date")
    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    private Integer approveItem;

    @Basic
    @javax.persistence.Column(name = "approve_Item")
    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    private String approveStatus;

    @Basic
    @javax.persistence.Column(name = "approve_status")
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    private Integer approveLevel;

    @Basic
    @javax.persistence.Column(name = "approve_level")
    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    private Integer auditor;

    @Basic
    @javax.persistence.Column(name = "auditor")
    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    private String auditorName;

    @Basic
    @javax.persistence.Column(name = "auditor_name")
    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    private String auditDate;

    @Basic
    @javax.persistence.Column(name = "audit_date")
    public String getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(String auditDate) {
        this.auditDate = auditDate;
    }

    private String operation;

    @Basic
    @javax.persistence.Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    private String applyMemo;

    @Basic
    @javax.persistence.Column(name = "apply_memo")
    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    private String approveMemo;

    @Basic
    @javax.persistence.Column(name = "approve_memo")
    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    private Integer messageId;

    @Basic
    @javax.persistence.Column(name = "message_id")
    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    @Basic
    @javax.persistence.Column(name = "subject_names")
    private String subjectNames;

    public String getSubjectNames() {
        return subjectNames;
    }

    public void setSubjectNames(String subjectNames) {
        this.subjectNames = subjectNames;
    }

    private Integer billDetail;

    public Integer getBillDetail() {
        return billDetail;
    }

    public void setBillDetail(Integer billDetail) {
        this.billDetail = billDetail;
    }

    private String subjectName;

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TAccountantVoucherSubject that = (TAccountantVoucherSubject) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (org != null ? !org.equals(that.org) : that.org != null) return false;
        if (voucherId != null ? !voucherId.equals(that.voucherId) : that.voucherId != null) return false;
        if (acountDate != null ? !acountDate.equals(that.acountDate) : that.acountDate != null) return false;
        if (subject != null ? !subject.equals(that.subject) : that.subject != null) return false;
        if (direction != null ? !direction.equals(that.direction) : that.direction != null) return false;
        if (amount != null ? !amount.equals(that.amount) : that.amount != null) return false;
        if (subjectDetail != null ? !subjectDetail.equals(that.subjectDetail) : that.subjectDetail != null)
            return false;
        if (creator != null ? !creator.equals(that.creator) : that.creator != null) return false;
        if (createName != null ? !createName.equals(that.createName) : that.createName != null) return false;
        if (createDate != null ? !createDate.equals(that.createDate) : that.createDate != null) return false;
        if (updator != null ? !updator.equals(that.updator) : that.updator != null) return false;
        if (updateName != null ? !updateName.equals(that.updateName) : that.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(that.updateDate) : that.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(that.approveItem) : that.approveItem != null) return false;
        if (approveStatus != null ? !approveStatus.equals(that.approveStatus) : that.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(that.approveLevel) : that.approveLevel != null) return false;
        if (auditor != null ? !auditor.equals(that.auditor) : that.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(that.auditorName) : that.auditorName != null) return false;
        if (auditDate != null ? !auditDate.equals(that.auditDate) : that.auditDate != null) return false;
        if (operation != null ? !operation.equals(that.operation) : that.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(that.applyMemo) : that.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(that.approveMemo) : that.approveMemo != null) return false;
        if (messageId != null ? !messageId.equals(that.messageId) : that.messageId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (voucherId != null ? voucherId.hashCode() : 0);
        result = 31 * result + (acountDate != null ? acountDate.hashCode() : 0);
        result = 31 * result + (subject != null ? subject.hashCode() : 0);
        result = 31 * result + (direction != null ? direction.hashCode() : 0);
        result = 31 * result + (amount != null ? amount.hashCode() : 0);
        result = 31 * result + (subjectDetail != null ? subjectDetail.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        return result;
    }
}
