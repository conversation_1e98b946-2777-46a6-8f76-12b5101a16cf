<template>
  <div class="lyricDetails2">
    <Pageheader title="歌曲信息" :showBack="true"/>
    <div class="live-container">
      <div class="txtCenter">
        <div class="ttl">{{ playName }}</div>
        <div>
          {{ playTime }}
          <el-icon class="" >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div>
        <div class="tr"  @click="playLyric(2)">
          <span>查看已制作歌词的效果</span>
          <el-icon class="live-right " >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
        <hr>
        <div class="tr">
          <span>仅重新配节奏，歌词不变</span>
          <el-icon class="live-right " >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
        <div class="tr">
          <span>歌词与节奏都重新设置</span>
          <el-icon class="live-right " >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
        <hr>

      </div>
    </div>
    <LyricPlayer v-if="playLyc" :playName="playName" :lyricTxt="lyricTxt" :fileUrl="fileUrl" @closePlayLyc="closePlayLyc"></LyricPlayer>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import { getLyriTaskApplyMes } from '@/api/music.js'
import JSONBig from 'json-bigint'
import auth from '@/sys/auth'

const router = useRouter();
const playName = ref('')
const lyricTxt = ref('')
const playLyc = ref(false)
const playTime = ref('')
const fileUrl = ref('')
let songDetails = reactive('')

onMounted(() => {
  getDetails()
});
const playLyric = (type) => {
  let uploadUrl = localStorage.getItem('uploadUrl');
  if(songDetails.rhythmOption == 1){ // 原唱
    fileUrl.value = uploadUrl + songDetails.originalPath
  }else if(songDetails.rhythmOption == 2){ // 伴唱
    fileUrl.value = uploadUrl + songDetails.bgmPath
  }else {
    let path = songDetails.originalPath ? songDetails.originalPath : (songDetails.bgmPath ? songDetails.bgmPath : '')
    fileUrl.value = uploadUrl + path
  }
  console.log(' fileUrl.value =',  fileUrl.value )
  playLyc.value = true

}
const getDetails = () => {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('newlycdata'));
  console.log('lyricDetailsItem=', lyricDetailsItem)
  const userInfoSys = auth.getUser()
  let type = userInfoSys.roleCode === "staff" ? 2 : 1
  getLyriTaskApplyMes({
    'id': lyricDetailsItem.id,
    // 'isSelected': 0, // 成员提交的 查看详情
    // 'type': type
  }).then( res => {
    console.log('详情返回值=', res)
    const data = res.data
    localStorage.setItem('songMessage', JSONBig.stringify(data))
    const song = data.song
    songDetails = song
    console.log(' songDetails.value ',  songDetails )
    const cmwRhythm = data.cmwRhythm && data.cmwRhythm.rhythm
    lyricTxt.value = cmwRhythm
    playName.value = song.name
    playTime.value = song.songDuration


  }).catch(err => {
    console.log('获取小青失败', err)
  })
}
const closePlayLyc = (val)=> {
  console.log('父级的 closePlayLyc')
  playLyc.value = val
  let timer = localStorage.getItem('timer')
  console.log('取消 clearInterval(t', timer)
  clearInterval(timer)
}
// creator：hxz 播放音乐
const playMusic = ()=> {
  playLyc.value = true
}

const goPage = (url) => {
  router.push({ name: url});
}
</script>
<style lang="scss" scoped>
.lyricDetails2{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .tr{
    line-height: 50px;
  }
  .houseSty8{
    position: relative;
    top:3px;
  }
}
</style>
