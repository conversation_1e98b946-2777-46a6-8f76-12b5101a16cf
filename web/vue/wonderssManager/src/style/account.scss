@charset "utf-8";

@import "variable";


#today {
  height: 100%;
  display: flex;
  flex-direction: column;
}

span, p {
  //处理文本溢出 溢出文本用省略号代替
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.backgroundColoreee {
  background-color: #eeeeee;
}

.cai-liGray {
  background-color: $tyBounceColorGray;
}

.margin-10-15 {
  margin: 10px 20px;
}

.padding-r-5 {
  padding-right: 5px;
}

.padding-10-15 {
  padding: 10px 15px;
}

.padding-0-15 {
  padding: 0 15px;
}

.padding-0-10 {
  padding: 0 10px;
}

.padding-10-30 {
  padding: 10px 30px;
}

.padding_bottom_green {
  border-bottom: 2px solid $tyColorGreen;
  color: $tyColorGreen;
}

.padding_bottom_gray {
  border-bottom: 2px solid $tyColorGray;
}

.flex {
  display: flex;
  flex-direction: row;
  padding: 5px 20px;
  justify-content: space-between;
}


.ta-list {
  &>div{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 25px;
    width: 100%;
  }
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 25px;

  &>span {
    //处理文本溢出 溢出文本用省略号代替
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &>span:first-child {
    width: 40%;
  }

}


.val_list {
  display: block;
  //bottom: 10px;
  background-color: $tyBounceColorGray;
  height: 100%;
  flex-grow: 1;
  //position: relative;
  li {
    padding: 12px 20px;

    img {
      width: 20px;
      height: 20px;
    }
  }

  li:nth-child(2n) {
    background-color: #ffffff;
  }

  li:nth-child(2n-1) {
    background-color: #f4f4f4;
  }

}

.page {
  width: 100%;
  display: flex;
  color: #000;
  justify-content: center;
  align-items: center;
  bottom: 20px;

  p {
    text-align: center;
    padding-top: 10px;
    width: 300px;
    height: 30px;
    background-color: #fff;
    font-size: 16px;
  }
}

.hr_gray {
  width: 100%;
  height: 15px;
  background-color: #eee;
}

.current-balance {
  display: flex;
  justify-content: center;
  background-color: #ffffff;

  p {
    color: #000000;
    font-size: 14px;
    margin: 10px 0px;

    span {
      font-size: 26px;
      margin-left: 5px;
    }
  }
}

.current-balance-1 {
  display: flex;
  align-items: center;
  justify-content: center;

  > div {
    display: flex;
    align-items: end;
    flex-direction: column;
  }

  span {
    font-size: 28px;
    margin-left: 20px;
  }
}

.balance1 {
  width: auto;
  padding: 5px 0;
  //margin: 8px 20px;
  display: flex;
  > div {
    margin: 0 2px;
  }
  img{
    display: inline-block; width: 36px; height: 36px;
  }
}

.date-account {
  display: flex;
  justify-content: space-between;
  padding: 0px 20px;

  div {
    width: 40%;
    text-align: center;
    line-height: 40px;

    font-size: 18px;

  }
}

.xiangUlT {
  li:nth-child(1), li:nth-child(2) {
    background-color: $tyColorGray;

    p, span {
      color: $tyColorRed;
    }
  }

  li:nth-child(3), li:nth-child(4) {
    background-color: $tyColorGray;

    p, span {
      color: $tyColorGreen;
    }
  }

  li {
    line-height: 40px;
  }

  li:nth-child(6) {
    display: flex;
    align-items: center;
    justify-content: space-between;

    > div {
      display: flex;
      align-items: center;

      img {
        width: 20px;
      }
    }

  }
}

.xiangUlB {
  li {
    display: flex;
    justify-content: space-between;
    line-height: 35px;
  }

  .endLi {
    justify-content: end;
    line-height: 70px;
    //width: 55%;
    div {
      width: 60%;
      display: flex;
      justify-content: space-between;
      //span:nth-child(1),span:nth-child(2){
      //  width: 10%;
      //}
    }
  }

  .notes {
    div {
      width: 55%;
      line-height: 25px;
      /* text-align: justify; 文本两端对齐 */
      //hyphens: auto; /* 允许在单词内断行，并添加连字符 */
      //word-break: break-word;
      //text-justify: inter-word;
      overflow-wrap: break-word;
      word-wrap: break-word;
    }
  }

}

.employeeBox {
  width: 100%;
  //height: 45%;
  display: flex;
  justify-content: center;
  padding-top: 20px;

  .xiangUlB {
    width: 75%;
  }
}

.changeBox {
  width: 100%;
  box-sizing: border-box;
  padding: 5%;
  display: flex;
  flex-direction: column;

  p:last-child {
    display: flex;
    justify-content: end;
  }

  p, span {
    font-size: 16px;
    line-height: 30px;
  }
}

.emTableBox {
  flex-grow: 1;
  background-color: #f4f4f4;
}

.emTable {
  width: 100%;
  font-size: 14px;

  border-collapse: collapse;

  tr {
    line-height: 35px;
  }

  thead, tr:nth-child(2n) {
    background-color: #ffffff;
  }

  th:first-child, td:first-child {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 30px;
    border-left: none;
  }

  th:last-child,
  td:last-child {
    border-right: none;
  }

  th, td {
    border: 1px solid #cccccc;
  }

  td {

    text-align: center;

    > p {
      text-align: center;
      //width: 90%;
    }
  }
}

p, span {
  font-size: 14px;
  //color: inherit;
}

.accounting {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.head {
  //height: 50px;
  //padding-left: 20px;
  //display: flex;
  //align-items: center;
  //color: #ffffff;
  //background-color: $ty-color-green;
  //  font-weight: 100;
  //  font-size: 18px;
  //  justify-content: center;

  //.head {
  //  position: fixed;
  //  width: 100%;
  //  z-index: 999;
  //}
  > p {
    font-size: 16px;
    color: #fff;
  }

}

.h35f16 {
  line-height: 35px;
  font-size: 16px;
}

.box {
  background-color: $tyColorGray;
  height: 100%;
}

.sticky {
  position: sticky;
  top: 40px;
  z-index: 9999;
}

.spaceBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #e8e8e8;
  top: 40px;
  position: relative;
}

.ic {
  width: 10%;
  display: flex;
  justify-content: end;
}

.end-text {
  text-align: right;
}

.center-text {
  text-align: center;
}

.hH {
  word-break: break-all;
  white-space: normal;
}

.btnM{
  width: 40px;display: inline-block; text-align: right;
  i{color: #aaa; font-size: 20px; position: relative; top: 3px; }
}

.repItem{
  padding:16px; position: relative;
  &:nth-child(even){ background-color: #f4f4f4; }
  .cre{ margin-bottom: 10px; }
  table{ width: 90%; }
  .detal{ position: absolute; right: 16px; top:50px; font-size: 20px; }
  td{
    text-align: center; line-height: 26px;
  }
  .endLi{ text-align: right; margin-top: 5px; }
}

.padding1015{
  padding: 10px 15px; line-height: 30px;
}

.greenTxt{ color: $tyColorGreen; font-size: 18px; font-weight: bold; }

.ttlSalary{
  text-align: center; line-height: 40px; font-size: 22px;
  margin-top: 20px;
}

