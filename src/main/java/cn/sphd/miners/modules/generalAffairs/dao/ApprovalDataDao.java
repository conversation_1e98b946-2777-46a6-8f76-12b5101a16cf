package cn.sphd.miners.modules.generalAffairs.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.generalAffairs.entity.ApprovalData;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsMaintain;

import java.io.Serializable;

/**
 * @ClassName ApprovalDataDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/9 10:11
 * @Version 1.0
 */
public interface ApprovalDataDao extends IBaseDao<ApprovalData,Serializable> {
}
