package cn.sphd.miners.modules.resourceAuthority.entity;


import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("resAclLog")
public class ResAclLog {
  private Integer id;
  private Integer org;
  private Integer category;
  private Integer resource;
  private String operateType;
  private Integer grantUserNum;
  private Integer increasedNum;
  private Integer decreasedNum;
  private String increasedUsers;
  private String decreasedUsers;
  private Integer isLatest;
  private String path;
  private Integer creator;
  private String createName;
  private Date createDate;
  private Integer updator;
  private String updateName;
  private Date updateDate;
  private Integer messageId;
  private String operation;
  private Integer previousId;
  private Integer versionNo;

  private String name;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getCategory() {
    return category;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public void setCategory(Integer category) {
    this.category = category;
  }

  public Integer getResource() {
    return resource;
  }

  public void setResource(Integer resource) {
    this.resource = resource;
  }

  public String getOperateType() {
    return operateType;
  }

  public void setOperateType(String operateType) {
    this.operateType = operateType;
  }

  public Integer getGrantUserNum() {
    return grantUserNum;
  }

  public void setGrantUserNum(Integer grantUserNum) {
    this.grantUserNum = grantUserNum;
  }

  public Integer getIncreasedNum() {
    return increasedNum;
  }

  public void setIncreasedNum(Integer increasedNum) {
    this.increasedNum = increasedNum;
  }

  public Integer getDecreasedNum() {
    return decreasedNum;
  }

  public void setDecreasedNum(Integer decreasedNum) {
    this.decreasedNum = decreasedNum;
  }

  public String getIncreasedUsers() {
    return increasedUsers;
  }

  public void setIncreasedUsers(String increasedUsers) {
    this.increasedUsers = increasedUsers;
  }

  public String getDecreasedUsers() {
    return decreasedUsers;
  }

  public void setDecreasedUsers(String decreasedUsers) {
    this.decreasedUsers = decreasedUsers;
  }

  public Integer getIsLatest() {
    return isLatest;
  }

  public void setIsLatest(Integer isLatest) {
    this.isLatest = isLatest;
  }

  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public Integer getPreviousId() {
    return previousId;
  }

  public void setPreviousId(Integer previousId) {
    this.previousId = previousId;
  }

  public Integer getVersionNo() {
    return versionNo;
  }

  public void setVersionNo(Integer versionNo) {
    this.versionNo = versionNo;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }
}
