package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingQuestion;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionKey;

import java.util.List;

/**
 * @ClassName RespTrainQuestionKeyList
 * @Description 备选项列表
 * <AUTHOR>
 * @Date 2021/2/18 16:01
 * @Version 1.0
 */
public class RespTrainQuestionKeyList {
    private Integer status;
    private TrainingQuestion trainingQuestion;
    private List<TrainingQuestionKey> trainingQuestionKeyList;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public TrainingQuestion getTrainingQuestion() {
        return trainingQuestion;
    }

    public void setTrainingQuestion(TrainingQuestion trainingQuestion) {
        this.trainingQuestion = trainingQuestion;
    }

    public List<TrainingQuestionKey> getTrainingQuestionKeyList() {
        return trainingQuestionKeyList;
    }

    public void setTrainingQuestionKeyList(List<TrainingQuestionKey> trainingQuestionKeyList) {
        this.trainingQuestionKeyList = trainingQuestionKeyList;
    }
}
