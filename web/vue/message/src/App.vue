<template>
  <div id="app" v-loading.fullscreen.lock="loading">
    <router-view></router-view>
  </div>
</template>

<style lang="less">
  #app{
    &>div{ height: 100%; overflow: hidden; }
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #68717a;
    margin-top: 0;
    padding: 0 0 50px 0;
    background-color: #eee;
    height: 549px;
    overflow: auto;
  }
</style>

<script>
import auth from '@/sys/auth'
export default {
  name: 'App',
  data () {
    return {
      loading: true,
      listenersUid: []
    }
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (sId) {
      _this.sphdSocket.unsubscribe(sId)
    })
  },
  mounted () {
    // 初始化菜单和角标数据
    this.getAllMenu()
  },
  methods: {
    getAllMenu: function () {
      // console.log('let userId2',process.env.VUE_APP_WEB_ROOT,process.env.VUE_APP_USER_ID)
      // console.log('let userId2 VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)
      // console.log('let userId2 BASE_URL', process.env.BASE_URL)
      // console.log('auth2', this.auth, auth)

      // let _this = this
      // _this.$http.post('../../../about/aboutPublichFile.do', {
      //   emulateJSON: true
      // }).then((response) => {
      //   let reqData = response.body.data
      //   _this.listApply = reqData.listApply
      //   _this.loading = false
      // }).catch(function () {
      //   _this.loading = false
      //   _this.$message({
      //     type: 'error',
      //     message: '系统错误，请重试！'
      //   })
      // })


      this.loading = false
      let userBadgeNumbers = window.localStorage.getItem('userBadgeNumbers')
      let userBadge = JSON.parse(userBadgeNumbers)
      // console.log('Appvue getAllMenu setNewMenu', userBadge)
      this.$store.dispatch('setNewMenu', userBadge)
      this.$store.dispatch('setNewVersion', userBadge)
      let totalCorner = this.$store.state.menu[0].corner
      // console.log('initMenu : ' + JSON.stringify(this.$store.state.menu))
      typeof window.parent.setMsgNum === 'function' && window.parent.setMsgNum(false, totalCorner) // 显示初始化的小铅笔角标数
      self !== top && window.parent.initBounceFloating() // 初始化浮窗状态
      // 获取存储的浮窗路由串
      let floatingUrl = window.localStorage.getItem('floating')
      if (floatingUrl) {
        floatingUrl = floatingUrl.split('-')
        this.$router.push({
          path: floatingUrl[floatingUrl.length - 1]
        })
        floatingUrl.pop()
        window.localStorage.setItem('floating', floatingUrl.join('-'))
      } else {
        let toUrl = '/'
        if (this.$store.state.menu[2].corner > 0) {
          toUrl = '/homeApprove'
        } else if (this.$store.state.menu[3].corner > 0) {
          toUrl = '/myMessage'
        }
        this.$router.push({
          path: toUrl
        })
      }
      this.setListener()
    },
    setListener: function () {
      let _this = this
      // _this.sphdSocket.send('myMessages', { 'userId': this.sphdSocket.user.userID, 'session': this.sphdSocket.sessionid })
      _this.sphdSocket.send('myMessages', {})
      _this.listenersUid = [
        this.sphdSocket.subscribe('updateUserBadgeNumbers', function (data) {
          // console.log('一眼看出updateUserBadgeNumbers callback', data)
          let getData = JSON.parse(data)
          let newVersion = getData.ver
          let oldVersion = _this.$store.getters.getVersion
          if (newVersion > oldVersion) {
            // console.log('Appvue setListener setNewMenu', getData)
            _this.$store.dispatch('setNewMenu', getData)
            _this.$store.dispatch('setNewVersion', getData)
            let totalCorner = _this.$store.state.menu[0].corner
            typeof window.parent.setMsgNum === 'function' && window.parent.setMsgNum(true, totalCorner) // 显示初始化的小铅笔角标数
          } else {
            // console.log('版本号不是大于关系')
          }
        }, null, 'user')
      ]
    }
  }
}
</script>

<style lang="less">
  @import 'less/index';
</style>
<style>
  html,body{
    padding: 0;
    margin: 0;
    font: 12px/1.5 "Helvetica Neue", "Luxi Sans", "DejaVu Sans", Tahoma, "Hiragino Sans GB", STHeiti, "Microsoft YaHei";
    height: 100%;
    overflow: hidden;
  }
</style>
