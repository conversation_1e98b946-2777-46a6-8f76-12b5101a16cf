<template>
  <div id="openInvoice" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="开票审批" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="tabs">
      <div class="tabs-bar">
        <router-link :to="{name: 'invoiceApproval1'}" tag="div">
          <TY_TabBar label="开票待审批" :msgCount="applyList2.length" :bool="$store.getters.getInvoiceNav === 1">
          </TY_TabBar>
        </router-link>
        <router-link :to="{name: 'invoiceApproval2'}" tag="div">
          <TY_TabBar :label="zan" :msgCount="applyList4.length" :bool="$store.getters.getInvoiceNav === 2">
          </TY_TabBar>
        </router-link>
        <router-link :to="{name: 'invoiceApproval3'}" tag="div">
          <TY_TabBar label="已批准待开票" :bool="$store.getters.getInvoiceNav === 3">
          </TY_TabBar>
        </router-link>
        <router-link :to="{name: 'invoiceApproval4'}" tag="div">
          <TY_TabBar label="已开票未登记" :msgCount="applyList5.length" :bool="$store.getters.getInvoiceNav === 4">
          </TY_TabBar>
        </router-link>
      </div>
      <div class="tabs-content">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>
<style>
  #openInvoice .ui-cell_con { position: relative;  }
  #openInvoice .tabs-tab {
    width: 25%;
  }
  #openInvoice .info {
    display: inline-block;
    width: 49%;
    overflow:hidden ;
    text-overflow: ellipsis ;
    white-space: nowrap;
    font-size:1rem;
  }
  #openInvoice .wid{ display: inline-block; width: 2rem; }
  #openInvoice .zan {
    padding:0 10px;
    border:3px solid #ed5565;
    background:yellow;
    border-radius:50%;
    position: absolute;
    left: 41%;
    top:0.7rem;
  }
</style>
<script>
import { formatDate, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceApproval',
  filters: {
    formatDate,
    numberFilter
  },
  data () {
    return {
      list: this.$store.getters.getInvoiceList,
      applyList2: [], // 待审批
      applyList3: [], // 待开票
      applyList4: [], // 暂缓开票
      applyList5: [], // 未登记
      loading: true,
      zan: '暂缓开票',
      listeners: []
    }
  },
  created () {
    let _this = this
    this.$store.dispatch('setNewInvoiceList')
    _this.listeners = [
      // 登记
      _this.sphdSocket.subscribe('signFor', _this.signForCallback, null, 'user'),
      // 签收
      _this.sphdSocket.subscribe('saleRegistration', _this.saleRegistrationCallback, null, 'user')
    ]
  },
  methods: {
    // 设置本页需要的全部订阅
    setLisenter: function () {
      let _this = this
      _this.zan = '暂缓开票'
      _this.loading = false
      if (_this.list && _this.list.applyList4) {
        if (_this.list.applyList4.length > 0) {
          _this.zan = '！暂缓开票'
        }
        _this.applyList2 = _this.list.applyList2
        _this.applyList3 = _this.list.applyList3
        _this.applyList4 = _this.list.applyList4
        _this.applyList5 = _this.list.applyList5
      }
    },
    search: function () {
      this.$router.push({
        name: `invoiceEnd`
      })
    },
    signForCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('签收回调', res)
      this.$store.dispatch('setNewInvoiceList')
    },
    saleRegistrationCallback: function (response) {
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('送达回调', res)
      this.$store.dispatch('setNewInvoiceList')
    }
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  watch: {
    '$store.state.invoiceList': {
      deep: true,
      immediate: true,
      handler (invoiceList) {
        this.list = invoiceList
        if (this.list && this.list.applyList2) {
          this.setLisenter()
        }
      }
    }
  }
}
</script>
