<template>
  <div class="demoUpload">
    <TyTab :namelist="namelist" :changeTabFun="changeTab" />
    <div v-if="tabIndex==0">
      <span>文件夹上传，手动上传，不限制文件类型，默认文件进度和状态显示</span>
      <uploadFile ref="uploadFile0"
          module="测试"
          :committed.async="committed"
          :selectFolder="true"
          :multiple="true"
          :autoUpload="false"
          :successFun="handleSuccess01"
      >
      </uploadFile>
    </div>
    <div v-if="tabIndex==1">
      <span>多文件上传，限制文件格式（与文件与资料相同），自动上传，默认文件进度和状态显示</span>
      <uploadFile ref="uploadFile1"
          module="测试"
          :committed.async="committed"
          :showSelect="false"
          :multiple="true"
          ext='.doc,.docx,.xls,.xlsx,.zip,.rar,.apk,.ipa,.ppt,.txt,.pdf,.png,.jpg,.wps,.et,.md'
          :successFun="handleSuccess01"
          :beforeUploadFun="beforeUpload1"
      >
        <template #btnArea>
          <span v-if="!uploading" class="txtLink">请选择文件</span>
        </template>
      </uploadFile>
    </div>
    <div v-if="tabIndex==2">
      <span>单文件上传，限制图片格式，自定义文件格式报错信息，自动上传，自定义状态显示</span>
      <uploadFile ref="uploadFile2"
          module="测试"
          :committed.async="committed"
          :showSelect="false"
          :showFileList="false"
          :autoUpload="true"
          ext='.png,.jpeg,.jpg,.gif'
          extMsg='只能上传图片格式文件'
          :removeFun="uploadRemove"
          :successFun="handleSuccess2"
          :beforeUploadFun="beforeUpload2"
      >
      <template #btnArea>
        <span class="txtLink">{{ uploadBtn1_txt }}</span>
      </template>
      </uploadFile>
    </div>
    <div v-if="uploaded">
      <button @click="mockCommitData">业务数据提交</button>
    </div>
  </div>
</template>
<script>
import { initNav } from "@/utils/routeChange"
import uploadFile from "@/components/uploadFile.vue"
import TyTab from "@/components/TyTab.vue"

export default {
  components: {
    uploadFile,
    TyTab
  },
  data() {
    return {
      pageName: 'demoUpload',
      namelist: ['文件夹上传','多文件上传','单文件自动上传'],
      tabIndex: 0,
      uploadBtn1_txt: '请选择文件',
      uploading: false,
      uploaded: false,
      committed: false,
      files: []
    }
  },
  // beforeRouteLeave(to, from, next) {
  //   beforeRouteLeave(to, from, next,this)
  // },
  created(){
    initNav({mid: this.pageName, name: '测试断点上传', pageName: this.pageName}, null, this)
  },
  mounted(){
  },
  methods: {
    changeTab(tabIndex) {
      if(this.tabIndex != tabIndex) {
        //数据清理
        this.$refs['uploadFile'+this.tabIndex].clearAll()
        this.uploadBtn1_txt = '请选择文件'
        this.uploading = false
        this.uploaded = false
        this.committed = false
        this.files = []
        //显示新Tab
        this.tabIndex = tabIndex
      }
    },
    beforeUpload1 (file) {
      // console.log('父组件的beforeUpload file==', file)
      this.uploading = true
      return true
    },
    beforeUpload2 (file) {
      // console.log('父组件的beforeUpload file==', file)
      this.uploadBtn1_txt = '正在上传...'
      return true
    },
    uploadRemove(file){
      console.log('uploadRemove = ', file)
    },
    handleSuccess01(file, files, ofile, totalFiles){
      if(files.length >= totalFiles.length) {//文件全部上传，可以提交业务数据
        this.uploaded = true;
      }
      // console.log('当前成功的前端文件', ofile)
      // console.log('计划上传的文件', totalFiles)
      // console.log('计划上传的文件数量', totalFiles.length)
      // console.log('完成上传的文件数量', files.length)
      // console.log('成功后的返回值，后台uploadfile对象', file)
      // console.log('完成上传uploadfile对象数组', files)

      return true
    },
    handleSuccess2(file, files, ofile, totalFiles){
      if(files.length >= totalFiles.length) {//文件全部上传，可以提交业务数据
        this.uploaded = true;
        this.uploadBtn1_txt = '上传成功，'+files.length+'个文件已上传！'
        this.files = files
      }
      // console.log('当前成功的前端文件', ofile)
      // console.log('计划上传的文件', totalFiles)
      // console.log('计划上传的文件数量', totalFiles.length)
      // console.log('完成上传的文件数量', files.length)
      // console.log('成功后的返回值，后台uploadfile对象', file)
      // console.log('完成上传uploadfile对象数组', files)

      return true
    },
    mockCommitData() {
      console.log(this.files)
      console.log(this.files.map(item=>item.filename))
      this.committed = true
    },
  }
}

</script>

<style lang="scss" scoped>
.demoUpload {
  padding: 50px;
}
.demoUpload ::v-deep(.uploadFile) {
  border: 2px solid #48cfad;
}
</style>