package cn.sphd.miners.modules.publicInformation.service.impl;

import cn.sphd.miners.modules.publicInformation.entity.PiInfo;
import cn.sphd.miners.modules.publicInformation.entity.PiInfoHistory;
import cn.sphd.miners.modules.publicInformation.service.PiInfoServies;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class PiInfoUsing implements FileUsingCallback {

    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            PiInfoServies servies = ac.getBean(PiInfoServies.class,"piInfoServies");
            switch (entityClass) {
                case "PiInfo":
                    PiInfo piInfo = servies.piInfoSingle(id);
                    if (piInfo != null && piInfo.getPath() != null && piInfo.getPath() != "") {
                        return filename.equals(piInfo.getPath());
                    }
                    break;
                case "PiInfoHistory":
                    PiInfoHistory piInfoHistory = servies.piInfoHistorySingle(id);
                    if(piInfoHistory != null && piInfoHistory.getPath() != null && piInfoHistory.getPath() != "") {
                        return  filename.equals(piInfoHistory.getPath());
                    }
                    break;
            }
        }
        return false;
    }

    @Override
    @JsonIgnore
    @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public PiInfoUsing(Integer id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }

    public PiInfoUsing() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }

}
