package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 装备管理_装备器具
 * 20230209 1.247装备器具新增
 * Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Repository
public interface TEquEquipmentMapper {
    /**
     * 查询装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_装备器具
     *           20230209 1.247装备器具新增
     *           主键
     * @return 装备管理_装备器具
     * 20230209 1.247装备器具新增
     */
    public TEquEquipment selectTEquEquipmentById(Long id);

    /**
     * 查询装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 列表
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @return 装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 集合
     */
    public List<TEquEquipment> selectTEquEquipmentList(TEquEquipment tEquEquipment);

    /**
     * 新增装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquEquipment(TEquEquipment tEquEquipment);

    /**
     * 修改装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipment 装备管理_装备器具
     *                      20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquEquipment(TEquEquipment tEquEquipment);

    /**
     * 删除装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_装备器具
     *           20230209 1.247装备器具新增
     *           主键
     * @return 结果
     */
    public int deleteTEquEquipmentById(Long id);

    /**
     * 批量删除装备管理_装备器具
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquEquipmentByIds(String[] ids);

    TEquEquipment selectTEquEquipmentByName(TEquEquipment equipment);
}
