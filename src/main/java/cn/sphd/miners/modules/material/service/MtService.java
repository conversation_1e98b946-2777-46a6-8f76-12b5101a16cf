package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.iws.entity.TIwsMaterialLocation;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtBaseHistory;
import cn.sphd.miners.modules.material.entity.MtSupplierMaterial;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MtService extends BadgeNumberCallback {
    Map operation(MtBase base,String operation,Integer userId);

    Map getList(String state, Integer oid,String keyword,Integer category,Integer supplier,Integer menu,Integer type,Integer pageNum,Integer per,Integer isConsistent);

    Map getSuppliersByMbId(Integer id,String enabled);

    Map getStocksByMtId(Integer id);

    Map getLocationDetails(Integer id, Integer supplier, Integer relationId);

    //获取供应商承兑汇票状态
    Integer draftState(Integer supplierId);

    Map getLocationHisDetails(MtSupplierMaterial history, SrmSupplier sh);
    //获取审批供应关系信息
    Map getLocationHisDetails(Integer hisId);
    //新增供应商审批查看
    Map getLocationHisDetails( ApprovalProcess approvalProcess);
    //获取审批材料信息
    Map getMaterielDetails(Integer hisId);
    //获取材料列表 之材料
    Map<String,Object> getMtList(String state,String keyword,List<Integer> code,Integer currPage, Integer pageSize);

    //获取材料列表 之配方
    Map<String,Object> getMtListForFormula(String state,String keyword,List<Integer> code,Integer currPage, Integer pageSize);

    //获取已停用材料列表 之材料
    Map<String,Object> getStopMtBaseList(List<Integer> code,Integer currPage, Integer pageSize);


    //获取已停用材料信息
    MtBase getStopMtBaseData(Integer id);
    //获取已停用材料列表 之配方
    Map<String,Object> getStopMtBaseListForFormula(List<Integer> code,String keyword,Integer currPage, Integer pageSize);

    //获取材料列表
    List<Map<String,Object>> getMtList(String state,List<Integer> code);

    Map getIndex(Long org);

    Map location(Integer supplierMaterial, Integer mtId, String params, BigDecimal initialStock, Integer operation, Integer re, User user);


    Integer getNum(int i, Integer id);

    Map getHistory(Integer id);

    Map getmtLocation(Integer id,Integer supplierMaterialId);

    String getSupplierState(Integer supplierId, Integer mtId);
    //添加暂停供应商审批
    Map addSuspendSupplier(MtBase base,String operation,Integer userId,String enabled);
    //暂停供应商审批
    String suspendSupplierApproval(ApprovalProcess approvalProcess);
    //暂停材料审批
    String suspendMaterielApproval(ApprovalProcess approvalProcess);
    //恢复供应商审批
    String recoveryMaterielApproval(ApprovalProcess approvalProcess);

    void saveStockRecord(Integer org, Integer material,Integer creator,String createName, Integer supplier, String event, BigDecimal quantity,BigDecimal oldStock, BigDecimal endStock, Integer business);
    public Boolean exclusiveByMtIdAndSupplier(Integer material, Integer supplier);
    //申
    List<ApprovalProcess> getApprovalProcessByFromUserId(Integer toUserId, String approvalStatus,  Integer businessType, Date beginTime, Date endTime, String sort);  //申请人申请数据

    MtBase getMtById(Integer id);

    Map getMtListByDate(Date date, User user);

    Map getMtListByCondition(Date date, String operation, Integer oid);

    void updateMaBase(MtBase mtBase);

    List<MtBaseHistory> getMtBaseHistoryById(Integer id, String operation);

    Map getMtListByWarehouse(Integer oid, Long previousId);

    void setStockZero(String ids, User user);

    void initialStock22(User user, Integer mtId, BigDecimal initialStock, Integer dripSupplier, Integer dripPackaging);

    void initialStock12(User user, List<TIwsMaterialLocation> materialLocations, Integer dripSupplier, Integer dripPackaging);

    void initialStock21(User user, List<TIwsMaterialLocation> materialLocations, Integer dripSupplier, Integer dripPackaging);

    List<Map<String, Object>> getStocksDetailByMtId(Integer material);


    List<Map<String, Object>> getStocksDetailByMtSupplierMaterial(TIwsMaterialLocation tIwsMaterialLocation);

    void deleteMaterialLocation(Integer material);
}
