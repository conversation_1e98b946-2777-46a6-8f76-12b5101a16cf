package cn.sphd.miners.modules.finance.dto;

import cn.sphd.miners.modules.finance.entity.PersonnelSalary;

/**
 * @ClassName RespPersonnelSalary
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/7 16:40
 * @Version 1.0
 */
public class RespPersonnelSalary extends PersonnelSalary {
    private String userName;
    private String phone;
    private String departName;//部门名称
    private String postName;//职位
    private Integer type;//0为未修改，1为修改，需要标红

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }
}
