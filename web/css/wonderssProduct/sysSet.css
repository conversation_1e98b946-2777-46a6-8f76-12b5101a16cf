.page{width: 1100px; padding: 10px 20px; display: none}
.bonceContainer .mainCon{
    width: 90%;
    margin: 16px auto;
}
h4{
    color: #3E4148;
}
.item-flex{
    display: flex;
    line-height: 36px;
    color: #5A5E68;
    margin-bottom: 8px;
}
.item-title{
    width: 120px;
    flex: none;
}
.item-content{
    flex: auto;
}
.ty-page-header{
    display: flex;
    line-height: 24px;
    padding: 0 0 0 20px;
    color: #5d9cec;
    margin-top: 16px;
}
.page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
}
.page-header__left::after {
    content: "";
    position: absolute;
    width: 1px;
    height: 16px;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #dcdfe6;
}
.ty-page-header .icon-back {
    font-size: 18px;
    margin-right: 6px;
    position: relative;
    vertical-align: bottom;
}