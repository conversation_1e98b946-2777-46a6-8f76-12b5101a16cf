.search{  border: 1px solid #0b94ea; display: inline-block; border-radius: 20px; width:300px; position: relative; float: right; margin-right: 30px;  }
.search span:nth-child(1){ border-radius: 20px 0 0 20px; background:#0b94ea;padding: 5px 10px; color: #fff;    }
.search span:nth-child(3){ text-align: right; width:44px; height:27px; border-radius:0 20px 20px 0;  padding: 5px 10px; position: absolute; right: 0; top: 0;   }
.search span:nth-child(3):hover{ background:#ecf6ff;    }
.search span:nth-child(3)>i{ display:inline-block; width:20px; height: 20px; background: url("../technology/img/search.png") no-repeat; background-size:20px 20px;    }
.search input{ border:none; margin: 0; display: inline-block; height: 27px; position: absolute; top: 0; padding: 0 5px; width:140px;    }
.search input:focus{ border:none;    }
.simpleTable{width: 100%;}
.simpleTable thead{border-bottom:1px solid #d1d1d1;}
.simpleTable td{font-size: 14px;  text-align: center;  padding: 0 15px;  height: 40px;  word-wrap: break-word;  word-spacing: normal;  word-break: break-all;
    color: #101010;}
.simpleTable tbody td{padding-top: 10px;padding-bottom: 6px;}
.jumpBtn{color: #0070c0; cursor: pointer; }
.ttl{font-size: 12px;}
.mainCon{display: none;width: 1200px;}
.checkStaff td.toggle{border-left: none;border-top: none;border-bottom: none;}
.bottomLine{padding: 20px 0;}
.mainCon>div{margin-left: 80px;}
.mainCon input{border: 1px solid #d7d7d7;}
.line{padding-bottom: 10px;}
.mar{margin-bottom: 50px}
.marL{margin-left: 20px}
.mar_f{margin-bottom: 6px}
.tbControl input{border: none;}
.recordScan span{margin-top: 10px;}
.recordScan h4{margin-top: 0;margin-bottom: 0;width: 90%;  line-height: 40px;}
#recordScanSalary td.red{color: #ed5565;}
.keywordSearch input,.bonceCon .keywordSearch input {  margin-left: 10px;padding: 0 10px;  font-size: 12px;  min-width: 200px;  height: 26px;  line-height: 26px;  }
.keywordSearch select{ margin-left: 10px;background: #fff;  border: 1px solid #d7d7d7;  padding: 3px 0;  line-height: 31px;  width: 200px;}

.flexbox{ display: flex; text-align: right; margin-top:15px;margin-bottom:15px; }
.flexbox>div{ flex:1; text-align: left; line-height: 30px; }
.flexbox>div .form-control{ width: 180px; display: inline-block; }
.flexbox>div:nth-child(3){  text-align: right; }
#unStaff ul{ max-height: 300px; overflow: auto; }
#unStaff li{ line-height: 25px; padding-left: 100px;   }
#unStaff li:hover{ background: #d3ebf8;   }
#unStaff li .fa{ color:#0b94ea; font-size: 16px; margin-right: 20px;  }
#scanPerson h4{ text-align: center;  }



























