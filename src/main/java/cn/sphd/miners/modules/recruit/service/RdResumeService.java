package cn.sphd.miners.modules.recruit.service;

import cn.sphd.miners.modules.recruit.entity.RdInterviewNotification;
import cn.sphd.miners.modules.recruit.entity.RdInterviewer;
import cn.sphd.miners.modules.recruit.entity.RdNotice;
import cn.sphd.miners.modules.recruit.entity.RdResume;

import java.util.List;

public interface RdResumeService {

    RdResume getRdResumeByOfferId(Integer offerId);

    List<RdInterviewNotification>  getRdInterviewNotificationsByResumeId(Long resumeId);

    List<RdInterviewer> getRdInterviewersByResumeId(Long resumeId);

    void saveRdResume(RdResume rdResume);

    RdResume getRdResumeById(Long id);

    void saveRdInterviewNotification(RdInterviewNotification rdInterviewNotification);

    void updateRdInterviewNotification(RdInterviewNotification rdInterviewNotification);

    RdInterviewNotification getRdInterviewNotificationById(Long id);

    List<RdResume> getRdResumesByNoticeId(Long noticeId,Integer state);

    RdResume getRdResume(Long noticeId,Long noticePostId,String hashKey);

    List<RdResume> getRdResumesByHashKey(String hashKey);

    RdNotice getSumRdResumeByNoticeId(Long noticeId);

    void  deleteRdResume(RdResume rdResume);

    void  updateRdResume(RdResume rdResume);

}
