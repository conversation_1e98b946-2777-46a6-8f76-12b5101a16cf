<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.orderReview.mapper.TSlOrdersItemReviewMapper">
    
    <resultMap type="TSlOrdersItemReview" id="TSlOrdersItemReviewResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="orders"    column="orders"    />
        <result property="ordersItem"    column="orders_item"    />
        <result property="ordersReview"    column="orders_review"    />
        <result property="deliveryNum"    column="delivery_num"    />
        <result property="state"    column="state"    />
        <result property="isScheduled"    column="is_scheduled"    />
        <result property="scheduledAmount"    column="scheduled_amount"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="deliveryAddressId"    column="delivery_address_id"    />
        <result property="deliveryAddress"    column="delivery_address"    />
        <result property="surplusDate"    column="surplus_date"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="reviewerName"    column="reviewer_name"    />
        <result property="reviewDate"    column="review_date"    />
        <result property="newAmount"    column="new_amount"    />
        <result property="newDate"    column="new_date"    />
        <result property="newAddressId"    column="new_address_id"    />
        <result property="newAddress"    column="new_address"    />
        <result property="keywords"    column="keywords"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTSlOrdersItemReviewVo">
        select id, org, orders, orders_item, orders_review, delivery_num, state,case ifnull(is_scheduled,0) when 0 then 0 when 1 then 1 when 2 then 2 end as is_scheduled, scheduled_amount, delivery_date, delivery_address_id, delivery_address, surplus_date, reviewer, reviewer_name, review_date, new_amount, new_date, new_address_id, new_address, keywords, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_sl_orders_item_review
    </sql>

    <select id="selectTSlOrdersItemReviewList" parameterType="TSlOrdersItemReview" resultMap="TSlOrdersItemReviewResult">
        <include refid="selectTSlOrdersItemReviewVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="orders != null "> and orders = #{orders}</if>
            <if test="ordersItem != null "> and orders_item = #{ordersItem}</if>
            <if test="ordersReview != null "> and orders_review = #{ordersReview}</if>
            <if test="deliveryNum != null "> and delivery_num = #{deliveryNum}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="isScheduled != null "> and is_scheduled = #{isScheduled}</if>
            <if test="scheduledAmount != null "> and scheduled_amount = #{scheduledAmount}</if>
            <if test="deliveryDate != null "> and delivery_date = #{deliveryDate}</if>
            <if test="deliveryAddressId != null "> and delivery_address_id = #{deliveryAddressId}</if>
            <if test="deliveryAddress != null  and deliveryAddress != ''"> and delivery_address = #{deliveryAddress}</if>
            <if test="surplusDate != null "> and surplus_date = #{surplusDate}</if>
            <if test="reviewer != null "> and reviewer = #{reviewer}</if>
            <if test="reviewerName != null  and reviewerName != ''"> and reviewer_name like concat('%', #{reviewerName}, '%')</if>
            <if test="reviewDate != null "> and review_date = #{reviewDate}</if>
            <if test="newAmount != null "> and new_amount = #{newAmount}</if>
            <if test="newDate != null "> and new_date = #{newDate}</if>
            <if test="newAddressId != null "> and new_address_id = #{newAddressId}</if>
            <if test="newAddress != null  and newAddress != ''"> and new_address = #{newAddress}</if>
            <if test="keywords != null  and keywords != ''"> and keywords = #{keywords}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTSlOrdersItemReviewById" parameterType="Long" resultMap="TSlOrdersItemReviewResult">
        <include refid="selectTSlOrdersItemReviewVo"/>
        where id = #{id}
    </select>
    <select id="getOrderItems" parameterType="integer" resultType="java.util.Map">

    </select>
    <select id="getPreReviewsByItemId" resultType="java.util.Map">
        select soi.id                                      as item_id,
               pm.outer_name                               as outerName,
               pm.id                                       as pm_id,
               pm.outer_sn                                 as outerSn,
               soi.amount,
               DATE_FORMAT(soi.delivery_date, '%Y-%m-%d')  as deliveryDate,
               soi.delivery_address                        as deliveryAddress,
               soi.delivery_address_id                     as deliveryAddressId,
               soih.amount                                 as oldAmount,
               DATE_FORMAT(soih.delivery_date, '%Y-%m-%d') as oldDeliveryDate,
               sca.address                                 as oldAddress,
               pm.unit,
               soir.scheduled_amount                       as scheduledAmount,
               DATE_FORMAT(soir.surplus_date, '%Y-%m-%d')  as surplusDate,
               soir.is_scheduled                           as isScheduled,
               soir.state,soir.version_no
        from t_sl_orders_item_review soir
                 left join t_sl_orders_item soi on soir.orders_item = soi.id
                 left join t_sl_orders_item_history soih on soir.orders_review = soih.id
                 left join t_pd_merchandise pm on soi.sales_relationship = pm.id
                 left join t_sl_customer_address sca on soih.peroid = sca.id
        where soir.orders_item = #{itemId} and soir.scheduled_amount is not null
    </select>

    <insert id="insertTSlOrdersItemReview" parameterType="TSlOrdersItemReview" useGeneratedKeys="true" keyProperty="id">
        insert into t_sl_orders_item_review
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="org != null">org,</if>
            <if test="orders != null">orders,</if>
            <if test="ordersItem != null">orders_item,</if>
            <if test="ordersReview != null">orders_review,</if>
            <if test="deliveryNum != null">delivery_num,</if>
            <if test="state != null">state,</if>
            <if test="isScheduled != null">is_scheduled,</if>
            <if test="scheduledAmount != null">scheduled_amount,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="deliveryAddressId != null">delivery_address_id,</if>
            <if test="deliveryAddress != null">delivery_address,</if>
            <if test="surplusDate != null">surplus_date,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewerName != null">reviewer_name,</if>
            <if test="reviewDate != null">review_date,</if>
            <if test="newAmount != null">new_amount,</if>
            <if test="newDate != null">new_date,</if>
            <if test="newAddressId != null">new_address_id,</if>
            <if test="newAddress != null">new_address,</if>
            <if test="keywords != null">keywords,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="org != null">#{org},</if>
            <if test="orders != null">#{orders},</if>
            <if test="ordersItem != null">#{ordersItem},</if>
            <if test="ordersReview != null">#{ordersReview},</if>
            <if test="deliveryNum != null">#{deliveryNum},</if>
            <if test="state != null">#{state},</if>
            <if test="isScheduled != null">#{isScheduled},</if>
            <if test="scheduledAmount != null">#{scheduledAmount},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="deliveryAddressId != null">#{deliveryAddressId},</if>
            <if test="deliveryAddress != null">#{deliveryAddress},</if>
            <if test="surplusDate != null">#{surplusDate},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewerName != null">#{reviewerName},</if>
            <if test="reviewDate != null">#{reviewDate},</if>
            <if test="newAmount != null">#{newAmount},</if>
            <if test="newDate != null">#{newDate},</if>
            <if test="newAddressId != null">#{newAddressId},</if>
            <if test="newAddress != null">#{newAddress},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTSlOrdersItemReview" parameterType="TSlOrdersItemReview">
        update t_sl_orders_item_review
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="orders != null">orders = #{orders},</if>
            <if test="ordersItem != null">orders_item = #{ordersItem},</if>
            <if test="ordersReview != null">orders_review = #{ordersReview},</if>
            <if test="deliveryNum != null">delivery_num = #{deliveryNum},</if>
            <if test="state != null">state = #{state},</if>
            <if test="isScheduled != null">is_scheduled = #{isScheduled},</if>
            <if test="scheduledAmount != null">scheduled_amount = #{scheduledAmount},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="deliveryAddressId != null">delivery_address_id = #{deliveryAddressId},</if>
            <if test="deliveryAddress != null">delivery_address = #{deliveryAddress},</if>
            <if test="surplusDate != null">surplus_date = #{surplusDate},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="reviewerName != null">reviewer_name = #{reviewerName},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="newAmount != null">new_amount = #{newAmount},</if>
            <if test="newDate != null">new_date = #{newDate},</if>
            <if test="newAddressId != null">new_address_id = #{newAddressId},</if>
            <if test="newAddress != null">new_address = #{newAddress},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTSlOrdersItemReviewById" parameterType="Long">
        delete from t_sl_orders_item_review where id = #{id}
    </delete>

    <delete id="deleteTSlOrdersItemReviewByIds" parameterType="String">
        delete from t_sl_orders_item_review where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>