package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.AccountantSettleService;
import cn.sphd.miners.modules.accountant.service.BuildAccountService;
import cn.sphd.miners.modules.accountant.service.FinanceRelevanceService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.accountant.util.Tools;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-06-14 18:52
 * @description
 **/
@Service
@Transactional(propagation= Propagation.REQUIRED)
public class BuildAccountServiceImplement implements BuildAccountService{

    @Autowired
    BuildAccountMapper buildAccountMapper;
    @Autowired
    TAccountSubjectMapper subjectMapper;
    @Autowired
    TAccountantCashMapper cashMapper;
    @Autowired
    TAccountantSubjectHistoryMapper tAccountantSubjectHistoryMapper;
    @Autowired
    SubjectPeroidMapper peroidMapper;
    @Autowired
    CashFlowMapper cashFlowMapper;
    @Autowired
    AccountantSettleService settleService;
    @Autowired
    SettleMapper settleMapper;
    @Autowired
    SubjectSettingService settingService;
    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    DetailMapper detailMapper;
    @Autowired
    TAccountantSubjectCashMapper subjectCashMapper;
    @Autowired
    VoucherHistoryMapper voucherHistoryMapper;
    @Autowired
    SubjectHistoryMapper voucherSubjectHistoryMapper;//凭证科目暂存表
    @Autowired
    TAccountantSubjectHistoryMapper subjectHistoryMapper;//科目暂存表
    @Autowired
    DetailHistoryMapper detailHistoryMapper;
    @Autowired
    TAccountantSubjectCashHistoryMapper subjectCashHistoryMapper;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    FinanceRelevanceService financeRelevanceService;
    @Autowired
    UserService userService;
    @Autowired
    ReimburseRelevanceMapper reimburseRelevanceMapper;
    @Autowired
    ReimburseRelevanceHistoryMapper reimburseRelevanceHistoryMapper;
    @Autowired
    TaccountantEstablishEntityMapper establishEntityMapper;
    @Autowired
    TaccountantSubjectChangeMapper changeMapper;
    @Autowired
    TaccountantSubjectRecordMapper recordMapper;
    @Autowired
    TaccountantSubjectJournalMapper journalMapper;
    @Autowired
    GeneralSubjectRecord generalSubjectRecord;
    @Autowired
    VoucherUpdate voucherUpdate;

    @Override
    public JSONObject getBaseSubjectsByOrg(int org) {
        QueryData qd = new QueryData();
        qd.put("org",org);
        qd.put("balance",0);
        //返回建账时某机构最后一层科目数据
        List<TAccountantSubjectEntity> listSubjects = buildAccountMapper.getBaseSubjectsByOrg(qd);
        for (TAccountantSubjectEntity tse : listSubjects) {
            String balanceDirection = tse.getBalanceDirection();//科目本身方向
            String beginningDirection = tse.getBeginningDirection() == null ? "3" : tse.getBeginningDirection();//年初方向
            String endDirection = tse.getEndDirection() == null ? "3" : tse.getEndDirection();//余额方向
            BigDecimal beginningBalance = tse.getBeginningBalance();//年初余额
            BigDecimal balance = tse.getBalance();//期末余额
            if (!balanceDirection.equals(beginningDirection) && !"3".equals(beginningDirection)) {
                //如果年初方向和科目本身方向不同，且年初方向不是平的话，说明年初方向和科目本身方向相反，需要在页面上显示负数
                if (beginningBalance != null) {
                    tse.setBeginningBalance(beginningBalance.negate());
                }
            }
            if (!balanceDirection.equals(endDirection) && !"3".equals(endDirection)) {
                //如果期末方向和科目本身方向不同，且期末方向不是平的话，说明期末方向和科目本身方向相反，需要在页面上显示负数
                if (endDirection != null) {
                    tse.setBalance(balance.negate());
                }
            }
        }
        //返回建账时某机构中暂存的现金流量表数据
        List<TAccountantCash> cashList = buildAccountMapper.getTempCashFlowByOrg(org);
        JSONObject result = new JSONObject();
        result.put("subjectList",listSubjects);
        result.put("cashList",cashList);
        return result;
    }

    @Override
    public String getBuildAccountState(int org) {
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(org);
        link = buildAccountMapper.getBuildAccountState(link);
        if (link != null) {
            return link.getValue_();
        }
        else
            return null;
    }

    @Override
    public int setBuildAccountState(User user, String state) {
        int org = user.getOid();
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(org);
        link.setValue_(state);
//        QueryData qd = new QueryData();
        Map<String,Object> qd = new HashMap<String,Object>();
        qd.put("org",org);
        qd.put("state",state);
        int res = buildAccountMapper.setBuildAccountState(qd);
        //更新机构表中的建账状态字段，用于登录系统后读取缓存方便
        buildAccountMapper.setOrgBuildAccountState(qd);
        //获取所有财务对公户，并逐个生成对应的会计科目。此工作只做一次，科目生成后需要更新finance_relevance_state状态为Y，表示以后不再生成
        TAccountantSetting param = new TAccountantSetting();
        param.setKey_("finance_relevance_state");
        param.setOrg(org);
        param = buildAccountMapper.getSettingByKey(param);//获取finance_relevance_state状态，如果是N表示财务对公户和会计科目从未关联过，这个工作只做一次
        if (param != null) {
            String value = param.getValue_();
            if ("N".equals(value)) {
                //N表示还没有进行过财务对公户与科目关联，所以建账时需要先生成科目
                //获取财务对公户列表
                List<FinanceAccount> financeAccountList = financeAccountService.getPublicAccount(org);
                for (FinanceAccount fa : financeAccountList) {
                    Integer financeID = fa.getId();
                    String accountName = fa.getBankName();
                    String accountNO = fa.getAccount();
                    financeRelevanceService.generateFinanceSubjects(org,financeID,accountName,accountNO,user);
                }
                //更新finance_relevance_state状态为Y，建账过程中或重新建账后再次执行该接口时避免在生成科目
                qd.put("key_","finance_relevance_state");
                qd.put("value_","Y");
                qd.put("update_name",user.getUserName());
                qd.put("update_date",new Date());
                buildAccountMapper.updateSettingByOrg(qd);
            }
        }

        userService.getPopedomStringByUser(user,true);//变更权限时用,重新覆盖缓存中的权限值
        return res;
    }

    /*金额核查，最后返回最后一级科目及其上一层父科目
    * 1、计算科目期末余额和数量是否正确，不正确的话需要标记
    * 2、整理返回的集合的顺序
    * 3、获取财务对公户并和1002下的科目对比余额，有不同的话页面显示对比列表
    * */
    @Override
    public JSONObject amountVerification(int org, String listSubjectsParam, String cashFlow) {
        //用于统计一共有多少差异
        int totalDifferent = 0;
        JSONObject result = new JSONObject();
        //如果没点暂存直接点的金额核算的话也需要先保存输入的科目数据
        if (listSubjectsParam != null || cashFlow != null) {
            saveBuildAccount(org,listSubjectsParam, cashFlow);
        }
        //金额核查之前需要把所有的父科目的余额
        List<TAccountantSubjectEntity> listResult = new ArrayList<TAccountantSubjectEntity>();//最后返回的结果集合
        //先把父科目之前的老数据清空包括余额、方向、different、数量等信息
        buildAccountMapper.clearAllParentSubject(org);
//        buildAccountMapper.clearParentSubject(org);
        QueryData qd = new QueryData();
        qd.put("org",org);
        qd.put("balance",1);
//        qd.put("balance",0);
        List<Integer> indexes = new ArrayList<Integer>();//该集合用于存放重复的parent科目对象的index，最后需要根据该集合中的index删除重复的parent
        List<TAccountantSubjectEntity> listSubjects = buildAccountMapper.getBaseSubjectsByOrg(qd);//返回所有期末余额>0的最后一层科目
        for (TAccountantSubjectEntity subjectEntity : listSubjects) {
            //计算期末余额和数量和输入的是否一致
            //数量计算的公式：年初数量 + 本年累计借数量 - 本年累计贷数量
            //余额的计算都是先把各方向转换成正负号，然后直接相加
            //得到该科目的上一级父科目
            TAccountantSubjectEntity parent = buildAccountMapper.getParentSubject(subjectEntity);
//            if (parent != null && parent.getSubject().equals("********")) {
//                System.out.println();
//            }
//            if (subjectEntity.getSubject().startsWith("1002")) {
//                System.out.println();
//            }
            String balanceDirection = subjectEntity.getBalanceDirection();
            BigDecimal beginningBalance = subjectEntity.getBeginningBalance() == null ? BigDecimal.ZERO : subjectEntity.getBeginningBalance();
            String beginningDirection = subjectEntity.getBeginningDirection() == null ? "3" : subjectEntity.getBeginningDirection();
            if (!beginningDirection.equals(balanceDirection) && !"3".equals(beginningDirection)) {
                beginningBalance = beginningBalance.negate();
            }
            BigDecimal creditAccumulative = subjectEntity.getCreditAccumulative() == null ? BigDecimal.ZERO : subjectEntity.getCreditAccumulative();
            BigDecimal debitAccumulative = subjectEntity.getDebitAccumulative() == null ? BigDecimal.ZERO : subjectEntity.getDebitAccumulative();
            BigDecimal cha = new BigDecimal(0);
            if ("1".equals(balanceDirection)) {
                cha = creditAccumulative.subtract(debitAccumulative);
            }
            else if ("2".equals(balanceDirection)) {
                cha = debitAccumulative.subtract(creditAccumulative);
            }
            BigDecimal balance = beginningBalance.add(cha);//带符号的期末余额
            //对比余额是不是对
            String endDirection = subjectEntity.getEndDirection() == null ? "3" : subjectEntity.getEndDirection();
            BigDecimal endBalance = subjectEntity.getBalance() == null ? BigDecimal.ZERO : subjectEntity.getBalance();
            if (!endDirection.equals(balanceDirection) && !"3".equals(endDirection)) {
                endBalance = endBalance.negate();
            }
            if (balance.compareTo(endBalance) != 0) {
                //如果期末余额和用户输入的不同则需要标记差异
                subjectEntity.setBalanceDifferent(false);
                totalDifferent += 1;
//                if (parent != null) {
//                    parent.setBalanceDifferent(false);
//                    totalDifferent += 1;
//                }
            }
            //比对期末数量
            Double beginningQuantity = subjectEntity.getBeginningQuantity() == null ? 0 : subjectEntity.getBeginningQuantity();//年初余额数量
            Double creditQuantity = subjectEntity.getCreditAccumulativeQuantity() == null ? 0 : subjectEntity.getCreditAccumulativeQuantity();//借数量
            Double debitQuantity = subjectEntity.getDebitAccumulativeQuantity() == null ? 0 : subjectEntity.getDebitAccumulativeQuantity();//贷数量
            Double quantity = subjectEntity.getQuantity() == null ? 0 : subjectEntity.getQuantity();//期末数量
            Double differentQuantity = beginningQuantity + creditQuantity - debitQuantity;
            if (differentQuantity.compareTo(quantity) != 0) {
                //如果期末数量和用户输入不同则需要标记
                subjectEntity.setQuantityDifferent(false);
//                totalDifferent += 1;
//                if (parent != null) {
//                    parent.setQuantityDifferent(false);
//                    totalDifferent += 1;
//                }
            }
            //需要把各数量和各余额累加到父科目上
            if (parent != null) {

                //递归将该科目的所有父科目的余额和本年累计都要计算
                updateParent(subjectEntity,parent,beginningBalance,endBalance);

                //在添加parent时需要先在集合中查找有没有和parent中的科目编号一致的对象存在，如果存在的话需要把之前那个对象的index记下来，最后要删除，因为最终的list中一个科目只能存在一个parent
                int index = getParentIndex(listResult,parent);
                if (index > 0) {
//                    indexes.add(index);
                    //删除之前先判断余额和数量是不是和录入的正确值有冲突，有冲突的话需要在冲突总数中减去
//                    TAccountantSubjectEntity parentRepeat = listResult.get(index);
//                    if (!parentRepeat.isBalanceDifferent())
//                        totalDifferent--;
//                    if (!parentRepeat.isQuantityDifferent())
//                        totalDifferent--;
                    listResult.remove(index);
                }
                listResult.add(parent);
            }
            listResult.add(subjectEntity);
        }
        //根据indexes集合中的下标元素，把listResult集合中的对象删除
//        for (int i=0;i<indexes.size();i++) {
//            int index = indexes.get(i);
//            listResult.remove(index);
//        }
        //删除完重复的parent对象之后还需要将listResult集合做一次排序操作，为了页面上显示合理
        Collections.sort(listResult);
        result.put("totalDifferent",totalDifferent);
        result.put("listResult",listResult);
        //生成period期数
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        //当期月报
        sdf.applyPattern("yyyy-MM");
        String term = sdf.format(calendar.getTime());
        result.put("period",term);

        //获取财务对公户列表，循环对比每个账户余额和它对应的1002子科目的期末余额，如果有差异需要把差异列表显示出来
//        boolean difference = false;//是否要显示"余额不一致"按钮，默认不显示，当存在某对公户和关联的科目余额不同时才显示
//        List<FinanceAccount> financeAccountList = new ArrayList<FinanceAccount>();//模拟获得的对公户列表
        List<FinanceAccount> financeAccountList = financeAccountService.getPublicAccount(org);
        List<BalanceComparison> list = new ArrayList<BalanceComparison>();//用户存储对比结果
        TAccountantSubjectEntity financeSubjectParam = new TAccountantSubjectEntity();
        financeSubjectParam.setLevel(2);
        financeSubjectParam.setRelevanceType("6");
        financeSubjectParam.setOrg(org);
        for (FinanceAccount fa:financeAccountList) {
            int financeID = fa.getId();
            String accountName = fa.getBankName();
            String account = fa.getAccount();
            BigDecimal financeBalance = fa.getBalance();
            //获取该对公户关联的二级科目
//            financeSubject.setName(accountName);

            financeSubjectParam.setRelevanceItem(financeID);
            TAccountantSubjectEntity financeSubject = subjectMapper.getFinanceRelevanceSubject(financeSubjectParam);//获取对公户对应的科目

//            financeSubject.setBalance(BigDecimal.valueOf(100));//测试代码
            BigDecimal accountantBalance = BigDecimal.ZERO;
            if (financeSubject != null) {
                accountantBalance = financeSubject.getBalance() == null ? BigDecimal.ZERO : financeSubject.getBalance();
            }
            if (financeBalance.compareTo(accountantBalance) != 0) {
                BalanceComparison bc = new BalanceComparison();
                bc.setBankName(accountName);
                bc.setAccountNO(account);
                bc.setFinanceBalance(financeBalance);
                bc.setAccountantBalance(accountantBalance);
                list.add(bc);
            }
        }
        result.put("balanceComparison",list);
        return result;
    }

    private void updateParent(TAccountantSubjectEntity subjectEntity, TAccountantSubjectEntity parent, BigDecimal subBeginningBalance, BigDecimal subEndBalance) {

        if (parent == null) return;

//        if (subjectEntity.getSubject().equals("********") && parent.getOrg()==1897) {
//            System.out.println(11);
//        }
        Double beginningQuantity = subjectEntity.getBeginningQuantity() == null ? 0 : subjectEntity.getBeginningQuantity();//年初余额数量
        Double creditQuantity = subjectEntity.getCreditAccumulativeQuantity() == null ? 0 : subjectEntity.getCreditAccumulativeQuantity();//借数量
        Double debitQuantity = subjectEntity.getDebitAccumulativeQuantity() == null ? 0 : subjectEntity.getDebitAccumulativeQuantity();//贷数量
        Double quantity = subjectEntity.getQuantity() == null ? 0 : subjectEntity.getQuantity();//期末数量
        //对比余额是不是对
//        String endDirection = subjectEntity.getEndDirection() == null ? "3" : subjectEntity.getEndDirection();
//        BigDecimal endBalance = subjectEntity.getBalance() == null ? BigDecimal.ZERO : subjectEntity.getBalance();
        BigDecimal creditAccumulative = subjectEntity.getCreditAccumulative() == null ? BigDecimal.ZERO : subjectEntity.getCreditAccumulative();
        BigDecimal debitAccumulative = subjectEntity.getDebitAccumulative() == null ? BigDecimal.ZERO : subjectEntity.getDebitAccumulative();
//        String balanceDirection = subjectEntity.getBalanceDirection();
//        BigDecimal beginningBalance = subjectEntity.getBeginningBalance() == null ? BigDecimal.ZERO : subjectEntity.getBeginningBalance();
//        String beginningDirection = subjectEntity.getBeginningDirection() == null ? "3" : subjectEntity.getBeginningDirection();

        //累加数量
        Double pBeginningQuantity = parent.getBeginningQuantity() == null ? 0 : parent.getBeginningQuantity();
        Double pCreditQuantity = parent.getCreditAccumulativeQuantity() == null ? 0 : parent.getCreditAccumulativeQuantity();
        Double pDebitQuantity = parent.getDebitAccumulativeQuantity() == null ? 0 : parent.getDebitAccumulativeQuantity();
        Double pBalanceQuantity = parent.getQuantity() == null ? 0 : parent.getQuantity();
        parent.setBeginningQuantity(pBeginningQuantity + beginningQuantity);
        parent.setCreditAccumulativeQuantity(pCreditQuantity + creditQuantity);
        parent.setDebitAccumulativeQuantity(pDebitQuantity + debitQuantity);
        parent.setQuantity(pBalanceQuantity + quantity);
        //累加余额
        BigDecimal pCreditBalance = parent.getCreditAccumulative() == null ? BigDecimal.ZERO : parent.getCreditAccumulative();
        BigDecimal pDebitBalance = parent.getDebitAccumulative() == null ? BigDecimal.ZERO : parent.getDebitAccumulative();
        parent.setCreditAccumulative(pCreditBalance.add(creditAccumulative));
        parent.setDebitAccumulative(pDebitBalance.add(debitAccumulative));

        BigDecimal pBeginningBalance = parent.getBeginningBalance() == null ? BigDecimal.ZERO : parent.getBeginningBalance();
        BigDecimal pBalance = parent.getBalance() == null ? BigDecimal.ZERO : parent.getBalance();
        String pBalanceDirection = parent.getBalanceDirection() == null ? "3" : parent.getBalanceDirection();
        String pBeginningDirection = parent.getBeginningDirection() == null ? "3" : parent.getBeginningDirection();
        String pEndDirection = parent.getEndDirection() == null ? "3" : parent.getEndDirection();
        //将父科目的方向变成正负数
        if (!pBeginningDirection.equals(pBalanceDirection) && !"3".equals(pBeginningDirection)) {
            pBeginningBalance = pBeginningBalance.negate();
        }

        if (!pEndDirection.equals(pBalanceDirection) && !"3".equals(pEndDirection)) {
            pBalance = pBalance.negate();
        }

        //22210001000 222100010005 222100010006 这三个科目时特殊科目，科目方向和父科目相反，父科目22210001是贷方科目，但是这三个的方向都是借，所以要特殊处理
        //判断父科目与子科目的方向是否相同，相同的话直接相加，不同的话父科目的余额-子科目的余额等于父科目的余额
//        String subject = parent.getSubject();
//        pBeginningBalance = vu.handleSpecialSubject(subject,pBeginningBalance);//余额相加之前先处理一下特殊科目的余额的正负数
//        pBalance = vu.handleSpecialSubject(subject,pBalance);

        String balanceDirenction = subjectEntity.getBalanceDirection();
        if (pBalanceDirection.equals(balanceDirenction)) {
            parent.setBeginningBalance(pBeginningBalance.add(subBeginningBalance));
            parent.setBalance(pBalance.add(subEndBalance));
        }
        else
        {
            parent.setBeginningBalance(pBeginningBalance.subtract(subBeginningBalance));
            parent.setBalance(pBalance.subtract(subEndBalance));
        }

        pBeginningBalance = parent.getBeginningBalance();
        pBalance = parent.getBalance();

        //最后需要把父科目的余额都变成正数，并确定余额方向
        settingSubjectDirection(pBeginningBalance,pBalance,pBalanceDirection,parent);

        //将累加后的父科目保存
        subjectMapper.update(parent);

        TAccountantSubjectEntity pparent = buildAccountMapper.getParentSubject(parent);
        //递归处理父科目的父科目，直到一级科目
        updateParent(subjectEntity,pparent,subBeginningBalance,subEndBalance);
    }

    /*返回和parent重复的科目对象的下标索引*/
    private Integer getParentIndex(List<TAccountantSubjectEntity> listResult, TAccountantSubjectEntity parent) {
        //需要从后往前遍历，因为如果存在重复的parent的话一定是在集合的尾部
        for (int i = listResult.size() - 1;i>=0;i--) {
            TAccountantSubjectEntity subjectEntity = listResult.get(i);
            if (subjectEntity.getSubject().equals(parent.getSubject())) {
                return i;
            }
        }
        return 0;
    }

    /*
    * 保存科目数据
    * */
    @Override
    public int saveBuildAccount(int org, String listSubjects, String cashFlow) {
        if (listSubjects != null && !"".equals(listSubjects)) {
            JSONArray subjectArray = JSONArray.parseArray(listSubjects);
            for (int i=0;i<subjectArray.size();i++) {
                TAccountantSubjectEntity tse = new TAccountantSubjectEntity();
                JSONObject obj = subjectArray.getJSONObject(i);
                Integer id = obj.getInteger("id");
                tse.setId(id);
//                BigDecimal beginningBalance = obj.getBigDecimal("beginningBalance") == null ? BigDecimal.ZERO : obj.getBigDecimal("beginningBalance");
                BigDecimal beginningBalance = obj.getBigDecimal("beginningBalance");
                tse.setBeginningBalance(beginningBalance);
                Double beginningQuantity = obj.getDouble("beginningQuantity");
                tse.setBeginningQuantity(beginningQuantity);
                BigDecimal creditAccumulative = obj.getBigDecimal("creditAccumulative");
                tse.setCreditAccumulative(creditAccumulative);
                Double creditAccumulativeQuantity = obj.getDouble("creditAccumulativeQuantity");
                tse.setCreditAccumulativeQuantity(creditAccumulativeQuantity);
                BigDecimal debitAccumulative = obj.getBigDecimal("debitAccumulative");
                tse.setDebitAccumulative(debitAccumulative);
                Double debitAccumulativeQuantity = obj.getDouble("debitAccumulativeQuantity");
                tse.setDebitAccumulativeQuantity(debitAccumulativeQuantity);
                String balanceDirection = obj.getString("balanceDirection");
                tse.setBalanceDirection(balanceDirection);
//                BigDecimal balance = obj.getBigDecimal("balance") == null ? BigDecimal.ZERO : obj.getBigDecimal("balance");
                BigDecimal balance = obj.getBigDecimal("balance");
                tse.setBalance(balance);
                Double quantity = obj.getDouble("quantity");
                tse.setQuantity(quantity);

                //因为页面上输入的都是带符号的数，所以在保存的时候需要把符号转换成方向

                settingSubjectDirection(beginningBalance,balance,balanceDirection,tse);

                buildAccountMapper.saveBuildAccount(tse);
            }

        }
        if (cashFlow != null && !"".equals(cashFlow)) {
            JSONArray cashArray = JSONArray.parseArray(cashFlow);
            for (int i=0;i<cashArray.size();i++) {
                JSONObject obj = cashArray.getJSONObject(i);
                String cashFlowCode = obj.getString("cashFlowCode");
                BigDecimal accumulative = obj.getBigDecimal("accumulative");
                QueryData qd = new QueryData();
                qd.put("org",org);
                qd.put("cashFlowCode",cashFlowCode);
                qd.put("accumulative",accumulative);
                cashMapper.updateBuildAccumulative(qd);
            }
        }
        return 1;
    }

    /*设置某科目的年初余额方向和期末余额方向*/
    private void settingSubjectDirection(BigDecimal beginningBalance, BigDecimal balance, String balanceDirection, TAccountantSubjectEntity tse) {

        //如果年初余额是负数，那么年初余额方向就得和科目本身方向相反
        if (beginningBalance != null) {
            if (beginningBalance.compareTo(BigDecimal.ZERO) < 0) {
                if ("1".equals(balanceDirection)) {
                    tse.setBeginningDirection("2");
                }
                else if ("2".equals(balanceDirection)) {
                    tse.setBeginningDirection("1");
                }
                tse.setBeginningBalance(beginningBalance.abs());
            }
            else if (beginningBalance.compareTo(BigDecimal.ZERO) == 0) {
                tse.setBeginningDirection("3");
            }
            else
                tse.setBeginningDirection(balanceDirection);
        }
//                else
//                    tse.setBeginningDirection("3");


        if (balance != null) {
            if (balance.compareTo(BigDecimal.ZERO) < 0) {
                if ("1".equals(balanceDirection)) {
                    tse.setEndDirection("2");
                }
                else if ("2".equals(balanceDirection)) {
                    tse.setEndDirection("1");
                }
                tse.setBalance(balance.abs());
            }
            else if (balance.compareTo(BigDecimal.ZERO) == 0) {
                tse.setEndDirection("3");
            }
            else
                tse.setEndDirection(balanceDirection);
        }
//                else
//                    tse.setEndDirection("3");

    }

    /*
    * 报表核查
    * 1、先删除之前的老数据，即周期表中state=4的数据
    * 2、以科目暂存表为基础数据写到周期表中
    * */
    @Override
    public JSONObject reportVerification(User user) {
        int org = user.getOid();
        //删除上次产生的老数据
        QueryData qd = new QueryData();
        qd.put("org",org);
        qd.put("state","4");
        buildAccountMapper.deleteOldData(qd);//删除周期表
        buildAccountMapper.deleteOldCashFlow(org);//删除现金流量表
        TAccountantSubjectHistory sh = new TAccountantSubjectHistory();
        sh.setOrg(org);
        tAccountantSubjectHistoryMapper.delSubjectHistory(sh);//删除科目暂存表
        //删除资产负债表
        buildAccountMapper.deleteBalanceSheet(qd);
        //删除利润表
        buildAccountMapper.deleteProfitSheet(qd);

        //生成period期数
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        //期数起始日期,上月第一天
        sdf.applyPattern("yyyy-MM-dd");
        String beginDay = sdf.format(calendar.getTime());
        int month = calendar.get(Calendar.MONTH);
        //当期月报
        sdf.applyPattern("yyyy-MM");
        String term = sdf.format(calendar.getTime());
        calendar.add(Calendar.MONTH, -1);
        sdf.applyPattern("yyyy-MM-dd HH:mm:ss");
        String now = sdf.format(new Date());
        //凭证日期，上月最后一天
//        String voucherDate = voucherUpdate.getPreMonthLastDay(org);
        Date changeDate = NewDateUtils.changeMonth(new Date(),-1);
        Date date = NewDateUtils.getLastTimeOfMonth(changeDate);
        String voucherDate = NewDateUtils.dateToString(date,"yyyy-MM-dd");

        //将科目表数据复制到科目暂存表，用于生成周期表
        TAccountantSubjectEntity as = new TAccountantSubjectEntity();
        as.setOrg(org);
        tAccountantSubjectHistoryMapper.insertOrgSubject(as);


        QueryData queryData = new QueryData();
        queryData.put("org",org);
        queryData.put("balance",1);
        List<TAccountantSubjectEntity> listSubjects = buildAccountMapper.getBaseSubjectsByOrg(queryData);
//        List<TAccountantSubjectPeroid> listPeroids = new ArrayList<TAccountantSubjectPeroid>();
        //存放所有涉及到的科目id
        List<Integer> subjectids = new ArrayList<Integer>();
        //将总账写进科目周期表，有些值来自科目表，比如上期余额,期末余额，本期借，本期贷
        for(TAccountantSubjectEntity subjectEntity : listSubjects)
        {
            String subject = subjectEntity.getSubject();
            int subjectID = 0;

            if(subjectEntity != null)
            {
//                subjectID = subjectEntity.getSubjectId();
                subjectID = subjectEntity.getId();
            }
            if (subjectEntity.getSubject().startsWith("1002")) {
                System.out.println(11);
            }
            double quantity = subjectEntity.getQuantity();
            TAccountantSubjectPeroid subjectPeroid = new TAccountantSubjectPeroid();
            subjectPeroid.setState("4");
            subjectPeroid.setSubject(subject);
            subjectPeroid.setSubjectId(subjectID);
            //月报4位，即，2017-05
            subjectPeroid.setPeriod(term);
            subjectPeroid.setBeginDate(beginDay);
            subjectPeroid.setEndDate(voucherDate);
            //本期和上期余额都来自科目表

            subjectPeroid.setBalanceDirection(subjectEntity.getEndDirection() == null ? "3" : subjectEntity.getEndDirection());
            subjectPeroid.setBalance(subjectEntity.getBalance());
            //本期借贷方都是没有的都包含在了本年累计中
//            subjectPeroid.setCredit(subjectEntity.getCreditAccumulative());
//            subjectPeroid.setDebit(subjectEntity.getDebitAccumulative());
            //创建人信息,就是指当前登录人员
            subjectPeroid.setCreator(user.getUserID());
            subjectPeroid.setCreateName(user.getUserName());
            subjectPeroid.setCreateDate(now);
            subjectPeroid.setSummary("本月合计");
            subjectPeroid.setBeginningBalance(subjectEntity.getBeginningBalance());
            subjectPeroid.setBeginningDirection(subjectEntity.getBeginningDirection() == null ? "3" : subjectEntity.getBeginningDirection());
            subjectPeroid.setQuantity(quantity);
            peroidMapper.insert(subjectPeroid);
            subjectids.add(subjectID);

            //将该科目的父科目写到周期表，递归操作，因为科目后来改成了最多四级
            writeParentSubjectIntoPeriod(subject,org,term,subjectPeroid,subjectids);
        }

//      //把科目暂存表的本年累计数据更新到科目周期表，查询往期数据的时候要用
        QueryData param = new QueryData();
        param.put("org",org);
        param.put("peroid",term);
        peroidMapper.updateAccumulative(param);

        //结帐之后需要把现金流量暂存表的数据写到现金流量表中t_accountant_cash_flow
        TAccountantCash cashTempParam = new TAccountantCash();
        cashTempParam.setOrg(org);
        List<TAccountantCash> cashTempList = cashMapper.getCashTempList(cashTempParam);
        if(cashTempList != null)
        {
            for(TAccountantCash cashTemp : cashTempList)
            {
                CashFlow cashFlow = new CashFlow();
                cashFlow.setState("4");
                cashFlow.setOrg(org);
                cashFlow.setPeriod(term);
                cashFlow.setAmount(cashTemp.getAmount());
                cashFlow.setAccumulative(cashTemp.getAccumulative());
                cashFlow.setCellCode(cashTemp.getCashFlowCode());
                cashFlowMapper.insert(cashFlow);
            }
        }

        //写资产负债表和利润表
//        settingService.balanceSheetComputationalProcess(org,1,beginDay,voucherDate,term,"4");//写资产表
//        settingService.profitStatementComputationalProcess(org,2,beginDay,voucherDate,term,"4");//写利润表

        JSONObject result = new JSONObject();
        result.put("period",term);
        return result;
    }

    /*
    * 建账完成,相当于总结张，需要把各报表的state字段改成3
    * 需要在结账表中添加一条记录
    * */
    @Override
    public int buildFinish(User user) {
        //修改各报表state值为3
        int org = user.getOid();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String voucherDate = "";
        Calendar calendar = Calendar.getInstance();
        sdf.applyPattern("yyyy-MM-dd HH:mm");
        String now = sdf.format(calendar.getTime());
        Map<String,Object> qd = new HashMap<String,Object>();
        qd.put("org",org);

        //建账完成之前需要先知道机构是否录入了建账数据，如果没有录入的话不应该按总结账处理，应该只改变建账状态即可
        String buildState = getBuildAccountState(org);
        if ("2".equals(buildState)) {
            //2 表示机构没有录入建账数据
            int res = buildFinish(org,now,qd,user);
            return res;
        }

        qd.put("state","3");
//        buildAccountMapper.deleteOldData(qd);//修改周期表的state值为3
        buildAccountMapper.buildFinishPeriod(qd);//修改周期表的state值为3
        buildAccountMapper.buildFinishCashFlow(org);//修改现金流量表的state为3
        buildAccountMapper.buildFinishBalanceSheet(org);//资产负债表
        buildAccountMapper.buildFinishProfitSheet(org);//利润表
        //在结账表中新增一条数据

        //上月最后一天,先将日期设置为本月的第一天，然后减去一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        sdf.applyPattern("yyyy-MM");
        voucherDate = sdf.format(calendar.getTime());
        TAccountantSettle settle = new TAccountantSettle();
        settle.setOrg(org);
        settle.setPeriod(voucherDate);
        settle.setBeginDate(voucherDate + "-01");
        sdf.applyPattern("yyyy-MM-dd");
        settle.setEndDate(sdf.format(calendar.getTime()));
        settle.setCreateDate(Tools.date2Str(new Date()));
        settle.setCreator(user.getUserID());
        settle.setCreateName(user.getUserName());
        settle.setState("3");
        settle.setNeedTrial("0");
        settle.setTaxDate(new Date());
        settle.setTaxState("1");
        int status = settleMapper.insert(settle);
        int res = buildFinish(org,now,qd,user);
        return res;
    }

    private int buildFinish(int org,String now,Map qd,User user) {
        //得到该机构的初始建账月份
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(user.getOid());
        link.setKey_("inital_month");
        link = buildAccountMapper.getSettingByKey(link);

        Date curDate = new Date();
        //修改setting表的建账状态,operate_state=4
        Map<String,Object> initMonth = new HashMap<String,Object>();
        initMonth.put("org",org);
        initMonth.put("value_","4");
        initMonth.put("key_","operate_state");
        initMonth.put("update_name",user.getUserName());
        initMonth.put("update_date",new Date());
        buildAccountMapper.updateSettingByOrg(initMonth);
        initMonth.put("key_","account_state");
        buildAccountMapper.updateSettingByOrg(initMonth);
        //如果setting表中的inital_month字段是null的话，需要赋值当前月份，表示第一次建账的月份
        //建账完成后需要生成一条建账记录，第一次叫建账，后面都叫重新建账
        TaccountantEstablishEntity establishEntity = new TaccountantEstablishEntity();
        establishEntity.setOrg(user.getOid());
        establishEntity.setCreator(user.getUserID());
        establishEntity.setCreateName(user.getUserName());
        establishEntity.setCreateDate(curDate);
//        String initalMonth = link.getValue_();
        boolean initialBuild = false;
        if (link == null) {
            initMonth.put("value_",now);
            initMonth.put("key_","inital_month");
            buildAccountMapper.setInitialBuildMonth(initMonth);//初始化initialMonth
            establishEntity.setType("1");//类型1-初始建账,2-重新建账
            initialBuild = true;
        }
        else {
            establishEntity.setType("2");//类型1-初始建账,2-重新建账
        }

        Map<String,Object> param = new HashMap<String,Object>();
        param.put("org",org);
        param.put("key_","kmgl");
        param.put("value_","N");
        param.put("update_name",user.getUserName());
        param.put("update_date",new Date());
        buildAccountMapper.updateSettingByOrg(param);//初始化采购与科目关联-N

        establishEntityMapper.insert(establishEntity);//写建账记录
        int eid = establishEntity.getId();

        //设置setting表的rebuild_label为N，
        initMonth.put("key_","rebuild_label");
        initMonth.put("value_","N");
        initMonth.put("update_name",user.getUserName());
        buildAccountMapper.updateSettingByOrg(initMonth);

        //如果机构没有establish_id，需新增
        //已经初始化的历史机构setting表里没有establish_id得新增，新机构和已建完帐的机构没有此问题
        TAccountantSetting checkEstablishParam = new TAccountantSetting();
        checkEstablishParam.setOrg(org);
        checkEstablishParam.setKey_("establish_id");
        TAccountantSetting res = buildAccountMapper.checkSettingKey(checkEstablishParam);
        if (res == null) {
            checkEstablishParam.setValue_(eid + "");
            checkEstablishParam.setUpdateDate(new Date());
//            buildAccountMapper.insertSettingKey(link);//将establish_id写进setting表
            buildAccountMapper.insertSettingKey(checkEstablishParam);//将establish_id写进setting表
        }
        else {
            //更新建账ID
            initMonth.put("key_","establish_id");
            initMonth.put("value_",eid);
            checkEstablishParam.setUpdateDate(new Date());
            buildAccountMapper.updateSettingByOrg(initMonth);
        }

        Integer userId = user.getUserID();
        String userName = user.getUserName();
        //建完帐需要产生一条变动记录，并且需要把所有科目写到change表
        generalSubjectRecord.insertJournal(org,eid,userId,userName,curDate,false);//false需要写两遍科目，一遍是变动后的，一遍是建账后的

        //建账之后需要新增操作记录，写进record表
        TaccountantSubjectRecord recordDtoParam = new TaccountantSubjectRecord();
        recordDtoParam.setOrg(org);
        recordDtoParam.setState(1);//1-修改后，0-修改前
        recordDtoParam.setEstablish(eid);
        if (initialBuild)
            recordDtoParam.setOperation("6");//操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
        else
            recordDtoParam.setOperation("7");//操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
        recordDtoParam.setCreateDate(curDate);
        recordDtoParam.setCreator(userId);
        recordDtoParam.setCreateName(userName);
        recordDtoParam.setSubject("");
        recordDtoParam.setLevel(0);
//        recordMapper.copySubjectToRecordTable(recordDtoParam);
        recordMapper.insert(recordDtoParam);

        //更新机构表中的建账状态字段，用于登录系统后读取缓存方便
        qd.put("state","4");
        buildAccountMapper.setOrgBuildAccountState(qd);
        user.getOrganization().setAccountState("4");
        qd.put("state","N");
        buildAccountMapper.setOrgRebuildState(qd);
        userService.getPopedomStringByUser(user,true);//变更权限时用,重新覆盖缓存中的权限值
        //返回低值易耗品1413和固定资产1601下的所有科目名称用于生成装配器具
        settingService.getEquipmentToolsSubjectNames(user);
        return 1;

    }

    /*
    * 重新建账
    * 清除该机构下所有的会计数据
    * 重置setting表里的状态
    * */
    @Override
    public int rebuild(int org,User user) {
        //清除原凭证表的凭证、凭证科目、凭证科目明细账、明细对应的现金明细
        List<TAccountantVoucher> listVoucher = voucherMapper.getOrgVouchers(org);
        for (TAccountantVoucher voucher : listVoucher) {
            int voucher_id = voucher.getId();
            TAccountantVoucherSubject voucherSubjectParam = new TAccountantVoucherSubject();
            voucherSubjectParam.setVoucherId(voucher_id);
            TAccountantSubjectDetail originalDetailParam = new TAccountantSubjectDetail();
            List<TAccountantVoucherSubject> voucherSubject = voucherSubjectMapper.getSubjectByVoucher(voucherSubjectParam);
            TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
            for (TAccountantVoucherSubject tvs : voucherSubject)
            {
                //将原明细数据写到历史明细中
                originalDetailParam.setSubjectId(tvs.getId());
                TAccountantSubjectDetail originalDetail = detailMapper.getSubjectDetail(originalDetailParam);
                if (originalDetail != null) {
                    cashParam.setDetailId(originalDetail.getId());
                    subjectCashMapper.deleteByDetailID(cashParam);
                    detailMapper.delete(originalDetail);
                }
                voucherSubjectMapper.delete(tvs);
            }
            voucherMapper.delete(voucher);
        }
        //删除凭证历史表的凭证、历史凭证科目、历史凭证科目明细、历史明细对应的现金明细
        List<TAccountantVoucherHistory> listHistoryVoucher = voucherHistoryMapper.getOrgHistoryVouchers(org);
        for (TAccountantVoucherHistory voucher : listHistoryVoucher) {
            int voucher_id = voucher.getId();
            TAccountantVoucherSubjectHistory voucherSubjectParam = new TAccountantVoucherSubjectHistory();
            voucherSubjectParam.setVoucherId(voucher_id);
            TAccountantSubjectDetailHistory originalDetailParam = new TAccountantSubjectDetailHistory();
            List<TAccountantVoucherSubjectHistory> subjectHistory = voucherSubjectHistoryMapper.getSubjectByVoucher(voucherSubjectParam);
            TAccountantSubjectCashHistory cashParam = new TAccountantSubjectCashHistory();
            for (TAccountantVoucherSubjectHistory tvs : subjectHistory)
            {
                //将原明细数据写到历史明细中
                originalDetailParam.setSubjectId(tvs.getId());
                TAccountantSubjectDetailHistory originalDetail = detailHistoryMapper.getSubjectDetail(originalDetailParam);
                if (originalDetail != null) {
//                    cashParam.setDetailId(originalDetail.getId());
                    cashParam.setDetailHistoryId(originalDetail.getId());
                    subjectCashHistoryMapper.deleteByDetailID(cashParam);
                    detailHistoryMapper.delete(originalDetail);
                }
                voucherSubjectHistoryMapper.delete(tvs);
            }
            voucherHistoryMapper.delete(voucher);
        }

        //删除与财务关联关系的数据，关联表和关联历史表
        reimburseRelevanceHistoryMapper.deleteByOrg(org);
        reimburseRelevanceMapper.deleteByOrg(org);

        //删除会计录入的凭证科目历史表数据
        voucherSubjectHistoryMapper.deleteByOrg(org);
        //删除周期表
        buildAccountMapper.deletePeriodData(org);
        //删除科目表和科目暂存表,只删除无建账ID的数据,因为无建账ID的科目都是建账之后新增的，重新建账时需要删除，只保留有建账ID的科目
//        buildAccountMapper.deleteSubjectByOrg(org);
        buildAccountMapper.deleteSubject(org);
        buildAccountMapper.deleteSubjectHistory(org);
        //删除各报表，现金流量表、现金流量暂存表、现金流量历史暂存表、资产负债表、利润表、结账表、初始化的设置表
//        buildAccountMapper.deleteReport(org);
        buildAccountMapper.deleteCashReport(org);
        buildAccountMapper.deleteCashFlowReport(org);
        buildAccountMapper.deleteCashHistoryReport(org);
        buildAccountMapper.deleteBalanceSheetReport(org);
        buildAccountMapper.deleteProfitReport(org);
        buildAccountMapper.deleteSettle(org);
//        buildAccountMapper.deleteSetting(org);

        //初始化科目表、现金暂存表、setting表
        settingService.accountInitialization(org,user.getUserID(),user.getUserName());
        //初始化机构表中的建账状态和重新建账状态
        buildAccountMapper.initOrgBuildState(org);
        user.getOrganization().setAccountState("0");
        user.getOrganization().setRebuildLabel("N");
        userService.getPopedomStringByUser(user,true);//变更权限时用,重新覆盖缓存中的权限值
        return 1;
    }

    /*
    * 检测是否需要重新建账
    * 1 需要
    * 0 不需要
    * */
    @Override
    public int checkRebuildFlag(int org) {
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(org);
        link = buildAccountMapper.checkRebuildFlag(link);
        if (link != null) {
            String value = link.getValue_();
            if ("Y".equals(value))
                return 1;
            if ("N".equals(value))
                return 0;
        }
        return 0;
    }

    @Override
    public void initialKJdata(int oid, User user) {
        //删除与财务关联关系的数据，关联表和关联历史表
//        reimburseRelevanceHistoryMapper.deleteByOrg(oid);
//        reimburseRelevanceMapper.deleteByOrg(oid);
//        buildAccountMapper.deleteSetting(oid);//删除setting表
        //删除其他表数据并重新建账
//        rebuild(oid,user);
    }

    /*
    * 将父科目写进周期表
    * */
    private void writeParentSubjectIntoPeriod(String subject, int org, String term,TAccountantSubjectPeroid subjectPeroid, List<Integer> subjectids) {
        if (subject.length() > 4)
        {
            TAccountantSubjectPeroid parent = null;
            String subLevel2 = subject.substring(0,subject.length() - 4);
            //写二级科目
            TAccountantSubjectHistory subParam = new TAccountantSubjectHistory();
            subParam.setOrg(org);
            subParam.setSubject(subLevel2);
            TAccountantSubjectHistory subjectLevel2 = subjectHistoryMapper.getOneSubjecthistory(subParam);

            //判断他的二级在周期表中有没有，有的话累加，没有的话新增
            TAccountantSubjectPeroid checkParentParam = new TAccountantSubjectPeroid();
            checkParentParam.setPeriod(term);
            checkParentParam.setSubjectId(subjectLevel2.getSubjectId());
            TAccountantSubjectPeroid level2 = this.peroidMapper.judgeAnnualReport(checkParentParam);
            if (level2 == null)
            {
                //没有的话新增,最后一个4代表是建账时产生的数据
                parent = settleService.newParentPeroid(subLevel2,subjectLevel2,subjectPeroid,subjectids,"4");
            }
            else
            {
                //如果父科目已经有了就需要把当前要写的科目累加上去
                parent = settleService.accumulationThisPeroid(level2,subjectPeroid,subjectLevel2,"4");

            }
            writeParentSubjectIntoPeriod(subLevel2,org,term,subjectPeroid,subjectids);
        }
    }

}
