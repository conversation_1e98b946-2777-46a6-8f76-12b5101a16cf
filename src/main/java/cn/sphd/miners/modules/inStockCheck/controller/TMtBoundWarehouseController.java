package cn.sphd.miners.modules.inStockCheck.controller;
import cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse;
import cn.sphd.miners.modules.inStockCheck.service.impl.TMtBoundWarehouseServiceImpl;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;

/**
* 材料_出入库智能库位对照表20241018 1.316入库 新增(t_mt_bound_warehouse)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/t_mt_bound_warehouse")
public class TMtBoundWarehouseController {
/**
* 服务对象
*/
    @Autowired
    private TMtBoundWarehouseServiceImpl tMtBoundWarehouseServiceImpl;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("selectOne")
    public TMtBoundWarehouse selectOne(Long id) {
    return tMtBoundWarehouseServiceImpl.selectByPrimaryKey(id);
    }

}
