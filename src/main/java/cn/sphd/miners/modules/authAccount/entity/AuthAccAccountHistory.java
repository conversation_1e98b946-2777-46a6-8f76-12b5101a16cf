package cn.sphd.miners.modules.authAccount.entity;

import cn.sphd.miners.common.persistence.BaseEntity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_auth_acc_account_history")
public class AuthAccAccountHistory extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;                                           //`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "acc_account")
    private Long accAccount;                                   //'acc交款ID',
    @Column(name = "acc_id")
    private Long accId;                                        //'账号accId',
    @Column(name = "annual_fee")
    private BigDecimal annualFee;                              //'服务年费',
    @Column(name = "fact_fee")
    private BigDecimal factFee;                                //'实际费用',
    @Column(name = "space_fee")
    private BigDecimal spaceFee;                               //'空间使用费',
    @Column(name = "space_allocation")
    private Long spaceAllocation;                              //'空间分配量:b为单位',
    @Column(name = "space_usage")
    private Long spaceUsage;                                   //'空间使用量:b为单位',
    @Column(name = "apply_time")
    private Date applyTime;                                    //'申请时间',
    @Column(name = "pay_time")
    private Date payTime;                                      //'支付时间',
    @Column(name = "order_id")
    private Long orderId;                                      //'电商主订单id',
    @Column(name = "expansion_id")
    private Long expansionId;                                  //'最后一次电商扩容订单id',
    @Column(name = "memo")
    private String memo;                                       //'提示',
    @Column(name = "creator", nullable = true)
    private Integer creator;
    @Column(name = "create_name", nullable = true, length = 100)
    private String createName;
    @Column(name = "create_date", nullable = true)
    private Date createDate;
    @Column(name = "updator", nullable = true)
    private Integer updator;
    @Column(name = "update_name", nullable = true, length = 100)
    private String updateName;
    @Column(name = "update_date", nullable = true)
    private Date updateDate;
    @Column(name = "operation", nullable = true, length = 1)
    private String operation;
    @Column(name = "previous_id", nullable = true)
    private Integer previousId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccAccount() {
        return accAccount;
    }

    public void setAccAccount(Long accAccount) {
        this.accAccount = accAccount;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public BigDecimal getAnnualFee() {
        return annualFee;
    }

    public void setAnnualFee(BigDecimal annualFee) {
        this.annualFee = annualFee;
    }

    public BigDecimal getFactFee() {
        return factFee;
    }

    public void setFactFee(BigDecimal factFee) {
        this.factFee = factFee;
    }

    public BigDecimal getSpaceFee() {
        return spaceFee;
    }

    public void setSpaceFee(BigDecimal spaceFee) {
        this.spaceFee = spaceFee;
    }

    public Long getSpaceAllocation() {
        return spaceAllocation;
    }

    public void setSpaceAllocation(Long spaceAllocation) {
        this.spaceAllocation = spaceAllocation;
    }

    public Long getSpaceUsage() {
        return spaceUsage;
    }

    public void setSpaceUsage(Long spaceUsage) {
        this.spaceUsage = spaceUsage;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getExpansionId() {
        return expansionId;
    }

    public void setExpansionId(Long expansionId) {
        this.expansionId = expansionId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }
}
